@echo off
REM PT System - Windows Server Deployment Script
REM This script deploys and starts the PT System on Windows Server

setlocal enabledelayedexpansion

REM Colors for output (Windows 10+)
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo.
echo ========================================
echo    PT System - Windows Deployment
echo ========================================
echo.

REM Check if Node.js is installed
echo %BLUE%[INFO]%NC% Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%NC% Node.js is not installed or not in PATH
    echo Please install Node.js 18.0.0 or higher from https://nodejs.org/
    pause
    exit /b 1
)

REM Get Node.js version
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo %GREEN%[SUCCESS]%NC% Node.js version %NODE_VERSION% detected

REM Check if npm is installed
echo %BLUE%[INFO]%NC% Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%NC% npm is not installed or not in PATH
    pause
    exit /b 1
)

REM Get npm version
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo %GREEN%[SUCCESS]%NC% npm version %NPM_VERSION% detected

REM Check if MongoDB is running
echo %BLUE%[INFO]%NC% Checking MongoDB connection...
timeout /t 2 /nobreak >nul

REM Install backend dependencies
echo %BLUE%[INFO]%NC% Installing backend dependencies...
cd backend
if exist "package.production.json" (
    copy /y package.production.json package.json >nul
    echo %BLUE%[INFO]%NC% Using production package.json
)

npm install --production
if %errorlevel% neq 0 (
    echo %RED%[ERROR]%NC% Failed to install backend dependencies
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% Backend dependencies installed

cd ..

REM Create logs directory
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups

REM Copy environment configuration
if not exist ".env" (
    if exist "config\environment.example" (
        copy "config\environment.example" ".env" >nul
        echo %GREEN%[SUCCESS]%NC% Environment file created from template
    ) else (
        echo %YELLOW%[WARNING]%NC% No environment template found
    )
) else (
    echo %GREEN%[SUCCESS]%NC% Environment file already exists
)

REM Start the application
echo.
echo %BLUE%[INFO]%NC% Starting PT System...
echo %BLUE%[INFO]%NC% Application will be available at: http://localhost:3016
echo %BLUE%[INFO]%NC% API Documentation: http://localhost:3016/api-docs
echo.
echo %YELLOW%[INFO]%NC% Demo Credentials:
echo %YELLOW%[INFO]%NC% Administrator: <EMAIL> / password123
echo %YELLOW%[INFO]%NC% Therapist: <EMAIL> / password123
echo.
echo %BLUE%[INFO]%NC% Press Ctrl+C to stop the server
echo.

cd backend
node server.js

pause
