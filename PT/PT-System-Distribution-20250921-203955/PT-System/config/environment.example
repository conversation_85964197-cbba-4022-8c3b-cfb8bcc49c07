# Physical Therapist System - Environment Configuration
# Copy this file to .env and update with your actual values

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pt_system
DB_USER=postgres
DB_PASSWORD=your_secure_database_password

# ==============================================
# REDIS CONFIGURATION
# ==============================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ==============================================
# APPLICATION CONFIGURATION
# ==============================================
NODE_ENV=development
PORT=3000
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# ==============================================
# JWT CONFIGURATION
# ==============================================
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# ==============================================
# AI SERVICE CONFIGURATION
# ==============================================
# OpenAI API Key for AI features
OPENAI_API_KEY=sk-your_openai_api_key_here

# Azure Cognitive Services (Alternative to OpenAI)
AZURE_COGNITIVE_KEY=your_azure_cognitive_services_key
AZURE_COGNITIVE_ENDPOINT=https://your-resource.cognitiveservices.azure.com/

# ==============================================
# SAUDI ARABIA INTEGRATION
# ==============================================

# NPHIES (National Platform for Health and Insurance Exchange Services)
NPHIES_BASE_URL=https://api.nphies.sa
NPHIES_CLIENT_ID=your_nphies_client_id
NPHIES_CLIENT_SECRET=your_nphies_client_secret
NPHIES_SCOPE=read write
NPHIES_ENVIRONMENT=sandbox

# SeHE (Saudi Health Information Exchange)
SEHE_BASE_URL=https://api.sehe.sa
SEHE_API_KEY=your_sehe_api_key
SEHE_CLIENT_ID=your_sehe_client_id
SEHE_ENVIRONMENT=sandbox

# Saudi Health Council Integration
SHC_API_URL=https://api.shc.gov.sa
SHC_API_KEY=your_shc_api_key

# ==============================================
# EMAIL CONFIGURATION
# ==============================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# ==============================================
# FILE STORAGE CONFIGURATION
# ==============================================
# Local storage path
UPLOAD_PATH=./uploads

# AWS S3 Configuration (if using cloud storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=pt-system-files

# ==============================================
# SECURITY CONFIGURATION
# ==============================================
# Encryption key for sensitive data
ENCRYPTION_KEY=your_32_character_encryption_key

# Session configuration
SESSION_SECRET=your_session_secret_key
SESSION_TIMEOUT=3600

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==============================================
# LOGGING CONFIGURATION
# ==============================================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/application.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# ==============================================
# MONITORING AND HEALTH CHECKS
# ==============================================
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true
METRICS_PORT=9090

# ==============================================
# BACKUP CONFIGURATION
# ==============================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=pt-system-backups

# ==============================================
# COMPLIANCE AND AUDIT
# ==============================================
# PDPL Compliance
DATA_RETENTION_DAYS=2555
AUDIT_LOG_RETENTION_DAYS=3650
ANONYMIZATION_ENABLED=true

# Audit trail configuration
AUDIT_ENABLED=true
AUDIT_SENSITIVE_FIELDS=password,ssn,medical_record

# ==============================================
# NOTIFICATION CONFIGURATION
# ==============================================
# SMS Configuration (for Saudi Arabia)
SMS_PROVIDER=stc
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=PT_SYSTEM

# Push notification configuration
PUSH_NOTIFICATION_KEY=your_push_notification_key

# ==============================================
# DEVELOPMENT CONFIGURATION
# ==============================================
# Only for development environment
DEBUG=true
MOCK_EXTERNAL_APIS=false
SEED_DATABASE=false

# ==============================================
# PRODUCTION CONFIGURATION
# ==============================================
# SSL Configuration
SSL_CERT_PATH=/path/to/certificate.crt
SSL_KEY_PATH=/path/to/private.key

# Domain configuration
DOMAIN=yourdomain.com
SUBDOMAIN=pt

# CDN Configuration
CDN_URL=https://cdn.yourdomain.com

# ==============================================
# MICROSERVICES CONFIGURATION
# ==============================================
# Service discovery
SERVICE_REGISTRY_URL=http://localhost:8761

# Patient Management Service
PATIENT_SERVICE_URL=http://localhost:3001
PATIENT_SERVICE_TIMEOUT=5000

# Form Management Service
FORM_SERVICE_URL=http://localhost:3002
FORM_SERVICE_TIMEOUT=5000

# AI Service
AI_SERVICE_URL=http://localhost:3004
AI_SERVICE_TIMEOUT=10000

# Integration Service
INTEGRATION_SERVICE_URL=http://localhost:3005
INTEGRATION_SERVICE_TIMEOUT=15000

# Authentication Service
AUTH_SERVICE_URL=http://localhost:3006
AUTH_SERVICE_TIMEOUT=5000

# ==============================================
# FEATURE FLAGS
# ==============================================
FEATURE_AI_ENABLED=true
FEATURE_VOICE_INPUT=true
FEATURE_PREDICTIVE_ANALYTICS=true
FEATURE_NPHIES_INTEGRATION=true
FEATURE_SEHE_INTEGRATION=true
FEATURE_MULTILINGUAL=true
FEATURE_MOBILE_APP=false

# ==============================================
# LOCALIZATION
# ==============================================
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
TIMEZONE=Asia/Riyadh
DATE_FORMAT=DD/MM/YYYY
TIME_FORMAT=HH:mm

# ==============================================
# PERFORMANCE CONFIGURATION
# ==============================================
# Database connection pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# Redis connection pool
REDIS_POOL_MIN=2
REDIS_POOL_MAX=10

# Cache configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=100mb

# ==============================================
# TESTING CONFIGURATION
# ==============================================
# Test database
TEST_DB_NAME=pt_system_test
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_USER=postgres
TEST_DB_PASSWORD=test_password

# Test configuration
TEST_TIMEOUT=10000
TEST_COVERAGE_THRESHOLD=80
