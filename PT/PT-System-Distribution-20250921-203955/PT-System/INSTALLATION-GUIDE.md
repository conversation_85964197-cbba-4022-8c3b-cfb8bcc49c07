# PT System - Windows Server Installation Guide

## System Requirements
- Windows Server 2016 or later
- Node.js 18.0.0 or higher
- MongoDB 5.0 or higher (local or remote)
- 4GB RAM minimum, 8GB recommended
- 10GB free disk space

## Quick Installation

### Step 1: Extract Files
Extract the PT-System distribution package to your desired location (e.g., C:\PT-System)

### Step 2: Install Node.js
1. Download Node.js from https://nodejs.org/
2. Install Node.js with default settings
3. Verify installation: Open Command Prompt and run `node --version`

### Step 3: Install MongoDB (if not already installed)
1. Download MongoDB Community Server from https://www.mongodb.com/
2. Install with default settings
3. Start MongoDB service

### Step 4: Deploy Application
1. Open Command Prompt as Administrator
2. Navigate to the PT-System directory
3. Run the deployment script:
   ```
   deploy-windows.bat
   ```

### Step 5: Access Application
- Open web browser and go to: http://localhost:3016
- Login with demo credentials:
  - Administrator: <EMAIL> / password123
  - Therapist: <EMAIL> / password123

## Alternative Installation Methods

### PowerShell Deployment
```powershell
.\deploy-windows.ps1
```

### Windows Service Installation
```powershell
.\install-service.ps1
```

### Manual Installation
1. Install backend dependencies:
   ```
   cd backend
   npm install --production
   ```

2. Start application:
   ```
   .\start.bat
   ```

## Configuration

### Environment Configuration
Edit the `.env` file to configure:
- Database connection
- Security settings
- Feature flags
- Integration settings

### Important Security Notes
1. Change default JWT_SECRET in .env file
2. Change default ENCRYPTION_KEY in .env file
3. Change default SESSION_SECRET in .env file
4. Update admin password after first login

## Troubleshooting

### Common Issues
1. **Port 3016 already in use**: Change PORT in .env file
2. **MongoDB connection failed**: Check MongoDB service is running
3. **Permission denied**: Run Command Prompt as Administrator

### Log Files
- Application logs: `logs/application.log`
- Error logs: `logs/pt-system-error.log`

### Support
For technical support, please contact the PT System team.

---
PT System v1.0.0 - Physical Therapy Management System
