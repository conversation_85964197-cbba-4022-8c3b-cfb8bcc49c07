<!DOCTYPE html>
<html>
<head>
    <title>Exercise Image Generator</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .exercise-card { 
            width: 400px; 
            height: 300px; 
            margin: 10px; 
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .exercise-title { font-size: 24px; font-weight: bold; margin-bottom: 20px; }
        .exercise-icon { font-size: 80px; margin: 40px 0; }
        .exercise-category { font-size: 16px; opacity: 0.8; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <h1>Exercise Placeholder Images</h1>
    <p>Right-click and save each image for use in the exercise library.</p>
    
    <div class="exercise-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
        <div class="exercise-title">Shoulder Blade Squeeze</div>
        <div class="exercise-icon"><i class="fas fa-user-md"></i></div>
        <div class="exercise-category">Upper Body • Beginner</div>
    </div>
    
    <div class="exercise-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
        <div class="exercise-title">Ankle Circles</div>
        <div class="exercise-icon"><i class="fas fa-sync"></i></div>
        <div class="exercise-category">Lower Body • Beginner</div>
    </div>
    
    <div class="exercise-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
        <div class="exercise-title">Wall Push-ups</div>
        <div class="exercise-icon"><i class="fas fa-dumbbell"></i></div>
        <div class="exercise-category">Upper Body • Beginner</div>
    </div>
    
    <div class="exercise-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
        <div class="exercise-title">Flexibility Exercise</div>
        <div class="exercise-icon"><i class="fas fa-expand-arrows-alt"></i></div>
        <div class="exercise-category">Flexibility • All Levels</div>
    </div>
    
    <div class="exercise-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
        <div class="exercise-title">Balance Training</div>
        <div class="exercise-icon"><i class="fas fa-balance-scale"></i></div>
        <div class="exercise-category">Balance • Intermediate</div>
    </div>
    
    <div class="exercise-card" style="background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);">
        <div class="exercise-title">Cardio Exercise</div>
        <div class="exercise-icon"><i class="fas fa-heartbeat"></i></div>
        <div class="exercise-category">Cardiovascular • All Levels</div>
    </div>

    <script>
        // This script can be used to generate actual image files
        function downloadAsImage(element, filename) {
            html2canvas(element).then(canvas => {
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
    </script>
</body>
</html>
