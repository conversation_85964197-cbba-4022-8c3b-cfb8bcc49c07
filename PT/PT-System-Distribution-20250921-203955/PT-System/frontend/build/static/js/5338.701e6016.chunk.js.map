{"version": 3, "file": "static/js/5338.701e6016.chunk.js", "mappings": "uNAKA,MA8GA,EA9G0BA,KAAO,IAADC,EAC9B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACfC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACVC,IAAgBC,EAAAA,EAAAA,OAChBC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,OACxCC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,OAE/CG,EAAAA,EAAAA,WAAU,KAAO,IAADC,EAAAC,EAEd,GAAkB,QAAdD,EAAAV,EAASY,aAAK,IAAAF,GAAdA,EAAgBG,SAAyB,QAAlBF,EAAIX,EAASY,aAAK,IAAAD,GAAdA,EAAgBG,mBAAoB,CAAC,IAADC,EAAAC,EAAAC,EACjE,MAAMJ,EAAUb,EAASY,MAAMC,QAC/BL,EAAeK,GAEfR,EAAe,CACba,UAAWL,EAAQM,KAAON,EAAQO,GAClCC,YAAaR,EAAQS,MAAI,GAAAC,OAAOV,EAAQW,UAAS,KAAAD,OAAIV,EAAQY,UAC7DC,WAA8B,QAAnBX,EAAAF,EAAQc,mBAAW,IAAAZ,OAAA,EAAnBA,EAAqBa,oBAA4C,QAAxBZ,EAAAH,EAAQe,wBAAgB,IAAAZ,OAAA,EAAxBA,EAA0BQ,WAAY,KAA8B,QAA3BP,EAAGJ,EAAQe,wBAAgB,IAAAX,OAAA,EAAxBA,EAA0BQ,WAAY,GACtII,YAAa7B,EAASY,MAAMkB,cAAe,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC/EC,aAAa,IAAIH,MAAOI,mBAAmB,QAAS,CAClDC,QAAQ,EACRC,KAAM,UACNC,OAAQ,YAEVC,SAAU,aAEd,KAAO,CAEL,MAAMrB,EAAYhB,EAAasC,IAAI,aAC7BnB,EAAcnB,EAAasC,IAAI,eAC/Bd,EAAYxB,EAAasC,IAAI,aAC7BC,EAAOvC,EAAasC,IAAI,QAE1BtB,GAAaG,GACfhB,EAAe,CACba,UAAWA,EACXG,YAAaqB,mBAAmBrB,GAChCK,UAAWA,EAAYgB,mBAAmBhB,GAAa,GACvDG,YAAaY,IAAQ,IAAIV,MAAOC,cAAcC,MAAM,KAAK,GACzDC,aAAa,IAAIH,MAAOI,mBAAmB,QAAS,CAClDC,QAAQ,EACRC,KAAM,UACNC,OAAQ,YAEVC,SAAU,aAGhB,GACC,CAACrC,EAAcF,EAASY,QAE3B,MAMM+B,EAAeA,KACnB7C,EAAS,aAADyB,QAAyB,OAAXnB,QAAW,IAAXA,OAAW,EAAXA,EAAac,YAAa,MAGlD,OACE0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEC,QAASL,EACTE,UAAU,4EAA2EC,UAErFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,gBAAAtB,OAAkB3B,EAAQ,QAAU,OAAM,0CAExDgD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DnD,EAAE,gBAAiB,qBAEV,OAAXS,QAAW,IAAXA,OAAW,EAAXA,EAAaiB,eACZuB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wCAAuCC,SAAA,CACjDnD,EAAE,UAAW,WAAW,KAAGS,EAAYiB,YAAY,WAAI1B,EAAE,OAAQ,QAAQ,KAAGS,EAAYyB,sBAMjGkB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASL,EACTE,UAAU,yIAAwIC,SAAA,EAElJC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZlD,EAAE,SAAU,qBAOrBoD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0FAAyFC,UACtGC,EAAAA,EAAAA,KAACE,EAAAA,EAAiB,CAChB1C,YAAaA,EACbO,mBAAkC,QAAhBpB,EAAEM,EAASY,aAAK,IAAAlB,OAAA,EAAdA,EAAgBoB,mBACpCV,YAAaA,EACb8C,SApDkBC,IACxBC,QAAQC,IAAI,4BAA6BF,GAEzCrD,EAAS,aAADyB,QAAyB,OAAXnB,QAAW,IAAXA,OAAW,EAAXA,EAAac,YAAa,MAkD1CoC,SAAUX,S", "sources": ["pages/Forms/DailyProgressPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport DailyProgressForm from '../../components/DailyProgress/DailyProgressForm';\n\nconst DailyProgressPage = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const [initialData, setInitialData] = useState(null);\n  const [patientData, setPatientData] = useState(null);\n\n  useEffect(() => {\n    // Check if patient data was passed via React Router state (from patient profile)\n    if (location.state?.patient && location.state?.fromPatientProfile) {\n      const patient = location.state.patient;\n      setPatientData(patient);\n\n      setInitialData({\n        patientId: patient._id || patient.id,\n        patientName: patient.name || `${patient.firstName} ${patient.lastName}`,\n        therapist: patient.therapyInfo?.primaryTherapist || patient.primaryTherapist?.firstName + ' ' + patient.primaryTherapist?.lastName || '',\n        sessionDate: location.state.defaultDate || new Date().toISOString().split('T')[0],\n        sessionTime: new Date().toLocaleTimeString('en-US', {\n          hour12: false,\n          hour: '2-digit',\n          minute: '2-digit'\n        }),\n        facility: 'PT Clinic'\n      });\n    } else {\n      // Fallback to URL parameters for backward compatibility\n      const patientId = searchParams.get('patientId');\n      const patientName = searchParams.get('patientName');\n      const therapist = searchParams.get('therapist');\n      const date = searchParams.get('date');\n\n      if (patientId && patientName) {\n        setInitialData({\n          patientId: patientId,\n          patientName: decodeURIComponent(patientName),\n          therapist: therapist ? decodeURIComponent(therapist) : '',\n          sessionDate: date || new Date().toISOString().split('T')[0],\n          sessionTime: new Date().toLocaleTimeString('en-US', {\n            hour12: false,\n            hour: '2-digit',\n            minute: '2-digit'\n          }),\n          facility: 'PT Clinic'\n        });\n      }\n    }\n  }, [searchParams, location.state]);\n\n  const handleFormSubmit = (formData) => {\n    console.log('Daily Progress submitted:', formData);\n    // Here you would typically save to backend\n    navigate(`/patients/${initialData?.patientId || ''}`);\n  };\n\n  const handleCancel = () => {\n    navigate(`/patients/${initialData?.patientId || ''}`);\n  };\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={handleCancel}\n              className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            >\n              <i className={`fas fa-arrow-${isRTL ? 'right' : 'left'} text-gray-600 dark:text-gray-400`}></i>\n            </button>\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                {t('dailyProgress', 'Daily Progress')}\n              </h1>\n              {initialData?.patientName && (\n                <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                  {t('patient', 'Patient')}: {initialData.patientName} • {t('date', 'Date')}: {initialData.sessionDate}\n                </p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={handleCancel}\n              className=\"px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              <i className=\"fas fa-times mr-2\"></i>\n              {t('cancel', 'Cancel')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <DailyProgressForm\n          patientData={patientData}\n          fromPatientProfile={location.state?.fromPatientProfile}\n          initialData={initialData}\n          onSubmit={handleFormSubmit}\n          onCancel={handleCancel}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default DailyProgressPage;\n"], "names": ["DailyProgressPage", "_location$state3", "t", "isRTL", "useLanguage", "navigate", "useNavigate", "location", "useLocation", "searchParams", "useSearchParams", "initialData", "setInitialData", "useState", "patientData", "setPatientData", "useEffect", "_location$state", "_location$state2", "state", "patient", "fromPatientProfile", "_patient$therapyInfo", "_patient$primaryThera", "_patient$primaryThera2", "patientId", "_id", "id", "patientName", "name", "concat", "firstName", "lastName", "therapist", "therapyInfo", "primaryTherapist", "sessionDate", "defaultDate", "Date", "toISOString", "split", "sessionTime", "toLocaleTimeString", "hour12", "hour", "minute", "facility", "get", "date", "decodeURIComponent", "handleCancel", "_jsxs", "className", "children", "_jsx", "onClick", "DailyProgressForm", "onSubmit", "formData", "console", "log", "onCancel"], "sourceRoot": ""}