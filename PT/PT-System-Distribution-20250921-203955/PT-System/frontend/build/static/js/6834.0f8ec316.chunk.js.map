{"version": 3, "file": "static/js/6834.0f8ec316.chunk.js", "mappings": "+LACA,SAASA,EAAyBC,EAI/BC,GAAQ,IAJwB,MACjCC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,ylBAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBR,E,yKCWlD,EA5BaC,IAUN,IAVO,SACZoB,EAAQ,UACRC,EAAY,GAAE,QACdC,EAAU,MAAK,OACfC,EAAS,YAAW,OACpBC,EAAS,yBAAwB,QACjCC,EAAU,aAAY,WACtBC,EAAa,WAAU,MACvBC,EAAQ,IAET3B,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,MAAMsB,EAAc,CAClBF,EACAF,EACAC,EACAF,EACAD,EACAK,EACAN,GACAQ,OAAOC,SAASC,KAAK,KAEvB,OACEC,EAAAA,EAAAA,KAAA,OAAAC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CAAKZ,UAAWO,GAAiBxB,GAAK,IAAAgB,SACnCA,K,sFC7BP,SAASc,EAAYlC,EAIlBC,GAAQ,IAJW,MACpBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qcAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB2B,E,sFCvBlD,SAASC,EAAcnC,EAIpBC,GAAQ,IAJa,MACtBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,oLAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB4B,E,iBCvBlD,SAAS9B,EAAyB+B,EAAGC,GACnC,GAAI,MAAQD,EAAG,MAAO,CAAC,EACvB,IAAIE,EACFC,EACAC,ECLJ,SAAuCD,EAAGH,GACxC,GAAI,MAAQG,EAAG,MAAO,CAAC,EACvB,IAAIF,EAAI,CAAC,EACT,IAAK,IAAII,KAAKF,EAAG,GAAI,CAAC,EAAEG,eAAeC,KAAKJ,EAAGE,GAAI,CACjD,IAAK,IAAML,EAAEQ,QAAQH,GAAI,SACzBJ,EAAEI,GAAKF,EAAEE,EACX,CACA,OAAOJ,CACT,CDHQ,CAA6BD,EAAGC,GACtC,GAAI7B,OAAOqC,sBAAuB,CAChC,IAAIJ,EAAIjC,OAAOqC,sBAAsBT,GACrC,IAAKG,EAAI,EAAGA,EAAIE,EAAEK,OAAQP,IAAKD,EAAIG,EAAEF,IAAK,IAAMF,EAAEO,QAAQN,IAAM,CAAC,EAAES,qBAAqBJ,KAAKP,EAAGE,KAAOE,EAAEF,GAAKF,EAAEE,GAClH,CACA,OAAOE,CACT,C,sGEVA,SAASQ,EAAOhD,EAIbC,GAAQ,IAJM,MACfC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,scAEP,CACA,MACA,EADiCZ,EAAAA,WAAiByC,E,sFCvBlD,SAASC,EAAejD,EAIrBC,GAAQ,IAJc,MACvBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0WAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB0C,E,sFCvBlD,SAASC,EAAelD,EAIrBC,GAAQ,IAJc,MACvBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,mEAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB2C,E,sFCvBlD,SAASC,EAASnD,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,8XAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB4C,E,sOCClD,MAyPA,EAzPuBC,KACrB,MAAM,EAAEf,IAAMgB,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAOC,IAAYF,EAAAA,EAAAA,UAAS,CACjCG,MAAO,EACPC,YAAa,EACbC,MAAO,EACPC,YAAa,EACbC,aAAc,UAGhBC,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAAoBC,UACxB,IAAK,IAADC,EAAAC,EAAAC,EAAAC,EACFf,GAAW,GAEX,MAAOgB,EAAeC,SAAuBC,QAAQC,IAAI,CACvDC,EAAAA,GAAIC,IAAI,UACRD,EAAAA,GAAIC,IAAI,wBAGVlB,EAAS,CACPC,MAAOY,EAAcM,KAAKC,QAAgC,QAA3BX,EAAII,EAAcM,KAAKA,YAAI,IAAAV,OAAA,EAAvBA,EAAyBrB,SAAU,EACtEc,aAAoC,QAAvBQ,EAAAG,EAAcM,KAAKA,YAAI,IAAAT,GAA+B,QAA/BC,EAAvBD,EAAyBvC,OAAOkD,GAAQA,EAAKC,iBAAS,IAAAX,OAA/B,EAAvBA,EAAwDvB,SAAU,EAC/Ee,OAA8B,QAAvBS,EAAAE,EAAcK,KAAKA,YAAI,IAAAP,OAAA,EAAvBA,EAAyBxB,SAAU,EAC1CgB,YAAa,GACbC,aAAc,QAElB,CAAE,MAAOkB,GACPC,QAAQD,MAAM,iCAAkCA,EAClD,CAAC,QACC1B,GAAW,EACb,GAGI4B,EAAa,CACjB,CACEjF,MAAOmC,EAAE,iBAAkB,mBAC3B+C,YAAa/C,EAAE,oBAAqB,wCACpCgD,KAAMlC,EAAAA,EACNmC,KAAM,eACNC,MAAO,OACPC,KAAK,GAADC,OAAKhC,EAAME,MAAK,WAEtB,CACEzD,MAAOmC,EAAE,wBAAyB,0BAClC+C,YAAa/C,EAAE,wBAAyB,iDACxCgD,KAAMK,EAAAA,EACNJ,KAAM,qBACNC,MAAO,QACPC,KAAK,GAADC,OAAKhC,EAAMK,YAAW,iBAE5B,CACE5D,MAAOmC,EAAE,iBAAkB,mBAC3B+C,YAAa/C,EAAE,kBAAmB,gCAClCgD,KAAMM,EAAAA,EACNL,KAAM,eACNC,MAAO,SACPC,KAAK,GAADC,OAAKhC,EAAMI,MAAK,WAEtB,CACE3D,MAAOmC,EAAE,iBAAkB,mBAC3B+C,YAAa/C,EAAE,0BAA2B,oCAC1CgD,KAAMrC,EAAAA,EACNsC,KAAM,yBACNC,MAAO,OACPC,KAAM,aAER,CACEtF,MAAOmC,EAAE,YAAa,cACtB+C,YAAa/C,EAAE,iBAAkB,uCACjCgD,KAAMtF,EAAAA,EACNuF,KAAM,oBACNC,MAAO,SACPC,KAAM,WAER,CACEtF,MAAOmC,EAAE,mBAAoB,qBAC7B+C,YAAa/C,EAAE,yBAA0B,yCACzCgD,KAAMlD,EAAAA,EACNmD,KAAM,kBACNC,MAAO,MACPC,KAAM,UAER,CACEtF,MAAOmC,EAAE,gBAAiB,oBAC1B+C,YAAa/C,EAAE,gBAAiB,2CAChCgD,KAAMpC,EAAAA,EACNqC,KAAM,gBACNC,MAAO,SACPC,KAAM,UAER,CACEtF,MAAOmC,EAAE,mBAAoB,qBAC7B+C,YAAa/C,EAAE,sBAAuB,yCACtCgD,KAAMnD,EAAAA,EACNoD,KAAM,oBACNC,MAAO,OACPC,KAAM/B,EAAMM,eAIV6B,EAAW5F,IAAA,IAAC,MAAEE,EAAK,MAAE2F,EAAOR,KAAMS,EAAI,MAAEP,EAAK,YAAEH,GAAapF,EAAA,OAChEgC,EAAAA,EAAAA,KAAC+D,EAAAA,EAAI,CAAC1E,UAAU,MAAKD,UACnB4E,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,oBAAmBD,SAAA,EAChCY,EAAAA,EAAAA,KAAA,OAAKX,UAAS,uBAAAoE,OAAyBF,EAAK,QAAOnE,UACjDY,EAAAA,EAAAA,KAAC8D,EAAI,CAACzE,UAAS,gBAAAoE,OAAkBF,EAAK,aAExCS,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,OAAMD,SAAA,EACnBY,EAAAA,EAAAA,KAAA,KAAGX,UAAU,oCAAmCD,SAAElB,KAClD8B,EAAAA,EAAAA,KAAA,KAAGX,UAAS,2BAAAoE,OAA6BF,EAAK,QAAOnE,SAAEyE,IACtDT,IACCpD,EAAAA,EAAAA,KAAA,KAAGX,UAAU,6BAA4BD,SAAEgE,aAO/Ca,EAAYC,IAAA,IAAC,MAAEhG,EAAK,YAAEkF,EAAaC,KAAMS,EAAI,KAAER,EAAI,MAAEC,EAAK,KAAEC,GAAMU,EAAA,OACtElE,EAAAA,EAAAA,KAACmE,EAAAA,GAAI,CAACC,GAAId,EAAMjE,UAAU,QAAOD,UAC/BY,EAAAA,EAAAA,KAAC+D,EAAAA,EAAI,CAAC1E,UAAU,4DAA2DD,UACzEY,EAAAA,EAAAA,KAAA,OAAKX,UAAU,mCAAkCD,UAC/C4E,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,SAAQD,SAAA,EACrB4E,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,yBAAwBD,SAAA,EACrCY,EAAAA,EAAAA,KAAA,OAAKX,UAAS,qBAAAoE,OAAuBF,EAAK,QAAOnE,UAC/CY,EAAAA,EAAAA,KAAC8D,EAAI,CAACzE,UAAS,gBAAAoE,OAAkBF,EAAK,aAExCvD,EAAAA,EAAAA,KAAA,MAAIX,UAAU,2CAA0CD,SAAElB,QAE5D8B,EAAAA,EAAAA,KAAA,KAAGX,UAAU,6BAA4BD,SAAEgE,KAC3CY,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,oCAAmCD,SAAA,EAChDY,EAAAA,EAAAA,KAAA,QAAMX,UAAS,4BAAAoE,OAA8BF,EAAK,QAAOnE,SAAEoE,KAC3DxD,EAAAA,EAAAA,KAAA,QAAMX,UAAU,wBAAuBD,SAAC,wCAQpD,OAAIkC,GAEAtB,EAAAA,EAAAA,KAAA,OAAKX,UAAU,wCAAuCD,UACpDY,EAAAA,EAAAA,KAACqE,EAAAA,GAAc,CAACC,KAAK,UAMzBN,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,YAAWD,SAAA,EAExBY,EAAAA,EAAAA,KAAA,OAAKX,UAAU,oCAAmCD,UAChD4E,EAAAA,EAAAA,MAAA,OAAA5E,SAAA,EACEY,EAAAA,EAAAA,KAAA,MAAIX,UAAU,mCAAkCD,SAC7CiB,EAAE,iBAAkB,sBAEvBL,EAAAA,EAAAA,KAAA,KAAGX,UAAU,qBAAoBD,SAC9BiB,EAAE,4BAA6B,oEAMtC2D,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,uDAAsDD,SAAA,EACnEY,EAAAA,EAAAA,KAAC4D,EAAQ,CACP1F,MAAOmC,EAAE,aAAc,eACvBwD,MAAOpC,EAAME,MACb0B,KAAMlC,EAAAA,EACNoC,MAAM,OACNH,YAAW,GAAAK,OAAKhC,EAAMG,YAAW,cAEnC5B,EAAAA,EAAAA,KAAC4D,EAAQ,CACP1F,MAAOmC,EAAE,YAAa,cACtBwD,MAAOpC,EAAMI,MACbwB,KAAMM,EAAAA,EACNJ,MAAM,QACNH,YAAY,mBAEdpD,EAAAA,EAAAA,KAAC4D,EAAQ,CACP1F,MAAOmC,EAAE,cAAe,eACxBwD,MAAOpC,EAAMK,YACbuB,KAAMK,EAAAA,EACNH,MAAM,SACNH,YAAY,wBAEdpD,EAAAA,EAAAA,KAAC4D,EAAQ,CACP1F,MAAOmC,EAAE,eAAgB,iBACzBwD,MAA8B,SAAvBpC,EAAMM,aAA0B,OAAS,UAChDsB,KAA6B,SAAvB5B,EAAMM,aAA0Bb,EAAAA,EAAkBqD,EAAAA,EACxDhB,MAA8B,SAAvB9B,EAAMM,aAA0B,QAAU,SACjDqB,YAAY,uBAKhBY,EAAAA,EAAAA,MAAA,OAAA5E,SAAA,EACEY,EAAAA,EAAAA,KAAA,MAAIX,UAAU,2CAA0CD,SACrDiB,EAAE,sBAAuB,2BAE5BL,EAAAA,EAAAA,KAAA,OAAKX,UAAU,sEAAqED,SACjF+D,EAAWqB,IAAKC,IACfzE,EAAAA,EAAAA,KAACiE,GAAShE,EAAAA,EAAAA,GAAA,GAAqBwE,GAAfA,EAAKnB,aAM3BU,EAAAA,EAAAA,MAACD,EAAAA,EAAI,CAAC1E,UAAU,MAAKD,SAAA,EACnBY,EAAAA,EAAAA,KAAA,MAAIX,UAAU,2CAA0CD,SACrDiB,EAAE,eAAgB,oBAErB2D,EAAAA,EAAAA,MAAA,OAAK3E,UAAU,wCAAuCD,SAAA,EACpD4E,EAAAA,EAAAA,MAACG,EAAAA,GAAI,CACHC,GAAG,eACH/E,UAAU,kFAAiFD,SAAA,EAE3FY,EAAAA,EAAAA,KAACmB,EAAAA,EAAS,CAAC9B,UAAU,gCACrBW,EAAAA,EAAAA,KAAA,QAAMX,UAAU,4BAA2BD,SACxCiB,EAAE,aAAc,sBAGrB2D,EAAAA,EAAAA,MAACG,EAAAA,GAAI,CACHC,GAAG,eACH/E,UAAU,oFAAmFD,SAAA,EAE7FY,EAAAA,EAAAA,KAAC2D,EAAAA,EAAa,CAACtE,UAAU,iCACzBW,EAAAA,EAAAA,KAAA,QAAMX,UAAU,6BAA4BD,SACzCiB,EAAE,cAAe,sBAGtB2D,EAAAA,EAAAA,MAACG,EAAAA,GAAI,CACHC,GAAG,oBACH/E,UAAU,sFAAqFD,SAAA,EAE/FY,EAAAA,EAAAA,KAACE,EAAAA,EAAY,CAACb,UAAU,kCACxBW,EAAAA,EAAAA,KAAA,QAAMX,UAAU,8BAA6BD,SAC1CiB,EAAE,mBAAoB,oC,sFCxQrC,SAASsD,EAAa3F,EAInBC,GAAQ,IAJY,MACrBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,mgBAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBoF,E,sFCvBlD,SAASD,EAAe1F,EAIrBC,GAAQ,IAJc,MACvBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,uNAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBmF,E,sFCvBlD,SAASa,EAAuBvG,EAI7BC,GAAQ,IAJsB,MAC/BC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qLAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBgG,E", "sources": ["../node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js", "components/Common/Card.jsx", "../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js", "../node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../node_modules/@heroicons/react/24/outline/esm/CogIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js", "pages/Admin/AdminDashboard.jsx", "../node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ClipboardDocumentListIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClipboardDocumentListIcon);\nexport default ForwardRef;", "import React from 'react';\n\n/**\n * Card Component\n * A reusable card component with consistent styling\n */\n\nconst Card = ({ \n  children, \n  className = '', \n  padding = 'p-6',\n  shadow = 'shadow-sm',\n  border = 'border border-gray-200',\n  rounded = 'rounded-lg',\n  background = 'bg-white',\n  hover = '',\n  ...props \n}) => {\n  const cardClasses = [\n    background,\n    border,\n    rounded,\n    shadow,\n    padding,\n    hover,\n    className\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className={cardClasses} {...props}>\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n", "import * as React from \"react\";\nfunction ChartBarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChartBarIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction LockClosedIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(LockClosedIcon);\nexport default ForwardRef;", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import * as React from \"react\";\nfunction CogIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CogIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CircleStackIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CircleStackIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CheckCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckCircleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction UsersIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UsersIcon);\nexport default ForwardRef;", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { Link } from 'react-router-dom';\nimport {\n  UsersIcon,\n  ShieldCheckIcon,\n  UserGroupIcon,\n  CogIcon,\n  ClipboardDocumentListIcon,\n  LockClosedIcon,\n  CircleStackIcon,\n  ChartBarIcon,\n  ExclamationTriangleIcon,\n  CheckCircleIcon\n} from '@heroicons/react/24/outline';\n\nimport Card from '../../components/Common/Card';\nimport LoadingSpinner from '../../components/UI/LoadingSpinner';\nimport api from '../../services/api';\n\n/**\n * Admin Dashboard Component\n * Main dashboard for system administration\n */\n\nconst AdminDashboard = () => {\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    users: 0,\n    activeUsers: 0,\n    roles: 0,\n    permissions: 0,\n    systemHealth: 'good'\n  });\n\n  useEffect(() => {\n    loadDashboardData();\n  }, []);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      // Load dashboard statistics\n      const [usersResponse, rolesResponse] = await Promise.all([\n        api.get('/users'),\n        api.get('/permissions/roles')\n      ]);\n\n      setStats({\n        users: usersResponse.data.total || usersResponse.data.data?.length || 0,\n        activeUsers: usersResponse.data.data?.filter(user => user.isActive)?.length || 0,\n        roles: rolesResponse.data.data?.length || 0,\n        permissions: 29, // Based on our permission system\n        systemHealth: 'good'\n      });\n    } catch (error) {\n      console.error('Failed to load dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const adminCards = [\n    {\n      title: t('userManagement', 'User Management'),\n      description: t('manageSystemUsers', 'Manage system users and their access'),\n      icon: UsersIcon,\n      path: '/admin/users',\n      color: 'blue',\n      stat: `${stats.users} users`\n    },\n    {\n      title: t('permissionsManagement', 'Permissions Management'),\n      description: t('manageUserPermissions', 'Configure user permissions and access control'),\n      icon: ShieldCheckIcon,\n      path: '/admin/permissions',\n      color: 'green',\n      stat: `${stats.permissions} permissions`\n    },\n    {\n      title: t('roleManagement', 'Role Management'),\n      description: t('manageUserRoles', 'Define and manage user roles'),\n      icon: UserGroupIcon,\n      path: '/admin/roles',\n      color: 'purple',\n      stat: `${stats.roles} roles`\n    },\n    {\n      title: t('systemSettings', 'System Settings'),\n      description: t('configureSystemSettings', 'Configure global system settings'),\n      icon: CogIcon,\n      path: '/admin/system-settings',\n      color: 'gray',\n      stat: 'Configure'\n    },\n    {\n      title: t('auditLogs', 'Audit Logs'),\n      description: t('viewSystemLogs', 'View system activity and audit logs'),\n      icon: ClipboardDocumentListIcon,\n      path: '/admin/audit-logs',\n      color: 'yellow',\n      stat: 'Monitor'\n    },\n    {\n      title: t('securitySettings', 'Security Settings'),\n      description: t('manageSecuritySettings', 'Manage security policies and settings'),\n      icon: LockClosedIcon,\n      path: '/admin/security',\n      color: 'red',\n      stat: 'Secure'\n    },\n    {\n      title: t('backupRestore', 'Backup & Restore'),\n      description: t('manageBackups', 'Manage system backups and data recovery'),\n      icon: CircleStackIcon,\n      path: '/admin/backup',\n      color: 'indigo',\n      stat: 'Backup'\n    },\n    {\n      title: t('systemMonitoring', 'System Monitoring'),\n      description: t('monitorSystemHealth', 'Monitor system performance and health'),\n      icon: ChartBarIcon,\n      path: '/admin/monitoring',\n      color: 'teal',\n      stat: stats.systemHealth\n    }\n  ];\n\n  const StatCard = ({ title, value, icon: Icon, color, description }) => (\n    <Card className=\"p-6\">\n      <div className=\"flex items-center\">\n        <div className={`p-3 rounded-full bg-${color}-100`}>\n          <Icon className={`h-6 w-6 text-${color}-600`} />\n        </div>\n        <div className=\"ml-4\">\n          <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n          <p className={`text-2xl font-bold text-${color}-600`}>{value}</p>\n          {description && (\n            <p className=\"text-xs text-gray-500 mt-1\">{description}</p>\n          )}\n        </div>\n      </div>\n    </Card>\n  );\n\n  const AdminCard = ({ title, description, icon: Icon, path, color, stat }) => (\n    <Link to={path} className=\"block\">\n      <Card className=\"p-6 hover:shadow-lg transition-shadow duration-200 h-full\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <div className=\"flex items-center mb-3\">\n              <div className={`p-2 rounded-lg bg-${color}-100`}>\n                <Icon className={`h-5 w-5 text-${color}-600`} />\n              </div>\n              <h3 className=\"ml-3 text-lg font-semibold text-gray-900\">{title}</h3>\n            </div>\n            <p className=\"text-gray-600 text-sm mb-3\">{description}</p>\n            <div className=\"flex items-center justify-between\">\n              <span className={`text-sm font-medium text-${color}-600`}>{stat}</span>\n              <span className=\"text-xs text-gray-400\">Click to manage →</span>\n            </div>\n          </div>\n        </div>\n      </Card>\n    </Link>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            {t('adminDashboard', 'Admin Dashboard')}\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            {t('adminDashboardDescription', 'Manage system settings, users, and monitor system health')}\n          </p>\n        </div>\n      </div>\n\n      {/* System Overview Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatCard\n          title={t('totalUsers', 'Total Users')}\n          value={stats.users}\n          icon={UsersIcon}\n          color=\"blue\"\n          description={`${stats.activeUsers} active`}\n        />\n        <StatCard\n          title={t('userRoles', 'User Roles')}\n          value={stats.roles}\n          icon={UserGroupIcon}\n          color=\"green\"\n          description=\"Defined roles\"\n        />\n        <StatCard\n          title={t('permissions', 'Permissions')}\n          value={stats.permissions}\n          icon={ShieldCheckIcon}\n          color=\"purple\"\n          description=\"System permissions\"\n        />\n        <StatCard\n          title={t('systemHealth', 'System Health')}\n          value={stats.systemHealth === 'good' ? 'Good' : 'Warning'}\n          icon={stats.systemHealth === 'good' ? CheckCircleIcon : ExclamationTriangleIcon}\n          color={stats.systemHealth === 'good' ? 'green' : 'yellow'}\n          description=\"Overall status\"\n        />\n      </div>\n\n      {/* Admin Management Cards */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          {t('administrationTools', 'Administration Tools')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {adminCards.map((card) => (\n            <AdminCard key={card.path} {...card} />\n          ))}\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <Card className=\"p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          {t('quickActions', 'Quick Actions')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Link\n            to=\"/admin/users\"\n            className=\"flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors\"\n          >\n            <UsersIcon className=\"h-5 w-5 text-blue-600 mr-3\" />\n            <span className=\"text-blue-700 font-medium\">\n              {t('addNewUser', 'Add New User')}\n            </span>\n          </Link>\n          <Link\n            to=\"/admin/roles\"\n            className=\"flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors\"\n          >\n            <UserGroupIcon className=\"h-5 w-5 text-green-600 mr-3\" />\n            <span className=\"text-green-700 font-medium\">\n              {t('manageRoles', 'Manage Roles')}\n            </span>\n          </Link>\n          <Link\n            to=\"/admin/monitoring\"\n            className=\"flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors\"\n          >\n            <ChartBarIcon className=\"h-5 w-5 text-purple-600 mr-3\" />\n            <span className=\"text-purple-700 font-medium\">\n              {t('viewSystemStatus', 'View System Status')}\n            </span>\n          </Link>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n", "import * as React from \"react\";\nfunction UserGroupIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserGroupIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;"], "names": ["ClipboardDocumentListIcon", "_ref", "svgRef", "title", "titleId", "props", "_objectWithoutProperties", "_excluded", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "children", "className", "padding", "shadow", "border", "rounded", "background", "hover", "cardClasses", "filter", "Boolean", "join", "_jsx", "_objectSpread", "ChartBarIcon", "LockClosedIcon", "e", "t", "o", "r", "i", "n", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "length", "propertyIsEnumerable", "CogIcon", "CircleStackIcon", "CheckCircleIcon", "UsersIcon", "AdminDashboard", "useTranslation", "loading", "setLoading", "useState", "stats", "setStats", "users", "activeUsers", "roles", "permissions", "systemHealth", "useEffect", "loadDashboardData", "async", "_usersResponse$data$d", "_usersResponse$data$d2", "_usersResponse$data$d3", "_rolesResponse$data$d", "usersResponse", "rolesResponse", "Promise", "all", "api", "get", "data", "total", "user", "isActive", "error", "console", "adminCards", "description", "icon", "path", "color", "stat", "concat", "ShieldCheckIcon", "UserGroupIcon", "StatCard", "value", "Icon", "Card", "_jsxs", "AdminCard", "_ref2", "Link", "to", "LoadingSpinner", "size", "ExclamationTriangleIcon", "map", "card"], "sourceRoot": ""}