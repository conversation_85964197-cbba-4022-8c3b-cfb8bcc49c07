{"version": 3, "file": "static/js/2770.c01d04a2.chunk.js", "mappings": "yMAKA,MAiiBMA,EAAsBC,IAAkF,IAAjF,SAAEC,EAAQ,WAAEC,EAAU,iBAAEC,EAAgB,eAAEC,EAAc,OAAEC,EAAM,QAAEC,GAASN,EACtG,MAAM,EAAEO,IAAMC,EAAAA,EAAAA,MACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAASR,IACpCS,EAAUC,IAAeF,EAAAA,EAAAA,UAASP,EAAeU,MAAQ,KACzDC,EAAcC,IAAmBL,EAAAA,EAAAA,UAASP,EAAea,UAAY,KACrEC,EAAeC,IAAoBR,EAAAA,EAAAA,UAASP,EAAegB,WAAa,KACxEC,EAAOC,IAAYX,EAAAA,EAAAA,UAASP,EAAeiB,OAAS,IAY3D,OACEE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFAAgFC,UAC7FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChElB,EAAE,iBAAkB,sBAEvBgB,EAAAA,EAAAA,KAAA,UACEI,QAASrB,EACTkB,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAIjBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,iDAAgDC,SAAA,CAC3DlB,EAAE,SAAU,UAAU,KAAGL,QAK9BwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,YAAa,cAAc,KAAGE,EAAU,UAE7Cc,EAAAA,EAAAA,KAAA,SACET,KAAK,QACLc,IAAI,IACJC,IAAI,KACJC,MAAOrB,EACPsB,SAAWC,GAAMtB,EAAauB,SAASD,EAAEE,OAAOJ,QAChDN,UAAU,sEAEZE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,QAAAE,SAAOlB,EAAE,SAAU,cACnBgB,EAAAA,EAAAA,KAAA,QAAAE,SAAOlB,EAAE,cAAe,yBAK5BmB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,UACEI,QAASrB,EACTkB,UAAU,qKAAoKC,SAE7KlB,EAAE,SAAU,aAEfgB,EAAAA,EAAAA,KAAA,UACEI,QA5DOQ,KAOjB9B,EAAOJ,EAAUQ,EAND,CACdK,KAAMF,EACNK,SAAUF,EACVK,UAAWF,EACXG,MAAOA,KAwDCG,UAAU,yFAAwFC,SAEjGlB,EAAE,OAAQ,qBASzB,EAjnBgB6B,IAA+H,IAADC,EAAAC,EAAAC,EAAA,IAA7H,UAAEC,EAAS,eAAEC,EAAc,gBAAEC,EAAkB,GAAE,WAAEC,EAAa,CAAC,EAAC,SAAEC,GAAW,EAAK,OAAEvC,EAAM,iBAAEwC,GAAmB,GAAOT,EACvI,MAAM,EAAE7B,EAAC,MAAEuC,IAAUtC,EAAAA,EAAAA,MACduC,EAAeC,IAAoBrC,EAAAA,EAAAA,UAAS,OAC5CsC,EAAsBC,IAA2BvC,EAAAA,EAAAA,UAAS+B,IAC1DS,EAAiBC,IAAsBzC,EAAAA,EAAAA,UAASgC,IAChDU,EAAaC,IAAkB3C,EAAAA,EAAAA,UAAS,CAAC,IACzC4C,EAAeC,IAAoB7C,EAAAA,EAAAA,WAAS,IAC5C8C,EAAwBC,IAA6B/C,EAAAA,EAAAA,UAAS,OAC9DgD,EAAWC,IAAgBjD,EAAAA,EAAAA,WAAS,IAG3CkD,EAAAA,EAAAA,WAAU,KACJrB,GACFsB,KAED,CAACtB,IAEJ,MAAMsB,EAAkBC,UACtB,IACE,MAAMC,QAAaC,EAAAA,EAAYC,eAAe1B,GAC1CwB,IACFd,EAAwBc,EAAKtB,iBAAmB,IAChDU,EAAmBY,EAAKrB,YAAc,CAAC,GACvCW,EAAeU,EAAKX,aAAe,CAAC,GAExC,CAAE,MAAOc,GACPC,QAAQD,MAAM,+BAAgCA,EAChD,GA8BIE,EAAc,CAClBC,KAAM,CAAEC,GAAI,OAAQC,KAAMjE,EAAE,OAAQ,QAASkE,OAAQ,kCACrDC,KAAM,CAAEH,GAAI,OAAQC,KAAMjE,EAAE,OAAQ,QAASkE,OAAQ,wCACrDE,aAAc,CAAEJ,GAAI,eAAgBC,KAAMjE,EAAE,eAAgB,iBAAkBkE,OAAQ,uEACtFG,cAAe,CAAEL,GAAI,gBAAiBC,KAAMjE,EAAE,gBAAiB,kBAAmBkE,OAAQ,uEAC1FI,QAAS,CAAEN,GAAI,UAAWC,KAAMjE,EAAE,UAAW,YAAakE,OAAQ,6EAClEK,SAAU,CAAEP,GAAI,WAAYC,KAAMjE,EAAE,WAAY,aAAckE,OAAQ,6EACtEM,UAAW,CAAER,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,uEAC1EO,WAAY,CAAET,GAAI,aAAcC,KAAMjE,EAAE,aAAc,eAAgBkE,OAAQ,uEAC9EQ,UAAW,CAAEV,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,6EAC1ES,WAAY,CAAEX,GAAI,aAAcC,KAAMjE,EAAE,aAAc,eAAgBkE,OAAQ,6EAC9EU,MAAO,CAAEZ,GAAI,QAASC,KAAMjE,EAAE,QAAS,SAAUkE,OAAQ,kCACzDW,UAAW,CAAEb,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,2DAC1EY,UAAW,CAAEd,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,2DAC1Ea,QAAS,CAAEf,GAAI,UAAWC,KAAMjE,EAAE,UAAW,WAAYkE,OAAQ,kCACjEc,QAAS,CAAEhB,GAAI,UAAWC,KAAMjE,EAAE,UAAW,YAAakE,OAAQ,uEAClEe,SAAU,CAAEjB,GAAI,WAAYC,KAAMjE,EAAE,WAAY,aAAckE,OAAQ,uEACtEgB,UAAW,CAAElB,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,uEAC1EiB,WAAY,CAAEnB,GAAI,aAAcC,KAAMjE,EAAE,aAAc,eAAgBkE,OAAQ,uEAC9EkB,SAAU,CAAEpB,GAAI,WAAYC,KAAMjE,EAAE,WAAY,aAAckE,OAAQ,6EACtEmB,UAAW,CAAErB,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,6EAC1EoB,SAAU,CAAEtB,GAAI,WAAYC,KAAMjE,EAAE,WAAY,aAAckE,OAAQ,gGACtEqB,UAAW,CAAEvB,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,gGAC1EsB,UAAW,CAAExB,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,6EAC1EuB,WAAY,CAAEzB,GAAI,aAAcC,KAAMjE,EAAE,aAAc,eAAgBkE,OAAQ,6EAC9EwB,SAAU,CAAE1B,GAAI,WAAYC,KAAMjE,EAAE,WAAY,aAAckE,OAAQ,uEACtEyB,UAAW,CAAE3B,GAAI,YAAaC,KAAMjE,EAAE,YAAa,cAAekE,OAAQ,wEAGtE0B,EAAqBlG,IACrB2C,IAEJc,EAA0BzD,GAC1BuD,GAAiB,GAEbf,GACFA,EAAexC,KA8BbmG,EAAgBnG,IACpB,MAAMQ,EAAY0C,EAAgBlD,IAAa,EACzCoG,EAAapD,EAAqBqD,SAASrG,GAEjD,OAAkB,IAAdQ,GAAoB4F,EACpB5F,GAAa,EAAU,UACvBA,GAAa,EAAU,UACvBA,GAAa,EAAU,UACvBA,GAAa,EAAU,UACpB,UALoC,WAQvC8F,EAAoBtG,IACxB,MAAMoG,EAAapD,EAAqBqD,SAASrG,GAGjD,OAFkB8C,IAAkB9C,EAEd,GAClBoG,EAAmB,GAChB,IAGT,OACE3E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBC,SAAA,EACjCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChElB,EAAE,UAAW,eAEdqC,IACArB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDlB,EAAE,sBAAuB,yCAMhCgB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,UAClCC,EAAAA,EAAAA,MAAA,OACE8E,MAAM,MACNC,OAAO,MACPC,QAAQ,cACRlF,UAAU,qFAAoFC,SAAA,EAG9FF,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,QACnBY,QAAST,EAAiB,QAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,QACjCgB,aAAcA,IAAMnE,EAAiB,QACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,KACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,QACnBY,QAAST,EAAiB,QAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,QACjCgB,aAAcA,IAAMnE,EAAiB,QACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,gBACnBY,QAAST,EAAiB,gBAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,gBACjCgB,aAAcA,IAAMnE,EAAiB,gBACrCoE,aAAcA,IAAMpE,EAAiB,SAEvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,iBACnBY,QAAST,EAAiB,iBAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,iBACjCgB,aAAcA,IAAMnE,EAAiB,iBACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,KACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,WACnBY,QAAST,EAAiB,WAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,WACjCgB,aAAcA,IAAMnE,EAAiB,WACrCoE,aAAcA,IAAMpE,EAAiB,SAEvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,YACnBY,QAAST,EAAiB,YAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,YACjCgB,aAAcA,IAAMnE,EAAiB,YACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,SACnBY,QAAST,EAAiB,SAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,SACjCgB,aAAcA,IAAMnE,EAAiB,SACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,WACnBY,QAAST,EAAiB,WAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,WACjCgB,aAAcA,IAAMnE,EAAiB,WACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,WACnBY,QAAST,EAAiB,WAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,WACjCgB,aAAcA,IAAMnE,EAAiB,WACrCoE,aAAcA,IAAMpE,EAAiB,SAEvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,YACnBY,QAAST,EAAiB,YAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,YACjCgB,aAAcA,IAAMnE,EAAiB,YACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,aACnBY,QAAST,EAAiB,aAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,aACjCgB,aAAcA,IAAMnE,EAAiB,aACrCoE,aAAcA,IAAMpE,EAAiB,SAEvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,cACnBY,QAAST,EAAiB,cAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,cACjCgB,aAAcA,IAAMnE,EAAiB,cACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,YACnBY,QAAST,EAAiB,YAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,YACjCgB,aAAcA,IAAMnE,EAAiB,YACrCoE,aAAcA,IAAMpE,EAAiB,SAEvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,aACnBY,QAAST,EAAiB,aAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,aACjCgB,aAAcA,IAAMnE,EAAiB,aACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,YACnBY,QAAST,EAAiB,YAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,YACjCgB,aAAcA,IAAMnE,EAAiB,YACrCoE,aAAcA,IAAMpE,EAAiB,SAEvCzB,EAAAA,EAAAA,KAAA,QACE8F,EAAE,MACFC,EAAE,MACFd,MAAM,KACNC,OAAO,KACPM,KAAMX,EAAa,aACnBY,QAAST,EAAiB,aAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,aACjCgB,aAAcA,IAAMnE,EAAiB,aACrCoE,aAAcA,IAAMpE,EAAiB,SAIvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,YACnBY,QAAST,EAAiB,YAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,YACjCgB,aAAcA,IAAMnE,EAAiB,YACrCoE,aAAcA,IAAMpE,EAAiB,SAEvCzB,EAAAA,EAAAA,KAAA,WACEoF,GAAG,MACHC,GAAG,MACHC,GAAG,KACHC,GAAG,KACHC,KAAMX,EAAa,aACnBY,QAAST,EAAiB,aAC1BU,OAAO,UACPC,YAAY,IACZ1F,UAAU,6CACVG,QAASA,IAAMwE,EAAkB,aACjCgB,aAAcA,IAAMnE,EAAiB,aACrCoE,aAAcA,IAAMpE,EAAiB,aAM1CD,IACCrB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClEqB,EAAkC,QAA7BT,EAAGgC,EAAYtB,UAAc,IAAAV,OAAA,EAA1BA,EAA4BoC,OAAmC,QAA7BnC,EAAG+B,EAAYtB,UAAc,IAAAT,OAAA,EAA1BA,EAA4BkC,OAE3ErB,EAAgBJ,KACfrB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,CAC3DlB,EAAE,YAAa,cAAc,KAAG4C,EAAgBJ,GAAe,YAOvEF,IACCnB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4DAA2DC,SAAA,EACvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BACZjB,EAAE,eAAgB,oBAErBmB,EAAAA,EAAAA,MAAA,MAAIF,UAAU,qDAAoDC,SAAA,EAChEC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAI,UAAGlB,EAAE,kBAAmB,qDAC5BmB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAI,UAAGlB,EAAE,kBAAmB,+DAC5BmB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAI,UAAGlB,EAAE,iBAAkB,4DAC3BmB,EAAAA,EAAAA,MAAA,MAAAD,SAAA,CAAI,UAAGlB,EAAE,iBAAkB,qDAMhCiC,GAAaS,EAAqBsE,OAAS,IAC1ChG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2BAA0BC,UACvCF,EAAAA,EAAAA,KAAA,UACEI,QA9bYoC,UACtB,GAAKvB,EAAL,CAEAoB,GAAa,GACb,IACE,MAAM4D,EAAc,CAClB9E,gBAAiBO,EACjBN,WAAYQ,EACZE,YAAaA,SAGTY,EAAAA,EAAYwD,gBAAgBjF,EAAWgF,GAEzCnH,GACFA,EAAOmH,GAGTE,EAAAA,GAAMC,QAAQpH,EAAE,eAAgB,0CAClC,CAAE,MAAO4D,GACPC,QAAQD,MAAM,8BAA+BA,GAC7CuD,EAAAA,GAAMvD,MAAM5D,EAAE,YAAa,qBAC7B,CAAC,QACCqD,GAAa,EACf,CAtBsB,GA8bZgE,SAAUjE,EACVnC,UAAU,8JAA6JC,SAEtKkC,GACCjC,EAAAA,EAAAA,MAAAmG,EAAAA,SAAA,CAAApG,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+DACfD,EAAAA,EAAAA,KAAA,QAAAE,SAAOlB,EAAE,SAAU,mBAGrBmB,EAAAA,EAAAA,MAAAmG,EAAAA,SAAA,CAAApG,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iBACbD,EAAAA,EAAAA,KAAA,QAAAE,SAAOlB,EAAE,iBAAkB,6BAQrCmB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnElB,EAAE,YAAa,iBAElBmB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,UAE7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,YAE7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,YAE7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,YAE7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,YAE7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,qBAOlE8B,GAAiBE,IAChBlC,EAAAA,EAAAA,KAACxB,EAAmB,CAClBE,SAAUwD,EACVvD,YAA+C,QAAnCqC,EAAA8B,EAAYZ,UAAuB,IAAAlB,OAAA,EAAnCA,EAAqCiC,OAAQf,EACzDtD,iBAAkBgD,EAAgBM,IAA2B,EAC7DrD,eAAgBiD,EAAYI,IAA2B,CAAC,EACxDpD,OAvbqByH,CAAC7H,EAAUQ,EAAWsH,KACjD,MAAMC,EAAyB,IAAI/E,GAC7BgF,GAAiBC,EAAAA,EAAAA,GAAA,GAAQ/E,GACzBgF,GAAkBD,EAAAA,EAAAA,GAAA,GAAQ7E,GAEhC,GAAI5C,EAAY,EACTuH,EAAuB1B,SAASrG,IACnC+H,EAAuBI,KAAKnI,GAE9BgI,EAAkBhI,GAAYQ,EAC9B0H,EAAmBlI,GAAY8H,MAC1B,CACL,MAAMM,EAAQL,EAAuBM,QAAQrI,GACzCoI,GAAS,GACXL,EAAuBO,OAAOF,EAAO,UAEhCJ,EAAkBhI,UAClBkI,EAAmBlI,EAC5B,CAEAiD,EAAwB8E,GACxB5E,EAAmB6E,GACnB3E,EAAe6E,GACf3E,GAAiB,IAiaXlD,QAASA,IAAMkD,GAAiB,Q", "sources": ["components/BodyMap/BodyMap.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport dataService from '../../services/dataService';\nimport toast from 'react-hot-toast';\n\nconst BodyMap = ({ patientId, onRegionSelect, selectedRegions = [], painLevels = {}, readOnly = false, onSave, showInstructions = false }) => {\n  const { t, isRTL } = useLanguage();\n  const [hoveredRegion, setHoveredRegion] = useState(null);\n  const [localSelectedRegions, setLocalSelectedRegions] = useState(selectedRegions);\n  const [localPainLevels, setLocalPainLevels] = useState(painLevels);\n  const [painDetails, setPainDetails] = useState({});\n  const [showPainModal, setShowPainModal] = useState(false);\n  const [selectedRegionForModal, setSelectedRegionForModal] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Load existing data when component mounts\n  useEffect(() => {\n    if (patientId) {\n      loadBodyMapData();\n    }\n  }, [patientId]);\n\n  const loadBodyMapData = async () => {\n    try {\n      const data = await dataService.getBodyMapData(patientId);\n      if (data) {\n        setLocalSelectedRegions(data.selectedRegions || []);\n        setLocalPainLevels(data.painLevels || {});\n        setPainDetails(data.painDetails || {});\n      }\n    } catch (error) {\n      console.error('Error loading body map data:', error);\n    }\n  };\n\n  const saveBodyMapData = async () => {\n    if (!patientId) return;\n\n    setIsLoading(true);\n    try {\n      const bodyMapData = {\n        selectedRegions: localSelectedRegions,\n        painLevels: localPainLevels,\n        painDetails: painDetails\n      };\n\n      await dataService.saveBodyMapData(patientId, bodyMapData);\n\n      if (onSave) {\n        onSave(bodyMapData);\n      }\n\n      toast.success(t('bodyMapSaved', 'Body map assessment saved successfully'));\n    } catch (error) {\n      console.error('Error saving body map data:', error);\n      toast.error(t('saveError', 'Error saving data'));\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Body regions with coordinates for SVG map\n  const bodyRegions = {\n    head: { id: 'head', name: t('head', 'Head'), nameAr: 'الرأس' },\n    neck: { id: 'neck', name: t('neck', 'Neck'), nameAr: 'الرقبة' },\n    leftShoulder: { id: 'leftShoulder', name: t('leftShoulder', 'Left Shoulder'), nameAr: 'الكتف الأيسر' },\n    rightShoulder: { id: 'rightShoulder', name: t('rightShoulder', 'Right Shoulder'), nameAr: 'الكتف الأيمن' },\n    leftArm: { id: 'leftArm', name: t('leftArm', 'Left Arm'), nameAr: 'الذراع الأيسر' },\n    rightArm: { id: 'rightArm', name: t('rightArm', 'Right Arm'), nameAr: 'الذراع الأيمن' },\n    leftElbow: { id: 'leftElbow', name: t('leftElbow', 'Left Elbow'), nameAr: 'الكوع الأيسر' },\n    rightElbow: { id: 'rightElbow', name: t('rightElbow', 'Right Elbow'), nameAr: 'الكوع الأيمن' },\n    leftWrist: { id: 'leftWrist', name: t('leftWrist', 'Left Wrist'), nameAr: 'المعصم الأيسر' },\n    rightWrist: { id: 'rightWrist', name: t('rightWrist', 'Right Wrist'), nameAr: 'المعصم الأيمن' },\n    chest: { id: 'chest', name: t('chest', 'Chest'), nameAr: 'الصدر' },\n    upperBack: { id: 'upperBack', name: t('upperBack', 'Upper Back'), nameAr: 'أعلى الظهر' },\n    lowerBack: { id: 'lowerBack', name: t('lowerBack', 'Lower Back'), nameAr: 'أسفل الظهر' },\n    abdomen: { id: 'abdomen', name: t('abdomen', 'Abdomen'), nameAr: 'البطن' },\n    leftHip: { id: 'leftHip', name: t('leftHip', 'Left Hip'), nameAr: 'الورك الأيسر' },\n    rightHip: { id: 'rightHip', name: t('rightHip', 'Right Hip'), nameAr: 'الورك الأيمن' },\n    leftThigh: { id: 'leftThigh', name: t('leftThigh', 'Left Thigh'), nameAr: 'الفخذ الأيسر' },\n    rightThigh: { id: 'rightThigh', name: t('rightThigh', 'Right Thigh'), nameAr: 'الفخذ الأيمن' },\n    leftKnee: { id: 'leftKnee', name: t('leftKnee', 'Left Knee'), nameAr: 'الركبة اليسرى' },\n    rightKnee: { id: 'rightKnee', name: t('rightKnee', 'Right Knee'), nameAr: 'الركبة اليمنى' },\n    leftCalf: { id: 'leftCalf', name: t('leftCalf', 'Left Calf'), nameAr: 'ربلة الساق اليسرى' },\n    rightCalf: { id: 'rightCalf', name: t('rightCalf', 'Right Calf'), nameAr: 'ربلة الساق اليمنى' },\n    leftAnkle: { id: 'leftAnkle', name: t('leftAnkle', 'Left Ankle'), nameAr: 'الكاحل الأيسر' },\n    rightAnkle: { id: 'rightAnkle', name: t('rightAnkle', 'Right Ankle'), nameAr: 'الكاحل الأيمن' },\n    leftFoot: { id: 'leftFoot', name: t('leftFoot', 'Left Foot'), nameAr: 'القدم اليسرى' },\n    rightFoot: { id: 'rightFoot', name: t('rightFoot', 'Right Foot'), nameAr: 'القدم اليمنى' }\n  };\n\n  const handleRegionClick = (regionId) => {\n    if (readOnly) return;\n\n    setSelectedRegionForModal(regionId);\n    setShowPainModal(true);\n\n    if (onRegionSelect) {\n      onRegionSelect(regionId);\n    }\n  };\n\n  const handlePainAssessment = (regionId, painLevel, details) => {\n    const updatedSelectedRegions = [...localSelectedRegions];\n    const updatedPainLevels = { ...localPainLevels };\n    const updatedPainDetails = { ...painDetails };\n\n    if (painLevel > 0) {\n      if (!updatedSelectedRegions.includes(regionId)) {\n        updatedSelectedRegions.push(regionId);\n      }\n      updatedPainLevels[regionId] = painLevel;\n      updatedPainDetails[regionId] = details;\n    } else {\n      const index = updatedSelectedRegions.indexOf(regionId);\n      if (index > -1) {\n        updatedSelectedRegions.splice(index, 1);\n      }\n      delete updatedPainLevels[regionId];\n      delete updatedPainDetails[regionId];\n    }\n\n    setLocalSelectedRegions(updatedSelectedRegions);\n    setLocalPainLevels(updatedPainLevels);\n    setPainDetails(updatedPainDetails);\n    setShowPainModal(false);\n  };\n\n  const getPainColor = (regionId) => {\n    const painLevel = localPainLevels[regionId] || 0;\n    const isSelected = localSelectedRegions.includes(regionId);\n\n    if (painLevel === 0 && !isSelected) return '#e5e7eb'; // Gray for no pain\n    if (painLevel <= 2) return '#fef3c7'; // Light yellow for mild pain\n    if (painLevel <= 4) return '#fed7aa'; // Light orange for moderate pain\n    if (painLevel <= 6) return '#fca5a5'; // Light red for severe pain\n    if (painLevel <= 8) return '#f87171'; // Red for very severe pain\n    return '#dc2626'; // Dark red for extreme pain\n  };\n\n  const getRegionOpacity = (regionId) => {\n    const isSelected = localSelectedRegions.includes(regionId);\n    const isHovered = hoveredRegion === regionId;\n\n    if (isHovered) return 0.8;\n    if (isSelected) return 0.7;\n    return 0.5;\n  };\n\n  return (\n    <div className=\"body-map-container\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('bodyMap', 'Body Map')}\n          </h3>\n          {!readOnly && (\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('clickToSelectRegion', 'Click on body regions to select')}\n            </div>\n          )}\n        </div>\n\n        {/* Body Map SVG */}\n        <div className=\"flex justify-center\">\n          <svg\n            width=\"300\"\n            height=\"600\"\n            viewBox=\"0 0 300 600\"\n            className=\"border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700\"\n          >\n            {/* Head */}\n            <ellipse\n              cx=\"150\"\n              cy=\"50\"\n              rx=\"25\"\n              ry=\"30\"\n              fill={getPainColor('head')}\n              opacity={getRegionOpacity('head')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('head')}\n              onMouseEnter={() => setHoveredRegion('head')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Neck */}\n            <rect\n              x=\"140\"\n              y=\"75\"\n              width=\"20\"\n              height=\"25\"\n              fill={getPainColor('neck')}\n              opacity={getRegionOpacity('neck')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('neck')}\n              onMouseEnter={() => setHoveredRegion('neck')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Shoulders */}\n            <ellipse\n              cx=\"110\"\n              cy=\"120\"\n              rx=\"20\"\n              ry=\"15\"\n              fill={getPainColor('leftShoulder')}\n              opacity={getRegionOpacity('leftShoulder')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('leftShoulder')}\n              onMouseEnter={() => setHoveredRegion('leftShoulder')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n            <ellipse\n              cx=\"190\"\n              cy=\"120\"\n              rx=\"20\"\n              ry=\"15\"\n              fill={getPainColor('rightShoulder')}\n              opacity={getRegionOpacity('rightShoulder')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('rightShoulder')}\n              onMouseEnter={() => setHoveredRegion('rightShoulder')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Arms */}\n            <rect\n              x=\"85\"\n              y=\"135\"\n              width=\"15\"\n              height=\"60\"\n              fill={getPainColor('leftArm')}\n              opacity={getRegionOpacity('leftArm')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('leftArm')}\n              onMouseEnter={() => setHoveredRegion('leftArm')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n            <rect\n              x=\"200\"\n              y=\"135\"\n              width=\"15\"\n              height=\"60\"\n              fill={getPainColor('rightArm')}\n              opacity={getRegionOpacity('rightArm')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('rightArm')}\n              onMouseEnter={() => setHoveredRegion('rightArm')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Torso */}\n            <rect\n              x=\"125\"\n              y=\"100\"\n              width=\"50\"\n              height=\"80\"\n              fill={getPainColor('chest')}\n              opacity={getRegionOpacity('chest')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('chest')}\n              onMouseEnter={() => setHoveredRegion('chest')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Abdomen */}\n            <rect\n              x=\"125\"\n              y=\"180\"\n              width=\"50\"\n              height=\"60\"\n              fill={getPainColor('abdomen')}\n              opacity={getRegionOpacity('abdomen')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('abdomen')}\n              onMouseEnter={() => setHoveredRegion('abdomen')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Hips */}\n            <ellipse\n              cx=\"135\"\n              cy=\"260\"\n              rx=\"15\"\n              ry=\"20\"\n              fill={getPainColor('leftHip')}\n              opacity={getRegionOpacity('leftHip')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('leftHip')}\n              onMouseEnter={() => setHoveredRegion('leftHip')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n            <ellipse\n              cx=\"165\"\n              cy=\"260\"\n              rx=\"15\"\n              ry=\"20\"\n              fill={getPainColor('rightHip')}\n              opacity={getRegionOpacity('rightHip')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('rightHip')}\n              onMouseEnter={() => setHoveredRegion('rightHip')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Thighs */}\n            <rect\n              x=\"125\"\n              y=\"280\"\n              width=\"20\"\n              height=\"80\"\n              fill={getPainColor('leftThigh')}\n              opacity={getRegionOpacity('leftThigh')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('leftThigh')}\n              onMouseEnter={() => setHoveredRegion('leftThigh')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n            <rect\n              x=\"155\"\n              y=\"280\"\n              width=\"20\"\n              height=\"80\"\n              fill={getPainColor('rightThigh')}\n              opacity={getRegionOpacity('rightThigh')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('rightThigh')}\n              onMouseEnter={() => setHoveredRegion('rightThigh')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Knees */}\n            <ellipse\n              cx=\"135\"\n              cy=\"370\"\n              rx=\"12\"\n              ry=\"15\"\n              fill={getPainColor('leftKnee')}\n              opacity={getRegionOpacity('leftKnee')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('leftKnee')}\n              onMouseEnter={() => setHoveredRegion('leftKnee')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n            <ellipse\n              cx=\"165\"\n              cy=\"370\"\n              rx=\"12\"\n              ry=\"15\"\n              fill={getPainColor('rightKnee')}\n              opacity={getRegionOpacity('rightKnee')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('rightKnee')}\n              onMouseEnter={() => setHoveredRegion('rightKnee')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Calves */}\n            <rect\n              x=\"125\"\n              y=\"385\"\n              width=\"20\"\n              height=\"70\"\n              fill={getPainColor('leftCalf')}\n              opacity={getRegionOpacity('leftCalf')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('leftCalf')}\n              onMouseEnter={() => setHoveredRegion('leftCalf')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n            <rect\n              x=\"155\"\n              y=\"385\"\n              width=\"20\"\n              height=\"70\"\n              fill={getPainColor('rightCalf')}\n              opacity={getRegionOpacity('rightCalf')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('rightCalf')}\n              onMouseEnter={() => setHoveredRegion('rightCalf')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n\n            {/* Feet */}\n            <ellipse\n              cx=\"135\"\n              cy=\"480\"\n              rx=\"15\"\n              ry=\"25\"\n              fill={getPainColor('leftFoot')}\n              opacity={getRegionOpacity('leftFoot')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('leftFoot')}\n              onMouseEnter={() => setHoveredRegion('leftFoot')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n            <ellipse\n              cx=\"165\"\n              cy=\"480\"\n              rx=\"15\"\n              ry=\"25\"\n              fill={getPainColor('rightFoot')}\n              opacity={getRegionOpacity('rightFoot')}\n              stroke=\"#374151\"\n              strokeWidth=\"2\"\n              className=\"cursor-pointer transition-all duration-200\"\n              onClick={() => handleRegionClick('rightFoot')}\n              onMouseEnter={() => setHoveredRegion('rightFoot')}\n              onMouseLeave={() => setHoveredRegion(null)}\n            />\n          </svg>\n        </div>\n\n        {/* Hovered Region Info */}\n        {hoveredRegion && (\n          <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <div className=\"text-sm font-medium text-blue-900 dark:text-blue-200\">\n              {isRTL ? bodyRegions[hoveredRegion]?.nameAr : bodyRegions[hoveredRegion]?.name}\n            </div>\n            {localPainLevels[hoveredRegion] && (\n              <div className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\n                {t('painLevel', 'Pain Level')}: {localPainLevels[hoveredRegion]}/10\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* Instructions */}\n        {showInstructions && (\n          <div className=\"mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-200 mb-2\">\n              <i className=\"fas fa-info-circle mr-2\"></i>\n              {t('instructions', 'Instructions')}\n            </h4>\n            <ul className=\"text-sm text-blue-800 dark:text-blue-300 space-y-1\">\n              <li>• {t('clickBodyRegion', 'Click on any body region to assess pain level')}</li>\n              <li>• {t('selectPainLevel', 'Select pain level from 0 (no pain) to 10 (extreme pain)')}</li>\n              <li>• {t('addPainDetails', 'Add details about pain type, duration, and frequency')}</li>\n              <li>• {t('saveAssessment', 'Click Save Assessment to store your data')}</li>\n            </ul>\n          </div>\n        )}\n\n        {/* Save Button */}\n        {patientId && localSelectedRegions.length > 0 && (\n          <div className=\"mt-6 flex justify-center\">\n            <button\n              onClick={saveBodyMapData}\n              disabled={isLoading}\n              className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\"\n            >\n              {isLoading ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  <span>{t('saving', 'Saving...')}</span>\n                </>\n              ) : (\n                <>\n                  <i className=\"fas fa-save\"></i>\n                  <span>{t('saveAssessment', 'Save Assessment')}</span>\n                </>\n              )}\n            </button>\n          </div>\n        )}\n\n        {/* Pain Scale Legend */}\n        <div className=\"mt-6\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">\n            {t('painScale', 'Pain Scale')}\n          </h4>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-4 h-4 bg-gray-300 rounded\"></div>\n              <span className=\"text-xs text-gray-600 dark:text-gray-400\">0</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-4 h-4 bg-yellow-200 rounded\"></div>\n              <span className=\"text-xs text-gray-600 dark:text-gray-400\">1-2</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-4 h-4 bg-orange-200 rounded\"></div>\n              <span className=\"text-xs text-gray-600 dark:text-gray-400\">3-4</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-4 h-4 bg-red-200 rounded\"></div>\n              <span className=\"text-xs text-gray-600 dark:text-gray-400\">5-6</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-4 h-4 bg-red-400 rounded\"></div>\n              <span className=\"text-xs text-gray-600 dark:text-gray-400\">7-8</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-4 h-4 bg-red-600 rounded\"></div>\n              <span className=\"text-xs text-gray-600 dark:text-gray-400\">9-10</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Pain Assessment Modal */}\n      {showPainModal && selectedRegionForModal && (\n        <PainAssessmentModal\n          regionId={selectedRegionForModal}\n          regionName={bodyRegions[selectedRegionForModal]?.name || selectedRegionForModal}\n          currentPainLevel={localPainLevels[selectedRegionForModal] || 0}\n          currentDetails={painDetails[selectedRegionForModal] || {}}\n          onSave={handlePainAssessment}\n          onClose={() => setShowPainModal(false)}\n        />\n      )}\n    </div>\n  );\n};\n\n// Pain Assessment Modal Component\nconst PainAssessmentModal = ({ regionId, regionName, currentPainLevel, currentDetails, onSave, onClose }) => {\n  const { t } = useLanguage();\n  const [painLevel, setPainLevel] = useState(currentPainLevel);\n  const [painType, setPainType] = useState(currentDetails.type || '');\n  const [painDuration, setPainDuration] = useState(currentDetails.duration || '');\n  const [painFrequency, setPainFrequency] = useState(currentDetails.frequency || '');\n  const [notes, setNotes] = useState(currentDetails.notes || '');\n\n  const handleSave = () => {\n    const details = {\n      type: painType,\n      duration: painDuration,\n      frequency: painFrequency,\n      notes: notes\n    };\n    onSave(regionId, painLevel, details);\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('painAssessment', 'Pain Assessment')}\n            </h3>\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"fas fa-times\"></i>\n            </button>\n          </div>\n\n          <div className=\"mb-4\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n              {t('region', 'Region')}: {regionName}\n            </h4>\n          </div>\n\n          {/* Pain Level Slider */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('painLevel', 'Pain Level')}: {painLevel}/10\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"10\"\n              value={painLevel}\n              onChange={(e) => setPainLevel(parseInt(e.target.value))}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n            />\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>{t('noPain', 'No Pain')}</span>\n              <span>{t('extremePain', 'Extreme Pain')}</span>\n            </div>\n          </div>\n\n          {/* Buttons */}\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={onClose}\n              className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors\"\n            >\n              {t('cancel', 'Cancel')}\n            </button>\n            <button\n              onClick={handleSave}\n              className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              {t('save', 'Save')}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BodyMap;\n"], "names": ["PainAssessmentModal", "_ref2", "regionId", "regionName", "currentPainLevel", "currentDetails", "onSave", "onClose", "t", "useLanguage", "painLevel", "setPainLevel", "useState", "painType", "setPainType", "type", "painDuration", "setPainDuration", "duration", "painFrequency", "setPainFrequency", "frequency", "notes", "setNotes", "_jsx", "className", "children", "_jsxs", "onClick", "min", "max", "value", "onChange", "e", "parseInt", "target", "handleSave", "_ref", "_bodyRegions$hoveredR", "_bodyRegions$hoveredR2", "_bodyRegions$selected", "patientId", "onRegionSelect", "selectedRegions", "painLevels", "readOnly", "showInstructions", "isRTL", "hoveredRegion", "setHoveredRegion", "localSelectedRegions", "setLocalSelectedRegions", "localPainLevels", "setLocalPainLevels", "painDetails", "setPainDetails", "showPainModal", "setShowPainModal", "selectedRegionForModal", "setSelectedRegionForModal", "isLoading", "setIsLoading", "useEffect", "loadBodyMapData", "async", "data", "dataService", "getBodyMapData", "error", "console", "bodyRegions", "head", "id", "name", "nameAr", "neck", "leftShoulder", "rightShoulder", "leftArm", "rightArm", "leftElbow", "<PERSON><PERSON><PERSON><PERSON>", "leftWrist", "rightWrist", "chest", "upperBack", "lowerBack", "abdomen", "leftHip", "rightHip", "leftThigh", "rightThigh", "leftKnee", "<PERSON><PERSON><PERSON>", "leftCalf", "rightCalf", "leftAnkle", "right<PERSON>nkle", "leftFoot", "rightFoot", "handleRegionClick", "getPainColor", "isSelected", "includes", "getRegionOpacity", "width", "height", "viewBox", "cx", "cy", "rx", "ry", "fill", "opacity", "stroke", "strokeWidth", "onMouseEnter", "onMouseLeave", "x", "y", "length", "bodyMapData", "saveBodyMapData", "toast", "success", "disabled", "_Fragment", "handlePainAssessment", "details", "updatedSelectedRegions", "updatedPainLevels", "_objectSpread", "updatedPainDetails", "push", "index", "indexOf", "splice"], "sourceRoot": ""}