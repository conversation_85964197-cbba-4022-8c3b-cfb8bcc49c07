"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[384],{384:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var s=a(5043),r=a(7921),l=a(5475),d=a(579);const i=()=>{const{t:e,isRTL:t}=(0,r.o)(),[a,i]=(0,s.useState)("overview"),[n,o]=(0,s.useState)(""),[c,x]=(0,s.useState)("all"),[m]=(0,s.useState)([{id:1,name:"PT Adult Initial Assessment Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0623\u0648\u0644\u064a \u0644\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a \u0644\u0644\u0628\u0627\u0644\u063a\u064a\u0646",category:"assessment",description:"Comprehensive 8-page PT Adult Initial Assessment Form with body map, CARF & CBAHI compliant",descriptionAr:"\u0646\u0645\u0648\u0630\u062c \u062a\u0642\u064a\u064a\u0645 \u0623\u0648\u0644\u064a \u0634\u0627\u0645\u0644 \u0644\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a \u0645\u0646 8 \u0635\u0641\u062d\u0627\u062a \u0645\u0639 \u062e\u0631\u064a\u0637\u0629 \u0627\u0644\u062c\u0633\u0645\u060c \u0645\u062a\u0648\u0627\u0641\u0642 \u0645\u0639 CARF \u0648 CBAHI",fields:300,lastModified:"2024-02-10",status:"active",usageCount:125,compliance:["CARF","CBAHI","HIPAA"],features:["Body Map","Pain Assessment","ROM Testing","Muscle Testing","Neurodynamics"],route:"/assessment/new"},{id:2,name:"Initial Assessment Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0623\u0648\u0644\u064a",category:"assessment",description:"Comprehensive initial assessment for new patients",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u0623\u0648\u0644\u064a \u0634\u0627\u0645\u0644 \u0644\u0644\u0645\u0631\u0636\u0649 \u0627\u0644\u062c\u062f\u062f",fields:12,lastModified:"2024-01-15",status:"active",usageCount:45},{id:2,name:"Progress Evaluation Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u062a\u0642\u062f\u0645",category:"evaluation",description:"Monthly progress evaluation for ongoing treatments",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u062a\u0642\u062f\u0645 \u0627\u0644\u0634\u0647\u0631\u064a \u0644\u0644\u0639\u0644\u0627\u062c\u0627\u062a \u0627\u0644\u062c\u0627\u0631\u064a\u0629",fields:8,lastModified:"2024-01-12",status:"active",usageCount:32},{id:3,name:"Sensory Profile Assessment",nameAr:"\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u062d\u0633\u064a",category:"special_needs",description:"Detailed sensory processing assessment for special needs patients",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u0645\u0641\u0635\u0644 \u0644\u0644\u0645\u0639\u0627\u0644\u062c\u0629 \u0627\u0644\u062d\u0633\u064a\u0629 \u0644\u0645\u0631\u0636\u0649 \u0627\u0644\u0627\u062d\u062a\u064a\u0627\u062c\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629",fields:15,lastModified:"2024-01-10",status:"active",usageCount:28},{id:4,name:"Family Consultation Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u0627\u0633\u062a\u0634\u0627\u0631\u0629 \u0627\u0644\u0623\u0633\u0631\u0629",category:"consultation",description:"Family meeting and consultation documentation",descriptionAr:"\u062a\u0648\u062b\u064a\u0642 \u0627\u062c\u062a\u0645\u0627\u0639 \u0648\u0627\u0633\u062a\u0634\u0627\u0631\u0629 \u0627\u0644\u0623\u0633\u0631\u0629",fields:6,lastModified:"2024-01-08",status:"active",usageCount:18},{id:5,name:"Discharge Summary Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u0645\u0644\u062e\u0635 \u0627\u0644\u062e\u0631\u0648\u062c",category:"discharge",description:"Patient discharge summary and recommendations",descriptionAr:"\u0645\u0644\u062e\u0635 \u062e\u0631\u0648\u062c \u0627\u0644\u0645\u0631\u064a\u0636 \u0648\u0627\u0644\u062a\u0648\u0635\u064a\u0627\u062a",fields:10,lastModified:"2024-01-05",status:"draft",usageCount:5}]),[g]=(0,s.useState)([{id:1,formName:"Initial Assessment Form",patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",patientNameEn:"Ahmed Mohammed Al-Ahmed",submittedBy:"Dr. Sarah Al-Rashid",submittedDate:"2024-01-20",status:"completed"},{id:2,formName:"Progress Evaluation Form",patientName:"\u0641\u0627\u0637\u0645\u0629 \u0639\u0644\u064a \u0627\u0644\u0633\u0627\u0644\u0645",patientNameEn:"Fatima Ali Al-Salem",submittedBy:"Dr. Ahmed Al-Mansouri",submittedDate:"2024-01-19",status:"pending_review"},{id:3,formName:"Sensory Profile Assessment",patientName:"\u0645\u062d\u0645\u062f \u0639\u0628\u062f\u0627\u0644\u0644\u0647 \u0627\u0644\u062e\u0627\u0644\u062f",patientNameEn:"Mohammed Abdullah Al-Khalid",submittedBy:"Dr. Maryam Al-Zahra",submittedDate:"2024-01-18",status:"completed"}]),h={assessment:{label:e("assessment","Assessment"),color:"bg-blue-100 text-blue-800"},evaluation:{label:e("evaluation","Evaluation"),color:"bg-green-100 text-green-800"},special_needs:{label:e("specialNeeds","Special Needs"),color:"bg-purple-100 text-purple-800"},consultation:{label:e("consultation","Consultation"),color:"bg-yellow-100 text-yellow-800"},discharge:{label:e("discharge","Discharge"),color:"bg-gray-100 text-gray-800"}},b={active:{label:e("active","Active"),color:"bg-green-100 text-green-800"},draft:{label:e("draft","Draft"),color:"bg-yellow-100 text-yellow-800"},archived:{label:e("archived","Archived"),color:"bg-gray-100 text-gray-800"},completed:{label:e("completed","Completed"),color:"bg-green-100 text-green-800"},pending_review:{label:e("pendingReview","Pending Review"),color:"bg-yellow-100 text-yellow-800"}},u=m.filter(e=>{const t=e.name.toLowerCase().includes(n.toLowerCase())||e.nameAr.includes(n),a="all"===c||e.category===c;return t&&a}),p=[{id:"overview",label:e("overview","Overview"),icon:"fas fa-tachometer-alt"},{id:"templates",label:e("formTemplates","Form Templates"),icon:"fas fa-file-alt"},{id:"submitted",label:e("submittedForms","Submitted Forms"),icon:"fas fa-inbox"},{id:"analytics",label:e("analytics","Analytics"),icon:"fas fa-chart-bar"},{id:"builder",label:e("formBuilder","Form Builder"),icon:"fas fa-tools"},{id:"settings",label:e("settings","Settings"),icon:"fas fa-cog"}];return(0,d.jsxs)("div",{className:"p-6 ".concat(t?"font-arabic":"font-english"),children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("forms","Forms")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("formsDesc","Manage assessment forms and patient documentation")})]}),(0,d.jsx)("div",{className:"flex space-x-4",children:(0,d.jsxs)(l.N_,{to:"/forms/builder",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("createNewForm","Create New Form")]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-file-alt text-blue-600 dark:text-blue-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalTemplates","Total Templates")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:m.length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("activeTemplates","Active Templates")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:m.filter(e=>"active"===e.status).length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-inbox text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("submittedForms","Submitted Forms")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-chart-bar text-purple-600 dark:text-purple-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalUsage","Total Usage")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:m.reduce((e,t)=>e+t.usageCount,0)})]})]})})]}),(0,d.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,d.jsx)("nav",{className:"-mb-px flex space-x-8",children:p.map(e=>(0,d.jsxs)("button",{onClick:()=>i(e.id),className:"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(a===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,d.jsx)("i",{className:e.icon}),(0,d.jsx)("span",{children:e.label})]},e.id))})}),"overview"===a&&(0,d.jsx)("div",{className:"space-y-8",children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-blue-900 dark:text-blue-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-tachometer-alt text-blue-600 dark:text-blue-400 mr-2"}),e("formsOverview","Forms Management Overview")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)(l.N_,{to:"/forms/templates",className:"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-900/60 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-th-large text-blue-600 dark:text-blue-400 text-2xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100",children:e("formTemplates","Form Templates")}),(0,d.jsxs)("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:[m.length," templates available"]})]})]}),(0,d.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:e("formTemplatesDesc","Browse and use pre-built form templates for patient assessments")})]}),(0,d.jsxs)(l.N_,{to:"/forms/analytics",className:"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-900/60 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-chart-bar text-green-600 dark:text-green-400 text-2xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-green-900 dark:text-green-100",children:e("formAnalytics","Form Analytics")}),(0,d.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:"87.5% completion rate"})]})]}),(0,d.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:e("formAnalyticsDesc","View comprehensive insights into form usage and performance")})]}),(0,d.jsxs)(l.N_,{to:"/forms/builder",className:"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg group-hover:bg-purple-200 dark:group-hover:bg-purple-900/60 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-tools text-purple-600 dark:text-purple-400 text-2xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-purple-900 dark:text-purple-100",children:e("formBuilder","Form Builder")}),(0,d.jsx)("p",{className:"text-sm text-purple-600 dark:text-purple-400",children:"Create custom forms"})]})]}),(0,d.jsx)("p",{className:"text-sm text-purple-700 dark:text-purple-300",children:e("formBuilderDesc","Build custom forms with drag-and-drop interface and advanced features")})]}),(0,d.jsxs)(l.N_,{to:"/forms/submissions",className:"bg-white dark:bg-orange-800/20 border border-orange-200 dark:border-orange-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg group-hover:bg-orange-200 dark:group-hover:bg-orange-900/60 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-inbox text-orange-600 dark:text-orange-400 text-2xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-orange-900 dark:text-orange-100",children:e("formSubmissions","Form Submissions")}),(0,d.jsxs)("p",{className:"text-sm text-orange-600 dark:text-orange-400",children:[g.length," submissions"]})]})]}),(0,d.jsx)("p",{className:"text-sm text-orange-700 dark:text-orange-300",children:e("formSubmissionsDesc","Review and manage all submitted forms and patient data")})]}),(0,d.jsxs)(l.N_,{to:"/forms/settings",className:"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-indigo-100 dark:bg-indigo-900/40 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-900/60 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-cog text-indigo-600 dark:text-indigo-400 text-2xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-indigo-900 dark:text-indigo-100",children:e("formSettings","Form Settings")}),(0,d.jsx)("p",{className:"text-sm text-indigo-600 dark:text-indigo-400",children:"Configure system"})]})]}),(0,d.jsx)("p",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:e("formSettingsDesc","Configure form behavior, security, and system preferences")})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-teal-800/20 border border-teal-200 dark:border-teal-600 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-teal-100 dark:bg-teal-900/40 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-bolt text-teal-600 dark:text-teal-400 text-2xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-teal-900 dark:text-teal-100",children:e("quickActions","Quick Actions")}),(0,d.jsx)("p",{className:"text-sm text-teal-600 dark:text-teal-400",children:"Common tasks"})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)(l.N_,{to:"/forms/pt-assessment",className:"block text-sm text-teal-700 dark:text-teal-300 hover:text-teal-900 dark:hover:text-teal-100 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("newPTAssessment","New PT Assessment")]}),(0,d.jsxs)(l.N_,{to:"/forms/treatment-plan",className:"block text-sm text-teal-700 dark:text-teal-300 hover:text-teal-900 dark:hover:text-teal-100 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("newTreatmentPlan","New Treatment Plan")]}),(0,d.jsxs)(l.N_,{to:"/forms/daily-progress",className:"block text-sm text-teal-700 dark:text-teal-300 hover:text-teal-900 dark:hover:text-teal-100 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("newProgressNote","New Progress Note")]})]})]})]})]})}),"templates"===a&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("search","Search")}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("input",{type:"text",value:n,onChange:e=>o(e.target.value),placeholder:e("searchForms","Search forms..."),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,d.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("category","Category")}),(0,d.jsxs)("select",{value:c,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"all",children:e("allCategories","All Categories")}),(0,d.jsx)("option",{value:"assessment",children:e("assessment","Assessment")}),(0,d.jsx)("option",{value:"evaluation",children:e("evaluation","Evaluation")}),(0,d.jsx)("option",{value:"special_needs",children:e("specialNeeds","Special Needs")}),(0,d.jsx)("option",{value:"consultation",children:e("consultation","Consultation")}),(0,d.jsx)("option",{value:"discharge",children:e("discharge","Discharge")})]})]}),(0,d.jsx)("div",{className:"flex items-end",children:(0,d.jsxs)("button",{className:"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-filter mr-2"}),e("advancedFilters","Advanced Filters")]})})]})}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 p-6",children:u.map(a=>(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-md transition-shadow",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t?a.nameAr:a.name}),(0,d.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(b[a.status].color),children:b[a.status].label})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:t?a.descriptionAr:a.description}),a.compliance&&(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-4",children:a.compliance.map(e=>(0,d.jsxs)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("CARF"===e?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200":"CBAHI"===e?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200":"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-200"),children:[(0,d.jsx)("i",{className:"fas ".concat("CARF"===e?"fa-certificate":"CBAHI"===e?"fa-shield-alt":"fa-lock"," mr-1")}),e]},e))}),a.features&&(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("features","Features"),":"]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-1",children:a.features.map(e=>(0,d.jsx)("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded",children:e},e))})]}),(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("category","Category"),":"]}),(0,d.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(h[a.category].color),children:h[a.category].label})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("fields","Fields"),":"]}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a.fields})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("usage","Usage"),":"]}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a.usageCount})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("lastModified","Last Modified"),":"]}),(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:new Date(a.lastModified).toLocaleDateString()})]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[a.route?(0,d.jsx)(l.N_,{to:a.route,className:"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors text-center",children:e("useForm","Use Form")}):(0,d.jsx)("button",{className:"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",children:e("useForm","Use Form")}),(0,d.jsx)("button",{className:"px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-edit"})}),(0,d.jsx)("button",{className:"px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-copy"})})]})]},a.id))}),0===u.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-file-alt text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noFormsFound","No forms found")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("tryAdjustingFilters","Try adjusting your search or filters")})]})]})]}),"submitted"===a&&(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("formName","Form Name")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("submittedBy","Submitted By")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("submittedDate","Submitted Date")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,d.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:g.map(e=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.formName})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t?e.patientName:e.patientNameEn})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:e.submittedBy}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:new Date(e.submittedDate).toLocaleDateString()}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(b[e.status].color),children:b[e.status].label})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300",children:(0,d.jsx)("i",{className:"fas fa-eye"})}),(0,d.jsx)("button",{className:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300",children:(0,d.jsx)("i",{className:"fas fa-download"})}),(0,d.jsx)("button",{className:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300",children:(0,d.jsx)("i",{className:"fas fa-edit"})})]})})]},e.id))})]})}),0===g.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-inbox text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noSubmittedForms","No submitted forms")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("submittedFormsPrompt","Submitted forms will appear here")})]})]}),"builder"===a&&(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-tools text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("formBuilder","Form Builder")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:e("formBuilderDesc","Create custom forms with drag-and-drop interface")}),(0,d.jsxs)(l.N_,{to:"/forms/builder",className:"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("startBuilding","Start Building")]})]})}),"analytics"===a&&(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-chart-bar text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("formAnalytics","Form Analytics")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:e("analyticsDesc","View comprehensive insights into form usage and performance")}),(0,d.jsxs)(l.N_,{to:"/forms/analytics",className:"inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-chart-line mr-2"}),e("viewAnalytics","View Analytics")]})]})}),"settings"===a&&(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-cog text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("formSettings","Form Settings")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:e("settingsDesc","Configure form behavior, security, and system preferences")}),(0,d.jsxs)(l.N_,{to:"/forms/settings",className:"inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-tools mr-2"}),e("manageSettings","Manage Settings")]})]})})]})}}}]);
//# sourceMappingURL=384.7c24b4d8.chunk.js.map