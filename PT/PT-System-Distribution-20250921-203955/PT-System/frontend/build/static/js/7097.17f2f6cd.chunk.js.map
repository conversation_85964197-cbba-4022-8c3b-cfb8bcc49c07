{"version": 3, "file": "static/js/7097.17f2f6cd.chunk.js", "mappings": "uNAKA,MAwZA,EAxZuBA,KACrB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MAEVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,CAC3CG,SAAU,CAAC,EACXC,aAAc,CAAC,EACfC,kBAAmB,CAAC,EACpBC,aAAc,CAAC,KAEVC,EAAgBC,IAAqBR,EAAAA,EAAAA,UAAS,UAC9CS,EAAgBC,IAAqBV,EAAAA,EAAAA,UAAS,aAC9CW,EAASC,IAAcZ,EAAAA,EAAAA,UAAS,CACrCa,SAAU,GACVC,OAAQ,GACRC,UAAW,GACXC,cAAe,GACfC,UAAW,MAGbC,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACZ,EAAgBE,IAEpB,MAAMU,EAAiBC,UACrB,IACErB,GAAW,GAGX,MAAMsB,EAAY,CAChBlB,SAAU,6BACVC,aAAc,iCACdkB,SAAU,6BACVhB,aAAc,mCAGViB,QAAiBC,MAAM,GAADC,OAAIJ,EAAUZ,GAAe,YAAAgB,OAAWlB,GAAkB,CACpFmB,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9B7B,EAAc8B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbD,GAAI,IACP,CAACvB,GAAiBqB,EAAOI,MAAQ,CAAC,IAEtC,KAAO,CAEL,MAAMC,EAAW,CACfhC,SAAU,CACRiC,cAAe,IACfC,eAAgB,IAChBC,YAAa,GACbC,mBAAoB,GACpBC,WAAY,KACZC,mBAAoB,CAAEC,KAAM,GAAIC,OAAQ,IACxCC,eAAgB,CACd,mBAAoB,IACpB,uBAAwB,GACxB,iBAAkB,GAClB,gBAAiB,KAGrBxC,aAAc,CACZyC,UAAW,CACT,OAAQ,GACR,QAAS,GACT,QAAS,GACT,QAAS,GACT,MAAO,IAETC,WAAY,CACV,aAAgB,GAChB,WAAc,GACd,UAAa,GACb,UAAa,IAEfC,UAAW,CACT,OAAU,IACV,OAAU,GACV,OAAU,KAGdzB,SAAU,CACR0B,gBAAiB,KACjBC,yBAA0B,GAC1BC,oBAAqB,KACrBC,gBAAiB,KACjBC,sBAAuB,CACrB,UAAa,GACb,KAAQ,GACR,KAAQ,GACR,KAAQ,IAGZ9C,aAAc,CACZ+C,kBAAmB,GACnBP,WAAY,CACV,kBAAmB,GACnB,iBAAkB,GAClB,gBAAiB,GACjB,KAAQ,EACR,MAAS,GAEXQ,eAAgB,CACd,kBAAmB,GACnB,qBAAsB,GACtB,sBAAuB,GACvB,qBAAsB,MAK5BpD,EAAc8B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbD,GAAI,IACP,CAACvB,GAAiB0B,EAAS1B,IAAmB,CAAC,IAEnD,CACF,CAAE,MAAO8C,GACPC,QAAQD,MAAM,6BAA8BA,GAC5CE,EAAAA,GAAMF,MAAM9D,EAAE,sBAAuB,yBACvC,CAAC,QACCM,GAAW,EACb,GAGI2D,EAAetC,UACnB,IACErB,GAAW,GAEX,MAAMwB,QAAiBC,MAAM,iDAADC,OAAkDkC,EAAM,YAAAlC,OAAWlB,GAAkB,CAC/GmB,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,aAIpD,IAAIL,EAASM,GAaX,MAAM,IAAI+B,MAAM,iBAbD,CACf,MAAMC,QAAatC,EAASsC,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,kBAAA7C,OAAqBlB,EAAc,KAAAkB,OAAIkC,GACjDQ,SAASI,KAAKC,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAASI,KAAKI,YAAYT,GAE1BT,EAAAA,GAAMmB,QAAQnF,EAAE,iBAAkB,gCACpC,CAGF,CAAE,MAAO8D,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAMF,MAAM9D,EAAE,iBAAkB,0BAClC,CAAC,QACCM,GAAW,EACb,GAGI8E,EAAc,CAClB,CAAEC,GAAI,WAAYC,MAAOtF,EAAE,kBAAmB,oBAAqBuF,KAAM,gBACzE,CAAEF,GAAI,eAAgBC,MAAOtF,EAAE,eAAgB,gBAAiBuF,KAAM,oBACtE,CAAEF,GAAI,WAAYC,MAAOtF,EAAE,oBAAqB,sBAAuBuF,KAAM,qBAC7E,CAAEF,GAAI,eAAgBC,MAAOtF,EAAE,sBAAuB,iBAAkBuF,KAAM,iBAqHhF,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sFAAqFC,UAClGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6GAA4GC,UACzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6GAA4GC,SACvH1F,EAAE,iBAAkB,sBAEvBwF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,kEAAiEC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCACZzF,EAAE,qBAAsB,wDAG7BwF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAM3B,EAAa,OAC5B4B,SAAUxF,EACVoF,UAAU,oGAAmGC,SAAA,EAE7GC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZzF,EAAE,YAAa,kBAElBwF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAM3B,EAAa,SAC5B4B,SAAUxF,EACVoF,UAAU,wGAAuGC,SAAA,EAEjHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZzF,EAAE,cAAe,gCAS9B2F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sGAAqGC,UAClHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E1F,EAAE,aAAc,kBAEnB2F,EAAAA,EAAAA,KAAA,UACEG,MAAO9E,EACP+E,SAAWC,GAAM/E,EAAkB+E,EAAEC,OAAOH,OAC5CL,UAAU,kIAAiIC,SAE1IN,EAAYc,IAAIC,IACfR,EAAAA,EAAAA,KAAA,UAAsBG,MAAOK,EAAKd,GAAGK,SAAES,EAAKb,OAA/Ba,EAAKd,WAKxBG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E1F,EAAE,aAAc,kBAEnBwF,EAAAA,EAAAA,MAAA,UACEM,MAAOhF,EACPiF,SAAWC,GAAMjF,EAAkBiF,EAAEC,OAAOH,OAC5CL,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQG,MAAM,OAAMJ,SAAE1F,EAAE,WAAY,gBACpC2F,EAAAA,EAAAA,KAAA,UAAQG,MAAM,QAAOJ,SAAE1F,EAAE,YAAa,iBACtC2F,EAAAA,EAAAA,KAAA,UAAQG,MAAM,UAASJ,SAAE1F,EAAE,cAAe,mBAC1C2F,EAAAA,EAAAA,KAAA,UAAQG,MAAM,OAAMJ,SAAE1F,EAAE,WAAY,sBAIxC2F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASlE,EACTmE,SAAUxF,EACVoF,UAAU,6GAA4GC,SAAA,EAEtHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZpF,EAAUL,EAAE,UAAW,cAAgBA,EAAE,gBAAiB,6BAOnE2F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iGAAgGC,SAC5GrF,GACCsF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAGjBD,EAAAA,EAAAA,MAAAY,EAAAA,SAAA,CAAAV,SAAA,CACsB,aAAnB1E,GAhNkBqF,MAC3B,MAAM5D,EAAOjC,EAAWE,SAExB,OACE8E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oJAAmJC,UAChKF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uDAAsDC,SAAE1F,EAAE,gBAAiB,qBACzF2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDAAqDC,SAAEjD,EAAKE,eAAiB,aAKhGgD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0JAAyJC,UACtKF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SAAE1F,EAAE,iBAAkB,sBAC5F2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDAAuDC,SAAEjD,EAAKG,gBAAkB,aAKnG+C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4JAA2JC,UACxKF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SAAE1F,EAAE,cAAe,mBAC3F2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAAyDC,SAAEjD,EAAKI,aAAe,aAKlG8C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0JAAyJC,UACtKF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SAAE1F,EAAE,qBAAsB,iBAClG2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAAyDC,SAAEjD,EAAKK,oBAAsB,gBAO3G0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1F,EAAE,6BAA8B,mCAEnC2F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEY,OAAOC,QAAQ9D,EAAKU,gBAAkB,CAAC,GAAG+C,IAAIM,IAAA,IAAEL,EAAMM,GAAMD,EAAA,OAC3DhB,EAAAA,EAAAA,MAAA,OAAgBC,UAAU,yDAAwDC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAAEe,KACjEd,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAES,MAFjDA,cAiJsBE,GACd,iBAAnBrF,GAvIsB0F,MAC/B,MAAMjE,EAAOjC,EAAWG,aAExB,OACEgF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1F,EAAE,kBAAmB,uBAExB2F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBY,OAAOC,QAAQ9D,EAAKW,WAAa,CAAC,GAAG8C,IAAIS,IAAA,IAAEC,EAAKH,GAAME,EAAA,OACrDnB,EAAAA,EAAAA,MAAA,OAAeC,UAAU,oCAAmCC,SAAA,EAC1DF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAAEkB,EAAI,IAAE5G,EAAE,QAAS,aACrE2F,EAAAA,EAAAA,KAAA,QAAMF,UAAU,8CAA6CC,SAAEe,MAFvDG,WAShBpB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1F,EAAE,iBAAkB,sBAEvB2F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBY,OAAOC,QAAQ9D,EAAKY,YAAc,CAAC,GAAG6C,IAAIW,IAAA,IAAErF,EAAWiF,GAAMI,EAAA,OAC5DrB,EAAAA,EAAAA,MAAA,OAAqBC,UAAU,oCAAmCC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAElE,KACpDmE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,8CAA6CC,SAAEe,MAFvDjF,gBA2GwBkF,GAClB,aAAnB1F,IACCwE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnE1F,EAAE,oBAAqB,yBAE1B2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5C1F,EAAE,wBAAyB,iEAId,iBAAnBgB,IACCwE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnE1F,EAAE,sBAAuB,4BAE5B2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5C1F,EAAE,4BAA6B,oE", "sources": ["pages/Reports/PatientReports.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst PatientReports = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  \n  const [loading, setLoading] = useState(false);\n  const [reportData, setReportData] = useState({\n    overview: {},\n    demographics: {},\n    treatmentOutcomes: {},\n    specialNeeds: {}\n  });\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n  const [selectedReport, setSelectedReport] = useState('overview');\n  const [filters, setFilters] = useState({\n    dateFrom: '',\n    dateTo: '',\n    therapist: '',\n    treatmentType: '',\n    condition: ''\n  });\n\n  useEffect(() => {\n    loadReportData();\n  }, [selectedPeriod, selectedReport]);\n\n  const loadReportData = async () => {\n    try {\n      setLoading(true);\n      \n      // Load different types of patient reports\n      const endpoints = {\n        overview: '/api/v1/analytics/patients',\n        demographics: '/api/v1/analytics/demographics',\n        outcomes: '/api/v1/analytics/outcomes',\n        specialNeeds: '/api/v1/analytics/special-needs'\n      };\n\n      const response = await fetch(`${endpoints[selectedReport]}?period=${selectedPeriod}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const result = await response.json();\n        setReportData(prev => ({\n          ...prev,\n          [selectedReport]: result.data || {}\n        }));\n      } else {\n        // Mock data for demonstration\n        const mockData = {\n          overview: {\n            totalPatients: 245,\n            activePatients: 189,\n            newPatients: 23,\n            dischargedPatients: 12,\n            averageAge: 42.5,\n            genderDistribution: { male: 45, female: 55 },\n            treatmentTypes: {\n              'Physical Therapy': 120,\n              'Occupational Therapy': 65,\n              'Speech Therapy': 35,\n              'Special Needs': 25\n            }\n          },\n          demographics: {\n            ageGroups: {\n              '0-18': 45,\n              '19-35': 67,\n              '36-50': 89,\n              '51-65': 32,\n              '65+': 12\n            },\n            conditions: {\n              'Neurological': 78,\n              'Orthopedic': 92,\n              'Pediatric': 45,\n              'Geriatric': 30\n            },\n            locations: {\n              'Riyadh': 145,\n              'Jeddah': 67,\n              'Dammam': 33\n            }\n          },\n          outcomes: {\n            improvementRate: 87.5,\n            averageTreatmentDuration: 45,\n            patientSatisfaction: 94.2,\n            goalAchievement: 82.1,\n            functionalImprovement: {\n              'Excellent': 45,\n              'Good': 32,\n              'Fair': 18,\n              'Poor': 5\n            }\n          },\n          specialNeeds: {\n            totalSpecialNeeds: 67,\n            conditions: {\n              'Autism Spectrum': 23,\n              'Cerebral Palsy': 18,\n              'Down Syndrome': 12,\n              'ADHD': 8,\n              'Other': 6\n            },\n            accommodations: {\n              'Sensory Support': 34,\n              'Communication Aids': 28,\n              'Mobility Assistance': 22,\n              'Behavioral Support': 19\n            }\n          }\n        };\n        \n        setReportData(prev => ({\n          ...prev,\n          [selectedReport]: mockData[selectedReport] || {}\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading report data:', error);\n      toast.error(t('errorLoadingReports', 'Error loading reports'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const exportReport = async (format) => {\n    try {\n      setLoading(true);\n      \n      const response = await fetch(`/api/v1/analytics/export?type=patients&format=${format}&period=${selectedPeriod}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `patient-report-${selectedPeriod}.${format}`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n        \n        toast.success(t('reportExported', 'Report exported successfully'));\n      } else {\n        throw new Error('Export failed');\n      }\n    } catch (error) {\n      console.error('Error exporting report:', error);\n      toast.error(t('errorExporting', 'Error exporting report'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const reportTypes = [\n    { id: 'overview', label: t('patientOverview', 'Patient Overview'), icon: 'fas fa-users' },\n    { id: 'demographics', label: t('demographics', 'Demographics'), icon: 'fas fa-chart-pie' },\n    { id: 'outcomes', label: t('treatmentOutcomes', 'Treatment Outcomes'), icon: 'fas fa-chart-line' },\n    { id: 'specialNeeds', label: t('specialNeedsReports', 'Special Needs'), icon: 'fas fa-heart' }\n  ];\n\n  const renderOverviewReport = () => {\n    const data = reportData.overview;\n    \n    return (\n      <div className=\"space-y-6\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg\">\n                <i className=\"fas fa-users text-blue-600 dark:text-blue-400 text-2xl\"></i>\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">{t('totalPatients', 'Total Patients')}</h3>\n                <p className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">{data.totalPatients || 0}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-6 border border-green-200 dark:border-green-700\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg\">\n                <i className=\"fas fa-user-check text-green-600 dark:text-green-400 text-2xl\"></i>\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-sm font-medium text-green-600 dark:text-green-400\">{t('activePatients', 'Active Patients')}</h3>\n                <p className=\"text-2xl font-bold text-green-900 dark:text-green-100\">{data.activePatients || 0}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg\">\n                <i className=\"fas fa-user-plus text-purple-600 dark:text-purple-400 text-2xl\"></i>\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-sm font-medium text-purple-600 dark:text-purple-400\">{t('newPatients', 'New Patients')}</h3>\n                <p className=\"text-2xl font-bold text-purple-900 dark:text-purple-100\">{data.newPatients || 0}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg p-6 border border-orange-200 dark:border-orange-700\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg\">\n                <i className=\"fas fa-user-minus text-orange-600 dark:text-orange-400 text-2xl\"></i>\n              </div>\n              <div className=\"ml-4\">\n                <h3 className=\"text-sm font-medium text-orange-600 dark:text-orange-400\">{t('dischargedPatients', 'Discharged')}</h3>\n                <p className=\"text-2xl font-bold text-orange-900 dark:text-orange-100\">{data.dischargedPatients || 0}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Treatment Types Distribution */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('treatmentTypesDistribution', 'Treatment Types Distribution')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            {Object.entries(data.treatmentTypes || {}).map(([type, count]) => (\n              <div key={type} className=\"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{count}</p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">{type}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderDemographicsReport = () => {\n    const data = reportData.demographics;\n    \n    return (\n      <div className=\"space-y-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Age Groups */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('ageDistribution', 'Age Distribution')}\n            </h3>\n            <div className=\"space-y-3\">\n              {Object.entries(data.ageGroups || {}).map(([age, count]) => (\n                <div key={age} className=\"flex justify-between items-center\">\n                  <span className=\"text-gray-700 dark:text-gray-300\">{age} {t('years', 'years')}</span>\n                  <span className=\"font-semibold text-gray-900 dark:text-white\">{count}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Conditions */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('conditionTypes', 'Condition Types')}\n            </h3>\n            <div className=\"space-y-3\">\n              {Object.entries(data.conditions || {}).map(([condition, count]) => (\n                <div key={condition} className=\"flex justify-between items-center\">\n                  <span className=\"text-gray-700 dark:text-gray-300\">{condition}</span>\n                  <span className=\"font-semibold text-gray-900 dark:text-white\">{count}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  {t('patientReports', 'Patient Reports')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-chart-bar text-blue-500 mr-2\"></i>\n                  {t('patientReportsDesc', 'Comprehensive patient analytics and reporting')}\n                </p>\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => exportReport('pdf')}\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors\"\n                >\n                  <i className=\"fas fa-file-pdf mr-2\"></i>\n                  {t('exportPDF', 'Export PDF')}\n                </button>\n                <button\n                  onClick={() => exportReport('excel')}\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors\"\n                >\n                  <i className=\"fas fa-file-excel mr-2\"></i>\n                  {t('exportExcel', 'Export Excel')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('reportType', 'Report Type')}\n            </label>\n            <select\n              value={selectedReport}\n              onChange={(e) => setSelectedReport(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              {reportTypes.map(type => (\n                <option key={type.id} value={type.id}>{type.label}</option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('timePeriod', 'Time Period')}\n            </label>\n            <select\n              value={selectedPeriod}\n              onChange={(e) => setSelectedPeriod(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"week\">{t('thisWeek', 'This Week')}</option>\n              <option value=\"month\">{t('thisMonth', 'This Month')}</option>\n              <option value=\"quarter\">{t('thisQuarter', 'This Quarter')}</option>\n              <option value=\"year\">{t('thisYear', 'This Year')}</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={loadReportData}\n              disabled={loading}\n              className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors\"\n            >\n              <i className=\"fas fa-sync-alt mr-2\"></i>\n              {loading ? t('loading', 'Loading...') : t('refreshReport', 'Refresh Report')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Report Content */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n          </div>\n        ) : (\n          <>\n            {selectedReport === 'overview' && renderOverviewReport()}\n            {selectedReport === 'demographics' && renderDemographicsReport()}\n            {selectedReport === 'outcomes' && (\n              <div className=\"text-center py-12\">\n                <i className=\"fas fa-chart-line text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('treatmentOutcomes', 'Treatment Outcomes')}\n                </h3>\n                <p className=\"text-gray-500 dark:text-gray-400\">\n                  {t('outcomesReportContent', 'Treatment outcomes report content will be displayed here')}\n                </p>\n              </div>\n            )}\n            {selectedReport === 'specialNeeds' && (\n              <div className=\"text-center py-12\">\n                <i className=\"fas fa-heart text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('specialNeedsReports', 'Special Needs Reports')}\n                </h3>\n                <p className=\"text-gray-500 dark:text-gray-400\">\n                  {t('specialNeedsReportContent', 'Special needs report content will be displayed here')}\n                </p>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default PatientReports;\n"], "names": ["PatientReports", "t", "isRTL", "useLanguage", "user", "useAuth", "loading", "setLoading", "useState", "reportData", "setReportData", "overview", "demographics", "treatmentOutcomes", "specialNeeds", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "selectedReport", "setSelectedReport", "filters", "setFilters", "dateFrom", "dateTo", "therapist", "treatmentType", "condition", "useEffect", "loadReportData", "async", "endpoints", "outcomes", "response", "fetch", "concat", "headers", "localStorage", "getItem", "ok", "result", "json", "prev", "_objectSpread", "data", "mockData", "totalPatients", "activePatients", "newPatients", "dischargedPatients", "averageAge", "genderDistribution", "male", "female", "treatmentTypes", "ageGroups", "conditions", "locations", "improvementRate", "averageTreatmentDuration", "patientSatisfaction", "goalAchievement", "functionalImprovement", "totalSpecialNeeds", "accommodations", "error", "console", "toast", "exportReport", "format", "Error", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "success", "reportTypes", "id", "label", "icon", "_jsxs", "className", "children", "_jsx", "onClick", "disabled", "value", "onChange", "e", "target", "map", "type", "_Fragment", "renderOverviewReport", "Object", "entries", "_ref", "count", "renderDemographicsReport", "_ref2", "age", "_ref3"], "sourceRoot": ""}