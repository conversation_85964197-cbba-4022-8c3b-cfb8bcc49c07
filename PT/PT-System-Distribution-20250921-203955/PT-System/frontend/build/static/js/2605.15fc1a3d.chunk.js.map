{"version": 3, "file": "static/js/2605.15fc1a3d.chunk.js", "mappings": "yLAGA,MAqSA,EArS4BA,KAC1B,MAAOC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAQC,IAAaF,EAAAA,EAAAA,UAAS,MAsMrC,OACEG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,UAC3DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,UAChCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDC,SAAC,wCAItEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,UACEI,QAASA,KACPC,aAAaC,QACbC,QAAQC,IAAI,qCACZT,EAAU,CAAEU,SAAS,EAAMC,QAAS,+CAEtCT,UAAU,gFAA+EC,SAC1F,gCAIDF,EAAAA,EAAAA,KAAA,UACEI,QArEoBO,UAC9B,IACEJ,QAAQC,IAAI,gDAGZ,MAAMI,QAAkBC,MAAM,uCAC9BN,QAAQC,IAAI,sCAA6BI,EAAUE,QAGnD,MAAMC,QAAkBF,MAAM,uCAAwC,CACpEG,OAAQ,YAEVT,QAAQC,IAAI,wCAA+BO,EAAUD,QAGrD,MAAMG,EAAQZ,aAAaa,QAAQ,iBAC7BC,QAAkBN,MAAM,uCAAwC,CACpEO,QAAS,CACP,cAAgB,UAADC,OAAYJ,GAC3B,eAAgB,sBAGpBV,QAAQC,IAAI,+CAAsCW,EAAUL,QAE5DP,QAAQC,IAAI,+CAEZT,EAAU,CACRU,SAAS,EACTa,KAAM,CACJC,YAAaX,EAAUE,OACvBU,aAAcT,EAAUD,OACxBW,qBAAsBN,EAAUL,QAElCJ,QAAS,wCAGb,CAAE,MAAOgB,GACPnB,QAAQmB,MAAM,8BAA0BA,GACxCnB,QAAQmB,MAAM,4CAEd3B,EAAU,CACRU,SAAS,EACTiB,MAAOA,EAAMhB,QACbiB,QAAS,CAAEC,aAAcF,EAAMG,aAEnC,GAyBU5B,UAAU,sFAAqFC,SAChG,+BAIDF,EAAAA,EAAAA,KAAA,UACEI,QA/HeO,UACzB,IACEJ,QAAQC,IAAI,0CACZD,QAAQC,IAAI,6BAAoBsB,gCAEhC,MAAMb,EAAQZ,aAAaa,QAAQ,iBAMnC,GALAX,QAAQC,IAAI,gCAAuBS,GACnCV,QAAQC,IAAI,6BAAoBS,EAAQA,EAAMc,OAAS,GACvDxB,QAAQC,IAAI,8BAAqBS,EAAQA,EAAMe,UAAU,EAAG,IAAM,MAAQ,QAGtEf,EACF,IACE,MAAMgB,EAAQhB,EAAMiB,MAAM,KAE1B,GADA3B,QAAQC,IAAI,gCAAuByB,EAAMF,QACpB,IAAjBE,EAAMF,OAAc,CACtB,MAAMI,EAAUC,KAAKC,MAAMC,KAAKL,EAAM,KACtC1B,QAAQC,IAAI,4BAAmB2B,GAC/B5B,QAAQC,IAAI,4BAAmB,IAAI+B,KAAmB,IAAdJ,EAAQK,MAChDjC,QAAQC,IAAI,4BAAmB+B,KAAKE,MAAsB,IAAdN,EAAQK,IACtD,CACF,CAAE,MAAOE,GACPnC,QAAQmB,MAAM,kCAAyBgB,EACzC,CAGF,MAAMC,QAAiBC,EAAAA,GAAIC,IAAI,YAC/BtC,QAAQC,IAAI,0BAAiBmC,GAC7BpC,QAAQC,IAAI,0CAEZT,EAAU,CACRU,SAAS,EACTa,KAAMqB,EACNjC,QAAS,8BAEb,CAAE,MAAOgB,GAAQ,IAADoB,EAAAC,EACdxC,QAAQmB,MAAM,2BAAuBA,GACrCnB,QAAQmB,MAAM,sCAEd3B,EAAU,CACRU,SAAS,EACTiB,MAAOA,EAAMhB,QACbiB,QAAS,CACPgB,SAAwB,QAAhBG,EAAEpB,EAAMiB,gBAAQ,IAAAG,OAAA,EAAdA,EAAgBxB,KAC1BR,OAAsB,QAAhBiC,EAAErB,EAAMiB,gBAAQ,IAAAI,OAAA,EAAdA,EAAgBjC,OACxBkC,QAAStB,EAAMsB,QAAU,+BAAiC,oBAGhE,GAgFU/C,UAAU,kFAAiFC,SAC5F,sCAIDF,EAAAA,EAAAA,KAAA,UACEI,QAvOgBO,UAC1Bf,GAAW,GACXG,EAAU,MAEV,IACEQ,QAAQC,IAAI,4CAGZ,MAAMS,EAAQZ,aAAaa,QAAQ,iBACnCX,QAAQC,IAAI,kCAAyBS,GACrCV,QAAQC,IAAI,4BAAmBS,EAAQA,EAAMe,UAAU,EAAG,IAAM,MAAQ,QAGxE,MAAMiB,EAAW,CACfC,UAAW,OACXC,SAAU,UACVC,WAAYC,KAAKC,SAASzB,WAAWG,UAAU,EAAG,IAClDuB,YAAa,aACbC,OAAQ,OACRC,MAAO,gBACPC,MAAO,yBAEPC,QAAS,CACPC,OAAQ,kBACRC,KAAM,SACNC,MAAO,kBACPC,QAAS,QACTC,QAAS,gBAGXC,iBAAkB,CAChBC,KAAM,oBACNT,MAAO,gBACPU,aAAc,UAGhBC,UAAW,CACTC,SAAU,iBACVC,aAAc,aACdC,YAAa,WAIjBhE,QAAQC,IAAI,kCAAyByC,GAGrC,MAAMN,QAAiBC,EAAAA,GAAI4B,KAAK,YAAavB,GAE7C1C,QAAQC,IAAI,kCAAyBmC,GAErC5C,EAAU,CACRU,SAAS,EACTa,KAAMqB,EACNjC,QAAS,kCAGXH,QAAQC,IAAI,4CAEd,CAAE,MAAOkB,GACPnB,QAAQmB,MAAM,sBAAkBA,GAEhC,IAAI+C,EAAe,gBACfC,EAAe,CAAC,EAEC,IAADC,EAApB,GAAIjD,EAAMiB,SAER8B,GAAkC,QAAnBE,EAAAjD,EAAMiB,SAASrB,YAAI,IAAAqD,OAAA,EAAnBA,EAAqBjE,UAAO,QAAAW,OAAYK,EAAMiB,SAAS7B,QACtE4D,EAAe,CACb5D,OAAQY,EAAMiB,SAAS7B,OACvBQ,KAAMI,EAAMiB,SAASrB,KACrBF,QAASM,EAAMiB,SAASvB,cAEjBM,EAAMsB,SAEfyB,EAAe,uCACfC,EAAe,CACb1B,QAAStB,EAAMsB,QACftC,QAASgB,EAAMhB,WAIjB+D,EAAe/C,EAAMhB,QACrBgE,EAAe,CAAEhD,MAAOA,EAAMG,aAGhC9B,EAAU,CACRU,SAAS,EACTiB,MAAO+C,EACP9C,QAAS+C,IAGXnE,QAAQmB,MAAM,uBAADL,OAAmBoD,GAClC,CAAC,QACC7E,GAAW,EACb,GA0IUgF,SAAUjF,EACVM,UAAU,wGAAuGC,SAEhHP,EAAU,oBAAiB,0CAI/BG,IACCK,EAAAA,EAAAA,MAAA,OAAKF,UAAS,kBAAAoB,OAAoBvB,EAAOW,QAAU,iCAAmC,8BAA+BP,SAAA,EACnHF,EAAAA,EAAAA,KAAA,MAAIC,UAAS,kBAAAoB,OAAoBvB,EAAOW,QAAU,qCAAuC,kCAAmCP,SACzHJ,EAAOW,QAAU,iBAAc,mBAGlCT,EAAAA,EAAAA,KAAA,KAAGC,UAAS,QAAAoB,OAAUvB,EAAOW,QAAU,qCAAuC,kCAAmCP,SAC9GJ,EAAOW,QAAUX,EAAOY,QAAUZ,EAAO4B,SAG5CvB,EAAAA,EAAAA,MAAA,WAASF,UAAU,OAAMC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,WAASC,UAAU,6BAA4BC,SAAC,0BAGhDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,SACjFkC,KAAKyC,UAAU/E,EAAOW,QAAUX,EAAOwB,KAAOxB,EAAO6B,QAAS,KAAM,YAM7ExB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kDAAiDC,SAAC,+BAGhEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,8EAA6EC,SAAA,EACzFF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2DACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,6DACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iDACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,sDAIRC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sDAAqDC,SAAA,EAClEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAAC,6BAGpEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yDAAwDC,SAAA,EACrEC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,aAAiB,IAAE4B,mCAC9B3B,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,mBAAuB,IAAEG,aAAaa,QAAQ,iBAAmB,MAAQ,SACpFf,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBAAqB,IAAE4B,iBAClC3B,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EAAGF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,cAAkB,IAAE4B,wB", "sources": ["pages/Debug/PatientCreationTest.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { apiHelpers as api } from '../../services/api';\n\nconst PatientCreationTest = () => {\n  const [loading, setLoading] = useState(false);\n  const [result, setResult] = useState(null);\n\n  const testPatientCreation = async () => {\n    setLoading(true);\n    setResult(null);\n\n    try {\n      console.log('🧪 Testing Patient Creation...');\n      \n      // Check authentication\n      const token = localStorage.getItem('pt_auth_token');\n      console.log('🔑 Token available:', !!token);\n      console.log('🔑 Token value:', token ? token.substring(0, 50) + '...' : 'None');\n\n      // Test data\n      const testData = {\n        firstName: 'Test',\n        lastName: 'Patient',\n        nationalId: Math.random().toString().substring(2, 12), // Random 10-digit ID\n        dateOfBirth: '1990-01-01',\n        gender: 'male',\n        phone: '+966501234567',\n        email: '<EMAIL>',\n        \n        address: {\n          street: '123 Test Street',\n          city: 'Riyadh',\n          state: 'Riyadh Province',\n          zipCode: '12345',\n          country: 'Saudi Arabia'\n        },\n        \n        emergencyContact: {\n          name: 'Emergency Contact',\n          phone: '+966507654321',\n          relationship: 'friend'\n        },\n        \n        insurance: {\n          provider: 'Test Insurance',\n          policyNumber: 'TEST123456',\n          groupNumber: 'GRP001'\n        }\n      };\n\n      console.log('📤 Sending test data:', testData);\n\n      // Make API call\n      const response = await api.post('/patients', testData);\n      \n      console.log('📥 Response received:', response);\n      \n      setResult({\n        success: true,\n        data: response,\n        message: 'Patient created successfully!'\n      });\n\n      console.log('✅ Test patient created successfully!');\n\n    } catch (error) {\n      console.error('❌ Test failed:', error);\n      \n      let errorMessage = 'Unknown error';\n      let errorDetails = {};\n\n      if (error.response) {\n        // Server responded with error\n        errorMessage = error.response.data?.message || `HTTP ${error.response.status}`;\n        errorDetails = {\n          status: error.response.status,\n          data: error.response.data,\n          headers: error.response.headers\n        };\n      } else if (error.request) {\n        // Network error\n        errorMessage = 'Network error - no response received';\n        errorDetails = {\n          request: error.request,\n          message: error.message\n        };\n      } else {\n        // Other error\n        errorMessage = error.message;\n        errorDetails = { error: error.toString() };\n      }\n\n      setResult({\n        success: false,\n        error: errorMessage,\n        details: errorDetails\n      });\n\n      console.error(`❌ Test failed: ${errorMessage}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testAuthentication = async () => {\n    try {\n      console.log('🔐 Testing authentication...');\n      console.log('🔗 API Base URL:', process.env.REACT_APP_API_URL);\n\n      const token = localStorage.getItem('pt_auth_token');\n      console.log('🔑 Token present:', !!token);\n      console.log('🔑 Token length:', token ? token.length : 0);\n      console.log('🔑 Token preview:', token ? token.substring(0, 50) + '...' : 'None');\n\n      // Check if token is valid JWT format\n      if (token) {\n        try {\n          const parts = token.split('.');\n          console.log('🔍 JWT parts count:', parts.length);\n          if (parts.length === 3) {\n            const payload = JSON.parse(atob(parts[1]));\n            console.log('🔍 JWT payload:', payload);\n            console.log('🔍 JWT expires:', new Date(payload.exp * 1000));\n            console.log('🔍 JWT expired:', Date.now() > payload.exp * 1000);\n          }\n        } catch (jwtError) {\n          console.error('🔍 JWT parsing error:', jwtError);\n        }\n      }\n\n      const response = await api.get('/auth/me');\n      console.log('👤 User info:', response);\n      console.log('✅ Authentication test successful!');\n\n      setResult({\n        success: true,\n        data: response,\n        message: 'Authentication successful!'\n      });\n    } catch (error) {\n      console.error('❌ Auth test failed:', error);\n      console.error('❌ Authentication test failed!');\n\n      setResult({\n        success: false,\n        error: error.message,\n        details: {\n          response: error.response?.data,\n          status: error.response?.status,\n          request: error.request ? 'Request made but no response' : 'No request made'\n        }\n      });\n    }\n  };\n\n  const testNetworkConnectivity = async () => {\n    try {\n      console.log('🌐 Testing network connectivity...');\n\n      // Test 1: Basic fetch to backend\n      const response1 = await fetch('http://localhost:5001/api/v1/health');\n      console.log('🏥 Health check response:', response1.status);\n\n      // Test 2: CORS preflight\n      const response2 = await fetch('http://localhost:5001/api/v1/auth/me', {\n        method: 'OPTIONS'\n      });\n      console.log('✈️ CORS preflight response:', response2.status);\n\n      // Test 3: Authenticated request\n      const token = localStorage.getItem('pt_auth_token');\n      const response3 = await fetch('http://localhost:5001/api/v1/auth/me', {\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      console.log('🔐 Authenticated request response:', response3.status);\n\n      console.log('✅ Network connectivity test completed!');\n\n      setResult({\n        success: true,\n        data: {\n          healthCheck: response1.status,\n          corsPreflght: response2.status,\n          authenticatedRequest: response3.status\n        },\n        message: 'Network connectivity test completed!'\n      });\n\n    } catch (error) {\n      console.error('❌ Network test failed:', error);\n      console.error('❌ Network connectivity test failed!');\n\n      setResult({\n        success: false,\n        error: error.message,\n        details: { networkError: error.toString() }\n      });\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 p-6\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">\n            🧪 Patient Creation Test\n          </h1>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n            <button\n              onClick={() => {\n                localStorage.clear();\n                console.log('🧹 localStorage cleared');\n                setResult({ success: true, message: 'localStorage cleared! Please login again.' });\n              }}\n              className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n            >\n              🧹 Clear Storage\n            </button>\n\n            <button\n              onClick={testNetworkConnectivity}\n              className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n            >\n              🌐 Test Network\n            </button>\n\n            <button\n              onClick={testAuthentication}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              🔐 Test Authentication\n            </button>\n\n            <button\n              onClick={testPatientCreation}\n              disabled={loading}\n              className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\"\n            >\n              {loading ? '⏳ Testing...' : '🧪 Test Patient Creation'}\n            </button>\n          </div>\n\n          {result && (\n            <div className={`p-4 rounded-lg ${result.success ? 'bg-green-100 dark:bg-green-900' : 'bg-red-100 dark:bg-red-900'}`}>\n              <h3 className={`font-bold mb-2 ${result.success ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'}`}>\n                {result.success ? '✅ Success' : '❌ Failed'}\n              </h3>\n              \n              <p className={`mb-4 ${result.success ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}`}>\n                {result.success ? result.message : result.error}\n              </p>\n\n              <details className=\"mt-4\">\n                <summary className=\"cursor-pointer font-medium\">\n                  📋 Details\n                </summary>\n                <pre className=\"mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs overflow-auto\">\n                  {JSON.stringify(result.success ? result.data : result.details, null, 2)}\n                </pre>\n              </details>\n            </div>\n          )}\n\n          <div className=\"mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg\">\n            <h3 className=\"font-bold text-blue-800 dark:text-blue-200 mb-2\">\n              📝 Instructions\n            </h3>\n            <ol className=\"list-decimal list-inside space-y-1 text-blue-700 dark:text-blue-300 text-sm\">\n              <li>First, test authentication to ensure you're logged in</li>\n              <li>Then test patient creation to see if the API call works</li>\n              <li>Check the browser console for detailed logs</li>\n              <li>If it fails, check the error details below</li>\n            </ol>\n          </div>\n\n          <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg\">\n            <h3 className=\"font-bold text-yellow-800 dark:text-yellow-200 mb-2\">\n              🔍 Debug Info\n            </h3>\n            <div className=\"text-yellow-700 dark:text-yellow-300 text-sm space-y-1\">\n              <p><strong>API URL:</strong> {process.env.REACT_APP_API_URL}</p>\n              <p><strong>Token Present:</strong> {localStorage.getItem('pt_auth_token') ? 'Yes' : 'No'}</p>\n              <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>\n              <p><strong>Mock API:</strong> {process.env.REACT_APP_MOCK_API}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientCreationTest;\n"], "names": ["PatientCreationTest", "loading", "setLoading", "useState", "result", "setResult", "_jsx", "className", "children", "_jsxs", "onClick", "localStorage", "clear", "console", "log", "success", "message", "async", "response1", "fetch", "status", "response2", "method", "token", "getItem", "response3", "headers", "concat", "data", "healthCheck", "corsPreflght", "authenticatedRequest", "error", "details", "networkError", "toString", "process", "length", "substring", "parts", "split", "payload", "JSON", "parse", "atob", "Date", "exp", "now", "jwtError", "response", "api", "get", "_error$response", "_error$response2", "request", "testData", "firstName", "lastName", "nationalId", "Math", "random", "dateOfBirth", "gender", "phone", "email", "address", "street", "city", "state", "zipCode", "country", "emergencyContact", "name", "relationship", "insurance", "provider", "policyNumber", "groupNumber", "post", "errorMessage", "errorDetails", "_error$response$data", "disabled", "stringify"], "sourceRoot": ""}