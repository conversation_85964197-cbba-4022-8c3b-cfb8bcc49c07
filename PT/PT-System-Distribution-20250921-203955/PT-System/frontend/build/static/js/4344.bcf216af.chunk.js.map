{"version": 3, "file": "static/js/4344.bcf216af.chunk.js", "mappings": "mMAIA,MA2pBA,EA3pBwBA,KACtB,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,OACX,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,aACpCC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,OACpCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAGvCK,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAgB,CACpBf,GAAIA,EACJgB,YAAa,uEACbC,cAAe,qBACfC,UAAW,cACXC,IAAK,EACLC,UAAW,iBACXC,YAAa,4EACbC,UAAW,aACXC,OAAQ,SACRC,UAAW,4DACXC,YAAa,kBACbC,SAAU,CACRC,UAAW,GACXC,MAAO,GACPC,UAAW,GAEbC,YAAa,sBACbC,MAAO,CACL,CACE/B,GAAI,EACJgC,MAAO,4EACPC,QAAS,kBACTC,SAAU,GACVC,OAAQ,aACRZ,OAAQ,YAEV,CACEvB,GAAI,EACJgC,MAAO,4EACPC,QAAS,qBACTC,SAAU,GACVC,OAAQ,aACRZ,OAAQ,YAEV,CACEvB,GAAI,EACJgC,MAAO,gEACPC,QAAS,kBACTC,SAAU,GACVC,OAAQ,aACRZ,OAAQ,oBAGZa,eAAgB,CACd,CACEpC,GAAI,EACJqC,KAAM,aACNC,SAAU,GACVC,WAAY,CAAC,oBAAqB,oBAAqB,iBACvDC,aAAc,CAAC,kFAAkB,gEAAe,iEAChDC,MAAO,oDACPC,QAAS,iOACTlB,UAAW,kBACXmB,OAAQ,GAEV,CACE3C,GAAI,EACJqC,KAAM,aACNC,SAAU,GACVC,WAAY,CAAC,kBAAmB,0BAChCC,aAAc,CAAC,2GAAuB,mFACtCC,MAAO,oDACPC,QAAS,2LACTlB,UAAW,kBACXmB,OAAQ,IAGZC,UAAW,CACT,CAAEC,KAAM,gBAAiBC,OAAQ,0DAAcvB,OAAQ,YACvD,CAAEsB,KAAM,gBAAiBC,OAAQ,gEAAevB,OAAQ,YACxD,CAAEsB,KAAM,eAAgBC,OAAQ,0DAAcvB,OAAQ,cAExDwB,YAAa,CACX,CAAEF,KAAM,WAAYG,OAAQ,OAAQC,UAAW,cAAeR,MAAO,yBACrE,CAAEI,KAAM,YAAaG,OAAQ,UAAWC,UAAW,QAASR,MAAO,wBAErES,YAAa,CACX,CACEb,KAAM,aACNc,KAAM,qBACNC,OAAQ,kFACRC,MAAO,GACPZ,MAAO,iCAET,CACEJ,KAAM,aACNc,KAAM,kBACNC,OAAQ,4EACRC,MAAO,GACPZ,MAAO,yCAKba,WAAW,KACT3C,EAAaI,GACbF,GAAW,IACV,MACF,CAACb,IAEJ,MAAMuD,EAAO,CACX,CAAEvD,GAAI,WAAYwD,MAAOpD,EAAE,WAAY,YAAaqD,KAAM,qBAC1D,CAAEzD,GAAI,WAAYwD,MAAOpD,EAAE,WAAY,YAAaqD,KAAM,uBAC1D,CAAEzD,GAAI,QAASwD,MAAOpD,EAAE,QAAS,SAAUqD,KAAM,mBACjD,CAAEzD,GAAI,cAAewD,MAAOpD,EAAE,cAAe,eAAgBqD,KAAM,0BACnE,CAAEzD,GAAI,YAAawD,MAAOpD,EAAE,YAAa,aAAcqD,KAAM,gBAC7D,CAAEzD,GAAI,QAASwD,MAAOpD,EAAE,QAAS,SAAUqD,KAAM,uBAGnD,GAAI7C,EACF,OACE8C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qEAKrB,IAAKjD,EACH,OACEmD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2CAA0CC,SACrDxD,EAAE,oBAAqB,0BAE1BsD,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAM5D,EAAS,eACxByD,UAAU,kFAAiFC,SAE1FxD,EAAE,mBAAoB,2BA0c/B,OACEyD,EAAAA,EAAAA,MAAA,OAAKF,UAAS,4CAAAI,OAA8C1D,EAAQ,cAAgB,gBAAiBuD,SAAA,EAEnGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oFAAmFC,UAChGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAM5D,EAAS,eACxByD,UAAU,wIAAuIC,UAEjJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kDAAiDC,SAAA,CAC5DxD,EAAE,gBAAiB,kBAAkB,KAAGM,EAAUV,OAErD6D,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpDvD,EAAQK,EAAUM,YAAcN,EAAUO,cAAc,WAAIZ,EAAQK,EAAUW,YAAcX,EAAUU,oBAK7GyC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kFAAiFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACZvD,EAAE,kBAAmB,wBAExByD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,oFAAmFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZvD,EAAE,eAAgB,6BAO3BsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEC,UACtFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,SAC5BL,EAAKS,IAAIC,IACRJ,EAAAA,EAAAA,MAAA,UAEEC,QAASA,IAAMtD,EAAayD,EAAIjE,IAChC2D,UAAS,8DAAAI,OACPxD,IAAc0D,EAAIjE,GACd,mDACA,0HACH4D,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAI,OAAKE,EAAIR,KAAI,WACxBQ,EAAIT,QATAS,EAAIjE,YAiBnB6D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,CACH,aAAdrD,IA9fLsD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExD,EAAE,qBAAsB,0BAE3BsD,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAAI,OACQ,WAArBrD,EAAUa,OACN,uEACA,oEACHqC,SACqB,WAArBlD,EAAUa,OAAsBnB,EAAE,SAAU,UAAYA,EAAE,WAAY,kBAI3EyD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExD,EAAE,cAAe,mBAEpBsD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SACrDvD,EAAQK,EAAUM,YAAcN,EAAUO,oBAG/C4C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExD,EAAE,YAAa,iBAElBsD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SAAElD,EAAUQ,gBAEtE2C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExD,EAAE,MAAO,UAEZyD,EAAAA,EAAAA,MAAA,KAAGF,UAAU,4CAA2CC,SAAA,CAAElD,EAAUS,IAAI,IAAEf,EAAE,QAAS,gBAEvFyD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExD,EAAE,YAAa,gBAElBsD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SACrDvD,EAAQK,EAAUW,YAAcX,EAAUU,sBAOnDyC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,CAChElD,EAAUgB,SAASC,UAAU,IAAEjB,EAAUgB,SAASE,UAErD8B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAExD,EAAE,oBAAqB,+BAG5EsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAU,2DACVO,MAAO,CAAEC,MAAM,GAADJ,OAAMrD,EAAUgB,SAASC,UAAYjB,EAAUgB,SAASE,MAAS,IAAG,gBAM1F8B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDC,UAC/DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,CAChElD,EAAUqB,MAAMqC,OAAOC,GAAkB,aAAbA,EAAE9C,QAAuB+C,OAAO,IAAE5D,EAAUqB,MAAMuC,WAEjFZ,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAExD,EAAE,eAAgB,6BAKzEsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE,IAAIW,KAAK7D,EAAUoB,aAAa0C,wBAEnCd,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAExD,EAAE,cAAe,8BAO1EyD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrExD,EAAE,iBAAkB,sBAEvBsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBlD,EAAU0B,eAAeqC,MAAM,EAAG,GAAGT,IAAIU,IACxCb,EAAAA,EAAAA,MAAA,OAAsBF,UAAU,wEAAuEC,SAAA,EACrGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4CAA2CC,SAAA,CACtDxD,EAAE,UAAW,WAAW,MAAI,IAAImE,KAAKG,EAAQrC,MAAMmC,yBAEtDd,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,SAC/B,IAAIe,MAAM,IAAIX,IAAI,CAACY,EAAGC,KACrBnB,EAAAA,EAAAA,KAAA,KAEEC,UAAS,uBAAAI,OACPc,EAAIH,EAAQ/B,OAAS,kBAAoB,qCAFtCkC,UAQbnB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDvD,EAAQqE,EAAQhC,QAAUgC,EAAQjC,SAErCiB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BC,UACtCvD,EAAQqE,EAAQlC,aAAekC,EAAQnC,YAAYyB,IAAI,CAACc,EAAUC,KAClErB,EAAAA,EAAAA,KAAA,QAEEC,UAAU,kGAAiGC,SAE1GkB,GAHIC,WA1BLL,EAAQ1E,aAmZP,aAAdO,IAzWLmD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExD,EAAE,iBAAkB,sBAEvByD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kFAAiFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvD,EAAE,aAAc,sBAIrBsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBlD,EAAU0B,eAAe4B,IAAIU,IAC5Bb,EAAAA,EAAAA,MAAA,OAAsBF,UAAU,6DAA4DC,SAAA,EAC1FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wDAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SACtD,IAAIW,KAAKG,EAAQrC,MAAMmC,wBAE1BX,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpDc,EAAQpC,SAAS,IAAElC,EAAE,UAAW,WAAW,WAAIsE,EAAQlD,oBAI9DqC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oBAAmBC,SAC/B,IAAIe,MAAM,IAAIX,IAAI,CAACY,EAAGC,KACrBnB,EAAAA,EAAAA,KAAA,KAEEC,UAAS,uBAAAI,OACPc,EAAIH,EAAQ/B,OAAS,kBAAoB,qCAFtCkC,OAOXnB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,iEAAgEC,UAChFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBAKnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4DAA2DC,SAAA,CACtExD,EAAE,aAAc,cAAc,QAEjCsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,UACjCvD,EAAQqE,EAAQlC,aAAekC,EAAQnC,YAAYyB,IAAI,CAACc,EAAUC,KAClErB,EAAAA,EAAAA,KAAA,QAEEC,UAAU,sGAAqGC,SAE9GkB,GAHIC,UASblB,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4DAA2DC,SAAA,CACtExD,EAAE,QAAS,SAAS,QAEvBsD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDvD,EAAQqE,EAAQhC,QAAUgC,EAAQjC,aArD/BiC,EAAQ1E,YA4VP,UAAdO,IA5RLmD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExD,EAAE,iBAAkB,sBAEvByD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,oFAAmFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvD,EAAE,UAAW,mBAIlBsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBlD,EAAUqB,MAAMiC,IAAIgB,IACnBnB,EAAAA,EAAAA,MAAA,OAAmBF,UAAU,6DAA4DC,SAAA,EACvFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SACtDvD,EAAQ2E,EAAKhD,MAAQgD,EAAK/C,WAE7ByB,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAAI,OACG,aAAhBiB,EAAKzD,OACD,uEACA,4EACHqC,SACgB,aAAhBoB,EAAKzD,OAAwBnB,EAAE,UAAW,YAAcA,EAAE,iBAAkB,yBAIjFyD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDxD,EAAE,WAAY,eAEjByD,EAAAA,EAAAA,MAAA,QAAMF,UAAU,oDAAmDC,SAAA,CAChEoB,EAAK9C,SAAS,WAGnBwB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,gDAAAI,OACS,aAAhBiB,EAAKzD,OAAwB,eAAiB,iBAEhD2C,MAAO,CAAEC,MAAM,GAADJ,OAAKiB,EAAK9C,SAAQ,cAKtC2B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4CAA2CC,SAAA,EACxDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAC/CxD,EAAE,aAAc,eAAe,KAAG,IAAImE,KAAKS,EAAK7C,QAAQqC,yBAE3DX,EAAAA,EAAAA,MAAA,UAAQF,UAAU,gFAA+EC,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvD,EAAE,OAAQ,gBAvCP4E,EAAKhF,YA+QJ,gBAAdO,IA7NLmD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExD,EAAE,oBAAqB,yBAE1ByD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,sFAAqFC,SAAA,EACrGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvD,EAAE,gBAAiB,yBAIxBsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBlD,EAAUwC,YAAYc,IAAI,CAACiB,EAAYF,KACtClB,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,6DAA4DC,SAAA,EACrFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SACtDvD,EAAQ4E,EAAW7B,OAAS6B,EAAW9B,QAE1CO,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpD,IAAIW,KAAKU,EAAW5C,MAAMmC,2BAG/BX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,SACjEqB,EAAW5B,SAEdK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDxD,EAAE,QAAS,kBAIlBsD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDqB,EAAWxC,UApBNsC,WAgNC,cAAdxE,IAlLLmD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExD,EAAE,oBAAqB,yBAE1ByD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,sFAAqFC,SAAA,EACrGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvD,EAAE,mBAAoB,4BAI3BsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClElD,EAAUkC,UAAUoB,IAAI,CAACkB,EAAMH,KAC9BlB,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,6DAA4DC,SAAA,EACrFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SACtDvD,EAAQ6E,EAAKpC,OAASoC,EAAKrC,QAE9Ba,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAAI,OACG,aAAhBmB,EAAK3D,OACD,uEACA,4EACHqC,SACgB,aAAhBsB,EAAK3D,OAAwBnB,EAAE,WAAY,YAAcA,EAAE,YAAa,mBAG7EyD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,UAAQF,UAAU,wFAAuFC,SAAA,EACvGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBACZvD,EAAE,cAAe,oBAEpByD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,oFAAmFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZvD,EAAE,SAAU,kBApBT2E,OA4BdlB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrExD,EAAE,cAAe,kBAEpBsD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBlD,EAAUqC,YAAYiB,IAAI,CAACmB,EAAKJ,KAC/BlB,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,+EAA8EC,SAAA,EACvGC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SAAEuB,EAAItC,QAC/DgB,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpDuB,EAAInC,OAAO,WAAImC,EAAIlC,aAErBkC,EAAI1C,QACHiB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAEuB,EAAI1C,YAGtEiB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,gFAA+EC,UAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAXPoB,cAmID,UAAdxE,IA7GLmD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExD,EAAE,iBAAkB,sBAEvByD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kFAAiFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvD,EAAE,UAAW,mBAIlByD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qDACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SAAC,wBAE9DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,mBAE7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAC,0NAMlDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qDACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SAAC,wBAE9DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,mBAE7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAC,2M", "sources": ["pages/Treatments/TreatmentDetail.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst TreatmentDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [treatment, setTreatment] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Mock treatment data\n  useEffect(() => {\n    const mockTreatment = {\n      id: id,\n      patientName: 'أحمد محمد علي',\n      patientNameEn: '<PERSON>',\n      patientId: 'PT-2024-001',\n      age: 8,\n      condition: 'Cerebral Palsy',\n      conditionAr: 'الشلل الدماغي',\n      startDate: '2024-01-15',\n      status: 'active',\n      therapist: 'د. سارة أحمد',\n      therapistEn: '<PERSON>. <PERSON>',\n      sessions: {\n        completed: 12,\n        total: 20,\n        remaining: 8\n      },\n      nextSession: '2024-02-15T10:00:00',\n      goals: [\n        {\n          id: 1,\n          title: 'تحسين التوازن',\n          titleEn: 'Improve Balance',\n          progress: 75,\n          target: '2024-03-01',\n          status: 'on-track'\n        },\n        {\n          id: 2,\n          title: 'تقوية العضلات',\n          titleEn: 'Strengthen Muscles',\n          progress: 60,\n          target: '2024-03-15',\n          status: 'on-track'\n        },\n        {\n          id: 3,\n          title: 'تحسين المشي',\n          titleEn: 'Improve Walking',\n          progress: 45,\n          target: '2024-04-01',\n          status: 'needs-attention'\n        }\n      ],\n      recentSessions: [\n        {\n          id: 1,\n          date: '2024-02-10',\n          duration: 45,\n          activities: ['Balance exercises', 'Strength training', 'Gait training'],\n          activitiesAr: ['تمارين التوازن', 'تدريب القوة', 'تدريب المشي'],\n          notes: 'Patient showed good progress in balance exercises',\n          notesAr: 'أظهر المريض تقدماً جيداً في تمارين التوازن',\n          therapist: 'Dr. Sarah Ahmed',\n          rating: 4\n        },\n        {\n          id: 2,\n          date: '2024-02-08',\n          duration: 45,\n          activities: ['Range of motion', 'Coordination exercises'],\n          activitiesAr: ['تمارين المدى الحركي', 'تمارين التنسيق'],\n          notes: 'Focused on improving coordination and flexibility',\n          notesAr: 'التركيز على تحسين التنسيق والمرونة',\n          therapist: 'Dr. Sarah Ahmed',\n          rating: 5\n        }\n      ],\n      equipment: [\n        { name: 'Walking Frame', nameAr: 'إطار المشي', status: 'assigned' },\n        { name: 'Balance Board', nameAr: 'لوح التوازن', status: 'assigned' },\n        { name: 'Therapy Ball', nameAr: 'كرة العلاج', status: 'requested' }\n      ],\n      medications: [\n        { name: 'Baclofen', dosage: '10mg', frequency: 'Twice daily', notes: 'For muscle spasticity' },\n        { name: 'Vitamin D', dosage: '1000 IU', frequency: 'Daily', notes: 'Bone health support' }\n      ],\n      assessments: [\n        {\n          date: '2024-01-15',\n          type: 'Initial Assessment',\n          typeAr: 'التقييم الأولي',\n          score: 65,\n          notes: 'Baseline assessment completed'\n        },\n        {\n          date: '2024-02-01',\n          type: 'Progress Review',\n          typeAr: 'مراجعة التقدم',\n          score: 72,\n          notes: 'Showing improvement in motor skills'\n        }\n      ]\n    };\n\n    setTimeout(() => {\n      setTreatment(mockTreatment);\n      setLoading(false);\n    }, 1000);\n  }, [id]);\n\n  const tabs = [\n    { id: 'overview', label: t('overview', 'Overview'), icon: 'fas fa-chart-line' },\n    { id: 'sessions', label: t('sessions', 'Sessions'), icon: 'fas fa-calendar-alt' },\n    { id: 'goals', label: t('goals', 'Goals'), icon: 'fas fa-bullseye' },\n    { id: 'assessments', label: t('assessments', 'Assessments'), icon: 'fas fa-clipboard-check' },\n    { id: 'equipment', label: t('equipment', 'Equipment'), icon: 'fas fa-tools' },\n    { id: 'notes', label: t('notes', 'Notes'), icon: 'fas fa-sticky-note' }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!treatment) {\n    return (\n      <div className=\"text-center py-12\">\n        <i className=\"fas fa-exclamation-triangle text-6xl text-red-300 mb-4\"></i>\n        <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n          {t('treatmentNotFound', 'Treatment Not Found')}\n        </h3>\n        <button\n          onClick={() => navigate('/treatments')}\n          className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          {t('backToTreatments', 'Back to Treatments')}\n        </button>\n      </div>\n    );\n  }\n\n  const renderOverview = () => (\n    <div className=\"space-y-6\">\n      {/* Patient Info Card */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('patientInformation', 'Patient Information')}\n          </h3>\n          <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n            treatment.status === 'active'\n              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'\n              : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'\n          }`}>\n            {treatment.status === 'active' ? t('active', 'Active') : t('inactive', 'Inactive')}\n          </span>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1\">\n              {t('patientName', 'Patient Name')}\n            </label>\n            <p className=\"text-gray-900 dark:text-white font-medium\">\n              {isRTL ? treatment.patientName : treatment.patientNameEn}\n            </p>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1\">\n              {t('patientId', 'Patient ID')}\n            </label>\n            <p className=\"text-gray-900 dark:text-white font-medium\">{treatment.patientId}</p>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1\">\n              {t('age', 'Age')}\n            </label>\n            <p className=\"text-gray-900 dark:text-white font-medium\">{treatment.age} {t('years', 'years')}</p>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1\">\n              {t('condition', 'Condition')}\n            </label>\n            <p className=\"text-gray-900 dark:text-white font-medium\">\n              {isRTL ? treatment.conditionAr : treatment.condition}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Progress Summary */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {treatment.sessions.completed}/{treatment.sessions.total}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('sessionsCompleted', 'Sessions Completed')}</p>\n            </div>\n          </div>\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n              <div\n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${(treatment.sessions.completed / treatment.sessions.total) * 100}%` }}\n              />\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-bullseye text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {treatment.goals.filter(g => g.status === 'on-track').length}/{treatment.goals.length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('goalsOnTrack', 'Goals On Track')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {new Date(treatment.nextSession).toLocaleDateString()}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('nextSession', 'Next Session')}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('recentActivity', 'Recent Activity')}\n        </h3>\n        <div className=\"space-y-4\">\n          {treatment.recentSessions.slice(0, 3).map(session => (\n            <div key={session.id} className=\"flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n              <div className=\"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n                <i className=\"fas fa-notes-medical text-blue-600 dark:text-blue-400\"></i>\n              </div>\n              <div className=\"flex-1\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                    {t('session', 'Session')} - {new Date(session.date).toLocaleDateString()}\n                  </h4>\n                  <div className=\"flex items-center\">\n                    {[...Array(5)].map((_, i) => (\n                      <i\n                        key={i}\n                        className={`fas fa-star text-sm ${\n                          i < session.rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'\n                        }`}\n                      />\n                    ))}\n                  </div>\n                </div>\n                <p className=\"text-gray-600 dark:text-gray-400 text-sm mt-1\">\n                  {isRTL ? session.notesAr : session.notes}\n                </p>\n                <div className=\"flex flex-wrap gap-2 mt-2\">\n                  {(isRTL ? session.activitiesAr : session.activities).map((activity, index) => (\n                    <span\n                      key={index}\n                      className=\"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full\"\n                    >\n                      {activity}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderSessions = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('sessionHistory', 'Session History')}\n          </h3>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('newSession', 'New Session')}\n          </button>\n        </div>\n\n        <div className=\"space-y-4\">\n          {treatment.recentSessions.map(session => (\n            <div key={session.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n                    <i className=\"fas fa-calendar text-blue-600 dark:text-blue-400\"></i>\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                      {new Date(session.date).toLocaleDateString()}\n                    </h4>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {session.duration} {t('minutes', 'minutes')} • {session.therapist}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"flex items-center\">\n                    {[...Array(5)].map((_, i) => (\n                      <i\n                        key={i}\n                        className={`fas fa-star text-sm ${\n                          i < session.rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'\n                        }`}\n                      />\n                    ))}\n                  </div>\n                  <button className=\"p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400\">\n                    <i className=\"fas fa-edit\"></i>\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"mb-3\">\n                <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('activities', 'Activities')}:\n                </h5>\n                <div className=\"flex flex-wrap gap-2\">\n                  {(isRTL ? session.activitiesAr : session.activities).map((activity, index) => (\n                    <span\n                      key={index}\n                      className=\"px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm rounded-full\"\n                    >\n                      {activity}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              <div>\n                <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('notes', 'Notes')}:\n                </h5>\n                <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\n                  {isRTL ? session.notesAr : session.notes}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderGoals = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('treatmentGoals', 'Treatment Goals')}\n          </h3>\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('addGoal', 'Add Goal')}\n          </button>\n        </div>\n\n        <div className=\"space-y-4\">\n          {treatment.goals.map(goal => (\n            <div key={goal.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                  {isRTL ? goal.title : goal.titleEn}\n                </h4>\n                <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                  goal.status === 'on-track'\n                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'\n                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n                }`}>\n                  {goal.status === 'on-track' ? t('onTrack', 'On Track') : t('needsAttention', 'Needs Attention')}\n                </span>\n              </div>\n\n              <div className=\"mb-3\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('progress', 'Progress')}\n                  </span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {goal.progress}%\n                  </span>\n                </div>\n                <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className={`h-2 rounded-full transition-all duration-300 ${\n                      goal.status === 'on-track' ? 'bg-green-600' : 'bg-yellow-600'\n                    }`}\n                    style={{ width: `${goal.progress}%` }}\n                  />\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-gray-500 dark:text-gray-400\">\n                  {t('targetDate', 'Target Date')}: {new Date(goal.target).toLocaleDateString()}\n                </span>\n                <button className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200\">\n                  <i className=\"fas fa-edit mr-1\"></i>\n                  {t('edit', 'Edit')}\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderAssessments = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('assessmentHistory', 'Assessment History')}\n          </h3>\n          <button className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('newAssessment', 'New Assessment')}\n          </button>\n        </div>\n\n        <div className=\"space-y-4\">\n          {treatment.assessments.map((assessment, index) => (\n            <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                    {isRTL ? assessment.typeAr : assessment.type}\n                  </h4>\n                  <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {new Date(assessment.date).toLocaleDateString()}\n                  </p>\n                </div>\n                <div className=\"text-right\">\n                  <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                    {assessment.score}\n                  </div>\n                  <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {t('score', 'Score')}\n                  </div>\n                </div>\n              </div>\n              <p className=\"text-gray-600 dark:text-gray-400 text-sm\">\n                {assessment.notes}\n              </p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderEquipment = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('assignedEquipment', 'Assigned Equipment')}\n          </h3>\n          <button className=\"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('requestEquipment', 'Request Equipment')}\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {treatment.equipment.map((item, index) => (\n            <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                  {isRTL ? item.nameAr : item.name}\n                </h4>\n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                  item.status === 'assigned'\n                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'\n                    : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n                }`}>\n                  {item.status === 'assigned' ? t('assigned', 'Assigned') : t('requested', 'Requested')}\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <button className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm\">\n                  <i className=\"fas fa-eye mr-1\"></i>\n                  {t('viewDetails', 'View Details')}\n                </button>\n                <button className=\"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm\">\n                  <i className=\"fas fa-times mr-1\"></i>\n                  {t('remove', 'Remove')}\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Medications Section */}\n        <div className=\"mt-8\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('medications', 'Medications')}\n          </h3>\n          <div className=\"space-y-3\">\n            {treatment.medications.map((med, index) => (\n              <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">{med.name}</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {med.dosage} • {med.frequency}\n                  </p>\n                  {med.notes && (\n                    <p className=\"text-xs text-gray-500 dark:text-gray-500 mt-1\">{med.notes}</p>\n                  )}\n                </div>\n                <button className=\"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200\">\n                  <i className=\"fas fa-edit\"></i>\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderNotes = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('treatmentNotes', 'Treatment Notes')}\n          </h3>\n          <button className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('addNote', 'Add Note')}\n          </button>\n        </div>\n\n        <div className=\"space-y-4\">\n          <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <div className=\"flex items-center space-x-2\">\n                <i className=\"fas fa-user-md text-blue-600 dark:text-blue-400\"></i>\n                <span className=\"font-medium text-gray-900 dark:text-white\">Dr. Sarah Ahmed</span>\n              </div>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">2024-02-10</span>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              Patient is responding well to the current treatment plan. Balance has improved significantly over the past two weeks.\n              Recommend continuing with current exercises and adding more challenging balance activities.\n            </p>\n          </div>\n\n          <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <div className=\"flex items-center space-x-2\">\n                <i className=\"fas fa-user-md text-blue-600 dark:text-blue-400\"></i>\n                <span className=\"font-medium text-gray-900 dark:text-white\">Dr. Sarah Ahmed</span>\n              </div>\n              <span className=\"text-sm text-gray-500 dark:text-gray-400\">2024-02-01</span>\n            </div>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              Initial progress review completed. Patient shows good motivation and compliance with home exercise program.\n              Family is very supportive and actively involved in the treatment process.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => navigate('/treatments')}\n              className=\"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700\"\n            >\n              <i className=\"fas fa-arrow-left\"></i>\n            </button>\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                {t('treatmentPlan', 'Treatment Plan')} #{treatment.id}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {isRTL ? treatment.patientName : treatment.patientNameEn} • {isRTL ? treatment.conditionAr : treatment.condition}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n              <i className=\"fas fa-calendar-plus mr-2\"></i>\n              {t('scheduleSession', 'Schedule Session')}\n            </button>\n            <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n              <i className=\"fas fa-download mr-2\"></i>\n              {t('exportReport', 'Export Report')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600\">\n        <div className=\"px-6\">\n          <nav className=\"flex space-x-8\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n                }`}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        {activeTab === 'overview' && renderOverview()}\n        {activeTab === 'sessions' && renderSessions()}\n        {activeTab === 'goals' && renderGoals()}\n        {activeTab === 'assessments' && renderAssessments()}\n        {activeTab === 'equipment' && renderEquipment()}\n        {activeTab === 'notes' && renderNotes()}\n      </div>\n    </div>\n  );\n};\n\nexport default TreatmentDetail;\n"], "names": ["TreatmentDetail", "id", "useParams", "navigate", "useNavigate", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "treatment", "setTreatment", "loading", "setLoading", "useEffect", "mockTreatment", "patientName", "patientNameEn", "patientId", "age", "condition", "conditionAr", "startDate", "status", "therapist", "therapistEn", "sessions", "completed", "total", "remaining", "nextSession", "goals", "title", "titleEn", "progress", "target", "recentSessions", "date", "duration", "activities", "activitiesAr", "notes", "notesAr", "rating", "equipment", "name", "nameAr", "medications", "dosage", "frequency", "assessments", "type", "typeAr", "score", "setTimeout", "tabs", "label", "icon", "_jsx", "className", "children", "_jsxs", "onClick", "concat", "map", "tab", "style", "width", "filter", "g", "length", "Date", "toLocaleDateString", "slice", "session", "Array", "_", "i", "activity", "index", "goal", "assessment", "item", "med"], "sourceRoot": ""}