"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[7016],{2816:(e,s,a)=>{a.d(s,{rR:()=>r});const r={FULL_COMPLIANCE:{score:100,label:"Full Compliance",color:"green",description:"Meets all CARF standards"},SUBSTANTIAL_COMPLIANCE:{score:85,label:"Substantial Compliance",color:"yellow",description:"Minor areas for improvement"},PARTIAL_COMPLIANCE:{score:70,label:"Partial Compliance",color:"orange",description:"Significant improvements needed"},NON_COMPLIANCE:{score:0,label:"Non-Compliance",color:"red",description:"Major deficiencies identified"}}},7016:(e,s,a)=>{a.r(s),a.d(s,{default:()=>n});var r=a(2555),t=a(5043),i=a(7921),l=(a(2816),a(579));const n=e=>{let{patientId:s,onSave:a,onCancel:n,initialData:o={}}=e;const{t:d,isRTL:c}=(0,i.o)(),[g,m]=(0,t.useState)(0),[x,h]=(0,t.useState)({patientId:s||"",assessmentDate:(new Date).toISOString().split("T")[0],assessor:"",assessorCredentials:"",primaryDiagnosis:o.primaryDiagnosis||"",secondaryDiagnoses:o.secondaryDiagnoses||[],medicalHistory:o.medicalHistory||"",currentMedications:o.currentMedications||"",allergies:o.allergies||"",precautions:o.precautions||"",vitalSigns:{bloodPressure:"",heartRate:"",temperature:"",oxygenSaturation:""},functionalAssessment:{selfCare:{eating:7,grooming:7,bathing:7,dressingUpper:7,dressingLower:7,toileting:7},sphincterControl:{bladder:7,bowel:7},transfers:{bedChairWheelchair:7,toilet:7,tubShower:7},locomotion:{walkWheelchair:7,stairs:7},communication:{comprehension:7,expression:7},socialCognition:{socialInteraction:7,problemSolving:7,memory:7}},psychosocialAssessment:{moodState:"stable",copingStrategies:"adaptive",motivationLevel:"high",insightLevel:"good",familySupport:"strong",socialConnections:"adequate",culturalConsiderations:"",spiritualNeeds:"",notes:"",mentalStatus:"",cognitiveFunction:"",emotionalStatus:"",copingMechanisms:"",socialSupport:"",culturalFactors:""},environmentalAssessment:{homeAccessibility:"accessible",homeSafety:"safe",homeModifications:"",transportationAccess:"adequate",communityResources:"available",notes:"",livingArrangement:"",accessibility:"",safetyHazards:"",transportation:"",financialResources:"",equipmentNeeds:""},riskAssessment:{fallRisk:"low",skinIntegrityRisk:"low",nutritionalRisk:"low",medicationRisk:"low",behavioralRisk:"low",cognitiveRisk:"low",riskMitigationPlan:""},goalsAndPreferences:{personServedGoals:"",familyGoals:"",clinicalGoals:"",preferences:"",barriers:"",strengths:"",dischargePlanning:""}}),u=[{id:"demographic",title:d("demographicInformation","Demographic Information"),icon:"fas fa-user"},{id:"medical",title:d("medicalHistory","Medical History"),icon:"fas fa-notes-medical"},{id:"functional",title:d("functionalAssessment","Functional Assessment (FIM)"),icon:"fas fa-tasks"},{id:"psychosocial",title:d("psychosocialAssessment","Psychosocial Assessment"),icon:"fas fa-brain"},{id:"environmental",title:d("environmentalAssessment","Environmental Assessment"),icon:"fas fa-home"},{id:"risk",title:d("riskAssessment","Risk Assessment"),icon:"fas fa-exclamation-triangle"},{id:"goals",title:d("goalsAndPreferences","Goals and Preferences"),icon:"fas fa-bullseye"}],b=(e,s,a)=>{h(t=>(0,r.A)((0,r.A)({},t),{},{[e]:(0,r.A)((0,r.A)({},t[e]),{},{[s]:a})}))},y=(e,s,a,t)=>{h(i=>(0,r.A)((0,r.A)({},i),{},{[e]:(0,r.A)((0,r.A)({},i[e]),{},{[s]:(0,r.A)((0,r.A)({},i[e][s]),{},{[a]:t})})}))},p=e=>{let{label:s,value:a,onChange:r,description:t}=e;return(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:s}),(0,l.jsxs)("select",{value:a,onChange:e=>r(parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:7,children:"7 - Complete Independence"}),(0,l.jsx)("option",{value:6,children:"6 - Modified Independence"}),(0,l.jsx)("option",{value:5,children:"5 - Supervision/Setup"}),(0,l.jsx)("option",{value:4,children:"4 - Minimal Contact Assistance"}),(0,l.jsx)("option",{value:3,children:"3 - Moderate Assistance"}),(0,l.jsx)("option",{value:2,children:"2 - Maximal Assistance"}),(0,l.jsx)("option",{value:1,children:"1 - Total Assistance"})]}),t&&(0,l.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:t})]})},v=e=>{let{label:s,value:a,onChange:r}=e;return(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:s}),(0,l.jsxs)("select",{value:a,onChange:e=>r(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"low",children:d("lowRisk","Low Risk")}),(0,l.jsx)("option",{value:"moderate",children:d("moderateRisk","Moderate Risk")}),(0,l.jsx)("option",{value:"high",children:d("highRisk","High Risk")})]})]})};return(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg ".concat(c?"font-arabic":"font-english"),children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:d("carfAssessment","CARF-Compliant Assessment")}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:d("carfAssessmentDescription","Comprehensive assessment following CARF standards")})]}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsx)("button",{onClick:n,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:d("cancel","Cancel")}),(0,l.jsx)("button",{onClick:()=>a(x),className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:d("saveAssessment","Save Assessment")})]})]})}),(0,l.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:[(0,l.jsx)("div",{className:"flex items-center justify-between",children:u.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full border-2 ".concat(g>=s?"border-blue-500 bg-blue-500 text-white":"border-gray-300 text-gray-400"),children:(0,l.jsx)("i",{className:e.icon})}),s<u.length-1&&(0,l.jsx)("div",{className:"w-full h-1 mx-2 ".concat(g>s?"bg-blue-500":"bg-gray-300")})]},e.id))}),(0,l.jsx)("div",{className:"mt-2",children:(0,l.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[d("step","Step")," ",g+1," ",d("of","of")," ",u.length,": ",u[g].title]})})]}),(0,l.jsx)("div",{className:"p-6",children:(()=>{var e,s,a,t,i,n,o,c,m,k,j,f,A,w,N,C,S,P,R,D,M,L,I,G,H,T;const E=u[g];switch(E.id){case"demographic":return(0,l.jsx)("div",{className:"space-y-6",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[d("assessmentDate","Assessment Date")," *"]}),(0,l.jsx)("input",{type:"date",value:x.assessmentDate,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{assessmentDate:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[d("assessor","Assessor")," *"]}),(0,l.jsx)("input",{type:"text",value:x.assessor,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{assessor:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("assessorName","Assessor Name"),required:!0})]}),(0,l.jsxs)("div",{className:"md:col-span-2",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("assessorCredentials","Assessor Credentials")}),(0,l.jsx)("input",{type:"text",value:x.assessorCredentials,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{assessorCredentials:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("credentialsPlaceholder","e.g., PT, DPT, OTR/L")})]})]})});case"medical":return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium text-red-900 dark:text-red-100 mb-2",children:d("medicalInstructions","Medical History Instructions")}),(0,l.jsx)("p",{className:"text-sm text-red-800 dark:text-red-200",children:d("medicalDescription","Document comprehensive medical history, current conditions, and medications.")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("diagnoses","Diagnoses")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[d("primaryDiagnosis","Primary Diagnosis")," *"]}),(0,l.jsx)("input",{type:"text",value:x.primaryDiagnosis,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{primaryDiagnosis:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("primaryDiagnosisPlaceholder","e.g., Stroke, TBI, SCI"),required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("secondaryDiagnoses","Secondary Diagnoses")}),(0,l.jsx)("textarea",{value:Array.isArray(x.secondaryDiagnoses)?x.secondaryDiagnoses.join(", "):x.secondaryDiagnoses,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{secondaryDiagnoses:e.target.value.split(",").map(e=>e.trim()).filter(e=>e)})),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("secondaryDiagnosesPlaceholder","List secondary diagnoses separated by commas...")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("medicalHistory","Medical History")}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("relevantMedicalHistory","Relevant Medical History")}),(0,l.jsx)("textarea",{value:x.medicalHistory,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{medicalHistory:e.target.value})),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("medicalHistoryPlaceholder","Include previous surgeries, hospitalizations, chronic conditions...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("currentMedications","Current Medications")}),(0,l.jsx)("textarea",{value:x.currentMedications,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{currentMedications:e.target.value})),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("medicationsPlaceholder","List all current medications with dosages...")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("allergiesPrecautions","Allergies and Precautions")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("allergies","Known Allergies")}),(0,l.jsx)("textarea",{value:x.allergies,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{allergies:e.target.value})),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("allergiesPlaceholder","List drug allergies, food allergies, environmental allergies...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("precautions","Medical Precautions")}),(0,l.jsx)("textarea",{value:x.precautions,onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{precautions:e.target.value})),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("precautionsPlaceholder","Weight bearing restrictions, cardiac precautions, etc...")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("vitalSigns","Vital Signs and Physical Status")}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("bloodPressure","Blood Pressure")}),(0,l.jsx)("input",{type:"text",value:(null===(e=x.vitalSigns)||void 0===e?void 0:e.bloodPressure)||"",onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{vitalSigns:(0,r.A)((0,r.A)({},s.vitalSigns),{},{bloodPressure:e.target.value})})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"120/80"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("heartRate","Heart Rate")}),(0,l.jsx)("input",{type:"text",value:(null===(s=x.vitalSigns)||void 0===s?void 0:s.heartRate)||"",onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{vitalSigns:(0,r.A)((0,r.A)({},s.vitalSigns),{},{heartRate:e.target.value})})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"72 bpm"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("temperature","Temperature")}),(0,l.jsx)("input",{type:"text",value:(null===(a=x.vitalSigns)||void 0===a?void 0:a.temperature)||"",onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{vitalSigns:(0,r.A)((0,r.A)({},s.vitalSigns),{},{temperature:e.target.value})})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"98.6\xb0F"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("oxygenSaturation","O2 Saturation")}),(0,l.jsx)("input",{type:"text",value:(null===(t=x.vitalSigns)||void 0===t?void 0:t.oxygenSaturation)||"",onChange:e=>h(s=>(0,r.A)((0,r.A)({},s),{},{vitalSigns:(0,r.A)((0,r.A)({},s.vitalSigns),{},{oxygenSaturation:e.target.value})})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:"98%"})]})]})]})]});case"functional":return(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium text-blue-900 dark:text-blue-100 mb-2",children:d("fimInstructions","FIM Assessment Instructions")}),(0,l.jsx)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:d("fimDescription","Rate each item based on the amount of assistance required. 7 = Complete Independence, 1 = Total Assistance.")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("selfCare","Self-Care")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsx)(p,{label:d("eating","Eating"),value:x.functionalAssessment.selfCare.eating,onChange:e=>y("functionalAssessment","selfCare","eating",e)}),(0,l.jsx)(p,{label:d("grooming","Grooming"),value:x.functionalAssessment.selfCare.grooming,onChange:e=>y("functionalAssessment","selfCare","grooming",e)}),(0,l.jsx)(p,{label:d("bathing","Bathing"),value:x.functionalAssessment.selfCare.bathing,onChange:e=>y("functionalAssessment","selfCare","bathing",e)}),(0,l.jsx)(p,{label:d("dressingUpper","Dressing Upper Body"),value:x.functionalAssessment.selfCare.dressingUpper,onChange:e=>y("functionalAssessment","selfCare","dressingUpper",e)}),(0,l.jsx)(p,{label:d("dressingLower","Dressing Lower Body"),value:x.functionalAssessment.selfCare.dressingLower,onChange:e=>y("functionalAssessment","selfCare","dressingLower",e)}),(0,l.jsx)(p,{label:d("toileting","Toileting"),value:x.functionalAssessment.selfCare.toileting,onChange:e=>y("functionalAssessment","selfCare","toileting",e)})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("communication","Communication")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsx)(p,{label:d("comprehension","Comprehension"),value:x.functionalAssessment.communication.comprehension,onChange:e=>y("functionalAssessment","communication","comprehension",e)}),(0,l.jsx)(p,{label:d("expression","Expression"),value:x.functionalAssessment.communication.expression,onChange:e=>y("functionalAssessment","communication","expression",e)})]})]})]});case"risk":return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium text-yellow-900 dark:text-yellow-100 mb-2",children:d("riskAssessmentNote","Risk Assessment Guidelines")}),(0,l.jsx)("p",{className:"text-sm text-yellow-800 dark:text-yellow-200",children:d("riskDescription","Assess each risk category and develop mitigation strategies for identified risks.")})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,l.jsx)(v,{label:d("fallRisk","Fall Risk"),value:x.riskAssessment.fallRisk,onChange:e=>b("riskAssessment","fallRisk",e)}),(0,l.jsx)(v,{label:d("skinIntegrityRisk","Skin Integrity Risk"),value:x.riskAssessment.skinIntegrityRisk,onChange:e=>b("riskAssessment","skinIntegrityRisk",e)}),(0,l.jsx)(v,{label:d("nutritionalRisk","Nutritional Risk"),value:x.riskAssessment.nutritionalRisk,onChange:e=>b("riskAssessment","nutritionalRisk",e)}),(0,l.jsx)(v,{label:d("medicationRisk","Medication Risk"),value:x.riskAssessment.medicationRisk,onChange:e=>b("riskAssessment","medicationRisk",e)}),(0,l.jsx)(v,{label:d("behavioralRisk","Behavioral Risk"),value:x.riskAssessment.behavioralRisk,onChange:e=>b("riskAssessment","behavioralRisk",e)}),(0,l.jsx)(v,{label:d("cognitiveRisk","Cognitive Risk"),value:x.riskAssessment.cognitiveRisk,onChange:e=>b("riskAssessment","cognitiveRisk",e)})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("riskMitigationPlan","Risk Mitigation Plan")}),(0,l.jsx)("textarea",{value:x.riskAssessment.riskMitigationPlan,onChange:e=>b("riskAssessment","riskMitigationPlan",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("riskMitigationPlaceholder","Describe specific interventions and monitoring strategies for identified risks...")})]})]});case"psychosocial":return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium text-purple-900 dark:text-purple-100 mb-2",children:d("psychosocialInstructions","Psychosocial Assessment Instructions")}),(0,l.jsx)("p",{className:"text-sm text-purple-800 dark:text-purple-200",children:d("psychosocialDescription","Assess psychological, social, and emotional factors affecting rehabilitation.")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("psychologicalStatus","Psychological Status")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("moodState","Mood State")}),(0,l.jsxs)("select",{value:(null===(i=x.psychosocialAssessment)||void 0===i?void 0:i.moodState)||"stable",onChange:e=>b("psychosocialAssessment","moodState",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"stable",children:d("stable","Stable")}),(0,l.jsx)("option",{value:"anxious",children:d("anxious","Anxious")}),(0,l.jsx)("option",{value:"depressed",children:d("depressed","Depressed")}),(0,l.jsx)("option",{value:"agitated",children:d("agitated","Agitated")}),(0,l.jsx)("option",{value:"euphoric",children:d("euphoric","Euphoric")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("copingStrategies","Coping Strategies")}),(0,l.jsxs)("select",{value:(null===(n=x.psychosocialAssessment)||void 0===n?void 0:n.copingStrategies)||"adaptive",onChange:e=>b("psychosocialAssessment","copingStrategies",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"adaptive",children:d("adaptive","Adaptive")}),(0,l.jsx)("option",{value:"maladaptive",children:d("maladaptive","Maladaptive")}),(0,l.jsx)("option",{value:"developing",children:d("developing","Developing")}),(0,l.jsx)("option",{value:"limited",children:d("limited","Limited")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("motivationLevel","Motivation Level")}),(0,l.jsxs)("select",{value:(null===(o=x.psychosocialAssessment)||void 0===o?void 0:o.motivationLevel)||"high",onChange:e=>b("psychosocialAssessment","motivationLevel",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"high",children:d("high","High")}),(0,l.jsx)("option",{value:"moderate",children:d("moderate","Moderate")}),(0,l.jsx)("option",{value:"low",children:d("low","Low")}),(0,l.jsx)("option",{value:"variable",children:d("variable","Variable")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("insightLevel","Insight Level")}),(0,l.jsxs)("select",{value:(null===(c=x.psychosocialAssessment)||void 0===c?void 0:c.insightLevel)||"good",onChange:e=>b("psychosocialAssessment","insightLevel",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"good",children:d("good","Good")}),(0,l.jsx)("option",{value:"fair",children:d("fair","Fair")}),(0,l.jsx)("option",{value:"poor",children:d("poor","Poor")}),(0,l.jsx)("option",{value:"absent",children:d("absent","Absent")})]})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("socialSupport","Social Support System")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("familySupport","Family Support")}),(0,l.jsxs)("select",{value:(null===(m=x.psychosocialAssessment)||void 0===m?void 0:m.familySupport)||"strong",onChange:e=>b("psychosocialAssessment","familySupport",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"strong",children:d("strong","Strong")}),(0,l.jsx)("option",{value:"moderate",children:d("moderate","Moderate")}),(0,l.jsx)("option",{value:"limited",children:d("limited","Limited")}),(0,l.jsx)("option",{value:"absent",children:d("absent","Absent")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("socialConnections","Social Connections")}),(0,l.jsxs)("select",{value:(null===(k=x.psychosocialAssessment)||void 0===k?void 0:k.socialConnections)||"adequate",onChange:e=>b("psychosocialAssessment","socialConnections",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"extensive",children:d("extensive","Extensive")}),(0,l.jsx)("option",{value:"adequate",children:d("adequate","Adequate")}),(0,l.jsx)("option",{value:"limited",children:d("limited","Limited")}),(0,l.jsx)("option",{value:"isolated",children:d("isolated","Isolated")})]})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("culturalSpiritual","Cultural and Spiritual Factors")}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("culturalConsiderations","Cultural Considerations")}),(0,l.jsx)("textarea",{value:(null===(j=x.psychosocialAssessment)||void 0===j?void 0:j.culturalConsiderations)||"",onChange:e=>b("psychosocialAssessment","culturalConsiderations",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("culturalConsiderationsPlaceholder","Describe cultural factors that may impact treatment...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("spiritualNeeds","Spiritual Needs")}),(0,l.jsx)("textarea",{value:(null===(f=x.psychosocialAssessment)||void 0===f?void 0:f.spiritualNeeds)||"",onChange:e=>b("psychosocialAssessment","spiritualNeeds",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("spiritualNeedsPlaceholder","Describe spiritual or religious considerations...")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("psychosocialNotes","Additional Psychosocial Notes")}),(0,l.jsx)("textarea",{value:(null===(A=x.psychosocialAssessment)||void 0===A?void 0:A.notes)||"",onChange:e=>b("psychosocialAssessment","notes",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("psychosocialNotesPlaceholder","Additional observations, concerns, or recommendations...")})]})]});case"environmental":return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium text-green-900 dark:text-green-100 mb-2",children:d("environmentalInstructions","Environmental Assessment Instructions")}),(0,l.jsx)("p",{className:"text-sm text-green-800 dark:text-green-200",children:d("environmentalDescription","Assess home, work, and community environments for accessibility and safety.")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("homeEnvironment","Home Environment")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("homeAccessibility","Home Accessibility")}),(0,l.jsxs)("select",{value:(null===(w=x.environmentalAssessment)||void 0===w?void 0:w.homeAccessibility)||"accessible",onChange:e=>b("environmentalAssessment","homeAccessibility",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"fully-accessible",children:d("fullyAccessible","Fully Accessible")}),(0,l.jsx)("option",{value:"accessible",children:d("accessible","Accessible")}),(0,l.jsx)("option",{value:"partially-accessible",children:d("partiallyAccessible","Partially Accessible")}),(0,l.jsx)("option",{value:"not-accessible",children:d("notAccessible","Not Accessible")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("homeSafety","Home Safety")}),(0,l.jsxs)("select",{value:(null===(N=x.environmentalAssessment)||void 0===N?void 0:N.homeSafety)||"safe",onChange:e=>b("environmentalAssessment","homeSafety",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"safe",children:d("safe","Safe")}),(0,l.jsx)("option",{value:"minor-hazards",children:d("minorHazards","Minor Hazards")}),(0,l.jsx)("option",{value:"moderate-hazards",children:d("moderateHazards","Moderate Hazards")}),(0,l.jsx)("option",{value:"unsafe",children:d("unsafe","Unsafe")})]})]})]}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("homeModifications","Recommended Home Modifications")}),(0,l.jsx)("textarea",{value:(null===(C=x.environmentalAssessment)||void 0===C?void 0:C.homeModifications)||"",onChange:e=>b("environmentalAssessment","homeModifications",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("homeModificationsPlaceholder","Describe recommended modifications for safety and accessibility...")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("communityEnvironment","Community Environment")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("transportationAccess","Transportation Access")}),(0,l.jsxs)("select",{value:(null===(S=x.environmentalAssessment)||void 0===S?void 0:S.transportationAccess)||"adequate",onChange:e=>b("environmentalAssessment","transportationAccess",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"excellent",children:d("excellent","Excellent")}),(0,l.jsx)("option",{value:"adequate",children:d("adequate","Adequate")}),(0,l.jsx)("option",{value:"limited",children:d("limited","Limited")}),(0,l.jsx)("option",{value:"none",children:d("none","None")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("communityResources","Community Resources")}),(0,l.jsxs)("select",{value:(null===(P=x.environmentalAssessment)||void 0===P?void 0:P.communityResources)||"available",onChange:e=>b("environmentalAssessment","communityResources",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"extensive",children:d("extensive","Extensive")}),(0,l.jsx)("option",{value:"available",children:d("available","Available")}),(0,l.jsx)("option",{value:"limited",children:d("limited","Limited")}),(0,l.jsx)("option",{value:"unavailable",children:d("unavailable","Unavailable")})]})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("environmentalNotes","Environmental Assessment Notes")}),(0,l.jsx)("textarea",{value:(null===(R=x.environmentalAssessment)||void 0===R?void 0:R.notes)||"",onChange:e=>b("environmentalAssessment","notes",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("environmentalNotesPlaceholder","Additional environmental observations and recommendations...")})]})]});case"goals":return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg",children:[(0,l.jsx)("h4",{className:"font-medium text-indigo-900 dark:text-indigo-100 mb-2",children:d("goalsInstructions","Goals and Preferences Instructions")}),(0,l.jsx)("p",{className:"text-sm text-indigo-800 dark:text-indigo-200",children:d("goalsDescription","Document person-served goals, preferences, and discharge planning.")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("personServedGoals","Person-Served Goals")}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("shortTermGoals","Short-Term Goals (1-3 months)")}),(0,l.jsx)("textarea",{value:(null===(D=x.goalsAndPreferences)||void 0===D?void 0:D.shortTermGoals)||"",onChange:e=>b("goalsAndPreferences","shortTermGoals",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("shortTermGoalsPlaceholder","List specific, measurable short-term goals...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("longTermGoals","Long-Term Goals (6+ months)")}),(0,l.jsx)("textarea",{value:(null===(M=x.goalsAndPreferences)||void 0===M?void 0:M.longTermGoals)||"",onChange:e=>b("goalsAndPreferences","longTermGoals",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("longTermGoalsPlaceholder","List long-term functional and life goals...")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("preferences","Preferences and Choices")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("treatmentPreferences","Treatment Preferences")}),(0,l.jsx)("textarea",{value:(null===(L=x.goalsAndPreferences)||void 0===L?void 0:L.treatmentPreferences)||"",onChange:e=>b("goalsAndPreferences","treatmentPreferences",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("treatmentPreferencesPlaceholder","Preferred treatment approaches, times, settings...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("communicationPreferences","Communication Preferences")}),(0,l.jsx)("textarea",{value:(null===(I=x.goalsAndPreferences)||void 0===I?void 0:I.communicationPreferences)||"",onChange:e=>b("goalsAndPreferences","communicationPreferences",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("communicationPreferencesPlaceholder","Preferred communication methods and languages...")})]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("dischargePlanning","Discharge Planning")}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("dischargeGoals","Discharge Goals")}),(0,l.jsx)("textarea",{value:(null===(G=x.goalsAndPreferences)||void 0===G?void 0:G.dischargeGoals)||"",onChange:e=>b("goalsAndPreferences","dischargeGoals",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("dischargeGoalsPlaceholder","Expected functional level and living situation at discharge...")})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("estimatedLength","Estimated Length of Stay")}),(0,l.jsx)("input",{type:"text",value:(null===(H=x.goalsAndPreferences)||void 0===H?void 0:H.estimatedLength)||"",onChange:e=>b("goalsAndPreferences","estimatedLength",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:d("estimatedLengthPlaceholder","e.g., 6-8 weeks")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("dischargeDestination","Anticipated Discharge Destination")}),(0,l.jsxs)("select",{value:(null===(T=x.goalsAndPreferences)||void 0===T?void 0:T.dischargeDestination)||"home",onChange:e=>b("goalsAndPreferences","dischargeDestination",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"home",children:d("home","Home")}),(0,l.jsx)("option",{value:"assisted-living",children:d("assistedLiving","Assisted Living")}),(0,l.jsx)("option",{value:"skilled-nursing",children:d("skilledNursing","Skilled Nursing Facility")}),(0,l.jsx)("option",{value:"other-facility",children:d("otherFacility","Other Facility")})]})]})]})]})]})]});default:return(0,l.jsxs)("div",{children:["Section content for ",E.title]})}})()}),(0,l.jsxs)("div",{className:"px-6 py-4 border-t border-gray-200 dark:border-gray-600 flex justify-between",children:[(0,l.jsxs)("button",{onClick:()=>m(Math.max(0,g-1)),disabled:0===g,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,l.jsx)("i",{className:"fas fa-arrow-left mr-2"}),d("previous","Previous")]}),g===u.length-1?(0,l.jsxs)("button",{onClick:()=>a(x),className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-save mr-2"}),d("saveAssessment","Save Assessment")]}):(0,l.jsxs)("button",{onClick:()=>m(Math.min(u.length-1,g+1)),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[d("next","Next"),(0,l.jsx)("i",{className:"fas fa-arrow-right ml-2"})]})]})]})}}}]);
//# sourceMappingURL=7016.3030a5fc.chunk.js.map