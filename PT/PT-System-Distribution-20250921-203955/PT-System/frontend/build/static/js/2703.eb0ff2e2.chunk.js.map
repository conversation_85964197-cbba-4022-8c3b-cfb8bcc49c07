{"version": 3, "file": "static/js/2703.eb0ff2e2.chunk.js", "mappings": "6MAKA,MA6kCA,EA7kCoCA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACxC,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACR,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAgBC,IAAqBF,EAAAA,EAAAA,WAAS,IAC9CG,EAASC,IAAcJ,EAAAA,EAAAA,UAAS,OAChCK,EAAeC,IAAoBN,EAAAA,EAAAA,UAAS,CACjDO,cAAe,GACfC,cAAe,KACfC,iBAAkB,GAClBC,aAAc,GACdC,gBAAiB,EACjBC,iCAAkC,KAClCC,2BAA4B,MAEvBC,EAAgBC,IAAqBf,EAAAA,EAAAA,UAAS,YAC9CgB,EAAgBC,IAAqBjB,EAAAA,EAAAA,UAAS,OAC9CkB,EAAiBC,IAAsBnB,EAAAA,EAAAA,WAAS,IAChDoB,GAAUC,KAAerB,EAAAA,EAAAA,UAAS,KAClCsB,GAAYC,KAAiBvB,EAAAA,EAAAA,UAAS,KAE7CwB,EAAAA,EAAAA,WAAU,KACJ9B,GACF+B,KACAC,OAGAC,KACA5B,GAAW,GACXG,GAAkB,KAEnB,CAACR,EAAWoB,IAEf,MAAMW,GAAkBG,UACtB,IACE1B,GAAkB,GAClB,MAAM2B,QAAiBC,MAAM,oBAADC,OAAqBrC,GAAa,CAC5DsC,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9BjC,EAAWgC,EAAOE,KACpB,MAEElC,EAAW,CACTmC,IAAK7C,EACL8C,UAAW,QACXC,SAAU,YACVC,YAAa,aACbC,OAAQ,OACRC,MAAO,mBACPC,MAAO,2BACPC,oBAAqB,eACrBC,iBAAkB,8BAClBC,cAAe,iCACfC,kBAAmB,iBACnBC,cAAe,aACfC,aAAc,CACZC,iBAAiB,EACjBC,eAAgB,KAIxB,CAAE,MAAOC,GACPC,QAAQD,MAAM,8BAA+BA,GAC7CE,EAAAA,GAAMF,MAAM9D,EAAE,sBAAuB,qCACvC,CAAC,QACCU,GAAkB,EACpB,GAGIyB,GAAmBC,UACvB,IACE,MAAMC,QAAiBC,MAAM,4BAA6B,CACxDE,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9BhB,GAAYe,EAAOE,MAAQ,GAC7B,MAEEjB,GAAY,CACV,CACEkB,IAAK,IACLC,UAAW,QACXC,SAAU,YACVK,oBAAqB,eACrBC,iBAAkB,8BAClBE,kBAAmB,kBAErB,CACEV,IAAK,IACLC,UAAW,SACXC,SAAU,cACVK,oBAAqB,eACrBC,iBAAkB,4BAClBE,kBAAmB,iBAErB,CACEV,IAAK,IACLC,UAAW,WACXC,SAAU,YACVK,oBAAqB,eACrBC,iBAAkB,oBAClBE,kBAAmB,mBAI3B,CAAE,MAAOK,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAMF,MAAM9D,EAAE,uBAAwB,+BACxC,GAGIkC,GAAoBE,UACxB,IACE7B,GAAW,GAGX,MAAM8B,QAAiBC,MAAM,uCAADC,OAAwCrC,EAAS,qBAAAqC,OAAoBjB,GAAkB,CACjHkB,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9B/B,EAAiB8B,EAAOE,KAC1B,KAAO,CAyHPhC,EAvHmB,CACjBC,cAAe,CACb,CACEkD,KAAM,aACNC,YAAa,GACbC,QAAS,KACTC,UAAW,EACXC,SAAU,GACVC,gBAAiB,IAEnB,CACEL,KAAM,aACNC,YAAa,GACbC,QAAS,KACTC,UAAW,EACXC,SAAU,GACVC,gBAAiB,IAEnB,CACEL,KAAM,aACNC,YAAa,GACbC,QAAS,KACTC,UAAW,EACXC,SAAU,IACVC,gBAAiB,KAGrBtD,cAAe,CACbkD,YAAa,CAAEK,MAAO,GAAIC,UAAW,YACrCC,QAAS,CAAEC,KAAM,KAAMF,UAAW,UAClCJ,UAAW,CAAEO,QAAS,EAAGC,QAAS,KAClCC,uBAAwB,CAAEC,MAAO,IAAKC,MAAO,GAAIC,UAAW,IAC5DV,gBAAiB,IAEnBrD,iBAAkB,CAAC,wBAAyB,kBAAmB,2BAC/DC,aAAc,GACdC,gBAAiB,EACjB8D,OAAQ3D,EAGRF,iCAAkC,CAChC8D,gBAAiB,CACfC,WAAY,GACZC,WAAY,GACZC,eAAgB,GAChBpB,KAAM,aACNqB,MAAO,uBAETC,eAAgB,CACdJ,WAAY,IACZC,WAAY,GACZC,eAAgB,GAChBpB,KAAM,aACNqB,MAAO,sBAETE,YAAa,CACXC,iBAAkB,GAClBC,iBAAkB,GAClBC,qBAAsB,EACtBC,sBAAuB,KACvBC,kBAAmB,KAKvBxE,2BAA4B,CAC1B,CACEyE,SAAU,8BACVC,cAAe,GACfC,mBAAoB,KACpBC,uBAAwB,KACxBC,eAAgB,KAChBC,cAAe,KACfC,eAAgB,KAChBC,YAAa,IAEf,CACEP,SAAU,4BACVC,cAAe,GACfC,mBAAoB,KACpBC,uBAAwB,KACxBC,eAAgB,KAChBC,cAAe,KACfC,eAAgB,KAChBC,YAAa,MAEf,CACEP,SAAU,2BACVC,cAAe,GACfC,mBAAoB,KACpBC,uBAAwB,KACxBC,eAAgB,KAChBC,cAAe,KACfC,eAAgB,KAChBC,YAAa,MAEf,CACEP,SAAU,wBACVC,cAAe,GACfC,mBAAoB,KACpBC,uBAAwB,KACxBC,eAAgB,KAChBC,cAAe,KACfC,eAAgB,GAChBC,YAAa,MAEf,CACEP,SAAU,yBACVC,cAAe,GACfC,mBAAoB,KACpBC,uBAAwB,KACxBC,eAAgB,KAChBC,cAAe,KACfC,eAAgB,GAChBC,YAAa,QAMnB,CACF,CAAE,MAAOvC,GACPC,QAAQD,MAAM,gCAAiCA,EACjD,CAAC,QACCvD,GAAW,EACb,GAGI+F,GAAoB/B,GACpBA,GAAS,GAAW,qCACpBA,GAAS,GAAW,uCACjB,iCAGHgC,GAAqB/B,IACzB,OAAQA,GACN,IAAK,WACL,IAAK,SACH,MAAO,uEACT,IAAK,gBACL,IAAK,kBACH,MAAO,2EACT,IAAK,YACL,IAAK,oBACH,MAAO,+DACT,QACE,MAAO,qEAIb,GAAIlE,EACF,OACEkG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yDACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4DAA2DC,SACvE,IAAIE,MAAM,IAAIC,IAAI,CAACC,EAAGC,KACrBP,EAAAA,EAAAA,KAAA,OAAaC,UAAU,gDAAbM,OAGdP,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAOvB,MAAMO,GAAmBpF,GAASqF,OAAOtG,GACvCA,EAAQqC,UAAUkE,cAAcC,SAASrF,GAAWoF,gBACpDvG,EAAQsC,SAASiE,cAAcC,SAASrF,GAAWoF,gBACnDvG,EAAQ2C,oBAAoB4D,cAAcC,SAASrF,GAAWoF,gBAC9DvG,EAAQ4C,iBAAiB2D,cAAcC,SAASrF,GAAWoF,gBAI7D,OAAKhH,GAkGHyG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sFAAqFC,UAClGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,UACEY,QAASA,IAAMhH,EAAS,aACxBqG,UAAU,2GAA0GC,UAEpHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,6GAA4GC,SACvH1G,EAAE,8BAA+B,sCAGrCS,GACCkG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uEACfD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAE1G,EAAE,iBAAkB,uCAErEW,GACFgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,UAClEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,sDAAqDC,SAAA,CAC/D/F,EAAQqC,UAAU,IAAErC,EAAQsC,aAE/B0D,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpD1G,EAAE,MAAO,OAAO,KAAGW,EAAQ2C,8BAIlCqD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDC,UACpEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mEAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D/F,EAAQ4C,oBAEXoD,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpD1G,EAAE,YAAa,aAAa,KAAGW,EAAQ8C,4BAI9CkD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D1G,EAAE,gBAAiB,qBAEtBwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpD,IAAIW,KAAK1G,EAAQ+C,eAAe4D,iCAMzCd,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAAgCC,SAAE1G,EAAE,kBAAmB,2BAGxE2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,UACES,QAASA,IAAMhH,EAAS,8BAADmC,OAA+BrC,IACtDuG,UAAU,oFAAmFC,SAAA,EAE7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZzG,EAAE,gBAAiB,sBAEtB2G,EAAAA,EAAAA,MAAA,UACEY,MAAOjG,EACPkG,SAAWC,GAAMlG,EAAkBkG,EAAEC,OAAOH,OAC5Cd,UAAU,2HAA0HC,SAAA,EAEpIF,EAAAA,EAAAA,KAAA,UAAQe,MAAM,SAAQb,SAAE1G,EAAE,SAAU,cACpCwG,EAAAA,EAAAA,KAAA,UAAQe,MAAM,UAASb,SAAE1G,EAAE,UAAW,eACtCwG,EAAAA,EAAAA,KAAA,UAAQe,MAAM,UAASb,SAAE1G,EAAE,UAAW,eACtCwG,EAAAA,EAAAA,KAAA,UAAQe,MAAM,QAAOb,SAAE1G,EAAE,QAAS,4BAS9C2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4DAA2DC,SAAA,EAExEC,EAAAA,EAAAA,MAAA,UACES,QAASA,IAAM3F,EAAkB,eACjCgF,UAAU,qPAAoPC,SAAA,EAE9PC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEAEfD,EAAAA,EAAAA,KAAA,QAAMC,UAAS,2EAAAlE,OAA6EgE,GAA6C,QAA5BvI,EAAC6C,EAAcG,qBAAa,IAAAhD,GAAa,QAAbC,EAA3BD,EAA6BkG,mBAAW,IAAAjG,OAAb,EAA3BA,EAA0CuG,YAAakC,SACvI,QADuIxI,EAClK2C,EAAcG,qBAAa,IAAA9C,GAAa,QAAbC,EAA3BD,EAA6BgG,mBAAW,IAAA/F,OAAb,EAA3BA,EAA0CqG,gBAG/CgC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtE1G,EAAE,mBAAoB,yBAEzB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,sDAAqDC,SAAA,EACpC,QAA3BtI,EAAAyC,EAAcG,qBAAa,IAAA5C,GAAa,QAAbC,EAA3BD,EAA6B8F,mBAAW,IAAA7F,OAAb,EAA3BA,EAA0CkG,QAAS,EAAE,UAExDiC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD1G,EAAE,oBAAqB,yBAE1B2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACZzG,EAAE,kBAAmB,4BAK1B2G,EAAAA,EAAAA,MAAA,UACES,QAASA,IAAM3F,EAAkB,WACjCgF,UAAU,2PAA0PC,SAAA,EAEpQC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDC,UAC/DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEAEfD,EAAAA,EAAAA,KAAA,QAAMC,UAAS,2EAAAlE,OAA6EgE,GAA6C,QAA5BjI,EAACuC,EAAcG,qBAAa,IAAA1C,GAAS,QAATC,EAA3BD,EAA6BmG,eAAO,IAAAlG,OAAT,EAA3BA,EAAsCiG,YAAakC,SACnI,QADmIlI,EAC9JqC,EAAcG,qBAAa,IAAAxC,GAAS,QAATC,EAA3BD,EAA6BiG,eAAO,IAAAhG,OAAT,EAA3BA,EAAsC+F,gBAG3CgC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SACxE1G,EAAE,UAAW,oBAEhB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wDAAuDC,SAAA,EACtC,QAA3BhI,EAAAmC,EAAcG,qBAAa,IAAAtC,GAAS,QAATC,EAA3BD,EAA6B+F,eAAO,IAAA9F,OAAT,EAA3BA,EAAsC+F,OAAQ,EAAE,QAEnD8B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAC3D1G,EAAE,qBAAsB,0BAE3B2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACZzG,EAAE,kBAAmB,4BAK1B2G,EAAAA,EAAAA,MAAA,UACES,QAASA,IAAM3F,EAAkB,aACjCgF,UAAU,6OAA4OC,SAAA,EAEtPC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,UAC3DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEAEfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,SAAC,kBAI1DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0DAAyDC,SACpE1G,EAAE,mBAAoB,yBAEzB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,oDAAmDC,SAAA,EAClC,QAA3B9H,EAAAiC,EAAcG,qBAAa,IAAApC,GAAW,QAAXC,EAA3BD,EAA6BwF,iBAAS,IAAAvF,OAAX,EAA3BA,EAAwC8F,UAAW,EAAE,UAExD6B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8CAA6CC,SACvD1G,EAAE,iBAAkB,sBAEvB2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACZzG,EAAE,kBAAmB,4BAK1B2G,EAAAA,EAAAA,MAAA,UACES,QAASA,IAAM3F,EAAkB,0BACjCgF,UAAU,2PAA0PC,SAAA,EAEpQC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEAEfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,SAAC,kBAIhEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gEAA+DC,SAC1E1G,EAAE,yBAA0B,8BAE/B2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,0DAAyDC,SAAA,EACxC,QAA3B5H,EAAA+B,EAAcG,qBAAa,IAAAlC,GAAwB,QAAxBC,EAA3BD,EAA6B+F,8BAAsB,IAAA9F,OAAxB,EAA3BA,EAAqD+F,QAAS,EAAE,WAEnE0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D1G,EAAE,oBAAqB,yBAE1B2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACZzG,EAAE,kBAAmB,4BAK1B2G,EAAAA,EAAAA,MAAA,UACES,QAASA,IAAM3F,EAAkB,mBACjCgF,UAAU,6PAA4PC,SAAA,EAEtQC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEAEfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,SAAC,kBAIhEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gEAA+DC,SAC1E1G,EAAE,kBAAmB,uBAExB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAS,sBAAAlE,OAAwB+D,IAA4C,QAA3BtH,EAAA6B,EAAcG,qBAAa,IAAAhC,OAAA,EAA3BA,EAA6BsF,kBAAmB,IAAKoC,SAAA,EAC5E,QAA3BzH,EAAA4B,EAAcG,qBAAa,IAAA/B,OAAA,EAA3BA,EAA6BqF,kBAAmB,EAAE,QAErDkC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D1G,EAAE,iBAAkB,sBAEvB2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACZzG,EAAE,kBAAmB,+BAM5B2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2KAA0KC,SAAA,EACvLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oFAAmFC,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZzG,EAAE,mCAAoC,2RAGxCa,EAAcO,mCACbuF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qDAAoDC,SAC/D1G,EAAE,kBAAmB,8DAExBwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CC,SACxD,IAAIW,KAAKxG,EAAcO,iCAAiC8D,gBAAgBjB,MAAMqD,8BAKrFX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAE1G,EAAE,gBAAiB,gFACnF2G,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iDAAgDC,SAAA,CAC7D7F,EAAcO,iCAAiC8D,gBAAgBC,WAAW,cAG/EwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAE1G,EAAE,aAAc,sFAChF2G,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iDAAgDC,SAAA,CAC7D7F,EAAcO,iCAAiC8D,gBAAgBE,WAAW,aAG/EuB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAE1G,EAAE,iBAAkB,4FACpF2G,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iDAAgDC,SAAA,CAC7D7F,EAAcO,iCAAiC8D,gBAAgBG,eAAe,aAGnFmB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yDAAwDC,UACrEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qDAAoDC,SACjE1G,EAAE,qBAAsB,kFAOjCwG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,UAC/CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8DAA6DC,SACvE1G,EAAE,cAAe,2CAEpB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wDAAuDC,SAAA,CAAC,IACjE7F,EAAcO,iCAAiCoE,YAAYI,sBAAsB,QAErFe,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6CAA4CC,SAAA,CACtD7F,EAAcO,iCAAiCoE,YAAYK,kBAAkB,IAAE7F,EAAE,OAAQ,kCAOlG2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDC,UACpEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qDAAoDC,SAC/D1G,EAAE,iBAAkB,8DAEvBwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CC,SACxD,IAAIW,KAAKxG,EAAcO,iCAAiCmE,eAAetB,MAAMqD,8BAKpFX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAE1G,EAAE,gBAAiB,gFACnF2G,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iDAAgDC,SAAA,CAC7D7F,EAAcO,iCAAiCmE,eAAeJ,WAAW,cAG9EwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAE1G,EAAE,aAAc,sFAChF2G,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iDAAgDC,SAAA,CAC7D7F,EAAcO,iCAAiCmE,eAAeH,WAAW,aAG9EuB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+CAA8CC,SAAE1G,EAAE,iBAAkB,4FACpF2G,EAAAA,EAAAA,MAAA,QAAMF,UAAU,iDAAgDC,SAAA,CAC7D7F,EAAcO,iCAAiCmE,eAAeF,eAAe,aAGlFmB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yDAAwDC,UACrEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yDAAwDC,SACrE1G,EAAE,oBAAqB,kFAUtC2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2KAA0KC,SAAA,EACvLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sFAAqFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZzG,EAAE,6BAA8B,6RAGnCwG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,aAAYC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,SAAAE,UACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iFAAgFC,SAC3F1G,EAAE,oBAAqB,yGAE1BwG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mFAAkFC,SAC7F1G,EAAE,gBAAiB,8DAEtBwG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mFAAkFC,SAC7F1G,EAAE,qBAAsB,0EAE3BwG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mFAAkFC,SAC7F1G,EAAE,kBAAmB,0EAExBwG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mFAAkFC,SAC7F1G,EAAE,iBAAkB,oEAEvBwG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mFAAkFC,SAC7F1G,EAAE,gBAAiB,oEAEtBwG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mFAAkFC,SAC7F1G,EAAE,cAAe,yEAIxBwG,EAAAA,EAAAA,KAAA,SAAAE,SACG7F,EAAcQ,2BAA2BwF,IAAI,CAACc,EAAMC,KACnDjB,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,uGAAsGC,SAAA,EAC9HF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,YAAWC,UACvBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4DAA2DC,UACxEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DAEfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qDAAoDC,SACjEiB,EAAK7B,iBAIZU,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2EAA0EC,SACrFiB,EAAK5B,iBAERS,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,aAAAlE,OAAe+D,GAAiBqB,EAAK3B,qBAAsBU,SAAA,CACvEiB,EAAK3B,mBAAmB,UAG7BQ,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,aAAAlE,OAAe+D,GAAiBqB,EAAK1B,yBAA0BS,SAAA,CAC3EiB,EAAK1B,uBAAuB,UAGjCO,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,aAAAlE,OAAe+D,GAAiBqB,EAAKzB,iBAAkBQ,SAAA,CACnEiB,EAAKzB,eAAe,UAGzBM,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,aAAAlE,OAAe+D,GAAiBqB,EAAKxB,gBAAiBO,SAAA,CAClEiB,EAAKxB,cAAc,UAGxBK,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAAuBC,UACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,2EAAAlE,OACboF,EAAKtB,aAAe,GAAK,uEACzBsB,EAAKtB,aAAe,GAAK,2EACzB,gEACCK,SAAA,CACAiB,EAAKtB,YAAY,WAxCfuB,eAmDnBjB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oKAAmKC,SAAA,EAChLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kFAAiFC,SAAA,EAC7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACZzG,EAAE,mBAAoB,2BAGxBa,EAAcI,iBAAiB4G,OAAS,GACvCrB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB7F,EAAcI,iBAAiB4F,IAAI,CAACiB,EAAMF,KACzCpB,EAAAA,EAAAA,KAAA,OAAiBC,UAAU,6FAA4FC,UACrHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDC,UACpEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAAEoB,KAChEtB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6CAA4CC,SACtD1G,EAAE,0BAA2B,sCAR5B4H,OAgBdjB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEACbD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCAAoCC,SAC9C1G,EAAE,oBAAqB,8CAOhC2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sJAAqJC,SAAA,EAClKC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,8EAA6EC,SAAA,EACzFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACZzG,EAAE,eAAgB,uBAGpBa,EAAcK,aAAa2G,OAAS,GACnCrB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB7F,EAAcK,aAAa2F,IAAI,CAACiB,EAAMF,KACrCpB,EAAAA,EAAAA,KAAA,OAAiBC,UAAU,uFAAsFC,UAC/GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,6CAA4CC,SAAEoB,KAC5DtB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCAAwCC,SAClD1G,EAAE,oBAAqB,+BARtB4H,OAgBdjB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEACbD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCAAoCC,SAC9C1G,EAAE,iBAAkB,8CAQ/B2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sGAAqGC,SAAA,EAClHC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,6EAA4EC,SAAA,EACxFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZzG,EAAE,oBAAqB,0BAG1B2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,UAClEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+CAA8CC,SAAE1G,EAAE,mBAAoB,wBACpFwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDAAqDC,SAAE7F,EAAcM,mBAClFqF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAE1G,EAAE,mBAAoB,4BAGjF2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDC,UACpEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+CAA8CC,SAAE1G,EAAE,kBAAmB,uBACnFwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wDAAuDC,SACjE7F,EAAcI,iBAAiB4G,OAAS,EAAI,MAAQ,QAEvDrB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAE1G,EAAE,mBAAoB,4BAGjF2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+CAA8CC,SAAE1G,EAAE,kBAAmB,uBACnFwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DAAyDC,SAAC,UACvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAE1G,EAAE,iBAAkB,iCAMlFwB,IACCgF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFAAgFC,UAC7FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+FAA8FC,UAC3GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,CAC7C,gBAAnBlF,GAAoCxB,EAAE,qBAAsB,8BACzC,YAAnBwB,GAAgCxB,EAAE,iBAAkB,8BACjC,cAAnBwB,GAAkCxB,EAAE,mBAAoB,sBACrC,2BAAnBwB,GAA+CxB,EAAE,gCAAiC,mCAC/D,oBAAnBwB,GAAwCxB,EAAE,yBAA0B,gCAEvEwG,EAAAA,EAAAA,KAAA,UACEY,QAASA,IAAM3F,EAAkB,MACjCgF,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BAIjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACJ,gBAAnBlF,IACCmF,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE1G,EAAE,eAAgB,oBAErB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,sDAAqDC,SAAA,EACpC,QAA3BxH,EAAA2B,EAAcG,qBAAa,IAAA9B,GAAa,QAAbC,EAA3BD,EAA6BgF,mBAAW,IAAA/E,OAAb,EAA3BA,EAA0CoF,QAAS,EAAE,UAExDoC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,gDAA+CC,SAAA,CAAC,eACnB,QAA5BtH,EAACyB,EAAcG,qBAAa,IAAA5B,GAAa,QAAbC,EAA3BD,EAA6B8E,mBAAW,IAAA7E,OAAb,EAA3BA,EAA0CmF,iBAG3DmC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAAC,2BAC/DC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iCACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,oCACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uCAGRC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAAC,qBAC/DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAC,6DAQ5C,YAAnBlF,IACCmF,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDC,SAClE1G,EAAE,cAAe,mBAEpB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wDAAuDC,SAAA,EACtC,QAA3BpH,EAAAuB,EAAcG,qBAAa,IAAA1B,GAAS,QAATC,EAA3BD,EAA6BmF,eAAO,IAAAlF,OAAT,EAA3BA,EAAsCmF,OAAQ,EAAE,eAEnDiC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,kDAAiDC,SAAA,CAAC,eACrB,QAA5BlH,EAACqB,EAAcG,qBAAa,IAAAxB,GAAS,QAATC,EAA3BD,EAA6BiF,eAAO,IAAAhF,OAAT,EAA3BA,EAAsC+E,iBAGvDmC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAAC,0BAC/DC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qCACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2CACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,+CACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iDAGRC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAAC,kBAC/DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAC,6DAQ5C,2BAAnBlF,IACCmF,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yDAAwDC,SAAA,EACrEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0DAAyDC,SACpE1G,EAAE,kBAAmB,wBAExB2G,EAAAA,EAAAA,MAAA,KAAGF,UAAU,0DAAyDC,SAAA,EACxC,QAA3BhH,EAAAmB,EAAcG,qBAAa,IAAAtB,GAAwB,QAAxBC,EAA3BD,EAA6BmF,8BAAsB,IAAAlF,OAAxB,EAA3BA,EAAqDmF,QAAS,EAAE,WAEnE6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CC,SAAC,iBAC5DC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,YAAWC,SAAA,EAA6B,QAA3B9G,EAAAiB,EAAcG,qBAAa,IAAApB,GAAwB,QAAxBC,EAA3BD,EAA6BiF,8BAAsB,IAAAhF,OAAxB,EAA3BA,EAAqDkF,QAAS,EAAE,aAE5F4B,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CC,SAAC,qBAC5DC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,YAAWC,SAAA,EAA6B,QAA3B5G,EAAAe,EAAcG,qBAAa,IAAAlB,GAAwB,QAAxBC,EAA3BD,EAA6B+E,8BAAsB,IAAA9E,OAAxB,EAA3BA,EAAqDiF,YAAa,EAAE,mBAIpG2B,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAAC,0BAC/DC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2CACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,0CACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,sCACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uCACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,sCACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gDAOdF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,UACpCF,EAAAA,EAAAA,KAAA,UACEY,QAASA,IAAM3F,EAAkB,MACjCgF,UAAU,kFAAiFC,SAE1F1G,EAAE,QAAS,wBAvwBxB2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sFAAqFC,UAClGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,6GAA4GC,SACvH1G,EAAE,qCAAsC,6CAE3CwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD1G,EAAE,oBAAqB,4EAQlCwG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sGAAqGC,UAClHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E1G,EAAE,iBAAkB,sBAEvBwG,EAAAA,EAAAA,KAAA,SACEuB,KAAK,OACLC,YAAahI,EAAE,4BAA6B,wCAC5CuH,MAAOzF,GACP0F,SAAWC,GAAM1F,GAAc0F,EAAEC,OAAOH,OACxCd,UAAU,oMAMhBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClEM,GAAiBH,IAAKlG,IACrBgG,EAAAA,EAAAA,MAAA,UAEES,QAASA,IAAMhH,EAAS,kCAADmC,OAAmC5B,EAAQoC,MAClE0D,UAAU,iLAAgLC,SAAA,EAE1LC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,UAClEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,CAChE/F,EAAQqC,UAAU,IAAErC,EAAQsC,aAE/B0D,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpD1G,EAAE,MAAO,OAAO,KAAGW,EAAQ2C,8BAKlCqD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0CACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvD/F,EAAQ4C,uBAGboD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvD/F,EAAQ8C,2BAKfkD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SACnE1G,EAAE,yBAA0B,+BAE/BwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DArCV9F,EAAQoC,QA2CU,IAA5BiE,GAAiBa,QAAgB/F,KAChC6E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE1G,EAAE,kBAAmB,wBAExBwG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5C1G,EAAE,qBAAsB,uC", "sources": ["pages/Analytics/ClinicalIndicatorsDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport toast from 'react-hot-toast';\n\nconst ClinicalIndicatorsDashboard = () => {\n  const { t } = useLanguage();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n  \n  const [loading, setLoading] = useState(true);\n  const [patientLoading, setPatientLoading] = useState(true);\n  const [patient, setPatient] = useState(null);\n  const [analyticsData, setAnalyticsData] = useState({\n    progressTrend: [],\n    currentStatus: null,\n    improvementAreas: [],\n    concernAreas: [],\n    assessmentCount: 0,\n    functionalIndependenceComparison: null,\n    treatmentPlanEffectiveness: []\n  });\n  const [selectedPeriod, setSelectedPeriod] = useState('6months');\n  const [selectedMetric, setSelectedMetric] = useState(null);\n  const [showDetailModal, setShowDetailModal] = useState(false);\n  const [patients, setPatients] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n      loadAnalyticsData();\n    } else {\n      // If no patientId, show patient selector\n      loadPatientsList();\n      setLoading(false);\n      setPatientLoading(false);\n    }\n  }, [patientId, selectedPeriod]);\n\n  const loadPatientData = async () => {\n    try {\n      setPatientLoading(true);\n      const response = await fetch(`/api/v1/patients/${patientId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setPatient(result.data);\n      } else {\n        // Mock patient data for demonstration\n        setPatient({\n          _id: patientId,\n          firstName: 'Ahmed',\n          lastName: 'Al-Rashid',\n          dateOfBirth: '1985-03-15',\n          gender: 'male',\n          phone: '+966-50-123-4567',\n          email: '<EMAIL>',\n          medicalRecordNumber: 'MRN-2024-001',\n          primaryCondition: 'Neurological Rehabilitation',\n          treatmentPlan: 'Comprehensive Physical Therapy',\n          assignedTherapist: 'Sarah Al-Zahra',\n          admissionDate: '2024-01-15',\n          specialNeeds: {\n            hasSpecialNeeds: false,\n            accommodations: []\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n      toast.error(t('errorLoadingPatient', 'Error loading patient information'));\n    } finally {\n      setPatientLoading(false);\n    }\n  };\n\n  const loadPatientsList = async () => {\n    try {\n      const response = await fetch('/api/v1/patients?limit=50', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setPatients(result.data || []);\n      } else {\n        // Mock patients data for demonstration\n        setPatients([\n          {\n            _id: '1',\n            firstName: 'Ahmed',\n            lastName: 'Al-Rashid',\n            medicalRecordNumber: 'MRN-2024-001',\n            primaryCondition: 'Neurological Rehabilitation',\n            assignedTherapist: 'Sarah Al-Zahra'\n          },\n          {\n            _id: '2',\n            firstName: 'Fatima',\n            lastName: 'Al-Mansouri',\n            medicalRecordNumber: 'MRN-2024-002',\n            primaryCondition: 'Orthopedic Rehabilitation',\n            assignedTherapist: 'Omar Al-Harbi'\n          },\n          {\n            _id: '3',\n            firstName: 'Mohammed',\n            lastName: 'Al-Otaibi',\n            medicalRecordNumber: 'MRN-2024-003',\n            primaryCondition: 'Pediatric Therapy',\n            assignedTherapist: 'Sarah Al-Zahra'\n          }\n        ]);\n      }\n    } catch (error) {\n      console.error('Error loading patients:', error);\n      toast.error(t('errorLoadingPatients', 'Error loading patients list'));\n    }\n  };\n\n  const loadAnalyticsData = async () => {\n    try {\n      setLoading(true);\n\n      // Try to load real patient clinical indicators data\n      const response = await fetch(`/api/v1/clinical-indicators/patient/${patientId}/progress?period=${selectedPeriod}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setAnalyticsData(result.data);\n      } else {\n        // Mock data for demonstration - patient-specific\n        const mockData = {\n        progressTrend: [\n          {\n            date: '2024-01-15',\n            bergBalance: 35,\n            tugTime: 18.5,\n            painLevel: 7,\n            fimScore: 85,\n            overallProgress: 65\n          },\n          {\n            date: '2024-02-15',\n            bergBalance: 42,\n            tugTime: 15.2,\n            painLevel: 5,\n            fimScore: 95,\n            overallProgress: 75\n          },\n          {\n            date: '2024-03-15',\n            bergBalance: 48,\n            tugTime: 12.8,\n            painLevel: 3,\n            fimScore: 105,\n            overallProgress: 85\n          }\n        ],\n        currentStatus: {\n          bergBalance: { score: 48, riskLevel: 'low-risk' },\n          tugTest: { time: 12.8, riskLevel: 'normal' },\n          painLevel: { current: 3, average: 3.5 },\n          functionalIndependence: { total: 105, motor: 78, cognitive: 27 },\n          overallProgress: 85\n        },\n        improvementAreas: ['Balance and Stability', 'Pain Management', 'Functional Independence'],\n        concernAreas: [],\n        assessmentCount: 3,\n        period: selectedPeriod,\n\n        // درجة الاستقلالية الوظيفية قبل وبعد البرنامج العلاجي\n        functionalIndependenceComparison: {\n          beforeTreatment: {\n            totalScore: 65,\n            motorScore: 45,\n            cognitiveScore: 20,\n            date: '2024-01-15',\n            level: 'moderate-dependence'\n          },\n          afterTreatment: {\n            totalScore: 105,\n            motorScore: 78,\n            cognitiveScore: 27,\n            date: '2024-03-15',\n            level: 'minimal-dependence'\n          },\n          improvement: {\n            totalImprovement: 40,\n            motorImprovement: 33,\n            cognitiveImprovement: 7,\n            improvementPercentage: 61.5,\n            treatmentDuration: 60 // days\n          }\n        },\n\n        // نسبة التحسن السريري حسب نوع الخطة العلاجية المستخدمة\n        treatmentPlanEffectiveness: [\n          {\n            planType: 'Neurological Rehabilitation',\n            patientsCount: 25,\n            averageImprovement: 78.5,\n            bergBalanceImprovement: 85.2,\n            tugImprovement: 72.8,\n            painReduction: 68.4,\n            fimImprovement: 82.1,\n            successRate: 88.0\n          },\n          {\n            planType: 'Orthopedic Rehabilitation',\n            patientsCount: 32,\n            averageImprovement: 71.3,\n            bergBalanceImprovement: 68.9,\n            tugImprovement: 75.2,\n            painReduction: 79.8,\n            fimImprovement: 61.4,\n            successRate: 81.3\n          },\n          {\n            planType: 'Geriatric Rehabilitation',\n            patientsCount: 18,\n            averageImprovement: 65.7,\n            bergBalanceImprovement: 62.1,\n            tugImprovement: 58.9,\n            painReduction: 71.2,\n            fimImprovement: 70.6,\n            successRate: 77.8\n          },\n          {\n            planType: 'Sports Rehabilitation',\n            patientsCount: 15,\n            averageImprovement: 83.2,\n            bergBalanceImprovement: 89.4,\n            tugImprovement: 91.7,\n            painReduction: 75.8,\n            fimImprovement: 76.0,\n            successRate: 93.3\n          },\n          {\n            planType: 'Cardiac Rehabilitation',\n            patientsCount: 12,\n            averageImprovement: 69.8,\n            bergBalanceImprovement: 64.3,\n            tugImprovement: 67.1,\n            painReduction: 58.9,\n            fimImprovement: 89.0,\n            successRate: 83.3\n          }\n        ]\n      };\n\n      setAnalyticsData(mockData);\n      }\n    } catch (error) {\n      console.error('Error loading analytics data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getProgressColor = (score) => {\n    if (score >= 80) return 'text-green-600 dark:text-green-400';\n    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';\n    return 'text-red-600 dark:text-red-400';\n  };\n\n  const getRiskLevelColor = (riskLevel) => {\n    switch (riskLevel) {\n      case 'low-risk':\n      case 'normal':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200';\n      case 'moderate-risk':\n      case 'mild-impairment':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200';\n      case 'high-risk':\n      case 'severe-impairment':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-200';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6\"></div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            {[...Array(4)].map((_, i) => (\n              <div key={i} className=\"h-32 bg-gray-200 dark:bg-gray-700 rounded-lg\"></div>\n            ))}\n          </div>\n          <div className=\"h-64 bg-gray-200 dark:bg-gray-700 rounded-lg\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  // Filter patients based on search term\n  const filteredPatients = patients.filter(patient =>\n    patient.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    patient.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    patient.medicalRecordNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    patient.primaryCondition.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  // If no patient is selected, show patient selector\n  if (!patientId) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8\">\n          <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n            <div className=\"px-6 py-6\">\n              <div className=\"text-center\">\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  {t('selectPatientForClinicalIndicators', 'Select Patient for Clinical Indicators')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2\">\n                  {t('selectPatientDesc', 'Choose a patient to view their clinical indicators dashboard')}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Patient Search */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8\">\n          <div className=\"max-w-md mx-auto\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('searchPatients', 'Search Patients')}\n            </label>\n            <input\n              type=\"text\"\n              placeholder={t('searchPatientsPlaceholder', 'Search by name, MRN, or condition...')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n\n        {/* Patients Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredPatients.map((patient) => (\n            <button\n              key={patient._id}\n              onClick={() => navigate(`/analytics/clinical-indicators/${patient._id}`)}\n              className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left\"\n            >\n              <div className=\"flex items-center mb-4\">\n                <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg mr-4\">\n                  <i className=\"fas fa-user text-blue-600 dark:text-blue-400 text-2xl\"></i>\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {patient.firstName} {patient.lastName}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('mrn', 'MRN')}: {patient.medicalRecordNumber}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center\">\n                  <i className=\"fas fa-heartbeat text-green-500 mr-2\"></i>\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    {patient.primaryCondition}\n                  </span>\n                </div>\n                <div className=\"flex items-center\">\n                  <i className=\"fas fa-user-md text-purple-500 mr-2\"></i>\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    {patient.assignedTherapist}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"mt-4 flex items-center justify-between\">\n                <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\n                  {t('viewClinicalIndicators', 'View Clinical Indicators')}\n                </span>\n                <i className=\"fas fa-arrow-right text-blue-600 dark:text-blue-400\"></i>\n              </div>\n            </button>\n          ))}\n        </div>\n\n        {filteredPatients.length === 0 && searchTerm && (\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('noPatientsFound', 'No patients found')}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('tryDifferentSearch', 'Try a different search term')}\n            </p>\n          </div>\n        )}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <div className=\"flex items-center mb-2\">\n                  <button\n                    onClick={() => navigate('/patients')}\n                    className=\"mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors\"\n                  >\n                    <i className=\"fas fa-arrow-left text-xl\"></i>\n                  </button>\n                  <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                    {t('clinicalIndicatorsDashboard', 'Clinical Indicators Dashboard')}\n                  </h1>\n                </div>\n                {patientLoading ? (\n                  <div className=\"flex items-center\">\n                    <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2\"></div>\n                    <p className=\"text-gray-600 dark:text-gray-400\">{t('loadingPatient', 'Loading patient information...')}</p>\n                  </div>\n                ) : patient ? (\n                  <div className=\"flex items-center space-x-6\">\n                    <div className=\"flex items-center\">\n                      <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg mr-3\">\n                        <i className=\"fas fa-user text-blue-600 dark:text-blue-400 text-xl\"></i>\n                      </div>\n                      <div>\n                        <p className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                          {patient.firstName} {patient.lastName}\n                        </p>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {t('mrn', 'MRN')}: {patient.medicalRecordNumber}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg mr-3\">\n                        <i className=\"fas fa-heartbeat text-green-600 dark:text-green-400 text-xl\"></i>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {patient.primaryCondition}\n                        </p>\n                        <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                          {t('therapist', 'Therapist')}: {patient.assignedTherapist}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg mr-3\">\n                        <i className=\"fas fa-calendar text-purple-600 dark:text-purple-400 text-xl\"></i>\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {t('admissionDate', 'Admission Date')}\n                        </p>\n                        <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                          {new Date(patient.admissionDate).toLocaleDateString()}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                ) : (\n                  <p className=\"text-red-600 dark:text-red-400\">{t('patientNotFound', 'Patient not found')}</p>\n                )}\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => navigate(`/forms/clinical-indicators/${patientId}`)}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  <i className=\"fas fa-plus mr-2\"></i>\n                  {t('newAssessment', 'New Assessment')}\n                </button>\n                <select\n                  value={selectedPeriod}\n                  onChange={(e) => setSelectedPeriod(e.target.value)}\n                  className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"1month\">{t('1month', '1 Month')}</option>\n                  <option value=\"3months\">{t('3months', '3 Months')}</option>\n                  <option value=\"6months\">{t('6months', '6 Months')}</option>\n                  <option value=\"1year\">{t('1year', '1 Year')}</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Key Metrics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8\">\n        {/* Berg Balance Scale */}\n        <button\n          onClick={() => setSelectedMetric('bergBalance')}\n          className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full\"\n        >\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg\">\n              <i className=\"fas fa-balance-scale text-blue-600 dark:text-blue-400 text-2xl\"></i>\n            </div>\n            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskLevelColor(analyticsData.currentStatus?.bergBalance?.riskLevel)}`}>\n              {analyticsData.currentStatus?.bergBalance?.riskLevel}\n            </span>\n          </div>\n          <h3 className=\"text-sm font-medium text-blue-600 dark:text-blue-400 mb-1\">\n            {t('bergBalanceScale', 'Berg Balance Scale')}\n          </h3>\n          <p className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n            {analyticsData.currentStatus?.bergBalance?.score || 0}/56\n          </p>\n          <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\n            {t('balanceAssessment', 'Balance Assessment')}\n          </p>\n          <div className=\"mt-2 text-xs text-blue-500 dark:text-blue-300\">\n            <i className=\"fas fa-mouse-pointer mr-1\"></i>\n            {t('clickForDetails', 'Click for details')}\n          </div>\n        </button>\n\n        {/* Timed Up and Go */}\n        <button\n          onClick={() => setSelectedMetric('tugTest')}\n          className=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full\"\n        >\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg\">\n              <i className=\"fas fa-walking text-green-600 dark:text-green-400 text-2xl\"></i>\n            </div>\n            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskLevelColor(analyticsData.currentStatus?.tugTest?.riskLevel)}`}>\n              {analyticsData.currentStatus?.tugTest?.riskLevel}\n            </span>\n          </div>\n          <h3 className=\"text-sm font-medium text-green-600 dark:text-green-400 mb-1\">\n            {t('tugTest', 'Timed Up & Go')}\n          </h3>\n          <p className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\n            {analyticsData.currentStatus?.tugTest?.time || 0}s\n          </p>\n          <p className=\"text-xs text-green-600 dark:text-green-400 mt-1\">\n            {t('mobilityAssessment', 'Mobility Assessment')}\n          </p>\n          <div className=\"mt-2 text-xs text-green-500 dark:text-green-300\">\n            <i className=\"fas fa-mouse-pointer mr-1\"></i>\n            {t('clickForDetails', 'Click for details')}\n          </div>\n        </button>\n\n        {/* Pain Level */}\n        <button\n          onClick={() => setSelectedMetric('painLevel')}\n          className=\"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-red-200 dark:border-red-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full\"\n        >\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"p-3 bg-red-100 dark:bg-red-900/40 rounded-lg\">\n              <i className=\"fas fa-heartbeat text-red-600 dark:text-red-400 text-2xl\"></i>\n            </div>\n            <div className=\"text-xs text-red-600 dark:text-red-400\">\n              VAS Scale\n            </div>\n          </div>\n          <h3 className=\"text-sm font-medium text-red-600 dark:text-red-400 mb-1\">\n            {t('currentPainLevel', 'Current Pain Level')}\n          </h3>\n          <p className=\"text-2xl font-bold text-red-900 dark:text-red-100\">\n            {analyticsData.currentStatus?.painLevel?.current || 0}/10\n          </p>\n          <p className=\"text-xs text-red-600 dark:text-red-400 mt-1\">\n            {t('painAssessment', 'Pain Assessment')}\n          </p>\n          <div className=\"mt-2 text-xs text-red-500 dark:text-red-300\">\n            <i className=\"fas fa-mouse-pointer mr-1\"></i>\n            {t('clickForDetails', 'Click for details')}\n          </div>\n        </button>\n\n        {/* Functional Independence */}\n        <button\n          onClick={() => setSelectedMetric('functionalIndependence')}\n          className=\"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full\"\n        >\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg\">\n              <i className=\"fas fa-user-check text-orange-600 dark:text-orange-400 text-2xl\"></i>\n            </div>\n            <div className=\"text-xs text-orange-600 dark:text-orange-400\">\n              FIM Score\n            </div>\n          </div>\n          <h3 className=\"text-sm font-medium text-orange-600 dark:text-orange-400 mb-1\">\n            {t('functionalIndependence', 'Functional Independence')}\n          </h3>\n          <p className=\"text-2xl font-bold text-orange-900 dark:text-orange-100\">\n            {analyticsData.currentStatus?.functionalIndependence?.total || 0}/126\n          </p>\n          <p className=\"text-xs text-orange-600 dark:text-orange-400 mt-1\">\n            {t('independenceLevel', 'Independence Level')}\n          </p>\n          <div className=\"mt-2 text-xs text-orange-500 dark:text-orange-300\">\n            <i className=\"fas fa-mouse-pointer mr-1\"></i>\n            {t('clickForDetails', 'Click for details')}\n          </div>\n        </button>\n\n        {/* Overall Progress */}\n        <button\n          onClick={() => setSelectedMetric('overallProgress')}\n          className=\"bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg shadow-lg border border-purple-200 dark:border-purple-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full\"\n        >\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg\">\n              <i className=\"fas fa-chart-line text-purple-600 dark:text-purple-400 text-2xl\"></i>\n            </div>\n            <div className=\"text-xs text-purple-600 dark:text-purple-400\">\n              Composite\n            </div>\n          </div>\n          <h3 className=\"text-sm font-medium text-purple-600 dark:text-purple-400 mb-1\">\n            {t('overallProgress', 'Overall Progress')}\n          </h3>\n          <p className={`text-2xl font-bold ${getProgressColor(analyticsData.currentStatus?.overallProgress || 0)}`}>\n            {analyticsData.currentStatus?.overallProgress || 0}%\n          </p>\n          <p className=\"text-xs text-purple-600 dark:text-purple-400 mt-1\">\n            {t('compositeScore', 'Composite Score')}\n          </p>\n          <div className=\"mt-2 text-xs text-purple-500 dark:text-purple-300\">\n            <i className=\"fas fa-mouse-pointer mr-1\"></i>\n            {t('clickForDetails', 'Click for details')}\n          </div>\n        </button>\n      </div>\n\n      {/* Functional Independence Comparison - درجة الاستقلالية الوظيفية قبل وبعد البرنامج العلاجي */}\n      <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-indigo-200 dark:border-indigo-700 p-6 mb-8\">\n        <h2 className=\"text-xl font-semibold text-indigo-900 dark:text-indigo-100 mb-6 flex items-center\">\n          <i className=\"fas fa-chart-bar text-indigo-600 dark:text-indigo-400 mr-2\"></i>\n          {t('functionalIndependenceComparison', 'درجة الاستقلالية الوظيفية قبل وبعد البرنامج العلاجي')}\n        </h2>\n\n        {analyticsData.functionalIndependenceComparison && (\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {/* Before Treatment */}\n            <div className=\"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-6\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"p-3 bg-red-100 dark:bg-red-900/40 rounded-lg mr-3\">\n                  <i className=\"fas fa-calendar-minus text-red-600 dark:text-red-400 text-xl\"></i>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-indigo-900 dark:text-indigo-100\">\n                    {t('beforeTreatment', 'قبل العلاج')}\n                  </h3>\n                  <p className=\"text-sm text-indigo-600 dark:text-indigo-400\">\n                    {new Date(analyticsData.functionalIndependenceComparison.beforeTreatment.date).toLocaleDateString()}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-indigo-700 dark:text-indigo-300\">{t('totalFimScore', 'إجمالي النقاط')}</span>\n                  <span className=\"font-bold text-indigo-900 dark:text-indigo-100\">\n                    {analyticsData.functionalIndependenceComparison.beforeTreatment.totalScore}/126\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-indigo-700 dark:text-indigo-300\">{t('motorScore', 'النقاط الحركية')}</span>\n                  <span className=\"font-bold text-indigo-900 dark:text-indigo-100\">\n                    {analyticsData.functionalIndependenceComparison.beforeTreatment.motorScore}/91\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-indigo-700 dark:text-indigo-300\">{t('cognitiveScore', 'النقاط المعرفية')}</span>\n                  <span className=\"font-bold text-indigo-900 dark:text-indigo-100\">\n                    {analyticsData.functionalIndependenceComparison.beforeTreatment.cognitiveScore}/35\n                  </span>\n                </div>\n                <div className=\"pt-2 border-t border-indigo-200 dark:border-indigo-600\">\n                  <span className=\"text-xs text-red-600 dark:text-red-400 font-medium\">\n                    {t('moderateDependence', 'اعتماد متوسط')}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Improvement Arrow */}\n            <div className=\"flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"p-4 bg-green-100 dark:bg-green-900/40 rounded-full mb-3\">\n                  <i className=\"fas fa-arrow-right text-green-600 dark:text-green-400 text-2xl\"></i>\n                </div>\n                <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-3\">\n                  <p className=\"text-sm font-medium text-green-700 dark:text-green-300 mb-1\">\n                    {t('improvement', 'التحسن')}\n                  </p>\n                  <p className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                    +{analyticsData.functionalIndependenceComparison.improvement.improvementPercentage}%\n                  </p>\n                  <p className=\"text-xs text-green-600 dark:text-green-400\">\n                    {analyticsData.functionalIndependenceComparison.improvement.treatmentDuration} {t('days', 'يوم')}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* After Treatment */}\n            <div className=\"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-6\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg mr-3\">\n                  <i className=\"fas fa-calendar-check text-green-600 dark:text-green-400 text-xl\"></i>\n                </div>\n                <div>\n                  <h3 className=\"font-semibold text-indigo-900 dark:text-indigo-100\">\n                    {t('afterTreatment', 'بعد العلاج')}\n                  </h3>\n                  <p className=\"text-sm text-indigo-600 dark:text-indigo-400\">\n                    {new Date(analyticsData.functionalIndependenceComparison.afterTreatment.date).toLocaleDateString()}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-indigo-700 dark:text-indigo-300\">{t('totalFimScore', 'إجمالي النقاط')}</span>\n                  <span className=\"font-bold text-indigo-900 dark:text-indigo-100\">\n                    {analyticsData.functionalIndependenceComparison.afterTreatment.totalScore}/126\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-indigo-700 dark:text-indigo-300\">{t('motorScore', 'النقاط الحركية')}</span>\n                  <span className=\"font-bold text-indigo-900 dark:text-indigo-100\">\n                    {analyticsData.functionalIndependenceComparison.afterTreatment.motorScore}/91\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-indigo-700 dark:text-indigo-300\">{t('cognitiveScore', 'النقاط المعرفية')}</span>\n                  <span className=\"font-bold text-indigo-900 dark:text-indigo-100\">\n                    {analyticsData.functionalIndependenceComparison.afterTreatment.cognitiveScore}/35\n                  </span>\n                </div>\n                <div className=\"pt-2 border-t border-indigo-200 dark:border-indigo-600\">\n                  <span className=\"text-xs text-green-600 dark:text-green-400 font-medium\">\n                    {t('minimalDependence', 'اعتماد قليل')}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Treatment Plan Effectiveness - نسبة التحسن السريري حسب نوع الخطة العلاجية المستخدمة */}\n      <div className=\"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6 mb-8\">\n        <h2 className=\"text-xl font-semibold text-emerald-900 dark:text-emerald-100 mb-6 flex items-center\">\n          <i className=\"fas fa-chart-pie text-emerald-600 dark:text-emerald-400 mr-2\"></i>\n          {t('treatmentPlanEffectiveness', 'نسبة التحسن السريري حسب نوع الخطة العلاجية المستخدمة')}\n        </h2>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full\">\n            <thead>\n              <tr className=\"border-b border-emerald-200 dark:border-emerald-600\">\n                <th className=\"text-left py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300\">\n                  {t('treatmentPlanType', 'نوع الخطة العلاجية')}\n                </th>\n                <th className=\"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300\">\n                  {t('patientsCount', 'عدد المرضى')}\n                </th>\n                <th className=\"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300\">\n                  {t('averageImprovement', 'متوسط التحسن')}\n                </th>\n                <th className=\"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300\">\n                  {t('bergImprovement', 'تحسن التوازن')}\n                </th>\n                <th className=\"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300\">\n                  {t('tugImprovement', 'تحسن الحركة')}\n                </th>\n                <th className=\"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300\">\n                  {t('painReduction', 'تقليل الألم')}\n                </th>\n                <th className=\"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300\">\n                  {t('successRate', 'معدل النجاح')}\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {analyticsData.treatmentPlanEffectiveness.map((plan, index) => (\n                <tr key={index} className=\"border-b border-emerald-100 dark:border-emerald-800 hover:bg-emerald-50 dark:hover:bg-emerald-900/10\">\n                  <td className=\"py-4 px-4\">\n                    <div className=\"flex items-center\">\n                      <div className=\"p-2 bg-emerald-100 dark:bg-emerald-900/40 rounded-lg mr-3\">\n                        <i className=\"fas fa-heartbeat text-emerald-600 dark:text-emerald-400\"></i>\n                      </div>\n                      <span className=\"font-medium text-emerald-900 dark:text-emerald-100\">\n                        {plan.planType}\n                      </span>\n                    </div>\n                  </td>\n                  <td className=\"text-center py-4 px-4 font-medium text-emerald-900 dark:text-emerald-100\">\n                    {plan.patientsCount}\n                  </td>\n                  <td className=\"text-center py-4 px-4\">\n                    <span className={`font-bold ${getProgressColor(plan.averageImprovement)}`}>\n                      {plan.averageImprovement}%\n                    </span>\n                  </td>\n                  <td className=\"text-center py-4 px-4\">\n                    <span className={`font-bold ${getProgressColor(plan.bergBalanceImprovement)}`}>\n                      {plan.bergBalanceImprovement}%\n                    </span>\n                  </td>\n                  <td className=\"text-center py-4 px-4\">\n                    <span className={`font-bold ${getProgressColor(plan.tugImprovement)}`}>\n                      {plan.tugImprovement}%\n                    </span>\n                  </td>\n                  <td className=\"text-center py-4 px-4\">\n                    <span className={`font-bold ${getProgressColor(plan.painReduction)}`}>\n                      {plan.painReduction}%\n                    </span>\n                  </td>\n                  <td className=\"text-center py-4 px-4\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      plan.successRate >= 90 ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200' :\n                      plan.successRate >= 80 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200' :\n                      'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'\n                    }`}>\n                      {plan.successRate}%\n                    </span>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Progress Trends and Analysis */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Improvement Areas */}\n        <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-green-900 dark:text-green-100 mb-6 flex items-center\">\n            <i className=\"fas fa-arrow-up text-green-600 dark:text-green-400 mr-2\"></i>\n            {t('improvementAreas', 'Areas of Improvement')}\n          </h2>\n          \n          {analyticsData.improvementAreas.length > 0 ? (\n            <div className=\"space-y-3\">\n              {analyticsData.improvementAreas.map((area, index) => (\n                <div key={index} className=\"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"p-2 bg-green-100 dark:bg-green-900/40 rounded-lg mr-3\">\n                      <i className=\"fas fa-check text-green-600 dark:text-green-400\"></i>\n                    </div>\n                    <div>\n                      <h3 className=\"font-medium text-green-900 dark:text-green-100\">{area}</h3>\n                      <p className=\"text-sm text-green-600 dark:text-green-400\">\n                        {t('showingPositiveProgress', 'Showing positive progress')}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <i className=\"fas fa-chart-line text-4xl text-green-300 dark:text-green-600 mb-4\"></i>\n              <p className=\"text-green-600 dark:text-green-400\">\n                {t('noImprovementData', 'No improvement data available yet')}\n              </p>\n            </div>\n          )}\n        </div>\n\n        {/* Concern Areas */}\n        <div className=\"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-red-200 dark:border-red-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-red-900 dark:text-red-100 mb-6 flex items-center\">\n            <i className=\"fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2\"></i>\n            {t('concernAreas', 'Areas of Concern')}\n          </h2>\n          \n          {analyticsData.concernAreas.length > 0 ? (\n            <div className=\"space-y-3\">\n              {analyticsData.concernAreas.map((area, index) => (\n                <div key={index} className=\"bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg p-4\">\n                  <div className=\"flex items-center\">\n                    <div className=\"p-2 bg-red-100 dark:bg-red-900/40 rounded-lg mr-3\">\n                      <i className=\"fas fa-exclamation text-red-600 dark:text-red-400\"></i>\n                    </div>\n                    <div>\n                      <h3 className=\"font-medium text-red-900 dark:text-red-100\">{area}</h3>\n                      <p className=\"text-sm text-red-600 dark:text-red-400\">\n                        {t('requiresAttention', 'Requires attention')}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <i className=\"fas fa-shield-alt text-4xl text-green-300 dark:text-green-600 mb-4\"></i>\n              <p className=\"text-green-600 dark:text-green-400\">\n                {t('noConcernAreas', 'No areas of concern identified')}\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Assessment Summary */}\n      <div className=\"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center\">\n          <i className=\"fas fa-clipboard-list text-gray-600 dark:text-gray-400 mr-2\"></i>\n          {t('assessmentSummary', 'Assessment Summary')}\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"p-4 bg-blue-100 dark:bg-blue-900/40 rounded-lg mb-3\">\n              <i className=\"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-3xl\"></i>\n            </div>\n            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('totalAssessments', 'Total Assessments')}</h3>\n            <p className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">{analyticsData.assessmentCount}</p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('inSelectedPeriod', 'In selected period')}</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"p-4 bg-green-100 dark:bg-green-900/40 rounded-lg mb-3\">\n              <i className=\"fas fa-trending-up text-green-600 dark:text-green-400 text-3xl\"></i>\n            </div>\n            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('improvementRate', 'Improvement Rate')}</h3>\n            <p className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n              {analyticsData.improvementAreas.length > 0 ? '85%' : '0%'}\n            </p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('acrossAllMetrics', 'Across all metrics')}</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"p-4 bg-purple-100 dark:bg-purple-900/40 rounded-lg mb-3\">\n              <i className=\"fas fa-award text-purple-600 dark:text-purple-400 text-3xl\"></i>\n            </div>\n            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('complianceScore', 'Compliance Score')}</h3>\n            <p className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">100%</p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('carfCbahiHipaa', 'CARF, CBAHI, HIPAA')}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Metric Detail Modal */}\n      {selectedMetric && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                  {selectedMetric === 'bergBalance' && t('bergBalanceDetails', 'Berg Balance Scale Details')}\n                  {selectedMetric === 'tugTest' && t('tugTestDetails', 'Timed Up & Go Test Details')}\n                  {selectedMetric === 'painLevel' && t('painLevelDetails', 'Pain Level Details')}\n                  {selectedMetric === 'functionalIndependence' && t('functionalIndependenceDetails', 'Functional Independence Details')}\n                  {selectedMetric === 'overallProgress' && t('overallProgressDetails', 'Overall Progress Details')}\n                </h3>\n                <button\n                  onClick={() => setSelectedMetric(null)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-times text-xl\"></i>\n                </button>\n              </div>\n\n              <div className=\"space-y-6\">\n                {selectedMetric === 'bergBalance' && (\n                  <div>\n                    <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-4\">\n                      <h4 className=\"font-semibold text-blue-900 dark:text-blue-100 mb-2\">\n                        {t('currentScore', 'Current Score')}\n                      </h4>\n                      <p className=\"text-3xl font-bold text-blue-600 dark:text-blue-400\">\n                        {analyticsData.currentStatus?.bergBalance?.score || 0}/56\n                      </p>\n                      <p className=\"text-sm text-blue-600 dark:text-blue-400 mt-1\">\n                        Risk Level: {analyticsData.currentStatus?.bergBalance?.riskLevel}\n                      </p>\n                    </div>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">Score Interpretation:</h5>\n                        <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n                          <li>• 41-56: Low fall risk</li>\n                          <li>• 21-40: Medium fall risk</li>\n                          <li>• 0-20: High fall risk</li>\n                        </ul>\n                      </div>\n                      <div>\n                        <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">Progress Trend:</h5>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          Showing improvement over the last 3 assessments\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {selectedMetric === 'tugTest' && (\n                  <div>\n                    <div className=\"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mb-4\">\n                      <h4 className=\"font-semibold text-green-900 dark:text-green-100 mb-2\">\n                        {t('currentTime', 'Current Time')}\n                      </h4>\n                      <p className=\"text-3xl font-bold text-green-600 dark:text-green-400\">\n                        {analyticsData.currentStatus?.tugTest?.time || 0} seconds\n                      </p>\n                      <p className=\"text-sm text-green-600 dark:text-green-400 mt-1\">\n                        Risk Level: {analyticsData.currentStatus?.tugTest?.riskLevel}\n                      </p>\n                    </div>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      <div>\n                        <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">Time Interpretation:</h5>\n                        <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n                          <li>• ≤10 seconds: Normal</li>\n                          <li>• 11-14 seconds: Mild impairment</li>\n                          <li>• 15-20 seconds: Moderate impairment</li>\n                          <li>• >20 seconds: Severe impairment</li>\n                        </ul>\n                      </div>\n                      <div>\n                        <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">Improvement:</h5>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          6.7 seconds improvement from initial assessment\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {selectedMetric === 'functionalIndependence' && (\n                  <div>\n                    <div className=\"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 mb-4\">\n                      <h4 className=\"font-semibold text-orange-900 dark:text-orange-100 mb-2\">\n                        {t('currentFimScore', 'Current FIM Score')}\n                      </h4>\n                      <p className=\"text-3xl font-bold text-orange-600 dark:text-orange-400\">\n                        {analyticsData.currentStatus?.functionalIndependence?.total || 0}/126\n                      </p>\n                      <div className=\"grid grid-cols-2 gap-4 mt-3\">\n                        <div>\n                          <p className=\"text-sm text-orange-600 dark:text-orange-400\">Motor Score</p>\n                          <p className=\"font-bold\">{analyticsData.currentStatus?.functionalIndependence?.motor || 0}/91</p>\n                        </div>\n                        <div>\n                          <p className=\"text-sm text-orange-600 dark:text-orange-400\">Cognitive Score</p>\n                          <p className=\"font-bold\">{analyticsData.currentStatus?.functionalIndependence?.cognitive || 0}/35</p>\n                        </div>\n                      </div>\n                    </div>\n                    <div>\n                      <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">Independence Levels:</h5>\n                      <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n                        <li>• 108-126: Complete independence</li>\n                        <li>• 90-107: Modified independence</li>\n                        <li>• 72-89: Minimal assistance</li>\n                        <li>• 54-71: Moderate assistance</li>\n                        <li>• 36-53: Maximal assistance</li>\n                        <li>• 18-35: Total assistance</li>\n                      </ul>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              <div className=\"mt-6 flex justify-end\">\n                <button\n                  onClick={() => setSelectedMetric(null)}\n                  className=\"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\"\n                >\n                  {t('close', 'Close')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ClinicalIndicatorsDashboard;\n"], "names": ["ClinicalIndicatorsDashboard", "_analyticsData$curren", "_analyticsData$curren2", "_analyticsData$curren3", "_analyticsData$curren4", "_analyticsData$curren5", "_analyticsData$curren6", "_analyticsData$curren7", "_analyticsData$curren8", "_analyticsData$curren9", "_analyticsData$curren0", "_analyticsData$curren1", "_analyticsData$curren10", "_analyticsData$curren11", "_analyticsData$curren12", "_analyticsData$curren13", "_analyticsData$curren14", "_analyticsData$curren15", "_analyticsData$curren16", "_analyticsData$curren17", "_analyticsData$curren18", "_analyticsData$curren19", "_analyticsData$curren20", "_analyticsData$curren21", "_analyticsData$curren22", "_analyticsData$curren23", "_analyticsData$curren24", "_analyticsData$curren25", "_analyticsData$curren26", "_analyticsData$curren27", "_analyticsData$curren28", "_analyticsData$curren29", "_analyticsData$curren30", "t", "useLanguage", "patientId", "useParams", "navigate", "useNavigate", "loading", "setLoading", "useState", "patientLoading", "setPatientLoading", "patient", "setPatient", "analyticsData", "setAnalyticsData", "progressTrend", "currentStatus", "improvementAreas", "concern<PERSON>reas", "assessmentCount", "functionalIndependenceComparison", "treatmentPlanEffectiveness", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "selectedMetric", "setSelectedMetric", "showDetailModal", "setShowDetailModal", "patients", "setPatients", "searchTerm", "setSearchTerm", "useEffect", "loadPatientData", "loadAnalyticsData", "loadPatientsList", "async", "response", "fetch", "concat", "headers", "localStorage", "getItem", "ok", "result", "json", "data", "_id", "firstName", "lastName", "dateOfBirth", "gender", "phone", "email", "medicalRecordNumber", "primaryCondition", "treatmentPlan", "assignedTherapist", "admissionDate", "specialNeeds", "hasSpecialNeeds", "accommodations", "error", "console", "toast", "date", "bergBalance", "tugTime", "painLevel", "fimScore", "overallProgress", "score", "riskLevel", "tugTest", "time", "current", "average", "functionalIndependence", "total", "motor", "cognitive", "period", "beforeTreatment", "totalScore", "motorScore", "cognitiveScore", "level", "afterTreatment", "improvement", "totalImprovement", "motorImprovement", "cognitiveImprovement", "improvementPercentage", "treatmentDuration", "planType", "patientsCount", "averageImprovement", "bergBalanceImprovement", "tugImprovement", "painReduction", "fimImprovement", "successRate", "getProgressColor", "getRiskLevelColor", "_jsx", "className", "children", "_jsxs", "Array", "map", "_", "i", "filteredPatients", "filter", "toLowerCase", "includes", "onClick", "Date", "toLocaleDateString", "value", "onChange", "e", "target", "plan", "index", "length", "area", "type", "placeholder"], "sourceRoot": ""}