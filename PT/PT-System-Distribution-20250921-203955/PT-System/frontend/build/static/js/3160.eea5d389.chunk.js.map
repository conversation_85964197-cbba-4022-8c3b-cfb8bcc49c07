{"version": 3, "file": "static/js/3160.eea5d389.chunk.js", "mappings": "mMAIA,MA0OA,EA1OkBA,KAChB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACfC,GAAWC,EAAAA,EAAAA,OACVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,GASvC,IAPAC,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAQC,WAAW,KACvBJ,GAAW,IACV,KACH,MAAO,IAAMK,aAAaF,IACzB,IAECJ,EACF,OACEO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2DAA0DC,UACvEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oGACfD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,8BAM7C,MAAME,EAAqBC,IACzB,OAAQA,GACN,IAAK,aACHd,EAAS,iBACT,MACF,IAAK,sBACHA,EAAS,qBACT,MACF,IAAK,mBACHA,EAAS,0BAOf,OACEY,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+DAA8DC,SAAA,EAC3EC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mCAAkCC,SAC7Cb,EAAQ,gEAAgB,eAE3BW,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SACtCb,EAAQ,2NAA8C,wCAG3Dc,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qCAAoCC,SAAA,EACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,UACjC,IAAII,MAAOC,mBAAmB,QAAS,CACtCC,QAAS,OACTC,KAAM,UACNC,MAAO,OACPC,IAAK,eAGTX,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAAqCC,UAC/C,IAAII,MAAOM,mBAAmB,QAAS,CACtCC,KAAM,UACNC,OAAQ,uBAOlBX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oEAAmEC,UAChFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAwBc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC1FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,mIAI3ElB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,oBACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAC,WAChDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BAA6BC,SAAC,yCAKjDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qEAAoEC,UACjFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAyBc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC3FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,uIAI3ElB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,sBACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAC,QAChDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BAA6BC,SAAC,uCAKjDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAA0Bc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC5FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,kGAI3ElB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,0BACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAC,QAChDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAA4BC,SAAC,wBAKhDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6FAA4FC,UACzGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAA0Bc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC5FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,0CAI3ElB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCAAmCC,SAAC,aACjDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAC,cAChDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BAA6BC,SAAC,2CAOnDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2DAA0DC,SAAA,EACvEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2CAA0CC,SAAC,mBACzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,UACEmB,QAASA,IAAMlB,EAAkB,cACjCH,UAAU,+FAA8FC,SAAA,EAExGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8GAA6GC,UAC1HF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAwBc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC1FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,oCAGzErB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAC,wBAGnDC,EAAAA,EAAAA,MAAA,UACEmB,QAASA,IAAMlB,EAAkB,uBACjCH,UAAU,iGAAgGC,SAAA,EAE1GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gHAA+GC,UAC5HF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAyBc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC3FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,gGAGzErB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAC,6BAGnDC,EAAAA,EAAAA,MAAA,UACEmB,QAASA,IAAMlB,EAAkB,oBACjCH,UAAU,mGAAkGC,SAAA,EAE5GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kHAAiHC,UAC9HF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAA0Bc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC5FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,8HAGzErB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAC,gCAMvDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yEAAwEC,SAAA,EACrFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2CAA0CC,SAAC,qBACzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAAqEC,UAClFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAwBc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC1FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,6EAGzElB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAC,4BACzCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,sCAEvCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,cAGzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uEAAsEC,UACnFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAyBc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC3FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,wBAGzElB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAC,iCACzCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,qCAEvCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,cAGzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kFAAiFC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wEAAuEC,UACpFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAA0Bc,KAAK,OAAOC,OAAO,eAAeC,QAAQ,YAAWf,UAC5FF,EAAAA,EAAAA,KAAA,QAAMkB,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,gGAGzElB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAC,2BACzCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAAuBC,SAAC,wCAEvCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SAAC,yB", "sources": ["pages/Dashboard/Dashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst Dashboard = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 text-lg\">Loading Dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const handleQuickAction = (action) => {\n    switch (action) {\n      case 'addPatient':\n        navigate('/patients/add');\n        break;\n      case 'scheduleAppointment':\n        navigate('/appointments/new');\n        break;\n      case 'createAssessment':\n        navigate('/forms/pt-assessment');\n        break;\n      default:\n        break;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header Section */}\n      <div className=\"bg-white rounded-lg shadow-sm p-6\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">\n              {isRTL ? 'لوحة التحكم' : 'Dashboard'}\n            </h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              {isRTL ? 'مرحباً بك في نظام فيزيوفلو للعلاج الطبيعي' : 'Welcome to PhysioFlow PT System'}\n            </p>\n          </div>\n          <div className=\"mt-4 sm:mt-0 text-sm text-gray-500\">\n            <p className=\"text-sm text-gray-500\">\n              {new Date().toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </p>\n            <p className=\"text-lg font-semibold text-gray-900\">\n              {new Date().toLocaleTimeString('en-US', {\n                hour: '2-digit',\n                minute: '2-digit'\n              })}\n            </p>\n          </div>\n        </div>\n      </div>\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                </svg>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Total Patients</p>\n              <p className=\"text-2xl font-bold text-gray-900\">1,234</p>\n              <p className=\"text-xs text-green-600 mt-1\">↗ +12% from last month</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                </svg>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Active Therapies</p>\n              <p className=\"text-2xl font-bold text-gray-900\">89</p>\n              <p className=\"text-xs text-green-600 mt-1\">↗ +5% from last week</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Today's Appointments</p>\n              <p className=\"text-2xl font-bold text-gray-900\">12</p>\n              <p className=\"text-xs text-blue-600 mt-1\">3 upcoming</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center\">\n                <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                </svg>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600\">Revenue</p>\n              <p className=\"text-2xl font-bold text-gray-900\">$328,000</p>\n              <p className=\"text-xs text-green-600 mt-1\">↗ +8% from last month</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions and Recent Activity */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Quick Actions */}\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">Quick Actions</h3>\n          <div className=\"space-y-4\">\n            <button\n              onClick={() => handleQuickAction('addPatient')}\n              className=\"w-full flex items-center p-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors group\"\n            >\n              <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors\">\n                <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                </svg>\n              </div>\n              <span className=\"ml-3 font-medium text-gray-900\">Add New Patient</span>\n            </button>\n\n            <button\n              onClick={() => handleQuickAction('scheduleAppointment')}\n              className=\"w-full flex items-center p-4 rounded-lg bg-green-50 hover:bg-green-100 transition-colors group\"\n            >\n              <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors\">\n                <svg className=\"w-5 h-5 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <span className=\"ml-3 font-medium text-gray-900\">Schedule Appointment</span>\n            </button>\n\n            <button\n              onClick={() => handleQuickAction('createAssessment')}\n              className=\"w-full flex items-center p-4 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors group\"\n            >\n              <div className=\"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors\">\n                <svg className=\"w-5 h-5 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <span className=\"ml-3 font-medium text-gray-900\">Create Assessment</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">Recent Activity</h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n              <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                <svg className=\"w-5 h-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4 flex-1\">\n                <p className=\"font-medium text-gray-900\">New patient registered</p>\n                <p className=\"text-sm text-gray-600\">Ahmed Al-Rashid - 2 hours ago</p>\n              </div>\n              <div className=\"text-xs text-gray-400\">14:30</div>\n            </div>\n\n            <div className=\"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n              <div className=\"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center\">\n                <svg className=\"w-5 h-5 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <div className=\"ml-4 flex-1\">\n                <p className=\"font-medium text-gray-900\">Treatment session completed</p>\n                <p className=\"text-sm text-gray-600\">Sarah Al-Harbi - 4 hours ago</p>\n              </div>\n              <div className=\"text-xs text-gray-400\">12:30</div>\n            </div>\n\n            <div className=\"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\">\n              <div className=\"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center\">\n                <svg className=\"w-5 h-5 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <div className=\"ml-4 flex-1\">\n                <p className=\"font-medium text-gray-900\">Appointment scheduled</p>\n                <p className=\"text-sm text-gray-600\">Mohammed Al-Ahmad - 6 hours ago</p>\n              </div>\n              <div className=\"text-xs text-gray-400\">10:30</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "names": ["Dashboard", "t", "isRTL", "useLanguage", "navigate", "useNavigate", "loading", "setLoading", "useState", "useEffect", "timer", "setTimeout", "clearTimeout", "_jsx", "className", "children", "_jsxs", "handleQuickAction", "action", "Date", "toLocaleDateString", "weekday", "year", "month", "day", "toLocaleTimeString", "hour", "minute", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick"], "sourceRoot": ""}