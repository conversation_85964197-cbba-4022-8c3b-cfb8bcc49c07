{"version": 3, "file": "static/js/8922.bfff2ed2.chunk.js", "mappings": "iOAMA,MAscA,EAtc+BA,KAAO,IAADC,EAAAC,EACnC,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,iBACpCG,EAAQC,IAAaJ,EAAAA,EAAAA,UAAS,CAAC,IAE/BK,EAAUC,IAAeN,EAAAA,EAAAA,UAAS,CACvCN,UAAWA,GAAa,GACxBa,eAAgB,WAChBC,gBAAgB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAGpDC,iBAAkB,CAChBC,WAAY,KACZC,UAAW,KACXC,iBAAkB,CAChBC,kBAAmB,KACnBC,oBAAqB,KACrBC,mBAAoB,KACpBC,kBAAmB,KACnBC,UAAW,KACXC,mBAAoB,KACpBC,qBAAsB,KACtBC,gBAAiB,KACjBC,gBAAiB,KACjBC,qBAAsB,KACtBC,kBAAmB,KACnBC,mBAAoB,KACpBC,gBAAiB,KACjBC,eAAgB,MAElBC,MAAO,IAITC,aAAc,CACZC,cAAe,KACflB,UAAW,KACXmB,gBAAiB,OACjBH,MAAO,IAITI,oBAAqB,CACnBC,eAAgB,CACdC,cAAe,CAAEC,QAAS,KAAMC,UAAW,KAAMC,UAAW,KAAMC,UAAW,MAC7EC,aAAc,CAAEJ,QAAS,KAAMC,UAAW,KAAMC,UAAW,KAAMC,UAAW,MAC5EE,WAAY,CAAEL,QAAS,KAAMC,UAAW,MACxCK,UAAW,CAAEN,QAAS,KAAMC,UAAW,MACvCM,WAAY,CAAEP,QAAS,KAAMC,UAAW,MACxCO,UAAW,CAAER,QAAS,KAAMC,UAAW,OAEzCQ,eAAgB,CACdC,SAAU,CAAEV,QAAS,KAAMC,UAAW,KAAMC,UAAW,KAAMC,UAAW,MACxEQ,QAAS,CAAEX,QAAS,KAAMC,UAAW,KAAMC,UAAW,KAAMC,UAAW,MACvES,UAAW,CAAEZ,QAAS,KAAMC,UAAW,MACvCY,SAAU,CAAEb,QAAS,KAAMC,UAAW,MACtCa,WAAY,CAAEC,aAAc,KAAMC,eAAgB,MAClDC,UAAW,CAAEF,aAAc,KAAMC,eAAgB,OAEnDE,MAAO,CACLlB,QAAS,KACTC,UAAW,KACXkB,oBAAqB,KACrBC,mBAAoB,MAEtBC,qBAAsB,KACtB5B,MAAO,IAIT6B,UAAW,CACTC,YAAa,KACbC,eAAgB,KAChBC,aAAc,KACdC,WAAY,KACZC,iBAAkB,KAClBC,aAAc,GACdC,YAAa,GACbC,YAAa,KACbrC,MAAO,IAITsC,8BAA+B,CAC7BC,SAAU,CACRC,OAAQ,KACRC,SAAU,KACVC,QAAS,KACTC,cAAe,KACfC,cAAe,KACfC,UAAW,MAEbC,iBAAkB,CAChBC,QAAS,KACTC,MAAO,MAET1D,UAAW,CACT2D,mBAAoB,KACpBC,OAAQ,KACRC,UAAW,MAEbC,WAAY,CACVC,eAAgB,KAChBC,OAAQ,MAEVC,cAAe,CACbC,cAAe,KACfC,WAAY,MAEdC,gBAAiB,CACfC,kBAAmB,KACnBC,eAAgB,KAChBC,OAAQ,MAEV9E,WAAY,KACZ+E,WAAY,KACZC,eAAgB,KAChB/D,MAAO,IAITgE,cAAe,CACbC,cAAe,CACb1D,QAAS,KACTC,UAAW,KACXkB,oBAAqB,KACrBC,mBAAoB,KACpBuC,cAAe,KACfC,aAAc,MAEhBC,YAAa,CACX7D,QAAS,KACTC,UAAW,KACXkB,oBAAqB,KACrBC,mBAAoB,MAEtB3B,MAAO,IAGTqE,cAAe,GACfC,gBAAiB,GACjBC,mBAAoB,KAGhBC,EAAO,CACX,CAAEC,GAAI,eAAgBC,MAAOnH,EAAE,mBAAoB,sBAAuBoH,KAAM,uBAAwBC,MAAO,QAC/G,CAAEH,GAAI,WAAYC,MAAOnH,EAAE,UAAW,iBAAkBoH,KAAM,iBAAkBC,MAAO,SACvF,CAAEH,GAAI,iBAAkBC,MAAOnH,EAAE,mBAAoB,yBAA0BoH,KAAM,kBAAmBC,MAAO,UAC/G,CAAEH,GAAI,aAAcC,MAAOnH,EAAE,eAAgB,oBAAqBoH,KAAM,mBAAoBC,MAAO,OACnG,CAAEH,GAAI,YAAaC,MAAOnH,EAAE,yBAA0B,iCAAkCoH,KAAM,oBAAqBC,MAAO,UAC1H,CAAEH,GAAI,eAAgBC,MAAOnH,EAAE,gBAAiB,mBAAoBoH,KAAM,oBAAqBC,MAAO,SAGlGC,EAAoB,SAACC,EAASC,EAAOC,GAA4B,IAArBC,EAAQC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAC3D1G,EAAY6G,GACNJ,GACFK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKD,GAAI,IACP,CAACP,IAAOQ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKP,IAAQ,IAChB,CAACC,IAAKO,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDD,EAAKP,GAASC,IAAM,IACvB,CAACE,GAAWD,SAKlBM,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKD,GAAI,IACP,CAACP,IAAOQ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKP,IAAQ,IAChB,CAACC,GAAQC,OAOjB,MAAMO,EAAWN,EAAQ,GAAAO,OAAMV,EAAO,KAAAU,OAAIT,EAAK,KAAAS,OAAIP,GAAQ,GAAAO,OAAQV,EAAO,KAAAU,OAAIT,GAC1E1G,EAAOkH,IACTjH,EAAU+G,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACE,GAAW,OAGlB,EA+HA,OAZAE,EAAAA,EAAAA,WAAU,KAjHwBC,MAChC,MAAMC,EAASpH,EAASO,iBAAiBG,iBACnC2G,EAAQC,OAAOC,OAAOH,GAAQI,OAAO,CAACC,EAAKC,IACxCD,GAAOC,GAAS,GACtB,GAEH,IAAIjH,EAAY,WACZ4G,EAAQ,GAAI5G,EAAY,YACnB4G,EAAQ,KAAI5G,EAAY,iBAEjC6F,EAAkB,mBAAoB,aAAce,GACpDf,EAAkB,mBAAoB,YAAa7F,IAuGnD0G,IACC,CAACnH,EAASO,iBAAiBG,oBAE9BwG,EAAAA,EAAAA,WAAU,KAvGoBS,MAC5B,MAAMC,EAAO5H,EAAS0B,aAAaC,cACnC,IAAKiG,EAAM,OAEX,IAAInH,EAAY,SACZmH,EAAO,GAAInH,EAAY,oBAClBmH,EAAO,GAAInH,EAAY,sBACvBmH,EAAO,KAAInH,EAAY,mBAEhC6F,EAAkB,eAAgB,YAAa7F,IA+F/CkH,IACC,CAAC3H,EAAS0B,aAAaC,iBAE1BuF,EAAAA,EAAAA,WAAU,KA/FiBW,MACzB,MAAMC,EAAM9H,EAAS+D,8BASfwB,EANa,IACd+B,OAAOC,OAAOO,EAAI9D,aAClBsD,OAAOC,OAAOO,EAAIvD,qBAClB+C,OAAOC,OAAOO,EAAI/G,cAClBuG,OAAOC,OAAOO,EAAIjD,aAEO2C,OAAO,CAACC,EAAKC,IAAUD,GAAOC,GAAS,GAAI,GAOnElC,EAJiB,IAClB8B,OAAOC,OAAOO,EAAI9C,kBAClBsC,OAAOC,OAAOO,EAAI3C,kBAEeqC,OAAO,CAACC,EAAKC,IAAUD,GAAOC,GAAS,GAAI,GAE3ElH,EAAa+E,EAAaC,EAEhCc,EAAkB,gCAAiC,aAAcf,GACjEe,EAAkB,gCAAiC,iBAAkBd,GACrEc,EAAkB,gCAAiC,aAAc9F,IAyEjEqH,IACC,CAAC7H,EAAS+D,iCAGXgE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sFAAqFC,UAClGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6GAA4GC,UACzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6GAA4GC,SACvHjJ,EAAE,+BAAgC,qCAErC+I,EAAAA,EAAAA,MAAA,KAAGC,UAAU,kEAAiEC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCACZhJ,EAAE,gCAAiC,iFAGxC+I,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2FAA0FC,SAAA,EACvGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZhJ,EAAE,oBAAqB,mCAQlC+I,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sGAAqGC,SAAA,EAClHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEjJ,EAAE,oBAAqB,yBAG1B+I,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EjJ,EAAE,iBAAkB,mBAAmB,SAE1C+I,EAAAA,EAAAA,MAAA,UACEtB,MAAOzG,EAASE,eAChBiI,SAAWC,GAAMnI,EAAY6G,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE5G,eAAgBkI,EAAEC,OAAO5B,SAC1EuB,UAAU,kIACVM,UAAQ,EAAAL,SAAA,EAERC,EAAAA,EAAAA,KAAA,UAAQzB,MAAM,UAASwB,SAAEjJ,EAAE,oBAAqB,yBAChDkJ,EAAAA,EAAAA,KAAA,UAAQzB,MAAM,WAAUwB,SAAEjJ,EAAE,qBAAsB,0BAClDkJ,EAAAA,EAAAA,KAAA,UAAQzB,MAAM,YAAWwB,SAAEjJ,EAAE,sBAAuB,2BACpDkJ,EAAAA,EAAAA,KAAA,UAAQzB,MAAM,YAAWwB,SAAEjJ,EAAE,qBAAsB,iCAIvD+I,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EjJ,EAAE,iBAAkB,mBAAmB,SAE1CkJ,EAAAA,EAAAA,KAAA,SACEK,KAAK,OACL9B,MAAOzG,EAASG,eAChBgI,SAAWC,GAAMnI,EAAY6G,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE3G,eAAgBiI,EAAEC,OAAO5B,SAC1EuB,UAAU,kIACVM,UAAQ,QAIZP,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EjJ,EAAE,qBAAsB,2BAE3BkJ,EAAAA,EAAAA,KAAA,SACEK,KAAK,OACL9B,MAAOzG,EAASgG,mBAChBmC,SAAWC,GAAMnI,EAAY6G,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEd,mBAAoBoC,EAAEC,OAAO5B,SAC9EuB,UAAU,8IAOlBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6GAA4GC,SAAA,EAEzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClChC,EAAKuC,IAAKC,IACTV,EAAAA,EAAAA,MAAA,UAEEW,QAASA,IAAM7I,EAAa4I,EAAIvC,IAChC8B,UAAS,4EAAAf,OACPrH,IAAc6I,EAAIvC,GAAE,UAAAe,OACNwB,EAAIpC,MAAK,cAAAY,OAAawB,EAAIpC,MAAK,mBAAAY,OAAkBwB,EAAIpC,MAAK,YAAAY,OAAWwB,EAAIpC,MAAK,gBAAAY,OAAewB,EAAIpC,MAAK,WAChH,0HACH4B,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAf,OAAKwB,EAAIrC,KAAI,WACxBqC,EAAItC,QATAsC,EAAIvC,UAgBjBgC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,UAElBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAf,OAAuC,QAAvCnI,EAAKmH,EAAK0C,KAAK3J,GAAKA,EAAEkH,KAAOtG,UAAU,IAAAd,OAAA,EAAlCA,EAAoCsH,KAAI,sDACzD8B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACjC,QADiClJ,EACnEkH,EAAK0C,KAAK3J,GAAKA,EAAEkH,KAAOtG,UAAU,IAAAb,OAAA,EAAlCA,EAAoCoH,SAEvC+B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CjJ,EAAE,wBAAyB,+DAOpC+I,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,MAAA,UACEQ,KAAK,SACLG,QAASA,IAAMnJ,GAAU,GACzByI,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZhJ,EAAE,SAAU,cAGf+I,EAAAA,EAAAA,MAAA,UACEW,QAvLaE,UAGnB,GAFAR,EAAES,iBAhBiBC,MACnB,MAAMC,EAAY,CAAC,EAWnB,OATK/I,EAASX,YACZ0J,EAAU1J,UAAYL,EAAE,oBAAqB,2BAG1CgB,EAASE,iBACZ6I,EAAU7I,eAAiBlB,EAAE,yBAA0B,gCAGzDe,EAAUgJ,GAC+B,IAAlCzB,OAAO0B,KAAKD,GAAWnC,QAMzBkC,GAKL,IACEpJ,GAAW,GAEX,MAAMuJ,GAAclC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACf/G,GAAQ,IACXkJ,WAAgB,OAAJ/J,QAAI,IAAJA,OAAI,EAAJA,EAAM+G,GAClBiD,OAAQ,cAGJC,QAAiBC,MAAM,8BAA+B,CAC1DC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADtC,OAAYuC,aAAaC,QAAQ,WAElDC,KAAMC,KAAKC,UAAUX,KAGvB,IAAIG,EAASS,GAUX,MAAM,IAAIC,MAAM,4CATKV,EAASW,OAC9BC,EAAAA,GAAMC,QAAQjL,EAAE,0BAA2B,4CAGzCO,EADEF,EACO,aAAD4H,OAAc5H,GAEb,qBAKf,CAAE,MAAO6K,GACPC,QAAQD,MAAM,oCAAqCA,GACnDF,EAAAA,GAAME,MAAMlL,EAAE,cAAe,uDAC/B,CAAC,QACCU,GAAW,EACb,MAvCEsK,EAAAA,GAAME,MAAMlL,EAAE,kBAAmB,6CAoL7BoL,SAAU3K,EACVuI,UAAU,6MAA4MC,SAAA,EAEtNC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZvI,EAAUT,EAAE,SAAU,aAAeA,EAAE,iBAAkB,4B", "sources": ["pages/Forms/ClinicalIndicatorsForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst ClinicalIndicatorsForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('berg-balance');\n  const [errors, setErrors] = useState({});\n\n  const [formData, setFormData] = useState({\n    patientId: patientId || '',\n    assessmentType: 'progress',\n    assessmentDate: new Date().toISOString().split('T')[0],\n    \n    // Berg Balance Scale\n    bergBalanceScale: {\n      totalScore: null,\n      riskLevel: null,\n      individualScores: {\n        sittingToStanding: null,\n        standingUnsupported: null,\n        sittingUnsupported: null,\n        standingToSitting: null,\n        transfers: null,\n        standingEyesClosed: null,\n        standingFeetTogether: null,\n        reachingForward: null,\n        pickingUpObject: null,\n        lookingOverShoulders: null,\n        turning360Degrees: null,\n        placingFeetOnStool: null,\n        standingOneFoot: null,\n        standingTandem: null\n      },\n      notes: ''\n    },\n\n    // Timed Up and Go\n    timedUpAndGo: {\n      timeInSeconds: null,\n      riskLevel: null,\n      assistiveDevice: 'none',\n      notes: ''\n    },\n\n    // Manual Muscle Testing\n    manualMuscleTesting: {\n      upperExtremity: {\n        rightShoulder: { flexion: null, extension: null, abduction: null, adduction: null },\n        leftShoulder: { flexion: null, extension: null, abduction: null, adduction: null },\n        rightElbow: { flexion: null, extension: null },\n        leftElbow: { flexion: null, extension: null },\n        rightWrist: { flexion: null, extension: null },\n        leftWrist: { flexion: null, extension: null }\n      },\n      lowerExtremity: {\n        rightHip: { flexion: null, extension: null, abduction: null, adduction: null },\n        leftHip: { flexion: null, extension: null, abduction: null, adduction: null },\n        rightKnee: { flexion: null, extension: null },\n        leftKnee: { flexion: null, extension: null },\n        rightAnkle: { dorsiflexion: null, plantarflexion: null },\n        leftAnkle: { dorsiflexion: null, plantarflexion: null }\n      },\n      trunk: {\n        flexion: null,\n        extension: null,\n        lateralFlexionRight: null,\n        lateralFlexionLeft: null\n      },\n      overallStrengthIndex: null,\n      notes: ''\n    },\n\n    // Pain Scale (VAS)\n    painScale: {\n      currentPain: null,\n      averagePain24h: null,\n      worstPain24h: null,\n      painAtRest: null,\n      painWithActivity: null,\n      painLocation: [],\n      painQuality: [],\n      painPattern: null,\n      notes: ''\n    },\n\n    // Functional Independence Measure\n    functionalIndependenceMeasure: {\n      selfCare: {\n        eating: null,\n        grooming: null,\n        bathing: null,\n        dressingUpper: null,\n        dressingLower: null,\n        toileting: null\n      },\n      sphincterControl: {\n        bladder: null,\n        bowel: null\n      },\n      transfers: {\n        bedChairWheelchair: null,\n        toilet: null,\n        tubShower: null\n      },\n      locomotion: {\n        walkWheelchair: null,\n        stairs: null\n      },\n      communication: {\n        comprehension: null,\n        expression: null\n      },\n      socialCognition: {\n        socialInteraction: null,\n        problemSolving: null,\n        memory: null\n      },\n      totalScore: null,\n      motorScore: null,\n      cognitiveScore: null,\n      notes: ''\n    },\n\n    // Range of Motion\n    rangeOfMotion: {\n      cervicalSpine: {\n        flexion: null,\n        extension: null,\n        lateralFlexionRight: null,\n        lateralFlexionLeft: null,\n        rotationRight: null,\n        rotationLeft: null\n      },\n      lumbarSpine: {\n        flexion: null,\n        extension: null,\n        lateralFlexionRight: null,\n        lateralFlexionLeft: null\n      },\n      notes: ''\n    },\n\n    clinicalNotes: '',\n    recommendations: '',\n    nextAssessmentDate: ''\n  });\n\n  const tabs = [\n    { id: 'berg-balance', label: t('bergBalanceScale', 'Berg Balance Scale'), icon: 'fas fa-balance-scale', color: 'blue' },\n    { id: 'tug-test', label: t('tugTest', 'Timed Up & Go'), icon: 'fas fa-walking', color: 'green' },\n    { id: 'muscle-testing', label: t('muscleTestingMMT', 'Manual Muscle Testing'), icon: 'fas fa-dumbbell', color: 'purple' },\n    { id: 'pain-scale', label: t('painScaleVAS', 'Pain Scale (VAS)'), icon: 'fas fa-heartbeat', color: 'red' },\n    { id: 'fim-scale', label: t('functionalIndependence', 'Functional Independence (FIM)'), icon: 'fas fa-user-check', color: 'orange' },\n    { id: 'range-motion', label: t('rangeOfMotion', 'Range of Motion'), icon: 'fas fa-arrows-alt', color: 'teal' }\n  ];\n\n  const handleInputChange = (section, field, value, subField = null) => {\n    setFormData(prev => {\n      if (subField) {\n        return {\n          ...prev,\n          [section]: {\n            ...prev[section],\n            [field]: {\n              ...prev[section][field],\n              [subField]: value\n            }\n          }\n        };\n      } else {\n        return {\n          ...prev,\n          [section]: {\n            ...prev[section],\n            [field]: value\n          }\n        };\n      }\n    });\n\n    // Clear errors\n    const errorKey = subField ? `${section}.${field}.${subField}` : `${section}.${field}`;\n    if (errors[errorKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [errorKey]: null\n      }));\n    }\n  };\n\n  const calculateBergBalanceTotal = () => {\n    const scores = formData.bergBalanceScale.individualScores;\n    const total = Object.values(scores).reduce((sum, score) => {\n      return sum + (score || 0);\n    }, 0);\n    \n    let riskLevel = 'low-risk';\n    if (total < 21) riskLevel = 'high-risk';\n    else if (total < 41) riskLevel = 'moderate-risk';\n    \n    handleInputChange('bergBalanceScale', 'totalScore', total);\n    handleInputChange('bergBalanceScale', 'riskLevel', riskLevel);\n  };\n\n  const calculateTugRiskLevel = () => {\n    const time = formData.timedUpAndGo.timeInSeconds;\n    if (!time) return;\n    \n    let riskLevel = 'normal';\n    if (time > 30) riskLevel = 'severe-impairment';\n    else if (time > 20) riskLevel = 'moderate-impairment';\n    else if (time > 14) riskLevel = 'mild-impairment';\n    \n    handleInputChange('timedUpAndGo', 'riskLevel', riskLevel);\n  };\n\n  const calculateFimScores = () => {\n    const fim = formData.functionalIndependenceMeasure;\n    \n    // Calculate motor score (13 items)\n    const motorItems = [\n      ...Object.values(fim.selfCare),\n      ...Object.values(fim.sphincterControl),\n      ...Object.values(fim.transfers),\n      ...Object.values(fim.locomotion)\n    ];\n    const motorScore = motorItems.reduce((sum, score) => sum + (score || 0), 0);\n    \n    // Calculate cognitive score (5 items)\n    const cognitiveItems = [\n      ...Object.values(fim.communication),\n      ...Object.values(fim.socialCognition)\n    ];\n    const cognitiveScore = cognitiveItems.reduce((sum, score) => sum + (score || 0), 0);\n    \n    const totalScore = motorScore + cognitiveScore;\n    \n    handleInputChange('functionalIndependenceMeasure', 'motorScore', motorScore);\n    handleInputChange('functionalIndependenceMeasure', 'cognitiveScore', cognitiveScore);\n    handleInputChange('functionalIndependenceMeasure', 'totalScore', totalScore);\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.patientId) {\n      newErrors.patientId = t('patientIdRequired', 'Patient ID is required');\n    }\n    \n    if (!formData.assessmentType) {\n      newErrors.assessmentType = t('assessmentTypeRequired', 'Assessment type is required');\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      const submissionData = {\n        ...formData,\n        assessedBy: user?.id,\n        status: 'completed'\n      };\n\n      const response = await fetch('/api/v1/clinical-indicators', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        toast.success(t('clinicalIndicatorsSaved', 'Clinical indicators saved successfully!'));\n        \n        if (patientId) {\n          navigate(`/patients/${patientId}`);\n        } else {\n          navigate('/forms/submissions');\n        }\n      } else {\n        throw new Error('Failed to save clinical indicators');\n      }\n    } catch (error) {\n      console.error('Error saving clinical indicators:', error);\n      toast.error(t('errorSaving', 'Error saving clinical indicators. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    calculateBergBalanceTotal();\n  }, [formData.bergBalanceScale.individualScores]);\n\n  useEffect(() => {\n    calculateTugRiskLevel();\n  }, [formData.timedUpAndGo.timeInSeconds]);\n\n  useEffect(() => {\n    calculateFimScores();\n  }, [formData.functionalIndependenceMeasure]);\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  {t('clinicalIndicatorsAssessment', 'Clinical Indicators Assessment')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-chart-line text-blue-500 mr-2\"></i>\n                  {t('clinicalIndicatorsDescription', 'Comprehensive clinical assessment using standardized measurement tools')}\n                </p>\n              </div>\n              <div className=\"bg-gradient-to-r from-blue-400 to-purple-400 text-white px-4 py-2 rounded-full shadow-lg\">\n                <i className=\"fas fa-stethoscope mr-2\"></i>\n                {t('medicalAssessment', 'Medical Assessment')}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Assessment Type Selection */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('assessmentDetails', 'Assessment Details')}\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('assessmentType', 'Assessment Type')} *\n            </label>\n            <select\n              value={formData.assessmentType}\n              onChange={(e) => setFormData(prev => ({ ...prev, assessmentType: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              required\n            >\n              <option value=\"initial\">{t('initialAssessment', 'Initial Assessment')}</option>\n              <option value=\"progress\">{t('progressAssessment', 'Progress Assessment')}</option>\n              <option value=\"discharge\">{t('dischargeAssessment', 'Discharge Assessment')}</option>\n              <option value=\"follow-up\">{t('followUpAssessment', 'Follow-up Assessment')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('assessmentDate', 'Assessment Date')} *\n            </label>\n            <input\n              type=\"date\"\n              value={formData.assessmentDate}\n              onChange={(e) => setFormData(prev => ({ ...prev, assessmentDate: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              required\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('nextAssessmentDate', 'Next Assessment Date')}\n            </label>\n            <input\n              type=\"date\"\n              value={formData.nextAssessmentDate}\n              onChange={(e) => setFormData(prev => ({ ...prev, nextAssessmentDate: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Assessment Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden\">\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex overflow-x-auto\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`flex-shrink-0 px-6 py-4 text-sm font-medium border-b-2 transition-colors ${\n                  activeTab === tab.id\n                    ? `border-${tab.color}-500 text-${tab.color}-600 dark:text-${tab.color}-400 bg-${tab.color}-50 dark:bg-${tab.color}-900/20`\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"p-6\">\n          {/* Content will be added in the next part */}\n          <div className=\"text-center py-12\">\n            <i className={`${tabs.find(t => t.id === activeTab)?.icon} text-4xl text-gray-300 dark:text-gray-600 mb-4`}></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {tabs.find(t => t.id === activeTab)?.label}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('assessmentFormContent', 'Assessment form content will be implemented here')}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Form Actions */}\n      <div className=\"mt-8 flex justify-between\">\n        <button\n          type=\"button\"\n          onClick={() => navigate(-1)}\n          className=\"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\"\n        >\n          <i className=\"fas fa-arrow-left mr-2\"></i>\n          {t('cancel', 'Cancel')}\n        </button>\n        \n        <button\n          onClick={handleSubmit}\n          disabled={loading}\n          className=\"px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg\"\n        >\n          <i className=\"fas fa-save mr-2\"></i>\n          {loading ? t('saving', 'Saving...') : t('saveAssessment', 'Save Assessment')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ClinicalIndicatorsForm;\n"], "names": ["ClinicalIndicatorsForm", "_tabs$find", "_tabs$find2", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "loading", "setLoading", "useState", "activeTab", "setActiveTab", "errors", "setErrors", "formData", "setFormData", "assessmentType", "assessmentDate", "Date", "toISOString", "split", "bergBalanceScale", "totalScore", "riskLevel", "individualScores", "sittingToStanding", "standingUnsupported", "sittingUnsupported", "standingToSitting", "transfers", "standingEyesClosed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reachingForward", "pickingUpObject", "lookingOverShoulders", "turning360Degrees", "placingFeetOnStool", "standingOneFoot", "standing<PERSON>and<PERSON>", "notes", "timedUpAndGo", "timeInSeconds", "assistiveDevice", "manualMuscleTesting", "upperExtremity", "rightShoulder", "flexion", "extension", "abduction", "adduction", "leftShoulder", "<PERSON><PERSON><PERSON><PERSON>", "leftElbow", "rightWrist", "leftWrist", "lowerExtremity", "rightHip", "leftHip", "<PERSON><PERSON><PERSON>", "leftKnee", "right<PERSON>nkle", "dorsiflexion", "plantarflexion", "leftAnkle", "trunk", "lateralFlexionRight", "lateralFlexionLeft", "overallStrengthIndex", "painScale", "currentPain", "averagePain24h", "worstPain24h", "painAtRest", "painWithActivity", "painLocation", "painQuality", "painPattern", "functionalIndependenceMeasure", "selfCare", "eating", "grooming", "bathing", "dressingUpper", "dressingLower", "toileting", "sphincterControl", "bladder", "bowel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toilet", "tubShower", "locomotion", "walkWheelchair", "stairs", "communication", "comprehension", "expression", "socialCognition", "socialInteraction", "problemSolving", "memory", "motorScore", "cognitiveScore", "rangeOfMotion", "cervicalSpine", "rotationRight", "rotationLeft", "lumbarSpine", "clinicalNotes", "recommendations", "nextAssessmentDate", "tabs", "id", "label", "icon", "color", "handleInputChange", "section", "field", "value", "subField", "arguments", "length", "undefined", "prev", "_objectSpread", "<PERSON><PERSON><PERSON>", "concat", "useEffect", "calculateBergBalanceTotal", "scores", "total", "Object", "values", "reduce", "sum", "score", "calculateTugRiskLevel", "time", "calculateFimScores", "fim", "_jsxs", "className", "children", "_jsx", "onChange", "e", "target", "required", "type", "map", "tab", "onClick", "find", "async", "preventDefault", "validateForm", "newErrors", "keys", "submissionData", "assessedBy", "status", "response", "fetch", "method", "headers", "localStorage", "getItem", "body", "JSON", "stringify", "ok", "Error", "json", "toast", "success", "error", "console", "disabled"], "sourceRoot": ""}