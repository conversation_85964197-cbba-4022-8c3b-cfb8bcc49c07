"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1188],{1188:(e,r,a)=>{a.r(r),a.d(r,{default:()=>i});var t=a(2555),s=a(5043),l=a(7921),d=a(579);const i=()=>{const{t:e}=(0,l.o)(),[r,a]=(0,s.useState)({autoSave:!0,pdfGeneration:!0,emailNotifications:!0,dataRetention:"7years",defaultLanguage:"en",requireSignature:!0,allowDrafts:!0,auditTrail:!0,backupFrequency:"daily",encryptionLevel:"high"}),[i,n]=(0,s.useState)("general"),o=(e,r)=>{a(a=>(0,t.A)((0,t.A)({},a),{},{[e]:r}))},c=[{id:"general",label:e("general","General"),icon:"fas fa-cog"},{id:"security",label:e("security","Security"),icon:"fas fa-shield-alt"},{id:"notifications",label:e("notifications","Notifications"),icon:"fas fa-bell"},{id:"backup",label:e("backup","Backup & Recovery"),icon:"fas fa-database"}];return(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8",children:(0,d.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,d.jsx)("div",{className:"px-6 py-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:e("formSettings","Form Settings")}),(0,d.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-cog text-indigo-500 mr-2"}),e("formSettingsDescription","Configure form behavior, security, and system preferences")]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-indigo-400 to-purple-400 text-white px-4 py-2 rounded-full shadow-lg",children:[(0,d.jsx)("i",{className:"fas fa-tools mr-2"}),e("settings","Settings")]})]})})})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4",children:(0,d.jsx)("nav",{className:"space-y-2",children:c.map(e=>(0,d.jsxs)("button",{onClick:()=>n(e.id),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 ".concat(i===e.id?"bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:[(0,d.jsx)("i",{className:"".concat(e.icon," mr-3")}),e.label]},e.id))})})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:["general"===i&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-cog text-indigo-500 mr-2"}),e("generalSettings","General Settings")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4",children:(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:e("autoSave","Auto Save")}),(0,d.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:e("autoSaveDesc","Automatically save form progress")})]}),(0,d.jsx)("input",{type:"checkbox",checked:r.autoSave,onChange:e=>o("autoSave",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded"})]})}),(0,d.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4",children:(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-green-900 dark:text-green-100",children:e("pdfGeneration","PDF Generation")}),(0,d.jsx)("p",{className:"text-xs text-green-600 dark:text-green-400",children:e("pdfGenerationDesc","Enable PDF export functionality")})]}),(0,d.jsx)("input",{type:"checkbox",checked:r.pdfGeneration,onChange:e=>o("pdfGeneration",e.target.checked),className:"h-4 w-4 text-green-600 focus:ring-green-500 border-green-300 rounded"})]})}),(0,d.jsx)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4",children:(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-purple-900 dark:text-purple-100",children:e("allowDrafts","Allow Drafts")}),(0,d.jsx)("p",{className:"text-xs text-purple-600 dark:text-purple-400",children:e("allowDraftsDesc","Save incomplete forms as drafts")})]}),(0,d.jsx)("input",{type:"checkbox",checked:r.allowDrafts,onChange:e=>o("allowDrafts",e.target.checked),className:"h-4 w-4 text-purple-600 focus:ring-purple-500 border-purple-300 rounded"})]})}),(0,d.jsx)("div",{className:"bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-orange-900 dark:text-orange-100 mb-2",children:e("defaultLanguage","Default Language")}),(0,d.jsxs)("select",{value:r.defaultLanguage,onChange:e=>o("defaultLanguage",e.target.value),className:"w-full px-3 py-2 border border-orange-300 dark:border-orange-600 rounded-lg bg-white dark:bg-orange-700 text-orange-900 dark:text-orange-100",children:[(0,d.jsx)("option",{value:"en",children:"English"}),(0,d.jsx)("option",{value:"ar",children:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"})]})]})})]})]}),"security"===i&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-shield-alt text-red-500 mr-2"}),e("securitySettings","Security Settings")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4",children:(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-red-900 dark:text-red-100",children:e("requireSignature","Require Digital Signature")}),(0,d.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400",children:e("requireSignatureDesc","Mandate digital signatures for forms")})]}),(0,d.jsx)("input",{type:"checkbox",checked:r.requireSignature,onChange:e=>o("requireSignature",e.target.checked),className:"h-4 w-4 text-red-600 focus:ring-red-500 border-red-300 rounded"})]})}),(0,d.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4",children:(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-yellow-900 dark:text-yellow-100",children:e("auditTrail","Audit Trail")}),(0,d.jsx)("p",{className:"text-xs text-yellow-600 dark:text-yellow-400",children:e("auditTrailDesc","Track all form modifications")})]}),(0,d.jsx)("input",{type:"checkbox",checked:r.auditTrail,onChange:e=>o("auditTrail",e.target.checked),className:"h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-yellow-300 rounded"})]})}),(0,d.jsx)("div",{className:"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-indigo-900 dark:text-indigo-100 mb-2",children:e("encryptionLevel","Encryption Level")}),(0,d.jsxs)("select",{value:r.encryptionLevel,onChange:e=>o("encryptionLevel",e.target.value),className:"w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100",children:[(0,d.jsx)("option",{value:"standard",children:"Standard (AES-128)"}),(0,d.jsx)("option",{value:"high",children:"High (AES-256)"}),(0,d.jsx)("option",{value:"maximum",children:"Maximum (AES-256 + RSA)"})]})]})}),(0,d.jsx)("div",{className:"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-700 rounded-lg p-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-teal-900 dark:text-teal-100 mb-2",children:e("dataRetention","Data Retention Period")}),(0,d.jsxs)("select",{value:r.dataRetention,onChange:e=>o("dataRetention",e.target.value),className:"w-full px-3 py-2 border border-teal-300 dark:border-teal-600 rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100",children:[(0,d.jsx)("option",{value:"1year",children:"1 Year"}),(0,d.jsx)("option",{value:"3years",children:"3 Years"}),(0,d.jsx)("option",{value:"5years",children:"5 Years"}),(0,d.jsx)("option",{value:"7years",children:"7 Years"}),(0,d.jsx)("option",{value:"indefinite",children:"Indefinite"})]})]})})]})]}),"notifications"===i&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-bell text-yellow-500 mr-2"}),e("notificationSettings","Notification Settings")]}),(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4",children:(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:e("emailNotifications","Email Notifications")}),(0,d.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:e("emailNotificationsDesc","Send email alerts for form submissions")})]}),(0,d.jsx)("input",{type:"checkbox",checked:r.emailNotifications,onChange:e=>o("emailNotifications",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded"})]})})})]}),"backup"===i&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-database text-green-500 mr-2"}),e("backupSettings","Backup & Recovery Settings")]}),(0,d.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-green-900 dark:text-green-100 mb-2",children:e("backupFrequency","Backup Frequency")}),(0,d.jsxs)("select",{value:r.backupFrequency,onChange:e=>o("backupFrequency",e.target.value),className:"w-full px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100",children:[(0,d.jsx)("option",{value:"hourly",children:"Hourly"}),(0,d.jsx)("option",{value:"daily",children:"Daily"}),(0,d.jsx)("option",{value:"weekly",children:"Weekly"}),(0,d.jsx)("option",{value:"monthly",children:"Monthly"})]})]})})]}),(0,d.jsx)("div",{className:"mt-8 flex justify-end",children:(0,d.jsxs)("button",{onClick:()=>{alert(e("settingsSaved","Settings saved successfully!"))},className:"px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),e("saveSettings","Save Settings")]})})]})})]})]})}}}]);
//# sourceMappingURL=1188.91dba9ce.chunk.js.map