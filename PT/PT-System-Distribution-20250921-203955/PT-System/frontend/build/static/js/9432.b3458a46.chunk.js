"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9432],{3091:(e,a,t)=>{t.d(a,{RS:()=>r});const r={PATIENT_CARE:{id:"PC",title:"Patient Care Standards",description:"Standards for comprehensive patient care and safety",requirements:["Patient assessment and care planning","Medication management and safety","Infection prevention and control","Patient rights and responsibilities","Pain assessment and management","Patient education and discharge planning"]},PATIENT_SAFETY:{id:"PS",title:"Patient Safety Standards",description:"Standards for ensuring patient safety throughout care",requirements:["Patient identification protocols","Fall prevention programs","Medication error prevention","Healthcare-associated infection prevention","Safe surgery protocols","Emergency response procedures"]},QUALITY_MANAGEMENT:{id:"QM",title:"Quality Management Standards",description:"Standards for quality improvement and management",requirements:["Quality improvement programs","Performance measurement and monitoring","Risk management systems","Patient satisfaction monitoring","Clinical outcome measurement","Continuous quality improvement"]},HUMAN_RESOURCES:{id:"HR",title:"Human Resources Standards",description:"Standards for healthcare workforce management",requirements:["Staff competency assessment","Continuing education programs","Professional development","Performance evaluation","Credentialing and privileging","Workplace safety"]},INFORMATION_MANAGEMENT:{id:"IM",title:"Information Management Standards",description:"Standards for healthcare information systems",requirements:["Medical record management","Data security and privacy","Information system reliability","Data backup and recovery","Electronic health records","Health information exchange"]},GOVERNANCE:{id:"GL",title:"Governance and Leadership Standards",description:"Standards for organizational governance",requirements:["Organizational structure","Leadership accountability","Strategic planning","Resource allocation","Policy development","Compliance monitoring"]},FACILITY_MANAGEMENT:{id:"FM",title:"Facility Management Standards",description:"Standards for healthcare facility operations",requirements:["Facility safety and security","Environmental controls","Equipment management","Emergency preparedness","Waste management","Utility systems"]}}},9432:(e,a,t)=>{t.r(a),t.d(a,{default:()=>l});var r=t(5043),s=t(7921),n=t(3091),i=t(579);const d=()=>{const{t:e}=(0,s.o)(),[a,t]=(0,r.useState)(null),[d,l]=(0,r.useState)({}),[c,o]=(0,r.useState)(!0);(0,r.useEffect)(()=>{setTimeout(()=>{l({overallScore:92,lastAssessment:"2024-02-10",nextAssessment:"2024-08-10",standards:{PATIENT_CARE:{score:95,status:"compliant",lastReview:"2024-02-01"},PATIENT_SAFETY:{score:88,status:"compliant",lastReview:"2024-02-05"},QUALITY_MANAGEMENT:{score:90,status:"compliant",lastReview:"2024-01-28"},HUMAN_RESOURCES:{score:94,status:"compliant",lastReview:"2024-02-03"},INFORMATION_MANAGEMENT:{score:96,status:"compliant",lastReview:"2024-02-08"},GOVERNANCE:{score:89,status:"compliant",lastReview:"2024-01-30"},FACILITY_MANAGEMENT:{score:91,status:"compliant",lastReview:"2024-02-02"}}}),o(!1)},1e3)},[]);const m=e=>{switch(e){case"compliant":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200";case"non-compliant":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200";case"pending":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"}},x=e=>e>=90?"text-green-600 dark:text-green-400":e>=80?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400";return c?(0,i.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("cbahiComplianceDashboard","CBAHI Compliance Dashboard")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("cbahiDashboardDescription","Central Board for Accreditation of Healthcare Institutions compliance monitoring")})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full",children:[(0,i.jsx)("i",{className:"fas fa-shield-alt text-green-600 dark:text-green-400"}),(0,i.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"CBAHI Accredited"})]})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center",children:(0,i.jsx)("i",{className:"fas fa-chart-line text-green-600 dark:text-green-400 text-xl"})})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("overallScore","Overall Score")}),(0,i.jsxs)("p",{className:"text-2xl font-bold ".concat(x(d.overallScore)),children:[d.overallScore,"%"]})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center",children:(0,i.jsx)("i",{className:"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-xl"})})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("lastAssessment","Last Assessment")}),(0,i.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:new Date(d.lastAssessment).toLocaleDateString()})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center",children:(0,i.jsx)("i",{className:"fas fa-clock text-orange-600 dark:text-orange-400 text-xl"})})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("nextAssessment","Next Assessment")}),(0,i.jsx)("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:new Date(d.nextAssessment).toLocaleDateString()})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("div",{className:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center",children:(0,i.jsx)("i",{className:"fas fa-award text-purple-600 dark:text-purple-400 text-xl"})})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("accreditationStatus","Accreditation Status")}),(0,i.jsx)("p",{className:"text-lg font-semibold text-green-600 dark:text-green-400",children:e("accredited","Accredited")})]})]})})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,i.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("cbahiStandardsCompliance","CBAHI Standards Compliance")})}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:Object.entries(n.RS).map(r=>{let[s,n]=r;const l=d.standards[s];return(0,i.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors",onClick:()=>t(a===s?null:s),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("i",{className:"fas fa-shield-alt text-green-600 dark:text-green-400"}),(0,i.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:n.title})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(m(null===l||void 0===l?void 0:l.status)),children:(null===l||void 0===l?void 0:l.status)||"pending"}),(0,i.jsxs)("span",{className:"text-lg font-bold ".concat(x((null===l||void 0===l?void 0:l.score)||0)),children:[(null===l||void 0===l?void 0:l.score)||0,"%"]})]})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:n.description}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-500",children:[e("lastReview","Last Review"),": ",null!==l&&void 0!==l&&l.lastReview?new Date(l.lastReview).toLocaleDateString():"N/A"]}),a===s&&(0,i.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600",children:[(0,i.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:[e("requirements","Requirements"),":"]}),(0,i.jsx)("ul",{className:"space-y-1",children:n.requirements.map((e,a)=>(0,i.jsxs)("li",{className:"text-sm text-gray-600 dark:text-gray-400 flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-2 text-xs"}),e]},a))})]})]},s)})})})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,i.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("actionItems","Action Items")})}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium text-yellow-800 dark:text-yellow-200",children:e("updatePolicies","Update Patient Safety Policies")}),(0,i.jsx)("p",{className:"text-sm text-yellow-600 dark:text-yellow-400",children:e("dueDateMarch","Due: March 15, 2024")})]})]}),(0,i.jsx)("button",{className:"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors",children:e("review","Review")})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("i",{className:"fas fa-info-circle text-blue-600 dark:text-blue-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"font-medium text-blue-800 dark:text-blue-200",children:e("staffTraining","Complete Staff Training Module")}),(0,i.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:e("dueDateApril","Due: April 1, 2024")})]})]}),(0,i.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e("start","Start")})]})]})})]})]})},l=()=>(0,i.jsx)(d,{})}}]);
//# sourceMappingURL=9432.b3458a46.chunk.js.map