{"version": 3, "file": "static/js/7688.36b69eb0.chunk.js", "mappings": "yLAGA,MAo6BA,EAp6BgBA,KACd,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,aACpCC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,UACpCG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAS,OAC9CK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,CAAC,IACrCO,EAASC,IAAcR,EAAAA,EAAAA,WAAS,GAGjCS,EAAa,CACjBC,SAAU,CACRC,cAAe,GACfC,eAAgB,GAChBC,kBAAmB,IACnBC,gBAAiB,GACjBC,cAAe,GACfC,iBAAkB,IAEpBC,aAAc,CACZ,CAAEC,UAAW,SAAUC,MAAO,GAAIC,WAAY,GAAIC,MAAO,eACzD,CAAEH,UAAW,iBAAkBC,MAAO,GAAIC,WAAY,GAAIC,MAAO,gBACjE,CAAEH,UAAW,gBAAiBC,MAAO,EAAGC,WAAY,GAAIC,MAAO,iBAC/D,CAAEH,UAAW,0BAA2BC,MAAO,EAAGC,WAAY,GAAIC,MAAO,iBACzE,CAAEH,UAAW,QAASC,MAAO,EAAGC,WAAY,EAAGC,MAAO,gBAExDC,qBAAsB,CACpB,CAAEC,KAAM,sBAAuBC,SAAU,GAAIC,SAAU,GAAIC,aAAc,GAAIC,SAAU,IACvF,CAAEJ,KAAM,wBAAyBC,SAAU,GAAIC,SAAU,GAAIC,aAAc,GAAIC,SAAU,IACzF,CAAEJ,KAAM,sBAAuBC,SAAU,EAAGC,SAAU,GAAIC,aAAc,GAAIC,SAAU,IACtF,CAAEJ,KAAM,oBAAqBC,SAAU,EAAGC,SAAU,GAAIC,aAAc,GAAIC,SAAU,IACpF,CAAEJ,KAAM,sBAAuBC,SAAU,EAAGC,SAAU,GAAIC,aAAc,GAAIC,SAAU,KAExFC,cAAe,CACb,CAAEC,MAAO,MAAOJ,SAAU,IAAKD,SAAU,GAAIG,SAAU,IACvD,CAAEE,MAAO,MAAOJ,SAAU,IAAKD,SAAU,GAAIG,SAAU,IACvD,CAAEE,MAAO,MAAOJ,SAAU,IAAKD,SAAU,GAAIG,SAAU,IACvD,CAAEE,MAAO,MAAOJ,SAAU,IAAKD,SAAU,GAAIG,SAAU,IACvD,CAAEE,MAAO,MAAOJ,SAAU,IAAKD,SAAU,GAAIG,SAAU,IACvD,CAAEE,MAAO,MAAOJ,SAAU,IAAKD,SAAU,GAAIG,SAAU,MAIrDG,EAAkB,CACtB,CACEC,GAAI,EACJR,KAAM,0BACNS,OAAQ,+FACRC,YAAa,mDACbC,cAAe,mKACfC,KAAM,oBACNC,SAAU,WACVC,UAAW,WAEb,CACEN,GAAI,EACJR,KAAM,+BACNS,OAAQ,iHACRC,YAAa,oDACbC,cAAe,sMACfC,KAAM,iBACNC,SAAU,cACVC,UAAW,aAEb,CACEN,GAAI,EACJR,KAAM,yBACNS,OAAQ,mIACRC,YAAa,sDACbC,cAAe,0NACfC,KAAM,sBACNC,SAAU,gBACVC,UAAW,WAEb,CACEN,GAAI,EACJR,KAAM,oBACNS,OAAQ,4EACRC,YAAa,+BACbC,cAAe,+IACfC,KAAM,qBACNC,SAAU,YACVC,UAAW,WAEb,CACEN,GAAI,EACJR,KAAM,wBACNS,OAAQ,8FACRC,YAAa,iDACbC,cAAe,4JACfC,KAAM,sBACNC,SAAU,eACVC,UAAW,UAEb,CACEN,GAAI,EACJR,KAAM,0BACNS,OAAQ,4EACRC,YAAa,2CACbC,cAAe,8HACfC,KAAM,mBACNC,SAAU,YACVC,UAAW,cAITC,EAAO,CACX,CAAEP,GAAI,WAAYQ,MAAO5C,EAAE,WAAY,YAAawC,KAAM,oBAC1D,CAAEJ,GAAI,YAAaQ,MAAO5C,EAAE,kBAAmB,oBAAqBwC,KAAM,mBAC1E,CAAEJ,GAAI,YAAaQ,MAAO5C,EAAE,YAAa,aAAcwC,KAAM,oBAC7D,CAAEJ,GAAI,SAAUQ,MAAO5C,EAAE,gBAAiB,kBAAmBwC,KAAM,iBASrE,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAC,OAAS9C,EAAQ,cAAgB,gBAAiB+C,SAAA,EAE9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DhD,EAAE,UAAW,cAEhBiD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5ChD,EAAE,cAAe,2CAGtB6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BH,EAAAA,EAAAA,MAAA,UACEK,MAAO5C,EACP6C,SAAWC,GAAM7C,EAAa6C,EAAEC,OAAOH,OACvCJ,UAAU,2HAA0HE,SAAA,EAEpIC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,OAAMF,SAAEhD,EAAE,WAAY,gBACpCiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,QAAOF,SAAEhD,EAAE,YAAa,iBACtCiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,UAASF,SAAEhD,EAAE,cAAe,mBAC1CiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,OAAMF,SAAEhD,EAAE,WAAY,mBAEtC6C,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oGAAmGE,SAAA,EACnHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,yBACZ9C,EAAE,aAAc,yBAMvBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBE,SACnCL,EAAKW,IAAKC,IACTV,EAAAA,EAAAA,MAAA,UAEEW,QAASA,IAAMpD,EAAamD,EAAInB,IAChCU,UAAS,0FAAAC,OACP5C,IAAcoD,EAAInB,GACd,mDACA,0HACHY,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAWS,EAAIf,QAClBS,EAAAA,EAAAA,KAAA,QAAAD,SAAOO,EAAIX,UATNW,EAAInB,SAgBF,aAAdjC,IACC0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sEAAqEE,SAAA,EAClFC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEhD,EAAE,gBAAiB,qBACxFiD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAAElC,EAAWC,SAASC,yBAK3FiC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEhD,EAAE,iBAAkB,sBACzFiD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAAElC,EAAWC,SAASE,0BAK3FgC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,0EAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEhD,EAAE,oBAAqB,yBAC5FiD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAAElC,EAAWC,SAASG,6BAK3F+B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEhD,EAAE,kBAAmB,uBAC1F6C,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mDAAkDE,SAAA,CAAElC,EAAWC,SAASI,gBAAgB,gBAK3G8B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEhD,EAAE,gBAAiB,qBACxF6C,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mDAAkDE,SAAA,CAAC,IAAElC,EAAWC,SAASK,cAAc,gBAK1G6B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEhD,EAAE,mBAAoB,wBAC3F6C,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mDAAkDE,SAAA,CAAElC,EAAWC,SAASM,iBAAiB,mBAO9GwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EAEpDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,sBAAuB,wCAE5BiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBlC,EAAWQ,aAAagC,IAAI,CAACG,EAAMC,KAClCb,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,oCAAmCE,SAAA,EAC5DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAS,wBAAAC,OAA0BU,EAAK/B,MAAK,YAClDuB,EAAAA,EAAAA,KAAA,QAAMH,UAAU,oDAAmDE,SAAES,EAAKlC,gBAE5EsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,2CAA0CE,SAAES,EAAKjC,SACjEyB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,OAAKH,UAAS,oBAAAC,OAAsBU,EAAK/B,OAASiC,MAAO,CAAEC,MAAM,GAADb,OAAKU,EAAKhC,WAAU,WAEtFoB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CE,SAAA,CAAES,EAAKhC,WAAW,YAV1EiC,UAkBhBb,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,uBAAwB,4BAE7BiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBlC,EAAWa,qBAAqBkC,MAAM,EAAG,GAAGP,IAAI,CAACQ,EAAWJ,KAC3Db,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,qEAAoEE,SAAA,EAC7FH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,oDAAmDE,SAAEc,EAAUlC,QAC/EiB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CE,SAAA,CAAEc,EAAUjC,SAAS,mBAEjFgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAgCE,SAAA,EAC7CH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCE,SAAA,CAAEhD,EAAE,WAAY,YAAY,SAC9EiD,EAAAA,EAAAA,KAAA,QAAMH,UAAU,4CAA2CE,SAAEc,EAAUhC,eAEzEe,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCE,SAAA,CAAEhD,EAAE,eAAgB,gBAAgB,SACtF6C,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4CAA2CE,SAAA,CAAEc,EAAU/B,aAAa,WAEtFc,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCE,SAAA,CAAEhD,EAAE,WAAY,YAAY,SAC9E6C,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4CAA2CE,SAAA,CAAEc,EAAU9B,SAAS,eAhB5E0B,aA0BlBb,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,gBAAiB,qBAEtBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yBAAwBE,SACpClC,EAAWmB,cAAcqB,IAAI,CAACpB,EAAOwB,KACpCb,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,cAAaE,SAAA,EACtCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yDAAwDE,SAAEd,EAAMA,SAC/EW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtDhD,EAAE,WAAY,YAAY,KAAGkC,EAAMJ,aAEtCe,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtDhD,EAAE,WAAY,YAAY,KAAGkC,EAAML,aAEtCgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtDhD,EAAE,WAAY,YAAY,KAAGkC,EAAMF,SAAS,YAVzC0B,YAqBL,cAAdvD,IACC8C,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,SAClEb,EAAgBmB,IAAKS,IACpBlB,EAAAA,EAAAA,MAAA,OAAuBC,UAAU,8FAA6FE,SAAA,EAC5HH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wDAAuDE,UACpEC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKgB,EAASvB,KAAI,kDAEhCK,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChE/C,EAAQ8D,EAAS1B,OAAS0B,EAASnC,QAEtCqB,EAAAA,EAAAA,KAAA,QAAMH,UAAU,2CAA0CE,SAAEe,EAASrB,mBAIzEO,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gDAA+CE,SACzD/C,EAAQ8D,EAASxB,cAAgBwB,EAASzB,eAG7CO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEO,QAASA,IAlPDQ,KAAc,IAADC,EACnCxD,EAAkBuD,GAElBE,MAAM,GAADnB,OAAI/C,EAAE,aAAc,cAAa,KAAA+C,OAAgD,QAAhDkB,EAAI9B,EAAgBgC,KAAKC,GAAKA,EAAEhC,KAAO4B,UAAS,IAAAC,OAAA,EAA5CA,EAA8CrC,KAAI,SA+O/DyC,CAAeN,EAAS3B,IACvCU,UAAU,iGAAgGE,SAEzGhD,EAAE,WAAY,eAEjBiD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wJAAuJE,UACvKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sBAzBTiB,EAAS3B,OAkCV,cAAdjC,IACC0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,sBAAuB,2BAE5B6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EAEpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnEhD,EAAE,wBAAyB,6BAE9BiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBlC,EAAWQ,aAAagC,IAAI,CAACG,EAAMC,KAClCb,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,oCAAmCE,SAAA,EAC5DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKH,UAAS,mBAAAC,OAAqBU,EAAK/B,UACxCuB,EAAAA,EAAAA,KAAA,QAAMH,UAAU,gCAA+BE,SAAES,EAAKlC,gBAExDsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCE,SAAES,EAAKjC,SACzDyB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,OACEH,UAAS,oBAAAC,OAAsBU,EAAK/B,OACpCiC,MAAO,CAAEC,MAAM,GAADb,OAAKU,EAAKhC,WAAU,WAGtCoB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CE,SAAA,CAAES,EAAKhC,WAAW,YAb1EiC,UAqBhBb,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnEhD,EAAE,kBAAmB,uBAExBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvB,CACC,CAAEsB,MAAO,YAAa9C,MAAO,GAAIC,WAAY,IAC7C,CAAE6C,MAAO,aAAc9C,MAAO,GAAIC,WAAY,IAC9C,CAAE6C,MAAO,cAAe9C,MAAO,GAAIC,WAAY,IAC/C,CAAE6C,MAAO,YAAa9C,MAAO,EAAGC,WAAY,KAC5C6B,IAAI,CAACiB,EAAKb,KACVb,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,oCAAmCE,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,gCAA+BE,SAAEuB,EAAID,SACrDzB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCE,SAAEuB,EAAI/C,SACxDyB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,OACEH,UAAU,+BACVa,MAAO,CAAEC,MAAM,GAADb,OAAKwB,EAAI9C,WAAU,WAGrCoB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CE,SAAA,CAAEuB,EAAI9C,WAAW,YAVzEiC,gBAoBpBb,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,oBAAqB,yBAE1B6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EAEpDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCE,SAAA,EAC9CH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAiC0B,QAAQ,YAAWxB,SAAA,EACjEC,EAAAA,EAAAA,KAAA,QACEwB,EAAE,gFACFC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZ9B,UAAU,sCAEZG,EAAAA,EAAAA,KAAA,QACEwB,EAAE,gFACFC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZC,gBAAgB,UAChB/B,UAAU,uBAGdG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,UAChEC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mDAAkDE,SAAC,cAGvEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CE,SAAEhD,EAAE,cAAe,mBAC5EiD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SAAEhD,EAAE,kBAAmB,0BAIhF6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCE,SAAA,EAC9CH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAiC0B,QAAQ,YAAWxB,SAAA,EACjEC,EAAAA,EAAAA,KAAA,QACEwB,EAAE,gFACFC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZ9B,UAAU,sCAEZG,EAAAA,EAAAA,KAAA,QACEwB,EAAE,gFACFC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZC,gBAAgB,UAChB/B,UAAU,sBAGdG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,UAChEC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mDAAkDE,SAAC,cAGvEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CE,SAAEhD,EAAE,cAAe,mBAC5EiD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SAAEhD,EAAE,qBAAsB,6BAInF6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCE,SAAA,EAC9CH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAiC0B,QAAQ,YAAWxB,SAAA,EACjEC,EAAAA,EAAAA,KAAA,QACEwB,EAAE,gFACFC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZ9B,UAAU,sCAEZG,EAAAA,EAAAA,KAAA,QACEwB,EAAE,gFACFC,KAAK,OACLC,OAAO,eACPC,YAAY,IACZC,gBAAgB,UAChB/B,UAAU,wBAGdG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,UAChEC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mDAAkDE,SAAC,cAGvEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CE,SAAEhD,EAAE,eAAgB,mBAC7EiD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SAAEhD,EAAE,qBAAsB,mCAMvF6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,uBAAwB,4BAE7BiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAAD,UACEH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,gDAA+CE,SAAA,EAC3DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,gEAA+DE,SAC1EhD,EAAE,YAAa,gBAElBiD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kEAAiEE,SAC5EhD,EAAE,WAAY,eAEjBiD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kEAAiEE,SAC5EhD,EAAE,WAAY,eAEjBiD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kEAAiEE,SAC5EhD,EAAE,eAAgB,mBAErBiD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kEAAiEE,SAC5EhD,EAAE,cAAe,wBAIxBiD,EAAAA,EAAAA,KAAA,SAAAD,SACGlC,EAAWa,qBAAqB2B,IAAI,CAACQ,EAAWJ,KAC/Cb,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,gDAA+CE,SAAA,EACvEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,YAAWE,UACvBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wFAAuFE,UACpGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+DAEfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,4CAA2CE,SAAEc,EAAUlC,aAG3EqB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SAAEc,EAAUjC,YAClFoB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SAAEc,EAAUhC,YAClFmB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,wBAAuBE,UACnCH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,EACzDH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gCAA+BE,SAAA,CAAEc,EAAU/B,aAAa,QACxEkB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,OACEH,UAAU,gCACVa,MAAO,CAAEC,MAAM,GAADb,OAAKe,EAAU/B,aAAY,gBAKjDkB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,wBAAuBE,UACnCH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,EACzDH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gCAA+BE,SAAA,CAAEc,EAAU9B,SAAS,QACpEiB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,OACEH,UAAU,+BACVa,MAAO,CAAEC,MAAM,GAADb,OAAKe,EAAU9B,SAAQ,iBA5BtC0B,eAyCnBb,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,gBAAiB,qBAEtBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yBAAwBE,SACpClC,EAAWmB,cAAcqB,IAAI,CAACpB,EAAOwB,KACpCb,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,cAAaE,SAAA,EACtCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yDAAwDE,SAAEd,EAAMA,SAG/EW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gDAA+CE,SAAEhD,EAAE,WAAY,eAC9E6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,OACEH,UAAU,+CACVa,MAAO,CAAEmB,OAAO,GAAD/B,OAAMb,EAAMJ,SAAW,IAAO,IAAG,SAElDmB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,sDAAqDE,UAClEC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,iCAAgCE,SAAEd,EAAMJ,oBAM9De,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtDhD,EAAE,WAAY,YAAY,KAAGkC,EAAMF,SAAS,QAE/CiB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4DAA2DE,UACxEC,EAAAA,EAAAA,KAAA,OACEH,UAAU,gCACVa,MAAO,CAAEC,MAAM,GAADb,OAAKb,EAAMF,SAAQ,YAxB7B0B,YAmCL,WAAdvD,IACC0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChEhD,EAAE,sBAAuB,4BAE5B6C,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oFAAmFE,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZ9C,EAAE,aAAc,sBAIrB6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EAEpDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeE,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3DhD,EAAE,cAAe,mBAEpBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvB,CACC,CAAEZ,GAAI,WAAYQ,MAAO,WAAYJ,KAAM,eAAgBuC,OAAQ,CAAC,OAAQ,MAAO,YAAa,WAChG,CAAE3C,GAAI,aAAcQ,MAAO,aAAcJ,KAAM,mBAAoBuC,OAAQ,CAAC,OAAQ,WAAY,WAAY,cAC5G,CAAE3C,GAAI,WAAYQ,MAAO,WAAYJ,KAAM,kBAAmBuC,OAAQ,CAAC,OAAQ,WAAY,aAAc,UACzG,CAAE3C,GAAI,cAAeQ,MAAO,cAAeJ,KAAM,yBAA0BuC,OAAQ,CAAC,OAAQ,QAAS,OAAQ,UAC7G,CAAE3C,GAAI,QAASQ,MAAO,QAASJ,KAAM,kBAAmBuC,OAAQ,CAAC,QAAS,WAAY,cAAe,YACrGzB,IAAI0B,IACJnC,EAAAA,EAAAA,MAAA,OAAqBC,UAAU,6DAA4DE,SAAA,EACzFH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCE,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKiC,EAAOxC,KAAI,wCAC5BS,EAAAA,EAAAA,KAAA,QAAMH,UAAU,4CAA2CE,SAAEgC,EAAOpC,YAEtEK,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBgC,EAAOD,OAAOzB,IAAI2B,IACjBhC,EAAAA,EAAAA,KAAA,OAEEiC,WAAS,EACTpC,UAAU,wIAAuIE,SAEhJiC,EAAME,QAAQ,IAAK,MAJfF,QARHD,EAAO5C,WAsBvBS,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeE,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3DhD,EAAE,eAAgB,oBAErBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,sFAAqFE,UAClGH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFE,SAAA,EACrGC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3DhD,EAAE,eAAgB,oBAErBiD,EAAAA,EAAAA,KAAA,SACEmC,KAAK,OACLC,YAAarF,EAAE,cAAe,gBAC9B8C,UAAU,0IAEZG,EAAAA,EAAAA,KAAA,YACEoC,YAAarF,EAAE,oBAAqB,sBACpCsF,KAAK,IACLxC,UAAU,wIAKdD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DE,SAAA,EACzEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CE,SACtDhD,EAAE,iBAAkB,sBAEvBiD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,4EAA2EE,UAC3FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+BAGjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAgCE,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,6CAA4CE,UACzDC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCE,SAAC,oBAErDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,6CAA4CE,UACzDC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCE,SAAC,iBAErDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,6CAA4CE,UACzDC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCE,SAAC,WAErDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,6CAA4CE,UACzDC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCE,SAAC,oBAKzDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DE,SAAA,EACzEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CE,SACtDhD,EAAE,oBAAqB,yBAE1BiD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,4EAA2EE,UAC3FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+BAGjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+CAA8CE,UAC3DC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qCAAoCE,SAAC,2BAEvDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+CAA8CE,UAC3DC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qCAAoCE,SAAC,0BAEvDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+CAA8CE,UAC3DC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qCAAoCE,SAAC,4BAM3DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFE,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6CACbG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5ChD,EAAE,iBAAkB,mDAQ/B6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeE,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3DhD,EAAE,iBAAkB,sBAEvB6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DE,SAAA,EACzEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3DhD,EAAE,UAAW,cAEhB6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,sDAAqDE,SACnEhD,EAAE,YAAa,iBAElB6C,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uIAAsIE,SAAA,EACtJC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,OAAMF,SAAEhD,EAAE,WAAY,gBACpCiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,QAAOF,SAAEhD,EAAE,YAAa,iBACtCiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,UAASF,SAAEhD,EAAE,cAAe,mBAC1CiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,OAAMF,SAAEhD,EAAE,WAAY,sBAIxC6C,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,sDAAqDE,SACnEhD,EAAE,YAAa,gBAElB6C,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uIAAsIE,SAAA,EACtJC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,MAAKF,SAAEhD,EAAE,gBAAiB,qBACxCiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,SAAQF,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,iBAAgBF,SAAC,oBAC/BC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,gBAAeF,SAAC,yBAIlCH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,sDAAqDE,SACnEhD,EAAE,YAAa,gBAElB6C,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uIAAsIE,SAAA,EACtJC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,MAAKF,SAAEhD,EAAE,gBAAiB,qBACxCiD,EAAAA,EAAAA,KAAA,UAAQC,MAAM,QAAOF,SAAC,qBACtBC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,QAAOF,SAAC,2BACtBC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,SAAQF,SAAC,qCAO/BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DE,SAAA,EACzEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3DhD,EAAE,aAAc,kBAEnBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yBAAwBE,SACpC,CACC,CAAEoC,KAAM,MAAO5C,KAAM,mBAAoBI,MAAO,aAChD,CAAEwC,KAAM,OAAQ5C,KAAM,oBAAqBI,MAAO,cAClD,CAAEwC,KAAM,MAAO5C,KAAM,mBAAoBI,MAAO,aAChD,CAAEwC,KAAM,QAAS5C,KAAM,eAAgBI,MAAO,UAC9CU,IAAIiC,IACJ1C,EAAAA,EAAAA,MAAA,UAEEC,UAAU,gIAA+HE,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKwC,EAAM/C,KAAI,6CAC3BS,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SAAEuC,EAAM3C,UAJ5D2C,EAAMH,aAWnBvC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DE,SAAA,EACzEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3DhD,EAAE,gBAAiB,qBAEtBiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvB,CAAC,MAAO,QAAS,MAAO,QAAQM,IAAIkC,IACnC3C,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,8BAA6BE,SAAA,EACzDC,EAAAA,EAAAA,KAAA,SAAOmC,KAAK,WAAWtC,UAAU,UAAU2C,eAA2B,QAAXD,KAC3DvC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,2CAA0CE,SAAEwC,MAFlDA,UASlB3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,UAAQC,UAAU,iGAAgGE,SAAA,EAChHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oBACZ9C,EAAE,gBAAiB,sBAEtB6C,EAAAA,EAAAA,MAAA,UAAQC,UAAU,mGAAkGE,SAAA,EAClHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,yBACZ9C,EAAE,iBAAkB,sCASjC6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FE,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEhD,EAAE,qBAAsB,2BAE3BiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,SAClE,CACC,CACEpB,KAAM,2BACNS,OAAQ,qGACRqD,QAAS,aACTC,QAAS,aACTjD,UAAW,WAEb,CACEd,KAAM,4BACNS,OAAQ,+FACRqD,QAAS,aACTC,QAAS,aACTjD,UAAW,UAEb,CACEd,KAAM,+BACNS,OAAQ,uHACRqD,QAAS,aACTC,QAAS,aACTjD,UAAW,cAEbY,IAAI,CAACsC,EAAQlC,KACbb,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,6DAA4DE,SAAA,EACrFC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,iDAAgDE,SAC3D/C,EAAQ2F,EAAOvD,OAASuD,EAAOhE,QAElCiB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0DAAyDE,SAAA,EACtEH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,CAAMhD,EAAE,UAAW,WAAW,KAAG,IAAI6F,KAAKD,EAAOF,SAASI,yBAC1DjD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,CAAMhD,EAAE,UAAW,YAAY,KAAG,IAAI6F,KAAKD,EAAOD,SAASG,yBAC3DjD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,CAAMhD,EAAE,YAAa,aAAa,KAAG4F,EAAOlD,iBAE9CG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,8FAA6FE,SAC5GhD,EAAE,MAAO,UAEZiD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,qJAAoJE,UACpKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mBAEfG,EAAAA,EAAAA,KAAA,UAAQH,UAAU,qJAAoJE,UACpKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wBAjBTY,e", "sources": ["pages/Reports/Reports.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst Reports = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [dateRange, setDateRange] = useState('month');\n  const [selectedReport, setSelectedReport] = useState(null);\n  const [chartData, setChartData] = useState({});\n  const [loading, setLoading] = useState(false);\n\n  // Mock data for reports\n  const reportData = {\n    overview: {\n      totalPatients: 45,\n      activePatients: 38,\n      completedSessions: 156,\n      averageProgress: 72,\n      monthlyGrowth: 12,\n      satisfactionRate: 94\n    },\n    patientStats: [\n      { condition: 'Autism', count: 18, percentage: 40, color: 'bg-blue-500' },\n      { condition: 'Cerebral Palsy', count: 12, percentage: 27, color: 'bg-green-500' },\n      { condition: 'Down Syndrome', count: 8, percentage: 18, color: 'bg-purple-500' },\n      { condition: 'Intellectual Disability', count: 5, percentage: 11, color: 'bg-yellow-500' },\n      { condition: 'Other', count: 2, percentage: 4, color: 'bg-gray-500' }\n    ],\n    therapistPerformance: [\n      { name: 'Dr. <PERSON> Al-<PERSON>', patients: 12, sessions: 48, satisfaction: 96, progress: 78 },\n      { name: 'Dr. <PERSON> <PERSON>-Mansouri', patients: 10, sessions: 42, satisfaction: 94, progress: 75 },\n      { name: 'Dr. Maryam Al-Zahra', patients: 8, sessions: 35, satisfaction: 92, progress: 70 },\n      { name: 'Dr. Omar Al-Harbi', patients: 6, sessions: 28, satisfaction: 90, progress: 68 },\n      { name: 'Dr. Layla Al-Dosari', patients: 9, sessions: 38, satisfaction: 95, progress: 73 }\n    ],\n    monthlyTrends: [\n      { month: 'Jan', sessions: 120, patients: 35, progress: 68 },\n      { month: 'Feb', sessions: 135, patients: 38, progress: 70 },\n      { month: 'Mar', sessions: 142, patients: 40, progress: 72 },\n      { month: 'Apr', sessions: 156, patients: 42, progress: 74 },\n      { month: 'May', sessions: 148, patients: 45, progress: 72 },\n      { month: 'Jun', sessions: 162, patients: 45, progress: 75 }\n    ]\n  };\n\n  const reportTemplates = [\n    {\n      id: 1,\n      name: 'Patient Progress Report',\n      nameAr: 'تقرير تقدم المرضى',\n      description: 'Comprehensive progress analysis for all patients',\n      descriptionAr: 'تحليل شامل للتقدم لجميع المرضى',\n      icon: 'fas fa-chart-line',\n      category: 'progress',\n      frequency: 'Monthly'\n    },\n    {\n      id: 2,\n      name: 'Therapist Performance Report',\n      nameAr: 'تقرير أداء المعالجين',\n      description: 'Performance metrics and statistics for therapists',\n      descriptionAr: 'مقاييس الأداء والإحصائيات للمعالجين',\n      icon: 'fas fa-user-md',\n      category: 'performance',\n      frequency: 'Quarterly'\n    },\n    {\n      id: 3,\n      name: 'Special Needs Analysis',\n      nameAr: 'تحليل الاحتياجات الخاصة',\n      description: 'Detailed analysis of special needs patient outcomes',\n      descriptionAr: 'تحليل مفصل لنتائج مرضى الاحتياجات الخاصة',\n      icon: 'fas fa-puzzle-piece',\n      category: 'special_needs',\n      frequency: 'Monthly'\n    },\n    {\n      id: 4,\n      name: 'Financial Summary',\n      nameAr: 'الملخص المالي',\n      description: 'Revenue and billing analysis',\n      descriptionAr: 'تحليل الإيرادات والفواتير',\n      icon: 'fas fa-dollar-sign',\n      category: 'financial',\n      frequency: 'Monthly'\n    },\n    {\n      id: 5,\n      name: 'Appointment Analytics',\n      nameAr: 'تحليلات المواعيد',\n      description: 'Appointment scheduling and attendance patterns',\n      descriptionAr: 'أنماط جدولة المواعيد والحضور',\n      icon: 'fas fa-calendar-alt',\n      category: 'appointments',\n      frequency: 'Weekly'\n    },\n    {\n      id: 6,\n      name: 'Treatment Effectiveness',\n      nameAr: 'فعالية العلاج',\n      description: 'Analysis of treatment plan effectiveness',\n      descriptionAr: 'تحليل فعالية خطط العلاج',\n      icon: 'fas fa-heartbeat',\n      category: 'treatment',\n      frequency: 'Quarterly'\n    }\n  ];\n\n  const tabs = [\n    { id: 'overview', label: t('overview', 'Overview'), icon: 'fas fa-chart-pie' },\n    { id: 'templates', label: t('reportTemplates', 'Report Templates'), icon: 'fas fa-file-alt' },\n    { id: 'analytics', label: t('analytics', 'Analytics'), icon: 'fas fa-chart-bar' },\n    { id: 'custom', label: t('customReports', 'Custom Reports'), icon: 'fas fa-tools' }\n  ];\n\n  const generateReport = (reportId) => {\n    setSelectedReport(reportId);\n    // In a real app, this would generate and download the report\n    alert(`${t('generating', 'Generating')} ${reportTemplates.find(r => r.id === reportId)?.name}...`);\n  };\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('reports', 'Reports')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('reportsDesc', 'Analytics and reporting dashboard')}\n          </p>\n        </div>\n        <div className=\"flex space-x-4\">\n          <select\n            value={dateRange}\n            onChange={(e) => setDateRange(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n          >\n            <option value=\"week\">{t('thisWeek', 'This Week')}</option>\n            <option value=\"month\">{t('thisMonth', 'This Month')}</option>\n            <option value=\"quarter\">{t('thisQuarter', 'This Quarter')}</option>\n            <option value=\"year\">{t('thisYear', 'This Year')}</option>\n          </select>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportData', 'Export Data')}\n          </button>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <i className={tab.icon}></i>\n              <span>{tab.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Overview Tab */}\n      {activeTab === 'overview' && (\n        <div className=\"space-y-6\">\n          {/* Key Metrics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6\">\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n                  <i className=\"fas fa-users text-blue-600 dark:text-blue-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('totalPatients', 'Total Patients')}</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{reportData.overview.totalPatients}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-full\">\n                  <i className=\"fas fa-user-check text-green-600 dark:text-green-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('activePatients', 'Active Patients')}</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{reportData.overview.activePatients}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n                  <i className=\"fas fa-calendar-check text-purple-600 dark:text-purple-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('completedSessions', 'Completed Sessions')}</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{reportData.overview.completedSessions}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full\">\n                  <i className=\"fas fa-chart-line text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('averageProgress', 'Average Progress')}</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{reportData.overview.averageProgress}%</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-indigo-100 dark:bg-indigo-900/30 rounded-full\">\n                  <i className=\"fas fa-arrow-up text-indigo-600 dark:text-indigo-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('monthlyGrowth', 'Monthly Growth')}</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">+{reportData.overview.monthlyGrowth}%</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-pink-100 dark:bg-pink-900/30 rounded-full\">\n                  <i className=\"fas fa-heart text-pink-600 dark:text-pink-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('satisfactionRate', 'Satisfaction Rate')}</p>\n                  <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{reportData.overview.satisfactionRate}%</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Charts Section */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Patient Distribution */}\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('patientDistribution', 'Patient Distribution by Condition')}\n              </h3>\n              <div className=\"space-y-4\">\n                {reportData.patientStats.map((stat, index) => (\n                  <div key={index} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <div className={`w-4 h-4 rounded-full ${stat.color} mr-3`}></div>\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{stat.condition}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{stat.count}</span>\n                      <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                        <div className={`h-2 rounded-full ${stat.color}`} style={{ width: `${stat.percentage}%` }}></div>\n                      </div>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400 w-8\">{stat.percentage}%</span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Therapist Performance */}\n            <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('therapistPerformance', 'Therapist Performance')}\n              </h3>\n              <div className=\"space-y-4\">\n                {reportData.therapistPerformance.slice(0, 3).map((therapist, index) => (\n                  <div key={index} className=\"border-b border-gray-200 dark:border-gray-600 pb-3 last:border-b-0\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{therapist.name}</span>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{therapist.patients} patients</span>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                      <div>\n                        <span className=\"text-gray-600 dark:text-gray-400\">{t('sessions', 'Sessions')}: </span>\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{therapist.sessions}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600 dark:text-gray-400\">{t('satisfaction', 'Satisfaction')}: </span>\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{therapist.satisfaction}%</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-600 dark:text-gray-400\">{t('progress', 'Progress')}: </span>\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{therapist.progress}%</span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Monthly Trends */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('monthlyTrends', 'Monthly Trends')}\n            </h3>\n            <div className=\"grid grid-cols-6 gap-4\">\n              {reportData.monthlyTrends.map((month, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">{month.month}</div>\n                  <div className=\"space-y-2\">\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      {t('sessions', 'Sessions')}: {month.sessions}\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      {t('patients', 'Patients')}: {month.patients}\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      {t('progress', 'Progress')}: {month.progress}%\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Report Templates Tab */}\n      {activeTab === 'templates' && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {reportTemplates.map((template) => (\n            <div key={template.id} className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-4\">\n                  <i className={`${template.icon} text-blue-600 dark:text-blue-400 text-xl`}></i>\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {isRTL ? template.nameAr : template.name}\n                  </h3>\n                  <span className=\"text-sm text-gray-500 dark:text-gray-400\">{template.frequency}</span>\n                </div>\n              </div>\n\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                {isRTL ? template.descriptionAr : template.description}\n              </p>\n\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => generateReport(template.id)}\n                  className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\"\n                >\n                  {t('generate', 'Generate')}\n                </button>\n                <button className=\"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm\">\n                  <i className=\"fas fa-cog\"></i>\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* Analytics Tab */}\n      {activeTab === 'analytics' && (\n        <div className=\"space-y-6\">\n          {/* Patient Demographics Chart */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('patientDemographics', 'Patient Demographics')}\n            </h3>\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* Condition Distribution */}\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                  {t('conditionDistribution', 'Condition Distribution')}\n                </h4>\n                <div className=\"space-y-3\">\n                  {reportData.patientStats.map((stat, index) => (\n                    <div key={index} className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`w-4 h-4 rounded ${stat.color}`}></div>\n                        <span className=\"text-gray-900 dark:text-white\">{stat.condition}</span>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-gray-600 dark:text-gray-400\">{stat.count}</span>\n                        <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                          <div\n                            className={`h-2 rounded-full ${stat.color}`}\n                            style={{ width: `${stat.percentage}%` }}\n                          />\n                        </div>\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400 w-8\">{stat.percentage}%</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Age Distribution */}\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                  {t('ageDistribution', 'Age Distribution')}\n                </h4>\n                <div className=\"space-y-3\">\n                  {[\n                    { range: '0-5 years', count: 12, percentage: 27 },\n                    { range: '6-10 years', count: 18, percentage: 40 },\n                    { range: '11-15 years', count: 10, percentage: 22 },\n                    { range: '16+ years', count: 5, percentage: 11 }\n                  ].map((age, index) => (\n                    <div key={index} className=\"flex items-center justify-between\">\n                      <span className=\"text-gray-900 dark:text-white\">{age.range}</span>\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-gray-600 dark:text-gray-400\">{age.count}</span>\n                        <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                          <div\n                            className=\"h-2 rounded-full bg-blue-500\"\n                            style={{ width: `${age.percentage}%` }}\n                          />\n                        </div>\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400 w-8\">{age.percentage}%</span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Treatment Outcomes */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('treatmentOutcomes', 'Treatment Outcomes')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {/* Success Rate */}\n              <div className=\"text-center\">\n                <div className=\"relative w-32 h-32 mx-auto mb-4\">\n                  <svg className=\"w-32 h-32 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                    <path\n                      d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      className=\"text-gray-200 dark:text-gray-700\"\n                    />\n                    <path\n                      d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      strokeDasharray=\"85, 100\"\n                      className=\"text-green-500\"\n                    />\n                  </svg>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">85%</span>\n                  </div>\n                </div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">{t('successRate', 'Success Rate')}</h4>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('goalAchievement', 'Goal Achievement')}</p>\n              </div>\n\n              {/* Average Progress */}\n              <div className=\"text-center\">\n                <div className=\"relative w-32 h-32 mx-auto mb-4\">\n                  <svg className=\"w-32 h-32 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                    <path\n                      d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      className=\"text-gray-200 dark:text-gray-700\"\n                    />\n                    <path\n                      d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      strokeDasharray=\"72, 100\"\n                      className=\"text-blue-500\"\n                    />\n                  </svg>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">72%</span>\n                  </div>\n                </div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">{t('avgProgress', 'Avg Progress')}</h4>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('overallImprovement', 'Overall Improvement')}</p>\n              </div>\n\n              {/* Satisfaction */}\n              <div className=\"text-center\">\n                <div className=\"relative w-32 h-32 mx-auto mb-4\">\n                  <svg className=\"w-32 h-32 transform -rotate-90\" viewBox=\"0 0 36 36\">\n                    <path\n                      d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      className=\"text-gray-200 dark:text-gray-700\"\n                    />\n                    <path\n                      d=\"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      strokeWidth=\"2\"\n                      strokeDasharray=\"94, 100\"\n                      className=\"text-purple-500\"\n                    />\n                  </svg>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">94%</span>\n                  </div>\n                </div>\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">{t('satisfaction', 'Satisfaction')}</h4>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('familySatisfaction', 'Family Satisfaction')}</p>\n              </div>\n            </div>\n          </div>\n\n          {/* Therapist Performance Analytics */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('therapistPerformance', 'Therapist Performance')}\n            </h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-200 dark:border-gray-600\">\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\">\n                      {t('therapist', 'Therapist')}\n                    </th>\n                    <th className=\"text-center py-3 px-4 font-medium text-gray-900 dark:text-white\">\n                      {t('patients', 'Patients')}\n                    </th>\n                    <th className=\"text-center py-3 px-4 font-medium text-gray-900 dark:text-white\">\n                      {t('sessions', 'Sessions')}\n                    </th>\n                    <th className=\"text-center py-3 px-4 font-medium text-gray-900 dark:text-white\">\n                      {t('satisfaction', 'Satisfaction')}\n                    </th>\n                    <th className=\"text-center py-3 px-4 font-medium text-gray-900 dark:text-white\">\n                      {t('avgProgress', 'Avg Progress')}\n                    </th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {reportData.therapistPerformance.map((therapist, index) => (\n                    <tr key={index} className=\"border-b border-gray-100 dark:border-gray-700\">\n                      <td className=\"py-3 px-4\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center\">\n                            <i className=\"fas fa-user-md text-blue-600 dark:text-blue-400 text-sm\"></i>\n                          </div>\n                          <span className=\"font-medium text-gray-900 dark:text-white\">{therapist.name}</span>\n                        </div>\n                      </td>\n                      <td className=\"text-center py-3 px-4 text-gray-600 dark:text-gray-400\">{therapist.patients}</td>\n                      <td className=\"text-center py-3 px-4 text-gray-600 dark:text-gray-400\">{therapist.sessions}</td>\n                      <td className=\"text-center py-3 px-4\">\n                        <div className=\"flex items-center justify-center space-x-2\">\n                          <span className=\"text-gray-900 dark:text-white\">{therapist.satisfaction}%</span>\n                          <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                            <div\n                              className=\"h-2 rounded-full bg-green-500\"\n                              style={{ width: `${therapist.satisfaction}%` }}\n                            />\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"text-center py-3 px-4\">\n                        <div className=\"flex items-center justify-center space-x-2\">\n                          <span className=\"text-gray-900 dark:text-white\">{therapist.progress}%</span>\n                          <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                            <div\n                              className=\"h-2 rounded-full bg-blue-500\"\n                              style={{ width: `${therapist.progress}%` }}\n                            />\n                          </div>\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Monthly Trends Chart */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('monthlyTrends', 'Monthly Trends')}\n            </h3>\n            <div className=\"grid grid-cols-6 gap-4\">\n              {reportData.monthlyTrends.map((month, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white mb-4\">{month.month}</div>\n\n                  {/* Sessions Bar */}\n                  <div className=\"mb-3\">\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400 mb-1\">{t('sessions', 'Sessions')}</div>\n                    <div className=\"relative h-20 bg-gray-200 dark:bg-gray-700 rounded\">\n                      <div\n                        className=\"absolute bottom-0 w-full bg-blue-500 rounded\"\n                        style={{ height: `${(month.sessions / 200) * 100}%` }}\n                      />\n                      <div className=\"absolute inset-0 flex items-end justify-center pb-1\">\n                        <span className=\"text-xs text-white font-medium\">{month.sessions}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Progress Indicator */}\n                  <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {t('progress', 'Progress')}: {month.progress}%\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mt-1\">\n                    <div\n                      className=\"h-1 rounded-full bg-green-500\"\n                      style={{ width: `${month.progress}%` }}\n                    />\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Custom Reports Tab */}\n      {activeTab === 'custom' && (\n        <div className=\"space-y-6\">\n          {/* Report Builder Interface */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('customReportBuilder', 'Custom Report Builder')}\n              </h3>\n              <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveReport', 'Save Report')}\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n              {/* Data Sources */}\n              <div className=\"lg:col-span-1\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-4\">\n                  {t('dataSources', 'Data Sources')}\n                </h4>\n                <div className=\"space-y-2\">\n                  {[\n                    { id: 'patients', label: 'Patients', icon: 'fas fa-users', fields: ['name', 'age', 'condition', 'status'] },\n                    { id: 'treatments', label: 'Treatments', icon: 'fas fa-heartbeat', fields: ['type', 'duration', 'progress', 'therapist'] },\n                    { id: 'sessions', label: 'Sessions', icon: 'fas fa-calendar', fields: ['date', 'duration', 'activities', 'notes'] },\n                    { id: 'assessments', label: 'Assessments', icon: 'fas fa-clipboard-check', fields: ['type', 'score', 'date', 'notes'] },\n                    { id: 'goals', label: 'Goals', icon: 'fas fa-bullseye', fields: ['title', 'progress', 'target_date', 'status'] }\n                  ].map(source => (\n                    <div key={source.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-3\">\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <i className={`${source.icon} text-blue-600 dark:text-blue-400`}></i>\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{source.label}</span>\n                      </div>\n                      <div className=\"space-y-1\">\n                        {source.fields.map(field => (\n                          <div\n                            key={field}\n                            draggable\n                            className=\"text-xs text-gray-600 dark:text-gray-400 p-1 bg-gray-50 dark:bg-gray-700 rounded cursor-move hover:bg-gray-100 dark:hover:bg-gray-600\"\n                          >\n                            {field.replace('_', ' ')}\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Report Canvas */}\n              <div className=\"lg:col-span-2\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-4\">\n                  {t('reportCanvas', 'Report Canvas')}\n                </h4>\n                <div className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 min-h-96\">\n                  <div className=\"space-y-4\">\n                    {/* Report Header */}\n                    <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700\">\n                      <h5 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                        {t('reportHeader', 'Report Header')}\n                      </h5>\n                      <input\n                        type=\"text\"\n                        placeholder={t('reportTitle', 'Report Title')}\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white mb-2\"\n                      />\n                      <textarea\n                        placeholder={t('reportDescription', 'Report Description')}\n                        rows=\"2\"\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                      />\n                    </div>\n\n                    {/* Sample Report Sections */}\n                    <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h5 className=\"font-medium text-gray-900 dark:text-white\">\n                          {t('patientSummary', 'Patient Summary')}\n                        </h5>\n                        <button className=\"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200\">\n                          <i className=\"fas fa-trash text-sm\"></i>\n                        </button>\n                      </div>\n                      <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                        <div className=\"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\">\n                          <span className=\"text-blue-800 dark:text-blue-300\">Patient Name</span>\n                        </div>\n                        <div className=\"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\">\n                          <span className=\"text-blue-800 dark:text-blue-300\">Condition</span>\n                        </div>\n                        <div className=\"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\">\n                          <span className=\"text-blue-800 dark:text-blue-300\">Age</span>\n                        </div>\n                        <div className=\"p-2 bg-blue-50 dark:bg-blue-900/20 rounded\">\n                          <span className=\"text-blue-800 dark:text-blue-300\">Status</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h5 className=\"font-medium text-gray-900 dark:text-white\">\n                          {t('treatmentProgress', 'Treatment Progress')}\n                        </h5>\n                        <button className=\"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200\">\n                          <i className=\"fas fa-trash text-sm\"></i>\n                        </button>\n                      </div>\n                      <div className=\"space-y-2 text-sm\">\n                        <div className=\"p-2 bg-green-50 dark:bg-green-900/20 rounded\">\n                          <span className=\"text-green-800 dark:text-green-300\">Progress Percentage</span>\n                        </div>\n                        <div className=\"p-2 bg-green-50 dark:bg-green-900/20 rounded\">\n                          <span className=\"text-green-800 dark:text-green-300\">Sessions Completed</span>\n                        </div>\n                        <div className=\"p-2 bg-green-50 dark:bg-green-900/20 rounded\">\n                          <span className=\"text-green-800 dark:text-green-300\">Goals Achieved</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Drop Zone */}\n                    <div className=\"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center\">\n                      <i className=\"fas fa-plus text-blue-400 text-2xl mb-2\"></i>\n                      <p className=\"text-blue-600 dark:text-blue-400\">\n                        {t('dropFieldsHere', 'Drop fields here to add to report')}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Report Settings */}\n              <div className=\"lg:col-span-1\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-4\">\n                  {t('reportSettings', 'Report Settings')}\n                </h4>\n                <div className=\"space-y-4\">\n                  {/* Filters */}\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-3\">\n                    <h5 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n                      {t('filters', 'Filters')}\n                    </h5>\n                    <div className=\"space-y-3\">\n                      <div>\n                        <label className=\"block text-sm text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('dateRange', 'Date Range')}\n                        </label>\n                        <select className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white\">\n                          <option value=\"week\">{t('lastWeek', 'Last Week')}</option>\n                          <option value=\"month\">{t('lastMonth', 'Last Month')}</option>\n                          <option value=\"quarter\">{t('lastQuarter', 'Last Quarter')}</option>\n                          <option value=\"year\">{t('lastYear', 'Last Year')}</option>\n                        </select>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('condition', 'Condition')}\n                        </label>\n                        <select className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white\">\n                          <option value=\"all\">{t('allConditions', 'All Conditions')}</option>\n                          <option value=\"autism\">Autism</option>\n                          <option value=\"cerebral_palsy\">Cerebral Palsy</option>\n                          <option value=\"down_syndrome\">Down Syndrome</option>\n                        </select>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('therapist', 'Therapist')}\n                        </label>\n                        <select className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white\">\n                          <option value=\"all\">{t('allTherapists', 'All Therapists')}</option>\n                          <option value=\"sarah\">Dr. Sarah Ahmed</option>\n                          <option value=\"ahmed\">Dr. Ahmed Al-Mansouri</option>\n                          <option value=\"maryam\">Dr. Maryam Al-Zahra</option>\n                        </select>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Chart Types */}\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-3\">\n                    <h5 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n                      {t('chartTypes', 'Chart Types')}\n                    </h5>\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      {[\n                        { type: 'bar', icon: 'fas fa-chart-bar', label: 'Bar Chart' },\n                        { type: 'line', icon: 'fas fa-chart-line', label: 'Line Chart' },\n                        { type: 'pie', icon: 'fas fa-chart-pie', label: 'Pie Chart' },\n                        { type: 'table', icon: 'fas fa-table', label: 'Table' }\n                      ].map(chart => (\n                        <button\n                          key={chart.type}\n                          className=\"p-2 border border-gray-200 dark:border-gray-600 rounded text-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                        >\n                          <i className={`${chart.icon} text-gray-600 dark:text-gray-400 mb-1`}></i>\n                          <div className=\"text-xs text-gray-600 dark:text-gray-400\">{chart.label}</div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Export Options */}\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-3\">\n                    <h5 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n                      {t('exportOptions', 'Export Options')}\n                    </h5>\n                    <div className=\"space-y-2\">\n                      {['PDF', 'Excel', 'CSV', 'Word'].map(format => (\n                        <label key={format} className=\"flex items-center space-x-2\">\n                          <input type=\"checkbox\" className=\"rounded\" defaultChecked={format === 'PDF'} />\n                          <span className=\"text-sm text-gray-700 dark:text-gray-300\">{format}</span>\n                        </label>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"space-y-2\">\n                    <button className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\">\n                      <i className=\"fas fa-eye mr-2\"></i>\n                      {t('previewReport', 'Preview Report')}\n                    </button>\n                    <button className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\">\n                      <i className=\"fas fa-download mr-2\"></i>\n                      {t('generateReport', 'Generate Report')}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Saved Custom Reports */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('savedCustomReports', 'Saved Custom Reports')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[\n                {\n                  name: 'Monthly Progress Summary',\n                  nameAr: 'ملخص التقدم الشهري',\n                  created: '2024-02-01',\n                  lastRun: '2024-02-10',\n                  frequency: 'Monthly'\n                },\n                {\n                  name: 'Autism Treatment Analysis',\n                  nameAr: 'تحليل علاج التوحد',\n                  created: '2024-01-15',\n                  lastRun: '2024-02-05',\n                  frequency: 'Weekly'\n                },\n                {\n                  name: 'Therapist Performance Review',\n                  nameAr: 'مراجعة أداء المعالجين',\n                  created: '2024-01-20',\n                  lastRun: '2024-02-08',\n                  frequency: 'Quarterly'\n                }\n              ].map((report, index) => (\n                <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                    {isRTL ? report.nameAr : report.name}\n                  </h4>\n                  <div className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1 mb-3\">\n                    <div>{t('created', 'Created')}: {new Date(report.created).toLocaleDateString()}</div>\n                    <div>{t('lastRun', 'Last Run')}: {new Date(report.lastRun).toLocaleDateString()}</div>\n                    <div>{t('frequency', 'Frequency')}: {report.frequency}</div>\n                  </div>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"flex-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors\">\n                      {t('run', 'Run')}\n                    </button>\n                    <button className=\"px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n                      <i className=\"fas fa-edit\"></i>\n                    </button>\n                    <button className=\"px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded text-sm hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors\">\n                      <i className=\"fas fa-trash\"></i>\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Reports;\n"], "names": ["Reports", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "date<PERSON><PERSON><PERSON>", "setDateRange", "selectedReport", "setSelectedReport", "chartData", "setChartData", "loading", "setLoading", "reportData", "overview", "totalPatients", "activePatients", "completedSessions", "averageProgress", "monthlyGrowth", "satisfactionRate", "patientStats", "condition", "count", "percentage", "color", "therapistPerformance", "name", "patients", "sessions", "satisfaction", "progress", "monthlyTrends", "month", "reportTemplates", "id", "nameAr", "description", "descriptionAr", "icon", "category", "frequency", "tabs", "label", "_jsxs", "className", "concat", "children", "_jsx", "value", "onChange", "e", "target", "map", "tab", "onClick", "stat", "index", "style", "width", "slice", "therapist", "template", "reportId", "_reportTemplates$find", "alert", "find", "r", "generateReport", "range", "age", "viewBox", "d", "fill", "stroke", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "height", "fields", "source", "field", "draggable", "replace", "type", "placeholder", "rows", "chart", "format", "defaultChecked", "created", "lastRun", "report", "Date", "toLocaleDateString"], "sourceRoot": ""}