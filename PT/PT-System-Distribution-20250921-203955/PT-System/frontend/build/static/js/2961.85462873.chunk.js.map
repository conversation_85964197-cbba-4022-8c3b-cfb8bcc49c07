{"version": 3, "file": "static/js/2961.85462873.chunk.js", "mappings": "iOAMA,MAqXA,EArXoBA,KAClB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MAEVC,EAASC,KADCC,EAAAA,EAAAA,OACaC,EAAAA,EAAAA,WAAS,KAChCC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,aACpCG,EAAWC,IAAgBJ,EAAAA,EAAAA,UAAS,CACzCK,UAAW,IAAIC,MAAK,IAAIA,MAAOC,eAAe,IAAID,MAAOE,WAAY,GAAGC,cAAcC,MAAM,KAAK,GACjGC,SAAS,IAAIL,MAAOG,cAAcC,MAAM,KAAK,MAGxCE,EAAYC,IAAiBb,EAAAA,EAAAA,UAAS,CAC3Cc,SAAU,CACRC,aAAc,MACdC,cAAe,KACfC,cAAe,KACfC,cAAe,KACfC,cAAe,IACfC,aAAc,IACdC,gBAAiB,GACjBC,gBAAiB,GAEnBC,cAAe,CACb,CAAEC,MAAO,MAAOC,QAAS,KAAOC,SAAU,MAC1C,CAAEF,MAAO,MAAOC,QAAS,KAAOC,SAAU,MAC1C,CAAEF,MAAO,MAAOC,QAAS,MAAQC,SAAU,MAC3C,CAAEF,MAAO,MAAOC,QAAS,MAAQC,SAAU,OAC3C,CAAEF,MAAO,MAAOC,QAAS,MAAQC,SAAU,QAE7CC,eAAgB,CACd,CAAEC,OAAQ,YAAaC,OAAQ,KAAOC,WAAY,MAClD,CAAEF,OAAQ,OAAQC,OAAQ,KAAOC,WAAY,MAC7C,CAAEF,OAAQ,OAAQC,OAAQ,IAAOC,WAAY,MAC7C,CAAEF,OAAQ,gBAAiBC,OAAQ,IAAMC,WAAY,MAEvDC,YAAa,CACX,CAAEC,QAAS,2BAA4BP,QAAS,KAAOQ,SAAU,KACjE,CAAED,QAAS,qBAAsBP,QAAS,KAAOQ,SAAU,KAC3D,CAAED,QAAS,oBAAqBP,QAAS,KAAOQ,SAAU,IAC1D,CAAED,QAAS,oBAAqBP,QAAS,IAAOQ,SAAU,SAI9DC,EAAAA,EAAAA,WAAU,KACRC,KACC,CAAChC,IAEJ,MAAMgC,EAAiBC,UACrBtC,GAAW,GACX,UAEQ,IAAIuC,QAAQC,GAAWC,WAAWD,EAAS,KAEnD,CAAE,MAAOE,GACPC,EAAAA,GAAMD,MAAMhD,EAAE,sBAAuB,mCACvC,CAAC,QACCM,GAAW,EACb,GAGI4C,EAAwBA,CAACC,EAAOC,KACpCxC,EAAayC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACF,GAAQC,MAGtCG,EAAgBC,IACpBP,EAAAA,GAAMQ,QAAQzD,EAAE,iBAAiB,sBAAD0D,OAAwBF,EAAOG,kBAOjE,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sFAAqFC,SAAA,EAClGF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D9D,EAAE,mBAAoB,wBAEzB+D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD9D,EAAE,uBAAwB,yDAK/B4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9D,EAAE,YAAa,iBAElB+D,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLZ,MAAOzC,EAAUE,UACjBoD,SAAWC,GAAMhB,EAAsB,YAAagB,EAAEC,OAAOf,OAC7DS,UAAU,iKAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9D,EAAE,UAAW,eAEhB+D,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLZ,MAAOzC,EAAUQ,QACjB8C,SAAWC,GAAMhB,EAAsB,UAAWgB,EAAEC,OAAOf,OAC3DS,UAAU,yKAQpBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCF,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAM1D,EAAa,YAC5BmD,UAAS,4CAAAH,OACO,aAAdjD,EACI,mDACA,0HACHqD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZ7D,EAAE,WAAY,gBAEjB4D,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAM1D,EAAa,WAC5BmD,UAAS,4CAAAH,OACO,YAAdjD,EACI,mDACA,0HACHqD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZ7D,EAAE,UAAW,eAEhB4D,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAM1D,EAAa,YAC5BmD,UAAS,4CAAAH,OACO,aAAdjD,EACI,mDACA,0HACHqD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZ7D,EAAE,WAAY,gBAEjB4D,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAM1D,EAAa,SAC5BmD,UAAS,4CAAAH,OACO,UAAdjD,EACI,mDACA,0HACHqD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ7D,EAAE,QAAS,aAEd4D,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAM1D,EAAa,UAC5BmD,UAAS,4CAAAH,OACO,WAAdjD,EACI,mDACA,0HACHqD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ7D,EAAE,SAAU,oBAON,aAAdS,IACCmD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7D1C,EAAWE,SAASC,aAAa8C,iBAAiB,IAAErE,EAAE,MAAO,WAEhE+D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAE9D,EAAE,eAAgB,4BAKzE+D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7D1C,EAAWE,SAASE,cAAc6C,iBAAiB,IAAErE,EAAE,MAAO,WAEjE+D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAE9D,EAAE,gBAAiB,6BAK1E+D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7D1C,EAAWE,SAASG,cAAc4C,iBAAiB,IAAErE,EAAE,MAAO,WAEjE+D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAE9D,EAAE,gBAAiB,6BAK1E+D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+CAA8CC,UAC3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0EAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7D1C,EAAWE,SAASI,cAAc2C,iBAAiB,IAAErE,EAAE,MAAO,WAEjE+D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAE9D,EAAE,gBAAiB,gCAO5E4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE9D,EAAE,eAAgB,oBAErB+D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+EAA8EC,UAC3FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5C9D,EAAE,mBAAoB,0CAO/B4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE9D,EAAE,6BAA8B,mCAEnC+D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB1C,EAAWe,eAAemC,IAAI,CAAClC,EAAQmC,KACtCX,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,6BAAAH,OACF,IAAVa,EAAc,cACJ,IAAVA,EAAc,eACJ,IAAVA,EAAc,gBAAkB,oBAElCR,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAE1B,EAAOA,aAE7DwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,CACzD1B,EAAOC,OAAOgC,iBAAiB,IAAErE,EAAE,MAAO,WAE7C4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtD1B,EAAOE,WAAW,YAdfiC,aAwBlBX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE9D,EAAE,cAAe,8BAEpB+D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9D,EAAE,UAAW,cAEhB+D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9D,EAAE,UAAW,cAEhB+D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9D,EAAE,WAAY,eAEjB+D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9D,EAAE,oBAAqB,uBAI9B+D,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvF1C,EAAWmB,YAAY+B,IAAI,CAAC9B,EAAS+B,KACpCX,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gFAA+EC,SAC1FtB,EAAQA,WAEXoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oEAAmEC,SAAA,CAC9EtB,EAAQP,QAAQoC,iBAAiB,IAAErE,EAAE,MAAO,WAE/C+D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9EtB,EAAQC,YAEXmB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oEAAmEC,SAAA,EAC7EtB,EAAQP,QAAUO,EAAQC,UAAU+B,QAAQ,GAAG,IAAExE,EAAE,MAAO,YAXvDuE,kBAuBvBX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE9D,EAAE,gBAAiB,qBAEtB4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAMb,EAAa,OAC5BM,UAAU,gFAA+EC,SAAA,EAEzFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZ7D,EAAE,YAAa,kBAElB4D,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAMb,EAAa,SAC5BM,UAAU,oFAAmFC,SAAA,EAE7FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZ7D,EAAE,cAAe,oBAEpB4D,EAAAA,EAAAA,MAAA,UACEQ,QAASA,IAAMb,EAAa,OAC5BM,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZ7D,EAAE,YAAa,kBAElB4D,EAAAA,EAAAA,MAAA,UACEQ,QArSmBK,KAC3BxB,EAAAA,GAAMQ,QAAQzD,EAAE,wBAAyB,0CAqSjC6D,UAAU,sFAAqFC,SAAA,EAE/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ7D,EAAE,eAAgB,6B", "sources": ["pages/Financial/ReportsPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst ReportsPage = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [dateRange, setDateRange] = useState({\n    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],\n    endDate: new Date().toISOString().split('T')[0]\n  });\n\n  const [reportData, setReportData] = useState({\n    overview: {\n      totalRevenue: 125000,\n      totalPayments: 98000,\n      pendingAmount: 27000,\n      overdueAmount: 15000,\n      totalInvoices: 156,\n      paidInvoices: 120,\n      pendingInvoices: 28,\n      overdueInvoices: 8\n    },\n    monthlyTrends: [\n      { month: 'Jan', revenue: 85000, payments: 78000 },\n      { month: 'Feb', revenue: 92000, payments: 85000 },\n      { month: 'Mar', revenue: 105000, payments: 95000 },\n      { month: 'Apr', revenue: 118000, payments: 108000 },\n      { month: 'May', revenue: 125000, payments: 115000 }\n    ],\n    paymentMethods: [\n      { method: 'Insurance', amount: 65000, percentage: 52.4 },\n      { method: 'Cash', amount: 35000, percentage: 28.2 },\n      { method: 'Card', amount: 20000, percentage: 16.1 },\n      { method: 'Bank Transfer', amount: 4000, percentage: 3.2 }\n    ],\n    topServices: [\n      { service: 'Physical Therapy Session', revenue: 45000, sessions: 180 },\n      { service: 'Initial Assessment', revenue: 28000, sessions: 140 },\n      { service: 'Treatment Package', revenue: 32000, sessions: 80 },\n      { service: 'Follow-up Session', revenue: 20000, sessions: 100 }\n    ]\n  });\n\n  useEffect(() => {\n    loadReportData();\n  }, [dateRange]);\n\n  const loadReportData = async () => {\n    setLoading(true);\n    try {\n      // Mock API call - replace with actual data fetching\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      // Data is already set in state for demo purposes\n    } catch (error) {\n      toast.error(t('errorLoadingReports', 'Error loading financial reports'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDateRangeChange = (field, value) => {\n    setDateRange(prev => ({ ...prev, [field]: value }));\n  };\n\n  const exportReport = (format) => {\n    toast.success(t('reportExported', `Report exported as ${format.toUpperCase()}`));\n  };\n\n  const generateCustomReport = () => {\n    toast.success(t('customReportGenerated', 'Custom report generated successfully'));\n  };\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {t('financialReports', 'Financial Reports')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n              {t('financialReportsDesc', 'Comprehensive financial analytics and reporting')}\n            </p>\n          </div>\n          \n          {/* Date Range Selector */}\n          <div className=\"flex items-center space-x-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('startDate', 'Start Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={dateRange.startDate}\n                onChange={(e) => handleDateRangeChange('startDate', e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('endDate', 'End Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={dateRange.endDate}\n                onChange={(e) => handleDateRangeChange('endDate', e.target.value)}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-600\">\n          <nav className=\"-mb-px flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('overview')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'overview'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-chart-pie mr-2\"></i>\n              {t('overview', 'Overview')}\n            </button>\n            <button\n              onClick={() => setActiveTab('revenue')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'revenue'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-chart-line mr-2\"></i>\n              {t('revenue', 'Revenue')}\n            </button>\n            <button\n              onClick={() => setActiveTab('payments')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'payments'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-credit-card mr-2\"></i>\n              {t('payments', 'Payments')}\n            </button>\n            <button\n              onClick={() => setActiveTab('aging')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'aging'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-clock mr-2\"></i>\n              {t('aging', 'Aging')}\n            </button>\n            <button\n              onClick={() => setActiveTab('custom')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'custom'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-cogs mr-2\"></i>\n              {t('custom', 'Custom')}\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'overview' && (\n        <div className=\"space-y-6\">\n          {/* Key Metrics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n                  <i className=\"fas fa-dollar-sign text-green-600 dark:text-green-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {reportData.overview.totalRevenue.toLocaleString()} {t('sar', 'SAR')}\n                  </h4>\n                  <p className=\"text-gray-500 dark:text-gray-400\">{t('totalRevenue', 'Total Revenue')}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n                  <i className=\"fas fa-credit-card text-blue-600 dark:text-blue-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {reportData.overview.totalPayments.toLocaleString()} {t('sar', 'SAR')}\n                  </h4>\n                  <p className=\"text-gray-500 dark:text-gray-400\">{t('totalPayments', 'Total Payments')}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n                  <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {reportData.overview.pendingAmount.toLocaleString()} {t('sar', 'SAR')}\n                  </h4>\n                  <p className=\"text-gray-500 dark:text-gray-400\">{t('pendingAmount', 'Pending Amount')}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg\">\n                  <i className=\"fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    {reportData.overview.overdueAmount.toLocaleString()} {t('sar', 'SAR')}\n                  </h4>\n                  <p className=\"text-gray-500 dark:text-gray-400\">{t('overdueAmount', 'Overdue Amount')}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Charts and Analytics */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Revenue Trend Chart */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('revenueTrend', 'Revenue Trend')}\n              </h3>\n              <div className=\"h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center\">\n                  <i className=\"fas fa-chart-line text-4xl text-gray-400 mb-2\"></i>\n                  <p className=\"text-gray-500 dark:text-gray-400\">\n                    {t('chartPlaceholder', 'Chart will be rendered here')}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Payment Methods Distribution */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('paymentMethodsDistribution', 'Payment Methods Distribution')}\n              </h3>\n              <div className=\"space-y-3\">\n                {reportData.paymentMethods.map((method, index) => (\n                  <div key={index} className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center\">\n                      <div className={`w-4 h-4 rounded-full mr-3 ${\n                        index === 0 ? 'bg-blue-500' :\n                        index === 1 ? 'bg-green-500' :\n                        index === 2 ? 'bg-yellow-500' : 'bg-purple-500'\n                      }`}></div>\n                      <span className=\"text-gray-700 dark:text-gray-300\">{method.method}</span>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"font-semibold text-gray-900 dark:text-white\">\n                        {method.amount.toLocaleString()} {t('sar', 'SAR')}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {method.percentage}%\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Top Services */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('topServices', 'Top Services by Revenue')}\n            </h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                      {t('service', 'Service')}\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                      {t('revenue', 'Revenue')}\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                      {t('sessions', 'Sessions')}\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                      {t('averagePerSession', 'Avg/Session')}\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                  {reportData.topServices.map((service, index) => (\n                    <tr key={index}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                        {service.service}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                        {service.revenue.toLocaleString()} {t('sar', 'SAR')}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                        {service.sessions}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                        {(service.revenue / service.sessions).toFixed(0)} {t('sar', 'SAR')}\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Export Actions */}\n      <div className=\"mt-8 bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('exportReports', 'Export Reports')}\n        </h3>\n        <div className=\"flex flex-wrap gap-4\">\n          <button\n            onClick={() => exportReport('pdf')}\n            className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\"\n          >\n            <i className=\"fas fa-file-pdf mr-2\"></i>\n            {t('exportPDF', 'Export PDF')}\n          </button>\n          <button\n            onClick={() => exportReport('excel')}\n            className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n          >\n            <i className=\"fas fa-file-excel mr-2\"></i>\n            {t('exportExcel', 'Export Excel')}\n          </button>\n          <button\n            onClick={() => exportReport('csv')}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <i className=\"fas fa-file-csv mr-2\"></i>\n            {t('exportCSV', 'Export CSV')}\n          </button>\n          <button\n            onClick={generateCustomReport}\n            className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n          >\n            <i className=\"fas fa-cogs mr-2\"></i>\n            {t('customReport', 'Custom Report')}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReportsPage;\n"], "names": ["ReportsPage", "t", "isRTL", "useLanguage", "user", "useAuth", "loading", "setLoading", "useNavigate", "useState", "activeTab", "setActiveTab", "date<PERSON><PERSON><PERSON>", "setDateRange", "startDate", "Date", "getFullYear", "getMonth", "toISOString", "split", "endDate", "reportData", "setReportData", "overview", "totalRevenue", "totalPayments", "pendingAmount", "overdueAmount", "totalInvoices", "paidInvoices", "pendingInvoices", "overdueInvoices", "monthlyTrends", "month", "revenue", "payments", "paymentMethods", "method", "amount", "percentage", "topServices", "service", "sessions", "useEffect", "loadReportData", "async", "Promise", "resolve", "setTimeout", "error", "toast", "handleDateRangeChange", "field", "value", "prev", "_objectSpread", "exportReport", "format", "success", "concat", "toUpperCase", "_jsxs", "className", "children", "_jsx", "type", "onChange", "e", "target", "onClick", "toLocaleString", "map", "index", "toFixed", "generateCustomReport"], "sourceRoot": ""}