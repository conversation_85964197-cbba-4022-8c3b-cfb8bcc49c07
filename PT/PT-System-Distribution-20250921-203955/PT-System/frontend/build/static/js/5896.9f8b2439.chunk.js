"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[5896],{5896:(e,a,r)=>{r.r(a),r.d(a,{default:()=>o});var t=r(2555),l=r(5043),s=r(7921),i=r(4528),n=r(3216),d=r(579);const o=()=>{const{t:e,isRTL:a}=(0,s.o)(),{user:r}=(0,i.A)(),{patientId:o}=(0,n.g)(),c=(0,n.Zp)(),[m,g]=(0,l.useState)({documentNumber:"QP-".concat(Date.now()),issueDate:(new Date).toISOString().split("T")[0],version:"01",reviewNumber:"01",assessmentType:"",patientLearningPreference:[],patientLearningOther:"",patientBarriers:[],patientBarriersOther:"",caregiverLearningPreference:[],caregiverLearningOther:"",caregiverBarriers:[],caregiverBarriersOther:"",selfCareCapability:"",selfCareCapabilityReason:"",selfCareMotivation:"",selfCareMotivationReason:"",comments:"",educationalNeeds:[],educationalNeedsOther:"",teachingMethod:[],teachingTool:[],evaluation:[],patientAttend:"",problemsAttending:"",literacySkills:"",careProviderAfterDischarge:[],careProviderOther:"",plansComments:"",therapistName:(null===r||void 0===r?void 0:r.name)||"",signature:"",badgeNo:"",date:(new Date).toISOString().split("T")[0],submittedBy:(null===r||void 0===r?void 0:r.id)||"",submittedAt:(new Date).toISOString()}),[u,b]=(0,l.useState)(!1),[x,p]=(0,l.useState)({}),h=[{value:"initial",label:e("initialAssessment","Initial Assessment")},{value:"reassessment",label:e("reAssessment","Re-Assessment")},{value:"discharge",label:e("dischargeAssessment","Discharge Assessment")}],v=[{value:"video",label:e("video","Video")},{value:"written_materials",label:e("writtenMaterialsPicture","Written Materials with Picture")},{value:"demonstration",label:e("demonstration","Demonstration")},{value:"other",label:e("other","Other")}],f=[{value:"physical",label:e("physical","Physical")},{value:"cognitive",label:e("cognitive","Cognitive")},{value:"emotional",label:e("emotional","Emotional")},{value:"cultural_believe",label:e("culturalBelieve","Cultural/Believe")},{value:"poor_motivation",label:e("poorMotivation","Poor Motivation")},{value:"financial_difficulties",label:e("financialDifficulties","Financial Difficulties")},{value:"reading_ability",label:e("readingAbility","Reading Ability")},{value:"psychological",label:e("psychological","Psychological")},{value:"language",label:e("language","Language")},{value:"impaired_hearing",label:e("impairedHearing","Impaired Hearing")},{value:"speech_barriers",label:e("speechBarriers","Speech Barriers")},{value:"responsibilities_home",label:e("responsibilitiesHome","Responsibilities at Home")},{value:"religious_practice",label:e("religiousPractice","Religious Practice")},{value:"impaired_vision",label:e("impairedVision","Impaired Vision")},{value:"age",label:e("age","Age")},{value:"no_barrier",label:e("noBarrier","No Barrier")},{value:"others",label:e("others","Others")}],y=[{value:"dependent",label:e("dependent","Dependent")},{value:"independent",label:e("independent","Independent")},{value:"need_assistant",label:e("needAssistant","Need Assistant")},{value:"not_applicable",label:e("notApplicable","Not Applicable")}],k=[{value:"poor",label:e("poor","Poor")},{value:"moderate",label:e("moderate","Moderate")},{value:"high",label:e("high","High")},{value:"not_applicable",label:e("notApplicable","Not Applicable")}],N=[{value:"disease_process",label:e("diseaseProcess","Disease Process")},{value:"medication_use",label:e("medicationUse","Safe and Effective Use of Medication, Side Effects, and Interactions")},{value:"medical_equipment",label:e("medicalEquipment","Safe and Effective Use of Medical Equipment")},{value:"pain_management",label:e("painManagement","Pain Management")},{value:"rehabilitation_technique",label:e("rehabilitationTechnique","Rehabilitation Technique")},{value:"community_resources",label:e("communityResources","Community Resources")},{value:"discharge_instruction",label:e("dischargeInstruction","Discharge Instruction of Continuing Care")},{value:"patient_rights",label:e("patientRights","Patients' Rights and Responsibilities")},{value:"further_treatment",label:e("furtherTreatment","When and How to Obtain Further Treatment")},{value:"personal_hygiene",label:e("personalHygiene","Personal Hygiene and Practitioner Hand Wash")},{value:"infection_control",label:e("infectionControl","Infection Control and Related Issues")},{value:"care_plan",label:e("carePlan","Care Plan and Treatment")},{value:"informed_consent",label:e("informedConsent","Informed Consent Procedure")},{value:"home_instruction",label:e("homeInstruction","Home Instruction")},{value:"diagnostic_test",label:e("diagnosticTest","Diagnostic Test/Procedure Social Services")},{value:"health_maintenance",label:e("healthMaintenance","Health Maintenance")},{value:"nutrition",label:e("nutrition","Nutrition")},{value:"risk_safety",label:e("riskSafety","Risk, Safety")},{value:"others",label:e("others","Others")}],j=[{value:"one_to_one",label:e("oneToOne","One to One")},{value:"group_teaching",label:e("groupTeaching","Group Teaching")},{value:"lecture",label:e("lecture","Lecture")}],w=[{value:"written_materials",label:e("writtenMaterials","Written Materials")},{value:"audio",label:e("audio","Audio")},{value:"verbal_instructions",label:e("verbalInstructions","Verbal Instructions")},{value:"written_instruction",label:e("writtenInstruction","Written Instruction")},{value:"video",label:e("video","Video")},{value:"demonstration",label:e("demonstration","Demonstration")}],C=[{value:"not_receptive",label:e("notReceptive","Not Receptive to Learning/No Learning")},{value:"unable_verbalize",label:e("unableVerbalize","Unable to Verbalize Basic Concepts")},{value:"verbalize_assistance",label:e("verbalizeAssistance","Verbalize Basic Concepts with Assistance")},{value:"verbalize_basic",label:e("verbalizeBAsic","Verbalize Basic Concept")},{value:"return_demonstration",label:e("returnDemonstration","Return Demonstration")},{value:"applies_knowledge",label:e("appliesKnowledge","Applies Knowledge/skills")},{value:"not_applicable",label:e("notApplicable","Not Applicable")},{value:"need_followup",label:e("needFollowup","Need Follow-up")}],A=[{value:"school",label:e("school","School")},{value:"work",label:e("work","Work")},{value:"not_applicable",label:e("notApplicable","NOT APPLICABLE")}],P=[{value:"yes",label:e("yes","Yes")},{value:"no",label:e("no","No")},{value:"not_applicable",label:e("notApplicable","NOT APPLICABLE")}],S=[{value:"yes",label:e("yes","Yes")},{value:"no",label:e("no","No")}],_=[{value:"caregiver",label:e("caregiver","Caregiver")},{value:"family",label:e("family","Family")},{value:"reassessment",label:e("reassessment","Re-Assessment")},{value:"discharge_assessment",label:e("dischargeAssessment","Discharge Assessment")},{value:"others",label:e("others","Others")}];(0,l.useEffect)(()=>{o&&O()},[o]);const O=async()=>{try{b(!0);const e=await fetch("/api/patients/".concat(o));if(e.ok){await e.json()}}catch(e){console.error("Error loading patient data:",e)}finally{b(!1)}},B=(e,a)=>{g(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a})),x[e]&&p(a=>(0,t.A)((0,t.A)({},a),{},{[e]:null}))},T=(e,a,r)=>{g(l=>{let s;return s=r?[...l[e],a]:l[e].filter(e=>e!==a),e.includes("Barriers")&&"no_barrier"===a&&r?s=["no_barrier"]:e.includes("Barriers")&&"no_barrier"!==a&&r&&l[e].includes("no_barrier")&&(s=s.filter(e=>"no_barrier"!==e)),(0,t.A)((0,t.A)({},l),{},{[e]:s})})};return u?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-6",children:(0,d.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,d.jsx)("div",{className:"px-6 py-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:e("patientFamilyEducationForm","Patient and Family Education Form for Physical Therapist")}),(0,d.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-stethoscope text-indigo-500 mr-2"}),e("educationFormDescription","Comprehensive patient and family education assessment and planning")]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 mt-3",children:[(0,d.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,d.jsx)("i",{className:"fas fa-clock text-blue-500 mr-1"}),e("estimatedTime","20-30 minutes")]}),(0,d.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,d.jsx)("i",{className:"fas fa-list text-green-500 mr-1"}),e("fields","35+ fields")]}),(0,d.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,d.jsx)("i",{className:"fas fa-language text-purple-500 mr-1"}),e("bilingual","Bilingual")]})]})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-emerald-400 to-cyan-400 text-white px-4 py-2 rounded-full shadow-lg",children:[(0,d.jsx)("i",{className:"fas fa-graduation-cap mr-2"}),e("education","Education")]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-orange-400 to-pink-400 text-white px-3 py-1 rounded-full text-sm shadow-md",children:[(0,d.jsx)("i",{className:"fas fa-heart mr-1"}),e("specialNeeds","Special Needs")]})]})]})})})}),(0,d.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),(()=>{const a={};return m.assessmentType||(a.assessmentType=e("assessmentTypeRequired","Assessment type is required")),m.selfCareCapability||(a.selfCareCapability=e("selfCareCapabilityRequired","Self-care capability is required")),m.selfCareMotivation||(a.selfCareMotivation=e("selfCareMotivationRequired","Self-care motivation is required")),m.patientAttend||(a.patientAttend=e("patientAttendRequired","Patient attend field is required")),m.problemsAttending||(a.problemsAttending=e("problemsAttendingRequired","Problems attending field is required")),m.literacySkills||(a.literacySkills=e("literacySkillsRequired","Literacy skills field is required")),m.therapistName.trim()||(a.therapistName=e("therapistNameRequired","Therapist name is required")),m.signature.trim()||(a.signature=e("signatureRequired","Signature is required")),m.badgeNo.trim()||(a.badgeNo=e("badgeNoRequired","Badge number is required")),p(a),0===Object.keys(a).length})())try{b(!0);const a=(0,t.A)((0,t.A)({},m),{},{submittedBy:r.id,submittedAt:(new Date).toISOString()});if(!(await fetch("/api/v1/patient-family-education/public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok)throw new Error("Failed to save education form");alert(e("educationFormSaved","Patient and Family Education form saved successfully!")),c(o?"/patients/".concat(o):"/patients")}catch(l){console.error("Error saving education form:",l),alert(e("errorSaving","Error saving education form. Please try again."))}finally{b(!1)}},className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg shadow-lg border border-cyan-200 dark:border-cyan-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-cyan-900 dark:text-cyan-100 mb-4 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-file-alt text-cyan-600 dark:text-cyan-400 mr-2"}),e("documentInformation","Document Information")]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-cyan-800/20 border border-cyan-200 dark:border-cyan-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-cyan-800 dark:text-cyan-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-hashtag text-cyan-600 dark:text-cyan-400 mr-1"}),e("documentNumber","Document Number")]}),(0,d.jsx)("input",{type:"text",value:m.documentNumber,onChange:e=>B("documentNumber",e.target.value),className:"w-full px-2 py-1 border border-cyan-300 dark:border-cyan-600 rounded bg-cyan-50 dark:bg-cyan-700 text-cyan-900 dark:text-cyan-100",readOnly:!0})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1"}),e("issueDate","Issue Date")]}),(0,d.jsx)("input",{type:"date",value:m.issueDate,onChange:e=>B("issueDate",e.target.value),className:"w-full px-2 py-1 border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-code-branch text-indigo-600 dark:text-indigo-400 mr-1"}),e("version","Version")]}),(0,d.jsx)("input",{type:"text",value:m.version,className:"w-full px-2 py-1 border border-indigo-300 dark:border-indigo-600 rounded bg-indigo-50 dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100",readOnly:!0})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-eye text-purple-600 dark:text-purple-400 mr-1"}),e("reviewNumber","Review Number")]}),(0,d.jsx)("input",{type:"text",value:m.reviewNumber,className:"w-full px-2 py-1 border border-purple-300 dark:border-purple-600 rounded bg-purple-50 dark:bg-purple-700 text-purple-900 dark:text-purple-100",readOnly:!0})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-purple-200 dark:border-purple-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-purple-900 dark:text-purple-100 mb-4 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-clipboard-check text-purple-600 dark:text-purple-400 mr-2"}),e("assessmentType","Assessment Type")]}),(0,d.jsx)("div",{className:"bg-purple-100 dark:bg-purple-800/30 border border-purple-300 dark:border-purple-600 rounded-lg p-4 mb-4",children:(0,d.jsxs)("p",{className:"text-sm text-purple-800 dark:text-purple-200",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-purple-600 dark:text-purple-400 mr-2"}),e("selectAssessmentTypeInstruction","Select the type of assessment being conducted for this patient education session.")]})}),(0,d.jsx)("div",{className:"flex flex-wrap gap-4",children:h.map(e=>(0,d.jsxs)("div",{className:"flex items-center bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg px-4 py-2",children:[(0,d.jsx)("input",{type:"radio",id:"assessment-".concat(e.value),name:"assessmentType",value:e.value,checked:m.assessmentType===e.value,onChange:e=>B("assessmentType",e.target.value),className:"mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 border-purple-300"}),(0,d.jsx)("label",{htmlFor:"assessment-".concat(e.value),className:"text-sm text-purple-800 dark:text-purple-200 font-medium",children:e.label})]},e.value))}),x.assessmentType&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),x.assessmentType]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-user-graduate text-blue-600 dark:text-blue-400 mr-2"}),e("patientLearningAndBarriers","Patient Learning Preferences and Barriers")]}),(0,d.jsx)("div",{className:"bg-blue-100 dark:bg-blue-800/30 border border-blue-300 dark:border-blue-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:[(0,d.jsx)("i",{className:"fas fa-lightbulb text-blue-600 dark:text-blue-400 mr-2"}),e("patientLearningInstruction","Identify how the patient learns best and any barriers that may affect their ability to understand and follow treatment instructions.")]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"text-md font-medium text-green-900 dark:text-green-100 mb-4 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-brain text-green-600 dark:text-green-400 mr-2"}),e("patientLearningPreference","Patient Learning Preference")]}),(0,d.jsxs)("div",{className:"space-y-2",children:[v.map(e=>(0,d.jsxs)("div",{className:"flex items-start bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg px-3 py-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"patient-pref-".concat(e.value),checked:m.patientLearningPreference.includes(e.value),onChange:a=>T("patientLearningPreference",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-green-600 focus:ring-green-500 border-green-300 rounded"}),(0,d.jsx)("label",{htmlFor:"patient-pref-".concat(e.value),className:"text-sm text-green-800 dark:text-green-200 font-medium",children:e.label})]},e.value)),m.patientLearningPreference.includes("other")&&(0,d.jsx)("textarea",{value:m.patientLearningOther,onChange:e=>B("patientLearningOther",e.target.value),placeholder:e("specifyOther","Specify other..."),className:"w-full mt-2 px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500",rows:"2"})]})]}),(0,d.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"text-md font-medium text-red-900 dark:text-red-100 mb-4 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2"}),e("patientBarriers","Patient Barriers")]}),(0,d.jsxs)("div",{className:"space-y-2",children:[f.map(e=>(0,d.jsxs)("div",{className:"flex items-start bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg px-3 py-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"patient-barrier-".concat(e.value),checked:m.patientBarriers.includes(e.value),onChange:a=>T("patientBarriers",e.value,a.target.checked),disabled:m.patientBarriers.includes("no_barrier")&&"no_barrier"!==e.value,className:"mt-1 mr-2 h-4 w-4 text-red-600 focus:ring-red-500 border-red-300 rounded"}),(0,d.jsx)("label",{htmlFor:"patient-barrier-".concat(e.value),className:"text-sm font-medium ".concat(m.patientBarriers.includes("no_barrier")&&"no_barrier"!==e.value?"text-red-400 dark:text-red-500":"text-red-800 dark:text-red-200"),children:e.label})]},e.value)),m.patientBarriers.includes("others")&&(0,d.jsx)("textarea",{value:m.patientBarriersOther,onChange:e=>B("patientBarriersOther",e.target.value),placeholder:e("specifyOther","Specify other..."),className:"w-full mt-2 px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500",rows:"2"})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-orange-900 dark:text-orange-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-users text-orange-600 dark:text-orange-400 mr-2"}),e("caregiverLearningAndBarriers","Caregiver Learning Preferences and Barriers")]}),(0,d.jsx)("div",{className:"bg-orange-100 dark:bg-orange-800/30 border border-orange-300 dark:border-orange-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-orange-800 dark:text-orange-200",children:[(0,d.jsx)("i",{className:"fas fa-heart text-orange-600 dark:text-orange-400 mr-2"}),e("caregiverLearningInstruction","Assess the caregiver's learning preferences and identify any barriers that may affect their ability to support the patient's care.")]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("caregiverLearningPreference","Caregiver Learning Preferences")}),(0,d.jsxs)("div",{className:"space-y-2",children:[v.map(e=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",id:"caregiver-pref-".concat(e.value),checked:m.caregiverLearningPreference.includes(e.value),onChange:a=>T("caregiverLearningPreference",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"caregiver-pref-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)),m.caregiverLearningPreference.includes("other")&&(0,d.jsx)("textarea",{value:m.caregiverLearningOther,onChange:e=>B("caregiverLearningOther",e.target.value),placeholder:e("specifyOther","Specify other..."),className:"w-full mt-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("caregiverBarriers","Caregiver Barriers")}),(0,d.jsxs)("div",{className:"space-y-2",children:[f.map(e=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",id:"caregiver-barrier-".concat(e.value),checked:m.caregiverBarriers.includes(e.value),onChange:a=>T("caregiverBarriers",e.value,a.target.checked),disabled:m.caregiverBarriers.includes("no_barrier")&&"no_barrier"!==e.value,className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"caregiver-barrier-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)),m.caregiverBarriers.includes("others")&&(0,d.jsx)("textarea",{value:m.caregiverBarriersOther,onChange:e=>B("caregiverBarriersOther",e.target.value),placeholder:e("specifyOther","Specify other..."),className:"w-full mt-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-teal-50 to-emerald-50 dark:from-teal-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-teal-200 dark:border-teal-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-teal-900 dark:text-teal-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-user-check text-teal-600 dark:text-teal-400 mr-2"}),e("patientSelfCare","Patient Self-Care Assessment")]}),(0,d.jsx)("div",{className:"bg-teal-100 dark:bg-teal-800/30 border border-teal-300 dark:border-teal-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-teal-800 dark:text-teal-200",children:[(0,d.jsx)("i",{className:"fas fa-hand-holding-heart text-teal-600 dark:text-teal-400 mr-2"}),e("selfCareInstruction","Evaluate the patient's ability and motivation to perform self-care activities independently.")]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"text-md font-medium text-indigo-900 dark:text-indigo-100 mb-4 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-tasks text-indigo-600 dark:text-indigo-400 mr-2"}),e("patientSelfCareCapability","Patient Self-Care Capability")]}),(0,d.jsx)("div",{className:"space-y-2",children:y.map(e=>(0,d.jsxs)("div",{className:"flex items-center bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg px-3 py-2",children:[(0,d.jsx)("input",{type:"radio",id:"capability-".concat(e.value),name:"selfCareCapability",value:e.value,checked:m.selfCareCapability===e.value,onChange:e=>B("selfCareCapability",e.target.value),className:"mr-2 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-indigo-300"}),(0,d.jsx)("label",{htmlFor:"capability-".concat(e.value),className:"text-sm text-indigo-800 dark:text-indigo-200 font-medium",children:e.label})]},e.value))}),"not_applicable"===m.selfCareCapability&&(0,d.jsxs)("div",{className:"mt-3",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("reason","Reason")}),(0,d.jsx)("textarea",{value:m.selfCareCapabilityReason,onChange:e=>B("selfCareCapabilityReason",e.target.value),placeholder:e("specifyReason","Specify reason..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})]}),x.selfCareCapability&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-2",children:x.selfCareCapability})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("patientSelfCareMotivation","Patient Self-Care Motivation")}),(0,d.jsx)("div",{className:"space-y-2",children:k.map(e=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",id:"motivation-".concat(e.value),name:"selfCareMotivation",value:e.value,checked:m.selfCareMotivation===e.value,onChange:e=>B("selfCareMotivation",e.target.value),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,d.jsx)("label",{htmlFor:"motivation-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))}),"not_applicable"===m.selfCareMotivation&&(0,d.jsxs)("div",{className:"mt-3",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("reason","Reason")}),(0,d.jsx)("textarea",{value:m.selfCareMotivationReason,onChange:e=>B("selfCareMotivationReason",e.target.value),placeholder:e("specifyReason","Specify reason..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})]}),x.selfCareMotivation&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-2",children:x.selfCareMotivation})]})]}),(0,d.jsxs)("div",{className:"mt-6",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("comments","Comments")}),(0,d.jsx)("textarea",{value:m.comments,onChange:e=>B("comments",e.target.value),placeholder:e("additionalComments","Additional comments..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"3"})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 rounded-lg shadow-lg border border-pink-200 dark:border-pink-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-pink-900 dark:text-pink-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-book-open text-pink-600 dark:text-pink-400 mr-2"}),e("educationalNeeds","Educational Needs")]}),(0,d.jsx)("div",{className:"bg-pink-100 dark:bg-pink-800/30 border border-pink-300 dark:border-pink-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-pink-800 dark:text-pink-200",children:[(0,d.jsx)("i",{className:"fas fa-graduation-cap text-pink-600 dark:text-pink-400 mr-2"}),e("educationalNeedsInstruction","Select all educational topics that the patient and family need to learn about for optimal care and recovery.")]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:N.map(e=>(0,d.jsxs)("div",{className:"flex items-start bg-white dark:bg-pink-800/20 border border-pink-200 dark:border-pink-600 rounded-lg px-3 py-2",children:[(0,d.jsx)("input",{type:"checkbox",id:"edu-need-".concat(e.value),checked:m.educationalNeeds.includes(e.value),onChange:a=>T("educationalNeeds",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-pink-600 focus:ring-pink-500 border-pink-300 rounded"}),(0,d.jsx)("label",{htmlFor:"edu-need-".concat(e.value),className:"text-sm text-pink-800 dark:text-pink-200 font-medium",children:e.label})]},e.value))}),m.educationalNeeds.includes("others")&&(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("otherEducationalNeeds","Other Educational Needs")}),(0,d.jsx)("textarea",{value:m.educationalNeedsOther,onChange:e=>B("educationalNeedsOther",e.target.value),placeholder:e("specifyOther","Specify other..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-violet-200 dark:border-violet-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-violet-900 dark:text-violet-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-chalkboard-teacher text-violet-600 dark:text-violet-400 mr-2"}),e("givenEducation","Given Education")]}),(0,d.jsx)("div",{className:"bg-violet-100 dark:bg-violet-800/30 border border-violet-300 dark:border-violet-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-violet-800 dark:text-violet-200",children:[(0,d.jsx)("i",{className:"fas fa-clipboard-list text-violet-600 dark:text-violet-400 mr-2"}),e("givenEducationInstruction","Document the teaching methods and tools used to provide education to the patient and family.")]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("teachingMethod","Teaching Method")}),(0,d.jsx)("div",{className:"space-y-2",children:j.map(e=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",id:"method-".concat(e.value),checked:m.teachingMethod.includes(e.value),onChange:a=>T("teachingMethod",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"method-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("teachingTool","Teaching Tool")}),(0,d.jsx)("div",{className:"space-y-2",children:w.map(e=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",id:"tool-".concat(e.value),checked:m.teachingTool.includes(e.value),onChange:a=>T("teachingTool",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"tool-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-emerald-900 dark:text-emerald-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-chart-line text-emerald-600 dark:text-emerald-400 mr-2"}),e("evaluation","Evaluation")]}),(0,d.jsx)("div",{className:"bg-emerald-100 dark:bg-emerald-800/30 border border-emerald-300 dark:border-emerald-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-emerald-800 dark:text-emerald-200",children:[(0,d.jsx)("i",{className:"fas fa-check-circle text-emerald-600 dark:text-emerald-400 mr-2"}),e("evaluationInstruction","Assess the patient's understanding and ability to apply the education provided.")]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:C.map(e=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",id:"eval-".concat(e.value),checked:m.evaluation.includes(e.value),onChange:a=>T("evaluation",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"eval-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg shadow-lg border border-amber-200 dark:border-amber-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-amber-900 dark:text-amber-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-question-circle text-amber-600 dark:text-amber-400 mr-2"}),e("additionalQuestions","Additional Questions")]}),(0,d.jsx)("div",{className:"bg-amber-100 dark:bg-amber-800/30 border border-amber-300 dark:border-amber-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-amber-800 dark:text-amber-200",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-amber-600 dark:text-amber-400 mr-2"}),e("additionalQuestionsInstruction","Complete these additional assessments to better understand the patient's circumstances and needs.")]})}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:e("patientAttend","Patient Attend")}),(0,d.jsx)("div",{className:"flex flex-wrap gap-4",children:A.map(e=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",id:"attend-".concat(e.value),name:"patientAttend",value:e.value,checked:m.patientAttend===e.value,onChange:e=>B("patientAttend",e.target.value),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,d.jsx)("label",{htmlFor:"attend-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))}),x.patientAttend&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-2",children:x.patientAttend})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:e("problemsAttendingSchoolWork","Does the Patient Have Any Problems Attending School / Work?")}),(0,d.jsx)("div",{className:"flex flex-wrap gap-4",children:P.map(e=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",id:"problems-".concat(e.value),name:"problemsAttending",value:e.value,checked:m.problemsAttending===e.value,onChange:e=>B("problemsAttending",e.target.value),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,d.jsx)("label",{htmlFor:"problems-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))}),x.problemsAttending&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-2",children:x.problemsAttending})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:e("literacySkills","Literacy Skills")}),(0,d.jsx)("div",{className:"flex flex-wrap gap-4",children:S.map(e=>(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",id:"literacy-".concat(e.value),name:"literacySkills",value:e.value,checked:m.literacySkills===e.value,onChange:e=>B("literacySkills",e.target.value),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),(0,d.jsx)("label",{htmlFor:"literacy-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))}),x.literacySkills&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-2",children:x.literacySkills})]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("whoWillProvideCareAfterDischarge","Who Will Provide Care After Discharge?")}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:_.map(e=>(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",id:"care-provider-".concat(e.value),checked:m.careProviderAfterDischarge.includes(e.value),onChange:a=>T("careProviderAfterDischarge",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{htmlFor:"care-provider-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))}),m.careProviderAfterDischarge.includes("others")&&(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("otherCareProvider","Other Care Provider")}),(0,d.jsx)("textarea",{value:m.careProviderOther,onChange:e=>B("careProviderOther",e.target.value),placeholder:e("specifyOther","Specify other..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("plansAndComments","Plans and Comments")}),(0,d.jsx)("textarea",{value:m.plansComments,onChange:e=>B("plansComments",e.target.value),placeholder:e("plansCommentsPlaceholder","Enter plans and additional comments..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"4"})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/20 dark:to-gray-900/20 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-slate-900 dark:text-slate-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-signature text-slate-600 dark:text-slate-400 mr-2"}),e("therapistSignature","Therapist Signature")]}),(0,d.jsx)("div",{className:"bg-slate-100 dark:bg-slate-800/30 border border-slate-300 dark:border-slate-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-slate-800 dark:text-slate-200",children:[(0,d.jsx)("i",{className:"fas fa-pen-fancy text-slate-600 dark:text-slate-400 mr-2"}),e("signatureInstruction","Complete the therapist signature section to validate this education assessment and documentation.")]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("therapistName","Therapist Name")," *"]}),(0,d.jsx)("input",{type:"text",value:m.therapistName,onChange:e=>B("therapistName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(x.therapistName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),x.therapistName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:x.therapistName})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("signature","Signature")," *"]}),(0,d.jsx)("input",{type:"text",value:m.signature,onChange:e=>B("signature",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(x.signature?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),x.signature&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:x.signature})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("badgeNo","Badge No.")," *"]}),(0,d.jsx)("input",{type:"text",value:m.badgeNo,onChange:e=>B("badgeNo",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(x.badgeNo?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),x.badgeNo&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:x.badgeNo})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("date","Date")}),(0,d.jsx)("input",{type:"date",value:m.date,onChange:e=>B("date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("button",{type:"button",onClick:()=>c(-1),className:"px-6 py-3 bg-gradient-to-r from-gray-500 to-slate-500 text-white rounded-lg hover:from-gray-600 hover:to-slate-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-arrow-left mr-2"}),e("cancel","Cancel")]}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("button",{type:"button",onClick:async()=>{try{b(!0);const a=(0,t.A)((0,t.A)({},m),{},{generatedAt:(new Date).toISOString(),generatedBy:r.name||r.email,patientId:o}),l=await fetch("/api/v1/patient-family-education/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(a)});if(!l.ok)throw new Error("HTTP error! status: ".concat(l.status));{const a=await l.blob(),r=window.URL.createObjectURL(a),t=document.createElement("a");t.href=r,t.download="patient-family-education-".concat(m.therapistName.replace(/\s+/g,"-"),"-").concat(m.date,".pdf"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(r),document.body.removeChild(t),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(a){console.error("Error generating PDF:",a),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{b(!1)}},disabled:u,className:"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg",children:[(0,d.jsx)("i",{className:"fas fa-file-pdf mr-2"}),u?e("generating","Generating..."):e("generatePDF","Generate PDF")]}),(0,d.jsxs)("button",{type:"submit",disabled:u,className:"px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg",children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),u?e("saving","Saving..."):e("saveEducationForm","Save Education Form")]})]})]})})]})]})}}}]);
//# sourceMappingURL=5896.9f8b2439.chunk.js.map