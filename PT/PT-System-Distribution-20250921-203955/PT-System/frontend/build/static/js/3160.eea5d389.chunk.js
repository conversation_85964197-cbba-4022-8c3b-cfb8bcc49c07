"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3160],{3160:(e,s,t)=>{t.r(s),t.d(s,{default:()=>i});var r=t(5043),l=t(3216),a=t(7921),n=t(579);const i=()=>{const{t:e,isRTL:s}=(0,a.o)(),t=(0,l.Zp)(),[i,d]=(0,r.useState)(!0);if((0,r.useEffect)(()=>{const e=setTimeout(()=>{d(!1)},1e3);return()=>clearTimeout(e)},[]),i)return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-600 text-lg",children:"Loading Dashboard..."})]})});const o=e=>{switch(e){case"addPatient":t("/patients/add");break;case"scheduleAppointment":t("/appointments/new");break;case"createAssessment":t("/forms/pt-assessment")}};return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:s?"\u0644\u0648\u062d\u0629 \u0627\u0644\u062a\u062d\u0643\u0645":"Dashboard"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:s?"\u0645\u0631\u062d\u0628\u0627\u064b \u0628\u0643 \u0641\u064a \u0646\u0638\u0627\u0645 \u0641\u064a\u0632\u064a\u0648\u0641\u0644\u0648 \u0644\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a":"Welcome to PhysioFlow PT System"})]}),(0,n.jsxs)("div",{className:"mt-4 sm:mt-0 text-sm text-gray-500",children:[(0,n.jsx)("p",{className:"text-sm text-gray-500",children:(new Date).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}),(0,n.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(new Date).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,n.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Patients"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"1,234"}),(0,n.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"\u2197 +12% from last month"})]})]})}),(0,n.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Therapies"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"89"}),(0,n.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"\u2197 +5% from last week"})]})]})}),(0,n.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Today's Appointments"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"12"}),(0,n.jsx)("p",{className:"text-xs text-blue-600 mt-1",children:"3 upcoming"})]})]})}),(0,n.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Revenue"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"$328,000"}),(0,n.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"\u2197 +8% from last month"})]})]})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,n.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Quick Actions"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("button",{onClick:()=>o("addPatient"),className:"w-full flex items-center p-4 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors group",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,n.jsx)("span",{className:"ml-3 font-medium text-gray-900",children:"Add New Patient"})]}),(0,n.jsxs)("button",{onClick:()=>o("scheduleAppointment"),className:"w-full flex items-center p-4 rounded-lg bg-green-50 hover:bg-green-100 transition-colors group",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),(0,n.jsx)("span",{className:"ml-3 font-medium text-gray-900",children:"Schedule Appointment"})]}),(0,n.jsxs)("button",{onClick:()=>o("createAssessment"),className:"w-full flex items-center p-4 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors group",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,n.jsx)("span",{className:"ml-3 font-medium text-gray-900",children:"Create Assessment"})]})]})]}),(0,n.jsxs)("div",{className:"lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"Recent Activity"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),(0,n.jsxs)("div",{className:"ml-4 flex-1",children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:"New patient registered"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Ahmed Al-Rashid - 2 hours ago"})]}),(0,n.jsx)("div",{className:"text-xs text-gray-400",children:"14:30"})]}),(0,n.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,n.jsxs)("div",{className:"ml-4 flex-1",children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:"Treatment session completed"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Sarah Al-Harbi - 4 hours ago"})]}),(0,n.jsx)("div",{className:"text-xs text-gray-400",children:"12:30"})]}),(0,n.jsxs)("div",{className:"flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,n.jsx)("div",{className:"w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,n.jsx)("svg",{className:"w-5 h-5 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),(0,n.jsxs)("div",{className:"ml-4 flex-1",children:[(0,n.jsx)("p",{className:"font-medium text-gray-900",children:"Appointment scheduled"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Mohammed Al-Ahmad - 6 hours ago"})]}),(0,n.jsx)("div",{className:"text-xs text-gray-400",children:"10:30"})]})]})]})]})]})}}}]);
//# sourceMappingURL=3160.eea5d389.chunk.js.map