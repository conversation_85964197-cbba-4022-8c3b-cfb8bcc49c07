{"version": 3, "file": "static/js/9277.300ca70a.chunk.js", "mappings": "wJAwZA,MAEA,EAFoB,IArZpB,MACEA,WAAAA,GACEC,KAAKC,QAAUC,+BACfF,KAAKG,cAAgB,cACrBH,KAAKI,mBACP,CAGAC,YAAAA,GACE,OAAOC,aAAaC,QAAQ,UAAY,YAC1C,CAGA,gBAAMC,CAAWC,GAAyB,IAAfC,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpC,MAAMG,EAAQd,KAAKK,eACbU,EAAG,GAAAC,OAAMhB,KAAKC,SAAOe,OAAGP,GAExBQ,EAAiB,CACrBC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADF,OAAYF,KAIzBK,GAAYC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbH,GACAP,GAAO,IACVQ,SAAOE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACFH,EAAeC,SACfR,EAAQQ,WAIf,IACE,MAAMG,QAAiBC,MAAMP,EAAKI,GAElC,IAAKE,EAASE,GACZ,MAAM,IAAIC,MAAM,uBAADR,OAAwBK,EAASI,SAIlD,aADmBJ,EAASK,MAE9B,CAAE,MAAOC,GAGP,OAFAC,QAAQC,KAAK,0BAADb,OAA2BP,EAAQ,kCAAkCkB,EAAMG,SAEhF9B,KAAK+B,uBAAuBtB,EAAUC,EAC/C,CACF,CAGAqB,sBAAAA,CAAuBtB,EAAUC,GAC/B,MAAMsB,EAAStB,EAAQsB,QAAU,MAC3BC,EAAOvB,EAAQwB,KAAOC,KAAKC,MAAM1B,EAAQwB,MAAQ,KAGvD,OAAIzB,EAAS4B,SAAS,YACbrC,KAAKsC,sBAAsB7B,EAAUuB,EAAQC,GAC3CxB,EAAS4B,SAAS,kBACpBrC,KAAKuC,4BAA4B9B,EAAUuB,EAAQC,GACjDxB,EAAS4B,SAAS,sBACpBrC,KAAKwC,uBAAuB/B,EAAUuB,EAAQC,GAC5CxB,EAAS4B,SAAS,oBACpBrC,KAAKyC,iBAAiBhC,EAAUuB,EAAQC,GAG1C,CAAES,SAAS,EAAOf,MAAO,0CAClC,CAGAvB,iBAAAA,GACOJ,KAAKO,QAAQ,iBAChBP,KAAK2C,QAAQ,eAAe,GAC5B3C,KAAK2C,QAAQ,WAAY3C,KAAK4C,sBAC9B5C,KAAK2C,QAAQ,cAAe,CAAC,GAC7B3C,KAAK2C,QAAQ,uBAAwB,IACrC3C,KAAK2C,QAAQ,mBAAoB,IACjC3C,KAAK2C,QAAQ,iBAAkB,IAEnC,CAGApC,OAAAA,CAAQsC,GACN,IACE,MAAMC,EAAOxC,aAAaC,QAAQP,KAAKG,cAAgB0C,GACvD,OAAOC,EAAOX,KAAKC,MAAMU,GAAQ,IACnC,CAAE,MAAOnB,GAEP,OADAC,QAAQD,MAAM,mCAAoCA,GAC3C,IACT,CACF,CAEAgB,OAAAA,CAAQE,EAAKE,GACX,IAEE,OADAzC,aAAaqC,QAAQ3C,KAAKG,cAAgB0C,EAAKV,KAAKa,UAAUD,KACvD,CACT,CAAE,MAAOpB,GAEP,OADAC,QAAQD,MAAM,iCAAkCA,IACzC,CACT,CACF,CAEAsB,UAAAA,CAAWJ,GACT,IAEE,OADAvC,aAAa2C,WAAWjD,KAAKG,cAAgB0C,IACtC,CACT,CAAE,MAAOlB,GAEP,OADAC,QAAQD,MAAM,oCAAqCA,IAC5C,CACT,CACF,CAGAiB,kBAAAA,GACE,MAAO,CACL,CACEM,GAAI,mBACJC,KAAM,iBACNC,OAAQ,oDACRC,IAAK,GACLC,OAAQ,OACRC,UAAW,iBACXC,YAAa,4EACbC,MAAO,gBACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,YAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAExB,CACEjB,GAAI,mBACJC,KAAM,gBACNC,OAAQ,gEACRC,IAAK,GACLC,OAAQ,SACRC,UAAW,qBACXC,YAAa,qGACbC,MAAO,cACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,OAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAG5B,CAGA,iBAAMC,GACJ,OAAO,IAAIC,QAASC,IAClBC,WAAW,KACTD,EAAQtE,KAAKO,QAAQ,aAAe,KACnC,MAEP,CAEA,gBAAMiE,CAAWC,GACf,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MACMG,GADW1E,KAAKO,QAAQ,aAAe,IACpBoE,KAAKC,GAAKA,EAAE1B,KAAOuB,GAC5CH,EAAQI,GAAW,OAClB,MAEP,CAGA,qBAAMG,CAAgBJ,EAAWK,GAC/B,IACE,MAAMzD,QAAiBrB,KAAKQ,WAAW,WAAY,CACjDwB,OAAQ,OACRE,KAAMC,KAAKa,WAAS5B,EAAAA,EAAAA,GAAC,CACnBqD,aACGK,MAGP,OAAOzD,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvDwE,EAAeN,IAAUrD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpB0D,GAAW,IACdE,WAAW,IAAId,MAAOC,gBAExBnE,KAAK2C,QAAQ,cAAeoC,GAC5BT,GAAQ,IACP,MAEP,CACF,CAEA,oBAAMW,CAAeR,GACnB,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvD+D,EAAQS,EAAeN,IAAc,OACpC,MAEP,CAGA,iBAAMS,CAAYC,GAChB,IACE,MAAM9D,QAAiBrB,KAAKQ,WAAW,iBAAkB,CACvDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUmC,KAEvB,OAAO9D,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMa,EAAUpF,KAAKO,QAAQ,yBAA2B,GAClDuB,GAAOV,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZH,GAAW,IACdH,WAAW,IAAId,MAAOC,cACtB1C,OAAQ,SAEV2D,EAAQG,KAAKzD,GACb9B,KAAK2C,QAAQ,uBAAwByC,GACrCd,EAAQxC,IACP,MAEP,CACF,CAEA,6BAAM0D,CAAwBf,GAC5B,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,kBAADQ,OAAmByD,IACzD,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACMkB,GADUzF,KAAKO,QAAQ,yBAA2B,IACzBmF,OAAOC,GAAOA,EAAIlB,YAAcA,GAC/DH,EAAQmB,IACP,MAEP,CACF,CAGA,yBAAMG,CAAoBC,GACxB,IACE,MAAMxE,QAAiBrB,KAAKQ,WAAW,qBAAsB,CAC3DwB,OAAQ,OACRE,KAAMC,KAAKa,UAAU6C,KAEvB,OAAOxE,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMuB,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CwF,GAAO3E,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZO,GAAW,IACd5B,WAAW,IAAIC,MAAOC,gBAExB2B,EAASP,KAAKQ,GACd/F,KAAK2C,QAAQ,mBAAoBmD,GACjCxB,EAAQyB,IACP,MAEP,CACF,CAEA,yBAAMC,CAAoBvB,GACxB,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,sBAADQ,OAAuByD,IAC7D,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACM0B,GADWjG,KAAKO,QAAQ,qBAAuB,IACpBmF,OAAOQ,GAAQA,EAAKzB,YAAcA,GACnEH,EAAQ2B,IACP,MAEP,CACF,CAGA,uBAAME,CAAkBC,GACtB,IACE,MAAM/E,QAAiBrB,KAAKQ,WAAW,mBAAoB,CACzDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUoD,KAEvB,OAAO/E,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjD+F,GAAWlF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACf8B,GAAIgB,KAAKmB,MAAMC,YACZc,GAAe,IAClBpB,WAAW,IAAId,MAAOC,gBAExBkC,EAAad,KAAKe,GAClBtG,KAAK2C,QAAQ,iBAAkB0D,GAC/B/B,EAAQgC,IACP,MAEP,CACF,CAEA,uBAAMC,CAAkB9B,GACtB,IACE,MAAMhE,EAAWgE,EAAS,oBAAAzD,OAAuByD,GAAc,mBACzDpD,QAAiBrB,KAAKQ,WAAWC,GACvC,OAAOY,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDiG,EAAsB/B,EACxB4B,EAAaX,OAAOe,GAAOA,EAAIhC,YAAcA,GAC7C4B,EACJ/B,EAAQkC,IACP,MAEP,CACF,CAGA,kBAAME,GACJ,OAAO,IAAIrC,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDoG,EAAiB3G,KAAKO,QAAQ,yBAA2B,GACzDuF,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CuE,EAAc9E,KAAKO,QAAQ,gBAAkB,CAAC,EAE9CqG,EAAY,CAChBC,eAAgBR,EAAazF,OAC7BkG,oBAAqBH,EAAe/F,OACpCmG,sBAAuBjB,EAASlF,OAChCoG,wBAAyBC,OAAOC,KAAKpC,GAAalE,OAClDuG,eAAgB,IACXd,EAAae,OAAO,GAAGC,IAAIC,IAAClG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,MAASD,OAClDX,EAAeS,OAAO,GAAGC,IAAIG,IAACpG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,iBAAoBC,OAC/D1B,EAASsB,OAAO,GAAGC,IAAIzC,IAACxD,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,YAAe3C,KACvD6C,KAAK,CAACC,EAAGC,IAAM,IAAIzD,KAAKyD,EAAE3C,WAAa,IAAId,KAAKwD,EAAE1C,YAAYoC,MAAM,EAAG,KAG3E9C,EAAQsC,IACP,MAEP,CAGAgB,YAAAA,GACe,CAAC,WAAY,cAAe,uBAAwB,mBAAoB,kBAChFC,QAAQhF,GAAO7C,KAAKiD,WAAWJ,IACpC7C,KAAKiD,WAAW,eAChBjD,KAAKI,mBACP,CAGA0H,UAAAA,GASE,MARa,CACXC,SAAU/H,KAAKO,QAAQ,YACvBuE,YAAa9E,KAAKO,QAAQ,eAC1ByH,qBAAsBhI,KAAKO,QAAQ,wBACnC0H,iBAAkBjI,KAAKO,QAAQ,oBAC/B2H,eAAgBlI,KAAKO,QAAQ,kBAC7B4H,YAAY,IAAIjE,MAAOC,cAG3B,CAGAiE,UAAAA,CAAWnG,GACT,IAME,OALIA,EAAK8F,UAAU/H,KAAK2C,QAAQ,WAAYV,EAAK8F,UAC7C9F,EAAK6C,aAAa9E,KAAK2C,QAAQ,cAAeV,EAAK6C,aACnD7C,EAAK+F,sBAAsBhI,KAAK2C,QAAQ,uBAAwBV,EAAK+F,sBACrE/F,EAAKgG,kBAAkBjI,KAAK2C,QAAQ,mBAAoBV,EAAKgG,kBAC7DhG,EAAKiG,gBAAgBlI,KAAK2C,QAAQ,iBAAkBV,EAAKiG,iBACtD,CACT,CAAE,MAAOvG,GAEP,OADAC,QAAQD,MAAM,wBAAyBA,IAChC,CACT,CACF,CAGA0G,KAAAA,GAAiB,IAAXC,EAAE3H,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,IACT,OAAO,IAAI0D,QAAQC,GAAWC,WAAWD,EAASgE,GACpD,E,uHC7YF,MA+PA,EA/PoBC,KAAO,IAADC,EACxB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,UAAElE,IAAcmE,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OACVpE,EAASqE,IAAcC,EAAAA,EAAAA,UAAS,OAChCC,EAAmBC,IAAwBF,EAAAA,EAAAA,UAAS,KACpDG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAoBC,IAAyBN,EAAAA,EAAAA,UAAS,OAE7DO,EAAAA,EAAAA,WAAU,KACRC,IACAC,KACC,CAAChF,IAEJ,MAAM+E,EAAkBE,UACtB,GAAIjF,EACF,IACE,MAAMkF,QAAoBC,EAAAA,EAAYpF,WAAWC,GACjDsE,EAAWY,EACb,CAAE,MAAOhI,GACPC,QAAQD,MAAM,yBAA0BA,GACxCkI,EAAAA,GAAMlI,MAAM8G,EAAE,sBAAuB,8BACvC,CAEFW,GAAW,IAGPK,EAAwBC,UAC5B,GAAIjF,EACF,IACE,MAAMK,QAAoB8E,EAAAA,EAAY3E,eAAeR,GACjDK,GACFoE,EAAqB,CAACpE,GAE1B,CAAE,MAAOnD,GACPC,QAAQD,MAAM,oCAAqCA,EACrD,GAmBJ,OAAIwH,GAEAW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yDAAwDC,SAAA,EAErEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oFAAmFC,UAChGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMrB,GAAU,GACzBkB,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,mDAAkDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACZtB,EAAE,oBAAqB,0BAEzB/D,IACCuF,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wCAAuCC,SAAA,CACjDvB,EAAE,UAAW,WAAW,KAAGC,EAAQhE,EAAQtB,OAASsB,EAAQvB,KAC5DuB,EAAQnB,YACPuG,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2DAA0DC,SACvEtB,EAAQhE,EAAQlB,YAAckB,EAAQnB,sBAOnDuG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,MAAA,UACEC,QA7CYC,KAC1Bb,EAAsB,OA6CRS,UAAU,8GAA6GC,SAAA,EAEvHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iBACbD,EAAAA,EAAAA,KAAA,QAAAE,SAAOvB,EAAE,gBAAiB,mCAQtCqB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEvB,EAAE,qBAAsB,2BAE3BwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,uEAAsEC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCACZtB,EAAE,aAAc,kBAEnBwB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yEAAwEC,SAAA,EACtFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZtB,EAAE,eAAgB,0BAKzBqB,EAAAA,EAAAA,KAACM,EAAAA,EAAO,CACN3F,UAAWA,EACX4F,OA3FeX,UAC3B,UACQE,EAAAA,EAAY/E,gBAAgBJ,EAAWK,SACvC2E,IACNI,EAAAA,GAAMnH,QAAQ+F,EAAE,kBAAmB,iCACrC,CAAE,MAAO9G,GACPC,QAAQD,MAAM,2BAA4BA,GAC1CkI,EAAAA,GAAMlI,MAAM8G,EAAE,cAAe,2BAC/B,GAoFY6B,kBAAkB,EAClBC,iBAAmC,OAAlBlB,QAAkB,IAAlBA,OAAkB,EAAlBA,EAAoBkB,kBAAmB,GACxDC,YAA8B,OAAlBnB,QAAkB,IAAlBA,OAAkB,EAAlBA,EAAoBmB,aAAc,CAAC,UAMrDP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACZtB,EAAE,oBAAqB,yBAGzBQ,EAAkBrI,OAAS,GAC1BkJ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBf,EAAkB5B,IAAI,CAACoD,EAAYC,KAClCT,EAAAA,EAAAA,MAAA,OAEEF,UAAS,0DAAA/I,OACPqI,IAAuBoB,EACnB,iDACA,gFAENP,QAASA,IAAMZ,EAAsBmB,GAAYT,SAAA,EAEjDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,SAC/D,IAAI9F,KAAKuG,EAAWzF,WAAW2F,wBAElCV,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,CACnC/C,OAAOC,KAAKuD,EAAWF,iBAAmB,CAAC,GAAG3J,OAAO,IAAE6H,EAAE,UAAW,kBAGzEqB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,SAC3D,IAAI9F,KAAKuG,EAAWzF,WAAW4F,uBAEjCH,EAAWF,iBAAmBE,EAAWF,gBAAgB3J,OAAS,IACjEqJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,CAAEvB,EAAE,kBAAmB,oBAAoB,QACtFwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,CAClCS,EAAWF,gBAAgBnD,MAAM,EAAG,GAAGC,IAAI,CAACwD,EAAQC,KACnDhB,EAAAA,EAAAA,KAAA,QAEEC,UAAU,oDAAmDC,SAE5Da,GAHIC,IAMRL,EAAWF,gBAAgB3J,OAAS,IACnCqJ,EAAAA,EAAAA,MAAA,QAAMF,UAAU,wBAAuBC,SAAA,CAAC,IACpCS,EAAWF,gBAAgB3J,OAAS,EAAE,IAAE6H,EAAE,OAAQ,mBAjCzDiC,OA2CXT,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDACbD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDvB,EAAE,gBAAiB,yBAEtBqB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDvB,EAAE,mBAAoB,sDAO/BwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0DAAyDC,SAAA,EACtEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCACZtB,EAAE,aAAc,mBAGnBwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDvB,EAAE,mBAAoB,wBAEzBqB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDC,SAClEf,EAAkBrI,YAItByI,IACCY,EAAAA,EAAAA,MAAAc,EAAAA,SAAA,CAAAf,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDvB,EAAE,kBAAmB,uBAExBqB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDC,UAChC,QAAlCxB,EAAAa,EAAmBkB,uBAAe,IAAA/B,OAAA,EAAlCA,EAAoC5H,SAAU,QAInDqJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDvB,EAAE,eAAgB,qBAErBqB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDC,SAClEX,EAAmBmB,YACjBvD,OAAO+D,OAAO3B,EAAmBmB,YAAYS,OAAO,CAACvD,EAAGC,IAAMD,EAAIC,EAAG,GACrEV,OAAO+D,OAAO3B,EAAmBmB,YAAY5J,QAAQsK,QAAQ,GAAK,6B", "sources": ["services/dataService.js", "pages/BodyMap/BodyMapPage.jsx"], "sourcesContent": ["// Data Service for PhysioFlow\n// Handles API calls with localStorage fallback for demonstration purposes\n\nclass DataService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n    this.storagePrefix = 'physioflow_';\n    this.initializeStorage();\n  }\n\n  // Helper method to get auth token\n  getAuthToken() {\n    return localStorage.getItem('token') || 'demo-token';\n  }\n\n  // Helper method to make API requests\n  async apiRequest(endpoint, options = {}) {\n    const token = this.getAuthToken();\n    const url = `${this.baseURL}${endpoint}`;\n\n    const defaultOptions = {\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      }\n    };\n\n    const finalOptions = {\n      ...defaultOptions,\n      ...options,\n      headers: {\n        ...defaultOptions.headers,\n        ...options.headers\n      }\n    };\n\n    try {\n      const response = await fetch(url, finalOptions);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.warn(`API request failed for ${endpoint}, using localStorage fallback:`, error.message);\n      // Fallback to localStorage for demo purposes\n      return this.fallbackToLocalStorage(endpoint, options);\n    }\n  }\n\n  // Fallback to localStorage when API is not available\n  fallbackToLocalStorage(endpoint, options) {\n    const method = options.method || 'GET';\n    const data = options.body ? JSON.parse(options.body) : null;\n\n    // Handle different endpoints with existing localStorage methods\n    if (endpoint.includes('/bodymap')) {\n      return this.handleBodyMapFallback(endpoint, method, data);\n    } else if (endpoint.includes('/communication')) {\n      return this.handleCommunicationFallback(endpoint, method, data);\n    } else if (endpoint.includes('/exercise-programs')) {\n      return this.handleExerciseFallback(endpoint, method, data);\n    } else if (endpoint.includes('/ai-interactions')) {\n      return this.handleAIFallback(endpoint, method, data);\n    }\n\n    return { success: false, error: 'Endpoint not supported in fallback mode' };\n  }\n\n  // Initialize storage with demo data\n  initializeStorage() {\n    if (!this.getItem('initialized')) {\n      this.setItem('initialized', true);\n      this.setItem('patients', this.getDefaultPatients());\n      this.setItem('bodyMapData', {});\n      this.setItem('communicationHistory', []);\n      this.setItem('exercisePrograms', []);\n      this.setItem('aiInteractions', []);\n    }\n  }\n\n  // Storage helpers\n  getItem(key) {\n    try {\n      const item = localStorage.getItem(this.storagePrefix + key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Error getting item from storage:', error);\n      return null;\n    }\n  }\n\n  setItem(key, value) {\n    try {\n      localStorage.setItem(this.storagePrefix + key, JSON.stringify(value));\n      return true;\n    } catch (error) {\n      console.error('Error setting item in storage:', error);\n      return false;\n    }\n  }\n\n  removeItem(key) {\n    try {\n      localStorage.removeItem(this.storagePrefix + key);\n      return true;\n    } catch (error) {\n      console.error('Error removing item from storage:', error);\n      return false;\n    }\n  }\n\n  // Default demo data\n  getDefaultPatients() {\n    return [\n      {\n        id: 'demo-patient-001',\n        name: 'Ahmed Mohammed',\n        nameAr: 'أحمد محمد',\n        age: 28,\n        gender: 'male',\n        condition: 'Cerebral Palsy',\n        conditionAr: 'الشلل الدماغي',\n        phone: '+966501234567',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'whatsapp'],\n          language: 'en',\n          quietHours: { start: '22:00', end: '08:00' }\n        },\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 'demo-patient-002',\n        name: 'Sarah Johnson',\n        nameAr: 'سارة جونسون',\n        age: 35,\n        gender: 'female',\n        condition: 'Spinal Cord Injury',\n        conditionAr: 'إصابة الحبل الشوكي',\n        phone: '+**********',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'sms'],\n          language: 'en',\n          quietHours: { start: '21:00', end: '07:00' }\n        },\n        createdAt: new Date().toISOString()\n      }\n    ];\n  }\n\n  // Patient data methods\n  async getPatients() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve(this.getItem('patients') || []);\n      }, 100);\n    });\n  }\n\n  async getPatient(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const patients = this.getItem('patients') || [];\n        const patient = patients.find(p => p.id === patientId);\n        resolve(patient || null);\n      }, 100);\n    });\n  }\n\n  // Body Map data methods\n  async saveBodyMapData(patientId, bodyMapData) {\n    try {\n      const response = await this.apiRequest('/bodymap', {\n        method: 'POST',\n        body: JSON.stringify({\n          patientId,\n          ...bodyMapData\n        })\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const allBodyMapData = this.getItem('bodyMapData') || {};\n          allBodyMapData[patientId] = {\n            ...bodyMapData,\n            timestamp: new Date().toISOString()\n          };\n          this.setItem('bodyMapData', allBodyMapData);\n          resolve(true);\n        }, 200);\n      });\n    }\n  }\n\n  async getBodyMapData(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const allBodyMapData = this.getItem('bodyMapData') || {};\n        resolve(allBodyMapData[patientId] || null);\n      }, 100);\n    });\n  }\n\n  // Communication methods\n  async sendMessage(messageData) {\n    try {\n      const response = await this.apiRequest('/communication', {\n        method: 'POST',\n        body: JSON.stringify(messageData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const message = {\n            id: Date.now().toString(),\n            ...messageData,\n            timestamp: new Date().toISOString(),\n            status: 'sent'\n          };\n          history.push(message);\n          this.setItem('communicationHistory', history);\n          resolve(message);\n        }, 500);\n      });\n    }\n  }\n\n  async getCommunicationHistory(patientId) {\n    try {\n      const response = await this.apiRequest(`/communication/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const patientHistory = history.filter(msg => msg.patientId === patientId);\n          resolve(patientHistory);\n        }, 100);\n      });\n    }\n  }\n\n  // Exercise Program methods\n  async saveExerciseProgram(programData) {\n    try {\n      const response = await this.apiRequest('/exercise-programs', {\n        method: 'POST',\n        body: JSON.stringify(programData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const program = {\n            id: Date.now().toString(),\n            ...programData,\n            createdAt: new Date().toISOString()\n          };\n          programs.push(program);\n          this.setItem('exercisePrograms', programs);\n          resolve(program);\n        }, 300);\n      });\n    }\n  }\n\n  async getExercisePrograms(patientId) {\n    try {\n      const response = await this.apiRequest(`/exercise-programs/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const patientPrograms = programs.filter(prog => prog.patientId === patientId);\n          resolve(patientPrograms);\n        }, 100);\n      });\n    }\n  }\n\n  // AI Interaction methods\n  async saveAIInteraction(interactionData) {\n    try {\n      const response = await this.apiRequest('/ai-interactions', {\n        method: 'POST',\n        body: JSON.stringify(interactionData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const interaction = {\n            id: Date.now().toString(),\n            ...interactionData,\n            timestamp: new Date().toISOString()\n          };\n          interactions.push(interaction);\n          this.setItem('aiInteractions', interactions);\n          resolve(interaction);\n        }, 100);\n      });\n    }\n  }\n\n  async getAIInteractions(patientId) {\n    try {\n      const endpoint = patientId ? `/ai-interactions/${patientId}` : '/ai-interactions';\n      const response = await this.apiRequest(endpoint);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const patientInteractions = patientId\n            ? interactions.filter(int => int.patientId === patientId)\n            : interactions;\n          resolve(patientInteractions);\n        }, 100);\n      });\n    }\n  }\n\n  // Analytics methods\n  async getAnalytics() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const interactions = this.getItem('aiInteractions') || [];\n        const communications = this.getItem('communicationHistory') || [];\n        const programs = this.getItem('exercisePrograms') || [];\n        const bodyMapData = this.getItem('bodyMapData') || {};\n\n        const analytics = {\n          totalAIQueries: interactions.length,\n          totalCommunications: communications.length,\n          totalExercisePrograms: programs.length,\n          totalBodyMapAssessments: Object.keys(bodyMapData).length,\n          recentActivity: [\n            ...interactions.slice(-5).map(i => ({ type: 'ai', ...i })),\n            ...communications.slice(-5).map(c => ({ type: 'communication', ...c })),\n            ...programs.slice(-5).map(p => ({ type: 'exercise', ...p }))\n          ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10)\n        };\n\n        resolve(analytics);\n      }, 200);\n    });\n  }\n\n  // Clear all demo data\n  clearAllData() {\n    const keys = ['patients', 'bodyMapData', 'communicationHistory', 'exercisePrograms', 'aiInteractions'];\n    keys.forEach(key => this.removeItem(key));\n    this.removeItem('initialized');\n    this.initializeStorage();\n  }\n\n  // Export data for backup\n  exportData() {\n    const data = {\n      patients: this.getItem('patients'),\n      bodyMapData: this.getItem('bodyMapData'),\n      communicationHistory: this.getItem('communicationHistory'),\n      exercisePrograms: this.getItem('exercisePrograms'),\n      aiInteractions: this.getItem('aiInteractions'),\n      exportedAt: new Date().toISOString()\n    };\n    return data;\n  }\n\n  // Import data from backup\n  importData(data) {\n    try {\n      if (data.patients) this.setItem('patients', data.patients);\n      if (data.bodyMapData) this.setItem('bodyMapData', data.bodyMapData);\n      if (data.communicationHistory) this.setItem('communicationHistory', data.communicationHistory);\n      if (data.exercisePrograms) this.setItem('exercisePrograms', data.exercisePrograms);\n      if (data.aiInteractions) this.setItem('aiInteractions', data.aiInteractions);\n      return true;\n    } catch (error) {\n      console.error('Error importing data:', error);\n      return false;\n    }\n  }\n\n  // Simulate API delay\n  delay(ms = 100) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n\n// Create singleton instance\nconst dataService = new DataService();\n\nexport default dataService;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport BodyMap from '../../components/BodyMap/BodyMap';\nimport dataService from '../../services/dataService';\nimport toast from 'react-hot-toast';\n\nconst BodyMapPage = () => {\n  const { t, isRTL } = useLanguage();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n  const [patient, setPatient] = useState(null);\n  const [assessmentHistory, setAssessmentHistory] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedAssessment, setSelectedAssessment] = useState(null);\n\n  useEffect(() => {\n    loadPatientData();\n    loadAssessmentHistory();\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    if (patientId) {\n      try {\n        const patientData = await dataService.getPatient(patientId);\n        setPatient(patientData);\n      } catch (error) {\n        console.error('Error loading patient:', error);\n        toast.error(t('errorLoadingPatient', 'Error loading patient data'));\n      }\n    }\n    setLoading(false);\n  };\n\n  const loadAssessmentHistory = async () => {\n    if (patientId) {\n      try {\n        const bodyMapData = await dataService.getBodyMapData(patientId);\n        if (bodyMapData) {\n          setAssessmentHistory([bodyMapData]);\n        }\n      } catch (error) {\n        console.error('Error loading assessment history:', error);\n      }\n    }\n  };\n\n  const handleAssessmentSave = async (bodyMapData) => {\n    try {\n      await dataService.saveBodyMapData(patientId, bodyMapData);\n      await loadAssessmentHistory();\n      toast.success(t('assessmentSaved', 'Assessment saved successfully'));\n    } catch (error) {\n      console.error('Error saving assessment:', error);\n      toast.error(t('errorSaving', 'Error saving assessment'));\n    }\n  };\n\n  const handleNewAssessment = () => {\n    setSelectedAssessment(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"body-map-page min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => navigate(-1)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-arrow-left text-xl\"></i>\n                </button>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-user-md mr-3 text-blue-600\"></i>\n                    {t('bodyMapAssessment', 'Body Map Assessment')}\n                  </h1>\n                  {patient && (\n                    <p className=\"text-gray-600 dark:text-gray-300 mt-1\">\n                      {t('patient', 'Patient')}: {isRTL ? patient.nameAr : patient.name}\n                      {patient.condition && (\n                        <span className=\"ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                          {isRTL ? patient.conditionAr : patient.condition}\n                        </span>\n                      )}\n                    </p>\n                  )}\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <button\n                  onClick={handleNewAssessment}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2\"\n                >\n                  <i className=\"fas fa-plus\"></i>\n                  <span>{t('newAssessment', 'New Assessment')}</span>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-8\">\n          {/* Main Body Map */}\n          <div className=\"xl:col-span-3\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                  {t('interactiveBodyMap', 'Interactive Body Map')}\n                </h2>\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-universal-access mr-1\"></i>\n                    {t('accessible', 'Accessible')}\n                  </span>\n                  <span className=\"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-language mr-1\"></i>\n                    {t('multilingual', 'Multilingual')}\n                  </span>\n                </div>\n              </div>\n              \n              <BodyMap\n                patientId={patientId}\n                onSave={handleAssessmentSave}\n                showInstructions={true}\n                selectedRegions={selectedAssessment?.selectedRegions || []}\n                painLevels={selectedAssessment?.painLevels || {}}\n              />\n            </div>\n          </div>\n\n          {/* Assessment History Sidebar */}\n          <div className=\"xl:col-span-1\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                <i className=\"fas fa-history mr-2 text-gray-600\"></i>\n                {t('assessmentHistory', 'Assessment History')}\n              </h3>\n              \n              {assessmentHistory.length > 0 ? (\n                <div className=\"space-y-3\">\n                  {assessmentHistory.map((assessment, index) => (\n                    <div\n                      key={index}\n                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${\n                        selectedAssessment === assessment\n                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                          : 'border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'\n                      }`}\n                      onClick={() => setSelectedAssessment(assessment)}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {new Date(assessment.timestamp).toLocaleDateString()}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">\n                          {Object.keys(assessment.selectedRegions || {}).length} {t('regions', 'regions')}\n                        </div>\n                      </div>\n                      <div className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">\n                        {new Date(assessment.timestamp).toLocaleTimeString()}\n                      </div>\n                      {assessment.selectedRegions && assessment.selectedRegions.length > 0 && (\n                        <div className=\"mt-2\">\n                          <div className=\"text-xs text-gray-500 mb-1\">{t('affectedRegions', 'Affected Regions')}:</div>\n                          <div className=\"flex flex-wrap gap-1\">\n                            {assessment.selectedRegions.slice(0, 3).map((region, idx) => (\n                              <span\n                                key={idx}\n                                className=\"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\"\n                              >\n                                {region}\n                              </span>\n                            ))}\n                            {assessment.selectedRegions.length > 3 && (\n                              <span className=\"text-xs text-gray-500\">\n                                +{assessment.selectedRegions.length - 3} {t('more', 'more')}\n                              </span>\n                            )}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <i className=\"fas fa-clipboard-list text-4xl text-gray-300 mb-4\"></i>\n                  <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n                    {t('noAssessments', 'No assessments yet')}\n                  </p>\n                  <p className=\"text-gray-400 dark:text-gray-500 text-xs mt-1\">\n                    {t('clickBodyRegions', 'Click on body regions to start assessment')}\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mt-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                <i className=\"fas fa-chart-bar mr-2 text-gray-600\"></i>\n                {t('quickStats', 'Quick Stats')}\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('totalAssessments', 'Total Assessments')}\n                  </span>\n                  <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {assessmentHistory.length}\n                  </span>\n                </div>\n                \n                {selectedAssessment && (\n                  <>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {t('affectedRegions', 'Affected Regions')}\n                      </span>\n                      <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                        {selectedAssessment.selectedRegions?.length || 0}\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {t('avgPainLevel', 'Avg Pain Level')}\n                      </span>\n                      <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                        {selectedAssessment.painLevels ? \n                          (Object.values(selectedAssessment.painLevels).reduce((a, b) => a + b, 0) / \n                           Object.values(selectedAssessment.painLevels).length).toFixed(1) : '0'}\n                      </span>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BodyMapPage;\n"], "names": ["constructor", "this", "baseURL", "process", "storagePrefix", "initializeStorage", "getAuthToken", "localStorage", "getItem", "apiRequest", "endpoint", "options", "arguments", "length", "undefined", "token", "url", "concat", "defaultOptions", "headers", "finalOptions", "_objectSpread", "response", "fetch", "ok", "Error", "status", "json", "error", "console", "warn", "message", "fallbackToLocalStorage", "method", "data", "body", "JSON", "parse", "includes", "handleBodyMapFallback", "handleCommunicationFallback", "handleExerciseFallback", "handleAIFallback", "success", "setItem", "getDefaultPatients", "key", "item", "value", "stringify", "removeItem", "id", "name", "nameAr", "age", "gender", "condition", "conditionAr", "phone", "email", "communicationPreferences", "preferredChannels", "language", "quietHours", "start", "end", "createdAt", "Date", "toISOString", "getPatients", "Promise", "resolve", "setTimeout", "getPatient", "patientId", "patient", "find", "p", "saveBodyMapData", "bodyMapData", "allBodyMapData", "timestamp", "getBodyMapData", "sendMessage", "messageData", "history", "now", "toString", "push", "getCommunicationHistory", "patientHistory", "filter", "msg", "saveExerciseProgram", "programData", "programs", "program", "getExercisePrograms", "patientPrograms", "prog", "saveAIInteraction", "interactionData", "interactions", "interaction", "getAIInteractions", "patientInteractions", "int", "getAnalytics", "communications", "analytics", "totalAIQueries", "totalCommunications", "totalExercisePrograms", "totalBodyMapAssessments", "Object", "keys", "recentActivity", "slice", "map", "i", "type", "c", "sort", "a", "b", "clearAllData", "for<PERSON>ach", "exportData", "patients", "communicationHistory", "exercisePrograms", "aiInteractions", "exportedAt", "importData", "delay", "ms", "BodyMapPage", "_selectedAssessment$s", "t", "isRTL", "useLanguage", "useParams", "navigate", "useNavigate", "setPatient", "useState", "assessmentHistory", "setAssessmentHistory", "loading", "setLoading", "selectedAssessment", "setSelectedAssessment", "useEffect", "loadPatientData", "loadAssessmentHistory", "async", "patientData", "dataService", "toast", "_jsx", "className", "children", "_jsxs", "onClick", "handleNewAssessment", "BodyMap", "onSave", "showInstructions", "selectedRegions", "painLevels", "assessment", "index", "toLocaleDateString", "toLocaleTimeString", "region", "idx", "_Fragment", "values", "reduce", "toFixed"], "sourceRoot": ""}