"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[948],{948:(e,s,t)=>{t.r(s),t.d(s,{default:()=>c});var a=t(5043),r=t(4117),i=t(7016),n=t(3768),l=t(579);const c=()=>{const{t:e}=(0,r.Bd)(),[s,t]=(0,a.useState)(!1),[c,o]=(0,a.useState)(null),d={primaryDiagnosis:"Stroke (CVA)",secondaryDiagnoses:["Hypertension","Diabetes Type 2"],medicalHistory:"Pat<PERSON> suffered ischemic stroke 3 months ago affecting left hemisphere. Previous history of hypertension and diabetes.",currentMedications:"Lisinopril 10mg daily, Metformin 500mg BID, Aspirin 81mg daily",allergies:"Penicillin - rash",precautions:"Fall risk, Right-sided weakness",psychosocialAssessment:{moodState:"anxious",copingStrategies:"developing",motivationLevel:"high",insightLevel:"good",familySupport:"strong",socialConnections:"limited",culturalConsiderations:"Arabic-speaking family, Islamic faith considerations",spiritualNeeds:"Prayer time accommodation needed",notes:"Patient shows good motivation but anxiety about recovery timeline"},environmentalAssessment:{homeAccessibility:"partially-accessible",homeSafety:"minor-hazards",homeModifications:"Ramp needed for front entrance, grab bars in bathroom, remove throw rugs",transportationAccess:"limited",communityResources:"available",notes:"Family willing to make home modifications, local mosque provides community support"}};return(0,l.jsxs)("div",{className:"p-6 max-w-4xl mx-auto",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:[(0,l.jsx)("i",{className:"fas fa-clipboard-check mr-3 text-blue-600"}),e("testCarfAssessment","Test CARF Assessment")]}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("testCarfDescription","Test the CARF Assessment form with all sections and functionality.")})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:e("carfAssessmentTesting","CARF Assessment Testing")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,l.jsx)("button",{onClick:()=>{o(null),t(!0)},className:"flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors border border-blue-200 dark:border-blue-800",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("i",{className:"fas fa-plus-circle text-blue-600 dark:text-blue-400 text-2xl mb-2"}),(0,l.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("newAssessment","New Assessment")}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("startBlankAssessment","Start with blank form")})]})}),(0,l.jsx)("button",{onClick:()=>{o(d),t(!0)},className:"flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors border border-green-200 dark:border-green-800",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("i",{className:"fas fa-file-medical text-green-600 dark:text-green-400 text-2xl mb-2"}),(0,l.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("sampleAssessment","Sample Assessment")}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("prefilledSampleData","Pre-filled with sample data")})]})})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("assessmentSections","Assessment Sections")}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{id:"demographic",title:"Demographic Information",icon:"fas fa-user",color:"blue"},{id:"medical",title:"Medical History",icon:"fas fa-notes-medical",color:"red"},{id:"functional",title:"Functional Assessment (FIM)",icon:"fas fa-tasks",color:"blue"},{id:"psychosocial",title:"Psychosocial Assessment",icon:"fas fa-brain",color:"purple"},{id:"environmental",title:"Environmental Assessment",icon:"fas fa-home",color:"green"},{id:"risk",title:"Risk Assessment",icon:"fas fa-exclamation-triangle",color:"yellow"},{id:"goals",title:"Goals and Preferences",icon:"fas fa-bullseye",color:"indigo"}].map(s=>(0,l.jsx)("div",{className:"p-3 bg-".concat(s.color,"-50 dark:bg-").concat(s.color,"-900/20 rounded-lg border border-").concat(s.color,"-200 dark:border-").concat(s.color,"-800"),children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("i",{className:"".concat(s.icon," text-").concat(s.color,"-600 dark:text-").concat(s.color,"-400 mr-2")}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e(s.id,s.title)})]})},s.id))})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("features","Features")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("comprehensiveSections","Comprehensive assessment sections")]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("fimScoring","FIM scoring system integration")]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("riskAssessment","Risk assessment and mitigation")]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("psychosocialEvaluation","Psychosocial evaluation")]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("environmentalAssessment","Environmental assessment")]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("goalSetting","Goal setting and preferences")]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("dischargePlanning","Discharge planning")]}),(0,l.jsxs)("div",{className:"flex items-center text-sm text-gray-700 dark:text-gray-300",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-600 mr-2"}),e("carfCompliance","CARF compliance standards")]})]})]})]}),c&&(0,l.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:e("lastAssessmentData","Last Assessment Data")}),(0,l.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,l.jsxs)("p",{children:[(0,l.jsxs)("strong",{children:[e("primaryDiagnosis","Primary Diagnosis"),":"]})," ",c.primaryDiagnosis]}),(0,l.jsxs)("p",{children:[(0,l.jsxs)("strong",{children:[e("assessmentDate","Assessment Date"),":"]})," ",c.assessmentDate]}),(0,l.jsxs)("p",{children:[(0,l.jsxs)("strong",{children:[e("assessor","Assessor"),":"]})," ",c.assessor]})]}),(0,l.jsx)("button",{onClick:()=>console.log("Full Assessment Data:",c),className:"mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700",children:e("logFullData","Log Full Data to Console")})]})]}),s&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",children:(0,l.jsx)(i.default,{patientId:"test-patient-123",initialData:c||{},onSave:e=>{console.log("CARF Assessment Data:",e),n.Ay.success("CARF Assessment saved successfully!"),o(e),t(!1)},onCancel:()=>{t(!1)}})})})]})}}}]);
//# sourceMappingURL=948.942c3f93.chunk.js.map