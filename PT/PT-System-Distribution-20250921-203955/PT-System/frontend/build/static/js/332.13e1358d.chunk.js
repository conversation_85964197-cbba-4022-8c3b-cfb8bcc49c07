"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[332],{332:(e,n,t)=>{t.r(n),t.d(n,{default:()=>r});t(5043);var a=t(3216),d=t(3850),s=t(579);const r=()=>{const{patientId:e,educationId:n}=(0,a.g)();return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)(d.default,{patientId:e,educationId:n})})}}}]);
//# sourceMappingURL=332.13e1358d.chunk.js.map