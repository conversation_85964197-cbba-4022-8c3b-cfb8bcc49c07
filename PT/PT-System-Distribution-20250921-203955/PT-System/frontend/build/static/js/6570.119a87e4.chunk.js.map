{"version": 3, "file": "static/js/6570.119a87e4.chunk.js", "mappings": "iOAMA,MA2IA,EA3IqBA,KAAO,IAADC,EACzB,MAAM,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACX,EAAEC,IAAMC,EAAAA,EAAAA,MACPC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAASC,IAAcF,EAAAA,EAAAA,UAAS,OAChCG,EAAoBC,IAAyBJ,EAAAA,EAAAA,UAAS,OAG7DK,EAAAA,EAAAA,WAAU,KACiBC,WACvBP,GAAW,GACX,IAAK,IAADQ,EAAAC,EAEF,GAAkB,QAAdD,EAAAb,EAASe,aAAK,IAAAF,GAAdA,EAAgBN,SAAyB,QAAlBO,EAAId,EAASe,aAAK,IAAAD,GAAdA,EAAgBE,mBAAoB,CACjE,MAAMC,EAAcjB,EAASe,MAAMR,QACnCC,GAAUU,EAAAA,EAAAA,GAAC,CACTC,GAAIF,EAAYG,KAAOH,EAAYE,GACnCE,KAAMJ,EAAYI,MAAI,GAAAC,OAAOL,EAAYM,UAAS,KAAAD,OAAIL,EAAYO,UAClEC,OAAQR,EAAYQ,QAAM,GAAAH,OAAOL,EAAYM,UAAS,KAAAD,OAAIL,EAAYO,UACtEE,IAAKT,EAAYS,IACjBC,IAA4B,SAAvBV,EAAYW,OAAoB,IAAM,IAC3CC,YAAaZ,EAAYY,aAAe,gBACxCjC,UAAWqB,EAAYa,WACvBC,WAAYd,EAAYG,KAAOH,EAAYE,GAC3Ca,MAAOf,EAAYe,MACnBC,MAAOhB,EAAYgB,MACnBC,QAASjB,EAAYiB,QACrBC,iBAAkBlB,EAAYkB,iBAC9BC,eAAgBnB,EAAYmB,gBAEzBnB,GAEP,MAAO,GAAIrB,GAA2B,QAAdA,EAAqB,CAE3C,MAAMyC,QAAiBC,MAAM,GAADhB,OAAIiB,+BAA+D,cAAAjB,OAAa1B,GAAa,CACvH4C,QAAS,CACP,cAAgB,UAADlB,OAAYmB,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAaP,EAASQ,OACxBD,EAAKE,SACPtC,EAAW,CACTW,GAAIyB,EAAKA,KAAKzB,GACdE,KAAMuB,EAAKA,KAAKG,UAAQ,GAAAzB,OAAOsB,EAAKA,KAAKrB,UAAS,KAAAD,OAAIsB,EAAKA,KAAKpB,UAChEE,IAAKkB,EAAKA,KAAKlB,IACfC,IAA0B,SAArBiB,EAAKA,KAAKhB,OAAoB,IAAM,IACzCC,YAAae,EAAKA,KAAKf,aAAe,gBACtCjC,UAAWgD,EAAKA,KAAKd,WACrBC,WAAYa,EAAKA,KAAKzB,IAG5B,CACF,MAEEX,EAAW,CACTW,GAAI,MACJE,KAAM,GACNK,IAAK,GACLC,IAAK,GACLE,YAAa,GACbjC,UAAW,GACXmC,WAAY,KAKhB,MAAMiB,EAAeP,aAAaC,QAAQ,iBAADpB,OAAkB1B,IACvDoD,GACFtC,EAAsBuC,KAAKC,MAAMF,GAErC,CAAE,MAAOG,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CE,EAAAA,GAAMF,MAAMjD,EAAE,2BAA4B,+BAC5C,CAAC,QACCG,GAAW,EACb,GAIFiD,IACC,CAAC1D,EAAWI,EAASe,MAAOb,IA2B/B,OAAIE,IAAYG,GAEZgD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4EAA2EC,UACxFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4EACfD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDvD,EAAE,qBAAsB,mCAQjCqD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,UACvDF,EAAAA,EAAAA,KAACI,EAAAA,EAAqB,CACpB/D,UAAWA,EACXqB,YAAaV,EACbS,mBAAkC,QAAhBrB,EAAEK,EAASe,aAAK,IAAApB,OAAA,EAAdA,EAAgBqB,mBACpC4C,YAAanD,EACboD,OA7CuBjD,UAC3B,IACEP,GAAW,GAIXoC,aAAaqB,QAAQ,iBAADxC,OAAkByC,EAAenE,WAAa,OAASqD,KAAKe,UAAUD,IAE1FV,EAAAA,GAAMP,QAAQ5C,EAAE,8BAA+B,kCAG/CJ,EAAS,YACX,CAAE,MAAOqD,GAGP,MAFAC,QAAQD,MAAM,2BAA4BA,GAC1CE,EAAAA,GAAMF,MAAMjD,EAAE,wBAAyB,4BACjCiD,CACR,CAAC,QACC9C,GAAW,EACb,GA4BI4D,SAzBeC,KACnBpE,EAAS,kB", "sources": ["pages/Assessment/PTAssessment.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport PTAdultAssessmentForm from '../../components/Assessment/PTAdultAssessmentForm';\nimport toast from 'react-hot-toast';\n\nconst PTAssessment = () => {\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { t } = useLanguage();\n  const [loading, setLoading] = useState(false);\n  const [patient, setPatient] = useState(null);\n  const [existingAssessment, setExistingAssessment] = useState(null);\n\n  // Enhanced patient data fetching with state support\n  useEffect(() => {\n    const fetchPatientData = async () => {\n      setLoading(true);\n      try {\n        // Check if patient data was passed via state (from patient profile)\n        if (location.state?.patient && location.state?.fromPatientProfile) {\n          const patientData = location.state.patient;\n          setPatient({\n            id: patientData._id || patientData.id,\n            name: patientData.name || `${patientData.firstName} ${patientData.lastName}`,\n            nameEn: patientData.nameEn || `${patientData.firstName} ${patientData.lastName}`,\n            age: patientData.age,\n            sex: patientData.gender === 'male' ? 'M' : 'F',\n            nationality: patientData.nationality || 'Saudi Arabian',\n            patientId: patientData.nationalId,\n            fileNumber: patientData._id || patientData.id,\n            phone: patientData.phone,\n            email: patientData.email,\n            address: patientData.address,\n            emergencyContact: patientData.emergencyContact,\n            medicalHistory: patientData.medicalHistory,\n            // Pre-populate all available patient information\n            ...patientData\n          });\n        } else if (patientId && patientId !== 'new') {\n          // Fetch real patient data from API\n          const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001/api/v1'}/patients/${patientId}`, {\n            headers: {\n              'Authorization': `Bearer ${localStorage.getItem('token')}`,\n              'Content-Type': 'application/json'\n            }\n          });\n\n          if (response.ok) {\n            const data = await response.json();\n            if (data.success) {\n              setPatient({\n                id: data.data.id,\n                name: data.data.fullName || `${data.data.firstName} ${data.data.lastName}`,\n                age: data.data.age,\n                sex: data.data.gender === 'male' ? 'M' : 'F',\n                nationality: data.data.nationality || 'Saudi Arabian',\n                patientId: data.data.nationalId,\n                fileNumber: data.data.id\n              });\n            }\n          }\n        } else {\n          // New patient - set empty data\n          setPatient({\n            id: 'new',\n            name: '',\n            age: '',\n            sex: '',\n            nationality: '',\n            patientId: '',\n            fileNumber: ''\n          });\n        }\n\n        // Check for existing assessment from API or localStorage fallback\n        const existingData = localStorage.getItem(`pt_assessment_${patientId}`);\n        if (existingData) {\n          setExistingAssessment(JSON.parse(existingData));\n        }\n      } catch (error) {\n        console.error('Error fetching patient data:', error);\n        toast.error(t('errorFetchingPatientData', 'Error fetching patient data'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Always call fetchPatientData to handle both state and API scenarios\n    fetchPatientData();\n  }, [patientId, location.state, t]);\n\n  const handleSaveAssessment = async (assessmentData) => {\n    try {\n      setLoading(true);\n\n      // In real implementation, save to backend API\n      // For now, save to localStorage\n      localStorage.setItem(`pt_assessment_${assessmentData.patientId || 'new'}`, JSON.stringify(assessmentData));\n\n      toast.success(t('assessmentSavedSuccessfully', 'Assessment saved successfully'));\n      \n      // Navigate back to patients list or patient details\n      navigate('/patients');\n    } catch (error) {\n      console.error('Error saving assessment:', error);\n      toast.error(t('errorSavingAssessment', 'Error saving assessment'));\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate('/patients');\n  };\n\n  if (loading && !patient) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-4\">\n            {t('loadingPatientData', 'Loading patient data...')}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <PTAdultAssessmentForm\n        patientId={patientId}\n        patientData={patient}\n        fromPatientProfile={location.state?.fromPatientProfile}\n        initialData={existingAssessment}\n        onSave={handleSaveAssessment}\n        onCancel={handleCancel}\n      />\n    </div>\n  );\n};\n\nexport default PTAssessment;\n"], "names": ["PTAssessment", "_location$state3", "patientId", "useParams", "navigate", "useNavigate", "location", "useLocation", "t", "useLanguage", "loading", "setLoading", "useState", "patient", "setPatient", "existingAssessment", "setExistingAssessment", "useEffect", "async", "_location$state", "_location$state2", "state", "fromPatientProfile", "patientData", "_objectSpread", "id", "_id", "name", "concat", "firstName", "lastName", "nameEn", "age", "sex", "gender", "nationality", "nationalId", "fileNumber", "phone", "email", "address", "emergencyContact", "medicalHistory", "response", "fetch", "process", "headers", "localStorage", "getItem", "ok", "data", "json", "success", "fullName", "existingData", "JSON", "parse", "error", "console", "toast", "fetchPatientData", "_jsx", "className", "children", "_jsxs", "PTAdultAssessmentForm", "initialData", "onSave", "setItem", "assessmentData", "stringify", "onCancel", "handleCancel"], "sourceRoot": ""}