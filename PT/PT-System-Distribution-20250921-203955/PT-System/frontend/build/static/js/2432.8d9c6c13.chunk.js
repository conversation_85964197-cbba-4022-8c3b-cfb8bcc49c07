"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2432],{2432:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});a(2555);var s=a(5043),r=a(7921),d=(a(3768),a(579));const i=()=>{const{t:e,isRTL:t}=(0,r.o)(),[a,i]=(0,s.useState)("invoices"),[l,n]=(0,s.useState)(""),[x,c]=(0,s.useState)(!1),[m,o]=(0,s.useState)(!1),[g,p]=(0,s.useState)(null),[h,u]=(0,s.useState)([{id:"INV-2024-001",patientName:"<PERSON>",patientId:"P001",issueDate:"2024-02-15",dueDate:"2024-03-15",amount:1500,paidAmount:1500,status:"Paid",paymentMethod:"Insurance",insuranceProvider:"Bupa Arabia",services:[{name:"Physical Therapy Session",quantity:5,unitPrice:250,total:1250},{name:"Initial Assessment",quantity:1,unitPrice:250,total:250}],notes:"Lower back pain treatment",therapist:"Dr. Sarah Ahmed"},{id:"INV-2024-002",patientName:"Fatima Al-Zahra",patientId:"P002",issueDate:"2024-02-14",dueDate:"2024-03-14",amount:2e3,paidAmount:400,status:"Partially Paid",paymentMethod:"Mixed",insuranceProvider:"Tawuniya",services:[{name:"Occupational Therapy",quantity:6,unitPrice:300,total:1800},{name:"Special Needs Assessment",quantity:1,unitPrice:200,total:200}],notes:"Autism spectrum disorder support",therapist:"Dr. Mona Hassan"},{id:"INV-2024-003",patientName:"Mohammed Al-Otaibi",patientId:"P003",issueDate:"2024-02-13",dueDate:"2024-03-13",amount:1200,paidAmount:0,status:"Pending",paymentMethod:"Cash",insuranceProvider:null,services:[{name:"Speech Therapy",quantity:4,unitPrice:300,total:1200}],notes:"Speech delay treatment",therapist:"Dr. Layla Ibrahim"}]),[y,b]=(0,s.useState)([{id:"PAY-2024-001",invoiceId:"INV-2024-001",patientName:"Ahmed Al-Rashid",amount:1500,method:"Insurance",provider:"Bupa Arabia",date:"2024-02-18",status:"Completed",reference:"BUPA-REF-12345",processedBy:"System"},{id:"PAY-2024-002",invoiceId:"INV-2024-002",patientName:"Fatima Al-Zahra",amount:400,method:"Cash",provider:null,date:"2024-02-16",status:"Completed",reference:"CASH-001",processedBy:"Receptionist"},{id:"PAY-2024-003",invoiceId:"INV-2024-002",patientName:"Fatima Al-Zahra",amount:1200,method:"Insurance",provider:"Tawuniya",date:"2024-02-20",status:"Pending",reference:"TAW-PENDING-456",processedBy:"System"}]),N=[{id:"invoices",label:e("invoices","Invoices"),icon:"fas fa-file-invoice"},{id:"payments",label:e("payments","Payments"),icon:"fas fa-credit-card"},{id:"reports",label:e("reports","Reports"),icon:"fas fa-chart-bar"},{id:"settings",label:e("settings","Settings"),icon:"fas fa-cog"}],j=e=>new Intl.NumberFormat("ar-SA",{style:"currency",currency:"SAR"}).format(e),f=e=>{switch(e.toLowerCase()){case"paid":case"completed":return"text-green-600 bg-green-100 dark:bg-green-900/30";case"partially paid":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30";case"pending":return"text-orange-600 bg-orange-100 dark:bg-orange-900/30";case"overdue":case"failed":return"text-red-600 bg-red-100 dark:bg-red-900/30";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30"}};return(0,d.jsxs)("div",{className:"space-y-6 ".concat(t?"font-arabic":"font-english"),children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("billingPayments","Billing & Payments")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("billingDescription","Manage invoices, payments, and billing reports")})]}),(0,d.jsx)("div",{className:"flex items-center space-x-4",children:(0,d.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-download mr-2"}),e("exportData","Export Data")]})})]}),(0,d.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,d.jsx)("nav",{className:"-mb-px flex space-x-8",children:N.map(e=>(0,d.jsxs)("button",{onClick:()=>i(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(a===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,d.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})}),"invoices"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-file-invoice text-blue-600 dark:text-blue-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalInvoices","Total Invoices")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("paidInvoices","Paid")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.filter(e=>"Paid"===e.status).length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("pendingInvoices","Pending")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.filter(e=>"Pending"===e.status||"Partially Paid"===e.status).length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-dollar-sign text-purple-600 dark:text-purple-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalAmount","Total Amount")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:j(h.reduce((e,t)=>e+t.amount,0))})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,d.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("invoices","Invoices")}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("input",{type:"text",placeholder:e("searchInvoices","Search invoices..."),value:l,onChange:e=>n(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,d.jsxs)("button",{onClick:()=>o(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("newInvoice","New Invoice")]})]})]}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("invoiceId","Invoice ID")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("amount","Amount")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("paid","Paid")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("dueDate","Due Date")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,d.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:h.map(t=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.id}),(0,d.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.issueDate})]}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.patientName}),(0,d.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",t.patientId]})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white",children:j(t.amount)}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:j(t.paidAmount)}),t.amount>t.paidAmount&&(0,d.jsxs)("div",{className:"text-sm text-red-600 dark:text-red-400",children:[j(t.amount-t.paidAmount)," ",e("remaining","remaining")]})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.dueDate}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(f(t.status)),children:t.status})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,d.jsx)("button",{onClick:()=>p(t),className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3",children:e("view","View")}),(0,d.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3",children:e("print","Print")}),"Paid"!==t.status&&(0,d.jsx)("button",{className:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300",children:e("recordPayment","Record Payment")})]})]},t.id))})]})})]})]}),"payments"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-credit-card text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalPayments","Total Payments")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-dollar-sign text-blue-600 dark:text-blue-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalReceived","Total Received")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:j(y.filter(e=>"Completed"===e.status).reduce((e,t)=>e+t.amount,0))})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("pendingPayments","Pending")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.filter(e=>"Pending"===e.status).length})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-percentage text-purple-600 dark:text-purple-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("successRate","Success Rate")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[Math.round(y.filter(e=>"Completed"===e.status).length/y.length*100),"%"]})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,d.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("paymentHistory","Payment History")}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("recordPayment","Record Payment")]})]}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("paymentId","Payment ID")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("invoice","Invoice")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("amount","Amount")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("method","Method")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("date","Date")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,d.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:y.map(t=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.id}),(0,d.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.reference})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.invoiceId}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.patientName}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white",children:j(t.amount)}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,d.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:t.method}),t.provider&&(0,d.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.provider})]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.date}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(f(t.status)),children:t.status})}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,d.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3",children:e("view","View")}),(0,d.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",children:e("receipt","Receipt")})]})]},t.id))})]})})]})]}),"reports"===a&&(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("billingReports","Billing Reports")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("reportsDescription","Comprehensive billing analytics and financial reports")})]}),"settings"===a&&(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("billingSettings","Billing Settings")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("settingsDescription","Configure billing preferences, tax rates, and payment methods")})]})]})}}}]);
//# sourceMappingURL=2432.8d9c6c13.chunk.js.map