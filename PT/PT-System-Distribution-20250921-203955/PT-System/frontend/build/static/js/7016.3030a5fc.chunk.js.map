{"version": 3, "file": "static/js/7016.3030a5fc.chunk.js", "mappings": "4IAEO,MAsOMA,EAAyB,CACpCC,gBAAiB,CACfC,MAAO,IACPC,MAAO,kBACPC,MAAO,QACPC,YAAa,4BAEfC,uBAAwB,CACtBJ,MAAO,GACPC,MAAO,yBACPC,MAAO,SACPC,YAAa,+BAEfE,mBAAoB,CAClBL,MAAO,GACPC,MAAO,qBACPC,MAAO,SACPC,YAAa,mCAEfG,eAAgB,CACdN,MAAO,EACPC,MAAO,iBACPC,MAAO,MACPC,YAAa,iC,oGC3PjB,MA6kCA,EA7kC2BI,IAAwD,IAAvD,UAAEC,EAAS,OAAEC,EAAM,SAAEC,EAAQ,YAAEC,EAAc,CAAC,GAAGJ,EAC3E,MAAM,EAAEK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,IAC9CC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CAEvCT,UAAWA,GAAa,GACxBY,gBAAgB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACpDC,SAAU,GACVC,oBAAqB,GAGrBC,iBAAkBf,EAAYe,kBAAoB,GAClDC,mBAAoBhB,EAAYgB,oBAAsB,GACtDC,eAAgBjB,EAAYiB,gBAAkB,GAC9CC,mBAAoBlB,EAAYkB,oBAAsB,GACtDC,UAAWnB,EAAYmB,WAAa,GACpCC,YAAapB,EAAYoB,aAAe,GAGxCC,WAAY,CACVC,cAAe,GACfC,UAAW,GACXC,YAAa,GACbC,iBAAkB,IAIpBC,qBAAsB,CACpBC,SAAU,CACRC,OAAQ,EACRC,SAAU,EACVC,QAAS,EACTC,cAAe,EACfC,cAAe,EACfC,UAAW,GAEbC,iBAAkB,CAChBC,QAAS,EACTC,MAAO,GAETC,UAAW,CACTC,mBAAoB,EACpBC,OAAQ,EACRC,UAAW,GAEbC,WAAY,CACVC,eAAgB,EAChBC,OAAQ,GAEVC,cAAe,CACbC,cAAe,EACfC,WAAY,GAEdC,gBAAiB,CACfC,kBAAmB,EACnBC,eAAgB,EAChBC,OAAQ,IAKZC,uBAAwB,CACtBC,UAAW,SACXC,iBAAkB,WAClBC,gBAAiB,OACjBC,aAAc,OACdC,cAAe,SACfC,kBAAmB,WACnBC,uBAAwB,GACxBC,eAAgB,GAChBC,MAAO,GAEPC,aAAc,GACdC,kBAAmB,GACnBC,gBAAiB,GACjBC,iBAAkB,GAClBC,cAAe,GACfC,gBAAiB,IAInBC,wBAAyB,CACvBC,kBAAmB,aACnBC,WAAY,OACZC,kBAAmB,GACnBC,qBAAsB,WACtBC,mBAAoB,YACpBZ,MAAO,GAEPa,kBAAmB,GACnBC,cAAe,GACfC,cAAe,GACfC,eAAgB,GAChBC,mBAAoB,GACpBC,eAAgB,IAIlBC,eAAgB,CACdC,SAAU,MACVC,kBAAmB,MACnBC,gBAAiB,MACjBC,eAAgB,MAChBC,eAAgB,MAChBC,cAAe,MACfC,mBAAoB,IAItBC,oBAAqB,CACnBC,kBAAmB,GACnBC,YAAa,GACbC,cAAe,GACfC,YAAa,GACbC,SAAU,GACVC,UAAW,GACXC,kBAAmB,MAIjBC,EAAW,CACf,CAAEC,GAAI,cAAeC,MAAOhG,EAAE,yBAA0B,2BAA4BiG,KAAM,eAC1F,CAAEF,GAAI,UAAWC,MAAOhG,EAAE,iBAAkB,mBAAoBiG,KAAM,wBACtE,CAAEF,GAAI,aAAcC,MAAOhG,EAAE,uBAAwB,+BAAgCiG,KAAM,gBAC3F,CAAEF,GAAI,eAAgBC,MAAOhG,EAAE,yBAA0B,2BAA4BiG,KAAM,gBAC3F,CAAEF,GAAI,gBAAiBC,MAAOhG,EAAE,0BAA2B,4BAA6BiG,KAAM,eAC9F,CAAEF,GAAI,OAAQC,MAAOhG,EAAE,iBAAkB,mBAAoBiG,KAAM,+BACnE,CAAEF,GAAI,QAASC,MAAOhG,EAAE,sBAAuB,yBAA0BiG,KAAM,oBAG3EC,EAAoBA,CAACC,EAASC,EAAOC,KACzC9F,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACH,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKH,IAAQ,IAChB,CAACC,GAAQC,QAKTG,EAA0BA,CAACL,EAASM,EAAYL,EAAOC,KAC3D9F,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACH,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKH,IAAQ,IAChB,CAACM,IAAUF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACND,EAAKH,GAASM,IAAW,IAC5B,CAACL,GAAQC,UAMXK,EAAgBC,IAAA,IAAC,MAAEtH,EAAK,MAAEgH,EAAK,SAAEO,EAAQ,YAAErH,GAAaoH,EAAA,OAC5DE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,6DAA4DC,SAC1E1H,KAEHwH,EAAAA,EAAAA,MAAA,UACER,MAAOA,EACPO,SAAWK,GAAML,EAASM,SAASD,EAAEE,OAAOd,QAC5CS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAO,EAAEU,SAAC,+BAClBC,EAAAA,EAAAA,KAAA,UAAQX,MAAO,EAAEU,SAAC,+BAClBC,EAAAA,EAAAA,KAAA,UAAQX,MAAO,EAAEU,SAAC,2BAClBC,EAAAA,EAAAA,KAAA,UAAQX,MAAO,EAAEU,SAAC,oCAClBC,EAAAA,EAAAA,KAAA,UAAQX,MAAO,EAAEU,SAAC,6BAClBC,EAAAA,EAAAA,KAAA,UAAQX,MAAO,EAAEU,SAAC,4BAClBC,EAAAA,EAAAA,KAAA,UAAQX,MAAO,EAAEU,SAAC,4BAEnBxH,IACCyH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAExH,QAKzD6H,EAAkBC,IAAA,IAAC,MAAEhI,EAAK,MAAEgH,EAAK,SAAEO,GAAUS,EAAA,OACjDR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,6DAA4DC,SAC1E1H,KAEHwH,EAAAA,EAAAA,MAAA,UACER,MAAOA,EACPO,SAAWK,GAAML,EAASK,EAAEE,OAAOd,OACnCS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,MAAKU,SAAE/G,EAAE,UAAW,eAClCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,eAAgB,oBAC5CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,WAAY,sBA6yB1C,OACE6G,EAAAA,EAAAA,MAAA,OAAKC,UAAS,kDAAAQ,OAAoDrH,EAAQ,cAAgB,gBAAiB8G,SAAA,EAEzGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,SAC5D/G,EAAE,iBAAkB,gCAEvBgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD/G,EAAE,4BAA6B,2DAGpC6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEO,QAASzH,EACTgH,UAAU,uFAAsFC,SAE/F/G,EAAE,SAAU,aAEfgH,EAAAA,EAAAA,KAAA,UACEO,QAASA,IAAM1H,EAAOS,GACtBwG,UAAU,kFAAiFC,SAE1F/G,EAAE,iBAAkB,8BAO7B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0DAAyDC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCC,SAC/CjB,EAAS0B,IAAI,CAACrB,EAASsB,KACtBZ,EAAAA,EAAAA,MAAA,OAAsBC,UAAU,oBAAmBC,SAAA,EACjDC,EAAAA,EAAAA,KAAA,OACEF,UAAS,kEAAAQ,OACPnH,GAAkBsH,EACd,yCACA,iCACHV,UAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAWX,EAAQF,SAEvBwB,EAAQ3B,EAAS4B,OAAS,IACzBV,EAAAA,EAAAA,KAAA,OACEF,UAAS,mBAAAQ,OACPnH,EAAiBsH,EAAQ,cAAgB,mBAbvCtB,EAAQJ,QAoBtBiB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,CACpD/G,EAAE,OAAQ,QAAQ,IAAEG,EAAiB,EAAE,IAAEH,EAAE,KAAM,MAAM,IAAE8F,EAAS4B,OAAO,KAAG5B,EAAS3F,GAAgB6F,eAM5GgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SAt2BFY,MAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC1B,MAAMlD,EAAUL,EAAS3F,GAEzB,OAAQgG,EAAQJ,IACd,IAAK,cACH,OACEiB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E/G,EAAE,iBAAkB,mBAAmB,SAE1CgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,MAAO/F,EAASE,eAChBoG,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE9F,eAAgByG,EAAEE,OAAOd,SAC1ES,UAAU,kIACVyC,UAAQ,QAGZ1C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E/G,EAAE,WAAY,YAAY,SAE7BgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,MAAO/F,EAASM,SAChBgG,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE1F,SAAUqG,EAAEE,OAAOd,SACpES,UAAU,kIACV0C,YAAaxJ,EAAE,eAAgB,iBAC/BuJ,UAAQ,QAGZ1C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,sBAAuB,2BAE5BgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,MAAO/F,EAASO,oBAChB+F,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEzF,oBAAqBoG,EAAEE,OAAOd,SAC/ES,UAAU,kIACV0C,YAAaxJ,EAAE,yBAA0B,kCAOrD,IAAK,UACH,OACE6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,SAC5D/G,EAAE,sBAAuB,mCAE5BgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCAAwCC,SAClD/G,EAAE,qBAAsB,sFAK7B6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,YAAa,gBAElB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E/G,EAAE,mBAAoB,qBAAqB,SAE9CgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,MAAO/F,EAASQ,iBAChB8F,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAExF,iBAAkBmG,EAAEE,OAAOd,SAC5ES,UAAU,kIACV0C,YAAaxJ,EAAE,8BAA+B,0BAC9CuJ,UAAQ,QAIZ1C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,qBAAsB,0BAE3BgH,EAAAA,EAAAA,KAAA,YACEX,MAAOoD,MAAMC,QAAQpJ,EAASS,oBAAsBT,EAASS,mBAAmB4I,KAAK,MAAQrJ,EAASS,mBACtG6F,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BD,GAAI,IACPvF,mBAAoBkG,EAAEE,OAAOd,MAAM1F,MAAM,KAAK6G,IAAIoC,GAAKA,EAAEC,QAAQC,OAAOF,GAAKA,MAE/EG,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,gCAAiC,+DAOxD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,iBAAkB,sBAEvB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,yBAA0B,+BAE/BgH,EAAAA,EAAAA,KAAA,YACEX,MAAO/F,EAASU,eAChB4F,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEtF,eAAgBiG,EAAEE,OAAOd,SAC1E0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,4BAA6B,6EAIhD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,qBAAsB,0BAE3BgH,EAAAA,EAAAA,KAAA,YACEX,MAAO/F,EAASW,mBAChB2F,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAErF,mBAAoBgG,EAAEE,OAAOd,SAC9E0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,yBAA0B,4DAOjD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,uBAAwB,gCAE7B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,YAAa,sBAElBgH,EAAAA,EAAAA,KAAA,YACEX,MAAO/F,EAASY,UAChB0F,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEpF,UAAW+F,EAAEE,OAAOd,SACrE0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,uBAAwB,yEAI3C6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,cAAe,0BAEpBgH,EAAAA,EAAAA,KAAA,YACEX,MAAO/F,EAASa,YAChByF,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEnF,YAAa8F,EAAEE,OAAOd,SACvE0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,yBAA0B,wEAOjD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,aAAc,sCAEnB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,gBAAiB,qBAEtBgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,OAA0B,QAAnBuB,EAAAtH,EAASc,kBAAU,IAAAwG,OAAA,EAAnBA,EAAqBvG,gBAAiB,GAC7CuF,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BD,GAAI,IACPlF,YAAUmF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAKlF,YAAU,IAAEC,cAAe4F,EAAEE,OAAOd,WAE5DS,UAAU,kIACV0C,YAAY,eAIhB3C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,YAAa,iBAElBgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,OAA0B,QAAnBwB,EAAAvH,EAASc,kBAAU,IAAAyG,OAAA,EAAnBA,EAAqBvG,YAAa,GACzCsF,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BD,GAAI,IACPlF,YAAUmF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAKlF,YAAU,IAAEE,UAAW2F,EAAEE,OAAOd,WAExDS,UAAU,kIACV0C,YAAY,eAIhB3C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,cAAe,kBAEpBgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,OAA0B,QAAnByB,EAAAxH,EAASc,kBAAU,IAAA0G,OAAA,EAAnBA,EAAqBvG,cAAe,GAC3CqF,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BD,GAAI,IACPlF,YAAUmF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAKlF,YAAU,IAAEG,YAAa0F,EAAEE,OAAOd,WAE1DS,UAAU,kIACV0C,YAAY,kBAIhB3C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,mBAAoB,oBAEzBgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,OAA0B,QAAnB0B,EAAAzH,EAASc,kBAAU,IAAA2G,OAAA,EAAnBA,EAAqBvG,mBAAoB,GAChDoF,SAAWK,GAAM1G,EAAY+F,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BD,GAAI,IACPlF,YAAUmF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAKlF,YAAU,IAAEI,iBAAkByF,EAAEE,OAAOd,WAE/DS,UAAU,kIACV0C,YAAY,oBAQ1B,IAAK,aACH,OACE3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oDAAmDC,SAC9D/G,EAAE,kBAAmB,kCAExBgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD/G,EAAE,iBAAkB,qHAKzB6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,WAAY,gBAEjB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,SAAU,UACnBqG,MAAO/F,EAASmB,qBAAqBC,SAASC,OAC9CiF,SAAWP,GAAUG,EAAwB,uBAAwB,WAAY,SAAUH,MAE7FW,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,WAAY,YACrBqG,MAAO/F,EAASmB,qBAAqBC,SAASE,SAC9CgF,SAAWP,GAAUG,EAAwB,uBAAwB,WAAY,WAAYH,MAE/FW,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,UAAW,WACpBqG,MAAO/F,EAASmB,qBAAqBC,SAASG,QAC9C+E,SAAWP,GAAUG,EAAwB,uBAAwB,WAAY,UAAWH,MAE9FW,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,gBAAiB,uBAC1BqG,MAAO/F,EAASmB,qBAAqBC,SAASI,cAC9C8E,SAAWP,GAAUG,EAAwB,uBAAwB,WAAY,gBAAiBH,MAEpGW,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,gBAAiB,uBAC1BqG,MAAO/F,EAASmB,qBAAqBC,SAASK,cAC9C6E,SAAWP,GAAUG,EAAwB,uBAAwB,WAAY,gBAAiBH,MAEpGW,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,YAAa,aACtBqG,MAAO/F,EAASmB,qBAAqBC,SAASM,UAC9C4E,SAAWP,GAAUG,EAAwB,uBAAwB,WAAY,YAAaH,YAMpGQ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,gBAAiB,oBAEtB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,gBAAiB,iBAC1BqG,MAAO/F,EAASmB,qBAAqBkB,cAAcC,cACnDgE,SAAWP,GAAUG,EAAwB,uBAAwB,gBAAiB,gBAAiBH,MAEzGW,EAAAA,EAAAA,KAACN,EAAa,CACZrH,MAAOW,EAAE,aAAc,cACvBqG,MAAO/F,EAASmB,qBAAqBkB,cAAcE,WACnD+D,SAAWP,GAAUG,EAAwB,uBAAwB,gBAAiB,aAAcH,cAOhH,IAAK,OACH,OACEQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,SAClE/G,EAAE,qBAAsB,iCAE3BgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SACxD/G,EAAE,kBAAmB,2FAI1B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACI,EAAe,CACd/H,MAAOW,EAAE,WAAY,aACrBqG,MAAO/F,EAASwE,eAAeC,SAC/B6B,SAAWP,GAAUH,EAAkB,iBAAkB,WAAYG,MAEvEW,EAAAA,EAAAA,KAACI,EAAe,CACd/H,MAAOW,EAAE,oBAAqB,uBAC9BqG,MAAO/F,EAASwE,eAAeE,kBAC/B4B,SAAWP,GAAUH,EAAkB,iBAAkB,oBAAqBG,MAEhFW,EAAAA,EAAAA,KAACI,EAAe,CACd/H,MAAOW,EAAE,kBAAmB,oBAC5BqG,MAAO/F,EAASwE,eAAeG,gBAC/B2B,SAAWP,GAAUH,EAAkB,iBAAkB,kBAAmBG,MAE9EW,EAAAA,EAAAA,KAACI,EAAe,CACd/H,MAAOW,EAAE,iBAAkB,mBAC3BqG,MAAO/F,EAASwE,eAAeI,eAC/B0B,SAAWP,GAAUH,EAAkB,iBAAkB,iBAAkBG,MAE7EW,EAAAA,EAAAA,KAACI,EAAe,CACd/H,MAAOW,EAAE,iBAAkB,mBAC3BqG,MAAO/F,EAASwE,eAAeK,eAC/ByB,SAAWP,GAAUH,EAAkB,iBAAkB,iBAAkBG,MAE7EW,EAAAA,EAAAA,KAACI,EAAe,CACd/H,MAAOW,EAAE,gBAAiB,kBAC1BqG,MAAO/F,EAASwE,eAAeM,cAC/BwB,SAAWP,GAAUH,EAAkB,iBAAkB,gBAAiBG,SAI9EQ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,qBAAsB,2BAE3BgH,EAAAA,EAAAA,KAAA,YACEX,MAAO/F,EAASwE,eAAeO,mBAC/BuB,SAAWK,GAAMf,EAAkB,iBAAkB,qBAAsBe,EAAEE,OAAOd,OACpF0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,4BAA6B,6FAMtD,IAAK,eACH,OACE6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,SAClE/G,EAAE,2BAA4B,2CAEjCgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SACxD/G,EAAE,0BAA2B,uFAKlC6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,sBAAuB,2BAE5B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,YAAa,iBAElB6G,EAAAA,EAAAA,MAAA,UACER,OAAsC,QAA/B2B,EAAA1H,EAAS4C,8BAAsB,IAAA8E,OAAA,EAA/BA,EAAiC7E,YAAa,SACrDyD,SAAWK,GAAMf,EAAkB,yBAA0B,YAAae,EAAEE,OAAOd,OACnFS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQU,SAAE/G,EAAE,SAAU,aACpCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,UAASU,SAAE/G,EAAE,UAAW,cACtCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,YAAWU,SAAE/G,EAAE,YAAa,gBAC1CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,eACxCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,qBAI5C6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,mBAAoB,wBAEzB6G,EAAAA,EAAAA,MAAA,UACER,OAAsC,QAA/B4B,EAAA3H,EAAS4C,8BAAsB,IAAA+E,OAAA,EAA/BA,EAAiC7E,mBAAoB,WAC5DwD,SAAWK,GAAMf,EAAkB,yBAA0B,mBAAoBe,EAAEE,OAAOd,OAC1FS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,eACxCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,cAAaU,SAAE/G,EAAE,cAAe,kBAC9CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,aAAYU,SAAE/G,EAAE,aAAc,iBAC5CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,UAASU,SAAE/G,EAAE,UAAW,oBAI1C6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,kBAAmB,uBAExB6G,EAAAA,EAAAA,MAAA,UACER,OAAsC,QAA/B6B,EAAA5H,EAAS4C,8BAAsB,IAAAgF,OAAA,EAA/BA,EAAiC7E,kBAAmB,OAC3DuD,SAAWK,GAAMf,EAAkB,yBAA0B,kBAAmBe,EAAEE,OAAOd,OACzFS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,OAAQ,WAChCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,eACxCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,MAAKU,SAAE/G,EAAE,MAAO,UAC9BgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,qBAI5C6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,eAAgB,oBAErB6G,EAAAA,EAAAA,MAAA,UACER,OAAsC,QAA/B8B,EAAA7H,EAAS4C,8BAAsB,IAAAiF,OAAA,EAA/BA,EAAiC7E,eAAgB,OACxDsD,SAAWK,GAAMf,EAAkB,yBAA0B,eAAgBe,EAAEE,OAAOd,OACtFS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,OAAQ,WAChCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,OAAQ,WAChCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,OAAQ,WAChCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQU,SAAE/G,EAAE,SAAU,yBAO5C6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,gBAAiB,4BAEtB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,gBAAiB,qBAEtB6G,EAAAA,EAAAA,MAAA,UACER,OAAsC,QAA/B+B,EAAA9H,EAAS4C,8BAAsB,IAAAkF,OAAA,EAA/BA,EAAiC7E,gBAAiB,SACzDqD,SAAWK,GAAMf,EAAkB,yBAA0B,gBAAiBe,EAAEE,OAAOd,OACvFS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQU,SAAE/G,EAAE,SAAU,aACpCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,eACxCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,UAASU,SAAE/G,EAAE,UAAW,cACtCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQU,SAAE/G,EAAE,SAAU,mBAIxC6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,oBAAqB,yBAE1B6G,EAAAA,EAAAA,MAAA,UACER,OAAsC,QAA/BgC,EAAA/H,EAAS4C,8BAAsB,IAAAmF,OAAA,EAA/BA,EAAiC7E,oBAAqB,WAC7DoD,SAAWK,GAAMf,EAAkB,yBAA0B,oBAAqBe,EAAEE,OAAOd,OAC3FS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,YAAWU,SAAE/G,EAAE,YAAa,gBAC1CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,eACxCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,UAASU,SAAE/G,EAAE,UAAW,cACtCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,2BAOhD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,oBAAqB,qCAE1B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,yBAA0B,8BAE/BgH,EAAAA,EAAAA,KAAA,YACEX,OAAsC,QAA/BiC,EAAAhI,EAAS4C,8BAAsB,IAAAoF,OAAA,EAA/BA,EAAiC7E,yBAA0B,GAClEmD,SAAWK,GAAMf,EAAkB,yBAA0B,yBAA0Be,EAAEE,OAAOd,OAChG0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,oCAAqC,gEAIxD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,iBAAkB,sBAEvBgH,EAAAA,EAAAA,KAAA,YACEX,OAAsC,QAA/BkC,EAAAjI,EAAS4C,8BAAsB,IAAAqF,OAAA,EAA/BA,EAAiC7E,iBAAkB,GAC1DkD,SAAWK,GAAMf,EAAkB,yBAA0B,iBAAkBe,EAAEE,OAAOd,OACxF0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,4BAA6B,iEAOpD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,oBAAqB,oCAE1BgH,EAAAA,EAAAA,KAAA,YACEX,OAAsC,QAA/BmC,EAAAlI,EAAS4C,8BAAsB,IAAAsF,OAAA,EAA/BA,EAAiC7E,QAAS,GACjDiD,SAAWK,GAAMf,EAAkB,yBAA0B,QAASe,EAAEE,OAAOd,OAC/E0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,+BAAgC,oEAMzD,IAAK,gBACH,OACE6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE/G,EAAE,4BAA6B,4CAElCgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAA4CC,SACtD/G,EAAE,2BAA4B,qFAKnC6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,kBAAmB,uBAExB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,oBAAqB,yBAE1B6G,EAAAA,EAAAA,MAAA,UACER,OAAuC,QAAhCoC,EAAAnI,EAAS4D,+BAAuB,IAAAuE,OAAA,EAAhCA,EAAkCtE,oBAAqB,aAC9DyC,SAAWK,GAAMf,EAAkB,0BAA2B,oBAAqBe,EAAEE,OAAOd,OAC5FS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,mBAAkBU,SAAE/G,EAAE,kBAAmB,uBACvDgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,aAAYU,SAAE/G,EAAE,aAAc,iBAC5CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,uBAAsBU,SAAE/G,EAAE,sBAAuB,2BAC/DgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,iBAAgBU,SAAE/G,EAAE,gBAAiB,2BAIvD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,aAAc,kBAEnB6G,EAAAA,EAAAA,MAAA,UACER,OAAuC,QAAhCqC,EAAApI,EAAS4D,+BAAuB,IAAAwE,OAAA,EAAhCA,EAAkCtE,aAAc,OACvDwC,SAAWK,GAAMf,EAAkB,0BAA2B,aAAce,EAAEE,OAAOd,OACrFS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,OAAQ,WAChCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,gBAAeU,SAAE/G,EAAE,eAAgB,oBACjDgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,mBAAkBU,SAAE/G,EAAE,kBAAmB,uBACvDgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQU,SAAE/G,EAAE,SAAU,sBAK1C6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,oBAAqB,qCAE1BgH,EAAAA,EAAAA,KAAA,YACEX,OAAuC,QAAhCsC,EAAArI,EAAS4D,+BAAuB,IAAAyE,OAAA,EAAhCA,EAAkCtE,oBAAqB,GAC9DuC,SAAWK,GAAMf,EAAkB,0BAA2B,oBAAqBe,EAAEE,OAAOd,OAC5F0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,+BAAgC,+EAMrD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,uBAAwB,4BAE7B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,uBAAwB,4BAE7B6G,EAAAA,EAAAA,MAAA,UACER,OAAuC,QAAhCuC,EAAAtI,EAAS4D,+BAAuB,IAAA0E,OAAA,EAAhCA,EAAkCtE,uBAAwB,WACjEsC,SAAWK,GAAMf,EAAkB,0BAA2B,uBAAwBe,EAAEE,OAAOd,OAC/FS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,YAAWU,SAAE/G,EAAE,YAAa,gBAC1CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUU,SAAE/G,EAAE,WAAY,eACxCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,UAASU,SAAE/G,EAAE,UAAW,cACtCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,OAAQ,iBAIpC6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,qBAAsB,0BAE3B6G,EAAAA,EAAAA,MAAA,UACER,OAAuC,QAAhCwC,EAAAvI,EAAS4D,+BAAuB,IAAA2E,OAAA,EAAhCA,EAAkCtE,qBAAsB,YAC/DqC,SAAWK,GAAMf,EAAkB,0BAA2B,qBAAsBe,EAAEE,OAAOd,OAC7FS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,YAAWU,SAAE/G,EAAE,YAAa,gBAC1CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,YAAWU,SAAE/G,EAAE,YAAa,gBAC1CgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,UAASU,SAAE/G,EAAE,UAAW,cACtCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,cAAaU,SAAE/G,EAAE,cAAe,8BAOtD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,qBAAsB,qCAE3BgH,EAAAA,EAAAA,KAAA,YACEX,OAAuC,QAAhCyC,EAAAxI,EAAS4D,+BAAuB,IAAA4E,OAAA,EAAhCA,EAAkCnF,QAAS,GAClDiD,SAAWK,GAAMf,EAAkB,0BAA2B,QAASe,EAAEE,OAAOd,OAChF0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,gCAAiC,wEAM1D,IAAK,QACH,OACE6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,SAClE/G,EAAE,oBAAqB,yCAE1BgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SACxD/G,EAAE,mBAAoB,4EAK3B6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,oBAAqB,0BAE1B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,iBAAkB,oCAEvBgH,EAAAA,EAAAA,KAAA,YACEX,OAAmC,QAA5B0C,EAAAzI,EAASgF,2BAAmB,IAAAyD,OAAA,EAA5BA,EAA8BiB,iBAAkB,GACvDpD,SAAWK,GAAMf,EAAkB,sBAAuB,iBAAkBe,EAAEE,OAAOd,OACrF0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,4BAA6B,uDAIhD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,gBAAiB,kCAEtBgH,EAAAA,EAAAA,KAAA,YACEX,OAAmC,QAA5B2C,EAAA1I,EAASgF,2BAAmB,IAAA0D,OAAA,EAA5BA,EAA8BiB,gBAAiB,GACtDrD,SAAWK,GAAMf,EAAkB,sBAAuB,gBAAiBe,EAAEE,OAAOd,OACpF0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,2BAA4B,2DAOnD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,cAAe,8BAEpB6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,uBAAwB,4BAE7BgH,EAAAA,EAAAA,KAAA,YACEX,OAAmC,QAA5B4C,EAAA3I,EAASgF,2BAAmB,IAAA2D,OAAA,EAA5BA,EAA8BiB,uBAAwB,GAC7DtD,SAAWK,GAAMf,EAAkB,sBAAuB,uBAAwBe,EAAEE,OAAOd,OAC3F0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,kCAAmC,4DAItD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,2BAA4B,gCAEjCgH,EAAAA,EAAAA,KAAA,YACEX,OAAmC,QAA5B6C,EAAA5I,EAASgF,2BAAmB,IAAA4D,OAAA,EAA5BA,EAA8BiB,2BAA4B,GACjEvD,SAAWK,GAAMf,EAAkB,sBAAuB,2BAA4Be,EAAEE,OAAOd,OAC/F0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,sCAAuC,gEAO9D6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/G,EAAE,oBAAqB,yBAE1B6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,iBAAkB,sBAEvBgH,EAAAA,EAAAA,KAAA,YACEX,OAAmC,QAA5B8C,EAAA7I,EAASgF,2BAAmB,IAAA6D,OAAA,EAA5BA,EAA8BiB,iBAAkB,GACvDxD,SAAWK,GAAMf,EAAkB,sBAAuB,iBAAkBe,EAAEE,OAAOd,OACrF0D,KAAK,IACLjD,UAAU,kIACV0C,YAAaxJ,EAAE,4BAA6B,wEAIhD6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,kBAAmB,+BAExBgH,EAAAA,EAAAA,KAAA,SACEsC,KAAK,OACLjD,OAAmC,QAA5B+C,EAAA9I,EAASgF,2BAAmB,IAAA8D,OAAA,EAA5BA,EAA8BiB,kBAAmB,GACxDzD,SAAWK,GAAMf,EAAkB,sBAAuB,kBAAmBe,EAAEE,OAAOd,OACtFS,UAAU,kIACV0C,YAAaxJ,EAAE,6BAA8B,yBAIjD6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E/G,EAAE,uBAAwB,wCAE7B6G,EAAAA,EAAAA,MAAA,UACER,OAAmC,QAA5BgD,EAAA/I,EAASgF,2BAAmB,IAAA+D,OAAA,EAA5BA,EAA8BiB,uBAAwB,OAC7D1D,SAAWK,GAAMf,EAAkB,sBAAuB,uBAAwBe,EAAEE,OAAOd,OAC3FS,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMU,SAAE/G,EAAE,OAAQ,WAChCgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,kBAAiBU,SAAE/G,EAAE,iBAAkB,sBACrDgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,kBAAiBU,SAAE/G,EAAE,iBAAkB,+BACrDgH,EAAAA,EAAAA,KAAA,UAAQX,MAAM,iBAAgBU,SAAE/G,EAAE,gBAAiB,sCASnE,QACE,OAAO6G,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CAAK,uBAAqBZ,EAAQH,WAmExC2B,MAIHd,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+EAA8EC,SAAA,EAC3FF,EAAAA,EAAAA,MAAA,UACEU,QAASA,IAAMnH,EAAkBmK,KAAKC,IAAI,EAAGrK,EAAiB,IAC9DsK,SAA6B,IAAnBtK,EACV2G,UAAU,uIAAsIC,SAAA,EAEhJC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZ9G,EAAE,WAAY,eAGhBG,IAAmB2F,EAAS4B,OAAS,GACpCb,EAAAA,EAAAA,MAAA,UACEU,QAASA,IAAM1H,EAAOS,GACtBwG,UAAU,oFAAmFC,SAAA,EAE7FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ9G,EAAE,iBAAkB,uBAGvB6G,EAAAA,EAAAA,MAAA,UACEU,QAASA,IAAMnH,EAAkBmK,KAAKG,IAAI5E,EAAS4B,OAAS,EAAGvH,EAAiB,IAChF2G,UAAU,kFAAiFC,SAAA,CAE1F/G,EAAE,OAAQ,SACXgH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qC", "sources": ["utils/carfStandards.js", "components/CARF/CARFAssessmentForm.jsx"], "sourcesContent": ["// CARF (Commission on Accreditation of Rehabilitation Facilities) Standards Configuration\n\nexport const CARF_STANDARDS = {\n  // CARF Documentation Requirements\n  DOCUMENTATION: {\n    ASSESSMENT: {\n      id: 'DOC_ASSESS',\n      title: 'Initial Assessment Requirements',\n      description: 'Comprehensive assessment within required timeframes',\n      requirements: [\n        'Initial assessment completed within 72 hours of admission',\n        'Comprehensive medical history documented',\n        'Functional assessment using standardized tools',\n        'Psychosocial assessment completed',\n        'Environmental assessment conducted',\n        'Risk assessment documented',\n        'Cultural and linguistic needs identified',\n        'Person-served preferences documented'\n      ],\n      timeframe: '72 hours',\n      responsible: 'Clinical Team',\n      category: 'Assessment'\n    },\n    TREATMENT_PLAN: {\n      id: 'DOC_PLAN',\n      title: 'Treatment Plan Documentation',\n      description: 'Individualized treatment plan requirements',\n      requirements: [\n        'Treatment plan developed within 30 days',\n        'Measurable goals and objectives defined',\n        'Person-served involvement in planning',\n        'Interdisciplinary team input documented',\n        'Frequency and duration of services specified',\n        'Discharge criteria established',\n        'Plan reviewed and updated regularly',\n        'Person-served signature obtained'\n      ],\n      timeframe: '30 days',\n      responsible: 'Treatment Team',\n      category: 'Planning'\n    },\n    PROGRESS_NOTES: {\n      id: 'DOC_PROGRESS',\n      title: 'Progress Documentation',\n      description: 'Regular progress monitoring and documentation',\n      requirements: [\n        'Progress notes completed after each session',\n        'Objective measurements recorded',\n        'Goal progress documented',\n        'Barriers to progress identified',\n        'Plan modifications noted',\n        'Person-served response documented',\n        'Safety incidents reported',\n        'Interdisciplinary communication noted'\n      ],\n      timeframe: 'Each session',\n      responsible: 'Service Provider',\n      category: 'Progress'\n    }\n  },\n\n  // CARF Quality Indicators\n  QUALITY_INDICATORS: {\n    SATISFACTION: {\n      id: 'QI_SAT',\n      title: 'Person-Served Satisfaction',\n      description: 'Satisfaction measurement and improvement',\n      metrics: [\n        'Overall satisfaction score ≥ 85%',\n        'Service quality rating ≥ 4.0/5.0',\n        'Recommendation likelihood ≥ 80%',\n        'Communication effectiveness ≥ 85%',\n        'Cultural competency rating ≥ 85%'\n      ],\n      frequency: 'Quarterly',\n      target: '≥ 85%',\n      category: 'Satisfaction'\n    },\n    OUTCOMES: {\n      id: 'QI_OUT',\n      title: 'Treatment Outcomes',\n      description: 'Functional improvement and goal achievement',\n      metrics: [\n        'Goal achievement rate ≥ 80%',\n        'Functional improvement ≥ 70%',\n        'Discharge to community ≥ 85%',\n        'Readmission rate ≤ 10%',\n        'Length of stay within targets'\n      ],\n      frequency: 'Monthly',\n      target: 'Varies by metric',\n      category: 'Outcomes'\n    },\n    EFFICIENCY: {\n      id: 'QI_EFF',\n      title: 'Service Efficiency',\n      description: 'Timely and efficient service delivery',\n      metrics: [\n        'Assessment completion within 72 hours',\n        'Treatment plan within 30 days',\n        'Service initiation within 14 days',\n        'Discharge planning within timeframes',\n        'Documentation completion rates ≥ 95%'\n      ],\n      frequency: 'Monthly',\n      target: '≥ 95%',\n      category: 'Efficiency'\n    }\n  },\n\n  // CARF Outcome Measures\n  OUTCOME_MEASURES: {\n    FUNCTIONAL: {\n      id: 'OM_FUNC',\n      title: 'Functional Independence Measure (FIM)',\n      description: 'Standardized functional assessment tool',\n      domains: [\n        'Self-Care',\n        'Sphincter Control',\n        'Transfers',\n        'Locomotion',\n        'Communication',\n        'Social Cognition'\n      ],\n      scale: '1-7 (1=Total Assist, 7=Complete Independence)',\n      frequency: 'Admission, Discharge, Follow-up',\n      category: 'Functional'\n    },\n    QUALITY_OF_LIFE: {\n      id: 'OM_QOL',\n      title: 'Quality of Life Measures',\n      description: 'Person-centered quality of life assessment',\n      domains: [\n        'Physical Health',\n        'Psychological Well-being',\n        'Social Relationships',\n        'Environmental Factors',\n        'Personal Satisfaction',\n        'Community Integration'\n      ],\n      scale: 'Standardized QOL instruments',\n      frequency: 'Quarterly',\n      category: 'Quality of Life'\n    },\n    PARTICIPATION: {\n      id: 'OM_PART',\n      title: 'Community Participation',\n      description: 'Community integration and participation measures',\n      domains: [\n        'Employment/Education',\n        'Social Activities',\n        'Community Mobility',\n        'Independent Living',\n        'Recreation/Leisure',\n        'Civic Participation'\n      ],\n      scale: 'Participation frequency and satisfaction',\n      frequency: 'Discharge, 3-month, 6-month follow-up',\n      category: 'Participation'\n    }\n  },\n\n  // CARF Risk Management\n  RISK_MANAGEMENT: {\n    SAFETY: {\n      id: 'RM_SAFETY',\n      title: 'Safety Risk Assessment',\n      description: 'Comprehensive safety risk evaluation',\n      areas: [\n        'Fall risk assessment',\n        'Medication safety',\n        'Equipment safety',\n        'Environmental hazards',\n        'Behavioral risks',\n        'Medical complications'\n      ],\n      frequency: 'Admission and ongoing',\n      category: 'Safety'\n    },\n    INCIDENTS: {\n      id: 'RM_INC',\n      title: 'Incident Reporting',\n      description: 'Systematic incident tracking and analysis',\n      types: [\n        'Falls',\n        'Medication errors',\n        'Equipment failures',\n        'Behavioral incidents',\n        'Medical emergencies',\n        'Rights violations'\n      ],\n      timeframe: 'Immediate reporting',\n      category: 'Incidents'\n    }\n  },\n\n  // CARF Performance Improvement\n  PERFORMANCE_IMPROVEMENT: {\n    DATA_COLLECTION: {\n      id: 'PI_DATA',\n      title: 'Data Collection and Analysis',\n      description: 'Systematic data collection for improvement',\n      requirements: [\n        'Regular data collection protocols',\n        'Statistical analysis methods',\n        'Trend identification',\n        'Benchmark comparisons',\n        'Root cause analysis',\n        'Action plan development'\n      ],\n      frequency: 'Ongoing',\n      category: 'Data'\n    },\n    CORRECTIVE_ACTION: {\n      id: 'PI_ACTION',\n      title: 'Corrective Action Plans',\n      description: 'Systematic approach to addressing deficiencies',\n      components: [\n        'Problem identification',\n        'Root cause analysis',\n        'Action plan development',\n        'Implementation timeline',\n        'Responsibility assignment',\n        'Monitoring and evaluation'\n      ],\n      timeframe: 'As needed',\n      category: 'Improvement'\n    }\n  }\n};\n\n// CARF Compliance Tracking\nexport const CARF_COMPLIANCE_LEVELS = {\n  FULL_COMPLIANCE: {\n    score: 100,\n    label: 'Full Compliance',\n    color: 'green',\n    description: 'Meets all CARF standards'\n  },\n  SUBSTANTIAL_COMPLIANCE: {\n    score: 85,\n    label: 'Substantial Compliance',\n    color: 'yellow',\n    description: 'Minor areas for improvement'\n  },\n  PARTIAL_COMPLIANCE: {\n    score: 70,\n    label: 'Partial Compliance',\n    color: 'orange',\n    description: 'Significant improvements needed'\n  },\n  NON_COMPLIANCE: {\n    score: 0,\n    label: 'Non-Compliance',\n    color: 'red',\n    description: 'Major deficiencies identified'\n  }\n};\n\n// CARF Assessment Tools\nexport const CARF_ASSESSMENT_TOOLS = {\n  FIM: {\n    name: 'Functional Independence Measure',\n    domains: ['Motor', 'Cognitive'],\n    items: 18,\n    scale: '1-7',\n    administration: 'Trained clinician'\n  },\n  CARE: {\n    name: 'Comprehensive Assessment of Rehabilitation Environments',\n    purpose: 'Environmental assessment',\n    domains: ['Physical', 'Social', 'Cultural'],\n    frequency: 'Annual'\n  },\n  SATISFACTION_SURVEY: {\n    name: 'Person-Served Satisfaction Survey',\n    purpose: 'Satisfaction measurement',\n    frequency: 'Quarterly',\n    method: 'Survey or interview'\n  }\n};\n\n// CARF Documentation Templates\nexport const CARF_TEMPLATES = {\n  ASSESSMENT: {\n    sections: [\n      'Demographic Information',\n      'Medical History',\n      'Functional Assessment',\n      'Psychosocial Assessment',\n      'Environmental Assessment',\n      'Risk Assessment',\n      'Goals and Preferences'\n    ]\n  },\n  TREATMENT_PLAN: {\n    sections: [\n      'Assessment Summary',\n      'Long-term Goals',\n      'Short-term Objectives',\n      'Service Plan',\n      'Discharge Criteria',\n      'Person-Served Input',\n      'Team Signatures'\n    ]\n  },\n  PROGRESS_NOTE: {\n    sections: [\n      'Session Information',\n      'Objective Measurements',\n      'Goal Progress',\n      'Interventions Provided',\n      'Person Response',\n      'Plan Modifications',\n      'Next Session Plan'\n    ]\n  }\n};\n\nexport default CARF_STANDARDS;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { CARF_TEMPLATES } from '../../utils/carfStandards';\n\nconst CARFAssessmentForm = ({ patientId, onSave, onCancel, initialData = {} }) => {\n  const { t, isRTL } = useLanguage();\n  const [currentSection, setCurrentSection] = useState(0);\n  const [formData, setFormData] = useState({\n    // Demographic Information\n    patientId: patientId || '',\n    assessmentDate: new Date().toISOString().split('T')[0],\n    assessor: '',\n    assessorCredentials: '',\n    \n    // Medical History\n    primaryDiagnosis: initialData.primaryDiagnosis || '',\n    secondaryDiagnoses: initialData.secondaryDiagnoses || [],\n    medicalHistory: initialData.medicalHistory || '',\n    currentMedications: initialData.currentMedications || '',\n    allergies: initialData.allergies || '',\n    precautions: initialData.precautions || '',\n\n    // Vital Signs\n    vitalSigns: {\n      bloodPressure: '',\n      heartRate: '',\n      temperature: '',\n      oxygenSaturation: ''\n    },\n    \n    // Functional Assessment (FIM Scores)\n    functionalAssessment: {\n      selfCare: {\n        eating: 7,\n        grooming: 7,\n        bathing: 7,\n        dressingUpper: 7,\n        dressingLower: 7,\n        toileting: 7\n      },\n      sphincterControl: {\n        bladder: 7,\n        bowel: 7\n      },\n      transfers: {\n        bedChairWheelchair: 7,\n        toilet: 7,\n        tubShower: 7\n      },\n      locomotion: {\n        walkWheelchair: 7,\n        stairs: 7\n      },\n      communication: {\n        comprehension: 7,\n        expression: 7\n      },\n      socialCognition: {\n        socialInteraction: 7,\n        problemSolving: 7,\n        memory: 7\n      }\n    },\n    \n    // Psychosocial Assessment\n    psychosocialAssessment: {\n      moodState: 'stable',\n      copingStrategies: 'adaptive',\n      motivationLevel: 'high',\n      insightLevel: 'good',\n      familySupport: 'strong',\n      socialConnections: 'adequate',\n      culturalConsiderations: '',\n      spiritualNeeds: '',\n      notes: '',\n      // Legacy fields for compatibility\n      mentalStatus: '',\n      cognitiveFunction: '',\n      emotionalStatus: '',\n      copingMechanisms: '',\n      socialSupport: '',\n      culturalFactors: ''\n    },\n\n    // Environmental Assessment\n    environmentalAssessment: {\n      homeAccessibility: 'accessible',\n      homeSafety: 'safe',\n      homeModifications: '',\n      transportationAccess: 'adequate',\n      communityResources: 'available',\n      notes: '',\n      // Legacy fields for compatibility\n      livingArrangement: '',\n      accessibility: '',\n      safetyHazards: '',\n      transportation: '',\n      financialResources: '',\n      equipmentNeeds: ''\n    },\n\n    // Risk Assessment\n    riskAssessment: {\n      fallRisk: 'low',\n      skinIntegrityRisk: 'low',\n      nutritionalRisk: 'low',\n      medicationRisk: 'low',\n      behavioralRisk: 'low',\n      cognitiveRisk: 'low',\n      riskMitigationPlan: ''\n    },\n    \n    // Goals and Preferences\n    goalsAndPreferences: {\n      personServedGoals: '',\n      familyGoals: '',\n      clinicalGoals: '',\n      preferences: '',\n      barriers: '',\n      strengths: '',\n      dischargePlanning: ''\n    }\n  });\n\n  const sections = [\n    { id: 'demographic', title: t('demographicInformation', 'Demographic Information'), icon: 'fas fa-user' },\n    { id: 'medical', title: t('medicalHistory', 'Medical History'), icon: 'fas fa-notes-medical' },\n    { id: 'functional', title: t('functionalAssessment', 'Functional Assessment (FIM)'), icon: 'fas fa-tasks' },\n    { id: 'psychosocial', title: t('psychosocialAssessment', 'Psychosocial Assessment'), icon: 'fas fa-brain' },\n    { id: 'environmental', title: t('environmentalAssessment', 'Environmental Assessment'), icon: 'fas fa-home' },\n    { id: 'risk', title: t('riskAssessment', 'Risk Assessment'), icon: 'fas fa-exclamation-triangle' },\n    { id: 'goals', title: t('goalsAndPreferences', 'Goals and Preferences'), icon: 'fas fa-bullseye' }\n  ];\n\n  const handleInputChange = (section, field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: value\n      }\n    }));\n  };\n\n  const handleNestedInputChange = (section, subsection, field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [subsection]: {\n          ...prev[section][subsection],\n          [field]: value\n        }\n      }\n    }));\n  };\n\n  const FIMScoreInput = ({ label, value, onChange, description }) => (\n    <div className=\"space-y-2\">\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n        {label}\n      </label>\n      <select\n        value={value}\n        onChange={(e) => onChange(parseInt(e.target.value))}\n        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n      >\n        <option value={7}>7 - Complete Independence</option>\n        <option value={6}>6 - Modified Independence</option>\n        <option value={5}>5 - Supervision/Setup</option>\n        <option value={4}>4 - Minimal Contact Assistance</option>\n        <option value={3}>3 - Moderate Assistance</option>\n        <option value={2}>2 - Maximal Assistance</option>\n        <option value={1}>1 - Total Assistance</option>\n      </select>\n      {description && (\n        <p className=\"text-xs text-gray-500 dark:text-gray-400\">{description}</p>\n      )}\n    </div>\n  );\n\n  const RiskLevelSelect = ({ label, value, onChange }) => (\n    <div className=\"space-y-2\">\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n        {label}\n      </label>\n      <select\n        value={value}\n        onChange={(e) => onChange(e.target.value)}\n        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n      >\n        <option value=\"low\">{t('lowRisk', 'Low Risk')}</option>\n        <option value=\"moderate\">{t('moderateRisk', 'Moderate Risk')}</option>\n        <option value=\"high\">{t('highRisk', 'High Risk')}</option>\n      </select>\n    </div>\n  );\n\n  const renderSection = () => {\n    const section = sections[currentSection];\n    \n    switch (section.id) {\n      case 'demographic':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('assessmentDate', 'Assessment Date')} *\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.assessmentDate}\n                  onChange={(e) => setFormData(prev => ({ ...prev, assessmentDate: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('assessor', 'Assessor')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.assessor}\n                  onChange={(e) => setFormData(prev => ({ ...prev, assessor: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('assessorName', 'Assessor Name')}\n                  required\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('assessorCredentials', 'Assessor Credentials')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.assessorCredentials}\n                  onChange={(e) => setFormData(prev => ({ ...prev, assessorCredentials: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('credentialsPlaceholder', 'e.g., PT, DPT, OTR/L')}\n                />\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'medical':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-red-900 dark:text-red-100 mb-2\">\n                {t('medicalInstructions', 'Medical History Instructions')}\n              </h4>\n              <p className=\"text-sm text-red-800 dark:text-red-200\">\n                {t('medicalDescription', 'Document comprehensive medical history, current conditions, and medications.')}\n              </p>\n            </div>\n\n            {/* Primary and Secondary Diagnoses */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('diagnoses', 'Diagnoses')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('primaryDiagnosis', 'Primary Diagnosis')} *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.primaryDiagnosis}\n                    onChange={(e) => setFormData(prev => ({ ...prev, primaryDiagnosis: e.target.value }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('primaryDiagnosisPlaceholder', 'e.g., Stroke, TBI, SCI')}\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('secondaryDiagnoses', 'Secondary Diagnoses')}\n                  </label>\n                  <textarea\n                    value={Array.isArray(formData.secondaryDiagnoses) ? formData.secondaryDiagnoses.join(', ') : formData.secondaryDiagnoses}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      secondaryDiagnoses: e.target.value.split(',').map(d => d.trim()).filter(d => d)\n                    }))}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('secondaryDiagnosesPlaceholder', 'List secondary diagnoses separated by commas...')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Medical History */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('medicalHistory', 'Medical History')}\n              </h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('relevantMedicalHistory', 'Relevant Medical History')}\n                  </label>\n                  <textarea\n                    value={formData.medicalHistory}\n                    onChange={(e) => setFormData(prev => ({ ...prev, medicalHistory: e.target.value }))}\n                    rows=\"4\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('medicalHistoryPlaceholder', 'Include previous surgeries, hospitalizations, chronic conditions...')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('currentMedications', 'Current Medications')}\n                  </label>\n                  <textarea\n                    value={formData.currentMedications}\n                    onChange={(e) => setFormData(prev => ({ ...prev, currentMedications: e.target.value }))}\n                    rows=\"4\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('medicationsPlaceholder', 'List all current medications with dosages...')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Allergies and Precautions */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('allergiesPrecautions', 'Allergies and Precautions')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('allergies', 'Known Allergies')}\n                  </label>\n                  <textarea\n                    value={formData.allergies}\n                    onChange={(e) => setFormData(prev => ({ ...prev, allergies: e.target.value }))}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('allergiesPlaceholder', 'List drug allergies, food allergies, environmental allergies...')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('precautions', 'Medical Precautions')}\n                  </label>\n                  <textarea\n                    value={formData.precautions}\n                    onChange={(e) => setFormData(prev => ({ ...prev, precautions: e.target.value }))}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('precautionsPlaceholder', 'Weight bearing restrictions, cardiac precautions, etc...')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Vital Signs and Physical Status */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('vitalSigns', 'Vital Signs and Physical Status')}\n              </h4>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('bloodPressure', 'Blood Pressure')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.vitalSigns?.bloodPressure || ''}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      vitalSigns: { ...prev.vitalSigns, bloodPressure: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder=\"120/80\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('heartRate', 'Heart Rate')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.vitalSigns?.heartRate || ''}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      vitalSigns: { ...prev.vitalSigns, heartRate: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder=\"72 bpm\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('temperature', 'Temperature')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.vitalSigns?.temperature || ''}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      vitalSigns: { ...prev.vitalSigns, temperature: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder=\"98.6°F\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('oxygenSaturation', 'O2 Saturation')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.vitalSigns?.oxygenSaturation || ''}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      vitalSigns: { ...prev.vitalSigns, oxygenSaturation: e.target.value }\n                    }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder=\"98%\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'functional':\n        return (\n          <div className=\"space-y-8\">\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-2\">\n                {t('fimInstructions', 'FIM Assessment Instructions')}\n              </h4>\n              <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n                {t('fimDescription', 'Rate each item based on the amount of assistance required. 7 = Complete Independence, 1 = Total Assistance.')}\n              </p>\n            </div>\n\n            {/* Self-Care */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('selfCare', 'Self-Care')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                <FIMScoreInput\n                  label={t('eating', 'Eating')}\n                  value={formData.functionalAssessment.selfCare.eating}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'selfCare', 'eating', value)}\n                />\n                <FIMScoreInput\n                  label={t('grooming', 'Grooming')}\n                  value={formData.functionalAssessment.selfCare.grooming}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'selfCare', 'grooming', value)}\n                />\n                <FIMScoreInput\n                  label={t('bathing', 'Bathing')}\n                  value={formData.functionalAssessment.selfCare.bathing}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'selfCare', 'bathing', value)}\n                />\n                <FIMScoreInput\n                  label={t('dressingUpper', 'Dressing Upper Body')}\n                  value={formData.functionalAssessment.selfCare.dressingUpper}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'selfCare', 'dressingUpper', value)}\n                />\n                <FIMScoreInput\n                  label={t('dressingLower', 'Dressing Lower Body')}\n                  value={formData.functionalAssessment.selfCare.dressingLower}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'selfCare', 'dressingLower', value)}\n                />\n                <FIMScoreInput\n                  label={t('toileting', 'Toileting')}\n                  value={formData.functionalAssessment.selfCare.toileting}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'selfCare', 'toileting', value)}\n                />\n              </div>\n            </div>\n\n            {/* Communication */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('communication', 'Communication')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <FIMScoreInput\n                  label={t('comprehension', 'Comprehension')}\n                  value={formData.functionalAssessment.communication.comprehension}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'communication', 'comprehension', value)}\n                />\n                <FIMScoreInput\n                  label={t('expression', 'Expression')}\n                  value={formData.functionalAssessment.communication.expression}\n                  onChange={(value) => handleNestedInputChange('functionalAssessment', 'communication', 'expression', value)}\n                />\n              </div>\n            </div>\n          </div>\n        );\n\n      case 'risk':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-yellow-900 dark:text-yellow-100 mb-2\">\n                {t('riskAssessmentNote', 'Risk Assessment Guidelines')}\n              </h4>\n              <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n                {t('riskDescription', 'Assess each risk category and develop mitigation strategies for identified risks.')}\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <RiskLevelSelect\n                label={t('fallRisk', 'Fall Risk')}\n                value={formData.riskAssessment.fallRisk}\n                onChange={(value) => handleInputChange('riskAssessment', 'fallRisk', value)}\n              />\n              <RiskLevelSelect\n                label={t('skinIntegrityRisk', 'Skin Integrity Risk')}\n                value={formData.riskAssessment.skinIntegrityRisk}\n                onChange={(value) => handleInputChange('riskAssessment', 'skinIntegrityRisk', value)}\n              />\n              <RiskLevelSelect\n                label={t('nutritionalRisk', 'Nutritional Risk')}\n                value={formData.riskAssessment.nutritionalRisk}\n                onChange={(value) => handleInputChange('riskAssessment', 'nutritionalRisk', value)}\n              />\n              <RiskLevelSelect\n                label={t('medicationRisk', 'Medication Risk')}\n                value={formData.riskAssessment.medicationRisk}\n                onChange={(value) => handleInputChange('riskAssessment', 'medicationRisk', value)}\n              />\n              <RiskLevelSelect\n                label={t('behavioralRisk', 'Behavioral Risk')}\n                value={formData.riskAssessment.behavioralRisk}\n                onChange={(value) => handleInputChange('riskAssessment', 'behavioralRisk', value)}\n              />\n              <RiskLevelSelect\n                label={t('cognitiveRisk', 'Cognitive Risk')}\n                value={formData.riskAssessment.cognitiveRisk}\n                onChange={(value) => handleInputChange('riskAssessment', 'cognitiveRisk', value)}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('riskMitigationPlan', 'Risk Mitigation Plan')}\n              </label>\n              <textarea\n                value={formData.riskAssessment.riskMitigationPlan}\n                onChange={(e) => handleInputChange('riskAssessment', 'riskMitigationPlan', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('riskMitigationPlaceholder', 'Describe specific interventions and monitoring strategies for identified risks...')}\n              />\n            </div>\n          </div>\n        );\n\n      case 'psychosocial':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-purple-900 dark:text-purple-100 mb-2\">\n                {t('psychosocialInstructions', 'Psychosocial Assessment Instructions')}\n              </h4>\n              <p className=\"text-sm text-purple-800 dark:text-purple-200\">\n                {t('psychosocialDescription', 'Assess psychological, social, and emotional factors affecting rehabilitation.')}\n              </p>\n            </div>\n\n            {/* Psychological Status */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('psychologicalStatus', 'Psychological Status')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('moodState', 'Mood State')}\n                  </label>\n                  <select\n                    value={formData.psychosocialAssessment?.moodState || 'stable'}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'moodState', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"stable\">{t('stable', 'Stable')}</option>\n                    <option value=\"anxious\">{t('anxious', 'Anxious')}</option>\n                    <option value=\"depressed\">{t('depressed', 'Depressed')}</option>\n                    <option value=\"agitated\">{t('agitated', 'Agitated')}</option>\n                    <option value=\"euphoric\">{t('euphoric', 'Euphoric')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('copingStrategies', 'Coping Strategies')}\n                  </label>\n                  <select\n                    value={formData.psychosocialAssessment?.copingStrategies || 'adaptive'}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'copingStrategies', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"adaptive\">{t('adaptive', 'Adaptive')}</option>\n                    <option value=\"maladaptive\">{t('maladaptive', 'Maladaptive')}</option>\n                    <option value=\"developing\">{t('developing', 'Developing')}</option>\n                    <option value=\"limited\">{t('limited', 'Limited')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('motivationLevel', 'Motivation Level')}\n                  </label>\n                  <select\n                    value={formData.psychosocialAssessment?.motivationLevel || 'high'}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'motivationLevel', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"high\">{t('high', 'High')}</option>\n                    <option value=\"moderate\">{t('moderate', 'Moderate')}</option>\n                    <option value=\"low\">{t('low', 'Low')}</option>\n                    <option value=\"variable\">{t('variable', 'Variable')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('insightLevel', 'Insight Level')}\n                  </label>\n                  <select\n                    value={formData.psychosocialAssessment?.insightLevel || 'good'}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'insightLevel', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"good\">{t('good', 'Good')}</option>\n                    <option value=\"fair\">{t('fair', 'Fair')}</option>\n                    <option value=\"poor\">{t('poor', 'Poor')}</option>\n                    <option value=\"absent\">{t('absent', 'Absent')}</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* Social Support */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('socialSupport', 'Social Support System')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('familySupport', 'Family Support')}\n                  </label>\n                  <select\n                    value={formData.psychosocialAssessment?.familySupport || 'strong'}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'familySupport', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"strong\">{t('strong', 'Strong')}</option>\n                    <option value=\"moderate\">{t('moderate', 'Moderate')}</option>\n                    <option value=\"limited\">{t('limited', 'Limited')}</option>\n                    <option value=\"absent\">{t('absent', 'Absent')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('socialConnections', 'Social Connections')}\n                  </label>\n                  <select\n                    value={formData.psychosocialAssessment?.socialConnections || 'adequate'}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'socialConnections', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"extensive\">{t('extensive', 'Extensive')}</option>\n                    <option value=\"adequate\">{t('adequate', 'Adequate')}</option>\n                    <option value=\"limited\">{t('limited', 'Limited')}</option>\n                    <option value=\"isolated\">{t('isolated', 'Isolated')}</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* Cultural and Spiritual Factors */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('culturalSpiritual', 'Cultural and Spiritual Factors')}\n              </h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('culturalConsiderations', 'Cultural Considerations')}\n                  </label>\n                  <textarea\n                    value={formData.psychosocialAssessment?.culturalConsiderations || ''}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'culturalConsiderations', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('culturalConsiderationsPlaceholder', 'Describe cultural factors that may impact treatment...')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('spiritualNeeds', 'Spiritual Needs')}\n                  </label>\n                  <textarea\n                    value={formData.psychosocialAssessment?.spiritualNeeds || ''}\n                    onChange={(e) => handleInputChange('psychosocialAssessment', 'spiritualNeeds', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('spiritualNeedsPlaceholder', 'Describe spiritual or religious considerations...')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Psychosocial Notes */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('psychosocialNotes', 'Additional Psychosocial Notes')}\n              </label>\n              <textarea\n                value={formData.psychosocialAssessment?.notes || ''}\n                onChange={(e) => handleInputChange('psychosocialAssessment', 'notes', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('psychosocialNotesPlaceholder', 'Additional observations, concerns, or recommendations...')}\n              />\n            </div>\n          </div>\n        );\n\n      case 'environmental':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-green-900 dark:text-green-100 mb-2\">\n                {t('environmentalInstructions', 'Environmental Assessment Instructions')}\n              </h4>\n              <p className=\"text-sm text-green-800 dark:text-green-200\">\n                {t('environmentalDescription', 'Assess home, work, and community environments for accessibility and safety.')}\n              </p>\n            </div>\n\n            {/* Home Environment */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('homeEnvironment', 'Home Environment')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('homeAccessibility', 'Home Accessibility')}\n                  </label>\n                  <select\n                    value={formData.environmentalAssessment?.homeAccessibility || 'accessible'}\n                    onChange={(e) => handleInputChange('environmentalAssessment', 'homeAccessibility', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"fully-accessible\">{t('fullyAccessible', 'Fully Accessible')}</option>\n                    <option value=\"accessible\">{t('accessible', 'Accessible')}</option>\n                    <option value=\"partially-accessible\">{t('partiallyAccessible', 'Partially Accessible')}</option>\n                    <option value=\"not-accessible\">{t('notAccessible', 'Not Accessible')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('homeSafety', 'Home Safety')}\n                  </label>\n                  <select\n                    value={formData.environmentalAssessment?.homeSafety || 'safe'}\n                    onChange={(e) => handleInputChange('environmentalAssessment', 'homeSafety', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"safe\">{t('safe', 'Safe')}</option>\n                    <option value=\"minor-hazards\">{t('minorHazards', 'Minor Hazards')}</option>\n                    <option value=\"moderate-hazards\">{t('moderateHazards', 'Moderate Hazards')}</option>\n                    <option value=\"unsafe\">{t('unsafe', 'Unsafe')}</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('homeModifications', 'Recommended Home Modifications')}\n                </label>\n                <textarea\n                  value={formData.environmentalAssessment?.homeModifications || ''}\n                  onChange={(e) => handleInputChange('environmentalAssessment', 'homeModifications', e.target.value)}\n                  rows=\"3\"\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('homeModificationsPlaceholder', 'Describe recommended modifications for safety and accessibility...')}\n                />\n              </div>\n            </div>\n\n            {/* Community Environment */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('communityEnvironment', 'Community Environment')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('transportationAccess', 'Transportation Access')}\n                  </label>\n                  <select\n                    value={formData.environmentalAssessment?.transportationAccess || 'adequate'}\n                    onChange={(e) => handleInputChange('environmentalAssessment', 'transportationAccess', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"excellent\">{t('excellent', 'Excellent')}</option>\n                    <option value=\"adequate\">{t('adequate', 'Adequate')}</option>\n                    <option value=\"limited\">{t('limited', 'Limited')}</option>\n                    <option value=\"none\">{t('none', 'None')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('communityResources', 'Community Resources')}\n                  </label>\n                  <select\n                    value={formData.environmentalAssessment?.communityResources || 'available'}\n                    onChange={(e) => handleInputChange('environmentalAssessment', 'communityResources', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"extensive\">{t('extensive', 'Extensive')}</option>\n                    <option value=\"available\">{t('available', 'Available')}</option>\n                    <option value=\"limited\">{t('limited', 'Limited')}</option>\n                    <option value=\"unavailable\">{t('unavailable', 'Unavailable')}</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* Environmental Notes */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('environmentalNotes', 'Environmental Assessment Notes')}\n              </label>\n              <textarea\n                value={formData.environmentalAssessment?.notes || ''}\n                onChange={(e) => handleInputChange('environmentalAssessment', 'notes', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('environmentalNotesPlaceholder', 'Additional environmental observations and recommendations...')}\n              />\n            </div>\n          </div>\n        );\n\n      case 'goals':\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-indigo-900 dark:text-indigo-100 mb-2\">\n                {t('goalsInstructions', 'Goals and Preferences Instructions')}\n              </h4>\n              <p className=\"text-sm text-indigo-800 dark:text-indigo-200\">\n                {t('goalsDescription', 'Document person-served goals, preferences, and discharge planning.')}\n              </p>\n            </div>\n\n            {/* Person-Served Goals */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('personServedGoals', 'Person-Served Goals')}\n              </h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('shortTermGoals', 'Short-Term Goals (1-3 months)')}\n                  </label>\n                  <textarea\n                    value={formData.goalsAndPreferences?.shortTermGoals || ''}\n                    onChange={(e) => handleInputChange('goalsAndPreferences', 'shortTermGoals', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('shortTermGoalsPlaceholder', 'List specific, measurable short-term goals...')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('longTermGoals', 'Long-Term Goals (6+ months)')}\n                  </label>\n                  <textarea\n                    value={formData.goalsAndPreferences?.longTermGoals || ''}\n                    onChange={(e) => handleInputChange('goalsAndPreferences', 'longTermGoals', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('longTermGoalsPlaceholder', 'List long-term functional and life goals...')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Preferences */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('preferences', 'Preferences and Choices')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('treatmentPreferences', 'Treatment Preferences')}\n                  </label>\n                  <textarea\n                    value={formData.goalsAndPreferences?.treatmentPreferences || ''}\n                    onChange={(e) => handleInputChange('goalsAndPreferences', 'treatmentPreferences', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('treatmentPreferencesPlaceholder', 'Preferred treatment approaches, times, settings...')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('communicationPreferences', 'Communication Preferences')}\n                  </label>\n                  <textarea\n                    value={formData.goalsAndPreferences?.communicationPreferences || ''}\n                    onChange={(e) => handleInputChange('goalsAndPreferences', 'communicationPreferences', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('communicationPreferencesPlaceholder', 'Preferred communication methods and languages...')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Discharge Planning */}\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('dischargePlanning', 'Discharge Planning')}\n              </h4>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('dischargeGoals', 'Discharge Goals')}\n                  </label>\n                  <textarea\n                    value={formData.goalsAndPreferences?.dischargeGoals || ''}\n                    onChange={(e) => handleInputChange('goalsAndPreferences', 'dischargeGoals', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('dischargeGoalsPlaceholder', 'Expected functional level and living situation at discharge...')}\n                  />\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('estimatedLength', 'Estimated Length of Stay')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.goalsAndPreferences?.estimatedLength || ''}\n                      onChange={(e) => handleInputChange('goalsAndPreferences', 'estimatedLength', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      placeholder={t('estimatedLengthPlaceholder', 'e.g., 6-8 weeks')}\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('dischargeDestination', 'Anticipated Discharge Destination')}\n                    </label>\n                    <select\n                      value={formData.goalsAndPreferences?.dischargeDestination || 'home'}\n                      onChange={(e) => handleInputChange('goalsAndPreferences', 'dischargeDestination', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"home\">{t('home', 'Home')}</option>\n                      <option value=\"assisted-living\">{t('assistedLiving', 'Assisted Living')}</option>\n                      <option value=\"skilled-nursing\">{t('skilledNursing', 'Skilled Nursing Facility')}</option>\n                      <option value=\"other-facility\">{t('otherFacility', 'Other Facility')}</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return <div>Section content for {section.title}</div>;\n    }\n  };\n\n  return (\n    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n              {t('carfAssessment', 'CARF-Compliant Assessment')}\n            </h2>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('carfAssessmentDescription', 'Comprehensive assessment following CARF standards')}\n            </p>\n          </div>\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={onCancel}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n            >\n              {t('cancel', 'Cancel')}\n            </button>\n            <button\n              onClick={() => onSave(formData)}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              {t('saveAssessment', 'Save Assessment')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Progress Steps */}\n      <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n        <div className=\"flex items-center justify-between\">\n          {sections.map((section, index) => (\n            <div key={section.id} className=\"flex items-center\">\n              <div\n                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n                  currentSection >= index\n                    ? 'border-blue-500 bg-blue-500 text-white'\n                    : 'border-gray-300 text-gray-400'\n                }`}\n              >\n                <i className={section.icon}></i>\n              </div>\n              {index < sections.length - 1 && (\n                <div\n                  className={`w-full h-1 mx-2 ${\n                    currentSection > index ? 'bg-blue-500' : 'bg-gray-300'\n                  }`}\n                ></div>\n              )}\n            </div>\n          ))}\n        </div>\n        <div className=\"mt-2\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {t('step', 'Step')} {currentSection + 1} {t('of', 'of')} {sections.length}: {sections[currentSection].title}\n          </p>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <div className=\"p-6\">\n        {renderSection()}\n      </div>\n\n      {/* Navigation */}\n      <div className=\"px-6 py-4 border-t border-gray-200 dark:border-gray-600 flex justify-between\">\n        <button\n          onClick={() => setCurrentSection(Math.max(0, currentSection - 1))}\n          disabled={currentSection === 0}\n          className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          <i className=\"fas fa-arrow-left mr-2\"></i>\n          {t('previous', 'Previous')}\n        </button>\n\n        {currentSection === sections.length - 1 ? (\n          <button\n            onClick={() => onSave(formData)}\n            className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n          >\n            <i className=\"fas fa-save mr-2\"></i>\n            {t('saveAssessment', 'Save Assessment')}\n          </button>\n        ) : (\n          <button\n            onClick={() => setCurrentSection(Math.min(sections.length - 1, currentSection + 1))}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            {t('next', 'Next')}\n            <i className=\"fas fa-arrow-right ml-2\"></i>\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CARFAssessmentForm;\n"], "names": ["CARF_COMPLIANCE_LEVELS", "FULL_COMPLIANCE", "score", "label", "color", "description", "SUBSTANTIAL_COMPLIANCE", "PARTIAL_COMPLIANCE", "NON_COMPLIANCE", "_ref", "patientId", "onSave", "onCancel", "initialData", "t", "isRTL", "useLanguage", "currentSection", "setCurrentSection", "useState", "formData", "setFormData", "assessmentDate", "Date", "toISOString", "split", "assessor", "assessorCredentials", "primaryDiagnosis", "secondaryDiagnoses", "medicalHistory", "currentMedications", "allergies", "precautions", "vitalSigns", "bloodPressure", "heartRate", "temperature", "oxygenSaturation", "functionalAssessment", "selfCare", "eating", "grooming", "bathing", "dressingUpper", "dressingLower", "toileting", "sphincterControl", "bladder", "bowel", "transfers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toilet", "tubShower", "locomotion", "walkWheelchair", "stairs", "communication", "comprehension", "expression", "socialCognition", "socialInteraction", "problemSolving", "memory", "psychosocialAssessment", "moodState", "copingStrategies", "motivationLevel", "insightLevel", "familySupport", "socialConnections", "culturalConsiderations", "spiritual<PERSON><PERSON>s", "notes", "mentalStatus", "cognitiveFunction", "emotionalStatus", "copingMechanisms", "socialSupport", "culturalFactors", "environmentalAssessment", "homeAccessibility", "homeSafety", "homeModifications", "transportationAccess", "communityResources", "livingArrangement", "accessibility", "safetyHazards", "transportation", "financialResources", "equipmentNeeds", "riskAssessment", "fallRisk", "skinIntegrityRisk", "nutritionalRisk", "medicationRisk", "behavioralRisk", "cognitiveRisk", "riskMitigationPlan", "goalsAndPreferences", "personServedGoals", "familyGoals", "clinicalGoals", "preferences", "barriers", "strengths", "dischargePlanning", "sections", "id", "title", "icon", "handleInputChange", "section", "field", "value", "prev", "_objectSpread", "handleNestedInputChange", "subsection", "FIMScoreInput", "_ref2", "onChange", "_jsxs", "className", "children", "_jsx", "e", "parseInt", "target", "RiskLevelSelect", "_ref3", "concat", "onClick", "map", "index", "length", "renderSection", "_formData$vitalSigns", "_formData$vitalSigns2", "_formData$vitalSigns3", "_formData$vitalSigns4", "_formData$psychosocia", "_formData$psychosocia2", "_formData$psychosocia3", "_formData$psychosocia4", "_formData$psychosocia5", "_formData$psychosocia6", "_formData$psychosocia7", "_formData$psychosocia8", "_formData$psychosocia9", "_formData$environment", "_formData$environment2", "_formData$environment3", "_formData$environment4", "_formData$environment5", "_formData$environment6", "_formData$goalsAndPre", "_formData$goalsAndPre2", "_formData$goalsAndPre3", "_formData$goalsAndPre4", "_formData$goalsAndPre5", "_formData$goalsAndPre6", "_formData$goalsAndPre7", "type", "required", "placeholder", "Array", "isArray", "join", "d", "trim", "filter", "rows", "shortTermGoals", "longTermGoals", "treatmentPreferences", "communicationPreferences", "dischargeGoals", "estimatedLength", "dischargeDestination", "Math", "max", "disabled", "min"], "sourceRoot": ""}