{"version": 3, "file": "static/js/6294.0bef723b.chunk.js", "mappings": "uNAKA,MAyhBA,EAzhBgCA,KAC9B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAEvCC,YAAa,GACbP,UAAWA,GAAa,GACxBQ,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,eAAmB,OAAJd,QAAI,IAAJA,OAAI,EAAJA,EAAMe,OAAQ,GAG7BC,aAAc,GACdC,gBAAiB,IACjBC,UAAW,QACXC,cAAe,GAGfC,UAAW,GAGXC,kBAAmB,GACnBC,kBAAmB,GACnBC,mBAAoB,GAGpBC,iBAAkB,GAClBC,WAAY,GACZC,qBAAsB,GAGtBC,oBAAqB,GACrBC,gBAAiB,GACjBC,iBAAkB,GAGlBC,iBAAkB,GAClBC,mBAAoB,GACpBC,iBAAkB,GAGlBC,mBAAoB,GACpBC,uBAAuB,EACvBC,yBAAyB,KAGpBC,EAASC,IAAc7B,EAAAA,EAAAA,WAAS,IAChC8B,EAAQC,IAAa/B,EAAAA,EAAAA,UAAS,CAAC,IAC/BgC,EAAiBC,IAAsBjC,EAAAA,EAAAA,UAAS,IAEjDkC,EAAmB,CACvB,CAAEC,MAAO,QAASC,MAAO/C,EAAE,QAAS,UACpC,CAAE8C,MAAO,cAAeC,MAAO/C,EAAE,aAAc,gBAC/C,CAAE8C,MAAO,kBAAmBC,MAAO/C,EAAE,gBAAiB,oBACtD,CAAE8C,MAAO,mBAAoBC,MAAO/C,EAAE,iBAAkB,qBACxD,CAAE8C,MAAO,SAAUC,MAAO/C,EAAE,SAAU,sBAQxCgD,EAAAA,EAAAA,WAAU,KACJ3C,GACF4C,IAEFC,KACC,CAAC7C,IAEJ,MAAM4C,EAAkBE,UACtB,IACEX,GAAW,GACX,MAAMY,QAAiBC,MAAM,iBAADC,OAAkBjD,IAC9C,GAAI+C,EAASG,GAAI,CACf,MAAMC,QAAoBJ,EAASK,OACnC/C,EAAYgD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP9C,YAAa4C,EAAYtC,MAAQ,GACjCb,UAAWmD,EAAYI,KAAOvD,IAElC,CACF,CAAE,MAAOwD,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACCrB,GAAW,EACb,GAGIU,EAAsBC,UAC1B,IACE,MAAMC,QAAiBC,MAAM,4BAC7B,GAAID,EAASG,GAAI,CACf,MAAMhC,QAAkB6B,EAASK,OACjCb,EAAmBrB,EACrB,CACF,CAAE,MAAOsC,GACPC,QAAQD,MAAM,kCAAmCA,GAEjDjB,EAAmB,CACjB,CACEmB,GAAI,EACJ7C,KAAM,cACN8C,SAAU,kBACVC,YAAa,2BACbC,aAAc,kFACdC,YAAa,QACbC,KAAM,MACN/C,UAAW,QACXgD,MAAO,qCAET,CACEN,GAAI,EACJ7C,KAAM,iBACN8C,SAAU,kBACVC,YAAa,sCACbC,aAAc,mGACdC,YAAa,KACbC,KAAM,IACN/C,UAAW,QACXgD,MAAO,yCAGb,GAGIC,EAAoBA,CAACC,EAAOzB,KAChCpC,EAAYgD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACa,GAAQzB,KAGPL,EAAO8B,IACT7B,EAAUgB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACa,GAAQ,SA6BTC,EAAiBA,CAACC,EAAYF,EAAOzB,KACzCpC,EAAYgD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPnC,UAAWmC,EAAKnC,UAAUmD,IAAIC,GAC5BA,EAAGZ,KAAOU,GAAUd,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQgB,GAAE,IAAE,CAACJ,GAAQzB,IAAU6B,OAkGzD,OAAIpC,GAEAqC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D9E,EAAE,sBAAuB,kCAE5B4E,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD9E,EAAE,iBAAkB,uDAGzB4E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,sGAAqGC,SAAA,EACnHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ7E,EAAE,kBAAmB,iCAQhC+E,EAAAA,EAAAA,MAAA,QAAMC,SAvEW7B,UAGnB,GAFA8B,EAAEC,iBAxDiBC,MACnB,MAAMC,EAAY,CAAC,EAQnB,OANK3E,EAASG,YAAYyE,SAAQD,EAAUxE,YAAcZ,EAAE,sBAAuB,6BAC9ES,EAASQ,cAAcoE,SAAQD,EAAUnE,cAAgBjB,EAAE,wBAAyB,+BACpFS,EAASU,aAAakE,SAAQD,EAAUjE,aAAenB,EAAE,uBAAwB,8BACpD,IAA9BS,EAASc,UAAU+D,SAAcF,EAAU7D,UAAYvB,EAAE,oBAAqB,sCAElF0C,EAAU0C,GAC+B,IAAlCG,OAAOC,KAAKJ,GAAWE,QAiDzBH,GAIL,IACE3C,GAAW,GAEX,MAAMiD,GAAc9B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACflD,GAAQ,IACXiF,YAAavF,EAAK4D,GAClB4B,aAAa,IAAI7E,MAAOC,gBAW1B,WARuBsC,MAAM,wCAAyC,CACpEuC,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAUP,MAGVlC,GAIX,MAAM,IAAI0C,MAAM,wCAHhBC,MAAMlG,EAAE,WAAY,8CACpBO,EAASF,EAAS,aAAAiD,OAAgBjD,GAAc,YAIpD,CAAE,MAAOwD,GACPC,QAAQD,MAAM,sCAAuCA,GACrDqC,MAAMlG,EAAE,cAAe,yDACzB,CAAC,QACCwC,GAAW,EACb,GAoCgCqC,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE9E,EAAE,qBAAsB,0BAG3B+E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,cAAe,gBAAgB,SAEpC4E,EAAAA,EAAAA,KAAA,SACEuB,KAAK,OACLrD,MAAOrC,EAASG,YAChBwF,SAAWnB,GAAMX,EAAkB,cAAeW,EAAEoB,OAAOvD,OAC3D+B,UAAS,8FAAAvB,OACPb,EAAO7B,YAAc,iBAAmB,wCAE1C0F,UAAQ,IAET7D,EAAO7B,cACNgE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAErC,EAAO7B,kBAIrDmE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,eAAgB,iBAAiB,SAEtC4E,EAAAA,EAAAA,KAAA,SACEuB,KAAK,OACLrD,MAAOrC,EAASU,aAChBiF,SAAWnB,GAAMX,EAAkB,eAAgBW,EAAEoB,OAAOvD,OAC5DyD,YAAavG,EAAE,0BAA2B,0CAC1C6E,UAAS,8FAAAvB,OACPb,EAAOtB,aAAe,iBAAmB,wCAE3CmF,UAAQ,IAET7D,EAAOtB,eACNyD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAErC,EAAOtB,mBAIrD4D,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E9E,EAAE,kBAAmB,+BAExB4E,EAAAA,EAAAA,KAAA,SACEuB,KAAK,SACLrD,MAAOrC,EAASW,gBAChBgF,SAAWnB,GAAMX,EAAkB,kBAAmBW,EAAEoB,OAAOvD,OAC/D0D,IAAI,IACJC,IAAI,KACJ5B,UAAU,wIAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E9E,EAAE,YAAa,gBAElB4E,EAAAA,EAAAA,KAAA,UACE9B,MAAOrC,EAASY,UAChB+E,SAAWnB,GAAMX,EAAkB,YAAaW,EAAEoB,OAAOvD,OACzD+B,UAAU,kIAAiIC,SAE1IjC,EAAiB6B,IAAKgC,IACrB9B,EAAAA,EAAAA,KAAA,UAA2B9B,MAAO4D,EAAO5D,MAAMgC,SAC5C4B,EAAO3D,OADG2D,EAAO5D,oBAU9BiC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE9E,EAAE,oBAAqB,yBAI1B+E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE9E,EAAE,kBAAmB,uBAExB4E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClEnC,EAAgB+B,IAAKiC,IACpB5B,EAAAA,EAAAA,MAAA,OAAuBF,UAAU,6DAA4DC,SAAA,EAC3FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SAAE6B,EAASzF,QACpE0D,EAAAA,EAAAA,KAAA,UACEuB,KAAK,SACLS,QAASA,IA/PRD,KACnB,MAAME,GAAWlD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZgD,GAAQ,IACX5C,GAAIjD,KAAKgG,MACTC,mBAAoB,GACpBC,oBAAqBL,EAASxC,YAC9B8C,aAAcN,EAASvC,KACvB8C,kBAAmBP,EAAStF,UAC5B8F,MAAO,KAGTzG,EAAYgD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPnC,UAAW,IAAImC,EAAKnC,UAAWsF,OAkPAO,CAAYT,GAC3B9B,UAAU,uDAAsDC,UAEhEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAGjBD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAE6B,EAAS1C,eACvEW,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,UACpCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iDAAgDC,SAAE6B,EAAS3C,eAbrE2C,EAAS5C,WAqBzBgB,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yDAAwDC,SAAA,CACnE9E,EAAE,oBAAqB,sBAAsB,KAAGS,EAASc,UAAU+D,OAAO,OAE5E7C,EAAOlB,YACNqD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAErC,EAAOlB,YAGpB,IAA9Bd,EAASc,UAAU+D,QAClBV,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D9E,EAAE,sBAAuB,4DAG5B4E,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBrE,EAASc,UAAUmD,IAAI,CAACiC,EAAUU,KACjCtC,EAAAA,EAAAA,MAAA,OAAuBF,UAAU,6DAA4DC,SAAA,EAC3FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4CAA2CC,SAAA,CACtDuC,EAAQ,EAAE,KAAGV,EAASzF,SAEzB0D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAE6B,EAAS1C,kBAEpEW,EAAAA,EAAAA,KAAA,UACEuB,KAAK,SACLS,QAASA,KAAMU,OAvRb7C,EAuR4BkC,EAAS5C,QAtR3DrD,EAAYgD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPnC,UAAWmC,EAAKnC,UAAUgG,OAAO5C,GAAMA,EAAGZ,KAAOU,MAH7BA,OAwRFI,UAAU,oDAAmDC,UAE7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAIjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E9E,EAAE,cAAe,kBAEpB4E,EAAAA,EAAAA,KAAA,SACEuB,KAAK,OACLrD,MAAO6D,EAASK,oBAChBZ,SAAWnB,GAAMT,EAAemC,EAAS5C,GAAI,sBAAuBkB,EAAEoB,OAAOvD,OAC7E+B,UAAU,wIAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E9E,EAAE,OAAQ,WAEb4E,EAAAA,EAAAA,KAAA,SACEuB,KAAK,OACLrD,MAAO6D,EAASM,aAChBb,SAAWnB,GAAMT,EAAemC,EAAS5C,GAAI,eAAgBkB,EAAEoB,OAAOvD,OACtE+B,UAAU,wIAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E9E,EAAE,YAAa,gBAElB4E,EAAAA,EAAAA,KAAA,SACEuB,KAAK,OACLrD,MAAO6D,EAASO,kBAChBd,SAAWnB,GAAMT,EAAemC,EAAS5C,GAAI,oBAAqBkB,EAAEoB,OAAOvD,OAC3E+B,UAAU,2IAKhBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E9E,EAAE,sBAAuB,2BAE5B4E,EAAAA,EAAAA,KAAA,YACE9B,MAAO6D,EAASI,mBAChBX,SAAWnB,GAAMT,EAAemC,EAAS5C,GAAI,qBAAsBkB,EAAEoB,OAAOvD,OAC5E0E,KAAK,IACL3C,UAAU,kIACV0B,YAAavG,EAAE,gCAAiC,kEAhE5C2G,EAAS5C,cA2E7BgB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,UACEuB,KAAK,SACLS,QAASA,IAAMrG,GAAU,GACzBsE,UAAU,4IAA2IC,SAEpJ9E,EAAE,SAAU,aAGf+E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEoB,KAAK,SACLS,QAxUQzD,UAClB,IACEX,GAAW,GAEX,MAAMiF,GAAO9D,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRlD,GAAQ,IACXiH,aAAa,IAAI5G,MAAOC,cACxB4G,YAAaxH,EAAKe,MAAQf,EAAKyH,MAC/BvH,UAAWA,IAGP+C,QAAiBC,MAAM,qCAAsC,CACjEuC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADvC,OAAYuE,aAAaC,QAAQ,WAElDhC,KAAMC,KAAKC,UAAUyB,KAGvB,IAAIrE,EAASG,GAaX,MAAM,IAAI0C,MAAM,uBAAD3C,OAAwBF,EAAS2E,SAbjC,CACf,MAAMC,QAAa5E,EAAS4E,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,yBAAAnF,OAA4B7C,EAASG,YAAY8H,QAAQ,OAAQ,KAAI,KAAApF,OAAI7C,EAASI,YAAW,QACvGyH,SAASxC,KAAK6C,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAASxC,KAAKgD,YAAYT,GAE1BnC,MAAMlG,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAO6D,GACPC,QAAQD,MAAM,wBAAyBA,GACvCqC,MAAMlG,EAAE,qBAAsB,2CAChC,CAAC,QACCwC,GAAW,EACb,GAiSUuG,SAAUxG,EACVsC,UAAU,gIAA+HC,SAAA,EAEzIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZtC,EAAUvC,EAAE,aAAc,iBAAmBA,EAAE,cAAe,oBAGjE4E,EAAAA,EAAAA,KAAA,UACEuB,KAAK,SACL4C,SAAUxG,EACVsC,UAAU,gHAA+GC,SAExHvC,EAAUvC,EAAE,SAAU,aAAeA,EAAE,UAAW,uC", "sources": ["pages/Forms/HomeExerciseProgramForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nconst HomeExerciseProgramForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [formData, setFormData] = useState({\n    // Patient Information\n    patientName: '',\n    patientId: patientId || '',\n    programDate: new Date().toISOString().split('T')[0],\n    therapistName: user?.name || '',\n    \n    // Program Details\n    programTitle: '',\n    programDuration: '4', // weeks\n    frequency: 'daily',\n    totalSessions: '',\n    \n    // Exercises\n    exercises: [],\n    \n    // Safety Instructions\n    safetyPrecautions: '',\n    contraindications: '',\n    warningSignsToStop: '',\n    \n    // Progress Tracking\n    progressMeasures: [],\n    reviewDate: '',\n    modificationCriteria: '',\n    \n    // Patient Education\n    patientInstructions: '',\n    equipmentNeeded: '',\n    environmentSetup: '',\n    \n    // Follow-up\n    followUpSchedule: '',\n    contactInformation: '',\n    emergencyContact: '',\n    \n    // Signatures\n    therapistSignature: '',\n    patientAcknowledgment: false,\n    caregiverAcknowledgment: false\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [exerciseLibrary, setExerciseLibrary] = useState([]);\n\n  const frequencyOptions = [\n    { value: 'daily', label: t('daily', 'Daily') },\n    { value: 'twice-daily', label: t('twiceDaily', 'Twice Daily') },\n    { value: 'every-other-day', label: t('everyOtherDay', 'Every Other Day') },\n    { value: 'three-times-week', label: t('threeTimesWeek', '3 Times per Week') },\n    { value: 'custom', label: t('custom', 'Custom Schedule') }\n  ];\n\n  const exerciseCategories = [\n    'Strengthening', 'Stretching', 'Range of Motion', 'Balance', \n    'Coordination', 'Endurance', 'Functional', 'Breathing'\n  ];\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n    loadExerciseLibrary();\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        setFormData(prev => ({\n          ...prev,\n          patientName: patientData.name || '',\n          patientId: patientData._id || patientId\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadExerciseLibrary = async () => {\n    try {\n      const response = await fetch('/api/v1/exercise-library');\n      if (response.ok) {\n        const exercises = await response.json();\n        setExerciseLibrary(exercises);\n      }\n    } catch (error) {\n      console.error('Error loading exercise library:', error);\n      // Fallback to default exercises\n      setExerciseLibrary([\n        {\n          id: 1,\n          name: 'Ankle Pumps',\n          category: 'Range of Motion',\n          description: 'Point and flex your foot',\n          instructions: 'Sit or lie down. Point your toes away from you, then flex them back toward you.',\n          repetitions: '10-15',\n          sets: '2-3',\n          frequency: 'Daily',\n          image: '/images/exercises/ankle-pumps.jpg'\n        },\n        {\n          id: 2,\n          name: 'Shoulder Rolls',\n          category: 'Range of Motion',\n          description: 'Roll shoulders forward and backward',\n          instructions: 'Sit or stand with arms at your sides. Roll shoulders forward in a circular motion, then reverse.',\n          repetitions: '10',\n          sets: '2',\n          frequency: 'Daily',\n          image: '/images/exercises/shoulder-rolls.jpg'\n        }\n      ]);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const addExercise = (exercise) => {\n    const newExercise = {\n      ...exercise,\n      id: Date.now(),\n      customInstructions: '',\n      modifiedRepetitions: exercise.repetitions,\n      modifiedSets: exercise.sets,\n      modifiedFrequency: exercise.frequency,\n      notes: ''\n    };\n\n    setFormData(prev => ({\n      ...prev,\n      exercises: [...prev.exercises, newExercise]\n    }));\n  };\n\n  const removeExercise = (exerciseId) => {\n    setFormData(prev => ({\n      ...prev,\n      exercises: prev.exercises.filter(ex => ex.id !== exerciseId)\n    }));\n  };\n\n  const updateExercise = (exerciseId, field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      exercises: prev.exercises.map(ex => \n        ex.id === exerciseId ? { ...ex, [field]: value } : ex\n      )\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.patientName.trim()) newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    if (!formData.therapistName.trim()) newErrors.therapistName = t('therapistNameRequired', 'Therapist name is required');\n    if (!formData.programTitle.trim()) newErrors.programTitle = t('programTitleRequired', 'Program title is required');\n    if (formData.exercises.length === 0) newErrors.exercises = t('exercisesRequired', 'At least one exercise is required');\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n\n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/home-exercise-programs/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `home-exercise-program-${formData.patientName.replace(/\\s+/g, '-')}-${formData.programDate}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n\n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      const submissionData = {\n        ...formData,\n        submittedBy: user.id,\n        submittedAt: new Date().toISOString()\n      };\n\n      const response = await fetch('/api/v1/home-exercise-programs/public', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        alert(t('hepSaved', 'Home Exercise Program saved successfully!'));\n        navigate(patientId ? `/patients/${patientId}` : '/patients');\n      } else {\n        throw new Error('Failed to save home exercise program');\n      }\n    } catch (error) {\n      console.error('Error saving home exercise program:', error);\n      alert(t('errorSaving', 'Error saving home exercise program. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('homeExerciseProgram', 'Home Exercise Program (HEP)')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                {t('hepDescription', 'Customized exercise program for home practice')}\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm rounded-full\">\n                <i className=\"fas fa-dumbbell mr-1\"></i>\n                {t('exerciseProgram', 'Exercise Program')}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Program Information */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {t('programInformation', 'Program Information')}\n          </h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('patientName', 'Patient Name')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.patientName}\n                onChange={(e) => handleInputChange('patientName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.patientName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.patientName && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('programTitle', 'Program Title')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.programTitle}\n                onChange={(e) => handleInputChange('programTitle', e.target.value)}\n                placeholder={t('programTitlePlaceholder', 'e.g., Lower Back Strengthening Program')}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.programTitle ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.programTitle && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.programTitle}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('programDuration', 'Program Duration (weeks)')}\n              </label>\n              <input\n                type=\"number\"\n                value={formData.programDuration}\n                onChange={(e) => handleInputChange('programDuration', e.target.value)}\n                min=\"1\"\n                max=\"52\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('frequency', 'Frequency')}\n              </label>\n              <select\n                value={formData.frequency}\n                onChange={(e) => handleInputChange('frequency', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {frequencyOptions.map((option) => (\n                  <option key={option.value} value={option.value}>\n                    {option.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Exercise Selection */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {t('exerciseSelection', 'Exercise Selection')}\n          </h2>\n          \n          {/* Exercise Library */}\n          <div className=\"mb-6\">\n            <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n              {t('exerciseLibrary', 'Exercise Library')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {exerciseLibrary.map((exercise) => (\n                <div key={exercise.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">{exercise.name}</h4>\n                    <button\n                      type=\"button\"\n                      onClick={() => addExercise(exercise)}\n                      className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400\"\n                    >\n                      <i className=\"fas fa-plus\"></i>\n                    </button>\n                  </div>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">{exercise.description}</p>\n                  <div className=\"text-xs text-gray-500\">\n                    <span className=\"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded\">{exercise.category}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Selected Exercises */}\n          <div>\n            <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n              {t('selectedExercises', 'Selected Exercises')} ({formData.exercises.length})\n            </h3>\n            {errors.exercises && (\n              <p className=\"text-red-500 text-sm mb-4\">{errors.exercises}</p>\n            )}\n            \n            {formData.exercises.length === 0 ? (\n              <p className=\"text-gray-500 dark:text-gray-400 text-center py-8\">\n                {t('noExercisesSelected', 'No exercises selected. Choose from the library above.')}\n              </p>\n            ) : (\n              <div className=\"space-y-4\">\n                {formData.exercises.map((exercise, index) => (\n                  <div key={exercise.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-4\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                          {index + 1}. {exercise.name}\n                        </h4>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">{exercise.description}</p>\n                      </div>\n                      <button\n                        type=\"button\"\n                        onClick={() => removeExercise(exercise.id)}\n                        className=\"text-red-600 hover:text-red-800 dark:text-red-400\"\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                    \n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('repetitions', 'Repetitions')}\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={exercise.modifiedRepetitions}\n                          onChange={(e) => updateExercise(exercise.id, 'modifiedRepetitions', e.target.value)}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('sets', 'Sets')}\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={exercise.modifiedSets}\n                          onChange={(e) => updateExercise(exercise.id, 'modifiedSets', e.target.value)}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        />\n                      </div>\n                      \n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('frequency', 'Frequency')}\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={exercise.modifiedFrequency}\n                          onChange={(e) => updateExercise(exercise.id, 'modifiedFrequency', e.target.value)}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        />\n                      </div>\n                    </div>\n                    \n                    <div className=\"mt-4\">\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        {t('specialInstructions', 'Special Instructions')}\n                      </label>\n                      <textarea\n                        value={exercise.customInstructions}\n                        onChange={(e) => updateExercise(exercise.id, 'customInstructions', e.target.value)}\n                        rows=\"2\"\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        placeholder={t('customInstructionsPlaceholder', 'Any modifications or special notes for this exercise...')}\n                      />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between\">\n          <button\n            type=\"button\"\n            onClick={() => navigate(-1)}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n\n          <div className=\"flex space-x-3\">\n            <button\n              type=\"button\"\n              onClick={generatePDF}\n              disabled={loading}\n              className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n            >\n              <i className=\"fas fa-file-pdf mr-2\"></i>\n              {loading ? t('generating', 'Generating...') : t('generatePDF', 'Generate PDF')}\n            </button>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? t('saving', 'Saving...') : t('saveHEP', 'Save Exercise Program')}\n            </button>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default HomeExerciseProgramForm;\n"], "names": ["HomeExerciseProgramForm", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "formData", "setFormData", "useState", "patientName", "programDate", "Date", "toISOString", "split", "<PERSON><PERSON><PERSON>", "name", "programTitle", "programDuration", "frequency", "totalSessions", "exercises", "safetyPrecautions", "contraindications", "warningSignsToStop", "progressMeasures", "reviewDate", "modificationCriteria", "patientInstructions", "equipmentNeeded", "environmentSetup", "followUpSchedule", "contactInformation", "emergencyContact", "therapistSignature", "patientAcknowledgment", "caregiverAcknowledgment", "loading", "setLoading", "errors", "setErrors", "exerciseLibrary", "setExerciseLibrary", "frequencyOptions", "value", "label", "useEffect", "loadPatientData", "loadExerciseLibrary", "async", "response", "fetch", "concat", "ok", "patientData", "json", "prev", "_objectSpread", "_id", "error", "console", "id", "category", "description", "instructions", "repetitions", "sets", "image", "handleInputChange", "field", "updateExercise", "exerciseId", "map", "ex", "_jsx", "className", "children", "_jsxs", "onSubmit", "e", "preventDefault", "validateForm", "newErrors", "trim", "length", "Object", "keys", "submissionData", "submittedBy", "submittedAt", "method", "headers", "body", "JSON", "stringify", "Error", "alert", "type", "onChange", "target", "required", "placeholder", "min", "max", "option", "exercise", "onClick", "newExercise", "now", "customInstructions", "modifiedRepetitions", "modifiedSets", "modifiedFrequency", "notes", "addExercise", "index", "removeExercise", "filter", "rows", "pdfData", "generatedAt", "generatedBy", "email", "localStorage", "getItem", "status", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "disabled"], "sourceRoot": ""}