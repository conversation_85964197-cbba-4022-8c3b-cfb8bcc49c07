"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[54],{54:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var s=a(5043),n=a(3216),r=a(5475),i=a(7921),l=a(2287),o=a(579);const d=()=>{var e;const{t:t,isRTL:a}=(0,i.o)(),d=(0,n.Zp)(),c=(0,n.zy)(),[g]=(0,r.ok)(),[m,p]=(0,s.useState)(null),[x,f]=(0,s.useState)(null);(0,s.useEffect)(()=>{var e,t;if(null!==(e=c.state)&&void 0!==e&&e.patient&&null!==(t=c.state)&&void 0!==t&&t.fromPatientProfile){const e=c.state.patient;f(e),p({patientId:e._id||e.id,patientName:e.name||"".concat(e.firstName," ").concat(e.lastName),age:e.age||"",gender:e.gender||"",assessmentDate:(new Date).toISOString().split("T")[0],assessor:"Current User",facility:"PT Clinic"})}else{const e=g.get("patientId"),t=g.get("patientName"),a=g.get("age"),s=g.get("gender");e&&t&&p({patientId:e,patientName:decodeURIComponent(t),age:a?parseInt(a):"",gender:s||"",assessmentDate:(new Date).toISOString().split("T")[0],assessor:"Current User",facility:"PT Clinic"})}},[g,c.state]);const u=()=>{d("/patients/".concat((null===m||void 0===m?void 0:m.patientId)||""))};return(0,o.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,o.jsx)("div",{className:"mb-8",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)("button",{onClick:u,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,o.jsx)("i",{className:"fas fa-arrow-".concat(a?"right":"left"," text-gray-600 dark:text-gray-400")})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:t("ptAssessment","PT Assessment")}),(null===m||void 0===m?void 0:m.patientName)&&(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:[t("patient","Patient"),": ",m.patientName]})]})]}),(0,o.jsx)("div",{className:"flex space-x-3",children:(0,o.jsxs)("button",{onClick:u,className:"px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-times mr-2"}),t("cancel","Cancel")]})})]})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,o.jsx)(l.A,{patientData:x,fromPatientProfile:null===(e=c.state)||void 0===e?void 0:e.fromPatientProfile,initialData:m,onSubmit:e=>{console.log("PT Assessment submitted:",e),d("/patients/".concat((null===m||void 0===m?void 0:m.patientId)||""))},onCancel:u})})]})}}}]);
//# sourceMappingURL=54.575416f4.chunk.js.map