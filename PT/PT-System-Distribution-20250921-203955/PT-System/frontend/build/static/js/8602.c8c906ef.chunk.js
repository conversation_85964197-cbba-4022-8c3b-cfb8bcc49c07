"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8602],{8602:(t,e,a)=>{a.r(e),a.d(e,{default:()=>s});a(5043);var n=a(3216),r=a(1145),d=a(579);const s=()=>{var t,e,a;const{patientId:s,treatmentId:i,progressId:o}=(0,n.g)(),l=(0,n.zy)();return(0,d.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,d.jsx)(r.A,{patientId:s,treatmentId:i,progressId:o,patientData:null===(t=l.state)||void 0===t?void 0:t.patient,fromPatientProfile:null===(e=l.state)||void 0===e?void 0:e.fromPatientProfile,defaultDate:null===(a=l.state)||void 0===a?void 0:a.defaultDate})})}}}]);
//# sourceMappingURL=8602.c8c906ef.chunk.js.map