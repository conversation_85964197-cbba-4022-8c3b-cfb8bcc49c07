"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3191],{3191:(e,a,t)=>{t.r(a),t.d(a,{default:()=>x});var r=t(5043),s=t(4117),l=t(7921),c=t(4528),n=t(3768),i=t(2555),o=t(579);const d=e=>{let{onClose:a,onSave:t}=e;const{t:c}=(0,s.Bd)(),{language:d}=(0,l.o)(),[x,g]=(0,r.useState)(1),[m,h]=(0,r.useState)({name:"",nameArabic:"",description:"",descriptionArabic:"",type:"standard",category:"general",config:{header:{showLogo:!0,showCompanyInfo:!0,showInvoiceTitle:!0,customTitle:"",customTitleArabic:""},patientInfo:{showPatientDetails:!0,showNationalId:!0,showInsuranceInfo:!1,showVisaInfo:!1,customFields:[]},services:{showServiceCode:!0,showDescription:!0,showQuantity:!0,showUnitPrice:!0,showDiscount:!1,showTax:!0,allowCustomServices:!0,predefinedServices:[]},pricing:{currency:"SAR",showSubtotal:!0,showTax:!0,taxRate:15,showDiscount:!1,showTotal:!0,roundingMethod:"round"},footer:{showPaymentTerms:!0,showBankDetails:!1,showNotes:!0,showSignature:!1,customText:"",customTextArabic:""},compliance:{zatcaCompliant:!0,nphiesCompliant:!1,showQRCode:!0,requireElectronicSignature:!1}},styling:{colorScheme:{primary:"#2563eb",secondary:"#64748b",accent:"#10b981"},fonts:{primary:"Inter",arabic:"Noto Sans Arabic"},layout:{orientation:"portrait",pageSize:"A4",margins:{top:20,bottom:20,left:20,right:20}}}}),u=[{id:1,title:"Basic Info",icon:"fas fa-info-circle"},{id:2,title:"Header Config",icon:"fas fa-heading"},{id:3,title:"Patient Info",icon:"fas fa-user"},{id:4,title:"Services",icon:"fas fa-list"},{id:5,title:"Pricing",icon:"fas fa-dollar-sign"},{id:6,title:"Footer & Compliance",icon:"fas fa-check-circle"},{id:7,title:"Styling",icon:"fas fa-palette"}],p=(e,a,t)=>{h(e?r=>(0,i.A)((0,i.A)({},r),{},{[e]:(0,i.A)((0,i.A)({},r[e]),{},{[a]:t})}):e=>(0,i.A)((0,i.A)({},e),{},{[a]:t}))},b=(e,a,t,r)=>{h(s=>(0,i.A)((0,i.A)({},s),{},{config:(0,i.A)((0,i.A)({},s.config),{},{[e]:(0,i.A)((0,i.A)({},s.config[e]),{},{[a||t]:a?(0,i.A)((0,i.A)({},s.config[e][a]),{},{[t]:r}):r})})}))},y=()=>{const e={label:"",labelArabic:"",fieldType:"text",required:!1};h(a=>(0,i.A)((0,i.A)({},a),{},{config:(0,i.A)((0,i.A)({},a.config),{},{patientInfo:(0,i.A)((0,i.A)({},a.config.patientInfo),{},{customFields:[...a.config.patientInfo.customFields,e]})})}))},f=()=>{const e={code:"",name:"",nameArabic:"",description:"",descriptionArabic:"",defaultPrice:0,taxable:!0};h(a=>(0,i.A)((0,i.A)({},a),{},{config:(0,i.A)((0,i.A)({},a.config),{},{services:(0,i.A)((0,i.A)({},a.config.services),{},{predefinedServices:[...a.config.services.predefinedServices,e]})})}))};return(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:c("createNewTemplate","Create New Template")}),(0,o.jsx)("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,o.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,o.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,o.jsx)("div",{className:"flex items-center justify-between",children:u.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ".concat(x>=e.id?"bg-blue-600 text-white":"bg-gray-200 text-gray-600 dark:bg-gray-600 dark:text-gray-400"),children:(0,o.jsx)("i",{className:e.icon})}),(0,o.jsx)("span",{className:"ml-2 text-sm ".concat(x>=e.id?"text-blue-600 dark:text-blue-400":"text-gray-600 dark:text-gray-400"),children:e.title}),a<u.length-1&&(0,o.jsx)("div",{className:"w-8 h-0.5 mx-4 ".concat(x>e.id?"bg-blue-600":"bg-gray-200 dark:bg-gray-600")})]},e.id))})}),(0,o.jsx)("div",{className:"p-6 overflow-y-auto max-h-96",children:(()=>{switch(x){case 1:return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c("basicInformation","Basic Information")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[c("templateName","Template Name")," *"]}),(0,o.jsx)("input",{type:"text",value:m.name,onChange:e=>p(null,"name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Enter template name"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("templateNameArabic","Template Name (Arabic)")}),(0,o.jsx)("input",{type:"text",value:m.nameArabic,onChange:e=>p(null,"nameArabic",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"\u0623\u062f\u062e\u0644 \u0627\u0633\u0645 \u0627\u0644\u0642\u0627\u0644\u0628",dir:"rtl"})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[c("description","Description")," *"]}),(0,o.jsx)("textarea",{value:m.description,onChange:e=>p(null,"description",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Enter template description"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("descriptionArabic","Description (Arabic)")}),(0,o.jsx)("textarea",{value:m.descriptionArabic,onChange:e=>p(null,"descriptionArabic",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"\u0623\u062f\u062e\u0644 \u0648\u0635\u0641 \u0627\u0644\u0642\u0627\u0644\u0628",dir:"rtl"})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("templateType","Template Type")}),(0,o.jsxs)("select",{value:m.type,onChange:e=>p(null,"type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"standard",children:c("standard","Standard")}),(0,o.jsx)("option",{value:"medical",children:c("medical","Medical")}),(0,o.jsx)("option",{value:"therapy",children:c("therapy","Therapy")}),(0,o.jsx)("option",{value:"insurance",children:c("insurance","Insurance")}),(0,o.jsx)("option",{value:"government",children:c("government","Government")}),(0,o.jsx)("option",{value:"custom",children:c("custom","Custom")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("category","Category")}),(0,o.jsxs)("select",{value:m.category,onChange:e=>p(null,"category",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"general",children:c("general","General")}),(0,o.jsx)("option",{value:"physical_therapy",children:c("physicalTherapy","Physical Therapy")}),(0,o.jsx)("option",{value:"occupational_therapy",children:c("occupationalTherapy","Occupational Therapy")}),(0,o.jsx)("option",{value:"speech_therapy",children:c("speechTherapy","Speech Therapy")}),(0,o.jsx)("option",{value:"medical_consultation",children:c("medicalConsultation","Medical Consultation")}),(0,o.jsx)("option",{value:"equipment_rental",children:c("equipmentRental","Equipment Rental")})]})]})]})]});case 2:return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c("headerConfiguration","Header Configuration")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.header.showLogo,onChange:e=>b("header",null,"showLogo",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showLogo","Show Logo")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.header.showCompanyInfo,onChange:e=>b("header",null,"showCompanyInfo",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showCompanyInfo","Show Company Information")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.header.showInvoiceTitle,onChange:e=>b("header",null,"showInvoiceTitle",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showInvoiceTitle","Show Invoice Title")})]})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("customTitle","Custom Title")}),(0,o.jsx)("input",{type:"text",value:m.config.header.customTitle,onChange:e=>b("header",null,"customTitle",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Invoice"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("customTitleArabic","Custom Title (Arabic)")}),(0,o.jsx)("input",{type:"text",value:m.config.header.customTitleArabic,onChange:e=>b("header",null,"customTitleArabic",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"\u0641\u0627\u062a\u0648\u0631\u0629",dir:"rtl"})]})]})]})]});case 3:return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c("patientInformation","Patient Information")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.patientInfo.showPatientDetails,onChange:e=>b("patientInfo",null,"showPatientDetails",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showPatientDetails","Show Patient Details")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.patientInfo.showNationalId,onChange:e=>b("patientInfo",null,"showNationalId",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showNationalId","Show National ID")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.patientInfo.showInsuranceInfo,onChange:e=>b("patientInfo",null,"showInsuranceInfo",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showInsuranceInfo","Show Insurance Information")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.patientInfo.showVisaInfo,onChange:e=>b("patientInfo",null,"showVisaInfo",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showVisaInfo","Show Visa Information")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-2",children:c("customFields","Custom Fields")}),(0,o.jsx)("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:m.config.patientInfo.customFields.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-700 rounded",children:[(0,o.jsx)("input",{type:"text",value:e.label,onChange:e=>{const t=[...m.config.patientInfo.customFields];t[a].label=e.target.value,b("patientInfo",null,"customFields",t)},placeholder:"Field label",className:"flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white"}),(0,o.jsx)("button",{onClick:()=>(e=>{h(a=>(0,i.A)((0,i.A)({},a),{},{config:(0,i.A)((0,i.A)({},a.config),{},{patientInfo:(0,i.A)((0,i.A)({},a.config.patientInfo),{},{customFields:a.config.patientInfo.customFields.filter((a,t)=>t!==e)})})}))})(a),className:"text-red-600 hover:text-red-800",children:(0,o.jsx)("i",{className:"fas fa-trash text-sm"})})]},a))}),(0,o.jsxs)("button",{onClick:y,className:"mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-1"}),c("addField","Add Field")]})]})]})]});case 4:return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c("servicesConfiguration","Services Configuration")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.services.showServiceCode,onChange:e=>b("services",null,"showServiceCode",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showServiceCode","Show Service Code")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.services.showDescription,onChange:e=>b("services",null,"showDescription",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showDescription","Show Description")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.services.showQuantity,onChange:e=>b("services",null,"showQuantity",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showQuantity","Show Quantity")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.services.showUnitPrice,onChange:e=>b("services",null,"showUnitPrice",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showUnitPrice","Show Unit Price")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.services.showDiscount,onChange:e=>b("services",null,"showDiscount",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showDiscount","Show Discount")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.services.allowCustomServices,onChange:e=>b("services",null,"allowCustomServices",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("allowCustomServices","Allow Custom Services")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-2",children:c("predefinedServices","Predefined Services")}),(0,o.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:m.config.services.predefinedServices.map((e,a)=>(0,o.jsxs)("div",{className:"p-2 bg-gray-50 dark:bg-gray-700 rounded",children:[(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-2",children:[(0,o.jsx)("input",{type:"text",value:e.code,onChange:e=>{const t=[...m.config.services.predefinedServices];t[a].code=e.target.value,b("services",null,"predefinedServices",t)},placeholder:"Service code",className:"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white"}),(0,o.jsx)("input",{type:"text",value:e.name,onChange:e=>{const t=[...m.config.services.predefinedServices];t[a].name=e.target.value,b("services",null,"predefinedServices",t)},placeholder:"Service name",className:"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white"})]}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("input",{type:"number",value:e.defaultPrice,onChange:e=>{const t=[...m.config.services.predefinedServices];t[a].defaultPrice=parseFloat(e.target.value)||0,b("services",null,"predefinedServices",t)},placeholder:"Price",className:"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white w-20"}),(0,o.jsx)("button",{onClick:()=>(e=>{h(a=>(0,i.A)((0,i.A)({},a),{},{config:(0,i.A)((0,i.A)({},a.config),{},{services:(0,i.A)((0,i.A)({},a.config.services),{},{predefinedServices:a.config.services.predefinedServices.filter((a,t)=>t!==e)})})}))})(a),className:"text-red-600 hover:text-red-800",children:(0,o.jsx)("i",{className:"fas fa-trash text-sm"})})]})]},a))}),(0,o.jsxs)("button",{onClick:f,className:"mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-1"}),c("addService","Add Service")]})]})]})]});case 5:return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c("pricingConfiguration","Pricing Configuration")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("currency","Currency")}),(0,o.jsxs)("select",{value:m.config.pricing.currency,onChange:e=>b("pricing",null,"currency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"SAR",children:"SAR - Saudi Riyal"}),(0,o.jsx)("option",{value:"USD",children:"USD - US Dollar"}),(0,o.jsx)("option",{value:"EUR",children:"EUR - Euro"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[c("taxRate","Tax Rate")," (%)"]}),(0,o.jsx)("input",{type:"number",value:m.config.pricing.taxRate,onChange:e=>b("pricing",null,"taxRate",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",min:"0",max:"100",step:"0.1"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("roundingMethod","Rounding Method")}),(0,o.jsxs)("select",{value:m.config.pricing.roundingMethod,onChange:e=>b("pricing",null,"roundingMethod",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"round",children:c("round","Round")}),(0,o.jsx)("option",{value:"floor",children:c("floor","Floor")}),(0,o.jsx)("option",{value:"ceil",children:c("ceil","Ceiling")})]})]})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.pricing.showSubtotal,onChange:e=>b("pricing",null,"showSubtotal",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showSubtotal","Show Subtotal")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.pricing.showTax,onChange:e=>b("pricing",null,"showTax",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showTax","Show Tax")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.pricing.showDiscount,onChange:e=>b("pricing",null,"showDiscount",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showDiscount","Show Discount")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.pricing.showTotal,onChange:e=>b("pricing",null,"showTotal",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showTotal","Show Total")})]})]})]})]});case 6:return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c("footerAndCompliance","Footer & Compliance")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white",children:c("footerOptions","Footer Options")}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.footer.showPaymentTerms,onChange:e=>b("footer",null,"showPaymentTerms",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showPaymentTerms","Show Payment Terms")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.footer.showBankDetails,onChange:e=>b("footer",null,"showBankDetails",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showBankDetails","Show Bank Details")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.footer.showNotes,onChange:e=>b("footer",null,"showNotes",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showNotes","Show Notes")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.footer.showSignature,onChange:e=>b("footer",null,"showSignature",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showSignature","Show Signature")})]})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white",children:c("complianceOptions","Compliance Options")}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.compliance.zatcaCompliant,onChange:e=>b("compliance",null,"zatcaCompliant",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("zatcaCompliant","ZATCA Compliant")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.compliance.nphiesCompliant,onChange:e=>b("compliance",null,"nphiesCompliant",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("nphiesCompliant","NPHIES Compliant")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.compliance.showQRCode,onChange:e=>b("compliance",null,"showQRCode",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("showQRCode","Show QR Code")})]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:m.config.compliance.requireElectronicSignature,onChange:e=>b("compliance",null,"requireElectronicSignature",e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("requireElectronicSignature","Require Electronic Signature")})]})]})]})]});case 7:return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:c("styling","Styling")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white",children:c("colorScheme","Color Scheme")}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("primaryColor","Primary Color")}),(0,o.jsx)("input",{type:"color",value:m.styling.colorScheme.primary,onChange:e=>b("styling","colorScheme","primary",e.target.value),className:"w-full h-10 border border-gray-300 dark:border-gray-600 rounded"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("secondaryColor","Secondary Color")}),(0,o.jsx)("input",{type:"color",value:m.styling.colorScheme.secondary,onChange:e=>b("styling","colorScheme","secondary",e.target.value),className:"w-full h-10 border border-gray-300 dark:border-gray-600 rounded"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("accentColor","Accent Color")}),(0,o.jsx)("input",{type:"color",value:m.styling.colorScheme.accent,onChange:e=>b("styling","colorScheme","accent",e.target.value),className:"w-full h-10 border border-gray-300 dark:border-gray-600 rounded"})]})]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white",children:c("layout","Layout")}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("orientation","Orientation")}),(0,o.jsxs)("select",{value:m.styling.layout.orientation,onChange:e=>b("styling","layout","orientation",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"portrait",children:c("portrait","Portrait")}),(0,o.jsx)("option",{value:"landscape",children:c("landscape","Landscape")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("pageSize","Page Size")}),(0,o.jsxs)("select",{value:m.styling.layout.pageSize,onChange:e=>b("styling","layout","pageSize",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"A4",children:"A4"}),(0,o.jsx)("option",{value:"Letter",children:"Letter"}),(0,o.jsx)("option",{value:"Legal",children:"Legal"})]})]})]})]})]});default:return(0,o.jsx)("div",{className:"text-center py-8",children:(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:["Step ",x," configuration will be implemented here."]})})}})()}),(0,o.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-600",children:[(0,o.jsxs)("button",{onClick:()=>g(Math.max(1,x-1)),disabled:1===x,className:"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,o.jsx)("i",{className:"fas fa-arrow-left mr-2"}),c("previous","Previous")]}),(0,o.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[x," of ",u.length]}),(0,o.jsx)("div",{className:"flex space-x-3",children:x<u.length?(0,o.jsxs)("button",{onClick:()=>g(Math.min(u.length,x+1)),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:[c("next","Next"),(0,o.jsx)("i",{className:"fas fa-arrow-right ml-2"})]}):(0,o.jsxs)("button",{onClick:async()=>{try{if(!m.name.trim())return void n.Ay.error("Template name is required");if(!m.description.trim())return void n.Ay.error("Template description is required");await t(m),n.Ay.success("Template created successfully!"),a()}catch(e){console.error("Error saving template:",e),n.Ay.error("Failed to save template")}},className:"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-save mr-2"}),c("createTemplate","Create Template")]})})]})]})})},x=()=>{const[e,a]=(0,r.useState)([]),[t,i]=(0,r.useState)(!0),[x,g]=(0,r.useState)(null),[m,h]=(0,r.useState)(!1),[u,p]=(0,r.useState)("all"),{t:b}=(0,s.Bd)(),{language:y}=(0,l.o)(),{user:f}=(0,c.A)();(0,r.useEffect)(()=>{j()},[u]);const j=async()=>{i(!0);try{const e=new URLSearchParams;"all"!==u&&e.append("type",u);const t=await fetch("/api/v1/invoice-templates?".concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to load templates");{const e=await t.json();a(e.data)}}catch(e){console.error("Error loading templates:",e),n.Ay.error("Failed to load invoice templates")}finally{i(!1)}},k=e=>{switch(e){case"standard":return"fas fa-file-invoice";case"medical":return"fas fa-user-md";case"therapy":return"fas fa-dumbbell";case"insurance":return"fas fa-shield-alt";case"government":return"fas fa-landmark";default:return"fas fa-file"}},v=e=>{switch(e){case"standard":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"medical":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";case"therapy":return"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400";case"insurance":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"government":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}};return t?(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,o.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice mr-3 text-blue-600"}),b("invoiceTemplates","Invoice Templates")]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:b("invoiceTemplatesDesc","Create and manage invoice templates for different types of services")})]}),(0,o.jsxs)("button",{onClick:()=>h(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),b("createTemplate","Create Template")]})]}),(0,o.jsx)("div",{className:"mt-6 border-b border-gray-200 dark:border-gray-600",children:(0,o.jsx)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:[{key:"all",label:"All Templates",icon:"fas fa-th-large"},{key:"standard",label:"Standard",icon:"fas fa-file-invoice"},{key:"medical",label:"Medical",icon:"fas fa-user-md"},{key:"therapy",label:"Therapy",icon:"fas fa-dumbbell"},{key:"insurance",label:"Insurance",icon:"fas fa-shield-alt"}].map(e=>(0,o.jsxs)("button",{onClick:()=>p(e.key),className:"py-4 px-1 border-b-2 font-medium text-sm flex items-center ".concat(u===e.key?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"".concat(e.icon," mr-2")}),b(e.label.toLowerCase().replace(" ",""),e.label)]},e.key))})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.map(e=>(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-shadow",children:[(0,o.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("div",{className:"flex items-start justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 rounded-lg ".concat(v(e.type)," mr-4"),children:(0,o.jsx)("i",{className:"".concat(k(e.type)," text-xl")})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"ar"===y&&e.nameArabic?e.nameArabic:e.name}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"ar"===y&&e.descriptionArabic?e.descriptionArabic:e.description})]})]}),e.isDefault&&(0,o.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 text-xs font-medium rounded",children:b("default","Default")})]})}),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[b("type","Type"),":"]}),(0,o.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(v(e.type)),children:b(e.type,e.type)})]}),(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[b("category","Category"),":"]}),(0,o.jsx)("span",{className:"text-gray-900 dark:text-white font-medium",children:b(e.category,e.category)})]}),(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[b("usageCount","Used"),":"]}),(0,o.jsxs)("span",{className:"text-gray-900 dark:text-white font-medium",children:[e.usageCount," ",b("times","times")]})]}),(0,o.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[b("version","Version"),":"]}),(0,o.jsx)("span",{className:"text-gray-900 dark:text-white font-medium",children:e.version})]})]}),(0,o.jsxs)("div",{className:"mt-4 flex flex-wrap gap-2",children:[e.config.compliance.zatcaCompliant&&(0,o.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 text-xs rounded",children:"ZATCA"}),e.config.compliance.nphiesCompliant&&(0,o.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded",children:"NPHIES"}),e.config.compliance.requireElectronicSignature&&(0,o.jsx)("span",{className:"px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 text-xs rounded",children:"E-Sign"})]})]}),(0,o.jsx)("div",{className:"px-6 pb-6",children:(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("button",{onClick:()=>(async e=>{try{await fetch("/api/v1/invoice-templates/".concat(e,"/use"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}),window.location.href="/financial/invoices/create?template=".concat(e)}catch(a){console.error("Error using template:",a),n.Ay.error("Failed to use template")}})(e._id),className:"flex-1 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-play mr-1"}),b("useTemplate","Use Template")]}),(0,o.jsx)("button",{onClick:()=>(async e=>{try{const a=await fetch("/api/v1/invoice-templates/".concat(e,"/duplicate"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!a.ok)throw new Error("Failed to duplicate template");await a.json(),n.Ay.success("Template duplicated successfully"),j()}catch(a){console.error("Error duplicating template:",a),n.Ay.error("Failed to duplicate template")}})(e._id),className:"px-3 py-2 bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500",title:b("duplicate","Duplicate"),children:(0,o.jsx)("i",{className:"fas fa-copy"})}),!e.isDefault&&(0,o.jsx)("button",{onClick:()=>(async e=>{try{if(!(await fetch("/api/v1/invoice-templates/".concat(e,"/set-default"),{method:"PUT",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}})).ok)throw new Error("Failed to set as default");n.Ay.success("Template set as default"),j()}catch(a){console.error("Error setting default:",a),n.Ay.error("Failed to set template as default")}})(e._id),className:"px-3 py-2 bg-green-200 text-green-700 dark:bg-green-600 dark:text-green-300 rounded hover:bg-green-300 dark:hover:bg-green-500",title:b("setAsDefault","Set as Default"),children:(0,o.jsx)("i",{className:"fas fa-star"})}),(0,o.jsx)("button",{onClick:()=>g(e),className:"px-3 py-2 bg-purple-200 text-purple-700 dark:bg-purple-600 dark:text-purple-300 rounded hover:bg-purple-300 dark:hover:bg-purple-500",title:b("preview","Preview"),children:(0,o.jsx)("i",{className:"fas fa-eye"})})]})})]},e._id)),(0,o.jsx)("div",{onClick:()=>h(!0),className:"bg-white dark:bg-gray-800 rounded-lg shadow border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-colors flex items-center justify-center min-h-[400px]",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("i",{className:"fas fa-plus text-4xl text-gray-400 dark:text-gray-500 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:b("createNewTemplate","Create New Template")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:b("createCustomTemplate","Create a custom invoice template")})]})})]}),0===e.length&&(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice text-6xl text-gray-400 mb-4"}),(0,o.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:b("noTemplatesFound","No Templates Found")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:b("noTemplatesDesc","No invoice templates match your current filter")}),(0,o.jsx)("button",{onClick:()=>p("all"),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:b("showAllTemplates","Show All Templates")})]}),m&&(0,o.jsx)(d,{onClose:()=>h(!1),onSave:async e=>{try{const a=await fetch("/api/v1/invoice-templates",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify(e)});if(a.ok){const e=await a.json();return n.Ay.success("Template created successfully"),j(),e.data}{const e=await a.json();throw new Error(e.message||"Failed to create template")}}catch(a){throw console.error("Error creating template:",a),a}}})]})}}}]);
//# sourceMappingURL=3191.dd85bbdc.chunk.js.map