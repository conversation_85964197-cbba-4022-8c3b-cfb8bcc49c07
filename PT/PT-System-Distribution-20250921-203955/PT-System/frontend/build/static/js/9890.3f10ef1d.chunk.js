"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9890],{9890:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var s=a(5043),r=a(7921),n=a(579);const i=()=>{var e,t,a,i,l,o,c,d,m;const{t:x,isRTL:g}=(0,r.o)(),[h,p]=(0,s.useState)("last-30-days"),[u,v]=(0,s.useState)("all"),[y,f]=(0,s.useState)({}),[j,b]=(0,s.useState)(!0);(0,s.useEffect)(()=>{const e={overview:{totalPatients:1247,activePatients:892,completedTreatments:456,ongoingTreatments:436,averageCompletionRate:78.5,averageTreatmentDuration:42,totalSessions:15678,completedSessions:12543,cancelledSessions:1234,noShowSessions:1901},patientProgress:{completed:456,inProgress:436,onHold:67,discontinued:89,notStarted:199},treatmentCompletion:{physicalTherapy:{completed:234,total:298,rate:78.5},occupationalTherapy:{completed:156,total:201,rate:77.6},speechTherapy:{completed:89,total:123,rate:72.4},specialNeeds:{completed:67,total:89,rate:75.3},carfPrograms:{completed:45,total:56,rate:80.4}},demographics:{ageGroups:{"0-18":234,"19-35":345,"36-50":298,"51-65":267,"65+":103},gender:{male:623,female:624},conditions:{"Cerebral Palsy":156,"Autism Spectrum":134,"Stroke Recovery":189,"Spinal Injury":98,"Developmental Delays":167,"Neurological Disorders":145,"Orthopedic Conditions":234,Other:124}},outcomeMetrics:{functionalImprovement:{significant:67,moderate:156,minimal:89,none:34},goalAchievement:{exceeded:89,achieved:234,partiallyAchieved:156,notAchieved:67},satisfactionScores:{excellent:456,good:234,fair:89,poor:23}},timeAnalysis:{averageWaitTime:5.2,averageSessionDuration:45,averageTreatmentLength:42,completionTimeVariance:12.5},financialMetrics:{totalRevenue:2456789,averageRevenuePerPatient:1970,insuranceCoverage:78.5,outOfPocketPayments:21.5,collectionRate:94.2},staffPerformance:{therapists:[{name:"Dr. Sarah Ahmed",patients:45,completionRate:85.2,satisfaction:4.8},{name:"Dr. Mohammed Ali",patients:38,completionRate:82.1,satisfaction:4.7},{name:"Dr. Fatima Hassan",patients:42,completionRate:88.9,satisfaction:4.9},{name:"Dr. Ahmed Khalil",patients:35,completionRate:79.3,satisfaction:4.6}]},trends:{monthlyAdmissions:[45,52,48,61,58,67,72,69,74,78,82,85],monthlyCompletions:[34,38,42,45,48,52,56,59,62,65,68,71],satisfactionTrend:[4.2,4.3,4.4,4.5,4.6,4.7,4.8,4.7,4.8,4.9,4.8,4.9]}};setTimeout(()=>{f(e),b(!1)},1e3)},[h]);const N=e=>{let{title:t,value:a,subtitle:s,icon:r,color:i,trend:l,percentage:o}=e;return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:t}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a}),s&&(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:s})]}),(0,n.jsx)("div",{className:"p-3 bg-".concat(i,"-100 dark:bg-").concat(i,"-900/30 rounded-lg"),children:(0,n.jsx)("i",{className:"".concat(r," text-").concat(i,"-600 dark:text-").concat(i,"-400 text-xl")})})]}),l&&(0,n.jsxs)("div",{className:"mt-4 flex items-center",children:[(0,n.jsxs)("span",{className:"text-sm font-medium ".concat("up"===l?"text-green-600":"down"===l?"text-red-600":"text-gray-600"),children:[(0,n.jsx)("i",{className:"fas fa-arrow-".concat("up"===l?"up":"down"===l?"down":"right"," mr-1")}),o,"%"]}),(0,n.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 ml-2",children:x("fromLastPeriod","from last period")})]})]})},w=()=>{if(!y.treatmentCompletion)return(0,n.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Loading chart data..."});const e=["bg-blue-500","bg-green-500","bg-yellow-500","bg-red-500","bg-purple-500"];return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("treatmentCompletionRates","Treatment Completion Rates by Type")}),Object.entries(y.treatmentCompletion).map((t,a)=>{let[s,r]=t;return(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:s}),(0,n.jsxs)("span",{className:"text-sm font-bold text-gray-900 dark:text-white",children:[r.rate,"%"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3",children:(0,n.jsx)("div",{className:"h-3 rounded-full ".concat(e[a%e.length]," transition-all duration-500"),style:{width:"".concat(r.rate,"%")}})}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[r.completed," of ",r.total," completed"]})]},s)})]})},k=()=>{if(!y.patientProgress)return(0,n.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Loading chart data..."});const e=[x("completed","Completed"),x("inProgress","In Progress"),x("onHold","On Hold"),x("discontinued","Discontinued"),x("notStarted","Not Started")],t=Object.values(y.patientProgress),a=t.reduce((e,t)=>e+t,0),s=["text-green-600","text-blue-600","text-yellow-600","text-red-600","text-gray-600"],r=["bg-green-500","bg-blue-500","bg-yellow-500","bg-red-500","bg-gray-500"];return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("patientProgressDistribution","Patient Progress Distribution")}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-3",children:e.map((e,i)=>{const l=t[i],o=a>0?Math.round(l/a*100):0;return(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded-full ".concat(r[i])}),(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("div",{className:"text-lg font-bold ".concat(s[i]),children:l}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[o,"%"]})]})]},e)})})]})},S=()=>{if(!y.trends)return(0,n.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Loading chart data..."});const e=y.trends.monthlyAdmissions||[],t=y.trends.monthlyCompletions||[],a=Math.max(...e,...t);return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("monthlyTrends","Monthly Trends")}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,n.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:x("admissions","Admissions")})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,n.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:x("completions","Completions")})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-12 gap-2 h-48",children:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((s,r)=>{const i=a>0?e[r]/a*100:0,l=a>0?t[r]/a*100:0;return(0,n.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,n.jsxs)("div",{className:"flex-1 flex flex-col justify-end space-y-1 w-full",children:[(0,n.jsx)("div",{className:"bg-blue-500 rounded-t",style:{height:"".concat(i,"%"),minHeight:"2px"},title:"".concat(s,": ").concat(e[r]," admissions")}),(0,n.jsx)("div",{className:"bg-green-500 rounded-t",style:{height:"".concat(l,"%"),minHeight:"2px"},title:"".concat(s,": ").concat(t[r]," completions")})]}),(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400 transform rotate-45 origin-bottom-left",children:s})]},s)})})]})]})};return j?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"space-y-6 ".concat(g?"font-arabic":"font-english"),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:x("advancedAnalytics","Advanced Analytics & Reporting")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:x("comprehensivePatientStatistics","Comprehensive patient statistics and treatment analytics")})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"last-7-days",children:x("last7Days","Last 7 Days")}),(0,n.jsx)("option",{value:"last-30-days",children:x("last30Days","Last 30 Days")}),(0,n.jsx)("option",{value:"last-90-days",children:x("last90Days","Last 90 Days")}),(0,n.jsx)("option",{value:"last-year",children:x("lastYear","Last Year")}),(0,n.jsx)("option",{value:"all-time",children:x("allTime","All Time")})]}),(0,n.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-download mr-2"}),x("exportReport","Export Report")]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,n.jsx)(N,{title:x("totalPatients","Total Patients"),value:null===(e=y.overview)||void 0===e||null===(t=e.totalPatients)||void 0===t?void 0:t.toLocaleString(),subtitle:"".concat(null===(a=y.overview)||void 0===a?void 0:a.activePatients," ").concat(x("active","active")),icon:"fas fa-users",color:"blue",trend:"up",percentage:"12.5"}),(0,n.jsx)(N,{title:x("completionRate","Completion Rate"),value:"".concat(null===(i=y.overview)||void 0===i?void 0:i.averageCompletionRate,"%"),subtitle:"".concat(null===(l=y.overview)||void 0===l?void 0:l.completedTreatments," ").concat(x("completed","completed")),icon:"fas fa-check-circle",color:"green",trend:"up",percentage:"5.2"}),(0,n.jsx)(N,{title:x("averageDuration","Average Duration"),value:"".concat(null===(o=y.overview)||void 0===o?void 0:o.averageTreatmentDuration," ").concat(x("days","days")),subtitle:x("treatmentLength","treatment length"),icon:"fas fa-clock",color:"yellow",trend:"down",percentage:"3.1"}),(0,n.jsx)(N,{title:x("totalSessions","Total Sessions"),value:null===(c=y.overview)||void 0===c||null===(d=c.totalSessions)||void 0===d?void 0:d.toLocaleString(),subtitle:"".concat(null===(m=y.overview)||void 0===m?void 0:m.completedSessions," ").concat(x("completed","completed")),icon:"fas fa-calendar-check",color:"purple",trend:"up",percentage:"8.7"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsx)(w,{})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsx)(k,{})})]}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsx)(S,{})})]})}}}]);
//# sourceMappingURL=9890.3f10ef1d.chunk.js.map