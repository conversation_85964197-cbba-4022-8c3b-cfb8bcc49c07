{"version": 3, "file": "static/js/6950.2cb9e87a.chunk.js", "mappings": "kNAGA,MAucA,EAvc+BA,KAC7B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,KAClCC,EAAkBC,IAAuBF,EAAAA,EAAAA,UAAS,KAClDG,EAASC,IAAcJ,EAAAA,EAAAA,UAAS,CACrCK,OAAQ,MACRC,cAAe,MACfC,eAAgB,MAChBC,UAAW,SAENC,EAAYC,IAAiBV,EAAAA,EAAAA,UAAS,KACtCW,EAAQC,IAAaZ,EAAAA,EAAAA,UAAS,SAC9Ba,EAAWC,IAAgBd,EAAAA,EAAAA,UAAS,QACpCe,EAASC,IAAchB,EAAAA,EAAAA,WAAS,IAGvCiB,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAe,CACnB,CACEC,GAAI,EACJC,KAAM,iBACNC,IAAK,GACLC,UAAW,gBACXhB,cAAe,mBACfD,OAAQ,cACRkB,UAAW,aACXC,gBAAiB,aACjBjB,eAAgB,GAChBkB,kBAAmB,GACnBC,cAAe,GACfC,YAAa,aACbC,YAAa,aACbC,UAAW,kBACXC,MAAO,CACL,CAAEC,KAAM,mBAAoBC,SAAU,GAAI3B,OAAQ,YAClD,CAAE0B,KAAM,cAAeC,SAAU,GAAI3B,OAAQ,SAC7C,CAAE0B,KAAM,oBAAqBC,SAAU,GAAI3B,OAAQ,WAErD4B,iBAAkB,CAChBC,QAAS,GACTC,QAAS,GACTC,OAAQ,IAEVC,MAAO,uDAET,CACElB,GAAI,EACJC,KAAM,gBACNC,IAAK,GACLC,UAAW,kBACXhB,cAAe,uBACfD,OAAQ,YACRkB,UAAW,aACXe,QAAS,aACT/B,eAAgB,IAChBkB,kBAAmB,GACnBC,cAAe,GACfC,YAAa,aACbE,UAAW,mBACXC,MAAO,CACL,CAAEC,KAAM,2BAA4BC,SAAU,GAAI3B,OAAQ,YAC1D,CAAE0B,KAAM,uBAAwBC,SAAU,GAAI3B,OAAQ,YACtD,CAAE0B,KAAM,0BAA2BC,SAAU,GAAI3B,OAAQ,aAE3D4B,iBAAkB,CAChBC,QAAS,GACTC,QAAS,GACTC,OAAQ,IAEVC,MAAO,4DAET,CACElB,GAAI,EACJC,KAAM,cACNC,IAAK,GACLC,UAAW,kBACXhB,cAAe,iBACfD,OAAQ,cACRkB,UAAW,aACXC,gBAAiB,aACjBjB,eAAgB,GAChBkB,kBAAmB,EACnBC,cAAe,GACfC,YAAa,aACbC,YAAa,aACbC,UAAW,oBACXC,MAAO,CACL,CAAEC,KAAM,wBAAyBC,SAAU,GAAI3B,OAAQ,YACvD,CAAE0B,KAAM,qBAAsBC,SAAU,GAAI3B,OAAQ,UACpD,CAAE0B,KAAM,uBAAwBC,SAAU,GAAI3B,OAAQ,UAExD4B,iBAAkB,CAChBC,QAAS,GACTC,QAAS,GACTC,OAAQ,IAEVC,MAAO,iEAET,CACElB,GAAI,EACJC,KAAM,iBACNC,IAAK,GACLC,UAAW,iBACXhB,cAAe,mBACfD,OAAQ,UACRkB,UAAW,aACXC,gBAAiB,aACjBjB,eAAgB,GAChBkB,kBAAmB,EACnBC,cAAe,GACfC,YAAa,aACbE,UAAW,mBACXC,MAAO,CACL,CAAEC,KAAM,kBAAmBC,SAAU,GAAI3B,OAAQ,UACjD,CAAE0B,KAAM,qBAAsBC,SAAU,GAAI3B,OAAQ,UACpD,CAAE0B,KAAM,mBAAoBC,SAAU,GAAI3B,OAAQ,WAEpD4B,iBAAkB,CAChBC,QAAS,GACTC,QAAS,GACTC,OAAQ,IAEVC,MAAO,kDAET,CACElB,GAAI,EACJC,KAAM,gBACNC,IAAK,GACLC,UAAW,wBACXhB,cAAe,uBACfD,OAAQ,eACRkB,UAAW,aACXe,QAAS,aACT/B,eAAgB,GAChBkB,kBAAmB,EACnBC,cAAe,GACfC,YAAa,aACbE,UAAW,kBACXC,MAAO,CACL,CAAEC,KAAM,wBAAyBC,SAAU,GAAI3B,OAAQ,gBACvD,CAAE0B,KAAM,qBAAsBC,SAAU,GAAI3B,OAAQ,gBACpD,CAAE0B,KAAM,mBAAoBC,SAAU,GAAI3B,OAAQ,YAEpD4B,iBAAkB,CAChBC,QAAS,GACTC,QAAS,GACTC,OAAQ,IAEVC,MAAO,kDAIXE,WAAW,KACTxC,EAAYmB,GACZhB,EAAoBgB,GACpBF,GAAW,IACV,MACF,KAGHC,EAAAA,EAAAA,WAAU,KACR,IAAIuB,EAAW1C,EAAS2C,OAAOC,IAC7B,MAAMC,EAAgBD,EAAQtB,KAAKwB,cAAcC,SAASpC,EAAWmC,gBAChDF,EAAQpB,UAAUsB,cAAcC,SAASpC,EAAWmC,gBACpDF,EAAQb,UAAUe,cAAcC,SAASpC,EAAWmC,eAEnEE,EAAmC,QAAnB3C,EAAQE,QAAoBqC,EAAQrC,SAAWF,EAAQE,OACvE0C,EAA6C,QAA1B5C,EAAQG,eAA2BoC,EAAQpC,gBAAkBH,EAAQG,cAE9F,IAAI0C,GAAoB,EAKxB,MAJ+B,SAA3B7C,EAAQI,eAA2ByC,EAAoBN,EAAQnC,gBAAkB,GACjD,WAA3BJ,EAAQI,eAA6ByC,EAAoBN,EAAQnC,gBAAkB,IAAMmC,EAAQnC,eAAiB,GACvF,QAA3BJ,EAAQI,iBAA0ByC,EAAoBN,EAAQnC,eAAiB,IAEjFoC,GAAiBG,GAAiBC,GAAoBC,IAI/DR,EAASS,KAAK,CAACC,EAAGC,KAChB,IAAIC,EAASF,EAAEvC,GACX0C,EAASF,EAAExC,GAOf,MALe,mBAAXA,IACFyC,EAASE,WAAWF,GACpBC,EAASC,WAAWD,IAGJ,QAAdxC,EACKuC,EAASC,EAAS,GAAK,EAEvBD,EAASC,EAAS,GAAK,IAIlCnD,EAAoBsC,IACnB,CAAC1C,EAAUW,EAAYN,EAASQ,EAAQE,IAE3C,MAAM0C,EAAkBlD,IACtB,OAAQA,GACN,IAAK,YAAa,MAAO,uEACzB,IAAK,cAAe,MAAO,mEAC3B,IAAK,UAAW,MAAO,2EACvB,IAAK,eAAgB,MAAO,+DAC5B,QAAS,MAAO,qEAWdmD,EAAsBnD,IAC1B,OAAQA,GACN,IAAK,WAEL,IAAK,WAAY,MAAO,iBADxB,IAAK,QAAS,MAAO,gBAErB,IAAK,SAAU,MAAO,kBACtB,IAAK,eAAgB,MAAO,eAC5B,IAAK,UAAW,MAAO,kBACvB,QAAS,MAAO,kBAIpB,OAAIU,GAEA0C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4CAA2CC,UACxDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAejE,EAAQ,cAAgB,gBAAiB+D,SAAA,EAEpEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,SAAU,aAEf8D,EAAAA,EAAAA,KAAA,SACEK,KAAK,OACLC,MAAOtD,EACPuD,SAAWC,GAAMvD,EAAcuD,EAAE7B,OAAO2B,OACxCG,YAAavE,EAAE,iBAAkB,sBACjC+D,UAAU,wIAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,SAAU,aAEfiE,EAAAA,EAAAA,MAAA,UACEG,MAAO5D,EAAQE,OACf2D,SAAWC,GAAM7D,EAAW+D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE9D,OAAQ4D,EAAE7B,OAAO2B,SACjEL,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,MAAKJ,SAAEhE,EAAE,cAAe,mBACtC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,cAAaJ,SAAEhE,EAAE,aAAc,kBAC7C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,YAAWJ,SAAEhE,EAAE,YAAa,gBAC1C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,UAASJ,SAAEhE,EAAE,SAAU,cACrC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,eAAcJ,SAAEhE,EAAE,eAAgB,yBAIpDiE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,gBAAiB,qBAEtBiE,EAAAA,EAAAA,MAAA,UACEG,MAAO5D,EAAQG,cACf0D,SAAWC,GAAM7D,EAAW+D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE7D,cAAe2D,EAAE7B,OAAO2B,SACxEL,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,MAAKJ,SAAEhE,EAAE,WAAY,gBACnC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,mBAAkBJ,SAAEhE,EAAE,kBAAmB,uBACvD8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,uBAAsBJ,SAAEhE,EAAE,sBAAuB,2BAC/D8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,iBAAgBJ,SAAEhE,EAAE,gBAAiB,2BAIvDiE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,iBAAkB,sBAEvBiE,EAAAA,EAAAA,MAAA,UACEG,MAAO5D,EAAQI,eACfyD,SAAWC,GAAM7D,EAAW+D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE5D,eAAgB0D,EAAE7B,OAAO2B,SACzEL,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,MAAKJ,SAAEhE,EAAE,WAAY,gBACnC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,OAAMJ,SAAEhE,EAAE,OAAQ,kBAChC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,SAAQJ,SAAEhE,EAAE,SAAU,sBACpC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,MAAKJ,SAAEhE,EAAE,MAAO,uBAIlCiE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,SAAU,cAEfiE,EAAAA,EAAAA,MAAA,UACEG,MAAOpD,EACPqD,SAAWC,GAAMrD,EAAUqD,EAAE7B,OAAO2B,OACpCL,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,OAAMJ,SAAEhE,EAAE,OAAQ,WAChC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,iBAAgBJ,SAAEhE,EAAE,iBAAkB,sBACpD8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,YAAWJ,SAAEhE,EAAE,YAAa,iBAC1C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,SAAQJ,SAAEhE,EAAE,SAAU,wBAO5C8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,SACnD1D,EAAiBoE,IAAI3B,IACpBkB,SAAAA,EAAAA,MAAA,OAAsBF,UAAU,8FAA6FC,SAAA,EAE3HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAAEjB,EAAQtB,QAC7EwC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpDjB,EAAQrB,IAAI,IAAE1B,EAAE,QAAS,SAAS,WAAI+C,EAAQpB,iBAGnDmC,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAAG,OAAgDN,EAAeb,EAAQrC,SAAUsD,SAC7FjB,EAAQrC,aAKbuD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SACnEhE,EAAE,kBAAmB,uBAExBiE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,oDAAmDC,SAAA,CAChEjB,EAAQnC,eAAe,WAG5BkD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,gDAAAG,QA7IC7B,EA6IkEU,EAAQnC,eA5I9FyB,GAAY,GAAW,eACvBA,GAAY,GAAW,cACvBA,GAAY,GAAW,gBACpB,eA0IOsC,MAAO,CAAEC,MAAM,GAADV,OAAKnB,EAAQnC,eAAc,cAM/CqD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sCAAqCC,SAAA,EAClDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhE,EAAE,gBAAiB,aAAa,QACpF8D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SAAEjB,EAAQpC,oBAEpEsD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhE,EAAE,YAAa,aAAa,QAChF8D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SAAEjB,EAAQb,gBAEpE+B,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhE,EAAE,WAAY,YAAY,QAC9EiE,EAAAA,EAAAA,MAAA,KAAGF,UAAU,4CAA2CC,SAAA,CACrDjB,EAAQjB,kBAAkB,IAAEiB,EAAQhB,qBAGzCkC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhE,EAAE,cAAe,gBAAgB,QACrF8D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SACrDjB,EAAQd,aAAejC,EAAE,eAAgB,0BAMhDiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtEhE,EAAE,QAAS,YAEd8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBjB,EAAQZ,MAAMuC,IAAI,CAACtC,EAAMyC,KACxBZ,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,oCAAmCC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kDAAiDC,SAC9D5B,EAAKA,QAER6B,EAAAA,EAAAA,MAAA,QAAMF,UAAS,uBAAAG,OAAyBL,EAAmBzB,EAAK1B,QAAO,SAAQsD,SAAA,CAC5E5B,EAAKC,SAAS,SALTwC,UAahBZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtEhE,EAAE,mBAAoB,wBAEzBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iCAAgCC,SAAA,EAC7CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4CAA2CC,SAAEjB,EAAQT,iBAAiBC,WACrFuB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SAAEhE,EAAE,UAAW,iBAElEiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BC,SAAEjB,EAAQT,iBAAiBE,WACrEsB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SAAEhE,EAAE,UAAW,iBAElEiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAA4BC,SAAEjB,EAAQT,iBAAiBG,UACtEqB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SAAEhE,EAAE,SAAU,uBA3F7D+C,EAAQvB,IAnHAa,WAuNtB4B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,oBAAqB,yBAE1BiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mCAAkCC,SAAE1D,EAAiBwE,UACpEhB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SAAEhE,EAAE,gBAAiB,wBAEhFiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,SAC/C1D,EAAiBwC,OAAOiC,GAAkB,cAAbA,EAAErE,QAAwBoE,UAE1DhB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SAAEhE,EAAE,YAAa,mBAE5EiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qCAAoCC,SAChD1D,EAAiBwC,OAAOiC,GAAkB,gBAAbA,EAAErE,QAA0BoE,UAE5DhB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SAAEhE,EAAE,aAAc,qBAE7EiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,CAC9CgB,KAAKC,MAAM3E,EAAiB4E,OAAO,CAACC,EAAKJ,IAAMI,EAAMJ,EAAEnE,eAAgB,GAAKN,EAAiBwE,QAAQ,QAExGhB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SAAEhE,EAAE,oBAAqB,qCCwI9F,EAvkB8BoF,KAC5B,MAAM,EAAEpF,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdmF,EAAcC,IAAmBjF,EAAAA,EAAAA,UAAS,CAC/CkF,MAAO,GACPC,YAAa,GACbrB,KAAM,QACNsB,WAAY,WACZC,OAAQ,GACRlF,QAAS,GACTmF,QAAS,GACT3E,OAAQ,GACRE,UAAW,MACXL,UAAW,CACT+E,MAAO,GACPC,IAAK,IAEPC,UAAW,MACXC,OAAQ,cAGHC,EAAiBC,IAAsB5F,EAAAA,EAAAA,UAAS,CAAC,IACjD6F,EAAaC,IAAkB9F,EAAAA,EAAAA,UAAS,KACxC+F,EAAcC,IAAmBhG,EAAAA,EAAAA,UAAS,KAC1CiG,EAAaC,IAAkBlG,EAAAA,EAAAA,WAAS,GAGzCmG,EAAc,CAClBrG,SAAU,CACRsG,MAAOzG,EAAE,WAAY,YACrB0F,OAAQ,CACN,eAAgB,CAAEe,MAAOzG,EAAE,cAAe,gBAAiBmE,KAAM,QACjE,cAAe,CAAEsC,MAAOzG,EAAE,MAAO,OAAQmE,KAAM,UAC/C,iBAAkB,CAAEsC,MAAOzG,EAAE,SAAU,UAAWmE,KAAM,QACxD,oBAAqB,CAAEsC,MAAOzG,EAAE,YAAa,aAAcmE,KAAM,QACjE,2BAA4B,CAAEsC,MAAOzG,EAAE,mBAAoB,qBAAsBmE,KAAM,QACvF,iBAAkB,CAAEsC,MAAOzG,EAAE,SAAU,UAAWmE,KAAM,QACxD,oBAAqB,CAAEsC,MAAOzG,EAAE,oBAAqB,sBAAuBmE,KAAM,QAClF,oBAAqB,CAAEsC,MAAOzG,EAAE,YAAa,aAAcmE,KAAM,QACjE,2BAA4B,CAAEsC,MAAOzG,EAAE,mBAAoB,qBAAsBmE,KAAM,UAG3FuC,WAAY,CACVD,MAAOzG,EAAE,aAAc,cACvB0F,OAAQ,CACN,iBAAkB,CAAEe,MAAOzG,EAAE,gBAAiB,kBAAmBmE,KAAM,QACvE,sBAAuB,CAAEsC,MAAOzG,EAAE,YAAa,cAAemE,KAAM,QACpE,oBAAqB,CAAEsC,MAAOzG,EAAE,UAAW,YAAamE,KAAM,QAC9D,mBAAoB,CAAEsC,MAAOzG,EAAE,kBAAmB,oBAAqBmE,KAAM,QAC7E,2BAA4B,CAAEsC,MAAOzG,EAAE,iBAAkB,mBAAoBmE,KAAM,UACnF,8BAA+B,CAAEsC,MAAOzG,EAAE,oBAAqB,sBAAuBmE,KAAM,UAC5F,0BAA2B,CAAEsC,MAAOzG,EAAE,gBAAiB,kBAAmBmE,KAAM,UAChF,qBAAsB,CAAEsC,MAAOzG,EAAE,WAAY,mBAAoBmE,KAAM,UACvE,iBAAkB,CAAEsC,MAAOzG,EAAE,gBAAiB,kBAAmBmE,KAAM,cAG3EwC,SAAU,CACRF,MAAOzG,EAAE,WAAY,YACrB0F,OAAQ,CACN,eAAgB,CAAEe,MAAOzG,EAAE,cAAe,gBAAiBmE,KAAM,QACjE,mBAAoB,CAAEsC,MAAOzG,EAAE,kBAAmB,oBAAqBmE,KAAM,UAC7E,eAAgB,CAAEsC,MAAOzG,EAAE,cAAe,gBAAiBmE,KAAM,QACjE,oBAAqB,CAAEsC,MAAOzG,EAAE,YAAa,aAAcmE,KAAM,QACjE,iBAAkB,CAAEsC,MAAOzG,EAAE,gBAAiB,kBAAmBmE,KAAM,QACvE,gBAAiB,CAAEsC,MAAOzG,EAAE,eAAgB,iBAAkBmE,KAAM,QACpE,qBAAsB,CAAEsC,MAAOzG,EAAE,aAAc,cAAemE,KAAM,QACpE,mBAAoB,CAAEsC,MAAOzG,EAAE,iBAAkB,mBAAoBmE,KAAM,YAG/EyC,UAAW,CACTH,MAAOzG,EAAE,YAAa,aACtB0F,OAAQ,CACN,yBAA0B,CAAEe,MAAOzG,EAAE,eAAgB,iBAAkBmE,KAAM,YAC7E,8BAA+B,CAAEsC,MAAOzG,EAAE,oBAAqB,sBAAuBmE,KAAM,YAC5F,gCAAiC,CAAEsC,MAAOzG,EAAE,sBAAuB,0BAA2BmE,KAAM,YACpG,4BAA6B,CAAEsC,MAAOzG,EAAE,kBAAmB,oBAAqBmE,KAAM,YACtF,wBAAyB,CAAEsC,MAAOzG,EAAE,cAAe,gBAAiBmE,KAAM,QAC1E,0BAA2B,CAAEsC,MAAOzG,EAAE,gBAAiB,kBAAmBmE,KAAM,UAGpF0C,SAAU,CACRJ,MAAOzG,EAAE,WAAY,YACrB0F,OAAQ,CACN,0BAA2B,CAAEe,MAAOzG,EAAE,kBAAmB,oBAAqBmE,KAAM,UACpF,oBAAqB,CAAEsC,MAAOzG,EAAE,YAAa,cAAemE,KAAM,UAClE,wBAAyB,CAAEsC,MAAOzG,EAAE,gBAAiB,kBAAmBmE,KAAM,UAC9E,wBAAyB,CAAEsC,MAAOzG,EAAE,gBAAiB,mBAAoBmE,KAAM,UAC/E,0BAA2B,CAAEsC,MAAOzG,EAAE,kBAAmB,oBAAqBmE,KAAM,cACpF,4BAA6B,CAAEsC,MAAOzG,EAAE,oBAAqB,sBAAuBmE,KAAM,UAC1F,yBAA0B,CAAEsC,MAAOzG,EAAE,iBAAkB,mBAAoBmE,KAAM,WAKjF2C,EAAc,CAClB,CAAE1C,MAAO,QAASqC,MAAOzG,EAAE,QAAS,SAAU+G,KAAM,gBACpD,CAAE3C,MAAO,QAASqC,MAAOzG,EAAE,QAAS,SAAU+G,KAAM,oBACpD,CAAE3C,MAAO,UAAWqC,MAAOzG,EAAE,UAAW,WAAY+G,KAAM,eAC1D,CAAE3C,MAAO,YAAaqC,MAAOzG,EAAE,YAAa,aAAc+G,KAAM,0BAIzC/G,EAAE,WAAY,aACbA,EAAE,YAAa,cAChBA,EAAE,WAAY,aACTA,EAAE,gBAAiB,kBACvBA,EAAE,YAAa,eAGzCsB,EAAAA,EAAAA,WAAU,KAAO,IAAD0F,EACdf,GAAuD,QAApCe,EAAAR,EAAYnB,EAAaI,mBAAW,IAAAuB,OAAA,EAApCA,EAAsCtB,SAAU,CAAC,IACnE,CAACL,EAAaI,cAGjBnE,EAAAA,EAAAA,WAAU,KA8BR+E,EA7ByB,CACvB,CACE7E,GAAI,EACJ+D,MAAO,4BACPC,YAAa,6CACbrB,KAAM,QACNsB,WAAY,aACZwB,YAAa,aACbC,aAAc,cAEhB,CACE1F,GAAI,EACJ+D,MAAO,2BACPC,YAAa,6CACbrB,KAAM,YACNsB,WAAY,YACZwB,YAAa,aACbC,aAAc,cAEhB,CACE1F,GAAI,EACJ+D,MAAO,uBACPC,YAAa,0CACbrB,KAAM,QACNsB,WAAY,WACZwB,YAAa,aACbC,aAAc,iBAIjB,IAEH,MAuBMC,EAAeA,CAACtC,EAAOuC,EAAKhD,KAChCkB,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACPhE,QAASgE,EAAKhE,QAAQkE,IAAI,CAAC5B,EAAQuE,IACjCA,IAAMxC,GAAKJ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQ3B,GAAM,IAAE,CAACsE,GAAMhD,IAAUtB,OAoC5CwE,EAAgBC,IACpBC,MAAMxH,EAAE,kBAAkB,uBAADkE,OAAyBqD,EAAOE,cAAa,UAGxE,OACExD,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAejE,EAAQ,cAAgB,gBAAiB+D,SAAA,EAEpEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7DhE,EAAE,wBAAyB,8BAE9B8D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDhE,EAAE,sBAAuB,gEAG9BiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEyD,QA1CcC,KAStBxB,EAPiB,CACf,CAAE,eAAgB,iBAAkB,cAAe,GAAI,2BAA4B,IACnF,CAAE,eAAgB,gBAAiB,cAAe,GAAI,2BAA4B,KAClF,CAAE,eAAgB,cAAe,cAAe,GAAI,2BAA4B,IAChF,CAAE,eAAgB,iBAAkB,cAAe,GAAI,2BAA4B,IACnF,CAAE,eAAgB,gBAAiB,cAAe,GAAI,2BAA4B,MAGpFI,GAAe,IAiCPxC,UAAU,kFAAiFC,SAAA,EAE3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBACZ/D,EAAE,UAAW,eAEhBiE,EAAAA,EAAAA,MAAA,UACEyD,QApCSE,KACjB,MAAMC,GAASpD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACbjD,GAAIsG,KAAKC,OACN1C,GAAY,IACf4B,aAAa,IAAIa,MAAOE,cAAcC,MAAM,KAAK,GACjDf,cAAc,IAAIY,MAAOE,cAAcC,MAAM,KAAK,KAEpD5B,EAAgB7B,GAAQ,CAACqD,KAAcrD,IACvCgD,MAAMxH,EAAE,cAAe,gCA6Bf+D,UAAU,oFAAmFC,SAAA,EAE7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ/D,EAAE,aAAc,yBAKvBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EAEtCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,mBAAoB,wBAEzBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,cAAe,mBAEpB8D,EAAAA,EAAAA,KAAA,SACEK,KAAK,OACLC,MAAOiB,EAAaE,MACpBlB,SAAWC,GAAMgB,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEe,MAAOjB,EAAE7B,OAAO2B,SACrEL,UAAU,kIACVQ,YAAavE,EAAE,mBAAoB,+BAGvCiE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,cAAe,kBAEpB8D,EAAAA,EAAAA,KAAA,YACEM,MAAOiB,EAAaG,YACpBnB,SAAWC,GAAMgB,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEgB,YAAalB,EAAE7B,OAAO2B,SAC3E8D,KAAK,IACLnE,UAAU,kIACVQ,YAAavE,EAAE,mBAAoB,2CAO3CiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,sBAAuB,2BAE5BiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,aAAc,kBAEnB8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,SACpC8C,EAAYpC,IAAIP,IACfF,EAAAA,EAAAA,MAAA,UAEEyD,QAASA,IAAMpC,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEL,KAAMA,EAAKC,SAC9DL,UAAS,uDAAAG,OACPmB,EAAalB,OAASA,EAAKC,MACvB,kFACA,8DACHJ,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAG,OAAKC,EAAK4C,KAAI,oBAC1BjD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,UAASC,SAAEG,EAAKsC,UAT1BtC,EAAKC,cAclBH,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,aAAc,kBAEnB8D,EAAAA,EAAAA,KAAA,UACEM,MAAOiB,EAAaI,WACpBpB,SAAWC,GAAMgB,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEiB,WAAYnB,EAAE7B,OAAO2B,MAAOsB,OAAQ,MACzF3B,UAAU,kIAAiIC,SAE1ImE,OAAOC,QAAQ5B,GAAa9B,IAAI2D,IAAA,IAAEjB,EAAKkB,GAAOD,EAAA,OAC7CvE,EAAAA,EAAAA,KAAA,UAAkBM,MAAOgD,EAAIpD,SAAEsE,EAAO7B,OAAzBW,iBAQvBnD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,eAAgB,oBAErBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtEhE,EAAE,kBAAmB,uBAExB8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qCAAoCC,SAChDmE,OAAOC,QAAQpC,GAAiBtB,IAAI6D,IAAA,IAAEnB,EAAKoB,GAAMD,EAAA,OAChDtE,EAAAA,EAAAA,MAAA,UAEEyD,QAASA,KAAMe,OA/LjBC,EA+L0BtB,OA9LrC/B,EAAaK,OAAOxC,SAASwF,IAChCpD,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACPkB,OAAQ,IAAIlB,EAAKkB,OAAQgD,OAJbA,OAgME3E,UAAU,2IACV4E,SAAUtD,EAAaK,OAAOxC,SAASkE,GAAKpD,SAAA,EAE5CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,SAAEwE,EAAM/B,SAC1E3C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SAAEwE,EAAMrE,SAN5DiD,WAWbnD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtEhE,EAAE,iBAAkB,sBAEvBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qCAAoCC,SAAA,CAChDqB,EAAaK,OAAOhB,IAAI,CAACgE,EAAU7D,KAAK,IAAA+D,EAAA,OACvC3E,EAAAA,EAAAA,MAAA,OAEEF,UAAU,iIAAgIC,SAAA,EAE1IF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAC9B,QAD8B4E,EACvD5C,EAAgB0C,UAAS,IAAAE,OAAA,EAAzBA,EAA2BnC,SAE9B3C,EAAAA,EAAAA,KAAA,UACE4D,QAASA,IA9MVgB,KACnBpD,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACPkB,OAAQlB,EAAKkB,OAAO5C,OAAO+F,GAAKA,IAAMH,OA2MLI,CAAYJ,GAC3B3E,UAAU,4EAA2EC,UAErFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAVV2E,KAcuB,IAA/BrD,EAAaK,OAAOZ,SACnBhB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,SAC/DhE,EAAE,mBAAoB,qCASnCiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhE,EAAE,UAAW,cAEhBiE,EAAAA,EAAAA,MAAA,UACEyD,QA/NIqB,KAChBzD,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACPhE,QAAS,IAAIgE,EAAKhE,QAAS,CAAEgI,MAAO,GAAIQ,SAAU,SAAU5E,MAAO,SA6NzDL,UAAU,uFAAsFC,SAAA,EAEhGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ/D,EAAE,YAAa,qBAGpBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBqB,EAAa7E,QAAQkE,IAAI,CAAC5B,EAAQ+B,KACjCZ,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,kDAAiDC,SAAA,EAC1EC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,QAAS,YAEdiE,EAAAA,EAAAA,MAAA,UACEG,MAAOtB,EAAO0F,MACdnE,SAAWC,GAAM6C,EAAatC,EAAO,QAASP,EAAE7B,OAAO2B,OACvDL,UAAU,uIAAsIC,SAAA,EAEhJF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,GAAEJ,SAAEhE,EAAE,cAAe,kBAClCmI,OAAOC,QAAQpC,GAAiBtB,IAAIuE,IAAA,IAAE7B,EAAKoB,GAAMS,EAAA,OAChDnF,EAAAA,EAAAA,KAAA,UAAkBM,MAAOgD,EAAIpD,SAAEwE,EAAM/B,OAAxBW,YAInBnD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,WAAY,eAEjBiE,EAAAA,EAAAA,MAAA,UACEG,MAAOtB,EAAOkG,SACd3E,SAAWC,GAAM6C,EAAatC,EAAO,WAAYP,EAAE7B,OAAO2B,OAC1DL,UAAU,uIAAsIC,SAAA,EAEhJF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,SAAQJ,SAAEhE,EAAE,SAAU,aACpC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,WAAUJ,SAAEhE,EAAE,WAAY,eACxC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,UAASJ,SAAEhE,EAAE,cAAe,mBAC1C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,OAAMJ,SAAEhE,EAAE,WAAY,gBACpC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,UAASJ,SAAEhE,EAAE,UAAW,oBAG1CiE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhE,EAAE,QAAS,YAEd8D,EAAAA,EAAAA,KAAA,SACEK,KAAK,OACLC,MAAOtB,EAAOsB,MACdC,SAAWC,GAAM6C,EAAatC,EAAO,QAASP,EAAE7B,OAAO2B,OACvDL,UAAU,uIACVQ,YAAavE,EAAE,aAAc,wBAGjC8D,EAAAA,EAAAA,KAAA,OAAAE,UACEF,EAAAA,EAAAA,KAAA,UACE4D,QAASA,IAtQP7C,KACpBS,EAAgBd,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACPhE,QAASgE,EAAKhE,QAAQsC,OAAO,CAACoG,EAAG7B,IAAMA,IAAMxC,OAmQdsE,CAAatE,GAC5Bd,UAAU,sFAAqFC,UAE/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAjDTc,IAsDqB,IAAhCQ,EAAa7E,QAAQsE,SACpBhB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,SAC/DhE,EAAE,iBAAkB,gCAQ/BiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,eAAgB,oBAErB8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBoC,EAAa1B,IAAI0E,IAChBnF,EAAAA,EAAAA,MAAA,OAAqBF,UAAU,6DAA4DC,SAAA,EACzFF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oDAAmDC,SAAEoF,EAAO7D,SAC1EzB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAEoF,EAAO5D,eACrEvB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvD,IAAI8D,KAAKsB,EAAOlC,cAAcmC,wBAEjCpF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,gFAA+EC,UAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BAEfD,EAAAA,EAAAA,KAAA,UAAQC,UAAU,oFAAmFC,UACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCAZXqF,EAAO5H,WAsBvByC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,gBAAiB,qBAEtBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,UACEyD,QAASA,IAAMJ,EAAa,OAC5BvD,UAAU,2IAA0IC,SAAA,EAEpJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACZ/D,EAAE,YAAa,qBAElBiE,EAAAA,EAAAA,MAAA,UACEyD,QAASA,IAAMJ,EAAa,SAC5BvD,UAAU,2IAA0IC,SAAA,EAEpJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0CACZ/D,EAAE,cAAe,uBAEpBiE,EAAAA,EAAAA,MAAA,UACEyD,QAASA,IAAMJ,EAAa,OAC5BvD,UAAU,2IAA0IC,SAAA,EAEpJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uCACZ/D,EAAE,YAAa,gCAQzBsG,IACCxC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFAAgFC,UAC7FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+FAA8FC,SAAA,EAC3GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhE,EAAE,gBAAiB,qBAEtB8D,EAAAA,EAAAA,KAAA,UACE4D,QAASA,IAAMnB,GAAe,GAC9BxC,UAAU,gFAA+EC,UAEzFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBAInBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uDAAsDC,SACjEqB,EAAaE,OAASvF,EAAE,iBAAkB,qBAE5CqF,EAAaG,cACZ1B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SAAEqB,EAAaG,eAIrE1B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,2DAA0DC,SAAA,EACzEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,KAAA,MAAAE,SACGqB,EAAaK,OAAOhB,IAAIgE,IAAQ,IAAAY,EAAA,OAC/BxF,EAAAA,EAAAA,KAAA,MAEEC,UAAU,oGAAmGC,SAEnF,QAFmFsF,EAE5GtD,EAAgB0C,UAAS,IAAAY,OAAA,EAAzBA,EAA2B7C,OAHvBiC,UAQb5E,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0EAAyEC,SACvFkC,EAAYxB,IAAI,CAAC6E,EAAK1E,KACrBf,EAAAA,EAAAA,KAAA,MAAAE,SACGqB,EAAaK,OAAOhB,IAAIgE,IACvB5E,EAAAA,EAAAA,KAAA,MAEEC,UAAU,oEAAmEC,SAE5EuF,EAAIb,IAAa,KAHbA,KAHF7D,uBC/D/B,EAlf6B2E,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACjC,MAAM,EAAEvK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdsK,EAAWC,IAAgBpK,EAAAA,EAAAA,UAAS,iBACpCqK,EAAiBC,IAAsBtK,EAAAA,EAAAA,UAAS,CAAC,aAAc,eAAgB,cAC/EuK,EAAgBC,IAAqBxK,EAAAA,EAAAA,UAAS,CAAC,IAC/Ce,EAASC,IAAchB,EAAAA,EAAAA,WAAS,IAGvCiB,EAAAA,EAAAA,WAAU,KACR,MAAMwJ,EAAW,CACfC,SAAU,CACRC,cAAe,KACfC,eAAgB,IAChBC,oBAAqB,IACrBC,kBAAmB,IACnBC,WAAY,KACZC,aAAc,IACdC,eAAgB,IAChBvJ,cAAe,MACfwJ,0BAA2B,KAC3BC,aAAc,QACdC,yBAA0B,MAE5BC,qBAAsB,CACpBC,gBAAiB,CACf,mBAAoB,CAAEC,MAAO,IAAKC,UAAW,IAAKC,KAAM,MACxD,uBAAwB,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC5D,iBAAkB,CAAEF,MAAO,IAAKC,UAAW,GAAIC,KAAM,MACrD,wBAAyB,CAAEF,MAAO,GAAIC,UAAW,GAAIC,KAAM,MAC3D,gBAAiB,CAAEF,MAAO,GAAIC,UAAW,GAAIC,KAAM,OAErDC,MAAO,CACL,OAAQ,CAAEH,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC5C,QAAS,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC7C,QAAS,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC7C,QAAS,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC7C,MAAO,CAAEF,MAAO,IAAKC,UAAW,GAAIC,KAAM,OAE5CE,YAAa,CACX,iBAAkB,CAAEJ,MAAO,IAAKC,UAAW,IAAKC,KAAM,MACtD,kBAAmB,CAAEF,MAAO,IAAKC,UAAW,GAAIC,KAAM,MACtD,kBAAmB,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MACvD,gBAAiB,CAAEF,MAAO,GAAIC,UAAW,GAAIC,KAAM,MACnD,uBAAwB,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC5D,yBAA0B,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC9D,wBAAyB,CAAEF,MAAO,IAAKC,UAAW,IAAKC,KAAM,MAC7D,MAAS,CAAEF,MAAO,IAAKC,UAAW,GAAIC,KAAM,QAGhDG,oBAAqB,CACnBC,gBAAiB,CACf,MAAO,GACP,OAAQ,IACR,QAAS,IACT,QAAS,IACT,QAAS,IACT,QAAS,IACT,QAAS,GACT,MAAO,IAETC,YAAa,CACXC,KAAM,CAAE,OAAQ,IAAK,QAAS,IAAK,QAAS,IAAK,QAAS,IAAK,MAAO,IACtEC,OAAQ,CAAE,OAAQ,IAAK,QAAS,IAAK,QAAS,IAAK,QAAS,IAAK,MAAO,KAE1EC,mBAAoB,CAClBF,KAAM,CACJ,iBAAkB,GAClB,kBAAmB,GACnB,kBAAmB,GACnB,gBAAiB,GACjB,uBAAwB,GACxB,yBAA0B,GAC1B,wBAAyB,IACzB,MAAS,IAEXC,OAAQ,CACN,iBAAkB,GAClB,kBAAmB,GACnB,kBAAmB,IACnB,gBAAiB,GACjB,uBAAwB,GACxB,yBAA0B,GAC1B,wBAAyB,IACzB,MAAS,MAIfE,eAAgB,CACdC,sBAAuB,CACrB,qBAAsB,IACtB,oBAAqB,IACrB,mBAAoB,IACpB,cAAe,IAEjBC,gBAAiB,CACf,iBAAkB,IAClB,YAAa,IACb,gBAAiB,IACjB,UAAW,IAEbC,oBAAqB,CACnB,gBAAiB,IACjB,WAAY,IACZ,WAAY,GACZ,WAAY,GACZ,gBAAiB,GAEnBC,cAAe,CACb,qBAAsB,IACtB,oBAAqB,IACrB,mBAAoB,IACpB,cAAe,KAGnBC,aAAc,CACZC,kBAAmB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAChEC,mBAAoB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACjEC,eAAgB,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,OACzGC,iBAAkB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,EAAK,IAAK,IAAK,IAAK,IAAK,KAC1EC,kBAAmB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,KAElEC,qBAAsB,CACpB,kBAAmB,CACjB/M,SAAU,GACVS,eAAgB,KAChBuM,aAAc,IACdC,mBAAoB,KACpBC,gBAAiB,IAEnB,mBAAoB,CAClBlN,SAAU,GACVS,eAAgB,KAChBuM,aAAc,IACdC,mBAAoB,KACpBC,gBAAiB,IAEnB,oBAAqB,CACnBlN,SAAU,GACVS,eAAgB,KAChBuM,aAAc,IACdC,mBAAoB,KACpBC,gBAAiB,IAEnB,mBAAoB,CAClBlN,SAAU,GACVS,eAAgB,KAChBuM,aAAc,IACdC,mBAAoB,KACpBC,gBAAiB,KAGrBC,kBAAmB,CACjBC,mBAAoB,CAClB,mBAAoB,MACpB,uBAAwB,MACxB,iBAAkB,MAClB,wBAAyB,MACzB,gBAAiB,OAEnBC,eAAgB,CACd,UAAa,QACb,gBAAiB,OACjB,WAAc,QAEhBC,gBAAiB,CACf,UAAa,KACb,gBAAiB,KACjB,WAAc,QAKpB7K,WAAW,KACTiI,EAAkBC,GAClBzJ,GAAW,IACV,MACF,CAACmJ,IAEJ,MAAMkD,EAAWrF,IAAA,IAAC,MAAE9C,EAAK,MAAEnB,EAAK,SAAEuJ,EAAQ,KAAE5G,EAAI,MAAE6G,EAAK,OAAEC,EAAM,WAAEC,GAAYzF,EAAA,OAC3EpE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDAAsDC,SAAEuB,KACrEzB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mDAAkDC,SAAEI,IAChEuJ,IACC7J,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAE2J,QAG7D7J,EAAAA,EAAAA,KAAA,OAAKC,UAAS,UAAAG,OAAY0J,EAAK,iBAAA1J,OAAgB0J,EAAK,sBAAqB5J,UACvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAG,OAAK6C,EAAI,UAAA7C,OAAS0J,EAAK,mBAAA1J,OAAkB0J,EAAK,uBAG7DC,IACC5J,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,uBAAAG,OACE,aAAf4J,EAA4B,iBACb,aAAfA,EAA4B,eAAiB,iBAC5C9J,SAAA,EACDF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,gBAAAG,OACK,aAAf4J,EAA4B,KACb,aAAfA,EAA4B,OAAS,QAAO,WAE7CD,MAEH/J,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5DhE,EAAE,iBAAkB,6BAS/B,OAAIoB,GAEA0C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAejE,EAAQ,cAAgB,gBAAiB+D,SAAA,EAEpEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7DhE,EAAE,uBAAwB,4BAE7B8D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDhE,EAAE,0BAA2B,wDAGlCiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEG,MAAOoG,EACPnG,SAAWC,GAAMmG,EAAanG,EAAE7B,OAAO2B,OACvCL,UAAU,2HAA0HC,SAAA,EAEpIF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,cAAaJ,SAAEhE,EAAE,YAAa,kBAC5C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,eAAcJ,SAAEhE,EAAE,aAAc,mBAC9C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,eAAcJ,SAAEhE,EAAE,aAAc,mBAC9C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,YAAWJ,SAAEhE,EAAE,WAAY,gBACzC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,WAAUJ,SAAEhE,EAAE,UAAW,kBAEzCiE,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kFAAiFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ/D,EAAE,aAAc,yBAMvBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,gBAAiB,kBAC1BoE,MAA8B,QAAzBqF,EAAEmB,EAAeG,gBAAQ,IAAAtB,GAAe,QAAfC,EAAvBD,EAAyBuB,qBAAa,IAAAtB,OAAf,EAAvBA,EAAwCqE,iBAC/CJ,SAAQ,GAAAzJ,OAA4B,QAA5ByF,EAAKiB,EAAeG,gBAAQ,IAAApB,OAAA,EAAvBA,EAAyBsB,eAAc,KAAA/G,OAAIlE,EAAE,SAAU,WACpE+G,KAAK,eACL6G,MAAM,OACNC,OAAO,SACPC,WAAW,cAEbhK,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,sBAAuB,wBAChCoE,MAA8B,QAAzBwF,EAAEgB,EAAeG,gBAAQ,IAAAnB,GAAqB,QAArBC,EAAvBD,EAAyBsB,2BAAmB,IAAArB,OAArB,EAAvBA,EAA8CkE,iBACrDJ,SAAQ,GAAAzJ,OAA4B,QAA5B4F,EAAKc,EAAeG,gBAAQ,IAAAjB,OAAA,EAAvBA,EAAyBqB,kBAAiB,KAAAjH,OAAIlE,EAAE,UAAW,YACxE+G,KAAK,sBACL6G,MAAM,QACNC,OAAO,QACPC,WAAW,cAEbhK,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,aAAc,eACvBoE,MAAK,GAAAF,OAA4B,QAA5B6F,EAAKa,EAAeG,gBAAQ,IAAAhB,OAAA,EAAvBA,EAAyBqB,WAAU,KAAAlH,OAAIlE,EAAE,QAAS,UAC5D2N,SAAQ,GAAAzJ,OAA4B,QAA5B8F,EAAKY,EAAeG,gBAAQ,IAAAf,OAAA,EAAvBA,EAAyBqB,aAAY,QAAAnH,OAA8B,QAA9B+F,EAAOW,EAAeG,gBAAQ,IAAAd,OAAA,EAAvBA,EAAyBqB,eAAc,KAChGvE,KAAK,uBACL6G,MAAM,SACNC,OAAO,OACPC,WAAW,cAEbhK,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,eAAgB,iBACzBoE,MAAK,GAAAF,SAA6B,QAAvBgG,EAAAU,EAAeG,gBAAQ,IAAAb,OAAA,EAAvBA,EAAyBsB,cAAe,KAASwC,QAAQ,GAAE,SACtEL,SAAQ,GAAAzJ,OAA4B,QAA5BiG,EAAKS,EAAeG,gBAAQ,IAAAZ,OAAA,EAAvBA,EAAyBsB,yBAAwB,KAAAvH,OAAIlE,EAAE,aAAc,gBAClF+G,KAAK,qBACL6G,MAAM,SACNC,OAAO,SACPC,WAAW,iBAKf7J,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,6BAA8B,yCAEnC8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBmE,OAAOC,SAA2C,QAAnCgC,EAAAQ,EAAec,4BAAoB,IAAAtB,OAAA,EAAnCA,EAAqCuB,kBAAmB,CAAC,GAAGjH,IAAI,CAAA6D,EAAe1D,KAAW,IAAxBV,EAAM8J,GAAK1F,EAC3F,MAAM2F,EAAS,CAAC,cAAe,eAAgB,gBAAiB,aAAc,iBAC9E,OACEjK,EAAAA,EAAAA,MAAA,OAAgBF,UAAU,YAAWC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAEG,KACxEF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,kDAAiDC,SAAA,CAAEiK,EAAKnC,KAAK,WAE/EhI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,oBAAAG,OAAsBgK,EAAOrJ,EAAQqJ,EAAOpJ,QAAO,gCAC5DH,MAAO,CAAEC,MAAM,GAADV,OAAK+J,EAAKnC,KAAI,WAGhC7H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CACtDiK,EAAKpC,UAAU,OAAKoC,EAAKrC,MAAM,kBAZ1BzH,WAqBlBF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,kBAAmB,uBAExB8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBmE,OAAOC,SAA0C,QAAlCiC,EAAAO,EAAeqB,2BAAmB,IAAA5B,OAAA,EAAlCA,EAAoC6B,kBAAmB,CAAC,GAAGxH,IAAI,CAAAuE,EAAoBpE,KAAW,IAADsJ,EAAA,IAA5BC,EAAUC,GAAMpF,EAC/F,MAAM2C,EAAQzD,OAAOmG,QAAyC,QAAlCH,EAAAvD,EAAeqB,2BAAmB,IAAAkC,OAAA,EAAlCA,EAAoCjC,kBAAmB,CAAC,GAAGhH,OAAO,CAACqJ,EAAKC,IAAQD,EAAMC,EAAK,GACjHC,EAAa7C,EAAQ,EAAI5G,KAAKC,MAAOoJ,EAAQzC,EAAS,KAAO,EAC7DsC,EAAS,CAAC,aAAc,cAAe,gBAAiB,eAAgB,gBAAiB,gBAAiB,cAAe,eAC/H,OACEjK,EAAAA,EAAAA,MAAA,OAAoBF,UAAU,+EAA8EC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,wBAAAG,OAA0BgK,EAAOrJ,EAAQqJ,EAAOpJ,YAC9DhB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAEoK,QAE1EnK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDC,SAAEqK,KAClEpK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CAAEyK,EAAW,YAPhEL,WAgBlBnK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,gBAAiB,qBAEtBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sCAAqCC,SAAA,EAClDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAEhE,EAAE,aAAc,oBAEtEiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAEhE,EAAE,cAAe,wBAGzE8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,SAC1C,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAAOU,IAAI,CAACgK,EAAO7J,KAAW,IAAD8J,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC1G,MAAMC,GAAwC,QAA3BN,EAAA/D,EAAegC,oBAAY,IAAA+B,GAAmB,QAAnBC,EAA3BD,EAA6B9B,yBAAiB,IAAA+B,OAAnB,EAA3BA,EAAiD/J,KAAU,EACxEqK,GAAyC,QAA3BL,EAAAjE,EAAegC,oBAAY,IAAAiC,GAAoB,QAApBC,EAA3BD,EAA6B/B,0BAAkB,IAAAgC,OAApB,EAA3BA,EAAkDjK,KAAU,EAC1EsK,EAAWnK,KAAKoK,QAAmC,QAA3BL,EAAAnE,EAAegC,oBAAY,IAAAmC,OAAA,EAA3BA,EAA6BlC,oBAAqB,OAAoC,QAA3BmC,EAAApE,EAAegC,oBAAY,IAAAoC,OAAA,EAA3BA,EAA6BlC,qBAAsB,IACtIuC,EAAkBF,EAAW,EAAKF,EAAaE,EAAY,IAAM,EACjEG,EAAmBH,EAAW,EAAKD,EAAcC,EAAY,IAAM,EACzE,OACElL,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,uCAAsCC,SAAA,EAC/DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,OACEC,UAAU,wBACVY,MAAO,CAAE4K,OAAO,GAADrL,OAAKmL,EAAe,KAAKG,UAAW,OACnDjK,MAAK,GAAArB,OAAKwK,EAAK,MAAAxK,OAAK+K,EAAU,kBAEhCnL,EAAAA,EAAAA,KAAA,OACEC,UAAU,yBACVY,MAAO,CAAE4K,OAAO,GAADrL,OAAKoL,EAAgB,KAAKE,UAAW,OACpDjK,MAAK,GAAArB,OAAKwK,EAAK,MAAAxK,OAAKgL,EAAW,sBAGnCpL,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE0K,MAbpDA,cAsBpBzK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,wBAAyB,sCAE9B8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBmE,OAAOC,SAAqC,QAA7BkC,EAAAM,EAAe2B,sBAAc,IAAAjC,OAAA,EAA7BA,EAA+BkC,wBAAyB,CAAC,GAAG9H,IAAI,CAAA+K,EAAoB5K,KAAW,IAAD6K,EAAA,IAA5BC,EAAUtB,GAAMoB,EAChG,MAAM7D,EAAQzD,OAAOmG,QAAoC,QAA7BoB,EAAA9E,EAAe2B,sBAAc,IAAAmD,OAAA,EAA7BA,EAA+BlD,wBAAyB,CAAC,GAAGtH,OAAO,CAACqJ,EAAKC,IAAQD,EAAMC,EAAK,GAClHC,EAAa7C,EAAQ,EAAI5G,KAAKC,MAAOoJ,EAAQzC,EAAS,KAAO,EAC7DsC,EAAS,CAAC,eAAgB,cAAe,gBAAiB,cAChE,OACEjK,EAAAA,EAAAA,MAAA,OAAoBF,UAAU,+EAA8EC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,wBAAAG,OAA0BgK,EAAOrJ,EAAQqJ,EAAOpJ,YAC9DhB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAE2L,QAE1E1L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDC,SAAEqK,KAClEpK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CAAEyK,EAAW,YAPhEkB,cAiBpB1L,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,uBAAwB,4BAE7B8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,2DAA0DC,SAAA,EACzEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8BAA6BC,UAC5CC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qFAAoFC,SAC/FhE,EAAE,YAAa,gBAElB8D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qFAAoFC,SAC/FhE,EAAE,WAAY,eAEjB8D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qFAAoFC,SAC/FhE,EAAE,aAAc,iBAEnB8D,EAAAA,EAAAA,KAAA,MAAIC,UAAU,qFAAoFC,SAC/FhE,EAAE,eAAgB,wBAIzB8D,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0EAAyEC,SACvFmE,OAAOC,QAAQwC,EAAesC,sBAAwB,CAAC,GAAGxI,IAAIkL,IAAA,IAAEnO,EAAMoO,GAAMD,EAAA,OAC3E3L,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gFAA+EC,SAC1FvC,KAEHqC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uEAAsEC,SACjF6L,EAAM1P,YAET8D,EAAAA,EAAAA,MAAA,MAAIF,UAAU,uEAAsEC,SAAA,CACjF6L,EAAMjP,eAAe,QAExBqD,EAAAA,EAAAA,MAAA,MAAIF,UAAU,uEAAsEC,SAAA,CACjF6L,EAAM1C,aAAa,YAXf1L,gBAqBnBwC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhE,EAAE,wBAAyB,oCAE9B8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBmE,OAAOC,SAA2C,QAAnCmC,EAAAK,EAAec,4BAAoB,IAAAnB,OAAA,EAAnCA,EAAqCyB,cAAe,CAAC,GAAGtH,IAAIoL,IAAA,IAAEnO,EAAWkO,GAAMC,EAAA,OAC7F7L,EAAAA,EAAAA,MAAA,OAAqBF,UAAU,oCAAmCC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAErC,KAC5DsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,OACEC,UAAU,+BACVY,MAAO,CAAEC,MAAM,GAADV,OAAK2L,EAAM/D,KAAI,WAGjC7H,EAAAA,EAAAA,MAAA,QAAMF,UAAU,oEAAmEC,SAAA,CAChF6L,EAAM/D,KAAK,YAVRnK,iBCpZxB,EApEkBoO,KAAO,IAADC,EACtB,MAAM,EAAEhQ,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACd+P,EAAWC,IAAgB7P,EAAAA,EAAAA,UAAS,aACpCe,EAASC,IAAchB,EAAAA,EAAAA,WAAS,GAEjC8P,EAAO,CACX,CACE3O,GAAI,WACJiF,MAAOzG,EAAE,WAAY,YACrB+G,KAAM,oBACNqJ,UAAWC,EAAAA,SAEb,CACE7O,GAAI,WACJiF,MAAOzG,EAAE,kBAAmB,oBAC5B+G,KAAM,eACNqJ,UAAWrQ,GAEb,CACEyB,GAAI,aACJiF,MAAOzG,EAAE,aAAc,cACvB+G,KAAM,mBACNqJ,UAAW5G,GAEb,CACEhI,GAAI,UACJiF,MAAOzG,EAAE,gBAAiB,kBAC1B+G,KAAM,kBACNqJ,UAAWhL,IAITkL,GAAwD,QAAtCN,EAAAG,EAAKI,KAAKC,GAAOA,EAAIhP,KAAOyO,UAAU,IAAAD,OAAA,EAAtCA,EAAwCI,YAAaC,EAAAA,QAE7E,OACEvM,EAAAA,EAAAA,KAAA,OAAKC,UAAS,4CAAAG,OAA8CjE,EAAQ,cAAgB,gBAAiB+D,UACnGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SACnCmM,EAAKzL,IAAK8L,IACTvM,EAAAA,EAAAA,MAAA,UAEEyD,QAASA,IAAMwI,EAAaM,EAAIhP,IAChCuC,UAAS,wEAAAG,OACP+L,IAAcO,EAAIhP,GACd,mDACA,0HACHwC,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAWyM,EAAIzJ,QAClBjD,EAAAA,EAAAA,KAAA,QAAAE,SAAOwM,EAAI/J,UATN+J,EAAIhP,YAiBnBsC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,KAACwM,EAAe,W,gFC/C1B,MAsXA,EAtX0BD,KAAO,IAADI,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC9B,MAAM,EAAEjR,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdgR,EAAgBC,IAAqB9Q,EAAAA,EAAAA,UAAS,iBAC9C+Q,EAAgBC,IAAqBhR,EAAAA,EAAAA,UAAS,QAC9CiR,EAAeC,IAAoBlR,EAAAA,EAAAA,UAAS,CAAC,IAC7Ce,EAASC,IAAchB,EAAAA,EAAAA,WAAS,IAGvCiB,EAAAA,EAAAA,WAAU,KACR,MAAMwJ,EAAW,CACfC,SAAU,CACRC,cAAe,KACfC,eAAgB,IAChBC,oBAAqB,IACrBC,kBAAmB,IACnBqG,sBAAuB,KACvBC,yBAA0B,GAC1B1P,cAAe,MACf2P,kBAAmB,MACnBC,kBAAmB,KACnBC,eAAgB,MAElBC,gBAAiB,CACfhG,UAAW,IACXiG,WAAY,IACZC,OAAQ,GACRC,aAAc,GACdC,WAAY,KAEdC,oBAAqB,CACnBC,gBAAiB,CAAEtG,UAAW,IAAKD,MAAO,IAAKE,KAAM,MACrDsG,oBAAqB,CAAEvG,UAAW,IAAKD,MAAO,IAAKE,KAAM,MACzDuG,cAAe,CAAExG,UAAW,GAAID,MAAO,IAAKE,KAAM,MAClDwG,aAAc,CAAEzG,UAAW,GAAID,MAAO,GAAIE,KAAM,MAChDyG,aAAc,CAAE1G,UAAW,GAAID,MAAO,GAAIE,KAAM,OAElD0G,aAAc,CACZC,UAAW,CACT,OAAQ,IACR,QAAS,IACT,QAAS,IACT,QAAS,IACT,MAAO,KAETC,OAAQ,CACNtG,KAAM,IACNC,OAAQ,KAEVsG,WAAY,CACV,iBAAkB,IAClB,kBAAmB,IACnB,kBAAmB,IACnB,gBAAiB,GACjB,uBAAwB,IACxB,yBAA0B,IAC1B,wBAAyB,IACzB,MAAS,MAGbpG,eAAgB,CACdC,sBAAuB,CACrBoG,YAAa,GACbC,SAAU,IACVC,QAAS,GACTC,KAAM,IAERtG,gBAAiB,CACfuG,SAAU,GACVC,SAAU,IACVC,kBAAmB,IACnBC,YAAa,IAEfC,mBAAoB,CAClBC,UAAW,IACXC,KAAM,IACNC,KAAM,GACNC,KAAM,KAGV5G,aAAc,CACZ6G,gBAAiB,IACjBC,uBAAwB,GACxBC,uBAAwB,GACxBC,uBAAwB,MAE1BC,iBAAkB,CAChBrI,aAAc,QACdC,yBAA0B,KAC1BqI,kBAAmB,KACnBC,oBAAqB,KACrBC,eAAgB,MAElBC,iBAAkB,CAChBC,WAAY,CACV,CAAEzS,KAAM,kBAAmBtB,SAAU,GAAIS,eAAgB,KAAMuM,aAAc,KAC7E,CAAE1L,KAAM,mBAAoBtB,SAAU,GAAIS,eAAgB,KAAMuM,aAAc,KAC9E,CAAE1L,KAAM,oBAAqBtB,SAAU,GAAIS,eAAgB,KAAMuM,aAAc,KAC/E,CAAE1L,KAAM,mBAAoBtB,SAAU,GAAIS,eAAgB,KAAMuM,aAAc,OAGlFgH,OAAQ,CACNtH,kBAAmB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAChEC,mBAAoB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACjEsH,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,OAI/ExR,WAAW,KACT2O,EAAiBzG,GACjBzJ,GAAW,IACV,MACF,CAAC6P,IAEJ,MAAMxD,EAAWrF,IAAA,IAAC,MAAE9C,EAAK,MAAEnB,EAAK,SAAEuJ,EAAQ,KAAE5G,EAAI,MAAE6G,EAAK,MAAEyG,EAAK,WAAE5F,GAAYpG,EAAA,OAC1EpE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDAAsDC,SAAEuB,KACrEzB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mDAAkDC,SAAEI,IAChEuJ,IACC7J,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAE2J,QAG7D7J,EAAAA,EAAAA,KAAA,OAAKC,UAAS,UAAAG,OAAY0J,EAAK,iBAAA1J,OAAgB0J,EAAK,sBAAqB5J,UACvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAG,OAAK6C,EAAI,UAAA7C,OAAS0J,EAAK,mBAAA1J,OAAkB0J,EAAK,uBAG7DyG,IACCpQ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,uBAAAG,OACH,OAAVmQ,EAAiB,iBAA6B,SAAVA,EAAmB,eAAiB,iBACvErQ,SAAA,EACDF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,gBAAAG,OAA4B,OAAVmQ,EAAiB,KAAiB,SAAVA,EAAmB,OAAS,QAAO,WACxF5F,EAAW,QAEd3K,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5DhE,EAAE,iBAAkB,6BAOzBsU,EAAkBA,KACtB,IAAKhD,EAAcY,oBACjB,OAAOpO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,SAAC,0BAG9E,MAAMkK,EAAS,CAAC,cAAe,eAAgB,gBAAiB,aAAc,iBAE9E,OACEjK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhE,EAAE,2BAA4B,wCAEhCmI,OAAOC,QAAQkJ,EAAcY,qBAAqBxN,IAAI,CAAA6D,EAAe1D,KAAK,IAAlBV,EAAM8J,GAAK1F,EAAA,OAClEtE,EAAAA,EAAAA,MAAA,OAAgBF,UAAU,YAAWC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAEG,KACxEF,EAAAA,EAAAA,MAAA,QAAMF,UAAU,kDAAiDC,SAAA,CAAEiK,EAAKnC,KAAK,WAE/EhI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,oBAAAG,OAAsBgK,EAAOrJ,EAAQqJ,EAAOpJ,QAAO,gCAC5DH,MAAO,CAAEC,MAAM,GAADV,OAAK+J,EAAKnC,KAAI,WAGhC7H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CACtDiK,EAAKpC,UAAU,OAAKoC,EAAKrC,MAAM,kBAZ1BzH,SAoBZoQ,EAAqBA,KACzB,IAAKjD,EAAcO,gBACjB,OAAO/N,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,SAAC,0BAG9E,MAAMwQ,EAAS,CACbxU,EAAE,YAAa,aACfA,EAAE,aAAc,eAChBA,EAAE,SAAU,WACZA,EAAE,eAAgB,gBAClBA,EAAE,aAAc,gBAGZsO,EAASnG,OAAOmG,OAAOgD,EAAcO,iBACrCjG,EAAQ0C,EAAOpJ,OAAO,CAACqJ,EAAKC,IAAQD,EAAMC,EAAK,GAC/CN,EAAS,CAAC,iBAAkB,gBAAiB,kBAAmB,eAAgB,iBAChFuG,EAAW,CAAC,eAAgB,cAAe,gBAAiB,aAAc,eAEhF,OACExQ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhE,EAAE,8BAA+B,oCAEpC8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yBAAwBC,SACpCwQ,EAAO9P,IAAI,CAAC+B,EAAO5B,KAClB,MAAMT,EAAQkK,EAAOzJ,GACf4J,EAAa7C,EAAQ,EAAI5G,KAAKC,MAAOb,EAAQwH,EAAS,KAAO,EACnE,OACE3H,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,+EAA8EC,SAAA,EACvGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,wBAAAG,OAA0BuQ,EAAS5P,OACjDf,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAEyC,QAE1ExC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,qBAAAG,OAAuBgK,EAAOrJ,IAASb,SAAEI,KACvDH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CAAEyK,EAAW,YAPhEhI,WAiBhBiO,EAAcA,KAClB,IAAKpD,EAAc6C,OACjB,OAAOrQ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,SAAC,0BAG9E,MACMiL,EAAaqC,EAAc6C,OAAOtH,mBAAqB,GACvDqC,EAAcoC,EAAc6C,OAAOrH,oBAAsB,GACzDqC,EAAWnK,KAAKoK,OAAOH,KAAeC,GAE5C,OACEjL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhE,EAAE,gBAAiB,qBAEtBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sCAAqCC,SAAA,EAClDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAEhE,EAAE,aAAc,oBAEtEiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kCACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAEhE,EAAE,cAAe,wBAGzE8D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,SArBpC,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAsB7EU,IAAI,CAACgK,EAAO7J,KAClB,MAAMwK,EAAkBF,EAAW,EAAKF,EAAWpK,GAASsK,EAAY,IAAM,EACxEG,EAAmBH,EAAW,EAAKD,EAAYrK,GAASsK,EAAY,IAAM,EAChF,OACElL,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,uCAAsCC,SAAA,EAC/DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,OACEC,UAAU,wBACVY,MAAO,CAAE4K,OAAO,GAADrL,OAAKmL,EAAe,KAAKG,UAAW,OACnDjK,MAAK,GAAArB,OAAKwK,EAAK,MAAAxK,OAAK+K,EAAWpK,GAAM,kBAEvCf,EAAAA,EAAAA,KAAA,OACEC,UAAU,yBACVY,MAAO,CAAE4K,OAAO,GAADrL,OAAKoL,EAAgB,KAAKE,UAAW,OACpDjK,MAAK,GAAArB,OAAKwK,EAAK,MAAAxK,OAAKgL,EAAYrK,GAAM,sBAG1Cf,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kFAAiFC,SAC9F0K,MAdKA,cAyBxB,OAAItN,GAEA0C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAS,aAAAG,OAAejE,EAAQ,cAAgB,gBAAiB+D,SAAA,EAEpEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7DhE,EAAE,oBAAqB,qCAE1B8D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDhE,EAAE,iCAAkC,kEAGzCiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEG,MAAO8M,EACP7M,SAAWC,GAAM6M,EAAkB7M,EAAE7B,OAAO2B,OAC5CL,UAAU,2HAA0HC,SAAA,EAEpIF,EAAAA,EAAAA,KAAA,UAAQM,MAAM,cAAaJ,SAAEhE,EAAE,YAAa,kBAC5C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,eAAcJ,SAAEhE,EAAE,aAAc,mBAC9C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,eAAcJ,SAAEhE,EAAE,aAAc,mBAC9C8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,YAAWJ,SAAEhE,EAAE,WAAY,gBACzC8D,EAAAA,EAAAA,KAAA,UAAQM,MAAM,WAAUJ,SAAEhE,EAAE,UAAW,kBAEzCiE,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kFAAiFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ/D,EAAE,eAAgB,2BAMzBiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,gBAAiB,kBAC1BoE,MAA6B,QAAxBqM,EAAEa,EAAcvG,gBAAQ,IAAA0F,GAAe,QAAfC,EAAtBD,EAAwBzF,qBAAa,IAAA0F,OAAf,EAAtBA,EAAuC3C,iBAC9CJ,SAAQ,GAAAzJ,OAA2B,QAA3ByM,EAAKW,EAAcvG,gBAAQ,IAAA4F,OAAA,EAAtBA,EAAwB1F,eAAc,KAAA/G,OAAIlE,EAAE,SAAU,WACnE+G,KAAK,eACL6G,MAAM,OACNyG,MAAM,KACN5F,WAAW,UAEb3K,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,iBAAkB,mBAC3BoE,MAAK,GAAAF,OAA2B,QAA3B0M,EAAKU,EAAcvG,gBAAQ,IAAA6F,OAAA,EAAtBA,EAAwBY,sBAAqB,KACvD7D,SAAQ,GAAAzJ,OAA2B,QAA3B2M,EAAKS,EAAcvG,gBAAQ,IAAA8F,OAAA,EAAtBA,EAAwB3F,oBAAmB,KAAAhH,OAAIlE,EAAE,YAAa,cAC3E+G,KAAK,sBACL6G,MAAM,QACNyG,MAAM,KACN5F,WAAW,SAEb3K,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,kBAAmB,oBAC5BoE,MAAK,GAAAF,OAA2B,QAA3B4M,EAAKQ,EAAcvG,gBAAQ,IAAA+F,OAAA,EAAtBA,EAAwBW,yBAAwB,KAAAvN,OAAIlE,EAAE,OAAQ,SACxE2N,SAAU3N,EAAE,kBAAmB,oBAC/B+G,KAAK,eACL6G,MAAM,SACNyG,MAAM,OACN5F,WAAW,SAEb3K,EAAAA,EAAAA,KAAC4J,EAAQ,CACPnI,MAAOvF,EAAE,gBAAiB,kBAC1BoE,MAA6B,QAAxB2M,EAAEO,EAAcvG,gBAAQ,IAAAgG,GAAe,QAAfC,EAAtBD,EAAwBhP,qBAAa,IAAAiP,OAAf,EAAtBA,EAAuCjD,iBAC9CJ,SAAQ,GAAAzJ,OAA2B,QAA3B+M,EAAKK,EAAcvG,gBAAQ,IAAAkG,OAAA,EAAtBA,EAAwBS,kBAAiB,KAAAxN,OAAIlE,EAAE,YAAa,cACzE+G,KAAK,wBACL6G,MAAM,SACNyG,MAAM,KACN5F,WAAW,YAKfxK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,KAACwQ,EAAe,OAElBxQ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,KAACyQ,EAAkB,UAKvBzQ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,KAAC4Q,EAAW,S", "sources": ["components/Analytics/PatientProgressTracker.jsx", "components/Reports/AdvancedReportBuilder.jsx", "components/Analytics/StatisticalDashboard.jsx", "pages/Analytics/Analytics.jsx", "pages/Analytics/AdvancedAnalytics.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst PatientProgressTracker = () => {\n  const { t, isRTL } = useLanguage();\n  const [patients, setPatients] = useState([]);\n  const [filteredPatients, setFilteredPatients] = useState([]);\n  const [filters, setFilters] = useState({\n    status: 'all',\n    treatmentType: 'all',\n    completionRate: 'all',\n    dateRange: 'all'\n  });\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState('name');\n  const [sortOrder, setSortOrder] = useState('asc');\n  const [loading, setLoading] = useState(true);\n\n  // Mock patient progress data\n  useEffect(() => {\n    const mockPatients = [\n      {\n        id: 1,\n        name: '<PERSON>',\n        age: 28,\n        condition: 'Spinal Injury',\n        treatmentType: 'Physical Therapy',\n        status: 'In Progress',\n        startDate: '2024-01-15',\n        expectedEndDate: '2024-03-15',\n        completionRate: 65,\n        sessionsCompleted: 13,\n        totalSessions: 20,\n        lastSession: '2024-02-08',\n        nextSession: '2024-02-10',\n        therapist: 'Dr. <PERSON> <PERSON>',\n        goals: [\n          { goal: 'Improve mobility', progress: 70, status: 'On Track' },\n          { goal: 'Reduce pain', progress: 80, status: 'Ahead' },\n          { goal: 'Increase strength', progress: 55, status: 'Behind' }\n        ],\n        functionalScores: {\n          initial: 45,\n          current: 68,\n          target: 85\n        },\n        notes: 'Patient showing good progress in mobility exercises'\n      },\n      {\n        id: 2,\n        name: 'Fatima Hassan',\n        age: 34,\n        condition: 'Stroke Recovery',\n        treatmentType: 'Occupational Therapy',\n        status: 'Completed',\n        startDate: '2023-11-01',\n        endDate: '2024-01-30',\n        completionRate: 100,\n        sessionsCompleted: 24,\n        totalSessions: 24,\n        lastSession: '2024-01-30',\n        therapist: 'Dr. Mohammed Ali',\n        goals: [\n          { goal: 'Regain fine motor skills', progress: 95, status: 'Achieved' },\n          { goal: 'Improve coordination', progress: 90, status: 'Achieved' },\n          { goal: 'Daily living activities', progress: 88, status: 'Achieved' }\n        ],\n        functionalScores: {\n          initial: 32,\n          current: 82,\n          target: 80\n        },\n        notes: 'Successfully completed treatment with excellent outcomes'\n      },\n      {\n        id: 3,\n        name: 'Omar Khalil',\n        age: 12,\n        condition: 'Autism Spectrum',\n        treatmentType: 'Speech Therapy',\n        status: 'In Progress',\n        startDate: '2024-01-08',\n        expectedEndDate: '2024-04-08',\n        completionRate: 45,\n        sessionsCompleted: 9,\n        totalSessions: 20,\n        lastSession: '2024-02-05',\n        nextSession: '2024-02-12',\n        therapist: 'Dr. Fatima Hassan',\n        goals: [\n          { goal: 'Improve communication', progress: 50, status: 'On Track' },\n          { goal: 'Social interaction', progress: 40, status: 'Behind' },\n          { goal: 'Vocabulary expansion', progress: 60, status: 'Ahead' }\n        ],\n        functionalScores: {\n          initial: 28,\n          current: 42,\n          target: 70\n        },\n        notes: 'Good progress in vocabulary, needs more work on social skills'\n      },\n      {\n        id: 4,\n        name: 'Layla Abdullah',\n        age: 45,\n        condition: 'Cerebral Palsy',\n        treatmentType: 'Physical Therapy',\n        status: 'On Hold',\n        startDate: '2023-12-01',\n        expectedEndDate: '2024-03-01',\n        completionRate: 30,\n        sessionsCompleted: 6,\n        totalSessions: 20,\n        lastSession: '2024-01-15',\n        therapist: 'Dr. Ahmed Khalil',\n        goals: [\n          { goal: 'Improve balance', progress: 35, status: 'Behind' },\n          { goal: 'Strengthen muscles', progress: 25, status: 'Behind' },\n          { goal: 'Enhance mobility', progress: 30, status: 'Behind' }\n        ],\n        functionalScores: {\n          initial: 38,\n          current: 45,\n          target: 75\n        },\n        notes: 'Treatment on hold due to medical complications'\n      },\n      {\n        id: 5,\n        name: 'Yusuf Ibrahim',\n        age: 67,\n        condition: 'Neurological Disorder',\n        treatmentType: 'Occupational Therapy',\n        status: 'Discontinued',\n        startDate: '2023-10-15',\n        endDate: '2023-12-20',\n        completionRate: 40,\n        sessionsCompleted: 8,\n        totalSessions: 20,\n        lastSession: '2023-12-20',\n        therapist: 'Dr. Sarah Ahmed',\n        goals: [\n          { goal: 'Cognitive improvement', progress: 25, status: 'Not Achieved' },\n          { goal: 'Memory enhancement', progress: 30, status: 'Not Achieved' },\n          { goal: 'Daily activities', progress: 45, status: 'Partial' }\n        ],\n        functionalScores: {\n          initial: 35,\n          current: 42,\n          target: 70\n        },\n        notes: 'Treatment discontinued due to patient request'\n      }\n    ];\n\n    setTimeout(() => {\n      setPatients(mockPatients);\n      setFilteredPatients(mockPatients);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  // Filter and search logic\n  useEffect(() => {\n    let filtered = patients.filter(patient => {\n      const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           patient.condition.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           patient.therapist.toLowerCase().includes(searchTerm.toLowerCase());\n      \n      const matchesStatus = filters.status === 'all' || patient.status === filters.status;\n      const matchesTreatment = filters.treatmentType === 'all' || patient.treatmentType === filters.treatmentType;\n      \n      let matchesCompletion = true;\n      if (filters.completionRate === 'high') matchesCompletion = patient.completionRate >= 80;\n      else if (filters.completionRate === 'medium') matchesCompletion = patient.completionRate >= 50 && patient.completionRate < 80;\n      else if (filters.completionRate === 'low') matchesCompletion = patient.completionRate < 50;\n\n      return matchesSearch && matchesStatus && matchesTreatment && matchesCompletion;\n    });\n\n    // Sort logic\n    filtered.sort((a, b) => {\n      let aValue = a[sortBy];\n      let bValue = b[sortBy];\n      \n      if (sortBy === 'completionRate') {\n        aValue = parseFloat(aValue);\n        bValue = parseFloat(bValue);\n      }\n      \n      if (sortOrder === 'asc') {\n        return aValue > bValue ? 1 : -1;\n      } else {\n        return aValue < bValue ? 1 : -1;\n      }\n    });\n\n    setFilteredPatients(filtered);\n  }, [patients, searchTerm, filters, sortBy, sortOrder]);\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'Completed': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';\n      case 'In Progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';\n      case 'On Hold': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';\n      case 'Discontinued': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';\n      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';\n    }\n  };\n\n  const getProgressColor = (progress) => {\n    if (progress >= 80) return 'bg-green-500';\n    if (progress >= 60) return 'bg-blue-500';\n    if (progress >= 40) return 'bg-yellow-500';\n    return 'bg-red-500';\n  };\n\n  const getGoalStatusColor = (status) => {\n    switch (status) {\n      case 'Achieved': return 'text-green-600';\n      case 'Ahead': return 'text-blue-600';\n      case 'On Track': return 'text-green-600';\n      case 'Behind': return 'text-yellow-600';\n      case 'Not Achieved': return 'text-red-600';\n      case 'Partial': return 'text-orange-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-64\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Filters and Search */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('search', 'Search')}\n            </label>\n            <input\n              type=\"text\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              placeholder={t('searchPatients', 'Search patients...')}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('status', 'Status')}\n            </label>\n            <select\n              value={filters.status}\n              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n              <option value=\"In Progress\">{t('inProgress', 'In Progress')}</option>\n              <option value=\"Completed\">{t('completed', 'Completed')}</option>\n              <option value=\"On Hold\">{t('onHold', 'On Hold')}</option>\n              <option value=\"Discontinued\">{t('discontinued', 'Discontinued')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('treatmentType', 'Treatment Type')}\n            </label>\n            <select\n              value={filters.treatmentType}\n              onChange={(e) => setFilters(prev => ({ ...prev, treatmentType: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">{t('allTypes', 'All Types')}</option>\n              <option value=\"Physical Therapy\">{t('physicalTherapy', 'Physical Therapy')}</option>\n              <option value=\"Occupational Therapy\">{t('occupationalTherapy', 'Occupational Therapy')}</option>\n              <option value=\"Speech Therapy\">{t('speechTherapy', 'Speech Therapy')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('completionRate', 'Completion Rate')}\n            </label>\n            <select\n              value={filters.completionRate}\n              onChange={(e) => setFilters(prev => ({ ...prev, completionRate: e.target.value }))}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">{t('allRates', 'All Rates')}</option>\n              <option value=\"high\">{t('high', 'High (80%+)')}</option>\n              <option value=\"medium\">{t('medium', 'Medium (50-79%)')}</option>\n              <option value=\"low\">{t('low', 'Low (<50%)')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('sortBy', 'Sort By')}\n            </label>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"name\">{t('name', 'Name')}</option>\n              <option value=\"completionRate\">{t('completionRate', 'Completion Rate')}</option>\n              <option value=\"startDate\">{t('startDate', 'Start Date')}</option>\n              <option value=\"status\">{t('status', 'Status')}</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Patient Progress Cards */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {filteredPatients.map(patient => (\n          <div key={patient.id} className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            {/* Patient Header */}\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{patient.name}</h3>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {patient.age} {t('years', 'years')} • {patient.condition}\n                </p>\n              </div>\n              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(patient.status)}`}>\n                {patient.status}\n              </span>\n            </div>\n\n            {/* Progress Bar */}\n            <div className=\"mb-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {t('overallProgress', 'Overall Progress')}\n                </span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {patient.completionRate}%\n                </span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div \n                  className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(patient.completionRate)}`}\n                  style={{ width: `${patient.completionRate}%` }}\n                ></div>\n              </div>\n            </div>\n\n            {/* Treatment Info */}\n            <div className=\"grid grid-cols-2 gap-4 mb-4 text-sm\">\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('treatmentType', 'Treatment')}:</span>\n                <p className=\"font-medium text-gray-900 dark:text-white\">{patient.treatmentType}</p>\n              </div>\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('therapist', 'Therapist')}:</span>\n                <p className=\"font-medium text-gray-900 dark:text-white\">{patient.therapist}</p>\n              </div>\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('sessions', 'Sessions')}:</span>\n                <p className=\"font-medium text-gray-900 dark:text-white\">\n                  {patient.sessionsCompleted}/{patient.totalSessions}\n                </p>\n              </div>\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('nextSession', 'Next Session')}:</span>\n                <p className=\"font-medium text-gray-900 dark:text-white\">\n                  {patient.nextSession || t('notScheduled', 'Not Scheduled')}\n                </p>\n              </div>\n            </div>\n\n            {/* Goals Progress */}\n            <div className=\"mb-4\">\n              <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('goals', 'Goals')}\n              </h4>\n              <div className=\"space-y-2\">\n                {patient.goals.map((goal, index) => (\n                  <div key={index} className=\"flex items-center justify-between\">\n                    <span className=\"text-xs text-gray-600 dark:text-gray-400 flex-1\">\n                      {goal.goal}\n                    </span>\n                    <span className={`text-xs font-medium ${getGoalStatusColor(goal.status)} ml-2`}>\n                      {goal.progress}%\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Functional Scores */}\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n              <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('functionalScores', 'Functional Scores')}\n              </h4>\n              <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                <div className=\"text-center\">\n                  <div className=\"font-medium text-gray-900 dark:text-white\">{patient.functionalScores.initial}</div>\n                  <div className=\"text-gray-600 dark:text-gray-400\">{t('initial', 'Initial')}</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"font-medium text-blue-600\">{patient.functionalScores.current}</div>\n                  <div className=\"text-gray-600 dark:text-gray-400\">{t('current', 'Current')}</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"font-medium text-green-600\">{patient.functionalScores.target}</div>\n                  <div className=\"text-gray-600 dark:text-gray-400\">{t('target', 'Target')}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Summary Stats */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('summaryStatistics', 'Summary Statistics')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">{filteredPatients.length}</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('totalPatients', 'Total Patients')}</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-green-600\">\n              {filteredPatients.filter(p => p.status === 'Completed').length}\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('completed', 'Completed')}</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-yellow-600\">\n              {filteredPatients.filter(p => p.status === 'In Progress').length}\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('inProgress', 'In Progress')}</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-gray-600\">\n              {Math.round(filteredPatients.reduce((acc, p) => acc + p.completionRate, 0) / filteredPatients.length)}%\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('averageCompletion', 'Average Completion')}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientProgressTracker;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst AdvancedReportBuilder = () => {\n  const { t, isRTL } = useLanguage();\n  const [reportConfig, setReportConfig] = useState({\n    title: '',\n    description: '',\n    type: 'table',\n    dataSource: 'patients',\n    fields: [],\n    filters: [],\n    groupBy: '',\n    sortBy: '',\n    sortOrder: 'asc',\n    dateRange: {\n      start: '',\n      end: ''\n    },\n    chartType: 'bar',\n    layout: 'portrait'\n  });\n  \n  const [availableFields, setAvailableFields] = useState({});\n  const [previewData, setPreviewData] = useState([]);\n  const [savedReports, setSavedReports] = useState([]);\n  const [showPreview, setShowPreview] = useState(false);\n\n  // Available data sources and their fields\n  const dataSources = {\n    patients: {\n      label: t('patients', 'Patients'),\n      fields: {\n        'patient.name': { label: t('patientName', 'Patient Name'), type: 'text' },\n        'patient.age': { label: t('age', 'Age'), type: 'number' },\n        'patient.gender': { label: t('gender', 'Gender'), type: 'text' },\n        'patient.condition': { label: t('condition', 'Condition'), type: 'text' },\n        'patient.registrationDate': { label: t('registrationDate', 'Registration Date'), type: 'date' },\n        'patient.status': { label: t('status', 'Status'), type: 'text' },\n        'patient.therapist': { label: t('assignedTherapist', 'Assigned Therapist'), type: 'text' },\n        'patient.insurance': { label: t('insurance', 'Insurance'), type: 'text' },\n        'patient.emergencyContact': { label: t('emergencyContact', 'Emergency Contact'), type: 'text' }\n      }\n    },\n    treatments: {\n      label: t('treatments', 'Treatments'),\n      fields: {\n        'treatment.type': { label: t('treatmentType', 'Treatment Type'), type: 'text' },\n        'treatment.startDate': { label: t('startDate', 'Start Date'), type: 'date' },\n        'treatment.endDate': { label: t('endDate', 'End Date'), type: 'date' },\n        'treatment.status': { label: t('treatmentStatus', 'Treatment Status'), type: 'text' },\n        'treatment.completionRate': { label: t('completionRate', 'Completion Rate'), type: 'number' },\n        'treatment.sessionsCompleted': { label: t('sessionsCompleted', 'Sessions Completed'), type: 'number' },\n        'treatment.totalSessions': { label: t('totalSessions', 'Total Sessions'), type: 'number' },\n        'treatment.duration': { label: t('duration', 'Duration (days)'), type: 'number' },\n        'treatment.cost': { label: t('treatmentCost', 'Treatment Cost'), type: 'currency' }\n      }\n    },\n    sessions: {\n      label: t('sessions', 'Sessions'),\n      fields: {\n        'session.date': { label: t('sessionDate', 'Session Date'), type: 'date' },\n        'session.duration': { label: t('sessionDuration', 'Session Duration'), type: 'number' },\n        'session.type': { label: t('sessionType', 'Session Type'), type: 'text' },\n        'session.therapist': { label: t('therapist', 'Therapist'), type: 'text' },\n        'session.status': { label: t('sessionStatus', 'Session Status'), type: 'text' },\n        'session.notes': { label: t('sessionNotes', 'Session Notes'), type: 'text' },\n        'session.attendance': { label: t('attendance', 'Attendance'), type: 'text' },\n        'session.progress': { label: t('progressRating', 'Progress Rating'), type: 'number' }\n      }\n    },\n    financial: {\n      label: t('financial', 'Financial'),\n      fields: {\n        'financial.totalRevenue': { label: t('totalRevenue', 'Total Revenue'), type: 'currency' },\n        'financial.insurancePayments': { label: t('insurancePayments', 'Insurance Payments'), type: 'currency' },\n        'financial.outOfPocketPayments': { label: t('outOfPocketPayments', 'Out of Pocket Payments'), type: 'currency' },\n        'financial.pendingPayments': { label: t('pendingPayments', 'Pending Payments'), type: 'currency' },\n        'financial.paymentDate': { label: t('paymentDate', 'Payment Date'), type: 'date' },\n        'financial.paymentMethod': { label: t('paymentMethod', 'Payment Method'), type: 'text' }\n      }\n    },\n    outcomes: {\n      label: t('outcomes', 'Outcomes'),\n      fields: {\n        'outcome.functionalScore': { label: t('functionalScore', 'Functional Score'), type: 'number' },\n        'outcome.painLevel': { label: t('painLevel', 'Pain Level'), type: 'number' },\n        'outcome.mobilityScore': { label: t('mobilityScore', 'Mobility Score'), type: 'number' },\n        'outcome.qualityOfLife': { label: t('qualityOfLife', 'Quality of Life'), type: 'number' },\n        'outcome.goalAchievement': { label: t('goalAchievement', 'Goal Achievement'), type: 'percentage' },\n        'outcome.satisfactionScore': { label: t('satisfactionScore', 'Satisfaction Score'), type: 'number' },\n        'outcome.assessmentDate': { label: t('assessmentDate', 'Assessment Date'), type: 'date' }\n      }\n    }\n  };\n\n  const reportTypes = [\n    { value: 'table', label: t('table', 'Table'), icon: 'fas fa-table' },\n    { value: 'chart', label: t('chart', 'Chart'), icon: 'fas fa-chart-bar' },\n    { value: 'summary', label: t('summary', 'Summary'), icon: 'fas fa-list' },\n    { value: 'dashboard', label: t('dashboard', 'Dashboard'), icon: 'fas fa-tachometer-alt' }\n  ];\n\n  const chartTypes = [\n    { value: 'bar', label: t('barChart', 'Bar Chart') },\n    { value: 'line', label: t('lineChart', 'Line Chart') },\n    { value: 'pie', label: t('pieChart', 'Pie Chart') },\n    { value: 'doughnut', label: t('doughnutChart', 'Doughnut Chart') },\n    { value: 'area', label: t('areaChart', 'Area Chart') }\n  ];\n\n  useEffect(() => {\n    setAvailableFields(dataSources[reportConfig.dataSource]?.fields || {});\n  }, [reportConfig.dataSource]);\n\n  // Mock saved reports\n  useEffect(() => {\n    const mockSavedReports = [\n      {\n        id: 1,\n        title: 'Patient Completion Report',\n        description: 'Treatment completion rates by therapy type',\n        type: 'chart',\n        dataSource: 'treatments',\n        createdDate: '2024-02-01',\n        lastModified: '2024-02-08'\n      },\n      {\n        id: 2,\n        title: 'Monthly Revenue Analysis',\n        description: 'Financial performance and payment tracking',\n        type: 'dashboard',\n        dataSource: 'financial',\n        createdDate: '2024-01-15',\n        lastModified: '2024-02-05'\n      },\n      {\n        id: 3,\n        title: 'Patient Demographics',\n        description: 'Age, gender, and condition distribution',\n        type: 'table',\n        dataSource: 'patients',\n        createdDate: '2024-01-20',\n        lastModified: '2024-02-03'\n      }\n    ];\n    setSavedReports(mockSavedReports);\n  }, []);\n\n  const addField = (fieldKey) => {\n    if (!reportConfig.fields.includes(fieldKey)) {\n      setReportConfig(prev => ({\n        ...prev,\n        fields: [...prev.fields, fieldKey]\n      }));\n    }\n  };\n\n  const removeField = (fieldKey) => {\n    setReportConfig(prev => ({\n      ...prev,\n      fields: prev.fields.filter(f => f !== fieldKey)\n    }));\n  };\n\n  const addFilter = () => {\n    setReportConfig(prev => ({\n      ...prev,\n      filters: [...prev.filters, { field: '', operator: 'equals', value: '' }]\n    }));\n  };\n\n  const updateFilter = (index, key, value) => {\n    setReportConfig(prev => ({\n      ...prev,\n      filters: prev.filters.map((filter, i) => \n        i === index ? { ...filter, [key]: value } : filter\n      )\n    }));\n  };\n\n  const removeFilter = (index) => {\n    setReportConfig(prev => ({\n      ...prev,\n      filters: prev.filters.filter((_, i) => i !== index)\n    }));\n  };\n\n  const generatePreview = () => {\n    // Mock preview data generation\n    const mockData = [\n      { 'patient.name': 'Ahmed Mohammed', 'patient.age': 28, 'treatment.completionRate': 65 },\n      { 'patient.name': 'Fatima Hassan', 'patient.age': 34, 'treatment.completionRate': 100 },\n      { 'patient.name': 'Omar Khalil', 'patient.age': 12, 'treatment.completionRate': 45 },\n      { 'patient.name': 'Layla Abdullah', 'patient.age': 45, 'treatment.completionRate': 30 },\n      { 'patient.name': 'Yusuf Ibrahim', 'patient.age': 67, 'treatment.completionRate': 40 }\n    ];\n    setPreviewData(mockData);\n    setShowPreview(true);\n  };\n\n  const saveReport = () => {\n    const newReport = {\n      id: Date.now(),\n      ...reportConfig,\n      createdDate: new Date().toISOString().split('T')[0],\n      lastModified: new Date().toISOString().split('T')[0]\n    };\n    setSavedReports(prev => [newReport, ...prev]);\n    alert(t('reportSaved', 'Report saved successfully!'));\n  };\n\n  const exportReport = (format) => {\n    alert(t('exportingReport', `Exporting report as ${format.toUpperCase()}...`));\n  };\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('advancedReportBuilder', 'Advanced Report Builder')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('createCustomReports', 'Create custom reports with drag-and-drop functionality')}\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={generatePreview}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <i className=\"fas fa-eye mr-2\"></i>\n            {t('preview', 'Preview')}\n          </button>\n          <button\n            onClick={saveReport}\n            className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n          >\n            <i className=\"fas fa-save mr-2\"></i>\n            {t('saveReport', 'Save Report')}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Report Configuration */}\n        <div className=\"lg:col-span-2 space-y-6\">\n          {/* Basic Information */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('basicInformation', 'Basic Information')}\n            </h3>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('reportTitle', 'Report Title')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={reportConfig.title}\n                  onChange={(e) => setReportConfig(prev => ({ ...prev, title: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('enterReportTitle', 'Enter report title...')}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('description', 'Description')}\n                </label>\n                <textarea\n                  value={reportConfig.description}\n                  onChange={(e) => setReportConfig(prev => ({ ...prev, description: e.target.value }))}\n                  rows=\"3\"\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('enterDescription', 'Enter report description...')}\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Report Type and Data Source */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('reportConfiguration', 'Report Configuration')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('reportType', 'Report Type')}\n                </label>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {reportTypes.map(type => (\n                    <button\n                      key={type.value}\n                      onClick={() => setReportConfig(prev => ({ ...prev, type: type.value }))}\n                      className={`p-3 border rounded-lg text-center transition-colors ${\n                        reportConfig.type === type.value\n                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'\n                          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'\n                      }`}\n                    >\n                      <i className={`${type.icon} text-lg mb-1`}></i>\n                      <div className=\"text-xs\">{type.label}</div>\n                    </button>\n                  ))}\n                </div>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('dataSource', 'Data Source')}\n                </label>\n                <select\n                  value={reportConfig.dataSource}\n                  onChange={(e) => setReportConfig(prev => ({ ...prev, dataSource: e.target.value, fields: [] }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  {Object.entries(dataSources).map(([key, source]) => (\n                    <option key={key} value={key}>{source.label}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Fields Selection */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('selectFields', 'Select Fields')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                  {t('availableFields', 'Available Fields')}\n                </h4>\n                <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                  {Object.entries(availableFields).map(([key, field]) => (\n                    <button\n                      key={key}\n                      onClick={() => addField(key)}\n                      className=\"w-full text-left px-3 py-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                      disabled={reportConfig.fields.includes(key)}\n                    >\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">{field.label}</div>\n                      <div className=\"text-xs text-gray-500 dark:text-gray-400\">{field.type}</div>\n                    </button>\n                  ))}\n                </div>\n              </div>\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                  {t('selectedFields', 'Selected Fields')}\n                </h4>\n                <div className=\"space-y-2 max-h-64 overflow-y-auto\">\n                  {reportConfig.fields.map((fieldKey, index) => (\n                    <div\n                      key={fieldKey}\n                      className=\"flex items-center justify-between px-3 py-2 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded\"\n                    >\n                      <span className=\"text-sm text-blue-900 dark:text-blue-100\">\n                        {availableFields[fieldKey]?.label}\n                      </span>\n                      <button\n                        onClick={() => removeField(fieldKey)}\n                        className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                      >\n                        <i className=\"fas fa-times\"></i>\n                      </button>\n                    </div>\n                  ))}\n                  {reportConfig.fields.length === 0 && (\n                    <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n                      {t('noFieldsSelected', 'No fields selected')}\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Filters */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('filters', 'Filters')}\n              </h3>\n              <button\n                onClick={addFilter}\n                className=\"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors\"\n              >\n                <i className=\"fas fa-plus mr-1\"></i>\n                {t('addFilter', 'Add Filter')}\n              </button>\n            </div>\n            <div className=\"space-y-3\">\n              {reportConfig.filters.map((filter, index) => (\n                <div key={index} className=\"grid grid-cols-1 md:grid-cols-4 gap-3 items-end\">\n                  <div>\n                    <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('field', 'Field')}\n                    </label>\n                    <select\n                      value={filter.field}\n                      onChange={(e) => updateFilter(index, 'field', e.target.value)}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"\">{t('selectField', 'Select Field')}</option>\n                      {Object.entries(availableFields).map(([key, field]) => (\n                        <option key={key} value={key}>{field.label}</option>\n                      ))}\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('operator', 'Operator')}\n                    </label>\n                    <select\n                      value={filter.operator}\n                      onChange={(e) => updateFilter(index, 'operator', e.target.value)}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"equals\">{t('equals', 'Equals')}</option>\n                      <option value=\"contains\">{t('contains', 'Contains')}</option>\n                      <option value=\"greater\">{t('greaterThan', 'Greater Than')}</option>\n                      <option value=\"less\">{t('lessThan', 'Less Than')}</option>\n                      <option value=\"between\">{t('between', 'Between')}</option>\n                    </select>\n                  </div>\n                  <div>\n                    <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('value', 'Value')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={filter.value}\n                      onChange={(e) => updateFilter(index, 'value', e.target.value)}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      placeholder={t('enterValue', 'Enter value...')}\n                    />\n                  </div>\n                  <div>\n                    <button\n                      onClick={() => removeFilter(index)}\n                      className=\"px-2 py-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                    >\n                      <i className=\"fas fa-trash\"></i>\n                    </button>\n                  </div>\n                </div>\n              ))}\n              {reportConfig.filters.length === 0 && (\n                <div className=\"text-center py-4 text-gray-500 dark:text-gray-400\">\n                  {t('noFiltersAdded', 'No filters added')}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Saved Reports */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('savedReports', 'Saved Reports')}\n            </h3>\n            <div className=\"space-y-3\">\n              {savedReports.map(report => (\n                <div key={report.id} className=\"p-3 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">{report.title}</h4>\n                  <p className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">{report.description}</p>\n                  <div className=\"flex items-center justify-between mt-2\">\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {new Date(report.lastModified).toLocaleDateString()}\n                    </span>\n                    <div className=\"flex space-x-1\">\n                      <button className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\">\n                        <i className=\"fas fa-edit text-xs\"></i>\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300\">\n                        <i className=\"fas fa-download text-xs\"></i>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Export Options */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('exportOptions', 'Export Options')}\n            </h3>\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => exportReport('pdf')}\n                className=\"w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-pdf text-red-600 mr-2\"></i>\n                {t('exportPDF', 'Export as PDF')}\n              </button>\n              <button\n                onClick={() => exportReport('excel')}\n                className=\"w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-excel text-green-600 mr-2\"></i>\n                {t('exportExcel', 'Export as Excel')}\n              </button>\n              <button\n                onClick={() => exportReport('csv')}\n                className=\"w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-csv text-blue-600 mr-2\"></i>\n                {t('exportCSV', 'Export as CSV')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Preview Modal */}\n      {showPreview && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {t('reportPreview', 'Report Preview')}\n                </h3>\n                <button\n                  onClick={() => setShowPreview(false)}\n                  className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              </div>\n            </div>\n            <div className=\"p-6\">\n              <h4 className=\"text-xl font-bold text-gray-900 dark:text-white mb-2\">\n                {reportConfig.title || t('untitledReport', 'Untitled Report')}\n              </h4>\n              {reportConfig.description && (\n                <p className=\"text-gray-600 dark:text-gray-400 mb-4\">{reportConfig.description}</p>\n              )}\n              \n              {/* Preview Table */}\n              <div className=\"overflow-x-auto\">\n                <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-600\">\n                  <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                    <tr>\n                      {reportConfig.fields.map(fieldKey => (\n                        <th\n                          key={fieldKey}\n                          className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\"\n                        >\n                          {availableFields[fieldKey]?.label}\n                        </th>\n                      ))}\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                    {previewData.map((row, index) => (\n                      <tr key={index}>\n                        {reportConfig.fields.map(fieldKey => (\n                          <td\n                            key={fieldKey}\n                            className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\"\n                          >\n                            {row[fieldKey] || '-'}\n                          </td>\n                        ))}\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdvancedReportBuilder;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst StatisticalDashboard = () => {\n  const { t, isRTL } = useLanguage();\n  const [timeRange, setTimeRange] = useState('last-30-days');\n  const [selectedMetrics, setSelectedMetrics] = useState(['completion', 'demographics', 'outcomes']);\n  const [statisticsData, setStatisticsData] = useState({});\n  const [loading, setLoading] = useState(true);\n\n  // Comprehensive mock statistics data\n  useEffect(() => {\n    const mockData = {\n      overview: {\n        totalPatients: 1247,\n        activePatients: 892,\n        completedTreatments: 456,\n        ongoingTreatments: 436,\n        averageAge: 34.5,\n        malePatients: 623,\n        femalePatients: 624,\n        totalSessions: 15678,\n        averageSessionsPerPatient: 12.6,\n        totalRevenue: 2456789,\n        averageRevenuePerPatient: 1970\n      },\n      completionStatistics: {\n        byTreatmentType: {\n          'Physical Therapy': { total: 298, completed: 234, rate: 78.5 },\n          'Occupational Therapy': { total: 201, completed: 156, rate: 77.6 },\n          'Speech Therapy': { total: 123, completed: 89, rate: 72.4 },\n          'Special Needs Therapy': { total: 89, completed: 67, rate: 75.3 },\n          'CARF Programs': { total: 56, completed: 45, rate: 80.4 }\n        },\n        byAge: {\n          '0-18': { total: 234, completed: 189, rate: 80.8 },\n          '19-35': { total: 345, completed: 267, rate: 77.4 },\n          '36-50': { total: 298, completed: 231, rate: 77.5 },\n          '51-65': { total: 267, completed: 198, rate: 74.2 },\n          '65+': { total: 103, completed: 71, rate: 68.9 }\n        },\n        byCondition: {\n          'Cerebral Palsy': { total: 156, completed: 124, rate: 79.5 },\n          'Autism Spectrum': { total: 134, completed: 98, rate: 73.1 },\n          'Stroke Recovery': { total: 189, completed: 156, rate: 82.5 },\n          'Spinal Injury': { total: 98, completed: 78, rate: 79.6 },\n          'Developmental Delays': { total: 167, completed: 134, rate: 80.2 },\n          'Neurological Disorders': { total: 145, completed: 109, rate: 75.2 },\n          'Orthopedic Conditions': { total: 234, completed: 189, rate: 80.8 },\n          'Other': { total: 124, completed: 89, rate: 71.8 }\n        }\n      },\n      demographicAnalysis: {\n        ageDistribution: {\n          '0-5': 89,\n          '6-12': 145,\n          '13-18': 123,\n          '19-30': 234,\n          '31-45': 298,\n          '46-60': 267,\n          '61-75': 78,\n          '75+': 25\n        },\n        genderByAge: {\n          male: { '0-18': 134, '19-35': 178, '36-50': 145, '51-65': 123, '65+': 43 },\n          female: { '0-18': 100, '19-35': 167, '36-50': 153, '51-65': 144, '65+': 60 }\n        },\n        conditionsByGender: {\n          male: {\n            'Cerebral Palsy': 89,\n            'Autism Spectrum': 98,\n            'Stroke Recovery': 87,\n            'Spinal Injury': 67,\n            'Developmental Delays': 78,\n            'Neurological Disorders': 65,\n            'Orthopedic Conditions': 123,\n            'Other': 56\n          },\n          female: {\n            'Cerebral Palsy': 67,\n            'Autism Spectrum': 36,\n            'Stroke Recovery': 102,\n            'Spinal Injury': 31,\n            'Developmental Delays': 89,\n            'Neurological Disorders': 80,\n            'Orthopedic Conditions': 111,\n            'Other': 68\n          }\n        }\n      },\n      outcomeMetrics: {\n        functionalImprovement: {\n          'Significant (>75%)': 234,\n          'Moderate (50-75%)': 345,\n          'Minimal (25-50%)': 156,\n          'None (<25%)': 67\n        },\n        goalAchievement: {\n          'Exceeded Goals': 123,\n          'Met Goals': 345,\n          'Partially Met': 234,\n          'Not Met': 89\n        },\n        satisfactionRatings: {\n          'Excellent (5)': 456,\n          'Good (4)': 234,\n          'Fair (3)': 89,\n          'Poor (2)': 23,\n          'Very Poor (1)': 5\n        },\n        painReduction: {\n          'Significant (>50%)': 189,\n          'Moderate (25-50%)': 234,\n          'Minimal (10-25%)': 123,\n          'None (<10%)': 67\n        }\n      },\n      timeAnalysis: {\n        monthlyAdmissions: [45, 52, 48, 61, 58, 67, 72, 69, 74, 78, 82, 85],\n        monthlyCompletions: [34, 38, 42, 45, 48, 52, 56, 59, 62, 65, 68, 71],\n        monthlyRevenue: [156000, 178000, 165000, 189000, 201000, 234000, 245000, 223000, 267000, 289000, 298000, 312000],\n        averageWaitTimes: [5.2, 4.8, 5.1, 4.9, 5.3, 4.7, 5.0, 4.6, 4.8, 4.5, 4.3, 4.1],\n        sessionAttendance: [92, 94, 91, 95, 93, 96, 94, 97, 95, 96, 98, 97]\n      },\n      therapistPerformance: {\n        'Dr. Sarah Ahmed': {\n          patients: 45,\n          completionRate: 85.2,\n          satisfaction: 4.8,\n          averageImprovement: 78.5,\n          sessionsPerWeek: 32\n        },\n        'Dr. Mohammed Ali': {\n          patients: 38,\n          completionRate: 82.1,\n          satisfaction: 4.7,\n          averageImprovement: 75.3,\n          sessionsPerWeek: 28\n        },\n        'Dr. Fatima Hassan': {\n          patients: 42,\n          completionRate: 88.9,\n          satisfaction: 4.9,\n          averageImprovement: 82.1,\n          sessionsPerWeek: 35\n        },\n        'Dr. Ahmed Khalil': {\n          patients: 35,\n          completionRate: 79.3,\n          satisfaction: 4.6,\n          averageImprovement: 73.8,\n          sessionsPerWeek: 26\n        }\n      },\n      financialAnalysis: {\n        revenueByTreatment: {\n          'Physical Therapy': 856000,\n          'Occupational Therapy': 634000,\n          'Speech Therapy': 423000,\n          'Special Needs Therapy': 345000,\n          'CARF Programs': 198000\n        },\n        paymentMethods: {\n          'Insurance': 1923456,\n          'Out of Pocket': 423789,\n          'Government': 109544\n        },\n        collectionRates: {\n          'Insurance': 94.2,\n          'Out of Pocket': 87.5,\n          'Government': 98.1\n        }\n      }\n    };\n\n    setTimeout(() => {\n      setStatisticsData(mockData);\n      setLoading(false);\n    }, 1000);\n  }, [timeRange]);\n\n  const StatCard = ({ title, value, subtitle, icon, color, change, changeType }) => (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{value}</p>\n          {subtitle && (\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">{subtitle}</p>\n          )}\n        </div>\n        <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/30 rounded-lg`}>\n          <i className={`${icon} text-${color}-600 dark:text-${color}-400 text-xl`}></i>\n        </div>\n      </div>\n      {change && (\n        <div className=\"mt-4 flex items-center\">\n          <span className={`text-sm font-medium ${\n            changeType === 'increase' ? 'text-green-600' : \n            changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'\n          }`}>\n            <i className={`fas fa-arrow-${\n              changeType === 'increase' ? 'up' : \n              changeType === 'decrease' ? 'down' : 'right'\n            } mr-1`}></i>\n            {change}\n          </span>\n          <span className=\"text-sm text-gray-500 dark:text-gray-400 ml-2\">\n            {t('fromLastPeriod', 'from last period')}\n          </span>\n        </div>\n      )}\n    </div>\n  );\n\n\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('statisticalDashboard', 'Statistical Dashboard')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('comprehensiveStatistics', 'Comprehensive patient and treatment statistics')}\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <select\n            value={timeRange}\n            onChange={(e) => setTimeRange(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          >\n            <option value=\"last-7-days\">{t('last7Days', 'Last 7 Days')}</option>\n            <option value=\"last-30-days\">{t('last30Days', 'Last 30 Days')}</option>\n            <option value=\"last-90-days\">{t('last90Days', 'Last 90 Days')}</option>\n            <option value=\"last-year\">{t('lastYear', 'Last Year')}</option>\n            <option value=\"all-time\">{t('allTime', 'All Time')}</option>\n          </select>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportData', 'Export Data')}\n          </button>\n        </div>\n      </div>\n\n      {/* Overview Statistics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatCard\n          title={t('totalPatients', 'Total Patients')}\n          value={statisticsData.overview?.totalPatients?.toLocaleString()}\n          subtitle={`${statisticsData.overview?.activePatients} ${t('active', 'active')}`}\n          icon=\"fas fa-users\"\n          color=\"blue\"\n          change=\"+12.5%\"\n          changeType=\"increase\"\n        />\n        <StatCard\n          title={t('completedTreatments', 'Completed Treatments')}\n          value={statisticsData.overview?.completedTreatments?.toLocaleString()}\n          subtitle={`${statisticsData.overview?.ongoingTreatments} ${t('ongoing', 'ongoing')}`}\n          icon=\"fas fa-check-circle\"\n          color=\"green\"\n          change=\"+8.3%\"\n          changeType=\"increase\"\n        />\n        <StatCard\n          title={t('averageAge', 'Average Age')}\n          value={`${statisticsData.overview?.averageAge} ${t('years', 'years')}`}\n          subtitle={`${statisticsData.overview?.malePatients}M / ${statisticsData.overview?.femalePatients}F`}\n          icon=\"fas fa-birthday-cake\"\n          color=\"purple\"\n          change=\"+0.5\"\n          changeType=\"increase\"\n        />\n        <StatCard\n          title={t('totalRevenue', 'Total Revenue')}\n          value={`${(statisticsData.overview?.totalRevenue / 1000000).toFixed(1)}M SAR`}\n          subtitle={`${statisticsData.overview?.averageRevenuePerPatient} ${t('perPatient', 'per patient')}`}\n          icon=\"fas fa-dollar-sign\"\n          color=\"yellow\"\n          change=\"+15.2%\"\n          changeType=\"increase\"\n        />\n      </div>\n\n      {/* Charts Grid */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Completion Rates by Treatment Type */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('completionRatesByTreatment', 'Completion Rates by Treatment Type')}\n          </h3>\n          <div className=\"space-y-4\">\n            {Object.entries(statisticsData.completionStatistics?.byTreatmentType || {}).map(([type, data], index) => {\n              const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-purple-500'];\n              return (\n                <div key={type} className=\"space-y-2\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{type}</span>\n                    <span className=\"text-sm font-bold text-gray-900 dark:text-white\">{data.rate}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                    <div\n                      className={`h-3 rounded-full ${colors[index % colors.length]} transition-all duration-500`}\n                      style={{ width: `${data.rate}%` }}\n                    ></div>\n                  </div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {data.completed} of {data.total} completed\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Age Distribution */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('ageDistribution', 'Age Distribution')}\n          </h3>\n          <div className=\"space-y-3\">\n            {Object.entries(statisticsData.demographicAnalysis?.ageDistribution || {}).map(([ageGroup, count], index) => {\n              const total = Object.values(statisticsData.demographicAnalysis?.ageDistribution || {}).reduce((sum, val) => sum + val, 0);\n              const percentage = total > 0 ? Math.round((count / total) * 100) : 0;\n              const colors = ['bg-red-400', 'bg-blue-400', 'bg-yellow-400', 'bg-green-400', 'bg-purple-400', 'bg-orange-400', 'bg-pink-400', 'bg-gray-400'];\n              return (\n                <div key={ageGroup} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`w-4 h-4 rounded-full ${colors[index % colors.length]}`}></div>\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{ageGroup}</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-lg font-bold text-gray-900 dark:text-white\">{count}</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">{percentage}%</div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Monthly Trends */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('monthlyTrends', 'Monthly Trends')}\n          </h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-4 text-sm\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded\"></div>\n                <span className=\"text-gray-700 dark:text-gray-300\">{t('admissions', 'Admissions')}</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-green-500 rounded\"></div>\n                <span className=\"text-gray-700 dark:text-gray-300\">{t('completions', 'Completions')}</span>\n              </div>\n            </div>\n            <div className=\"grid grid-cols-12 gap-1 h-32\">\n              {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].map((month, index) => {\n                const admissions = statisticsData.timeAnalysis?.monthlyAdmissions?.[index] || 0;\n                const completions = statisticsData.timeAnalysis?.monthlyCompletions?.[index] || 0;\n                const maxValue = Math.max(...(statisticsData.timeAnalysis?.monthlyAdmissions || []), ...(statisticsData.timeAnalysis?.monthlyCompletions || []));\n                const admissionHeight = maxValue > 0 ? (admissions / maxValue) * 100 : 0;\n                const completionHeight = maxValue > 0 ? (completions / maxValue) * 100 : 0;\n                return (\n                  <div key={month} className=\"flex flex-col items-center space-y-1\">\n                    <div className=\"flex-1 flex flex-col justify-end space-y-1 w-full\">\n                      <div\n                        className=\"bg-blue-500 rounded-t\"\n                        style={{ height: `${admissionHeight}%`, minHeight: '2px' }}\n                        title={`${month}: ${admissions} admissions`}\n                      ></div>\n                      <div\n                        className=\"bg-green-500 rounded-t\"\n                        style={{ height: `${completionHeight}%`, minHeight: '2px' }}\n                        title={`${month}: ${completions} completions`}\n                      ></div>\n                    </div>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">{month}</span>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Functional Improvement Outcomes */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('functionalImprovement', 'Functional Improvement Outcomes')}\n          </h3>\n          <div className=\"space-y-3\">\n            {Object.entries(statisticsData.outcomeMetrics?.functionalImprovement || {}).map(([category, count], index) => {\n              const total = Object.values(statisticsData.outcomeMetrics?.functionalImprovement || {}).reduce((sum, val) => sum + val, 0);\n              const percentage = total > 0 ? Math.round((count / total) * 100) : 0;\n              const colors = ['bg-green-500', 'bg-blue-500', 'bg-yellow-500', 'bg-red-500'];\n              return (\n                <div key={category} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`w-4 h-4 rounded-full ${colors[index % colors.length]}`}></div>\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{category}</span>\n                  </div>\n                  <div className=\"text-right\">\n                    <div className=\"text-lg font-bold text-gray-900 dark:text-white\">{count}</div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">{percentage}%</div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Detailed Statistics Tables */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Therapist Performance */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('therapistPerformance', 'Therapist Performance')}\n          </h3>\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-600\">\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                <tr>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase\">\n                    {t('therapist', 'Therapist')}\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase\">\n                    {t('patients', 'Patients')}\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase\">\n                    {t('completion', 'Completion')}\n                  </th>\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase\">\n                    {t('satisfaction', 'Satisfaction')}\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                {Object.entries(statisticsData.therapistPerformance || {}).map(([name, stats]) => (\n                  <tr key={name}>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                      {name}\n                    </td>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                      {stats.patients}\n                    </td>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                      {stats.completionRate}%\n                    </td>\n                    <td className=\"px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                      {stats.satisfaction}/5.0\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Completion Rates by Condition */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('completionByCondition', 'Completion Rates by Condition')}\n          </h3>\n          <div className=\"space-y-3\">\n            {Object.entries(statisticsData.completionStatistics?.byCondition || {}).map(([condition, stats]) => (\n              <div key={condition} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{condition}</span>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div \n                      className=\"bg-blue-600 h-2 rounded-full\"\n                      style={{ width: `${stats.rate}%` }}\n                    ></div>\n                  </div>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white w-12 text-right\">\n                    {stats.rate}%\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatisticalDashboard;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport AdvancedAnalytics from './AdvancedAnalytics';\nimport PatientProgressTracker from '../../components/Analytics/PatientProgressTracker';\nimport AdvancedReportBuilder from '../../components/Reports/AdvancedReportBuilder';\nimport StatisticalDashboard from '../../components/Analytics/StatisticalDashboard';\n\nconst Analytics = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [loading, setLoading] = useState(false);\n\n  const tabs = [\n    {\n      id: 'overview',\n      label: t('overview', 'Overview'),\n      icon: 'fas fa-chart-line',\n      component: AdvancedAnalytics\n    },\n    {\n      id: 'progress',\n      label: t('patientProgress', 'Patient Progress'),\n      icon: 'fas fa-users',\n      component: PatientProgressTracker\n    },\n    {\n      id: 'statistics',\n      label: t('statistics', 'Statistics'),\n      icon: 'fas fa-chart-bar',\n      component: StatisticalDashboard\n    },\n    {\n      id: 'reports',\n      label: t('reportBuilder', 'Report Builder'),\n      icon: 'fas fa-file-alt',\n      component: AdvancedReportBuilder\n    }\n  ];\n\n  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || AdvancedAnalytics;\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Tab Navigation */}\n        <div className=\"mb-8\">\n          <div className=\"border-b border-gray-200 dark:border-gray-700\">\n            <nav className=\"-mb-px flex space-x-8\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                  }`}\n                >\n                  <i className={tab.icon}></i>\n                  <span>{tab.label}</span>\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Tab Content */}\n        <div className=\"tab-content\">\n          <ActiveComponent />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Analytics;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\nimport { format, subDays, startOfMonth, endOfMonth } from 'date-fns';\n\nconst AdvancedAnalytics = () => {\n  const { t, isRTL } = useLanguage();\n  const [selectedPeriod, setSelectedPeriod] = useState('last-30-days');\n  const [selectedMetric, setSelectedMetric] = useState('all');\n  const [analyticsData, setAnalyticsData] = useState({});\n  const [loading, setLoading] = useState(true);\n\n  // Mock comprehensive analytics data\n  useEffect(() => {\n    const mockData = {\n      overview: {\n        totalPatients: 1247,\n        activePatients: 892,\n        completedTreatments: 456,\n        ongoingTreatments: 436,\n        averageCompletionRate: 78.5,\n        averageTreatmentDuration: 42, // days\n        totalSessions: 15678,\n        completedSessions: 12543,\n        cancelledSessions: 1234,\n        noShowSessions: 1901\n      },\n      patientProgress: {\n        completed: 456,\n        inProgress: 436,\n        onHold: 67,\n        discontinued: 89,\n        notStarted: 199\n      },\n      treatmentCompletion: {\n        physicalTherapy: { completed: 234, total: 298, rate: 78.5 },\n        occupationalTherapy: { completed: 156, total: 201, rate: 77.6 },\n        speechTherapy: { completed: 89, total: 123, rate: 72.4 },\n        specialNeeds: { completed: 67, total: 89, rate: 75.3 },\n        carfPrograms: { completed: 45, total: 56, rate: 80.4 }\n      },\n      demographics: {\n        ageGroups: {\n          '0-18': 234,\n          '19-35': 345,\n          '36-50': 298,\n          '51-65': 267,\n          '65+': 103\n        },\n        gender: {\n          male: 623,\n          female: 624\n        },\n        conditions: {\n          'Cerebral Palsy': 156,\n          'Autism Spectrum': 134,\n          'Stroke Recovery': 189,\n          'Spinal Injury': 98,\n          'Developmental Delays': 167,\n          'Neurological Disorders': 145,\n          'Orthopedic Conditions': 234,\n          'Other': 124\n        }\n      },\n      outcomeMetrics: {\n        functionalImprovement: {\n          significant: 67, // >75% improvement\n          moderate: 156,   // 50-75% improvement\n          minimal: 89,     // 25-50% improvement\n          none: 34         // <25% improvement\n        },\n        goalAchievement: {\n          exceeded: 89,\n          achieved: 234,\n          partiallyAchieved: 156,\n          notAchieved: 67\n        },\n        satisfactionScores: {\n          excellent: 456,\n          good: 234,\n          fair: 89,\n          poor: 23\n        }\n      },\n      timeAnalysis: {\n        averageWaitTime: 5.2, // days\n        averageSessionDuration: 45, // minutes\n        averageTreatmentLength: 42, // days\n        completionTimeVariance: 12.5 // days\n      },\n      financialMetrics: {\n        totalRevenue: 2456789,\n        averageRevenuePerPatient: 1970,\n        insuranceCoverage: 78.5,\n        outOfPocketPayments: 21.5,\n        collectionRate: 94.2\n      },\n      staffPerformance: {\n        therapists: [\n          { name: 'Dr. Sarah Ahmed', patients: 45, completionRate: 85.2, satisfaction: 4.8 },\n          { name: 'Dr. Mohammed Ali', patients: 38, completionRate: 82.1, satisfaction: 4.7 },\n          { name: 'Dr. Fatima Hassan', patients: 42, completionRate: 88.9, satisfaction: 4.9 },\n          { name: 'Dr. Ahmed Khalil', patients: 35, completionRate: 79.3, satisfaction: 4.6 }\n        ]\n      },\n      trends: {\n        monthlyAdmissions: [45, 52, 48, 61, 58, 67, 72, 69, 74, 78, 82, 85],\n        monthlyCompletions: [34, 38, 42, 45, 48, 52, 56, 59, 62, 65, 68, 71],\n        satisfactionTrend: [4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 4.8, 4.7, 4.8, 4.9, 4.8, 4.9]\n      }\n    };\n\n    setTimeout(() => {\n      setAnalyticsData(mockData);\n      setLoading(false);\n    }, 1000);\n  }, [selectedPeriod]);\n\n  const StatCard = ({ title, value, subtitle, icon, color, trend, percentage }) => (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{value}</p>\n          {subtitle && (\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">{subtitle}</p>\n          )}\n        </div>\n        <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/30 rounded-lg`}>\n          <i className={`${icon} text-${color}-600 dark:text-${color}-400 text-xl`}></i>\n        </div>\n      </div>\n      {trend && (\n        <div className=\"mt-4 flex items-center\">\n          <span className={`text-sm font-medium ${\n            trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'\n          }`}>\n            <i className={`fas fa-arrow-${trend === 'up' ? 'up' : trend === 'down' ? 'down' : 'right'} mr-1`}></i>\n            {percentage}%\n          </span>\n          <span className=\"text-sm text-gray-500 dark:text-gray-400 ml-2\">\n            {t('fromLastPeriod', 'from last period')}\n          </span>\n        </div>\n      )}\n    </div>\n  );\n\n  const CompletionChart = () => {\n    if (!analyticsData.treatmentCompletion) {\n      return <div className=\"flex items-center justify-center h-64 text-gray-500\">Loading chart data...</div>;\n    }\n\n    const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-purple-500'];\n\n    return (\n      <div className=\"space-y-4\">\n        <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {t('treatmentCompletionRates', 'Treatment Completion Rates by Type')}\n        </h4>\n        {Object.entries(analyticsData.treatmentCompletion).map(([type, data], index) => (\n          <div key={type} className=\"space-y-2\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{type}</span>\n              <span className=\"text-sm font-bold text-gray-900 dark:text-white\">{data.rate}%</span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n              <div\n                className={`h-3 rounded-full ${colors[index % colors.length]} transition-all duration-500`}\n                style={{ width: `${data.rate}%` }}\n              ></div>\n            </div>\n            <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n              {data.completed} of {data.total} completed\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  const PatientProgressPie = () => {\n    if (!analyticsData.patientProgress) {\n      return <div className=\"flex items-center justify-center h-64 text-gray-500\">Loading chart data...</div>;\n    }\n\n    const labels = [\n      t('completed', 'Completed'),\n      t('inProgress', 'In Progress'),\n      t('onHold', 'On Hold'),\n      t('discontinued', 'Discontinued'),\n      t('notStarted', 'Not Started')\n    ];\n\n    const values = Object.values(analyticsData.patientProgress);\n    const total = values.reduce((sum, val) => sum + val, 0);\n    const colors = ['text-green-600', 'text-blue-600', 'text-yellow-600', 'text-red-600', 'text-gray-600'];\n    const bgColors = ['bg-green-500', 'bg-blue-500', 'bg-yellow-500', 'bg-red-500', 'bg-gray-500'];\n\n    return (\n      <div className=\"space-y-4\">\n        <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {t('patientProgressDistribution', 'Patient Progress Distribution')}\n        </h4>\n        <div className=\"grid grid-cols-1 gap-3\">\n          {labels.map((label, index) => {\n            const value = values[index];\n            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;\n            return (\n              <div key={label} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className={`w-4 h-4 rounded-full ${bgColors[index]}`}></div>\n                  <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{label}</span>\n                </div>\n                <div className=\"text-right\">\n                  <div className={`text-lg font-bold ${colors[index]}`}>{value}</div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">{percentage}%</div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    );\n  };\n\n  const TrendsChart = () => {\n    if (!analyticsData.trends) {\n      return <div className=\"flex items-center justify-center h-64 text-gray-500\">Loading chart data...</div>;\n    }\n\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const admissions = analyticsData.trends.monthlyAdmissions || [];\n    const completions = analyticsData.trends.monthlyCompletions || [];\n    const maxValue = Math.max(...admissions, ...completions);\n\n    return (\n      <div className=\"space-y-4\">\n        <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {t('monthlyTrends', 'Monthly Trends')}\n        </h4>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center space-x-4 text-sm\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-blue-500 rounded\"></div>\n              <span className=\"text-gray-700 dark:text-gray-300\">{t('admissions', 'Admissions')}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-green-500 rounded\"></div>\n              <span className=\"text-gray-700 dark:text-gray-300\">{t('completions', 'Completions')}</span>\n            </div>\n          </div>\n          <div className=\"grid grid-cols-12 gap-2 h-48\">\n            {months.map((month, index) => {\n              const admissionHeight = maxValue > 0 ? (admissions[index] / maxValue) * 100 : 0;\n              const completionHeight = maxValue > 0 ? (completions[index] / maxValue) * 100 : 0;\n              return (\n                <div key={month} className=\"flex flex-col items-center space-y-1\">\n                  <div className=\"flex-1 flex flex-col justify-end space-y-1 w-full\">\n                    <div\n                      className=\"bg-blue-500 rounded-t\"\n                      style={{ height: `${admissionHeight}%`, minHeight: '2px' }}\n                      title={`${month}: ${admissions[index]} admissions`}\n                    ></div>\n                    <div\n                      className=\"bg-green-500 rounded-t\"\n                      style={{ height: `${completionHeight}%`, minHeight: '2px' }}\n                      title={`${month}: ${completions[index]} completions`}\n                    ></div>\n                  </div>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400 transform rotate-45 origin-bottom-left\">\n                    {month}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('advancedAnalytics', 'Advanced Analytics & Reporting')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('comprehensivePatientStatistics', 'Comprehensive patient statistics and treatment analytics')}\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <select\n            value={selectedPeriod}\n            onChange={(e) => setSelectedPeriod(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          >\n            <option value=\"last-7-days\">{t('last7Days', 'Last 7 Days')}</option>\n            <option value=\"last-30-days\">{t('last30Days', 'Last 30 Days')}</option>\n            <option value=\"last-90-days\">{t('last90Days', 'Last 90 Days')}</option>\n            <option value=\"last-year\">{t('lastYear', 'Last Year')}</option>\n            <option value=\"all-time\">{t('allTime', 'All Time')}</option>\n          </select>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportReport', 'Export Report')}\n          </button>\n        </div>\n      </div>\n\n      {/* Overview Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatCard\n          title={t('totalPatients', 'Total Patients')}\n          value={analyticsData.overview?.totalPatients?.toLocaleString()}\n          subtitle={`${analyticsData.overview?.activePatients} ${t('active', 'active')}`}\n          icon=\"fas fa-users\"\n          color=\"blue\"\n          trend=\"up\"\n          percentage=\"12.5\"\n        />\n        <StatCard\n          title={t('completionRate', 'Completion Rate')}\n          value={`${analyticsData.overview?.averageCompletionRate}%`}\n          subtitle={`${analyticsData.overview?.completedTreatments} ${t('completed', 'completed')}`}\n          icon=\"fas fa-check-circle\"\n          color=\"green\"\n          trend=\"up\"\n          percentage=\"5.2\"\n        />\n        <StatCard\n          title={t('averageDuration', 'Average Duration')}\n          value={`${analyticsData.overview?.averageTreatmentDuration} ${t('days', 'days')}`}\n          subtitle={t('treatmentLength', 'treatment length')}\n          icon=\"fas fa-clock\"\n          color=\"yellow\"\n          trend=\"down\"\n          percentage=\"3.1\"\n        />\n        <StatCard\n          title={t('totalSessions', 'Total Sessions')}\n          value={analyticsData.overview?.totalSessions?.toLocaleString()}\n          subtitle={`${analyticsData.overview?.completedSessions} ${t('completed', 'completed')}`}\n          icon=\"fas fa-calendar-check\"\n          color=\"purple\"\n          trend=\"up\"\n          percentage=\"8.7\"\n        />\n      </div>\n\n      {/* Charts Row 1 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <CompletionChart />\n        </div>\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <PatientProgressPie />\n        </div>\n      </div>\n\n      {/* Trends Chart */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <TrendsChart />\n      </div>\n    </div>\n  );\n};\n\nexport default AdvancedAnalytics;\n"], "names": ["PatientProgressTracker", "t", "isRTL", "useLanguage", "patients", "setPatients", "useState", "filteredPatients", "setFilteredPatients", "filters", "setFilters", "status", "treatmentType", "completionRate", "date<PERSON><PERSON><PERSON>", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "sortOrder", "setSortOrder", "loading", "setLoading", "useEffect", "mockPatients", "id", "name", "age", "condition", "startDate", "expectedEndDate", "sessionsCompleted", "totalSessions", "lastSession", "nextSession", "therapist", "goals", "goal", "progress", "functionalScores", "initial", "current", "target", "notes", "endDate", "setTimeout", "filtered", "filter", "patient", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "matchesTreatment", "matchesCompletion", "sort", "a", "b", "aValue", "bValue", "parseFloat", "getStatusColor", "getGoalStatusColor", "_jsx", "className", "children", "_jsxs", "concat", "type", "value", "onChange", "e", "placeholder", "prev", "_objectSpread", "map", "style", "width", "index", "length", "p", "Math", "round", "reduce", "acc", "AdvancedReportBuilder", "reportConfig", "setReportConfig", "title", "description", "dataSource", "fields", "groupBy", "start", "end", "chartType", "layout", "availableFields", "setAvailableFields", "previewData", "setPreviewData", "savedReports", "setSavedReports", "showPreview", "setShowPreview", "dataSources", "label", "treatments", "sessions", "financial", "outcomes", "reportTypes", "icon", "_dataSources$reportCo", "createdDate", "lastModified", "updateFilter", "key", "i", "exportReport", "format", "alert", "toUpperCase", "onClick", "generatePreview", "saveReport", "newReport", "Date", "now", "toISOString", "split", "rows", "Object", "entries", "_ref", "source", "_ref2", "field", "addField", "<PERSON><PERSON><PERSON>", "disabled", "_availableFields$fiel", "f", "removeField", "addFilter", "operator", "_ref3", "_", "removeFilter", "report", "toLocaleDateString", "_availableFields$fiel2", "row", "StatisticalDashboard", "_statisticsData$overv", "_statisticsData$overv2", "_statisticsData$overv3", "_statisticsData$overv4", "_statisticsData$overv5", "_statisticsData$overv6", "_statisticsData$overv7", "_statisticsData$overv8", "_statisticsData$overv9", "_statisticsData$overv0", "_statisticsData$overv1", "_statisticsData$compl", "_statisticsData$demog", "_statisticsData$outco", "_statisticsData$compl2", "timeRange", "setTimeRange", "selectedMetrics", "setSelectedMetrics", "statisticsData", "setStatisticsData", "mockData", "overview", "totalPatients", "activePatients", "completedTreatments", "ongoingTreatments", "averageAge", "malePatients", "femalePatients", "averageSessionsPerPatient", "totalRevenue", "averageRevenuePerPatient", "completionStatistics", "byTreatmentType", "total", "completed", "rate", "byAge", "byCondition", "demographicAnalysis", "ageDistribution", "genderByAge", "male", "female", "conditionsByGender", "outcomeMetrics", "functionalImprovement", "goalAchievement", "satisfactionRatings", "painReduction", "timeAnalysis", "monthlyAdmissions", "monthlyCompletions", "monthlyRevenue", "averageWaitTimes", "sessionAttendance", "therapistPerformance", "satisfaction", "averageImprovement", "sessionsPerWeek", "financialAnalysis", "revenueByTreatment", "paymentMethods", "collectionRates", "StatCard", "subtitle", "color", "change", "changeType", "toLocaleString", "toFixed", "data", "colors", "_statisticsData$demog2", "ageGroup", "count", "values", "sum", "val", "percentage", "month", "_statisticsData$timeA", "_statisticsData$timeA2", "_statisticsData$timeA3", "_statisticsData$timeA4", "_statisticsData$timeA5", "_statisticsData$timeA6", "admissions", "completions", "maxValue", "max", "admissionHeight", "completionHeight", "height", "minHeight", "_ref4", "_statisticsData$outco2", "category", "_ref5", "stats", "_ref6", "Analytics", "_tabs$find", "activeTab", "setActiveTab", "tabs", "component", "AdvancedAnalytics", "ActiveComponent", "find", "tab", "_analyticsData$overvi", "_analyticsData$overvi2", "_analyticsData$overvi3", "_analyticsData$overvi4", "_analyticsData$overvi5", "_analyticsData$overvi6", "_analyticsData$overvi7", "_analyticsData$overvi8", "_analyticsData$overvi9", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "selectedMetric", "setSelectedMetric", "analyticsData", "setAnalyticsData", "averageCompletionRate", "averageTreatmentDuration", "completedSessions", "cancelledSessions", "noShowSessions", "patientProgress", "inProgress", "onHold", "discontinued", "notStarted", "treatmentCompletion", "physicalTherapy", "occupationalTherapy", "speechTherapy", "specialNeeds", "carfPrograms", "demographics", "ageGroups", "gender", "conditions", "significant", "moderate", "minimal", "none", "exceeded", "achieved", "partiallyAchieved", "notAchieved", "satisfactionScores", "excellent", "good", "fair", "poor", "averageWaitTime", "averageSessionDuration", "averageTreatmentLength", "completionTimeVariance", "financialMetrics", "insuranceCoverage", "outOfPocketPayments", "collectionRate", "staffPerformance", "therapists", "trends", "satisfactionTrend", "trend", "CompletionChart", "PatientProgress<PERSON>ie", "labels", "bgColors", "TrendsChart"], "sourceRoot": ""}