"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3095],{3095:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=a(2555),r=a(5043),d=a(7921),i=a(4528),l=a(3216),n=a(5475),o=a(579);const c=()=>{const{t:e,isRTL:t}=(0,d.o)(),{user:a}=(0,i.A)(),[c,g]=((0,l.Zp)(),(0,r.useState)([])),[x,h]=(0,r.useState)(!0),[m,y]=(0,r.useState)(null),[p,u]=(0,r.useState)({search:"",therapist:"",dischargeReason:"",startDate:"",endDate:""}),[b,j]=(0,r.useState)({currentPage:1,totalPages:1,total:0,limit:10});(0,r.useEffect)(()=>{f()},[p,b.currentPage]);const f=async()=>{try{h(!0),y(null),console.log("Loading discharge assessments...");const e=Object.fromEntries(Object.entries(p).filter(e=>{let[t,a]=e;return""!==a})),t=new URLSearchParams((0,s.A)({page:b.currentPage.toString(),limit:b.limit.toString()},e));console.log("API URL:","/api/v1/discharge-assessments/public?".concat(t));const a=await fetch("/api/v1/discharge-assessments/public?".concat(t),{headers:(0,s.A)({"Content-Type":"application/json"},localStorage.getItem("token")&&{Authorization:"Bearer ".concat(localStorage.getItem("token"))})});if(console.log("Response status:",a.status),!a.ok){const e=await a.json().catch(()=>({}));throw console.log("Error response:",e),new Error(e.message||"HTTP ".concat(a.status,": ").concat(a.statusText))}{const e=await a.json();if(console.log("Response data:",e),!1===e.success)throw new Error(e.message||"Failed to load discharge assessments");g(e.assessments||[]),j(t=>(0,s.A)((0,s.A)({},t),{},{totalPages:e.totalPages||1,total:e.total||0}))}}catch(m){console.error("Error loading discharge assessments:",m),y(m.message),g([])}finally{h(!1)}},w=(e,t)=>{u(a=>(0,s.A)((0,s.A)({},a),{},{[e]:t})),j(e=>(0,s.A)((0,s.A)({},e),{},{currentPage:1}))},k=e=>{j(t=>(0,s.A)((0,s.A)({},t),{},{currentPage:e}))},v=e=>{if(!e||0===e.length)return null;const t=e[0];return(0,o.jsxs)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat({"Goals Met":"bg-green-100 text-green-800","Medical Condition":"bg-red-100 text-red-800","Reached Maximal Potential":"bg-blue-100 text-blue-800","Non-Compliance":"bg-orange-100 text-orange-800",Other:"bg-gray-100 text-gray-800"}[t]||"bg-gray-100 text-gray-800"),children:[t,e.length>1&&" +".concat(e.length-1)]})};return x?(0,o.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):m?(0,o.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("i",{className:"fas fa-exclamation-circle text-red-400"})}),(0,o.jsxs)("div",{className:"ml-3",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-red-800",children:e("errorLoadingAssessments","Error loading discharge assessments")}),(0,o.jsx)("div",{className:"mt-2 text-sm text-red-700",children:(0,o.jsx)("p",{children:m})}),(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsx)("button",{onClick:f,className:"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200",children:e("tryAgain","Try Again")})})]})]})}):(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:[(0,o.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("dischargeAssessments","Discharge Assessments")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e("manageDischargeAssessments","Manage and view patient discharge assessments")})]}),(0,o.jsxs)(n.N_,{to:"/forms/discharge-assessment/new",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),e("newDischargeAssessment","New Discharge Assessment")]})]})}),(0,o.jsx)("div",{className:"px-6 py-4 bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("search","Search")}),(0,o.jsx)("input",{type:"text",value:p.search,onChange:e=>w("search",e.target.value),placeholder:e("searchPatients","Search patients..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("therapist","Therapist")}),(0,o.jsx)("input",{type:"text",value:p.therapist,onChange:e=>w("therapist",e.target.value),placeholder:e("filterByTherapist","Filter by therapist..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("dischargeReason","Discharge Reason")}),(0,o.jsxs)("select",{value:p.dischargeReason,onChange:e=>w("dischargeReason",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm",children:[(0,o.jsx)("option",{value:"",children:e("allReasons","All Reasons")}),(0,o.jsx)("option",{value:"Goals Met",children:e("goalsMet","Goals Met")}),(0,o.jsx)("option",{value:"Medical Condition",children:e("medicalCondition","Medical Condition")}),(0,o.jsx)("option",{value:"Reached Maximal Potential",children:e("reachedMaximalPotential","Reached Maximal Potential")}),(0,o.jsx)("option",{value:"Non-Compliance",children:e("nonCompliance","Non-Compliance")}),(0,o.jsx)("option",{value:"Other",children:e("other","Other")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("startDate","Start Date")}),(0,o.jsx)("input",{type:"date",value:p.startDate,onChange:e=>w("startDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("endDate","End Date")}),(0,o.jsx)("input",{type:"date",value:p.endDate,onChange:e=>w("endDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"})]})]})})]}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6 p-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("showingResults","Showing {{start}} to {{end}} of {{total}} results",{start:(b.currentPage-1)*b.limit+1,end:Math.min(b.currentPage*b.limit,b.total),total:b.total})}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("label",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("itemsPerPage","Items per page:")}),(0,o.jsxs)("select",{value:b.limit,onChange:e=>j(t=>(0,s.A)((0,s.A)({},t),{},{limit:parseInt(e.target.value),currentPage:1})),className:"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm",children:[(0,o.jsx)("option",{value:10,children:"10"}),(0,o.jsx)("option",{value:25,children:"25"}),(0,o.jsx)("option",{value:50,children:"50"})]})]})]})}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,o.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("patient","Patient")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("dischargeDate","Discharge Date")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("therapist","Therapist")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("dischargeReason","Discharge Reason")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("visits","Visits")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("status","Status")}),(0,o.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,o.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:c.map(t=>{return(0,o.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.patientName}),(0,o.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["MR: ",t.mrNumber]})]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:new Date(t.dischargeDate).toLocaleDateString()}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.therapistSignature}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:v(t.dischargeReasons)}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{children:[t.totalVisits," total"]}),(0,o.jsxs)("div",{className:"text-xs text-gray-500",children:[t.noShowCancellations," no-shows"]})]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(a=t.status,(0,o.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat({draft:"bg-yellow-100 text-yellow-800",completed:"bg-green-100 text-green-800",reviewed:"bg-blue-100 text-blue-800"}[a]||"bg-gray-100 text-gray-800"),children:e(a,a)}))}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,o.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,o.jsx)(n.N_,{to:"/forms/discharge-assessment/".concat(t._id),className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300",title:e("view","View"),children:(0,o.jsx)("i",{className:"fas fa-eye"})}),(0,o.jsx)(n.N_,{to:"/forms/discharge-assessment/".concat(t._id,"/edit"),className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",title:e("edit","Edit"),children:(0,o.jsx)("i",{className:"fas fa-edit"})}),(0,o.jsx)("button",{onClick:()=>(async t=>{try{const e=await fetch("/api/v1/discharge-assessments/pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(e.ok){const a=await e.blob(),s=window.URL.createObjectURL(a),r=document.createElement("a");r.href=s,r.download="discharge-assessment-".concat(t.patientName,"-").concat(t.dischargeDate,".pdf"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(s),document.body.removeChild(r)}}catch(m){console.error("Error downloading PDF:",m),alert(e("errorDownloadingPDF","Error downloading PDF. Please try again."))}})(t),className:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300",title:e("downloadPDF","Download PDF"),children:(0,o.jsx)("i",{className:"fas fa-file-pdf"})}),(0,o.jsx)("button",{onClick:()=>(async t=>{if(window.confirm(e("confirmDeleteAssessment","Are you sure you want to delete this discharge assessment?")))try{if(!(await fetch("/api/v1/discharge-assessments/".concat(t),{method:"DELETE"})).ok)throw new Error("Failed to delete assessment");g(e=>e.filter(e=>e._id!==t)),alert(e("assessmentDeleted","Discharge assessment deleted successfully"))}catch(m){console.error("Error deleting assessment:",m),alert(e("errorDeleting","Error deleting assessment. Please try again."))}})(t._id),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",title:e("delete","Delete"),children:(0,o.jsx)("i",{className:"fas fa-trash"})})]})})]},t._id);var a})})]})}),0===c.length&&(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("i",{className:"fas fa-clipboard-list text-gray-400 text-4xl mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noAssessmentsFound","No discharge assessments found")}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:e("noAssessmentsDescription","Get started by creating your first discharge assessment.")}),(0,o.jsxs)(n.N_,{to:"/forms/discharge-assessment/new",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),e("createFirstAssessment","Create First Assessment")]})]})]}),b.totalPages>1&&(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mt-6 px-6 py-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("page","Page")," ",b.currentPage," ",e("of","of")," ",b.totalPages]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("button",{onClick:()=>k(b.currentPage-1),disabled:1===b.currentPage,className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed",children:e("previous","Previous")}),Array.from({length:Math.min(5,b.totalPages)},(e,t)=>{const a=t+Math.max(1,b.currentPage-2);return a>b.totalPages?null:(0,o.jsx)("button",{onClick:()=>k(a),className:"px-3 py-2 border rounded-lg text-sm font-medium ".concat(a===b.currentPage?"bg-blue-600 text-white border-blue-600":"border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"),children:a},a)}),(0,o.jsx)("button",{onClick:()=>k(b.currentPage+1),disabled:b.currentPage===b.totalPages,className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed",children:e("next","Next")})]})]})})]})}}}]);
//# sourceMappingURL=3095.61e14377.chunk.js.map