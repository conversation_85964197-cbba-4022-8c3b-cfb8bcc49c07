{"version": 3, "file": "static/js/5677.0475a3fb.chunk.js", "mappings": "wJAwZA,MAEA,EAFoB,IArZpB,MACEA,WAAAA,GACEC,KAAKC,QAAUC,+BACfF,KAAKG,cAAgB,cACrBH,KAAKI,mBACP,CAGAC,YAAAA,GACE,OAAOC,aAAaC,QAAQ,UAAY,YAC1C,CAGA,gBAAMC,CAAWC,GAAyB,IAAfC,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpC,MAAMG,EAAQd,KAAKK,eACbU,EAAG,GAAAC,OAAMhB,KAAKC,SAAOe,OAAGP,GAExBQ,EAAiB,CACrBC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADF,OAAYF,KAIzBK,GAAYC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbH,GACAP,GAAO,IACVQ,SAAOE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACFH,EAAeC,SACfR,EAAQQ,WAIf,IACE,MAAMG,QAAiBC,MAAMP,EAAKI,GAElC,IAAKE,EAASE,GACZ,MAAM,IAAIC,MAAM,uBAADR,OAAwBK,EAASI,SAIlD,aADmBJ,EAASK,MAE9B,CAAE,MAAOC,GAGP,OAFAC,QAAQC,KAAK,0BAADb,OAA2BP,EAAQ,kCAAkCkB,EAAMG,SAEhF9B,KAAK+B,uBAAuBtB,EAAUC,EAC/C,CACF,CAGAqB,sBAAAA,CAAuBtB,EAAUC,GAC/B,MAAMsB,EAAStB,EAAQsB,QAAU,MAC3BC,EAAOvB,EAAQwB,KAAOC,KAAKC,MAAM1B,EAAQwB,MAAQ,KAGvD,OAAIzB,EAAS4B,SAAS,YACbrC,KAAKsC,sBAAsB7B,EAAUuB,EAAQC,GAC3CxB,EAAS4B,SAAS,kBACpBrC,KAAKuC,4BAA4B9B,EAAUuB,EAAQC,GACjDxB,EAAS4B,SAAS,sBACpBrC,KAAKwC,uBAAuB/B,EAAUuB,EAAQC,GAC5CxB,EAAS4B,SAAS,oBACpBrC,KAAKyC,iBAAiBhC,EAAUuB,EAAQC,GAG1C,CAAES,SAAS,EAAOf,MAAO,0CAClC,CAGAvB,iBAAAA,GACOJ,KAAKO,QAAQ,iBAChBP,KAAK2C,QAAQ,eAAe,GAC5B3C,KAAK2C,QAAQ,WAAY3C,KAAK4C,sBAC9B5C,KAAK2C,QAAQ,cAAe,CAAC,GAC7B3C,KAAK2C,QAAQ,uBAAwB,IACrC3C,KAAK2C,QAAQ,mBAAoB,IACjC3C,KAAK2C,QAAQ,iBAAkB,IAEnC,CAGApC,OAAAA,CAAQsC,GACN,IACE,MAAMC,EAAOxC,aAAaC,QAAQP,KAAKG,cAAgB0C,GACvD,OAAOC,EAAOX,KAAKC,MAAMU,GAAQ,IACnC,CAAE,MAAOnB,GAEP,OADAC,QAAQD,MAAM,mCAAoCA,GAC3C,IACT,CACF,CAEAgB,OAAAA,CAAQE,EAAKE,GACX,IAEE,OADAzC,aAAaqC,QAAQ3C,KAAKG,cAAgB0C,EAAKV,KAAKa,UAAUD,KACvD,CACT,CAAE,MAAOpB,GAEP,OADAC,QAAQD,MAAM,iCAAkCA,IACzC,CACT,CACF,CAEAsB,UAAAA,CAAWJ,GACT,IAEE,OADAvC,aAAa2C,WAAWjD,KAAKG,cAAgB0C,IACtC,CACT,CAAE,MAAOlB,GAEP,OADAC,QAAQD,MAAM,oCAAqCA,IAC5C,CACT,CACF,CAGAiB,kBAAAA,GACE,MAAO,CACL,CACEM,GAAI,mBACJC,KAAM,iBACNC,OAAQ,oDACRC,IAAK,GACLC,OAAQ,OACRC,UAAW,iBACXC,YAAa,4EACbC,MAAO,gBACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,YAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAExB,CACEjB,GAAI,mBACJC,KAAM,gBACNC,OAAQ,gEACRC,IAAK,GACLC,OAAQ,SACRC,UAAW,qBACXC,YAAa,qGACbC,MAAO,cACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,OAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAG5B,CAGA,iBAAMC,GACJ,OAAO,IAAIC,QAASC,IAClBC,WAAW,KACTD,EAAQtE,KAAKO,QAAQ,aAAe,KACnC,MAEP,CAEA,gBAAMiE,CAAWC,GACf,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MACMG,GADW1E,KAAKO,QAAQ,aAAe,IACpBoE,KAAKC,GAAKA,EAAE1B,KAAOuB,GAC5CH,EAAQI,GAAW,OAClB,MAEP,CAGA,qBAAMG,CAAgBJ,EAAWK,GAC/B,IACE,MAAMzD,QAAiBrB,KAAKQ,WAAW,WAAY,CACjDwB,OAAQ,OACRE,KAAMC,KAAKa,WAAS5B,EAAAA,EAAAA,GAAC,CACnBqD,aACGK,MAGP,OAAOzD,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvDwE,EAAeN,IAAUrD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpB0D,GAAW,IACdE,WAAW,IAAId,MAAOC,gBAExBnE,KAAK2C,QAAQ,cAAeoC,GAC5BT,GAAQ,IACP,MAEP,CACF,CAEA,oBAAMW,CAAeR,GACnB,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvD+D,EAAQS,EAAeN,IAAc,OACpC,MAEP,CAGA,iBAAMS,CAAYC,GAChB,IACE,MAAM9D,QAAiBrB,KAAKQ,WAAW,iBAAkB,CACvDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUmC,KAEvB,OAAO9D,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMa,EAAUpF,KAAKO,QAAQ,yBAA2B,GAClDuB,GAAOV,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZH,GAAW,IACdH,WAAW,IAAId,MAAOC,cACtB1C,OAAQ,SAEV2D,EAAQG,KAAKzD,GACb9B,KAAK2C,QAAQ,uBAAwByC,GACrCd,EAAQxC,IACP,MAEP,CACF,CAEA,6BAAM0D,CAAwBf,GAC5B,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,kBAADQ,OAAmByD,IACzD,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACMkB,GADUzF,KAAKO,QAAQ,yBAA2B,IACzBmF,OAAOC,GAAOA,EAAIlB,YAAcA,GAC/DH,EAAQmB,IACP,MAEP,CACF,CAGA,yBAAMG,CAAoBC,GACxB,IACE,MAAMxE,QAAiBrB,KAAKQ,WAAW,qBAAsB,CAC3DwB,OAAQ,OACRE,KAAMC,KAAKa,UAAU6C,KAEvB,OAAOxE,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMuB,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CwF,GAAO3E,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZO,GAAW,IACd5B,WAAW,IAAIC,MAAOC,gBAExB2B,EAASP,KAAKQ,GACd/F,KAAK2C,QAAQ,mBAAoBmD,GACjCxB,EAAQyB,IACP,MAEP,CACF,CAEA,yBAAMC,CAAoBvB,GACxB,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,sBAADQ,OAAuByD,IAC7D,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACM0B,GADWjG,KAAKO,QAAQ,qBAAuB,IACpBmF,OAAOQ,GAAQA,EAAKzB,YAAcA,GACnEH,EAAQ2B,IACP,MAEP,CACF,CAGA,uBAAME,CAAkBC,GACtB,IACE,MAAM/E,QAAiBrB,KAAKQ,WAAW,mBAAoB,CACzDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUoD,KAEvB,OAAO/E,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjD+F,GAAWlF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACf8B,GAAIgB,KAAKmB,MAAMC,YACZc,GAAe,IAClBpB,WAAW,IAAId,MAAOC,gBAExBkC,EAAad,KAAKe,GAClBtG,KAAK2C,QAAQ,iBAAkB0D,GAC/B/B,EAAQgC,IACP,MAEP,CACF,CAEA,uBAAMC,CAAkB9B,GACtB,IACE,MAAMhE,EAAWgE,EAAS,oBAAAzD,OAAuByD,GAAc,mBACzDpD,QAAiBrB,KAAKQ,WAAWC,GACvC,OAAOY,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDiG,EAAsB/B,EACxB4B,EAAaX,OAAOe,GAAOA,EAAIhC,YAAcA,GAC7C4B,EACJ/B,EAAQkC,IACP,MAEP,CACF,CAGA,kBAAME,GACJ,OAAO,IAAIrC,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDoG,EAAiB3G,KAAKO,QAAQ,yBAA2B,GACzDuF,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CuE,EAAc9E,KAAKO,QAAQ,gBAAkB,CAAC,EAE9CqG,EAAY,CAChBC,eAAgBR,EAAazF,OAC7BkG,oBAAqBH,EAAe/F,OACpCmG,sBAAuBjB,EAASlF,OAChCoG,wBAAyBC,OAAOC,KAAKpC,GAAalE,OAClDuG,eAAgB,IACXd,EAAae,OAAO,GAAGC,IAAIC,IAAClG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,MAASD,OAClDX,EAAeS,OAAO,GAAGC,IAAIG,IAACpG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,iBAAoBC,OAC/D1B,EAASsB,OAAO,GAAGC,IAAIzC,IAACxD,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,YAAe3C,KACvD6C,KAAK,CAACC,EAAGC,IAAM,IAAIzD,KAAKyD,EAAE3C,WAAa,IAAId,KAAKwD,EAAE1C,YAAYoC,MAAM,EAAG,KAG3E9C,EAAQsC,IACP,MAEP,CAGAgB,YAAAA,GACe,CAAC,WAAY,cAAe,uBAAwB,mBAAoB,kBAChFC,QAAQhF,GAAO7C,KAAKiD,WAAWJ,IACpC7C,KAAKiD,WAAW,eAChBjD,KAAKI,mBACP,CAGA0H,UAAAA,GASE,MARa,CACXC,SAAU/H,KAAKO,QAAQ,YACvBuE,YAAa9E,KAAKO,QAAQ,eAC1ByH,qBAAsBhI,KAAKO,QAAQ,wBACnC0H,iBAAkBjI,KAAKO,QAAQ,oBAC/B2H,eAAgBlI,KAAKO,QAAQ,kBAC7B4H,YAAY,IAAIjE,MAAOC,cAG3B,CAGAiE,UAAAA,CAAWnG,GACT,IAME,OALIA,EAAK8F,UAAU/H,KAAK2C,QAAQ,WAAYV,EAAK8F,UAC7C9F,EAAK6C,aAAa9E,KAAK2C,QAAQ,cAAeV,EAAK6C,aACnD7C,EAAK+F,sBAAsBhI,KAAK2C,QAAQ,uBAAwBV,EAAK+F,sBACrE/F,EAAKgG,kBAAkBjI,KAAK2C,QAAQ,mBAAoBV,EAAKgG,kBAC7DhG,EAAKiG,gBAAgBlI,KAAK2C,QAAQ,iBAAkBV,EAAKiG,iBACtD,CACT,CAAE,MAAOvG,GAEP,OADAC,QAAQD,MAAM,wBAAyBA,IAChC,CACT,CACF,CAGA0G,KAAAA,GAAiB,IAAXC,EAAE3H,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,IACT,OAAO,IAAI0D,QAAQC,GAAWC,WAAWD,EAASgE,GACpD,E,iIC7YF,MA4SA,EA5S0BC,KACxB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,UAAEjE,IAAckE,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OACVnE,EAASoE,IAAcC,EAAAA,EAAAA,UAAS,OAChCf,EAAsBgB,IAA2BD,EAAAA,EAAAA,UAAS,KAC1DE,EAASC,IAAcH,EAAAA,EAAAA,WAAS,IAChCI,EAAOC,IAAYL,EAAAA,EAAAA,UAAS,CACjCM,cAAe,EACfC,WAAY,EACZC,SAAU,EACVC,cAAe,EACfC,UAAW,KAGbC,EAAAA,EAAAA,WAAU,KACRC,IACAC,KACC,CAACnF,IAEJ,MAAMkF,EAAkBE,UACtB,GAAIpF,EACF,IACE,MAAMqF,QAAoBC,EAAAA,EAAYvF,WAAWC,GACjDqE,EAAWgB,EACb,CAAE,MAAOnI,GACPC,QAAQD,MAAM,yBAA0BA,GACxCqI,EAAAA,GAAMrI,MAAM6G,EAAE,sBAAuB,8BACvC,CAEFU,GAAW,IAGPU,EAA2BC,UAC/B,GAAIpF,EACF,IACE,MAAMW,QAAgB2E,EAAAA,EAAYvE,wBAAwBf,GAC1DuE,EAAwB5D,GAGxB,MAAM+D,EAAQ,CACZE,cAAejE,EAAQxE,OACvB0I,WAAYlE,EAAQM,OAAOC,IAAG,IAAAsE,EAAA,OAAgB,QAAhBA,EAAItE,EAAIuE,gBAAQ,IAAAD,OAAA,EAAZA,EAAc5H,SAAS,WAAUzB,OACnE2I,SAAUnE,EAAQM,OAAOC,IAAG,IAAAwE,EAAA,OAAgB,QAAhBA,EAAIxE,EAAIuE,gBAAQ,IAAAC,OAAA,EAAZA,EAAc9H,SAAS,SAAQzB,OAC/D4I,cAAepE,EAAQM,OAAOC,IAAG,IAAAyE,EAAA,OAAgB,QAAhBA,EAAIzE,EAAIuE,gBAAQ,IAAAE,OAAA,EAAZA,EAAc/H,SAAS,cAAazB,OACzE6I,UAAWrE,EAAQM,OAAOC,IAAG,IAAA0E,EAAA,OAAgB,QAAhBA,EAAI1E,EAAIuE,gBAAQ,IAAAG,OAAA,EAAZA,EAAchI,SAAS,UAASzB,QAEnEwI,EAASD,EACX,CAAE,MAAOxH,GACPC,QAAQD,MAAM,uCAAwCA,EACxD,GAeE2I,EAAkBC,IACtB,OAAQA,GACN,IAAK,QAAS,MAAO,kBACrB,IAAK,MAAO,MAAO,aACnB,IAAK,WAAY,MAAO,kBACxB,IAAK,OAAQ,MAAO,cACpB,QAAS,MAAO,mBAIdC,EAAmBD,IACvB,OAAQA,GACN,IAAK,QAAS,MAAO,gBACrB,IAAK,MAAO,MAAO,iBACnB,IAAK,WAAY,MAAO,iBACxB,IAAK,OAAQ,MAAO,kBACpB,QAAS,MAAO,kBAIdE,EAAmBC,IACvB,OAAQA,GACN,IAAK,OAAQ,MAAO,0BACpB,IAAK,SAAU,MAAO,gCACtB,IAAK,MAAO,MAAO,8BACnB,QAAS,MAAO,8BAIpB,OAAIzB,GAEA0B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8DAA6DC,SAAA,EAE1EF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oFAAmFC,UAChGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMnC,GAAU,GACzBgC,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,mDAAkDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCACZpC,EAAE,mBAAoB,wBAExB9D,IACCoG,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wCAAuCC,SAAA,CACjDrC,EAAE,UAAW,WAAW,KAAGC,EAAQ/D,EAAQtB,OAASsB,EAAQvB,MAC7D2H,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6BAA4BC,SAAA,CACzCnG,EAAQjB,MAAM,WAAIiB,EAAQhB,mBAMrCiH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,yEAAwEC,SAAA,EACtFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZpC,EAAE,eAAgB,qBAErBsC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,uEAAsEC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZpC,EAAE,YAAa,+BAS9BmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrErC,EAAE,cAAe,mBAGpBmC,EAAAA,EAAAA,KAACK,EAAAA,EAAoB,CACnBvG,UAAWA,EACXwG,OAzGYpB,UACxB,UACQE,EAAAA,EAAY7E,aAAW9D,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAI+D,GAAW,IAAEV,qBAC1CmF,IACNI,EAAAA,GAAMtH,QAAQ8F,EAAE,cAAe,6BACjC,CAAE,MAAO7G,GACPC,QAAQD,MAAM,yBAA0BA,GACxCqI,EAAAA,GAAMrI,MAAM6G,EAAE,eAAgB,yBAChC,WAuGMsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0BAAyBC,SAAA,EAEtCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCACZpC,EAAE,qBAAsB,2BAG3BsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDrC,EAAE,gBAAiB,qBAEtBmC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDC,SAClE1B,EAAME,oBAIXyB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,cAE7DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAChE1B,EAAMG,iBAIXwB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,YAE7DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAChE1B,EAAMI,eAIXuB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,iBAE7DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAChE1B,EAAMK,oBAIXsB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAC,aAE7DF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAChE1B,EAAMM,yBAQjBqB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACZpC,EAAE,iBAAkB,sBAGtBR,EAAqBpH,OAAS,GAC7B+J,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qCAAoCC,SAChD7C,EAAqBZ,MAAM,EAAG,IAAIC,IAAI,CAACvF,EAASoJ,KAAK,IAAAC,EAAA,OACpDL,EAAAA,EAAAA,MAAA,OAEEF,UAAU,6DAA4DC,SAAA,EAEtEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,CACzB,QADyBM,EACzCrJ,EAAQoI,gBAAQ,IAAAiB,OAAA,EAAhBA,EAAkB9D,IAAI,CAACkD,EAASa,KAC/BT,EAAAA,EAAAA,KAAA,KAEEC,UAAS,GAAA5J,OAAKsJ,EAAeC,GAAQ,KAAAvJ,OAAIwJ,EAAgBD,GAAQ,aAD5Da,KAITT,EAAAA,EAAAA,KAAA,QAAMC,UAAS,kCAAA5J,OAAoCyJ,EAAgB3I,EAAQ4I,UAAWG,SACnF/I,EAAQ4I,SAAW,eAGxBC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SACpC,IAAI3G,KAAKpC,EAAQkD,WAAWqG,2BAIjCV,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yDAAwDC,SACpE/I,EAAQwJ,SAAW9C,EAAE,YAAa,iBAGrCmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDC,SACnE/I,EAAQA,WAGXgJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SACpC,IAAI3G,KAAKpC,EAAQkD,WAAWuG,wBAE/BZ,EAAAA,EAAAA,KAAA,QAAMC,UAAS,kCAAA5J,OACM,SAAnBc,EAAQL,OAAoB,8BACT,YAAnBK,EAAQL,OAAuB,gCAC/B,2BACCoJ,SACA/I,EAAQL,QAAU,cArClByJ,QA4CXJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDACbD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDrC,EAAE,aAAc,sBAEnBmC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDrC,EAAE,mBAAoB,6D,6ECnS3C,MA6YA,EA7Y6BgD,IAA4C,IAA3C,UAAE/G,EAAS,OAAEwG,EAAM,UAAEQ,EAAY,IAAID,EACjE,MAAM,EAAEhD,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdgD,EAAkBC,IAAuB5C,EAAAA,EAAAA,UAAS,CAAC,WACnDjH,EAAS8J,IAAc7C,EAAAA,EAAAA,UAAS,KAChCuC,EAASO,IAAc9C,EAAAA,EAAAA,UAAS,KAChC+C,EAAkBC,IAAuBhD,EAAAA,EAAAA,UAAS,KAClD2B,EAASsB,IAAcjD,EAAAA,EAAAA,UAAS,WAChCkD,EAAeC,IAAoBnD,EAAAA,EAAAA,UAAS,KAC5CoD,EAAoBC,IAAyBrD,EAAAA,EAAAA,UAAS,CAAC,GAGxDmB,EAAW,CACf,CACEhH,GAAI,QACJC,KAAMqF,EAAE,QAAS,SACjB6D,KAAM,kBACNC,MAAO,OACPC,WAAW,EACXC,YAAahE,EAAE,YAAa,mBAE9B,CACEtF,GAAI,MACJC,KAAMqF,EAAE,MAAO,OACf6D,KAAM,aACNC,MAAO,QACPC,WAAW,EACXC,YAAahE,EAAE,UAAW,iBAE5B,CACEtF,GAAI,WACJC,KAAMqF,EAAE,WAAY,YACpB6D,KAAM,kBACNC,MAAO,QACPC,WAAW,EACXC,YAAahE,EAAE,eAAgB,sBAEjC,CACEtF,GAAI,OACJC,KAAMqF,EAAE,mBAAoB,qBAC5B6D,KAAM,cACNC,MAAO,SACPC,WAAW,EACXC,YAAahE,EAAE,WAAY,yCAE7B,CACEtF,GAAI,WACJC,KAAMqF,EAAE,WAAY,YACpB6D,KAAM,kBACNC,MAAO,OACPC,WAAW,EACXC,YAAahE,EAAE,eAAgB,qCAK7BiE,EAAmB,CACvB,CACEvJ,GAAI,uBACJC,KAAMqF,EAAE,sBAAuB,wBAC/B8C,QAAS9C,EAAE,6BAA8B,wBACzCkE,QAASlE,EAAE,6BAA8B,oHACzC0B,SAAU,CAAC,QAAS,MAAO,WAAY,SAEzC,CACEhH,GAAI,wBACJC,KAAMqF,EAAE,uBAAwB,yBAChC8C,QAAS9C,EAAE,8BAA+B,yBAC1CkE,QAASlE,EAAE,8BAA+B,mJAC1C0B,SAAU,CAAC,QAAS,aAEtB,CACEhH,GAAI,kBACJC,KAAMqF,EAAE,iBAAkB,mBAC1B8C,QAAS9C,EAAE,wBAAyB,wBACpCkE,QAASlE,EAAE,wBAAyB,oFACpC0B,SAAU,CAAC,QAAS,WAAY,SAElC,CACEhH,GAAI,sBACJC,KAAMqF,EAAE,qBAAsB,uBAC9B8C,QAAS9C,EAAE,4BAA6B,uBACxCkE,QAASlE,EAAE,4BAA6B,iFACxC0B,SAAU,CAAC,MAAO,SAEpB,CACEhH,GAAI,SACJC,KAAMqF,EAAE,gBAAiB,kBACzB8C,QAAS,GACToB,QAAS,GACTxC,SAAU,CAAC,QAAS,MAAO,WAAY,WAK3CR,EAAAA,EAAAA,WAAU,KACRiD,KACC,CAAClI,IAEJ,MAAMkI,EAAyB9C,UAC7B,IAEE,MAAM+C,EAAc,CAClBhJ,kBAAmB,CAAC,QAAS,YAC7BC,SAAU,KACVgJ,SAAU,cACV/I,WAAY,CAAEC,MAAO,QAASC,IAAK,SACnC8I,YAAY,EACZC,eAAgB,MAChBC,aAAc,SACdC,kBAAmB,MACnBC,cAAe,OAEjBd,EAAsBQ,GACtBjB,EAAoBiB,EAAYhJ,kBAClC,CAAE,MAAOjC,GACPC,QAAQD,MAAM,qCAAsCA,EACtD,GAkBIwL,EAAuBC,IAC3BzB,EAAoB0B,GAClBA,EAAKhL,SAAS+K,GACVC,EAAK3H,OAAOxC,GAAMA,IAAOkK,GACzB,IAAIC,EAAMD,KA0ElB,OACEzC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,UACpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uCACZpC,EAAE,sBAAuB,4BAE5BsC,EAAAA,EAAAA,MAAA,UACEC,QArEeuC,KACvB,MAAMC,EAVU,WAAZ7C,EACK,CAAC,MAAO,OAAQ,YACF,SAAZA,EACF,CAAC,QAAS,WAAY,QAEtByB,EAAmBvI,mBAAqB,CAAC,SAMlD+H,EAAoB4B,GACpBvD,EAAAA,GAAMtH,QAAQ8F,EAAE,uBAAwB,qEAmEhCoC,UAAU,6FAA4FC,SAAA,EAEtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZpC,EAAE,aAAc,sBAIrBsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ErC,EAAE,kBAAmB,uBAExBsC,EAAAA,EAAAA,MAAA,UACE/H,MAAO+I,EACP0B,SAAWC,GAxHKC,KAC5B,MAAMC,EAAWlB,EAAiB9H,KAAK6D,GAAKA,EAAEtF,KAAOwK,GACrD,GAAIC,EAAU,CACZ5B,EAAoB2B,GACpB7B,EAAW8B,EAASrC,SACpBM,EAAW+B,EAASjB,SAGpB,MAAMkB,EAAoBD,EAASzD,SAASxE,OAAO6E,GACjDL,EAASvF,KAAK6C,GAAKA,EAAEtE,KAAOqH,GAAW/C,EAAE+E,YAE3CZ,EAAoBiC,EACtB,GA4G6BC,CAAqBJ,EAAEK,OAAO/K,OAC/C6H,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQ5H,MAAM,GAAE8H,SAAErC,EAAE,iBAAkB,uBACrCiE,EAAiBpF,IAAIsG,IACpBhD,EAAAA,EAAAA,KAAA,UAA0B5H,MAAO4K,EAASzK,GAAG2H,SAC1C8C,EAASxK,MADCwK,EAASzK,WAQ3BwI,EAAiBrJ,SAAS,WACzByI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ErC,EAAE,UAAW,cAEhBmC,EAAAA,EAAAA,KAAA,SACEpD,KAAK,OACLxE,MAAOuI,EACPkC,SAAWC,GAAM5B,EAAW4B,EAAEK,OAAO/K,OACrC6H,UAAU,kKACVmD,YAAavF,EAAE,eAAgB,6BAMrCsC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ErC,EAAE,UAAW,cAEhBmC,EAAAA,EAAAA,KAAA,YACE5H,MAAOjB,EACP0L,SAAWC,GAAM7B,EAAW6B,EAAEK,OAAO/K,OACrCiL,KAAM,EACNpD,UAAU,8KACVmD,YAAavF,EAAE,eAAgB,4BAEjCsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,CAC3D/I,EAAQlB,OAAO,SAAO4H,EAAE,aAAc,qBAK3CsC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ErC,EAAE,UAAW,cAEhBsC,EAAAA,EAAAA,MAAA,UACE/H,MAAO2H,EACP8C,SAAWC,GAAMzB,EAAWyB,EAAEK,OAAO/K,OACrC6H,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQ5H,MAAM,MAAK8H,SAAErC,EAAE,aAAc,UACrCmC,EAAAA,EAAAA,KAAA,UAAQ5H,MAAM,SAAQ8H,SAAErC,EAAE,gBAAiB,aAC3CmC,EAAAA,EAAAA,KAAA,UAAQ5H,MAAM,OAAM8H,SAAErC,EAAE,cAAe,WACvCmC,EAAAA,EAAAA,KAAA,UAAQ5H,MAAM,SAAQ8H,SAAErC,EAAE,gBAAiB,mBAK/CsC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ErC,EAAE,eAAgB,iBAAiB,KAAGA,EAAE,WAAY,YAAY,QAEnEmC,EAAAA,EAAAA,KAAA,SACEpD,KAAK,iBACLxE,MAAOkJ,EACPuB,SAAWC,GAAMvB,EAAiBuB,EAAEK,OAAO/K,OAC3C6H,UAAU,2KAMhBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtErC,EAAE,wBAAyB,6BAE9BmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBX,EAAS7C,IAAIkD,IACZI,EAAAA,EAAAA,KAAA,OAEEC,UAAS,uDAAA5J,OACP0K,EAAiBrJ,SAASkI,EAAQrH,IAC9B,iDACA,6DAA4D,KAAAlC,OAC7DuJ,EAAQgC,UAA8C,GAAlC,iCACzBxB,QAASA,IAAMR,EAAQgC,WAAaY,EAAoB5C,EAAQrH,IAAI2H,UAEpEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAA5J,OAAKuJ,EAAQ8B,KAAI,UAAArL,OAASuJ,EAAQ+B,MAAK,mBACnDxB,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4CAA2CC,SACvDN,EAAQpH,QAEXwH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDN,EAAQiC,qBAIf1B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EACxCN,EAAQgC,YACR5B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wBAAuBC,SACpCrC,EAAE,aAAc,kBAGrBmC,EAAAA,EAAAA,KAAA,SACEpD,KAAK,WACL0G,QAASvC,EAAiBrJ,SAASkI,EAAQrH,IAC3CsK,SAAUA,IAAMjD,EAAQgC,WAAaY,EAAoB5C,EAAQrH,IACjEgL,UAAW3D,EAAQgC,UACnB3B,UAAU,6DA/BXL,EAAQrH,UAyCpBiJ,EAAmBvI,oBAClBkH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtErC,EAAE,qBAAsB,0BAE3BsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CACtDrC,EAAE,oBAAqB,aAAa,KAAG2D,EAAmBvI,kBAAkBuK,KAAK,SAEnFhC,EAAmBrI,aAClBgH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CACtDrC,EAAE,aAAc,eAAe,KAAG2D,EAAmBrI,WAAWC,MAAM,MAAIoI,EAAmBrI,WAAWE,WAOjH8G,EAAAA,EAAAA,MAAA,UACEC,QA9MOlB,UACjB,KAnBK/H,EAAQsM,OAKT1C,EAAiBrJ,SAAS,WAAaiJ,EAAQ8C,QACjDpE,EAAAA,GAAMrI,MAAM6G,EAAE,kBAAmB,kCAC1B,GAGuB,IAA5BkD,EAAiB9K,SACnBoJ,EAAAA,GAAMrI,MAAM6G,EAAE,gBAAiB,qDACxB,IAXPwB,EAAAA,GAAMrI,MAAM6G,EAAE,kBAAmB,gCAC1B,IAiBe,OAExB,MAAM6F,EAAoB,CACxB5J,YACAyF,SAAUwB,EACV5J,UACAwJ,UACAZ,UACAuB,cAAeA,GAAiB,KAChC0B,SAAU7B,EACV9G,WAAW,IAAId,MAAOC,eAGxB,IACM8G,SACIA,EAAOoD,GAIfzM,QAAQ0M,IAAI,yBAA0BD,GAEtCrE,EAAAA,GAAMtH,QAAQ8F,EAAE,cAAe,oDAG/BoD,EAAW,IACXC,EAAW,IACXE,EAAoB,IACpBG,EAAiB,GACnB,CAAE,MAAOvK,GACPqI,EAAAA,GAAMrI,MAAM6G,EAAE,YAAa,0BAC3B5G,QAAQD,MAAM,cAAeA,EAC/B,GA+KUuM,SAAsC,IAA5BxC,EAAiB9K,SAAiBkB,EAAQsM,OACpDxD,UAAU,yIAAwIC,SAAA,EAElJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BACZqB,EAAgBzD,EAAE,kBAAmB,oBAAsBA,EAAE,cAAe,8B", "sources": ["services/dataService.js", "pages/Communication/CommunicationPage.jsx", "components/Communication/CommunicationManager.jsx"], "sourcesContent": ["// Data Service for PhysioFlow\n// Handles API calls with localStorage fallback for demonstration purposes\n\nclass DataService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n    this.storagePrefix = 'physioflow_';\n    this.initializeStorage();\n  }\n\n  // Helper method to get auth token\n  getAuthToken() {\n    return localStorage.getItem('token') || 'demo-token';\n  }\n\n  // Helper method to make API requests\n  async apiRequest(endpoint, options = {}) {\n    const token = this.getAuthToken();\n    const url = `${this.baseURL}${endpoint}`;\n\n    const defaultOptions = {\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      }\n    };\n\n    const finalOptions = {\n      ...defaultOptions,\n      ...options,\n      headers: {\n        ...defaultOptions.headers,\n        ...options.headers\n      }\n    };\n\n    try {\n      const response = await fetch(url, finalOptions);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.warn(`API request failed for ${endpoint}, using localStorage fallback:`, error.message);\n      // Fallback to localStorage for demo purposes\n      return this.fallbackToLocalStorage(endpoint, options);\n    }\n  }\n\n  // Fallback to localStorage when API is not available\n  fallbackToLocalStorage(endpoint, options) {\n    const method = options.method || 'GET';\n    const data = options.body ? JSON.parse(options.body) : null;\n\n    // Handle different endpoints with existing localStorage methods\n    if (endpoint.includes('/bodymap')) {\n      return this.handleBodyMapFallback(endpoint, method, data);\n    } else if (endpoint.includes('/communication')) {\n      return this.handleCommunicationFallback(endpoint, method, data);\n    } else if (endpoint.includes('/exercise-programs')) {\n      return this.handleExerciseFallback(endpoint, method, data);\n    } else if (endpoint.includes('/ai-interactions')) {\n      return this.handleAIFallback(endpoint, method, data);\n    }\n\n    return { success: false, error: 'Endpoint not supported in fallback mode' };\n  }\n\n  // Initialize storage with demo data\n  initializeStorage() {\n    if (!this.getItem('initialized')) {\n      this.setItem('initialized', true);\n      this.setItem('patients', this.getDefaultPatients());\n      this.setItem('bodyMapData', {});\n      this.setItem('communicationHistory', []);\n      this.setItem('exercisePrograms', []);\n      this.setItem('aiInteractions', []);\n    }\n  }\n\n  // Storage helpers\n  getItem(key) {\n    try {\n      const item = localStorage.getItem(this.storagePrefix + key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Error getting item from storage:', error);\n      return null;\n    }\n  }\n\n  setItem(key, value) {\n    try {\n      localStorage.setItem(this.storagePrefix + key, JSON.stringify(value));\n      return true;\n    } catch (error) {\n      console.error('Error setting item in storage:', error);\n      return false;\n    }\n  }\n\n  removeItem(key) {\n    try {\n      localStorage.removeItem(this.storagePrefix + key);\n      return true;\n    } catch (error) {\n      console.error('Error removing item from storage:', error);\n      return false;\n    }\n  }\n\n  // Default demo data\n  getDefaultPatients() {\n    return [\n      {\n        id: 'demo-patient-001',\n        name: 'Ahmed Mohammed',\n        nameAr: 'أحمد محمد',\n        age: 28,\n        gender: 'male',\n        condition: 'Cerebral Palsy',\n        conditionAr: 'الشلل الدماغي',\n        phone: '+966501234567',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'whatsapp'],\n          language: 'en',\n          quietHours: { start: '22:00', end: '08:00' }\n        },\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 'demo-patient-002',\n        name: 'Sarah Johnson',\n        nameAr: 'سارة جونسون',\n        age: 35,\n        gender: 'female',\n        condition: 'Spinal Cord Injury',\n        conditionAr: 'إصابة الحبل الشوكي',\n        phone: '+**********',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'sms'],\n          language: 'en',\n          quietHours: { start: '21:00', end: '07:00' }\n        },\n        createdAt: new Date().toISOString()\n      }\n    ];\n  }\n\n  // Patient data methods\n  async getPatients() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve(this.getItem('patients') || []);\n      }, 100);\n    });\n  }\n\n  async getPatient(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const patients = this.getItem('patients') || [];\n        const patient = patients.find(p => p.id === patientId);\n        resolve(patient || null);\n      }, 100);\n    });\n  }\n\n  // Body Map data methods\n  async saveBodyMapData(patientId, bodyMapData) {\n    try {\n      const response = await this.apiRequest('/bodymap', {\n        method: 'POST',\n        body: JSON.stringify({\n          patientId,\n          ...bodyMapData\n        })\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const allBodyMapData = this.getItem('bodyMapData') || {};\n          allBodyMapData[patientId] = {\n            ...bodyMapData,\n            timestamp: new Date().toISOString()\n          };\n          this.setItem('bodyMapData', allBodyMapData);\n          resolve(true);\n        }, 200);\n      });\n    }\n  }\n\n  async getBodyMapData(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const allBodyMapData = this.getItem('bodyMapData') || {};\n        resolve(allBodyMapData[patientId] || null);\n      }, 100);\n    });\n  }\n\n  // Communication methods\n  async sendMessage(messageData) {\n    try {\n      const response = await this.apiRequest('/communication', {\n        method: 'POST',\n        body: JSON.stringify(messageData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const message = {\n            id: Date.now().toString(),\n            ...messageData,\n            timestamp: new Date().toISOString(),\n            status: 'sent'\n          };\n          history.push(message);\n          this.setItem('communicationHistory', history);\n          resolve(message);\n        }, 500);\n      });\n    }\n  }\n\n  async getCommunicationHistory(patientId) {\n    try {\n      const response = await this.apiRequest(`/communication/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const patientHistory = history.filter(msg => msg.patientId === patientId);\n          resolve(patientHistory);\n        }, 100);\n      });\n    }\n  }\n\n  // Exercise Program methods\n  async saveExerciseProgram(programData) {\n    try {\n      const response = await this.apiRequest('/exercise-programs', {\n        method: 'POST',\n        body: JSON.stringify(programData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const program = {\n            id: Date.now().toString(),\n            ...programData,\n            createdAt: new Date().toISOString()\n          };\n          programs.push(program);\n          this.setItem('exercisePrograms', programs);\n          resolve(program);\n        }, 300);\n      });\n    }\n  }\n\n  async getExercisePrograms(patientId) {\n    try {\n      const response = await this.apiRequest(`/exercise-programs/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const patientPrograms = programs.filter(prog => prog.patientId === patientId);\n          resolve(patientPrograms);\n        }, 100);\n      });\n    }\n  }\n\n  // AI Interaction methods\n  async saveAIInteraction(interactionData) {\n    try {\n      const response = await this.apiRequest('/ai-interactions', {\n        method: 'POST',\n        body: JSON.stringify(interactionData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const interaction = {\n            id: Date.now().toString(),\n            ...interactionData,\n            timestamp: new Date().toISOString()\n          };\n          interactions.push(interaction);\n          this.setItem('aiInteractions', interactions);\n          resolve(interaction);\n        }, 100);\n      });\n    }\n  }\n\n  async getAIInteractions(patientId) {\n    try {\n      const endpoint = patientId ? `/ai-interactions/${patientId}` : '/ai-interactions';\n      const response = await this.apiRequest(endpoint);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const patientInteractions = patientId\n            ? interactions.filter(int => int.patientId === patientId)\n            : interactions;\n          resolve(patientInteractions);\n        }, 100);\n      });\n    }\n  }\n\n  // Analytics methods\n  async getAnalytics() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const interactions = this.getItem('aiInteractions') || [];\n        const communications = this.getItem('communicationHistory') || [];\n        const programs = this.getItem('exercisePrograms') || [];\n        const bodyMapData = this.getItem('bodyMapData') || {};\n\n        const analytics = {\n          totalAIQueries: interactions.length,\n          totalCommunications: communications.length,\n          totalExercisePrograms: programs.length,\n          totalBodyMapAssessments: Object.keys(bodyMapData).length,\n          recentActivity: [\n            ...interactions.slice(-5).map(i => ({ type: 'ai', ...i })),\n            ...communications.slice(-5).map(c => ({ type: 'communication', ...c })),\n            ...programs.slice(-5).map(p => ({ type: 'exercise', ...p }))\n          ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10)\n        };\n\n        resolve(analytics);\n      }, 200);\n    });\n  }\n\n  // Clear all demo data\n  clearAllData() {\n    const keys = ['patients', 'bodyMapData', 'communicationHistory', 'exercisePrograms', 'aiInteractions'];\n    keys.forEach(key => this.removeItem(key));\n    this.removeItem('initialized');\n    this.initializeStorage();\n  }\n\n  // Export data for backup\n  exportData() {\n    const data = {\n      patients: this.getItem('patients'),\n      bodyMapData: this.getItem('bodyMapData'),\n      communicationHistory: this.getItem('communicationHistory'),\n      exercisePrograms: this.getItem('exercisePrograms'),\n      aiInteractions: this.getItem('aiInteractions'),\n      exportedAt: new Date().toISOString()\n    };\n    return data;\n  }\n\n  // Import data from backup\n  importData(data) {\n    try {\n      if (data.patients) this.setItem('patients', data.patients);\n      if (data.bodyMapData) this.setItem('bodyMapData', data.bodyMapData);\n      if (data.communicationHistory) this.setItem('communicationHistory', data.communicationHistory);\n      if (data.exercisePrograms) this.setItem('exercisePrograms', data.exercisePrograms);\n      if (data.aiInteractions) this.setItem('aiInteractions', data.aiInteractions);\n      return true;\n    } catch (error) {\n      console.error('Error importing data:', error);\n      return false;\n    }\n  }\n\n  // Simulate API delay\n  delay(ms = 100) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n\n// Create singleton instance\nconst dataService = new DataService();\n\nexport default dataService;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport CommunicationManager from '../../components/Communication/CommunicationManager';\nimport dataService from '../../services/dataService';\nimport toast from 'react-hot-toast';\n\nconst CommunicationPage = () => {\n  const { t, isRTL } = useLanguage();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n  const [patient, setPatient] = useState(null);\n  const [communicationHistory, setCommunicationHistory] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    totalMessages: 0,\n    emailCount: 0,\n    smsCount: 0,\n    whatsappCount: 0,\n    pushCount: 0\n  });\n\n  useEffect(() => {\n    loadPatientData();\n    loadCommunicationHistory();\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    if (patientId) {\n      try {\n        const patientData = await dataService.getPatient(patientId);\n        setPatient(patientData);\n      } catch (error) {\n        console.error('Error loading patient:', error);\n        toast.error(t('errorLoadingPatient', 'Error loading patient data'));\n      }\n    }\n    setLoading(false);\n  };\n\n  const loadCommunicationHistory = async () => {\n    if (patientId) {\n      try {\n        const history = await dataService.getCommunicationHistory(patientId);\n        setCommunicationHistory(history);\n        \n        // Calculate stats\n        const stats = {\n          totalMessages: history.length,\n          emailCount: history.filter(msg => msg.channels?.includes('email')).length,\n          smsCount: history.filter(msg => msg.channels?.includes('sms')).length,\n          whatsappCount: history.filter(msg => msg.channels?.includes('whatsapp')).length,\n          pushCount: history.filter(msg => msg.channels?.includes('push')).length\n        };\n        setStats(stats);\n      } catch (error) {\n        console.error('Error loading communication history:', error);\n      }\n    }\n  };\n\n  const handleMessageSend = async (messageData) => {\n    try {\n      await dataService.sendMessage({ ...messageData, patientId });\n      await loadCommunicationHistory();\n      toast.success(t('messageSent', 'Message sent successfully'));\n    } catch (error) {\n      console.error('Error sending message:', error);\n      toast.error(t('errorSending', 'Error sending message'));\n    }\n  };\n\n  const getChannelIcon = (channel) => {\n    switch (channel) {\n      case 'email': return 'fas fa-envelope';\n      case 'sms': return 'fas fa-sms';\n      case 'whatsapp': return 'fab fa-whatsapp';\n      case 'push': return 'fas fa-bell';\n      default: return 'fas fa-comment';\n    }\n  };\n\n  const getChannelColor = (channel) => {\n    switch (channel) {\n      case 'email': return 'text-blue-600';\n      case 'sms': return 'text-green-600';\n      case 'whatsapp': return 'text-green-500';\n      case 'push': return 'text-purple-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const getUrgencyColor = (urgency) => {\n    switch (urgency) {\n      case 'high': return 'bg-red-100 text-red-800';\n      case 'medium': return 'bg-yellow-100 text-yellow-800';\n      case 'low': return 'bg-green-100 text-green-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"communication-page min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => navigate(-1)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-arrow-left text-xl\"></i>\n                </button>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-comments mr-3 text-green-600\"></i>\n                    {t('communicationHub', 'Communication Hub')}\n                  </h1>\n                  {patient && (\n                    <p className=\"text-gray-600 dark:text-gray-300 mt-1\">\n                      {t('patient', 'Patient')}: {isRTL ? patient.nameAr : patient.name}\n                      <span className=\"ml-2 text-sm text-gray-500\">\n                        {patient.phone} • {patient.email}\n                      </span>\n                    </p>\n                  )}\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-robot mr-1\"></i>\n                    {t('smartRouting', 'Smart Routing')}\n                  </span>\n                  <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-clock mr-1\"></i>\n                    {t('scheduled', 'Scheduled')}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 xl:grid-cols-3 gap-8\">\n          {/* Communication Manager */}\n          <div className=\"xl:col-span-2\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                {t('sendMessage', 'Send Message')}\n              </h2>\n              \n              <CommunicationManager\n                patientId={patientId}\n                onSend={handleMessageSend}\n              />\n            </div>\n          </div>\n\n          {/* Stats and History Sidebar */}\n          <div className=\"xl:col-span-1 space-y-6\">\n            {/* Communication Stats */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                <i className=\"fas fa-chart-bar mr-2 text-gray-600\"></i>\n                {t('communicationStats', 'Communication Stats')}\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('totalMessages', 'Total Messages')}\n                  </span>\n                  <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {stats.totalMessages}\n                  </span>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <i className=\"fas fa-envelope text-blue-600\"></i>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Email</span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {stats.emailCount}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <i className=\"fas fa-sms text-green-600\"></i>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">SMS</span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {stats.smsCount}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <i className=\"fab fa-whatsapp text-green-500\"></i>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">WhatsApp</span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {stats.whatsappCount}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <i className=\"fas fa-bell text-purple-600\"></i>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">Push</span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {stats.pushCount}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Recent Messages */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                <i className=\"fas fa-history mr-2 text-gray-600\"></i>\n                {t('recentMessages', 'Recent Messages')}\n              </h3>\n              \n              {communicationHistory.length > 0 ? (\n                <div className=\"space-y-3 max-h-96 overflow-y-auto\">\n                  {communicationHistory.slice(0, 10).map((message, index) => (\n                    <div\n                      key={index}\n                      className=\"p-3 border border-gray-200 dark:border-gray-600 rounded-lg\"\n                    >\n                      <div className=\"flex items-start justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          {message.channels?.map((channel, idx) => (\n                            <i\n                              key={idx}\n                              className={`${getChannelIcon(channel)} ${getChannelColor(channel)} text-sm`}\n                            />\n                          ))}\n                          <span className={`text-xs px-2 py-1 rounded-full ${getUrgencyColor(message.urgency)}`}>\n                            {message.urgency || 'normal'}\n                          </span>\n                        </div>\n                        <span className=\"text-xs text-gray-500\">\n                          {new Date(message.timestamp).toLocaleDateString()}\n                        </span>\n                      </div>\n                      \n                      <div className=\"text-sm text-gray-900 dark:text-white font-medium mb-1\">\n                        {message.subject || t('noSubject', 'No Subject')}\n                      </div>\n                      \n                      <div className=\"text-sm text-gray-600 dark:text-gray-400 line-clamp-2\">\n                        {message.message}\n                      </div>\n                      \n                      <div className=\"flex items-center justify-between mt-2\">\n                        <span className=\"text-xs text-gray-500\">\n                          {new Date(message.timestamp).toLocaleTimeString()}\n                        </span>\n                        <span className={`text-xs px-2 py-1 rounded-full ${\n                          message.status === 'sent' ? 'bg-green-100 text-green-800' : \n                          message.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :\n                          'bg-red-100 text-red-800'\n                        }`}>\n                          {message.status || 'sent'}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <i className=\"fas fa-comment-slash text-4xl text-gray-300 mb-4\"></i>\n                  <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n                    {t('noMessages', 'No messages yet')}\n                  </p>\n                  <p className=\"text-gray-400 dark:text-gray-500 text-xs mt-1\">\n                    {t('sendFirstMessage', 'Send your first message to get started')}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CommunicationPage;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst CommunicationManager = ({ patientId, onSend, templates = [] }) => {\n  const { t, isRTL } = useLanguage();\n  const [selectedChannels, setSelectedChannels] = useState(['email']);\n  const [message, setMessage] = useState('');\n  const [subject, setSubject] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState('');\n  const [urgency, setUrgency] = useState('normal');\n  const [scheduledTime, setScheduledTime] = useState('');\n  const [patientPreferences, setPatientPreferences] = useState({});\n\n  // Communication channels\n  const channels = [\n    {\n      id: 'email',\n      name: t('email', 'Email'),\n      icon: 'fas fa-envelope',\n      color: 'blue',\n      available: true,\n      description: t('emailDesc', 'Send via email')\n    },\n    {\n      id: 'sms',\n      name: t('sms', 'SMS'),\n      icon: 'fas fa-sms',\n      color: 'green',\n      available: true,\n      description: t('smsDesc', 'Send via SMS')\n    },\n    {\n      id: 'whatsapp',\n      name: t('whatsapp', 'WhatsApp'),\n      icon: 'fab fa-whatsapp',\n      color: 'green',\n      available: true,\n      description: t('whatsappDesc', 'Send via WhatsApp')\n    },\n    {\n      id: 'push',\n      name: t('pushNotification', 'Push Notification'),\n      icon: 'fas fa-bell',\n      color: 'purple',\n      available: true,\n      description: t('pushDesc', 'Send push notification to mobile app')\n    },\n    {\n      id: 'telegram',\n      name: t('telegram', 'Telegram'),\n      icon: 'fab fa-telegram',\n      color: 'blue',\n      available: false,\n      description: t('telegramDesc', 'Send via Telegram (Coming Soon)')\n    }\n  ];\n\n  // Message templates\n  const messageTemplates = [\n    {\n      id: 'appointment_reminder',\n      name: t('appointmentReminder', 'Appointment Reminder'),\n      subject: t('appointmentReminderSubject', 'Appointment Reminder'),\n      content: t('appointmentReminderContent', 'Dear {patientName}, this is a reminder for your appointment on {date} at {time}. Please arrive 15 minutes early.'),\n      channels: ['email', 'sms', 'whatsapp', 'push']\n    },\n    {\n      id: 'exercise_instructions',\n      name: t('exerciseInstructions', 'Exercise Instructions'),\n      subject: t('exerciseInstructionsSubject', 'Your Exercise Program'),\n      content: t('exerciseInstructionsContent', 'Dear {patientName}, please find your personalized exercise program. Follow the instructions carefully and contact us if you have any questions.'),\n      channels: ['email', 'whatsapp']\n    },\n    {\n      id: 'progress_update',\n      name: t('progressUpdate', 'Progress Update'),\n      subject: t('progressUpdateSubject', 'Your Progress Update'),\n      content: t('progressUpdateContent', 'Dear {patientName}, here is your latest progress update. Keep up the great work!'),\n      channels: ['email', 'whatsapp', 'push']\n    },\n    {\n      id: 'medication_reminder',\n      name: t('medicationReminder', 'Medication Reminder'),\n      subject: t('medicationReminderSubject', 'Medication Reminder'),\n      content: t('medicationReminderContent', 'Time to take your medication: {medicationName}. Follow the prescribed dosage.'),\n      channels: ['sms', 'push']\n    },\n    {\n      id: 'custom',\n      name: t('customMessage', 'Custom Message'),\n      subject: '',\n      content: '',\n      channels: ['email', 'sms', 'whatsapp', 'push']\n    }\n  ];\n\n  // Load patient communication preferences\n  useEffect(() => {\n    loadPatientPreferences();\n  }, [patientId]);\n\n  const loadPatientPreferences = async () => {\n    try {\n      // Mock patient preferences - replace with actual API call\n      const preferences = {\n        preferredChannels: ['email', 'whatsapp'],\n        language: 'en',\n        timezone: 'Asia/Riyadh',\n        quietHours: { start: '22:00', end: '08:00' },\n        urgentOnly: false,\n        emailFrequency: 'all',\n        smsFrequency: 'urgent',\n        whatsappFrequency: 'all',\n        pushFrequency: 'all'\n      };\n      setPatientPreferences(preferences);\n      setSelectedChannels(preferences.preferredChannels);\n    } catch (error) {\n      console.error('Error loading patient preferences:', error);\n    }\n  };\n\n  const handleTemplateSelect = (templateId) => {\n    const template = messageTemplates.find(t => t.id === templateId);\n    if (template) {\n      setSelectedTemplate(templateId);\n      setSubject(template.subject);\n      setMessage(template.content);\n      \n      // Auto-select appropriate channels for this template\n      const availableChannels = template.channels.filter(channel => \n        channels.find(c => c.id === channel && c.available)\n      );\n      setSelectedChannels(availableChannels);\n    }\n  };\n\n  const handleChannelToggle = (channelId) => {\n    setSelectedChannels(prev => \n      prev.includes(channelId)\n        ? prev.filter(id => id !== channelId)\n        : [...prev, channelId]\n    );\n  };\n\n  const getRecommendedChannels = () => {\n    if (urgency === 'urgent') {\n      return ['sms', 'push', 'whatsapp'];\n    } else if (urgency === 'high') {\n      return ['email', 'whatsapp', 'push'];\n    } else {\n      return patientPreferences.preferredChannels || ['email'];\n    }\n  };\n\n  const handleAutoSelect = () => {\n    const recommended = getRecommendedChannels();\n    setSelectedChannels(recommended);\n    toast.success(t('channelsAutoSelected', 'Channels auto-selected based on urgency and patient preferences'));\n  };\n\n  const validateMessage = () => {\n    if (!message.trim()) {\n      toast.error(t('messageRequired', 'Message content is required'));\n      return false;\n    }\n    \n    if (selectedChannels.includes('email') && !subject.trim()) {\n      toast.error(t('subjectRequired', 'Subject is required for email'));\n      return false;\n    }\n    \n    if (selectedChannels.length === 0) {\n      toast.error(t('selectChannel', 'Please select at least one communication channel'));\n      return false;\n    }\n    \n    return true;\n  };\n\n  const handleSend = async () => {\n    if (!validateMessage()) return;\n\n    const communicationData = {\n      patientId,\n      channels: selectedChannels,\n      message,\n      subject,\n      urgency,\n      scheduledTime: scheduledTime || null,\n      template: selectedTemplate,\n      timestamp: new Date().toISOString()\n    };\n\n    try {\n      if (onSend) {\n        await onSend(communicationData);\n      }\n      \n      // Mock sending logic\n      console.log('Sending communication:', communicationData);\n      \n      toast.success(t('messageSent', 'Message sent successfully via selected channels'));\n      \n      // Reset form\n      setMessage('');\n      setSubject('');\n      setSelectedTemplate('');\n      setScheduledTime('');\n    } catch (error) {\n      toast.error(t('sendError', 'Error sending message'));\n      console.error('Send error:', error);\n    }\n  };\n\n  return (\n    <div className=\"communication-manager\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            <i className=\"fas fa-comments mr-2 text-blue-600\"></i>\n            {t('communicationCenter', 'Communication Center')}\n          </h3>\n          <button\n            onClick={handleAutoSelect}\n            className=\"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\"\n          >\n            <i className=\"fas fa-magic mr-1\"></i>\n            {t('autoSelect', 'Auto Select')}\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Message Composition */}\n          <div className=\"space-y-4\">\n            {/* Template Selection */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('messageTemplate', 'Message Template')}\n              </label>\n              <select\n                value={selectedTemplate}\n                onChange={(e) => handleTemplateSelect(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              >\n                <option value=\"\">{t('selectTemplate', 'Select a template')}</option>\n                {messageTemplates.map(template => (\n                  <option key={template.id} value={template.id}>\n                    {template.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject (for email) */}\n            {selectedChannels.includes('email') && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('subject', 'Subject')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={subject}\n                  onChange={(e) => setSubject(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterSubject', 'Enter email subject')}\n                />\n              </div>\n            )}\n\n            {/* Message Content */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('message', 'Message')}\n              </label>\n              <textarea\n                value={message}\n                onChange={(e) => setMessage(e.target.value)}\n                rows={6}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none\"\n                placeholder={t('enterMessage', 'Enter your message...')}\n              />\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                {message.length}/1000 {t('characters', 'characters')}\n              </div>\n            </div>\n\n            {/* Urgency */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('urgency', 'Urgency')}\n              </label>\n              <select\n                value={urgency}\n                onChange={(e) => setUrgency(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              >\n                <option value=\"low\">{t('lowUrgency', 'Low')}</option>\n                <option value=\"normal\">{t('normalUrgency', 'Normal')}</option>\n                <option value=\"high\">{t('highUrgency', 'High')}</option>\n                <option value=\"urgent\">{t('urgentUrgency', 'Urgent')}</option>\n              </select>\n            </div>\n\n            {/* Scheduled Time */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('scheduleTime', 'Schedule Time')} ({t('optional', 'Optional')})\n              </label>\n              <input\n                type=\"datetime-local\"\n                value={scheduledTime}\n                onChange={(e) => setScheduledTime(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              />\n            </div>\n          </div>\n\n          {/* Channel Selection */}\n          <div className=\"space-y-4\">\n            <div>\n              <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                {t('communicationChannels', 'Communication Channels')}\n              </h4>\n              <div className=\"space-y-3\">\n                {channels.map(channel => (\n                  <div\n                    key={channel.id}\n                    className={`p-3 border rounded-lg transition-all cursor-pointer ${\n                      selectedChannels.includes(channel.id)\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                    } ${!channel.available ? 'opacity-50 cursor-not-allowed' : ''}`}\n                    onClick={() => channel.available && handleChannelToggle(channel.id)}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <i className={`${channel.icon} text-${channel.color}-600 text-lg`}></i>\n                        <div>\n                          <div className=\"font-medium text-gray-900 dark:text-white\">\n                            {channel.name}\n                          </div>\n                          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                            {channel.description}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {!channel.available && (\n                          <span className=\"text-xs text-gray-400\">\n                            {t('comingSoon', 'Coming Soon')}\n                          </span>\n                        )}\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedChannels.includes(channel.id)}\n                          onChange={() => channel.available && handleChannelToggle(channel.id)}\n                          disabled={!channel.available}\n                          className=\"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Patient Preferences */}\n            {patientPreferences.preferredChannels && (\n              <div className=\"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('patientPreferences', 'Patient Preferences')}\n                </h5>\n                <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                  {t('preferredChannels', 'Preferred')}: {patientPreferences.preferredChannels.join(', ')}\n                </div>\n                {patientPreferences.quietHours && (\n                  <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {t('quietHours', 'Quiet Hours')}: {patientPreferences.quietHours.start} - {patientPreferences.quietHours.end}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* Send Button */}\n            <button\n              onClick={handleSend}\n              disabled={selectedChannels.length === 0 || !message.trim()}\n              className=\"w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              <i className=\"fas fa-paper-plane mr-2\"></i>\n              {scheduledTime ? t('scheduleMessage', 'Schedule Message') : t('sendMessage', 'Send Message')}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CommunicationManager;\n\n// Communication History Component\nexport const CommunicationHistory = ({ patientId }) => {\n  const { t, isRTL } = useLanguage();\n  const [history, setHistory] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n\n  useEffect(() => {\n    loadCommunicationHistory();\n  }, [patientId, filter]);\n\n  const loadCommunicationHistory = async () => {\n    setLoading(true);\n    try {\n      // Mock communication history - replace with actual API call\n      const mockHistory = [\n        {\n          id: 1,\n          type: 'appointment_reminder',\n          channels: ['email', 'sms'],\n          subject: 'Appointment Reminder',\n          message: 'Your appointment is tomorrow at 2:00 PM',\n          status: 'delivered',\n          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n          deliveryStatus: {\n            email: 'delivered',\n            sms: 'delivered'\n          }\n        },\n        {\n          id: 2,\n          type: 'exercise_instructions',\n          channels: ['whatsapp'],\n          subject: 'Exercise Program',\n          message: 'Please find your updated exercise program',\n          status: 'read',\n          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n          deliveryStatus: {\n            whatsapp: 'read'\n          }\n        }\n      ];\n\n      setHistory(mockHistory);\n    } catch (error) {\n      console.error('Error loading communication history:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'sent': return 'fas fa-paper-plane text-blue-500';\n      case 'delivered': return 'fas fa-check text-green-500';\n      case 'read': return 'fas fa-check-double text-green-600';\n      case 'failed': return 'fas fa-exclamation-triangle text-red-500';\n      default: return 'fas fa-clock text-gray-400';\n    }\n  };\n\n  const getChannelIcon = (channel) => {\n    switch (channel) {\n      case 'email': return 'fas fa-envelope';\n      case 'sms': return 'fas fa-sms';\n      case 'whatsapp': return 'fab fa-whatsapp';\n      case 'push': return 'fas fa-bell';\n      default: return 'fas fa-comment';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"communication-history\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            <i className=\"fas fa-history mr-2 text-gray-600\"></i>\n            {t('communicationHistory', 'Communication History')}\n          </h3>\n          <select\n            value={filter}\n            onChange={(e) => setFilter(e.target.value)}\n            className=\"px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n          >\n            <option value=\"all\">{t('allMessages', 'All Messages')}</option>\n            <option value=\"email\">{t('emailOnly', 'Email Only')}</option>\n            <option value=\"sms\">{t('smsOnly', 'SMS Only')}</option>\n            <option value=\"whatsapp\">{t('whatsappOnly', 'WhatsApp Only')}</option>\n          </select>\n        </div>\n\n        {history.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <i className=\"fas fa-inbox text-4xl text-gray-400 mb-4\"></i>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('noCommunicationHistory', 'No communication history found')}\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {history.map(item => (\n              <div key={item.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <i className={getStatusIcon(item.status)}></i>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                        {item.subject}\n                      </h4>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {new Date(item.timestamp).toLocaleString()}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    {item.channels.map(channel => (\n                      <div key={channel} className=\"flex items-center space-x-1\">\n                        <i className={`${getChannelIcon(channel)} text-sm text-gray-500`}></i>\n                        <span className=\"text-xs text-gray-500\">\n                          {item.deliveryStatus[channel]}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                <p className=\"text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-2 rounded\">\n                  {item.message}\n                </p>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n"], "names": ["constructor", "this", "baseURL", "process", "storagePrefix", "initializeStorage", "getAuthToken", "localStorage", "getItem", "apiRequest", "endpoint", "options", "arguments", "length", "undefined", "token", "url", "concat", "defaultOptions", "headers", "finalOptions", "_objectSpread", "response", "fetch", "ok", "Error", "status", "json", "error", "console", "warn", "message", "fallbackToLocalStorage", "method", "data", "body", "JSON", "parse", "includes", "handleBodyMapFallback", "handleCommunicationFallback", "handleExerciseFallback", "handleAIFallback", "success", "setItem", "getDefaultPatients", "key", "item", "value", "stringify", "removeItem", "id", "name", "nameAr", "age", "gender", "condition", "conditionAr", "phone", "email", "communicationPreferences", "preferredChannels", "language", "quietHours", "start", "end", "createdAt", "Date", "toISOString", "getPatients", "Promise", "resolve", "setTimeout", "getPatient", "patientId", "patient", "find", "p", "saveBodyMapData", "bodyMapData", "allBodyMapData", "timestamp", "getBodyMapData", "sendMessage", "messageData", "history", "now", "toString", "push", "getCommunicationHistory", "patientHistory", "filter", "msg", "saveExerciseProgram", "programData", "programs", "program", "getExercisePrograms", "patientPrograms", "prog", "saveAIInteraction", "interactionData", "interactions", "interaction", "getAIInteractions", "patientInteractions", "int", "getAnalytics", "communications", "analytics", "totalAIQueries", "totalCommunications", "totalExercisePrograms", "totalBodyMapAssessments", "Object", "keys", "recentActivity", "slice", "map", "i", "type", "c", "sort", "a", "b", "clearAllData", "for<PERSON>ach", "exportData", "patients", "communicationHistory", "exercisePrograms", "aiInteractions", "exportedAt", "importData", "delay", "ms", "CommunicationPage", "t", "isRTL", "useLanguage", "useParams", "navigate", "useNavigate", "setPatient", "useState", "setCommunicationHistory", "loading", "setLoading", "stats", "setStats", "totalMessages", "emailCount", "smsCount", "whatsappCount", "pushCount", "useEffect", "loadPatientData", "loadCommunicationHistory", "async", "patientData", "dataService", "toast", "_msg$channels", "channels", "_msg$channels2", "_msg$channels3", "_msg$channels4", "getChannelIcon", "channel", "getChannelColor", "getUrgencyColor", "urgency", "_jsx", "className", "children", "_jsxs", "onClick", "CommunicationManager", "onSend", "index", "_message$channels", "idx", "toLocaleDateString", "subject", "toLocaleTimeString", "_ref", "templates", "selectedChannels", "setSelectedChannels", "setMessage", "setSubject", "selectedTemplate", "setSelectedTemplate", "setUrgency", "scheduledTime", "setScheduledTime", "patientPreferences", "setPatientPreferences", "icon", "color", "available", "description", "messageTemplates", "content", "loadPatientPreferences", "preferences", "timezone", "urgentOnly", "emailFrequency", "smsFrequency", "whatsappFrequency", "pushFrequency", "handleChannelToggle", "channelId", "prev", "handleAutoSelect", "recommended", "onChange", "e", "templateId", "template", "availableChannels", "handleTemplateSelect", "target", "placeholder", "rows", "checked", "disabled", "join", "trim", "communicationData", "log"], "sourceRoot": ""}