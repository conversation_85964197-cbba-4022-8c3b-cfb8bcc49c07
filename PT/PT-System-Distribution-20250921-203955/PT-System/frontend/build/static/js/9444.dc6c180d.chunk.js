"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9444],{9444:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(2555),n=s(5043),i=s(7921),r=(s(3768),s(579));const o=()=>{const{t:e,isRTL:t}=(0,i.o)(),[s,o]=(0,n.useState)([]),[c,l]=(0,n.useState)(null),[d,m]=(0,n.useState)(!0),p={admin:{name:e("admin","\u0645\u062f\u064a\u0631 \u0627\u0644\u0646\u0638\u0627\u0645"),nameEn:"Administrator",description:e("adminDesc","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0643\u0627\u0645\u0644\u0629 \u0644\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0646\u0638\u0627\u0645 \u0648\u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"),color:"gray",icon:"fas fa-crown",userCount:2,permissions:{forms:{medicalDiagnosis:!0,ptEvaluation:!0,reassessment:!0,dischargeEvaluation:!0,followUpForm:!0,patientEducationDoctor:!0,progressNotes:!0,patientEducationTherapist:!0,patientEducationNurse:!0,nursingForm:!0,clinicalIndicators:!0,labResults:!0,radiologyResults:!0},system:{viewPatients:!0,createPatients:!0,editPatients:!0,deletePatients:!0,viewAppointments:!0,createAppointments:!0,editAppointments:!0,deleteAppointments:!0,viewAnalytics:!0,manageUsers:!0,systemSettings:!0},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!0}}},doctor:{name:e("doctor","\u0637\u0628\u064a\u0628"),nameEn:"Doctor",description:e("doctorDesc","\u0627\u0644\u062a\u0634\u062e\u064a\u0635 \u0627\u0644\u0637\u0628\u064a \u0648\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a \u0648\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u062a\u0642\u064a\u064a\u0645"),color:"blue",icon:"fas fa-user-md",userCount:5,permissions:{forms:{medicalDiagnosis:!0,ptEvaluation:!0,reassessment:!0,dischargeEvaluation:!0,followUpForm:!0,patientEducationDoctor:!0,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!0,labResults:!0,radiologyResults:!0},system:{viewPatients:!0,createPatients:!0,editPatients:!0,deletePatients:!1,viewAppointments:!0,createAppointments:!0,editAppointments:!0,deleteAppointments:!1,viewAnalytics:!0,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!0}}},therapist:{name:e("therapist","\u0623\u062e\u0635\u0627\u0626\u064a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),nameEn:"Physical Therapist",description:e("therapistDesc","\u0645\u0644\u0627\u062d\u0638\u0627\u062a \u062a\u0642\u062f\u0645 \u0627\u0644\u062d\u0627\u0644\u0629 \u0648\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636"),color:"green",icon:"fas fa-hands-helping",userCount:8,permissions:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!0,patientEducationTherapist:!0,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!0,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!0,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!0,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!1}}},nurse:{name:e("nurse","\u0645\u0645\u0631\u0636/\u0645\u0645\u0631\u0636\u0629"),nameEn:"Nurse",description:e("nurseDesc","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 \u0648\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u062a\u0645\u0631\u064a\u0636 \u0627\u0644\u062a\u0627\u0628\u0639 \u0644\u0644\u0645\u0631\u0643\u0632"),color:"pink",icon:"fas fa-user-nurse",userCount:12,permissions:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!0,nursingForm:!0,clinicalIndicators:!1,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!0,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}}},external_lab:{name:e("externalLab","\u0645\u062e\u062a\u0628\u0631 \u062e\u0627\u0631\u062c\u064a"),nameEn:"External Laboratory",description:e("externalLabDesc","\u0625\u062f\u062e\u0627\u0644 \u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0645\u062e\u062a\u0628\u0631 \u0644\u0644\u0645\u0631\u0636\u0649 \u0627\u0644\u0645\u062d\u0648\u0644\u064a\u0646"),color:"purple",icon:"fas fa-flask",userCount:3,permissions:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!1,labResults:!0,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!1,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}}},external_radiology:{name:e("externalRadiology","\u0645\u0631\u0643\u0632 \u0623\u0634\u0639\u0629 \u062e\u0627\u0631\u062c\u064a"),nameEn:"External Radiology",description:e("externalRadiologyDesc","\u0625\u062f\u062e\u0627\u0644 \u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0623\u0634\u0639\u0629 \u0644\u0644\u0645\u0631\u0636\u0649 \u0627\u0644\u0645\u062d\u0648\u0644\u064a\u0646"),color:"indigo",icon:"fas fa-x-ray",userCount:2,permissions:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!1,labResults:!1,radiologyResults:!0},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!1,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}}},receptionist:{name:e("receptionist","\u0645\u0648\u0638\u0641 \u0627\u0633\u062a\u0642\u0628\u0627\u0644"),nameEn:"Receptionist",description:e("receptionistDesc","\u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u0645\u0631\u0636\u0649 \u0648\u062d\u062c\u0632 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),color:"yellow",icon:"fas fa-concierge-bell",userCount:4,permissions:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!1,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!0,editPatients:!0,deletePatients:!1,viewAppointments:!0,createAppointments:!0,editAppointments:!0,deleteAppointments:!0,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}}},manager:{name:e("manager","\u0645\u062f\u064a\u0631"),nameEn:"Manager",description:e("managerDesc","\u0639\u0631\u0636 \u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a \u0648\u0627\u0644\u062a\u0642\u0627\u0631\u064a\u0631 \u0648\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"),color:"red",icon:"fas fa-user-tie",userCount:1,permissions:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!1,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!0,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!0,manageUsers:!0,systemSettings:!0},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!0}}}},g={forms:{name:e("formsPermissions","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u0646\u0645\u0627\u0630\u062c"),nameEn:"Forms Permissions",permissions:{medicalDiagnosis:e("medicalDiagnosis","\u0627\u0644\u062a\u0634\u062e\u064a\u0635 \u0627\u0644\u0637\u0628\u064a"),ptEvaluation:e("ptEvaluation","\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),reassessment:e("reassessment","\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u062a\u0642\u064a\u064a\u0645"),dischargeEvaluation:e("dischargeEvaluation","\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u062e\u0631\u0648\u062c"),followUpForm:e("followUpForm","\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u0645\u062a\u0627\u0628\u0639\u0629"),patientEducationDoctor:e("patientEducationDoctor","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u0637\u0628\u064a\u0628)"),progressNotes:e("progressNotes","\u0645\u0644\u0627\u062d\u0638\u0627\u062a \u062a\u0642\u062f\u0645 \u0627\u0644\u062d\u0627\u0644\u0629"),patientEducationTherapist:e("patientEducationTherapist","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u0623\u062e\u0635\u0627\u0626\u064a)"),patientEducationNurse:e("patientEducationNurse","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u062a\u0645\u0631\u064a\u0636)"),nursingForm:e("nursingForm","\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u062a\u0645\u0631\u064a\u0636 \u0627\u0644\u062a\u0627\u0628\u0639 \u0644\u0644\u0645\u0631\u0643\u0632"),clinicalIndicators:e("clinicalIndicators","\u0627\u0644\u0645\u0624\u0634\u0631\u0627\u062a \u0627\u0644\u0633\u0631\u064a\u0631\u064a\u0629"),labResults:e("labResults","\u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0645\u062e\u062a\u0628\u0631"),radiologyResults:e("radiologyResults","\u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0623\u0634\u0639\u0629")}},system:{name:e("systemPermissions","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u0646\u0638\u0627\u0645"),nameEn:"System Permissions",permissions:{viewPatients:e("viewPatients","\u0639\u0631\u0636 \u0627\u0644\u0645\u0631\u0636\u0649"),createPatients:e("createPatients","\u0625\u0646\u0634\u0627\u0621 \u0645\u0631\u0636\u0649"),editPatients:e("editPatients","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0631\u0636\u0649"),deletePatients:e("deletePatients","\u062d\u0630\u0641 \u0627\u0644\u0645\u0631\u0636\u0649"),viewAppointments:e("viewAppointments","\u0639\u0631\u0636 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),createAppointments:e("createAppointments","\u0625\u0646\u0634\u0627\u0621 \u0645\u0648\u0627\u0639\u064a\u062f"),editAppointments:e("editAppointments","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),deleteAppointments:e("deleteAppointments","\u062d\u0630\u0641 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),viewAnalytics:e("viewAnalytics","\u0639\u0631\u0636 \u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a"),manageUsers:e("manageUsers","\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"),systemSettings:e("systemSettings","\u0625\u0639\u062f\u0627\u062f\u0627\u062a \u0627\u0644\u0646\u0638\u0627\u0645")}},analytics:{name:e("analyticsPermissions","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a"),nameEn:"Analytics Permissions",permissions:{functionalIndependenceComparison:e("functionalIndependenceComparison","\u062f\u0631\u062c\u0629 \u0627\u0644\u0627\u0633\u062a\u0642\u0644\u0627\u0644\u064a\u0629 \u0627\u0644\u0648\u0638\u064a\u0641\u064a\u0629"),treatmentPlanEffectiveness:e("treatmentPlanEffectiveness","\u0641\u0639\u0627\u0644\u064a\u0629 \u0627\u0644\u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c\u064a\u0629"),clinicalProgressTracking:e("clinicalProgressTracking","\u062a\u062a\u0628\u0639 \u0627\u0644\u062a\u0642\u062f\u0645 \u0627\u0644\u0633\u0631\u064a\u0631\u064a"),outcomeMetrics:e("outcomeMetrics","\u0645\u0642\u0627\u064a\u064a\u0633 \u0627\u0644\u0646\u062a\u0627\u0626\u062c"),complianceReporting:e("complianceReporting","\u062a\u0642\u0627\u0631\u064a\u0631 \u0627\u0644\u0627\u0645\u062a\u062b\u0627\u0644")}}};(0,n.useEffect)(()=>{o(Object.entries(p).map(e=>{let[t,s]=e;return(0,a.A)({id:t},s)})),m(!1)},[]);return d?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:e("roleManagement","\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0623\u062f\u0648\u0627\u0631")}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("roleManagementDesc","\u0639\u0631\u0636 \u0648\u0625\u062f\u0627\u0631\u0629 \u0623\u062f\u0648\u0627\u0631 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646 \u0648\u0635\u0644\u0627\u062d\u064a\u0627\u062a\u0647\u0645 \u0641\u064a \u0627\u0644\u0646\u0638\u0627\u0645")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("systemRoles","\u0623\u062f\u0648\u0627\u0631 \u0627\u0644\u0646\u0638\u0627\u0645")})}),(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)("div",{className:"space-y-3",children:s.map(t=>{const s=(e=>{let t=0,s=0;return Object.values(e).forEach(e=>{Object.values(e).forEach(e=>{t++,e&&s++})}),{total:t,granted:s}})(t.permissions);return(0,r.jsxs)("div",{onClick:()=>l(t),className:"p-4 rounded-lg border cursor-pointer transition-colors ".concat((null===c||void 0===c?void 0:c.id)===t.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"),children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-2",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"".concat(t.icon," text-").concat(t.color,"-600 dark:text-").concat(t.color,"-400 mr-3")}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:t.name}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:[t.userCount," ",e("users","\u0645\u0633\u062a\u062e\u062f\u0645")]})]})]})}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:t.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:[s.granted,"/",s.total," ",e("permissions","\u0635\u0644\u0627\u062d\u064a\u0629")]}),(0,r.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-1",children:(0,r.jsx)("div",{className:"bg-".concat(t.color,"-600 h-1 rounded-full"),style:{width:"".concat(s.granted/s.total*100,"%")}})})]})]},t.id)})})})]})}),(0,r.jsx)("div",{className:"lg:col-span-2",children:c?(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("i",{className:"".concat(c.icon," text-").concat(c.color,"-600 dark:text-").concat(c.color,"-400 text-2xl mr-4")}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:c.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:c.description}),(0,r.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[(0,r.jsx)("i",{className:"fas fa-users mr-1"}),c.userCount," ",e("users","\u0645\u0633\u062a\u062e\u062f\u0645")]}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-".concat(c.color,"-100 text-").concat(c.color,"-800 dark:bg-").concat(c.color,"-900/30 dark:text-").concat(c.color,"-200"),children:c.nameEn})]})]})]})})}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-6",children:e("rolePermissions","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u062f\u0648\u0631")}),Object.entries(g).map(e=>{let[t,s]=e;return(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("h5",{className:"text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,r.jsx)("i",{className:"fas ".concat("forms"===t?"fa-file-alt":"system"===t?"fa-cog":"fa-chart-bar"," mr-2 text-gray-500")}),s.name]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:Object.entries(s.permissions).map(e=>{var s;let[a,n]=e;const i=null===(s=c.permissions[t])||void 0===s?void 0:s[a];return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg ".concat(i?"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800":"bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600"),children:[(0,r.jsx)("span",{className:"text-sm ".concat(i?"text-green-900 dark:text-green-200":"text-gray-600 dark:text-gray-400"),children:n}),(0,r.jsx)("i",{className:"fas ".concat(i?"fa-check-circle text-green-600":"fa-times-circle text-gray-400")})]},a)})})]},t)})]})]}):(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("i",{className:"fas fa-user-tag text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("selectRole","\u0627\u062e\u062a\u0631 \u062f\u0648\u0631")}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("selectRoleDesc","\u0627\u062e\u062a\u0631 \u062f\u0648\u0631\u0627\u064b \u0645\u0646 \u0627\u0644\u0642\u0627\u0626\u0645\u0629 \u0644\u0639\u0631\u0636 \u062a\u0641\u0627\u0635\u064a\u0644 \u0635\u0644\u0627\u062d\u064a\u0627\u062a\u0647")})]})})})]})]})}}}]);
//# sourceMappingURL=9444.dc6c180d.chunk.js.map