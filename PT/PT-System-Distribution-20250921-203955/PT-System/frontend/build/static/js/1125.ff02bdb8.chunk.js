"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1125],{1125:(e,t,a)=>{a.r(t),a.d(t,{default:()=>j});var s=a(2555),r=a(5043),l=a(4117),n=a(3768),i=a(4538),c=a(9399),o=a(7012),d=a(4791),u=a(3986);const m=["title","titleId"];function x(e,t){let{title:a,titleId:s}=e,l=(0,u.A)(e,m);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},l),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5"}))}const p=r.forwardRef(x),h=["title","titleId"];function g(e,t){let{title:a,titleId:s}=e,l=(0,u.A)(e,h);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},l),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const b=r.forwardRef(g);var y=a(4122),f=a(3099),k=a(2593),w=a(6761),v=a(579);const j=()=>{const{t:e}=(0,l.Bd)(),[t,a]=(0,r.useState)(!1),[u,m]=(0,r.useState)([{id:1,name:"Daily Backup - 2024-01-15",type:"automatic",size:"2.3 GB",created:"2024-01-15T02:00:00Z",status:"completed",includes:["database","files","configurations"]},{id:2,name:"Manual Backup - Pre-Update",type:"manual",size:"2.1 GB",created:"2024-01-14T14:30:00Z",status:"completed",includes:["database","configurations"]},{id:3,name:"Weekly Backup - 2024-01-08",type:"automatic",size:"2.5 GB",created:"2024-01-08T02:00:00Z",status:"completed",includes:["database","files","configurations","logs"]}]),[x,h]=(0,r.useState)({automaticBackups:!0,backupFrequency:"daily",backupTime:"02:00",retentionDays:30,includeFiles:!0,includeDatabase:!0,includeConfigurations:!0,includeLogs:!1,compressionEnabled:!0,encryptionEnabled:!0}),[g,j]=(0,r.useState)(!1);(0,r.useEffect)(()=>{N()},[]);const N=async()=>{try{a(!0)}catch(t){console.error("Failed to load backups:",t),n.oR.error(e("failedToLoadBackups","Failed to load backups"))}finally{a(!1)}},A=e=>{switch(e){case"completed":return(0,v.jsx)(i.A,{className:"h-5 w-5 text-green-500"});case"failed":return(0,v.jsx)(c.A,{className:"h-5 w-5 text-red-500"});default:return(0,v.jsx)(o.A,{className:"h-5 w-5 text-yellow-500"})}},B=e=>{switch(e){case"completed":return"text-green-600 bg-green-100";case"failed":return"text-red-600 bg-red-100";default:return"text-yellow-600 bg-yellow-100"}},C=t=>{let{backup:a}=t;return(0,v.jsxs)(f.A,{className:"p-4",children:[(0,v.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,v.jsxs)("div",{className:"flex items-center",children:[A(a.status),(0,v.jsxs)("div",{className:"ml-3",children:[(0,v.jsx)("h4",{className:"font-medium text-gray-900",children:a.name}),(0,v.jsxs)("p",{className:"text-sm text-gray-500",children:[new Date(a.created).toLocaleString()," \u2022 ",a.size]})]})]}),(0,v.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(B(a.status)),children:a.status})]}),(0,v.jsxs)("div",{className:"mb-3",children:[(0,v.jsx)("p",{className:"text-sm text-gray-600 mb-1",children:"Includes:"}),(0,v.jsx)("div",{className:"flex flex-wrap gap-1",children:a.includes.map(e=>(0,v.jsx)("span",{className:"px-2 py-1 bg-gray-100 rounded text-xs",children:e},e))})]}),(0,v.jsxs)("div",{className:"flex space-x-2",children:[(0,v.jsxs)(k.A,{size:"sm",variant:"outline",onClick:()=>(a.id,void n.oR.info(e("downloadStarted","Backup download started"))),children:[(0,v.jsx)(d.A,{className:"h-4 w-4 mr-1"}),"Download"]}),(0,v.jsxs)(k.A,{size:"sm",variant:"outline",onClick:()=>(async()=>{if(window.confirm(e("confirmRestore","Are you sure you want to restore this backup? This will overwrite current data.")))try{j(!0),await new Promise(e=>setTimeout(e,3e3)),n.oR.success(e("restoreCompleted","Backup restored successfully"))}catch(t){console.error("Failed to restore backup:",t),n.oR.error(e("failedToRestore","Failed to restore backup"))}finally{j(!1)}})(a.id),disabled:g,children:[(0,v.jsx)(p,{className:"h-4 w-4 mr-1"}),"Restore"]}),(0,v.jsx)(k.A,{size:"sm",variant:"outline",onClick:()=>(async t=>{if(window.confirm(e("confirmDelete","Are you sure you want to delete this backup?")))try{m(e=>e.filter(e=>e.id!==t)),n.oR.success(e("backupDeleted","Backup deleted successfully"))}catch(a){console.error("Failed to delete backup:",a),n.oR.error(e("failedToDeleteBackup","Failed to delete backup"))}})(a.id),className:"text-red-600 hover:text-red-700",children:(0,v.jsx)(b,{className:"h-4 w-4"})})]})]})};return(0,v.jsxs)("div",{className:"space-y-6",children:[(0,v.jsxs)("div",{className:"flex items-center justify-between",children:[(0,v.jsxs)("div",{children:[(0,v.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,v.jsx)(y.A,{className:"h-8 w-8 mr-3 text-blue-600"}),e("backupRestore","Backup & Restore")]}),(0,v.jsx)("p",{className:"text-gray-600 mt-1",children:e("backupDescription","Manage system backups and data recovery")})]}),(0,v.jsx)(k.A,{onClick:async()=>{try{a(!0);const t={id:Date.now(),name:"Manual Backup - ".concat((new Date).toLocaleDateString()),type:"manual",size:"2.2 GB",created:(new Date).toISOString(),status:"completed",includes:["database","files","configurations"]};m(e=>[t,...e]),n.oR.success(e("backupCreated","Backup created successfully"))}catch(t){console.error("Failed to create backup:",t),n.oR.error(e("failedToCreateBackup","Failed to create backup"))}finally{a(!1)}},disabled:t,children:e("createBackup","Create Backup")})]}),(0,v.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,v.jsxs)(f.A,{className:"p-6",children:[(0,v.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("backupSettings","Backup Settings")}),(0,v.jsxs)("div",{className:"space-y-4",children:[(0,v.jsx)("div",{children:(0,v.jsxs)("label",{className:"flex items-center",children:[(0,v.jsx)("input",{type:"checkbox",checked:x.automaticBackups,onChange:e=>h(t=>(0,s.A)((0,s.A)({},t),{},{automaticBackups:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,v.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:e("enableAutomaticBackups","Enable Automatic Backups")})]})}),(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("frequency","Frequency")}),(0,v.jsxs)("select",{value:x.backupFrequency,onChange:e=>h(t=>(0,s.A)((0,s.A)({},t),{},{backupFrequency:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[(0,v.jsx)("option",{value:"daily",children:e("daily","Daily")}),(0,v.jsx)("option",{value:"weekly",children:e("weekly","Weekly")}),(0,v.jsx)("option",{value:"monthly",children:e("monthly","Monthly")})]})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("backupTime","Backup Time")}),(0,v.jsx)("input",{type:"time",value:x.backupTime,onChange:e=>h(t=>(0,s.A)((0,s.A)({},t),{},{backupTime:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,v.jsxs)("div",{children:[(0,v.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("retentionDays","Retention (days)")}),(0,v.jsx)("input",{type:"number",value:x.retentionDays,onChange:e=>h(t=>(0,s.A)((0,s.A)({},t),{},{retentionDays:parseInt(e.target.value)})),className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),(0,v.jsxs)("div",{className:"space-y-2",children:[(0,v.jsx)("p",{className:"text-sm font-medium text-gray-700",children:e("includeInBackup","Include in Backup:")}),[{key:"includeDatabase",label:"Database"},{key:"includeFiles",label:"Files"},{key:"includeConfigurations",label:"Configurations"},{key:"includeLogs",label:"Logs"}].map(e=>{let{key:t,label:a}=e;return(0,v.jsxs)("label",{className:"flex items-center",children:[(0,v.jsx)("input",{type:"checkbox",checked:x[t],onChange:e=>h(a=>(0,s.A)((0,s.A)({},a),{},{[t]:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,v.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:a})]},t)})]}),(0,v.jsx)(k.A,{onClick:async()=>{try{a(!0),n.oR.success(e("settingsSaved","Backup settings saved successfully"))}catch(t){console.error("Failed to save settings:",t),n.oR.error(e("failedToSaveSettings","Failed to save settings"))}finally{a(!1)}},className:"w-full",children:e("saveSettings","Save Settings")})]})]}),(0,v.jsxs)("div",{className:"lg:col-span-2",children:[(0,v.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("availableBackups","Available Backups")}),t?(0,v.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,v.jsx)(w.Ay,{size:"lg"})}):(0,v.jsxs)("div",{className:"space-y-4",children:[u.map(e=>(0,v.jsx)(C,{backup:e},e.id)),0===u.length&&(0,v.jsxs)(f.A,{className:"p-8 text-center",children:[(0,v.jsx)(y.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,v.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:e("noBackups","No backups available")}),(0,v.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:e("createFirstBackup","Create your first backup to get started.")})]})]})]})]}),g&&(0,v.jsx)(f.A,{className:"p-6 bg-blue-50 border-blue-200",children:(0,v.jsxs)("div",{className:"flex items-center",children:[(0,v.jsx)(w.Ay,{size:"sm"}),(0,v.jsxs)("div",{className:"ml-3",children:[(0,v.jsx)("h4",{className:"text-lg font-medium text-blue-900",children:e("restoreInProgress","Restore in Progress")}),(0,v.jsx)("p",{className:"text-blue-700",children:e("restoreDescription","Please wait while the backup is being restored. Do not close this page.")})]})]})})]})}},2593:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(2555),r=a(3986),l=(a(5043),a(579));const n=["children","variant","size","disabled","loading","className","type","onClick"],i=e=>{let{children:t,variant:a="primary",size:i="md",disabled:c=!1,loading:o=!1,className:d="",type:u="button",onClick:m}=e,x=(0,r.A)(e,n);const p={primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500",warning:"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500",ghost:"hover:bg-gray-100 text-gray-700 focus:ring-gray-500"},h={xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm",md:"px-4 py-2 text-sm",lg:"px-4 py-2 text-base",xl:"px-6 py-3 text-base"},g=["inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200",p[a]||p.primary,h[i]||h.md,c||o?"opacity-50 cursor-not-allowed":"",d].filter(Boolean).join(" ");return(0,l.jsxs)("button",(0,s.A)((0,s.A)({type:u,className:g,onClick:e=>{c||o?e.preventDefault():m&&m(e)},disabled:c||o},x),{},{children:[o&&(0,l.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,l.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,l.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),t]}))}},3099:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(2555),r=a(3986),l=(a(5043),a(579));const n=["children","className","padding","shadow","border","rounded","background","hover"],i=e=>{let{children:t,className:a="",padding:i="p-6",shadow:c="shadow-sm",border:o="border border-gray-200",rounded:d="rounded-lg",background:u="bg-white",hover:m=""}=e,x=(0,r.A)(e,n);const p=[u,o,d,c,i,m,a].filter(Boolean).join(" ");return(0,l.jsx)("div",(0,s.A)((0,s.A)({className:p},x),{},{children:t}))}},3986:(e,t,a)=>{function s(e,t){if(null==e)return{};var a,s,r=function(e,t){if(null==e)return{};var a={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;a[s]=e[s]}return a}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(s=0;s<l.length;s++)a=l[s],-1===t.indexOf(a)&&{}.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}a.d(t,{A:()=>s})},4122:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(3986),r=a(5043);const l=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,i=(0,s.A)(e,l);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},i),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))}const i=r.forwardRef(n)},4538:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(3986),r=a(5043);const l=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,i=(0,s.A)(e,l);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},i),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(n)},4791:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(3986),r=a(5043);const l=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,i=(0,s.A)(e,l);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},i),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))}const i=r.forwardRef(n)},7012:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(3986),r=a(5043);const l=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,i=(0,s.A)(e,l);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},i),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=r.forwardRef(n)},9399:(e,t,a)=>{a.d(t,{A:()=>i});var s=a(3986),r=a(5043);const l=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,i=(0,s.A)(e,l);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},i),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const i=r.forwardRef(n)}}]);
//# sourceMappingURL=1125.ff02bdb8.chunk.js.map