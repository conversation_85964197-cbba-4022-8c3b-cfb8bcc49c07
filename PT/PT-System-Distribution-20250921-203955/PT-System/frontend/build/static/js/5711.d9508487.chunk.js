"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[5711],{2593:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(2555),a=r(3986),s=(r(5043),r(579));const l=["children","variant","size","disabled","loading","className","type","onClick"],i=e=>{let{children:t,variant:r="primary",size:i="md",disabled:o=!1,loading:c=!1,className:d="",type:u="button",onClick:g}=e,p=(0,a.A)(e,l);const m={primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500",warning:"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500",ghost:"hover:bg-gray-100 text-gray-700 focus:ring-gray-500"},h={xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm",md:"px-4 py-2 text-sm",lg:"px-4 py-2 text-base",xl:"px-6 py-3 text-base"},x=["inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200",m[r]||m.primary,h[i]||h.md,o||c?"opacity-50 cursor-not-allowed":"",d].filter(Boolean).join(" ");return(0,s.jsxs)("button",(0,n.A)((0,n.A)({type:u,className:x,onClick:e=>{o||c?e.preventDefault():g&&g(e)},disabled:o||c},p),{},{children:[c&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),t]}))}},3099:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(2555),a=r(3986),s=(r(5043),r(579));const l=["children","className","padding","shadow","border","rounded","background","hover"],i=e=>{let{children:t,className:r="",padding:i="p-6",shadow:o="shadow-sm",border:c="border border-gray-200",rounded:d="rounded-lg",background:u="bg-white",hover:g=""}=e,p=(0,a.A)(e,l);const m=[u,c,d,o,i,g,r].filter(Boolean).join(" ");return(0,s.jsx)("div",(0,n.A)((0,n.A)({className:m},p),{},{children:t}))}},3641:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(3986),a=r(5043);const s=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,n.A)(e,s);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))}const i=a.forwardRef(l)},3986:(e,t,r)=>{function n(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(n=0;n<s.length;n++)r=s[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}r.d(t,{A:()=>n})},5711:(e,t,r)=>{r.r(t),r.d(t,{default:()=>f});var n=r(2555),a=r(5043),s=r(4117),l=r(3768),i=r(3641),o=r(3986);const c=["title","titleId"];function d(e,t){let{title:r,titleId:n}=e,s=(0,o.A)(e,c);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}))}const u=a.forwardRef(d);var g=r(8153);const p=["title","titleId"];function m(e,t){let{title:r,titleId:n}=e,s=(0,o.A)(e,p);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),r?a.createElement("title",{id:n},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))}const h=a.forwardRef(m);var x=r(9399),b=r(3099),y=r(2593),v=r(6761),w=r(579);const f=()=>{const{t:e}=(0,s.Bd)(),[t,r]=(0,a.useState)(!1),[o,c]=(0,a.useState)({passwordMinLength:8,passwordRequireUppercase:!0,passwordRequireLowercase:!0,passwordRequireNumbers:!0,passwordRequireSpecialChars:!0,passwordExpirationDays:90,passwordHistoryCount:5,maxLoginAttempts:5,accountLockoutDuration:30,sessionTimeout:30,enableTwoFactor:!1,requireTwoFactorForAdmin:!0,enableIPWhitelist:!1,allowedIPs:["***********/24"],enableGeoBlocking:!1,blockedCountries:[],enableLoginAudit:!0,enableActionAudit:!0,auditRetentionDays:365,enableRealTimeAlerts:!0,enableDataEncryption:!0,encryptionAlgorithm:"AES-256",enableBackupEncryption:!0,enableSSL:!0,sslCertificateExpiry:"2024-12-31"}),[d,p]=(0,a.useState)([{id:1,type:"failed_login",user:"<EMAIL>",timestamp:(new Date).toISOString(),severity:"medium",description:"Multiple failed login attempts"},{id:2,type:"suspicious_activity",user:"<EMAIL>",timestamp:new Date(Date.now()-36e5).toISOString(),severity:"high",description:"Login from unusual location"}]);(0,a.useEffect)(()=>{m()},[]);const m=async()=>{try{r(!0)}catch(t){console.error("Failed to load security settings:",t),l.oR.error(e("failedToLoadSettings","Failed to load security settings"))}finally{r(!1)}},f=(e,t)=>{c(r=>(0,n.A)((0,n.A)({},r),{},{[e]:t}))},j=e=>{switch(e){case"high":return"text-red-600 bg-red-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},k=e=>{let{title:t,icon:r,children:n}=e;return(0,w.jsxs)(b.A,{className:"p-6",children:[(0,w.jsxs)("div",{className:"flex items-center mb-4",children:[(0,w.jsx)(r,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,w.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t})]}),(0,w.jsx)("div",{className:"space-y-4",children:n})]})},A=e=>{let{label:t,type:r="text",value:n,onChange:a,options:s=null,description:l=null}=e;return(0,w.jsxs)("div",{children:[(0,w.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:t}),"select"===r?(0,w.jsx)("select",{value:n,onChange:e=>a(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:null===s||void 0===s?void 0:s.map(e=>(0,w.jsx)("option",{value:e.value,children:e.label},e.value))}):"checkbox"===r?(0,w.jsxs)("div",{className:"flex items-center",children:[(0,w.jsx)("input",{type:"checkbox",checked:n,onChange:e=>a(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,w.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Enable this security feature"})]}):"textarea"===r?(0,w.jsx)("textarea",{value:Array.isArray(n)?n.join("\n"):n,onChange:e=>a("array"===r?e.target.value.split("\n"):e.target.value),rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}):(0,w.jsx)("input",{type:r,value:n,onChange:e=>a("number"===r?parseInt(e.target.value):e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),l&&(0,w.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:l})]})};return t?(0,w.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,w.jsx)(v.Ay,{size:"lg"})}):(0,w.jsxs)("div",{className:"space-y-6",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsxs)("div",{children:[(0,w.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,w.jsx)(i.A,{className:"h-8 w-8 mr-3 text-blue-600"}),e("securitySettings","Security Settings")]}),(0,w.jsx)("p",{className:"text-gray-600 mt-1",children:e("securitySettingsDescription","Configure security policies and access controls")})]}),(0,w.jsx)(y.A,{onClick:async()=>{try{r(!0),l.oR.success(e("securitySettingsSaved","Security settings saved successfully"))}catch(t){console.error("Failed to save security settings:",t),l.oR.error(e("failedToSaveSettings","Failed to save security settings"))}finally{r(!1)}},disabled:t,children:e("saveSettings","Save Settings")})]}),(0,w.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,w.jsxs)(k,{title:e("passwordPolicy","Password Policy"),icon:u,children:[(0,w.jsx)(A,{label:e("minimumLength","Minimum Length"),type:"number",value:o.passwordMinLength,onChange:e=>f("passwordMinLength",e)}),(0,w.jsx)(A,{label:e("requireUppercase","Require Uppercase Letters"),type:"checkbox",value:o.passwordRequireUppercase,onChange:e=>f("passwordRequireUppercase",e)}),(0,w.jsx)(A,{label:e("requireNumbers","Require Numbers"),type:"checkbox",value:o.passwordRequireNumbers,onChange:e=>f("passwordRequireNumbers",e)}),(0,w.jsx)(A,{label:e("passwordExpiration","Password Expiration (days)"),type:"number",value:o.passwordExpirationDays,onChange:e=>f("passwordExpirationDays",e)})]}),(0,w.jsxs)(k,{title:e("accountSecurity","Account Security"),icon:g.A,children:[(0,w.jsx)(A,{label:e("maxLoginAttempts","Max Login Attempts"),type:"number",value:o.maxLoginAttempts,onChange:e=>f("maxLoginAttempts",e)}),(0,w.jsx)(A,{label:e("lockoutDuration","Lockout Duration (minutes)"),type:"number",value:o.accountLockoutDuration,onChange:e=>f("accountLockoutDuration",e)}),(0,w.jsx)(A,{label:e("sessionTimeout","Session Timeout (minutes)"),type:"number",value:o.sessionTimeout,onChange:e=>f("sessionTimeout",e)}),(0,w.jsx)(A,{label:e("enableTwoFactor","Enable Two-Factor Authentication"),type:"checkbox",value:o.enableTwoFactor,onChange:e=>f("enableTwoFactor",e)})]}),(0,w.jsxs)(k,{title:e("accessControl","Access Control"),icon:h,children:[(0,w.jsx)(A,{label:e("enableIPWhitelist","Enable IP Whitelist"),type:"checkbox",value:o.enableIPWhitelist,onChange:e=>f("enableIPWhitelist",e)}),o.enableIPWhitelist&&(0,w.jsx)(A,{label:e("allowedIPs","Allowed IP Addresses"),type:"textarea",value:o.allowedIPs,onChange:e=>f("allowedIPs",e),description:"One IP address or CIDR block per line"}),(0,w.jsx)(A,{label:e("enableGeoBlocking","Enable Geographic Blocking"),type:"checkbox",value:o.enableGeoBlocking,onChange:e=>f("enableGeoBlocking",e)})]}),(0,w.jsxs)(k,{title:e("dataProtection","Data Protection"),icon:i.A,children:[(0,w.jsx)(A,{label:e("enableDataEncryption","Enable Data Encryption"),type:"checkbox",value:o.enableDataEncryption,onChange:e=>f("enableDataEncryption",e)}),(0,w.jsx)(A,{label:e("encryptionAlgorithm","Encryption Algorithm"),type:"select",value:o.encryptionAlgorithm,onChange:e=>f("encryptionAlgorithm",e),options:[{value:"AES-256",label:"AES-256"},{value:"AES-128",label:"AES-128"},{value:"ChaCha20",label:"ChaCha20"}]}),(0,w.jsx)(A,{label:e("enableBackupEncryption","Enable Backup Encryption"),type:"checkbox",value:o.enableBackupEncryption,onChange:e=>f("enableBackupEncryption",e)}),(0,w.jsx)(A,{label:e("enableSSL","Enable SSL/TLS"),type:"checkbox",value:o.enableSSL,onChange:e=>f("enableSSL",e)})]})]}),(0,w.jsxs)(b.A,{className:"p-6",children:[(0,w.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,w.jsx)(x.A,{className:"h-6 w-6 text-yellow-600 mr-3"}),e("recentSecurityEvents","Recent Security Events")]}),(0,w.jsx)("div",{className:"space-y-3",children:d.map(e=>(0,w.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,w.jsxs)("div",{className:"flex items-center",children:[(0,w.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(j(e.severity)),children:e.severity.toUpperCase()}),(0,w.jsxs)("div",{className:"ml-3",children:[(0,w.jsx)("p",{className:"font-medium text-gray-900",children:e.description}),(0,w.jsxs)("p",{className:"text-sm text-gray-500",children:["User: ",e.user]})]})]}),(0,w.jsx)("div",{className:"text-right",children:(0,w.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.timestamp).toLocaleString()})})]},e.id))})]})]})}},8153:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(3986),a=r(5043);const s=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,n.A)(e,s);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const i=a.forwardRef(l)},9399:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(3986),a=r(5043);const s=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,n.A)(e,s);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const i=a.forwardRef(l)}}]);
//# sourceMappingURL=5711.d9508487.chunk.js.map