{"version": 3, "file": "static/js/3553.4fb841aa.chunk.js", "mappings": "wMAGA,MAsbA,EAtb+BA,IAMxB,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAN0B,YAC9BC,EAAW,OACXC,EAAM,SACNC,EAAQ,UACRC,GAAY,EAAK,YACjBC,EAAc,CAAC,GAChB1B,EACC,MAAM,EAAE2B,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KAGfC,EAAkBR,GAAe,CAAC,GAEjCS,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,CAEnDC,gBAAgB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACpDC,WAAY,GACZC,iBAAkB,GAGlBC,iBAAkBV,EAAgBU,kBAAoB,GACtDC,mBAAoBX,EAAgBW,oBAAsB,GAC1DC,eAAgBZ,EAAgBY,gBAAkB,GAClDC,gBAAiBb,EAAgBa,iBAAmB,GAGpDC,uBAAwB,CACtBC,QAA8C,QAAtC5C,EAAA6B,EAAgBc,8BAAsB,IAAA3C,OAAA,EAAtCA,EAAwC4C,SAAU,OAC1DC,WAAiD,QAAtC5C,EAAA4B,EAAgBc,8BAAsB,IAAA1C,OAAA,EAAtCA,EAAwC4C,YAAa,GAChEC,eAAqD,QAAtC5C,EAAA2B,EAAgBc,8BAAsB,IAAAzC,OAAA,EAAtCA,EAAwC4C,gBAAiB,UACxEC,YAAkD,QAAtC5C,EAAA0B,EAAgBc,8BAAsB,IAAAxC,OAAA,EAAtCA,EAAwC4C,aAAc,WAIpEC,eAAgB,CACdC,QAAsC,QAA9B7C,EAAAyB,EAAgBmB,sBAAc,IAAA5C,OAAA,EAA9BA,EAAgC6C,SAAU,UAClDC,UAAwC,QAA9B7C,EAAAwB,EAAgBmB,sBAAc,IAAA3C,OAAA,EAA9BA,EAAgC6C,WAAY,UACtDC,SAAuC,QAA9B7C,EAAAuB,EAAgBmB,sBAAc,IAAA1C,OAAA,EAA9BA,EAAgC6C,UAAW,UACpDC,YAA0C,QAA9B7C,EAAAsB,EAAgBmB,sBAAc,IAAAzC,OAAA,EAA9BA,EAAgC6C,aAAc,UAC1DC,gBAA8C,QAA9B7C,EAAAqB,EAAgBmB,sBAAc,IAAAxC,OAAA,EAA9BA,EAAgC6C,iBAAkB,WAIpEC,gBAAiB,CACfC,mBAAkD,QAA/B9C,EAAAoB,EAAgByB,uBAAe,IAAA7C,OAAA,EAA/BA,EAAiC8C,oBAAqB,UACzEC,eAA8C,QAA/B9C,EAAAmB,EAAgByB,uBAAe,IAAA5C,OAAA,EAA/BA,EAAiC8C,gBAAiB,QACjEC,qBAAoD,QAA/B9C,EAAAkB,EAAgByB,uBAAe,IAAA3C,OAAA,EAA/BA,EAAiC8C,sBAAuB,gBAC7EC,cAA6C,QAA/B9C,EAAAiB,EAAgByB,uBAAe,IAAA1C,OAAA,EAA/BA,EAAiC8C,eAAgB,SAIjEC,YAAa,CACXC,YAAuC,QAA3B/C,EAAAgB,EAAgB8B,mBAAW,IAAA9C,OAAA,EAA3BA,EAA6B+C,aAAc,UACvDC,WAAsC,QAA3B/C,EAAAe,EAAgB8B,mBAAW,IAAA7C,OAAA,EAA3BA,EAA6B+C,YAAa,UACrDC,cAAyC,QAA3B/C,EAAAc,EAAgB8B,mBAAW,IAAA5C,OAAA,EAA3BA,EAA6B+C,eAAgB,WAC3DC,SAAoC,QAA3B/C,EAAAa,EAAgB8B,mBAAW,IAAA3C,OAAA,EAA3BA,EAA6B+C,UAAW,YAInDC,mBAAoB,CAClBC,4BAA8D,QAAlChD,EAAAY,EAAgBmC,0BAAkB,IAAA/C,OAAA,EAAlCA,EAAoCgD,6BAA8B,GAC9FC,uBAAyD,QAAlChD,EAAAW,EAAgBmC,0BAAkB,IAAA9C,OAAA,EAAlCA,EAAoCgD,wBAAyB,GACpFC,oBAAsD,QAAlChD,EAAAU,EAAgBmC,0BAAkB,IAAA7C,OAAA,EAAlCA,EAAoCgD,qBAAsB,GAC9EC,iBAAmD,QAAlChD,EAAAS,EAAgBmC,0BAAkB,IAAA5C,OAAA,EAAlCA,EAAoCgD,kBAAmB,IAI1EC,eAAgBxC,EAAgBwC,gBAAkB,GAClDC,gBAAiBzC,EAAgByC,iBAAmB,GACpDC,eAAgB1C,EAAgB0C,iBAAkB,EAClDC,mBAAoB3C,EAAgB2C,oBAAsB,GAG1DC,UAAW5C,EAAgB4C,WAAa,GACxCC,WAAY7C,EAAgB6C,YAAc,GAC1CC,eAAgB9C,EAAgB8C,gBAAkB,GAClDC,gBAAiB/C,EAAgB+C,iBAAmB,KAGhDC,EAAkB,CACtB,CAAEC,MAAO,UAAWC,MAAOrD,EAAE,UAAW,YACxC,CAAEoD,MAAO,aAAcC,MAAOrD,EAAE,YAAa,eAC7C,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,mBACrD,CAAEoD,MAAO,eAAgBC,MAAOrD,EAAE,cAAe,iBACjD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,oBAGjDsD,EAAmB,CACvB,CAAEF,MAAO,cAAeC,MAAOrD,EAAE,cAAe,gBAChD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,uBACrD,CAAEoD,MAAO,kBAAmBC,MAAOrD,EAAE,iBAAkB,wBACvD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,uBACrD,CAAEoD,MAAO,eAAgBC,MAAOrD,EAAE,cAAe,sBAG7CuD,EAAsB,CAC1B,CAAEH,MAAO,OAAQC,MAAOrD,EAAE,OAAQ,SAClC,CAAEoD,MAAO,UAAWC,MAAOrD,EAAE,UAAW,YACxC,CAAEoD,MAAO,aAAcC,MAAOrD,EAAE,aAAc,eAC9C,CAAEoD,MAAO,OAAQC,MAAOrD,EAAE,OAAQ,SAClC,CAAEoD,MAAO,YAAaC,MAAOrD,EAAE,YAAa,eAGxCwD,EAAgB,CACpB,CAAEJ,MAAO,UAAWC,MAAOrD,EAAE,UAAW,YACxC,CAAEoD,MAAO,gBAAiBC,MAAOrD,EAAE,gBAAiB,qBACpD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,iBAAkB,oBACtD,CAAEoD,MAAO,UAAWC,MAAOrD,EAAE,UAAW,oBACxC,CAAEoD,MAAO,WAAYC,MAAOrD,EAAE,WAAY,sBAGtCyD,EAAoBA,CAACC,EAAON,KAChC/C,EAAkBsD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBD,GAAI,IACP,CAACD,GAAQN,MAIPS,EAAqBA,CAACC,EAAUJ,EAAON,KAC3C/C,EAAkBsD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBD,GAAI,IACP,CAACG,IAAQF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACJD,EAAKG,IAAS,IACjB,CAACJ,GAAQN,QAoBf,OACEW,EAAAA,EAAAA,MAAA,OAAKC,UAAS,sDAAAC,OAAwDhE,EAAQ,cAAgB,gBAAiBiE,SAAA,EAC7GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kDAAiDE,SAC5DlE,EAAE,yBAA0B,8BAE9BD,EAAYqE,OACXL,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CE,SAAA,CACpDlE,EAAE,UAAW,WAAW,KAAGD,EAAYqE,YAI9CL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEE,QAASxE,EACTmE,UAAU,uFAAsFE,SAE/FlE,EAAE,SAAU,aAEfmE,EAAAA,EAAAA,KAAA,UACEE,QA3BSC,KACb1E,GACFA,EAAOQ,IA0BD4D,UAAU,kFAAiFE,SAE1FlE,EAAE,iBAAkB,4BAK3B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,sBAAuB,mCAE5B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAeG,eACtBiE,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DY,UAAU,wIAGdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,aAAc,kBAEnBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAeO,WACtB6D,SAAWC,GAAMhB,EAAkB,aAAcgB,EAAEC,OAAOtB,OAC1DY,UAAU,kIACVW,YAAa3E,EAAE,gBAAiB,wBAGpC+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,mBAAoB,wBAEzBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAeQ,iBACtB4D,SAAWC,GAAMhB,EAAkB,mBAAoBgB,EAAEC,OAAOtB,OAChEY,UAAU,kIACVW,YAAa3E,EAAE,gBAAiB,8BAOxC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,8BAA+B,qCAEpC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvB+D,EAAAA,EAAAA,MAAA,UACEX,MAAOhD,EAAeW,eACtByD,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,GAAEc,SAAElE,EAAE,cAAe,kBAClCmD,EAAgByB,IAAIC,IACnBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,eAIzBW,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,kBAAmB,uBAExB+D,EAAAA,EAAAA,MAAA,UACEX,MAAOhD,EAAeY,gBACtBwD,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOtB,OAC/DY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,GAAEc,SAAElE,EAAE,cAAe,kBAClCsD,EAAiBsB,IAAIC,IACpBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,qBAQ7BW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,0BAA2B,+BAEhC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,sBAAuB,2BAE5BmE,EAAAA,EAAAA,KAAA,UACEf,MAAOhD,EAAea,uBAAuBC,OAC7CsD,SAAWC,GAAMZ,EAAmB,yBAA0B,SAAUY,EAAEC,OAAOtB,OACjFY,UAAU,kIAAiIE,SAE1IX,EAAoBqB,IAAIC,IACvBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,cAIzBW,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,gBAAiB,oBAEtBmE,EAAAA,EAAAA,KAAA,UACEf,MAAOhD,EAAea,uBAAuBG,cAC7CoD,SAAWC,GAAMZ,EAAmB,yBAA0B,gBAAiBY,EAAEC,OAAOtB,OACxFY,UAAU,kIAAiIE,SAE1IX,EAAoBqB,IAAIC,IACvBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,oBAQ7BW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,oBAAqB,yBAE1BmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,SAClEY,OAAOC,QAAQ3E,EAAekB,gBAAgBsD,IAAII,IAAA,IAAEC,EAAO7B,GAAM4B,EAAA,OAChEjB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAEiF,EAAOA,EAAMC,OAAO,GAAGC,cAAgBF,EAAMG,MAAM,OAExDjB,EAAAA,EAAAA,KAAA,UACEf,MAAOA,EACPoB,SAAWC,GAAMZ,EAAmB,iBAAkBoB,EAAOR,EAAEC,OAAOtB,OACtEY,UAAU,kIAAiIE,SAE1IV,EAAcoB,IAAIC,IACjBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,YAVf6B,WAmBhBlB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,cAAe,mBAEpBmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDY,OAAOC,QAAQ3E,EAAe6B,aAAa2C,IAAIS,IAAA,IAAEC,EAAOlC,GAAMiC,EAAA,OAC7DtB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAEsF,EAAOA,EAAMJ,OAAO,GAAGC,cAAgBG,EAAMF,MAAM,OAExDrB,EAAAA,EAAAA,MAAA,UACEX,MAAOA,EACPoB,SAAWC,GAAMZ,EAAmB,cAAeyB,EAAOb,EAAEC,OAAOtB,OACnEY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,cACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,cACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,WAAUc,SAAElE,EAAE,WAAY,eACxCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,WAAUc,SAAElE,EAAE,WAAY,mBAZlCsF,WAoBhBvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,yBAA0B,+BAE/B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,YAAa,gBAElBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAe2C,UACtByB,SAAWC,GAAMhB,EAAkB,YAAagB,EAAEC,OAAOtB,OACzDmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,uBAAwB,iDAG3C+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,aAAc,iBAEnBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAe4C,WACtBwB,SAAWC,GAAMhB,EAAkB,aAAcgB,EAAEC,OAAOtB,OAC1DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,wBAAyB,0DAOhD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,0BAA2B,gCAEhC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,kBAAmB,sBAExBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAewC,gBACtB4B,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOtB,OAC/DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,6BAA8B,qDAGjD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAe6C,eACtBuB,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,4BAA6B,2CAGhD+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLiB,GAAG,iBACHC,QAASrF,EAAeyC,eACxB2B,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOe,SAC9DzB,UAAU,2BAEZG,EAAAA,EAAAA,KAAA,SAAOuB,QAAQ,iBAAiB1B,UAAU,uDAAsDE,SAC7FlE,EAAE,2BAA4B,oCAGlCI,EAAeyC,iBACdkB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,qBAAsB,2BAE3BmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAe0C,mBACtB0B,SAAWC,GAAMhB,EAAkB,qBAAsBgB,EAAEC,OAAOtB,OAClEY,UAAU,6J,sJC3a5B,MAAM2B,UAAsBC,EAAAA,UAC1BC,WAAAA,CAAYC,GACVC,MAAMD,GACNE,KAAKC,MAAQ,CAAEC,UAAU,EAAOC,MAAO,KAAMC,UAAW,KAC1D,CAEA,+BAAOC,CAAyBF,GAE9B,MAAO,CAAED,UAAU,EACrB,CAEAI,iBAAAA,CAAkBH,EAAOC,GAEvBG,QAAQJ,MAAM,iCAAkCA,EAAOC,GAEvDJ,KAAKQ,SAAS,CACZL,MAAOA,EACPC,UAAWA,GAEf,CAEAK,MAAAA,GACE,OAAIT,KAAKC,MAAMC,UAGX/B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4EAA2EE,UACxFH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qEAAoEE,SAAA,EACjFC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qGAAoGE,UACjHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,0EAGfG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,uEAAsEE,SAAC,0BAIrFC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oDAAmDE,SAAC,oHAIjEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BH,EAAAA,EAAAA,MAAA,UACEM,QAASA,IAAMqC,OAAOC,SAASC,SAC/B5C,UAAU,yFAAwFE,SAAA,EAElGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wBAA0B,mBAIzCD,EAAAA,EAAAA,MAAA,UACEM,QAASA,IAAMqC,OAAOG,QAAQC,OAC9B9C,UAAU,yFAAwFE,SAAA,EAElGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2BAA6B,iBAM7C+C,OAuBFf,KAAKF,MAAM5B,QACpB,EAIF,MAUA,I,cCzFA,MA6gCA,EA7gCmB8C,KAAO,IAADC,EAAAC,EACvB,MAAM,EAAElH,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEiH,EAAI,gBAAEC,EAAe,cAAEC,IAAkBC,EAAAA,EAAAA,KAC3CC,GAAWC,EAAAA,EAAAA,OACX,GAAEhC,IAAOiC,EAAAA,EAAAA,MACRC,EAAaC,IAAkBrH,EAAAA,EAAAA,UAAS,IACxCsH,EAASC,IAAcvH,EAAAA,EAAAA,WAAS,IAChCwH,EAAYC,IAAiBzH,EAAAA,EAAAA,WAAS,IACtC0H,EAAgBC,IAAqB3H,EAAAA,EAAAA,WAAS,IAC9CF,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,OAC9C4H,EAAoBC,IAAyB7H,EAAAA,EAAAA,WAAS,IACtD8H,EAAoBC,IAAyB/H,EAAAA,EAAAA,UAAS,OAEtDgI,EAAUC,IAAejI,EAAAA,EAAAA,UAAS,CAEvCkI,UAAW,GACXC,SAAU,GACVC,YAAa,GACbC,WAAY,GACZC,WAAY,GACZC,YAAa,GACbC,OAAQ,GACRC,MAAO,GACPC,MAAO,GAGPC,QAAS,GACTC,KAAM,GACNC,OAAQ,GACRC,WAAY,GAGZC,qBAAsB,GACtBC,sBAAuB,GACvBC,yBAA0B,GAG1BC,kBAAmB,GACnBC,gBAAiB,GACjBC,gBAAiB,GAGjBC,eAAgB,GAChBC,mBAAoB,GACpBC,UAAW,GACXhJ,iBAAkB,GAClBiJ,mBAAoB,GAGpBC,iBAAiB,EACjBC,iBAAkB,GAClBC,oBAAqB,GACrBC,mBAAoB,CAClBC,SAAU,SACVC,MAAO,SACPC,QAAS,UAEXC,gBAAiB,GACjBC,eAAgB,MAIlBC,EAAAA,EAAAA,WAAU,KACJhF,IACFuC,GAAc,GACd0C,EAAgBjF,KAEjB,CAACA,IAEJ,MAAMiF,EAAkBC,UACtB7C,GAAW,GACX,IAEE,MAAM8C,EAAc,CAClBnF,GAAIoF,EACJpC,UAAW,QACXC,SAAU,oBACVC,YAAa,2BACbC,WAAY,gEACZC,WAAY,aACZC,YAAa,aACbC,OAAQ,OACRC,MAAO,gBACPC,MAAO,2BACPC,QAAS,SACT4B,UAAW,uCACX3B,KAAM,SACNE,WAAY,QACZ0B,QAAS,eACTzB,qBAAsB,kBACtB0B,uBAAwB,sEACxBzB,sBAAuB,gBACvB0B,6BAA8B,SAC9BxB,kBAAmB,cACnByB,sBAAuB,cACvBpK,iBAAkB,iBAClB+I,mBAAoB,2BACpBC,UAAW,aACXC,mBAAoB,uBAGtBvB,EAAY2C,IAAQtH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfsH,GACAP,GAEP,CAAE,MAAOxE,GACPgF,EAAAA,GAAMhF,MAAMnG,EAAE,sBAAuB,8BACvC,CAAC,QACC6H,GAAW,EACb,GAGIuD,EAAQ,CACZ,CAAE5F,GAAI,EAAG6F,MAAOrL,EAAE,YAAa,qBAAsBsL,KAAM,eAC3D,CAAE9F,GAAI,EAAG6F,MAAOrL,EAAE,cAAe,uBAAwBsL,KAAM,gBAC/D,CAAE9F,GAAI,EAAG6F,MAAOrL,EAAE,mBAAoB,qBAAsBsL,KAAM,+BAClE,CAAE9F,GAAI,EAAG6F,MAAOrL,EAAE,YAAa,aAAcsL,KAAM,qBACnD,CAAE9F,GAAI,EAAG6F,MAAOrL,EAAE,cAAe,uBAAwBsL,KAAM,wBAC/D,CAAE9F,GAAI,EAAG6F,MAAOrL,EAAE,eAAgB,iBAAkBsL,KAAM,wBAGtDC,EAAoB,CACxB,CAAE/F,GAAI,SAAUnC,MAAOrD,EAAE,SAAU,4BAA6BsL,KAAM,gBACtE,CAAE9F,GAAI,gBAAiBnC,MAAOrD,EAAE,gBAAiB,kBAAmBsL,KAAM,gBAC1E,CAAE9F,GAAI,eAAgBnC,MAAOrD,EAAE,eAAgB,iBAAkBsL,KAAM,gBACvE,CAAE9F,GAAI,yBAA0BnC,MAAOrD,EAAE,yBAA0B,2BAA4BsL,KAAM,gBACrG,CAAE9F,GAAI,OAAQnC,MAAOrD,EAAE,OAAQ,QAASsL,KAAM,UAC9C,CAAE9F,GAAI,oBAAqBnC,MAAOrD,EAAE,oBAAqB,+BAAgCsL,KAAM,uBAG3F7H,EAAoBA,CAACC,EAAON,KAChCmF,EAAY5E,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACD,GAAQN,MAaPoI,EAAgBC,IACpB,OAAQA,GACN,KAAK,EACH,OAAOnD,EAASE,WAAaF,EAASG,UAAYH,EAASM,YAAcN,EAASO,aAAeP,EAASQ,OAC5G,KAAK,EACH,OAAOR,EAASS,OAAST,EAASW,SAAWX,EAASY,KACxD,KAAK,EACH,OAAOZ,EAASe,sBAAwBf,EAASgB,sBAOnD,QACE,OAAO,IAytBb,OACEvF,EAAAA,EAAAA,MAAA,OAAKC,UAAS,4CAAAC,OAA8ChE,EAAQ,cAAgB,gBAAiBiE,SAAA,EAEnGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oFAAmFE,UAChGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oCAAmCE,UAChDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEE,QAASA,IAAMkD,EAAS,aACxBvD,UAAU,uJAAsJE,UAEhKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,yBAEfD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kDAAiDE,SAC5D4D,EAAa9H,EAAE,cAAe,gBAAkBA,EAAE,gBAAiB,sBAEtE+D,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CE,SAAA,CACpDlE,EAAE,OAAQ,QAAQ,IAAE0H,EAAY,IAAE1H,EAAE,KAAM,MAAM,IAAEoL,EAAMM,OAAO,KAAyB,QAAvBzE,EAACmE,EAAM1D,EAAc,UAAE,IAAAT,OAAA,EAAtBA,EAAwBoE,oBAOrGtH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBE,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,OAAME,UACnBC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oCAAmCE,SAC/CkH,EAAMxG,IAAI,CAAC6G,EAAME,KAChB5H,EAAAA,EAAAA,MAAA,OAAmBC,UAAU,oBAAmBE,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,OACEH,UAAS,oEAAAC,OACPyD,GAAe+D,EAAKjG,GAChB,yCACA,iCACHtB,UAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAWyH,EAAKH,SAEpBK,EAAQP,EAAMM,OAAS,IACtBvH,EAAAA,EAAAA,KAAA,OACEH,UAAS,mBAAAC,OACPyD,EAAc+D,EAAKjG,GAAK,cAAgB,mBAbtCiG,EAAKjG,UAuBrBzB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SAC/C,QAD+CgD,EACrEkE,EAAM1D,EAAc,UAAE,IAAAR,OAAA,EAAtBA,EAAwBmE,QArmBTO,MACxB,OAAQlE,GACN,KAAK,EACH,OACEvD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,YAAa,cAAc,SAEhCmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASE,UAChBhE,SAAWC,GAAMhB,EAAkB,YAAagB,EAAEC,OAAOtB,OACzDY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,WAAY,aAAa,SAE9BmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASG,SAChBjE,SAAWC,GAAMhB,EAAkB,WAAYgB,EAAEC,OAAOtB,OACxDY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,cAAe,0BAEpBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASI,YAChBlE,SAAWC,GAAMhB,EAAkB,cAAegB,EAAEC,OAAOtB,OAC3DY,UAAU,kIACV8H,IAAI,YAGR/H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,aAAc,yBAEnBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASK,WAChBnE,SAAWC,GAAMhB,EAAkB,aAAcgB,EAAEC,OAAOtB,OAC1DY,UAAU,kIACV8H,IAAI,YAGR/H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,aAAc,eAAe,SAElCmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASM,WAChBpE,SAAWC,GAAMhB,EAAkB,aAAcgB,EAAEC,OAAOtB,OAC1DY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,cAAe,iBAAiB,SAErCmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASO,YAChBrE,SAAWC,GAAMhB,EAAkB,cAAegB,EAAEC,OAAOtB,OAC3DY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeE,SAAA,EAC5BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,SAAU,UAAU,SAEzB+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBE,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLH,KAAK,SACLhB,MAAM,OACNqC,QAA6B,SAApB6C,EAASQ,OAClBtE,SAAWC,GAAMhB,EAAkB,SAAUgB,EAAEC,OAAOtB,OACtDY,UAAU,SAEXhE,EAAE,OAAQ,YAEb+D,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBE,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLH,KAAK,SACLhB,MAAM,SACNqC,QAA6B,WAApB6C,EAASQ,OAClBtE,SAAWC,GAAMhB,EAAkB,SAAUgB,EAAEC,OAAOtB,OACtDY,UAAU,SAEXhE,EAAE,SAAU,wBAQ3B,KAAK,EACH,OACEmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,QAAS,gBAAgB,SAE9BmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,MACLnB,MAAOkF,EAASS,MAChBvE,SAAWC,GAAMhB,EAAkB,QAASgB,EAAEC,OAAOtB,OACrDY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,QAAS,YAEdmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLnB,MAAOkF,EAASU,MAChBxE,SAAWC,GAAMhB,EAAkB,QAASgB,EAAEC,OAAOtB,OACrDY,UAAU,wIAGdD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeE,SAAA,EAC5BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,UAAW,WAAW,SAE3BmE,EAAAA,EAAAA,KAAA,YACEf,MAAOkF,EAASW,QAChBzE,SAAWC,GAAMhB,EAAkB,UAAWgB,EAAEC,OAAOtB,OACvDmC,KAAK,IACLvB,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,OAAQ,QAAQ,SAErBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASY,KAChB1E,SAAWC,GAAMhB,EAAkB,OAAQgB,EAAEC,OAAOtB,OACpDY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,SAAU,aAEfmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASa,OAChB3E,SAAWC,GAAMhB,EAAkB,SAAUgB,EAAEC,OAAOtB,OACtDY,UAAU,4IAOtB,KAAK,EACH,OACEG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,uBAAwB,0BAA0B,SAEvDmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASe,qBAChB7E,SAAWC,GAAMhB,EAAkB,uBAAwBgB,EAAEC,OAAOtB,OACpEY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEE,SAAA,CAC/ElE,EAAE,wBAAyB,2BAA2B,SAEzDmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,MACLnB,MAAOkF,EAASgB,sBAChB9E,SAAWC,GAAMhB,EAAkB,wBAAyBgB,EAAEC,OAAOtB,OACrEY,UAAU,kIACV6H,UAAQ,QAGZ9H,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,2BAA4B,mBAEjC+D,EAAAA,EAAAA,MAAA,UACEX,MAAOkF,EAASiB,yBAChB/E,SAAWC,GAAMhB,EAAkB,2BAA4BgB,EAAEC,OAAOtB,OACxEY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,GAAEc,SAAElE,EAAE,qBAAsB,0BAC1CmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,aACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,aACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,cACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,QAAOc,SAAElE,EAAE,QAAS,YAClCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,aACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,YAAWc,SAAElE,EAAE,YAAa,gBAC1CmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,QAAOc,SAAElE,EAAE,QAAS,sBAO9C,KAAK,EACH,OACEmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,oBAAqB,yBAE1B+D,EAAAA,EAAAA,MAAA,UACEX,MAAOkF,EAASkB,kBAChBhF,SAAWC,GAAMhB,EAAkB,oBAAqBgB,EAAEC,OAAOtB,OACjEY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,GAAEc,SAAElE,EAAE,0BAA2B,gCAC/CmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,OAAMc,SAAElE,EAAE,OAAQ,kBAChCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,WAAUc,SAAElE,EAAE,WAAY,eACxCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,cACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,uBACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,QAAOc,SAAElE,EAAE,QAAS,YAClCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,OAAMc,SAAElE,EAAE,OAAQ,WAChCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,QAAOc,SAAElE,EAAE,QAAS,kBAGtC+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,kBAAmB,uBAExBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASmB,gBAChBjF,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOtB,OAC/DY,UAAU,wIAGdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,kBAAmB,4BAExBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASoB,gBAChBlF,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOtB,OAC/DY,UAAU,4IAOtB,KAAK,EACH,OACEG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,mBAAoB,wBAEzBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASzH,iBAChB2D,SAAWC,GAAMhB,EAAkB,mBAAoBgB,EAAEC,OAAOtB,OAChEY,UAAU,kIACVW,YAAa3E,EAAE,8BAA+B,8CAGlD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,qBAAsB,0BAE3BmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOkF,EAASwB,mBAChBtF,SAAWC,GAAMhB,EAAkB,qBAAsBgB,EAAEC,OAAOtB,OAClEY,UAAU,kIACVW,YAAa3E,EAAE,gCAAiC,wCAGpD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOkF,EAASqB,eAChBnF,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,4BAA6B,2EAGhD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,qBAAsB,0BAE3BmE,EAAAA,EAAAA,KAAA,YACEf,MAAOkF,EAASsB,mBAChBpF,SAAWC,GAAMhB,EAAkB,qBAAsBgB,EAAEC,OAAOtB,OAClEmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,gCAAiC,6DAGpD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,YAAa,gBAElBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOkF,EAASuB,UAChBrF,SAAWC,GAAMhB,EAAkB,YAAagB,EAAEC,OAAOtB,OACzDmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,uBAAwB,6EAOnD,KAAK,EACH,OACE+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCE,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLiB,GAAG,kBACHC,QAAS6C,EAASyB,gBAClBvF,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOe,SAC/DzB,UAAU,2BAEZG,EAAAA,EAAAA,KAAA,SAAOuB,QAAQ,kBAAkB1B,UAAU,uDAAsDE,SAC9FlE,EAAE,yBAA0B,kCAIhCsI,EAASyB,kBACRhG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,mBAAoB,4BAEzBmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDqH,EAAkB3G,IAAKL,IACtBJ,EAAAA,EAAAA,KAAA,OAEEE,QAASA,IArjBGE,KAChCgE,EAAY5E,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPqG,iBAAkBrG,EAAKqG,iBAAiB+B,SAASxH,GAC7CZ,EAAKqG,iBAAiBgC,OAAOhM,GAAKA,IAAMuE,GACxC,IAAIZ,EAAKqG,iBAAkBzF,OAgjBE0H,CAAyB1H,EAAKiB,IAC7CxB,UAAS,0DAAAC,OACPqE,EAAS0B,iBAAiB+B,SAASxH,EAAKiB,IACpC,iDACA,8DACHtB,UAEHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,WAAUE,SAAEK,EAAK+G,QACjCnH,EAAAA,EAAAA,KAAA,QAAMH,UAAU,oDAAmDE,SAChEK,EAAKlB,YAXLkB,EAAKiB,WAmBlBzB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,sBAAuB,qCAE5B+D,EAAAA,EAAAA,MAAA,UACEX,MAAOkF,EAAS2B,oBAChBzF,SAAWC,GAAMhB,EAAkB,sBAAuBgB,EAAEC,OAAOtB,OACnEY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,GAAEc,SAAElE,EAAE,4BAA6B,kCACjDmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,2BACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,2BACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,OAAMc,SAAElE,EAAE,eAAgB,oBACxCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,4BACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,YAAWc,SAAElE,EAAE,kBAAmB,qCAChDmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,YAAWc,SAAElE,EAAE,mBAAoB,8BAIrD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,qBAAsB,0BAE3B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,WAAY,eAEjB+D,EAAAA,EAAAA,MAAA,UACEX,MAAOkF,EAAS4B,mBAAmBC,SACnC3F,SAAWC,GAAMhB,EAAkB,sBAAoBG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClD0E,EAAS4B,oBAAkB,IAC9BC,SAAU1F,EAAEC,OAAOtB,SAErBY,UAAU,uIAAsIE,SAAA,EAEhJC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,aACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,MAAKc,SAAElE,EAAE,MAAO,cAC9BmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,aACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,QAAOc,SAAElE,EAAE,gBAAiB,2BAG9C+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,QAAS,YAEd+D,EAAAA,EAAAA,MAAA,UACEX,MAAOkF,EAAS4B,mBAAmBE,MACnC5F,SAAWC,GAAMhB,EAAkB,sBAAoBG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClD0E,EAAS4B,oBAAkB,IAC9BE,MAAO3F,EAAEC,OAAOtB,SAElBY,UAAU,uIAAsIE,SAAA,EAEhJC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,aACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,QAAOc,SAAElE,EAAE,QAAS,gBAClCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,WAAUc,SAAElE,EAAE,iBAAkB,uBAC9CmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,eAAcc,SAAElE,EAAE,eAAgB,0BAGpD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,UAAW,oBAEhB+D,EAAAA,EAAAA,MAAA,UACEX,MAAOkF,EAAS4B,mBAAmBG,QACnC7F,SAAWC,GAAMhB,EAAkB,sBAAoBG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAClD0E,EAAS4B,oBAAkB,IAC9BG,QAAS5F,EAAEC,OAAOtB,SAEpBY,UAAU,uIAAsIE,SAAA,EAEhJC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,aACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,SAAQc,SAAElE,EAAE,SAAU,mBACpCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,OAAMc,SAAElE,EAAE,OAAQ,oBAChCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,QAAOc,SAAElE,EAAE,gBAAiB,yCAMlD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,kBAAmB,uBAExBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOkF,EAASgC,gBAChB9F,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOtB,OAC/DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,6BAA8B,kFAIjD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOkF,EAASiC,eAChB/F,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,4BAA6B,+DAKhDmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4FAA2FE,UACxGH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,uDAAsDE,SACjElE,EAAE,qBAAsB,wCAE3BmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gDAA+CE,SACzD9D,EACGJ,EAAE,sBAAuB,kCACzBA,EAAE,wBAAyB,sEAInCmE,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACLF,QAASA,IAAM4D,GAAkB,GACjCjE,UAAU,0FAAyFE,SAElG9D,EACGJ,EAAE,iBAAkB,mBACpBA,EAAE,kBAAmB,4BAO/BmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gGAA+FE,UAC5GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnElE,EAAE,iBAAkB,gCAEvBmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kDAAiDE,SAC3DkE,EACGpI,EAAE,0BAA2B,uCAC7BA,EAAE,4BAA6B,yEAIvCmE,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACLF,QAASA,IAAM8D,GAAsB,GACrCnE,UAAU,4FAA2FE,SAEpGkE,EACGpI,EAAE,qBAAsB,wBACxBA,EAAE,sBAAuB,sCAU7C,QACE,OAAO+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,CAAK,yBAAuBwD,OA6DhCkE,IAGD7H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FE,SAAA,EACxGH,EAAAA,EAAAA,MAAA,UACEM,QA3wBK6H,KACfvE,EAAehE,GAAQwI,KAAKC,IAAIzI,EAAO,EAAG,KA2wBhC0I,SAA0B,IAAhB3E,EACV1D,UAAU,uIAAsIE,SAAA,EAEhJC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2BACZhE,EAAE,WAAY,gBAGjB+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEE,QAASA,IAAMkD,EAAS,aACxBvD,UAAU,uFAAsFE,SAE/FlE,EAAE,SAAU,YAGd0H,EAAc0D,EAAMM,QACnB3H,EAAAA,EAAAA,MAAA,UACEM,QAryBCiI,KACXd,EAAa9D,GACfC,EAAehE,GAAQwI,KAAKI,IAAI5I,EAAO,EAAGyH,EAAMM,SAEhDP,EAAAA,GAAMhF,MAAMnG,EAAE,+BAAgC,yCAkyBlCgE,UAAU,kFAAiFE,SAAA,CAE1FlE,EAAE,OAAQ,SACXmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gCAGfG,EAAAA,EAAAA,KAAA,UACEE,QArxBKqG,UACnB,GAAKc,EAAa9D,GAAlB,CAMA,IAAKN,IAAoBD,EAGvB,OAFAgE,EAAAA,GAAMhF,MAAMnG,EAAE,yBAA0B,2CACxCuH,EAAS,UAKX,GAAKF,EAAc,mBAAnB,CAKAd,QAAQiG,IAAI,sBAAuBrF,GACnCZ,QAAQiG,IAAI,oBAAqBrF,EAAKsF,aACtClG,QAAQiG,IAAI,qBAAsBE,aAAaC,QAAQ,kBAEvD9E,GAAW,GACX,IACEtB,QAAQiG,IAAI,6CACZjG,QAAQiG,IAAI,0BAAiBlE,GAC7B/B,QAAQiG,IAAI,gCAAuBpM,GACnCmG,QAAQiG,IAAI,qCAA4BpE,GAGxC,MAAMwE,EAAc,CAElBpE,UAAWF,EAASE,UACpBC,SAAUH,EAASG,SACnBG,WAAYN,EAASM,WACrBC,YAAaP,EAASO,YACtBC,OAAQR,EAASQ,OACjBC,MAAOT,EAASS,MAChBC,MAAOV,EAASU,MAGhBC,QAAS,CACP4D,OAAQvE,EAASW,QACjBC,KAAMZ,EAASY,KACfjD,MAAOqC,EAASa,OAChB2D,QAASxE,EAASc,WAClB0B,QAASxC,EAASwC,SAAW,gBAI/BiC,iBAAkB,CAChB3I,KAAMkE,EAASe,qBACfN,MAAOT,EAASgB,sBAChB0D,aAAc1E,EAASiB,0BAA4BjB,EAAS0C,8BAI9DiC,UAAW,CACTC,SAAU5E,EAASkB,kBACnB2D,aAAc7E,EAASmB,iBAAmBnB,EAAS2C,sBACnDmC,YAAa9E,EAAS+E,sBAIxB1D,eAAgBrB,EAASqB,eACzBC,mBAAoBtB,EAASsB,mBAC7BC,UAAWvB,EAASuB,UACpBhJ,iBAAkByH,EAASzH,iBAC3BiJ,mBAAoBxB,EAASwB,mBAG7BwD,uBAAwBlN,EACxBmN,eAAgBnF,GAQlB,GALA7B,QAAQiG,IAAI,4CAAmCI,GAC/CrG,QAAQiG,IAAI,wCACZjG,QAAQiG,IAAI,sCAA6BE,aAAaC,QAAQ,iBAAmB,UAAY,WAGzF7E,EAAY,CACd,MAAM0F,QAAiBC,EAAAA,GAAIC,IAAI,aAADzJ,OAAcuB,GAAMoH,GAClD,IAAIY,EAASG,QAIX,MAAM,IAAIC,MAAMJ,EAASK,SAAW,4BAHpC1C,EAAAA,GAAMwC,QAAQ3N,EAAE,6BAA8B,iCAC9CuH,EAAS,aAADtD,OAAcuB,GAI1B,KAAO,CACLe,QAAQiG,IAAI,yDACZ,MAAMgB,QAAiBC,EAAAA,GAAIK,KAAK,YAAalB,GAE7C,GADArG,QAAQiG,IAAI,sCAA6BgB,IACrCA,EAASG,QAIX,MAAM,IAAIC,MAAMJ,EAASK,SAAW,4BAHpC1C,EAAAA,GAAMwC,QAAQ3N,EAAE,6BAA8B,iCAC9CuH,EAAS,YAIb,CACF,CAAE,MAAOpB,GACPI,QAAQJ,MAAM,iCAA6BA,GAC3CI,QAAQJ,MAAM,wBAAoB,CAChC0H,QAAS1H,EAAM0H,QACfL,SAAUrH,EAAMqH,SAChBO,QAAS5H,EAAM4H,QACfC,OAAQ7H,EAAM6H,SAIhB,IAAIC,EAAejO,EAAE,uBAAwB,0BAEzCmG,EAAMqH,UAERjH,QAAQJ,MAAM,yBAA0BA,EAAMqH,SAASU,MACvD3H,QAAQJ,MAAM,eAAgBA,EAAMqH,SAASW,QAEf,MAA1BhI,EAAMqH,SAASW,OACjBF,EAAejO,EAAE,yBAA0B,iDACR,MAA1BmG,EAAMqH,SAASW,OACxBF,EAAejO,EAAE,0BAA2B,kDACT,MAA1BmG,EAAMqH,SAASW,OACxBF,EAAejO,EAAE,uBAAwB,mDAChCmG,EAAMqH,SAASU,MAAQ/H,EAAMqH,SAASU,KAAKL,UACpDI,EAAe9H,EAAMqH,SAASU,KAAKL,UAE5B1H,EAAM4H,SAEfxH,QAAQJ,MAAM,iBAAkBA,EAAM4H,SACtCE,EAAejO,EAAE,eAAgB,gEAGjCuG,QAAQJ,MAAM,iBAAkBA,EAAM0H,SACtCI,EAAe9H,EAAM0H,SAAWI,GAGlC9C,EAAAA,GAAMhF,MAAM8H,EACd,CAAC,QACCpG,GAAW,EACb,CA1HA,MAFEsD,EAAAA,GAAMhF,MAAMnG,EAAE,0BAA2B,iDAX3C,MAFEmL,EAAAA,GAAMhF,MAAMnG,EAAE,+BAAgC,yCAoxBlCqM,SAAUzE,EACV5D,UAAU,wGAAuGE,SAEhH0D,GACC7D,EAAAA,EAAAA,MAAAqK,EAAAA,SAAA,CAAAlK,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gCACZ8D,EAAa9H,EAAE,WAAY,eAAiBA,EAAE,WAAY,mBAG7D+D,EAAAA,EAAAA,MAAAqK,EAAAA,SAAA,CAAAlK,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZ8D,EAAa9H,EAAE,gBAAiB,kBAAoBA,EAAE,gBAAiB,mCAWvFgI,IACC7D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iFAAgFE,UAC7FC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+FAA8FE,UAC3GC,EAAAA,EAAAA,KAACwB,EAAa,CAAAzB,UACZC,EAAAA,EAAAA,KAACkK,EAAAA,QAAsB,CACrB1O,YAAaS,GAAkB,CAAC,EAChCR,OA9zBgBsO,IAC5B7N,EAAkB6N,GAClBjG,GAAkB,GAClBkD,EAAAA,GAAMwC,QAAQ3N,EAAE,kBAAmB,mCA4zBvBH,SAAUA,IAAMoI,GAAkB,GAClClI,YAAa,CACXqE,KAAM,GAAAH,OAAGqE,EAASE,UAAS,KAAAvE,OAAIqE,EAASG,UAAW6F,QAAUtO,EAAE,aAAc,wBASxFkI,IACC/D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iFAAgFE,UAC7FC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+FAA8FE,UAC3GC,EAAAA,EAAAA,KAACwB,EAAa,CAAAzB,UACZC,EAAAA,EAAAA,KAACoK,EAAAA,QAAkB,CACjB3D,UAAW,KACXjL,YAAayI,GAAsB,CAAC,EACpCxI,OA30BoBsO,IAChC7F,EAAsB6F,GACtB/F,GAAsB,GACtBgD,EAAAA,GAAMwC,QAAQ3N,EAAE,sBAAuB,wCAy0B3BH,SAAUA,IAAMsI,GAAsB,c", "sources": ["components/SpecialNeeds/SpecialNeedsAssessment.jsx", "components/Common/ErrorBoundary.jsx", "pages/Patients/AddPatient.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SpecialNeedsAssessment = ({\n  initialData,\n  onSave,\n  onCancel,\n  isEditing = false,\n  patientInfo = {}\n}) => {\n  const { t, isRTL } = useLanguage();\n\n  // Ensure initialData is always an object\n  const safeInitialData = initialData || {};\n\n  const [assessmentData, setAssessmentData] = useState({\n    // Basic Assessment Info\n    assessmentDate: new Date().toISOString().split('T')[0],\n    assessedBy: '',\n    caregiverPresent: '',\n\n    // Diagnosis and Conditions\n    primaryDiagnosis: safeInitialData.primaryDiagnosis || '',\n    secondaryDiagnoses: safeInitialData.secondaryDiagnoses || [],\n    cognitiveLevel: safeInitialData.cognitiveLevel || '',\n    functionalLevel: safeInitialData.functionalLevel || '',\n\n    // Communication Assessment\n    communicationAbilities: {\n      verbal: safeInitialData.communicationAbilities?.verbal || 'none',\n      nonVerbal: safeInitialData.communicationAbilities?.nonVerbal || [],\n      comprehension: safeInitialData.communicationAbilities?.comprehension || 'limited',\n      expression: safeInitialData.communicationAbilities?.expression || 'limited'\n    },\n\n    // Sensory Processing\n    sensoryProfile: {\n      visual: safeInitialData.sensoryProfile?.visual || 'typical',\n      auditory: safeInitialData.sensoryProfile?.auditory || 'typical',\n      tactile: safeInitialData.sensoryProfile?.tactile || 'typical',\n      vestibular: safeInitialData.sensoryProfile?.vestibular || 'typical',\n      proprioceptive: safeInitialData.sensoryProfile?.proprioceptive || 'typical'\n    },\n\n    // Behavioral Patterns\n    behaviorProfile: {\n      socialInteraction: safeInitialData.behaviorProfile?.socialInteraction || 'limited',\n      attentionSpan: safeInitialData.behaviorProfile?.attentionSpan || 'short',\n      emotionalRegulation: safeInitialData.behaviorProfile?.emotionalRegulation || 'needs-support',\n      adaptability: safeInitialData.behaviorProfile?.adaptability || 'rigid'\n    },\n    \n    // Motor Skills\n    motorSkills: {\n      grossMotor: safeInitialData.motorSkills?.grossMotor || 'delayed',\n      fineMotor: safeInitialData.motorSkills?.fineMotor || 'delayed',\n      coordination: safeInitialData.motorSkills?.coordination || 'impaired',\n      balance: safeInitialData.motorSkills?.balance || 'impaired'\n    },\n\n    // Adaptive Strategies\n    adaptiveStrategies: {\n      environmentalModifications: safeInitialData.adaptiveStrategies?.environmentalModifications || [],\n      communicationSupports: safeInitialData.adaptiveStrategies?.communicationSupports || [],\n      behavioralSupports: safeInitialData.adaptiveStrategies?.behavioralSupports || [],\n      sensorySupports: safeInitialData.adaptiveStrategies?.sensorySupports || []\n    },\n\n    // Goals and Recommendations\n    treatmentGoals: safeInitialData.treatmentGoals || [],\n    recommendations: safeInitialData.recommendations || '',\n    followUpNeeded: safeInitialData.followUpNeeded || false,\n    nextAssessmentDate: safeInitialData.nextAssessmentDate || '',\n\n    // Additional Notes\n    strengths: safeInitialData.strengths || '',\n    challenges: safeInitialData.challenges || '',\n    familyConcerns: safeInitialData.familyConcerns || '',\n    additionalNotes: safeInitialData.additionalNotes || ''\n  });\n\n  const cognitiveLevels = [\n    { value: 'typical', label: t('typical', 'Typical') },\n    { value: 'mild-delay', label: t('mildDelay', 'Mild Delay') },\n    { value: 'moderate-delay', label: t('moderateDelay', 'Moderate Delay') },\n    { value: 'severe-delay', label: t('severeDelay', 'Severe Delay') },\n    { value: 'profound-delay', label: t('profoundDelay', 'Profound Delay') }\n  ];\n\n  const functionalLevels = [\n    { value: 'independent', label: t('independent', 'Independent') },\n    { value: 'minimal-assist', label: t('minimalAssist', 'Minimal Assistance') },\n    { value: 'moderate-assist', label: t('moderateAssist', 'Moderate Assistance') },\n    { value: 'maximum-assist', label: t('maximumAssist', 'Maximum Assistance') },\n    { value: 'total-assist', label: t('totalAssist', 'Total Assistance') }\n  ];\n\n  const communicationLevels = [\n    { value: 'none', label: t('none', 'None') },\n    { value: 'limited', label: t('limited', 'Limited') },\n    { value: 'functional', label: t('functional', 'Functional') },\n    { value: 'good', label: t('good', 'Good') },\n    { value: 'excellent', label: t('excellent', 'Excellent') }\n  ];\n\n  const sensoryLevels = [\n    { value: 'typical', label: t('typical', 'Typical') },\n    { value: 'hyposensitive', label: t('hyposensitive', 'Under-responsive') },\n    { value: 'hypersensitive', label: t('hypersensitive', 'Over-responsive') },\n    { value: 'seeking', label: t('seeking', 'Sensory Seeking') },\n    { value: 'avoiding', label: t('avoiding', 'Sensory Avoiding') }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setAssessmentData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleNestedChange = (category, field, value) => {\n    setAssessmentData(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [field]: value\n      }\n    }));\n  };\n\n  const handleArrayChange = (field, value, isAdd = true) => {\n    setAssessmentData(prev => ({\n      ...prev,\n      [field]: isAdd \n        ? [...prev[field], value]\n        : prev[field].filter(item => item !== value)\n    }));\n  };\n\n  const handleSave = () => {\n    if (onSave) {\n      onSave(assessmentData);\n    }\n  };\n\n  return (\n    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n            {t('specialNeedsAssessment', 'Special Needs Assessment')}\n          </h2>\n          {patientInfo.name && (\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('patient', 'Patient')}: {patientInfo.name}\n            </p>\n          )}\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={onCancel}\n            className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          <button\n            onClick={handleSave}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            {t('saveAssessment', 'Save Assessment')}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"space-y-8\">\n        {/* Basic Assessment Information */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('basicAssessmentInfo', 'Basic Assessment Information')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('assessmentDate', 'Assessment Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={assessmentData.assessmentDate}\n                onChange={(e) => handleInputChange('assessmentDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('assessedBy', 'Assessed By')}\n              </label>\n              <input\n                type=\"text\"\n                value={assessmentData.assessedBy}\n                onChange={(e) => handleInputChange('assessedBy', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('therapistName', 'Therapist Name')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('caregiverPresent', 'Caregiver Present')}\n              </label>\n              <input\n                type=\"text\"\n                value={assessmentData.caregiverPresent}\n                onChange={(e) => handleInputChange('caregiverPresent', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('caregiverName', 'Caregiver Name')}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Diagnosis and Functional Level */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('diagnosisAndFunctionalLevel', 'Diagnosis and Functional Level')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('cognitiveLevel', 'Cognitive Level')}\n              </label>\n              <select\n                value={assessmentData.cognitiveLevel}\n                onChange={(e) => handleInputChange('cognitiveLevel', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                <option value=\"\">{t('selectLevel', 'Select Level')}</option>\n                {cognitiveLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('functionalLevel', 'Functional Level')}\n              </label>\n              <select\n                value={assessmentData.functionalLevel}\n                onChange={(e) => handleInputChange('functionalLevel', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                <option value=\"\">{t('selectLevel', 'Select Level')}</option>\n                {functionalLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Communication Assessment */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('communicationAssessment', 'Communication Assessment')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('verbalCommunication', 'Verbal Communication')}\n              </label>\n              <select\n                value={assessmentData.communicationAbilities.verbal}\n                onChange={(e) => handleNestedChange('communicationAbilities', 'verbal', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {communicationLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('comprehension', 'Comprehension')}\n              </label>\n              <select\n                value={assessmentData.communicationAbilities.comprehension}\n                onChange={(e) => handleNestedChange('communicationAbilities', 'comprehension', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {communicationLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Sensory Processing */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('sensoryProcessing', 'Sensory Processing')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {Object.entries(assessmentData.sensoryProfile).map(([sense, value]) => (\n              <div key={sense}>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t(sense, sense.charAt(0).toUpperCase() + sense.slice(1))}\n                </label>\n                <select\n                  value={value}\n                  onChange={(e) => handleNestedChange('sensoryProfile', sense, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  {sensoryLevels.map(level => (\n                    <option key={level.value} value={level.value}>{level.label}</option>\n                  ))}\n                </select>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Motor Skills */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('motorSkills', 'Motor Skills')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {Object.entries(assessmentData.motorSkills).map(([skill, value]) => (\n              <div key={skill}>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t(skill, skill.charAt(0).toUpperCase() + skill.slice(1))}\n                </label>\n                <select\n                  value={value}\n                  onChange={(e) => handleNestedChange('motorSkills', skill, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"typical\">{t('typical', 'Typical')}</option>\n                  <option value=\"delayed\">{t('delayed', 'Delayed')}</option>\n                  <option value=\"impaired\">{t('impaired', 'Impaired')}</option>\n                  <option value=\"emerging\">{t('emerging', 'Emerging')}</option>\n                </select>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Strengths and Challenges */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('strengthsAndChallenges', 'Strengths and Challenges')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('strengths', 'Strengths')}\n              </label>\n              <textarea\n                value={assessmentData.strengths}\n                onChange={(e) => handleInputChange('strengths', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('strengthsPlaceholder', 'List patient strengths and abilities...')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('challenges', 'Challenges')}\n              </label>\n              <textarea\n                value={assessmentData.challenges}\n                onChange={(e) => handleInputChange('challenges', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('challengesPlaceholder', 'List areas of difficulty and challenges...')}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Recommendations */}\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('recommendationsAndGoals', 'Recommendations and Goals')}\n          </h3>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('recommendations', 'Recommendations')}\n              </label>\n              <textarea\n                value={assessmentData.recommendations}\n                onChange={(e) => handleInputChange('recommendations', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('recommendationsPlaceholder', 'Treatment recommendations and strategies...')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('familyConcerns', 'Family Concerns')}\n              </label>\n              <textarea\n                value={assessmentData.familyConcerns}\n                onChange={(e) => handleInputChange('familyConcerns', e.target.value)}\n                rows=\"3\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('familyConcernsPlaceholder', 'Family concerns and priorities...')}\n              />\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <input\n                type=\"checkbox\"\n                id=\"followUpNeeded\"\n                checked={assessmentData.followUpNeeded}\n                onChange={(e) => handleInputChange('followUpNeeded', e.target.checked)}\n                className=\"w-4 h-4 text-blue-600\"\n              />\n              <label htmlFor=\"followUpNeeded\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {t('followUpAssessmentNeeded', 'Follow-up assessment needed')}\n              </label>\n            </div>\n            {assessmentData.followUpNeeded && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('nextAssessmentDate', 'Next Assessment Date')}\n                </label>\n                <input\n                  type=\"date\"\n                  value={assessmentData.nextAssessmentDate}\n                  onChange={(e) => handleInputChange('nextAssessmentDate', e.target.value)}\n                  className=\"w-full md:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SpecialNeedsAssessment;\n", "import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error to console for debugging\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n          <div className=\"max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6\">\n            <div className=\"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full mb-4\">\n              <i className=\"fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl\"></i>\n            </div>\n            \n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white text-center mb-2\">\n              Something went wrong\n            </h2>\n            \n            <p className=\"text-gray-600 dark:text-gray-400 text-center mb-6\">\n              We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.\n            </p>\n            \n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => window.location.reload()}\n                className=\"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                <i className=\"fas fa-refresh mr-2\"></i>\n                Refresh Page\n              </button>\n              \n              <button\n                onClick={() => window.history.back()}\n                className=\"flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n              >\n                <i className=\"fas fa-arrow-left mr-2\"></i>\n                Go Back\n              </button>\n            </div>\n            \n            {/* Development error details */}\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mt-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg\">\n                <summary className=\"cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Error Details (Development)\n                </summary>\n                <div className=\"text-xs text-gray-600 dark:text-gray-400 font-mono\">\n                  <div className=\"mb-2\">\n                    <strong>Error:</strong> {this.state.error.toString()}\n                  </div>\n                  <div>\n                    <strong>Stack Trace:</strong>\n                    <pre className=\"mt-1 whitespace-pre-wrap\">\n                      {this.state.errorInfo.componentStack}\n                    </pre>\n                  </div>\n                </div>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// Functional wrapper component for using hooks\nconst ErrorBoundaryWrapper = ({ children, fallback }) => {\n  const { t } = useTranslation();\n  \n  return (\n    <ErrorBoundary fallback={fallback}>\n      {children}\n    </ErrorBoundary>\n  );\n};\n\nexport default ErrorBoundary;\nexport { ErrorBoundaryWrapper };\n", "import React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport SpecialNeedsAssessment from '../../components/SpecialNeeds/SpecialNeedsAssessment';\nimport CARFAssessmentForm from '../../components/CARF/CARFAssessmentForm';\nimport ErrorBoundary from '../../components/Common/ErrorBoundary';\nimport { apiHelpers as api } from '../../services/api';\n\nconst AddPatient = () => {\n  const { t, isRTL } = useLanguage();\n  const { user, isAuthenticated, hasPermission } = useAuth();\n  const navigate = useNavigate();\n  const { id } = useParams(); // Get patient ID from URL for edit mode\n  const [currentStep, setCurrentStep] = useState(1);\n  const [loading, setLoading] = useState(false);\n  const [isEditMode, setIsEditMode] = useState(false);\n  const [showAssessment, setShowAssessment] = useState(false);\n  const [assessmentData, setAssessmentData] = useState(null);\n  const [showCARFAssessment, setShowCARFAssessment] = useState(false);\n  const [carfAssessmentData, setCARFAssessmentData] = useState(null);\n\n  const [formData, setFormData] = useState({\n    // Basic Information\n    firstName: '',\n    lastName: '',\n    firstNameAr: '',\n    lastNameAr: '',\n    nationalId: '',\n    dateOfBirth: '',\n    gender: '',\n    phone: '',\n    email: '',\n    \n    // Address\n    address: '',\n    city: '',\n    region: '',\n    postalCode: '',\n    \n    // Emergency Contact\n    emergencyContactName: '',\n    emergencyContactPhone: '',\n    emergencyContactRelation: '',\n    \n    // Insurance\n    insuranceProvider: '',\n    insuranceNumber: '',\n    insuranceExpiry: '',\n    \n    // Medical Information\n    medicalHistory: '',\n    currentMedications: '',\n    allergies: '',\n    primaryDiagnosis: '',\n    referringPhysician: '',\n    \n    // Special Needs Assessment\n    hasSpecialNeeds: false,\n    specialNeedsType: [],\n    communicationMethod: '',\n    sensoryPreferences: {\n      lighting: 'normal',\n      sound: 'normal',\n      texture: 'normal'\n    },\n    behavioralNotes: '',\n    caregiverNotes: ''\n  });\n\n  // Check if we're in edit mode and load patient data\n  useEffect(() => {\n    if (id) {\n      setIsEditMode(true);\n      loadPatientData(id);\n    }\n  }, [id]);\n\n  const loadPatientData = async (patientId) => {\n    setLoading(true);\n    try {\n      // Mock patient data - replace with actual API call\n      const mockPatient = {\n        id: patientId,\n        firstName: 'Ahmed',\n        lastName: 'Mohammed Al-Ahmed',\n        firstNameAr: 'أحمد',\n        lastNameAr: 'محمد الأحمد',\n        nationalId: '**********',\n        dateOfBirth: '1995-03-15',\n        gender: 'male',\n        phone: '+966501234567',\n        email: '<EMAIL>',\n        address: 'Riyadh',\n        addressAr: 'الرياض',\n        city: 'Riyadh',\n        postalCode: '12345',\n        country: 'Saudi Arabia',\n        emergencyContactName: 'Fatima Al-Ahmed',\n        emergencyContactNameAr: 'فاطمة الأحمد',\n        emergencyContactPhone: '+966507654321',\n        emergencyContactRelationship: 'mother',\n        insuranceProvider: 'Bupa Arabia',\n        insurancePolicyNumber: 'BP123456789',\n        primaryDiagnosis: 'Cerebral Palsy',\n        currentMedications: 'Baclofen 10mg, Vitamin D',\n        allergies: 'Penicillin',\n        referringPhysician: 'Dr. Sarah Al-Rashid'\n      };\n\n      setFormData(prevData => ({\n        ...prevData,\n        ...mockPatient\n      }));\n    } catch (error) {\n      toast.error(t('errorLoadingPatient', 'Error loading patient data'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const steps = [\n    { id: 1, title: t('basicInfo', 'Basic Information'), icon: 'fas fa-user' },\n    { id: 2, title: t('contactInfo', 'Contact Information'), icon: 'fas fa-phone' },\n    { id: 3, title: t('emergencyContact', 'Emergency Contact'), icon: 'fas fa-exclamation-triangle' },\n    { id: 4, title: t('insurance', 'Insurance'), icon: 'fas fa-shield-alt' },\n    { id: 5, title: t('medicalInfo', 'Medical Information'), icon: 'fas fa-notes-medical' },\n    { id: 6, title: t('specialNeeds', 'Special Needs'), icon: 'fas fa-puzzle-piece' }\n  ];\n\n  const specialNeedsTypes = [\n    { id: 'autism', label: t('autism', 'Autism Spectrum Disorder'), icon: '🧩' },\n    { id: 'cerebralPalsy', label: t('cerebralPalsy', 'Cerebral Palsy'), icon: '🦽' },\n    { id: 'downSyndrome', label: t('downSyndrome', 'Down Syndrome'), icon: '💙' },\n    { id: 'intellectualDisability', label: t('intellectualDisability', 'Intellectual Disability'), icon: '🧠' },\n    { id: 'adhd', label: t('adhd', 'ADHD'), icon: '⚡' },\n    { id: 'sensoryProcessing', label: t('sensoryProcessing', 'Sensory Processing Disorder'), icon: '👁️' }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSpecialNeedsToggle = (type) => {\n    setFormData(prev => ({\n      ...prev,\n      specialNeedsType: prev.specialNeedsType.includes(type)\n        ? prev.specialNeedsType.filter(t => t !== type)\n        : [...prev.specialNeedsType, type]\n    }));\n  };\n\n  const validateStep = (step) => {\n    switch (step) {\n      case 1:\n        return formData.firstName && formData.lastName && formData.nationalId && formData.dateOfBirth && formData.gender;\n      case 2:\n        return formData.phone && formData.address && formData.city;\n      case 3:\n        return formData.emergencyContactName && formData.emergencyContactPhone;\n      case 4:\n        return true; // Insurance is optional\n      case 5:\n        return true; // Medical info is optional\n      case 6:\n        return true; // Special needs is optional\n      default:\n        return true;\n    }\n  };\n\n  const nextStep = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, steps.length));\n    } else {\n      toast.error(t('pleaseCompleteRequiredFields', 'Please complete all required fields'));\n    }\n  };\n\n  const prevStep = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1));\n  };\n\n  const handleAssessmentSave = (data) => {\n    setAssessmentData(data);\n    setShowAssessment(false);\n    toast.success(t('assessmentSaved', 'Assessment saved successfully'));\n  };\n\n  const handleCARFAssessmentSave = (data) => {\n    setCARFAssessmentData(data);\n    setShowCARFAssessment(false);\n    toast.success(t('carfAssessmentSaved', 'CARF Assessment saved successfully'));\n  };\n\n  const handleSubmit = async () => {\n    if (!validateStep(currentStep)) {\n      toast.error(t('pleaseCompleteRequiredFields', 'Please complete all required fields'));\n      return;\n    }\n\n    // Check authentication\n    if (!isAuthenticated || !user) {\n      toast.error(t('authenticationRequired', 'Please log in to create a patient'));\n      navigate('/login');\n      return;\n    }\n\n    // Check permissions using the hasPermission function from AuthContext\n    if (!hasPermission('create_patients')) {\n      toast.error(t('insufficientPermissions', 'You do not have permission to create patients'));\n      return;\n    }\n\n    console.log('User authenticated:', user);\n    console.log('User permissions:', user.permissions);\n    console.log('Token available:', !!localStorage.getItem('pt_auth_token'));\n\n    setLoading(true);\n    try {\n      console.log('🚀 Starting patient creation...');\n      console.log('📋 Form data:', formData);\n      console.log('🔍 Assessment data:', assessmentData);\n      console.log('📊 CARF assessment data:', carfAssessmentData);\n\n      // Transform form data to match backend API structure\n      const patientData = {\n        // Basic information\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        nationalId: formData.nationalId,\n        dateOfBirth: formData.dateOfBirth,\n        gender: formData.gender,\n        phone: formData.phone,\n        email: formData.email,\n\n        // Address information\n        address: {\n          street: formData.address,\n          city: formData.city,\n          state: formData.region,\n          zipCode: formData.postalCode,\n          country: formData.country || 'Saudi Arabia'\n        },\n\n        // Emergency contact (transform flat fields to nested object)\n        emergencyContact: {\n          name: formData.emergencyContactName,\n          phone: formData.emergencyContactPhone,\n          relationship: formData.emergencyContactRelation || formData.emergencyContactRelationship\n        },\n\n        // Insurance information\n        insurance: {\n          provider: formData.insuranceProvider,\n          policyNumber: formData.insuranceNumber || formData.insurancePolicyNumber,\n          groupNumber: formData.insuranceGroupNumber\n        },\n\n        // Medical information\n        medicalHistory: formData.medicalHistory,\n        currentMedications: formData.currentMedications,\n        allergies: formData.allergies,\n        primaryDiagnosis: formData.primaryDiagnosis,\n        referringPhysician: formData.referringPhysician,\n\n        // Assessment data\n        specialNeedsAssessment: assessmentData,\n        carfAssessment: carfAssessmentData\n      };\n\n      console.log('📤 Sending patient data to API:', patientData);\n      console.log('🔗 API endpoint: /patients');\n      console.log('🔑 Token in localStorage:', localStorage.getItem('pt_auth_token') ? 'Present' : 'Missing');\n\n      // Real API call\n      if (isEditMode) {\n        const response = await api.put(`/patients/${id}`, patientData);\n        if (response.success) {\n          toast.success(t('patientUpdatedSuccessfully', 'Patient updated successfully'));\n          navigate(`/patients/${id}`);\n        } else {\n          throw new Error(response.message || 'Failed to update patient');\n        }\n      } else {\n        console.log('🔄 Making POST request to create patient...');\n        const response = await api.post('/patients', patientData);\n        console.log('📥 API response received:', response);\n        if (response.success) {\n          toast.success(t('patientCreatedSuccessfully', 'Patient created successfully'));\n          navigate('/patients');\n        } else {\n          throw new Error(response.message || 'Failed to create patient');\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error creating patient:', error);\n      console.error('❌ Error details:', {\n        message: error.message,\n        response: error.response,\n        request: error.request,\n        config: error.config\n      });\n\n      // More detailed error handling\n      let errorMessage = t('errorCreatingPatient', 'Error creating patient');\n\n      if (error.response) {\n        // Server responded with error status\n        console.error('Server Error Response:', error.response.data);\n        console.error('Status Code:', error.response.status);\n\n        if (error.response.status === 401) {\n          errorMessage = t('authenticationRequired', 'Authentication required. Please log in again.');\n        } else if (error.response.status === 403) {\n          errorMessage = t('insufficientPermissions', 'You do not have permission to create patients.');\n        } else if (error.response.status === 409) {\n          errorMessage = t('patientAlreadyExists', 'A patient with this National ID already exists.');\n        } else if (error.response.data && error.response.data.message) {\n          errorMessage = error.response.data.message;\n        }\n      } else if (error.request) {\n        // Request was made but no response received\n        console.error('Network Error:', error.request);\n        errorMessage = t('networkError', 'Network error. Please check your connection and try again.');\n      } else {\n        // Something else happened\n        console.error('Error Message:', error.message);\n        errorMessage = error.message || errorMessage;\n      }\n\n      toast.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('firstName', 'First Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.firstName}\n                  onChange={(e) => handleInputChange('firstName', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('lastName', 'Last Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.lastName}\n                  onChange={(e) => handleInputChange('lastName', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('firstNameAr', 'First Name (Arabic)')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.firstNameAr}\n                  onChange={(e) => handleInputChange('firstNameAr', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  dir=\"rtl\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('lastNameAr', 'Last Name (Arabic)')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.lastNameAr}\n                  onChange={(e) => handleInputChange('lastNameAr', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  dir=\"rtl\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('nationalId', 'National ID')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.nationalId}\n                  onChange={(e) => handleInputChange('nationalId', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('dateOfBirth', 'Date of Birth')} *\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.dateOfBirth}\n                  onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('gender', 'Gender')} *\n                </label>\n                <div className=\"flex space-x-4\">\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      name=\"gender\"\n                      value=\"male\"\n                      checked={formData.gender === 'male'}\n                      onChange={(e) => handleInputChange('gender', e.target.value)}\n                      className=\"mr-2\"\n                    />\n                    {t('male', 'Male')}\n                  </label>\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      name=\"gender\"\n                      value=\"female\"\n                      checked={formData.gender === 'female'}\n                      onChange={(e) => handleInputChange('gender', e.target.value)}\n                      className=\"mr-2\"\n                    />\n                    {t('female', 'Female')}\n                  </label>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('phone', 'Phone Number')} *\n                </label>\n                <input\n                  type=\"tel\"\n                  value={formData.phone}\n                  onChange={(e) => handleInputChange('phone', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('email', 'Email')}\n                </label>\n                <input\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={(e) => handleInputChange('email', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('address', 'Address')} *\n                </label>\n                <textarea\n                  value={formData.address}\n                  onChange={(e) => handleInputChange('address', e.target.value)}\n                  rows=\"3\"\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('city', 'City')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.city}\n                  onChange={(e) => handleInputChange('city', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('region', 'Region')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.region}\n                  onChange={(e) => handleInputChange('region', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('emergencyContactName', 'Emergency Contact Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.emergencyContactName}\n                  onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('emergencyContactPhone', 'Emergency Contact Phone')} *\n                </label>\n                <input\n                  type=\"tel\"\n                  value={formData.emergencyContactPhone}\n                  onChange={(e) => handleInputChange('emergencyContactPhone', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('emergencyContactRelation', 'Relationship')}\n                </label>\n                <select\n                  value={formData.emergencyContactRelation}\n                  onChange={(e) => handleInputChange('emergencyContactRelation', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"\">{t('selectRelationship', 'Select Relationship')}</option>\n                  <option value=\"parent\">{t('parent', 'Parent')}</option>\n                  <option value=\"spouse\">{t('spouse', 'Spouse')}</option>\n                  <option value=\"sibling\">{t('sibling', 'Sibling')}</option>\n                  <option value=\"child\">{t('child', 'Child')}</option>\n                  <option value=\"friend\">{t('friend', 'Friend')}</option>\n                  <option value=\"caregiver\">{t('caregiver', 'Caregiver')}</option>\n                  <option value=\"other\">{t('other', 'Other')}</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('insuranceProvider', 'Insurance Provider')}\n                </label>\n                <select\n                  value={formData.insuranceProvider}\n                  onChange={(e) => handleInputChange('insuranceProvider', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"\">{t('selectInsuranceProvider', 'Select Insurance Provider')}</option>\n                  <option value=\"bupa\">{t('bupa', 'Bupa Arabia')}</option>\n                  <option value=\"tawuniya\">{t('tawuniya', 'Tawuniya')}</option>\n                  <option value=\"medgulf\">{t('medgulf', 'MedGulf')}</option>\n                  <option value=\"alrajhi\">{t('alrajhi', 'Al Rajhi Takaful')}</option>\n                  <option value=\"saico\">{t('saico', 'SAICO')}</option>\n                  <option value=\"ncci\">{t('ncci', 'NCCI')}</option>\n                  <option value=\"other\">{t('other', 'Other')}</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('insuranceNumber', 'Insurance Number')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.insuranceNumber}\n                  onChange={(e) => handleInputChange('insuranceNumber', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('insuranceExpiry', 'Insurance Expiry Date')}\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.insuranceExpiry}\n                  onChange={(e) => handleInputChange('insuranceExpiry', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            </div>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('primaryDiagnosis', 'Primary Diagnosis')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.primaryDiagnosis}\n                  onChange={(e) => handleInputChange('primaryDiagnosis', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('primaryDiagnosisPlaceholder', 'Enter primary diagnosis or condition')}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('referringPhysician', 'Referring Physician')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.referringPhysician}\n                  onChange={(e) => handleInputChange('referringPhysician', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('referringPhysicianPlaceholder', 'Enter referring physician name')}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('medicalHistory', 'Medical History')}\n                </label>\n                <textarea\n                  value={formData.medicalHistory}\n                  onChange={(e) => handleInputChange('medicalHistory', e.target.value)}\n                  rows=\"4\"\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('medicalHistoryPlaceholder', 'Enter relevant medical history, previous surgeries, conditions...')}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('currentMedications', 'Current Medications')}\n                </label>\n                <textarea\n                  value={formData.currentMedications}\n                  onChange={(e) => handleInputChange('currentMedications', e.target.value)}\n                  rows=\"3\"\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('currentMedicationsPlaceholder', 'List current medications, dosages, and frequency...')}\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('allergies', 'Allergies')}\n                </label>\n                <textarea\n                  value={formData.allergies}\n                  onChange={(e) => handleInputChange('allergies', e.target.value)}\n                  rows=\"2\"\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('allergiesPlaceholder', 'List any known allergies to medications, foods, or materials...')}\n                />\n              </div>\n            </div>\n          </div>\n        );\n\n      case 6:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"flex items-center space-x-3 mb-6\">\n              <input\n                type=\"checkbox\"\n                id=\"hasSpecialNeeds\"\n                checked={formData.hasSpecialNeeds}\n                onChange={(e) => handleInputChange('hasSpecialNeeds', e.target.checked)}\n                className=\"w-4 h-4 text-blue-600\"\n              />\n              <label htmlFor=\"hasSpecialNeeds\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {t('patientHasSpecialNeeds', 'Patient has special needs')}\n              </label>\n            </div>\n\n            {formData.hasSpecialNeeds && (\n              <div className=\"space-y-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                    {t('specialNeedsType', 'Type of Special Needs')}\n                  </label>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                    {specialNeedsTypes.map((type) => (\n                      <div\n                        key={type.id}\n                        onClick={() => handleSpecialNeedsToggle(type.id)}\n                        className={`p-4 border rounded-lg cursor-pointer transition-colors ${\n                          formData.specialNeedsType.includes(type.id)\n                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'\n                        }`}\n                      >\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-2xl\">{type.icon}</span>\n                          <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                            {type.label}\n                          </span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('communicationMethod', 'Preferred Communication Method')}\n                  </label>\n                  <select\n                    value={formData.communicationMethod}\n                    onChange={(e) => handleInputChange('communicationMethod', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  >\n                    <option value=\"\">{t('selectCommunicationMethod', 'Select Communication Method')}</option>\n                    <option value=\"verbal\">{t('verbal', 'Verbal Communication')}</option>\n                    <option value=\"visual\">{t('visual', 'Visual Aids/Pictures')}</option>\n                    <option value=\"sign\">{t('signLanguage', 'Sign Language')}</option>\n                    <option value=\"written\">{t('written', 'Written Communication')}</option>\n                    <option value=\"assistive\">{t('assistiveDevice', 'Assistive Communication Device')}</option>\n                    <option value=\"caregiver\">{t('throughCaregiver', 'Through Caregiver')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                    {t('sensoryPreferences', 'Sensory Preferences')}\n                  </label>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                    <div>\n                      <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\n                        {t('lighting', 'Lighting')}\n                      </label>\n                      <select\n                        value={formData.sensoryPreferences.lighting}\n                        onChange={(e) => handleInputChange('sensoryPreferences', {\n                          ...formData.sensoryPreferences,\n                          lighting: e.target.value\n                        })}\n                        className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      >\n                        <option value=\"normal\">{t('normal', 'Normal')}</option>\n                        <option value=\"dim\">{t('dim', 'Dim/Low')}</option>\n                        <option value=\"bright\">{t('bright', 'Bright')}</option>\n                        <option value=\"avoid\">{t('avoidFlashing', 'Avoid Flashing')}</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\n                        {t('sound', 'Sound')}\n                      </label>\n                      <select\n                        value={formData.sensoryPreferences.sound}\n                        onChange={(e) => handleInputChange('sensoryPreferences', {\n                          ...formData.sensoryPreferences,\n                          sound: e.target.value\n                        })}\n                        className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      >\n                        <option value=\"normal\">{t('normal', 'Normal')}</option>\n                        <option value=\"quiet\">{t('quiet', 'Quiet/Low')}</option>\n                        <option value=\"noSudden\">{t('noSuddenSounds', 'No Sudden Sounds')}</option>\n                        <option value=\"musicTherapy\">{t('musicTherapy', 'Music Therapy')}</option>\n                      </select>\n                    </div>\n                    <div>\n                      <label className=\"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1\">\n                        {t('texture', 'Texture/Touch')}\n                      </label>\n                      <select\n                        value={formData.sensoryPreferences.texture}\n                        onChange={(e) => handleInputChange('sensoryPreferences', {\n                          ...formData.sensoryPreferences,\n                          texture: e.target.value\n                        })}\n                        className=\"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      >\n                        <option value=\"normal\">{t('normal', 'Normal')}</option>\n                        <option value=\"gentle\">{t('gentle', 'Gentle Touch')}</option>\n                        <option value=\"firm\">{t('firm', 'Firm Pressure')}</option>\n                        <option value=\"avoid\">{t('avoidTextures', 'Avoid Certain Textures')}</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('behavioralNotes', 'Behavioral Notes')}\n                  </label>\n                  <textarea\n                    value={formData.behavioralNotes}\n                    onChange={(e) => handleInputChange('behavioralNotes', e.target.value)}\n                    rows=\"4\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('behavioralNotesPlaceholder', 'Describe any behavioral patterns, triggers, or special considerations...')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('caregiverNotes', 'Caregiver Notes')}\n                  </label>\n                  <textarea\n                    value={formData.caregiverNotes}\n                    onChange={(e) => handleInputChange('caregiverNotes', e.target.value)}\n                    rows=\"3\"\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('caregiverNotesPlaceholder', 'Additional notes from caregivers or family members...')}\n                  />\n                </div>\n\n                {/* Detailed Assessment Button */}\n                <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\n                        {t('detailedAssessment', 'Detailed Special Needs Assessment')}\n                      </h4>\n                      <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-1\">\n                        {assessmentData\n                          ? t('assessmentCompleted', 'Assessment completed and saved')\n                          : t('assessmentRecommended', 'Complete a detailed assessment for better treatment planning')\n                        }\n                      </p>\n                    </div>\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowAssessment(true)}\n                      className=\"px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                      {assessmentData\n                        ? t('editAssessment', 'Edit Assessment')\n                        : t('startAssessment', 'Start Assessment')\n                      }\n                    </button>\n                  </div>\n                </div>\n\n                {/* CARF Assessment Button */}\n                <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100\">\n                        {t('carfAssessment', 'CARF-Compliant Assessment')}\n                      </h4>\n                      <p className=\"text-xs text-green-700 dark:text-green-300 mt-1\">\n                        {carfAssessmentData\n                          ? t('carfAssessmentCompleted', 'CARF assessment completed and saved')\n                          : t('carfAssessmentRecommended', 'Complete CARF-compliant assessment for accreditation compliance')\n                        }\n                      </p>\n                    </div>\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowCARFAssessment(true)}\n                      className=\"px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors\"\n                    >\n                      {carfAssessmentData\n                        ? t('editCARFAssessment', 'Edit CARF Assessment')\n                        : t('startCARFAssessment', 'Start CARF Assessment')\n                      }\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        );\n\n      default:\n        return <div>Step content for step {currentStep}</div>;\n    }\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => navigate('/patients')}\n              className=\"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            >\n              <i className=\"fas fa-arrow-left\"></i>\n            </button>\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                {isEditMode ? t('editPatient', 'Edit Patient') : t('addNewPatient', 'Add New Patient')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('step', 'Step')} {currentStep} {t('of', 'of')} {steps.length}: {steps[currentStep - 1]?.title}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto p-6\">\n        {/* Progress Steps */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            {steps.map((step, index) => (\n              <div key={step.id} className=\"flex items-center\">\n                <div\n                  className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${\n                    currentStep >= step.id\n                      ? 'border-blue-500 bg-blue-500 text-white'\n                      : 'border-gray-300 text-gray-400'\n                  }`}\n                >\n                  <i className={step.icon}></i>\n                </div>\n                {index < steps.length - 1 && (\n                  <div\n                    className={`w-full h-1 mx-4 ${\n                      currentStep > step.id ? 'bg-blue-500' : 'bg-gray-300'\n                    }`}\n                  ></div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Form Content */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {steps[currentStep - 1]?.title}\n          </h2>\n\n          {renderStepContent()}\n\n          {/* Navigation Buttons */}\n          <div className=\"flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-600\">\n            <button\n              onClick={prevStep}\n              disabled={currentStep === 1}\n              className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <i className=\"fas fa-arrow-left mr-2\"></i>\n              {t('previous', 'Previous')}\n            </button>\n\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => navigate('/patients')}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n\n              {currentStep < steps.length ? (\n                <button\n                  onClick={nextStep}\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  {t('next', 'Next')}\n                  <i className=\"fas fa-arrow-right ml-2\"></i>\n                </button>\n              ) : (\n                <button\n                  onClick={handleSubmit}\n                  disabled={loading}\n                  className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\"\n                >\n                  {loading ? (\n                    <>\n                      <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                      {isEditMode ? t('updating', 'Updating...') : t('creating', 'Creating...')}\n                    </>\n                  ) : (\n                    <>\n                      <i className=\"fas fa-save mr-2\"></i>\n                      {isEditMode ? t('updatePatient', 'Update Patient') : t('createPatient', 'Create Patient')}\n                    </>\n                  )}\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Special Needs Assessment Modal */}\n      {showAssessment && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n            <ErrorBoundary>\n              <SpecialNeedsAssessment\n                initialData={assessmentData || {}}\n                onSave={handleAssessmentSave}\n                onCancel={() => setShowAssessment(false)}\n                patientInfo={{\n                  name: `${formData.firstName} ${formData.lastName}`.trim() || t('newPatient', 'New Patient')\n                }}\n              />\n            </ErrorBoundary>\n          </div>\n        </div>\n      )}\n\n      {/* CARF Assessment Modal */}\n      {showCARFAssessment && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n            <ErrorBoundary>\n              <CARFAssessmentForm\n                patientId={null}\n                initialData={carfAssessmentData || {}}\n                onSave={handleCARFAssessmentSave}\n                onCancel={() => setShowCARFAssessment(false)}\n              />\n            </ErrorBoundary>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AddPatient;\n"], "names": ["_ref", "_safeInitialData$comm", "_safeInitialData$comm2", "_safeInitialData$comm3", "_safeInitialData$comm4", "_safeInitialData$sens", "_safeInitialData$sens2", "_safeInitialData$sens3", "_safeInitialData$sens4", "_safeInitialData$sens5", "_safeInitialData$beha", "_safeInitialData$beha2", "_safeInitialData$beha3", "_safeInitialData$beha4", "_safeInitialData$moto", "_safeInitialData$moto2", "_safeInitialData$moto3", "_safeInitialData$moto4", "_safeInitialData$adap", "_safeInitialData$adap2", "_safeInitialData$adap3", "_safeInitialData$adap4", "initialData", "onSave", "onCancel", "isEditing", "patientInfo", "t", "isRTL", "useLanguage", "safeInitialData", "assessmentData", "setAssessmentData", "useState", "assessmentDate", "Date", "toISOString", "split", "assessedBy", "caregiverPresent", "primaryDiagnosis", "secondaryDiagnoses", "cognitiveLevel", "functionalLevel", "communicationAbilities", "verbal", "nonVerbal", "comprehension", "expression", "sensoryProfile", "visual", "auditory", "tactile", "vestibular", "proprioceptive", "behaviorProfile", "socialInteraction", "attentionSpan", "emotionalRegulation", "adaptability", "motorSkills", "grossMotor", "fineMotor", "coordination", "balance", "adaptiveStrategies", "environmentalModifications", "communicationSupports", "behavioralSupports", "sensorySupports", "treatmentGoals", "recommendations", "followUpNeeded", "nextAssessmentDate", "strengths", "challenges", "familyConcerns", "additionalNotes", "cognitiveLevels", "value", "label", "functionalLevels", "communicationLevels", "sensoryLevels", "handleInputChange", "field", "prev", "_objectSpread", "handleNestedChange", "category", "_jsxs", "className", "concat", "children", "_jsx", "name", "onClick", "handleSave", "type", "onChange", "e", "target", "placeholder", "map", "level", "Object", "entries", "_ref2", "sense", "char<PERSON>t", "toUpperCase", "slice", "_ref3", "skill", "rows", "id", "checked", "htmlFor", "Error<PERSON>ou<PERSON><PERSON>", "React", "constructor", "props", "super", "this", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "console", "setState", "render", "window", "location", "reload", "history", "back", "process", "AddPatient", "_steps", "_steps2", "user", "isAuthenticated", "hasPermission", "useAuth", "navigate", "useNavigate", "useParams", "currentStep", "setCurrentStep", "loading", "setLoading", "isEditMode", "setIsEditMode", "showAssessment", "setShowAssessment", "showCARFAssessment", "setShowCARFAssessment", "carfAssessmentData", "setCARFAssessmentData", "formData", "setFormData", "firstName", "lastName", "firstNameAr", "lastNameAr", "nationalId", "dateOfBirth", "gender", "phone", "email", "address", "city", "region", "postalCode", "emergencyContactName", "emergencyContactPhone", "emergencyContactRelation", "insuranceProvider", "insuranceNumber", "insuranceExpiry", "medicalHistory", "currentMedications", "allergies", "referringPhysician", "hasSpecialNeeds", "specialNeedsType", "communicationMethod", "sensoryPreferences", "lighting", "sound", "texture", "behavioralNotes", "caregiverNotes", "useEffect", "loadPatientData", "async", "mockPatient", "patientId", "addressAr", "country", "emergencyContactNameAr", "emergencyContactRelationship", "insurancePolicyNumber", "prevData", "toast", "steps", "title", "icon", "specialNeedsTypes", "validateStep", "step", "length", "index", "renderStepContent", "required", "dir", "includes", "filter", "handleSpecialNeedsToggle", "prevStep", "Math", "max", "disabled", "nextStep", "min", "log", "permissions", "localStorage", "getItem", "patientData", "street", "zipCode", "emergencyContact", "relationship", "insurance", "provider", "policyNumber", "groupNumber", "insuranceGroupNumber", "specialNeedsAssessment", "carfAssessment", "response", "api", "put", "success", "Error", "message", "post", "request", "config", "errorMessage", "data", "status", "_Fragment", "SpecialNeedsAssessment", "trim", "CARFAssessmentForm"], "sourceRoot": ""}