{"version": 3, "file": "static/js/9432.b3458a46.chunk.js", "mappings": "4IAGO,MAAMA,EAAkB,CAE7BC,aAAc,CACZC,GAAI,KACJC,MAAO,yBACPC,YAAa,sDACbC,aAAc,CACZ,uCACA,mCACA,mCACA,sCACA,iCACA,6CAKJC,eAAgB,CACdJ,GAAI,KACJC,MAAO,2BACPC,YAAa,wDACbC,aAAc,CACZ,mCACA,2BACA,8BACA,6CACA,yBACA,kCAKJE,mBAAoB,CAClBL,GAAI,KACJC,MAAO,+BACPC,YAAa,mDACbC,aAAc,CACZ,+BACA,yCACA,0BACA,kCACA,+BACA,mCAKJG,gBAAiB,CACfN,GAAI,KACJC,MAAO,4BACPC,YAAa,gDACbC,aAAc,CACZ,8BACA,gCACA,2BACA,yBACA,gCACA,qBAKJI,uBAAwB,CACtBP,GAAI,KACJC,MAAO,mCACPC,YAAa,+CACbC,aAAc,CACZ,4BACA,4BACA,iCACA,2BACA,4BACA,gCAKJK,WAAY,CACVR,GAAI,KACJC,MAAO,sCACPC,YAAa,0CACbC,aAAc,CACZ,2BACA,4BACA,qBACA,sBACA,qBACA,0BAKJM,oBAAqB,CACnBT,GAAI,KACJC,MAAO,gCACPC,YAAa,+CACbC,aAAc,CACZ,+BACA,yBACA,uBACA,yBACA,mBACA,oB,0FCrGN,MA6PA,EA7PiCO,KAC/B,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACPC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,OAClDC,EAAgBC,IAAqBF,EAAAA,EAAAA,UAAS,CAAC,IAC/CG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAEvCK,EAAAA,EAAAA,WAAU,KAERC,WAAW,KAeTJ,EAd2B,CACzBK,aAAc,GACdC,eAAgB,aAChBC,eAAgB,aAChBC,UAAW,CACT1B,aAAc,CAAE2B,MAAO,GAAIC,OAAQ,YAAaC,WAAY,cAC5DxB,eAAgB,CAAEsB,MAAO,GAAIC,OAAQ,YAAaC,WAAY,cAC9DvB,mBAAoB,CAAEqB,MAAO,GAAIC,OAAQ,YAAaC,WAAY,cAClEtB,gBAAiB,CAAEoB,MAAO,GAAIC,OAAQ,YAAaC,WAAY,cAC/DrB,uBAAwB,CAAEmB,MAAO,GAAIC,OAAQ,YAAaC,WAAY,cACtEpB,WAAY,CAAEkB,MAAO,GAAIC,OAAQ,YAAaC,WAAY,cAC1DnB,oBAAqB,CAAEiB,MAAO,GAAIC,OAAQ,YAAaC,WAAY,iBAIvET,GAAW,IACV,MACF,IAEH,MAAMU,EAAkBF,IACtB,OAAQA,GACN,IAAK,YACH,MAAO,uEACT,IAAK,gBACH,MAAO,+DACT,IAAK,UACH,MAAO,2EACT,QACE,MAAO,kEAIPG,EAAiBJ,GACjBA,GAAS,GAAW,qCACpBA,GAAS,GAAW,uCACjB,iCAGT,OAAIR,GAEAa,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7DtB,EAAE,2BAA4B,iCAEjCoB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDtB,EAAE,4BAA6B,0FAGpCuB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yDAAwDC,SAAC,8BAM/EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0FAAyFC,UACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEAGjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDAAsDC,SAAEtB,EAAE,eAAgB,oBACvFuB,EAAAA,EAAAA,MAAA,KAAGF,UAAS,sBAAAG,OAAwBL,EAAcd,EAAeM,eAAgBW,SAAA,CAC9EjB,EAAeM,aAAa,gBAMrCS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wFAAuFC,UACpGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEAGjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDAAsDC,SAAEtB,EAAE,iBAAkB,sBACzFoB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDAAqDC,SAC/D,IAAIG,KAAKpB,EAAeO,gBAAgBc,gCAMjDN,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mEAGjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDAAsDC,SAAEtB,EAAE,iBAAkB,sBACzFoB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDAAqDC,SAC/D,IAAIG,KAAKpB,EAAeQ,gBAAgBa,gCAMjDN,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mEAGjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDAAsDC,SAAEtB,EAAE,sBAAuB,2BAC9FoB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DAA0DC,SACpEtB,EAAE,aAAc,4BAQ3BuB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEtB,EAAE,2BAA4B,mCAGnCoB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,SACnDK,OAAOC,QAAQzC,EAAAA,IAAiB0C,IAAIC,IAAsB,IAApBC,EAAKC,GAASF,EACnD,MAAMG,EAAa5B,EAAeS,UAAUiB,GAC5C,OACER,EAAAA,EAAAA,MAAA,OAEEF,UAAU,sIACVa,QAASA,IAAM/B,EAAoBD,IAAqB6B,EAAM,KAAOA,GAAKT,SAAA,EAE1EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SAAEU,EAAS1C,YAEtEiC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAAG,OAAgDN,EAAyB,OAAVe,QAAU,IAAVA,OAAU,EAAVA,EAAYjB,SAAUM,UACvF,OAAVW,QAAU,IAAVA,OAAU,EAAVA,EAAYjB,SAAU,aAEzBO,EAAAA,EAAAA,MAAA,QAAMF,UAAS,qBAAAG,OAAuBL,GAAwB,OAAVc,QAAU,IAAVA,OAAU,EAAVA,EAAYlB,QAAS,IAAKO,SAAA,EACjE,OAAVW,QAAU,IAAVA,OAAU,EAAVA,EAAYlB,QAAS,EAAE,cAK9BK,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDU,EAASzC,eAGZgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CACtDtB,EAAE,aAAc,eAAe,KAAa,OAAViC,QAAU,IAAVA,GAAAA,EAAYhB,WAAa,IAAIQ,KAAKQ,EAAWhB,YAAYS,qBAAuB,SAGpHxB,IAAqB6B,IACpBR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0DAAyDC,SAAA,EACtEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,iDAAgDC,SAAA,CAC3DtB,EAAE,eAAgB,gBAAgB,QAErCoB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,YAAWC,SACtBU,EAASxC,aAAaqC,IAAI,CAACM,EAAaC,KACvCb,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,6DAA4DC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDACZc,IAFMC,WAlCZL,aAkDjBR,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEtB,EAAE,cAAe,qBAGtBoB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sIAAqIC,SAAA,EAClJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEACbE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mDAAkDC,SAC5DtB,EAAE,iBAAkB,qCAEvBoB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CC,SACxDtB,EAAE,eAAgB,gCAIzBoB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,sFAAqFC,SACpGtB,EAAE,SAAU,gBAIjBuB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8HAA6HC,SAAA,EAC1IC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACbE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CC,SACxDtB,EAAE,gBAAiB,qCAEtBoB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDtB,EAAE,eAAgB,+BAIzBoB,EAAAA,EAAAA,KAAA,UAAQC,UAAU,kFAAiFC,SAChGtB,EAAE,QAAS,0BChP5B,EAJuBqC,KACdjB,EAAAA,EAAAA,KAACrB,EAAwB,G", "sources": ["utils/cbahiStandards.js", "components/CBAHI/CBAHIComplianceDashboard.jsx", "pages/Compliance/CBAHIDashboard.jsx"], "sourcesContent": ["// CBAHI (Central Board for Accreditation of Healthcare Institutions) Standards\n// These standards are specific to healthcare institutions in Saudi Arabia\n\nexport const CBAHI_STANDARDS = {\n  // Patient Care Standards\n  PATIENT_CARE: {\n    id: 'PC',\n    title: 'Patient Care Standards',\n    description: 'Standards for comprehensive patient care and safety',\n    requirements: [\n      'Patient assessment and care planning',\n      'Medication management and safety',\n      'Infection prevention and control',\n      'Patient rights and responsibilities',\n      'Pain assessment and management',\n      'Patient education and discharge planning'\n    ]\n  },\n\n  // Patient Safety\n  PATIENT_SAFETY: {\n    id: 'PS',\n    title: 'Patient Safety Standards',\n    description: 'Standards for ensuring patient safety throughout care',\n    requirements: [\n      'Patient identification protocols',\n      'Fall prevention programs',\n      'Medication error prevention',\n      'Healthcare-associated infection prevention',\n      'Safe surgery protocols',\n      'Emergency response procedures'\n    ]\n  },\n\n  // Quality Management\n  QUALITY_MANAGEMENT: {\n    id: 'QM',\n    title: 'Quality Management Standards',\n    description: 'Standards for quality improvement and management',\n    requirements: [\n      'Quality improvement programs',\n      'Performance measurement and monitoring',\n      'Risk management systems',\n      'Patient satisfaction monitoring',\n      'Clinical outcome measurement',\n      'Continuous quality improvement'\n    ]\n  },\n\n  // Human Resources\n  HUMAN_RESOURCES: {\n    id: 'HR',\n    title: 'Human Resources Standards',\n    description: 'Standards for healthcare workforce management',\n    requirements: [\n      'Staff competency assessment',\n      'Continuing education programs',\n      'Professional development',\n      'Performance evaluation',\n      'Credentialing and privileging',\n      'Workplace safety'\n    ]\n  },\n\n  // Information Management\n  INFORMATION_MANAGEMENT: {\n    id: 'IM',\n    title: 'Information Management Standards',\n    description: 'Standards for healthcare information systems',\n    requirements: [\n      'Medical record management',\n      'Data security and privacy',\n      'Information system reliability',\n      'Data backup and recovery',\n      'Electronic health records',\n      'Health information exchange'\n    ]\n  },\n\n  // Governance and Leadership\n  GOVERNANCE: {\n    id: 'GL',\n    title: 'Governance and Leadership Standards',\n    description: 'Standards for organizational governance',\n    requirements: [\n      'Organizational structure',\n      'Leadership accountability',\n      'Strategic planning',\n      'Resource allocation',\n      'Policy development',\n      'Compliance monitoring'\n    ]\n  },\n\n  // Facility Management\n  FACILITY_MANAGEMENT: {\n    id: 'FM',\n    title: 'Facility Management Standards',\n    description: 'Standards for healthcare facility operations',\n    requirements: [\n      'Facility safety and security',\n      'Environmental controls',\n      'Equipment management',\n      'Emergency preparedness',\n      'Waste management',\n      'Utility systems'\n    ]\n  }\n};\n\n// CBAHI Compliance Requirements for PT Assessment\nexport const CBAHI_PT_ASSESSMENT_REQUIREMENTS = {\n  PATIENT_IDENTIFICATION: {\n    standard: 'PS.1',\n    requirement: 'Patient identification must include at least two identifiers',\n    implementation: 'Patient ID and name verification in assessment form'\n  },\n\n  COMPREHENSIVE_ASSESSMENT: {\n    standard: 'PC.1',\n    requirement: 'Comprehensive patient assessment within specified timeframe',\n    implementation: '8-page comprehensive PT assessment covering all required domains'\n  },\n\n  PAIN_ASSESSMENT: {\n    standard: 'PC.3',\n    requirement: 'Standardized pain assessment and documentation',\n    implementation: 'Multiple pain scales and body map for pain location'\n  },\n\n  FUNCTIONAL_ASSESSMENT: {\n    standard: 'PC.2',\n    requirement: 'Functional status assessment and documentation',\n    implementation: 'ROM, muscle testing, coordination, and functional mobility assessment'\n  },\n\n  DOCUMENTATION_STANDARDS: {\n    standard: 'IM.1',\n    requirement: 'Complete, accurate, and timely documentation',\n    implementation: 'Structured digital form with validation and audit trail'\n  },\n\n  PATIENT_SAFETY: {\n    standard: 'PS.2',\n    requirement: 'Fall risk and safety assessment',\n    implementation: 'Fall risk assessment and safety screening included'\n  },\n\n  QUALITY_MEASURES: {\n    standard: 'QM.1',\n    requirement: 'Quality indicators and outcome measurement',\n    implementation: 'Standardized assessment tools and outcome measures'\n  },\n\n  PROFESSIONAL_STANDARDS: {\n    standard: 'HR.1',\n    requirement: 'Licensed healthcare professional assessment',\n    implementation: 'Professional signature and credential verification'\n  }\n};\n\n// CBAHI Assessment Checklist\nexport const CBAHI_ASSESSMENT_CHECKLIST = [\n  {\n    category: 'Patient Identification',\n    items: [\n      'Patient name verification',\n      'Unique patient identifier',\n      'Date of birth confirmation',\n      'Contact information validation'\n    ]\n  },\n  {\n    category: 'Assessment Completeness',\n    items: [\n      'Medical history documentation',\n      'Current medications review',\n      'Allergy documentation',\n      'Social history assessment',\n      'Functional status evaluation'\n    ]\n  },\n  {\n    category: 'Clinical Assessment',\n    items: [\n      'Physical examination findings',\n      'Neurological assessment',\n      'Musculoskeletal evaluation',\n      'Pain assessment with scales',\n      'Functional mobility testing'\n    ]\n  },\n  {\n    category: 'Safety Assessment',\n    items: [\n      'Fall risk evaluation',\n      'Cognitive assessment',\n      'Environmental safety review',\n      'Equipment safety check',\n      'Emergency contact information'\n    ]\n  },\n  {\n    category: 'Documentation Quality',\n    items: [\n      'Complete form submission',\n      'Professional signature',\n      'Date and time stamps',\n      'Legible documentation',\n      'Error correction procedures'\n    ]\n  },\n  {\n    category: 'Patient Rights',\n    items: [\n      'Informed consent process',\n      'Privacy protection',\n      'Cultural considerations',\n      'Language preferences',\n      'Patient preferences documentation'\n    ]\n  }\n];\n\n// CBAHI Compliance Validation\nexport const validateCBAHICompliance = (assessmentData) => {\n  const compliance = {\n    score: 0,\n    maxScore: 0,\n    issues: [],\n    recommendations: []\n  };\n\n  // Check patient identification\n  if (assessmentData.patientInfo?.name && assessmentData.patientInfo?.patientId) {\n    compliance.score += 10;\n  } else {\n    compliance.issues.push('Missing patient identification information');\n  }\n  compliance.maxScore += 10;\n\n  // Check assessment completeness\n  if (assessmentData.patientInfo?.diagnosis && assessmentData.patientInfo?.chiefComplaint) {\n    compliance.score += 15;\n  } else {\n    compliance.issues.push('Incomplete medical history documentation');\n  }\n  compliance.maxScore += 15;\n\n  // Check pain assessment\n  if (assessmentData.musculoskeletalExam?.painAssessment?.present !== undefined) {\n    compliance.score += 10;\n  } else {\n    compliance.issues.push('Pain assessment not completed');\n  }\n  compliance.maxScore += 10;\n\n  // Check safety assessment\n  if (assessmentData.assessmentReview?.fallRisk) {\n    compliance.score += 10;\n  } else {\n    compliance.issues.push('Fall risk assessment not completed');\n  }\n  compliance.maxScore += 10;\n\n  // Check professional documentation\n  if (assessmentData.signatures?.therapistSignature) {\n    compliance.score += 5;\n  } else {\n    compliance.issues.push('Professional signature required');\n  }\n  compliance.maxScore += 5;\n\n  // Calculate compliance percentage\n  compliance.percentage = Math.round((compliance.score / compliance.maxScore) * 100);\n\n  // Add recommendations based on score\n  if (compliance.percentage < 80) {\n    compliance.recommendations.push('Complete all required assessment sections');\n    compliance.recommendations.push('Ensure proper documentation of patient safety measures');\n  }\n\n  if (compliance.percentage < 90) {\n    compliance.recommendations.push('Review CBAHI standards for comprehensive assessment');\n  }\n\n  return compliance;\n};\n\nexport default CBAHI_STANDARDS;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { CBAHI_STANDARDS, validateCBAHICompliance } from '../../utils/cbahiStandards';\n\nconst CBAHIComplianceDashboard = () => {\n  const { t } = useLanguage();\n  const [selectedStandard, setSelectedStandard] = useState(null);\n  const [complianceData, setComplianceData] = useState({});\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Mock compliance data loading\n    setTimeout(() => {\n      const mockComplianceData = {\n        overallScore: 92,\n        lastAssessment: '2024-02-10',\n        nextAssessment: '2024-08-10',\n        standards: {\n          PATIENT_CARE: { score: 95, status: 'compliant', lastReview: '2024-02-01' },\n          PATIENT_SAFETY: { score: 88, status: 'compliant', lastReview: '2024-02-05' },\n          QUALITY_MANAGEMENT: { score: 90, status: 'compliant', lastReview: '2024-01-28' },\n          HUMAN_RESOURCES: { score: 94, status: 'compliant', lastReview: '2024-02-03' },\n          INFORMATION_MANAGEMENT: { score: 96, status: 'compliant', lastReview: '2024-02-08' },\n          GOVERNANCE: { score: 89, status: 'compliant', lastReview: '2024-01-30' },\n          FACILITY_MANAGEMENT: { score: 91, status: 'compliant', lastReview: '2024-02-02' }\n        }\n      };\n      setComplianceData(mockComplianceData);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'compliant':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200';\n      case 'non-compliant':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200';\n      case 'pending':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';\n    }\n  };\n\n  const getScoreColor = (score) => {\n    if (score >= 90) return 'text-green-600 dark:text-green-400';\n    if (score >= 80) return 'text-yellow-600 dark:text-yellow-400';\n    return 'text-red-600 dark:text-red-400';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {t('cbahiComplianceDashboard', 'CBAHI Compliance Dashboard')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n              {t('cbahiDashboardDescription', 'Central Board for Accreditation of Healthcare Institutions compliance monitoring')}\n            </p>\n          </div>\n          <div className=\"flex items-center space-x-2 px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full\">\n            <i className=\"fas fa-shield-alt text-green-600 dark:text-green-400\"></i>\n            <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">CBAHI Accredited</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Overall Compliance Score */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center\">\n                <i className=\"fas fa-chart-line text-green-600 dark:text-green-400 text-xl\"></i>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('overallScore', 'Overall Score')}</p>\n              <p className={`text-2xl font-bold ${getScoreColor(complianceData.overallScore)}`}>\n                {complianceData.overallScore}%\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\n                <i className=\"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-xl\"></i>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('lastAssessment', 'Last Assessment')}</p>\n              <p className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {new Date(complianceData.lastAssessment).toLocaleDateString()}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center\">\n                <i className=\"fas fa-clock text-orange-600 dark:text-orange-400 text-xl\"></i>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('nextAssessment', 'Next Assessment')}</p>\n              <p className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {new Date(complianceData.nextAssessment).toLocaleDateString()}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"flex-shrink-0\">\n              <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center\">\n                <i className=\"fas fa-award text-purple-600 dark:text-purple-400 text-xl\"></i>\n              </div>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('accreditationStatus', 'Accreditation Status')}</p>\n              <p className=\"text-lg font-semibold text-green-600 dark:text-green-400\">\n                {t('accredited', 'Accredited')}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Standards Compliance */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('cbahiStandardsCompliance', 'CBAHI Standards Compliance')}\n          </h2>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {Object.entries(CBAHI_STANDARDS).map(([key, standard]) => {\n              const compliance = complianceData.standards[key];\n              return (\n                <div\n                  key={key}\n                  className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\"\n                  onClick={() => setSelectedStandard(selectedStandard === key ? null : key)}\n                >\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-3\">\n                      <i className=\"fas fa-shield-alt text-green-600 dark:text-green-400\"></i>\n                      <h3 className=\"font-medium text-gray-900 dark:text-white\">{standard.title}</h3>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(compliance?.status)}`}>\n                        {compliance?.status || 'pending'}\n                      </span>\n                      <span className={`text-lg font-bold ${getScoreColor(compliance?.score || 0)}`}>\n                        {compliance?.score || 0}%\n                      </span>\n                    </div>\n                  </div>\n                  \n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                    {standard.description}\n                  </p>\n                  \n                  <div className=\"text-xs text-gray-500 dark:text-gray-500\">\n                    {t('lastReview', 'Last Review')}: {compliance?.lastReview ? new Date(compliance.lastReview).toLocaleDateString() : 'N/A'}\n                  </div>\n\n                  {selectedStandard === key && (\n                    <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600\">\n                      <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                        {t('requirements', 'Requirements')}:\n                      </h4>\n                      <ul className=\"space-y-1\">\n                        {standard.requirements.map((requirement, index) => (\n                          <li key={index} className=\"text-sm text-gray-600 dark:text-gray-400 flex items-center\">\n                            <i className=\"fas fa-check-circle text-green-500 mr-2 text-xs\"></i>\n                            {requirement}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n\n      {/* Action Items */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('actionItems', 'Action Items')}\n          </h2>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n              <div className=\"flex items-center space-x-3\">\n                <i className=\"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400\"></i>\n                <div>\n                  <p className=\"font-medium text-yellow-800 dark:text-yellow-200\">\n                    {t('updatePolicies', 'Update Patient Safety Policies')}\n                  </p>\n                  <p className=\"text-sm text-yellow-600 dark:text-yellow-400\">\n                    {t('dueDateMarch', 'Due: March 15, 2024')}\n                  </p>\n                </div>\n              </div>\n              <button className=\"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors\">\n                {t('review', 'Review')}\n              </button>\n            </div>\n\n            <div className=\"flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n              <div className=\"flex items-center space-x-3\">\n                <i className=\"fas fa-info-circle text-blue-600 dark:text-blue-400\"></i>\n                <div>\n                  <p className=\"font-medium text-blue-800 dark:text-blue-200\">\n                    {t('staffTraining', 'Complete Staff Training Module')}\n                  </p>\n                  <p className=\"text-sm text-blue-600 dark:text-blue-400\">\n                    {t('dueDateApril', 'Due: April 1, 2024')}\n                  </p>\n                </div>\n              </div>\n              <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                {t('start', 'Start')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CBAHIComplianceDashboard;\n", "import React from 'react';\nimport CBAHIComplianceDashboard from '../../components/CBAHI/CBAHIComplianceDashboard';\n\nconst CBAHIDashboard = () => {\n  return <CBAHIComplianceDashboard />;\n};\n\nexport default CBAHIDashboard;\n"], "names": ["CBAHI_STANDARDS", "PATIENT_CARE", "id", "title", "description", "requirements", "PATIENT_SAFETY", "QUALITY_MANAGEMENT", "HUMAN_RESOURCES", "INFORMATION_MANAGEMENT", "GOVERNANCE", "FACILITY_MANAGEMENT", "CBAHIComplianceDashboard", "t", "useLanguage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedStandard", "useState", "complianceData", "setComplianceData", "loading", "setLoading", "useEffect", "setTimeout", "overallScore", "lastAssessment", "nextAssessment", "standards", "score", "status", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getStatusColor", "getScoreColor", "_jsx", "className", "children", "_jsxs", "concat", "Date", "toLocaleDateString", "Object", "entries", "map", "_ref", "key", "standard", "compliance", "onClick", "requirement", "index", "CBAHIDashboard"], "sourceRoot": ""}