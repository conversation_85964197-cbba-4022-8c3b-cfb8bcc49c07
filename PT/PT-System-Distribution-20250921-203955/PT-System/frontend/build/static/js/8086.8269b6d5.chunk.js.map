{"version": 3, "file": "static/js/8086.8269b6d5.chunk.js", "mappings": "0OAOA,MAm2BA,EAn2B0BA,KACxB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,iBAAEC,EAAgB,iBAAEC,EAAkBC,YAAaC,EAAiBC,QAASC,IAAiBC,EAAAA,EAAAA,OAC9F,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAEvCC,YAAa,GACbP,UAAWA,GAAa,GACxBQ,UAAU,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC9CC,eAAmB,OAAJrB,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,OAAQ,GAG7BC,iBAAkB,GAClBC,mBAAoB,GACpBC,SAAU,GACVC,sBAAuB,GACvBC,YAAa,GACbC,kBAAmB,GAGnBC,MAAO,CACL,CACEC,GAAI,EACJC,KAAM,aACNC,YAAa,GACbC,SAAU,GACVC,WAAY,GACZC,WAAY,GACZC,SAAU,GACVC,UAAW,GACXC,WAAY,GACZC,OAAQ,WAKZC,cAAe,GACfC,UAAW,GACXC,SAAU,GACVC,cAAe,GAGfC,aAAc,GACdC,iBAAkB,GAClBC,eAAgB,GAGhBC,kBAAmB,GACnBC,gBAAiB,GACjBC,qBAAqB,EACrBC,iBAAkB,GAGlBC,gBAAiB,SACjBC,mBAAoB,GACpBC,qBAAsB,GACtBC,eAAgB,GAGhBC,mBAAoB,GACpBC,gBAAgB,EAChBC,cAAc,KAGTnD,EAASoD,IAAc3C,EAAAA,EAAAA,WAAS,IAChC4C,EAAQC,IAAa7C,EAAAA,EAAAA,UAAS,CAAC,GAEhC8C,EAAY,CAChB,CAAEC,MAAO,aAAcC,MAAOlE,EAAE,YAAa,2BAC7C,CAAEiE,MAAO,YAAaC,MAAOlE,EAAE,WAAY,2BAC3C,CAAEiE,MAAO,aAAcC,MAAOlE,EAAE,aAAc,oBAC9C,CAAEiE,MAAO,aAAcC,MAAOlE,EAAE,aAAc,4BAkBhDmE,EAAAA,EAAAA,WAAU,KACJvD,GACFwD,KAED,CAACxD,IAEJ,MAAMwD,EAAkBC,UACtB,IACER,GAAW,GACX,MAAMS,QAAiBC,MAAM,iBAADC,OAAkB5D,IAC9C,GAAI0D,EAASG,GAAI,CACf,MAAMC,QAAoBJ,EAASK,OACnC1D,EAAY2D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPzD,YAAauD,EAAYjD,MAAQ,GACjCb,UAAW8D,EAAYI,KAAOlE,IAElC,CACF,CAAE,MAAOmE,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACClB,GAAW,EACb,GAGIoB,EAAoBA,CAACC,EAAOjB,KAChChD,EAAY2D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACM,GAAQjB,KAGPH,EAAOoB,IACTnB,EAAUa,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACM,GAAQ,SAmDTC,EAAmBA,CAACC,EAAOF,EAAOjB,KACtChD,EAAY2D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP5C,MAAO4C,EAAK5C,MAAMqD,IAAI,CAACC,EAAMC,IAC3BA,IAAMH,GAAKP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQS,GAAI,IAAE,CAACJ,GAAQjB,IAAUqB,MAIhD,MAAME,EAAQ,QAAAhB,OAAWY,EAAK,KAAAZ,OAAIU,GAC9BpB,EAAO0B,IACTzB,EAAUa,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACY,GAAW,SA0GlB,OAAI/E,GAEAgF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wFAAuFC,UACpGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+GAA8GC,SACzH3F,EAAE,qBAAsB,6BAE3B4F,EAAAA,EAAAA,MAAA,KAAGF,UAAU,kEAAiEC,SAAA,EAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6CACZ1F,EAAE,2BAA4B,yDAEjC4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCACZ1F,EAAE,gBAAiB,qBAEtB4F,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACZ1F,EAAE,aAAc,mBAEnB4F,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CACZ1F,EAAE,gBAAiB,2BAI1B4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uCAAsCC,SAAA,EACnDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2FAA0FC,SAAA,EACvGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACZ1F,EAAE,gBAAiB,sBAEtB4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qGAAoGC,SAAA,EACjHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ1F,EAAE,QAAS,yBASxB4F,EAAAA,EAAAA,MAAA,QAAMC,SApGWxB,UAGnB,GAFAyB,EAAEC,iBAxDiBC,MACnB,MAAMC,EAAY,CAAC,EAQnB,OANKjF,EAASG,YAAY+E,SAAQD,EAAU9E,YAAcnB,EAAE,sBAAuB,6BAC9EgB,EAASU,iBAAiBwE,SAAQD,EAAUvE,iBAAmB1B,EAAE,2BAA4B,kCAC7FgB,EAASQ,cAAc0E,SAAQD,EAAUzE,cAAgBxB,EAAE,wBAAyB,+BAC3D,IAA1BgB,EAASgB,MAAMmE,SAAcF,EAAUjE,MAAQhC,EAAE,gBAAiB,kCAEtE+D,EAAUkC,GAC+B,IAAlCG,OAAOC,KAAKJ,GAAWE,QAiDzBH,GAIL,IACEnC,GAAW,GAEX,MAAMyC,EAAiB,CACrBC,SAAU,iBACVC,SAAU,yBACV5F,UAAWA,EACXO,YAAaH,EAASG,YACtBsF,KAAMzF,EACN0F,aAAiB,OAAJvG,QAAI,IAAJA,OAAI,EAAJA,EAAM8B,KAAM,YACzB0E,iBAAqB,OAAJxG,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,OAAQ,iBAC/BmF,aAAa,IAAIvF,MAAOC,cACxBoB,OAAQ,YACRmE,SAAU,CACRC,QAAS,MACTC,YAAa,UACbC,WAAY,CAAC,QAAS,OAAQ,iBAIb3G,EAAiBiG,GAEtCW,EAAAA,GAAMC,QAAQlH,EAAE,qBAAsB,uCAIpCc,EADEF,EACO,aAAD4D,OAAc5D,GAEb,qBAGb,CAAE,MAAOmE,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CkC,EAAAA,GAAMlC,MAAM/E,EAAE,cAAe,kDAC/B,CAAC,QACC6D,GAAW,EACb,GAyDgC6B,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oKAAmKC,SAAA,EAChLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kFAAiFC,SAAA,EAC7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wDACZ1F,EAAE,qBAAsB,2BAG3ByF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sGAAqGC,UAClHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6CAA4CC,SAAA,EACvDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZ1F,EAAE,yBAA0B,mFAIjC4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6FAA4FC,SAAA,EACzGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,sFAAqFC,SAAA,EACpGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACZ1F,EAAE,cAAe,gBAAgB,SAEpCyF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAASG,YAChBgG,SAAWrB,GAAMb,EAAkB,cAAea,EAAEsB,OAAOnD,OAC3DyB,UAAS,sIAAAlB,OACPV,EAAO3C,YAAc,iBAAmB,0CAE1CkG,YAAarH,EAAE,mBAAoB,yBACnCsH,UAAQ,IAETxD,EAAO3C,cACNyE,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ5B,EAAO3C,mBAKdyE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZ1F,EAAE,WAAY,aAAa,SAE9ByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAASI,SAChB+F,SAAWrB,GAAMb,EAAkB,WAAYa,EAAEsB,OAAOnD,OACxDyB,UAAU,sKACV4B,UAAQ,QAIZ1B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZ1F,EAAE,gBAAiB,sBAEtByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAASQ,cAChB2F,SAAWrB,GAAMb,EAAkB,gBAAiBa,EAAEsB,OAAOnD,OAC7DyB,UAAU,kLACV2B,YAAarH,EAAE,qBAAsB,uCAO7C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oKAAmKC,SAAA,EAChLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oFAAmFC,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZ1F,EAAE,sBAAuB,8BAG5ByF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0GAAyGC,UACtHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,+CAA8CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qEACZ1F,EAAE,uBAAwB,6GAI/B4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kFAAiFC,SAAA,EAChGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZ1F,EAAE,mBAAoB,qBAAqB,SAE9CyF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAASU,iBAChByF,SAAWrB,GAAMb,EAAkB,mBAAoBa,EAAEsB,OAAOnD,OAChEyB,UAAS,8HAAAlB,OACPV,EAAOpC,iBAAmB,iBAAmB,sCAE/C2F,YAAarH,EAAE,wBAAyB,8BACxCsH,UAAQ,IAETxD,EAAOpC,mBACNkE,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ5B,EAAOpC,wBAKdkE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZ1F,EAAE,qBAAsB,2BAE3ByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAASW,mBAChBwF,SAAWrB,GAAMb,EAAkB,qBAAsBa,EAAEsB,OAAOnD,OAClEyB,UAAU,sKACV2B,YAAarH,EAAE,0BAA2B,sCAI9C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZ1F,EAAE,WAAY,iBAEjByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAASY,SAChBuF,SAAWrB,GAAMb,EAAkB,WAAYa,EAAEsB,OAAOnD,OACxDyB,UAAU,kLACV2B,YAAarH,EAAE,gBAAiB,+BAKtC4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0EACZ1F,EAAE,wBAAyB,8BAE9ByF,EAAAA,EAAAA,KAAA,YACExB,MAAOjD,EAASa,sBAChBsF,SAAWrB,GAAMb,EAAkB,wBAAyBa,EAAEsB,OAAOnD,OACrEyB,UAAU,kLACV6B,KAAK,IACLF,YAAarH,EAAE,gCAAiC,4CAIpD4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kFAAiFC,SAAA,EAChGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZ1F,EAAE,cAAe,mBAEpByF,EAAAA,EAAAA,KAAA,YACExB,MAAOjD,EAASc,YAChBqF,SAAWrB,GAAMb,EAAkB,cAAea,EAAEsB,OAAOnD,OAC3DyB,UAAU,gKACV6B,KAAK,IACLF,YAAarH,EAAE,kBAAmB,6BAItC4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qDACZ1F,EAAE,oBAAqB,yBAE1ByF,EAAAA,EAAAA,KAAA,YACExB,MAAOjD,EAASe,kBAChBoF,SAAWrB,GAAMb,EAAkB,oBAAqBa,EAAEsB,OAAOnD,OACjEyB,UAAU,sKACV6B,KAAK,IACLF,YAAarH,EAAE,wBAAyB,4CAQlD4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sKAAqKC,SAAA,EAClLC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,iFAAgFC,SAAA,EAC5FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZ1F,EAAE,aAAc,mBAGnB4F,EAAAA,EAAAA,MAAA,UACE1D,KAAK,SACLsF,QA1YIC,KACd,MAAMC,EAAU,CACdzF,GAAIZ,KAAKsG,MACTzF,KAAM,aACNC,YAAa,GACbC,SAAU,GACVC,WAAY,GACZC,WAAY,GACZC,SAAU,GACVC,UAAW,GACXC,WAAY,GACZC,OAAQ,UAGVzB,EAAY2D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP5C,MAAO,IAAI4C,EAAK5C,MAAO0F,OA2XfhC,UAAU,mLAAkLC,SAAA,EAE5LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ1F,EAAE,UAAW,mBAIlByF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8GAA6GC,UAC1HC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,iDAAgDC,SAAA,EAC3DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8DACZ1F,EAAE,wBAAyB,uHAIhCyF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB3E,EAASgB,MAAMqD,IAAI,CAACC,EAAMF,KACzBQ,EAAAA,EAAAA,MAAA,OAAmBF,UAAU,mGAAkGC,SAAA,EAC7HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,+EAA8EC,SAAA,EAC1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACZ1F,EAAE,OAAQ,QAAQ,IAAEoF,EAAQ,MAG/BQ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACExB,MAAOqB,EAAKpD,KACZiF,SAAWrB,GAAMX,EAAiBC,EAAO,OAAQU,EAAEsB,OAAOnD,OAC1DyB,UAAU,qJAAoJC,SAE7J3B,EAAUqB,IAAKnD,IACduD,EAAAA,EAAAA,KAAA,UAAyBxB,MAAO/B,EAAK+B,MAAM0B,SACxCzD,EAAKgC,OADKhC,EAAK+B,UAMrBjD,EAASgB,MAAMmE,OAAS,IACvBP,EAAAA,EAAAA,MAAA,UACE1D,KAAK,SACLsF,QAASA,IA9ZXpC,KACdpE,EAASgB,MAAMmE,OAAS,GAC1BlF,EAAY2D,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP5C,MAAO4C,EAAK5C,MAAM4F,OAAO,CAACC,EAAGtC,IAAMA,IAAMH,OA0ZV0C,CAAW1C,GAC1BM,UAAU,iLAAgLC,SAAA,EAE1LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZ1F,EAAE,SAAU,oBAOrB4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACZ1F,EAAE,kBAAmB,oBAAoB,SAE5CyF,EAAAA,EAAAA,KAAA,YACExB,MAAOqB,EAAKnD,YACZgF,SAAWrB,GAAMX,EAAiBC,EAAO,cAAeU,EAAEsB,OAAOnD,OACjEyB,UAAS,kIAAAlB,OACPV,EAAO,QAADU,OAASY,EAAK,iBAAkB,iBAAmB,wCAE3DmC,KAAK,IACLF,YAAarH,EAAE,6BAA8B,mBAC7CsH,UAAQ,IAETxD,EAAO,QAADU,OAASY,EAAK,mBACnBQ,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ5B,EAAO,QAADU,OAASY,EAAK,wBAM3BQ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZ1F,EAAE,WAAY,gBAEjByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOqB,EAAKlD,SACZ+E,SAAWrB,GAAMX,EAAiBC,EAAO,WAAYU,EAAEsB,OAAOnD,OAC9DyB,UAAU,kLACV2B,YAAarH,EAAE,sBAAuB,wCAK1C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,sFAAqFC,SAAA,EACpGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZ1F,EAAE,aAAc,kBAEnByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOqB,EAAKjD,WACZ8E,SAAWrB,GAAMX,EAAiBC,EAAO,aAAcU,EAAEsB,OAAOnD,OAChEyB,UAAU,4KACV2B,YAAarH,EAAE,wBAAyB,wCAK5C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEACZ1F,EAAE,aAAc,kBAEnByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOqB,EAAKhD,WACZ6E,SAAWrB,GAAMX,EAAiBC,EAAO,aAAcU,EAAEsB,OAAOnD,OAChEyB,UAAU,kLACV2B,YAAarH,EAAE,wBAAyB,iCAK5C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZ1F,EAAE,WAAY,gBAEjByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOqB,EAAK/C,SACZ4E,SAAWrB,GAAMX,EAAiBC,EAAO,WAAYU,EAAEsB,OAAOnD,OAC9DyB,UAAU,kLACV2B,YAAarH,EAAE,sBAAuB,wCAM5C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wFAAuFC,SAAA,EACpGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kFAAiFC,SAAA,EAChGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qDACZ1F,EAAE,YAAa,kBAElByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOqB,EAAK9C,UACZ2E,SAAWrB,GAAMX,EAAiBC,EAAO,YAAaU,EAAEsB,OAAOnD,OAC/DyB,UAAU,gKACV2B,YAAarH,EAAE,uBAAwB,qCAI3C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8DACZ1F,EAAE,aAAc,mBAEnByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOqB,EAAK7C,WACZ0E,SAAWrB,GAAMX,EAAiBC,EAAO,aAAcU,EAAEsB,OAAOnD,OAChEyB,UAAU,gLAjJRJ,EAAKrD,WA2JrB2D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sKAAqKC,SAAA,EAClLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oFAAmFC,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DACZ1F,EAAE,yBAA0B,+BAG/ByF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0GAAyGC,UACtHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,+CAA8CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0EACZ1F,EAAE,2BAA4B,iGAInC4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZ1F,EAAE,sBAAuB,4BAG5ByF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDC,SAlpBhD,CAC1B,iBAAkB,uBAAwB,6BAC1C,gBAAiB,mBAAoB,oBACrC,kBAAmB,kBAAmB,oBACtC,sBAAuB,aAAc,mBA+oBJN,IAAK0C,IACxBnC,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,0HAAyHC,SAAA,EACzJF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,WACLD,GAAE,gBAAAuC,OAAkBuD,GACpBC,QAAShH,EAAS2B,cAAcsF,SAASF,GACzCZ,SAAWrB,IAAMoC,OAnmBPhD,EAmmB4B,gBAnmBrBjB,EAmmBsC8D,EAnmB/BC,EAmmB6ClC,EAAEsB,OAAOY,aAlmBhG/G,EAAY2D,IACV,MAAMuD,EAAevD,EAAKM,IAAU,GACpC,OAAI8C,GACFnD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKD,GAAI,IACP,CAACM,GAAQ,IAAIiD,EAAclE,MAG7BY,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKD,GAAI,IACP,CAACM,GAAQiD,EAAaP,OAAOQ,GAAQA,IAASnE,OAXzBiE,IAAChD,EAAOjB,EAAO+D,GAomBxBtC,UAAU,kFAEZD,EAAAA,EAAAA,KAAA,SAAO4C,QAAO,gBAAA7D,OAAkBuD,GAAgBrC,UAAU,2DAA0DC,SACjHoC,MATKA,UAiBhBnC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZ1F,EAAE,YAAa,aAAa,SAE/ByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAAS4B,UAChBuE,SAAWrB,GAAMb,EAAkB,YAAaa,EAAEsB,OAAOnD,OACzDyB,UAAS,kIAAAlB,OACPV,EAAOlB,UAAY,iBAAmB,wCAExCyE,YAAarH,EAAE,uBAAwB,qBACvCsH,UAAQ,IAETxD,EAAOlB,YACNgD,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ5B,EAAOlB,iBAKdgD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6FAA4FC,SAAA,EACzGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,sFAAqFC,SAAA,EACpGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEACZ1F,EAAE,WAAY,YAAY,SAE7ByF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,OACL+B,MAAOjD,EAAS6B,SAChBsE,SAAWrB,GAAMb,EAAkB,WAAYa,EAAEsB,OAAOnD,OACxDyB,UAAS,sIAAAlB,OACPV,EAAOjB,SAAW,iBAAmB,0CAEvCwE,YAAarH,EAAE,sBAAuB,oBACtCsH,UAAQ,IAETxD,EAAOjB,WACN+C,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ5B,EAAOjB,gBAKd+C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZ1F,EAAE,gBAAiB,kBAAkB,SAExCyF,EAAAA,EAAAA,KAAA,SACEvD,KAAK,SACL+B,MAAOjD,EAAS8B,cAChBqE,SAAWrB,GAAMb,EAAkB,gBAAiBa,EAAEsB,OAAOnD,OAC7DyB,UAAS,0IAAAlB,OACPV,EAAOhB,cAAgB,iBAAmB,4CAE5CuE,YAAarH,EAAE,2BAA4B,YAC3CsI,IAAI,IACJhB,UAAQ,IAETxD,EAAOhB,gBACN8C,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ5B,EAAOhB,8BASpB2C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sJAAqJC,UAClKC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,UACE1D,KAAK,SACLsF,QAASA,IAAM1G,GAAU,GACzB4E,UAAU,6KAA4KC,SAAA,EAEtLF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BACZ1F,EAAE,SAAU,cAGf4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACE1D,KAAK,SACLsF,QA7nBMnD,UAClB,IACER,GAAW,GAEX,MAAM0E,GAAO1D,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACR7D,GAAQ,IACXwH,aAAa,IAAInH,MAAOC,cACxBmH,YAAatI,EAAKsB,MAAQtB,EAAKuI,MAC/B9H,UAAWA,IAGP0D,QAAiBC,MAAM,8BAA+B,CAC1DoE,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADpE,OAAYqE,aAAaC,QAAQ,WAElDC,KAAMC,KAAKC,UAAUV,KAGvB,IAAIjE,EAASG,GAaX,MAAM,IAAIyE,MAAM,uBAAD1E,OAAwBF,EAAS5B,SAbjC,CACf,MAAMyG,QAAa7E,EAAS6E,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,kBAAApF,OAAqBxD,EAASG,YAAY0I,QAAQ,OAAQ,KAAI,KAAArF,OAAIxD,EAASI,SAAQ,QAC7FqI,SAASV,KAAKe,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAASV,KAAKkB,YAAYT,GAE1BU,MAAMlK,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAO+E,GACPC,QAAQD,MAAM,wBAAyBA,GACvCmF,MAAMlK,EAAE,qBAAsB,2CAChC,CAAC,QACC6D,GAAW,EACb,GAslBYsG,SAAU1J,EACViF,UAAU,yNAAwNC,SAAA,EAElOF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZjF,EAAUT,EAAE,aAAc,iBAAmBA,EAAE,cAAe,oBAGjE4F,EAAAA,EAAAA,MAAA,UACE1D,KAAK,SACLiI,SAAU1J,EACViF,UAAU,+NAA8NC,SAAA,EAExOF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZjF,EAAUT,EAAE,SAAU,aAAeA,EAAE,oBAAqB,wC", "sources": ["pages/Forms/TreatmentPlanForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useForms } from '../../contexts/FormsContext';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport toast from 'react-hot-toast';\n\nconst TreatmentPlanForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { createSubmission, updateSubmission, generatePDF: generateFormPDF, loading: formsLoading } = useForms();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [formData, setFormData] = useState({\n    // Patient Information\n    patientName: '',\n    patientId: patientId || '',\n    planDate: new Date().toISOString().split('T')[0],\n    therapistName: user?.name || '',\n    \n    // Diagnosis & Assessment\n    primaryDiagnosis: '',\n    secondaryDiagnosis: '',\n    icdCodes: '',\n    functionalLimitations: '',\n    precautions: '',\n    contraindications: '',\n    \n    // SMART Goals\n    goals: [\n      {\n        id: 1,\n        type: 'short-term',\n        description: '',\n        specific: '',\n        measurable: '',\n        achievable: '',\n        relevant: '',\n        timebound: '',\n        targetDate: '',\n        status: 'active'\n      }\n    ],\n    \n    // Treatment Interventions\n    interventions: [],\n    frequency: '',\n    duration: '',\n    totalSessions: '',\n    \n    // Outcome Measures\n    outcomeTools: [],\n    baselineMeasures: '',\n    targetOutcomes: '',\n    \n    // Plan Details\n    treatmentApproach: '',\n    progressionPlan: '',\n    homeExerciseProgram: false,\n    patientEducation: '',\n    \n    // Review & Monitoring\n    reviewFrequency: 'weekly',\n    progressIndicators: '',\n    modificationCriteria: '',\n    dischargeGoals: '',\n    \n    // Signatures\n    therapistSignature: '',\n    patientConsent: false,\n    planApproved: false\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  const goalTypes = [\n    { value: 'short-term', label: t('shortTerm', 'Short-term (1-4 weeks)') },\n    { value: 'long-term', label: t('longTerm', 'Long-term (1-3 months)') },\n    { value: 'functional', label: t('functional', 'Functional Goal') },\n    { value: 'impairment', label: t('impairment', 'Impairment-based Goal') }\n  ];\n\n  const interventionOptions = [\n    'Manual Therapy', 'Therapeutic Exercise', 'Neuromuscular Re-education',\n    'Gait Training', 'Balance Training', 'Strength Training',\n    'Range of Motion', 'Pain Management', 'Patient Education',\n    'Functional Training', 'Modalities', 'Aquatic Therapy'\n  ];\n\n  const outcomeToolOptions = [\n    'Visual Analog Scale (VAS)', 'Numeric Pain Rating Scale',\n    'Oswestry Disability Index', 'Neck Disability Index',\n    'DASH Questionnaire', 'Berg Balance Scale',\n    'Timed Up and Go', 'Six Minute Walk Test',\n    'Manual Muscle Testing', 'Range of Motion Measurements'\n  ];\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        setFormData(prev => ({\n          ...prev,\n          patientName: patientData.name || '',\n          patientId: patientData._id || patientId\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleCheckboxChange = (field, value, checked) => {\n    setFormData(prev => {\n      const currentArray = prev[field] || [];\n      if (checked) {\n        return {\n          ...prev,\n          [field]: [...currentArray, value]\n        };\n      } else {\n        return {\n          ...prev,\n          [field]: currentArray.filter(item => item !== value)\n        };\n      }\n    });\n  };\n\n  const addGoal = () => {\n    const newGoal = {\n      id: Date.now(),\n      type: 'short-term',\n      description: '',\n      specific: '',\n      measurable: '',\n      achievable: '',\n      relevant: '',\n      timebound: '',\n      targetDate: '',\n      status: 'active'\n    };\n\n    setFormData(prev => ({\n      ...prev,\n      goals: [...prev.goals, newGoal]\n    }));\n  };\n\n  const removeGoal = (index) => {\n    if (formData.goals.length > 1) {\n      setFormData(prev => ({\n        ...prev,\n        goals: prev.goals.filter((_, i) => i !== index)\n      }));\n    }\n  };\n\n  const handleGoalChange = (index, field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      goals: prev.goals.map((goal, i) =>\n        i === index ? { ...goal, [field]: value } : goal\n      )\n    }));\n\n    const errorKey = `goal_${index}_${field}`;\n    if (errors[errorKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [errorKey]: null\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.patientName.trim()) newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    if (!formData.primaryDiagnosis.trim()) newErrors.primaryDiagnosis = t('primaryDiagnosisRequired', 'Primary diagnosis is required');\n    if (!formData.therapistName.trim()) newErrors.therapistName = t('therapistNameRequired', 'Therapist name is required');\n    if (formData.goals.length === 0) newErrors.goals = t('goalsRequired', 'At least one goal is required');\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n      \n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/treatment-plans/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `treatment-plan-${formData.patientName.replace(/\\s+/g, '-')}-${formData.planDate}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n        \n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      const submissionData = {\n        formType: 'treatment-plan',\n        formName: 'Treatment Plan & Goals',\n        patientId: patientId,\n        patientName: formData.patientName,\n        data: formData,\n        submittedBy: user?.id || 'anonymous',\n        submittedByName: user?.name || 'Anonymous User',\n        submittedAt: new Date().toISOString(),\n        status: 'completed',\n        metadata: {\n          version: '1.0',\n          formVersion: 'v2024.1',\n          compliance: ['HIPAA', 'CARF', 'CBAHI']\n        }\n      };\n\n      const result = await createSubmission(submissionData);\n\n      toast.success(t('treatmentPlanSaved', 'Treatment plan saved successfully!'));\n\n      // Navigate back to patient page or forms list\n      if (patientId) {\n        navigate(`/patients/${patientId}`);\n      } else {\n        navigate('/forms/submissions');\n      }\n\n    } catch (error) {\n      console.error('Error saving treatment plan:', error);\n      toast.error(t('errorSaving', 'Error saving treatment plan. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 rounded-lg shadow-lg mb-6\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent\">\n                  {t('treatmentPlanGoals', 'Treatment Plan & Goals')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-clipboard-list text-blue-500 mr-2\"></i>\n                  {t('treatmentPlanDescription', 'Comprehensive treatment planning with SMART goals')}\n                </p>\n                <div className=\"flex items-center space-x-4 mt-3\">\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-clock text-blue-500 mr-1\"></i>\n                    {t('estimatedTime', '30-45 minutes')}\n                  </span>\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-target text-green-500 mr-1\"></i>\n                    {t('smartGoals', 'SMART Goals')}\n                  </span>\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-chart-line text-purple-500 mr-1\"></i>\n                    {t('comprehensive', 'Comprehensive')}\n                  </span>\n                </div>\n              </div>\n              <div className=\"flex flex-col items-center space-y-2\">\n                <div className=\"bg-gradient-to-r from-blue-400 to-indigo-400 text-white px-4 py-2 rounded-full shadow-lg\">\n                  <i className=\"fas fa-clipboard-list mr-2\"></i>\n                  {t('treatmentPlan', 'Treatment Plan')}\n                </div>\n                <div className=\"bg-gradient-to-r from-green-400 to-emerald-400 text-white px-3 py-1 rounded-full text-sm shadow-md\">\n                  <i className=\"fas fa-bullseye mr-1\"></i>\n                  {t('goals', 'Goals')}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Patient Information */}\n        <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-green-900 dark:text-green-100 mb-6 flex items-center\">\n            <i className=\"fas fa-user text-green-600 dark:text-green-400 mr-2\"></i>\n            {t('patientInformation', 'Patient Information')}\n          </h2>\n\n          <div className=\"bg-green-100 dark:bg-green-800/30 border border-green-300 dark:border-green-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-green-800 dark:text-green-200\">\n              <i className=\"fas fa-info-circle text-green-600 dark:text-green-400 mr-2\"></i>\n              {t('patientInfoInstruction', 'Enter basic patient information and plan details for this treatment plan.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4\">\n              <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center\">\n                <i className=\"fas fa-user-tag text-green-600 dark:text-green-400 mr-1\"></i>\n                {t('patientName', 'Patient Name')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.patientName}\n                onChange={(e) => handleInputChange('patientName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ${\n                  errors.patientName ? 'border-red-500' : 'border-green-300 dark:border-green-600'\n                }`}\n                placeholder={t('enterPatientName', 'Enter patient name...')}\n                required\n              />\n              {errors.patientName && (\n                <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                  <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                  {errors.patientName}\n                </p>\n              )}\n            </div>\n\n            <div className=\"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4\">\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n                <i className=\"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1\"></i>\n                {t('planDate', 'Plan Date')} *\n              </label>\n              <input\n                type=\"date\"\n                value={formData.planDate}\n                onChange={(e) => handleInputChange('planDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500\"\n                required\n              />\n            </div>\n\n            <div className=\"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4\">\n              <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center\">\n                <i className=\"fas fa-user-md text-purple-600 dark:text-purple-400 mr-1\"></i>\n                {t('therapistName', 'Therapist Name')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.therapistName}\n                onChange={(e) => handleInputChange('therapistName', e.target.value)}\n                className=\"w-full px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500\"\n                placeholder={t('enterTherapistName', 'Enter therapist name...')}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Diagnosis & Assessment */}\n        <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-orange-900 dark:text-orange-100 mb-6 flex items-center\">\n            <i className=\"fas fa-stethoscope text-orange-600 dark:text-orange-400 mr-2\"></i>\n            {t('diagnosisAssessment', 'Diagnosis & Assessment')}\n          </h2>\n\n          <div className=\"bg-orange-100 dark:bg-orange-800/30 border border-orange-300 dark:border-orange-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-orange-800 dark:text-orange-200\">\n              <i className=\"fas fa-clipboard-check text-orange-600 dark:text-orange-400 mr-2\"></i>\n              {t('diagnosisInstruction', 'Document the patient\\'s diagnosis, functional limitations, and any precautions or contraindications.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            <div className=\"space-y-4\">\n              <div className=\"bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-diagnoses text-red-600 dark:text-red-400 mr-1\"></i>\n                  {t('primaryDiagnosis', 'Primary Diagnosis')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.primaryDiagnosis}\n                  onChange={(e) => handleInputChange('primaryDiagnosis', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500 ${\n                    errors.primaryDiagnosis ? 'border-red-500' : 'border-red-300 dark:border-red-600'\n                  }`}\n                  placeholder={t('enterPrimaryDiagnosis', 'Enter primary diagnosis...')}\n                  required\n                />\n                {errors.primaryDiagnosis && (\n                  <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                    <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                    {errors.primaryDiagnosis}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"bg-white dark:bg-pink-800/20 border border-pink-200 dark:border-pink-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-pink-800 dark:text-pink-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-plus-circle text-pink-600 dark:text-pink-400 mr-1\"></i>\n                  {t('secondaryDiagnosis', 'Secondary Diagnosis')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.secondaryDiagnosis}\n                  onChange={(e) => handleInputChange('secondaryDiagnosis', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-pink-300 dark:border-pink-600 rounded-lg bg-white dark:bg-pink-700 text-pink-900 dark:text-pink-100 focus:ring-2 focus:ring-pink-500\"\n                  placeholder={t('enterSecondaryDiagnosis', 'Enter secondary diagnosis...')}\n                />\n              </div>\n\n              <div className=\"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-code text-indigo-600 dark:text-indigo-400 mr-1\"></i>\n                  {t('icdCodes', 'ICD Codes')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.icdCodes}\n                  onChange={(e) => handleInputChange('icdCodes', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500\"\n                  placeholder={t('enterIcdCodes', 'Enter ICD codes...')}\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"bg-white dark:bg-yellow-800/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-1\"></i>\n                  {t('functionalLimitations', 'Functional Limitations')}\n                </label>\n                <textarea\n                  value={formData.functionalLimitations}\n                  onChange={(e) => handleInputChange('functionalLimitations', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500\"\n                  rows=\"3\"\n                  placeholder={t('describeFunctionalLimitations', 'Describe functional limitations...')}\n                />\n              </div>\n\n              <div className=\"bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-shield-alt text-red-600 dark:text-red-400 mr-1\"></i>\n                  {t('precautions', 'Precautions')}\n                </label>\n                <textarea\n                  value={formData.precautions}\n                  onChange={(e) => handleInputChange('precautions', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500\"\n                  rows=\"2\"\n                  placeholder={t('listPrecautions', 'List precautions...')}\n                />\n              </div>\n\n              <div className=\"bg-white dark:bg-gray-800/20 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-ban text-gray-600 dark:text-gray-400 mr-1\"></i>\n                  {t('contraindications', 'Contraindications')}\n                </label>\n                <textarea\n                  value={formData.contraindications}\n                  onChange={(e) => handleInputChange('contraindications', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500\"\n                  rows=\"2\"\n                  placeholder={t('listContraindications', 'List contraindications...')}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* SMART Goals */}\n        <div className=\"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6\">\n          <div className=\"flex items-center justify-between mb-6\">\n            <h2 className=\"text-lg font-semibold text-emerald-900 dark:text-emerald-100 flex items-center\">\n              <i className=\"fas fa-bullseye text-emerald-600 dark:text-emerald-400 mr-2\"></i>\n              {t('smartGoals', 'SMART Goals')}\n            </h2>\n\n            <button\n              type=\"button\"\n              onClick={addGoal}\n              className=\"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg flex items-center\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('addGoal', 'Add Goal')}\n            </button>\n          </div>\n\n          <div className=\"bg-emerald-100 dark:bg-emerald-800/30 border border-emerald-300 dark:border-emerald-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-emerald-800 dark:text-emerald-200\">\n              <i className=\"fas fa-target text-emerald-600 dark:text-emerald-400 mr-2\"></i>\n              {t('smartGoalsInstruction', 'Create SMART goals (Specific, Measurable, Achievable, Relevant, Time-bound) for the patient\\'s treatment plan.')}\n            </p>\n          </div>\n\n          <div className=\"space-y-6\">\n            {formData.goals.map((goal, index) => (\n              <div key={goal.id} className=\"bg-white dark:bg-emerald-800/20 border border-emerald-200 dark:border-emerald-600 rounded-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-lg font-medium text-emerald-900 dark:text-emerald-100 flex items-center\">\n                    <i className=\"fas fa-flag text-emerald-600 dark:text-emerald-400 mr-2\"></i>\n                    {t('goal', 'Goal')} {index + 1}\n                  </h3>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <select\n                      value={goal.type}\n                      onChange={(e) => handleGoalChange(index, 'type', e.target.value)}\n                      className=\"px-3 py-1 border border-emerald-300 dark:border-emerald-600 rounded-lg bg-white dark:bg-emerald-700 text-emerald-900 dark:text-emerald-100 text-sm\"\n                    >\n                      {goalTypes.map((type) => (\n                        <option key={type.value} value={type.value}>\n                          {type.label}\n                        </option>\n                      ))}\n                    </select>\n\n                    {formData.goals.length > 1 && (\n                      <button\n                        type=\"button\"\n                        onClick={() => removeGoal(index)}\n                        className=\"px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md flex items-center text-sm\"\n                      >\n                        <i className=\"fas fa-trash mr-1\"></i>\n                        {t('remove', 'Remove')}\n                      </button>\n                    )}\n                  </div>\n                </div>\n\n                {/* Goal Description */}\n                <div className=\"mb-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4\">\n                  <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n                    <i className=\"fas fa-align-left text-blue-600 dark:text-blue-400 mr-1\"></i>\n                    {t('goalDescription', 'Goal Description')} *\n                  </label>\n                  <textarea\n                    value={goal.description}\n                    onChange={(e) => handleGoalChange(index, 'description', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ${\n                      errors[`goal_${index}_description`] ? 'border-red-500' : 'border-blue-300 dark:border-blue-600'\n                    }`}\n                    rows=\"2\"\n                    placeholder={t('goalDescriptionPlaceholder', 'Patient will...')}\n                    required\n                  />\n                  {errors[`goal_${index}_description`] && (\n                    <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                      <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                      {errors[`goal_${index}_description`]}\n                    </p>\n                  )}\n                </div>\n\n                {/* SMART Components */}\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                  {/* Specific */}\n                  <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4\">\n                    <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center\">\n                      <i className=\"fas fa-crosshairs text-purple-600 dark:text-purple-400 mr-1\"></i>\n                      {t('specific', 'Specific')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={goal.specific}\n                      onChange={(e) => handleGoalChange(index, 'specific', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500\"\n                      placeholder={t('specificPlaceholder', 'What exactly will be achieved?')}\n                    />\n                  </div>\n\n                  {/* Measurable */}\n                  <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4\">\n                    <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center\">\n                      <i className=\"fas fa-ruler text-green-600 dark:text-green-400 mr-1\"></i>\n                      {t('measurable', 'Measurable')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={goal.measurable}\n                      onChange={(e) => handleGoalChange(index, 'measurable', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500\"\n                      placeholder={t('measurablePlaceholder', 'How will progress be measured?')}\n                    />\n                  </div>\n\n                  {/* Achievable */}\n                  <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4\">\n                    <label className=\"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center\">\n                      <i className=\"fas fa-check-circle text-yellow-600 dark:text-yellow-400 mr-1\"></i>\n                      {t('achievable', 'Achievable')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={goal.achievable}\n                      onChange={(e) => handleGoalChange(index, 'achievable', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500\"\n                      placeholder={t('achievablePlaceholder', 'Is this goal realistic?')}\n                    />\n                  </div>\n\n                  {/* Relevant */}\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4\">\n                    <label className=\"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center\">\n                      <i className=\"fas fa-link text-indigo-600 dark:text-indigo-400 mr-1\"></i>\n                      {t('relevant', 'Relevant')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={goal.relevant}\n                      onChange={(e) => handleGoalChange(index, 'relevant', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500\"\n                      placeholder={t('relevantPlaceholder', 'Why is this goal important?')}\n                    />\n                  </div>\n                </div>\n\n                {/* Time-bound */}\n                <div className=\"mt-4 grid grid-cols-1 lg:grid-cols-2 gap-4\">\n                  <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-600 rounded-lg p-4\">\n                    <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-2 flex items-center\">\n                      <i className=\"fas fa-clock text-red-600 dark:text-red-400 mr-1\"></i>\n                      {t('timebound', 'Time-bound')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={goal.timebound}\n                      onChange={(e) => handleGoalChange(index, 'timebound', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500\"\n                      placeholder={t('timeboundPlaceholder', 'When will this be achieved?')}\n                    />\n                  </div>\n\n                  <div className=\"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-600 rounded-lg p-4\">\n                    <label className=\"block text-sm font-medium text-teal-800 dark:text-teal-200 mb-2 flex items-center\">\n                      <i className=\"fas fa-calendar-alt text-teal-600 dark:text-teal-400 mr-1\"></i>\n                      {t('targetDate', 'Target Date')}\n                    </label>\n                    <input\n                      type=\"date\"\n                      value={goal.targetDate}\n                      onChange={(e) => handleGoalChange(index, 'targetDate', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-teal-300 dark:border-teal-600 rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100 focus:ring-2 focus:ring-teal-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Treatment Interventions */}\n        <div className=\"bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-violet-200 dark:border-violet-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-violet-900 dark:text-violet-100 mb-6 flex items-center\">\n            <i className=\"fas fa-tools text-violet-600 dark:text-violet-400 mr-2\"></i>\n            {t('treatmentInterventions', 'Treatment Interventions')}\n          </h2>\n\n          <div className=\"bg-violet-100 dark:bg-violet-800/30 border border-violet-300 dark:border-violet-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-violet-800 dark:text-violet-200\">\n              <i className=\"fas fa-hand-holding-medical text-violet-600 dark:text-violet-400 mr-2\"></i>\n              {t('interventionsInstruction', 'Select the treatment interventions and specify frequency, duration, and total sessions.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Interventions Selection */}\n            <div className=\"bg-white dark:bg-violet-800/20 border border-violet-200 dark:border-violet-600 rounded-lg p-4\">\n              <label className=\"block text-sm font-medium text-violet-800 dark:text-violet-200 mb-4 flex items-center\">\n                <i className=\"fas fa-list-check text-violet-600 dark:text-violet-400 mr-1\"></i>\n                {t('selectInterventions', 'Select Interventions')}\n              </label>\n\n              <div className=\"grid grid-cols-1 gap-2 max-h-64 overflow-y-auto\">\n                {interventionOptions.map((intervention) => (\n                  <div key={intervention} className=\"flex items-center bg-gray-50 dark:bg-violet-900/20 border border-violet-200 dark:border-violet-600 rounded-lg px-3 py-2\">\n                    <input\n                      type=\"checkbox\"\n                      id={`intervention-${intervention}`}\n                      checked={formData.interventions.includes(intervention)}\n                      onChange={(e) => handleCheckboxChange('interventions', intervention, e.target.checked)}\n                      className=\"mr-2 h-4 w-4 text-violet-600 focus:ring-violet-500 border-violet-300 rounded\"\n                    />\n                    <label htmlFor={`intervention-${intervention}`} className=\"text-sm text-violet-800 dark:text-violet-200 font-medium\">\n                      {intervention}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Treatment Schedule */}\n            <div className=\"space-y-4\">\n              <div className=\"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-calendar-week text-blue-600 dark:text-blue-400 mr-1\"></i>\n                  {t('frequency', 'Frequency')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.frequency}\n                  onChange={(e) => handleInputChange('frequency', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ${\n                    errors.frequency ? 'border-red-500' : 'border-blue-300 dark:border-blue-600'\n                  }`}\n                  placeholder={t('frequencyPlaceholder', 'e.g., 3x per week')}\n                  required\n                />\n                {errors.frequency && (\n                  <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                    <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                    {errors.frequency}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-hourglass-half text-green-600 dark:text-green-400 mr-1\"></i>\n                  {t('duration', 'Duration')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.duration}\n                  onChange={(e) => handleInputChange('duration', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ${\n                    errors.duration ? 'border-red-500' : 'border-green-300 dark:border-green-600'\n                  }`}\n                  placeholder={t('durationPlaceholder', 'e.g., 45 minutes')}\n                  required\n                />\n                {errors.duration && (\n                  <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                    <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                    {errors.duration}\n                  </p>\n                )}\n              </div>\n\n              <div className=\"bg-white dark:bg-orange-800/20 border border-orange-200 dark:border-orange-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-orange-800 dark:text-orange-200 mb-2 flex items-center\">\n                  <i className=\"fas fa-hashtag text-orange-600 dark:text-orange-400 mr-1\"></i>\n                  {t('totalSessions', 'Total Sessions')} *\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.totalSessions}\n                  onChange={(e) => handleInputChange('totalSessions', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-orange-700 text-orange-900 dark:text-orange-100 focus:ring-2 focus:ring-orange-500 ${\n                    errors.totalSessions ? 'border-red-500' : 'border-orange-300 dark:border-orange-600'\n                  }`}\n                  placeholder={t('totalSessionsPlaceholder', 'e.g., 12')}\n                  min=\"1\"\n                  required\n                />\n                {errors.totalSessions && (\n                  <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                    <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                    {errors.totalSessions}\n                  </p>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex justify-between items-center\">\n            <button\n              type=\"button\"\n              onClick={() => navigate(-1)}\n              className=\"px-6 py-3 bg-gradient-to-r from-gray-500 to-slate-500 text-white rounded-lg hover:from-gray-600 hover:to-slate-600 transition-all duration-200 shadow-lg flex items-center\"\n            >\n              <i className=\"fas fa-arrow-left mr-2\"></i>\n              {t('cancel', 'Cancel')}\n            </button>\n\n            <div className=\"flex space-x-4\">\n              <button\n                type=\"button\"\n                onClick={generatePDF}\n                disabled={loading}\n                className=\"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {loading ? t('generating', 'Generating...') : t('generatePDF', 'Generate PDF')}\n              </button>\n\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg\"\n              >\n                <i className=\"fas fa-save mr-2\"></i>\n                {loading ? t('saving', 'Saving...') : t('saveTreatmentPlan', 'Save Treatment Plan')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default TreatmentPlanForm;\n"], "names": ["TreatmentPlanForm", "t", "isRTL", "useLanguage", "user", "useAuth", "createSubmission", "updateSubmission", "generatePDF", "generateFormPDF", "loading", "formsLoading", "useForms", "patientId", "useParams", "navigate", "useNavigate", "formData", "setFormData", "useState", "patientName", "planDate", "Date", "toISOString", "split", "<PERSON><PERSON><PERSON>", "name", "primaryDiagnosis", "secondaryDiagnosis", "icdCodes", "functionalLimitations", "precautions", "contraindications", "goals", "id", "type", "description", "specific", "measurable", "achievable", "relevant", "timebound", "targetDate", "status", "interventions", "frequency", "duration", "totalSessions", "outcomeTools", "baselineMeasures", "targetOutcomes", "treatmentApproach", "progressionPlan", "homeExerciseProgram", "patientEducation", "reviewFrequency", "progressIndicators", "modificationCriteria", "dischargeGoals", "therapistSignature", "patientConsent", "planApproved", "setLoading", "errors", "setErrors", "goalTypes", "value", "label", "useEffect", "loadPatientData", "async", "response", "fetch", "concat", "ok", "patientData", "json", "prev", "_objectSpread", "_id", "error", "console", "handleInputChange", "field", "handleGoalChange", "index", "map", "goal", "i", "<PERSON><PERSON><PERSON>", "_jsx", "className", "children", "_jsxs", "onSubmit", "e", "preventDefault", "validateForm", "newErrors", "trim", "length", "Object", "keys", "submissionData", "formType", "formName", "data", "submittedBy", "submittedByName", "submittedAt", "metadata", "version", "formVersion", "compliance", "toast", "success", "onChange", "target", "placeholder", "required", "rows", "onClick", "addGoal", "newGoal", "now", "filter", "_", "removeGoal", "intervention", "checked", "includes", "handleCheckboxChange", "currentArray", "item", "htmlFor", "min", "pdfData", "generatedAt", "generatedBy", "email", "method", "headers", "localStorage", "getItem", "body", "JSON", "stringify", "Error", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "alert", "disabled"], "sourceRoot": ""}