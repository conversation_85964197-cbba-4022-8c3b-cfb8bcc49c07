"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[7752],{7752:(e,a,r)=>{r.r(a),r.d(a,{default:()=>c});var t=r(2555),s=r(5043),i=r(3216),l=r(7921),d=r(6305),n=r(3768),o=r(579);const c=()=>{const{id:e}=(0,i.g)(),a=(0,i.Zp)(),r=(0,i.zy)(),{t:c,isRTL:g}=(0,l.o)(),[u,m]=(0,s.useState)(!1),[x,y]=(0,s.useState)(!1),[b,p]=(0,s.useState)(null),[h,v]=(0,s.useState)({firstName:"",lastName:"",firstNameAr:"",lastNameAr:"",dateOfBirth:"",gender:"",nationality:"",nationalId:"",phone:"",email:"",address:{street:"",city:"",region:"",postalCode:"",country:"Saudi Arabia"},emergencyContact:{name:"",relationship:"",phone:"",email:""},insurance:{provider:"",policyNumber:"",groupNumber:"",expiryDate:"",copayAmount:"",deductible:"",coverageLimit:"",isActive:!0},medicalHistory:{allergies:[],currentMedications:[],pastSurgeries:[],chronicConditions:[],familyHistory:[],primaryDiagnosis:"",secondaryDiagnoses:[],referringPhysician:"",notes:""},specialNeeds:{hasSpecialNeeds:!1,types:[],severity:"mild",communicationMethod:"verbal",behavioralNotes:"",sensoryPreferences:{lighting:"natural",sound:"moderate",temperature:"room_temperature"},adaptiveEquipment:[],caregiverInstructions:""}});(0,s.useEffect)(()=>{(async()=>{m(!0);try{var a,t;let n=null;if(null!==(a=r.state)&&void 0!==a&&a.patient&&null!==(t=r.state)&&void 0!==t&&t.fromPatientProfile)n=r.state.patient;else if(e){const a=await d.mo.get("/patients/".concat(e));a.success&&(n=a.data)}var s,i,l,o,g,u,x,y,b,h,f,N,k,j,w,C,A,H,S,P,M,D,E,I,B,q,_,F,L,O,T,G,R,U,V,W,z,Z,J;if(n)p(n),v({firstName:n.firstName||"",lastName:n.lastName||"",firstNameAr:n.firstNameAr||"",lastNameAr:n.lastNameAr||"",dateOfBirth:n.dateOfBirth?n.dateOfBirth.split("T")[0]:"",gender:n.gender||"",nationality:n.nationality||"",nationalId:n.nationalId||"",phone:n.phone||"",email:n.email||"",address:{street:(null===(s=n.address)||void 0===s?void 0:s.street)||"",city:(null===(i=n.address)||void 0===i?void 0:i.city)||"",region:(null===(l=n.address)||void 0===l?void 0:l.region)||"",postalCode:(null===(o=n.address)||void 0===o?void 0:o.postalCode)||"",country:(null===(g=n.address)||void 0===g?void 0:g.country)||"Saudi Arabia"},emergencyContact:{name:(null===(u=n.emergencyContact)||void 0===u?void 0:u.name)||"",relationship:(null===(x=n.emergencyContact)||void 0===x?void 0:x.relationship)||"",phone:(null===(y=n.emergencyContact)||void 0===y?void 0:y.phone)||"",email:(null===(b=n.emergencyContact)||void 0===b?void 0:b.email)||""},insurance:{provider:(null===(h=n.insurance)||void 0===h?void 0:h.provider)||"",policyNumber:(null===(f=n.insurance)||void 0===f?void 0:f.policyNumber)||"",groupNumber:(null===(N=n.insurance)||void 0===N?void 0:N.groupNumber)||"",expiryDate:null!==(k=n.insurance)&&void 0!==k&&k.expiryDate?n.insurance.expiryDate.split("T")[0]:"",copayAmount:(null===(j=n.insurance)||void 0===j?void 0:j.copayAmount)||"",deductible:(null===(w=n.insurance)||void 0===w?void 0:w.deductible)||"",coverageLimit:(null===(C=n.insurance)||void 0===C?void 0:C.coverageLimit)||"",isActive:!1!==(null===(A=n.insurance)||void 0===A?void 0:A.isActive)},medicalHistory:{allergies:(null===(H=n.medicalHistory)||void 0===H?void 0:H.allergies)||[],currentMedications:(null===(S=n.medicalHistory)||void 0===S?void 0:S.currentMedications)||[],pastSurgeries:(null===(P=n.medicalHistory)||void 0===P?void 0:P.pastSurgeries)||[],chronicConditions:(null===(M=n.medicalHistory)||void 0===M?void 0:M.chronicConditions)||[],familyHistory:(null===(D=n.medicalHistory)||void 0===D?void 0:D.familyHistory)||[],primaryDiagnosis:(null===(E=n.medicalHistory)||void 0===E?void 0:E.primaryDiagnosis)||"",secondaryDiagnoses:(null===(I=n.medicalHistory)||void 0===I?void 0:I.secondaryDiagnoses)||[],referringPhysician:(null===(B=n.medicalHistory)||void 0===B?void 0:B.referringPhysician)||"",notes:(null===(q=n.medicalHistory)||void 0===q?void 0:q.notes)||""},specialNeeds:{hasSpecialNeeds:(null===(_=n.specialNeeds)||void 0===_?void 0:_.hasSpecialNeeds)||!1,types:(null===(F=n.specialNeeds)||void 0===F?void 0:F.types)||[],severity:(null===(L=n.specialNeeds)||void 0===L?void 0:L.severity)||"mild",communicationMethod:(null===(O=n.specialNeeds)||void 0===O?void 0:O.communicationMethod)||"verbal",behavioralNotes:(null===(T=n.specialNeeds)||void 0===T?void 0:T.behavioralNotes)||"",sensoryPreferences:{lighting:(null===(G=n.specialNeeds)||void 0===G||null===(R=G.sensoryPreferences)||void 0===R?void 0:R.lighting)||"natural",sound:(null===(U=n.specialNeeds)||void 0===U||null===(V=U.sensoryPreferences)||void 0===V?void 0:V.sound)||"moderate",temperature:(null===(W=n.specialNeeds)||void 0===W||null===(z=W.sensoryPreferences)||void 0===z?void 0:z.temperature)||"room_temperature"},adaptiveEquipment:(null===(Z=n.specialNeeds)||void 0===Z?void 0:Z.adaptiveEquipment)||[],caregiverInstructions:(null===(J=n.specialNeeds)||void 0===J?void 0:J.caregiverInstructions)||""}})}catch(K){console.error("Error loading patient data:",K),n.Ay.error(c("errorLoadingPatient","Error loading patient data"))}finally{m(!1)}})()},[e,r.state,c]);const f=(e,a,r)=>{v(e?s=>(0,t.A)((0,t.A)({},s),{},{[e]:(0,t.A)((0,t.A)({},s[e]),{},{[a]:r})}):e=>(0,t.A)((0,t.A)({},e),{},{[a]:r}))},N=(e,a,r,s)=>{v(i=>{const l=[...i[e][a]];return l[r]=s,(0,t.A)((0,t.A)({},i),{},{[e]:(0,t.A)((0,t.A)({},i[e]),{},{[a]:l})})})},k=(e,a)=>{v(r=>(0,t.A)((0,t.A)({},r),{},{[e]:(0,t.A)((0,t.A)({},r[e]),{},{[a]:[...r[e][a],""]})}))},j=(e,a,r)=>{v(s=>(0,t.A)((0,t.A)({},s),{},{[e]:(0,t.A)((0,t.A)({},s[e]),{},{[a]:s[e][a].filter((e,a)=>a!==r)})}))};return u?(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):b?(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-6",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,o.jsx)("div",{className:"mb-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:c("editPatient","Edit Patient")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:g?b.name:b.nameEn||"".concat(b.firstName," ").concat(b.lastName)})]}),(0,o.jsxs)("div",{className:"flex space-x-3",children:[(0,o.jsxs)("button",{onClick:()=>a("/patients/".concat(b._id||b.id)),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-times mr-2"}),c("cancel","Cancel")]}),(0,o.jsxs)("button",{onClick:async()=>{y(!0);try{const e=await d.mo.put("/patients/".concat(b._id||b.id),h);if(!e.success)throw new Error(e.message||"Failed to update patient");n.Ay.success(c("patientUpdated","Patient information updated successfully")),a("/patients/".concat(b._id||b.id))}catch(e){console.error("Error updating patient:",e),n.Ay.error(c("errorUpdatingPatient","Error updating patient information"))}finally{y(!1)}},disabled:x,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[(0,o.jsx)("i",{className:"fas ".concat(x?"fa-spinner fa-spin":"fa-save"," mr-2")}),x?c("saving","Saving..."):c("saveChanges","Save Changes")]})]})]})}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,o.jsx)("i",{className:"fas fa-user text-blue-600 dark:text-blue-400 mr-2"}),c("basicInformation","Basic Information")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("firstName","First Name")}),(0,o.jsx)("input",{type:"text",value:h.firstName,onChange:e=>f(null,"firstName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("lastName","Last Name")}),(0,o.jsx)("input",{type:"text",value:h.lastName,onChange:e=>f(null,"lastName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("dateOfBirth","Date of Birth")}),(0,o.jsx)("input",{type:"date",value:h.dateOfBirth,onChange:e=>f(null,"dateOfBirth",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("gender","Gender")}),(0,o.jsxs)("select",{value:h.gender,onChange:e=>f(null,"gender",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:c("selectGender","Select Gender")}),(0,o.jsx)("option",{value:"male",children:c("male","Male")}),(0,o.jsx)("option",{value:"female",children:c("female","Female")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("nationality","Nationality")}),(0,o.jsx)("input",{type:"text",value:h.nationality,onChange:e=>f(null,"nationality",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("nationalId","National ID")}),(0,o.jsx)("input",{type:"text",value:h.nationalId,onChange:e=>f(null,"nationalId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("phone","Phone")}),(0,o.jsx)("input",{type:"tel",value:h.phone,onChange:e=>f(null,"phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("email","Email")}),(0,o.jsx)("input",{type:"email",value:h.email,onChange:e=>f(null,"email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,o.jsx)("i",{className:"fas fa-phone text-red-600 dark:text-red-400 mr-2"}),c("emergencyContact","Emergency Contact")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("name","Name")}),(0,o.jsx)("input",{type:"text",value:h.emergencyContact.name,onChange:e=>f("emergencyContact","name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("relationship","Relationship")}),(0,o.jsx)("input",{type:"text",value:h.emergencyContact.relationship,onChange:e=>f("emergencyContact","relationship",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("phone","Phone")}),(0,o.jsx)("input",{type:"tel",value:h.emergencyContact.phone,onChange:e=>f("emergencyContact","phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("email","Email")}),(0,o.jsx)("input",{type:"email",value:h.emergencyContact.email,onChange:e=>f("emergencyContact","email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]})]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,o.jsx)("i",{className:"fas fa-shield-alt text-purple-600 dark:text-purple-400 mr-2"}),c("insuranceInformation","Insurance Information")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("provider","Provider")}),(0,o.jsx)("input",{type:"text",value:h.insurance.provider,onChange:e=>f("insurance","provider",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Bupa Arabia, Tawuniya"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("policyNumber","Policy Number")}),(0,o.jsx)("input",{type:"text",value:h.insurance.policyNumber,onChange:e=>f("insurance","policyNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("groupNumber","Group Number")}),(0,o.jsx)("input",{type:"text",value:h.insurance.groupNumber,onChange:e=>f("insurance","groupNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("expiryDate","Expiry Date")}),(0,o.jsx)("input",{type:"date",value:h.insurance.expiryDate,onChange:e=>f("insurance","expiryDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("copayAmount","Copay Amount")}),(0,o.jsx)("input",{type:"number",value:h.insurance.copayAmount,onChange:e=>f("insurance","copayAmount",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"0"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("deductible","Deductible")}),(0,o.jsx)("input",{type:"number",value:h.insurance.deductible,onChange:e=>f("insurance","deductible",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"0"})]}),(0,o.jsx)("div",{className:"md:col-span-2",children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:h.insurance.isActive,onChange:e=>f("insurance","isActive",e.target.checked),className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("insuranceActive","Insurance is active")})]})})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,o.jsx)("i",{className:"fas fa-notes-medical text-red-600 dark:text-red-400 mr-2"}),c("medicalHistory","Medical History")]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("primaryDiagnosis","Primary Diagnosis")}),(0,o.jsx)("input",{type:"text",value:h.medicalHistory.primaryDiagnosis,onChange:e=>f("medicalHistory","primaryDiagnosis",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Cerebral Palsy, Stroke, etc."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("referringPhysician","Referring Physician")}),(0,o.jsx)("input",{type:"text",value:h.medicalHistory.referringPhysician,onChange:e=>f("medicalHistory","referringPhysician",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Dr. Name"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("allergies","Allergies")}),(0,o.jsxs)("div",{className:"space-y-2",children:[h.medicalHistory.allergies.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"text",value:e,onChange:e=>N("medicalHistory","allergies",a,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Penicillin, Latex, etc."}),(0,o.jsx)("button",{type:"button",onClick:()=>j("medicalHistory","allergies",a),className:"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]},a)),(0,o.jsxs)("button",{type:"button",onClick:()=>k("medicalHistory","allergies"),className:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),c("addAllergy","Add Allergy")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("currentMedications","Current Medications")}),(0,o.jsxs)("div",{className:"space-y-2",children:[h.medicalHistory.currentMedications.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"text",value:e,onChange:e=>N("medicalHistory","currentMedications",a,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Baclofen 10mg twice daily"}),(0,o.jsx)("button",{type:"button",onClick:()=>j("medicalHistory","currentMedications",a),className:"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]},a)),(0,o.jsxs)("button",{type:"button",onClick:()=>k("medicalHistory","currentMedications"),className:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),c("addMedication","Add Medication")]})]})]})]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,o.jsx)("i",{className:"fas fa-heart text-pink-600 dark:text-pink-400 mr-2"}),c("specialNeeds","Special Needs")]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("div",{children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:h.specialNeeds.hasSpecialNeeds,onChange:e=>f("specialNeeds","hasSpecialNeeds",e.target.checked),className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"}),(0,o.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:c("hasSpecialNeeds","Patient has special needs")})]})}),h.specialNeeds.hasSpecialNeeds&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("severity","Severity")}),(0,o.jsxs)("select",{value:h.specialNeeds.severity,onChange:e=>f("specialNeeds","severity",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"mild",children:c("mild","Mild")}),(0,o.jsx)("option",{value:"moderate",children:c("moderate","Moderate")}),(0,o.jsx)("option",{value:"severe",children:c("severe","Severe")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("communicationMethod","Communication Method")}),(0,o.jsxs)("select",{value:h.specialNeeds.communicationMethod,onChange:e=>f("specialNeeds","communicationMethod",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"verbal",children:c("verbal","Verbal")}),(0,o.jsx)("option",{value:"sign_language",children:c("signLanguage","Sign Language")}),(0,o.jsx)("option",{value:"visual_aids",children:c("visualAids","Visual Aids")}),(0,o.jsx)("option",{value:"assistive_technology",children:c("assistiveTechnology","Assistive Technology")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("behavioralNotes","Behavioral Notes")}),(0,o.jsx)("textarea",{value:h.specialNeeds.behavioralNotes,onChange:e=>f("specialNeeds","behavioralNotes",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Behavioral patterns, triggers, calming techniques..."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("caregiverInstructions","Caregiver Instructions")}),(0,o.jsx)("textarea",{value:h.specialNeeds.caregiverInstructions,onChange:e=>f("specialNeeds","caregiverInstructions",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Special instructions for caregivers..."})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("adaptiveEquipment","Adaptive Equipment")}),(0,o.jsxs)("div",{className:"space-y-2",children:[h.specialNeeds.adaptiveEquipment.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"text",value:e,onChange:e=>N("specialNeeds","adaptiveEquipment",a,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Wheelchair, Walker, Communication device"}),(0,o.jsx)("button",{type:"button",onClick:()=>j("specialNeeds","adaptiveEquipment",a),className:"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]},a)),(0,o.jsxs)("button",{type:"button",onClick:()=>k("specialNeeds","adaptiveEquipment"),className:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),c("addEquipment","Add Equipment")]})]})]})]})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,o.jsx)("i",{className:"fas fa-stethoscope text-teal-600 dark:text-teal-400 mr-2"}),c("additionalMedicalInfo","Additional Medical Information")]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("pastSurgeries","Past Surgeries")}),(0,o.jsxs)("div",{className:"space-y-2",children:[h.medicalHistory.pastSurgeries.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"text",value:e,onChange:e=>N("medicalHistory","pastSurgeries",a,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Appendectomy (2020), Hip replacement (2019)"}),(0,o.jsx)("button",{type:"button",onClick:()=>j("medicalHistory","pastSurgeries",a),className:"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]},a)),(0,o.jsxs)("button",{type:"button",onClick:()=>k("medicalHistory","pastSurgeries"),className:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),c("addSurgery","Add Surgery")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("chronicConditions","Chronic Conditions")}),(0,o.jsxs)("div",{className:"space-y-2",children:[h.medicalHistory.chronicConditions.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"text",value:e,onChange:e=>N("medicalHistory","chronicConditions",a,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Diabetes, Hypertension, Arthritis"}),(0,o.jsx)("button",{type:"button",onClick:()=>j("medicalHistory","chronicConditions",a),className:"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]},a)),(0,o.jsxs)("button",{type:"button",onClick:()=>k("medicalHistory","chronicConditions"),className:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),c("addCondition","Add Condition")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("familyHistory","Family History")}),(0,o.jsxs)("div",{className:"space-y-2",children:[h.medicalHistory.familyHistory.map((e,a)=>(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{type:"text",value:e,onChange:e=>N("medicalHistory","familyHistory",a,e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"e.g., Father - Heart disease, Mother - Diabetes"}),(0,o.jsx)("button",{type:"button",onClick:()=>j("medicalHistory","familyHistory",a),className:"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]},a)),(0,o.jsxs)("button",{type:"button",onClick:()=>k("medicalHistory","familyHistory"),className:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),c("addFamilyHistory","Add Family History")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:c("medicalNotes","Medical Notes")}),(0,o.jsx)("textarea",{value:h.medicalHistory.notes,onChange:e=>f("medicalHistory","notes",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:"Additional medical notes, observations, or important information..."})]})]})]})]})]})}):(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:c("patientNotFound","Patient not found")}),(0,o.jsx)("button",{onClick:()=>a("/patients"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:c("backToPatients","Back to Patients")})]})})}}}]);
//# sourceMappingURL=7752.b5a7a2a5.chunk.js.map