{"version": 3, "file": "static/js/2570.5c7e56b8.chunk.js", "mappings": "uNAKA,MAioCA,EAjoC8BA,KAC5B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAEvCC,eAAe,MAADC,OAAQC,KAAKC,OAC3BC,WAAW,IAAIF,MAAOG,cAAcC,MAAM,KAAK,GAC/CC,QAAS,KACTC,aAAc,KAGdC,YAAa,GACbC,SAAU,GACVC,UAAW,GACXC,UAAW,GACXC,UAAW,GAGXC,mBAAoB,GACpBC,sBAAuB,GACvBC,+BAAgC,GAChCC,qBAAsB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAG3CC,eAAgB,CACdC,MAAO,GACPC,MAAO,CAAC,KAEVC,cAAe,CACbF,MAAO,GACPC,MAAO,CAAC,KAIVE,YAAa,GACbC,eAAgB,GAChBC,WAAY,GACZC,mBAAoB,GACpBC,oBAAqB,GACrBC,mBAAoB,GACpBC,eAAgB,GAChBC,iBAAkB,GAClBC,aAAc,GACdC,kBAAkB,EAClBC,gBAAiB,GAGjBC,yBAAyB,EACzBC,mBAAoB,GACpBC,eAAgB,GAChBC,eAAe,IAAIlC,MAAOG,cAAcC,MAAM,KAAK,GACnD+B,mBAAoB,GACpBC,eAAgB,GAChBC,cAAe,GAGfC,aAAiB,OAAJjD,QAAI,IAAJA,OAAI,EAAJA,EAAMkD,KAAM,GACzBC,aAAa,IAAIxC,MAAOG,iBAGnBsC,EAASC,IAAc7C,EAAAA,EAAAA,WAAS,IAChC8C,EAAQC,IAAa/C,EAAAA,EAAAA,UAAS,CAAC,IAC/BgD,EAAaC,IAAkBjD,EAAAA,EAAAA,UAAS,GAGzCkD,EAA4B,CAChC,CAAEC,MAAO,OAAQC,MAAO/D,EAAE,OAAQ,QAASgE,eAAe,GAC1D,CAAEF,MAAO,eAAgBC,MAAO/D,EAAE,cAAe,kCAA8BgE,eAAe,GAC9F,CAAEF,MAAO,kBAAmBC,MAAO/D,EAAE,iBAAkB,0BAAsBgE,eAAe,GAC5F,CAAEF,MAAO,SAAUC,MAAO/D,EAAE,QAAS,kBAAmBgE,eAAe,GACvE,CAAEF,MAAO,SAAUC,MAAO/D,EAAE,QAAS,kBAAmBgE,eAAe,GACvE,CAAEF,MAAO,cAAeC,MAAO/D,EAAE,aAAc,sBAAkBgE,eAAe,GAChF,CAAEF,MAAO,cAAeC,MAAO/D,EAAE,aAAc,sBAAkBgE,eAAe,GAChF,CAAEF,MAAO,sBAAuBC,MAAO/D,EAAE,oBAAqB,8BAA0BgE,eAAe,GACvG,CAAEF,MAAO,gBAAiBC,MAAO/D,EAAE,eAAgB,iBAAkBgE,eAAe,GACpF,CAAEF,MAAO,oBAAqBC,MAAO/D,EAAE,mBAAoB,qBAAsBgE,eAAe,GAChG,CAAEF,MAAO,iBAAkBC,MAAO/D,EAAE,gBAAiB,kBAAmBgE,eAAe,GACvF,CAAEF,MAAO,iBAAkBC,MAAO/D,EAAE,gBAAiB,uCAAwCgE,eAAe,IAGxGC,EAA+B,CACnC,CAAEH,MAAO,UAAWC,MAAO/D,EAAE,UAAW,YACxC,CAAE8D,MAAO,kBAAmBC,MAAO/D,EAAE,iBAAkB,oBACvD,CAAE8D,MAAO,YAAaC,MAAO/D,EAAE,YAAa,2BAC5C,CAAE8D,MAAO,oBAAqBC,MAAO/D,EAAE,mBAAoB,yBAC3D,CAAE8D,MAAO,oBAAqBC,MAAO/D,EAAE,mBAAoB,6BAC3D,CAAE8D,MAAO,0BAA2BC,MAAO/D,EAAE,wBAAyB,4BACtE,CAAE8D,MAAO,eAAgBC,MAAO/D,EAAE,cAAe,kCACjD,CAAE8D,MAAO,0BAA2BC,MAAO/D,EAAE,wBAAyB,4BACtE,CAAE8D,MAAO,cAAeC,MAAO/D,EAAE,aAAc,wBAC/C,CAAE8D,MAAO,wBAAyBC,MAAO/D,EAAE,uBAAwB,0BACnE,CAAE8D,MAAO,gBAAiBC,MAAO/D,EAAE,eAAgB,mBAG/CkE,EAAwC,CAC5C,CAAEJ,MAAO,YAAaC,MAAO/D,EAAE,YAAa,qBAC5C,CAAE8D,MAAO,YAAaC,MAAO/D,EAAE,YAAa,oCAC5C,CAAE8D,MAAO,cAAeC,MAAO/D,EAAE,cAAe,gCAChD,CAAE8D,MAAO,aAAcC,MAAO/D,EAAE,YAAa,gCAC7C,CAAE8D,MAAO,sBAAuBC,MAAO/D,EAAE,qBAAsB,mDAC/D,CAAE8D,MAAO,qBAAsBC,MAAO/D,EAAE,oBAAqB,uBAC7D,CAAE8D,MAAO,sBAAuBC,MAAO/D,EAAE,qBAAsB,wBAC/D,CAAE8D,MAAO,cAAeC,MAAO/D,EAAE,cAAe,gBAChD,CAAE8D,MAAO,QAASC,MAAO/D,EAAE,QAAS,WAIhCmE,EAAmB,CACvBjC,YAAa,CACX,CAAE4B,MAAO,KAAMC,MAAO/D,EAAE,aAAc,OACtC,CAAE8D,MAAO,QAASC,MAAO/D,EAAE,QAAS,UACpC,CAAE8D,MAAO,OAAQC,MAAO/D,EAAE,OAAQ,SAClC,CAAE8D,MAAO,UAAWC,MAAO/D,EAAE,UAAW,aAE1CmC,eAAgB,CACd,CAAE2B,MAAO,cAAeC,MAAO/D,EAAE,cAAe,gBAChD,CAAE8D,MAAO,MAAOC,MAAO/D,EAAE,MAAO,QAChC,CAAE8D,MAAO,cAAeC,MAAO/D,EAAE,cAAe,iBAElDoC,WAAY,CACV,CAAE0B,MAAO,OAAQC,MAAO/D,EAAE,OAAQ,SAClC,CAAE8D,MAAO,eAAgBC,MAAO/D,EAAE,eAAgB,iBAClD,CAAE8D,MAAO,MAAOC,MAAO/D,EAAE,MAAO,SAElCqC,mBAAoB,CAClB,CAAEyB,MAAO,aAAcC,MAAO/D,EAAE,aAAc,eAC9C,CAAE8D,MAAO,eAAgBC,MAAO/D,EAAE,UAAW,YAC7C,CAAE8D,MAAO,aAAcC,MAAO/D,EAAE,aAAc,wBAEhDsC,oBAAqB,CACnB,CAAEwB,MAAO,YAAaC,MAAO/D,EAAE,YAAa,cAC5C,CAAE8D,MAAO,kBAAmBC,MAAO/D,EAAE,iBAAkB,oBACvD,CAAE8D,MAAO,kBAAmBC,MAAO/D,EAAE,iBAAkB,oBACvD,CAAE8D,MAAO,qBAAsBC,MAAO/D,EAAE,oBAAqB,uBAC7D,CAAE8D,MAAO,cAAeC,MAAO/D,EAAE,cAAe,gBAChD,CAAE8D,MAAO,MAAOC,MAAO/D,EAAE,MAAO,QAChC,CAAE8D,MAAO,MAAOC,MAAO/D,EAAE,MAAO,SAElCuC,mBAAoB,CAClB,CAAEuB,MAAO,iBAAkBC,MAAO/D,EAAE,gBAAiB,0BACrD,CAAE8D,MAAO,aAAcC,MAAO/D,EAAE,aAAc,eAC9C,CAAE8D,MAAO,aAAcC,MAAO/D,EAAE,YAAa,gBAE/CwC,eAAgB,CACd,CAAEsB,MAAO,WAAYC,MAAO/D,EAAE,WAAY,iBAC1C,CAAE8D,MAAO,gBAAiBC,MAAO/D,EAAE,eAAgB,kBACnD,CAAE8D,MAAO,UAAWC,MAAO/D,EAAE,UAAW,iBACxC,CAAE8D,MAAO,iBAAkBC,MAAO/D,EAAE,iBAAkB,4BACtD,CAAE8D,MAAO,cAAeC,MAAO/D,EAAE,cAAe,wBAElDyC,iBAAkB,CAChB,CAAEqB,MAAO,UAAWC,MAAO/D,EAAE,UAAW,iBACxC,CAAE8D,MAAO,UAAWC,MAAO/D,EAAE,UAAW,YACxC,CAAE8D,MAAO,YAAaC,MAAO/D,EAAE,YAAa,eAE9C0C,aAAc,CACZ,CAAEoB,MAAO,cAAeC,MAAO/D,EAAE,aAAc,wBAC/C,CAAE8D,MAAO,MAAOC,MAAO/D,EAAE,MAAO,wBAChC,CAAE8D,MAAO,MAAOC,MAAO/D,EAAE,MAAO,QAChC,CAAE8D,MAAO,KAAMC,MAAO/D,EAAE,KAAM,UAIlCoE,EAAAA,EAAAA,WAAU,KACJ/D,GACFgE,KAED,CAAChE,IAEJ,MAAMgE,EAAkBC,UACtB,IACEd,GAAW,GACX,MAAMe,QAAiBC,MAAM,iBAAD3D,OAAkBR,IAC9C,GAAIkE,EAASE,GAAI,CACf,MAAMC,QAAoBH,EAASI,OACnCjE,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPvD,YAAaqD,EAAYI,MAAQ,GACjCxD,SAAUoD,EAAYpD,UAAY,GAClCC,UAAWmD,EAAYnD,WAAa,KAExC,CACF,CAAE,MAAOwD,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACCvB,GAAW,EACb,GAGIyB,EAAoBA,CAACC,EAAOpB,KAChCpD,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACM,GAAQpB,KAGPL,EAAOyB,IACTxB,EAAUkB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACM,GAAQ,SAKTC,EAAuBA,CAACD,EAAOE,EAAQC,KAC3C3E,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACM,GAAQG,EACL,IAAIT,EAAKM,GAAQE,GACjBR,EAAKM,GAAOI,OAAOC,GAAQA,IAASH,OAItCI,EAAmBA,CAACC,EAAMC,EAAO5B,KACrCpD,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACa,IAAIZ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACAD,EAAKa,IAAK,IACbzD,MAAO4C,EAAKa,GAAMzD,MAAM2D,IAAI,CAACC,EAAMC,IAAMA,IAAMH,EAAQ5B,EAAQ8B,SAK/DE,EAAWL,IACf/E,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACa,IAAIZ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACAD,EAAKa,IAAK,IACbzD,MAAO,IAAI4C,EAAKa,GAAMzD,MAAO,UAK7B+D,EAAaA,CAACN,EAAMC,KACxBhF,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACa,IAAIZ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACAD,EAAKa,IAAK,IACbzD,MAAO4C,EAAKa,GAAMzD,MAAMsD,OAAO,CAACU,EAAGH,IAAMA,IAAMH,SAmHrD,OAAInC,GAEA0C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7DnG,EAAE,oBAAqB,4CAE1BiG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDnG,EAAE,wBAAyB,0DAGhCoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,kGAAiGC,SAAA,EAC/GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACZlG,EAAE,aAAc,oBAEnBoG,EAAAA,EAAAA,MAAA,QAAMF,UAAU,sGAAqGC,SAAA,CAClHnG,EAAE,OAAQ,QAAQ,IAAE2D,EAAY,mBAQ3CsC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAsB,aAAW,OAAMC,SACnD,CACC,CAAE9C,GAAI,EAAGU,MAAO/D,EAAE,uBAAwB,6BAC1C,CAAEqD,GAAI,EAAGU,MAAO/D,EAAE,0BAA2B,iCAC7C2F,IAAKU,IACLJ,EAAAA,EAAAA,KAAA,UAEEK,QAASA,IAAM1C,EAAeyC,EAAIhD,IAClC6C,UAAS,4CAAArF,OACP8C,IAAgB0C,EAAIhD,GAChB,mDACA,0HACH8C,SAEFE,EAAItC,OARAsC,EAAIhD,YAgBnB+C,EAAAA,EAAAA,MAAA,QAAMG,SAlGWjC,UAGnB,GAFAkC,EAAEC,iBAzEiBC,MACnB,MAAMC,EAAY,CAAC,EAyBnB,OAtBKlG,EAASY,YAAYuF,SAAQD,EAAUtF,YAAcrB,EAAE,sBAAuB,6BAC9ES,EAASa,SAASsF,SAAQD,EAAUrF,SAAWtB,EAAE,mBAAoB,0BACrES,EAASc,UAAUqF,SAAQD,EAAUpF,UAAYvB,EAAE,oBAAqB,0BACxES,EAASe,YAAWmF,EAAUnF,UAAYxB,EAAE,oBAAqB,2BACjES,EAASgB,UAAUmF,SAAQD,EAAUlF,UAAYzB,EAAE,oBAAqB,0BAGxES,EAASqB,eAAeC,QAAO4E,EAAUE,eAAiB7G,EAAE,yBAA0B,8BACvFS,EAASqB,eAAeE,MAAM8E,MAAMlB,IAASA,EAAKgB,UACpDD,EAAU7E,eAAiB9B,EAAE,yBAA0B,6CAGpDS,EAASwB,cAAcF,QAAO4E,EAAUI,cAAgB/G,EAAE,wBAAyB,6BACpFS,EAASwB,cAAcD,MAAM8E,MAAMlB,IAASA,EAAKgB,UACnDD,EAAU1E,cAAgBjC,EAAE,wBAAyB,4CAIlDS,EAASqC,mBAAmB8D,SAAQD,EAAU7D,mBAAqB9C,EAAE,6BAA8B,oCACnGS,EAASsC,eAAe6D,SAAQD,EAAU5D,eAAiB/C,EAAE,yBAA0B,uCAE5F0D,EAAUiD,GAC+B,IAAlCK,OAAOC,KAAKN,GAAWO,QAiDzBR,GAIL,IACElD,GAAW,GAEX,MAAM2D,GAActC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfpE,GAAQ,IACX2C,YAAajD,EAAKkD,GAClBC,aAAa,IAAIxC,MAAOG,gBAW1B,WARuBuD,MAAM,sCAAuC,CAClE4C,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAUL,MAGV1C,GAIX,MAAM,IAAIgD,MAAM,+BAHhBC,MAAM1H,EAAE,kBAAmB,6CAC3BO,EAASF,EAAS,aAAAQ,OAAgBR,GAAc,YAIpD,CAAE,MAAO0E,GACPC,QAAQD,MAAM,6BAA8BA,GAC5C2C,MAAM1H,EAAE,cAAe,gDACzB,CAAC,QACCwD,GAAW,EACb,GA+DgC0C,UAAU,YAAWC,SAAA,CAEhC,IAAhBxC,IACCyC,EAAAA,EAAAA,MAAAuB,EAAAA,SAAA,CAAAxB,SAAA,EAEEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iGAAgGC,UAC7GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0DAAyDC,SACvEnG,EAAE,iBAAkB,sBAEvBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASG,eAChBgH,SAAWpB,GAAMvB,EAAkB,iBAAkBuB,EAAEqB,OAAO/D,OAC9DoC,UAAU,+HACV4B,UAAQ,QAGZ1B,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0DAAyDC,SACvEnG,EAAE,YAAa,iBAElBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASO,UAChB4G,SAAWpB,GAAMvB,EAAkB,YAAauB,EAAEqB,OAAO/D,OACzDoC,UAAU,qIAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0DAAyDC,SACvEnG,EAAE,UAAW,cAEhBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASU,QAChB+E,UAAU,kIACV4B,UAAQ,QAGZ1B,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0DAAyDC,SACvEnG,EAAE,eAAgB,oBAErBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASW,aAChB8E,UAAU,kIACV4B,UAAQ,aAOhB1B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEnG,EAAE,qBAAsB,0BAG3BoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,cAAe,gBAAgB,SAEpCiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASY,YAChBuG,SAAWpB,GAAMvB,EAAkB,cAAeuB,EAAEqB,OAAO/D,OAC3DoC,UAAS,8FAAArF,OACP4C,EAAOpC,YAAc,iBAAmB,wCAE1C0G,UAAQ,IAETtE,EAAOpC,cACN4E,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOpC,kBAIrD+E,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,WAAY,QAAQ,SAEzBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASa,SAChBsG,SAAWpB,GAAMvB,EAAkB,WAAYuB,EAAEqB,OAAO/D,OACxDoC,UAAS,8FAAArF,OACP4C,EAAOnC,SAAW,iBAAmB,wCAEvCyG,UAAQ,IAETtE,EAAOnC,WACN2E,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOnC,eAIrD8E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,YAAa,aAAa,SAE/BiG,EAAAA,EAAAA,KAAA,YACEnC,MAAOrD,EAASc,UAChBqG,SAAWpB,GAAMvB,EAAkB,YAAauB,EAAEqB,OAAO/D,OACzDkE,KAAK,IACL9B,UAAS,8FAAArF,OACP4C,EAAOlC,UAAY,iBAAmB,wCAExCwG,UAAQ,IAETtE,EAAOlC,YACN0E,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOlC,gBAIrD6E,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,YAAa,cAAc,SAEhCiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASe,UAChBoG,SAAWpB,GAAMvB,EAAkB,YAAauB,EAAEqB,OAAO/D,OACzDoC,UAAS,8FAAArF,OACP4C,EAAOjC,UAAY,iBAAmB,wCAExCuG,UAAQ,IAETtE,EAAOjC,YACNyE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOjC,gBAIrD4E,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,YAAa,aAAa,SAE/BiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASgB,UAChBmG,SAAWpB,GAAMvB,EAAkB,YAAauB,EAAEqB,OAAO/D,OACzDoC,UAAS,8FAAArF,OACP4C,EAAOhC,UAAY,iBAAmB,wCAExCsG,UAAQ,IAETtE,EAAOhC,YACNwE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOhC,sBAOzD2E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEnG,EAAE,kBAAmB,6CAGxBiG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,qEAAoEC,SAAA,EACnFF,EAAAA,EAAAA,KAAA,SAAAE,UACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,8BAA6BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oHAAmHC,SAC9HnG,EAAE,qBAAsB,0BAE3BiG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oHAAmHC,SAC9HnG,EAAE,wBAAyB,6BAE9BiG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oHAAmHC,SAC9HnG,EAAE,iCAAkC,uCAEvCiG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oHAAmHC,SAC9HnG,EAAE,sBAAuB,oDAIhCiG,EAAAA,EAAAA,KAAA,SAAAE,UACEC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kEAAiEC,UAC7EF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBtC,EAA0B8B,IAAKP,IAC9BgB,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACER,KAAK,WACLpC,GAAE,MAAAxC,OAAQuE,EAAOtB,OACjBuB,QAAS5E,EAASiB,mBAAmBuG,KAAKC,GAAKA,EAAEpE,QAAUsB,EAAOtB,OAClE8D,SAAWpB,IACLA,EAAEqB,OAAOxC,QACXF,EAAqB,qBAAsB,CAAErB,MAAOsB,EAAOtB,MAAOC,MAAOqB,EAAOrB,MAAOoE,WAAY/C,EAAOpB,cAAgB,IAAM,OAAQ,GAExItD,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPlD,mBAAoBkD,EAAKlD,mBAAmB4D,OAAO4C,GAAKA,EAAEpE,QAAUsB,EAAOtB,WAIjFoC,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAOmC,QAAO,MAAAvH,OAAQuE,EAAOtB,OAASoC,UAAU,2CAA0CC,SACvFf,EAAOrB,QAETqB,EAAOpB,eAAiBvD,EAASiB,mBAAmBuG,KAAKC,GAAKA,EAAEpE,QAAUsB,EAAOtB,SAChFmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,SACjC,CAAC,IAAK,IAAK,KAAKR,IAAK0C,IAAI,IAAAC,EAAA,OACxBlC,EAAAA,EAAAA,MAAA,SAAkBF,UAAU,oBAAmBC,SAAA,EAC7CF,EAAAA,EAAAA,KAAA,SACER,KAAK,QACLX,KAAI,cAAAjE,OAAgBuE,EAAOtB,OAC3BA,MAAOuE,EACPhD,SAAwE,QAA/DiD,EAAA7H,EAASiB,mBAAmB6G,KAAKL,GAAKA,EAAEpE,QAAUsB,EAAOtB,cAAM,IAAAwE,OAAA,EAA/DA,EAAiEH,cAAeE,EACzFT,SAAWpB,IACT9F,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPlD,mBAAoBkD,EAAKlD,mBAAmBiE,IAAIuC,GAC9CA,EAAEpE,QAAUsB,EAAOtB,OAAKe,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQqD,GAAC,IAAEC,WAAY3B,EAAEqB,OAAO/D,QAAUoE,OAIxEhC,UAAU,gCAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAEkC,MAhBlDA,SAvBVjD,EAAOtB,aAgDvBmC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kEAAiEC,UAC7EF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBlC,EAA6B0B,IAAKP,IACjCgB,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACER,KAAK,WACLpC,GAAE,MAAAxC,OAAQuE,EAAOtB,OACjBuB,QAAS5E,EAASkB,sBAAsB6G,SAASpD,EAAOtB,OACxD8D,SAAWpB,GAAMrB,EAAqB,wBAAyBC,EAAOtB,MAAO0C,EAAEqB,OAAOxC,SACtFa,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAOmC,QAAO,MAAAvH,OAAQuE,EAAOtB,OAASoC,UAAU,2CAA0CC,SACvFf,EAAOrB,UATFqB,EAAOtB,aAevBmC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kEAAiEC,UAC7EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBjC,EAAsCyB,IAAKP,IAC1CgB,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACER,KAAK,WACLpC,GAAE,OAAAxC,OAASuE,EAAOtB,OAClBuB,QAAS5E,EAASmB,+BAA+B4G,SAASpD,EAAOtB,OACjE8D,SAAWpB,GAAMrB,EAAqB,iCAAkCC,EAAOtB,MAAO0C,EAAEqB,OAAOxC,SAC/Fa,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAOmC,QAAO,OAAAvH,OAASuE,EAAOtB,OAASoC,UAAU,2CAA0CC,SACxFf,EAAOrB,UATFqB,EAAOtB,QAalBrD,EAASmB,+BAA+B4G,SAAS,WAChDvC,EAAAA,EAAAA,KAAA,YACEwC,YAAazI,EAAE,eAAgB,oBAC/B8D,MAAOrD,EAASiI,0BAA4B,GAC5Cd,SAAWpB,GAAMvB,EAAkB,2BAA4BuB,EAAEqB,OAAO/D,OACxEoC,UAAU,4IACV8B,KAAK,YAKb/B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kEAAiEC,UAC7EF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,GAAGR,IAAKgD,IACvBvC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EwC,EAAI,QAEP1C,EAAAA,EAAAA,KAAA,YACEnC,MAAOrD,EAASoB,qBAAqB8G,EAAM,GAC3Cf,SAAWpB,IACT,MAAMoC,EAAkB,IAAInI,EAASoB,sBACrC+G,EAAgBD,EAAM,GAAKnC,EAAEqB,OAAO/D,MACpCmB,EAAkB,uBAAwB2D,IAE5C1C,UAAU,uIACV8B,KAAK,IACLS,YAAazI,EAAE,mBAAoB,2CAb7B2I,sBA0B1BvC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEnG,EAAE,mBAAoB,yBAGzBoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEnG,EAAE,iBAAkB,uBAGvBoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,QAAS,SAAS,SAEvBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,SACL3B,MAAOrD,EAASqB,eAAeC,MAC/B6F,SAAWpB,GAAM9F,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BD,GAAI,IACP9C,gBAAc+C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAK9C,gBAAc,IAAEC,MAAOyE,EAAEqB,OAAO/D,WAE5D+E,IAAI,IACJC,IAAI,KACJ5C,UAAS,8FAAArF,OACP4C,EAAOoD,eAAiB,iBAAmB,wCAE7CkB,UAAQ,IAETtE,EAAOoD,iBACNZ,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOoD,qBAIrDT,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB1F,EAASqB,eAAeE,MAAM2D,IAAI,CAACC,EAAMF,KACxCU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,6BAA4BC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,4DAA2DC,SAAA,CACxET,EAAQ,EAAE,QAEbO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACrBF,EAAAA,EAAAA,KAAA,YACEnC,MAAO8B,EACPgC,SAAWpB,GAAMhB,EAAiB,iBAAkBE,EAAOc,EAAEqB,OAAO/D,OACpE2E,YAAazI,EAAE,cAAe,mBAC9BkG,UAAU,kIACV8B,KAAK,QAGRvH,EAASqB,eAAeE,MAAMkF,OAAS,IACtCjB,EAAAA,EAAAA,KAAA,UACER,KAAK,SACLa,QAASA,IAAMP,EAAW,iBAAkBL,GAC5CQ,UAAU,yDAAwDC,UAElEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAnBTR,KAyBZU,EAAAA,EAAAA,MAAA,UACEX,KAAK,SACLa,QAASA,IAAMR,EAAQ,kBACvBI,UAAU,iFAAgFC,SAAA,EAE1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZlG,EAAE,UAAW,eAGfyD,EAAO3B,iBACNmE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAAsBC,SAAE1C,EAAO3B,wBAMlDsE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEnG,EAAE,gBAAiB,sBAGtBoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,QAAS,SAAS,SAEvBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,SACL3B,MAAOrD,EAASwB,cAAcF,MAC9B6F,SAAWpB,GAAM9F,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAC5BD,GAAI,IACP3C,eAAa4C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOD,EAAK3C,eAAa,IAAEF,MAAOyE,EAAEqB,OAAO/D,WAE1D+E,IAAI,IACJC,IAAI,KACJ5C,UAAS,8FAAArF,OACP4C,EAAOsD,cAAgB,iBAAmB,wCAE5CgB,UAAQ,IAETtE,EAAOsD,gBACNd,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOsD,oBAIrDX,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB1F,EAASwB,cAAcD,MAAM2D,IAAI,CAACC,EAAMF,KACvCU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,6BAA4BC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,4DAA2DC,SAAA,CACxET,EAAQ,EAAE,QAEbO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACrBF,EAAAA,EAAAA,KAAA,YACEnC,MAAO8B,EACPgC,SAAWpB,GAAMhB,EAAiB,gBAAiBE,EAAOc,EAAEqB,OAAO/D,OACnE2E,YAAazI,EAAE,cAAe,mBAC9BkG,UAAU,kIACV8B,KAAK,QAGRvH,EAASwB,cAAcD,MAAMkF,OAAS,IACrCjB,EAAAA,EAAAA,KAAA,UACER,KAAK,SACLa,QAASA,IAAMP,EAAW,gBAAiBL,GAC3CQ,UAAU,yDAAwDC,UAElEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAnBTR,KAyBZU,EAAAA,EAAAA,MAAA,UACEX,KAAK,SACLa,QAASA,IAAMR,EAAQ,iBACvBI,UAAU,iFAAgFC,SAAA,EAE1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZlG,EAAE,UAAW,eAGfyD,EAAOxB,gBACNgE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAAsBC,SAAE1C,EAAOxB,+BAUzC,IAAhB0B,IACCyC,EAAAA,EAAAA,MAAAuB,EAAAA,SAAA,CAAAxB,SAAA,EAEEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEnG,EAAE,gBAAiB,qBAGtBiG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,qEAAoEC,SAAA,EACnFF,EAAAA,EAAAA,KAAA,SAAAE,UACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,8BAA6BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oHAAmHC,SAC9HnG,EAAE,oBAAqB,yBAE1BiG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oHAAmHC,SAC9HnG,EAAE,qBAAsB,+BAI/BoG,EAAAA,EAAAA,MAAA,SAAAD,SAAA,CACGa,OAAO+B,QAAQ5E,GAAkBwB,IAAIqD,IAAA,IAAEC,EAAUC,GAAQF,EAAA,OACxD5C,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4GAA2GC,SACtHnG,EAAEiJ,EAAUA,EAASE,QAAQ,WAAY,OAAOA,QAAQ,KAAMC,GAAOA,EAAIC,mBAE5EpD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClE+C,EAAQvD,IAAKP,IAAM,IAAAkE,EAAA,OAClBlD,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,oBAAmBC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SACER,KAAK,WACLpC,GAAE,GAAAxC,OAAKoI,EAAQ,KAAApI,OAAIuE,EAAOtB,OAC1BuB,SAA2B,QAAlBiE,EAAA7I,EAASwI,UAAS,IAAAK,OAAA,EAAlBA,EAAoBd,SAASpD,EAAOtB,UAAU,EACvD8D,SAAWpB,GAAMrB,EAAqB8D,EAAU7D,EAAOtB,MAAO0C,EAAEqB,OAAOxC,SACvEa,UAAU,4EAEZD,EAAAA,EAAAA,KAAA,SAAOmC,QAAO,GAAAvH,OAAKoI,EAAQ,KAAApI,OAAIuE,EAAOtB,OAASoC,UAAU,2CAA0CC,SAChGf,EAAOrB,UATFqB,EAAOtB,eAPhBmF,MA0BX7C,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4GAA2GC,SACtHnG,EAAE,mBAAoB,wBAEzBiG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,SACER,KAAK,WACLpC,GAAG,mBACHgC,QAAS5E,EAASkC,iBAClBiF,SAAWpB,GAAMvB,EAAkB,mBAAoBuB,EAAEqB,OAAOxC,SAChEa,UAAU,4EAEZD,EAAAA,EAAAA,KAAA,SAAOmC,QAAQ,mBAAmBlC,UAAU,2CAA0CC,SACnFnG,EAAE,0BAA2B,iDAOtCoG,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4GAA2GC,SACtHnG,EAAE,SAAU,aAEfiG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,KAAA,YACEnC,MAAOrD,EAASmC,gBAChBgF,SAAWpB,GAAMvB,EAAkB,kBAAmBuB,EAAEqB,OAAO/D,OAC/D2E,YAAazI,EAAE,yBAA0B,+BACzCkG,UAAU,kIACV8B,KAAK,uBAUnB/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iGAAgGC,UAC7GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,SACER,KAAK,WACLpC,GAAG,eACHgC,QAAS5E,EAASoC,wBAClB+E,SAAWpB,GAAMvB,EAAkB,0BAA2BuB,EAAEqB,OAAOxC,SACvEa,UAAU,4EAEZD,EAAAA,EAAAA,KAAA,SAAOmC,QAAQ,eAAelC,UAAU,uDAAsDC,SAC3FnG,EAAE,0BAA2B,8CAMpCoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEnG,EAAE,aAAc,iBAGnBoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEnG,EAAE,qBAAsB,0BAG3BoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,YAAa,aAAa,SAE/BiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASqC,mBAChB8E,SAAWpB,GAAMvB,EAAkB,qBAAsBuB,EAAEqB,OAAO/D,OAClEoC,UAAS,8FAAArF,OACP4C,EAAOX,mBAAqB,iBAAmB,wCAEjDiF,UAAQ,IAETtE,EAAOX,qBACNmD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOX,yBAIrDsD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EnG,EAAE,cAAe,aAAa,SAEjCiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASsC,eAChB6E,SAAWpB,GAAMvB,EAAkB,iBAAkBuB,EAAEqB,OAAO/D,OAC9DoC,UAAS,8FAAArF,OACP4C,EAAOV,eAAiB,iBAAmB,wCAE7CgF,UAAQ,IAETtE,EAAOV,iBACNkD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE1C,EAAOV,qBAIrDqD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EnG,EAAE,OAAQ,WAEbiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASuC,cAChB4E,SAAWpB,GAAMvB,EAAkB,gBAAiBuB,EAAEqB,OAAO/D,OAC7DoC,UAAU,8IAOlBE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEnG,EAAE,kBAAmB,uBAGxBiG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAC3DnG,EAAE,sBAAuB,uFAI9BoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EnG,EAAE,qBAAsB,0BAE3BiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASwC,mBAChB2E,SAAWpB,GAAMvB,EAAkB,qBAAsBuB,EAAEqB,OAAO/D,OAClEoC,UAAU,wIAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EnG,EAAE,cAAe,gBAEpBiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAASyC,eAChB0E,SAAWpB,GAAMvB,EAAkB,iBAAkBuB,EAAEqB,OAAO/D,OAC9DoC,UAAU,wIAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EnG,EAAE,OAAQ,WAEbiG,EAAAA,EAAAA,KAAA,SACER,KAAK,OACL3B,MAAOrD,EAAS0C,cAChByE,SAAWpB,GAAMvB,EAAkB,gBAAiBuB,EAAEqB,OAAO/D,OAC7DoC,UAAU,uJAW1BE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,UACER,KAAK,SACLa,QAASA,IAAM/F,GAAU,GACzB2F,UAAU,4IAA2IC,SAEpJnG,EAAE,SAAU,aAGfoG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,CAC5BxC,EAAc,IACbsC,EAAAA,EAAAA,KAAA,UACER,KAAK,SACLa,QAASA,IAAM1C,EAAeD,EAAc,GAC5CuC,UAAU,4IAA2IC,SAEpJnG,EAAE,WAAY,cAIlB2D,EAAc,GACbsC,EAAAA,EAAAA,KAAA,UACER,KAAK,SACLa,QAASA,IAAM1C,EAAeD,EAAc,GAC5CuC,UAAU,gEAA+DC,SAExEnG,EAAE,OAAQ,WAGboG,EAAAA,EAAAA,MAAAuB,EAAAA,SAAA,CAAAxB,SAAA,EACEC,EAAAA,EAAAA,MAAA,UACEX,KAAK,SACLa,QAv1BIhC,UAClB,IACEd,GAAW,GAEX,MAAM+F,GAAO1E,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRpE,GAAQ,IACX+I,aAAa,IAAI1I,MAAOG,cACxBwI,YAAatJ,EAAK2E,MAAQ3E,EAAKuJ,MAC/BrJ,UAAWA,IAGPkE,QAAiBC,MAAM,mCAAoC,CAC/D4C,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADxG,OAAY8I,aAAaC,QAAQ,WAElDtC,KAAMC,KAAKC,UAAU+B,KAGvB,IAAIhF,EAASE,GAaX,MAAM,IAAIgD,MAAM,uBAAD5G,OAAwB0D,EAASsF,SAbjC,CACf,MAAMC,QAAavF,EAASuF,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,wBAAA1J,OAA2BJ,EAASY,YAAY8H,QAAQ,OAAQ,KAAI,KAAAtI,OAAIJ,EAASO,UAAS,QACpGoJ,SAAS9C,KAAKkD,YAAYL,GAC1BA,EAAEM,QACFT,OAAOC,IAAIS,gBAAgBX,GAC3BK,SAAS9C,KAAKqD,YAAYR,GAE1BzC,MAAM1H,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAO+E,GACPC,QAAQD,MAAM,wBAAyBA,GACvC2C,MAAM1H,EAAE,qBAAsB,2CAChC,CAAC,QACCwD,GAAW,EACb,GAgzBcoH,SAAUrH,EACV2C,UAAU,gIAA+HC,SAAA,EAEzIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ3C,EAAUvD,EAAE,aAAc,iBAAmBA,EAAE,cAAe,oBAGjEiG,EAAAA,EAAAA,KAAA,UACER,KAAK,SACLmF,SAAUrH,EACV2C,UAAU,gHAA+GC,SAExH5C,EAAUvD,EAAE,SAAU,aAAeA,EAAE,iBAAkB,sC", "sources": ["pages/Forms/InitialPlanOfCareForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nconst InitialPlanOfCareForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [formData, setFormData] = useState({\n    // Document Metadata\n    documentNumber: `QP-${Date.now()}`,\n    issueDate: new Date().toISOString().split('T')[0],\n    version: '01',\n    reviewNumber: '01',\n    \n    // Patient Information\n    patientName: '',\n    mrNumber: '',\n    diagnosis: '',\n    onsetDate: '',\n    physician: '',\n    \n    // Patient Problems\n    functionalProblems: [],\n    functionalImpairments: [],\n    functionalActivityRestrictions: [],\n    specificDescriptions: ['', '', '', '', '', ''],\n    \n    // Goals\n    shortTermGoals: {\n      weeks: '',\n      goals: ['']\n    },\n    longTermGoals: {\n      weeks: '',\n      goals: ['']\n    },\n    \n    // Treatment Plan\n    painControl: [],\n    reduceSwelling: [],\n    improveROM: [],\n    improveFlexibility: [],\n    muscleStrengthening: [],\n    posturalCorrection: [],\n    improveBalance: [],\n    improveEndurance: [],\n    gaitTraining: [],\n    homeInstructions: false,\n    otherTreatments: '',\n    \n    // Review and Signatures\n    planReviewedWithPatient: false,\n    therapistSignature: '',\n    therapistBadge: '',\n    therapistDate: new Date().toISOString().split('T')[0],\n    physicianSignature: '',\n    physicianBadge: '',\n    physicianDate: '',\n    \n    // Metadata\n    submittedBy: user?.id || '',\n    submittedAt: new Date().toISOString()\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [currentPage, setCurrentPage] = useState(1);\n\n  // Problem options with R/L/B support\n  const functionalProblemsOptions = [\n    { value: 'pain', label: t('pain', 'Pain'), hasLaterality: false },\n    { value: 'bed_mobility', label: t('bedMobility', '↓ Bed/mat mobility status'), hasLaterality: false },\n    { value: 'transfer_status', label: t('transferStatus', '↓ Transfer status'), hasLaterality: false },\n    { value: 'ue_rom', label: t('ueROM', 'Limited UE ROM'), hasLaterality: true },\n    { value: 'le_rom', label: t('leROM', 'Limited LE ROM'), hasLaterality: true },\n    { value: 'le_strength', label: t('leStrength', '↓ LE strength'), hasLaterality: true },\n    { value: 'ue_strength', label: t('ueStrength', '↓ UE strength'), hasLaterality: true },\n    { value: 'neck_trunk_strength', label: t('neckTrunkStrength', '↓ Neck/trunk strength'), hasLaterality: false },\n    { value: 'abnormal_tone', label: t('abnormalTone', 'Abnormal tone'), hasLaterality: false },\n    { value: 'abnormal_movement', label: t('abnormalMovement', 'Abnormal movement'), hasLaterality: false },\n    { value: 'skin_breakdown', label: t('skinBreakdown', 'Skin breakdown'), hasLaterality: false },\n    { value: 'gait_asymmetry', label: t('gaitAsymmetry', 'Gait asymmetry altered Gait pattern'), hasLaterality: false }\n  ];\n\n  const functionalImpairmentsOptions = [\n    { value: 'atrophy', label: t('atrophy', 'Atrophy') },\n    { value: 'muscle_weakness', label: t('muscleWeakness', 'Muscle weakness') },\n    { value: 'imbalance', label: t('imbalance', 'Imbalance/poor balance') },\n    { value: 'lack_coordination', label: t('lackCoordination', 'Lack of coordination') },\n    { value: 'visual_perception', label: t('visualPerception', '↓ Visual perception') },\n    { value: 'soft_tissue_dysfunction', label: t('softTissueDysfunction', 'Soft tissue dysfunction') },\n    { value: 'poor_posture', label: t('poorPosture', 'Poor posture/abnormal posture') },\n    { value: 'improper_body_mechanics', label: t('improperBodyMechanics', 'Improper body mechanics') },\n    { value: 'wc_mobility', label: t('wcMobility', '↓ W/C mobility') },\n    { value: 'difficulty_ambulating', label: t('difficultyAmbulating', 'Difficulty ambulating') },\n    { value: 'abnormal_gait', label: t('abnormalGait', 'Abnormal gait') }\n  ];\n\n  const functionalActivityRestrictionsOptions = [\n    { value: 'endurance', label: t('endurance', '↓ Endurance') },\n    { value: 'sensation', label: t('sensation', '↓ Sensation/proprioception') },\n    { value: 'respiratory', label: t('respiratory', '↓ Respiratory capacity') },\n    { value: 'fine_motor', label: t('fineMotor', '↓ Fine motor/dexterity') },\n    { value: 'functional_activity', label: t('functionalActivity', 'Decreased functional activity: ADL/work skills') },\n    { value: 'joint_hypomobility', label: t('jointHypomobility', 'Joint hypomobility') },\n    { value: 'joint_hypermobility', label: t('jointHypermobility', 'Joint hypermobility') },\n    { value: 'contracture', label: t('contracture', 'Contracture') },\n    { value: 'other', label: t('other', 'Other') }\n  ];\n\n  // Treatment options\n  const treatmentOptions = {\n    painControl: [\n      { value: 'us', label: t('ultrasound', 'US') },\n      { value: 'laser', label: t('laser', 'LASER') },\n      { value: 'tens', label: t('tens', 'TENS') },\n      { value: 'thermal', label: t('thermal', 'Thermal') }\n    ],\n    reduceSwelling: [\n      { value: 'cryotherapy', label: t('cryotherapy', 'Cryotherapy') },\n      { value: 'hvc', label: t('hvc', 'HVC') },\n      { value: 'compression', label: t('compression', 'Compression') }\n    ],\n    improveROM: [\n      { value: 'prom', label: t('prom', 'PROM') },\n      { value: 'mobilization', label: t('mobilization', 'Mobilization') },\n      { value: 'met', label: t('met', 'MET') }\n    ],\n    improveFlexibility: [\n      { value: 'stretching', label: t('stretching', 'Stretching') },\n      { value: 'thermal_flex', label: t('thermal', 'Thermal') },\n      { value: 'myofascial', label: t('myofascial', 'Myofascial release') }\n    ],\n    muscleStrengthening: [\n      { value: 'isometric', label: t('isometric', 'Isometric') },\n      { value: 'active_assisted', label: t('activeAssisted', 'Active Assisted') },\n      { value: 'active_resisted', label: t('activeResisted', 'Active Resisted') },\n      { value: 'core_strengthening', label: t('coreStrengthening', 'Core Strengthening') },\n      { value: 'plyometrics', label: t('plyometrics', 'Plyometrics') },\n      { value: 'fes', label: t('fes', 'FES') },\n      { value: 'pnf', label: t('pnf', 'PNF') }\n    ],\n    posturalCorrection: [\n      { value: 'body_mechanics', label: t('bodyMechanics', 'Proper Body Mechanics') },\n      { value: 'ergonomics', label: t('ergonomics', 'Ergonomics') },\n      { value: 'tilt_table', label: t('tiltTable', 'Tilt table') }\n    ],\n    improveBalance: [\n      { value: 'frenkels', label: t('frenkels', \"Frenkel's Ex\") },\n      { value: 'balance_board', label: t('balanceBoard', 'Balance Board') },\n      { value: 'agility', label: t('agility', \"Agility Ex's\") },\n      { value: 'proprioception', label: t('proprioception', 'Proprioception Training') },\n      { value: 'lumbopelvic', label: t('lumbopelvic', 'Lumbopelvic Rhythm') }\n    ],\n    improveEndurance: [\n      { value: 'aerobic', label: t('aerobic', \"Aerobic Ex's\") },\n      { value: 'bicycle', label: t('bicycle', 'Bicycle') },\n      { value: 'treadmill', label: t('treadmill', 'Treadmill') }\n    ],\n    gaitTraining: [\n      { value: 'normal_gait', label: t('normalGait', 'Normal Gait Pattern') },\n      { value: 'fwb', label: t('fwb', 'Weight Bearing: FWB') },\n      { value: 'pwb', label: t('pwb', 'PWB') },\n      { value: 'wb', label: t('wb', 'WB') }\n    ]\n  };\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        setFormData(prev => ({\n          ...prev,\n          patientName: patientData.name || '',\n          mrNumber: patientData.mrNumber || '',\n          diagnosis: patientData.diagnosis || ''\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleCheckboxChange = (field, option, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: checked \n        ? [...prev[field], option]\n        : prev[field].filter(item => item !== option)\n    }));\n  };\n\n  const handleGoalChange = (type, index, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [type]: {\n        ...prev[type],\n        goals: prev[type].goals.map((goal, i) => i === index ? value : goal)\n      }\n    }));\n  };\n\n  const addGoal = (type) => {\n    setFormData(prev => ({\n      ...prev,\n      [type]: {\n        ...prev[type],\n        goals: [...prev[type].goals, '']\n      }\n    }));\n  };\n\n  const removeGoal = (type, index) => {\n    setFormData(prev => ({\n      ...prev,\n      [type]: {\n        ...prev[type],\n        goals: prev[type].goals.filter((_, i) => i !== index)\n      }\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.patientName.trim()) newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    if (!formData.mrNumber.trim()) newErrors.mrNumber = t('mrNumberRequired', 'MR number is required');\n    if (!formData.diagnosis.trim()) newErrors.diagnosis = t('diagnosisRequired', 'Diagnosis is required');\n    if (!formData.onsetDate) newErrors.onsetDate = t('onsetDateRequired', 'Onset date is required');\n    if (!formData.physician.trim()) newErrors.physician = t('physicianRequired', 'Physician is required');\n\n    // Goals validation\n    if (!formData.shortTermGoals.weeks) newErrors.shortTermWeeks = t('shortTermWeeksRequired', 'Short term weeks required');\n    if (formData.shortTermGoals.goals.every(goal => !goal.trim())) {\n      newErrors.shortTermGoals = t('shortTermGoalsRequired', 'At least one short term goal is required');\n    }\n\n    if (!formData.longTermGoals.weeks) newErrors.longTermWeeks = t('longTermWeeksRequired', 'Long term weeks required');\n    if (formData.longTermGoals.goals.every(goal => !goal.trim())) {\n      newErrors.longTermGoals = t('longTermGoalsRequired', 'At least one long term goal is required');\n    }\n\n    // Signature validation\n    if (!formData.therapistSignature.trim()) newErrors.therapistSignature = t('therapistSignatureRequired', 'Therapist signature is required');\n    if (!formData.therapistBadge.trim()) newErrors.therapistBadge = t('therapistBadgeRequired', 'Therapist badge number is required');\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n      \n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/initial-plan-of-care/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `initial-plan-of-care-${formData.patientName.replace(/\\s+/g, '-')}-${formData.issueDate}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n        \n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      const submissionData = {\n        ...formData,\n        submittedBy: user.id,\n        submittedAt: new Date().toISOString()\n      };\n\n      const response = await fetch('/api/v1/initial-plan-of-care/public', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        alert(t('planOfCareSaved', 'Initial Plan of Care saved successfully!'));\n        navigate(patientId ? `/patients/${patientId}` : '/patients');\n      } else {\n        throw new Error('Failed to save plan of care');\n      }\n    } catch (error) {\n      console.error('Error saving plan of care:', error);\n      alert(t('errorSaving', 'Error saving plan of care. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('initialPlanOfCare', 'Initial Plan of Care Physical Therapy')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                {t('planOfCareDescription', '2-page comprehensive treatment planning document')}\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm rounded-full\">\n                <i className=\"fas fa-clipboard-list mr-1\"></i>\n                {t('planOfCare', 'Plan of Care')}\n              </span>\n              <span className=\"px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm rounded-full\">\n                {t('page', 'Page')} {currentPage}/2\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Page Navigation */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n            {[\n              { id: 1, label: t('patientProblemsGoals', 'Patient Problems & Goals') },\n              { id: 2, label: t('treatmentPlanSignatures', 'Treatment Plan & Signatures') }\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setCurrentPage(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  currentPage === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Page 1: Patient Information and Problems */}\n        {currentPage === 1 && (\n          <>\n            {/* Document Header */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                <div>\n                  <label className=\"block font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('documentNumber', 'Document Number')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.documentNumber}\n                    onChange={(e) => handleInputChange('documentNumber', e.target.value)}\n                    className=\"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    readOnly\n                  />\n                </div>\n                <div>\n                  <label className=\"block font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('issueDate', 'Issue Date')}\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.issueDate}\n                    onChange={(e) => handleInputChange('issueDate', e.target.value)}\n                    className=\"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('version', 'Version')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.version}\n                    className=\"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white\"\n                    readOnly\n                  />\n                </div>\n                <div>\n                  <label className=\"block font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('reviewNumber', 'Review Number')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.reviewNumber}\n                    className=\"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white\"\n                    readOnly\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Patient Information */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                {t('patientInformation', 'Patient Information')}\n              </h2>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('patientName', 'Patient Name')} *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.patientName}\n                    onChange={(e) => handleInputChange('patientName', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.patientName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                    required\n                  />\n                  {errors.patientName && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('mrNumber', 'MR #')} *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.mrNumber}\n                    onChange={(e) => handleInputChange('mrNumber', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.mrNumber ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                    required\n                  />\n                  {errors.mrNumber && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.mrNumber}</p>\n                  )}\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('diagnosis', 'Diagnosis')} *\n                  </label>\n                  <textarea\n                    value={formData.diagnosis}\n                    onChange={(e) => handleInputChange('diagnosis', e.target.value)}\n                    rows=\"3\"\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.diagnosis ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                    required\n                  />\n                  {errors.diagnosis && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.diagnosis}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('onsetDate', 'Onset Date')} *\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.onsetDate}\n                    onChange={(e) => handleInputChange('onsetDate', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.onsetDate ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                    required\n                  />\n                  {errors.onsetDate && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.onsetDate}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('physician', 'Physician')} *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.physician}\n                    onChange={(e) => handleInputChange('physician', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.physician ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                    required\n                  />\n                  {errors.physician && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.physician}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Patient Problems Table */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                {t('patientProblems', 'Patient Problems (Reason for Referral)')}\n              </h2>\n\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full border-collapse border border-gray-300 dark:border-gray-600\">\n                  <thead>\n                    <tr className=\"bg-gray-50 dark:bg-gray-700\">\n                      <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                        {t('functionalProblems', 'Functional Problems')}\n                      </th>\n                      <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                        {t('functionalImpairments', 'Functional Impairments')}\n                      </th>\n                      <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                        {t('functionalActivityRestrictions', 'Functional Activity Restrictions')}\n                      </th>\n                      <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                        {t('specificDescription', 'Specific Description / Objective Measure')}\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top\">\n                        <div className=\"space-y-2\">\n                          {functionalProblemsOptions.map((option) => (\n                            <div key={option.value} className=\"flex items-start\">\n                              <input\n                                type=\"checkbox\"\n                                id={`fp-${option.value}`}\n                                checked={formData.functionalProblems.some(p => p.value === option.value)}\n                                onChange={(e) => {\n                                  if (e.target.checked) {\n                                    handleCheckboxChange('functionalProblems', { value: option.value, label: option.label, laterality: option.hasLaterality ? 'B' : null }, true);\n                                  } else {\n                                    setFormData(prev => ({\n                                      ...prev,\n                                      functionalProblems: prev.functionalProblems.filter(p => p.value !== option.value)\n                                    }));\n                                  }\n                                }}\n                                className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                              />\n                              <label htmlFor={`fp-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                                {option.label}\n                              </label>\n                              {option.hasLaterality && formData.functionalProblems.some(p => p.value === option.value) && (\n                                <div className=\"ml-4 flex space-x-2\">\n                                  {['R', 'L', 'B'].map((side) => (\n                                    <label key={side} className=\"flex items-center\">\n                                      <input\n                                        type=\"radio\"\n                                        name={`laterality-${option.value}`}\n                                        value={side}\n                                        checked={formData.functionalProblems.find(p => p.value === option.value)?.laterality === side}\n                                        onChange={(e) => {\n                                          setFormData(prev => ({\n                                            ...prev,\n                                            functionalProblems: prev.functionalProblems.map(p =>\n                                              p.value === option.value ? { ...p, laterality: e.target.value } : p\n                                            )\n                                          }));\n                                        }}\n                                        className=\"mr-1 h-3 w-3 text-blue-600\"\n                                      />\n                                      <span className=\"text-xs text-gray-600 dark:text-gray-400\">{side}</span>\n                                    </label>\n                                  ))}\n                                </div>\n                              )}\n                            </div>\n                          ))}\n                        </div>\n                      </td>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top\">\n                        <div className=\"space-y-2\">\n                          {functionalImpairmentsOptions.map((option) => (\n                            <div key={option.value} className=\"flex items-start\">\n                              <input\n                                type=\"checkbox\"\n                                id={`fi-${option.value}`}\n                                checked={formData.functionalImpairments.includes(option.value)}\n                                onChange={(e) => handleCheckboxChange('functionalImpairments', option.value, e.target.checked)}\n                                className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                              />\n                              <label htmlFor={`fi-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                                {option.label}\n                              </label>\n                            </div>\n                          ))}\n                        </div>\n                      </td>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top\">\n                        <div className=\"space-y-2\">\n                          {functionalActivityRestrictionsOptions.map((option) => (\n                            <div key={option.value} className=\"flex items-start\">\n                              <input\n                                type=\"checkbox\"\n                                id={`far-${option.value}`}\n                                checked={formData.functionalActivityRestrictions.includes(option.value)}\n                                onChange={(e) => handleCheckboxChange('functionalActivityRestrictions', option.value, e.target.checked)}\n                                className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                              />\n                              <label htmlFor={`far-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                                {option.label}\n                              </label>\n                            </div>\n                          ))}\n                          {formData.functionalActivityRestrictions.includes('other') && (\n                            <textarea\n                              placeholder={t('specifyOther', 'Specify other...')}\n                              value={formData.otherActivityRestriction || ''}\n                              onChange={(e) => handleInputChange('otherActivityRestriction', e.target.value)}\n                              className=\"w-full mt-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                              rows=\"2\"\n                            />\n                          )}\n                        </div>\n                      </td>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top\">\n                        <div className=\"space-y-3\">\n                          {[1, 2, 3, 4, 5, 6].map((num) => (\n                            <div key={num}>\n                              <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                                {num}.\n                              </label>\n                              <textarea\n                                value={formData.specificDescriptions[num - 1]}\n                                onChange={(e) => {\n                                  const newDescriptions = [...formData.specificDescriptions];\n                                  newDescriptions[num - 1] = e.target.value;\n                                  handleInputChange('specificDescriptions', newDescriptions);\n                                }}\n                                className=\"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                                rows=\"2\"\n                                placeholder={t('objectiveMeasure', 'Objective measure or description...')}\n                              />\n                            </div>\n                          ))}\n                        </div>\n                      </td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            {/* Goals of Treatment */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                {t('goalsOfTreatment', 'Goals of Treatment')}\n              </h2>\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Short Term Goals */}\n                <div>\n                  <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                    {t('shortTermGoals', 'Short Term Goals')}\n                  </h3>\n\n                  <div className=\"mb-4\">\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('weeks', 'Weeks')} *\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={formData.shortTermGoals.weeks}\n                      onChange={(e) => setFormData(prev => ({\n                        ...prev,\n                        shortTermGoals: { ...prev.shortTermGoals, weeks: e.target.value }\n                      }))}\n                      min=\"1\"\n                      max=\"52\"\n                      className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                        errors.shortTermWeeks ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                      }`}\n                      required\n                    />\n                    {errors.shortTermWeeks && (\n                      <p className=\"text-red-500 text-sm mt-1\">{errors.shortTermWeeks}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    {formData.shortTermGoals.goals.map((goal, index) => (\n                      <div key={index} className=\"flex items-start space-x-2\">\n                        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mt-2\">\n                          {index + 1}.\n                        </span>\n                        <div className=\"flex-1\">\n                          <textarea\n                            value={goal}\n                            onChange={(e) => handleGoalChange('shortTermGoals', index, e.target.value)}\n                            placeholder={t('patientWill', 'Patient will...')}\n                            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                            rows=\"2\"\n                          />\n                        </div>\n                        {formData.shortTermGoals.goals.length > 1 && (\n                          <button\n                            type=\"button\"\n                            onClick={() => removeGoal('shortTermGoals', index)}\n                            className=\"mt-2 text-red-600 hover:text-red-800 dark:text-red-400\"\n                          >\n                            <i className=\"fas fa-trash text-sm\"></i>\n                          </button>\n                        )}\n                      </div>\n                    ))}\n\n                    <button\n                      type=\"button\"\n                      onClick={() => addGoal('shortTermGoals')}\n                      className=\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm\"\n                    >\n                      <i className=\"fas fa-plus mr-1\"></i>\n                      {t('addGoal', 'Add Goal')}\n                    </button>\n\n                    {errors.shortTermGoals && (\n                      <p className=\"text-red-500 text-sm\">{errors.shortTermGoals}</p>\n                    )}\n                  </div>\n                </div>\n\n                {/* Long Term Goals */}\n                <div>\n                  <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                    {t('longTermGoals', 'Long Term Goals')}\n                  </h3>\n\n                  <div className=\"mb-4\">\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('weeks', 'Weeks')} *\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={formData.longTermGoals.weeks}\n                      onChange={(e) => setFormData(prev => ({\n                        ...prev,\n                        longTermGoals: { ...prev.longTermGoals, weeks: e.target.value }\n                      }))}\n                      min=\"1\"\n                      max=\"52\"\n                      className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                        errors.longTermWeeks ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                      }`}\n                      required\n                    />\n                    {errors.longTermWeeks && (\n                      <p className=\"text-red-500 text-sm mt-1\">{errors.longTermWeeks}</p>\n                    )}\n                  </div>\n\n                  <div className=\"space-y-3\">\n                    {formData.longTermGoals.goals.map((goal, index) => (\n                      <div key={index} className=\"flex items-start space-x-2\">\n                        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mt-2\">\n                          {index + 1}.\n                        </span>\n                        <div className=\"flex-1\">\n                          <textarea\n                            value={goal}\n                            onChange={(e) => handleGoalChange('longTermGoals', index, e.target.value)}\n                            placeholder={t('patientWill', 'Patient will...')}\n                            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                            rows=\"2\"\n                          />\n                        </div>\n                        {formData.longTermGoals.goals.length > 1 && (\n                          <button\n                            type=\"button\"\n                            onClick={() => removeGoal('longTermGoals', index)}\n                            className=\"mt-2 text-red-600 hover:text-red-800 dark:text-red-400\"\n                          >\n                            <i className=\"fas fa-trash text-sm\"></i>\n                          </button>\n                        )}\n                      </div>\n                    ))}\n\n                    <button\n                      type=\"button\"\n                      onClick={() => addGoal('longTermGoals')}\n                      className=\"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm\"\n                    >\n                      <i className=\"fas fa-plus mr-1\"></i>\n                      {t('addGoal', 'Add Goal')}\n                    </button>\n\n                    {errors.longTermGoals && (\n                      <p className=\"text-red-500 text-sm\">{errors.longTermGoals}</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Page 2: Treatment Plan and Signatures */}\n        {currentPage === 2 && (\n          <>\n            {/* Treatment Plan Table */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                {t('treatmentPlan', 'Treatment Plan')}\n              </h2>\n\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full border-collapse border border-gray-300 dark:border-gray-600\">\n                  <thead>\n                    <tr className=\"bg-gray-50 dark:bg-gray-700\">\n                      <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                        {t('treatmentCategory', 'Treatment Category')}\n                      </th>\n                      <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                        {t('specificTreatments', 'Specific Treatments')}\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {Object.entries(treatmentOptions).map(([category, options]) => (\n                      <tr key={category}>\n                        <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top font-medium text-gray-900 dark:text-white\">\n                          {t(category, category.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()))}\n                        </td>\n                        <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3\">\n                          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">\n                            {options.map((option) => (\n                              <div key={option.value} className=\"flex items-center\">\n                                <input\n                                  type=\"checkbox\"\n                                  id={`${category}-${option.value}`}\n                                  checked={formData[category]?.includes(option.value) || false}\n                                  onChange={(e) => handleCheckboxChange(category, option.value, e.target.checked)}\n                                  className=\"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                />\n                                <label htmlFor={`${category}-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                                  {option.label}\n                                </label>\n                              </div>\n                            ))}\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n\n                    {/* Home Instructions */}\n                    <tr>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top font-medium text-gray-900 dark:text-white\">\n                        {t('homeInstructions', 'Home Instructions')}\n                      </td>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3\">\n                        <div className=\"flex items-center\">\n                          <input\n                            type=\"checkbox\"\n                            id=\"homeInstructions\"\n                            checked={formData.homeInstructions}\n                            onChange={(e) => handleInputChange('homeInstructions', e.target.checked)}\n                            className=\"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                          />\n                          <label htmlFor=\"homeInstructions\" className=\"text-sm text-gray-700 dark:text-gray-300\">\n                            {t('provideHomeInstructions', 'Provide home exercise instructions')}\n                          </label>\n                        </div>\n                      </td>\n                    </tr>\n\n                    {/* Others */}\n                    <tr>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top font-medium text-gray-900 dark:text-white\">\n                        {t('others', 'Others')}\n                      </td>\n                      <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-3\">\n                        <textarea\n                          value={formData.otherTreatments}\n                          onChange={(e) => handleInputChange('otherTreatments', e.target.value)}\n                          placeholder={t('specifyOtherTreatments', 'Specify other treatments...')}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                          rows=\"3\"\n                        />\n                      </td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n\n            {/* Plan Review */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"planReviewed\"\n                  checked={formData.planReviewedWithPatient}\n                  onChange={(e) => handleInputChange('planReviewedWithPatient', e.target.checked)}\n                  className=\"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"planReviewed\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {t('planReviewedWithPatient', 'Plan of Care Reviewed with Patient')}\n                </label>\n              </div>\n            </div>\n\n            {/* Signatures */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                {t('signatures', 'Signatures')}\n              </h2>\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Therapist Signature */}\n                <div>\n                  <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                    {t('therapistSignature', 'Therapist Signature')}\n                  </h3>\n\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('signature', 'Signature')} *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.therapistSignature}\n                        onChange={(e) => handleInputChange('therapistSignature', e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                          errors.therapistSignature ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                        }`}\n                        required\n                      />\n                      {errors.therapistSignature && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors.therapistSignature}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('badgeNumber', 'Badge No.')} *\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.therapistBadge}\n                        onChange={(e) => handleInputChange('therapistBadge', e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                          errors.therapistBadge ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                        }`}\n                        required\n                      />\n                      {errors.therapistBadge && (\n                        <p className=\"text-red-500 text-sm mt-1\">{errors.therapistBadge}</p>\n                      )}\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('date', 'Date')}\n                      </label>\n                      <input\n                        type=\"date\"\n                        value={formData.therapistDate}\n                        onChange={(e) => handleInputChange('therapistDate', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Physician Review */}\n                <div>\n                  <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                    {t('physicianReview', 'Physician Review')}\n                  </h3>\n\n                  <div className=\"mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <p className=\"text-sm text-gray-700 dark:text-gray-300 italic\">\n                      {t('physicianReviewText', 'Have reviewed this plan of care and re-certify a continuing need for services.')}\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('physicianSignature', 'Physician Signature')}\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.physicianSignature}\n                        onChange={(e) => handleInputChange('physicianSignature', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('badgeNumber', 'Badge No.')}\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={formData.physicianBadge}\n                        onChange={(e) => handleInputChange('physicianBadge', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('date', 'Date')}\n                      </label>\n                      <input\n                        type=\"date\"\n                        value={formData.physicianDate}\n                        onChange={(e) => handleInputChange('physicianDate', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between\">\n          <button\n            type=\"button\"\n            onClick={() => navigate(-1)}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          \n          <div className=\"flex space-x-3\">\n            {currentPage > 1 && (\n              <button\n                type=\"button\"\n                onClick={() => setCurrentPage(currentPage - 1)}\n                className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\"\n              >\n                {t('previous', 'Previous')}\n              </button>\n            )}\n            \n            {currentPage < 2 ? (\n              <button\n                type=\"button\"\n                onClick={() => setCurrentPage(currentPage + 1)}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                {t('next', 'Next')}\n              </button>\n            ) : (\n              <>\n                <button\n                  type=\"button\"\n                  onClick={generatePDF}\n                  disabled={loading}\n                  className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n                >\n                  <i className=\"fas fa-file-pdf mr-2\"></i>\n                  {loading ? t('generating', 'Generating...') : t('generatePDF', 'Generate PDF')}\n                </button>\n                \n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {loading ? t('saving', 'Saving...') : t('savePlanOfCare', 'Save Plan of Care')}\n                </button>\n              </>\n            )}\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default InitialPlanOfCareForm;\n"], "names": ["InitialPlanOfCareForm", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "formData", "setFormData", "useState", "documentNumber", "concat", "Date", "now", "issueDate", "toISOString", "split", "version", "reviewNumber", "patientName", "mr<PERSON><PERSON><PERSON>", "diagnosis", "onsetDate", "physician", "functionalProblems", "functionalImpairments", "functionalActivityRestrictions", "specificDescriptions", "shortTermGoals", "weeks", "goals", "longTermGoals", "painControl", "reduceSwelling", "improveROM", "improveFlexibility", "muscleStrengthening", "posturalCorrection", "improveBalance", "improveEndurance", "gaitTraining", "homeInstructions", "otherTreatments", "planReviewedWithPatient", "therapistSignature", "therapistBadge", "therapistDate", "physicianSignature", "<PERSON><PERSON><PERSON><PERSON>", "physicianDate", "submittedBy", "id", "submittedAt", "loading", "setLoading", "errors", "setErrors", "currentPage", "setCurrentPage", "functionalProblemsOptions", "value", "label", "hasLaterality", "functionalImpairmentsOptions", "functionalActivityRestrictionsOptions", "treatmentOptions", "useEffect", "loadPatientData", "async", "response", "fetch", "ok", "patientData", "json", "prev", "_objectSpread", "name", "error", "console", "handleInputChange", "field", "handleCheckboxChange", "option", "checked", "filter", "item", "handleGoalChange", "type", "index", "map", "goal", "i", "addGoal", "removeGoal", "_", "_jsx", "className", "children", "_jsxs", "tab", "onClick", "onSubmit", "e", "preventDefault", "validateForm", "newErrors", "trim", "shortTermWeeks", "every", "longTermWeeks", "Object", "keys", "length", "submissionData", "method", "headers", "body", "JSON", "stringify", "Error", "alert", "_Fragment", "onChange", "target", "readOnly", "required", "rows", "some", "p", "laterality", "htmlFor", "side", "_formData$functionalP", "find", "includes", "placeholder", "otherActivityRestriction", "num", "newDescriptions", "min", "max", "entries", "_ref", "category", "options", "replace", "str", "toUpperCase", "_formData$category", "pdfData", "generatedAt", "generatedBy", "email", "localStorage", "getItem", "status", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "disabled"], "sourceRoot": ""}