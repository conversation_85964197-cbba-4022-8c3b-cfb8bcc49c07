"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[682],{682:(e,s,t)=>{t.r(s),t.d(s,{default:()=>d});var a=t(2555),r=t(5043),i=t(4117),n=t(4528),l=t(3768),c=t(579);const d=()=>{const[e,s]=(0,r.useState)({nphies:{status:"disconnected",totalClaims:0,successfulClaims:0,eligibilityChecks:0,lastSync:null},zatca:{status:"disconnected",totalInvoices:0,successfulSubmissions:0,complianceScore:0,lastSync:null},electronicSignatures:{totalDocuments:0,pendingSignatures:0,completedSignatures:0,expiredDocuments:0},visaIntegration:{linkedVisas:0,verifiedVisas:0,expiredVisas:0}}),[t,d]=(0,r.useState)(!0),{t:x}=(0,i.Bd)(),{user:m}=(0,n.A)();(0,r.useEffect)(()=>{o()},[]);const o=async()=>{d(!0);try{const t=await fetch("/api/v1/nphies/stats",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}),r=await fetch("/api/v1/zatca/compliance",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}),i=(await fetch("/api/v1/signature/stats",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}),(0,a.A)({},e));if(t.ok){const e=await t.json();i.nphies=(0,a.A)({status:"connected"},e.data)}if(r.ok){const e=await r.json();i.zatca={status:"connected",totalInvoices:e.data.totalInvoicesSubmitted,successfulSubmissions:e.data.successfulSubmissions,complianceScore:e.data.overallCompliance,lastSync:e.data.lastSyncDate}}i.electronicSignatures={totalDocuments:45,pendingSignatures:8,completedSignatures:37,expiredDocuments:2},i.visaIntegration={linkedVisas:23,verifiedVisas:21,expiredVisas:2},s(i)}catch(t){console.error("Error loading integration stats:",t),l.Ay.error("Failed to load integration statistics")}finally{d(!1)}},g=e=>{switch(e){case"connected":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"warning":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"disconnected":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}},u=e=>{switch(e){case"connected":return"fas fa-check-circle";case"warning":return"fas fa-exclamation-triangle";case"disconnected":return"fas fa-times-circle";default:return"fas fa-question-circle"}};return t?(0,c.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,c.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,c.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,c.jsxs)("div",{className:"mb-8",children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:x("integrationDashboard","Integration Dashboard")}),(0,c.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:x("integrationDashboardDesc","Monitor and manage Saudi regulatory integrations including NPHIES, ZATCA, and electronic signatures")})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-shield-alt text-2xl text-blue-600 dark:text-blue-400 mr-3"}),(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"NPHIES"})]}),(0,c.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(g(e.nphies.status)),children:[(0,c.jsx)("i",{className:"".concat(u(e.nphies.status)," mr-1")}),x(e.nphies.status,e.nphies.status)]})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("totalClaims","Total Claims")}),(0,c.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.nphies.totalClaims})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("successful","Successful")}),(0,c.jsx)("span",{className:"text-sm font-medium text-green-600",children:e.nphies.successfulClaims})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("eligibilityChecks","Eligibility Checks")}),(0,c.jsx)("span",{className:"text-sm font-medium text-blue-600",children:e.nphies.eligibilityChecks})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-receipt text-2xl text-green-600 dark:text-green-400 mr-3"}),(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"ZATCA"})]}),(0,c.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(g(e.zatca.status)),children:[(0,c.jsx)("i",{className:"".concat(u(e.zatca.status)," mr-1")}),x(e.zatca.status,e.zatca.status)]})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("totalInvoices","Total Invoices")}),(0,c.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.zatca.totalInvoices})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("successful","Successful")}),(0,c.jsx)("span",{className:"text-sm font-medium text-green-600",children:e.zatca.successfulSubmissions})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("compliance","Compliance")}),(0,c.jsxs)("span",{className:"text-sm font-medium text-blue-600",children:[e.zatca.complianceScore,"%"]})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-signature text-2xl text-purple-600 dark:text-purple-400 mr-3"}),(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("eSignatures","E-Signatures")})]}),(0,c.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400",children:[(0,c.jsx)("i",{className:"fas fa-check-circle mr-1"}),x("active","Active")]})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("totalDocuments","Total Documents")}),(0,c.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.electronicSignatures.totalDocuments})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("pending","Pending")}),(0,c.jsx)("span",{className:"text-sm font-medium text-yellow-600",children:e.electronicSignatures.pendingSignatures})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("completed","Completed")}),(0,c.jsx)("span",{className:"text-sm font-medium text-green-600",children:e.electronicSignatures.completedSignatures})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-passport text-2xl text-orange-600 dark:text-orange-400 mr-3"}),(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("visaIntegration","Visa Integration")})]}),(0,c.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400",children:[(0,c.jsx)("i",{className:"fas fa-check-circle mr-1"}),x("active","Active")]})]}),(0,c.jsxs)("div",{className:"space-y-2",children:[(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("linkedVisas","Linked Visas")}),(0,c.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.visaIntegration.linkedVisas})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("verified","Verified")}),(0,c.jsx)("span",{className:"text-sm font-medium text-green-600",children:e.visaIntegration.verifiedVisas})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:x("expired","Expired")}),(0,c.jsx)("span",{className:"text-sm font-medium text-red-600",children:e.visaIntegration.expiredVisas})]})]})]})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,c.jsx)("i",{className:"fas fa-chart-line text-blue-600 dark:text-blue-400 mr-2"}),x("complianceOverview","Compliance Overview")]}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,c.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"ZATCA Compliance"}),(0,c.jsxs)("span",{className:"text-sm font-bold text-blue-600",children:[e.zatca.complianceScore,"%"]})]}),(0,c.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,c.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.zatca.complianceScore,"%")}})})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,c.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"NPHIES Integration"}),(0,c.jsx)("span",{className:"text-sm font-bold text-green-600",children:"95%"})]}),(0,c.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,c.jsx)("div",{className:"bg-green-600 h-2 rounded-full w-[95%]"})})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,c.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Electronic Signatures"}),(0,c.jsx)("span",{className:"text-sm font-bold text-purple-600",children:"88%"})]}),(0,c.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,c.jsx)("div",{className:"bg-purple-600 h-2 rounded-full w-[88%]"})})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,c.jsx)("i",{className:"fas fa-clock text-green-600 dark:text-green-400 mr-2"}),x("recentActivities","Recent Activities")]}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,c.jsx)("i",{className:"fas fa-file-invoice text-blue-600 mr-3"}),(0,c.jsxs)("div",{className:"flex-1",children:[(0,c.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:x("invoiceSubmittedZatca","Invoice submitted to ZATCA")}),(0,c.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"2 minutes ago"})]})]}),(0,c.jsxs)("div",{className:"flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,c.jsx)("i",{className:"fas fa-check-circle text-green-600 mr-3"}),(0,c.jsxs)("div",{className:"flex-1",children:[(0,c.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:x("eligibilityVerified","Patient eligibility verified")}),(0,c.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"15 minutes ago"})]})]}),(0,c.jsxs)("div",{className:"flex items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg",children:[(0,c.jsx)("i",{className:"fas fa-signature text-purple-600 mr-3"}),(0,c.jsxs)("div",{className:"flex-1",children:[(0,c.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:x("documentSigned","Treatment plan signed electronically")}),(0,c.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"1 hour ago"})]})]}),(0,c.jsxs)("div",{className:"flex items-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg",children:[(0,c.jsx)("i",{className:"fas fa-passport text-orange-600 mr-3"}),(0,c.jsxs)("div",{className:"flex-1",children:[(0,c.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:x("visaLinked","Visa linked to patient services")}),(0,c.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"3 hours ago"})]})]})]})]})]}),(0,c.jsxs)("div",{className:"mt-8 flex flex-wrap gap-4",children:[(0,c.jsxs)("button",{onClick:()=>(0,l.Ay)("NPHIES configuration coming soon"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-cog mr-2"}),x("configureNphies","Configure NPHIES")]}),(0,c.jsxs)("button",{onClick:()=>(0,l.Ay)("ZATCA setup coming soon"),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-receipt mr-2"}),x("setupZatca","Setup ZATCA")]}),(0,c.jsxs)("button",{onClick:()=>(0,l.Ay)("Signature templates coming soon"),className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-signature mr-2"}),x("manageSignatures","Manage Signatures")]}),(0,c.jsxs)("button",{onClick:o,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-sync mr-2"}),x("refreshData","Refresh Data")]})]})]})}}}]);
//# sourceMappingURL=682.f5425a5f.chunk.js.map