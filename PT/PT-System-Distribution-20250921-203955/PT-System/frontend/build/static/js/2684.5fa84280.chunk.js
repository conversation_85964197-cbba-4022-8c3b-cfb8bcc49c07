"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2684],{2684:(e,t,s)=>{s.r(t),s.d(t,{default:()=>u});var i=s(2555),a=s(5043),n=s(7921),r=s(4528);const o=()=>{const{user:e}=(0,r.A)(),[t,s]=(0,a.useState)(null),[i,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{e?o():(s(null),n(!1))},[e]);const o=async()=>{try{n(!0);const t=await fetch("/api/v1/permissions/me",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(t.ok){const e=await t.json();s(e.data.permissions)}else{const t=l(e.role);s(t)}}catch(t){console.error("Error loading permissions:",t);const i=l((null===e||void 0===e?void 0:e.role)||"nurse");s(i)}finally{n(!1)}},l=e=>{const t={admin:{forms:{medicalDiagnosis:!0,ptEvaluation:!0,reassessment:!0,dischargeEvaluation:!0,followUpForm:!0,patientEducationDoctor:!0,progressNotes:!0,patientEducationTherapist:!0,patientEducationNurse:!0,nursingForm:!0,clinicalIndicators:!0,labResults:!0,radiologyResults:!0},system:{viewPatients:!0,createPatients:!0,editPatients:!0,deletePatients:!0,viewAppointments:!0,createAppointments:!0,editAppointments:!0,deleteAppointments:!0,viewAnalytics:!0,manageUsers:!0,systemSettings:!0},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!0}},doctor:{forms:{medicalDiagnosis:!0,ptEvaluation:!0,reassessment:!0,dischargeEvaluation:!0,followUpForm:!0,patientEducationDoctor:!0,clinicalIndicators:!0,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,labResults:!0,radiologyResults:!0},system:{viewPatients:!0,createPatients:!0,editPatients:!0,deletePatients:!1,viewAppointments:!0,createAppointments:!0,editAppointments:!0,deleteAppointments:!1,viewAnalytics:!0,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!0}},therapist:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!0,patientEducationTherapist:!0,clinicalIndicators:!0,patientEducationNurse:!1,nursingForm:!1,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!0,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!0,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!1}},nurse:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!0,nursingForm:!0,clinicalIndicators:!1,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!0,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}},external_lab:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!1,labResults:!0,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!1,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}},external_radiology:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!1,labResults:!1,radiologyResults:!0},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!1,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}},receptionist:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!1,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!0,editPatients:!0,deletePatients:!1,viewAppointments:!0,createAppointments:!0,editAppointments:!0,deleteAppointments:!0,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}}};return t[e]||t.nurse};return{permissions:t,loading:i,hasPermission:(s,i)=>{var a;return!(!t||!e)&&("admin"===e.role||((null===(a=t[s])||void 0===a?void 0:a[i])||!1))},hasAnyPermission:(s,i)=>!(!t||!e)&&("admin"===e.role||i.some(e=>{var i;return null===(i=t[s])||void 0===i?void 0:i[e]})),hasRole:t=>(null===e||void 0===e?void 0:e.role)===t,hasAnyRole:t=>t.includes(null===e||void 0===e?void 0:e.role),userRole:null===e||void 0===e?void 0:e.role,isAdmin:"admin"===(null===e||void 0===e?void 0:e.role),isDoctor:"doctor"===(null===e||void 0===e?void 0:e.role),isTherapist:"therapist"===(null===e||void 0===e?void 0:e.role),isNurse:"nurse"===(null===e||void 0===e?void 0:e.role),isExternalLab:"external_lab"===(null===e||void 0===e?void 0:e.role),isExternalRadiology:"external_radiology"===(null===e||void 0===e?void 0:e.role),isReceptionist:"receptionist"===(null===e||void 0===e?void 0:e.role),isManager:"manager"===(null===e||void 0===e?void 0:e.role)}};var l=s(579);const c=e=>{let{children:t,permission:s,category:i="forms",role:a,roles:r,fallback:c=null,showFallback:m=!0}=e;const{hasPermission:p,hasRole:u,hasAnyRole:g,loading:v,userRole:h}=o(),{t:y}=(0,n.o)();return v?(0,l.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"})}):a&&!u(a)?m?c||(0,l.jsx)(d,{requiredRole:a}):null:r&&!g(r)?m?c||(0,l.jsx)(d,{requiredRoles:r}):null:s&&!p(i,s)?m?c||(0,l.jsx)(d,{requiredPermission:s,category:i}):null:t},d=e=>{let{requiredRole:t,requiredRoles:s,requiredPermission:i,category:a}=e;const{t:r}=(0,n.o)(),{userRole:c}=o(),d={admin:r("admin","\u0645\u062f\u064a\u0631 \u0627\u0644\u0646\u0638\u0627\u0645"),doctor:r("doctor","\u0637\u0628\u064a\u0628"),therapist:r("therapist","\u0623\u062e\u0635\u0627\u0626\u064a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),nurse:r("nurse","\u0645\u0645\u0631\u0636/\u0645\u0645\u0631\u0636\u0629"),external_lab:r("externalLab","\u0645\u062e\u062a\u0628\u0631 \u062e\u0627\u0631\u062c\u064a"),external_radiology:r("externalRadiology","\u0645\u0631\u0643\u0632 \u0623\u0634\u0639\u0629 \u062e\u0627\u0631\u062c\u064a"),receptionist:r("receptionist","\u0645\u0648\u0638\u0641 \u0627\u0633\u062a\u0642\u0628\u0627\u0644"),manager:r("manager","\u0645\u062f\u064a\u0631")},m={medicalDiagnosis:r("medicalDiagnosis","\u0627\u0644\u062a\u0634\u062e\u064a\u0635 \u0627\u0644\u0637\u0628\u064a"),ptEvaluation:r("ptEvaluation","\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),reassessment:r("reassessment","\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u062a\u0642\u064a\u064a\u0645"),dischargeEvaluation:r("dischargeEvaluation","\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u062e\u0631\u0648\u062c"),followUpForm:r("followUpForm","\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u0645\u062a\u0627\u0628\u0639\u0629"),patientEducationDoctor:r("patientEducationDoctor","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u0637\u0628\u064a\u0628)"),progressNotes:r("progressNotes","\u0645\u0644\u0627\u062d\u0638\u0627\u062a \u062a\u0642\u062f\u0645 \u0627\u0644\u062d\u0627\u0644\u0629"),patientEducationTherapist:r("patientEducationTherapist","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u0623\u062e\u0635\u0627\u0626\u064a)"),patientEducationNurse:r("patientEducationNurse","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u062a\u0645\u0631\u064a\u0636)"),nursingForm:r("nursingForm","\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u062a\u0645\u0631\u064a\u0636 \u0627\u0644\u062a\u0627\u0628\u0639 \u0644\u0644\u0645\u0631\u0643\u0632"),clinicalIndicators:r("clinicalIndicators","\u0627\u0644\u0645\u0624\u0634\u0631\u0627\u062a \u0627\u0644\u0633\u0631\u064a\u0631\u064a\u0629"),labResults:r("labResults","\u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0645\u062e\u062a\u0628\u0631"),radiologyResults:r("radiologyResults","\u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0623\u0634\u0639\u0629"),viewPatients:r("viewPatients","\u0639\u0631\u0636 \u0627\u0644\u0645\u0631\u0636\u0649"),createPatients:r("createPatients","\u0625\u0646\u0634\u0627\u0621 \u0645\u0631\u0636\u0649"),editPatients:r("editPatients","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0631\u0636\u0649"),deletePatients:r("deletePatients","\u062d\u0630\u0641 \u0627\u0644\u0645\u0631\u0636\u0649"),viewAppointments:r("viewAppointments","\u0639\u0631\u0636 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),createAppointments:r("createAppointments","\u0625\u0646\u0634\u0627\u0621 \u0645\u0648\u0627\u0639\u064a\u062f"),editAppointments:r("editAppointments","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),deleteAppointments:r("deleteAppointments","\u062d\u0630\u0641 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),viewAnalytics:r("viewAnalytics","\u0639\u0631\u0636 \u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a"),manageUsers:r("manageUsers","\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"),systemSettings:r("systemSettings","\u0625\u0639\u062f\u0627\u062f\u0627\u062a \u0627\u0644\u0646\u0638\u0627\u0645"),functionalIndependenceComparison:r("functionalIndependenceComparison","\u062f\u0631\u062c\u0629 \u0627\u0644\u0627\u0633\u062a\u0642\u0644\u0627\u0644\u064a\u0629 \u0627\u0644\u0648\u0638\u064a\u0641\u064a\u0629"),treatmentPlanEffectiveness:r("treatmentPlanEffectiveness","\u0641\u0639\u0627\u0644\u064a\u0629 \u0627\u0644\u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c\u064a\u0629"),clinicalProgressTracking:r("clinicalProgressTracking","\u062a\u062a\u0628\u0639 \u0627\u0644\u062a\u0642\u062f\u0645 \u0627\u0644\u0633\u0631\u064a\u0631\u064a"),outcomeMetrics:r("outcomeMetrics","\u0645\u0642\u0627\u064a\u064a\u0633 \u0627\u0644\u0646\u062a\u0627\u0626\u062c"),complianceReporting:r("complianceReporting","\u062a\u0642\u0627\u0631\u064a\u0631 \u0627\u0644\u0627\u0645\u062a\u062b\u0627\u0644")};return(0,l.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,l.jsxs)("div",{className:"text-center max-w-md",children:[(0,l.jsx)("div",{className:"mb-4",children:(0,l.jsx)("i",{className:"fas fa-shield-alt text-6xl text-red-400"})}),(0,l.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:r("accessDenied","\u0627\u0644\u0648\u0635\u0648\u0644 \u0645\u0631\u0641\u0648\u0636")}),(0,l.jsxs)("div",{className:"text-gray-600 dark:text-gray-400 space-y-2",children:[(0,l.jsxs)("p",{children:[r("currentRole","\u062f\u0648\u0631\u0643 \u0627\u0644\u062d\u0627\u0644\u064a"),": ",(0,l.jsx)("span",{className:"font-medium",children:d[c]||c})]}),t&&(0,l.jsxs)("p",{children:[r("requiredRole","\u0627\u0644\u062f\u0648\u0631 \u0627\u0644\u0645\u0637\u0644\u0648\u0628"),": ",(0,l.jsx)("span",{className:"font-medium text-red-600",children:d[t]||t})]}),s&&(0,l.jsxs)("p",{children:[r("requiredRoles","\u0627\u0644\u0623\u062f\u0648\u0627\u0631 \u0627\u0644\u0645\u0637\u0644\u0648\u0628\u0629"),":",(0,l.jsx)("span",{className:"font-medium text-red-600",children:s.map(e=>d[e]||e).join(" \u0623\u0648 ")})]}),i&&(0,l.jsxs)("p",{children:[r("requiredPermission","\u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0629 \u0627\u0644\u0645\u0637\u0644\u0648\u0628\u0629"),":",(0,l.jsx)("span",{className:"font-medium text-red-600",children:m[i]||i})]})]}),(0,l.jsx)("div",{className:"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:(0,l.jsx)("p",{className:"text-sm text-yellow-800 dark:text-yellow-200",children:r("contactAdminForAccess","\u064a\u0631\u062c\u0649 \u0627\u0644\u062a\u0648\u0627\u0635\u0644 \u0645\u0639 \u0645\u062f\u064a\u0631 \u0627\u0644\u0646\u0638\u0627\u0645 \u0644\u0644\u062d\u0635\u0648\u0644 \u0639\u0644\u0649 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u0645\u0637\u0644\u0648\u0628\u0629")})})]})})},m=e=>{let{children:t,fallback:s,showFallback:i=!0}=e;return(0,l.jsx)(c,{role:"admin",fallback:s,showFallback:i,children:t})};var p=s(3768);const u=()=>{var e,t;const{t:s,isRTL:r}=(0,n.o)(),[o,c]=(0,a.useState)([]),[d,u]=(0,a.useState)(null),[g,v]=(0,a.useState)(null),[h,y]=(0,a.useState)(!0),[x,f]=(0,a.useState)(!1),[b,P]=(0,a.useState)("users"),E={doctor:{name:s("doctor","\u0637\u0628\u064a\u0628"),nameEn:"Doctor",description:s("doctorPermissions","\u0627\u0644\u062a\u0634\u062e\u064a\u0635 \u0627\u0644\u0637\u0628\u064a\u060c \u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a\u060c \u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u062a\u0642\u064a\u064a\u0645\u060c \u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u062e\u0631\u0648\u062c\u060c \u0646\u0645\u0648\u0630\u062c \u0627\u0644\u0645\u062a\u0627\u0628\u0639\u0629\u060c \u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636"),color:"blue"},therapist:{name:s("therapist","\u0623\u062e\u0635\u0627\u0626\u064a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),nameEn:"Physical Therapist",description:s("therapistPermissions","\u0645\u0644\u0627\u062d\u0638\u0627\u062a \u062a\u0642\u062f\u0645 \u0627\u0644\u062d\u0627\u0644\u0629\u060c \u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636"),color:"green"},nurse:{name:s("nurse","\u0645\u0645\u0631\u0636/\u0645\u0645\u0631\u0636\u0629"),nameEn:"Nurse",description:s("nursePermissions","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636\u060c \u0646\u0645\u0648\u0630\u062c \u0627\u0644\u062a\u0645\u0631\u064a\u0636 \u0627\u0644\u062a\u0627\u0628\u0639 \u0644\u0644\u0645\u0631\u0643\u0632"),color:"pink"},external_lab:{name:s("externalLab","\u0645\u062e\u062a\u0628\u0631 \u062e\u0627\u0631\u062c\u064a"),nameEn:"External Laboratory",description:s("externalLabPermissions","\u0625\u062f\u062e\u0627\u0644 \u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0645\u062e\u062a\u0628\u0631"),color:"purple"},external_radiology:{name:s("externalRadiology","\u0645\u0631\u0643\u0632 \u0623\u0634\u0639\u0629 \u062e\u0627\u0631\u062c\u064a"),nameEn:"External Radiology",description:s("externalRadiologyPermissions","\u0625\u062f\u062e\u0627\u0644 \u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0623\u0634\u0639\u0629"),color:"indigo"},receptionist:{name:s("receptionist","\u0645\u0648\u0638\u0641 \u0627\u0633\u062a\u0642\u0628\u0627\u0644"),nameEn:"Receptionist",description:s("receptionistPermissions","\u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u0645\u0631\u0636\u0649\u060c \u062d\u062c\u0632 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),color:"yellow"},manager:{name:s("manager","\u0645\u062f\u064a\u0631"),nameEn:"Manager",description:s("managerPermissions","\u0639\u0631\u0636 \u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a \u0648\u0627\u0644\u062a\u0642\u0627\u0631\u064a\u0631\u060c \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"),color:"red"},admin:{name:s("admin","\u0645\u062f\u064a\u0631 \u0627\u0644\u0646\u0638\u0627\u0645"),nameEn:"Administrator",description:s("adminPermissions","\u062c\u0645\u064a\u0639 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a"),color:"gray"}},w={forms:{name:s("formsPermissions","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u0646\u0645\u0627\u0630\u062c"),nameEn:"Forms Permissions",permissions:{medicalDiagnosis:s("medicalDiagnosis","\u0627\u0644\u062a\u0634\u062e\u064a\u0635 \u0627\u0644\u0637\u0628\u064a"),ptEvaluation:s("ptEvaluation","\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),reassessment:s("reassessment","\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u062a\u0642\u064a\u064a\u0645"),dischargeEvaluation:s("dischargeEvaluation","\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u062e\u0631\u0648\u062c"),followUpForm:s("followUpForm","\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u0645\u062a\u0627\u0628\u0639\u0629"),patientEducationDoctor:s("patientEducationDoctor","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u0637\u0628\u064a\u0628)"),progressNotes:s("progressNotes","\u0645\u0644\u0627\u062d\u0638\u0627\u062a \u062a\u0642\u062f\u0645 \u0627\u0644\u062d\u0627\u0644\u0629"),patientEducationTherapist:s("patientEducationTherapist","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u0623\u062e\u0635\u0627\u0626\u064a)"),patientEducationNurse:s("patientEducationNurse","\u0646\u0645\u0648\u0630\u062c \u062a\u062b\u0642\u064a\u0641 \u0627\u0644\u0645\u0631\u064a\u0636 (\u062a\u0645\u0631\u064a\u0636)"),nursingForm:s("nursingForm","\u0646\u0645\u0648\u0630\u062c \u0627\u0644\u062a\u0645\u0631\u064a\u0636 \u0627\u0644\u062a\u0627\u0628\u0639 \u0644\u0644\u0645\u0631\u0643\u0632"),clinicalIndicators:s("clinicalIndicators","\u0627\u0644\u0645\u0624\u0634\u0631\u0627\u062a \u0627\u0644\u0633\u0631\u064a\u0631\u064a\u0629"),labResults:s("labResults","\u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0645\u062e\u062a\u0628\u0631"),radiologyResults:s("radiologyResults","\u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0623\u0634\u0639\u0629")}},system:{name:s("systemPermissions","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u0646\u0638\u0627\u0645"),nameEn:"System Permissions",permissions:{viewPatients:s("viewPatients","\u0639\u0631\u0636 \u0627\u0644\u0645\u0631\u0636\u0649"),createPatients:s("createPatients","\u0625\u0646\u0634\u0627\u0621 \u0645\u0631\u0636\u0649"),editPatients:s("editPatients","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0631\u0636\u0649"),deletePatients:s("deletePatients","\u062d\u0630\u0641 \u0627\u0644\u0645\u0631\u0636\u0649"),viewAppointments:s("viewAppointments","\u0639\u0631\u0636 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),createAppointments:s("createAppointments","\u0625\u0646\u0634\u0627\u0621 \u0645\u0648\u0627\u0639\u064a\u062f"),editAppointments:s("editAppointments","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),deleteAppointments:s("deleteAppointments","\u062d\u0630\u0641 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f"),viewAnalytics:s("viewAnalytics","\u0639\u0631\u0636 \u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a"),manageUsers:s("manageUsers","\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"),systemSettings:s("systemSettings","\u0625\u0639\u062f\u0627\u062f\u0627\u062a \u0627\u0644\u0646\u0638\u0627\u0645")}},analytics:{name:s("analyticsPermissions","\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a"),nameEn:"Analytics Permissions",permissions:{functionalIndependenceComparison:s("functionalIndependenceComparison","\u062f\u0631\u062c\u0629 \u0627\u0644\u0627\u0633\u062a\u0642\u0644\u0627\u0644\u064a\u0629 \u0627\u0644\u0648\u0638\u064a\u0641\u064a\u0629 \u0642\u0628\u0644 \u0648\u0628\u0639\u062f \u0627\u0644\u0628\u0631\u0646\u0627\u0645\u062c \u0627\u0644\u0639\u0644\u0627\u062c\u064a"),treatmentPlanEffectiveness:s("treatmentPlanEffectiveness","\u0646\u0633\u0628\u0629 \u0627\u0644\u062a\u062d\u0633\u0646 \u0627\u0644\u0633\u0631\u064a\u0631\u064a \u062d\u0633\u0628 \u0646\u0648\u0639 \u0627\u0644\u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c\u064a\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u0629"),clinicalProgressTracking:s("clinicalProgressTracking","\u062a\u062a\u0628\u0639 \u0627\u0644\u062a\u0642\u062f\u0645 \u0627\u0644\u0633\u0631\u064a\u0631\u064a"),outcomeMetrics:s("outcomeMetrics","\u0645\u0642\u0627\u064a\u064a\u0633 \u0627\u0644\u0646\u062a\u0627\u0626\u062c"),complianceReporting:s("complianceReporting","\u062a\u0642\u0627\u0631\u064a\u0631 \u0627\u0644\u0627\u0645\u062a\u062b\u0627\u0644")}}};(0,a.useEffect)(()=>{A()},[]);const A=async()=>{try{y(!0);const e=await fetch("/api/v1/users",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.ok){const t=await e.json();c(t.data)}else{c([{_id:"1",firstName:"\u0623\u062d\u0645\u062f",lastName:"\u0645\u062d\u0645\u062f",email:"<EMAIL>",role:"doctor",department:"\u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a",isActive:!0},{_id:"2",firstName:"\u0641\u0627\u0637\u0645\u0629",lastName:"\u0639\u0644\u064a",email:"<EMAIL>",role:"therapist",department:"\u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a",isActive:!0},{_id:"3",firstName:"\u0633\u0627\u0631\u0629",lastName:"\u0623\u062d\u0645\u062f",email:"<EMAIL>",role:"nurse",department:"\u0627\u0644\u062a\u0645\u0631\u064a\u0636",isActive:!0},{_id:"4",firstName:"\u0645\u062e\u062a\u0628\u0631 \u0627\u0644\u0634\u0641\u0627\u0621",lastName:"",email:"<EMAIL>",role:"external_lab",department:"\u0645\u062e\u062a\u0628\u0631 \u062e\u0627\u0631\u062c\u064a",isActive:!0}])}}catch(e){console.error("Error loading users:",e),p.Ay.error(s("errorLoadingUsers","\u062e\u0637\u0623 \u0641\u064a \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"))}finally{y(!1)}},N=e=>{const t={doctor:{forms:{medicalDiagnosis:!0,ptEvaluation:!0,reassessment:!0,dischargeEvaluation:!0,followUpForm:!0,patientEducationDoctor:!0,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!0,labResults:!0,radiologyResults:!0},system:{viewPatients:!0,createPatients:!0,editPatients:!0,deletePatients:!1,viewAppointments:!0,createAppointments:!0,editAppointments:!0,deleteAppointments:!1,viewAnalytics:!0,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!0}},therapist:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!0,patientEducationTherapist:!0,patientEducationNurse:!1,nursingForm:!1,clinicalIndicators:!0,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!0,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!0,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!0,treatmentPlanEffectiveness:!0,clinicalProgressTracking:!0,outcomeMetrics:!0,complianceReporting:!1}},nurse:{forms:{medicalDiagnosis:!1,ptEvaluation:!1,reassessment:!1,dischargeEvaluation:!1,followUpForm:!1,patientEducationDoctor:!1,progressNotes:!1,patientEducationTherapist:!1,patientEducationNurse:!0,nursingForm:!0,clinicalIndicators:!1,labResults:!1,radiologyResults:!1},system:{viewPatients:!0,createPatients:!1,editPatients:!1,deletePatients:!1,viewAppointments:!0,createAppointments:!1,editAppointments:!1,deleteAppointments:!1,viewAnalytics:!1,manageUsers:!1,systemSettings:!1},analytics:{functionalIndependenceComparison:!1,treatmentPlanEffectiveness:!1,clinicalProgressTracking:!1,outcomeMetrics:!1,complianceReporting:!1}}};return t[e]||t.nurse},j=e=>{u(e),(async e=>{try{const t=await fetch("/api/v1/permissions/user/".concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(t.ok){const e=await t.json();v(e.data.permissions)}else{const t=o.find(t=>t._id===e);if(t){const e=N(t.role);v(e)}}}catch(t){console.error("Error loading permissions:",t),p.Ay.error(s("errorLoadingPermissions","\u062e\u0637\u0623 \u0641\u064a \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a"))}})(e._id)};return h?(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,l.jsx)(m,{children:(0,l.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:s("permissionsManagement","\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:s("permissionsManagementDesc","\u062a\u062d\u062f\u064a\u062f \u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0625\u062f\u062e\u0627\u0644 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0641\u064a \u0627\u0644\u0646\u0638\u0627\u0645 \u0627\u0644\u0637\u0628\u064a \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u062d\u0633\u0628 \u0623\u062f\u0648\u0627\u0631 \u0627\u0644\u0641\u0631\u064a\u0642 \u0627\u0644\u0639\u0644\u0627\u062c\u064a")})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,l.jsx)("div",{className:"lg:col-span-1",children:(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,l.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s("users","\u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u0648\u0646")})}),(0,l.jsx)("div",{className:"p-6",children:(0,l.jsx)("div",{className:"space-y-3",children:o.map(e=>{var t,s,i,a,n;return(0,l.jsx)("div",{onClick:()=>j(e),className:"p-4 rounded-lg border cursor-pointer transition-colors ".concat((null===d||void 0===d?void 0:d._id)===e._id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"),children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white",children:[e.firstName," ",e.lastName]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.email})]}),(0,l.jsx)("div",{className:"text-right",children:(0,l.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-".concat(null===(t=E[e.role])||void 0===t?void 0:t.color,"-100 text-").concat(null===(s=E[e.role])||void 0===s?void 0:s.color,"-800 dark:bg-").concat(null===(i=E[e.role])||void 0===i?void 0:i.color,"-900/30 dark:text-").concat(null===(a=E[e.role])||void 0===a?void 0:a.color,"-200"),children:null===(n=E[e.role])||void 0===n?void 0:n.name})})]})},e._id)})})})]})}),(0,l.jsx)("div",{className:"lg:col-span-2",children:d?(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,l.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[s("permissionsFor","\u0635\u0644\u0627\u062d\u064a\u0627\u062a")," ",d.firstName," ",d.lastName]}),(0,l.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[null===(e=E[d.role])||void 0===e?void 0:e.name," - ",null===(t=E[d.role])||void 0===t?void 0:t.description]})]}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsx)("button",{onClick:async()=>{if(d)try{f(!0);const e=await fetch("/api/v1/permissions/user/".concat(d._id,"/reset"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.ok){const t=await e.json();v(t.data.permissions),p.Ay.success(s("permissionsReset","\u062a\u0645 \u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0644\u0644\u0627\u0641\u062a\u0631\u0627\u0636\u064a\u0629"))}else{const e=N(d.role);v(e),p.Ay.success(s("permissionsReset","\u062a\u0645 \u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0644\u0644\u0627\u0641\u062a\u0631\u0627\u0636\u064a\u0629 (\u0648\u0636\u0639 \u0627\u0644\u062a\u062c\u0631\u064a\u0628)"))}}catch(e){console.error("Error resetting permissions:",e),p.Ay.error(s("errorResettingPermissions","\u062e\u0637\u0623 \u0641\u064a \u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a"))}finally{f(!1)}},disabled:x,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50",children:s("resetToDefaults","\u0625\u0639\u0627\u062f\u0629 \u0644\u0644\u0627\u0641\u062a\u0631\u0627\u0636\u064a")}),(0,l.jsx)("button",{onClick:async()=>{if(d&&g)try{f(!0);(await fetch("/api/v1/permissions/user/".concat(d._id),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify({permissions:g})})).ok?p.Ay.success(s("permissionsSaved","\u062a\u0645 \u062d\u0641\u0638 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0628\u0646\u062c\u0627\u062d")):p.Ay.success(s("permissionsSaved","\u062a\u0645 \u062d\u0641\u0638 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a \u0628\u0646\u062c\u0627\u062d (\u0648\u0636\u0639 \u0627\u0644\u062a\u062c\u0631\u064a\u0628)"))}catch(e){console.error("Error saving permissions:",e),p.Ay.error(s("errorSavingPermissions","\u062e\u0637\u0623 \u0641\u064a \u062d\u0641\u0638 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a"))}finally{f(!1)}},disabled:x,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:x?s("saving","\u062c\u0627\u0631\u064a \u0627\u0644\u062d\u0641\u0638..."):s("savePermissions","\u062d\u0641\u0638 \u0627\u0644\u0635\u0644\u0627\u062d\u064a\u0627\u062a")})]})]})}),g&&(0,l.jsx)("div",{className:"p-6",children:Object.entries(w).map(e=>{let[t,s]=e;return(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:s.name}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(s.permissions).map(e=>{var s;let[a,n]=e;return(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,l.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:n}),(0,l.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,l.jsx)("input",{type:"checkbox",checked:(null===(s=g[t])||void 0===s?void 0:s[a])||!1,onChange:e=>((e,t,s)=>{v(a=>(0,i.A)((0,i.A)({},a),{},{[e]:(0,i.A)((0,i.A)({},a[e]),{},{[t]:s})}))})(t,a,e.target.checked),className:"sr-only peer"}),(0,l.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]},a)})})]},t)})})]}):(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-12",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("i",{className:"fas fa-user-shield text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:s("selectUser","\u0627\u062e\u062a\u0631 \u0645\u0633\u062a\u062e\u062f\u0645")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:s("selectUserDesc","\u0627\u062e\u062a\u0631 \u0645\u0633\u062a\u062e\u062f\u0645\u0627\u064b \u0645\u0646 \u0627\u0644\u0642\u0627\u0626\u0645\u0629 \u0644\u0639\u0631\u0636 \u0648\u062a\u0639\u062f\u064a\u0644 \u0635\u0644\u0627\u062d\u064a\u0627\u062a\u0647")})]})})})]})]})})}}}]);
//# sourceMappingURL=2684.5fa84280.chunk.js.map