{"version": 3, "file": "static/js/5711.d9508487.chunk.js", "mappings": "8QA6FA,EAtFeA,IAUR,IAVS,SACdC,EAAQ,QACRC,EAAU,UAAS,KACnBC,EAAO,KAAI,SACXC,GAAW,EAAK,QAChBC,GAAU,EAAK,UACfC,EAAY,GAAE,KACdC,EAAO,SAAQ,QACfC,GAEDR,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,MAEMC,EAAW,CACfC,QAAS,+DACTC,UAAW,+DACXC,QAAS,qFACTC,OAAQ,4DACRC,QAAS,kEACTC,QAAS,qEACTC,MAAO,uDAGHC,EAAQ,CACZC,GAAI,wBACJC,GAAI,oBACJC,GAAI,oBACJC,GAAI,sBACJC,GAAI,uBAKAC,EAAgB,CAtBF,oJAwBlBd,EAASV,IAAYU,EAASC,QAC9BO,EAAMjB,IAASiB,EAAMG,GALCnB,GAAYC,EAAU,gCAAkC,GAO9EC,GACAqB,OAAOC,SAASC,KAAK,KAYvB,OACEC,EAAAA,EAAAA,MAAA,UAAAC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACExB,KAAMA,EACND,UAAWoB,EACXlB,QAdiBwB,IACf5B,GAAYC,EACd2B,EAAEC,iBAGAzB,GACFA,EAAQwB,IASR5B,SAAUA,GAAYC,GAClBI,GAAK,IAAAR,SAAA,CAERI,IACCyB,EAAAA,EAAAA,MAAA,OACExB,UAAU,kCACV4B,MAAM,6BACNC,KAAK,OACLC,QAAQ,YAAWnC,SAAA,EAEnBoC,EAAAA,EAAAA,KAAA,UACE/B,UAAU,aACVgC,GAAG,KACHC,GAAG,KACHC,EAAE,KACFC,OAAO,eACPC,YAAY,OAEdL,EAAAA,EAAAA,KAAA,QACE/B,UAAU,aACV6B,KAAK,eACLQ,EAAE,uHAIP1C,M,yKCrDP,EA5BaD,IAUN,IAVO,SACZC,EAAQ,UACRK,EAAY,GAAE,QACdsC,EAAU,MAAK,OACfC,EAAS,YAAW,OACpBC,EAAS,yBAAwB,QACjCC,EAAU,aAAY,WACtBC,EAAa,WAAU,MACvBC,EAAQ,IAETjD,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,MAAMuC,EAAc,CAClBF,EACAF,EACAC,EACAF,EACAD,EACAK,EACA3C,GACAqB,OAAOC,SAASC,KAAK,KAEvB,OACEQ,EAAAA,EAAAA,KAAA,OAAAN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CAAKzB,UAAW4C,GAAiBzC,GAAK,IAAAR,SACnCA,K,sFC7BP,SAASkD,EAAcnD,EAIpBoD,GAAQ,IAJa,MACtBC,EAAK,QACLC,GAEDtD,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoB4C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DvB,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTM,YAAa,IACbD,OAAQ,eACR,cAAe,OACf,YAAa,OACbiB,IAAKN,EACL,kBAAmBE,GAClB7C,GAAQ4C,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DI,GAAIL,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDK,cAAe,QACfC,eAAgB,QAChBlB,EAAG,oLAEP,CACA,MACA,EADiCY,EAAAA,WAAiBJ,E,iBCvBlD,SAASzC,EAAyBsB,EAAG8B,GACnC,GAAI,MAAQ9B,EAAG,MAAO,CAAC,EACvB,IAAI+B,EACFvB,EACAwB,ECLJ,SAAuCxB,EAAGR,GACxC,GAAI,MAAQQ,EAAG,MAAO,CAAC,EACvB,IAAIsB,EAAI,CAAC,EACT,IAAK,IAAIG,KAAKzB,EAAG,GAAI,CAAC,EAAE0B,eAAeC,KAAK3B,EAAGyB,GAAI,CACjD,IAAK,IAAMjC,EAAEoC,QAAQH,GAAI,SACzBH,EAAEG,GAAKzB,EAAEyB,EACX,CACA,OAAOH,CACT,CDHQ,CAA6B9B,EAAG8B,GACtC,GAAIN,OAAOa,sBAAuB,CAChC,IAAIJ,EAAIT,OAAOa,sBAAsBrC,GACrC,IAAKQ,EAAI,EAAGA,EAAIyB,EAAEK,OAAQ9B,IAAKuB,EAAIE,EAAEzB,IAAK,IAAMsB,EAAEM,QAAQL,IAAM,CAAC,EAAEQ,qBAAqBJ,KAAKnC,EAAG+B,KAAOC,EAAED,GAAK/B,EAAE+B,GAClH,CACA,OAAOC,CACT,C,2JEVA,SAASQ,EAAOxE,EAIboD,GAAQ,IAJM,MACfC,EAAK,QACLC,GAEDtD,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoB4C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DvB,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTM,YAAa,IACbD,OAAQ,eACR,cAAe,OACf,YAAa,OACbiB,IAAKN,EACL,kBAAmBE,GAClB7C,GAAQ4C,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DI,GAAIL,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDK,cAAe,QACfC,eAAgB,QAChBlB,EAAG,oNAEP,CACA,MACA,EADiCY,EAAAA,WAAiBiB,G,0CCvBlD,SAASC,EAAQzE,EAIdoD,GAAQ,IAJO,MAChBC,EAAK,QACLC,GAEDtD,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoB4C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DvB,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTM,YAAa,IACbD,OAAQ,eACR,cAAe,OACf,YAAa,OACbiB,IAAKN,EACL,kBAAmBE,GAClB7C,GAAQ4C,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DI,GAAIL,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDK,cAAe,QACfC,eAAgB,QAChBlB,EAAG,4JAEP,CACA,MACA,EADiCY,EAAAA,WAAiBkB,G,qDCFlD,MAwVA,EAxVyBC,KACvB,MAAM,EAAEZ,IAAMa,EAAAA,EAAAA,OACPtE,EAASuE,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CAEvCG,kBAAmB,EACnBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,wBAAwB,EACxBC,6BAA6B,EAC7BC,uBAAwB,GACxBC,qBAAsB,EAGtBC,iBAAkB,EAClBC,uBAAwB,GACxBC,eAAgB,GAChBC,iBAAiB,EACjBC,0BAA0B,EAG1BC,mBAAmB,EACnBC,WAAY,CAAC,kBACbC,mBAAmB,EACnBC,iBAAkB,GAGlBC,kBAAkB,EAClBC,mBAAmB,EACnBC,mBAAoB,IACpBC,sBAAsB,EAGtBC,sBAAsB,EACtBC,oBAAqB,UACrBC,wBAAwB,EACxBC,WAAW,EACXC,qBAAsB,gBAGjBC,EAAgBC,IAAqB7B,EAAAA,EAAAA,UAAS,CACnD,CACElB,GAAI,EACJpD,KAAM,eACNoG,KAAM,sBACNC,WAAW,IAAIC,MAAOC,cACtBC,SAAU,SACVC,YAAa,kCAEf,CACErD,GAAI,EACJpD,KAAM,sBACNoG,KAAM,wBACNC,UAAW,IAAIC,KAAKA,KAAKI,MAAQ,MAASH,cAC1CC,SAAU,OACVC,YAAa,kCAIjBE,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAAuBC,UAC3B,IACExC,GAAW,EAIb,CAAE,MAAOyC,GACPC,QAAQD,MAAM,oCAAqCA,GACnDE,EAAAA,GAAMF,MAAMvD,EAAE,uBAAwB,oCACxC,CAAC,QACCc,GAAW,EACb,GAiBI4C,EAAoBA,CAACC,EAAOC,KAChC3C,EAAY4C,IAAI5F,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACX4F,GAAI,IACP,CAACF,GAAQC,MAIPE,EAAoBb,IACxB,OAAQA,GACN,IAAK,OACH,MAAO,0BACT,IAAK,SACH,MAAO,gCACT,IAAK,MACH,MAAO,8BACT,QACE,MAAO,8BAIPc,EAAc7H,IAAA,IAAC,MAAEqD,EAAOyE,KAAMC,EAAI,SAAE9H,GAAUD,EAAA,OAClD8B,EAAAA,EAAAA,MAACkG,EAAAA,EAAI,CAAC1H,UAAU,MAAKL,SAAA,EACnB6B,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,yBAAwBL,SAAA,EACrCoC,EAAAA,EAAAA,KAAC0F,EAAI,CAACzH,UAAU,gCAChB+B,EAAAA,EAAAA,KAAA,MAAI/B,UAAU,sCAAqCL,SAAEoD,QAEvDhB,EAAAA,EAAAA,KAAA,OAAK/B,UAAU,YAAWL,SACvBA,QAKDgI,EAAaC,IAAA,IAAC,MAAEC,EAAK,KAAE5H,EAAO,OAAM,MAAEmH,EAAK,SAAEU,EAAQ,QAAEC,EAAU,KAAI,YAAErB,EAAc,MAAMkB,EAAA,OAC/FpG,EAAAA,EAAAA,MAAA,OAAA7B,SAAA,EACEoC,EAAAA,EAAAA,KAAA,SAAO/B,UAAU,+CAA8CL,SAC5DkI,IAEO,WAAT5H,GACC8B,EAAAA,EAAAA,KAAA,UACEqF,MAAOA,EACPU,SAAWpG,GAAMoG,EAASpG,EAAEsG,OAAOZ,OACnCpH,UAAU,yGAAwGL,SAE1G,OAAPoI,QAAO,IAAPA,OAAO,EAAPA,EAASE,IAAIC,IACZnG,EAAAA,EAAAA,KAAA,UAA2BqF,MAAOc,EAAOd,MAAMzH,SAC5CuI,EAAOL,OADGK,EAAOd,UAKb,aAATnH,GACFuB,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBL,SAAA,EAChCoC,EAAAA,EAAAA,KAAA,SACE9B,KAAK,WACLkI,QAASf,EACTU,SAAWpG,GAAMoG,EAASpG,EAAEsG,OAAOG,SACnCnI,UAAU,uEAEZ+B,EAAAA,EAAAA,KAAA,QAAM/B,UAAU,6BAA4BL,SAAC,oCAEpC,aAATM,GACF8B,EAAAA,EAAAA,KAAA,YACEqF,MAAOgB,MAAMC,QAAQjB,GAASA,EAAM7F,KAAK,MAAQ6F,EACjDU,SAAWpG,GAAMoG,EAAkB,UAAT7H,EAAmByB,EAAEsG,OAAOZ,MAAMkB,MAAM,MAAQ5G,EAAEsG,OAAOZ,OACnFmB,KAAM,EACNvI,UAAU,4GAGZ+B,EAAAA,EAAAA,KAAA,SACE9B,KAAMA,EACNmH,MAAOA,EACPU,SAAWpG,GAAMoG,EAAkB,WAAT7H,EAAoBuI,SAAS9G,EAAEsG,OAAOZ,OAAS1F,EAAEsG,OAAOZ,OAClFpH,UAAU,2GAGb0G,IACC3E,EAAAA,EAAAA,KAAA,KAAG/B,UAAU,6BAA4BL,SAAE+G,QAKjD,OAAI3G,GAEAgC,EAAAA,EAAAA,KAAA,OAAK/B,UAAU,wCAAuCL,UACpDoC,EAAAA,EAAAA,KAAC0G,EAAAA,GAAc,CAAC5I,KAAK,UAMzB2B,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,YAAWL,SAAA,EAExB6B,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oCAAmCL,SAAA,EAChD6B,EAAAA,EAAAA,MAAA,OAAA7B,SAAA,EACE6B,EAAAA,EAAAA,MAAA,MAAIxB,UAAU,qDAAoDL,SAAA,EAChEoC,EAAAA,EAAAA,KAACc,EAAAA,EAAc,CAAC7C,UAAU,+BACzBwD,EAAE,mBAAoB,yBAEzBzB,EAAAA,EAAAA,KAAA,KAAG/B,UAAU,qBAAoBL,SAC9B6D,EAAE,8BAA+B,yDAGtCzB,EAAAA,EAAAA,KAAC2G,EAAAA,EAAM,CAACxI,QAnHK4G,UACjB,IACExC,GAAW,GAGX2C,EAAAA,GAAMtG,QAAQ6C,EAAE,wBAAyB,wCAC3C,CAAE,MAAOuD,GACPC,QAAQD,MAAM,oCAAqCA,GACnDE,EAAAA,GAAMF,MAAMvD,EAAE,uBAAwB,oCACxC,CAAC,QACCc,GAAW,EACb,GAwGiCxE,SAAUC,EAAQJ,SAC5C6D,EAAE,eAAgB,uBAIvBhC,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,wCAAuCL,SAAA,EAEpD6B,EAAAA,EAAAA,MAAC+F,EAAW,CAACxE,MAAOS,EAAE,iBAAkB,mBAAoBgE,KAAMtD,EAAQvE,SAAA,EACxEoC,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,gBAAiB,kBAC1BvD,KAAK,SACLmH,MAAO5C,EAASE,kBAChBoD,SAAWV,GAAUF,EAAkB,oBAAqBE,MAE9DrF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,mBAAoB,6BAC7BvD,KAAK,WACLmH,MAAO5C,EAASG,yBAChBmD,SAAWV,GAAUF,EAAkB,2BAA4BE,MAErErF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,iBAAkB,mBAC3BvD,KAAK,WACLmH,MAAO5C,EAASK,uBAChBiD,SAAWV,GAAUF,EAAkB,yBAA0BE,MAEnErF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,qBAAsB,8BAC/BvD,KAAK,SACLmH,MAAO5C,EAASO,uBAChB+C,SAAWV,GAAUF,EAAkB,yBAA0BE,SAKrE5F,EAAAA,EAAAA,MAAC+F,EAAW,CAACxE,MAAOS,EAAE,kBAAmB,oBAAqBgE,KAAMmB,EAAAA,EAAgBhJ,SAAA,EAClFoC,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,mBAAoB,sBAC7BvD,KAAK,SACLmH,MAAO5C,EAASS,iBAChB6C,SAAWV,GAAUF,EAAkB,mBAAoBE,MAE7DrF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,kBAAmB,8BAC5BvD,KAAK,SACLmH,MAAO5C,EAASU,uBAChB4C,SAAWV,GAAUF,EAAkB,yBAA0BE,MAEnErF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,iBAAkB,6BAC3BvD,KAAK,SACLmH,MAAO5C,EAASW,eAChB2C,SAAWV,GAAUF,EAAkB,iBAAkBE,MAE3DrF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,kBAAmB,oCAC5BvD,KAAK,WACLmH,MAAO5C,EAASY,gBAChB0C,SAAWV,GAAUF,EAAkB,kBAAmBE,SAK9D5F,EAAAA,EAAAA,MAAC+F,EAAW,CAACxE,MAAOS,EAAE,gBAAiB,kBAAmBgE,KAAMrD,EAASxE,SAAA,EACvEoC,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,oBAAqB,uBAC9BvD,KAAK,WACLmH,MAAO5C,EAASc,kBAChBwC,SAAWV,GAAUF,EAAkB,oBAAqBE,KAE7D5C,EAASc,oBACRvD,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,aAAc,wBACvBvD,KAAK,WACLmH,MAAO5C,EAASe,WAChBuC,SAAWV,GAAUF,EAAkB,aAAcE,GACrDV,YAAY,2CAGhB3E,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,oBAAqB,8BAC9BvD,KAAK,WACLmH,MAAO5C,EAASgB,kBAChBsC,SAAWV,GAAUF,EAAkB,oBAAqBE,SAKhE5F,EAAAA,EAAAA,MAAC+F,EAAW,CAACxE,MAAOS,EAAE,iBAAkB,mBAAoBgE,KAAM3E,EAAAA,EAAelD,SAAA,EAC/EoC,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,uBAAwB,0BACjCvD,KAAK,WACLmH,MAAO5C,EAASsB,qBAChBgC,SAAWV,GAAUF,EAAkB,uBAAwBE,MAEjErF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,sBAAuB,wBAChCvD,KAAK,SACLmH,MAAO5C,EAASuB,oBAChB+B,SAAWV,GAAUF,EAAkB,sBAAuBE,GAC9DW,QAAS,CACP,CAAEX,MAAO,UAAWS,MAAO,WAC3B,CAAET,MAAO,UAAWS,MAAO,WAC3B,CAAET,MAAO,WAAYS,MAAO,gBAGhC9F,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,yBAA0B,4BACnCvD,KAAK,WACLmH,MAAO5C,EAASwB,uBAChB8B,SAAWV,GAAUF,EAAkB,yBAA0BE,MAEnErF,EAAAA,EAAAA,KAAC4F,EAAU,CACTE,MAAOrE,EAAE,YAAa,kBACtBvD,KAAK,WACLmH,MAAO5C,EAASyB,UAChB6B,SAAWV,GAAUF,EAAkB,YAAaE,YAM1D5F,EAAAA,EAAAA,MAACkG,EAAAA,EAAI,CAAC1H,UAAU,MAAKL,SAAA,EACnB6B,EAAAA,EAAAA,MAAA,MAAIxB,UAAU,6DAA4DL,SAAA,EACxEoC,EAAAA,EAAAA,KAAC6G,EAAAA,EAAuB,CAAC5I,UAAU,iCAClCwD,EAAE,uBAAwB,8BAE7BzB,EAAAA,EAAAA,KAAA,OAAK/B,UAAU,YAAWL,SACvBwG,EAAe8B,IAAKY,IACnBrH,EAAAA,EAAAA,MAAA,OAAoBxB,UAAU,0EAAyEL,SAAA,EACrG6B,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,oBAAmBL,SAAA,EAChCoC,EAAAA,EAAAA,KAAA,QAAM/B,UAAS,8CAAA8I,OAAgDxB,EAAiBuB,EAAMpC,WAAY9G,SAC/FkJ,EAAMpC,SAASsC,iBAElBvH,EAAAA,EAAAA,MAAA,OAAKxB,UAAU,OAAML,SAAA,EACnBoC,EAAAA,EAAAA,KAAA,KAAG/B,UAAU,4BAA2BL,SAAEkJ,EAAMnC,eAChDlF,EAAAA,EAAAA,MAAA,KAAGxB,UAAU,wBAAuBL,SAAA,CAAC,SAAOkJ,EAAMxC,eAGtDtE,EAAAA,EAAAA,KAAA,OAAK/B,UAAU,aAAYL,UACzBoC,EAAAA,EAAAA,KAAA,KAAG/B,UAAU,wBAAuBL,SACjC,IAAI4G,KAAKsC,EAAMvC,WAAW0C,uBAZvBH,EAAMxF,a,sFCtV5B,SAASsF,EAAejJ,EAIrBoD,GAAQ,IAJc,MACvBC,EAAK,QACLC,GAEDtD,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoB4C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DvB,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTM,YAAa,IACbD,OAAQ,eACR,cAAe,OACf,YAAa,OACbiB,IAAKN,EACL,kBAAmBE,GAClB7C,GAAQ4C,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DI,GAAIL,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDK,cAAe,QACfC,eAAgB,QAChBlB,EAAG,uNAEP,CACA,MACA,EADiCY,EAAAA,WAAiB0F,E,sFCvBlD,SAASC,EAAuBlJ,EAI7BoD,GAAQ,IAJsB,MAC/BC,EAAK,QACLC,GAEDtD,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoB4C,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DvB,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTM,YAAa,IACbD,OAAQ,eACR,cAAe,OACf,YAAa,OACbiB,IAAKN,EACL,kBAAmBE,GAClB7C,GAAQ4C,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DI,GAAIL,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDK,cAAe,QACfC,eAAgB,QAChBlB,EAAG,qLAEP,CACA,MACA,EADiCY,EAAAA,WAAiB2F,E", "sources": ["components/Common/Button.jsx", "components/Common/Card.jsx", "../node_modules/@heroicons/react/24/outline/esm/LockClosedIcon.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../node_modules/@heroicons/react/24/outline/esm/KeyIcon.js", "../node_modules/@heroicons/react/24/outline/esm/UserIcon.js", "pages/Admin/SecuritySettings.jsx", "../node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js"], "sourcesContent": ["import React from 'react';\n\n/**\n * Button Component\n * A reusable button component with multiple variants and sizes\n */\n\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  className = '',\n  type = 'button',\n  onClick,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200';\n  \n  const variants = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',\n    outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',\n    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500',\n    ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500'\n  };\n\n  const sizes = {\n    xs: 'px-2.5 py-1.5 text-xs',\n    sm: 'px-3 py-2 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-4 py-2 text-base',\n    xl: 'px-6 py-3 text-base'\n  };\n\n  const disabledClasses = disabled || loading ? 'opacity-50 cursor-not-allowed' : '';\n  \n  const buttonClasses = [\n    baseClasses,\n    variants[variant] || variants.primary,\n    sizes[size] || sizes.md,\n    disabledClasses,\n    className\n  ].filter(Boolean).join(' ');\n\n  const handleClick = (e) => {\n    if (disabled || loading) {\n      e.preventDefault();\n      return;\n    }\n    if (onClick) {\n      onClick(e);\n    }\n  };\n\n  return (\n    <button\n      type={type}\n      className={buttonClasses}\n      onClick={handleClick}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n", "import React from 'react';\n\n/**\n * Card Component\n * A reusable card component with consistent styling\n */\n\nconst Card = ({ \n  children, \n  className = '', \n  padding = 'p-6',\n  shadow = 'shadow-sm',\n  border = 'border border-gray-200',\n  rounded = 'rounded-lg',\n  background = 'bg-white',\n  hover = '',\n  ...props \n}) => {\n  const cardClasses = [\n    background,\n    border,\n    rounded,\n    shadow,\n    padding,\n    hover,\n    className\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className={cardClasses} {...props}>\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n", "import * as React from \"react\";\nfunction LockClosedIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(LockClosedIcon);\nexport default ForwardRef;", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import * as React from \"react\";\nfunction KeyIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(KeyIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction UserIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserIcon);\nexport default ForwardRef;", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { toast } from 'react-hot-toast';\nimport {\n  LockClosedIcon,\n  ShieldCheckIcon,\n  KeyIcon,\n  ExclamationTriangleIcon,\n  ClockIcon,\n  UserIcon,\n  DevicePhoneMobileIcon\n} from '@heroicons/react/24/outline';\n\nimport Card from '../../components/Common/Card';\nimport Button from '../../components/Common/Button';\nimport LoadingSpinner from '../../components/UI/LoadingSpinner';\n\n/**\n * Security Settings Component\n * Manage system security policies and settings\n */\n\nconst SecuritySettings = () => {\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(false);\n  const [settings, setSettings] = useState({\n    // Password Policy\n    passwordMinLength: 8,\n    passwordRequireUppercase: true,\n    passwordRequireLowercase: true,\n    passwordRequireNumbers: true,\n    passwordRequireSpecialChars: true,\n    passwordExpirationDays: 90,\n    passwordHistoryCount: 5,\n    \n    // Account Security\n    maxLoginAttempts: 5,\n    accountLockoutDuration: 30,\n    sessionTimeout: 30,\n    enableTwoFactor: false,\n    requireTwoFactorForAdmin: true,\n    \n    // Access Control\n    enableIPWhitelist: false,\n    allowedIPs: ['***********/24'],\n    enableGeoBlocking: false,\n    blockedCountries: [],\n    \n    // Audit & Monitoring\n    enableLoginAudit: true,\n    enableActionAudit: true,\n    auditRetentionDays: 365,\n    enableRealTimeAlerts: true,\n    \n    // Data Protection\n    enableDataEncryption: true,\n    encryptionAlgorithm: 'AES-256',\n    enableBackupEncryption: true,\n    enableSSL: true,\n    sslCertificateExpiry: '2024-12-31'\n  });\n\n  const [securityEvents, setSecurityEvents] = useState([\n    {\n      id: 1,\n      type: 'failed_login',\n      user: '<EMAIL>',\n      timestamp: new Date().toISOString(),\n      severity: 'medium',\n      description: 'Multiple failed login attempts'\n    },\n    {\n      id: 2,\n      type: 'suspicious_activity',\n      user: '<EMAIL>',\n      timestamp: new Date(Date.now() - 3600000).toISOString(),\n      severity: 'high',\n      description: 'Login from unusual location'\n    }\n  ]);\n\n  useEffect(() => {\n    loadSecuritySettings();\n  }, []);\n\n  const loadSecuritySettings = async () => {\n    try {\n      setLoading(true);\n      // In a real implementation, load from API\n      // const response = await api.get('/admin/security-settings');\n      // setSettings(response.data);\n    } catch (error) {\n      console.error('Failed to load security settings:', error);\n      toast.error(t('failedToLoadSettings', 'Failed to load security settings'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      setLoading(true);\n      // In a real implementation, save to API\n      // await api.put('/admin/security-settings', settings);\n      toast.success(t('securitySettingsSaved', 'Security settings saved successfully'));\n    } catch (error) {\n      console.error('Failed to save security settings:', error);\n      toast.error(t('failedToSaveSettings', 'Failed to save security settings'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const getSeverityColor = (severity) => {\n    switch (severity) {\n      case 'high':\n        return 'text-red-600 bg-red-100';\n      case 'medium':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'low':\n        return 'text-green-600 bg-green-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const SettingCard = ({ title, icon: Icon, children }) => (\n    <Card className=\"p-6\">\n      <div className=\"flex items-center mb-4\">\n        <Icon className=\"h-6 w-6 text-blue-600 mr-3\" />\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n      </div>\n      <div className=\"space-y-4\">\n        {children}\n      </div>\n    </Card>\n  );\n\n  const InputField = ({ label, type = 'text', value, onChange, options = null, description = null }) => (\n    <div>\n      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n        {label}\n      </label>\n      {type === 'select' ? (\n        <select\n          value={value}\n          onChange={(e) => onChange(e.target.value)}\n          className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        >\n          {options?.map(option => (\n            <option key={option.value} value={option.value}>\n              {option.label}\n            </option>\n          ))}\n        </select>\n      ) : type === 'checkbox' ? (\n        <div className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={value}\n            onChange={(e) => onChange(e.target.checked)}\n            className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n          />\n          <span className=\"ml-2 text-sm text-gray-600\">Enable this security feature</span>\n        </div>\n      ) : type === 'textarea' ? (\n        <textarea\n          value={Array.isArray(value) ? value.join('\\n') : value}\n          onChange={(e) => onChange(type === 'array' ? e.target.value.split('\\n') : e.target.value)}\n          rows={3}\n          className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        />\n      ) : (\n        <input\n          type={type}\n          value={value}\n          onChange={(e) => onChange(type === 'number' ? parseInt(e.target.value) : e.target.value)}\n          className=\"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n        />\n      )}\n      {description && (\n        <p className=\"mt-1 text-xs text-gray-500\">{description}</p>\n      )}\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n            <LockClosedIcon className=\"h-8 w-8 mr-3 text-blue-600\" />\n            {t('securitySettings', 'Security Settings')}\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            {t('securitySettingsDescription', 'Configure security policies and access controls')}\n          </p>\n        </div>\n        <Button onClick={handleSave} disabled={loading}>\n          {t('saveSettings', 'Save Settings')}\n        </Button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Password Policy */}\n        <SettingCard title={t('passwordPolicy', 'Password Policy')} icon={KeyIcon}>\n          <InputField\n            label={t('minimumLength', 'Minimum Length')}\n            type=\"number\"\n            value={settings.passwordMinLength}\n            onChange={(value) => handleInputChange('passwordMinLength', value)}\n          />\n          <InputField\n            label={t('requireUppercase', 'Require Uppercase Letters')}\n            type=\"checkbox\"\n            value={settings.passwordRequireUppercase}\n            onChange={(value) => handleInputChange('passwordRequireUppercase', value)}\n          />\n          <InputField\n            label={t('requireNumbers', 'Require Numbers')}\n            type=\"checkbox\"\n            value={settings.passwordRequireNumbers}\n            onChange={(value) => handleInputChange('passwordRequireNumbers', value)}\n          />\n          <InputField\n            label={t('passwordExpiration', 'Password Expiration (days)')}\n            type=\"number\"\n            value={settings.passwordExpirationDays}\n            onChange={(value) => handleInputChange('passwordExpirationDays', value)}\n          />\n        </SettingCard>\n\n        {/* Account Security */}\n        <SettingCard title={t('accountSecurity', 'Account Security')} icon={ShieldCheckIcon}>\n          <InputField\n            label={t('maxLoginAttempts', 'Max Login Attempts')}\n            type=\"number\"\n            value={settings.maxLoginAttempts}\n            onChange={(value) => handleInputChange('maxLoginAttempts', value)}\n          />\n          <InputField\n            label={t('lockoutDuration', 'Lockout Duration (minutes)')}\n            type=\"number\"\n            value={settings.accountLockoutDuration}\n            onChange={(value) => handleInputChange('accountLockoutDuration', value)}\n          />\n          <InputField\n            label={t('sessionTimeout', 'Session Timeout (minutes)')}\n            type=\"number\"\n            value={settings.sessionTimeout}\n            onChange={(value) => handleInputChange('sessionTimeout', value)}\n          />\n          <InputField\n            label={t('enableTwoFactor', 'Enable Two-Factor Authentication')}\n            type=\"checkbox\"\n            value={settings.enableTwoFactor}\n            onChange={(value) => handleInputChange('enableTwoFactor', value)}\n          />\n        </SettingCard>\n\n        {/* Access Control */}\n        <SettingCard title={t('accessControl', 'Access Control')} icon={UserIcon}>\n          <InputField\n            label={t('enableIPWhitelist', 'Enable IP Whitelist')}\n            type=\"checkbox\"\n            value={settings.enableIPWhitelist}\n            onChange={(value) => handleInputChange('enableIPWhitelist', value)}\n          />\n          {settings.enableIPWhitelist && (\n            <InputField\n              label={t('allowedIPs', 'Allowed IP Addresses')}\n              type=\"textarea\"\n              value={settings.allowedIPs}\n              onChange={(value) => handleInputChange('allowedIPs', value)}\n              description=\"One IP address or CIDR block per line\"\n            />\n          )}\n          <InputField\n            label={t('enableGeoBlocking', 'Enable Geographic Blocking')}\n            type=\"checkbox\"\n            value={settings.enableGeoBlocking}\n            onChange={(value) => handleInputChange('enableGeoBlocking', value)}\n          />\n        </SettingCard>\n\n        {/* Data Protection */}\n        <SettingCard title={t('dataProtection', 'Data Protection')} icon={LockClosedIcon}>\n          <InputField\n            label={t('enableDataEncryption', 'Enable Data Encryption')}\n            type=\"checkbox\"\n            value={settings.enableDataEncryption}\n            onChange={(value) => handleInputChange('enableDataEncryption', value)}\n          />\n          <InputField\n            label={t('encryptionAlgorithm', 'Encryption Algorithm')}\n            type=\"select\"\n            value={settings.encryptionAlgorithm}\n            onChange={(value) => handleInputChange('encryptionAlgorithm', value)}\n            options={[\n              { value: 'AES-256', label: 'AES-256' },\n              { value: 'AES-128', label: 'AES-128' },\n              { value: 'ChaCha20', label: 'ChaCha20' }\n            ]}\n          />\n          <InputField\n            label={t('enableBackupEncryption', 'Enable Backup Encryption')}\n            type=\"checkbox\"\n            value={settings.enableBackupEncryption}\n            onChange={(value) => handleInputChange('enableBackupEncryption', value)}\n          />\n          <InputField\n            label={t('enableSSL', 'Enable SSL/TLS')}\n            type=\"checkbox\"\n            value={settings.enableSSL}\n            onChange={(value) => handleInputChange('enableSSL', value)}\n          />\n        </SettingCard>\n      </div>\n\n      {/* Security Events */}\n      <Card className=\"p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n          <ExclamationTriangleIcon className=\"h-6 w-6 text-yellow-600 mr-3\" />\n          {t('recentSecurityEvents', 'Recent Security Events')}\n        </h3>\n        <div className=\"space-y-3\">\n          {securityEvents.map((event) => (\n            <div key={event.id} className=\"flex items-center justify-between p-3 border border-gray-200 rounded-lg\">\n              <div className=\"flex items-center\">\n                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>\n                  {event.severity.toUpperCase()}\n                </span>\n                <div className=\"ml-3\">\n                  <p className=\"font-medium text-gray-900\">{event.description}</p>\n                  <p className=\"text-sm text-gray-500\">User: {event.user}</p>\n                </div>\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-sm text-gray-500\">\n                  {new Date(event.timestamp).toLocaleString()}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default SecuritySettings;\n", "import * as React from \"react\";\nfunction ShieldCheckIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShieldCheckIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;"], "names": ["_ref", "children", "variant", "size", "disabled", "loading", "className", "type", "onClick", "props", "_objectWithoutProperties", "_excluded", "variants", "primary", "secondary", "outline", "danger", "success", "warning", "ghost", "sizes", "xs", "sm", "md", "lg", "xl", "buttonClasses", "filter", "Boolean", "join", "_jsxs", "_objectSpread", "e", "preventDefault", "xmlns", "fill", "viewBox", "_jsx", "cx", "cy", "r", "stroke", "strokeWidth", "d", "padding", "shadow", "border", "rounded", "background", "hover", "cardClasses", "LockClosedIcon", "svgRef", "title", "titleId", "React", "Object", "assign", "ref", "id", "strokeLinecap", "strokeLinejoin", "t", "o", "i", "n", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "length", "propertyIsEnumerable", "KeyIcon", "UserIcon", "SecuritySettings", "useTranslation", "setLoading", "useState", "settings", "setSettings", "<PERSON><PERSON>in<PERSON><PERSON><PERSON>", "passwordRequireUppercase", "passwordRequireLowercase", "passwordRequireNumbers", "passwordRequireSpecialChars", "passwordExpirationDays", "passwordHistoryCount", "maxLogin<PERSON><PERSON><PERSON>s", "accountLockoutDuration", "sessionTimeout", "enableTwoFactor", "requireTwoFactorForAdmin", "enable<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedIPs", "enableGeoBlocking", "blockedCountries", "enableLoginAudit", "enableActionAudit", "auditRetentionDays", "enableRealTimeAlerts", "enableDataEncryption", "encryptionAlgorithm", "enableBackupEncryption", "enableSSL", "sslCertificateExpiry", "securityEvents", "setSecurityEvents", "user", "timestamp", "Date", "toISOString", "severity", "description", "now", "useEffect", "loadSecuritySettings", "async", "error", "console", "toast", "handleInputChange", "field", "value", "prev", "getSeverityColor", "SettingCard", "icon", "Icon", "Card", "InputField", "_ref2", "label", "onChange", "options", "target", "map", "option", "checked", "Array", "isArray", "split", "rows", "parseInt", "LoadingSpinner", "<PERSON><PERSON>", "ShieldCheckIcon", "ExclamationTriangleIcon", "event", "concat", "toUpperCase", "toLocaleString"], "sourceRoot": ""}