{"version": 3, "file": "static/js/276.a82cc9d4.chunk.js", "mappings": "yMAIA,MAwkBA,EAxkBkBA,KAChB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,cACpCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAkBC,IAAuBN,EAAAA,EAAAA,UAAS,OAClDO,EAAmBC,IAAwBR,EAAAA,EAAAA,WAAS,IAGpDS,EAAoBC,IAAyBV,EAAAA,EAAAA,UAAS,CAC3D,CACEW,GAAI,EACJC,KAAM,cACNC,OAAQ,sEACRC,KAAM,UACNC,OAAQ,SACRC,cAAe,aACfC,YAAa,aACbC,cAAe,IACfC,WAAY,IACZC,gBAAiB,GACjBC,cAAe,kBACfC,MAAO,mBACPC,MAAO,oBACPC,QAAS,uBACTC,cAAe,IACfC,YAAa,GACbC,eAAgB,GAChBC,cAAe,EACfC,eAAgB,EAChBC,gBAAiB,OAEnB,CACEnB,GAAI,EACJC,KAAM,WACNC,OAAQ,yDACRC,KAAM,cACNC,OAAQ,SACRC,cAAe,aACfC,YAAa,aACbC,cAAe,IACfC,WAAY,IACZC,gBAAiB,GACjBC,cAAe,kBACfC,MAAO,mBACPC,MAAO,yBACPC,QAAS,uBACTC,cAAe,GACfC,YAAa,GACbC,eAAgB,GAChBC,cAAe,EACfC,eAAgB,EAChBC,gBAAiB,MAEnB,CACEnB,GAAI,EACJC,KAAM,kBACNC,OAAQ,0IACRC,KAAM,aACNC,OAAQ,SACRC,cAAe,aACfC,YAAa,aACbC,cAAe,KACfC,WAAY,IACZC,gBAAiB,GACjBC,cAAe,qBACfC,MAAO,mBACPC,MAAO,uBACPC,QAAS,uBACTC,cAAe,GACfC,YAAa,GACbC,eAAgB,GAChBC,cAAe,EACfC,eAAgB,EAChBC,gBAAiB,SAKdC,EAAiBC,IAAsBhC,EAAAA,EAAAA,UAAS,CACrD,CACEW,GAAI,eACJsB,YAAa,kBACbC,UAAW,OACXC,SAAU,cACVC,cAAe,mBACfC,YAAa,KACbC,eAAgB,KAChBvB,OAAQ,WACRwB,eAAgB,aAChBC,aAAc,aACdC,UAAW,kBACXC,eAAgB,2BAChBC,UAAW,mBAEb,CACEhC,GAAI,eACJsB,YAAa,kBACbC,UAAW,OACXC,SAAU,WACVC,cAAe,uBACfC,YAAa,IACbC,eAAgB,EAChBvB,OAAQ,UACRwB,eAAgB,aAChBC,aAAc,KACdC,UAAW,2BACXC,eAAgB,2BAChBC,UAAW,mBAEb,CACEhC,GAAI,eACJsB,YAAa,qBACbC,UAAW,OACXC,SAAU,kBACVC,cAAe,iBACfC,YAAa,KACbC,eAAgB,IAChBvB,OAAQ,WACRwB,eAAgB,aAChBC,aAAc,aACdC,UAAW,eACXC,eAAgB,2BAChBC,UAAW,qBAEb,CACEhC,GAAI,eACJsB,YAAa,mBACbC,UAAW,OACXC,SAAU,cACVC,cAAe,mBACfC,YAAa,KACbC,eAAgB,EAChBvB,OAAQ,WACRwB,eAAgB,aAChBC,aAAc,aACdC,UAAW,eACXC,eAAgB,2BAChBC,UAAW,kBACXC,gBAAiB,gCAIfC,EAAO,CACX,CAAElC,GAAI,YAAamC,MAAOnD,EAAE,qBAAsB,uBAAwBoD,KAAM,mBAChF,CAAEpC,GAAI,SAAUmC,MAAOnD,EAAE,SAAU,UAAWoD,KAAM,uBACpD,CAAEpC,GAAI,eAAgBmC,MAAOnD,EAAE,eAAgB,gBAAiBoD,KAAM,uBACtE,CAAEpC,GAAI,UAAWmC,MAAOnD,EAAE,UAAW,WAAYoD,KAAM,qBAGnDC,EAAkBC,GACf,IAAIC,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,QACTC,OAAOL,GAGNM,EAAkBxC,IACtB,OAAQA,EAAOyC,eACb,IAAK,WAGL,IAAK,SAAU,MAAO,mDAFtB,IAAK,UAAW,MAAO,sDACvB,IAAK,WAAY,MAAO,6CAExB,QAAS,MAAO,kDAiWpB,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAS,aAAAC,OAAe/D,EAAQ,cAAgB,gBAAiBgE,SAAA,EAEpEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DjE,EAAE,sBAAuB,2BAE5BkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjDjE,EAAE,uBAAwB,uEAI/BkE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8BAA6BE,UAC1CH,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oFAAmFE,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6BACZ/D,EAAE,oBAAqB,+BAM9BkE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gDAA+CE,UAC5DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBE,SACnCf,EAAKiB,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMjE,EAAagE,EAAIpD,IAChC+C,UAAS,4CAAAC,OACP7D,IAAciE,EAAIpD,GACd,mDACA,0HACHiD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKI,EAAIhB,KAAI,WACxBgB,EAAIjB,QATAiB,EAAIpD,SAgBF,cAAdb,IA7WH2D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEjE,EAAE,iBAAkB,sBAEvBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DnD,EAAmBwD,kBAM5BJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEjE,EAAE,uBAAwB,6BAE7BkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DnD,EAAmByD,OAAO,CAACC,EAAKhC,IAAagC,EAAMhC,EAASV,cAAe,cAMpFoC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEjE,EAAE,kBAAmB,uBAExBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DZ,EAAevC,EAAmByD,OAAO,CAACC,EAAKhC,IAAagC,EAAMhC,EAASL,gBAAiB,kBAQvG2B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FE,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChEjE,EAAE,qBAAsB,0BAE3B8D,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFE,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZ/D,EAAE,cAAe,uBAItBkE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,WAAY,eAEjBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,OAAQ,WAEbkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,WAAY,eAEjBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,SAAU,aAEfkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,aAAc,iBAEnBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,SAAU,aAEfkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,UAAW,mBAIpBkE,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvFnD,EAAmBqD,IAAK3B,IACvBsB,EAAAA,EAAAA,MAAA,MAAsBC,UAAU,0CAAyCE,SAAA,EACvEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DzB,EAASvB,QAEZiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDzB,EAAStB,eAIhBgD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,8GAA6GE,SAC1HzB,EAASrB,UAGd+C,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EzB,EAASV,iBAEZgC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,CACnDzB,EAAST,YAAY,IAAE/B,EAAE,QAAS,aAErC8D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtDzB,EAASR,eAAe,IAAEhC,EAAE,WAAY,YAAY,KAAGwC,EAASP,cAAc,IAAEjC,EAAE,UAAW,kBAGlGkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,gFAA+EE,SAC1FZ,EAAeb,EAASL,oBAE3B+B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAepB,EAASpB,SAAU6C,SAC9FzB,EAASpB,YAGd0C,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDE,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAM1D,EAAoB6B,GACnCuB,UAAU,qFAAoFE,SAE7FjE,EAAE,OAAQ,WAEbkE,EAAAA,EAAAA,KAAA,UAAQH,UAAU,oFAAmFE,SAClGjE,EAAE,OAAQ,eA3CRwC,EAASxB,kBAiRb,WAAdb,IAzNH2D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEjE,EAAE,cAAe,mBAEpBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5D7B,EAAgBkC,kBAMzBJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEjE,EAAE,iBAAkB,eAEvBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5D7B,EAAgBqC,OAAOC,GAA0B,aAAjBA,EAAMtD,QAAuBkD,kBAMtEJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEjE,EAAE,gBAAiB,cAEtBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5D7B,EAAgBqC,OAAOC,GAA0B,YAAjBA,EAAMtD,QAAsBkD,kBAMrEJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+CAA8CE,UAC3DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEjE,EAAE,iBAAkB,eAEvBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5D7B,EAAgBqC,OAAOC,GAA0B,aAAjBA,EAAMtD,QAAuBkD,qBAQxER,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FE,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChEjE,EAAE,kBAAmB,uBAExB8D,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAMxD,GAAqB,GACpCkD,UAAU,kFAAiFE,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZ/D,EAAE,cAAe,uBAItBkE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,UAAW,eAEhBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,UAAW,cAEhBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,WAAY,eAEjBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,YAAa,gBAElBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,SAAU,aAEfkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,SAAU,aAEfkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,UAAW,mBAIpBkE,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvF7B,EAAgB+B,IAAKO,IACpBZ,EAAAA,EAAAA,MAAA,MAAmBC,UAAU,0CAAyCE,SAAA,EACpEH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DS,EAAM1D,MAETkD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDS,EAAM9B,qBAGXkB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DS,EAAMpC,eAETwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CAAC,OACnDS,EAAMnC,iBAGf2B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9ES,EAAMlC,YAETsB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDS,EAAMjC,iBAETyB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDS,EAAM5B,gBAGXgB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DZ,EAAeqB,EAAMhC,eAEvBgC,EAAM/B,eAAiB,IACtBmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,CACxDZ,EAAeqB,EAAM/B,gBAAgB,IAAE3C,EAAE,WAAY,mBAI5DkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAec,EAAMtD,SAAU6C,SAC3FS,EAAMtD,YAGX0C,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDE,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,qFAAoFE,SACnGjE,EAAE,OAAQ,UAEK,YAAjB0E,EAAMtD,SACL8C,EAAAA,EAAAA,KAAA,UAAQH,UAAU,oFAAmFE,SAClGjE,EAAE,QAAS,gBAjDX0E,EAAM1D,kBA0GV,iBAAdb,IACC2D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEjE,EAAE,0BAA2B,+BAEhCkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CjE,EAAE,0BAA2B,4DAIrB,YAAdG,IACC2D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEjE,EAAE,mBAAoB,wBAEzBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CjE,EAAE,qBAAsB,2D", "sources": ["pages/Insurance/Insurance.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst Insurance = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('providers');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [selectedProvider, setSelectedProvider] = useState(null);\n  const [showNewClaimModal, setShowNewClaimModal] = useState(false);\n\n  // Mock data for insurance providers\n  const [insuranceProviders, setInsuranceProviders] = useState([\n    {\n      id: 1,\n      name: 'Bupa Arabia',\n      nameAr: 'بوبا العربية',\n      type: 'Private',\n      status: 'Active',\n      contractStart: '2024-01-01',\n      contractEnd: '2024-12-31',\n      coverageLimit: 50000,\n      deductible: 500,\n      copayPercentage: 20,\n      contactPerson: '<PERSON>',\n      phone: '+966-11-123-4567',\n      email: '<EMAIL>',\n      address: 'Riyadh, Saudi Arabia',\n      totalPatients: 145,\n      totalClaims: 89,\n      approvedClaims: 78,\n      pendingClaims: 8,\n      rejectedClaims: 3,\n      totalReimbursed: 125000\n    },\n    {\n      id: 2,\n      name: 'Tawuniya',\n      nameAr: 'التعاونية',\n      type: 'Cooperative',\n      status: 'Active',\n      contractStart: '2024-01-01',\n      contractEnd: '2024-12-31',\n      coverageLimit: 30000,\n      deductible: 300,\n      copayPercentage: 15,\n      contactPerson: 'Fatima Al-Zahra',\n      phone: '+966-11-234-5678',\n      email: '<EMAIL>',\n      address: 'Jeddah, Saudi Arabia',\n      totalPatients: 98,\n      totalClaims: 67,\n      approvedClaims: 58,\n      pendingClaims: 6,\n      rejectedClaims: 3,\n      totalReimbursed: 89000\n    },\n    {\n      id: 3,\n      name: 'CCHI Government',\n      nameAr: 'مجلس الضمان الصحي الحكومي',\n      type: 'Government',\n      status: 'Active',\n      contractStart: '2024-01-01',\n      contractEnd: '2024-12-31',\n      coverageLimit: 25000,\n      deductible: 200,\n      copayPercentage: 10,\n      contactPerson: 'Mohammed Al-Otaibi',\n      phone: '+966-11-345-6789',\n      email: '<EMAIL>',\n      address: 'Riyadh, Saudi Arabia',\n      totalPatients: 67,\n      totalClaims: 45,\n      approvedClaims: 38,\n      pendingClaims: 5,\n      rejectedClaims: 2,\n      totalReimbursed: 56000\n    }\n  ]);\n\n  // Mock data for insurance claims\n  const [insuranceClaims, setInsuranceClaims] = useState([\n    {\n      id: 'CLM-2024-001',\n      patientName: 'Ahmed Al-Rashid',\n      patientId: 'P001',\n      provider: 'Bupa Arabia',\n      treatmentType: 'Physical Therapy',\n      claimAmount: 1500,\n      approvedAmount: 1200,\n      status: 'Approved',\n      submissionDate: '2024-02-15',\n      approvalDate: '2024-02-18',\n      diagnosis: 'Lower back pain',\n      treatmentDates: '2024-02-10 to 2024-02-14',\n      therapist: 'Dr. Sarah Ahmed'\n    },\n    {\n      id: 'CLM-2024-002',\n      patientName: 'Fatima Al-Zahra',\n      patientId: 'P002',\n      provider: 'Tawuniya',\n      treatmentType: 'Occupational Therapy',\n      claimAmount: 2000,\n      approvedAmount: 0,\n      status: 'Pending',\n      submissionDate: '2024-02-14',\n      approvalDate: null,\n      diagnosis: 'Autism spectrum disorder',\n      treatmentDates: '2024-02-08 to 2024-02-12',\n      therapist: 'Dr. Mona Hassan'\n    },\n    {\n      id: 'CLM-2024-003',\n      patientName: 'Mohammed Al-Otaibi',\n      patientId: 'P003',\n      provider: 'CCHI Government',\n      treatmentType: 'Speech Therapy',\n      claimAmount: 1200,\n      approvedAmount: 1000,\n      status: 'Approved',\n      submissionDate: '2024-02-13',\n      approvalDate: '2024-02-16',\n      diagnosis: 'Speech delay',\n      treatmentDates: '2024-02-05 to 2024-02-09',\n      therapist: 'Dr. Layla Ibrahim'\n    },\n    {\n      id: 'CLM-2024-004',\n      patientName: 'Nora Al-Mansouri',\n      patientId: 'P004',\n      provider: 'Bupa Arabia',\n      treatmentType: 'Physical Therapy',\n      claimAmount: 1800,\n      approvedAmount: 0,\n      status: 'Rejected',\n      submissionDate: '2024-02-12',\n      approvalDate: '2024-02-15',\n      diagnosis: 'Chronic pain',\n      treatmentDates: '2024-02-01 to 2024-02-05',\n      therapist: 'Dr. Omar Khalil',\n      rejectionReason: 'Pre-authorization required'\n    }\n  ]);\n\n  const tabs = [\n    { id: 'providers', label: t('insuranceProviders', 'Insurance Providers'), icon: 'fas fa-building' },\n    { id: 'claims', label: t('claims', 'Claims'), icon: 'fas fa-file-invoice' },\n    { id: 'verification', label: t('verification', 'Verification'), icon: 'fas fa-check-circle' },\n    { id: 'reports', label: t('reports', 'Reports'), icon: 'fas fa-chart-bar' }\n  ];\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'approved': return 'text-green-600 bg-green-100 dark:bg-green-900/30';\n      case 'pending': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';\n      case 'rejected': return 'text-red-600 bg-red-100 dark:bg-red-900/30';\n      case 'active': return 'text-green-600 bg-green-100 dark:bg-green-900/30';\n      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';\n    }\n  };\n\n  const handleSubmitClaim = async (claimData) => {\n    setLoading(true);\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const newClaim = {\n        id: `CLM-2024-${String(insuranceClaims.length + 1).padStart(3, '0')}`,\n        ...claimData,\n        status: 'Pending',\n        submissionDate: new Date().toISOString().split('T')[0],\n        approvalDate: null,\n        approvedAmount: 0\n      };\n      \n      setInsuranceClaims(prev => [newClaim, ...prev]);\n      setShowNewClaimModal(false);\n      toast.success(t('claimSubmittedSuccessfully', 'Claim submitted successfully'));\n    } catch (error) {\n      toast.error(t('errorSubmittingClaim', 'Error submitting claim'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderProvidersTab = () => (\n    <div className=\"space-y-6\">\n      {/* Providers Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-building text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalProviders', 'Total Providers')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {insuranceProviders.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-users text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalCoveredPatients', 'Total Covered Patients')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {insuranceProviders.reduce((sum, provider) => sum + provider.totalPatients, 0)}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-dollar-sign text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalReimbursed', 'Total Reimbursed')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(insuranceProviders.reduce((sum, provider) => sum + provider.totalReimbursed, 0))}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Providers List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('insuranceProviders', 'Insurance Providers')}\n          </h3>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('addProvider', 'Add Provider')}\n          </button>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('provider', 'Provider')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('type', 'Type')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patients', 'Patients')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('claims', 'Claims')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('reimbursed', 'Reimbursed')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {insuranceProviders.map((provider) => (\n                <tr key={provider.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {provider.name}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {provider.nameAr}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"px-2 py-1 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300\">\n                      {provider.type}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {provider.totalPatients}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {provider.totalClaims} {t('total', 'total')}\n                    </div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {provider.approvedClaims} {t('approved', 'approved')}, {provider.pendingClaims} {t('pending', 'pending')}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatCurrency(provider.totalReimbursed)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(provider.status)}`}>\n                      {provider.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button\n                      onClick={() => setSelectedProvider(provider)}\n                      className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3\"\n                    >\n                      {t('view', 'View')}\n                    </button>\n                    <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\">\n                      {t('edit', 'Edit')}\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderClaimsTab = () => (\n    <div className=\"space-y-6\">\n      {/* Claims Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-file-invoice text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalClaims', 'Total Claims')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {insuranceClaims.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('approvedClaims', 'Approved')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {insuranceClaims.filter(claim => claim.status === 'Approved').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('pendingClaims', 'Pending')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {insuranceClaims.filter(claim => claim.status === 'Pending').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg\">\n              <i className=\"fas fa-times-circle text-red-600 dark:text-red-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('rejectedClaims', 'Rejected')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {insuranceClaims.filter(claim => claim.status === 'Rejected').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Claims List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('insuranceClaims', 'Insurance Claims')}\n          </h3>\n          <button\n            onClick={() => setShowNewClaimModal(true)}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('submitClaim', 'Submit Claim')}\n          </button>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('claimId', 'Claim ID')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('provider', 'Provider')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('treatment', 'Treatment')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('amount', 'Amount')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {insuranceClaims.map((claim) => (\n                <tr key={claim.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {claim.id}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {claim.submissionDate}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {claim.patientName}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      ID: {claim.patientId}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {claim.provider}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {claim.treatmentType}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {claim.diagnosis}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {formatCurrency(claim.claimAmount)}\n                    </div>\n                    {claim.approvedAmount > 0 && (\n                      <div className=\"text-sm text-green-600 dark:text-green-400\">\n                        {formatCurrency(claim.approvedAmount)} {t('approved', 'approved')}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(claim.status)}`}>\n                      {claim.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3\">\n                      {t('view', 'View')}\n                    </button>\n                    {claim.status === 'Pending' && (\n                      <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\">\n                        {t('track', 'Track')}\n                      </button>\n                    )}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('insuranceManagement', 'Insurance Management')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('insuranceDescription', 'Manage insurance providers, claims, and coverage verification')}\n          </p>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n            <i className=\"fas fa-check-circle mr-2\"></i>\n            {t('verifyEligibility', 'Verify Eligibility')}\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <i className={`${tab.icon} mr-2`}></i>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'providers' && renderProvidersTab()}\n      {activeTab === 'claims' && renderClaimsTab()}\n      {activeTab === 'verification' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('eligibilityVerification', 'Eligibility Verification')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('verificationDescription', 'Real-time insurance eligibility verification system')}\n          </p>\n        </div>\n      )}\n      {activeTab === 'reports' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('insuranceReports', 'Insurance Reports')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('reportsDescription', 'Comprehensive insurance analytics and reporting')}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Insurance;\n"], "names": ["Insurance", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "searchTerm", "setSearchTerm", "loading", "setLoading", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedProvider", "showNewClaimModal", "setShowNewClaimModal", "insuranceProviders", "setInsuranceProviders", "id", "name", "nameAr", "type", "status", "contractStart", "contractEnd", "coverageLimit", "deductible", "copayPercentage", "<PERSON><PERSON><PERSON>", "phone", "email", "address", "totalPatients", "totalClaims", "approvedClaims", "pendingClaims", "rejectedClaims", "totalReimbursed", "insuranceClaims", "setInsuranceClaims", "patientName", "patientId", "provider", "treatmentType", "claimAmount", "approvedAmount", "submissionDate", "approvalDate", "diagnosis", "treatmentDates", "therapist", "rejectionReason", "tabs", "label", "icon", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "toLowerCase", "_jsxs", "className", "concat", "children", "_jsx", "map", "tab", "onClick", "length", "reduce", "sum", "filter", "claim"], "sourceRoot": ""}