"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9277],{930:(e,t,s)=>{s.d(t,{A:()=>i});var a=s(2555);const i=new class{constructor(){this.baseURL="http://localhost:5001/api/v1",this.storagePrefix="physioflow_",this.initializeStorage()}getAuthToken(){return localStorage.getItem("token")||"demo-token"}async apiRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=this.getAuthToken(),i="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}},n=(0,a.A)((0,a.A)((0,a.A)({},r),t),{},{headers:(0,a.A)((0,a.A)({},r.headers),t.headers)});try{const e=await fetch(i,n);if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return await e.json()}catch(o){return console.warn("API request failed for ".concat(e,", using localStorage fallback:"),o.message),this.fallbackToLocalStorage(e,t)}}fallbackToLocalStorage(e,t){const s=t.method||"GET",a=t.body?JSON.parse(t.body):null;return e.includes("/bodymap")?this.handleBodyMapFallback(e,s,a):e.includes("/communication")?this.handleCommunicationFallback(e,s,a):e.includes("/exercise-programs")?this.handleExerciseFallback(e,s,a):e.includes("/ai-interactions")?this.handleAIFallback(e,s,a):{success:!1,error:"Endpoint not supported in fallback mode"}}initializeStorage(){this.getItem("initialized")||(this.setItem("initialized",!0),this.setItem("patients",this.getDefaultPatients()),this.setItem("bodyMapData",{}),this.setItem("communicationHistory",[]),this.setItem("exercisePrograms",[]),this.setItem("aiInteractions",[]))}getItem(e){try{const t=localStorage.getItem(this.storagePrefix+e);return t?JSON.parse(t):null}catch(t){return console.error("Error getting item from storage:",t),null}}setItem(e,t){try{return localStorage.setItem(this.storagePrefix+e,JSON.stringify(t)),!0}catch(s){return console.error("Error setting item in storage:",s),!1}}removeItem(e){try{return localStorage.removeItem(this.storagePrefix+e),!0}catch(t){return console.error("Error removing item from storage:",t),!1}}getDefaultPatients(){return[{id:"demo-patient-001",name:"Ahmed Mohammed",nameAr:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f",age:28,gender:"male",condition:"Cerebral Palsy",conditionAr:"\u0627\u0644\u0634\u0644\u0644 \u0627\u0644\u062f\u0645\u0627\u063a\u064a",phone:"+966501234567",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","whatsapp"],language:"en",quietHours:{start:"22:00",end:"08:00"}},createdAt:(new Date).toISOString()},{id:"demo-patient-002",name:"Sarah Johnson",nameAr:"\u0633\u0627\u0631\u0629 \u062c\u0648\u0646\u0633\u0648\u0646",age:35,gender:"female",condition:"Spinal Cord Injury",conditionAr:"\u0625\u0635\u0627\u0628\u0629 \u0627\u0644\u062d\u0628\u0644 \u0627\u0644\u0634\u0648\u0643\u064a",phone:"+**********",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","sms"],language:"en",quietHours:{start:"21:00",end:"07:00"}},createdAt:(new Date).toISOString()}]}async getPatients(){return new Promise(e=>{setTimeout(()=>{e(this.getItem("patients")||[])},100)})}async getPatient(e){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("patients")||[]).find(t=>t.id===e);t(s||null)},100)})}async saveBodyMapData(e,t){try{const s=await this.apiRequest("/bodymap",{method:"POST",body:JSON.stringify((0,a.A)({patientId:e},t))});return s.success?s.data:null}catch(s){return new Promise(s=>{setTimeout(()=>{const i=this.getItem("bodyMapData")||{};i[e]=(0,a.A)((0,a.A)({},t),{},{timestamp:(new Date).toISOString()}),this.setItem("bodyMapData",i),s(!0)},200)})}}async getBodyMapData(e){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("bodyMapData")||{};t(s[e]||null)},100)})}async sendMessage(e){try{const t=await this.apiRequest("/communication",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("communicationHistory")||[],i=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString(),status:"sent"});s.push(i),this.setItem("communicationHistory",s),t(i)},500)})}}async getCommunicationHistory(e){try{const t=await this.apiRequest("/communication/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("communicationHistory")||[]).filter(t=>t.patientId===e);t(s)},100)})}}async saveExerciseProgram(e){try{const t=await this.apiRequest("/exercise-programs",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("exercisePrograms")||[],i=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{createdAt:(new Date).toISOString()});s.push(i),this.setItem("exercisePrograms",s),t(i)},300)})}}async getExercisePrograms(e){try{const t=await this.apiRequest("/exercise-programs/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("exercisePrograms")||[]).filter(t=>t.patientId===e);t(s)},100)})}}async saveAIInteraction(e){try{const t=await this.apiRequest("/ai-interactions",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("aiInteractions")||[],i=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString()});s.push(i),this.setItem("aiInteractions",s),t(i)},100)})}}async getAIInteractions(e){try{const t=e?"/ai-interactions/".concat(e):"/ai-interactions",s=await this.apiRequest(t);return s.success?s.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("aiInteractions")||[],a=e?s.filter(t=>t.patientId===e):s;t(a)},100)})}}async getAnalytics(){return new Promise(e=>{setTimeout(()=>{const t=this.getItem("aiInteractions")||[],s=this.getItem("communicationHistory")||[],i=this.getItem("exercisePrograms")||[],r=this.getItem("bodyMapData")||{},n={totalAIQueries:t.length,totalCommunications:s.length,totalExercisePrograms:i.length,totalBodyMapAssessments:Object.keys(r).length,recentActivity:[...t.slice(-5).map(e=>(0,a.A)({type:"ai"},e)),...s.slice(-5).map(e=>(0,a.A)({type:"communication"},e)),...i.slice(-5).map(e=>(0,a.A)({type:"exercise"},e))].sort((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)).slice(0,10)};e(n)},200)})}clearAllData(){["patients","bodyMapData","communicationHistory","exercisePrograms","aiInteractions"].forEach(e=>this.removeItem(e)),this.removeItem("initialized"),this.initializeStorage()}exportData(){return{patients:this.getItem("patients"),bodyMapData:this.getItem("bodyMapData"),communicationHistory:this.getItem("communicationHistory"),exercisePrograms:this.getItem("exercisePrograms"),aiInteractions:this.getItem("aiInteractions"),exportedAt:(new Date).toISOString()}}importData(e){try{return e.patients&&this.setItem("patients",e.patients),e.bodyMapData&&this.setItem("bodyMapData",e.bodyMapData),e.communicationHistory&&this.setItem("communicationHistory",e.communicationHistory),e.exercisePrograms&&this.setItem("exercisePrograms",e.exercisePrograms),e.aiInteractions&&this.setItem("aiInteractions",e.aiInteractions),!0}catch(t){return console.error("Error importing data:",t),!1}}delay(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;return new Promise(t=>setTimeout(t,e))}}},9277:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var a=s(5043),i=s(7921),r=s(3216),n=s(2770),o=s(930),c=s(3768),l=s(579);const m=()=>{var e;const{t:t,isRTL:s}=(0,i.o)(),{patientId:m}=(0,r.g)(),d=(0,r.Zp)(),[g,h]=(0,a.useState)(null),[x,u]=(0,a.useState)([]),[p,y]=(0,a.useState)(!0),[b,f]=(0,a.useState)(null);(0,a.useEffect)(()=>{v(),w()},[m]);const v=async()=>{if(m)try{const e=await o.A.getPatient(m);h(e)}catch(e){console.error("Error loading patient:",e),c.Ay.error(t("errorLoadingPatient","Error loading patient data"))}y(!1)},w=async()=>{if(m)try{const e=await o.A.getBodyMapData(m);e&&u([e])}catch(e){console.error("Error loading assessment history:",e)}};return p?(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,l.jsxs)("div",{className:"body-map-page min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsx)("div",{className:"py-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("button",{onClick:()=>d(-1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,l.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,l.jsx)("i",{className:"fas fa-user-md mr-3 text-blue-600"}),t("bodyMapAssessment","Body Map Assessment")]}),g&&(0,l.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mt-1",children:[t("patient","Patient"),": ",s?g.nameAr:g.name,g.condition&&(0,l.jsx)("span",{className:"ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded",children:s?g.conditionAr:g.condition})]})]})]}),(0,l.jsx)("div",{className:"flex items-center space-x-3",children:(0,l.jsxs)("button",{onClick:()=>{f(null)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2",children:[(0,l.jsx)("i",{className:"fas fa-plus"}),(0,l.jsx)("span",{children:t("newAssessment","New Assessment")})]})})]})})})}),(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,l.jsx)("div",{className:"xl:col-span-3",children:(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:t("interactiveBodyMap","Interactive Body Map")}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,l.jsx)("i",{className:"fas fa-universal-access mr-1"}),t("accessible","Accessible")]}),(0,l.jsxs)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,l.jsx)("i",{className:"fas fa-language mr-1"}),t("multilingual","Multilingual")]})]})]}),(0,l.jsx)(n.A,{patientId:m,onSave:async e=>{try{await o.A.saveBodyMapData(m,e),await w(),c.Ay.success(t("assessmentSaved","Assessment saved successfully"))}catch(s){console.error("Error saving assessment:",s),c.Ay.error(t("errorSaving","Error saving assessment"))}},showInstructions:!0,selectedRegions:(null===b||void 0===b?void 0:b.selectedRegions)||[],painLevels:(null===b||void 0===b?void 0:b.painLevels)||{}})]})}),(0,l.jsxs)("div",{className:"xl:col-span-1",children:[(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,l.jsx)("i",{className:"fas fa-history mr-2 text-gray-600"}),t("assessmentHistory","Assessment History")]}),x.length>0?(0,l.jsx)("div",{className:"space-y-3",children:x.map((e,s)=>(0,l.jsxs)("div",{className:"p-3 border rounded-lg cursor-pointer transition-colors ".concat(b===e?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"),onClick:()=>f(e),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:new Date(e.timestamp).toLocaleDateString()}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:[Object.keys(e.selectedRegions||{}).length," ",t("regions","regions")]})]}),(0,l.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1",children:new Date(e.timestamp).toLocaleTimeString()}),e.selectedRegions&&e.selectedRegions.length>0&&(0,l.jsxs)("div",{className:"mt-2",children:[(0,l.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[t("affectedRegions","Affected Regions"),":"]}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.selectedRegions.slice(0,3).map((e,t)=>(0,l.jsx)("span",{className:"text-xs bg-red-100 text-red-800 px-2 py-1 rounded",children:e},t)),e.selectedRegions.length>3&&(0,l.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.selectedRegions.length-3," ",t("more","more")]})]})]})]},s))}):(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 mb-4"}),(0,l.jsx)("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:t("noAssessments","No assessments yet")}),(0,l.jsx)("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:t("clickBodyRegions","Click on body regions to start assessment")})]})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mt-6",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,l.jsx)("i",{className:"fas fa-chart-bar mr-2 text-gray-600"}),t("quickStats","Quick Stats")]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:t("totalAssessments","Total Assessments")}),(0,l.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x.length})]}),b&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:t("affectedRegions","Affected Regions")}),(0,l.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:(null===(e=b.selectedRegions)||void 0===e?void 0:e.length)||0})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:t("avgPainLevel","Avg Pain Level")}),(0,l.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:b.painLevels?(Object.values(b.painLevels).reduce((e,t)=>e+t,0)/Object.values(b.painLevels).length).toFixed(1):"0"})]})]})]})]})]})]})})]})}}}]);
//# sourceMappingURL=9277.300ca70a.chunk.js.map