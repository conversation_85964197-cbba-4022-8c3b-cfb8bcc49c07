"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6950,9890],{6950:(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var s=a(5043),r=a(7921),l=a(9890),i=a(2555),n=a(579);const o=()=>{const{t:e,isRTL:t}=(0,r.o)(),[a,l]=(0,s.useState)([]),[o,d]=(0,s.useState)([]),[c,m]=(0,s.useState)({status:"all",treatmentType:"all",completionRate:"all",dateRange:"all"}),[x,g]=(0,s.useState)(""),[h,p]=(0,s.useState)("name"),[u,y]=(0,s.useState)("asc"),[b,v]=(0,s.useState)(!0);(0,s.useEffect)(()=>{const e=[{id:1,name:"<PERSON>",age:28,condition:"Spinal Injury",treatmentType:"Physical Therapy",status:"In Progress",startDate:"2024-01-15",expectedEndDate:"2024-03-15",completionRate:65,sessionsCompleted:13,totalSessions:20,lastSession:"2024-02-08",nextSession:"2024-02-10",therapist:"Dr. Sarah Ahmed",goals:[{goal:"Improve mobility",progress:70,status:"On Track"},{goal:"Reduce pain",progress:80,status:"Ahead"},{goal:"Increase strength",progress:55,status:"Behind"}],functionalScores:{initial:45,current:68,target:85},notes:"Patient showing good progress in mobility exercises"},{id:2,name:"Fatima Hassan",age:34,condition:"Stroke Recovery",treatmentType:"Occupational Therapy",status:"Completed",startDate:"2023-11-01",endDate:"2024-01-30",completionRate:100,sessionsCompleted:24,totalSessions:24,lastSession:"2024-01-30",therapist:"Dr. Mohammed Ali",goals:[{goal:"Regain fine motor skills",progress:95,status:"Achieved"},{goal:"Improve coordination",progress:90,status:"Achieved"},{goal:"Daily living activities",progress:88,status:"Achieved"}],functionalScores:{initial:32,current:82,target:80},notes:"Successfully completed treatment with excellent outcomes"},{id:3,name:"Omar Khalil",age:12,condition:"Autism Spectrum",treatmentType:"Speech Therapy",status:"In Progress",startDate:"2024-01-08",expectedEndDate:"2024-04-08",completionRate:45,sessionsCompleted:9,totalSessions:20,lastSession:"2024-02-05",nextSession:"2024-02-12",therapist:"Dr. Fatima Hassan",goals:[{goal:"Improve communication",progress:50,status:"On Track"},{goal:"Social interaction",progress:40,status:"Behind"},{goal:"Vocabulary expansion",progress:60,status:"Ahead"}],functionalScores:{initial:28,current:42,target:70},notes:"Good progress in vocabulary, needs more work on social skills"},{id:4,name:"Layla Abdullah",age:45,condition:"Cerebral Palsy",treatmentType:"Physical Therapy",status:"On Hold",startDate:"2023-12-01",expectedEndDate:"2024-03-01",completionRate:30,sessionsCompleted:6,totalSessions:20,lastSession:"2024-01-15",therapist:"Dr. Ahmed Khalil",goals:[{goal:"Improve balance",progress:35,status:"Behind"},{goal:"Strengthen muscles",progress:25,status:"Behind"},{goal:"Enhance mobility",progress:30,status:"Behind"}],functionalScores:{initial:38,current:45,target:75},notes:"Treatment on hold due to medical complications"},{id:5,name:"Yusuf Ibrahim",age:67,condition:"Neurological Disorder",treatmentType:"Occupational Therapy",status:"Discontinued",startDate:"2023-10-15",endDate:"2023-12-20",completionRate:40,sessionsCompleted:8,totalSessions:20,lastSession:"2023-12-20",therapist:"Dr. Sarah Ahmed",goals:[{goal:"Cognitive improvement",progress:25,status:"Not Achieved"},{goal:"Memory enhancement",progress:30,status:"Not Achieved"},{goal:"Daily activities",progress:45,status:"Partial"}],functionalScores:{initial:35,current:42,target:70},notes:"Treatment discontinued due to patient request"}];setTimeout(()=>{l(e),d(e),v(!1)},1e3)},[]),(0,s.useEffect)(()=>{let e=a.filter(e=>{const t=e.name.toLowerCase().includes(x.toLowerCase())||e.condition.toLowerCase().includes(x.toLowerCase())||e.therapist.toLowerCase().includes(x.toLowerCase()),a="all"===c.status||e.status===c.status,s="all"===c.treatmentType||e.treatmentType===c.treatmentType;let r=!0;return"high"===c.completionRate?r=e.completionRate>=80:"medium"===c.completionRate?r=e.completionRate>=50&&e.completionRate<80:"low"===c.completionRate&&(r=e.completionRate<50),t&&a&&s&&r});e.sort((e,t)=>{let a=e[h],s=t[h];return"completionRate"===h&&(a=parseFloat(a),s=parseFloat(s)),"asc"===u?a>s?1:-1:a<s?1:-1}),d(e)},[a,x,c,h,u]);const f=e=>{switch(e){case"Completed":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";case"In Progress":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";case"On Hold":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";case"Discontinued":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"}},j=e=>{switch(e){case"Achieved":case"On Track":return"text-green-600";case"Ahead":return"text-blue-600";case"Behind":return"text-yellow-600";case"Not Achieved":return"text-red-600";case"Partial":return"text-orange-600";default:return"text-gray-600"}};return b?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"space-y-6 ".concat(t?"font-arabic":"font-english"),children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("search","Search")}),(0,n.jsx)("input",{type:"text",value:x,onChange:e=>g(e.target.value),placeholder:e("searchPatients","Search patients..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("status","Status")}),(0,n.jsxs)("select",{value:c.status,onChange:e=>m(t=>(0,i.A)((0,i.A)({},t),{},{status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,n.jsx)("option",{value:"In Progress",children:e("inProgress","In Progress")}),(0,n.jsx)("option",{value:"Completed",children:e("completed","Completed")}),(0,n.jsx)("option",{value:"On Hold",children:e("onHold","On Hold")}),(0,n.jsx)("option",{value:"Discontinued",children:e("discontinued","Discontinued")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("treatmentType","Treatment Type")}),(0,n.jsxs)("select",{value:c.treatmentType,onChange:e=>m(t=>(0,i.A)((0,i.A)({},t),{},{treatmentType:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"all",children:e("allTypes","All Types")}),(0,n.jsx)("option",{value:"Physical Therapy",children:e("physicalTherapy","Physical Therapy")}),(0,n.jsx)("option",{value:"Occupational Therapy",children:e("occupationalTherapy","Occupational Therapy")}),(0,n.jsx)("option",{value:"Speech Therapy",children:e("speechTherapy","Speech Therapy")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("completionRate","Completion Rate")}),(0,n.jsxs)("select",{value:c.completionRate,onChange:e=>m(t=>(0,i.A)((0,i.A)({},t),{},{completionRate:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"all",children:e("allRates","All Rates")}),(0,n.jsx)("option",{value:"high",children:e("high","High (80%+)")}),(0,n.jsx)("option",{value:"medium",children:e("medium","Medium (50-79%)")}),(0,n.jsx)("option",{value:"low",children:e("low","Low (<50%)")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("sortBy","Sort By")}),(0,n.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"name",children:e("name","Name")}),(0,n.jsx)("option",{value:"completionRate",children:e("completionRate","Completion Rate")}),(0,n.jsx)("option",{value:"startDate",children:e("startDate","Start Date")}),(0,n.jsx)("option",{value:"status",children:e("status","Status")})]})]})]})}),(0,n.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:o.map(t=>{return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t.name}),(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[t.age," ",e("years","years")," \u2022 ",t.condition]})]}),(0,n.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat(f(t.status)),children:t.status})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e("overallProgress","Overall Progress")}),(0,n.jsxs)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[t.completionRate,"%"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,n.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat((a=t.completionRate,a>=80?"bg-green-500":a>=60?"bg-blue-500":a>=40?"bg-yellow-500":"bg-red-500")),style:{width:"".concat(t.completionRate,"%")}})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("treatmentType","Treatment"),":"]}),(0,n.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:t.treatmentType})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("therapist","Therapist"),":"]}),(0,n.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:t.therapist})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("sessions","Sessions"),":"]}),(0,n.jsxs)("p",{className:"font-medium text-gray-900 dark:text-white",children:[t.sessionsCompleted,"/",t.totalSessions]})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("nextSession","Next Session"),":"]}),(0,n.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:t.nextSession||e("notScheduled","Not Scheduled")})]})]}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("goals","Goals")}),(0,n.jsx)("div",{className:"space-y-2",children:t.goals.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400 flex-1",children:e.goal}),(0,n.jsxs)("span",{className:"text-xs font-medium ".concat(j(e.status)," ml-2"),children:[e.progress,"%"]})]},t))})]}),(0,n.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("functionalScores","Functional Scores")}),(0,n.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:t.functionalScores.initial}),(0,n.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:e("initial","Initial")})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-medium text-blue-600",children:t.functionalScores.current}),(0,n.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:e("current","Current")})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"font-medium text-green-600",children:t.functionalScores.target}),(0,n.jsx)("div",{className:"text-gray-600 dark:text-gray-400",children:e("target","Target")})]})]})]})]},t.id);var a})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("summaryStatistics","Summary Statistics")}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:o.length}),(0,n.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("totalPatients","Total Patients")})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-green-600",children:o.filter(e=>"Completed"===e.status).length}),(0,n.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("completed","Completed")})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:o.filter(e=>"In Progress"===e.status).length}),(0,n.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("inProgress","In Progress")})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsxs)("div",{className:"text-2xl font-bold text-gray-600",children:[Math.round(o.reduce((e,t)=>e+t.completionRate,0)/o.length),"%"]}),(0,n.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("averageCompletion","Average Completion")})]})]})]})]})},d=()=>{const{t:e,isRTL:t}=(0,r.o)(),[a,l]=(0,s.useState)({title:"",description:"",type:"table",dataSource:"patients",fields:[],filters:[],groupBy:"",sortBy:"",sortOrder:"asc",dateRange:{start:"",end:""},chartType:"bar",layout:"portrait"}),[o,d]=(0,s.useState)({}),[c,m]=(0,s.useState)([]),[x,g]=(0,s.useState)([]),[h,p]=(0,s.useState)(!1),u={patients:{label:e("patients","Patients"),fields:{"patient.name":{label:e("patientName","Patient Name"),type:"text"},"patient.age":{label:e("age","Age"),type:"number"},"patient.gender":{label:e("gender","Gender"),type:"text"},"patient.condition":{label:e("condition","Condition"),type:"text"},"patient.registrationDate":{label:e("registrationDate","Registration Date"),type:"date"},"patient.status":{label:e("status","Status"),type:"text"},"patient.therapist":{label:e("assignedTherapist","Assigned Therapist"),type:"text"},"patient.insurance":{label:e("insurance","Insurance"),type:"text"},"patient.emergencyContact":{label:e("emergencyContact","Emergency Contact"),type:"text"}}},treatments:{label:e("treatments","Treatments"),fields:{"treatment.type":{label:e("treatmentType","Treatment Type"),type:"text"},"treatment.startDate":{label:e("startDate","Start Date"),type:"date"},"treatment.endDate":{label:e("endDate","End Date"),type:"date"},"treatment.status":{label:e("treatmentStatus","Treatment Status"),type:"text"},"treatment.completionRate":{label:e("completionRate","Completion Rate"),type:"number"},"treatment.sessionsCompleted":{label:e("sessionsCompleted","Sessions Completed"),type:"number"},"treatment.totalSessions":{label:e("totalSessions","Total Sessions"),type:"number"},"treatment.duration":{label:e("duration","Duration (days)"),type:"number"},"treatment.cost":{label:e("treatmentCost","Treatment Cost"),type:"currency"}}},sessions:{label:e("sessions","Sessions"),fields:{"session.date":{label:e("sessionDate","Session Date"),type:"date"},"session.duration":{label:e("sessionDuration","Session Duration"),type:"number"},"session.type":{label:e("sessionType","Session Type"),type:"text"},"session.therapist":{label:e("therapist","Therapist"),type:"text"},"session.status":{label:e("sessionStatus","Session Status"),type:"text"},"session.notes":{label:e("sessionNotes","Session Notes"),type:"text"},"session.attendance":{label:e("attendance","Attendance"),type:"text"},"session.progress":{label:e("progressRating","Progress Rating"),type:"number"}}},financial:{label:e("financial","Financial"),fields:{"financial.totalRevenue":{label:e("totalRevenue","Total Revenue"),type:"currency"},"financial.insurancePayments":{label:e("insurancePayments","Insurance Payments"),type:"currency"},"financial.outOfPocketPayments":{label:e("outOfPocketPayments","Out of Pocket Payments"),type:"currency"},"financial.pendingPayments":{label:e("pendingPayments","Pending Payments"),type:"currency"},"financial.paymentDate":{label:e("paymentDate","Payment Date"),type:"date"},"financial.paymentMethod":{label:e("paymentMethod","Payment Method"),type:"text"}}},outcomes:{label:e("outcomes","Outcomes"),fields:{"outcome.functionalScore":{label:e("functionalScore","Functional Score"),type:"number"},"outcome.painLevel":{label:e("painLevel","Pain Level"),type:"number"},"outcome.mobilityScore":{label:e("mobilityScore","Mobility Score"),type:"number"},"outcome.qualityOfLife":{label:e("qualityOfLife","Quality of Life"),type:"number"},"outcome.goalAchievement":{label:e("goalAchievement","Goal Achievement"),type:"percentage"},"outcome.satisfactionScore":{label:e("satisfactionScore","Satisfaction Score"),type:"number"},"outcome.assessmentDate":{label:e("assessmentDate","Assessment Date"),type:"date"}}}},y=[{value:"table",label:e("table","Table"),icon:"fas fa-table"},{value:"chart",label:e("chart","Chart"),icon:"fas fa-chart-bar"},{value:"summary",label:e("summary","Summary"),icon:"fas fa-list"},{value:"dashboard",label:e("dashboard","Dashboard"),icon:"fas fa-tachometer-alt"}];e("barChart","Bar Chart"),e("lineChart","Line Chart"),e("pieChart","Pie Chart"),e("doughnutChart","Doughnut Chart"),e("areaChart","Area Chart");(0,s.useEffect)(()=>{var e;d((null===(e=u[a.dataSource])||void 0===e?void 0:e.fields)||{})},[a.dataSource]),(0,s.useEffect)(()=>{g([{id:1,title:"Patient Completion Report",description:"Treatment completion rates by therapy type",type:"chart",dataSource:"treatments",createdDate:"2024-02-01",lastModified:"2024-02-08"},{id:2,title:"Monthly Revenue Analysis",description:"Financial performance and payment tracking",type:"dashboard",dataSource:"financial",createdDate:"2024-01-15",lastModified:"2024-02-05"},{id:3,title:"Patient Demographics",description:"Age, gender, and condition distribution",type:"table",dataSource:"patients",createdDate:"2024-01-20",lastModified:"2024-02-03"}])},[]);const b=(e,t,a)=>{l(s=>(0,i.A)((0,i.A)({},s),{},{filters:s.filters.map((s,r)=>r===e?(0,i.A)((0,i.A)({},s),{},{[t]:a}):s)}))},v=t=>{alert(e("exportingReport","Exporting report as ".concat(t.toUpperCase(),"...")))};return(0,n.jsxs)("div",{className:"space-y-6 ".concat(t?"font-arabic":"font-english"),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("advancedReportBuilder","Advanced Report Builder")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("createCustomReports","Create custom reports with drag-and-drop functionality")})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsxs)("button",{onClick:()=>{m([{"patient.name":"Ahmed Mohammed","patient.age":28,"treatment.completionRate":65},{"patient.name":"Fatima Hassan","patient.age":34,"treatment.completionRate":100},{"patient.name":"Omar Khalil","patient.age":12,"treatment.completionRate":45},{"patient.name":"Layla Abdullah","patient.age":45,"treatment.completionRate":30},{"patient.name":"Yusuf Ibrahim","patient.age":67,"treatment.completionRate":40}]),p(!0)},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-eye mr-2"}),e("preview","Preview")]}),(0,n.jsxs)("button",{onClick:()=>{const t=(0,i.A)((0,i.A)({id:Date.now()},a),{},{createdDate:(new Date).toISOString().split("T")[0],lastModified:(new Date).toISOString().split("T")[0]});g(e=>[t,...e]),alert(e("reportSaved","Report saved successfully!"))},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-save mr-2"}),e("saveReport","Save Report")]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("basicInformation","Basic Information")}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("reportTitle","Report Title")}),(0,n.jsx)("input",{type:"text",value:a.title,onChange:e=>l(t=>(0,i.A)((0,i.A)({},t),{},{title:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterReportTitle","Enter report title...")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("description","Description")}),(0,n.jsx)("textarea",{value:a.description,onChange:e=>l(t=>(0,i.A)((0,i.A)({},t),{},{description:e.target.value})),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterDescription","Enter report description...")})]})]})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("reportConfiguration","Report Configuration")}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("reportType","Report Type")}),(0,n.jsx)("div",{className:"grid grid-cols-2 gap-2",children:y.map(e=>(0,n.jsxs)("button",{onClick:()=>l(t=>(0,i.A)((0,i.A)({},t),{},{type:e.value})),className:"p-3 border rounded-lg text-center transition-colors ".concat(a.type===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300":"border-gray-300 dark:border-gray-600 hover:border-gray-400"),children:[(0,n.jsx)("i",{className:"".concat(e.icon," text-lg mb-1")}),(0,n.jsx)("div",{className:"text-xs",children:e.label})]},e.value))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("dataSource","Data Source")}),(0,n.jsx)("select",{value:a.dataSource,onChange:e=>l(t=>(0,i.A)((0,i.A)({},t),{},{dataSource:e.target.value,fields:[]})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:Object.entries(u).map(e=>{let[t,a]=e;return(0,n.jsx)("option",{value:t,children:a.label},t)})})]})]})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("selectFields","Select Fields")}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:e("availableFields","Available Fields")}),(0,n.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:Object.entries(o).map(e=>{let[t,s]=e;return(0,n.jsxs)("button",{onClick:()=>{return e=t,void(a.fields.includes(e)||l(t=>(0,i.A)((0,i.A)({},t),{},{fields:[...t.fields,e]})));var e},className:"w-full text-left px-3 py-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",disabled:a.fields.includes(t),children:[(0,n.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:s.label}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:s.type})]},t)})})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:e("selectedFields","Selected Fields")}),(0,n.jsxs)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:[a.fields.map((e,t)=>{var a;return(0,n.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded",children:[(0,n.jsx)("span",{className:"text-sm text-blue-900 dark:text-blue-100",children:null===(a=o[e])||void 0===a?void 0:a.label}),(0,n.jsx)("button",{onClick:()=>(e=>{l(t=>(0,i.A)((0,i.A)({},t),{},{fields:t.fields.filter(t=>t!==e)}))})(e),className:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",children:(0,n.jsx)("i",{className:"fas fa-times"})})]},e)}),0===a.fields.length&&(0,n.jsx)("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:e("noFieldsSelected","No fields selected")})]})]})]})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("filters","Filters")}),(0,n.jsxs)("button",{onClick:()=>{l(e=>(0,i.A)((0,i.A)({},e),{},{filters:[...e.filters,{field:"",operator:"equals",value:""}]}))},className:"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-plus mr-1"}),e("addFilter","Add Filter")]})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[a.filters.map((t,a)=>(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-3 items-end",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("field","Field")}),(0,n.jsxs)("select",{value:t.field,onChange:e=>b(a,"field",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"",children:e("selectField","Select Field")}),Object.entries(o).map(e=>{let[t,a]=e;return(0,n.jsx)("option",{value:t,children:a.label},t)})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("operator","Operator")}),(0,n.jsxs)("select",{value:t.operator,onChange:e=>b(a,"operator",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"equals",children:e("equals","Equals")}),(0,n.jsx)("option",{value:"contains",children:e("contains","Contains")}),(0,n.jsx)("option",{value:"greater",children:e("greaterThan","Greater Than")}),(0,n.jsx)("option",{value:"less",children:e("lessThan","Less Than")}),(0,n.jsx)("option",{value:"between",children:e("between","Between")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("value","Value")}),(0,n.jsx)("input",{type:"text",value:t.value,onChange:e=>b(a,"value",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterValue","Enter value...")})]}),(0,n.jsx)("div",{children:(0,n.jsx)("button",{onClick:()=>(e=>{l(t=>(0,i.A)((0,i.A)({},t),{},{filters:t.filters.filter((t,a)=>a!==e)}))})(a),className:"px-2 py-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",children:(0,n.jsx)("i",{className:"fas fa-trash"})})})]},a)),0===a.filters.length&&(0,n.jsx)("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400",children:e("noFiltersAdded","No filters added")})]})]})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("savedReports","Saved Reports")}),(0,n.jsx)("div",{className:"space-y-3",children:x.map(e=>(0,n.jsxs)("div",{className:"p-3 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.title}),(0,n.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1",children:e.description}),(0,n.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(e.lastModified).toLocaleDateString()}),(0,n.jsxs)("div",{className:"flex space-x-1",children:[(0,n.jsx)("button",{className:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",children:(0,n.jsx)("i",{className:"fas fa-edit text-xs"})}),(0,n.jsx)("button",{className:"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300",children:(0,n.jsx)("i",{className:"fas fa-download text-xs"})})]})]})]},e.id))})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("exportOptions","Export Options")}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("button",{onClick:()=>v("pdf"),className:"w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-file-pdf text-red-600 mr-2"}),e("exportPDF","Export as PDF")]}),(0,n.jsxs)("button",{onClick:()=>v("excel"),className:"w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-file-excel text-green-600 mr-2"}),e("exportExcel","Export as Excel")]}),(0,n.jsxs)("button",{onClick:()=>v("csv"),className:"w-full px-3 py-2 text-left border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-file-csv text-blue-600 mr-2"}),e("exportCSV","Export as CSV")]})]})]})]})]}),h&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("reportPreview","Report Preview")}),(0,n.jsx)("button",{onClick:()=>p(!1),className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300",children:(0,n.jsx)("i",{className:"fas fa-times"})})]})}),(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsx)("h4",{className:"text-xl font-bold text-gray-900 dark:text-white mb-2",children:a.title||e("untitledReport","Untitled Report")}),a.description&&(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:a.description}),(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-600",children:[(0,n.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,n.jsx)("tr",{children:a.fields.map(e=>{var t;return(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:null===(t=o[e])||void 0===t?void 0:t.label},e)})})}),(0,n.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:c.map((e,t)=>(0,n.jsx)("tr",{children:a.fields.map(t=>(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:e[t]||"-"},t))},t))})]})})]})]})})]})},c=()=>{var e,t,a,l,i,o,d,c,m,x,g,h,p,u,y;const{t:b,isRTL:v}=(0,r.o)(),[f,j]=(0,s.useState)("last-30-days"),[N,k]=(0,s.useState)(["completion","demographics","outcomes"]),[w,S]=(0,s.useState)({}),[T,C]=(0,s.useState)(!0);(0,s.useEffect)(()=>{const e={overview:{totalPatients:1247,activePatients:892,completedTreatments:456,ongoingTreatments:436,averageAge:34.5,malePatients:623,femalePatients:624,totalSessions:15678,averageSessionsPerPatient:12.6,totalRevenue:2456789,averageRevenuePerPatient:1970},completionStatistics:{byTreatmentType:{"Physical Therapy":{total:298,completed:234,rate:78.5},"Occupational Therapy":{total:201,completed:156,rate:77.6},"Speech Therapy":{total:123,completed:89,rate:72.4},"Special Needs Therapy":{total:89,completed:67,rate:75.3},"CARF Programs":{total:56,completed:45,rate:80.4}},byAge:{"0-18":{total:234,completed:189,rate:80.8},"19-35":{total:345,completed:267,rate:77.4},"36-50":{total:298,completed:231,rate:77.5},"51-65":{total:267,completed:198,rate:74.2},"65+":{total:103,completed:71,rate:68.9}},byCondition:{"Cerebral Palsy":{total:156,completed:124,rate:79.5},"Autism Spectrum":{total:134,completed:98,rate:73.1},"Stroke Recovery":{total:189,completed:156,rate:82.5},"Spinal Injury":{total:98,completed:78,rate:79.6},"Developmental Delays":{total:167,completed:134,rate:80.2},"Neurological Disorders":{total:145,completed:109,rate:75.2},"Orthopedic Conditions":{total:234,completed:189,rate:80.8},Other:{total:124,completed:89,rate:71.8}}},demographicAnalysis:{ageDistribution:{"0-5":89,"6-12":145,"13-18":123,"19-30":234,"31-45":298,"46-60":267,"61-75":78,"75+":25},genderByAge:{male:{"0-18":134,"19-35":178,"36-50":145,"51-65":123,"65+":43},female:{"0-18":100,"19-35":167,"36-50":153,"51-65":144,"65+":60}},conditionsByGender:{male:{"Cerebral Palsy":89,"Autism Spectrum":98,"Stroke Recovery":87,"Spinal Injury":67,"Developmental Delays":78,"Neurological Disorders":65,"Orthopedic Conditions":123,Other:56},female:{"Cerebral Palsy":67,"Autism Spectrum":36,"Stroke Recovery":102,"Spinal Injury":31,"Developmental Delays":89,"Neurological Disorders":80,"Orthopedic Conditions":111,Other:68}}},outcomeMetrics:{functionalImprovement:{"Significant (>75%)":234,"Moderate (50-75%)":345,"Minimal (25-50%)":156,"None (<25%)":67},goalAchievement:{"Exceeded Goals":123,"Met Goals":345,"Partially Met":234,"Not Met":89},satisfactionRatings:{"Excellent (5)":456,"Good (4)":234,"Fair (3)":89,"Poor (2)":23,"Very Poor (1)":5},painReduction:{"Significant (>50%)":189,"Moderate (25-50%)":234,"Minimal (10-25%)":123,"None (<10%)":67}},timeAnalysis:{monthlyAdmissions:[45,52,48,61,58,67,72,69,74,78,82,85],monthlyCompletions:[34,38,42,45,48,52,56,59,62,65,68,71],monthlyRevenue:[156e3,178e3,165e3,189e3,201e3,234e3,245e3,223e3,267e3,289e3,298e3,312e3],averageWaitTimes:[5.2,4.8,5.1,4.9,5.3,4.7,5,4.6,4.8,4.5,4.3,4.1],sessionAttendance:[92,94,91,95,93,96,94,97,95,96,98,97]},therapistPerformance:{"Dr. Sarah Ahmed":{patients:45,completionRate:85.2,satisfaction:4.8,averageImprovement:78.5,sessionsPerWeek:32},"Dr. Mohammed Ali":{patients:38,completionRate:82.1,satisfaction:4.7,averageImprovement:75.3,sessionsPerWeek:28},"Dr. Fatima Hassan":{patients:42,completionRate:88.9,satisfaction:4.9,averageImprovement:82.1,sessionsPerWeek:35},"Dr. Ahmed Khalil":{patients:35,completionRate:79.3,satisfaction:4.6,averageImprovement:73.8,sessionsPerWeek:26}},financialAnalysis:{revenueByTreatment:{"Physical Therapy":856e3,"Occupational Therapy":634e3,"Speech Therapy":423e3,"Special Needs Therapy":345e3,"CARF Programs":198e3},paymentMethods:{Insurance:1923456,"Out of Pocket":423789,Government:109544},collectionRates:{Insurance:94.2,"Out of Pocket":87.5,Government:98.1}}};setTimeout(()=>{S(e),C(!1)},1e3)},[f]);const A=e=>{let{title:t,value:a,subtitle:s,icon:r,color:l,change:i,changeType:o}=e;return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:t}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a}),s&&(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:s})]}),(0,n.jsx)("div",{className:"p-3 bg-".concat(l,"-100 dark:bg-").concat(l,"-900/30 rounded-lg"),children:(0,n.jsx)("i",{className:"".concat(r," text-").concat(l,"-600 dark:text-").concat(l,"-400 text-xl")})})]}),i&&(0,n.jsxs)("div",{className:"mt-4 flex items-center",children:[(0,n.jsxs)("span",{className:"text-sm font-medium ".concat("increase"===o?"text-green-600":"decrease"===o?"text-red-600":"text-gray-600"),children:[(0,n.jsx)("i",{className:"fas fa-arrow-".concat("increase"===o?"up":"decrease"===o?"down":"right"," mr-1")}),i]}),(0,n.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 ml-2",children:b("fromLastPeriod","from last period")})]})]})};return T?(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"space-y-6 ".concat(v?"font-arabic":"font-english"),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:b("statisticalDashboard","Statistical Dashboard")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:b("comprehensiveStatistics","Comprehensive patient and treatment statistics")})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsxs)("select",{value:f,onChange:e=>j(e.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"last-7-days",children:b("last7Days","Last 7 Days")}),(0,n.jsx)("option",{value:"last-30-days",children:b("last30Days","Last 30 Days")}),(0,n.jsx)("option",{value:"last-90-days",children:b("last90Days","Last 90 Days")}),(0,n.jsx)("option",{value:"last-year",children:b("lastYear","Last Year")}),(0,n.jsx)("option",{value:"all-time",children:b("allTime","All Time")})]}),(0,n.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-download mr-2"}),b("exportData","Export Data")]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,n.jsx)(A,{title:b("totalPatients","Total Patients"),value:null===(e=w.overview)||void 0===e||null===(t=e.totalPatients)||void 0===t?void 0:t.toLocaleString(),subtitle:"".concat(null===(a=w.overview)||void 0===a?void 0:a.activePatients," ").concat(b("active","active")),icon:"fas fa-users",color:"blue",change:"+12.5%",changeType:"increase"}),(0,n.jsx)(A,{title:b("completedTreatments","Completed Treatments"),value:null===(l=w.overview)||void 0===l||null===(i=l.completedTreatments)||void 0===i?void 0:i.toLocaleString(),subtitle:"".concat(null===(o=w.overview)||void 0===o?void 0:o.ongoingTreatments," ").concat(b("ongoing","ongoing")),icon:"fas fa-check-circle",color:"green",change:"+8.3%",changeType:"increase"}),(0,n.jsx)(A,{title:b("averageAge","Average Age"),value:"".concat(null===(d=w.overview)||void 0===d?void 0:d.averageAge," ").concat(b("years","years")),subtitle:"".concat(null===(c=w.overview)||void 0===c?void 0:c.malePatients,"M / ").concat(null===(m=w.overview)||void 0===m?void 0:m.femalePatients,"F"),icon:"fas fa-birthday-cake",color:"purple",change:"+0.5",changeType:"increase"}),(0,n.jsx)(A,{title:b("totalRevenue","Total Revenue"),value:"".concat(((null===(x=w.overview)||void 0===x?void 0:x.totalRevenue)/1e6).toFixed(1),"M SAR"),subtitle:"".concat(null===(g=w.overview)||void 0===g?void 0:g.averageRevenuePerPatient," ").concat(b("perPatient","per patient")),icon:"fas fa-dollar-sign",color:"yellow",change:"+15.2%",changeType:"increase"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:b("completionRatesByTreatment","Completion Rates by Treatment Type")}),(0,n.jsx)("div",{className:"space-y-4",children:Object.entries((null===(h=w.completionStatistics)||void 0===h?void 0:h.byTreatmentType)||{}).map((e,t)=>{let[a,s]=e;const r=["bg-blue-500","bg-green-500","bg-yellow-500","bg-red-500","bg-purple-500"];return(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:a}),(0,n.jsxs)("span",{className:"text-sm font-bold text-gray-900 dark:text-white",children:[s.rate,"%"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3",children:(0,n.jsx)("div",{className:"h-3 rounded-full ".concat(r[t%r.length]," transition-all duration-500"),style:{width:"".concat(s.rate,"%")}})}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[s.completed," of ",s.total," completed"]})]},a)})})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:b("ageDistribution","Age Distribution")}),(0,n.jsx)("div",{className:"space-y-3",children:Object.entries((null===(p=w.demographicAnalysis)||void 0===p?void 0:p.ageDistribution)||{}).map((e,t)=>{var a;let[s,r]=e;const l=Object.values((null===(a=w.demographicAnalysis)||void 0===a?void 0:a.ageDistribution)||{}).reduce((e,t)=>e+t,0),i=l>0?Math.round(r/l*100):0,o=["bg-red-400","bg-blue-400","bg-yellow-400","bg-green-400","bg-purple-400","bg-orange-400","bg-pink-400","bg-gray-400"];return(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded-full ".concat(o[t%o.length])}),(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:s})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("div",{className:"text-lg font-bold text-gray-900 dark:text-white",children:r}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[i,"%"]})]})]},s)})})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:b("monthlyTrends","Monthly Trends")}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,n.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:b("admissions","Admissions")})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,n.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:b("completions","Completions")})]})]}),(0,n.jsx)("div",{className:"grid grid-cols-12 gap-1 h-32",children:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((e,t)=>{var a,s,r,l,i,o;const d=(null===(a=w.timeAnalysis)||void 0===a||null===(s=a.monthlyAdmissions)||void 0===s?void 0:s[t])||0,c=(null===(r=w.timeAnalysis)||void 0===r||null===(l=r.monthlyCompletions)||void 0===l?void 0:l[t])||0,m=Math.max(...(null===(i=w.timeAnalysis)||void 0===i?void 0:i.monthlyAdmissions)||[],...(null===(o=w.timeAnalysis)||void 0===o?void 0:o.monthlyCompletions)||[]),x=m>0?d/m*100:0,g=m>0?c/m*100:0;return(0,n.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,n.jsxs)("div",{className:"flex-1 flex flex-col justify-end space-y-1 w-full",children:[(0,n.jsx)("div",{className:"bg-blue-500 rounded-t",style:{height:"".concat(x,"%"),minHeight:"2px"},title:"".concat(e,": ").concat(d," admissions")}),(0,n.jsx)("div",{className:"bg-green-500 rounded-t",style:{height:"".concat(g,"%"),minHeight:"2px"},title:"".concat(e,": ").concat(c," completions")})]}),(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:e})]},e)})})]})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:b("functionalImprovement","Functional Improvement Outcomes")}),(0,n.jsx)("div",{className:"space-y-3",children:Object.entries((null===(u=w.outcomeMetrics)||void 0===u?void 0:u.functionalImprovement)||{}).map((e,t)=>{var a;let[s,r]=e;const l=Object.values((null===(a=w.outcomeMetrics)||void 0===a?void 0:a.functionalImprovement)||{}).reduce((e,t)=>e+t,0),i=l>0?Math.round(r/l*100):0,o=["bg-green-500","bg-blue-500","bg-yellow-500","bg-red-500"];return(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-4 h-4 rounded-full ".concat(o[t%o.length])}),(0,n.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:s})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("div",{className:"text-lg font-bold text-gray-900 dark:text-white",children:r}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[i,"%"]})]})]},s)})})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:b("therapistPerformance","Therapist Performance")}),(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-600",children:[(0,n.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase",children:b("therapist","Therapist")}),(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase",children:b("patients","Patients")}),(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase",children:b("completion","Completion")}),(0,n.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase",children:b("satisfaction","Satisfaction")})]})}),(0,n.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:Object.entries(w.therapistPerformance||{}).map(e=>{let[t,a]=e;return(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white",children:t}),(0,n.jsx)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:a.patients}),(0,n.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:[a.completionRate,"%"]}),(0,n.jsxs)("td",{className:"px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:[a.satisfaction,"/5.0"]})]},t)})})]})})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:b("completionByCondition","Completion Rates by Condition")}),(0,n.jsx)("div",{className:"space-y-3",children:Object.entries((null===(y=w.completionStatistics)||void 0===y?void 0:y.byCondition)||{}).map(e=>{let[t,a]=e;return(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:t}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,n.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(a.rate,"%")}})}),(0,n.jsxs)("span",{className:"text-sm font-medium text-gray-900 dark:text-white w-12 text-right",children:[a.rate,"%"]})]})]},t)})})]})]})]})},m=()=>{var e;const{t:t,isRTL:a}=(0,r.o)(),[i,m]=(0,s.useState)("overview"),[x,g]=(0,s.useState)(!1),h=[{id:"overview",label:t("overview","Overview"),icon:"fas fa-chart-line",component:l.default},{id:"progress",label:t("patientProgress","Patient Progress"),icon:"fas fa-users",component:o},{id:"statistics",label:t("statistics","Statistics"),icon:"fas fa-chart-bar",component:c},{id:"reports",label:t("reportBuilder","Report Builder"),icon:"fas fa-file-alt",component:d}],p=(null===(e=h.find(e=>e.id===i))||void 0===e?void 0:e.component)||l.default;return(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(a?"font-arabic":"font-english"),children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,n.jsx)("nav",{className:"-mb-px flex space-x-8",children:h.map(e=>(0,n.jsxs)("button",{onClick:()=>m(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(i===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,n.jsx)("i",{className:e.icon}),(0,n.jsx)("span",{children:e.label})]},e.id))})})}),(0,n.jsx)("div",{className:"tab-content",children:(0,n.jsx)(p,{})})]})})}},9890:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var s=a(5043),r=a(7921),l=a(579);const i=()=>{var e,t,a,i,n,o,d,c,m;const{t:x,isRTL:g}=(0,r.o)(),[h,p]=(0,s.useState)("last-30-days"),[u,y]=(0,s.useState)("all"),[b,v]=(0,s.useState)({}),[f,j]=(0,s.useState)(!0);(0,s.useEffect)(()=>{const e={overview:{totalPatients:1247,activePatients:892,completedTreatments:456,ongoingTreatments:436,averageCompletionRate:78.5,averageTreatmentDuration:42,totalSessions:15678,completedSessions:12543,cancelledSessions:1234,noShowSessions:1901},patientProgress:{completed:456,inProgress:436,onHold:67,discontinued:89,notStarted:199},treatmentCompletion:{physicalTherapy:{completed:234,total:298,rate:78.5},occupationalTherapy:{completed:156,total:201,rate:77.6},speechTherapy:{completed:89,total:123,rate:72.4},specialNeeds:{completed:67,total:89,rate:75.3},carfPrograms:{completed:45,total:56,rate:80.4}},demographics:{ageGroups:{"0-18":234,"19-35":345,"36-50":298,"51-65":267,"65+":103},gender:{male:623,female:624},conditions:{"Cerebral Palsy":156,"Autism Spectrum":134,"Stroke Recovery":189,"Spinal Injury":98,"Developmental Delays":167,"Neurological Disorders":145,"Orthopedic Conditions":234,Other:124}},outcomeMetrics:{functionalImprovement:{significant:67,moderate:156,minimal:89,none:34},goalAchievement:{exceeded:89,achieved:234,partiallyAchieved:156,notAchieved:67},satisfactionScores:{excellent:456,good:234,fair:89,poor:23}},timeAnalysis:{averageWaitTime:5.2,averageSessionDuration:45,averageTreatmentLength:42,completionTimeVariance:12.5},financialMetrics:{totalRevenue:2456789,averageRevenuePerPatient:1970,insuranceCoverage:78.5,outOfPocketPayments:21.5,collectionRate:94.2},staffPerformance:{therapists:[{name:"Dr. Sarah Ahmed",patients:45,completionRate:85.2,satisfaction:4.8},{name:"Dr. Mohammed Ali",patients:38,completionRate:82.1,satisfaction:4.7},{name:"Dr. Fatima Hassan",patients:42,completionRate:88.9,satisfaction:4.9},{name:"Dr. Ahmed Khalil",patients:35,completionRate:79.3,satisfaction:4.6}]},trends:{monthlyAdmissions:[45,52,48,61,58,67,72,69,74,78,82,85],monthlyCompletions:[34,38,42,45,48,52,56,59,62,65,68,71],satisfactionTrend:[4.2,4.3,4.4,4.5,4.6,4.7,4.8,4.7,4.8,4.9,4.8,4.9]}};setTimeout(()=>{v(e),j(!1)},1e3)},[h]);const N=e=>{let{title:t,value:a,subtitle:s,icon:r,color:i,trend:n,percentage:o}=e;return(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:t}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:a}),s&&(0,l.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:s})]}),(0,l.jsx)("div",{className:"p-3 bg-".concat(i,"-100 dark:bg-").concat(i,"-900/30 rounded-lg"),children:(0,l.jsx)("i",{className:"".concat(r," text-").concat(i,"-600 dark:text-").concat(i,"-400 text-xl")})})]}),n&&(0,l.jsxs)("div",{className:"mt-4 flex items-center",children:[(0,l.jsxs)("span",{className:"text-sm font-medium ".concat("up"===n?"text-green-600":"down"===n?"text-red-600":"text-gray-600"),children:[(0,l.jsx)("i",{className:"fas fa-arrow-".concat("up"===n?"up":"down"===n?"down":"right"," mr-1")}),o,"%"]}),(0,l.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 ml-2",children:x("fromLastPeriod","from last period")})]})]})},k=()=>{if(!b.treatmentCompletion)return(0,l.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Loading chart data..."});const e=["bg-blue-500","bg-green-500","bg-yellow-500","bg-red-500","bg-purple-500"];return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("treatmentCompletionRates","Treatment Completion Rates by Type")}),Object.entries(b.treatmentCompletion).map((t,a)=>{let[s,r]=t;return(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:s}),(0,l.jsxs)("span",{className:"text-sm font-bold text-gray-900 dark:text-white",children:[r.rate,"%"]})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3",children:(0,l.jsx)("div",{className:"h-3 rounded-full ".concat(e[a%e.length]," transition-all duration-500"),style:{width:"".concat(r.rate,"%")}})}),(0,l.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[r.completed," of ",r.total," completed"]})]},s)})]})},w=()=>{if(!b.patientProgress)return(0,l.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Loading chart data..."});const e=[x("completed","Completed"),x("inProgress","In Progress"),x("onHold","On Hold"),x("discontinued","Discontinued"),x("notStarted","Not Started")],t=Object.values(b.patientProgress),a=t.reduce((e,t)=>e+t,0),s=["text-green-600","text-blue-600","text-yellow-600","text-red-600","text-gray-600"],r=["bg-green-500","bg-blue-500","bg-yellow-500","bg-red-500","bg-gray-500"];return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("patientProgressDistribution","Patient Progress Distribution")}),(0,l.jsx)("div",{className:"grid grid-cols-1 gap-3",children:e.map((e,i)=>{const n=t[i],o=a>0?Math.round(n/a*100):0;return(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-4 h-4 rounded-full ".concat(r[i])}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("div",{className:"text-lg font-bold ".concat(s[i]),children:n}),(0,l.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[o,"%"]})]})]},e)})})]})},S=()=>{if(!b.trends)return(0,l.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-500",children:"Loading chart data..."});const e=b.trends.monthlyAdmissions||[],t=b.trends.monthlyCompletions||[],a=Math.max(...e,...t);return(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:x("monthlyTrends","Monthly Trends")}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded"}),(0,l.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:x("admissions","Admissions")})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded"}),(0,l.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:x("completions","Completions")})]})]}),(0,l.jsx)("div",{className:"grid grid-cols-12 gap-2 h-48",children:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((s,r)=>{const i=a>0?e[r]/a*100:0,n=a>0?t[r]/a*100:0;return(0,l.jsxs)("div",{className:"flex flex-col items-center space-y-1",children:[(0,l.jsxs)("div",{className:"flex-1 flex flex-col justify-end space-y-1 w-full",children:[(0,l.jsx)("div",{className:"bg-blue-500 rounded-t",style:{height:"".concat(i,"%"),minHeight:"2px"},title:"".concat(s,": ").concat(e[r]," admissions")}),(0,l.jsx)("div",{className:"bg-green-500 rounded-t",style:{height:"".concat(n,"%"),minHeight:"2px"},title:"".concat(s,": ").concat(t[r]," completions")})]}),(0,l.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400 transform rotate-45 origin-bottom-left",children:s})]},s)})})]})]})};return f?(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,l.jsxs)("div",{className:"space-y-6 ".concat(g?"font-arabic":"font-english"),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:x("advancedAnalytics","Advanced Analytics & Reporting")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:x("comprehensivePatientStatistics","Comprehensive patient statistics and treatment analytics")})]}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsxs)("select",{value:h,onChange:e=>p(e.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"last-7-days",children:x("last7Days","Last 7 Days")}),(0,l.jsx)("option",{value:"last-30-days",children:x("last30Days","Last 30 Days")}),(0,l.jsx)("option",{value:"last-90-days",children:x("last90Days","Last 90 Days")}),(0,l.jsx)("option",{value:"last-year",children:x("lastYear","Last Year")}),(0,l.jsx)("option",{value:"all-time",children:x("allTime","All Time")})]}),(0,l.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-download mr-2"}),x("exportReport","Export Report")]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,l.jsx)(N,{title:x("totalPatients","Total Patients"),value:null===(e=b.overview)||void 0===e||null===(t=e.totalPatients)||void 0===t?void 0:t.toLocaleString(),subtitle:"".concat(null===(a=b.overview)||void 0===a?void 0:a.activePatients," ").concat(x("active","active")),icon:"fas fa-users",color:"blue",trend:"up",percentage:"12.5"}),(0,l.jsx)(N,{title:x("completionRate","Completion Rate"),value:"".concat(null===(i=b.overview)||void 0===i?void 0:i.averageCompletionRate,"%"),subtitle:"".concat(null===(n=b.overview)||void 0===n?void 0:n.completedTreatments," ").concat(x("completed","completed")),icon:"fas fa-check-circle",color:"green",trend:"up",percentage:"5.2"}),(0,l.jsx)(N,{title:x("averageDuration","Average Duration"),value:"".concat(null===(o=b.overview)||void 0===o?void 0:o.averageTreatmentDuration," ").concat(x("days","days")),subtitle:x("treatmentLength","treatment length"),icon:"fas fa-clock",color:"yellow",trend:"down",percentage:"3.1"}),(0,l.jsx)(N,{title:x("totalSessions","Total Sessions"),value:null===(d=b.overview)||void 0===d||null===(c=d.totalSessions)||void 0===c?void 0:c.toLocaleString(),subtitle:"".concat(null===(m=b.overview)||void 0===m?void 0:m.completedSessions," ").concat(x("completed","completed")),icon:"fas fa-calendar-check",color:"purple",trend:"up",percentage:"8.7"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,l.jsx)(k,{})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,l.jsx)(w,{})})]}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,l.jsx)(S,{})})]})}}}]);
//# sourceMappingURL=6950.2cb9e87a.chunk.js.map