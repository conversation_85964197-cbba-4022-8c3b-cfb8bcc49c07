{"version": 3, "file": "static/js/8481.6430d1fe.chunk.js", "mappings": "uNAKA,MAuXA,EAvXsBA,KACpB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACfC,GAAWC,EAAAA,EAAAA,OACVC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,CACnDC,KAAM,GACNC,WAAY,GACZC,MAAO,GACPC,MAAO,GACPC,YAAa,GACbC,UAAW,GACXC,UAAW,GACXC,kBAAmB,MAEdC,EAAeC,IAAoBV,EAAAA,EAAAA,UAAS,KAC5CW,EAASC,IAAcZ,EAAAA,EAAAA,WAAS,IAChCa,EAAaC,IAAkBd,EAAAA,EAAAA,WAAS,GAGzCe,EAAe,CACnB,CACEC,GAAI,OACJf,KAAM,yFACNgB,OAAQ,0BACRf,WAAY,aACZC,MAAO,gBACPC,MAAO,2BACPC,YAAa,aACba,IAAK,GACLZ,UAAW,iBACXa,YAAa,4EACbZ,UAAW,sBACXC,kBAAmB,cACnBY,UAAW,aACXC,OAAQ,UAEV,CACEL,GAAI,OACJf,KAAM,yFACNgB,OAAQ,sBACRf,WAAY,aACZC,MAAO,gBACPC,MAAO,uBACPC,YAAa,aACba,IAAK,GACLZ,UAAW,qBACXa,YAAa,qGACbZ,UAAW,wBACXC,kBAAmB,WACnBY,UAAW,aACXC,OAAQ,UAEV,CACEL,GAAI,OACJf,KAAM,2GACNgB,OAAQ,8BACRf,WAAY,aACZC,MAAO,gBACPC,MAAO,8BACPC,YAAa,aACba,IAAK,GACLZ,UAAW,2BACXa,YAAa,+FACbZ,UAAW,sBACXC,kBAAmB,UACnBY,UAAW,aACXC,OAAQ,WAINC,EAAoBA,CAACC,EAAOC,KAChCzB,EAAkB0B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACF,GAAQC,MA8DjD,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DpC,EAAE,gBAAiB,qBAEtBqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDpC,EAAE,oBAAqB,oDAK5BkC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mGAAkGC,SAAA,EAC/GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDACZnC,EAAE,iBAAkB,uBAGvBkC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,cAAe,mBAEpBqC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLP,MAAO1B,EAAeG,KACtB+B,SAAWC,GAAMX,EAAkB,OAAQW,EAAEC,OAAOV,OACpDI,UAAU,kKACVO,YAAa1C,EAAE,mBAAoB,4BAIvCkC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,aAAc,kBAEnBqC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLP,MAAO1B,EAAeI,WACtB8B,SAAWC,GAAMX,EAAkB,aAAcW,EAAEC,OAAOV,OAC1DI,UAAU,kKACVO,YAAa1C,EAAE,kBAAmB,2BAItCkC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,cAAe,mBAEpBqC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLP,MAAO1B,EAAeK,MACtB6B,SAAWC,GAAMX,EAAkB,QAASW,EAAEC,OAAOV,OACrDI,UAAU,kKACVO,YAAa1C,EAAE,mBAAoB,4BAIvCkC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,QAAS,YAEdqC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLP,MAAO1B,EAAeM,MACtB4B,SAAWC,GAAMX,EAAkB,QAASW,EAAEC,OAAOV,OACrDI,UAAU,kKACVO,YAAa1C,EAAE,aAAc,6BAIjCkC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,cAAe,oBAEpBqC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLP,MAAO1B,EAAeO,YACtB2B,SAAWC,GAAMX,EAAkB,cAAeW,EAAEC,OAAOV,OAC3DI,UAAU,wKAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,YAAa,gBAElBqC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLP,MAAO1B,EAAeQ,UACtB0B,SAAWC,GAAMX,EAAkB,YAAaW,EAAEC,OAAOV,OACzDI,UAAU,kKACVO,YAAa1C,EAAE,iBAAkB,yBAIrCkC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,YAAa,gBAElBqC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLP,MAAO1B,EAAeS,UACtByB,SAAWC,GAAMX,EAAkB,YAAaW,EAAEC,OAAOV,OACzDI,UAAU,kKACVO,YAAa1C,EAAE,qBAAsB,8BAIzCkC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpC,EAAE,oBAAqB,yBAE1BkC,EAAAA,EAAAA,MAAA,UACEH,MAAO1B,EAAeU,kBACtBwB,SAAWC,GAAMX,EAAkB,oBAAqBW,EAAEC,OAAOV,OACjEI,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQN,MAAM,GAAEK,SAAEpC,EAAE,0BAA2B,gCAC/CqC,EAAAA,EAAAA,KAAA,UAAQN,MAAM,cAAaK,SAAC,iBAC5BC,EAAAA,EAAAA,KAAA,UAAQN,MAAM,WAAUK,SAAC,cACzBC,EAAAA,EAAAA,KAAA,UAAQN,MAAM,UAASK,SAAC,aACxBC,EAAAA,EAAAA,KAAA,UAAQN,MAAM,QAAOK,SAAC,oBAM5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CF,EAAAA,EAAAA,MAAA,UACES,QAtJgBC,KACxBtC,EAAkB,CAChBE,KAAM,GACNC,WAAY,GACZC,MAAO,GACPC,MAAO,GACPC,YAAa,GACbC,UAAW,GACXC,UAAW,GACXC,kBAAmB,KAErBE,EAAiB,IACjBI,GAAe,IA2IPc,UAAU,yIAAwIC,SAAA,EAElJC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZnC,EAAE,QAAS,aAEdqC,EAAAA,EAAAA,KAAA,UACEM,QArMWE,UACnB1B,GAAW,GACXE,GAAe,GAEf,UAEQ,IAAIyB,QAAQC,GAAWC,WAAWD,EAAS,MAGjD,MAAME,EAAU3B,EAAa4B,OAAOC,KAE9B9C,EAAeG,MAChB2C,EAAQ3C,KAAK4C,cAAcC,SAAShD,EAAeG,KAAK4C,gBACxDD,EAAQ3B,OAAO4B,cAAcC,SAAShD,EAAeG,KAAK4C,mBACzD/C,EAAeI,YAAc0C,EAAQ1C,WAAW4C,SAAShD,EAAeI,gBACxEJ,EAAeK,OAASyC,EAAQzC,MAAM2C,SAAShD,EAAeK,WAC9DL,EAAeM,OAASwC,EAAQxC,MAAMyC,cAAcC,SAAShD,EAAeM,MAAMyC,mBAClF/C,EAAeO,aAAeuC,EAAQvC,cAAgBP,EAAeO,gBACrEP,EAAeQ,WAChBsC,EAAQtC,UAAUuC,cAAcC,SAAShD,EAAeQ,UAAUuC,gBAClED,EAAQzB,YAAY2B,SAAShD,EAAeQ,eAC3CR,EAAeS,WAAaqC,EAAQrC,UAAUsC,cAAcC,SAAShD,EAAeS,UAAUsC,mBAC9F/C,EAAeU,mBAAqBoC,EAAQpC,kBAAkBqC,cAAcC,SAAShD,EAAeU,kBAAkBqC,iBAI5HnC,EAAiBgC,GAEM,IAAnBA,EAAQK,OACVC,EAAAA,GAAMC,KAAKxD,EAAE,iBAAkB,oDAE/BuD,EAAAA,GAAME,QAAQzD,EAAE,kBAAkB,SAAD0D,OAAWT,EAAQK,OAAM,gBAE9D,CAAE,MAAOK,GACPJ,EAAAA,GAAMI,MAAM3D,EAAE,cAAe,2BAC/B,CAAC,QACCmB,GAAW,EACb,GAiKQyC,SAAU1C,EACViB,UAAU,kIAAiIC,SAE1IlB,GACCgB,EAAAA,EAAAA,MAAA2B,EAAAA,SAAA,CAAAzB,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZnC,EAAE,YAAa,oBAGlBkC,EAAAA,EAAAA,MAAA2B,EAAAA,SAAA,CAAAzB,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBACZnC,EAAE,SAAU,qBAQtBoB,IACCc,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDACZnC,EAAE,gBAAiB,kBAAkB,KAAGgB,EAAcsC,OAAO,SAIjEtC,EAAcsC,OAAS,GACtBjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,UAClBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEpB,EAAc8C,IAAKX,IAClBjB,EAAAA,EAAAA,MAAA,OAEES,QAASA,KAAMoB,OAjLLC,EAiLwBb,EAAQ5B,QAhL1DpB,EAAS,aAADuD,OAAcM,IADIA,OAkLV7B,UAAU,4JAA2JC,SAAA,EAErKF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wFAAuFC,UACpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4DAEfE,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAuB,OACM,WAAnBP,EAAQvB,OACJ,uEACA,oEACHQ,SACApC,EAAEmD,EAAQvB,OAAQuB,EAAQvB,cAI/BS,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEnC,EAAQkD,EAAQ3C,KAAO2C,EAAQ3B,UAGlCU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACbE,EAAAA,EAAAA,KAAA,QAAAD,SAAOe,EAAQ1C,iBAEjByB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACbE,EAAAA,EAAAA,KAAA,QAAAD,SAAOe,EAAQzC,YAEjBwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iCACbE,EAAAA,EAAAA,KAAA,QAAAD,SAAOnC,EAAQkD,EAAQzB,YAAcyB,EAAQtC,gBAE/CqB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACbE,EAAAA,EAAAA,KAAA,QAAAD,SAAOe,EAAQrC,gBAEjBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8BACbD,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOpC,EAAE,YAAa,cAAc,KAAGmD,EAAQxB,oBAInDU,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,yFAAwFC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZnC,EAAE,cAAe,uBA/CjBmD,EAAQ5B,UAuDrBW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEpC,EAAE,iBAAkB,uBAEvBqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CpC,EAAE,gBAAiB,gG", "sources": ["pages/Patients/PatientSearch.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst PatientSearch = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const [searchCriteria, setSearchCriteria] = useState({\n    name: '',\n    nationalId: '',\n    phone: '',\n    email: '',\n    dateOfBirth: '',\n    diagnosis: '',\n    therapist: '',\n    insuranceProvider: ''\n  });\n  const [searchResults, setSearchResults] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [hasSearched, setHasSearched] = useState(false);\n\n  // Mock search results\n  const mockPatients = [\n    {\n      id: 'P001',\n      name: 'أحمد محمد الأحمد',\n      nameEn: '<PERSON>',\n      nationalId: '**********',\n      phone: '+966501234567',\n      email: '<EMAIL>',\n      dateOfBirth: '1995-03-15',\n      age: 29,\n      diagnosis: 'Cerebral Palsy',\n      diagnosisAr: 'الشلل الدماغي',\n      therapist: 'Dr. Sarah Al-Rashid',\n      insuranceProvider: 'Bupa Arabia',\n      lastVisit: '2024-01-22',\n      status: 'active'\n    },\n    {\n      id: 'P002',\n      name: 'فاطمة علي السالم',\n      nameEn: 'Fatima Ali Al-Salem',\n      nationalId: '**********',\n      phone: '+966507654321',\n      email: '<EMAIL>',\n      dateOfBirth: '1988-07-20',\n      age: 36,\n      diagnosis: 'Spinal Cord Injury',\n      diagnosisAr: 'إصابة الحبل الشوكي',\n      therapist: 'Dr. Ahmed Al-Mansouri',\n      insuranceProvider: 'Tawuniya',\n      lastVisit: '2024-01-20',\n      status: 'active'\n    },\n    {\n      id: 'P003',\n      name: 'محمد عبدالله الخالد',\n      nameEn: 'Mohammed Abdullah Al-Khalid',\n      nationalId: '**********',\n      phone: '+966512345678',\n      email: '<EMAIL>',\n      dateOfBirth: '2010-12-05',\n      age: 14,\n      diagnosis: 'Autism Spectrum Disorder',\n      diagnosisAr: 'اضطراب طيف التوحد',\n      therapist: 'Dr. Fatima Al-Zahra',\n      insuranceProvider: 'Medgulf',\n      lastVisit: '2024-01-18',\n      status: 'active'\n    }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setSearchCriteria(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleSearch = async () => {\n    setLoading(true);\n    setHasSearched(true);\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Filter mock data based on search criteria\n      const results = mockPatients.filter(patient => {\n        return (\n          (!searchCriteria.name || \n           patient.name.toLowerCase().includes(searchCriteria.name.toLowerCase()) ||\n           patient.nameEn.toLowerCase().includes(searchCriteria.name.toLowerCase())) &&\n          (!searchCriteria.nationalId || patient.nationalId.includes(searchCriteria.nationalId)) &&\n          (!searchCriteria.phone || patient.phone.includes(searchCriteria.phone)) &&\n          (!searchCriteria.email || patient.email.toLowerCase().includes(searchCriteria.email.toLowerCase())) &&\n          (!searchCriteria.dateOfBirth || patient.dateOfBirth === searchCriteria.dateOfBirth) &&\n          (!searchCriteria.diagnosis || \n           patient.diagnosis.toLowerCase().includes(searchCriteria.diagnosis.toLowerCase()) ||\n           patient.diagnosisAr.includes(searchCriteria.diagnosis)) &&\n          (!searchCriteria.therapist || patient.therapist.toLowerCase().includes(searchCriteria.therapist.toLowerCase())) &&\n          (!searchCriteria.insuranceProvider || patient.insuranceProvider.toLowerCase().includes(searchCriteria.insuranceProvider.toLowerCase()))\n        );\n      });\n      \n      setSearchResults(results);\n      \n      if (results.length === 0) {\n        toast.info(t('noResultsFound', 'No patients found matching your search criteria'));\n      } else {\n        toast.success(t('searchCompleted', `Found ${results.length} patient(s)`));\n      }\n    } catch (error) {\n      toast.error(t('searchError', 'Error performing search'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClearSearch = () => {\n    setSearchCriteria({\n      name: '',\n      nationalId: '',\n      phone: '',\n      email: '',\n      dateOfBirth: '',\n      diagnosis: '',\n      therapist: '',\n      insuranceProvider: ''\n    });\n    setSearchResults([]);\n    setHasSearched(false);\n  };\n\n  const handlePatientClick = (patientId) => {\n    navigate(`/patients/${patientId}`);\n  };\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          {t('patientSearch', 'Patient Search')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          {t('patientSearchDesc', 'Search for patients using various criteria')}\n        </p>\n      </div>\n\n      {/* Search Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-8\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n          <i className=\"fas fa-search text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('searchCriteria', 'Search Criteria')}\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('patientName', 'Patient Name')}\n            </label>\n            <input\n              type=\"text\"\n              value={searchCriteria.name}\n              onChange={(e) => handleInputChange('name', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterPatientName', 'Enter patient name')}\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('nationalId', 'National ID')}\n            </label>\n            <input\n              type=\"text\"\n              value={searchCriteria.nationalId}\n              onChange={(e) => handleInputChange('nationalId', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterNationalId', 'Enter national ID')}\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('phoneNumber', 'Phone Number')}\n            </label>\n            <input\n              type=\"text\"\n              value={searchCriteria.phone}\n              onChange={(e) => handleInputChange('phone', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterPhoneNumber', 'Enter phone number')}\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('email', 'Email')}\n            </label>\n            <input\n              type=\"email\"\n              value={searchCriteria.email}\n              onChange={(e) => handleInputChange('email', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterEmail', 'Enter email address')}\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('dateOfBirth', 'Date of Birth')}\n            </label>\n            <input\n              type=\"date\"\n              value={searchCriteria.dateOfBirth}\n              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('diagnosis', 'Diagnosis')}\n            </label>\n            <input\n              type=\"text\"\n              value={searchCriteria.diagnosis}\n              onChange={(e) => handleInputChange('diagnosis', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterDiagnosis', 'Enter diagnosis')}\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('therapist', 'Therapist')}\n            </label>\n            <input\n              type=\"text\"\n              value={searchCriteria.therapist}\n              onChange={(e) => handleInputChange('therapist', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterTherapistName', 'Enter therapist name')}\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('insuranceProvider', 'Insurance Provider')}\n            </label>\n            <select\n              value={searchCriteria.insuranceProvider}\n              onChange={(e) => handleInputChange('insuranceProvider', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"\">{t('selectInsuranceProvider', 'Select insurance provider')}</option>\n              <option value=\"Bupa Arabia\">Bupa Arabia</option>\n              <option value=\"Tawuniya\">Tawuniya</option>\n              <option value=\"Medgulf\">Medgulf</option>\n              <option value=\"SAICO\">SAICO</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Search Actions */}\n        <div className=\"flex justify-end space-x-4 mt-6\">\n          <button\n            onClick={handleClearSearch}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n          >\n            <i className=\"fas fa-times mr-2\"></i>\n            {t('clear', 'Clear')}\n          </button>\n          <button\n            onClick={handleSearch}\n            disabled={loading}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                {t('searching', 'Searching...')}\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-search mr-2\"></i>\n                {t('search', 'Search')}\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n\n      {/* Search Results */}\n      {hasSearched && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n              <i className=\"fas fa-list text-green-600 dark:text-green-400 mr-2\"></i>\n              {t('searchResults', 'Search Results')} ({searchResults.length})\n            </h2>\n          </div>\n          \n          {searchResults.length > 0 ? (\n            <div className=\"p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n                {searchResults.map((patient) => (\n                  <div\n                    key={patient.id}\n                    onClick={() => handlePatientClick(patient.id)}\n                    className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-all cursor-pointer hover:border-blue-300 dark:hover:border-blue-600\"\n                  >\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\n                        <i className=\"fas fa-user text-blue-600 dark:text-blue-400 text-xl\"></i>\n                      </div>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        patient.status === 'active' \n                          ? 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400'\n                          : 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400'\n                      }`}>\n                        {t(patient.status, patient.status)}\n                      </span>\n                    </div>\n                    \n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                      {isRTL ? patient.name : patient.nameEn}\n                    </h3>\n                    \n                    <div className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n                      <div className=\"flex items-center\">\n                        <i className=\"fas fa-id-card mr-2 w-4\"></i>\n                        <span>{patient.nationalId}</span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <i className=\"fas fa-phone mr-2 w-4\"></i>\n                        <span>{patient.phone}</span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <i className=\"fas fa-stethoscope mr-2 w-4\"></i>\n                        <span>{isRTL ? patient.diagnosisAr : patient.diagnosis}</span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <i className=\"fas fa-user-md mr-2 w-4\"></i>\n                        <span>{patient.therapist}</span>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <i className=\"fas fa-calendar mr-2 w-4\"></i>\n                        <span>{t('lastVisit', 'Last Visit')}: {patient.lastVisit}</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600\">\n                      <button className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                        <i className=\"fas fa-eye mr-2\"></i>\n                        {t('viewDetails', 'View Details')}\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          ) : (\n            <div className=\"p-12 text-center\">\n              <i className=\"fas fa-search text-4xl text-gray-400 mb-4\"></i>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {t('noResultsFound', 'No Results Found')}\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                {t('noResultsDesc', 'No patients found matching your search criteria. Try adjusting your search terms.')}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PatientSearch;\n"], "names": ["PatientSearch", "t", "isRTL", "useLanguage", "navigate", "useNavigate", "searchCriteria", "setSearchCriteria", "useState", "name", "nationalId", "phone", "email", "dateOfBirth", "diagnosis", "therapist", "insuranceProvider", "searchResults", "setSearchResults", "loading", "setLoading", "hasSearched", "setHasSearched", "mockPatients", "id", "nameEn", "age", "diagnosisAr", "lastVisit", "status", "handleInputChange", "field", "value", "prev", "_objectSpread", "_jsxs", "className", "children", "_jsx", "type", "onChange", "e", "target", "placeholder", "onClick", "handleClearSearch", "async", "Promise", "resolve", "setTimeout", "results", "filter", "patient", "toLowerCase", "includes", "length", "toast", "info", "success", "concat", "error", "disabled", "_Fragment", "map", "handlePatientClick", "patientId"], "sourceRoot": ""}