"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2381],{2381:(e,a,t)=>{t.r(a),t.d(a,{default:()=>c});var r=t(2555),s=t(5043),n=t(3216),d=t(7921),l=t(4528),i=t(3768),o=t(579);const c=()=>{const{t:e,isRTL:a}=(0,d.o)(),{user:t}=(0,l.A)(),c=(0,n.Zp)(),m=(0,n.zy)(),[x,p]=(0,s.useState)(!1),[u,y]=(m.pathname.includes("/new"),(0,s.useState)("process")),[g,h]=(0,s.useState)([]),[b,f]=(0,s.useState)(""),[N,j]=(0,s.useState)("all"),[k,v]=(0,s.useState)([]),[w,P]=(0,s.useState)(!1),[A,C]=(0,s.useState)({patientId:"",patientName:"",amount:"",paymentMethod:"",paymentType:"",description:"",invoiceNumber:"",sessionDate:(new Date).toISOString().split("T")[0],copayAmount:"",insuranceAmount:"",discountAmount:"",notes:""}),[S,T]=(0,s.useState)({});(0,s.useEffect)(()=>{M()},[]);const M=async()=>{p(!0);try{const e=new URLSearchParams((0,r.A)((0,r.A)({page:1,limit:50},b&&{search:b}),"all"!==N&&{status:N})),a=await fetch("/api/v1/payments?".concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!a.ok)throw new Error("Failed to load payments");{const e=await a.json();h(e.data||[])}}catch(e){console.error("Error loading payments:",e),i.Ay.error("Failed to load payments");h([{id:"PAY001",patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",patientNameEn:"Ahmed Mohammed Ali",amount:1500,paymentMethod:"cash",paymentType:"full_payment",status:"completed",date:"2024-01-15",invoiceNumber:"INV-2024-001",description:"Physical Therapy Session"},{id:"PAY002",patientName:"\u0641\u0627\u0637\u0645\u0629 \u0623\u062d\u0645\u062f",patientNameEn:"Fatima Ahmed",amount:800,paymentMethod:"card",paymentType:"copay",status:"pending",date:"2024-01-14",invoiceNumber:"INV-2024-002",description:"Initial Assessment"},{id:"PAY003",patientName:"\u0645\u062d\u0645\u062f \u0633\u0627\u0644\u0645",patientNameEn:"Mohammed Salem",amount:2200,paymentMethod:"insurance",paymentType:"insurance_payment",status:"completed",date:"2024-01-13",invoiceNumber:"INV-2024-003",description:"Treatment Package"}])}finally{p(!1)}},R=(e,a)=>{C(t=>(0,r.A)((0,r.A)({},t),{},{[e]:a})),S[e]&&T(a=>(0,r.A)((0,r.A)({},a),{},{[e]:null}))},D=e=>{switch(e){case"completed":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"failed":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";case"refunded":return"text-purple-600 bg-purple-100 dark:bg-purple-900/30 dark:text-purple-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}},E=e=>{switch(e){case"cash":return"fas fa-money-bill-wave";case"card":return"fas fa-credit-card";case"insurance":return"fas fa-shield-alt";case"bank_transfer":return"fas fa-university";default:return"fas fa-coins"}},I=g.filter(e=>{var a;const t=e.patientName.toLowerCase().includes(b.toLowerCase())||(null===(a=e.patientNameEn)||void 0===a?void 0:a.toLowerCase().includes(b.toLowerCase()))||e.invoiceNumber.toLowerCase().includes(b.toLowerCase()),r="all"===N||e.status===N;return t&&r}),F=[{value:"cash",label:e("cash","Cash"),icon:"fas fa-money-bill-wave"},{value:"card",label:e("creditCard","Credit Card"),icon:"fas fa-credit-card"},{value:"bank_transfer",label:e("bankTransfer","Bank Transfer"),icon:"fas fa-university"},{value:"insurance",label:e("insurance","Insurance"),icon:"fas fa-shield-alt"}],_=[{value:"full_payment",label:e("fullPayment","Full Payment")},{value:"copay",label:e("copay","Co-payment")},{value:"deductible",label:e("deductible","Deductible")},{value:"insurance_payment",label:e("insurancePayment","Insurance Payment")},{value:"partial_payment",label:e("partialPayment","Partial Payment")}];return(0,o.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("paymentsManagement","Payments Management")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("paymentsManagementDesc","Process payments, track transactions, and manage financial records")})]}),(0,o.jsx)("div",{className:"mb-6",children:(0,o.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,o.jsxs)("button",{onClick:()=>y("process"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("process"===u?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),e("processPayment","Process Payment")]}),(0,o.jsxs)("button",{onClick:()=>y("history"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("history"===u?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-history mr-2"}),e("paymentHistory","Payment History")]}),(0,o.jsxs)("button",{onClick:()=>y("reports"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("reports"===u?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-chart-bar mr-2"}),e("paymentReports","Payment Reports")]})]})})}),"process"===u&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-credit-card text-green-600 dark:text-green-400 mr-2"}),e("processNewPayment","Process New Payment")]}),(0,o.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),(()=>{const a={};return A.patientName.trim()||(a.patientName=e("patientNameRequired","Patient name is required")),(!A.amount||A.amount<=0)&&(a.amount=e("validAmountRequired","Valid amount is required")),A.paymentMethod||(a.paymentMethod=e("paymentMethodRequired","Payment method is required")),A.paymentType||(a.paymentType=e("paymentTypeRequired","Payment type is required")),T(a),0===Object.keys(a).length})()){p(!0);try{const a={patient:A.patientId,amount:parseFloat(A.amount),method:A.paymentMethod,reference:A.invoiceNumber,notes:A.notes||A.description};"card"===A.paymentMethod&&A.cardDetails&&(a.cardDetails=A.cardDetails),"bank_transfer"===A.paymentMethod&&A.bankDetails&&(a.bankDetails=A.bankDetails);const t=await fetch("/api/v1/payments",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify(a)});if(!t.ok){const e=await t.json();throw new Error(e.message||"Failed to process payment")}{const a=await t.json();C({patientId:"",patientName:"",amount:"",paymentMethod:"",paymentType:"",description:"",invoiceNumber:"",sessionDate:(new Date).toISOString().split("T")[0],copayAmount:"",insuranceAmount:"",discountAmount:"",notes:""}),i.Ay.success(a.message||e("paymentProcessedSuccessfully","Payment processed successfully")),y("history"),M()}}catch(t){i.Ay.error(e("errorProcessingPayment","Error processing payment"))}finally{p(!1)}}else i.Ay.error(e("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("patientName","Patient Name")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:"text",value:A.patientName,onChange:e=>{R("patientName",e.target.value),(async e=>{if(!e||e.length<2)v([]);else try{const a=await fetch("/api/v1/patients/search?q=".concat(encodeURIComponent(e)),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(a.ok){const e=await a.json();v(e.data||[])}}catch(a){console.error("Error searching patients:",a)}})(e.target.value),P(!0)},onFocus:()=>P(!0),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(S.patientName?"border-red-500":"border-gray-300"),placeholder:e("enterPatientName","Enter patient name")}),(0,o.jsx)("i",{className:"fas fa-search absolute right-3 top-3 text-gray-400"})]}),w&&k.length>0&&(0,o.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:k.map(e=>(0,o.jsxs)("div",{onClick:()=>(e=>{C(a=>(0,r.A)((0,r.A)({},a),{},{patientId:e._id,patientName:"".concat(e.firstName," ").concat(e.lastName)})),v([]),P(!1)})(e),className:"px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0",children:[(0,o.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[e.firstName," ",e.lastName]}),(0,o.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.nationalId," \u2022 ",e.phone]})]},e._id))}),S.patientName&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:S.patientName})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("invoiceNumber","Invoice Number")}),(0,o.jsx)("input",{type:"text",value:A.invoiceNumber,onChange:e=>R("invoiceNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterInvoiceNumber","Enter invoice number")})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("amount","Amount")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:"number",step:"0.01",min:"0",value:A.amount,onChange:e=>R("amount",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(S.amount?"border-red-500":"border-gray-300"),placeholder:"0.00"}),(0,o.jsx)("span",{className:"absolute right-3 top-2 text-gray-500 dark:text-gray-400",children:e("sar","SAR")})]}),S.amount&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:S.amount})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("paymentMethod","Payment Method")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsxs)("select",{value:A.paymentMethod,onChange:e=>R("paymentMethod",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(S.paymentMethod?"border-red-500":"border-gray-300"),children:[(0,o.jsx)("option",{value:"",children:e("selectPaymentMethod","Select payment method")}),F.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))]}),S.paymentMethod&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:S.paymentMethod})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("paymentType","Payment Type")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsxs)("select",{value:A.paymentType,onChange:e=>R("paymentType",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(S.paymentType?"border-red-500":"border-gray-300"),children:[(0,o.jsx)("option",{value:"",children:e("selectPaymentType","Select payment type")}),_.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))]}),S.paymentType&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:S.paymentType})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("sessionDate","Session Date")}),(0,o.jsx)("input",{type:"date",value:A.sessionDate,onChange:e=>R("sessionDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("description","Description")}),(0,o.jsx)("input",{type:"text",value:A.description,onChange:e=>R("description",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterDescription","Enter payment description")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("notes","Notes")}),(0,o.jsx)("textarea",{value:A.notes,onChange:e=>R("notes",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterNotes","Enter additional notes")})]}),(0,o.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,o.jsx)("button",{type:"button",onClick:()=>c("/financial"),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:e("cancel","Cancel")}),(0,o.jsx)("button",{type:"submit",disabled:x,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),e("processing","Processing...")]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-credit-card mr-2"}),e("processPayment","Process Payment")]})})]})]})]}),"history"===u&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,o.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-history text-blue-600 dark:text-blue-400 mr-2"}),e("paymentHistory","Payment History")]}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:"text",value:b,onChange:e=>f(e.target.value),placeholder:e("searchPayments","Search payments..."),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),(0,o.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]}),(0,o.jsxs)("select",{value:N,onChange:e=>j(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,o.jsx)("option",{value:"completed",children:e("completed","Completed")}),(0,o.jsx)("option",{value:"pending",children:e("pending","Pending")}),(0,o.jsx)("option",{value:"failed",children:e("failed","Failed")}),(0,o.jsx)("option",{value:"refunded",children:e("refunded","Refunded")})]})]})]})}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full",children:[(0,o.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("amount","Amount")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("method","Method")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("type","Type")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("date","Date")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,o.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:I.map(t=>(0,o.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a?t.patientName:t.patientNameEn}),(0,o.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.invoiceNumber})]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[t.amount.toLocaleString()," ",e("sar","SAR")]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"".concat(E(t.paymentMethod)," text-gray-400 mr-2")}),(0,o.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:e(t.paymentMethod,t.paymentMethod)})]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:e(t.paymentType,t.paymentType)})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(D(t.status)),children:e(t.status,t.status)})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.date}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200",children:(0,o.jsx)("i",{className:"fas fa-eye"})}),(0,o.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200",children:(0,o.jsx)("i",{className:"fas fa-print"})}),"completed"===t.status&&(0,o.jsx)("button",{className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200",children:(0,o.jsx)("i",{className:"fas fa-undo"})})]})})]},t.id))})]})}),0===I.length&&(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("i",{className:"fas fa-receipt text-4xl text-gray-400 mb-4"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("noPaymentsFound","No payments found")})]})]}),"reports"===u&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-chart-bar text-purple-600 dark:text-purple-400 mr-2"}),e("paymentReports","Payment Reports")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,o.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:e("dailyRevenue","Daily Revenue")}),(0,o.jsxs)("p",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:["12,500 ",e("sar","SAR")]})]}),(0,o.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-calendar-day text-blue-600 dark:text-blue-400 text-xl"})})]})}),(0,o.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:e("monthlyRevenue","Monthly Revenue")}),(0,o.jsxs)("p",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:["125,000 ",e("sar","SAR")]})]}),(0,o.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-calendar-alt text-green-600 dark:text-green-400 text-xl"})})]})}),(0,o.jsx)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("p",{className:"text-sm font-medium text-purple-800 dark:text-purple-200",children:e("collectionRate","Collection Rate")}),(0,o.jsx)("p",{className:"text-2xl font-bold text-purple-900 dark:text-purple-100",children:"92.5%"})]}),(0,o.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-percentage text-purple-600 dark:text-purple-400 text-xl"})})]})})]}),(0,o.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsx)("button",{className:"p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-file-excel text-green-600 dark:text-green-400 text-2xl mr-4"}),(0,o.jsxs)("div",{className:"text-left",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e("exportToExcel","Export to Excel")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("downloadPaymentData","Download payment data as Excel file")})]})]})}),(0,o.jsx)("button",{className:"p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-file-pdf text-red-600 dark:text-red-400 text-2xl mr-4"}),(0,o.jsxs)("div",{className:"text-left",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e("generateReport","Generate Report")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("createDetailedReport","Create detailed payment report")})]})]})})]})]})]})}}}]);
//# sourceMappingURL=2381.136bc80f.chunk.js.map