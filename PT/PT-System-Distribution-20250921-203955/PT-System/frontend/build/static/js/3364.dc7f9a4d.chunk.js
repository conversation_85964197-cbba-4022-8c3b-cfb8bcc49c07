"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3364],{3364:(e,s,t)=>{t.r(s),t.d(s,{default:()=>n});var a=t(5043),l=t(8816),r=t(6305),o=t(579);const n=()=>{const[e,s]=(0,a.useState)(null),[t,n]=(0,a.useState)(!1);return(0,o.jsxs)("div",{className:"p-6 max-w-4xl mx-auto",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Authentication Debug"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,o.jsx)("button",{onClick:async()=>{n(!0),s(null);try{console.log("Testing direct API call...");const e=await r.mo.post("/auth/login",{email:"<EMAIL>",password:"Admin123!"});console.log("Direct API response:",e),s({type:"direct-api",success:!0,data:e})}catch(t){var e;console.error("Direct API error:",t),s({type:"direct-api",success:!1,error:t.message,details:null===(e=t.response)||void 0===e?void 0:e.data})}finally{n(!1)}},disabled:t,className:"p-4 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:"Test Direct API Call"}),(0,o.jsx)("button",{onClick:async()=>{n(!0),s(null);try{console.log("Testing auth service...");const e=await l.A.login({username:"<EMAIL>",password:"Admin123!"});console.log("Auth service response:",e),s({type:"auth-service",success:!0,data:e})}catch(e){console.error("Auth service error:",e),s({type:"auth-service",success:!1,error:e.message})}finally{n(!1)}},disabled:t,className:"p-4 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50",children:"Test Auth Service"}),(0,o.jsx)("button",{onClick:()=>{const e=localStorage.getItem("pt_auth_token"),t=localStorage.getItem("pt_user_data");s({type:"stored-data",data:{token:e?e.substring(0,20)+"...":"Not found",user:t?JSON.parse(t):"Not found",isAuthenticated:l.A.isAuthenticated()}})},className:"p-4 bg-yellow-600 text-white rounded hover:bg-yellow-700",children:"Check Stored Data"}),(0,o.jsx)("button",{onClick:()=>{localStorage.removeItem("pt_auth_token"),localStorage.removeItem("pt_user_data"),delete r.mo.defaults.headers.common.Authorization,s({type:"clear",message:"Stored data cleared"})},className:"p-4 bg-red-600 text-white rounded hover:bg-red-700",children:"Clear Stored Data"})]}),t&&(0,o.jsxs)("div",{className:"text-center py-4",children:[(0,o.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,o.jsx)("p",{className:"mt-2",children:"Testing..."})]}),e&&(0,o.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg",children:[(0,o.jsxs)("h3",{className:"font-bold mb-2",children:["Result (",e.type,"):"]}),(0,o.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,o.jsxs)("div",{className:"mt-6 bg-blue-50 p-4 rounded-lg",children:[(0,o.jsx)("h3",{className:"font-bold mb-2",children:"Environment Info:"}),(0,o.jsxs)("ul",{className:"text-sm",children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"API Base URL:"})," ","http://localhost:5001/api/v1"]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Node ENV:"})," ","production"]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Current URL:"})," ",window.location.href]})]})]})]})}}}]);
//# sourceMappingURL=3364.dc7f9a4d.chunk.js.map