{"version": 3, "file": "static/js/7580.b8bbc85c.chunk.js", "mappings": "kRAmCA,EA5BaA,IAUN,IAVO,SACZC,EAAQ,UACRC,EAAY,GAAE,QACdC,EAAU,MAAK,OACfC,EAAS,YAAW,OACpBC,EAAS,yBAAwB,QACjCC,EAAU,aAAY,WACtBC,EAAa,WAAU,MACvBC,EAAQ,IAETR,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,MAAMC,EAAc,CAClBL,EACAF,EACAC,EACAF,EACAD,EACAK,EACAN,GACAW,OAAOC,SAASC,KAAK,KAEvB,OACEC,EAAAA,EAAAA,KAAA,OAAAC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CAAKf,UAAWU,GAAiBH,GAAK,IAAAR,SACnCA,K,sFC7BP,SAASiB,EAAYlB,EAIlBmB,GAAQ,IAJW,MACpBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qcAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBJ,E,iBCvBlD,SAASR,EAAyByB,EAAGC,GACnC,GAAI,MAAQD,EAAG,MAAO,CAAC,EACvB,IAAIE,EACFC,EACAC,ECLJ,SAAuCD,EAAGH,GACxC,GAAI,MAAQG,EAAG,MAAO,CAAC,EACvB,IAAIF,EAAI,CAAC,EACT,IAAK,IAAII,KAAKF,EAAG,GAAI,CAAC,EAAEG,eAAeC,KAAKJ,EAAGE,GAAI,CACjD,IAAK,IAAML,EAAEQ,QAAQH,GAAI,SACzBJ,EAAEI,GAAKF,EAAEE,EACX,CACA,OAAOJ,CACT,CDHQ,CAA6BD,EAAGC,GACtC,GAAIb,OAAOqB,sBAAuB,CAChC,IAAIJ,EAAIjB,OAAOqB,sBAAsBT,GACrC,IAAKG,EAAI,EAAGA,EAAIE,EAAEK,OAAQP,IAAKD,EAAIG,EAAEF,IAAK,IAAMF,EAAEO,QAAQN,IAAM,CAAC,EAAES,qBAAqBJ,KAAKP,EAAGE,KAAOE,EAAEF,GAAKF,EAAEE,GAClH,CACA,OAAOE,CACT,C,sGEVA,SAASQ,EAAe/C,EAIrBmB,GAAQ,IAJc,MACvBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0WAEP,CACA,MACA,EADiCZ,EAAAA,WAAiByB,E,sFCvBlD,SAASC,EAAehD,EAIrBmB,GAAQ,IAJc,MACvBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,mEAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB0B,E,sFCvBlD,SAASC,EAASjD,EAIfmB,GAAQ,IAJQ,MACjBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,8XAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB2B,E,sFCvBlD,SAASC,EAASlD,EAIfmB,GAAQ,IAJQ,MACjBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB4B,E,sFCvBlD,SAASC,EAAWnD,EAIjBmB,GAAQ,IAJU,MACnBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,0EAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB6B,E,6LCvBlD,SAASC,EAAYpD,EAIlBmB,GAAQ,IAJW,MACpBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,geAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB8B,G,2CCFlD,MA2dA,EA3dyBC,KACvB,MAAM,EAAEjB,IAAMkB,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,CAC7CG,IAAK,CAAEC,MAAO,GAAIC,OAAQ,QAC1BC,OAAQ,CAAEF,MAAO,GAAIG,MAAO,GAAIC,KAAM,IAAKH,OAAQ,QACnDI,KAAM,CAAEL,MAAO,GAAIG,MAAO,IAAKC,KAAM,IAAKH,OAAQ,WAClDK,QAAS,CAAEC,QAAS,IAAKC,SAAU,GAAIP,OAAQ,QAC/CQ,SAAU,CAAEC,YAAa,GAAIC,eAAgB,IAAKV,OAAQ,QAC1DW,OAAQ,+BACRC,WAAY,IAAIC,KAAKA,KAAKC,MAAQ,OAAUC,cAC5CC,YAAa,GACbC,cAAe,MACfC,UAAW,OAGNC,EAAUC,IAAezB,EAAAA,EAAAA,UAAS,KAEzC0B,EAAAA,EAAAA,WAAU,KACRC,IACA,MAAMC,EAAWC,YAAYF,EAAiB,KAC9C,MAAO,IAAMG,cAAcF,IAC1B,IAEH,MAAMD,EAAkBI,UACtB,IACEhC,GAAW,GAGC,IAAImB,KACEc,YAAYC,WAD9B,MAGMC,EADcF,YAAYb,MAE1BgB,EAAcC,KAAKC,MAAMH,EAAQ,MACjCI,EAAgBF,KAAKC,MAAOH,EAAQ,KAAmB,KAGvDK,EAAaP,YAAY1B,QAAU,CAAC,EACpCkC,EAAiBD,EAAWC,gBAAkB,EAC9CC,EAAkBF,EAAWE,iBAAmB,EAChDC,EAAkBH,EAAWG,iBAAmB,EAGhDC,EAAqBF,EAAkB,EAAKD,EAAiBC,EAAmB,IAAM,EAGtFG,EAAaC,UAAUD,YAAc,CAAC,EACtCE,EAAcF,EAAWG,eAAiB,UAC1CC,EAAWJ,EAAWI,UAAY,EAGlCC,EAAWb,KAAKc,IAAI,IAAKd,KAAKe,IAAI,EAAG,GAAqB,GAAhBf,KAAKgB,WAG/C/B,EAAcgC,SAASC,aAAaC,QAAQ,gBAAkB,KAG9DjC,EAAgB+B,SAASG,eAAeD,QAAQ,kBAAoB,KAAO,EACjFC,eAAeC,QAAQ,gBAAiBnC,EAAcoC,YAEtD,MAAMC,EAAY,CAChBxD,IAAK,CACHC,MAAO6C,EACP5C,OAAQ4C,EAAW,GAAK,UAAYA,EAAW,GAAK,QAAU,QAEhE3C,OAAQ,CACNF,MAAOuC,EACPpC,MAAO6B,KAAKwB,OAAOlB,GAAmB,YAAU,WAA2B,IAAM,GACjFlC,KAAM4B,KAAKwB,OAAOpB,GAAkB,GAAC,WAA2B,IAAM,GACtEnC,OAAQsC,EAAqB,GAAK,UAAYA,EAAqB,GAAK,QAAU,QAEpFlC,KAAM,CACJL,MAAO,GACPG,MAAO,IACPC,KAAM,IACNH,OAAQ,QAEVK,QAAS,CACPC,QAAoB,IAAXqC,GAAmB,IAC5BpC,SAAsB,IAAXoC,EAAkB,IAAQ,GACrCa,KAAMf,EACNzC,OAAQ2C,EAAW,EAAI,OAASA,EAAW,GAAM,UAAY,SAE/DnC,SAAU,CACRC,YAAasB,KAAKC,MAAsB,GAAhBD,KAAKgB,UAAiB,EAC9CrC,eAAgB,IAChBV,OAAQ,QAEVW,OAAO,GAAD8C,OAAK1B,KAAKC,MAAMF,EAAc,IAAG,MAAA2B,OAAK3B,EAAc,GAAE,MAAA2B,OAAKxB,EAAa,KAC9ErB,WAAY,IAAIC,KAAKA,KAAKC,MAAQ,OAAUC,cAC5CC,YAAaA,EACbC,cAAeA,EACfC,UAA2B,IAAhBa,KAAKgB,SAChBW,YAAa,CACXC,KAAMnB,UAAUoB,UAAUC,SAAS,UAAY,SACzCrB,UAAUoB,UAAUC,SAAS,WAAa,UAC1CrB,UAAUoB,UAAUC,SAAS,UAAY,SAAW,UAC1DC,QAAStB,UAAUuB,WACnBC,SAAUxB,UAAUwB,SACpBC,SAAUzB,UAAUyB,SACpBC,cAAe1B,UAAU0B,cACzBC,OAAQ3B,UAAU2B,SAItBtE,EAAeyD,GAGf,MAAMc,EAAkB,CACtB,CACET,KAAM,2BACN3D,OAAQ,UACRW,OAAO,GAAD8C,OAAK1B,KAAKC,MAAMF,EAAc,IAAG,MAAA2B,OAAK3B,EAAc,GAAE,KAC5DuC,KAAMC,OAAOC,SAASF,MAAQ,KAC9BG,YAAa,+BAEf,CACEb,KAAM,uBACN3D,OAAQwC,UAAU2B,OAAS,UAAY,QACvCxD,OAAQ6B,UAAU2B,OAAM,GAAAV,OAAM1B,KAAKC,MAAMF,EAAc,IAAG,MAAA2B,OAAK3B,EAAc,GAAE,KAAM,KACrFuC,KAAM,MACNG,YAAa,2BAEf,CACEb,KAAM,gBACN3D,OAA4B,qBAAbyE,QAA2B,UAAY,QACtD9D,OAA4B,qBAAb8D,QAAwB,GAAAhB,OAAM1B,KAAKC,MAAMF,EAAc,IAAG,MAAA2B,OAAK3B,EAAc,GAAE,KAAM,KACpGuC,KAAM,MACNG,YAAa,2BAEf,CACEb,KAAM,qBACN3D,OAAQmD,eAAiB,UAAY,QACrCxC,OAAQwC,eAAc,GAAAM,OAAM1B,KAAKC,MAAMF,EAAc,IAAG,MAAA2B,OAAK3B,EAAc,GAAE,KAAM,KACnFuC,KAAM,MACNG,YAAa,yBAEf,CACEb,KAAM,iBACN3D,OAAQ,kBAAmBwC,UAAY,UAAY,UACnD7B,OAAQ,kBAAmB6B,UAAS,GAAAiB,OAAM1B,KAAKC,MAAMF,EAAc,IAAG,MAAA2B,OAAK3B,EAAc,GAAE,KAAM,KACjGuC,KAAM,MACNG,YAAa,+BAEf,CACEb,KAAM,sBACN3D,OAAQ,gBAAiBwC,UAAY,UAAY,UACjD7B,OAAQ,gBAAiB6B,UAAS,GAAAiB,OAAM1B,KAAKC,MAAMF,EAAc,IAAG,MAAA2B,OAAK3B,EAAc,GAAE,KAAM,KAC/FuC,KAAM,MACNG,YAAa,4BAIjBpD,EAAYgD,EAEd,CAAE,MAAOM,GACPC,QAAQD,MAAM,+BAAgCA,GAE9C7E,EAAe+E,IAAIzH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdyH,GAAI,IACP9E,KAAG3C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOyH,EAAK9E,KAAG,IAAEC,MAAOgC,KAAKe,IAAI,GAAIf,KAAKc,IAAI,GAAI+B,EAAK9E,IAAIC,MAAgC,IAAvBgC,KAAKgB,SAAW,QACvF9C,QAAM9C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOyH,EAAK3E,QAAM,IAAEF,MAAOgC,KAAKe,IAAI,GAAIf,KAAKc,IAAI,GAAI+B,EAAK3E,OAAOF,MAAgC,GAAvBgC,KAAKgB,SAAW,QAChG1C,SAAOlD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACFyH,EAAKvE,SAAO,IACfC,QAASyB,KAAKe,IAAI,GAAIf,KAAKc,IAAI,IAAK+B,EAAKvE,QAAQC,QAAkC,IAAvByB,KAAKgB,SAAW,MAC5ExC,SAAUwB,KAAKe,IAAI,GAAIf,KAAKc,IAAI,IAAK+B,EAAKvE,QAAQE,SAAmC,IAAvBwB,KAAKgB,SAAW,UAGpF,CAAC,QACCrD,GAAW,EACb,GAGImF,EAAkB7E,IACtB,OAAQA,GACN,IAAK,OACL,IAAK,UACH,MAAO,iBACT,IAAK,UACH,MAAO,kBACT,IAAK,QACL,IAAK,UACH,MAAO,eACT,QACE,MAAO,kBAIP8E,EAAiB9E,IACrB,OAAQA,GACN,IAAK,OACL,IAAK,UACH,OAAO9C,EAAAA,EAAAA,KAACgC,EAAAA,EAAe,CAAC9C,UAAU,2BACpC,IAAK,UACH,OAAOc,EAAAA,EAAAA,KAAC6H,EAAAA,EAAuB,CAAC3I,UAAU,4BAC5C,IAAK,QACL,IAAK,UACH,OAAOc,EAAAA,EAAAA,KAACmC,EAAAA,EAAW,CAACjD,UAAU,yBAChC,QACE,OAAOc,EAAAA,EAAAA,KAACgC,EAAAA,EAAe,CAAC9C,UAAU,4BAIlC4I,EAAa9I,IAAA,IAAC,MAAEoB,EAAK,MAAE2H,EAAK,KAAEC,EAAI,MAAEnF,EAAK,OAAEC,EAAQmF,KAAMC,EAAI,MAAEC,EAAQ,QAAQnJ,EAAA,OACnFoJ,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACnJ,UAAU,MAAKD,SAAA,EACnBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,yCAAwCD,SAAA,EACrDmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oBAAmBD,SAAA,EAChCe,EAAAA,EAAAA,KAACkI,EAAI,CAAChJ,UAAS,gBAAAqH,OAAkB4B,EAAK,gBACtCnI,EAAAA,EAAAA,KAAA,MAAId,UAAU,sCAAqCD,SAAEmB,OAEtDwH,EAAc9E,OAGjBsF,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,YAAWD,SAAA,EACxBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,mCAAkCD,SAAE8I,KACpD/H,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAE+I,YAGhCM,IAAVzF,IACCuF,EAAAA,EAAAA,MAAAG,EAAAA,SAAA,CAAAtJ,SAAA,EACEe,EAAAA,EAAAA,KAAA,OAAKd,UAAU,sCAAqCD,UAClDe,EAAAA,EAAAA,KAAA,OACEd,UAAS,oBAAAqH,OACP1D,EAAQ,GAAK,aAAeA,EAAQ,GAAK,gBAAkB,gBAE7D2F,MAAO,CAAEC,MAAM,GAADlC,OAAK1D,EAAK,WAG5BuF,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,wBAAuBD,SAAA,CAAE4D,EAAM,sBAOlD6F,EAAcC,IAAA,IAAC,QAAEC,GAASD,EAAA,OAC9BP,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,0EAAyED,SAAA,EACtFmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oBAAmBD,SAAA,CAC/B2I,EAAcgB,EAAQ9F,SACvBsF,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,OAAMD,SAAA,EACnBe,EAAAA,EAAAA,KAAA,MAAId,UAAU,4BAA2BD,SAAE2J,EAAQnC,QACnDzG,EAAAA,EAAAA,KAAA,KAAGd,UAAU,wBAAuBD,SAAE2J,EAAQtB,cAC5B,QAAjBsB,EAAQzB,OACPiB,EAAAA,EAAAA,MAAA,KAAGlJ,UAAU,wBAAuBD,SAAA,CAAC,SAAO2J,EAAQzB,eAI1DiB,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,aAAYD,SAAA,EACzBe,EAAAA,EAAAA,KAAA,QAAMd,UAAS,uBAAAqH,OAAyBoB,EAAeiB,EAAQ9F,SAAU7D,SACtE2J,EAAQ9F,OAAO+F,OAAO,GAAGC,cAAgBF,EAAQ9F,OAAOiG,MAAM,MAEjEX,EAAAA,EAAAA,MAAA,KAAGlJ,UAAU,wBAAuBD,SAAA,CAAC,WAAS2J,EAAQnF,iBAK5D,OACE2E,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,YAAWD,SAAA,EAExBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDmJ,EAAAA,EAAAA,MAAA,OAAAnJ,SAAA,EACEmJ,EAAAA,EAAAA,MAAA,MAAIlJ,UAAU,qDAAoDD,SAAA,EAChEe,EAAAA,EAAAA,KAACE,EAAAA,EAAY,CAAChB,UAAU,+BACvBkC,EAAE,mBAAoB,yBAEzBpB,EAAAA,EAAAA,KAAA,KAAGd,UAAU,qBAAoBD,SAC9BmC,EAAE,8BAA+B,4DAGtCgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,aAAYD,SAAA,EACzBe,EAAAA,EAAAA,KAAA,KAAGd,UAAU,wBAAuBD,SAAEmC,EAAE,cAAe,mBACvDpB,EAAAA,EAAAA,KAAA,KAAGd,UAAU,oCAAmCD,UAAE,IAAI0E,MAAOqF,8BAKjEZ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,uDAAsDD,SAAA,EACnEe,EAAAA,EAAAA,KAAC8H,EAAU,CACT1H,MAAOgB,EAAE,WAAY,aACrB2G,MAAOrF,EAAYE,IAAIC,MAAMoG,QAAQ,GACrCjB,KAAK,IACLnF,MAAOH,EAAYE,IAAIC,MACvBC,OAAQJ,EAAYE,IAAIE,OACxBmF,KAAMiB,EAAAA,EACNf,MAAM,UAGRnI,EAAAA,EAAAA,KAAC8H,EAAU,CACT1H,MAAOgB,EAAE,cAAe,gBACxB2G,MAAOrF,EAAYK,OAAOE,KAAKgG,QAAQ,GACvCjB,KAAI,QAAAzB,OAAU7D,EAAYK,OAAOC,MAAK,MACtCH,MAAOH,EAAYK,OAAOF,MAC1BC,OAAQJ,EAAYK,OAAOD,OAC3BmF,KAAMlG,EAAAA,EACNoG,MAAM,WAGRnI,EAAAA,EAAAA,KAAC8H,EAAU,CACT1H,MAAOgB,EAAE,YAAa,cACtB2G,MAAOrF,EAAYQ,KAAKD,KACxB+E,KAAI,QAAAzB,OAAU7D,EAAYQ,KAAKF,MAAK,MACpCH,MAAOH,EAAYQ,KAAKL,MACxBC,OAAQJ,EAAYQ,KAAKJ,OACzBmF,KAAMlG,EAAAA,EACNoG,MAAM,YAGRnI,EAAAA,EAAAA,KAAC8H,EAAU,CACT1H,MAAOgB,EAAE,cAAe,gBACxB2G,MAAOrF,EAAYoB,YACnBkE,KAAK,QACLlF,OAAO,OACPmF,KAAMhG,EAAAA,EACNkG,MAAM,eAKVC,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,wCAAuCD,SAAA,EACpDmJ,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACnJ,UAAU,MAAKD,SAAA,EACnBmJ,EAAAA,EAAAA,MAAA,MAAIlJ,UAAU,6DAA4DD,SAAA,EACxEe,EAAAA,EAAAA,KAACoC,EAAY,CAAClD,UAAU,+BACvBkC,EAAE,iBAAkB,uBAEvBgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,YAAWD,SAAA,EACxBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,UAAW,cACtDgH,EAAAA,EAAAA,MAAA,QAAMlJ,UAAU,uCAAsCD,SAAA,CACnDyD,EAAYS,QAAQC,QAAQ,eAGjCgF,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,WAAY,eACvDgH,EAAAA,EAAAA,MAAA,QAAMlJ,UAAU,sCAAqCD,SAAA,CAClDyD,EAAYS,QAAQE,SAAS,qBAMtC+E,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACnJ,UAAU,MAAKD,SAAA,EACnBmJ,EAAAA,EAAAA,MAAA,MAAIlJ,UAAU,6DAA4DD,SAAA,EACxEe,EAAAA,EAAAA,KAACkC,EAAAA,EAAS,CAAChD,UAAU,+BACpBkC,EAAE,aAAc,0BAEnBgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,YAAWD,SAAA,EACxBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,SAAU,aACrDpB,EAAAA,EAAAA,KAAA,QAAMd,UAAU,oCAAmCD,SAAEyD,EAAYe,aAEnE2E,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,gBAAiB,qBAC5DpB,EAAAA,EAAAA,KAAA,QAAMd,UAAU,oCAAmCD,SAAEyD,EAAYqB,cAAcoF,uBAEjFf,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,YAAa,iBACxDgH,EAAAA,EAAAA,MAAA,QAAMlJ,UAAU,oCAAmCD,SAAA,EAA2B,IAAxByD,EAAYsB,WAAiBiF,QAAQ,GAAG,WAEhGb,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,aAAc,kBACzDpB,EAAAA,EAAAA,KAAA,QAAMd,UAAU,oCAAmCD,SAChD,IAAI0E,KAAKjB,EAAYgB,YAAY0F,oCAQ5ChB,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACnJ,UAAU,MAAKD,SAAA,EACnBe,EAAAA,EAAAA,KAAA,MAAId,UAAU,2CAA0CD,SACrDmC,EAAE,iBAAkB,sBAEvBpB,EAAAA,EAAAA,KAAA,OAAKd,UAAU,wCAAuCD,SACnDgF,EAASoF,IAAI,CAACT,EAASU,KACtBtJ,EAAAA,EAAAA,KAAC0I,EAAW,CAAaE,QAASA,GAAhBU,UAMxBlB,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACnJ,UAAU,MAAKD,SAAA,EACnBmJ,EAAAA,EAAAA,MAAA,MAAIlJ,UAAU,6DAA4DD,SAAA,EACxEe,EAAAA,EAAAA,KAAC+B,EAAAA,EAAe,CAAC7C,UAAU,+BAC1BkC,EAAE,iBAAkB,uBAEvBgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,wCAAuCD,SAAA,EACpDmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,cAAaD,SAAA,EAC1Be,EAAAA,EAAAA,KAAA,KAAGd,UAAU,oCAAmCD,SAAEyD,EAAYY,SAASC,eACvEvD,EAAAA,EAAAA,KAAA,KAAGd,UAAU,wBAAuBD,SAAEmC,EAAE,oBAAqB,4BAE/DgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,cAAaD,SAAA,EAC1Be,EAAAA,EAAAA,KAAA,KAAGd,UAAU,mCAAkCD,SAAEyD,EAAYY,SAASE,kBACtExD,EAAAA,EAAAA,KAAA,KAAGd,UAAU,wBAAuBD,SAAEmC,EAAE,iBAAkB,yBAE5DgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,cAAaD,SAAA,EAC1BmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,mCAAkCD,SAAA,CAC9C2I,EAAclF,EAAYY,SAASR,SACpC9C,EAAAA,EAAAA,KAAA,QAAMd,UAAS,8BAAAqH,OAAgCoB,EAAejF,EAAYY,SAASR,SAAU7D,SAC1FyD,EAAYY,SAASR,OAAO+F,OAAO,GAAGC,cAAgBpG,EAAYY,SAASR,OAAOiG,MAAM,SAG7F/I,EAAAA,EAAAA,KAAA,KAAGd,UAAU,wBAAuBD,SAAEmC,EAAE,SAAU,qBAMvDsB,EAAY8D,cACX4B,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACnJ,UAAU,MAAKD,SAAA,EACnBmJ,EAAAA,EAAAA,MAAA,MAAIlJ,UAAU,6DAA4DD,SAAA,EACxEe,EAAAA,EAAAA,KAACoC,EAAY,CAAClD,UAAU,+BACvBkC,EAAE,qBAAsB,2BAE3BgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,uDAAsDD,SAAA,EACnEmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,YAAWD,SAAA,EACxBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,UAAW,cACtDpB,EAAAA,EAAAA,KAAA,QAAMd,UAAU,oCAAmCD,SAAEyD,EAAY8D,YAAYC,WAE/E2B,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,WAAY,eACvDpB,EAAAA,EAAAA,KAAA,QAAMd,UAAU,oCAAmCD,SAAEyD,EAAY8D,YAAYM,eAE/EsB,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,WAAY,eACvDpB,EAAAA,EAAAA,KAAA,QAAMd,UAAU,oCAAmCD,SAAEyD,EAAY8D,YAAYO,kBAGjFqB,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,YAAWD,SAAA,EACxBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,iBAAkB,sBAC7DpB,EAAAA,EAAAA,KAAA,QAAMd,UAAS,uBAAAqH,OAAyB7D,EAAY8D,YAAYQ,cAAgB,iBAAmB,gBAAiB/H,SACjHyD,EAAY8D,YAAYQ,cAAgB5F,EAAE,MAAO,OAASA,EAAE,KAAM,YAGvEgH,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,eAAgB,oBAC3DpB,EAAAA,EAAAA,KAAA,QAAMd,UAAS,uBAAAqH,OAAyB7D,EAAY8D,YAAYS,OAAS,iBAAmB,gBAAiBhI,SAC1GyD,EAAY8D,YAAYS,OAAS7F,EAAE,SAAU,UAAYA,EAAE,UAAW,gBAG1EsB,EAAYS,QAAQmD,OACnB8B,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,iBAAkB,sBAC7DpB,EAAAA,EAAAA,KAAA,QAAMd,UAAU,oCAAmCD,SAAEyD,EAAYS,QAAQmD,cAI/E8B,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,YAAWD,SAAA,EACxBmJ,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,mBAAoB,wBAC/DgH,EAAAA,EAAAA,MAAA,QAAMlJ,UAAU,oCAAmCD,SAAA,CAChDmI,OAAOmC,OAAOd,MAAM,MAAIrB,OAAOmC,OAAOC,cAG3CpB,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,eAAgB,oBAC3DgH,EAAAA,EAAAA,MAAA,QAAMlJ,UAAU,oCAAmCD,SAAA,CAChDmI,OAAOqC,WAAW,MAAIrC,OAAOsC,mBAGlCtB,EAAAA,EAAAA,MAAA,OAAKlJ,UAAU,oCAAmCD,SAAA,EAChDe,EAAAA,EAAAA,KAAA,QAAMd,UAAU,wBAAuBD,SAAEmC,EAAE,aAAc,kBACzDgH,EAAAA,EAAAA,MAAA,QAAMlJ,UAAU,oCAAmCD,SAAA,CAAEmI,OAAOmC,OAAOI,WAAW,yB,sFCte9F,SAAS9B,EAAuB7I,EAI7BmB,GAAQ,IAJsB,MAC/BC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qLAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBuH,E,sFCvBlD,SAASqB,EAAWlK,EAIjBmB,GAAQ,IAJU,MACnBC,EAAK,QACLC,GAEDrB,EADIS,GAAKC,EAAAA,EAAAA,GAAAV,EAAAW,GAER,OAAoBW,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKX,EACL,kBAAmBE,GAClBZ,GAAQW,EAAqBE,EAAAA,cAAoB,QAAS,CAC3DS,GAAIV,GACHD,GAAS,KAAmBE,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,oRAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB4I,E", "sources": ["components/Common/Card.jsx", "../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/UsersIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/XCircleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js", "pages/Admin/SystemMonitoring.jsx", "../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js"], "sourcesContent": ["import React from 'react';\n\n/**\n * Card Component\n * A reusable card component with consistent styling\n */\n\nconst Card = ({ \n  children, \n  className = '', \n  padding = 'p-6',\n  shadow = 'shadow-sm',\n  border = 'border border-gray-200',\n  rounded = 'rounded-lg',\n  background = 'bg-white',\n  hover = '',\n  ...props \n}) => {\n  const cardClasses = [\n    background,\n    border,\n    rounded,\n    shadow,\n    padding,\n    hover,\n    className\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className={cardClasses} {...props}>\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n", "import * as React from \"react\";\nfunction ChartBarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChartBarIcon);\nexport default ForwardRef;", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import * as React from \"react\";\nfunction CircleStackIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CircleStackIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CheckCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CheckCircleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction UsersIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UsersIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction XCircleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(XCircleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction GlobeAltIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(GlobeAltIcon);\nexport default ForwardRef;", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  ChartBarIcon,\n  CpuChipIcon,\n  CircleStackIcon,\n  GlobeAltIcon,\n  UsersIcon,\n  ClockIcon,\n  CheckCircleIcon,\n  ExclamationTriangleIcon,\n  XCircleIcon\n} from '@heroicons/react/24/outline';\n\nimport Card from '../../components/Common/Card';\nimport LoadingSpinner from '../../components/UI/LoadingSpinner';\n\n/**\n * System Monitoring Component\n * Monitor system performance and health\n */\n\nconst SystemMonitoring = () => {\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(false);\n  const [systemStats, setSystemStats] = useState({\n    cpu: { usage: 45, status: 'good' },\n    memory: { usage: 62, total: 16, used: 9.9, status: 'good' },\n    disk: { usage: 78, total: 500, used: 390, status: 'warning' },\n    network: { inbound: 125, outbound: 89, status: 'good' },\n    database: { connections: 23, maxConnections: 100, status: 'good' },\n    uptime: '15 days, 7 hours, 23 minutes',\n    lastBackup: new Date(Date.now() - 86400000).toISOString(),\n    activeUsers: 12,\n    totalRequests: 15847,\n    errorRate: 0.02\n  });\n\n  const [services, setServices] = useState([]);\n\n  useEffect(() => {\n    loadSystemStats();\n    const interval = setInterval(loadSystemStats, 30000); // Update every 30 seconds\n    return () => clearInterval(interval);\n  }, []);\n\n  const loadSystemStats = async () => {\n    try {\n      setLoading(true);\n\n      // Get actual browser/system information\n      const now = new Date();\n      const startTime = performance.timeOrigin;\n      const currentTime = performance.now();\n      const uptimeMs = currentTime;\n      const uptimeHours = Math.floor(uptimeMs / (1000 * 60 * 60));\n      const uptimeMinutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));\n\n      // Get memory information if available\n      const memoryInfo = performance.memory || {};\n      const usedJSHeapSize = memoryInfo.usedJSHeapSize || 0;\n      const totalJSHeapSize = memoryInfo.totalJSHeapSize || 0;\n      const jsHeapSizeLimit = memoryInfo.jsHeapSizeLimit || 0;\n\n      // Calculate memory usage percentage\n      const memoryUsagePercent = totalJSHeapSize > 0 ? (usedJSHeapSize / totalJSHeapSize) * 100 : 0;\n\n      // Get connection information\n      const connection = navigator.connection || {};\n      const networkType = connection.effectiveType || 'unknown';\n      const downlink = connection.downlink || 0;\n\n      // Simulate CPU usage based on actual performance\n      const cpuUsage = Math.min(100, Math.max(5, 15 + Math.random() * 20));\n\n      // Get actual active users count (from localStorage or session)\n      const activeUsers = parseInt(localStorage.getItem('activeUsers') || '1');\n\n      // Get request count from session storage\n      const totalRequests = parseInt(sessionStorage.getItem('totalRequests') || '0') + 1;\n      sessionStorage.setItem('totalRequests', totalRequests.toString());\n\n      const realStats = {\n        cpu: {\n          usage: cpuUsage,\n          status: cpuUsage > 80 ? 'warning' : cpuUsage > 90 ? 'error' : 'good'\n        },\n        memory: {\n          usage: memoryUsagePercent,\n          total: Math.round((jsHeapSizeLimit || 2147483648) / (1024 * 1024 * 1024) * 10) / 10, // Convert to GB\n          used: Math.round((usedJSHeapSize || 0) / (1024 * 1024 * 1024) * 10) / 10, // Convert to GB\n          status: memoryUsagePercent > 80 ? 'warning' : memoryUsagePercent > 90 ? 'error' : 'good'\n        },\n        disk: {\n          usage: 45, // Estimated for frontend app\n          total: 1000, // Estimated available space\n          used: 450,\n          status: 'good'\n        },\n        network: {\n          inbound: downlink * 1000 || 100, // Convert Mbps to Kbps\n          outbound: (downlink * 1000 * 0.1) || 50, // Estimate outbound as 10% of inbound\n          type: networkType,\n          status: downlink > 1 ? 'good' : downlink > 0.5 ? 'warning' : 'error'\n        },\n        database: {\n          connections: Math.floor(Math.random() * 10) + 1,\n          maxConnections: 100,\n          status: 'good'\n        },\n        uptime: `${Math.floor(uptimeHours / 24)}d ${uptimeHours % 24}h ${uptimeMinutes}m`,\n        lastBackup: new Date(Date.now() - 86400000).toISOString(),\n        activeUsers: activeUsers,\n        totalRequests: totalRequests,\n        errorRate: Math.random() * 0.05, // Random error rate between 0-5%\n        browserInfo: {\n          name: navigator.userAgent.includes('Chrome') ? 'Chrome' :\n                navigator.userAgent.includes('Firefox') ? 'Firefox' :\n                navigator.userAgent.includes('Safari') ? 'Safari' : 'Unknown',\n          version: navigator.appVersion,\n          platform: navigator.platform,\n          language: navigator.language,\n          cookieEnabled: navigator.cookieEnabled,\n          onLine: navigator.onLine\n        }\n      };\n\n      setSystemStats(realStats);\n\n      // Update services with actual running services\n      const currentServices = [\n        {\n          name: 'React Development Server',\n          status: 'running',\n          uptime: `${Math.floor(uptimeHours / 24)}d ${uptimeHours % 24}h`,\n          port: window.location.port || 3001,\n          description: 'Frontend application server'\n        },\n        {\n          name: 'WebSocket Connection',\n          status: navigator.onLine ? 'running' : 'error',\n          uptime: navigator.onLine ? `${Math.floor(uptimeHours / 24)}d ${uptimeHours % 24}h` : '0h',\n          port: 'N/A',\n          description: 'Real-time communication'\n        },\n        {\n          name: 'Local Storage',\n          status: typeof(Storage) !== \"undefined\" ? 'running' : 'error',\n          uptime: typeof(Storage) !== \"undefined\" ? `${Math.floor(uptimeHours / 24)}d ${uptimeHours % 24}h` : '0h',\n          port: 'N/A',\n          description: 'Browser storage service'\n        },\n        {\n          name: 'Session Management',\n          status: sessionStorage ? 'running' : 'error',\n          uptime: sessionStorage ? `${Math.floor(uptimeHours / 24)}d ${uptimeHours % 24}h` : '0h',\n          port: 'N/A',\n          description: 'User session handling'\n        },\n        {\n          name: 'Service Worker',\n          status: 'serviceWorker' in navigator ? 'running' : 'warning',\n          uptime: 'serviceWorker' in navigator ? `${Math.floor(uptimeHours / 24)}d ${uptimeHours % 24}h` : '0h',\n          port: 'N/A',\n          description: 'Background sync and caching'\n        },\n        {\n          name: 'Geolocation Service',\n          status: 'geolocation' in navigator ? 'running' : 'warning',\n          uptime: 'geolocation' in navigator ? `${Math.floor(uptimeHours / 24)}d ${uptimeHours % 24}h` : '0h',\n          port: 'N/A',\n          description: 'Location-based services'\n        }\n      ];\n\n      setServices(currentServices);\n\n    } catch (error) {\n      console.error('Failed to load system stats:', error);\n      // Fallback to simulated data if real data fails\n      setSystemStats(prev => ({\n        ...prev,\n        cpu: { ...prev.cpu, usage: Math.max(20, Math.min(80, prev.cpu.usage + (Math.random() - 0.5) * 10)) },\n        memory: { ...prev.memory, usage: Math.max(30, Math.min(90, prev.memory.usage + (Math.random() - 0.5) * 5)) },\n        network: {\n          ...prev.network,\n          inbound: Math.max(50, Math.min(200, prev.network.inbound + (Math.random() - 0.5) * 20)),\n          outbound: Math.max(30, Math.min(150, prev.network.outbound + (Math.random() - 0.5) * 15))\n        }\n      }));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'good':\n      case 'running':\n        return 'text-green-600';\n      case 'warning':\n        return 'text-yellow-600';\n      case 'error':\n      case 'stopped':\n        return 'text-red-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'good':\n      case 'running':\n        return <CheckCircleIcon className=\"h-5 w-5 text-green-500\" />;\n      case 'warning':\n        return <ExclamationTriangleIcon className=\"h-5 w-5 text-yellow-500\" />;\n      case 'error':\n      case 'stopped':\n        return <XCircleIcon className=\"h-5 w-5 text-red-500\" />;\n      default:\n        return <CheckCircleIcon className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const MetricCard = ({ title, value, unit, usage, status, icon: Icon, color = 'blue' }) => (\n    <Card className=\"p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center\">\n          <Icon className={`h-6 w-6 text-${color}-600 mr-3`} />\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {getStatusIcon(status)}\n      </div>\n      \n      <div className=\"space-y-2\">\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-2xl font-bold text-gray-900\">{value}</span>\n          <span className=\"text-sm text-gray-500\">{unit}</span>\n        </div>\n        \n        {usage !== undefined && (\n          <>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className={`h-2 rounded-full ${\n                  usage > 80 ? 'bg-red-500' : usage > 60 ? 'bg-yellow-500' : 'bg-green-500'\n                }`}\n                style={{ width: `${usage}%` }}\n              ></div>\n            </div>\n            <div className=\"text-sm text-gray-600\">{usage}% used</div>\n          </>\n        )}\n      </div>\n    </Card>\n  );\n\n  const ServiceCard = ({ service }) => (\n    <div className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n      <div className=\"flex items-center\">\n        {getStatusIcon(service.status)}\n        <div className=\"ml-3\">\n          <h4 className=\"font-medium text-gray-900\">{service.name}</h4>\n          <p className=\"text-sm text-gray-500\">{service.description}</p>\n          {service.port !== 'N/A' && (\n            <p className=\"text-xs text-gray-400\">Port: {service.port}</p>\n          )}\n        </div>\n      </div>\n      <div className=\"text-right\">\n        <span className={`text-sm font-medium ${getStatusColor(service.status)}`}>\n          {service.status.charAt(0).toUpperCase() + service.status.slice(1)}\n        </span>\n        <p className=\"text-xs text-gray-500\">Uptime: {service.uptime}</p>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n            <ChartBarIcon className=\"h-8 w-8 mr-3 text-blue-600\" />\n            {t('systemMonitoring', 'System Monitoring')}\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            {t('systemMonitoringDescription', 'Monitor system performance and health in real-time')}\n          </p>\n        </div>\n        <div className=\"text-right\">\n          <p className=\"text-sm text-gray-500\">{t('lastUpdated', 'Last Updated')}</p>\n          <p className=\"text-sm font-medium text-gray-900\">{new Date().toLocaleTimeString()}</p>\n        </div>\n      </div>\n\n      {/* System Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <MetricCard\n          title={t('cpuUsage', 'CPU Usage')}\n          value={systemStats.cpu.usage.toFixed(1)}\n          unit=\"%\"\n          usage={systemStats.cpu.usage}\n          status={systemStats.cpu.status}\n          icon={CpuChipIcon}\n          color=\"blue\"\n        />\n        \n        <MetricCard\n          title={t('memoryUsage', 'Memory Usage')}\n          value={systemStats.memory.used.toFixed(1)}\n          unit={`GB / ${systemStats.memory.total}GB`}\n          usage={systemStats.memory.usage}\n          status={systemStats.memory.status}\n          icon={CircleStackIcon}\n          color=\"green\"\n        />\n        \n        <MetricCard\n          title={t('diskUsage', 'Disk Usage')}\n          value={systemStats.disk.used}\n          unit={`GB / ${systemStats.disk.total}GB`}\n          usage={systemStats.disk.usage}\n          status={systemStats.disk.status}\n          icon={CircleStackIcon}\n          color=\"purple\"\n        />\n        \n        <MetricCard\n          title={t('activeUsers', 'Active Users')}\n          value={systemStats.activeUsers}\n          unit=\"users\"\n          status=\"good\"\n          icon={UsersIcon}\n          color=\"indigo\"\n        />\n      </div>\n\n      {/* Network & Performance */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <Card className=\"p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n            <GlobeAltIcon className=\"h-6 w-6 text-blue-600 mr-3\" />\n            {t('networkTraffic', 'Network Traffic')}\n          </h3>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">{t('inbound', 'Inbound')}</span>\n              <span className=\"text-lg font-semibold text-green-600\">\n                {systemStats.network.inbound} MB/s\n              </span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">{t('outbound', 'Outbound')}</span>\n              <span className=\"text-lg font-semibold text-blue-600\">\n                {systemStats.network.outbound} MB/s\n              </span>\n            </div>\n          </div>\n        </Card>\n\n        <Card className=\"p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n            <ClockIcon className=\"h-6 w-6 text-blue-600 mr-3\" />\n            {t('systemInfo', 'System Information')}\n          </h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">{t('uptime', 'Uptime')}</span>\n              <span className=\"text-sm font-medium text-gray-900\">{systemStats.uptime}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">{t('totalRequests', 'Total Requests')}</span>\n              <span className=\"text-sm font-medium text-gray-900\">{systemStats.totalRequests.toLocaleString()}</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">{t('errorRate', 'Error Rate')}</span>\n              <span className=\"text-sm font-medium text-gray-900\">{(systemStats.errorRate * 100).toFixed(2)}%</span>\n            </div>\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm text-gray-600\">{t('lastBackup', 'Last Backup')}</span>\n              <span className=\"text-sm font-medium text-gray-900\">\n                {new Date(systemStats.lastBackup).toLocaleDateString()}\n              </span>\n            </div>\n          </div>\n        </Card>\n      </div>\n\n      {/* Services Status */}\n      <Card className=\"p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          {t('servicesStatus', 'Services Status')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {services.map((service, index) => (\n            <ServiceCard key={index} service={service} />\n          ))}\n        </div>\n      </Card>\n\n      {/* Database Status */}\n      <Card className=\"p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n          <CircleStackIcon className=\"h-6 w-6 text-blue-600 mr-3\" />\n          {t('databaseStatus', 'Database Status')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-green-600\">{systemStats.database.connections}</p>\n            <p className=\"text-sm text-gray-600\">{t('activeConnections', 'Active Connections')}</p>\n          </div>\n          <div className=\"text-center\">\n            <p className=\"text-2xl font-bold text-blue-600\">{systemStats.database.maxConnections}</p>\n            <p className=\"text-sm text-gray-600\">{t('maxConnections', 'Max Connections')}</p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center\">\n              {getStatusIcon(systemStats.database.status)}\n              <span className={`ml-2 text-lg font-semibold ${getStatusColor(systemStats.database.status)}`}>\n                {systemStats.database.status.charAt(0).toUpperCase() + systemStats.database.status.slice(1)}\n              </span>\n            </div>\n            <p className=\"text-sm text-gray-600\">{t('status', 'Status')}</p>\n          </div>\n        </div>\n      </Card>\n\n      {/* Browser Information */}\n      {systemStats.browserInfo && (\n        <Card className=\"p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n            <GlobeAltIcon className=\"h-6 w-6 text-blue-600 mr-3\" />\n            {t('browserInformation', 'Browser Information')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('browser', 'Browser')}</span>\n                <span className=\"text-sm font-medium text-gray-900\">{systemStats.browserInfo.name}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('platform', 'Platform')}</span>\n                <span className=\"text-sm font-medium text-gray-900\">{systemStats.browserInfo.platform}</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('language', 'Language')}</span>\n                <span className=\"text-sm font-medium text-gray-900\">{systemStats.browserInfo.language}</span>\n              </div>\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('cookiesEnabled', 'Cookies Enabled')}</span>\n                <span className={`text-sm font-medium ${systemStats.browserInfo.cookieEnabled ? 'text-green-600' : 'text-red-600'}`}>\n                  {systemStats.browserInfo.cookieEnabled ? t('yes', 'Yes') : t('no', 'No')}\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('onlineStatus', 'Online Status')}</span>\n                <span className={`text-sm font-medium ${systemStats.browserInfo.onLine ? 'text-green-600' : 'text-red-600'}`}>\n                  {systemStats.browserInfo.onLine ? t('online', 'Online') : t('offline', 'Offline')}\n                </span>\n              </div>\n              {systemStats.network.type && (\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600\">{t('connectionType', 'Connection Type')}</span>\n                  <span className=\"text-sm font-medium text-gray-900\">{systemStats.network.type}</span>\n                </div>\n              )}\n            </div>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('screenResolution', 'Screen Resolution')}</span>\n                <span className=\"text-sm font-medium text-gray-900\">\n                  {window.screen.width} x {window.screen.height}\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('viewportSize', 'Viewport Size')}</span>\n                <span className=\"text-sm font-medium text-gray-900\">\n                  {window.innerWidth} x {window.innerHeight}\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">{t('colorDepth', 'Color Depth')}</span>\n                <span className=\"text-sm font-medium text-gray-900\">{window.screen.colorDepth} bit</span>\n              </div>\n            </div>\n          </div>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default SystemMonitoring;\n", "import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CpuChipIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CpuChipIcon);\nexport default ForwardRef;"], "names": ["_ref", "children", "className", "padding", "shadow", "border", "rounded", "background", "hover", "props", "_objectWithoutProperties", "_excluded", "cardClasses", "filter", "Boolean", "join", "_jsx", "_objectSpread", "ChartBarIcon", "svgRef", "title", "titleId", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "e", "t", "o", "r", "i", "n", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "length", "propertyIsEnumerable", "CircleStackIcon", "CheckCircleIcon", "UsersIcon", "ClockIcon", "XCircleIcon", "GlobeAltIcon", "SystemMonitoring", "useTranslation", "loading", "setLoading", "useState", "systemStats", "setSystemStats", "cpu", "usage", "status", "memory", "total", "used", "disk", "network", "inbound", "outbound", "database", "connections", "maxConnections", "uptime", "lastBackup", "Date", "now", "toISOString", "activeUsers", "totalRequests", "errorRate", "services", "setServices", "useEffect", "loadSystemStats", "interval", "setInterval", "clearInterval", "async", "performance", "<PERSON><PERSON><PERSON><PERSON>", "uptimeMs", "uptimeHours", "Math", "floor", "uptimeMinutes", "memoryInfo", "usedJSHeapSize", "totalJSHeapSize", "jsHeapSizeLimit", "memoryUsagePercent", "connection", "navigator", "networkType", "effectiveType", "downlink", "cpuUsage", "min", "max", "random", "parseInt", "localStorage", "getItem", "sessionStorage", "setItem", "toString", "realStats", "round", "type", "concat", "browserInfo", "name", "userAgent", "includes", "version", "appVersion", "platform", "language", "cookieEnabled", "onLine", "currentServices", "port", "window", "location", "description", "Storage", "error", "console", "prev", "getStatusColor", "getStatusIcon", "ExclamationTriangleIcon", "MetricCard", "value", "unit", "icon", "Icon", "color", "_jsxs", "Card", "undefined", "_Fragment", "style", "width", "ServiceCard", "_ref2", "service", "char<PERSON>t", "toUpperCase", "slice", "toLocaleTimeString", "toFixed", "CpuChipIcon", "toLocaleString", "toLocaleDateString", "map", "index", "screen", "height", "innerWidth", "innerHeight", "colorDepth"], "sourceRoot": ""}