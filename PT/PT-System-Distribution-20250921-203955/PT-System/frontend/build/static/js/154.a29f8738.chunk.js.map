{"version": 3, "file": "static/js/154.a29f8738.chunk.js", "mappings": "2MAKA,MA8lBA,EA9lBsBA,KACpB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,aAAEC,IAAiBC,EAAAA,EAAAA,KACnBC,GAAWC,EAAAA,EAAAA,OACVC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,KACtCC,EAAkBC,IAAuBF,EAAAA,EAAAA,UAAS,QAClDG,EAAkBC,IAAuBJ,EAAAA,EAAAA,UAAS,MAGnDK,EAAgB,CACpB,CACEC,GAAI,iBACJC,KAAM,sBACNC,OAAQ,iHACRC,SAAU,SACVC,YAAa,qDACbC,cAAe,yKACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,wBACPC,UAAW,oBACXC,WAAW,EACXC,QAAS,CACP,sBACA,kBACA,mBACA,wBACA,oBACA,kBAGJ,CACEb,GAAI,qBACJC,KAAM,8BACNC,OAAQ,uMACRC,SAAU,aACVC,YAAa,8EACbC,cAAe,iSACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,uBACPC,UAAW,mBACXC,WAAW,EACXC,QAAS,CACP,gCACA,6BACA,0BACA,0BACA,wBACA,yBACA,oBACA,8BAGJ,CACEb,GAAI,kBACJC,KAAM,wBACNC,OAAQ,+FACRC,SAAU,aACVC,YAAa,6CACbC,cAAe,wHACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,yBACPC,UAAW,qBACXC,WAAW,EACXC,QAAS,CACP,uBACA,wBACA,uBACA,oBACA,sBACA,uBAGJ,CACEb,GAAI,uBACJC,KAAM,kCACNC,OAAQ,sJACRC,SAAU,YACVC,YAAa,iEACbC,cAAe,mMACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,8BACPC,UAAW,sBACXC,WAAW,EACXC,QAAS,CACP,+BACA,sBACA,oBACA,sBACA,oBACA,4BACA,yBAGJ,CACEb,GAAI,iBACJC,KAAM,sBACNC,OAAQ,iHACRC,SAAU,WACVC,YAAa,oDACbC,cAAe,uOACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,wBACPC,UAAW,oBACXC,WAAW,EACXC,QAAS,CACP,sBACA,6BACA,qBACA,mBACA,iBACA,sBAGJ,CACEb,GAAI,eACJC,KAAM,uBACNC,OAAQ,2LACRC,SAAU,aACVC,YAAa,kDACbC,cAAe,4LACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,sBACPC,UAAW,mBACXC,WAAW,EACXC,QAAS,CACP,wBACA,mBACA,qBACA,qBACA,0BACA,kBAGJ,CACEb,GAAI,kBACJC,KAAM,wBACNC,OAAQ,+FACRC,SAAU,aACVC,YAAa,6CACbC,cAAe,mKACfC,OAAQ,GACRC,cAAe,eACfC,SAAU,YACVC,cAAc,EACdI,QAAS,CACP,gBACA,iBACA,eACA,sBACA,oBACA,uBAGJ,CACEb,GAAI,sBACJC,KAAM,2BACNC,OAAQ,iHACRC,SAAU,eACVC,YAAa,gDACbC,cAAe,4JACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdI,QAAS,CACP,yBACA,qBACA,qBACA,sBACA,sBACA,eAGJ,CACEb,GAAI,uBACJC,KAAM,iCACNC,OAAQ,6HACRC,SAAU,aACVC,YAAa,uDACbC,cAAe,+NACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,8BACPC,UAAW,0BACXE,QAAS,CACP,oBACA,sBACA,oBACA,4BACA,iBACA,oBAGJ,CACEb,GAAI,wBACJC,KAAM,8BACNC,OAAQ,yIACRC,SAAU,YACVC,YAAa,qDACbC,cAAe,8MACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,+BACPC,UAAW,0BACXC,WAAW,EACXC,QAAS,CACP,qBACA,uBACA,sBACA,qBACA,oBACA,4BAGJ,CACEb,GAAI,iBACJC,KAAM,yBACNC,OAAQ,2GACRC,SAAU,YACVC,YAAa,oDACbC,cAAe,wJACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,wBACPC,UAAW,oBACXC,WAAW,EACXC,QAAS,CACP,uBACA,cACA,0BACA,wBACA,mBACA,oBAGJ,CACEb,GAAI,uBACJC,KAAM,wCACNC,OAAQ,2LACRC,SAAU,YACVC,YAAa,2FACbC,cAAe,0UACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,8BACPC,UAAW,wBACXC,WAAW,EACXC,QAAS,CACP,kCACA,iCACA,0BACA,+BACA,mCACA,+BAGJ,CACEb,GAAI,2BACJC,KAAM,oCACNC,OAAQ,gJACRC,SAAU,YACVC,YAAa,4FACbC,cAAe,+UACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,kCACPC,UAAW,6BACXC,WAAW,EACXC,QAAS,CACP,4BACA,kCACA,kCACA,mCACA,2BACA,oCAGJ,CACEb,GAAI,iBACJC,KAAM,4BACNC,OAAQ,2GACRC,SAAU,YACVC,YAAa,gFACbC,cAAe,+WACfC,OAAQ,GACRC,cAAe,gBACfC,SAAU,YACVC,cAAc,EACdC,MAAO,wBACPC,UAAW,mBACXC,WAAW,EACXC,QAAS,CACP,sBACA,yCACA,+BACA,sBACA,8BACA,wCAKAC,EAAa,CACjB,CAAEd,GAAI,MAAOe,MAAO9B,EAAE,gBAAiB,kBAAmB+B,MAAOjB,EAAckB,QAC/E,CAAEjB,GAAI,SAAUe,MAAO9B,EAAE,SAAU,UAAW+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,WAAfA,EAAEkB,UAAuBc,QACxG,CAAEjB,GAAI,aAAce,MAAO9B,EAAE,aAAc,cAAe+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,eAAfA,EAAEkB,UAA2Bc,QACxH,CAAEjB,GAAI,gBAAiBe,MAAO9B,EAAE,eAAgB,iBAAkB+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,kBAAfA,EAAEkB,UAA8Bc,QACnI,CAAEjB,GAAI,WAAYe,MAAO9B,EAAE,WAAY,YAAa+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,aAAfA,EAAEkB,UAAyBc,QAChH,CAAEjB,GAAI,YAAae,MAAO9B,EAAE,YAAa,aAAc+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,cAAfA,EAAEkB,UAA0Bc,QACpH,CAAEjB,GAAI,eAAgBe,MAAO9B,EAAE,eAAgB,gBAAiB+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,iBAAfA,EAAEkB,UAA6Bc,QAChI,CAAEjB,GAAI,YAAae,MAAO9B,EAAE,YAAa,aAAc+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,cAAfA,EAAEkB,UAA0Bc,QACpH,CAAEjB,GAAI,YAAae,MAAO9B,EAAE,YAAa,aAAc+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,cAAfA,EAAEkB,UAA0Bc,QACpH,CAAEjB,GAAI,iBAAkBe,MAAO9B,EAAE,iBAAkB,kBAAmB+B,MAAOjB,EAAcmB,OAAOjC,GAAoB,mBAAfA,EAAEkB,UAA+Bc,SAGpIE,EAAoBpB,EAAcmB,OAAOE,IAC7C,MAAMC,EAAgBD,EAASnB,KAAKqB,cAAcC,SAAS/B,EAAW8B,gBACjDF,EAASlB,OAAOqB,SAAS/B,IACzB4B,EAAShB,YAAYkB,cAAcC,SAAS/B,EAAW8B,eACtEE,EAAuC,QAArB7B,GAA8ByB,EAASjB,WAAaR,EAC5E,OAAO0B,GAAiBG,IAGpBC,EAAqBL,IAErBA,EAASV,OAASU,EAASR,UAC7BtB,EAAS8B,EAASV,QAGlBgB,MAAMzC,EAAE,mBAAmB,GAAD0C,OAAKP,EAASnB,KAAI,iFAC5Cb,EAAawC,eAQjB,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAH,OAASzC,EAAQ,cAAgB,gBAAiB6C,SAAA,EAE9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D9C,EAAE,gBAAiB,qBAEtB+C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5C9C,EAAE,oBAAqB,mEAG5B4C,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAM7C,EAAawC,aAC5BE,UAAU,oGAAmGC,SAAA,EAE7GC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ7C,EAAE,mBAAoB,6BAK3B+C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mGAAkGC,UAC/GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9C,EAAE,kBAAmB,uBAExB4C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEE,KAAK,OACLC,MAAO3C,EACP4C,SAAWC,GAAM5C,EAAc4C,EAAEC,OAAOH,OACxCI,YAAatD,EAAE,oBAAqB,+CACpC6C,UAAU,2IAEZE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DAIjBD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9C,EAAE,WAAY,eAEjB+C,EAAAA,EAAAA,KAAA,UACEG,MAAOxC,EACPyC,SAAWC,GAAMzC,EAAoByC,EAAEC,OAAOH,OAC9CL,UAAU,kIAAiIC,SAE1IjB,EAAW0B,IAAIrC,IACd0B,EAAAA,EAAAA,MAAA,UAA0BM,MAAOhC,EAASH,GAAG+B,SAAA,CAC1C5B,EAASY,MAAM,KAAGZ,EAASa,MAAM,MADvBb,EAASH,WAO5BgC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uJAAsJC,SAAA,EACtKC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBACZ7C,EAAE,kBAAmB,+BAO9B+C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEZ,EAAkBqB,IAAIpB,IACrBS,EAAAA,EAAAA,MAAA,OAAuBC,UAAU,4IAA2IC,SAAA,EAE1KF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE7C,EAAQkC,EAASlB,OAASkB,EAASnB,QAEtC4B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,CACrCX,EAASR,WACRiB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,sGAAqGC,SAAA,EACnHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ7C,EAAE,YAAa,iBAGlB4C,EAAAA,EAAAA,MAAA,QAAMC,UAAU,0GAAyGC,SAAA,EACvHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBACZ7C,EAAE,gBAAiB,qBAGvBmC,EAASX,eACRoB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,0GAAyGC,SAAA,EACvHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZ7C,EAAE,eAAgB,6BAO7B+C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzD7C,EAAQkC,EAASf,cAAgBe,EAAShB,eAG7CyB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6EAA4EC,SAAA,EACzFF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZV,EAASd,OAAO,IAAErB,EAAE,SAAU,cAEjC4C,EAAAA,EAAAA,MAAA,QAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZV,EAASb,wBAMhBsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DC,SAAA,CACtE9C,EAAE,WAAY,YAAY,QAE7B4C,EAAAA,EAAAA,MAAA,MAAIC,UAAU,YAAWC,SAAA,CACtBX,EAASP,QAAQ4B,MAAM,EAAG,GAAGD,IAAI,CAACE,EAAMC,KACvCd,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,6DAA4DC,SAAA,EACpFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CACZY,IAFMC,IAKVvB,EAASP,QAAQI,OAAS,IACzBY,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2CAA0CC,SAAA,CAAC,IACrDX,EAASP,QAAQI,OAAS,EAAE,IAAEhC,EAAE,YAAa,wBAOvD4C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qBAAoBC,SAAA,EACjCC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMR,EAAkBL,GACjCU,UAAS,yDAAAH,OACPP,EAASR,UACL,2CACA,4CACHmB,SAEFX,EAASR,UAAY3B,EAAE,cAAe,gBAAkBA,EAAE,eAAgB,oBAE7E+C,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAxJEb,KACvBtB,EAAoBsB,IAuJOwB,CAAgBxB,GAC/BU,UAAU,wJAAuJC,UAEjKC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBAlFTV,EAASpB,OAyFO,IAA7BmB,EAAkBF,SACjBY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnE9C,EAAE,mBAAoB,yBAEzB+C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD9C,EAAE,qBAAsB,2CAE3B+C,EAAAA,EAAAA,KAAA,UACEC,QAASA,KACPxC,EAAc,IACdG,EAAoB,QAEtBkC,UAAU,kFAAiFC,SAE1F9C,EAAE,eAAgB,sBAMxBY,IACCmC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iFAAgFC,UAC7FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+FAA8FC,SAAA,EAC3GC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE7C,EAAQW,EAAiBK,OAASL,EAAiBI,QAEtD+B,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMnC,EAAoB,MACnCgC,UAAU,6DAA4DC,UAEtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iCAKnBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD7C,EAAQW,EAAiBQ,cAAgBR,EAAiBO,eAG7DyB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,CACnE9C,EAAE,eAAgB,iBAAiB,QAGtC+C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6CAA4CC,SACxDlC,EAAiBgB,QAAQ2B,IAAI,CAACK,EAASF,KACtCd,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,+DAA8DC,SAAA,EACvFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BC,SAAEc,MAFzCF,OAOdd,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEC,QAASA,KACPR,EAAkB5B,GAClBC,EAAoB,OAEtBgC,UAAU,yFAAwFC,SAEjG9C,EAAE,kBAAmB,wBAExB+C,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMnC,EAAoB,MACnCgC,UAAU,gJAA+IC,SAExJ9C,EAAE,QAAS,yB", "sources": ["pages/Forms/FormTemplates.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useNavigation } from '../../contexts/NavigationContext';\nimport { useNavigate } from 'react-router-dom';\n\nconst FormTemplates = () => {\n  const { t, isRTL } = useLanguage();\n  const { quickActions } = useNavigation();\n  const navigate = useNavigate();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n\n  // Available form templates with working components only\n  const formTemplates = [\n    {\n      id: 'patient-intake',\n      name: 'Patient Intake Form',\n      nameAr: 'نموذج استقبال المريض',\n      category: 'intake',\n      description: 'Comprehensive patient intake form for new patients',\n      descriptionAr: 'نموذج استقبال شامل للمرضى الجدد',\n      fields: 15,\n      estimatedTime: '10-15 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/patient-intake',\n      component: 'PatientIntakeForm',\n      available: true,\n      preview: [\n        'Patient Information',\n        'Medical History',\n        'Current Symptoms',\n        'Insurance Information',\n        'Emergency Contact',\n        'Consent Forms'\n      ]\n    },\n    {\n      id: 'initial-assessment',\n      name: 'PT Adult Initial Assessment',\n      nameAr: 'تقييم العلاج الطبيعي الأولي للبالغين',\n      category: 'assessment',\n      description: '8-page comprehensive initial assessment for adult physical therapy patients',\n      descriptionAr: 'تقييم أولي شامل من 8 صفحات لمرضى العلاج الطبيعي البالغين',\n      fields: 50,\n      estimatedTime: '30-45 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/pt-assessment',\n      component: 'PTAssessmentPage',\n      available: true,\n      preview: [\n        'Patient Information & History',\n        'Pain Assessment & Body Map',\n        'Range of Motion Testing',\n        'Muscle Strength Testing',\n        'Functional Assessment',\n        'Treatment Goals & Plan',\n        'ICF Documentation',\n        'Equipment Recommendations'\n      ]\n    },\n    {\n      id: 'pain-assessment',\n      name: 'Pain Assessment Scale',\n      nameAr: 'مقياس تقييم الألم',\n      category: 'assessment',\n      description: 'Comprehensive pain evaluation and tracking',\n      descriptionAr: 'تقييم وتتبع شامل للألم',\n      fields: 12,\n      estimatedTime: '10-15 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/pain-assessment',\n      component: 'PainAssessmentForm',\n      available: true,\n      preview: [\n        'Pain Intensity Scale',\n        'Pain Location Mapping',\n        'Pain Characteristics',\n        'Functional Impact',\n        'Aggravating Factors',\n        'Treatment Response'\n      ]\n    },\n    {\n      id: 'discharge-assessment',\n      name: 'Outpatient Discharge Assessment',\n      nameAr: 'تقييم خروج المرضى الخارجيين',\n      category: 'discharge',\n      description: '2-page comprehensive discharge assessment with recommendations',\n      descriptionAr: 'تقييم خروج شامل من صفحتين مع التوصيات',\n      fields: 35,\n      estimatedTime: '20-30 minutes',\n      language: 'bilingual',\n      specialNeeds: false,\n      route: '/forms/discharge-assessment',\n      component: 'DischargeAssessment',\n      available: true,\n      preview: [\n        'Patient Information & Visits',\n        'Evaluation Addendum',\n        'Treatment Summary',\n        'Progress Assessment',\n        'Discharge Reasons',\n        'Follow-up Recommendations',\n        'Therapist Signatures'\n      ]\n    },\n    {\n      id: 'daily-progress',\n      name: 'Daily Progress Note',\n      nameAr: 'ملاحظة التقدم اليومي',\n      category: 'progress',\n      description: 'Daily session progress notes with pain assessment',\n      descriptionAr: 'ملاحظات التقدم اليومي للجلسة مع تقييم الألم',\n      fields: 20,\n      estimatedTime: '10-15 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/daily-progress',\n      component: 'DailyProgressPage',\n      available: true,\n      preview: [\n        'Session Information',\n        'Pain Assessment & Body Map',\n        'Treatment Provided',\n        'Patient Response',\n        'Progress Notes',\n        'Next Session Plan'\n      ]\n    },\n    {\n      id: 'reassessment',\n      name: 'PT Reassessment Form',\n      nameAr: 'نموذج إعادة التقييم للعلاج الطبيعي',\n      category: 'assessment',\n      description: 'Periodic reassessment to track patient progress',\n      descriptionAr: 'إعادة تقييم دورية لتتبع تقدم المريض',\n      fields: 30,\n      estimatedTime: '20-25 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/reassessment',\n      component: 'ReassessmentPage',\n      available: true,\n      preview: [\n        'Current Status Review',\n        'Goal Achievement',\n        'Functional Changes',\n        'Pain Level Changes',\n        'Treatment Modifications',\n        'Updated Goals'\n      ]\n    },\n    {\n      id: 'pain-assessment',\n      name: 'Pain Assessment Scale',\n      nameAr: 'مقياس تقييم الألم',\n      category: 'assessment',\n      description: 'Detailed pain assessment and tracking form',\n      descriptionAr: 'نموذج تقييم وتتبع الألم المفصل',\n      fields: 10,\n      estimatedTime: '5-10 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      preview: [\n        'Pain Location',\n        'Pain Intensity',\n        'Pain Quality',\n        'Aggravating Factors',\n        'Relieving Factors',\n        'Impact on Function'\n      ]\n    },\n    {\n      id: 'family-consultation',\n      name: 'Family Consultation Form',\n      nameAr: 'نموذج استشارة الأسرة',\n      category: 'consultation',\n      description: 'Family meeting and consultation documentation',\n      descriptionAr: 'توثيق اجتماع واستشارة الأسرة',\n      fields: 14,\n      estimatedTime: '10-15 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      preview: [\n        'Family Members Present',\n        'Concerns Discussed',\n        'Treatment Progress',\n        'Home Program Review',\n        'Questions & Answers',\n        'Next Steps'\n      ]\n    },\n    {\n      id: 'equipment-assessment',\n      name: 'Assistive Equipment Assessment',\n      nameAr: 'تقييم المعدات المساعدة',\n      category: 'assessment',\n      description: 'Assessment for assistive devices and equipment needs',\n      descriptionAr: 'تقييم احتياجات الأجهزة والمعدات المساعدة',\n      fields: 16,\n      estimatedTime: '15-20 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/equipment-assessment',\n      component: 'EquipmentAssessmentForm',\n      preview: [\n        'Current Equipment',\n        'Mobility Assessment',\n        'Safety Evaluation',\n        'Equipment Recommendations',\n        'Training Needs',\n        'Funding Options'\n      ]\n    },\n    {\n      id: 'home-exercise-program',\n      name: 'Home Exercise Program (HEP)',\n      nameAr: 'برنامج التمارين المنزلية',\n      category: 'treatment',\n      description: 'Customized home exercise program with instructions',\n      descriptionAr: 'برنامج تمارين منزلية مخصص مع التعليمات',\n      fields: 20,\n      estimatedTime: '15-25 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/home-exercise-program',\n      component: 'HomeExerciseProgramForm',\n      available: true,\n      preview: [\n        'Exercise Selection',\n        'Frequency & Duration',\n        'Visual Instructions',\n        'Safety Precautions',\n        'Progress Tracking',\n        'Modification Guidelines'\n      ]\n    },\n    {\n      id: 'treatment-plan',\n      name: 'Treatment Plan & Goals',\n      nameAr: 'خطة العلاج والأهداف',\n      category: 'treatment',\n      description: 'Comprehensive treatment planning with SMART goals',\n      descriptionAr: 'تخطيط علاج شامل مع أهداف ذكية',\n      fields: 25,\n      estimatedTime: '20-30 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/treatment-plan',\n      component: 'TreatmentPlanForm',\n      available: true,\n      preview: [\n        'Treatment Objectives',\n        'SMART Goals',\n        'Intervention Strategies',\n        'Timeline & Milestones',\n        'Outcome Measures',\n        'Review Schedule'\n      ]\n    },\n    {\n      id: 'initial-plan-of-care',\n      name: 'Initial Plan of Care Physical Therapy',\n      nameAr: 'خطة الرعاية الأولية للعلاج الطبيعي',\n      category: 'treatment',\n      description: '2-page comprehensive treatment planning document with problems, goals, and interventions',\n      descriptionAr: 'وثيقة تخطيط علاج شاملة من صفحتين مع المشاكل والأهداف والتدخلات',\n      fields: 40,\n      estimatedTime: '25-35 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/initial-plan-of-care',\n      component: 'InitialPlanOfCareForm',\n      available: true,\n      preview: [\n        'Patient Information & Diagnosis',\n        'Functional Problems Assessment',\n        'Short & Long Term Goals',\n        'Comprehensive Treatment Plan',\n        'Therapist & Physician Signatures',\n        'Professional Documentation'\n      ]\n    },\n    {\n      id: 'patient-family-education',\n      name: 'Patient and Family Education Form',\n      nameAr: 'نموذج تعليم المريض والأسرة',\n      category: 'education',\n      description: 'Comprehensive patient and family education assessment and planning for physical therapist',\n      descriptionAr: 'تقييم وتخطيط شامل لتعليم المريض والأسرة لأخصائي العلاج الطبيعي',\n      fields: 35,\n      estimatedTime: '20-30 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/patient-family-education',\n      component: 'PatientFamilyEducationForm',\n      available: true,\n      preview: [\n        'Assessment Type Selection',\n        'Learning Preferences & Barriers',\n        'Self-Care Capability Assessment',\n        'Educational Needs Identification',\n        'Teaching Methods & Tools',\n        'Evaluation & Follow-up Planning'\n      ]\n    },\n    {\n      id: 'follow-up-plan',\n      name: 'Follow Up Plan for Doctor',\n      nameAr: 'خطة المتابعة للطبيب',\n      category: 'treatment',\n      description: '2-page follow-up planning document with repeatable entries for medical review',\n      descriptionAr: 'وثيقة تخطيط متابعة من صفحتين مع إدخالات قابلة للتكرار للمراجعة الطبية',\n      fields: 25,\n      estimatedTime: '15-25 minutes',\n      language: 'bilingual',\n      specialNeeds: true,\n      route: '/forms/follow-up-plan',\n      component: 'FollowUpPlanForm',\n      available: true,\n      preview: [\n        'Patient Information',\n        'Repeatable Follow-up Entries (up to 4)',\n        'Current Situation Assessment',\n        'Treatment Proposals',\n        'Next Appointment Scheduling',\n        'Professional Medical Documentation'\n      ]\n    }\n  ];\n\n  const categories = [\n    { id: 'all', label: t('allCategories', 'All Categories'), count: formTemplates.length },\n    { id: 'intake', label: t('intake', 'Intake'), count: formTemplates.filter(t => t.category === 'intake').length },\n    { id: 'assessment', label: t('assessment', 'Assessment'), count: formTemplates.filter(t => t.category === 'assessment').length },\n    { id: 'special-needs', label: t('specialNeeds', 'Special Needs'), count: formTemplates.filter(t => t.category === 'special-needs').length },\n    { id: 'progress', label: t('progress', 'Progress'), count: formTemplates.filter(t => t.category === 'progress').length },\n    { id: 'discharge', label: t('discharge', 'Discharge'), count: formTemplates.filter(t => t.category === 'discharge').length },\n    { id: 'consultation', label: t('consultation', 'Consultation'), count: formTemplates.filter(t => t.category === 'consultation').length },\n    { id: 'treatment', label: t('treatment', 'Treatment'), count: formTemplates.filter(t => t.category === 'treatment').length },\n    { id: 'education', label: t('education', 'Education'), count: formTemplates.filter(t => t.category === 'education').length },\n    { id: 'administrative', label: t('administrative', 'Administrative'), count: formTemplates.filter(t => t.category === 'administrative').length }\n  ];\n\n  const filteredTemplates = formTemplates.filter(template => {\n    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         template.nameAr.includes(searchTerm) ||\n                         template.description.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const handleUseTemplate = (template) => {\n    // Navigate to the specific form route if available\n    if (template.route && template.available) {\n      navigate(template.route);\n    } else {\n      // Show message for unavailable forms\n      alert(t('formNotAvailable', `${template.name} is not yet available. Please use the form builder to create a custom form.`));\n      quickActions.createForm();\n    }\n  };\n\n  const previewTemplate = (template) => {\n    setSelectedTemplate(template);\n  };\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('formTemplates', 'Form Templates')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('formTemplatesDesc', 'Choose from pre-built healthcare forms or create your own')}\n          </p>\n        </div>\n        <button\n          onClick={() => quickActions.createForm()}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center\"\n        >\n          <i className=\"fas fa-plus mr-2\"></i>\n          {t('createCustomForm', 'Create Custom Form')}\n        </button>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-4\">\n          <div className=\"lg:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('searchTemplates', 'Search Templates')}\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                placeholder={t('searchPlaceholder', 'Search by name, description, or category...')}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n              <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n            </div>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('category', 'Category')}\n            </label>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {category.label} ({category.count})\n                </option>\n              ))}\n            </select>\n          </div>\n          \n          <div className=\"flex items-end\">\n            <button className=\"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n              <i className=\"fas fa-filter mr-2\"></i>\n              {t('advancedFilters', 'Advanced Filters')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Templates Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {filteredTemplates.map(template => (\n          <div key={template.id} className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 overflow-hidden hover:shadow-lg transition-shadow\">\n            {/* Template Header */}\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-start justify-between mb-3\">\n                <div className=\"flex items-start justify-between\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {isRTL ? template.nameAr : template.name}\n                  </h3>\n                  <div className=\"flex flex-col space-y-1\">\n                    {template.available ? (\n                      <span className=\"px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-xs rounded-full\">\n                        <i className=\"fas fa-check mr-1\"></i>\n                        {t('available', 'Available')}\n                      </span>\n                    ) : (\n                      <span className=\"px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 text-xs rounded-full\">\n                        <i className=\"fas fa-wrench mr-1\"></i>\n                        {t('inDevelopment', 'In Development')}\n                      </span>\n                    )}\n                    {template.specialNeeds && (\n                      <span className=\"px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 text-xs rounded-full\">\n                        <i className=\"fas fa-puzzle-piece mr-1\"></i>\n                        {t('specialNeeds', 'Special Needs')}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              </div>\n              \n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                {isRTL ? template.descriptionAr : template.description}\n              </p>\n              \n              <div className=\"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400\">\n                <span>\n                  <i className=\"fas fa-list mr-1\"></i>\n                  {template.fields} {t('fields', 'fields')}\n                </span>\n                <span>\n                  <i className=\"fas fa-clock mr-1\"></i>\n                  {template.estimatedTime}\n                </span>\n              </div>\n            </div>\n\n            {/* Template Preview */}\n            <div className=\"p-4 bg-gray-50 dark:bg-gray-700\">\n              <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('includes', 'Includes')}:\n              </h4>\n              <ul className=\"space-y-1\">\n                {template.preview.slice(0, 3).map((item, index) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-center\">\n                    <i className=\"fas fa-check text-green-500 mr-2 text-xs\"></i>\n                    {item}\n                  </li>\n                ))}\n                {template.preview.length > 3 && (\n                  <li className=\"text-xs text-gray-500 dark:text-gray-500\">\n                    +{template.preview.length - 3} {t('moreItems', 'more items')}\n                  </li>\n                )}\n              </ul>\n            </div>\n\n            {/* Template Actions */}\n            <div className=\"p-4 flex space-x-2\">\n              <button\n                onClick={() => handleUseTemplate(template)}\n                className={`flex-1 px-4 py-2 rounded-lg transition-colors text-sm ${\n                  template.available\n                    ? 'bg-blue-600 text-white hover:bg-blue-700'\n                    : 'bg-gray-400 text-white hover:bg-gray-500'\n                }`}\n              >\n                {template.available ? t('useTemplate', 'Use Template') : t('createCustom', 'Create Custom')}\n              </button>\n              <button\n                onClick={() => previewTemplate(template)}\n                className=\"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm\"\n              >\n                <i className=\"fas fa-eye\"></i>\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {filteredTemplates.length === 0 && (\n        <div className=\"text-center py-12\">\n          <i className=\"fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n            {t('noTemplatesFound', 'No templates found')}\n          </h3>\n          <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n            {t('tryDifferentSearch', 'Try adjusting your search or filters')}\n          </p>\n          <button\n            onClick={() => {\n              setSearchTerm('');\n              setSelectedCategory('all');\n            }}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            {t('clearFilters', 'Clear Filters')}\n          </button>\n        </div>\n      )}\n\n      {/* Template Preview Modal */}\n      {selectedTemplate && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                  {isRTL ? selectedTemplate.nameAr : selectedTemplate.name}\n                </h3>\n                <button\n                  onClick={() => setSelectedTemplate(null)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-times text-xl\"></i>\n                </button>\n              </div>\n            </div>\n            \n            <div className=\"p-6\">\n              <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n                {isRTL ? selectedTemplate.descriptionAr : selectedTemplate.description}\n              </p>\n              \n              <h4 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                {t('formSections', 'Form Sections')}:\n              </h4>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n                {selectedTemplate.preview.map((section, index) => (\n                  <div key={index} className=\"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <i className=\"fas fa-check-circle text-green-500 mr-3\"></i>\n                    <span className=\"text-gray-900 dark:text-white\">{section}</span>\n                  </div>\n                ))}\n              </div>\n              \n              <div className=\"flex space-x-4\">\n                <button\n                  onClick={() => {\n                    handleUseTemplate(selectedTemplate);\n                    setSelectedTemplate(null);\n                  }}\n                  className=\"flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  {t('useThisTemplate', 'Use This Template')}\n                </button>\n                <button\n                  onClick={() => setSelectedTemplate(null)}\n                  className=\"px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  {t('close', 'Close')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FormTemplates;\n"], "names": ["FormTemplates", "t", "isRTL", "useLanguage", "quickActions", "useNavigation", "navigate", "useNavigate", "searchTerm", "setSearchTerm", "useState", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedTemplate", "setSelectedTemplate", "formTemplates", "id", "name", "nameAr", "category", "description", "descriptionAr", "fields", "estimatedTime", "language", "specialNeeds", "route", "component", "available", "preview", "categories", "label", "count", "length", "filter", "filteredTemplates", "template", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "handleUseTemplate", "alert", "concat", "createForm", "_jsxs", "className", "children", "_jsx", "onClick", "type", "value", "onChange", "e", "target", "placeholder", "map", "slice", "item", "index", "previewTemplate", "section"], "sourceRoot": ""}