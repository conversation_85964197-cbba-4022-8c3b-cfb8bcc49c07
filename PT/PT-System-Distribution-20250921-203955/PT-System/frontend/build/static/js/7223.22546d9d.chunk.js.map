{"version": 3, "file": "static/js/7223.22546d9d.chunk.js", "mappings": "uNAKA,MAuYA,EAvY0BA,KACxB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MAEVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAgBC,IAAqBF,EAAAA,EAAAA,UAAS,CACnDG,KAAM,CAAC,EACPC,MAAO,CAAC,EACRC,MAAO,CAAC,EACRC,OAAQ,CAAC,KAEJC,EAAoBC,IAAyBR,EAAAA,EAAAA,UAAS,SACtDS,EAAgBC,IAAqBV,EAAAA,EAAAA,UAAS,UAErDW,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACL,EAAoBE,IAExB,MAAMG,EAAqBC,UACzB,IACEd,GAAW,GAEX,MAAMe,QAAiBC,MAAM,qCAADC,OAAsCT,EAAkB,YAAAS,OAAWP,GAAkB,CAC/GQ,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9BpB,EAAkBqB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBD,GAAI,IACP,CAAChB,GAAqBc,EAAOI,MAAQ,CAAC,IAE1C,KAAO,CAEL,MAAMC,EAAW,CACfvB,KAAM,CACJwB,aAAc,KACdC,UAAW,aACXC,UAAW,aACXC,UAAW,CACT,WAAc,CAAEC,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GACxD,kBAAmB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAC7D,qBAAsB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAChE,sBAAuB,CAAEF,MAAO,GAAIC,OAAQ,kBAAmBC,OAAQ,GACvE,kBAAmB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAC7D,2BAA4B,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,IAExEC,cAAe,CACb,CAAEC,KAAM,aAAcC,OAAQ,yCAA0CJ,OAAQ,aAChF,CAAEG,KAAM,aAAcC,OAAQ,kCAAmCJ,OAAQ,aACzE,CAAEG,KAAM,aAAcC,OAAQ,uBAAwBJ,OAAQ,iBAGlE5B,MAAO,CACLuB,aAAc,KACdC,UAAW,aACXC,UAAW,aACXC,UAAW,CACT,iBAAkB,CAAEC,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAC5D,qBAAsB,CAAEF,MAAO,GAAIC,OAAQ,kBAAmBC,OAAQ,GACtE,oBAAqB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAC/D,wBAAyB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GACnE,sBAAuB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GACjE,kBAAmB,CAAEF,MAAO,GAAIC,OAAQ,kBAAmBC,OAAQ,IAErEC,cAAe,CACb,CAAEC,KAAM,aAAcC,OAAQ,0BAA2BJ,OAAQ,aACjE,CAAEG,KAAM,aAAcC,OAAQ,yBAA0BJ,OAAQ,aAChE,CAAEG,KAAM,aAAcC,OAAQ,8BAA+BJ,OAAQ,iBAGzE3B,MAAO,CACLsB,aAAc,KACdC,UAAW,aACXC,UAAW,aACXC,UAAW,CACT,4BAA6B,CAAEC,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GACvE,sBAAuB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GACjE,uBAAwB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAClE,eAAgB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAC1D,gBAAiB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAC3D,sBAAuB,CAAEF,MAAO,IAAKC,OAAQ,YAAaC,OAAQ,IAEpEC,cAAe,CACb,CAAEC,KAAM,aAAcC,OAAQ,2BAA4BJ,OAAQ,aAClE,CAAEG,KAAM,aAAcC,OAAQ,0BAA2BJ,OAAQ,aACjE,CAAEG,KAAM,aAAcC,OAAQ,wBAAyBJ,OAAQ,eAGnE1B,OAAQ,CACNqB,aAAc,KACdC,UAAW,aACXC,UAAW,aACXC,UAAW,CACT,mBAAoB,CAAEC,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAC9D,oBAAqB,CAAEF,MAAO,GAAIC,OAAQ,kBAAmBC,OAAQ,GACrE,sBAAuB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GACjE,2BAA4B,CAAEF,MAAO,GAAIC,OAAQ,kBAAmBC,OAAQ,GAC5E,qBAAsB,CAAEF,MAAO,GAAIC,OAAQ,YAAaC,OAAQ,GAChE,sBAAuB,CAAEF,MAAO,GAAIC,OAAQ,kBAAmBC,OAAQ,IAEzEC,cAAe,CACb,CAAEC,KAAM,aAAcC,OAAQ,iCAAkCJ,OAAQ,eACxE,CAAEG,KAAM,aAAcC,OAAQ,sBAAuBJ,OAAQ,aAC7D,CAAEG,KAAM,aAAcC,OAAQ,0BAA2BJ,OAAQ,gBAKvE9B,EAAkBqB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBD,GAAI,IACP,CAAChB,GAAqBmB,EAASnB,IAAuB,CAAC,IAE3D,CACF,CAAE,MAAO8B,GACPC,QAAQD,MAAM,iCAAkCA,GAChDE,EAAAA,GAAMF,MAAM5C,EAAE,yBAA0B,iCAC1C,CAAC,QACCM,GAAW,EACb,GAGIyC,EAAyB3B,UAC7B,IACEd,GAAW,GAEX,MAAMe,QAAiBC,MAAM,mDAADC,OAAoDyB,EAAM,gBAAAzB,OAAeT,GAAsB,CACzHU,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,aAIpD,IAAIL,EAASM,GAaX,MAAM,IAAIsB,MAAM,iBAbD,CACf,MAAMC,QAAa7B,EAAS6B,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,GAAApC,OAAMT,EAAkB,uBAAAS,OAAsByB,GACxDQ,SAASI,KAAKC,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAASI,KAAKI,YAAYT,GAE1BT,EAAAA,GAAMmB,QAAQjE,EAAE,iBAAkB,gCACpC,CAGF,CAAE,MAAO4C,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAMF,MAAM5C,EAAE,iBAAkB,0BAClC,CAAC,QACCM,GAAW,EACb,GAGI4D,EAAkB,CACtB,CACEC,GAAI,OACJC,MAAO,kBACPC,KAAM,qBACNC,YAAa,2DACbC,MAAO,QAET,CACEJ,GAAI,QACJC,MAAO,mBACPC,KAAM,oBACNC,YAAa,6DACbC,MAAO,SAET,CACEJ,GAAI,QACJC,MAAO,mBACPC,KAAM,cACNC,YAAa,sDACbC,MAAO,UAET,CACEJ,GAAI,SACJC,MAAO,oBACPC,KAAM,uBACNC,YAAa,6DACbC,MAAO,WAILC,EAAkBjC,IACtB,OAAQA,GACN,IAAK,YACH,MAAO,uEACT,IAAK,kBACH,MAAO,2EACT,IAAK,gBACH,MAAO,+DACT,QACE,MAAO,qEAIPkC,EAAoBjE,EAAeM,IAAuB,CAAC,EAC3D4D,EAAcR,EAAgBS,KAAKC,GAAQA,EAAKT,KAAOrD,GAE7D,OACE+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wFAAuFC,UACpGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6GAA4GC,UACzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+GAA8GC,SACzH/E,EAAE,oBAAqB,yBAE1B6E,EAAAA,EAAAA,MAAA,KAAGC,UAAU,kEAAiEC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CACZ9E,EAAE,wBAAyB,yDAGhC6E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMlC,EAAuB,OACtCmC,SAAU7E,EACVyE,UAAU,oGAAmGC,SAAA,EAE7GC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZ9E,EAAE,YAAa,kBAElB6E,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMlC,EAAuB,SACtCmC,SAAU7E,EACVyE,UAAU,wGAAuGC,SAAA,EAEjHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZ9E,EAAE,cAAe,gCAS9BgF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4DAA2DC,SACvEb,EAAgBiB,IAAKP,IACpBC,EAAAA,EAAAA,MAAA,UAEEI,QAASA,IAAMlE,EAAsB6D,EAAKT,IAC1CW,UAAS,iEAAAvD,OACPT,IAAuB8D,EAAKT,GAAE,UAAA5C,OAChBqD,EAAKL,MAAK,YAAAhD,OAAWqD,EAAKL,MAAK,gBAAAhD,OAAeqD,EAAKL,MAAK,WAClE,mHACHQ,SAAA,EAEHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,UAAAvD,OAAYqD,EAAKL,MAAK,iBAAAhD,OAAgBqD,EAAKL,MAAK,2BAA0BQ,UACtFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAvD,OAAKqD,EAAKP,KAAI,UAAA9C,OAASqD,EAAKL,MAAK,mBAAAhD,OAAkBqD,EAAKL,MAAK,qBAE3EM,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8CAA6CC,SAAEH,EAAKR,QACjEK,EAAkBvC,eACjB2C,EAAAA,EAAAA,MAAA,KAAGC,UAAS,4BAAAvD,OAA8BqD,EAAKL,MAAK,mBAAAhD,OAAkBqD,EAAKL,MAAK,QAAOQ,SAAA,CACpFN,EAAkBvC,aAAa,KAAGlC,EAAE,YAAa,uBAK1DgF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEH,EAAKN,gBArBzDM,EAAKT,OA2Bf9D,GACC2E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wEAGjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,yBAAAvD,OAAsC,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,WAAAhD,OAAqB,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,mBAAAhD,OAA6B,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,oBAAAhD,OAA8B,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,yCAAAhD,OAAmD,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,qBAAAhD,OAA+B,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,QAAOQ,UACrQF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,UAAAvD,OAAuB,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,iBAAAhD,OAA2B,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,sBAAqBQ,UACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,0BAAAvD,OAAuC,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,mBAAAhD,OAA6B,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,sBAEhGM,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAS,4BAAAvD,OAAyC,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,mBAAAhD,OAA6B,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,QAAOQ,SACrG/E,EAAE,eAAgB,oBAErB6E,EAAAA,EAAAA,MAAA,KAAGC,UAAS,2BAAAvD,OAAwC,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,mBAAAhD,OAA6B,OAAXmD,QAAW,IAAXA,OAAW,EAAXA,EAAaH,MAAK,QAAOQ,SAAA,CACnGN,EAAkBvC,cAAgB,EAAE,gBAM7C8C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uFAAsFC,UACnGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uDAAsDC,SACjE/E,EAAE,YAAa,iBAElBgF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qDAAoDC,SAC9DN,EAAkBtC,UAAY,IAAIiD,KAAKX,EAAkBtC,WAAWkD,qBAAuB,iBAMpGL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uFAAsFC,UACnGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/E,EAAE,YAAa,iBAElBgF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDAAwDC,SAClEN,EAAkBrC,UAAY,IAAIgD,KAAKX,EAAkBrC,WAAWiD,qBAAuB,oBAQtGR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/E,EAAE,sBAAuB,2BAG5BgF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEO,OAAOC,QAAQd,EAAkBpC,WAAa,CAAC,GAAG8C,IAAIK,IAAA,IAAEC,EAAUzD,GAAKwD,EAAA,OACtEX,EAAAA,EAAAA,MAAA,OAAoBC,UAAU,6DAA4DC,SAAA,EACxFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEU,KAC3DT,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAvD,OAAgDiD,EAAexC,EAAKO,SAAUwC,SAC1F/C,EAAKO,aAGVsC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mDAAkDC,SAAA,CAAE/C,EAAKM,MAAM,OAC9EN,EAAKQ,OAAS,IACbqC,EAAAA,EAAAA,MAAA,QAAMC,UAAU,yCAAwCC,SAAA,CACrD/C,EAAKQ,OAAO,IAAExC,EAAE,SAAU,kBAXzByF,WAqBhBZ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE/E,EAAE,gBAAiB,qBAGtBgF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACtBN,EAAkBhC,eAAiB,IAAI0C,IAAI,CAACxC,EAAQ+C,KACpDb,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,+EAA8EC,SAAA,EACvGF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAEpC,EAAOA,UACjEqC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD,IAAIK,KAAKzC,EAAOD,MAAM2C,2BAG3BL,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAvD,OACK,cAAlBoB,EAAOJ,OACH,uEACA,4EACHwC,SACApC,EAAOJ,WAZFmD,e", "sources": ["pages/Reports/ComplianceReports.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst ComplianceReports = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  \n  const [loading, setLoading] = useState(false);\n  const [complianceData, setComplianceData] = useState({\n    carf: {},\n    cbahi: {},\n    hipaa: {},\n    nphies: {}\n  });\n  const [selectedCompliance, setSelectedCompliance] = useState('carf');\n  const [selectedPeriod, setSelectedPeriod] = useState('month');\n\n  useEffect(() => {\n    loadComplianceData();\n  }, [selectedCompliance, selectedPeriod]);\n\n  const loadComplianceData = async () => {\n    try {\n      setLoading(true);\n      \n      const response = await fetch(`/api/v1/analytics/compliance?type=${selectedCompliance}&period=${selectedPeriod}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const result = await response.json();\n        setComplianceData(prev => ({\n          ...prev,\n          [selectedCompliance]: result.data || {}\n        }));\n      } else {\n        // Mock data for demonstration\n        const mockData = {\n          carf: {\n            overallScore: 94.5,\n            lastAudit: '2024-01-15',\n            nextAudit: '2024-07-15',\n            standards: {\n              'Leadership': { score: 96, status: 'compliant', issues: 0 },\n              'Human Resources': { score: 92, status: 'compliant', issues: 1 },\n              'Business Practices': { score: 98, status: 'compliant', issues: 0 },\n              'Quality Improvement': { score: 89, status: 'needs-attention', issues: 2 },\n              'Health & Safety': { score: 95, status: 'compliant', issues: 0 },\n              'Rights of Persons Served': { score: 97, status: 'compliant', issues: 0 }\n            },\n            recentActions: [\n              { date: '2024-03-15', action: 'Updated quality improvement procedures', status: 'completed' },\n              { date: '2024-03-10', action: 'Staff training on new protocols', status: 'completed' },\n              { date: '2024-03-05', action: 'Documentation review', status: 'in-progress' }\n            ]\n          },\n          cbahi: {\n            overallScore: 91.2,\n            lastAudit: '2024-02-01',\n            nextAudit: '2024-08-01',\n            standards: {\n              'Patient Safety': { score: 93, status: 'compliant', issues: 1 },\n              'Quality Management': { score: 88, status: 'needs-attention', issues: 3 },\n              'Infection Control': { score: 95, status: 'compliant', issues: 0 },\n              'Medication Management': { score: 90, status: 'compliant', issues: 1 },\n              'Clinical Governance': { score: 92, status: 'compliant', issues: 1 },\n              'Human Resources': { score: 89, status: 'needs-attention', issues: 2 }\n            },\n            recentActions: [\n              { date: '2024-03-20', action: 'Infection control audit', status: 'completed' },\n              { date: '2024-03-18', action: 'Quality metrics review', status: 'completed' },\n              { date: '2024-03-12', action: 'Staff competency assessment', status: 'in-progress' }\n            ]\n          },\n          hipaa: {\n            overallScore: 97.8,\n            lastAudit: '2024-01-30',\n            nextAudit: '2024-07-30',\n            standards: {\n              'Administrative Safeguards': { score: 98, status: 'compliant', issues: 0 },\n              'Physical Safeguards': { score: 96, status: 'compliant', issues: 1 },\n              'Technical Safeguards': { score: 99, status: 'compliant', issues: 0 },\n              'Privacy Rule': { score: 97, status: 'compliant', issues: 0 },\n              'Security Rule': { score: 98, status: 'compliant', issues: 0 },\n              'Breach Notification': { score: 100, status: 'compliant', issues: 0 }\n            },\n            recentActions: [\n              { date: '2024-03-22', action: 'Security risk assessment', status: 'completed' },\n              { date: '2024-03-15', action: 'Privacy training update', status: 'completed' },\n              { date: '2024-03-08', action: 'Access control review', status: 'completed' }\n            ]\n          },\n          nphies: {\n            overallScore: 88.5,\n            lastAudit: '2024-02-15',\n            nextAudit: '2024-08-15',\n            standards: {\n              'Data Integration': { score: 90, status: 'compliant', issues: 1 },\n              'Claims Processing': { score: 85, status: 'needs-attention', issues: 3 },\n              'Prior Authorization': { score: 92, status: 'compliant', issues: 1 },\n              'Eligibility Verification': { score: 87, status: 'needs-attention', issues: 2 },\n              'Payment Processing': { score: 89, status: 'compliant', issues: 1 },\n              'Reporting Standards': { score: 88, status: 'needs-attention', issues: 2 }\n            },\n            recentActions: [\n              { date: '2024-03-25', action: 'Claims processing optimization', status: 'in-progress' },\n              { date: '2024-03-20', action: 'Integration testing', status: 'completed' },\n              { date: '2024-03-15', action: 'Reporting format update', status: 'completed' }\n            ]\n          }\n        };\n        \n        setComplianceData(prev => ({\n          ...prev,\n          [selectedCompliance]: mockData[selectedCompliance] || {}\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading compliance data:', error);\n      toast.error(t('errorLoadingCompliance', 'Error loading compliance data'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const exportComplianceReport = async (format) => {\n    try {\n      setLoading(true);\n      \n      const response = await fetch(`/api/v1/analytics/export?type=compliance&format=${format}&compliance=${selectedCompliance}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${selectedCompliance}-compliance-report.${format}`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n        \n        toast.success(t('reportExported', 'Report exported successfully'));\n      } else {\n        throw new Error('Export failed');\n      }\n    } catch (error) {\n      console.error('Error exporting report:', error);\n      toast.error(t('errorExporting', 'Error exporting report'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const complianceTypes = [\n    { \n      id: 'carf', \n      label: 'CARF Compliance', \n      icon: 'fas fa-certificate',\n      description: 'Commission on Accreditation of Rehabilitation Facilities',\n      color: 'blue'\n    },\n    { \n      id: 'cbahi', \n      label: 'CBAHI Compliance', \n      icon: 'fas fa-shield-alt',\n      description: 'Central Board for Accreditation of Healthcare Institutions',\n      color: 'green'\n    },\n    { \n      id: 'hipaa', \n      label: 'HIPAA Compliance', \n      icon: 'fas fa-lock',\n      description: 'Health Insurance Portability and Accountability Act',\n      color: 'purple'\n    },\n    { \n      id: 'nphies', \n      label: 'NPHIES Compliance', \n      icon: 'fas fa-network-wired',\n      description: 'National Platform for Health Information Exchange Services',\n      color: 'orange'\n    }\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'compliant':\n        return 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30';\n      case 'needs-attention':\n        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30';\n      case 'non-compliant':\n        return 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30';\n      default:\n        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30';\n    }\n  };\n\n  const currentCompliance = complianceData[selectedCompliance] || {};\n  const currentType = complianceTypes.find(type => type.id === selectedCompliance);\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  {t('complianceReports', 'Compliance Reports')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-shield-check text-indigo-500 mr-2\"></i>\n                  {t('complianceReportsDesc', 'Regulatory compliance monitoring and reporting')}\n                </p>\n              </div>\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => exportComplianceReport('pdf')}\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors\"\n                >\n                  <i className=\"fas fa-file-pdf mr-2\"></i>\n                  {t('exportPDF', 'Export PDF')}\n                </button>\n                <button\n                  onClick={() => exportComplianceReport('excel')}\n                  disabled={loading}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors\"\n                >\n                  <i className=\"fas fa-file-excel mr-2\"></i>\n                  {t('exportExcel', 'Export Excel')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Compliance Type Selector */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        {complianceTypes.map((type) => (\n          <button\n            key={type.id}\n            onClick={() => setSelectedCompliance(type.id)}\n            className={`p-6 rounded-lg border-2 transition-all duration-200 text-left ${\n              selectedCompliance === type.id\n                ? `border-${type.color}-500 bg-${type.color}-50 dark:bg-${type.color}-900/20`\n                : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600'\n            }`}\n          >\n            <div className=\"flex items-center mb-3\">\n              <div className={`p-3 bg-${type.color}-100 dark:bg-${type.color}-900/40 rounded-lg mr-3`}>\n                <i className={`${type.icon} text-${type.color}-600 dark:text-${type.color}-400 text-xl`}></i>\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900 dark:text-white\">{type.label}</h3>\n                {currentCompliance.overallScore && (\n                  <p className={`text-sm font-medium text-${type.color}-600 dark:text-${type.color}-400`}>\n                    {currentCompliance.overallScore}% {t('compliant', 'Compliant')}\n                  </p>\n                )}\n              </div>\n            </div>\n            <p className=\"text-xs text-gray-600 dark:text-gray-400\">{type.description}</p>\n          </button>\n        ))}\n      </div>\n\n      {/* Compliance Dashboard */}\n      {loading ? (\n        <div className=\"flex items-center justify-center py-12\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600\"></div>\n        </div>\n      ) : (\n        <div className=\"space-y-8\">\n          {/* Overview Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className={`bg-gradient-to-r from-${currentType?.color}-50 to-${currentType?.color}-100 dark:from-${currentType?.color}-900/20 dark:to-${currentType?.color}-800/20 rounded-lg p-6 border border-${currentType?.color}-200 dark:border-${currentType?.color}-700`}>\n              <div className=\"flex items-center\">\n                <div className={`p-3 bg-${currentType?.color}-100 dark:bg-${currentType?.color}-900/40 rounded-lg`}>\n                  <i className={`fas fa-chart-line text-${currentType?.color}-600 dark:text-${currentType?.color}-400 text-2xl`}></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className={`text-sm font-medium text-${currentType?.color}-600 dark:text-${currentType?.color}-400`}>\n                    {t('overallScore', 'Overall Score')}\n                  </h3>\n                  <p className={`text-2xl font-bold text-${currentType?.color}-900 dark:text-${currentType?.color}-100`}>\n                    {currentCompliance.overallScore || 0}%\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg\">\n                  <i className=\"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-2xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">\n                    {t('lastAudit', 'Last Audit')}\n                  </h3>\n                  <p className=\"text-lg font-bold text-blue-900 dark:text-blue-100\">\n                    {currentCompliance.lastAudit ? new Date(currentCompliance.lastAudit).toLocaleDateString() : 'N/A'}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700\">\n              <div className=\"flex items-center\">\n                <div className=\"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg\">\n                  <i className=\"fas fa-calendar-alt text-orange-600 dark:text-orange-400 text-2xl\"></i>\n                </div>\n                <div className=\"ml-4\">\n                  <h3 className=\"text-sm font-medium text-orange-600 dark:text-orange-400\">\n                    {t('nextAudit', 'Next Audit')}\n                  </h3>\n                  <p className=\"text-lg font-bold text-orange-900 dark:text-orange-100\">\n                    {currentCompliance.nextAudit ? new Date(currentCompliance.nextAudit).toLocaleDateString() : 'N/A'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Standards Compliance */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('standardsCompliance', 'Standards Compliance')}\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {Object.entries(currentCompliance.standards || {}).map(([standard, data]) => (\n                <div key={standard} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">{standard}</h4>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(data.status)}`}>\n                      {data.status}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">{data.score}%</span>\n                    {data.issues > 0 && (\n                      <span className=\"text-sm text-red-600 dark:text-red-400\">\n                        {data.issues} {t('issues', 'issues')}\n                      </span>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Recent Actions */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('recentActions', 'Recent Actions')}\n            </h3>\n            \n            <div className=\"space-y-4\">\n              {(currentCompliance.recentActions || []).map((action, index) => (\n                <div key={index} className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  <div>\n                    <p className=\"font-medium text-gray-900 dark:text-white\">{action.action}</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {new Date(action.date).toLocaleDateString()}\n                    </p>\n                  </div>\n                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                    action.status === 'completed' \n                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'\n                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200'\n                  }`}>\n                    {action.status}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ComplianceReports;\n"], "names": ["ComplianceReports", "t", "isRTL", "useLanguage", "user", "useAuth", "loading", "setLoading", "useState", "complianceData", "setComplianceData", "carf", "cbahi", "hipaa", "nphies", "selectedCompliance", "setSelectedCompliance", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "useEffect", "loadComplianceData", "async", "response", "fetch", "concat", "headers", "localStorage", "getItem", "ok", "result", "json", "prev", "_objectSpread", "data", "mockData", "overallScore", "<PERSON><PERSON><PERSON><PERSON>", "next<PERSON><PERSON><PERSON>", "standards", "score", "status", "issues", "recentActions", "date", "action", "error", "console", "toast", "exportComplianceReport", "format", "Error", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "success", "complianceTypes", "id", "label", "icon", "description", "color", "getStatusColor", "currentCompliance", "currentType", "find", "type", "_jsxs", "className", "children", "_jsx", "onClick", "disabled", "map", "Date", "toLocaleDateString", "Object", "entries", "_ref", "standard", "index"], "sourceRoot": ""}