"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2960],{2960:(e,r,a)=>{a.r(r),a.d(r,{default:()=>o});var t=a(2555),s=a(5043),i=a(3216),l=a(7921),d=a(4528),n=a(3768),c=a(579);const o=()=>{const{t:e,isRTL:r}=(0,l.o)(),{user:a}=(0,d.A)(),o=(0,i.Zp)(),m=(0,i.zy)(),[x,u]=(0,s.useState)(!1),g=m.pathname.includes("/new"),[b,p]=(0,s.useState)(g?"submit":"claims"),[y,h]=(0,s.useState)([]),[f,N]=(0,s.useState)([]),[v,j]=(0,s.useState)(""),[k,w]=(0,s.useState)("all"),[C,P]=(0,s.useState)({patientId:"",patientName:"",insuranceProvider:"",policyNumber:"",claimAmount:"",serviceDate:(new Date).toISOString().split("T")[0],serviceType:"",diagnosis:"",treatmentCode:"",notes:"",attachments:[]}),[A,S]=(0,s.useState)({});(0,s.useEffect)(()=>{D(),I()},[]);const D=async()=>{u(!0);try{h([{id:"CLM001",patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",patientNameEn:"Ahmed Mohammed Ali",insuranceProvider:"Bupa Arabia",policyNumber:"BP123456789",claimAmount:2500,serviceDate:"2024-01-15",serviceType:"Physical Therapy",status:"approved",submissionDate:"2024-01-16",approvalDate:"2024-01-20",diagnosis:"Cerebral Palsy"},{id:"CLM002",patientName:"\u0641\u0627\u0637\u0645\u0629 \u0623\u062d\u0645\u062f",patientNameEn:"Fatima Ahmed",insuranceProvider:"Tawuniya",policyNumber:"TW987654321",claimAmount:1800,serviceDate:"2024-01-14",serviceType:"Initial Assessment",status:"pending",submissionDate:"2024-01-15",diagnosis:"Spinal Cord Injury"},{id:"CLM003",patientName:"\u0645\u062d\u0645\u062f \u0633\u0627\u0644\u0645",patientNameEn:"Mohammed Salem",insuranceProvider:"Medgulf",policyNumber:"MG456789123",claimAmount:3200,serviceDate:"2024-01-13",serviceType:"Treatment Package",status:"denied",submissionDate:"2024-01-14",denialReason:"Pre-authorization required",diagnosis:"Muscular Dystrophy"}])}catch(r){n.Ay.error(e("errorLoadingClaims","Error loading insurance claims"))}finally{u(!1)}},I=async()=>{try{N([{id:"bupa",name:"Bupa Arabia",nameAr:"\u0628\u0648\u0628\u0627 \u0627\u0644\u0639\u0631\u0628\u064a\u0629",contactNumber:"+966-11-123-4567",email:"<EMAIL>",website:"www.bupa.com.sa",status:"active",contractNumber:"BUPA-2024-001"},{id:"tawuniya",name:"Tawuniya",nameAr:"\u0627\u0644\u062a\u0639\u0627\u0648\u0646\u064a\u0629",contactNumber:"+966-11-234-5678",email:"<EMAIL>",website:"www.tawuniya.com.sa",status:"active",contractNumber:"TAW-2024-002"},{id:"medgulf",name:"Medgulf",nameAr:"\u0645\u062f\u062c\u0644\u0641",contactNumber:"+966-11-345-6789",email:"<EMAIL>",website:"www.medgulf.com.sa",status:"active",contractNumber:"MED-2024-003"}])}catch(r){n.Ay.error(e("errorLoadingProviders","Error loading insurance providers"))}},T=(e,r)=>{P(a=>(0,t.A)((0,t.A)({},a),{},{[e]:r})),A[e]&&S(r=>(0,t.A)((0,t.A)({},r),{},{[e]:null}))},E=e=>{switch(e){case"approved":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"denied":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";case"processing":return"text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}},M=y.filter(e=>{var r;const a=e.patientName.toLowerCase().includes(v.toLowerCase())||(null===(r=e.patientNameEn)||void 0===r?void 0:r.toLowerCase().includes(v.toLowerCase()))||e.policyNumber.toLowerCase().includes(v.toLowerCase())||e.insuranceProvider.toLowerCase().includes(v.toLowerCase()),t="all"===k||e.status===k;return a&&t}),L=[{value:"initial_assessment",label:e("initialAssessment","Initial Assessment")},{value:"physical_therapy",label:e("physicalTherapy","Physical Therapy")},{value:"occupational_therapy",label:e("occupationalTherapy","Occupational Therapy")},{value:"speech_therapy",label:e("speechTherapy","Speech Therapy")},{value:"treatment_package",label:e("treatmentPackage","Treatment Package")},{value:"consultation",label:e("consultation","Consultation")},{value:"follow_up",label:e("followUp","Follow-up")}];return(0,c.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,c.jsxs)("div",{className:"mb-8",children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("insuranceManagement","Insurance Management")}),(0,c.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("insuranceManagementDesc","Manage insurance claims, providers, and verification processes")})]}),(0,c.jsx)("div",{className:"mb-6",children:(0,c.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,c.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,c.jsxs)("button",{onClick:()=>p("claims"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("claims"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,c.jsx)("i",{className:"fas fa-file-medical mr-2"}),e("insuranceClaims","Insurance Claims")]}),(0,c.jsxs)("button",{onClick:()=>p("submit"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("submit"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,c.jsx)("i",{className:"fas fa-plus mr-2"}),e("submitClaim","Submit Claim")]}),(0,c.jsxs)("button",{onClick:()=>p("providers"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("providers"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,c.jsx)("i",{className:"fas fa-shield-alt mr-2"}),e("insuranceProviders","Insurance Providers")]}),(0,c.jsxs)("button",{onClick:()=>p("verification"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("verification"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,c.jsx)("i",{className:"fas fa-check-circle mr-2"}),e("verification","Verification")]})]})})}),"claims"===b&&(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,c.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,c.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,c.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[(0,c.jsx)("i",{className:"fas fa-file-medical text-blue-600 dark:text-blue-400 mr-2"}),e("insuranceClaims","Insurance Claims")]}),(0,c.jsxs)("div",{className:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4",children:[(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsx)("input",{type:"text",value:v,onChange:e=>j(e.target.value),placeholder:e("searchClaims","Search claims..."),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),(0,c.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]}),(0,c.jsxs)("select",{value:k,onChange:e=>w(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,c.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,c.jsx)("option",{value:"approved",children:e("approved","Approved")}),(0,c.jsx)("option",{value:"pending",children:e("pending","Pending")}),(0,c.jsx)("option",{value:"denied",children:e("denied","Denied")}),(0,c.jsx)("option",{value:"processing",children:e("processing","Processing")})]})]})]})}),(0,c.jsx)("div",{className:"overflow-x-auto",children:(0,c.jsxs)("table",{className:"w-full",children:[(0,c.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,c.jsxs)("tr",{children:[(0,c.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("claimId","Claim ID")}),(0,c.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,c.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("provider","Provider")}),(0,c.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("amount","Amount")}),(0,c.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,c.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("submissionDate","Submission Date")}),(0,c.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,c.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:M.map(a=>(0,c.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,c.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,c.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a.id})}),(0,c.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:r?a.patientName:a.patientNameEn}),(0,c.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:a.policyNumber})]})}),(0,c.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,c.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:a.insuranceProvider})}),(0,c.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,c.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[a.claimAmount.toLocaleString()," ",e("sar","SAR")]})}),(0,c.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,c.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(E(a.status)),children:e(a.status,a.status)})}),(0,c.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:a.submissionDate}),(0,c.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200",children:(0,c.jsx)("i",{className:"fas fa-eye"})}),(0,c.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200",children:(0,c.jsx)("i",{className:"fas fa-download"})}),"pending"===a.status&&(0,c.jsx)("button",{className:"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-200",children:(0,c.jsx)("i",{className:"fas fa-edit"})})]})})]},a.id))})]})}),0===M.length&&(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("i",{className:"fas fa-file-medical text-4xl text-gray-400 mb-4"}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("noClaimsFound","No insurance claims found")})]})]}),"submit"===b&&(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-plus text-green-600 dark:text-green-400 mr-2"}),e("submitNewClaim","Submit New Insurance Claim")]}),(0,c.jsxs)("form",{onSubmit:async r=>{if(r.preventDefault(),(()=>{const r={};return C.patientName.trim()||(r.patientName=e("patientNameRequired","Patient name is required")),C.insuranceProvider||(r.insuranceProvider=e("insuranceProviderRequired","Insurance provider is required")),C.policyNumber.trim()||(r.policyNumber=e("policyNumberRequired","Policy number is required")),(!C.claimAmount||C.claimAmount<=0)&&(r.claimAmount=e("validAmountRequired","Valid claim amount is required")),C.serviceType||(r.serviceType=e("serviceTypeRequired","Service type is required")),C.diagnosis.trim()||(r.diagnosis=e("diagnosisRequired","Diagnosis is required")),S(r),0===Object.keys(r).length})()){u(!0);try{await new Promise(e=>setTimeout(e,1500));const r=(0,t.A)((0,t.A)({},C),{},{id:"CLM".concat(Date.now()),status:"pending",submissionDate:(new Date).toISOString().split("T")[0]});h(e=>[r,...e]),P({patientId:"",patientName:"",insuranceProvider:"",policyNumber:"",claimAmount:"",serviceDate:(new Date).toISOString().split("T")[0],serviceType:"",diagnosis:"",treatmentCode:"",notes:"",attachments:[]}),n.Ay.success(e("claimSubmittedSuccessfully","Insurance claim submitted successfully")),p("claims")}catch(a){n.Ay.error(e("errorSubmittingClaim","Error submitting insurance claim"))}finally{u(!1)}}else n.Ay.error(e("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-6",children:[(0,c.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,c.jsx)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4",children:e("patientInformation","Patient Information")}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("patientName","Patient Name")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("input",{type:"text",value:C.patientName,onChange:e=>T("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(A.patientName?"border-red-500":"border-gray-300"),placeholder:e("enterPatientName","Enter patient name")}),A.patientName&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:A.patientName})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("policyNumber","Policy Number")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("input",{type:"text",value:C.policyNumber,onChange:e=>T("policyNumber",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(A.policyNumber?"border-red-500":"border-gray-300"),placeholder:e("enterPolicyNumber","Enter policy number")}),A.policyNumber&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:A.policyNumber})]})]})]}),(0,c.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:[(0,c.jsx)("h3",{className:"text-md font-semibold text-green-900 dark:text-green-100 mb-4",children:e("insuranceInformation","Insurance Information")}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("insuranceProvider","Insurance Provider")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsxs)("select",{value:C.insuranceProvider,onChange:e=>T("insuranceProvider",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(A.insuranceProvider?"border-red-500":"border-gray-300"),children:[(0,c.jsx)("option",{value:"",children:e("selectInsuranceProvider","Select insurance provider")}),f.map(e=>(0,c.jsx)("option",{value:e.name,children:e.name},e.id))]}),A.insuranceProvider&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:A.insuranceProvider})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("claimAmount","Claim Amount")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsx)("input",{type:"number",step:"0.01",min:"0",value:C.claimAmount,onChange:e=>T("claimAmount",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(A.claimAmount?"border-red-500":"border-gray-300"),placeholder:"0.00"}),(0,c.jsx)("span",{className:"absolute right-3 top-2 text-gray-500 dark:text-gray-400",children:e("sar","SAR")})]}),A.claimAmount&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:A.claimAmount})]})]})]}),(0,c.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4",children:[(0,c.jsx)("h3",{className:"text-md font-semibold text-purple-900 dark:text-purple-100 mb-4",children:e("serviceInformation","Service Information")}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("serviceDate","Service Date")}),(0,c.jsx)("input",{type:"date",value:C.serviceDate,onChange:e=>T("serviceDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("serviceType","Service Type")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsxs)("select",{value:C.serviceType,onChange:e=>T("serviceType",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(A.serviceType?"border-red-500":"border-gray-300"),children:[(0,c.jsx)("option",{value:"",children:e("selectServiceType","Select service type")}),L.map(e=>(0,c.jsx)("option",{value:e.value,children:e.label},e.value))]}),A.serviceType&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:A.serviceType})]})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("diagnosis","Diagnosis")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("textarea",{value:C.diagnosis,onChange:e=>T("diagnosis",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(A.diagnosis?"border-red-500":"border-gray-300"),placeholder:e("enterDiagnosis","Enter diagnosis")}),A.diagnosis&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:A.diagnosis})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("treatmentCode","Treatment Code")}),(0,c.jsx)("input",{type:"text",value:C.treatmentCode,onChange:e=>T("treatmentCode",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterTreatmentCode","Enter treatment code")}),(0,c.jsxs)("div",{className:"mt-3",children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("notes","Additional Notes")}),(0,c.jsx)("textarea",{value:C.notes,onChange:e=>T("notes",e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterAdditionalNotes","Enter additional notes")})]})]})]})]}),(0,c.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:[(0,c.jsx)("h3",{className:"text-md font-semibold text-yellow-900 dark:text-yellow-100 mb-4",children:e("attachments","Attachments")}),(0,c.jsxs)("div",{className:"border-2 border-dashed border-yellow-300 dark:border-yellow-600 rounded-lg p-6 text-center",children:[(0,c.jsx)("i",{className:"fas fa-cloud-upload-alt text-3xl text-yellow-400 mb-2"}),(0,c.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300 mb-2",children:e("dragDropFiles","Drag and drop files here, or click to select")}),(0,c.jsxs)("button",{type:"button",className:"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-plus mr-2"}),e("selectFiles","Select Files")]}),(0,c.jsx)("p",{className:"text-xs text-yellow-600 dark:text-yellow-400 mt-2",children:e("supportedFormats","Supported formats: PDF, JPG, PNG, DOC (Max 10MB)")})]})]}),(0,c.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,c.jsx)("button",{type:"button",onClick:()=>o("/financial/insurance"),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:e("cancel","Cancel")}),(0,c.jsx)("button",{type:"submit",disabled:x,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),e("submitting","Submitting...")]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("i",{className:"fas fa-paper-plane mr-2"}),e("submitClaim","Submit Claim")]})})]})]})]}),"providers"===b&&(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-shield-alt text-purple-600 dark:text-purple-400 mr-2"}),e("insuranceProviders","Insurance Providers")]}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:f.map(r=>(0,c.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:r.name}),(0,c.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("active"===r.status?"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400":"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400"),children:e(r.status,r.status)})]}),(0,c.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-phone text-gray-400 mr-2"}),(0,c.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:r.contactNumber})]}),(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-envelope text-gray-400 mr-2"}),(0,c.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:r.email})]}),(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-globe text-gray-400 mr-2"}),(0,c.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:r.website})]}),(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-file-contract text-gray-400 mr-2"}),(0,c.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:r.contractNumber})]})]}),(0,c.jsxs)("div",{className:"mt-4 flex space-x-2",children:[(0,c.jsxs)("button",{className:"flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:[(0,c.jsx)("i",{className:"fas fa-edit mr-1"}),e("edit","Edit")]}),(0,c.jsxs)("button",{className:"flex-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:[(0,c.jsx)("i",{className:"fas fa-check mr-1"}),e("verify","Verify")]})]})]},r.id))})]}),"verification"===b&&(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 mr-2"}),e("insuranceVerification","Insurance Verification")]}),(0,c.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,c.jsx)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4",children:e("verifyPatientInsurance","Verify Patient Insurance")}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("patientId","Patient ID")}),(0,c.jsx)("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterPatientId","Enter patient ID")})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("policyNumber","Policy Number")}),(0,c.jsx)("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterPolicyNumber","Enter policy number")})]})]}),(0,c.jsx)("div",{className:"mt-4",children:(0,c.jsxs)("button",{className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-search mr-2"}),e("verifyInsurance","Verify Insurance")]})})]}),(0,c.jsxs)("div",{className:"mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center",children:[(0,c.jsx)("i",{className:"fas fa-shield-alt text-4xl text-gray-400 mb-4"}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("verificationResultsWillAppearHere","Verification results will appear here")})]})]})]})}}}]);
//# sourceMappingURL=2960.9cda3f16.chunk.js.map