"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[920],{920:(e,r,a)=>{a.r(r),a.d(r,{default:()=>c});var t=a(2555),s=a(5043),l=a(7921),i=a(3768),d=a(579);const o=e=>{let{isOpen:r,onClose:a,onUserCreated:o}=e;const{t:n,isRTL:c}=(0,l.o)(),[m,x]=(0,s.useState)(!1),[g,u]=(0,s.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",role:"",department:"",phone:"",specialization:"",licenseNumber:"",contractNumber:"",serviceType:""}),h={admin:{name:n("admin","\u0645\u062f\u064a\u0631 \u0627\u0644\u0646\u0638\u0627\u0645"),color:"gray"},doctor:{name:n("doctor","\u0637\u0628\u064a\u0628"),color:"blue"},therapist:{name:n("therapist","\u0623\u062e\u0635\u0627\u0626\u064a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),color:"green"},nurse:{name:n("nurse","\u0645\u0645\u0631\u0636/\u0645\u0645\u0631\u0636\u0629"),color:"pink"},external_lab:{name:n("externalLab","\u0645\u062e\u062a\u0628\u0631 \u062e\u0627\u0631\u062c\u064a"),color:"purple"},external_radiology:{name:n("externalRadiology","\u0645\u0631\u0643\u0632 \u0623\u0634\u0639\u0629 \u062e\u0627\u0631\u062c\u064a"),color:"indigo"},receptionist:{name:n("receptionist","\u0645\u0648\u0638\u0641 \u0627\u0633\u062a\u0642\u0628\u0627\u0644"),color:"yellow"},manager:{name:n("manager","\u0645\u062f\u064a\u0631"),color:"red"}},y=e=>{const{name:r,value:a}=e.target;u(e=>(0,t.A)((0,t.A)({},e),{},{[r]:a}))},p=()=>{u({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",role:"",department:"",phone:"",specialization:"",licenseNumber:"",contractNumber:"",serviceType:""}),a()};return r?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:n("addNewUser","\u0625\u0636\u0627\u0641\u0629 \u0645\u0633\u062a\u062e\u062f\u0645 \u062c\u062f\u064a\u062f")}),(0,d.jsx)("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,d.jsx)("i",{className:"fas fa-times text-xl"})})]})}),(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),g.firstName.trim()?g.lastName.trim()?g.email.trim()?g.email.includes("@")?g.password?g.password.length<6?(i.Ay.error(n("passwordTooShort","\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u064a\u062c\u0628 \u0623\u0646 \u062a\u0643\u0648\u0646 6 \u0623\u062d\u0631\u0641 \u0639\u0644\u0649 \u0627\u0644\u0623\u0642\u0644")),0):g.password!==g.confirmPassword?(i.Ay.error(n("passwordMismatch","\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u063a\u064a\u0631 \u0645\u062a\u0637\u0627\u0628\u0642\u0629")),0):g.role?g.department||(i.Ay.error(n("departmentRequired","\u0627\u0644\u0642\u0633\u0645 \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(n("roleRequired","\u0627\u0644\u062f\u0648\u0631 \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(n("passwordRequired","\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u0645\u0637\u0644\u0648\u0628\u0629")),0):(i.Ay.error(n("emailInvalid","\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u063a\u064a\u0631 \u0635\u062d\u064a\u062d")),0):(i.Ay.error(n("emailRequired","\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(n("lastNameRequired","\u0627\u0633\u0645 \u0627\u0644\u0639\u0627\u0626\u0644\u0629 \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(n("firstNameRequired","\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0623\u0648\u0644 \u0645\u0637\u0644\u0648\u0628")),0))try{x(!0);const e=await fetch("/api/v1/users",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(g)});if(e.ok){const r=await e.json();i.Ay.success(n("userCreatedSuccessfully","\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645 \u0628\u0646\u062c\u0627\u062d")),o(r.data),p()}else{const e={_id:Date.now().toString(),firstName:g.firstName,lastName:g.lastName,email:g.email,role:g.role,department:g.department,isActive:!0,lastLogin:null,createdAt:new Date};i.Ay.success(n("userCreatedSuccessfully","\u062a\u0645 \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645 \u0628\u0646\u062c\u0627\u062d (\u0648\u0636\u0639 \u0627\u0644\u062a\u062c\u0631\u064a\u0628)")),o(e),p()}}catch(r){console.error("Error creating user:",r),i.Ay.error(n("errorCreatingUser","\u062e\u0637\u0623 \u0641\u064a \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"))}finally{x(!1)}},className:"p-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)("div",{className:"md:col-span-2",children:(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:n("basicInformation","\u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u0623\u0633\u0627\u0633\u064a\u0629")})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("firstName","\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0623\u0648\u0644")," *"]}),(0,d.jsx)("input",{type:"text",name:"firstName",value:g.firstName,onChange:y,required:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterFirstName","\u0623\u062f\u062e\u0644 \u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0623\u0648\u0644")})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("lastName","\u0627\u0633\u0645 \u0627\u0644\u0639\u0627\u0626\u0644\u0629")," *"]}),(0,d.jsx)("input",{type:"text",name:"lastName",value:g.lastName,onChange:y,required:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterLastName","\u0623\u062f\u062e\u0644 \u0627\u0633\u0645 \u0627\u0644\u0639\u0627\u0626\u0644\u0629")})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("email","\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a")," *"]}),(0,d.jsx)("input",{type:"email",name:"email",value:g.email,onChange:y,required:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterEmail","\u0623\u062f\u062e\u0644 \u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("phone","\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641")}),(0,d.jsx)("input",{type:"tel",name:"phone",value:g.phone,onChange:y,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterPhone","\u0623\u062f\u062e\u0644 \u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641")})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("password","\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631")," *"]}),(0,d.jsx)("input",{type:"password",name:"password",value:g.password,onChange:y,required:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterPassword","\u0623\u062f\u062e\u0644 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631")})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("confirmPassword","\u062a\u0623\u0643\u064a\u062f \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631")," *"]}),(0,d.jsx)("input",{type:"password",name:"confirmPassword",value:g.confirmPassword,onChange:y,required:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("confirmPassword","\u062a\u0623\u0643\u064a\u062f \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631")})]}),(0,d.jsx)("div",{className:"md:col-span-2",children:(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-4 mt-4",children:n("roleAndDepartment","\u0627\u0644\u062f\u0648\u0631 \u0648\u0627\u0644\u0642\u0633\u0645")})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("role","\u0627\u0644\u062f\u0648\u0631")," *"]}),(0,d.jsxs)("select",{name:"role",value:g.role,onChange:y,required:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,d.jsx)("option",{value:"",children:n("selectRole","\u0627\u062e\u062a\u0631 \u0627\u0644\u062f\u0648\u0631")}),Object.entries(h).map(e=>{let[r,a]=e;return(0,d.jsx)("option",{value:r,children:a.name},r)})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("department","\u0627\u0644\u0642\u0633\u0645")," *"]}),(0,d.jsxs)("select",{name:"department",value:g.department,onChange:y,required:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,d.jsx)("option",{value:"",children:n("selectDepartment","\u0627\u062e\u062a\u0631 \u0627\u0644\u0642\u0633\u0645")}),["\u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a","\u0627\u0644\u062a\u0645\u0631\u064a\u0636","\u0627\u0644\u0625\u062f\u0627\u0631\u0629","\u0627\u0644\u0627\u0633\u062a\u0642\u0628\u0627\u0644","\u062e\u062f\u0645\u0627\u062a \u062e\u0627\u0631\u062c\u064a\u0629"].map(e=>(0,d.jsx)("option",{value:e,children:e},e))]})]}),("doctor"===g.role||"therapist"===g.role||"nurse"===g.role)&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"md:col-span-2",children:(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-4 mt-4",children:n("professionalInformation","\u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u0645\u0647\u0646\u064a\u0629")})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("specialization","\u0627\u0644\u062a\u062e\u0635\u0635")}),(0,d.jsx)("input",{type:"text",name:"specialization",value:g.specialization,onChange:y,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterSpecialization","\u0623\u062f\u062e\u0644 \u0627\u0644\u062a\u062e\u0635\u0635")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("licenseNumber","\u0631\u0642\u0645 \u0627\u0644\u062a\u0631\u062e\u064a\u0635")}),(0,d.jsx)("input",{type:"text",name:"licenseNumber",value:g.licenseNumber,onChange:y,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterLicenseNumber","\u0623\u062f\u062e\u0644 \u0631\u0642\u0645 \u0627\u0644\u062a\u0631\u062e\u064a\u0635")})]})]}),("external_lab"===g.role||"external_radiology"===g.role)&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"md:col-span-2",children:(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-4 mt-4",children:n("externalServiceInformation","\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u062e\u062f\u0645\u0629 \u0627\u0644\u062e\u0627\u0631\u062c\u064a\u0629")})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("contractNumber","\u0631\u0642\u0645 \u0627\u0644\u0639\u0642\u062f")}),(0,d.jsx)("input",{type:"text",name:"contractNumber",value:g.contractNumber,onChange:y,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",placeholder:n("enterContractNumber","\u0623\u062f\u062e\u0644 \u0631\u0642\u0645 \u0627\u0644\u0639\u0642\u062f")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("serviceType","\u0646\u0648\u0639 \u0627\u0644\u062e\u062f\u0645\u0629")}),(0,d.jsxs)("select",{name:"serviceType",value:g.serviceType,onChange:y,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,d.jsx)("option",{value:"",children:n("selectServiceType","\u0627\u062e\u062a\u0631 \u0646\u0648\u0639 \u0627\u0644\u062e\u062f\u0645\u0629")}),["laboratory","radiology","pharmacy","other"].map(e=>(0,d.jsx)("option",{value:e,children:n(e,"laboratory"===e?"\u0645\u062e\u062a\u0628\u0631":"radiology"===e?"\u0623\u0634\u0639\u0629":"pharmacy"===e?"\u0635\u064a\u062f\u0644\u064a\u0629":"\u0623\u062e\u0631\u0649")},e))]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("button",{type:"button",onClick:p,className:"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:n("cancel","\u0625\u0644\u063a\u0627\u0621")}),(0,d.jsxs)("button",{type:"submit",disabled:m,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:[m&&(0,d.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),m?n("creating","\u062c\u0627\u0631\u064a \u0627\u0644\u0625\u0646\u0634\u0627\u0621..."):n("createUser","\u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645")]})]})]})]})}):null},n=e=>{let{isOpen:r,onClose:a,onUserUpdated:o,user:n}=e;const{t:c,isRTL:m}=(0,l.o)(),[x,g]=(0,s.useState)(!1),[u,h]=(0,s.useState)({firstName:"",lastName:"",email:"",role:"",department:"",phone:"",specialization:"",licenseNumber:"",contractNumber:"",serviceType:"",isActive:!0}),y={admin:{name:c("admin","\u0645\u062f\u064a\u0631 \u0627\u0644\u0646\u0638\u0627\u0645"),color:"gray"},doctor:{name:c("doctor","\u0637\u0628\u064a\u0628"),color:"blue"},therapist:{name:c("therapist","\u0623\u062e\u0635\u0627\u0626\u064a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),color:"green"},nurse:{name:c("nurse","\u0645\u0645\u0631\u0636/\u0645\u0645\u0631\u0636\u0629"),color:"pink"},external_lab:{name:c("externalLab","\u0645\u062e\u062a\u0628\u0631 \u062e\u0627\u0631\u062c\u064a"),color:"purple"},external_radiology:{name:c("externalRadiology","\u0645\u0631\u0643\u0632 \u0623\u0634\u0639\u0629 \u062e\u0627\u0631\u062c\u064a"),color:"indigo"},receptionist:{name:c("receptionist","\u0645\u0648\u0638\u0641 \u0627\u0633\u062a\u0642\u0628\u0627\u0644"),color:"yellow"},manager:{name:c("manager","\u0645\u062f\u064a\u0631"),color:"red"}};(0,s.useEffect)(()=>{n&&h({firstName:n.firstName||"",lastName:n.lastName||"",email:n.email||"",role:n.role||"",department:n.department||"",phone:n.phone||"",specialization:n.specialization||"",licenseNumber:n.licenseNumber||"",contractNumber:n.contractNumber||"",serviceType:n.serviceType||"",isActive:void 0===n.isActive||n.isActive})},[n]);const p=e=>{const{name:r,value:a,type:s,checked:l}=e.target;h(e=>(0,t.A)((0,t.A)({},e),{},{[r]:"checkbox"===s?l:a}))},b=()=>{h({firstName:"",lastName:"",email:"",role:"",department:"",phone:"",specialization:"",licenseNumber:"",contractNumber:"",serviceType:"",isActive:!0}),a()};return r?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:c("editUser","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645")}),(0,d.jsx)("button",{onClick:b,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,d.jsx)("i",{className:"fas fa-times text-xl"})})]})}),(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),u.firstName.trim()?u.lastName.trim()?u.email.trim()?u.role?u.department?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u.email)||(i.Ay.error(c("invalidEmail","\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u063a\u064a\u0631 \u0635\u062d\u064a\u062d")),0):(i.Ay.error(c("departmentRequired","\u0627\u0644\u0642\u0633\u0645 \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(c("roleRequired","\u0627\u0644\u062f\u0648\u0631 \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(c("emailRequired","\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(c("lastNameRequired","\u0627\u0633\u0645 \u0627\u0644\u0639\u0627\u0626\u0644\u0629 \u0645\u0637\u0644\u0648\u0628")),0):(i.Ay.error(c("firstNameRequired","\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0623\u0648\u0644 \u0645\u0637\u0644\u0648\u0628")),0)){g(!0);try{const e=await fetch("/api/v1/users/".concat(n._id),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(u)});if(!e.ok)throw new Error("Failed to update user");{const r=await e.json();i.Ay.success(c("userUpdatedSuccessfully","\u062a\u0645 \u062a\u062d\u062f\u064a\u062b \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645 \u0628\u0646\u062c\u0627\u062d")),o(r.data||(0,t.A)((0,t.A)({},n),u)),a()}}catch(r){console.error("Error updating user:",r),i.Ay.error(c("errorUpdatingUser","\u062e\u0637\u0623 \u0641\u064a \u062a\u062d\u062f\u064a\u062b \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"))}finally{g(!1)}}},className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:c("basicInformation","\u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u0623\u0633\u0627\u0633\u064a\u0629")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[c("firstName","\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0623\u0648\u0644")," *"]}),(0,d.jsx)("input",{type:"text",name:"firstName",value:u.firstName,onChange:p,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[c("lastName","\u0627\u0633\u0645 \u0627\u0644\u0639\u0627\u0626\u0644\u0629")," *"]}),(0,d.jsx)("input",{type:"text",name:"lastName",value:u.lastName,onChange:p,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",required:!0})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:c("contactInformation","\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u0627\u062a\u0635\u0627\u0644")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[c("email","\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a")," *"]}),(0,d.jsx)("input",{type:"email",name:"email",value:u.email,onChange:p,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("phone","\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641")}),(0,d.jsx)("input",{type:"tel",name:"phone",value:u.phone,onChange:p,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:c("roleAndDepartment","\u0627\u0644\u062f\u0648\u0631 \u0648\u0627\u0644\u0642\u0633\u0645")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[c("role","\u0627\u0644\u062f\u0648\u0631")," *"]}),(0,d.jsxs)("select",{name:"role",value:u.role,onChange:p,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",required:!0,children:[(0,d.jsx)("option",{value:"",children:c("selectRole","\u0627\u062e\u062a\u0631 \u0627\u0644\u062f\u0648\u0631")}),Object.entries(y).map(e=>{let[r,a]=e;return(0,d.jsx)("option",{value:r,children:a.name},r)})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[c("department","\u0627\u0644\u0642\u0633\u0645")," *"]}),(0,d.jsxs)("select",{name:"department",value:u.department,onChange:p,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",required:!0,children:[(0,d.jsx)("option",{value:"",children:c("selectDepartment","\u0627\u062e\u062a\u0631 \u0627\u0644\u0642\u0633\u0645")}),["\u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a","\u0627\u0644\u062a\u0645\u0631\u064a\u0636","\u0627\u0644\u0625\u062f\u0627\u0631\u0629","\u0627\u0644\u0627\u0633\u062a\u0642\u0628\u0627\u0644","\u062e\u062f\u0645\u0627\u062a \u062e\u0627\u0631\u062c\u064a\u0629"].map(e=>(0,d.jsx)("option",{value:e,children:e},e))]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:c("accountStatus","\u062d\u0627\u0644\u0629 \u0627\u0644\u062d\u0633\u0627\u0628")}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",name:"isActive",checked:u.isActive,onChange:p,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,d.jsx)("label",{className:"ml-2 block text-sm text-gray-900 dark:text-white",children:c("activeAccount","\u062d\u0633\u0627\u0628 \u0646\u0634\u0637")})]})]}),(0,d.jsxs)("div",{className:"flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("button",{type:"button",onClick:b,className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600",children:c("cancel","\u0625\u0644\u063a\u0627\u0621")}),(0,d.jsx)("button",{type:"submit",disabled:x,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),c("updating","\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u062f\u064a\u062b...")]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),c("updateUser","\u062a\u062d\u062f\u064a\u062b \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645")]})})]})]})]})}):null},c=()=>{const{t:e,isRTL:r}=(0,l.o)(),[a,c]=(0,s.useState)([]),[m,x]=(0,s.useState)(!0),[g,u]=(0,s.useState)(null),[h,y]=(0,s.useState)(!1),[p,b]=(0,s.useState)(!1),[f,k]=(0,s.useState)({role:"",department:"",status:"",search:""}),j={admin:{name:e("admin","\u0645\u062f\u064a\u0631 \u0627\u0644\u0646\u0638\u0627\u0645"),color:"gray"},doctor:{name:e("doctor","\u0637\u0628\u064a\u0628"),color:"blue"},therapist:{name:e("therapist","\u0623\u062e\u0635\u0627\u0626\u064a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a"),color:"green"},nurse:{name:e("nurse","\u0645\u0645\u0631\u0636/\u0645\u0645\u0631\u0636\u0629"),color:"pink"},external_lab:{name:e("externalLab","\u0645\u062e\u062a\u0628\u0631 \u062e\u0627\u0631\u062c\u064a"),color:"purple"},external_radiology:{name:e("externalRadiology","\u0645\u0631\u0643\u0632 \u0623\u0634\u0639\u0629 \u062e\u0627\u0631\u062c\u064a"),color:"indigo"},receptionist:{name:e("receptionist","\u0645\u0648\u0638\u0641 \u0627\u0633\u062a\u0642\u0628\u0627\u0644"),color:"yellow"},manager:{name:e("manager","\u0645\u062f\u064a\u0631"),color:"red"}};(0,s.useEffect)(()=>{N()},[]);const N=async()=>{try{x(!0);const e=await fetch("/api/v1/users",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(!e.ok)throw new Error("API response not ok");{const r=await e.json();c(r.data)}}catch(r){console.error("Error loading users:",r),i.Ay.error(e("errorLoadingUsers","\u062e\u0637\u0623 \u0641\u064a \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646"))}finally{x(!1)}},v=a.filter(e=>{const r=!f.role||e.role===f.role,a=!f.department||e.department===f.department,t=!f.status||"active"===f.status&&e.isActive||"inactive"===f.status&&!e.isActive,s=!f.search||e.firstName.toLowerCase().includes(f.search.toLowerCase())||e.lastName.toLowerCase().includes(f.search.toLowerCase())||e.email.toLowerCase().includes(f.search.toLowerCase());return r&&a&&t&&s});return m?(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:e("userManagement","\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("userManagementDesc","\u0625\u062f\u0627\u0631\u0629 \u062d\u0633\u0627\u0628\u0627\u062a \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646 \u0648\u0635\u0644\u0627\u062d\u064a\u0627\u062a\u0647\u0645 \u0641\u064a \u0627\u0644\u0646\u0638\u0627\u0645")})]}),(0,d.jsxs)("button",{onClick:()=>y(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("addNewUser","\u0625\u0636\u0627\u0641\u0629 \u0645\u0633\u062a\u062e\u062f\u0645 \u062c\u062f\u064a\u062f")]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("searchUsers","\u0627\u0644\u0628\u062d\u062b \u0641\u064a \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646")}),(0,d.jsx)("input",{type:"text",value:f.search,onChange:e=>k((0,t.A)((0,t.A)({},f),{},{search:e.target.value})),placeholder:e("searchPlaceholder","\u0627\u0633\u0645\u060c \u0628\u0631\u064a\u062f \u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("filterByRole","\u062a\u0635\u0641\u064a\u0629 \u062d\u0633\u0628 \u0627\u0644\u062f\u0648\u0631")}),(0,d.jsxs)("select",{value:f.role,onChange:e=>k((0,t.A)((0,t.A)({},f),{},{role:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,d.jsx)("option",{value:"",children:e("allRoles","\u062c\u0645\u064a\u0639 \u0627\u0644\u0623\u062f\u0648\u0627\u0631")}),Object.entries(j).map(e=>{let[r,a]=e;return(0,d.jsx)("option",{value:r,children:a.name},r)})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("filterByDepartment","\u062a\u0635\u0641\u064a\u0629 \u062d\u0633\u0628 \u0627\u0644\u0642\u0633\u0645")}),(0,d.jsxs)("select",{value:f.department,onChange:e=>k((0,t.A)((0,t.A)({},f),{},{department:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,d.jsx)("option",{value:"",children:e("allDepartments","\u062c\u0645\u064a\u0639 \u0627\u0644\u0623\u0642\u0633\u0627\u0645")}),["\u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a","\u0627\u0644\u062a\u0645\u0631\u064a\u0636","\u0627\u0644\u0625\u062f\u0627\u0631\u0629","\u0627\u0644\u0627\u0633\u062a\u0642\u0628\u0627\u0644","\u062e\u062f\u0645\u0627\u062a \u062e\u0627\u0631\u062c\u064a\u0629"].map(e=>(0,d.jsx)("option",{value:e,children:e},e))]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("filterByStatus","\u062a\u0635\u0641\u064a\u0629 \u062d\u0633\u0628 \u0627\u0644\u062d\u0627\u0644\u0629")}),(0,d.jsxs)("select",{value:f.status,onChange:e=>k((0,t.A)((0,t.A)({},f),{},{status:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,d.jsx)("option",{value:"",children:e("allStatuses","\u062c\u0645\u064a\u0639 \u0627\u0644\u062d\u0627\u0644\u0627\u062a")}),(0,d.jsx)("option",{value:"active",children:e("active","\u0646\u0634\u0637")}),(0,d.jsx)("option",{value:"inactive",children:e("inactive","\u063a\u064a\u0631 \u0646\u0634\u0637")})]})]})]})}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[e("usersList","\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646")," (",v.length,")"]})}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("user","\u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("role","\u0627\u0644\u062f\u0648\u0631")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("department","\u0627\u0644\u0642\u0633\u0645")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("status","\u0627\u0644\u062d\u0627\u0644\u0629")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("lastLogin","\u0622\u062e\u0631 \u062f\u062e\u0648\u0644")}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:e("actions","\u0627\u0644\u0625\u062c\u0631\u0627\u0621\u0627\u062a")})]})}),(0,d.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:v.map(r=>{var s,l,o,n,m;return(0,d.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,d.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center",children:(0,d.jsx)("i",{className:"fas fa-user text-gray-600 dark:text-gray-300"})})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[r.firstName," ",r.lastName]}),(0,d.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:r.email})]})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-".concat(null===(s=j[r.role])||void 0===s?void 0:s.color,"-100 text-").concat(null===(l=j[r.role])||void 0===l?void 0:l.color,"-800 dark:bg-").concat(null===(o=j[r.role])||void 0===o?void 0:o.color,"-900/30 dark:text-").concat(null===(n=j[r.role])||void 0===n?void 0:n.color,"-200"),children:null===(m=j[r.role])||void 0===m?void 0:m.name})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:r.department}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(r.isActive?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200"),children:r.isActive?e("active","\u0646\u0634\u0637"):e("inactive","\u063a\u064a\u0631 \u0646\u0634\u0637")})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:r.lastLogin?new Date(r.lastLogin).toLocaleDateString("ar-SA"):e("never","\u0644\u0645 \u064a\u062f\u062e\u0644")}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{onClick:()=>{u(r),b(!0)},className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300",title:e("editUser","\u062a\u0639\u062f\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"),children:(0,d.jsx)("i",{className:"fas fa-edit"})}),(0,d.jsx)("button",{onClick:()=>(async(r,s)=>{try{(await fetch("/api/v1/users/".concat(r,"/toggle-status"),{method:"PATCH",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).ok?(c(a.map(e=>e._id===r?(0,t.A)((0,t.A)({},e),{},{isActive:!s}):e)),i.Ay.success(e("userStatusUpdated","\u062a\u0645 \u062a\u062d\u062f\u064a\u062b \u062d\u0627\u0644\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"))):(c(a.map(e=>e._id===r?(0,t.A)((0,t.A)({},e),{},{isActive:!s}):e)),i.Ay.success(e("userStatusUpdated","\u062a\u0645 \u062a\u062d\u062f\u064a\u062b \u062d\u0627\u0644\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645 (\u0648\u0636\u0639 \u0627\u0644\u062a\u062c\u0631\u064a\u0628)")))}catch(l){console.error("Error updating user status:",l),i.Ay.error(e("errorUpdatingUser","\u062e\u0637\u0623 \u0641\u064a \u062a\u062d\u062f\u064a\u062b \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"))}})(r._id,r.isActive),className:"".concat(r.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"," dark:text-opacity-80"),title:r.isActive?e("deactivateUser","\u0625\u0644\u063a\u0627\u0621 \u062a\u0641\u0639\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"):e("activateUser","\u062a\u0641\u0639\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"),children:(0,d.jsx)("i",{className:"fas ".concat(r.isActive?"fa-user-slash":"fa-user-check")})}),(0,d.jsx)("button",{onClick:()=>(async r=>{if(window.confirm(e("confirmDeleteUser","\u0647\u0644 \u0623\u0646\u062a \u0645\u062a\u0623\u0643\u062f \u0645\u0646 \u062d\u0630\u0641 \u0647\u0630\u0627 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u061f")))try{(await fetch("/api/v1/users/".concat(r),{method:"DELETE",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).ok?(c(a.filter(e=>e._id!==r)),i.Ay.success(e("userDeleted","\u062a\u0645 \u062d\u0630\u0641 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"))):(c(a.filter(e=>e._id!==r)),i.Ay.success(e("userDeleted","\u062a\u0645 \u062d\u0630\u0641 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645 (\u0648\u0636\u0639 \u0627\u0644\u062a\u062c\u0631\u064a\u0628)")))}catch(t){console.error("Error deleting user:",t),i.Ay.error(e("errorDeletingUser","\u062e\u0637\u0623 \u0641\u064a \u062d\u0630\u0641 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"))}})(r._id),className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",title:e("deleteUser","\u062d\u0630\u0641 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645"),children:(0,d.jsx)("i",{className:"fas fa-trash"})})]})})]},r._id)})})]})}),0===v.length&&(0,d.jsxs)("div",{className:"p-12 text-center",children:[(0,d.jsx)("i",{className:"fas fa-users text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noUsersFound","\u0644\u0627 \u062a\u0648\u062c\u062f \u0645\u0633\u062a\u062e\u062f\u0645\u0648\u0646")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("noUsersFoundDesc","\u0644\u0645 \u064a\u062a\u0645 \u0627\u0644\u0639\u062b\u0648\u0631 \u0639\u0644\u0649 \u0645\u0633\u062a\u062e\u062f\u0645\u064a\u0646 \u064a\u0637\u0627\u0628\u0642\u0648\u0646 \u0645\u0639\u0627\u064a\u064a\u0631 \u0627\u0644\u0628\u062d\u062b")})]})]}),(0,d.jsx)(o,{isOpen:h,onClose:()=>y(!1),onUserCreated:e=>{c(r=>[...r,e]),y(!1)}}),(0,d.jsx)(n,{isOpen:p,onClose:()=>{b(!1),u(null)},onUserUpdated:e=>{c(r=>r.map(r=>r._id===e._id?e:r)),b(!1),u(null)},user:g})]})}}}]);
//# sourceMappingURL=920.004d0eed.chunk.js.map