"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8629],{8629:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var s=a(5043),r=a(3216),l=a(7921),n=a(579);const i=()=>{const{t:e,isRTL:t}=(0,l.o)(),a=(0,r.Zp)(),[i,d]=(0,s.useState)(""),[o,c]=(0,s.useState)("all"),[m,x]=(0,s.useState)("today"),[p]=(0,s.useState)([{id:1,patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",patientNameEn:"<PERSON>",date:"2024-01-25",time:"10:00",duration:60,type:"Physical Therapy",therapist:"Dr. <PERSON>",location:"Room 1",status:"confirmed",notes:"Regular therapy session"},{id:2,patientName:"\u0641\u0627\u0637\u0645\u0629 \u0639\u0644\u064a \u0627\u0644\u0633\u0627\u0644\u0645",patientNameEn:"Fatima Ali Al-Salem",date:"2024-01-25",time:"11:30",duration:45,type:"Occupational Therapy",therapist:"Dr. Ahmed Al-Mansouri",location:"Room 2",status:"pending",notes:"Assessment session"},{id:3,patientName:"\u0645\u062d\u0645\u062f \u0639\u0628\u062f\u0627\u0644\u0644\u0647 \u0627\u0644\u062e\u0627\u0644\u062f",patientNameEn:"Mohammed Abdullah Al-Khalid",date:"2024-01-25",time:"14:00",duration:30,type:"Speech Therapy",therapist:"Dr. Fatima Al-Zahra",location:"Room 3",status:"completed",notes:"Communication skills session"},{id:4,patientName:"\u0633\u0627\u0631\u0629 \u0623\u062d\u0645\u062f \u0627\u0644\u0645\u0637\u064a\u0631\u064a",patientNameEn:"Sarah Ahmed Al-Mutairi",date:"2024-01-26",time:"09:00",duration:90,type:"Group Therapy",therapist:"Dr. Mohammed Al-Khalid",location:"Gymnasium",status:"confirmed",notes:"Group session for social skills"}]),h=p.filter(e=>{var t;const a=e.patientName.toLowerCase().includes(i.toLowerCase())||(null===(t=e.patientNameEn)||void 0===t?void 0:t.toLowerCase().includes(i.toLowerCase()))||e.therapist.toLowerCase().includes(i.toLowerCase())||e.type.toLowerCase().includes(i.toLowerCase()),s="all"===o||e.status===o,r=(new Date).toISOString().split("T")[0],l=e.date;return a&&s&&("all"===m||"today"===m&&l===r||"upcoming"===m&&l>=r||"past"===m&&l<r)}),u=e=>{switch(e){case"confirmed":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"completed":return"text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400";case"cancelled":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}},g=e=>{switch(e.toLowerCase()){case"physical therapy":return"fas fa-running";case"occupational therapy":return"fas fa-hand-paper";case"speech therapy":return"fas fa-comments";case"group therapy":return"fas fa-users";default:return"fas fa-stethoscope"}};return(0,n.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("appointmentList","Appointment List")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("appointmentListDesc","View and manage all appointments")})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsxs)("button",{onClick:()=>a("/appointments/calendar"),className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-calendar mr-2"}),e("calendarView","Calendar View")]}),(0,n.jsxs)("button",{onClick:()=>a("/appointments/new"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-plus mr-2"}),e("newAppointment","New Appointment")]})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("search","Search")}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("input",{type:"text",value:i,onChange:e=>d(e.target.value),placeholder:e("searchAppointments","Search appointments..."),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),(0,n.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("status","Status")}),(0,n.jsxs)("select",{value:o,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,n.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,n.jsx)("option",{value:"confirmed",children:e("confirmed","Confirmed")}),(0,n.jsx)("option",{value:"pending",children:e("pending","Pending")}),(0,n.jsx)("option",{value:"completed",children:e("completed","Completed")}),(0,n.jsx)("option",{value:"cancelled",children:e("cancelled","Cancelled")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("date","Date")}),(0,n.jsxs)("select",{value:m,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,n.jsx)("option",{value:"all",children:e("allDates","All Dates")}),(0,n.jsx)("option",{value:"today",children:e("today","Today")}),(0,n.jsx)("option",{value:"upcoming",children:e("upcoming","Upcoming")}),(0,n.jsx)("option",{value:"past",children:e("past","Past")})]})]}),(0,n.jsx)("div",{className:"flex items-end",children:(0,n.jsxs)("button",{onClick:()=>{d(""),c("all"),x("today")},className:"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-refresh mr-2"}),e("reset","Reset")]})})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,n.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[e("appointments","Appointments")," (",h.length,")"]})}),(0,n.jsx)("div",{className:"p-6",children:h.length>0?(0,n.jsx)("div",{className:"space-y-4",children:h.map(a=>(0,n.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-md transition-shadow",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center",children:(0,n.jsx)("i",{className:"".concat(g(a.type)," text-blue-600 dark:text-blue-400")})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t?a.patientName:a.patientNameEn}),(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[a.type," \u2022 ",a.therapist]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("span",{className:"px-3 py-1 text-sm font-medium rounded-full ".concat(u(a.status)),children:e(a.status,a.status)}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a.date}),(0,n.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[a.time," (",a.duration," ",e("min","min"),")"]})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("location","Location"),":"]}),(0,n.jsx)("span",{className:"ml-2 text-gray-900 dark:text-white",children:a.location})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("duration","Duration"),":"]}),(0,n.jsxs)("span",{className:"ml-2 text-gray-900 dark:text-white",children:[a.duration," ",e("minutes","minutes")]})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("notes","Notes"),":"]}),(0,n.jsx)("span",{className:"ml-2 text-gray-900 dark:text-white",children:a.notes})]})]}),(0,n.jsxs)("div",{className:"mt-4 flex justify-end space-x-2",children:[(0,n.jsxs)("button",{className:"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-eye mr-1"}),e("view","View")]}),(0,n.jsxs)("button",{className:"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-edit mr-1"}),e("edit","Edit")]}),"pending"===a.status&&(0,n.jsxs)("button",{className:"px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-check mr-1"}),e("confirm","Confirm")]}),(0,n.jsxs)("button",{className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-times mr-1"}),e("cancel","Cancel")]})]})]},a.id))}):(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("i",{className:"fas fa-calendar-times text-4xl text-gray-400 mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noAppointmentsFound","No Appointments Found")}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:e("noAppointmentsDesc","No appointments match your current filters")}),(0,n.jsxs)("button",{onClick:()=>a("/appointments/new"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-plus mr-2"}),e("scheduleAppointment","Schedule Appointment")]})]})})]})]})}}}]);
//# sourceMappingURL=8629.62403b95.chunk.js.map