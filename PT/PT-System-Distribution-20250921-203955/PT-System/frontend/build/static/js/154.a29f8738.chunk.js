"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[154],{154:(e,a,s)=>{s.r(a),s.d(a,{default:()=>o});var t=s(5043),i=s(7921),r=s(6928),n=s(3216),l=s(579);const o=()=>{const{t:e,isRTL:a}=(0,i.o)(),{quickActions:s}=(0,r.c)(),o=(0,n.Zp)(),[m,c]=(0,t.useState)(""),[d,g]=(0,t.useState)("all"),[p,u]=(0,t.useState)(null),x=[{id:"patient-intake",name:"Patient Intake Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u0627\u0633\u062a\u0642\u0628\u0627\u0644 \u0627\u0644\u0645\u0631\u064a\u0636",category:"intake",description:"Comprehensive patient intake form for new patients",descriptionAr:"\u0646\u0645\u0648\u0630\u062c \u0627\u0633\u062a\u0642\u0628\u0627\u0644 \u0634\u0627\u0645\u0644 \u0644\u0644\u0645\u0631\u0636\u0649 \u0627\u0644\u062c\u062f\u062f",fields:15,estimatedTime:"10-15 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/patient-intake",component:"PatientIntakeForm",available:!0,preview:["Patient Information","Medical History","Current Symptoms","Insurance Information","Emergency Contact","Consent Forms"]},{id:"initial-assessment",name:"PT Adult Initial Assessment",nameAr:"\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a \u0627\u0644\u0623\u0648\u0644\u064a \u0644\u0644\u0628\u0627\u0644\u063a\u064a\u0646",category:"assessment",description:"8-page comprehensive initial assessment for adult physical therapy patients",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u0623\u0648\u0644\u064a \u0634\u0627\u0645\u0644 \u0645\u0646 8 \u0635\u0641\u062d\u0627\u062a \u0644\u0645\u0631\u0636\u0649 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a \u0627\u0644\u0628\u0627\u0644\u063a\u064a\u0646",fields:50,estimatedTime:"30-45 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/pt-assessment",component:"PTAssessmentPage",available:!0,preview:["Patient Information & History","Pain Assessment & Body Map","Range of Motion Testing","Muscle Strength Testing","Functional Assessment","Treatment Goals & Plan","ICF Documentation","Equipment Recommendations"]},{id:"pain-assessment",name:"Pain Assessment Scale",nameAr:"\u0645\u0642\u064a\u0627\u0633 \u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0623\u0644\u0645",category:"assessment",description:"Comprehensive pain evaluation and tracking",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u0648\u062a\u062a\u0628\u0639 \u0634\u0627\u0645\u0644 \u0644\u0644\u0623\u0644\u0645",fields:12,estimatedTime:"10-15 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/pain-assessment",component:"PainAssessmentForm",available:!0,preview:["Pain Intensity Scale","Pain Location Mapping","Pain Characteristics","Functional Impact","Aggravating Factors","Treatment Response"]},{id:"discharge-assessment",name:"Outpatient Discharge Assessment",nameAr:"\u062a\u0642\u064a\u064a\u0645 \u062e\u0631\u0648\u062c \u0627\u0644\u0645\u0631\u0636\u0649 \u0627\u0644\u062e\u0627\u0631\u062c\u064a\u064a\u0646",category:"discharge",description:"2-page comprehensive discharge assessment with recommendations",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u062e\u0631\u0648\u062c \u0634\u0627\u0645\u0644 \u0645\u0646 \u0635\u0641\u062d\u062a\u064a\u0646 \u0645\u0639 \u0627\u0644\u062a\u0648\u0635\u064a\u0627\u062a",fields:35,estimatedTime:"20-30 minutes",language:"bilingual",specialNeeds:!1,route:"/forms/discharge-assessment",component:"DischargeAssessment",available:!0,preview:["Patient Information & Visits","Evaluation Addendum","Treatment Summary","Progress Assessment","Discharge Reasons","Follow-up Recommendations","Therapist Signatures"]},{id:"daily-progress",name:"Daily Progress Note",nameAr:"\u0645\u0644\u0627\u062d\u0638\u0629 \u0627\u0644\u062a\u0642\u062f\u0645 \u0627\u0644\u064a\u0648\u0645\u064a",category:"progress",description:"Daily session progress notes with pain assessment",descriptionAr:"\u0645\u0644\u0627\u062d\u0638\u0627\u062a \u0627\u0644\u062a\u0642\u062f\u0645 \u0627\u0644\u064a\u0648\u0645\u064a \u0644\u0644\u062c\u0644\u0633\u0629 \u0645\u0639 \u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0623\u0644\u0645",fields:20,estimatedTime:"10-15 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/daily-progress",component:"DailyProgressPage",available:!0,preview:["Session Information","Pain Assessment & Body Map","Treatment Provided","Patient Response","Progress Notes","Next Session Plan"]},{id:"reassessment",name:"PT Reassessment Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u062a\u0642\u064a\u064a\u0645 \u0644\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a",category:"assessment",description:"Periodic reassessment to track patient progress",descriptionAr:"\u0625\u0639\u0627\u062f\u0629 \u062a\u0642\u064a\u064a\u0645 \u062f\u0648\u0631\u064a\u0629 \u0644\u062a\u062a\u0628\u0639 \u062a\u0642\u062f\u0645 \u0627\u0644\u0645\u0631\u064a\u0636",fields:30,estimatedTime:"20-25 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/reassessment",component:"ReassessmentPage",available:!0,preview:["Current Status Review","Goal Achievement","Functional Changes","Pain Level Changes","Treatment Modifications","Updated Goals"]},{id:"pain-assessment",name:"Pain Assessment Scale",nameAr:"\u0645\u0642\u064a\u0627\u0633 \u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0623\u0644\u0645",category:"assessment",description:"Detailed pain assessment and tracking form",descriptionAr:"\u0646\u0645\u0648\u0630\u062c \u062a\u0642\u064a\u064a\u0645 \u0648\u062a\u062a\u0628\u0639 \u0627\u0644\u0623\u0644\u0645 \u0627\u0644\u0645\u0641\u0635\u0644",fields:10,estimatedTime:"5-10 minutes",language:"bilingual",specialNeeds:!0,preview:["Pain Location","Pain Intensity","Pain Quality","Aggravating Factors","Relieving Factors","Impact on Function"]},{id:"family-consultation",name:"Family Consultation Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u0627\u0633\u062a\u0634\u0627\u0631\u0629 \u0627\u0644\u0623\u0633\u0631\u0629",category:"consultation",description:"Family meeting and consultation documentation",descriptionAr:"\u062a\u0648\u062b\u064a\u0642 \u0627\u062c\u062a\u0645\u0627\u0639 \u0648\u0627\u0633\u062a\u0634\u0627\u0631\u0629 \u0627\u0644\u0623\u0633\u0631\u0629",fields:14,estimatedTime:"10-15 minutes",language:"bilingual",specialNeeds:!0,preview:["Family Members Present","Concerns Discussed","Treatment Progress","Home Program Review","Questions & Answers","Next Steps"]},{id:"equipment-assessment",name:"Assistive Equipment Assessment",nameAr:"\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0645\u0639\u062f\u0627\u062a \u0627\u0644\u0645\u0633\u0627\u0639\u062f\u0629",category:"assessment",description:"Assessment for assistive devices and equipment needs",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u0627\u062d\u062a\u064a\u0627\u062c\u0627\u062a \u0627\u0644\u0623\u062c\u0647\u0632\u0629 \u0648\u0627\u0644\u0645\u0639\u062f\u0627\u062a \u0627\u0644\u0645\u0633\u0627\u0639\u062f\u0629",fields:16,estimatedTime:"15-20 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/equipment-assessment",component:"EquipmentAssessmentForm",preview:["Current Equipment","Mobility Assessment","Safety Evaluation","Equipment Recommendations","Training Needs","Funding Options"]},{id:"home-exercise-program",name:"Home Exercise Program (HEP)",nameAr:"\u0628\u0631\u0646\u0627\u0645\u062c \u0627\u0644\u062a\u0645\u0627\u0631\u064a\u0646 \u0627\u0644\u0645\u0646\u0632\u0644\u064a\u0629",category:"treatment",description:"Customized home exercise program with instructions",descriptionAr:"\u0628\u0631\u0646\u0627\u0645\u062c \u062a\u0645\u0627\u0631\u064a\u0646 \u0645\u0646\u0632\u0644\u064a\u0629 \u0645\u062e\u0635\u0635 \u0645\u0639 \u0627\u0644\u062a\u0639\u0644\u064a\u0645\u0627\u062a",fields:20,estimatedTime:"15-25 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/home-exercise-program",component:"HomeExerciseProgramForm",available:!0,preview:["Exercise Selection","Frequency & Duration","Visual Instructions","Safety Precautions","Progress Tracking","Modification Guidelines"]},{id:"treatment-plan",name:"Treatment Plan & Goals",nameAr:"\u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c \u0648\u0627\u0644\u0623\u0647\u062f\u0627\u0641",category:"treatment",description:"Comprehensive treatment planning with SMART goals",descriptionAr:"\u062a\u062e\u0637\u064a\u0637 \u0639\u0644\u0627\u062c \u0634\u0627\u0645\u0644 \u0645\u0639 \u0623\u0647\u062f\u0627\u0641 \u0630\u0643\u064a\u0629",fields:25,estimatedTime:"20-30 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/treatment-plan",component:"TreatmentPlanForm",available:!0,preview:["Treatment Objectives","SMART Goals","Intervention Strategies","Timeline & Milestones","Outcome Measures","Review Schedule"]},{id:"initial-plan-of-care",name:"Initial Plan of Care Physical Therapy",nameAr:"\u062e\u0637\u0629 \u0627\u0644\u0631\u0639\u0627\u064a\u0629 \u0627\u0644\u0623\u0648\u0644\u064a\u0629 \u0644\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a",category:"treatment",description:"2-page comprehensive treatment planning document with problems, goals, and interventions",descriptionAr:"\u0648\u062b\u064a\u0642\u0629 \u062a\u062e\u0637\u064a\u0637 \u0639\u0644\u0627\u062c \u0634\u0627\u0645\u0644\u0629 \u0645\u0646 \u0635\u0641\u062d\u062a\u064a\u0646 \u0645\u0639 \u0627\u0644\u0645\u0634\u0627\u0643\u0644 \u0648\u0627\u0644\u0623\u0647\u062f\u0627\u0641 \u0648\u0627\u0644\u062a\u062f\u062e\u0644\u0627\u062a",fields:40,estimatedTime:"25-35 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/initial-plan-of-care",component:"InitialPlanOfCareForm",available:!0,preview:["Patient Information & Diagnosis","Functional Problems Assessment","Short & Long Term Goals","Comprehensive Treatment Plan","Therapist & Physician Signatures","Professional Documentation"]},{id:"patient-family-education",name:"Patient and Family Education Form",nameAr:"\u0646\u0645\u0648\u0630\u062c \u062a\u0639\u0644\u064a\u0645 \u0627\u0644\u0645\u0631\u064a\u0636 \u0648\u0627\u0644\u0623\u0633\u0631\u0629",category:"education",description:"Comprehensive patient and family education assessment and planning for physical therapist",descriptionAr:"\u062a\u0642\u064a\u064a\u0645 \u0648\u062a\u062e\u0637\u064a\u0637 \u0634\u0627\u0645\u0644 \u0644\u062a\u0639\u0644\u064a\u0645 \u0627\u0644\u0645\u0631\u064a\u0636 \u0648\u0627\u0644\u0623\u0633\u0631\u0629 \u0644\u0623\u062e\u0635\u0627\u0626\u064a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a",fields:35,estimatedTime:"20-30 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/patient-family-education",component:"PatientFamilyEducationForm",available:!0,preview:["Assessment Type Selection","Learning Preferences & Barriers","Self-Care Capability Assessment","Educational Needs Identification","Teaching Methods & Tools","Evaluation & Follow-up Planning"]},{id:"follow-up-plan",name:"Follow Up Plan for Doctor",nameAr:"\u062e\u0637\u0629 \u0627\u0644\u0645\u062a\u0627\u0628\u0639\u0629 \u0644\u0644\u0637\u0628\u064a\u0628",category:"treatment",description:"2-page follow-up planning document with repeatable entries for medical review",descriptionAr:"\u0648\u062b\u064a\u0642\u0629 \u062a\u062e\u0637\u064a\u0637 \u0645\u062a\u0627\u0628\u0639\u0629 \u0645\u0646 \u0635\u0641\u062d\u062a\u064a\u0646 \u0645\u0639 \u0625\u062f\u062e\u0627\u0644\u0627\u062a \u0642\u0627\u0628\u0644\u0629 \u0644\u0644\u062a\u0643\u0631\u0627\u0631 \u0644\u0644\u0645\u0631\u0627\u062c\u0639\u0629 \u0627\u0644\u0637\u0628\u064a\u0629",fields:25,estimatedTime:"15-25 minutes",language:"bilingual",specialNeeds:!0,route:"/forms/follow-up-plan",component:"FollowUpPlanForm",available:!0,preview:["Patient Information","Repeatable Follow-up Entries (up to 4)","Current Situation Assessment","Treatment Proposals","Next Appointment Scheduling","Professional Medical Documentation"]}],h=[{id:"all",label:e("allCategories","All Categories"),count:x.length},{id:"intake",label:e("intake","Intake"),count:x.filter(e=>"intake"===e.category).length},{id:"assessment",label:e("assessment","Assessment"),count:x.filter(e=>"assessment"===e.category).length},{id:"special-needs",label:e("specialNeeds","Special Needs"),count:x.filter(e=>"special-needs"===e.category).length},{id:"progress",label:e("progress","Progress"),count:x.filter(e=>"progress"===e.category).length},{id:"discharge",label:e("discharge","Discharge"),count:x.filter(e=>"discharge"===e.category).length},{id:"consultation",label:e("consultation","Consultation"),count:x.filter(e=>"consultation"===e.category).length},{id:"treatment",label:e("treatment","Treatment"),count:x.filter(e=>"treatment"===e.category).length},{id:"education",label:e("education","Education"),count:x.filter(e=>"education"===e.category).length},{id:"administrative",label:e("administrative","Administrative"),count:x.filter(e=>"administrative"===e.category).length}],y=x.filter(e=>{const a=e.name.toLowerCase().includes(m.toLowerCase())||e.nameAr.includes(m)||e.description.toLowerCase().includes(m.toLowerCase()),s="all"===d||e.category===d;return a&&s}),b=a=>{a.route&&a.available?o(a.route):(alert(e("formNotAvailable","".concat(a.name," is not yet available. Please use the form builder to create a custom form."))),s.createForm())};return(0,l.jsxs)("div",{className:"p-6 ".concat(a?"font-arabic":"font-english"),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("formTemplates","Form Templates")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("formTemplatesDesc","Choose from pre-built healthcare forms or create your own")})]}),(0,l.jsxs)("button",{onClick:()=>s.createForm(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-plus mr-2"}),e("createCustomForm","Create Custom Form")]})]}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{className:"lg:col-span-2",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("searchTemplates","Search Templates")}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{type:"text",value:m,onChange:e=>c(e.target.value),placeholder:e("searchPlaceholder","Search by name, description, or category..."),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,l.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("category","Category")}),(0,l.jsx)("select",{value:d,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:h.map(e=>(0,l.jsxs)("option",{value:e.id,children:[e.label," (",e.count,")"]},e.id))})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsxs)("button",{className:"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-filter mr-2"}),e("advancedFilters","Advanced Filters")]})})]})}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(s=>(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 overflow-hidden hover:shadow-lg transition-shadow",children:[(0,l.jsxs)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:[(0,l.jsx)("div",{className:"flex items-start justify-between mb-3",children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:a?s.nameAr:s.name}),(0,l.jsxs)("div",{className:"flex flex-col space-y-1",children:[s.available?(0,l.jsxs)("span",{className:"px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-xs rounded-full",children:[(0,l.jsx)("i",{className:"fas fa-check mr-1"}),e("available","Available")]}):(0,l.jsxs)("span",{className:"px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 text-xs rounded-full",children:[(0,l.jsx)("i",{className:"fas fa-wrench mr-1"}),e("inDevelopment","In Development")]}),s.specialNeeds&&(0,l.jsxs)("span",{className:"px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 text-xs rounded-full",children:[(0,l.jsx)("i",{className:"fas fa-puzzle-piece mr-1"}),e("specialNeeds","Special Needs")]})]})]})}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:a?s.descriptionAr:s.description}),(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 dark:text-gray-400",children:[(0,l.jsxs)("span",{children:[(0,l.jsx)("i",{className:"fas fa-list mr-1"}),s.fields," ",e("fields","fields")]}),(0,l.jsxs)("span",{children:[(0,l.jsx)("i",{className:"fas fa-clock mr-1"}),s.estimatedTime]})]})]}),(0,l.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-700",children:[(0,l.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("includes","Includes"),":"]}),(0,l.jsxs)("ul",{className:"space-y-1",children:[s.preview.slice(0,3).map((e,a)=>(0,l.jsxs)("li",{className:"text-xs text-gray-600 dark:text-gray-400 flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-check text-green-500 mr-2 text-xs"}),e]},a)),s.preview.length>3&&(0,l.jsxs)("li",{className:"text-xs text-gray-500 dark:text-gray-500",children:["+",s.preview.length-3," ",e("moreItems","more items")]})]})]}),(0,l.jsxs)("div",{className:"p-4 flex space-x-2",children:[(0,l.jsx)("button",{onClick:()=>b(s),className:"flex-1 px-4 py-2 rounded-lg transition-colors text-sm ".concat(s.available?"bg-blue-600 text-white hover:bg-blue-700":"bg-gray-400 text-white hover:bg-gray-500"),children:s.available?e("useTemplate","Use Template"):e("createCustom","Create Custom")}),(0,l.jsx)("button",{onClick:()=>(e=>{u(e)})(s),className:"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm",children:(0,l.jsx)("i",{className:"fas fa-eye"})})]})]},s.id))}),0===y.length&&(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("i",{className:"fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noTemplatesFound","No templates found")}),(0,l.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:e("tryDifferentSearch","Try adjusting your search or filters")}),(0,l.jsx)("button",{onClick:()=>{c(""),g("all")},className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e("clearFilters","Clear Filters")})]}),p&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,l.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:a?p.nameAr:p.name}),(0,l.jsx)("button",{onClick:()=>u(null),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,l.jsx)("i",{className:"fas fa-times text-xl"})})]})}),(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:a?p.descriptionAr:p.description}),(0,l.jsxs)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:[e("formSections","Form Sections"),":"]}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:p.preview.map((e,a)=>(0,l.jsxs)("div",{className:"flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,l.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-3"}),(0,l.jsx)("span",{className:"text-gray-900 dark:text-white",children:e})]},a))}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)("button",{onClick:()=>{b(p),u(null)},className:"flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e("useThisTemplate","Use This Template")}),(0,l.jsx)("button",{onClick:()=>u(null),className:"px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:e("close","Close")})]})]})]})})]})}}}]);
//# sourceMappingURL=154.a29f8738.chunk.js.map