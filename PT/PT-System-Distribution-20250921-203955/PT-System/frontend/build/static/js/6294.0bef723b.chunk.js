"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6294],{6294:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var a=t(2555),s=t(5043),i=t(7921),d=t(4528),o=t(3216),l=t(579);const n=()=>{const{t:e,isRTL:r}=(0,i.o)(),{user:t}=(0,d.A)(),{patientId:n}=(0,o.g)(),c=(0,o.Zp)(),[m,g]=(0,s.useState)({patientName:"",patientId:n||"",programDate:(new Date).toISOString().split("T")[0],therapistName:(null===t||void 0===t?void 0:t.name)||"",programTitle:"",programDuration:"4",frequency:"daily",totalSessions:"",exercises:[],safetyPrecautions:"",contraindications:"",warningSignsToStop:"",progressMeasures:[],reviewDate:"",modificationCriteria:"",patientInstructions:"",equipmentNeeded:"",environmentSetup:"",followUpSchedule:"",contactInformation:"",emergencyContact:"",therapistSignature:"",patientAcknowledgment:!1,caregiverAcknowledgment:!1}),[x,y]=(0,s.useState)(!1),[u,p]=(0,s.useState)({}),[h,b]=(0,s.useState)([]),f=[{value:"daily",label:e("daily","Daily")},{value:"twice-daily",label:e("twiceDaily","Twice Daily")},{value:"every-other-day",label:e("everyOtherDay","Every Other Day")},{value:"three-times-week",label:e("threeTimesWeek","3 Times per Week")},{value:"custom",label:e("custom","Custom Schedule")}];(0,s.useEffect)(()=>{n&&k(),v()},[n]);const k=async()=>{try{y(!0);const e=await fetch("/api/patients/".concat(n));if(e.ok){const r=await e.json();g(e=>(0,a.A)((0,a.A)({},e),{},{patientName:r.name||"",patientId:r._id||n}))}}catch(e){console.error("Error loading patient data:",e)}finally{y(!1)}},v=async()=>{try{const e=await fetch("/api/v1/exercise-library");if(e.ok){const r=await e.json();b(r)}}catch(e){console.error("Error loading exercise library:",e),b([{id:1,name:"Ankle Pumps",category:"Range of Motion",description:"Point and flex your foot",instructions:"Sit or lie down. Point your toes away from you, then flex them back toward you.",repetitions:"10-15",sets:"2-3",frequency:"Daily",image:"/images/exercises/ankle-pumps.jpg"},{id:2,name:"Shoulder Rolls",category:"Range of Motion",description:"Roll shoulders forward and backward",instructions:"Sit or stand with arms at your sides. Roll shoulders forward in a circular motion, then reverse.",repetitions:"10",sets:"2",frequency:"Daily",image:"/images/exercises/shoulder-rolls.jpg"}])}},w=(e,r)=>{g(t=>(0,a.A)((0,a.A)({},t),{},{[e]:r})),u[e]&&p(r=>(0,a.A)((0,a.A)({},r),{},{[e]:null}))},j=(e,r,t)=>{g(s=>(0,a.A)((0,a.A)({},s),{},{exercises:s.exercises.map(s=>s.id===e?(0,a.A)((0,a.A)({},s),{},{[r]:t}):s)}))};return x?(0,l.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,l.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("homeExerciseProgram","Home Exercise Program (HEP)")}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e("hepDescription","Customized exercise program for home practice")})]}),(0,l.jsx)("div",{className:"flex items-center space-x-2",children:(0,l.jsxs)("span",{className:"px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm rounded-full",children:[(0,l.jsx)("i",{className:"fas fa-dumbbell mr-1"}),e("exerciseProgram","Exercise Program")]})})]})})}),(0,l.jsxs)("form",{onSubmit:async r=>{if(r.preventDefault(),(()=>{const r={};return m.patientName.trim()||(r.patientName=e("patientNameRequired","Patient name is required")),m.therapistName.trim()||(r.therapistName=e("therapistNameRequired","Therapist name is required")),m.programTitle.trim()||(r.programTitle=e("programTitleRequired","Program title is required")),0===m.exercises.length&&(r.exercises=e("exercisesRequired","At least one exercise is required")),p(r),0===Object.keys(r).length})())try{y(!0);const r=(0,a.A)((0,a.A)({},m),{},{submittedBy:t.id,submittedAt:(new Date).toISOString()});if(!(await fetch("/api/v1/home-exercise-programs/public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok)throw new Error("Failed to save home exercise program");alert(e("hepSaved","Home Exercise Program saved successfully!")),c(n?"/patients/".concat(n):"/patients")}catch(s){console.error("Error saving home exercise program:",s),alert(e("errorSaving","Error saving home exercise program. Please try again."))}finally{y(!1)}},className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("programInformation","Program Information")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("patientName","Patient Name")," *"]}),(0,l.jsx)("input",{type:"text",value:m.patientName,onChange:e=>w("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.patientName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.patientName&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.patientName})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("programTitle","Program Title")," *"]}),(0,l.jsx)("input",{type:"text",value:m.programTitle,onChange:e=>w("programTitle",e.target.value),placeholder:e("programTitlePlaceholder","e.g., Lower Back Strengthening Program"),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.programTitle?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.programTitle&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.programTitle})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("programDuration","Program Duration (weeks)")}),(0,l.jsx)("input",{type:"number",value:m.programDuration,onChange:e=>w("programDuration",e.target.value),min:"1",max:"52",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("frequency","Frequency")}),(0,l.jsx)("select",{value:m.frequency,onChange:e=>w("frequency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:f.map(e=>(0,l.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("exerciseSelection","Exercise Selection")}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("exerciseLibrary","Exercise Library")}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:h.map(e=>(0,l.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,l.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,l.jsx)("button",{type:"button",onClick:()=>(e=>{const r=(0,a.A)((0,a.A)({},e),{},{id:Date.now(),customInstructions:"",modifiedRepetitions:e.repetitions,modifiedSets:e.sets,modifiedFrequency:e.frequency,notes:""});g(e=>(0,a.A)((0,a.A)({},e),{},{exercises:[...e.exercises,r]}))})(e),className:"text-blue-600 hover:text-blue-800 dark:text-blue-400",children:(0,l.jsx)("i",{className:"fas fa-plus"})})]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:e.description}),(0,l.jsx)("div",{className:"text-xs text-gray-500",children:(0,l.jsx)("span",{className:"bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded",children:e.category})})]},e.id))})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:[e("selectedExercises","Selected Exercises")," (",m.exercises.length,")"]}),u.exercises&&(0,l.jsx)("p",{className:"text-red-500 text-sm mb-4",children:u.exercises}),0===m.exercises.length?(0,l.jsx)("p",{className:"text-gray-500 dark:text-gray-400 text-center py-8",children:e("noExercisesSelected","No exercises selected. Choose from the library above.")}):(0,l.jsx)("div",{className:"space-y-4",children:m.exercises.map((r,t)=>(0,l.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white",children:[t+1,". ",r.name]}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r.description})]}),(0,l.jsx)("button",{type:"button",onClick:()=>{return e=r.id,void g(r=>(0,a.A)((0,a.A)({},r),{},{exercises:r.exercises.filter(r=>r.id!==e)}));var e},className:"text-red-600 hover:text-red-800 dark:text-red-400",children:(0,l.jsx)("i",{className:"fas fa-trash"})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("repetitions","Repetitions")}),(0,l.jsx)("input",{type:"text",value:r.modifiedRepetitions,onChange:e=>j(r.id,"modifiedRepetitions",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("sets","Sets")}),(0,l.jsx)("input",{type:"text",value:r.modifiedSets,onChange:e=>j(r.id,"modifiedSets",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("frequency","Frequency")}),(0,l.jsx)("input",{type:"text",value:r.modifiedFrequency,onChange:e=>j(r.id,"modifiedFrequency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("specialInstructions","Special Instructions")}),(0,l.jsx)("textarea",{value:r.customInstructions,onChange:e=>j(r.id,"customInstructions",e.target.value),rows:"2",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("customInstructionsPlaceholder","Any modifications or special notes for this exercise...")})]})]},r.id))})]})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("button",{type:"button",onClick:()=>c(-1),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700",children:e("cancel","Cancel")}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsxs)("button",{type:"button",onClick:async()=>{try{y(!0);const r=(0,a.A)((0,a.A)({},m),{},{generatedAt:(new Date).toISOString(),generatedBy:t.name||t.email,patientId:n}),s=await fetch("/api/v1/home-exercise-programs/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(r)});if(!s.ok)throw new Error("HTTP error! status: ".concat(s.status));{const r=await s.blob(),t=window.URL.createObjectURL(r),a=document.createElement("a");a.href=t,a.download="home-exercise-program-".concat(m.patientName.replace(/\s+/g,"-"),"-").concat(m.programDate,".pdf"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(r){console.error("Error generating PDF:",r),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{y(!1)}},disabled:x,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-file-pdf mr-2"}),x?e("generating","Generating..."):e("generatePDF","Generate PDF")]}),(0,l.jsx)("button",{type:"submit",disabled:x,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:x?e("saving","Saving..."):e("saveHEP","Save Exercise Program")})]})]})]})]})}}}]);
//# sourceMappingURL=6294.0bef723b.chunk.js.map