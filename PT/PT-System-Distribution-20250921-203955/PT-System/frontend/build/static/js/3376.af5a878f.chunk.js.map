{"version": 3, "file": "static/js/3376.af5a878f.chunk.js", "mappings": "2OAIA,MAwZA,EAxZ0BA,IAAoB,IAAnB,UAAEC,GAAWD,EACtC,MAAOE,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,OACtCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAUC,IAAeJ,EAAAA,EAAAA,UAAS,CACvCK,WAAY,GACZC,SAAU,OACVC,WAAY,MAEPC,EAAcC,IAAmBT,EAAAA,EAAAA,WAAS,IAC3C,EAAEU,IAAMC,EAAAA,EAAAA,OAEdC,EAAAA,EAAAA,WAAU,KACJf,GACFgB,KAED,CAAChB,IAEJ,MAAMgB,EAAmBC,UACvBZ,GAAW,GACX,IACE,MAAMa,QAAiBC,MAAM,yBAADC,OAA0BpB,GAAa,CACjEqB,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAaP,EAASQ,OAC5BxB,EAAcuB,EAAKA,KACrB,KAAO,IAAwB,MAApBP,EAASS,OAIlB,MAAM,IAAIC,MAAM,gCAFhB1B,EAAc,KAGhB,CACF,CAAE,MAAO2B,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CE,EAAAA,GAAMF,MAAM,2CACd,CAAC,QACCxB,GAAW,EACb,GAoEI2B,EAAkBL,IACtB,OAAQA,GACN,IAAK,WACL,IAAK,SACH,MAAO,uEACT,IAAK,UACH,MAAO,2EACT,IAAK,WACL,IAAK,UACH,MAAO,+DACT,QACE,MAAO,qEAIb,OAAIvB,GAEA6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uCAAsCC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kEACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDtB,EAAE,oBAAqB,gCAO9BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4DACZrB,EAAE,oBAAqB,yBAGzBZ,IACCgC,EAAAA,EAAAA,MAAA,UACEI,QArGgBpB,UACxB,IACE,MAAMC,QAAiBC,MAAM,8BAADC,OAA+BpB,GAAa,CACtEqB,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIL,EAASM,GAQN,CACL,MAAMc,QAAkBpB,EAASQ,OACjC,MAAM,IAAIE,MAAMU,EAAUC,SAAW,+BACvC,CAXiB,CACf,MAAMd,QAAaP,EAASQ,OAC5BK,EAAAA,GAAMS,QAAQ,qCACdtC,EAAcuC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbD,GAAI,IACPE,kBAAmBlB,EAAKA,KAAKkB,kBAC7BC,oBAAqBnB,EAAKA,KAAKmB,sBAEnC,CAIF,CAAE,MAAOf,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CE,EAAAA,GAAMF,MAAMA,EAAMU,SAAW,+BAC/B,GA8EQL,UAAU,qEAAoEC,SAAA,EAE9EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,oBAAqB,4BAK5BZ,GAcAgC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,oBAAqB,yBAE1BuB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,yCAAAd,OAA2CY,EAAe/B,EAAW0C,oBAAqBR,SACtGtB,EAAEZ,EAAW0C,kBAAmB1C,EAAW0C,qBAE7C1C,EAAW4C,cACVZ,EAAAA,EAAAA,MAAA,KAAGC,UAAU,gDAA+CC,SAAA,CACzDtB,EAAE,cAAe,gBAAgB,KAAG,IAAIiC,KAAK7C,EAAW4C,aAAaE,4BAK5Ed,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,oBAAqB,yBAE1BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBC,SAC/BlC,EAAW+C,sBACVf,EAAAA,EAAAA,MAAAgB,EAAAA,SAAA,CAAAd,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qCAAoCC,SACjDtB,EAAE,SAAU,gBAIjBoB,EAAAA,EAAAA,MAAAgB,EAAAA,SAAA,CAAAd,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iCAAgCC,SAC7CtB,EAAE,WAAY,yBAS1BZ,EAAWiD,gBACVjB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,uBAAwB,4BAE7BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,YAAa,cAAc,QAEhCuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDlC,EAAWiD,cAAcC,gBAG9BlB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,mBAAoB,qBAAqB,QAE9CuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDlC,EAAWiD,cAAcE,uBAG9BnB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,eAAgB,iBAAiB,QAEtCuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDlC,EAAWiD,cAAcG,mBAG9BpB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,cAAe,gBAAgB,QAEpCoB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wCAAuCC,SAAA,CACpDlC,EAAWiD,cAAcI,YAAY,IAAEzC,EAAE,MAAO,oBAQ3DoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtDtB,EAAE,kBAAmB,uBAEtBZ,EAAWsD,WACXtB,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMzB,GAAgB,GAC/BsB,UAAU,uEAAsEC,SAAA,EAEhFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,WAAY,mBAKpBZ,EAAWsD,UACVtB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,aAAc,eAAe,QAElCuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDlC,EAAWsD,SAAS/C,iBAGzByB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,WAAY,aAAa,QAE9BuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDtB,EAAEZ,EAAWsD,SAAS9C,SAAUR,EAAWsD,SAAS9C,gBAGzDwB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,aAAc,UAAU,QAE7BuB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAd,OAAgDY,EAAe/B,EAAWsD,SAAS5B,SAAUQ,SACzGtB,EAAEZ,EAAWsD,SAAS5B,OAAQ1B,EAAWsD,SAAS5B,cAGvDM,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DtB,EAAE,aAAc,eAAe,QAElCuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpD,IAAIW,KAAK7C,EAAWsD,SAASC,YAAYT,8BAKhDX,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDtB,EAAE,eAAgB,oCAMzBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,gBAAiB,qBAEtBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SACjElC,EAAWwD,aAAe,KAE7BrB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtB,EAAE,cAAe,sBAGtBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wDAAuDC,SACnElC,EAAWyD,iBAAmB,SAAM,YAEvCtB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtB,EAAE,mBAAoB,2BAG3BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,SACrElC,EAAW+C,qBAAuB,SAAM,YAE3CZ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtB,EAAE,oBAAqB,qCAvLlCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,sBAAuB,mDAE5BuB,EAAAA,EAAAA,KAAA,UACEC,QAASA,KAAMN,EAAAA,EAAAA,IAAM,wCACrBG,UAAU,6DAA4DC,SAErEtB,EAAE,mBAAoB,0BAuL5BF,IACCyB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6EAA4EC,UACzFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,WAAY,gBAGjBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,aAAc,kBAEnBuB,EAAAA,EAAAA,KAAA,SACEuB,KAAK,OACLC,MAAOtD,EAASE,WAChBqD,SAAWC,GAAMvD,EAAYkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEjC,WAAYsD,EAAEC,OAAOH,SACtE1B,UAAU,kKACV8B,YAAanD,EAAE,kBAAmB,2BAItCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,WAAY,gBAEjBoB,EAAAA,EAAAA,MAAA,UACE2B,MAAOtD,EAASG,SAChBoD,SAAWC,GAAMvD,EAAYkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEhC,SAAUqD,EAAEC,OAAOH,SACpE1B,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQwB,MAAM,OAAMzB,SAAEtB,EAAE,OAAQ,WAChCuB,EAAAA,EAAAA,KAAA,UAAQwB,MAAM,QAAOzB,SAAEtB,EAAE,QAAS,YAClCuB,EAAAA,EAAAA,KAAA,UAAQwB,MAAM,YAAWzB,SAAEtB,EAAE,YAAa,gBAC1CuB,EAAAA,EAAAA,KAAA,UAAQwB,MAAM,OAAMzB,SAAEtB,EAAE,OAAQ,WAChCuB,EAAAA,EAAAA,KAAA,UAAQwB,MAAM,QAAOzB,SAAEtB,EAAE,QAAS,YAClCuB,EAAAA,EAAAA,KAAA,UAAQwB,MAAM,UAASzB,SAAEtB,EAAE,UAAW,uBAK5CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMzB,GAAgB,GAC/BsB,UAAU,0FAAyFC,SAElGtB,EAAE,SAAU,aAEfuB,EAAAA,EAAAA,KAAA,UACEC,QApUGpB,UACf,GAAKX,EAASE,WAKd,IACE,MAAMU,QAAiBC,MAAM,2BAA4B,CACvD8C,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElB2C,KAAMC,KAAKC,WAAS1B,EAAAA,EAAAA,GAAC,CACnB1C,aACGM,MAIP,IAAIY,EAASM,GASN,CACL,MAAMc,QAAkBpB,EAASQ,OACjC,MAAM,IAAIE,MAAMU,EAAUC,SAAW,sBACvC,CAZiB,CACf,MAAMd,QAAaP,EAASQ,OAC5BK,EAAAA,GAAMS,QAAQ,4BACdtC,EAAcuC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbD,GAAI,IACPc,SAAU9B,EAAKA,KAAK8B,YAEtB3C,GAAgB,GAChBL,EAAY,CAAEC,WAAY,GAAIC,SAAU,OAAQC,WAAY,IAC9D,CAIF,CAAE,MAAOmB,GACPC,QAAQD,MAAM,sBAAuBA,GACrCE,EAAAA,GAAMF,MAAMA,EAAMU,SAAW,sBAC/B,MAjCER,EAAAA,GAAMF,MAAM,6BAmUFK,UAAU,6DAA4DC,SAErEtB,EAAE,WAAY,2BCtC/B,EAxWyBd,IAAoB,IAAnB,UAAEsE,GAAWtE,EACrC,MAAOuE,EAAaC,IAAkBpE,EAAAA,EAAAA,UAAS,OACxCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCqE,EAAYC,IAAiBtE,EAAAA,EAAAA,WAAS,IACvC,EAAEU,IAAMC,EAAAA,EAAAA,OAEdC,EAAAA,EAAAA,WAAU,KACR2D,KACC,IAEH,MAAMA,EAAkBzD,UACtBZ,GAAW,GACX,IACE,MAAMa,QAAiBC,MAAM,2BAA4B,CACvDE,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIL,EAASM,GAIX,MAAM,IAAII,MAAM,+BAJD,CACf,MAAMH,QAAaP,EAASQ,OAC5B6C,EAAe9C,EAAKA,KACtB,CAGF,CAAE,MAAOI,GACPC,QAAQD,MAAM,8BAA+BA,GAC7CE,EAAAA,GAAMF,MAAM,0CACd,CAAC,QACCxB,GAAW,EACb,GAwEI2B,EAAkBL,IACtB,OAAQA,GACN,IAAK,aACL,IAAK,WACL,IAAK,SACH,MAAO,uEACT,IAAK,UACL,IAAK,YACH,MAAO,2EACT,IAAK,WACL,IAAK,YACL,IAAK,YACH,MAAO,+DACT,QACE,MAAO,qEAWb,GAAIvB,EACF,OACE6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uCAAsCC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kEACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDtB,EAAE,mBAAoB,8BAM/B,IAAKyD,EACH,OACElC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,qBAAsB,uCAE3BuB,EAAAA,EAAAA,KAAA,UACEC,QAASA,KAAMN,EAAAA,EAAAA,IAAM,uCACrBG,UAAU,6DAA4DC,SAErEtB,EAAE,mBAAoB,4BAOjC,MAAM8D,GArCsBC,EAqCUN,EAAYO,oBApC9B,GAAW,CAAEC,MAAO,YAAaC,MAAO,iBAAkBC,KAAM,uBAC9EJ,GAAc,GAAW,CAAEE,MAAO,OAAQC,MAAO,gBAAiBC,KAAM,sBACxEJ,GAAc,GAAW,CAAEE,MAAO,OAAQC,MAAO,kBAAmBC,KAAM,+BACvE,CAAEF,MAAO,OAAQC,MAAO,eAAgBC,KAAM,uBAJ3BJ,MAuC5B,OACE3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DACZrB,EAAE,mBAAoB,yBAGzBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,CAC5BkC,IACCjC,EAAAA,EAAAA,KAAA,UACEC,QAzIiBpB,UAC3B,GAAKoD,EAAL,CAKAI,GAAc,GACd,IACE,MAAMvD,QAAiBC,MAAM,+BAAgC,CAC3D8C,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElB2C,KAAMC,KAAKC,UAAU,CAAEC,gBAGzB,IAAInD,EAASM,GASN,CACL,MAAMc,QAAkBpB,EAASQ,OACjC,MAAM,IAAIE,MAAMU,EAAUC,SAAW,oCACvC,CAZiB,CACf,MAAMd,QAAaP,EAASQ,OAC5BK,EAAAA,GAAMS,QAAQ,2CAGdT,EAAAA,GAAMS,QAAQ,yBAADpB,OAA0BK,EAAKA,KAAKwD,qBAGjDP,GACF,CAIF,CAAE,MAAO7C,GACPC,QAAQD,MAAM,6BAA8BA,GAC5CE,EAAAA,GAAMF,MAAMA,EAAMU,SAAW,oCAC/B,CAAC,QACCkC,GAAc,EAChB,CA/BA,MAFE1C,EAAAA,GAAMF,MAAM,wBAwIJqD,SAAUV,EACVtC,UAAU,yFAAwFC,SAEjGqC,GACCvC,EAAAA,EAAAA,MAAAgB,EAAAA,SAAA,CAAAd,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gFACdrB,EAAE,aAAc,qBAGnBoB,EAAAA,EAAAA,MAAAgB,EAAAA,SAAA,CAAAd,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBACZrB,EAAE,gBAAiB,yBAM5BoB,EAAAA,EAAAA,MAAA,UACEI,QAtHgBpB,UACxB,IACE,MAAMC,QAAiBC,MAAM,gCAAiC,CAC5D8C,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElB2C,KAAMC,KAAKC,UAAU,CACnBe,WAAY,UACZC,OAAQ,CACNC,MAAM,IAAIvC,MAAOwC,cACjBC,OAAO,IAAIzC,MAAO0C,WAAa,OAKrC,IAAItE,EAASM,GAIN,CACL,MAAMc,QAAkBpB,EAASQ,OACjC,MAAM,IAAIE,MAAMU,EAAUC,SAAW,gCACvC,OANqBrB,EAASQ,OAC5BK,EAAAA,GAAMS,QAAQ,qCACdkC,GAKJ,CAAE,MAAO7C,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CE,EAAAA,GAAMF,MAAMA,EAAMU,SAAW,gCAC/B,GA2FQL,UAAU,uEAAsEC,SAAA,EAEhFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZrB,EAAE,iBAAkB,6BAK3BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,qBAAsB,0BAE3BuB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,yCAAAd,OAA2CY,EAAesC,EAAYmB,qBAAsBtD,SACxGtB,EAAEyD,EAAYmB,mBAAoBnB,EAAYmB,0BAInDxD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,oBAAqB,yBAE1BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAmBC,SAC/BmC,EAAYoB,kBACXzD,EAAAA,EAAAA,MAAAgB,EAAAA,SAAA,CAAAd,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qCAAoCC,SACjDtB,EAAE,QAAS,eAIhBoB,EAAAA,EAAAA,MAAAgB,EAAAA,SAAA,CAAAd,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iCAAgCC,SAC7CtB,EAAE,UAAW,yBAS1BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,qBAAsB,0BAG3BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAd,OAAKuD,EAAWK,KAAI,KAAA5D,OAAIuD,EAAWI,MAAK,YACpD9C,EAAAA,EAAAA,MAAA,QAAMC,UAAS,eAAAd,OAAiBuD,EAAWI,OAAQ5C,SAAA,CAChDmC,EAAYO,kBAAkB,KAAGhE,EAAE,YAAa,iBAEnDoB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gDAA+CC,SAAA,CAAC,IAC5DtB,EAAE8D,EAAWG,MAAOH,EAAWG,OAAO,WAI5C1C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,gDAAAd,OACPkD,EAAYO,mBAAqB,GAAK,eACtCP,EAAYO,mBAAqB,GAAK,cACtCP,EAAYO,mBAAqB,GAAK,gBAAkB,cAE1Dc,MAAO,CAAEC,MAAM,GAADxE,OAAKkD,EAAYO,kBAAiB,cAMtD5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,uBAAwB,4BAG7BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SACjEmC,EAAYuB,0BAEfzD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtB,EAAE,iBAAkB,yBAIzBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wDAAuDC,SACnEmC,EAAYwB,yBAEf1D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtB,EAAE,aAAc,oBAIrBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DmC,EAAYyB,qBAEf3D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtB,EAAE,SAAU,qBAOpByD,EAAY0B,kBAAoB1B,EAAY0B,iBAAiBC,OAAS,IACrEhE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,yBAA0B,+BAG/BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBmC,EAAY0B,iBAAiBE,IAAI,CAACC,EAAOC,KACxCnE,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,0EAAyEC,SAAA,EAClGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,OAAAd,OACO,WAAjB+E,EAAMxE,OAAsB,iCACX,YAAjBwE,EAAMxE,OAAuB,0CAC7B,+BAA8B,YAEhCS,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDtB,EAAEsF,EAAME,UAAWF,EAAME,iBAI9BjE,EAAAA,EAAAA,KAAA,QAAMF,UAAS,yCAAAd,OAA2CY,EAAemE,EAAMxE,SAAUQ,SACtFtB,EAAEsF,EAAMxE,OAAQwE,EAAMxE,YAbjByE,SAsBjB9B,EAAYgC,eACXrE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAC3DtB,EAAE,sBAAuB,4BAE5BoB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,CACpDtB,EAAE,eAAgB,aAAa,KAAG,IAAIiC,KAAKwB,EAAYgC,cAAcC,wBAM5EnE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mEACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oDAAmDC,SAC9DtB,EAAE,kBAAmB,uBAExBuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDtB,EAAE,sBAAuB,kKCqiB1C,EA93B0B2F,KAAO,IAADC,EAAAC,EAC9B,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,OACX,EAAEjG,IAAMC,EAAAA,EAAAA,OACR,MAAEiG,IAAUC,EAAAA,EAAAA,MACZ,KAAEC,IAASC,EAAAA,EAAAA,MAEVC,EAASC,IAAcjH,EAAAA,EAAAA,UAAS,OAChCkH,EAAUC,IAAenH,EAAAA,EAAAA,UAAS,KAClCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCoH,EAAWC,IAAgBrH,EAAAA,EAAAA,UAAS,OACpCsH,EAAeC,IAAoBvH,EAAAA,EAAAA,WAAS,IAC5CwH,EAAkBC,IAAuBzH,EAAAA,EAAAA,WAAS,IAClD0H,EAAWC,IAAgB3H,EAAAA,EAAAA,UAAS,YAE3CY,EAAAA,EAAAA,WAAU,KACRgH,IACAC,KACC,CAACrB,IAEJ,MAAMoB,EAAqB9G,UACzBZ,GAAW,GACX,IAIE,GAFwB,oBAAoB4H,KAAKtB,GAE5B,CACnB,MAAMzF,QAAiBC,MAAM,mBAADC,OAAoBuF,GAAM,CACpDtF,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAaP,EAASQ,OAG5B,OAFA0F,EAAW3F,EAAKA,WAChB6F,EAAY7F,EAAKA,KAAK4F,UAAY,GAEpC,CACE,MAAM,IAAIzF,MAAM,iCAEpB,CAEE,MAAM,IAAIA,MAAM,2BAEpB,CAAE,MAAOC,GACPC,QAAQD,MAAM,yBAA0BA,GAExCC,QAAQoG,IAAI,+BAAgCvB,GAwC5CS,EAvCoB,CAClBe,IAAKxB,EACLyB,cAAe,eACfC,QAAS,CACPC,UAAW,2BACXC,SAAU,8CACVC,WAAY,aACZC,MAAO,gBACPC,MAAO,qBAETC,SAAU,CACR,CACEC,KAAM,2BACNC,YAAa,mCACbC,SAAU,EACVC,UAAW,IACXC,MAAO,KAET,CACEJ,KAAM,mBACNC,YAAa,2BACbC,SAAU,EACVC,UAAW,IACXC,MAAO,MAGXC,SAAU,KACVC,IAAK,CAAEC,KAAM,GAAIC,OAAQ,KACzBC,SAAU,CAAE1F,KAAM,QAASyF,OAAQ,IACnCJ,MAAO,KACPrH,OAAQ,OACR2H,UAAW,aACXC,QAAS,aACTC,MAAO,gDACPC,UAAW,CACTnB,UAAW,YACXC,SAAU,WAIdjB,EAAY,CACV,CACEa,IAAK,IACLiB,OAAQ,IACRnF,OAAQ,OACRyF,KAAM,aACNC,UAAW,UACXC,WAAY,CAAEtB,UAAW,QAASC,SAAU,UAGlD,CAAC,QACClI,GAAW,EACb,GAGI2H,EAAuB/G,UAC3B,IAGE,GAFwB,oBAAoBgH,KAAKtB,GAE5B,CACnB,MAAMzF,QAAiBC,MAAM,6BAADC,OAA8BuF,GAAM,CAC9DtF,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAaP,EAASQ,OAE5B,YADA8F,EAAa/F,EAAKA,KAEpB,CACF,CAGA+F,EAAa,CACXqC,UAAWC,KAAKC,MAAsB,GAAhBD,KAAKE,UAAiB,EAC5CC,WAAYH,KAAKC,MAAsB,EAAhBD,KAAKE,UAAgB,EAC5CE,YAAY,IAAIpH,MAAOqH,cACvBC,eAAgB,CACd,CAAEV,KAAM,aAAcN,OAAQ,IAAKnF,OAAQ,SAE7CoG,gBAAiBP,KAAKC,MAAsB,EAAhBD,KAAKE,UAAgB,EACjDM,kBAAmBR,KAAKC,MAAsB,IAAhBD,KAAKE,UAAmB,KAE1D,CAAE,MAAOnI,GACPC,QAAQD,MAAM,2BAA4BA,GAE1C2F,EAAa,CACXqC,UAAW,EACXI,WAAY,EACZC,WAAY,uBACZE,eAAgB,CACd,CAAEV,KAAM,aAAcN,OAAQ,IAAKnF,OAAQ,SAE7CoG,gBAAiB,EACjBC,kBAAmB,MAEvB,GA0HF,OAAIlK,GAEAgC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qEAKhBiF,GAoBHlF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wEAAuEC,SAAA,EACpFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAEhCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mGAAkGC,UAC/GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+DAA8DC,SAAA,EAC3EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMwE,EAAS,oBACxB3E,UAAU,6EAA4EC,UAEtFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iCAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DtB,EAAE,iBAAkB,sBAEvBuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CgF,EAAQiB,uBAMfhG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAd,OAnEHO,KACtB,OAAQA,GACN,IAAK,OACH,MAAO,uEACT,IAAK,OACH,MAAO,mEACT,IAAK,UACH,MAAO,2EACT,IAAK,UACH,MAAO,+DACT,QACE,MAAO,qEAwD8DK,CAAemF,EAAQxF,SAAUQ,SAC7FtB,EAAEsG,EAAQxF,OAAQwF,EAAQxF,kBAOnCS,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,UAC3GC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAsB,aAAW,OAAMC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMyF,EAAa,WAC5B5F,UAAS,4CAAAd,OACO,YAAdyG,EACI,mDACA,0HACH1F,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZrB,EAAE,iBAAkB,uBAGvBoB,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMyF,EAAa,gBAC5B5F,UAAS,4CAAAd,OACO,iBAAdyG,EACI,mDACA,0HACH1F,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,eAAgB,oBAGrBoB,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMyF,EAAa,cAC5B5F,UAAS,4CAAAd,OACO,eAAdyG,EACI,mDACA,0HACH1F,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZrB,EAAE,aAAc,wBAOV,YAAdgH,IACC5F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0DAAyDC,SAAA,EAExEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,EAEtCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DACZrB,EAAE,qBAAsB,2BAG3BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,iBAAkB,sBAEvBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,OAAQ,QAAQ,OAAQ,IAAEsG,EAAQkB,QAAQC,UAAU,IAAEnB,EAAQkB,QAAQE,aAEzGtG,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,aAAc,eAAe,OAAQ,IAAEsG,EAAQkB,QAAQG,eAE1FvG,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,QAAS,SAAS,OAAQ,IAAEsG,EAAQkB,QAAQI,UAE/ExG,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,QAAS,SAAS,OAAQ,IAAEsG,EAAQkB,QAAQK,gBAKnFzG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,iBAAkB,sBAEvBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,gBAAiB,aAAa,OAAQ,IAAEsG,EAAQiB,kBAEnFnG,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,YAAa,cAAc,OAAQ,IAAE,IAAIiC,KAAKqE,EAAQmC,WAAWvG,yBAEpGd,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,UAAW,YAAY,OAAQ,IAAE,IAAIiC,KAAKqE,EAAQoC,SAASxG,yBAE9Fd,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mCAAkCC,SAAA,EAC7CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEtB,EAAE,YAAa,cAAc,OAAQ,IAAmB,QAAlB4F,EAACU,EAAQsC,iBAAS,IAAAhD,OAAA,EAAjBA,EAAmB6B,UAAU,IAAmB,QAAlB5B,EAACS,EAAQsC,iBAAS,IAAA/C,OAAA,EAAjBA,EAAmB6B,qBAM/HpB,EAAQqC,QACPvH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,QAAS,YAEduB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8EAA6EC,SACvFgF,EAAQqC,eAOjBvH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACZrB,EAAE,WAAY,gBAGjBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtB,EAAE,UAAW,cAEhBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sGAAqGC,SAChHtB,EAAE,WAAY,UAEjBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qGAAoGC,SAC/GtB,EAAE,YAAa,iBAElBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qGAAoGC,SAC/GtB,EAAE,QAAS,iBAIlBuB,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFgF,EAAQwB,SAASzC,IAAI,CAACqE,EAASnE,KAC9BnE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,YAAWC,UACvBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DoI,EAAQ3B,QAEXxG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDoI,EAAQ1B,oBAIfzG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACxEoI,EAAQzB,YAEX7G,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6DAA4DC,SAAA,CACvEoI,EAAQxB,UAAUxC,iBAAiB,IAAE1F,EAAE,MAAO,WAEjDoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yEAAwEC,SAAA,CACnFoI,EAAQvB,MAAMzC,iBAAiB,IAAE1F,EAAE,MAAO,YAlBtCuF,YA2BjBhE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOtB,EAAE,WAAY,YAAY,QACjCoB,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOgF,EAAQ8B,SAAS1C,iBAAiB,IAAE1F,EAAE,MAAO,aAErDsG,EAAQkC,UAAYlC,EAAQkC,SAASD,OAAS,IAC7CnH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOtB,EAAE,WAAY,YAAY,QACjCoB,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,IAAEgF,EAAQkC,SAASD,OAAO7C,iBAAiB,IAAE1F,EAAE,MAAO,cAGhEoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOtB,EAAE,MAAO,OAAO,KAAGsG,EAAQ+B,IAAIC,KAAK,UAC3ClH,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOgF,EAAQ+B,IAAIE,OAAO7C,iBAAiB,IAAE1F,EAAE,MAAO,cAExDoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0HAAyHC,SAAA,EACtIF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOtB,EAAE,QAAS,SAAS,QAC3BoB,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOgF,EAAQ6B,MAAMzC,iBAAiB,IAAE1F,EAAE,MAAO,wBAQ3DoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZrB,EAAE,iBAAkB,sBAGtBwG,EAASpB,OAAS,GACjB7D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtB,EAAE,OAAQ,WAEbuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtB,EAAE,SAAU,aAEfuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtB,EAAE,SAAU,aAEfuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtB,EAAE,YAAa,gBAElBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtB,EAAE,aAAc,uBAIvBuB,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFkF,EAASnB,IAAKsE,IAAO,IAAAC,EAAAC,EAAA,OACpBzI,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,SAC5D,IAAIW,KAAK0H,EAAQd,MAAM3G,wBAE1Bd,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,CACxEqI,EAAQpB,OAAO7C,iBAAiB,IAAE1F,EAAE,MAAO,WAE9CuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,UAC7DC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,aAAYC,SAAEqI,EAAQvG,YAExC7B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,SAC5DqI,EAAQb,WAAa,OAExB1H,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDC,SAAA,CAC1C,QAD0CsI,EAC5DD,EAAQZ,kBAAU,IAAAa,OAAA,EAAlBA,EAAoBnC,UAAU,IAAoB,QAAnBoC,EAACF,EAAQZ,kBAAU,IAAAc,OAAA,EAAlBA,EAAoBnC,cAdhDiC,EAAQrC,eAsBzBlG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,kBAAmB,4CAM5BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEtB,EAAE,YAAa,iBAElBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sDAAqDC,SAAA,CACjEkF,EAASsD,OAAO,CAACC,EAAKJ,IAAYI,EAAMJ,EAAQpB,OAAQ,GAAG7C,iBAAiB,IAAE1F,EAAE,MAAO,cAG5FoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2DAA0DC,SACtEtB,EAAE,mBAAoB,wBAEzBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0DAAyDC,SAAA,EACpEgF,EAAQ6B,MAAQ3B,EAASsD,OAAO,CAACC,EAAKJ,IAAYI,EAAMJ,EAAQpB,OAAQ,IAAI7C,iBAAiB,IAAE1F,EAAE,MAAO,cAG9GoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yDAAwDC,SACpEtB,EAAE,gBAAiB,qBAEtBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEkF,EAASsD,OAAO,CAACC,EAAKJ,IAAYI,EAAMJ,EAAQpB,OAAQ,IAAMjC,EAAQ6B,MACnEnI,EAAE,YAAa,cACfA,EAAE,gBAAiB,mCAUnCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EAErCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACZrB,EAAE,UAAW,eAGhBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,UACEI,QA1dUpB,UACxB,IAGE,GAFwB,oBAAoBgH,KAAKtB,GAE5B,CASnB,WARuBxF,MAAM,mBAADC,OAAoBuF,EAAE,SAAS,CACzD1C,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,uBAIPC,GAIX,MAAM,IAAII,MAAM,0BAHhBG,EAAAA,GAAMS,QAAQ,6BACduF,GAIJ,MAEEhG,EAAAA,GAAMS,QAAQ,yCAEd4E,EAAW3E,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEd,OAAQ,SAE3C,CAAE,MAAOE,GACPE,EAAAA,GAAMF,MAAM,yBACd,GAgccK,UAAU,0HAAyHC,SAAA,EAEnIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZrB,EAAE,cAAe,oBAGpBoB,EAAAA,EAAAA,MAAA,UACEI,QApcWwI,KACzBC,OAAOC,SAocO7I,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZrB,EAAE,eAAgB,qBAGrBoB,EAAAA,EAAAA,MAAA,UACEI,QAxcUpB,UACxB,IAGE,GAFwB,oBAAoBgH,KAAKtB,GAE5B,CAOnB,WANuBxF,MAAM,mBAADC,OAAoBuF,EAAE,QAAQ,CACxDtF,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,cAIvCC,GAGX,MAAM,IAAII,MAAM,0BAFhBG,EAAAA,GAAMS,QAAQ,sCAIlB,MAEET,EAAAA,GAAMS,QAAQ,0CAEdV,QAAQoG,IAAI,kCAAmCf,EAAQiB,cAE3D,CAAE,MAAOvG,GACPE,EAAAA,GAAMF,MAAM,yBACd,GAibcK,UAAU,8HAA6HC,SAAA,EAEvIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZrB,EAAE,cAAe,oBAGC,YAAnBsG,EAAQxF,QAA2C,SAAnBwF,EAAQxF,UACxCM,EAAAA,EAAAA,MAAA,UACEI,QAtbOpB,UACvB,IAGE,GAFwB,oBAAoBgH,KAAKtB,GAE5B,CAanB,WAZuBxF,MAAM,mBAADC,OAAoBuF,EAAE,cAAc,CAC9D1C,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElB2C,KAAMC,KAAKC,UAAU,CACnB4G,cAAe,OACfxB,MAAO,0CAIEhI,GAIX,MAAM,IAAII,MAAM,0BAHhBG,EAAAA,GAAMS,QAAQ,0BACduF,GAIJ,KAAO,CAELhG,EAAAA,GAAMS,QAAQ,sCAEd4E,EAAW3E,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAEd,OAAQ,UACvC,MAAMsJ,EAAa,CACjB9C,IAAKrF,KAAKoI,MAAMC,WAChB/B,OAAQjC,EAAQ6B,MAAQ3B,EAASsD,OAAO,CAACC,EAAKJ,IAAYI,EAAMJ,EAAQpB,OAAQ,GAChFnF,OAAQ,OACRyF,MAAM,IAAI5G,MAAOqH,cACjBR,UAAW,QAAU7G,KAAKoI,MAC1BtB,WAAY,CAAEtB,UAAW,OAAQC,SAAU,SAE7CjB,EAAY7E,GAAQ,IAAIA,EAAMwI,GAChC,CACF,CAAE,MAAOpJ,GACPE,EAAAA,GAAMF,MAAM,yBACd,GA+YgBK,UAAU,gIAA+HC,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZrB,EAAE,aAAc,oBAIrBoB,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMuF,GAAoB,GACnC1F,UAAU,8HAA6HC,SAAA,EAEvIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,aAAc,kBAGC,YAAnBsG,EAAQxF,SACPM,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMqF,GAAiB,GAChCxF,UAAU,0HAAyHC,SAAA,EAEnIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,cAAe,yBAOzB0G,IACCtF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4DACZrB,EAAE,YAAa,iBAGlBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE,YAAa,iBAElBuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4CAA2CC,SACxDoF,EAAUsC,gBAIf5H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE,aAAc,kBAEnBuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4CAA2CC,SACxDoF,EAAU0C,iBAIfhI,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE,aAAc,kBAEnBuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4CAA2CC,SACxD,IAAIW,KAAKyE,EAAU2C,YAAYnH,2BAIpCd,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE,kBAAmB,uBAExBuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4CAA2CC,SACxDoF,EAAU8C,sBAIfpI,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE,oBAAqB,0BAE1BoB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4CAA2CC,SAAA,CACxDoF,EAAU+C,kBAAkB/D,iBAAiB,IAAE1F,EAAE,MAAO,oBAQnEoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZrB,EAAE,aAAc,mBAGnBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEtB,EAAE,aAAc,kBAEnBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,CAChE2H,KAAKC,OAAO,IAAIjH,KAAS,IAAIA,KAAKqE,EAAQmC,YAAU,OAA2B,IAAEzI,EAAE,OAAQ,eAIhGoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yDAAwDC,SACpEtB,EAAE,kBAAmB,uBAExBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,CAClE2H,KAAKsB,MAAO/D,EAASsD,OAAO,CAACC,EAAKJ,IAAYI,EAAMJ,EAAQpB,OAAQ,GAAKjC,EAAQ6B,MAAS,KAAK,QAElG5G,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4DAA2DC,UACxEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,gCACVyD,MAAO,CAAEC,MAAM,GAADxE,OAAK0I,KAAKsB,MAAO/D,EAASsD,OAAO,CAACC,EAAKJ,IAAYI,EAAMJ,EAAQpB,OAAQ,GAAKjC,EAAQ6B,MAAS,KAAI,cAKvH/G,EAAAA,EAAAA,MAAA,OAAKC,UAAS,kBAAAd,OACZ,IAAI0B,KAAKqE,EAAQoC,SAAW,IAAIzG,KAC5B,+BACA,sCACHX,SAAA,EACDC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uBAAAd,OACZ,IAAI0B,KAAKqE,EAAQoC,SAAW,IAAIzG,KAC5B,iCACA,wCACHX,SACA,IAAIW,KAAKqE,EAAQoC,SAAW,IAAIzG,KAASjC,EAAE,UAAW,WAAaA,EAAE,eAAgB,qBAExFoB,EAAAA,EAAAA,MAAA,OAAKC,UAAS,qBAAAd,OACZ,IAAI0B,KAAKqE,EAAQoC,SAAW,IAAIzG,KAC5B,iCACA,wCACHX,SAAA,CACA2H,KAAKuB,IAAIvB,KAAKC,OAAO,IAAIjH,KAAKqE,EAAQoC,SAAW,IAAIzG,MAAM,QAA4B,IAAEjC,EAAE,OAAQ,0BAUjG,iBAAdgH,IACCzF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,KAACkJ,EAAiB,CAACtL,UAAWmH,EAAQkB,QAAQF,OAG9C/F,EAAAA,EAAAA,KAACmJ,EAAgB,CAAClH,UAAW8C,EAAQgB,WAM5B,eAAdN,IACCzF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,uBAAwB,4BAE7BuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,uBAAwB,kFAE7BoB,EAAAA,EAAAA,MAAA,UACEI,QAASA,KAAMN,EAAAA,EAAAA,IAAM,4CACrBG,UAAU,6DAA4DC,SAAA,EAEtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,0BAA2B,wCAQvC8G,IACCvF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6EAA4EC,UACzFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,aAAc,kBAEnBuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,0BAA2B,4CAEhCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMuF,GAAoB,GACnC1F,UAAU,0FAAyFC,SAElGtB,EAAE,SAAU,aAEfuB,EAAAA,EAAAA,KAAA,UACEC,QAASA,KACPuF,GAAoB,GACpB7F,EAAAA,GAAMS,QAAQ,sCAEhBN,UAAU,gEAA+DC,SAExEtB,EAAE,aAAc,yBAQ1B4G,IACCrF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6EAA4EC,UACzFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,cAAe,mBAEpBuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,uBAAwB,oDAE7BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMqF,GAAiB,GAChCxF,UAAU,0FAAyFC,SAElGtB,EAAE,SAAU,aAEfuB,EAAAA,EAAAA,KAAA,UACEC,QAASA,KACPqF,GAAiB,GACjB3F,EAAAA,GAAMS,QAAQ,mCAEhBN,UAAU,gEAA+DC,SAExEtB,EAAE,cAAe,8BA5lB5BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,kBAAmB,wBAExBuB,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMwE,EAAS,oBACxB3E,UAAU,qEAAoEC,SAE7EtB,EAAE,iBAAkB,2B", "sources": ["components/Integration/NphiesIntegration.jsx", "components/Integration/ZatcaIntegration.jsx", "pages/Financial/InvoiceDetailPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport toast from 'react-hot-toast';\n\nconst NphiesIntegration = ({ patientId }) => {\n  const [nphiesData, setNphiesData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [visaForm, setVisaForm] = useState({\n    visaNumber: '',\n    visaType: 'work',\n    serviceIds: []\n  });\n  const [showVisaForm, setShowVisaForm] = useState(false);\n  const { t } = useTranslation();\n\n  useEffect(() => {\n    if (patientId) {\n      loadNphiesStatus();\n    }\n  }, [patientId]);\n\n  const loadNphiesStatus = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/v1/nphies/status/${patientId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setNphiesData(data.data);\n      } else if (response.status === 404) {\n        // No NPHIES integration found\n        setNphiesData(null);\n      } else {\n        throw new Error('Failed to load NPHIES status');\n      }\n    } catch (error) {\n      console.error('Error loading NPHIES status:', error);\n      toast.error('Failed to load NPHIES integration status');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const verifyEligibility = async () => {\n    try {\n      const response = await fetch(`/api/v1/nphies/eligibility/${patientId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        toast.success('Eligibility verified successfully');\n        setNphiesData(prev => ({\n          ...prev,\n          eligibilityStatus: data.data.eligibilityStatus,\n          eligibilityResponse: data.data.eligibilityResponse\n        }));\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to verify eligibility');\n      }\n    } catch (error) {\n      console.error('Error verifying eligibility:', error);\n      toast.error(error.message || 'Failed to verify eligibility');\n    }\n  };\n\n  const linkVisa = async () => {\n    if (!visaForm.visaNumber) {\n      toast.error('Please enter visa number');\n      return;\n    }\n\n    try {\n      const response = await fetch('/api/v1/nphies/visa/link', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          patientId,\n          ...visaForm\n        })\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        toast.success('Visa linked successfully');\n        setNphiesData(prev => ({\n          ...prev,\n          visaInfo: data.data.visaInfo\n        }));\n        setShowVisaForm(false);\n        setVisaForm({ visaNumber: '', visaType: 'work', serviceIds: [] });\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to link visa');\n      }\n    } catch (error) {\n      console.error('Error linking visa:', error);\n      toast.error(error.message || 'Failed to link visa');\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'verified':\n      case 'active':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'rejected':\n      case 'expired':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-6\">\n        <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n          {t('loadingNphiesData', 'Loading NPHIES data...')}\n        </span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          <i className=\"fas fa-shield-alt text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('nphiesIntegration', 'NPHIES Integration')}\n        </h3>\n        \n        {nphiesData && (\n          <button\n            onClick={verifyEligibility}\n            className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            <i className=\"fas fa-sync mr-1\"></i>\n            {t('verifyEligibility', 'Verify Eligibility')}\n          </button>\n        )}\n      </div>\n\n      {!nphiesData ? (\n        <div className=\"text-center py-8\">\n          <i className=\"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4\"></i>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            {t('noNphiesIntegration', 'No NPHIES integration found for this patient')}\n          </p>\n          <button\n            onClick={() => toast('NPHIES integration setup coming soon')}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            {t('setupIntegration', 'Setup Integration')}\n          </button>\n        </div>\n      ) : (\n        <div className=\"space-y-6\">\n          {/* Eligibility Status */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                {t('eligibilityStatus', 'Eligibility Status')}\n              </h4>\n              <span className={`px-2 py-1 rounded text-sm font-medium ${getStatusColor(nphiesData.eligibilityStatus)}`}>\n                {t(nphiesData.eligibilityStatus, nphiesData.eligibilityStatus)}\n              </span>\n              {nphiesData.lastUpdated && (\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-2\">\n                  {t('lastUpdated', 'Last Updated')}: {new Date(nphiesData.lastUpdated).toLocaleDateString()}\n                </p>\n              )}\n            </div>\n\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                {t('activeEligibility', 'Active Eligibility')}\n              </h4>\n              <div className=\"flex items-center\">\n                {nphiesData.hasActiveEligibility ? (\n                  <>\n                    <i className=\"fas fa-check-circle text-green-600 mr-2\"></i>\n                    <span className=\"text-green-600 dark:text-green-400\">\n                      {t('active', 'Active')}\n                    </span>\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-times-circle text-red-600 mr-2\"></i>\n                    <span className=\"text-red-600 dark:text-red-400\">\n                      {t('inactive', 'Inactive')}\n                    </span>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Insurance Information */}\n          {nphiesData.insuranceInfo && (\n            <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n              <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n                {t('insuranceInformation', 'Insurance Information')}\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('payerName', 'Payer Name')}:\n                  </span>\n                  <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n                    {nphiesData.insuranceInfo.payerName}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('membershipNumber', 'Membership Number')}:\n                  </span>\n                  <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n                    {nphiesData.insuranceInfo.membershipNumber}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('coverageType', 'Coverage Type')}:\n                  </span>\n                  <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n                    {nphiesData.insuranceInfo.coverageType}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('copayAmount', 'Copay Amount')}:\n                  </span>\n                  <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n                    {nphiesData.insuranceInfo.copayAmount} {t('sar', 'SAR')}\n                  </span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Visa Information */}\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                {t('visaInformation', 'Visa Information')}\n              </h4>\n              {!nphiesData.visaInfo && (\n                <button\n                  onClick={() => setShowVisaForm(true)}\n                  className=\"px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700\"\n                >\n                  <i className=\"fas fa-plus mr-1\"></i>\n                  {t('linkVisa', 'Link Visa')}\n                </button>\n              )}\n            </div>\n\n            {nphiesData.visaInfo ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('visaNumber', 'Visa Number')}:\n                  </span>\n                  <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n                    {nphiesData.visaInfo.visaNumber}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('visaType', 'Visa Type')}:\n                  </span>\n                  <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n                    {t(nphiesData.visaInfo.visaType, nphiesData.visaInfo.visaType)}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('visaStatus', 'Status')}:\n                  </span>\n                  <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${getStatusColor(nphiesData.visaInfo.status)}`}>\n                    {t(nphiesData.visaInfo.status, nphiesData.visaInfo.status)}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700 dark:text-gray-300\">\n                    {t('expiryDate', 'Expiry Date')}:\n                  </span>\n                  <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n                    {new Date(nphiesData.visaInfo.expiryDate).toLocaleDateString()}\n                  </span>\n                </div>\n              </div>\n            ) : (\n              <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n                {t('noVisaLinked', 'No visa information linked')}\n              </p>\n            )}\n          </div>\n\n          {/* Claims Summary */}\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n              {t('claimsSummary', 'Claims Summary')}\n            </h4>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                  {nphiesData.claimsCount || 0}\n                </div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {t('totalClaims', 'Total Claims')}\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                  {nphiesData.hasActivePreAuth ? '✓' : '✗'}\n                </div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {t('preAuthorization', 'Pre-Authorization')}\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">\n                  {nphiesData.hasActiveEligibility ? '✓' : '✗'}\n                </div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {t('eligibilityActive', 'Eligibility Active')}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Visa Form Modal */}\n      {showVisaForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('linkVisa', 'Link Visa')}\n            </h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('visaNumber', 'Visa Number')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={visaForm.visaNumber}\n                  onChange={(e) => setVisaForm(prev => ({ ...prev, visaNumber: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder={t('enterVisaNumber', 'Enter visa number')}\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('visaType', 'Visa Type')}\n                </label>\n                <select\n                  value={visaForm.visaType}\n                  onChange={(e) => setVisaForm(prev => ({ ...prev, visaType: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"work\">{t('work', 'Work')}</option>\n                  <option value=\"visit\">{t('visit', 'Visit')}</option>\n                  <option value=\"residence\">{t('residence', 'Residence')}</option>\n                  <option value=\"hajj\">{t('hajj', 'Hajj')}</option>\n                  <option value=\"umrah\">{t('umrah', 'Umrah')}</option>\n                  <option value=\"transit\">{t('transit', 'Transit')}</option>\n                </select>\n              </div>\n            </div>\n            \n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <button\n                onClick={() => setShowVisaForm(false)}\n                className=\"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                onClick={linkVisa}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                {t('linkVisa', 'Link Visa')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NphiesIntegration;\n", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport toast from 'react-hot-toast';\n\nconst ZatcaIntegration = ({ invoiceId }) => {\n  const [zatcaStatus, setZatcaStatus] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const { t } = useTranslation();\n\n  useEffect(() => {\n    loadZatcaStatus();\n  }, []);\n\n  const loadZatcaStatus = async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/v1/zatca/compliance', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setZatcaStatus(data.data);\n      } else {\n        throw new Error('Failed to load ZATCA status');\n      }\n    } catch (error) {\n      console.error('Error loading ZATCA status:', error);\n      toast.error('Failed to load ZATCA integration status');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const submitInvoiceToZatca = async () => {\n    if (!invoiceId) {\n      toast.error('No invoice selected');\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      const response = await fetch('/api/v1/zatca/invoice/submit', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ invoiceId })\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        toast.success('Invoice submitted to ZATCA successfully');\n        \n        // Show submission details\n        toast.success(`ZATCA Invoice Number: ${data.data.zatcaInvoiceNumber}`);\n        \n        // Reload status\n        loadZatcaStatus();\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to submit invoice to ZATCA');\n      }\n    } catch (error) {\n      console.error('Error submitting to ZATCA:', error);\n      toast.error(error.message || 'Failed to submit invoice to ZATCA');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const generateTaxReport = async () => {\n    try {\n      const response = await fetch('/api/v1/zatca/report/generate', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          reportType: 'monthly',\n          period: {\n            year: new Date().getFullYear(),\n            month: new Date().getMonth() + 1\n          }\n        })\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        toast.success('Tax report generated successfully');\n        loadZatcaStatus();\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to generate tax report');\n      }\n    } catch (error) {\n      console.error('Error generating tax report:', error);\n      toast.error(error.message || 'Failed to generate tax report');\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'registered':\n      case 'accepted':\n      case 'active':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'pending':\n      case 'submitted':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'rejected':\n      case 'suspended':\n      case 'cancelled':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const getComplianceLevel = (percentage) => {\n    if (percentage >= 90) return { level: 'excellent', color: 'text-green-600', icon: 'fas fa-check-circle' };\n    if (percentage >= 70) return { level: 'good', color: 'text-blue-600', icon: 'fas fa-info-circle' };\n    if (percentage >= 50) return { level: 'fair', color: 'text-yellow-600', icon: 'fas fa-exclamation-triangle' };\n    return { level: 'poor', color: 'text-red-600', icon: 'fas fa-times-circle' };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-6\">\n        <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n          {t('loadingZatcaData', 'Loading ZATCA data...')}\n        </span>\n      </div>\n    );\n  }\n\n  if (!zatcaStatus) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"text-center py-8\">\n          <i className=\"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4\"></i>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n            {t('noZatcaIntegration', 'ZATCA integration not configured')}\n          </p>\n          <button\n            onClick={() => toast('ZATCA integration setup coming soon')}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            {t('setupIntegration', 'Setup Integration')}\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const compliance = getComplianceLevel(zatcaStatus.overallCompliance);\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          <i className=\"fas fa-receipt text-green-600 dark:text-green-400 mr-2\"></i>\n          {t('zatcaIntegration', 'ZATCA Integration')}\n        </h3>\n        \n        <div className=\"flex space-x-2\">\n          {invoiceId && (\n            <button\n              onClick={submitInvoiceToZatca}\n              disabled={submitting}\n              className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\"\n            >\n              {submitting ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1 inline-block\"></div>\n                  {t('submitting', 'Submitting...')}\n                </>\n              ) : (\n                <>\n                  <i className=\"fas fa-upload mr-1\"></i>\n                  {t('submitToZatca', 'Submit to ZATCA')}\n                </>\n              )}\n            </button>\n          )}\n          \n          <button\n            onClick={generateTaxReport}\n            className=\"px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700\"\n          >\n            <i className=\"fas fa-file-alt mr-1\"></i>\n            {t('generateReport', 'Generate Report')}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"space-y-6\">\n        {/* Registration Status */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n              {t('registrationStatus', 'Registration Status')}\n            </h4>\n            <span className={`px-2 py-1 rounded text-sm font-medium ${getStatusColor(zatcaStatus.registrationStatus)}`}>\n              {t(zatcaStatus.registrationStatus, zatcaStatus.registrationStatus)}\n            </span>\n          </div>\n\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n              {t('certificateStatus', 'Certificate Status')}\n            </h4>\n            <div className=\"flex items-center\">\n              {zatcaStatus.certificateValid ? (\n                <>\n                  <i className=\"fas fa-check-circle text-green-600 mr-2\"></i>\n                  <span className=\"text-green-600 dark:text-green-400\">\n                    {t('valid', 'Valid')}\n                  </span>\n                </>\n              ) : (\n                <>\n                  <i className=\"fas fa-times-circle text-red-600 mr-2\"></i>\n                  <span className=\"text-red-600 dark:text-red-400\">\n                    {t('invalid', 'Invalid')}\n                  </span>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Compliance Overview */}\n        <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n            {t('complianceOverview', 'Compliance Overview')}\n          </h4>\n          \n          <div className=\"flex items-center mb-3\">\n            <i className={`${compliance.icon} ${compliance.color} mr-2`}></i>\n            <span className={`font-medium ${compliance.color}`}>\n              {zatcaStatus.overallCompliance}% {t('compliant', 'Compliant')}\n            </span>\n            <span className=\"ml-2 text-sm text-gray-500 dark:text-gray-400\">\n              ({t(compliance.level, compliance.level)})\n            </span>\n          </div>\n          \n          <div className=\"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2\">\n            <div \n              className={`h-2 rounded-full transition-all duration-300 ${\n                zatcaStatus.overallCompliance >= 90 ? 'bg-green-600' :\n                zatcaStatus.overallCompliance >= 70 ? 'bg-blue-600' :\n                zatcaStatus.overallCompliance >= 50 ? 'bg-yellow-600' : 'bg-red-600'\n              }`}\n              style={{ width: `${zatcaStatus.overallCompliance}%` }}\n            ></div>\n          </div>\n        </div>\n\n        {/* Submission Statistics */}\n        <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n          <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n            {t('submissionStatistics', 'Submission Statistics')}\n          </h4>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n                {zatcaStatus.totalInvoicesSubmitted}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('totalSubmitted', 'Total Submitted')}\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                {zatcaStatus.successfulSubmissions}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('successful', 'Successful')}\n              </div>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-2xl font-bold text-red-600 dark:text-red-400\">\n                {zatcaStatus.failedSubmissions}\n              </div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('failed', 'Failed')}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Compliance Checks */}\n        {zatcaStatus.complianceChecks && zatcaStatus.complianceChecks.length > 0 && (\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">\n              {t('recentComplianceChecks', 'Recent Compliance Checks')}\n            </h4>\n            \n            <div className=\"space-y-2\">\n              {zatcaStatus.complianceChecks.map((check, index) => (\n                <div key={index} className=\"flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded\">\n                  <div className=\"flex items-center\">\n                    <i className={`fas ${\n                      check.status === 'passed' ? 'fa-check-circle text-green-600' :\n                      check.status === 'warning' ? 'fa-exclamation-triangle text-yellow-600' :\n                      'fa-times-circle text-red-600'\n                    } mr-2`}></i>\n                    <span className=\"text-sm text-gray-900 dark:text-white\">\n                      {t(check.checkType, check.checkType)}\n                    </span>\n                  </div>\n                  \n                  <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(check.status)}`}>\n                    {t(check.status, check.status)}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Last Sync Information */}\n        {zatcaStatus.lastSyncDate && (\n          <div className=\"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n              {t('lastSyncInformation', 'Last Sync Information')}\n            </h4>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('lastSyncDate', 'Last Sync')}: {new Date(zatcaStatus.lastSyncDate).toLocaleString()}\n            </p>\n          </div>\n        )}\n\n        {/* QR Code Information */}\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800\">\n          <div className=\"flex items-start\">\n            <i className=\"fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2 mt-1\"></i>\n            <div>\n              <h4 className=\"font-medium text-blue-900 dark:text-blue-100 mb-1\">\n                {t('zatcaCompliance', 'ZATCA Compliance')}\n              </h4>\n              <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                {t('zatcaComplianceInfo', 'All invoices are automatically generated with ZATCA-compliant QR codes and digital signatures. Tax calculations follow Saudi VAT regulations.')}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ZatcaIntegration;\n", "import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport NphiesIntegration from '../../components/Integration/NphiesIntegration';\nimport ZatcaIntegration from '../../components/Integration/ZatcaIntegration';\n\nconst InvoiceDetailPage = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { t } = useTranslation();\n  const { isRTL } = useLanguage();\n  const { user } = useAuth();\n\n  const [invoice, setInvoice] = useState(null);\n  const [payments, setPayments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [analytics, setAnalytics] = useState(null);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [showPaymentModal, setShowPaymentModal] = useState(false);\n  const [activeTab, setActiveTab] = useState('details');\n\n  useEffect(() => {\n    loadInvoiceDetails();\n    loadInvoiceAnalytics();\n  }, [id]);\n\n  const loadInvoiceDetails = async () => {\n    setLoading(true);\n    try {\n      // Check if ID looks like a MongoDB ObjectId (24 hex characters)\n      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(id);\n\n      if (isValidObjectId) {\n        const response = await fetch(`/api/v1/billing/${id}`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          setInvoice(data.data);\n          setPayments(data.data.payments || []);\n          return;\n        } else {\n          throw new Error('Failed to load invoice details');\n        }\n      } else {\n        // For demo purposes, use mock data for non-ObjectId IDs\n        throw new Error('Using mock data for demo');\n      }\n    } catch (error) {\n      console.error('Error loading invoice:', error);\n      // Always use mock data for development/demo\n      console.log('Using mock data for invoice:', id);\n      const mockInvoice = {\n        _id: id,\n        invoiceNumber: 'INV-2024-001',\n        patient: {\n          firstName: 'أحمد',\n          lastName: 'محمد علي',\n          nationalId: '**********',\n          phone: '+966501234567',\n          email: '<EMAIL>'\n        },\n        services: [\n          {\n            name: 'Physical Therapy Session',\n            description: 'Initial assessment and treatment',\n            quantity: 2,\n            unitPrice: 500,\n            total: 1000\n          },\n          {\n            name: 'Exercise Program',\n            description: 'Customized exercise plan',\n            quantity: 1,\n            unitPrice: 300,\n            total: 300\n          }\n        ],\n        subtotal: 1300,\n        tax: { rate: 15, amount: 195 },\n        discount: { type: 'fixed', amount: 50 },\n        total: 1445,\n        status: 'sent',\n        issueDate: '2024-01-15',\n        dueDate: '2024-02-14',\n        notes: 'Initial treatment package for lower back pain',\n        createdBy: {\n          firstName: 'Dr. Sarah',\n          lastName: 'Ahmed'\n        }\n      };\n      setInvoice(mockInvoice);\n      setPayments([\n        {\n          _id: '1',\n          amount: 500,\n          method: 'cash',\n          date: '2024-01-20',\n          reference: 'CASH001',\n          receivedBy: { firstName: 'Admin', lastName: 'User' }\n        }\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadInvoiceAnalytics = async () => {\n    try {\n      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(id);\n\n      if (isValidObjectId) {\n        const response = await fetch(`/api/v1/analytics/invoice/${id}`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          const data = await response.json();\n          setAnalytics(data.data);\n          return;\n        }\n      }\n\n      // Always fall back to mock analytics data for demo\n      setAnalytics({\n        viewCount: Math.floor(Math.random() * 15) + 5,\n        emailsSent: Math.floor(Math.random() * 3) + 1,\n        lastViewed: new Date().toISOString(),\n        paymentHistory: [\n          { date: '2024-01-20', amount: 500, method: 'cash' }\n        ],\n        relatedInvoices: Math.floor(Math.random() * 5) + 2,\n        patientTotalSpent: Math.floor(Math.random() * 5000) + 2000\n      });\n    } catch (error) {\n      console.error('Error loading analytics:', error);\n      // Mock analytics data\n      setAnalytics({\n        viewCount: 5,\n        emailsSent: 2,\n        lastViewed: '2024-01-22T10:30:00Z',\n        paymentHistory: [\n          { date: '2024-01-20', amount: 500, method: 'cash' }\n        ],\n        relatedInvoices: 3,\n        patientTotalSpent: 2500\n      });\n    }\n  };\n\n  const handleSendInvoice = async () => {\n    try {\n      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(id);\n\n      if (isValidObjectId) {\n        const response = await fetch(`/api/v1/billing/${id}/send`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          }\n        });\n\n        if (response.ok) {\n          toast.success('Invoice sent successfully');\n          loadInvoiceDetails(); // Reload to update status\n        } else {\n          throw new Error('Failed to send invoice');\n        }\n      } else {\n        // Mock success for demo data\n        toast.success('Invoice sent successfully (Demo Mode)');\n        // Update mock invoice status\n        setInvoice(prev => ({ ...prev, status: 'sent' }));\n      }\n    } catch (error) {\n      toast.error('Failed to send invoice');\n    }\n  };\n\n  const handlePrintInvoice = () => {\n    window.print();\n  };\n\n  const handleDownloadPDF = async () => {\n    try {\n      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(id);\n\n      if (isValidObjectId) {\n        const response = await fetch(`/api/v1/billing/${id}/pdf`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n\n        if (response.ok) {\n          toast.success('PDF download will be available soon');\n        } else {\n          throw new Error('Failed to generate PDF');\n        }\n      } else {\n        // For demo, simulate PDF generation\n        toast.success('PDF generated successfully (Demo Mode)');\n        // In a real implementation, you would generate and download the PDF\n        console.log('Would generate PDF for invoice:', invoice.invoiceNumber);\n      }\n    } catch (error) {\n      toast.error('Failed to download PDF');\n    }\n  };\n\n  const handleMarkAsPaid = async () => {\n    try {\n      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(id);\n\n      if (isValidObjectId) {\n        const response = await fetch(`/api/v1/billing/${id}/mark-paid`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            paymentMethod: 'cash',\n            notes: 'Marked as paid from invoice detail'\n          })\n        });\n\n        if (response.ok) {\n          toast.success('Invoice marked as paid');\n          loadInvoiceDetails();\n        } else {\n          throw new Error('Failed to mark as paid');\n        }\n      } else {\n        // Mock success for demo data\n        toast.success('Invoice marked as paid (Demo Mode)');\n        // Update mock invoice status and add payment\n        setInvoice(prev => ({ ...prev, status: 'paid' }));\n        const newPayment = {\n          _id: Date.now().toString(),\n          amount: invoice.total - payments.reduce((sum, payment) => sum + payment.amount, 0),\n          method: 'cash',\n          date: new Date().toISOString(),\n          reference: 'DEMO-' + Date.now(),\n          receivedBy: { firstName: 'Demo', lastName: 'User' }\n        };\n        setPayments(prev => [...prev, newPayment]);\n      }\n    } catch (error) {\n      toast.error('Failed to mark as paid');\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'paid':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'sent':\n        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400';\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'overdue':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!invoice) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <i className=\"fas fa-file-invoice text-4xl text-gray-400 mb-4\"></i>\n          <p className=\"text-gray-500 dark:text-gray-400\">\n            {t('invoiceNotFound', 'Invoice not found')}\n          </p>\n          <button\n            onClick={() => navigate('/billing/invoice')}\n            className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n          >\n            {t('backToInvoices', 'Back to Invoices')}\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 p-6 print:bg-white print:p-0\">\n      <div className=\"max-w-6xl mx-auto\">\n        {/* Header */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between\">\n            <div className=\"flex items-center space-x-4 mb-4 lg:mb-0\">\n              <button\n                onClick={() => navigate('/billing/invoice')}\n                className=\"text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white\"\n              >\n                <i className=\"fas fa-arrow-left text-xl\"></i>\n              </button>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {t('invoiceDetails', 'Invoice Details')}\n                </h1>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {invoice.invoiceNumber}\n                </p>\n              </div>\n            </div>\n\n            {/* Status Badge */}\n            <div className=\"flex items-center space-x-4\">\n              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(invoice.status)}`}>\n                {t(invoice.status, invoice.status)}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6\">\n          <div className=\"border-b border-gray-200 dark:border-gray-600\">\n            <nav className=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n              <button\n                onClick={() => setActiveTab('details')}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'details'\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <i className=\"fas fa-file-invoice mr-2\"></i>\n                {t('invoiceDetails', 'Invoice Details')}\n              </button>\n\n              <button\n                onClick={() => setActiveTab('integrations')}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'integrations'\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <i className=\"fas fa-link mr-2\"></i>\n                {t('integrations', 'Integrations')}\n              </button>\n\n              <button\n                onClick={() => setActiveTab('signatures')}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeTab === 'signatures'\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <i className=\"fas fa-signature mr-2\"></i>\n                {t('signatures', 'Signatures')}\n              </button>\n            </nav>\n          </div>\n        </div>\n\n        {/* Tab Content */}\n        {activeTab === 'details' && (\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6 print:grid-cols-1\">\n          {/* Main Invoice Content */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Invoice Information */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                <i className=\"fas fa-file-invoice text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('invoiceInformation', 'Invoice Information')}\n              </h2>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                    {t('patientDetails', 'Patient Details')}\n                  </h3>\n                  <div className=\"space-y-2\">\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('name', 'Name')}:</span> {invoice.patient.firstName} {invoice.patient.lastName}\n                    </p>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('nationalId', 'National ID')}:</span> {invoice.patient.nationalId}\n                    </p>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('phone', 'Phone')}:</span> {invoice.patient.phone}\n                    </p>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('email', 'Email')}:</span> {invoice.patient.email}\n                    </p>\n                  </div>\n                </div>\n\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                    {t('invoiceDetails', 'Invoice Details')}\n                  </h3>\n                  <div className=\"space-y-2\">\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('invoiceNumber', 'Invoice #')}:</span> {invoice.invoiceNumber}\n                    </p>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('issueDate', 'Issue Date')}:</span> {new Date(invoice.issueDate).toLocaleDateString()}\n                    </p>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('dueDate', 'Due Date')}:</span> {new Date(invoice.dueDate).toLocaleDateString()}\n                    </p>\n                    <p className=\"text-gray-600 dark:text-gray-400\">\n                      <span className=\"font-medium\">{t('createdBy', 'Created By')}:</span> {invoice.createdBy?.firstName} {invoice.createdBy?.lastName}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {invoice.notes && (\n                <div className=\"mt-6\">\n                  <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                    {t('notes', 'Notes')}\n                  </h3>\n                  <p className=\"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\">\n                    {invoice.notes}\n                  </p>\n                </div>\n              )}\n            </div>\n\n            {/* Services Table */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                <i className=\"fas fa-list text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('services', 'Services')}\n              </h2>\n\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full\">\n                  <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                    <tr>\n                      <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                        {t('service', 'Service')}\n                      </th>\n                      <th className=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                        {t('quantity', 'Qty')}\n                      </th>\n                      <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                        {t('unitPrice', 'Unit Price')}\n                      </th>\n                      <th className=\"px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                        {t('total', 'Total')}\n                      </th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                    {invoice.services.map((service, index) => (\n                      <tr key={index}>\n                        <td className=\"px-4 py-4\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                              {service.name}\n                            </div>\n                            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                              {service.description}\n                            </div>\n                          </div>\n                        </td>\n                        <td className=\"px-4 py-4 text-center text-sm text-gray-900 dark:text-white\">\n                          {service.quantity}\n                        </td>\n                        <td className=\"px-4 py-4 text-right text-sm text-gray-900 dark:text-white\">\n                          {service.unitPrice.toLocaleString()} {t('sar', 'SAR')}\n                        </td>\n                        <td className=\"px-4 py-4 text-right text-sm font-medium text-gray-900 dark:text-white\">\n                          {service.total.toLocaleString()} {t('sar', 'SAR')}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              </div>\n\n              {/* Invoice Totals */}\n              <div className=\"mt-6 border-t border-gray-200 dark:border-gray-600 pt-6\">\n                <div className=\"flex justify-end\">\n                  <div className=\"w-64 space-y-2\">\n                    <div className=\"flex justify-between text-sm text-gray-600 dark:text-gray-400\">\n                      <span>{t('subtotal', 'Subtotal')}:</span>\n                      <span>{invoice.subtotal.toLocaleString()} {t('sar', 'SAR')}</span>\n                    </div>\n                    {invoice.discount && invoice.discount.amount > 0 && (\n                      <div className=\"flex justify-between text-sm text-gray-600 dark:text-gray-400\">\n                        <span>{t('discount', 'Discount')}:</span>\n                        <span>-{invoice.discount.amount.toLocaleString()} {t('sar', 'SAR')}</span>\n                      </div>\n                    )}\n                    <div className=\"flex justify-between text-sm text-gray-600 dark:text-gray-400\">\n                      <span>{t('tax', 'Tax')} ({invoice.tax.rate}%):</span>\n                      <span>{invoice.tax.amount.toLocaleString()} {t('sar', 'SAR')}</span>\n                    </div>\n                    <div className=\"flex justify-between text-lg font-bold text-gray-900 dark:text-white border-t border-gray-200 dark:border-gray-600 pt-2\">\n                      <span>{t('total', 'Total')}:</span>\n                      <span>{invoice.total.toLocaleString()} {t('sar', 'SAR')}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Payment History */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                <i className=\"fas fa-credit-card text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('paymentHistory', 'Payment History')}\n              </h2>\n\n              {payments.length > 0 ? (\n                <div className=\"overflow-x-auto\">\n                  <table className=\"w-full\">\n                    <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                      <tr>\n                        <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                          {t('date', 'Date')}\n                        </th>\n                        <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                          {t('amount', 'Amount')}\n                        </th>\n                        <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                          {t('method', 'Method')}\n                        </th>\n                        <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                          {t('reference', 'Reference')}\n                        </th>\n                        <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                          {t('receivedBy', 'Received By')}\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                      {payments.map((payment) => (\n                        <tr key={payment._id}>\n                          <td className=\"px-4 py-4 text-sm text-gray-900 dark:text-white\">\n                            {new Date(payment.date).toLocaleDateString()}\n                          </td>\n                          <td className=\"px-4 py-4 text-sm font-medium text-gray-900 dark:text-white\">\n                            {payment.amount.toLocaleString()} {t('sar', 'SAR')}\n                          </td>\n                          <td className=\"px-4 py-4 text-sm text-gray-900 dark:text-white\">\n                            <span className=\"capitalize\">{payment.method}</span>\n                          </td>\n                          <td className=\"px-4 py-4 text-sm text-gray-900 dark:text-white\">\n                            {payment.reference || '-'}\n                          </td>\n                          <td className=\"px-4 py-4 text-sm text-gray-900 dark:text-white\">\n                            {payment.receivedBy?.firstName} {payment.receivedBy?.lastName}\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              ) : (\n                <div className=\"text-center py-8\">\n                  <i className=\"fas fa-credit-card text-4xl text-gray-400 mb-4\"></i>\n                  <p className=\"text-gray-500 dark:text-gray-400\">\n                    {t('noPaymentsFound', 'No payments found for this invoice')}\n                  </p>\n                </div>\n              )}\n\n              {/* Payment Summary */}\n              <div className=\"mt-6 border-t border-gray-200 dark:border-gray-600 pt-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n                    <div className=\"text-sm text-blue-600 dark:text-blue-400 font-medium\">\n                      {t('totalPaid', 'Total Paid')}\n                    </div>\n                    <div className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n                      {payments.reduce((sum, payment) => sum + payment.amount, 0).toLocaleString()} {t('sar', 'SAR')}\n                    </div>\n                  </div>\n                  <div className=\"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg\">\n                    <div className=\"text-sm text-yellow-600 dark:text-yellow-400 font-medium\">\n                      {t('remainingBalance', 'Remaining Balance')}\n                    </div>\n                    <div className=\"text-2xl font-bold text-yellow-900 dark:text-yellow-100\">\n                      {(invoice.total - payments.reduce((sum, payment) => sum + payment.amount, 0)).toLocaleString()} {t('sar', 'SAR')}\n                    </div>\n                  </div>\n                  <div className=\"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg\">\n                    <div className=\"text-sm text-green-600 dark:text-green-400 font-medium\">\n                      {t('paymentStatus', 'Payment Status')}\n                    </div>\n                    <div className=\"text-lg font-bold text-green-900 dark:text-green-100\">\n                      {payments.reduce((sum, payment) => sum + payment.amount, 0) >= invoice.total\n                        ? t('fullyPaid', 'Fully Paid')\n                        : t('partiallyPaid', 'Partially Paid')\n                      }\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Sidebar - Actions & Analytics */}\n          <div className=\"space-y-6 print:hidden\">\n            {/* Actions */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                <i className=\"fas fa-cogs text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('actions', 'Actions')}\n              </h2>\n\n              <div className=\"space-y-3\">\n                <button\n                  onClick={handleSendInvoice}\n                  className=\"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <i className=\"fas fa-paper-plane mr-2\"></i>\n                  {t('sendInvoice', 'Send Invoice')}\n                </button>\n\n                <button\n                  onClick={handlePrintInvoice}\n                  className=\"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                >\n                  <i className=\"fas fa-print mr-2\"></i>\n                  {t('printInvoice', 'Print Invoice')}\n                </button>\n\n                <button\n                  onClick={handleDownloadPDF}\n                  className=\"w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n                >\n                  <i className=\"fas fa-download mr-2\"></i>\n                  {t('downloadPDF', 'Download PDF')}\n                </button>\n\n                {(invoice.status === 'pending' || invoice.status === 'sent') && (\n                  <button\n                    onClick={handleMarkAsPaid}\n                    className=\"w-full flex items-center justify-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors\"\n                  >\n                    <i className=\"fas fa-check-circle mr-2\"></i>\n                    {t('markAsPaid', 'Mark as Paid')}\n                  </button>\n                )}\n\n                <button\n                  onClick={() => setShowPaymentModal(true)}\n                  className=\"w-full flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors\"\n                >\n                  <i className=\"fas fa-plus mr-2\"></i>\n                  {t('addPayment', 'Add Payment')}\n                </button>\n\n                {invoice.status === 'pending' && (\n                  <button\n                    onClick={() => setShowEditModal(true)}\n                    className=\"w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n                  >\n                    <i className=\"fas fa-edit mr-2\"></i>\n                    {t('editInvoice', 'Edit Invoice')}\n                  </button>\n                )}\n              </div>\n            </div>\n\n            {/* Analytics */}\n            {analytics && (\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                  <i className=\"fas fa-chart-line text-blue-600 dark:text-blue-400 mr-2\"></i>\n                  {t('analytics', 'Analytics')}\n                </h2>\n\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('viewCount', 'View Count')}\n                    </span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">\n                      {analytics.viewCount}\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('emailsSent', 'Emails Sent')}\n                    </span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">\n                      {analytics.emailsSent}\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('lastViewed', 'Last Viewed')}\n                    </span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">\n                      {new Date(analytics.lastViewed).toLocaleDateString()}\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('relatedInvoices', 'Related Invoices')}\n                    </span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">\n                      {analytics.relatedInvoices}\n                    </span>\n                  </div>\n\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('patientTotalSpent', 'Patient Total Spent')}\n                    </span>\n                    <span className=\"font-medium text-gray-900 dark:text-white\">\n                      {analytics.patientTotalSpent.toLocaleString()} {t('sar', 'SAR')}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Quick Stats */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                <i className=\"fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('quickStats', 'Quick Stats')}\n              </h2>\n\n              <div className=\"space-y-4\">\n                <div className=\"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg\">\n                  <div className=\"text-sm text-blue-600 dark:text-blue-400 font-medium\">\n                    {t('invoiceAge', 'Invoice Age')}\n                  </div>\n                  <div className=\"text-lg font-bold text-blue-900 dark:text-blue-100\">\n                    {Math.floor((new Date() - new Date(invoice.issueDate)) / (1000 * 60 * 60 * 24))} {t('days', 'days')}\n                  </div>\n                </div>\n\n                <div className=\"bg-green-50 dark:bg-green-900/20 p-3 rounded-lg\">\n                  <div className=\"text-sm text-green-600 dark:text-green-400 font-medium\">\n                    {t('paymentProgress', 'Payment Progress')}\n                  </div>\n                  <div className=\"text-lg font-bold text-green-900 dark:text-green-100\">\n                    {Math.round((payments.reduce((sum, payment) => sum + payment.amount, 0) / invoice.total) * 100)}%\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2\">\n                    <div\n                      className=\"bg-green-600 h-2 rounded-full\"\n                      style={{ width: `${Math.round((payments.reduce((sum, payment) => sum + payment.amount, 0) / invoice.total) * 100)}%` }}\n                    ></div>\n                  </div>\n                </div>\n\n                <div className={`p-3 rounded-lg ${\n                  new Date(invoice.dueDate) < new Date()\n                    ? 'bg-red-50 dark:bg-red-900/20'\n                    : 'bg-yellow-50 dark:bg-yellow-900/20'\n                }`}>\n                  <div className={`text-sm font-medium ${\n                    new Date(invoice.dueDate) < new Date()\n                      ? 'text-red-600 dark:text-red-400'\n                      : 'text-yellow-600 dark:text-yellow-400'\n                  }`}>\n                    {new Date(invoice.dueDate) < new Date() ? t('overdue', 'Overdue') : t('daysUntilDue', 'Days Until Due')}\n                  </div>\n                  <div className={`text-lg font-bold ${\n                    new Date(invoice.dueDate) < new Date()\n                      ? 'text-red-900 dark:text-red-100'\n                      : 'text-yellow-900 dark:text-yellow-100'\n                  }`}>\n                    {Math.abs(Math.floor((new Date(invoice.dueDate) - new Date()) / (1000 * 60 * 60 * 24)))} {t('days', 'days')}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        )}\n\n        {/* Integrations Tab */}\n        {activeTab === 'integrations' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {/* NPHIES Integration */}\n              <NphiesIntegration patientId={invoice.patient._id} />\n\n              {/* ZATCA Integration */}\n              <ZatcaIntegration invoiceId={invoice._id} />\n            </div>\n          </div>\n        )}\n\n        {/* Signatures Tab */}\n        {activeTab === 'signatures' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <div className=\"text-center py-8\">\n              <i className=\"fas fa-signature text-4xl text-gray-400 mb-4\"></i>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                {t('electronicSignatures', 'Electronic Signatures')}\n              </h3>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                {t('signatureFeatureInfo', 'Electronic signature functionality for invoice approval and patient consent')}\n              </p>\n              <button\n                onClick={() => toast('Electronic signature feature coming soon')}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                <i className=\"fas fa-plus mr-2\"></i>\n                {t('createSignatureDocument', 'Create Signature Document')}\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Payment Modal */}\n      {showPaymentModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('addPayment', 'Add Payment')}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              {t('paymentModalPlaceholder', 'Payment form will be implemented here')}\n            </p>\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => setShowPaymentModal(false)}\n                className=\"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                onClick={() => {\n                  setShowPaymentModal(false);\n                  toast.success('Payment functionality coming soon');\n                }}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                {t('addPayment', 'Add Payment')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Modal */}\n      {showEditModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('editInvoice', 'Edit Invoice')}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              {t('editModalPlaceholder', 'Invoice editing form will be implemented here')}\n            </p>\n            <div className=\"flex justify-end space-x-3\">\n              <button\n                onClick={() => setShowEditModal(false)}\n                className=\"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                onClick={() => {\n                  setShowEditModal(false);\n                  toast.success('Edit functionality coming soon');\n                }}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n              >\n                {t('saveChanges', 'Save Changes')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default InvoiceDetailPage;\n"], "names": ["_ref", "patientId", "nphiesData", "setNphiesData", "useState", "loading", "setLoading", "visaForm", "setVisaForm", "visaNumber", "visaType", "serviceIds", "showVisaForm", "setShowVisaForm", "t", "useTranslation", "useEffect", "loadNphiesStatus", "async", "response", "fetch", "concat", "headers", "localStorage", "getItem", "ok", "data", "json", "status", "Error", "error", "console", "toast", "getStatusColor", "_jsxs", "className", "children", "_jsx", "onClick", "errorData", "message", "success", "prev", "_objectSpread", "eligibilityStatus", "eligibilityResponse", "lastUpdated", "Date", "toLocaleDateString", "hasActiveEligibility", "_Fragment", "insuranceInfo", "payerName", "membershipNumber", "coverageType", "copayAmount", "visaInfo", "expiryDate", "claimsCount", "hasActivePreAuth", "type", "value", "onChange", "e", "target", "placeholder", "method", "body", "JSON", "stringify", "invoiceId", "zatcaStatus", "setZatcaStatus", "submitting", "setSubmitting", "loadZatcaStatus", "compliance", "percentage", "overallCompliance", "level", "color", "icon", "zatcaInvoiceNumber", "disabled", "reportType", "period", "year", "getFullYear", "month", "getMonth", "registrationStatus", "certificateValid", "style", "width", "totalInvoicesSubmitted", "successfulSubmissions", "failedSubmissions", "complianceChecks", "length", "map", "check", "index", "checkType", "lastSyncDate", "toLocaleString", "InvoiceDetailPage", "_invoice$createdBy", "_invoice$createdBy2", "id", "useParams", "navigate", "useNavigate", "isRTL", "useLanguage", "user", "useAuth", "invoice", "setInvoice", "payments", "setPayments", "analytics", "setAnalytics", "showEditModal", "setShowEditModal", "showPaymentModal", "setShowPaymentModal", "activeTab", "setActiveTab", "loadInvoiceDetails", "loadInvoiceAnalytics", "test", "log", "_id", "invoiceNumber", "patient", "firstName", "lastName", "nationalId", "phone", "email", "services", "name", "description", "quantity", "unitPrice", "total", "subtotal", "tax", "rate", "amount", "discount", "issueDate", "dueDate", "notes", "created<PERSON>y", "date", "reference", "receivedBy", "viewCount", "Math", "floor", "random", "emailsSent", "lastViewed", "toISOString", "paymentHistory", "relatedInvoices", "patientTotalSpent", "service", "payment", "_payment$receivedBy", "_payment$receivedBy2", "reduce", "sum", "handlePrintInvoice", "window", "print", "paymentMethod", "newPayment", "now", "toString", "round", "abs", "NphiesIntegration", "ZatcaIntegration"], "sourceRoot": ""}