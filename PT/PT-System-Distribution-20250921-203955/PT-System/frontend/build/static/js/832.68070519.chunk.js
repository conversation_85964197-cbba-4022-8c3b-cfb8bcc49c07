"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[832],{832:(e,t,a)=>{a.r(t),a.d(t,{default:()=>_});var s=a(2555),r=a(5043),i=a(4117),n=a(3768),l=a(3986);const o=["title","titleId"];function c(e,t){let{title:a,titleId:s}=e,i=(0,l.A)(e,o);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))}const d=r.forwardRef(c),m=["title","titleId"];function h(e,t){let{title:a,titleId:s}=e,i=(0,l.A)(e,m);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},i),a?r.createElement("title",{id:s},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181"}))}const g=r.forwardRef(h);var u=a(9942),x=a(3315),y=a(9399),p=a(2903),f=a(7012),b=a(7504),v=a(6305);const w=new class{async performAnalysis(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"progress_evaluation";try{return(await v.Ay.post("/ai-analytics/analyze/".concat(e),{analysis_type:t})).data}catch(a){throw console.error("AI analysis failed:",a),a}}async getPatientAnalytics(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{const{limit:a=10,page:s=1}=t;return(await v.Ay.get("/ai-analytics/patient/".concat(e),{params:{limit:a,page:s}})).data}catch(a){throw console.error("Failed to get patient analytics:",a),a}}async getLatestAnalysis(e){try{return(await v.Ay.get("/ai-analytics/patient/".concat(e,"/latest"))).data}catch(t){throw console.error("Failed to get latest analysis:",t),t}}async getTreatmentRecommendations(e){try{return(await v.Ay.get("/ai-analytics/patient/".concat(e,"/recommendations"))).data}catch(t){throw console.error("Failed to get treatment recommendations:",t),t}}async getOutcomePredictions(e){try{return(await v.Ay.get("/ai-analytics/patient/".concat(e,"/predictions"))).data}catch(t){throw console.error("Failed to get outcome predictions:",t),t}}async getHighRiskPatients(){try{return(await v.Ay.get("/ai-analytics/high-risk")).data}catch(e){throw console.error("Failed to get high-risk patients:",e),e}}async getPendingAlerts(){try{return(await v.Ay.get("/ai-analytics/alerts")).data}catch(e){throw console.error("Failed to get pending alerts:",e),e}}async dismissAlert(e,t,a){try{return(await v.Ay.put("/ai-analytics/".concat(e,"/alerts/").concat(t,"/dismiss"),{reason:a})).data}catch(s){throw console.error("Failed to dismiss alert:",s),s}}async addFeedback(e,t){try{return(await v.Ay.post("/ai-analytics/".concat(e,"/feedback"),t)).data}catch(a){throw console.error("Failed to add feedback:",a),a}}async getDashboardData(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"30";try{return(await v.Ay.get("/ai-analytics/dashboard",{params:{timeframe:e}})).data}catch(t){throw console.error("Failed to get dashboard data:",t),t}}async batchAnalyze(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"progress_evaluation";try{return(await v.Ay.post("/ai-analytics/batch-analyze",{patient_ids:e,analysis_type:t})).data}catch(a){throw console.error("Batch analysis failed:",a),a}}async getStatistics(){try{return(await v.Ay.get("/ai-analytics/statistics")).data}catch(e){throw console.error("Failed to get statistics:",e),e}}formatRiskLevel(e){const t={low:{label:"Low Risk",color:"green",icon:"\u2713"},medium:{label:"Medium Risk",color:"yellow",icon:"\u26a0"},high:{label:"High Risk",color:"orange",icon:"\u26a0"},critical:{label:"Critical Risk",color:"red",icon:"\ud83d\udea8"}};return t[e]||t.low}formatConfidence(e){const t=Math.round(100*e);let a="Low",s="red";return t>=80?(a="High",s="green"):t>=60&&(a="Medium",s="yellow"),{percentage:t,level:a,color:s}}formatPriority(e){const t={1:{label:"Urgent",color:"red",icon:"\ud83d\udd34"},2:{label:"High",color:"orange",icon:"\ud83d\udfe0"},3:{label:"Medium",color:"yellow",icon:"\ud83d\udfe1"},4:{label:"Low",color:"blue",icon:"\ud83d\udd35"},5:{label:"Optional",color:"gray",icon:"\u26aa"}};return t[e]||t[3]}calculateTrend(e,t){const a=t-e,s=Math.round(a/e*100);let r="stable",i="\u27a1\ufe0f",n="gray";return s>10?(r="improving",i="\ud83d\udcc8",n="green"):s<-10&&(r="declining",i="\ud83d\udcc9",n="red"),{trend:r,percentage:s,icon:i,color:n}}formatTimeline(e){if(e<4)return"".concat(e," week").concat(1!==e?"s":"");if(e<52){const t=Math.round(e/4);return"".concat(t," month").concat(1!==t?"s":"")}{const t=Math.round(e/52);return"".concat(t," year").concat(1!==t?"s":"")}}getAlertStyle(e){const t={critical:{icon:"\ud83d\udea8",color:"red",bgColor:"bg-red-50"},warning:{icon:"\u26a0\ufe0f",color:"orange",bgColor:"bg-orange-50"},caution:{icon:"\u26a0\ufe0f",color:"yellow",bgColor:"bg-yellow-50"},info:{icon:"\u2139\ufe0f",color:"blue",bgColor:"bg-blue-50"}};return t[e]||t.info}validateAnalysisData(e){const t=["patient","analysis_type","status"].filter(t=>!e[t]);if(t.length>0)throw new Error("Missing required fields: ".concat(t.join(", ")));return!0}};var j=a(6761),A=a(3099),N=a(2593),k=a(579);const _=()=>{const{t:e}=(0,i.Bd)(),[t,a]=(0,r.useState)(!0),[l,o]=(0,r.useState)(null),[c,m]=(0,r.useState)([]),[h,v]=(0,r.useState)([]),[_,C]=(0,r.useState)("30");(0,r.useEffect)(()=>{L()},[_]);const L=async()=>{try{a(!0);const[e,t,s]=await Promise.all([w.getDashboardData(_),w.getHighRiskPatients(),w.getPendingAlerts()]);o(e.data),m(t.data),v(s.data)}catch(t){console.error("Failed to load dashboard data:",t),n.oR.error(e("failedToLoadDashboard","Failed to load dashboard data"))}finally{a(!1)}},R=async(t,a,s)=>{try{await w.dismissAlert(t,a,s),n.oR.success(e("alertDismissed","Alert dismissed successfully")),L()}catch(r){console.error("Failed to dismiss alert:",r),n.oR.error(e("failedToDismissAlert","Failed to dismiss alert"))}},M=e=>{let{title:t,value:a,icon:s,color:r,trend:i,subtitle:n}=e;return(0,k.jsxs)(A.A,{className:"p-6",children:[(0,k.jsxs)("div",{className:"flex items-center justify-between",children:[(0,k.jsxs)("div",{children:[(0,k.jsx)("p",{className:"text-sm font-medium text-gray-600",children:t}),(0,k.jsx)("p",{className:"text-2xl font-bold text-".concat(r,"-600"),children:a}),n&&(0,k.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:n})]}),(0,k.jsx)("div",{className:"p-3 rounded-full bg-".concat(r,"-100"),children:(0,k.jsx)(s,{className:"h-6 w-6 text-".concat(r,"-600")})})]}),i&&(0,k.jsxs)("div",{className:"mt-4 flex items-center",children:["up"===i.direction?(0,k.jsx)(d,{className:"h-4 w-4 text-green-500 mr-1"}):(0,k.jsx)(g,{className:"h-4 w-4 text-red-500 mr-1"}),(0,k.jsxs)("span",{className:"text-sm ".concat("up"===i.direction?"text-green-600":"text-red-600"),children:[i.percentage,"% ",i.period]})]})]})},E=e=>{var t,a;let{patient:s,analysis:r}=e;const i=w.formatRiskLevel(null===(t=r.risk_assessment)||void 0===t?void 0:t.risk_category);return(0,k.jsxs)("div",{className:"border rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,k.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,k.jsxs)("h4",{className:"font-medium text-gray-900",children:[s.firstName," ",s.lastName]}),(0,k.jsxs)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-".concat(i.color,"-100 text-").concat(i.color,"-800"),children:[i.icon," ",i.label]})]}),(0,k.jsxs)("p",{className:"text-sm text-gray-600 mb-2",children:["Risk Score: ",null===(a=r.risk_assessment)||void 0===a?void 0:a.overall_risk_score,"/100"]}),(0,k.jsxs)("div",{className:"text-xs text-gray-500",children:["Last Analysis: ",new Date(r.analysis_date).toLocaleDateString()]})]})},P=e=>{var t,a;let{alert:s,analysis:r,onDismiss:i}=e;const n=w.getAlertStyle(s.type);return(0,k.jsx)("div",{className:"border-l-4 border-".concat(n.color,"-400 ").concat(n.bgColor," p-4 rounded-r-lg"),children:(0,k.jsxs)("div",{className:"flex items-start justify-between",children:[(0,k.jsxs)("div",{className:"flex items-start",children:[(0,k.jsx)("span",{className:"text-lg mr-2",children:n.icon}),(0,k.jsxs)("div",{children:[(0,k.jsxs)("h4",{className:"font-medium text-".concat(n.color,"-800"),children:[s.type.charAt(0).toUpperCase()+s.type.slice(1)," Alert"]}),(0,k.jsx)("p",{className:"text-sm text-".concat(n.color,"-700 mt-1"),children:s.message}),(0,k.jsxs)("p",{className:"text-xs text-gray-500 mt-2",children:["Patient: ",null===(t=r.patient)||void 0===t?void 0:t.firstName," ",null===(a=r.patient)||void 0===a?void 0:a.lastName]})]})]}),(0,k.jsx)(N.A,{variant:"outline",size:"sm",onClick:()=>i(r._id,s.index,"Reviewed by clinician"),className:"ml-4",children:"Dismiss"})]})})};return t?(0,k.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,k.jsx)(j.Ay,{size:"lg"})}):(0,k.jsxs)("div",{className:"space-y-6",children:[(0,k.jsxs)("div",{className:"flex items-center justify-between",children:[(0,k.jsxs)("div",{children:[(0,k.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,k.jsx)(u.A,{className:"h-8 w-8 mr-3 text-blue-600"}),e("aiAnalyticsDashboard","AI Analytics Dashboard")]}),(0,k.jsx)("p",{className:"text-gray-600 mt-1",children:e("aiDashboardDescription","AI-powered insights and predictions for patient care")})]}),(0,k.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,k.jsxs)("select",{value:_,onChange:e=>C(e.target.value),className:"border border-gray-300 rounded-md px-3 py-2 text-sm",children:[(0,k.jsx)("option",{value:"7",children:"Last 7 days"}),(0,k.jsx)("option",{value:"30",children:"Last 30 days"}),(0,k.jsx)("option",{value:"90",children:"Last 90 days"})]}),(0,k.jsx)(N.A,{onClick:L,variant:"outline",children:e("refresh","Refresh")})]})]}),l&&(0,k.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,k.jsx)(M,{title:e("totalAnalyses","Total Analyses"),value:l.summary.total_analyses,icon:x.A,color:"blue",subtitle:"Last ".concat(_," days")}),(0,k.jsx)(M,{title:e("highRiskPatients","High Risk Patients"),value:l.summary.high_risk_patients,icon:y.A,color:"red",subtitle:"Require attention"}),(0,k.jsx)(M,{title:e("pendingAlerts","Pending Alerts"),value:l.summary.pending_alerts,icon:p.A,color:"orange",subtitle:"Need review"}),(0,k.jsx)(M,{title:e("avgProcessingTime","Avg Processing Time"),value:"".concat(Math.round(l.analysis_trends.reduce((e,t)=>e+t.avgProcessingTime,0)/l.analysis_trends.length/1e3),"s"),icon:f.A,color:"green",subtitle:"Per analysis"})]}),l&&(0,k.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,k.jsxs)(A.A,{className:"p-6",children:[(0,k.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("riskDistribution","Risk Distribution")}),(0,k.jsx)("div",{className:"space-y-3",children:l.risk_distribution.map(e=>(0,k.jsxs)("div",{className:"flex items-center justify-between",children:[(0,k.jsxs)("span",{className:"text-sm font-medium text-gray-700 capitalize",children:[e._id||"Unknown"," Risk"]}),(0,k.jsxs)("div",{className:"flex items-center",children:[(0,k.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2 mr-3",children:(0,k.jsx)("div",{className:"h-2 rounded-full ".concat("critical"===e._id?"bg-red-500":"high"===e._id?"bg-orange-500":"medium"===e._id?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(e.count/l.summary.total_analyses*100,"%")}})}),(0,k.jsx)("span",{className:"text-sm font-semibold text-gray-900",children:e.count})]})]},e._id))})]}),(0,k.jsxs)(A.A,{className:"p-6",children:[(0,k.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("modelPerformance","Model Performance")}),(0,k.jsx)("div",{className:"space-y-3",children:l.model_performance.map(e=>(0,k.jsxs)("div",{className:"flex items-center justify-between",children:[(0,k.jsx)("span",{className:"text-sm font-medium text-gray-700 capitalize",children:e._id.replace("_"," ")}),(0,k.jsxs)("div",{className:"flex items-center",children:[(0,k.jsx)("div",{className:"w-24 bg-gray-200 rounded-full h-2 mr-3",children:(0,k.jsx)("div",{className:"h-2 rounded-full bg-blue-500",style:{width:"".concat(100*e.avgConfidence,"%")}})}),(0,k.jsxs)("span",{className:"text-sm font-semibold text-gray-900",children:[Math.round(100*e.avgConfidence),"%"]})]})]},e._id))})]})]}),(0,k.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,k.jsxs)(A.A,{className:"p-6",children:[(0,k.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,k.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e("highRiskPatients","High Risk Patients")}),(0,k.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})]}),(0,k.jsx)("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:c.length>0?c.map(e=>(0,k.jsx)(E,{patient:e.patient,analysis:e},e._id)):(0,k.jsx)("p",{className:"text-gray-500 text-center py-4",children:e("noHighRiskPatients","No high-risk patients found")})})]}),(0,k.jsxs)(A.A,{className:"p-6",children:[(0,k.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,k.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e("pendingAlerts","Pending Alerts")}),(0,k.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})]}),(0,k.jsx)("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:h.length>0?h.map(e=>e.clinical_alerts.filter(e=>!e.dismissed).map((t,a)=>(0,k.jsx)(P,{alert:(0,s.A)((0,s.A)({},t),{},{index:a}),analysis:e,onDismiss:R},"".concat(e._id,"-").concat(a)))):(0,k.jsx)("p",{className:"text-gray-500 text-center py-4",children:e("noPendingAlerts","No pending alerts")})})]})]})]})}},2593:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(2555),r=a(3986),i=(a(5043),a(579));const n=["children","variant","size","disabled","loading","className","type","onClick"],l=e=>{let{children:t,variant:a="primary",size:l="md",disabled:o=!1,loading:c=!1,className:d="",type:m="button",onClick:h}=e,g=(0,r.A)(e,n);const u={primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500",warning:"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500",ghost:"hover:bg-gray-100 text-gray-700 focus:ring-gray-500"},x={xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm",md:"px-4 py-2 text-sm",lg:"px-4 py-2 text-base",xl:"px-6 py-3 text-base"},y=["inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200",u[a]||u.primary,x[l]||x.md,o||c?"opacity-50 cursor-not-allowed":"",d].filter(Boolean).join(" ");return(0,i.jsxs)("button",(0,s.A)((0,s.A)({type:m,className:y,onClick:e=>{o||c?e.preventDefault():h&&h(e)},disabled:o||c},g),{},{children:[c&&(0,i.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,i.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,i.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),t]}))}},2903:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(3986),r=a(5043);const i=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,l=(0,s.A)(e,i);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const l=r.forwardRef(n)},3099:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(2555),r=a(3986),i=(a(5043),a(579));const n=["children","className","padding","shadow","border","rounded","background","hover"],l=e=>{let{children:t,className:a="",padding:l="p-6",shadow:o="shadow-sm",border:c="border border-gray-200",rounded:d="rounded-lg",background:m="bg-white",hover:h=""}=e,g=(0,r.A)(e,n);const u=[m,c,d,o,l,h,a].filter(Boolean).join(" ");return(0,i.jsx)("div",(0,s.A)((0,s.A)({className:u},g),{},{children:t}))}},3315:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(3986),r=a(5043);const i=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,l=(0,s.A)(e,i);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const l=r.forwardRef(n)},3986:(e,t,a)=>{function s(e,t){if(null==e)return{};var a,s,r=function(e,t){if(null==e)return{};var a={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;a[s]=e[s]}return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)a=i[s],-1===t.indexOf(a)&&{}.propertyIsEnumerable.call(e,a)&&(r[a]=e[a])}return r}a.d(t,{A:()=>s})},7012:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(3986),r=a(5043);const i=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,l=(0,s.A)(e,i);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const l=r.forwardRef(n)},7504:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(3986),r=a(5043);const i=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,l=(0,s.A)(e,i);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}const l=r.forwardRef(n)},9399:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(3986),r=a(5043);const i=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,l=(0,s.A)(e,i);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const l=r.forwardRef(n)},9942:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(3986),r=a(5043);const i=["title","titleId"];function n(e,t){let{title:a,titleId:n}=e,l=(0,s.A)(e,i);return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},l),a?r.createElement("title",{id:n},a):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}const l=r.forwardRef(n)}}]);
//# sourceMappingURL=832.68070519.chunk.js.map