{"version": 3, "file": "static/js/9920.c58f1c32.chunk.js", "mappings": "mMAIA,MAyOA,EAzOsBA,KACpB,MAAOC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,KACpCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,OAC7B,KAAEK,IAASC,EAAAA,EAAAA,MAEjBC,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAAgBC,UACpBP,GAAW,GACXE,EAAS,MAET,IACEM,QAAQC,IAAI,wBACZD,QAAQC,IAAI,QAASN,GACrBK,QAAQC,IAAI,SAAUC,aAAaC,QAAQ,UAE3C,MAAMC,QAAiBC,MAAM,4BAA6B,CACxDC,OAAQ,MACRC,QAAS,CACP,cAAgB,UAADC,OAAYN,aAAaC,QAAQ,UAChD,eAAgB,sBAOpB,GAHAH,QAAQC,IAAI,mBAAoBG,EAASK,QACzCT,QAAQC,IAAI,oBAAqBG,EAASG,UAEtCH,EAASM,GAKN,CACL,MAAMC,QAAkBP,EAASQ,OAEjC,MADAZ,QAAQP,MAAM,kBAAmBkB,GAC3B,IAAIE,MAAM,QAADL,OAASJ,EAASK,OAAM,MAAAD,OAAKG,GAC9C,CATiB,CAAC,IAADG,EACf,MAAMC,QAAaX,EAASY,OAC5BhB,QAAQC,IAAI,kBAAmBc,GAC/B1B,EAAa0B,EAAKA,MAAQ,IAC1BE,EAAAA,GAAMC,QAAQ,UAADV,QAAoB,QAATM,EAAAC,EAAKA,YAAI,IAAAD,OAAA,EAATA,EAAWK,SAAU,EAAC,cAChD,CAKF,CAAE,MAAO1B,GACPO,QAAQP,MAAM,2BAA4BA,GAC1CC,EAASD,EAAM2B,SACfH,EAAAA,GAAMxB,MAAM,6BAADe,OAA8Bf,EAAM2B,SACjD,CAAC,QACC5B,GAAW,EACb,GA+BF,OAAID,GAEA8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0BAAyBC,SAAC,4BACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uEACfE,EAAAA,EAAAA,KAAA,QAAAD,SAAM,+BAMV9B,GAEA4B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0BAAyBC,SAAC,4BACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iDAAgDC,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2BAA0BC,SAAC,6BACzCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBAAmBC,SAAE9B,KAClC+B,EAAAA,EAAAA,KAAA,UACEC,QAAS3B,EACTwB,UAAU,gEAA+DC,SAC1E,iBASPF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAAC,4BAGjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SAAC,iDAIrDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wDAAuDC,SAAA,EACpEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4BAA2BC,SAAC,gBAC1CF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,6BAA4BC,SAAA,CAAC,SAC7B,OAAJ5B,QAAI,IAAJA,OAAI,EAAJA,EAAM+B,UAAU,IAAM,OAAJ/B,QAAI,IAAJA,OAAI,EAAJA,EAAMgC,SAAS,KAAO,OAAJhC,QAAI,IAAJA,OAAI,EAAJA,EAAMiC,MAAM,QAEzDP,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CAAC,UAC3BrB,aAAaC,QAAQ,SAAW,UAAY,cAEtDkB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CAAC,qBAChBnC,EAAU+B,iBAKnCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wBAAuBC,SAAA,CAAC,wBAAsBnC,EAAU+B,OAAO,QAC7EK,EAAAA,EAAAA,KAAA,UACEC,QAAS3B,EACTwB,UAAU,6DAA4DC,SACvE,wBAKmB,IAArBnC,EAAU+B,QACTK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gBAAeC,SAAC,0BAG/BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEnC,EAAUyC,IAAKC,IAAQ,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACtBf,EAAAA,EAAAA,MAAA,OAEEC,UAAU,0FAAyFC,SAAA,EAEnGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,SAAEO,EAASO,QACtDb,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBAAuBC,SAAEO,EAASQ,iBAEhDR,EAASS,YACRf,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wDAAuDC,SAAC,gBAM5EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gBAAeC,SAAC,WAChCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEO,EAASU,WAE1CnB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gBAAeC,SAAC,eAChCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEO,EAASW,eAE1CpB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gBAAeC,SAAC,YAChCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEO,EAASY,WAAW,gBAErDrB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gBAAeC,SAAC,cAChCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEO,EAASa,iBAI5CtB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAxID1B,WACtB,IACEC,QAAQC,IAAI,kBAAmB2C,GAE/B,MAAMxC,QAAiBC,MAAM,6BAADG,OAA8BoC,EAAU,QAAQ,CAC1EtC,OAAQ,OACRC,QAAS,CACP,cAAgB,UAADC,OAAYN,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIC,EAASM,GAKN,CACL,MAAMC,QAAkBP,EAASQ,OAEjC,MADAZ,QAAQP,MAAM,sBAAuBkB,GAC/B,IAAIE,MAAM,QAADL,OAASJ,EAASK,OAAM,MAAAD,OAAKG,GAC9C,CATiB,CACf,MAAMI,QAAaX,EAASY,OAC5BhB,QAAQC,IAAI,yBAA0Bc,GACtCE,EAAAA,GAAMC,QAAQ,+BACdpB,GACF,CAKF,CAAE,MAAOL,GACPO,QAAQP,MAAM,wBAAyBA,GACvCwB,EAAAA,GAAMxB,MAAM,2BAADe,OAA4Bf,EAAM2B,SAC/C,GA+G6ByB,CAAgBf,EAASgB,KACxCxB,UAAU,4EAA2EC,SACtF,uBAIDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMzB,QAAQC,IAAI,oBAAqB6B,GAChDR,UAAU,wEAAuEC,SAClF,iBAGDC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMsB,UAAUC,UAAUC,UAAUnB,EAASgB,KACtDxB,UAAU,wEAAuEC,SAClF,mBAOLF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxB,QAAfQ,EAAAD,EAASoB,cAAM,IAAAnB,GAAY,QAAZC,EAAfD,EAAiBoB,kBAAU,IAAAnB,OAAZ,EAAfA,EAA6BoB,kBAC5B5B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wDAAuDC,SAAC,WAI1D,QAAfU,EAAAH,EAASoB,cAAM,IAAAjB,GAAY,QAAZC,EAAfD,EAAiBkB,kBAAU,IAAAjB,OAAZ,EAAfA,EAA6BmB,mBAC5B7B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sDAAqDC,SAAC,YAIxD,QAAfY,EAAAL,EAASoB,cAAM,IAAAf,GAAY,QAAZC,EAAfD,EAAiBgB,kBAAU,IAAAf,OAAZ,EAAfA,EAA6BkB,8BAC5B9B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,0DAAyDC,SAAC,gBAvEzEO,EAASgB,Y", "sources": ["pages/Financial/TestTemplates.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst TestTemplates = () => {\n  const [templates, setTemplates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const { user } = useAuth();\n\n  useEffect(() => {\n    loadTemplates();\n  }, []);\n\n  const loadTemplates = async () => {\n    setLoading(true);\n    setError(null);\n    \n    try {\n      console.log('Loading templates...');\n      console.log('User:', user);\n      console.log('Token:', localStorage.getItem('token'));\n\n      const response = await fetch('/api/v1/invoice-templates', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      console.log('Response status:', response.status);\n      console.log('Response headers:', response.headers);\n\n      if (response.ok) {\n        const data = await response.json();\n        console.log('Templates data:', data);\n        setTemplates(data.data || []);\n        toast.success(`Loaded ${data.data?.length || 0} templates`);\n      } else {\n        const errorData = await response.text();\n        console.error('Error response:', errorData);\n        throw new Error(`HTTP ${response.status}: ${errorData}`);\n      }\n    } catch (error) {\n      console.error('Error loading templates:', error);\n      setError(error.message);\n      toast.error(`Failed to load templates: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testUseTemplate = async (templateId) => {\n    try {\n      console.log('Using template:', templateId);\n      \n      const response = await fetch(`/api/v1/invoice-templates/${templateId}/use`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        console.log('Use template response:', data);\n        toast.success('Template used successfully!');\n        loadTemplates(); // Reload to see updated usage count\n      } else {\n        const errorData = await response.text();\n        console.error('Use template error:', errorData);\n        throw new Error(`HTTP ${response.status}: ${errorData}`);\n      }\n    } catch (error) {\n      console.error('Error using template:', error);\n      toast.error(`Failed to use template: ${error.message}`);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"p-6\">\n        <h1 className=\"text-2xl font-bold mb-4\">Test Invoice Templates</h1>\n        <div className=\"flex items-center\">\n          <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2\"></div>\n          <span>Loading templates...</span>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"p-6\">\n        <h1 className=\"text-2xl font-bold mb-4\">Test Invoice Templates</h1>\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n          <h3 className=\"text-red-800 font-medium\">Error Loading Templates</h3>\n          <p className=\"text-red-600 mt-1\">{error}</p>\n          <button\n            onClick={loadTemplates}\n            className=\"mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 max-w-6xl mx-auto\">\n      <div className=\"mb-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          Test Invoice Templates\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          Testing the invoice templates functionality\n        </p>\n        \n        <div className=\"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <h3 className=\"text-blue-800 font-medium\">Debug Info</h3>\n          <p className=\"text-blue-600 text-sm mt-1\">\n            User: {user?.firstName} {user?.lastName} ({user?.email})\n          </p>\n          <p className=\"text-blue-600 text-sm\">\n            Token: {localStorage.getItem('token') ? 'Present' : 'Missing'}\n          </p>\n          <p className=\"text-blue-600 text-sm\">\n            Templates loaded: {templates.length}\n          </p>\n        </div>\n      </div>\n\n      <div className=\"mb-4 flex justify-between items-center\">\n        <h2 className=\"text-lg font-semibold\">Available Templates ({templates.length})</h2>\n        <button\n          onClick={loadTemplates}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n        >\n          Reload Templates\n        </button>\n      </div>\n\n      {templates.length === 0 ? (\n        <div className=\"text-center py-8 bg-gray-50 rounded-lg\">\n          <p className=\"text-gray-600\">No templates found</p>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {templates.map((template) => (\n            <div\n              key={template._id}\n              className=\"bg-white border border-gray-200 rounded-lg p-4 shadow hover:shadow-md transition-shadow\"\n            >\n              <div className=\"flex items-start justify-between mb-3\">\n                <div>\n                  <h3 className=\"font-semibold text-gray-900\">{template.name}</h3>\n                  <p className=\"text-sm text-gray-600\">{template.description}</p>\n                </div>\n                {template.isDefault && (\n                  <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs rounded\">\n                    Default\n                  </span>\n                )}\n              </div>\n\n              <div className=\"space-y-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Type:</span>\n                  <span className=\"font-medium\">{template.type}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Category:</span>\n                  <span className=\"font-medium\">{template.category}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Usage:</span>\n                  <span className=\"font-medium\">{template.usageCount} times</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Version:</span>\n                  <span className=\"font-medium\">{template.version}</span>\n                </div>\n              </div>\n\n              <div className=\"mt-4 space-y-2\">\n                <button\n                  onClick={() => testUseTemplate(template._id)}\n                  className=\"w-full px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm\"\n                >\n                  Test Use Template\n                </button>\n                \n                <div className=\"grid grid-cols-2 gap-2\">\n                  <button\n                    onClick={() => console.log('Template details:', template)}\n                    className=\"px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm\"\n                  >\n                    Log Details\n                  </button>\n                  <button\n                    onClick={() => navigator.clipboard.writeText(template._id)}\n                    className=\"px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm\"\n                  >\n                    Copy ID\n                  </button>\n                </div>\n              </div>\n\n              {/* Compliance badges */}\n              <div className=\"mt-3 flex flex-wrap gap-1\">\n                {template.config?.compliance?.zatcaCompliant && (\n                  <span className=\"px-2 py-1 bg-green-100 text-green-700 text-xs rounded\">\n                    ZATCA\n                  </span>\n                )}\n                {template.config?.compliance?.nphiesCompliant && (\n                  <span className=\"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded\">\n                    NPHIES\n                  </span>\n                )}\n                {template.config?.compliance?.requireElectronicSignature && (\n                  <span className=\"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded\">\n                    E-Sign\n                  </span>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TestTemplates;\n"], "names": ["TestTemplates", "templates", "setTemplates", "useState", "loading", "setLoading", "error", "setError", "user", "useAuth", "useEffect", "loadTemplates", "async", "console", "log", "localStorage", "getItem", "response", "fetch", "method", "headers", "concat", "status", "ok", "errorData", "text", "Error", "_data$data", "data", "json", "toast", "success", "length", "message", "_jsxs", "className", "children", "_jsx", "onClick", "firstName", "lastName", "email", "map", "template", "_template$config", "_template$config$comp", "_template$config2", "_template$config2$com", "_template$config3", "_template$config3$com", "name", "description", "isDefault", "type", "category", "usageCount", "version", "templateId", "testUseTemplate", "_id", "navigator", "clipboard", "writeText", "config", "compliance", "zatcaCompliant", "nphiesCompliant", "requireElectronicSignature"], "sourceRoot": ""}