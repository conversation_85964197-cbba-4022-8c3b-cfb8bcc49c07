"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6570],{6570:(a,e,t)=>{t.r(e),t.d(e,{default:()=>d});var n=t(2555),s=t(5043),i=t(3216),r=t(7921),o=t(2287),c=t(3768),l=t(579);const d=()=>{var a;const{patientId:e}=(0,i.g)(),t=(0,i.Zp)(),d=(0,i.zy)(),{t:m}=(0,r.o)(),[f,g]=(0,s.useState)(!1),[p,u]=(0,s.useState)(null),[y,h]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{g(!0);try{var a,t;if(null!==(a=d.state)&&void 0!==a&&a.patient&&null!==(t=d.state)&&void 0!==t&&t.fromPatientProfile){const a=d.state.patient;u((0,n.A)({id:a._id||a.id,name:a.name||"".concat(a.firstName," ").concat(a.lastName),nameEn:a.nameEn||"".concat(a.firstName," ").concat(a.lastName),age:a.age,sex:"male"===a.gender?"M":"F",nationality:a.nationality||"Saudi Arabian",patientId:a.nationalId,fileNumber:a._id||a.id,phone:a.phone,email:a.email,address:a.address,emergencyContact:a.emergencyContact,medicalHistory:a.medicalHistory},a))}else if(e&&"new"!==e){const a=await fetch("".concat("http://localhost:5001/api/v1","/patients/").concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(a.ok){const e=await a.json();e.success&&u({id:e.data.id,name:e.data.fullName||"".concat(e.data.firstName," ").concat(e.data.lastName),age:e.data.age,sex:"male"===e.data.gender?"M":"F",nationality:e.data.nationality||"Saudi Arabian",patientId:e.data.nationalId,fileNumber:e.data.id})}}else u({id:"new",name:"",age:"",sex:"",nationality:"",patientId:"",fileNumber:""});const s=localStorage.getItem("pt_assessment_".concat(e));s&&h(JSON.parse(s))}catch(s){console.error("Error fetching patient data:",s),c.Ay.error(m("errorFetchingPatientData","Error fetching patient data"))}finally{g(!1)}})()},[e,d.state,m]);return f&&!p?(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-4",children:m("loadingPatientData","Loading patient data...")})]})}):(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,l.jsx)(o.A,{patientId:e,patientData:p,fromPatientProfile:null===(a=d.state)||void 0===a?void 0:a.fromPatientProfile,initialData:y,onSave:async a=>{try{g(!0),localStorage.setItem("pt_assessment_".concat(a.patientId||"new"),JSON.stringify(a)),c.Ay.success(m("assessmentSavedSuccessfully","Assessment saved successfully")),t("/patients")}catch(e){throw console.error("Error saving assessment:",e),c.Ay.error(m("errorSavingAssessment","Error saving assessment")),e}finally{g(!1)}},onCancel:()=>{t("/patients")}})})}}}]);
//# sourceMappingURL=6570.119a87e4.chunk.js.map