"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[146],{146:(e,t,r)=>{r.r(t),r.d(t,{default:()=>i});var a=r(2555),s=r(5043),o=r(7921),l=r(4528),n=r(3216),d=r(579);const i=()=>{const{t:e,isRTL:t}=(0,o.o)(),{user:r}=(0,l.A)(),{patientId:i}=(0,n.g)(),c=(0,n.Zp)(),[m,x]=(0,s.useState)(1),[p,g]=(0,s.useState)({documentNumber:"QP-".concat(Date.now()),issueDate:(new Date).toISOString().split("T")[0],version:"01",reviewNumber:"01",patientName:"",followUps:[{id:1,date:"",currentSituation:"",physiotherapistName:(null===r||void 0===r?void 0:r.name)||"",treatmentProposals:"",nextFollowUpDate:"",remarks:""}],submittedBy:(null===r||void 0===r?void 0:r.id)||"",submittedAt:(new Date).toISOString()}),[u,b]=(0,s.useState)(!1),[f,h]=(0,s.useState)({});(0,s.useEffect)(()=>{i&&w()},[i]);const w=async()=>{try{b(!0);const e=await fetch("/api/patients/".concat(i));if(e.ok){const t=await e.json();g(e=>(0,a.A)((0,a.A)({},e),{},{patientName:t.name||"".concat(t.firstName," ").concat(t.lastName)||""}))}}catch(e){console.error("Error loading patient data:",e)}finally{b(!1)}},k=(e,t)=>{g(r=>(0,a.A)((0,a.A)({},r),{},{[e]:t})),f[e]&&h(t=>(0,a.A)((0,a.A)({},t),{},{[e]:null}))},N=(e,t,r)=>{g(s=>(0,a.A)((0,a.A)({},s),{},{followUps:s.followUps.map((s,o)=>o===e?(0,a.A)((0,a.A)({},s),{},{[t]:r}):s)}));const s="followUp_".concat(e,"_").concat(t);f[s]&&h(e=>(0,a.A)((0,a.A)({},e),{},{[s]:null}))},j=()=>{p.followUps.length<4&&g(e=>(0,a.A)((0,a.A)({},e),{},{followUps:[...e.followUps,{id:e.followUps.length+1,date:"",currentSituation:"",physiotherapistName:(null===r||void 0===r?void 0:r.name)||"",treatmentProposals:"",nextFollowUpDate:"",remarks:""}]}))},y=e=>{p.followUps.length>1&&g(t=>(0,a.A)((0,a.A)({},t),{},{followUps:t.followUps.filter((t,r)=>r!==e)}))};return u?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 rounded-lg shadow-lg mb-6",children:(0,d.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,d.jsx)("div",{className:"px-6 py-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 bg-clip-text text-transparent",children:e("followUpPlanForDoctor","Follow Up Plan for Doctor")}),(0,d.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-emerald-500 mr-2"}),e("followUpPlanDescription","Comprehensive follow-up planning and progress tracking for medical review")]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 mt-3",children:[(0,d.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,d.jsx)("i",{className:"fas fa-clock text-blue-500 mr-1"}),e("estimatedTime","15-25 minutes")]}),(0,d.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,d.jsx)("i",{className:"fas fa-file-alt text-green-500 mr-1"}),e("pages","2 pages")]}),(0,d.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,d.jsx)("i",{className:"fas fa-repeat text-purple-500 mr-1"}),e("repeatableEntries","Up to 4 follow-ups")]})]})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-emerald-400 to-teal-400 text-white px-4 py-2 rounded-full shadow-lg",children:[(0,d.jsx)("i",{className:"fas fa-stethoscope mr-2"}),e("medical","Medical")]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-blue-400 to-indigo-400 text-white px-3 py-1 rounded-full text-sm shadow-md",children:[(0,d.jsx)("i",{className:"fas fa-calendar-check mr-1"}),e("followUp","Follow-up")]})]})]})})})}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-indigo-200 dark:border-indigo-700 p-4 mb-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("div",{className:"flex space-x-2",children:[1,2].map(t=>(0,d.jsxs)("button",{onClick:()=>x(t),className:"px-4 py-2 rounded-lg font-medium transition-all duration-200 ".concat(m===t?"bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg":"bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-600 hover:bg-indigo-50 dark:hover:bg-indigo-900/30"),children:[(0,d.jsx)("i",{className:"fas ".concat(1===t?"fa-user":"fa-calendar-alt"," mr-2")}),1===t?e("patientInfo","Patient Info & Follow-ups"):e("additionalFollowUps","Additional Follow-ups")]},t))}),(0,d.jsxs)("div",{className:"text-sm text-indigo-600 dark:text-indigo-400 font-medium",children:[e("page","Page")," ",m," ",e("of","of")," 2"]})]})}),(0,d.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),(()=>{const t={};return p.patientName.trim()||(t.patientName=e("patientNameRequired","Patient name is required")),p.followUps.forEach((r,a)=>{if(r.date){const s=new Date(r.date),o=new Date;o.setHours(0,0,0,0),s>o&&(t["followUp_".concat(a,"_date")]=e("dateMustBePastOrPresent","Date must be in the past or present"))}else t["followUp_".concat(a,"_date")]=e("dateRequired","Date is required");if(r.currentSituation.trim()||(t["followUp_".concat(a,"_currentSituation")]=e("currentSituationRequired","Current situation is required")),r.physiotherapistName.trim()||(t["followUp_".concat(a,"_physiotherapistName")]=e("physiotherapistNameRequired","Physiotherapist name is required")),r.treatmentProposals.trim()||(t["followUp_".concat(a,"_treatmentProposals")]=e("treatmentProposalsRequired","Treatment proposals are required")),r.nextFollowUpDate){const s=new Date(r.nextFollowUpDate),o=new Date;o.setHours(23,59,59,999),s<=o&&(t["followUp_".concat(a,"_nextFollowUpDate")]=e("nextDateMustBeFuture","Next follow-up date must be in the future"))}else t["followUp_".concat(a,"_nextFollowUpDate")]=e("nextFollowUpDateRequired","Next follow-up date is required")}),h(t),0===Object.keys(t).length})())try{b(!0);const t=(0,a.A)((0,a.A)({},p),{},{submittedBy:r.id,submittedAt:(new Date).toISOString()});if(!(await fetch("/api/v1/follow-up-plan/public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok)throw new Error("Failed to save follow-up plan");alert(e("followUpPlanSaved","Follow-up plan saved successfully!")),c(i?"/patients/".concat(i):"/patients")}catch(s){console.error("Error saving follow-up plan:",s),alert(e("errorSaving","Error saving follow-up plan. Please try again."))}finally{b(!1)}},className:"space-y-6",children:[1===m&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg shadow-lg border border-cyan-200 dark:border-cyan-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-cyan-900 dark:text-cyan-100 mb-4 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-file-alt text-cyan-600 dark:text-cyan-400 mr-2"}),e("documentInformation","Document Information")]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-cyan-800/20 border border-cyan-200 dark:border-cyan-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-cyan-800 dark:text-cyan-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-hashtag text-cyan-600 dark:text-cyan-400 mr-1"}),e("documentNumber","Document Number")]}),(0,d.jsx)("input",{type:"text",value:p.documentNumber,onChange:e=>k("documentNumber",e.target.value),className:"w-full px-2 py-1 border border-cyan-300 dark:border-cyan-600 rounded bg-cyan-50 dark:bg-cyan-700 text-cyan-900 dark:text-cyan-100",readOnly:!0})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1"}),e("issueDate","Issue Date")]}),(0,d.jsx)("input",{type:"date",value:p.issueDate,onChange:e=>k("issueDate",e.target.value),className:"w-full px-2 py-1 border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-code-branch text-indigo-600 dark:text-indigo-400 mr-1"}),e("version","Version")]}),(0,d.jsx)("input",{type:"text",value:p.version,className:"w-full px-2 py-1 border border-indigo-300 dark:border-indigo-600 rounded bg-indigo-50 dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100",readOnly:!0})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-3",children:[(0,d.jsxs)("label",{className:"block font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-eye text-purple-600 dark:text-purple-400 mr-1"}),e("reviewNumber","Review Number")]}),(0,d.jsx)("input",{type:"text",value:p.reviewNumber,className:"w-full px-2 py-1 border border-purple-300 dark:border-purple-600 rounded bg-purple-50 dark:bg-purple-700 text-purple-900 dark:text-purple-100",readOnly:!0})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-green-900 dark:text-green-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-user text-green-600 dark:text-green-400 mr-2"}),e("patientInformation","Patient Information")]}),(0,d.jsx)("div",{className:"bg-green-100 dark:bg-green-800/30 border border-green-300 dark:border-green-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-green-800 dark:text-green-200",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-green-600 dark:text-green-400 mr-2"}),e("patientInfoInstruction","Enter the patient's name for whom this follow-up plan is being created.")]})}),(0,d.jsxs)("div",{className:"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2",children:[(0,d.jsx)("i",{className:"fas fa-user-tag text-green-600 dark:text-green-400 mr-1"}),e("patientName","Patient Name")," *"]}),(0,d.jsx)("input",{type:"text",value:p.patientName,onChange:e=>k("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ".concat(f.patientName?"border-red-500":"border-green-300 dark:border-green-600"),placeholder:e("enterPatientName","Enter patient name..."),required:!0}),f.patientName&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f.patientName]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-orange-900 dark:text-orange-100 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar-check text-orange-600 dark:text-orange-400 mr-2"}),e("followUpEntries","Follow-up Entries")]}),p.followUps.length<4&&(0,d.jsxs)("button",{type:"button",onClick:j,className:"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("addFollowUp","Add Follow-up")]})]}),(0,d.jsx)("div",{className:"bg-orange-100 dark:bg-orange-800/30 border border-orange-300 dark:border-orange-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-orange-800 dark:text-orange-200",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-orange-600 dark:text-orange-400 mr-2"}),e("followUpInstruction","Document up to 4 follow-up sessions with detailed progress notes and treatment plans. Each entry should include current situation, treatment proposals, and next appointment scheduling.")]})}),(0,d.jsx)("div",{className:"space-y-6",children:p.followUps.slice(0,2).map((t,r)=>(0,d.jsxs)("div",{className:"bg-white dark:bg-orange-800/20 border border-orange-200 dark:border-orange-600 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("h3",{className:"text-lg font-medium text-orange-900 dark:text-orange-100 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-clipboard-list text-orange-600 dark:text-orange-400 mr-2"}),e("followUp","Follow-up")," ",r+1]}),p.followUps.length>1&&(0,d.jsxs)("button",{type:"button",onClick:()=>y(r),className:"px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md flex items-center text-sm",children:[(0,d.jsx)("i",{className:"fas fa-trash mr-1"}),e("remove","Remove")]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1"}),e("date","Date")," *"]}),(0,d.jsx)("input",{type:"date",value:t.date,onChange:e=>N(r,"date",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ".concat(f["followUp_".concat(r,"_date")]?"border-red-500":"border-blue-300 dark:border-blue-600"),required:!0}),f["followUp_".concat(r,"_date")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(r,"_date")]]})]}),(0,d.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-purple-600 dark:text-purple-400 mr-1"}),e("physiotherapistName","Physiotherapist Name")," *"]}),(0,d.jsx)("input",{type:"text",value:t.physiotherapistName,onChange:e=>N(r,"physiotherapistName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500 ".concat(f["followUp_".concat(r,"_physiotherapistName")]?"border-red-500":"border-purple-300 dark:border-purple-600"),placeholder:e("enterPhysiotherapistName","Enter physiotherapist name..."),required:!0}),f["followUp_".concat(r,"_physiotherapistName")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(r,"_physiotherapistName")]]})]})]}),(0,d.jsxs)("div",{className:"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-chart-line text-green-600 dark:text-green-400 mr-1"}),e("currentSituationOfPatient","Current Situation of the Patient")," *"]}),(0,d.jsx)("p",{className:"text-xs text-green-600 dark:text-green-400 mb-2",children:e("currentSituationHint","Include: Improvement-goals achieved, functional status, ROM-Muscle strength etc., compared to previous assessment")}),(0,d.jsx)("textarea",{value:t.currentSituation,onChange:e=>N(r,"currentSituation",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ".concat(f["followUp_".concat(r,"_currentSituation")]?"border-red-500":"border-green-300 dark:border-green-600"),rows:"4",placeholder:e("currentSituationPlaceholder","Describe the patient's current condition, progress made, functional improvements, ROM measurements, muscle strength changes, etc..."),required:!0}),f["followUp_".concat(r,"_currentSituation")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(r,"_currentSituation")]]})]}),(0,d.jsxs)("div",{className:"mt-6 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-prescription text-indigo-600 dark:text-indigo-400 mr-1"}),e("treatmentProposals","Treatment Proposals")," *"]}),(0,d.jsx)("textarea",{value:t.treatmentProposals,onChange:e=>N(r,"treatmentProposals",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500 ".concat(f["followUp_".concat(r,"_treatmentProposals")]?"border-red-500":"border-indigo-300 dark:border-indigo-600"),rows:"4",placeholder:e("treatmentProposalsPlaceholder","Outline recommended treatments, interventions, modifications to current plan, new exercises, equipment needs, etc..."),required:!0}),f["followUp_".concat(r,"_treatmentProposals")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(r,"_treatmentProposals")]]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6",children:[(0,d.jsxs)("div",{className:"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-teal-800 dark:text-teal-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar-plus text-teal-600 dark:text-teal-400 mr-1"}),e("nextFollowUpOutpatient","Next Follow Up (Outpatient)")," *"]}),(0,d.jsx)("input",{type:"date",value:t.nextFollowUpDate,onChange:e=>N(r,"nextFollowUpDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100 focus:ring-2 focus:ring-teal-500 ".concat(f["followUp_".concat(r,"_nextFollowUpDate")]?"border-red-500":"border-teal-300 dark:border-teal-600"),required:!0}),f["followUp_".concat(r,"_nextFollowUpDate")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(r,"_nextFollowUpDate")]]})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-sticky-note text-yellow-600 dark:text-yellow-400 mr-1"}),e("remarks","Remarks")]}),(0,d.jsx)("textarea",{value:t.remarks,onChange:e=>N(r,"remarks",e.target.value),className:"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500",rows:"3",placeholder:e("remarksPlaceholder","Additional notes, observations, or special considerations...")})]})]})]},t.id))})]})]}),2===m&&(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-rose-200 dark:border-rose-700 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-rose-900 dark:text-rose-100 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar-week text-rose-600 dark:text-rose-400 mr-2"}),e("additionalFollowUps","Additional Follow-up Entries")]}),p.followUps.length<4&&(0,d.jsxs)("button",{type:"button",onClick:j,className:"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("addFollowUp","Add Follow-up")]})]}),(0,d.jsx)("div",{className:"bg-rose-100 dark:bg-rose-800/30 border border-rose-300 dark:border-rose-600 rounded-lg p-4 mb-6",children:(0,d.jsxs)("p",{className:"text-sm text-rose-800 dark:text-rose-200",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-rose-600 dark:text-rose-400 mr-2"}),e("additionalFollowUpInstruction","Continue documenting follow-up sessions. Each entry builds upon previous assessments to track long-term progress and treatment effectiveness.")]})}),(0,d.jsxs)("div",{className:"space-y-6",children:[p.followUps.slice(2,4).map((t,r)=>{const a=r+2;return(0,d.jsxs)("div",{className:"bg-white dark:bg-rose-800/20 border border-rose-200 dark:border-rose-600 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsxs)("h3",{className:"text-lg font-medium text-rose-900 dark:text-rose-100 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-clipboard-list text-rose-600 dark:text-rose-400 mr-2"}),e("followUp","Follow-up")," ",a+1]}),(0,d.jsxs)("button",{type:"button",onClick:()=>y(a),className:"px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md flex items-center text-sm",children:[(0,d.jsx)("i",{className:"fas fa-trash mr-1"}),e("remove","Remove")]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1"}),e("date","Date")," *"]}),(0,d.jsx)("input",{type:"date",value:t.date,onChange:e=>N(a,"date",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ".concat(f["followUp_".concat(a,"_date")]?"border-red-500":"border-blue-300 dark:border-blue-600"),required:!0}),f["followUp_".concat(a,"_date")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(a,"_date")]]})]}),(0,d.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-purple-600 dark:text-purple-400 mr-1"}),e("physiotherapistName","Physiotherapist Name")," *"]}),(0,d.jsx)("input",{type:"text",value:t.physiotherapistName,onChange:e=>N(a,"physiotherapistName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500 ".concat(f["followUp_".concat(a,"_physiotherapistName")]?"border-red-500":"border-purple-300 dark:border-purple-600"),placeholder:e("enterPhysiotherapistName","Enter physiotherapist name..."),required:!0}),f["followUp_".concat(a,"_physiotherapistName")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(a,"_physiotherapistName")]]})]})]}),(0,d.jsxs)("div",{className:"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-chart-line text-green-600 dark:text-green-400 mr-1"}),e("currentSituationOfPatient","Current Situation of the Patient")," *"]}),(0,d.jsx)("p",{className:"text-xs text-green-600 dark:text-green-400 mb-2",children:e("currentSituationHint","Include: Improvement-goals achieved, functional status, ROM-Muscle strength etc., compared to previous assessment")}),(0,d.jsx)("textarea",{value:t.currentSituation,onChange:e=>N(a,"currentSituation",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ".concat(f["followUp_".concat(a,"_currentSituation")]?"border-red-500":"border-green-300 dark:border-green-600"),rows:"4",placeholder:e("currentSituationPlaceholder","Describe the patient's current condition, progress made, functional improvements, ROM measurements, muscle strength changes, etc..."),required:!0}),f["followUp_".concat(a,"_currentSituation")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(a,"_currentSituation")]]})]}),(0,d.jsxs)("div",{className:"mt-6 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-prescription text-indigo-600 dark:text-indigo-400 mr-1"}),e("treatmentProposals","Treatment Proposals")," *"]}),(0,d.jsx)("textarea",{value:t.treatmentProposals,onChange:e=>N(a,"treatmentProposals",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500 ".concat(f["followUp_".concat(a,"_treatmentProposals")]?"border-red-500":"border-indigo-300 dark:border-indigo-600"),rows:"4",placeholder:e("treatmentProposalsPlaceholder","Outline recommended treatments, interventions, modifications to current plan, new exercises, equipment needs, etc..."),required:!0}),f["followUp_".concat(a,"_treatmentProposals")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(a,"_treatmentProposals")]]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6",children:[(0,d.jsxs)("div",{className:"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-teal-800 dark:text-teal-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-calendar-plus text-teal-600 dark:text-teal-400 mr-1"}),e("nextFollowUpOutpatient","Next Follow Up (Outpatient)")," *"]}),(0,d.jsx)("input",{type:"date",value:t.nextFollowUpDate,onChange:e=>N(a,"nextFollowUpDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100 focus:ring-2 focus:ring-teal-500 ".concat(f["followUp_".concat(a,"_nextFollowUpDate")]?"border-red-500":"border-teal-300 dark:border-teal-600"),required:!0}),f["followUp_".concat(a,"_nextFollowUpDate")]&&(0,d.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),f["followUp_".concat(a,"_nextFollowUpDate")]]})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-sticky-note text-yellow-600 dark:text-yellow-400 mr-1"}),e("remarks","Remarks")]}),(0,d.jsx)("textarea",{value:t.remarks,onChange:e=>N(a,"remarks",e.target.value),className:"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500",rows:"3",placeholder:e("remarksPlaceholder","Additional notes, observations, or special considerations...")})]})]})]},t.id)}),p.followUps.length<3&&(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800/50 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center",children:[(0,d.jsx)("i",{className:"fas fa-plus-circle text-4xl text-gray-400 dark:text-gray-500 mb-4"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:e("noAdditionalFollowUps","No additional follow-ups added yet")}),(0,d.jsxs)("button",{type:"button",onClick:j,className:"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg flex items-center mx-auto",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("addFirstAdditionalFollowUp","Add Additional Follow-up")]})]})]})]})}),(0,d.jsx)("div",{className:"bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsxs)("button",{type:"button",onClick:()=>c(-1),className:"px-6 py-3 bg-gradient-to-r from-gray-500 to-slate-500 text-white rounded-lg hover:from-gray-600 hover:to-slate-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-arrow-left mr-2"}),e("cancel","Cancel")]}),m>1&&(0,d.jsxs)("button",{type:"button",onClick:()=>{m>1&&x(m-1)},className:"px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-chevron-left mr-2"}),e("previousPage","Previous Page")]})]}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("button",{type:"button",onClick:async()=>{try{b(!0);const t=(0,a.A)((0,a.A)({},p),{},{generatedAt:(new Date).toISOString(),generatedBy:r.name||r.email,patientId:i}),s=await fetch("/api/v1/follow-up-plan/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(t)});if(!s.ok)throw new Error("HTTP error! status: ".concat(s.status));{const t=await s.blob(),r=window.URL.createObjectURL(t),a=document.createElement("a");a.href=r,a.download="follow-up-plan-".concat(p.patientName.replace(/\s+/g,"-"),"-").concat(p.issueDate,".pdf"),document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(r),document.body.removeChild(a),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(t){console.error("Error generating PDF:",t),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{b(!1)}},disabled:u,className:"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg",children:[(0,d.jsx)("i",{className:"fas fa-file-pdf mr-2"}),u?e("generating","Generating..."):e("generatePDF","Generate PDF")]}),m<2?(0,d.jsxs)("button",{type:"button",onClick:()=>{m<2&&x(m+1)},className:"px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg flex items-center",children:[e("nextPage","Next Page"),(0,d.jsx)("i",{className:"fas fa-chevron-right ml-2"})]}):(0,d.jsxs)("button",{type:"submit",disabled:u,className:"px-8 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-lg hover:from-emerald-600 hover:to-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg",children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),u?e("saving","Saving..."):e("saveFollowUpPlan","Save Follow-up Plan")]})]})]})})]})]})}}}]);
//# sourceMappingURL=146.b7d966e3.chunk.js.map