{"version": 3, "file": "static/js/2359.ee8ba429.chunk.js", "mappings": "yLAGA,MAgNA,EAhNsBA,KACpB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACPC,EAAeC,IAAoBC,EAAAA,EAAAA,UAAS,CACjDC,WAAY,GACZC,iBAAkB,EAClBC,eAAgB,EAChBC,YAAa,EACbC,aAAc,GACdC,eAAgB,KA4BlB,OAzBAC,EAAAA,EAAAA,WAAU,KAsBRR,EApBiB,CACfE,WAAY,GACZC,iBAAkB,IAClBC,eAAgB,KAChBC,YAAa,KACbC,aAAc,CACZ,CAAEG,KAAM,8BAA+BC,YAAa,GAAIC,WAAY,MACpE,CAAEF,KAAM,sBAAuBC,YAAa,GAAIC,WAAY,MAC5D,CAAEF,KAAM,wBAAyBC,YAAa,GAAIC,WAAY,IAC9D,CAAEF,KAAM,yBAA0BC,YAAa,GAAIC,WAAY,MAC/D,CAAEF,KAAM,wBAAyBC,YAAa,GAAIC,WAAY,OAEhEJ,eAAgB,CACd,CAAEK,KAAM,8BAA+BC,QAAS,WAAYC,KAAM,cAAeC,OAAQ,aACzF,CAAEH,KAAM,sBAAuBC,QAAS,aAAcC,KAAM,cAAeC,OAAQ,aACnF,CAAEH,KAAM,wBAAyBC,QAAS,eAAgBC,KAAM,cAAeC,OAAQ,aACvF,CAAEH,KAAM,yBAA0BC,QAAS,eAAgBC,KAAM,cAAeC,OAAQ,aACxF,CAAEH,KAAM,iBAAkBC,QAAS,cAAeC,KAAM,YAAaC,OAAQ,iBAIhF,KAGDC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sFAAqFC,UAClGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6GAA4GC,UACzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6GAA4GC,SACvHrB,EAAE,gBAAiB,qBAEtBmB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,kEAAiEC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCACZpB,EAAE,2BAA4B,iEAGnCmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2FAA0FC,SAAA,EACvGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZpB,EAAE,YAAa,0BAQ1BmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4DAA2DC,SAAA,EACxEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8JAA6JC,UAC1KF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAAErB,EAAE,aAAc,kBACrFsB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDAAqDC,SAAEnB,EAAcG,sBAKxFiB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oKAAmKC,UAChLF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDAAwDC,SAAErB,EAAE,mBAAoB,wBAC7FsB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDAAuDC,SAAEnB,EAAcI,4BAK1FgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kKAAiKC,UAC9KF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DAA0DC,SAAErB,EAAE,iBAAkB,sBAC7FmB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,0DAAyDC,SAAA,CAAEnB,EAAcK,eAAe,gBAK3Ge,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gKAA+JC,UAC5KF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DAA0DC,SAAErB,EAAE,cAAe,gBAC1FmB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,0DAAyDC,SAAA,CAAEnB,EAAcM,YAAY,mBAM1GW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sKAAqKC,SAAA,EAClLF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sFAAqFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4DACZpB,EAAE,eAAgB,0BAGrBsB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBnB,EAAcO,aAAac,IAAI,CAACR,EAAMS,KACrCL,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,mGAAkGC,SAAA,EAC3HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qDAAoDC,SAAEN,EAAKH,QACzEO,EAAAA,EAAAA,MAAA,QAAMC,UAAU,iDAAgDC,SAAA,CAAEN,EAAKF,YAAY,sBAErFS,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6DAA4DC,UACzEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,6FACVK,MAAO,CAAEC,MAAM,GAADC,OAAKZ,EAAKD,WAAU,WAGtCK,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sDAAqDC,SAAA,CAAEN,EAAKD,WAAW,8BAX5EU,UAkBhBL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sKAAqKC,SAAA,EAClLF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,oFAAmFC,SAAA,EAC/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZpB,EAAE,iBAAkB,uBAGvBsB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBnB,EAAcQ,eAAea,IAAI,CAACK,EAAUJ,KAC3CF,EAAAA,EAAAA,KAAA,OAAiBF,UAAU,gGAA+FC,UACxHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAAEO,EAASb,QAC3EI,EAAAA,EAAAA,MAAA,KAAGC,UAAU,+CAA8CC,SAAA,CAAC,YAAUO,EAASZ,eAEjFG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+IAA8IC,SAAA,EAC5JC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZQ,EAASV,WAEZI,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDAAmDC,SAAEO,EAASX,cAXvEO,aAqBlBL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iKAAgKC,SAAA,EAC7KF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,gFAA+EC,SAAA,EAC3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4DACZpB,EAAE,qBAAsB,2BAG3BmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,UAClEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DAEfE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAErB,EAAE,cAAe,mBAC/EsB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDAAqDC,SAAC,QACnEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAErB,EAAE,WAAY,mBAGzEmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wDAAuDC,UACpEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mEAEfE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAErB,EAAE,eAAgB,oBAChFsB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDAAuDC,SAAC,QACrEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAErB,EAAE,YAAa,oBAG1EmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEAEfE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAErB,EAAE,iBAAkB,wBAClFsB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAAyDC,SAAC,WACvEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAErB,EAAE,UAAW,0B", "sources": ["pages/Forms/FormAnalytics.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst FormAnalytics = () => {\n  const { t } = useLanguage();\n  const [analyticsData, setAnalyticsData] = useState({\n    totalForms: 11,\n    totalSubmissions: 0,\n    completionRate: 0,\n    averageTime: 0,\n    popularForms: [],\n    recentActivity: []\n  });\n\n  useEffect(() => {\n    // Simulate loading analytics data\n    const mockData = {\n      totalForms: 11,\n      totalSubmissions: 156,\n      completionRate: 87.5,\n      averageTime: 18.5,\n      popularForms: [\n        { name: 'PT Adult Initial Assessment', submissions: 45, percentage: 28.8 },\n        { name: 'Daily Progress Note', submissions: 38, percentage: 24.4 },\n        { name: 'Pain Assessment Scale', submissions: 25, percentage: 16.0 },\n        { name: 'Treatment Plan & Goals', submissions: 22, percentage: 14.1 },\n        { name: 'Home Exercise Program', submissions: 18, percentage: 11.5 }\n      ],\n      recentActivity: [\n        { form: 'PT Adult Initial Assessment', patient: '<PERSON>', time: '2 hours ago', status: 'completed' },\n        { form: 'Daily Progress Note', patient: '<PERSON>', time: '4 hours ago', status: 'completed' },\n        { form: 'Pain Assessment Scale', patient: '<PERSON>', time: '6 hours ago', status: 'completed' },\n        { form: 'Treatment Plan & Goals', patient: 'Sarah Wilson', time: '8 hours ago', status: 'completed' },\n        { form: 'Follow Up Plan', patient: 'David Brown', time: '1 day ago', status: 'completed' }\n      ]\n    };\n    setAnalyticsData(mockData);\n  }, []);\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  {t('formAnalytics', 'Form Analytics')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-chart-bar text-blue-500 mr-2\"></i>\n                  {t('formAnalyticsDescription', 'Comprehensive insights into form usage and performance')}\n                </p>\n              </div>\n              <div className=\"bg-gradient-to-r from-blue-400 to-purple-400 text-white px-4 py-2 rounded-full shadow-lg\">\n                <i className=\"fas fa-analytics mr-2\"></i>\n                {t('analytics', 'Analytics')}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg\">\n              <i className=\"fas fa-file-alt text-blue-600 dark:text-blue-400 text-2xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-blue-600 dark:text-blue-400\">{t('totalForms', 'Total Forms')}</p>\n              <p className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">{analyticsData.totalForms}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg\">\n              <i className=\"fas fa-paper-plane text-green-600 dark:text-green-400 text-2xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-green-600 dark:text-green-400\">{t('totalSubmissions', 'Total Submissions')}</p>\n              <p className=\"text-2xl font-bold text-green-900 dark:text-green-100\">{analyticsData.totalSubmissions}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-purple-200 dark:border-purple-700 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg\">\n              <i className=\"fas fa-percentage text-purple-600 dark:text-purple-400 text-2xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-purple-600 dark:text-purple-400\">{t('completionRate', 'Completion Rate')}</p>\n              <p className=\"text-2xl font-bold text-purple-900 dark:text-purple-100\">{analyticsData.completionRate}%</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg\">\n              <i className=\"fas fa-clock text-orange-600 dark:text-orange-400 text-2xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-orange-600 dark:text-orange-400\">{t('averageTime', 'Avg. Time')}</p>\n              <p className=\"text-2xl font-bold text-orange-900 dark:text-orange-100\">{analyticsData.averageTime}m</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Popular Forms */}\n        <div className=\"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-emerald-900 dark:text-emerald-100 mb-6 flex items-center\">\n            <i className=\"fas fa-star text-emerald-600 dark:text-emerald-400 mr-2\"></i>\n            {t('popularForms', 'Most Popular Forms')}\n          </h2>\n          \n          <div className=\"space-y-4\">\n            {analyticsData.popularForms.map((form, index) => (\n              <div key={index} className=\"bg-white dark:bg-emerald-800/20 border border-emerald-200 dark:border-emerald-600 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <h3 className=\"font-medium text-emerald-900 dark:text-emerald-100\">{form.name}</h3>\n                  <span className=\"text-sm text-emerald-600 dark:text-emerald-400\">{form.submissions} submissions</span>\n                </div>\n                <div className=\"w-full bg-emerald-200 dark:bg-emerald-800 rounded-full h-2\">\n                  <div \n                    className=\"bg-gradient-to-r from-emerald-500 to-teal-500 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${form.percentage}%` }}\n                  ></div>\n                </div>\n                <p className=\"text-xs text-emerald-600 dark:text-emerald-400 mt-1\">{form.percentage}% of total submissions</p>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Recent Activity */}\n        <div className=\"bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-violet-200 dark:border-violet-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-violet-900 dark:text-violet-100 mb-6 flex items-center\">\n            <i className=\"fas fa-history text-violet-600 dark:text-violet-400 mr-2\"></i>\n            {t('recentActivity', 'Recent Activity')}\n          </h2>\n          \n          <div className=\"space-y-4\">\n            {analyticsData.recentActivity.map((activity, index) => (\n              <div key={index} className=\"bg-white dark:bg-violet-800/20 border border-violet-200 dark:border-violet-600 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"font-medium text-violet-900 dark:text-violet-100\">{activity.form}</h3>\n                    <p className=\"text-sm text-violet-600 dark:text-violet-400\">Patient: {activity.patient}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200\">\n                      <i className=\"fas fa-check-circle mr-1\"></i>\n                      {activity.status}\n                    </span>\n                    <p className=\"text-xs text-violet-500 dark:text-violet-400 mt-1\">{activity.time}</p>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Additional Analytics */}\n      <div className=\"mt-8 bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center\">\n          <i className=\"fas fa-chart-line text-gray-600 dark:text-gray-400 mr-2\"></i>\n          {t('additionalInsights', 'Additional Insights')}\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"text-center\">\n            <div className=\"p-4 bg-blue-100 dark:bg-blue-900/40 rounded-lg mb-3\">\n              <i className=\"fas fa-users text-blue-600 dark:text-blue-400 text-3xl\"></i>\n            </div>\n            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('activeUsers', 'Active Users')}</h3>\n            <p className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">24</p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('thisWeek', 'This week')}</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"p-4 bg-green-100 dark:bg-green-900/40 rounded-lg mb-3\">\n              <i className=\"fas fa-download text-green-600 dark:text-green-400 text-3xl\"></i>\n            </div>\n            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('pdfDownloads', 'PDF Downloads')}</h3>\n            <p className=\"text-2xl font-bold text-green-600 dark:text-green-400\">89</p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('thisMonth', 'This month')}</p>\n          </div>\n          \n          <div className=\"text-center\">\n            <div className=\"p-4 bg-purple-100 dark:bg-purple-900/40 rounded-lg mb-3\">\n              <i className=\"fas fa-clock text-purple-600 dark:text-purple-400 text-3xl\"></i>\n            </div>\n            <h3 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('avgSessionTime', 'Avg. Session Time')}</h3>\n            <p className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">12.3m</p>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('perForm', 'Per form')}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FormAnalytics;\n"], "names": ["FormAnalytics", "t", "useLanguage", "analyticsData", "setAnalyticsData", "useState", "totalForms", "totalSubmissions", "completionRate", "averageTime", "popularForms", "recentActivity", "useEffect", "name", "submissions", "percentage", "form", "patient", "time", "status", "_jsxs", "className", "children", "_jsx", "map", "index", "style", "width", "concat", "activity"], "sourceRoot": ""}