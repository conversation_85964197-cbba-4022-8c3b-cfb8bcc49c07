{"version": 3, "file": "static/js/9932.ccfa9754.chunk.js", "mappings": "uNAKA,MAmYA,EAnY0BA,KACxB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAEvCC,UAAW,GACXC,SAAU,GACVC,YAAa,GACbC,OAAQ,GACRC,cAAe,GACfC,WAAY,GACZC,YAAa,GACbC,MAAO,GACPC,QAAS,GACTC,KAAM,GACNC,QAAS,GAGTC,qBAAsB,GACtBC,yBAA0B,GAC1BC,sBAAuB,GAGvBC,kBAAmB,GACnBC,aAAc,GACdC,YAAa,GACbC,eAAgB,GAGhBC,iBAAkB,GAClBC,mBAAoB,GACpBC,eAAgB,GAChBC,mBAAoB,GACpBC,UAAW,GACXC,kBAAmB,GACnBC,kBAAmB,GAGnBC,UAAW,EACXC,aAAc,GACdC,gBAAiB,GACjBC,mBAAoB,GACpBC,sBAAuB,GAGvBC,kBAAkB,EAClBC,gBAAgB,EAChBC,kBAAkB,EAClBC,sBAAsB,EAGtBC,aAAc,GACdC,eAAgB,GAChBC,kBAAmB,UACnBC,mBAAmB,KAGdC,EAASC,IAAcxC,EAAAA,EAAAA,WAAS,IAChCyC,EAAQC,IAAa1C,EAAAA,EAAAA,UAAS,CAAC,IAC/B2C,EAAeC,IAAoB5C,EAAAA,EAAAA,UAAS,YASzBX,EAAE,OAAQ,QACRA,EAAE,SAAU,UACbA,EAAE,QAAS,SAIVA,EAAE,SAAU,UACXA,EAAE,UAAW,WACZA,EAAE,WAAY,YACfA,EAAE,UAAW,YAG1CwD,EAAAA,EAAAA,WAAU,KACJnD,GACFoD,KAED,CAACpD,IAEJ,MAAMoD,EAAkBC,UACtB,IACEP,GAAW,GACX,MAAMQ,QAAiBC,MAAM,iBAADC,OAAkBxD,IAC9C,GAAIsD,EAASG,GAAI,CACf,MAAMC,QAAoBJ,EAASK,OACnCtD,EAAYuD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPrD,UAAWmD,EAAYnD,WAAa,GACpCC,SAAUkD,EAAYlD,UAAY,GAClCC,YAAaiD,EAAYjD,aAAe,GACxCI,YAAa6C,EAAY7C,aAAe,GACxCC,MAAO4C,EAAY5C,OAAS,KAEhC,CACF,CAAE,MAAOgD,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACChB,GAAW,EACb,GAGIkB,EAAoBA,CAACC,EAAOC,KAChC7D,EAAYuD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACK,GAAQC,KAGPnB,EAAOkB,IACTjB,EAAUY,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACK,GAAQ,SA6Hf,OAAIpB,GAEAsB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D1E,EAAE,oBAAqB,0BAE1BwE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD1E,EAAE,wBAAyB,0DAGhCwE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,kGAAiGC,SAAA,EAC/GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BACZzE,EAAE,wBAAyB,wCAQtCwE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAsB,aAAW,OAAMC,SACnD,CACC,CAAEE,GAAI,WAAYC,MAAO7E,EAAE,sBAAuB,yBAClD,CAAE4E,GAAI,UAAWC,MAAO7E,EAAE,iBAAkB,oBAC5C,CAAE4E,GAAI,WAAYC,MAAO7E,EAAE,kBAAmB,qBAC9C,CAAE4E,GAAI,UAAWC,MAAO7E,EAAE,eAAgB,mBAC1C8E,IAAKC,IACLP,EAAAA,EAAAA,KAAA,UAEEQ,QAASA,IAAMzB,EAAiBwB,EAAIH,IACpCH,UAAS,4CAAAZ,OACPP,IAAkByB,EAAIH,GAClB,mDACA,0HACHF,SAEFK,EAAIF,OARAE,EAAIH,YAgBnBD,EAAAA,EAAAA,MAAA,QAAMM,SAlGWvB,UAGnB,GAFAwB,EAAEC,iBAzEiBC,MACnB,MAAMC,EAAY,CAAC,EAyBnB,OAtBK5E,EAASG,UAAU0E,SAAQD,EAAUzE,UAAYZ,EAAE,oBAAqB,2BACxES,EAASI,SAASyE,SAAQD,EAAUxE,SAAWb,EAAE,mBAAoB,0BACrES,EAASK,cAAauE,EAAUvE,YAAcd,EAAE,sBAAuB,8BACvES,EAASM,SAAQsE,EAAUtE,OAASf,EAAE,iBAAkB,uBACxDS,EAASS,YAAYoE,SAAQD,EAAUnE,YAAclB,EAAE,gBAAiB,6BACxES,EAASuB,eAAesD,SAAQD,EAAUrD,eAAiBhC,EAAE,yBAA0B,gCAGvFS,EAASiC,mBAAkB2C,EAAU3C,iBAAmB1C,EAAE,2BAA4B,kCACtFS,EAASkC,iBAAgB0C,EAAU1C,eAAiB3C,EAAE,yBAA0B,gCAGjFS,EAASU,QAAU,eAAeoE,KAAK9E,EAASU,SAClDkE,EAAUlE,MAAQnB,EAAE,eAAgB,yBAIlCS,EAASS,cAAgB,qBAAqBqE,KAAK9E,EAASS,eAC9DmE,EAAUnE,YAAclB,EAAE,eAAgB,gCAG5CqD,EAAUgC,GAC+B,IAAlCG,OAAOC,KAAKJ,GAAWK,QAiDzBN,GAIL,IACEjC,GAAW,GAEX,MAAMwC,GAAczB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfzD,GAAQ,IACXmF,YAAazF,EAAKyE,GAClBiB,aAAa,IAAIC,MAAOC,cACxB1F,UAAWA,IAWb,WARuBuD,MAAM,gCAAiC,CAC5DoC,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAUT,MAGV7B,GAIX,MAAM,IAAIuC,MAAM,8BAHhBC,MAAMtG,EAAE,kBAAmB,4CAC3BO,EAASF,EAAS,aAAAwD,OAAgBxD,GAAc,YAIpD,CAAE,MAAO8D,GACPC,QAAQD,MAAM,4BAA6BA,GAC3CmC,MAAMtG,EAAE,cAAe,+CACzB,CAAC,QACCmD,GAAW,EACb,GA8DgCsB,UAAU,YAAWC,SAAA,CAE9B,aAAlBpB,IACCqB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE1E,EAAE,sBAAuB,2BAG5B2E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E1E,EAAE,YAAa,cAAc,SAEhCwE,EAAAA,EAAAA,KAAA,SACE+B,KAAK,OACLhC,MAAO9D,EAASG,UAChB4F,SAAWtB,GAAMb,EAAkB,YAAaa,EAAEuB,OAAOlC,OACzDE,UAAS,8FAAAZ,OACPT,EAAOxC,UAAY,iBAAmB,wCAExC8F,UAAQ,IAETtD,EAAOxC,YACN4D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEtB,EAAOxC,gBAIrD+D,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E1E,EAAE,WAAY,aAAa,SAE9BwE,EAAAA,EAAAA,KAAA,SACE+B,KAAK,OACLhC,MAAO9D,EAASI,SAChB2F,SAAWtB,GAAMb,EAAkB,WAAYa,EAAEuB,OAAOlC,OACxDE,UAAS,8FAAAZ,OACPT,EAAOvC,SAAW,iBAAmB,wCAEvC6F,UAAQ,IAETtD,EAAOvC,WACN2D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEtB,EAAOvC,qBAQ3D8D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,UACE+B,KAAK,SACLvB,QAASA,IAAMzE,GAAU,GACzBkE,UAAU,4IAA2IC,SAEpJ1E,EAAE,SAAU,aAGf2E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACE4B,KAAK,SACLvB,QA1MQtB,UAClB,IACEP,GAAW,GAEX,MAAMwD,GAAOzC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRzD,GAAQ,IACXmG,aAAa,IAAId,MAAOC,cACxBc,YAAa1G,EAAK2G,MAAQ3G,EAAKgB,MAC/Bd,UAAWA,IAGPsD,QAAiBC,MAAM,6BAA8B,CACzDoC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADpC,OAAYkD,aAAaC,QAAQ,WAElDd,KAAMC,KAAKC,UAAUO,KAGvB,IAAIhD,EAASG,GAaX,MAAM,IAAIuC,MAAM,uBAADxC,OAAwBF,EAASsD,SAbjC,CACf,MAAMC,QAAavD,EAASuD,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,kBAAA9D,OAAqBpD,EAASG,UAAS,KAAAiD,OAAIpD,EAASI,SAAQ,KAAAgD,QAAI,IAAIiC,MAAOC,cAAc6B,MAAM,KAAK,GAAE,QAChHJ,SAAStB,KAAK2B,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAAStB,KAAK8B,YAAYT,GAE1BjB,MAAMtG,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAOmE,GACPC,QAAQD,MAAM,wBAAyBA,GACvCmC,MAAMtG,EAAE,qBAAsB,2CAChC,CAAC,QACCmD,GAAW,EACb,GAmKU8E,SAAU/E,EACVuB,UAAU,gIAA+HC,SAAA,EAEzIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZvB,EAAUlD,EAAE,aAAc,iBAAmBA,EAAE,cAAe,oBAGjEwE,EAAAA,EAAAA,KAAA,UACE+B,KAAK,SACL0B,SAAU/E,EACVuB,UAAU,gHAA+GC,SAExHxB,EAAUlD,EAAE,SAAU,aAAeA,EAAE,iBAAkB,kC", "sources": ["pages/Forms/PatientIntakeForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nconst PatientIntakeForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [formData, setFormData] = useState({\n    // Personal Information\n    firstName: '',\n    lastName: '',\n    dateOfBirth: '',\n    gender: '',\n    maritalStatus: '',\n    occupation: '',\n    phoneNumber: '',\n    email: '',\n    address: '',\n    city: '',\n    zipCode: '',\n    \n    // Emergency Contact\n    emergencyContactName: '',\n    emergencyContactRelation: '',\n    emergencyContactPhone: '',\n    \n    // Insurance Information\n    insuranceProvider: '',\n    policyNumber: '',\n    groupNumber: '',\n    subscriberName: '',\n    \n    // Medical History\n    primaryPhysician: '',\n    referringPhysician: '',\n    chiefComplaint: '',\n    currentMedications: '',\n    allergies: '',\n    previousSurgeries: '',\n    medicalConditions: [],\n    \n    // Current Symptoms\n    painLevel: 0,\n    painLocation: '',\n    symptomDuration: '',\n    symptomDescription: '',\n    functionalLimitations: '',\n    \n    // Consent and Agreements\n    treatmentConsent: false,\n    privacyConsent: false,\n    financialConsent: false,\n    communicationConsent: false,\n    \n    // Special Needs\n    specialNeeds: '',\n    accommodations: '',\n    preferredLanguage: 'English',\n    interpreterNeeded: false\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [activeSection, setActiveSection] = useState('personal');\n\n  const medicalConditionOptions = [\n    'Diabetes', 'Hypertension', 'Heart Disease', 'Arthritis', 'Osteoporosis',\n    'Cancer', 'Stroke', 'Neurological Disorders', 'Respiratory Conditions',\n    'Mental Health Conditions', 'Other'\n  ];\n\n  const genderOptions = [\n    { value: 'male', label: t('male', 'Male'), labelAr: 'ذكر' },\n    { value: 'female', label: t('female', 'Female'), labelAr: 'أنثى' },\n    { value: 'other', label: t('other', 'Other'), labelAr: 'آخر' }\n  ];\n\n  const maritalStatusOptions = [\n    { value: 'single', label: t('single', 'Single'), labelAr: 'أعزب' },\n    { value: 'married', label: t('married', 'Married'), labelAr: 'متزوج' },\n    { value: 'divorced', label: t('divorced', 'Divorced'), labelAr: 'مطلق' },\n    { value: 'widowed', label: t('widowed', 'Widowed'), labelAr: 'أرمل' }\n  ];\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        setFormData(prev => ({\n          ...prev,\n          firstName: patientData.firstName || '',\n          lastName: patientData.lastName || '',\n          dateOfBirth: patientData.dateOfBirth || '',\n          phoneNumber: patientData.phoneNumber || '',\n          email: patientData.email || ''\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleCheckboxChange = (field, option, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: checked \n        ? [...prev[field], option]\n        : prev[field].filter(item => item !== option)\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.firstName.trim()) newErrors.firstName = t('firstNameRequired', 'First name is required');\n    if (!formData.lastName.trim()) newErrors.lastName = t('lastNameRequired', 'Last name is required');\n    if (!formData.dateOfBirth) newErrors.dateOfBirth = t('dateOfBirthRequired', 'Date of birth is required');\n    if (!formData.gender) newErrors.gender = t('genderRequired', 'Gender is required');\n    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = t('phoneRequired', 'Phone number is required');\n    if (!formData.chiefComplaint.trim()) newErrors.chiefComplaint = t('chiefComplaintRequired', 'Chief complaint is required');\n    \n    // Consent validation\n    if (!formData.treatmentConsent) newErrors.treatmentConsent = t('treatmentConsentRequired', 'Treatment consent is required');\n    if (!formData.privacyConsent) newErrors.privacyConsent = t('privacyConsentRequired', 'Privacy consent is required');\n\n    // Email validation\n    if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n      newErrors.email = t('invalidEmail', 'Invalid email format');\n    }\n\n    // Phone validation\n    if (formData.phoneNumber && !/^\\+?[\\d\\s\\-\\(\\)]+$/.test(formData.phoneNumber)) {\n      newErrors.phoneNumber = t('invalidPhone', 'Invalid phone number format');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n\n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/patient-intake/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `patient-intake-${formData.firstName}-${formData.lastName}-${new Date().toISOString().split('T')[0]}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n\n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      const submissionData = {\n        ...formData,\n        submittedBy: user.id,\n        submittedAt: new Date().toISOString(),\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/patient-intake/public', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        alert(t('intakeFormSaved', 'Patient intake form saved successfully!'));\n        navigate(patientId ? `/patients/${patientId}` : '/patients');\n      } else {\n        throw new Error('Failed to save intake form');\n      }\n    } catch (error) {\n      console.error('Error saving intake form:', error);\n      alert(t('errorSaving', 'Error saving intake form. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('patientIntakeForm', 'Patient Intake Form')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                {t('intakeFormDescription', 'Complete patient information and medical history')}\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm rounded-full\">\n                <i className=\"fas fa-puzzle-piece mr-1\"></i>\n                {t('specialNeedsSupported', 'Special Needs Supported')}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n            {[\n              { id: 'personal', label: t('personalInformation', 'Personal Information') },\n              { id: 'medical', label: t('medicalHistory', 'Medical History') },\n              { id: 'symptoms', label: t('currentSymptoms', 'Current Symptoms') },\n              { id: 'consent', label: t('consentForms', 'Consent Forms') }\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveSection(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeSection === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Personal Information Section */}\n        {activeSection === 'personal' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('personalInformation', 'Personal Information')}\n            </h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('firstName', 'First Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.firstName}\n                  onChange={(e) => handleInputChange('firstName', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.firstName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.firstName && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.firstName}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('lastName', 'Last Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.lastName}\n                  onChange={(e) => handleInputChange('lastName', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.lastName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.lastName && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.lastName}</p>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between\">\n          <button\n            type=\"button\"\n            onClick={() => navigate(-1)}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n\n          <div className=\"flex space-x-3\">\n            <button\n              type=\"button\"\n              onClick={generatePDF}\n              disabled={loading}\n              className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n            >\n              <i className=\"fas fa-file-pdf mr-2\"></i>\n              {loading ? t('generating', 'Generating...') : t('generatePDF', 'Generate PDF')}\n            </button>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? t('saving', 'Saving...') : t('saveIntakeForm', 'Save Intake Form')}\n            </button>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default PatientIntakeForm;\n"], "names": ["PatientIntakeForm", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "formData", "setFormData", "useState", "firstName", "lastName", "dateOfBirth", "gender", "maritalStatus", "occupation", "phoneNumber", "email", "address", "city", "zipCode", "emergencyContactName", "emergencyContactRelation", "emergencyContactPhone", "insuranceProvider", "policyNumber", "groupNumber", "subscriberName", "primaryPhysician", "referringPhysician", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentMedications", "allergies", "previousSurgeries", "medicalConditions", "painLevel", "painLocation", "symptomDuration", "symptomDescription", "functionalLimitations", "treatmentConsent", "privacyConsent", "financialConsent", "communicationConsent", "specialNeeds", "accommodations", "preferredLanguage", "<PERSON><PERSON><PERSON><PERSON>", "loading", "setLoading", "errors", "setErrors", "activeSection", "setActiveSection", "useEffect", "loadPatientData", "async", "response", "fetch", "concat", "ok", "patientData", "json", "prev", "_objectSpread", "error", "console", "handleInputChange", "field", "value", "_jsx", "className", "children", "_jsxs", "id", "label", "map", "tab", "onClick", "onSubmit", "e", "preventDefault", "validateForm", "newErrors", "trim", "test", "Object", "keys", "length", "submissionData", "submittedBy", "submittedAt", "Date", "toISOString", "method", "headers", "body", "JSON", "stringify", "Error", "alert", "type", "onChange", "target", "required", "pdfData", "generatedAt", "generatedBy", "name", "localStorage", "getItem", "status", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "split", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "disabled"], "sourceRoot": ""}