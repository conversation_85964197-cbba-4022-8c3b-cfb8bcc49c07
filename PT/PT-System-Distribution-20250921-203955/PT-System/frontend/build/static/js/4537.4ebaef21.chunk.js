"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[4537],{4537:(e,s,r)=>{r.r(s),r.d(s,{default:()=>d});var a=r(2555),t=r(5043),l=r(5475),n=r(4528),i=r(7921),c=r(2015),o=r(6761),m=r(579);const d=()=>{const{login:e,loading:s}=(0,n.A)(),{t:r,isRTL:d,toggleLanguage:x}=(0,i.o)(),{theme:h,toggleTheme:u}=(0,c.D)(),[p,f]=(0,t.useState)({username:"",password:"",rememberMe:!1}),[b,g]=(0,t.useState)({}),[y,j]=(0,t.useState)(!1),w=e=>{const{name:s,value:r,type:t,checked:l}=e.target;f(e=>(0,a.A)((0,a.A)({},e),{},{[s]:"checkbox"===t?l:r})),b[s]&&g(e=>(0,a.A)((0,a.A)({},e),{},{[s]:""}))};return(0,m.jsxs)("div",{className:"min-h-screen flex ".concat(d?"font-arabic":"font-english"),children:[(0,m.jsx)("div",{className:"flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24",children:(0,m.jsxs)("div",{className:"mx-auto w-full max-w-sm lg:w-96",children:[(0,m.jsxs)("div",{className:"text-center mb-8",children:[(0,m.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,m.jsx)("i",{className:"fas fa-heartbeat text-white text-2xl"})}),(0,m.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:r("login")}),(0,m.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:d?"\u0646\u0638\u0627\u0645 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a":"Physical Therapy Management System"})]}),(0,m.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800",children:[(0,m.jsx)("h3",{className:"text-sm font-medium text-blue-900 dark:text-blue-300 mb-2",children:d?"\u0628\u064a\u0627\u0646\u0627\u062a \u062a\u062c\u0631\u064a\u0628\u064a\u0629:":"Demo Credentials:"}),(0,m.jsx)("div",{className:"space-y-2",children:[{username:"<EMAIL>",password:"Admin123!",role:"Administrator",name:"Admin User"},{username:"<EMAIL>",password:"Doctor123!",role:"Doctor",name:"Dr. Ahmed Al-Rashid"},{username:"<EMAIL>",password:"Therapist123!",role:"Physical Therapist",name:"Sarah Al-Zahra"}].map((e,s)=>(0,m.jsxs)("button",{onClick:()=>{return s=e,void f(e=>(0,a.A)((0,a.A)({},e),{},{username:s.username,password:s.password}));var s},className:"w-full text-left p-2 text-xs bg-white dark:bg-gray-800 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors",children:[(0,m.jsxs)("div",{className:"font-medium text-blue-900 dark:text-blue-300",children:[e.name," (",e.role,")"]}),(0,m.jsx)("div",{className:"text-blue-600 dark:text-blue-400",children:e.username})]},s))})]}),(0,m.jsxs)("form",{onSubmit:async s=>{if(s.preventDefault(),(()=>{const e={};return p.username.trim()?p.username.includes("@")||(e.username=r("invalidEmail")):e.username=r("required"),p.password?p.password.length<6&&(e.password=r("passwordTooShort")):e.password=r("required"),g(e),0===Object.keys(e).length})())try{await e({username:p.username,password:p.password,rememberMe:p.rememberMe})}catch(a){console.error("Login error:",a)}},className:"space-y-6",children:[(0,m.jsxs)("div",{children:[(0,m.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:r("username")}),(0,m.jsxs)("div",{className:"relative",children:[(0,m.jsx)("input",{id:"username",name:"username",type:"email",autoComplete:"username",required:!0,value:p.username,onChange:w,className:"form-input ".concat(b.username?"border-red-500 focus:border-red-500 focus:ring-red-500":""," ").concat(d?"text-right":"text-left"),placeholder:d?"\u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a":"Email address"}),(0,m.jsx)("div",{className:"absolute inset-y-0 ".concat(d?"left-0 pl-3":"right-0 pr-3"," flex items-center"),children:(0,m.jsx)("i",{className:"fas fa-user text-gray-400"})})]}),b.username&&(0,m.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:b.username})]}),(0,m.jsxs)("div",{children:[(0,m.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:r("password")}),(0,m.jsxs)("div",{className:"relative",children:[(0,m.jsx)("input",{id:"password",name:"password",type:y?"text":"password",autoComplete:"current-password",required:!0,value:p.password,onChange:w,className:"form-input ".concat(b.password?"border-red-500 focus:border-red-500 focus:ring-red-500":""," ").concat(d?"text-right pr-10":"text-left pr-10"),placeholder:d?"\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631":"Password"}),(0,m.jsx)("button",{type:"button",onClick:()=>j(!y),className:"absolute inset-y-0 ".concat(d?"left-0 pl-3":"right-0 pr-3"," flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"),children:(0,m.jsx)("i",{className:"fas ".concat(y?"fa-eye-slash":"fa-eye")})})]}),b.password&&(0,m.jsx)("p",{className:"mt-1 text-sm text-red-600 dark:text-red-400",children:b.password})]}),(0,m.jsxs)("div",{className:"flex items-center justify-between ".concat(d?"flex-row-reverse":""),children:[(0,m.jsxs)("div",{className:"flex items-center",children:[(0,m.jsx)("input",{id:"rememberMe",name:"rememberMe",type:"checkbox",checked:p.rememberMe,onChange:w,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,m.jsx)("label",{htmlFor:"rememberMe",className:"".concat(d?"mr-2":"ml-2"," block text-sm text-gray-700 dark:text-gray-300"),children:r("rememberMe")})]}),(0,m.jsx)(l.N_,{to:"/forgot-password",className:"text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300",children:r("forgotPassword")})]}),(0,m.jsx)("button",{type:"submit",disabled:s,className:"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:s?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(o.az,{className:"mr-2"}),d?"\u062c\u0627\u0631\u064a \u062a\u0633\u062c\u064a\u0644 \u0627\u0644\u062f\u062e\u0648\u0644...":"Signing in..."]}):(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("i",{className:"fas fa-sign-in-alt ".concat(d?"ml-2":"mr-2")}),r("login")]})}),(0,m.jsxs)("button",{type:"button",onClick:()=>{f({username:"",password:"",rememberMe:!1}),g({})},className:"w-full flex justify-center items-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors",children:[(0,m.jsx)("i",{className:"fas fa-eraser ".concat(d?"ml-2":"mr-2")}),d?"\u0645\u0633\u062d \u0627\u0644\u0646\u0645\u0648\u0630\u062c":"Clear Form"]})]}),(0,m.jsxs)("div",{className:"mt-8 text-center",children:[(0,m.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,m.jsx)("button",{onClick:x,className:"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:d?"English":"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"}),(0,m.jsx)("button",{onClick:u,className:"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:(0,m.jsx)("i",{className:"fas ".concat("light"===h?"fa-moon":"fa-sun")})})]}),(0,m.jsxs)("p",{className:"mt-4 text-xs text-gray-500 dark:text-gray-400",children:["\xa9 2024 PT System. ",d?"\u062c\u0645\u064a\u0639 \u0627\u0644\u062d\u0642\u0648\u0642 \u0645\u062d\u0641\u0648\u0638\u0629.":"All rights reserved."]})]})]})}),(0,m.jsx)("div",{className:"hidden lg:block relative w-0 flex-1",children:(0,m.jsxs)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary-600 to-primary-800",children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-black opacity-20"}),(0,m.jsx)("div",{className:"relative h-full flex flex-col justify-center items-center text-white p-12",children:(0,m.jsxs)("div",{className:"text-center",children:[(0,m.jsx)("div",{className:"w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-8",children:(0,m.jsx)("i",{className:"fas fa-heartbeat text-4xl"})}),(0,m.jsx)("h1",{className:"text-4xl font-bold mb-4",children:d?"\u0646\u0638\u0627\u0645 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a":"PT Management System"}),(0,m.jsx)("p",{className:"text-xl opacity-90 mb-8",children:d?"\u062d\u0644\u0648\u0644 \u0634\u0627\u0645\u0644\u0629 \u0644\u0625\u062f\u0627\u0631\u0629 \u0639\u064a\u0627\u062f\u0627\u062a \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a \u0641\u064a \u0627\u0644\u0645\u0645\u0644\u0643\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629 \u0627\u0644\u0633\u0639\u0648\u062f\u064a\u0629":"Comprehensive solutions for physical therapy clinics in Saudi Arabia"}),(0,m.jsx)("div",{className:"grid grid-cols-1 gap-4 max-w-md mx-auto",children:[{icon:"fas fa-users",text:d?"\u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0645\u0631\u0636\u0649":"Patient Management"},{icon:"fas fa-calendar",text:d?"\u062c\u062f\u0648\u0644\u0629 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f":"Appointment Scheduling"},{icon:"fas fa-file-medical",text:d?"\u0627\u0644\u0646\u0645\u0627\u0630\u062c \u0627\u0644\u0637\u0628\u064a\u0629":"Medical Forms"},{icon:"fas fa-chart-line",text:d?"\u0627\u0644\u062a\u0642\u0627\u0631\u064a\u0631 \u0648\u0627\u0644\u062a\u062d\u0644\u064a\u0644\u0627\u062a":"Reports & Analytics"}].map((e,s)=>(0,m.jsxs)("div",{className:"flex items-center space-x-3 text-left",children:[(0,m.jsx)("i",{className:"".concat(e.icon," text-lg opacity-80")}),(0,m.jsx)("span",{className:"text-sm opacity-90",children:e.text})]},s))})]})})]})})]})}}}]);
//# sourceMappingURL=4537.4ebaef21.chunk.js.map