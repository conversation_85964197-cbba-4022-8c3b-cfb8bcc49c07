{"version": 3, "file": "static/js/3070.1a733a74.chunk.js", "mappings": "gMAIA,MAmWA,EAnWwBA,IAAiD,IAAhD,SAAEC,EAAQ,SAAEC,EAAQ,YAAEC,EAAc,MAAMH,EACjE,MAAM,EAAEI,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAEhCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CACvCG,WAAsB,OAAXT,QAAW,IAAXA,OAAW,EAAXA,EAAaS,YAAa,GACrCC,aAAwB,OAAXV,QAAW,IAAXA,OAAW,EAAXA,EAAaU,cAAe,GACzCC,MAAiB,OAAXX,QAAW,IAAXA,OAAW,EAAXA,EAAaW,QAAQ,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC/DC,MAAiB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAae,OAAQ,GAC3BC,UAAqB,OAAXhB,QAAW,IAAXA,OAAW,EAAXA,EAAagB,WAAY,KACnCC,MAAiB,OAAXjB,QAAW,IAAXA,OAAW,EAAXA,EAAaiB,OAAQ,eAC3BC,WAAsB,OAAXlB,QAAW,IAAXA,OAAW,EAAXA,EAAakB,YAAa,GACrCC,UAAqB,OAAXnB,QAAW,IAAXA,OAAW,EAAXA,EAAamB,WAAY,SACnCC,OAAkB,OAAXpB,QAAW,IAAXA,OAAW,EAAXA,EAAaoB,QAAS,GAC7BC,UAAqB,OAAXrB,QAAW,IAAXA,OAAW,EAAXA,EAAaqB,WAAY,SACnCC,iBAA4B,OAAXtB,QAAW,IAAXA,OAAW,EAAXA,EAAasB,mBAAmB,EACjDC,cAAyB,OAAXvB,QAAW,IAAXA,OAAW,EAAXA,EAAauB,eAAgB,QAGtCC,EAAQC,IAAanB,EAAAA,EAAAA,UAAS,CAAC,GAGhCoB,EAAmB,CACvB,CAAEC,MAAO,eAAgBC,MAAO3B,EAAE,eAAgB,iBAClD,CAAE0B,MAAO,aAAcC,MAAO3B,EAAE,aAAc,eAC9C,CAAE0B,MAAO,kBAAmBC,MAAO3B,EAAE,iBAAkB,oBACvD,CAAE0B,MAAO,YAAaC,MAAO3B,EAAE,WAAY,cAC3C,CAAE0B,MAAO,gBAAiBC,MAAO3B,EAAE,eAAgB,kBACnD,CAAE0B,MAAO,aAAcC,MAAO3B,EAAE,aAAc,gBAU1C4B,EAAY,CAChB,CAAEF,MAAO,SAAUC,MAAO3B,EAAE,QAAS,0BACrC,CAAE0B,MAAO,SAAUC,MAAO3B,EAAE,QAAS,wBACrC,CAAE0B,MAAO,SAAUC,MAAO3B,EAAE,QAAS,2BACrC,CAAE0B,MAAO,MAAOC,MAAO3B,EAAE,MAAO,cAChC,CAAE0B,MAAO,OAAQC,MAAO3B,EAAE,OAAQ,uBAU9B6B,EAAoBA,CAACC,EAAOJ,KAChCnB,EAAYwB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACD,GAAQJ,KACrCH,EAAOO,IACTN,EAAUO,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACD,GAAQ,SAiD3C,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEpC,EAAcC,EAAE,kBAAmB,oBAAsBA,EAAE,iBAAkB,qBAE/EF,IACCsC,EAAAA,EAAAA,KAAA,UACEC,QAASvC,EACToC,UAAU,6DAA4DC,UAEtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+BAKnBD,EAAAA,EAAAA,MAAA,QAAMpC,SAzCWyC,UAGnB,GAFAC,EAAEC,iBArBiBC,MACnB,MAAMC,EAAY,CAAC,EAgBnB,OAdKpC,EAASG,YAAYkC,SACxBD,EAAUjC,YAAcT,EAAE,sBAAuB,6BAE9CM,EAASI,OACZgC,EAAUhC,KAAOV,EAAE,eAAgB,qBAEhCM,EAASQ,OACZ4B,EAAU5B,KAAOd,EAAE,eAAgB,qBAEhCM,EAASW,YACZyB,EAAUzB,UAAYjB,EAAE,oBAAqB,0BAG/CwB,EAAUkB,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWI,QAMzBL,GAAL,CAKArC,GAAW,GACX,UAEQ,IAAI2C,QAAQC,GAAWC,WAAWD,EAAS,MAE7CnD,GACFA,EAASS,GAGX4C,EAAAA,GAAMC,QAAQnD,EAAE,mBAAoB,kCACtC,CAAE,MAAOoD,GACPF,EAAAA,GAAME,MAAMpD,EAAE,yBAA0B,4BAC1C,CAAC,QACCI,GAAW,EACb,CAhBA,MAFE8C,EAAAA,GAAME,MAAMpD,EAAE,kBAAmB,6CAqCHkC,UAAU,YAAWC,SAAA,EAEjDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,cAAe,gBAAgB,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEpB,KAAK,OACLU,MAAOpB,EAASG,YAChB4C,SAAWd,GAAMV,EAAkB,cAAeU,EAAEe,OAAO5B,OAC3DQ,UAAS,mJAAAqB,OACPhC,EAAOd,YAAc,iBAAmB,mBAE1C+C,YAAaxD,EAAE,mBAAoB,wBAEpCuB,EAAOd,cACN2B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAOd,kBAIrDwB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,kBAAmB,uBAExBoC,EAAAA,EAAAA,KAAA,UACEV,MAAOpB,EAASU,KAChBqC,SAAWd,GAAMV,EAAkB,OAAQU,EAAEe,OAAO5B,OACpDQ,UAAU,kKAAiKC,SAE1KV,EAAiBgC,IAAIzC,IACpBoB,EAAAA,EAAAA,KAAA,UAAyBV,MAAOV,EAAKU,MAAMS,SACxCnB,EAAKW,OADKX,EAAKU,iBAS1BO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,OAAQ,QAAQ,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAErDC,EAAAA,EAAAA,KAAA,SACEpB,KAAK,OACLU,MAAOpB,EAASI,KAChB2C,SAAWd,GAAMV,EAAkB,OAAQU,EAAEe,OAAO5B,OACpDQ,UAAS,mJAAAqB,OACPhC,EAAOb,KAAO,iBAAmB,qBAGpCa,EAAOb,OACN0B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAOb,WAIrDuB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,OAAQ,QAAQ,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAErDF,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASQ,KAChBuC,SAAWd,GAAMV,EAAkB,OAAQU,EAAEe,OAAO5B,OACpDQ,UAAS,mJAAAqB,OACPhC,EAAOT,KAAO,iBAAmB,mBAChCqB,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,GAAES,SAAEnC,EAAE,aAAc,iBAhJ5B,CAChB,QAAS,QAAS,QAAS,QAAS,QAAS,QAC7C,QAAS,QAAS,QAAS,QAAS,QAAS,QAC7C,QAAS,QAAS,QAAS,QAAS,QAAS,QAC7C,QAAS,QAAS,SA6IGyD,IAAI3C,IACbsB,EAAAA,EAAAA,KAAA,UAAmBV,MAAOZ,EAAKqB,SAC5BrB,GADUA,OAKhBS,EAAOT,OACNsB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAOT,WAIrDmB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,yBAEjBiC,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASS,SAChBsC,SAAWd,GAAMV,EAAkB,WAAYU,EAAEe,OAAO5B,OACxDQ,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,MAAKS,SAAA,CAAC,OAAKnC,EAAE,UAAW,wBAM5CiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,YAAa,aAAa,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DF,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASW,UAChBoC,SAAWd,GAAMV,EAAkB,YAAaU,EAAEe,OAAO5B,OACzDQ,UAAS,mJAAAqB,OACPhC,EAAON,UAAY,iBAAmB,mBACrCkB,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,GAAES,SAAEnC,EAAE,kBAAmB,sBA1MhC,CACjB,CAAE0B,MAAO,WAAYC,MAAO,uBAC5B,CAAED,MAAO,WAAYC,MAAO,yBAC5B,CAAED,MAAO,YAAaC,MAAO,uBAC7B,CAAED,MAAO,cAAeC,MAAO,2BAuMT8B,IAAIxC,IACdmB,EAAAA,EAAAA,KAAA,UAA8BV,MAAOT,EAAUS,MAAMS,SAClDlB,EAAUU,OADAV,EAAUS,WAK1BH,EAAON,YACNmB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAON,gBAIrDgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,eAEjBoC,EAAAA,EAAAA,KAAA,UACEV,MAAOpB,EAASY,SAChBmC,SAAWd,GAAMV,EAAkB,WAAYU,EAAEe,OAAO5B,OACxDQ,UAAU,kKAAiKC,SAE1KP,EAAU6B,IAAIvC,IACbkB,EAAAA,EAAAA,KAAA,UAA6BV,MAAOR,EAASQ,MAAMS,SAChDjB,EAASS,OADCT,EAASQ,iBAS9BO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,eAEjBiC,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASc,SAChBiC,SAAWd,GAAMV,EAAkB,WAAYU,EAAEe,OAAO5B,OACxDQ,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,MAAKS,SAAEnC,EAAE,MAAO,UAC9BoC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAEnC,EAAE,SAAU,aACpCoC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,OAAMS,SAAEnC,EAAE,OAAQ,WAChCoC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAEnC,EAAE,SAAU,mBAIxCiC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,eAEjBiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,SACEpB,KAAK,WACL0C,QAASpD,EAASe,gBAClBgC,SAAWd,GAAMV,EAAkB,kBAAmBU,EAAEe,OAAOI,SAC/DxB,UAAU,+DAEZD,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASgB,aAChB+B,SAAWd,GAAMV,EAAkB,eAAgBU,EAAEe,OAAO5B,OAC5DiC,UAAWrD,EAASe,gBACpBa,UAAU,sLAAqLC,SAAA,EAE/LF,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,gBAAiB,sBAC1CiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,gBAAiB,sBAC1CiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,KAAGnC,EAAE,aAAc,mBACtCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,OAAMS,SAAA,CAAC,KAAGnC,EAAE,YAAa,8BAO/CiC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,QAAS,YAEdoC,EAAAA,EAAAA,KAAA,YACEV,MAAOpB,EAASa,MAChBkC,SAAWd,GAAMV,EAAkB,QAASU,EAAEe,OAAO5B,OACrDkC,KAAM,EACN1B,UAAU,kKACVsB,YAAaxD,EAAE,aAAc,oCAKjCiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,CACxCrC,IACCsC,EAAAA,EAAAA,KAAA,UACEpB,KAAK,SACLqB,QAASvC,EACToC,UAAU,+FAA8FC,SAEvGnC,EAAE,SAAU,aAGjBoC,EAAAA,EAAAA,KAAA,UACEpB,KAAK,SACL2C,SAAUxD,EACV+B,UAAU,kIAAiIC,SAE1IhC,GACC8B,EAAAA,EAAAA,MAAA4B,EAAAA,SAAA,CAAA1B,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZlC,EAAE,SAAU,iBAGfiC,EAAAA,EAAAA,MAAA4B,EAAAA,SAAA,CAAA1B,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,kBAAmB,kC,0FC1VtC,MAuNA,EAvN4BJ,IAA8D,IAA7D,aAAEkE,EAAe,GAAE,aAAEC,EAAY,mBAAEC,GAAoBpE,EAClF,MAAM,EAAEI,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACd+D,EAAaC,IAAkB7D,EAAAA,EAAAA,UAAS,IAAIM,OAC5CwD,EAAcC,IAAmB/D,EAAAA,EAAAA,UAAS,MAG3CgE,EAAeJ,EAAYK,WAC3BC,EAAcN,EAAYO,cAG1BC,EAAkB,IAAI9D,KAAK4D,EAAaF,EAAc,GACtDK,EAAiB,IAAI/D,KAAK4D,EAAaF,EAAe,EAAG,GACzDM,EAAkBF,EAAgBG,SAClCC,EAAcH,EAAeI,UAG7BC,EAAe,GAGrB,IAAK,IAAIC,EAAI,EAAGA,EAAIL,EAAiBK,IACnCD,EAAaE,KAAK,MAIpB,IAAK,IAAIC,EAAM,EAAGA,GAAOL,EAAaK,IACpCH,EAAaE,KAAKC,GAIpB,MAAMC,EAAa,CACjBnF,EAAE,UAAW,WAAYA,EAAE,WAAY,YAAaA,EAAE,QAAS,SAC/DA,EAAE,QAAS,SAAUA,EAAE,MAAO,OAAQA,EAAE,OAAQ,QAChDA,EAAE,OAAQ,QAASA,EAAE,SAAU,UAAWA,EAAE,YAAa,aACzDA,EAAE,UAAW,WAAYA,EAAE,WAAY,YAAaA,EAAE,WAAY,aAI9DoF,EAAW,CACfpF,EAAE,SAAU,OAAQA,EAAE,SAAU,OAAQA,EAAE,UAAW,OACrDA,EAAE,YAAa,OAAQA,EAAE,WAAY,OAAQA,EAAE,SAAU,OAAQA,EAAE,WAAY,QAI3EqF,EAAiBC,IACrB,MAAMC,EAAU,IAAI5E,KAAKsD,GACzBsB,EAAQC,SAASnB,EAAeiB,GAChCpB,EAAeqB,IAIXE,EAA0BP,IAC9B,IAAKA,EAAK,MAAO,GACjB,MAAMQ,EAAO,GAAAnC,OAAMgB,EAAW,KAAAhB,OAAIoC,OAAOtB,EAAe,GAAGuB,SAAS,EAAG,KAAI,KAAArC,OAAIoC,OAAOT,GAAKU,SAAS,EAAG,MACvG,OAAO9B,EAAa+B,OAAOC,GAAOA,EAAIpF,OAASgF,IAc3CK,EAAWb,IACf,MAAMc,EAAQ,IAAIrF,KAClB,OAAOuE,IAAQc,EAAMlB,WACdT,IAAiB2B,EAAM1B,YACvBC,IAAgByB,EAAMxB,eAIzByB,EAAcf,IAClB,IAAKA,IAAQf,EAAc,OAAO,EAElC,MADa,GAAAZ,OAAMgB,EAAW,KAAAhB,OAAIoC,OAAOtB,EAAe,GAAGuB,SAAS,EAAG,KAAI,KAAArC,OAAIoC,OAAOT,GAAKU,SAAS,EAAG,QACpFzB,GAGrB,OACElC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EAEtGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sFAAqFC,SAAA,EAClGC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMgD,GAAe,GAC9BnD,UAAU,4EAA2EC,UAErFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,kBAAAqB,OAAoBtD,EAAQ,QAAU,OAAM,0CAG1DgC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,CAChEgD,EAAWd,GAAc,IAAEE,MAG9BnC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMgD,EAAc,GAC7BnD,UAAU,4EAA2EC,UAErFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,kBAAAqB,OAAoBtD,EAAQ,OAAS,QAAO,6CAK5DgC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAElBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,SACzCiD,EAAS3B,IAAI,CAACyB,EAAKgB,KAClB9D,EAAAA,EAAAA,KAAA,OAAiBF,UAAU,uEAAsEC,SAC9F+C,GADOgB,OAOd9D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBC,SACpC4C,EAAatB,IAAI,CAACyB,EAAKgB,KACtB,MAAMC,EAAkBV,EAAuBP,GACzCkB,EAAkBD,EAAgBrD,OAAS,EAEjD,OACEV,EAAAA,EAAAA,KAAA,OAEEC,QAASA,IAnEE6C,KACvB,IAAKA,EAAK,OACV,MAAMQ,EAAO,GAAAnC,OAAMgB,EAAW,KAAAhB,OAAIoC,OAAOtB,EAAe,GAAGuB,SAAS,EAAG,KAAI,KAAArC,OAAIoC,OAAOT,GAAKU,SAAS,EAAG,MACvGxB,EAAgBsB,GACZ3B,GACFA,EAAa2B,IA8DYW,CAAgBnB,GAC/BhD,UAAS,iJAAAqB,OAEL2B,EAAM,0CAA4C,iBAAgB,wBAAA3B,OAClEwC,EAAQb,GAAO,sEAAwE,GAAE,wBAAA3B,OACzF0C,EAAWf,GAAO,uEAAyE,GAAE,wBAAA3B,OAC5F2B,EAAsC,GAAhC,8BAAkC,sBAC3C/C,SAED+C,IACCjD,EAAAA,EAAAA,MAAA4B,EAAAA,SAAA,CAAA1B,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uBAAAqB,OAAyBwC,EAAQb,GAAO,mCAAqC,iCAAkC/C,SAC1H+C,IAGFkB,IACCnE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,CAC5BgE,EAAgBG,MAAM,EAAG,GAAG7C,IAAI,CAAC8C,EAAaC,KAC7CpE,EAAAA,EAAAA,KAAA,OAEEC,QAAUE,IACRA,EAAEkE,kBACEzC,GACFA,EAAmBuC,IAGvBrE,UAAU,kGACVwE,MAAK,GAAAnD,OAAKgD,EAAYzF,KAAI,OAAAyC,OAAMgD,EAAY9F,aAAc0B,SAEzDoE,EAAYzF,MAVR0F,IAaRL,EAAgBrD,OAAS,IACxBb,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CAAC,IACtDgE,EAAgBrD,OAAS,EAAE,IAAE9C,EAAE,OAAQ,kBAnChDkG,UAiDd/B,IACClC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,CACnEnC,EAAE,kBAAmB,oBAAoB,IAAEmE,KAG7CsB,EAAuBkB,SAASxC,EAAatD,MAAM,KAAK,KAAKiC,OAAS,GACrEV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBsD,EAAuBkB,SAASxC,EAAatD,MAAM,KAAK,KAAK4C,IAAI,CAAC8C,EAAaL,KAC9EjE,EAAAA,EAAAA,MAAA,OAEEI,QAASA,IAAM2B,GAAsBA,EAAmBuC,GACxDrE,UAAU,yJAAwJC,SAAA,EAElKF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DoE,EAAY9F,eAEfwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDoE,EAAYzF,KAAK,MAAIyF,EAAYvF,YAGtCoB,EAAAA,EAAAA,KAAA,OAAKF,UAAS,kCAAAqB,OACW,cAAvBgD,EAAYK,OAAyB,uEACd,YAAvBL,EAAYK,OAAuB,2EACnC,gEACCzE,SACAnC,EAAEuG,EAAYK,OAAQL,EAAYK,YAjBhCV,OAuBX9D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDnC,EAAE,iBAAkB,oD,cC3MnC,MA4SA,EA5SgC6G,KAC9B,MAAM,EAAE7G,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACf4G,GAAWC,EAAAA,EAAAA,OACV5C,EAAcC,IAAmB/D,EAAAA,EAAAA,UAAS,OAC1C2G,EAAqBC,IAA0B5G,EAAAA,EAAAA,WAAS,IACxD6G,EAAqBC,IAA0B9G,EAAAA,EAAAA,UAAS,OAGxDyD,IAAgBzD,EAAAA,EAAAA,UAAS,CAC9B,CACE+G,GAAI,EACJ3G,YAAa,yFACb4G,cAAe,0BACf3G,KAAM,aACNI,KAAM,QACNE,KAAM,mBACNC,UAAW,sBACX2F,OAAQ,aAEV,CACEQ,GAAI,EACJ3G,YAAa,yFACb4G,cAAe,sBACf3G,KAAM,aACNI,KAAM,QACNE,KAAM,uBACNC,UAAW,wBACX2F,OAAQ,WAEV,CACEQ,GAAI,EACJ3G,YAAa,2GACb4G,cAAe,8BACf3G,KAAM,aACNI,KAAM,QACNE,KAAM,iBACNC,UAAW,sBACX2F,OAAQ,aAEV,CACEQ,GAAI,EACJ3G,YAAa,+FACb4G,cAAe,yBACf3G,KAAM,aACNI,KAAM,QACNE,KAAM,gBACNC,UAAW,yBACX2F,OAAQ,aAEV,CACEQ,GAAI,EACJ3G,YAAa,+FACb4G,cAAe,0BACf3G,KAAM,aACNI,KAAM,QACNE,KAAM,mBACNC,UAAW,sBACX2F,OAAQ,eAQNU,EAA0Bf,IAC9BY,EAAuBZ,GAEvBgB,QAAQC,IAAI,uBAAwBjB,IAGhCkB,EAAuB,WAAkB,IAAjB/G,EAAIgH,UAAA5E,OAAA,QAAA6E,IAAAD,UAAA,GAAAA,UAAA,GAAG,KACnCP,EAAuB,MACnBzG,GACF0D,EAAgB,IAAIzD,KAAKD,IAE3BuG,GAAuB,EACzB,EAQA,OACEhF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DnC,EAAE,sBAAuB,2BAE5BoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDnC,EAAE,0BAA2B,yDAIlCiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMyE,EAAS,iBACxB5E,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,WAAY,iBAEjBiC,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMoF,IACfvF,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,iBAAkB,+BAO7BiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D2B,EAAa+B,OAAOC,GAAsB,cAAfA,EAAIc,QAAwB9D,UAE1DV,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,YAAa,wBAKtEoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D2B,EAAa+B,OAAOC,GAAsB,YAAfA,EAAIc,QAAsB9D,UAExDV,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,UAAW,sBAKpEoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D2B,EAAa+B,OAAOC,GAAsB,cAAfA,EAAIc,QAAwB9D,UAE1DV,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,YAAa,wBAKtEoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D2B,EAAahB,UAEhBV,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,oBAAqB,uBAOhFiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,KAACwF,EAAmB,CAClB9D,aAAcA,EACdC,aAzHgBrD,IACxB0D,EAAgB,IAAIzD,KAAKD,KAyHjBsD,mBAAoBsD,OAKxBrF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEnC,EAAE,eAAgB,oBAErBiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMoF,IACfvF,UAAU,mGAAkGC,SAAA,EAE5GC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,sBAAuB,4BAE5BiC,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMyE,EAAS,iBACxB5E,UAAU,mGAAkGC,SAAA,EAE5GC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,sBAAuB,6BAE5BiC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,qGAAoGC,SAAA,EACpHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZlC,EAAE,iBAAkB,6BAM3BiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEnC,EAAE,qBAAsB,0BAG1B8D,EAAa+B,OAAOC,GAAOA,EAAIpF,QAAS,IAAIC,MAAOC,cAAcC,MAAM,KAAK,IAAIiC,OAAS,GACxFV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB2B,EACE+B,OAAOC,GAAOA,EAAIpF,QAAS,IAAIC,MAAOC,cAAcC,MAAM,KAAK,IAC/D4C,IAAK8C,IACJnE,EAAAA,EAAAA,KAAA,OAEEF,UAAU,oHACVG,QAASA,IAAMiF,EAAuBf,GAAapE,UAEnDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DoE,EAAYzF,QAEfsB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDlC,EAAQsG,EAAY9F,YAAc8F,EAAYc,oBAGnDjF,EAAAA,EAAAA,KAAA,OAAKF,UAAS,wBAAAqB,OACW,cAAvBgD,EAAYK,OAAyB,eACd,YAAvBL,EAAYK,OAAuB,gBACZ,cAAvBL,EAAYK,OAAyB,cAAgB,oBAhBpDL,EAAYa,QAuBzBhF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDnC,EAAE,sBAAuB,6CAMhCiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEnC,EAAE,SAAU,aAEfiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEnC,EAAE,YAAa,mBAE7EiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6CACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEnC,EAAE,UAAW,iBAE3EiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEnC,EAAE,YAAa,mBAE7EiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEnC,EAAE,YAAa,8BAQpFgH,IACC5E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iFAAgFC,UAC7FC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,UAC3GC,EAAAA,EAAAA,KAACyF,EAAAA,EAAe,CACd9H,YAAaoE,EAAe,CAAEzD,KAAMyD,EAAavD,cAAcC,MAAM,KAAK,IAAO,KACjFhB,SAnNqBS,IAC/BiH,QAAQC,IAAI,mBAAoBlH,GAChC2G,GAAuB,IAkNbnH,SAAUA,IAAMmH,GAAuB,Y", "sources": ["components/Appointments/AppointmentForm.jsx", "components/Appointments/AppointmentCalendar.jsx", "pages/Appointments/AppointmentCalendarPage.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst AppointmentForm = ({ onSubmit, onCancel, initialData = null }) => {\n  const { t, isRTL } = useLanguage();\n  const [loading, setLoading] = useState(false);\n  \n  const [formData, setFormData] = useState({\n    patientId: initialData?.patientId || '',\n    patientName: initialData?.patientName || '',\n    date: initialData?.date || new Date().toISOString().split('T')[0],\n    time: initialData?.time || '',\n    duration: initialData?.duration || '60',\n    type: initialData?.type || 'consultation',\n    therapist: initialData?.therapist || '',\n    location: initialData?.location || 'room_1',\n    notes: initialData?.notes || '',\n    priority: initialData?.priority || 'normal',\n    reminderEnabled: initialData?.reminderEnabled || true,\n    reminderTime: initialData?.reminderTime || '24'\n  });\n\n  const [errors, setErrors] = useState({});\n\n  // Mock data for dropdowns\n  const appointmentTypes = [\n    { value: 'consultation', label: t('consultation', 'Consultation') },\n    { value: 'assessment', label: t('assessment', 'Assessment') },\n    { value: 'therapy_session', label: t('therapySession', 'Therapy Session') },\n    { value: 'follow_up', label: t('followUp', 'Follow-up') },\n    { value: 'group_therapy', label: t('groupTherapy', 'Group Therapy') },\n    { value: 'evaluation', label: t('evaluation', 'Evaluation') }\n  ];\n\n  const therapists = [\n    { value: 'dr_sarah', label: 'Dr. Sarah Al-Rashid' },\n    { value: 'dr_ahmed', label: 'Dr. Ahmed Al-Mansouri' },\n    { value: 'dr_fatima', label: 'Dr. Fatima Al-Zahra' },\n    { value: 'dr_mohammed', label: 'Dr. Mohammed Al-Khalid' }\n  ];\n\n  const locations = [\n    { value: 'room_1', label: t('room1', 'Room 1 - Main Therapy') },\n    { value: 'room_2', label: t('room2', 'Room 2 - Assessment') },\n    { value: 'room_3', label: t('room3', 'Room 3 - Group Therapy') },\n    { value: 'gym', label: t('gym', 'Gymnasium') },\n    { value: 'pool', label: t('pool', 'Hydrotherapy Pool') }\n  ];\n\n  const timeSlots = [\n    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',\n    '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',\n    '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',\n    '17:00', '17:30', '18:00'\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n    if (!formData.date) {\n      newErrors.date = t('dateRequired', 'Date is required');\n    }\n    if (!formData.time) {\n      newErrors.time = t('timeRequired', 'Time is required');\n    }\n    if (!formData.therapist) {\n      newErrors.therapist = t('therapistRequired', 'Therapist is required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      if (onSubmit) {\n        onSubmit(formData);\n      }\n      \n      toast.success(t('appointmentSaved', 'Appointment saved successfully'));\n    } catch (error) {\n      toast.error(t('errorSavingAppointment', 'Error saving appointment'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {initialData ? t('editAppointment', 'Edit Appointment') : t('newAppointment', 'New Appointment')}\n        </h2>\n        {onCancel && (\n          <button\n            onClick={onCancel}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <i className=\"fas fa-times text-xl\"></i>\n          </button>\n        )}\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Patient Information */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.patientName}\n              onChange={(e) => handleInputChange('patientName', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.patientName ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterPatientName', 'Enter patient name')}\n            />\n            {errors.patientName && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('appointmentType', 'Appointment Type')}\n            </label>\n            <select\n              value={formData.type}\n              onChange={(e) => handleInputChange('type', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {appointmentTypes.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Date and Time */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('date', 'Date')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"date\"\n              value={formData.date}\n              onChange={(e) => handleInputChange('date', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.date ? 'border-red-500' : 'border-gray-300'\n              }`}\n            />\n            {errors.date && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.date}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('time', 'Time')} <span className=\"text-red-500\">*</span>\n            </label>\n            <select\n              value={formData.time}\n              onChange={(e) => handleInputChange('time', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.time ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">{t('selectTime', 'Select time')}</option>\n              {timeSlots.map(time => (\n                <option key={time} value={time}>\n                  {time}\n                </option>\n              ))}\n            </select>\n            {errors.time && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.time}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('duration', 'Duration (minutes)')}\n            </label>\n            <select\n              value={formData.duration}\n              onChange={(e) => handleInputChange('duration', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"30\">30 {t('minutes', 'minutes')}</option>\n              <option value=\"45\">45 {t('minutes', 'minutes')}</option>\n              <option value=\"60\">60 {t('minutes', 'minutes')}</option>\n              <option value=\"90\">90 {t('minutes', 'minutes')}</option>\n              <option value=\"120\">120 {t('minutes', 'minutes')}</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Therapist and Location */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('therapist', 'Therapist')} <span className=\"text-red-500\">*</span>\n            </label>\n            <select\n              value={formData.therapist}\n              onChange={(e) => handleInputChange('therapist', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.therapist ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">{t('selectTherapist', 'Select therapist')}</option>\n              {therapists.map(therapist => (\n                <option key={therapist.value} value={therapist.value}>\n                  {therapist.label}\n                </option>\n              ))}\n            </select>\n            {errors.therapist && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.therapist}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('location', 'Location')}\n            </label>\n            <select\n              value={formData.location}\n              onChange={(e) => handleInputChange('location', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {locations.map(location => (\n                <option key={location.value} value={location.value}>\n                  {location.label}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Priority and Reminder */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('priority', 'Priority')}\n            </label>\n            <select\n              value={formData.priority}\n              onChange={(e) => handleInputChange('priority', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"low\">{t('low', 'Low')}</option>\n              <option value=\"normal\">{t('normal', 'Normal')}</option>\n              <option value=\"high\">{t('high', 'High')}</option>\n              <option value=\"urgent\">{t('urgent', 'Urgent')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('reminder', 'Reminder')}\n            </label>\n            <div className=\"flex items-center space-x-2\">\n              <input\n                type=\"checkbox\"\n                checked={formData.reminderEnabled}\n                onChange={(e) => handleInputChange('reminderEnabled', e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <select\n                value={formData.reminderTime}\n                onChange={(e) => handleInputChange('reminderTime', e.target.value)}\n                disabled={!formData.reminderEnabled}\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50\"\n              >\n                <option value=\"15\">15 {t('minutesBefore', 'minutes before')}</option>\n                <option value=\"30\">30 {t('minutesBefore', 'minutes before')}</option>\n                <option value=\"60\">1 {t('hourBefore', 'hour before')}</option>\n                <option value=\"1440\">1 {t('dayBefore', 'day before')}</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Notes */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            {t('notes', 'Notes')}\n          </label>\n          <textarea\n            value={formData.notes}\n            onChange={(e) => handleInputChange('notes', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterNotes', 'Enter any additional notes')}\n          />\n        </div>\n\n        {/* Form Actions */}\n        <div className=\"flex justify-end space-x-4\">\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              {t('cancel', 'Cancel')}\n            </button>\n          )}\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                {t('saving', 'Saving...')}\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveAppointment', 'Save Appointment')}\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default AppointmentForm;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst AppointmentCalendar = ({ appointments = [], onDateSelect, onAppointmentClick }) => {\n  const { t, isRTL } = useLanguage();\n  const [currentDate, setCurrentDate] = useState(new Date());\n  const [selectedDate, setSelectedDate] = useState(null);\n\n  // Get current month and year\n  const currentMonth = currentDate.getMonth();\n  const currentYear = currentDate.getFullYear();\n\n  // Get first day of the month and number of days\n  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);\n  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);\n  const firstDayWeekday = firstDayOfMonth.getDay();\n  const daysInMonth = lastDayOfMonth.getDate();\n\n  // Generate calendar days\n  const calendarDays = [];\n  \n  // Add empty cells for days before the first day of the month\n  for (let i = 0; i < firstDayWeekday; i++) {\n    calendarDays.push(null);\n  }\n  \n  // Add days of the month\n  for (let day = 1; day <= daysInMonth; day++) {\n    calendarDays.push(day);\n  }\n\n  // Month names\n  const monthNames = [\n    t('january', 'January'), t('february', 'February'), t('march', 'March'),\n    t('april', 'April'), t('may', 'May'), t('june', 'June'),\n    t('july', 'July'), t('august', 'August'), t('september', 'September'),\n    t('october', 'October'), t('november', 'November'), t('december', 'December')\n  ];\n\n  // Day names\n  const dayNames = [\n    t('sunday', 'Sun'), t('monday', 'Mon'), t('tuesday', 'Tue'),\n    t('wednesday', 'Wed'), t('thursday', 'Thu'), t('friday', 'Fri'), t('saturday', 'Sat')\n  ];\n\n  // Navigate months\n  const navigateMonth = (direction) => {\n    const newDate = new Date(currentDate);\n    newDate.setMonth(currentMonth + direction);\n    setCurrentDate(newDate);\n  };\n\n  // Get appointments for a specific date\n  const getAppointmentsForDate = (day) => {\n    if (!day) return [];\n    const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n    return appointments.filter(apt => apt.date === dateStr);\n  };\n\n  // Handle date click\n  const handleDateClick = (day) => {\n    if (!day) return;\n    const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n    setSelectedDate(dateStr);\n    if (onDateSelect) {\n      onDateSelect(dateStr);\n    }\n  };\n\n  // Check if date is today\n  const isToday = (day) => {\n    const today = new Date();\n    return day === today.getDate() && \n           currentMonth === today.getMonth() && \n           currentYear === today.getFullYear();\n  };\n\n  // Check if date is selected\n  const isSelected = (day) => {\n    if (!day || !selectedDate) return false;\n    const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n    return dateStr === selectedDate;\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n      {/* Calendar Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600\">\n        <button\n          onClick={() => navigateMonth(-1)}\n          className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n        >\n          <i className={`fas fa-chevron-${isRTL ? 'right' : 'left'} text-gray-600 dark:text-gray-400`}></i>\n        </button>\n        \n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {monthNames[currentMonth]} {currentYear}\n        </h2>\n        \n        <button\n          onClick={() => navigateMonth(1)}\n          className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n        >\n          <i className={`fas fa-chevron-${isRTL ? 'left' : 'right'} text-gray-600 dark:text-gray-400`}></i>\n        </button>\n      </div>\n\n      {/* Calendar Grid */}\n      <div className=\"p-4\">\n        {/* Day Headers */}\n        <div className=\"grid grid-cols-7 gap-1 mb-2\">\n          {dayNames.map((day, index) => (\n            <div key={index} className=\"p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400\">\n              {day}\n            </div>\n          ))}\n        </div>\n\n        {/* Calendar Days */}\n        <div className=\"grid grid-cols-7 gap-1\">\n          {calendarDays.map((day, index) => {\n            const dayAppointments = getAppointmentsForDate(day);\n            const hasAppointments = dayAppointments.length > 0;\n            \n            return (\n              <div\n                key={index}\n                onClick={() => handleDateClick(day)}\n                className={`\n                  relative p-2 h-20 border border-gray-100 dark:border-gray-700 rounded-lg cursor-pointer transition-all\n                  ${day ? 'hover:bg-gray-50 dark:hover:bg-gray-700' : 'cursor-default'}\n                  ${isToday(day) ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : ''}\n                  ${isSelected(day) ? 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700' : ''}\n                  ${!day ? 'bg-gray-50 dark:bg-gray-900' : ''}\n                `}\n              >\n                {day && (\n                  <>\n                    <div className={`text-sm font-medium ${isToday(day) ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'}`}>\n                      {day}\n                    </div>\n                    \n                    {hasAppointments && (\n                      <div className=\"mt-1 space-y-1\">\n                        {dayAppointments.slice(0, 2).map((appointment, aptIndex) => (\n                          <div\n                            key={aptIndex}\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              if (onAppointmentClick) {\n                                onAppointmentClick(appointment);\n                              }\n                            }}\n                            className=\"text-xs bg-blue-500 text-white px-1 py-0.5 rounded truncate hover:bg-blue-600 transition-colors\"\n                            title={`${appointment.time} - ${appointment.patientName}`}\n                          >\n                            {appointment.time}\n                          </div>\n                        ))}\n                        {dayAppointments.length > 2 && (\n                          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                            +{dayAppointments.length - 2} {t('more', 'more')}\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </>\n                )}\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Selected Date Info */}\n      {selectedDate && (\n        <div className=\"p-4 border-t border-gray-200 dark:border-gray-600\">\n          <h3 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n            {t('appointmentsFor', 'Appointments for')} {selectedDate}\n          </h3>\n          \n          {getAppointmentsForDate(parseInt(selectedDate.split('-')[2])).length > 0 ? (\n            <div className=\"space-y-2\">\n              {getAppointmentsForDate(parseInt(selectedDate.split('-')[2])).map((appointment, index) => (\n                <div\n                  key={index}\n                  onClick={() => onAppointmentClick && onAppointmentClick(appointment)}\n                  className=\"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {appointment.patientName}\n                    </div>\n                    <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {appointment.time} - {appointment.type}\n                    </div>\n                  </div>\n                  <div className={`px-2 py-1 text-xs rounded-full ${\n                    appointment.status === 'confirmed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                    appointment.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :\n                    'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n                  }`}>\n                    {t(appointment.status, appointment.status)}\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {t('noAppointments', 'No appointments scheduled for this date')}\n            </p>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AppointmentCalendar;\n", "import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport AppointmentCalendar from '../../components/Appointments/AppointmentCalendar';\nimport AppointmentForm from '../../components/Appointments/AppointmentForm';\n\nconst AppointmentCalendarPage = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const [selectedDate, setSelectedDate] = useState(null);\n  const [showAppointmentForm, setShowAppointmentForm] = useState(false);\n  const [selectedAppointment, setSelectedAppointment] = useState(null);\n\n  // Mock appointment data\n  const [appointments] = useState([\n    {\n      id: 1,\n      patientName: 'أحمد محمد الأحمد',\n      patientNameEn: '<PERSON>',\n      date: '2024-01-25',\n      time: '10:00',\n      type: 'Physical Therapy',\n      therapist: 'Dr. <PERSON>',\n      status: 'confirmed'\n    },\n    {\n      id: 2,\n      patientName: 'فاطمة علي السالم',\n      patientNameEn: 'Fat<PERSON>-<PERSON>',\n      date: '2024-01-25',\n      time: '11:30',\n      type: 'Occupational Therapy',\n      therapist: 'Dr. Ahmed Al-Mansouri',\n      status: 'pending'\n    },\n    {\n      id: 3,\n      patientName: 'محمد عبدالله الخالد',\n      patientNameEn: 'Mohammed Abdullah Al-Khalid',\n      date: '2024-01-25',\n      time: '14:00',\n      type: 'Speech Therapy',\n      therapist: 'Dr. Fatima Al-Zahra',\n      status: 'completed'\n    },\n    {\n      id: 4,\n      patientName: 'سارة أحمد المطيري',\n      patientNameEn: 'Sarah Ahmed Al-Mutairi',\n      date: '2024-01-26',\n      time: '09:00',\n      type: 'Group Therapy',\n      therapist: 'Dr. Mohammed Al-Khalid',\n      status: 'confirmed'\n    },\n    {\n      id: 5,\n      patientName: 'علي محمد الزهراني',\n      patientNameEn: 'Ali Mohammed Al-Zahrani',\n      date: '2024-01-26',\n      time: '15:30',\n      type: 'Physical Therapy',\n      therapist: 'Dr. Sarah Al-Rashid',\n      status: 'confirmed'\n    }\n  ]);\n\n  const handleDateSelect = (date) => {\n    setSelectedDate(new Date(date));\n  };\n\n  const handleAppointmentClick = (appointment) => {\n    setSelectedAppointment(appointment);\n    // Could open appointment details modal or navigate to appointment details\n    console.log('Appointment clicked:', appointment);\n  };\n\n  const handleNewAppointment = (date = null) => {\n    setSelectedAppointment(null);\n    if (date) {\n      setSelectedDate(new Date(date));\n    }\n    setShowAppointmentForm(true);\n  };\n\n  const handleAppointmentSubmit = (formData) => {\n    console.log('New appointment:', formData);\n    setShowAppointmentForm(false);\n    // Here you would typically save the appointment to your backend\n  };\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {t('appointmentCalendar', 'Appointment Calendar')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n              {t('appointmentCalendarDesc', 'View and manage appointments in calendar format')}\n            </p>\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={() => navigate('/appointments')}\n              className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n            >\n              <i className=\"fas fa-list mr-2\"></i>\n              {t('listView', 'List View')}\n            </button>\n            <button\n              onClick={() => handleNewAppointment()}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('newAppointment', 'New Appointment')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Calendar Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {appointments.filter(apt => apt.status === 'confirmed').length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('confirmed', 'Confirmed')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {appointments.filter(apt => apt.status === 'pending').length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('pending', 'Pending')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {appointments.filter(apt => apt.status === 'completed').length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('completed', 'Completed')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-calendar text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {appointments.length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('totalAppointments', 'Total')}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Calendar */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2\">\n          <AppointmentCalendar \n            appointments={appointments}\n            onDateSelect={handleDateSelect}\n            onAppointmentClick={handleAppointmentClick}\n          />\n        </div>\n        \n        {/* Sidebar */}\n        <div className=\"space-y-6\">\n          {/* Quick Actions */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('quickActions', 'Quick Actions')}\n            </h3>\n            <div className=\"space-y-3\">\n              <button\n                onClick={() => handleNewAppointment()}\n                className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-left\"\n              >\n                <i className=\"fas fa-plus mr-2\"></i>\n                {t('scheduleAppointment', 'Schedule Appointment')}\n              </button>\n              <button\n                onClick={() => navigate('/appointments')}\n                className=\"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-left\"\n              >\n                <i className=\"fas fa-list mr-2\"></i>\n                {t('viewAllAppointments', 'View All Appointments')}\n              </button>\n              <button className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-left\">\n                <i className=\"fas fa-download mr-2\"></i>\n                {t('exportCalendar', 'Export Calendar')}\n              </button>\n            </div>\n          </div>\n\n          {/* Today's Appointments */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('todaysAppointments', \"Today's Appointments\")}\n            </h3>\n            \n            {appointments.filter(apt => apt.date === new Date().toISOString().split('T')[0]).length > 0 ? (\n              <div className=\"space-y-3\">\n                {appointments\n                  .filter(apt => apt.date === new Date().toISOString().split('T')[0])\n                  .map((appointment) => (\n                    <div\n                      key={appointment.id}\n                      className=\"p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\"\n                      onClick={() => handleAppointmentClick(appointment)}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                            {appointment.time}\n                          </div>\n                          <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                            {isRTL ? appointment.patientName : appointment.patientNameEn}\n                          </div>\n                        </div>\n                        <div className={`w-3 h-3 rounded-full ${\n                          appointment.status === 'confirmed' ? 'bg-green-500' :\n                          appointment.status === 'pending' ? 'bg-yellow-500' :\n                          appointment.status === 'completed' ? 'bg-blue-500' : 'bg-red-500'\n                        }`}></div>\n                      </div>\n                    </div>\n                  ))}\n              </div>\n            ) : (\n              <p className=\"text-gray-500 dark:text-gray-400 text-sm\">\n                {t('noAppointmentsToday', 'No appointments scheduled for today')}\n              </p>\n            )}\n          </div>\n\n          {/* Legend */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('legend', 'Legend')}\n            </h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-green-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('confirmed', 'Confirmed')}</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-yellow-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('pending', 'Pending')}</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-blue-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('completed', 'Completed')}</span>\n              </div>\n              <div className=\"flex items-center\">\n                <div className=\"w-3 h-3 bg-red-500 rounded-full mr-3\"></div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('cancelled', 'Cancelled')}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* New Appointment Modal */}\n      {showAppointmentForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\">\n            <AppointmentForm\n              initialData={selectedDate ? { date: selectedDate.toISOString().split('T')[0] } : null}\n              onSubmit={handleAppointmentSubmit}\n              onCancel={() => setShowAppointmentForm(false)}\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AppointmentCalendarPage;\n"], "names": ["_ref", "onSubmit", "onCancel", "initialData", "t", "isRTL", "useLanguage", "loading", "setLoading", "useState", "formData", "setFormData", "patientId", "patientName", "date", "Date", "toISOString", "split", "time", "duration", "type", "therapist", "location", "notes", "priority", "reminderEnabled", "reminderTime", "errors", "setErrors", "appointmentTypes", "value", "label", "locations", "handleInputChange", "field", "prev", "_objectSpread", "_jsxs", "className", "children", "_jsx", "onClick", "async", "e", "preventDefault", "validateForm", "newErrors", "trim", "Object", "keys", "length", "Promise", "resolve", "setTimeout", "toast", "success", "error", "onChange", "target", "concat", "placeholder", "map", "checked", "disabled", "rows", "_Fragment", "appointments", "onDateSelect", "onAppointmentClick", "currentDate", "setCurrentDate", "selectedDate", "setSelectedDate", "currentMonth", "getMonth", "currentYear", "getFullYear", "firstDayOfMonth", "lastDayOfMonth", "firstDayWeekday", "getDay", "daysInMonth", "getDate", "calendarDays", "i", "push", "day", "monthNames", "dayNames", "navigateMonth", "direction", "newDate", "setMonth", "getAppointmentsForDate", "dateStr", "String", "padStart", "filter", "apt", "isToday", "today", "isSelected", "index", "dayAppointments", "hasAppointments", "handleDateClick", "slice", "appointment", "aptIndex", "stopPropagation", "title", "parseInt", "status", "AppointmentCalendarPage", "navigate", "useNavigate", "showAppointmentForm", "setShowAppointmentForm", "selectedAppointment", "setSelectedAppointment", "id", "patientNameEn", "handleAppointmentClick", "console", "log", "handleNewAppointment", "arguments", "undefined", "AppointmentCalendar", "AppointmentForm"], "sourceRoot": ""}