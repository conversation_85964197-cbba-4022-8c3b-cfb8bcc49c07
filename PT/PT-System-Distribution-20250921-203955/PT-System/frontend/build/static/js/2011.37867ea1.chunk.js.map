{"version": 3, "file": "static/js/2011.37867ea1.chunk.js", "mappings": "uNAKA,MAylCA,EAzlC4BA,KAC1B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAGVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAEvCC,eAAgB,MAChBC,WAAW,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC/CC,QAAS,KACTC,aAAc,KAGdC,YAAa,GACbC,SAAU,GACVC,cAAe,GACfC,UAAW,GACXC,cAAe,GACfC,UAAW,GACXC,YAAa,EACbC,oBAAqB,EAGrBC,mBAAoB,GACpBC,gBAAiB,GAGjBC,kBAAmB,GAGnBC,KAAM,GACNC,oBAAqB,GACrBC,KAAM,GACNC,qBAAsB,GACtBC,wBAAyB,GACzBC,qBAAsB,GACtBC,uBAAwB,GACxBC,cAAe,GACfC,iBAAkB,GAClBC,YAAa,GAGbC,yBAA0B,GAG1BC,iBAAkB,GAClBC,eAAgB,GAGhBC,aAAa,EACbC,mBAAoB,GACpBC,eAAgB,GAChBC,mBAAmB,EACnBC,yBAA0B,GAC1BC,oBAAqB,GACrBC,kBAAmB,GACnBC,aAAc,GACdC,oBAAqB,GACrBC,wBAAwB,EAGxBC,mBAAoB,GACpBC,iBAAkB,GAClBC,eAAe,IAAIzC,MAAOC,cAAcC,MAAM,KAAK,GACnDwC,SAAU,GACVC,uBAAuB,EACvBC,oBAAqB,GACrBC,kBAAmB,GACnBC,gBAAgB,IAAI9C,MAAOC,cAAcC,MAAM,KAAK,MAG/C6C,EAASC,IAAcnD,EAAAA,EAAAA,WAAS,IAChCoD,EAAQC,IAAarD,EAAAA,EAAAA,UAAS,CAAC,IAC/BsD,EAAeC,IAAoBvD,EAAAA,EAAAA,UAAS,iBAoBnDwD,EAAAA,EAAAA,WAAU,KACJ9D,GACF+D,KAED,CAAC/D,IAEJ,MAAM+D,EAAkBC,UACtB,IACEP,GAAW,GAEX,MAAMQ,QAAiBC,MAAM,iBAADC,OAAkBnE,IAC9C,GAAIiE,EAASG,GAAI,CACf,MAAMC,QAAoBJ,EAASK,OACnCjE,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPzD,YAAauD,EAAYI,MAAQ,GACjC1D,SAAUsD,EAAYtD,UAAY,GAClCI,UAAWkD,EAAYlD,WAAa,GACpCF,UAAWoD,EAAYpD,WAAa,KAExC,CACF,CAAE,MAAOyD,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACCjB,GAAW,EACb,GAGImB,EAAoBA,CAACC,EAAOC,KAChCzE,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACM,GAAQC,KAIPpB,EAAOmB,IACTlB,EAAUY,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACM,GAAQ,SAKTE,EAAuBA,CAACF,EAAOG,EAAQC,KAC3C5E,EAAYkE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACM,GAAQI,EACL,IAAIV,EAAKM,GAAQG,GACjBT,EAAKM,GAAOK,OAAOC,GAAQA,IAASH,OA4H5C,OAAIxB,GAEA4B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kGAAiGC,SAAA,EAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D3F,EAAE,gCAAiC,sCAEtCyF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD3F,EAAE,iCAAkC,2DAGzCyF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,MAAA,UACEC,QApEMxB,UAClB,IACEP,GAAW,GAGX,MAAMgC,GAAOjB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRpE,GAAQ,IACXsF,aAAa,IAAIjF,MAAOC,cACxBiF,YAAa7F,EAAK2E,MAAQ3E,EAAK8F,MAC/B5F,UAAWA,IAGPiE,QAAiBC,MAAM,oCAAqC,CAChE2B,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAAD3B,OAAY4B,aAAaC,QAAQ,WAElDC,KAAMC,KAAKC,UAAUV,KAGvB,IAAIxB,EAASG,GAaX,MAAM,IAAIgC,MAAM,uBAADjC,OAAwBF,EAASoC,SAbjC,CACf,MAAMC,QAAarC,EAASqC,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,wBAAA5C,OAA2B/D,EAASU,YAAYkG,QAAQ,OAAQ,KAAI,KAAA7C,OAAI/D,EAASY,cAAa,QACxG4F,SAASX,KAAKgB,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAASX,KAAKmB,YAAYT,GAE1BU,MAAM1H,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAO+E,GACPC,QAAQD,MAAM,wBAAyBA,GACvC2C,MAAM1H,EAAE,qBAAsB,2CAChC,CAAC,QACC8D,GAAW,EACb,GA4BY4B,UAAU,oFAAmFC,SAAA,EAE7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ1F,EAAE,cAAe,2BAO1ByF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sFAAqFC,UAClGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8CAA6CC,SAC3D3F,EAAE,iBAAkB,sBAEvByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASG,eAChBgH,SAAWC,GAAM5C,EAAkB,iBAAkB4C,EAAEC,OAAO3C,OAC9DO,UAAU,qIAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8CAA6CC,SAC3D3F,EAAE,YAAa,iBAElByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASI,UAChB+G,SAAWC,GAAM5C,EAAkB,YAAa4C,EAAEC,OAAO3C,OACzDO,UAAU,qIAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8CAA6CC,SAC3D3F,EAAE,UAAW,cAEhByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASQ,QAChB2G,SAAWC,GAAM5C,EAAkB,UAAW4C,EAAEC,OAAO3C,OACvDO,UAAU,qIAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8CAA6CC,SAC3D3F,EAAE,eAAgB,oBAErByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASS,aAChB0G,SAAWC,GAAM5C,EAAkB,eAAgB4C,EAAEC,OAAO3C,OAC5DO,UAAU,6IAQpBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAsB,aAAW,OAAMC,SACnD,CACC,CAAEoC,GAAI,eAAgBC,MAAOhI,EAAE,qBAAsB,wBACrD,CAAE+H,GAAI,aAAcC,MAAOhI,EAAE,aAAc,eAC3C,CAAE+H,GAAI,WAAYC,MAAOhI,EAAE,WAAY,qBACvC,CAAE+H,GAAI,YAAaC,MAAOhI,EAAE,YAAa,gCACzC,CAAE+H,GAAI,aAAcC,MAAOhI,EAAE,aAAc,gBAC3CiI,IAAKC,IACLzC,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IAAM3B,EAAiBgE,EAAIH,IACpCrC,UAAS,4CAAAlB,OACPP,IAAkBiE,EAAIH,GAClB,mDACA,0HACHpC,SAEFuC,EAAIF,OARAE,EAAIH,YAgBnBnC,EAAAA,EAAAA,MAAA,QAAMuC,SArMW9D,UAGnB,GAFAwD,EAAEO,iBArCiBC,MACnB,MAAMC,EAAY,CAAC,EAgCnB,OA7BK7H,EAASU,YAAYoH,SAAQD,EAAUnH,YAAcnB,EAAE,sBAAuB,6BAC9ES,EAASW,SAASmH,SAAQD,EAAUlH,SAAWpB,EAAE,mBAAoB,0BACrES,EAASY,gBAAeiH,EAAUjH,cAAgBrB,EAAE,wBAAyB,+BAC7ES,EAASa,UAAUiH,SAAQD,EAAUhH,UAAYtB,EAAE,oBAAqB,0BACxES,EAASc,gBAAe+G,EAAU/G,cAAgBvB,EAAE,wBAAyB,+BAC7ES,EAASe,UAAU+G,SAAQD,EAAU9G,UAAYxB,EAAE,oBAAqB,0BACxES,EAASoB,kBAAkB0G,SAAQD,EAAUzG,kBAAoB7B,EAAE,4BAA6B,mCAChGS,EAAS+B,yBAAyB+F,SAAQD,EAAU9F,yBAA2BxC,EAAE,mBAAoB,2CACjE,IAArCS,EAASgC,iBAAiB+F,SAAcF,EAAU7F,iBAAmBzC,EAAE,0BAA2B,8CACjGS,EAAS4C,mBAAmBkF,SAAQD,EAAUjF,mBAAqBrD,EAAE,6BAA8B,oCACnGS,EAAS6C,iBAAiBiF,SAAQD,EAAUhF,iBAAmBtD,EAAE,sBAAuB,6BAGzFS,EAASc,eAAiBd,EAASY,eACjC,IAAIP,KAAKL,EAASc,gBAAkB,IAAIT,KAAKL,EAASY,iBACxDiH,EAAUjH,cAAgBrB,EAAE,8BAA+B,gDAK3DS,EAASyC,cAAgB,IAAIpC,KAAKL,EAASyC,eAAiB,IAAIpC,OAClEwH,EAAUpF,aAAelD,EAAE,qBAAsB,yCAI/CS,EAASgB,YAAc,IAAG6G,EAAU7G,YAAczB,EAAE,sBAAuB,kCAC3ES,EAASiB,oBAAsB,IAAG4G,EAAU5G,oBAAsB1B,EAAE,iBAAkB,2CAE1FgE,EAAUsE,GAC+B,IAAlCG,OAAOC,KAAKJ,GAAWE,QAMzBH,GAIL,IACEvE,GAAW,GAEX,MAAM6E,GAAc9D,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfpE,GAAQ,IACXmI,YAAazI,EAAK4H,GAClBc,aAAa,IAAI/H,MAAOC,cACxBV,UAAWA,IAGPiE,QAAiBC,MAAM,uCAAwC,CACnE2B,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBG,KAAMC,KAAKC,UAAUmC,KAGvB,IAAIrE,EAASG,GAKX,MAAM,IAAIgC,MAAM,6CAJKnC,EAASK,OAC9B+C,MAAM1H,EAAE,2BAA4B,6CACpCO,EAAS,aAADiE,OAAcnE,GAI1B,CAAE,MAAO0E,GACPC,QAAQD,MAAM,qCAAsCA,GACpD2C,MAAM1H,EAAE,cAAe,wDACzB,CAAC,QACC8D,GAAW,EACb,GAgKgC4B,UAAU,YAAWC,SAAA,CAE9B,iBAAlB1B,IACC2B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE3F,EAAE,qBAAsB,0BAG3B4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,cAAe,gBAAgB,SAEpCyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASU,YAChByG,SAAWC,GAAM5C,EAAkB,cAAe4C,EAAEC,OAAO3C,OAC3DO,UAAS,8FAAAlB,OACPT,EAAO5C,YAAc,iBAAmB,wCAE1C2H,UAAQ,IAET/E,EAAO5C,cACNsE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAO5C,kBAIrDyE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,WAAY,aAAa,SAE9ByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASW,SAChBwG,SAAWC,GAAM5C,EAAkB,WAAY4C,EAAEC,OAAO3C,OACxDO,UAAS,8FAAAlB,OACPT,EAAO3C,SAAW,iBAAmB,wCAEvC0H,UAAQ,IAET/E,EAAO3C,WACNqE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAO3C,eAIrDwE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,gBAAiB,kBAAkB,SAExCyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASY,cAChBuG,SAAWC,GAAM5C,EAAkB,gBAAiB4C,EAAEC,OAAO3C,OAC7DO,UAAS,8FAAAlB,OACPT,EAAO1C,cAAgB,iBAAmB,wCAE5CyH,UAAQ,IAET/E,EAAO1C,gBACNoE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAO1C,oBAIrDuE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,gBAAiB,kBAAkB,SAExCyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASc,cAChBqG,SAAWC,GAAM5C,EAAkB,gBAAiB4C,EAAEC,OAAO3C,OAC7DO,UAAS,8FAAAlB,OACPT,EAAOxC,cAAgB,iBAAmB,wCAE5CuH,UAAQ,IAET/E,EAAOxC,gBACNkE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOxC,oBAIrDqE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,YAAa,aAAa,SAE/ByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASe,UAChBoG,SAAWC,GAAM5C,EAAkB,YAAa4C,EAAEC,OAAO3C,OACzDO,UAAS,8FAAAlB,OACPT,EAAOvC,UAAY,iBAAmB,wCAExCsH,UAAQ,IAET/E,EAAOvC,YACNiE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOvC,gBAIrDoE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,cAAe,0BAA0B,SAE9CyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,SACLoB,IAAI,IACJ5D,MAAO1E,EAASgB,YAChBmG,SAAWC,GAAM5C,EAAkB,cAAe+D,SAASnB,EAAEC,OAAO3C,QAAU,GAC9EO,UAAS,8FAAAlB,OACPT,EAAOtC,YAAc,iBAAmB,wCAE1CqH,UAAQ,IAET/E,EAAOtC,cACNgE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOtC,kBAIrDmE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,sBAAuB,mCAAmC,SAE/DyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,SACLoB,IAAI,IACJ5D,MAAO1E,EAASiB,oBAChBkG,SAAWC,GAAM5C,EAAkB,sBAAuB+D,SAASnB,EAAEC,OAAO3C,QAAU,GACtFO,UAAS,8FAAAlB,OACPT,EAAOrC,oBAAsB,iBAAmB,wCAElDoH,UAAQ,IAET/E,EAAOrC,sBACN+D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOrC,6BAKvDkE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,YAAa,aAAa,SAE/ByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASa,UAChBsG,SAAWC,GAAM5C,EAAkB,YAAa4C,EAAEC,OAAO3C,OACzD8D,KAAM,EACNvD,UAAS,8FAAAlB,OACPT,EAAOzC,UAAY,iBAAmB,wCAExCwH,UAAQ,IAET/E,EAAOzC,YACNmE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOzC,kBAOtC,eAAlB2C,IACC2B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE3F,EAAE,qBAAsB,0BAG3ByF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4DAA2DC,SAnd1D,CACxB,aAAc,KAAM,KAAM,aAAc,WAAY,SACpD,UAAW,OAAQ,QAAS,YAAa,oBACzC,QAAS,aAAc,YAAa,gBAAiB,YACrD,UAAW,eAAgB,SAgdEsC,IAAK5C,IACtBO,EAAAA,EAAAA,MAAA,SAAoBF,UAAU,8BAA6BC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,WACLrC,QAAS7E,EAASkB,mBAAmBuH,SAAS7D,GAC9CuC,SAAWC,GAAMzC,EAAqB,qBAAsBC,EAAQwC,EAAEC,OAAOxC,SAC7EI,UAAU,+DAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAEN,MAPlDA,MAYf5E,EAASkB,mBAAmBuH,SAAS,WACpCtD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,kBAAmB,+BAExByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASmB,gBAChBgG,SAAWC,GAAM5C,EAAkB,kBAAmB4C,EAAEC,OAAO3C,OAC/D8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,oBAAqB,2CAK1C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,oBAAqB,sBAAsB,SAEhDyF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASoB,kBAChB+F,SAAWC,GAAM5C,EAAkB,oBAAqB4C,EAAEC,OAAO3C,OACjE8D,KAAM,EACNvD,UAAS,8FAAAlB,OACPT,EAAOlC,kBAAoB,iBAAmB,wCAEhDsH,YAAanJ,EAAE,4BAA6B,gEAC5C8I,UAAQ,IAET/E,EAAOlC,oBACN4D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOlC,0BAOtC,aAAlBoC,IACC2B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE3F,EAAE,oBAAqB,0BAG1B4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,OAAQ,WAEbyF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASqB,KAChB8F,SAAWC,GAAM5C,EAAkB,OAAQ4C,EAAEC,OAAO3C,OACpD8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,uBAAwB,mCAI3C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,sBAAuB,2BAE5ByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASsB,oBAChB6F,SAAWC,GAAM5C,EAAkB,sBAAuB4C,EAAEC,OAAO3C,OACnE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,0BAA2B,mDAI9C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,OAAQ,WAEbyF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASuB,KAChB4F,SAAWC,GAAM5C,EAAkB,OAAQ4C,EAAEC,OAAO3C,OACpD8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,uBAAwB,8CAI3C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,uBAAwB,4BAE7ByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASwB,qBAChB2F,SAAWC,GAAM5C,EAAkB,uBAAwB4C,EAAEC,OAAO3C,OACpE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,6BAA8B,oDAIjD4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,0BAA2B,+BAEhCyF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASyB,wBAChB0F,SAAWC,GAAM5C,EAAkB,0BAA2B4C,EAAEC,OAAO3C,OACvE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,gCAAiC,8CAIpD4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,uBAAwB,4BAE7ByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS0B,qBAChByF,SAAWC,GAAM5C,EAAkB,uBAAwB4C,EAAEC,OAAO3C,OACpE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,+BAAgC,2CAInD4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,yBAA0B,8BAE/ByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS2B,uBAChBwF,SAAWC,GAAM5C,EAAkB,yBAA0B4C,EAAEC,OAAO3C,OACtE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,iCAAkC,+CAIrD4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,gBAAiB,qBAEtByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS4B,cAChBuF,SAAWC,GAAM5C,EAAkB,gBAAiB4C,EAAEC,OAAO3C,OAC7D8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,wBAAyB,oCAI5C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,mBAAoB,wBAEzByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS6B,iBAChBsF,SAAWC,GAAM5C,EAAkB,mBAAoB4C,EAAEC,OAAO3C,OAChE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,2BAA4B,4CAI/C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,cAAe,mBAEpByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS8B,YAChBqF,SAAWC,GAAM5C,EAAkB,cAAe4C,EAAEC,OAAO3C,OAC3D8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,sBAAuB,qCAK5C4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,2BAA4B,8BAA8B,SAE/DyF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS+B,yBAChBoF,SAAWC,GAAM5C,EAAkB,2BAA4B4C,EAAEC,OAAO3C,OACxE8D,KAAM,EACNvD,UAAS,8FAAAlB,OACPT,EAAOvB,yBAA2B,iBAAmB,wCAEvD2G,YAAanJ,EAAE,mBAAoB,uDACnC8I,UAAQ,IAET/E,EAAOvB,2BACNiD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOvB,iCAOtC,cAAlByB,IACC2B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE3F,EAAE,8BAA+B,kCAIpC4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yDAAwDC,SAAA,CACnE3F,EAAE,qBAAsB,wBAAwB,SAEnDyF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SAzqBP,CAC7B,YACA,oBACA,4BACA,iBACA,6EACA,SAoqBoCsC,IAAKmB,IAC3BxD,EAAAA,EAAAA,MAAA,SAAoBF,UAAU,6BAA4BC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,WACLrC,QAAS7E,EAASgC,iBAAiByG,SAASE,GAC5CxB,SAAWC,GAAMzC,EAAqB,mBAAoBgE,EAAQvB,EAAEC,OAAOxC,SAC3EI,UAAU,oEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAEyD,MAPlDA,MAWf3I,EAASgC,iBAAiByG,SAAS,WAClCzD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASiC,eAChBkF,SAAWC,GAAM5C,EAAkB,iBAAkB4C,EAAEC,OAAO3C,OAC9D8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,4BAA6B,uCAIjD+D,EAAOtB,mBACNgD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOtB,uBAKrDmD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE3F,EAAE,kBAAmB,sBAGxB4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,mCAAkCC,SAAA,EACjDF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,WACLrC,QAAS7E,EAASkC,YAClBiF,SAAWC,GAAM5C,EAAkB,cAAe4C,EAAEC,OAAOxC,SAC3DI,UAAU,+DAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SACnE3F,EAAE,kBAAmB,4DAGzBS,EAASkC,cACR8C,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASmC,mBAChBgF,SAAWC,GAAM5C,EAAkB,qBAAsB4C,EAAEC,OAAO3C,OAClE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,kBAAmB,+CAMxC4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,iBAAkB,sBAEvByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASoC,eAChB+E,SAAWC,GAAM5C,EAAkB,iBAAkB4C,EAAEC,OAAO3C,OAC9D8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,yBAA0B,yCAK7C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,mCAAkCC,SAAA,EACjDF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,WACLrC,QAAS7E,EAASqC,kBAClB8E,SAAWC,GAAM5C,EAAkB,oBAAqB4C,EAAEC,OAAOxC,SACjEI,UAAU,+DAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SACnE3F,EAAE,wBAAyB,iCAG/BS,EAASqC,oBACR2C,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASsC,yBAChB6E,SAAWC,GAAM5C,EAAkB,2BAA4B4C,EAAEC,OAAO3C,OACxE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,uBAAwB,oCAM7C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,uBAAwB,4BAE7ByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAASuC,oBAChB4E,SAAWC,GAAM5C,EAAkB,sBAAuB4C,EAAEC,OAAO3C,OACnE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,4BAA6B,8CAOpD4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE3F,EAAE,sBAAuB,4BAG5B4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,oBAAqB,yCAE1ByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASwC,kBAChB2E,SAAWC,GAAM5C,EAAkB,oBAAqB4C,EAAEC,OAAO3C,OACjEO,UAAU,kIACVyD,YAAanJ,EAAE,iBAAkB,8BAIrC4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,eAAgB,iCAErByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASyC,aAChB0E,SAAWC,GAAM5C,EAAkB,eAAgB4C,EAAEC,OAAO3C,OAC5DO,UAAS,8FAAAlB,OACPT,EAAOb,aAAe,iBAAmB,0CAG5Ca,EAAOb,eACNuC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOb,sBAKvD0C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,sBAAuB,0CAE5ByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS0C,oBAChByE,SAAWC,GAAM5C,EAAkB,sBAAuB4C,EAAEC,OAAO3C,OACnE8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,8BAA+B,4DAIlDyF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,8BAA6BC,SAAA,EAC5CF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,WACLrC,QAAS7E,EAAS2C,uBAClBwE,SAAWC,GAAM5C,EAAkB,yBAA0B4C,EAAEC,OAAOxC,SACtEI,UAAU,+DAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvD3F,EAAE,yBAA0B,iEAStB,eAAlBiE,IACC2B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE3F,EAAE,wBAAyB,8BAG9B4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,qBAAsB,uBAAuB,SAElDyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAAS4C,mBAChBuE,SAAWC,GAAM5C,EAAkB,qBAAsB4C,EAAEC,OAAO3C,OAClEO,UAAS,8FAAAlB,OACPT,EAAOV,mBAAqB,iBAAmB,wCAEjD8F,YAAanJ,EAAE,qBAAsB,wBACrC8I,UAAQ,IAET/E,EAAOV,qBACNoC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOV,yBAIrDuC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,cAAe,aAAa,SAEjCyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAAS6C,iBAChBsE,SAAWC,GAAM5C,EAAkB,mBAAoB4C,EAAEC,OAAO3C,OAChEO,UAAS,8FAAAlB,OACPT,EAAOT,iBAAmB,iBAAmB,wCAE/C6F,YAAanJ,EAAE,mBAAoB,sBACnC8I,UAAQ,IAET/E,EAAOT,mBACNmC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAE5B,EAAOT,uBAIrDsC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,OAAQ,QAAQ,SAErByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAAS8C,cAChBqE,SAAWC,GAAM5C,EAAkB,gBAAiB4C,EAAEC,OAAO3C,OAC7DO,UAAU,kIACVoD,UAAQ,WAMdlD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,WAAY,eAEjByF,EAAAA,EAAAA,KAAA,YACEN,MAAO1E,EAAS+C,SAChBoE,SAAWC,GAAM5C,EAAkB,WAAY4C,EAAEC,OAAO3C,OACxD8D,KAAM,EACNvD,UAAU,kIACVyD,YAAanJ,EAAE,gBAAiB,0CAKpCyF,EAAAA,EAAAA,KAAA,OAAAE,UACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,8BAA6BC,SAAA,EAC5CF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,WACLrC,QAAS7E,EAASgD,sBAClBmE,SAAWC,GAAM5C,EAAkB,wBAAyB4C,EAAEC,OAAOxC,SACrEI,UAAU,+DAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvD3F,EAAE,4BAA6B,+DAMtC4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE3F,EAAE,wBAAyB,sCAG9B4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,0BAA2B,gCAEhCyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASiD,oBAChBkE,SAAWC,GAAM5C,EAAkB,sBAAuB4C,EAAEC,OAAO3C,OACnEO,UAAU,kIACVyD,YAAanJ,EAAE,0BAA2B,wCAI9C4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,cAAe,gBAEpByF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASkD,kBAChBiE,SAAWC,GAAM5C,EAAkB,oBAAqB4C,EAAEC,OAAO3C,OACjEO,UAAU,kIACVyD,YAAanJ,EAAE,mBAAoB,4BAIvC4F,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E3F,EAAE,OAAQ,WAEbyF,EAAAA,EAAAA,KAAA,SACEkC,KAAK,OACLxC,MAAO1E,EAASmD,eAChBgE,SAAWC,GAAM5C,EAAkB,iBAAkB4C,EAAEC,OAAO3C,OAC9DO,UAAU,oJAUxBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,UACEkC,KAAK,SACL9B,QAASA,IAAMtF,GAAU,GACzBmF,UAAU,4IAA2IC,SAEpJ3F,EAAE,SAAU,aAGfyF,EAAAA,EAAAA,KAAA,UACEkC,KAAK,SACL0B,SAAUxF,EACV6B,UAAU,gHAA+GC,SAExH9B,EAAU7D,EAAE,SAAU,aAAeA,EAAE,iBAAkB,8B", "sources": ["pages/Forms/DischargeAssessment.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nconst DischargeAssessment = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  // Form state\n  const [formData, setFormData] = useState({\n    // Header Information\n    documentNumber: 'QP-',\n    issueDate: new Date().toISOString().split('T')[0],\n    version: '01',\n    reviewNumber: '01',\n    \n    // Patient Information\n    patientName: '',\n    mrNumber: '',\n    dischargeDate: '',\n    diagnosis: '',\n    admissionDate: '',\n    physician: '',\n    totalVisits: 0,\n    noShowCancellations: 0,\n    \n    // Evaluation Addendum\n    evaluationAddendum: [],\n    evaluationOther: '',\n    \n    // Treatment Received\n    treatmentReceived: '',\n    \n    // Summary of Progress\n    gait: '',\n    balanceCoordination: '',\n    pain: '',\n    functionalAssessment: '',\n    environmentalAssessment: '',\n    activitiesLimitation: '',\n    participateRestriction: '',\n    familySupport: '',\n    assistiveDevices: '',\n    riskFactors: '',\n    \n    // Patient/Caregiver Training\n    patientCaregiverTraining: '',\n    \n    // Reason for Discharge\n    dischargeReasons: [],\n    dischargeOther: '',\n    \n    // Recommendations\n    continueHEP: false,\n    continueHEPDetails: '',\n    equipmentNeeds: '',\n    followUpPhysician: false,\n    followUpPhysicianDetails: '',\n    recommendationOther: '',\n    followUpFrequency: '',\n    followUpDate: '',\n    followUpRiskFactors: '',\n    planReviewedWithFamily: false,\n    \n    // Signatures\n    therapistSignature: '',\n    therapistBadgeNo: '',\n    therapistDate: new Date().toISOString().split('T')[0],\n    comments: '',\n    dischargePlanReviewed: false,\n    therapistSignature2: '',\n    therapistBadgeNo2: '',\n    therapistDate2: new Date().toISOString().split('T')[0]\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [activeSection, setActiveSection] = useState('patient-info');\n\n  // Evaluation addendum options\n  const evaluationOptions = [\n    'Functional', 'LE', 'UE', 'Ankle/Foot', 'Cervical', 'Lumbar', \n    'Amputee', 'Hand', 'Voice', 'Cognitive', 'Visual/Perceptual', \n    'Wound', 'Lymphedema', 'Pulmonary', 'Communication', 'Dysphagia', \n    'Urinary', 'Incontinence', 'Other'\n  ];\n\n  // Discharge reason options\n  const dischargeReasonOptions = [\n    'Goals Met',\n    'Medical Condition',\n    'Reached Maximal Potential',\n    'Non-Compliance',\n    'Objective findings inconsistent with patient\\'s complaints and/or diagnosis',\n    'Other'\n  ];\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      // Simulate API call to load patient data\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        setFormData(prev => ({\n          ...prev,\n          patientName: patientData.name || '',\n          mrNumber: patientData.mrNumber || '',\n          physician: patientData.physician || '',\n          diagnosis: patientData.diagnosis || ''\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleCheckboxChange = (field, option, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: checked \n        ? [...prev[field], option]\n        : prev[field].filter(item => item !== option)\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.patientName.trim()) newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    if (!formData.mrNumber.trim()) newErrors.mrNumber = t('mrNumberRequired', 'MR number is required');\n    if (!formData.dischargeDate) newErrors.dischargeDate = t('dischargeDateRequired', 'Discharge date is required');\n    if (!formData.diagnosis.trim()) newErrors.diagnosis = t('diagnosisRequired', 'Diagnosis is required');\n    if (!formData.admissionDate) newErrors.admissionDate = t('admissionDateRequired', 'Admission date is required');\n    if (!formData.physician.trim()) newErrors.physician = t('physicianRequired', 'Physician is required');\n    if (!formData.treatmentReceived.trim()) newErrors.treatmentReceived = t('treatmentReceivedRequired', 'Treatment received is required');\n    if (!formData.patientCaregiverTraining.trim()) newErrors.patientCaregiverTraining = t('trainingRequired', 'Patient/Caregiver training is required');\n    if (formData.dischargeReasons.length === 0) newErrors.dischargeReasons = t('dischargeReasonRequired', 'At least one discharge reason is required');\n    if (!formData.therapistSignature.trim()) newErrors.therapistSignature = t('therapistSignatureRequired', 'Therapist signature is required');\n    if (!formData.therapistBadgeNo.trim()) newErrors.therapistBadgeNo = t('badgeNumberRequired', 'Badge number is required');\n\n    // Date validation\n    if (formData.admissionDate && formData.dischargeDate) {\n      if (new Date(formData.admissionDate) >= new Date(formData.dischargeDate)) {\n        newErrors.dischargeDate = t('dischargeDateAfterAdmission', 'Discharge date must be after admission date');\n      }\n    }\n\n    // Future date validation for follow-up\n    if (formData.followUpDate && new Date(formData.followUpDate) <= new Date()) {\n      newErrors.followUpDate = t('followUpDateFuture', 'Follow-up date must be in the future');\n    }\n\n    // Number validation\n    if (formData.totalVisits < 0) newErrors.totalVisits = t('totalVisitsPositive', 'Total visits must be positive');\n    if (formData.noShowCancellations < 0) newErrors.noShowCancellations = t('noShowPositive', 'No-show/cancellations must be positive');\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      const submissionData = {\n        ...formData,\n        submittedBy: user.id,\n        submittedAt: new Date().toISOString(),\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/discharge-assessments/public', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        alert(t('dischargeAssessmentSaved', 'Discharge assessment saved successfully!'));\n        navigate(`/patients/${patientId}`);\n      } else {\n        throw new Error('Failed to save discharge assessment');\n      }\n    } catch (error) {\n      console.error('Error saving discharge assessment:', error);\n      alert(t('errorSaving', 'Error saving discharge assessment. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n\n      // Create a comprehensive PDF data structure\n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/discharge-assessments/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `discharge-assessment-${formData.patientName.replace(/\\s+/g, '-')}-${formData.dischargeDate}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n\n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('outpatientDischargeAssessment', 'Outpatient Discharge Assessment')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                {t('dischargeAssessmentDescription', 'Complete discharge evaluation and recommendations')}\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={generatePDF}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {t('generatePDF', 'Generate PDF')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Document Metadata */}\n        <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n            <div>\n              <label className=\"block text-gray-600 dark:text-gray-400 mb-1\">\n                {t('documentNumber', 'Document Number')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.documentNumber}\n                onChange={(e) => handleInputChange('documentNumber', e.target.value)}\n                className=\"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-gray-600 dark:text-gray-400 mb-1\">\n                {t('issueDate', 'Issue Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={formData.issueDate}\n                onChange={(e) => handleInputChange('issueDate', e.target.value)}\n                className=\"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-gray-600 dark:text-gray-400 mb-1\">\n                {t('version', 'Version')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.version}\n                onChange={(e) => handleInputChange('version', e.target.value)}\n                className=\"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-gray-600 dark:text-gray-400 mb-1\">\n                {t('reviewNumber', 'Review Number')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.reviewNumber}\n                onChange={(e) => handleInputChange('reviewNumber', e.target.value)}\n                className=\"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex space-x-8 px-6\" aria-label=\"Tabs\">\n            {[\n              { id: 'patient-info', label: t('patientInformation', 'Patient Information') },\n              { id: 'evaluation', label: t('evaluation', 'Evaluation') },\n              { id: 'progress', label: t('progress', 'Progress Summary') },\n              { id: 'discharge', label: t('discharge', 'Discharge & Recommendations') },\n              { id: 'signatures', label: t('signatures', 'Signatures') }\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveSection(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                  activeSection === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Patient Information Section */}\n        {activeSection === 'patient-info' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('patientInformation', 'Patient Information')}\n            </h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('patientName', 'Patient Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.patientName}\n                  onChange={(e) => handleInputChange('patientName', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.patientName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.patientName && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('mrNumber', 'MR Number')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.mrNumber}\n                  onChange={(e) => handleInputChange('mrNumber', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.mrNumber ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.mrNumber && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.mrNumber}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('dischargeDate', 'Discharge Date')} *\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.dischargeDate}\n                  onChange={(e) => handleInputChange('dischargeDate', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.dischargeDate ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.dischargeDate && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.dischargeDate}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('admissionDate', 'Admission Date')} *\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.admissionDate}\n                  onChange={(e) => handleInputChange('admissionDate', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.admissionDate ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.admissionDate && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.admissionDate}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('physician', 'Physician')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.physician}\n                  onChange={(e) => handleInputChange('physician', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.physician ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.physician && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.physician}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('totalVisits', 'Number of Total Visits')} *\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.totalVisits}\n                  onChange={(e) => handleInputChange('totalVisits', parseInt(e.target.value) || 0)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.totalVisits ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.totalVisits && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.totalVisits}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('noShowCancellations', 'Number of No-show/Cancellations')} *\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.noShowCancellations}\n                  onChange={(e) => handleInputChange('noShowCancellations', parseInt(e.target.value) || 0)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                    errors.noShowCancellations ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                  }`}\n                  required\n                />\n                {errors.noShowCancellations && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.noShowCancellations}</p>\n                )}\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('diagnosis', 'Diagnosis')} *\n              </label>\n              <textarea\n                value={formData.diagnosis}\n                onChange={(e) => handleInputChange('diagnosis', e.target.value)}\n                rows={3}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.diagnosis ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.diagnosis && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.diagnosis}</p>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Evaluation Section */}\n        {activeSection === 'evaluation' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('evaluationAddendum', 'Evaluation Addendum')}\n            </h2>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6\">\n              {evaluationOptions.map((option) => (\n                <label key={option} className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.evaluationAddendum.includes(option)}\n                    onChange={(e) => handleCheckboxChange('evaluationAddendum', option, e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option}</span>\n                </label>\n              ))}\n            </div>\n\n            {formData.evaluationAddendum.includes('Other') && (\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('otherEvaluation', 'Other Evaluation Details')}\n                </label>\n                <textarea\n                  value={formData.evaluationOther}\n                  onChange={(e) => handleInputChange('evaluationOther', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('enterOtherDetails', 'Enter other evaluation details...')}\n                />\n              </div>\n            )}\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('treatmentReceived', 'Treatment Received')} *\n              </label>\n              <textarea\n                value={formData.treatmentReceived}\n                onChange={(e) => handleInputChange('treatmentReceived', e.target.value)}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.treatmentReceived ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder={t('describeTreatmentReceived', 'Describe the treatment received during the course of care...')}\n                required\n              />\n              {errors.treatmentReceived && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.treatmentReceived}</p>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Progress Summary Section */}\n        {activeSection === 'progress' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('summaryOfProgress', 'Summary of Progress')}\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('gait', 'Gait')}\n                </label>\n                <textarea\n                  value={formData.gait}\n                  onChange={(e) => handleInputChange('gait', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeGaitProgress', 'Describe gait progress...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('balanceCoordination', 'Balance/Coordination')}\n                </label>\n                <textarea\n                  value={formData.balanceCoordination}\n                  onChange={(e) => handleInputChange('balanceCoordination', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeBalanceProgress', 'Describe balance/coordination progress...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('pain', 'PAIN')}\n                </label>\n                <textarea\n                  value={formData.pain}\n                  onChange={(e) => handleInputChange('pain', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describePainProgress', 'Describe pain management progress...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('functionalAssessment', 'Functional Assessment')}\n                </label>\n                <textarea\n                  value={formData.functionalAssessment}\n                  onChange={(e) => handleInputChange('functionalAssessment', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeFunctionalProgress', 'Describe functional assessment progress...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('environmentalAssessment', 'Environmental Assessment')}\n                </label>\n                <textarea\n                  value={formData.environmentalAssessment}\n                  onChange={(e) => handleInputChange('environmentalAssessment', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeEnvironmentalProgress', 'Describe environmental assessment...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('activitiesLimitation', 'Activities Limitation')}\n                </label>\n                <textarea\n                  value={formData.activitiesLimitation}\n                  onChange={(e) => handleInputChange('activitiesLimitation', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeActivitiesLimitation', 'Describe activities limitation...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('participateRestriction', 'Participate Restriction')}\n                </label>\n                <textarea\n                  value={formData.participateRestriction}\n                  onChange={(e) => handleInputChange('participateRestriction', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeParticipateRestriction', 'Describe participation restriction...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('familySupport', 'Family Support')}\n                </label>\n                <textarea\n                  value={formData.familySupport}\n                  onChange={(e) => handleInputChange('familySupport', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeFamilySupport', 'Describe family support...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('assistiveDevices', 'Assistive Devices')}\n                </label>\n                <textarea\n                  value={formData.assistiveDevices}\n                  onChange={(e) => handleInputChange('assistiveDevices', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeAssistiveDevices', 'Describe assistive devices used...')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('riskFactors', 'Risk Factors')}\n                </label>\n                <textarea\n                  value={formData.riskFactors}\n                  onChange={(e) => handleInputChange('riskFactors', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeRiskFactors', 'Describe risk factors...')}\n                />\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('patientCaregiverTraining', 'Patient/Caregiver Training')} *\n              </label>\n              <textarea\n                value={formData.patientCaregiverTraining}\n                onChange={(e) => handleInputChange('patientCaregiverTraining', e.target.value)}\n                rows={4}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.patientCaregiverTraining ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                placeholder={t('describeTraining', 'Describe patient and caregiver training provided...')}\n                required\n              />\n              {errors.patientCaregiverTraining && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.patientCaregiverTraining}</p>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Discharge & Recommendations Section */}\n        {activeSection === 'discharge' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('dischargeAndRecommendations', 'Discharge & Recommendations')}\n            </h2>\n\n            {/* Reason for Discharge */}\n            <div className=\"mb-8\">\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('reasonForDischarge', 'Reason for Discharge')} *\n              </h3>\n              <div className=\"space-y-3\">\n                {dischargeReasonOptions.map((reason) => (\n                  <label key={reason} className=\"flex items-start space-x-3\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.dischargeReasons.includes(reason)}\n                      onChange={(e) => handleCheckboxChange('dischargeReasons', reason, e.target.checked)}\n                      className=\"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                    <span className=\"text-sm text-gray-700 dark:text-gray-300\">{reason}</span>\n                  </label>\n                ))}\n              </div>\n              {formData.dischargeReasons.includes('Other') && (\n                <div className=\"mt-4\">\n                  <textarea\n                    value={formData.dischargeOther}\n                    onChange={(e) => handleInputChange('dischargeOther', e.target.value)}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('enterOtherDischargeReason', 'Enter other discharge reason...')}\n                  />\n                </div>\n              )}\n              {errors.dischargeReasons && (\n                <p className=\"text-red-500 text-sm mt-2\">{errors.dischargeReasons}</p>\n              )}\n            </div>\n\n            {/* Recommendations */}\n            <div className=\"mb-8\">\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('recommendations', 'Recommendations')}\n              </h3>\n\n              <div className=\"space-y-6\">\n                {/* Continue with HEP */}\n                <div>\n                  <label className=\"flex items-center space-x-3 mb-3\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.continueHEP}\n                      onChange={(e) => handleInputChange('continueHEP', e.target.checked)}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      {t('continueWithHEP', 'Continue with HEP (Home Exercise Program) as issued')}\n                    </span>\n                  </label>\n                  {formData.continueHEP && (\n                    <textarea\n                      value={formData.continueHEPDetails}\n                      onChange={(e) => handleInputChange('continueHEPDetails', e.target.value)}\n                      rows={3}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      placeholder={t('enterHEPDetails', 'Enter HEP details and instructions...')}\n                    />\n                  )}\n                </div>\n\n                {/* Equipment Needs */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('equipmentNeeds', 'Equipment Needs')}\n                  </label>\n                  <textarea\n                    value={formData.equipmentNeeds}\n                    onChange={(e) => handleInputChange('equipmentNeeds', e.target.value)}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('describeEquipmentNeeds', 'Describe any equipment needs...')}\n                  />\n                </div>\n\n                {/* Follow up with physician */}\n                <div>\n                  <label className=\"flex items-center space-x-3 mb-3\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.followUpPhysician}\n                      onChange={(e) => handleInputChange('followUpPhysician', e.target.checked)}\n                      className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                    />\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                      {t('followUpWithPhysician', 'Follow up with physician')}\n                    </span>\n                  </label>\n                  {formData.followUpPhysician && (\n                    <textarea\n                      value={formData.followUpPhysicianDetails}\n                      onChange={(e) => handleInputChange('followUpPhysicianDetails', e.target.value)}\n                      rows={3}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      placeholder={t('enterFollowUpDetails', 'Enter follow-up details...')}\n                    />\n                  )}\n                </div>\n\n                {/* Other Recommendations */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('otherRecommendations', 'Other Recommendations')}\n                  </label>\n                  <textarea\n                    value={formData.recommendationOther}\n                    onChange={(e) => handleInputChange('recommendationOther', e.target.value)}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('enterOtherRecommendations', 'Enter other recommendations...')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Follow-up Information */}\n            <div className=\"mb-8\">\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('followUpInformation', 'Follow-up Information')}\n              </h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('followUpFrequency', 'How often patient needs follow-up?')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.followUpFrequency}\n                    onChange={(e) => handleInputChange('followUpFrequency', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder={t('enterFrequency', 'e.g., Every 3 months')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('followUpDate', 'Date follow up appointment')}\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.followUpDate}\n                    onChange={(e) => handleInputChange('followUpDate', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.followUpDate ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                  />\n                  {errors.followUpDate && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.followUpDate}</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('followUpRiskFactors', 'Risk factors (affect the follow up)')}\n                </label>\n                <textarea\n                  value={formData.followUpRiskFactors}\n                  onChange={(e) => handleInputChange('followUpRiskFactors', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('describeFollowUpRiskFactors', 'Describe risk factors that may affect follow-up...')}\n                />\n              </div>\n\n              <div className=\"mt-4\">\n                <label className=\"flex items-center space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.planReviewedWithFamily}\n                    onChange={(e) => handleInputChange('planReviewedWithFamily', e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    {t('planReviewedWithFamily', 'Plan of follow up reviewed with patient / family')}\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Signatures Section */}\n        {activeSection === 'signatures' && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n              {t('signaturesAndComments', 'Signatures and Comments')}\n            </h2>\n\n            <div className=\"space-y-6\">\n              {/* Primary Signature */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('therapistSignature', 'Therapist Signature')} *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.therapistSignature}\n                    onChange={(e) => handleInputChange('therapistSignature', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.therapistSignature ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                    placeholder={t('enterTherapistName', 'Enter therapist name')}\n                    required\n                  />\n                  {errors.therapistSignature && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.therapistSignature}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('badgeNumber', 'Badge No.')} *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.therapistBadgeNo}\n                    onChange={(e) => handleInputChange('therapistBadgeNo', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                      errors.therapistBadgeNo ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                    }`}\n                    placeholder={t('enterBadgeNumber', 'Enter badge number')}\n                    required\n                  />\n                  {errors.therapistBadgeNo && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.therapistBadgeNo}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('date', 'Date')} *\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.therapistDate}\n                    onChange={(e) => handleInputChange('therapistDate', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    required\n                  />\n                </div>\n              </div>\n\n              {/* Comments */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('comments', 'Comments')}\n                </label>\n                <textarea\n                  value={formData.comments}\n                  onChange={(e) => handleInputChange('comments', e.target.value)}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  placeholder={t('enterComments', 'Enter any additional comments...')}\n                />\n              </div>\n\n              {/* Discharge Planning Reviewed */}\n              <div>\n                <label className=\"flex items-center space-x-3\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.dischargePlanReviewed}\n                    onChange={(e) => handleInputChange('dischargePlanReviewed', e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    {t('dischargePlanningReviewed', 'Discharge planning was reviewed with patient/family')}\n                  </span>\n                </label>\n              </div>\n\n              {/* Secondary Signature (Page 2) */}\n              <div className=\"border-t border-gray-200 dark:border-gray-600 pt-6\">\n                <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                  {t('confirmationSignature', 'Confirmation Signature (Page 2)')}\n                </h3>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('therapistSignatureTitle', 'Therapist Signature/Title')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.therapistSignature2}\n                      onChange={(e) => handleInputChange('therapistSignature2', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      placeholder={t('enterTherapistNameTitle', 'Enter therapist name and title')}\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('badgeNumber', 'Badge No.')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={formData.therapistBadgeNo2}\n                      onChange={(e) => handleInputChange('therapistBadgeNo2', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      placeholder={t('enterBadgeNumber', 'Enter badge number')}\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('date', 'Date')}\n                    </label>\n                    <input\n                      type=\"date\"\n                      value={formData.therapistDate2}\n                      onChange={(e) => handleInputChange('therapistDate2', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between\">\n          <button\n            type=\"button\"\n            onClick={() => navigate(-1)}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          \n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? t('saving', 'Saving...') : t('saveAssessment', 'Save Assessment')}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DischargeAssessment;\n"], "names": ["Discharge<PERSON><PERSON><PERSON>ent", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "formData", "setFormData", "useState", "documentNumber", "issueDate", "Date", "toISOString", "split", "version", "reviewNumber", "patientName", "mr<PERSON><PERSON><PERSON>", "dischargeDate", "diagnosis", "admissionDate", "physician", "totalVisits", "noShowCancellations", "evaluationAddendum", "evaluationOther", "treatmentReceived", "gait", "balanceCoordination", "pain", "functionalAssessment", "environmentalAssessment", "activitiesLimitation", "participateRestriction", "familySupport", "assistiveDevices", "riskFactors", "patientCaregiverTraining", "dischargeReasons", "dischargeOther", "continueHEP", "continueHEPDetails", "equipmentNeeds", "followUpPhysician", "followUpPhysicianDetails", "recommendationOther", "followUpFrequency", "followUpDate", "followUpRiskFactors", "planReviewedWithFamily", "therapistSignature", "therapistBadgeNo", "therapistDate", "comments", "dischargePlanReviewed", "therapistSignature2", "therapistBadgeNo2", "therapistDate2", "loading", "setLoading", "errors", "setErrors", "activeSection", "setActiveSection", "useEffect", "loadPatientData", "async", "response", "fetch", "concat", "ok", "patientData", "json", "prev", "_objectSpread", "name", "error", "console", "handleInputChange", "field", "value", "handleCheckboxChange", "option", "checked", "filter", "item", "_jsx", "className", "children", "_jsxs", "onClick", "pdfData", "generatedAt", "generatedBy", "email", "method", "headers", "localStorage", "getItem", "body", "JSON", "stringify", "Error", "status", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "alert", "type", "onChange", "e", "target", "id", "label", "map", "tab", "onSubmit", "preventDefault", "validateForm", "newErrors", "trim", "length", "Object", "keys", "submissionData", "submittedBy", "submittedAt", "required", "min", "parseInt", "rows", "includes", "placeholder", "reason", "disabled"], "sourceRoot": ""}