"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8922],{8922:(e,l,n)=>{n.r(l),n.d(l,{default:()=>d});var t=n(2555),s=n(5043),a=n(3216),r=n(7921),i=n(4528),o=n(3768),c=n(579);const d=()=>{var e,l;const{t:n,isRTL:d}=(0,r.o)(),{user:u}=(0,i.A)(),{patientId:g}=(0,a.g)(),m=(0,a.Zp)(),[x,p]=(0,s.useState)(!1),[b,h]=(0,s.useState)("berg-balance"),[f,y]=(0,s.useState)({}),[v,k]=(0,s.useState)({patientId:g||"",assessmentType:"progress",assessmentDate:(new Date).toISOString().split("T")[0],bergBalanceScale:{totalScore:null,riskLevel:null,individualScores:{sittingToStanding:null,standingUnsupported:null,sittingUnsupported:null,standingToSitting:null,transfers:null,standingEyesClosed:null,standingFeetTogether:null,reachingForward:null,pickingUpObject:null,lookingOverShoulders:null,turning360Degrees:null,placingFeetOnStool:null,standingOneFoot:null,standingTandem:null},notes:""},timedUpAndGo:{timeInSeconds:null,riskLevel:null,assistiveDevice:"none",notes:""},manualMuscleTesting:{upperExtremity:{rightShoulder:{flexion:null,extension:null,abduction:null,adduction:null},leftShoulder:{flexion:null,extension:null,abduction:null,adduction:null},rightElbow:{flexion:null,extension:null},leftElbow:{flexion:null,extension:null},rightWrist:{flexion:null,extension:null},leftWrist:{flexion:null,extension:null}},lowerExtremity:{rightHip:{flexion:null,extension:null,abduction:null,adduction:null},leftHip:{flexion:null,extension:null,abduction:null,adduction:null},rightKnee:{flexion:null,extension:null},leftKnee:{flexion:null,extension:null},rightAnkle:{dorsiflexion:null,plantarflexion:null},leftAnkle:{dorsiflexion:null,plantarflexion:null}},trunk:{flexion:null,extension:null,lateralFlexionRight:null,lateralFlexionLeft:null},overallStrengthIndex:null,notes:""},painScale:{currentPain:null,averagePain24h:null,worstPain24h:null,painAtRest:null,painWithActivity:null,painLocation:[],painQuality:[],painPattern:null,notes:""},functionalIndependenceMeasure:{selfCare:{eating:null,grooming:null,bathing:null,dressingUpper:null,dressingLower:null,toileting:null},sphincterControl:{bladder:null,bowel:null},transfers:{bedChairWheelchair:null,toilet:null,tubShower:null},locomotion:{walkWheelchair:null,stairs:null},communication:{comprehension:null,expression:null},socialCognition:{socialInteraction:null,problemSolving:null,memory:null},totalScore:null,motorScore:null,cognitiveScore:null,notes:""},rangeOfMotion:{cervicalSpine:{flexion:null,extension:null,lateralFlexionRight:null,lateralFlexionLeft:null,rotationRight:null,rotationLeft:null},lumbarSpine:{flexion:null,extension:null,lateralFlexionRight:null,lateralFlexionLeft:null},notes:""},clinicalNotes:"",recommendations:"",nextAssessmentDate:""}),A=[{id:"berg-balance",label:n("bergBalanceScale","Berg Balance Scale"),icon:"fas fa-balance-scale",color:"blue"},{id:"tug-test",label:n("tugTest","Timed Up & Go"),icon:"fas fa-walking",color:"green"},{id:"muscle-testing",label:n("muscleTestingMMT","Manual Muscle Testing"),icon:"fas fa-dumbbell",color:"purple"},{id:"pain-scale",label:n("painScaleVAS","Pain Scale (VAS)"),icon:"fas fa-heartbeat",color:"red"},{id:"fim-scale",label:n("functionalIndependence","Functional Independence (FIM)"),icon:"fas fa-user-check",color:"orange"},{id:"range-motion",label:n("rangeOfMotion","Range of Motion"),icon:"fas fa-arrows-alt",color:"teal"}],j=function(e,l,n){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;k(a=>s?(0,t.A)((0,t.A)({},a),{},{[e]:(0,t.A)((0,t.A)({},a[e]),{},{[l]:(0,t.A)((0,t.A)({},a[e][l]),{},{[s]:n})})}):(0,t.A)((0,t.A)({},a),{},{[e]:(0,t.A)((0,t.A)({},a[e]),{},{[l]:n})}));const a=s?"".concat(e,".").concat(l,".").concat(s):"".concat(e,".").concat(l);f[a]&&y(e=>(0,t.A)((0,t.A)({},e),{},{[a]:null}))};return(0,s.useEffect)(()=>{(()=>{const e=v.bergBalanceScale.individualScores,l=Object.values(e).reduce((e,l)=>e+(l||0),0);let n="low-risk";l<21?n="high-risk":l<41&&(n="moderate-risk"),j("bergBalanceScale","totalScore",l),j("bergBalanceScale","riskLevel",n)})()},[v.bergBalanceScale.individualScores]),(0,s.useEffect)(()=>{(()=>{const e=v.timedUpAndGo.timeInSeconds;if(!e)return;let l="normal";e>30?l="severe-impairment":e>20?l="moderate-impairment":e>14&&(l="mild-impairment"),j("timedUpAndGo","riskLevel",l)})()},[v.timedUpAndGo.timeInSeconds]),(0,s.useEffect)(()=>{(()=>{const e=v.functionalIndependenceMeasure,l=[...Object.values(e.selfCare),...Object.values(e.sphincterControl),...Object.values(e.transfers),...Object.values(e.locomotion)].reduce((e,l)=>e+(l||0),0),n=[...Object.values(e.communication),...Object.values(e.socialCognition)].reduce((e,l)=>e+(l||0),0),t=l+n;j("functionalIndependenceMeasure","motorScore",l),j("functionalIndependenceMeasure","cognitiveScore",n),j("functionalIndependenceMeasure","totalScore",t)})()},[v.functionalIndependenceMeasure]),(0,c.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,c.jsx)("div",{className:"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8",children:(0,c.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,c.jsx)("div",{className:"px-6 py-6",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:n("clinicalIndicatorsAssessment","Clinical Indicators Assessment")}),(0,c.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-chart-line text-blue-500 mr-2"}),n("clinicalIndicatorsDescription","Comprehensive clinical assessment using standardized measurement tools")]})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-blue-400 to-purple-400 text-white px-4 py-2 rounded-full shadow-lg",children:[(0,c.jsx)("i",{className:"fas fa-stethoscope mr-2"}),n("medicalAssessment","Medical Assessment")]})]})})})}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("assessmentDetails","Assessment Details")}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("assessmentType","Assessment Type")," *"]}),(0,c.jsxs)("select",{value:v.assessmentType,onChange:e=>k(l=>(0,t.A)((0,t.A)({},l),{},{assessmentType:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0,children:[(0,c.jsx)("option",{value:"initial",children:n("initialAssessment","Initial Assessment")}),(0,c.jsx)("option",{value:"progress",children:n("progressAssessment","Progress Assessment")}),(0,c.jsx)("option",{value:"discharge",children:n("dischargeAssessment","Discharge Assessment")}),(0,c.jsx)("option",{value:"follow-up",children:n("followUpAssessment","Follow-up Assessment")})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[n("assessmentDate","Assessment Date")," *"]}),(0,c.jsx)("input",{type:"date",value:v.assessmentDate,onChange:e=>k(l=>(0,t.A)((0,t.A)({},l),{},{assessmentDate:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("nextAssessmentDate","Next Assessment Date")}),(0,c.jsx)("input",{type:"date",value:v.nextAssessmentDate,onChange:e=>k(l=>(0,t.A)((0,t.A)({},l),{},{nextAssessmentDate:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden",children:[(0,c.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,c.jsx)("nav",{className:"flex overflow-x-auto",children:A.map(e=>(0,c.jsxs)("button",{onClick:()=>h(e.id),className:"flex-shrink-0 px-6 py-4 text-sm font-medium border-b-2 transition-colors ".concat(b===e.id?"border-".concat(e.color,"-500 text-").concat(e.color,"-600 dark:text-").concat(e.color,"-400 bg-").concat(e.color,"-50 dark:bg-").concat(e.color,"-900/20"):"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,c.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})}),(0,c.jsx)("div",{className:"p-6",children:(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("i",{className:"".concat(null===(e=A.find(e=>e.id===b))||void 0===e?void 0:e.icon," text-4xl text-gray-300 dark:text-gray-600 mb-4")}),(0,c.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:null===(l=A.find(e=>e.id===b))||void 0===l?void 0:l.label}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:n("assessmentFormContent","Assessment form content will be implemented here")})]})})]}),(0,c.jsxs)("div",{className:"mt-8 flex justify-between",children:[(0,c.jsxs)("button",{type:"button",onClick:()=>m(-1),className:"px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-arrow-left mr-2"}),n("cancel","Cancel")]}),(0,c.jsxs)("button",{onClick:async e=>{if(e.preventDefault(),(()=>{const e={};return v.patientId||(e.patientId=n("patientIdRequired","Patient ID is required")),v.assessmentType||(e.assessmentType=n("assessmentTypeRequired","Assessment type is required")),y(e),0===Object.keys(e).length})())try{p(!0);const e=(0,t.A)((0,t.A)({},v),{},{assessedBy:null===u||void 0===u?void 0:u.id,status:"completed"}),l=await fetch("/api/v1/clinical-indicators",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(e)});if(!l.ok)throw new Error("Failed to save clinical indicators");await l.json();o.Ay.success(n("clinicalIndicatorsSaved","Clinical indicators saved successfully!")),m(g?"/patients/".concat(g):"/forms/submissions")}catch(l){console.error("Error saving clinical indicators:",l),o.Ay.error(n("errorSaving","Error saving clinical indicators. Please try again."))}finally{p(!1)}else o.Ay.error(n("pleaseFixErrors","Please fix the errors before submitting"))},disabled:x,className:"px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:from-blue-600 hover:to-purple-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg",children:[(0,c.jsx)("i",{className:"fas fa-save mr-2"}),x?n("saving","Saving..."):n("saveAssessment","Save Assessment")]})]})]})}}}]);
//# sourceMappingURL=8922.bfff2ed2.chunk.js.map