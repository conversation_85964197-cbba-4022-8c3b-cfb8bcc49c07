{"version": 3, "file": "static/js/3364.dc7f9a4d.chunk.js", "mappings": "mMAIA,MAuJA,EAvJkBA,KAChB,MAAOC,EAAQC,IAAaC,EAAAA,EAAAA,UAAS,OAC9BC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAqFvC,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0BAAyBC,SAAC,0BAExCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,UACEC,QAzFcC,UACpBN,GAAW,GACXH,EAAU,MAEV,IACEU,QAAQC,IAAI,8BACZ,MAAMC,QAAiBC,EAAAA,GAAIC,KAAK,cAAe,CAC7CC,MAAO,wBACPC,SAAU,cAGZN,QAAQC,IAAI,uBAAwBC,GACpCZ,EAAU,CACRiB,KAAM,aACNC,SAAS,EACTC,KAAMP,GAEV,CAAE,MAAOQ,GAAQ,IAADC,EACdX,QAAQU,MAAM,oBAAqBA,GACnCpB,EAAU,CACRiB,KAAM,aACNC,SAAS,EACTE,MAAOA,EAAME,QACbC,QAAuB,QAAhBF,EAAED,EAAMR,gBAAQ,IAAAS,OAAA,EAAdA,EAAgBF,MAE7B,CAAC,QACChB,GAAW,EACb,GA+DMqB,SAAUtB,EACVG,UAAU,2EAA0EC,SACrF,0BAIDC,EAAAA,EAAAA,KAAA,UACEC,QAnEgBC,UACtBN,GAAW,GACXH,EAAU,MAEV,IACEU,QAAQC,IAAI,2BACZ,MAAMC,QAAiBa,EAAAA,EAAYC,MAAM,CACvCC,SAAU,wBACVX,SAAU,cAGZN,QAAQC,IAAI,yBAA0BC,GACtCZ,EAAU,CACRiB,KAAM,eACNC,SAAS,EACTC,KAAMP,GAEV,CAAE,MAAOQ,GACPV,QAAQU,MAAM,sBAAuBA,GACrCpB,EAAU,CACRiB,KAAM,eACNC,SAAS,EACTE,MAAOA,EAAME,SAEjB,CAAC,QACCnB,GAAW,EACb,GA0CMqB,SAAUtB,EACVG,UAAU,6EAA4EC,SACvF,uBAIDC,EAAAA,EAAAA,KAAA,UACEC,QA9CgBoB,KACtB,MAAMC,EAAQC,aAAaC,QAAQ,iBAC7BC,EAAOF,aAAaC,QAAQ,gBAElC/B,EAAU,CACRiB,KAAM,cACNE,KAAM,CACJU,MAAOA,EAAQA,EAAMI,UAAU,EAAG,IAAM,MAAQ,YAChDD,KAAMA,EAAOE,KAAKC,MAAMH,GAAQ,YAChCI,gBAAiBX,EAAAA,EAAYW,sBAsC3B/B,UAAU,2DAA0DC,SACrE,uBAIDC,EAAAA,EAAAA,KAAA,UACEC,QAvCgB6B,KACtBP,aAAaQ,WAAW,iBACxBR,aAAaQ,WAAW,uBACjBzB,EAAAA,GAAI0B,SAASC,QAAQC,OAAsB,cAClDzC,EAAU,CACRiB,KAAM,QACNK,QAAS,yBAkCLjB,UAAU,qDAAoDC,SAC/D,yBAKFJ,IACCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+EACfE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,OAAMC,SAAC,kBAIvBP,IACCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,iBAAgBC,SAAA,CAAC,WAASP,EAAOkB,KAAK,SACpDV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SACnC4B,KAAKQ,UAAU3C,EAAQ,KAAM,SAKpCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iCAAgCC,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iBAAgBC,SAAC,uBAC/BF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,UAASC,SAAA,EACrBF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,kBAAsB,IAAEqC,mCACpCvC,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,cAAkB,IAAEqC,iBAChCvC,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,iBAAqB,IAAEsC,OAAOC,SAASC,iB", "sources": ["components/Debug/AuthDebug.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport authService from '../../services/authService';\nimport { apiHelpers as api } from '../../services/api';\n\nconst AuthDebug = () => {\n  const [result, setResult] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  const testDirectAPI = async () => {\n    setLoading(true);\n    setResult(null);\n    \n    try {\n      console.log('Testing direct API call...');\n      const response = await api.post('/auth/login', {\n        email: '<EMAIL>',\n        password: 'Admin123!'\n      });\n      \n      console.log('Direct API response:', response);\n      setResult({\n        type: 'direct-api',\n        success: true,\n        data: response\n      });\n    } catch (error) {\n      console.error('Direct API error:', error);\n      setResult({\n        type: 'direct-api',\n        success: false,\n        error: error.message,\n        details: error.response?.data\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testAuthService = async () => {\n    setLoading(true);\n    setResult(null);\n    \n    try {\n      console.log('Testing auth service...');\n      const response = await authService.login({\n        username: '<EMAIL>',\n        password: 'Admin123!'\n      });\n      \n      console.log('Auth service response:', response);\n      setResult({\n        type: 'auth-service',\n        success: true,\n        data: response\n      });\n    } catch (error) {\n      console.error('Auth service error:', error);\n      setResult({\n        type: 'auth-service',\n        success: false,\n        error: error.message\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkStoredData = () => {\n    const token = localStorage.getItem('pt_auth_token');\n    const user = localStorage.getItem('pt_user_data');\n    \n    setResult({\n      type: 'stored-data',\n      data: {\n        token: token ? token.substring(0, 20) + '...' : 'Not found',\n        user: user ? JSON.parse(user) : 'Not found',\n        isAuthenticated: authService.isAuthenticated()\n      }\n    });\n  };\n\n  const clearStoredData = () => {\n    localStorage.removeItem('pt_auth_token');\n    localStorage.removeItem('pt_user_data');\n    delete api.defaults.headers.common['Authorization'];\n    setResult({\n      type: 'clear',\n      message: 'Stored data cleared'\n    });\n  };\n\n  return (\n    <div className=\"p-6 max-w-4xl mx-auto\">\n      <h1 className=\"text-2xl font-bold mb-6\">Authentication Debug</h1>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n        <button\n          onClick={testDirectAPI}\n          disabled={loading}\n          className=\"p-4 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\"\n        >\n          Test Direct API Call\n        </button>\n        \n        <button\n          onClick={testAuthService}\n          disabled={loading}\n          className=\"p-4 bg-green-600 text-white rounded hover:bg-green-700 disabled:opacity-50\"\n        >\n          Test Auth Service\n        </button>\n        \n        <button\n          onClick={checkStoredData}\n          className=\"p-4 bg-yellow-600 text-white rounded hover:bg-yellow-700\"\n        >\n          Check Stored Data\n        </button>\n        \n        <button\n          onClick={clearStoredData}\n          className=\"p-4 bg-red-600 text-white rounded hover:bg-red-700\"\n        >\n          Clear Stored Data\n        </button>\n      </div>\n\n      {loading && (\n        <div className=\"text-center py-4\">\n          <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          <p className=\"mt-2\">Testing...</p>\n        </div>\n      )}\n\n      {result && (\n        <div className=\"bg-gray-100 p-4 rounded-lg\">\n          <h3 className=\"font-bold mb-2\">Result ({result.type}):</h3>\n          <pre className=\"text-sm overflow-auto\">\n            {JSON.stringify(result, null, 2)}\n          </pre>\n        </div>\n      )}\n\n      <div className=\"mt-6 bg-blue-50 p-4 rounded-lg\">\n        <h3 className=\"font-bold mb-2\">Environment Info:</h3>\n        <ul className=\"text-sm\">\n          <li><strong>API Base URL:</strong> {process.env.REACT_APP_API_URL || 'http://localhost:5001/api/v1'}</li>\n          <li><strong>Node ENV:</strong> {process.env.NODE_ENV}</li>\n          <li><strong>Current URL:</strong> {window.location.href}</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthDebug;\n"], "names": ["AuthDebug", "result", "setResult", "useState", "loading", "setLoading", "_jsxs", "className", "children", "_jsx", "onClick", "async", "console", "log", "response", "api", "post", "email", "password", "type", "success", "data", "error", "_error$response", "message", "details", "disabled", "authService", "login", "username", "checkStoredData", "token", "localStorage", "getItem", "user", "substring", "JSON", "parse", "isAuthenticated", "clearStoredData", "removeItem", "defaults", "headers", "common", "stringify", "process", "window", "location", "href"], "sourceRoot": ""}