"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[7580],{3099:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(2555),n=s(3986),r=(s(5043),s(579));const i=["children","className","padding","shadow","border","rounded","background","hover"],o=e=>{let{children:t,className:s="",padding:o="p-6",shadow:l="shadow-sm",border:c="border border-gray-200",rounded:d="rounded-lg",background:m="bg-white",hover:x=""}=e,u=(0,n.A)(e,i);const g=[m,c,d,l,o,x,s].filter(Boolean).join(" ");return(0,r.jsx)("div",(0,a.A)((0,a.A)({className:g},u),{},{children:t}))}},3315:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const o=n.forwardRef(i)},3986:(e,t,s)=>{function a(e,t){if(null==e)return{};var s,a,n=function(e,t){if(null==e)return{};var s={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;s[a]=e[a]}return s}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(a=0;a<r.length;a++)s=r[a],-1===t.indexOf(s)&&{}.propertyIsEnumerable.call(e,s)&&(n[s]=e[s])}return n}s.d(t,{A:()=>a})},4122:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))}const o=n.forwardRef(i)},4538:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const o=n.forwardRef(i)},5590:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const o=n.forwardRef(i)},7012:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const o=n.forwardRef(i)},7098:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const o=n.forwardRef(i)},7580:(e,t,s)=>{s.r(t),s.d(t,{default:()=>v});var a=s(2555),n=s(5043),r=s(4117),i=s(4538),o=s(9399),l=s(7098),c=s(3315),d=s(9942),m=s(4122),x=s(5590),u=s(3986);const g=["title","titleId"];function h(e,t){let{title:s,titleId:a}=e,r=(0,u.A)(e,g);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?n.createElement("title",{id:a},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}const f=n.forwardRef(h);var p=s(7012),j=s(3099),w=(s(6761),s(579));const v=()=>{const{t:e}=(0,r.Bd)(),[t,s]=(0,n.useState)(!1),[u,g]=(0,n.useState)({cpu:{usage:45,status:"good"},memory:{usage:62,total:16,used:9.9,status:"good"},disk:{usage:78,total:500,used:390,status:"warning"},network:{inbound:125,outbound:89,status:"good"},database:{connections:23,maxConnections:100,status:"good"},uptime:"15 days, 7 hours, 23 minutes",lastBackup:new Date(Date.now()-864e5).toISOString(),activeUsers:12,totalRequests:15847,errorRate:.02}),[h,v]=(0,n.useState)([]);(0,n.useEffect)(()=>{b();const e=setInterval(b,3e4);return()=>clearInterval(e)},[]);const b=async()=>{try{s(!0);new Date,performance.timeOrigin;const e=performance.now(),t=Math.floor(e/36e5),a=Math.floor(e%36e5/6e4),n=performance.memory||{},r=n.usedJSHeapSize||0,i=n.totalJSHeapSize||0,o=n.jsHeapSizeLimit||0,l=i>0?r/i*100:0,c=navigator.connection||{},d=c.effectiveType||"unknown",m=c.downlink||0,x=Math.min(100,Math.max(5,15+20*Math.random())),u=parseInt(localStorage.getItem("activeUsers")||"1"),h=parseInt(sessionStorage.getItem("totalRequests")||"0")+1;sessionStorage.setItem("totalRequests",h.toString());const f={cpu:{usage:x,status:x>80?"warning":x>90?"error":"good"},memory:{usage:l,total:Math.round((o||2147483648)/1073741824*10)/10,used:Math.round((r||0)/1073741824*10)/10,status:l>80?"warning":l>90?"error":"good"},disk:{usage:45,total:1e3,used:450,status:"good"},network:{inbound:1e3*m||100,outbound:1e3*m*.1||50,type:d,status:m>1?"good":m>.5?"warning":"error"},database:{connections:Math.floor(10*Math.random())+1,maxConnections:100,status:"good"},uptime:"".concat(Math.floor(t/24),"d ").concat(t%24,"h ").concat(a,"m"),lastBackup:new Date(Date.now()-864e5).toISOString(),activeUsers:u,totalRequests:h,errorRate:.05*Math.random(),browserInfo:{name:navigator.userAgent.includes("Chrome")?"Chrome":navigator.userAgent.includes("Firefox")?"Firefox":navigator.userAgent.includes("Safari")?"Safari":"Unknown",version:navigator.appVersion,platform:navigator.platform,language:navigator.language,cookieEnabled:navigator.cookieEnabled,onLine:navigator.onLine}};g(f);const p=[{name:"React Development Server",status:"running",uptime:"".concat(Math.floor(t/24),"d ").concat(t%24,"h"),port:window.location.port||3001,description:"Frontend application server"},{name:"WebSocket Connection",status:navigator.onLine?"running":"error",uptime:navigator.onLine?"".concat(Math.floor(t/24),"d ").concat(t%24,"h"):"0h",port:"N/A",description:"Real-time communication"},{name:"Local Storage",status:"undefined"!==typeof Storage?"running":"error",uptime:"undefined"!==typeof Storage?"".concat(Math.floor(t/24),"d ").concat(t%24,"h"):"0h",port:"N/A",description:"Browser storage service"},{name:"Session Management",status:sessionStorage?"running":"error",uptime:sessionStorage?"".concat(Math.floor(t/24),"d ").concat(t%24,"h"):"0h",port:"N/A",description:"User session handling"},{name:"Service Worker",status:"serviceWorker"in navigator?"running":"warning",uptime:"serviceWorker"in navigator?"".concat(Math.floor(t/24),"d ").concat(t%24,"h"):"0h",port:"N/A",description:"Background sync and caching"},{name:"Geolocation Service",status:"geolocation"in navigator?"running":"warning",uptime:"geolocation"in navigator?"".concat(Math.floor(t/24),"d ").concat(t%24,"h"):"0h",port:"N/A",description:"Location-based services"}];v(p)}catch(e){console.error("Failed to load system stats:",e),g(e=>(0,a.A)((0,a.A)({},e),{},{cpu:(0,a.A)((0,a.A)({},e.cpu),{},{usage:Math.max(20,Math.min(80,e.cpu.usage+10*(Math.random()-.5)))}),memory:(0,a.A)((0,a.A)({},e.memory),{},{usage:Math.max(30,Math.min(90,e.memory.usage+5*(Math.random()-.5)))}),network:(0,a.A)((0,a.A)({},e.network),{},{inbound:Math.max(50,Math.min(200,e.network.inbound+20*(Math.random()-.5))),outbound:Math.max(30,Math.min(150,e.network.outbound+15*(Math.random()-.5)))})}))}finally{s(!1)}},y=e=>{switch(e){case"good":case"running":return"text-green-600";case"warning":return"text-yellow-600";case"error":case"stopped":return"text-red-600";default:return"text-gray-600"}},N=e=>{switch(e){case"good":case"running":return(0,w.jsx)(i.A,{className:"h-5 w-5 text-green-500"});case"warning":return(0,w.jsx)(o.A,{className:"h-5 w-5 text-yellow-500"});case"error":case"stopped":return(0,w.jsx)(l.A,{className:"h-5 w-5 text-red-500"});default:return(0,w.jsx)(i.A,{className:"h-5 w-5 text-gray-500"})}},k=e=>{let{title:t,value:s,unit:a,usage:n,status:r,icon:i,color:o="blue"}=e;return(0,w.jsxs)(j.A,{className:"p-6",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,w.jsxs)("div",{className:"flex items-center",children:[(0,w.jsx)(i,{className:"h-6 w-6 text-".concat(o,"-600 mr-3")}),(0,w.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t})]}),N(r)]}),(0,w.jsxs)("div",{className:"space-y-2",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:s}),(0,w.jsx)("span",{className:"text-sm text-gray-500",children:a})]}),void 0!==n&&(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,w.jsx)("div",{className:"h-2 rounded-full ".concat(n>80?"bg-red-500":n>60?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(n,"%")}})}),(0,w.jsxs)("div",{className:"text-sm text-gray-600",children:[n,"% used"]})]})]})]})},A=e=>{let{service:t}=e;return(0,w.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,w.jsxs)("div",{className:"flex items-center",children:[N(t.status),(0,w.jsxs)("div",{className:"ml-3",children:[(0,w.jsx)("h4",{className:"font-medium text-gray-900",children:t.name}),(0,w.jsx)("p",{className:"text-sm text-gray-500",children:t.description}),"N/A"!==t.port&&(0,w.jsxs)("p",{className:"text-xs text-gray-400",children:["Port: ",t.port]})]})]}),(0,w.jsxs)("div",{className:"text-right",children:[(0,w.jsx)("span",{className:"text-sm font-medium ".concat(y(t.status)),children:t.status.charAt(0).toUpperCase()+t.status.slice(1)}),(0,w.jsxs)("p",{className:"text-xs text-gray-500",children:["Uptime: ",t.uptime]})]})]})};return(0,w.jsxs)("div",{className:"space-y-6",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsxs)("div",{children:[(0,w.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,w.jsx)(c.A,{className:"h-8 w-8 mr-3 text-blue-600"}),e("systemMonitoring","System Monitoring")]}),(0,w.jsx)("p",{className:"text-gray-600 mt-1",children:e("systemMonitoringDescription","Monitor system performance and health in real-time")})]}),(0,w.jsxs)("div",{className:"text-right",children:[(0,w.jsx)("p",{className:"text-sm text-gray-500",children:e("lastUpdated","Last Updated")}),(0,w.jsx)("p",{className:"text-sm font-medium text-gray-900",children:(new Date).toLocaleTimeString()})]})]}),(0,w.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,w.jsx)(k,{title:e("cpuUsage","CPU Usage"),value:u.cpu.usage.toFixed(1),unit:"%",usage:u.cpu.usage,status:u.cpu.status,icon:d.A,color:"blue"}),(0,w.jsx)(k,{title:e("memoryUsage","Memory Usage"),value:u.memory.used.toFixed(1),unit:"GB / ".concat(u.memory.total,"GB"),usage:u.memory.usage,status:u.memory.status,icon:m.A,color:"green"}),(0,w.jsx)(k,{title:e("diskUsage","Disk Usage"),value:u.disk.used,unit:"GB / ".concat(u.disk.total,"GB"),usage:u.disk.usage,status:u.disk.status,icon:m.A,color:"purple"}),(0,w.jsx)(k,{title:e("activeUsers","Active Users"),value:u.activeUsers,unit:"users",status:"good",icon:x.A,color:"indigo"})]}),(0,w.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,w.jsxs)(j.A,{className:"p-6",children:[(0,w.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,w.jsx)(f,{className:"h-6 w-6 text-blue-600 mr-3"}),e("networkTraffic","Network Traffic")]}),(0,w.jsxs)("div",{className:"space-y-4",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("inbound","Inbound")}),(0,w.jsxs)("span",{className:"text-lg font-semibold text-green-600",children:[u.network.inbound," MB/s"]})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("outbound","Outbound")}),(0,w.jsxs)("span",{className:"text-lg font-semibold text-blue-600",children:[u.network.outbound," MB/s"]})]})]})]}),(0,w.jsxs)(j.A,{className:"p-6",children:[(0,w.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,w.jsx)(p.A,{className:"h-6 w-6 text-blue-600 mr-3"}),e("systemInfo","System Information")]}),(0,w.jsxs)("div",{className:"space-y-3",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("uptime","Uptime")}),(0,w.jsx)("span",{className:"text-sm font-medium text-gray-900",children:u.uptime})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("totalRequests","Total Requests")}),(0,w.jsx)("span",{className:"text-sm font-medium text-gray-900",children:u.totalRequests.toLocaleString()})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("errorRate","Error Rate")}),(0,w.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[(100*u.errorRate).toFixed(2),"%"]})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("lastBackup","Last Backup")}),(0,w.jsx)("span",{className:"text-sm font-medium text-gray-900",children:new Date(u.lastBackup).toLocaleDateString()})]})]})]})]}),(0,w.jsxs)(j.A,{className:"p-6",children:[(0,w.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("servicesStatus","Services Status")}),(0,w.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:h.map((e,t)=>(0,w.jsx)(A,{service:e},t))})]}),(0,w.jsxs)(j.A,{className:"p-6",children:[(0,w.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,w.jsx)(m.A,{className:"h-6 w-6 text-blue-600 mr-3"}),e("databaseStatus","Database Status")]}),(0,w.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,w.jsxs)("div",{className:"text-center",children:[(0,w.jsx)("p",{className:"text-2xl font-bold text-green-600",children:u.database.connections}),(0,w.jsx)("p",{className:"text-sm text-gray-600",children:e("activeConnections","Active Connections")})]}),(0,w.jsxs)("div",{className:"text-center",children:[(0,w.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:u.database.maxConnections}),(0,w.jsx)("p",{className:"text-sm text-gray-600",children:e("maxConnections","Max Connections")})]}),(0,w.jsxs)("div",{className:"text-center",children:[(0,w.jsxs)("div",{className:"flex items-center justify-center",children:[N(u.database.status),(0,w.jsx)("span",{className:"ml-2 text-lg font-semibold ".concat(y(u.database.status)),children:u.database.status.charAt(0).toUpperCase()+u.database.status.slice(1)})]}),(0,w.jsx)("p",{className:"text-sm text-gray-600",children:e("status","Status")})]})]})]}),u.browserInfo&&(0,w.jsxs)(j.A,{className:"p-6",children:[(0,w.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,w.jsx)(f,{className:"h-6 w-6 text-blue-600 mr-3"}),e("browserInformation","Browser Information")]}),(0,w.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,w.jsxs)("div",{className:"space-y-3",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("browser","Browser")}),(0,w.jsx)("span",{className:"text-sm font-medium text-gray-900",children:u.browserInfo.name})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("platform","Platform")}),(0,w.jsx)("span",{className:"text-sm font-medium text-gray-900",children:u.browserInfo.platform})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("language","Language")}),(0,w.jsx)("span",{className:"text-sm font-medium text-gray-900",children:u.browserInfo.language})]})]}),(0,w.jsxs)("div",{className:"space-y-3",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("cookiesEnabled","Cookies Enabled")}),(0,w.jsx)("span",{className:"text-sm font-medium ".concat(u.browserInfo.cookieEnabled?"text-green-600":"text-red-600"),children:u.browserInfo.cookieEnabled?e("yes","Yes"):e("no","No")})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("onlineStatus","Online Status")}),(0,w.jsx)("span",{className:"text-sm font-medium ".concat(u.browserInfo.onLine?"text-green-600":"text-red-600"),children:u.browserInfo.onLine?e("online","Online"):e("offline","Offline")})]}),u.network.type&&(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("connectionType","Connection Type")}),(0,w.jsx)("span",{className:"text-sm font-medium text-gray-900",children:u.network.type})]})]}),(0,w.jsxs)("div",{className:"space-y-3",children:[(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("screenResolution","Screen Resolution")}),(0,w.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[window.screen.width," x ",window.screen.height]})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("viewportSize","Viewport Size")}),(0,w.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[window.innerWidth," x ",window.innerHeight]})]}),(0,w.jsxs)("div",{className:"flex items-center justify-between",children:[(0,w.jsx)("span",{className:"text-sm text-gray-600",children:e("colorDepth","Color Depth")}),(0,w.jsxs)("span",{className:"text-sm font-medium text-gray-900",children:[window.screen.colorDepth," bit"]})]})]})]})]})]})}},9399:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const o=n.forwardRef(i)},9942:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(3986),n=s(5043);const r=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,o=(0,a.A)(e,r);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z"}))}const o=n.forwardRef(i)}}]);
//# sourceMappingURL=7580.b8bbc85c.chunk.js.map