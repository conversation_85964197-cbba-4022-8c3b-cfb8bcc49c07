{"version": 3, "file": "static/js/8530.4ec8e10c.chunk.js", "mappings": "mMAGA,MAy1BA,EAz1BoBA,KAClB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACfC,GAAeC,EAAAA,EAAAA,QAAO,OAErBC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CACvCC,MAAO,GACPC,YAAa,GACbC,SAAU,aACVC,OAAQ,GACRC,SAAU,CACRC,WAAW,EACXC,kBAAkB,EAClBC,UAAU,EACVC,WAAW,EACXC,MAAO,eACPC,YAAa,WAIVC,EAAWC,IAAgBb,EAAAA,EAAAA,UAAS,WACpCc,EAAeC,IAAoBf,EAAAA,EAAAA,UAAS,OAC5CgB,EAAsBC,IAA2BjB,EAAAA,EAAAA,WAAS,IAC1DkB,EAAkBC,IAAuBnB,EAAAA,EAAAA,UAAS,OAClDoB,EAAcC,IAAmBrB,EAAAA,EAAAA,UAAS,MAG3CsB,EAAa,CACjB,CACEC,KAAM,OACNC,MAAO/B,EAAE,YAAa,cACtBgC,KAAM,cACNtB,SAAU,QACVuB,MAAO,OACPC,aAAc,CACZC,YAAa,gBACbC,UAAU,EACVC,UAAW,IACXC,gBAAiB,UACjBC,YAAa,UACbC,UAAW,YAGf,CACEV,KAAM,WACNC,MAAO/B,EAAE,WAAY,aACrBgC,KAAM,oBACNtB,SAAU,QACVuB,MAAO,QACPC,aAAc,CACZC,YAAa,yBACbC,UAAU,EACVK,KAAM,EACNH,gBAAiB,UACjBC,YAAa,UACbC,UAAW,YAGf,CACEV,KAAM,SACNC,MAAO/B,EAAE,SAAU,UACnBgC,KAAM,iBACNtB,SAAU,QACVuB,MAAO,SACPC,aAAc,CACZC,YAAa,IACbC,UAAU,EACVM,IAAK,EACLC,IAAK,OACLL,gBAAiB,UACjBC,YAAa,UACbC,UAAW,YAGf,CACEV,KAAM,QACNC,MAAO/B,EAAE,QAAS,SAClBgC,KAAM,kBACNtB,SAAU,QACVuB,MAAO,SACPC,aAAc,CACZC,YAAa,oBACbC,UAAU,EACVE,gBAAiB,UACjBC,YAAa,UACbC,UAAW,YAGf,CACEV,KAAM,QACNC,MAAO/B,EAAE,QAAS,SAClBgC,KAAM,eACNtB,SAAU,QACVuB,MAAO,OACPC,aAAc,CACZC,YAAa,oBACbC,UAAU,EACVE,gBAAiB,UACjBC,YAAa,UACbC,UAAW,YAGf,CACEV,KAAM,OACNC,MAAO/B,EAAE,OAAQ,QACjBgC,KAAM,kBACNtB,SAAU,QACVuB,MAAO,SACPC,aAAc,CACZE,UAAU,EACVE,gBAAiB,UACjBC,YAAa,UACbC,UAAW,YAGf,CACEV,KAAM,aACNC,MAAO/B,EAAE,YAAa,qBACtBgC,KAAM,0BACNtB,SAAU,UACVwB,aAAc,CAAEQ,IAAK,EAAGC,IAAK,GAAIC,KAAM,EAAGC,YAAY,IAExD,CACEf,KAAM,WACNC,MAAO/B,EAAE,UAAW,YACpBgC,KAAM,cACNtB,SAAU,UACVwB,aAAc,CAAEY,eAAe,EAAMC,UAAW,CAAC,QAAS,OAAQ,aAEpE,CACEjB,KAAM,kBACNC,MAAO/B,EAAE,gBAAiB,mBAC1BgC,KAAM,2BACNtB,SAAU,UACVwB,aAAc,CAAEc,MAAO,WAAYC,UAAW,CAAC,UAAW,eAE5D,CACEnB,KAAM,kBACNC,MAAO/B,EAAE,iBAAkB,yBAC3BgC,KAAM,kBACNtB,SAAU,UACVwB,aAAc,CAAEgB,MAAO,SAAUC,kBAAkB,IAErD,CACErB,KAAM,cACNC,MAAO/B,EAAE,aAAc,eACvBgC,KAAM,mBACNtB,SAAU,UACVwB,aAAc,CAAEvB,OAAQ,CAAC,KAAM,KAAM,OAAQ,UAE/C,CACEmB,KAAM,YACNC,MAAO/B,EAAE,sBAAuB,wBAChCgC,KAAM,mBACNtB,SAAU,UACVwB,aAAc,CAAEE,UAAU,EAAMgB,cAAe,cAEjD,CACEtB,KAAM,mBACNC,MAAO/B,EAAE,kBAAmB,oBAC5BgC,KAAM,sBACNtB,SAAU,UACVwB,aAAc,CAAEmB,YAAa,yBAA0BjB,UAAU,IAEnE,CACEN,KAAM,kBACNC,MAAO/B,EAAE,iBAAkB,4BAC3BgC,KAAM,cACNtB,SAAU,UACVwB,aAAc,CAAEY,eAAe,EAAMQ,YAAY,IAEnD,CACExB,KAAM,kBACNC,MAAO/B,EAAE,iBAAkB,mBAC3BgC,KAAM,eACNtB,SAAU,UACVwB,aAAc,CAAEqB,UAAU,EAAM5C,OAAQ,CAAC,OAAQ,SAAU,eAE7D,CACEmB,KAAM,QACNC,MAAO/B,EAAE,QAAS,SAClBgC,KAAM,kBACNtB,SAAU,QACVwB,aAAc,CAAEC,YAAa,oBAAqBC,UAAU,IAE9D,CACEN,KAAM,QACNC,MAAO/B,EAAE,QAAS,SAClBgC,KAAM,eACNtB,SAAU,QACVwB,aAAc,CAAEC,YAAa,mBAAoBC,UAAU,IAE7D,CACEN,KAAM,OACNC,MAAO/B,EAAE,OAAQ,QACjBgC,KAAM,kBACNtB,SAAU,QACVwB,aAAc,CAAEE,UAAU,IAE5B,CACEN,KAAM,SACNC,MAAO/B,EAAE,WAAY,YACrBgC,KAAM,sBACNtB,SAAU,SACVwB,aAAc,CAAEsB,QAAS,CAAC,WAAY,WAAY,YAAapB,UAAU,IAE3E,CACEN,KAAM,QACNC,MAAO/B,EAAE,eAAgB,iBACzBgC,KAAM,oBACNtB,SAAU,SACVwB,aAAc,CAAEsB,QAAS,CAAC,WAAY,WAAY,YAAapB,UAAU,IAE3E,CACEN,KAAM,WACNC,MAAO/B,EAAE,aAAc,cACvBgC,KAAM,sBACNtB,SAAU,SACVwB,aAAc,CAAEsB,QAAS,CAAC,WAAY,WAAY,YAAapB,UAAU,IAE3E,CACEN,KAAM,SACNC,MAAO/B,EAAE,SAAU,UACnBgC,KAAM,cACNtB,SAAU,WACVwB,aAAc,CAAES,IAAK,EAAGP,UAAU,IAEpC,CACEN,KAAM,aACNC,MAAO/B,EAAE,YAAa,cACtBgC,KAAM,0BACNtB,SAAU,UACVwB,aAAc,CAAEQ,IAAK,EAAGC,IAAK,GAAIP,UAAU,IAE7C,CACEN,KAAM,YACNC,MAAO/B,EAAE,YAAa,aACtBgC,KAAM,mBACNtB,SAAU,WACVwB,aAAc,CAAEE,UAAU,IAE5B,CACEN,KAAM,UACNC,MAAO/B,EAAE,UAAW,kBACpBgC,KAAM,iBACNtB,SAAU,SACVwB,aAAc,CAAE1B,MAAO,gBAAiBC,YAAa,MA2DnDgD,EAAYC,IAChB,MAAMC,GAAQC,EAAAA,EAAAA,GAAA,CACZC,GAAG,SAADC,OAAWC,KAAKC,OAClBlC,KAAM4B,EAAU5B,KAChBC,MAAO2B,EAAU3B,OACd2B,EAAUxB,cAGf5B,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXK,GAAI,IACPtD,OAAQ,IAAIsD,EAAKtD,OAAQgD,OAIvBO,GAAcC,EAAAA,EAAAA,aAAY,CAACC,EAASC,KACxC/D,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXK,GAAI,IACPtD,OAAQsD,EAAKtD,OAAO2D,IAAIC,GACtBA,EAAMV,KAAOO,GAAOR,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQW,GAAUF,GAAYE,MAKlDlD,GAAiBA,EAAcwC,KAAOO,GACxC9C,EAAiB2C,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUK,GAASI,KAEzC,CAAChD,IAgDEmD,IA9CyBL,EAAAA,EAAAA,aAAY,CAACC,EAASK,KACnDP,EAAYE,EAAS,CAAErC,MAAO0C,KAC7B,CAACP,KAE8BC,EAAAA,EAAAA,aAAY,CAACC,EAASM,EAAUD,KAChEP,EAAYE,EAAS,CAAE,CAACM,GAAWD,KAClC,CAACP,KAGyBC,EAAAA,EAAAA,aAAY,CAACQ,EAAGjB,KAC3C9B,EAAgB8B,GAChBiB,EAAEC,aAAaC,cAAgB,QAC9B,KAEsBV,EAAAA,EAAAA,aAAaQ,IACpCA,EAAEG,iBACEnD,IACF8B,EAAS9B,EAAaG,MACtBF,EAAgB,QAEjB,CAACD,KAEyBwC,EAAAA,EAAAA,aAAaQ,IACxCA,EAAEG,iBACFH,EAAEC,aAAaG,WAAa,QAC3B,KAEeZ,EAAAA,EAAAA,aAAY,CAACa,EAAWC,KACxC,MAAMC,EAAY,IAAI7E,EAASM,SACxBwE,GAAcD,EAAUE,OAAOJ,EAAW,GACjDE,EAAUE,OAAOH,EAAS,EAAGE,GAE7B7E,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXK,GAAI,IACPtD,OAAQuE,MAET,CAAC7E,EAASM,SAUe4D,IAAW,IAADc,EAAAC,EAAAC,EACpC,MAAMC,EAAc,CAClBC,UAAW,kIACXC,UAAU,GAGZ,OAAQnB,EAAMzC,MACZ,IAAK,OACL,IAAK,QACL,IAAK,QACH,OAAO6D,EAAAA,EAAAA,KAAA,SAAA/B,EAAAA,EAAAA,GAAA,CAAO9B,KAAMyC,EAAMzC,KAAMK,YAAaoC,EAAMpC,aAAiBqD,IAEtE,IAAK,WACH,OAAOG,EAAAA,EAAAA,KAAA,YAAA/B,EAAAA,EAAAA,GAAA,CAAUnB,KAAM8B,EAAM9B,KAAMN,YAAaoC,EAAMpC,aAAiBqD,IAEzE,IAAK,SACH,OAAOG,EAAAA,EAAAA,KAAA,SAAA/B,EAAAA,EAAAA,GAAA,CAAO9B,KAAK,SAASY,IAAK6B,EAAM7B,IAAKC,IAAK4B,EAAM5B,IAAKR,YAAaoC,EAAMpC,aAAiBqD,IAElG,IAAK,OACH,OAAOG,EAAAA,EAAAA,KAAA,SAAA/B,EAAAA,EAAAA,GAAA,CAAO9B,KAAK,QAAW0D,IAEhC,IAAK,SACH,OACEI,EAAAA,EAAAA,MAAA,UAAAhC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAY4B,GAAW,IAAAK,SAAA,EACrBF,EAAAA,EAAAA,KAAA,UAAAE,SAAS7F,EAAE,eAAgB,yBACb,QAD6CqF,EAC1Dd,EAAMf,eAAO,IAAA6B,OAAA,EAAbA,EAAef,IAAI,CAACwB,EAAQC,KAC3BJ,EAAAA,EAAAA,KAAA,UAAoBlB,MAAOqB,EAAOD,SAAEC,GAAvBC,QAKrB,IAAK,QACH,OACEJ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWI,SACV,QADUP,EACvBf,EAAMf,eAAO,IAAA8B,OAAA,EAAbA,EAAehB,IAAI,CAACwB,EAAQC,KAC3BH,EAAAA,EAAAA,MAAA,SAAmBH,UAAU,8BAA6BI,SAAA,EACxDF,EAAAA,EAAAA,KAAA,SAAO7D,KAAK,QAAQkE,KAAMzB,EAAMV,GAAI6B,UAAQ,EAACD,UAAU,mBACvDE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BI,SAAEC,MAFvCC,MAQpB,IAAK,WACH,OACEJ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWI,SACV,QADUN,EACvBhB,EAAMf,eAAO,IAAA+B,OAAA,EAAbA,EAAejB,IAAI,CAACwB,EAAQC,KAC3BH,EAAAA,EAAAA,MAAA,SAAmBH,UAAU,8BAA6BI,SAAA,EACxDF,EAAAA,EAAAA,KAAA,SAAO7D,KAAK,WAAW4D,UAAQ,EAACD,UAAU,mBAC1CE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BI,SAAEC,MAFvCC,MAQpB,IAAK,SACH,OACEJ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBI,SAC5B,IAAII,MAAM1B,EAAM5B,MAAM2B,IAAI,CAAC4B,EAAGH,KAC7BJ,EAAAA,EAAAA,KAAA,KAAeF,UAAU,qCAAjBM,MAKhB,IAAK,aACH,OACEH,EAAAA,EAAAA,MAAA,OAAKH,UAAU,YAAWI,SAAA,EACxBF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCI,SAC/C,IAAII,MAAM,KAAK3B,IAAI,CAAC4B,EAAGH,KACtBJ,EAAAA,EAAAA,KAAA,OAAiBF,UAAU,cAAaI,UACtCF,EAAAA,EAAAA,KAAA,OAAKF,UAAS,0FAAA3B,OACZiC,GAAS,EAAI,eAAiBA,GAAS,EAAI,gBAAkB,cAC5DF,SACAE,KAJKA,OASdH,EAAAA,EAAAA,MAAA,OAAKH,UAAU,6CAA4CI,SAAA,EACzDF,EAAAA,EAAAA,KAAA,QAAAE,SAAO7F,EAAE,SAAU,cACnB2F,EAAAA,EAAAA,KAAA,QAAAE,SAAO7F,EAAE,YAAa,sBAK9B,IAAK,YACH,OACE2F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+HAA8HI,UAC3IF,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCI,SAAE7F,EAAE,gBAAiB,sBAI7E,IAAK,UACH,OACE4F,EAAAA,EAAAA,MAAA,OAAKH,UAAU,kCAAiCI,SAAA,EAC9CF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDI,SAAEtB,EAAM/D,QAC1E+D,EAAM9D,cACLkF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCI,SAAEtB,EAAM9D,iBAKpE,QACE,OAAOmF,EAAAA,EAAAA,MAAA,OAAKH,UAAU,gBAAeI,SAAA,CAAC,uBAAqBtB,EAAMzC,WAIvE,OACE8D,EAAAA,EAAAA,MAAA,OAAKH,UAAS,4CAAA3B,OAA8C7D,EAAQ,cAAgB,gBAAiB4F,SAAA,EAEnGD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oFAAmFI,SAAA,EAChGD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BI,UAC1CD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDI,SAC5D7F,EAAE,cAAe,mBAEpB2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CI,SACpD7F,EAAE,kBAAmB,4DAK5B4F,EAAAA,EAAAA,MAAA,OAAKH,UAAU,8BAA6BI,SAAA,EAC1CD,EAAAA,EAAAA,MAAA,UACEO,QAASA,KAAA,IAAAC,EAAA,OAA0B,QAA1BA,EAAMjG,EAAakG,eAAO,IAAAD,OAAA,EAApBA,EAAsBE,SACrCZ,SAAUnE,EACVkE,UAAU,4HAA2HI,SAAA,EAErIF,EAAAA,EAAAA,KAAA,KAAGF,UAAS,OAAA3B,OAASvC,EAAuB,qBAAuB,WAAU,WAC5EA,EAAuBvB,EAAE,aAAc,iBAAmBA,EAAE,aAAc,mBAE7E2F,EAAAA,EAAAA,KAAA,UAAQF,UAAU,kFAAiFI,SAChG7F,EAAE,UAAW,cAEhB2F,EAAAA,EAAAA,KAAA,UAAQF,UAAU,kFAAiFI,SAChG7F,EAAE,OAAQ,iBAMjB2F,EAAAA,EAAAA,KAAA,SACEY,IAAKpG,EACL2B,KAAK,OACL0E,OAAO,uBACPC,SAnOkBC,IACxB,MAAMC,EAAOD,EAAME,OAAOC,MAAM,GAC5BF,GAjDkBG,WACtBtF,GAAwB,GAExB,UAEQ,IAAIuF,QAAQC,GAAWC,WAAWD,EAAS,MAGjD,MAAME,EAAe,CACnBC,eAAgB,CACd,CAAErF,KAAM,OAAQC,MAAO,eAAgBK,UAAU,GACjD,CAAEN,KAAM,OAAQC,MAAO,gBAAiBK,UAAU,GAClD,CAAEN,KAAM,QAASC,MAAO,gBAAiBK,UAAU,GACnD,CAAEN,KAAM,QAASC,MAAO,eAAgBK,UAAU,GAClD,CAAEN,KAAM,WAAYC,MAAO,kBAAmBK,UAAU,GACxD,CAAEN,KAAM,SAAUC,MAAO,qBAAsByB,QAAS,CAAC,OAAQ,WAAY,SAAU,SAAUpB,UAAU,GAC3G,CAAEN,KAAM,WAAYC,MAAO,WAAYyB,QAAS,CAAC,OAAQ,YAAa,WAAY,YAAapB,UAAU,GACzG,CAAEN,KAAM,aAAcC,MAAO,aAAcK,UAAU,GACrD,CAAEN,KAAM,YAAaC,MAAO,oBAAqBK,UAAU,IAE7DgF,WAAY,IACZC,aAAc,sBACdC,SAAU,UACVC,MAAO,GAGT7F,EAAoBwF,GAGpB5G,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXK,GAAI,IACPzD,MAAO0G,EAAaG,aACpB5G,YAAY,8CAADqD,OAAgD0D,KAAKC,MAAgC,IAA1BP,EAAaE,YAAiB,gBACpGzG,OAAQuG,EAAaC,eAAe7C,IAAI,CAACC,EAAOwB,KAAK,IAAA2B,EAAA,OAAA9D,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACnDC,GAAG,SAADC,OAAWiC,IACVxB,GAC6C,QADxCmD,EACL7F,EAAW8F,KAAKC,GAAMA,EAAG9F,OAASyC,EAAMzC,aAAK,IAAA4F,OAAA,EAA7CA,EAA+CxF,kBAIxD,CAAE,MAAO2F,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACCrG,GAAwB,EAC1B,GAMEuG,IAiOItC,UAAU,cAKblE,IACCoE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iGAAgGI,UAC7GD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDACbG,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DI,SACrE7F,EAAE,eAAgB,gCAErB2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CI,SACxD7F,EAAE,mBAAoB,qEAQhCyB,IACCkE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6FAA4FI,UACzGD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oCAAmCI,SAAA,EAChDD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,8BAA6BI,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4DACbG,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDI,SACnE7F,EAAE,qBAAsB,2BAE3B2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAA4CI,SACtD7F,EAAE,iBAAiB,YAAD8D,OAAcrC,EAAiB0F,eAAea,OAAM,iBAAAlE,OAAgB0D,KAAKC,MAAoC,IAA9BhG,EAAiB2F,YAAiB,0BAI1IzB,EAAAA,EAAAA,KAAA,UACEQ,QAASA,IAAMzE,EAAoB,MACnC+D,UAAU,oFAAmFI,UAE7FF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBAMrBG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,gBAAeI,SAAA,EAE5BF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FI,UAC3GD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,MAAKI,SAAA,EAClBF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DI,SACrE7F,EAAE,aAAc,iBAIlB,CAAC,QAAS,SAAU,WAAY,UAAW,UAAUsE,IAAI5D,IACxDkF,EAAAA,EAAAA,MAAA,OAAoBH,UAAU,OAAMI,SAAA,EAClCF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oFAAmFI,SAC9F7F,EAAEU,EAAUA,MAEfiF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBI,SACpChE,EAAWoG,OAAO1D,GAASA,EAAM7D,WAAaA,GAAU4D,IAAIZ,IAC3DiC,EAAAA,EAAAA,KAAA,UAEEQ,QAASA,IAAM1C,EAASC,GACxB+B,UAAU,iIAAgII,UAE1ID,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCI,SAAA,EACrDF,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAA3B,OAAKJ,EAAU1B,KAAI,6CAC/B2D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCI,SAAEnC,EAAU3B,YANhE2B,EAAU5B,WAPbpB,UAwBhBkF,EAAAA,EAAAA,MAAA,OAAKH,UAAU,uBAAsBI,SAAA,EAEnCF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8EAA6EI,UAC1FD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,wCAAuCI,SAAA,EACpDD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEI,SAC/E7F,EAAE,YAAa,iBAElB2F,EAAAA,EAAAA,KAAA,SACE7D,KAAK,OACL2C,MAAOpE,EAASG,MAChBiG,SAAW9B,GAAMrE,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUK,GAAI,IAAEzD,MAAOmE,EAAEiC,OAAOnC,SACjEtC,YAAanC,EAAE,iBAAkB,uBACjCyF,UAAU,wIAIdG,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEI,SAC/E7F,EAAE,WAAY,eAEjB4F,EAAAA,EAAAA,MAAA,UACEnB,MAAOpE,EAASK,SAChB+F,SAAW9B,GAAMrE,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUK,GAAI,IAAEvD,SAAUiE,EAAEiC,OAAOnC,SACpEgB,UAAU,kIAAiII,SAAA,EAE3IF,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,aAAYoB,SAAE7F,EAAE,aAAc,iBAC5C2F,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,SAAQoB,SAAE7F,EAAE,SAAU,aACpC2F,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,WAAUoB,SAAE7F,EAAE,WAAY,eACxC2F,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,YAAWoB,SAAE7F,EAAE,YAAa,gBAC1C2F,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,UAASoB,SAAE7F,EAAE,UAAW,oBAI1C4F,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEI,SAC/E7F,EAAE,cAAe,kBAEpB2F,EAAAA,EAAAA,KAAA,SACE7D,KAAK,OACL2C,MAAOpE,EAASI,YAChBgG,SAAW9B,GAAMrE,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUK,GAAI,IAAExD,YAAakE,EAAEiC,OAAOnC,SACvEtC,YAAanC,EAAE,mBAAoB,wBACnCyF,UAAU,6IAOlBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BI,UACzCD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oBAAmBI,SAAA,EAEhCD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,mGAAkGI,SAAA,EAC/GF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDI,SAClExF,EAASG,OAASR,EAAE,eAAgB,mBAEtCK,EAASI,cACRkF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCI,SAAExF,EAASI,kBAK9DkF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWI,SACI,IAA3BxF,EAASM,OAAOqH,QACfpC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,2GAA0GI,SAAA,EACvHF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uEACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDI,SACnE7F,EAAE,cAAe,oBAEpB2F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCI,SACjD7F,EAAE,gBAAiB,sEAEtB4F,EAAAA,EAAAA,MAAA,UACEO,QAASA,KAAA,IAAA+B,EAAA,OAA0B,QAA1BA,EAAM/H,EAAakG,eAAO,IAAA6B,OAAA,EAApBA,EAAsB5B,SACrCb,UAAU,sFAAqFI,SAAA,EAE/FF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZzF,EAAE,iBAAkB,yBAIzBK,EAASM,OAAO2D,IAAI,CAACC,EAAOwB,KAAK,IAAAoC,EAAA,OAC/BxC,EAAAA,EAAAA,KAAA,OAEEF,UAAS,8EAAA3B,QACM,OAAbzC,QAAa,IAAbA,OAAa,EAAbA,EAAewC,MAAOU,EAAMV,GACxB,kBACA,yFAENsC,QAASA,IAAM7E,EAAiBiD,GAAOsB,UAEvCD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,wCAAuCI,SAAA,EACpDD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,SAAQI,SAAA,EACrBD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,mCAAkCI,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,KAAGF,WAAwD,QAA7C0C,EAAAtG,EAAW8F,KAAKC,GAAMA,EAAG9F,OAASyC,EAAMzC,aAAK,IAAAqG,OAAA,EAA7CA,EAA+CnG,OAAQ,qBACrE2D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CI,SACtDtB,EAAMxC,QAERwC,EAAMnC,WACLuD,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uBAAsBI,SAAC,UAK3CF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMI,SAClBrB,EAAmBD,SAIxBqB,EAAAA,EAAAA,MAAA,OAAKH,UAAU,mCAAkCI,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,UACEQ,QAAUxB,IACRA,EAAEyD,kBACF9G,EAAiBiD,IAEnBkB,UAAU,yEAAwEI,UAElFF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mBAEfE,EAAAA,EAAAA,KAAA,UACEQ,QAAUxB,IApWfP,MAqWOO,EAAEyD,kBArWThE,EAsWmBG,EAAMV,GArW5CvD,EAAY2D,IAAIL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXK,GAAI,IACPtD,OAAQsD,EAAKtD,OAAOsH,OAAO1D,GAASA,EAAMV,KAAOO,MAEnD9C,EAAiB,OAmWOmE,UAAU,uEAAsEI,UAEhFF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BA3CdlB,EAAMV,gBAwDxBxC,IACCsE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FI,UAC3GD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,MAAKI,SAAA,EAClBD,EAAAA,EAAAA,MAAA,OAAKH,UAAU,yCAAwCI,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDI,SAChE7F,EAAE,kBAAmB,uBAExB2F,EAAAA,EAAAA,KAAA,UACEQ,QAASA,IAAM7E,EAAiB,MAChCmE,UAAU,6DAA4DI,UAEtEF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAIjBG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,YAAWI,SAAA,EAExBD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEI,SAC/E7F,EAAE,aAAc,kBAEnB2F,EAAAA,EAAAA,KAAA,SACE7D,KAAK,OACL2C,MAAOpD,EAAcU,MACrB0E,SAAW9B,GAAMT,EAAY7C,EAAcwC,GAAI,CAAE9B,MAAO4C,EAAEiC,OAAOnC,QACjEgB,UAAU,wIAKdG,EAAAA,EAAAA,MAAA,OAAKH,UAAU,oCAAmCI,SAAA,EAChDF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,uDAAsDI,SACpE7F,EAAE,WAAY,eAEjB4F,EAAAA,EAAAA,MAAA,SAAOH,UAAU,mDAAkDI,SAAA,EACjEF,EAAAA,EAAAA,KAAA,SACE7D,KAAK,WACLuG,QAAShH,EAAce,WAAY,EACnCqE,SAAW9B,GAAMT,EAAY7C,EAAcwC,GAAI,CAAEzB,SAAUuC,EAAEiC,OAAOyB,UACpE5C,UAAU,kBAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,ucAKM,SAAvBpE,EAAcS,MAA0C,aAAvBT,EAAcS,QAC/C8D,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEI,SAC/E7F,EAAE,cAAe,kBAEpB2F,EAAAA,EAAAA,KAAA,SACE7D,KAAK,OACL2C,MAAOpD,EAAcc,aAAe,GACpCsE,SAAW9B,GAAMT,EAAY7C,EAAcwC,GAAI,CAAE1B,YAAawC,EAAEiC,OAAOnC,QACvEgB,UAAU,wIAKS,WAAvBpE,EAAcS,MAA4C,UAAvBT,EAAcS,MAA2C,aAAvBT,EAAcS,QACnF8D,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEI,SAC/E7F,EAAE,UAAW,cAEhB4F,EAAAA,EAAAA,MAAA,OAAKH,UAAU,YAAWI,SAAA,EACtBxE,EAAcmC,SAAW,IAAIc,IAAI,CAACwB,EAAQC,KAC1CH,EAAAA,EAAAA,MAAA,OAAiBH,UAAU,8BAA6BI,SAAA,EACtDF,EAAAA,EAAAA,KAAA,SACE7D,KAAK,OACL2C,MAAOqB,EACPW,SAAW9B,IACT,MAAM2D,EAAa,IAAKjH,EAAcmC,SAAW,IACjD8E,EAAWvC,GAASpB,EAAEiC,OAAOnC,MAC7BP,EAAY7C,EAAcwC,GAAI,CAAEL,QAAS8E,KAE3C7C,UAAU,qIAEZE,EAAAA,EAAAA,KAAA,UACEQ,QAASA,KACP,MAAMmC,EAAa,IAAKjH,EAAcmC,SAAW,IACjD8E,EAAWlD,OAAOW,EAAO,GACzB7B,EAAY7C,EAAcwC,GAAI,CAAEL,QAAS8E,KAE3C7C,UAAU,gFAA+EI,UAEzFF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAnBPM,KAuBZH,EAAAA,EAAAA,MAAA,UACEO,QAASA,KACP,MAAMmC,EAAa,IAAKjH,EAAcmC,SAAW,GAAK,cACtDU,EAAY7C,EAAcwC,GAAI,CAAEL,QAAS8E,KAE3C7C,UAAU,uNAAsNI,SAAA,EAEhOF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZzF,EAAE,YAAa,qC", "sources": ["pages/Forms/FormBuilder.jsx"], "sourcesContent": ["import React, { useState, useRef, useCallback } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst FormBuilder = () => {\n  const { t, isRTL } = useLanguage();\n  const fileInputRef = useRef(null);\n\n  const [formData, setFormData] = useState({\n    title: '',\n    description: '',\n    category: 'assessment',\n    fields: [],\n    settings: {\n      allowSave: true,\n      requireSignature: false,\n      autoSave: true,\n      multiPage: false,\n      theme: 'professional',\n      colorScheme: 'blue'\n    }\n  });\n\n  const [activeTab, setActiveTab] = useState('design');\n  const [selectedField, setSelectedField] = useState(null);\n  const [isProcessingDocument, setIsProcessingDocument] = useState(false);\n  const [documentAnalysis, setDocumentAnalysis] = useState(null);\n  const [draggedField, setDraggedField] = useState(null);\n\n  // Enhanced field types with medical-specific options and colors\n  const fieldTypes = [\n    {\n      type: 'text',\n      label: t('textInput', 'Text Input'),\n      icon: 'fas fa-font',\n      category: 'basic',\n      color: 'blue',\n      defaultProps: {\n        placeholder: 'Enter text...',\n        required: false,\n        maxLength: 255,\n        backgroundColor: '#f8fafc',\n        borderColor: '#3b82f6',\n        textColor: '#1e40af'\n      }\n    },\n    {\n      type: 'textarea',\n      label: t('textArea', 'Text Area'),\n      icon: 'fas fa-align-left',\n      category: 'basic',\n      color: 'green',\n      defaultProps: {\n        placeholder: 'Enter detailed text...',\n        required: false,\n        rows: 4,\n        backgroundColor: '#f0fdf4',\n        borderColor: '#10b981',\n        textColor: '#065f46'\n      }\n    },\n    {\n      type: 'number',\n      label: t('number', 'Number'),\n      icon: 'fas fa-hashtag',\n      category: 'basic',\n      color: 'purple',\n      defaultProps: {\n        placeholder: '0',\n        required: false,\n        min: 0,\n        max: 999999,\n        backgroundColor: '#faf5ff',\n        borderColor: '#8b5cf6',\n        textColor: '#6b21a8'\n      }\n    },\n    {\n      type: 'email',\n      label: t('email', 'Email'),\n      icon: 'fas fa-envelope',\n      category: 'basic',\n      color: 'indigo',\n      defaultProps: {\n        placeholder: '<EMAIL>',\n        required: false,\n        backgroundColor: '#f0f9ff',\n        borderColor: '#6366f1',\n        textColor: '#3730a3'\n      }\n    },\n    {\n      type: 'phone',\n      label: t('phone', 'Phone'),\n      icon: 'fas fa-phone',\n      category: 'basic',\n      color: 'teal',\n      defaultProps: {\n        placeholder: '+****************',\n        required: false,\n        backgroundColor: '#f0fdfa',\n        borderColor: '#14b8a6',\n        textColor: '#0f766e'\n      }\n    },\n    {\n      type: 'date',\n      label: t('date', 'Date'),\n      icon: 'fas fa-calendar',\n      category: 'basic',\n      color: 'orange',\n      defaultProps: {\n        required: false,\n        backgroundColor: '#fff7ed',\n        borderColor: '#f97316',\n        textColor: '#c2410c'\n      }\n    },\n    {\n      type: 'pain-scale',\n      label: t('painScale', 'Pain Scale (0-10)'),\n      icon: 'fas fa-thermometer-half',\n      category: 'medical',\n      defaultProps: { min: 0, max: 10, step: 1, showLabels: true }\n    },\n    {\n      type: 'body-map',\n      label: t('bodyMap', 'Body Map'),\n      icon: 'fas fa-male',\n      category: 'medical',\n      defaultProps: { allowMultiple: true, painTypes: ['sharp', 'dull', 'burning'] }\n    },\n    {\n      type: 'range-of-motion',\n      label: t('rangeOfMotion', 'Range of Motion'),\n      icon: 'fas fa-expand-arrows-alt',\n      category: 'medical',\n      defaultProps: { joint: 'shoulder', movements: ['flexion', 'extension'] }\n    },\n    {\n      type: 'muscle-strength',\n      label: t('muscleStrength', 'Muscle Strength (0-5)'),\n      icon: 'fas fa-dumbbell',\n      category: 'medical',\n      defaultProps: { scale: 'oxford', showDescriptions: true }\n    },\n    {\n      type: 'vital-signs',\n      label: t('vitalSigns', 'Vital Signs'),\n      icon: 'fas fa-heartbeat',\n      category: 'medical',\n      defaultProps: { fields: ['bp', 'hr', 'temp', 'resp'] }\n    },\n    {\n      type: 'signature',\n      label: t('electronicSignature', 'Electronic Signature'),\n      icon: 'fas fa-signature',\n      category: 'medical',\n      defaultProps: { required: true, signatureType: 'therapist' }\n    },\n    {\n      type: 'consent-checkbox',\n      label: t('consentCheckbox', 'Consent Checkbox'),\n      icon: 'fas fa-check-square',\n      category: 'medical',\n      defaultProps: { consentText: 'I consent to treatment', required: true }\n    },\n    {\n      type: 'diagnosis-codes',\n      label: t('diagnosisCodes', 'Diagnosis Codes (ICD-10)'),\n      icon: 'fas fa-code',\n      category: 'medical',\n      defaultProps: { allowMultiple: true, searchable: true }\n    },\n    {\n      type: 'medication-list',\n      label: t('medicationList', 'Medication List'),\n      icon: 'fas fa-pills',\n      category: 'medical',\n      defaultProps: { allowAdd: true, fields: ['name', 'dosage', 'frequency'] }\n    },\n    {\n      type: 'email',\n      label: t('email', 'Email'),\n      icon: 'fas fa-envelope',\n      category: 'basic',\n      defaultProps: { placeholder: '<EMAIL>', required: false }\n    },\n    {\n      type: 'phone',\n      label: t('phone', 'Phone'),\n      icon: 'fas fa-phone',\n      category: 'basic',\n      defaultProps: { placeholder: '+966 50 123 4567', required: false }\n    },\n    {\n      type: 'date',\n      label: t('date', 'Date'),\n      icon: 'fas fa-calendar',\n      category: 'basic',\n      defaultProps: { required: false }\n    },\n    {\n      type: 'select',\n      label: t('dropdown', 'Dropdown'),\n      icon: 'fas fa-chevron-down',\n      category: 'choice',\n      defaultProps: { options: ['Option 1', 'Option 2', 'Option 3'], required: false }\n    },\n    {\n      type: 'radio',\n      label: t('radioButtons', 'Radio Buttons'),\n      icon: 'fas fa-dot-circle',\n      category: 'choice',\n      defaultProps: { options: ['Option 1', 'Option 2', 'Option 3'], required: false }\n    },\n    {\n      type: 'checkbox',\n      label: t('checkboxes', 'Checkboxes'),\n      icon: 'fas fa-check-square',\n      category: 'choice',\n      defaultProps: { options: ['Option 1', 'Option 2', 'Option 3'], required: false }\n    },\n    {\n      type: 'rating',\n      label: t('rating', 'Rating'),\n      icon: 'fas fa-star',\n      category: 'advanced',\n      defaultProps: { max: 5, required: false }\n    },\n    {\n      type: 'pain-scale',\n      label: t('painScale', 'Pain Scale'),\n      icon: 'fas fa-thermometer-half',\n      category: 'medical',\n      defaultProps: { min: 0, max: 10, required: false }\n    },\n    {\n      type: 'signature',\n      label: t('signature', 'Signature'),\n      icon: 'fas fa-signature',\n      category: 'advanced',\n      defaultProps: { required: false }\n    },\n    {\n      type: 'section',\n      label: t('section', 'Section Header'),\n      icon: 'fas fa-heading',\n      category: 'layout',\n      defaultProps: { title: 'Section Title', description: '' }\n    }\n  ];\n\n  // AI Document Processing\n  const processDocument = async (file) => {\n    setIsProcessingDocument(true);\n\n    try {\n      // Simulate AI processing\n      await new Promise(resolve => setTimeout(resolve, 3000));\n\n      // Mock AI analysis result\n      const mockAnalysis = {\n        detectedFields: [\n          { type: 'text', label: 'Patient Name', required: true },\n          { type: 'date', label: 'Date of Birth', required: true },\n          { type: 'email', label: 'Email Address', required: false },\n          { type: 'phone', label: 'Phone Number', required: true },\n          { type: 'textarea', label: 'Medical History', required: false },\n          { type: 'select', label: 'Insurance Provider', options: ['Bupa', 'Tawuniya', 'Malath', 'Other'], required: true },\n          { type: 'checkbox', label: 'Symptoms', options: ['Pain', 'Stiffness', 'Weakness', 'Numbness'], required: false },\n          { type: 'pain-scale', label: 'Pain Level', required: false },\n          { type: 'signature', label: 'Patient Signature', required: true }\n        ],\n        confidence: 0.92,\n        documentType: 'Patient Intake Form',\n        language: 'English',\n        pages: 1\n      };\n\n      setDocumentAnalysis(mockAnalysis);\n\n      // Auto-populate form with detected fields\n      setFormData(prev => ({\n        ...prev,\n        title: mockAnalysis.documentType,\n        description: `Auto-generated from uploaded document with ${Math.round(mockAnalysis.confidence * 100)}% confidence`,\n        fields: mockAnalysis.detectedFields.map((field, index) => ({\n          id: `field_${index}`,\n          ...field,\n          ...fieldTypes.find(ft => ft.type === field.type)?.defaultProps\n        }))\n      }));\n\n    } catch (error) {\n      console.error('Document processing failed:', error);\n    } finally {\n      setIsProcessingDocument(false);\n    }\n  };\n\n  const handleFileUpload = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      processDocument(file);\n    }\n  };\n\n  const addField = (fieldType) => {\n    const newField = {\n      id: `field_${Date.now()}`,\n      type: fieldType.type,\n      label: fieldType.label,\n      ...fieldType.defaultProps\n    };\n\n    setFormData(prev => ({\n      ...prev,\n      fields: [...prev.fields, newField]\n    }));\n  };\n\n  const updateField = useCallback((fieldId, updates) => {\n    setFormData(prev => ({\n      ...prev,\n      fields: prev.fields.map(field =>\n        field.id === fieldId ? { ...field, ...updates } : field\n      )\n    }));\n\n    // Update selected field if it's the one being updated\n    if (selectedField && selectedField.id === fieldId) {\n      setSelectedField(prev => ({ ...prev, ...updates }));\n    }\n  }, [selectedField]);\n\n  const handleFieldLabelChange = useCallback((fieldId, value) => {\n    updateField(fieldId, { label: value });\n  }, [updateField]);\n\n  const handleFieldPropertyChange = useCallback((fieldId, property, value) => {\n    updateField(fieldId, { [property]: value });\n  }, [updateField]);\n\n  // Simple drag and drop handlers\n  const handleFieldDragStart = useCallback((e, fieldType) => {\n    setDraggedField(fieldType);\n    e.dataTransfer.effectAllowed = 'copy';\n  }, []);\n\n  const handleCanvasDrop = useCallback((e) => {\n    e.preventDefault();\n    if (draggedField) {\n      addField(draggedField.type);\n      setDraggedField(null);\n    }\n  }, [draggedField]);\n\n  const handleCanvasDragOver = useCallback((e) => {\n    e.preventDefault();\n    e.dataTransfer.dropEffect = 'copy';\n  }, []);\n\n  const moveField = useCallback((fromIndex, toIndex) => {\n    const newFields = [...formData.fields];\n    const [movedField] = newFields.splice(fromIndex, 1);\n    newFields.splice(toIndex, 0, movedField);\n\n    setFormData(prev => ({\n      ...prev,\n      fields: newFields\n    }));\n  }, [formData.fields]);\n\n  const deleteField = (fieldId) => {\n    setFormData(prev => ({\n      ...prev,\n      fields: prev.fields.filter(field => field.id !== fieldId)\n    }));\n    setSelectedField(null);\n  };\n\n  const renderFieldPreview = (field) => {\n    const commonProps = {\n      className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\",\n      disabled: true\n    };\n\n    switch (field.type) {\n      case 'text':\n      case 'email':\n      case 'phone':\n        return <input type={field.type} placeholder={field.placeholder} {...commonProps} />;\n\n      case 'textarea':\n        return <textarea rows={field.rows} placeholder={field.placeholder} {...commonProps} />;\n\n      case 'number':\n        return <input type=\"number\" min={field.min} max={field.max} placeholder={field.placeholder} {...commonProps} />;\n\n      case 'date':\n        return <input type=\"date\" {...commonProps} />;\n\n      case 'select':\n        return (\n          <select {...commonProps}>\n            <option>{t('selectOption', 'Select an option...')}</option>\n            {field.options?.map((option, index) => (\n              <option key={index} value={option}>{option}</option>\n            ))}\n          </select>\n        );\n\n      case 'radio':\n        return (\n          <div className=\"space-y-2\">\n            {field.options?.map((option, index) => (\n              <label key={index} className=\"flex items-center space-x-2\">\n                <input type=\"radio\" name={field.id} disabled className=\"text-blue-600\" />\n                <span className=\"text-gray-900 dark:text-white\">{option}</span>\n              </label>\n            ))}\n          </div>\n        );\n\n      case 'checkbox':\n        return (\n          <div className=\"space-y-2\">\n            {field.options?.map((option, index) => (\n              <label key={index} className=\"flex items-center space-x-2\">\n                <input type=\"checkbox\" disabled className=\"text-blue-600\" />\n                <span className=\"text-gray-900 dark:text-white\">{option}</span>\n              </label>\n            ))}\n          </div>\n        );\n\n      case 'rating':\n        return (\n          <div className=\"flex space-x-1\">\n            {[...Array(field.max)].map((_, index) => (\n              <i key={index} className=\"fas fa-star text-gray-300 text-xl\"></i>\n            ))}\n          </div>\n        );\n\n      case 'pain-scale':\n        return (\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between items-center\">\n              {[...Array(11)].map((_, index) => (\n                <div key={index} className=\"text-center\">\n                  <div className={`w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center text-sm ${\n                    index <= 3 ? 'bg-green-100' : index <= 6 ? 'bg-yellow-100' : 'bg-red-100'\n                  }`}>\n                    {index}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500\">\n              <span>{t('noPain', 'No Pain')}</span>\n              <span>{t('worstPain', 'Worst Pain')}</span>\n            </div>\n          </div>\n        );\n\n      case 'signature':\n        return (\n          <div className=\"border border-gray-300 dark:border-gray-600 rounded-lg p-4 h-32 bg-gray-50 dark:bg-gray-700 flex items-center justify-center\">\n            <span className=\"text-gray-500 dark:text-gray-400\">{t('signatureArea', 'Signature Area')}</span>\n          </div>\n        );\n\n      case 'section':\n        return (\n          <div className=\"border-l-4 border-blue-500 pl-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{field.title}</h3>\n            {field.description && (\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">{field.description}</p>\n            )}\n          </div>\n        );\n\n      default:\n        return <div className=\"text-gray-500\">Unknown field type: {field.type}</div>;\n    }\n  };\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                {t('formBuilder', 'Form Builder')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('formBuilderDesc', 'Create custom forms with drag-and-drop interface')}\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => fileInputRef.current?.click()}\n              disabled={isProcessingDocument}\n              className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center\"\n            >\n              <i className={`fas ${isProcessingDocument ? 'fa-spinner fa-spin' : 'fa-magic'} mr-2`}></i>\n              {isProcessingDocument ? t('processing', 'Processing...') : t('aiGenerate', 'AI Generate')}\n            </button>\n            <button className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\">\n              {t('preview', 'Preview')}\n            </button>\n            <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n              {t('save', 'Save')}\n            </button>\n          </div>\n        </div>\n\n        {/* Hidden file input for AI processing */}\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept=\".pdf,.doc,.docx,.txt\"\n          onChange={handleFileUpload}\n          className=\"hidden\"\n        />\n      </div>\n\n      {/* AI Processing Status */}\n      {isProcessingDocument && (\n        <div className=\"bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-800 px-6 py-4\">\n          <div className=\"flex items-center space-x-3\">\n            <i className=\"fas fa-magic text-purple-600 dark:text-purple-400\"></i>\n            <div>\n              <h3 className=\"text-sm font-medium text-purple-900 dark:text-purple-100\">\n                {t('aiProcessing', 'AI Processing Document...')}\n              </h3>\n              <p className=\"text-xs text-purple-700 dark:text-purple-300\">\n                {t('aiProcessingDesc', 'Analyzing document structure and extracting form fields')}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Document Analysis Results */}\n      {documentAnalysis && (\n        <div className=\"bg-green-50 dark:bg-green-900/20 border-b border-green-200 dark:border-green-800 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400\"></i>\n              <div>\n                <h3 className=\"text-sm font-medium text-green-900 dark:text-green-100\">\n                  {t('aiAnalysisComplete', 'AI Analysis Complete')}\n                </h3>\n                <p className=\"text-xs text-green-700 dark:text-green-300\">\n                  {t('detectedFields', `Detected ${documentAnalysis.detectedFields.length} fields with ${Math.round(documentAnalysis.confidence * 100)}% confidence`)}\n                </p>\n              </div>\n            </div>\n            <button\n              onClick={() => setDocumentAnalysis(null)}\n              className=\"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200\"\n            >\n              <i className=\"fas fa-times\"></i>\n            </button>\n          </div>\n        </div>\n      )}\n\n      <div className=\"flex h-screen\">\n        {/* Left Sidebar - Field Types */}\n        <div className=\"w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-600 overflow-y-auto\">\n          <div className=\"p-4\">\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('fieldTypes', 'Field Types')}\n            </h2>\n\n            {/* Field Categories */}\n            {['basic', 'choice', 'advanced', 'medical', 'layout'].map(category => (\n              <div key={category} className=\"mb-6\">\n                <h3 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 uppercase tracking-wide\">\n                  {t(category, category)}\n                </h3>\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {fieldTypes.filter(field => field.category === category).map(fieldType => (\n                    <button\n                      key={fieldType.type}\n                      onClick={() => addField(fieldType)}\n                      className=\"p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left\"\n                    >\n                      <div className=\"flex flex-col items-center text-center\">\n                        <i className={`${fieldType.icon} text-gray-600 dark:text-gray-400 mb-2`}></i>\n                        <span className=\"text-xs text-gray-900 dark:text-white\">{fieldType.label}</span>\n                      </div>\n                    </button>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Main Content Area */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* Form Settings */}\n          <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 p-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('formTitle', 'Form Title')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.title}\n                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}\n                  placeholder={t('enterFormTitle', 'Enter form title...')}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('category', 'Category')}\n                </label>\n                <select\n                  value={formData.category}\n                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"assessment\">{t('assessment', 'Assessment')}</option>\n                  <option value=\"intake\">{t('intake', 'Intake')}</option>\n                  <option value=\"progress\">{t('progress', 'Progress')}</option>\n                  <option value=\"discharge\">{t('discharge', 'Discharge')}</option>\n                  <option value=\"consent\">{t('consent', 'Consent')}</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('description', 'Description')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.description}\n                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\n                  placeholder={t('enterDescription', 'Enter description...')}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Form Canvas */}\n          <div className=\"flex-1 p-6 overflow-y-auto\">\n            <div className=\"max-w-4xl mx-auto\">\n              {/* Form Header */}\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6\">\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                  {formData.title || t('untitledForm', 'Untitled Form')}\n                </h1>\n                {formData.description && (\n                  <p className=\"text-gray-600 dark:text-gray-400\">{formData.description}</p>\n                )}\n              </div>\n\n              {/* Form Fields */}\n              <div className=\"space-y-4\">\n                {formData.fields.length === 0 ? (\n                  <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-12 text-center\">\n                    <i className=\"fas fa-plus-circle text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                      {t('noFieldsYet', 'No fields yet')}\n                    </h3>\n                    <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n                      {t('addFieldsDesc', 'Add fields from the sidebar or upload a document to get started')}\n                    </p>\n                    <button\n                      onClick={() => fileInputRef.current?.click()}\n                      className=\"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n                    >\n                      <i className=\"fas fa-magic mr-2\"></i>\n                      {t('uploadDocument', 'Upload Document')}\n                    </button>\n                  </div>\n                ) : (\n                  formData.fields.map((field, index) => (\n                    <div\n                      key={field.id}\n                      className={`bg-white dark:bg-gray-800 rounded-lg shadow border-2 transition-colors p-6 ${\n                        selectedField?.id === field.id\n                          ? 'border-blue-500'\n                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\n                      }`}\n                      onClick={() => setSelectedField(field)}\n                    >\n                      <div className=\"flex items-start justify-between mb-4\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            <i className={fieldTypes.find(ft => ft.type === field.type)?.icon || 'fas fa-question'}></i>\n                            <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                              {field.label}\n                            </h3>\n                            {field.required && (\n                              <span className=\"text-red-500 text-sm\">*</span>\n                            )}\n                          </div>\n\n                          {/* Field Preview */}\n                          <div className=\"mt-3\">\n                            {renderFieldPreview(field)}\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center space-x-2 ml-4\">\n                          <button\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              setSelectedField(field);\n                            }}\n                            className=\"p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded\"\n                          >\n                            <i className=\"fas fa-edit\"></i>\n                          </button>\n                          <button\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              deleteField(field.id);\n                            }}\n                            className=\"p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded\"\n                          >\n                            <i className=\"fas fa-trash\"></i>\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Right Sidebar - Field Properties */}\n        {selectedField && (\n          <div className=\"w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-600 overflow-y-auto\">\n            <div className=\"p-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {t('fieldProperties', 'Field Properties')}\n                </h2>\n                <button\n                  onClick={() => setSelectedField(null)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                {/* Field Label */}\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('fieldLabel', 'Field Label')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={selectedField.label}\n                    onChange={(e) => updateField(selectedField.id, { label: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                </div>\n\n                {/* Required Field */}\n                <div className=\"flex items-center justify-between\">\n                  <label className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    {t('required', 'Required')}\n                  </label>\n                  <label className=\"relative inline-flex items-center cursor-pointer\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedField.required || false}\n                      onChange={(e) => updateField(selectedField.id, { required: e.target.checked })}\n                      className=\"sr-only peer\"\n                    />\n                    <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                  </label>\n                </div>\n\n                {/* Field-specific properties */}\n                {(selectedField.type === 'text' || selectedField.type === 'textarea') && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('placeholder', 'Placeholder')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={selectedField.placeholder || ''}\n                      onChange={(e) => updateField(selectedField.id, { placeholder: e.target.value })}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    />\n                  </div>\n                )}\n\n                {(selectedField.type === 'select' || selectedField.type === 'radio' || selectedField.type === 'checkbox') && (\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('options', 'Options')}\n                    </label>\n                    <div className=\"space-y-2\">\n                      {(selectedField.options || []).map((option, index) => (\n                        <div key={index} className=\"flex items-center space-x-2\">\n                          <input\n                            type=\"text\"\n                            value={option}\n                            onChange={(e) => {\n                              const newOptions = [...(selectedField.options || [])];\n                              newOptions[index] = e.target.value;\n                              updateField(selectedField.id, { options: newOptions });\n                            }}\n                            className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                          />\n                          <button\n                            onClick={() => {\n                              const newOptions = [...(selectedField.options || [])];\n                              newOptions.splice(index, 1);\n                              updateField(selectedField.id, { options: newOptions });\n                            }}\n                            className=\"p-2 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200\"\n                          >\n                            <i className=\"fas fa-trash text-sm\"></i>\n                          </button>\n                        </div>\n                      ))}\n                      <button\n                        onClick={() => {\n                          const newOptions = [...(selectedField.options || []), 'New Option'];\n                          updateField(selectedField.id, { options: newOptions });\n                        }}\n                        className=\"w-full px-3 py-2 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500\"\n                      >\n                        <i className=\"fas fa-plus mr-2\"></i>\n                        {t('addOption', 'Add Option')}\n                      </button>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FormBuilder;\n"], "names": ["FormBuilder", "t", "isRTL", "useLanguage", "fileInputRef", "useRef", "formData", "setFormData", "useState", "title", "description", "category", "fields", "settings", "allowSave", "requireSignature", "autoSave", "multiPage", "theme", "colorScheme", "activeTab", "setActiveTab", "<PERSON><PERSON><PERSON>", "setSelectedField", "isProcessingDocument", "setIsProcessingDocument", "documentAnalysis", "setDocumentAnalysis", "<PERSON><PERSON><PERSON>", "setDraggedField", "fieldTypes", "type", "label", "icon", "color", "defaultProps", "placeholder", "required", "max<PERSON><PERSON><PERSON>", "backgroundColor", "borderColor", "textColor", "rows", "min", "max", "step", "showLabels", "allowMultiple", "painTypes", "joint", "movements", "scale", "showDescriptions", "signatureType", "consentText", "searchable", "allowAdd", "options", "addField", "fieldType", "newField", "_objectSpread", "id", "concat", "Date", "now", "prev", "updateField", "useCallback", "fieldId", "updates", "map", "field", "renderFieldPreview", "value", "property", "e", "dataTransfer", "effectAllowed", "preventDefault", "dropEffect", "fromIndex", "toIndex", "new<PERSON>ields", "movedField", "splice", "_field$options", "_field$options2", "_field$options3", "commonProps", "className", "disabled", "_jsx", "_jsxs", "children", "option", "index", "name", "Array", "_", "onClick", "_fileInputRef$current", "current", "click", "ref", "accept", "onChange", "event", "file", "target", "files", "async", "Promise", "resolve", "setTimeout", "mockAnalysis", "<PERSON><PERSON>ields", "confidence", "documentType", "language", "pages", "Math", "round", "_fieldTypes$find", "find", "ft", "error", "console", "processDocument", "length", "filter", "_fileInputRef$current2", "_fieldTypes$find2", "stopPropagation", "checked", "newOptions"], "sourceRoot": ""}