"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[276],{276:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});a(2555);var s=a(5043),r=a(7921),i=(a(3768),a(579));const d=()=>{const{t:e,isRTL:t}=(0,r.o)(),[a,d]=(0,s.useState)("providers"),[l,n]=(0,s.useState)(""),[c,x]=(0,s.useState)(!1),[m,o]=(0,s.useState)(null),[p,g]=(0,s.useState)(!1),[h,u]=(0,s.useState)([{id:1,name:"Bupa Arabia",nameAr:"\u0628\u0648\u0628\u0627 \u0627\u0644\u0639\u0631\u0628\u064a\u0629",type:"Private",status:"Active",contractStart:"2024-01-01",contractEnd:"2024-12-31",coverageLimit:5e4,deductible:500,copayPercentage:20,contactPerson:"Ahmed Al-Rashid",phone:"+966-11-123-4567",email:"<EMAIL>",address:"Riyadh, Saudi Arabia",totalPatients:145,totalClaims:89,approvedClaims:78,pendingClaims:8,rejectedClaims:3,totalReimbursed:125e3},{id:2,name:"Tawuniya",nameAr:"\u0627\u0644\u062a\u0639\u0627\u0648\u0646\u064a\u0629",type:"Cooperative",status:"Active",contractStart:"2024-01-01",contractEnd:"2024-12-31",coverageLimit:3e4,deductible:300,copayPercentage:15,contactPerson:"Fatima Al-Zahra",phone:"+966-11-234-5678",email:"<EMAIL>",address:"Jeddah, Saudi Arabia",totalPatients:98,totalClaims:67,approvedClaims:58,pendingClaims:6,rejectedClaims:3,totalReimbursed:89e3},{id:3,name:"CCHI Government",nameAr:"\u0645\u062c\u0644\u0633 \u0627\u0644\u0636\u0645\u0627\u0646 \u0627\u0644\u0635\u062d\u064a \u0627\u0644\u062d\u0643\u0648\u0645\u064a",type:"Government",status:"Active",contractStart:"2024-01-01",contractEnd:"2024-12-31",coverageLimit:25e3,deductible:200,copayPercentage:10,contactPerson:"Mohammed Al-Otaibi",phone:"+966-11-345-6789",email:"<EMAIL>",address:"Riyadh, Saudi Arabia",totalPatients:67,totalClaims:45,approvedClaims:38,pendingClaims:5,rejectedClaims:2,totalReimbursed:56e3}]),[y,b]=(0,s.useState)([{id:"CLM-2024-001",patientName:"Ahmed Al-Rashid",patientId:"P001",provider:"Bupa Arabia",treatmentType:"Physical Therapy",claimAmount:1500,approvedAmount:1200,status:"Approved",submissionDate:"2024-02-15",approvalDate:"2024-02-18",diagnosis:"Lower back pain",treatmentDates:"2024-02-10 to 2024-02-14",therapist:"Dr. Sarah Ahmed"},{id:"CLM-2024-002",patientName:"Fatima Al-Zahra",patientId:"P002",provider:"Tawuniya",treatmentType:"Occupational Therapy",claimAmount:2e3,approvedAmount:0,status:"Pending",submissionDate:"2024-02-14",approvalDate:null,diagnosis:"Autism spectrum disorder",treatmentDates:"2024-02-08 to 2024-02-12",therapist:"Dr. Mona Hassan"},{id:"CLM-2024-003",patientName:"Mohammed Al-Otaibi",patientId:"P003",provider:"CCHI Government",treatmentType:"Speech Therapy",claimAmount:1200,approvedAmount:1e3,status:"Approved",submissionDate:"2024-02-13",approvalDate:"2024-02-16",diagnosis:"Speech delay",treatmentDates:"2024-02-05 to 2024-02-09",therapist:"Dr. Layla Ibrahim"},{id:"CLM-2024-004",patientName:"Nora Al-Mansouri",patientId:"P004",provider:"Bupa Arabia",treatmentType:"Physical Therapy",claimAmount:1800,approvedAmount:0,status:"Rejected",submissionDate:"2024-02-12",approvalDate:"2024-02-15",diagnosis:"Chronic pain",treatmentDates:"2024-02-01 to 2024-02-05",therapist:"Dr. Omar Khalil",rejectionReason:"Pre-authorization required"}]),v=[{id:"providers",label:e("insuranceProviders","Insurance Providers"),icon:"fas fa-building"},{id:"claims",label:e("claims","Claims"),icon:"fas fa-file-invoice"},{id:"verification",label:e("verification","Verification"),icon:"fas fa-check-circle"},{id:"reports",label:e("reports","Reports"),icon:"fas fa-chart-bar"}],j=e=>new Intl.NumberFormat("ar-SA",{style:"currency",currency:"SAR"}).format(e),N=e=>{switch(e.toLowerCase()){case"approved":case"active":return"text-green-600 bg-green-100 dark:bg-green-900/30";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30";case"rejected":return"text-red-600 bg-red-100 dark:bg-red-900/30";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30"}};return(0,i.jsxs)("div",{className:"space-y-6 ".concat(t?"font-arabic":"font-english"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("insuranceManagement","Insurance Management")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("insuranceDescription","Manage insurance providers, claims, and coverage verification")})]}),(0,i.jsx)("div",{className:"flex items-center space-x-4",children:(0,i.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-check-circle mr-2"}),e("verifyEligibility","Verify Eligibility")]})})]}),(0,i.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,i.jsx)("nav",{className:"-mb-px flex space-x-8",children:v.map(e=>(0,i.jsxs)("button",{onClick:()=>d(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(a===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,i.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})}),"providers"===a&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-building text-blue-600 dark:text-blue-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalProviders","Total Providers")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.length})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-users text-green-600 dark:text-green-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalCoveredPatients","Total Covered Patients")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.reduce((e,t)=>e+t.totalPatients,0)})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-dollar-sign text-purple-600 dark:text-purple-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalReimbursed","Total Reimbursed")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:j(h.reduce((e,t)=>e+t.totalReimbursed,0))})]})]})})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("insuranceProviders","Insurance Providers")}),(0,i.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),e("addProvider","Add Provider")]})]}),(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full",children:[(0,i.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("provider","Provider")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("type","Type")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patients","Patients")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("claims","Claims")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("reimbursed","Reimbursed")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,i.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:h.map(t=>(0,i.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.name}),(0,i.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.nameAr})]})}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300",children:t.type})}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.totalPatients}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-900 dark:text-white",children:[t.totalClaims," ",e("total","total")]}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[t.approvedClaims," ",e("approved","approved"),", ",t.pendingClaims," ",e("pending","pending")]})]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white",children:j(t.totalReimbursed)}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(N(t.status)),children:t.status})}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,i.jsx)("button",{onClick:()=>o(t),className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3",children:e("view","View")}),(0,i.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",children:e("edit","Edit")})]})]},t.id))})]})})]})]}),"claims"===a&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-file-invoice text-blue-600 dark:text-blue-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalClaims","Total Claims")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.length})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("approvedClaims","Approved")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.filter(e=>"Approved"===e.status).length})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("pendingClaims","Pending")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.filter(e=>"Pending"===e.status).length})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-times-circle text-red-600 dark:text-red-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("rejectedClaims","Rejected")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:y.filter(e=>"Rejected"===e.status).length})]})]})})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("insuranceClaims","Insurance Claims")}),(0,i.jsxs)("button",{onClick:()=>g(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),e("submitClaim","Submit Claim")]})]}),(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full",children:[(0,i.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("claimId","Claim ID")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("provider","Provider")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("treatment","Treatment")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("amount","Amount")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,i.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:y.map(t=>(0,i.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.id}),(0,i.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.submissionDate})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.patientName}),(0,i.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",t.patientId]})]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:t.provider}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:t.treatmentType}),(0,i.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.diagnosis})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:j(t.claimAmount)}),t.approvedAmount>0&&(0,i.jsxs)("div",{className:"text-sm text-green-600 dark:text-green-400",children:[j(t.approvedAmount)," ",e("approved","approved")]})]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(N(t.status)),children:t.status})}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,i.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3",children:e("view","View")}),"Pending"===t.status&&(0,i.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",children:e("track","Track")})]})]},t.id))})]})})]})]}),"verification"===a&&(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("eligibilityVerification","Eligibility Verification")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("verificationDescription","Real-time insurance eligibility verification system")})]}),"reports"===a&&(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("insuranceReports","Insurance Reports")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("reportsDescription","Comprehensive insurance analytics and reporting")})]})]})}}}]);
//# sourceMappingURL=276.a82cc9d4.chunk.js.map