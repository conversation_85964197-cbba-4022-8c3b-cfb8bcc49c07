{"version": 3, "file": "static/js/6053.710b71d0.chunk.js", "mappings": "iMAIA,MAgBA,EAhBuBA,KAAO,IAADC,EAAAC,EAC3B,MAAM,UAAEC,EAAS,OAAEC,IAAWC,EAAAA,EAAAA,KACxBC,GAAWC,EAAAA,EAAAA,MAEjB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,UACvDF,EAAAA,EAAAA,KAACG,EAAAA,QAAc,CACbR,UAAWA,EACXC,OAAQA,EACRQ,YAA2B,QAAhBX,EAAEK,EAASO,aAAK,IAAAZ,OAAA,EAAdA,EAAgBa,QAC7BC,mBAAkC,QAAhBb,EAAEI,EAASO,aAAK,IAAAX,OAAA,EAAdA,EAAgBa,uB", "sources": ["pages/PlanOfCarePage.jsx"], "sourcesContent": ["import React from 'react';\nimport { useParams, useLocation } from 'react-router-dom';\nimport PlanOfCareForm from '../components/PlanOfCare/PlanOfCareForm';\n\nconst PlanOfCarePage = () => {\n  const { patientId, planId } = useParams();\n  const location = useLocation();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <PlanOfCareForm\n        patientId={patientId}\n        planId={planId}\n        patientData={location.state?.patient}\n        fromPatientProfile={location.state?.fromPatientProfile}\n      />\n    </div>\n  );\n};\n\nexport default PlanOfCarePage;\n"], "names": ["PlanOfCarePage", "_location$state", "_location$state2", "patientId", "planId", "useParams", "location", "useLocation", "_jsx", "className", "children", "PlanOfCareForm", "patientData", "state", "patient", "fromPatientProfile"], "sourceRoot": ""}