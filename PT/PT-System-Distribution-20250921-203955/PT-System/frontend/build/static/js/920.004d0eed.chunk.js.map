{"version": 3, "file": "static/js/920.004d0eed.chunk.js", "mappings": "2MAIA,MA0aA,EA1awBA,IAAyC,IAAxC,OAAEC,EAAM,QAAEC,EAAO,cAAEC,GAAeH,EACzD,MAAM,EAAEI,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CACvCG,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,SAAU,GACVC,gBAAiB,GACjBC,KAAM,GACNC,WAAY,GACZC,MAAO,GACPC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,YAAa,KAGTC,EAAQ,CACZC,MAAO,CAAEC,KAAMtB,EAAE,QAAS,iEAAgBuB,MAAO,QACjDC,OAAQ,CAAEF,KAAMtB,EAAE,SAAU,4BAASuB,MAAO,QAC5CE,UAAW,CAAEH,KAAMtB,EAAE,YAAa,wHAA0BuB,MAAO,SACnEG,MAAO,CAAEJ,KAAMtB,EAAE,QAAS,2DAAeuB,MAAO,QAChDI,aAAc,CAAEL,KAAMtB,EAAE,cAAe,iEAAgBuB,MAAO,UAC9DK,mBAAoB,CAAEN,KAAMtB,EAAE,oBAAqB,oFAAoBuB,MAAO,UAC9EM,aAAc,CAAEP,KAAMtB,EAAE,eAAgB,uEAAiBuB,MAAO,UAChEO,QAAS,CAAER,KAAMtB,EAAE,UAAW,4BAASuB,MAAO,QAkB1CQ,EAAqBC,IACzB,MAAM,KAAEV,EAAI,MAAEW,GAAUD,EAAEE,OAC1B3B,EAAY4B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACb,GAAOW,MA6FNI,EAAcA,KAClB9B,EAAY,CACVC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPC,SAAU,GACVC,gBAAiB,GACjBC,KAAM,GACNC,WAAY,GACZC,MAAO,GACPC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,YAAa,KAEfrB,KAGF,OAAKD,GAGHyC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6EAA4EC,UACzFC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExC,EAAE,aAAc,mGAEnBsC,EAAAA,EAAAA,KAAA,UACEI,QAASL,EACTE,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAKnBE,EAAAA,EAAAA,MAAA,QAAME,SAtFSC,UAGnB,GAFAZ,EAAEa,iBAxCGvC,EAASE,UAAUsC,OAInBxC,EAASG,SAASqC,OAIlBxC,EAASI,MAAMoC,OAIfxC,EAASI,MAAMqC,SAAS,KAIxBzC,EAASK,SAIVL,EAASK,SAASqC,OAAS,GAC7BC,EAAAA,GAAMC,MAAMlD,EAAE,mBAAoB,wMAC3B,GAELM,EAASK,WAAaL,EAASM,iBACjCqC,EAAAA,GAAMC,MAAMlD,EAAE,mBAAoB,gIAC3B,GAEJM,EAASO,KAITP,EAASQ,aACZmC,EAAAA,GAAMC,MAAMlD,EAAE,qBAAsB,kEAC7B,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,eAAgB,kEACvB,IAbPiD,EAAAA,GAAMC,MAAMlD,EAAE,mBAAoB,uGAC3B,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,eAAgB,kJACvB,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,gBAAiB,qIACxB,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,mBAAoB,iGAC3B,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,oBAAqB,iGAC5B,GA4CT,IACEI,GAAW,GAEX,MAAM+C,QAAiBC,MAAM,gBAAiB,CAC5CC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,WAElDC,KAAMC,KAAKC,UAAUtD,KAGvB,GAAI6C,EAASU,GAAI,CACf,MAAMC,QAAeX,EAASY,OAC9Bd,EAAAA,GAAMe,QAAQhE,EAAE,0BAA2B,gIAC3CD,EAAc+D,EAAOG,MACrB5B,GACF,KAAO,CAEL,MAAM6B,EAAW,CACfC,IAAKC,KAAKC,MAAMC,WAChB9D,UAAWF,EAASE,UACpBC,SAAUH,EAASG,SACnBC,MAAOJ,EAASI,MAChBG,KAAMP,EAASO,KACfC,WAAYR,EAASQ,WACrByD,UAAU,EACVC,UAAW,KACXC,UAAW,IAAIL,MAEjBnB,EAAAA,GAAMe,QAAQhE,EAAE,0BAA2B,gMAC3CD,EAAcmE,GACd7B,GACF,CACF,CAAE,MAAOa,GACPwB,QAAQxB,MAAM,uBAAwBA,GACtCD,EAAAA,GAAMC,MAAMlD,EAAE,oBAAqB,mHACrC,CAAC,QACCI,GAAW,EACb,GAwCkCmC,UAAU,MAAKC,SAAA,EAC3CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnExC,EAAE,mBAAoB,gHAI3ByC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,YAAa,iEAAe,SAEjCsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACLrD,KAAK,YACLW,MAAO3B,EAASE,UAChBoE,SAAU7C,EACV8C,UAAQ,EACRtC,UAAU,4IACVuC,YAAa9E,EAAE,iBAAkB,gGAIrCyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,WAAY,iEAAe,SAEhCsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACLrD,KAAK,WACLW,MAAO3B,EAASG,SAChBmE,SAAU7C,EACV8C,UAAQ,EACRtC,UAAU,4IACVuC,YAAa9E,EAAE,gBAAiB,gGAIpCyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,QAAS,qGAAqB,SAEnCsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,QACLrD,KAAK,QACLW,MAAO3B,EAASI,MAChBkE,SAAU7C,EACV8C,UAAQ,EACRtC,UAAU,4IACVuC,YAAa9E,EAAE,aAAc,oIAIjCyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,QAAS,8DAEdsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,MACLrD,KAAK,QACLW,MAAO3B,EAASS,MAChB6D,SAAU7C,EACVQ,UAAU,4IACVuC,YAAa9E,EAAE,aAAc,0FAIjCyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,WAAY,iEAAe,SAEhCsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,WACLrD,KAAK,WACLW,MAAO3B,EAASK,SAChBiE,SAAU7C,EACV8C,UAAQ,EACRtC,UAAU,4IACVuC,YAAa9E,EAAE,gBAAiB,gGAIpCyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,kBAAmB,gGAAqB,SAE7CsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,WACLrD,KAAK,kBACLW,MAAO3B,EAASM,gBAChBgE,SAAU7C,EACV8C,UAAQ,EACRtC,UAAU,4IACVuC,YAAa9E,EAAE,kBAAmB,sGAKtCsC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SACxExC,EAAE,oBAAqB,4EAI5ByC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,OAAQ,kCAAS,SAEtByC,EAAAA,EAAAA,MAAA,UACEnB,KAAK,OACLW,MAAO3B,EAASO,KAChB+D,SAAU7C,EACV8C,UAAQ,EACRtC,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,aAAc,6DACjC+E,OAAOC,QAAQ5D,GAAO6D,IAAIC,IAAA,IAAEC,EAAKtE,GAAKqE,EAAA,OACrC5C,EAAAA,EAAAA,KAAA,UAAkBL,MAAOkD,EAAI3C,SAAE3B,EAAKS,MAAvB6D,YAKnB1C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,aAAc,kCAAS,SAE5ByC,EAAAA,EAAAA,MAAA,UACEnB,KAAK,aACLW,MAAO3B,EAASQ,WAChB8D,SAAU7C,EACV8C,UAAQ,EACRtC,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,mBAAoB,6DA3RlC,CAClB,kFACA,6CACA,6CACA,yDACA,uEAuRyBiF,IAAIG,IACf9C,EAAAA,EAAAA,KAAA,UAAmBL,MAAOmD,EAAK5C,SAAE4C,GAApBA,WAMC,WAAlB9E,EAASO,MAAuC,cAAlBP,EAASO,MAA0C,UAAlBP,EAASO,QACxE4B,EAAAA,EAAAA,MAAA4C,EAAAA,SAAA,CAAA7C,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SACxExC,EAAE,0BAA2B,0GAIlCyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,iBAAkB,2CAEvBsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACLrD,KAAK,iBACLW,MAAO3B,EAASU,eAChB4D,SAAU7C,EACVQ,UAAU,4IACVuC,YAAa9E,EAAE,sBAAuB,uEAI1CyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,gBAAiB,oEAEtBsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACLrD,KAAK,gBACLW,MAAO3B,EAASW,cAChB2D,SAAU7C,EACVQ,UAAU,4IACVuC,YAAa9E,EAAE,qBAAsB,mGAOzB,iBAAlBM,EAASO,MAA6C,uBAAlBP,EAASO,QAC7C4B,EAAAA,EAAAA,MAAA4C,EAAAA,SAAA,CAAA7C,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SACxExC,EAAE,6BAA8B,yIAIrCyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,iBAAkB,wDAEvBsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACLrD,KAAK,iBACLW,MAAO3B,EAASY,eAChB0D,SAAU7C,EACVQ,UAAU,4IACVuC,YAAa9E,EAAE,sBAAuB,oFAI1CyC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,cAAe,8DAEpByC,EAAAA,EAAAA,MAAA,UACEnB,KAAK,cACLW,MAAO3B,EAASa,YAChByD,SAAU7C,EACVQ,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,oBAAqB,sFAlWtC,CACnB,aACA,YACA,WACA,SA+V8BiF,IAAIN,IAChBrC,EAAAA,EAAAA,KAAA,UAAmBL,MAAO0C,EAAKnC,SAC5BxC,EAAE2E,EAAe,eAATA,EAAwB,iCAAmB,cAATA,EAAuB,2BAAkB,aAATA,EAAsB,uCAAW,6BADjGA,iBAUzBlC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kGAAiGC,SAAA,EAC9GF,EAAAA,EAAAA,KAAA,UACEqC,KAAK,SACLjC,QAASL,EACTE,UAAU,gJAA+IC,SAExJxC,EAAE,SAAU,qCAEfyC,EAAAA,EAAAA,MAAA,UACEkC,KAAK,SACLW,SAAUnF,EACVoC,UAAU,oJAAmJC,SAAA,CAE5JrC,IAAWmC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gCACxBpC,EAAUH,EAAE,WAAY,0EAAqBA,EAAE,aAAc,iGAlQtD,MCiMtB,EAhWsBJ,IAA+C,IAA9C,OAAEC,EAAM,QAAEC,EAAO,cAAEyF,EAAa,KAAEC,GAAM5F,EAC7D,MAAM,EAAEI,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CACvCG,UAAW,GACXC,SAAU,GACVC,MAAO,GACPG,KAAM,GACNC,WAAY,GACZC,MAAO,GACPC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,YAAa,GACboD,UAAU,IAGNnD,EAAQ,CACZC,MAAO,CAAEC,KAAMtB,EAAE,QAAS,iEAAgBuB,MAAO,QACjDC,OAAQ,CAAEF,KAAMtB,EAAE,SAAU,4BAASuB,MAAO,QAC5CE,UAAW,CAAEH,KAAMtB,EAAE,YAAa,wHAA0BuB,MAAO,SACnEG,MAAO,CAAEJ,KAAMtB,EAAE,QAAS,2DAAeuB,MAAO,QAChDI,aAAc,CAAEL,KAAMtB,EAAE,cAAe,iEAAgBuB,MAAO,UAC9DK,mBAAoB,CAAEN,KAAMtB,EAAE,oBAAqB,oFAAoBuB,MAAO,UAC9EM,aAAc,CAAEP,KAAMtB,EAAE,eAAgB,uEAAiBuB,MAAO,UAChEO,QAAS,CAAER,KAAMtB,EAAE,UAAW,4BAASuB,MAAO,SAmBhDkE,EAAAA,EAAAA,WAAU,KACJD,GACFjF,EAAY,CACVC,UAAWgF,EAAKhF,WAAa,GAC7BC,SAAU+E,EAAK/E,UAAY,GAC3BC,MAAO8E,EAAK9E,OAAS,GACrBG,KAAM2E,EAAK3E,MAAQ,GACnBC,WAAY0E,EAAK1E,YAAc,GAC/BC,MAAOyE,EAAKzE,OAAS,GACrBC,eAAgBwE,EAAKxE,gBAAkB,GACvCC,cAAeuE,EAAKvE,eAAiB,GACrCC,eAAgBsE,EAAKtE,gBAAkB,GACvCC,YAAaqE,EAAKrE,aAAe,GACjCoD,cAA4BmB,IAAlBF,EAAKjB,UAAyBiB,EAAKjB,YAGhD,CAACiB,IAEJ,MAAMzD,EAAqBC,IACzB,MAAM,KAAEV,EAAI,MAAEW,EAAK,KAAE0C,EAAI,QAAEgB,GAAY3D,EAAEE,OACzC3B,EAAY4B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACb,GAAgB,aAATqD,EAAsBgB,EAAU1D,MAqFtCI,EAAcA,KAClB9B,EAAY,CACVC,UAAW,GACXC,SAAU,GACVC,MAAO,GACPG,KAAM,GACNC,WAAY,GACZC,MAAO,GACPC,eAAgB,GAChBC,cAAe,GACfC,eAAgB,GAChBC,YAAa,GACboD,UAAU,IAEZzE,KAGF,OAAKD,GAGHyC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFAAgFC,UAC7FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+FAA8FC,SAAA,EAC3GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExC,EAAE,WAAY,sFAEjBsC,EAAAA,EAAAA,KAAA,UACEI,QAASL,EACTE,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAKnBE,EAAAA,EAAAA,MAAA,QAAME,SAtFSC,UAGnB,GAFAZ,EAAEa,iBA/BGvC,EAASE,UAAUsC,OAInBxC,EAASG,SAASqC,OAIlBxC,EAASI,MAAMoC,OAIfxC,EAASO,KAITP,EAASQ,WAKK,6BACH8E,KAAKtF,EAASI,SAC5BuC,EAAAA,GAAMC,MAAMlD,EAAE,eAAgB,kJACvB,IAPPiD,EAAAA,GAAMC,MAAMlD,EAAE,qBAAsB,kEAC7B,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,eAAgB,kEACvB,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,gBAAiB,qIACxB,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,mBAAoB,iGAC3B,IALPiD,EAAAA,GAAMC,MAAMlD,EAAE,oBAAqB,iGAC5B,GA+BT,CAIAI,GAAW,GACX,IACE,MAAM+C,QAAiBC,MAAM,iBAADG,OAAkBiC,EAAKrB,KAAO,CACxDd,OAAQ,MACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,WAElDC,KAAMC,KAAKC,UAAUtD,KAGvB,IAAI6C,EAASU,GAaT,MAAM,IAAIgC,MAAM,yBAbH,CACf,MAAMC,QAAoB3C,EAASY,OACnCd,EAAAA,GAAMe,QAAQhE,EAAE,0BAA2B,gIAC3CuF,EAAcO,EAAY7B,OAAI7B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAASoD,GAASlF,IAChDR,GACF,CAWF,CAAE,MAAOoD,GACPwB,QAAQxB,MAAM,uBAAwBA,GAQpCD,EAAAA,GAAMC,MAAMlD,EAAE,oBAAqB,mHAEvC,CAAC,QACCI,GAAW,EACb,CA1CA,GAiFkCmC,UAAU,gBAAeC,SAAA,EAErDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnExC,EAAE,mBAAoB,8GAEzByC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,YAAa,iEAAe,SAEjCsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACLrD,KAAK,YACLW,MAAO3B,EAASE,UAChBoE,SAAU7C,EACVQ,UAAU,4IACVsC,UAAQ,QAGZpC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,WAAY,iEAAe,SAEhCsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACLrD,KAAK,WACLW,MAAO3B,EAASG,SAChBmE,SAAU7C,EACVQ,UAAU,4IACVsC,UAAQ,cAOhBpC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnExC,EAAE,qBAAsB,4FAE3ByC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,QAAS,qGAAqB,SAEnCsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,QACLrD,KAAK,QACLW,MAAO3B,EAASI,MAChBkE,SAAU7C,EACVQ,UAAU,4IACVsC,UAAQ,QAGZpC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,QAAS,8DAEdsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,MACLrD,KAAK,QACLW,MAAO3B,EAASS,MAChB6D,SAAU7C,EACVQ,UAAU,wJAOlBE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnExC,EAAE,oBAAqB,0EAE1ByC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,OAAQ,kCAAS,SAEtByC,EAAAA,EAAAA,MAAA,UACEnB,KAAK,OACLW,MAAO3B,EAASO,KAChB+D,SAAU7C,EACVQ,UAAU,4IACVsC,UAAQ,EAAArC,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,aAAc,6DACjC+E,OAAOC,QAAQ5D,GAAO6D,IAAIC,IAAA,IAAEC,EAAKtE,GAAKqE,EAAA,OACrC5C,EAAAA,EAAAA,KAAA,UAAkBL,MAAOkD,EAAI3C,SAAE3B,EAAKS,MAAvB6D,YAInB1C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/ExC,EAAE,aAAc,kCAAS,SAE5ByC,EAAAA,EAAAA,MAAA,UACEnB,KAAK,aACLW,MAAO3B,EAASQ,WAChB8D,SAAU7C,EACVQ,UAAU,4IACVsC,UAAQ,EAAArC,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,mBAAoB,6DAvQpC,CAClB,kFACA,6CACA,6CACA,yDACA,uEAmQ2BiF,IAAIG,IACf9C,EAAAA,EAAAA,KAAA,UAAmBL,MAAOmD,EAAK5C,SAAE4C,GAApBA,iBAQvB3C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnExC,EAAE,gBAAiB,oEAEtByC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,SACEqC,KAAK,WACLrD,KAAK,WACLqE,QAASrF,EAASiE,SAClBK,SAAU7C,EACVQ,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mDAAkDC,SAChExC,EAAE,gBAAiB,wDAM1ByC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6FAA4FC,SAAA,EACzGF,EAAAA,EAAAA,KAAA,UACEqC,KAAK,SACLjC,QAASL,EACTE,UAAU,0LAAyLC,SAElMxC,EAAE,SAAU,qCAEfsC,EAAAA,EAAAA,KAAA,UACEqC,KAAK,SACLW,SAAUnF,EACVoC,UAAU,sOAAqOC,SAE9OrC,GACCsC,EAAAA,EAAAA,MAAA4C,EAAAA,SAAA,CAAA7C,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gCACZvC,EAAE,WAAY,8EAGjByC,EAAAA,EAAAA,MAAA4C,EAAAA,SAAA,CAAA7C,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvC,EAAE,aAAc,mGA7Kb,MCsRtB,EA5buB+F,KACrB,MAAM,EAAE/F,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACd8F,EAAOC,IAAY5F,EAAAA,EAAAA,UAAS,KAC5BF,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChC6F,EAAcC,IAAmB9F,EAAAA,EAAAA,UAAS,OAC1C+F,EAAiBC,IAAsBhG,EAAAA,EAAAA,WAAS,IAChDiG,EAAeC,IAAoBlG,EAAAA,EAAAA,WAAS,IAC5CmG,EAASC,IAAcpG,EAAAA,EAAAA,UAAS,CACrCQ,KAAM,GACNC,WAAY,GACZ4F,OAAQ,GACRC,OAAQ,KAIJvF,EAAQ,CACZC,MAAO,CAAEC,KAAMtB,EAAE,QAAS,iEAAgBuB,MAAO,QACjDC,OAAQ,CAAEF,KAAMtB,EAAE,SAAU,4BAASuB,MAAO,QAC5CE,UAAW,CAAEH,KAAMtB,EAAE,YAAa,wHAA0BuB,MAAO,SACnEG,MAAO,CAAEJ,KAAMtB,EAAE,QAAS,2DAAeuB,MAAO,QAChDI,aAAc,CAAEL,KAAMtB,EAAE,cAAe,iEAAgBuB,MAAO,UAC9DK,mBAAoB,CAAEN,KAAMtB,EAAE,oBAAqB,oFAAoBuB,MAAO,UAC9EM,aAAc,CAAEP,KAAMtB,EAAE,eAAgB,uEAAiBuB,MAAO,UAChEO,QAAS,CAAER,KAAMtB,EAAE,UAAW,4BAASuB,MAAO,SAWhDkE,EAAAA,EAAAA,WAAU,KACRmB,KACC,IAEH,MAAMA,EAAYhE,UAChB,IACExC,GAAW,GACX,MAAM+C,QAAiBC,MAAM,gBAAiB,CAC5CE,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,aAIpD,IAAIN,EAASU,GAKX,MAAM,IAAIgC,MAAM,uBALD,CACf,MAAM/B,QAAeX,EAASY,OAC9BkC,EAASnC,EAAOG,KAClB,CAIF,CAAE,MAAOf,GACPwB,QAAQxB,MAAM,uBAAwBA,GA+DpCD,EAAAA,GAAMC,MAAMlD,EAAE,oBAAqB,+HAEvC,CAAC,QACCI,GAAW,EACb,GAGIyG,EAAgBb,EAAMc,OAAOtB,IACjC,MAAMuB,GAAeP,EAAQ3F,MAAQ2E,EAAK3E,OAAS2F,EAAQ3F,KACrDmG,GAAqBR,EAAQ1F,YAAc0E,EAAK1E,aAAe0F,EAAQ1F,WACvEmG,GAAiBT,EAAQE,QACT,WAAnBF,EAAQE,QAAuBlB,EAAKjB,UACjB,aAAnBiC,EAAQE,SAA0BlB,EAAKjB,SACpC2C,GAAiBV,EAAQG,QAC7BnB,EAAKhF,UAAU2G,cAAcpE,SAASyD,EAAQG,OAAOQ,gBACrD3B,EAAK/E,SAAS0G,cAAcpE,SAASyD,EAAQG,OAAOQ,gBACpD3B,EAAK9E,MAAMyG,cAAcpE,SAASyD,EAAQG,OAAOQ,eAEnD,OAAOJ,GAAeC,GAAqBC,GAAiBC,IAwE9D,OAAI/G,GAEAmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAEpCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDC,SAClExC,EAAE,iBAAkB,kGAEvBsC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5CxC,EAAE,qBAAsB,yPAG7ByC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAM2D,GAAmB,GAClC9D,UAAU,oGAAmGC,SAAA,EAE7GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZvC,EAAE,aAAc,yGAMvBsC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mGAAkGC,UAC/GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,cAAe,+GAEpBsC,EAAAA,EAAAA,KAAA,SACEqC,KAAK,OACL1C,MAAOuE,EAAQG,OACf/B,SAAW5C,GAAMyE,GAAUrE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIoE,GAAO,IAAEG,OAAQ3E,EAAEE,OAAOD,SAC3D6C,YAAa9E,EAAE,oBAAqB,yGACpCuC,UAAU,kJAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,eAAgB,uFAErByC,EAAAA,EAAAA,MAAA,UACER,MAAOuE,EAAQ3F,KACf+D,SAAW5C,GAAMyE,GAAUrE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIoE,GAAO,IAAE3F,KAAMmB,EAAEE,OAAOD,SACzDM,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,WAAY,yEAC/B+E,OAAOC,QAAQ5D,GAAO6D,IAAIrF,IAAA,IAAEuF,EAAKtE,GAAKjB,EAAA,OACrC0C,EAAAA,EAAAA,KAAA,UAAkBL,MAAOkD,EAAI3C,SAAE3B,EAAKS,MAAvB6D,YAKnB1C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,qBAAsB,uFAE3ByC,EAAAA,EAAAA,MAAA,UACER,MAAOuE,EAAQ1F,WACf8D,SAAW5C,GAAMyE,GAAUrE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIoE,GAAO,IAAE1F,WAAYkB,EAAEE,OAAOD,SAC/DM,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,iBAAkB,yEA9P9B,CAClB,kFACA,6CACA,6CACA,yDACA,uEA0PuBiF,IAAIG,IACf9C,EAAAA,EAAAA,KAAA,UAAmBL,MAAOmD,EAAK5C,SAAE4C,GAApBA,WAKnB3C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ExC,EAAE,iBAAkB,6FAEvByC,EAAAA,EAAAA,MAAA,UACER,MAAOuE,EAAQE,OACf9B,SAAW5C,GAAMyE,GAAUrE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIoE,GAAO,IAAEE,OAAQ1E,EAAEE,OAAOD,SAC3DM,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQL,MAAM,GAAEO,SAAExC,EAAE,cAAe,0EACnCsC,EAAAA,EAAAA,KAAA,UAAQL,MAAM,SAAQO,SAAExC,EAAE,SAAU,yBACpCsC,EAAAA,EAAAA,KAAA,UAAQL,MAAM,WAAUO,SAAExC,EAAE,WAAY,uDAOhDyC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0GAAyGC,SAAA,EACtHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,CAChExC,EAAE,YAAa,+FAAoB,KAAG6G,EAAc7D,OAAO,UAIhEV,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,SAAQC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8BAA6BC,UAC5CC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GxC,EAAE,OAAQ,uDAEbsC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GxC,EAAE,OAAQ,qCAEbsC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GxC,EAAE,aAAc,qCAEnBsC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GxC,EAAE,SAAU,2CAEfsC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GxC,EAAE,YAAa,kDAElBsC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GxC,EAAE,UAAW,kEAIpBsC,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0EAAyEC,SACvFqE,EAAc5B,IAAIO,IAAI,IAAA4B,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACrB/E,EAAAA,EAAAA,MAAA,MAAmBF,UAAU,0CAAyCC,SAAA,EACpEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0BAAyBC,UACtCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uFAAsFC,UACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDAGjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,CAC/DgD,EAAKhF,UAAU,IAAEgF,EAAK/E,aAEzB6B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDgD,EAAK9E,iBAKd4B,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8EAAAgB,OAAgG,QAAhG6D,EAAgFhG,EAAMoE,EAAK3E,aAAK,IAAAuG,OAAA,EAAhBA,EAAkB7F,MAAK,cAAAgC,OAA6B,QAA7B8D,EAAajG,EAAMoE,EAAK3E,aAAK,IAAAwG,OAAA,EAAhBA,EAAkB9F,MAAK,iBAAAgC,OAAgC,QAAhC+D,EAAgBlG,EAAMoE,EAAK3E,aAAK,IAAAyG,OAAA,EAAhBA,EAAkB/F,MAAK,sBAAAgC,OAAqC,QAArCgE,EAAqBnG,EAAMoE,EAAK3E,aAAK,IAAA0G,OAAA,EAAhBA,EAAkBhG,MAAK,QAAOiB,SACjO,QADiOgF,EACjPpG,EAAMoE,EAAK3E,aAAK,IAAA2G,OAAA,EAAhBA,EAAkBlG,UAGvBgB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oEAAmEC,SAC9EgD,EAAK1E,cAERwB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,2EAAAgB,OACbiC,EAAKjB,SACD,uEACA,gEACH/B,SACAgD,EAAKjB,SAAWvE,EAAE,SAAU,sBAASA,EAAE,WAAY,8CAGxDsC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uEAAsEC,SACjFgD,EAAKhB,UAAY,IAAIJ,KAAKoB,EAAKhB,WAAWiD,mBAAmB,SAAWzH,EAAE,QAAS,4CAEtFsC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kDAAiDC,UAC7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEI,QAASA,KACPyD,EAAgBX,GAChBe,GAAiB,IAEnBhE,UAAU,gFACVmF,MAAO1H,EAAE,WAAY,mFAAkBwC,UAEvCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAEfD,EAAAA,EAAAA,KAAA,UACEI,QAASA,IA1PAE,OAAO+E,EAAQC,KAC5C,WACyBxE,MAAM,iBAADG,OAAkBoE,EAAM,kBAAkB,CACpEtE,OAAQ,QACRC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,cAIvCI,IACXoC,EAASD,EAAMf,IAAIO,GACjBA,EAAKrB,MAAQwD,GAAMvF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQoD,GAAI,IAAEjB,UAAWqD,IAAkBpC,IAEhEvC,EAAAA,GAAMe,QAAQhE,EAAE,oBAAqB,4HAGrCiG,EAASD,EAAMf,IAAIO,GACjBA,EAAKrB,MAAQwD,GAAMvF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQoD,GAAI,IAAEjB,UAAWqD,IAAkBpC,IAEhEvC,EAAAA,GAAMe,QAAQhE,EAAE,oBAAqB,0LAEzC,CAAE,MAAOkD,GACPwB,QAAQxB,MAAM,8BAA+BA,GAC7CD,EAAAA,GAAMC,MAAMlD,EAAE,oBAAqB,mHACrC,GAkOmC6H,CAAuBrC,EAAKrB,IAAKqB,EAAKjB,UACrDhC,UAAS,GAAAgB,OAAKiC,EAAKjB,SAAW,kCAAoC,sCAAqC,yBACvGmD,MAAOlC,EAAKjB,SAAWvE,EAAE,iBAAkB,kHAA0BA,EAAE,eAAgB,mFAAkBwC,UAEzGF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,OAAAgB,OAASiC,EAAKjB,SAAW,gBAAkB,sBAEzDjC,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAtONE,WACvB,GAAKkF,OAAOC,QAAQ/H,EAAE,oBAAqB,6KAI3C,WACyBoD,MAAM,iBAADG,OAAkBoE,GAAU,CACtDtE,OAAQ,SACRC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,cAIvCI,IACXoC,EAASD,EAAMc,OAAOtB,GAAQA,EAAKrB,MAAQwD,IAC3C1E,EAAAA,GAAMe,QAAQhE,EAAE,cAAe,uFAG/BiG,EAASD,EAAMc,OAAOtB,GAAQA,EAAKrB,MAAQwD,IAC3C1E,EAAAA,GAAMe,QAAQhE,EAAE,cAAe,qJAEnC,CAAE,MAAOkD,GACPwB,QAAQxB,MAAM,uBAAwBA,GACtCD,EAAAA,GAAMC,MAAMlD,EAAE,oBAAqB,uGACrC,GA8MmCgI,CAAiBxC,EAAKrB,KACrC5B,UAAU,4EACVmF,MAAO1H,EAAE,aAAc,uEAAgBwC,UAEvCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0BA9DZiD,EAAKrB,cAwEI,IAAzB0C,EAAc7D,SACbP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mBAAkBC,SAAA,EAC/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnExC,EAAE,eAAgB,6FAErBsC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5CxC,EAAE,mBAAoB,oQAO/BsC,EAAAA,EAAAA,KAAC2F,EAAe,CACdpI,OAAQuG,EACRtG,QAASA,IAAMuG,GAAmB,GAClCtG,cA1OqBmI,IACzBjC,EAASkC,GAAa,IAAIA,EAAWD,IACrC7B,GAAmB,OA4OjB/D,EAAAA,EAAAA,KAAC8F,EAAa,CACZvI,OAAQyG,EACRxG,QAASA,KACPyG,GAAiB,GACjBJ,EAAgB,OAElBZ,cA/OqBO,IACzBG,EAASkC,GACPA,EAAUlD,IAAIO,GACZA,EAAKrB,MAAQ2B,EAAY3B,IAAM2B,EAAcN,IAGjDe,GAAiB,GACjBJ,EAAgB,OAyOZX,KAAMU,O", "sources": ["components/Admin/CreateUserModal.jsx", "components/Admin/EditUserModal.jsx", "pages/Admin/UserManagement.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst CreateUserModal = ({ isOpen, onClose, onUserCreated }) => {\n  const { t, isRTL } = useLanguage();\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    role: '',\n    department: '',\n    phone: '',\n    specialization: '',\n    licenseNumber: '',\n    contractNumber: '',\n    serviceType: ''\n  });\n\n  const roles = {\n    admin: { name: t('admin', 'مدير النظام'), color: 'gray' },\n    doctor: { name: t('doctor', 'طبيب'), color: 'blue' },\n    therapist: { name: t('therapist', 'أخصائي العلاج الطبيعي'), color: 'green' },\n    nurse: { name: t('nurse', 'ممرض/ممرضة'), color: 'pink' },\n    external_lab: { name: t('externalLab', 'مختبر خارجي'), color: 'purple' },\n    external_radiology: { name: t('externalRadiology', 'مركز أشعة خارجي'), color: 'indigo' },\n    receptionist: { name: t('receptionist', 'موظف استقبال'), color: 'yellow' },\n    manager: { name: t('manager', 'مدير'), color: 'red' }\n  };\n\n  const departments = [\n    'العلاج الطبيعي',\n    'التمريض',\n    'الإدارة',\n    'الاستقبال',\n    'خدمات خارجية'\n  ];\n\n  const serviceTypes = [\n    'laboratory',\n    'radiology',\n    'pharmacy',\n    'other'\n  ];\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const validateForm = () => {\n    if (!formData.firstName.trim()) {\n      toast.error(t('firstNameRequired', 'الاسم الأول مطلوب'));\n      return false;\n    }\n    if (!formData.lastName.trim()) {\n      toast.error(t('lastNameRequired', 'اسم العائلة مطلوب'));\n      return false;\n    }\n    if (!formData.email.trim()) {\n      toast.error(t('emailRequired', 'البريد الإلكتروني مطلوب'));\n      return false;\n    }\n    if (!formData.email.includes('@')) {\n      toast.error(t('emailInvalid', 'البريد الإلكتروني غير صحيح'));\n      return false;\n    }\n    if (!formData.password) {\n      toast.error(t('passwordRequired', 'كلمة المرور مطلوبة'));\n      return false;\n    }\n    if (formData.password.length < 6) {\n      toast.error(t('passwordTooShort', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'));\n      return false;\n    }\n    if (formData.password !== formData.confirmPassword) {\n      toast.error(t('passwordMismatch', 'كلمة المرور غير متطابقة'));\n      return false;\n    }\n    if (!formData.role) {\n      toast.error(t('roleRequired', 'الدور مطلوب'));\n      return false;\n    }\n    if (!formData.department) {\n      toast.error(t('departmentRequired', 'القسم مطلوب'));\n      return false;\n    }\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      const response = await fetch('/api/v1/users', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(formData)\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        toast.success(t('userCreatedSuccessfully', 'تم إنشاء المستخدم بنجاح'));\n        onUserCreated(result.data);\n        handleClose();\n      } else {\n        // Mock success for demo\n        const mockUser = {\n          _id: Date.now().toString(),\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n          email: formData.email,\n          role: formData.role,\n          department: formData.department,\n          isActive: true,\n          lastLogin: null,\n          createdAt: new Date()\n        };\n        toast.success(t('userCreatedSuccessfully', 'تم إنشاء المستخدم بنجاح (وضع التجريب)'));\n        onUserCreated(mockUser);\n        handleClose();\n      }\n    } catch (error) {\n      console.error('Error creating user:', error);\n      toast.error(t('errorCreatingUser', 'خطأ في إنشاء المستخدم'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    setFormData({\n      firstName: '',\n      lastName: '',\n      email: '',\n      password: '',\n      confirmPassword: '',\n      role: '',\n      department: '',\n      phone: '',\n      specialization: '',\n      licenseNumber: '',\n      contractNumber: '',\n      serviceType: ''\n    });\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('addNewUser', 'إضافة مستخدم جديد')}\n            </h3>\n            <button\n              onClick={handleClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"fas fa-times text-xl\"></i>\n            </button>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            {/* Basic Information */}\n            <div className=\"md:col-span-2\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('basicInformation', 'المعلومات الأساسية')}\n              </h4>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('firstName', 'الاسم الأول')} *\n              </label>\n              <input\n                type=\"text\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                placeholder={t('enterFirstName', 'أدخل الاسم الأول')}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('lastName', 'اسم العائلة')} *\n              </label>\n              <input\n                type=\"text\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                placeholder={t('enterLastName', 'أدخل اسم العائلة')}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('email', 'البريد الإلكتروني')} *\n              </label>\n              <input\n                type=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                placeholder={t('enterEmail', 'أدخل البريد الإلكتروني')}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('phone', 'رقم الهاتف')}\n              </label>\n              <input\n                type=\"tel\"\n                name=\"phone\"\n                value={formData.phone}\n                onChange={handleInputChange}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                placeholder={t('enterPhone', 'أدخل رقم الهاتف')}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('password', 'كلمة المرور')} *\n              </label>\n              <input\n                type=\"password\"\n                name=\"password\"\n                value={formData.password}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                placeholder={t('enterPassword', 'أدخل كلمة المرور')}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('confirmPassword', 'تأكيد كلمة المرور')} *\n              </label>\n              <input\n                type=\"password\"\n                name=\"confirmPassword\"\n                value={formData.confirmPassword}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                placeholder={t('confirmPassword', 'تأكيد كلمة المرور')}\n              />\n            </div>\n\n            {/* Role and Department */}\n            <div className=\"md:col-span-2\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4 mt-4\">\n                {t('roleAndDepartment', 'الدور والقسم')}\n              </h4>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('role', 'الدور')} *\n              </label>\n              <select\n                name=\"role\"\n                value={formData.role}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">{t('selectRole', 'اختر الدور')}</option>\n                {Object.entries(roles).map(([key, role]) => (\n                  <option key={key} value={key}>{role.name}</option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('department', 'القسم')} *\n              </label>\n              <select\n                name=\"department\"\n                value={formData.department}\n                onChange={handleInputChange}\n                required\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"\">{t('selectDepartment', 'اختر القسم')}</option>\n                {departments.map(dept => (\n                  <option key={dept} value={dept}>{dept}</option>\n                ))}\n              </select>\n            </div>\n\n            {/* Professional Information */}\n            {(formData.role === 'doctor' || formData.role === 'therapist' || formData.role === 'nurse') && (\n              <>\n                <div className=\"md:col-span-2\">\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4 mt-4\">\n                    {t('professionalInformation', 'المعلومات المهنية')}\n                  </h4>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('specialization', 'التخصص')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"specialization\"\n                    value={formData.specialization}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                    placeholder={t('enterSpecialization', 'أدخل التخصص')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('licenseNumber', 'رقم الترخيص')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"licenseNumber\"\n                    value={formData.licenseNumber}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                    placeholder={t('enterLicenseNumber', 'أدخل رقم الترخيص')}\n                  />\n                </div>\n              </>\n            )}\n\n            {/* External Service Information */}\n            {(formData.role === 'external_lab' || formData.role === 'external_radiology') && (\n              <>\n                <div className=\"md:col-span-2\">\n                  <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4 mt-4\">\n                    {t('externalServiceInformation', 'معلومات الخدمة الخارجية')}\n                  </h4>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('contractNumber', 'رقم العقد')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"contractNumber\"\n                    value={formData.contractNumber}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                    placeholder={t('enterContractNumber', 'أدخل رقم العقد')}\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('serviceType', 'نوع الخدمة')}\n                  </label>\n                  <select\n                    name=\"serviceType\"\n                    value={formData.serviceType}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  >\n                    <option value=\"\">{t('selectServiceType', 'اختر نوع الخدمة')}</option>\n                    {serviceTypes.map(type => (\n                      <option key={type} value={type}>\n                        {t(type, type === 'laboratory' ? 'مختبر' : type === 'radiology' ? 'أشعة' : type === 'pharmacy' ? 'صيدلية' : 'أخرى')}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </>\n            )}\n          </div>\n\n          <div className=\"flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200 dark:border-gray-600\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              className=\"px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n            >\n              {t('cancel', 'إلغاء')}\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n            >\n              {loading && <i className=\"fas fa-spinner fa-spin mr-2\"></i>}\n              {loading ? t('creating', 'جاري الإنشاء...') : t('createUser', 'إنشاء المستخدم')}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default CreateUserModal;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst EditUserModal = ({ isOpen, onClose, onUserUpdated, user }) => {\n  const { t, isRTL } = useLanguage();\n  const [loading, setLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    role: '',\n    department: '',\n    phone: '',\n    specialization: '',\n    licenseNumber: '',\n    contractNumber: '',\n    serviceType: '',\n    isActive: true\n  });\n\n  const roles = {\n    admin: { name: t('admin', 'مدير النظام'), color: 'gray' },\n    doctor: { name: t('doctor', 'طبيب'), color: 'blue' },\n    therapist: { name: t('therapist', 'أخصائي العلاج الطبيعي'), color: 'green' },\n    nurse: { name: t('nurse', 'ممرض/ممرضة'), color: 'pink' },\n    external_lab: { name: t('externalLab', 'مختبر خارجي'), color: 'purple' },\n    external_radiology: { name: t('externalRadiology', 'مركز أشعة خارجي'), color: 'indigo' },\n    receptionist: { name: t('receptionist', 'موظف استقبال'), color: 'yellow' },\n    manager: { name: t('manager', 'مدير'), color: 'red' }\n  };\n\n  const departments = [\n    'العلاج الطبيعي',\n    'التمريض',\n    'الإدارة',\n    'الاستقبال',\n    'خدمات خارجية'\n  ];\n\n  const serviceTypes = [\n    'laboratory',\n    'radiology',\n    'pharmacy',\n    'other'\n  ];\n\n  // Populate form data when user prop changes\n  useEffect(() => {\n    if (user) {\n      setFormData({\n        firstName: user.firstName || '',\n        lastName: user.lastName || '',\n        email: user.email || '',\n        role: user.role || '',\n        department: user.department || '',\n        phone: user.phone || '',\n        specialization: user.specialization || '',\n        licenseNumber: user.licenseNumber || '',\n        contractNumber: user.contractNumber || '',\n        serviceType: user.serviceType || '',\n        isActive: user.isActive !== undefined ? user.isActive : true\n      });\n    }\n  }, [user]);\n\n  const handleInputChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n  };\n\n  const validateForm = () => {\n    if (!formData.firstName.trim()) {\n      toast.error(t('firstNameRequired', 'الاسم الأول مطلوب'));\n      return false;\n    }\n    if (!formData.lastName.trim()) {\n      toast.error(t('lastNameRequired', 'اسم العائلة مطلوب'));\n      return false;\n    }\n    if (!formData.email.trim()) {\n      toast.error(t('emailRequired', 'البريد الإلكتروني مطلوب'));\n      return false;\n    }\n    if (!formData.role) {\n      toast.error(t('roleRequired', 'الدور مطلوب'));\n      return false;\n    }\n    if (!formData.department) {\n      toast.error(t('departmentRequired', 'القسم مطلوب'));\n      return false;\n    }\n\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(formData.email)) {\n      toast.error(t('invalidEmail', 'البريد الإلكتروني غير صحيح'));\n      return false;\n    }\n\n    return true;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const response = await fetch(`/api/v1/users/${user._id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(formData)\n      });\n\n      if (response.ok) {\n        const updatedUser = await response.json();\n        toast.success(t('userUpdatedSuccessfully', 'تم تحديث المستخدم بنجاح'));\n        onUserUpdated(updatedUser.data || { ...user, ...formData });\n        onClose();\n      } else {\n        // In development mode, simulate successful update\n        if (process.env.NODE_ENV === 'development') {\n          const updatedUser = { ...user, ...formData };\n          toast.success(t('userUpdatedSuccessfully', 'تم تحديث المستخدم بنجاح'));\n          onUserUpdated(updatedUser);\n          onClose();\n        } else {\n          throw new Error('Failed to update user');\n        }\n      }\n    } catch (error) {\n      console.error('Error updating user:', error);\n      if (process.env.NODE_ENV === 'development') {\n        // In development mode, simulate successful update\n        const updatedUser = { ...user, ...formData };\n        toast.success(t('userUpdatedSuccessfully', 'تم تحديث المستخدم بنجاح'));\n        onUserUpdated(updatedUser);\n        onClose();\n      } else {\n        toast.error(t('errorUpdatingUser', 'خطأ في تحديث المستخدم'));\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    setFormData({\n      firstName: '',\n      lastName: '',\n      email: '',\n      role: '',\n      department: '',\n      phone: '',\n      specialization: '',\n      licenseNumber: '',\n      contractNumber: '',\n      serviceType: '',\n      isActive: true\n    });\n    onClose();\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n              {t('editUser', 'تعديل المستخدم')}\n            </h2>\n            <button\n              onClick={handleClose}\n              className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <i className=\"fas fa-times text-xl\"></i>\n            </button>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n          {/* Basic Information */}\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n              {t('basicInformation', 'المعلومات الأساسية')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('firstName', 'الاسم الأول')} *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"firstName\"\n                  value={formData.firstName}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('lastName', 'اسم العائلة')} *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"lastName\"\n                  value={formData.lastName}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  required\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Contact Information */}\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n              {t('contactInformation', 'معلومات الاتصال')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('email', 'البريد الإلكتروني')} *\n                </label>\n                <input\n                  type=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  required\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('phone', 'رقم الهاتف')}\n                </label>\n                <input\n                  type=\"tel\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Role and Department */}\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n              {t('roleAndDepartment', 'الدور والقسم')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('role', 'الدور')} *\n                </label>\n                <select\n                  name=\"role\"\n                  value={formData.role}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  required\n                >\n                  <option value=\"\">{t('selectRole', 'اختر الدور')}</option>\n                  {Object.entries(roles).map(([key, role]) => (\n                    <option key={key} value={key}>{role.name}</option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('department', 'القسم')} *\n                </label>\n                <select\n                  name=\"department\"\n                  value={formData.department}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  required\n                >\n                  <option value=\"\">{t('selectDepartment', 'اختر القسم')}</option>\n                  {departments.map(dept => (\n                    <option key={dept} value={dept}>{dept}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Status */}\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n              {t('accountStatus', 'حالة الحساب')}\n            </h3>\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                name=\"isActive\"\n                checked={formData.isActive}\n                onChange={handleInputChange}\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <label className=\"ml-2 block text-sm text-gray-900 dark:text-white\">\n                {t('activeAccount', 'حساب نشط')}\n              </label>\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-600\">\n            <button\n              type=\"button\"\n              onClick={handleClose}\n              className=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600\"\n            >\n              {t('cancel', 'إلغاء')}\n            </button>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? (\n                <>\n                  <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                  {t('updating', 'جاري التحديث...')}\n                </>\n              ) : (\n                <>\n                  <i className=\"fas fa-save mr-2\"></i>\n                  {t('updateUser', 'تحديث المستخدم')}\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default EditUserModal;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport CreateUserModal from '../../components/Admin/CreateUserModal';\nimport EditUserModal from '../../components/Admin/EditUserModal';\nimport toast from 'react-hot-toast';\n\nconst UserManagement = () => {\n  const { t, isRTL } = useLanguage();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [showEditModal, setShowEditModal] = useState(false);\n  const [filters, setFilters] = useState({\n    role: '',\n    department: '',\n    status: '',\n    search: ''\n  });\n\n  // Role definitions with Arabic translations\n  const roles = {\n    admin: { name: t('admin', 'مدير النظام'), color: 'gray' },\n    doctor: { name: t('doctor', 'طبيب'), color: 'blue' },\n    therapist: { name: t('therapist', 'أخصائي العلاج الطبيعي'), color: 'green' },\n    nurse: { name: t('nurse', 'ممرض/ممرضة'), color: 'pink' },\n    external_lab: { name: t('externalLab', 'مختبر خارجي'), color: 'purple' },\n    external_radiology: { name: t('externalRadiology', 'مركز أشعة خارجي'), color: 'indigo' },\n    receptionist: { name: t('receptionist', 'موظف استقبال'), color: 'yellow' },\n    manager: { name: t('manager', 'مدير'), color: 'red' }\n  };\n\n  const departments = [\n    'العلاج الطبيعي',\n    'التمريض',\n    'الإدارة',\n    'الاستقبال',\n    'خدمات خارجية'\n  ];\n\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/v1/users', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setUsers(result.data);\n      } else {\n        // API call failed, will be handled in catch block\n        throw new Error('API response not ok');\n      }\n    } catch (error) {\n      console.error('Error loading users:', error);\n      // In development mode, use mock data without showing error\n      if (process.env.NODE_ENV === 'development') {\n        console.log('Using mock user data for development');\n        const mockUsers = [\n          {\n            _id: '1',\n            firstName: 'أحمد',\n            lastName: 'محمد',\n            email: '<EMAIL>',\n            role: 'doctor',\n            department: 'العلاج الطبيعي',\n            isActive: true,\n            lastLogin: new Date('2024-01-15'),\n            createdAt: new Date('2024-01-01')\n          },\n          {\n            _id: '2',\n            firstName: 'فاطمة',\n            lastName: 'أحمد',\n            email: '<EMAIL>',\n            role: 'therapist',\n            department: 'العلاج الطبيعي',\n            isActive: true,\n            lastLogin: new Date('2024-01-14'),\n            createdAt: new Date('2024-01-02')\n          },\n          {\n            _id: '3',\n            firstName: 'محمد',\n            lastName: 'علي',\n            email: '<EMAIL>',\n            role: 'admin',\n            department: 'الإدارة',\n            isActive: true,\n            lastLogin: new Date('2024-01-13'),\n            createdAt: new Date('2024-01-03')\n          },\n          {\n            _id: '4',\n            firstName: 'سارة',\n            lastName: 'محمود',\n            email: '<EMAIL>',\n            role: 'receptionist',\n            department: 'الاستقبال',\n            isActive: true,\n            lastLogin: new Date('2024-01-12'),\n            createdAt: new Date('2024-01-04')\n          },\n          {\n            _id: '5',\n            firstName: 'مركز الأشعة المتقدم',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'external_radiology',\n            department: 'خدمات خارجية',\n            isActive: false,\n            lastLogin: new Date('2024-01-10'),\n            createdAt: new Date('2024-01-05')\n          }\n        ];\n        setUsers(mockUsers);\n      } else {\n        toast.error(t('errorLoadingUsers', 'خطأ في تحميل المستخدمين'));\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filteredUsers = users.filter(user => {\n    const matchesRole = !filters.role || user.role === filters.role;\n    const matchesDepartment = !filters.department || user.department === filters.department;\n    const matchesStatus = !filters.status || \n      (filters.status === 'active' && user.isActive) ||\n      (filters.status === 'inactive' && !user.isActive);\n    const matchesSearch = !filters.search || \n      user.firstName.toLowerCase().includes(filters.search.toLowerCase()) ||\n      user.lastName.toLowerCase().includes(filters.search.toLowerCase()) ||\n      user.email.toLowerCase().includes(filters.search.toLowerCase());\n\n    return matchesRole && matchesDepartment && matchesStatus && matchesSearch;\n  });\n\n  const handleToggleUserStatus = async (userId, currentStatus) => {\n    try {\n      const response = await fetch(`/api/v1/users/${userId}/toggle-status`, {\n        method: 'PATCH',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        setUsers(users.map(user => \n          user._id === userId ? { ...user, isActive: !currentStatus } : user\n        ));\n        toast.success(t('userStatusUpdated', 'تم تحديث حالة المستخدم'));\n      } else {\n        // Mock success for demo\n        setUsers(users.map(user => \n          user._id === userId ? { ...user, isActive: !currentStatus } : user\n        ));\n        toast.success(t('userStatusUpdated', 'تم تحديث حالة المستخدم (وضع التجريب)'));\n      }\n    } catch (error) {\n      console.error('Error updating user status:', error);\n      toast.error(t('errorUpdatingUser', 'خطأ في تحديث المستخدم'));\n    }\n  };\n\n  const handleDeleteUser = async (userId) => {\n    if (!window.confirm(t('confirmDeleteUser', 'هل أنت متأكد من حذف هذا المستخدم؟'))) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/v1/users/${userId}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        setUsers(users.filter(user => user._id !== userId));\n        toast.success(t('userDeleted', 'تم حذف المستخدم'));\n      } else {\n        // Mock success for demo\n        setUsers(users.filter(user => user._id !== userId));\n        toast.success(t('userDeleted', 'تم حذف المستخدم (وضع التجريب)'));\n      }\n    } catch (error) {\n      console.error('Error deleting user:', error);\n      toast.error(t('errorDeletingUser', 'خطأ في حذف المستخدم'));\n    }\n  };\n\n  const handleUserCreated = (newUser) => {\n    setUsers(prevUsers => [...prevUsers, newUser]);\n    setShowCreateModal(false);\n  };\n\n  const handleUserUpdated = (updatedUser) => {\n    setUsers(prevUsers =>\n      prevUsers.map(user =>\n        user._id === updatedUser._id ? updatedUser : user\n      )\n    );\n    setShowEditModal(false);\n    setSelectedUser(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n              {t('userManagement', 'إدارة المستخدمين')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {t('userManagementDesc', 'إدارة حسابات المستخدمين وصلاحياتهم في النظام')}\n            </p>\n          </div>\n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center\"\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('addNewUser', 'إضافة مستخدم جديد')}\n          </button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('searchUsers', 'البحث في المستخدمين')}\n            </label>\n            <input\n              type=\"text\"\n              value={filters.search}\n              onChange={(e) => setFilters({ ...filters, search: e.target.value })}\n              placeholder={t('searchPlaceholder', 'اسم، بريد إلكتروني...')}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('filterByRole', 'تصفية حسب الدور')}\n            </label>\n            <select\n              value={filters.role}\n              onChange={(e) => setFilters({ ...filters, role: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">{t('allRoles', 'جميع الأدوار')}</option>\n              {Object.entries(roles).map(([key, role]) => (\n                <option key={key} value={key}>{role.name}</option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('filterByDepartment', 'تصفية حسب القسم')}\n            </label>\n            <select\n              value={filters.department}\n              onChange={(e) => setFilters({ ...filters, department: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">{t('allDepartments', 'جميع الأقسام')}</option>\n              {departments.map(dept => (\n                <option key={dept} value={dept}>{dept}</option>\n              ))}\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('filterByStatus', 'تصفية حسب الحالة')}\n            </label>\n            <select\n              value={filters.status}\n              onChange={(e) => setFilters({ ...filters, status: e.target.value })}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">{t('allStatuses', 'جميع الحالات')}</option>\n              <option value=\"active\">{t('active', 'نشط')}</option>\n              <option value=\"inactive\">{t('inactive', 'غير نشط')}</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Users Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('usersList', 'قائمة المستخدمين')} ({filteredUsers.length})\n          </h3>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('user', 'المستخدم')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('role', 'الدور')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('department', 'القسم')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('status', 'الحالة')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('lastLogin', 'آخر دخول')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('actions', 'الإجراءات')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {filteredUsers.map(user => (\n                <tr key={user._id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                          <i className=\"fas fa-user text-gray-600 dark:text-gray-300\"></i>\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {user.firstName} {user.lastName}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {user.email}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${roles[user.role]?.color}-100 text-${roles[user.role]?.color}-800 dark:bg-${roles[user.role]?.color}-900/30 dark:text-${roles[user.role]?.color}-200`}>\n                      {roles[user.role]?.name}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {user.department}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                      user.isActive \n                        ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200'\n                        : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200'\n                    }`}>\n                      {user.isActive ? t('active', 'نشط') : t('inactive', 'غير نشط')}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                    {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-SA') : t('never', 'لم يدخل')}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex items-center space-x-2\">\n                      <button\n                        onClick={() => {\n                          setSelectedUser(user);\n                          setShowEditModal(true);\n                        }}\n                        className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\n                        title={t('editUser', 'تعديل المستخدم')}\n                      >\n                        <i className=\"fas fa-edit\"></i>\n                      </button>\n                      <button\n                        onClick={() => handleToggleUserStatus(user._id, user.isActive)}\n                        className={`${user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'} dark:text-opacity-80`}\n                        title={user.isActive ? t('deactivateUser', 'إلغاء تفعيل المستخدم') : t('activateUser', 'تفعيل المستخدم')}\n                      >\n                        <i className={`fas ${user.isActive ? 'fa-user-slash' : 'fa-user-check'}`}></i>\n                      </button>\n                      <button\n                        onClick={() => handleDeleteUser(user._id)}\n                        className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                        title={t('deleteUser', 'حذف المستخدم')}\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {filteredUsers.length === 0 && (\n          <div className=\"p-12 text-center\">\n            <i className=\"fas fa-users text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('noUsersFound', 'لا توجد مستخدمون')}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {t('noUsersFoundDesc', 'لم يتم العثور على مستخدمين يطابقون معايير البحث')}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Create User Modal */}\n      <CreateUserModal\n        isOpen={showCreateModal}\n        onClose={() => setShowCreateModal(false)}\n        onUserCreated={handleUserCreated}\n      />\n\n      {/* Edit User Modal */}\n      <EditUserModal\n        isOpen={showEditModal}\n        onClose={() => {\n          setShowEditModal(false);\n          setSelectedUser(null);\n        }}\n        onUserUpdated={handleUserUpdated}\n        user={selectedUser}\n      />\n    </div>\n  );\n};\n\nexport default UserManagement;\n"], "names": ["_ref", "isOpen", "onClose", "onUserCreated", "t", "isRTL", "useLanguage", "loading", "setLoading", "useState", "formData", "setFormData", "firstName", "lastName", "email", "password", "confirmPassword", "role", "department", "phone", "specialization", "licenseNumber", "contractNumber", "serviceType", "roles", "admin", "name", "color", "doctor", "therapist", "nurse", "external_lab", "external_radiology", "receptionist", "manager", "handleInputChange", "e", "value", "target", "prev", "_objectSpread", "handleClose", "_jsx", "className", "children", "_jsxs", "onClick", "onSubmit", "async", "preventDefault", "trim", "includes", "length", "toast", "error", "response", "fetch", "method", "headers", "concat", "localStorage", "getItem", "body", "JSON", "stringify", "ok", "result", "json", "success", "data", "mockUser", "_id", "Date", "now", "toString", "isActive", "lastLogin", "createdAt", "console", "type", "onChange", "required", "placeholder", "Object", "entries", "map", "_ref2", "key", "dept", "_Fragment", "disabled", "onUserUpdated", "user", "useEffect", "undefined", "checked", "test", "Error", "updatedUser", "UserManagement", "users", "setUsers", "selected<PERSON>ser", "setSelectedUser", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "filters", "setFilters", "status", "search", "loadUsers", "filteredUsers", "filter", "matchesRole", "matchesDepartment", "matchesStatus", "matchesSearch", "toLowerCase", "_roles$user$role", "_roles$user$role2", "_roles$user$role3", "_roles$user$role4", "_roles$user$role5", "toLocaleDateString", "title", "userId", "currentStatus", "handleToggleUserStatus", "window", "confirm", "handleDeleteUser", "CreateUserModal", "newUser", "prevUsers", "EditUserModal"], "sourceRoot": ""}