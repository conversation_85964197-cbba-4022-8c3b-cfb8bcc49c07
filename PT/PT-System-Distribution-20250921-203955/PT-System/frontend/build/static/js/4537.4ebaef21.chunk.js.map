{"version": 3, "file": "static/js/4537.4ebaef21.chunk.js", "mappings": "2OAOA,MA+UA,EA/UcA,KACZ,MAAM,MAAEC,EAAK,QAAEC,IAAYC,EAAAA,EAAAA,MACrB,EAAEC,EAAC,MAAEC,EAAK,eAAEC,IAAmBC,EAAAA,EAAAA,MAC/B,MAAEC,EAAK,YAAEC,IAAgBC,EAAAA,EAAAA,MAExBC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CACvCC,SAAU,GACVC,SAAU,GACVC,YAAY,KAGPC,EAAQC,IAAaL,EAAAA,EAAAA,UAAS,CAAC,IAC/BM,EAAcC,IAAmBP,EAAAA,EAAAA,WAAS,GAyB3CQ,EAAgBC,IACpB,MAAM,KAAEC,EAAI,MAAEC,EAAK,KAAEC,EAAI,QAAEC,GAAYJ,EAAEK,OACzCf,EAAYgB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACL,GAAgB,aAATE,EAAsBC,EAAUF,KAItCP,EAAOM,IACTL,EAAUU,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACL,GAAO,OA4D1C,OACEO,EAAAA,EAAAA,MAAA,OAAKC,UAAS,qBAAAC,OAAuB3B,EAAQ,cAAgB,gBAAiB4B,SAAA,EAE5EC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wFAAuFE,UACpGH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCE,SAAA,EAE9CH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBE,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uHAAsHE,UACnIC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,4CAEfG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7D7B,EAAE,YAEL8B,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gDAA+CE,SACzD5B,EACG,0IACA,2CAMRyB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGE,SAAA,EAC7GC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4DAA2DE,SACtE5B,EAAQ,mFAAoB,uBAE/B6B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SArHZ,CACtB,CACEnB,SAAU,wBACVC,SAAU,YACVoB,KAAM,gBACNZ,KAAM,cAER,CACET,SAAU,yBACVC,SAAU,aACVoB,KAAM,SACNZ,KAAM,uBAER,CACET,SAAU,4BACVC,SAAU,gBACVoB,KAAM,qBACNZ,KAAM,mBAqGmBa,IAAI,CAACC,EAAMC,KAC1BR,EAAAA,EAAAA,MAAA,UAEES,QAASA,KAAMC,OAhDFC,EAgDsBJ,OA/CjDzB,EAAYgB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPd,SAAU2B,EAAY3B,SACtBC,SAAU0B,EAAY1B,YAJG0B,OAiDbV,UAAU,0KAAyKE,SAAA,EAEnLH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+CAA8CE,SAAA,CAC1DI,EAAKd,KAAK,KAAGc,EAAKF,KAAK,QAE1BD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mCAAkCE,SAC9CI,EAAKvB,aARHwB,UAgBbR,EAAAA,EAAAA,MAAA,QAAMY,SAhFOC,UAGnB,GAFArB,EAAEsB,iBArBiBC,MACnB,MAAMC,EAAY,CAAC,EAenB,OAbKnC,EAASG,SAASiC,OAEXpC,EAASG,SAASkC,SAAS,OACrCF,EAAUhC,SAAWV,EAAE,iBAFvB0C,EAAUhC,SAAWV,EAAE,YAKpBO,EAASI,SAEHJ,EAASI,SAASkC,OAAS,IACpCH,EAAU/B,SAAWX,EAAE,qBAFvB0C,EAAU/B,SAAWX,EAAE,YAKzBc,EAAU4B,GAC+B,IAAlCI,OAAOC,KAAKL,GAAWG,QAOzBJ,GAEL,UACQ5C,EAAM,CACVa,SAAUH,EAASG,SACnBC,SAAUJ,EAASI,SACnBC,WAAYL,EAASK,YAEzB,CAAE,MAAOoC,GACPC,QAAQD,MAAM,eAAgBA,EAChC,GAmEoCrB,UAAU,YAAWE,SAAA,EAEjDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOoB,QAAQ,WAAWvB,UAAU,kEAAiEE,SAClG7B,EAAE,eAEL0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEqB,GAAG,WACHhC,KAAK,WACLE,KAAK,QACL+B,aAAa,WACbC,UAAQ,EACRjC,MAAOb,EAASG,SAChB4C,SAAUrC,EACVU,UAAS,cAAAC,OAAgBf,EAAOH,SAAW,yDAA2D,GAAE,KAAAkB,OACtG3B,EAAQ,aAAe,aAEzBsD,YAAatD,EAAQ,oGAAsB,mBAE7C6B,EAAAA,EAAAA,KAAA,OAAKH,UAAS,sBAAAC,OAAwB3B,EAAQ,cAAgB,eAAc,sBAAqB4B,UAC/FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAGhBd,EAAOH,WACNoB,EAAAA,EAAAA,KAAA,KAAGH,UAAU,8CAA6CE,SACvDhB,EAAOH,eAMdgB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOoB,QAAQ,WAAWvB,UAAU,kEAAiEE,SAClG7B,EAAE,eAEL0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEqB,GAAG,WACHhC,KAAK,WACLE,KAAMN,EAAe,OAAS,WAC9BqC,aAAa,mBACbC,UAAQ,EACRjC,MAAOb,EAASI,SAChB2C,SAAUrC,EACVU,UAAS,cAAAC,OAAgBf,EAAOF,SAAW,yDAA2D,GAAE,KAAAiB,OACtG3B,EAAQ,mBAAqB,mBAE/BsD,YAAatD,EAAQ,gEAAgB,cAEvC6B,EAAAA,EAAAA,KAAA,UACET,KAAK,SACLc,QAASA,IAAMnB,GAAiBD,GAChCY,UAAS,sBAAAC,OAAwB3B,EAAQ,cAAgB,eAAc,iFAAgF4B,UAEvJC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,OAAAC,OAASb,EAAe,eAAiB,iBAGxDF,EAAOF,WACNmB,EAAAA,EAAAA,KAAA,KAAGH,UAAU,8CAA6CE,SACvDhB,EAAOF,eAMde,EAAAA,EAAAA,MAAA,OAAKC,UAAS,qCAAAC,OAAuC3B,EAAQ,mBAAqB,IAAK4B,SAAA,EACrFH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,SACEqB,GAAG,aACHhC,KAAK,aACLE,KAAK,WACLC,QAASf,EAASK,WAClB0C,SAAUrC,EACVU,UAAU,6EAEZG,EAAAA,EAAAA,KAAA,SAAOoB,QAAQ,aAAavB,UAAS,GAAAC,OAAK3B,EAAQ,OAAS,OAAM,mDAAkD4B,SAChH7B,EAAE,oBAGP8B,EAAAA,EAAAA,KAAC0B,EAAAA,GAAI,CACHC,GAAG,mBACH9B,UAAU,oGAAmGE,SAE5G7B,EAAE,wBAKP8B,EAAAA,EAAAA,KAAA,UACET,KAAK,SACLqC,SAAU5D,EACV6B,UAAU,mTAAkTE,SAE3T/B,GACC4B,EAAAA,EAAAA,MAAAiC,EAAAA,SAAA,CAAA9B,SAAA,EACEC,EAAAA,EAAAA,KAAC8B,EAAAA,GAAa,CAACjC,UAAU,SACxB1B,EAAQ,kGAAyB,oBAGpCyB,EAAAA,EAAAA,MAAAiC,EAAAA,SAAA,CAAA9B,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,sBAAAC,OAAwB3B,EAAQ,OAAS,UACpDD,EAAE,eAMT0B,EAAAA,EAAAA,MAAA,UACEL,KAAK,SACLc,QApKM0B,KAChBrD,EAAY,CACVE,SAAU,GACVC,SAAU,GACVC,YAAY,IAEdE,EAAU,CAAC,IA+JDa,UAAU,yUAAwUE,SAAA,EAElVC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,iBAAAC,OAAmB3B,EAAQ,OAAS,UAC/CA,EAAQ,gEAAgB,oBAK7ByB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBE,SAAA,EAC/BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,EAEzDC,EAAAA,EAAAA,KAAA,UACEK,QAASjC,EACTyB,UAAU,qFAAoFE,SAE7F5B,EAAQ,UAAY,gDAIvB6B,EAAAA,EAAAA,KAAA,UACEK,QAAS9B,EACTsB,UAAU,qFAAoFE,UAE9FC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,OAAAC,OAAmB,UAAVxB,EAAoB,UAAY,kBAIzDsB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,gDAA+CE,SAAA,CAAC,wBACxC5B,EAAQ,sGAAwB,mCAO3D6B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,sCAAqCE,UAClDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qEAAoEE,SAAA,EACjFC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0CACfG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4EAA2EE,UACxFH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iCAEfG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,0BAAyBE,SACpC5B,EAAQ,0IAA8B,0BAEzC6B,EAAAA,EAAAA,KAAA,KAAGH,UAAU,0BAAyBE,SACnC5B,EACG,wWACA,0EAGN6B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0CAAyCE,SACrD,CACC,CAAEiC,KAAM,eAAgBC,KAAM9D,EAAQ,sEAAiB,sBACvD,CAAE6D,KAAM,kBAAmBC,KAAM9D,EAAQ,kFAAmB,0BAC5D,CAAE6D,KAAM,sBAAuBC,KAAM9D,EAAQ,kFAAmB,iBAChE,CAAE6D,KAAM,oBAAqBC,KAAM9D,EAAQ,gHAAwB,wBACnE+B,IAAI,CAACgC,EAAS9B,KACdR,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,wCAAuCE,SAAA,EAChEC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKoC,EAAQF,KAAI,0BAC7BhC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qBAAoBE,SAAEmC,EAAQD,SAFtC7B,mB", "sources": ["pages/Auth/Login.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport LoadingSpinner, { ButtonSpinner } from '../../components/UI/LoadingSpinner';\n\nconst Login = () => {\n  const { login, loading } = useAuth();\n  const { t, isRTL, toggleLanguage } = useLanguage();\n  const { theme, toggleTheme } = useTheme();\n  \n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n    rememberMe: false,\n  });\n  \n  const [errors, setErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n\n  // Demo credentials\n  const demoCredentials = [\n    {\n      username: '<EMAIL>',\n      password: 'Admin123!',\n      role: 'Administrator',\n      name: 'Admin User'\n    },\n    {\n      username: '<EMAIL>',\n      password: 'Doctor123!',\n      role: 'Doctor',\n      name: 'Dr. <PERSON> <PERSON>-<PERSON>'\n    },\n    {\n      username: '<EMAIL>',\n      password: 'Therapist123!',\n      role: 'Physical Therapist',\n      name: 'Sarah Al-Zahra'\n    }\n  ];\n\n  // Handle input change\n  const handleChange = (e) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({ ...prev, [name]: '' }));\n    }\n  };\n\n  // Validate form\n  const validateForm = () => {\n    const newErrors = {};\n    \n    if (!formData.username.trim()) {\n      newErrors.username = t('required');\n    } else if (!formData.username.includes('@')) {\n      newErrors.username = t('invalidEmail');\n    }\n    \n    if (!formData.password) {\n      newErrors.password = t('required');\n    } else if (formData.password.length < 6) {\n      newErrors.password = t('passwordTooShort');\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) return;\n    \n    try {\n      await login({\n        username: formData.username,\n        password: formData.password,\n        rememberMe: formData.rememberMe,\n      });\n    } catch (error) {\n      console.error('Login error:', error);\n    }\n  };\n\n  // Fill demo credentials\n  const fillDemoCredentials = (credentials) => {\n    setFormData(prev => ({\n      ...prev,\n      username: credentials.username,\n      password: credentials.password,\n    }));\n  };\n\n  // Clear form\n  const clearForm = () => {\n    setFormData({\n      username: '',\n      password: '',\n      rememberMe: false,\n    });\n    setErrors({});\n  };\n\n  return (\n    <div className={`min-h-screen flex ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Left side - Login Form */}\n      <div className=\"flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:flex-none lg:px-20 xl:px-24\">\n        <div className=\"mx-auto w-full max-w-sm lg:w-96\">\n          {/* Header */}\n          <div className=\"text-center mb-8\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-4\">\n              <i className=\"fas fa-heartbeat text-white text-2xl\"></i>\n            </div>\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {t('login')}\n            </h2>\n            <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n              {isRTL \n                ? 'نظام إدارة العلاج الطبيعي' \n                : 'Physical Therapy Management System'\n              }\n            </p>\n          </div>\n\n          {/* Demo Credentials */}\n          <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800\">\n            <h3 className=\"text-sm font-medium text-blue-900 dark:text-blue-300 mb-2\">\n              {isRTL ? 'بيانات تجريبية:' : 'Demo Credentials:'}\n            </h3>\n            <div className=\"space-y-2\">\n              {demoCredentials.map((cred, index) => (\n                <button\n                  key={index}\n                  onClick={() => fillDemoCredentials(cred)}\n                  className=\"w-full text-left p-2 text-xs bg-white dark:bg-gray-800 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors\"\n                >\n                  <div className=\"font-medium text-blue-900 dark:text-blue-300\">\n                    {cred.name} ({cred.role})\n                  </div>\n                  <div className=\"text-blue-600 dark:text-blue-400\">\n                    {cred.username}\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Login Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Username/Email */}\n            <div>\n              <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('username')}\n              </label>\n              <div className=\"relative\">\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"email\"\n                  autoComplete=\"username\"\n                  required\n                  value={formData.username}\n                  onChange={handleChange}\n                  className={`form-input ${errors.username ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''} ${\n                    isRTL ? 'text-right' : 'text-left'\n                  }`}\n                  placeholder={isRTL ? 'البريد الإلكتروني' : 'Email address'}\n                />\n                <div className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center`}>\n                  <i className=\"fas fa-user text-gray-400\"></i>\n                </div>\n              </div>\n              {errors.username && (\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                  {errors.username}\n                </p>\n              )}\n            </div>\n\n            {/* Password */}\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('password')}\n              </label>\n              <div className=\"relative\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  autoComplete=\"current-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  className={`form-input ${errors.password ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''} ${\n                    isRTL ? 'text-right pr-10' : 'text-left pr-10'\n                  }`}\n                  placeholder={isRTL ? 'كلمة المرور' : 'Password'}\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300`}\n                >\n                  <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>\n                </button>\n              </div>\n              {errors.password && (\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                  {errors.password}\n                </p>\n              )}\n            </div>\n\n            {/* Remember Me & Forgot Password */}\n            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>\n              <div className=\"flex items-center\">\n                <input\n                  id=\"rememberMe\"\n                  name=\"rememberMe\"\n                  type=\"checkbox\"\n                  checked={formData.rememberMe}\n                  onChange={handleChange}\n                  className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"rememberMe\" className={`${isRTL ? 'mr-2' : 'ml-2'} block text-sm text-gray-700 dark:text-gray-300`}>\n                  {t('rememberMe')}\n                </label>\n              </div>\n              <Link\n                to=\"/forgot-password\"\n                className=\"text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300\"\n              >\n                {t('forgotPassword')}\n              </Link>\n            </div>\n\n            {/* Submit Button */}\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              {loading ? (\n                <>\n                  <ButtonSpinner className=\"mr-2\" />\n                  {isRTL ? 'جاري تسجيل الدخول...' : 'Signing in...'}\n                </>\n              ) : (\n                <>\n                  <i className={`fas fa-sign-in-alt ${isRTL ? 'ml-2' : 'mr-2'}`}></i>\n                  {t('login')}\n                </>\n              )}\n            </button>\n\n            {/* Clear Form Button */}\n            <button\n              type=\"button\"\n              onClick={clearForm}\n              className=\"w-full flex justify-center items-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors\"\n            >\n              <i className={`fas fa-eraser ${isRTL ? 'ml-2' : 'mr-2'}`}></i>\n              {isRTL ? 'مسح النموذج' : 'Clear Form'}\n            </button>\n          </form>\n\n          {/* Footer */}\n          <div className=\"mt-8 text-center\">\n            <div className=\"flex items-center justify-center space-x-4\">\n              {/* Language Toggle */}\n              <button\n                onClick={toggleLanguage}\n                className=\"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                {isRTL ? 'English' : 'العربية'}\n              </button>\n              \n              {/* Theme Toggle */}\n              <button\n                onClick={toggleTheme}\n                className=\"text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                <i className={`fas ${theme === 'light' ? 'fa-moon' : 'fa-sun'}`}></i>\n              </button>\n            </div>\n            \n            <p className=\"mt-4 text-xs text-gray-500 dark:text-gray-400\">\n              © 2024 PT System. {isRTL ? 'جميع الحقوق محفوظة.' : 'All rights reserved.'}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Right side - Branding */}\n      <div className=\"hidden lg:block relative w-0 flex-1\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary-600 to-primary-800\">\n          <div className=\"absolute inset-0 bg-black opacity-20\"></div>\n          <div className=\"relative h-full flex flex-col justify-center items-center text-white p-12\">\n            <div className=\"text-center\">\n              <div className=\"w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-8\">\n                <i className=\"fas fa-heartbeat text-4xl\"></i>\n              </div>\n              <h1 className=\"text-4xl font-bold mb-4\">\n                {isRTL ? 'نظام إدارة العلاج الطبيعي' : 'PT Management System'}\n              </h1>\n              <p className=\"text-xl opacity-90 mb-8\">\n                {isRTL \n                  ? 'حلول شاملة لإدارة عيادات العلاج الطبيعي في المملكة العربية السعودية'\n                  : 'Comprehensive solutions for physical therapy clinics in Saudi Arabia'\n                }\n              </p>\n              <div className=\"grid grid-cols-1 gap-4 max-w-md mx-auto\">\n                {[\n                  { icon: 'fas fa-users', text: isRTL ? 'إدارة المرضى' : 'Patient Management' },\n                  { icon: 'fas fa-calendar', text: isRTL ? 'جدولة المواعيد' : 'Appointment Scheduling' },\n                  { icon: 'fas fa-file-medical', text: isRTL ? 'النماذج الطبية' : 'Medical Forms' },\n                  { icon: 'fas fa-chart-line', text: isRTL ? 'التقارير والتحليلات' : 'Reports & Analytics' },\n                ].map((feature, index) => (\n                  <div key={index} className=\"flex items-center space-x-3 text-left\">\n                    <i className={`${feature.icon} text-lg opacity-80`}></i>\n                    <span className=\"text-sm opacity-90\">{feature.text}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "names": ["<PERSON><PERSON>", "login", "loading", "useAuth", "t", "isRTL", "toggleLanguage", "useLanguage", "theme", "toggleTheme", "useTheme", "formData", "setFormData", "useState", "username", "password", "rememberMe", "errors", "setErrors", "showPassword", "setShowPassword", "handleChange", "e", "name", "value", "type", "checked", "target", "prev", "_objectSpread", "_jsxs", "className", "concat", "children", "_jsx", "role", "map", "cred", "index", "onClick", "fillDemoCredentials", "credentials", "onSubmit", "async", "preventDefault", "validateForm", "newErrors", "trim", "includes", "length", "Object", "keys", "error", "console", "htmlFor", "id", "autoComplete", "required", "onChange", "placeholder", "Link", "to", "disabled", "_Fragment", "<PERSON>ton<PERSON><PERSON>ner", "clearForm", "icon", "text", "feature"], "sourceRoot": ""}