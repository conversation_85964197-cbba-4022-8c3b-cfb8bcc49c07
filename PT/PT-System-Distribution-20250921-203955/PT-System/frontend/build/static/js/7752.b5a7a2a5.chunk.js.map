{"version": 3, "file": "static/js/7752.b5a7a2a5.chunk.js", "mappings": "iOAMA,MAi5BA,EAj5B8BA,KAC5B,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACX,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MAEdC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAQC,IAAaF,EAAAA,EAAAA,WAAS,IAC9BG,EAASC,IAAcJ,EAAAA,EAAAA,UAAS,OAChCK,EAAUC,IAAeN,EAAAA,EAAAA,UAAS,CAEvCO,UAAW,GACXC,SAAU,GACVC,YAAa,GACbC,WAAY,GACZC,YAAa,GACbC,OAAQ,GACRC,YAAa,GACbC,WAAY,GACZC,MAAO,GACPC,MAAO,GAGPC,QAAS,CACPC,OAAQ,GACRC,KAAM,GACNC,OAAQ,GACRC,WAAY,GACZC,QAAS,gBAIXC,iBAAkB,CAChBC,KAAM,GACNC,aAAc,GACdV,MAAO,GACPC,MAAO,IAITU,UAAW,CACTC,SAAU,GACVC,aAAc,GACdC,YAAa,GACbC,WAAY,GACZC,YAAa,GACbC,WAAY,GACZC,cAAe,GACfC,UAAU,GAIZC,eAAgB,CACdC,UAAW,GACXC,mBAAoB,GACpBC,cAAe,GACfC,kBAAmB,GACnBC,cAAe,GACfC,iBAAkB,GAClBC,mBAAoB,GACpBC,mBAAoB,GACpBC,MAAO,IAITC,aAAc,CACZC,iBAAiB,EACjBC,MAAO,GACPC,SAAU,OACVC,oBAAqB,SACrBC,gBAAiB,GACjBC,mBAAoB,CAClBC,SAAU,UACVC,MAAO,WACPC,YAAa,oBAEfC,kBAAmB,GACnBC,sBAAuB,OAI3BC,EAAAA,EAAAA,WAAU,KACgBC,WACtB3D,GAAW,GACX,IAAK,IAAD4D,EAAAC,EACF,IAAIC,EAAc,KAGlB,GAAkB,QAAdF,EAAAlE,EAASqE,aAAK,IAAAH,GAAdA,EAAgBxD,SAAyB,QAAlByD,EAAInE,EAASqE,aAAK,IAAAF,GAAdA,EAAgBG,mBAC7CF,EAAcpE,EAASqE,MAAM3D,aACxB,GAAId,EAAI,CAEb,MAAM2E,QAAiBC,EAAAA,GAAIC,IAAI,aAADC,OAAc9E,IACxC2E,EAASI,UACXP,EAAcG,EAASK,KAE3B,CAEkB,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAjB,GAAI/C,EACFzD,EAAWyD,GAEXvD,EAAY,CACVC,UAAWsD,EAAYtD,WAAa,GACpCC,SAAUqD,EAAYrD,UAAY,GAClCC,YAAaoD,EAAYpD,aAAe,GACxCC,WAAYmD,EAAYnD,YAAc,GACtCC,YAAakD,EAAYlD,YAAckD,EAAYlD,YAAYkG,MAAM,KAAK,GAAK,GAC/EjG,OAAQiD,EAAYjD,QAAU,GAC9BC,YAAagD,EAAYhD,aAAe,GACxCC,WAAY+C,EAAY/C,YAAc,GACtCC,MAAO8C,EAAY9C,OAAS,GAC5BC,MAAO6C,EAAY7C,OAAS,GAC5BC,QAAS,CACPC,QAA2B,QAAnBoD,EAAAT,EAAY5C,eAAO,IAAAqD,OAAA,EAAnBA,EAAqBpD,SAAU,GACvCC,MAAyB,QAAnBoD,EAAAV,EAAY5C,eAAO,IAAAsD,OAAA,EAAnBA,EAAqBpD,OAAQ,GACnCC,QAA2B,QAAnBoD,EAAAX,EAAY5C,eAAO,IAAAuD,OAAA,EAAnBA,EAAqBpD,SAAU,GACvCC,YAA+B,QAAnBoD,EAAAZ,EAAY5C,eAAO,IAAAwD,OAAA,EAAnBA,EAAqBpD,aAAc,GAC/CC,SAA4B,QAAnBoD,EAAAb,EAAY5C,eAAO,IAAAyD,OAAA,EAAnBA,EAAqBpD,UAAW,gBAE3CC,iBAAkB,CAChBC,MAAkC,QAA5BmD,EAAAd,EAAYtC,wBAAgB,IAAAoD,OAAA,EAA5BA,EAA8BnD,OAAQ,GAC5CC,cAA0C,QAA5BmD,EAAAf,EAAYtC,wBAAgB,IAAAqD,OAAA,EAA5BA,EAA8BnD,eAAgB,GAC5DV,OAAmC,QAA5B8D,EAAAhB,EAAYtC,wBAAgB,IAAAsD,OAAA,EAA5BA,EAA8B9D,QAAS,GAC9CC,OAAmC,QAA5B8D,EAAAjB,EAAYtC,wBAAgB,IAAAuD,OAAA,EAA5BA,EAA8B9D,QAAS,IAEhDU,UAAW,CACTC,UAA+B,QAArBoD,EAAAlB,EAAYnC,iBAAS,IAAAqD,OAAA,EAArBA,EAAuBpD,WAAY,GAC7CC,cAAmC,QAArBoD,EAAAnB,EAAYnC,iBAAS,IAAAsD,OAAA,EAArBA,EAAuBpD,eAAgB,GACrDC,aAAkC,QAArBoD,EAAApB,EAAYnC,iBAAS,IAAAuD,OAAA,EAArBA,EAAuBpD,cAAe,GACnDC,WAAiC,QAArBoD,EAAArB,EAAYnC,iBAAS,IAAAwD,GAArBA,EAAuBpD,WAAa+B,EAAYnC,UAAUI,WAAW+E,MAAM,KAAK,GAAK,GACjG9E,aAAkC,QAArBoD,EAAAtB,EAAYnC,iBAAS,IAAAyD,OAAA,EAArBA,EAAuBpD,cAAe,GACnDC,YAAiC,QAArBoD,EAAAvB,EAAYnC,iBAAS,IAAA0D,OAAA,EAArBA,EAAuBpD,aAAc,GACjDC,eAAoC,QAArBoD,EAAAxB,EAAYnC,iBAAS,IAAA2D,OAAA,EAArBA,EAAuBpD,gBAAiB,GACvDC,UAA8C,KAAf,QAArBoD,EAAAzB,EAAYnC,iBAAS,IAAA4D,OAAA,EAArBA,EAAuBpD,WAEnCC,eAAgB,CACdC,WAAqC,QAA1BmD,EAAA1B,EAAY1B,sBAAc,IAAAoD,OAAA,EAA1BA,EAA4BnD,YAAa,GACpDC,oBAA8C,QAA1BmD,EAAA3B,EAAY1B,sBAAc,IAAAqD,OAAA,EAA1BA,EAA4BnD,qBAAsB,GACtEC,eAAyC,QAA1BmD,EAAA5B,EAAY1B,sBAAc,IAAAsD,OAAA,EAA1BA,EAA4BnD,gBAAiB,GAC5DC,mBAA6C,QAA1BmD,EAAA7B,EAAY1B,sBAAc,IAAAuD,OAAA,EAA1BA,EAA4BnD,oBAAqB,GACpEC,eAAyC,QAA1BmD,EAAA9B,EAAY1B,sBAAc,IAAAwD,OAAA,EAA1BA,EAA4BnD,gBAAiB,GAC5DC,kBAA4C,QAA1BmD,EAAA/B,EAAY1B,sBAAc,IAAAyD,OAAA,EAA1BA,EAA4BnD,mBAAoB,GAClEC,oBAA8C,QAA1BmD,EAAAhC,EAAY1B,sBAAc,IAAA0D,OAAA,EAA1BA,EAA4BnD,qBAAsB,GACtEC,oBAA8C,QAA1BmD,EAAAjC,EAAY1B,sBAAc,IAAA2D,OAAA,EAA1BA,EAA4BnD,qBAAsB,GACtEC,OAAiC,QAA1BmD,EAAAlC,EAAY1B,sBAAc,IAAA4D,OAAA,EAA1BA,EAA4BnD,QAAS,IAE9CC,aAAc,CACZC,iBAAyC,QAAxBkD,EAAAnC,EAAYhB,oBAAY,IAAAmD,OAAA,EAAxBA,EAA0BlD,mBAAmB,EAC9DC,OAA+B,QAAxBkD,EAAApC,EAAYhB,oBAAY,IAAAoD,OAAA,EAAxBA,EAA0BlD,QAAS,GAC1CC,UAAkC,QAAxBkD,EAAArC,EAAYhB,oBAAY,IAAAqD,OAAA,EAAxBA,EAA0BlD,WAAY,OAChDC,qBAA6C,QAAxBkD,EAAAtC,EAAYhB,oBAAY,IAAAsD,OAAA,EAAxBA,EAA0BlD,sBAAuB,SACtEC,iBAAyC,QAAxBkD,EAAAvC,EAAYhB,oBAAY,IAAAuD,OAAA,EAAxBA,EAA0BlD,kBAAmB,GAC9DC,mBAAoB,CAClBC,UAAkC,QAAxBiD,EAAAxC,EAAYhB,oBAAY,IAAAwD,GAAoB,QAApBC,EAAxBD,EAA0BlD,0BAAkB,IAAAmD,OAApB,EAAxBA,EAA8ClD,WAAY,UACpEC,OAA+B,QAAxBkD,EAAA1C,EAAYhB,oBAAY,IAAA0D,GAAoB,QAApBC,EAAxBD,EAA0BpD,0BAAkB,IAAAqD,OAApB,EAAxBA,EAA8CnD,QAAS,WAC9DC,aAAqC,QAAxBmD,EAAA5C,EAAYhB,oBAAY,IAAA4D,GAAoB,QAApBC,EAAxBD,EAA0BtD,0BAAkB,IAAAuD,OAApB,EAAxBA,EAA8CpD,cAAe,oBAE5EC,mBAA2C,QAAxBoD,EAAA9C,EAAYhB,oBAAY,IAAA8D,OAAA,EAAxBA,EAA0BpD,oBAAqB,GAClEC,uBAA+C,QAAxBoD,EAAA/C,EAAYhB,oBAAY,IAAA+D,OAAA,EAAxBA,EAA0BpD,wBAAyB,KAIlF,CAAE,MAAOsD,GACPC,QAAQD,MAAM,8BAA+BA,GAC7CE,EAAAA,GAAMF,MAAMnH,EAAE,sBAAuB,8BACvC,CAAC,QACCI,GAAW,EACb,GAGFkH,IACC,CAAC5H,EAAII,EAASqE,MAAOnE,IAExB,MAAMuH,EAAoBA,CAACC,EAASC,EAAOC,KAEvC/G,EADE6G,EACUG,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACH,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKH,IAAQ,IAChB,CAACC,GAAQC,MAIDC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACF,GAAQC,MAKTG,EAAoBA,CAACL,EAASC,EAAOK,EAAOJ,KAChD/G,EAAYgH,IACV,MAAMI,EAAW,IAAIJ,EAAKH,GAASC,IAEnC,OADAM,EAASD,GAASJ,GAClBE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKD,GAAI,IACP,CAACH,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKH,IAAQ,IAChB,CAACC,GAAQM,SAMXC,EAAeA,CAACR,EAASC,KAC7B9G,EAAYgH,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACH,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKH,IAAQ,IAChB,CAACC,GAAQ,IAAIE,EAAKH,GAASC,GAAQ,UAKnCQ,EAAkBA,CAACT,EAASC,EAAOK,KACvCnH,EAAYgH,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACH,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKH,IAAQ,IAChB,CAACC,GAAQE,EAAKH,GAASC,GAAOS,OAAO,CAACC,EAAGC,IAAMA,IAAMN,SAuB3D,OAAI3H,GAEAkI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qEAKhB9H,GAmBH6H,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EAErDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7DvI,EAAE,cAAe,mBAEpBqI,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5CtI,EAAQO,EAAQqB,KAAOrB,EAAQiI,QAAM,GAAAjE,OAAOhE,EAAQI,UAAS,KAAA4D,OAAIhE,EAAQK,gBAG9E2H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEE,QAASA,IAAM9I,EAAS,aAAD4E,OAAchE,EAAQmI,KAAOnI,EAAQd,KAC5D4I,UAAU,kFAAiFC,SAAA,EAE3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZtI,EAAE,SAAU,cAEfwI,EAAAA,EAAAA,MAAA,UACEE,QAnEK3E,UACjBxD,GAAU,GACV,IACE,MAAM8D,QAAiBC,EAAAA,GAAIsE,IAAI,aAADpE,OAAchE,EAAQmI,KAAOnI,EAAQd,IAAMgB,GACzE,IAAI2D,EAASI,QAIX,MAAM,IAAIoE,MAAMxE,EAASyE,SAAW,4BAHpCzB,EAAAA,GAAM5C,QAAQzE,EAAE,iBAAkB,6CAClCJ,EAAS,aAAD4E,OAAchE,EAAQmI,KAAOnI,EAAQd,IAIjD,CAAE,MAAOyH,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAMF,MAAMnH,EAAE,uBAAwB,sCACxC,CAAC,QACCO,GAAU,EACZ,GAqDYwI,SAAUzI,EACVgI,UAAU,sGAAqGC,SAAA,EAE/GF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,OAAA9D,OAASlE,EAAS,qBAAuB,UAAS,WAC7DA,EAASN,EAAE,SAAU,aAAeA,EAAE,cAAe,4BAO9DwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDACZtI,EAAE,mBAAoB,yBAEzBwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,YAAa,iBAElBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASE,UAChBqI,SAAWC,GAAM3B,EAAkB,KAAM,YAAa2B,EAAEC,OAAOzB,OAC/DY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,WAAY,gBAEjBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASG,SAChBoI,SAAWC,GAAM3B,EAAkB,KAAM,WAAY2B,EAAEC,OAAOzB,OAC9DY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,cAAe,oBAEpBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASM,YAChBiI,SAAWC,GAAM3B,EAAkB,KAAM,cAAe2B,EAAEC,OAAOzB,OACjEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,SAAU,aAEfwI,EAAAA,EAAAA,MAAA,UACEd,MAAOhH,EAASO,OAChBgI,SAAWC,GAAM3B,EAAkB,KAAM,SAAU2B,EAAEC,OAAOzB,OAC5DY,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQX,MAAM,GAAEa,SAAEvI,EAAE,eAAgB,oBACpCqI,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMa,SAAEvI,EAAE,OAAQ,WAChCqI,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQa,SAAEvI,EAAE,SAAU,mBAGxCwI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,cAAe,kBAEpBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASQ,YAChB+H,SAAWC,GAAM3B,EAAkB,KAAM,cAAe2B,EAAEC,OAAOzB,OACjEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,aAAc,kBAEnBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASS,WAChB8H,SAAWC,GAAM3B,EAAkB,KAAM,aAAc2B,EAAEC,OAAOzB,OAChEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,QAAS,YAEdqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,MACLtB,MAAOhH,EAASU,MAChB6H,SAAWC,GAAM3B,EAAkB,KAAM,QAAS2B,EAAEC,OAAOzB,OAC3DY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,QAAS,YAEdqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,QACLtB,MAAOhH,EAASW,MAChB4H,SAAWC,GAAM3B,EAAkB,KAAM,QAAS2B,EAAEC,OAAOzB,OAC3DY,UAAU,wJAOlBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qDACZtI,EAAE,mBAAoB,yBAEzBwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,OAAQ,WAEbqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASkB,iBAAiBC,KACjCoH,SAAWC,GAAM3B,EAAkB,mBAAoB,OAAQ2B,EAAEC,OAAOzB,OACxEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,eAAgB,mBAErBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASkB,iBAAiBE,aACjCmH,SAAWC,GAAM3B,EAAkB,mBAAoB,eAAgB2B,EAAEC,OAAOzB,OAChFY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,QAAS,YAEdqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,MACLtB,MAAOhH,EAASkB,iBAAiBR,MACjC6H,SAAWC,GAAM3B,EAAkB,mBAAoB,QAAS2B,EAAEC,OAAOzB,OACzEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,QAAS,YAEdqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,QACLtB,MAAOhH,EAASkB,iBAAiBP,MACjC4H,SAAWC,GAAM3B,EAAkB,mBAAoB,QAAS2B,EAAEC,OAAOzB,OACzEY,UAAU,2JAQpBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EAEzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZtI,EAAE,uBAAwB,6BAE7BwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,WAAY,eAEjBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASqB,UAAUC,SAC1BiH,SAAWC,GAAM3B,EAAkB,YAAa,WAAY2B,EAAEC,OAAOzB,OACrEY,UAAU,4IACVc,YAAY,oCAGhBZ,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,eAAgB,oBAErBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASqB,UAAUE,aAC1BgH,SAAWC,GAAM3B,EAAkB,YAAa,eAAgB2B,EAAEC,OAAOzB,OACzEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,cAAe,mBAEpBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASqB,UAAUG,YAC1B+G,SAAWC,GAAM3B,EAAkB,YAAa,cAAe2B,EAAEC,OAAOzB,OACxEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,aAAc,kBAEnBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAASqB,UAAUI,WAC1B8G,SAAWC,GAAM3B,EAAkB,YAAa,aAAc2B,EAAEC,OAAOzB,OACvEY,UAAU,kJAGdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,cAAe,mBAEpBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,SACLtB,MAAOhH,EAASqB,UAAUK,YAC1B6G,SAAWC,GAAM3B,EAAkB,YAAa,cAAe2B,EAAEC,OAAOzB,OACxEY,UAAU,4IACVc,YAAY,UAGhBZ,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,aAAc,iBAEnBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,SACLtB,MAAOhH,EAASqB,UAAUM,WAC1B4G,SAAWC,GAAM3B,EAAkB,YAAa,aAAc2B,EAAEC,OAAOzB,OACvEY,UAAU,4IACVc,YAAY,UAGhBf,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEW,KAAK,WACLK,QAAS3I,EAASqB,UAAUQ,SAC5B0G,SAAWC,GAAM3B,EAAkB,YAAa,WAAY2B,EAAEC,OAAOE,SACrEf,UAAU,gIAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5DvI,EAAE,kBAAmB,qCAQhCwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZtI,EAAE,iBAAkB,uBAEvBwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,mBAAoB,wBAEzBqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAAS8B,eAAeM,iBAC/BmG,SAAWC,GAAM3B,EAAkB,iBAAkB,mBAAoB2B,EAAEC,OAAOzB,OAClFY,UAAU,4IACVc,YAAY,2CAGhBZ,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,qBAAsB,0BAE3BqI,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOhH,EAAS8B,eAAeQ,mBAC/BiG,SAAWC,GAAM3B,EAAkB,iBAAkB,qBAAsB2B,EAAEC,OAAOzB,OACpFY,UAAU,4IACVc,YAAY,iBAKhBZ,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,YAAa,gBAElBwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB7H,EAAS8B,eAAeC,UAAU6G,IAAI,CAACC,EAASzB,KAC/CU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAO6B,EACPN,SAAWC,GAAMrB,EAAkB,iBAAkB,YAAaC,EAAOoB,EAAEC,OAAOzB,OAClFY,UAAU,4IACVc,YAAY,mCAEdf,EAAAA,EAAAA,KAAA,UACEW,KAAK,SACLN,QAASA,IAAMT,EAAgB,iBAAkB,YAAaH,GAC9DQ,UAAU,8DAA6DC,UAEvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAbPR,KAiBZU,EAAAA,EAAAA,MAAA,UACEQ,KAAK,SACLN,QAASA,IAAMV,EAAa,iBAAkB,aAC9CM,UAAU,kEAAiEC,SAAA,EAE3EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZtI,EAAE,aAAc,yBAMvBwI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,qBAAsB,0BAE3BwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB7H,EAAS8B,eAAeE,mBAAmB4G,IAAI,CAACE,EAAY1B,KAC3DU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAO8B,EACPP,SAAWC,GAAMrB,EAAkB,iBAAkB,qBAAsBC,EAAOoB,EAAEC,OAAOzB,OAC3FY,UAAU,4IACVc,YAAY,qCAEdf,EAAAA,EAAAA,KAAA,UACEW,KAAK,SACLN,QAASA,IAAMT,EAAgB,iBAAkB,qBAAsBH,GACvEQ,UAAU,8DAA6DC,UAEvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAbPR,KAiBZU,EAAAA,EAAAA,MAAA,UACEQ,KAAK,SACLN,QAASA,IAAMV,EAAa,iBAAkB,sBAC9CM,UAAU,kEAAiEC,SAAA,EAE3EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZtI,EAAE,gBAAiB,qCAShCwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EAEzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uDACZtI,EAAE,eAAgB,qBAErBwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,OAAAE,UACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEW,KAAK,WACLK,QAAS3I,EAASwC,aAAaC,gBAC/B8F,SAAWC,GAAM3B,EAAkB,eAAgB,kBAAmB2B,EAAEC,OAAOE,SAC/Ef,UAAU,gIAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5DvI,EAAE,kBAAmB,oCAK3BU,EAASwC,aAAaC,kBACrBqF,EAAAA,EAAAA,MAAAiB,EAAAA,SAAA,CAAAlB,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,WAAY,eAEjBwI,EAAAA,EAAAA,MAAA,UACEd,MAAOhH,EAASwC,aAAaG,SAC7B4F,SAAWC,GAAM3B,EAAkB,eAAgB,WAAY2B,EAAEC,OAAOzB,OACxEY,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQX,MAAM,OAAMa,SAAEvI,EAAE,OAAQ,WAChCqI,EAAAA,EAAAA,KAAA,UAAQX,MAAM,WAAUa,SAAEvI,EAAE,WAAY,eACxCqI,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQa,SAAEvI,EAAE,SAAU,mBAIxCwI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,sBAAuB,2BAE5BwI,EAAAA,EAAAA,MAAA,UACEd,MAAOhH,EAASwC,aAAaI,oBAC7B2F,SAAWC,GAAM3B,EAAkB,eAAgB,sBAAuB2B,EAAEC,OAAOzB,OACnFY,UAAU,4IAA2IC,SAAA,EAErJF,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQa,SAAEvI,EAAE,SAAU,aACpCqI,EAAAA,EAAAA,KAAA,UAAQX,MAAM,gBAAea,SAAEvI,EAAE,eAAgB,oBACjDqI,EAAAA,EAAAA,KAAA,UAAQX,MAAM,cAAaa,SAAEvI,EAAE,aAAc,kBAC7CqI,EAAAA,EAAAA,KAAA,UAAQX,MAAM,uBAAsBa,SAAEvI,EAAE,sBAAuB,iCAInEwI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,kBAAmB,uBAExBqI,EAAAA,EAAAA,KAAA,YACEX,MAAOhH,EAASwC,aAAaK,gBAC7B0F,SAAWC,GAAM3B,EAAkB,eAAgB,kBAAmB2B,EAAEC,OAAOzB,OAC/EgC,KAAM,EACNpB,UAAU,4IACVc,YAAY,6DAIhBZ,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,wBAAyB,6BAE9BqI,EAAAA,EAAAA,KAAA,YACEX,MAAOhH,EAASwC,aAAaW,sBAC7BoF,SAAWC,GAAM3B,EAAkB,eAAgB,wBAAyB2B,EAAEC,OAAOzB,OACrFgC,KAAM,EACNpB,UAAU,4IACVc,YAAY,+CAKhBZ,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,oBAAqB,yBAE1BwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB7H,EAASwC,aAAaU,kBAAkB0F,IAAI,CAACK,EAAW7B,KACvDU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOiC,EACPV,SAAWC,GAAMrB,EAAkB,eAAgB,oBAAqBC,EAAOoB,EAAEC,OAAOzB,OACxFY,UAAU,4IACVc,YAAY,oDAEdf,EAAAA,EAAAA,KAAA,UACEW,KAAK,SACLN,QAASA,IAAMT,EAAgB,eAAgB,oBAAqBH,GACpEQ,UAAU,8DAA6DC,UAEvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAbPR,KAiBZU,EAAAA,EAAAA,MAAA,UACEQ,KAAK,SACLN,QAASA,IAAMV,EAAa,eAAgB,qBAC5CM,UAAU,kEAAiEC,SAAA,EAE3EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZtI,EAAE,eAAgB,oCAUjCwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZtI,EAAE,wBAAyB,sCAE9BwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,gBAAiB,qBAEtBwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB7H,EAAS8B,eAAeG,cAAc2G,IAAI,CAACM,EAAS9B,KACnDU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOkC,EACPX,SAAWC,GAAMrB,EAAkB,iBAAkB,gBAAiBC,EAAOoB,EAAEC,OAAOzB,OACtFY,UAAU,4IACVc,YAAY,uDAEdf,EAAAA,EAAAA,KAAA,UACEW,KAAK,SACLN,QAASA,IAAMT,EAAgB,iBAAkB,gBAAiBH,GAClEQ,UAAU,8DAA6DC,UAEvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAbPR,KAiBZU,EAAAA,EAAAA,MAAA,UACEQ,KAAK,SACLN,QAASA,IAAMV,EAAa,iBAAkB,iBAC9CM,UAAU,kEAAiEC,SAAA,EAE3EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZtI,EAAE,aAAc,yBAMvBwI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,oBAAqB,yBAE1BwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB7H,EAAS8B,eAAeI,kBAAkB0G,IAAI,CAACO,EAAW/B,KACzDU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOmC,EACPZ,SAAWC,GAAMrB,EAAkB,iBAAkB,oBAAqBC,EAAOoB,EAAEC,OAAOzB,OAC1FY,UAAU,4IACVc,YAAY,6CAEdf,EAAAA,EAAAA,KAAA,UACEW,KAAK,SACLN,QAASA,IAAMT,EAAgB,iBAAkB,oBAAqBH,GACtEQ,UAAU,8DAA6DC,UAEvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAbPR,KAiBZU,EAAAA,EAAAA,MAAA,UACEQ,KAAK,SACLN,QAASA,IAAMV,EAAa,iBAAkB,qBAC9CM,UAAU,kEAAiEC,SAAA,EAE3EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZtI,EAAE,eAAgB,2BAMzBwI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,gBAAiB,qBAEtBwI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvB7H,EAAS8B,eAAeK,cAAcyG,IAAI,CAACQ,EAAShC,KACnDU,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,8BAA6BC,SAAA,EACtDF,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOoC,EACPb,SAAWC,GAAMrB,EAAkB,iBAAkB,gBAAiBC,EAAOoB,EAAEC,OAAOzB,OACtFY,UAAU,4IACVc,YAAY,qDAEdf,EAAAA,EAAAA,KAAA,UACEW,KAAK,SACLN,QAASA,IAAMT,EAAgB,iBAAkB,gBAAiBH,GAClEQ,UAAU,8DAA6DC,UAEvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBAbPR,KAiBZU,EAAAA,EAAAA,MAAA,UACEQ,KAAK,SACLN,QAASA,IAAMV,EAAa,iBAAkB,iBAC9CM,UAAU,kEAAiEC,SAAA,EAE3EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZtI,EAAE,mBAAoB,gCAM7BwI,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EvI,EAAE,eAAgB,oBAErBqI,EAAAA,EAAAA,KAAA,YACEX,MAAOhH,EAAS8B,eAAeS,MAC/BgG,SAAWC,GAAM3B,EAAkB,iBAAkB,QAAS2B,EAAEC,OAAOzB,OACvEgC,KAAM,EACNpB,UAAU,4IACVc,YAAY,0FA1oBxBf,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEvI,EAAE,kBAAmB,wBAExBqI,EAAAA,EAAAA,KAAA,UACEK,QAASA,IAAM9I,EAAS,aACxB0I,UAAU,gEAA+DC,SAExEvI,EAAE,iBAAkB,2B", "sources": ["pages/Patients/PatientEditSinglePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate, useLocation } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { apiHelpers as api } from '../../services/api';\nimport toast from 'react-hot-toast';\n\nconst PatientEditSinglePage = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { t, isRTL } = useLanguage();\n  \n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [patient, setPatient] = useState(null);\n  const [formData, setFormData] = useState({\n    // Basic Information\n    firstName: '',\n    lastName: '',\n    firstNameAr: '',\n    lastNameAr: '',\n    dateOfBirth: '',\n    gender: '',\n    nationality: '',\n    nationalId: '',\n    phone: '',\n    email: '',\n    \n    // Address\n    address: {\n      street: '',\n      city: '',\n      region: '',\n      postalCode: '',\n      country: 'Saudi Arabia'\n    },\n    \n    // Emergency Contact\n    emergencyContact: {\n      name: '',\n      relationship: '',\n      phone: '',\n      email: ''\n    },\n    \n    // Insurance\n    insurance: {\n      provider: '',\n      policyNumber: '',\n      groupNumber: '',\n      expiryDate: '',\n      copayAmount: '',\n      deductible: '',\n      coverageLimit: '',\n      isActive: true\n    },\n    \n    // Medical History\n    medicalHistory: {\n      allergies: [],\n      currentMedications: [],\n      pastSurgeries: [],\n      chronicConditions: [],\n      familyHistory: [],\n      primaryDiagnosis: '',\n      secondaryDiagnoses: [],\n      referringPhysician: '',\n      notes: ''\n    },\n    \n    // Special Needs\n    specialNeeds: {\n      hasSpecialNeeds: false,\n      types: [],\n      severity: 'mild',\n      communicationMethod: 'verbal',\n      behavioralNotes: '',\n      sensoryPreferences: {\n        lighting: 'natural',\n        sound: 'moderate',\n        temperature: 'room_temperature'\n      },\n      adaptiveEquipment: [],\n      caregiverInstructions: ''\n    }\n  });\n\n  useEffect(() => {\n    const loadPatientData = async () => {\n      setLoading(true);\n      try {\n        let patientData = null;\n        \n        // Check if patient data was passed via state\n        if (location.state?.patient && location.state?.fromPatientProfile) {\n          patientData = location.state.patient;\n        } else if (id) {\n          // Fetch from API\n          const response = await api.get(`/patients/${id}`);\n          if (response.success) {\n            patientData = response.data;\n          }\n        }\n        \n        if (patientData) {\n          setPatient(patientData);\n          // Populate form with existing data\n          setFormData({\n            firstName: patientData.firstName || '',\n            lastName: patientData.lastName || '',\n            firstNameAr: patientData.firstNameAr || '',\n            lastNameAr: patientData.lastNameAr || '',\n            dateOfBirth: patientData.dateOfBirth ? patientData.dateOfBirth.split('T')[0] : '',\n            gender: patientData.gender || '',\n            nationality: patientData.nationality || '',\n            nationalId: patientData.nationalId || '',\n            phone: patientData.phone || '',\n            email: patientData.email || '',\n            address: {\n              street: patientData.address?.street || '',\n              city: patientData.address?.city || '',\n              region: patientData.address?.region || '',\n              postalCode: patientData.address?.postalCode || '',\n              country: patientData.address?.country || 'Saudi Arabia'\n            },\n            emergencyContact: {\n              name: patientData.emergencyContact?.name || '',\n              relationship: patientData.emergencyContact?.relationship || '',\n              phone: patientData.emergencyContact?.phone || '',\n              email: patientData.emergencyContact?.email || ''\n            },\n            insurance: {\n              provider: patientData.insurance?.provider || '',\n              policyNumber: patientData.insurance?.policyNumber || '',\n              groupNumber: patientData.insurance?.groupNumber || '',\n              expiryDate: patientData.insurance?.expiryDate ? patientData.insurance.expiryDate.split('T')[0] : '',\n              copayAmount: patientData.insurance?.copayAmount || '',\n              deductible: patientData.insurance?.deductible || '',\n              coverageLimit: patientData.insurance?.coverageLimit || '',\n              isActive: patientData.insurance?.isActive !== false\n            },\n            medicalHistory: {\n              allergies: patientData.medicalHistory?.allergies || [],\n              currentMedications: patientData.medicalHistory?.currentMedications || [],\n              pastSurgeries: patientData.medicalHistory?.pastSurgeries || [],\n              chronicConditions: patientData.medicalHistory?.chronicConditions || [],\n              familyHistory: patientData.medicalHistory?.familyHistory || [],\n              primaryDiagnosis: patientData.medicalHistory?.primaryDiagnosis || '',\n              secondaryDiagnoses: patientData.medicalHistory?.secondaryDiagnoses || [],\n              referringPhysician: patientData.medicalHistory?.referringPhysician || '',\n              notes: patientData.medicalHistory?.notes || ''\n            },\n            specialNeeds: {\n              hasSpecialNeeds: patientData.specialNeeds?.hasSpecialNeeds || false,\n              types: patientData.specialNeeds?.types || [],\n              severity: patientData.specialNeeds?.severity || 'mild',\n              communicationMethod: patientData.specialNeeds?.communicationMethod || 'verbal',\n              behavioralNotes: patientData.specialNeeds?.behavioralNotes || '',\n              sensoryPreferences: {\n                lighting: patientData.specialNeeds?.sensoryPreferences?.lighting || 'natural',\n                sound: patientData.specialNeeds?.sensoryPreferences?.sound || 'moderate',\n                temperature: patientData.specialNeeds?.sensoryPreferences?.temperature || 'room_temperature'\n              },\n              adaptiveEquipment: patientData.specialNeeds?.adaptiveEquipment || [],\n              caregiverInstructions: patientData.specialNeeds?.caregiverInstructions || ''\n            }\n          });\n        }\n      } catch (error) {\n        console.error('Error loading patient data:', error);\n        toast.error(t('errorLoadingPatient', 'Error loading patient data'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadPatientData();\n  }, [id, location.state, t]);\n\n  const handleInputChange = (section, field, value) => {\n    if (section) {\n      setFormData(prev => ({\n        ...prev,\n        [section]: {\n          ...prev[section],\n          [field]: value\n        }\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        [field]: value\n      }));\n    }\n  };\n\n  const handleArrayChange = (section, field, index, value) => {\n    setFormData(prev => {\n      const newArray = [...prev[section][field]];\n      newArray[index] = value;\n      return {\n        ...prev,\n        [section]: {\n          ...prev[section],\n          [field]: newArray\n        }\n      };\n    });\n  };\n\n  const addArrayItem = (section, field) => {\n    setFormData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: [...prev[section][field], '']\n      }\n    }));\n  };\n\n  const removeArrayItem = (section, field, index) => {\n    setFormData(prev => ({\n      ...prev,\n      [section]: {\n        ...prev[section],\n        [field]: prev[section][field].filter((_, i) => i !== index)\n      }\n    }));\n  };\n\n  const handleSave = async () => {\n    setSaving(true);\n    try {\n      const response = await api.put(`/patients/${patient._id || patient.id}`, formData);\n      if (response.success) {\n        toast.success(t('patientUpdated', 'Patient information updated successfully'));\n        navigate(`/patients/${patient._id || patient.id}`);\n      } else {\n        throw new Error(response.message || 'Failed to update patient');\n      }\n    } catch (error) {\n      console.error('Error updating patient:', error);\n      toast.error(t('errorUpdatingPatient', 'Error updating patient information'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!patient) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            {t('patientNotFound', 'Patient not found')}\n          </h2>\n          <button\n            onClick={() => navigate('/patients')}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700\"\n          >\n            {t('backToPatients', 'Back to Patients')}\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-6\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"mb-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('editPatient', 'Edit Patient')}\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                {isRTL ? patient.name : patient.nameEn || `${patient.firstName} ${patient.lastName}`}\n              </p>\n            </div>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => navigate(`/patients/${patient._id || patient.id}`)}\n                className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n              >\n                <i className=\"fas fa-times mr-2\"></i>\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                onClick={handleSave}\n                disabled={saving}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n              >\n                <i className={`fas ${saving ? 'fa-spinner fa-spin' : 'fa-save'} mr-2`}></i>\n                {saving ? t('saving', 'Saving...') : t('saveChanges', 'Save Changes')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Form Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Basic Information */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-user text-blue-600 dark:text-blue-400 mr-2\"></i>\n              {t('basicInformation', 'Basic Information')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('firstName', 'First Name')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.firstName}\n                  onChange={(e) => handleInputChange(null, 'firstName', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('lastName', 'Last Name')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.lastName}\n                  onChange={(e) => handleInputChange(null, 'lastName', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('dateOfBirth', 'Date of Birth')}\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.dateOfBirth}\n                  onChange={(e) => handleInputChange(null, 'dateOfBirth', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('gender', 'Gender')}\n                </label>\n                <select\n                  value={formData.gender}\n                  onChange={(e) => handleInputChange(null, 'gender', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"\">{t('selectGender', 'Select Gender')}</option>\n                  <option value=\"male\">{t('male', 'Male')}</option>\n                  <option value=\"female\">{t('female', 'Female')}</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('nationality', 'Nationality')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.nationality}\n                  onChange={(e) => handleInputChange(null, 'nationality', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('nationalId', 'National ID')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.nationalId}\n                  onChange={(e) => handleInputChange(null, 'nationalId', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('phone', 'Phone')}\n                </label>\n                <input\n                  type=\"tel\"\n                  value={formData.phone}\n                  onChange={(e) => handleInputChange(null, 'phone', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('email', 'Email')}\n                </label>\n                <input\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={(e) => handleInputChange(null, 'email', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Emergency Contact */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-phone text-red-600 dark:text-red-400 mr-2\"></i>\n              {t('emergencyContact', 'Emergency Contact')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('name', 'Name')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.emergencyContact.name}\n                  onChange={(e) => handleInputChange('emergencyContact', 'name', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('relationship', 'Relationship')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.emergencyContact.relationship}\n                  onChange={(e) => handleInputChange('emergencyContact', 'relationship', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('phone', 'Phone')}\n                </label>\n                <input\n                  type=\"tel\"\n                  value={formData.emergencyContact.phone}\n                  onChange={(e) => handleInputChange('emergencyContact', 'phone', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('email', 'Email')}\n                </label>\n                <input\n                  type=\"email\"\n                  value={formData.emergencyContact.email}\n                  onChange={(e) => handleInputChange('emergencyContact', 'email', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Second Row - Insurance and Medical History */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\">\n          {/* Insurance Information */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-shield-alt text-purple-600 dark:text-purple-400 mr-2\"></i>\n              {t('insuranceInformation', 'Insurance Information')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('provider', 'Provider')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.insurance.provider}\n                  onChange={(e) => handleInputChange('insurance', 'provider', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"e.g., Bupa Arabia, Tawuniya\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('policyNumber', 'Policy Number')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.insurance.policyNumber}\n                  onChange={(e) => handleInputChange('insurance', 'policyNumber', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('groupNumber', 'Group Number')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.insurance.groupNumber}\n                  onChange={(e) => handleInputChange('insurance', 'groupNumber', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('expiryDate', 'Expiry Date')}\n                </label>\n                <input\n                  type=\"date\"\n                  value={formData.insurance.expiryDate}\n                  onChange={(e) => handleInputChange('insurance', 'expiryDate', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('copayAmount', 'Copay Amount')}\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.insurance.copayAmount}\n                  onChange={(e) => handleInputChange('insurance', 'copayAmount', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"0\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('deductible', 'Deductible')}\n                </label>\n                <input\n                  type=\"number\"\n                  value={formData.insurance.deductible}\n                  onChange={(e) => handleInputChange('insurance', 'deductible', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"0\"\n                />\n              </div>\n              <div className=\"md:col-span-2\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.insurance.isActive}\n                    onChange={(e) => handleInputChange('insurance', 'isActive', e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('insuranceActive', 'Insurance is active')}\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          {/* Medical History */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-notes-medical text-red-600 dark:text-red-400 mr-2\"></i>\n              {t('medicalHistory', 'Medical History')}\n            </h3>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('primaryDiagnosis', 'Primary Diagnosis')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.medicalHistory.primaryDiagnosis}\n                  onChange={(e) => handleInputChange('medicalHistory', 'primaryDiagnosis', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"e.g., Cerebral Palsy, Stroke, etc.\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('referringPhysician', 'Referring Physician')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.medicalHistory.referringPhysician}\n                  onChange={(e) => handleInputChange('medicalHistory', 'referringPhysician', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Dr. Name\"\n                />\n              </div>\n\n              {/* Allergies */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('allergies', 'Allergies')}\n                </label>\n                <div className=\"space-y-2\">\n                  {formData.medicalHistory.allergies.map((allergy, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"text\"\n                        value={allergy}\n                        onChange={(e) => handleArrayChange('medicalHistory', 'allergies', index, e.target.value)}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                        placeholder=\"e.g., Penicillin, Latex, etc.\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => removeArrayItem('medicalHistory', 'allergies', index)}\n                        className=\"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  ))}\n                  <button\n                    type=\"button\"\n                    onClick={() => addArrayItem('medicalHistory', 'allergies')}\n                    className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('addAllergy', 'Add Allergy')}\n                  </button>\n                </div>\n              </div>\n\n              {/* Current Medications */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('currentMedications', 'Current Medications')}\n                </label>\n                <div className=\"space-y-2\">\n                  {formData.medicalHistory.currentMedications.map((medication, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"text\"\n                        value={medication}\n                        onChange={(e) => handleArrayChange('medicalHistory', 'currentMedications', index, e.target.value)}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                        placeholder=\"e.g., Baclofen 10mg twice daily\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => removeArrayItem('medicalHistory', 'currentMedications', index)}\n                        className=\"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  ))}\n                  <button\n                    type=\"button\"\n                    onClick={() => addArrayItem('medicalHistory', 'currentMedications')}\n                    className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('addMedication', 'Add Medication')}\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Third Row - Special Needs and Additional Medical Information */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\">\n          {/* Special Needs */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-heart text-pink-600 dark:text-pink-400 mr-2\"></i>\n              {t('specialNeeds', 'Special Needs')}\n            </h3>\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.specialNeeds.hasSpecialNeeds}\n                    onChange={(e) => handleInputChange('specialNeeds', 'hasSpecialNeeds', e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('hasSpecialNeeds', 'Patient has special needs')}\n                  </span>\n                </label>\n              </div>\n\n              {formData.specialNeeds.hasSpecialNeeds && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('severity', 'Severity')}\n                    </label>\n                    <select\n                      value={formData.specialNeeds.severity}\n                      onChange={(e) => handleInputChange('specialNeeds', 'severity', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                    >\n                      <option value=\"mild\">{t('mild', 'Mild')}</option>\n                      <option value=\"moderate\">{t('moderate', 'Moderate')}</option>\n                      <option value=\"severe\">{t('severe', 'Severe')}</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('communicationMethod', 'Communication Method')}\n                    </label>\n                    <select\n                      value={formData.specialNeeds.communicationMethod}\n                      onChange={(e) => handleInputChange('specialNeeds', 'communicationMethod', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                    >\n                      <option value=\"verbal\">{t('verbal', 'Verbal')}</option>\n                      <option value=\"sign_language\">{t('signLanguage', 'Sign Language')}</option>\n                      <option value=\"visual_aids\">{t('visualAids', 'Visual Aids')}</option>\n                      <option value=\"assistive_technology\">{t('assistiveTechnology', 'Assistive Technology')}</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('behavioralNotes', 'Behavioral Notes')}\n                    </label>\n                    <textarea\n                      value={formData.specialNeeds.behavioralNotes}\n                      onChange={(e) => handleInputChange('specialNeeds', 'behavioralNotes', e.target.value)}\n                      rows={3}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                      placeholder=\"Behavioral patterns, triggers, calming techniques...\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('caregiverInstructions', 'Caregiver Instructions')}\n                    </label>\n                    <textarea\n                      value={formData.specialNeeds.caregiverInstructions}\n                      onChange={(e) => handleInputChange('specialNeeds', 'caregiverInstructions', e.target.value)}\n                      rows={3}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                      placeholder=\"Special instructions for caregivers...\"\n                    />\n                  </div>\n\n                  {/* Adaptive Equipment */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('adaptiveEquipment', 'Adaptive Equipment')}\n                    </label>\n                    <div className=\"space-y-2\">\n                      {formData.specialNeeds.adaptiveEquipment.map((equipment, index) => (\n                        <div key={index} className=\"flex items-center space-x-2\">\n                          <input\n                            type=\"text\"\n                            value={equipment}\n                            onChange={(e) => handleArrayChange('specialNeeds', 'adaptiveEquipment', index, e.target.value)}\n                            className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                            placeholder=\"e.g., Wheelchair, Walker, Communication device\"\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={() => removeArrayItem('specialNeeds', 'adaptiveEquipment', index)}\n                            className=\"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                          >\n                            <i className=\"fas fa-trash\"></i>\n                          </button>\n                        </div>\n                      ))}\n                      <button\n                        type=\"button\"\n                        onClick={() => addArrayItem('specialNeeds', 'adaptiveEquipment')}\n                        className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                      >\n                        <i className=\"fas fa-plus mr-2\"></i>\n                        {t('addEquipment', 'Add Equipment')}\n                      </button>\n                    </div>\n                  </div>\n                </>\n              )}\n            </div>\n          </div>\n\n          {/* Additional Medical Information */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-stethoscope text-teal-600 dark:text-teal-400 mr-2\"></i>\n              {t('additionalMedicalInfo', 'Additional Medical Information')}\n            </h3>\n            <div className=\"space-y-4\">\n              {/* Past Surgeries */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('pastSurgeries', 'Past Surgeries')}\n                </label>\n                <div className=\"space-y-2\">\n                  {formData.medicalHistory.pastSurgeries.map((surgery, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"text\"\n                        value={surgery}\n                        onChange={(e) => handleArrayChange('medicalHistory', 'pastSurgeries', index, e.target.value)}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                        placeholder=\"e.g., Appendectomy (2020), Hip replacement (2019)\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => removeArrayItem('medicalHistory', 'pastSurgeries', index)}\n                        className=\"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  ))}\n                  <button\n                    type=\"button\"\n                    onClick={() => addArrayItem('medicalHistory', 'pastSurgeries')}\n                    className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('addSurgery', 'Add Surgery')}\n                  </button>\n                </div>\n              </div>\n\n              {/* Chronic Conditions */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('chronicConditions', 'Chronic Conditions')}\n                </label>\n                <div className=\"space-y-2\">\n                  {formData.medicalHistory.chronicConditions.map((condition, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"text\"\n                        value={condition}\n                        onChange={(e) => handleArrayChange('medicalHistory', 'chronicConditions', index, e.target.value)}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                        placeholder=\"e.g., Diabetes, Hypertension, Arthritis\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => removeArrayItem('medicalHistory', 'chronicConditions', index)}\n                        className=\"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  ))}\n                  <button\n                    type=\"button\"\n                    onClick={() => addArrayItem('medicalHistory', 'chronicConditions')}\n                    className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('addCondition', 'Add Condition')}\n                  </button>\n                </div>\n              </div>\n\n              {/* Family History */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('familyHistory', 'Family History')}\n                </label>\n                <div className=\"space-y-2\">\n                  {formData.medicalHistory.familyHistory.map((history, index) => (\n                    <div key={index} className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"text\"\n                        value={history}\n                        onChange={(e) => handleArrayChange('medicalHistory', 'familyHistory', index, e.target.value)}\n                        className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                        placeholder=\"e.g., Father - Heart disease, Mother - Diabetes\"\n                      />\n                      <button\n                        type=\"button\"\n                        onClick={() => removeArrayItem('medicalHistory', 'familyHistory', index)}\n                        className=\"px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700\"\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  ))}\n                  <button\n                    type=\"button\"\n                    onClick={() => addArrayItem('medicalHistory', 'familyHistory')}\n                    className=\"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('addFamilyHistory', 'Add Family History')}\n                  </button>\n                </div>\n              </div>\n\n              {/* Medical Notes */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('medicalNotes', 'Medical Notes')}\n                </label>\n                <textarea\n                  value={formData.medicalHistory.notes}\n                  onChange={(e) => handleInputChange('medicalHistory', 'notes', e.target.value)}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Additional medical notes, observations, or important information...\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientEditSinglePage;\n"], "names": ["PatientEditSinglePage", "id", "useParams", "navigate", "useNavigate", "location", "useLocation", "t", "isRTL", "useLanguage", "loading", "setLoading", "useState", "saving", "setSaving", "patient", "setPatient", "formData", "setFormData", "firstName", "lastName", "firstNameAr", "lastNameAr", "dateOfBirth", "gender", "nationality", "nationalId", "phone", "email", "address", "street", "city", "region", "postalCode", "country", "emergencyContact", "name", "relationship", "insurance", "provider", "policyNumber", "groupNumber", "expiryDate", "copayAmount", "deductible", "coverageLimit", "isActive", "medicalHistory", "allergies", "currentMedications", "pastSurgeries", "chronicConditions", "familyHistory", "primaryDiagnosis", "secondaryDiagnoses", "referringPhysician", "notes", "specialNeeds", "hasSpecialNeeds", "types", "severity", "communicationMethod", "behavioralNotes", "sensoryPreferences", "lighting", "sound", "temperature", "adaptiveEquipment", "caregiverInstructions", "useEffect", "async", "_location$state", "_location$state2", "patientData", "state", "fromPatientProfile", "response", "api", "get", "concat", "success", "data", "_patientData$address", "_patientData$address2", "_patientData$address3", "_patientData$address4", "_patientData$address5", "_patientData$emergenc", "_patientData$emergenc2", "_patientData$emergenc3", "_patientData$emergenc4", "_patientData$insuranc", "_patientData$insuranc2", "_patientData$insuranc3", "_patientData$insuranc4", "_patientData$insuranc5", "_patientData$insuranc6", "_patientData$insuranc7", "_patientData$insuranc8", "_patientData$medicalH", "_patientData$medicalH2", "_patientData$medicalH3", "_patientData$medicalH4", "_patientData$medicalH5", "_patientData$medicalH6", "_patientData$medicalH7", "_patientData$medicalH8", "_patientData$medicalH9", "_patientData$specialN", "_patientData$specialN2", "_patientData$specialN3", "_patientData$specialN4", "_patientData$specialN5", "_patientData$specialN6", "_patientData$specialN7", "_patientData$specialN8", "_patientData$specialN9", "_patientData$specialN0", "_patientData$specialN1", "_patientData$specialN10", "_patientData$specialN11", "split", "error", "console", "toast", "loadPatientData", "handleInputChange", "section", "field", "value", "prev", "_objectSpread", "handleArrayChange", "index", "newArray", "addArrayItem", "removeArrayItem", "filter", "_", "i", "_jsx", "className", "children", "_jsxs", "nameEn", "onClick", "_id", "put", "Error", "message", "disabled", "type", "onChange", "e", "target", "placeholder", "checked", "map", "allergy", "medication", "_Fragment", "rows", "equipment", "surgery", "condition", "history"], "sourceRoot": ""}