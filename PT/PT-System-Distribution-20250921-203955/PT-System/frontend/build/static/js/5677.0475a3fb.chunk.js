"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[5677],{930:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(2555);const r=new class{constructor(){this.baseURL="http://localhost:5001/api/v1",this.storagePrefix="physioflow_",this.initializeStorage()}getAuthToken(){return localStorage.getItem("token")||"demo-token"}async apiRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=this.getAuthToken(),r="".concat(this.baseURL).concat(e),n={headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}},i=(0,a.A)((0,a.A)((0,a.A)({},n),t),{},{headers:(0,a.A)((0,a.A)({},n.headers),t.headers)});try{const e=await fetch(r,i);if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return await e.json()}catch(c){return console.warn("API request failed for ".concat(e,", using localStorage fallback:"),c.message),this.fallbackToLocalStorage(e,t)}}fallbackToLocalStorage(e,t){const s=t.method||"GET",a=t.body?JSON.parse(t.body):null;return e.includes("/bodymap")?this.handleBodyMapFallback(e,s,a):e.includes("/communication")?this.handleCommunicationFallback(e,s,a):e.includes("/exercise-programs")?this.handleExerciseFallback(e,s,a):e.includes("/ai-interactions")?this.handleAIFallback(e,s,a):{success:!1,error:"Endpoint not supported in fallback mode"}}initializeStorage(){this.getItem("initialized")||(this.setItem("initialized",!0),this.setItem("patients",this.getDefaultPatients()),this.setItem("bodyMapData",{}),this.setItem("communicationHistory",[]),this.setItem("exercisePrograms",[]),this.setItem("aiInteractions",[]))}getItem(e){try{const t=localStorage.getItem(this.storagePrefix+e);return t?JSON.parse(t):null}catch(t){return console.error("Error getting item from storage:",t),null}}setItem(e,t){try{return localStorage.setItem(this.storagePrefix+e,JSON.stringify(t)),!0}catch(s){return console.error("Error setting item in storage:",s),!1}}removeItem(e){try{return localStorage.removeItem(this.storagePrefix+e),!0}catch(t){return console.error("Error removing item from storage:",t),!1}}getDefaultPatients(){return[{id:"demo-patient-001",name:"Ahmed Mohammed",nameAr:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f",age:28,gender:"male",condition:"Cerebral Palsy",conditionAr:"\u0627\u0644\u0634\u0644\u0644 \u0627\u0644\u062f\u0645\u0627\u063a\u064a",phone:"+966501234567",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","whatsapp"],language:"en",quietHours:{start:"22:00",end:"08:00"}},createdAt:(new Date).toISOString()},{id:"demo-patient-002",name:"Sarah Johnson",nameAr:"\u0633\u0627\u0631\u0629 \u062c\u0648\u0646\u0633\u0648\u0646",age:35,gender:"female",condition:"Spinal Cord Injury",conditionAr:"\u0625\u0635\u0627\u0628\u0629 \u0627\u0644\u062d\u0628\u0644 \u0627\u0644\u0634\u0648\u0643\u064a",phone:"+**********",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","sms"],language:"en",quietHours:{start:"21:00",end:"07:00"}},createdAt:(new Date).toISOString()}]}async getPatients(){return new Promise(e=>{setTimeout(()=>{e(this.getItem("patients")||[])},100)})}async getPatient(e){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("patients")||[]).find(t=>t.id===e);t(s||null)},100)})}async saveBodyMapData(e,t){try{const s=await this.apiRequest("/bodymap",{method:"POST",body:JSON.stringify((0,a.A)({patientId:e},t))});return s.success?s.data:null}catch(s){return new Promise(s=>{setTimeout(()=>{const r=this.getItem("bodyMapData")||{};r[e]=(0,a.A)((0,a.A)({},t),{},{timestamp:(new Date).toISOString()}),this.setItem("bodyMapData",r),s(!0)},200)})}}async getBodyMapData(e){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("bodyMapData")||{};t(s[e]||null)},100)})}async sendMessage(e){try{const t=await this.apiRequest("/communication",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("communicationHistory")||[],r=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString(),status:"sent"});s.push(r),this.setItem("communicationHistory",s),t(r)},500)})}}async getCommunicationHistory(e){try{const t=await this.apiRequest("/communication/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("communicationHistory")||[]).filter(t=>t.patientId===e);t(s)},100)})}}async saveExerciseProgram(e){try{const t=await this.apiRequest("/exercise-programs",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("exercisePrograms")||[],r=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{createdAt:(new Date).toISOString()});s.push(r),this.setItem("exercisePrograms",s),t(r)},300)})}}async getExercisePrograms(e){try{const t=await this.apiRequest("/exercise-programs/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("exercisePrograms")||[]).filter(t=>t.patientId===e);t(s)},100)})}}async saveAIInteraction(e){try{const t=await this.apiRequest("/ai-interactions",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("aiInteractions")||[],r=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString()});s.push(r),this.setItem("aiInteractions",s),t(r)},100)})}}async getAIInteractions(e){try{const t=e?"/ai-interactions/".concat(e):"/ai-interactions",s=await this.apiRequest(t);return s.success?s.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("aiInteractions")||[],a=e?s.filter(t=>t.patientId===e):s;t(a)},100)})}}async getAnalytics(){return new Promise(e=>{setTimeout(()=>{const t=this.getItem("aiInteractions")||[],s=this.getItem("communicationHistory")||[],r=this.getItem("exercisePrograms")||[],n=this.getItem("bodyMapData")||{},i={totalAIQueries:t.length,totalCommunications:s.length,totalExercisePrograms:r.length,totalBodyMapAssessments:Object.keys(n).length,recentActivity:[...t.slice(-5).map(e=>(0,a.A)({type:"ai"},e)),...s.slice(-5).map(e=>(0,a.A)({type:"communication"},e)),...r.slice(-5).map(e=>(0,a.A)({type:"exercise"},e))].sort((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)).slice(0,10)};e(i)},200)})}clearAllData(){["patients","bodyMapData","communicationHistory","exercisePrograms","aiInteractions"].forEach(e=>this.removeItem(e)),this.removeItem("initialized"),this.initializeStorage()}exportData(){return{patients:this.getItem("patients"),bodyMapData:this.getItem("bodyMapData"),communicationHistory:this.getItem("communicationHistory"),exercisePrograms:this.getItem("exercisePrograms"),aiInteractions:this.getItem("aiInteractions"),exportedAt:(new Date).toISOString()}}importData(e){try{return e.patients&&this.setItem("patients",e.patients),e.bodyMapData&&this.setItem("bodyMapData",e.bodyMapData),e.communicationHistory&&this.setItem("communicationHistory",e.communicationHistory),e.exercisePrograms&&this.setItem("exercisePrograms",e.exercisePrograms),e.aiInteractions&&this.setItem("aiInteractions",e.aiInteractions),!0}catch(t){return console.error("Error importing data:",t),!1}}delay(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;return new Promise(t=>setTimeout(t,e))}}},5677:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var a=s(2555),r=s(5043),n=s(7921),i=s(3216),c=s(9737),o=s(930),l=s(3768),m=s(579);const d=()=>{const{t:e,isRTL:t}=(0,n.o)(),{patientId:s}=(0,i.g)(),d=(0,i.Zp)(),[u,g]=(0,r.useState)(null),[h,x]=(0,r.useState)([]),[p,y]=(0,r.useState)(!0),[b,f]=(0,r.useState)({totalMessages:0,emailCount:0,smsCount:0,whatsappCount:0,pushCount:0});(0,r.useEffect)(()=>{j(),w()},[s]);const j=async()=>{if(s)try{const e=await o.A.getPatient(s);g(e)}catch(t){console.error("Error loading patient:",t),l.Ay.error(e("errorLoadingPatient","Error loading patient data"))}y(!1)},w=async()=>{if(s)try{const e=await o.A.getCommunicationHistory(s);x(e);const t={totalMessages:e.length,emailCount:e.filter(e=>{var t;return null===(t=e.channels)||void 0===t?void 0:t.includes("email")}).length,smsCount:e.filter(e=>{var t;return null===(t=e.channels)||void 0===t?void 0:t.includes("sms")}).length,whatsappCount:e.filter(e=>{var t;return null===(t=e.channels)||void 0===t?void 0:t.includes("whatsapp")}).length,pushCount:e.filter(e=>{var t;return null===(t=e.channels)||void 0===t?void 0:t.includes("push")}).length};f(t)}catch(e){console.error("Error loading communication history:",e)}},v=e=>{switch(e){case"email":return"fas fa-envelope";case"sms":return"fas fa-sms";case"whatsapp":return"fab fa-whatsapp";case"push":return"fas fa-bell";default:return"fas fa-comment"}},N=e=>{switch(e){case"email":return"text-blue-600";case"sms":return"text-green-600";case"whatsapp":return"text-green-500";case"push":return"text-purple-600";default:return"text-gray-600"}},S=e=>{switch(e){case"high":return"bg-red-100 text-red-800";case"medium":return"bg-yellow-100 text-yellow-800";case"low":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}};return p?(0,m.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,m.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,m.jsxs)("div",{className:"communication-page min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,m.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,m.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,m.jsx)("div",{className:"py-6",children:(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,m.jsx)("button",{onClick:()=>d(-1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,m.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,m.jsxs)("div",{children:[(0,m.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,m.jsx)("i",{className:"fas fa-comments mr-3 text-green-600"}),e("communicationHub","Communication Hub")]}),u&&(0,m.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mt-1",children:[e("patient","Patient"),": ",t?u.nameAr:u.name,(0,m.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:[u.phone," \u2022 ",u.email]})]})]})]}),(0,m.jsx)("div",{className:"flex items-center space-x-3",children:(0,m.jsxs)("div",{className:"flex space-x-2",children:[(0,m.jsxs)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,m.jsx)("i",{className:"fas fa-robot mr-1"}),e("smartRouting","Smart Routing")]}),(0,m.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,m.jsx)("i",{className:"fas fa-clock mr-1"}),e("scheduled","Scheduled")]})]})})]})})})}),(0,m.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,m.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-3 gap-8",children:[(0,m.jsx)("div",{className:"xl:col-span-2",children:(0,m.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,m.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:e("sendMessage","Send Message")}),(0,m.jsx)(c.A,{patientId:s,onSend:async t=>{try{await o.A.sendMessage((0,a.A)((0,a.A)({},t),{},{patientId:s})),await w(),l.Ay.success(e("messageSent","Message sent successfully"))}catch(r){console.error("Error sending message:",r),l.Ay.error(e("errorSending","Error sending message"))}}})]})}),(0,m.jsxs)("div",{className:"xl:col-span-1 space-y-6",children:[(0,m.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,m.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,m.jsx)("i",{className:"fas fa-chart-bar mr-2 text-gray-600"}),e("communicationStats","Communication Stats")]}),(0,m.jsxs)("div",{className:"space-y-4",children:[(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("totalMessages","Total Messages")}),(0,m.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:b.totalMessages})]}),(0,m.jsxs)("div",{className:"space-y-2",children:[(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)("i",{className:"fas fa-envelope text-blue-600"}),(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Email"})]}),(0,m.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.emailCount})]}),(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)("i",{className:"fas fa-sms text-green-600"}),(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"SMS"})]}),(0,m.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.smsCount})]}),(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)("i",{className:"fab fa-whatsapp text-green-500"}),(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"WhatsApp"})]}),(0,m.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.whatsappCount})]}),(0,m.jsxs)("div",{className:"flex items-center justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)("i",{className:"fas fa-bell text-purple-600"}),(0,m.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Push"})]}),(0,m.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.pushCount})]})]})]})]}),(0,m.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,m.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,m.jsx)("i",{className:"fas fa-history mr-2 text-gray-600"}),e("recentMessages","Recent Messages")]}),h.length>0?(0,m.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:h.slice(0,10).map((t,s)=>{var a;return(0,m.jsxs)("div",{className:"p-3 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,m.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[null===(a=t.channels)||void 0===a?void 0:a.map((e,t)=>(0,m.jsx)("i",{className:"".concat(v(e)," ").concat(N(e)," text-sm")},t)),(0,m.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(S(t.urgency)),children:t.urgency||"normal"})]}),(0,m.jsx)("span",{className:"text-xs text-gray-500",children:new Date(t.timestamp).toLocaleDateString()})]}),(0,m.jsx)("div",{className:"text-sm text-gray-900 dark:text-white font-medium mb-1",children:t.subject||e("noSubject","No Subject")}),(0,m.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:t.message}),(0,m.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,m.jsx)("span",{className:"text-xs text-gray-500",children:new Date(t.timestamp).toLocaleTimeString()}),(0,m.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("sent"===t.status?"bg-green-100 text-green-800":"pending"===t.status?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"),children:t.status||"sent"})]})]},s)})}):(0,m.jsxs)("div",{className:"text-center py-8",children:[(0,m.jsx)("i",{className:"fas fa-comment-slash text-4xl text-gray-300 mb-4"}),(0,m.jsx)("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:e("noMessages","No messages yet")}),(0,m.jsx)("p",{className:"text-gray-400 dark:text-gray-500 text-xs mt-1",children:e("sendFirstMessage","Send your first message to get started")})]})]})]})]})})]})}},9737:(e,t,s)=>{s.d(t,{A:()=>c});var a=s(5043),r=s(7921),n=s(3768),i=s(579);const c=e=>{let{patientId:t,onSend:s,templates:c=[]}=e;const{t:o,isRTL:l}=(0,r.o)(),[m,d]=(0,a.useState)(["email"]),[u,g]=(0,a.useState)(""),[h,x]=(0,a.useState)(""),[p,y]=(0,a.useState)(""),[b,f]=(0,a.useState)("normal"),[j,w]=(0,a.useState)(""),[v,N]=(0,a.useState)({}),S=[{id:"email",name:o("email","Email"),icon:"fas fa-envelope",color:"blue",available:!0,description:o("emailDesc","Send via email")},{id:"sms",name:o("sms","SMS"),icon:"fas fa-sms",color:"green",available:!0,description:o("smsDesc","Send via SMS")},{id:"whatsapp",name:o("whatsapp","WhatsApp"),icon:"fab fa-whatsapp",color:"green",available:!0,description:o("whatsappDesc","Send via WhatsApp")},{id:"push",name:o("pushNotification","Push Notification"),icon:"fas fa-bell",color:"purple",available:!0,description:o("pushDesc","Send push notification to mobile app")},{id:"telegram",name:o("telegram","Telegram"),icon:"fab fa-telegram",color:"blue",available:!1,description:o("telegramDesc","Send via Telegram (Coming Soon)")}],k=[{id:"appointment_reminder",name:o("appointmentReminder","Appointment Reminder"),subject:o("appointmentReminderSubject","Appointment Reminder"),content:o("appointmentReminderContent","Dear {patientName}, this is a reminder for your appointment on {date} at {time}. Please arrive 15 minutes early."),channels:["email","sms","whatsapp","push"]},{id:"exercise_instructions",name:o("exerciseInstructions","Exercise Instructions"),subject:o("exerciseInstructionsSubject","Your Exercise Program"),content:o("exerciseInstructionsContent","Dear {patientName}, please find your personalized exercise program. Follow the instructions carefully and contact us if you have any questions."),channels:["email","whatsapp"]},{id:"progress_update",name:o("progressUpdate","Progress Update"),subject:o("progressUpdateSubject","Your Progress Update"),content:o("progressUpdateContent","Dear {patientName}, here is your latest progress update. Keep up the great work!"),channels:["email","whatsapp","push"]},{id:"medication_reminder",name:o("medicationReminder","Medication Reminder"),subject:o("medicationReminderSubject","Medication Reminder"),content:o("medicationReminderContent","Time to take your medication: {medicationName}. Follow the prescribed dosage."),channels:["sms","push"]},{id:"custom",name:o("customMessage","Custom Message"),subject:"",content:"",channels:["email","sms","whatsapp","push"]}];(0,a.useEffect)(()=>{I()},[t]);const I=async()=>{try{const e={preferredChannels:["email","whatsapp"],language:"en",timezone:"Asia/Riyadh",quietHours:{start:"22:00",end:"08:00"},urgentOnly:!1,emailFrequency:"all",smsFrequency:"urgent",whatsappFrequency:"all",pushFrequency:"all"};N(e),d(e.preferredChannels)}catch(e){console.error("Error loading patient preferences:",e)}},A=e=>{d(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return(0,i.jsx)("div",{className:"communication-manager",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[(0,i.jsx)("i",{className:"fas fa-comments mr-2 text-blue-600"}),o("communicationCenter","Communication Center")]}),(0,i.jsxs)("button",{onClick:()=>{const e="urgent"===b?["sms","push","whatsapp"]:"high"===b?["email","whatsapp","push"]:v.preferredChannels||["email"];d(e),n.Ay.success(o("channelsAutoSelected","Channels auto-selected based on urgency and patient preferences"))},className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-magic mr-1"}),o("autoSelect","Auto Select")]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("messageTemplate","Message Template")}),(0,i.jsxs)("select",{value:p,onChange:e=>(e=>{const t=k.find(t=>t.id===e);if(t){y(e),x(t.subject),g(t.content);const s=t.channels.filter(e=>S.find(t=>t.id===e&&t.available));d(s)}})(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:o("selectTemplate","Select a template")}),k.map(e=>(0,i.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),m.includes("email")&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("subject","Subject")}),(0,i.jsx)("input",{type:"text",value:h,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:o("enterSubject","Enter email subject")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("message","Message")}),(0,i.jsx)("textarea",{value:u,onChange:e=>g(e.target.value),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none",placeholder:o("enterMessage","Enter your message...")}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[u.length,"/1000 ",o("characters","characters")]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("urgency","Urgency")}),(0,i.jsxs)("select",{value:b,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,i.jsx)("option",{value:"low",children:o("lowUrgency","Low")}),(0,i.jsx)("option",{value:"normal",children:o("normalUrgency","Normal")}),(0,i.jsx)("option",{value:"high",children:o("highUrgency","High")}),(0,i.jsx)("option",{value:"urgent",children:o("urgentUrgency","Urgent")})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[o("scheduleTime","Schedule Time")," (",o("optional","Optional"),")"]}),(0,i.jsx)("input",{type:"datetime-local",value:j,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:o("communicationChannels","Communication Channels")}),(0,i.jsx)("div",{className:"space-y-3",children:S.map(e=>(0,i.jsx)("div",{className:"p-3 border rounded-lg transition-all cursor-pointer ".concat(m.includes(e.id)?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"," ").concat(e.available?"":"opacity-50 cursor-not-allowed"),onClick:()=>e.available&&A(e.id),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("i",{className:"".concat(e.icon," text-").concat(e.color,"-600 text-lg")}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,i.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.available&&(0,i.jsx)("span",{className:"text-xs text-gray-400",children:o("comingSoon","Coming Soon")}),(0,i.jsx)("input",{type:"checkbox",checked:m.includes(e.id),onChange:()=>e.available&&A(e.id),disabled:!e.available,className:"w-4 h-4 text-blue-600 rounded focus:ring-blue-500"})]})]})},e.id))})]}),v.preferredChannels&&(0,i.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,i.jsx)("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("patientPreferences","Patient Preferences")}),(0,i.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[o("preferredChannels","Preferred"),": ",v.preferredChannels.join(", ")]}),v.quietHours&&(0,i.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[o("quietHours","Quiet Hours"),": ",v.quietHours.start," - ",v.quietHours.end]})]}),(0,i.jsxs)("button",{onClick:async()=>{if(!(u.trim()?m.includes("email")&&!h.trim()?(n.Ay.error(o("subjectRequired","Subject is required for email")),0):0!==m.length||(n.Ay.error(o("selectChannel","Please select at least one communication channel")),0):(n.Ay.error(o("messageRequired","Message content is required")),0)))return;const e={patientId:t,channels:m,message:u,subject:h,urgency:b,scheduledTime:j||null,template:p,timestamp:(new Date).toISOString()};try{s&&await s(e),console.log("Sending communication:",e),n.Ay.success(o("messageSent","Message sent successfully via selected channels")),g(""),x(""),y(""),w("")}catch(a){n.Ay.error(o("sendError","Error sending message")),console.error("Send error:",a)}},disabled:0===m.length||!u.trim(),className:"w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-paper-plane mr-2"}),j?o("scheduleMessage","Schedule Message"):o("sendMessage","Send Message")]})]})]})]})})}}}]);
//# sourceMappingURL=5677.0475a3fb.chunk.js.map