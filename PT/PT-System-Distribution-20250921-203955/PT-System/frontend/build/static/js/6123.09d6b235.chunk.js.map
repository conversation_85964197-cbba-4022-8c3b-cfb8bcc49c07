{"version": 3, "file": "static/js/6123.09d6b235.chunk.js", "mappings": "iOAMA,MA+xCA,EA/xCuBA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC3B,MAAM,GAAEC,IAAOC,EAAAA,EAAAA,KACTC,GAAWC,EAAAA,EAAAA,OACX,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAWC,IAAgBF,EAAAA,EAAAA,UAAS,aACpCG,EAASC,IAAcJ,EAAAA,EAAAA,UAAS,OAChCK,EAAeC,IAAoBN,EAAAA,EAAAA,WAAS,IAC5CO,EAAUC,IAAeR,EAAAA,EAAAA,UAAS,KAClCS,EAAoBC,IAAyBV,EAAAA,EAAAA,UAAS,CAC3DF,SAAS,EACTa,KAAM,KACNC,eAAgB,aAGlBC,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACvB,IAGJ,MAsIMuB,EAAqBC,UACzBhB,GAAW,GACX,IAEE,MAAMiB,QAAiBC,EAAAA,GAAIC,IAAI,aAADC,OAAc5B,IAC5C,IAAIyB,EAASI,QAgCX,MAAM,IAAIC,MAAML,EAASM,SAAW,kCAhChB,CAAC,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAEpB,MAAMC,GAAWC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACZd,EAASL,MAAI,IAChBoB,YAAaf,EAASL,KAAKoB,aAAe,CACxCC,SAAU,EACVC,aAAc,GACdC,UAAWlB,EAASL,KAAKwB,mBAAoB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjFC,iBAAgD,QAA9BhB,EAAAP,EAASL,KAAK4B,wBAAgB,IAAAhB,GAA9BA,EAAgCiB,WAA2C,QAAlChB,EAAIR,EAASL,KAAK4B,wBAAgB,IAAAf,GAA9BA,EAAgCiB,SAAQ,GAAAtB,OAChGH,EAASL,KAAK4B,iBAAiBC,UAAS,KAAArB,OAAIH,EAASL,KAAK4B,iBAAiBE,UAC9E,eACJC,MAAO,IAETC,YAAa3B,EAASL,KAAKgC,aAAe,CACxCC,WAAuC,QAA5BnB,EAAAT,EAASL,KAAKkC,sBAAc,IAAApB,OAAA,EAA5BA,EAA8BqB,mBAAoB,gBAC7DC,aAAyC,QAA5BrB,EAAAV,EAASL,KAAKkC,sBAAc,IAAAnB,OAAA,EAA5BA,EAA8BoB,mBAAoB,8CAC/DE,SAAU,OACVC,WAAuC,QAA5BtB,EAAAX,EAASL,KAAKkC,sBAAc,IAAAlB,OAAA,EAA5BA,EAA8BsB,YAAa,GACtDC,aAAyC,QAA5BtB,EAAAZ,EAASL,KAAKkC,sBAAc,IAAAjB,OAAA,EAA5BA,EAA8BuB,qBAAsB,IAEnEC,aAAcpC,EAASL,KAAKyC,cAAgB,GAC5CC,WAAYrC,EAASL,KAAK0C,YAAc,GACxCC,UAAWtC,EAASL,KAAK2C,WAAa,GACtCC,iBAAkBvC,EAASL,KAAK4C,kBAAoB,CAClDC,KAAM,eACNC,OAAQ,eACRC,aAAc,gBACdC,MAAO,kBAGXvD,EAAWyB,EACb,CAGF,CAAE,MAAO+B,GACPC,QAAQD,MAAM,iCAAkCA,GA+C9CE,EAAAA,GAAMF,MAAMjE,EAAE,sBAAuB,kCACrCS,EAAW,KAEf,CAAC,QACCL,GAAW,EACb,GAoBIgE,EAAmBA,KACvBzD,GAAiB,GACjBE,EAAY,KAIRwD,EAAyBjD,iBAA+B,IAAxBkD,EAAMC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,UAC7C,IACExD,EAAsB2D,IAAIvC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUuC,GAAI,IAAEvE,SAAS,KAEnD,MAAMkB,QAAiBC,EAAAA,GAAIC,IAAI,gCAADC,OAAiC5B,EAAE,qBAAA4B,OAAoB8C,IAErF,GAAIjD,EAASI,QACXV,EAAsB2D,IAAIvC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACrBuC,GAAI,IACP1D,KAAMK,EAASL,KACfC,eAAgBqD,EAChBnE,SAAS,SAEN,CAEL,MAAMwE,EAAW,CACfC,cAAe,CACb,CACEC,KAAM,aACNC,YAAa,GACbC,QAAS,KACTC,UAAW,EACXC,SAAU,GACVC,gBAAiB,IAEnB,CACEL,KAAM,aACNC,YAAa,GACbC,QAAS,KACTC,UAAW,EACXC,SAAU,GACVC,gBAAiB,IAEnB,CACEL,KAAM,aACNC,YAAa,GACbC,QAAS,KACTC,UAAW,EACXC,SAAU,IACVC,gBAAiB,KAGrBC,cAAe,CACbL,YAAa,CAAEM,MAAO,GAAIC,UAAW,YACrCC,QAAS,CAAEC,KAAM,KAAMF,UAAW,UAClCL,UAAW,CAAEQ,QAAS,EAAGC,QAAS,KAClCC,uBAAwB,CAAEC,MAAO,IAAKC,MAAO,GAAIC,UAAW,IAC5DX,gBAAiB,IAEnBY,iBAAkB,CAAC,wBAAyB,kBAAmB,2BAC/DC,aAAc,GACdC,gBAAiB,EACjBC,iCAAkC,CAChCC,gBAAiB,CACfC,WAAY,GACZC,WAAY,GACZC,eAAgB,GAChBxB,KAAM,aACNyB,MAAO,uBAETC,eAAgB,CACdJ,WAAY,IACZC,WAAY,GACZC,eAAgB,GAChBxB,KAAM,aACNyB,MAAO,sBAETE,YAAa,CACXC,iBAAkB,GAClBC,iBAAkB,GAClBC,qBAAsB,EACtBC,sBAAuB,KACvBC,kBAAmB,MAKzB9F,EAAsB2D,IAAIvC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACrBuC,GAAI,IACP1D,KAAM2D,EACN1D,eAAgBqD,EAChBnE,SAAS,IAEb,CACF,CAAE,MAAO8D,GACPC,QAAQD,MAAM,qCAAsCA,GACpDE,EAAAA,GAAMF,MAAMjE,EAAE,iCAAkC,sCAChDe,EAAsB2D,IAAIvC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUuC,GAAI,IAAEvE,SAAS,IACrD,CACF,EAUA,OAAIA,GAEA2G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qEAKhBvG,EAeAA,GAqBHyG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAEpCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMpH,EAAS,aACxBiH,UAAU,4EAA2EC,UAErFF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,gBAAAvF,OAAkBvB,EAAQ,QAAU,OAAM,0CAExDgH,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D/G,EAAQO,EAAQqD,KAAOrD,EAAQsD,UAElCmD,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wCAAuCC,SAAA,CACjDhH,EAAE,YAAa,cAAc,KAAGQ,EAAQZ,GAAG,WAAII,EAAE,MAAO,OAAO,KAAGQ,EAAQ2G,cAKjFF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,aAAD0B,OAAchB,EAAQ4G,KAAO5G,EAAQZ,GAAE,SAAS,CACrEyH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,kFAAiFC,SAAA,EAE3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ/G,EAAE,cAAe,oBAEpBiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,oBAAqB,CAC3CuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,oFAAmFC,SAAA,EAE7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACZ/G,EAAE,iBAAkB,+BAO7BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,CAChExG,EAAQ4B,YAAYC,SAAS,QAEhCyE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAEhH,EAAE,kBAAmB,+BAK5E8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDC,UAC/DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExG,EAAQiD,aAAae,UAExBsC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAEhH,EAAE,oBAAqB,iCAK9E8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExG,EAAQ4B,YAAYE,aAAakC,UAEpCsC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAEhH,EAAE,eAAgB,4BAKzE8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEAEfE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChExG,EAAQmD,UAAUa,UAErBsC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAAEhH,EAAE,YAAa,2BAOxE8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wBAAuBC,SACnC,CACC,CAAEpH,GAAI,WAAY2H,MAAOvH,EAAE,WAAY,YAAawH,KAAM,eAC1D,CAAE5H,GAAI,UAAW2H,MAAOvH,EAAE,cAAe,gBAAiBwH,KAAM,sBAChE,CAAE5H,GAAI,qBAAsB2H,MAAOvH,EAAE,qBAAsB,uBAAwBwH,KAAM,oBACzF,CAAE5H,GAAI,UAAW2H,MAAOvH,EAAE,kBAAmB,oBAAqBwH,KAAM,qBACxE,CAAE5H,GAAI,eAAgB2H,MAAOvH,EAAE,eAAgB,gBAAiBwH,KAAM,mBACtE,CAAE5H,GAAI,YAAa2H,MAAOvH,EAAE,YAAa,aAAcwH,KAAM,kBAC7DC,IAAKC,IACLT,EAAAA,EAAAA,MAAA,UAEEC,QAASA,KAAMS,OA9KJC,EA8KoBF,EAAI9H,GA7K/CW,EAAaqH,QACC,uBAAVA,GAAmC9G,EAAmBE,MACxDqD,KAHqBuD,OA+KXb,UAAS,8DAAAvF,OACPlB,IAAcoH,EAAI9H,GACd,mDACA,0HACHoH,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAvF,OAAKkG,EAAIF,KAAI,WACxBE,EAAIH,QATAG,EAAI9H,WAiBJ,aAAdU,IACC2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhH,EAAE,sBAAuB,2BAE5BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,WAAY,aAAa,QAC/E8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACxD/G,EAAQO,EAAQqD,KAAOrD,EAAQsD,aAGpCmD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,cAAe,iBAAiB,QACtF8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SAAExG,EAAQqH,kBAEvEZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,SAAU,UAAU,QAC1E8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACxDhH,EAAEQ,EAAQsH,OAAQtH,EAAQsH,cAG/Bb,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,aAAc,eAAe,QACnF8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SAAExG,EAAQuH,iBAEvEd,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,QAAS,SAAS,QACxE8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SAAExG,EAAQwD,YAEvEiD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,QAAS,SAAS,QACxE8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SAAExG,EAAQwH,kBAM3Ef,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhH,EAAE,mBAAoB,wBAEzBiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,OAAQ,QAAQ,QACtE8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACxD/G,EAAQO,EAAQoD,iBAAiBC,KAAOrD,EAAQoD,iBAAiBE,aAGtEmD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,eAAgB,gBAAgB,QACtF8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACxDhH,EAAEQ,EAAQoD,iBAAiBG,aAAcvD,EAAQoD,iBAAiBG,oBAGvEkD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,QAAS,SAAS,QACxE8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACxDxG,EAAQoD,iBAAiBI,oBASvB,YAAd1D,IACC2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhH,EAAE,YAAa,gBAElBiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,mBAAoB,qBAAqB,QAC/F8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iDAAgDC,SAC1D/G,EAAQO,EAAQwC,YAAYI,YAAc5C,EAAQwC,YAAYC,gBAGnEgE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,WAAY,YAAY,QAC9E8G,EAAAA,EAAAA,KAAA,QAAMC,UAAS,mDAAAvF,OACoB,SAAjChB,EAAQwC,YAAYK,SAAsB,uEACT,aAAjC7C,EAAQwC,YAAYK,SAA0B,2EAC9C,gEACC2D,SACAhH,EAAEQ,EAAQwC,YAAYK,SAAU7C,EAAQwC,YAAYK,sBAM7D4D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhH,EAAE,0BAA2B,8BAEhCiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,YAAa,aAAa,QAChF8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BC,SACvCxG,EAAQwC,YAAYM,UAAUmE,IAAI,CAACQ,EAASC,KAC3CpB,EAAAA,EAAAA,KAAA,QAAkBC,UAAU,8FAA6FC,SACtHiB,GADQC,UAMjBjB,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,qBAAsB,uBAAuB,QACnG8G,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iBAAgBC,SAC3BxG,EAAQwC,YAAYO,YAAYkE,IAAI,CAACU,EAAYD,KAChDjB,EAAAA,EAAAA,MAAA,MAAgBF,UAAU,gCAA+BC,SAAA,CAAC,UAAGmB,IAApDD,kBAUR,uBAAd5H,IACCwG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBlG,EAAmBX,SAClB2G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uEAGhBjG,EAAmBE,MAoBtBiG,EAAAA,EAAAA,MAAAmB,EAAAA,SAAA,CAAApB,SAAA,EAEEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhH,EAAE,qBAAsB,0BAE3BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,UACEoB,MAAOvH,EAAmBG,eAC1BqH,SAAWC,GAAMlE,EAAuBkE,EAAEC,OAAOH,OACjDtB,UAAU,2HAA0HC,SAAA,EAEpIF,EAAAA,EAAAA,KAAA,UAAQuB,MAAM,SAAQrB,SAAEhH,EAAE,SAAU,cACpC8G,EAAAA,EAAAA,KAAA,UAAQuB,MAAM,UAASrB,SAAEhH,EAAE,UAAW,eACtC8G,EAAAA,EAAAA,KAAA,UAAQuB,MAAM,UAASrB,SAAEhH,EAAE,UAAW,eACtC8G,EAAAA,EAAAA,KAAA,UAAQuB,MAAM,QAAOrB,SAAEhH,EAAE,QAAS,gBAEpCiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,8BAAD0B,OAA+B5B,IACtDmH,UAAU,oFAAmFC,SAAA,EAE7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ/G,EAAE,gBAAiB,8BAO5BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EAEnEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2JAA0JC,SAAA,EACvKC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEAEfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+IAA8IC,UACtH,QAArCjI,EAAA+B,EAAmBE,KAAKmE,qBAAa,IAAApG,GAAa,QAAbC,EAArCD,EAAuC+F,mBAAW,IAAA9F,OAAb,EAArCA,EAAoDqG,YAAa,iBAGtEyB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4DAA2DC,SACtEhH,EAAE,mBAAoB,yBAEzBiH,EAAAA,EAAAA,MAAA,KAAGF,UAAU,sDAAqDC,SAAA,EAC1B,QAArC/H,EAAA6B,EAAmBE,KAAKmE,qBAAa,IAAAlG,GAAa,QAAbC,EAArCD,EAAuC6F,mBAAW,IAAA5F,OAAb,EAArCA,EAAoDkG,QAAS,EAAE,UAElE0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDhH,EAAE,oBAAqB,4BAK5BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iKAAgKC,SAAA,EAC7KC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mDAAkDC,UAC/DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEAEfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+IAA8IC,UACtH,QAArC7H,EAAA2B,EAAmBE,KAAKmE,qBAAa,IAAAhG,GAAS,QAATC,EAArCD,EAAuCmG,eAAO,IAAAlG,OAAT,EAArCA,EAAgDiG,YAAa,eAGlEyB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8DAA6DC,SACxEhH,EAAE,UAAW,oBAEhBiH,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wDAAuDC,SAAA,EAC5B,QAArC3H,EAAAyB,EAAmBE,KAAKmE,qBAAa,IAAA9F,GAAS,QAATC,EAArCD,EAAuCiG,eAAO,IAAAhG,OAAT,EAArCA,EAAgDiG,OAAQ,EAAE,QAE7DuB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAC3DhH,EAAE,qBAAsB,6BAK7BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mJAAkJC,SAAA,EAC/JC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,UAC3DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEAEfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,SAAC,kBAI1DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,0DAAyDC,SACpEhH,EAAE,mBAAoB,yBAEzBiH,EAAAA,EAAAA,MAAA,KAAGF,UAAU,oDAAmDC,SAAA,EACxB,QAArCzH,EAAAuB,EAAmBE,KAAKmE,qBAAa,IAAA5F,GAAW,QAAXC,EAArCD,EAAuCyF,iBAAS,IAAAxF,OAAX,EAArCA,EAAkDgG,UAAW,EAAE,UAElEsB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8CAA6CC,SACvDhH,EAAE,iBAAkB,yBAKzBiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iKAAgKC,SAAA,EAC7KC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEAEfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,SAAC,kBAIhEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gEAA+DC,SAC1EhH,EAAE,yBAA0B,8BAE/BiH,EAAAA,EAAAA,MAAA,KAAGF,UAAU,0DAAyDC,SAAA,EAC9B,QAArCvH,EAAAqB,EAAmBE,KAAKmE,qBAAa,IAAA1F,GAAwB,QAAxBC,EAArCD,EAAuCiG,8BAAsB,IAAAhG,OAAxB,EAArCA,EAA+DiG,QAAS,EAAE,WAE7EmB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7DhH,EAAE,oBAAqB,4BAK5BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mKAAkKC,SAAA,EAC/KC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEAEfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+CAA8CC,SAAC,kBAIhEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,gEAA+DC,SAC1EhH,EAAE,kBAAmB,uBAExBiH,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wDAAuDC,SAAA,EAC5B,QAArCrH,EAAAmB,EAAmBE,KAAKmE,qBAAa,IAAAxF,OAAA,EAArCA,EAAuCuF,kBAAmB,EAAE,QAE/D4B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7DhH,EAAE,iBAAkB,4BAM3BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,6EAA4EC,SAAA,EACxFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZ/G,EAAE,oBAAqB,0BAG1BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sDAAqDC,UAClEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+CAA8CC,SAAEhH,EAAE,mBAAoB,wBACpF8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDAAqDC,SAAElG,EAAmBE,KAAKgF,mBAC5Fc,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAEhH,EAAE,mBAAoB,4BAGjFiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDC,UACpEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+CAA8CC,SAAEhH,EAAE,kBAAmB,uBACnF8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wDAAuDC,SACjElG,EAAmBE,KAAK8E,iBAAiBtB,OAAS,EAAI,MAAQ,QAEjEsC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAEhH,EAAE,mBAAoB,4BAGjFiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+CAA8CC,SAAEhH,EAAE,kBAAmB,uBACnF8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DAAyDC,SAAC,UACvEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAEhH,EAAE,iBAAkB,qCA9LrF8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEhH,EAAE,uBAAwB,6BAE7B8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDhH,EAAE,2BAA4B,gEAEjCiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,8BAAD0B,OAA+B5B,IACtDmH,UAAU,kFAAiFC,SAAA,EAE3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ/G,EAAE,wBAAyB,qCA0LzB,YAAdM,IACC2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhH,EAAE,kBAAmB,uBAExBiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,YAAa,cAAc,QACjF8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SAAExG,EAAQ4B,YAAYG,gBAEhF0E,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,mBAAoB,qBAAqB,QAC/F8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CAA2CC,SAAExG,EAAQ4B,YAAYQ,uBAEhFqE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAEhH,EAAE,kBAAmB,oBAAoB,QAC7FiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,oDAAmDC,SAAA,CAAExG,EAAQ4B,YAAYC,SAAS,UAEpGyE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAU,+BACV0B,MAAO,CAAEC,MAAM,GAADlH,OAAKhB,EAAQ4B,YAAYC,SAAQ,uBAQ3D4E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhH,EAAE,eAAgB,oBAErB8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,SACnDxG,EAAQ4B,YAAYW,MAAM0E,IAAI,CAACkB,EAAMT,KACpCjB,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,kEAAiEC,SAAA,EAC1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gCAA+BC,SAAE2B,MAFzCT,YAWL,iBAAd5H,IACC2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhH,EAAE,qBAAsB,0BAE3BiH,EAAAA,EAAAA,MAAA,UAAQF,UAAU,kFAAiFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ/G,EAAE,sBAAuB,iCAIhC8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBxG,EAAQiD,aAAagE,IAAKmB,IACzB3B,EAAAA,EAAAA,MAAA,OAA0BF,UAAU,+FAA8FC,SAAA,EAChIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wFAAuFC,UACpGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wDAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SAAE4B,EAAYC,QACvE5B,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpD4B,EAAY/D,KAAK,OAAK+D,EAAYrD,KAAK,WAAIqD,EAAYE,oBAI9DhC,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAAvF,OACU,cAAvBoH,EAAYG,OAAyB,uEACd,cAAvBH,EAAYG,OAAyB,mEACrC,4EACC/B,SACAhH,EAAE4I,EAAYG,OAAQH,EAAYG,YAjB7BH,EAAYhJ,YA2BjB,cAAdU,IACC2G,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhH,EAAE,mBAAoB,wBAEzBiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,KAEP,MAAM8B,EAAYC,SAASC,cAAc,SACzCF,EAAUH,KAAO,OACjBG,EAAUG,OAAS,kCACnBH,EAAUI,UAAW,EACrBJ,EAAUK,SAAYd,IACpB,MAAMe,EAAQC,MAAMC,KAAKjB,EAAEC,OAAOc,OAC9BA,EAAM9E,OAAS,IAEjBN,QAAQuF,IAAI,6BAA8BH,GAC1CnF,EAAAA,GAAM1C,QAAQzB,EAAE,gBAAgB,GAADwB,OAAK8H,EAAM9E,OAAM,oCAIpDwE,EAAUU,SAEZ3C,UAAU,oFAAmFC,SAAA,EAE7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBACZ/G,EAAE,iBAAkB,4BAI3B8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClExG,EAAQmD,UAAU8D,IAAKwB,IACtBhC,EAAAA,EAAAA,MAAA,OAAuBF,UAAU,+FAA8FC,SAAA,EAC7HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sFAAqFC,UAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDAEfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAEiC,EAASU,WAEvE7C,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAAEiC,EAASpF,QACzEiD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SAAEiC,EAASpE,QACvEoC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UAAQF,UAAU,8FAA6FC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBACZ/G,EAAE,OAAQ,YAEbiH,EAAAA,EAAAA,MAAA,UAAQF,UAAU,8FAA6FC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ/G,EAAE,WAAY,oBAhBXiJ,EAASrJ,aA2B7BkH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sDACZ/G,EAAE,eAAgB,qBAErBiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAElFC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,oBAAqB,CAC3CuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,mHAAkHC,SAAA,EAE5HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,sBAAuB,8BAIlEiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,aAAD0B,OAAchB,EAAQ4G,KAAO5G,EAAQZ,GAAE,SAAS,CACrEyH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,EACpBsC,SAAU,iBAGd7C,UAAU,qHAAoHC,SAAA,EAE9HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,cAAe,sBAI1DiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,uBAAwB,CAC9CuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,uHAAsHC,SAAA,EAEhIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,eAAgB,uBAG3DiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,sBAAuB,CAC7CuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,mHAAkHC,SAAA,EAE5HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8BACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,eAAgB,sBAG3DiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,kCAAmC,CACzDuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,mHAAkHC,SAAA,EAE5HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,yBAA0B,kCAGrEiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,8BAAD0B,OAA+BhB,EAAQ4G,KAAO5G,EAAQZ,IAAM,CACjFyH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,uHAAsHC,SAAA,EAEhIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,qBAAsB,6BAIjEiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,sBAAuB,CAC7CuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,uHAAsHC,SAAA,EAEhIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,aAAc,sBAGzDiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,4BAA6B,CACnDuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,mHAAkHC,SAAA,EAE5HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,oBAAqB,4BAIhEiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,wBAAyB,CAC/CuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,EACpBuC,aAAa,IAAIpH,MAAOC,cAAcC,MAAM,KAAK,MAGrDoE,UAAU,yHAAwHC,SAAA,EAElIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,gBAAiB,wBAI5DiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,mBAAoB,CAC1CuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,KAGxBP,UAAU,uHAAsHC,SAAA,EAEhIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,gBAAiB,wBAI5DiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpH,EAAS,8BAA+B,CACrDuH,MAAO,CACL7G,QAASA,EACT8G,oBAAoB,EACpBwC,SAAU,eAGd/C,UAAU,iHAAgHC,SAAA,EAE1HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,iBAAkB,yBAI7DiH,EAAAA,EAAAA,MAAA,UACEC,QA17BU6C,KACpBpJ,GAAiB,GACjBE,EAAY,KAy7BFkG,UAAU,uHAAsHC,SAAA,EAEhIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,UAAW,kBAGtDiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,KAEP8C,OAAOC,SAETlD,UAAU,mHAAkHC,SAAA,EAE5HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,eAAgB,uBAG3DiH,EAAAA,EAAAA,MAAA,UACEC,QAASA,KAEP,MAAMhF,EAAcgI,KAAKC,UAAU3J,EAAS,KAAM,GAC5C4J,EAAW,IAAIC,KAAK,CAACnI,GAAc,CAAE2G,KAAM,qBAC3CyB,EAAMC,IAAIC,gBAAgBJ,GAC1BK,EAAOxB,SAASC,cAAc,KACpCuB,EAAKC,KAAOJ,EACZG,EAAKE,SAAQ,WAAAnJ,OAAchB,EAAQZ,GAAE,iBACrCqJ,SAAS2B,KAAKC,YAAYJ,GAC1BA,EAAKf,QACLT,SAAS2B,KAAKE,YAAYL,GAC1BF,IAAIQ,gBAAgBT,IAEtBvD,UAAU,uHAAsHC,SAAA,EAEhIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sBAAqBC,SAAEhH,EAAE,aAAc,4BAO9DU,IACCoG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFAAgFC,UAC7FF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iEAAgEC,UAC7EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sDAAqDC,SAAA,EACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZ/G,EAAE,UAAW,gBAEhB8G,EAAAA,EAAAA,KAAA,UACEI,QAAS9C,EACT2C,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAIjBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,gDAA+CC,SAAA,CACzDhH,EAAE,UAAW,WAAW,MAAE8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACnF/G,EAAQO,EAAQqD,KAAOrD,EAAQsD,aAGpCmD,EAAAA,EAAAA,MAAA,KAAGF,UAAU,gDAA+CC,SAAA,CACzDhH,EAAE,OAAQ,QAAQ,MAAE8G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,UAC7E,IAAIvE,MAAOuI,8BAKlB/D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/EhH,EAAE,OAAQ,WAEb8G,EAAAA,EAAAA,KAAA,YACEuB,MAAOzH,EACP0H,SAAWC,GAAM1H,EAAY0H,EAAEC,OAAOH,OACtC4C,KAAM,EACNlE,UAAU,kLACVmE,YAAalL,EAAE,YAAa,iCAIhCiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,UACEI,QAAS9C,EACT2C,UAAU,yIAAwIC,SAEjJhH,EAAE,SAAU,aAEfiH,EAAAA,EAAAA,MAAA,UACEC,QAlhCOiE,KACjBvK,EAASwK,QAEXlH,QAAQuF,IAAI,2BAA4BjJ,EAAQZ,GAAI,QAASgB,GAC7DuD,EAAAA,GAAM1C,QAAQzB,EAAE,YAAa,4BAC7BW,GAAiB,GACjBE,EAAY,KAEZsD,EAAAA,GAAMF,MAAMjE,EAAE,eAAgB,yBA2gClB+G,UAAU,sFAAqFC,SAAA,EAE/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ/G,EAAE,WAAY,8BAx4B3BiH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mDACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEhH,EAAE,kBAAmB,wBAExB8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDhH,EAAE,sBAAuB,+CAE5B8G,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMpH,EAAS,aACxBiH,UAAU,kFAAiFC,SAE1FhH,EAAE,iBAAkB,0BA3BzB8G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhH,EAAE,kBAAmB,wBAExB8G,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5ChH,EAAE,yBAA0B,oD", "sources": ["pages/Patients/PatientDetails.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\nimport { apiHelpers as api } from '../../services/api';\n\nconst PatientDetails = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { t, isRTL } = useLanguage();\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('overview');\n  const [patient, setPatient] = useState(null);\n  const [showNoteModal, setShowNoteModal] = useState(false);\n  const [noteText, setNoteText] = useState('');\n  const [clinicalIndicators, setClinicalIndicators] = useState({\n    loading: false,\n    data: null,\n    selectedPeriod: '6months'\n  });\n\n  useEffect(() => {\n    loadPatientDetails();\n  }, [id]);\n\n  // Mock patient data for development\n  const getMockPatientData = (patientId) => {\n    const mockPatients = {\n      '1': {\n        id: 1,\n        _id: '1',\n        name: 'أحمد محمد الأحمد',\n        nameEn: 'Ahmed Mohammed Al-Ahmed',\n        age: 8,\n        gender: 'male',\n        dateOfBirth: '2016-03-15',\n        nationalId: '**********',\n        phone: '+966 50 123 4567',\n        email: '<EMAIL>',\n        therapyInfo: {\n          progress: 75,\n          therapyTypes: ['Physical Therapy', 'Occupational Therapy'],\n          startDate: '2024-01-15',\n          primaryTherapist: 'Dr. Sarah Al-Rashid',\n          goals: [\n            'Improve balance and coordination',\n            'Enhance communication skills',\n            'Develop social interaction abilities'\n          ]\n        },\n        medicalInfo: {\n          diagnosis: 'Autism Spectrum Disorder',\n          diagnosisAr: 'اضطراب طيف التوحد',\n          severity: 'moderate',\n          allergies: ['Peanuts', 'Shellfish'],\n          medications: ['Risperidone 0.5mg', 'Melatonin 3mg']\n        },\n        emergencyContact: {\n          name: 'محمد الأحمد',\n          nameEn: 'Mohammed Al-Ahmed',\n          relationship: 'Father',\n          phone: '+966 50 765 4321'\n        },\n        appointments: [\n          {\n            id: 1,\n            type: 'Physical Therapy',\n            date: '2024-01-22',\n            time: '10:00 AM',\n            therapist: 'Dr. Sarah Al-Rashid',\n            status: 'scheduled'\n          },\n          {\n            id: 2,\n            type: 'Occupational Therapy',\n            date: '2024-01-20',\n            time: '2:00 PM',\n            therapist: 'Dr. Ahmed Al-Mansouri',\n            status: 'completed'\n          }\n        ],\n        treatments: [],\n        documents: [\n          {\n            id: 1,\n            name: 'Initial Assessment Report',\n            date: '2024-01-15',\n            size: '2.3 MB',\n            type: 'pdf'\n          },\n          {\n            id: 2,\n            name: 'Progress Report - Month 1',\n            date: '2024-02-15',\n            size: '1.8 MB',\n            type: 'pdf'\n          }\n        ]\n      },\n      '2': {\n        id: 2,\n        _id: '2',\n        name: 'فاطمة علي السالم',\n        nameEn: 'Fatima Ali Al-Salem',\n        age: 12,\n        gender: 'female',\n        dateOfBirth: '2012-07-22',\n        nationalId: '**********',\n        phone: '+966 50 234 5678',\n        email: '<EMAIL>',\n        therapyInfo: {\n          progress: 60,\n          therapyTypes: ['Physical Therapy', 'Speech Therapy'],\n          startDate: '2024-01-10',\n          primaryTherapist: 'Dr. Ahmed Al-Mansouri',\n          goals: [\n            'Improve motor skills',\n            'Enhance speech clarity',\n            'Increase independence in daily activities'\n          ]\n        },\n        medicalInfo: {\n          diagnosis: 'Cerebral Palsy',\n          diagnosisAr: 'الشلل الدماغي',\n          severity: 'mild',\n          allergies: [],\n          medications: ['Baclofen 10mg', 'Vitamin D3']\n        },\n        emergencyContact: {\n          name: 'علي السالم',\n          nameEn: 'Ali Al-Salem',\n          relationship: 'Father',\n          phone: '+966 50 876 5432'\n        },\n        appointments: [\n          {\n            id: 3,\n            type: 'Physical Therapy',\n            date: '2024-01-21',\n            time: '11:00 AM',\n            therapist: 'Dr. Ahmed Al-Mansouri',\n            status: 'scheduled'\n          }\n        ],\n        treatments: [],\n        documents: [\n          {\n            id: 3,\n            name: 'Medical History',\n            date: '2024-01-10',\n            size: '1.5 MB',\n            type: 'pdf'\n          }\n        ]\n      }\n    };\n\n    return mockPatients[patientId] || null;\n  };\n\n  const loadPatientDetails = async () => {\n    setLoading(true);\n    try {\n      // Real API call to get patient details\n      const response = await api.get(`/patients/${id}`);\n      if (response.success) {\n        // Add default structures if missing\n        const patientData = {\n          ...response.data,\n          therapyInfo: response.data.therapyInfo || {\n            progress: 0,\n            therapyTypes: [],\n            startDate: response.data.registrationDate || new Date().toISOString().split('T')[0],\n            primaryTherapist: response.data.primaryTherapist?.firstName && response.data.primaryTherapist?.lastName\n              ? `${response.data.primaryTherapist.firstName} ${response.data.primaryTherapist.lastName}`\n              : 'Not Assigned',\n            goals: []\n          },\n          medicalInfo: response.data.medicalInfo || {\n            diagnosis: response.data.medicalHistory?.primaryDiagnosis || 'Not specified',\n            diagnosisAr: response.data.medicalHistory?.primaryDiagnosis || 'غير محدد',\n            severity: 'mild',\n            allergies: response.data.medicalHistory?.allergies || [],\n            medications: response.data.medicalHistory?.currentMedications || []\n          },\n          appointments: response.data.appointments || [],\n          treatments: response.data.treatments || [],\n          documents: response.data.documents || [],\n          emergencyContact: response.data.emergencyContact || {\n            name: 'Not provided',\n            nameEn: 'Not provided',\n            relationship: 'Not specified',\n            phone: 'Not provided'\n          }\n        };\n        setPatient(patientData);\n      } else {\n        throw new Error(response.message || 'Failed to load patient details');\n      }\n    } catch (error) {\n      console.error('Error loading patient details:', error);\n\n      // In development mode, use mock data instead of showing error\n      if (process.env.NODE_ENV === 'development') {\n        console.log('Using mock patient data in development mode');\n        const mockPatient = getMockPatientData(id);\n        if (mockPatient) {\n          setPatient(mockPatient);\n        } else {\n          // If no mock data for this ID, create a generic patient\n          setPatient({\n            id: id,\n            _id: id,\n            name: 'مريض تجريبي',\n            nameEn: 'Test Patient',\n            age: 10,\n            gender: 'male',\n            dateOfBirth: '2014-01-01',\n            nationalId: '**********',\n            phone: '+966 50 000 0000',\n            email: '<EMAIL>',\n            therapyInfo: {\n              progress: 50,\n              therapyTypes: ['Physical Therapy'],\n              startDate: '2024-01-01',\n              primaryTherapist: 'Dr. Test Therapist',\n              goals: ['Improve overall function']\n            },\n            medicalInfo: {\n              diagnosis: 'General Condition',\n              diagnosisAr: 'حالة عامة',\n              severity: 'mild',\n              allergies: [],\n              medications: []\n            },\n            emergencyContact: {\n              name: 'جهة اتصال طوارئ',\n              nameEn: 'Emergency Contact',\n              relationship: 'Parent',\n              phone: '+966 50 000 0001'\n            },\n            appointments: [],\n            treatments: [],\n            documents: []\n          });\n        }\n      } else {\n        toast.error(t('errorLoadingPatient', 'Error loading patient details'));\n        setPatient(null);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleAddNote = () => {\n    setShowNoteModal(true);\n    setNoteText('');\n  };\n\n  const handleSaveNote = () => {\n    if (noteText.trim()) {\n      // Here you would typically save the note to your backend\n      console.log('Saving note for patient:', patient.id, 'Note:', noteText);\n      toast.success(t('noteSaved', 'Note saved successfully'));\n      setShowNoteModal(false);\n      setNoteText('');\n    } else {\n      toast.error(t('noteRequired', 'Please enter a note'));\n    }\n  };\n\n  const handleCancelNote = () => {\n    setShowNoteModal(false);\n    setNoteText('');\n  };\n\n  // Load clinical indicators data\n  const loadClinicalIndicators = async (period = '6months') => {\n    try {\n      setClinicalIndicators(prev => ({ ...prev, loading: true }));\n\n      const response = await api.get(`/clinical-indicators/patient/${id}/progress?period=${period}`);\n\n      if (response.success) {\n        setClinicalIndicators(prev => ({\n          ...prev,\n          data: response.data,\n          selectedPeriod: period,\n          loading: false\n        }));\n      } else {\n        // Mock clinical indicators data\n        const mockData = {\n          progressTrend: [\n            {\n              date: '2024-01-15',\n              bergBalance: 35,\n              tugTime: 18.5,\n              painLevel: 7,\n              fimScore: 85,\n              overallProgress: 65\n            },\n            {\n              date: '2024-02-15',\n              bergBalance: 42,\n              tugTime: 15.2,\n              painLevel: 5,\n              fimScore: 95,\n              overallProgress: 75\n            },\n            {\n              date: '2024-03-15',\n              bergBalance: 48,\n              tugTime: 12.8,\n              painLevel: 3,\n              fimScore: 105,\n              overallProgress: 85\n            }\n          ],\n          currentStatus: {\n            bergBalance: { score: 48, riskLevel: 'low-risk' },\n            tugTest: { time: 12.8, riskLevel: 'normal' },\n            painLevel: { current: 3, average: 3.5 },\n            functionalIndependence: { total: 105, motor: 78, cognitive: 27 },\n            overallProgress: 85\n          },\n          improvementAreas: ['Balance and Stability', 'Pain Management', 'Functional Independence'],\n          concernAreas: [],\n          assessmentCount: 3,\n          functionalIndependenceComparison: {\n            beforeTreatment: {\n              totalScore: 65,\n              motorScore: 45,\n              cognitiveScore: 20,\n              date: '2024-01-15',\n              level: 'moderate-dependence'\n            },\n            afterTreatment: {\n              totalScore: 105,\n              motorScore: 78,\n              cognitiveScore: 27,\n              date: '2024-03-15',\n              level: 'minimal-dependence'\n            },\n            improvement: {\n              totalImprovement: 40,\n              motorImprovement: 33,\n              cognitiveImprovement: 7,\n              improvementPercentage: 61.5,\n              treatmentDuration: 60\n            }\n          }\n        };\n\n        setClinicalIndicators(prev => ({\n          ...prev,\n          data: mockData,\n          selectedPeriod: period,\n          loading: false\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading clinical indicators:', error);\n      toast.error(t('errorLoadingClinicalIndicators', 'Error loading clinical indicators'));\n      setClinicalIndicators(prev => ({ ...prev, loading: false }));\n    }\n  };\n\n  // Handle tab change with lazy loading\n  const handleTabChange = (tabId) => {\n    setActiveTab(tabId);\n    if (tabId === 'clinicalIndicators' && !clinicalIndicators.data) {\n      loadClinicalIndicators();\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!patient) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            {t('patientNotFound', 'Patient not found')}\n          </h2>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('patientNotFoundMessage', 'The requested patient could not be found.')}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!patient) {\n    return (\n      <div className=\"text-center py-12\">\n        <i className=\"fas fa-user-slash text-4xl text-gray-400 mb-4\"></i>\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n          {t('patientNotFound', 'Patient Not Found')}\n        </h3>\n        <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n          {t('patientNotFoundDesc', 'The requested patient could not be found')}\n        </p>\n        <button\n          onClick={() => navigate('/patients')}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          {t('backToPatients', 'Back to Patients')}\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={() => navigate('/patients')}\n              className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            >\n              <i className={`fas fa-arrow-${isRTL ? 'right' : 'left'} text-gray-600 dark:text-gray-400`}></i>\n            </button>\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                {isRTL ? patient.name : patient.nameEn}\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                {t('patientId', 'Patient ID')}: {patient.id} • {t('age', 'Age')}: {patient.age}\n              </p>\n            </div>\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={() => navigate(`/patients/${patient._id || patient.id}/edit`, {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-edit mr-2\"></i>\n              {t('editPatient', 'Edit Patient')}\n            </button>\n            <button\n              onClick={() => navigate('/appointments/new', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n            >\n              <i className=\"fas fa-calendar-plus mr-2\"></i>\n              {t('newAppointment', 'New Appointment')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Patient Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-heartbeat text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {patient.therapyInfo.progress}%\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('overallProgress', 'Overall Progress')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-calendar-check text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {patient.appointments.length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('totalAppointments', 'Total Appointments')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-user-md text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {patient.therapyInfo.therapyTypes.length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('therapyTypes', 'Therapy Types')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-file-medical text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {patient.documents.length}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('documents', 'Documents')}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-600\">\n          <nav className=\"-mb-px flex space-x-8\">\n            {[\n              { id: 'overview', label: t('overview', 'Overview'), icon: 'fas fa-user' },\n              { id: 'medical', label: t('medicalInfo', 'Medical Info'), icon: 'fas fa-stethoscope' },\n              { id: 'clinicalIndicators', label: t('clinicalIndicators', 'Clinical Indicators'), icon: 'fas fa-chart-bar' },\n              { id: 'therapy', label: t('therapyProgress', 'Therapy Progress'), icon: 'fas fa-chart-line' },\n              { id: 'appointments', label: t('appointments', 'Appointments'), icon: 'fas fa-calendar' },\n              { id: 'documents', label: t('documents', 'Documents'), icon: 'fas fa-folder' }\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => handleTabChange(tab.id)}\n                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'overview' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Personal Information */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('personalInformation', 'Personal Information')}\n            </h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('fullName', 'Full Name')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">\n                  {isRTL ? patient.name : patient.nameEn}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('dateOfBirth', 'Date of Birth')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">{patient.dateOfBirth}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('gender', 'Gender')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">\n                  {t(patient.gender, patient.gender)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('nationalId', 'National ID')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">{patient.nationalId}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('phone', 'Phone')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">{patient.phone}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('email', 'Email')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">{patient.email}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Emergency Contact */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('emergencyContact', 'Emergency Contact')}\n            </h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('name', 'Name')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">\n                  {isRTL ? patient.emergencyContact.name : patient.emergencyContact.nameEn}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('relationship', 'Relationship')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">\n                  {t(patient.emergencyContact.relationship, patient.emergencyContact.relationship)}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('phone', 'Phone')}:</span>\n                <span className=\"font-medium text-gray-900 dark:text-white\">\n                  {patient.emergencyContact.phone}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Medical Information Tab */}\n      {activeTab === 'medical' && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('diagnosis', 'Diagnosis')}\n            </h3>\n            <div className=\"space-y-3\">\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('primaryDiagnosis', 'Primary Diagnosis')}:</span>\n                <p className=\"font-medium text-gray-900 dark:text-white mt-1\">\n                  {isRTL ? patient.medicalInfo.diagnosisAr : patient.medicalInfo.diagnosis}\n                </p>\n              </div>\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('severity', 'Severity')}:</span>\n                <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${\n                  patient.medicalInfo.severity === 'mild' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                  patient.medicalInfo.severity === 'moderate' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :\n                  'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'\n                }`}>\n                  {t(patient.medicalInfo.severity, patient.medicalInfo.severity)}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('allergiesAndMedications', 'Allergies & Medications')}\n            </h3>\n            <div className=\"space-y-4\">\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('allergies', 'Allergies')}:</span>\n                <div className=\"mt-2 flex flex-wrap gap-2\">\n                  {patient.medicalInfo.allergies.map((allergy, index) => (\n                    <span key={index} className=\"px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 text-sm rounded-full\">\n                      {allergy}\n                    </span>\n                  ))}\n                </div>\n              </div>\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('currentMedications', 'Current Medications')}:</span>\n                <ul className=\"mt-2 space-y-1\">\n                  {patient.medicalInfo.medications.map((medication, index) => (\n                    <li key={index} className=\"text-gray-900 dark:text-white\">• {medication}</li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Clinical Indicators Tab */}\n      {activeTab === 'clinicalIndicators' && (\n        <div className=\"space-y-6\">\n          {clinicalIndicators.loading ? (\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <div className=\"flex items-center justify-center py-12\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n              </div>\n            </div>\n          ) : !clinicalIndicators.data ? (\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n              <div className=\"text-center py-12\">\n                <i className=\"fas fa-chart-line text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('noClinicalIndicators', 'No Clinical Indicators')}\n                </h3>\n                <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n                  {t('noClinicalIndicatorsDesc', 'No clinical indicators assessments found for this patient')}\n                </p>\n                <button\n                  onClick={() => navigate(`/forms/clinical-indicators/${id}`)}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <i className=\"fas fa-plus mr-2\"></i>\n                  {t('createFirstAssessment', 'Create First Assessment')}\n                </button>\n              </div>\n            </div>\n          ) : (\n            <>\n              {/* Controls */}\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {t('clinicalIndicators', 'Clinical Indicators')}\n                  </h3>\n                  <div className=\"flex items-center space-x-4\">\n                    <select\n                      value={clinicalIndicators.selectedPeriod}\n                      onChange={(e) => loadClinicalIndicators(e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"1month\">{t('1month', '1 Month')}</option>\n                      <option value=\"3months\">{t('3months', '3 Months')}</option>\n                      <option value=\"6months\">{t('6months', '6 Months')}</option>\n                      <option value=\"1year\">{t('1year', '1 Year')}</option>\n                    </select>\n                    <button\n                      onClick={() => navigate(`/forms/clinical-indicators/${id}`)}\n                      className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n                    >\n                      <i className=\"fas fa-plus mr-2\"></i>\n                      {t('newAssessment', 'New Assessment')}\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Key Metrics Cards */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\n                {/* Berg Balance Scale */}\n                <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow border border-blue-200 dark:border-blue-700 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg\">\n                      <i className=\"fas fa-balance-scale text-blue-600 dark:text-blue-400 text-2xl\"></i>\n                    </div>\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200\">\n                      {clinicalIndicators.data.currentStatus?.bergBalance?.riskLevel || 'low-risk'}\n                    </span>\n                  </div>\n                  <h3 className=\"text-sm font-medium text-blue-600 dark:text-blue-400 mb-1\">\n                    {t('bergBalanceScale', 'Berg Balance Scale')}\n                  </h3>\n                  <p className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n                    {clinicalIndicators.data.currentStatus?.bergBalance?.score || 0}/56\n                  </p>\n                  <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\n                    {t('balanceAssessment', 'Balance Assessment')}\n                  </p>\n                </div>\n\n                {/* Timed Up and Go */}\n                <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow border border-green-200 dark:border-green-700 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg\">\n                      <i className=\"fas fa-walking text-green-600 dark:text-green-400 text-2xl\"></i>\n                    </div>\n                    <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200\">\n                      {clinicalIndicators.data.currentStatus?.tugTest?.riskLevel || 'normal'}\n                    </span>\n                  </div>\n                  <h3 className=\"text-sm font-medium text-green-600 dark:text-green-400 mb-1\">\n                    {t('tugTest', 'Timed Up & Go')}\n                  </h3>\n                  <p className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\n                    {clinicalIndicators.data.currentStatus?.tugTest?.time || 0}s\n                  </p>\n                  <p className=\"text-xs text-green-600 dark:text-green-400 mt-1\">\n                    {t('mobilityAssessment', 'Mobility Assessment')}\n                  </p>\n                </div>\n\n                {/* Pain Level */}\n                <div className=\"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg shadow border border-red-200 dark:border-red-700 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"p-3 bg-red-100 dark:bg-red-900/40 rounded-lg\">\n                      <i className=\"fas fa-heartbeat text-red-600 dark:text-red-400 text-2xl\"></i>\n                    </div>\n                    <div className=\"text-xs text-red-600 dark:text-red-400\">\n                      VAS Scale\n                    </div>\n                  </div>\n                  <h3 className=\"text-sm font-medium text-red-600 dark:text-red-400 mb-1\">\n                    {t('currentPainLevel', 'Current Pain Level')}\n                  </h3>\n                  <p className=\"text-2xl font-bold text-red-900 dark:text-red-100\">\n                    {clinicalIndicators.data.currentStatus?.painLevel?.current || 0}/10\n                  </p>\n                  <p className=\"text-xs text-red-600 dark:text-red-400 mt-1\">\n                    {t('painAssessment', 'Pain Assessment')}\n                  </p>\n                </div>\n\n                {/* Functional Independence */}\n                <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow border border-orange-200 dark:border-orange-700 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg\">\n                      <i className=\"fas fa-user-check text-orange-600 dark:text-orange-400 text-2xl\"></i>\n                    </div>\n                    <div className=\"text-xs text-orange-600 dark:text-orange-400\">\n                      FIM Score\n                    </div>\n                  </div>\n                  <h3 className=\"text-sm font-medium text-orange-600 dark:text-orange-400 mb-1\">\n                    {t('functionalIndependence', 'Functional Independence')}\n                  </h3>\n                  <p className=\"text-2xl font-bold text-orange-900 dark:text-orange-100\">\n                    {clinicalIndicators.data.currentStatus?.functionalIndependence?.total || 0}/126\n                  </p>\n                  <p className=\"text-xs text-orange-600 dark:text-orange-400 mt-1\">\n                    {t('independenceLevel', 'Independence Level')}\n                  </p>\n                </div>\n\n                {/* Overall Progress */}\n                <div className=\"bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg shadow border border-purple-200 dark:border-purple-700 p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg\">\n                      <i className=\"fas fa-chart-line text-purple-600 dark:text-purple-400 text-2xl\"></i>\n                    </div>\n                    <div className=\"text-xs text-purple-600 dark:text-purple-400\">\n                      Composite\n                    </div>\n                  </div>\n                  <h3 className=\"text-sm font-medium text-purple-600 dark:text-purple-400 mb-1\">\n                    {t('overallProgress', 'Overall Progress')}\n                  </h3>\n                  <p className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                    {clinicalIndicators.data.currentStatus?.overallProgress || 0}%\n                  </p>\n                  <p className=\"text-xs text-purple-600 dark:text-purple-400 mt-1\">\n                    {t('compositeScore', 'Composite Score')}\n                  </p>\n                </div>\n              </div>\n\n              {/* Assessment Summary */}\n              <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center\">\n                  <i className=\"fas fa-clipboard-list text-gray-600 dark:text-gray-400 mr-2\"></i>\n                  {t('assessmentSummary', 'Assessment Summary')}\n                </h3>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  <div className=\"text-center\">\n                    <div className=\"p-4 bg-blue-100 dark:bg-blue-900/40 rounded-lg mb-3\">\n                      <i className=\"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-3xl\"></i>\n                    </div>\n                    <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('totalAssessments', 'Total Assessments')}</h4>\n                    <p className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">{clinicalIndicators.data.assessmentCount}</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('inSelectedPeriod', 'In selected period')}</p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <div className=\"p-4 bg-green-100 dark:bg-green-900/40 rounded-lg mb-3\">\n                      <i className=\"fas fa-trending-up text-green-600 dark:text-green-400 text-3xl\"></i>\n                    </div>\n                    <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('improvementRate', 'Improvement Rate')}</h4>\n                    <p className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                      {clinicalIndicators.data.improvementAreas.length > 0 ? '85%' : '0%'}\n                    </p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('acrossAllMetrics', 'Across all metrics')}</p>\n                  </div>\n\n                  <div className=\"text-center\">\n                    <div className=\"p-4 bg-purple-100 dark:bg-purple-900/40 rounded-lg mb-3\">\n                      <i className=\"fas fa-award text-purple-600 dark:text-purple-400 text-3xl\"></i>\n                    </div>\n                    <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">{t('complianceScore', 'Compliance Score')}</h4>\n                    <p className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">100%</p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('carfCbahiHipaa', 'CARF, CBAHI, HIPAA')}</p>\n                  </div>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Therapy Progress Tab */}\n      {activeTab === 'therapy' && (\n        <div className=\"space-y-6\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('therapyOverview', 'Therapy Overview')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('startDate', 'Start Date')}:</span>\n                <p className=\"font-medium text-gray-900 dark:text-white\">{patient.therapyInfo.startDate}</p>\n              </div>\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('primaryTherapist', 'Primary Therapist')}:</span>\n                <p className=\"font-medium text-gray-900 dark:text-white\">{patient.therapyInfo.primaryTherapist}</p>\n              </div>\n              <div>\n                <span className=\"text-gray-600 dark:text-gray-400\">{t('overallProgress', 'Overall Progress')}:</span>\n                <div className=\"mt-2\">\n                  <div className=\"flex items-center justify-between mb-1\">\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{patient.therapyInfo.progress}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className=\"bg-blue-600 h-2 rounded-full\"\n                      style={{ width: `${patient.therapyInfo.progress}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('therapyGoals', 'Therapy Goals')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {patient.therapyInfo.goals.map((goal, index) => (\n                <div key={index} className=\"flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                  <i className=\"fas fa-bullseye text-blue-600 dark:text-blue-400 mr-3\"></i>\n                  <span className=\"text-gray-900 dark:text-white\">{goal}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Appointments Tab */}\n      {activeTab === 'appointments' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('appointmentHistory', 'Appointment History')}\n              </h3>\n              <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                <i className=\"fas fa-plus mr-2\"></i>\n                {t('scheduleAppointment', 'Schedule Appointment')}\n              </button>\n            </div>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-4\">\n              {patient.appointments.map((appointment) => (\n                <div key={appointment.id} className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\n                      <i className=\"fas fa-calendar text-blue-600 dark:text-blue-400\"></i>\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">{appointment.type}</h4>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {appointment.date} at {appointment.time} • {appointment.therapist}\n                      </p>\n                    </div>\n                  </div>\n                  <span className={`px-3 py-1 text-sm font-medium rounded-full ${\n                    appointment.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :\n                    appointment.status === 'scheduled' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' :\n                    'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'\n                  }`}>\n                    {t(appointment.status, appointment.status)}\n                  </span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Documents Tab */}\n      {activeTab === 'documents' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('patientDocuments', 'Patient Documents')}\n              </h3>\n              <button\n                onClick={() => {\n                  // Create a file input element\n                  const fileInput = document.createElement('input');\n                  fileInput.type = 'file';\n                  fileInput.accept = '.pdf,.doc,.docx,.jpg,.jpeg,.png';\n                  fileInput.multiple = true;\n                  fileInput.onchange = (e) => {\n                    const files = Array.from(e.target.files);\n                    if (files.length > 0) {\n                      // Handle file upload here\n                      console.log('Files selected for upload:', files);\n                      toast.success(t('filesSelected', `${files.length} file(s) selected for upload`));\n                      // TODO: Implement actual file upload logic\n                    }\n                  };\n                  fileInput.click();\n                }}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <i className=\"fas fa-upload mr-2\"></i>\n                {t('uploadDocument', 'Upload Document')}\n              </button>\n            </div>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {patient.documents.map((document) => (\n                <div key={document.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center\">\n                      <i className=\"fas fa-file-pdf text-red-600 dark:text-red-400\"></i>\n                    </div>\n                    <span className=\"text-xs text-gray-500 dark:text-gray-400\">{document.size}</span>\n                  </div>\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-1\">{document.name}</h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">{document.date}</p>\n                  <div className=\"flex space-x-2\">\n                    <button className=\"flex-1 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors\">\n                      <i className=\"fas fa-eye mr-1\"></i>\n                      {t('view', 'View')}\n                    </button>\n                    <button className=\"flex-1 px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors\">\n                      <i className=\"fas fa-download mr-1\"></i>\n                      {t('download', 'Download')}\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Quick Actions Horizontal Bar */}\n      <div className=\"mt-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            <i className=\"fas fa-bolt text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('quickActions', 'Quick Actions')}\n          </h3>\n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3\">\n            {/* Appointment Actions */}\n            <button\n              onClick={() => navigate('/appointments/new', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-calendar-plus text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('scheduleAppointment', 'Schedule Appointment')}</span>\n            </button>\n\n            {/* Patient Management */}\n            <button\n              onClick={() => navigate(`/patients/${patient._id || patient.id}/edit`, {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true,\n                  editMode: 'single-page'  // Flag for single-page editing\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-edit text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('editPatient', 'Edit Patient')}</span>\n            </button>\n\n            {/* Assessment Forms */}\n            <button\n              onClick={() => navigate('/forms/pt-assessment', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-clipboard-check text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('ptAssessment', 'PT Assessment')}</span>\n            </button>\n\n            <button\n              onClick={() => navigate('/forms/reassessment', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-redo text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('reassessment', 'Reassessment')}</span>\n            </button>\n\n            <button\n              onClick={() => navigate('/forms/special-needs-assessment', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-heart text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('specialNeedsAssessment', 'Special Needs Assessment')}</span>\n            </button>\n\n            <button\n              onClick={() => navigate(`/forms/clinical-indicators/${patient._id || patient.id}`, {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-chart-bar text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('clinicalIndicators', 'Clinical Indicators')}</span>\n            </button>\n\n            {/* Treatment Plans */}\n            <button\n              onClick={() => navigate('/forms/plan-of-care', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-route text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('planOfCare', 'Plan of Care')}</span>\n            </button>\n\n            <button\n              onClick={() => navigate('/forms/adaptive-treatment', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-puzzle-piece text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('adaptiveTreatment', 'Adaptive Treatment')}</span>\n            </button>\n\n            {/* Progress Tracking */}\n            <button\n              onClick={() => navigate('/forms/daily-progress', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true,\n                  defaultDate: new Date().toISOString().split('T')[0]\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-chart-line text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('dailyProgress', 'Daily Progress')}</span>\n            </button>\n\n            {/* Education */}\n            <button\n              onClick={() => navigate('/forms/education', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-graduation-cap text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('educationForm', 'Education Form')}</span>\n            </button>\n\n            {/* Compliance Forms */}\n            <button\n              onClick={() => navigate('/compliance/carf-assessment', {\n                state: {\n                  patient: patient,\n                  fromPatientProfile: true,\n                  facility: 'PT-Clinic'\n                }\n              })}\n              className=\"flex flex-col items-center p-4 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-shield-alt text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('carfAssessment', 'CARF Assessment')}</span>\n            </button>\n\n            {/* Utility Actions */}\n            <button\n              onClick={handleAddNote}\n              className=\"flex flex-col items-center p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-sticky-note text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('addNote', 'Add Note')}</span>\n            </button>\n\n            <button\n              onClick={() => {\n                // Handle print functionality\n                window.print();\n              }}\n              className=\"flex flex-col items-center p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-print text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('printProfile', 'Print Profile')}</span>\n            </button>\n\n            <button\n              onClick={() => {\n                // Handle export functionality\n                const patientData = JSON.stringify(patient, null, 2);\n                const dataBlob = new Blob([patientData], { type: 'application/json' });\n                const url = URL.createObjectURL(dataBlob);\n                const link = document.createElement('a');\n                link.href = url;\n                link.download = `patient-${patient.id}-profile.json`;\n                document.body.appendChild(link);\n                link.click();\n                document.body.removeChild(link);\n                URL.revokeObjectURL(url);\n              }}\n              className=\"flex flex-col items-center p-4 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-center\"\n            >\n              <i className=\"fas fa-download text-xl mb-2\"></i>\n              <span className=\"text-xs font-medium\">{t('exportData', 'Export Data')}</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Add Note Modal */}\n      {showNoteModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  <i className=\"fas fa-sticky-note text-purple-600 dark:text-purple-400 mr-2\"></i>\n                  {t('addNote', 'Add Note')}\n                </h3>\n                <button\n                  onClick={handleCancelNote}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              </div>\n\n              <div className=\"mb-4\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  {t('patient', 'Patient')}: <span className=\"font-medium text-gray-900 dark:text-white\">\n                    {isRTL ? patient.name : patient.nameEn}\n                  </span>\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  {t('date', 'Date')}: <span className=\"font-medium text-gray-900 dark:text-white\">\n                    {new Date().toLocaleDateString()}\n                  </span>\n                </p>\n              </div>\n\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('note', 'Note')}\n                </label>\n                <textarea\n                  value={noteText}\n                  onChange={(e) => setNoteText(e.target.value)}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none\"\n                  placeholder={t('enterNote', 'Enter your note here...')}\n                />\n              </div>\n\n              <div className=\"flex justify-end space-x-3\">\n                <button\n                  onClick={handleCancelNote}\n                  className=\"px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  {t('cancel', 'Cancel')}\n                </button>\n                <button\n                  onClick={handleSaveNote}\n                  className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n                >\n                  <i className=\"fas fa-save mr-2\"></i>\n                  {t('saveNote', 'Save Note')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PatientDetails;\n"], "names": ["PatientDetails", "_clinicalIndicators$d", "_clinicalIndicators$d2", "_clinicalIndicators$d3", "_clinicalIndicators$d4", "_clinicalIndicators$d5", "_clinicalIndicators$d6", "_clinicalIndicators$d7", "_clinicalIndicators$d8", "_clinicalIndicators$d9", "_clinicalIndicators$d0", "_clinicalIndicators$d1", "_clinicalIndicators$d10", "_clinicalIndicators$d11", "id", "useParams", "navigate", "useNavigate", "t", "isRTL", "useLanguage", "loading", "setLoading", "useState", "activeTab", "setActiveTab", "patient", "setPatient", "showNoteModal", "setShowNoteModal", "noteText", "setNoteText", "clinicalIndicators", "setClinicalIndicators", "data", "<PERSON><PERSON><PERSON><PERSON>", "useEffect", "loadPatientDetails", "async", "response", "api", "get", "concat", "success", "Error", "message", "_response$data$primar", "_response$data$primar2", "_response$data$medica", "_response$data$medica2", "_response$data$medica3", "_response$data$medica4", "patientData", "_objectSpread", "therapyInfo", "progress", "therapyTypes", "startDate", "registrationDate", "Date", "toISOString", "split", "primaryTherapist", "firstName", "lastName", "goals", "medicalInfo", "diagnosis", "medicalHistory", "primaryDiagnosis", "diagnosisAr", "severity", "allergies", "medications", "currentMedications", "appointments", "treatments", "documents", "emergencyContact", "name", "nameEn", "relationship", "phone", "error", "console", "toast", "handleCancelNote", "loadClinicalIndicators", "period", "arguments", "length", "undefined", "prev", "mockData", "progressTrend", "date", "bergBalance", "tugTime", "painLevel", "fimScore", "overallProgress", "currentStatus", "score", "riskLevel", "tugTest", "time", "current", "average", "functionalIndependence", "total", "motor", "cognitive", "improvementAreas", "concern<PERSON>reas", "assessmentCount", "functionalIndependenceComparison", "beforeTreatment", "totalScore", "motorScore", "cognitiveScore", "level", "afterTreatment", "improvement", "totalImprovement", "motorImprovement", "cognitiveImprovement", "improvementPercentage", "treatmentDuration", "_jsx", "className", "children", "_jsxs", "onClick", "age", "_id", "state", "fromPatientProfile", "label", "icon", "map", "tab", "handleTabChange", "tabId", "dateOfBirth", "gender", "nationalId", "email", "allergy", "index", "medication", "_Fragment", "value", "onChange", "e", "target", "style", "width", "goal", "appointment", "type", "therapist", "status", "fileInput", "document", "createElement", "accept", "multiple", "onchange", "files", "Array", "from", "log", "click", "size", "editMode", "defaultDate", "facility", "handleAddNote", "window", "print", "JSON", "stringify", "dataBlob", "Blob", "url", "URL", "createObjectURL", "link", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "toLocaleDateString", "rows", "placeholder", "handleSaveNote", "trim"], "sourceRoot": ""}