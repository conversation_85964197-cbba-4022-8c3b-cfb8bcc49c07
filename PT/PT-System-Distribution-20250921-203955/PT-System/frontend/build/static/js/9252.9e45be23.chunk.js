"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9252],{930:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(2555);const s=new class{constructor(){this.baseURL="http://localhost:5001/api/v1",this.storagePrefix="physioflow_",this.initializeStorage()}getAuthToken(){return localStorage.getItem("token")||"demo-token"}async apiRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=this.getAuthToken(),s="".concat(this.baseURL).concat(e),i={headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)}},n=(0,a.A)((0,a.A)((0,a.A)({},i),t),{},{headers:(0,a.A)((0,a.A)({},i.headers),t.headers)});try{const e=await fetch(s,n);if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return await e.json()}catch(o){return console.warn("API request failed for ".concat(e,", using localStorage fallback:"),o.message),this.fallbackToLocalStorage(e,t)}}fallbackToLocalStorage(e,t){const r=t.method||"GET",a=t.body?JSON.parse(t.body):null;return e.includes("/bodymap")?this.handleBodyMapFallback(e,r,a):e.includes("/communication")?this.handleCommunicationFallback(e,r,a):e.includes("/exercise-programs")?this.handleExerciseFallback(e,r,a):e.includes("/ai-interactions")?this.handleAIFallback(e,r,a):{success:!1,error:"Endpoint not supported in fallback mode"}}initializeStorage(){this.getItem("initialized")||(this.setItem("initialized",!0),this.setItem("patients",this.getDefaultPatients()),this.setItem("bodyMapData",{}),this.setItem("communicationHistory",[]),this.setItem("exercisePrograms",[]),this.setItem("aiInteractions",[]))}getItem(e){try{const t=localStorage.getItem(this.storagePrefix+e);return t?JSON.parse(t):null}catch(t){return console.error("Error getting item from storage:",t),null}}setItem(e,t){try{return localStorage.setItem(this.storagePrefix+e,JSON.stringify(t)),!0}catch(r){return console.error("Error setting item in storage:",r),!1}}removeItem(e){try{return localStorage.removeItem(this.storagePrefix+e),!0}catch(t){return console.error("Error removing item from storage:",t),!1}}getDefaultPatients(){return[{id:"demo-patient-001",name:"Ahmed Mohammed",nameAr:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f",age:28,gender:"male",condition:"Cerebral Palsy",conditionAr:"\u0627\u0644\u0634\u0644\u0644 \u0627\u0644\u062f\u0645\u0627\u063a\u064a",phone:"+966501234567",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","whatsapp"],language:"en",quietHours:{start:"22:00",end:"08:00"}},createdAt:(new Date).toISOString()},{id:"demo-patient-002",name:"Sarah Johnson",nameAr:"\u0633\u0627\u0631\u0629 \u062c\u0648\u0646\u0633\u0648\u0646",age:35,gender:"female",condition:"Spinal Cord Injury",conditionAr:"\u0625\u0635\u0627\u0628\u0629 \u0627\u0644\u062d\u0628\u0644 \u0627\u0644\u0634\u0648\u0643\u064a",phone:"+**********",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","sms"],language:"en",quietHours:{start:"21:00",end:"07:00"}},createdAt:(new Date).toISOString()}]}async getPatients(){return new Promise(e=>{setTimeout(()=>{e(this.getItem("patients")||[])},100)})}async getPatient(e){return new Promise(t=>{setTimeout(()=>{const r=(this.getItem("patients")||[]).find(t=>t.id===e);t(r||null)},100)})}async saveBodyMapData(e,t){try{const r=await this.apiRequest("/bodymap",{method:"POST",body:JSON.stringify((0,a.A)({patientId:e},t))});return r.success?r.data:null}catch(r){return new Promise(r=>{setTimeout(()=>{const s=this.getItem("bodyMapData")||{};s[e]=(0,a.A)((0,a.A)({},t),{},{timestamp:(new Date).toISOString()}),this.setItem("bodyMapData",s),r(!0)},200)})}}async getBodyMapData(e){return new Promise(t=>{setTimeout(()=>{const r=this.getItem("bodyMapData")||{};t(r[e]||null)},100)})}async sendMessage(e){try{const t=await this.apiRequest("/communication",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const r=this.getItem("communicationHistory")||[],s=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString(),status:"sent"});r.push(s),this.setItem("communicationHistory",r),t(s)},500)})}}async getCommunicationHistory(e){try{const t=await this.apiRequest("/communication/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const r=(this.getItem("communicationHistory")||[]).filter(t=>t.patientId===e);t(r)},100)})}}async saveExerciseProgram(e){try{const t=await this.apiRequest("/exercise-programs",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const r=this.getItem("exercisePrograms")||[],s=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{createdAt:(new Date).toISOString()});r.push(s),this.setItem("exercisePrograms",r),t(s)},300)})}}async getExercisePrograms(e){try{const t=await this.apiRequest("/exercise-programs/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const r=(this.getItem("exercisePrograms")||[]).filter(t=>t.patientId===e);t(r)},100)})}}async saveAIInteraction(e){try{const t=await this.apiRequest("/ai-interactions",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const r=this.getItem("aiInteractions")||[],s=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString()});r.push(s),this.setItem("aiInteractions",r),t(s)},100)})}}async getAIInteractions(e){try{const t=e?"/ai-interactions/".concat(e):"/ai-interactions",r=await this.apiRequest(t);return r.success?r.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const r=this.getItem("aiInteractions")||[],a=e?r.filter(t=>t.patientId===e):r;t(a)},100)})}}async getAnalytics(){return new Promise(e=>{setTimeout(()=>{const t=this.getItem("aiInteractions")||[],r=this.getItem("communicationHistory")||[],s=this.getItem("exercisePrograms")||[],i=this.getItem("bodyMapData")||{},n={totalAIQueries:t.length,totalCommunications:r.length,totalExercisePrograms:s.length,totalBodyMapAssessments:Object.keys(i).length,recentActivity:[...t.slice(-5).map(e=>(0,a.A)({type:"ai"},e)),...r.slice(-5).map(e=>(0,a.A)({type:"communication"},e)),...s.slice(-5).map(e=>(0,a.A)({type:"exercise"},e))].sort((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)).slice(0,10)};e(n)},200)})}clearAllData(){["patients","bodyMapData","communicationHistory","exercisePrograms","aiInteractions"].forEach(e=>this.removeItem(e)),this.removeItem("initialized"),this.initializeStorage()}exportData(){return{patients:this.getItem("patients"),bodyMapData:this.getItem("bodyMapData"),communicationHistory:this.getItem("communicationHistory"),exercisePrograms:this.getItem("exercisePrograms"),aiInteractions:this.getItem("aiInteractions"),exportedAt:(new Date).toISOString()}}importData(e){try{return e.patients&&this.setItem("patients",e.patients),e.bodyMapData&&this.setItem("bodyMapData",e.bodyMapData),e.communicationHistory&&this.setItem("communicationHistory",e.communicationHistory),e.exercisePrograms&&this.setItem("exercisePrograms",e.exercisePrograms),e.aiInteractions&&this.setItem("aiInteractions",e.aiInteractions),!0}catch(t){return console.error("Error importing data:",t),!1}}delay(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;return new Promise(t=>setTimeout(t,e))}}},6711:(e,t,r)=>{r.d(t,{A:()=>g,h:()=>u});var a=r(2555),s=r(5043),i=r(7921),n=r(3768),o=r(579);const l=e=>{let{onSelectExercise:t,selectedExercises:r=[],patientId:a}=e;const{t:l,isRTL:c}=(0,i.o)(),[d,u]=(0,s.useState)([]),[g,h]=(0,s.useState)([]),[x,p]=(0,s.useState)(!0),[b,y]=(0,s.useState)(""),[f,v]=(0,s.useState)("all"),[w,j]=(0,s.useState)("all"),[k,N]=(0,s.useState)("all"),A=[{id:"all",name:l("allCategories","All Categories"),nameAr:"\u062c\u0645\u064a\u0639 \u0627\u0644\u0641\u0626\u0627\u062a"},{id:"strength",name:l("strength","Strength Training"),nameAr:"\u062a\u062f\u0631\u064a\u0628 \u0627\u0644\u0642\u0648\u0629"},{id:"flexibility",name:l("flexibility","Flexibility"),nameAr:"\u0627\u0644\u0645\u0631\u0648\u0646\u0629"},{id:"balance",name:l("balance","Balance"),nameAr:"\u0627\u0644\u062a\u0648\u0627\u0632\u0646"},{id:"coordination",name:l("coordination","Coordination"),nameAr:"\u0627\u0644\u062a\u0646\u0633\u064a\u0642"},{id:"cardio",name:l("cardio","Cardiovascular"),nameAr:"\u0627\u0644\u0642\u0644\u0628 \u0648\u0627\u0644\u0623\u0648\u0639\u064a\u0629"},{id:"mobility",name:l("mobility","Mobility"),nameAr:"\u0627\u0644\u062d\u0631\u0643\u0629"},{id:"posture",name:l("posture","Posture"),nameAr:"\u0627\u0644\u0648\u0636\u0639\u064a\u0629"},{id:"pain_relief",name:l("painRelief","Pain Relief"),nameAr:"\u062a\u062e\u0641\u064a\u0641 \u0627\u0644\u0623\u0644\u0645"}],S=[{id:"all",name:l("allLevels","All Levels"),nameAr:"\u062c\u0645\u064a\u0639 \u0627\u0644\u0645\u0633\u062a\u0648\u064a\u0627\u062a"},{id:"beginner",name:l("beginner","Beginner"),nameAr:"\u0645\u0628\u062a\u062f\u0626"},{id:"intermediate",name:l("intermediate","Intermediate"),nameAr:"\u0645\u062a\u0648\u0633\u0637"},{id:"advanced",name:l("advanced","Advanced"),nameAr:"\u0645\u062a\u0642\u062f\u0645"}],I=[{id:"all",name:l("allBodyParts","All Body Parts"),nameAr:"\u062c\u0645\u064a\u0639 \u0623\u062c\u0632\u0627\u0621 \u0627\u0644\u062c\u0633\u0645"},{id:"upper_body",name:l("upperBody","Upper Body"),nameAr:"\u0627\u0644\u062c\u0632\u0621 \u0627\u0644\u0639\u0644\u0648\u064a"},{id:"lower_body",name:l("lowerBody","Lower Body"),nameAr:"\u0627\u0644\u062c\u0632\u0621 \u0627\u0644\u0633\u0641\u0644\u064a"},{id:"core",name:l("core","Core"),nameAr:"\u0627\u0644\u062c\u0630\u0639"},{id:"full_body",name:l("fullBody","Full Body"),nameAr:"\u0627\u0644\u062c\u0633\u0645 \u0643\u0627\u0645\u0644\u0627\u064b"},{id:"neck",name:l("neck","Neck"),nameAr:"\u0627\u0644\u0631\u0642\u0628\u0629"},{id:"shoulders",name:l("shoulders","Shoulders"),nameAr:"\u0627\u0644\u0623\u0643\u062a\u0627\u0641"},{id:"arms",name:l("arms","Arms"),nameAr:"\u0627\u0644\u0630\u0631\u0627\u0639\u064a\u0646"},{id:"back",name:l("back","Back"),nameAr:"\u0627\u0644\u0638\u0647\u0631"},{id:"legs",name:l("legs","Legs"),nameAr:"\u0627\u0644\u0633\u0627\u0642\u064a\u0646"}];(0,s.useEffect)(()=>{P()},[]),(0,s.useEffect)(()=>{C()},[d,b,f,w,k]);const P=async()=>{p(!0);try{u([{id:1,name:"Shoulder Blade Squeeze",nameAr:"\u0636\u063a\u0637 \u0644\u0648\u062d \u0627\u0644\u0643\u062a\u0641",category:"strength",difficulty:"beginner",bodyPart:"shoulders",duration:"10-15 reps",equipment:"None",equipmentAr:"\u0644\u0627 \u0634\u064a\u0621",image:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80",video:"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",instructions:{en:["Sit or stand with your arms at your sides","Squeeze your shoulder blades together","Hold for 5 seconds","Slowly release and repeat"],ar:["\u0627\u062c\u0644\u0633 \u0623\u0648 \u0642\u0641 \u0645\u0639 \u0630\u0631\u0627\u0639\u064a\u0643 \u0639\u0644\u0649 \u062c\u0627\u0646\u0628\u064a\u0643","\u0627\u0636\u063a\u0637 \u0644\u0648\u062d\u064a \u0643\u062a\u0641\u0643 \u0645\u0639\u0627\u064b","\u0627\u0645\u0633\u0643 \u0644\u0645\u062f\u0629 5 \u062b\u0648\u0627\u0646","\u062d\u0631\u0631 \u0628\u0628\u0637\u0621 \u0648\u0643\u0631\u0631"]},benefits:{en:"Improves posture, strengthens upper back muscles, reduces shoulder tension",ar:"\u064a\u062d\u0633\u0646 \u0627\u0644\u0648\u0636\u0639\u064a\u0629\u060c \u064a\u0642\u0648\u064a \u0639\u0636\u0644\u0627\u062a \u0627\u0644\u0638\u0647\u0631 \u0627\u0644\u0639\u0644\u0648\u064a\u0629\u060c \u064a\u0642\u0644\u0644 \u062a\u0648\u062a\u0631 \u0627\u0644\u0643\u062a\u0641"},precautions:{en:"Avoid if you have acute shoulder injury. Stop if you feel pain.",ar:"\u062a\u062c\u0646\u0628 \u0625\u0630\u0627 \u0643\u0627\u0646 \u0644\u062f\u064a\u0643 \u0625\u0635\u0627\u0628\u0629 \u062d\u0627\u062f\u0629 \u0641\u064a \u0627\u0644\u0643\u062a\u0641. \u062a\u0648\u0642\u0641 \u0625\u0630\u0627 \u0634\u0639\u0631\u062a \u0628\u0623\u0644\u0645."},modifications:{en:"Can be done seated for those with balance issues",ar:"\u064a\u0645\u0643\u0646 \u0627\u0644\u0642\u064a\u0627\u0645 \u0628\u0647 \u062c\u0627\u0644\u0633\u0627\u064b \u0644\u0645\u0646 \u0644\u062f\u064a\u0647\u0645 \u0645\u0634\u0627\u0643\u0644 \u0641\u064a \u0627\u0644\u062a\u0648\u0627\u0632\u0646"},tags:["posture","upper_back","beginner_friendly"],createdBy:"Dr. Sarah Al-Rashid",approved:!0,rating:4.8,usageCount:156},{id:2,name:"Ankle Circles",nameAr:"\u062f\u0648\u0627\u0626\u0631 \u0627\u0644\u0643\u0627\u062d\u0644",category:"mobility",difficulty:"beginner",bodyPart:"legs",duration:"10 circles each direction",equipment:"None",equipmentAr:"\u0644\u0627 \u0634\u064a\u0621",image:"https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&q=80",video:"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",instructions:{en:["Sit comfortably with one leg extended","Lift your foot slightly off the ground","Slowly rotate your ankle in a circular motion","Complete 10 circles clockwise, then 10 counterclockwise","Repeat with the other foot"],ar:["\u0627\u062c\u0644\u0633 \u0628\u0634\u0643\u0644 \u0645\u0631\u064a\u062d \u0645\u0639 \u0633\u0627\u0642 \u0648\u0627\u062d\u062f\u0629 \u0645\u0645\u062f\u0648\u062f\u0629","\u0627\u0631\u0641\u0639 \u0642\u062f\u0645\u0643 \u0642\u0644\u064a\u0644\u0627\u064b \u0639\u0646 \u0627\u0644\u0623\u0631\u0636","\u0642\u0645 \u0628\u062a\u062f\u0648\u064a\u0631 \u0643\u0627\u062d\u0644\u0643 \u0628\u0628\u0637\u0621 \u0641\u064a \u062d\u0631\u0643\u0629 \u062f\u0627\u0626\u0631\u064a\u0629","\u0623\u0643\u0645\u0644 10 \u062f\u0648\u0627\u0626\u0631 \u0641\u064a \u0627\u062a\u062c\u0627\u0647 \u0639\u0642\u0627\u0631\u0628 \u0627\u0644\u0633\u0627\u0639\u0629\u060c \u062b\u0645 10 \u0639\u0643\u0633 \u0639\u0642\u0627\u0631\u0628 \u0627\u0644\u0633\u0627\u0639\u0629","\u0643\u0631\u0631 \u0645\u0639 \u0627\u0644\u0642\u062f\u0645 \u0627\u0644\u0623\u062e\u0631\u0649"]},benefits:{en:"Improves ankle mobility, reduces stiffness, enhances circulation",ar:"\u064a\u062d\u0633\u0646 \u062d\u0631\u0643\u0629 \u0627\u0644\u0643\u0627\u062d\u0644\u060c \u064a\u0642\u0644\u0644 \u0627\u0644\u062a\u0635\u0644\u0628\u060c \u064a\u0639\u0632\u0632 \u0627\u0644\u062f\u0648\u0631\u0629 \u0627\u0644\u062f\u0645\u0648\u064a\u0629"},precautions:{en:"Move slowly and gently. Stop if you experience pain.",ar:"\u062a\u062d\u0631\u0643 \u0628\u0628\u0637\u0621 \u0648\u0644\u0637\u0641. \u062a\u0648\u0642\u0641 \u0625\u0630\u0627 \u0634\u0639\u0631\u062a \u0628\u0623\u0644\u0645."},modifications:{en:"Can be done while lying down if sitting is uncomfortable",ar:"\u064a\u0645\u0643\u0646 \u0627\u0644\u0642\u064a\u0627\u0645 \u0628\u0647 \u0623\u062b\u0646\u0627\u0621 \u0627\u0644\u0627\u0633\u062a\u0644\u0642\u0627\u0621 \u0625\u0630\u0627 \u0643\u0627\u0646 \u0627\u0644\u062c\u0644\u0648\u0633 \u063a\u064a\u0631 \u0645\u0631\u064a\u062d"},tags:["ankle_mobility","circulation","easy"],createdBy:"Dr. Ahmed Al-Rashid",approved:!0,rating:4.6,usageCount:203},{id:3,name:"Wall Push-ups",nameAr:"\u062a\u0645\u0631\u064a\u0646 \u0627\u0644\u0636\u063a\u0637 \u0639\u0644\u0649 \u0627\u0644\u062d\u0627\u0626\u0637",category:"strength",difficulty:"beginner",bodyPart:"upper_body",duration:"8-12 reps",equipment:"Wall",equipmentAr:"\u062d\u0627\u0626\u0637",image:"https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400&h=300&fit=crop&q=80",video:"https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",instructions:{en:["Stand arm's length from a wall","Place palms flat against the wall at shoulder height","Lean forward and push back to starting position","Keep your body straight throughout the movement"],ar:["\u0642\u0641 \u0639\u0644\u0649 \u0645\u0633\u0627\u0641\u0629 \u0630\u0631\u0627\u0639 \u0645\u0646 \u0627\u0644\u062d\u0627\u0626\u0637","\u0636\u0639 \u0631\u0627\u062d\u062a\u064a \u064a\u062f\u064a\u0643 \u0645\u0633\u0637\u062d\u062a\u064a\u0646 \u0639\u0644\u0649 \u0627\u0644\u062d\u0627\u0626\u0637 \u0639\u0644\u0649 \u0645\u0633\u062a\u0648\u0649 \u0627\u0644\u0643\u062a\u0641","\u0627\u0646\u062d\u0646 \u0644\u0644\u0623\u0645\u0627\u0645 \u062b\u0645 \u0627\u062f\u0641\u0639 \u0644\u0644\u0639\u0648\u062f\u0629 \u0625\u0644\u0649 \u0627\u0644\u0648\u0636\u0639 \u0627\u0644\u0623\u0648\u0644\u064a","\u062d\u0627\u0641\u0638 \u0639\u0644\u0649 \u0627\u0633\u062a\u0642\u0627\u0645\u0629 \u062c\u0633\u0645\u0643 \u0637\u0648\u0627\u0644 \u0627\u0644\u062d\u0631\u0643\u0629"]},benefits:{en:"Strengthens chest, shoulders, and arms. Improves upper body strength.",ar:"\u064a\u0642\u0648\u064a \u0627\u0644\u0635\u062f\u0631 \u0648\u0627\u0644\u0643\u062a\u0641\u064a\u0646 \u0648\u0627\u0644\u0630\u0631\u0627\u0639\u064a\u0646. \u064a\u062d\u0633\u0646 \u0642\u0648\u0629 \u0627\u0644\u062c\u0632\u0621 \u0627\u0644\u0639\u0644\u0648\u064a \u0645\u0646 \u0627\u0644\u062c\u0633\u0645."},precautions:{en:"Avoid if you have wrist or shoulder problems.",ar:"\u062a\u062c\u0646\u0628 \u0625\u0630\u0627 \u0643\u0627\u0646 \u0644\u062f\u064a\u0643 \u0645\u0634\u0627\u0643\u0644 \u0641\u064a \u0627\u0644\u0645\u0639\u0635\u0645 \u0623\u0648 \u0627\u0644\u0643\u062a\u0641."},modifications:{en:"Move closer to wall for easier version, further for harder version",ar:"\u0627\u0642\u062a\u0631\u0628 \u0645\u0646 \u0627\u0644\u062d\u0627\u0626\u0637 \u0644\u0644\u0646\u0633\u062e\u0629 \u0627\u0644\u0623\u0633\u0647\u0644\u060c \u0627\u0628\u062a\u0639\u062f \u0644\u0644\u0646\u0633\u062e\u0629 \u0627\u0644\u0623\u0635\u0639\u0628"},tags:["upper_body_strength","functional","home_exercise"],createdBy:"Dr. Fatima Al-Zahra",approved:!0,rating:4.7,usageCount:189}])}catch(e){console.error("Error loading exercises:",e),n.Ay.error(l("errorLoadingExercises","Error loading exercises"))}finally{p(!1)}},C=()=>{let e=d;b&&(e=e.filter(e=>e.name.toLowerCase().includes(b.toLowerCase())||e.nameAr.includes(b)||e.tags.some(e=>e.toLowerCase().includes(b.toLowerCase())))),"all"!==f&&(e=e.filter(e=>e.category===f)),"all"!==w&&(e=e.filter(e=>e.difficulty===w)),"all"!==k&&(e=e.filter(e=>e.bodyPart===k)),h(e)},D=e=>{t&&t(e),n.Ay.success(l("exerciseAdded","Exercise added to program"))},E=e=>{switch(e){case"beginner":return"bg-green-100 text-green-800";case"intermediate":return"bg-yellow-100 text-yellow-800";case"advanced":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}};return x?(0,o.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:l("loadingExercises","Loading exercises...")})]}):(0,o.jsx)("div",{className:"exercise-library",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-dumbbell mr-2 text-blue-600"}),l("exerciseLibrary","Exercise Library")]}),(0,o.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[g.length," ",l("exercises","exercises")]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[(0,o.jsx)("div",{children:(0,o.jsx)("input",{type:"text",placeholder:l("searchExercises","Search exercises..."),value:b,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})}),(0,o.jsx)("div",{children:(0,o.jsx)("select",{value:f,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:A.map(e=>(0,o.jsx)("option",{value:e.id,children:c?e.nameAr:e.name},e.id))})}),(0,o.jsx)("div",{children:(0,o.jsx)("select",{value:w,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:S.map(e=>(0,o.jsx)("option",{value:e.id,children:c?e.nameAr:e.name},e.id))})}),(0,o.jsx)("div",{children:(0,o.jsx)("select",{value:k,onChange:e=>N(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:I.map(e=>(0,o.jsx)("option",{value:e.id,children:c?e.nameAr:e.name},e.id))})})]}),0===g.length?(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("i",{className:"fas fa-search text-4xl text-gray-400 mb-4"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:l("noExercisesFound","No exercises found matching your criteria")})]}):(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(e=>{return(0,o.jsx)(m,{exercise:e,onSelect:D,isSelected:(t=e.id,r.some(e=>e.id===t)),getDifficultyColor:E},e.id);var t})})]})})},c=e=>({"shoulder-blade-squeeze":"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80","ankle-circles":"https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&q=80","wall-pushups":"https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400&h=300&fit=crop&q=80"}[e.id]||{strength:"https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400&h=300&fit=crop&q=80",flexibility:"https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&q=80",balance:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80",coordination:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80",cardio:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80",mobility:"https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&q=80",posture:"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80",pain_relief:"https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=400&h=300&fit=crop&q=80"}[e.category]||"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80"),d=e=>({strength:"dumbbell",flexibility:"expand-arrows-alt",balance:"balance-scale",coordination:"sync",cardio:"heartbeat",mobility:"walking",posture:"user-check",pain_relief:"hand-holding-medical"}[e.category]||"dumbbell"),m=e=>{let{exercise:t,onSelect:r,isSelected:a,getDifficultyColor:n}=e;const{t:l,isRTL:m}=(0,i.o)(),[u,g]=(0,s.useState)(!1),[h,x]=(0,s.useState)(!1),[p,b]=(0,s.useState)(!1);return(0,o.jsxs)("div",{className:"border rounded-lg overflow-hidden transition-all duration-200 hover:shadow-lg ".concat(a?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600"),children:[(0,o.jsxs)("div",{className:"relative h-48 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-gray-700 dark:to-gray-600",children:[h?(0,o.jsxs)("div",{className:"w-full h-full flex flex-col items-center justify-center text-gray-500 dark:text-gray-400",children:[(0,o.jsx)("i",{className:"fas fa-".concat(d(t)," text-4xl mb-2")}),(0,o.jsx)("span",{className:"text-sm font-medium text-center px-2",children:m?t.nameAr:t.name})]}):(0,o.jsx)("img",{src:t.image||c(t),alt:m?t.nameAr:t.name,className:"w-full h-full object-cover",onError:()=>x(!0)}),(0,o.jsx)("div",{className:"absolute top-2 right-2",children:(0,o.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(n(t.difficulty)),children:l(t.difficulty,t.difficulty)})}),t.video&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"absolute top-2 left-2",children:(0,o.jsxs)("span",{className:"px-2 py-1 text-xs font-medium rounded-full bg-red-500 text-white",children:[(0,o.jsx)("i",{className:"fas fa-play mr-1"}),l("video","Video")]})}),(0,o.jsx)("button",{onClick:()=>b(!0),className:"absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity duration-200",children:(0,o.jsx)("div",{className:"bg-white bg-opacity-90 rounded-full p-3",children:(0,o.jsx)("i",{className:"fas fa-play text-blue-600 text-xl"})})})]}),a&&(0,o.jsx)("div",{className:"absolute bottom-2 left-2",children:(0,o.jsx)("i",{className:"fas fa-check-circle text-blue-600 text-xl bg-white rounded-full"})})]}),(0,o.jsxs)("div",{className:"p-4",children:[(0,o.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-2",children:m?t.nameAr:t.name}),(0,o.jsxs)("div",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-clock mr-2"}),t.duration]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-tools mr-2"}),m?t.equipmentAr:t.equipment]}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-star mr-2 text-yellow-500"}),t.rating," (",t.usageCount," ",l("uses","uses"),")"]})]}),(0,o.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,o.jsx)("button",{onClick:()=>g(!u),className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:u?l("hideDetails","Hide Details"):l("viewDetails","View Details")}),(0,o.jsx)("button",{onClick:()=>r(t),disabled:a,className:"px-3 py-1 text-sm rounded-lg transition-colors ".concat(a?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:a?l("added","Added"):l("addExercise","Add Exercise")})]}),u&&(0,o.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h5",{className:"font-medium text-gray-900 dark:text-white mb-1",children:[l("instructions","Instructions"),":"]}),(0,o.jsx)("ol",{className:"list-decimal list-inside space-y-1 text-gray-600 dark:text-gray-400",children:(m?t.instructions.ar:t.instructions.en).map((e,t)=>(0,o.jsx)("li",{children:e},t))})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("h5",{className:"font-medium text-gray-900 dark:text-white mb-1",children:[l("benefits","Benefits"),":"]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:m?t.benefits.ar:t.benefits.en})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("h5",{className:"font-medium text-gray-900 dark:text-white mb-1",children:[l("precautions","Precautions"),":"]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:m?t.precautions.ar:t.precautions.en})]})]})})]}),p&&t.video&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",onClick:()=>b(!1),children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-4 max-w-4xl w-full mx-4",onClick:e=>e.stopPropagation(),children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:m?t.nameAr:t.name}),(0,o.jsx)("button",{onClick:()=>b(!1),className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:(0,o.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,o.jsx)("div",{className:"aspect-video",children:(0,o.jsx)("video",{className:"w-full h-full rounded-lg",controls:!0,autoPlay:!0,src:t.video,children:"Your browser does not support the video tag."})})]})})]})},u=l,g=e=>{let{patientId:t,onSave:r,initialProgram:m=null}=e;const{t:u,isRTL:g}=(0,i.o)(),[h,x]=(0,s.useState)((null===m||void 0===m?void 0:m.exercises)||[]),[p,b]=(0,s.useState)((null===m||void 0===m?void 0:m.name)||""),[y,f]=(0,s.useState)((null===m||void 0===m?void 0:m.description)||""),[v,w]=(0,s.useState)((null===m||void 0===m?void 0:m.duration)||"4"),[j,k]=(0,s.useState)((null===m||void 0===m?void 0:m.frequency)||"daily"),[N,A]=(0,s.useState)(!1),S=(e,t,r)=>{const s=[...h];s[e]=(0,a.A)((0,a.A)({},s[e]),{},{[t]:r}),x(s)};return(0,o.jsxs)("div",{className:"exercise-program-builder",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-clipboard-list mr-2 text-green-600"}),u("exerciseProgramBuilder","Exercise Program Builder")]}),(0,o.jsxs)("button",{onClick:()=>A(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),u("addExercise","Add Exercise")]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:u("programName","Program Name")}),(0,o.jsx)("input",{type:"text",value:p,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:u("enterProgramName","Enter program name")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:u("description","Description")}),(0,o.jsx)("textarea",{value:y,onChange:e=>f(e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none",placeholder:u("enterDescription","Enter program description")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:u("programDuration","Program Duration (weeks)")}),(0,o.jsxs)("select",{value:v,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsxs)("option",{value:"1",children:["1 ",u("week","week")]}),(0,o.jsxs)("option",{value:"2",children:["2 ",u("weeks","weeks")]}),(0,o.jsxs)("option",{value:"4",children:["4 ",u("weeks","weeks")]}),(0,o.jsxs)("option",{value:"6",children:["6 ",u("weeks","weeks")]}),(0,o.jsxs)("option",{value:"8",children:["8 ",u("weeks","weeks")]}),(0,o.jsxs)("option",{value:"12",children:["12 ",u("weeks","weeks")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:u("frequency","Frequency")}),(0,o.jsxs)("select",{value:j,onChange:e=>k(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"daily",children:u("daily","Daily")}),(0,o.jsx)("option",{value:"every_other_day",children:u("everyOtherDay","Every Other Day")}),(0,o.jsx)("option",{value:"3_times_week",children:u("threeTimes","3 Times per Week")}),(0,o.jsx)("option",{value:"2_times_week",children:u("twoTimes","2 Times per Week")}),(0,o.jsx)("option",{value:"weekly",children:u("weekly","Weekly")})]})]}),(0,o.jsxs)("button",{onClick:()=>{if(!p.trim())return void n.Ay.error(u("programNameRequired","Program name is required"));if(0===h.length)return void n.Ay.error(u("selectExercises","Please select at least one exercise"));const e={id:(null===m||void 0===m?void 0:m.id)||Date.now(),name:p,description:y,duration:v,frequency:j,exercises:h,patientId:t,createdAt:(null===m||void 0===m?void 0:m.createdAt)||(new Date).toISOString(),updatedAt:(new Date).toISOString()};r&&r(e),n.Ay.success(u("programSaved","Exercise program saved successfully"))},className:"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-save mr-2"}),u("saveProgram","Save Program")]})]}),(0,o.jsxs)("div",{className:"lg:col-span-2",children:[(0,o.jsxs)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:[u("selectedExercises","Selected Exercises")," (",h.length,")"]}),0===h.length?(0,o.jsxs)("div",{className:"text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg",children:[(0,o.jsx)("i",{className:"fas fa-dumbbell text-4xl text-gray-400 mb-4"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:u("noExercisesSelected","No exercises selected yet")}),(0,o.jsx)("button",{onClick:()=>A(!0),className:"mt-2 text-blue-600 hover:text-blue-800 font-medium",children:u("addFirstExercise","Add your first exercise")})]}):(0,o.jsx)("div",{className:"space-y-4",children:h.map((e,t)=>(0,o.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsxs)("div",{className:"w-16 h-16 rounded-lg overflow-hidden bg-gradient-to-br from-blue-100 to-purple-100 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center",children:[(0,o.jsx)("img",{src:e.image||c(e),alt:g?e.nameAr:e.name,className:"w-full h-full object-cover",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="flex"}}),(0,o.jsx)("div",{className:"hidden w-full h-full flex-col items-center justify-center",children:(0,o.jsx)("i",{className:"fas fa-".concat(d(e)," text-lg text-gray-500 dark:text-gray-400")})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white",children:g?e.nameAr:e.name}),(0,o.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[u(e.category,e.category)," \u2022 ",u(e.difficulty,e.difficulty)]})]})]}),(0,o.jsx)("button",{onClick:()=>(e=>{x(h.filter((t,r)=>r!==e))})(t),className:"text-red-600 hover:text-red-800",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:u("sets","Sets")}),(0,o.jsx)("input",{type:"number",min:"1",value:e.sets,onChange:e=>S(t,"sets",parseInt(e.target.value)),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:u("reps","Reps")}),(0,o.jsx)("input",{type:"number",min:"1",value:e.reps,onChange:e=>S(t,"reps",parseInt(e.target.value)),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:u("duration","Duration")}),(0,o.jsx)("input",{type:"text",value:e.duration,onChange:e=>S(t,"duration",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:u("rest","Rest")}),(0,o.jsx)("input",{type:"text",value:e.restTime,onChange:e=>S(t,"restTime",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),(0,o.jsxs)("div",{className:"mt-3",children:[(0,o.jsx)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:u("notes","Notes")}),(0,o.jsx)("input",{type:"text",value:e.notes,onChange:e=>S(t,"notes",e.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:u("addNotes","Add notes for this exercise...")})]})]},"".concat(e.id,"-").concat(t)))})]})]})]}),N&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",children:(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:u("selectExercise","Select Exercise")}),(0,o.jsx)("button",{onClick:()=>A(!1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,o.jsx)("i",{className:"fas fa-times"})})]}),(0,o.jsx)(l,{onSelectExercise:e=>{const t=(0,a.A)((0,a.A)({},e),{},{sets:1,reps:10,duration:"30 seconds",restTime:"30 seconds",notes:""});x([...h,t]),A(!1)},selectedExercises:h,patientId:t})]})})})]})}}}]);
//# sourceMappingURL=9252.9e45be23.chunk.js.map