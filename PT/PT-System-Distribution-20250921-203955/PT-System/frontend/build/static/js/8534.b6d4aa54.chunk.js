"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8534],{1369:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(3986),a=r(5043);const n=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,s.A)(e,n);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))}const i=a.forwardRef(l)},2593:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(2555),a=r(3986),n=(r(5043),r(579));const l=["children","variant","size","disabled","loading","className","type","onClick"],i=e=>{let{children:t,variant:r="primary",size:i="md",disabled:o=!1,loading:c=!1,className:d="",type:u="button",onClick:m}=e,x=(0,a.A)(e,l);const h={primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500",warning:"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500",ghost:"hover:bg-gray-100 text-gray-700 focus:ring-gray-500"},p={xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm",md:"px-4 py-2 text-sm",lg:"px-4 py-2 text-base",xl:"px-6 py-3 text-base"},g=["inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200",h[r]||h.primary,p[i]||p.md,o||c?"opacity-50 cursor-not-allowed":"",d].filter(Boolean).join(" ");return(0,n.jsxs)("button",(0,s.A)((0,s.A)({type:u,className:g,onClick:e=>{o||c?e.preventDefault():m&&m(e)},disabled:o||c},x),{},{children:[c&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),t]}))}},3099:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(2555),a=r(3986),n=(r(5043),r(579));const l=["children","className","padding","shadow","border","rounded","background","hover"],i=e=>{let{children:t,className:r="",padding:i="p-6",shadow:o="shadow-sm",border:c="border border-gray-200",rounded:d="rounded-lg",background:u="bg-white",hover:m=""}=e,x=(0,a.A)(e,l);const h=[u,c,d,o,i,m,r].filter(Boolean).join(" ");return(0,n.jsx)("div",(0,s.A)((0,s.A)({className:h},x),{},{children:t}))}},3986:(e,t,r)=>{function s(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}r.d(t,{A:()=>s})},4538:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(3986),a=r(5043);const n=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,s.A)(e,n);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=a.forwardRef(l)},4791:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(3986),a=r(5043);const n=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,s.A)(e,n);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))}const i=a.forwardRef(l)},7098:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(3986),a=r(5043);const n=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,s.A)(e,n);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const i=a.forwardRef(l)},8534:(e,t,r)=>{r.r(t),r.d(t,{default:()=>j});var s=r(2555),a=r(5043),n=r(4117),l=r(7098),i=r(9399),o=r(4538),c=r(3986);const d=["title","titleId"];function u(e,t){let{title:r,titleId:s}=e,n=(0,c.A)(e,d);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"}))}const m=a.forwardRef(u);var x=r(1369),h=r(4791);const p=["title","titleId"];function g(e,t){let{title:r,titleId:s}=e,n=(0,c.A)(e,p);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const f=a.forwardRef(g);var w=r(3099),v=r(2593),y=r(6761),b=r(579);const j=()=>{const{t:e}=(0,n.Bd)(),[t,r]=(0,a.useState)(!1),[c,d]=(0,a.useState)([]),[u,p]=(0,a.useState)({search:"",level:"all",dateFrom:"",dateTo:"",user:"",action:"all"}),g=[{id:1,timestamp:(new Date).toISOString(),level:"info",action:"user_login",user:"<EMAIL>",description:"User logged in successfully",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"},{id:2,timestamp:new Date(Date.now()-36e5).toISOString(),level:"warning",action:"permission_denied",user:"<EMAIL>",description:"Attempted to access admin panel without permissions",ipAddress:"*************",userAgent:"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"},{id:3,timestamp:new Date(Date.now()-72e5).toISOString(),level:"success",action:"patient_created",user:"<EMAIL>",description:"New patient record created: John Doe",ipAddress:"*************",userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64)"},{id:4,timestamp:new Date(Date.now()-108e5).toISOString(),level:"error",action:"system_error",user:"system",description:"Database connection timeout",ipAddress:"localhost",userAgent:"System Process"},{id:5,timestamp:new Date(Date.now()-144e5).toISOString(),level:"info",action:"form_submitted",user:"<EMAIL>",description:"PT Assessment form submitted for patient ID: 123",ipAddress:"*************",userAgent:"Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)"}];(0,a.useEffect)(()=>{j()},[u]);const j=async()=>{try{r(!0);let e=g;u.search&&(e=e.filter(e=>e.description.toLowerCase().includes(u.search.toLowerCase())||e.user.toLowerCase().includes(u.search.toLowerCase())||e.action.toLowerCase().includes(u.search.toLowerCase()))),"all"!==u.level&&(e=e.filter(e=>e.level===u.level)),"all"!==u.action&&(e=e.filter(e=>e.action===u.action)),d(e)}catch(e){console.error("Failed to load audit logs:",e)}finally{r(!1)}},A=e=>{switch(e){case"error":return(0,b.jsx)(l.A,{className:"h-5 w-5 text-red-500"});case"warning":return(0,b.jsx)(i.A,{className:"h-5 w-5 text-yellow-500"});case"success":return(0,b.jsx)(o.A,{className:"h-5 w-5 text-green-500"});default:return(0,b.jsx)(m,{className:"h-5 w-5 text-blue-500"})}},N=e=>{const t={error:"bg-red-100 text-red-800",warning:"bg-yellow-100 text-yellow-800",success:"bg-green-100 text-green-800",info:"bg-blue-100 text-blue-800"};return(0,b.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(t[e]||t.info),children:e.charAt(0).toUpperCase()+e.slice(1)})};return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,b.jsx)(x.A,{className:"h-8 w-8 mr-3 text-blue-600"}),e("auditLogs","Audit Logs")]}),(0,b.jsx)("p",{className:"text-gray-600 mt-1",children:e("auditLogsDescription","Monitor system activity and security events")})]}),(0,b.jsxs)(v.A,{onClick:()=>{const e=c.map(e=>"".concat(e.timestamp,",").concat(e.level,",").concat(e.action,",").concat(e.user,",").concat(e.description,",").concat(e.ipAddress)).join("\n"),t=new Blob([e],{type:"text/csv"}),r=window.URL.createObjectURL(t),s=document.createElement("a");s.href=r,s.download="audit-logs.csv",s.click()},variant:"outline",children:[(0,b.jsx)(h.A,{className:"h-4 w-4 mr-2"}),e("exportLogs","Export Logs")]})]}),(0,b.jsx)(w.A,{className:"p-6",children:(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("search","Search")}),(0,b.jsxs)("div",{className:"relative",children:[(0,b.jsx)(f,{className:"h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2"}),(0,b.jsx)("input",{type:"text",value:u.search,onChange:e=>p((0,s.A)((0,s.A)({},u),{},{search:e.target.value})),placeholder:e("searchLogs","Search logs..."),className:"pl-10 w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("level","Level")}),(0,b.jsxs)("select",{value:u.level,onChange:e=>p((0,s.A)((0,s.A)({},u),{},{level:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:e("allLevels","All Levels")}),(0,b.jsx)("option",{value:"info",children:e("info","Info")}),(0,b.jsx)("option",{value:"success",children:e("success","Success")}),(0,b.jsx)("option",{value:"warning",children:e("warning","Warning")}),(0,b.jsx)("option",{value:"error",children:e("error","Error")})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("action","Action")}),(0,b.jsxs)("select",{value:u.action,onChange:e=>p((0,s.A)((0,s.A)({},u),{},{action:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,b.jsx)("option",{value:"all",children:e("allActions","All Actions")}),(0,b.jsx)("option",{value:"user_login",children:e("userLogin","User Login")}),(0,b.jsx)("option",{value:"patient_created",children:e("patientCreated","Patient Created")}),(0,b.jsx)("option",{value:"form_submitted",children:e("formSubmitted","Form Submitted")}),(0,b.jsx)("option",{value:"permission_denied",children:e("permissionDenied","Permission Denied")}),(0,b.jsx)("option",{value:"system_error",children:e("systemError","System Error")})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("dateFrom","Date From")}),(0,b.jsx)("input",{type:"date",value:u.dateFrom,onChange:e=>p((0,s.A)((0,s.A)({},u),{},{dateFrom:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("dateTo","Date To")}),(0,b.jsx)("input",{type:"date",value:u.dateTo,onChange:e=>p((0,s.A)((0,s.A)({},u),{},{dateTo:e.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]})}),(0,b.jsx)(w.A,{className:"overflow-hidden",children:t?(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsx)(y.Ay,{size:"lg"})}):(0,b.jsxs)("div",{className:"overflow-x-auto",children:[(0,b.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,b.jsx)("thead",{className:"bg-gray-50",children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e("timestamp","Timestamp")}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e("level","Level")}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e("user","User")}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e("action","Action")}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e("description","Description")}),(0,b.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:e("ipAddress","IP Address")})]})}),(0,b.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(e=>(0,b.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:new Date(e.timestamp).toLocaleString()}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,b.jsxs)("div",{className:"flex items-center",children:[A(e.level),(0,b.jsx)("span",{className:"ml-2",children:N(e.level)})]})}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.user}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,b.jsx)("span",{className:"px-2 py-1 bg-gray-100 rounded text-xs font-mono",children:e.action})}),(0,b.jsx)("td",{className:"px-6 py-4 text-sm text-gray-900 max-w-md truncate",children:e.description}),(0,b.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.ipAddress})]},e.id))})]}),0===c.length&&(0,b.jsxs)("div",{className:"text-center py-12",children:[(0,b.jsx)(x.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,b.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:e("noLogsFound","No logs found")}),(0,b.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:e("noLogsDescription","No audit logs match your current filters.")})]})]})})]})}},9399:(e,t,r)=>{r.d(t,{A:()=>i});var s=r(3986),a=r(5043);const n=["title","titleId"];function l(e,t){let{title:r,titleId:l}=e,i=(0,s.A)(e,n);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":l},i),r?a.createElement("title",{id:l},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const i=a.forwardRef(l)}}]);
//# sourceMappingURL=8534.b6d4aa54.chunk.js.map