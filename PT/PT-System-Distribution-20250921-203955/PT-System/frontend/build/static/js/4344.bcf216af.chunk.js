"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[4344],{4344:(e,s,t)=>{t.r(s),t.d(s,{default:()=>l});var a=t(5043),r=t(3216),i=t(7921),d=t(579);const l=()=>{const{id:e}=(0,r.g)(),s=(0,r.Zp)(),{t:t,isRTL:l}=(0,i.o)(),[n,c]=(0,a.useState)("overview"),[o,x]=(0,a.useState)(null),[m,g]=(0,a.useState)(!0);(0,a.useEffect)(()=>{const s={id:e,patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",patientNameEn:"<PERSON>",patientId:"PT-2024-001",age:8,condition:"Cerebral Palsy",conditionAr:"\u0627\u0644\u0634\u0644\u0644 \u0627\u0644\u062f\u0645\u0627\u063a\u064a",startDate:"2024-01-15",status:"active",therapist:"\u062f. \u0633\u0627\u0631\u0629 \u0623\u062d\u0645\u062f",therapistEn:"Dr. Sarah Ahmed",sessions:{completed:12,total:20,remaining:8},nextSession:"2024-02-15T10:00:00",goals:[{id:1,title:"\u062a\u062d\u0633\u064a\u0646 \u0627\u0644\u062a\u0648\u0627\u0632\u0646",titleEn:"Improve Balance",progress:75,target:"2024-03-01",status:"on-track"},{id:2,title:"\u062a\u0642\u0648\u064a\u0629 \u0627\u0644\u0639\u0636\u0644\u0627\u062a",titleEn:"Strengthen Muscles",progress:60,target:"2024-03-15",status:"on-track"},{id:3,title:"\u062a\u062d\u0633\u064a\u0646 \u0627\u0644\u0645\u0634\u064a",titleEn:"Improve Walking",progress:45,target:"2024-04-01",status:"needs-attention"}],recentSessions:[{id:1,date:"2024-02-10",duration:45,activities:["Balance exercises","Strength training","Gait training"],activitiesAr:["\u062a\u0645\u0627\u0631\u064a\u0646 \u0627\u0644\u062a\u0648\u0627\u0632\u0646","\u062a\u062f\u0631\u064a\u0628 \u0627\u0644\u0642\u0648\u0629","\u062a\u062f\u0631\u064a\u0628 \u0627\u0644\u0645\u0634\u064a"],notes:"Patient showed good progress in balance exercises",notesAr:"\u0623\u0638\u0647\u0631 \u0627\u0644\u0645\u0631\u064a\u0636 \u062a\u0642\u062f\u0645\u0627\u064b \u062c\u064a\u062f\u0627\u064b \u0641\u064a \u062a\u0645\u0627\u0631\u064a\u0646 \u0627\u0644\u062a\u0648\u0627\u0632\u0646",therapist:"Dr. Sarah Ahmed",rating:4},{id:2,date:"2024-02-08",duration:45,activities:["Range of motion","Coordination exercises"],activitiesAr:["\u062a\u0645\u0627\u0631\u064a\u0646 \u0627\u0644\u0645\u062f\u0649 \u0627\u0644\u062d\u0631\u0643\u064a","\u062a\u0645\u0627\u0631\u064a\u0646 \u0627\u0644\u062a\u0646\u0633\u064a\u0642"],notes:"Focused on improving coordination and flexibility",notesAr:"\u0627\u0644\u062a\u0631\u0643\u064a\u0632 \u0639\u0644\u0649 \u062a\u062d\u0633\u064a\u0646 \u0627\u0644\u062a\u0646\u0633\u064a\u0642 \u0648\u0627\u0644\u0645\u0631\u0648\u0646\u0629",therapist:"Dr. Sarah Ahmed",rating:5}],equipment:[{name:"Walking Frame",nameAr:"\u0625\u0637\u0627\u0631 \u0627\u0644\u0645\u0634\u064a",status:"assigned"},{name:"Balance Board",nameAr:"\u0644\u0648\u062d \u0627\u0644\u062a\u0648\u0627\u0632\u0646",status:"assigned"},{name:"Therapy Ball",nameAr:"\u0643\u0631\u0629 \u0627\u0644\u0639\u0644\u0627\u062c",status:"requested"}],medications:[{name:"Baclofen",dosage:"10mg",frequency:"Twice daily",notes:"For muscle spasticity"},{name:"Vitamin D",dosage:"1000 IU",frequency:"Daily",notes:"Bone health support"}],assessments:[{date:"2024-01-15",type:"Initial Assessment",typeAr:"\u0627\u0644\u062a\u0642\u064a\u064a\u0645 \u0627\u0644\u0623\u0648\u0644\u064a",score:65,notes:"Baseline assessment completed"},{date:"2024-02-01",type:"Progress Review",typeAr:"\u0645\u0631\u0627\u062c\u0639\u0629 \u0627\u0644\u062a\u0642\u062f\u0645",score:72,notes:"Showing improvement in motor skills"}]};setTimeout(()=>{x(s),g(!1)},1e3)},[e]);const h=[{id:"overview",label:t("overview","Overview"),icon:"fas fa-chart-line"},{id:"sessions",label:t("sessions","Sessions"),icon:"fas fa-calendar-alt"},{id:"goals",label:t("goals","Goals"),icon:"fas fa-bullseye"},{id:"assessments",label:t("assessments","Assessments"),icon:"fas fa-clipboard-check"},{id:"equipment",label:t("equipment","Equipment"),icon:"fas fa-tools"},{id:"notes",label:t("notes","Notes"),icon:"fas fa-sticky-note"}];if(m)return(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});if(!o)return(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle text-6xl text-red-300 mb-4"}),(0,d.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:t("treatmentNotFound","Treatment Not Found")}),(0,d.jsx)("button",{onClick:()=>s("/treatments"),className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:t("backToTreatments","Back to Treatments")})]});return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(l?"font-arabic":"font-english"),children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 px-6 py-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("button",{onClick:()=>s("/treatments"),className:"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,d.jsx)("i",{className:"fas fa-arrow-left"})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:[t("treatmentPlan","Treatment Plan")," #",o.id]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[l?o.patientName:o.patientNameEn," \u2022 ",l?o.conditionAr:o.condition]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-calendar-plus mr-2"}),t("scheduleSession","Schedule Session")]}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-download mr-2"}),t("exportReport","Export Report")]})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600",children:(0,d.jsx)("div",{className:"px-6",children:(0,d.jsx)("nav",{className:"flex space-x-8",children:h.map(e=>(0,d.jsxs)("button",{onClick:()=>c(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(n===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,d.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})})}),(0,d.jsxs)("div",{className:"p-6",children:["overview"===n&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("patientInformation","Patient Information")}),(0,d.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("active"===o.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"),children:"active"===o.status?t("active","Active"):t("inactive","Inactive")})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1",children:t("patientName","Patient Name")}),(0,d.jsx)("p",{className:"text-gray-900 dark:text-white font-medium",children:l?o.patientName:o.patientNameEn})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1",children:t("patientId","Patient ID")}),(0,d.jsx)("p",{className:"text-gray-900 dark:text-white font-medium",children:o.patientId})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1",children:t("age","Age")}),(0,d.jsxs)("p",{className:"text-gray-900 dark:text-white font-medium",children:[o.age," ",t("years","years")]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1",children:t("condition","Condition")}),(0,d.jsx)("p",{className:"text-gray-900 dark:text-white font-medium",children:l?o.conditionAr:o.condition})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[o.sessions.completed,"/",o.sessions.total]}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:t("sessionsCompleted","Sessions Completed")})]})]}),(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(o.sessions.completed/o.sessions.total*100,"%")}})})})]}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-bullseye text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[o.goals.filter(e=>"on-track"===e.status).length,"/",o.goals.length]}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:t("goalsOnTrack","Goals On Track")})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-clock text-purple-600 dark:text-purple-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:new Date(o.nextSession).toLocaleDateString()}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:t("nextSession","Next Session")})]})]})})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:t("recentActivity","Recent Activity")}),(0,d.jsx)("div",{className:"space-y-4",children:o.recentSessions.slice(0,3).map(e=>(0,d.jsxs)("div",{className:"flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-notes-medical text-blue-600 dark:text-blue-400"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white",children:[t("session","Session")," - ",new Date(e.date).toLocaleDateString()]}),(0,d.jsx)("div",{className:"flex items-center",children:[...Array(5)].map((s,t)=>(0,d.jsx)("i",{className:"fas fa-star text-sm ".concat(t<e.rating?"text-yellow-400":"text-gray-300 dark:text-gray-600")},t))})]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mt-1",children:l?e.notesAr:e.notes}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:(l?e.activitiesAr:e.activities).map((e,s)=>(0,d.jsx)("span",{className:"px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs rounded-full",children:e},s))})]})]},e.id))})]})]}),"sessions"===n&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("sessionHistory","Session History")}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),t("newSession","New Session")]})]}),(0,d.jsx)("div",{className:"space-y-4",children:o.recentSessions.map(e=>(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-calendar text-blue-600 dark:text-blue-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:new Date(e.date).toLocaleDateString()}),(0,d.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.duration," ",t("minutes","minutes")," \u2022 ",e.therapist]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"flex items-center",children:[...Array(5)].map((s,t)=>(0,d.jsx)("i",{className:"fas fa-star text-sm ".concat(t<e.rating?"text-yellow-400":"text-gray-300 dark:text-gray-600")},t))}),(0,d.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400",children:(0,d.jsx)("i",{className:"fas fa-edit"})})]})]}),(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsxs)("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[t("activities","Activities"),":"]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:(l?e.activitiesAr:e.activities).map((e,s)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm rounded-full",children:e},s))})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[t("notes","Notes"),":"]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:l?e.notesAr:e.notes})]})]},e.id))})]})}),"goals"===n&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("treatmentGoals","Treatment Goals")}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),t("addGoal","Add Goal")]})]}),(0,d.jsx)("div",{className:"space-y-4",children:o.goals.map(e=>(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:l?e.title:e.titleEn}),(0,d.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("on-track"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"),children:"on-track"===e.status?t("onTrack","On Track"):t("needsAttention","Needs Attention")})]}),(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:t("progress","Progress")}),(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[e.progress,"%"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat("on-track"===e.status?"bg-green-600":"bg-yellow-600"),style:{width:"".concat(e.progress,"%")}})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsxs)("span",{className:"text-gray-500 dark:text-gray-400",children:[t("targetDate","Target Date"),": ",new Date(e.target).toLocaleDateString()]}),(0,d.jsxs)("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200",children:[(0,d.jsx)("i",{className:"fas fa-edit mr-1"}),t("edit","Edit")]})]})]},e.id))})]})}),"assessments"===n&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("assessmentHistory","Assessment History")}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),t("newAssessment","New Assessment")]})]}),(0,d.jsx)("div",{className:"space-y-4",children:o.assessments.map((e,s)=>(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:l?e.typeAr:e.type}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e.date).toLocaleDateString()})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:e.score}),(0,d.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t("score","Score")})]})]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:e.notes})]},s))})]})}),"equipment"===n&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("assignedEquipment","Assigned Equipment")}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),t("requestEquipment","Request Equipment")]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:o.equipment.map((e,s)=>(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:l?e.nameAr:e.name}),(0,d.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("assigned"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"),children:"assigned"===e.status?t("assigned","Assigned"):t("requested","Requested")})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm",children:[(0,d.jsx)("i",{className:"fas fa-eye mr-1"}),t("viewDetails","View Details")]}),(0,d.jsxs)("button",{className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 text-sm",children:[(0,d.jsx)("i",{className:"fas fa-times mr-1"}),t("remove","Remove")]})]})]},s))}),(0,d.jsxs)("div",{className:"mt-8",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:t("medications","Medications")}),(0,d.jsx)("div",{className:"space-y-3",children:o.medications.map((e,s)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.dosage," \u2022 ",e.frequency]}),e.notes&&(0,d.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500 mt-1",children:e.notes})]}),(0,d.jsx)("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200",children:(0,d.jsx)("i",{className:"fas fa-edit"})})]},s))})]})]})}),"notes"===n&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t("treatmentNotes","Treatment Notes")}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),t("addNote","Add Note")]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-blue-600 dark:text-blue-400"}),(0,d.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:"Dr. Sarah Ahmed"})]}),(0,d.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"2024-02-10"})]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Patient is responding well to the current treatment plan. Balance has improved significantly over the past two weeks. Recommend continuing with current exercises and adding more challenging balance activities."})]}),(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-blue-600 dark:text-blue-400"}),(0,d.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:"Dr. Sarah Ahmed"})]}),(0,d.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"2024-02-01"})]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Initial progress review completed. Patient shows good motivation and compliance with home exercise program. Family is very supportive and actively involved in the treatment process."})]})]})]})})]})]})}}}]);
//# sourceMappingURL=4344.bcf216af.chunk.js.map