{"version": 3, "file": "static/js/6643.5b2be08f.chunk.js", "mappings": "sLAGA,MAwoBA,EAxoBwBA,IAIjB,IAJkB,kBACvBC,EAAiB,mBACjBC,EAAqB,GAAE,SACvBC,GAAW,GACZH,EACC,MAAM,EAAEI,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAeC,IAAoBC,EAAAA,EAAAA,UAAS,OAC5CC,EAAiBC,IAAsBF,EAAAA,EAAAA,UAASP,GA6EjDU,EAAa,CACjB,CAAEC,MAAO,EAAGC,MAAO,UAAWC,MAAO,QACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,iBACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,YACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,mBACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,UACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,eACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,WACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,cACrC,CAAEF,MAAO,EAAGC,MAAO,UAAWC,MAAO,WACrC,CAAEF,MAAO,GAAIC,MAAO,UAAWC,MAAO,mBAqDxC,OAJAC,EAAAA,EAAAA,WAAU,KACRL,EAAmBT,IAClB,CAACA,KAGFe,EAAAA,EAAAA,KAAA,OAAKC,UAAU,SAAQC,UACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2BAA0BC,UACvCF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEf,EAAE,kBAAmB,sCAIxBD,IACAc,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDf,EAAE,sBAAuB,sIAKhCa,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,UAClCC,EAAAA,EAAAA,MAAA,OACEC,MAAM,MACNC,OAAO,MACPC,QAAQ,cACRL,UAAU,mFAAkFC,SAAA,EAK5FC,EAAAA,EAAAA,MAAA,KAAGI,GAAG,aAAYL,SAAA,EAChBF,EAAAA,EAAAA,KAAA,QAAMQ,EAAE,MAAMC,EAAE,KAAKC,WAAW,SAAST,UAAU,qDAAoDC,SACpGf,EAAE,YAAa,eAIlBgB,EAAAA,EAAAA,MAAA,KAAGQ,KAAK,UAAUC,OAAO,UAAUC,YAAY,MAAKX,SAAA,EAElDF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,6IAQRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,yCAGRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+KAaRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mJAiBRX,EAAAA,EAAAA,MAAA,KAAGI,GAAG,YAAWL,SAAA,EAEfF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,8KAYRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,oKAYRd,EAAAA,EAAAA,KAAA,WAASe,GAAG,KAAKC,GAAG,MAAMC,GAAG,KAAKC,GAAG,UAIvCf,EAAAA,EAAAA,MAAA,KAAGI,GAAG,WAAUL,SAAA,EAEdF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mLAYRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mLAYRd,EAAAA,EAAAA,KAAA,WAASe,GAAG,MAAMC,GAAG,MAAMC,GAAG,KAAKC,GAAG,UAIxCf,EAAAA,EAAAA,MAAA,KAAGI,GAAG,YAAWL,SAAA,EAEfF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAaRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAaRd,EAAAA,EAAAA,KAAA,WAASe,GAAG,MAAMC,GAAG,MAAMC,GAAG,KAAKC,GAAG,UAIxCf,EAAAA,EAAAA,MAAA,KAAGI,GAAG,WAAUL,SAAA,EAEdF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAaRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAaRd,EAAAA,EAAAA,KAAA,WAASe,GAAG,MAAMC,GAAG,MAAMC,GAAG,KAAKC,GAAG,gBAM5Cf,EAAAA,EAAAA,MAAA,KAAGI,GAAG,YAAWL,SAAA,EACfF,EAAAA,EAAAA,KAAA,QAAMQ,EAAE,MAAMC,EAAE,KAAKC,WAAW,SAAST,UAAU,qDAAoDC,SACpGf,EAAE,WAAY,gBAIjBgB,EAAAA,EAAAA,MAAA,KAAGQ,KAAK,UAAUC,OAAO,UAAUC,YAAY,MAAKX,SAAA,EAElDF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,6IAQRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,yCAGRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+KAYRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mJAiBRd,EAAAA,EAAAA,KAAA,QAAMmB,GAAG,MAAMC,GAAG,KAAKC,GAAG,MAAMC,GAAG,MAAMV,OAAO,UAAUC,YAAY,OAGtEb,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aACrCX,EAAAA,EAAAA,KAAA,UAAQe,GAAG,MAAMC,GAAG,MAAMO,EAAE,IAAIZ,KAAK,aAGrCR,EAAAA,EAAAA,MAAA,KAAGI,GAAG,iBAAgBL,SAAA,EACpBF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mLAWRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sLAYVX,EAAAA,EAAAA,MAAA,KAAGI,GAAG,gBAAeL,SAAA,EACnBF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mLAWRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sLAaVX,EAAAA,EAAAA,MAAA,KAAGI,GAAG,iBAAgBL,SAAA,EACpBF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAYRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAYRd,EAAAA,EAAAA,KAAA,WAASe,GAAG,MAAMC,GAAG,MAAMC,GAAG,KAAKC,GAAG,UAGxCf,EAAAA,EAAAA,MAAA,KAAGI,GAAG,gBAAeL,SAAA,EACnBF,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAYRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+IAYRd,EAAAA,EAAAA,KAAA,WAASe,GAAG,MAAMC,GAAG,MAAMC,GAAG,KAAKC,GAAG,eAM3CM,OAAOC,QA9fE,CAElBC,KAAM,CAAEnB,GAAI,EAAGoB,KAAM,OAAQC,OAAQ,iCAASpB,EAAG,IAAKC,EAAG,GAAIoB,KAAM,aACnEC,KAAM,CAAEvB,GAAI,EAAGoB,KAAM,OAAQC,OAAQ,uCAAUpB,EAAG,IAAKC,EAAG,GAAIoB,KAAM,aAGpEE,cAAe,CAAExB,GAAI,EAAGoB,KAAM,iBAAkBC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC9FG,cAAe,CAAEzB,GAAI,EAAGoB,KAAM,kBAAmBC,OAAQ,iHAAwBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACvGI,WAAY,CAAE1B,GAAI,EAAGoB,KAAM,cAAeC,OAAQ,sEAAgBpB,EAAG,GAAIC,EAAG,IAAKoB,KAAM,mBACvFK,aAAc,CAAE3B,GAAI,EAAGoB,KAAM,gBAAiBC,OAAQ,4EAAiBpB,EAAG,GAAIC,EAAG,IAAKoB,KAAM,mBAC5FM,WAAY,CAAE5B,GAAI,EAAGoB,KAAM,cAAeC,OAAQ,sEAAgBpB,EAAG,GAAIC,EAAG,IAAKoB,KAAM,mBACvFO,UAAW,CAAE7B,GAAI,EAAGoB,KAAM,aAAcC,OAAQ,gEAAepB,EAAG,GAAIC,EAAG,IAAKoB,KAAM,mBAGpFQ,aAAc,CAAE9B,GAAI,EAAGoB,KAAM,gBAAiBC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC5FS,aAAc,CAAE/B,GAAI,GAAIoB,KAAM,iBAAkBC,OAAQ,iHAAwBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACtGU,UAAW,CAAEhC,GAAI,GAAIoB,KAAM,aAAcC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACvFW,YAAa,CAAEjC,GAAI,GAAIoB,KAAM,eAAgBC,OAAQ,4EAAiBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC5FY,UAAW,CAAElC,GAAI,GAAIoB,KAAM,aAAcC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACvFa,SAAU,CAAEnC,GAAI,GAAIoB,KAAM,YAAaC,OAAQ,gEAAepB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAGpFc,WAAY,CAAEpC,GAAI,GAAIoB,KAAM,cAAeC,OAAQ,0DAAcpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SACvFe,WAAY,CAAErC,GAAI,GAAIoB,KAAM,cAAeC,OAAQ,0DAAcpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SACvFgB,aAAc,CAAEtC,GAAI,GAAIoB,KAAM,gBAAiBC,OAAQ,0DAAcpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SAC3FiB,aAAc,CAAEvC,GAAI,GAAIoB,KAAM,gBAAiBC,OAAQ,0DAAcpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SAC3FkB,OAAQ,CAAExC,GAAI,GAAIoB,KAAM,SAAUC,OAAQ,iCAASpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SAGzEmB,SAAU,CAAEzC,GAAI,GAAIoB,KAAM,YAAaC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACrFoB,WAAY,CAAE1C,GAAI,GAAIoB,KAAM,cAAeC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACzFqB,UAAW,CAAE3C,GAAI,GAAIoB,KAAM,aAAcC,OAAQ,4EAAiBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACxFsB,UAAW,CAAE5C,GAAI,GAAIoB,KAAM,aAAcC,OAAQ,+FAAqBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC5FuB,WAAY,CAAE7C,GAAI,GAAIoB,KAAM,cAAeC,OAAQ,4EAAiBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC1FwB,UAAW,CAAE9C,GAAI,GAAIoB,KAAM,aAAcC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAGvFyB,QAAS,CAAE/C,GAAI,GAAIoB,KAAM,WAAYC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACnF0B,UAAW,CAAEhD,GAAI,GAAIoB,KAAM,aAAcC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACvF2B,SAAU,CAAEjD,GAAI,GAAIoB,KAAM,YAAaC,OAAQ,4EAAiBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACtF4B,SAAU,CAAElD,GAAI,GAAIoB,KAAM,YAAaC,OAAQ,+FAAqBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC1F6B,UAAW,CAAEnD,GAAI,GAAIoB,KAAM,aAAcC,OAAQ,4EAAiBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACxF8B,SAAU,CAAEpD,GAAI,GAAIoB,KAAM,YAAaC,OAAQ,sEAAgBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAGrF+B,SAAU,CAAErD,GAAI,GAAIoB,KAAM,cAAeC,OAAQ,sDAAepB,EAAG,IAAKC,EAAG,GAAIoB,KAAM,aACrFgC,SAAU,CAAEtD,GAAI,GAAIoB,KAAM,cAAeC,OAAQ,4DAAgBpB,EAAG,IAAKC,EAAG,GAAIoB,KAAM,aAGtFiC,cAAe,CAAEvD,GAAI,GAAIoB,KAAM,iBAAkBC,OAAQ,wFAAmBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SAClGkC,cAAe,CAAExD,GAAI,GAAIoB,KAAM,iBAAkBC,OAAQ,wFAAmBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SAClGmC,YAAa,CAAEzD,GAAI,GAAIoB,KAAM,eAAgBC,OAAQ,wFAAmBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SAC9FoC,OAAQ,CAAE1D,GAAI,GAAIoB,KAAM,SAAUC,OAAQ,iCAASpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,SAGzEqC,kBAAmB,CAAE3D,GAAI,GAAIoB,KAAM,wBAAyBC,OAAQ,2FAAsBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAChHsC,iBAAkB,CAAE5D,GAAI,GAAIoB,KAAM,uBAAwBC,OAAQ,2FAAsBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC9GuC,kBAAmB,CAAE7D,GAAI,GAAIoB,KAAM,yBAA0BC,OAAQ,sIAA8BpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACzHwC,iBAAkB,CAAE9D,GAAI,GAAIoB,KAAM,wBAAyBC,OAAQ,sIAA8BpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACvHyC,eAAgB,CAAE/D,GAAI,GAAIoB,KAAM,qBAAsBC,OAAQ,2FAAsBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC1G0C,cAAe,CAAEhE,GAAI,GAAIoB,KAAM,oBAAqBC,OAAQ,2FAAsBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAGxG2C,aAAc,CAAEjE,GAAI,GAAIoB,KAAM,gBAAiBC,OAAQ,kFAAkBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,WAC/F4C,YAAa,CAAElE,GAAI,GAAIoB,KAAM,eAAgBC,OAAQ,kFAAkBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,WAC7F6C,eAAgB,CAAEnE,GAAI,GAAIoB,KAAM,qBAAsBC,OAAQ,2FAAsBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC1G8C,cAAe,CAAEpE,GAAI,GAAIoB,KAAM,oBAAqBC,OAAQ,2FAAsBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBACxG+C,cAAe,CAAErE,GAAI,GAAIoB,KAAM,oBAAqBC,OAAQ,oHAA2BpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC7GgD,aAAc,CAAEtE,GAAI,GAAIoB,KAAM,mBAAoBC,OAAQ,oHAA2BpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,mBAC3GiD,cAAe,CAAEvE,GAAI,GAAIoB,KAAM,oBAAqBC,OAAQ,2FAAsBpB,EAAG,IAAKC,EAAG,IAAKoB,KAAM,qBAybnEkD,IAAIC,IAAoB,IAAlBC,EAAKC,GAAOF,EAC7C,MAAMG,EAhYIC,KACpB,MAAMC,EAAY5F,EAAgB6F,KAAKC,GAAKA,EAAEH,WAAaA,GAC3D,OAAgB,OAATC,QAAS,IAATA,OAAS,EAATA,EAAWF,YAAa,GA8XHK,CAAaN,EAAO3E,IAChCV,EAvYIuF,KAAc,IAADK,EACjC,MAAMJ,EAAY5F,EAAgB6F,KAAKC,GAAKA,EAAEH,WAAaA,GAC3D,OAAKC,IACuD,QAArDI,EAAA9F,EAAW2F,KAAKI,GAAKA,EAAE9F,QAAUyF,EAAUF,kBAAU,IAAAM,OAAA,EAArDA,EAAuD5F,QADvC,WAqYC8F,CAAaT,EAAO3E,IAElC,OACEJ,EAAAA,EAAAA,MAAA,KAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UACEe,GAAImE,EAAO1E,EACXQ,GAAIkE,EAAOzE,EACXc,EAAE,KACFZ,KAAMd,EACN+F,YAAY,MACZhF,OAAQuE,EAAY,EAAI,UAAY,UACpCtE,YAAasE,EAAY,EAAI,IAAM,IACnClF,UAAS,8CAAA4F,OACN3G,EAA+D,GAApD,mDAEd4G,QAASA,IAzbDC,EAACC,EAAWd,KACpC,GAAIhG,EAAU,OAEd,MAAM+G,EAAgBxG,EAAgByG,UAAUX,GAAKA,EAAEH,WAAaF,EAAO3E,IAE3E,GAAI0F,GAAiB,EAAG,CACtB,MAAME,EAAc1G,EAAgBwG,GAC9BG,EAAYD,EAAYhB,UAAY,GAAKgB,EAAYhB,UAAY,EAAI,EAE3E,GAAkB,IAAdiB,EAAiB,CACnB,MAAMC,EAAc5G,EAAgB6G,OAAOf,GAAKA,EAAEH,WAAaF,EAAO3E,IACtEb,EAAmB2G,GACF,OAAjBrH,QAAiB,IAAjBA,GAAAA,EAAoBqH,EACtB,KAAO,CACL,MAAMA,EAAc5G,EAAgBsF,IAAIQ,GACtCA,EAAEH,WAAaF,EAAO3E,IAAEgG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQhB,GAAC,IAAEJ,UAAWiB,IAAcb,GAE9D7F,EAAmB2G,GACF,OAAjBrH,QAAiB,IAAjBA,GAAAA,EAAoBqH,EACtB,CACF,KAAO,CACL,MAAMG,EAAe,CACnBpB,SAAUF,EAAO3E,GACjBkG,WAAYvB,EAAOvD,KACnB+E,aAAcxB,EAAOtD,OACrBuD,UAAW,EACXwB,eAAgBzB,EAAOrD,KACvB+E,WAAW,IAAIC,MAAOC,eAElBT,EAAc,IAAI5G,EAAiB+G,GACzC9G,EAAmB2G,GACF,OAAjBrH,QAAiB,IAAjBA,GAAAA,EAAoBqH,EACtB,GAyZ+BN,CAAkBd,EAAKC,GACtC6B,aAAcA,IAAMxH,EAAiB2F,GACrC8B,aAAcA,IAAMzH,EAAiB,SAEvCS,EAAAA,EAAAA,KAAA,QACEQ,EAAG0E,EAAO1E,EACVC,EAAGyE,EAAOzE,EAAI,EACdC,WAAW,SACXT,UAAU,yEACVgH,MAAO,CAAEC,SAAU,QAAShH,SAE3BgF,EAAO3E,KAET4E,EAAY,IACXnF,EAAAA,EAAAA,KAAA,QACEQ,EAAG0E,EAAO1E,EACVC,EAAGyE,EAAOzE,EAAI,GACdC,WAAW,SACXT,UAAU,mDACVgH,MAAO,CAAEC,SAAU,QAAShH,SAE3BiF,MAjCCF,UA6Cf3F,IACCU,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iEAAgEC,UAC7EC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,uDAAsDC,SAAA,EACpEC,EAAAA,EAAAA,MAAA,UAAAD,SAAA,CAAQ,IAAEZ,EAAciB,MAAY,MAAInB,EAAQE,EAAcsC,OAAStC,EAAcqC,MACrFxB,EAAAA,EAAAA,MAAA,QAAMF,UAAU,wCAAuCC,SAAA,CAAC,IACpDZ,EAAcuC,KAAK,aAO7B1B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEf,EAAE,qBAAsB,kCAE3BgB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4CAA2CC,SAAA,EACxDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6DACfD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAC,mBAEpDP,EAAWwH,MAAM,EAAG,GAAGpC,IAAKnF,IAC3BO,EAAAA,EAAAA,MAAA,OAAuBF,UAAU,8BAA6BC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,OACEC,UAAU,8CACVgH,MAAO,CAAEG,gBAAiBxH,EAAMC,UAElCG,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAEN,EAAMA,UALlDA,EAAMA,SAQlBO,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,OACEC,UAAU,8CACVgH,MAAO,CAAEG,gBAAiB,cAE5BpH,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAC,yBAMzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEf,EAAE,oBAAqB,iDAE1BgB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iFAAgFC,SAAA,EAC7FC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,qBACRF,EAAAA,EAAAA,KAAA,SAAM,iBAERG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,8BACRF,EAAAA,EAAAA,KAAA,SAAM,6BAERG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,mBACRF,EAAAA,EAAAA,KAAA,SAAM,6BAERG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,+BACRF,EAAAA,EAAAA,KAAA,SAAM,uBAERG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,mBACRF,EAAAA,EAAAA,KAAA,SAAM,yBAERG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,qBACRF,EAAAA,EAAAA,KAAA,SAAM,gC", "sources": ["components/BodyMap/ClinicalBodyMap.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst ClinicalBodyMap = ({ \n  onPainPointSelect, \n  selectedPainPoints = [], \n  readonly = false \n}) => {\n  const { t, isRTL } = useLanguage();\n  const [hoveredRegion, setHoveredRegion] = useState(null);\n  const [selectedRegions, setSelectedRegions] = useState(selectedPainPoints);\n\n  // Clinical body regions mapped to the detailed anatomical SVG diagram\n  // 50 standardized anatomical regions following medical research standards\n  const bodyRegions = {\n    // Front View - Head and Neck (1-2)\n    head: { id: 1, name: 'Head', nameAr: 'الرأس', x: 175, y: 55, area: 'head-neck' },\n    neck: { id: 2, name: 'Neck', nameAr: 'الرقبة', x: 175, y: 82, area: 'head-neck' },\n\n    // Front View - Upper Extremities Right (3-8)\n    rightShoulder: { id: 3, name: 'Right Shoulder', nameAr: 'الكتف الأيمن', x: 140, y: 105, area: 'upper-extremity' },\n    rightUpperArm: { id: 4, name: 'Right Upper Arm', nameAr: 'الذراع الأيمن العلوي', x: 112, y: 150, area: 'upper-extremity' },\n    rightElbow: { id: 5, name: 'Right Elbow', nameAr: 'الكوع الأيمن', x: 95, y: 189, area: 'upper-extremity' },\n    rightForearm: { id: 6, name: 'Right Forearm', nameAr: 'الساعد الأيمن', x: 78, y: 230, area: 'upper-extremity' },\n    rightWrist: { id: 7, name: 'Right Wrist', nameAr: 'الرسغ الأيمن', x: 65, y: 268, area: 'upper-extremity' },\n    rightHand: { id: 8, name: 'Right Hand', nameAr: 'اليد اليمنى', x: 62, y: 275, area: 'upper-extremity' },\n\n    // Front View - Upper Extremities Left (9-14)\n    leftShoulder: { id: 9, name: 'Left Shoulder', nameAr: 'الكتف الأيسر', x: 210, y: 105, area: 'upper-extremity' },\n    leftUpperArm: { id: 10, name: 'Left Upper Arm', nameAr: 'الذراع الأيسر العلوي', x: 238, y: 150, area: 'upper-extremity' },\n    leftElbow: { id: 11, name: 'Left Elbow', nameAr: 'الكوع الأيسر', x: 255, y: 189, area: 'upper-extremity' },\n    leftForearm: { id: 12, name: 'Left Forearm', nameAr: 'الساعد الأيسر', x: 272, y: 230, area: 'upper-extremity' },\n    leftWrist: { id: 13, name: 'Left Wrist', nameAr: 'الرسغ الأيسر', x: 285, y: 268, area: 'upper-extremity' },\n    leftHand: { id: 14, name: 'Left Hand', nameAr: 'اليد اليسرى', x: 288, y: 275, area: 'upper-extremity' },\n\n    // Front View - Trunk (15-19)\n    upperChest: { id: 15, name: 'Upper Chest', nameAr: 'أعلى الصدر', x: 175, y: 125, area: 'trunk' },\n    lowerChest: { id: 16, name: 'Lower Chest', nameAr: 'أسفل الصدر', x: 175, y: 160, area: 'trunk' },\n    upperAbdomen: { id: 17, name: 'Upper Abdomen', nameAr: 'أعلى البطن', x: 175, y: 200, area: 'trunk' },\n    lowerAbdomen: { id: 18, name: 'Lower Abdomen', nameAr: 'أسفل البطن', x: 175, y: 240, area: 'trunk' },\n    pelvis: { id: 19, name: 'Pelvis', nameAr: 'الحوض', x: 175, y: 280, area: 'trunk' },\n\n    // Front View - Lower Extremities Right (20-25)\n    rightHip: { id: 20, name: 'Right Hip', nameAr: 'الورك الأيمن', x: 155, y: 315, area: 'lower-extremity' },\n    rightThigh: { id: 21, name: 'Right Thigh', nameAr: 'الفخذ الأيمن', x: 155, y: 370, area: 'lower-extremity' },\n    rightKnee: { id: 22, name: 'Right Knee', nameAr: 'الركبة اليمنى', x: 152, y: 420, area: 'lower-extremity' },\n    rightCalf: { id: 23, name: 'Right Calf', nameAr: 'ربلة الساق اليمنى', x: 148, y: 480, area: 'lower-extremity' },\n    rightAnkle: { id: 24, name: 'Right Ankle', nameAr: 'الكاحل الأيمن', x: 145, y: 530, area: 'lower-extremity' },\n    rightFoot: { id: 25, name: 'Right Foot', nameAr: 'القدم اليمنى', x: 142, y: 570, area: 'lower-extremity' },\n\n    // Front View - Lower Extremities Left (26-31)\n    leftHip: { id: 26, name: 'Left Hip', nameAr: 'الورك الأيسر', x: 195, y: 315, area: 'lower-extremity' },\n    leftThigh: { id: 27, name: 'Left Thigh', nameAr: 'الفخذ الأيسر', x: 195, y: 370, area: 'lower-extremity' },\n    leftKnee: { id: 28, name: 'Left Knee', nameAr: 'الركبة اليسرى', x: 198, y: 420, area: 'lower-extremity' },\n    leftCalf: { id: 29, name: 'Left Calf', nameAr: 'ربلة الساق اليسرى', x: 202, y: 480, area: 'lower-extremity' },\n    leftAnkle: { id: 30, name: 'Left Ankle', nameAr: 'الكاحل الأيسر', x: 205, y: 530, area: 'lower-extremity' },\n    leftFoot: { id: 31, name: 'Left Foot', nameAr: 'القدم اليسرى', x: 208, y: 570, area: 'lower-extremity' },\n\n    // Back View - Head and Neck (32-33)\n    headBack: { id: 32, name: 'Head (Back)', nameAr: 'الرأس (خلف)', x: 525, y: 55, area: 'head-neck' },\n    neckBack: { id: 33, name: 'Neck (Back)', nameAr: 'الرقبة (خلف)', x: 525, y: 82, area: 'head-neck' },\n\n    // Back View - Spine (34-37)\n    cervicalSpine: { id: 34, name: 'Cervical Spine', nameAr: 'الفقرات العنقية', x: 525, y: 110, area: 'spine' },\n    thoracicSpine: { id: 35, name: 'Thoracic Spine', nameAr: 'الفقرات الصدرية', x: 525, y: 170, area: 'spine' },\n    lumbarSpine: { id: 36, name: 'Lumbar Spine', nameAr: 'الفقرات القطنية', x: 525, y: 230, area: 'spine' },\n    sacrum: { id: 37, name: 'Sacrum', nameAr: 'العجز', x: 525, y: 290, area: 'spine' },\n\n    // Back View - Upper Extremities (38-43)\n    rightShoulderBack: { id: 38, name: 'Right Shoulder (Back)', nameAr: 'الكتف الأيمن (خلف)', x: 490, y: 105, area: 'upper-extremity' },\n    leftShoulderBack: { id: 39, name: 'Left Shoulder (Back)', nameAr: 'الكتف الأيسر (خلف)', x: 560, y: 105, area: 'upper-extremity' },\n    rightUpperArmBack: { id: 40, name: 'Right Upper Arm (Back)', nameAr: 'الذراع الأيمن العلوي (خلف)', x: 462, y: 150, area: 'upper-extremity' },\n    leftUpperArmBack: { id: 41, name: 'Left Upper Arm (Back)', nameAr: 'الذراع الأيسر العلوي (خلف)', x: 588, y: 150, area: 'upper-extremity' },\n    rightElbowBack: { id: 42, name: 'Right Elbow (Back)', nameAr: 'الكوع الأيمن (خلف)', x: 445, y: 189, area: 'upper-extremity' },\n    leftElbowBack: { id: 43, name: 'Left Elbow (Back)', nameAr: 'الكوع الأيسر (خلف)', x: 605, y: 189, area: 'upper-extremity' },\n\n    // Back View - Gluteal and Lower Extremities (44-50)\n    rightGluteal: { id: 44, name: 'Right Gluteal', nameAr: 'الأرداف اليمنى', x: 505, y: 315, area: 'gluteal' },\n    leftGluteal: { id: 45, name: 'Left Gluteal', nameAr: 'الأرداف اليسرى', x: 545, y: 315, area: 'gluteal' },\n    rightThighBack: { id: 46, name: 'Right Thigh (Back)', nameAr: 'الفخذ الأيمن (خلف)', x: 505, y: 370, area: 'lower-extremity' },\n    leftThighBack: { id: 47, name: 'Left Thigh (Back)', nameAr: 'الفخذ الأيسر (خلف)', x: 545, y: 370, area: 'lower-extremity' },\n    rightCalfBack: { id: 48, name: 'Right Calf (Back)', nameAr: 'ربلة الساق اليمنى (خلف)', x: 498, y: 480, area: 'lower-extremity' },\n    leftCalfBack: { id: 49, name: 'Left Calf (Back)', nameAr: 'ربلة الساق اليسرى (خلف)', x: 552, y: 480, area: 'lower-extremity' },\n    rightFootBack: { id: 50, name: 'Right Foot (Back)', nameAr: 'القدم اليمنى (خلف)', x: 492, y: 570, area: 'lower-extremity' },\n  };\n\n  // Pain intensity colors (clinical standard)\n  const painLevels = [\n    { level: 1, color: '#FEF3C7', label: 'Mild' },\n    { level: 2, color: '#FDE68A', label: 'Mild-Moderate' },\n    { level: 3, color: '#FCD34D', label: 'Moderate' },\n    { level: 4, color: '#FBBF24', label: 'Moderate-Severe' },\n    { level: 5, color: '#F59E0B', label: 'Severe' },\n    { level: 6, color: '#D97706', label: 'Very Severe' },\n    { level: 7, color: '#B45309', label: 'Extreme' },\n    { level: 8, color: '#92400E', label: 'Unbearable' },\n    { level: 9, color: '#78350F', label: 'Maximum' },\n    { level: 10, color: '#451A03', label: 'Worst Possible' }\n  ];\n\n  const handleRegionClick = (regionKey, region) => {\n    if (readonly) return;\n\n    const existingIndex = selectedRegions.findIndex(p => p.regionId === region.id);\n    \n    if (existingIndex >= 0) {\n      const currentPain = selectedRegions[existingIndex];\n      const nextLevel = currentPain.painLevel < 10 ? currentPain.painLevel + 1 : 0;\n      \n      if (nextLevel === 0) {\n        const newSelected = selectedRegions.filter(p => p.regionId !== region.id);\n        setSelectedRegions(newSelected);\n        onPainPointSelect?.(newSelected);\n      } else {\n        const newSelected = selectedRegions.map(p => \n          p.regionId === region.id ? { ...p, painLevel: nextLevel } : p\n        );\n        setSelectedRegions(newSelected);\n        onPainPointSelect?.(newSelected);\n      }\n    } else {\n      const newPainPoint = {\n        regionId: region.id,\n        regionName: region.name,\n        regionNameAr: region.nameAr,\n        painLevel: 1,\n        anatomicalArea: region.area,\n        timestamp: new Date().toISOString()\n      };\n      const newSelected = [...selectedRegions, newPainPoint];\n      setSelectedRegions(newSelected);\n      onPainPointSelect?.(newSelected);\n    }\n  };\n\n  const getPainColor = (regionId) => {\n    const painPoint = selectedRegions.find(p => p.regionId === regionId);\n    if (!painPoint) return '#F3F4F6';\n    return painLevels.find(l => l.level === painPoint.painLevel)?.color || '#F3F4F6';\n  };\n\n  const getPainLevel = (regionId) => {\n    const painPoint = selectedRegions.find(p => p.regionId === regionId);\n    return painPoint?.painLevel || 0;\n  };\n\n  useEffect(() => {\n    setSelectedRegions(selectedPainPoints);\n  }, [selectedPainPoints]);\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex justify-center mb-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('clinicalBodyMap', 'Clinical Body Pain Assessment')}\n          </h3>\n        </div>\n        \n        {!readonly && (\n          <div className=\"mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n              {t('bodyMapInstructions', 'Click on numbered body regions to mark pain. Click repeatedly to increase pain intensity (1-10). Click at level 10 to remove.')}\n            </p>\n          </div>\n        )}\n\n        <div className=\"flex justify-center\">\n          <svg\n            width=\"700\"\n            height=\"600\"\n            viewBox=\"0 0 700 600\"\n            className=\"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800\"\n          >\n            {/* Medical-Grade Body Diagram */}\n\n            {/* Front View */}\n            <g id=\"front-view\">\n              <text x=\"175\" y=\"25\" textAnchor=\"middle\" className=\"fill-gray-700 dark:fill-gray-300 text-lg font-bold\">\n                {t('frontView', 'ANTERIOR')}\n              </text>\n\n              {/* Detailed Human Body - Front View */}\n              <g fill=\"#F7FAFC\" stroke=\"#2D3748\" strokeWidth=\"1.5\">\n                {/* Head - Anatomically correct */}\n                <path d=\"M 175 35\n                         C 185 35, 195 40, 200 50\n                         C 205 60, 200 70, 195 75\n                         C 185 80, 165 80, 155 75\n                         C 150 70, 145 60, 150 50\n                         C 155 40, 165 35, 175 35 Z\"/>\n\n                {/* Neck */}\n                <path d=\"M 165 75 L 165 90 L 185 90 L 185 75\"/>\n\n                {/* Shoulders and Chest */}\n                <path d=\"M 140 90\n                         C 130 95, 125 100, 125 110\n                         L 125 130\n                         C 125 140, 130 150, 140 155\n                         L 150 160\n                         L 200 160\n                         L 210 155\n                         C 220 150, 225 140, 225 130\n                         L 225 110\n                         C 225 100, 220 95, 210 90\n                         Z\"/>\n\n                {/* Torso - Anatomically shaped */}\n                <path d=\"M 150 160\n                         L 145 180\n                         L 140 220\n                         L 138 260\n                         L 140 300\n                         L 145 320\n                         L 155 330\n                         L 195 330\n                         L 205 320\n                         L 210 300\n                         L 212 260\n                         L 210 220\n                         L 205 180\n                         L 200 160\n                         Z\"/>\n\n                {/* Right Arm - Detailed */}\n                <g id=\"right-arm\">\n                  {/* Upper Arm */}\n                  <path d=\"M 125 110\n                           C 115 115, 105 120, 100 130\n                           L 95 150\n                           L 90 170\n                           C 88 180, 90 185, 95 188\n                           L 105 190\n                           C 110 188, 115 185, 118 180\n                           L 125 160\n                           L 130 140\n                           C 132 130, 130 120, 125 110 Z\"/>\n\n                  {/* Forearm */}\n                  <path d=\"M 95 188\n                           C 85 192, 75 200, 70 210\n                           L 65 230\n                           L 60 250\n                           C 58 260, 60 265, 65 268\n                           L 75 270\n                           C 80 268, 85 265, 88 260\n                           L 95 240\n                           L 100 220\n                           C 102 210, 100 200, 95 188 Z\"/>\n\n                  {/* Hand */}\n                  <ellipse cx=\"62\" cy=\"275\" rx=\"12\" ry=\"8\"/>\n                </g>\n\n                {/* Left Arm - Detailed */}\n                <g id=\"left-arm\">\n                  {/* Upper Arm */}\n                  <path d=\"M 225 110\n                           C 235 115, 245 120, 250 130\n                           L 255 150\n                           L 260 170\n                           C 262 180, 260 185, 255 188\n                           L 245 190\n                           C 240 188, 235 185, 232 180\n                           L 225 160\n                           L 220 140\n                           C 218 130, 220 120, 225 110 Z\"/>\n\n                  {/* Forearm */}\n                  <path d=\"M 255 188\n                           C 265 192, 275 200, 280 210\n                           L 285 230\n                           L 290 250\n                           C 292 260, 290 265, 285 268\n                           L 275 270\n                           C 270 268, 265 265, 262 260\n                           L 255 240\n                           L 250 220\n                           C 248 210, 250 200, 255 188 Z\"/>\n\n                  {/* Hand */}\n                  <ellipse cx=\"288\" cy=\"275\" rx=\"12\" ry=\"8\"/>\n                </g>\n\n                {/* Right Leg - Detailed */}\n                <g id=\"right-leg\">\n                  {/* Thigh */}\n                  <path d=\"M 155 330\n                           L 150 350\n                           L 145 390\n                           L 140 430\n                           C 138 440, 140 445, 145 448\n                           L 155 450\n                           C 160 448, 165 445, 168 440\n                           L 170 400\n                           L 175 360\n                           L 180 330\n                           Z\"/>\n\n                  {/* Calf */}\n                  <path d=\"M 145 448\n                           L 140 470\n                           L 135 510\n                           L 130 540\n                           C 128 550, 130 555, 135 558\n                           L 145 560\n                           C 150 558, 155 555, 158 550\n                           L 160 520\n                           L 165 480\n                           L 168 448\n                           Z\"/>\n\n                  {/* Foot */}\n                  <ellipse cx=\"142\" cy=\"570\" rx=\"15\" ry=\"8\"/>\n                </g>\n\n                {/* Left Leg - Detailed */}\n                <g id=\"left-leg\">\n                  {/* Thigh */}\n                  <path d=\"M 195 330\n                           L 200 350\n                           L 205 390\n                           L 210 430\n                           C 212 440, 210 445, 205 448\n                           L 195 450\n                           C 190 448, 185 445, 182 440\n                           L 180 400\n                           L 175 360\n                           L 170 330\n                           Z\"/>\n\n                  {/* Calf */}\n                  <path d=\"M 205 448\n                           L 210 470\n                           L 215 510\n                           L 220 540\n                           C 222 550, 220 555, 215 558\n                           L 205 560\n                           C 200 558, 195 555, 192 550\n                           L 190 520\n                           L 185 480\n                           L 182 448\n                           Z\"/>\n\n                  {/* Foot */}\n                  <ellipse cx=\"208\" cy=\"570\" rx=\"15\" ry=\"8\"/>\n                </g>\n              </g>\n            </g>\n\n            {/* Back View */}\n            <g id=\"back-view\">\n              <text x=\"525\" y=\"25\" textAnchor=\"middle\" className=\"fill-gray-700 dark:fill-gray-300 text-lg font-bold\">\n                {t('backView', 'POSTERIOR')}\n              </text>\n\n              {/* Detailed Human Body - Back View */}\n              <g fill=\"#F7FAFC\" stroke=\"#2D3748\" strokeWidth=\"1.5\">\n                {/* Head - Back */}\n                <path d=\"M 525 35\n                         C 535 35, 545 40, 550 50\n                         C 555 60, 550 70, 545 75\n                         C 535 80, 515 80, 505 75\n                         C 500 70, 495 60, 500 50\n                         C 505 40, 515 35, 525 35 Z\"/>\n\n                {/* Neck - Back */}\n                <path d=\"M 515 75 L 515 90 L 535 90 L 535 75\"/>\n\n                {/* Back Torso with Spine */}\n                <path d=\"M 490 90\n                         C 480 95, 475 100, 475 110\n                         L 475 130\n                         C 475 140, 480 150, 490 155\n                         L 500 160\n                         L 550 160\n                         L 560 155\n                         C 570 150, 575 140, 575 130\n                         L 575 110\n                         C 575 100, 570 95, 560 90\n                         Z\"/>\n\n                <path d=\"M 500 160\n                         L 495 180\n                         L 490 220\n                         L 488 260\n                         L 490 300\n                         L 495 320\n                         L 505 330\n                         L 545 330\n                         L 555 320\n                         L 560 300\n                         L 562 260\n                         L 560 220\n                         L 555 180\n                         L 550 160\n                         Z\"/>\n\n                {/* Spine Line */}\n                <line x1=\"525\" y1=\"90\" x2=\"525\" y2=\"330\" stroke=\"#4A5568\" strokeWidth=\"2\"/>\n\n                {/* Vertebrae markers */}\n                <circle cx=\"525\" cy=\"110\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"130\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"150\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"170\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"190\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"210\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"230\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"250\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"270\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"290\" r=\"2\" fill=\"#4A5568\"/>\n                <circle cx=\"525\" cy=\"310\" r=\"2\" fill=\"#4A5568\"/>\n\n                {/* Back Arms */}\n                <g id=\"back-right-arm\">\n                  <path d=\"M 475 110\n                           C 465 115, 455 120, 450 130\n                           L 445 150\n                           L 440 170\n                           C 438 180, 440 185, 445 188\n                           L 455 190\n                           C 460 188, 465 185, 468 180\n                           L 475 160\n                           L 480 140\n                           C 482 130, 480 120, 475 110 Z\"/>\n\n                  <path d=\"M 445 188\n                           C 435 192, 425 200, 420 210\n                           L 415 230\n                           L 410 250\n                           C 408 260, 410 265, 415 268\n                           L 425 270\n                           C 430 268, 435 265, 438 260\n                           L 445 240\n                           L 450 220\n                           C 452 210, 450 200, 445 188 Z\"/>\n                </g>\n\n                <g id=\"back-left-arm\">\n                  <path d=\"M 575 110\n                           C 585 115, 595 120, 600 130\n                           L 605 150\n                           L 610 170\n                           C 612 180, 610 185, 605 188\n                           L 595 190\n                           C 590 188, 585 185, 582 180\n                           L 575 160\n                           L 570 140\n                           C 568 130, 570 120, 575 110 Z\"/>\n\n                  <path d=\"M 605 188\n                           C 615 192, 625 200, 630 210\n                           L 635 230\n                           L 640 250\n                           C 642 260, 640 265, 635 268\n                           L 625 270\n                           C 620 268, 615 265, 612 260\n                           L 605 240\n                           L 600 220\n                           C 598 210, 600 200, 605 188 Z\"/>\n                </g>\n\n                {/* Back Legs */}\n                <g id=\"back-right-leg\">\n                  <path d=\"M 505 330\n                           L 500 350\n                           L 495 390\n                           L 490 430\n                           C 488 440, 490 445, 495 448\n                           L 505 450\n                           C 510 448, 515 445, 518 440\n                           L 520 400\n                           L 525 360\n                           L 530 330\n                           Z\"/>\n\n                  <path d=\"M 495 448\n                           L 490 470\n                           L 485 510\n                           L 480 540\n                           C 478 550, 480 555, 485 558\n                           L 495 560\n                           C 500 558, 505 555, 508 550\n                           L 510 520\n                           L 515 480\n                           L 518 448\n                           Z\"/>\n\n                  <ellipse cx=\"492\" cy=\"570\" rx=\"15\" ry=\"8\"/>\n                </g>\n\n                <g id=\"back-left-leg\">\n                  <path d=\"M 545 330\n                           L 550 350\n                           L 555 390\n                           L 560 430\n                           C 562 440, 560 445, 555 448\n                           L 545 450\n                           C 540 448, 535 445, 532 440\n                           L 530 400\n                           L 525 360\n                           L 520 330\n                           Z\"/>\n\n                  <path d=\"M 555 448\n                           L 560 470\n                           L 565 510\n                           L 570 540\n                           C 572 550, 570 555, 565 558\n                           L 555 560\n                           C 550 558, 545 555, 542 550\n                           L 540 520\n                           L 535 480\n                           L 532 448\n                           Z\"/>\n\n                  <ellipse cx=\"558\" cy=\"570\" rx=\"15\" ry=\"8\"/>\n                </g>\n              </g>\n            </g>\n\n            {/* Clickable Pain Points */}\n            {Object.entries(bodyRegions).map(([key, region]) => {\n              const painLevel = getPainLevel(region.id);\n              const color = getPainColor(region.id);\n\n              return (\n                <g key={key}>\n                  <circle\n                    cx={region.x}\n                    cy={region.y}\n                    r=\"12\"\n                    fill={color}\n                    fillOpacity=\"0.9\"\n                    stroke={painLevel > 0 ? '#DC2626' : '#2D3748'}\n                    strokeWidth={painLevel > 0 ? '3' : '2'}\n                    className={`cursor-pointer transition-all duration-200 ${\n                      !readonly ? 'hover:stroke-blue-500 hover:stroke-4 hover:r-14' : ''\n                    }`}\n                    onClick={() => handleRegionClick(key, region)}\n                    onMouseEnter={() => setHoveredRegion(region)}\n                    onMouseLeave={() => setHoveredRegion(null)}\n                  />\n                  <text\n                    x={region.x}\n                    y={region.y + 4}\n                    textAnchor=\"middle\"\n                    className=\"fill-gray-800 dark:fill-gray-200 text-sm font-bold pointer-events-none\"\n                    style={{ fontSize: '11px' }}\n                  >\n                    {region.id}\n                  </text>\n                  {painLevel > 0 && (\n                    <text\n                      x={region.x}\n                      y={region.y - 20}\n                      textAnchor=\"middle\"\n                      className=\"fill-red-600 dark:fill-red-400 text-sm font-bold\"\n                      style={{ fontSize: '13px' }}\n                    >\n                      {painLevel}\n                    </text>\n                  )}\n                </g>\n              );\n            })}\n          </svg>\n\n\n        </div>\n\n        {/* Hover Information */}\n        {hoveredRegion && (\n          <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center\">\n            <span className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">\n              <strong>#{hoveredRegion.id}</strong> - {isRTL ? hoveredRegion.nameAr : hoveredRegion.name}\n              <span className=\"text-blue-600 dark:text-blue-300 ml-2\">\n                ({hoveredRegion.area})\n              </span>\n            </span>\n          </div>\n        )}\n\n        {/* Clinical Pain Scale */}\n        <div className=\"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-2\">\n            {t('painIntensityScale', 'Pain Intensity Scale (0-10)')}\n          </h4>\n          <div className=\"flex items-center justify-between text-xs\">\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-3 h-3 rounded-full border border-gray-400 bg-gray-200\"></div>\n              <span className=\"text-gray-700 dark:text-gray-300\">0 - No Pain</span>\n            </div>\n            {painLevels.slice(0, 5).map((level) => (\n              <div key={level.level} className=\"flex items-center space-x-1\">\n                <div \n                  className=\"w-3 h-3 rounded-full border border-gray-400\"\n                  style={{ backgroundColor: level.color }}\n                ></div>\n                <span className=\"text-gray-700 dark:text-gray-300\">{level.level}</span>\n              </div>\n            ))}\n            <div className=\"flex items-center space-x-1\">\n              <div \n                className=\"w-3 h-3 rounded-full border border-gray-400\"\n                style={{ backgroundColor: '#451A03' }}\n              ></div>\n              <span className=\"text-gray-700 dark:text-gray-300\">10 - Worst</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Region Legend */}\n        <div className=\"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <h4 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-2\">\n            {t('anatomicalRegions', 'Anatomical Regions (50 Standardized Areas)')}\n          </h4>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600 dark:text-gray-400\">\n            <div>\n              <strong>Head/Neck (1-4)</strong>\n              <br />Head, Neck\n            </div>\n            <div>\n              <strong>Upper Extremities (5-20)</strong>\n              <br />Shoulders, Arms, Hands\n            </div>\n            <div>\n              <strong>Trunk (21-28)</strong>\n              <br />Chest, Abdomen, Pelvis\n            </div>\n            <div>\n              <strong>Lower Extremities (29-44)</strong>\n              <br />Hips, Legs, Feet\n            </div>\n            <div>\n              <strong>Spine (45-48)</strong>\n              <br />Cervical to Sacrum\n            </div>\n            <div>\n              <strong>Gluteal (49-50)</strong>\n              <br />Buttocks Region\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ClinicalBodyMap;\n"], "names": ["_ref", "onPainPointSelect", "selectedPainPoints", "readonly", "t", "isRTL", "useLanguage", "hoveredRegion", "setHoveredRegion", "useState", "selectedRegions", "setSelectedRegions", "painLevels", "level", "color", "label", "useEffect", "_jsx", "className", "children", "_jsxs", "width", "height", "viewBox", "id", "x", "y", "textAnchor", "fill", "stroke", "strokeWidth", "d", "cx", "cy", "rx", "ry", "x1", "y1", "x2", "y2", "r", "Object", "entries", "head", "name", "nameAr", "area", "neck", "rightShoulder", "rightUpperArm", "<PERSON><PERSON><PERSON><PERSON>", "rightForearm", "rightWrist", "rightHand", "leftShoulder", "leftUpperArm", "leftElbow", "leftForearm", "leftWrist", "leftHand", "upperChest", "lowerChest", "upperAbdomen", "lowerAbdomen", "pelvis", "rightHip", "rightThigh", "<PERSON><PERSON><PERSON>", "rightCalf", "right<PERSON>nkle", "rightFoot", "leftHip", "leftThigh", "leftKnee", "leftCalf", "leftAnkle", "leftFoot", "headBack", "neckBack", "cervicalSpine", "thoracicSpine", "lumbarSpine", "sacrum", "rightShoulderBack", "leftShoulderBack", "rightUpperArmBack", "leftUpperArmBack", "rightElbowBack", "leftElbowBack", "rightGluteal", "leftGluteal", "rightThighBack", "leftThighBack", "rightCalfBack", "leftCalfBack", "rightFootBack", "map", "_ref2", "key", "region", "painLevel", "regionId", "painPoint", "find", "p", "getPainLevel", "_painLevels$find", "l", "getPainColor", "fillOpacity", "concat", "onClick", "handleRegionClick", "regionKey", "existingIndex", "findIndex", "currentPain", "nextLevel", "newSelected", "filter", "_objectSpread", "newPainPoint", "regionName", "regionNameAr", "anatomicalArea", "timestamp", "Date", "toISOString", "onMouseEnter", "onMouseLeave", "style", "fontSize", "slice", "backgroundColor"], "sourceRoot": ""}