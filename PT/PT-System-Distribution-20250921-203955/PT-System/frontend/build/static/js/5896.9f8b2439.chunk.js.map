{"version": 3, "file": "static/js/5896.9f8b2439.chunk.js", "mappings": "uNAKA,MA6sCA,EA7sCmCA,KACjC,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAEvCC,eAAe,MAADC,OAAQC,KAAKC,OAC3BC,WAAW,IAAIF,MAAOG,cAAcC,MAAM,KAAK,GAC/CC,QAAS,KACTC,aAAc,KAGdC,eAAgB,GAGhBC,0BAA2B,GAC3BC,qBAAsB,GACtBC,gBAAiB,GACjBC,qBAAsB,GAGtBC,4BAA6B,GAC7BC,uBAAwB,GACxBC,kBAAmB,GACnBC,uBAAwB,GAGxBC,mBAAoB,GACpBC,yBAA0B,GAC1BC,mBAAoB,GACpBC,yBAA0B,GAC1BC,SAAU,GAGVC,iBAAkB,GAClBC,sBAAuB,GAGvBC,eAAgB,GAChBC,aAAc,GAGdC,WAAY,GAGZC,cAAe,GACfC,kBAAmB,GACnBC,eAAgB,GAGhBC,2BAA4B,GAC5BC,kBAAmB,GAGnBC,cAAe,GAGfC,eAAmB,OAAJ3C,QAAI,IAAJA,OAAI,EAAJA,EAAM4C,OAAQ,GAC7BC,UAAW,GACXC,QAAS,GACTC,MAAM,IAAIpC,MAAOG,cAAcC,MAAM,KAAK,GAG1CiC,aAAiB,OAAJhD,QAAI,IAAJA,OAAI,EAAJA,EAAMiD,KAAM,GACzBC,aAAa,IAAIvC,MAAOG,iBAGnBqC,EAASC,IAAc5C,EAAAA,EAAAA,WAAS,IAChC6C,EAAQC,IAAa9C,EAAAA,EAAAA,UAAS,CAAC,GAGhC+C,EAAwB,CAC5B,CAAEC,MAAO,UAAWC,MAAO5D,EAAE,oBAAqB,uBAClD,CAAE2D,MAAO,eAAgBC,MAAO5D,EAAE,eAAgB,kBAClD,CAAE2D,MAAO,YAAaC,MAAO5D,EAAE,sBAAuB,0BAGlD6D,EAA4B,CAChC,CAAEF,MAAO,QAASC,MAAO5D,EAAE,QAAS,UACpC,CAAE2D,MAAO,oBAAqBC,MAAO5D,EAAE,0BAA2B,mCAClE,CAAE2D,MAAO,gBAAiBC,MAAO5D,EAAE,gBAAiB,kBACpD,CAAE2D,MAAO,QAASC,MAAO5D,EAAE,QAAS,WAGhC8D,EAAiB,CACrB,CAAEH,MAAO,WAAYC,MAAO5D,EAAE,WAAY,aAC1C,CAAE2D,MAAO,YAAaC,MAAO5D,EAAE,YAAa,cAC5C,CAAE2D,MAAO,YAAaC,MAAO5D,EAAE,YAAa,cAC5C,CAAE2D,MAAO,mBAAoBC,MAAO5D,EAAE,kBAAmB,qBACzD,CAAE2D,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,oBACvD,CAAE2D,MAAO,yBAA0BC,MAAO5D,EAAE,wBAAyB,2BACrE,CAAE2D,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,oBACvD,CAAE2D,MAAO,gBAAiBC,MAAO5D,EAAE,gBAAiB,kBACpD,CAAE2D,MAAO,WAAYC,MAAO5D,EAAE,WAAY,aAC1C,CAAE2D,MAAO,mBAAoBC,MAAO5D,EAAE,kBAAmB,qBACzD,CAAE2D,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,oBACvD,CAAE2D,MAAO,wBAAyBC,MAAO5D,EAAE,uBAAwB,6BACnE,CAAE2D,MAAO,qBAAsBC,MAAO5D,EAAE,oBAAqB,uBAC7D,CAAE2D,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,oBACvD,CAAE2D,MAAO,MAAOC,MAAO5D,EAAE,MAAO,QAChC,CAAE2D,MAAO,aAAcC,MAAO5D,EAAE,YAAa,eAC7C,CAAE2D,MAAO,SAAUC,MAAO5D,EAAE,SAAU,YAGlC+D,EAA4B,CAChC,CAAEJ,MAAO,YAAaC,MAAO5D,EAAE,YAAa,cAC5C,CAAE2D,MAAO,cAAeC,MAAO5D,EAAE,cAAe,gBAChD,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,mBACrD,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,oBAGjDgE,EAA4B,CAChC,CAAEL,MAAO,OAAQC,MAAO5D,EAAE,OAAQ,SAClC,CAAE2D,MAAO,WAAYC,MAAO5D,EAAE,WAAY,aAC1C,CAAE2D,MAAO,OAAQC,MAAO5D,EAAE,OAAQ,SAClC,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,oBAGjDiE,EAA0B,CAC9B,CAAEN,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,oBACvD,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,yEACrD,CAAE2D,MAAO,oBAAqBC,MAAO5D,EAAE,mBAAoB,gDAC3D,CAAE2D,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,oBACvD,CAAE2D,MAAO,2BAA4BC,MAAO5D,EAAE,0BAA2B,6BACzE,CAAE2D,MAAO,sBAAuBC,MAAO5D,EAAE,qBAAsB,wBAC/D,CAAE2D,MAAO,wBAAyBC,MAAO5D,EAAE,uBAAwB,6CACnE,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,0CACrD,CAAE2D,MAAO,oBAAqBC,MAAO5D,EAAE,mBAAoB,6CAC3D,CAAE2D,MAAO,mBAAoBC,MAAO5D,EAAE,kBAAmB,gDACzD,CAAE2D,MAAO,oBAAqBC,MAAO5D,EAAE,mBAAoB,yCAC3D,CAAE2D,MAAO,YAAaC,MAAO5D,EAAE,WAAY,4BAC3C,CAAE2D,MAAO,mBAAoBC,MAAO5D,EAAE,kBAAmB,+BACzD,CAAE2D,MAAO,mBAAoBC,MAAO5D,EAAE,kBAAmB,qBACzD,CAAE2D,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,8CACvD,CAAE2D,MAAO,qBAAsBC,MAAO5D,EAAE,oBAAqB,uBAC7D,CAAE2D,MAAO,YAAaC,MAAO5D,EAAE,YAAa,cAC5C,CAAE2D,MAAO,cAAeC,MAAO5D,EAAE,aAAc,iBAC/C,CAAE2D,MAAO,SAAUC,MAAO5D,EAAE,SAAU,YAGlCkE,EAAwB,CAC5B,CAAEP,MAAO,aAAcC,MAAO5D,EAAE,WAAY,eAC5C,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,mBACrD,CAAE2D,MAAO,UAAWC,MAAO5D,EAAE,UAAW,aAGpCmE,EAAsB,CAC1B,CAAER,MAAO,oBAAqBC,MAAO5D,EAAE,mBAAoB,sBAC3D,CAAE2D,MAAO,QAASC,MAAO5D,EAAE,QAAS,UACpC,CAAE2D,MAAO,sBAAuBC,MAAO5D,EAAE,qBAAsB,wBAC/D,CAAE2D,MAAO,sBAAuBC,MAAO5D,EAAE,qBAAsB,wBAC/D,CAAE2D,MAAO,QAASC,MAAO5D,EAAE,QAAS,UACpC,CAAE2D,MAAO,gBAAiBC,MAAO5D,EAAE,gBAAiB,mBAGhDoE,EAAoB,CACxB,CAAET,MAAO,gBAAiBC,MAAO5D,EAAE,eAAgB,0CACnD,CAAE2D,MAAO,mBAAoBC,MAAO5D,EAAE,kBAAmB,uCACzD,CAAE2D,MAAO,uBAAwBC,MAAO5D,EAAE,sBAAuB,6CACjE,CAAE2D,MAAO,kBAAmBC,MAAO5D,EAAE,iBAAkB,4BACvD,CAAE2D,MAAO,uBAAwBC,MAAO5D,EAAE,sBAAuB,yBACjE,CAAE2D,MAAO,oBAAqBC,MAAO5D,EAAE,mBAAoB,6BAC3D,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,mBACrD,CAAE2D,MAAO,gBAAiBC,MAAO5D,EAAE,eAAgB,oBAG/CqE,EAAuB,CAC3B,CAAEV,MAAO,SAAUC,MAAO5D,EAAE,SAAU,WACtC,CAAE2D,MAAO,OAAQC,MAAO5D,EAAE,OAAQ,SAClC,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,oBAGjDsE,EAA4B,CAChC,CAAEX,MAAO,MAAOC,MAAO5D,EAAE,MAAO,QAChC,CAAE2D,MAAO,KAAMC,MAAO5D,EAAE,KAAM,OAC9B,CAAE2D,MAAO,iBAAkBC,MAAO5D,EAAE,gBAAiB,oBAGjDuE,EAAe,CACnB,CAAEZ,MAAO,MAAOC,MAAO5D,EAAE,MAAO,QAChC,CAAE2D,MAAO,KAAMC,MAAO5D,EAAE,KAAM,QAG1BwE,EAAsB,CAC1B,CAAEb,MAAO,YAAaC,MAAO5D,EAAE,YAAa,cAC5C,CAAE2D,MAAO,SAAUC,MAAO5D,EAAE,SAAU,WACtC,CAAE2D,MAAO,eAAgBC,MAAO5D,EAAE,eAAgB,kBAClD,CAAE2D,MAAO,uBAAwBC,MAAO5D,EAAE,sBAAuB,yBACjE,CAAE2D,MAAO,SAAUC,MAAO5D,EAAE,SAAU,aAGxCyE,EAAAA,EAAAA,WAAU,KACJpE,GACFqE,KAED,CAACrE,IAEJ,MAAMqE,EAAkBC,UACtB,IACEpB,GAAW,GACX,MAAMqB,QAAiBC,MAAM,iBAADhE,OAAkBR,IAC9C,GAAIuE,EAASE,GAAI,OACWF,EAASG,MAErC,CACF,CAAE,MAAOC,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACCzB,GAAW,EACb,GAGI2B,EAAoBA,CAACC,EAAOxB,KAChCjD,EAAY0E,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACD,GAAQxB,KAGPH,EAAO2B,IACT1B,EAAU2B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACD,GAAQ,SAKTG,EAAuBA,CAACH,EAAOI,EAAQC,KAC3C9E,EAAY0E,IACV,IAAIK,EAcJ,OAZEA,EADED,EACS,IAAIJ,EAAKD,GAAQI,GAEjBH,EAAKD,GAAOO,OAAOC,GAAQA,IAASJ,GAI7CJ,EAAMS,SAAS,aAA0B,eAAXL,GAA2BC,EAC3DC,EAAW,CAAC,cACHN,EAAMS,SAAS,aAA0B,eAAXL,GAA2BC,GAAWJ,EAAKD,GAAOS,SAAS,gBAClGH,EAAWA,EAASC,OAAOC,GAAiB,eAATA,KAGrCN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACKD,GAAI,IACP,CAACD,GAAQM,OAwGf,OAAInC,GAEAuC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wFAAuFC,UACpGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+GAA8GC,SACzH/F,EAAE,6BAA8B,+DAEnCgG,EAAAA,EAAAA,MAAA,KAAGF,UAAU,kEAAiEC,SAAA,EAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CACZ9F,EAAE,2BAA4B,0EAEjCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCACZ9F,EAAE,gBAAiB,qBAEtBgG,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCACZ9F,EAAE,SAAU,kBAEfgG,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCACZ9F,EAAE,YAAa,uBAItBgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uCAAsCC,SAAA,EACnDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACZ9F,EAAE,YAAa,iBAElBgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mGAAkGC,SAAA,EAC/GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZ9F,EAAE,eAAgB,iCAS/BgG,EAAAA,EAAAA,MAAA,QAAMC,SA5FWtB,UAGnB,GAFAuB,EAAEC,iBA9DiBC,MACnB,MAAMC,EAAY,CAAC,EAcnB,OAXK5F,EAASY,iBAAgBgF,EAAUhF,eAAiBrB,EAAE,yBAA0B,gCAChFS,EAASqB,qBAAoBuE,EAAUvE,mBAAqB9B,EAAE,6BAA8B,qCAC5FS,EAASuB,qBAAoBqE,EAAUrE,mBAAqBhC,EAAE,6BAA8B,qCAC5FS,EAAS+B,gBAAe6D,EAAU7D,cAAgBxC,EAAE,wBAAyB,qCAC7ES,EAASgC,oBAAmB4D,EAAU5D,kBAAoBzC,EAAE,4BAA6B,yCACzFS,EAASiC,iBAAgB2D,EAAU3D,eAAiB1C,EAAE,yBAA0B,sCAChFS,EAASqC,cAAcwD,SAAQD,EAAUvD,cAAgB9C,EAAE,wBAAyB,+BACpFS,EAASuC,UAAUsD,SAAQD,EAAUrD,UAAYhD,EAAE,oBAAqB,0BACxES,EAASwC,QAAQqD,SAAQD,EAAUpD,QAAUjD,EAAE,kBAAmB,6BAEvEyD,EAAU4C,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWI,QAiDzBL,GAIL,IACE7C,GAAW,GAEX,MAAMmD,GAAcrB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACf5E,GAAQ,IACX0C,YAAahD,EAAKiD,GAClBC,aAAa,IAAIvC,MAAOG,gBAW1B,WARuB4D,MAAM,0CAA2C,CACtE8B,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAUL,MAGV5B,GAIX,MAAM,IAAIkC,MAAM,iCAHhBC,MAAMjH,EAAE,qBAAsB,0DAC9BO,EAASF,EAAS,aAAAQ,OAAgBR,GAAc,YAIpD,CAAE,MAAO2E,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CiC,MAAMjH,EAAE,cAAe,kDACzB,CAAC,QACCuD,GAAW,EACb,GAyDgCuC,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0JAAyJC,SAAA,EACtKC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gFAA+EC,SAAA,EAC3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZ9F,EAAE,sBAAuB,4BAG5BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,4EAA2EC,SAAA,EAC1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZ9F,EAAE,iBAAkB,uBAEvB6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASG,eAChBuG,SAAWjB,GAAMhB,EAAkB,iBAAkBgB,EAAEkB,OAAOzD,OAC9DmC,UAAU,oIACVuB,UAAQ,QAGZrB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,4EAA2EC,SAAA,EAC1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZ9F,EAAE,YAAa,kBAElB6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASO,UAChBmG,SAAWjB,GAAMhB,EAAkB,YAAagB,EAAEkB,OAAOzD,OACzDmC,UAAU,yKAGdE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,gFAA+EC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZ9F,EAAE,UAAW,eAEhB6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASU,QAChB2E,UAAU,gJACVuB,UAAQ,QAGZrB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,gFAA+EC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZ9F,EAAE,eAAgB,qBAErB6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASW,aAChB0E,UAAU,gJACVuB,UAAQ,cAOhBrB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sKAAqKC,SAAA,EAClLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oFAAmFC,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qEACZ9F,EAAE,iBAAkB,uBAGvB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0GAAyGC,UACtHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,+CAA8CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZ9F,EAAE,kCAAmC,2FAI1C6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClCrC,EAAsB4D,IAAK/B,IAC1BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,wHAAuHC,SAAA,EACvJF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,QACL9D,GAAE,cAAAvC,OAAgB0E,EAAO5B,OACzBZ,KAAK,iBACLY,MAAO4B,EAAO5B,MACd6B,QAAS/E,EAASY,iBAAmBkE,EAAO5B,MAC5CwD,SAAWjB,GAAMhB,EAAkB,iBAAkBgB,EAAEkB,OAAOzD,OAC9DmC,UAAU,0EAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,cAAA1G,OAAgB0E,EAAO5B,OAASmC,UAAU,2DAA0DC,SAC/GR,EAAO3B,UAXF2B,EAAO5B,UAgBpBH,EAAOnC,iBACN2E,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZtC,EAAOnC,sBAMd2E,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0JAAyJC,SAAA,EACtKC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gFAA+EC,SAAA,EAC3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZ9F,EAAE,6BAA8B,iDAGnC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DACZ9F,EAAE,6BAA8B,8IAIrCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gFAA+EC,SAAA,EAC3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZ9F,EAAE,4BAA6B,mCAGlCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBlC,EAA0ByD,IAAK/B,IAC9BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,oHAAmHC,SAAA,EACnJF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,gBAAAvC,OAAkB0E,EAAO5B,OAC3B6B,QAAS/E,EAASa,0BAA0BsE,SAASL,EAAO5B,OAC5DwD,SAAWjB,GAAMZ,EAAqB,4BAA6BC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAC1FM,UAAU,oFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,gBAAA1G,OAAkB0E,EAAO5B,OAASmC,UAAU,yDAAwDC,SAC/GR,EAAO3B,UATF2B,EAAO5B,QAclBlD,EAASa,0BAA0BsE,SAAS,WAC3CC,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASc,qBAChB4F,SAAWjB,GAAMhB,EAAkB,uBAAwBgB,EAAEkB,OAAOzD,OACpE6D,YAAaxH,EAAE,eAAgB,oBAC/B8F,UAAU,iLACV2B,KAAK,aAObzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wFAAuFC,SAAA,EACpGC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4EAA2EC,SAAA,EACvFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACZ9F,EAAE,kBAAmB,wBAGxBgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBjC,EAAewD,IAAK/B,IACnBS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,8GAA6GC,SAAA,EAC7IF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,mBAAAvC,OAAqB0E,EAAO5B,OAC9B6B,QAAS/E,EAASe,gBAAgBoE,SAASL,EAAO5B,OAClDwD,SAAWjB,GAAMZ,EAAqB,kBAAmBC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAChFkC,SAAUjH,EAASe,gBAAgBoE,SAAS,eAAkC,eAAjBL,EAAO5B,MACpEmC,UAAU,8EAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,mBAAA1G,OAAqB0E,EAAO5B,OAASmC,UAAS,uBAAAjF,OAC1DJ,EAASe,gBAAgBoE,SAAS,eAAkC,eAAjBL,EAAO5B,MACtD,iCACA,kCACHoC,SACAR,EAAO3B,UAdF2B,EAAO5B,QAmBlBlD,EAASe,gBAAgBoE,SAAS,YACjCC,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASgB,qBAChB0F,SAAWjB,GAAMhB,EAAkB,uBAAwBgB,EAAEkB,OAAOzD,OACpE6D,YAAaxH,EAAE,eAAgB,oBAC/B8F,UAAU,qKACV2B,KAAK,mBASjBzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sKAAqKC,SAAA,EAClLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oFAAmFC,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DACZ9F,EAAE,+BAAgC,mDAGrC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0GAAyGC,UACtHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,+CAA8CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DACZ9F,EAAE,+BAAgC,4IAIvCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,8BAA+B,qCAGpCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBlC,EAA0ByD,IAAK/B,IAC9BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,kBAAAvC,OAAoB0E,EAAO5B,OAC7B6B,QAAS/E,EAASiB,4BAA4BkE,SAASL,EAAO5B,OAC9DwD,SAAWjB,GAAMZ,EAAqB,8BAA+BC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAC5FM,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,kBAAA1G,OAAoB0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SACnGR,EAAO3B,UATF2B,EAAO5B,QAclBlD,EAASiB,4BAA4BkE,SAAS,WAC7CC,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASkB,uBAChBwF,SAAWjB,GAAMhB,EAAkB,yBAA0BgB,EAAEkB,OAAOzD,OACtE6D,YAAaxH,EAAE,eAAgB,oBAC/B8F,UAAU,uIACV2B,KAAK,aAObzB,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,oBAAqB,yBAG1BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBjC,EAAewD,IAAK/B,IACnBS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,qBAAAvC,OAAuB0E,EAAO5B,OAChC6B,QAAS/E,EAASmB,kBAAkBgE,SAASL,EAAO5B,OACpDwD,SAAWjB,GAAMZ,EAAqB,oBAAqBC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAClFkC,SAAUjH,EAASmB,kBAAkBgE,SAAS,eAAkC,eAAjBL,EAAO5B,MACtEmC,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,qBAAA1G,OAAuB0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SACtGR,EAAO3B,UAVF2B,EAAO5B,QAelBlD,EAASmB,kBAAkBgE,SAAS,YACnCC,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASoB,uBAChBsF,SAAWjB,GAAMhB,EAAkB,yBAA0BgB,EAAEkB,OAAOzD,OACtE6D,YAAaxH,EAAE,eAAgB,oBAC/B8F,UAAU,uIACV2B,KAAK,mBASjBzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gKAA+JC,SAAA,EAC5KC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gFAA+EC,SAAA,EAC3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACZ9F,EAAE,kBAAmB,oCAGxB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACZ9F,EAAE,sBAAuB,sGAI9BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kFAAiFC,SAAA,EAC7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DACZ9F,EAAE,4BAA6B,oCAGlC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBhC,EAA0BuD,IAAK/B,IAC9BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,wHAAuHC,SAAA,EACvJF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,QACL9D,GAAE,cAAAvC,OAAgB0E,EAAO5B,OACzBZ,KAAK,qBACLY,MAAO4B,EAAO5B,MACd6B,QAAS/E,EAASqB,qBAAuByD,EAAO5B,MAChDwD,SAAWjB,GAAMhB,EAAkB,qBAAsBgB,EAAEkB,OAAOzD,OAClEmC,UAAU,0EAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,cAAA1G,OAAgB0E,EAAO5B,OAASmC,UAAU,2DAA0DC,SAC/GR,EAAO3B,UAXF2B,EAAO5B,UAiBY,mBAAhClD,EAASqB,qBACRkE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,SAAU,aAEf6F,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASsB,yBAChBoF,SAAWjB,GAAMhB,EAAkB,2BAA4BgB,EAAEkB,OAAOzD,OACxE6D,YAAaxH,EAAE,gBAAiB,qBAChC8F,UAAU,kIACV2B,KAAK,SAKVjE,EAAO1B,qBACN+D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAO1B,yBAKrDkE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,4BAA6B,mCAGlC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB/B,EAA0BsD,IAAK/B,IAC9BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,oBAAmBC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,QACL9D,GAAE,cAAAvC,OAAgB0E,EAAO5B,OACzBZ,KAAK,qBACLY,MAAO4B,EAAO5B,MACd6B,QAAS/E,EAASuB,qBAAuBuD,EAAO5B,MAChDwD,SAAWjB,GAAMhB,EAAkB,qBAAsBgB,EAAEkB,OAAOzD,OAClEmC,UAAU,oEAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,cAAA1G,OAAgB0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SAC/FR,EAAO3B,UAXF2B,EAAO5B,UAiBY,mBAAhClD,EAASuB,qBACRgE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,SAAU,aAEf6F,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASwB,yBAChBkF,SAAWjB,GAAMhB,EAAkB,2BAA4BgB,EAAEkB,OAAOzD,OACxE6D,YAAaxH,EAAE,gBAAiB,qBAChC8F,UAAU,kIACV2B,KAAK,SAKVjE,EAAOxB,qBACN6D,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAOxB,4BAMvDgE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,WAAY,eAEjB6F,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASyB,SAChBiF,SAAWjB,GAAMhB,EAAkB,WAAYgB,EAAEkB,OAAOzD,OACxD6D,YAAaxH,EAAE,qBAAsB,0BACrC8F,UAAU,kIACV2B,KAAK,aAMXzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0JAAyJC,SAAA,EACtKC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gFAA+EC,SAAA,EAC3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2DACZ9F,EAAE,mBAAoB,yBAGzB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZ9F,EAAE,8BAA+B,sHAItC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClE9B,EAAwBqD,IAAK/B,IAC5BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,iHAAgHC,SAAA,EAChJF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,YAAAvC,OAAc0E,EAAO5B,OACvB6B,QAAS/E,EAAS0B,iBAAiByD,SAASL,EAAO5B,OACnDwD,SAAWjB,GAAMZ,EAAqB,mBAAoBC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SACjFM,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,YAAA1G,OAAc0E,EAAO5B,OAASmC,UAAU,uDAAsDC,SACzGR,EAAO3B,UATF2B,EAAO5B,UAepBlD,EAAS0B,iBAAiByD,SAAS,YAClCI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,wBAAyB,8BAE9B6F,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAAS2B,sBAChB+E,SAAWjB,GAAMhB,EAAkB,wBAAyBgB,EAAEkB,OAAOzD,OACrE6D,YAAaxH,EAAE,eAAgB,oBAC/B8F,UAAU,kIACV2B,KAAK,aAObzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sKAAqKC,SAAA,EAClLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,oFAAmFC,SAAA,EAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEACZ9F,EAAE,iBAAkB,uBAGvB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0GAAyGC,UACtHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,+CAA8CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACZ9F,EAAE,4BAA6B,sGAIpCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,iBAAkB,sBAGvB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB7B,EAAsBoD,IAAK/B,IAC1BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,UAAAvC,OAAY0E,EAAO5B,OACrB6B,QAAS/E,EAAS4B,eAAeuD,SAASL,EAAO5B,OACjDwD,SAAWjB,GAAMZ,EAAqB,iBAAkBC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAC/EM,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,UAAA1G,OAAY0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SAC3FR,EAAO3B,UATF2B,EAAO5B,cAiBvBqC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,eAAgB,oBAGrB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB5B,EAAoBmD,IAAK/B,IACxBS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,QAAAvC,OAAU0E,EAAO5B,OACnB6B,QAAS/E,EAAS6B,aAAasD,SAASL,EAAO5B,OAC/CwD,SAAWjB,GAAMZ,EAAqB,eAAgBC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAC7EM,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,QAAA1G,OAAU0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SACzFR,EAAO3B,UATF2B,EAAO5B,oBAmB3BqC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sKAAqKC,SAAA,EAClLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,sFAAqFC,SAAA,EACjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEACZ9F,EAAE,aAAc,kBAGnB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8GAA6GC,UAC1HC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,iDAAgDC,SAAA,EAC3DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACZ9F,EAAE,wBAAyB,yFAIhC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,SACnD3B,EAAkBkD,IAAK/B,IACtBS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,QAAAvC,OAAU0E,EAAO5B,OACnB6B,QAAS/E,EAAS8B,WAAWqD,SAASL,EAAO5B,OAC7CwD,SAAWjB,GAAMZ,EAAqB,aAAcC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAC3EM,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,QAAA1G,OAAU0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SACzFR,EAAO3B,UATF2B,EAAO5B,cAiBvBqC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kKAAiKC,SAAA,EAC9KC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kFAAiFC,SAAA,EAC7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mEACZ9F,EAAE,sBAAuB,4BAG5B6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sGAAqGC,UAClHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6CAA4CC,SAAA,EACvDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZ9F,EAAE,iCAAkC,2GAIzCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,gBAAiB,qBAGtB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClC1B,EAAqBiD,IAAK/B,IACzBS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,oBAAmBC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,QACL9D,GAAE,UAAAvC,OAAY0E,EAAO5B,OACrBZ,KAAK,gBACLY,MAAO4B,EAAO5B,MACd6B,QAAS/E,EAAS+B,gBAAkB+C,EAAO5B,MAC3CwD,SAAWjB,GAAMhB,EAAkB,gBAAiBgB,EAAEkB,OAAOzD,OAC7DmC,UAAU,oEAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,UAAA1G,OAAY0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SAC3FR,EAAO3B,UAXF2B,EAAO5B,UAgBpBH,EAAOhB,gBACNqD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAOhB,oBAKrDwD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,8BAA+B,kEAGpC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClCzB,EAA0BgD,IAAK/B,IAC9BS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,oBAAmBC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,QACL9D,GAAE,YAAAvC,OAAc0E,EAAO5B,OACvBZ,KAAK,oBACLY,MAAO4B,EAAO5B,MACd6B,QAAS/E,EAASgC,oBAAsB8C,EAAO5B,MAC/CwD,SAAWjB,GAAMhB,EAAkB,oBAAqBgB,EAAEkB,OAAOzD,OACjEmC,UAAU,oEAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,YAAA1G,OAAc0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SAC7FR,EAAO3B,UAXF2B,EAAO5B,UAgBpBH,EAAOf,oBACNoD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAOf,wBAKrDuD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,iBAAkB,sBAGvB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClCxB,EAAa+C,IAAK/B,IACjBS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,oBAAmBC,SAAA,EACnDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,QACL9D,GAAE,YAAAvC,OAAc0E,EAAO5B,OACvBZ,KAAK,iBACLY,MAAO4B,EAAO5B,MACd6B,QAAS/E,EAASiC,iBAAmB6C,EAAO5B,MAC5CwD,SAAWjB,GAAMhB,EAAkB,iBAAkBgB,EAAEkB,OAAOzD,OAC9DmC,UAAU,oEAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,YAAA1G,OAAc0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SAC7FR,EAAO3B,UAXF2B,EAAO5B,UAgBpBH,EAAOd,iBACNmD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAOd,2BAOzDsD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE/F,EAAE,mCAAoC,6CAGzC6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClEvB,EAAoB8C,IAAK/B,IACxBS,EAAAA,EAAAA,MAAA,OAAwBF,UAAU,mBAAkBC,SAAA,EAClDF,EAAAA,EAAAA,KAAA,SACEqB,KAAK,WACL9D,GAAE,iBAAAvC,OAAmB0E,EAAO5B,OAC5B6B,QAAS/E,EAASkC,2BAA2BiD,SAASL,EAAO5B,OAC7DwD,SAAWjB,GAAMZ,EAAqB,6BAA8BC,EAAO5B,MAAOuC,EAAEkB,OAAO5B,SAC3FM,UAAU,iFAEZD,EAAAA,EAAAA,KAAA,SAAO0B,QAAO,iBAAA1G,OAAmB0E,EAAO5B,OAASmC,UAAU,2CAA0CC,SAClGR,EAAO3B,UATF2B,EAAO5B,UAepBlD,EAASkC,2BAA2BiD,SAAS,YAC5CI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,oBAAqB,0BAE1B6F,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASmC,kBAChBuE,SAAWjB,GAAMhB,EAAkB,oBAAqBgB,EAAEkB,OAAOzD,OACjE6D,YAAaxH,EAAE,eAAgB,oBAC/B8F,UAAU,kIACV2B,KAAK,aAObzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE/F,EAAE,mBAAoB,yBAGzB6F,EAAAA,EAAAA,KAAA,YACElC,MAAOlD,EAASoC,cAChBsE,SAAWjB,GAAMhB,EAAkB,gBAAiBgB,EAAEkB,OAAOzD,OAC7D6D,YAAaxH,EAAE,2BAA4B,0CAC3C8F,UAAU,kIACV2B,KAAK,UAKTzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8JAA6JC,SAAA,EAC1KC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kFAAiFC,SAAA,EAC7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZ9F,EAAE,qBAAsB,2BAG3B6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sGAAqGC,UAClHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6CAA4CC,SAAA,EACvDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZ9F,EAAE,uBAAwB,2GAI/BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E/F,EAAE,gBAAiB,kBAAkB,SAExC6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASqC,cAChBqE,SAAWjB,GAAMhB,EAAkB,gBAAiBgB,EAAEkB,OAAOzD,OAC7DmC,UAAS,8FAAAjF,OACP2C,EAAOV,cAAgB,iBAAmB,wCAE5C6E,UAAQ,IAETnE,EAAOV,gBACN+C,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAOV,oBAIrDkD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E/F,EAAE,YAAa,aAAa,SAE/B6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASuC,UAChBmE,SAAWjB,GAAMhB,EAAkB,YAAagB,EAAEkB,OAAOzD,OACzDmC,UAAS,8FAAAjF,OACP2C,EAAOR,UAAY,iBAAmB,wCAExC2E,UAAQ,IAETnE,EAAOR,YACN6C,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAOR,gBAIrDgD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E/F,EAAE,UAAW,aAAa,SAE7B6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASwC,QAChBkE,SAAWjB,GAAMhB,EAAkB,UAAWgB,EAAEkB,OAAOzD,OACvDmC,UAAS,8FAAAjF,OACP2C,EAAOP,QAAU,iBAAmB,wCAEtC0E,UAAQ,IAETnE,EAAOP,UACN4C,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAEvC,EAAOP,cAIrD+C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,OAAQ,WAEb6F,EAAAA,EAAAA,KAAA,SACEqB,KAAK,OACLvD,MAAOlD,EAASyC,KAChBiE,SAAWjB,GAAMhB,EAAkB,OAAQgB,EAAEkB,OAAOzD,OACpDmC,UAAU,8IAOlBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sJAAqJC,UAClKC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,UACEkB,KAAK,SACLU,QAASA,IAAMrH,GAAU,GACzBuF,UAAU,6KAA4KC,SAAA,EAEtLF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BACZ9F,EAAE,SAAU,cAGfgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEkB,KAAK,SACLU,QAx6BMjD,UAClB,IACEpB,GAAW,GAEX,MAAMsE,GAAOxC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACR5E,GAAQ,IACXqH,aAAa,IAAIhH,MAAOG,cACxB8G,YAAa5H,EAAK4C,MAAQ5C,EAAK6H,MAC/B3H,UAAWA,IAGPuE,QAAiBC,MAAM,uCAAwC,CACnE8B,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAAD/F,OAAYoH,aAAaC,QAAQ,WAElDrB,KAAMC,KAAKC,UAAUc,KAGvB,IAAIjD,EAASE,GAaX,MAAM,IAAIkC,MAAM,uBAADnG,OAAwB+D,EAASuD,SAbjC,CACf,MAAMC,QAAaxD,EAASwD,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,4BAAAhI,OAA+BJ,EAASqC,cAAcgG,QAAQ,OAAQ,KAAI,KAAAjI,OAAIJ,EAASyC,KAAI,QACrGwF,SAAS7B,KAAKkC,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAAS7B,KAAKqC,YAAYT,GAE1BxB,MAAMjH,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAOgF,GACPC,QAAQD,MAAM,wBAAyBA,GACvCiC,MAAMjH,EAAE,qBAAsB,2CAChC,CAAC,QACCuD,GAAW,EACb,GAi4BYmE,SAAUpE,EACVwC,UAAU,yNAAwNC,SAAA,EAElOF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZxC,EAAUtD,EAAE,aAAc,iBAAmBA,EAAE,cAAe,oBAGjEgG,EAAAA,EAAAA,MAAA,UACEkB,KAAK,SACLQ,SAAUpE,EACVwC,UAAU,+NAA8NC,SAAA,EAExOF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZxC,EAAUtD,EAAE,SAAU,aAAeA,EAAE,oBAAqB,wC", "sources": ["pages/Forms/PatientFamilyEducationForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nconst PatientFamilyEducationForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [formData, setFormData] = useState({\n    // Document Metadata\n    documentNumber: `QP-${Date.now()}`,\n    issueDate: new Date().toISOString().split('T')[0],\n    version: '01',\n    reviewNumber: '01',\n    \n    // Assessment Type\n    assessmentType: '',\n    \n    // Patient Learning Preferences and Barriers\n    patientLearningPreference: [],\n    patientLearningOther: '',\n    patientBarriers: [],\n    patientBarriersOther: '',\n    \n    // Caregiver Learning Preferences and Barriers\n    caregiverLearningPreference: [],\n    caregiverLearningOther: '',\n    caregiverBarriers: [],\n    caregiverBarriersOther: '',\n    \n    // Patient Self-Care\n    selfCareCapability: '',\n    selfCareCapabilityReason: '',\n    selfCareMotivation: '',\n    selfCareMotivationReason: '',\n    comments: '',\n    \n    // Educational Needs\n    educationalNeeds: [],\n    educationalNeedsOther: '',\n    \n    // Given Education\n    teachingMethod: [],\n    teachingTool: [],\n    \n    // Evaluation\n    evaluation: [],\n    \n    // Additional Questions\n    patientAttend: '',\n    problemsAttending: '',\n    literacySkills: '',\n    \n    // Post-Discharge Care\n    careProviderAfterDischarge: [],\n    careProviderOther: '',\n    \n    // Plans and Comments\n    plansComments: '',\n    \n    // Therapist Signature\n    therapistName: user?.name || '',\n    signature: '',\n    badgeNo: '',\n    date: new Date().toISOString().split('T')[0],\n    \n    // Metadata\n    submittedBy: user?.id || '',\n    submittedAt: new Date().toISOString()\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  // Form options\n  const assessmentTypeOptions = [\n    { value: 'initial', label: t('initialAssessment', 'Initial Assessment') },\n    { value: 'reassessment', label: t('reAssessment', 'Re-Assessment') },\n    { value: 'discharge', label: t('dischargeAssessment', 'Discharge Assessment') }\n  ];\n\n  const learningPreferenceOptions = [\n    { value: 'video', label: t('video', 'Video') },\n    { value: 'written_materials', label: t('writtenMaterialsPicture', 'Written Materials with Picture') },\n    { value: 'demonstration', label: t('demonstration', 'Demonstration') },\n    { value: 'other', label: t('other', 'Other') }\n  ];\n\n  const barrierOptions = [\n    { value: 'physical', label: t('physical', 'Physical') },\n    { value: 'cognitive', label: t('cognitive', 'Cognitive') },\n    { value: 'emotional', label: t('emotional', 'Emotional') },\n    { value: 'cultural_believe', label: t('culturalBelieve', 'Cultural/Believe') },\n    { value: 'poor_motivation', label: t('poorMotivation', 'Poor Motivation') },\n    { value: 'financial_difficulties', label: t('financialDifficulties', 'Financial Difficulties') },\n    { value: 'reading_ability', label: t('readingAbility', 'Reading Ability') },\n    { value: 'psychological', label: t('psychological', 'Psychological') },\n    { value: 'language', label: t('language', 'Language') },\n    { value: 'impaired_hearing', label: t('impairedHearing', 'Impaired Hearing') },\n    { value: 'speech_barriers', label: t('speechBarriers', 'Speech Barriers') },\n    { value: 'responsibilities_home', label: t('responsibilitiesHome', 'Responsibilities at Home') },\n    { value: 'religious_practice', label: t('religiousPractice', 'Religious Practice') },\n    { value: 'impaired_vision', label: t('impairedVision', 'Impaired Vision') },\n    { value: 'age', label: t('age', 'Age') },\n    { value: 'no_barrier', label: t('noBarrier', 'No Barrier') },\n    { value: 'others', label: t('others', 'Others') }\n  ];\n\n  const selfCareCapabilityOptions = [\n    { value: 'dependent', label: t('dependent', 'Dependent') },\n    { value: 'independent', label: t('independent', 'Independent') },\n    { value: 'need_assistant', label: t('needAssistant', 'Need Assistant') },\n    { value: 'not_applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  const selfCareMotivationOptions = [\n    { value: 'poor', label: t('poor', 'Poor') },\n    { value: 'moderate', label: t('moderate', 'Moderate') },\n    { value: 'high', label: t('high', 'High') },\n    { value: 'not_applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  const educationalNeedsOptions = [\n    { value: 'disease_process', label: t('diseaseProcess', 'Disease Process') },\n    { value: 'medication_use', label: t('medicationUse', 'Safe and Effective Use of Medication, Side Effects, and Interactions') },\n    { value: 'medical_equipment', label: t('medicalEquipment', 'Safe and Effective Use of Medical Equipment') },\n    { value: 'pain_management', label: t('painManagement', 'Pain Management') },\n    { value: 'rehabilitation_technique', label: t('rehabilitationTechnique', 'Rehabilitation Technique') },\n    { value: 'community_resources', label: t('communityResources', 'Community Resources') },\n    { value: 'discharge_instruction', label: t('dischargeInstruction', 'Discharge Instruction of Continuing Care') },\n    { value: 'patient_rights', label: t('patientRights', \"Patients' Rights and Responsibilities\") },\n    { value: 'further_treatment', label: t('furtherTreatment', 'When and How to Obtain Further Treatment') },\n    { value: 'personal_hygiene', label: t('personalHygiene', 'Personal Hygiene and Practitioner Hand Wash') },\n    { value: 'infection_control', label: t('infectionControl', 'Infection Control and Related Issues') },\n    { value: 'care_plan', label: t('carePlan', 'Care Plan and Treatment') },\n    { value: 'informed_consent', label: t('informedConsent', 'Informed Consent Procedure') },\n    { value: 'home_instruction', label: t('homeInstruction', 'Home Instruction') },\n    { value: 'diagnostic_test', label: t('diagnosticTest', 'Diagnostic Test/Procedure Social Services') },\n    { value: 'health_maintenance', label: t('healthMaintenance', 'Health Maintenance') },\n    { value: 'nutrition', label: t('nutrition', 'Nutrition') },\n    { value: 'risk_safety', label: t('riskSafety', 'Risk, Safety') },\n    { value: 'others', label: t('others', 'Others') }\n  ];\n\n  const teachingMethodOptions = [\n    { value: 'one_to_one', label: t('oneToOne', 'One to One') },\n    { value: 'group_teaching', label: t('groupTeaching', 'Group Teaching') },\n    { value: 'lecture', label: t('lecture', 'Lecture') }\n  ];\n\n  const teachingToolOptions = [\n    { value: 'written_materials', label: t('writtenMaterials', 'Written Materials') },\n    { value: 'audio', label: t('audio', 'Audio') },\n    { value: 'verbal_instructions', label: t('verbalInstructions', 'Verbal Instructions') },\n    { value: 'written_instruction', label: t('writtenInstruction', 'Written Instruction') },\n    { value: 'video', label: t('video', 'Video') },\n    { value: 'demonstration', label: t('demonstration', 'Demonstration') }\n  ];\n\n  const evaluationOptions = [\n    { value: 'not_receptive', label: t('notReceptive', 'Not Receptive to Learning/No Learning') },\n    { value: 'unable_verbalize', label: t('unableVerbalize', 'Unable to Verbalize Basic Concepts') },\n    { value: 'verbalize_assistance', label: t('verbalizeAssistance', 'Verbalize Basic Concepts with Assistance') },\n    { value: 'verbalize_basic', label: t('verbalizeBAsic', 'Verbalize Basic Concept') },\n    { value: 'return_demonstration', label: t('returnDemonstration', 'Return Demonstration') },\n    { value: 'applies_knowledge', label: t('appliesKnowledge', 'Applies Knowledge/skills') },\n    { value: 'not_applicable', label: t('notApplicable', 'Not Applicable') },\n    { value: 'need_followup', label: t('needFollowup', 'Need Follow-up') }\n  ];\n\n  const patientAttendOptions = [\n    { value: 'school', label: t('school', 'School') },\n    { value: 'work', label: t('work', 'Work') },\n    { value: 'not_applicable', label: t('notApplicable', 'NOT APPLICABLE') }\n  ];\n\n  const yesNoNotApplicableOptions = [\n    { value: 'yes', label: t('yes', 'Yes') },\n    { value: 'no', label: t('no', 'No') },\n    { value: 'not_applicable', label: t('notApplicable', 'NOT APPLICABLE') }\n  ];\n\n  const yesNoOptions = [\n    { value: 'yes', label: t('yes', 'Yes') },\n    { value: 'no', label: t('no', 'No') }\n  ];\n\n  const careProviderOptions = [\n    { value: 'caregiver', label: t('caregiver', 'Caregiver') },\n    { value: 'family', label: t('family', 'Family') },\n    { value: 'reassessment', label: t('reassessment', 'Re-Assessment') },\n    { value: 'discharge_assessment', label: t('dischargeAssessment', 'Discharge Assessment') },\n    { value: 'others', label: t('others', 'Others') }\n  ];\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        // Auto-populate any relevant patient data\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleCheckboxChange = (field, option, checked) => {\n    setFormData(prev => {\n      let newArray;\n      if (checked) {\n        newArray = [...prev[field], option];\n      } else {\n        newArray = prev[field].filter(item => item !== option);\n      }\n\n      // Special logic for \"No Barrier\" - if selected, clear other barriers\n      if (field.includes('Barriers') && option === 'no_barrier' && checked) {\n        newArray = ['no_barrier'];\n      } else if (field.includes('Barriers') && option !== 'no_barrier' && checked && prev[field].includes('no_barrier')) {\n        newArray = newArray.filter(item => item !== 'no_barrier');\n      }\n\n      return {\n        ...prev,\n        [field]: newArray\n      };\n    });\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.assessmentType) newErrors.assessmentType = t('assessmentTypeRequired', 'Assessment type is required');\n    if (!formData.selfCareCapability) newErrors.selfCareCapability = t('selfCareCapabilityRequired', 'Self-care capability is required');\n    if (!formData.selfCareMotivation) newErrors.selfCareMotivation = t('selfCareMotivationRequired', 'Self-care motivation is required');\n    if (!formData.patientAttend) newErrors.patientAttend = t('patientAttendRequired', 'Patient attend field is required');\n    if (!formData.problemsAttending) newErrors.problemsAttending = t('problemsAttendingRequired', 'Problems attending field is required');\n    if (!formData.literacySkills) newErrors.literacySkills = t('literacySkillsRequired', 'Literacy skills field is required');\n    if (!formData.therapistName.trim()) newErrors.therapistName = t('therapistNameRequired', 'Therapist name is required');\n    if (!formData.signature.trim()) newErrors.signature = t('signatureRequired', 'Signature is required');\n    if (!formData.badgeNo.trim()) newErrors.badgeNo = t('badgeNoRequired', 'Badge number is required');\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n      \n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/patient-family-education/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `patient-family-education-${formData.therapistName.replace(/\\s+/g, '-')}-${formData.date}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n        \n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      const submissionData = {\n        ...formData,\n        submittedBy: user.id,\n        submittedAt: new Date().toISOString()\n      };\n\n      const response = await fetch('/api/v1/patient-family-education/public', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        alert(t('educationFormSaved', 'Patient and Family Education form saved successfully!'));\n        navigate(patientId ? `/patients/${patientId}` : '/patients');\n      } else {\n        throw new Error('Failed to save education form');\n      }\n    } catch (error) {\n      console.error('Error saving education form:', error);\n      alert(t('errorSaving', 'Error saving education form. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-6\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  {t('patientFamilyEducationForm', 'Patient and Family Education Form for Physical Therapist')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-stethoscope text-indigo-500 mr-2\"></i>\n                  {t('educationFormDescription', 'Comprehensive patient and family education assessment and planning')}\n                </p>\n                <div className=\"flex items-center space-x-4 mt-3\">\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-clock text-blue-500 mr-1\"></i>\n                    {t('estimatedTime', '20-30 minutes')}\n                  </span>\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-list text-green-500 mr-1\"></i>\n                    {t('fields', '35+ fields')}\n                  </span>\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-language text-purple-500 mr-1\"></i>\n                    {t('bilingual', 'Bilingual')}\n                  </span>\n                </div>\n              </div>\n              <div className=\"flex flex-col items-center space-y-2\">\n                <div className=\"bg-gradient-to-r from-emerald-400 to-cyan-400 text-white px-4 py-2 rounded-full shadow-lg\">\n                  <i className=\"fas fa-graduation-cap mr-2\"></i>\n                  {t('education', 'Education')}\n                </div>\n                <div className=\"bg-gradient-to-r from-orange-400 to-pink-400 text-white px-3 py-1 rounded-full text-sm shadow-md\">\n                  <i className=\"fas fa-heart mr-1\"></i>\n                  {t('specialNeeds', 'Special Needs')}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Document Header */}\n        <div className=\"bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg shadow-lg border border-cyan-200 dark:border-cyan-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-cyan-900 dark:text-cyan-100 mb-4 flex items-center\">\n            <i className=\"fas fa-file-alt text-cyan-600 dark:text-cyan-400 mr-2\"></i>\n            {t('documentInformation', 'Document Information')}\n          </h2>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n            <div className=\"bg-white dark:bg-cyan-800/20 border border-cyan-200 dark:border-cyan-600 rounded-lg p-3\">\n              <label className=\"block font-medium text-cyan-800 dark:text-cyan-200 mb-2 flex items-center\">\n                <i className=\"fas fa-hashtag text-cyan-600 dark:text-cyan-400 mr-1\"></i>\n                {t('documentNumber', 'Document Number')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.documentNumber}\n                onChange={(e) => handleInputChange('documentNumber', e.target.value)}\n                className=\"w-full px-2 py-1 border border-cyan-300 dark:border-cyan-600 rounded bg-cyan-50 dark:bg-cyan-700 text-cyan-900 dark:text-cyan-100\"\n                readOnly\n              />\n            </div>\n            <div className=\"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-3\">\n              <label className=\"block font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n                <i className=\"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1\"></i>\n                {t('issueDate', 'Issue Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={formData.issueDate}\n                onChange={(e) => handleInputChange('issueDate', e.target.value)}\n                className=\"w-full px-2 py-1 border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n            <div className=\"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-3\">\n              <label className=\"block font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center\">\n                <i className=\"fas fa-code-branch text-indigo-600 dark:text-indigo-400 mr-1\"></i>\n                {t('version', 'Version')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.version}\n                className=\"w-full px-2 py-1 border border-indigo-300 dark:border-indigo-600 rounded bg-indigo-50 dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100\"\n                readOnly\n              />\n            </div>\n            <div className=\"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-3\">\n              <label className=\"block font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center\">\n                <i className=\"fas fa-eye text-purple-600 dark:text-purple-400 mr-1\"></i>\n                {t('reviewNumber', 'Review Number')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.reviewNumber}\n                className=\"w-full px-2 py-1 border border-purple-300 dark:border-purple-600 rounded bg-purple-50 dark:bg-purple-700 text-purple-900 dark:text-purple-100\"\n                readOnly\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Assessment Type */}\n        <div className=\"bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-purple-200 dark:border-purple-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-purple-900 dark:text-purple-100 mb-4 flex items-center\">\n            <i className=\"fas fa-clipboard-check text-purple-600 dark:text-purple-400 mr-2\"></i>\n            {t('assessmentType', 'Assessment Type')}\n          </h2>\n\n          <div className=\"bg-purple-100 dark:bg-purple-800/30 border border-purple-300 dark:border-purple-600 rounded-lg p-4 mb-4\">\n            <p className=\"text-sm text-purple-800 dark:text-purple-200\">\n              <i className=\"fas fa-info-circle text-purple-600 dark:text-purple-400 mr-2\"></i>\n              {t('selectAssessmentTypeInstruction', 'Select the type of assessment being conducted for this patient education session.')}\n            </p>\n          </div>\n\n          <div className=\"flex flex-wrap gap-4\">\n            {assessmentTypeOptions.map((option) => (\n              <div key={option.value} className=\"flex items-center bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg px-4 py-2\">\n                <input\n                  type=\"radio\"\n                  id={`assessment-${option.value}`}\n                  name=\"assessmentType\"\n                  value={option.value}\n                  checked={formData.assessmentType === option.value}\n                  onChange={(e) => handleInputChange('assessmentType', e.target.value)}\n                  className=\"mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 border-purple-300\"\n                />\n                <label htmlFor={`assessment-${option.value}`} className=\"text-sm text-purple-800 dark:text-purple-200 font-medium\">\n                  {option.label}\n                </label>\n              </div>\n            ))}\n          </div>\n          {errors.assessmentType && (\n            <p className=\"text-red-500 text-sm mt-2 flex items-center\">\n              <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n              {errors.assessmentType}\n            </p>\n          )}\n        </div>\n\n        {/* Patient Learning Preferences and Barriers */}\n        <div className=\"bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-6 flex items-center\">\n            <i className=\"fas fa-user-graduate text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('patientLearningAndBarriers', 'Patient Learning Preferences and Barriers')}\n          </h2>\n\n          <div className=\"bg-blue-100 dark:bg-blue-800/30 border border-blue-300 dark:border-blue-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n              <i className=\"fas fa-lightbulb text-blue-600 dark:text-blue-400 mr-2\"></i>\n              {t('patientLearningInstruction', 'Identify how the patient learns best and any barriers that may affect their ability to understand and follow treatment instructions.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Patient Learning Preference */}\n            <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4\">\n              <h3 className=\"text-md font-medium text-green-900 dark:text-green-100 mb-4 flex items-center\">\n                <i className=\"fas fa-brain text-green-600 dark:text-green-400 mr-2\"></i>\n                {t('patientLearningPreference', 'Patient Learning Preference')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {learningPreferenceOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-start bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg px-3 py-2\">\n                    <input\n                      type=\"checkbox\"\n                      id={`patient-pref-${option.value}`}\n                      checked={formData.patientLearningPreference.includes(option.value)}\n                      onChange={(e) => handleCheckboxChange('patientLearningPreference', option.value, e.target.checked)}\n                      className=\"mt-1 mr-2 h-4 w-4 text-green-600 focus:ring-green-500 border-green-300 rounded\"\n                    />\n                    <label htmlFor={`patient-pref-${option.value}`} className=\"text-sm text-green-800 dark:text-green-200 font-medium\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n\n                {formData.patientLearningPreference.includes('other') && (\n                  <textarea\n                    value={formData.patientLearningOther}\n                    onChange={(e) => handleInputChange('patientLearningOther', e.target.value)}\n                    placeholder={t('specifyOther', 'Specify other...')}\n                    className=\"w-full mt-2 px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500\"\n                    rows=\"2\"\n                  />\n                )}\n              </div>\n            </div>\n\n            {/* Patient Barriers */}\n            <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4\">\n              <h3 className=\"text-md font-medium text-red-900 dark:text-red-100 mb-4 flex items-center\">\n                <i className=\"fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2\"></i>\n                {t('patientBarriers', 'Patient Barriers')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {barrierOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-start bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg px-3 py-2\">\n                    <input\n                      type=\"checkbox\"\n                      id={`patient-barrier-${option.value}`}\n                      checked={formData.patientBarriers.includes(option.value)}\n                      onChange={(e) => handleCheckboxChange('patientBarriers', option.value, e.target.checked)}\n                      disabled={formData.patientBarriers.includes('no_barrier') && option.value !== 'no_barrier'}\n                      className=\"mt-1 mr-2 h-4 w-4 text-red-600 focus:ring-red-500 border-red-300 rounded\"\n                    />\n                    <label htmlFor={`patient-barrier-${option.value}`} className={`text-sm font-medium ${\n                      formData.patientBarriers.includes('no_barrier') && option.value !== 'no_barrier'\n                        ? 'text-red-400 dark:text-red-500'\n                        : 'text-red-800 dark:text-red-200'\n                    }`}>\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n\n                {formData.patientBarriers.includes('others') && (\n                  <textarea\n                    value={formData.patientBarriersOther}\n                    onChange={(e) => handleInputChange('patientBarriersOther', e.target.value)}\n                    placeholder={t('specifyOther', 'Specify other...')}\n                    className=\"w-full mt-2 px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500\"\n                    rows=\"2\"\n                  />\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Caregiver Learning Preferences and Barriers */}\n        <div className=\"bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-orange-900 dark:text-orange-100 mb-6 flex items-center\">\n            <i className=\"fas fa-users text-orange-600 dark:text-orange-400 mr-2\"></i>\n            {t('caregiverLearningAndBarriers', 'Caregiver Learning Preferences and Barriers')}\n          </h2>\n\n          <div className=\"bg-orange-100 dark:bg-orange-800/30 border border-orange-300 dark:border-orange-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-orange-800 dark:text-orange-200\">\n              <i className=\"fas fa-heart text-orange-600 dark:text-orange-400 mr-2\"></i>\n              {t('caregiverLearningInstruction', 'Assess the caregiver\\'s learning preferences and identify any barriers that may affect their ability to support the patient\\'s care.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Caregiver Learning Preference */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('caregiverLearningPreference', 'Caregiver Learning Preferences')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {learningPreferenceOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-start\">\n                    <input\n                      type=\"checkbox\"\n                      id={`caregiver-pref-${option.value}`}\n                      checked={formData.caregiverLearningPreference.includes(option.value)}\n                      onChange={(e) => handleCheckboxChange('caregiverLearningPreference', option.value, e.target.checked)}\n                      className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor={`caregiver-pref-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n\n                {formData.caregiverLearningPreference.includes('other') && (\n                  <textarea\n                    value={formData.caregiverLearningOther}\n                    onChange={(e) => handleInputChange('caregiverLearningOther', e.target.value)}\n                    placeholder={t('specifyOther', 'Specify other...')}\n                    className=\"w-full mt-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    rows=\"2\"\n                  />\n                )}\n              </div>\n            </div>\n\n            {/* Caregiver Barriers */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('caregiverBarriers', 'Caregiver Barriers')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {barrierOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-start\">\n                    <input\n                      type=\"checkbox\"\n                      id={`caregiver-barrier-${option.value}`}\n                      checked={formData.caregiverBarriers.includes(option.value)}\n                      onChange={(e) => handleCheckboxChange('caregiverBarriers', option.value, e.target.checked)}\n                      disabled={formData.caregiverBarriers.includes('no_barrier') && option.value !== 'no_barrier'}\n                      className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor={`caregiver-barrier-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n\n                {formData.caregiverBarriers.includes('others') && (\n                  <textarea\n                    value={formData.caregiverBarriersOther}\n                    onChange={(e) => handleInputChange('caregiverBarriersOther', e.target.value)}\n                    placeholder={t('specifyOther', 'Specify other...')}\n                    className=\"w-full mt-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    rows=\"2\"\n                  />\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Patient Self-Care */}\n        <div className=\"bg-gradient-to-r from-teal-50 to-emerald-50 dark:from-teal-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-teal-200 dark:border-teal-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-teal-900 dark:text-teal-100 mb-6 flex items-center\">\n            <i className=\"fas fa-user-check text-teal-600 dark:text-teal-400 mr-2\"></i>\n            {t('patientSelfCare', 'Patient Self-Care Assessment')}\n          </h2>\n\n          <div className=\"bg-teal-100 dark:bg-teal-800/30 border border-teal-300 dark:border-teal-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-teal-800 dark:text-teal-200\">\n              <i className=\"fas fa-hand-holding-heart text-teal-600 dark:text-teal-400 mr-2\"></i>\n              {t('selfCareInstruction', 'Evaluate the patient\\'s ability and motivation to perform self-care activities independently.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Self-Care Capability */}\n            <div className=\"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-4\">\n              <h3 className=\"text-md font-medium text-indigo-900 dark:text-indigo-100 mb-4 flex items-center\">\n                <i className=\"fas fa-tasks text-indigo-600 dark:text-indigo-400 mr-2\"></i>\n                {t('patientSelfCareCapability', 'Patient Self-Care Capability')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {selfCareCapabilityOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-center bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg px-3 py-2\">\n                    <input\n                      type=\"radio\"\n                      id={`capability-${option.value}`}\n                      name=\"selfCareCapability\"\n                      value={option.value}\n                      checked={formData.selfCareCapability === option.value}\n                      onChange={(e) => handleInputChange('selfCareCapability', e.target.value)}\n                      className=\"mr-2 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-indigo-300\"\n                    />\n                    <label htmlFor={`capability-${option.value}`} className=\"text-sm text-indigo-800 dark:text-indigo-200 font-medium\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n\n              {formData.selfCareCapability === 'not_applicable' && (\n                <div className=\"mt-3\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('reason', 'Reason')}\n                  </label>\n                  <textarea\n                    value={formData.selfCareCapabilityReason}\n                    onChange={(e) => handleInputChange('selfCareCapabilityReason', e.target.value)}\n                    placeholder={t('specifyReason', 'Specify reason...')}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    rows=\"2\"\n                  />\n                </div>\n              )}\n\n              {errors.selfCareCapability && (\n                <p className=\"text-red-500 text-sm mt-2\">{errors.selfCareCapability}</p>\n              )}\n            </div>\n\n            {/* Self-Care Motivation */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('patientSelfCareMotivation', 'Patient Self-Care Motivation')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {selfCareMotivationOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      id={`motivation-${option.value}`}\n                      name=\"selfCareMotivation\"\n                      value={option.value}\n                      checked={formData.selfCareMotivation === option.value}\n                      onChange={(e) => handleInputChange('selfCareMotivation', e.target.value)}\n                      className=\"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                    />\n                    <label htmlFor={`motivation-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n\n              {formData.selfCareMotivation === 'not_applicable' && (\n                <div className=\"mt-3\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('reason', 'Reason')}\n                  </label>\n                  <textarea\n                    value={formData.selfCareMotivationReason}\n                    onChange={(e) => handleInputChange('selfCareMotivationReason', e.target.value)}\n                    placeholder={t('specifyReason', 'Specify reason...')}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    rows=\"2\"\n                  />\n                </div>\n              )}\n\n              {errors.selfCareMotivation && (\n                <p className=\"text-red-500 text-sm mt-2\">{errors.selfCareMotivation}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Comments */}\n          <div className=\"mt-6\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('comments', 'Comments')}\n            </label>\n            <textarea\n              value={formData.comments}\n              onChange={(e) => handleInputChange('comments', e.target.value)}\n              placeholder={t('additionalComments', 'Additional comments...')}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              rows=\"3\"\n            />\n          </div>\n        </div>\n\n        {/* Educational Needs */}\n        <div className=\"bg-gradient-to-r from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 rounded-lg shadow-lg border border-pink-200 dark:border-pink-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-pink-900 dark:text-pink-100 mb-6 flex items-center\">\n            <i className=\"fas fa-book-open text-pink-600 dark:text-pink-400 mr-2\"></i>\n            {t('educationalNeeds', 'Educational Needs')}\n          </h2>\n\n          <div className=\"bg-pink-100 dark:bg-pink-800/30 border border-pink-300 dark:border-pink-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-pink-800 dark:text-pink-200\">\n              <i className=\"fas fa-graduation-cap text-pink-600 dark:text-pink-400 mr-2\"></i>\n              {t('educationalNeedsInstruction', 'Select all educational topics that the patient and family need to learn about for optimal care and recovery.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n            {educationalNeedsOptions.map((option) => (\n              <div key={option.value} className=\"flex items-start bg-white dark:bg-pink-800/20 border border-pink-200 dark:border-pink-600 rounded-lg px-3 py-2\">\n                <input\n                  type=\"checkbox\"\n                  id={`edu-need-${option.value}`}\n                  checked={formData.educationalNeeds.includes(option.value)}\n                  onChange={(e) => handleCheckboxChange('educationalNeeds', option.value, e.target.checked)}\n                  className=\"mt-1 mr-2 h-4 w-4 text-pink-600 focus:ring-pink-500 border-pink-300 rounded\"\n                />\n                <label htmlFor={`edu-need-${option.value}`} className=\"text-sm text-pink-800 dark:text-pink-200 font-medium\">\n                  {option.label}\n                </label>\n              </div>\n            ))}\n          </div>\n\n          {formData.educationalNeeds.includes('others') && (\n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('otherEducationalNeeds', 'Other Educational Needs')}\n              </label>\n              <textarea\n                value={formData.educationalNeedsOther}\n                onChange={(e) => handleInputChange('educationalNeedsOther', e.target.value)}\n                placeholder={t('specifyOther', 'Specify other...')}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                rows=\"2\"\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Given Education */}\n        <div className=\"bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-violet-200 dark:border-violet-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-violet-900 dark:text-violet-100 mb-6 flex items-center\">\n            <i className=\"fas fa-chalkboard-teacher text-violet-600 dark:text-violet-400 mr-2\"></i>\n            {t('givenEducation', 'Given Education')}\n          </h2>\n\n          <div className=\"bg-violet-100 dark:bg-violet-800/30 border border-violet-300 dark:border-violet-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-violet-800 dark:text-violet-200\">\n              <i className=\"fas fa-clipboard-list text-violet-600 dark:text-violet-400 mr-2\"></i>\n              {t('givenEducationInstruction', 'Document the teaching methods and tools used to provide education to the patient and family.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Teaching Method */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('teachingMethod', 'Teaching Method')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {teachingMethodOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-start\">\n                    <input\n                      type=\"checkbox\"\n                      id={`method-${option.value}`}\n                      checked={formData.teachingMethod.includes(option.value)}\n                      onChange={(e) => handleCheckboxChange('teachingMethod', option.value, e.target.checked)}\n                      className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor={`method-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Teaching Tool */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                {t('teachingTool', 'Teaching Tool')}\n              </h3>\n\n              <div className=\"space-y-2\">\n                {teachingToolOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-start\">\n                    <input\n                      type=\"checkbox\"\n                      id={`tool-${option.value}`}\n                      checked={formData.teachingTool.includes(option.value)}\n                      onChange={(e) => handleCheckboxChange('teachingTool', option.value, e.target.checked)}\n                      className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                    />\n                    <label htmlFor={`tool-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Evaluation */}\n        <div className=\"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-emerald-900 dark:text-emerald-100 mb-6 flex items-center\">\n            <i className=\"fas fa-chart-line text-emerald-600 dark:text-emerald-400 mr-2\"></i>\n            {t('evaluation', 'Evaluation')}\n          </h2>\n\n          <div className=\"bg-emerald-100 dark:bg-emerald-800/30 border border-emerald-300 dark:border-emerald-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-emerald-800 dark:text-emerald-200\">\n              <i className=\"fas fa-check-circle text-emerald-600 dark:text-emerald-400 mr-2\"></i>\n              {t('evaluationInstruction', 'Assess the patient\\'s understanding and ability to apply the education provided.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n            {evaluationOptions.map((option) => (\n              <div key={option.value} className=\"flex items-start\">\n                <input\n                  type=\"checkbox\"\n                  id={`eval-${option.value}`}\n                  checked={formData.evaluation.includes(option.value)}\n                  onChange={(e) => handleCheckboxChange('evaluation', option.value, e.target.checked)}\n                  className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor={`eval-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                  {option.label}\n                </label>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Additional Questions */}\n        <div className=\"bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-lg shadow-lg border border-amber-200 dark:border-amber-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-amber-900 dark:text-amber-100 mb-6 flex items-center\">\n            <i className=\"fas fa-question-circle text-amber-600 dark:text-amber-400 mr-2\"></i>\n            {t('additionalQuestions', 'Additional Questions')}\n          </h2>\n\n          <div className=\"bg-amber-100 dark:bg-amber-800/30 border border-amber-300 dark:border-amber-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-amber-800 dark:text-amber-200\">\n              <i className=\"fas fa-info-circle text-amber-600 dark:text-amber-400 mr-2\"></i>\n              {t('additionalQuestionsInstruction', 'Complete these additional assessments to better understand the patient\\'s circumstances and needs.')}\n            </p>\n          </div>\n\n          <div className=\"space-y-6\">\n            {/* Patient Attend */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n                {t('patientAttend', 'Patient Attend')}\n              </h3>\n\n              <div className=\"flex flex-wrap gap-4\">\n                {patientAttendOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      id={`attend-${option.value}`}\n                      name=\"patientAttend\"\n                      value={option.value}\n                      checked={formData.patientAttend === option.value}\n                      onChange={(e) => handleInputChange('patientAttend', e.target.value)}\n                      className=\"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                    />\n                    <label htmlFor={`attend-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n              {errors.patientAttend && (\n                <p className=\"text-red-500 text-sm mt-2\">{errors.patientAttend}</p>\n              )}\n            </div>\n\n            {/* Problems Attending */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n                {t('problemsAttendingSchoolWork', 'Does the Patient Have Any Problems Attending School / Work?')}\n              </h3>\n\n              <div className=\"flex flex-wrap gap-4\">\n                {yesNoNotApplicableOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      id={`problems-${option.value}`}\n                      name=\"problemsAttending\"\n                      value={option.value}\n                      checked={formData.problemsAttending === option.value}\n                      onChange={(e) => handleInputChange('problemsAttending', e.target.value)}\n                      className=\"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                    />\n                    <label htmlFor={`problems-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n              {errors.problemsAttending && (\n                <p className=\"text-red-500 text-sm mt-2\">{errors.problemsAttending}</p>\n              )}\n            </div>\n\n            {/* Literacy Skills */}\n            <div>\n              <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n                {t('literacySkills', 'Literacy Skills')}\n              </h3>\n\n              <div className=\"flex flex-wrap gap-4\">\n                {yesNoOptions.map((option) => (\n                  <div key={option.value} className=\"flex items-center\">\n                    <input\n                      type=\"radio\"\n                      id={`literacy-${option.value}`}\n                      name=\"literacySkills\"\n                      value={option.value}\n                      checked={formData.literacySkills === option.value}\n                      onChange={(e) => handleInputChange('literacySkills', e.target.value)}\n                      className=\"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                    />\n                    <label htmlFor={`literacy-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {option.label}\n                    </label>\n                  </div>\n                ))}\n              </div>\n              {errors.literacySkills && (\n                <p className=\"text-red-500 text-sm mt-2\">{errors.literacySkills}</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Post-Discharge Care */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {t('whoWillProvideCareAfterDischarge', 'Who Will Provide Care After Discharge?')}\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n            {careProviderOptions.map((option) => (\n              <div key={option.value} className=\"flex items-start\">\n                <input\n                  type=\"checkbox\"\n                  id={`care-provider-${option.value}`}\n                  checked={formData.careProviderAfterDischarge.includes(option.value)}\n                  onChange={(e) => handleCheckboxChange('careProviderAfterDischarge', option.value, e.target.checked)}\n                  className=\"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor={`care-provider-${option.value}`} className=\"text-sm text-gray-700 dark:text-gray-300\">\n                  {option.label}\n                </label>\n              </div>\n            ))}\n          </div>\n\n          {formData.careProviderAfterDischarge.includes('others') && (\n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('otherCareProvider', 'Other Care Provider')}\n              </label>\n              <textarea\n                value={formData.careProviderOther}\n                onChange={(e) => handleInputChange('careProviderOther', e.target.value)}\n                placeholder={t('specifyOther', 'Specify other...')}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                rows=\"2\"\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Plans and Comments */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {t('plansAndComments', 'Plans and Comments')}\n          </h2>\n\n          <textarea\n            value={formData.plansComments}\n            onChange={(e) => handleInputChange('plansComments', e.target.value)}\n            placeholder={t('plansCommentsPlaceholder', 'Enter plans and additional comments...')}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            rows=\"4\"\n          />\n        </div>\n\n        {/* Therapist Signature */}\n        <div className=\"bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/20 dark:to-gray-900/20 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-slate-900 dark:text-slate-100 mb-6 flex items-center\">\n            <i className=\"fas fa-signature text-slate-600 dark:text-slate-400 mr-2\"></i>\n            {t('therapistSignature', 'Therapist Signature')}\n          </h2>\n\n          <div className=\"bg-slate-100 dark:bg-slate-800/30 border border-slate-300 dark:border-slate-600 rounded-lg p-4 mb-6\">\n            <p className=\"text-sm text-slate-800 dark:text-slate-200\">\n              <i className=\"fas fa-pen-fancy text-slate-600 dark:text-slate-400 mr-2\"></i>\n              {t('signatureInstruction', 'Complete the therapist signature section to validate this education assessment and documentation.')}\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('therapistName', 'Therapist Name')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.therapistName}\n                onChange={(e) => handleInputChange('therapistName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.therapistName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.therapistName && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.therapistName}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('signature', 'Signature')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.signature}\n                onChange={(e) => handleInputChange('signature', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.signature ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.signature && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.signature}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('badgeNo', 'Badge No.')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.badgeNo}\n                onChange={(e) => handleInputChange('badgeNo', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.badgeNo ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.badgeNo && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.badgeNo}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('date', 'Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={formData.date}\n                onChange={(e) => handleInputChange('date', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation Buttons */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex justify-between items-center\">\n            <button\n              type=\"button\"\n              onClick={() => navigate(-1)}\n              className=\"px-6 py-3 bg-gradient-to-r from-gray-500 to-slate-500 text-white rounded-lg hover:from-gray-600 hover:to-slate-600 transition-all duration-200 shadow-lg flex items-center\"\n            >\n              <i className=\"fas fa-arrow-left mr-2\"></i>\n              {t('cancel', 'Cancel')}\n            </button>\n\n            <div className=\"flex space-x-4\">\n              <button\n                type=\"button\"\n                onClick={generatePDF}\n                disabled={loading}\n                className=\"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {loading ? t('generating', 'Generating...') : t('generatePDF', 'Generate PDF')}\n              </button>\n\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg\"\n              >\n                <i className=\"fas fa-save mr-2\"></i>\n                {loading ? t('saving', 'Saving...') : t('saveEducationForm', 'Save Education Form')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default PatientFamilyEducationForm;\n"], "names": ["PatientFamilyEducationForm", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "formData", "setFormData", "useState", "documentNumber", "concat", "Date", "now", "issueDate", "toISOString", "split", "version", "reviewNumber", "assessmentType", "patientLearningPreference", "patientLearningOther", "patientBarriers", "patientBarriersOther", "caregiverLearningPreference", "caregiverLearningOther", "caregiver<PERSON><PERSON><PERSON>s", "caregiver<PERSON><PERSON><PERSON><PERSON><PERSON>ther", "selfCareCapability", "selfCareCapabilityReason", "selfCareMotivation", "selfCareMotivationReason", "comments", "educationalNeeds", "educationalNeedsOther", "teachingMethod", "teachingTool", "evaluation", "patientAttend", "problemsAttending", "literacySkills", "careProviderAfterDischarge", "careProviderOther", "plansComments", "<PERSON><PERSON><PERSON>", "name", "signature", "badgeNo", "date", "submittedBy", "id", "submittedAt", "loading", "setLoading", "errors", "setErrors", "assessmentTypeOptions", "value", "label", "learningPreferenceOptions", "barrierOptions", "selfCareCapabilityOptions", "selfCareMotivationOptions", "educationalNeedsOptions", "teachingMethodOptions", "teachingToolOptions", "evaluationOptions", "patientAttendOptions", "yesNoNotApplicableOptions", "yesNoOptions", "careProviderOptions", "useEffect", "loadPatientData", "async", "response", "fetch", "ok", "json", "error", "console", "handleInputChange", "field", "prev", "_objectSpread", "handleCheckboxChange", "option", "checked", "newArray", "filter", "item", "includes", "_jsx", "className", "children", "_jsxs", "onSubmit", "e", "preventDefault", "validateForm", "newErrors", "trim", "Object", "keys", "length", "submissionData", "method", "headers", "body", "JSON", "stringify", "Error", "alert", "type", "onChange", "target", "readOnly", "map", "htmlFor", "placeholder", "rows", "disabled", "required", "onClick", "pdfData", "generatedAt", "generatedBy", "email", "localStorage", "getItem", "status", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}