"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[7097],{7097:(e,r,t)=>{t.r(r),t.d(r,{default:()=>n});var a=t(2555),s=t(5043),i=t(7921),d=t(4528),l=t(3768),o=t(579);const n=()=>{const{t:e,isRTL:r}=(0,i.o)(),{user:t}=(0,d.A)(),[n,c]=(0,s.useState)(!1),[x,m]=(0,s.useState)({overview:{},demographics:{},treatmentOutcomes:{},specialNeeds:{}}),[g,p]=(0,s.useState)("month"),[h,b]=(0,s.useState)("overview"),[u,y]=(0,s.useState)({dateFrom:"",dateTo:"",therapist:"",treatmentType:"",condition:""});(0,s.useEffect)(()=>{f()},[g,h]);const f=async()=>{try{c(!0);const e={overview:"/api/v1/analytics/patients",demographics:"/api/v1/analytics/demographics",outcomes:"/api/v1/analytics/outcomes",specialNeeds:"/api/v1/analytics/special-needs"},r=await fetch("".concat(e[h],"?period=").concat(g),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(r.ok){const e=await r.json();m(r=>(0,a.A)((0,a.A)({},r),{},{[h]:e.data||{}}))}else{const e={overview:{totalPatients:245,activePatients:189,newPatients:23,dischargedPatients:12,averageAge:42.5,genderDistribution:{male:45,female:55},treatmentTypes:{"Physical Therapy":120,"Occupational Therapy":65,"Speech Therapy":35,"Special Needs":25}},demographics:{ageGroups:{"0-18":45,"19-35":67,"36-50":89,"51-65":32,"65+":12},conditions:{Neurological:78,Orthopedic:92,Pediatric:45,Geriatric:30},locations:{Riyadh:145,Jeddah:67,Dammam:33}},outcomes:{improvementRate:87.5,averageTreatmentDuration:45,patientSatisfaction:94.2,goalAchievement:82.1,functionalImprovement:{Excellent:45,Good:32,Fair:18,Poor:5}},specialNeeds:{totalSpecialNeeds:67,conditions:{"Autism Spectrum":23,"Cerebral Palsy":18,"Down Syndrome":12,ADHD:8,Other:6},accommodations:{"Sensory Support":34,"Communication Aids":28,"Mobility Assistance":22,"Behavioral Support":19}}};m(r=>(0,a.A)((0,a.A)({},r),{},{[h]:e[h]||{}}))}}catch(r){console.error("Error loading report data:",r),l.Ay.error(e("errorLoadingReports","Error loading reports"))}finally{c(!1)}},j=async r=>{try{c(!0);const t=await fetch("/api/v1/analytics/export?type=patients&format=".concat(r,"&period=").concat(g),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(!t.ok)throw new Error("Export failed");{const a=await t.blob(),s=window.URL.createObjectURL(a),i=document.createElement("a");i.href=s,i.download="patient-report-".concat(g,".").concat(r),document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(s),document.body.removeChild(i),l.Ay.success(e("reportExported","Report exported successfully"))}}catch(t){console.error("Error exporting report:",t),l.Ay.error(e("errorExporting","Error exporting report"))}finally{c(!1)}},v=[{id:"overview",label:e("patientOverview","Patient Overview"),icon:"fas fa-users"},{id:"demographics",label:e("demographics","Demographics"),icon:"fas fa-chart-pie"},{id:"outcomes",label:e("treatmentOutcomes","Treatment Outcomes"),icon:"fas fa-chart-line"},{id:"specialNeeds",label:e("specialNeedsReports","Special Needs"),icon:"fas fa-heart"}];return(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,o.jsx)("div",{className:"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8",children:(0,o.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,o.jsx)("div",{className:"px-6 py-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:e("patientReports","Patient Reports")}),(0,o.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-chart-bar text-blue-500 mr-2"}),e("patientReportsDesc","Comprehensive patient analytics and reporting")]})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("button",{onClick:()=>j("pdf"),disabled:n,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-file-pdf mr-2"}),e("exportPDF","Export PDF")]}),(0,o.jsxs)("button",{onClick:()=>j("excel"),disabled:n,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-file-excel mr-2"}),e("exportExcel","Export Excel")]})]})]})})})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("reportType","Report Type")}),(0,o.jsx)("select",{value:h,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:v.map(e=>(0,o.jsx)("option",{value:e.id,children:e.label},e.id))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("timePeriod","Time Period")}),(0,o.jsxs)("select",{value:g,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,o.jsx)("option",{value:"week",children:e("thisWeek","This Week")}),(0,o.jsx)("option",{value:"month",children:e("thisMonth","This Month")}),(0,o.jsx)("option",{value:"quarter",children:e("thisQuarter","This Quarter")}),(0,o.jsx)("option",{value:"year",children:e("thisYear","This Year")})]})]}),(0,o.jsx)("div",{className:"flex items-end",children:(0,o.jsxs)("button",{onClick:f,disabled:n,className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-sync-alt mr-2"}),n?e("loading","Loading..."):e("refreshReport","Refresh Report")]})})]})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:n?(0,o.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,o.jsxs)(o.Fragment,{children:["overview"===h&&(()=>{const r=x.overview;return(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,o.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-700",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-users text-blue-600 dark:text-blue-400 text-2xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-blue-600 dark:text-blue-400",children:e("totalPatients","Total Patients")}),(0,o.jsx)("p",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:r.totalPatients||0})]})]})}),(0,o.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg p-6 border border-green-200 dark:border-green-700",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-user-check text-green-600 dark:text-green-400 text-2xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:e("activePatients","Active Patients")}),(0,o.jsx)("p",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:r.activePatients||0})]})]})}),(0,o.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg p-6 border border-purple-200 dark:border-purple-700",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-user-plus text-purple-600 dark:text-purple-400 text-2xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-purple-600 dark:text-purple-400",children:e("newPatients","New Patients")}),(0,o.jsx)("p",{className:"text-2xl font-bold text-purple-900 dark:text-purple-100",children:r.newPatients||0})]})]})}),(0,o.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg p-6 border border-orange-200 dark:border-orange-700",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-user-minus text-orange-600 dark:text-orange-400 text-2xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-orange-600 dark:text-orange-400",children:e("dischargedPatients","Discharged")}),(0,o.jsx)("p",{className:"text-2xl font-bold text-orange-900 dark:text-orange-100",children:r.dischargedPatients||0})]})]})})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("treatmentTypesDistribution","Treatment Types Distribution")}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(r.treatmentTypes||{}).map(e=>{let[r,t]=e;return(0,o.jsxs)("div",{className:"text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,o.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:t}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:r})]},r)})})]})]})})(),"demographics"===h&&(()=>{const r=x.demographics;return(0,o.jsx)("div",{className:"space-y-6",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("ageDistribution","Age Distribution")}),(0,o.jsx)("div",{className:"space-y-3",children:Object.entries(r.ageGroups||{}).map(r=>{let[t,a]=r;return(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:[t," ",e("years","years")]}),(0,o.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:a})]},t)})})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("conditionTypes","Condition Types")}),(0,o.jsx)("div",{className:"space-y-3",children:Object.entries(r.conditions||{}).map(e=>{let[r,t]=e;return(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:r}),(0,o.jsx)("span",{className:"font-semibold text-gray-900 dark:text-white",children:t})]},r)})})]})]})})})(),"outcomes"===h&&(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("i",{className:"fas fa-chart-line text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("treatmentOutcomes","Treatment Outcomes")}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("outcomesReportContent","Treatment outcomes report content will be displayed here")})]}),"specialNeeds"===h&&(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("i",{className:"fas fa-heart text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("specialNeedsReports","Special Needs Reports")}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("specialNeedsReportContent","Special needs report content will be displayed here")})]})]})})]})}}}]);
//# sourceMappingURL=7097.17f2f6cd.chunk.js.map