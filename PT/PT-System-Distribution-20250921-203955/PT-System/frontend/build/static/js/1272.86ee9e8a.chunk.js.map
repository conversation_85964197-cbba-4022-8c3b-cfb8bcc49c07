{"version": 3, "file": "static/js/1272.86ee9e8a.chunk.js", "mappings": "mMAGA,MA+aA,EA/aaA,KACX,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAeC,IAAoBC,EAAAA,EAAAA,UAAS,oBAC5CC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAAeC,IAAoBJ,EAAAA,EAAAA,UAAS,CAAC,GAE9CK,EAAe,CACnB,kBAAmB,CACjBC,MAAOX,EAAE,iBAAkB,mBAC3BY,KAAM,qBACNC,MAAO,CACL,CACEC,GAAI,WACJH,MAAOX,EAAE,iBAAkB,mBAC3Be,QAASf,EAAE,wBAAyB,uDACpCgB,MAAO,CACLhB,EAAE,gBAAiB,uCACnBA,EAAE,gBAAiB,iDACnBA,EAAE,gBAAiB,8CAGvB,CACEc,GAAI,cACJH,MAAOX,EAAE,aAAc,eACvBe,QAASf,EAAE,oBAAqB,yCAChCgB,MAAO,CACLhB,EAAE,aAAc,4CAChBA,EAAE,aAAc,qCAChBA,EAAE,aAAc,6CAGpB,CACEc,GAAI,aACJH,MAAOX,EAAE,kBAAmB,oBAC5Be,QAASf,EAAE,oBAAqB,4CAChCgB,MAAO,CACLhB,EAAE,WAAY,oDACdA,EAAE,WAAY,4DACdA,EAAE,WAAY,qDAKtB,SAAY,CACVW,MAAOX,EAAE,oBAAqB,sBAC9BY,KAAM,eACNC,MAAO,CACL,CACEC,GAAI,cACJH,MAAOX,EAAE,gBAAiB,uBAC1Be,QAASf,EAAE,oBAAqB,0CAChCgB,MAAO,CACLhB,EAAE,kBAAmB,kCACrBA,EAAE,kBAAmB,mDACrBA,EAAE,kBAAmB,kDACrBA,EAAE,kBAAmB,yCAGzB,CACEc,GAAI,kBACJH,MAAOX,EAAE,kBAAmB,4BAC5Be,QAASf,EAAE,iBAAkB,8CAC7BgB,MAAO,CACLhB,EAAE,eAAgB,2CAClBA,EAAE,eAAgB,8CAClBA,EAAE,eAAgB,4CAClBA,EAAE,eAAgB,4CAGtB,CACEc,GAAI,gBACJH,MAAOX,EAAE,uBAAwB,0BACjCe,QAASf,EAAE,sBAAuB,uCAClCgB,MAAO,CACLhB,EAAE,oBAAqB,gDACvBA,EAAE,oBAAqB,2CACvBA,EAAE,oBAAqB,0CACvBA,EAAE,oBAAqB,oDAK/B,aAAgB,CACdW,MAAOX,EAAE,wBAAyB,0BAClCY,KAAM,kBACNC,MAAO,CACL,CACEC,GAAI,uBACJH,MAAOX,EAAE,yBAA0B,2BACnCe,QAASf,EAAE,kBAAmB,2CAC9BgB,MAAO,CACLhB,EAAE,gBAAiB,6BACnBA,EAAE,gBAAiB,kCACnBA,EAAE,gBAAiB,6CACnBA,EAAE,gBAAiB,uCAGvB,CACEc,GAAI,kBACJH,MAAOX,EAAE,qBAAsB,uBAC/Be,QAASf,EAAE,kBAAmB,kCAC9BgB,MAAO,CACLhB,EAAE,gBAAiB,2CACnBA,EAAE,gBAAiB,oDACnBA,EAAE,gBAAiB,4CACnBA,EAAE,gBAAiB,0DAK3B,WAAc,CACZW,MAAOX,EAAE,oBAAqB,sBAC9BY,KAAM,mBACNC,MAAO,CACL,CACEC,GAAI,cACJH,MAAOX,EAAE,yBAA0B,4BACnCe,QAASf,EAAE,cAAe,2CAC1BgB,MAAO,CACLhB,EAAE,YAAa,sCACfA,EAAE,YAAa,0CACfA,EAAE,YAAa,+CACfA,EAAE,YAAa,uCAGnB,CACEc,GAAI,iBACJH,MAAOX,EAAE,mBAAoB,qBAC7Be,QAASf,EAAE,kBAAmB,2CAC9BgB,MAAO,CACLhB,EAAE,gBAAiB,oCACnBA,EAAE,gBAAiB,sCACnBA,EAAE,gBAAiB,2CACnBA,EAAE,gBAAiB,2DAK3B,QAAW,CACTW,MAAOX,EAAE,mBAAoB,uBAC7BY,KAAM,mBACNC,MAAO,CACL,CACEC,GAAI,mBACJH,MAAOX,EAAE,oBAAqB,sBAC9Be,QAASf,EAAE,iBAAkB,uCAC7BgB,MAAO,CACLhB,EAAE,eAAgB,8BAClBA,EAAE,eAAgB,kDAClBA,EAAE,eAAgB,uCAClBA,EAAE,eAAgB,kDAGtB,CACEc,GAAI,YACJH,MAAOX,EAAE,yBAA0B,2BACnCe,QAASf,EAAE,mBAAoB,6CAC/BgB,MAAO,CACLhB,EAAE,iBAAkB,kDACpBA,EAAE,iBAAkB,kCACpBA,EAAE,iBAAkB,yCACpBA,EAAE,iBAAkB,mDAK5B,gBAAmB,CACjBW,MAAOX,EAAE,kBAAmB,mBAC5BY,KAAM,eACNC,MAAO,CACL,CACEC,GAAI,gBACJH,MAAOX,EAAE,eAAgB,iBACzBe,QAASf,EAAE,gBAAiB,gDAC5BgB,MAAO,CACLhB,EAAE,cAAe,uDACjBA,EAAE,cAAe,mCACjBA,EAAE,cAAe,6CACjBA,EAAE,cAAe,oDAGrB,CACEc,GAAI,cACJH,MAAOX,EAAE,0BAA2B,4BACpCe,QAASf,EAAE,qBAAsB,sCACjCgB,MAAO,CACLhB,EAAE,mBAAoB,iDACtBA,EAAE,mBAAoB,mDACtBA,EAAE,mBAAoB,qCACtBA,EAAE,mBAAoB,iDAO1BiB,EAAW,CACf,CACEH,GAAI,OACJI,SAAUlB,EAAE,eAAgB,+BAC5BmB,OAAQnB,EAAE,aAAc,sFAE1B,CACEc,GAAI,OACJI,SAAUlB,EAAE,eAAgB,8CAC5BmB,OAAQnB,EAAE,aAAc,8EAE1B,CACEc,GAAI,OACJI,SAAUlB,EAAE,eAAgB,iCAC5BmB,OAAQnB,EAAE,aAAc,mFAE1B,CACEc,GAAI,OACJI,SAAUlB,EAAE,eAAgB,sCAC5BmB,OAAQnB,EAAE,aAAc,mFAE1B,CACEc,GAAI,OACJI,SAAUlB,EAAE,eAAgB,iDAC5BmB,OAAQnB,EAAE,aAAc,6FAItBoB,EAAkBC,IACtBZ,EAAiBa,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBD,GAAI,IACP,CAACD,IAAUC,EAAKD,OAIKG,OAAOC,QAAQf,GAAcgB,OAAOC,IAAqB,IAAnBC,EAAKC,GAAQF,EAC1E,IAAKrB,EAAY,OAAO,EACxB,MAAMwB,EAAcxB,EAAWyB,cAC/B,OAAOF,EAAQlB,MAAMoB,cAAcC,SAASF,IACrCD,EAAQhB,MAAMoB,KAAKC,GACjBA,EAAKvB,MAAMoB,cAAcC,SAASF,IAClCI,EAAKnB,QAAQgB,cAAcC,SAASF,MAI/C,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAC,OAASpC,EAAQ,cAAgB,gBAAiBqC,SAAA,EAE9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DtC,EAAE,OAAQ,2BAEbuC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CtC,EAAE,WAAY,qEAGnBmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CH,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oGAAmGE,SAAA,EACnHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sBACZpC,EAAE,iBAAkB,uBAEvBmC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,sGAAqGE,SAAA,EACrHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wBACZpC,EAAE,iBAAkB,6BAM3BuC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,OAAME,UACnBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOnC,EACPoC,SAAWC,GAAMpC,EAAcoC,EAAEC,OAAOH,OACxCI,YAAa7C,EAAE,aAAc,2BAC7BoC,UAAU,2IAEZG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,4DAIjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EAEpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,UAC5BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFE,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,UAChEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8CAA6CE,SAAEtC,EAAE,aAAc,oBAE/EmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeE,SAAA,CAC3Bd,OAAOC,QAAQf,GAAcoC,IAAIC,IAAA,IAAEnB,EAAKC,GAAQkB,EAAA,OAC/CZ,EAAAA,EAAAA,MAAA,UAEEa,QAASA,IAAM5C,EAAiBwB,GAChCQ,UAAS,uFAAAC,OACPlC,IAAkByB,EACd,mEACA,6EACHU,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAWP,EAAQjB,QACtB2B,EAAAA,EAAAA,KAAA,QAAAD,SAAOT,EAAQlB,UATViB,MAYTO,EAAAA,EAAAA,MAAA,UACEa,QAASA,IAAM5C,EAAiB,OAChCgC,UAAS,uFAAAC,OACW,QAAlBlC,EACI,mEACA,6EACHmC,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,4BACbG,EAAAA,EAAAA,KAAA,QAAAD,SAAOtC,EAAE,MAAO,qBAOxBuC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,UAC5BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0FAAyFE,SACnF,QAAlBnC,GACCgC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,uDAAsDE,SACjEtC,EAAE,2BAA4B,iCAEjCuC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBrB,EAAS6B,IAAKG,IACbd,EAAAA,EAAAA,MAAA,OAAkBC,UAAU,yDAAwDE,SAAA,EAClFH,EAAAA,EAAAA,MAAA,UACEa,QAASA,IAAM5B,EAAe6B,EAAInC,IAClCsB,UAAU,mHAAkHE,SAAA,EAE5HC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,4CAA2CE,SAAEW,EAAI/B,YACjEqB,EAAAA,EAAAA,KAAA,KAAGH,UAAS,kBAAAC,OAAoB7B,EAAcyC,EAAInC,IAAM,KAAO,OAAM,uBAEtEN,EAAcyC,EAAInC,MACjByB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,6CAA4CE,SACxDW,EAAI9B,WAVD8B,EAAInC,UAkBpBJ,EAAaP,KACXgC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAK3B,EAAaP,GAAeS,KAAI,sDACjD2B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kDAAiDE,SAC5D5B,EAAaP,GAAeQ,YAIjC4B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvB5B,EAAaP,GAAeU,MAAMiC,IAAKZ,IACtCC,EAAAA,EAAAA,MAAA,OAAmBC,UAAU,yDAAwDE,SAAA,EACnFH,EAAAA,EAAAA,MAAA,UACEa,QAASA,IAAM5B,EAAec,EAAKpB,IACnCsB,UAAU,mHAAkHE,SAAA,EAE5HH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8CAA6CE,SAAEJ,EAAKvB,SAClE4B,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gDAA+CE,SAAEJ,EAAKnB,cAErEwB,EAAAA,EAAAA,KAAA,KAAGH,UAAS,kBAAAC,OAAoB7B,EAAc0B,EAAKpB,IAAM,KAAO,OAAM,uBAGvEN,EAAc0B,EAAKpB,MAClBqB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,iDAAgDE,SAAA,CAAEtC,EAAE,QAAS,SAAS,QACpFuC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,YAAWE,SACtBJ,EAAKlB,MAAM8B,IAAI,CAACI,EAAMC,KACrBhB,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,mBAAkBE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,2HAA0HE,SACvIa,EAAQ,KAEXZ,EAAAA,EAAAA,KAAA,QAAMH,UAAU,mCAAkCE,SAAEY,MAJ7CC,WAjBTjB,EAAKpB,kBAsC/BqB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,EACzDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uEAAsEE,SAAA,EACnFH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,yCACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,wBAAuBE,SAAEtC,EAAE,aAAc,2BAEzDuC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBAAoBE,SAAEtC,EAAE,iBAAkB,oCACvDuC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,0FAAyFE,SACxGtC,EAAE,aAAc,qBAIrBmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yEAAwEE,SAAA,EACrFH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+BACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,wBAAuBE,SAAEtC,EAAE,aAAc,qBAEzDuC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sBAAqBE,SAAEtC,EAAE,iBAAkB,6BACxDuC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,4FAA2FE,SAC1GtC,EAAE,cAAe,sBAItBmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2EAA0EE,SAAA,EACvFH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,wBAAuBE,SAAEtC,EAAE,YAAa,yBAExDuC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBAAsBE,SAAEtC,EAAE,gBAAiB,+BACxDuC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,8FAA6FE,SAC5GtC,EAAE,YAAa,yB", "sources": ["pages/Help/Help.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst Help = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeSection, setActiveSection] = useState('getting-started');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [expandedItems, setExpandedItems] = useState({});\n\n  const helpSections = {\n    'getting-started': {\n      title: t('gettingStarted', 'Getting Started'),\n      icon: 'fas fa-play-circle',\n      items: [\n        {\n          id: 'overview',\n          title: t('systemOverview', 'System Overview'),\n          content: t('systemOverviewContent', 'Learn about the PT System features and capabilities'),\n          steps: [\n            t('step1Overview', 'Navigate through the main dashboard'),\n            t('step2Overview', 'Explore different modules and their functions'),\n            t('step3Overview', 'Customize your workspace and preferences')\n          ]\n        },\n        {\n          id: 'first-login',\n          title: t('firstLogin', 'First Login'),\n          content: t('firstLoginContent', 'How to log in and set up your account'),\n          steps: [\n            t('step1Login', 'Enter your credentials on the login page'),\n            t('step2Login', 'Complete your profile information'),\n            t('step3Login', 'Set your language and theme preferences')\n          ]\n        },\n        {\n          id: 'navigation',\n          title: t('navigationGuide', 'Navigation Guide'),\n          content: t('navigationContent', 'Learn how to navigate through the system'),\n          steps: [\n            t('step1Nav', 'Use the sidebar menu to access different modules'),\n            t('step2Nav', 'Use the search bar for quick access to patients and data'),\n            t('step3Nav', 'Access settings and help from the header menu')\n          ]\n        }\n      ]\n    },\n    'patients': {\n      title: t('patientManagement', 'Patient Management'),\n      icon: 'fas fa-users',\n      items: [\n        {\n          id: 'add-patient',\n          title: t('addNewPatient', 'Adding New Patients'),\n          content: t('addPatientContent', 'Step-by-step guide to add new patients'),\n          steps: [\n            t('step1AddPatient', 'Click \"Add New Patient\" button'),\n            t('step2AddPatient', 'Fill in patient information and medical history'),\n            t('step3AddPatient', 'Set up initial treatment goals and preferences'),\n            t('step4AddPatient', 'Save and schedule first appointment')\n          ]\n        },\n        {\n          id: 'patient-records',\n          title: t('managingRecords', 'Managing Patient Records'),\n          content: t('recordsContent', 'How to update and maintain patient records'),\n          steps: [\n            t('step1Records', 'Search for patient using the search bar'),\n            t('step2Records', 'Click on patient name to view full profile'),\n            t('step3Records', 'Update information using the edit button'),\n            t('step4Records', 'Add notes and track progress over time')\n          ]\n        },\n        {\n          id: 'special-needs',\n          title: t('specialNeedsPatients', 'Special Needs Patients'),\n          content: t('specialNeedsContent', 'Working with special needs patients'),\n          steps: [\n            t('step1SpecialNeeds', 'Access the Special Needs module from sidebar'),\n            t('step2SpecialNeeds', 'Complete comprehensive assessment forms'),\n            t('step3SpecialNeeds', 'Set up sensory environment preferences'),\n            t('step4SpecialNeeds', 'Use communication tools and visual schedules')\n          ]\n        }\n      ]\n    },\n    'appointments': {\n      title: t('appointmentScheduling', 'Appointment Scheduling'),\n      icon: 'fas fa-calendar',\n      items: [\n        {\n          id: 'schedule-appointment',\n          title: t('schedulingAppointments', 'Scheduling Appointments'),\n          content: t('scheduleContent', 'How to schedule and manage appointments'),\n          steps: [\n            t('step1Schedule', 'Go to Appointments module'),\n            t('step2Schedule', 'Click \"New Appointment\" button'),\n            t('step3Schedule', 'Select patient, date, time, and therapist'),\n            t('step4Schedule', 'Add appointment notes and confirm')\n          ]\n        },\n        {\n          id: 'manage-calendar',\n          title: t('calendarManagement', 'Calendar Management'),\n          content: t('calendarContent', 'Managing your therapy calendar'),\n          steps: [\n            t('step1Calendar', 'Switch between different calendar views'),\n            t('step2Calendar', 'Use filters to view specific therapists or types'),\n            t('step3Calendar', 'Drag and drop to reschedule appointments'),\n            t('step4Calendar', 'Set up recurring appointments for regular patients')\n          ]\n        }\n      ]\n    },\n    'treatments': {\n      title: t('treatmentPlanning', 'Treatment Planning'),\n      icon: 'fas fa-heartbeat',\n      items: [\n        {\n          id: 'create-plan',\n          title: t('creatingTreatmentPlans', 'Creating Treatment Plans'),\n          content: t('planContent', 'How to create effective treatment plans'),\n          steps: [\n            t('step1Plan', 'Assess patient condition and needs'),\n            t('step2Plan', 'Set SMART goals for treatment outcomes'),\n            t('step3Plan', 'Select appropriate exercises and activities'),\n            t('step4Plan', 'Schedule regular progress reviews')\n          ]\n        },\n        {\n          id: 'track-progress',\n          title: t('trackingProgress', 'Tracking Progress'),\n          content: t('progressContent', 'Monitoring patient progress effectively'),\n          steps: [\n            t('step1Progress', 'Use progress visualization tools'),\n            t('step2Progress', 'Update goal completion percentages'),\n            t('step3Progress', 'Document session notes and observations'),\n            t('step4Progress', 'Generate progress reports for patients and families')\n          ]\n        }\n      ]\n    },\n    'reports': {\n      title: t('reportsAnalytics', 'Reports & Analytics'),\n      icon: 'fas fa-chart-bar',\n      items: [\n        {\n          id: 'generate-reports',\n          title: t('generatingReports', 'Generating Reports'),\n          content: t('reportsContent', 'How to create and customize reports'),\n          steps: [\n            t('step1Reports', 'Navigate to Reports module'),\n            t('step2Reports', 'Select report template or create custom report'),\n            t('step3Reports', 'Set date ranges and filter criteria'),\n            t('step4Reports', 'Generate and export report in desired format')\n          ]\n        },\n        {\n          id: 'analytics',\n          title: t('understandingAnalytics', 'Understanding Analytics'),\n          content: t('analyticsContent', 'Interpreting system analytics and metrics'),\n          steps: [\n            t('step1Analytics', 'Review key performance indicators on dashboard'),\n            t('step2Analytics', 'Analyze patient outcome trends'),\n            t('step3Analytics', 'Compare therapist performance metrics'),\n            t('step4Analytics', 'Use insights to improve practice efficiency')\n          ]\n        }\n      ]\n    },\n    'troubleshooting': {\n      title: t('troubleshooting', 'Troubleshooting'),\n      icon: 'fas fa-tools',\n      items: [\n        {\n          id: 'common-issues',\n          title: t('commonIssues', 'Common Issues'),\n          content: t('issuesContent', 'Solutions to frequently encountered problems'),\n          steps: [\n            t('step1Issues', 'Check internet connection and browser compatibility'),\n            t('step2Issues', 'Clear browser cache and cookies'),\n            t('step3Issues', 'Verify user permissions and access rights'),\n            t('step4Issues', 'Contact system administrator if issues persist')\n          ]\n        },\n        {\n          id: 'performance',\n          title: t('performanceOptimization', 'Performance Optimization'),\n          content: t('performanceContent', 'Tips to improve system performance'),\n          steps: [\n            t('step1Performance', 'Use modern browsers (Chrome, Firefox, Safari)'),\n            t('step2Performance', 'Close unnecessary browser tabs and applications'),\n            t('step3Performance', 'Ensure stable internet connection'),\n            t('step4Performance', 'Regularly update browser and clear cache')\n          ]\n        }\n      ]\n    }\n  };\n\n  const faqItems = [\n    {\n      id: 'faq1',\n      question: t('faq1Question', 'How do I reset my password?'),\n      answer: t('faq1Answer', 'Go to Settings > Account > Change Password, or contact your system administrator.')\n    },\n    {\n      id: 'faq2',\n      question: t('faq2Question', 'Can I access the system on mobile devices?'),\n      answer: t('faq2Answer', 'Yes, the system is fully responsive and works on tablets and smartphones.')\n    },\n    {\n      id: 'faq3',\n      question: t('faq3Question', 'How do I export patient data?'),\n      answer: t('faq3Answer', 'Use the Reports module to generate and export patient data in various formats.')\n    },\n    {\n      id: 'faq4',\n      question: t('faq4Question', 'Is the system available in Arabic?'),\n      answer: t('faq4Answer', 'Yes, the system supports both Arabic and English with full RTL layout support.')\n    },\n    {\n      id: 'faq5',\n      question: t('faq5Question', 'How do I set up special needs accommodations?'),\n      answer: t('faq5Answer', 'Use the Special Needs module to configure sensory environments and communication tools.')\n    }\n  ];\n\n  const toggleExpanded = (itemId) => {\n    setExpandedItems(prev => ({\n      ...prev,\n      [itemId]: !prev[itemId]\n    }));\n  };\n\n  const filteredSections = Object.entries(helpSections).filter(([key, section]) => {\n    if (!searchTerm) return true;\n    const searchLower = searchTerm.toLowerCase();\n    return section.title.toLowerCase().includes(searchLower) ||\n           section.items.some(item =>\n             item.title.toLowerCase().includes(searchLower) ||\n             item.content.toLowerCase().includes(searchLower)\n           );\n  });\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('help', 'Help & Documentation')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('helpDesc', 'Find answers and learn how to use the PT System effectively')}\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center\">\n            <i className=\"fas fa-video mr-2\"></i>\n            {t('videoTutorials', 'Video Tutorials')}\n          </button>\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center\">\n            <i className=\"fas fa-headset mr-2\"></i>\n            {t('contactSupport', 'Contact Support')}\n          </button>\n        </div>\n      </div>\n\n      {/* Search */}\n      <div className=\"mb-6\">\n        <div className=\"relative max-w-md\">\n          <input\n            type=\"text\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            placeholder={t('searchHelp', 'Search help articles...')}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          />\n          <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Sidebar Navigation */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <div className=\"p-4 border-b border-gray-200 dark:border-gray-600\">\n              <h3 className=\"font-semibold text-gray-900 dark:text-white\">{t('helpTopics', 'Help Topics')}</h3>\n            </div>\n            <nav className=\"p-4 space-y-2\">\n              {Object.entries(helpSections).map(([key, section]) => (\n                <button\n                  key={key}\n                  onClick={() => setActiveSection(key)}\n                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                    activeSection === key\n                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <i className={section.icon}></i>\n                  <span>{section.title}</span>\n                </button>\n              ))}\n              <button\n                onClick={() => setActiveSection('faq')}\n                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                  activeSection === 'faq'\n                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'\n                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                <i className=\"fas fa-question-circle\"></i>\n                <span>{t('faq', 'FAQ')}</span>\n              </button>\n            </nav>\n          </div>\n        </div>\n\n        {/* Content Area */}\n        <div className=\"lg:col-span-3\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            {activeSection === 'faq' ? (\n              <div className=\"p-6\">\n                <h2 className=\"text-xl font-bold text-gray-900 dark:text-white mb-6\">\n                  {t('frequentlyAskedQuestions', 'Frequently Asked Questions')}\n                </h2>\n                <div className=\"space-y-4\">\n                  {faqItems.map((faq) => (\n                    <div key={faq.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg\">\n                      <button\n                        onClick={() => toggleExpanded(faq.id)}\n                        className=\"w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                      >\n                        <span className=\"font-medium text-gray-900 dark:text-white\">{faq.question}</span>\n                        <i className={`fas fa-chevron-${expandedItems[faq.id] ? 'up' : 'down'} text-gray-400`}></i>\n                      </button>\n                      {expandedItems[faq.id] && (\n                        <div className=\"px-4 pb-4 text-gray-600 dark:text-gray-400\">\n                          {faq.answer}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            ) : (\n              helpSections[activeSection] && (\n                <div className=\"p-6\">\n                  <div className=\"flex items-center mb-6\">\n                    <i className={`${helpSections[activeSection].icon} text-2xl text-blue-600 dark:text-blue-400 mr-3`}></i>\n                    <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                      {helpSections[activeSection].title}\n                    </h2>\n                  </div>\n\n                  <div className=\"space-y-6\">\n                    {helpSections[activeSection].items.map((item) => (\n                      <div key={item.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg\">\n                        <button\n                          onClick={() => toggleExpanded(item.id)}\n                          className=\"w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                        >\n                          <div>\n                            <h3 className=\"font-semibold text-gray-900 dark:text-white\">{item.title}</h3>\n                            <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">{item.content}</p>\n                          </div>\n                          <i className={`fas fa-chevron-${expandedItems[item.id] ? 'up' : 'down'} text-gray-400`}></i>\n                        </button>\n\n                        {expandedItems[item.id] && (\n                          <div className=\"px-4 pb-4\">\n                            <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">{t('steps', 'Steps')}:</h4>\n                            <ol className=\"space-y-2\">\n                              {item.steps.map((step, index) => (\n                                <li key={index} className=\"flex items-start\">\n                                  <span className=\"flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5\">\n                                    {index + 1}\n                                  </span>\n                                  <span className=\"text-gray-700 dark:text-gray-300\">{step}</span>\n                                </li>\n                              ))}\n                            </ol>\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Links */}\n      <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white\">\n          <div className=\"flex items-center mb-4\">\n            <i className=\"fas fa-graduation-cap text-2xl mr-3\"></i>\n            <h3 className=\"text-lg font-semibold\">{t('quickStart', 'Quick Start Guide')}</h3>\n          </div>\n          <p className=\"text-blue-100 mb-4\">{t('quickStartDesc', 'Get up and running in minutes')}</p>\n          <button className=\"bg-white text-blue-600 px-4 py-2 rounded font-medium hover:bg-blue-50 transition-colors\">\n            {t('startGuide', 'Start Guide')}\n          </button>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white\">\n          <div className=\"flex items-center mb-4\">\n            <i className=\"fas fa-book text-2xl mr-3\"></i>\n            <h3 className=\"text-lg font-semibold\">{t('userManual', 'User Manual')}</h3>\n          </div>\n          <p className=\"text-green-100 mb-4\">{t('userManualDesc', 'Complete documentation')}</p>\n          <button className=\"bg-white text-green-600 px-4 py-2 rounded font-medium hover:bg-green-50 transition-colors\">\n            {t('downloadPDF', 'Download PDF')}\n          </button>\n        </div>\n\n        <div className=\"bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white\">\n          <div className=\"flex items-center mb-4\">\n            <i className=\"fas fa-comments text-2xl mr-3\"></i>\n            <h3 className=\"text-lg font-semibold\">{t('community', 'Community Forum')}</h3>\n          </div>\n          <p className=\"text-purple-100 mb-4\">{t('communityDesc', 'Connect with other users')}</p>\n          <button className=\"bg-white text-purple-600 px-4 py-2 rounded font-medium hover:bg-purple-50 transition-colors\">\n            {t('joinForum', 'Join Forum')}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Help;\n"], "names": ["Help", "t", "isRTL", "useLanguage", "activeSection", "setActiveSection", "useState", "searchTerm", "setSearchTerm", "expandedItems", "setExpandedItems", "helpSections", "title", "icon", "items", "id", "content", "steps", "faqItems", "question", "answer", "toggleExpanded", "itemId", "prev", "_objectSpread", "Object", "entries", "filter", "_ref", "key", "section", "searchLower", "toLowerCase", "includes", "some", "item", "_jsxs", "className", "concat", "children", "_jsx", "type", "value", "onChange", "e", "target", "placeholder", "map", "_ref2", "onClick", "faq", "step", "index"], "sourceRoot": ""}