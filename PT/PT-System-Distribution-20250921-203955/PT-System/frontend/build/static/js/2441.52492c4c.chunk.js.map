{"version": 3, "file": "static/js/2441.52492c4c.chunk.js", "mappings": "iMAIA,MAcA,EAdyBA,KACvB,MAAM,UAAEC,EAAS,aAAEC,EAAY,eAAEC,IAAmBC,EAAAA,EAAAA,KAEpD,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,UACvDF,EAAAA,EAAAA,KAACG,EAAAA,EAAgB,CACfP,UAAWA,EACXC,aAAcA,EACdC,eAAgBA,M", "sources": ["pages/ReassessmentPage.jsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\nimport ReassessmentForm from '../components/Reassessment/ReassessmentForm';\n\nconst ReassessmentPage = () => {\n  const { patientId, planOfCareId, reassessmentId } = useParams();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <ReassessmentForm \n        patientId={patientId} \n        planOfCareId={planOfCareId} \n        reassessmentId={reassessmentId} \n      />\n    </div>\n  );\n};\n\nexport default ReassessmentPage;\n"], "names": ["ReassessmentPage", "patientId", "planOfCareId", "reassessmentId", "useParams", "_jsx", "className", "children", "ReassessmentForm"], "sourceRoot": ""}