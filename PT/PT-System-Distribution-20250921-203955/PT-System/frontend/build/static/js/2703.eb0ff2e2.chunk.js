"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2703],{2703:(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});var r=a(5043),s=a(7921),n=a(3216),l=a(3768),d=a(579);const i=()=>{var e,t,a,i,o,c,m,x,g,p,h,u,b,f,v,j,N,k,y,w,S,I,C,T,A,R,P,L,D,B,M,F;const{t:E}=(0,s.o)(),{patientId:_}=(0,n.g)(),O=(0,n.Zp)(),[H,z]=(0,r.useState)(!0),[Z,G]=(0,r.useState)(!0),[q,U]=(0,r.useState)(null),[V,Y]=(0,r.useState)({progressTrend:[],currentStatus:null,improvementAreas:[],concernAreas:[],assessmentCount:0,functionalIndependenceComparison:null,treatmentPlanEffectiveness:[]}),[J,K]=(0,r.useState)("6months"),[Q,W]=(0,r.useState)(null),[X,$]=(0,r.useState)(!1),[ee,te]=(0,r.useState)([]),[ae,re]=(0,r.useState)("");(0,r.useEffect)(()=>{_?(se(),le()):(ne(),z(!1),G(!1))},[_,J]);const se=async()=>{try{G(!0);const e=await fetch("/api/v1/patients/".concat(_),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.ok){const t=await e.json();U(t.data)}else U({_id:_,firstName:"Ahmed",lastName:"Al-Rashid",dateOfBirth:"1985-03-15",gender:"male",phone:"+966-50-123-4567",email:"<EMAIL>",medicalRecordNumber:"MRN-2024-001",primaryCondition:"Neurological Rehabilitation",treatmentPlan:"Comprehensive Physical Therapy",assignedTherapist:"Sarah Al-Zahra",admissionDate:"2024-01-15",specialNeeds:{hasSpecialNeeds:!1,accommodations:[]}})}catch(e){console.error("Error loading patient data:",e),l.Ay.error(E("errorLoadingPatient","Error loading patient information"))}finally{G(!1)}},ne=async()=>{try{const e=await fetch("/api/v1/patients?limit=50",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.ok){const t=await e.json();te(t.data||[])}else te([{_id:"1",firstName:"Ahmed",lastName:"Al-Rashid",medicalRecordNumber:"MRN-2024-001",primaryCondition:"Neurological Rehabilitation",assignedTherapist:"Sarah Al-Zahra"},{_id:"2",firstName:"Fatima",lastName:"Al-Mansouri",medicalRecordNumber:"MRN-2024-002",primaryCondition:"Orthopedic Rehabilitation",assignedTherapist:"Omar Al-Harbi"},{_id:"3",firstName:"Mohammed",lastName:"Al-Otaibi",medicalRecordNumber:"MRN-2024-003",primaryCondition:"Pediatric Therapy",assignedTherapist:"Sarah Al-Zahra"}])}catch(e){console.error("Error loading patients:",e),l.Ay.error(E("errorLoadingPatients","Error loading patients list"))}},le=async()=>{try{z(!0);const e=await fetch("/api/v1/clinical-indicators/patient/".concat(_,"/progress?period=").concat(J),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.ok){const t=await e.json();Y(t.data)}else{Y({progressTrend:[{date:"2024-01-15",bergBalance:35,tugTime:18.5,painLevel:7,fimScore:85,overallProgress:65},{date:"2024-02-15",bergBalance:42,tugTime:15.2,painLevel:5,fimScore:95,overallProgress:75},{date:"2024-03-15",bergBalance:48,tugTime:12.8,painLevel:3,fimScore:105,overallProgress:85}],currentStatus:{bergBalance:{score:48,riskLevel:"low-risk"},tugTest:{time:12.8,riskLevel:"normal"},painLevel:{current:3,average:3.5},functionalIndependence:{total:105,motor:78,cognitive:27},overallProgress:85},improvementAreas:["Balance and Stability","Pain Management","Functional Independence"],concernAreas:[],assessmentCount:3,period:J,functionalIndependenceComparison:{beforeTreatment:{totalScore:65,motorScore:45,cognitiveScore:20,date:"2024-01-15",level:"moderate-dependence"},afterTreatment:{totalScore:105,motorScore:78,cognitiveScore:27,date:"2024-03-15",level:"minimal-dependence"},improvement:{totalImprovement:40,motorImprovement:33,cognitiveImprovement:7,improvementPercentage:61.5,treatmentDuration:60}},treatmentPlanEffectiveness:[{planType:"Neurological Rehabilitation",patientsCount:25,averageImprovement:78.5,bergBalanceImprovement:85.2,tugImprovement:72.8,painReduction:68.4,fimImprovement:82.1,successRate:88},{planType:"Orthopedic Rehabilitation",patientsCount:32,averageImprovement:71.3,bergBalanceImprovement:68.9,tugImprovement:75.2,painReduction:79.8,fimImprovement:61.4,successRate:81.3},{planType:"Geriatric Rehabilitation",patientsCount:18,averageImprovement:65.7,bergBalanceImprovement:62.1,tugImprovement:58.9,painReduction:71.2,fimImprovement:70.6,successRate:77.8},{planType:"Sports Rehabilitation",patientsCount:15,averageImprovement:83.2,bergBalanceImprovement:89.4,tugImprovement:91.7,painReduction:75.8,fimImprovement:76,successRate:93.3},{planType:"Cardiac Rehabilitation",patientsCount:12,averageImprovement:69.8,bergBalanceImprovement:64.3,tugImprovement:67.1,painReduction:58.9,fimImprovement:89,successRate:83.3}]})}}catch(e){console.error("Error loading analytics data:",e)}finally{z(!1)}},de=e=>e>=80?"text-green-600 dark:text-green-400":e>=60?"text-yellow-600 dark:text-yellow-400":"text-red-600 dark:text-red-400",ie=e=>{switch(e){case"low-risk":case"normal":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200";case"moderate-risk":case"mild-impairment":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200";case"high-risk":case"severe-impairment":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-200"}};if(H)return(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6"}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[...Array(4)].map((e,t)=>(0,d.jsx)("div",{className:"h-32 bg-gray-200 dark:bg-gray-700 rounded-lg"},t))}),(0,d.jsx)("div",{className:"h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"})]})});const oe=ee.filter(e=>e.firstName.toLowerCase().includes(ae.toLowerCase())||e.lastName.toLowerCase().includes(ae.toLowerCase())||e.medicalRecordNumber.toLowerCase().includes(ae.toLowerCase())||e.primaryCondition.toLowerCase().includes(ae.toLowerCase()));return _?(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8",children:(0,d.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,d.jsx)("div",{className:"px-6 py-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)("button",{onClick:()=>O("/patients"),className:"mr-4 p-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,d.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:E("clinicalIndicatorsDashboard","Clinical Indicators Dashboard")})]}),Z?(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:E("loadingPatient","Loading patient information...")})]}):q?(0,d.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-user text-blue-600 dark:text-blue-400 text-xl"})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[q.firstName," ",q.lastName]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[E("mrn","MRN"),": ",q.medicalRecordNumber]})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-heartbeat text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:q.primaryCondition}),(0,d.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[E("therapist","Therapist"),": ",q.assignedTherapist]})]})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-calendar text-purple-600 dark:text-purple-400 text-xl"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:E("admissionDate","Admission Date")}),(0,d.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:new Date(q.admissionDate).toLocaleDateString()})]})]})]}):(0,d.jsx)("p",{className:"text-red-600 dark:text-red-400",children:E("patientNotFound","Patient not found")})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("button",{onClick:()=>O("/forms/clinical-indicators/".concat(_)),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),E("newAssessment","New Assessment")]}),(0,d.jsxs)("select",{value:J,onChange:e=>K(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"1month",children:E("1month","1 Month")}),(0,d.jsx)("option",{value:"3months",children:E("3months","3 Months")}),(0,d.jsx)("option",{value:"6months",children:E("6months","6 Months")}),(0,d.jsx)("option",{value:"1year",children:E("1year","1 Year")})]})]})]})})})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8",children:[(0,d.jsxs)("button",{onClick:()=>W("bergBalance"),className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-balance-scale text-blue-600 dark:text-blue-400 text-2xl"})}),(0,d.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(ie(null===(e=V.currentStatus)||void 0===e||null===(t=e.bergBalance)||void 0===t?void 0:t.riskLevel)),children:null===(a=V.currentStatus)||void 0===a||null===(i=a.bergBalance)||void 0===i?void 0:i.riskLevel})]}),(0,d.jsx)("h3",{className:"text-sm font-medium text-blue-600 dark:text-blue-400 mb-1",children:E("bergBalanceScale","Berg Balance Scale")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:[(null===(o=V.currentStatus)||void 0===o||null===(c=o.bergBalance)||void 0===c?void 0:c.score)||0,"/56"]}),(0,d.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1",children:E("balanceAssessment","Balance Assessment")}),(0,d.jsxs)("div",{className:"mt-2 text-xs text-blue-500 dark:text-blue-300",children:[(0,d.jsx)("i",{className:"fas fa-mouse-pointer mr-1"}),E("clickForDetails","Click for details")]})]}),(0,d.jsxs)("button",{onClick:()=>W("tugTest"),className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-walking text-green-600 dark:text-green-400 text-2xl"})}),(0,d.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(ie(null===(m=V.currentStatus)||void 0===m||null===(x=m.tugTest)||void 0===x?void 0:x.riskLevel)),children:null===(g=V.currentStatus)||void 0===g||null===(p=g.tugTest)||void 0===p?void 0:p.riskLevel})]}),(0,d.jsx)("h3",{className:"text-sm font-medium text-green-600 dark:text-green-400 mb-1",children:E("tugTest","Timed Up & Go")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:[(null===(h=V.currentStatus)||void 0===h||null===(u=h.tugTest)||void 0===u?void 0:u.time)||0,"s"]}),(0,d.jsx)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1",children:E("mobilityAssessment","Mobility Assessment")}),(0,d.jsxs)("div",{className:"mt-2 text-xs text-green-500 dark:text-green-300",children:[(0,d.jsx)("i",{className:"fas fa-mouse-pointer mr-1"}),E("clickForDetails","Click for details")]})]}),(0,d.jsxs)("button",{onClick:()=>W("painLevel"),className:"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-red-200 dark:border-red-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-red-100 dark:bg-red-900/40 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-heartbeat text-red-600 dark:text-red-400 text-2xl"})}),(0,d.jsx)("div",{className:"text-xs text-red-600 dark:text-red-400",children:"VAS Scale"})]}),(0,d.jsx)("h3",{className:"text-sm font-medium text-red-600 dark:text-red-400 mb-1",children:E("currentPainLevel","Current Pain Level")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-red-900 dark:text-red-100",children:[(null===(b=V.currentStatus)||void 0===b||null===(f=b.painLevel)||void 0===f?void 0:f.current)||0,"/10"]}),(0,d.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400 mt-1",children:E("painAssessment","Pain Assessment")}),(0,d.jsxs)("div",{className:"mt-2 text-xs text-red-500 dark:text-red-300",children:[(0,d.jsx)("i",{className:"fas fa-mouse-pointer mr-1"}),E("clickForDetails","Click for details")]})]}),(0,d.jsxs)("button",{onClick:()=>W("functionalIndependence"),className:"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-user-check text-orange-600 dark:text-orange-400 text-2xl"})}),(0,d.jsx)("div",{className:"text-xs text-orange-600 dark:text-orange-400",children:"FIM Score"})]}),(0,d.jsx)("h3",{className:"text-sm font-medium text-orange-600 dark:text-orange-400 mb-1",children:E("functionalIndependence","Functional Independence")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-orange-900 dark:text-orange-100",children:[(null===(v=V.currentStatus)||void 0===v||null===(j=v.functionalIndependence)||void 0===j?void 0:j.total)||0,"/126"]}),(0,d.jsx)("p",{className:"text-xs text-orange-600 dark:text-orange-400 mt-1",children:E("independenceLevel","Independence Level")}),(0,d.jsxs)("div",{className:"mt-2 text-xs text-orange-500 dark:text-orange-300",children:[(0,d.jsx)("i",{className:"fas fa-mouse-pointer mr-1"}),E("clickForDetails","Click for details")]})]}),(0,d.jsxs)("button",{onClick:()=>W("overallProgress"),className:"bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg shadow-lg border border-purple-200 dark:border-purple-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left w-full",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-chart-line text-purple-600 dark:text-purple-400 text-2xl"})}),(0,d.jsx)("div",{className:"text-xs text-purple-600 dark:text-purple-400",children:"Composite"})]}),(0,d.jsx)("h3",{className:"text-sm font-medium text-purple-600 dark:text-purple-400 mb-1",children:E("overallProgress","Overall Progress")}),(0,d.jsxs)("p",{className:"text-2xl font-bold ".concat(de((null===(N=V.currentStatus)||void 0===N?void 0:N.overallProgress)||0)),children:[(null===(k=V.currentStatus)||void 0===k?void 0:k.overallProgress)||0,"%"]}),(0,d.jsx)("p",{className:"text-xs text-purple-600 dark:text-purple-400 mt-1",children:E("compositeScore","Composite Score")}),(0,d.jsxs)("div",{className:"mt-2 text-xs text-purple-500 dark:text-purple-300",children:[(0,d.jsx)("i",{className:"fas fa-mouse-pointer mr-1"}),E("clickForDetails","Click for details")]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-indigo-200 dark:border-indigo-700 p-6 mb-8",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-indigo-900 dark:text-indigo-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-chart-bar text-indigo-600 dark:text-indigo-400 mr-2"}),E("functionalIndependenceComparison","\u062f\u0631\u062c\u0629 \u0627\u0644\u0627\u0633\u062a\u0642\u0644\u0627\u0644\u064a\u0629 \u0627\u0644\u0648\u0638\u064a\u0641\u064a\u0629 \u0642\u0628\u0644 \u0648\u0628\u0639\u062f \u0627\u0644\u0628\u0631\u0646\u0627\u0645\u062c \u0627\u0644\u0639\u0644\u0627\u062c\u064a")]}),V.functionalIndependenceComparison&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-red-100 dark:bg-red-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-calendar-minus text-red-600 dark:text-red-400 text-xl"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-indigo-900 dark:text-indigo-100",children:E("beforeTreatment","\u0642\u0628\u0644 \u0627\u0644\u0639\u0644\u0627\u062c")}),(0,d.jsx)("p",{className:"text-sm text-indigo-600 dark:text-indigo-400",children:new Date(V.functionalIndependenceComparison.beforeTreatment.date).toLocaleDateString()})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:E("totalFimScore","\u0625\u062c\u0645\u0627\u0644\u064a \u0627\u0644\u0646\u0642\u0627\u0637")}),(0,d.jsxs)("span",{className:"font-bold text-indigo-900 dark:text-indigo-100",children:[V.functionalIndependenceComparison.beforeTreatment.totalScore,"/126"]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:E("motorScore","\u0627\u0644\u0646\u0642\u0627\u0637 \u0627\u0644\u062d\u0631\u0643\u064a\u0629")}),(0,d.jsxs)("span",{className:"font-bold text-indigo-900 dark:text-indigo-100",children:[V.functionalIndependenceComparison.beforeTreatment.motorScore,"/91"]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:E("cognitiveScore","\u0627\u0644\u0646\u0642\u0627\u0637 \u0627\u0644\u0645\u0639\u0631\u0641\u064a\u0629")}),(0,d.jsxs)("span",{className:"font-bold text-indigo-900 dark:text-indigo-100",children:[V.functionalIndependenceComparison.beforeTreatment.cognitiveScore,"/35"]})]}),(0,d.jsx)("div",{className:"pt-2 border-t border-indigo-200 dark:border-indigo-600",children:(0,d.jsx)("span",{className:"text-xs text-red-600 dark:text-red-400 font-medium",children:E("moderateDependence","\u0627\u0639\u062a\u0645\u0627\u062f \u0645\u062a\u0648\u0633\u0637")})})]})]}),(0,d.jsx)("div",{className:"flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"p-4 bg-green-100 dark:bg-green-900/40 rounded-full mb-3",children:(0,d.jsx)("i",{className:"fas fa-arrow-right text-green-600 dark:text-green-400 text-2xl"})}),(0,d.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 rounded-lg p-3",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-green-700 dark:text-green-300 mb-1",children:E("improvement","\u0627\u0644\u062a\u062d\u0633\u0646")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:["+",V.functionalIndependenceComparison.improvement.improvementPercentage,"%"]}),(0,d.jsxs)("p",{className:"text-xs text-green-600 dark:text-green-400",children:[V.functionalIndependenceComparison.improvement.treatmentDuration," ",E("days","\u064a\u0648\u0645")]})]})]})}),(0,d.jsxs)("div",{className:"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-6",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-calendar-check text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-indigo-900 dark:text-indigo-100",children:E("afterTreatment","\u0628\u0639\u062f \u0627\u0644\u0639\u0644\u0627\u062c")}),(0,d.jsx)("p",{className:"text-sm text-indigo-600 dark:text-indigo-400",children:new Date(V.functionalIndependenceComparison.afterTreatment.date).toLocaleDateString()})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:E("totalFimScore","\u0625\u062c\u0645\u0627\u0644\u064a \u0627\u0644\u0646\u0642\u0627\u0637")}),(0,d.jsxs)("span",{className:"font-bold text-indigo-900 dark:text-indigo-100",children:[V.functionalIndependenceComparison.afterTreatment.totalScore,"/126"]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:E("motorScore","\u0627\u0644\u0646\u0642\u0627\u0637 \u0627\u0644\u062d\u0631\u0643\u064a\u0629")}),(0,d.jsxs)("span",{className:"font-bold text-indigo-900 dark:text-indigo-100",children:[V.functionalIndependenceComparison.afterTreatment.motorScore,"/91"]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:E("cognitiveScore","\u0627\u0644\u0646\u0642\u0627\u0637 \u0627\u0644\u0645\u0639\u0631\u0641\u064a\u0629")}),(0,d.jsxs)("span",{className:"font-bold text-indigo-900 dark:text-indigo-100",children:[V.functionalIndependenceComparison.afterTreatment.cognitiveScore,"/35"]})]}),(0,d.jsx)("div",{className:"pt-2 border-t border-indigo-200 dark:border-indigo-600",children:(0,d.jsx)("span",{className:"text-xs text-green-600 dark:text-green-400 font-medium",children:E("minimalDependence","\u0627\u0639\u062a\u0645\u0627\u062f \u0642\u0644\u064a\u0644")})})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6 mb-8",children:[(0,d.jsxs)("h2",{className:"text-xl font-semibold text-emerald-900 dark:text-emerald-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-chart-pie text-emerald-600 dark:text-emerald-400 mr-2"}),E("treatmentPlanEffectiveness","\u0646\u0633\u0628\u0629 \u0627\u0644\u062a\u062d\u0633\u0646 \u0627\u0644\u0633\u0631\u064a\u0631\u064a \u062d\u0633\u0628 \u0646\u0648\u0639 \u0627\u0644\u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c\u064a\u0629 \u0627\u0644\u0645\u0633\u062a\u062e\u062f\u0645\u0629")]}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"min-w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"border-b border-emerald-200 dark:border-emerald-600",children:[(0,d.jsx)("th",{className:"text-left py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300",children:E("treatmentPlanType","\u0646\u0648\u0639 \u0627\u0644\u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c\u064a\u0629")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300",children:E("patientsCount","\u0639\u062f\u062f \u0627\u0644\u0645\u0631\u0636\u0649")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300",children:E("averageImprovement","\u0645\u062a\u0648\u0633\u0637 \u0627\u0644\u062a\u062d\u0633\u0646")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300",children:E("bergImprovement","\u062a\u062d\u0633\u0646 \u0627\u0644\u062a\u0648\u0627\u0632\u0646")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300",children:E("tugImprovement","\u062a\u062d\u0633\u0646 \u0627\u0644\u062d\u0631\u0643\u0629")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300",children:E("painReduction","\u062a\u0642\u0644\u064a\u0644 \u0627\u0644\u0623\u0644\u0645")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 text-sm font-medium text-emerald-700 dark:text-emerald-300",children:E("successRate","\u0645\u0639\u062f\u0644 \u0627\u0644\u0646\u062c\u0627\u062d")})]})}),(0,d.jsx)("tbody",{children:V.treatmentPlanEffectiveness.map((e,t)=>(0,d.jsxs)("tr",{className:"border-b border-emerald-100 dark:border-emerald-800 hover:bg-emerald-50 dark:hover:bg-emerald-900/10",children:[(0,d.jsx)("td",{className:"py-4 px-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-2 bg-emerald-100 dark:bg-emerald-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-heartbeat text-emerald-600 dark:text-emerald-400"})}),(0,d.jsx)("span",{className:"font-medium text-emerald-900 dark:text-emerald-100",children:e.planType})]})}),(0,d.jsx)("td",{className:"text-center py-4 px-4 font-medium text-emerald-900 dark:text-emerald-100",children:e.patientsCount}),(0,d.jsx)("td",{className:"text-center py-4 px-4",children:(0,d.jsxs)("span",{className:"font-bold ".concat(de(e.averageImprovement)),children:[e.averageImprovement,"%"]})}),(0,d.jsx)("td",{className:"text-center py-4 px-4",children:(0,d.jsxs)("span",{className:"font-bold ".concat(de(e.bergBalanceImprovement)),children:[e.bergBalanceImprovement,"%"]})}),(0,d.jsx)("td",{className:"text-center py-4 px-4",children:(0,d.jsxs)("span",{className:"font-bold ".concat(de(e.tugImprovement)),children:[e.tugImprovement,"%"]})}),(0,d.jsx)("td",{className:"text-center py-4 px-4",children:(0,d.jsxs)("span",{className:"font-bold ".concat(de(e.painReduction)),children:[e.painReduction,"%"]})}),(0,d.jsx)("td",{className:"text-center py-4 px-4",children:(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.successRate>=90?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200":e.successRate>=80?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200"),children:[e.successRate,"%"]})})]},t))})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-green-900 dark:text-green-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-arrow-up text-green-600 dark:text-green-400 mr-2"}),E("improvementAreas","Areas of Improvement")]}),V.improvementAreas.length>0?(0,d.jsx)("div",{className:"space-y-3",children:V.improvementAreas.map((e,t)=>(0,d.jsx)("div",{className:"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-check text-green-600 dark:text-green-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-green-900 dark:text-green-100",children:e}),(0,d.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:E("showingPositiveProgress","Showing positive progress")})]})]})},t))}):(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("i",{className:"fas fa-chart-line text-4xl text-green-300 dark:text-green-600 mb-4"}),(0,d.jsx)("p",{className:"text-green-600 dark:text-green-400",children:E("noImprovementData","No improvement data available yet")})]})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-red-200 dark:border-red-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-red-900 dark:text-red-100 mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2"}),E("concernAreas","Areas of Concern")]}),V.concernAreas.length>0?(0,d.jsx)("div",{className:"space-y-3",children:V.concernAreas.map((e,t)=>(0,d.jsx)("div",{className:"bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-2 bg-red-100 dark:bg-red-900/40 rounded-lg mr-3",children:(0,d.jsx)("i",{className:"fas fa-exclamation text-red-600 dark:text-red-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-red-900 dark:text-red-100",children:e}),(0,d.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:E("requiresAttention","Requires attention")})]})]})},t))}):(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("i",{className:"fas fa-shield-alt text-4xl text-green-300 dark:text-green-600 mb-4"}),(0,d.jsx)("p",{className:"text-green-600 dark:text-green-400",children:E("noConcernAreas","No areas of concern identified")})]})]})]}),(0,d.jsxs)("div",{className:"mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-clipboard-list text-gray-600 dark:text-gray-400 mr-2"}),E("assessmentSummary","Assessment Summary")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"p-4 bg-blue-100 dark:bg-blue-900/40 rounded-lg mb-3",children:(0,d.jsx)("i",{className:"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-3xl"})}),(0,d.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:E("totalAssessments","Total Assessments")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:V.assessmentCount}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:E("inSelectedPeriod","In selected period")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"p-4 bg-green-100 dark:bg-green-900/40 rounded-lg mb-3",children:(0,d.jsx)("i",{className:"fas fa-trending-up text-green-600 dark:text-green-400 text-3xl"})}),(0,d.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:E("improvementRate","Improvement Rate")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:V.improvementAreas.length>0?"85%":"0%"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:E("acrossAllMetrics","Across all metrics")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"p-4 bg-purple-100 dark:bg-purple-900/40 rounded-lg mb-3",children:(0,d.jsx)("i",{className:"fas fa-award text-purple-600 dark:text-purple-400 text-3xl"})}),(0,d.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:E("complianceScore","Compliance Score")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:"100%"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:E("carfCbahiHipaa","CARF, CBAHI, HIPAA")})]})]})]}),Q&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:["bergBalance"===Q&&E("bergBalanceDetails","Berg Balance Scale Details"),"tugTest"===Q&&E("tugTestDetails","Timed Up & Go Test Details"),"painLevel"===Q&&E("painLevelDetails","Pain Level Details"),"functionalIndependence"===Q&&E("functionalIndependenceDetails","Functional Independence Details"),"overallProgress"===Q&&E("overallProgressDetails","Overall Progress Details")]}),(0,d.jsx)("button",{onClick:()=>W(null),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,d.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,d.jsxs)("div",{className:"space-y-6",children:["bergBalance"===Q&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-blue-900 dark:text-blue-100 mb-2",children:E("currentScore","Current Score")}),(0,d.jsxs)("p",{className:"text-3xl font-bold text-blue-600 dark:text-blue-400",children:[(null===(y=V.currentStatus)||void 0===y||null===(w=y.bergBalance)||void 0===w?void 0:w.score)||0,"/56"]}),(0,d.jsxs)("p",{className:"text-sm text-blue-600 dark:text-blue-400 mt-1",children:["Risk Level: ",null===(S=V.currentStatus)||void 0===S||null===(I=S.bergBalance)||void 0===I?void 0:I.riskLevel]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Score Interpretation:"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsx)("li",{children:"\u2022 41-56: Low fall risk"}),(0,d.jsx)("li",{children:"\u2022 21-40: Medium fall risk"}),(0,d.jsx)("li",{children:"\u2022 0-20: High fall risk"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Progress Trend:"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Showing improvement over the last 3 assessments"})]})]})]}),"tugTest"===Q&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mb-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-green-900 dark:text-green-100 mb-2",children:E("currentTime","Current Time")}),(0,d.jsxs)("p",{className:"text-3xl font-bold text-green-600 dark:text-green-400",children:[(null===(C=V.currentStatus)||void 0===C||null===(T=C.tugTest)||void 0===T?void 0:T.time)||0," seconds"]}),(0,d.jsxs)("p",{className:"text-sm text-green-600 dark:text-green-400 mt-1",children:["Risk Level: ",null===(A=V.currentStatus)||void 0===A||null===(R=A.tugTest)||void 0===R?void 0:R.riskLevel]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Time Interpretation:"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsx)("li",{children:"\u2022 \u226410 seconds: Normal"}),(0,d.jsx)("li",{children:"\u2022 11-14 seconds: Mild impairment"}),(0,d.jsx)("li",{children:"\u2022 15-20 seconds: Moderate impairment"}),(0,d.jsx)("li",{children:"\u2022 >20 seconds: Severe impairment"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Improvement:"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"6.7 seconds improvement from initial assessment"})]})]})]}),"functionalIndependence"===Q&&(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 mb-4",children:[(0,d.jsx)("h4",{className:"font-semibold text-orange-900 dark:text-orange-100 mb-2",children:E("currentFimScore","Current FIM Score")}),(0,d.jsxs)("p",{className:"text-3xl font-bold text-orange-600 dark:text-orange-400",children:[(null===(P=V.currentStatus)||void 0===P||null===(L=P.functionalIndependence)||void 0===L?void 0:L.total)||0,"/126"]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-orange-600 dark:text-orange-400",children:"Motor Score"}),(0,d.jsxs)("p",{className:"font-bold",children:[(null===(D=V.currentStatus)||void 0===D||null===(B=D.functionalIndependence)||void 0===B?void 0:B.motor)||0,"/91"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-orange-600 dark:text-orange-400",children:"Cognitive Score"}),(0,d.jsxs)("p",{className:"font-bold",children:[(null===(M=V.currentStatus)||void 0===M||null===(F=M.functionalIndependence)||void 0===F?void 0:F.cognitive)||0,"/35"]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-2",children:"Independence Levels:"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsx)("li",{children:"\u2022 108-126: Complete independence"}),(0,d.jsx)("li",{children:"\u2022 90-107: Modified independence"}),(0,d.jsx)("li",{children:"\u2022 72-89: Minimal assistance"}),(0,d.jsx)("li",{children:"\u2022 54-71: Moderate assistance"}),(0,d.jsx)("li",{children:"\u2022 36-53: Maximal assistance"}),(0,d.jsx)("li",{children:"\u2022 18-35: Total assistance"})]})]})]})]}),(0,d.jsx)("div",{className:"mt-6 flex justify-end",children:(0,d.jsx)("button",{onClick:()=>W(null),className:"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:E("close","Close")})})]})})})]}):(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8",children:(0,d.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,d.jsx)("div",{className:"px-6 py-6",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:E("selectPatientForClinicalIndicators","Select Patient for Clinical Indicators")}),(0,d.jsx)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2",children:E("selectPatientDesc","Choose a patient to view their clinical indicators dashboard")})]})})})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8",children:(0,d.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:E("searchPatients","Search Patients")}),(0,d.jsx)("input",{type:"text",placeholder:E("searchPatientsPlaceholder","Search by name, MRN, or condition..."),value:ae,onChange:e=>re(e.target.value),className:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:oe.map(e=>(0,d.jsxs)("button",{onClick:()=>O("/analytics/clinical-indicators/".concat(e._id)),className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-xl transition-all duration-200 transform hover:scale-105 text-left",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg mr-4",children:(0,d.jsx)("i",{className:"fas fa-user text-blue-600 dark:text-blue-400 text-2xl"})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[e.firstName," ",e.lastName]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[E("mrn","MRN"),": ",e.medicalRecordNumber]})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-heartbeat text-green-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.primaryCondition})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-purple-500 mr-2"}),(0,d.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.assignedTherapist})]})]}),(0,d.jsxs)("div",{className:"mt-4 flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-xs text-blue-600 dark:text-blue-400 font-medium",children:E("viewClinicalIndicators","View Clinical Indicators")}),(0,d.jsx)("i",{className:"fas fa-arrow-right text-blue-600 dark:text-blue-400"})]})]},e._id))}),0===oe.length&&ae&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:E("noPatientsFound","No patients found")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:E("tryDifferentSearch","Try a different search term")})]})]})}}}]);
//# sourceMappingURL=2703.eb0ff2e2.chunk.js.map