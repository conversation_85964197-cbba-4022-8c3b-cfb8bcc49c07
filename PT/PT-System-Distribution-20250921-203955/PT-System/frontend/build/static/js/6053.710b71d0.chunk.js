"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6053],{6053:(t,e,a)=>{a.r(e),a.d(e,{default:()=>d});a(5043);var n=a(3216),i=a(6873),r=a(579);const d=()=>{var t,e;const{patientId:a,planId:d}=(0,n.g)(),l=(0,n.zy)();return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)(i.default,{patientId:a,planId:d,patientData:null===(t=l.state)||void 0===t?void 0:t.patient,fromPatientProfile:null===(e=l.state)||void 0===e?void 0:e.fromPatientProfile})})}}}]);
//# sourceMappingURL=6053.710b71d0.chunk.js.map