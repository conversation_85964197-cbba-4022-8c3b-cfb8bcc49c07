"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6834],{1369:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))}const l=n.forwardRef(i)},3099:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(2555),n=s(3986),a=(s(5043),s(579));const i=["children","className","padding","shadow","border","rounded","background","hover"],l=e=>{let{children:t,className:s="",padding:l="p-6",shadow:o="shadow-sm",border:c="border border-gray-200",rounded:d="rounded-lg",background:m="bg-white",hover:u=""}=e,h=(0,n.A)(e,i);const g=[m,c,d,o,l,u,s].filter(Boolean).join(" ");return(0,a.jsx)("div",(0,r.A)((0,r.A)({className:g},h),{},{children:t}))}},3315:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const l=n.forwardRef(i)},3641:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))}const l=n.forwardRef(i)},3986:(e,t,s)=>{function r(e,t){if(null==e)return{};var s,r,n=function(e,t){if(null==e)return{};var s={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;s[r]=e[r]}return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)s=a[r],-1===t.indexOf(s)&&{}.propertyIsEnumerable.call(e,s)&&(n[s]=e[s])}return n}s.d(t,{A:()=>r})},4053:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const l=n.forwardRef(i)},4122:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M20.25 6.375c0 2.278-3.694 4.125-8.25 4.125S3.75 8.653 3.75 6.375m16.5 0c0-2.278-3.694-4.125-8.25-4.125S3.75 4.097 3.75 6.375m16.5 0v11.25c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125V6.375m16.5 0v3.75m-16.5-3.75v3.75m16.5 0v3.75C20.25 16.153 16.556 18 12 18s-8.25-1.847-8.25-4.125v-3.75m16.5 0c0 2.278-3.694 4.125-8.25 4.125s-8.25-1.847-8.25-4.125"}))}const l=n.forwardRef(i)},4538:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const l=n.forwardRef(i)},5590:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const l=n.forwardRef(i)},6834:(e,t,s)=>{s.r(t),s.d(t,{default:()=>j});var r=s(2555),n=s(5043),a=s(4117),i=s(5475),l=s(3315),o=s(4538),c=s(4122),d=s(1369),m=s(4053),u=s(9399),h=s(3641),g=s(8153),p=s(7504),x=s(5590),v=s(3099),f=s(6761),w=s(6305),b=s(579);const j=()=>{const{t:e}=(0,a.Bd)(),[t,s]=(0,n.useState)(!0),[j,y]=(0,n.useState)({users:0,activeUsers:0,roles:0,permissions:0,systemHealth:"good"});(0,n.useEffect)(()=>{A()},[]);const A=async()=>{try{var e,t,r,n;s(!0);const[a,i]=await Promise.all([w.Ay.get("/users"),w.Ay.get("/permissions/roles")]);y({users:a.data.total||(null===(e=a.data.data)||void 0===e?void 0:e.length)||0,activeUsers:(null===(t=a.data.data)||void 0===t||null===(r=t.filter(e=>e.isActive))||void 0===r?void 0:r.length)||0,roles:(null===(n=i.data.data)||void 0===n?void 0:n.length)||0,permissions:29,systemHealth:"good"})}catch(a){console.error("Failed to load dashboard data:",a)}finally{s(!1)}},k=[{title:e("userManagement","User Management"),description:e("manageSystemUsers","Manage system users and their access"),icon:x.A,path:"/admin/users",color:"blue",stat:"".concat(j.users," users")},{title:e("permissionsManagement","Permissions Management"),description:e("manageUserPermissions","Configure user permissions and access control"),icon:g.A,path:"/admin/permissions",color:"green",stat:"".concat(j.permissions," permissions")},{title:e("roleManagement","Role Management"),description:e("manageUserRoles","Define and manage user roles"),icon:p.A,path:"/admin/roles",color:"purple",stat:"".concat(j.roles," roles")},{title:e("systemSettings","System Settings"),description:e("configureSystemSettings","Configure global system settings"),icon:m.A,path:"/admin/system-settings",color:"gray",stat:"Configure"},{title:e("auditLogs","Audit Logs"),description:e("viewSystemLogs","View system activity and audit logs"),icon:d.A,path:"/admin/audit-logs",color:"yellow",stat:"Monitor"},{title:e("securitySettings","Security Settings"),description:e("manageSecuritySettings","Manage security policies and settings"),icon:h.A,path:"/admin/security",color:"red",stat:"Secure"},{title:e("backupRestore","Backup & Restore"),description:e("manageBackups","Manage system backups and data recovery"),icon:c.A,path:"/admin/backup",color:"indigo",stat:"Backup"},{title:e("systemMonitoring","System Monitoring"),description:e("monitorSystemHealth","Monitor system performance and health"),icon:l.A,path:"/admin/monitoring",color:"teal",stat:j.systemHealth}],N=e=>{let{title:t,value:s,icon:r,color:n,description:a}=e;return(0,b.jsx)(v.A,{className:"p-6",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)("div",{className:"p-3 rounded-full bg-".concat(n,"-100"),children:(0,b.jsx)(r,{className:"h-6 w-6 text-".concat(n,"-600")})}),(0,b.jsxs)("div",{className:"ml-4",children:[(0,b.jsx)("p",{className:"text-sm font-medium text-gray-600",children:t}),(0,b.jsx)("p",{className:"text-2xl font-bold text-".concat(n,"-600"),children:s}),a&&(0,b.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a})]})]})})},M=e=>{let{title:t,description:s,icon:r,path:n,color:a,stat:l}=e;return(0,b.jsx)(i.N_,{to:n,className:"block",children:(0,b.jsx)(v.A,{className:"p-6 hover:shadow-lg transition-shadow duration-200 h-full",children:(0,b.jsx)("div",{className:"flex items-start justify-between",children:(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsxs)("div",{className:"flex items-center mb-3",children:[(0,b.jsx)("div",{className:"p-2 rounded-lg bg-".concat(a,"-100"),children:(0,b.jsx)(r,{className:"h-5 w-5 text-".concat(a,"-600")})}),(0,b.jsx)("h3",{className:"ml-3 text-lg font-semibold text-gray-900",children:t})]}),(0,b.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:s}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsx)("span",{className:"text-sm font-medium text-".concat(a,"-600"),children:l}),(0,b.jsx)("span",{className:"text-xs text-gray-400",children:"Click to manage \u2192"})]})]})})})})};return t?(0,b.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,b.jsx)(f.Ay,{size:"lg"})}):(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsx)("div",{className:"flex items-center justify-between",children:(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("adminDashboard","Admin Dashboard")}),(0,b.jsx)("p",{className:"text-gray-600 mt-1",children:e("adminDashboardDescription","Manage system settings, users, and monitor system health")})]})}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,b.jsx)(N,{title:e("totalUsers","Total Users"),value:j.users,icon:x.A,color:"blue",description:"".concat(j.activeUsers," active")}),(0,b.jsx)(N,{title:e("userRoles","User Roles"),value:j.roles,icon:p.A,color:"green",description:"Defined roles"}),(0,b.jsx)(N,{title:e("permissions","Permissions"),value:j.permissions,icon:g.A,color:"purple",description:"System permissions"}),(0,b.jsx)(N,{title:e("systemHealth","System Health"),value:"good"===j.systemHealth?"Good":"Warning",icon:"good"===j.systemHealth?o.A:u.A,color:"good"===j.systemHealth?"green":"yellow",description:"Overall status"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("administrationTools","Administration Tools")}),(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:k.map(e=>(0,b.jsx)(M,(0,r.A)({},e),e.path))})]}),(0,b.jsxs)(v.A,{className:"p-6",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("quickActions","Quick Actions")}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,b.jsxs)(i.N_,{to:"/admin/users",className:"flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors",children:[(0,b.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,b.jsx)("span",{className:"text-blue-700 font-medium",children:e("addNewUser","Add New User")})]}),(0,b.jsxs)(i.N_,{to:"/admin/roles",className:"flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors",children:[(0,b.jsx)(p.A,{className:"h-5 w-5 text-green-600 mr-3"}),(0,b.jsx)("span",{className:"text-green-700 font-medium",children:e("manageRoles","Manage Roles")})]}),(0,b.jsxs)(i.N_,{to:"/admin/monitoring",className:"flex items-center p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors",children:[(0,b.jsx)(l.A,{className:"h-5 w-5 text-purple-600 mr-3"}),(0,b.jsx)("span",{className:"text-purple-700 font-medium",children:e("viewSystemStatus","View System Status")})]})]})]})]})}},7504:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}const l=n.forwardRef(i)},8153:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const l=n.forwardRef(i)},9399:(e,t,s)=>{s.d(t,{A:()=>l});var r=s(3986),n=s(5043);const a=["title","titleId"];function i(e,t){let{title:s,titleId:i}=e,l=(0,r.A)(e,a);return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},l),s?n.createElement("title",{id:i},s):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const l=n.forwardRef(i)}}]);
//# sourceMappingURL=6834.0f8ec316.chunk.js.map