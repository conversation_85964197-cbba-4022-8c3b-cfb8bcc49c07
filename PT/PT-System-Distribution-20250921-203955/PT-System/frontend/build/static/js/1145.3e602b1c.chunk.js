"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1145],{1145:(e,r,a)=>{a.d(r,{A:()=>b});var t=a(2555),s=a(5043),l=a(3216),i=a(7921),n=a(4528),d=a(3768),o=a(579);const c=e=>{let{formData:r,handleInputChange:a,errors:t}=e;const{t:s}=(0,i.o)(),l=[{value:"present",label:s("present","Present"),color:"green"},{value:"absent",label:s("absent","Absent"),color:"red"},{value:"late",label:s("late","Late"),color:"yellow"},{value:"early_departure",label:s("earlyDeparture","Early Departure"),color:"orange"}],n=[{value:"cooperative",label:s("cooperative","Cooperative"),color:"green"},{value:"anxious",label:s("anxious","Anxious"),color:"yellow"},{value:"agitated",label:s("agitated","Agitated"),color:"red"},{value:"withdrawn",label:s("withdrawn","Withdrawn"),color:"gray"},{value:"cheerful",label:s("cheerful","Cheerful"),color:"blue"}],d=[{value:"high",label:s("high","High"),color:"green"},{value:"normal",label:s("normal","Normal"),color:"blue"},{value:"low",label:s("low","Low"),color:"yellow"},{value:"fatigued",label:s("fatigued","Fatigued"),color:"red"}],c=(e,r)=>{const a="px-3 py-2 rounded-lg border transition-colors cursor-pointer";if(!r)return"".concat(a," bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600");switch(e){case"green":return"".concat(a," bg-green-100 border-green-500 text-green-800 dark:bg-green-900/30 dark:border-green-400 dark:text-green-200");case"red":return"".concat(a," bg-red-100 border-red-500 text-red-800 dark:bg-red-900/30 dark:border-red-400 dark:text-red-200");case"yellow":return"".concat(a," bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/30 dark:border-yellow-400 dark:text-yellow-200");case"orange":return"".concat(a," bg-orange-100 border-orange-500 text-orange-800 dark:bg-orange-900/30 dark:border-orange-400 dark:text-orange-200");case"blue":return"".concat(a," bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/30 dark:border-blue-400 dark:text-blue-200");default:return"".concat(a," bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/30 dark:border-gray-400 dark:text-gray-200")}};return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-user-check text-green-600 dark:text-green-400 mr-2"}),s("patientStatus","Patient Status & Presentation")]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:s("patientAttendance","Patient Attendance")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:l.map(e=>(0,o.jsxs)("label",{className:c(e.color,r.patientAttendance===e.value),children:[(0,o.jsx)("input",{type:"radio",name:"patientAttendance",value:e.value,checked:r.patientAttendance===e.value,onChange:e=>a("patientAttendance",e.target.value),className:"sr-only"}),(0,o.jsx)("div",{className:"flex items-center justify-center",children:(0,o.jsx)("span",{className:"text-sm font-medium",children:e.label})})]},e.value))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:s("patientMood","Patient Mood & Behavior")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-3",children:n.map(e=>(0,o.jsxs)("label",{className:c(e.color,r.patientMood===e.value),children:[(0,o.jsx)("input",{type:"radio",name:"patientMood",value:e.value,checked:r.patientMood===e.value,onChange:e=>a("patientMood",e.target.value),className:"sr-only"}),(0,o.jsx)("div",{className:"flex items-center justify-center",children:(0,o.jsx)("span",{className:"text-sm font-medium",children:e.label})})]},e.value))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:s("energyLevel","Energy Level")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:d.map(e=>(0,o.jsxs)("label",{className:c(e.color,r.patientEnergyLevel===e.value),children:[(0,o.jsx)("input",{type:"radio",name:"patientEnergyLevel",value:e.value,checked:r.patientEnergyLevel===e.value,onChange:e=>a("patientEnergyLevel",e.target.value),className:"sr-only"}),(0,o.jsx)("div",{className:"flex items-center justify-center",children:(0,o.jsx)("span",{className:"text-sm font-medium",children:e.label})})]},e.value))})]}),(0,o.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:[(0,o.jsxs)("h3",{className:"text-md font-semibold text-red-900 dark:text-red-100 mb-4",children:[(0,o.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2"}),s("painAssessment","Pain Assessment (0-10 Scale)")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:s("painBefore","Pain Level Before Treatment")}),(0,o.jsx)("input",{type:"number",min:"0",max:"10",value:r.painLevel.before,onChange:e=>a("painLevel.before",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white ".concat(t.painBefore?"border-red-500":"border-red-300"),placeholder:"0-10"}),t.painBefore&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:t.painBefore})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:s("painAfter","Pain Level After Treatment")}),(0,o.jsx)("input",{type:"number",min:"0",max:"10",value:r.painLevel.after,onChange:e=>a("painLevel.after",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white ".concat(t.painAfter?"border-red-500":"border-red-300"),placeholder:"0-10"}),t.painAfter&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:t.painAfter})]})]}),(0,o.jsx)("div",{className:"mt-3 text-xs text-red-700 dark:text-red-300",children:(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"0:"})," ",s("noPain","No pain")," | ",(0,o.jsx)("strong",{children:"1-3:"})," ",s("mildPain","Mild pain")," | ",(0,o.jsx)("strong",{children:"4-6:"})," ",s("moderatePain","Moderate pain")," | ",(0,o.jsx)("strong",{children:"7-10:"})," ",s("severePain","Severe pain")]})})]})]})]})},g=e=>{var r,a;let{formData:s,handleInputChange:l,addArrayItem:n,removeArrayItem:d,handleArrayChange:c}=e;const{t:g}=(0,i.o)(),m=["Range of Motion Exercises","Strengthening Exercises","Balance Training","Gait Training","Transfer Training","Functional Activities","Pain Management","Postural Training","Coordination Exercises","Endurance Training"],x=["Parallel Bars","Exercise Mat","Therapy Ball","Resistance Bands","Weights","Balance Board","Walker","Wheelchair","TENS Unit","Ultrasound Machine"],u=()=>{n("activitiesPerformed",{activity:"",duration:"",intensity:"moderate",patientResponse:"",completed:!0})};return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-dumbbell text-purple-600 dark:text-purple-400 mr-2"}),g("treatmentActivities","Treatment Activities & Interventions")]}),(0,o.jsxs)("div",{className:"space-y-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-gray-900 dark:text-white",children:g("activitiesPerformed","Activities Performed")}),(0,o.jsxs)("button",{type:"button",onClick:u,className:"px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-1"}),g("addActivity","Add Activity")]})]}),null===(r=s.activitiesPerformed)||void 0===r?void 0:r.map((e,r)=>(0,o.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,o.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white",children:[g("activity","Activity")," ",r+1]}),(0,o.jsx)("button",{type:"button",onClick:()=>d("activitiesPerformed",r),className:"text-red-600 hover:text-red-800 transition-colors",children:(0,o.jsx)("i",{className:"fas fa-trash text-sm"})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,o.jsxs)("div",{className:"md:col-span-2",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("activityName","Activity Name")}),(0,o.jsxs)("select",{value:e.activity,onChange:a=>c("activitiesPerformed",r,(0,t.A)((0,t.A)({},e),{},{activity:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:g("selectActivity","Select Activity")}),m.map(e=>(0,o.jsx)("option",{value:e,children:e},e)),(0,o.jsx)("option",{value:"other",children:g("other","Other")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("duration","Duration (min)")}),(0,o.jsx)("input",{type:"number",min:"1",value:e.duration,onChange:a=>c("activitiesPerformed",r,(0,t.A)((0,t.A)({},e),{},{duration:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"15"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("intensity","Intensity")}),(0,o.jsxs)("select",{value:e.intensity,onChange:a=>c("activitiesPerformed",r,(0,t.A)((0,t.A)({},e),{},{intensity:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"low",children:g("low","Low")}),(0,o.jsx)("option",{value:"moderate",children:g("moderate","Moderate")}),(0,o.jsx)("option",{value:"high",children:g("high","High")})]})]})]}),(0,o.jsxs)("div",{className:"mt-4",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("patientResponse","Patient Response")}),(0,o.jsx)("textarea",{value:e.patientResponse,onChange:a=>c("activitiesPerformed",r,(0,t.A)((0,t.A)({},e),{},{patientResponse:a.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:g("describePatientResponse","Describe patient response to activity")})]})]},r)),(!s.activitiesPerformed||0===s.activitiesPerformed.length)&&(0,o.jsxs)("div",{className:"text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg",children:[(0,o.jsx)("i",{className:"fas fa-dumbbell text-4xl text-gray-400 mb-2"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-2",children:g("noActivitiesAdded","No activities added yet")}),(0,o.jsxs)("button",{type:"button",onClick:u,className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),g("addFirstActivity","Add First Activity")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-gray-900 dark:text-white",children:g("equipmentUsed","Equipment Used")}),(0,o.jsxs)("button",{type:"button",onClick:()=>{n("equipmentUsed",{equipment:"",duration:"",settings:"",effectiveness:"effective"})},className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-1"}),g("addEquipment","Add Equipment")]})]}),null===(a=s.equipmentUsed)||void 0===a?void 0:a.map((e,r)=>(0,o.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,o.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white",children:[g("equipment","Equipment")," ",r+1]}),(0,o.jsx)("button",{type:"button",onClick:()=>d("equipmentUsed",r),className:"text-red-600 hover:text-red-800 transition-colors",children:(0,o.jsx)("i",{className:"fas fa-trash text-sm"})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("equipmentName","Equipment Name")}),(0,o.jsxs)("select",{value:e.equipment,onChange:a=>c("equipmentUsed",r,(0,t.A)((0,t.A)({},e),{},{equipment:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:g("selectEquipment","Select Equipment")}),x.map(e=>(0,o.jsx)("option",{value:e,children:e},e)),(0,o.jsx)("option",{value:"other",children:g("other","Other")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("duration","Duration (min)")}),(0,o.jsx)("input",{type:"number",min:"1",value:e.duration,onChange:a=>c("equipmentUsed",r,(0,t.A)((0,t.A)({},e),{},{duration:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"10"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("effectiveness","Effectiveness")}),(0,o.jsxs)("select",{value:e.effectiveness,onChange:a=>c("equipmentUsed",r,(0,t.A)((0,t.A)({},e),{},{effectiveness:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"very_effective",children:g("veryEffective","Very Effective")}),(0,o.jsx)("option",{value:"effective",children:g("effective","Effective")}),(0,o.jsx)("option",{value:"somewhat_effective",children:g("somewhatEffective","Somewhat Effective")}),(0,o.jsx)("option",{value:"not_effective",children:g("notEffective","Not Effective")})]})]})]}),(0,o.jsxs)("div",{className:"mt-4",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("settings","Settings/Notes")}),(0,o.jsx)("input",{type:"text",value:e.settings,onChange:a=>c("equipmentUsed",r,(0,t.A)((0,t.A)({},e),{},{settings:a.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:g("equipmentSettings","Equipment settings, intensity, or special notes")})]})]},r))]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:g("modificationsNeeded","Modifications Needed")}),(0,o.jsx)("textarea",{value:s.modificationsNeeded,onChange:e=>l("modificationsNeeded",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:g("describeModifications","Describe any modifications made to activities or equipment")})]})]})]})},m=e=>{var r;let{formData:a,handleInputChange:s,handleArrayChange:l,errors:n}=e;const{t:d}=(0,i.o)(),c=[{value:"excellent",label:d("excellent","Excellent"),color:"green"},{value:"good",label:d("good","Good"),color:"blue"},{value:"satisfactory",label:d("satisfactory","Satisfactory"),color:"yellow"},{value:"needs_improvement",label:d("needsImprovement","Needs Improvement"),color:"red"}],g=[{value:"excellent",label:d("excellent","Excellent (90-100%)"),color:"green"},{value:"good",label:d("good","Good (70-89%)"),color:"blue"},{value:"fair",label:d("fair","Fair (50-69%)"),color:"yellow"},{value:"poor",label:d("poor","Poor (<50%)"),color:"red"},{value:"not_applicable",label:d("notApplicable","Not Applicable"),color:"gray"}],m=(e,r)=>{const a="px-3 py-2 rounded-lg border transition-colors cursor-pointer text-center";if(!r)return"".concat(a," bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600");switch(e){case"green":return"".concat(a," bg-green-100 border-green-500 text-green-800 dark:bg-green-900/30 dark:border-green-400 dark:text-green-200");case"blue":return"".concat(a," bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/30 dark:border-blue-400 dark:text-blue-200");case"yellow":return"".concat(a," bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/30 dark:border-yellow-400 dark:text-yellow-200");case"red":return"".concat(a," bg-red-100 border-red-500 text-red-800 dark:bg-red-900/30 dark:border-red-400 dark:text-red-200");default:return"".concat(a," bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/30 dark:border-gray-400 dark:text-gray-200")}};return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-clipboard-list text-orange-600 dark:text-orange-400 mr-2"}),d("progressNotesAssessment","Progress Notes & Assessment")]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-gray-900 dark:text-white mb-4",children:d("functionalGoalsProgress","Functional Goals Progress")}),null===(r=a.functionalGoals)||void 0===r?void 0:r.map((e,r)=>(0,o.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4",children:[(0,o.jsxs)("div",{className:"mb-3",children:[(0,o.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:[d("goal","Goal")," ",r+1,": ",e.goal]}),(0,o.jsx)("div",{className:"flex items-center space-x-4",children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:e.achieved,onChange:a=>l("functionalGoals",r,(0,t.A)((0,t.A)({},e),{},{achieved:a.target.checked})),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:d("goalAchieved","Goal Achieved")})]})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:d("progressDescription","Progress Description")}),(0,o.jsx)("textarea",{value:e.progress,onChange:a=>l("functionalGoals",r,(0,t.A)((0,t.A)({},e),{},{progress:a.target.value})),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("describeProgress","Describe progress towards this goal")})]})]},r))]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[d("objectiveFindings","Objective Findings")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("textarea",{value:a.objectiveFindings,onChange:e=>s("objectiveFindings",e.target.value),rows:4,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(n.objectiveFindings?"border-red-500":"border-gray-300"),placeholder:d("objectiveFindingsPlaceholder","Measurable observations, ROM, strength, balance, etc.")}),n.objectiveFindings&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:n.objectiveFindings})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("subjectiveComplaints","Subjective Complaints")}),(0,o.jsx)("textarea",{value:a.subjectiveComplaints,onChange:e=>s("subjectiveComplaints",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("subjectiveComplaintsPlaceholder","Patient reported symptoms, concerns, feedback")})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("treatmentResponse","Treatment Response")}),(0,o.jsx)("textarea",{value:a.treatmentResponse,onChange:e=>s("treatmentResponse",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("treatmentResponsePlaceholder","How patient responded to treatment interventions")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("adverseReactions","Adverse Reactions")}),(0,o.jsx)("textarea",{value:a.adverseReactions,onChange:e=>s("adverseReactions",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("adverseReactionsPlaceholder","Any negative reactions or complications")})]})]}),(0,o.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,o.jsxs)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4",children:[(0,o.jsx)("i",{className:"fas fa-star text-blue-600 dark:text-blue-400 mr-2"}),d("qualityIndicators","Quality Indicators")]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-3",children:d("sessionQuality","Session Quality")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:c.map(e=>(0,o.jsxs)("label",{className:m(e.color,a.sessionQuality===e.value),children:[(0,o.jsx)("input",{type:"radio",name:"sessionQuality",value:e.value,checked:a.sessionQuality===e.value,onChange:e=>s("sessionQuality",e.target.value),className:"sr-only"}),(0,o.jsx)("span",{className:"text-sm font-medium",children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-3",children:d("homeExerciseCompliance","Home Exercise Compliance")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-3",children:g.map(e=>(0,o.jsxs)("label",{className:m(e.color,a.homeExerciseCompliance===e.value),children:[(0,o.jsx)("input",{type:"radio",name:"homeExerciseCompliance",value:e.value,checked:a.homeExerciseCompliance===e.value,onChange:e=>s("homeExerciseCompliance",e.target.value),className:"sr-only"}),(0,o.jsx)("span",{className:"text-xs font-medium",children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:d("patientSatisfaction","Patient Satisfaction (1-5)")}),(0,o.jsx)("input",{type:"number",min:"1",max:"5",value:a.patientSatisfaction,onChange:e=>s("patientSatisfaction",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:"1-5"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:d("goalAchievementPercentage","Goal Achievement (%)")}),(0,o.jsx)("input",{type:"number",min:"0",max:"100",value:a.goalAchievement,onChange:e=>s("goalAchievement",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:"0-100"})]})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("nextSessionPlan","Next Session Plan")}),(0,o.jsx)("textarea",{value:a.nextSessionPlan,onChange:e=>s("nextSessionPlan",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("nextSessionPlanPlaceholder","Plan for next treatment session, modifications, goals")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:d("recommendationsForFamily","Recommendations for Family/Caregivers")}),(0,o.jsx)("textarea",{value:a.recommendationsForFamily,onChange:e=>s("recommendationsForFamily",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("familyRecommendationsPlaceholder","Home exercises, precautions, activities to encourage")})]})]})]})};var x=a(6643);const u=e=>{let{painPoints:r=[],onPainPointUpdate:a,onPainPointDelete:l,readonly:n=!1}=e;const{t:d,isRTL:c}=(0,i.o)(),[g,m]=(0,s.useState)(null),x=[{value:"sharp",label:"Sharp",labelAr:"\u062d\u0627\u062f"},{value:"dull",label:"Dull",labelAr:"\u0643\u0644\u064a\u0644"},{value:"burning",label:"Burning",labelAr:"\u062d\u0627\u0631\u0642"},{value:"throbbing",label:"Throbbing",labelAr:"\u0646\u0627\u0628\u0636"},{value:"stabbing",label:"Stabbing",labelAr:"\u0637\u0627\u0639\u0646"},{value:"cramping",label:"Cramping",labelAr:"\u062a\u0634\u0646\u062c\u064a"},{value:"aching",label:"Aching",labelAr:"\u0645\u0624\u0644\u0645"},{value:"tingling",label:"Tingling",labelAr:"\u0648\u062e\u0632"}],u=[{value:"constant",label:"Constant",labelAr:"\u0645\u0633\u062a\u0645\u0631"},{value:"intermittent",label:"Intermittent",labelAr:"\u0645\u062a\u0642\u0637\u0639"},{value:"occasional",label:"Occasional",labelAr:"\u0623\u062d\u064a\u0627\u0646\u0627\u064b"},{value:"rare",label:"Rare",labelAr:"\u0646\u0627\u062f\u0631"}],b=[{value:"movement",label:"Movement",labelAr:"\u0627\u0644\u062d\u0631\u0643\u0629"},{value:"rest",label:"Rest",labelAr:"\u0627\u0644\u0631\u0627\u062d\u0629"},{value:"pressure",label:"Pressure",labelAr:"\u0627\u0644\u0636\u063a\u0637"},{value:"weather",label:"Weather",labelAr:"\u0627\u0644\u0637\u0642\u0633"},{value:"stress",label:"Stress",labelAr:"\u0627\u0644\u062a\u0648\u062a\u0631"},{value:"activity",label:"Physical Activity",labelAr:"\u0627\u0644\u0646\u0634\u0627\u0637 \u0627\u0644\u0628\u062f\u0646\u064a"}],p=(e,s,l)=>{if(n)return;const i=r.map(r=>r.regionId===e?(0,t.A)((0,t.A)({},r),{},{[s]:l}):r);null===a||void 0===a||a(i)},h=e=>{var r,a;const t={1:{en:"Mild",ar:"\u062e\u0641\u064a\u0641"},2:{en:"Mild-Moderate",ar:"\u062e\u0641\u064a\u0641 \u0625\u0644\u0649 \u0645\u062a\u0648\u0633\u0637"},3:{en:"Moderate",ar:"\u0645\u062a\u0648\u0633\u0637"},4:{en:"Moderate-Severe",ar:"\u0645\u062a\u0648\u0633\u0637 \u0625\u0644\u0649 \u0634\u062f\u064a\u062f"},5:{en:"Severe",ar:"\u0634\u062f\u064a\u062f"},6:{en:"Very Severe",ar:"\u0634\u062f\u064a\u062f \u062c\u062f\u0627\u064b"},7:{en:"Extreme",ar:"\u0645\u0641\u0631\u0637"},8:{en:"Unbearable",ar:"\u0644\u0627 \u064a\u064f\u062d\u062a\u0645\u0644"},9:{en:"Maximum",ar:"\u0623\u0642\u0635\u0649"},10:{en:"Worst Possible",ar:"\u0627\u0644\u0623\u0633\u0648\u0623 \u0639\u0644\u0649 \u0627\u0644\u0625\u0637\u0644\u0627\u0642"}};return c?null===(r=t[e])||void 0===r?void 0:r.ar:null===(a=t[e])||void 0===a?void 0:a.en};return 0===r.length?(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("painAssessmentTable","Pain Assessment Details")}),(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("div",{className:"text-gray-400 dark:text-gray-500 mb-2",children:(0,o.jsx)("svg",{className:"mx-auto h-12 w-12",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:d("noPainPointsSelected","No pain points selected. Click on the body map to add pain locations.")})]})]}):(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600",children:[(0,o.jsxs)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-600",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:d("painAssessmentTable","Pain Assessment Details")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:d("painTableDescription","Detailed assessment of each pain location")})]}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full",children:[(0,o.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:d("region","Region")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:d("painLevel","Pain Level")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:d("painType","Pain Type")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:d("frequency","Frequency")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:d("triggers","Triggers")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:d("notes","Notes")}),!n&&(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:d("actions","Actions")})]})}),(0,o.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:r.map((e,r)=>{var a,t,s,i,g,m,v;return(0,o.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,o.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-4 h-4 rounded-full mr-3 border border-gray-300",style:{backgroundColor:(v=e.painLevel,{1:"#FEF3C7",2:"#FDE68A",3:"#FCD34D",4:"#FBBF24",5:"#F59E0B",6:"#D97706",7:"#B45309",8:"#92400E",9:"#78350F",10:"#451A03"}[v]||"#E5E7EB")}}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:c?e.regionNameAr:e.regionName}),(0,o.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["#",e.regionId]})]})]})}),(0,o.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsxs)("span",{className:"text-lg font-bold text-gray-900 dark:text-white mr-2",children:[e.painLevel,"/10"]}),(0,o.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:h(e.painLevel)})]})}),(0,o.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:n?(0,o.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:e.painType?c?null===(a=x.find(r=>r.value===e.painType))||void 0===a?void 0:a.labelAr:null===(t=x.find(r=>r.value===e.painType))||void 0===t?void 0:t.label:"-"}):(0,o.jsxs)("select",{value:e.painType||"",onChange:r=>p(e.regionId,"painType",r.target.value),className:"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:d("select","Select...")}),x.map(e=>(0,o.jsx)("option",{value:e.value,children:c?e.labelAr:e.label},e.value))]})}),(0,o.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:n?(0,o.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:e.frequency?c?null===(s=u.find(r=>r.value===e.frequency))||void 0===s?void 0:s.labelAr:null===(i=u.find(r=>r.value===e.frequency))||void 0===i?void 0:i.label:"-"}):(0,o.jsxs)("select",{value:e.frequency||"",onChange:r=>p(e.regionId,"frequency",r.target.value),className:"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:d("select","Select...")}),u.map(e=>(0,o.jsx)("option",{value:e.value,children:c?e.labelAr:e.label},e.value))]})}),(0,o.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:n?(0,o.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:e.triggers?c?null===(g=b.find(r=>r.value===e.triggers))||void 0===g?void 0:g.labelAr:null===(m=b.find(r=>r.value===e.triggers))||void 0===m?void 0:m.label:"-"}):(0,o.jsxs)("select",{value:e.triggers||"",onChange:r=>p(e.regionId,"triggers",r.target.value),className:"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:d("select","Select...")}),b.map(e=>(0,o.jsx)("option",{value:e.value,children:c?e.labelAr:e.label},e.value))]})}),(0,o.jsx)("td",{className:"px-4 py-4",children:n?(0,o.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:e.notes||"-"}):(0,o.jsx)("input",{type:"text",value:e.notes||"",onChange:r=>p(e.regionId,"notes",r.target.value),placeholder:d("addNotes","Add notes..."),className:"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white w-full"})}),!n&&(0,o.jsx)("td",{className:"px-4 py-4 whitespace-nowrap",children:(0,o.jsx)("button",{onClick:()=>{return r=e.regionId,void(n||null===l||void 0===l||l(r));var r},className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300",title:d("deletePainPoint","Delete pain point"),children:(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})})]},e.regionId)})})]})}),(0,o.jsx)("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[d("totalPainPoints","Total Pain Points"),": ",(0,o.jsx)("strong",{children:r.length})]}),(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[d("averagePainLevel","Average Pain Level"),": ",(0,o.jsxs)("strong",{children:[r.length>0?(r.reduce((e,r)=>e+r.painLevel,0)/r.length).toFixed(1):"0","/10"]})]})]})})]})},b=e=>{let{patientId:r,treatmentId:a,patientData:b,fromPatientProfile:p,initialData:h,onSave:v,onCancel:y}=e;const{t:f}=(0,i.o)(),{user:j}=(0,n.A)(),k=(0,l.Zp)(),{patientId:N,treatmentId:w,progressId:A}=(0,l.g)(),[P,C]=(0,s.useState)(!1),[D,S]=(0,s.useState)({}),[T,E]=(0,s.useState)(null),[I,L]=(0,s.useState)(null),q=r||N,F=a||w,[R,M]=(0,s.useState)({sessionDate:(new Date).toISOString().split("T")[0],sessionTime:(new Date).toTimeString().slice(0,5),sessionDuration:"",sessionType:"individual",caregiverName:(null===j||void 0===j?void 0:j.name)||"",caregiverTitle:(null===j||void 0===j?void 0:j.title)||"",caregiverBadgeNo:(null===j||void 0===j?void 0:j.badgeNo)||"",caregiverDepartment:(null===j||void 0===j?void 0:j.department)||"",patientAttendance:"present",patientMood:"cooperative",patientEnergyLevel:"normal",activitiesPerformed:[],exercisesCompleted:[],equipmentUsed:[],modificationsNeeded:"",painLevel:{before:"",after:"",scale:"0-10"},painPoints:[],painAssessmentNotes:"",functionalGoals:[],progressTowardsGoals:"",vitalSigns:{bloodPressure:"",heartRate:"",respiratoryRate:"",temperature:"",oxygenSaturation:""},objectiveFindings:"",subjectiveComplaints:"",treatmentResponse:"",adverseReactions:"",nextSessionPlan:"",homeExerciseCompliance:"",recommendationsForFamily:"",sessionQuality:"excellent",patientSatisfaction:"",goalAchievement:"",followUpRequired:!1,followUpDate:"",followUpReason:"",safetyIncidents:!1,incidentDescription:"",infectionControlCompliance:!0,equipmentSafetyCheck:!0});(0,s.useEffect)(()=>{if(p&&b){var e,r;E({id:b._id||b.id,name:b.name||"".concat(b.firstName," ").concat(b.lastName),nameEn:b.nameEn||"".concat(b.firstName," ").concat(b.lastName),mrNumber:b.mrNumber||b._id,age:b.age,diagnosis:(null===(e=b.medicalHistory)||void 0===e?void 0:e.primaryDiagnosis)||b.diagnosis||"Not specified"});const a={id:F,type:"Physical Therapy",startDate:"2024-01-15",therapist:(null===(r=b.therapyInfo)||void 0===r?void 0:r.primaryTherapist)||"Current Therapist",currentGoals:["Improve balance and coordination","Increase lower extremity strength","Enhance functional mobility"]};L(a),h&&M(e=>(0,t.A)((0,t.A)((0,t.A)({},e),h),{},{functionalGoals:a.currentGoals.map(e=>({goal:e,progress:"",achieved:!1}))})),C(!1)}else q&&(C(!0),setTimeout(()=>{const e={id:F,type:"Physical Therapy",startDate:"2024-01-15",therapist:"Dr. Sarah Ahmed",currentGoals:["Improve balance and coordination","Increase lower extremity strength","Enhance functional mobility"]};E({id:q,name:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",nameEn:"Ahmed Mohammed Ali",mrNumber:"MR-2024-001",age:8,diagnosis:"Cerebral Palsy"}),L(e),M(r=>(0,t.A)((0,t.A)({},r),{},{functionalGoals:e.currentGoals.map(e=>({goal:e,progress:"",achieved:!1}))})),C(!1)},500))},[q,F,p,b,h]);const B=(e,r)=>{const a=(0,t.A)({},R);if(e.includes(".")){const t=e.split(".");let s=a;for(let e=0;e<t.length-1;e++)s[t[e]]||(s[t[e]]={}),s=s[t[e]];s[t[t.length-1]]=r}else a[e]=r;M(a),D[e]&&S(r=>(0,t.A)((0,t.A)({},r),{},{[e]:null}))},G=(e,r,a)=>{const s=(0,t.A)({},R),l=e.split(".");let i=s;for(let t=0;t<l.length-1;t++)i=i[l[t]];i[l[l.length-1]][r]=a,M(s)},U=e=>{M(r=>(0,t.A)((0,t.A)({},r),{},{painPoints:e}))};return P&&!T?(0,o.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,o.jsx)("div",{className:"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900",children:(0,o.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return R.sessionDate||(e.sessionDate=f("sessionDateRequired","Session date is required")),R.sessionTime||(e.sessionTime=f("sessionTimeRequired","Session time is required")),R.sessionDuration||(e.sessionDuration=f("sessionDurationRequired","Session duration is required")),R.caregiverName.trim()||(e.caregiverName=f("caregiverNameRequired","Caregiver name is required")),R.objectiveFindings.trim()||(e.objectiveFindings=f("objectiveFindingsRequired","Objective findings are required")),R.painLevel.before&&(R.painLevel.before<0||R.painLevel.before>10)&&(e.painBefore=f("painLevelInvalid","Pain level must be between 0-10")),R.painLevel.after&&(R.painLevel.after<0||R.painLevel.after>10)&&(e.painAfter=f("painLevelInvalid","Pain level must be between 0-10")),S(e),0===Object.keys(e).length})()){C(!0);try{await new Promise(e=>setTimeout(e,1e3));const e=(0,t.A)((0,t.A)({},R),{},{id:Date.now(),patientId:q,treatmentId:F,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),createdBy:(null===j||void 0===j?void 0:j.id)||"current-user"});if(v)v(e);else{const r=JSON.parse(localStorage.getItem("dailyProgressData")||"[]");r.push(e),localStorage.setItem("dailyProgressData",JSON.stringify(r)),d.Ay.success(f("progressNoteSaved","Daily progress note saved successfully")),k(q?"/patients/".concat(q):"/patients")}}catch(r){console.error("Error saving progress note:",r),d.Ay.error(f("errorSavingProgress","Error saving progress note"))}finally{C(!1)}}else d.Ay.error(f("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-8",children:[(0,o.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:(0,o.jsxs)("div",{className:"flex items-start justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[f("dailyProgressNote","Daily Progress Note"),q&&T&&(0,o.jsxs)("span",{className:"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3",children:["- ",T.nameEn||T.name]})]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:f("progressNoteDescription","Comprehensive daily treatment progress documentation")}),T&&I&&(0,o.jsxs)("div",{className:"flex flex-wrap gap-4 mt-3 text-sm",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("i",{className:"fas fa-user text-blue-600 dark:text-blue-400"}),(0,o.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:[f("patient","Patient"),": ",T.nameEn," (",T.mrNumber,")"]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("i",{className:"fas fa-heartbeat text-green-600 dark:text-green-400"}),(0,o.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:[f("treatment","Treatment"),": ",I.type]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("i",{className:"fas fa-user-md text-purple-600 dark:text-purple-400"}),(0,o.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:[f("caregiver","Caregiver"),": ",R.caregiverName]})]})]})]}),(0,o.jsx)("div",{className:"flex space-x-3",children:(0,o.jsx)("button",{type:"button",onClick:y||(()=>k(q?"/patients/".concat(q):"/patients")),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:f("cancel","Cancel")})})]})}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,o.jsx)("i",{className:"fas fa-clock text-blue-600 dark:text-blue-400 mr-2"}),f("sessionInformation","Session Information")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[f("sessionDate","Session Date")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"date",value:R.sessionDate,onChange:e=>B("sessionDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(D.sessionDate?"border-red-500":"border-gray-300")}),D.sessionDate&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:D.sessionDate})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[f("sessionTime","Session Time")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"time",value:R.sessionTime,onChange:e=>B("sessionTime",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(D.sessionTime?"border-red-500":"border-gray-300")}),D.sessionTime&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:D.sessionTime})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[f("duration","Duration (minutes)")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"number",min:"15",max:"180",value:R.sessionDuration,onChange:e=>B("sessionDuration",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(D.sessionDuration?"border-red-500":"border-gray-300"),placeholder:"30"}),D.sessionDuration&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:D.sessionDuration})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:f("sessionType","Session Type")}),(0,o.jsxs)("select",{value:R.sessionType,onChange:e=>B("sessionType",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"individual",children:f("individual","Individual")}),(0,o.jsx)("option",{value:"group",children:f("group","Group")}),(0,o.jsx)("option",{value:"home_visit",children:f("homeVisit","Home Visit")})]})]})]})]}),(0,o.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4",children:[(0,o.jsx)("i",{className:"fas fa-user-md text-blue-600 dark:text-blue-400 mr-2"}),f("caregiverInformation","Caregiver Information")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:[f("caregiverName","Caregiver Name")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"text",value:R.caregiverName,onChange:e=>B("caregiverName",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ".concat(D.caregiverName?"border-red-500":"border-blue-300"),placeholder:f("enterCaregiverName","Enter caregiver name")}),D.caregiverName&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:D.caregiverName})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:f("title","Title/Position")}),(0,o.jsx)("input",{type:"text",value:R.caregiverTitle,onChange:e=>B("caregiverTitle",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:f("enterTitle","e.g., Physical Therapist")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:f("badgeNumber","Badge Number")}),(0,o.jsx)("input",{type:"text",value:R.caregiverBadgeNo,onChange:e=>B("caregiverBadgeNo",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:f("enterBadgeNumber","Enter badge number")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:f("department","Department")}),(0,o.jsx)("input",{type:"text",value:R.caregiverDepartment,onChange:e=>B("caregiverDepartment",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:f("enterDepartment","e.g., Physical Therapy")})]})]})]}),(0,o.jsx)(c,{formData:R,handleInputChange:B,errors:D}),(0,o.jsx)(g,{formData:R,handleInputChange:B,addArrayItem:function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const a=(0,t.A)({},R);a[e]||(a[e]=[]),a[e].push(r),M(a)},removeArrayItem:(e,r)=>{const a=(0,t.A)({},R);a[e].splice(r,1),M(a)},handleArrayChange:G}),(0,o.jsx)(m,{formData:R,handleInputChange:B,handleArrayChange:G,errors:D}),(0,o.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold text-red-900 dark:text-red-100 mb-4",children:[(0,o.jsx)("i",{className:"fas fa-heartbeat text-red-600 dark:text-red-400 mr-2"}),f("painAssessment","Pain Assessment")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:f("painLevelBefore","Pain Level Before Treatment")}),(0,o.jsxs)("select",{value:R.painLevel.before,onChange:e=>B("painLevel.before",e.target.value),className:"w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:f("select","Select...")}),[...Array(11)].map((e,r)=>(0,o.jsxs)("option",{value:r,children:[r,"/10 - ",0===r?f("noPain","No Pain"):r<=3?f("mildPain","Mild"):r<=6?f("moderatePain","Moderate"):f("severePain","Severe")]},r))]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:f("painLevelAfter","Pain Level After Treatment")}),(0,o.jsxs)("select",{value:R.painLevel.after,onChange:e=>B("painLevel.after",e.target.value),className:"w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:f("select","Select...")}),[...Array(11)].map((e,r)=>(0,o.jsxs)("option",{value:r,children:[r,"/10 - ",0===r?f("noPain","No Pain"):r<=3?f("mildPain","Mild"):r<=6?f("moderatePain","Moderate"):f("severePain","Severe")]},r))]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-1",children:f("painAssessmentNotes","Pain Assessment Notes")}),(0,o.jsx)("textarea",{value:R.painAssessmentNotes,onChange:e=>B("painAssessmentNotes",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white",placeholder:f("painNotesPlaceholder","Describe pain characteristics, triggers, relief factors...")})]})]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-md font-medium text-red-800 dark:text-red-200 mb-3",children:f("detailedPainMapping","Detailed Pain Mapping")}),(0,o.jsx)(x.A,{selectedPainPoints:R.painPoints,onPainPointSelect:U,readonly:!1})]}),R.painPoints.length>0&&(0,o.jsx)("div",{children:(0,o.jsx)(u,{painPoints:R.painPoints,onPainPointUpdate:U,onPainPointDelete:e=>{const r=R.painPoints.filter(r=>r.regionId!==e);M(e=>(0,t.A)((0,t.A)({},e),{},{painPoints:r}))},readonly:!1})})]})]}),(0,o.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,o.jsx)("button",{type:"button",onClick:y||(()=>k(q?"/patients/".concat(q):"/patients")),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:f("cancel","Cancel")}),(0,o.jsx)("button",{type:"submit",disabled:P,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:P?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),f("saving","Saving...")]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-save mr-2"}),f("saveProgressNote","Save Progress Note")]})})]})]})})}}}]);
//# sourceMappingURL=1145.3e602b1c.chunk.js.map