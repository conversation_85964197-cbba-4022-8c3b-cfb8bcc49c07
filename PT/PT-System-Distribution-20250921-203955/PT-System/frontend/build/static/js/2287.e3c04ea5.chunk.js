"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2287],{2287:(e,a,t)=>{t.d(a,{A:()=>y});var s=t(2555),r=t(5043),l=t(7921),n=t(3216),i=t(3768),o=t(579);const d=e=>{let{formData:a,updateFormData:t,errors:r,setErrors:n}=e;const{t:i,isRTL:d}=(0,l.o)(),c=(e,l)=>{const i=(0,s.A)({},a.patientInfo);if(e.includes(".")){const[a,t]=e.split(".");i[a]=(0,s.A)((0,s.A)({},i[a]),{},{[t]:l})}else i[e]=l;t("patientInfo",i),r[e]&&n(a=>(0,s.A)((0,s.A)({},a),{},{[e]:null}))},m=(e,t,s)=>{const r=a.patientInfo[e]||[];let l;l=s?[...r,t]:r.filter(e=>e!==t),c(e,l)};return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:i("patientInformation","Patient Information")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:i("basicPatientDetails","Basic patient details and demographics")})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("patientName","Patient Name")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"text",value:a.patientInfo.name,onChange:e=>c("name",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.name?"border-red-500":"border-gray-300"),placeholder:i("enterPatientName","Enter patient name")}),r.name&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.name})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("sex","Sex")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"sex",value:"M",checked:"M"===a.patientInfo.sex,onChange:e=>c("sex",e.target.value),className:"mr-2"}),i("male","Male")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"sex",value:"F",checked:"F"===a.patientInfo.sex,onChange:e=>c("sex",e.target.value),className:"mr-2"}),i("female","Female")]})]}),r.sex&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.sex})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("maritalStatus","Marital Status")}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"maritalStatus",value:"Single",checked:"Single"===a.patientInfo.maritalStatus,onChange:e=>c("maritalStatus",e.target.value),className:"mr-2"}),i("single","Single")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"maritalStatus",value:"Married",checked:"Married"===a.patientInfo.maritalStatus,onChange:e=>c("maritalStatus",e.target.value),className:"mr-2"}),i("married","Married")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("patientId","Patient I.D No")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"text",value:a.patientInfo.patientId,onChange:e=>c("patientId",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.patientId?"border-red-500":"border-gray-300"),placeholder:i("enterPatientId","Enter patient ID")}),r.patientId&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.patientId})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("nationality","Nationality")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"text",value:a.patientInfo.nationality,onChange:e=>c("nationality",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.nationality?"border-red-500":"border-gray-300"),placeholder:i("enterNationality","Enter nationality")}),r.nationality&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.nationality})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("age","Age")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"number",min:"18",max:"120",value:a.patientInfo.age,onChange:e=>c("age",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.age?"border-red-500":"border-gray-300"),placeholder:i("enterAge","Enter age")}),r.age&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.age})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("fileNumber","File Number")}),(0,o.jsx)("input",{type:"text",value:a.patientInfo.fileNumber,onChange:e=>c("fileNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterFileNumber","Enter file number")})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("dateOfEvaluation","Date of Evaluation")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"date",value:a.patientInfo.dateOfEval,onChange:e=>c("dateOfEval",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("time","Time")}),(0,o.jsx)("input",{type:"time",value:a.patientInfo.time,onChange:e=>c("time",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("referredByHospital","Referred by Hospital")}),(0,o.jsx)("input",{type:"text",value:a.patientInfo.referredByHospital,onChange:e=>c("referredByHospital",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterReferringHospital","Enter referring hospital")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("jobOccupation","Job & Occupation")}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:[["Military","Teacher","Homemaker","Engineer-Technician","Field work","Farmers/fisherman","Office workers","Retired","Unemployed & not active","Student"].map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:a.patientInfo.jobOccupation.includes(e),onChange:a=>m("jobOccupation",e,a.target.checked),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:i(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)),(0,o.jsx)("div",{className:"col-span-full",children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:a.patientInfo.jobOccupation.includes("Other"),onChange:e=>m("jobOccupation","Other",e.target.checked),className:"mr-2"}),(0,o.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300 mr-2",children:[i("other","Other"),":"]}),(0,o.jsx)("input",{type:"text",placeholder:i("specify","Specify"),className:"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white",disabled:!a.patientInfo.jobOccupation.includes("Other")})]})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("educationLevel","Education Level")}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[["Illiterate","Literate","Literate without formal education","Complete education","Incomplete education"].map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"educationLevel",value:e,checked:a.patientInfo.educationLevel===e,onChange:e=>c("educationLevel",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:i(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)),(0,o.jsx)("div",{className:"col-span-full",children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"educationLevel",value:"Other",checked:"Other"===a.patientInfo.educationLevel,onChange:e=>c("educationLevel",e.target.value),className:"mr-2"}),(0,o.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300 mr-2",children:[i("other","Other"),":"]}),(0,o.jsx)("input",{type:"text",placeholder:i("specify","Specify"),className:"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white",disabled:"Other"!==a.patientInfo.educationLevel})]})})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("typeOfResidence","Type of Residence")}),(0,o.jsx)("div",{className:"space-y-2",children:["Apartment","Detached House","Nursing Home"].map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"typeOfResidence",value:e,checked:a.patientInfo.livingCondition.typeOfResidence===e,onChange:e=>c("livingCondition.typeOfResidence",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:i(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("socialSupport","Social Support")}),(0,o.jsx)("div",{className:"space-y-2",children:["Family assistance","Living alone","Limited Support"].map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:a.patientInfo.livingCondition.socialSupport.includes(e),onChange:t=>{const s=a.patientInfo.livingCondition.socialSupport||[];let r;r=t.target.checked?[...s,e]:s.filter(a=>a!==e),c("livingCondition.socialSupport",r)},className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:i(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e))})]})]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("diagnosis","Diagnosis")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("textarea",{value:a.patientInfo.diagnosis,onChange:e=>c("diagnosis",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.diagnosis?"border-red-500":"border-gray-300"),placeholder:i("enterDiagnosis","Enter diagnosis")}),r.diagnosis&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.diagnosis})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("icd10Code","ICD 10 CODE")}),(0,o.jsx)("input",{type:"text",value:a.patientInfo.icd10Code,onChange:e=>c("icd10Code",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterIcd10Code","Enter ICD 10 code")})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("chiefComplaint","Chief Complaint")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("textarea",{value:a.patientInfo.chiefComplaint,onChange:e=>c("chiefComplaint",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.chiefComplaint?"border-red-500":"border-gray-300"),placeholder:i("enterChiefComplaint","Enter chief complaint")}),r.chiefComplaint&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.chiefComplaint})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("associatedMedicalIssues","Associated Medical Issues")}),(0,o.jsx)("textarea",{value:a.patientInfo.associatedMedicalIssues,onChange:e=>c("associatedMedicalIssues",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterAssociatedMedicalIssues","Enter associated medical issues")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("historyOfTraumaIllness","History of Trauma/Illness")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:i("date","Date")}),(0,o.jsx)("input",{type:"date",value:a.patientInfo.historyOfTrauma.date,onChange:e=>c("historyOfTrauma.date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:i("circumstancesEtiology","Circumstances/Etiology")}),(0,o.jsx)("textarea",{value:a.patientInfo.historyOfTrauma.circumstances,onChange:e=>c("historyOfTrauma.circumstances",e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterCircumstances","Enter circumstances")})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("previousSurgery","Previous Surgery")}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"previousSurgery",value:"Yes",checked:a.patientInfo.previousSurgery.hasSurgery,onChange:e=>c("previousSurgery.hasSurgery","Yes"===e.target.value),className:"mr-2"}),i("yes","Yes")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"previousSurgery",value:"No",checked:!a.patientInfo.previousSurgery.hasSurgery,onChange:e=>c("previousSurgery.hasSurgery","Yes"===e.target.value),className:"mr-2"}),i("no","No")]})]}),a.patientInfo.previousSurgery.hasSurgery&&(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:i("typeOfSurgery","Type of Surgery")}),(0,o.jsx)("input",{type:"text",value:a.patientInfo.previousSurgery.type,onChange:e=>c("previousSurgery.type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterTypeOfSurgery","Enter type of surgery")})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("radiologicalInvestigationFindings","Radiological Investigation/Findings")}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"radiologicalInvestigation",value:"Yes",checked:a.patientInfo.radiologicalInvestigation.hasInvestigation,onChange:e=>c("radiologicalInvestigation.hasInvestigation","Yes"===e.target.value),className:"mr-2"}),i("yes","Yes")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"radiologicalInvestigation",value:"No",checked:!a.patientInfo.radiologicalInvestigation.hasInvestigation,onChange:e=>c("radiologicalInvestigation.hasInvestigation","Yes"===e.target.value),className:"mr-2"}),i("no","No")]})]}),a.patientInfo.radiologicalInvestigation.hasInvestigation&&(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:i("findings","Findings")}),(0,o.jsx)("textarea",{value:a.patientInfo.radiologicalInvestigation.findings,onChange:e=>c("radiologicalInvestigation.findings",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterFindings","Enter findings")})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("previousRehabIntervention","Previous Rehab Intervention")}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"previousRehabIntervention",value:"Yes",checked:a.patientInfo.previousRehabIntervention.hasIntervention,onChange:e=>c("previousRehabIntervention.hasIntervention","Yes"===e.target.value),className:"mr-2"}),i("yes","Yes")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"previousRehabIntervention",value:"No",checked:!a.patientInfo.previousRehabIntervention.hasIntervention,onChange:e=>c("previousRehabIntervention.hasIntervention","Yes"===e.target.value),className:"mr-2"}),i("no","No")]})]}),a.patientInfo.previousRehabIntervention.hasIntervention&&(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:i("details","Details")}),(0,o.jsx)("textarea",{value:a.patientInfo.previousRehabIntervention.details,onChange:e=>c("previousRehabIntervention.details",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterDetails","Enter details")})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("currentEquipmentAssistiveTechnology","Current Equipment/Assistive Technology")}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"currentEquipment",value:"Yes",checked:a.patientInfo.currentEquipment.hasEquipment,onChange:e=>c("currentEquipment.hasEquipment","Yes"===e.target.value),className:"mr-2"}),i("yes","Yes")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"currentEquipment",value:"No",checked:!a.patientInfo.currentEquipment.hasEquipment,onChange:e=>c("currentEquipment.hasEquipment","Yes"===e.target.value),className:"mr-2"}),i("no","No")]})]}),a.patientInfo.currentEquipment.hasEquipment&&(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:i("details","Details")}),(0,o.jsx)("textarea",{value:a.patientInfo.currentEquipment.details,onChange:e=>c("currentEquipment.details",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterDetails","Enter details")})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("precautions","Precautions")}),(0,o.jsx)("textarea",{value:a.patientInfo.precautions,onChange:e=>c("precautions",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterPrecautions","Enter precautions")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("pastMedicalHistory","Past Medical History")}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:[["Diabetes","Hypertension","Heart Conditions","Asthma","Kidney Diseases","Cancer","No known past medical history","None"].map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:a.patientInfo.pastMedicalHistory.includes(e),onChange:a=>m("pastMedicalHistory",e,a.target.checked),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:i(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)),(0,o.jsx)("div",{className:"col-span-full",children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:a.patientInfo.pastMedicalHistory.includes("Other"),onChange:e=>m("pastMedicalHistory","Other",e.target.checked),className:"mr-2"}),(0,o.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300 mr-2",children:[i("other","Other"),":"]}),(0,o.jsx)("input",{type:"text",placeholder:i("specify","Specify"),className:"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white",disabled:!a.patientInfo.pastMedicalHistory.includes("Other")})]})})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("medications","Medications")}),(0,o.jsx)("textarea",{value:a.patientInfo.medications,onChange:e=>c("medications",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterMedications","Enter medications")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("effectsSideEffects","Effects/Side Effects")}),(0,o.jsx)("textarea",{value:a.patientInfo.effectsSideEffects,onChange:e=>c("effectsSideEffects",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterEffectsSideEffects","Enter effects/side effects")})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("familyHistory","Family History")}),(0,o.jsx)("textarea",{value:a.patientInfo.familyHistory,onChange:e=>c("familyHistory",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterFamilyHistory","Enter family history")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:i("allergies","Allergies")}),(0,o.jsx)("textarea",{value:a.patientInfo.allergies,onChange:e=>c("allergies",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterAllergies","Enter allergies")})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:i("sourceOfInformation","Source of Information")}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[["Patient","Relative"].map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:a.patientInfo.sourceOfInformation.includes(e),onChange:a=>m("sourceOfInformation",e,a.target.checked),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:i(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)),(0,o.jsx)("div",{className:"col-span-full",children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:a.patientInfo.sourceOfInformation.includes("Others"),onChange:e=>m("sourceOfInformation","Others",e.target.checked),className:"mr-2"}),(0,o.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300 mr-2",children:[i("others","Others"),":"]}),(0,o.jsx)("input",{type:"text",placeholder:i("specify","Specify"),className:"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white",disabled:!a.patientInfo.sourceOfInformation.includes("Others")})]})})]})]})]})},c=e=>{var a,t,r,n,i,d,c,m;let{formData:x,updateFormData:u,errors:g,setErrors:h}=e;const{t:p,isRTL:b}=(0,l.o)(),y=(e,a)=>{const t=(0,s.A)({},x.assessmentReview);if(e.includes(".")){const s=e.split(".");let r=t;for(let e=0;e<s.length-1;e++)r[s[e]]||(r[s[e]]={}),r=r[s[e]];r[s[s.length-1]]=a}else t[e]=a;u("assessmentReview",t),g[e]&&h(a=>(0,s.A)((0,s.A)({},a),{},{[e]:null}))},v=(e,a,t)=>{const r=(0,s.A)({},x.assessmentReview);if(r.reviewOfSystems[e]||(r.reviewOfSystems[e]={complaints:[],explain:""}),"complaints"===a){const a=r.reviewOfSystems[e].complaints||[];a.includes(t)?r.reviewOfSystems[e].complaints=a.filter(e=>e!==t):r.reviewOfSystems[e].complaints=[...a,t]}else r.reviewOfSystems[e][a]=t;u("assessmentReview",r)},f=[{value:"Normal",label:p("normal","Normal")},{value:"Impaired",label:p("impaired","Impaired")},{value:"Not Applicable",label:p("notApplicable","Not Applicable")}],k=[{value:"Low",label:p("low","Low")},{value:"High",label:p("high","High")},{value:"Moderate",label:p("moderate","Moderate")},{value:"None",label:p("none","None")}],j=[{value:"Functional/effective",label:p("functionalEffective","Functional/effective")},{value:"Mild impairment",label:p("mildImpairment","Mild impairment")},{value:"Moderate impairment",label:p("moderateImpairment","Moderate impairment")},{value:"Severe impairment",label:p("severeImpairment","Severe impairment")}],N=[{value:"Excellent",label:p("excellent","Excellent")},{value:"Good",label:p("good","Good")},{value:"Fair",label:p("fair","Fair")},{value:"Poor",label:p("poor","Poor")}],w=[{value:"No apparent risk",label:p("noApparentRisk","No apparent risk")},{value:"Mild behavioral concerns",label:p("mildBehavioralConcerns","Mild behavioral concerns")},{value:"Moderate Risk",label:p("moderateRisk","Moderate Risk")},{value:"High psychosocial risk",label:p("highPsychosocialRisk","High psychosocial risk")}],R=[{value:"Age appropriate",label:p("ageAppropriate","Age appropriate")},{value:"Mild delay",label:p("mildDelay","Mild delay")},{value:"Moderate delay",label:p("moderateDelay","Moderate delay")},{value:"Severe delay",label:p("severeDelay","Severe delay")}],C={respiratory:["No Complaints","COPD","Sputum","Dyspnea","Asthma","Cyanosis","Cough"],neurology:["No Complaints","Headache/Vertigo","Seizures","Tremors","Paralysis","Tingling","Numbness"],gastrointestinal:["No Complaints","Nausea","Vomiting","Constipation","Hernia","Hematemesis"],genitourinary:["No Complaints","Incontinence","Frequency","Discharge","Hematuria","Catheter"],musculoskeletal:["No Complaints","Fractures","Trauma","Stiffness","Amputation","Congenital problems"],hematology:["No Complaints","Anemia","Bruising","Hematemesis","History of transfusion"],cardiovascular:["No Complaints","Pacemaker","Stent placement","Exertional dyspnea","Palpitations","Tachycardia"],integumentary:["No Complaints","Pliability","Skin Color","Poor healing","Presence of scar formation","Pigmentations"],reproductive:["No Complaints","Bleeding","Impotence","Hot flushes","Obstetric history"],endocrine:["No Complaints","Night Sweat","Weight Changes","Fever","Polyphagia"]},S=function(e,a){var t,s;let r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:e}),(0,o.jsx)("div",{className:"flex flex-wrap gap-4",children:f.map(e=>{var t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:a,value:e.value,checked:(null===(t=x.assessmentReview[a])||void 0===t?void 0:t.status)===e.value,onChange:e=>y("".concat(a,".status"),e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})}),r&&"Impaired"===(null===(t=x.assessmentReview[a])||void 0===t?void 0:t.status)&&(0,o.jsxs)("div",{className:"mt-3",children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:p("comments","Comments")}),(0,o.jsx)("textarea",{value:(null===(s=x.assessmentReview[a])||void 0===s?void 0:s.comments)||"",onChange:e=>y("".concat(a,".comments"),e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:p("enterComments","Enter comments")})]})]})},L=(e,a)=>{var t,s,r,l;return(0,o.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:p(a,e)}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:null===(t=C[a])||void 0===t?void 0:t.map(e=>{var t,s;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(t=x.assessmentReview.reviewOfSystems[a])||void 0===t||null===(s=t.complaints)||void 0===s?void 0:s.includes(e))||!1,onChange:t=>v(a,"complaints",e),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:p(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)})}),(null===(s=x.assessmentReview.reviewOfSystems[a])||void 0===s||null===(r=s.complaints)||void 0===r?void 0:r.some(e=>"No Complaints"!==e))&&(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:p("explain","Explain")}),(0,o.jsx)("textarea",{value:(null===(l=x.assessmentReview.reviewOfSystems[a])||void 0===l?void 0:l.explain)||"",onChange:e=>v(a,"explain",e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:p("explainSymptoms","Explain symptoms")})]})]})]},a)};return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:p("assessmentReviewOfSystems","Assessment & Review of Systems")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:p("comprehensiveSystemsAssessment","Comprehensive systems assessment and screening")})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[S(p("skinAppearance","Skin Appearance"),"skinAppearance"),S(p("mentalFunctionsScreening","Mental Functions Screening"),"mentalFunctions"),S(p("visualScreening","Visual Screening"),"visualScreening"),S(p("hearingFunctionScreening","Hearing Function Screening"),"hearingFunction")]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("nutritionalRisk","Nutritional Risk")}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:["Reduced food intake last week","BMI > 18.5 kg/m2","Weight Loss","Enteral/Parenteral Feeding","None"].map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=x.assessmentReview.nutritionalRisk)||void 0===a?void 0:a.includes(e))||!1,onChange:a=>((e,a,t)=>{const s=x.assessmentReview[e]||[];let r;r=t?[...s,a]:s.filter(e=>e!==a),y(e,r)})("nutritionalRisk",e,a.target.checked),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:p(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("fallRisk","Fall Risk")}),(0,o.jsx)("div",{className:"space-y-2",children:k.map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"fallRisk",value:e.value,checked:x.assessmentReview.fallRisk===e.value,onChange:e=>y("fallRisk",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("psychosocialBehaviorRisk","Psychosocial/Behavior Risk")}),(0,o.jsx)("div",{className:"space-y-2",children:w.map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"psychosocialBehaviorRisk",value:e.value,checked:x.assessmentReview.psychosocialBehaviorRisk===e.value,onChange:e=>y("psychosocialBehaviorRisk",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("developmentalRisk","Developmental Risk")}),(0,o.jsx)("div",{className:"space-y-2",children:R.map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"developmentalRisk",value:e.value,checked:x.assessmentReview.developmentalRisk===e.value,onChange:e=>y("developmentalRisk",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("communication","Communication")}),(0,o.jsx)("div",{className:"space-y-2",children:j.map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"communication",value:e.value,checked:x.assessmentReview.communication===e.value,onChange:e=>y("communication",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("rehabPotential","Rehab Potential")}),(0,o.jsx)("div",{className:"space-y-2",children:N.map(e=>(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"rehabPotential",value:e.value,checked:x.assessmentReview.rehabPotential===e.value,onChange:e=>y("rehabPotential",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("vitalSigns","Vital Signs")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:p("bloodPressure","Blood Pressure (BP)")}),(0,o.jsx)("input",{type:"text",value:(null===(a=x.assessmentReview.vitalSigns)||void 0===a?void 0:a.bp)||"",onChange:e=>y("vitalSigns.bp",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"120/80"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:p("pulseRate","Pulse Rate (PR)")}),(0,o.jsx)("input",{type:"number",value:(null===(t=x.assessmentReview.vitalSigns)||void 0===t?void 0:t.pr)||"",onChange:e=>y("vitalSigns.pr",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"72"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:p("respiratoryRate","Respiratory Rate (RR)")}),(0,o.jsx)("input",{type:"number",value:(null===(r=x.assessmentReview.vitalSigns)||void 0===r?void 0:r.rr)||"",onChange:e=>y("vitalSigns.rr",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"16"})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:p("reviewOfSystems","Review of Systems")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[L("Respiratory","respiratory"),L("Neurology","neurology"),L("Gastrointestinal","gastrointestinal"),L("Genitourinary","genitourinary"),L("Musculoskeletal","musculoskeletal"),L("Hematology","hematology"),L("Cardiovascular","cardiovascular"),L("Integumentary (Skin)","integumentary"),L("Reproductive","reproductive"),L("Endocrine","endocrine")]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:p("boneHealthScreening","Bone Health Screening")}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:[["History of Falls","Crohn's/Ulcerative","Celiac Disease","Glucocorticoids >1 year","Bed Bound >6 months","Heterotrophic Ossification","Aromatase Inhibitors","Chemotherapy","None"].map(e=>{var a,t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=x.assessmentReview.boneHealthScreening)||void 0===a||null===(t=a.conditions)||void 0===t?void 0:t.includes(e))||!1,onChange:a=>{var t;const s=(null===(t=x.assessmentReview.boneHealthScreening)||void 0===t?void 0:t.conditions)||[];let r;r=a.target.checked?[...s,e]:s.filter(a=>a!==e),y("boneHealthScreening.conditions",r)},className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:p(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)}),(0,o.jsx)("div",{className:"col-span-full",children:(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(n=x.assessmentReview.boneHealthScreening)||void 0===n||null===(i=n.conditions)||void 0===i?void 0:i.includes("Others"))||!1,onChange:e=>{var a;const t=(null===(a=x.assessmentReview.boneHealthScreening)||void 0===a?void 0:a.conditions)||[];let s;s=e.target.checked?[...t,"Others"]:t.filter(e=>"Others"!==e),y("boneHealthScreening.conditions",s)},className:"mr-2"}),(0,o.jsxs)("span",{className:"text-sm text-gray-700 dark:text-gray-300 mr-2",children:[p("others","Others"),":"]}),(0,o.jsx)("input",{type:"text",placeholder:p("specify","Specify"),className:"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white",disabled:!(null!==(d=x.assessmentReview.boneHealthScreening)&&void 0!==d&&null!==(c=d.conditions)&&void 0!==c&&c.includes("Others"))})]})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:p("comments","Comments")}),(0,o.jsx)("textarea",{value:(null===(m=x.assessmentReview.boneHealthScreening)||void 0===m?void 0:m.comments)||"",onChange:e=>y("boneHealthScreening.comments",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:p("enterComments","Enter comments")})]})]})]})]})},m=e=>{let{selectedAreas:a=[],onAreaSelect:t,title:s,showLegend:n=!0}=e;const{t:i}=(0,l.o)(),d=(0,r.useRef)(null),[c,m]=(0,r.useState)(null),x={head:{label:i("head","Head"),color:"#FF6B6B",path:"M150,20 C170,20 180,30 180,50 C180,70 170,80 150,80 C130,80 120,70 120,50 C120,30 130,20 150,20 Z"},neck:{label:i("neck","Neck"),color:"#4ECDC4",path:"M140,80 L160,80 L160,100 L140,100 Z"},leftShoulder:{label:i("leftShoulder","Left Shoulder"),color:"#45B7D1",path:"M100,100 C110,95 120,100 130,110 L120,130 C110,125 100,120 90,115 Z"},rightShoulder:{label:i("rightShoulder","Right Shoulder"),color:"#45B7D1",path:"M200,100 C190,95 180,100 170,110 L180,130 C190,125 200,120 210,115 Z"},leftArm:{label:i("leftArm","Left Arm"),color:"#96CEB4",path:"M90,115 L80,180 L95,185 L105,120 Z"},rightArm:{label:i("rightArm","Right Arm"),color:"#96CEB4",path:"M210,115 L220,180 L205,185 L195,120 Z"},leftForearm:{label:i("leftForearm","Left Forearm"),color:"#FFEAA7",path:"M80,180 L70,240 L85,245 L95,185 Z"},rightForearm:{label:i("rightForearm","Right Forearm"),color:"#FFEAA7",path:"M220,180 L230,240 L215,245 L205,185 Z"},leftHand:{label:i("leftHand","Left Hand"),color:"#DDA0DD",path:"M70,240 L60,260 L75,265 L85,245 Z"},rightHand:{label:i("rightHand","Right Hand"),color:"#DDA0DD",path:"M230,240 L240,260 L225,265 L215,245 Z"},chest:{label:i("chest","Chest"),color:"#74B9FF",path:"M130,100 L170,100 L170,160 L130,160 Z"},abdomen:{label:i("abdomen","Abdomen"),color:"#A29BFE",path:"M130,160 L170,160 L170,220 L130,220 Z"},pelvis:{label:i("pelvis","Pelvis"),color:"#FD79A8",path:"M130,220 L170,220 L170,260 L130,260 Z"},leftThigh:{label:i("leftThigh","Left Thigh"),color:"#00B894",path:"M120,260 L140,260 L135,340 L115,340 Z"},rightThigh:{label:i("rightThigh","Right Thigh"),color:"#00B894",path:"M160,260 L180,260 L185,340 L165,340 Z"},leftKnee:{label:i("leftKnee","Left Knee"),color:"#E17055",path:"M115,340 L135,340 L135,360 L115,360 Z"},rightKnee:{label:i("rightKnee","Right Knee"),color:"#E17055",path:"M165,340 L185,340 L185,360 L165,360 Z"},leftLeg:{label:i("leftLeg","Left Leg"),color:"#FDCB6E",path:"M115,360 L135,360 L130,440 L110,440 Z"},rightLeg:{label:i("rightLeg","Right Leg"),color:"#FDCB6E",path:"M165,360 L185,360 L190,440 L170,440 Z"},leftFoot:{label:i("leftFoot","Left Foot"),color:"#6C5CE7",path:"M110,440 L130,440 L135,460 L105,460 Z"},rightFoot:{label:i("rightFoot","Right Foot"),color:"#6C5CE7",path:"M170,440 L190,440 L195,460 L165,460 Z"},upperBack:{label:i("upperBack","Upper Back"),color:"#00CEC9",path:"M130,100 L170,100 L170,180 L130,180 Z"},lowerBack:{label:i("lowerBack","Lower Back"),color:"#55A3FF",path:"M130,180 L170,180 L170,260 L130,260 Z"}},u=e=>{if(t){const s=a.includes(e)?a.filter(a=>a!==e):[...a,e];t(s)}},g=e=>a.includes(e);return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[s&&(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:s}),(0,o.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-700 dark:text-gray-300 mb-3 text-center",children:i("frontView","Front View")}),(0,o.jsx)("div",{className:"flex justify-center",children:(0,o.jsxs)("svg",{ref:d,width:"300",height:"480",viewBox:"0 0 300 480",className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700",children:[(0,o.jsx)("path",{d:"M150,20 C170,20 180,30 180,50 C180,70 170,80 150,80 C130,80 120,70 120,50 C120,30 130,20 150,20 Z M140,80 L160,80 L170,100 L200,100 C210,95 220,100 230,115 L220,180 L230,240 L240,260 L225,265 L215,245 L205,185 L195,120 L180,130 L170,110 L170,260 L180,260 L185,340 L185,360 L190,440 L195,460 L165,460 L170,440 L165,360 L165,340 L160,260 L140,260 L135,340 L135,360 L130,440 L135,460 L105,460 L110,440 L115,360 L115,340 L120,260 L130,260 L130,110 L120,130 L105,120 L95,185 L85,245 L75,265 L60,260 L70,240 L80,180 L90,115 C100,100 110,95 130,100 L140,80 Z",fill:"none",stroke:"#374151",strokeWidth:"2",className:"dark:stroke-gray-400"}),Object.entries(x).filter(e=>{let[a]=e;return!a.includes("Back")}).map(e=>{let[a,t]=e;return(0,o.jsxs)("g",{children:[(0,o.jsx)("path",{d:t.path,fill:g(a)?t.color:"transparent",stroke:t.color,strokeWidth:"2",opacity:g(a)?.8:c===a?.4:.2,className:"cursor-pointer transition-opacity duration-200",onClick:()=>u(a),onMouseEnter:()=>m(a),onMouseLeave:()=>m(null)}),c===a&&(0,o.jsx)("text",{x:"150",y:"10",textAnchor:"middle",className:"fill-gray-900 dark:fill-white text-sm font-medium",children:t.label})]},a)})]})})]}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("h4",{className:"text-md font-medium text-gray-700 dark:text-gray-300 mb-3 text-center",children:i("backView","Back View")}),(0,o.jsx)("div",{className:"flex justify-center",children:(0,o.jsxs)("svg",{width:"300",height:"480",viewBox:"0 0 300 480",className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700",children:[(0,o.jsx)("path",{d:"M150,20 C170,20 180,30 180,50 C180,70 170,80 150,80 C130,80 120,70 120,50 C120,30 130,20 150,20 Z M140,80 L160,80 L170,100 L170,260 L160,260 L140,260 L130,260 L130,100 L140,80 Z",fill:"none",stroke:"#374151",strokeWidth:"2",className:"dark:stroke-gray-400"}),Object.entries(x).filter(e=>{let[a]=e;return a.includes("Back")}).map(e=>{let[a,t]=e;return(0,o.jsxs)("g",{children:[(0,o.jsx)("path",{d:t.path,fill:g(a)?t.color:"transparent",stroke:t.color,strokeWidth:"2",opacity:g(a)?.8:c===a?.4:.2,className:"cursor-pointer transition-opacity duration-200",onClick:()=>u(a),onMouseEnter:()=>m(a),onMouseLeave:()=>m(null)}),c===a&&(0,o.jsx)("text",{x:"150",y:"10",textAnchor:"middle",className:"fill-gray-900 dark:fill-white text-sm font-medium",children:t.label})]},a)})]})})]})]}),a.length>0&&(0,o.jsxs)("div",{className:"mt-6",children:[(0,o.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("selectedAreas","Selected Areas"),":"]}),(0,o.jsx)("div",{className:"flex flex-wrap gap-2",children:a.map(e=>{var a;return(0,o.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:[null===(a=x[e])||void 0===a?void 0:a.label,(0,o.jsx)("button",{onClick:()=>u(e),className:"ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100",children:"\xd7"})]},e)})})]}),n&&(0,o.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,o.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[i("instructions","Instructions"),":"]}),(0,o.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1",children:[(0,o.jsxs)("li",{children:["\u2022 ",i("clickToSelectArea","Click on body areas to select/deselect them")]}),(0,o.jsxs)("li",{children:["\u2022 ",i("hoverForLabel","Hover over areas to see their labels")]}),(0,o.jsxs)("li",{children:["\u2022 ",i("multipleSelection","Multiple areas can be selected")]}),(0,o.jsxs)("li",{children:["\u2022 ",i("selectedAreasShown","Selected areas are highlighted and listed below")]})]})]})]})},x=e=>{var a,t,r,n,i,d,c,x,u,g,h,p,b;let{formData:y,updateFormData:v,errors:f,setErrors:k}=e;const{t:j,isRTL:N}=(0,l.o)(),w=(e,a)=>{const t=(0,s.A)({},y.musculoskeletalExam);if(e.includes(".")){const s=e.split(".");let r=t;for(let e=0;e<s.length-1;e++)r[s[e]]||(r[s[e]]={}),r=r[s[e]];r[s[s.length-1]]=a}else t[e]=a;v("musculoskeletalExam",t),f[e]&&k(a=>(0,s.A)((0,s.A)({},a),{},{[e]:null}))},R=[{value:"Normal",label:j("normal","Normal")},{value:"Impaired",label:j("impaired","Impaired")},{value:"Not Applicable",label:j("notApplicable","Not Applicable")}],C=[{value:"Present",label:j("present","Present")},{value:"Diminished",label:j("diminished","Diminished")},{value:"Not Palpable",label:j("notPalpable","Not Palpable")}],S=[{value:"Absent",label:j("absent","Absent")},{value:"Present",label:j("present","Present")},{value:"Not Applicable",label:j("notApplicable","Not Applicable")}],L=[{value:"Numeric Pain Scale",label:j("numericPainScale","Numeric Pain Scale")},{value:"Disability Pain Index",label:j("disabilityPainIndex","Disability Pain Index")},{value:"Wong Baker",label:j("wongBaker","Wong Baker")},{value:"Flacc",label:j("flacc","Flacc")}],I=[{value:"Nociceptive",label:j("nociceptive","Nociceptive")},{value:"Neuropathic",label:j("neuropathic","Neuropathic")},{value:"Mixed",label:j("mixed","Mixed")}],A=function(e,a){var t,s,r;let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:R,n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:e}),(0,o.jsx)("div",{className:"flex flex-wrap gap-4",children:l.map(e=>{var t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:a,value:e.value,checked:(null===(t=y.musculoskeletalExam[a])||void 0===t?void 0:t.status)===e.value,onChange:e=>w("".concat(a,".status"),e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})}),n&&("Impaired"===(null===(t=y.musculoskeletalExam[a])||void 0===t?void 0:t.status)||"Present"===(null===(s=y.musculoskeletalExam[a])||void 0===s?void 0:s.status))&&(0,o.jsxs)("div",{className:"mt-3",children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:j("comments","Comments")}),(0,o.jsx)("textarea",{value:(null===(r=y.musculoskeletalExam[a])||void 0===r?void 0:r.comments)||"",onChange:e=>w("".concat(a,".comments"),e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:j("enterComments","Enter comments")})]})]})};return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:j("musculoskeletalExaminationPain","Musculoskeletal Examination & Pain")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:j("physicalExaminationPainAssessment","Physical examination and pain assessment")})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[A(j("skinWarmthTurgor","Skin Warmth/Turgor"),"skinWarmthTurgor"),A(j("circulationPulsesByPalpation","Circulation: Pulses by Palpation"),"circulation",C)]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:j("edema","Edema")}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("div",{className:"flex flex-wrap gap-4",children:S.map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"edema",value:e.value,checked:(null===(a=y.musculoskeletalExam.edema)||void 0===a?void 0:a.status)===e.value,onChange:e=>w("edema.status",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})}),"Present"===(null===(a=y.musculoskeletalExam.edema)||void 0===a?void 0:a.status)&&(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("grade","Grade")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:["+1 Trace","+2 Moderate","+3 Deep","+4 Very deep, non-pitting"].map(e=>{var a,t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=y.musculoskeletalExam.edema)||void 0===a||null===(t=a.grade)||void 0===t?void 0:t.includes(e))||!1,onChange:a=>{var t;const s=(null===(t=y.musculoskeletalExam.edema)||void 0===t?void 0:t.grade)||[];let r;r=a.target.checked?[...s,e]:s.filter(a=>a!==e),w("edema.grade",r)},className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e})]},e)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:j("comments","Comments")}),(0,o.jsx)("textarea",{value:(null===(t=y.musculoskeletalExam.edema)||void 0===t?void 0:t.comments)||"",onChange:e=>w("edema.comments",e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:j("enterComments","Enter comments")})]})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:j("softTissuePalpation","Soft Tissue Palpation")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3",children:["Tenderness","Trigger Point","Swelling","Warmness","Muscles Spasm"].map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=y.musculoskeletalExam.softTissuePalpation)||void 0===a?void 0:a.includes(e))||!1,onChange:a=>((e,a,t)=>{const s=y.musculoskeletalExam[e]||[];let r;r=t?[...s,a]:s.filter(e=>e!==a),w(e,r)})("softTissuePalpation",e,a.target.checked),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:j(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:j("presenceOfWoundOrSkinBreakdown","Presence of Wound or Skin Breakdown")}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"woundPresence",value:"Yes",checked:null===(r=y.musculoskeletalExam.woundPresence)||void 0===r?void 0:r.hasWound,onChange:e=>w("woundPresence.hasWound","Yes"===e.target.value),className:"mr-2"}),j("yes","Yes")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"woundPresence",value:"No",checked:!(null!==(n=y.musculoskeletalExam.woundPresence)&&void 0!==n&&n.hasWound),onChange:e=>w("woundPresence.hasWound","Yes"===e.target.value),className:"mr-2"}),j("no","No")]})]}),(null===(i=y.musculoskeletalExam.woundPresence)||void 0===i?void 0:i.hasWound)&&(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("woundTypes","Wound Types")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:["Traumatic Wounds","Surgical Wounds","Scar Tissue","Pressure Ulcers","Diabetic Foot Ulcers","Post-Surgical Scar","Venous Ulcers","Fungal Infections","Burn (1st/2nd/3rd)"].map(e=>{var a,t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=y.musculoskeletalExam.woundPresence)||void 0===a||null===(t=a.types)||void 0===t?void 0:t.includes(e))||!1,onChange:a=>{var t;const s=(null===(t=y.musculoskeletalExam.woundPresence)||void 0===t?void 0:t.types)||[];let r;r=a.target.checked?[...s,e]:s.filter(a=>a!==e),w("woundPresence.types",r)},className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:j(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:j("comments","Comments")}),(0,o.jsx)("textarea",{value:(null===(d=y.musculoskeletalExam.woundPresence)||void 0===d?void 0:d.comments)||"",onChange:e=>w("woundPresence.comments",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:j("enterComments","Enter comments")})]})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:j("painAssessment","Pain Assessment")}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"painAssessment",value:"Yes",checked:null===(c=y.musculoskeletalExam.painAssessment)||void 0===c?void 0:c.hasPain,onChange:e=>w("painAssessment.hasPain","Yes"===e.target.value),className:"mr-2"}),j("yes","Yes")]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"painAssessment",value:"No",checked:!(null!==(x=y.musculoskeletalExam.painAssessment)&&void 0!==x&&x.hasPain),onChange:e=>w("painAssessment.hasPain","Yes"===e.target.value),className:"mr-2"}),j("no","No")]})]}),(null===(u=y.musculoskeletalExam.painAssessment)||void 0===u?void 0:u.hasPain)&&(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:j("painScore","Pain Score (0-10)")}),(0,o.jsx)("input",{type:"number",min:"0",max:"10",value:(null===(g=y.musculoskeletalExam.painAssessment)||void 0===g?void 0:g.painScore)||"",onChange:e=>w("painAssessment.painScore",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0-10"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:j("scale","Scale")}),(0,o.jsxs)("select",{value:(null===(h=y.musculoskeletalExam.painAssessment)||void 0===h?void 0:h.scale)||"",onChange:e=>w("painAssessment.scale",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:j("selectScale","Select scale")}),L.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("painType","Pain Type")}),(0,o.jsx)("div",{className:"flex flex-wrap gap-4",children:I.map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"painType",value:e.value,checked:(null===(a=y.musculoskeletalExam.painAssessment)||void 0===a?void 0:a.type)===e.value,onChange:e=>w("painAssessment.type",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})})]}),(0,o.jsx)("div",{children:(0,o.jsx)(m,{title:j("painLocation","Pain Location - Select affected areas"),selectedAreas:(null===(p=y.musculoskeletalExam.painAssessment)||void 0===p?void 0:p.bodyMapAreas)||[],onAreaSelect:e=>w("painAssessment.bodyMapAreas",e),showLegend:!0})}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("painOnset","Pain Onset")}),(0,o.jsx)("div",{className:"space-y-2",children:[{value:"New last 7 days",label:j("newLast7Days","New last 7 days")},{value:"Recent last 3 Mos",label:j("recentLast3Months","Recent last 3 months")},{value:"More Distant >3 Mos",label:j("moreDistant3Months","More distant >3 months")},{value:"Unknown",label:j("unknown","Unknown")}].map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"painOnset",value:e.value,checked:(null===(a=y.musculoskeletalExam.painAssessment)||void 0===a?void 0:a.onset)===e.value,onChange:e=>w("painAssessment.onset",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("painFrequency","Pain Frequency")}),(0,o.jsx)("div",{className:"space-y-2",children:[{value:"Constant",label:j("constant","Constant")},{value:"Intermittent",label:j("intermittent","Intermittent")},{value:"Occasional",label:j("occasional","Occasional")},{value:"Rare",label:j("rare","Rare")}].map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"painFrequency",value:e.value,checked:(null===(a=y.musculoskeletalExam.painAssessment)||void 0===a?void 0:a.frequency)===e.value,onChange:e=>w("painAssessment.frequency",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("painDescription","Pain Description")}),(0,o.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:["Knife-like","Throbbing","Burning","Aching","Sharp","Dull","Cramping","Shooting","Stabbing","Tingling","Numbness","Stiffness"].map(e=>{var a,t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=y.musculoskeletalExam.painAssessment)||void 0===a||null===(t=a.description)||void 0===t?void 0:t.includes(e))||!1,onChange:a=>{var t;const s=(null===(t=y.musculoskeletalExam.painAssessment)||void 0===t?void 0:t.description)||[];let r;r=a.target.checked?[...s,e]:s.filter(a=>a!==e),w("painAssessment.description",r)},className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:j(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("aggravatingFactors","Aggravating Factors")}),(0,o.jsx)("div",{className:"space-y-2",children:["Movement","Fatigue","Weather","Stress","Activity","Rest","Position changes"].map(e=>{var a,t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=y.musculoskeletalExam.painAssessment)||void 0===a||null===(t=a.aggravatingFactors)||void 0===t?void 0:t.includes(e))||!1,onChange:a=>{var t;const s=(null===(t=y.musculoskeletalExam.painAssessment)||void 0===t?void 0:t.aggravatingFactors)||[];let r;r=a.target.checked?[...s,e]:s.filter(a=>a!==e),w("painAssessment.aggravatingFactors",r)},className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:j(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-2",children:j("relievingFactors","Relieving Factors")}),(0,o.jsx)("div",{className:"space-y-2",children:["Cold","Heat","Rest","Medication","Exercise","Massage","Position change"].map(e=>{var a,t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(a=y.musculoskeletalExam.painAssessment)||void 0===a||null===(t=a.relievingFactors)||void 0===t?void 0:t.includes(e))||!1,onChange:a=>{var t;const s=(null===(t=y.musculoskeletalExam.painAssessment)||void 0===t?void 0:t.relievingFactors)||[];let r;r=a.target.checked?[...s,e]:s.filter(a=>a!==e),w("painAssessment.relievingFactors",r)},className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:j(e.toLowerCase().replace(/[^a-z]/g,""),e)})]},e)})}),(0,o.jsxs)("div",{className:"mt-3",children:[(0,o.jsx)("label",{className:"block text-sm text-gray-600 dark:text-gray-400 mb-1",children:j("describeReliefMethods","Describe relief methods")}),(0,o.jsx)("textarea",{value:(null===(b=y.musculoskeletalExam.painAssessment)||void 0===b?void 0:b.relievingDescription)||"",onChange:e=>w("painAssessment.relievingDescription",e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:j("describeWhatHelps","Describe what helps relieve the pain")})]})]})]})]})]})]})]})},u=e=>{var a,t,r;let{formData:n,updateFormData:i,errors:d,setErrors:c}=e;const{t:m}=(0,l.o)(),x=(e,a)=>{const t=(0,s.A)({},n.sensoryFunctions);if(e.includes(".")){const s=e.split(".");let r=t;for(let e=0;e<s.length-1;e++)r[s[e]]||(r[s[e]]={}),r=r[s[e]];r[s[s.length-1]]=a}else t[e]=a;i("sensoryFunctions",t),d[e]&&c(a=>(0,s.A)((0,s.A)({},a),{},{[e]:null}))},u=[{value:"Normal",label:m("normal","Normal")},{value:"Impaired",label:m("impaired","Impaired")},{value:"Need Further Assessment",label:m("needFurtherAssessment","Need Further Assessment")}],g=[{value:"Intact",label:m("intact","Intact")},{value:"Impaired",label:m("impaired","Impaired")},{value:"Not Applicable",label:m("notApplicable","Not Applicable")}],h=[{key:"pain",label:m("pain","Pain")},{key:"lightTouch",label:m("lightTouch","Light Touch")},{key:"sharpDull",label:m("sharpDull","Sharp-Dull")},{key:"temperature",label:m("temperature","Temperature")},{key:"stereognosis",label:m("stereognosis","Stereognosis")},{key:"proprioception",label:m("proprioception","Proprioception")}],p=[{key:"eyeContact",label:m("eyeContact","Eye Contact")},{key:"eyeToHand",label:m("eyeToHand","Eye to Hand")},{key:"fingerToNose",label:m("fingerToNose","Finger to Nose")},{key:"heelToShin",label:m("heelToShin","Heel to Shin")},{key:"rapidAlternatingMovements",label:m("rapidAlternatingMovements","Rapid Alternating Movements")},{key:"dysdiadochokinesia",label:m("dysdiadochokinesia","Dysdiadochokinesia")}],b=(e,a)=>{var t,s;return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:a}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:m("overallStatus","Overall Status")}),(0,o.jsx)("div",{className:"flex flex-wrap gap-4",children:u.map(a=>{var t;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"".concat(e,"Status"),value:a.value,checked:(null===(t=n.sensoryFunctions[e])||void 0===t?void 0:t.status)===a.value,onChange:a=>x("".concat(e,".status"),a.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:a.label})]},a.value)})})]}),("Impaired"===(null===(t=n.sensoryFunctions[e])||void 0===t?void 0:t.status)||"Need Further Assessment"===(null===(s=n.sensoryFunctions[e])||void 0===s?void 0:s.status))&&(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white",children:m("assessment","Assessment")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:m("left","Left")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:m("right","Right")})]})}),(0,o.jsx)("tbody",{children:h.map(a=>{var t,s,r,l,i,d;return(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-900 dark:text-white",children:a.label}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("input",{type:"text",value:(null===(t=n.sensoryFunctions[e])||void 0===t||null===(s=t.assessments)||void 0===s||null===(r=s[a.key])||void 0===r?void 0:r.left)||"",onChange:t=>x("".concat(e,".assessments.").concat(a.key,".left"),t.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:m("intactImpaired","Intact/Impaired")})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("input",{type:"text",value:(null===(l=n.sensoryFunctions[e])||void 0===l||null===(i=l.assessments)||void 0===i||null===(d=i[a.key])||void 0===d?void 0:d.right)||"",onChange:t=>x("".concat(e,".assessments.").concat(a.key,".right"),t.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:m("intactImpaired","Intact/Impaired")})})]},a.key)})})]})})]})};return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:m("sensoryFunctionsCoordination","Sensory Functions & Coordination")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:m("sensoryAssessmentCoordinationTests","Sensory assessment and coordination tests")})]}),b("upperBodySensory",m("upperBodySensoryFunctions","Upper Body Sensory Functions")),b("lowerBodySensory",m("lowerBodySensoryFunctions","Lower Body Sensory Functions")),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:m("coordinationVoluntaryMovement","Coordination and Voluntary Movement")}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:m("overallStatus","Overall Status")}),(0,o.jsx)("div",{className:"flex flex-wrap gap-4",children:u.map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"coordinationStatus",value:e.value,checked:(null===(a=n.sensoryFunctions.coordinationVoluntaryMovement)||void 0===a?void 0:a.status)===e.value,onChange:e=>x("coordinationVoluntaryMovement.status",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})})]}),("Impaired"===(null===(a=n.sensoryFunctions.coordinationVoluntaryMovement)||void 0===a?void 0:a.status)||"Need Further Assessment"===(null===(t=n.sensoryFunctions.coordinationVoluntaryMovement)||void 0===t?void 0:t.status))&&(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map(e=>(0,o.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e.label}),(0,o.jsx)("div",{className:"flex flex-wrap gap-3",children:g.map(a=>{var t,s;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"coordination_".concat(e.key),value:a.value,checked:(null===(t=n.sensoryFunctions.coordinationVoluntaryMovement)||void 0===t||null===(s=t.assessments)||void 0===s?void 0:s[e.key])===a.value,onChange:a=>x("coordinationVoluntaryMovement.assessments.".concat(e.key),a.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:a.label})]},a.value)})})]},e.key))}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:m("comments","Comments")}),(0,o.jsx)("textarea",{value:(null===(r=n.sensoryFunctions.coordinationVoluntaryMovement)||void 0===r?void 0:r.comments)||"",onChange:e=>x("coordinationVoluntaryMovement.comments",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:m("enterComments","Enter comments")})]})]})]})]})},g=e=>{let{formData:a,updateFormData:t,errors:r,setErrors:n}=e;const{t:i}=(0,l.o)(),d=(e,l)=>{const i=(0,s.A)({},a.neuromusculoskeletal);if(e.includes(".")){const a=e.split(".");let t=i;for(let e=0;e<a.length-1;e++)t[a[e]]||(t[a[e]]={}),t=t[a[e]];t[a[a.length-1]]=l}else i[e]=l;t("neuromusculoskeletal",i),r[e]&&n(a=>(0,s.A)((0,s.A)({},a),{},{[e]:null}))},c=[{value:"0",label:"0 - No contraction"},{value:"1",label:"1 - Trace contraction"},{value:"2",label:"2 - Poor (gravity eliminated)"},{value:"3",label:"3 - Fair (against gravity)"},{value:"4",label:"4 - Good (against resistance)"},{value:"5",label:"5 - Normal"},{value:"N/A",label:"N/A - Not Applicable"}],m=[{value:"0",label:"0 - Flaccid"},{value:"1",label:"1 - Hypotonic"},{value:"2",label:"2 - Normal"},{value:"3",label:"3 - Hypertonic"},{value:"4",label:"4 - Rigid"},{value:"N/A",label:"N/A - Not Applicable"}],x={neck:{title:i("neck","Neck"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"60\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"75\xb0"},{key:"lateralFlexionL",label:i("lateralFlexionLeft","Lateral Flexion (L)"),normalRange:"45\xb0"},{key:"lateralFlexionR",label:i("lateralFlexionRight","Lateral Flexion (R)"),normalRange:"45\xb0"},{key:"rotationL",label:i("rotationLeft","Rotation (L)"),normalRange:"80\xb0"},{key:"rotationR",label:i("rotationRight","Rotation (R)"),normalRange:"80\xb0"}]},shoulder:{title:i("shoulder","Shoulder"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"180\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"60\xb0"},{key:"abduction",label:i("abduction","Abduction"),normalRange:"180\xb0"},{key:"adduction",label:i("adduction","Adduction"),normalRange:"50\xb0"},{key:"internalRotation",label:i("internalRotation","Internal Rotation"),normalRange:"90\xb0"},{key:"externalRotation",label:i("externalRotation","External Rotation"),normalRange:"90\xb0"}]},elbow:{title:i("elbow","Elbow"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"150\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"0\xb0"}]},forearm:{title:i("forearm","Forearm"),movements:[{key:"pronation",label:i("pronation","Pronation"),normalRange:"90\xb0"},{key:"supination",label:i("supination","Supination"),normalRange:"90\xb0"}]},wrist:{title:i("wrist","Wrist"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"90\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"70\xb0"},{key:"radialDeviation",label:i("radialDeviation","Radial Deviation"),normalRange:"25\xb0"},{key:"ulnarDeviation",label:i("ulnarDeviation","Ulnar Deviation"),normalRange:"35\xb0"}]},fingers:{title:i("fingers","Fingers"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"90\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"0\xb0"},{key:"abduction",label:i("abduction","Abduction"),normalRange:"25\xb0"},{key:"adduction",label:i("adduction","Adduction"),normalRange:"0\xb0"}]},trunk:{title:i("trunk","Trunk"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"80\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"30\xb0"},{key:"lateralFlexionL",label:i("lateralFlexionLeft","Lateral Flexion (L)"),normalRange:"40\xb0"},{key:"lateralFlexionR",label:i("lateralFlexionRight","Lateral Flexion (R)"),normalRange:"40\xb0"},{key:"rotationL",label:i("rotationLeft","Rotation (L)"),normalRange:"45\xb0"},{key:"rotationR",label:i("rotationRight","Rotation (R)"),normalRange:"45\xb0"}]},hip:{title:i("hip","Hip"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"120\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"30\xb0"},{key:"abduction",label:i("abduction","Abduction"),normalRange:"45\xb0"},{key:"adduction",label:i("adduction","Adduction"),normalRange:"30\xb0"},{key:"internalRotation",label:i("internalRotation","Internal Rotation"),normalRange:"45\xb0"},{key:"externalRotation",label:i("externalRotation","External Rotation"),normalRange:"45\xb0"}]},knee:{title:i("knee","Knee"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"135\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"0\xb0"}]},ankle:{title:i("ankle","Ankle"),movements:[{key:"dorsiflexion",label:i("dorsiflexion","Dorsiflexion"),normalRange:"20\xb0"},{key:"plantarflexion",label:i("plantarflexion","Plantarflexion"),normalRange:"50\xb0"},{key:"inversion",label:i("inversion","Inversion"),normalRange:"35\xb0"},{key:"eversion",label:i("eversion","Eversion"),normalRange:"15\xb0"}]},thoracoLumbar:{title:i("thoracoLumbar","Thoraco-Lumbar"),movements:[{key:"flexion",label:i("flexion","Flexion"),normalRange:"80\xb0"},{key:"extension",label:i("extension","Extension"),normalRange:"30\xb0"},{key:"lateralFlexion",label:i("lateralFlexion","Lateral Flexion"),normalRange:"40\xb0"},{key:"rotation",label:i("rotation","Rotation"),normalRange:"45\xb0"}]}},u=(e,r)=>(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:r.title}),(0,o.jsx)("button",{onClick:()=>(e=>{const r=x[e],l=(0,s.A)({},a.neuromusculoskeletal);l[e]||(l[e]={}),r.movements.forEach(a=>{l[e][a.key]||(l[e][a.key]={});const t=a.normalRange.replace("\xb0","");l[e][a.key]={arom:t,prom:t,muscleTestL:"5",muscleTestR:"5",muscleToneL:"2",muscleToneR:"2"}}),t("neuromusculoskeletal",l)})(e),className:"px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors",children:i("fillNormal","Fill Normal")})]}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-left text-sm font-medium text-gray-900 dark:text-white",children:i("movement","Movement")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:i("normalRange","Normal Range")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:"AROM"}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:"PROM"}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:i("muscleTestL","Muscle Test (L)")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:i("muscleTestR","Muscle Test (R)")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:i("muscleToneL","Muscle Tone (L)")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:i("muscleToneR","Muscle Tone (R)")})]})}),(0,o.jsx)("tbody",{children:r.movements.map(t=>{var s,r,l,n,x,u,g,h,p,b,y,v;return(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm text-gray-900 dark:text-white",children:t.label}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm text-gray-600 dark:text-gray-400",children:t.normalRange}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("input",{type:"number",min:"0",max:"180",value:(null===(s=a.neuromusculoskeletal[e])||void 0===s||null===(r=s[t.key])||void 0===r?void 0:r.arom)||"",onChange:a=>d("".concat(e,".").concat(t.key,".arom"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0-180"})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("input",{type:"number",min:"0",max:"180",value:(null===(l=a.neuromusculoskeletal[e])||void 0===l||null===(n=l[t.key])||void 0===n?void 0:n.prom)||"",onChange:a=>d("".concat(e,".").concat(t.key,".prom"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0-180"})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsxs)("select",{value:(null===(x=a.neuromusculoskeletal[e])||void 0===x||null===(u=x[t.key])||void 0===u?void 0:u.muscleTestL)||"",onChange:a=>d("".concat(e,".").concat(t.key,".muscleTestL"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:i("select","Select")}),c.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))]})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsxs)("select",{value:(null===(g=a.neuromusculoskeletal[e])||void 0===g||null===(h=g[t.key])||void 0===h?void 0:h.muscleTestR)||"",onChange:a=>d("".concat(e,".").concat(t.key,".muscleTestR"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:i("select","Select")}),c.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))]})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsxs)("select",{value:(null===(p=a.neuromusculoskeletal[e])||void 0===p||null===(b=p[t.key])||void 0===b?void 0:b.muscleToneL)||"",onChange:a=>d("".concat(e,".").concat(t.key,".muscleToneL"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:i("select","Select")}),m.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))]})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsxs)("select",{value:(null===(y=a.neuromusculoskeletal[e])||void 0===y||null===(v=y[t.key])||void 0===v?void 0:v.muscleToneR)||"",onChange:a=>d("".concat(e,".").concat(t.key,".muscleToneR"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"",children:i("select","Select")}),m.map(e=>(0,o.jsx)("option",{value:e.value,children:e.label},e.value))]})})]},t.key)})})]})})]},e);return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:i("neuromusculoskeletalMovementFunctions","Neuromusculoskeletal & Movement Functions")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:i("rangeOfMotionMuscleTestTone","Range of Motion, Muscle Test, and Muscle Tone assessments")})]}),(0,o.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-blue-900 dark:text-blue-100 mb-4",children:i("assessmentParameters","Assessment Parameters")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"AROM"}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:i("activeRangeOfMotion","Active Range of Motion")})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"PROM"}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:i("passiveRangeOfMotion","Passive Range of Motion")})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:i("muscleTest","Muscle Test")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:i("muscleStrengthScale","0-5 Scale (Manual Muscle Testing)")})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:i("muscleTone","Muscle Tone")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:i("muscleToneScale","0-4 Scale (Modified Ashworth)")})]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:i("upperExtremity","Upper Extremity")}),(0,o.jsx)("div",{className:"space-y-6",children:Object.entries(x).filter(e=>{let[a]=e;return["neck","shoulder","elbow","forearm","wrist","fingers"].includes(a)}).map(e=>{let[a,t]=e;return u(a,t)})})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:i("lowerExtremityTrunk","Lower Extremity & Trunk")}),(0,o.jsx)("div",{className:"space-y-6",children:Object.entries(x).filter(e=>{let[a]=e;return["trunk","hip","knee","ankle","thoracoLumbar"].includes(a)}).map(e=>{let[a,t]=e;return u(a,t)})})]})]})},h=e=>{var a;let{formData:t,updateFormData:r,errors:n,setErrors:i}=e;const{t:d}=(0,l.o)(),c=(e,a)=>{const l=(0,s.A)({},t.neurodynamicsReflexes);if(e.includes(".")){const t=e.split(".");let s=l;for(let e=0;e<t.length-1;e++)s[t[e]]||(s[t[e]]={}),s=s[t[e]];s[t[t.length-1]]=a}else l[e]=a;r("neurodynamicsReflexes",l),n[e]&&i(a=>(0,s.A)((0,s.A)({},a),{},{[e]:null}))},m=[{key:"slr",label:"SLR (Straight Leg Raise)"},{key:"slump",label:"Slump Test"},{key:"pkb",label:"PKB (Prone Knee Bend)"},{key:"ulnt1",label:"ULNT1 (Median Nerve)"},{key:"ulnt2",label:"ULNT2 (Median Nerve)"},{key:"ulnt3",label:"ULNT3 (Radial Nerve)"},{key:"ulnt4",label:"ULNT4 (Ulnar Nerve)"},{key:"others",label:d("others","Others")}],x=[{value:"Normal",label:d("normal","Normal")},{value:"Impaired",label:d("impaired","Impaired")}],u=[{value:"+",label:"+"},{value:"-",label:"-"},{value:"Hyper",label:d("hyper","Hyper")},{value:"Hypo",label:d("hypo","Hypo")}];return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:d("neurodynamicsReflexesFunctionalEvaluation","Neurodynamics, Reflexes & Functional Evaluation")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:d("neurodynamicsReflexesGaitAssessment","Neurodynamics tests, reflexes, and functional gait assessment")})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("neurodynamicsTests","Neurodynamics Tests")}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white",children:d("test","Test")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:d("leftRange","L Range")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:d("leftSymptom","L Symptom")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:d("rightRange","R Range")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:d("rightSymptom","R Symptom")})]})}),(0,o.jsx)("tbody",{children:m.map(e=>{var a,s,r,l,n,i,m,x,u,g,h,p;return(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-900 dark:text-white",children:e.label}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("input",{type:"text",value:(null===(a=t.neurodynamicsReflexes.neurodynamicsTests)||void 0===a||null===(s=a[e.key])||void 0===s?void 0:s.lRange)||"",onChange:a=>c("neurodynamicsTests.".concat(e.key,".lRange"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("range","Range")})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"".concat(e.key,"_lSymptom"),value:"+",checked:"+"===(null===(r=t.neurodynamicsReflexes.neurodynamicsTests)||void 0===r||null===(l=r[e.key])||void 0===l?void 0:l.lSymptom),onChange:a=>c("neurodynamicsTests.".concat(e.key,".lSymptom"),a.target.value),className:"mr-1"}),"+"]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"".concat(e.key,"_lSymptom"),value:"-",checked:"-"===(null===(n=t.neurodynamicsReflexes.neurodynamicsTests)||void 0===n||null===(i=n[e.key])||void 0===i?void 0:i.lSymptom),onChange:a=>c("neurodynamicsTests.".concat(e.key,".lSymptom"),a.target.value),className:"mr-1"}),"-"]})]})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("input",{type:"text",value:(null===(m=t.neurodynamicsReflexes.neurodynamicsTests)||void 0===m||null===(x=m[e.key])||void 0===x?void 0:x.rRange)||"",onChange:a=>c("neurodynamicsTests.".concat(e.key,".rRange"),a.target.value),className:"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:d("range","Range")})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"".concat(e.key,"_rSymptom"),value:"+",checked:"+"===(null===(u=t.neurodynamicsReflexes.neurodynamicsTests)||void 0===u||null===(g=u[e.key])||void 0===g?void 0:g.rSymptom),onChange:a=>c("neurodynamicsTests.".concat(e.key,".rSymptom"),a.target.value),className:"mr-1"}),"+"]}),(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"".concat(e.key,"_rSymptom"),value:"-",checked:"-"===(null===(h=t.neurodynamicsReflexes.neurodynamicsTests)||void 0===h||null===(p=h[e.key])||void 0===p?void 0:p.rSymptom),onChange:a=>c("neurodynamicsTests.".concat(e.key,".rSymptom"),a.target.value),className:"mr-1"}),"-"]})]})})]},e.key)})})]})})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:d("reflexes","Reflexes")}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:d("overallStatus","Overall Status")}),(0,o.jsx)("div",{className:"flex flex-wrap gap-4",children:x.map(e=>{var a;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"radio",name:"reflexStatus",value:e.value,checked:(null===(a=t.neurodynamicsReflexes.reflexes)||void 0===a?void 0:a.status)===e.value,onChange:e=>c("reflexes.status",e.target.value),className:"mr-2"}),(0,o.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})})]}),"Impaired"===(null===(a=t.neurodynamicsReflexes.reflexes)||void 0===a?void 0:a.status)&&(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white",children:d("reflex","Reflex")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:d("right","Right")}),(0,o.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white",children:d("left","Left")})]})}),(0,o.jsx)("tbody",{children:[{key:"btr",label:"BTR (Biceps Tendon Reflex)"},{key:"ttr",label:"TTR (Triceps Tendon Reflex)"},{key:"ktr",label:"KTR (Knee Tendon Reflex)"},{key:"atr",label:"ATR (Achilles Tendon Reflex)"},{key:"babinsky",label:"Babinsky"},{key:"hoffmanns",label:"Hoffmann's"},{key:"clonus",label:"Clonus"}].map(e=>(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-900 dark:text-white",children:e.label}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(a=>{var s,r,l,n;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(s=t.neurodynamicsReflexes.reflexes)||void 0===s||null===(r=s.assessments)||void 0===r||null===(l=r[e.key])||void 0===l||null===(n=l.right)||void 0===n?void 0:n.includes(a.value))||!1,onChange:s=>{var r,l,n;const i=(null===(r=t.neurodynamicsReflexes.reflexes)||void 0===r||null===(l=r.assessments)||void 0===l||null===(n=l[e.key])||void 0===n?void 0:n.right)||[];let o;o=s.target.checked?[...i,a.value]:i.filter(e=>e!==a.value),c("reflexes.assessments.".concat(e.key,".right"),o)},className:"mr-1"}),(0,o.jsx)("span",{className:"text-xs",children:a.label})]},a.value)})})}),(0,o.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-2 py-2",children:(0,o.jsx)("div",{className:"flex flex-wrap gap-2",children:u.map(a=>{var s,r,l,n;return(0,o.jsxs)("label",{className:"flex items-center",children:[(0,o.jsx)("input",{type:"checkbox",checked:(null===(s=t.neurodynamicsReflexes.reflexes)||void 0===s||null===(r=s.assessments)||void 0===r||null===(l=r[e.key])||void 0===l||null===(n=l.left)||void 0===n?void 0:n.includes(a.value))||!1,onChange:s=>{var r,l,n;const i=(null===(r=t.neurodynamicsReflexes.reflexes)||void 0===r||null===(l=r.assessments)||void 0===l||null===(n=l[e.key])||void 0===n?void 0:n.left)||[];let o;o=s.target.checked?[...i,a.value]:i.filter(e=>e!==a.value),c("reflexes.assessments.".concat(e.key,".left"),o)},className:"mr-1"}),(0,o.jsx)("span",{className:"text-xs",children:a.label})]},a.value)})})})]},e.key))})]})})]})]})},p=e=>{let{formData:a,updateFormData:t,errors:s,setErrors:r}=e;const{t:n,isRTL:i}=(0,l.o)();return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:n("recommendedEquipmentICF","Recommended Equipment & ICF")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:n("assistiveTechnologyICFFramework","Assistive technology recommendations and ICF framework assessment")})]}),(0,o.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-info-circle text-blue-500 mr-3"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-blue-900 dark:text-blue-100",children:n("pageInDevelopment","Page In Development")}),(0,o.jsx)("p",{className:"text-blue-700 dark:text-blue-300 mt-1",children:n("equipmentICFPageDescription","This page will include assistive technology recommendations and comprehensive ICF (International Classification of Functioning, Disability and Health) framework assessment.")})]})]})}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("recommendedEquipmentAssistiveTechnology","Recommended Equipment/Assistive Technology")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willIncludeEquipmentCategories","Will include equipment categories:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsxs)("li",{children:[n("walkingAids","Walking Aids")," (Axillary crutches, etc.)"]}),(0,o.jsxs)("li",{children:[n("wheelchairs","Wheelchairs")," (Adult/Child, various types)"]}),(0,o.jsx)("li",{children:n("standingFrames","Standing Frames")}),(0,o.jsx)("li",{children:n("lowerLimbProstheses","Lower Limb Prostheses")}),(0,o.jsx)("li",{children:n("upperLimbProstheses","Upper Limb Prostheses")}),(0,o.jsx)("li",{children:n("spinalOrthoses","Spinal Orthoses")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("icfFramework","ICF Framework")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willIncludeICFComponents","Will include ICF components:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsx)("li",{children:n("healthCondition","Health Condition")}),(0,o.jsx)("li",{children:n("bodyStructureFunction","Body Structure/Function (Impairment)")}),(0,o.jsx)("li",{children:n("limitationsOfActivity","Limitations of Activity")}),(0,o.jsx)("li",{children:n("restrictionOfParticipation","Restriction of Participation")}),(0,o.jsx)("li",{children:n("environmentalPersonalFactors","Environmental & Personal Factors")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("equipmentSpecifications","Equipment Specifications")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willIncludeSpecificationFields","Will include specification fields:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsxs)("li",{children:[n("usageStatus","Usage Status")," (Used/Not Used)"]}),(0,o.jsx)("li",{children:n("technicalSpecifications","Technical Specifications")}),(0,o.jsx)("li",{children:n("recommendedUse","Recommended Use")}),(0,o.jsx)("li",{children:n("reviewedWithPhysician","Reviewed with Physician")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("environmentalFactors","Environmental Factors")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willAssessEnvironmentalFactors","Will assess environmental factors:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsx)("li",{children:n("physicalEnvironment","Physical Environment")}),(0,o.jsx)("li",{children:n("homeAssessment","Home Assessment")}),(0,o.jsx)("li",{children:n("transportation","Transportation")}),(0,o.jsx)("li",{children:n("workEnvironment","Work Environment")}),(0,o.jsx)("li",{children:n("socialSupport","Social Support")}),(0,o.jsx)("li",{children:n("attitudesOfOthers","Attitudes of Others")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 lg:col-span-2",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("icfAssessmentAreas","ICF Assessment Areas")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("div",{className:"bg-blue-100 dark:bg-blue-900/30 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-blue-900 dark:text-blue-100",children:n("bodyFunctions","Body Functions")}),(0,o.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:n("physiologicalFunctions","Physiological functions of body systems")})]})}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("div",{className:"bg-green-100 dark:bg-green-900/30 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-green-900 dark:text-green-100",children:n("bodyStructures","Body Structures")}),(0,o.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:n("anatomicalParts","Anatomical parts of the body")})]})}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("div",{className:"bg-yellow-100 dark:bg-yellow-900/30 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-yellow-900 dark:text-yellow-100",children:n("activities","Activities")}),(0,o.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:n("executionOfTasks","Execution of tasks or actions")})]})}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("div",{className:"bg-purple-100 dark:bg-purple-900/30 rounded-lg p-3",children:[(0,o.jsx)("h4",{className:"font-medium text-purple-900 dark:text-purple-100",children:n("participation","Participation")}),(0,o.jsx)("p",{className:"text-sm text-purple-700 dark:text-purple-300",children:n("involvementInLifeSituations","Involvement in life situations")})]})})]})]})]})]})},b=e=>{let{formData:a,updateFormData:t,errors:s,setErrors:r}=e;const{t:n,isRTL:i}=(0,l.o)();return(0,o.jsxs)("div",{className:"p-6 space-y-8",children:[(0,o.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:n("signaturesFinal","Signatures & Final")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:n("finalCommentsSignatures","Final comments, patient concerns, and professional signatures")})]}),(0,o.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-info-circle text-blue-500 mr-3"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-blue-900 dark:text-blue-100",children:n("pageInDevelopment","Page In Development")}),(0,o.jsx)("p",{className:"text-blue-700 dark:text-blue-300 mt-1",children:n("signaturesPageDescription","This page will include therapist and physician signatures, area comments, patient concerns/expectations, and family preferences.")})]})]})}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("professionalSignatures","Professional Signatures")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willIncludeSignatureFields","Will include signature fields for:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsxs)("li",{children:[n("therapistSignature","Therapist Signature")," & ",n("date","Date")]}),(0,o.jsxs)("li",{children:[n("physicianSignature","Physician Signature")," & ",n("date","Date")]}),(0,o.jsx)("li",{children:n("digitalSignatureSupport","Digital signature support")}),(0,o.jsx)("li",{children:n("signatureValidation","Signature validation")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("areaComments","Area Comments")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willIncludeCommentFields","Will include comment fields for:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsx)("li",{children:n("generalComments","General comments")}),(0,o.jsx)("li",{children:n("clinicalObservations","Clinical observations")}),(0,o.jsx)("li",{children:n("recommendationsNotes","Recommendations and notes")}),(0,o.jsx)("li",{children:n("followUpInstructions","Follow-up instructions")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("patientConcernsExpectations","Patient Concerns/Expectations")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willCapturePatientInput","Will capture patient input on:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsx)("li",{children:n("mainConcerns","Main concerns")}),(0,o.jsx)("li",{children:n("treatmentExpectations","Treatment expectations")}),(0,o.jsx)("li",{children:n("functionalGoals","Functional goals")}),(0,o.jsx)("li",{children:n("qualityOfLifeGoals","Quality of life goals")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("patientFamilyPreferences","Patient & Family Preferences")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("willDocumentPreferences","Will document preferences for:")}),(0,o.jsxs)("ul",{className:"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1",children:[(0,o.jsx)("li",{children:n("treatmentApproach","Treatment approach")}),(0,o.jsx)("li",{children:n("schedulingPreferences","Scheduling preferences")}),(0,o.jsx)("li",{children:n("communicationPreferences","Communication preferences")}),(0,o.jsx)("li",{children:n("culturalConsiderations","Cultural considerations")})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 lg:col-span-2",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-3",children:n("assessmentCompletion","Assessment Completion")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("div",{className:"bg-green-100 dark:bg-green-900/30 rounded-lg p-4",children:[(0,o.jsx)("i",{className:"fas fa-check-circle text-green-500 text-2xl mb-2"}),(0,o.jsx)("h4",{className:"font-medium text-green-900 dark:text-green-100",children:n("validation","Validation")}),(0,o.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300",children:n("formValidationChecks","Form validation and completeness checks")})]})}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("div",{className:"bg-blue-100 dark:bg-blue-900/30 rounded-lg p-4",children:[(0,o.jsx)("i",{className:"fas fa-file-pdf text-blue-500 text-2xl mb-2"}),(0,o.jsx)("h4",{className:"font-medium text-blue-900 dark:text-blue-100",children:n("pdfGeneration","PDF Generation")}),(0,o.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:n("generatePrintableAssessment","Generate printable assessment report")})]})}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsxs)("div",{className:"bg-purple-100 dark:bg-purple-900/30 rounded-lg p-4",children:[(0,o.jsx)("i",{className:"fas fa-save text-purple-500 text-2xl mb-2"}),(0,o.jsx)("h4",{className:"font-medium text-purple-900 dark:text-purple-100",children:n("dataStorage","Data Storage")}),(0,o.jsx)("p",{className:"text-sm text-purple-700 dark:text-purple-300",children:n("secureDataStorageBackup","Secure data storage and backup")})]})})]})]})]}),(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("assessmentSummary","Assessment Summary")}),(0,o.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-center",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:"8"}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("totalPages","Total Pages")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:"200+"}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("dataFields","Data Fields")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",children:"15+"}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("assessmentSections","Assessment Sections")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:"CARF"}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("compliant","Compliant")})]})]})]})]})},y=e=>{var a;let{patientId:t,patientData:m,fromPatientProfile:y,initialData:v={},onSave:f,onCancel:k}=e;const{t:j,isRTL:N}=(0,l.o)(),w=(0,n.Zp)(),{patientId:R,assessmentId:C}=(0,n.g)(),[S,L]=(0,r.useState)(1),[I,A]=(0,r.useState)(!1),[F,T]=(0,r.useState)({}),[E,P]=(0,r.useState)(null),M=t||R,[D,O]=(0,r.useState)({patientInfo:{name:"",sex:"",maritalStatus:"",patientId:"",nationality:"",age:"",fileNumber:"",dateOfEval:(new Date).toISOString().split("T")[0],time:(new Date).toTimeString().slice(0,5),referredByHospital:"",jobOccupation:[],educationLevel:"",livingCondition:{typeOfResidence:"",socialSupport:[]},diagnosis:"",icd10Code:"",chiefComplaint:"",associatedMedicalIssues:"",historyOfTrauma:{date:"",circumstances:""},previousSurgery:{hasSurgery:!1,type:""},radiologicalInvestigation:{hasInvestigation:!1,findings:""},previousRehabIntervention:{hasIntervention:!1,details:""},currentEquipment:{hasEquipment:!1,details:""},precautions:"",pastMedicalHistory:[],medications:"",effectsSideEffects:"",familyHistory:"",allergies:"",sourceOfInformation:[]},assessmentReview:{skinAppearance:{status:"",comments:""},mentalFunctions:{status:"",comments:""},visualScreening:{status:"",comments:""},hearingFunction:{status:"",comments:""},nutritionalRisk:[],fallRisk:"",psychosocialBehaviorRisk:"",developmentalRisk:"",communication:"",rehabPotential:"",vitalSigns:{bp:"",pr:"",rr:""},reviewOfSystems:{respiratory:{complaints:[],explain:""},neurology:{complaints:[],explain:""},gastrointestinal:{complaints:[],explain:""},genitourinary:{complaints:[],explain:""},musculoskeletal:{complaints:[],explain:""},hematology:{complaints:[],explain:""},cardiovascular:{complaints:[],explain:""},integumentary:{complaints:[],explain:""},reproductive:{complaints:[],explain:""},endocrine:{complaints:[],explain:""}},boneHealthScreening:{conditions:[],comments:""}},musculoskeletalExam:{skinWarmthTurgor:{status:"",comments:""},circulation:{status:"",comments:""},edema:{status:"",grade:[],comments:""},softTissuePalpation:[],woundPresence:{hasWound:!1,types:[],comments:""},woundAssessment:{needsAssessment:!1,onsetDate:"",location:"",classification:"",clinicalImpression:"",conclusion:""},eligibility:{status:"",reasons:"",alternativeServices:"",recommendations:"",referral:"",precaution:"",instructions:""},treatmentFrequency:{sessions:"",timesPerWeek:"",duration:"",oneVisit:!1,anticipatedDischarge:""},jointPathology:{conditions:[],jointIntegrity:[],comments:"",deformityTypes:"",deformitySite:""},painAssessment:{hasPain:!1,painScore:"",scale:"",type:"",location:[],onset:"",description:[],frequency:"",aggravatingFactors:[],relievingFactors:[],relievingDescription:"",pattern:[],associatedSymptoms:[]}},sensoryFunctions:{upperBodySensory:{status:"",assessments:{pain:{left:"",right:""},lightTouch:{left:"",right:""},sharpDull:{left:"",right:""},temperature:{left:"",right:""},stereognosis:{left:"",right:""},proprioception:{left:"",right:""}}},lowerBodySensory:{status:"",assessments:{pain:{left:"",right:""},lightTouch:{left:"",right:""},sharpDull:{left:"",right:""},temperature:{left:"",right:""},stereognosis:{left:"",right:""},proprioception:{left:"",right:""}}},coordinationVoluntaryMovement:{status:"",assessments:{eyeContact:"",eyeToHand:"",fingerToNose:"",heelToShin:"",rapidAlternatingMovements:"",dysdiadochokinesia:""},comments:""}},neuromusculoskeletal:{neck:{flexion:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},extension:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},lateralFlexionL:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},lateralFlexionR:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},rotationL:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},rotationR:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""}},shoulder:{flexion:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},extension:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},abduction:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},adduction:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},internalRotation:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""},externalRotation:{arom:"",prom:"",muscleTestL:"",muscleTestR:"",muscleToneL:"",muscleToneR:""}}},neurodynamicsReflexes:{neurodynamicsTests:{slr:{lRange:"",lSymptom:"",rRange:"",rSymptom:""},slump:{lRange:"",lSymptom:"",rRange:"",rSymptom:""},pkb:{lRange:"",lSymptom:"",rRange:"",rSymptom:""},ulnt1:{lRange:"",lSymptom:"",rRange:"",rSymptom:""},ulnt2:{lRange:"",lSymptom:"",rRange:"",rSymptom:""},ulnt3:{lRange:"",lSymptom:"",rRange:"",rSymptom:""},ulnt4:{lRange:"",lSymptom:"",rRange:"",rSymptom:""},others:{lRange:"",lSymptom:"",rRange:"",rSymptom:""}},reflexes:{status:"",assessments:{btr:{right:"",left:""},ttr:{right:"",left:""},ktr:{right:"",left:""},atr:{right:"",left:""},babinsky:{right:"",left:""},hoffmanns:{right:"",left:""},clonus:{right:"",left:""}}},posturalAssessment:{status:"",findings:[],comments:""},functionalMobility:{status:"",comments:""},balanceAssessment:{tests:[],status:"",score:"",dynamicStatic:"",comments:"",fallRisk:""},coordination:{upperLimbs:{status:"",sides:[],comments:""},lowerLimbs:{status:"",sides:[],comments:""}},gaitAnalysis:{patterns:[],mobilityTests:[],score:"",functionalQuality:{safety:"",cadence:"",speed:"",fatigue:""}},assessmentTools:{fim:{score:""},oswestry:{score:""},lefs:{score:""},uefi:{score:""},kneeInjury:{score:""},others:{name:"",score:""}}},equipmentICF:{recommendedEquipment:{usageStatus:"",walkingAids:[],wheelchairs:[],other:[],lowerLimbProstheses:[],upperLimbProstheses:[],spinalOrthoses:[],technicalSpecifications:"",recommendedUse:"",reviewedWithPhysician:!1},icf:{healthCondition:"",bodyStructureFunction:"",limitationsOfActivity:["","","","","","","","","",""],restrictionOfParticipation:["","","","","","","","","",""],environmentalPersonalFactors:{physicalEnvironment:{barrier:"",facilitator:""},homeAssessment:{barrier:"",facilitator:""},transportation:{barrier:"",facilitator:""},workEnvironment:{barrier:"",facilitator:""},socialSupport:{barrier:"",facilitator:""},attitudesOfOthers:{barrier:"",facilitator:""},personalFactors:{barrier:"",facilitator:""}}}},signatures:{therapistSignature:"",therapistDate:"",physicianSignature:"",physicianDate:"",areaComments:"",patientConcerns:["","",""],patientFamilyPreferences:""}});(0,r.useEffect)(()=>{if(y&&m){var e,a,t;P(m);const r=(0,s.A)((0,s.A)({},D),{},{patientInfo:(0,s.A)((0,s.A)({},D.patientInfo),{},{name:m.name||"".concat(m.firstName," ").concat(m.lastName),sex:"male"===m.gender?"M":"female"===m.gender?"F":"",patientId:m.nationalId||"",nationality:m.nationality||"",age:(null===(e=m.age)||void 0===e?void 0:e.toString())||"",fileNumber:m._id||m.id||"",diagnosis:(null===(a=m.medicalHistory)||void 0===a?void 0:a.primaryDiagnosis)||"",chiefComplaint:(null===(t=m.medicalHistory)||void 0===t?void 0:t.notes)||""})});O(r),A(!1)}else M&&(A(!0),setTimeout(()=>{const e={id:M,name:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",nameEn:"Ahmed Mohammed Ali",dateOfBirth:"2016-03-15",age:8,gender:"male",nationalId:"**********",phone:"+966 50 123 4567",nationality:"Saudi",address:"\u0627\u0644\u0631\u064a\u0627\u0636\u060c \u0627\u0644\u0645\u0645\u0644\u0643\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629 \u0627\u0644\u0633\u0639\u0648\u062f\u064a\u0629"};P(e),O(a=>(0,s.A)((0,s.A)({},a),{},{patientInfo:(0,s.A)((0,s.A)({},a.patientInfo),{},{name:e.nameEn,patientId:e.id,age:e.age.toString(),sex:e.gender,nationality:e.nationality})})),A(!1)},500))},[M,m,y]),(0,r.useEffect)(()=>{v&&Object.keys(v).length>0&&O(e=>(0,s.A)((0,s.A)({},e),v))},[v]);const H=[{id:1,title:j("patientInformation","Patient Information"),component:d},{id:2,title:j("assessmentReview","Assessment & Review of Systems"),component:c},{id:3,title:j("musculoskeletalExam","Musculoskeletal Examination & Pain"),component:x},{id:4,title:j("sensoryFunctions","Sensory Functions & Coordination"),component:u},{id:5,title:j("neuromusculoskeletal","Neuromusculoskeletal & Movement Functions"),component:g},{id:6,title:j("neurodynamicsReflexes","Neurodynamics, Reflexes & Functional Evaluation"),component:h},{id:7,title:j("equipmentICF","Recommended Equipment & ICF"),component:p},{id:8,title:j("signatures","Signatures & Final"),component:b}],B=()=>{const e={};if(1===S)D.patientInfo.name||(e.name=j("nameRequired","Name is required")),D.patientInfo.sex||(e.sex=j("sexRequired","Sex is required")),D.patientInfo.patientId||(e.patientId=j("patientIdRequired","Patient ID is required")),D.patientInfo.nationality||(e.nationality=j("nationalityRequired","Nationality is required")),(!D.patientInfo.age||D.patientInfo.age<18)&&(e.age=j("ageRequired","Age must be 18 or older")),D.patientInfo.diagnosis||(e.diagnosis=j("diagnosisRequired","Diagnosis is required")),D.patientInfo.chiefComplaint||(e.chiefComplaint=j("chiefComplaintRequired","Chief complaint is required"));return T(e),0===Object.keys(e).length},q=async()=>{if(B()){A(!0);try{const e=(0,s.A)((0,s.A)({},D),{},{patientId:t,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()});f&&await f(e),i.Ay.success(j("assessmentSaved","Assessment saved successfully")),k?k():w("/patients")}catch(e){console.error("Error saving assessment:",e),i.Ay.error(j("errorSaving","Error saving assessment"))}finally{A(!1)}}else i.Ay.error(j("pleaseFixErrors","Please fix the errors before saving"))},W=null===(a=H[S-1])||void 0===a?void 0:a.component;return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(N?"font-arabic":"font-english"),children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[j("ptAdultInitialAssessment","PT Adult Initial Assessment Form"),M&&E&&(0,o.jsxs)("span",{className:"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3",children:["- ",E.nameEn||E.name]})]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:j("comprehensivePhysicalTherapyEvaluation","Comprehensive Physical Therapy Evaluation")}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-2 mt-3",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:[(0,o.jsx)("i",{className:"fas fa-certificate text-blue-600 dark:text-blue-400"}),(0,o.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"CARF Compliant"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full",children:[(0,o.jsx)("i",{className:"fas fa-shield-alt text-green-600 dark:text-green-400"}),(0,o.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"CBAHI Compliant"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:[(0,o.jsx)("i",{className:"fas fa-lock text-purple-600 dark:text-purple-400"}),(0,o.jsx)("span",{className:"text-sm font-medium text-purple-800 dark:text-purple-200",children:"HIPAA Secure"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-orange-100 dark:bg-orange-900/30 rounded-full",children:[(0,o.jsx)("i",{className:"fas fa-universal-access text-orange-600 dark:text-orange-400"}),(0,o.jsx)("span",{className:"text-sm font-medium text-orange-800 dark:text-orange-200",children:"Accessible"})]})]})]}),(0,o.jsxs)("div",{className:"flex space-x-3",children:[M&&(0,o.jsxs)("button",{onClick:()=>w("/patients/".concat(M)),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-user mr-2"}),j("viewPatient","View Patient")]}),(0,o.jsxs)("button",{onClick:()=>{i.Ay.info(j("pdfExportNotImplemented","PDF export functionality will be implemented"))},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-file-pdf mr-2"}),j("exportPDF","Export PDF")]}),(0,o.jsx)("button",{onClick:k||(()=>w(M?"/patients/".concat(M):"/patients")),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:j("cancel","Cancel")})]})]})}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:[j("progress","Progress"),": ",S," / ",H.length]}),(0,o.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(S/H.length*100),"% ",j("complete","Complete")]})]}),(0,o.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,o.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(S/H.length*100,"%")}})})]}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow mb-6",children:(0,o.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,o.jsx)("nav",{className:"-mb-px flex overflow-x-auto",children:H.map(e=>(0,o.jsx)("button",{onClick:()=>{return a=e.id,void L(a);var a},className:"py-4 px-6 text-sm font-medium whitespace-nowrap border-b-2 transition-colors ".concat(S===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:(0,o.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,o.jsx)("span",{className:"w-6 h-6 rounded-full flex items-center justify-center text-xs ".concat(S===e.id?"bg-blue-500 text-white":S>e.id?"bg-green-500 text-white":"bg-gray-300 text-gray-600"),children:S>e.id?"\u2713":e.id}),(0,o.jsx)("span",{children:e.title})]})},e.id))})})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:W&&(0,o.jsx)(W,{formData:D,updateFormData:(e,a)=>{O(t=>(0,s.A)((0,s.A)({},t),{},{[e]:(0,s.A)((0,s.A)({},t[e]),a)}))},errors:F,setErrors:T})}),(0,o.jsxs)("div",{className:"flex items-center justify-between mt-6",children:[(0,o.jsxs)("button",{onClick:()=>{S>1&&L(S-1)},disabled:1===S,className:"px-6 py-2 rounded-lg transition-colors ".concat(1===S?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-gray-600 text-white hover:bg-gray-700"),children:[(0,o.jsx)("i",{className:"fas fa-arrow-left mr-2"}),j("previous","Previous")]}),(0,o.jsxs)("div",{className:"flex space-x-3",children:[(0,o.jsx)("button",{onClick:q,disabled:I,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:I?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),j("saving","Saving...")]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-save mr-2"}),j("save","Save")]})}),S<H.length?(0,o.jsxs)("button",{onClick:()=>{B()?S<H.length&&L(S+1):i.Ay.error(j("pleaseFixErrors","Please fix the errors before proceeding"))},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[j("next","Next"),(0,o.jsx)("i",{className:"fas fa-arrow-right ml-2"})]}):(0,o.jsx)("button",{onClick:q,disabled:I,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:I?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),j("completing","Completing...")]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-check mr-2"}),j("complete","Complete Assessment")]})})]})]})]})})}}}]);
//# sourceMappingURL=2287.e3c04ea5.chunk.js.map