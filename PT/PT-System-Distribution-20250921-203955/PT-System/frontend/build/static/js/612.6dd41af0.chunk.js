"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[612],{612:(e,r,a)=>{a.r(r),a.d(r,{default:()=>c});var t=a(2555),s=a(5043),l=a(7921),i=a(2015),d=a(579);const c=()=>{const{t:e,isRTL:r,language:a,setLanguage:c}=(0,l.o)(),{theme:n,setTheme:o}=(0,i.D)(),[x,g]=(0,s.useState)("general"),[m,h]=(0,s.useState)({notifications:{email:!0,sms:!1,push:!0,appointments:!0,reports:!1},privacy:{shareData:!1,analytics:!0,marketing:!1},system:{autoSave:!0,backupFrequency:"daily",sessionTimeout:30}}),b=[{id:"general",label:e("general","General"),icon:"fas fa-cog"},{id:"appearance",label:e("appearance","Appearance"),icon:"fas fa-palette"},{id:"notifications",label:e("notifications","Notifications"),icon:"fas fa-bell"},{id:"privacy",label:e("privacy","Privacy"),icon:"fas fa-shield-alt"},{id:"system",label:e("system","System"),icon:"fas fa-server"},{id:"account",label:e("account","Account"),icon:"fas fa-user"}],u=(e,r,a)=>{h(s=>(0,t.A)((0,t.A)({},s),{},{[e]:(0,t.A)((0,t.A)({},s[e]),{},{[r]:a})}))};return(0,d.jsxs)("div",{className:"p-6 ".concat(r?"font-arabic":"font-english"),children:[(0,d.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("settings","Settings")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("settingsDesc","Manage your application preferences and configuration")})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-1",children:(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsx)("nav",{className:"p-4 space-y-2",children:b.map(e=>(0,d.jsxs)("button",{onClick:()=>g(e.id),className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ".concat(x===e.id?"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:[(0,d.jsx)("i",{className:e.icon}),(0,d.jsx)("span",{children:e.label})]},e.id))})})}),(0,d.jsx)("div",{className:"lg:col-span-3",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:["general"===x&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("generalSettings","General Settings")}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("clinicName","Clinic Name")}),(0,d.jsx)("input",{type:"text",defaultValue:"\u0645\u0631\u0643\u0632 \u0627\u0644\u0631\u064a\u0627\u0636 \u0644\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("clinicAddress","Clinic Address")}),(0,d.jsx)("textarea",{rows:"3",defaultValue:"\u0634\u0627\u0631\u0639 \u0627\u0644\u0645\u0644\u0643 \u0641\u0647\u062f\u060c \u0627\u0644\u0631\u064a\u0627\u0636\u060c \u0627\u0644\u0645\u0645\u0644\u0643\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629 \u0627\u0644\u0633\u0639\u0648\u062f\u064a\u0629",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("phoneNumber","Phone Number")}),(0,d.jsx)("input",{type:"tel",defaultValue:"+966 11 234 5678",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("email","Email")}),(0,d.jsx)("input",{type:"email",defaultValue:"<EMAIL>",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("timezone","Timezone")}),(0,d.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"Asia/Riyadh",children:e("riyadhTime","Riyadh Time (GMT+3)")}),(0,d.jsx)("option",{value:"Asia/Dubai",children:e("dubaiTime","Dubai Time (GMT+4)")}),(0,d.jsx)("option",{value:"UTC",children:e("utc","UTC (GMT+0)")})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("currency","Currency")}),(0,d.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"SAR",children:e("sar","Saudi Riyal (SAR)")}),(0,d.jsx)("option",{value:"USD",children:e("usd","US Dollar (USD)")}),(0,d.jsx)("option",{value:"EUR",children:e("eur","Euro (EUR)")})]})]})]})]})]}),"appearance"===x&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("appearanceSettings","Appearance Settings")}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:e("language","Language")}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsx)("button",{onClick:()=>c("en"),className:"p-4 border-2 rounded-lg transition-colors ".concat("en"===a?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\ud83c\uddfa\ud83c\uddf8"}),(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:"English"})]})}),(0,d.jsx)("button",{onClick:()=>c("ar"),className:"p-4 border-2 rounded-lg transition-colors ".concat("ar"===a?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\ud83c\uddf8\ud83c\udde6"}),(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"})]})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:e("theme","Theme")}),(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,d.jsx)("button",{onClick:()=>o("light"),className:"p-4 border-2 rounded-lg transition-colors ".concat("light"===n?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\u2600\ufe0f"}),(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e("light","Light")})]})}),(0,d.jsx)("button",{onClick:()=>o("dark"),className:"p-4 border-2 rounded-lg transition-colors ".concat("dark"===n?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\ud83c\udf19"}),(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e("dark","Dark")})]})}),(0,d.jsx)("button",{onClick:()=>o("system"),className:"p-4 border-2 rounded-lg transition-colors ".concat("system"===n?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-2",children:"\ud83d\udcbb"}),(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e("system","System")})]})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("fontSize","Font Size")}),(0,d.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"small",children:e("small","Small")}),(0,d.jsx)("option",{value:"medium",selected:!0,children:e("medium","Medium")}),(0,d.jsx)("option",{value:"large",children:e("large","Large")})]})]})]})]}),"notifications"===x&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("notificationSettings","Notification Settings")}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("notificationChannels","Notification Channels")}),(0,d.jsx)("div",{className:"space-y-4",children:Object.entries(m.notifications).slice(0,3).map(r=>{let[a,t]=r;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e(a,a.charAt(0).toUpperCase()+a.slice(1))}),(0,d.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("".concat(a,"Desc"),"Receive notifications via ".concat(a))})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:t,onChange:e=>u("notifications",a,e.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]},a)})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("notificationTypes","Notification Types")}),(0,d.jsx)("div",{className:"space-y-4",children:Object.entries(m.notifications).slice(3).map(r=>{let[a,t]=r;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e(a,a.charAt(0).toUpperCase()+a.slice(1))}),(0,d.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("".concat(a,"NotificationDesc"),"Get notified about ".concat(a.toLowerCase()))})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:t,onChange:e=>u("notifications",a,e.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]},a)})})]})]})]}),"privacy"===x&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("privacySettings","Privacy Settings")}),(0,d.jsx)("div",{className:"space-y-6",children:Object.entries(m.privacy).map(r=>{let[a,t]=r;return(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e(a,a.charAt(0).toUpperCase()+a.slice(1).replace(/([A-Z])/g," $1"))}),(0,d.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("".concat(a,"PrivacyDesc"),"Control ".concat(a.toLowerCase()," privacy settings"))})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:t,onChange:e=>u("privacy",a,e.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]},a)})})]}),"system"===x&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("systemSettings","System Settings")}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e("autoSave","Auto Save")}),(0,d.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("autoSaveDesc","Automatically save changes")})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:m.system.autoSave,onChange:e=>u("system","autoSave",e.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("backupFrequency","Backup Frequency")}),(0,d.jsxs)("select",{value:m.system.backupFrequency,onChange:e=>u("system","backupFrequency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"hourly",children:e("hourly","Hourly")}),(0,d.jsx)("option",{value:"daily",children:e("daily","Daily")}),(0,d.jsx)("option",{value:"weekly",children:e("weekly","Weekly")}),(0,d.jsx)("option",{value:"monthly",children:e("monthly","Monthly")})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("sessionTimeout","Session Timeout (minutes)")}),(0,d.jsxs)("select",{value:m.system.sessionTimeout,onChange:e=>u("system","sessionTimeout",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,d.jsxs)("option",{value:15,children:["15 ",e("minutes","minutes")]}),(0,d.jsxs)("option",{value:30,children:["30 ",e("minutes","minutes")]}),(0,d.jsxs)("option",{value:60,children:["60 ",e("minutes","minutes")]}),(0,d.jsxs)("option",{value:120,children:["120 ",e("minutes","minutes")]})]})]})]})]}),"account"===x&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("accountSettings","Account Settings")}),(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("i",{className:"fas fa-user text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("accountManagement","Account Management")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:e("accountDesc","Manage your account settings and security")}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e("changePassword","Change Password")}),(0,d.jsx)("button",{className:"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:e("updateProfile","Update Profile")}),(0,d.jsx)("button",{className:"w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:e("deleteAccount","Delete Account")})]})]})})]}),(0,d.jsx)("div",{className:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-b-lg",children:(0,d.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,d.jsx)("button",{className:"px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:e("reset","Reset")}),(0,d.jsx)("button",{className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e("saveChanges","Save Changes")})]})})]})})]})]})}}}]);
//# sourceMappingURL=612.6dd41af0.chunk.js.map