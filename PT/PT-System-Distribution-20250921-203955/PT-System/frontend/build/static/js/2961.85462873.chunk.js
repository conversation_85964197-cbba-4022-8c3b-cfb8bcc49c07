"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2961],{2961:(e,r,t)=>{t.r(r),t.d(r,{default:()=>c});var a=t(2555),s=t(5043),d=t(3216),l=t(7921),n=t(4528),i=t(3768),o=t(579);const c=()=>{const{t:e,isRTL:r}=(0,l.o)(),{user:t}=(0,n.A)(),[c,x]=((0,d.Zp)(),(0,s.useState)(!1)),[g,m]=(0,s.useState)("overview"),[h,y]=(0,s.useState)({startDate:new Date((new Date).getFullYear(),(new Date).getMonth(),1).toISOString().split("T")[0],endDate:(new Date).toISOString().split("T")[0]}),[b,p]=(0,s.useState)({overview:{totalRevenue:125e3,totalPayments:98e3,pendingAmount:27e3,overdueAmount:15e3,totalInvoices:156,paidInvoices:120,pendingInvoices:28,overdueInvoices:8},monthlyTrends:[{month:"Jan",revenue:85e3,payments:78e3},{month:"Feb",revenue:92e3,payments:85e3},{month:"Mar",revenue:105e3,payments:95e3},{month:"Apr",revenue:118e3,payments:108e3},{month:"May",revenue:125e3,payments:115e3}],paymentMethods:[{method:"Insurance",amount:65e3,percentage:52.4},{method:"Cash",amount:35e3,percentage:28.2},{method:"Card",amount:2e4,percentage:16.1},{method:"Bank Transfer",amount:4e3,percentage:3.2}],topServices:[{service:"Physical Therapy Session",revenue:45e3,sessions:180},{service:"Initial Assessment",revenue:28e3,sessions:140},{service:"Treatment Package",revenue:32e3,sessions:80},{service:"Follow-up Session",revenue:2e4,sessions:100}]});(0,s.useEffect)(()=>{u()},[h]);const u=async()=>{x(!0);try{await new Promise(e=>setTimeout(e,1e3))}catch(r){i.Ay.error(e("errorLoadingReports","Error loading financial reports"))}finally{x(!1)}},v=(e,r)=>{y(t=>(0,a.A)((0,a.A)({},t),{},{[e]:r}))},j=r=>{i.Ay.success(e("reportExported","Report exported as ".concat(r.toUpperCase())))};return(0,o.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,o.jsx)("div",{className:"mb-8",children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("financialReports","Financial Reports")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("financialReportsDesc","Comprehensive financial analytics and reporting")})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("startDate","Start Date")}),(0,o.jsx)("input",{type:"date",value:h.startDate,onChange:e=>v("startDate",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("endDate","End Date")}),(0,o.jsx)("input",{type:"date",value:h.endDate,onChange:e=>v("endDate",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]})]})}),(0,o.jsx)("div",{className:"mb-6",children:(0,o.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,o.jsxs)("button",{onClick:()=>m("overview"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("overview"===g?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-chart-pie mr-2"}),e("overview","Overview")]}),(0,o.jsxs)("button",{onClick:()=>m("revenue"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("revenue"===g?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-chart-line mr-2"}),e("revenue","Revenue")]}),(0,o.jsxs)("button",{onClick:()=>m("payments"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("payments"===g?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-credit-card mr-2"}),e("payments","Payments")]}),(0,o.jsxs)("button",{onClick:()=>m("aging"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("aging"===g?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-clock mr-2"}),e("aging","Aging")]}),(0,o.jsxs)("button",{onClick:()=>m("custom"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("custom"===g?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-cogs mr-2"}),e("custom","Custom")]})]})})}),"overview"===g&&(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-dollar-sign text-green-600 dark:text-green-400 text-xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsxs)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[b.overview.totalRevenue.toLocaleString()," ",e("sar","SAR")]}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("totalRevenue","Total Revenue")})]})]})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-credit-card text-blue-600 dark:text-blue-400 text-xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsxs)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[b.overview.totalPayments.toLocaleString()," ",e("sar","SAR")]}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("totalPayments","Total Payments")})]})]})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsxs)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[b.overview.pendingAmount.toLocaleString()," ",e("sar","SAR")]}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("pendingAmount","Pending Amount")})]})]})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg",children:(0,o.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl"})}),(0,o.jsxs)("div",{className:"ml-4",children:[(0,o.jsxs)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[b.overview.overdueAmount.toLocaleString()," ",e("sar","SAR")]}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("overdueAmount","Overdue Amount")})]})]})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("revenueTrend","Revenue Trend")}),(0,o.jsx)("div",{className:"h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("i",{className:"fas fa-chart-line text-4xl text-gray-400 mb-2"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("chartPlaceholder","Chart will be rendered here")})]})})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("paymentMethodsDistribution","Payment Methods Distribution")}),(0,o.jsx)("div",{className:"space-y-3",children:b.paymentMethods.map((r,t)=>(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"w-4 h-4 rounded-full mr-3 ".concat(0===t?"bg-blue-500":1===t?"bg-green-500":2===t?"bg-yellow-500":"bg-purple-500")}),(0,o.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:r.method})]}),(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsxs)("div",{className:"font-semibold text-gray-900 dark:text-white",children:[r.amount.toLocaleString()," ",e("sar","SAR")]}),(0,o.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[r.percentage,"%"]})]})]},t))})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("topServices","Top Services by Revenue")}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full",children:[(0,o.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("service","Service")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("revenue","Revenue")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("sessions","Sessions")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("averagePerSession","Avg/Session")})]})}),(0,o.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:b.topServices.map((r,t)=>(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white",children:r.service}),(0,o.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:[r.revenue.toLocaleString()," ",e("sar","SAR")]}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:r.sessions}),(0,o.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:[(r.revenue/r.sessions).toFixed(0)," ",e("sar","SAR")]})]},t))})]})})]})]}),(0,o.jsxs)("div",{className:"mt-8 bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("exportReports","Export Reports")}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,o.jsxs)("button",{onClick:()=>j("pdf"),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-file-pdf mr-2"}),e("exportPDF","Export PDF")]}),(0,o.jsxs)("button",{onClick:()=>j("excel"),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-file-excel mr-2"}),e("exportExcel","Export Excel")]}),(0,o.jsxs)("button",{onClick:()=>j("csv"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-file-csv mr-2"}),e("exportCSV","Export CSV")]}),(0,o.jsxs)("button",{onClick:()=>{i.Ay.success(e("customReportGenerated","Custom report generated successfully"))},className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-cogs mr-2"}),e("customReport","Custom Report")]})]})]})]})}}}]);
//# sourceMappingURL=2961.85462873.chunk.js.map