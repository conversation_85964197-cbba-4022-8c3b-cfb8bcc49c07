"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6813],{6813:(e,s,i)=>{i.r(s),i.d(s,{default:()=>r});var o=i(2555),t=i(5043),n=i(7921),a=i(579);const r=e=>{let{patientProfile:s,onPlanUpdate:i}=e;const{t:r}=(0,n.o)(),[l,c]=(0,t.useState)("goals"),[d,m]=(0,t.useState)({goals:[],activities:[],accommodations:[],schedule:{},progressMetrics:[]}),g={motor:{label:r("motorSkills","Motor Skills"),icon:"\ud83c\udfc3",color:"bg-blue-100 border-blue-300",goals:[{id:"gross_motor",label:r("grossMotor","Gross Motor Development"),description:r("grossMotorDesc","Large muscle movements and coordination")},{id:"fine_motor",label:r("fineMotor","Fine Motor Development"),description:r("fineMotorDesc","Small muscle control and dexterity")},{id:"balance",label:r("balance","Balance and Stability"),description:r("balanceDesc","Postural control and equilibrium")},{id:"coordination",label:r("coordination","Coordination"),description:r("coordinationDesc","Movement planning and execution")}]},sensory:{label:r("sensoryIntegration","Sensory Integration"),icon:"\ud83d\udc41\ufe0f",color:"bg-purple-100 border-purple-300",goals:[{id:"tactile_processing",label:r("tactileProcessing","Tactile Processing"),description:r("tactileDesc","Touch sensitivity and discrimination")},{id:"vestibular_processing",label:r("vestibularProcessing","Vestibular Processing"),description:r("vestibularDesc","Movement and balance processing")},{id:"proprioceptive",label:r("proprioceptive","Proprioceptive Awareness"),description:r("proprioceptiveDesc","Body position awareness")},{id:"sensory_modulation",label:r("sensoryModulation","Sensory Modulation"),description:r("sensoryModulationDesc","Regulating sensory input")}]},communication:{label:r("communication","Communication"),icon:"\ud83d\udcac",color:"bg-green-100 border-green-300",goals:[{id:"verbal_expression",label:r("verbalExpression","Verbal Expression"),description:r("verbalExpressionDesc","Speaking and articulation")},{id:"nonverbal_communication",label:r("nonverbalCommunication","Non-verbal Communication"),description:r("nonverbalDesc","Gestures and body language")},{id:"aac_usage",label:r("aacUsage","AAC Device Usage"),description:r("aacUsageDesc","Alternative communication methods")},{id:"social_communication",label:r("socialCommunication","Social Communication"),description:r("socialCommDesc","Interactive communication skills")}]},behavioral:{label:r("behavioral","Behavioral"),icon:"\ud83c\udfaf",color:"bg-yellow-100 border-yellow-300",goals:[{id:"self_regulation",label:r("selfRegulation","Self-Regulation"),description:r("selfRegulationDesc","Managing emotions and behaviors")},{id:"attention_focus",label:r("attentionFocus","Attention and Focus"),description:r("attentionDesc","Sustained attention skills")},{id:"social_skills",label:r("socialSkills","Social Skills"),description:r("socialSkillsDesc","Interaction with others")},{id:"independence",label:r("independence","Independence"),description:r("independenceDesc","Self-care and autonomy")}]}},u={autism:[{id:"structured_play",name:r("structuredPlay","Structured Play"),duration:15,sensoryLevel:"low",description:r("structuredPlayDesc","Predictable play activities with clear rules")},{id:"sensory_breaks",name:r("sensoryBreaks","Sensory Breaks"),duration:5,sensoryLevel:"calming",description:r("sensoryBreaksDesc","Calming activities to regulate sensory input")},{id:"visual_schedules",name:r("visualSchedules","Visual Schedule Review"),duration:10,sensoryLevel:"low",description:r("visualSchedulesDesc","Review upcoming activities with pictures")},{id:"social_stories",name:r("socialStories","Social Stories"),duration:10,sensoryLevel:"low",description:r("socialStoriesDesc","Stories about social situations and expectations")}],cerebral_palsy:[{id:"range_of_motion",name:r("rangeOfMotion","Range of Motion"),duration:20,sensoryLevel:"moderate",description:r("romDesc","Gentle stretching and joint mobility")},{id:"strength_training",name:r("strengthTraining","Strength Training"),duration:15,sensoryLevel:"moderate",description:r("strengthDesc","Muscle strengthening exercises")},{id:"gait_training",name:r("gaitTraining","Gait Training"),duration:20,sensoryLevel:"high",description:r("gaitDesc","Walking and mobility practice")},{id:"positioning",name:r("positioning","Positioning"),duration:10,sensoryLevel:"low",description:r("positioningDesc","Proper body positioning and support")}],down_syndrome:[{id:"motor_planning",name:r("motorPlanning","Motor Planning"),duration:15,sensoryLevel:"moderate",description:r("motorPlanningDesc","Planning and executing movements")},{id:"balance_activities",name:r("balanceActivities","Balance Activities"),duration:15,sensoryLevel:"moderate",description:r("balanceActivitiesDesc","Static and dynamic balance training")},{id:"cognitive_motor",name:r("cognitiveMotor","Cognitive-Motor Tasks"),duration:20,sensoryLevel:"moderate",description:r("cognitiveMotorDesc","Thinking while moving activities")},{id:"functional_skills",name:r("functionalSkills","Functional Skills"),duration:25,sensoryLevel:"low",description:r("functionalSkillsDesc","Daily living movement skills")}]},p=[{id:"extra_time",label:r("extraTime","Extra Time"),icon:"\u23f0",description:r("extraTimeDesc","Additional time for task completion")},{id:"visual_cues",label:r("visualCues","Visual Cues"),icon:"\ud83d\udc41\ufe0f",description:r("visualCuesDesc","Picture prompts and visual supports")},{id:"sensory_tools",label:r("sensoryTools","Sensory Tools"),icon:"\ud83e\uddf8",description:r("sensoryToolsDesc","Fidgets, weighted items, sensory aids")},{id:"quiet_space",label:r("quietSpace","Quiet Space"),icon:"\ud83e\udd2b",description:r("quietSpaceDesc","Low-stimulation environment option")},{id:"movement_breaks",label:r("movementBreaks","Movement Breaks"),icon:"\ud83c\udfc3",description:r("movementBreaksDesc","Regular movement and stretch breaks")},{id:"simplified_instructions",label:r("simplifiedInstructions","Simplified Instructions"),icon:"\ud83d\udcdd",description:r("simplifiedDesc","Clear, step-by-step directions")},{id:"peer_support",label:r("peerSupport","Peer Support"),icon:"\ud83d\udc65",description:r("peerSupportDesc","Buddy system or group activities")},{id:"assistive_technology",label:r("assistiveTechnology","Assistive Technology"),icon:"\ud83d\udcf1",description:r("assistiveTechDesc","Communication devices and mobility aids")}];(0,t.useEffect)(()=>{s&&b(s)},[s]);const b=e=>{const s=e.primaryDiagnosis,i=u[s]||[],t=x(s),n=v(e);m(e=>(0,o.A)((0,o.A)({},e),{},{activities:i,goals:t,accommodations:n}))},x=e=>({autism:["sensory_modulation","social_communication","self_regulation","fine_motor"],cerebral_palsy:["gross_motor","balance","coordination","independence"],down_syndrome:["motor_planning","balance","cognitive_motor","social_skills"],intellectual_disability:["functional_skills","independence","social_skills","communication"]}[e]||[]),v=e=>{const s=[];return"sensory_seeking"===e.sensoryProfile&&s.push("sensory_tools","movement_breaks"),"sensory_avoiding"===e.sensoryProfile&&s.push("quiet_space","visual_cues"),"non_verbal"===e.communicationLevel&&s.push("visual_cues","assistive_technology"),s.push("extra_time","simplified_instructions"),s},h=[{id:"goals",label:r("treatmentGoals","Treatment Goals"),icon:"\ud83c\udfaf"},{id:"activities",label:r("activities","Activities"),icon:"\ud83c\udfae"},{id:"accommodations",label:r("accommodations","Accommodations"),icon:"\ud83d\udee0\ufe0f"},{id:"schedule",label:r("schedule","Schedule"),icon:"\ud83d\udcc5"},{id:"progress",label:r("progress","Progress"),icon:"\ud83d\udcca"}];return(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,a.jsx)("span",{className:"mr-3",children:"\ud83c\udfaf"}),r("adaptiveTreatmentPlan","Adaptive Treatment Plan")]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)("span",{className:"px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm font-medium",children:null!==s&&void 0!==s&&s.primaryDiagnosis?r(s.primaryDiagnosis):r("general","General")})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,a.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:h.map(e=>(0,a.jsxs)("button",{onClick:()=>c(e.id),className:"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(l===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,a.jsx)("span",{children:e.icon}),(0,a.jsx)("span",{children:e.label})]},e.id))})}),(0,a.jsxs)("div",{className:"space-y-6",children:["goals"===l&&(0,a.jsxs)("div",{className:"space-y-6",children:[d.goals.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:r("currentGoals","Current Goals")}),(0,a.jsx)("div",{className:"space-y-4",children:d.goals.map(e=>(0,a.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.label}),(0,a.jsx)("button",{onClick:()=>{return s=e.id,void m(e=>(0,o.A)((0,o.A)({},e),{},{goals:e.goals.filter(e=>e.id!==s)}));var s},className:"text-red-500 hover:text-red-700 text-sm",children:"\u2715"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 mr-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1",children:[(0,a.jsx)("span",{children:r("progress","Progress")}),(0,a.jsxs)("span",{children:[e.progress,"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})})]}),(0,a.jsx)("input",{type:"range",min:"0",max:"100",value:e.progress,onChange:s=>{return i=e.id,t=parseInt(s.target.value),void m(e=>(0,o.A)((0,o.A)({},e),{},{goals:e.goals.map(e=>e.id===i?(0,o.A)((0,o.A)({},e),{},{progress:t}):e)}));var i,t},className:"w-20"})]})]},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:r("addNewGoals","Add New Goals")}),(0,a.jsx)("div",{className:"space-y-6",children:Object.entries(g).map(e=>{let[s,i]=e;return(0,a.jsxs)("div",{className:"p-4 border-2 border-dashed rounded-lg ".concat(i.color),children:[(0,a.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,a.jsx)("span",{className:"mr-2",children:i.icon}),i.label]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:i.goals.map(e=>(0,a.jsxs)("button",{onClick:()=>((e,s)=>{const i=g[e].goals.find(e=>e.id===s);if(i&&!d.goals.find(e=>e.id===s)){const s=(0,o.A)((0,o.A)({},i),{},{categoryId:e,targetDate:new Date(Date.now()+2592e6),priority:"medium",progress:0,milestones:[]});m(e=>(0,o.A)((0,o.A)({},e),{},{goals:[...e.goals,s]}))}})(s,e.id),disabled:d.goals.find(s=>s.id===e.id),className:"p-3 text-left rounded-lg border transition-colors ".concat(d.goals.find(s=>s.id===e.id)?"bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed":"bg-white border-gray-200 hover:border-gray-300 hover:bg-gray-50"),children:[(0,a.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:e.description})]},e.id))})]},s)})})]})]}),"activities"===l&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:r("recommendedActivities","Recommended Activities")}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:d.activities.map(e=>(0,a.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[e.duration," min"]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat("low"===e.sensoryLevel?"bg-green-100 text-green-800":"moderate"===e.sensoryLevel?"bg-yellow-100 text-yellow-800":"high"===e.sensoryLevel?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:[r(e.sensoryLevel,e.sensoryLevel)," ",r("sensory","sensory")]}),(0,a.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:r("addToSession","Add to Session")})]})]},e.id))})]}),"accommodations"===l&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:r("recommendedAccommodations","Recommended Accommodations")}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:p.map(e=>(0,a.jsx)("div",{className:"p-4 border-2 rounded-lg cursor-pointer transition-all ".concat(d.accommodations.includes(e.id)?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),onClick:()=>{const s=d.accommodations.includes(e.id);m(i=>(0,o.A)((0,o.A)({},i),{},{accommodations:s?i.accommodations.filter(s=>s!==e.id):[...i.accommodations,e.id]}))},children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:e.icon}),(0,a.jsx)("div",{className:"font-medium text-gray-900 dark:text-white mb-1",children:e.label}),(0,a.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.description})]})},e.id))})]}),("schedule"===l||"progress"===l)&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"schedule"===l?"\ud83d\udcc5":"\ud83d\udcca"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"schedule"===l?r("scheduleBuilder","Schedule Builder"):r("progressTracking","Progress Tracking")}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"schedule"===l?r("scheduleComingSoon","Visual schedule builder coming soon..."):r("progressComingSoon","Advanced progress tracking coming soon...")})]})]}),(0,a.jsxs)("div",{className:"mt-8 flex justify-end space-x-4",children:[(0,a.jsx)("button",{onClick:()=>b(s),className:"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:r("regeneratePlan","Regenerate Plan")}),(0,a.jsx)("button",{onClick:()=>null===i||void 0===i?void 0:i(d),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:r("saveTreatmentPlan","Save Treatment Plan")})]})]})}}}]);
//# sourceMappingURL=6813.43825602.chunk.js.map