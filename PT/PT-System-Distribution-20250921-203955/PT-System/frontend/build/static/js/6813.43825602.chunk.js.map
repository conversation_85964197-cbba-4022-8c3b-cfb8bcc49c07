{"version": 3, "file": "static/js/6813.43825602.chunk.js", "mappings": "mMAGA,MA6ZA,EA7Z8BA,IAAuC,IAAtC,eAAEC,EAAc,aAAEC,GAAcF,EAC7D,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,UACpCC,EAAeC,IAAoBF,EAAAA,EAAAA,UAAS,CACjDG,MAAO,GACPC,WAAY,GACZC,eAAgB,GAChBC,SAAU,CAAC,EACXC,gBAAiB,KAGbC,EAAiB,CACrBC,MAAO,CACLC,MAAOd,EAAE,cAAe,gBACxBe,KAAM,eACNC,MAAO,8BACPT,MAAO,CACL,CAAEU,GAAI,cAAeH,MAAOd,EAAE,aAAc,2BAA4BkB,YAAalB,EAAE,iBAAkB,4CACzG,CAAEiB,GAAI,aAAcH,MAAOd,EAAE,YAAa,0BAA2BkB,YAAalB,EAAE,gBAAiB,uCACrG,CAAEiB,GAAI,UAAWH,MAAOd,EAAE,UAAW,yBAA0BkB,YAAalB,EAAE,cAAe,qCAC7F,CAAEiB,GAAI,eAAgBH,MAAOd,EAAE,eAAgB,gBAAiBkB,YAAalB,EAAE,mBAAoB,sCAGvGmB,QAAS,CACPL,MAAOd,EAAE,qBAAsB,uBAC/Be,KAAM,qBACNC,MAAO,kCACPT,MAAO,CACL,CAAEU,GAAI,qBAAsBH,MAAOd,EAAE,oBAAqB,sBAAuBkB,YAAalB,EAAE,cAAe,yCAC/G,CAAEiB,GAAI,wBAAyBH,MAAOd,EAAE,uBAAwB,yBAA0BkB,YAAalB,EAAE,iBAAkB,oCAC3H,CAAEiB,GAAI,iBAAkBH,MAAOd,EAAE,iBAAkB,4BAA6BkB,YAAalB,EAAE,qBAAsB,4BACrH,CAAEiB,GAAI,qBAAsBH,MAAOd,EAAE,oBAAqB,sBAAuBkB,YAAalB,EAAE,wBAAyB,+BAG7HoB,cAAe,CACbN,MAAOd,EAAE,gBAAiB,iBAC1Be,KAAM,eACNC,MAAO,gCACPT,MAAO,CACL,CAAEU,GAAI,oBAAqBH,MAAOd,EAAE,mBAAoB,qBAAsBkB,YAAalB,EAAE,uBAAwB,8BACrH,CAAEiB,GAAI,0BAA2BH,MAAOd,EAAE,yBAA0B,4BAA6BkB,YAAalB,EAAE,gBAAiB,+BACjI,CAAEiB,GAAI,YAAaH,MAAOd,EAAE,WAAY,oBAAqBkB,YAAalB,EAAE,eAAgB,sCAC5F,CAAEiB,GAAI,uBAAwBH,MAAOd,EAAE,sBAAuB,wBAAyBkB,YAAalB,EAAE,iBAAkB,uCAG5HqB,WAAY,CACVP,MAAOd,EAAE,aAAc,cACvBe,KAAM,eACNC,MAAO,kCACPT,MAAO,CACL,CAAEU,GAAI,kBAAmBH,MAAOd,EAAE,iBAAkB,mBAAoBkB,YAAalB,EAAE,qBAAsB,oCAC7G,CAAEiB,GAAI,kBAAmBH,MAAOd,EAAE,iBAAkB,uBAAwBkB,YAAalB,EAAE,gBAAiB,+BAC5G,CAAEiB,GAAI,gBAAiBH,MAAOd,EAAE,eAAgB,iBAAkBkB,YAAalB,EAAE,mBAAoB,4BACrG,CAAEiB,GAAI,eAAgBH,MAAOd,EAAE,eAAgB,gBAAiBkB,YAAalB,EAAE,mBAAoB,8BAKnGsB,EAAqB,CACzBC,OAAQ,CACN,CAAEN,GAAI,kBAAmBO,KAAMxB,EAAE,iBAAkB,mBAAoByB,SAAU,GAAIC,aAAc,MAAOR,YAAalB,EAAE,qBAAsB,iDAC/I,CAAEiB,GAAI,iBAAkBO,KAAMxB,EAAE,gBAAiB,kBAAmByB,SAAU,EAAGC,aAAc,UAAWR,YAAalB,EAAE,oBAAqB,iDAC9I,CAAEiB,GAAI,mBAAoBO,KAAMxB,EAAE,kBAAmB,0BAA2ByB,SAAU,GAAIC,aAAc,MAAOR,YAAalB,EAAE,sBAAuB,6CACzJ,CAAEiB,GAAI,iBAAkBO,KAAMxB,EAAE,gBAAiB,kBAAmByB,SAAU,GAAIC,aAAc,MAAOR,YAAalB,EAAE,oBAAqB,sDAE7I2B,eAAgB,CACd,CAAEV,GAAI,kBAAmBO,KAAMxB,EAAE,gBAAiB,mBAAoByB,SAAU,GAAIC,aAAc,WAAYR,YAAalB,EAAE,UAAW,yCACxI,CAAEiB,GAAI,oBAAqBO,KAAMxB,EAAE,mBAAoB,qBAAsByB,SAAU,GAAIC,aAAc,WAAYR,YAAalB,EAAE,eAAgB,mCACpJ,CAAEiB,GAAI,gBAAiBO,KAAMxB,EAAE,eAAgB,iBAAkByB,SAAU,GAAIC,aAAc,OAAQR,YAAalB,EAAE,WAAY,kCAChI,CAAEiB,GAAI,cAAeO,KAAMxB,EAAE,cAAe,eAAgByB,SAAU,GAAIC,aAAc,MAAOR,YAAalB,EAAE,kBAAmB,yCAEnI4B,cAAe,CACb,CAAEX,GAAI,iBAAkBO,KAAMxB,EAAE,gBAAiB,kBAAmByB,SAAU,GAAIC,aAAc,WAAYR,YAAalB,EAAE,oBAAqB,qCAChJ,CAAEiB,GAAI,qBAAsBO,KAAMxB,EAAE,oBAAqB,sBAAuByB,SAAU,GAAIC,aAAc,WAAYR,YAAalB,EAAE,wBAAyB,wCAChK,CAAEiB,GAAI,kBAAmBO,KAAMxB,EAAE,iBAAkB,yBAA0ByB,SAAU,GAAIC,aAAc,WAAYR,YAAalB,EAAE,qBAAsB,qCAC1J,CAAEiB,GAAI,oBAAqBO,KAAMxB,EAAE,mBAAoB,qBAAsByB,SAAU,GAAIC,aAAc,MAAOR,YAAalB,EAAE,uBAAwB,mCAIrJ6B,EAAuB,CAC3B,CAAEZ,GAAI,aAAcH,MAAOd,EAAE,YAAa,cAAee,KAAM,SAAKG,YAAalB,EAAE,gBAAiB,wCACpG,CAAEiB,GAAI,cAAeH,MAAOd,EAAE,aAAc,eAAgBe,KAAM,qBAAOG,YAAalB,EAAE,iBAAkB,wCAC1G,CAAEiB,GAAI,gBAAiBH,MAAOd,EAAE,eAAgB,iBAAkBe,KAAM,eAAMG,YAAalB,EAAE,mBAAoB,0CACjH,CAAEiB,GAAI,cAAeH,MAAOd,EAAE,aAAc,eAAgBe,KAAM,eAAMG,YAAalB,EAAE,iBAAkB,uCACzG,CAAEiB,GAAI,kBAAmBH,MAAOd,EAAE,iBAAkB,mBAAoBe,KAAM,eAAMG,YAAalB,EAAE,qBAAsB,wCACzH,CAAEiB,GAAI,0BAA2BH,MAAOd,EAAE,yBAA0B,2BAA4Be,KAAM,eAAMG,YAAalB,EAAE,iBAAkB,mCAC7I,CAAEiB,GAAI,eAAgBH,MAAOd,EAAE,cAAe,gBAAiBe,KAAM,eAAMG,YAAalB,EAAE,kBAAmB,qCAC7G,CAAEiB,GAAI,uBAAwBH,MAAOd,EAAE,sBAAuB,wBAAyBe,KAAM,eAAMG,YAAalB,EAAE,oBAAqB,8CAGzI8B,EAAAA,EAAAA,WAAU,KACJhC,GACFiC,EAAqBjC,IAEtB,CAACA,IAEJ,MAAMiC,EAAwBC,IAC5B,MAAMC,EAAYD,EAAQE,iBACpBC,EAAsBb,EAAmBW,IAAc,GAGvDG,EAAiBC,EAA0BJ,GAG3CK,EAA0BC,EAAuBP,GAEvD1B,EAAiBkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBD,GAAI,IACPhC,WAAY2B,EACZ5B,MAAO6B,EACP3B,eAAgB6B,MAIdD,EAA6BJ,IACjB,CACdV,OAAQ,CAAC,qBAAsB,uBAAwB,kBAAmB,cAC1EI,eAAgB,CAAC,cAAe,UAAW,eAAgB,gBAC3DC,cAAe,CAAC,iBAAkB,UAAW,kBAAmB,iBAChEc,wBAAyB,CAAC,oBAAqB,eAAgB,gBAAiB,kBAGnET,IAAc,IAGzBM,EAA0BP,IAC9B,MAAMvB,EAAiB,GAcvB,MAZ+B,oBAA3BuB,EAAQW,gBACVlC,EAAemC,KAAK,gBAAiB,mBAER,qBAA3BZ,EAAQW,gBACVlC,EAAemC,KAAK,cAAe,eAEF,eAA/BZ,EAAQa,oBACVpC,EAAemC,KAAK,cAAe,wBAGrCnC,EAAemC,KAAK,aAAc,2BAE3BnC,GAwCHqC,EAAO,CACX,CAAE7B,GAAI,QAASH,MAAOd,EAAE,iBAAkB,mBAAoBe,KAAM,gBACpE,CAAEE,GAAI,aAAcH,MAAOd,EAAE,aAAc,cAAee,KAAM,gBAChE,CAAEE,GAAI,iBAAkBH,MAAOd,EAAE,iBAAkB,kBAAmBe,KAAM,sBAC5E,CAAEE,GAAI,WAAYH,MAAOd,EAAE,WAAY,YAAae,KAAM,gBAC1D,CAAEE,GAAI,WAAYH,MAAOd,EAAE,WAAY,YAAae,KAAM,iBAG5D,OACEgC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBjD,EAAE,wBAAyB,+BAE9BkD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+GAA8GC,SAC7G,OAAdnD,QAAc,IAAdA,GAAAA,EAAgBoC,iBAAmBlC,EAAEF,EAAeoC,kBAAoBlC,EAAE,UAAW,mBAM5FkD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDH,EAAKK,IAAKC,IACTL,EAAAA,EAAAA,MAAA,UAEEM,QAASA,IAAMlD,EAAaiD,EAAInC,IAChC+B,UAAS,0FAAAM,OACPpD,IAAckD,EAAInC,GACd,mDACA,0HACHgC,SAAA,EAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAOG,EAAIrC,QACXmC,EAAAA,EAAAA,KAAA,QAAAD,SAAOG,EAAItC,UATNsC,EAAInC,UAgBjB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CACT,UAAd/C,IACC6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvB5C,EAAcE,MAAMgD,OAAS,IAC5BR,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEjD,EAAE,eAAgB,oBAErBkD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB5C,EAAcE,MAAM4C,IAAKK,IACxBT,EAAAA,EAAAA,MAAA,OAAmBC,UAAU,6DAA4DC,SAAA,EACvFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEO,EAAK1C,SAChEoC,EAAAA,EAAAA,KAAA,UACEG,QAASA,KAAMI,OA1EnBC,EA0E8BF,EAAKvC,QAzErDX,EAAiBkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBD,GAAI,IACPjC,MAAOiC,EAAKjC,MAAMoD,OAAOC,GAAKA,EAAE3C,KAAOyC,MAHvBA,OA2EIV,UAAU,0CAAyCC,SACpD,eAIHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SAAEO,EAAKtC,eACnE6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kFAAiFC,SAAA,EAC9FC,EAAAA,EAAAA,KAAA,QAAAD,SAAOjD,EAAE,WAAY,eACrB+C,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAOO,EAAKK,SAAS,WAEvBX,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,2DACVc,MAAO,CAAEC,MAAM,GAADT,OAAKE,EAAKK,SAAQ,cAItCX,EAAAA,EAAAA,KAAA,SACEc,KAAK,QACLC,IAAI,IACJC,IAAI,MACJC,MAAOX,EAAKK,SACZO,SAAWC,IAAMC,OA5FbZ,EA4FgCF,EAAKvC,GA5F7B4C,EA4FiCU,SAASF,EAAEG,OAAOL,YA3FrF7D,EAAiBkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBD,GAAI,IACPjC,MAAOiC,EAAKjC,MAAM4C,IAAIS,GACpBA,EAAE3C,KAAOyC,GAAMjB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQmB,GAAC,IAAEC,aAAaD,MAJlBU,IAACZ,EAAQG,GA6FZb,UAAU,cA9BNQ,EAAKvC,WAwCvB8B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEjD,EAAE,cAAe,oBAEpBkD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBwB,OAAOC,QAAQ9D,GAAgBuC,IAAIwB,IAAA,IAAEC,EAAYC,GAASF,EAAA,OACzD5B,EAAAA,EAAAA,MAAA,OAAsBC,UAAS,yCAAAM,OAA2CuB,EAAS7D,OAAQiC,SAAA,EACzFF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAE4B,EAAS9D,OAChC8D,EAAS/D,UAEZoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnD4B,EAAStE,MAAM4C,IAAKK,IACnBT,EAAAA,EAAAA,MAAA,UAEEM,QAASA,IAlJjByB,EAACF,EAAYlB,KAC3B,MACMF,EADW5C,EAAegE,GACVrE,MAAMwE,KAAKnB,GAAKA,EAAE3C,KAAOyC,GAE/C,GAAIF,IAASnD,EAAcE,MAAMwE,KAAKnB,GAAKA,EAAE3C,KAAOyC,GAAS,CAC3D,MAAMsB,GAAOvC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRe,GAAI,IACPoB,aACAK,WAAY,IAAIC,KAAKA,KAAKC,MAAQ,QAClCC,SAAU,SACVvB,SAAU,EACVwB,WAAY,KAGd/E,EAAiBkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBD,GAAI,IACPjC,MAAO,IAAIiC,EAAKjC,MAAOyE,KAE3B,GAgIqCF,CAAQF,EAAYpB,EAAKvC,IACxCqE,SAAUjF,EAAcE,MAAMwE,KAAKnB,GAAKA,EAAE3C,KAAOuC,EAAKvC,IACtD+B,UAAS,qDAAAM,OACPjD,EAAcE,MAAMwE,KAAKnB,GAAKA,EAAE3C,KAAOuC,EAAKvC,IACxC,+DACA,mEACHgC,SAAA,EAEHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCC,SAAEO,EAAK1C,SACzDoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAEO,EAAKtC,gBAV7CsC,EAAKvC,SARR2D,aA6BL,eAAd1E,IACC6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEjD,EAAE,wBAAyB,6BAE9BkD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnD5C,EAAcG,WAAW2C,IAAKoC,IAC7BxC,EAAAA,EAAAA,MAAA,OAAuBC,UAAU,6DAA4DC,SAAA,EAC3FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEsC,EAAS/D,QACpEuB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wBAAuBC,SAAA,CAAEsC,EAAS9D,SAAS,cAE7DyB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SAAEsC,EAASrE,eACvE6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,yCAAAM,OACa,QAA1BiC,EAAS7D,aAAyB,8BACR,aAA1B6D,EAAS7D,aAA8B,gCACb,SAA1B6D,EAAS7D,aAA0B,0BACnC,6BACCuB,SAAA,CACAjD,EAAEuF,EAAS7D,aAAc6D,EAAS7D,cAAc,IAAE1B,EAAE,UAAW,eAElEkD,EAAAA,EAAAA,KAAA,UAAQF,UAAU,wDAAuDC,SACtEjD,EAAE,eAAgB,yBAhBfuF,EAAStE,UAyBZ,mBAAdf,IACC6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEjD,EAAE,4BAA6B,iCAElCkD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEpB,EAAqBsB,IAAKqC,IACzBtC,EAAAA,EAAAA,KAAA,OAEEF,UAAS,yDAAAM,OACPjD,EAAcI,eAAegF,SAASD,EAAcvE,IAChD,iDACA,8DAENoC,QAASA,KACP,MAAMqC,EAAarF,EAAcI,eAAegF,SAASD,EAAcvE,IACvEX,EAAiBkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBD,GAAI,IACP/B,eAAgBiF,EACZlD,EAAK/B,eAAekD,OAAOgC,GAAKA,IAAMH,EAAcvE,IACpD,IAAIuB,EAAK/B,eAAgB+E,EAAcvE,QAE7CgC,UAEFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAEuC,EAAczE,QAC9CmC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,SAAEuC,EAAc1E,SAC/EoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAEuC,EAActE,kBAnBtEsE,EAAcvE,WA2Bb,aAAdf,GAA0C,aAAdA,KAC5B6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SACb,aAAd/C,EAA2B,eAAO,kBAErCgD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACvD,aAAd/C,EAA2BF,EAAE,kBAAmB,oBAAsBA,EAAE,mBAAoB,wBAE/FkD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC9B,aAAd/C,EACGF,EAAE,qBAAsB,0CACxBA,EAAE,qBAAsB,sDAQpC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMtB,EAAqBjC,GACpCkD,UAAU,wIAAuIC,SAEhJjD,EAAE,iBAAkB,sBAEvBkD,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAkB,OAAZtD,QAAY,IAAZA,OAAY,EAAZA,EAAeM,GAC9B2C,UAAU,wIAAuIC,SAEhJjD,EAAE,oBAAqB,+B", "sources": ["components/SpecialNeeds/AdaptiveTreatmentPlan.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst AdaptiveTreatmentPlan = ({ patientProfile, onPlanUpdate }) => {\n  const { t } = useLanguage();\n  const [activeTab, setActiveTab] = useState('goals');\n  const [treatmentPlan, setTreatmentPlan] = useState({\n    goals: [],\n    activities: [],\n    accommodations: [],\n    schedule: {},\n    progressMetrics: []\n  });\n\n  const goalCategories = {\n    motor: {\n      label: t('motorSkills', 'Motor Skills'),\n      icon: '🏃',\n      color: 'bg-blue-100 border-blue-300',\n      goals: [\n        { id: 'gross_motor', label: t('grossMotor', 'Gross Motor Development'), description: t('grossMotorDesc', 'Large muscle movements and coordination') },\n        { id: 'fine_motor', label: t('fineMotor', 'Fine Motor Development'), description: t('fineMotorDesc', 'Small muscle control and dexterity') },\n        { id: 'balance', label: t('balance', 'Balance and Stability'), description: t('balanceDesc', 'Postural control and equilibrium') },\n        { id: 'coordination', label: t('coordination', 'Coordination'), description: t('coordinationDesc', 'Movement planning and execution') }\n      ]\n    },\n    sensory: {\n      label: t('sensoryIntegration', 'Sensory Integration'),\n      icon: '👁️',\n      color: 'bg-purple-100 border-purple-300',\n      goals: [\n        { id: 'tactile_processing', label: t('tactileProcessing', 'Tactile Processing'), description: t('tactileDesc', 'Touch sensitivity and discrimination') },\n        { id: 'vestibular_processing', label: t('vestibularProcessing', 'Vestibular Processing'), description: t('vestibularDesc', 'Movement and balance processing') },\n        { id: 'proprioceptive', label: t('proprioceptive', 'Proprioceptive Awareness'), description: t('proprioceptiveDesc', 'Body position awareness') },\n        { id: 'sensory_modulation', label: t('sensoryModulation', 'Sensory Modulation'), description: t('sensoryModulationDesc', 'Regulating sensory input') }\n      ]\n    },\n    communication: {\n      label: t('communication', 'Communication'),\n      icon: '💬',\n      color: 'bg-green-100 border-green-300',\n      goals: [\n        { id: 'verbal_expression', label: t('verbalExpression', 'Verbal Expression'), description: t('verbalExpressionDesc', 'Speaking and articulation') },\n        { id: 'nonverbal_communication', label: t('nonverbalCommunication', 'Non-verbal Communication'), description: t('nonverbalDesc', 'Gestures and body language') },\n        { id: 'aac_usage', label: t('aacUsage', 'AAC Device Usage'), description: t('aacUsageDesc', 'Alternative communication methods') },\n        { id: 'social_communication', label: t('socialCommunication', 'Social Communication'), description: t('socialCommDesc', 'Interactive communication skills') }\n      ]\n    },\n    behavioral: {\n      label: t('behavioral', 'Behavioral'),\n      icon: '🎯',\n      color: 'bg-yellow-100 border-yellow-300',\n      goals: [\n        { id: 'self_regulation', label: t('selfRegulation', 'Self-Regulation'), description: t('selfRegulationDesc', 'Managing emotions and behaviors') },\n        { id: 'attention_focus', label: t('attentionFocus', 'Attention and Focus'), description: t('attentionDesc', 'Sustained attention skills') },\n        { id: 'social_skills', label: t('socialSkills', 'Social Skills'), description: t('socialSkillsDesc', 'Interaction with others') },\n        { id: 'independence', label: t('independence', 'Independence'), description: t('independenceDesc', 'Self-care and autonomy') }\n      ]\n    }\n  };\n\n  const adaptiveActivities = {\n    autism: [\n      { id: 'structured_play', name: t('structuredPlay', 'Structured Play'), duration: 15, sensoryLevel: 'low', description: t('structuredPlayDesc', 'Predictable play activities with clear rules') },\n      { id: 'sensory_breaks', name: t('sensoryBreaks', 'Sensory Breaks'), duration: 5, sensoryLevel: 'calming', description: t('sensoryBreaksDesc', 'Calming activities to regulate sensory input') },\n      { id: 'visual_schedules', name: t('visualSchedules', 'Visual Schedule Review'), duration: 10, sensoryLevel: 'low', description: t('visualSchedulesDesc', 'Review upcoming activities with pictures') },\n      { id: 'social_stories', name: t('socialStories', 'Social Stories'), duration: 10, sensoryLevel: 'low', description: t('socialStoriesDesc', 'Stories about social situations and expectations') }\n    ],\n    cerebral_palsy: [\n      { id: 'range_of_motion', name: t('rangeOfMotion', 'Range of Motion'), duration: 20, sensoryLevel: 'moderate', description: t('romDesc', 'Gentle stretching and joint mobility') },\n      { id: 'strength_training', name: t('strengthTraining', 'Strength Training'), duration: 15, sensoryLevel: 'moderate', description: t('strengthDesc', 'Muscle strengthening exercises') },\n      { id: 'gait_training', name: t('gaitTraining', 'Gait Training'), duration: 20, sensoryLevel: 'high', description: t('gaitDesc', 'Walking and mobility practice') },\n      { id: 'positioning', name: t('positioning', 'Positioning'), duration: 10, sensoryLevel: 'low', description: t('positioningDesc', 'Proper body positioning and support') }\n    ],\n    down_syndrome: [\n      { id: 'motor_planning', name: t('motorPlanning', 'Motor Planning'), duration: 15, sensoryLevel: 'moderate', description: t('motorPlanningDesc', 'Planning and executing movements') },\n      { id: 'balance_activities', name: t('balanceActivities', 'Balance Activities'), duration: 15, sensoryLevel: 'moderate', description: t('balanceActivitiesDesc', 'Static and dynamic balance training') },\n      { id: 'cognitive_motor', name: t('cognitiveMotor', 'Cognitive-Motor Tasks'), duration: 20, sensoryLevel: 'moderate', description: t('cognitiveMotorDesc', 'Thinking while moving activities') },\n      { id: 'functional_skills', name: t('functionalSkills', 'Functional Skills'), duration: 25, sensoryLevel: 'low', description: t('functionalSkillsDesc', 'Daily living movement skills') }\n    ]\n  };\n\n  const accommodationOptions = [\n    { id: 'extra_time', label: t('extraTime', 'Extra Time'), icon: '⏰', description: t('extraTimeDesc', 'Additional time for task completion') },\n    { id: 'visual_cues', label: t('visualCues', 'Visual Cues'), icon: '👁️', description: t('visualCuesDesc', 'Picture prompts and visual supports') },\n    { id: 'sensory_tools', label: t('sensoryTools', 'Sensory Tools'), icon: '🧸', description: t('sensoryToolsDesc', 'Fidgets, weighted items, sensory aids') },\n    { id: 'quiet_space', label: t('quietSpace', 'Quiet Space'), icon: '🤫', description: t('quietSpaceDesc', 'Low-stimulation environment option') },\n    { id: 'movement_breaks', label: t('movementBreaks', 'Movement Breaks'), icon: '🏃', description: t('movementBreaksDesc', 'Regular movement and stretch breaks') },\n    { id: 'simplified_instructions', label: t('simplifiedInstructions', 'Simplified Instructions'), icon: '📝', description: t('simplifiedDesc', 'Clear, step-by-step directions') },\n    { id: 'peer_support', label: t('peerSupport', 'Peer Support'), icon: '👥', description: t('peerSupportDesc', 'Buddy system or group activities') },\n    { id: 'assistive_technology', label: t('assistiveTechnology', 'Assistive Technology'), icon: '📱', description: t('assistiveTechDesc', 'Communication devices and mobility aids') }\n  ];\n\n  useEffect(() => {\n    if (patientProfile) {\n      generateAdaptivePlan(patientProfile);\n    }\n  }, [patientProfile]);\n\n  const generateAdaptivePlan = (profile) => {\n    const diagnosis = profile.primaryDiagnosis;\n    const suggestedActivities = adaptiveActivities[diagnosis] || [];\n    \n    // Generate goals based on diagnosis and profile\n    const suggestedGoals = generateGoalsForDiagnosis(diagnosis);\n    \n    // Generate accommodations based on sensory profile\n    const suggestedAccommodations = generateAccommodations(profile);\n    \n    setTreatmentPlan(prev => ({\n      ...prev,\n      activities: suggestedActivities,\n      goals: suggestedGoals,\n      accommodations: suggestedAccommodations\n    }));\n  };\n\n  const generateGoalsForDiagnosis = (diagnosis) => {\n    const goalMap = {\n      autism: ['sensory_modulation', 'social_communication', 'self_regulation', 'fine_motor'],\n      cerebral_palsy: ['gross_motor', 'balance', 'coordination', 'independence'],\n      down_syndrome: ['motor_planning', 'balance', 'cognitive_motor', 'social_skills'],\n      intellectual_disability: ['functional_skills', 'independence', 'social_skills', 'communication']\n    };\n    \n    return goalMap[diagnosis] || [];\n  };\n\n  const generateAccommodations = (profile) => {\n    const accommodations = [];\n    \n    if (profile.sensoryProfile === 'sensory_seeking') {\n      accommodations.push('sensory_tools', 'movement_breaks');\n    }\n    if (profile.sensoryProfile === 'sensory_avoiding') {\n      accommodations.push('quiet_space', 'visual_cues');\n    }\n    if (profile.communicationLevel === 'non_verbal') {\n      accommodations.push('visual_cues', 'assistive_technology');\n    }\n    \n    accommodations.push('extra_time', 'simplified_instructions');\n    \n    return accommodations;\n  };\n\n  const addGoal = (categoryId, goalId) => {\n    const category = goalCategories[categoryId];\n    const goal = category.goals.find(g => g.id === goalId);\n    \n    if (goal && !treatmentPlan.goals.find(g => g.id === goalId)) {\n      const newGoal = {\n        ...goal,\n        categoryId,\n        targetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now\n        priority: 'medium',\n        progress: 0,\n        milestones: []\n      };\n      \n      setTreatmentPlan(prev => ({\n        ...prev,\n        goals: [...prev.goals, newGoal]\n      }));\n    }\n  };\n\n  const removeGoal = (goalId) => {\n    setTreatmentPlan(prev => ({\n      ...prev,\n      goals: prev.goals.filter(g => g.id !== goalId)\n    }));\n  };\n\n  const updateGoalProgress = (goalId, progress) => {\n    setTreatmentPlan(prev => ({\n      ...prev,\n      goals: prev.goals.map(g => \n        g.id === goalId ? { ...g, progress } : g\n      )\n    }));\n  };\n\n  const tabs = [\n    { id: 'goals', label: t('treatmentGoals', 'Treatment Goals'), icon: '🎯' },\n    { id: 'activities', label: t('activities', 'Activities'), icon: '🎮' },\n    { id: 'accommodations', label: t('accommodations', 'Accommodations'), icon: '🛠️' },\n    { id: 'schedule', label: t('schedule', 'Schedule'), icon: '📅' },\n    { id: 'progress', label: t('progress', 'Progress'), icon: '📊' }\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <span className=\"mr-3\">🎯</span>\n          {t('adaptiveTreatmentPlan', 'Adaptive Treatment Plan')}\n        </h2>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm font-medium\">\n            {patientProfile?.primaryDiagnosis ? t(patientProfile.primaryDiagnosis) : t('general', 'General')}\n          </span>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-8 overflow-x-auto\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <span>{tab.icon}</span>\n              <span>{tab.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"space-y-6\">\n        {activeTab === 'goals' && (\n          <div className=\"space-y-6\">\n            {/* Current Goals */}\n            {treatmentPlan.goals.length > 0 && (\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                  {t('currentGoals', 'Current Goals')}\n                </h3>\n                <div className=\"space-y-4\">\n                  {treatmentPlan.goals.map((goal) => (\n                    <div key={goal.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h4 className=\"font-medium text-gray-900 dark:text-white\">{goal.label}</h4>\n                        <button\n                          onClick={() => removeGoal(goal.id)}\n                          className=\"text-red-500 hover:text-red-700 text-sm\"\n                        >\n                          ✕\n                        </button>\n                      </div>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">{goal.description}</p>\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex-1 mr-4\">\n                          <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                            <span>{t('progress', 'Progress')}</span>\n                            <span>{goal.progress}%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                            <div \n                              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                              style={{ width: `${goal.progress}%` }}\n                            />\n                          </div>\n                        </div>\n                        <input\n                          type=\"range\"\n                          min=\"0\"\n                          max=\"100\"\n                          value={goal.progress}\n                          onChange={(e) => updateGoalProgress(goal.id, parseInt(e.target.value))}\n                          className=\"w-20\"\n                        />\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Add New Goals */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('addNewGoals', 'Add New Goals')}\n              </h3>\n              <div className=\"space-y-6\">\n                {Object.entries(goalCategories).map(([categoryId, category]) => (\n                  <div key={categoryId} className={`p-4 border-2 border-dashed rounded-lg ${category.color}`}>\n                    <h4 className=\"font-medium text-gray-900 mb-3 flex items-center\">\n                      <span className=\"mr-2\">{category.icon}</span>\n                      {category.label}\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                      {category.goals.map((goal) => (\n                        <button\n                          key={goal.id}\n                          onClick={() => addGoal(categoryId, goal.id)}\n                          disabled={treatmentPlan.goals.find(g => g.id === goal.id)}\n                          className={`p-3 text-left rounded-lg border transition-colors ${\n                            treatmentPlan.goals.find(g => g.id === goal.id)\n                              ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed'\n                              : 'bg-white border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n                          }`}\n                        >\n                          <div className=\"font-medium text-sm text-gray-900\">{goal.label}</div>\n                          <div className=\"text-xs text-gray-600 mt-1\">{goal.description}</div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'activities' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('recommendedActivities', 'Recommended Activities')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {treatmentPlan.activities.map((activity) => (\n                <div key={activity.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">{activity.name}</h4>\n                    <span className=\"text-sm text-gray-500\">{activity.duration} min</span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">{activity.description}</p>\n                  <div className=\"flex items-center justify-between\">\n                    <span className={`px-2 py-1 rounded text-xs font-medium ${\n                      activity.sensoryLevel === 'low' ? 'bg-green-100 text-green-800' :\n                      activity.sensoryLevel === 'moderate' ? 'bg-yellow-100 text-yellow-800' :\n                      activity.sensoryLevel === 'high' ? 'bg-red-100 text-red-800' :\n                      'bg-blue-100 text-blue-800'\n                    }`}>\n                      {t(activity.sensoryLevel, activity.sensoryLevel)} {t('sensory', 'sensory')}\n                    </span>\n                    <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                      {t('addToSession', 'Add to Session')}\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'accommodations' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('recommendedAccommodations', 'Recommended Accommodations')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {accommodationOptions.map((accommodation) => (\n                <div\n                  key={accommodation.id}\n                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${\n                    treatmentPlan.accommodations.includes(accommodation.id)\n                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                  }`}\n                  onClick={() => {\n                    const isSelected = treatmentPlan.accommodations.includes(accommodation.id);\n                    setTreatmentPlan(prev => ({\n                      ...prev,\n                      accommodations: isSelected\n                        ? prev.accommodations.filter(a => a !== accommodation.id)\n                        : [...prev.accommodations, accommodation.id]\n                    }));\n                  }}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-2\">{accommodation.icon}</div>\n                    <div className=\"font-medium text-gray-900 dark:text-white mb-1\">{accommodation.label}</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">{accommodation.description}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {(activeTab === 'schedule' || activeTab === 'progress') && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">\n              {activeTab === 'schedule' ? '📅' : '📊'}\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              {activeTab === 'schedule' ? t('scheduleBuilder', 'Schedule Builder') : t('progressTracking', 'Progress Tracking')}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {activeTab === 'schedule' \n                ? t('scheduleComingSoon', 'Visual schedule builder coming soon...')\n                : t('progressComingSoon', 'Advanced progress tracking coming soon...')\n              }\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Save Button */}\n      <div className=\"mt-8 flex justify-end space-x-4\">\n        <button\n          onClick={() => generateAdaptivePlan(patientProfile)}\n          className=\"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n        >\n          {t('regeneratePlan', 'Regenerate Plan')}\n        </button>\n        <button\n          onClick={() => onPlanUpdate?.(treatmentPlan)}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n        >\n          {t('saveTreatmentPlan', 'Save Treatment Plan')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default AdaptiveTreatmentPlan;\n"], "names": ["_ref", "patientProfile", "onPlanUpdate", "t", "useLanguage", "activeTab", "setActiveTab", "useState", "treatmentPlan", "setTreatmentPlan", "goals", "activities", "accommodations", "schedule", "progressMetrics", "goalCategories", "motor", "label", "icon", "color", "id", "description", "sensory", "communication", "behavioral", "adaptiveActivities", "autism", "name", "duration", "sensoryLevel", "cerebral_palsy", "down_syndrome", "accommodationOptions", "useEffect", "generateAdaptivePlan", "profile", "diagnosis", "primaryDiagnosis", "suggestedActivities", "suggestedGoals", "generateGoalsForDiagnosis", "suggestedAccommodations", "generateAccommodations", "prev", "_objectSpread", "intellectual_disability", "sensoryProfile", "push", "communicationLevel", "tabs", "_jsxs", "className", "children", "_jsx", "map", "tab", "onClick", "concat", "length", "goal", "removeGoal", "goalId", "filter", "g", "progress", "style", "width", "type", "min", "max", "value", "onChange", "e", "updateGoalProgress", "parseInt", "target", "Object", "entries", "_ref2", "categoryId", "category", "addGoal", "find", "newGoal", "targetDate", "Date", "now", "priority", "milestones", "disabled", "activity", "accommodation", "includes", "isSelected", "a"], "sourceRoot": ""}