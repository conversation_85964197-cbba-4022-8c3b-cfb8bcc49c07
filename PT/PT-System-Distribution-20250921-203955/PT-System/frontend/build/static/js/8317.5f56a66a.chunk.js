"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8317],{2945:(e,a,r)=>{r.d(a,{A:()=>n});var t=r(2555),s=r(5043),l=r(7921),i=r(3768),d=r(579);const n=e=>{let{onSubmit:a,onCancel:r,initialData:n=null}=e;const{t:o,isRTL:c}=(0,l.o)(),[m,x]=(0,s.useState)(!1),[p,g]=(0,s.useState)({patientId:(null===n||void 0===n?void 0:n.patientId)||"",patientName:(null===n||void 0===n?void 0:n.patientName)||"",date:(null===n||void 0===n?void 0:n.date)||(new Date).toISOString().split("T")[0],time:(null===n||void 0===n?void 0:n.time)||"",duration:(null===n||void 0===n?void 0:n.duration)||"60",type:(null===n||void 0===n?void 0:n.type)||"consultation",therapist:(null===n||void 0===n?void 0:n.therapist)||"",location:(null===n||void 0===n?void 0:n.location)||"room_1",notes:(null===n||void 0===n?void 0:n.notes)||"",priority:(null===n||void 0===n?void 0:n.priority)||"normal",reminderEnabled:(null===n||void 0===n?void 0:n.reminderEnabled)||!0,reminderTime:(null===n||void 0===n?void 0:n.reminderTime)||"24"}),[u,h]=(0,s.useState)({}),b=[{value:"consultation",label:o("consultation","Consultation")},{value:"assessment",label:o("assessment","Assessment")},{value:"therapy_session",label:o("therapySession","Therapy Session")},{value:"follow_up",label:o("followUp","Follow-up")},{value:"group_therapy",label:o("groupTherapy","Group Therapy")},{value:"evaluation",label:o("evaluation","Evaluation")}],y=[{value:"room_1",label:o("room1","Room 1 - Main Therapy")},{value:"room_2",label:o("room2","Room 2 - Assessment")},{value:"room_3",label:o("room3","Room 3 - Group Therapy")},{value:"gym",label:o("gym","Gymnasium")},{value:"pool",label:o("pool","Hydrotherapy Pool")}],v=(e,a)=>{g(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a})),u[e]&&h(a=>(0,t.A)((0,t.A)({},a),{},{[e]:null}))};return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:n?o("editAppointment","Edit Appointment"):o("newAppointment","New Appointment")}),r&&(0,d.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,d.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return p.patientName.trim()||(e.patientName=o("patientNameRequired","Patient name is required")),p.date||(e.date=o("dateRequired","Date is required")),p.time||(e.time=o("timeRequired","Time is required")),p.therapist||(e.therapist=o("therapistRequired","Therapist is required")),h(e),0===Object.keys(e).length})()){x(!0);try{await new Promise(e=>setTimeout(e,1e3)),a&&a(p),i.Ay.success(o("appointmentSaved","Appointment saved successfully"))}catch(r){i.Ay.error(o("errorSavingAppointment","Error saving appointment"))}finally{x(!1)}}else i.Ay.error(o("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("patientName","Patient Name")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:p.patientName,onChange:e=>v("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(u.patientName?"border-red-500":"border-gray-300"),placeholder:o("enterPatientName","Enter patient name")}),u.patientName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.patientName})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("appointmentType","Appointment Type")}),(0,d.jsx)("select",{value:p.type,onChange:e=>v("type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:b.map(e=>(0,d.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("date","Date")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"date",value:p.date,onChange:e=>v("date",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(u.date?"border-red-500":"border-gray-300")}),u.date&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.date})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("time","Time")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("select",{value:p.time,onChange:e=>v("time",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(u.time?"border-red-500":"border-gray-300"),children:[(0,d.jsx)("option",{value:"",children:o("selectTime","Select time")}),["08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00"].map(e=>(0,d.jsx)("option",{value:e,children:e},e))]}),u.time&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.time})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("duration","Duration (minutes)")}),(0,d.jsxs)("select",{value:p.duration,onChange:e=>v("duration",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,d.jsxs)("option",{value:"30",children:["30 ",o("minutes","minutes")]}),(0,d.jsxs)("option",{value:"45",children:["45 ",o("minutes","minutes")]}),(0,d.jsxs)("option",{value:"60",children:["60 ",o("minutes","minutes")]}),(0,d.jsxs)("option",{value:"90",children:["90 ",o("minutes","minutes")]}),(0,d.jsxs)("option",{value:"120",children:["120 ",o("minutes","minutes")]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("therapist","Therapist")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("select",{value:p.therapist,onChange:e=>v("therapist",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(u.therapist?"border-red-500":"border-gray-300"),children:[(0,d.jsx)("option",{value:"",children:o("selectTherapist","Select therapist")}),[{value:"dr_sarah",label:"Dr. Sarah Al-Rashid"},{value:"dr_ahmed",label:"Dr. Ahmed Al-Mansouri"},{value:"dr_fatima",label:"Dr. Fatima Al-Zahra"},{value:"dr_mohammed",label:"Dr. Mohammed Al-Khalid"}].map(e=>(0,d.jsx)("option",{value:e.value,children:e.label},e.value))]}),u.therapist&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.therapist})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("location","Location")}),(0,d.jsx)("select",{value:p.location,onChange:e=>v("location",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:y.map(e=>(0,d.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("priority","Priority")}),(0,d.jsxs)("select",{value:p.priority,onChange:e=>v("priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,d.jsx)("option",{value:"low",children:o("low","Low")}),(0,d.jsx)("option",{value:"normal",children:o("normal","Normal")}),(0,d.jsx)("option",{value:"high",children:o("high","High")}),(0,d.jsx)("option",{value:"urgent",children:o("urgent","Urgent")})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("reminder","Reminder")}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:p.reminderEnabled,onChange:e=>v("reminderEnabled",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("select",{value:p.reminderTime,onChange:e=>v("reminderTime",e.target.value),disabled:!p.reminderEnabled,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50",children:[(0,d.jsxs)("option",{value:"15",children:["15 ",o("minutesBefore","minutes before")]}),(0,d.jsxs)("option",{value:"30",children:["30 ",o("minutesBefore","minutes before")]}),(0,d.jsxs)("option",{value:"60",children:["1 ",o("hourBefore","hour before")]}),(0,d.jsxs)("option",{value:"1440",children:["1 ",o("dayBefore","day before")]})]})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("notes","Notes")}),(0,d.jsx)("textarea",{value:p.notes,onChange:e=>v("notes",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:o("enterNotes","Enter any additional notes")})]}),(0,d.jsxs)("div",{className:"flex justify-end space-x-4",children:[r&&(0,d.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:o("cancel","Cancel")}),(0,d.jsx)("button",{type:"submit",disabled:m,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:m?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),o("saving","Saving...")]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),o("saveAppointment","Save Appointment")]})})]})]})]})}},8317:(e,a,r)=>{r.r(a),r.d(a,{default:()=>o});var t=r(5043),s=r(3216),l=r(5475),i=r(7921),d=r(2945),n=r(579);const o=()=>{const{t:e,isRTL:a}=(0,i.o)(),r=(0,s.Zp)(),[o]=(0,l.ok)(),[c,m]=(0,t.useState)(null);(0,t.useEffect)(()=>{const e=o.get("patientId"),a=o.get("patientName");e&&a&&m({patientId:e,patientName:decodeURIComponent(a),date:(new Date).toISOString().split("T")[0],time:"09:00",duration:60,priority:"normal"})},[o]);return(0,n.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("scheduleNewAppointment","Schedule New Appointment")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("scheduleNewAppointmentDesc","Create a new appointment for a patient")})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsxs)("button",{onClick:()=>r("/appointments"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-list mr-2"}),e("viewAppointments","View Appointments")]}),(0,n.jsxs)("button",{onClick:()=>r("/appointments/calendar"),className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-calendar mr-2"}),e("calendarView","Calendar View")]})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,n.jsx)("i",{className:"fas fa-calendar-plus text-blue-600 dark:text-blue-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("newAppointment","New Appointment")}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("schedulePatient","Schedule a patient")})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,n.jsx)("i",{className:"fas fa-user-md text-green-600 dark:text-green-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"4"}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("availableTherapists","Available Therapists")})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,n.jsx)("i",{className:"fas fa-door-open text-purple-600 dark:text-purple-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"5"}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("availableRooms","Available Rooms")})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,n.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"12"}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("availableSlots","Available Time Slots")})]})]})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsx)("div",{className:"lg:col-span-2",children:(0,n.jsx)(d.A,{initialData:c,onSubmit:e=>{console.log("New appointment created:",e),r("/appointments")},onCancel:()=>{r("/appointments")}})}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,n.jsx)("i",{className:"fas fa-lightbulb text-yellow-500 mr-2"}),e("quickTips","Quick Tips")]}),(0,n.jsxs)("div",{className:"space-y-3 text-sm",children:[(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-2 mt-0.5"}),(0,n.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:e("tip1","Select the appropriate therapy type for the patient's condition")})]}),(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-2 mt-0.5"}),(0,n.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:e("tip2","Consider the patient's availability and preferences")})]}),(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-2 mt-0.5"}),(0,n.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:e("tip3","Set reminders to ensure patients don't miss appointments")})]}),(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-2 mt-0.5"}),(0,n.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:e("tip4","Add detailed notes for better session preparation")})]})]})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,n.jsx)("i",{className:"fas fa-user-md text-blue-500 mr-2"}),e("availableTherapists","Available Therapists")]}),(0,n.jsx)("div",{className:"space-y-3",children:[{name:"Dr. Sarah Al-Rashid",specialty:"Physical Therapy",available:!0},{name:"Dr. Ahmed Al-Mansouri",specialty:"Occupational Therapy",available:!0},{name:"Dr. Fatima Al-Zahra",specialty:"Speech Therapy",available:!0},{name:"Dr. Mohammed Al-Khalid",specialty:"Group Therapy",available:!1}].map((e,a)=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),(0,n.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.specialty})]}),(0,n.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.available?"bg-green-500":"bg-red-500")})]},a))})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,n.jsx)("i",{className:"fas fa-history text-purple-500 mr-2"}),e("recentAppointments","Recent Appointments")]}),(0,n.jsx)("div",{className:"space-y-3",children:[{patient:"Ahmed Al-Ahmed",time:"10:00",type:"Physical Therapy"},{patient:"Fatima Al-Salem",time:"11:30",type:"Occupational Therapy"},{patient:"Mohammed Al-Khalid",time:"14:00",type:"Speech Therapy"}].map((e,a)=>(0,n.jsxs)("div",{className:"p-2 border border-gray-200 dark:border-gray-600 rounded",children:[(0,n.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.patient}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e.time," - ",e.type]})]},a))})]})]})]})]})}}}]);
//# sourceMappingURL=8317.5f56a66a.chunk.js.map