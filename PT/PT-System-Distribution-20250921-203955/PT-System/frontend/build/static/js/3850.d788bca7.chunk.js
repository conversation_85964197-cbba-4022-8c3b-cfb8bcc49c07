"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3850],{3850:(e,a,t)=>{t.r(a),t.d(a,{default:()=>p});var r=t(2555),s=t(5043),i=t(3216),l=t(7921),n=t(4528),o=t(3768),c=t(579);const d=e=>{let{formData:a,handleInputChange:t,errors:r}=e;const{t:s}=(0,l.o)(),i=[{value:"Initial Assessment",label:s("initialAssessment","Initial Assessment")},{value:"Re-Assessment",label:s("reAssessment","Re-Assessment")},{value:"Discharge Assessment",label:s("dischargeAssessment","Discharge Assessment")}];return(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,c.jsx)("i",{className:"fas fa-file-alt text-blue-600 dark:text-blue-400 mr-2"}),s("documentInformation","Document Information")]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:s("documentNumber","Document Number")}),(0,c.jsx)("input",{type:"text",value:a.documentNumber,onChange:e=>t("documentNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"QP-"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:s("issueDate","Issue Date")}),(0,c.jsx)("input",{type:"date",value:a.issueDate,onChange:e=>t("issueDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:s("version","Version")}),(0,c.jsx)("input",{type:"text",value:a.version,onChange:e=>t("version",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"01"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:s("reviewNumber","Review Number")}),(0,c.jsx)("input",{type:"text",value:a.reviewNumber,onChange:e=>t("reviewNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"01"})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,c.jsx)("i",{className:"fas fa-clipboard-check text-green-600 dark:text-green-400 mr-2"}),s("assessmentType","Assessment Type")]}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:[s("selectAssessmentType","Please select the type of assessment being conducted")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:i.map(e=>(0,c.jsxs)("label",{className:"flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors ".concat(a.assessmentType===e.value?"border-green-500 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200":"border-gray-300 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-500"),children:[(0,c.jsx)("input",{type:"radio",name:"assessmentType",value:e.value,checked:a.assessmentType===e.value,onChange:e=>t("assessmentType",e.target.value),className:"sr-only"}),(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("div",{className:"w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ".concat(a.assessmentType===e.value?"border-green-500 bg-green-500":"border-gray-300 dark:border-gray-600"),children:a.assessmentType===e.value&&(0,c.jsx)("div",{className:"w-2 h-2 rounded-full bg-white"})}),(0,c.jsx)("span",{className:"font-medium",children:e.label})]})]},e.value))}),r.assessmentType&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-2",children:r.assessmentType})]})]})]})},b=e=>{let{formData:a,handleInputChange:t,handleBarrierChange:r}=e;const{t:s}=(0,l.o)(),i=[{key:"video",label:s("video","Video")},{key:"writtenMaterialsWithPicture",label:s("writtenMaterialsWithPicture","Written Materials with Picture")},{key:"demonstration",label:s("demonstration","Demonstration")},{key:"other",label:s("other","Other")}],n=[{key:"physical",label:s("physical","Physical")},{key:"cognitive",label:s("cognitive","Cognitive")},{key:"emotional",label:s("emotional","Emotional")},{key:"culturalBelieve",label:s("culturalBelieve","Cultural/Believe")},{key:"poorMotivation",label:s("poorMotivation","Poor Motivation")},{key:"financialDifficulties",label:s("financialDifficulties","Financial Difficulties")},{key:"readingAbility",label:s("readingAbility","Reading Ability")},{key:"psychological",label:s("psychological","Psychological")},{key:"language",label:s("language","Language")},{key:"impairedHearing",label:s("impairedHearing","Impaired Hearing")},{key:"speechBarriers",label:s("speechBarriers","Speech Barriers")},{key:"responsibilitiesAtHome",label:s("responsibilitiesAtHome","Responsibilities at Home")},{key:"religiousPractice",label:s("religiousPractice","Religious Practice")},{key:"impairedVision",label:s("impairedVision","Impaired Vision")},{key:"age",label:s("age","Age")},{key:"others",label:s("others","Others")},{key:"noBarrier",label:s("noBarrier","No Barrier")}],o=(e,r,l,n)=>(0,c.jsxs)("div",{className:"bg-".concat(n,"-50 dark:bg-").concat(n,"-900/20 border border-").concat(n,"-200 dark:border-").concat(n,"-800 rounded-lg p-6"),children:[(0,c.jsxs)("h3",{className:"text-md font-semibold text-".concat(n,"-900 dark:text-").concat(n,"-100 mb-4"),children:[(0,c.jsx)("i",{className:"".concat(l," text-").concat(n,"-600 dark:text-").concat(n,"-400 mr-2")}),r]}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:i.map(r=>(0,c.jsxs)("label",{className:"flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",checked:a[e][r.key],onChange:a=>t("".concat(e,".").concat(r.key),a.target.checked),className:"mr-3 text-".concat(n,"-600 focus:ring-").concat(n,"-500")}),(0,c.jsx)("span",{className:"text-sm text-".concat(n,"-800 dark:text-").concat(n,"-200"),children:r.label})]},r.key))}),a[e].other&&(0,c.jsxs)("div",{className:"mt-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-".concat(n,"-800 dark:text-").concat(n,"-200 mb-1"),children:[s("otherDescription","Other Description"),":"]}),(0,c.jsx)("textarea",{value:a[e].otherDescription,onChange:a=>t("".concat(e,".otherDescription"),a.target.value),rows:2,className:"w-full px-3 py-2 border border-".concat(n,"-300 rounded-lg focus:ring-2 focus:ring-").concat(n,"-500 focus:border-").concat(n,"-500 dark:bg-").concat(n,"-800 dark:border-").concat(n,"-600 dark:text-white"),placeholder:s("describeOtherPreference","Describe other learning preference")})]})]}),d=(e,i,l,o)=>(0,c.jsxs)("div",{className:"bg-".concat(o,"-50 dark:bg-").concat(o,"-900/20 border border-").concat(o,"-200 dark:border-").concat(o,"-800 rounded-lg p-6"),children:[(0,c.jsxs)("h3",{className:"text-md font-semibold text-".concat(o,"-900 dark:text-").concat(o,"-100 mb-4"),children:[(0,c.jsx)("i",{className:"".concat(l," text-").concat(o,"-600 dark:text-").concat(o,"-400 mr-2")}),i]}),(0,c.jsx)("div",{className:"mb-4 p-3 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg",children:(0,c.jsxs)("label",{className:"flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",checked:a[e].noBarrier,onChange:a=>r(e,"noBarrier",a.target.checked),className:"mr-3 text-green-600 focus:ring-green-500"}),(0,c.jsx)("span",{className:"font-medium text-green-800 dark:text-green-200",children:s("noBarrier","No Barrier")})]})}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:n.filter(e=>"noBarrier"!==e.key).map(t=>(0,c.jsxs)("label",{className:"flex items-center ".concat(a[e].noBarrier?"opacity-50":""),children:[(0,c.jsx)("input",{type:"checkbox",checked:a[e][t.key],onChange:a=>r(e,t.key,a.target.checked),disabled:a[e].noBarrier,className:"mr-3 text-".concat(o,"-600 focus:ring-").concat(o,"-500")}),(0,c.jsx)("span",{className:"text-sm text-".concat(o,"-800 dark:text-").concat(o,"-200"),children:t.label})]},t.key))}),a[e].others&&!a[e].noBarrier&&(0,c.jsxs)("div",{className:"mt-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-".concat(o,"-800 dark:text-").concat(o,"-200 mb-1"),children:[s("othersDescription","Others Description"),":"]}),(0,c.jsx)("textarea",{value:a[e].othersDescription,onChange:a=>t("".concat(e,".othersDescription"),a.target.value),rows:2,className:"w-full px-3 py-2 border border-".concat(o,"-300 rounded-lg focus:ring-2 focus:ring-").concat(o,"-500 focus:border-").concat(o,"-500 dark:bg-").concat(o,"-800 dark:border-").concat(o,"-600 dark:text-white"),placeholder:s("describeOtherBarriers","Describe other barriers")})]}),a[e].noBarrier&&(0,c.jsxs)("div",{className:"mt-3 text-sm text-green-700 dark:text-green-300",children:[(0,c.jsx)("i",{className:"fas fa-info-circle mr-1"}),s("noBarrierSelected","No barriers selected - other options are disabled")]})]});return(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-graduation-cap text-purple-600 dark:text-purple-400 mr-2"}),s("learningPreferencesBarriers","Learning Preferences & Barriers")]}),(0,c.jsxs)("div",{className:"space-y-6",children:[o("patientLearningPreference",s("patientLearningPreference","Patient Learning Preference"),"fas fa-user-graduate","blue"),d("patientBarriers",s("patientBarriers","Patient Barriers"),"fas fa-user-times","red"),o("caregiverLearningPreference",s("caregiverLearningPreference","Caregiver Learning Preferences"),"fas fa-users","green"),d("caregiverBarriers",s("caregiverBarriers","Caregiver Barriers"),"fas fa-user-slash","orange")]})]})},m=e=>{let{formData:a,handleInputChange:t,errors:r}=e;const{t:s}=(0,l.o)(),i=[{value:"Dependent",label:s("dependent","Dependent")},{value:"Independent",label:s("independent","Independent")},{value:"Need Assistant",label:s("needAssistant","Need Assistant")},{value:"Not Applicable",label:s("notApplicable","Not Applicable")}],n=[{value:"Poor",label:s("poor","Poor")},{value:"Moderate",label:s("moderate","Moderate")},{value:"High",label:s("high","High")},{value:"Not Applicable",label:s("notApplicable","Not Applicable")}],o=(e,i,l,n,o,d)=>(0,c.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,c.jsxs)("h3",{className:"text-md font-semibold text-gray-900 dark:text-white mb-2",children:[o," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:d}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3",children:i.map(a=>{const r=(e=>{switch(e){case"Independent":case"High":return"green";case"Need Assistant":case"Moderate":return"yellow";case"Dependent":case"Poor":return"red";case"Not Applicable":return"gray";default:return"blue"}})(a.value),s=l===a.value;return(0,c.jsxs)("label",{className:"flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ".concat(s?"border-".concat(r,"-500 bg-").concat(r,"-50 dark:bg-").concat(r,"-900/20 text-").concat(r,"-800 dark:text-").concat(r,"-200"):"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"),children:[(0,c.jsx)("input",{type:"radio",name:e,value:a.value,checked:s,onChange:a=>t(e,a.target.value),className:"sr-only"}),(0,c.jsx)("div",{className:"w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ".concat(s?"border-".concat(r,"-500 bg-").concat(r,"-500"):"border-gray-300 dark:border-gray-600"),children:s&&(0,c.jsx)("div",{className:"w-2 h-2 rounded-full bg-white"})}),(0,c.jsx)("span",{className:"font-medium text-sm",children:a.label})]},a.value)})}),"Not Applicable"===l&&(0,c.jsxs)("div",{className:"mt-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[s("reason","Reason")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("textarea",{value:a["".concat(e,"Reason")]||"",onChange:a=>t("".concat(e,"Reason"),a.target.value),rows:2,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white ".concat(r["".concat(e,"Reason")]?"border-red-500":"border-gray-300"),placeholder:s("explainWhyNotApplicable","Please explain why this is not applicable")}),r["".concat(e,"Reason")]&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r["".concat(e,"Reason")]})]}),r[n]&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-2",children:r[n]})]});return(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-hands-helping text-blue-600 dark:text-blue-400 mr-2"}),s("patientSelfCareAssessment","Patient Self-Care Assessment")]}),(0,c.jsxs)("div",{className:"space-y-6",children:[o("patientSelfCareCapability",i,a.patientSelfCareCapability,"patientSelfCareCapability",s("patientSelfCareCapability","Patient Self-Care Capability"),s("selfCareCapabilityDescription","Assess the patient's ability to perform self-care activities independently")),o("patientSelfCareMotivation",n,a.patientSelfCareMotivation,"patientSelfCareMotivation",s("patientSelfCareMotivation","Patient Self-Care Motivation"),s("selfCareMotivationDescription","Evaluate the patient's motivation level for engaging in self-care activities")),(0,c.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,c.jsxs)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4",children:[(0,c.jsx)("i",{className:"fas fa-comment-alt text-blue-600 dark:text-blue-400 mr-2"}),s("additionalComments","Additional Comments")]}),(0,c.jsx)("textarea",{value:a.comments,onChange:e=>t("comments",e.target.value),rows:4,className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:s("commentsPlaceholder","Add any additional observations, notes, or comments about the patient's self-care assessment")}),(0,c.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-300 mt-2",children:s("commentsHint","Include any relevant observations about the patient's self-care abilities, barriers, or special considerations")})]}),(0,c.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:[(0,c.jsxs)("h4",{className:"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2",children:[(0,c.jsx)("i",{className:"fas fa-lightbulb text-yellow-600 dark:text-yellow-400 mr-1"}),s("assessmentGuidelines","Assessment Guidelines"),":"]}),(0,c.jsxs)("ul",{className:"text-xs text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,c.jsxs)("li",{children:["\u2022 ",(0,c.jsxs)("strong",{children:[s("dependent","Dependent"),":"]})," ",s("dependentDescription","Requires full assistance for self-care activities")]}),(0,c.jsxs)("li",{children:["\u2022 ",(0,c.jsxs)("strong",{children:[s("needAssistant","Need Assistant"),":"]})," ",s("needAssistantDescription","Requires partial assistance or supervision")]}),(0,c.jsxs)("li",{children:["\u2022 ",(0,c.jsxs)("strong",{children:[s("independent","Independent"),":"]})," ",s("independentDescription","Can perform self-care activities without assistance")]}),(0,c.jsxs)("li",{children:["\u2022 ",(0,c.jsxs)("strong",{children:[s("notApplicable","Not Applicable"),":"]})," ",s("notApplicableDescription","Self-care assessment is not relevant (provide reason)")]})]})]})]})]})},u=e=>{let{formData:a,handleInputChange:t}=e;const{t:r}=(0,l.o)(),s=[{key:"diseaseProcess",label:r("diseaseProcess","Disease Process")},{key:"safeEffectiveUseMedication",label:r("safeEffectiveUseMedication","Safe and Effective Use of Medication Side Effects and Interactions")},{key:"safeEffectiveUseMedicalEquipment",label:r("safeEffectiveUseMedicalEquipment","Safe and Effective Use of Medical Equipment")},{key:"painManagement",label:r("painManagement","Pain Management")},{key:"rehabilitationTechnique",label:r("rehabilitationTechnique","Rehabilitation Technique")},{key:"communityResources",label:r("communityResources","Community Resources")},{key:"dischargeInstruction",label:r("dischargeInstruction","Discharge Instruction of Continuing Care")},{key:"patientsRightsResponsibilities",label:r("patientsRightsResponsibilities","Patients' Rights and Responsibilities")},{key:"whenHowObtainFurtherTreatment",label:r("whenHowObtainFurtherTreatment","When and How to Obtain Further Treatment")},{key:"personalHygienePractitionerHandWash",label:r("personalHygienePractitionerHandWash","Personal Hygiene and Practitioner Hand Wash")},{key:"infectionControlRelatedIssues",label:r("infectionControlRelatedIssues","Infection Control and Related Issues")},{key:"carePlanTreatment",label:r("carePlanTreatment","Care Plan and Treatment")},{key:"informedConsentProcedure",label:r("informedConsentProcedure","Informed Consent Procedure")},{key:"homeInstruction",label:r("homeInstruction","Home Instruction")},{key:"diagnosticTestProcedure",label:r("diagnosticTestProcedure","Diagnostic Test/Procedure")},{key:"socialServices",label:r("socialServices","Social Services")},{key:"healthMaintenance",label:r("healthMaintenance","Health Maintenance")},{key:"nutrition",label:r("nutrition","Nutrition")},{key:"risk",label:r("risk","Risk")},{key:"safety",label:r("safety","Safety")},{key:"others",label:r("others","Others (Specify in comment)")}],i=[{key:"oneToOne",label:r("oneToOne","One to One")},{key:"groupTeaching",label:r("groupTeaching","Group Teaching")},{key:"lecture",label:r("lecture","Lecture")}],n=[{key:"writtenMaterials",label:r("writtenMaterials","Written Materials")},{key:"audio",label:r("audio","Audio")},{key:"verbalInstructions",label:r("verbalInstructions","Verbal Instructions")},{key:"writtenInstruction",label:r("writtenInstruction","Written Instruction")},{key:"video",label:r("video","Video")},{key:"demonstration",label:r("demonstration","Demonstration")}],o=[{key:"notReceptiveToLearning",label:r("notReceptiveToLearning","Not Receptive to Learning/No Learning")},{key:"unableToVerbalizeBasicConcepts",label:r("unableToVerbalizeBasicConcepts","Unable to Verbalize Basic Concepts")},{key:"verbalizeBasicConceptsWithAssistance",label:r("verbalizeBasicConceptsWithAssistance","Verbalize Basic Concepts with Assistance")},{key:"verbalizeBasicConcept",label:r("verbalizeBasicConcept","Verbalize Basic Concept")},{key:"returnDemonstration",label:r("returnDemonstration","Return Demonstration")},{key:"appliesKnowledgeSkills",label:r("appliesKnowledgeSkills","Applies Knowledge/skills")},{key:"notApplicable",label:r("notApplicable","Not Applicable")},{key:"needFollowUp",label:r("needFollowUp","Need Follow-up")}],d=(e,s,i,l,n)=>(0,c.jsxs)("div",{className:"bg-".concat(n,"-50 dark:bg-").concat(n,"-900/20 border border-").concat(n,"-200 dark:border-").concat(n,"-800 rounded-lg p-6"),children:[(0,c.jsxs)("h3",{className:"text-md font-semibold text-".concat(n,"-900 dark:text-").concat(n,"-100 mb-4"),children:[(0,c.jsx)("i",{className:"".concat(l," text-").concat(n,"-600 dark:text-").concat(n,"-400 mr-2")}),e]}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:s.map(e=>(0,c.jsxs)("label",{className:"flex items-start",children:[(0,c.jsx)("input",{type:"checkbox",checked:a[i][e.key],onChange:a=>t("".concat(i,".").concat(e.key),a.target.checked),className:"mt-1 mr-3 text-".concat(n,"-600 focus:ring-").concat(n,"-500")}),(0,c.jsx)("span",{className:"text-sm text-".concat(n,"-800 dark:text-").concat(n,"-200 leading-tight"),children:e.label})]},e.key))}),"educationalNeeds"===i&&a[i].others&&(0,c.jsxs)("div",{className:"mt-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-".concat(n,"-800 dark:text-").concat(n,"-200 mb-1"),children:[r("othersDescription","Others Description"),":"]}),(0,c.jsx)("textarea",{value:a[i].othersDescription,onChange:e=>t("".concat(i,".othersDescription"),e.target.value),rows:2,className:"w-full px-3 py-2 border border-".concat(n,"-300 rounded-lg focus:ring-2 focus:ring-").concat(n,"-500 focus:border-").concat(n,"-500 dark:bg-").concat(n,"-800 dark:border-").concat(n,"-600 dark:text-white"),placeholder:r("specifyOtherEducationalNeeds","Specify other educational needs")})]})]});return(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-book-open text-indigo-600 dark:text-indigo-400 mr-2"}),r("educationalNeedsGivenEducation","Educational Needs & Given Education")]}),(0,c.jsxs)("div",{className:"space-y-6",children:[d(r("educationalNeeds","Educational Needs"),s,"educationalNeeds","fas fa-list-check","purple"),(0,c.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,c.jsxs)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-6",children:[(0,c.jsx)("i",{className:"fas fa-chalkboard-teacher text-blue-600 dark:text-blue-400 mr-2"}),r("givenEducation","Given Education")]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h4",{className:"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3",children:r("teachingMethod","Teaching Method")}),(0,c.jsx)("div",{className:"space-y-2",children:i.map(e=>(0,c.jsxs)("label",{className:"flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",checked:a.teachingMethod[e.key],onChange:a=>t("teachingMethod.".concat(e.key),a.target.checked),className:"mr-3 text-blue-600 focus:ring-blue-500"}),(0,c.jsx)("span",{className:"text-sm text-blue-800 dark:text-blue-200",children:e.label})]},e.key))})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h4",{className:"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3",children:r("teachingTool","Teaching Tool")}),(0,c.jsx)("div",{className:"space-y-2",children:n.map(e=>(0,c.jsxs)("label",{className:"flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",checked:a.teachingTool[e.key],onChange:a=>t("teachingTool.".concat(e.key),a.target.checked),className:"mr-3 text-blue-600 focus:ring-blue-500"}),(0,c.jsx)("span",{className:"text-sm text-blue-800 dark:text-blue-200",children:e.label})]},e.key))})]})]})]}),d(r("evaluation","Evaluation"),o,"evaluation","fas fa-clipboard-check","green"),(0,c.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:[(0,c.jsxs)("h4",{className:"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2",children:[(0,c.jsx)("i",{className:"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mr-1"}),r("educationGuidelines","Education Guidelines"),":"]}),(0,c.jsxs)("ul",{className:"text-xs text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,c.jsxs)("li",{children:["\u2022 ",r("selectRelevantNeeds","Select all educational needs relevant to the patient and family")]}),(0,c.jsxs)("li",{children:["\u2022 ",r("chooseAppropriateMethod","Choose appropriate teaching methods based on learning preferences")]}),(0,c.jsxs)("li",{children:["\u2022 ",r("useMultipleTools","Use multiple teaching tools for better comprehension")]}),(0,c.jsxs)("li",{children:["\u2022 ",r("evaluateEffectiveness","Evaluate the effectiveness of education provided")]}),(0,c.jsxs)("li",{children:["\u2022 ",r("documentFollowUp","Document any follow-up education needs")]})]})]})]})]})},g=e=>{let{formData:a,handleInputChange:t,errors:r}=e;const{t:s}=(0,l.o)(),i=[{value:"School",label:s("school","School")},{value:"Work",label:s("work","Work")},{value:"NOT APPLICABLE",label:s("notApplicable","NOT APPLICABLE")}],n=[{value:"Yes",label:s("yes","Yes")},{value:"No",label:s("no","No")},{value:"NOT APPLICABLE",label:s("notApplicable","NOT APPLICABLE")}],o=[{value:"Yes",label:s("yes","Yes")},{value:"No",label:s("no","No")}],d=[{key:"caregiver",label:s("caregiver","Caregiver")},{key:"family",label:s("family","Family")},{key:"reAssessment",label:s("reAssessment","Re-Assessment")},{key:"dischargeAssessment",label:s("dischargeAssessment","Discharge Assessment")},{key:"others",label:s("others","Others")}],b=(e,a,s,i,l,n)=>(0,c.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,c.jsxs)("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-2",children:[l," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),n&&(0,c.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 mb-3",children:n}),(0,c.jsx)("div",{className:"flex flex-wrap gap-3",children:a.map(a=>(0,c.jsxs)("label",{className:"flex items-center px-3 py-2 border rounded-lg cursor-pointer transition-colors ".concat(s===a.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200":"border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500"),children:[(0,c.jsx)("input",{type:"radio",name:e,value:a.value,checked:s===a.value,onChange:a=>t(e,a.target.value),className:"sr-only"}),(0,c.jsx)("div",{className:"w-3 h-3 rounded-full border-2 mr-2 flex items-center justify-center ".concat(s===a.value?"border-blue-500 bg-blue-500":"border-gray-300 dark:border-gray-600"),children:s===a.value&&(0,c.jsx)("div",{className:"w-1.5 h-1.5 rounded-full bg-white"})}),(0,c.jsx)("span",{className:"text-sm font-medium",children:a.label})]},a.value))}),r[i]&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-2",children:r[i]})]});return(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-question-circle text-orange-600 dark:text-orange-400 mr-2"}),s("additionalQuestions","Additional Questions")]}),(0,c.jsxs)("div",{className:"space-y-4",children:[b("patientAttend",i,a.patientAttend,"patientAttend",s("patientAttend","Patient Attend"),s("patientAttendDescription","Does the patient currently attend school or work?")),b("problemsAttending",n,a.problemsAttending,"problemsAttending",s("problemsAttendingSchoolWork","Does the Patient Have Any Problems Attending School / Work?"),s("problemsAttendingDescription","Are there any barriers preventing regular attendance?")),b("literacySkills",o,a.literacySkills,"literacySkills",s("literacySkills","Literacy Skills"),s("literacySkillsDescription","Can the patient read and write at an appropriate level?"))]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-home text-green-600 dark:text-green-400 mr-2"}),s("postDischargeCare","Post-Discharge Care")]}),(0,c.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:[(0,c.jsx)("h3",{className:"text-md font-semibold text-green-900 dark:text-green-100 mb-4",children:s("whoWillProvideCareAfterDischarge","Who Will Provide Care After Discharge?")}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:d.map(e=>(0,c.jsxs)("label",{className:"flex items-center",children:[(0,c.jsx)("input",{type:"checkbox",checked:a.postDischargeCareProvider[e.key],onChange:a=>t("postDischargeCareProvider.".concat(e.key),a.target.checked),className:"mr-3 text-green-600 focus:ring-green-500"}),(0,c.jsx)("span",{className:"text-sm text-green-800 dark:text-green-200",children:e.label})]},e.key))}),a.postDischargeCareProvider.others&&(0,c.jsxs)("div",{className:"mt-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:[s("othersDescription","Others Description"),":"]}),(0,c.jsx)("textarea",{value:a.postDischargeCareProvider.othersDescription,onChange:e=>t("postDischargeCareProvider.othersDescription",e.target.value),rows:2,className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white",placeholder:s("specifyOtherCareProvider","Specify other care provider")})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,c.jsx)("i",{className:"fas fa-clipboard-list text-purple-600 dark:text-purple-400 mr-2"}),s("plansAndComments","Plans and Comments")]}),(0,c.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4",children:[(0,c.jsx)("textarea",{value:a.plansAndComments,onChange:e=>t("plansAndComments",e.target.value),rows:6,className:"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white",placeholder:s("plansCommentsPlaceholder","Document future education plans, follow-up requirements, additional comments, or special considerations for the patient and family education")}),(0,c.jsx)("p",{className:"text-xs text-purple-700 dark:text-purple-300 mt-2",children:s("plansCommentsHint","Include any specific education plans, follow-up schedules, or special considerations for ongoing patient and family education")})]})]}),(0,c.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-6",children:[(0,c.jsx)("i",{className:"fas fa-signature text-blue-600 dark:text-blue-400 mr-2"}),s("therapistSignature","Therapist Signature")]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:[s("therapistName","Therapist Name")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("input",{type:"text",value:a.therapistName,onChange:e=>t("therapistName",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ".concat(r.therapistName?"border-red-500":"border-blue-300"),placeholder:s("enterTherapistName","Enter therapist full name")}),r.therapistName&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.therapistName})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:s("signature","Signature")}),(0,c.jsx)("input",{type:"text",value:a.therapistSignature,onChange:e=>t("therapistSignature",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:s("digitalSignature","Digital signature")})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:[s("badgeNumber","Badge No.")," ",(0,c.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,c.jsx)("input",{type:"text",value:a.therapistBadgeNo,onChange:e=>t("therapistBadgeNo",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ".concat(r.therapistBadgeNo?"border-red-500":"border-blue-300"),placeholder:s("enterBadgeNumber","Enter badge number")}),r.therapistBadgeNo&&(0,c.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.therapistBadgeNo})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:s("date","Date")}),(0,c.jsx)("input",{type:"date",value:a.therapistDate,onChange:e=>t("therapistDate",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white"})]})]}),(0,c.jsxs)("div",{className:"mt-6",children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2",children:s("digitalSignature","Digital Signature")}),(0,c.jsxs)("div",{className:"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center",children:[(0,c.jsx)("i",{className:"fas fa-signature text-3xl text-blue-400 mb-2"}),(0,c.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:s("digitalSignaturePlaceholder","Digital signature will be captured here")}),(0,c.jsxs)("button",{type:"button",className:"mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-pen mr-2"}),s("addSignature","Add Signature")]})]})]})]})]})},p=e=>{let{patientId:a,onSave:t,onCancel:p}=e;const{t:h}=(0,l.o)(),{user:x}=(0,n.A)(),f=(0,i.Zp)(),{patientId:y,educationId:k}=(0,i.g)(),[v,N]=(0,s.useState)(!1),[j,w]=(0,s.useState)({}),[C,A]=(0,s.useState)(null),D=a||y,[P,S]=(0,s.useState)({documentNumber:"QP-",issueDate:(new Date).toISOString().split("T")[0],version:"01",reviewNumber:"01",assessmentType:"",patientLearningPreference:{video:!1,writtenMaterialsWithPicture:!1,demonstration:!1,other:!1,otherDescription:""},patientBarriers:{physical:!1,cognitive:!1,emotional:!1,culturalBelieve:!1,poorMotivation:!1,financialDifficulties:!1,readingAbility:!1,psychological:!1,language:!1,others:!1,othersDescription:"",noBarrier:!1,impairedHearing:!1,speechBarriers:!1,responsibilitiesAtHome:!1,religiousPractice:!1,impairedVision:!1,age:!1},caregiverLearningPreference:{video:!1,writtenMaterialsWithPicture:!1,demonstration:!1,other:!1,otherDescription:""},caregiverBarriers:{physical:!1,cognitive:!1,emotional:!1,culturalBelieve:!1,poorMotivation:!1,financialDifficulties:!1,readingAbility:!1,psychological:!1,language:!1,others:!1,othersDescription:"",noBarrier:!1,impairedHearing:!1,speechBarriers:!1,responsibilitiesAtHome:!1,religiousPractice:!1,impairedVision:!1,age:!1},patientSelfCareCapability:"",patientSelfCareCapabilityReason:"",patientSelfCareMotivation:"",patientSelfCareMotivationReason:"",comments:"",educationalNeeds:{diseaseProcess:!1,safeEffectiveUseMedication:!1,safeEffectiveUseMedicalEquipment:!1,painManagement:!1,rehabilitationTechnique:!1,communityResources:!1,dischargeInstruction:!1,patientsRightsResponsibilities:!1,whenHowObtainFurtherTreatment:!1,personalHygienePractitionerHandWash:!1,infectionControlRelatedIssues:!1,carePlanTreatment:!1,informedConsentProcedure:!1,homeInstruction:!1,others:!1,othersDescription:"",diagnosticTestProcedure:!1,socialServices:!1,healthMaintenance:!1,nutrition:!1,risk:!1,safety:!1},teachingMethod:{oneToOne:!1,groupTeaching:!1,lecture:!1},teachingTool:{writtenMaterials:!1,audio:!1,verbalInstructions:!1,writtenInstruction:!1,video:!1,demonstration:!1},evaluation:{notReceptiveToLearning:!1,unableToVerbalizeBasicConcepts:!1,verbalizeBasicConceptsWithAssistance:!1,verbalizeBasicConcept:!1,returnDemonstration:!1,appliesKnowledgeSkills:!1,notApplicable:!1,needFollowUp:!1},patientAttend:"",problemsAttending:"",literacySkills:"",postDischargeCareProvider:{caregiver:!1,family:!1,reAssessment:!1,dischargeAssessment:!1,others:!1,othersDescription:""},plansAndComments:"",therapistName:(null===x||void 0===x?void 0:x.name)||"",therapistSignature:"",therapistBadgeNo:(null===x||void 0===x?void 0:x.badgeNo)||"",therapistDate:(new Date).toISOString().split("T")[0]});(0,s.useEffect)(()=>{D&&(N(!0),setTimeout(()=>{A({id:D,name:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",nameEn:"Ahmed Mohammed Ali",mrNumber:"MR-2024-001",dateOfBirth:"2016-03-15",age:8,gender:"male",diagnosis:"Cerebral Palsy"}),N(!1)},500))},[D]);const B=(e,a)=>{const t=(0,r.A)({},P);if(e.includes(".")){const r=e.split(".");let s=t;for(let e=0;e<r.length-1;e++)s[r[e]]||(s[r[e]]={}),s=s[r[e]];s[r[r.length-1]]=a}else t[e]=a;S(t),j[e]&&w(a=>(0,r.A)((0,r.A)({},a),{},{[e]:null}))};return v&&!C?(0,c.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,c.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,c.jsx)("div",{className:"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900",children:(0,c.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return P.assessmentType||(e.assessmentType=h("assessmentTypeRequired","Assessment type is required")),P.patientSelfCareCapability||(e.patientSelfCareCapability=h("selfCareCapabilityRequired","Patient self-care capability is required")),P.patientSelfCareMotivation||(e.patientSelfCareMotivation=h("selfCareMotivationRequired","Patient self-care motivation is required")),P.patientAttend||(e.patientAttend=h("patientAttendRequired","Patient attend field is required")),P.problemsAttending||(e.problemsAttending=h("problemsAttendingRequired","Problems attending field is required")),P.literacySkills||(e.literacySkills=h("literacySkillsRequired","Literacy skills field is required")),P.therapistName.trim()||(e.therapistName=h("therapistNameRequired","Therapist name is required")),P.therapistBadgeNo.trim()||(e.therapistBadgeNo=h("therapistBadgeRequired","Therapist badge number is required")),"Not Applicable"!==P.patientSelfCareCapability||P.patientSelfCareCapabilityReason.trim()||(e.patientSelfCareCapabilityReason=h("reasonRequired",'Reason is required when selecting "Not Applicable"')),"Not Applicable"!==P.patientSelfCareMotivation||P.patientSelfCareMotivationReason.trim()||(e.patientSelfCareMotivationReason=h("reasonRequired",'Reason is required when selecting "Not Applicable"')),w(e),0===Object.keys(e).length})()){N(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),t)t(P);else{const e=JSON.parse(localStorage.getItem("educationFormData")||"[]"),a=(0,r.A)((0,r.A)({},P),{},{id:Date.now(),patientId:D,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()});e.push(a),localStorage.setItem("educationFormData",JSON.stringify(e)),o.Ay.success(h("educationFormSaved","Patient and Family Education Form saved successfully")),f(D?"/patients/".concat(D):"/patients")}}catch(a){console.error("Error saving education form:",a),o.Ay.error(h("errorSavingForm","Error saving education form"))}finally{N(!1)}}else o.Ay.error(h("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-8",children:[(0,c.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:(0,c.jsxs)("div",{className:"flex items-start justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[h("patientFamilyEducationForm","Patient and Family Education Form for Physical Therapist"),D&&C&&(0,c.jsxs)("span",{className:"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3",children:["- ",C.nameEn||C.name]})]}),(0,c.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:h("educationFormDescription","Comprehensive 1-page education assessment and planning form")}),(0,c.jsxs)("div",{className:"flex flex-wrap gap-2 mt-3",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:[(0,c.jsx)("i",{className:"fas fa-certificate text-blue-600 dark:text-blue-400"}),(0,c.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"CARF Compliant"})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full",children:[(0,c.jsx)("i",{className:"fas fa-shield-alt text-green-600 dark:text-green-400"}),(0,c.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"CBAHI Compliant"})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:[(0,c.jsx)("i",{className:"fas fa-lock text-purple-600 dark:text-purple-400"}),(0,c.jsx)("span",{className:"text-sm font-medium text-purple-800 dark:text-purple-200",children:"HIPAA Secure"})]})]})]}),(0,c.jsxs)("div",{className:"flex space-x-3",children:[D&&(0,c.jsxs)("button",{type:"button",onClick:()=>f("/patients/".concat(D)),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-user mr-2"}),h("viewPatient","View Patient")]}),(0,c.jsxs)("button",{type:"button",onClick:()=>{o.Ay.success(h("pdfExported","PDF exported successfully"))},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-file-pdf mr-2"}),h("exportPDF","Export PDF")]}),(0,c.jsx)("button",{type:"button",onClick:p||(()=>f(D?"/patients/".concat(D):"/patients")),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:h("cancel","Cancel")})]})]})}),(0,c.jsx)(d,{formData:P,handleInputChange:B,errors:j}),(0,c.jsx)(b,{formData:P,handleInputChange:B,handleBarrierChange:(e,a,t)=>{const s=(0,r.A)({},P);"noBarrier"===a&&t?(Object.keys(s[e]).forEach(a=>{"noBarrier"!==a&&"othersDescription"!==a&&(s[e][a]=!1)}),s[e].othersDescription=""):"noBarrier"!==a&&t&&(s[e].noBarrier=!1),s[e][a]=t,S(s)}}),(0,c.jsx)(m,{formData:P,handleInputChange:B,errors:j}),(0,c.jsx)(u,{formData:P,handleInputChange:B}),(0,c.jsx)(g,{formData:P,handleInputChange:B,errors:j}),(0,c.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,c.jsx)("button",{type:"button",onClick:p||(()=>f(D?"/patients/".concat(D):"/patients")),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:h("cancel","Cancel")}),(0,c.jsx)("button",{type:"submit",disabled:v,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:v?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),h("saving","Saving...")]}):(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("i",{className:"fas fa-save mr-2"}),h("saveEducationForm","Save Education Form")]})})]})]})})}}}]);
//# sourceMappingURL=3850.d788bca7.chunk.js.map