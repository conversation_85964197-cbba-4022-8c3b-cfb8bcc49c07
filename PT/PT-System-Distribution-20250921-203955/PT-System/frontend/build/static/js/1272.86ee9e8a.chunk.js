"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1272],{1272:(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(2555),n=s(5043),r=s(7921),i=s(579);const o=()=>{const{t:e,isRTL:t}=(0,r.o)(),[s,o]=(0,n.useState)("getting-started"),[l,d]=(0,n.useState)(""),[c,p]=(0,n.useState)({}),m={"getting-started":{title:e("gettingStarted","Getting Started"),icon:"fas fa-play-circle",items:[{id:"overview",title:e("systemOverview","System Overview"),content:e("systemOverviewContent","Learn about the PT System features and capabilities"),steps:[e("step1Overview","Navigate through the main dashboard"),e("step2Overview","Explore different modules and their functions"),e("step3Overview","Customize your workspace and preferences")]},{id:"first-login",title:e("firstLogin","First Login"),content:e("firstLoginContent","How to log in and set up your account"),steps:[e("step1Login","Enter your credentials on the login page"),e("step2Login","Complete your profile information"),e("step3Login","Set your language and theme preferences")]},{id:"navigation",title:e("navigationGuide","Navigation Guide"),content:e("navigationContent","Learn how to navigate through the system"),steps:[e("step1Nav","Use the sidebar menu to access different modules"),e("step2Nav","Use the search bar for quick access to patients and data"),e("step3Nav","Access settings and help from the header menu")]}]},patients:{title:e("patientManagement","Patient Management"),icon:"fas fa-users",items:[{id:"add-patient",title:e("addNewPatient","Adding New Patients"),content:e("addPatientContent","Step-by-step guide to add new patients"),steps:[e("step1AddPatient",'Click "Add New Patient" button'),e("step2AddPatient","Fill in patient information and medical history"),e("step3AddPatient","Set up initial treatment goals and preferences"),e("step4AddPatient","Save and schedule first appointment")]},{id:"patient-records",title:e("managingRecords","Managing Patient Records"),content:e("recordsContent","How to update and maintain patient records"),steps:[e("step1Records","Search for patient using the search bar"),e("step2Records","Click on patient name to view full profile"),e("step3Records","Update information using the edit button"),e("step4Records","Add notes and track progress over time")]},{id:"special-needs",title:e("specialNeedsPatients","Special Needs Patients"),content:e("specialNeedsContent","Working with special needs patients"),steps:[e("step1SpecialNeeds","Access the Special Needs module from sidebar"),e("step2SpecialNeeds","Complete comprehensive assessment forms"),e("step3SpecialNeeds","Set up sensory environment preferences"),e("step4SpecialNeeds","Use communication tools and visual schedules")]}]},appointments:{title:e("appointmentScheduling","Appointment Scheduling"),icon:"fas fa-calendar",items:[{id:"schedule-appointment",title:e("schedulingAppointments","Scheduling Appointments"),content:e("scheduleContent","How to schedule and manage appointments"),steps:[e("step1Schedule","Go to Appointments module"),e("step2Schedule",'Click "New Appointment" button'),e("step3Schedule","Select patient, date, time, and therapist"),e("step4Schedule","Add appointment notes and confirm")]},{id:"manage-calendar",title:e("calendarManagement","Calendar Management"),content:e("calendarContent","Managing your therapy calendar"),steps:[e("step1Calendar","Switch between different calendar views"),e("step2Calendar","Use filters to view specific therapists or types"),e("step3Calendar","Drag and drop to reschedule appointments"),e("step4Calendar","Set up recurring appointments for regular patients")]}]},treatments:{title:e("treatmentPlanning","Treatment Planning"),icon:"fas fa-heartbeat",items:[{id:"create-plan",title:e("creatingTreatmentPlans","Creating Treatment Plans"),content:e("planContent","How to create effective treatment plans"),steps:[e("step1Plan","Assess patient condition and needs"),e("step2Plan","Set SMART goals for treatment outcomes"),e("step3Plan","Select appropriate exercises and activities"),e("step4Plan","Schedule regular progress reviews")]},{id:"track-progress",title:e("trackingProgress","Tracking Progress"),content:e("progressContent","Monitoring patient progress effectively"),steps:[e("step1Progress","Use progress visualization tools"),e("step2Progress","Update goal completion percentages"),e("step3Progress","Document session notes and observations"),e("step4Progress","Generate progress reports for patients and families")]}]},reports:{title:e("reportsAnalytics","Reports & Analytics"),icon:"fas fa-chart-bar",items:[{id:"generate-reports",title:e("generatingReports","Generating Reports"),content:e("reportsContent","How to create and customize reports"),steps:[e("step1Reports","Navigate to Reports module"),e("step2Reports","Select report template or create custom report"),e("step3Reports","Set date ranges and filter criteria"),e("step4Reports","Generate and export report in desired format")]},{id:"analytics",title:e("understandingAnalytics","Understanding Analytics"),content:e("analyticsContent","Interpreting system analytics and metrics"),steps:[e("step1Analytics","Review key performance indicators on dashboard"),e("step2Analytics","Analyze patient outcome trends"),e("step3Analytics","Compare therapist performance metrics"),e("step4Analytics","Use insights to improve practice efficiency")]}]},troubleshooting:{title:e("troubleshooting","Troubleshooting"),icon:"fas fa-tools",items:[{id:"common-issues",title:e("commonIssues","Common Issues"),content:e("issuesContent","Solutions to frequently encountered problems"),steps:[e("step1Issues","Check internet connection and browser compatibility"),e("step2Issues","Clear browser cache and cookies"),e("step3Issues","Verify user permissions and access rights"),e("step4Issues","Contact system administrator if issues persist")]},{id:"performance",title:e("performanceOptimization","Performance Optimization"),content:e("performanceContent","Tips to improve system performance"),steps:[e("step1Performance","Use modern browsers (Chrome, Firefox, Safari)"),e("step2Performance","Close unnecessary browser tabs and applications"),e("step3Performance","Ensure stable internet connection"),e("step4Performance","Regularly update browser and clear cache")]}]}},u=[{id:"faq1",question:e("faq1Question","How do I reset my password?"),answer:e("faq1Answer","Go to Settings > Account > Change Password, or contact your system administrator.")},{id:"faq2",question:e("faq2Question","Can I access the system on mobile devices?"),answer:e("faq2Answer","Yes, the system is fully responsive and works on tablets and smartphones.")},{id:"faq3",question:e("faq3Question","How do I export patient data?"),answer:e("faq3Answer","Use the Reports module to generate and export patient data in various formats.")},{id:"faq4",question:e("faq4Question","Is the system available in Arabic?"),answer:e("faq4Answer","Yes, the system supports both Arabic and English with full RTL layout support.")},{id:"faq5",question:e("faq5Question","How do I set up special needs accommodations?"),answer:e("faq5Answer","Use the Special Needs module to configure sensory environments and communication tools.")}],g=e=>{p(t=>(0,a.A)((0,a.A)({},t),{},{[e]:!t[e]}))};Object.entries(m).filter(e=>{let[t,s]=e;if(!l)return!0;const a=l.toLowerCase();return s.title.toLowerCase().includes(a)||s.items.some(e=>e.title.toLowerCase().includes(a)||e.content.toLowerCase().includes(a))});return(0,i.jsxs)("div",{className:"p-6 ".concat(t?"font-arabic":"font-english"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("help","Help & Documentation")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("helpDesc","Find answers and learn how to use the PT System effectively")})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-video mr-2"}),e("videoTutorials","Video Tutorials")]}),(0,i.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-headset mr-2"}),e("contactSupport","Contact Support")]})]})]}),(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsxs)("div",{className:"relative max-w-md",children:[(0,i.jsx)("input",{type:"text",value:l,onChange:e=>d(e.target.value),placeholder:e("searchHelp","Search help articles..."),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,i.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-600",children:(0,i.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:e("helpTopics","Help Topics")})}),(0,i.jsxs)("nav",{className:"p-4 space-y-2",children:[Object.entries(m).map(e=>{let[t,a]=e;return(0,i.jsxs)("button",{onClick:()=>o(t),className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ".concat(s===t?"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:[(0,i.jsx)("i",{className:a.icon}),(0,i.jsx)("span",{children:a.title})]},t)}),(0,i.jsxs)("button",{onClick:()=>o("faq"),className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ".concat("faq"===s?"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:[(0,i.jsx)("i",{className:"fas fa-question-circle"}),(0,i.jsx)("span",{children:e("faq","FAQ")})]})]})]})}),(0,i.jsx)("div",{className:"lg:col-span-3",children:(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:"faq"===s?(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white mb-6",children:e("frequentlyAskedQuestions","Frequently Asked Questions")}),(0,i.jsx)("div",{className:"space-y-4",children:u.map(e=>(0,i.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,i.jsxs)("button",{onClick:()=>g(e.id),className:"w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,i.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:e.question}),(0,i.jsx)("i",{className:"fas fa-chevron-".concat(c[e.id]?"up":"down"," text-gray-400")})]}),c[e.id]&&(0,i.jsx)("div",{className:"px-4 pb-4 text-gray-600 dark:text-gray-400",children:e.answer})]},e.id))})]}):m[s]&&(0,i.jsxs)("div",{className:"p-6",children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[(0,i.jsx)("i",{className:"".concat(m[s].icon," text-2xl text-blue-600 dark:text-blue-400 mr-3")}),(0,i.jsx)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:m[s].title})]}),(0,i.jsx)("div",{className:"space-y-6",children:m[s].items.map(t=>(0,i.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,i.jsxs)("button",{onClick:()=>g(t.id),className:"w-full p-4 text-left flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:t.title}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:t.content})]}),(0,i.jsx)("i",{className:"fas fa-chevron-".concat(c[t.id]?"up":"down"," text-gray-400")})]}),c[t.id]&&(0,i.jsxs)("div",{className:"px-4 pb-4",children:[(0,i.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:[e("steps","Steps"),":"]}),(0,i.jsx)("ol",{className:"space-y-2",children:t.steps.map((e,t)=>(0,i.jsxs)("li",{className:"flex items-start",children:[(0,i.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5",children:t+1}),(0,i.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:e})]},t))})]})]},t.id))})]})})})]}),(0,i.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)("i",{className:"fas fa-graduation-cap text-2xl mr-3"}),(0,i.jsx)("h3",{className:"text-lg font-semibold",children:e("quickStart","Quick Start Guide")})]}),(0,i.jsx)("p",{className:"text-blue-100 mb-4",children:e("quickStartDesc","Get up and running in minutes")}),(0,i.jsx)("button",{className:"bg-white text-blue-600 px-4 py-2 rounded font-medium hover:bg-blue-50 transition-colors",children:e("startGuide","Start Guide")})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)("i",{className:"fas fa-book text-2xl mr-3"}),(0,i.jsx)("h3",{className:"text-lg font-semibold",children:e("userManual","User Manual")})]}),(0,i.jsx)("p",{className:"text-green-100 mb-4",children:e("userManualDesc","Complete documentation")}),(0,i.jsx)("button",{className:"bg-white text-green-600 px-4 py-2 rounded font-medium hover:bg-green-50 transition-colors",children:e("downloadPDF","Download PDF")})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-lg text-white",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)("i",{className:"fas fa-comments text-2xl mr-3"}),(0,i.jsx)("h3",{className:"text-lg font-semibold",children:e("community","Community Forum")})]}),(0,i.jsx)("p",{className:"text-purple-100 mb-4",children:e("communityDesc","Connect with other users")}),(0,i.jsx)("button",{className:"bg-white text-purple-600 px-4 py-2 rounded font-medium hover:bg-purple-50 transition-colors",children:e("joinForum","Join Forum")})]})]})]})}}}]);
//# sourceMappingURL=1272.86ee9e8a.chunk.js.map