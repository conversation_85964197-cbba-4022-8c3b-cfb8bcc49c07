"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8280],{8280:(e,r,s)=>{s.r(r),s.d(r,{default:()=>o});var a=s(2555),t=s(5043),l=s(7921),i=s(3216),c=s(6711),n=s(930),x=s(3768),d=s(579);const o=()=>{const{t:e,isRTL:r}=(0,l.o)(),{patientId:s}=(0,i.g)(),o=(0,i.Zp)(),[m,g]=(0,t.useState)(null),[p,h]=(0,t.useState)([]),[u,y]=(0,t.useState)(!0),[b,f]=(0,t.useState)("library"),[j,N]=(0,t.useState)({totalPrograms:0,totalExercises:0,completedSessions:0,avgDifficulty:0});(0,t.useEffect)(()=>{v(),w()},[s]);const v=async()=>{if(s)try{const e=await n.A.getPatient(s);g(e)}catch(r){console.error("Error loading patient:",r),x.Ay.error(e("errorLoadingPatient","Error loading patient data"))}y(!1)},w=async()=>{if(s)try{const e=await n.A.getExercisePrograms(s);h(e);const r=e.reduce((e,r)=>{var s;return e+((null===(s=r.exercises)||void 0===s?void 0:s.length)||0)},0),a=e.length>0?e.reduce((e,r)=>e+(r.difficulty||1),0)/e.length:0;N({totalPrograms:e.length,totalExercises:r,completedSessions:e.filter(e=>"completed"===e.status).length,avgDifficulty:a.toFixed(1)})}catch(e){console.error("Error loading exercise programs:",e)}},k=e=>{switch(e){case 1:return"bg-green-100 text-green-800";case 2:return"bg-yellow-100 text-yellow-800";case 3:return"bg-orange-100 text-orange-800";case 4:return"bg-red-100 text-red-800";case 5:return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}},E=r=>{switch(r){case 1:return e("beginner","Beginner");case 2:return e("easy","Easy");case 3:return e("intermediate","Intermediate");case 4:return e("advanced","Advanced");case 5:return e("expert","Expert");default:return e("unknown","Unknown")}};return u?(0,d.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"exercise-library-page min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsx)("div",{className:"py-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("button",{onClick:()=>o(-1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,d.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,d.jsx)("i",{className:"fas fa-dumbbell mr-3 text-purple-600"}),e("exerciseLibrary","Exercise Library & Programs")]}),m&&(0,d.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mt-1",children:[e("patient","Patient"),": ",r?m.nameAr:m.name,m.condition&&(0,d.jsx)("span",{className:"ml-2 text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded",children:r?m.conditionAr:m.condition})]})]})]}),(0,d.jsx)("div",{className:"flex items-center space-x-3",children:(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,d.jsx)("i",{className:"fas fa-video mr-1"}),"500+ ",e("exercises","Exercises")]}),(0,d.jsxs)("span",{className:"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,d.jsx)("i",{className:"fas fa-magic mr-1"}),e("adaptive","Adaptive")]})]})})]})})})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("nav",{className:"flex space-x-8",children:[(0,d.jsxs)("button",{onClick:()=>f("library"),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat("library"===b?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,d.jsx)("i",{className:"fas fa-book mr-2"}),e("exerciseLibrary","Exercise Library")]}),(0,d.jsxs)("button",{onClick:()=>f("builder"),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat("builder"===b?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("createProgram","Create Program")]}),(0,d.jsxs)("button",{onClick:()=>f("programs"),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat("programs"===b?"border-purple-500 text-purple-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,d.jsx)("i",{className:"fas fa-list mr-2"}),e("myPrograms","My Programs")," (",p.length,")"]})]})})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,d.jsx)("div",{className:"xl:col-span-3",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:["library"===b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:e("browseExercises","Browse Exercise Library")}),(0,d.jsx)(c.h,{patientId:s,showProgramBuilder:!1})]}),"builder"===b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:e("createExerciseProgram","Create Exercise Program")}),(0,d.jsx)(c.A,{patientId:s,onSave:async r=>{try{await n.A.saveExerciseProgram((0,a.A)((0,a.A)({},r),{},{patientId:s})),await w(),x.Ay.success(e("programSaved","Exercise program saved successfully")),f("programs")}catch(t){console.error("Error saving program:",t),x.Ay.error(e("errorSaving","Error saving program"))}}})]}),"programs"===b&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:e("exercisePrograms","Exercise Programs")}),p.length>0?(0,d.jsx)("div",{className:"space-y-4",children:p.map((r,s)=>{var a;return(0,d.jsx)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:r.name}),(0,d.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(k(r.difficulty)),children:E(r.difficulty)}),(0,d.jsxs)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:[(null===(a=r.exercises)||void 0===a?void 0:a.length)||0," ",e("exercises","exercises")]})]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-3",children:r.description}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,d.jsxs)("span",{children:[(0,d.jsx)("i",{className:"fas fa-calendar mr-1"}),e("duration","Duration"),": ",r.duration," ",e("weeks","weeks")]}),(0,d.jsxs)("span",{children:[(0,d.jsx)("i",{className:"fas fa-repeat mr-1"}),e("frequency","Frequency"),": ",r.frequency]}),(0,d.jsxs)("span",{children:[(0,d.jsx)("i",{className:"fas fa-clock mr-1"}),e("created","Created"),": ",new Date(r.createdAt).toLocaleDateString()]})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsxs)("button",{className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-play mr-1"}),e("start","Start")]}),(0,d.jsxs)("button",{className:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-edit mr-1"}),e("edit","Edit")]})]})]})},s)})}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("i",{className:"fas fa-dumbbell text-6xl text-gray-300 mb-4"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noPrograms","No exercise programs yet")}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:e("createFirstProgram","Create your first exercise program to get started")}),(0,d.jsxs)("button",{onClick:()=>f("builder"),className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("createProgram","Create Program")]})]})]})]})}),(0,d.jsx)("div",{className:"xl:col-span-1",children:(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,d.jsx)("i",{className:"fas fa-chart-bar mr-2 text-gray-600"}),e("exerciseStats","Exercise Stats")]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("totalPrograms","Total Programs")}),(0,d.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:j.totalPrograms})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("totalExercises","Total Exercises")}),(0,d.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:j.totalExercises})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("avgDifficulty","Avg Difficulty")}),(0,d.jsxs)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[j.avgDifficulty,"/5"]})]})]})]})})]})})]})}}}]);
//# sourceMappingURL=8280.6c302a9c.chunk.js.map