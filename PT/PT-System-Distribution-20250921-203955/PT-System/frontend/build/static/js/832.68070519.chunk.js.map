{"version": 3, "file": "static/js/832.68070519.chunk.js", "mappings": "wOACA,SAASA,EAAmBC,EAIzBC,GAAQ,IAJkB,MAC3BC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,6GAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBR,G,sBCvBlD,SAASqB,EAAqBpB,EAI3BC,GAAQ,IAJoB,MAC7BC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,kHAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBa,G,0ECsRlD,MACA,EAD2B,IAvS3B,MAKE,qBAAMC,CAAgBC,GAAkD,IAAvCC,EAAYC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sBAC9C,IAIE,aAHuBG,EAAAA,GAAIC,KAAK,yBAADC,OAA0BP,GAAa,CACpEQ,cAAeP,KAEDQ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,sBAAuBA,GAC/BA,CACR,CACF,CAKA,yBAAME,CAAoBZ,GAA0B,IAAfa,EAAOX,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC9C,IACE,MAAM,MAAEY,EAAQ,GAAE,KAAEC,EAAO,GAAMF,EAIjC,aAHuBR,EAAAA,GAAIW,IAAI,yBAADT,OAA0BP,GAAa,CACnEiB,OAAQ,CAAEH,QAAOC,WAEHN,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,mCAAoCA,GAC5CA,CACR,CACF,CAKA,uBAAMQ,CAAkBlB,GACtB,IAEE,aADuBK,EAAAA,GAAIW,IAAI,yBAADT,OAA0BP,EAAS,aACjDS,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,iCAAkCA,GAC1CA,CACR,CACF,CAKA,iCAAMS,CAA4BnB,GAChC,IAEE,aADuBK,EAAAA,GAAIW,IAAI,yBAADT,OAA0BP,EAAS,sBACjDS,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2CAA4CA,GACpDA,CACR,CACF,CAKA,2BAAMU,CAAsBpB,GAC1B,IAEE,aADuBK,EAAAA,GAAIW,IAAI,yBAADT,OAA0BP,EAAS,kBACjDS,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,qCAAsCA,GAC9CA,CACR,CACF,CAKA,yBAAMW,GACJ,IAEE,aADuBhB,EAAAA,GAAIW,IAAI,4BACfP,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,oCAAqCA,GAC7CA,CACR,CACF,CAKA,sBAAMY,GACJ,IAEE,aADuBjB,EAAAA,GAAIW,IAAI,yBACfP,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,gCAAiCA,GACzCA,CACR,CACF,CAKA,kBAAMa,CAAaC,EAAYC,EAAYC,GACzC,IAIE,aAHuBrB,EAAAA,GAAIsB,IAAI,iBAADpB,OAAkBiB,EAAU,YAAAjB,OAAWkB,EAAU,YAAY,CACzFC,YAEcjB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,2BAA4BA,GACpCA,CACR,CACF,CAKA,iBAAMkB,CAAYJ,EAAYK,GAC5B,IAEE,aADuBxB,EAAAA,GAAIC,KAAK,iBAADC,OAAkBiB,EAAU,aAAaK,IACxDpB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,0BAA2BA,GACnCA,CACR,CACF,CAKA,sBAAMoB,GAAoC,IAAnBC,EAAS7B,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KACjC,IAIE,aAHuBG,EAAAA,GAAIW,IAAI,0BAA2B,CACxDC,OAAQ,CAAEc,gBAEItB,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,gCAAiCA,GACzCA,CACR,CACF,CAKA,kBAAMsB,CAAaC,GAAmD,IAAvChC,EAAYC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,sBAC5C,IAKE,aAJuBG,EAAAA,GAAIC,KAAK,8BAA+B,CAC7D4B,YAAaD,EACbzB,cAAeP,KAEDQ,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,yBAA0BA,GAClCA,CACR,CACF,CAKA,mBAAMyB,GACJ,IAEE,aADuB9B,EAAAA,GAAIW,IAAI,6BACfP,IAClB,CAAE,MAAOC,GAEP,MADAC,QAAQD,MAAM,4BAA6BA,GACrCA,CACR,CACF,CAKA0B,eAAAA,CAAgBC,GACd,MAAMC,EAAa,CACjB,IAAO,CAAEC,MAAO,WAAYC,MAAO,QAASC,KAAM,UAClD,OAAU,CAAEF,MAAO,cAAeC,MAAO,SAAUC,KAAM,UACzD,KAAQ,CAAEF,MAAO,YAAaC,MAAO,SAAUC,KAAM,UACrD,SAAY,CAAEF,MAAO,gBAAiBC,MAAO,MAAOC,KAAM,iBAG5D,OAAOH,EAAWD,IAAiBC,EAAgB,GACrD,CAKAI,gBAAAA,CAAiBC,GACf,MAAMC,EAAaC,KAAKC,MAAmB,IAAbH,GAC9B,IAAII,EAAQ,MACRP,EAAQ,MAUZ,OARII,GAAc,IAChBG,EAAQ,OACRP,EAAQ,SACCI,GAAc,KACvBG,EAAQ,SACRP,EAAQ,UAGH,CACLI,aACAG,QACAP,QAEJ,CAKAQ,cAAAA,CAAeC,GACb,MAAMC,EAAa,CACjB,EAAG,CAAEX,MAAO,SAAUC,MAAO,MAAOC,KAAM,gBAC1C,EAAG,CAAEF,MAAO,OAAQC,MAAO,SAAUC,KAAM,gBAC3C,EAAG,CAAEF,MAAO,SAAUC,MAAO,SAAUC,KAAM,gBAC7C,EAAG,CAAEF,MAAO,MAAOC,MAAO,OAAQC,KAAM,gBACxC,EAAG,CAAEF,MAAO,WAAYC,MAAO,OAAQC,KAAM,WAG/C,OAAOS,EAAWD,IAAaC,EAAW,EAC5C,CAKAC,cAAAA,CAAeC,EAAcC,GAC3B,MAAMC,EAAcD,EAAiBD,EAC/BR,EAAaC,KAAKC,MAAOQ,EAAcF,EAAgB,KAE7D,IAAIG,EAAQ,SACRd,EAAO,eACPD,EAAQ,OAYZ,OAVII,EAAa,IACfW,EAAQ,YACRd,EAAO,eACPD,EAAQ,SACCI,GAAc,KACvBW,EAAQ,YACRd,EAAO,eACPD,EAAQ,OAGH,CACLe,QACAX,aACAH,OACAD,QAEJ,CAKAgB,cAAAA,CAAeC,GACb,GAAIA,EAAQ,EACV,MAAM,GAANlD,OAAUkD,EAAK,SAAAlD,OAAkB,IAAVkD,EAAc,IAAM,IACtC,GAAIA,EAAQ,GAAI,CACrB,MAAMC,EAASb,KAAKC,MAAMW,EAAQ,GAClC,MAAM,GAANlD,OAAUmD,EAAM,UAAAnD,OAAoB,IAAXmD,EAAe,IAAM,GAChD,CAAO,CACL,MAAMC,EAAQd,KAAKC,MAAMW,EAAQ,IACjC,MAAM,GAANlD,OAAUoD,EAAK,SAAApD,OAAkB,IAAVoD,EAAc,IAAM,GAC7C,CACF,CAKAC,aAAAA,CAAcC,GACZ,MAAMC,EAAS,CACb,SAAY,CAAErB,KAAM,eAAMD,MAAO,MAAOuB,QAAS,aACjD,QAAW,CAAEtB,KAAM,eAAMD,MAAO,SAAUuB,QAAS,gBACnD,QAAW,CAAEtB,KAAM,eAAMD,MAAO,SAAUuB,QAAS,gBACnD,KAAQ,CAAEtB,KAAM,eAAMD,MAAO,OAAQuB,QAAS,eAGhD,OAAOD,EAAOD,IAAcC,EAAa,IAC3C,CAKAE,oBAAAA,CAAqBvD,GACnB,MACMwD,EADW,CAAC,UAAW,gBAAiB,UACrBC,OAAOC,IAAU1D,EAAK0D,IAE/C,GAAIF,EAAQ9D,OAAS,EACnB,MAAM,IAAIiE,MAAM,4BAAD7D,OAA6B0D,EAAQI,KAAK,QAG3D,OAAO,CACT,G,2CCnRF,MA2UA,EA3U6BC,KAC3B,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCC,EAAeC,IAAoBF,EAAAA,EAAAA,UAAS,OAC5CG,EAAkBC,IAAuBJ,EAAAA,EAAAA,UAAS,KAClDK,EAAeC,IAAoBN,EAAAA,EAAAA,UAAS,KAC5C5C,EAAWmD,IAAgBP,EAAAA,EAAAA,UAAS,OAE3CQ,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACrD,IAEJ,MAAMqD,EAAoBC,UACxB,IACEX,GAAW,GAEX,MAAOY,EAAmBC,EAAkBC,SAAwBC,QAAQC,IAAI,CAC9EC,EAAmB7D,iBAAiBC,GACpC4D,EAAmBtE,sBACnBsE,EAAmBrE,qBAGrBuD,EAAiBS,EAAkB7E,MACnCsE,EAAoBQ,EAAiB9E,MACrCwE,EAAiBO,EAAe/E,KAClC,CAAE,MAAOC,GACPC,QAAQD,MAAM,iCAAkCA,GAChDkF,EAAAA,GAAMlF,MAAM6D,EAAE,wBAAyB,iCACzC,CAAC,QACCG,GAAW,EACb,GAGImB,EAAqBR,MAAO7D,EAAYC,EAAYC,KACxD,UACQiE,EAAmBpE,aAAaC,EAAYC,EAAYC,GAC9DkE,EAAAA,GAAME,QAAQvB,EAAE,iBAAkB,iCAClCa,GACF,CAAE,MAAO1E,GACPC,QAAQD,MAAM,2BAA4BA,GAC1CkF,EAAAA,GAAMlF,MAAM6D,EAAE,uBAAwB,2BACxC,GAGIwB,EAAWrH,IAAA,IAAC,MAAEE,EAAK,MAAEoH,EAAOvD,KAAMwD,EAAI,MAAEzD,EAAK,MAAEe,EAAK,SAAE2C,GAAUxH,EAAA,OACpEyH,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACC,UAAU,MAAKC,SAAA,EACnBH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oCAAmCC,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCAAmCC,SAAE1H,KAClD2H,EAAAA,EAAAA,KAAA,KAAGF,UAAS,2BAAA9F,OAA6BiC,EAAK,QAAO8D,SAAEN,IACtDE,IAAYK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAA4BC,SAAEJ,QAE1DK,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uBAAA9F,OAAyBiC,EAAK,QAAO8D,UACjDC,EAAAA,EAAAA,KAACN,EAAI,CAACI,UAAS,gBAAA9F,OAAkBiC,EAAK,eAGzCe,IACC4C,EAAAA,EAAAA,MAAA,OAAKE,UAAU,yBAAwBC,SAAA,CAChB,OAApB/C,EAAMiD,WACLD,EAAAA,EAAAA,KAAC9H,EAAmB,CAAC4H,UAAU,iCAE/BE,EAAAA,EAAAA,KAACzG,EAAqB,CAACuG,UAAU,+BAEnCF,EAAAA,EAAAA,MAAA,QAAME,UAAS,WAAA9F,OAAiC,OAApBgD,EAAMiD,UAAqB,iBAAmB,gBAAiBF,SAAA,CACxF/C,EAAMX,WAAW,KAAGW,EAAMkD,iBAO/BC,EAAkBC,IAA4B,IAADC,EAAAC,EAAA,IAA1B,QAAEC,EAAO,SAAEC,GAAUJ,EAC5C,MAAMK,EAAWrB,EAAmBvD,gBAAwC,QAAzBwE,EAACG,EAASE,uBAAe,IAAAL,OAAA,EAAxBA,EAA0BM,eAE9E,OACEf,EAAAA,EAAAA,MAAA,OAAKE,UAAU,0DAAyDC,SAAA,EACtEH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,yCAAwCC,SAAA,EACrDH,EAAAA,EAAAA,MAAA,MAAIE,UAAU,4BAA2BC,SAAA,CACtCQ,EAAQK,UAAU,IAAEL,EAAQM,aAE/BjB,EAAAA,EAAAA,MAAA,QAAME,UAAS,iDAAA9F,OAAmDyG,EAASxE,MAAK,cAAAjC,OAAayG,EAASxE,MAAK,QAAO8D,SAAA,CAC/GU,EAASvE,KAAK,IAAEuE,EAASzE,aAG9B4D,EAAAA,EAAAA,MAAA,KAAGE,UAAU,6BAA4BC,SAAA,CAAC,eACH,QAAzBO,EAACE,EAASE,uBAAe,IAAAJ,OAAA,EAAxBA,EAA0BQ,mBAAmB,WAE5DlB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,wBAAuBC,SAAA,CAAC,kBACrB,IAAIgB,KAAKP,EAASQ,eAAeC,4BAMnDC,EAAYC,IAAqC,IAADC,EAAAC,EAAA,IAAnC,MAAEC,EAAK,SAAEd,EAAQ,UAAEe,GAAWJ,EAC/C,MAAMK,EAAapC,EAAmB/B,cAAciE,EAAMG,MAE1D,OACEzB,EAAAA,EAAAA,KAAA,OAAKF,UAAS,qBAAA9F,OAAuBwH,EAAWvF,MAAK,SAAAjC,OAAQwH,EAAWhE,QAAO,qBAAoBuC,UACjGH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,mCAAkCC,SAAA,EAC/CH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAEyB,EAAWtF,QAC3C0D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,MAAIE,UAAS,oBAAA9F,OAAsBwH,EAAWvF,MAAK,QAAO8D,SAAA,CACvDuB,EAAMG,KAAKC,OAAO,GAAGC,cAAgBL,EAAMG,KAAKG,MAAM,GAAG,aAE5D5B,EAAAA,EAAAA,KAAA,KAAGF,UAAS,gBAAA9F,OAAkBwH,EAAWvF,MAAK,aAAY8D,SACvDuB,EAAMO,WAETjC,EAAAA,EAAAA,MAAA,KAAGE,UAAU,6BAA4BC,SAAA,CAAC,YACd,QAAjBqB,EAACZ,EAASD,eAAO,IAAAa,OAAA,EAAhBA,EAAkBR,UAAU,IAAkB,QAAjBS,EAACb,EAASD,eAAO,IAAAc,OAAA,EAAhBA,EAAkBR,mBAIhEb,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CACLC,QAAQ,UACRC,KAAK,KACLC,QAASA,IAAMV,EAAUf,EAAS0B,IAAKZ,EAAMa,MAAO,yBACpDrC,UAAU,OAAMC,SACjB,kBAQT,OAAI7B,GAEA8B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAACoC,EAAAA,GAAc,CAACJ,KAAK,UAMzBpC,EAAAA,EAAAA,MAAA,OAAKE,UAAU,YAAWC,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oCAAmCC,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEH,EAAAA,EAAAA,MAAA,MAAIE,UAAU,qDAAoDC,SAAA,EAChEC,EAAAA,EAAAA,KAACqC,EAAAA,EAAW,CAACvC,UAAU,+BACtB9B,EAAE,uBAAwB,8BAE7BgC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAC9B/B,EAAE,yBAA0B,8DAIjC4B,EAAAA,EAAAA,MAAA,OAAKE,UAAU,8BAA6BC,SAAA,EAC1CH,EAAAA,EAAAA,MAAA,UACEH,MAAOjE,EACP8G,SAAWC,GAAM5D,EAAa4D,EAAEC,OAAO/C,OACvCK,UAAU,sDAAqDC,SAAA,EAE/DC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,IAAGM,SAAC,iBAClBC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,KAAIM,SAAC,kBACnBC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,KAAIM,SAAC,qBAGrBC,EAAAA,EAAAA,KAAC8B,EAAAA,EAAM,CAACG,QAASpD,EAAmBkD,QAAQ,UAAShC,SAClD/B,EAAE,UAAW,mBAMnBK,IACCuB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACR,EAAQ,CACPnH,MAAO2F,EAAE,gBAAiB,kBAC1ByB,MAAOpB,EAAcoE,QAAQC,eAC7BxG,KAAMyG,EAAAA,EACN1G,MAAM,OACN0D,SAAQ,QAAA3F,OAAUwB,EAAS,YAG7BwE,EAAAA,EAAAA,KAACR,EAAQ,CACPnH,MAAO2F,EAAE,mBAAoB,sBAC7ByB,MAAOpB,EAAcoE,QAAQG,mBAC7B1G,KAAM2G,EAAAA,EACN5G,MAAM,MACN0D,SAAS,uBAGXK,EAAAA,EAAAA,KAACR,EAAQ,CACPnH,MAAO2F,EAAE,gBAAiB,kBAC1ByB,MAAOpB,EAAcoE,QAAQK,eAC7B5G,KAAM6G,EAAAA,EACN9G,MAAM,SACN0D,SAAS,iBAGXK,EAAAA,EAAAA,KAACR,EAAQ,CACPnH,MAAO2F,EAAE,oBAAqB,uBAC9ByB,MAAK,GAAAzF,OAAKsC,KAAKC,MAAM8B,EAAc2E,gBAAgBC,OAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,kBAAmB,GAAK/E,EAAc2E,gBAAgBpJ,OAAS,KAAK,KACxJsC,KAAMmH,EAAAA,EACNpH,MAAM,QACN0D,SAAS,oBAMdtB,IACCuB,EAAAA,EAAAA,MAAA,OAAKE,UAAU,wCAAuCC,SAAA,EAEpDH,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACC,UAAU,MAAKC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SACrD/B,EAAE,mBAAoB,wBAEzBgC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB1B,EAAciF,kBAAkBC,IAAKC,IACpC5D,EAAAA,EAAAA,MAAA,OAAoBE,UAAU,oCAAmCC,SAAA,EAC/DH,EAAAA,EAAAA,MAAA,QAAME,UAAU,+CAA8CC,SAAA,CAC3DyD,EAAKtB,KAAO,UAAU,YAEzBtC,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,OACEF,UAAS,oBAAA9F,OACM,aAAbwJ,EAAKtB,IAAqB,aACb,SAAbsB,EAAKtB,IAAiB,gBACT,WAAbsB,EAAKtB,IAAmB,gBAAkB,gBAE5CuB,MAAO,CACLC,MAAM,GAAD1J,OAAMwJ,EAAKG,MAAQtF,EAAcoE,QAAQC,eAAkB,IAAG,WAIzE1C,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sCAAqCC,SAClDyD,EAAKG,aAlBFH,EAAKtB,YA2BrBtC,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACC,UAAU,MAAKC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2CAA0CC,SACrD/B,EAAE,mBAAoB,wBAEzBgC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB1B,EAAcuF,kBAAkBL,IAAKM,IACpCjE,EAAAA,EAAAA,MAAA,OAAqBE,UAAU,oCAAmCC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+CAA8CC,SAC3D8D,EAAM3B,IAAI4B,QAAQ,IAAK,QAE1BlE,EAAAA,EAAAA,MAAA,OAAKE,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,OACEF,UAAU,+BACV2D,MAAO,CAAEC,MAAM,GAAD1J,OAA2B,IAAtB6J,EAAME,cAAmB,WAGhDnE,EAAAA,EAAAA,MAAA,QAAME,UAAU,sCAAqCC,SAAA,CAClDzD,KAAKC,MAA4B,IAAtBsH,EAAME,eAAqB,YAZnCF,EAAM3B,eAuB1BtC,EAAAA,EAAAA,MAAA,OAAKE,UAAU,wCAAuCC,SAAA,EAEpDH,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACC,UAAU,MAAKC,SAAA,EACnBH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAAqCC,SAChD/B,EAAE,mBAAoB,yBAEzBgC,EAAAA,EAAAA,KAACgE,EAAAA,EAAa,CAAClE,UAAU,8BAG3BE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,SAChDxB,EAAiB3E,OAAS,EACzB2E,EAAiBgF,IAAKU,IACpBjE,EAAAA,EAAAA,KAACG,EAAe,CAEdI,QAAS0D,EAAK1D,QACdC,SAAUyD,GAFLA,EAAK/B,OAMdlC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iCAAgCC,SAC1C/B,EAAE,qBAAsB,uCAOjC4B,EAAAA,EAAAA,MAACC,EAAAA,EAAI,CAACC,UAAU,MAAKC,SAAA,EACnBH,EAAAA,EAAAA,MAAA,OAAKE,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sCAAqCC,SAChD/B,EAAE,gBAAiB,qBAEtBgC,EAAAA,EAAAA,KAAC+C,EAAAA,EAAQ,CAACjD,UAAU,8BAGtBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,SAChDtB,EAAc7E,OAAS,EACtB6E,EAAc8E,IAAKU,GACjBA,EAAKC,gBACFvG,OAAO2D,IAAUA,EAAM6C,WACvBZ,IAAI,CAACjC,EAAOa,KACXnC,EAAAA,EAAAA,KAACkB,EAAS,CAERI,OAAK8C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAO9C,GAAK,IAAEa,UACnB3B,SAAUyD,EACV1C,UAAWjC,GAAmB,GAAAtF,OAHtBiK,EAAK/B,IAAG,KAAAlI,OAAImI,OAQ5BnC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iCAAgCC,SAC1C/B,EAAE,kBAAmB,kC,qKC5PtC,EAtFe7F,IAUR,IAVS,SACd4H,EAAQ,QACRgC,EAAU,UAAS,KACnBC,EAAO,KAAI,SACXqC,GAAW,EAAK,QAChBnG,GAAU,EAAK,UACf4B,EAAY,GAAE,KACd2B,EAAO,SAAQ,QACfQ,GAED9J,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,MAEM6L,EAAW,CACfC,QAAS,+DACTC,UAAW,+DACXC,QAAS,qFACTC,OAAQ,4DACRnF,QAAS,kEACToF,QAAS,qEACTC,MAAO,uDAGHC,EAAQ,CACZC,GAAI,wBACJC,GAAI,oBACJC,GAAI,oBACJC,GAAI,sBACJC,GAAI,uBAKAC,EAAgB,CAtBF,oJAwBlBb,EAASvC,IAAYuC,EAASC,QAC9BM,EAAM7C,IAAS6C,EAAMG,GALCX,GAAYnG,EAAU,gCAAkC,GAO9E4B,GACAnC,OAAOyH,SAAStH,KAAK,KAYvB,OACE8B,EAAAA,EAAAA,MAAA,UAAAwE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACE3C,KAAMA,EACN3B,UAAWqF,EACXlD,QAdiBM,IACf8B,GAAYnG,EACdqE,EAAE8C,iBAGApD,GACFA,EAAQM,IASR8B,SAAUA,GAAYnG,GAClB3F,GAAK,IAAAwH,SAAA,CAER7B,IACC0B,EAAAA,EAAAA,MAAA,OACEE,UAAU,kCACVjH,MAAM,6BACNC,KAAK,OACLC,QAAQ,YAAWgH,SAAA,EAEnBC,EAAAA,EAAAA,KAAA,UACEF,UAAU,aACVwF,GAAG,KACHC,GAAG,KACHC,EAAE,KACFvM,OAAO,eACPD,YAAY,OAEdgH,EAAAA,EAAAA,KAAA,QACEF,UAAU,aACVhH,KAAK,eACLQ,EAAE,uHAIPyG,M,sFCvFP,SAASgD,EAAQ5K,EAIdC,GAAQ,IAJO,MAChBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,2NAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBqK,E,yKCWlD,EA5Ba5K,IAUN,IAVO,SACZ4H,EAAQ,UACRD,EAAY,GAAE,QACd2F,EAAU,MAAK,OACfC,EAAS,YAAW,OACpBC,EAAS,yBAAwB,QACjCC,EAAU,aAAY,WACtBC,EAAa,WAAU,MACvBC,EAAQ,IAET3N,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,MAAMsN,EAAc,CAClBF,EACAF,EACAC,EACAF,EACAD,EACAK,EACAhG,GACAnC,OAAOyH,SAAStH,KAAK,KAEvB,OACEkC,EAAAA,EAAAA,KAAA,OAAAoE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CAAKtE,UAAWiG,GAAiBxN,GAAK,IAAAwH,SACnCA,K,sFC7BP,SAAS4C,EAAYxK,EAIlBC,GAAQ,IAJW,MACpBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qcAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBiK,E,iBCvBlD,SAASnK,EAAyB+J,EAAGvE,GACnC,GAAI,MAAQuE,EAAG,MAAO,CAAC,EACvB,IAAIyD,EACFR,EACAS,ECLJ,SAAuCT,EAAGjD,GACxC,GAAI,MAAQiD,EAAG,MAAO,CAAC,EACvB,IAAIxH,EAAI,CAAC,EACT,IAAK,IAAIkI,KAAKV,EAAG,GAAI,CAAC,EAAEW,eAAeC,KAAKZ,EAAGU,GAAI,CACjD,IAAK,IAAM3D,EAAE8D,QAAQH,GAAI,SACzBlI,EAAEkI,GAAKV,EAAEU,EACX,CACA,OAAOlI,CACT,CDHQ,CAA6BuE,EAAGvE,GACtC,GAAIrF,OAAO2N,sBAAuB,CAChC,IAAIJ,EAAIvN,OAAO2N,sBAAsB/D,GACrC,IAAKiD,EAAI,EAAGA,EAAIU,EAAEtM,OAAQ4L,IAAKQ,EAAIE,EAAEV,IAAK,IAAMxH,EAAEqI,QAAQL,IAAM,CAAC,EAAEO,qBAAqBH,KAAK7D,EAAGyD,KAAOC,EAAED,GAAKzD,EAAEyD,GAClH,CACA,OAAOC,CACT,C,sGEVA,SAAS5C,EAASlL,EAIfC,GAAQ,IAJQ,MACjBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qDAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB2K,E,sFCvBlD,SAASW,EAAa7L,EAInBC,GAAQ,IAJY,MACrBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,mgBAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBsL,E,sFCvBlD,SAASnB,EAAuB1K,EAI7BC,GAAQ,IAJsB,MAC/BC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,qLAEP,CACA,MACA,EADiCZ,EAAAA,WAAiBmK,E,sFCvBlD,SAASR,EAAWlK,EAIjBC,GAAQ,IAJU,MACnBC,EAAK,QACLC,GAEDH,EADII,GAAKC,EAAAA,EAAAA,GAAAL,EAAAM,GAER,OAAoBC,EAAAA,cAAoB,MAAOC,OAAOC,OAAO,CAC3DC,MAAO,6BACPC,KAAM,OACNC,QAAS,YACTC,YAAa,IACbC,OAAQ,eACR,cAAe,OACf,YAAa,OACbC,IAAKd,EACL,kBAAmBE,GAClBC,GAAQF,EAAqBK,EAAAA,cAAoB,QAAS,CAC3DS,GAAIb,GACHD,GAAS,KAAmBK,EAAAA,cAAoB,OAAQ,CACzDU,cAAe,QACfC,eAAgB,QAChBC,EAAG,oRAEP,CACA,MACA,EADiCZ,EAAAA,WAAiB2J,E", "sources": ["../node_modules/@heroicons/react/24/outline/esm/ArrowTrendingUpIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ArrowTrendingDownIcon.js", "services/aiAnalyticsService.js", "components/AIAnalytics/AIAnalyticsDashboard.jsx", "components/Common/Button.jsx", "../node_modules/@heroicons/react/24/outline/esm/BellIcon.js", "components/Common/Card.jsx", "../node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../node_modules/@heroicons/react/24/outline/esm/ClockIcon.js", "../node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js", "../node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js", "../node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ArrowTrendingUpIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowTrendingUpIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ArrowTrendingDownIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M2.25 6 9 12.75l4.286-4.286a11.948 11.948 0 0 1 4.306 6.43l.776 2.898m0 0 3.182-5.511m-3.182 5.51-5.511-3.181\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ArrowTrendingDownIcon);\nexport default ForwardRef;", "import api from './api';\n\n/**\n * AI Analytics Service\n * Frontend service for AI-powered analytics, predictions, and recommendations\n */\n\nclass AIAnalyticsService {\n  \n  /**\n   * Perform AI analysis for a patient\n   */\n  async performAnalysis(patientId, analysisType = 'progress_evaluation') {\n    try {\n      const response = await api.post(`/ai-analytics/analyze/${patientId}`, {\n        analysis_type: analysisType\n      });\n      return response.data;\n    } catch (error) {\n      console.error('AI analysis failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get AI analytics for a patient\n   */\n  async getPatientAnalytics(patientId, options = {}) {\n    try {\n      const { limit = 10, page = 1 } = options;\n      const response = await api.get(`/ai-analytics/patient/${patientId}`, {\n        params: { limit, page }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get patient analytics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get latest AI analysis for a patient\n   */\n  async getLatestAnalysis(patientId) {\n    try {\n      const response = await api.get(`/ai-analytics/patient/${patientId}/latest`);\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get latest analysis:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get treatment recommendations for a patient\n   */\n  async getTreatmentRecommendations(patientId) {\n    try {\n      const response = await api.get(`/ai-analytics/patient/${patientId}/recommendations`);\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get treatment recommendations:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get outcome predictions for a patient\n   */\n  async getOutcomePredictions(patientId) {\n    try {\n      const response = await api.get(`/ai-analytics/patient/${patientId}/predictions`);\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get outcome predictions:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get high-risk patients\n   */\n  async getHighRiskPatients() {\n    try {\n      const response = await api.get('/ai-analytics/high-risk');\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get high-risk patients:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get pending clinical alerts\n   */\n  async getPendingAlerts() {\n    try {\n      const response = await api.get('/ai-analytics/alerts');\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get pending alerts:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Dismiss a clinical alert\n   */\n  async dismissAlert(analysisId, alertIndex, reason) {\n    try {\n      const response = await api.put(`/ai-analytics/${analysisId}/alerts/${alertIndex}/dismiss`, {\n        reason\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to dismiss alert:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Add feedback to AI recommendation\n   */\n  async addFeedback(analysisId, feedback) {\n    try {\n      const response = await api.post(`/ai-analytics/${analysisId}/feedback`, feedback);\n      return response.data;\n    } catch (error) {\n      console.error('Failed to add feedback:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get AI analytics dashboard data\n   */\n  async getDashboardData(timeframe = '30') {\n    try {\n      const response = await api.get('/ai-analytics/dashboard', {\n        params: { timeframe }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get dashboard data:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Trigger batch analysis for multiple patients\n   */\n  async batchAnalyze(patientIds, analysisType = 'progress_evaluation') {\n    try {\n      const response = await api.post('/ai-analytics/batch-analyze', {\n        patient_ids: patientIds,\n        analysis_type: analysisType\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Batch analysis failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get AI analytics statistics\n   */\n  async getStatistics() {\n    try {\n      const response = await api.get('/ai-analytics/statistics');\n      return response.data;\n    } catch (error) {\n      console.error('Failed to get statistics:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Helper method to format risk level for display\n   */\n  formatRiskLevel(riskCategory) {\n    const riskLevels = {\n      'low': { label: 'Low Risk', color: 'green', icon: '✓' },\n      'medium': { label: 'Medium Risk', color: 'yellow', icon: '⚠' },\n      'high': { label: 'High Risk', color: 'orange', icon: '⚠' },\n      'critical': { label: 'Critical Risk', color: 'red', icon: '🚨' }\n    };\n    \n    return riskLevels[riskCategory] || riskLevels['low'];\n  }\n\n  /**\n   * Helper method to format confidence score\n   */\n  formatConfidence(confidence) {\n    const percentage = Math.round(confidence * 100);\n    let level = 'Low';\n    let color = 'red';\n    \n    if (percentage >= 80) {\n      level = 'High';\n      color = 'green';\n    } else if (percentage >= 60) {\n      level = 'Medium';\n      color = 'yellow';\n    }\n    \n    return {\n      percentage,\n      level,\n      color\n    };\n  }\n\n  /**\n   * Helper method to format treatment priority\n   */\n  formatPriority(priority) {\n    const priorities = {\n      1: { label: 'Urgent', color: 'red', icon: '🔴' },\n      2: { label: 'High', color: 'orange', icon: '🟠' },\n      3: { label: 'Medium', color: 'yellow', icon: '🟡' },\n      4: { label: 'Low', color: 'blue', icon: '🔵' },\n      5: { label: 'Optional', color: 'gray', icon: '⚪' }\n    };\n    \n    return priorities[priority] || priorities[3];\n  }\n\n  /**\n   * Helper method to calculate improvement trend\n   */\n  calculateTrend(currentScore, predictedScore) {\n    const improvement = predictedScore - currentScore;\n    const percentage = Math.round((improvement / currentScore) * 100);\n    \n    let trend = 'stable';\n    let icon = '➡️';\n    let color = 'gray';\n    \n    if (percentage > 10) {\n      trend = 'improving';\n      icon = '📈';\n      color = 'green';\n    } else if (percentage < -10) {\n      trend = 'declining';\n      icon = '📉';\n      color = 'red';\n    }\n    \n    return {\n      trend,\n      percentage,\n      icon,\n      color\n    };\n  }\n\n  /**\n   * Helper method to format timeline\n   */\n  formatTimeline(weeks) {\n    if (weeks < 4) {\n      return `${weeks} week${weeks !== 1 ? 's' : ''}`;\n    } else if (weeks < 52) {\n      const months = Math.round(weeks / 4);\n      return `${months} month${months !== 1 ? 's' : ''}`;\n    } else {\n      const years = Math.round(weeks / 52);\n      return `${years} year${years !== 1 ? 's' : ''}`;\n    }\n  }\n\n  /**\n   * Helper method to get alert icon and color\n   */\n  getAlertStyle(alertType) {\n    const styles = {\n      'critical': { icon: '🚨', color: 'red', bgColor: 'bg-red-50' },\n      'warning': { icon: '⚠️', color: 'orange', bgColor: 'bg-orange-50' },\n      'caution': { icon: '⚠️', color: 'yellow', bgColor: 'bg-yellow-50' },\n      'info': { icon: 'ℹ️', color: 'blue', bgColor: 'bg-blue-50' }\n    };\n    \n    return styles[alertType] || styles['info'];\n  }\n\n  /**\n   * Helper method to validate analysis data\n   */\n  validateAnalysisData(data) {\n    const required = ['patient', 'analysis_type', 'status'];\n    const missing = required.filter(field => !data[field]);\n    \n    if (missing.length > 0) {\n      throw new Error(`Missing required fields: ${missing.join(', ')}`);\n    }\n    \n    return true;\n  }\n}\n\nconst aiAnalyticsService = new AIAnalyticsService();\nexport default aiAnalyticsService;\n", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { toast } from 'react-hot-toast';\nimport {\n  ChartBarIcon,\n  ExclamationTriangleIcon,\n  UserGroupIcon,\n  ClockIcon,\n  ArrowTrendingUpIcon,\n  ArrowTrendingDownIcon,\n  BellIcon,\n  CpuChipIcon\n} from '@heroicons/react/24/outline';\n\nimport aiAnalyticsService from '../../services/aiAnalyticsService';\nimport LoadingSpinner from '../UI/LoadingSpinner';\nimport Card from '../Common/Card';\nimport Button from '../Common/Button';\n\n/**\n * AI Analytics Dashboard Component\n * Main dashboard for AI-powered analytics and insights\n */\n\nconst AIAnalyticsDashboard = () => {\n  const { t } = useTranslation();\n  const [loading, setLoading] = useState(true);\n  const [dashboardData, setDashboardData] = useState(null);\n  const [highRiskPatients, setHighRiskPatients] = useState([]);\n  const [pendingAlerts, setPendingAlerts] = useState([]);\n  const [timeframe, setTimeframe] = useState('30');\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [timeframe]);\n\n  const loadDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      const [dashboardResponse, highRiskResponse, alertsResponse] = await Promise.all([\n        aiAnalyticsService.getDashboardData(timeframe),\n        aiAnalyticsService.getHighRiskPatients(),\n        aiAnalyticsService.getPendingAlerts()\n      ]);\n\n      setDashboardData(dashboardResponse.data);\n      setHighRiskPatients(highRiskResponse.data);\n      setPendingAlerts(alertsResponse.data);\n    } catch (error) {\n      console.error('Failed to load dashboard data:', error);\n      toast.error(t('failedToLoadDashboard', 'Failed to load dashboard data'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDismissAlert = async (analysisId, alertIndex, reason) => {\n    try {\n      await aiAnalyticsService.dismissAlert(analysisId, alertIndex, reason);\n      toast.success(t('alertDismissed', 'Alert dismissed successfully'));\n      loadDashboardData(); // Refresh data\n    } catch (error) {\n      console.error('Failed to dismiss alert:', error);\n      toast.error(t('failedToDismissAlert', 'Failed to dismiss alert'));\n    }\n  };\n\n  const StatCard = ({ title, value, icon: Icon, color, trend, subtitle }) => (\n    <Card className=\"p-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600\">{title}</p>\n          <p className={`text-2xl font-bold text-${color}-600`}>{value}</p>\n          {subtitle && <p className=\"text-xs text-gray-500 mt-1\">{subtitle}</p>}\n        </div>\n        <div className={`p-3 rounded-full bg-${color}-100`}>\n          <Icon className={`h-6 w-6 text-${color}-600`} />\n        </div>\n      </div>\n      {trend && (\n        <div className=\"mt-4 flex items-center\">\n          {trend.direction === 'up' ? (\n            <ArrowTrendingUpIcon className=\"h-4 w-4 text-green-500 mr-1\" />\n          ) : (\n            <ArrowTrendingDownIcon className=\"h-4 w-4 text-red-500 mr-1\" />\n          )}\n          <span className={`text-sm ${trend.direction === 'up' ? 'text-green-600' : 'text-red-600'}`}>\n            {trend.percentage}% {trend.period}\n          </span>\n        </div>\n      )}\n    </Card>\n  );\n\n  const RiskPatientCard = ({ patient, analysis }) => {\n    const riskInfo = aiAnalyticsService.formatRiskLevel(analysis.risk_assessment?.risk_category);\n    \n    return (\n      <div className=\"border rounded-lg p-4 hover:shadow-md transition-shadow\">\n        <div className=\"flex items-center justify-between mb-2\">\n          <h4 className=\"font-medium text-gray-900\">\n            {patient.firstName} {patient.lastName}\n          </h4>\n          <span className={`px-2 py-1 rounded-full text-xs font-medium bg-${riskInfo.color}-100 text-${riskInfo.color}-800`}>\n            {riskInfo.icon} {riskInfo.label}\n          </span>\n        </div>\n        <p className=\"text-sm text-gray-600 mb-2\">\n          Risk Score: {analysis.risk_assessment?.overall_risk_score}/100\n        </p>\n        <div className=\"text-xs text-gray-500\">\n          Last Analysis: {new Date(analysis.analysis_date).toLocaleDateString()}\n        </div>\n      </div>\n    );\n  };\n\n  const AlertCard = ({ alert, analysis, onDismiss }) => {\n    const alertStyle = aiAnalyticsService.getAlertStyle(alert.type);\n    \n    return (\n      <div className={`border-l-4 border-${alertStyle.color}-400 ${alertStyle.bgColor} p-4 rounded-r-lg`}>\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-start\">\n            <span className=\"text-lg mr-2\">{alertStyle.icon}</span>\n            <div>\n              <h4 className={`font-medium text-${alertStyle.color}-800`}>\n                {alert.type.charAt(0).toUpperCase() + alert.type.slice(1)} Alert\n              </h4>\n              <p className={`text-sm text-${alertStyle.color}-700 mt-1`}>\n                {alert.message}\n              </p>\n              <p className=\"text-xs text-gray-500 mt-2\">\n                Patient: {analysis.patient?.firstName} {analysis.patient?.lastName}\n              </p>\n            </div>\n          </div>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onDismiss(analysis._id, alert.index, 'Reviewed by clinician')}\n            className=\"ml-4\"\n          >\n            Dismiss\n          </Button>\n        </div>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <LoadingSpinner size=\"lg\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n            <CpuChipIcon className=\"h-8 w-8 mr-3 text-blue-600\" />\n            {t('aiAnalyticsDashboard', 'AI Analytics Dashboard')}\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            {t('aiDashboardDescription', 'AI-powered insights and predictions for patient care')}\n          </p>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <select\n            value={timeframe}\n            onChange={(e) => setTimeframe(e.target.value)}\n            className=\"border border-gray-300 rounded-md px-3 py-2 text-sm\"\n          >\n            <option value=\"7\">Last 7 days</option>\n            <option value=\"30\">Last 30 days</option>\n            <option value=\"90\">Last 90 days</option>\n          </select>\n          \n          <Button onClick={loadDashboardData} variant=\"outline\">\n            {t('refresh', 'Refresh')}\n          </Button>\n        </div>\n      </div>\n\n      {/* Summary Statistics */}\n      {dashboardData && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <StatCard\n            title={t('totalAnalyses', 'Total Analyses')}\n            value={dashboardData.summary.total_analyses}\n            icon={ChartBarIcon}\n            color=\"blue\"\n            subtitle={`Last ${timeframe} days`}\n          />\n          \n          <StatCard\n            title={t('highRiskPatients', 'High Risk Patients')}\n            value={dashboardData.summary.high_risk_patients}\n            icon={ExclamationTriangleIcon}\n            color=\"red\"\n            subtitle=\"Require attention\"\n          />\n          \n          <StatCard\n            title={t('pendingAlerts', 'Pending Alerts')}\n            value={dashboardData.summary.pending_alerts}\n            icon={BellIcon}\n            color=\"orange\"\n            subtitle=\"Need review\"\n          />\n          \n          <StatCard\n            title={t('avgProcessingTime', 'Avg Processing Time')}\n            value={`${Math.round(dashboardData.analysis_trends.reduce((acc, curr) => acc + curr.avgProcessingTime, 0) / dashboardData.analysis_trends.length / 1000)}s`}\n            icon={ClockIcon}\n            color=\"green\"\n            subtitle=\"Per analysis\"\n          />\n        </div>\n      )}\n\n      {/* Risk Distribution and Trends */}\n      {dashboardData && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Risk Distribution */}\n          <Card className=\"p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              {t('riskDistribution', 'Risk Distribution')}\n            </h3>\n            <div className=\"space-y-3\">\n              {dashboardData.risk_distribution.map((risk) => (\n                <div key={risk._id} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium text-gray-700 capitalize\">\n                    {risk._id || 'Unknown'} Risk\n                  </span>\n                  <div className=\"flex items-center\">\n                    <div className=\"w-32 bg-gray-200 rounded-full h-2 mr-3\">\n                      <div\n                        className={`h-2 rounded-full ${\n                          risk._id === 'critical' ? 'bg-red-500' :\n                          risk._id === 'high' ? 'bg-orange-500' :\n                          risk._id === 'medium' ? 'bg-yellow-500' : 'bg-green-500'\n                        }`}\n                        style={{\n                          width: `${(risk.count / dashboardData.summary.total_analyses) * 100}%`\n                        }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm font-semibold text-gray-900\">\n                      {risk.count}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card>\n\n          {/* Model Performance */}\n          <Card className=\"p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              {t('modelPerformance', 'Model Performance')}\n            </h3>\n            <div className=\"space-y-3\">\n              {dashboardData.model_performance.map((model) => (\n                <div key={model._id} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium text-gray-700 capitalize\">\n                    {model._id.replace('_', ' ')}\n                  </span>\n                  <div className=\"flex items-center\">\n                    <div className=\"w-24 bg-gray-200 rounded-full h-2 mr-3\">\n                      <div\n                        className=\"h-2 rounded-full bg-blue-500\"\n                        style={{ width: `${model.avgConfidence * 100}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm font-semibold text-gray-900\">\n                      {Math.round(model.avgConfidence * 100)}%\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card>\n        </div>\n      )}\n\n      {/* High Risk Patients and Alerts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* High Risk Patients */}\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              {t('highRiskPatients', 'High Risk Patients')}\n            </h3>\n            <UserGroupIcon className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          \n          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\n            {highRiskPatients.length > 0 ? (\n              highRiskPatients.map((item) => (\n                <RiskPatientCard\n                  key={item._id}\n                  patient={item.patient}\n                  analysis={item}\n                />\n              ))\n            ) : (\n              <p className=\"text-gray-500 text-center py-4\">\n                {t('noHighRiskPatients', 'No high-risk patients found')}\n              </p>\n            )}\n          </div>\n        </Card>\n\n        {/* Pending Alerts */}\n        <Card className=\"p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">\n              {t('pendingAlerts', 'Pending Alerts')}\n            </h3>\n            <BellIcon className=\"h-5 w-5 text-gray-400\" />\n          </div>\n          \n          <div className=\"space-y-3 max-h-64 overflow-y-auto\">\n            {pendingAlerts.length > 0 ? (\n              pendingAlerts.map((item) =>\n                item.clinical_alerts\n                  .filter(alert => !alert.dismissed)\n                  .map((alert, index) => (\n                    <AlertCard\n                      key={`${item._id}-${index}`}\n                      alert={{ ...alert, index }}\n                      analysis={item}\n                      onDismiss={handleDismissAlert}\n                    />\n                  ))\n              )\n            ) : (\n              <p className=\"text-gray-500 text-center py-4\">\n                {t('noPendingAlerts', 'No pending alerts')}\n              </p>\n            )}\n          </div>\n        </Card>\n      </div>\n    </div>\n  );\n};\n\nexport default AIAnalyticsDashboard;\n", "import React from 'react';\n\n/**\n * Button Component\n * A reusable button component with multiple variants and sizes\n */\n\nconst Button = ({\n  children,\n  variant = 'primary',\n  size = 'md',\n  disabled = false,\n  loading = false,\n  className = '',\n  type = 'button',\n  onClick,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200';\n  \n  const variants = {\n    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',\n    secondary: 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500',\n    outline: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500',\n    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',\n    success: 'bg-green-600 hover:bg-green-700 text-white focus:ring-green-500',\n    warning: 'bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500',\n    ghost: 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500'\n  };\n\n  const sizes = {\n    xs: 'px-2.5 py-1.5 text-xs',\n    sm: 'px-3 py-2 text-sm',\n    md: 'px-4 py-2 text-sm',\n    lg: 'px-4 py-2 text-base',\n    xl: 'px-6 py-3 text-base'\n  };\n\n  const disabledClasses = disabled || loading ? 'opacity-50 cursor-not-allowed' : '';\n  \n  const buttonClasses = [\n    baseClasses,\n    variants[variant] || variants.primary,\n    sizes[size] || sizes.md,\n    disabledClasses,\n    className\n  ].filter(Boolean).join(' ');\n\n  const handleClick = (e) => {\n    if (disabled || loading) {\n      e.preventDefault();\n      return;\n    }\n    if (onClick) {\n      onClick(e);\n    }\n  };\n\n  return (\n    <button\n      type={type}\n      className={buttonClasses}\n      onClick={handleClick}\n      disabled={disabled || loading}\n      {...props}\n    >\n      {loading && (\n        <svg\n          className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n          fill=\"none\"\n          viewBox=\"0 0 24 24\"\n        >\n          <circle\n            className=\"opacity-25\"\n            cx=\"12\"\n            cy=\"12\"\n            r=\"10\"\n            stroke=\"currentColor\"\n            strokeWidth=\"4\"\n          ></circle>\n          <path\n            className=\"opacity-75\"\n            fill=\"currentColor\"\n            d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n          ></path>\n        </svg>\n      )}\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n", "import * as React from \"react\";\nfunction BellIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BellIcon);\nexport default ForwardRef;", "import React from 'react';\n\n/**\n * Card Component\n * A reusable card component with consistent styling\n */\n\nconst Card = ({ \n  children, \n  className = '', \n  padding = 'p-6',\n  shadow = 'shadow-sm',\n  border = 'border border-gray-200',\n  rounded = 'rounded-lg',\n  background = 'bg-white',\n  hover = '',\n  ...props \n}) => {\n  const cardClasses = [\n    background,\n    border,\n    rounded,\n    shadow,\n    padding,\n    hover,\n    className\n  ].filter(Boolean).join(' ');\n\n  return (\n    <div className={cardClasses} {...props}>\n      {children}\n    </div>\n  );\n};\n\nexport default Card;\n", "import * as React from \"react\";\nfunction ChartBarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChartBarIcon);\nexport default ForwardRef;", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose.js\";\nfunction _objectWithoutProperties(e, t) {\n  if (null == e) return {};\n  var o,\n    r,\n    i = objectWithoutPropertiesLoose(e, t);\n  if (Object.getOwnPropertySymbols) {\n    var n = Object.getOwnPropertySymbols(e);\n    for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);\n  }\n  return i;\n}\nexport { _objectWithoutProperties as default };", "function _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (-1 !== e.indexOf(n)) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\nexport { _objectWithoutPropertiesLoose as default };", "import * as React from \"react\";\nfunction ClockIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ClockIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction UserGroupIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(UserGroupIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction ExclamationTriangleIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ExclamationTriangleIcon);\nexport default ForwardRef;", "import * as React from \"react\";\nfunction CpuChipIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(CpuChipIcon);\nexport default ForwardRef;"], "names": ["ArrowTrendingUpIcon", "_ref", "svgRef", "title", "titleId", "props", "_objectWithoutProperties", "_excluded", "React", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "ArrowTrendingDownIcon", "performAnalysis", "patientId", "analysisType", "arguments", "length", "undefined", "api", "post", "concat", "analysis_type", "data", "error", "console", "getPatientAnalytics", "options", "limit", "page", "get", "params", "getLatestAnalysis", "getTreatmentRecommendations", "getOutcomePredictions", "getHighRiskPatients", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "analysisId", "alertIndex", "reason", "put", "addFeedback", "feedback", "getDashboardData", "timeframe", "batchAnalyze", "patientIds", "patient_ids", "getStatistics", "formatRiskLevel", "riskCategory", "riskLevels", "label", "color", "icon", "formatConfidence", "confidence", "percentage", "Math", "round", "level", "formatPriority", "priority", "priorities", "calculateTrend", "currentScore", "predictedScore", "improvement", "trend", "formatTimeline", "weeks", "months", "years", "getAlertStyle", "alertType", "styles", "bgColor", "validateAnalysisData", "missing", "filter", "field", "Error", "join", "AIAnalyticsDashboard", "t", "useTranslation", "loading", "setLoading", "useState", "dashboardData", "setDashboardData", "highRiskPatients", "setHighRiskPatients", "<PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "setTimeframe", "useEffect", "loadDashboardData", "async", "dashboardResponse", "highRiskResponse", "alertsResponse", "Promise", "all", "aiAnalyticsService", "toast", "handleDismissAlert", "success", "StatCard", "value", "Icon", "subtitle", "_jsxs", "Card", "className", "children", "_jsx", "direction", "period", "RiskPatientCard", "_ref2", "_analysis$risk_assess", "_analysis$risk_assess2", "patient", "analysis", "riskInfo", "risk_assessment", "risk_category", "firstName", "lastName", "overall_risk_score", "Date", "analysis_date", "toLocaleDateString", "AlertCard", "_ref3", "_analysis$patient", "_analysis$patient2", "alert", "on<PERSON><PERSON><PERSON>", "alertStyle", "type", "char<PERSON>t", "toUpperCase", "slice", "message", "<PERSON><PERSON>", "variant", "size", "onClick", "_id", "index", "LoadingSpinner", "CpuChipIcon", "onChange", "e", "target", "summary", "total_analyses", "ChartBarIcon", "high_risk_patients", "ExclamationTriangleIcon", "pending_alerts", "BellIcon", "analysis_trends", "reduce", "acc", "curr", "avgProcessingTime", "ClockIcon", "risk_distribution", "map", "risk", "style", "width", "count", "model_performance", "model", "replace", "avgConfidence", "UserGroupIcon", "item", "clinical_alerts", "dismissed", "_objectSpread", "disabled", "variants", "primary", "secondary", "outline", "danger", "warning", "ghost", "sizes", "xs", "sm", "md", "lg", "xl", "buttonClasses", "Boolean", "preventDefault", "cx", "cy", "r", "padding", "shadow", "border", "rounded", "background", "hover", "cardClasses", "o", "i", "n", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable"], "sourceRoot": ""}