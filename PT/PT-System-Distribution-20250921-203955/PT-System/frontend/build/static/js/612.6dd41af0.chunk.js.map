{"version": 3, "file": "static/js/612.6dd41af0.chunk.js", "mappings": "2MAIA,MAmeA,EAneiBA,KACf,MAAM,EAAEC,EAAC,MAAEC,EAAK,SAAEC,EAAQ,YAAEC,IAAgBC,EAAAA,EAAAA,MACtC,MAAEC,EAAK,SAAEC,IAAaC,EAAAA,EAAAA,MACrBC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,YACpCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CACvCG,cAAe,CACbC,OAAO,EACPC,KAAK,EACLC,MAAM,EACNC,cAAc,EACdC,SAAS,GAEXC,QAAS,CACPC,WAAW,EACXC,WAAW,EACXC,WAAW,GAEbC,OAAQ,CACNC,UAAU,EACVC,gBAAiB,QACjBC,eAAgB,MAIdC,EAAO,CACX,CAAEC,GAAI,UAAWC,MAAO7B,EAAE,UAAW,WAAY8B,KAAM,cACvD,CAAEF,GAAI,aAAcC,MAAO7B,EAAE,aAAc,cAAe8B,KAAM,kBAChE,CAAEF,GAAI,gBAAiBC,MAAO7B,EAAE,gBAAiB,iBAAkB8B,KAAM,eACzE,CAAEF,GAAI,UAAWC,MAAO7B,EAAE,UAAW,WAAY8B,KAAM,qBACvD,CAAEF,GAAI,SAAUC,MAAO7B,EAAE,SAAU,UAAW8B,KAAM,iBACpD,CAAEF,GAAI,UAAWC,MAAO7B,EAAE,UAAW,WAAY8B,KAAM,gBAGnDC,EAAgBA,CAACC,EAAUC,EAAKC,KACpCtB,EAAYuB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACH,IAAQI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACJD,EAAKH,IAAS,IACjB,CAACC,GAAMC,QAKb,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAC,OAAStC,EAAQ,cAAgB,gBAAiBuC,SAAA,EAE9DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yCAAwCE,UACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DxC,EAAE,WAAY,eAEjByC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CxC,EAAE,eAAgB,iEAKzBqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EAEpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,UAC5BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0FAAyFE,UACtGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,SAC3Bb,EAAKe,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMnC,EAAakC,EAAIf,IAChCU,UAAS,uFAAAC,OACP/B,IAAcmC,EAAIf,GACd,mEACA,6EACHY,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAWK,EAAIb,QAClBW,EAAAA,EAAAA,KAAA,QAAAD,SAAOG,EAAId,UATNc,EAAIf,YAiBnBa,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,UAC5BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFE,SAAA,CAGvF,YAAdhC,IACC6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrExC,EAAE,kBAAmB,uBAGxBqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,aAAc,kBAEnByC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLC,aAAa,gJACbR,UAAU,wIAIdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,gBAAiB,qBAEtByC,EAAAA,EAAAA,KAAA,YACEM,KAAK,IACLD,aAAa,qQACbR,UAAU,wIAIdD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,cAAe,mBAEpByC,EAAAA,EAAAA,KAAA,SACEI,KAAK,MACLC,aAAa,mBACbR,UAAU,wIAIdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,QAAS,YAEdyC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLC,aAAa,oBACbR,UAAU,2IAKhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,WAAY,eAEjBqC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kIAAiIE,SAAA,EACjJC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,cAAaM,SAAExC,EAAE,aAAc,0BAC7CyC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,aAAYM,SAAExC,EAAE,YAAa,yBAC3CyC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,MAAKM,SAAExC,EAAE,MAAO,wBAIlCqC,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,WAAY,eAEjBqC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kIAAiIE,SAAA,EACjJC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,MAAKM,SAAExC,EAAE,MAAO,wBAC9ByC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,MAAKM,SAAExC,EAAE,MAAO,sBAC9ByC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,MAAKM,SAAExC,EAAE,MAAO,+BAS3B,eAAdQ,IACC6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrExC,EAAE,qBAAsB,0BAG3BqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,WAAY,eAEjBqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMzC,EAAY,MAC3BmC,UAAS,6CAAAC,OACM,OAAbrC,EACI,iDACA,8DACHsC,UAEHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,SAAC,8BAC/BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAC,kBAG/DC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMzC,EAAY,MAC3BmC,UAAS,6CAAAC,OACM,OAAbrC,EACI,iDACA,8DACHsC,UAEHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,SAAC,8BAC/BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAC,2DAMnEH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,QAAS,YAEdqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMtC,EAAS,SACxBgC,UAAS,6CAAAC,OACG,UAAVlC,EACI,iDACA,8DACHmC,UAEHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,SAAC,kBAC/BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAExC,EAAE,QAAS,iBAG3EyC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMtC,EAAS,QACxBgC,UAAS,6CAAAC,OACG,SAAVlC,EACI,iDACA,8DACHmC,UAEHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,SAAC,kBAC/BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAExC,EAAE,OAAQ,gBAG1EyC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMtC,EAAS,UACxBgC,UAAS,6CAAAC,OACG,WAAVlC,EACI,iDACA,8DACHmC,UAEHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaE,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,SAAC,kBAC/BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAExC,EAAE,SAAU,wBAMhFqC,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,WAAY,gBAEjBqC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kIAAiIE,SAAA,EACjJC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,QAAOM,SAAExC,EAAE,QAAS,YAClCyC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,SAASc,UAAQ,EAAAR,SAAExC,EAAE,SAAU,aAC7CyC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,QAAOM,SAAExC,EAAE,QAAS,uBAQ7B,kBAAdQ,IACC6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrExC,EAAE,uBAAwB,4BAG7BqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExC,EAAE,uBAAwB,4BAE7ByC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBS,OAAOC,QAAQvC,EAASE,eAAesC,MAAM,EAAG,GAAGT,IAAIU,IAAA,IAAEnB,EAAKC,GAAMkB,EAAA,OACnEf,EAAAA,EAAAA,MAAA,OAAeC,UAAU,oCAAmCE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SACvDxC,EAAEiC,EAAKA,EAAIoB,OAAO,GAAGC,cAAgBrB,EAAIkB,MAAM,OAElDV,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDxC,EAAE,GAADuC,OAAIN,EAAG,qCAAAM,OAAqCN,UAGlDI,EAAAA,EAAAA,MAAA,SAAOC,UAAU,mDAAkDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLU,QAASrB,EACTsB,SAAWC,GAAM1B,EAAc,gBAAiBE,EAAKwB,EAAEC,OAAOH,SAC9DjB,UAAU,kBAEZG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qcAhBTL,WAuBhBI,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExC,EAAE,oBAAqB,yBAE1ByC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBS,OAAOC,QAAQvC,EAASE,eAAesC,MAAM,GAAGT,IAAIiB,IAAA,IAAE1B,EAAKC,GAAMyB,EAAA,OAChEtB,EAAAA,EAAAA,MAAA,OAAeC,UAAU,oCAAmCE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SACvDxC,EAAEiC,EAAKA,EAAIoB,OAAO,GAAGC,cAAgBrB,EAAIkB,MAAM,OAElDV,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDxC,EAAE,GAADuC,OAAIN,EAAG,0CAAAM,OAA0CN,EAAI2B,sBAG3DvB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,mDAAkDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLU,QAASrB,EACTsB,SAAWC,GAAM1B,EAAc,gBAAiBE,EAAKwB,EAAEC,OAAOH,SAC9DjB,UAAU,kBAEZG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qcAhBTL,gBA2BP,YAAdzB,IACC6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrExC,EAAE,kBAAmB,uBAGxByC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBS,OAAOC,QAAQvC,EAASQ,SAASuB,IAAImB,IAAA,IAAE5B,EAAKC,GAAM2B,EAAA,OACjDxB,EAAAA,EAAAA,MAAA,OAAeC,UAAU,oCAAmCE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SACvDxC,EAAEiC,EAAKA,EAAIoB,OAAO,GAAGC,cAAgBrB,EAAIkB,MAAM,GAAGW,QAAQ,WAAY,WAEzErB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDxC,EAAE,GAADuC,OAAIN,EAAG,0BAAAM,OAA0BN,EAAI2B,cAAa,4BAGxDvB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,mDAAkDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLU,QAASrB,EACTsB,SAAWC,GAAM1B,EAAc,UAAWE,EAAKwB,EAAEC,OAAOH,SACxDjB,UAAU,kBAEZG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qcAhBTL,UAyBH,WAAdzB,IACC6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrExC,EAAE,iBAAkB,sBAGvBqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SACvDxC,EAAE,WAAY,gBAEjByC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDxC,EAAE,eAAgB,oCAGvBqC,EAAAA,EAAAA,MAAA,SAAOC,UAAU,mDAAkDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLU,QAAS5C,EAASY,OAAOC,SACzBgC,SAAWC,GAAM1B,EAAc,SAAU,WAAY0B,EAAEC,OAAOH,SAC9DjB,UAAU,kBAEZG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,ucAInBD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,kBAAmB,uBAExBqC,EAAAA,EAAAA,MAAA,UACEH,MAAOvB,EAASY,OAAOE,gBACvB+B,SAAWC,GAAM1B,EAAc,SAAU,kBAAmB0B,EAAEC,OAAOxB,OACrEI,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,SAAQM,SAAExC,EAAE,SAAU,aACpCyC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,QAAOM,SAAExC,EAAE,QAAS,YAClCyC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,SAAQM,SAAExC,EAAE,SAAU,aACpCyC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,UAASM,SAAExC,EAAE,UAAW,oBAI1CqC,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExC,EAAE,iBAAkB,gCAEvBqC,EAAAA,EAAAA,MAAA,UACEH,MAAOvB,EAASY,OAAOG,eACvB8B,SAAWC,GAAM1B,EAAc,SAAU,iBAAkBgC,SAASN,EAAEC,OAAOxB,QAC7EI,UAAU,kIAAiIE,SAAA,EAE3IH,EAAAA,EAAAA,MAAA,UAAQH,MAAO,GAAGM,SAAA,CAAC,MAAIxC,EAAE,UAAW,eACpCqC,EAAAA,EAAAA,MAAA,UAAQH,MAAO,GAAGM,SAAA,CAAC,MAAIxC,EAAE,UAAW,eACpCqC,EAAAA,EAAAA,MAAA,UAAQH,MAAO,GAAGM,SAAA,CAAC,MAAIxC,EAAE,UAAW,eACpCqC,EAAAA,EAAAA,MAAA,UAAQH,MAAO,IAAIM,SAAA,CAAC,OAAKxC,EAAE,UAAW,0BAQjC,YAAdQ,IACC6B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrExC,EAAE,kBAAmB,uBAGxByC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBE,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gEACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExC,EAAE,oBAAqB,yBAE1ByC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjDxC,EAAE,cAAe,gDAEpBqC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,yFAAwFE,SACvGxC,EAAE,iBAAkB,sBAEvByC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,uJAAsJE,SACrKxC,EAAE,gBAAiB,qBAEtByC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,uFAAsFE,SACrGxC,EAAE,gBAAiB,gCAShCyC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mGAAkGE,UAC/GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,kFAAiFE,SAChGxC,EAAE,QAAS,YAEdyC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,kFAAiFE,SAChGxC,EAAE,cAAe,kC", "sources": ["pages/Settings/Settings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst Settings = () => {\n  const { t, isRTL, language, setLanguage } = useLanguage();\n  const { theme, setTheme } = useTheme();\n  const [activeTab, setActiveTab] = useState('general');\n  const [settings, setSettings] = useState({\n    notifications: {\n      email: true,\n      sms: false,\n      push: true,\n      appointments: true,\n      reports: false\n    },\n    privacy: {\n      shareData: false,\n      analytics: true,\n      marketing: false\n    },\n    system: {\n      autoSave: true,\n      backupFrequency: 'daily',\n      sessionTimeout: 30\n    }\n  });\n\n  const tabs = [\n    { id: 'general', label: t('general', 'General'), icon: 'fas fa-cog' },\n    { id: 'appearance', label: t('appearance', 'Appearance'), icon: 'fas fa-palette' },\n    { id: 'notifications', label: t('notifications', 'Notifications'), icon: 'fas fa-bell' },\n    { id: 'privacy', label: t('privacy', 'Privacy'), icon: 'fas fa-shield-alt' },\n    { id: 'system', label: t('system', 'System'), icon: 'fas fa-server' },\n    { id: 'account', label: t('account', 'Account'), icon: 'fas fa-user' }\n  ];\n\n  const updateSetting = (category, key, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('settings', 'Settings')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('settingsDesc', 'Manage your application preferences and configuration')}\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Sidebar Navigation */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <nav className=\"p-4 space-y-2\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                    activeTab === tab.id\n                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <i className={tab.icon}></i>\n                  <span>{tab.label}</span>\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Content Area */}\n        <div className=\"lg:col-span-3\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n\n            {/* General Settings */}\n            {activeTab === 'general' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('generalSettings', 'General Settings')}\n                </h2>\n\n                <div className=\"space-y-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('clinicName', 'Clinic Name')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      defaultValue=\"مركز الرياض للعلاج الطبيعي\"\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('clinicAddress', 'Clinic Address')}\n                    </label>\n                    <textarea\n                      rows=\"3\"\n                      defaultValue=\"شارع الملك فهد، الرياض، المملكة العربية السعودية\"\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('phoneNumber', 'Phone Number')}\n                      </label>\n                      <input\n                        type=\"tel\"\n                        defaultValue=\"+966 11 234 5678\"\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('email', 'Email')}\n                      </label>\n                      <input\n                        type=\"email\"\n                        defaultValue=\"<EMAIL>\"\n                        className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('timezone', 'Timezone')}\n                      </label>\n                      <select className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\">\n                        <option value=\"Asia/Riyadh\">{t('riyadhTime', 'Riyadh Time (GMT+3)')}</option>\n                        <option value=\"Asia/Dubai\">{t('dubaiTime', 'Dubai Time (GMT+4)')}</option>\n                        <option value=\"UTC\">{t('utc', 'UTC (GMT+0)')}</option>\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                        {t('currency', 'Currency')}\n                      </label>\n                      <select className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\">\n                        <option value=\"SAR\">{t('sar', 'Saudi Riyal (SAR)')}</option>\n                        <option value=\"USD\">{t('usd', 'US Dollar (USD)')}</option>\n                        <option value=\"EUR\">{t('eur', 'Euro (EUR)')}</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Appearance Settings */}\n            {activeTab === 'appearance' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('appearanceSettings', 'Appearance Settings')}\n                </h2>\n\n                <div className=\"space-y-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                      {t('language', 'Language')}\n                    </label>\n                    <div className=\"grid grid-cols-2 gap-4\">\n                      <button\n                        onClick={() => setLanguage('en')}\n                        className={`p-4 border-2 rounded-lg transition-colors ${\n                          language === 'en'\n                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl mb-2\">🇺🇸</div>\n                          <div className=\"font-medium text-gray-900 dark:text-white\">English</div>\n                        </div>\n                      </button>\n                      <button\n                        onClick={() => setLanguage('ar')}\n                        className={`p-4 border-2 rounded-lg transition-colors ${\n                          language === 'ar'\n                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl mb-2\">🇸🇦</div>\n                          <div className=\"font-medium text-gray-900 dark:text-white\">العربية</div>\n                        </div>\n                      </button>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                      {t('theme', 'Theme')}\n                    </label>\n                    <div className=\"grid grid-cols-3 gap-4\">\n                      <button\n                        onClick={() => setTheme('light')}\n                        className={`p-4 border-2 rounded-lg transition-colors ${\n                          theme === 'light'\n                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl mb-2\">☀️</div>\n                          <div className=\"font-medium text-gray-900 dark:text-white\">{t('light', 'Light')}</div>\n                        </div>\n                      </button>\n                      <button\n                        onClick={() => setTheme('dark')}\n                        className={`p-4 border-2 rounded-lg transition-colors ${\n                          theme === 'dark'\n                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl mb-2\">🌙</div>\n                          <div className=\"font-medium text-gray-900 dark:text-white\">{t('dark', 'Dark')}</div>\n                        </div>\n                      </button>\n                      <button\n                        onClick={() => setTheme('system')}\n                        className={`p-4 border-2 rounded-lg transition-colors ${\n                          theme === 'system'\n                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                        }`}\n                      >\n                        <div className=\"text-center\">\n                          <div className=\"text-2xl mb-2\">💻</div>\n                          <div className=\"font-medium text-gray-900 dark:text-white\">{t('system', 'System')}</div>\n                        </div>\n                      </button>\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('fontSize', 'Font Size')}\n                    </label>\n                    <select className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\">\n                      <option value=\"small\">{t('small', 'Small')}</option>\n                      <option value=\"medium\" selected>{t('medium', 'Medium')}</option>\n                      <option value=\"large\">{t('large', 'Large')}</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Notifications Settings */}\n            {activeTab === 'notifications' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('notificationSettings', 'Notification Settings')}\n                </h2>\n\n                <div className=\"space-y-6\">\n                  <div>\n                    <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                      {t('notificationChannels', 'Notification Channels')}\n                    </h3>\n                    <div className=\"space-y-4\">\n                      {Object.entries(settings.notifications).slice(0, 3).map(([key, value]) => (\n                        <div key={key} className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"font-medium text-gray-900 dark:text-white\">\n                              {t(key, key.charAt(0).toUpperCase() + key.slice(1))}\n                            </div>\n                            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                              {t(`${key}Desc`, `Receive notifications via ${key}`)}\n                            </div>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={value}\n                              onChange={(e) => updateSetting('notifications', key, e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  <div>\n                    <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                      {t('notificationTypes', 'Notification Types')}\n                    </h3>\n                    <div className=\"space-y-4\">\n                      {Object.entries(settings.notifications).slice(3).map(([key, value]) => (\n                        <div key={key} className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"font-medium text-gray-900 dark:text-white\">\n                              {t(key, key.charAt(0).toUpperCase() + key.slice(1))}\n                            </div>\n                            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                              {t(`${key}NotificationDesc`, `Get notified about ${key.toLowerCase()}`)}\n                            </div>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={value}\n                              onChange={(e) => updateSetting('notifications', key, e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Privacy Settings */}\n            {activeTab === 'privacy' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('privacySettings', 'Privacy Settings')}\n                </h2>\n\n                <div className=\"space-y-6\">\n                  {Object.entries(settings.privacy).map(([key, value]) => (\n                    <div key={key} className=\"flex items-center justify-between\">\n                      <div>\n                        <div className=\"font-medium text-gray-900 dark:text-white\">\n                          {t(key, key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'))}\n                        </div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {t(`${key}PrivacyDesc`, `Control ${key.toLowerCase()} privacy settings`)}\n                        </div>\n                      </div>\n                      <label className=\"relative inline-flex items-center cursor-pointer\">\n                        <input\n                          type=\"checkbox\"\n                          checked={value}\n                          onChange={(e) => updateSetting('privacy', key, e.target.checked)}\n                          className=\"sr-only peer\"\n                        />\n                        <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                      </label>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* System Settings */}\n            {activeTab === 'system' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('systemSettings', 'System Settings')}\n                </h2>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <div className=\"font-medium text-gray-900 dark:text-white\">\n                        {t('autoSave', 'Auto Save')}\n                      </div>\n                      <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {t('autoSaveDesc', 'Automatically save changes')}\n                      </div>\n                    </div>\n                    <label className=\"relative inline-flex items-center cursor-pointer\">\n                      <input\n                        type=\"checkbox\"\n                        checked={settings.system.autoSave}\n                        onChange={(e) => updateSetting('system', 'autoSave', e.target.checked)}\n                        className=\"sr-only peer\"\n                      />\n                      <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                    </label>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('backupFrequency', 'Backup Frequency')}\n                    </label>\n                    <select\n                      value={settings.system.backupFrequency}\n                      onChange={(e) => updateSetting('system', 'backupFrequency', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"hourly\">{t('hourly', 'Hourly')}</option>\n                      <option value=\"daily\">{t('daily', 'Daily')}</option>\n                      <option value=\"weekly\">{t('weekly', 'Weekly')}</option>\n                      <option value=\"monthly\">{t('monthly', 'Monthly')}</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('sessionTimeout', 'Session Timeout (minutes)')}\n                    </label>\n                    <select\n                      value={settings.system.sessionTimeout}\n                      onChange={(e) => updateSetting('system', 'sessionTimeout', parseInt(e.target.value))}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value={15}>15 {t('minutes', 'minutes')}</option>\n                      <option value={30}>30 {t('minutes', 'minutes')}</option>\n                      <option value={60}>60 {t('minutes', 'minutes')}</option>\n                      <option value={120}>120 {t('minutes', 'minutes')}</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Account Settings */}\n            {activeTab === 'account' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('accountSettings', 'Account Settings')}\n                </h2>\n\n                <div className=\"space-y-6\">\n                  <div className=\"text-center py-8\">\n                    <i className=\"fas fa-user text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                    <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                      {t('accountManagement', 'Account Management')}\n                    </h3>\n                    <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n                      {t('accountDesc', 'Manage your account settings and security')}\n                    </p>\n                    <div className=\"space-y-3\">\n                      <button className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                        {t('changePassword', 'Change Password')}\n                      </button>\n                      <button className=\"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n                        {t('updateProfile', 'Update Profile')}\n                      </button>\n                      <button className=\"w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors\">\n                        {t('deleteAccount', 'Delete Account')}\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Save Button */}\n            <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 rounded-b-lg\">\n              <div className=\"flex justify-end space-x-4\">\n                <button className=\"px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\">\n                  {t('reset', 'Reset')}\n                </button>\n                <button className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                  {t('saveChanges', 'Save Changes')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "names": ["Settings", "t", "isRTL", "language", "setLanguage", "useLanguage", "theme", "setTheme", "useTheme", "activeTab", "setActiveTab", "useState", "settings", "setSettings", "notifications", "email", "sms", "push", "appointments", "reports", "privacy", "shareData", "analytics", "marketing", "system", "autoSave", "backupFrequency", "sessionTimeout", "tabs", "id", "label", "icon", "updateSetting", "category", "key", "value", "prev", "_objectSpread", "_jsxs", "className", "concat", "children", "_jsx", "map", "tab", "onClick", "type", "defaultValue", "rows", "selected", "Object", "entries", "slice", "_ref", "char<PERSON>t", "toUpperCase", "checked", "onChange", "e", "target", "_ref2", "toLowerCase", "_ref3", "replace", "parseInt"], "sourceRoot": ""}