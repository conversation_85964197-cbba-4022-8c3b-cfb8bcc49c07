{"version": 3, "file": "static/js/8324.8a837c48.chunk.js", "mappings": "uNAKA,MAoeA,EApegBA,KACd,MAAM,EAAEC,EAAC,MAAEC,EAAK,SAAEC,EAAQ,YAAEC,IAAgBC,EAAAA,EAAAA,MACtC,KAAEC,IAASC,EAAAA,EAAAA,MACX,MAAEC,EAAK,SAAEC,IAAaC,EAAAA,EAAAA,MACrBC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,YACpCC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAaC,IAAkBJ,EAAAA,EAAAA,UAAS,CAC7CK,UAAW,YACXC,SAAU,YACVC,MAAO,4BACPC,MAAO,mBACPC,eAAgB,6BAChBC,QAAS,cACTC,WAAY,2BACZC,WAAY,UACZC,IAAK,kFACLC,OAAQ,QAGHC,EAAaC,IAAkBhB,EAAAA,EAAAA,UAAS,CAC7CiB,oBAAoB,EACpBC,kBAAkB,EAClBC,mBAAmB,EACnBC,sBAAsB,EACtBC,qBAAqB,EACrBC,iBAAiB,EACjBC,UAAU,EACVC,eAAgB,GAChBC,YAAa,YACbC,WAAY,aACZC,WAAY,MACZC,SAAU,iBAGLC,EAAkBC,IAAuB9B,EAAAA,EAAAA,UAAS,CACvD+B,kBAAkB,EAClBC,aAAa,EACbC,gBAAgB,EAChBC,mBAAoB,KAGhBC,EAAO,CACX,CAAEC,GAAI,UAAWC,MAAOjD,EAAE,UAAW,WAAYkD,KAAM,eACvD,CAAEF,GAAI,cAAeC,MAAOjD,EAAE,cAAe,eAAgBkD,KAAM,cACnE,CAAEF,GAAI,WAAYC,MAAOjD,EAAE,WAAY,YAAakD,KAAM,qBAC1D,CAAEF,GAAI,WAAYC,MAAOjD,EAAE,WAAY,YAAakD,KAAM,mBAGtDC,EAAsBA,CAACC,EAAOC,KAClCrC,EAAesC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAI,IACP,CAACF,GAAQC,MAIPG,EAAyBA,CAACJ,EAAOC,KACrCzB,EAAe0B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAI,IACP,CAACF,GAAQC,MA+Bb,OACEI,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAC,OAAS1D,EAAQ,cAAgB,gBAAiB2D,SAAA,EAE9DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,yCAAwCE,UACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7D5D,EAAE,cAAe,mBAEpB6D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5C5D,EAAE,cAAe,wDAKxByD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EAEpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,UAC5BC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0FAAyFE,UACtGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,SAC3Bb,EAAKe,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMrD,EAAaoD,EAAIf,IAChCU,UAAS,uFAAAC,OACPjD,IAAcqD,EAAIf,GACd,mEACA,6EACHY,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAWK,EAAIb,QAClBW,EAAAA,EAAAA,KAAA,QAAAD,SAAOG,EAAId,UATNc,EAAIf,YAiBnBa,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gBAAeE,UAC5BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFE,SAAA,CAGvF,YAAdlD,IACC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChE5D,EAAE,qBAAsB,0BAE3B6D,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMnD,OAnE/BC,GAAa,GAmEoDA,GAAa,GAC9D4C,UAAU,kFAAiFE,SAE1F/C,EAAYb,EAAE,OAAQ,QAAUA,EAAE,OAAQ,cAK/CyD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uGAAsGE,SAClH7C,EAAYW,QACXmC,EAAAA,EAAAA,KAAA,OAAKI,IAAKlD,EAAYW,OAAQwC,IAAI,SAASR,UAAU,gCAErDG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,4DAGhB7C,IACC4C,EAAAA,EAAAA,MAAA,SAAOC,UAAU,qGAAoGE,SAAA,EACnHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2BACbG,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLC,OAAO,UACPC,SAtFIC,IAC1B,MAAMC,EAAOD,EAAME,OAAOC,MAAM,GAChC,GAAIF,EAAM,CACR,MAAMG,EAAS,IAAIC,WACnBD,EAAOE,OAAUC,IACf7D,EAAesC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAI,IACP5B,OAAQmD,EAAEL,OAAOM,WAGrBJ,EAAOK,cAAcR,EACvB,GA4EsBb,UAAU,kBAKlBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDE,SAAA,CAChE7C,EAAYE,UAAU,IAAEF,EAAYG,aAEvC2C,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAAE7C,EAAYM,kBAC7DwC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SAAE7C,EAAYQ,oBAKzEkC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,YAAa,iBAElB6D,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLd,MAAOtC,EAAYE,UACnBoD,SAAWQ,GAAM1B,EAAoB,YAAa0B,EAAEL,OAAOnB,OAC3D2B,UAAWnE,EACX6C,UAAU,uLAIdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,WAAY,gBAEjB6D,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLd,MAAOtC,EAAYG,SACnBmD,SAAWQ,GAAM1B,EAAoB,WAAY0B,EAAEL,OAAOnB,OAC1D2B,UAAWnE,EACX6C,UAAU,uLAIdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,QAAS,YAEd6D,EAAAA,EAAAA,KAAA,SACEM,KAAK,QACLd,MAAOtC,EAAYI,MACnBkD,SAAWQ,GAAM1B,EAAoB,QAAS0B,EAAEL,OAAOnB,OACvD2B,UAAWnE,EACX6C,UAAU,uLAIdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,QAAS,YAEd6D,EAAAA,EAAAA,KAAA,SACEM,KAAK,MACLd,MAAOtC,EAAYK,MACnBiD,SAAWQ,GAAM1B,EAAoB,QAAS0B,EAAEL,OAAOnB,OACvD2B,UAAWnE,EACX6C,UAAU,uLAIdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,iBAAkB,qBAEvB6D,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLd,MAAOtC,EAAYM,eACnBgD,SAAWQ,GAAM1B,EAAoB,iBAAkB0B,EAAEL,OAAOnB,OAChE2B,UAAWnE,EACX6C,UAAU,uLAIdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,UAAW,qBAEhB6D,EAAAA,EAAAA,KAAA,SACEM,KAAK,OACLd,MAAOtC,EAAYO,QACnB+C,SAAWQ,GAAM1B,EAAoB,UAAW0B,EAAEL,OAAOnB,OACzD2B,UAAWnE,EACX6C,UAAU,0LAKhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,MAAO,UAEZ6D,EAAAA,EAAAA,KAAA,YACEoB,KAAK,IACL5B,MAAOtC,EAAYU,IACnB4C,SAAWQ,GAAM1B,EAAoB,MAAO0B,EAAEL,OAAOnB,OACrD2B,UAAWnE,EACX6C,UAAU,yLAOH,gBAAdhD,IACC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrE5D,EAAE,kBAAmB,uBAGxByD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnE5D,EAAE,0BAA2B,+BAEhC6D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvBsB,OAAOC,QAAQxD,GAAayD,MAAM,EAAG,GAAGtB,IAAIuB,IAAA,IAAEC,EAAKjC,GAAMgC,EAAA,OACxD5B,EAAAA,EAAAA,MAAA,OAAeC,UAAU,oCAAmCE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SACvD5D,EAAEsF,EAAKA,EAAIC,OAAO,GAAGC,cAAgBF,EAAIF,MAAM,GAAGK,QAAQ,WAAY,WAEzE5B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtD5D,EAAE,GAAD2D,OAAI2B,EAAG,kBAAA3B,OAAkB2B,EAAII,cAAcD,QAAQ,WAAY,OAAM,yBAG3EhC,EAAAA,EAAAA,MAAA,SAAOC,UAAU,mDAAkDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,SACEM,KAAK,WACLwB,QAAStC,EACTgB,SAAWQ,GAAMrB,EAAuB8B,EAAKT,EAAEL,OAAOmB,SACtDjC,UAAU,kBAEZG,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qcAhBT4B,WAwBhB7B,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnE5D,EAAE,oBAAqB,yBAE1ByD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,WAAY,eAEjByD,EAAAA,EAAAA,MAAA,UACEJ,MAAOnD,EACPmE,SAAWQ,GAAM1E,EAAY0E,EAAEL,OAAOnB,OACtCK,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,KAAIO,SAAC,aACnBC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,KAAIO,SAAC,sDAIvBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,QAAS,YAEdyD,EAAAA,EAAAA,MAAA,UACEJ,MAAO9C,EACP8D,SAAWQ,GAAMrE,EAASqE,EAAEL,OAAOnB,OACnCK,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,QAAOO,SAAE5D,EAAE,QAAS,YAClC6D,EAAAA,EAAAA,KAAA,UAAQR,MAAM,OAAMO,SAAE5D,EAAE,OAAQ,WAChC6D,EAAAA,EAAAA,KAAA,UAAQR,MAAM,SAAQO,SAAE5D,EAAE,SAAU,mBAIxCyD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,aAAc,kBAEnByD,EAAAA,EAAAA,MAAA,UACEJ,MAAO1B,EAAYW,WACnB+B,SAAWQ,GAAMrB,EAAuB,aAAcqB,EAAEL,OAAOnB,OAC/DK,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,aAAYO,SAAC,gBAC3BC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,aAAYO,SAAC,gBAC3BC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,aAAYO,SAAC,sBAI/BH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/E5D,EAAE,aAAc,kBAEnByD,EAAAA,EAAAA,MAAA,UACEJ,MAAO1B,EAAYY,WACnB8B,SAAWQ,GAAMrB,EAAuB,aAAcqB,EAAEL,OAAOnB,OAC/DK,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,MAAKO,SAAC,aACpBC,EAAAA,EAAAA,KAAA,UAAQR,MAAM,MAAKO,SAAC,8BAUnB,aAAdlD,IACC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrE5D,EAAE,mBAAoB,wBAGzByD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+FAA8FE,SAAA,EAC3GH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CE,SACtD5D,EAAE,gBAAiB,gCAEtB6D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SACpD5D,EAAE,gBAAiB,wDAGxB6D,EAAAA,EAAAA,KAAA,UACEG,QAASA,KAAM4B,OA/ULxC,EA+U0B,mBA/UnBC,GA+UwCZ,EAAiBE,sBA9U5FD,EAAoBY,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACnBD,GAAI,IACP,CAACF,GAAQC,KAHgBuC,IAACxC,EAAOC,GAgVjBK,UAAS,0CAAAC,OACPlB,EAAiBE,iBACb,yCACA,8CACHiB,SAEFnB,EAAiBE,iBAAmB3C,EAAE,UAAW,WAAaA,EAAE,SAAU,gBAI/EyD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,4CAA2CE,SACtD5D,EAAE,mBAAoB,0BAEzByD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wIAAuIE,UACvJH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAE5D,EAAE,iBAAkB,sBAChF6D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SAAE5D,EAAE,cAAe,kCAE9E6D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6CAIjBG,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wIAAuIE,UACvJH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAE5D,EAAE,eAAgB,oBAC9E6D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SAAE5D,EAAE,mBAAoB,oCAEnF6D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6CAIjBG,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wIAAuIE,UACvJH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAE5D,EAAE,iBAAkB,sBAChF6D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SAAE5D,EAAE,uBAAwB,qCAEvF6D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wDAUZ,aAAdhD,IACC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKE,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrE5D,EAAE,cAAe,mBAGpB6D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,SACvB,CACC,CAAEiC,OAAQ,QAASC,KAAM,cAAeC,QAAS,mCAAoC7C,KAAM,qBAAsB8C,MAAO,kBACxH,CAAEH,OAAQ,kBAAmBC,KAAM,YAAaC,QAAS,8BAA+B7C,KAAM,mBAAoB8C,MAAO,iBACzH,CAAEH,OAAQ,mBAAoBC,KAAM,aAAcC,QAAS,oCAAqC7C,KAAM,aAAc8C,MAAO,mBAC3H,CAAEH,OAAQ,cAAeC,KAAM,aAAcC,QAAS,+BAAgC7C,KAAM,kBAAmB8C,MAAO,mBACtH,CAAEH,OAAQ,oBAAqBC,KAAM,cAAeC,QAAS,mCAAoC7C,KAAM,aAAc8C,MAAO,kBAC5HlC,IAAI,CAACmC,EAAUC,KACfzC,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,+EAA8EE,SAAA,EACvGC,EAAAA,EAAAA,KAAA,OAAKH,UAAS,qDAAuDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKsC,EAAS/C,KAAI,KAAAS,OAAIsC,EAASD,YAE7CvC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQE,SAAA,EACrBC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CAA2CE,SAAEqC,EAASJ,UACrEhC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SAAEqC,EAASF,WACpElC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gDAA+CE,SAAEqC,EAASH,YAPnEI,oB", "sources": ["pages/Profile/Profile.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst Profile = () => {\n  const { t, isRTL, language, setLanguage } = useLanguage();\n  const { user } = useAuth();\n  const { theme, setTheme } = useTheme();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [isEditing, setIsEditing] = useState(false);\n  const [profileData, setProfileData] = useState({\n    firstName: 'Dr. <PERSON>',\n    lastName: 'Al-<PERSON>',\n    email: '<EMAIL>',\n    phone: '+966 50 123 4567',\n    specialization: 'Pediatric Physical Therapy',\n    license: 'PT-2024-001',\n    department: 'Special Needs Department',\n    experience: '8 years',\n    bio: 'Specialized in pediatric physical therapy with focus on special needs children.',\n    avatar: null\n  });\n\n  const [preferences, setPreferences] = useState({\n    emailNotifications: true,\n    smsNotifications: false,\n    pushNotifications: true,\n    appointmentReminders: true,\n    reportNotifications: false,\n    marketingEmails: false,\n    autoSave: true,\n    sessionTimeout: 30,\n    defaultView: 'dashboard',\n    dateFormat: 'DD/MM/YYYY',\n    timeFormat: '24h',\n    timezone: 'Asia/Riyadh'\n  });\n\n  const [securitySettings, setSecuritySettings] = useState({\n    twoFactorEnabled: false,\n    loginAlerts: true,\n    sessionHistory: true,\n    dataExportRequests: []\n  });\n\n  const tabs = [\n    { id: 'profile', label: t('profile', 'Profile'), icon: 'fas fa-user' },\n    { id: 'preferences', label: t('preferences', 'Preferences'), icon: 'fas fa-cog' },\n    { id: 'security', label: t('security', 'Security'), icon: 'fas fa-shield-alt' },\n    { id: 'activity', label: t('activity', 'Activity'), icon: 'fas fa-history' }\n  ];\n\n  const handleProfileUpdate = (field, value) => {\n    setProfileData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handlePreferenceUpdate = (field, value) => {\n    setPreferences(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSecurityUpdate = (field, value) => {\n    setSecuritySettings(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSaveProfile = () => {\n    // In a real app, this would make an API call\n    setIsEditing(false);\n    // Show success message\n  };\n\n  const handleAvatarUpload = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setProfileData(prev => ({\n          ...prev,\n          avatar: e.target.result\n        }));\n      };\n      reader.readAsDataURL(file);\n    }\n  };\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('userProfile', 'User Profile')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('profileDesc', 'Manage your account settings and preferences')}\n          </p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Sidebar Navigation */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <nav className=\"p-4 space-y-2\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${\n                    activeTab === tab.id\n                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <i className={tab.icon}></i>\n                  <span>{tab.label}</span>\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Content Area */}\n        <div className=\"lg:col-span-3\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            \n            {/* Profile Tab */}\n            {activeTab === 'profile' && (\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {t('profileInformation', 'Profile Information')}\n                  </h2>\n                  <button\n                    onClick={() => isEditing ? handleSaveProfile() : setIsEditing(true)}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    {isEditing ? t('save', 'Save') : t('edit', 'Edit')}\n                  </button>\n                </div>\n\n                {/* Avatar Section */}\n                <div className=\"flex items-center mb-6\">\n                  <div className=\"relative\">\n                    <div className=\"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center overflow-hidden\">\n                      {profileData.avatar ? (\n                        <img src={profileData.avatar} alt=\"Avatar\" className=\"w-full h-full object-cover\" />\n                      ) : (\n                        <i className=\"fas fa-user text-3xl text-gray-500 dark:text-gray-400\"></i>\n                      )}\n                    </div>\n                    {isEditing && (\n                      <label className=\"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700\">\n                        <i className=\"fas fa-camera text-sm\"></i>\n                        <input\n                          type=\"file\"\n                          accept=\"image/*\"\n                          onChange={handleAvatarUpload}\n                          className=\"hidden\"\n                        />\n                      </label>\n                    )}\n                  </div>\n                  <div className=\"ml-6\">\n                    <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                      {profileData.firstName} {profileData.lastName}\n                    </h3>\n                    <p className=\"text-gray-600 dark:text-gray-400\">{profileData.specialization}</p>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-500\">{profileData.department}</p>\n                  </div>\n                </div>\n\n                {/* Profile Fields */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('firstName', 'First Name')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={profileData.firstName}\n                      onChange={(e) => handleProfileUpdate('firstName', e.target.value)}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('lastName', 'Last Name')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={profileData.lastName}\n                      onChange={(e) => handleProfileUpdate('lastName', e.target.value)}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('email', 'Email')}\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={profileData.email}\n                      onChange={(e) => handleProfileUpdate('email', e.target.value)}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('phone', 'Phone')}\n                    </label>\n                    <input\n                      type=\"tel\"\n                      value={profileData.phone}\n                      onChange={(e) => handleProfileUpdate('phone', e.target.value)}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('specialization', 'Specialization')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={profileData.specialization}\n                      onChange={(e) => handleProfileUpdate('specialization', e.target.value)}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                      {t('license', 'License Number')}\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={profileData.license}\n                      onChange={(e) => handleProfileUpdate('license', e.target.value)}\n                      disabled={!isEditing}\n                      className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mt-6\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('bio', 'Bio')}\n                  </label>\n                  <textarea\n                    rows=\"4\"\n                    value={profileData.bio}\n                    onChange={(e) => handleProfileUpdate('bio', e.target.value)}\n                    disabled={!isEditing}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800\"\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Preferences Tab */}\n            {activeTab === 'preferences' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('userPreferences', 'User Preferences')}\n                </h2>\n\n                <div className=\"space-y-8\">\n                  {/* Notification Preferences */}\n                  <div>\n                    <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                      {t('notificationPreferences', 'Notification Preferences')}\n                    </h3>\n                    <div className=\"space-y-4\">\n                      {Object.entries(preferences).slice(0, 6).map(([key, value]) => (\n                        <div key={key} className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"font-medium text-gray-900 dark:text-white\">\n                              {t(key, key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'))}\n                            </div>\n                            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                              {t(`${key}Desc`, `Enable ${key.toLowerCase().replace(/([A-Z])/g, ' $1')} notifications`)}\n                            </div>\n                          </div>\n                          <label className=\"relative inline-flex items-center cursor-pointer\">\n                            <input\n                              type=\"checkbox\"\n                              checked={value}\n                              onChange={(e) => handlePreferenceUpdate(key, e.target.checked)}\n                              className=\"sr-only peer\"\n                            />\n                            <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                          </label>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* System Preferences */}\n                  <div>\n                    <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n                      {t('systemPreferences', 'System Preferences')}\n                    </h3>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          {t('language', 'Language')}\n                        </label>\n                        <select\n                          value={language}\n                          onChange={(e) => setLanguage(e.target.value)}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        >\n                          <option value=\"en\">English</option>\n                          <option value=\"ar\">العربية</option>\n                        </select>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          {t('theme', 'Theme')}\n                        </label>\n                        <select\n                          value={theme}\n                          onChange={(e) => setTheme(e.target.value)}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        >\n                          <option value=\"light\">{t('light', 'Light')}</option>\n                          <option value=\"dark\">{t('dark', 'Dark')}</option>\n                          <option value=\"system\">{t('system', 'System')}</option>\n                        </select>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          {t('dateFormat', 'Date Format')}\n                        </label>\n                        <select\n                          value={preferences.dateFormat}\n                          onChange={(e) => handlePreferenceUpdate('dateFormat', e.target.value)}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        >\n                          <option value=\"DD/MM/YYYY\">DD/MM/YYYY</option>\n                          <option value=\"MM/DD/YYYY\">MM/DD/YYYY</option>\n                          <option value=\"YYYY-MM-DD\">YYYY-MM-DD</option>\n                        </select>\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                          {t('timeFormat', 'Time Format')}\n                        </label>\n                        <select\n                          value={preferences.timeFormat}\n                          onChange={(e) => handlePreferenceUpdate('timeFormat', e.target.value)}\n                          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                        >\n                          <option value=\"12h\">12 Hour</option>\n                          <option value=\"24h\">24 Hour</option>\n                        </select>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Security Tab */}\n            {activeTab === 'security' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('securitySettings', 'Security Settings')}\n                </h2>\n\n                <div className=\"space-y-6\">\n                  <div className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                    <div>\n                      <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                        {t('twoFactorAuth', 'Two-Factor Authentication')}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {t('twoFactorDesc', 'Add an extra layer of security to your account')}\n                      </p>\n                    </div>\n                    <button\n                      onClick={() => handleSecurityUpdate('twoFactorEnabled', !securitySettings.twoFactorEnabled)}\n                      className={`px-4 py-2 rounded-lg transition-colors ${\n                        securitySettings.twoFactorEnabled\n                          ? 'bg-red-600 text-white hover:bg-red-700'\n                          : 'bg-green-600 text-white hover:bg-green-700'\n                      }`}\n                    >\n                      {securitySettings.twoFactorEnabled ? t('disable', 'Disable') : t('enable', 'Enable')}\n                    </button>\n                  </div>\n\n                  <div className=\"space-y-4\">\n                    <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                      {t('passwordSecurity', 'Password & Security')}\n                    </h3>\n                    <div className=\"space-y-3\">\n                      <button className=\"w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"font-medium text-gray-900 dark:text-white\">{t('changePassword', 'Change Password')}</div>\n                            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('lastChanged', 'Last changed 30 days ago')}</div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </div>\n                      </button>\n\n                      <button className=\"w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"font-medium text-gray-900 dark:text-white\">{t('loginHistory', 'Login History')}</div>\n                            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('viewRecentLogins', 'View recent login activity')}</div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </div>\n                      </button>\n\n                      <button className=\"w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <div className=\"font-medium text-gray-900 dark:text-white\">{t('activeSessions', 'Active Sessions')}</div>\n                            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('manageActiveSessions', 'Manage your active sessions')}</div>\n                          </div>\n                          <i className=\"fas fa-chevron-right text-gray-400\"></i>\n                        </div>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Activity Tab */}\n            {activeTab === 'activity' && (\n              <div className=\"p-6\">\n                <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n                  {t('activityLog', 'Activity Log')}\n                </h2>\n\n                <div className=\"space-y-4\">\n                  {[\n                    { action: 'Login', time: '2 hours ago', details: 'Logged in from Chrome on Windows', icon: 'fas fa-sign-in-alt', color: 'text-green-600' },\n                    { action: 'Profile Updated', time: '1 day ago', details: 'Updated contact information', icon: 'fas fa-user-edit', color: 'text-blue-600' },\n                    { action: 'Password Changed', time: '3 days ago', details: 'Password was successfully changed', icon: 'fas fa-key', color: 'text-yellow-600' },\n                    { action: 'Data Export', time: '1 week ago', details: 'Exported patient data report', icon: 'fas fa-download', color: 'text-purple-600' },\n                    { action: 'Settings Modified', time: '2 weeks ago', details: 'Updated notification preferences', icon: 'fas fa-cog', color: 'text-gray-600' }\n                  ].map((activity, index) => (\n                    <div key={index} className=\"flex items-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                      <div className={`p-3 rounded-full bg-gray-100 dark:bg-gray-700 mr-4`}>\n                        <i className={`${activity.icon} ${activity.color}`}></i>\n                      </div>\n                      <div className=\"flex-1\">\n                        <div className=\"font-medium text-gray-900 dark:text-white\">{activity.action}</div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">{activity.details}</div>\n                        <div className=\"text-xs text-gray-500 dark:text-gray-500 mt-1\">{activity.time}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "names": ["Profile", "t", "isRTL", "language", "setLanguage", "useLanguage", "user", "useAuth", "theme", "setTheme", "useTheme", "activeTab", "setActiveTab", "useState", "isEditing", "setIsEditing", "profileData", "setProfileData", "firstName", "lastName", "email", "phone", "specialization", "license", "department", "experience", "bio", "avatar", "preferences", "setPreferences", "emailNotifications", "smsNotifications", "pushNotifications", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reportNotifications", "marketingEmails", "autoSave", "sessionTimeout", "defaultView", "dateFormat", "timeFormat", "timezone", "securitySettings", "setSecuritySettings", "twoFactorEnabled", "loginAlerts", "sessionHistory", "dataExportRequests", "tabs", "id", "label", "icon", "handleProfileUpdate", "field", "value", "prev", "_objectSpread", "handlePreferenceUpdate", "_jsxs", "className", "concat", "children", "_jsx", "map", "tab", "onClick", "src", "alt", "type", "accept", "onChange", "event", "file", "target", "files", "reader", "FileReader", "onload", "e", "result", "readAsDataURL", "disabled", "rows", "Object", "entries", "slice", "_ref", "key", "char<PERSON>t", "toUpperCase", "replace", "toLowerCase", "checked", "handleSecurityUpdate", "action", "time", "details", "color", "activity", "index"], "sourceRoot": ""}