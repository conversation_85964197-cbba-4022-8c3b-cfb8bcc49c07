{"version": 3, "file": "static/js/3403.b52b547d.chunk.js", "mappings": "4IAEO,MAsOMA,EAAyB,CACpCC,gBAAiB,CACfC,MAAO,IACPC,MAAO,kBACPC,MAAO,QACPC,YAAa,4BAEfC,uBAAwB,CACtBJ,MAAO,GACPC,MAAO,yBACPC,MAAO,SACPC,YAAa,+BAEfE,mBAAoB,CAClBL,MAAO,GACPC,MAAO,qBACPC,MAAO,SACPC,YAAa,mCAEfG,eAAgB,CACdN,MAAO,EACPC,MAAO,iBACPC,MAAO,MACPC,YAAa,iC,0FC3PjB,MAyYA,EAzY4BI,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAChC,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,oBAC9CC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,CAAC,IACzCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAGvCK,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAW,CACfC,aAAc,CACZC,oBAAqB,GACrBC,eAAgB,IAChBC,yBAA0B,GAC1BC,2BAA4B,GAC5BC,mBAAoB,GACpBC,MAAO,MACPC,OAAQ,GACRC,UAAW,IACXC,aAAc,IAEhBC,SAAU,CACRC,gBAAiB,GACjBC,sBAAuB,GACvBC,qBAAsB,GACtBC,gBAAiB,EACjBC,oBAAqB,GACrBT,MAAO,MACPU,cAAe,KAEjBC,WAAY,CACVC,qBAAsB,GACtBC,wBAAyB,GACzBC,kBAAmB,GACnBC,4BAA6B,GAC7BC,wBAAyB,GACzBhB,MAAO,OAETiB,mBAAoB,CAClBC,IAAK,CACHC,iBAAkB,GAClBC,iBAAkB,IAClBC,YAAa,GACbC,gBAAiB,IAEnBC,cAAe,CACbC,eAAgB,IAChBC,uBAAwB,IACxBC,oBAAqB,EACrBC,qBAAsB,IACtBC,aAAc,KAEhBC,uBAAwB,CACtBC,WAAY,GACZC,iBAAkB,GAClBC,kBAAmB,GACnBC,WAAY,GACZC,qBAAsB,KAG1BC,eAAgB,CACdC,cAAe,EACfC,iBAAkB,EAClBC,kBAAmB,EACnBC,oBAAqB,EACrBC,eAAgB,EAChBC,aAAc,IACdzC,MAAO,SAIX0C,WAAW,KACTrD,EAAeI,GACfF,GAAW,IACV,MACF,CAACN,IAEJ,MAAM0D,EAAaC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,OAAE7C,EAAM,KAAE8C,EAAI,MAAE/C,EAAK,OAAEgD,EAAM,KAAEC,EAAI,MAAEpG,EAAK,YAAEC,GAAa8F,EAAA,OACzFM,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,UAAAG,OAAYzG,EAAK,iBAAAyG,OAAgBzG,EAAK,sBAAqBuG,UACvEC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAG,OAAKL,EAAI,UAAAK,OAASzG,EAAK,mBAAAyG,OAAkBzG,EAAK,qBAE5DqG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAAEP,KACrEQ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEtG,WAG7DoG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mDAAkDC,SAAA,CAC/DN,EAAOC,KAET/C,IACCqD,EAAAA,EAAAA,KAAA,QAAMF,UAAS,uBAAAG,OACbtD,EAAMuD,WAAW,KAAO,iBAAmB,gBAC1CH,SACApD,OAINC,IACCiD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDtE,EAAE,SAAU,UAAU,KAAGmB,EAAQ8C,WAMzC9C,IACCoD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,MAAAG,OAAQzG,EAAK,qDACtB2G,MAAO,CAAEC,MAAM,GAADH,OAAKI,KAAKC,IAAKb,EAAQ7C,EAAU,IAAK,KAAI,WAK9DoD,EAAAA,EAAAA,KAAA,OAAKF,UAAS,4BAAAG,OACD,iBAAXN,EAA4B,iBACjB,cAAXA,EAAyB,gBACd,iBAAXA,EAA4B,kBAC5B,gBACCI,SACW,iBAAXJ,EAA4BlE,EAAE,cAAe,gBAClC,cAAXkE,EAAyBlE,EAAE,WAAY,aAC5B,iBAAXkE,EAA4BlE,EAAE,cAAe,gBAC7CA,EAAE,mBAAoB,2BAKvB8E,EAAeC,IAAA,IAAC,MAAEhB,EAAK,KAAEiB,EAAI,KAAEC,EAAO,OAAOF,EAAA,OACjDX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SAAEP,KAC1EQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBY,OAAOC,QAAQH,GAAMI,IAAIC,IAAA,IAAEC,EAAKtB,GAAMqB,EAAA,OACrCjB,EAAAA,EAAAA,MAAA,OAAeC,UAAU,oCAAmCC,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sDAAqDC,SAClEtE,EAAEsF,EAAKA,EAAIC,QAAQ,WAAY,OAAOC,WAEzCpB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,+BACVK,MAAO,CAAEC,MAAM,GAADH,OAAKI,KAAKC,IAAIb,EAAO,KAAI,WAG3CO,EAAAA,EAAAA,KAAA,QAAMF,UAAU,oEAAmEC,SAC/D,kBAAVN,EAAqBA,EAAMyB,QAAQ,GAAKzB,SAZ5CsB,WAqBlB,OAAI9E,GAEA+D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAMnBD,EAAAA,EAAAA,MAAA,OAAKC,UAAS,aAAAG,OAAevE,EAAQ,cAAgB,gBAAiBqE,SAAA,EAEpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DtE,EAAE,sBAAuB,4BAE5BuE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtE,EAAE,6BAA8B,yDAGrCoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEJ,MAAO7D,EACPuF,SAAWC,GAAMvF,EAAkBuF,EAAExE,OAAO6C,OAC5CK,UAAU,2HAA0HC,SAAA,EAEpIC,EAAAA,EAAAA,KAAA,UAAQP,MAAM,kBAAiBM,SAAEtE,EAAE,iBAAkB,sBACrDuE,EAAAA,EAAAA,KAAA,UAAQP,MAAM,eAAcM,SAAEtE,EAAE,cAAe,mBAC/CuE,EAAAA,EAAAA,KAAA,UAAQP,MAAM,eAAcM,SAAEtE,EAAE,cAAe,mBAC/CuE,EAAAA,EAAAA,KAAA,UAAQP,MAAM,YAAWM,SAAEtE,EAAE,WAAY,mBAE3CoE,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZrE,EAAE,eAAgB,2BAMzBoE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtE,EAAE,2BAA4B,iCAEjCoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,sBAAuB,wBAChCgE,MAA+B,QAA1B3F,EAAEiC,EAAYM,oBAAY,IAAAvC,OAAA,EAAxBA,EAA0BwC,oBACjCM,OAAQ,GACR8C,KAAK,IACL/C,MAA+B,QAA1B5C,EAAEgC,EAAYM,oBAAY,IAAAtC,OAAA,EAAxBA,EAA0B4C,MACjCgD,OAAO,eACPC,KAAK,eACLpG,MAAM,QACNC,YAAW,GAAAwG,OAA6B,QAA7BjG,EAAK+B,EAAYM,oBAAY,IAAArC,OAAA,EAAxBA,EAA0B6C,UAAS,KAAAoD,OAAIxE,EAAE,YAAa,iBAExEuE,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,iBAAkB,mBAC3BgE,MAA+B,QAA1BxF,EAAE8B,EAAYM,oBAAY,IAAApC,OAAA,EAAxBA,EAA0BsC,eACjCK,OAAQ,EACR8C,KAAK,KACL/C,MAAM,OACNgD,OAAO,eACPC,KAAK,cACLpG,MAAM,OACNC,YAAagC,EAAE,gBAAiB,qBAElCuE,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,2BAA4B,6BACrCgE,MAA+B,QAA1BvF,EAAE6B,EAAYM,oBAAY,IAAAnC,OAAA,EAAxBA,EAA0BsC,yBACjCI,OAAQ,GACR8C,KAAK,IACL/C,MAAM,MACNgD,OAAO,eACPC,KAAK,mBACLpG,MAAM,SACNC,YAAagC,EAAE,iBAAkB,4BAMvCoE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtE,EAAE,oBAAqB,yBAE1BoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,kBAAmB,oBAC5BgE,MAA2B,QAAtBtF,EAAE4B,EAAYgB,gBAAQ,IAAA5C,OAAA,EAApBA,EAAsB6C,gBAC7BJ,OAAQ,GACR8C,KAAK,IACL/C,MAA2B,QAAtBvC,EAAE2B,EAAYgB,gBAAQ,IAAA3C,OAAA,EAApBA,EAAsBuC,MAC7BgD,OAAO,eACPC,KAAK,kBACLpG,MAAM,QACNC,YAAagC,EAAE,gBAAiB,qBAElCuE,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,wBAAyB,0BAClCgE,MAA2B,QAAtBpF,EAAE0B,EAAYgB,gBAAQ,IAAA1C,OAAA,EAApBA,EAAsB4C,sBAC7BL,OAAQ,GACR8C,KAAK,IACL/C,MAAM,MACNgD,OAAO,eACPC,KAAK,oBACLpG,MAAM,OACNC,YAAagC,EAAE,mBAAoB,wBAErCuE,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,uBAAwB,0BACjCgE,MAA2B,QAAtBnF,EAAEyB,EAAYgB,gBAAQ,IAAAzC,OAAA,EAApBA,EAAsB4C,qBAC7BN,OAAQ,GACR8C,KAAK,IACL/C,MAAM,MACNgD,OAAO,eACPC,KAAK,cACLpG,MAAM,SACNC,YAAagC,EAAE,uBAAwB,kCAM7CoE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtE,EAAE,oBAAqB,yBAE1BoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,uBAAwB,yBACjCgE,MAA6B,QAAxBlF,EAAEwB,EAAYuB,kBAAU,IAAA/C,OAAA,EAAtBA,EAAwBgD,qBAC/BX,OAAQ,GACR8C,KAAK,IACL/C,MAAM,MACNgD,OAAO,eACPC,KAAK,yBACLpG,MAAM,QACNC,YAAagC,EAAE,gBAAiB,sBAElCuE,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,0BAA2B,6BACpCgE,MAA6B,QAAxBjF,EAAEuB,EAAYuB,kBAAU,IAAA9C,OAAA,EAAtBA,EAAwBgD,wBAC/BZ,OAAQ,GACR8C,KAAK,IACL/C,MAAM,MACNgD,OAAO,eACPC,KAAK,wBACLpG,MAAM,SACNC,YAAagC,EAAE,eAAgB,qBAEjCuE,EAAAA,EAAAA,KAACV,EAAU,CACTE,MAAO/D,EAAE,0BAA2B,4BACpCgE,MAA6B,QAAxBhF,EAAEsB,EAAYuB,kBAAU,IAAA7C,OAAA,EAAtBA,EAAwBkD,wBAC/Bf,OAAQ,GACR8C,KAAK,IACL/C,MAAM,MACNgD,OAAO,eACPC,KAAK,kBACLpG,MAAM,OACNC,YAAagC,EAAE,wBAAyB,mCAM9CoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAACO,EAAY,CACXf,MAAO/D,EAAE,gCAAiC,yCAC1CgF,KAAM,CACJY,UAAyC,QAAhC3G,EAAEqB,EAAY6B,0BAAkB,IAAAlD,GAAK,QAALC,EAA9BD,EAAgCmD,WAAG,IAAAlD,OAAL,EAA9BA,EAAqCmD,iBAChDwD,UAAyC,QAAhC1G,EAAEmB,EAAY6B,0BAAkB,IAAAhD,GAAK,QAALC,EAA9BD,EAAgCiD,WAAG,IAAAhD,OAAL,EAA9BA,EAAqCkD,iBAChDC,YAA2C,QAAhClD,EAAEiB,EAAY6B,0BAAkB,IAAA9C,GAAK,QAALC,EAA9BD,EAAgC+C,WAAG,IAAA9C,OAAL,EAA9BA,EAAqCiD,gBAGtDgC,EAAAA,EAAAA,KAACO,EAAY,CACXf,MAAO/D,EAAE,gBAAiB,4BAC1BgF,KAAoC,QAAhCzF,EAAEe,EAAY6B,0BAAkB,IAAA5C,OAAA,EAA9BA,EAAgCkD,iBAExC8B,EAAAA,EAAAA,KAACO,EAAY,CACXf,MAAO/D,EAAE,yBAA0B,2BACnCgF,KAAoC,QAAhCxF,EAAEc,EAAY6B,0BAAkB,IAAA3C,OAAA,EAA9BA,EAAgCuD,6BAK1CqB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtE,EAAE,iBAAkB,iCAEvBoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SACrC,QADqC7E,EAC/Da,EAAY+C,sBAAc,IAAA5D,OAAA,EAA1BA,EAA4B6D,iBAE/BiB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtE,EAAE,gBAAiB,wBAGxBoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,SAC3C,QAD2C5E,EACrEY,EAAY+C,sBAAc,IAAA3D,OAAA,EAA1BA,EAA4B6D,oBAE/BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtE,EAAE,mBAAoB,2BAG3BoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SACvC,QADuC3E,EACjEW,EAAY+C,sBAAc,IAAA1D,OAAA,EAA1BA,EAA4B8D,uBAE/Bc,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtE,EAAE,sBAAuB,8BAG9BoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wDAAuDC,SACzC,QADyC1E,EACnEU,EAAY+C,sBAAc,IAAAzD,OAAA,EAA1BA,EAA4B+D,gBAE/BS,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDtE,EAAE,eAAgB,iBAAiB,gCAI1CuE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,uBAAAG,OACa,QAA1B3E,EAAAS,EAAY+C,sBAAc,IAAAxD,GAAO,QAAPC,EAA1BD,EAA4BqB,aAAK,IAAApB,GAAjCA,EAAmC2E,WAAW,KAAO,iBAAmB,gBACvEH,SAAA,CAC0B,QAD1BvE,EACAO,EAAY+C,sBAAc,IAAAtD,OAAA,EAA1BA,EAA4BmB,MAAM,IAAElB,EAAE,iBAAkB,gCC9XrE,EAJ4B5B,KACnBmG,EAAAA,EAAAA,KAACuB,EAA4B,G", "sources": ["utils/carfStandards.js", "components/CARF/CARFQualityMeasures.jsx", "pages/Compliance/CARFQualityMeasures.jsx"], "sourcesContent": ["// CARF (Commission on Accreditation of Rehabilitation Facilities) Standards Configuration\n\nexport const CARF_STANDARDS = {\n  // CARF Documentation Requirements\n  DOCUMENTATION: {\n    ASSESSMENT: {\n      id: 'DOC_ASSESS',\n      title: 'Initial Assessment Requirements',\n      description: 'Comprehensive assessment within required timeframes',\n      requirements: [\n        'Initial assessment completed within 72 hours of admission',\n        'Comprehensive medical history documented',\n        'Functional assessment using standardized tools',\n        'Psychosocial assessment completed',\n        'Environmental assessment conducted',\n        'Risk assessment documented',\n        'Cultural and linguistic needs identified',\n        'Person-served preferences documented'\n      ],\n      timeframe: '72 hours',\n      responsible: 'Clinical Team',\n      category: 'Assessment'\n    },\n    TREATMENT_PLAN: {\n      id: 'DOC_PLAN',\n      title: 'Treatment Plan Documentation',\n      description: 'Individualized treatment plan requirements',\n      requirements: [\n        'Treatment plan developed within 30 days',\n        'Measurable goals and objectives defined',\n        'Person-served involvement in planning',\n        'Interdisciplinary team input documented',\n        'Frequency and duration of services specified',\n        'Discharge criteria established',\n        'Plan reviewed and updated regularly',\n        'Person-served signature obtained'\n      ],\n      timeframe: '30 days',\n      responsible: 'Treatment Team',\n      category: 'Planning'\n    },\n    PROGRESS_NOTES: {\n      id: 'DOC_PROGRESS',\n      title: 'Progress Documentation',\n      description: 'Regular progress monitoring and documentation',\n      requirements: [\n        'Progress notes completed after each session',\n        'Objective measurements recorded',\n        'Goal progress documented',\n        'Barriers to progress identified',\n        'Plan modifications noted',\n        'Person-served response documented',\n        'Safety incidents reported',\n        'Interdisciplinary communication noted'\n      ],\n      timeframe: 'Each session',\n      responsible: 'Service Provider',\n      category: 'Progress'\n    }\n  },\n\n  // CARF Quality Indicators\n  QUALITY_INDICATORS: {\n    SATISFACTION: {\n      id: 'QI_SAT',\n      title: 'Person-Served Satisfaction',\n      description: 'Satisfaction measurement and improvement',\n      metrics: [\n        'Overall satisfaction score ≥ 85%',\n        'Service quality rating ≥ 4.0/5.0',\n        'Recommendation likelihood ≥ 80%',\n        'Communication effectiveness ≥ 85%',\n        'Cultural competency rating ≥ 85%'\n      ],\n      frequency: 'Quarterly',\n      target: '≥ 85%',\n      category: 'Satisfaction'\n    },\n    OUTCOMES: {\n      id: 'QI_OUT',\n      title: 'Treatment Outcomes',\n      description: 'Functional improvement and goal achievement',\n      metrics: [\n        'Goal achievement rate ≥ 80%',\n        'Functional improvement ≥ 70%',\n        'Discharge to community ≥ 85%',\n        'Readmission rate ≤ 10%',\n        'Length of stay within targets'\n      ],\n      frequency: 'Monthly',\n      target: 'Varies by metric',\n      category: 'Outcomes'\n    },\n    EFFICIENCY: {\n      id: 'QI_EFF',\n      title: 'Service Efficiency',\n      description: 'Timely and efficient service delivery',\n      metrics: [\n        'Assessment completion within 72 hours',\n        'Treatment plan within 30 days',\n        'Service initiation within 14 days',\n        'Discharge planning within timeframes',\n        'Documentation completion rates ≥ 95%'\n      ],\n      frequency: 'Monthly',\n      target: '≥ 95%',\n      category: 'Efficiency'\n    }\n  },\n\n  // CARF Outcome Measures\n  OUTCOME_MEASURES: {\n    FUNCTIONAL: {\n      id: 'OM_FUNC',\n      title: 'Functional Independence Measure (FIM)',\n      description: 'Standardized functional assessment tool',\n      domains: [\n        'Self-Care',\n        'Sphincter Control',\n        'Transfers',\n        'Locomotion',\n        'Communication',\n        'Social Cognition'\n      ],\n      scale: '1-7 (1=Total Assist, 7=Complete Independence)',\n      frequency: 'Admission, Discharge, Follow-up',\n      category: 'Functional'\n    },\n    QUALITY_OF_LIFE: {\n      id: 'OM_QOL',\n      title: 'Quality of Life Measures',\n      description: 'Person-centered quality of life assessment',\n      domains: [\n        'Physical Health',\n        'Psychological Well-being',\n        'Social Relationships',\n        'Environmental Factors',\n        'Personal Satisfaction',\n        'Community Integration'\n      ],\n      scale: 'Standardized QOL instruments',\n      frequency: 'Quarterly',\n      category: 'Quality of Life'\n    },\n    PARTICIPATION: {\n      id: 'OM_PART',\n      title: 'Community Participation',\n      description: 'Community integration and participation measures',\n      domains: [\n        'Employment/Education',\n        'Social Activities',\n        'Community Mobility',\n        'Independent Living',\n        'Recreation/Leisure',\n        'Civic Participation'\n      ],\n      scale: 'Participation frequency and satisfaction',\n      frequency: 'Discharge, 3-month, 6-month follow-up',\n      category: 'Participation'\n    }\n  },\n\n  // CARF Risk Management\n  RISK_MANAGEMENT: {\n    SAFETY: {\n      id: 'RM_SAFETY',\n      title: 'Safety Risk Assessment',\n      description: 'Comprehensive safety risk evaluation',\n      areas: [\n        'Fall risk assessment',\n        'Medication safety',\n        'Equipment safety',\n        'Environmental hazards',\n        'Behavioral risks',\n        'Medical complications'\n      ],\n      frequency: 'Admission and ongoing',\n      category: 'Safety'\n    },\n    INCIDENTS: {\n      id: 'RM_INC',\n      title: 'Incident Reporting',\n      description: 'Systematic incident tracking and analysis',\n      types: [\n        'Falls',\n        'Medication errors',\n        'Equipment failures',\n        'Behavioral incidents',\n        'Medical emergencies',\n        'Rights violations'\n      ],\n      timeframe: 'Immediate reporting',\n      category: 'Incidents'\n    }\n  },\n\n  // CARF Performance Improvement\n  PERFORMANCE_IMPROVEMENT: {\n    DATA_COLLECTION: {\n      id: 'PI_DATA',\n      title: 'Data Collection and Analysis',\n      description: 'Systematic data collection for improvement',\n      requirements: [\n        'Regular data collection protocols',\n        'Statistical analysis methods',\n        'Trend identification',\n        'Benchmark comparisons',\n        'Root cause analysis',\n        'Action plan development'\n      ],\n      frequency: 'Ongoing',\n      category: 'Data'\n    },\n    CORRECTIVE_ACTION: {\n      id: 'PI_ACTION',\n      title: 'Corrective Action Plans',\n      description: 'Systematic approach to addressing deficiencies',\n      components: [\n        'Problem identification',\n        'Root cause analysis',\n        'Action plan development',\n        'Implementation timeline',\n        'Responsibility assignment',\n        'Monitoring and evaluation'\n      ],\n      timeframe: 'As needed',\n      category: 'Improvement'\n    }\n  }\n};\n\n// CARF Compliance Tracking\nexport const CARF_COMPLIANCE_LEVELS = {\n  FULL_COMPLIANCE: {\n    score: 100,\n    label: 'Full Compliance',\n    color: 'green',\n    description: 'Meets all CARF standards'\n  },\n  SUBSTANTIAL_COMPLIANCE: {\n    score: 85,\n    label: 'Substantial Compliance',\n    color: 'yellow',\n    description: 'Minor areas for improvement'\n  },\n  PARTIAL_COMPLIANCE: {\n    score: 70,\n    label: 'Partial Compliance',\n    color: 'orange',\n    description: 'Significant improvements needed'\n  },\n  NON_COMPLIANCE: {\n    score: 0,\n    label: 'Non-Compliance',\n    color: 'red',\n    description: 'Major deficiencies identified'\n  }\n};\n\n// CARF Assessment Tools\nexport const CARF_ASSESSMENT_TOOLS = {\n  FIM: {\n    name: 'Functional Independence Measure',\n    domains: ['Motor', 'Cognitive'],\n    items: 18,\n    scale: '1-7',\n    administration: 'Trained clinician'\n  },\n  CARE: {\n    name: 'Comprehensive Assessment of Rehabilitation Environments',\n    purpose: 'Environmental assessment',\n    domains: ['Physical', 'Social', 'Cultural'],\n    frequency: 'Annual'\n  },\n  SATISFACTION_SURVEY: {\n    name: 'Person-Served Satisfaction Survey',\n    purpose: 'Satisfaction measurement',\n    frequency: 'Quarterly',\n    method: 'Survey or interview'\n  }\n};\n\n// CARF Documentation Templates\nexport const CARF_TEMPLATES = {\n  ASSESSMENT: {\n    sections: [\n      'Demographic Information',\n      'Medical History',\n      'Functional Assessment',\n      'Psychosocial Assessment',\n      'Environmental Assessment',\n      'Risk Assessment',\n      'Goals and Preferences'\n    ]\n  },\n  TREATMENT_PLAN: {\n    sections: [\n      'Assessment Summary',\n      'Long-term Goals',\n      'Short-term Objectives',\n      'Service Plan',\n      'Discharge Criteria',\n      'Person-Served Input',\n      'Team Signatures'\n    ]\n  },\n  PROGRESS_NOTE: {\n    sections: [\n      'Session Information',\n      'Objective Measurements',\n      'Goal Progress',\n      'Interventions Provided',\n      'Person Response',\n      'Plan Modifications',\n      'Next Session Plan'\n    ]\n  }\n};\n\nexport default CARF_STANDARDS;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { CARF_STANDARDS } from '../../utils/carfStandards';\n\nconst CARFQualityMeasures = () => {\n  const { t, isRTL } = useLanguage();\n  const [selectedPeriod, setSelectedPeriod] = useState('current-quarter');\n  const [qualityData, setQualityData] = useState({});\n  const [loading, setLoading] = useState(true);\n\n  // Mock quality measures data\n  useEffect(() => {\n    const mockData = {\n      satisfaction: {\n        overallSatisfaction: 88,\n        serviceQuality: 4.2,\n        recommendationLikelihood: 85,\n        communicationEffectiveness: 90,\n        culturalCompetency: 87,\n        trend: '+3%',\n        target: 85,\n        responses: 156,\n        responseRate: 78\n      },\n      outcomes: {\n        goalAchievement: 82,\n        functionalImprovement: 75,\n        dischargeToCommunity: 88,\n        readmissionRate: 8,\n        averageLengthOfStay: 45,\n        trend: '+5%',\n        totalPatients: 234\n      },\n      efficiency: {\n        assessmentCompletion: 96,\n        treatmentPlanTimeliness: 94,\n        serviceInitiation: 98,\n        dischargePlanningTimeliness: 92,\n        documentationCompletion: 97,\n        trend: '+2%'\n      },\n      functionalOutcomes: {\n        fim: {\n          admissionAverage: 78,\n          dischargeAverage: 105,\n          improvement: 27,\n          efficiencyScore: 0.6\n        },\n        qualityOfLife: {\n          physicalHealth: 7.2,\n          psychologicalWellbeing: 6.8,\n          socialRelationships: 7.0,\n          environmentalFactors: 7.5,\n          overallScore: 7.1\n        },\n        communityParticipation: {\n          employment: 65,\n          socialActivities: 78,\n          independentLiving: 72,\n          recreation: 85,\n          overallParticipation: 75\n        }\n      },\n      riskManagement: {\n        fallIncidents: 2,\n        medicationErrors: 1,\n        equipmentFailures: 0,\n        behavioralIncidents: 3,\n        totalIncidents: 6,\n        incidentRate: 2.6,\n        trend: '-15%'\n      }\n    };\n\n    setTimeout(() => {\n      setQualityData(mockData);\n      setLoading(false);\n    }, 1000);\n  }, [selectedPeriod]);\n\n  const MetricCard = ({ title, value, target, unit, trend, status, icon, color, description }) => (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/30 rounded-lg`}>\n            <i className={`${icon} text-${color}-600 dark:text-${color}-400 text-xl`}></i>\n          </div>\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{description}</p>\n          </div>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"flex items-center space-x-2\">\n            <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {value}{unit}\n            </span>\n            {trend && (\n              <span className={`text-sm font-medium ${\n                trend.startsWith('+') ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {trend}\n              </span>\n            )}\n          </div>\n          {target && (\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('target', 'Target')}: {target}{unit}\n            </div>\n          )}\n        </div>\n      </div>\n      \n      {target && (\n        <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n          <div \n            className={`bg-${color}-600 h-2 rounded-full transition-all duration-300`}\n            style={{ width: `${Math.min((value / target) * 100, 100)}%` }}\n          ></div>\n        </div>\n      )}\n      \n      <div className={`mt-2 text-xs font-medium ${\n        status === 'above-target' ? 'text-green-600' :\n        status === 'at-target' ? 'text-blue-600' :\n        status === 'below-target' ? 'text-yellow-600' :\n        'text-red-600'\n      }`}>\n        {status === 'above-target' ? t('aboveTarget', 'Above Target') :\n         status === 'at-target' ? t('atTarget', 'At Target') :\n         status === 'below-target' ? t('belowTarget', 'Below Target') :\n         t('needsImprovement', 'Needs Improvement')}\n      </div>\n    </div>\n  );\n\n  const OutcomeChart = ({ title, data, type = 'bar' }) => (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">{title}</h3>\n      <div className=\"space-y-4\">\n        {Object.entries(data).map(([key, value]) => (\n          <div key={key} className=\"flex items-center justify-between\">\n            <span className=\"text-sm text-gray-600 dark:text-gray-400 capitalize\">\n              {t(key, key.replace(/([A-Z])/g, ' $1').trim())}\n            </span>\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div \n                  className=\"bg-blue-600 h-2 rounded-full\"\n                  style={{ width: `${Math.min(value, 100)}%` }}\n                ></div>\n              </div>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white w-12 text-right\">\n                {typeof value === 'number' ? value.toFixed(1) : value}\n              </span>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('carfQualityMeasures', 'CARF Quality Measures')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('qualityMeasuresDescription', 'Monitor quality indicators and outcome measures')}\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <select\n            value={selectedPeriod}\n            onChange={(e) => setSelectedPeriod(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          >\n            <option value=\"current-quarter\">{t('currentQuarter', 'Current Quarter')}</option>\n            <option value=\"last-quarter\">{t('lastQuarter', 'Last Quarter')}</option>\n            <option value=\"current-year\">{t('currentYear', 'Current Year')}</option>\n            <option value=\"last-year\">{t('lastYear', 'Last Year')}</option>\n          </select>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportReport', 'Export Report')}\n          </button>\n        </div>\n      </div>\n\n      {/* Person-Served Satisfaction */}\n      <div>\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('personServedSatisfaction', 'Person-Served Satisfaction')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <MetricCard\n            title={t('overallSatisfaction', 'Overall Satisfaction')}\n            value={qualityData.satisfaction?.overallSatisfaction}\n            target={85}\n            unit=\"%\"\n            trend={qualityData.satisfaction?.trend}\n            status=\"above-target\"\n            icon=\"fas fa-smile\"\n            color=\"green\"\n            description={`${qualityData.satisfaction?.responses} ${t('responses', 'responses')}`}\n          />\n          <MetricCard\n            title={t('serviceQuality', 'Service Quality')}\n            value={qualityData.satisfaction?.serviceQuality}\n            target={4.0}\n            unit=\"/5\"\n            trend=\"+0.2\"\n            status=\"above-target\"\n            icon=\"fas fa-star\"\n            color=\"blue\"\n            description={t('averageRating', 'Average Rating')}\n          />\n          <MetricCard\n            title={t('recommendationLikelihood', 'Recommendation Likelihood')}\n            value={qualityData.satisfaction?.recommendationLikelihood}\n            target={80}\n            unit=\"%\"\n            trend=\"+2%\"\n            status=\"above-target\"\n            icon=\"fas fa-thumbs-up\"\n            color=\"purple\"\n            description={t('wouldRecommend', 'Would Recommend')}\n          />\n        </div>\n      </div>\n\n      {/* Treatment Outcomes */}\n      <div>\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('treatmentOutcomes', 'Treatment Outcomes')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <MetricCard\n            title={t('goalAchievement', 'Goal Achievement')}\n            value={qualityData.outcomes?.goalAchievement}\n            target={80}\n            unit=\"%\"\n            trend={qualityData.outcomes?.trend}\n            status=\"above-target\"\n            icon=\"fas fa-bullseye\"\n            color=\"green\"\n            description={t('goalsAchieved', 'Goals Achieved')}\n          />\n          <MetricCard\n            title={t('functionalImprovement', 'Functional Improvement')}\n            value={qualityData.outcomes?.functionalImprovement}\n            target={70}\n            unit=\"%\"\n            trend=\"+3%\"\n            status=\"above-target\"\n            icon=\"fas fa-chart-line\"\n            color=\"blue\"\n            description={t('patientsImproved', 'Patients Improved')}\n          />\n          <MetricCard\n            title={t('dischargeToCommunity', 'Discharge to Community')}\n            value={qualityData.outcomes?.dischargeToCommunity}\n            target={85}\n            unit=\"%\"\n            trend=\"+1%\"\n            status=\"above-target\"\n            icon=\"fas fa-home\"\n            color=\"purple\"\n            description={t('successfulDischarges', 'Successful Discharges')}\n          />\n        </div>\n      </div>\n\n      {/* Service Efficiency */}\n      <div>\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('serviceEfficiency', 'Service Efficiency')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <MetricCard\n            title={t('assessmentCompletion', 'Assessment Completion')}\n            value={qualityData.efficiency?.assessmentCompletion}\n            target={95}\n            unit=\"%\"\n            trend=\"+1%\"\n            status=\"above-target\"\n            icon=\"fas fa-clipboard-check\"\n            color=\"green\"\n            description={t('within72Hours', 'Within 72 Hours')}\n          />\n          <MetricCard\n            title={t('treatmentPlanTimeliness', 'Treatment Plan Timeliness')}\n            value={qualityData.efficiency?.treatmentPlanTimeliness}\n            target={95}\n            unit=\"%\"\n            trend=\"-1%\"\n            status=\"below-target\"\n            icon=\"fas fa-calendar-check\"\n            color=\"yellow\"\n            description={t('within30Days', 'Within 30 Days')}\n          />\n          <MetricCard\n            title={t('documentationCompletion', 'Documentation Completion')}\n            value={qualityData.efficiency?.documentationCompletion}\n            target={95}\n            unit=\"%\"\n            trend=\"+2%\"\n            status=\"above-target\"\n            icon=\"fas fa-file-alt\"\n            color=\"blue\"\n            description={t('completeDocumentation', 'Complete Documentation')}\n          />\n        </div>\n      </div>\n\n      {/* Functional Outcomes */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <OutcomeChart\n          title={t('functionalIndependenceMeasure', 'Functional Independence Measure (FIM)')}\n          data={{\n            admission: qualityData.functionalOutcomes?.fim?.admissionAverage,\n            discharge: qualityData.functionalOutcomes?.fim?.dischargeAverage,\n            improvement: qualityData.functionalOutcomes?.fim?.improvement\n          }}\n        />\n        <OutcomeChart\n          title={t('qualityOfLife', 'Quality of Life Measures')}\n          data={qualityData.functionalOutcomes?.qualityOfLife}\n        />\n        <OutcomeChart\n          title={t('communityParticipation', 'Community Participation')}\n          data={qualityData.functionalOutcomes?.communityParticipation}\n        />\n      </div>\n\n      {/* Risk Management */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('riskManagement', 'Risk Management Indicators')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-red-600 dark:text-red-400\">\n              {qualityData.riskManagement?.fallIncidents}\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('fallIncidents', 'Fall Incidents')}\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-orange-600 dark:text-orange-400\">\n              {qualityData.riskManagement?.medicationErrors}\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('medicationErrors', 'Medication Errors')}\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">\n              {qualityData.riskManagement?.behavioralIncidents}\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('behavioralIncidents', 'Behavioral Incidents')}\n            </div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n              {qualityData.riskManagement?.incidentRate}\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('incidentRate', 'Incident Rate')} (per 100 patients)\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-4 flex items-center justify-center\">\n          <span className={`text-sm font-medium ${\n            qualityData.riskManagement?.trend?.startsWith('-') ? 'text-green-600' : 'text-red-600'\n          }`}>\n            {qualityData.riskManagement?.trend} {t('fromLastPeriod', 'from last period')}\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CARFQualityMeasures;\n", "import React from 'react';\nimport CARFQualityMeasuresComponent from '../../components/CARF/CARFQualityMeasures';\n\nconst CARFQualityMeasures = () => {\n  return <CARFQualityMeasuresComponent />;\n};\n\nexport default CARFQualityMeasures;\n"], "names": ["CARF_COMPLIANCE_LEVELS", "FULL_COMPLIANCE", "score", "label", "color", "description", "SUBSTANTIAL_COMPLIANCE", "PARTIAL_COMPLIANCE", "NON_COMPLIANCE", "CARFQualityMeasures", "_qualityData$satisfac", "_qualityData$satisfac2", "_qualityData$satisfac3", "_qualityData$satisfac4", "_qualityData$satisfac5", "_qualityData$outcomes", "_qualityData$outcomes2", "_qualityData$outcomes3", "_qualityData$outcomes4", "_qualityData$efficien", "_qualityData$efficien2", "_qualityData$efficien3", "_qualityData$function", "_qualityData$function2", "_qualityData$function3", "_qualityData$function4", "_qualityData$function5", "_qualityData$function6", "_qualityData$function7", "_qualityData$function8", "_qualityData$riskMana", "_qualityData$riskMana2", "_qualityData$riskMana3", "_qualityData$riskMana4", "_qualityData$riskMana5", "_qualityData$riskMana6", "_qualityData$riskMana7", "t", "isRTL", "useLanguage", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "useState", "qualityData", "setQualityData", "loading", "setLoading", "useEffect", "mockData", "satisfaction", "overallSatisfaction", "serviceQuality", "recommendationLikelihood", "communicationEffectiveness", "culturalCompetency", "trend", "target", "responses", "responseRate", "outcomes", "goalAchievement", "functionalImprovement", "dischargeToCommunity", "readmissionRate", "averageLengthOfStay", "totalPatients", "efficiency", "assessmentCompletion", "treatmentPlanTimeliness", "serviceInitiation", "dischargePlanningTimeliness", "documentationCompletion", "functionalOutcomes", "fim", "admissionAverage", "dischargeAverage", "improvement", "efficiencyScore", "qualityOfLife", "physicalHealth", "psychologicalWellbeing", "socialRelationships", "environmentalFactors", "overallScore", "communityParticipation", "employment", "socialActivities", "independentLiving", "recreation", "overallParticipation", "riskManagement", "fallIncidents", "medicationErrors", "equipmentFailures", "behavioralIncidents", "totalIncidents", "incidentRate", "setTimeout", "MetricCard", "_ref", "title", "value", "unit", "status", "icon", "_jsxs", "className", "children", "_jsx", "concat", "startsWith", "style", "width", "Math", "min", "OutcomeChart", "_ref2", "data", "type", "Object", "entries", "map", "_ref3", "key", "replace", "trim", "toFixed", "onChange", "e", "admission", "discharge", "CARFQualityMeasuresComponent"], "sourceRoot": ""}