{"version": 3, "file": "static/js/2844.e3673cc9.chunk.js", "mappings": "8LAGA,MAwQA,EAxQmCA,IAA4B,IAA3B,QAAEC,EAAO,SAAEC,GAAUF,EACvD,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,aAErCC,EAAiB,CACrB,CAAEC,GAAI,SAAUC,MAAOP,EAAE,SAAU,4BAA6BQ,KAAM,gBACtE,CAAEF,GAAI,iBAAkBC,MAAOP,EAAE,gBAAiB,kBAAmBQ,KAAM,gBAC3E,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,gBACxE,CAAEF,GAAI,0BAA2BC,MAAOP,EAAE,yBAA0B,2BAA4BQ,KAAM,gBACtG,CAAEF,GAAI,eAAgBC,MAAOP,EAAE,cAAe,gBAAiBQ,KAAM,gBACrE,CAAEF,GAAI,qBAAsBC,MAAOP,EAAE,oBAAqB,sBAAuBQ,KAAM,gBACvF,CAAEF,GAAI,OAAQC,MAAOP,EAAE,OAAQ,QAASQ,KAAM,UAC9C,CAAEF,GAAI,qBAAsBC,MAAOP,EAAE,oBAAqB,+BAAgCQ,KAAM,uBAG5FC,EAAuB,CAC3B,CAAEH,GAAI,SAAUC,MAAOP,EAAE,SAAU,wBAAyBQ,KAAM,sBAClE,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,gBACxE,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,sBAAuBQ,KAAM,sBAC7E,CAAEF,GAAI,aAAcC,MAAOP,EAAE,YAAa,cAAeQ,KAAM,gBAC/D,CAAEF,GAAI,WAAYC,MAAOP,EAAE,WAAY,0BAA2BQ,KAAM,gBACxE,CAAEF,GAAI,UAAWC,MAAOP,EAAE,UAAW,yBAA0BQ,KAAM,iBAGjEE,EAAqB,CACzB,CAAEJ,GAAI,SAAUC,MAAOP,EAAE,gBAAiB,kBAAmBW,QAAS,CAAC,MAAO,WAAY,OAAQ,UAClG,CAAEL,GAAI,WAAYC,MAAOP,EAAE,kBAAmB,oBAAqBW,QAAS,CAAC,QAAS,OAAQ,WAAY,eAC1G,CAAEL,GAAI,UAAWC,MAAOP,EAAE,eAAgB,iBAAkBW,QAAS,CAAC,cAAe,gBAAiB,cAAe,gBACrH,CAAEL,GAAI,aAAcC,MAAOP,EAAE,kBAAmB,oBAAqBW,QAAS,CAAC,gBAAiB,gBAAiB,iBAAkB,mBACnI,CAAEL,GAAI,iBAAkBC,MAAOP,EAAE,sBAAuB,wBAAyBW,QAAS,CAAC,QAAS,WAAY,QAAS,iBAGrHC,EAAO,CACX,CAAEN,GAAI,YAAaC,MAAOP,EAAE,YAAa,0BAA2BQ,KAAM,gBAC1E,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,gBAAiB,iBAAkBQ,KAAM,gBACzE,CAAEF,GAAI,UAAWC,MAAOP,EAAE,iBAAkB,mBAAoBQ,KAAM,sBACtE,CAAEF,GAAI,aAAcC,MAAOP,EAAE,aAAc,uBAAwBQ,KAAM,gBACzE,CAAEF,GAAI,UAAWC,MAAOP,EAAE,cAAe,uBAAwBQ,KAAM,gBACvE,CAAEF,GAAI,SAAUC,MAAOP,EAAE,aAAc,uBAAwBQ,KAAM,uEAGvE,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7Df,EAAE,sBAAuB,oCAE5BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2GAA0GC,UAChH,OAAPjB,QAAO,IAAPA,OAAO,EAAPA,EAASmB,mBAAoBjB,EAAE,cAAe,uBAMrDgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDH,EAAKM,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMjB,EAAagB,EAAIb,IAChCQ,UAAS,0FAAAO,OACPnB,IAAciB,EAAIb,GACd,mDACA,0HACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAOI,EAAIX,QACXQ,EAAAA,EAAAA,KAAA,QAAAD,SAAOI,EAAIZ,UATNY,EAAIb,UAgBjBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CACT,cAAdb,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,4BAA6B,oCAElCgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEV,EAAea,IAAKI,IACnBN,EAAAA,EAAAA,KAAA,OAEEF,UAAU,sIAAqIC,UAE/IF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,WAAUC,SAAEO,EAAUd,QACtCK,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEO,EAAUf,SACrEM,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEO,KAAK,QACLC,KAAK,mBACLC,MAAOH,EAAUhB,GACjBQ,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEf,EAAE,UAAW,iBAE3Ea,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEO,KAAK,WACLC,KAAK,qBACLC,MAAOH,EAAUhB,GACjBQ,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEf,EAAE,YAAa,4BAxB9EsB,EAAUhB,UAmCV,kBAAdJ,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,uBAAwB,0CAE7BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDN,EAAqBS,IAAKQ,IACzBb,EAAAA,EAAAA,MAAA,OAEEC,UAAU,6DAA4DC,SAAA,EAEtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAEW,EAAOlB,QAClCQ,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEW,EAAOnB,YAEpEM,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SAAOO,KAAK,WAAWT,UAAU,UACjCE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEf,EAAE,SAAU,iBAE1Ea,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SAAOO,KAAK,WAAWT,UAAU,UACjCE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEf,EAAE,YAAa,0BAE7Ea,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SAAOO,KAAK,WAAWT,UAAU,UACjCE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEf,EAAE,eAAgB,2BAlB7E0B,EAAOpB,QAyBlBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yDAAwDC,SAAA,EACrEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,SAClEf,EAAE,qBAAsB,uCAE3BgB,EAAAA,EAAAA,KAAA,YACEF,UAAU,gIACVa,KAAK,IACLC,YAAa5B,EAAE,gCAAiC,mGAMzC,YAAdE,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,2BAA4B,iCAEjCgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBL,EAAmBQ,IAAKW,IACvBhB,EAAAA,EAAAA,MAAA,OAAsBC,UAAU,6DAA4DC,SAAA,EAC1FC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAAEc,EAAQtB,SACxES,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDc,EAAQlB,QAAQO,IAAKY,IACpBjB,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,mIAAkIC,SAAA,EAC9JC,EAAAA,EAAAA,KAAA,SACEO,KAAK,QACLC,KAAI,WAAAH,OAAaQ,EAAQvB,IACzBmB,MAAOK,EACPhB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDf,EAAE8B,EAAOC,QAAQ,IAAK,IAAKD,EAAOC,QAAQ,IAAK,UARxCD,QAJRD,EAAQvB,QAqBtBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEf,EAAE,oBAAqB,0CAE1BgB,EAAAA,EAAAA,KAAA,YACEF,UAAU,8HACVa,KAAK,IACLC,YAAa5B,EAAE,+BAAgC,8FAMxC,eAAdE,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,qBAAsB,uCAG3Ba,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mEAAkEC,SAAA,EAC9EC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,qBAAsB,2BAE3BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CAAC,cAAe,gBAAiB,qBAAsB,iBAAkB,mBAAoB,WAAWG,IAAKc,IAC5GnB,EAAAA,EAAAA,MAAA,SAAqBC,UAAU,oBAAmBC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,SAAOO,KAAK,WAAWT,UAAU,UACjCE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDf,EAAEgC,EAASA,EAAQD,QAAQ,IAAK,UAHzBC,UAUlBnB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mEAAkEC,SAAA,EAC9EC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,WACtBf,EAAE,oBAAqB,0BAE1BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CAAC,iBAAkB,cAAe,cAAe,QAAS,kBAAmB,oBAAoBG,IAAKe,IACrGpB,EAAAA,EAAAA,MAAA,SAAsBC,UAAU,oBAAmBC,SAAA,EACjDC,EAAAA,EAAAA,KAAA,SAAOO,KAAK,WAAWT,UAAU,UACjCE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDf,EAAEiC,EAAUA,EAASF,QAAQ,IAAK,UAH3BE,mBAe1BjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,UACpCC,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAc,OAARrB,QAAQ,IAARA,OAAQ,EAARA,EAAWD,GAC1BgB,UAAU,wIAAuIC,SAEhJf,EAAE,cAAe,wBChC5B,EAjO2BH,IAA0B,IAAzB,gBAAEqC,GAAiBrC,EAC7C,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPkC,EAAkBC,IAAuBhC,EAAAA,EAAAA,UAAS,aAClDiC,EAAkBC,IAAuBlC,EAAAA,EAAAA,UAAS,IAEnDmC,EAA0B,CAC9BC,SAAU,CACRjC,MAAOP,EAAE,WAAY,YACrBQ,KAAM,eACNiC,MAAO,kDACPC,MAAO,CACL,CAAEpC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,iBAC7D,CAAEnC,GAAI,MAAOqC,KAAM3C,EAAE,MAAO,OAAQQ,KAAM,eAAMiC,MAAO,eACvD,CAAEnC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,cAC7D,CAAEnC,GAAI,SAAUqC,KAAM3C,EAAE,SAAU,UAAWQ,KAAM,eAAMiC,MAAO,iBAChE,CAAEnC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,eAC7D,CAAEnC,GAAI,UAAWqC,KAAM3C,EAAE,UAAW,WAAYQ,KAAM,eAAMiC,MAAO,iBACnE,CAAEnC,GAAI,WAAYqC,KAAM3C,EAAE,WAAY,YAAaQ,KAAM,eAAMiC,MAAO,iBACtE,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,kBAG9DG,MAAO,CACLrC,MAAOP,EAAE,QAAS,SAClBQ,KAAM,eACNiC,MAAO,4CACPC,MAAO,CACL,CAAEpC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,eAC7D,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,cAC1D,CAAEnC,GAAI,WAAYqC,KAAM3C,EAAE,WAAY,YAAaQ,KAAM,eAAMiC,MAAO,eACtE,CAAEnC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,iBAC7D,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,cAC1D,CAAEnC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,iBAC7D,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,gBAC1D,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,SAAKiC,MAAO,gBAG7DI,WAAY,CACVtC,MAAOP,EAAE,aAAc,cACvBQ,KAAM,eACNiC,MAAO,+CACPC,MAAO,CACL,CAAEpC,GAAI,WAAYqC,KAAM3C,EAAE,WAAY,YAAaQ,KAAM,qBAAOiC,MAAO,gBACvE,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,eAC1D,CAAEnC,GAAI,UAAWqC,KAAM3C,EAAE,UAAW,WAAYQ,KAAM,eAAMiC,MAAO,iBACnE,CAAEnC,GAAI,UAAWqC,KAAM3C,EAAE,UAAW,WAAYQ,KAAM,eAAMiC,MAAO,iBACnE,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,eAC1D,CAAEnC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,iBAC7D,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,iBAC1D,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,eAAMiC,MAAO,kBAG9DK,KAAM,CACJvC,MAAOP,EAAE,OAAQ,mBACjBQ,KAAM,eACNiC,MAAO,yCACPC,MAAO,CACL,CAAEpC,GAAI,YAAaqC,KAAM3C,EAAE,WAAY,cAAeQ,KAAM,eAAMiC,MAAO,cACzE,CAAEnC,GAAI,YAAaqC,KAAM3C,EAAE,WAAY,cAAeQ,KAAM,eAAMiC,MAAO,cACzE,CAAEnC,GAAI,WAAYqC,KAAM3C,EAAE,UAAW,aAAcQ,KAAM,eAAMiC,MAAO,cACtE,CAAEnC,GAAI,WAAYqC,KAAM3C,EAAE,UAAW,aAAcQ,KAAM,eAAMiC,MAAO,cACtE,CAAEnC,GAAI,eAAgBqC,KAAM3C,EAAE,cAAe,iBAAkBQ,KAAM,eAAMiC,MAAO,cAClF,CAAEnC,GAAI,UAAWqC,KAAM3C,EAAE,SAAU,WAAYQ,KAAM,SAAKiC,MAAO,gBACjE,CAAEnC,GAAI,cAAeqC,KAAM3C,EAAE,aAAc,eAAgBQ,KAAM,eAAMiC,MAAO,iBAC9E,CAAEnC,GAAI,WAAYqC,KAAM3C,EAAE,UAAW,YAAaQ,KAAM,eAAMiC,MAAO,gBAGzEM,UAAW,CACTxC,MAAOP,EAAE,YAAa,eACtBQ,KAAM,SACNiC,MAAO,kDACPC,MAAO,CACL,CAAEpC,GAAI,MAAOqC,KAAM3C,EAAE,MAAO,OAAQQ,KAAM,SAAKiC,MAAO,gBACtD,CAAEnC,GAAI,KAAMqC,KAAM3C,EAAE,KAAM,MAAOQ,KAAM,SAAKiC,MAAO,cACnD,CAAEnC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,iBAC7D,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,SAAKiC,MAAO,eACzD,CAAEnC,GAAI,OAAQqC,KAAM3C,EAAE,OAAQ,QAASQ,KAAM,SAAKiC,MAAO,iBACzD,CAAEnC,GAAI,WAAYqC,KAAM3C,EAAE,WAAY,YAAaQ,KAAM,eAAMiC,MAAO,gBACtE,CAAEnC,GAAI,QAASqC,KAAM3C,EAAE,QAAS,SAAUQ,KAAM,eAAMiC,MAAO,iBAC7D,CAAEnC,GAAI,YAAaqC,KAAM3C,EAAE,YAAa,aAAcQ,KAAM,eAAMiC,MAAO,oBAKzEO,EAAoBC,IACxB,MAAMC,EAAa,CACjB5C,GAAI6C,KAAKC,MACTT,KAAMM,EAAKN,KACXnC,KAAMyC,EAAKzC,KACX6C,SAAUlB,EACVmB,UAAW,IAAIH,MAGjBb,EAAoBiB,GAAQ,IAAIA,EAAML,IACvB,OAAfhB,QAAe,IAAfA,GAAAA,EAAkBgB,IAepB,OACErC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,qBAAsB,2BAE3BgB,EAAAA,EAAAA,KAAA,UACEI,QApBcoC,KACpBlB,EAAoB,KAoBdxB,UAAU,wIAAuIC,SAEhJf,EAAE,QAAS,cAKfqC,EAAiBoB,OAAS,IACzB5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtDf,EAAE,mBAAoB,wBAEzBa,EAAAA,EAAAA,MAAA,UACEO,QAASA,IA/BCuB,KACpB,GAAI,oBAAqBe,OAAQ,CAC/B,MAAMC,EAAY,IAAIC,yBAAyBjB,GAC/CgB,EAAUE,KAAO,QACjBC,gBAAgBC,MAAMJ,EACxB,GA0ByBK,CAAa3B,EAAiBnB,IAAI+C,GAAKA,EAAEtB,MAAMuB,KAAK,OACnEpD,UAAU,qEAAoEC,SAAA,CAC/E,gBACKf,EAAE,QAAS,gBAGnBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCsB,EAAiBnB,IAAKiD,IACrBtD,EAAAA,EAAAA,MAAA,OAEEC,UAAU,yHAAwHC,SAAA,EAElIC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAEoD,EAAQ3D,QACnCQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,oDAAmDC,SAAEoD,EAAQxB,QAC7E3B,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMkB,EAAoBiB,GAAQA,EAAKa,OAAOH,GAAKA,EAAE3D,KAAO6D,EAAQ7D,KAC7EQ,UAAU,0CAAyCC,SACpD,aARIoD,EAAQ7D,WAkBvBU,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDsD,OAAOC,QAAQ/B,GAAyBrB,IAAIqD,IAAA,IAAEC,EAAKnB,GAASkB,EAAA,OAC3D1D,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMgB,EAAoBoC,GACnC1D,UAAS,4GAAAO,OACPc,IAAqBqC,EACjB,mDACA,0HACHzD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAEsC,EAAS7C,QACpCQ,EAAAA,EAAAA,KAAA,QAAAD,SAAOsC,EAAS9C,UATXiE,UAgBbxD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEwB,EAAwBJ,GAAkBO,MAAMxB,IAAK+B,IACpDjC,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IAAM4B,EAAiBC,GAChCnC,UAAS,6KAAAO,OAA+K4B,EAAKR,MAAK,uBAAsB1B,UAExNF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAEkC,EAAKzC,QACrCQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEkC,EAAKN,WAPLM,EAAK3C,QAehBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qCAAoCC,SAAA,EACjDF,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAM4B,EAAiB,CAAEL,KAAM3C,EAAE,WAAY,cAAeQ,KAAM,eAAMiC,MAAO,iBACxF3B,UAAU,uKAAsKC,SAAA,EAEhLC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBACNC,EAAAA,EAAAA,KAAA,QAAAD,SAAOf,EAAE,WAAY,oBAGvBa,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAM4B,EAAiB,CAAEL,KAAM3C,EAAE,YAAa,eAAgBQ,KAAM,eAAMiC,MAAO,eAC1F3B,UAAU,iKAAgKC,SAAA,EAE1KC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,kBACNC,EAAAA,EAAAA,KAAA,QAAAD,SAAOf,EAAE,YAAa,qBAGxBa,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAM4B,EAAiB,CAAEL,KAAM3C,EAAE,UAAW,YAAaQ,KAAM,SAAKiC,MAAO,gBACpF3B,UAAU,oKAAmKC,SAAA,EAE7KC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,YACNC,EAAAA,EAAAA,KAAA,QAAAD,SAAOf,EAAE,UAAW,wB,cC1N9B,MA8WA,EA9WmCH,IAA8C,IAAD4E,EAAA,IAA5C,eAAEC,EAAc,oBAAEC,GAAqB9E,EACzE,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACP2E,EAAaC,IAAkBzE,EAAAA,EAAAA,UAAS,CAC7C0E,SAAU,CACRC,WAAY,GACZtC,MAAO,OACPuC,UAAU,GAEZC,MAAO,CACLC,OAAQ,GACR3D,KAAM,SACN4D,SAAS,GAEXC,OAAQ,CACNC,YAAY,EACZC,SAAU,SACVC,YAAa,WAEfC,OAAQ,CACNJ,QAAQ,EACRK,OAAO,EACPC,WAAW,MAIRC,EAAcC,IAAmBxF,EAAAA,EAAAA,UAAS,UAE3CyF,EAAiB,CACrBC,gBAAiB,CACftE,KAAMxB,EAAE,iBAAkB,mBAC1BQ,KAAM,eACNuF,YAAa/F,EAAE,qBAAsB,+BACrCgG,SAAU,CACRlB,SAAU,CAAEC,WAAY,GAAItC,MAAO,OAAQuC,UAAU,GACrDC,MAAO,CAAEC,OAAQ,GAAI3D,KAAM,UAAW4D,SAAS,GAC/CC,OAAQ,CAAEC,YAAY,EAAOC,SAAU,OAAQC,YAAa,QAC5DC,OAAQ,CAAEJ,QAAQ,EAAMK,OAAO,EAAOC,WAAW,KAGrDO,gBAAiB,CACfzE,KAAMxB,EAAE,iBAAkB,mBAC1BQ,KAAM,eACNuF,YAAa/F,EAAE,qBAAsB,0BACrCgG,SAAU,CACRlB,SAAU,CAAEC,WAAY,GAAItC,MAAO,UAAWuC,UAAU,GACxDC,MAAO,CAAEC,OAAQ,GAAI3D,KAAM,SAAU4D,SAAS,GAC9CC,OAAQ,CAAEC,YAAY,EAAMC,SAAU,SAAUC,YAAa,WAC7DC,OAAQ,CAAEJ,QAAQ,EAAMK,OAAO,EAAMC,WAAW,KAGpDQ,QAAS,CACP1E,KAAMxB,EAAE,UAAW,WACnBQ,KAAM,qBACNuF,YAAa/F,EAAE,cAAe,uCAC9BgG,SAAU,CACRlB,SAAU,CAAEC,WAAY,GAAItC,MAAO,OAAQuC,UAAU,GACrDC,MAAO,CAAEC,OAAQ,GAAI3D,KAAM,SAAU4D,SAAS,GAC9CC,OAAQ,CAAEC,YAAY,EAAOC,SAAU,MAAOC,YAAa,QAC3DC,OAAQ,CAAEJ,QAAQ,EAAMK,OAAO,EAAOC,WAAW,KAGrDS,MAAO,CACL3E,KAAMxB,EAAE,QAAS,SACjBQ,KAAM,eACNuF,YAAa/F,EAAE,YAAa,0CAC5BgG,SAAU,CACRlB,SAAU,CAAEC,WAAY,GAAItC,MAAO,UAAWuC,UAAU,GACxDC,MAAO,CAAEC,OAAQ,EAAG3D,KAAM,UAAW4D,SAAS,GAC9CC,OAAQ,CAAEC,YAAY,EAAOC,SAAU,OAAQC,YAAa,WAC5DC,OAAQ,CAAEJ,QAAQ,EAAMK,OAAO,EAAOC,WAAW,MAKjDU,EAAe,CACnB,CAAE9F,GAAI,UAAWkB,KAAMxB,EAAE,UAAW,WAAYQ,KAAM,gBACtD,CAAEF,GAAI,SAAUkB,KAAMxB,EAAE,eAAgB,iBAAkBQ,KAAM,gBAChE,CAAEF,GAAI,cAAekB,KAAMxB,EAAE,aAAc,eAAgBQ,KAAM,gBACjE,CAAEF,GAAI,YAAakB,KAAMxB,EAAE,YAAa,mBAAoBQ,KAAM,gBAClE,CAAEF,GAAI,SAAUkB,KAAMxB,EAAE,SAAU,gBAAiBQ,KAAM,iBAGrD6F,EAAe,CACnB,CAAE/F,GAAI,UAAWkB,KAAMxB,EAAE,UAAW,WAAYsG,OAAQ,CAAC,UAAW,UAAW,YAC/E,CAAEhG,GAAI,OAAQkB,KAAMxB,EAAE,OAAQ,QAASsG,OAAQ,CAAC,UAAW,UAAW,YACtE,CAAEhG,GAAI,UAAWkB,KAAMxB,EAAE,UAAW,WAAYsG,OAAQ,CAAC,UAAW,UAAW,YAC/E,CAAEhG,GAAI,OAAQkB,KAAMxB,EAAE,OAAQ,QAASsG,OAAQ,CAAC,UAAW,UAAW,YACtE,CAAEhG,GAAI,UAAWkB,KAAMxB,EAAE,UAAW,WAAYsG,OAAQ,CAAC,UAAW,UAAW,cAGjFC,EAAAA,EAAAA,WAAU,KACW,OAAnB5B,QAAmB,IAAnBA,GAAAA,EAAsBC,IACrB,CAACA,EAAaD,IAEjB,MAQM6B,EAAoBA,CAACnD,EAAUmB,EAAK/C,KACxCoD,EAAetB,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdlD,GAAI,IACP,CAACF,IAAQoD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACJlD,EAAKF,IAAS,IACjB,CAACmB,GAAM/C,OAGXmE,EAAgB,WAGlB,OACE/E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,uBACtBf,EAAE,6BAA8B,oCAEnCgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+GAA8GC,UAC/F,QAA5B0D,EAAAoB,EAAeF,UAAa,IAAAlB,OAAA,EAA5BA,EAA8BjD,OAAQxB,EAAE,SAAU,kBAMzDa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,eAAgB,oBAErBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEsD,OAAOC,QAAQuB,GAAgB3E,IAAIqD,IAAA,IAAEC,EAAKkC,GAAOnC,EAAA,OAChDvD,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IA1CAuF,KACnB,MAAMD,EAASb,EAAec,GAC1BD,IACF7B,EAAe6B,EAAOV,UACtBJ,EAAgBe,KAsCOC,CAAYpC,GAC3B1D,UAAS,uEAAAO,OACPsE,IAAiBnB,EACb,iDACA,8DACHzD,UAEHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAE2F,EAAOlG,QACvCQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,SAAE2F,EAAOlF,QACxER,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAE2F,EAAOX,kBAX/DvB,WAmBb3D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6EAA4EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,mBAAoB,yBAEzBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,aAAc,iBAEnBgB,EAAAA,EAAAA,KAAA,SACEO,KAAK,QACLsF,IAAI,IACJC,IAAI,MACJrF,MAAOmD,EAAYE,SAASC,WAC5BgC,SAAWC,GAAMR,EAAkB,WAAY,aAAcS,SAASD,EAAEE,OAAOzF,QAC/EX,UAAU,sEAEZD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4DAA2DC,SAAA,CACvE6D,EAAYE,SAASC,WAAW,WAIrClE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,mBAAoB,wBAEzBa,EAAAA,EAAAA,MAAA,UACEY,MAAOmD,EAAYE,SAASrC,MAC5BsE,SAAWC,GAAMR,EAAkB,WAAY,QAASQ,EAAEE,OAAOzF,OACjEX,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQS,MAAM,OAAMV,SAAEf,EAAE,OAAQ,WAChCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,UAASV,SAAEf,EAAE,UAAW,cACtCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,OAAMV,SAAEf,EAAE,OAAQ,WAChCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,UAASV,SAAEf,EAAE,UAAW,oBAI1CgB,EAAAA,EAAAA,KAAA,OAAAD,UACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEO,KAAK,WACL4F,QAASvC,EAAYE,SAASE,SAC9B+B,SAAWC,GAAMR,EAAkB,WAAY,WAAYQ,EAAEE,OAAOC,SACpErG,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SACnEf,EAAE,gBAAiB,gCAQ9Ba,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6EAA4EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,gBAAiB,sBAEtBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,yBAAwBC,SAAA,EACvCC,EAAAA,EAAAA,KAAA,SACEO,KAAK,WACL4F,QAASvC,EAAYK,MAAME,QAC3B4B,SAAWC,GAAMR,EAAkB,QAAS,UAAWQ,EAAEE,OAAOC,SAChErG,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SACnEf,EAAE,cAAe,qBAIrB4E,EAAYK,MAAME,UACjBtE,EAAAA,EAAAA,MAAAuG,EAAAA,SAAA,CAAArG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,SAAU,aAEfgB,EAAAA,EAAAA,KAAA,SACEO,KAAK,QACLsF,IAAI,IACJC,IAAI,MACJrF,MAAOmD,EAAYK,MAAMC,OACzB6B,SAAWC,GAAMR,EAAkB,QAAS,SAAUS,SAASD,EAAEE,OAAOzF,QACxEX,UAAU,sEAEZD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4DAA2DC,SAAA,CACvE6D,EAAYK,MAAMC,OAAO,cAMlCrE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,YAAa,iBAElBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDqF,EAAalF,IAAKY,IACjBjB,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMoF,EAAkB,QAAS,OAAQ1E,EAAOxB,IACzDQ,UAAS,uDAAAO,OACPuD,EAAYK,MAAM1D,OAASO,EAAOxB,GAC9B,kFACA,8DACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAAEe,EAAOtB,QACtCQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBC,SAAEe,EAAON,SATxCM,EAAOxB,iBAkBxBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6EAA4EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,uBACtBf,EAAE,iBAAkB,uBAEvBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,yBAAwBC,SAAA,EACvCC,EAAAA,EAAAA,KAAA,SACEO,KAAK,WACL4F,QAASvC,EAAYQ,OAAOC,WAC5B0B,SAAWC,GAAMR,EAAkB,SAAU,aAAcQ,EAAEE,OAAOC,SACpErG,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SACnEf,EAAE,mBAAoB,2BAI3BgB,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,WAAY,eAEjBa,EAAAA,EAAAA,MAAA,UACEY,MAAOmD,EAAYQ,OAAOE,SAC1ByB,SAAWC,GAAMR,EAAkB,SAAU,WAAYQ,EAAEE,OAAOzF,OAClEX,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQS,MAAM,MAAKV,SAAEf,EAAE,MAAO,UAC9BgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,SAAQV,SAAEf,EAAE,SAAU,aACpCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,OAAMV,SAAEf,EAAE,OAAQ,iBAIpCa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,cAAe,mBAEpBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDsF,EAAanF,IAAKmG,IACjBxG,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMoF,EAAkB,SAAU,cAAea,EAAO/G,IACjEQ,UAAS,uDAAAO,OACPuD,EAAYQ,OAAOG,cAAgB8B,EAAO/G,GACtC,iDACA,8DACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAAoCC,SAChDsG,EAAOf,OAAOpF,IAAI,CAACuB,EAAO6E,KACzBtG,EAAAA,EAAAA,KAAA,OAEEF,UAAU,uBACVyG,MAAO,CAAEC,gBAAiB/E,IAFrB6E,OAMXtG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAAEsG,EAAO7F,SAjBtE6F,EAAO/G,oBA2B1BO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMyD,EAAe,CAC5BC,SAAU,CAAEC,WAAY,GAAItC,MAAO,OAAQuC,UAAU,GACrDC,MAAO,CAAEC,OAAQ,GAAI3D,KAAM,SAAU4D,SAAS,GAC9CC,OAAQ,CAAEC,YAAY,EAAOC,SAAU,SAAUC,YAAa,WAC9DC,OAAQ,CAAEJ,QAAQ,EAAMK,OAAO,EAAOC,WAAW,KAEnD5E,UAAU,wIAAuIC,SAEhJf,EAAE,QAAS,YAEdgB,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAyB,OAAnBuD,QAAmB,IAAnBA,OAAmB,EAAnBA,EAAsBC,GACrC9D,UAAU,wIAAuIC,SAEhJf,EAAE,gBAAiB,2BC/B9B,EAxU2BH,IAAmC,IAAlC,UAAE4H,EAAS,cAAEC,GAAe7H,EACtD,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,QACpCuH,EAAaC,IAAkBxH,EAAAA,EAAAA,UAAS,CAC7CyH,SAAU,GACVC,UAAW,EACXC,SAAU,GACVC,SAAU,GACVC,cAAe,GACfC,QAAS,GACTC,MAAO,KAGHC,EAAgB,CACpB,CAAE9H,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,eAAMiC,MAAO,eACrF,CAAEnC,GAAI,aAAcC,MAAOP,EAAE,aAAc,cAAeQ,KAAM,eAAMiC,MAAO,cAC7E,CAAEnC,GAAI,aAAcC,MAAOP,EAAE,aAAc,cAAeQ,KAAM,eAAMiC,MAAO,eAC7E,CAAEnC,GAAI,UAAWC,MAAOP,EAAE,UAAW,WAAYQ,KAAM,eAAMiC,MAAO,iBACpE,CAAEnC,GAAI,WAAYC,MAAOP,EAAE,WAAY,YAAaQ,KAAM,eAAMiC,MAAO,iBACvE,CAAEnC,GAAI,cAAeC,MAAOP,EAAE,cAAe,eAAgBQ,KAAM,eAAMiC,MAAO,gBAChF,CAAEnC,GAAI,gBAAiBC,MAAOP,EAAE,gBAAiB,iBAAkBQ,KAAM,eAAMiC,MAAO,iBACtF,CAAEnC,GAAI,QAASC,MAAOP,EAAE,QAAS,mBAAoBQ,KAAM,eAAMiC,MAAO,kBAGpE4F,EAAiB,CACrB,CAAE/H,GAAI,aAAcC,MAAOP,EAAE,YAAa,cAAeQ,KAAM,gBAC/D,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,gBACxE,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,gBACxE,CAAEF,GAAI,oBAAqBC,MAAOP,EAAE,mBAAoB,qBAAsBQ,KAAM,gBACpF,CAAEF,GAAI,mBAAoBC,MAAOP,EAAE,kBAAmB,oBAAqBQ,KAAM,gBACjF,CAAEF,GAAI,UAAWC,MAAOP,EAAE,UAAW,WAAYQ,KAAM,UACvD,CAAEF,GAAI,aAAcC,MAAOP,EAAE,YAAa,cAAeQ,KAAM,gBAC/D,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,iBAGpEyH,EAAgB,CACpB,CAAE3H,GAAI,iBAAkBC,MAAOP,EAAE,gBAAiB,kBAAmBQ,KAAM,gBAC3E,CAAEF,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,gBACxE,CAAEF,GAAI,kBAAmBC,MAAOP,EAAE,iBAAkB,mBAAoBQ,KAAM,gBAC9E,CAAEF,GAAI,aAAcC,MAAOP,EAAE,YAAa,cAAeQ,KAAM,gBAC/D,CAAEF,GAAI,cAAeC,MAAOP,EAAE,aAAc,eAAgBQ,KAAM,gBAClE,CAAEF,GAAI,qBAAsBC,MAAOP,EAAE,oBAAqB,sBAAuBQ,KAAM,gBACvF,CAAEF,GAAI,eAAgBC,MAAOP,EAAE,cAAe,gBAAiBQ,KAAM,gBACrE,CAAEF,GAAI,iBAAkBC,MAAOP,EAAE,gBAAiB,kBAAmBQ,KAAM,iBAGvE8H,EAAkB,CACtB,CAAE7G,MAAO,EAAGlB,MAAOP,EAAE,UAAW,YAAayC,MAAO,eAAgB8F,MAAO,gBAC3E,CAAE9G,MAAO,EAAGlB,MAAOP,EAAE,MAAO,OAAQyC,MAAO,eAAgB8F,MAAO,gBAClE,CAAE9G,MAAO,EAAGlB,MAAOP,EAAE,WAAY,YAAayC,MAAO,gBAAiB8F,MAAO,gBAC7E,CAAE9G,MAAO,EAAGlB,MAAOP,EAAE,OAAQ,QAASyC,MAAO,gBAAiB8F,MAAO,gBACrE,CAAE9G,MAAO,EAAGlB,MAAOP,EAAE,WAAY,aAAcyC,MAAO,aAAc8F,MAAO,iBA6CvE3H,EAAO,CACX,CAAEN,GAAI,MAAOC,MAAOP,EAAE,cAAe,gBAAiBQ,KAAM,gBAC5D,CAAEF,GAAI,WAAYC,MAAOP,EAAE,WAAY,YAAaQ,KAAM,gBAC1D,CAAEF,GAAI,aAAcC,MAAOP,EAAE,aAAc,cAAeQ,KAAM,iBAGlE,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,qBAAsB,2BAE3BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,UACtD,IAAIoC,MAAOqF,2BAKhBxH,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SACnCH,EAAKM,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMjB,EAAagB,EAAIb,IAChCQ,UAAS,0FAAAO,OACPnB,IAAciB,EAAIb,GACd,mDACA,0HACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAOI,EAAIX,QACXQ,EAAAA,EAAAA,KAAA,QAAAD,SAAOI,EAAIZ,UATNY,EAAIb,SAeF,QAAdJ,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,qBAAsB,2BAE3BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDqH,EAAclH,IAAK2G,IAClB7G,EAAAA,EAAAA,KAAA,UAEEI,QAASA,KAAMqH,OA3FDC,EA2FsBb,OA1FlDD,EAAerE,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAEsE,SAAUa,EAAapI,MAD9BoI,OA4Fd5H,UAAS,0CAAAO,OACPsG,EAAYE,WAAaA,EAASvH,GAC9B,iDACA,6DAA4D,KAAAe,OAC9DwG,EAASpF,OAAQ1B,UAErBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAE8G,EAASrH,QACzCQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCC,SAAE8G,EAAStH,YAV1DsH,EAASvH,WAkBtBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,YAAa,sBAElBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBuH,EAAgBpH,IAAKyH,IACpB9H,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMwG,EAAerE,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAEuE,UAAWa,EAAMlH,SACnEX,UAAS,6EAAAO,OACPsG,EAAYG,YAAca,EAAMlH,MAC5B,iDACA,6DAA4D,KAAAJ,OAC9DsH,EAAMlG,OAAQ1B,SAAA,EAElBC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAE4H,EAAMJ,SACjCvH,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4BAA2BC,SAAE4H,EAAMpI,UAT9CoI,EAAMlH,cAenBZ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,WAAY,eAEjBa,EAAAA,EAAAA,MAAA,UACEY,MAAOkG,EAAYI,SACnBhB,SAAWC,GAAMY,EAAerE,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAEwE,SAAUf,EAAEE,OAAOzF,SACvEX,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQS,MAAM,GAAEV,SAAEf,EAAE,iBAAkB,sBACtCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,aAAYV,SAAEf,EAAE,YAAa,qBAC3CgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,SAAQV,SAAEf,EAAE,UAAW,kBACrCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,UAASV,SAAEf,EAAE,WAAY,mBACvCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,WAAUV,SAAEf,EAAE,YAAa,oBACzCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,aAAYV,SAAEf,EAAE,YAAa,+BAMjDa,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,qBAAsB,0BAE3BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDsH,EAAenH,IAAKc,IACnBhB,EAAAA,EAAAA,KAAA,UAEEI,QAASA,KAAMwH,OA3JFC,EA2JsB7G,EAAQ1B,QA1JzDsH,EAAerE,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdlD,GAAI,IACPyE,SAAUzE,EAAKyE,SAASc,SAASD,GAC7BtF,EAAKyE,SAAS5D,OAAOpE,GAAKA,IAAM6I,GAChC,IAAItF,EAAKyE,SAAUa,MALEA,OA4Jb/H,UAAS,0CAAAO,OACPsG,EAAYK,SAASc,SAAS9G,EAAQ1B,IAClC,8CACA,8DACHS,UAEHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAAEiB,EAAQxB,QACvCQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAAEiB,EAAQzB,YAVzEyB,EAAQ1B,WAkBrBO,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,oBAAqB,yBAE1BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDkH,EAAc/G,IAAK6H,IAClB/H,EAAAA,EAAAA,KAAA,UAEEI,QAASA,KAAM4H,OA3KGC,EA2KsBF,EAAazI,QA1KnEsH,EAAerE,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdlD,GAAI,IACP0E,cAAe1E,EAAK0E,cAAca,SAASG,GACvC1F,EAAK0E,cAAc7D,OAAO8E,GAAKA,IAAMD,GACrC,IAAI1F,EAAK0E,cAAegB,MALEA,OA4KlBnI,UAAS,0CAAAO,OACPsG,EAAYM,cAAca,SAASC,EAAazI,IAC5C,oDACA,8DACHS,UAEHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAAEgI,EAAavI,QAC5CQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAAEgI,EAAaxI,YAV9EwI,EAAazI,WAkB1BO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,UAAW,cAEhBa,EAAAA,EAAAA,MAAA,UACEY,MAAOkG,EAAYO,QACnBnB,SAAWC,GAAMY,EAAerE,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAE2E,QAASlB,EAAEE,OAAOzF,SACtEX,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQS,MAAM,GAAEV,SAAEf,EAAE,gBAAiB,qBACrCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,mBAAkBV,SAAEf,EAAE,kBAAmB,uBACvDgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,wBAAuBV,SAAEf,EAAE,sBAAuB,4BAChEgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,qBAAoBV,SAAEf,EAAE,oBAAqB,yBAC3DgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,YAAWV,SAAEf,EAAE,YAAa,gBAC1CgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,UAASV,SAAEf,EAAE,UAAW,oBAI1Ca,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,kBAAmB,uBAExBgB,EAAAA,EAAAA,KAAA,YACES,MAAOkG,EAAYQ,MACnBpB,SAAWC,GAAMY,EAAerE,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAE4E,MAAOnB,EAAEE,OAAOzF,SACpEE,KAAK,IACLb,UAAU,4HACVc,YAAa5B,EAAE,mBAAoB,sEAMzCgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC/BC,EAAAA,EAAAA,KAAA,UACEI,QAvNY+H,KACtB,MAAMC,GAAQ3C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTkB,GAAW,IACdrE,UAAW,IAAIH,KACfsE,cAEW,OAAbC,QAAa,IAAbA,GAAAA,EAAgB0B,GAGhBxB,EAAe,CACbC,SAAU,GACVC,UAAW,EACXC,SAAU,GACVC,SAAU,GACVC,cAAe,GACfC,QAAS,GACTC,MAAO,MAwMCkB,UAAW1B,EAAYE,SACvB/G,UAAU,yLAAwLC,SAEjMf,EAAE,kBAAmB,4BAMf,aAAdE,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAC,kBAC/BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,mBAAoB,wBAEzBgB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5Cf,EAAE,qBAAsB,mDAKhB,eAAdE,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAC,kBAC/BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,qBAAsB,0BAE3BgB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5Cf,EAAE,uBAAwB,4D,cChUvC,MA6ZA,EA7ZuBH,IAA8D,IAA7D,eAAE6E,EAAc,kBAAE4E,EAAiB,iBAAEC,GAAkB1J,EAC7E,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPuJ,EAAUC,IAAerJ,EAAAA,EAAAA,UAAS,KAClCsJ,EAAkBC,IAAuBvJ,EAAAA,EAAAA,UAAS,OAClDwJ,EAAcC,IAAmBzJ,EAAAA,EAAAA,UAAS,WAE3C0J,EAAkB,CACtBC,QAAS,CACPzJ,GAAI,UACJkB,KAAMxB,EAAE,UAAW,WACnBQ,KAAM,eACNiC,MAAO,cACPsF,SAAU,EACVhC,YAAa/F,EAAE,cAAe,yBAEhCgK,OAAQ,CACN1J,GAAI,SACJkB,KAAMxB,EAAE,SAAU,WAClBQ,KAAM,eACNiC,MAAO,eACPsF,SAAU,GACVhC,YAAa/F,EAAE,aAAc,gCAE/BiK,SAAU,CACR3J,GAAI,WACJkB,KAAMxB,EAAE,WAAY,YACpBQ,KAAM,qBACNiC,MAAO,aACPsF,SAAU,GACVhC,YAAa/F,EAAE,eAAgB,4BAEjCkK,cAAe,CACb5J,GAAI,gBACJkB,KAAMxB,EAAE,eAAgB,iBACxBQ,KAAM,eACNiC,MAAO,gBACPsF,SAAU,EACVhC,YAAa/F,EAAE,mBAAoB,6BAErCmK,aAAc,CACZ7J,GAAI,eACJkB,KAAMxB,EAAE,cAAe,gBACvBQ,KAAM,eACNiC,MAAO,gBACPsF,SAAU,GACVhC,YAAa/F,EAAE,kBAAmB,gCAEpCoK,cAAe,CACb9J,GAAI,gBACJkB,KAAMxB,EAAE,gBAAiB,iBACzBQ,KAAM,eACNiC,MAAO,cACPsF,SAAU,GACVhC,YAAa/F,EAAE,oBAAqB,2BAEtCqK,WAAY,CACV/J,GAAI,aACJkB,KAAMxB,EAAE,YAAa,cACrBQ,KAAM,eACNiC,MAAO,gBACPsF,SAAU,GACVhC,YAAa/F,EAAE,gBAAiB,8BAElCsK,QAAS,CACPhK,GAAI,UACJkB,KAAMxB,EAAE,UAAW,YACnBQ,KAAM,eACNiC,MAAO,cACPsF,SAAU,EACVhC,YAAa/F,EAAE,cAAe,2BAEhCuK,QAAS,CACPjK,GAAI,UACJkB,KAAMxB,EAAE,UAAW,WACnBQ,KAAM,eACNiC,MAAO,gBACPsF,SAAU,EACVhC,YAAa/F,EAAE,cAAe,kCAI5BwK,EAAoB,CACxB1E,gBAAiB,CACftE,KAAMxB,EAAE,iBAAkB,mBAC1B+F,YAAa/F,EAAE,qBAAsB,2CACrC6C,WAAY,CAAC,UAAW,gBAAiB,SAAU,WAAY,gBAAiB,gBAAiB,UAAW,YAE9G4H,cAAe,CACbjJ,KAAMxB,EAAE,eAAgB,wBACxB+F,YAAa/F,EAAE,oBAAqB,oCACpC6C,WAAY,CAAC,UAAW,SAAU,WAAY,eAAgB,WAAY,UAAW,YAEvF6H,sBAAuB,CACrBlJ,KAAMxB,EAAE,uBAAwB,yBAChC+F,YAAa/F,EAAE,mBAAoB,wCACnC6C,WAAY,CAAC,UAAW,gBAAiB,SAAU,eAAgB,gBAAiB,aAAc,YAEpG8H,oBAAqB,CACnBnJ,KAAMxB,EAAE,qBAAsB,uBAC9B+F,YAAa/F,EAAE,sBAAuB,gCACtC6C,WAAY,CAAC,UAAW,gBAAiB,WAAY,gBAAiB,eAAgB,gBAAiB,aAsBrG+H,EAAeA,CAACC,EAAQC,KAC5B,MAAMC,EAAevB,EAASwB,UAAU/H,GAAQA,EAAK3C,KAAOuK,GAC5D,IAAsB,IAAlBE,EAAqB,OAEzB,MAAME,EAAyB,OAAdH,EAAqBC,EAAe,EAAIA,EAAe,EACxE,GAAIE,EAAW,GAAKA,GAAYzB,EAAS/F,OAAQ,OAEjD,MAAMyH,EAAc,IAAI1B,IACvB0B,EAAYH,GAAeG,EAAYD,IAAa,CAACC,EAAYD,GAAWC,EAAYH,IACzFtB,EAAYyB,IAGRC,EAAqBA,KACzB,GAAwB,IAApB3B,EAAS/F,OACX,MAAO,QAGT,MAAM2H,EAAe5B,EAASA,EAAS/F,OAAS,GAC1C4H,EAAgBD,EAAaE,WAC5BC,EAAOC,GAAWH,EAAcI,MAAM,KAAKvK,IAAIwK,QAChDC,EAAc,IAAIxI,KAGxB,OAFAwI,EAAYC,SAASL,EAAOC,EAAUJ,EAAarD,UAE7C,GAAN1G,OAAUsK,EAAYE,WAAWC,WAAWC,SAAS,EAAG,KAAI,KAAA1K,OAAIsK,EAAYK,aAAaF,WAAWC,SAAS,EAAG,OAuC5GE,EAA0BA,KAC9B,GAAwB,IAApBzC,EAAS/F,OAAc,OAAO,EAClC,MAAMyI,EAAY1C,EAASpF,OAAOnB,GAAQA,EAAKiJ,WAAWzI,OAC1D,OAAO0I,KAAKC,MAAOF,EAAY1C,EAAS/F,OAAU,MAGpD,OACE5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,iBAAkB,uBAEvBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDf,EAAE,YAAa,cAAc,KAlB/BwJ,EAAS6C,OAAO,CAACC,EAAOC,IAAaD,EAAQC,EAASxE,SAAU,GAkBX,IAAE/H,EAAE,UAAW,eAErEa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDf,EAAE,YAAa,aAAa,KAAGiM,IAA0B,cAMhEjL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,SAC5B,CAAC,UAAW,QAAS,UAAUG,IAAKsL,IACnCxL,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IAAMyI,EAAgB2C,GAC/B1L,UAAS,8DAAAO,OACPuI,IAAiB4C,EACb,yBACA,0GACHzL,SAEFf,EAAEwM,EAAMA,IARJA,SAcb3L,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,kBAAmB,uBAIxBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEf,EAAE,YAAa,gBAElBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBsD,OAAOC,QAAQkG,GAAmBtJ,IAAIqD,IAAA,IAAEC,EAAKiI,GAASlI,EAAA,OACrD1D,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IA9FFsL,KACrB,MAAMD,EAAWjC,EAAkBkC,GACnC,GAAID,EAAU,CACZ,MAAMvB,EAAcuB,EAAS5J,WAAW3B,IAAI,CAACyL,EAAYrF,KACvD,MAAMiF,EAAWzC,EAAgB6C,GAC3BrB,EAAY,IAAInI,KAGtB,OAFAmI,EAAUM,SAAS,EAAW,GAARtE,IAEtBb,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACK8F,GAAQ,IACXjM,GAAG,GAADe,OAAKsL,EAAU,KAAAtL,OAAI8B,KAAKC,MAAK,KAAA/B,OAAIiG,GACnCgE,UAAU,GAADjK,OAAKiK,EAAUO,WAAWC,WAAWC,SAAS,EAAG,KAAI,KAAA1K,OAAIiK,EAAUU,aAAaF,WAAWC,SAAS,EAAG,MAChHG,WAAW,EACX/D,MAAO,OAGXsB,EAAYyB,EACd,GA6E6B0B,CAAcpI,GAC7B1D,UAAU,wIAAuIC,SAAA,EAEjJC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAAE0L,EAASjL,QAC7ER,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,SAAE0L,EAAS1G,gBALpEvB,WAYb3D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEf,EAAE,aAAc,iBAEnBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBsD,OAAOwI,OAAO/C,GAAiB5I,IAAKqL,IACnCvL,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IA7JMuL,KAC7B,MAAMJ,EAAWzC,EAAgB6C,GACjC,GAAIJ,EAAU,CACZ,MAAMO,GAAerG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChB8F,GAAQ,IACXjM,GAAG,GAADe,OAAKsL,EAAU,KAAAtL,OAAI8B,KAAKC,OAC1BkI,UAAWH,IACXe,WAAW,EACX/D,MAAO,KAETsB,EAAYlG,GAAQ,IAAIA,EAAMuJ,GAChC,GAkJ6BC,CAAsBR,EAASjM,IAC9CQ,UAAS,yIAAAO,OAA2IkL,EAAS9J,OAAQ1B,UAErKF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,WAAUC,SAAEwL,EAAS/L,QACrCK,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCC,SAAEwL,EAAS/K,QAC7DX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,CAAEwL,EAASxE,SAAS,iBAR9DwE,EAASjM,cAkBxBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,kBAAmB,sBAGH,IAApBwJ,EAAS/F,QACR5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2FAA0FC,SAAA,EACvGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAC,kBAC/BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEf,EAAE,gBAAiB,8BAEtBgB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5Cf,EAAE,sBAAuB,0EAI9BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvByI,EAAStI,IAAI,CAAC+B,EAAMqE,KACnBzG,EAAAA,EAAAA,MAAA,OAEEC,UAAS,6EAAAO,OACP4B,EAAKiJ,UAAY,oDAAsD,6BACtEnL,SAAA,EAEHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,EACtCC,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMwJ,EAAa3H,EAAK3C,GAAI,MACrC+I,SAAoB,IAAV/B,EACVxG,UAAU,oFAAmFC,SAC9F,YAGDC,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMwJ,EAAa3H,EAAK3C,GAAI,QACrC+I,SAAU/B,IAAUkC,EAAS/F,OAAS,EACtC3C,UAAU,oFAAmFC,SAC9F,eAKHC,EAAAA,EAAAA,KAAA,UACEI,QAASA,KAAM4L,OA1JHnC,EA0J4B5H,EAAK3C,QAzJjEmJ,EAAYlG,GAAQA,EAAKrC,IAAI+B,GAC3BA,EAAK3C,KAAOuK,GAAMpE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQxD,GAAI,IAAEiJ,WAAYjJ,EAAKiJ,YAAcjJ,IAFjC4H,OA2JZ/J,UAAS,oFAAAO,OACP4B,EAAKiJ,UACD,2CACA,yCACHnL,SAEFkC,EAAKiJ,WAAa,YAGrBlL,EAAAA,EAAAA,KAAA,OAAKF,UAAS,kEAAAO,OAAoE4B,EAAKR,OAAQ1B,SAC5FkC,EAAKzC,QAGRK,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,MAAIF,UAAS,eAAAO,OAAiB4B,EAAKiJ,UAAY,6BAA+B,iCAAkCnL,SAC7GkC,EAAKzB,QAERX,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wBAAuBC,SAAA,CAAC,IAAEkC,EAAK8E,SAAS,eAE1DlH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDkC,EAAKqI,UAAU,MAAIrI,EAAK8C,sBAK/B/E,EAAAA,EAAAA,KAAA,UACEI,QAASA,KAAM6L,OAxOCpC,EAwO0B5H,EAAK3C,QAvOjEmJ,EAAYlG,GAAQA,EAAKa,OAAOnB,GAAQA,EAAK3C,KAAOuK,IADlBA,OAyOhB/J,UAAU,0CAAyCC,SACpD,eAMHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,YACES,MAAOwB,EAAKkF,MACZpB,SAAWC,IAAMkG,OA3LRrC,EA2L4B5H,EAAK3C,GA3LzB6H,EA2L6BnB,EAAEE,OAAOzF,WA1LzEgI,EAAYlG,GAAQA,EAAKrC,IAAI+B,GAC3BA,EAAK3C,KAAOuK,GAAMpE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQxD,GAAI,IAAEkF,UAAUlF,IAFlBiK,IAACrC,EAAQ1C,GA4LjBvG,YAAa5B,EAAE,WAAY,oCAC3Bc,UAAU,iIACVa,KAAK,UAnEJsB,EAAK3C,OA4EjBkJ,EAAS/F,OAAS,IACjB5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SACnEf,EAAE,kBAAmB,uBAExBa,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CACvDyI,EAASpF,OAAOnB,GAAQA,EAAKiJ,WAAWzI,OAAO,MAAI+F,EAAS/F,OAAO,IAAEzD,EAAE,YAAa,oBAGzFgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,4DACVyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAK4K,IAAyB,oBASxDpL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMqI,EAAY,IAC3B3I,UAAU,wIAAuIC,SAEhJf,EAAE,gBAAiB,qBAEtBgB,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAsB,OAAhBmI,QAAgB,IAAhBA,OAAgB,EAAhBA,EAAmBC,GAClC1I,UAAU,wIAAuIC,SAEhJf,EAAE,eAAgB,0BCrD7B,EAjW8BH,IAA2C,IAA1C,YAAEuN,EAAW,MAAEC,EAAK,aAAEC,GAAczN,EACjE,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPsN,EAAYC,IAAiBpN,EAAAA,EAAAA,UAAS,aACtCqN,EAAWC,IAAgBtN,EAAAA,EAAAA,UAAS,QAGrCuN,EAAe,CACnBN,MAAO,CACL,CAAE/M,GAAI,eAAgBkB,KAAMxB,EAAE,cAAe,gBAAiB4N,SAAU,GAAI1G,OAAQ,IAAKzE,MAAO,eAChG,CAAEnC,GAAI,gBAAiBkB,KAAMxB,EAAE,gBAAiB,iBAAkB4N,SAAU,GAAI1G,OAAQ,IAAKzE,MAAO,gBACpG,CAAEnC,GAAI,sBAAuBkB,KAAMxB,EAAE,qBAAsB,uBAAwB4N,SAAU,GAAI1G,OAAQ,IAAKzE,MAAO,iBACrH,CAAEnC,GAAI,gBAAiBkB,KAAMxB,EAAE,eAAgB,iBAAkB4N,SAAU,GAAI1G,OAAQ,IAAKzE,MAAO,kBAErGoL,WAAY,CACV,CAAEvN,GAAI,EAAGkB,KAAMxB,EAAE,aAAc,2BAA4B8N,UAAU,EAAMC,KAAM,aAAcvN,KAAM,gBACrG,CAAEF,GAAI,EAAGkB,KAAMxB,EAAE,gBAAiB,wBAAyB8N,UAAU,EAAMC,KAAM,aAAcvN,KAAM,gBACrG,CAAEF,GAAI,EAAGkB,KAAMxB,EAAE,oBAAqB,gCAAiC8N,UAAU,EAAO5G,OAAQ,aAAc1G,KAAM,gBACpH,CAAEF,GAAI,EAAGkB,KAAMxB,EAAE,kBAAmB,gCAAiC8N,UAAU,EAAO5G,OAAQ,aAAc1G,KAAM,WAEpHwN,eAAgB,CACd,CAAEC,KAAM,SAAUC,MAAO,GAAI9D,cAAe,GAAIvI,QAAS,GAAIsM,OAAQ,GACrE,CAAEF,KAAM,SAAUC,MAAO,GAAI9D,cAAe,GAAIvI,QAAS,GAAIsM,OAAQ,IACrE,CAAEF,KAAM,SAAUC,MAAO,GAAI9D,cAAe,GAAIvI,QAAS,GAAIsM,OAAQ,IACrE,CAAEF,KAAM,SAAUC,MAAO,GAAI9D,cAAe,GAAIvI,QAAS,GAAIsM,OAAQ,KAEvEC,eAAgB,CACd,CAAEvG,SAAU,cAAewG,MAAO,YAAaC,OAAQ,OAAQ7L,MAAO,kBACtE,CAAEoF,SAAU,QAASwG,MAAO,SAAUC,OAAQ,KAAM7L,MAAO,iBAC3D,CAAEoF,SAAU,UAAWwG,MAAO,aAAcC,OAAQ,OAAQ7L,MAAO,kBACnE,CAAEoF,SAAU,YAAawG,MAAO,aAAcC,OAAQ,OAAQ7L,MAAO,oBAInE8L,EAAe,CACnB,CAAEjO,GAAI,EAAGkO,MAAOxO,EAAE,aAAc,gBAAiB+F,YAAa/F,EAAE,iBAAkB,mCAAoCQ,KAAM,eAAMiO,QAAQ,GAC1I,CAAEnO,GAAI,EAAGkO,MAAOxO,EAAE,eAAgB,sBAAuB+F,YAAa/F,EAAE,mBAAoB,qCAAsCQ,KAAM,eAAMiO,QAAQ,GACtJ,CAAEnO,GAAI,EAAGkO,MAAOxO,EAAE,QAAS,kBAAmB+F,YAAa/F,EAAE,YAAa,0BAA2BQ,KAAM,eAAMiO,QAAQ,GACzH,CAAEnO,GAAI,EAAGkO,MAAOxO,EAAE,SAAU,gBAAiB+F,YAAa/F,EAAE,aAAc,2BAA4BQ,KAAM,eAAMiO,QAAQ,IAGtHC,EAAQ,CACZ,CAAEpO,GAAI,WAAYC,MAAOP,EAAE,WAAY,YAAaQ,KAAM,gBAC1D,CAAEF,GAAI,QAASC,MAAOP,EAAE,QAAS,SAAUQ,KAAM,gBACjD,CAAEF,GAAI,aAAcC,MAAOP,EAAE,aAAc,cAAeQ,KAAM,gBAChE,CAAEF,GAAI,WAAYC,MAAOP,EAAE,WAAY,YAAaQ,KAAM,gBAC1D,CAAEF,GAAI,eAAgBC,MAAOP,EAAE,eAAgB,gBAAiBQ,KAAM,iBAGlEmO,EAAiBA,KACrB9N,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE4M,EAAaN,MAAMnM,IAAK0N,IACvB/N,EAAAA,EAAAA,MAAA,OAAmBC,UAAU,8FAA6FC,SAAA,EACxHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAE6N,EAAKpN,QAChEX,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mDAAkDC,SAAA,CAAE6N,EAAKhB,SAAS,WAEpF5M,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4DAA2DC,UACxEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,gDAAAO,OAAkDuN,EAAKnM,OAChE8E,MAAO,CAAE4F,MAAM,GAAD9L,OAAKuN,EAAKhB,SAAQ,WAGpC/M,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDf,EAAE,SAAU,UAAU,KAAG4O,EAAK1H,OAAO,SAZhC0H,EAAKtO,QAmBnBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,qBAAsB,0BAE3BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDwN,EAAanK,OAAOyK,GAAKA,EAAEJ,QAAQvN,IAAK4N,IACvCjO,EAAAA,EAAAA,MAAA,OAA0BC,UAAU,gIAA+HC,SAAA,EACjKC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,WAAUC,SAAE+N,EAAYtO,QACxCK,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAA2CC,SAAE+N,EAAYN,SACxExN,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAE+N,EAAY/I,mBAJjE+I,EAAYxO,WAY5BO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,iBAAkB,sBAEvBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE4M,EAAaS,eAAelN,IAAI,CAACmN,EAAO/G,KACvCzG,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,yEAAwEC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,SAAEsN,EAAMxG,YACvE7G,EAAAA,EAAAA,KAAA,OAAKF,UAAS,sBAAAO,OAAwBgN,EAAM5L,MAAK,SAAQ1B,SAAEsN,EAAMC,UACjEtN,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SAAEsN,EAAMA,UAHpE/G,YAgNpB,OACEtG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+CAA8CC,UAC3DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAEhCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,wBAAyB,8BAE9BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CF,EAAAA,EAAAA,MAAA,UACEY,MAAOgM,EACP1G,SAAWC,GAAM0G,EAAa1G,EAAEE,OAAOzF,OACvCX,UAAU,2HAA0HC,SAAA,EAEpIC,EAAAA,EAAAA,KAAA,UAAQS,MAAM,OAAMV,SAAEf,EAAE,WAAY,gBACpCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,QAAOV,SAAEf,EAAE,YAAa,iBACtCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,UAASV,SAAEf,EAAE,cAAe,2BAMhDgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SACjD2N,EAAMxN,IAAK6N,IACVlO,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMoM,EAAcuB,EAAKzO,IAClCQ,UAAS,wFAAAO,OACPkM,IAAewB,EAAKzO,GAChB,yBACA,kJACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAOgO,EAAKvO,QACZQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEgO,EAAKxO,UAT/BwO,EAAKzO,SAtCF0O,MACpB,OAAQzB,GACN,IAAK,WAKL,QAAS,OAAOoB,IAJhB,IAAK,QAAS,OA5LhB3N,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,eAAgB,8BAIrBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB4M,EAAaK,eAAe9M,IAAI,CAAC+M,EAAM3G,KACtCzG,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,YAAWC,SAAA,EACpCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEkN,EAAKA,QAChEpN,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEf,EAAE,QAAS,YAC/Da,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEkN,EAAKC,MAAM,WAE5ClN,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2DAA2DyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAK4M,EAAKC,MAAK,cAG3GrN,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEf,EAAE,gBAAiB,oBACvEa,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEkN,EAAK7D,cAAc,WAEpDpJ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4DAA4DyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAK4M,EAAK7D,cAAa,cAGpHvJ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEf,EAAE,UAAW,cACjEa,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEkN,EAAKpM,QAAQ,WAE9Cb,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6DAA6DyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAK4M,EAAKpM,QAAO,cAG/GhB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEf,EAAE,SAAU,aAChEa,EAAAA,EAAAA,MAAA,QAAMC,UAAU,cAAaC,SAAA,CAAEkN,EAAKE,OAAO,WAE7CnN,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6DAA6DyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAK4M,EAAKE,OAAM,kBApCxG7G,WAoLhB,IAAK,aAAc,OApIrBtG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,oBAAqB,yBAG1BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB4M,EAAaE,WAAW3M,IAAI,CAAC+N,EAAW3H,KACvCzG,EAAAA,EAAAA,MAAA,OAAwBC,UAAU,6BAA4BC,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,oEAAAO,OACZ4N,EAAUnB,SAAW,yCAA2C,wCAC/D/M,SACAkO,EAAUnB,SAAW,SAAMmB,EAAUzO,QAExCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,MAAIF,UAAS,eAAAO,OACX4N,EAAUnB,SAAW,qCAAuC,iCAC3D/M,SACAkO,EAAUzN,QAEbR,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDkO,EAAUnB,SAAWmB,EAAUlB,KAAI,GAAA1M,OAAMrB,EAAE,SAAU,UAAS,MAAAqB,OAAK4N,EAAU/H,aAGjF+H,EAAUnB,WACTjN,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,CAC7Df,EAAE,WAAY,YAAY,kBAnBzBiP,EAAU3O,YA6H1B,IAAK,WAAY,OA9FnBU,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,mBAAoB,wBAGzBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,WACtBf,EAAE,oBAAqB,0BAE1BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CAAC,cAAe,gBAAiB,SAASG,IAAI,CAAC2G,EAAUP,KACxDzG,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,oFAAmFC,SAAA,EAC5GC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BC,SAAEf,EAAE6H,EAASqH,cAAerH,MAC3EhH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gCAAgCyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAK,GAAa,GAARiG,EAAU,WAEnFzG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,qCAAoCC,SAAA,CAAE,GAAa,GAARuG,EAAW,YANhEA,UAchBzG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,0EAAyEC,SAAA,EACrFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,uBAAwB,6BAE7BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CAAC,YAAa,UAAW,cAAcG,IAAI,CAAC2G,EAAUP,KACrDzG,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,sFAAqFC,SAAA,EAC9GC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gCAA+BC,SAAEf,EAAE6H,EAASqH,cAAerH,MAC3EhH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCAAiCyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAK,GAAa,EAARiG,EAAS,WAEnFzG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,sCAAqCC,SAAA,CAAE,GAAa,EAARuG,EAAU,YANhEA,iBA2DpB,IAAK,eAAgB,OAzCvBtG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,oBAAqB,yBAG1BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEwN,EAAarN,IAAK4N,IACjBjO,EAAAA,EAAAA,MAAA,OAEEC,UAAS,sDAAAO,OACPyN,EAAYL,OACR,8EACA,+EACH1N,SAAA,EAEHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAE+N,EAAYtO,QAC5CQ,EAAAA,EAAAA,KAAA,MAAIF,UAAS,oBAAAO,OACXyN,EAAYL,OAAS,uCAAyC,oCAC7D1N,SACA+N,EAAYN,SAEfxN,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAE+N,EAAY/I,cACpE+I,EAAYL,SACX5N,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,CAC3Ef,EAAE,SAAU,UAAU,eAhBtB8O,EAAYxO,cAgFtB0O,SCkDT,EA7Y2BnP,IAAyC,IAAxC,eAAE6E,EAAc,eAAEyK,GAAgBtP,EAC5D,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPmP,EAAgBC,IAAqBjP,EAAAA,EAAAA,UAAS,OAC9CkP,EAAcC,IAAmBnP,EAAAA,EAAAA,UAAS,CAC/CmB,KAAM,GACNiO,SAAU,MACVxH,SAAU,GACVC,cAAe,GACfC,QAAS,GACTH,SAAU,GACVI,MAAO,KAGHsH,EAAiB,CACrBC,WAAY,CACVpP,GAAI,aACJkB,KAAMxB,EAAE,sBAAuB,wBAC/BQ,KAAM,eACNiC,MAAO,4BACPkN,UAAW,CACT,CACErP,GAAI,WACJkB,KAAMxB,EAAE,WAAY,YACpBwP,SAAU,OACVI,MAAO,CACL5P,EAAE,gBAAiB,uCACnBA,EAAE,gBAAiB,6BACnBA,EAAE,gBAAiB,6BACnBA,EAAE,gBAAiB,0BACnBA,EAAE,gBAAiB,0BAGvB,CACEM,GAAI,aACJkB,KAAMxB,EAAE,aAAc,uBACtBwP,SAAU,OACVI,MAAO,CACL5P,EAAE,kBAAmB,qBACrBA,EAAE,kBAAmB,0BACrBA,EAAE,kBAAmB,gCACrBA,EAAE,kBAAmB,2BACrBA,EAAE,kBAAmB,0CAGzB,CACEM,GAAI,YACJkB,KAAMxB,EAAE,WAAY,2BACpBwP,SAAU,WACVI,MAAO,CACL5P,EAAE,gBAAiB,4CACnBA,EAAE,gBAAiB,iCACnBA,EAAE,gBAAiB,uBACnBA,EAAE,gBAAiB,8BACnBA,EAAE,gBAAiB,2CAK3B6P,QAAS,CACPvP,GAAI,UACJkB,KAAMxB,EAAE,mBAAoB,qBAC5BQ,KAAM,eACNiC,MAAO,8BACPkN,UAAW,CACT,CACErP,GAAI,UACJkB,KAAMxB,EAAE,UAAW,WACnBwP,SAAU,WACVI,MAAO,CACL5P,EAAE,eAAgB,sCAClBA,EAAE,eAAgB,mCAClBA,EAAE,eAAgB,oBAClBA,EAAE,eAAgB,4CAClBA,EAAE,eAAgB,0CAGtB,CACEM,GAAI,YACJkB,KAAMxB,EAAE,sBAAuB,wBAC/BwP,SAAU,WACVI,MAAO,CACL5P,EAAE,iBAAkB,+BACpBA,EAAE,iBAAkB,kCACpBA,EAAE,iBAAkB,8CACpBA,EAAE,iBAAkB,2BACpBA,EAAE,iBAAkB,yBAGxB,CACEM,GAAI,SACJkB,KAAMxB,EAAE,SAAU,mBAClBwP,SAAU,SACVI,MAAO,CACL5P,EAAE,cAAe,6BACjBA,EAAE,cAAe,qBACjBA,EAAE,cAAe,+BACjBA,EAAE,cAAe,6BACjBA,EAAE,cAAe,wCAKzB6B,QAAS,CACPvB,GAAI,UACJkB,KAAMxB,EAAE,kBAAmB,oBAC3BQ,KAAM,qBACNiC,MAAO,kCACPkN,UAAW,CACT,CACErP,GAAI,WACJkB,KAAMxB,EAAE,kBAAmB,oBAC3BwP,SAAU,SACVI,MAAO,CACL5P,EAAE,gBAAiB,wCACnBA,EAAE,gBAAiB,uCACnBA,EAAE,gBAAiB,mDACnBA,EAAE,gBAAiB,0BACnBA,EAAE,gBAAiB,yBAGvB,CACEM,GAAI,WACJkB,KAAMxB,EAAE,kBAAmB,oBAC3BwP,SAAU,SACVI,MAAO,CACL5P,EAAE,gBAAiB,4BACnBA,EAAE,gBAAiB,6BACnBA,EAAE,gBAAiB,qCACnBA,EAAE,gBAAiB,uBACnBA,EAAE,gBAAiB,gCAOvB8P,EAAiB,CACrBC,IAAK,CAAEtN,MAAO,8BAA+BlC,MAAOP,EAAE,MAAO,QAC7DgQ,OAAQ,CAAEvN,MAAO,gCAAiClC,MAAOP,EAAE,SAAU,WACrEiQ,KAAM,CAAExN,MAAO,gCAAiClC,MAAOP,EAAE,OAAQ,SACjEkQ,SAAU,CAAEzN,MAAO,0BAA2BlC,MAAOP,EAAE,WAAY,cAG/DmQ,EAAoB,CACxB,CAAE7P,GAAI,UAAWkB,KAAMxB,EAAE,gBAAiB,kBAAmBoQ,MAAO,mBAAoBC,KAAMrQ,EAAE,SAAU,WAC1G,CAAEM,GAAI,UAAWkB,KAAMxB,EAAE,kBAAmB,oBAAqBoQ,MAAO,mBAAoBC,KAAMrQ,EAAE,SAAU,WAC9G,CAAEM,GAAI,YAAakB,KAAMxB,EAAE,oBAAqB,sBAAuBoQ,MAAO,MAAOC,KAAMrQ,EAAE,UAAW,YACxG,CAAEM,GAAI,SAAUkB,KAAMxB,EAAE,gBAAiB,kBAAmBoQ,MAAO,mBAAoBC,KAAMrQ,EAAE,YAAa,eAYxGsQ,EAAqBA,KACzBjB,EAAkB,MAClBE,EAAgB,CACdhO,KAAM,GACNiO,SAAU,MACVxH,SAAU,GACVC,cAAe,GACfC,QAAS,GACTH,SAAU,GACVI,MAAO,MAeLoI,EAAwBC,IAE5BC,MAAM,GAADpP,OAAIrB,EAAE,UAAW,WAAU,KAAAqB,OAAImP,EAAQhP,KAAI,MAAAH,OAAKmP,EAAQJ,SAG/D,OACEvP,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,qBAAsB,0BAE1BoP,IACCpO,EAAAA,EAAAA,KAAA,UACEI,QAASkP,EACTxP,UAAU,wIAAuIC,SAEhJf,EAAE,aAAc,mBAKrBoP,GA8DAvO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,iBAAkB,mBAAmB,KAAGoP,EAAe5N,SAE5DX,EAAAA,EAAAA,MAAA,QAAMC,UAAS,8CAAAO,OAAgDyO,EAAeV,EAAeI,UAAU/M,OAAQ1B,SAAA,CAC5G+O,EAAeV,EAAeI,UAAUjP,MAAM,IAAEP,EAAE,WAAY,mBAInEa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SACzDf,EAAE,cAAe,0BAEpBgB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,YAAWC,SACtBqO,EAAeQ,MAAM1O,IAAI,CAACwP,EAAMpJ,KAC/BzG,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,6BAA4BC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,8GAA6GC,SAC1HuG,EAAQ,KAEXtG,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iCAAgCC,SAAE2P,MAJ3CpJ,aAYjBzG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7Df,EAAE,eAAgB,4BAGrBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,WAAY,eAEjBa,EAAAA,EAAAA,MAAA,UACEY,MAAO6N,EAAavH,SACpBhB,SAAWC,GAAMuI,EAAgBhM,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAEwE,SAAUf,EAAEE,OAAOzF,SACxEX,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQS,MAAM,GAAEV,SAAEf,EAAE,iBAAkB,sBACtCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,aAAYV,SAAEf,EAAE,YAAa,sBAC3CgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,UAASV,SAAEf,EAAE,WAAY,mBACvCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,WAAUV,SAAEf,EAAE,YAAa,oBACzCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,aAAYV,SAAEf,EAAE,YAAa,4BAI/Ca,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,UAAW,cAEhBa,EAAAA,EAAAA,MAAA,UACEY,MAAO6N,EAAapH,QACpBnB,SAAWC,GAAMuI,EAAgBhM,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAE2E,QAASlB,EAAEE,OAAOzF,SACvEX,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQS,MAAM,GAAEV,SAAEf,EAAE,gBAAiB,qBACrCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,WAAUV,SAAEf,EAAE,WAAY,eACxCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,qBAAoBV,SAAEf,EAAE,oBAAqB,yBAC3DgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,UAASV,SAAEf,EAAE,UAAW,cACtCgB,EAAAA,EAAAA,KAAA,UAAQS,MAAM,YAAWV,SAAEf,EAAE,YAAa,oCAKhDa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ef,EAAE,iBAAkB,sBAEvBgB,EAAAA,EAAAA,KAAA,YACES,MAAO6N,EAAanH,MACpBpB,SAAWC,GAAMuI,EAAgBhM,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUlD,GAAI,IAAE4E,MAAOnB,EAAEE,OAAOzF,SACrEE,KAAK,IACLb,UAAU,4HACVc,YAAa5B,EAAE,4BAA6B,uFAIhDa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,UACEI,QAASkP,EACTxP,UAAU,wIAAuIC,SAEhJf,EAAE,SAAU,aAEfgB,EAAAA,EAAAA,KAAA,UACEI,QA5LOuP,KACnB,MAAMvH,GAAQ3C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACT6I,GAAY,IACfhM,UAAW,IAAIH,KACfsE,UAAyB,OAAd/C,QAAc,IAAdA,OAAc,EAAdA,EAAgBpE,GAC3BsQ,aAA4B,OAAdxB,QAAc,IAAdA,OAAc,EAAdA,EAAgB9O,KAElB,OAAd6O,QAAc,IAAdA,GAAAA,EAAiB/F,GACjBkH,KAqLYxP,UAAU,qIAAoIC,SAE7If,EAAE,cAAe,kCAMxBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0DAAyDC,SACpEf,EAAE,eAAgB,oBAErBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,CAClCoP,EAAkBU,MAAM,EAAG,GAAG3P,IAAKsP,IAClC3P,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMmP,EAAqBC,GACpC1P,UAAU,sJAAqJC,SAAA,CAChK,gBACKyP,EAAQhP,OAJPgP,EAAQlQ,MAOjBO,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAMmP,EAAqBJ,EAAkB,IACtDrP,UAAU,6IAA4IC,SAAA,CACvJ,gBACKf,EAAE,gBAAiB,yBArL/Ba,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,sBAAuB,4BAE5BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDsD,OAAOwI,OAAO4C,GAAgBvO,IAAKmC,IAClCxC,EAAAA,EAAAA,MAAA,OAAuBC,UAAS,yCAAAO,OAA2CgC,EAASZ,OAAQ1B,SAAA,EAC1FF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gBAAeC,SAAEsC,EAAS7C,OACzC6C,EAAS7B,SAEZR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBsC,EAASsM,UAAUzO,IAAK4P,IACvB9P,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IAzEL0P,KACxBzB,EAAkByB,GAClBvB,EAAgBhM,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACflD,GAAI,IACPhC,KAAMuP,EAASxQ,GACfkP,SAAUsB,EAAStB,aAoEcuB,CAAiBD,GAChChQ,UAAU,qGAAoGC,UAE9GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,4BAA2BC,SAAE+P,EAAStP,QACtDR,EAAAA,EAAAA,KAAA,QAAMF,UAAS,yCAAAO,OAA2CyO,EAAegB,EAAStB,UAAU/M,OAAQ1B,SACjG+O,EAAegB,EAAStB,UAAUjP,YAPlCuQ,EAASxQ,SARZ+C,EAAS/C,WA2BzBO,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,oBAAqB,yBAE1BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDoP,EAAkBjP,IAAKsP,IACtBxP,EAAAA,EAAAA,KAAA,OAAsBF,UAAU,6DAA4DC,UAC1FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEyP,EAAQhP,QACnER,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEyP,EAAQH,QACjErP,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qDAAoDC,SAAEyP,EAAQJ,YAE7EvP,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAMmP,EAAqBC,GACpC1P,UAAU,2IAA0IC,SAAA,CACrJ,gBACKf,EAAE,OAAQ,eAXVwQ,EAAQlQ,iBCyElC,EAxT8B0Q,KAC5B,MAAM,EAAEhR,IAAMC,EAAAA,EAAAA,MACPgR,EAAeC,IAAoB9Q,EAAAA,EAAAA,UAAS,aAC5C+Q,EAAgBC,IAAqBhR,EAAAA,EAAAA,UAAS,CACnDE,GAAI,cACJkB,KAAM,kBACN6P,IAAK,EACLpQ,iBAAkB,SAClBqQ,mBAAoB,aACpBC,eAAgB,oBAGZC,EAAW,CACf,CACElR,GAAI,WACJkO,MAAOxO,EAAE,WAAY,YACrBQ,KAAM,eACNuF,YAAa/F,EAAE,eAAgB,qCAEjC,CACEM,GAAI,UACJkO,MAAOxO,EAAE,iBAAkB,mBAC3BQ,KAAM,eACNuF,YAAa/F,EAAE,cAAe,mCAEhC,CACEM,GAAI,gBACJkO,MAAOxO,EAAE,gBAAiB,iBAC1BQ,KAAM,eACNuF,YAAa/F,EAAE,oBAAqB,+BAEtC,CACEM,GAAI,UACJkO,MAAOxO,EAAE,qBAAsB,uBAC/BQ,KAAM,qBACNuF,YAAa/F,EAAE,cAAe,wCAEhC,CACEM,GAAI,WACJkO,MAAOxO,EAAE,mBAAoB,qBAC7BQ,KAAM,eACNuF,YAAa/F,EAAE,eAAgB,gCAEjC,CACEM,GAAI,YACJkO,MAAOxO,EAAE,gBAAiB,kBAC1BQ,KAAM,eACNuF,YAAa/F,EAAE,gBAAiB,gCAElC,CACEM,GAAI,WACJkO,MAAOxO,EAAE,iBAAkB,mBAC3BQ,KAAM,eACNuF,YAAa/F,EAAE,eAAgB,yCAEjC,CACEM,GAAI,WACJkO,MAAOxO,EAAE,mBAAoB,qBAC7BQ,KAAM,eACNuF,YAAa/F,EAAE,eAAgB,qCAEjC,CACEM,GAAI,YACJkO,MAAOxO,EAAE,qBAAsB,uBAC/BQ,KAAM,eACNuF,YAAa/F,EAAE,gBAAiB,4CAI9ByR,EAAa,CACjB,CACEjD,MAAOxO,EAAE,gBAAiB,kBAC1ByB,MAAO,KACP6M,OAAQ,KACRoD,WAAY,WACZlR,KAAM,gBAER,CACEgO,MAAOxO,EAAE,gBAAiB,sBAC1ByB,MAAO,OACP6M,OAAQ,KACRoD,WAAY,WACZlR,KAAM,gBAER,CACEgO,MAAOxO,EAAE,wBAAyB,0BAClCyB,MAAO,MACP6M,OAAQ,OACRoD,WAAY,WACZlR,KAAM,gBAER,CACEgO,MAAOxO,EAAE,mBAAoB,qBAC7ByB,MAAO,YACP6M,OAAQ,SACRoD,WAAY,UACZlR,KAAM,uBAIJmR,EAAmB,CACvB,CACErR,GAAI,EACJiB,KAAM,WACNiN,MAAOxO,EAAE,mBAAoB,sBAC7B+F,YAAa/F,EAAE,sBAAuB,yCACtC4R,KAAM,iBACNpR,KAAM,eACNiC,MAAO,kBAET,CACEnC,GAAI,EACJiB,KAAM,gBACNiN,MAAOxO,EAAE,uBAAwB,yBACjC+F,YAAa/F,EAAE,mBAAoB,oDACnC4R,KAAM,aACNpR,KAAM,eACNiC,MAAO,iBAET,CACEnC,GAAI,EACJiB,KAAM,UACNiN,MAAOxO,EAAE,oBAAqB,sBAC9B+F,YAAa/F,EAAE,kBAAmB,oCAClC4R,KAAM,cACNpR,KAAM,qBACNiC,MAAO,oBAILoP,EAAgB,CACpB,CACEvR,GAAI,EACJkO,MAAOxO,EAAE,yBAA0B,4BACnC4N,SAAU,GACV1G,OAAQlH,EAAE,4BAA6B,iCACvC8R,QAAS9R,EAAE,WAAY,cAEzB,CACEM,GAAI,EACJkO,MAAOxO,EAAE,qBAAsB,wBAC/B4N,SAAU,GACV1G,OAAQlH,EAAE,oBAAqB,uBAC/B8R,QAAS9R,EAAE,WAAY,cAEzB,CACEM,GAAI,EACJkO,MAAOxO,EAAE,uBAAwB,0BACjC4N,SAAU,GACV1G,OAAQlH,EAAE,yBAA0B,2BACpC8R,QAAS9R,EAAE,YAAa,gBAItB2O,EAAiBA,KACrB9N,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yEAAwEC,UACrFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0BAAyBC,SAAEoQ,EAAe3P,QACxDX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4CAA2CC,SAAA,EACxDF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,gBAAIf,EAAE,MAAO,OAAO,KAAGmR,EAAeE,QAC5CxQ,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,gBAAIf,EAAE,YAAa,aAAa,KAAGA,EAAEmR,EAAelQ,iBAAkBkQ,EAAelQ,sBAC3FJ,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,gBAAIf,EAAE,gBAAiB,iBAAiB,KAAGA,EAAEmR,EAAeG,mBAAoBH,EAAeG,8BAGzGtQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBC,SAAC,uBAOzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE0Q,EAAWvQ,IAAI,CAAC6Q,EAAMzK,KACrBtG,EAAAA,EAAAA,KAAA,OAAiBF,UAAU,qDAAoDC,UAC7EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAAEgR,EAAKvD,SAC1ExN,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAAEgR,EAAKtQ,SACtET,EAAAA,EAAAA,KAAA,KAAGF,UAAS,WAAAO,OACU,aAApB0Q,EAAKL,WAA4B,iBACb,aAApBK,EAAKL,WAA4B,eAAiB,iBACjD3Q,SACAgR,EAAKzD,aAGVtN,EAAAA,EAAAA,KAAA,OAAKF,UAAU,WAAUC,SAAEgR,EAAKvR,WAZ1B8G,OAmBdzG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,mBAAoB,wBAEzBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB4Q,EAAiBzQ,IAAKqL,IACrB1L,EAAAA,EAAAA,MAAA,OAAuBC,UAAU,6BAA4BC,SAAA,EAC3DC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,WAAAO,OAAakL,EAAS9J,OAAQ1B,SAAEwL,EAAS/L,QACvDK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAEwL,EAASiC,SACnExN,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEwL,EAASxG,eAClE/E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEwL,EAASqF,YAL5DrF,EAASjM,WAazBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,gBAAiB,qBAEtBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB8Q,EAAc3Q,IAAK0N,IAClB/N,EAAAA,EAAAA,MAAA,OAAmBC,UAAU,YAAWC,SAAA,EACtCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAE6N,EAAKJ,SAC/D3N,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CAAE6N,EAAKhB,SAAS,WAE5E5M,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,2DACVyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAKuN,EAAKhB,SAAQ,WAGpC/M,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6EAA4EC,SAAA,EACzFC,EAAAA,EAAAA,KAAA,QAAAD,SAAO6N,EAAK1H,UACZlG,EAAAA,EAAAA,KAAA,QAAAD,SAAO6N,EAAKkD,eAbNlD,EAAKtO,gBAgD3B,OACEU,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,UACvDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,SAClEf,EAAE,sBAAuB,qCAE5BgB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5Cf,EAAE,mBAAoB,4DAK3BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,SACjDyQ,EAAStQ,IAAK8Q,IACbnR,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAM8P,EAAiBc,EAAQ1R,IACxCQ,UAAS,wFAAAO,OACP4P,IAAkBe,EAAQ1R,GACtB,yBACA,sGACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAOiR,EAAQxR,QACfQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEiR,EAAQxD,UATlCwD,EAAQ1R,UAgBrBU,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,SA3DLiO,MACpB,OAAQiC,GACN,IAAK,WAkBL,QACE,OAAOtC,IAjBT,IAAK,UACH,OAAO3N,EAAAA,EAAAA,KAACiR,EAA0B,CAACnS,QAASqR,EAAgBpR,SAAUqR,IACxE,IAAK,gBACH,OAAOpQ,EAAAA,EAAAA,KAACkR,EAAkB,CAAChQ,gBAAkBiC,GAAYgO,QAAQC,IAAI,WAAYjO,KACnF,IAAK,UACH,OAAOnD,EAAAA,EAAAA,KAACqR,EAA0B,CAAC3N,eAAgByM,EAAgBxM,oBAAsB2N,GAAQH,QAAQC,IAAI,eAAgBE,KAC/H,IAAK,WACH,OAAOtR,EAAAA,EAAAA,KAACuR,EAAkB,CAAC9K,UAAW0J,EAAe7Q,GAAIoH,cAAgB0K,GAAQD,QAAQC,IAAI,gBAAiBA,KAChH,IAAK,YACH,OAAOpR,EAAAA,EAAAA,KAACwR,EAAAA,QAAqB,CAAC9N,eAAgByM,EAAgBsB,aAAeC,GAASP,QAAQC,IAAI,kBAAmBM,KACvH,IAAK,WACH,OAAO1R,EAAAA,EAAAA,KAAC2R,EAAc,CAACjO,eAAgByM,EAAgB7H,kBAAmB,GAAIC,iBAAmBC,GAAa2I,QAAQC,IAAI,YAAa5I,KACzI,IAAK,WACH,OAAOxI,EAAAA,EAAAA,KAAC4R,EAAqB,CAACxF,YAAa+D,EAAgB9D,MAAO,GAAIC,aAAc,KACtF,IAAK,YACH,OAAOtM,EAAAA,EAAAA,KAAC6R,EAAkB,CAACnO,eAAgByM,EAAgBhC,eAAiBiD,GAAQD,QAAQC,IAAI,iBAAkBA,OAyC/GpD,U,0FCzTX,MA6ZA,EA7Z8BnP,IAAuC,IAAtC,eAAE6E,EAAc,aAAE+N,GAAc5S,EAC7D,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,MACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,UACpC0S,EAAeC,IAAoB3S,EAAAA,EAAAA,UAAS,CACjDiN,MAAO,GACPxK,WAAY,GACZmQ,eAAgB,GAChBxJ,SAAU,CAAC,EACXyJ,gBAAiB,KAGbC,EAAiB,CACrBhF,MAAO,CACL3N,MAAOP,EAAE,cAAe,gBACxBQ,KAAM,eACNiC,MAAO,8BACP4K,MAAO,CACL,CAAE/M,GAAI,cAAeC,MAAOP,EAAE,aAAc,2BAA4B+F,YAAa/F,EAAE,iBAAkB,4CACzG,CAAEM,GAAI,aAAcC,MAAOP,EAAE,YAAa,0BAA2B+F,YAAa/F,EAAE,gBAAiB,uCACrG,CAAEM,GAAI,UAAWC,MAAOP,EAAE,UAAW,yBAA0B+F,YAAa/F,EAAE,cAAe,qCAC7F,CAAEM,GAAI,eAAgBC,MAAOP,EAAE,eAAgB,gBAAiB+F,YAAa/F,EAAE,mBAAoB,sCAGvG6B,QAAS,CACPtB,MAAOP,EAAE,qBAAsB,uBAC/BQ,KAAM,qBACNiC,MAAO,kCACP4K,MAAO,CACL,CAAE/M,GAAI,qBAAsBC,MAAOP,EAAE,oBAAqB,sBAAuB+F,YAAa/F,EAAE,cAAe,yCAC/G,CAAEM,GAAI,wBAAyBC,MAAOP,EAAE,uBAAwB,yBAA0B+F,YAAa/F,EAAE,iBAAkB,oCAC3H,CAAEM,GAAI,iBAAkBC,MAAOP,EAAE,iBAAkB,4BAA6B+F,YAAa/F,EAAE,qBAAsB,4BACrH,CAAEM,GAAI,qBAAsBC,MAAOP,EAAE,oBAAqB,sBAAuB+F,YAAa/F,EAAE,wBAAyB,+BAG7HoK,cAAe,CACb7J,MAAOP,EAAE,gBAAiB,iBAC1BQ,KAAM,eACNiC,MAAO,gCACP4K,MAAO,CACL,CAAE/M,GAAI,oBAAqBC,MAAOP,EAAE,mBAAoB,qBAAsB+F,YAAa/F,EAAE,uBAAwB,8BACrH,CAAEM,GAAI,0BAA2BC,MAAOP,EAAE,yBAA0B,4BAA6B+F,YAAa/F,EAAE,gBAAiB,+BACjI,CAAEM,GAAI,YAAaC,MAAOP,EAAE,WAAY,oBAAqB+F,YAAa/F,EAAE,eAAgB,sCAC5F,CAAEM,GAAI,uBAAwBC,MAAOP,EAAE,sBAAuB,wBAAyB+F,YAAa/F,EAAE,iBAAkB,uCAG5H0P,WAAY,CACVnP,MAAOP,EAAE,aAAc,cACvBQ,KAAM,eACNiC,MAAO,kCACP4K,MAAO,CACL,CAAE/M,GAAI,kBAAmBC,MAAOP,EAAE,iBAAkB,mBAAoB+F,YAAa/F,EAAE,qBAAsB,oCAC7G,CAAEM,GAAI,kBAAmBC,MAAOP,EAAE,iBAAkB,uBAAwB+F,YAAa/F,EAAE,gBAAiB,+BAC5G,CAAEM,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkB+F,YAAa/F,EAAE,mBAAoB,4BACrG,CAAEM,GAAI,eAAgBC,MAAOP,EAAE,eAAgB,gBAAiB+F,YAAa/F,EAAE,mBAAoB,8BAKnGmT,EAAqB,CACzBC,OAAQ,CACN,CAAE9S,GAAI,kBAAmBkB,KAAMxB,EAAE,iBAAkB,mBAAoB+H,SAAU,GAAIsL,aAAc,MAAOtN,YAAa/F,EAAE,qBAAsB,iDAC/I,CAAEM,GAAI,iBAAkBkB,KAAMxB,EAAE,gBAAiB,kBAAmB+H,SAAU,EAAGsL,aAAc,UAAWtN,YAAa/F,EAAE,oBAAqB,iDAC9I,CAAEM,GAAI,mBAAoBkB,KAAMxB,EAAE,kBAAmB,0BAA2B+H,SAAU,GAAIsL,aAAc,MAAOtN,YAAa/F,EAAE,sBAAuB,6CACzJ,CAAEM,GAAI,iBAAkBkB,KAAMxB,EAAE,gBAAiB,kBAAmB+H,SAAU,GAAIsL,aAAc,MAAOtN,YAAa/F,EAAE,oBAAqB,sDAE7IsT,eAAgB,CACd,CAAEhT,GAAI,kBAAmBkB,KAAMxB,EAAE,gBAAiB,mBAAoB+H,SAAU,GAAIsL,aAAc,WAAYtN,YAAa/F,EAAE,UAAW,yCACxI,CAAEM,GAAI,oBAAqBkB,KAAMxB,EAAE,mBAAoB,qBAAsB+H,SAAU,GAAIsL,aAAc,WAAYtN,YAAa/F,EAAE,eAAgB,mCACpJ,CAAEM,GAAI,gBAAiBkB,KAAMxB,EAAE,eAAgB,iBAAkB+H,SAAU,GAAIsL,aAAc,OAAQtN,YAAa/F,EAAE,WAAY,kCAChI,CAAEM,GAAI,cAAekB,KAAMxB,EAAE,cAAe,eAAgB+H,SAAU,GAAIsL,aAAc,MAAOtN,YAAa/F,EAAE,kBAAmB,yCAEnIuT,cAAe,CACb,CAAEjT,GAAI,iBAAkBkB,KAAMxB,EAAE,gBAAiB,kBAAmB+H,SAAU,GAAIsL,aAAc,WAAYtN,YAAa/F,EAAE,oBAAqB,qCAChJ,CAAEM,GAAI,qBAAsBkB,KAAMxB,EAAE,oBAAqB,sBAAuB+H,SAAU,GAAIsL,aAAc,WAAYtN,YAAa/F,EAAE,wBAAyB,wCAChK,CAAEM,GAAI,kBAAmBkB,KAAMxB,EAAE,iBAAkB,yBAA0B+H,SAAU,GAAIsL,aAAc,WAAYtN,YAAa/F,EAAE,qBAAsB,qCAC1J,CAAEM,GAAI,oBAAqBkB,KAAMxB,EAAE,mBAAoB,qBAAsB+H,SAAU,GAAIsL,aAAc,MAAOtN,YAAa/F,EAAE,uBAAwB,mCAIrJwT,EAAuB,CAC3B,CAAElT,GAAI,aAAcC,MAAOP,EAAE,YAAa,cAAeQ,KAAM,SAAKuF,YAAa/F,EAAE,gBAAiB,wCACpG,CAAEM,GAAI,cAAeC,MAAOP,EAAE,aAAc,eAAgBQ,KAAM,qBAAOuF,YAAa/F,EAAE,iBAAkB,wCAC1G,CAAEM,GAAI,gBAAiBC,MAAOP,EAAE,eAAgB,iBAAkBQ,KAAM,eAAMuF,YAAa/F,EAAE,mBAAoB,0CACjH,CAAEM,GAAI,cAAeC,MAAOP,EAAE,aAAc,eAAgBQ,KAAM,eAAMuF,YAAa/F,EAAE,iBAAkB,uCACzG,CAAEM,GAAI,kBAAmBC,MAAOP,EAAE,iBAAkB,mBAAoBQ,KAAM,eAAMuF,YAAa/F,EAAE,qBAAsB,wCACzH,CAAEM,GAAI,0BAA2BC,MAAOP,EAAE,yBAA0B,2BAA4BQ,KAAM,eAAMuF,YAAa/F,EAAE,iBAAkB,mCAC7I,CAAEM,GAAI,eAAgBC,MAAOP,EAAE,cAAe,gBAAiBQ,KAAM,eAAMuF,YAAa/F,EAAE,kBAAmB,qCAC7G,CAAEM,GAAI,uBAAwBC,MAAOP,EAAE,sBAAuB,wBAAyBQ,KAAM,eAAMuF,YAAa/F,EAAE,oBAAqB,8CAGzIuG,EAAAA,EAAAA,WAAU,KACJ7B,GACF+O,EAAqB/O,IAEtB,CAACA,IAEJ,MAAM+O,EAAwBC,IAC5B,MAAMpS,EAAYoS,EAAQzS,iBACpB0S,EAAsBR,EAAmB7R,IAAc,GAGvDsS,EAAiBC,EAA0BvS,GAG3CwS,EAA0BC,EAAuBL,GAEvDX,EAAiBxP,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBlD,GAAI,IACPV,WAAY8Q,EACZtG,MAAOuG,EACPZ,eAAgBc,MAIdD,EAA6BvS,IACjB,CACd8R,OAAQ,CAAC,qBAAsB,uBAAwB,kBAAmB,cAC1EE,eAAgB,CAAC,cAAe,UAAW,eAAgB,gBAC3DC,cAAe,CAAC,iBAAkB,UAAW,kBAAmB,iBAChES,wBAAyB,CAAC,oBAAqB,eAAgB,gBAAiB,kBAGnE1S,IAAc,IAGzByS,EAA0BL,IAC9B,MAAMV,EAAiB,GAcvB,MAZ+B,oBAA3BU,EAAQnC,gBACVyB,EAAeiB,KAAK,gBAAiB,mBAER,qBAA3BP,EAAQnC,gBACVyB,EAAeiB,KAAK,cAAe,eAEF,eAA/BP,EAAQpC,oBACV0B,EAAeiB,KAAK,cAAe,wBAGrCjB,EAAeiB,KAAK,aAAc,2BAE3BjB,GAwCHpS,EAAO,CACX,CAAEN,GAAI,QAASC,MAAOP,EAAE,iBAAkB,mBAAoBQ,KAAM,gBACpE,CAAEF,GAAI,aAAcC,MAAOP,EAAE,aAAc,cAAeQ,KAAM,gBAChE,CAAEF,GAAI,iBAAkBC,MAAOP,EAAE,iBAAkB,kBAAmBQ,KAAM,sBAC5E,CAAEF,GAAI,WAAYC,MAAOP,EAAE,WAAY,YAAaQ,KAAM,gBAC1D,CAAEF,GAAI,WAAYC,MAAOP,EAAE,WAAY,YAAaQ,KAAM,iBAG5D,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qEAAoEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,iBACtBf,EAAE,wBAAyB,+BAE9BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+GAA8GC,SAC7G,OAAd2D,QAAc,IAAdA,GAAAA,EAAgBzD,iBAAmBjB,EAAE0E,EAAezD,kBAAoBjB,EAAE,UAAW,mBAM5FgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDH,EAAKM,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMjB,EAAagB,EAAIb,IAChCQ,UAAS,0FAAAO,OACPnB,IAAciB,EAAIb,GACd,mDACA,0HACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,QAAAD,SAAOI,EAAIX,QACXQ,EAAAA,EAAAA,KAAA,QAAAD,SAAOI,EAAIZ,UATNY,EAAIb,UAgBjBO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CACT,UAAdb,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvB+R,EAAczF,MAAM5J,OAAS,IAC5B5C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,eAAgB,oBAErBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB+R,EAAczF,MAAMnM,IAAK0N,IACxB/N,EAAAA,EAAAA,MAAA,OAAmBC,UAAU,6DAA4DC,SAAA,EACvFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAE6N,EAAKrO,SAChES,EAAAA,EAAAA,KAAA,UACEI,QAASA,KAAM8S,OA1EnBC,EA0E8BvF,EAAKtO,QAzErDyS,EAAiBxP,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBlD,GAAI,IACP8J,MAAO9J,EAAK8J,MAAMjJ,OAAOgQ,GAAKA,EAAE9T,KAAO6T,MAHvBA,OA2EIrT,UAAU,0CAAyCC,SACpD,eAIHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SAAE6N,EAAK7I,eACnElF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kFAAiFC,SAAA,EAC9FC,EAAAA,EAAAA,KAAA,QAAAD,SAAOf,EAAE,WAAY,eACrBa,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAO6N,EAAKhB,SAAS,WAEvB5M,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,2DACVyG,MAAO,CAAE4F,MAAM,GAAD9L,OAAKuN,EAAKhB,SAAQ,cAItC5M,EAAAA,EAAAA,KAAA,SACEO,KAAK,QACLsF,IAAI,IACJC,IAAI,MACJrF,MAAOmN,EAAKhB,SACZ7G,SAAWC,IAAMqN,OA5FbF,EA4FgCvF,EAAKtO,GA5F7BsN,EA4FiC3G,SAASD,EAAEE,OAAOzF,YA3FrFsR,EAAiBxP,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBlD,GAAI,IACP8J,MAAO9J,EAAK8J,MAAMnM,IAAIkT,GACpBA,EAAE9T,KAAO6T,GAAM1N,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQ2N,GAAC,IAAExG,aAAawG,MAJlBC,IAACF,EAAQvG,GA6FZ9M,UAAU,cA9BN8N,EAAKtO,WAwCvBO,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,cAAe,oBAEpBgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBsD,OAAOC,QAAQ4O,GAAgBhS,IAAIqD,IAAA,IAAE+P,EAAYjR,GAASkB,EAAA,OACzD1D,EAAAA,EAAAA,MAAA,OAAsBC,UAAS,yCAAAO,OAA2CgC,EAASZ,OAAQ1B,SAAA,EACzFF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAEsC,EAAS7C,OAChC6C,EAAS9C,UAEZS,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDsC,EAASgK,MAAMnM,IAAK0N,IACnB/N,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAlJjBmT,EAACD,EAAYH,KAC3B,MACMvF,EADWsE,EAAeoB,GACVjH,MAAMmH,KAAKJ,GAAKA,EAAE9T,KAAO6T,GAE/C,GAAIvF,IAASkE,EAAczF,MAAMmH,KAAKJ,GAAKA,EAAE9T,KAAO6T,GAAS,CAC3D,MAAMM,GAAOhO,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRmI,GAAI,IACP0F,aACAI,WAAY,IAAIvR,KAAKA,KAAKC,MAAQ,QAClCuR,SAAU,SACV/G,SAAU,EACVC,WAAY,KAGdkF,EAAiBxP,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBlD,GAAI,IACP8J,MAAO,IAAI9J,EAAK8J,MAAOoH,KAE3B,GAgIqCF,CAAQD,EAAY1F,EAAKtO,IACxC+I,SAAUyJ,EAAczF,MAAMmH,KAAKJ,GAAKA,EAAE9T,KAAOsO,EAAKtO,IACtDQ,UAAS,qDAAAO,OACPyR,EAAczF,MAAMmH,KAAKJ,GAAKA,EAAE9T,KAAOsO,EAAKtO,IACxC,+DACA,mEACHS,SAAA,EAEHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oCAAmCC,SAAE6N,EAAKrO,SACzDS,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6BAA4BC,SAAE6N,EAAK7I,gBAV7C6I,EAAKtO,SARRgU,aA6BL,eAAdpU,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,wBAAyB,6BAE9BgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnD+R,EAAcjQ,WAAW3B,IAAKqL,IAC7B1L,EAAAA,EAAAA,MAAA,OAAuBC,UAAU,6DAA4DC,SAAA,EAC3FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEwL,EAAS/K,QACpEX,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wBAAuBC,SAAA,CAAEwL,EAASxE,SAAS,cAE7D/G,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SAAEwL,EAASxG,eACvElF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,yCAAAO,OACa,QAA1BkL,EAAS8G,aAAyB,8BACR,aAA1B9G,EAAS8G,aAA8B,gCACb,SAA1B9G,EAAS8G,aAA0B,0BACnC,6BACCtS,SAAA,CACAf,EAAEuM,EAAS8G,aAAc9G,EAAS8G,cAAc,IAAErT,EAAE,UAAW,eAElEgB,EAAAA,EAAAA,KAAA,UAAQF,UAAU,wDAAuDC,SACtEf,EAAE,eAAgB,yBAhBfuM,EAASjM,UAyBZ,mBAAdJ,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEf,EAAE,4BAA6B,iCAElCgB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEyS,EAAqBtS,IAAK0T,IACzB5T,EAAAA,EAAAA,KAAA,OAEEF,UAAS,yDAAAO,OACPyR,EAAcE,eAAelK,SAAS8L,EAActU,IAChD,iDACA,8DAENc,QAASA,KACP,MAAMyT,EAAa/B,EAAcE,eAAelK,SAAS8L,EAActU,IACvEyS,EAAiBxP,IAAIkD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBlD,GAAI,IACPyP,eAAgB6B,EACZtR,EAAKyP,eAAe5O,OAAOyK,GAAKA,IAAM+F,EAActU,IACpD,IAAIiD,EAAKyP,eAAgB4B,EAActU,QAE7CS,UAEFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SAAE6T,EAAcpU,QAC9CQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,SAAE6T,EAAcrU,SAC/ES,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAE6T,EAAc7O,kBAnBtE6O,EAActU,WA2Bb,aAAdJ,GAA0C,aAAdA,KAC5BW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,SACb,aAAdb,EAA2B,eAAO,kBAErCc,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACvD,aAAdb,EAA2BF,EAAE,kBAAmB,oBAAsBA,EAAE,mBAAoB,wBAE/FgB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC9B,aAAdb,EACGF,EAAE,qBAAsB,0CACxBA,EAAE,qBAAsB,sDAQpCa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMqS,EAAqB/O,GACpC5D,UAAU,wIAAuIC,SAEhJf,EAAE,iBAAkB,sBAEvBgB,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAkB,OAAZqR,QAAY,IAAZA,OAAY,EAAZA,EAAeK,GAC9BhS,UAAU,wIAAuIC,SAEhJf,EAAE,oBAAqB,+B", "sources": ["components/SpecialNeeds/PatientProfile.jsx", "components/SpecialNeeds/CommunicationBoard.jsx", "components/SpecialNeeds/SensoryControls.jsx", "components/SpecialNeeds/BehavioralTracking.jsx", "components/SpecialNeeds/VisualSchedule.jsx", "components/SpecialNeeds/ProgressVisualization.jsx", "components/SpecialNeeds/EmergencyProtocols.jsx", "pages/SpecialNeeds/SpecialNeedsDashboard.jsx", "components/SpecialNeeds/AdaptiveTreatmentPlan.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SpecialNeedsPatientProfile = ({ patient, onUpdate }) => {\n  const { t } = useLanguage();\n  const [activeTab, setActiveTab] = useState('diagnosis');\n\n  const diagnosisTypes = [\n    { id: 'autism', label: t('autism', 'Autism Spectrum Disorder'), icon: '🧩' },\n    { id: 'cerebral_palsy', label: t('cerebralPalsy', 'Cerebral Palsy'), icon: '🦽' },\n    { id: 'down_syndrome', label: t('downSyndrome', 'Down Syndrome'), icon: '💙' },\n    { id: 'intellectual_disability', label: t('intellectualDisability', 'Intellectual Disability'), icon: '🧠' },\n    { id: 'spina_bifida', label: t('spinaBifida', 'Spina Bifida'), icon: '🦴' },\n    { id: 'muscular_dystrophy', label: t('muscularDystrophy', 'Muscular Dystrophy'), icon: '💪' },\n    { id: 'adhd', label: t('adhd', 'ADHD'), icon: '⚡' },\n    { id: 'sensory_processing', label: t('sensoryProcessing', 'Sensory Processing Disorder'), icon: '👁️' }\n  ];\n\n  const communicationMethods = [\n    { id: 'verbal', label: t('verbal', 'Verbal Communication'), icon: '🗣️' },\n    { id: 'sign_language', label: t('signLanguage', 'Sign Language'), icon: '👋' },\n    { id: 'picture_cards', label: t('pictureCards', 'Picture Cards/PECS'), icon: '🖼️' },\n    { id: 'aac_device', label: t('aacDevice', 'AAC Device'), icon: '📱' },\n    { id: 'gestures', label: t('gestures', 'Gestures/Body Language'), icon: '👐' },\n    { id: 'written', label: t('written', 'Written Communication'), icon: '✍️' }\n  ];\n\n  const sensoryPreferences = [\n    { id: 'visual', label: t('visualStimuli', 'Visual Stimuli'), options: ['low', 'moderate', 'high', 'avoid'] },\n    { id: 'auditory', label: t('auditoryStimuli', 'Auditory Stimuli'), options: ['quiet', 'soft', 'moderate', 'avoid_loud'] },\n    { id: 'tactile', label: t('tactileInput', 'Tactile Input'), options: ['light_touch', 'firm_pressure', 'avoid_touch', 'seeks_input'] },\n    { id: 'vestibular', label: t('vestibularInput', 'Vestibular Input'), options: ['slow_movement', 'fast_movement', 'avoid_movement', 'seeks_movement'] },\n    { id: 'proprioceptive', label: t('proprioceptiveInput', 'Proprioceptive Input'), options: ['light', 'moderate', 'heavy', 'seeks_input'] }\n  ];\n\n  const tabs = [\n    { id: 'diagnosis', label: t('diagnosis', 'Diagnosis & Conditions'), icon: '🏥' },\n    { id: 'communication', label: t('communication', 'Communication'), icon: '💬' },\n    { id: 'sensory', label: t('sensoryProfile', 'Sensory Profile'), icon: '👁️' },\n    { id: 'behavioral', label: t('behavioral', 'Behavioral Patterns'), icon: '📊' },\n    { id: 'medical', label: t('medicalInfo', 'Medical Information'), icon: '⚕️' },\n    { id: 'family', label: t('familyInfo', 'Family & Caregivers'), icon: '👨‍👩‍👧‍👦' }\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n          {t('specialNeedsProfile', 'Special Needs Patient Profile')}\n        </h2>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium\">\n            {patient?.primaryDiagnosis || t('unspecified', 'Unspecified')}\n          </span>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-8 overflow-x-auto\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <span>{tab.icon}</span>\n              <span>{tab.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"space-y-6\">\n        {activeTab === 'diagnosis' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('primarySecondaryDiagnoses', 'Primary & Secondary Diagnoses')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {diagnosisTypes.map((diagnosis) => (\n                <div\n                  key={diagnosis.id}\n                  className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-2xl\">{diagnosis.icon}</span>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">{diagnosis.label}</h4>\n                      <div className=\"flex items-center space-x-2 mt-2\">\n                        <label className=\"flex items-center\">\n                          <input\n                            type=\"radio\"\n                            name=\"primaryDiagnosis\"\n                            value={diagnosis.id}\n                            className=\"mr-2\"\n                          />\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('primary', 'Primary')}</span>\n                        </label>\n                        <label className=\"flex items-center\">\n                          <input\n                            type=\"checkbox\"\n                            name=\"secondaryDiagnoses\"\n                            value={diagnosis.id}\n                            className=\"mr-2\"\n                          />\n                          <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('secondary', 'Secondary')}</span>\n                        </label>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'communication' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('communicationMethods', 'Communication Methods & Preferences')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {communicationMethods.map((method) => (\n                <div\n                  key={method.id}\n                  className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\"\n                >\n                  <div className=\"flex items-center space-x-3 mb-3\">\n                    <span className=\"text-xl\">{method.icon}</span>\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">{method.label}</h4>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <label className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('canUse', 'Can Use')}</span>\n                    </label>\n                    <label className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('preferred', 'Preferred Method')}</span>\n                    </label>\n                    <label className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('needsSupport', 'Needs Support')}</span>\n                    </label>\n                  </div>\n                </div>\n              ))}\n            </div>\n            \n            <div className=\"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\n              <h4 className=\"font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n                {t('communicationNotes', 'Communication Notes & Strategies')}\n              </h4>\n              <textarea\n                className=\"w-full p-3 border border-yellow-200 dark:border-yellow-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                rows=\"4\"\n                placeholder={t('communicationNotesPlaceholder', 'Enter specific communication strategies, triggers to avoid, successful approaches, etc.')}\n              />\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'sensory' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('sensoryProcessingProfile', 'Sensory Processing Profile')}\n            </h3>\n            <div className=\"space-y-6\">\n              {sensoryPreferences.map((sensory) => (\n                <div key={sensory.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white mb-3\">{sensory.label}</h4>\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n                    {sensory.options.map((option) => (\n                      <label key={option} className=\"flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\">\n                        <input\n                          type=\"radio\"\n                          name={`sensory_${sensory.id}`}\n                          value={option}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                          {t(option.replace('_', ''), option.replace('_', ' '))}\n                        </span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n            \n            <div className=\"mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n              <h4 className=\"font-medium text-green-800 dark:text-green-200 mb-2\">\n                {t('sensoryStrategies', 'Sensory Strategies & Accommodations')}\n              </h4>\n              <textarea\n                className=\"w-full p-3 border border-green-200 dark:border-green-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                rows=\"4\"\n                placeholder={t('sensoryStrategiesPlaceholder', 'Enter sensory breaks needed, calming strategies, environmental modifications, etc.')}\n              />\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'behavioral' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('behavioralPatterns', 'Behavioral Patterns & Management')}\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-3 flex items-center\">\n                  <span className=\"mr-2\">⚠️</span>\n                  {t('behavioralTriggers', 'Behavioral Triggers')}\n                </h4>\n                <div className=\"space-y-2\">\n                  {['loud_noises', 'bright_lights', 'unexpected_changes', 'crowded_spaces', 'physical_contact', 'waiting'].map((trigger) => (\n                    <label key={trigger} className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                        {t(trigger, trigger.replace('_', ' '))}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-3 flex items-center\">\n                  <span className=\"mr-2\">✅</span>\n                  {t('calmingStrategies', 'Calming Strategies')}\n                </h4>\n                <div className=\"space-y-2\">\n                  {['deep_breathing', 'fidget_toys', 'quiet_space', 'music', 'movement_breaks', 'visual_schedules'].map((strategy) => (\n                    <label key={strategy} className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-2\" />\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                        {t(strategy, strategy.replace('_', ' '))}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Save Button */}\n      <div className=\"mt-8 flex justify-end\">\n        <button\n          onClick={() => onUpdate?.(patient)}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n        >\n          {t('saveProfile', 'Save Profile')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default SpecialNeedsPatientProfile;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst CommunicationBoard = ({ onMessageSelect }) => {\n  const { t } = useLanguage();\n  const [selectedCategory, setSelectedCategory] = useState('feelings');\n  const [selectedMessages, setSelectedMessages] = useState([]);\n\n  const communicationCategories = {\n    feelings: {\n      label: t('feelings', 'Feelings'),\n      icon: '😊',\n      color: 'bg-yellow-100 border-yellow-300 text-yellow-800',\n      items: [\n        { id: 'happy', text: t('happy', 'Happy'), icon: '😊', color: 'bg-yellow-200' },\n        { id: 'sad', text: t('sad', 'Sad'), icon: '😢', color: 'bg-blue-200' },\n        { id: 'angry', text: t('angry', 'Angry'), icon: '😠', color: 'bg-red-200' },\n        { id: 'scared', text: t('scared', 'Scared'), icon: '😨', color: 'bg-purple-200' },\n        { id: 'tired', text: t('tired', 'Tired'), icon: '😴', color: 'bg-gray-200' },\n        { id: 'excited', text: t('excited', 'Excited'), icon: '🤩', color: 'bg-orange-200' },\n        { id: 'confused', text: t('confused', 'Confused'), icon: '😕', color: 'bg-indigo-200' },\n        { id: 'calm', text: t('calm', 'Calm'), icon: '😌', color: 'bg-green-200' }\n      ]\n    },\n    needs: {\n      label: t('needs', 'Needs'),\n      icon: '🙋',\n      color: 'bg-blue-100 border-blue-300 text-blue-800',\n      items: [\n        { id: 'water', text: t('water', 'Water'), icon: '💧', color: 'bg-blue-200' },\n        { id: 'food', text: t('food', 'Food'), icon: '🍎', color: 'bg-red-200' },\n        { id: 'bathroom', text: t('bathroom', 'Bathroom'), icon: '🚻', color: 'bg-gray-200' },\n        { id: 'break', text: t('break', 'Break'), icon: '⏸️', color: 'bg-yellow-200' },\n        { id: 'help', text: t('help', 'Help'), icon: '🆘', color: 'bg-red-200' },\n        { id: 'quiet', text: t('quiet', 'Quiet'), icon: '🤫', color: 'bg-purple-200' },\n        { id: 'move', text: t('move', 'Move'), icon: '🏃', color: 'bg-green-200' },\n        { id: 'stop', text: t('stop', 'Stop'), icon: '✋', color: 'bg-red-200' }\n      ]\n    },\n    activities: {\n      label: t('activities', 'Activities'),\n      icon: '🎯',\n      color: 'bg-green-100 border-green-300 text-green-800',\n      items: [\n        { id: 'exercise', text: t('exercise', 'Exercise'), icon: '🏋️', color: 'bg-green-200' },\n        { id: 'walk', text: t('walk', 'Walk'), icon: '🚶', color: 'bg-blue-200' },\n        { id: 'stretch', text: t('stretch', 'Stretch'), icon: '🤸', color: 'bg-purple-200' },\n        { id: 'balance', text: t('balance', 'Balance'), icon: '⚖️', color: 'bg-yellow-200' },\n        { id: 'play', text: t('play', 'Play'), icon: '🎮', color: 'bg-pink-200' },\n        { id: 'music', text: t('music', 'Music'), icon: '🎵', color: 'bg-indigo-200' },\n        { id: 'draw', text: t('draw', 'Draw'), icon: '🎨', color: 'bg-orange-200' },\n        { id: 'read', text: t('read', 'Read'), icon: '📚', color: 'bg-brown-200' }\n      ]\n    },\n    pain: {\n      label: t('pain', 'Pain/Discomfort'),\n      icon: '🤕',\n      color: 'bg-red-100 border-red-300 text-red-800',\n      items: [\n        { id: 'head_pain', text: t('headPain', 'Head Hurts'), icon: '🤕', color: 'bg-red-200' },\n        { id: 'back_pain', text: t('backPain', 'Back Hurts'), icon: '🫸', color: 'bg-red-200' },\n        { id: 'leg_pain', text: t('legPain', 'Leg Hurts'), icon: '🦵', color: 'bg-red-200' },\n        { id: 'arm_pain', text: t('armPain', 'Arm Hurts'), icon: '💪', color: 'bg-red-200' },\n        { id: 'stomach_pain', text: t('stomachPain', 'Stomach Hurts'), icon: '🤰', color: 'bg-red-200' },\n        { id: 'no_pain', text: t('noPain', 'No Pain'), icon: '✅', color: 'bg-green-200' },\n        { id: 'little_pain', text: t('littlePain', 'Little Pain'), icon: '😐', color: 'bg-yellow-200' },\n        { id: 'big_pain', text: t('bigPain', 'Big Pain'), icon: '😣', color: 'bg-red-300' }\n      ]\n    },\n    responses: {\n      label: t('responses', 'Yes/No/More'),\n      icon: '✅',\n      color: 'bg-purple-100 border-purple-300 text-purple-800',\n      items: [\n        { id: 'yes', text: t('yes', 'Yes'), icon: '✅', color: 'bg-green-200' },\n        { id: 'no', text: t('no', 'No'), icon: '❌', color: 'bg-red-200' },\n        { id: 'maybe', text: t('maybe', 'Maybe'), icon: '🤔', color: 'bg-yellow-200' },\n        { id: 'more', text: t('more', 'More'), icon: '➕', color: 'bg-blue-200' },\n        { id: 'less', text: t('less', 'Less'), icon: '➖', color: 'bg-orange-200' },\n        { id: 'finished', text: t('finished', 'Finished'), icon: '✔️', color: 'bg-green-200' },\n        { id: 'again', text: t('again', 'Again'), icon: '🔄', color: 'bg-purple-200' },\n        { id: 'different', text: t('different', 'Different'), icon: '🔀', color: 'bg-indigo-200' }\n      ]\n    }\n  };\n\n  const handleItemSelect = (item) => {\n    const newMessage = {\n      id: Date.now(),\n      text: item.text,\n      icon: item.icon,\n      category: selectedCategory,\n      timestamp: new Date()\n    };\n    \n    setSelectedMessages(prev => [...prev, newMessage]);\n    onMessageSelect?.(newMessage);\n  };\n\n  const clearMessages = () => {\n    setSelectedMessages([]);\n  };\n\n  const speakMessage = (text) => {\n    if ('speechSynthesis' in window) {\n      const utterance = new SpeechSynthesisUtterance(text);\n      utterance.lang = 'en-US'; // Could be dynamic based on language context\n      speechSynthesis.speak(utterance);\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <span className=\"mr-3\">💬</span>\n          {t('communicationBoard', 'Communication Board')}\n        </h2>\n        <button\n          onClick={clearMessages}\n          className=\"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n        >\n          {t('clear', 'Clear')}\n        </button>\n      </div>\n\n      {/* Selected Messages Display */}\n      {selectedMessages.length > 0 && (\n        <div className=\"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"font-medium text-gray-900 dark:text-white\">\n              {t('selectedMessages', 'Selected Messages')}\n            </h3>\n            <button\n              onClick={() => speakMessage(selectedMessages.map(m => m.text).join(', '))}\n              className=\"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\"\n            >\n              🔊 {t('speak', 'Speak')}\n            </button>\n          </div>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedMessages.map((message) => (\n              <div\n                key={message.id}\n                className=\"flex items-center space-x-2 px-3 py-2 bg-white dark:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-500\"\n              >\n                <span className=\"text-lg\">{message.icon}</span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{message.text}</span>\n                <button\n                  onClick={() => setSelectedMessages(prev => prev.filter(m => m.id !== message.id))}\n                  className=\"text-red-500 hover:text-red-700 text-xs\"\n                >\n                  ✕\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Category Tabs */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-4 overflow-x-auto\">\n          {Object.entries(communicationCategories).map(([key, category]) => (\n            <button\n              key={key}\n              onClick={() => setSelectedCategory(key)}\n              className={`whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${\n                selectedCategory === key\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"text-lg\">{category.icon}</span>\n              <span>{category.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Communication Items Grid */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4\">\n        {communicationCategories[selectedCategory].items.map((item) => (\n          <button\n            key={item.id}\n            onClick={() => handleItemSelect(item)}\n            className={`p-6 rounded-lg border-2 border-dashed transition-all duration-200 hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${item.color} hover:border-solid`}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-4xl mb-2\">{item.icon}</div>\n              <div className=\"text-sm font-medium text-gray-800 dark:text-gray-900\">\n                {item.text}\n              </div>\n            </div>\n          </button>\n        ))}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"mt-6 flex justify-center space-x-4\">\n        <button\n          onClick={() => handleItemSelect({ text: t('iAmReady', 'I am ready'), icon: '👍', color: 'bg-green-200' })}\n          className=\"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center space-x-2\"\n        >\n          <span>👍</span>\n          <span>{t('iAmReady', 'I am ready')}</span>\n        </button>\n        \n        <button\n          onClick={() => handleItemSelect({ text: t('iNeedHelp', 'I need help'), icon: '🆘', color: 'bg-red-200' })}\n          className=\"px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center space-x-2\"\n        >\n          <span>🆘</span>\n          <span>{t('iNeedHelp', 'I need help')}</span>\n        </button>\n        \n        <button\n          onClick={() => handleItemSelect({ text: t('allDone', 'All done'), icon: '✅', color: 'bg-blue-200' })}\n          className=\"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2\"\n        >\n          <span>✅</span>\n          <span>{t('allDone', 'All done')}</span>\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CommunicationBoard;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SensoryEnvironmentControls = ({ patientProfile, onEnvironmentChange }) => {\n  const { t } = useLanguage();\n  const [environment, setEnvironment] = useState({\n    lighting: {\n      brightness: 50,\n      color: 'warm',\n      flashing: false\n    },\n    sound: {\n      volume: 30,\n      type: 'nature',\n      enabled: false\n    },\n    visual: {\n      animations: false,\n      contrast: 'normal',\n      colorScheme: 'default'\n    },\n    alerts: {\n      visual: true,\n      audio: false,\n      vibration: false\n    }\n  });\n\n  const [activePreset, setActivePreset] = useState('custom');\n\n  const sensoryPresets = {\n    autism_friendly: {\n      name: t('autismFriendly', 'Autism Friendly'),\n      icon: '🧩',\n      description: t('autismFriendlyDesc', 'Low stimulation environment'),\n      settings: {\n        lighting: { brightness: 30, color: 'cool', flashing: false },\n        sound: { volume: 10, type: 'silence', enabled: false },\n        visual: { animations: false, contrast: 'high', colorScheme: 'calm' },\n        alerts: { visual: true, audio: false, vibration: false }\n      }\n    },\n    sensory_seeking: {\n      name: t('sensorySeeking', 'Sensory Seeking'),\n      icon: '🌈',\n      description: t('sensorySeekingDesc', 'Enhanced sensory input'),\n      settings: {\n        lighting: { brightness: 70, color: 'dynamic', flashing: false },\n        sound: { volume: 50, type: 'upbeat', enabled: true },\n        visual: { animations: true, contrast: 'normal', colorScheme: 'vibrant' },\n        alerts: { visual: true, audio: true, vibration: true }\n      }\n    },\n    calming: {\n      name: t('calming', 'Calming'),\n      icon: '🕯️',\n      description: t('calmingDesc', 'Soothing environment for relaxation'),\n      settings: {\n        lighting: { brightness: 20, color: 'warm', flashing: false },\n        sound: { volume: 25, type: 'nature', enabled: true },\n        visual: { animations: false, contrast: 'low', colorScheme: 'soft' },\n        alerts: { visual: true, audio: false, vibration: false }\n      }\n    },\n    focus: {\n      name: t('focus', 'Focus'),\n      icon: '🎯',\n      description: t('focusDesc', 'Minimal distractions for concentration'),\n      settings: {\n        lighting: { brightness: 60, color: 'neutral', flashing: false },\n        sound: { volume: 0, type: 'silence', enabled: false },\n        visual: { animations: false, contrast: 'high', colorScheme: 'minimal' },\n        alerts: { visual: true, audio: false, vibration: false }\n      }\n    }\n  };\n\n  const soundOptions = [\n    { id: 'silence', name: t('silence', 'Silence'), icon: '🔇' },\n    { id: 'nature', name: t('natureSounds', 'Nature Sounds'), icon: '🌿' },\n    { id: 'white_noise', name: t('whiteNoise', 'White Noise'), icon: '📻' },\n    { id: 'classical', name: t('classical', 'Classical Music'), icon: '🎼' },\n    { id: 'upbeat', name: t('upbeat', 'Upbeat Music'), icon: '🎵' }\n  ];\n\n  const colorSchemes = [\n    { id: 'default', name: t('default', 'Default'), colors: ['#3B82F6', '#10B981', '#F59E0B'] },\n    { id: 'calm', name: t('calm', 'Calm'), colors: ['#6B7280', '#9CA3AF', '#D1D5DB'] },\n    { id: 'vibrant', name: t('vibrant', 'Vibrant'), colors: ['#EF4444', '#F97316', '#EAB308'] },\n    { id: 'soft', name: t('soft', 'Soft'), colors: ['#F3E8FF', '#FEF3C7', '#ECFDF5'] },\n    { id: 'minimal', name: t('minimal', 'Minimal'), colors: ['#000000', '#FFFFFF', '#6B7280'] }\n  ];\n\n  useEffect(() => {\n    onEnvironmentChange?.(environment);\n  }, [environment, onEnvironmentChange]);\n\n  const applyPreset = (presetKey) => {\n    const preset = sensoryPresets[presetKey];\n    if (preset) {\n      setEnvironment(preset.settings);\n      setActivePreset(presetKey);\n    }\n  };\n\n  const updateEnvironment = (category, key, value) => {\n    setEnvironment(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n    setActivePreset('custom');\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <span className=\"mr-3\">🎛️</span>\n          {t('sensoryEnvironmentControls', 'Sensory Environment Controls')}\n        </h2>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm font-medium\">\n            {sensoryPresets[activePreset]?.name || t('custom', 'Custom')}\n          </span>\n        </div>\n      </div>\n\n      {/* Quick Presets */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('quickPresets', 'Quick Presets')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {Object.entries(sensoryPresets).map(([key, preset]) => (\n            <button\n              key={key}\n              onClick={() => applyPreset(key)}\n              className={`p-4 rounded-lg border-2 transition-all duration-200 hover:scale-105 ${\n                activePreset === key\n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                  : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n              }`}\n            >\n              <div className=\"text-center\">\n                <div className=\"text-3xl mb-2\">{preset.icon}</div>\n                <div className=\"font-medium text-gray-900 dark:text-white mb-1\">{preset.name}</div>\n                <div className=\"text-xs text-gray-600 dark:text-gray-400\">{preset.description}</div>\n              </div>\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Detailed Controls */}\n      <div className=\"space-y-8\">\n        {/* Lighting Controls */}\n        <div className=\"p-6 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n            <span className=\"mr-2\">💡</span>\n            {t('lightingControls', 'Lighting Controls')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('brightness', 'Brightness')}\n              </label>\n              <input\n                type=\"range\"\n                min=\"0\"\n                max=\"100\"\n                value={environment.lighting.brightness}\n                onChange={(e) => updateEnvironment('lighting', 'brightness', parseInt(e.target.value))}\n                className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n              />\n              <div className=\"text-center text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                {environment.lighting.brightness}%\n              </div>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('colorTemperature', 'Color Temperature')}\n              </label>\n              <select\n                value={environment.lighting.color}\n                onChange={(e) => updateEnvironment('lighting', 'color', e.target.value)}\n                className=\"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              >\n                <option value=\"warm\">{t('warm', 'Warm')}</option>\n                <option value=\"neutral\">{t('neutral', 'Neutral')}</option>\n                <option value=\"cool\">{t('cool', 'Cool')}</option>\n                <option value=\"dynamic\">{t('dynamic', 'Dynamic')}</option>\n              </select>\n            </div>\n            \n            <div>\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={environment.lighting.flashing}\n                  onChange={(e) => updateEnvironment('lighting', 'flashing', e.target.checked)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {t('allowFlashing', 'Allow Flashing')}\n                </span>\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* Sound Controls */}\n        <div className=\"p-6 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n            <span className=\"mr-2\">🔊</span>\n            {t('soundControls', 'Sound Controls')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"flex items-center mb-4\">\n                <input\n                  type=\"checkbox\"\n                  checked={environment.sound.enabled}\n                  onChange={(e) => updateEnvironment('sound', 'enabled', e.target.checked)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {t('enableSound', 'Enable Sound')}\n                </span>\n              </label>\n              \n              {environment.sound.enabled && (\n                <>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('volume', 'Volume')}\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0\"\n                    max=\"100\"\n                    value={environment.sound.volume}\n                    onChange={(e) => updateEnvironment('sound', 'volume', parseInt(e.target.value))}\n                    className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n                  />\n                  <div className=\"text-center text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                    {environment.sound.volume}%\n                  </div>\n                </>\n              )}\n            </div>\n            \n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('soundType', 'Sound Type')}\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n                {soundOptions.map((option) => (\n                  <button\n                    key={option.id}\n                    onClick={() => updateEnvironment('sound', 'type', option.id)}\n                    className={`p-3 rounded-lg border text-center transition-colors ${\n                      environment.sound.type === option.id\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"text-lg mb-1\">{option.icon}</div>\n                    <div className=\"text-xs font-medium\">{option.name}</div>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Visual Controls */}\n        <div className=\"p-6 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center\">\n            <span className=\"mr-2\">👁️</span>\n            {t('visualControls', 'Visual Controls')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"flex items-center mb-4\">\n                <input\n                  type=\"checkbox\"\n                  checked={environment.visual.animations}\n                  onChange={(e) => updateEnvironment('visual', 'animations', e.target.checked)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {t('enableAnimations', 'Enable Animations')}\n                </span>\n              </label>\n              \n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('contrast', 'Contrast')}\n              </label>\n              <select\n                value={environment.visual.contrast}\n                onChange={(e) => updateEnvironment('visual', 'contrast', e.target.value)}\n                className=\"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              >\n                <option value=\"low\">{t('low', 'Low')}</option>\n                <option value=\"normal\">{t('normal', 'Normal')}</option>\n                <option value=\"high\">{t('high', 'High')}</option>\n              </select>\n            </div>\n            \n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('colorScheme', 'Color Scheme')}\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n                {colorSchemes.map((scheme) => (\n                  <button\n                    key={scheme.id}\n                    onClick={() => updateEnvironment('visual', 'colorScheme', scheme.id)}\n                    className={`p-3 rounded-lg border text-center transition-colors ${\n                      environment.visual.colorScheme === scheme.id\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"flex justify-center space-x-1 mb-2\">\n                      {scheme.colors.map((color, index) => (\n                        <div\n                          key={index}\n                          className=\"w-4 h-4 rounded-full\"\n                          style={{ backgroundColor: color }}\n                        />\n                      ))}\n                    </div>\n                    <div className=\"text-xs font-medium text-gray-900 dark:text-white\">{scheme.name}</div>\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Apply Button */}\n      <div className=\"mt-8 flex justify-end space-x-4\">\n        <button\n          onClick={() => setEnvironment({\n            lighting: { brightness: 50, color: 'warm', flashing: false },\n            sound: { volume: 30, type: 'nature', enabled: false },\n            visual: { animations: false, contrast: 'normal', colorScheme: 'default' },\n            alerts: { visual: true, audio: false, vibration: false }\n          })}\n          className=\"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n        >\n          {t('reset', 'Reset')}\n        </button>\n        <button\n          onClick={() => onEnvironmentChange?.(environment)}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n        >\n          {t('applySettings', 'Apply Settings')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default SensoryEnvironmentControls;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst BehavioralTracking = ({ patientId, onBehaviorLog }) => {\n  const { t } = useLanguage();\n  const [activeTab, setActiveTab] = useState('log');\n  const [behaviorLog, setBehaviorLog] = useState({\n    behavior: '',\n    intensity: 3,\n    duration: '',\n    triggers: [],\n    interventions: [],\n    outcome: '',\n    notes: ''\n  });\n\n  const behaviorTypes = [\n    { id: 'self_stimming', label: t('selfStimming', 'Self-Stimming'), icon: '🔄', color: 'bg-blue-100' },\n    { id: 'aggression', label: t('aggression', 'Aggression'), icon: '😠', color: 'bg-red-100' },\n    { id: 'withdrawal', label: t('withdrawal', 'Withdrawal'), icon: '😔', color: 'bg-gray-100' },\n    { id: 'anxiety', label: t('anxiety', 'Anxiety'), icon: '😰', color: 'bg-yellow-100' },\n    { id: 'meltdown', label: t('meltdown', 'Meltdown'), icon: '😭', color: 'bg-orange-100' },\n    { id: 'cooperation', label: t('cooperation', 'Cooperation'), icon: '🤝', color: 'bg-green-100' },\n    { id: 'communication', label: t('communication', 'Communication'), icon: '💬', color: 'bg-purple-100' },\n    { id: 'focus', label: t('focus', 'Focus/Attention'), icon: '🎯', color: 'bg-indigo-100' }\n  ];\n\n  const commonTriggers = [\n    { id: 'loud_noise', label: t('loudNoise', 'Loud Noise'), icon: '🔊' },\n    { id: 'bright_lights', label: t('brightLights', 'Bright Lights'), icon: '💡' },\n    { id: 'crowded_space', label: t('crowdedSpace', 'Crowded Space'), icon: '👥' },\n    { id: 'unexpected_change', label: t('unexpectedChange', 'Unexpected Change'), icon: '🔄' },\n    { id: 'physical_contact', label: t('physicalContact', 'Physical Contact'), icon: '🤚' },\n    { id: 'waiting', label: t('waiting', 'Waiting'), icon: '⏰' },\n    { id: 'new_person', label: t('newPerson', 'New Person'), icon: '👤' },\n    { id: 'hunger_thirst', label: t('hungerThirst', 'Hunger/Thirst'), icon: '🍎' }\n  ];\n\n  const interventions = [\n    { id: 'deep_breathing', label: t('deepBreathing', 'Deep Breathing'), icon: '🫁' },\n    { id: 'sensory_break', label: t('sensoryBreak', 'Sensory Break'), icon: '⏸️' },\n    { id: 'visual_schedule', label: t('visualSchedule', 'Visual Schedule'), icon: '📅' },\n    { id: 'fidget_toy', label: t('fidgetToy', 'Fidget Toy'), icon: '🧸' },\n    { id: 'quiet_space', label: t('quietSpace', 'Quiet Space'), icon: '🤫' },\n    { id: 'preferred_activity', label: t('preferredActivity', 'Preferred Activity'), icon: '🎮' },\n    { id: 'social_story', label: t('socialStory', 'Social Story'), icon: '📖' },\n    { id: 'movement_break', label: t('movementBreak', 'Movement Break'), icon: '🏃' }\n  ];\n\n  const intensityLevels = [\n    { value: 1, label: t('veryLow', 'Very Low'), color: 'bg-green-200', emoji: '😌' },\n    { value: 2, label: t('low', 'Low'), color: 'bg-green-300', emoji: '🙂' },\n    { value: 3, label: t('moderate', 'Moderate'), color: 'bg-yellow-300', emoji: '😐' },\n    { value: 4, label: t('high', 'High'), color: 'bg-orange-300', emoji: '😟' },\n    { value: 5, label: t('veryHigh', 'Very High'), color: 'bg-red-300', emoji: '😰' }\n  ];\n\n  const handleBehaviorSelect = (behaviorType) => {\n    setBehaviorLog(prev => ({ ...prev, behavior: behaviorType.id }));\n  };\n\n  const handleTriggerToggle = (triggerId) => {\n    setBehaviorLog(prev => ({\n      ...prev,\n      triggers: prev.triggers.includes(triggerId)\n        ? prev.triggers.filter(t => t !== triggerId)\n        : [...prev.triggers, triggerId]\n    }));\n  };\n\n  const handleInterventionToggle = (interventionId) => {\n    setBehaviorLog(prev => ({\n      ...prev,\n      interventions: prev.interventions.includes(interventionId)\n        ? prev.interventions.filter(i => i !== interventionId)\n        : [...prev.interventions, interventionId]\n    }));\n  };\n\n  const saveBehaviorLog = () => {\n    const logEntry = {\n      ...behaviorLog,\n      timestamp: new Date(),\n      patientId\n    };\n    onBehaviorLog?.(logEntry);\n    \n    // Reset form\n    setBehaviorLog({\n      behavior: '',\n      intensity: 3,\n      duration: '',\n      triggers: [],\n      interventions: [],\n      outcome: '',\n      notes: ''\n    });\n  };\n\n  const tabs = [\n    { id: 'log', label: t('logBehavior', 'Log Behavior'), icon: '📝' },\n    { id: 'patterns', label: t('patterns', 'Patterns'), icon: '📊' },\n    { id: 'strategies', label: t('strategies', 'Strategies'), icon: '💡' }\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <span className=\"mr-3\">📊</span>\n          {t('behavioralTracking', 'Behavioral Tracking')}\n        </h2>\n        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {new Date().toLocaleDateString()}\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <span>{tab.icon}</span>\n              <span>{tab.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {activeTab === 'log' && (\n        <div className=\"space-y-6\">\n          {/* Behavior Type Selection */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('selectBehaviorType', 'Select Behavior Type')}\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              {behaviorTypes.map((behavior) => (\n                <button\n                  key={behavior.id}\n                  onClick={() => handleBehaviorSelect(behavior)}\n                  className={`p-4 rounded-lg border-2 transition-all ${\n                    behaviorLog.behavior === behavior.id\n                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                  } ${behavior.color}`}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-2\">{behavior.icon}</div>\n                    <div className=\"text-sm font-medium text-gray-900\">{behavior.label}</div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Intensity and Duration */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('intensity', 'Intensity Level')}\n              </h3>\n              <div className=\"space-y-2\">\n                {intensityLevels.map((level) => (\n                  <button\n                    key={level.value}\n                    onClick={() => setBehaviorLog(prev => ({ ...prev, intensity: level.value }))}\n                    className={`w-full p-3 rounded-lg border-2 flex items-center space-x-3 transition-all ${\n                      behaviorLog.intensity === level.value\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                    } ${level.color}`}\n                  >\n                    <span className=\"text-xl\">{level.emoji}</span>\n                    <span className=\"font-medium text-gray-900\">{level.label}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('duration', 'Duration')}\n              </h3>\n              <select\n                value={behaviorLog.duration}\n                onChange={(e) => setBehaviorLog(prev => ({ ...prev, duration: e.target.value }))}\n                className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              >\n                <option value=\"\">{t('selectDuration', 'Select Duration')}</option>\n                <option value=\"under_1min\">{t('under1Min', 'Under 1 minute')}</option>\n                <option value=\"1_5min\">{t('1to5Min', '1-5 minutes')}</option>\n                <option value=\"5_15min\">{t('5to15Min', '5-15 minutes')}</option>\n                <option value=\"15_30min\">{t('15to30Min', '15-30 minutes')}</option>\n                <option value=\"over_30min\">{t('over30Min', 'Over 30 minutes')}</option>\n              </select>\n            </div>\n          </div>\n\n          {/* Triggers */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('identifiedTriggers', 'Identified Triggers')}\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              {commonTriggers.map((trigger) => (\n                <button\n                  key={trigger.id}\n                  onClick={() => handleTriggerToggle(trigger.id)}\n                  className={`p-3 rounded-lg border-2 transition-all ${\n                    behaviorLog.triggers.includes(trigger.id)\n                      ? 'border-red-500 bg-red-50 dark:bg-red-900/20'\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-lg mb-1\">{trigger.icon}</div>\n                    <div className=\"text-xs font-medium text-gray-900 dark:text-white\">{trigger.label}</div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Interventions */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('interventionsUsed', 'Interventions Used')}\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n              {interventions.map((intervention) => (\n                <button\n                  key={intervention.id}\n                  onClick={() => handleInterventionToggle(intervention.id)}\n                  className={`p-3 rounded-lg border-2 transition-all ${\n                    behaviorLog.interventions.includes(intervention.id)\n                      ? 'border-green-500 bg-green-50 dark:bg-green-900/20'\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-lg mb-1\">{intervention.icon}</div>\n                    <div className=\"text-xs font-medium text-gray-900 dark:text-white\">{intervention.label}</div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Outcome and Notes */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('outcome', 'Outcome')}\n              </label>\n              <select\n                value={behaviorLog.outcome}\n                onChange={(e) => setBehaviorLog(prev => ({ ...prev, outcome: e.target.value }))}\n                className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              >\n                <option value=\"\">{t('selectOutcome', 'Select Outcome')}</option>\n                <option value=\"resolved_quickly\">{t('resolvedQuickly', 'Resolved Quickly')}</option>\n                <option value=\"resolved_with_support\">{t('resolvedWithSupport', 'Resolved with Support')}</option>\n                <option value=\"partially_resolved\">{t('partiallyResolved', 'Partially Resolved')}</option>\n                <option value=\"escalated\">{t('escalated', 'Escalated')}</option>\n                <option value=\"ongoing\">{t('ongoing', 'Ongoing')}</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('additionalNotes', 'Additional Notes')}\n              </label>\n              <textarea\n                value={behaviorLog.notes}\n                onChange={(e) => setBehaviorLog(prev => ({ ...prev, notes: e.target.value }))}\n                rows=\"4\"\n                className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                placeholder={t('notesPlaceholder', 'Enter any additional observations, context, or details...')}\n              />\n            </div>\n          </div>\n\n          {/* Save Button */}\n          <div className=\"flex justify-end\">\n            <button\n              onClick={saveBehaviorLog}\n              disabled={!behaviorLog.behavior}\n              className=\"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n            >\n              {t('saveBehaviorLog', 'Save Behavior Log')}\n            </button>\n          </div>\n        </div>\n      )}\n\n      {activeTab === 'patterns' && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-6xl mb-4\">📊</div>\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            {t('behaviorPatterns', 'Behavior Patterns')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('patternsComingSoon', 'Pattern analysis and charts coming soon...')}\n          </p>\n        </div>\n      )}\n\n      {activeTab === 'strategies' && (\n        <div className=\"text-center py-12\">\n          <div className=\"text-6xl mb-4\">💡</div>\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            {t('behaviorStrategies', 'Behavior Strategies')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('strategiesComingSoon', 'Personalized behavior strategies coming soon...')}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default BehavioralTracking;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst VisualSchedule = ({ patientProfile, sessionActivities, onScheduleUpdate }) => {\n  const { t } = useLanguage();\n  const [schedule, setSchedule] = useState([]);\n  const [selectedActivity, setSelectedActivity] = useState(null);\n  const [scheduleMode, setScheduleMode] = useState('session'); // session, daily, weekly\n\n  const activityLibrary = {\n    arrival: {\n      id: 'arrival',\n      name: t('arrival', 'Arrival'),\n      icon: '🚪',\n      color: 'bg-blue-100',\n      duration: 5,\n      description: t('arrivalDesc', 'Welcome and check-in')\n    },\n    warmup: {\n      id: 'warmup',\n      name: t('warmup', 'Warm-up'),\n      icon: '🤸',\n      color: 'bg-green-100',\n      duration: 10,\n      description: t('warmupDesc', 'Gentle movement preparation')\n    },\n    exercise: {\n      id: 'exercise',\n      name: t('exercise', 'Exercise'),\n      icon: '🏋️',\n      color: 'bg-red-100',\n      duration: 20,\n      description: t('exerciseDesc', 'Main therapy activities')\n    },\n    sensory_break: {\n      id: 'sensory_break',\n      name: t('sensoryBreak', 'Sensory Break'),\n      icon: '🧘',\n      color: 'bg-purple-100',\n      duration: 5,\n      description: t('sensoryBreakDesc', 'Calming sensory activity')\n    },\n    play_therapy: {\n      id: 'play_therapy',\n      name: t('playTherapy', 'Play Therapy'),\n      icon: '🎮',\n      color: 'bg-yellow-100',\n      duration: 15,\n      description: t('playTherapyDesc', 'Therapeutic play activities')\n    },\n    communication: {\n      id: 'communication',\n      name: t('communication', 'Communication'),\n      icon: '💬',\n      color: 'bg-pink-100',\n      duration: 10,\n      description: t('communicationDesc', 'Communication practice')\n    },\n    snack_time: {\n      id: 'snack_time',\n      name: t('snackTime', 'Snack Time'),\n      icon: '🍎',\n      color: 'bg-orange-100',\n      duration: 10,\n      description: t('snackTimeDesc', 'Nutrition and social time')\n    },\n    cleanup: {\n      id: 'cleanup',\n      name: t('cleanup', 'Clean-up'),\n      icon: '🧹',\n      color: 'bg-gray-100',\n      duration: 5,\n      description: t('cleanupDesc', 'Organizing and tidying')\n    },\n    goodbye: {\n      id: 'goodbye',\n      name: t('goodbye', 'Goodbye'),\n      icon: '👋',\n      color: 'bg-indigo-100',\n      duration: 5,\n      description: t('goodbyeDesc', 'Session wrap-up and farewell')\n    }\n  };\n\n  const scheduleTemplates = {\n    autism_friendly: {\n      name: t('autismFriendly', 'Autism Friendly'),\n      description: t('autismScheduleDesc', 'Structured schedule with sensory breaks'),\n      activities: ['arrival', 'sensory_break', 'warmup', 'exercise', 'sensory_break', 'communication', 'cleanup', 'goodbye']\n    },\n    motor_focused: {\n      name: t('motorFocused', 'Motor Skills Focused'),\n      description: t('motorScheduleDesc', 'Emphasis on physical development'),\n      activities: ['arrival', 'warmup', 'exercise', 'play_therapy', 'exercise', 'cleanup', 'goodbye']\n    },\n    communication_focused: {\n      name: t('communicationFocused', 'Communication Focused'),\n      description: t('commScheduleDesc', 'Enhanced communication opportunities'),\n      activities: ['arrival', 'communication', 'warmup', 'play_therapy', 'communication', 'snack_time', 'goodbye']\n    },\n    sensory_integration: {\n      name: t('sensoryIntegration', 'Sensory Integration'),\n      description: t('sensoryScheduleDesc', 'Balanced sensory experiences'),\n      activities: ['arrival', 'sensory_break', 'exercise', 'sensory_break', 'play_therapy', 'sensory_break', 'goodbye']\n    }\n  };\n\n  const addActivityToSchedule = (activityId) => {\n    const activity = activityLibrary[activityId];\n    if (activity) {\n      const newScheduleItem = {\n        ...activity,\n        id: `${activityId}_${Date.now()}`,\n        startTime: calculateStartTime(),\n        completed: false,\n        notes: ''\n      };\n      setSchedule(prev => [...prev, newScheduleItem]);\n    }\n  };\n\n  const removeActivityFromSchedule = (itemId) => {\n    setSchedule(prev => prev.filter(item => item.id !== itemId));\n  };\n\n  const moveActivity = (itemId, direction) => {\n    const currentIndex = schedule.findIndex(item => item.id === itemId);\n    if (currentIndex === -1) return;\n\n    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;\n    if (newIndex < 0 || newIndex >= schedule.length) return;\n\n    const newSchedule = [...schedule];\n    [newSchedule[currentIndex], newSchedule[newIndex]] = [newSchedule[newIndex], newSchedule[currentIndex]];\n    setSchedule(newSchedule);\n  };\n\n  const calculateStartTime = () => {\n    if (schedule.length === 0) {\n      return '09:00';\n    }\n    \n    const lastActivity = schedule[schedule.length - 1];\n    const lastStartTime = lastActivity.startTime;\n    const [hours, minutes] = lastStartTime.split(':').map(Number);\n    const lastEndTime = new Date();\n    lastEndTime.setHours(hours, minutes + lastActivity.duration);\n    \n    return `${lastEndTime.getHours().toString().padStart(2, '0')}:${lastEndTime.getMinutes().toString().padStart(2, '0')}`;\n  };\n\n  const applyTemplate = (templateKey) => {\n    const template = scheduleTemplates[templateKey];\n    if (template) {\n      const newSchedule = template.activities.map((activityId, index) => {\n        const activity = activityLibrary[activityId];\n        const startTime = new Date();\n        startTime.setHours(9, index * 15); // Start at 9 AM, 15 minutes apart\n        \n        return {\n          ...activity,\n          id: `${activityId}_${Date.now()}_${index}`,\n          startTime: `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}`,\n          completed: false,\n          notes: ''\n        };\n      });\n      setSchedule(newSchedule);\n    }\n  };\n\n  const toggleActivityCompletion = (itemId) => {\n    setSchedule(prev => prev.map(item => \n      item.id === itemId ? { ...item, completed: !item.completed } : item\n    ));\n  };\n\n  const updateActivityNotes = (itemId, notes) => {\n    setSchedule(prev => prev.map(item => \n      item.id === itemId ? { ...item, notes } : item\n    ));\n  };\n\n  const getTotalDuration = () => {\n    return schedule.reduce((total, activity) => total + activity.duration, 0);\n  };\n\n  const getCompletionPercentage = () => {\n    if (schedule.length === 0) return 0;\n    const completed = schedule.filter(item => item.completed).length;\n    return Math.round((completed / schedule.length) * 100);\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <span className=\"mr-3\">📅</span>\n          {t('visualSchedule', 'Visual Schedule')}\n        </h2>\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {t('totalTime', 'Total Time')}: {getTotalDuration()} {t('minutes', 'minutes')}\n          </div>\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {t('completed', 'Completed')}: {getCompletionPercentage()}%\n          </div>\n        </div>\n      </div>\n\n      {/* Schedule Mode Selector */}\n      <div className=\"mb-6\">\n        <div className=\"flex space-x-4\">\n          {['session', 'daily', 'weekly'].map((mode) => (\n            <button\n              key={mode}\n              onClick={() => setScheduleMode(mode)}\n              className={`px-4 py-2 rounded-lg font-medium text-sm transition-colors ${\n                scheduleMode === mode\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\n              }`}\n            >\n              {t(mode, mode)}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Activity Library */}\n        <div className=\"lg:col-span-1\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('activityLibrary', 'Activity Library')}\n          </h3>\n          \n          {/* Templates */}\n          <div className=\"mb-6\">\n            <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n              {t('templates', 'Templates')}\n            </h4>\n            <div className=\"space-y-2\">\n              {Object.entries(scheduleTemplates).map(([key, template]) => (\n                <button\n                  key={key}\n                  onClick={() => applyTemplate(key)}\n                  className=\"w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                >\n                  <div className=\"font-medium text-sm text-gray-900 dark:text-white\">{template.name}</div>\n                  <div className=\"text-xs text-gray-600 dark:text-gray-400 mt-1\">{template.description}</div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Individual Activities */}\n          <div>\n            <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n              {t('activities', 'Activities')}\n            </h4>\n            <div className=\"space-y-2\">\n              {Object.values(activityLibrary).map((activity) => (\n                <button\n                  key={activity.id}\n                  onClick={() => addActivityToSchedule(activity.id)}\n                  className={`w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${activity.color}`}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <span className=\"text-2xl\">{activity.icon}</span>\n                    <div>\n                      <div className=\"font-medium text-sm text-gray-900\">{activity.name}</div>\n                      <div className=\"text-xs text-gray-600 mt-1\">{activity.duration} min</div>\n                    </div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Schedule Builder */}\n        <div className=\"lg:col-span-2\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('currentSchedule', 'Current Schedule')}\n          </h3>\n          \n          {schedule.length === 0 ? (\n            <div className=\"text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg\">\n              <div className=\"text-6xl mb-4\">📅</div>\n              <h4 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {t('emptySchedule', 'No activities scheduled')}\n              </h4>\n              <p className=\"text-gray-600 dark:text-gray-400\">\n                {t('addActivitiesPrompt', 'Add activities from the library or use a template to get started')}\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {schedule.map((item, index) => (\n                <div\n                  key={item.id}\n                  className={`p-4 border border-gray-200 dark:border-gray-600 rounded-lg transition-all ${\n                    item.completed ? 'bg-green-50 dark:bg-green-900/20 border-green-300' : 'bg-white dark:bg-gray-800'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"flex flex-col space-y-1\">\n                        <button\n                          onClick={() => moveActivity(item.id, 'up')}\n                          disabled={index === 0}\n                          className=\"text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed\"\n                        >\n                          ▲\n                        </button>\n                        <button\n                          onClick={() => moveActivity(item.id, 'down')}\n                          disabled={index === schedule.length - 1}\n                          className=\"text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed\"\n                        >\n                          ▼\n                        </button>\n                      </div>\n                      \n                      <button\n                        onClick={() => toggleActivityCompletion(item.id)}\n                        className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ${\n                          item.completed\n                            ? 'bg-green-500 border-green-500 text-white'\n                            : 'border-gray-300 hover:border-gray-400'\n                        }`}\n                      >\n                        {item.completed && '✓'}\n                      </button>\n                      \n                      <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-2xl ${item.color}`}>\n                        {item.icon}\n                      </div>\n                      \n                      <div>\n                        <div className=\"flex items-center space-x-2\">\n                          <h4 className={`font-medium ${item.completed ? 'line-through text-gray-500' : 'text-gray-900 dark:text-white'}`}>\n                            {item.name}\n                          </h4>\n                          <span className=\"text-sm text-gray-500\">({item.duration} min)</span>\n                        </div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {item.startTime} - {item.description}\n                        </div>\n                      </div>\n                    </div>\n                    \n                    <button\n                      onClick={() => removeActivityFromSchedule(item.id)}\n                      className=\"text-red-500 hover:text-red-700 text-sm\"\n                    >\n                      ✕\n                    </button>\n                  </div>\n                  \n                  {/* Notes Section */}\n                  <div className=\"mt-3\">\n                    <textarea\n                      value={item.notes}\n                      onChange={(e) => updateActivityNotes(item.id, e.target.value)}\n                      placeholder={t('addNotes', 'Add notes about this activity...')}\n                      className=\"w-full p-2 text-sm border border-gray-200 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                      rows=\"2\"\n                    />\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n          \n          {/* Progress Bar */}\n          {schedule.length > 0 && (\n            <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  {t('sessionProgress', 'Session Progress')}\n                </span>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  {schedule.filter(item => item.completed).length} / {schedule.length} {t('completed', 'completed')}\n                </span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3\">\n                <div \n                  className=\"bg-green-600 h-3 rounded-full transition-all duration-300\"\n                  style={{ width: `${getCompletionPercentage()}%` }}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Save Button */}\n      <div className=\"mt-8 flex justify-end space-x-4\">\n        <button\n          onClick={() => setSchedule([])}\n          className=\"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n        >\n          {t('clearSchedule', 'Clear Schedule')}\n        </button>\n        <button\n          onClick={() => onScheduleUpdate?.(schedule)}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n        >\n          {t('saveSchedule', 'Save Schedule')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default VisualSchedule;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst ProgressVisualization = ({ patientData, goals, behaviorLogs }) => {\n  const { t } = useLanguage();\n  const [activeView, setActiveView] = useState('overview');\n  const [timeRange, setTimeRange] = useState('week'); // week, month, quarter\n\n  // Mock data for demonstration\n  const progressData = {\n    goals: [\n      { id: 'motor_skills', name: t('motorSkills', 'Motor Skills'), progress: 75, target: 100, color: 'bg-blue-500' },\n      { id: 'communication', name: t('communication', 'Communication'), progress: 60, target: 100, color: 'bg-green-500' },\n      { id: 'sensory_integration', name: t('sensoryIntegration', 'Sensory Integration'), progress: 45, target: 100, color: 'bg-purple-500' },\n      { id: 'social_skills', name: t('socialSkills', 'Social Skills'), progress: 30, target: 100, color: 'bg-yellow-500' }\n    ],\n    milestones: [\n      { id: 1, name: t('firstSteps', 'First Independent Steps'), achieved: true, date: '2024-01-15', icon: '👶' },\n      { id: 2, name: t('verbalRequest', 'First Verbal Request'), achieved: true, date: '2024-01-20', icon: '💬' },\n      { id: 3, name: t('socialInteraction', 'Initiated Social Interaction'), achieved: false, target: '2024-02-15', icon: '👋' },\n      { id: 4, name: t('independentTask', 'Completed Task Independently'), achieved: false, target: '2024-02-28', icon: '✅' }\n    ],\n    weeklyProgress: [\n      { week: 'Week 1', motor: 20, communication: 15, sensory: 10, social: 5 },\n      { week: 'Week 2', motor: 35, communication: 25, sensory: 20, social: 10 },\n      { week: 'Week 3', motor: 50, communication: 40, sensory: 30, social: 15 },\n      { week: 'Week 4', motor: 75, communication: 60, sensory: 45, social: 30 }\n    ],\n    behaviorTrends: [\n      { behavior: 'Cooperation', trend: 'improving', change: '+25%', color: 'text-green-600' },\n      { behavior: 'Focus', trend: 'stable', change: '0%', color: 'text-blue-600' },\n      { behavior: 'Anxiety', trend: 'decreasing', change: '-15%', color: 'text-green-600' },\n      { behavior: 'Meltdowns', trend: 'decreasing', change: '-40%', color: 'text-green-600' }\n    ]\n  };\n\n  const achievements = [\n    { id: 1, title: t('weekStreak', '7-Day Streak'), description: t('weekStreakDesc', 'Attended all sessions this week'), icon: '🔥', earned: true },\n    { id: 2, title: t('communicator', 'Great Communicator'), description: t('communicatorDesc', 'Used communication board 10 times'), icon: '💬', earned: true },\n    { id: 3, title: t('brave', 'Brave Explorer'), description: t('braveDesc', 'Tried 3 new activities'), icon: '🌟', earned: false },\n    { id: 4, title: t('helper', 'Super Helper'), description: t('helperDesc', 'Helped clean up 5 times'), icon: '🦸', earned: false }\n  ];\n\n  const views = [\n    { id: 'overview', label: t('overview', 'Overview'), icon: '📊' },\n    { id: 'goals', label: t('goals', 'Goals'), icon: '🎯' },\n    { id: 'milestones', label: t('milestones', 'Milestones'), icon: '🏆' },\n    { id: 'behavior', label: t('behavior', 'Behavior'), icon: '📈' },\n    { id: 'achievements', label: t('achievements', 'Achievements'), icon: '🌟' }\n  ];\n\n  const renderOverview = () => (\n    <div className=\"space-y-6\">\n      {/* Progress Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {progressData.goals.map((goal) => (\n          <div key={goal.id} className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">{goal.name}</h3>\n              <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">{goal.progress}%</span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2\">\n              <div \n                className={`h-3 rounded-full transition-all duration-500 ${goal.color}`}\n                style={{ width: `${goal.progress}%` }}\n              />\n            </div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('target', 'Target')}: {goal.target}%\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Recent Achievements */}\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('recentAchievements', 'Recent Achievements')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {achievements.filter(a => a.earned).map((achievement) => (\n            <div key={achievement.id} className=\"flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700\">\n              <span className=\"text-2xl\">{achievement.icon}</span>\n              <div>\n                <div className=\"font-medium text-gray-900 dark:text-white\">{achievement.title}</div>\n                <div className=\"text-sm text-gray-600 dark:text-gray-400\">{achievement.description}</div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Behavior Trends */}\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('behaviorTrends', 'Behavior Trends')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          {progressData.behaviorTrends.map((trend, index) => (\n            <div key={index} className=\"text-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n              <div className=\"font-medium text-gray-900 dark:text-white mb-2\">{trend.behavior}</div>\n              <div className={`text-2xl font-bold ${trend.color} mb-1`}>{trend.change}</div>\n              <div className=\"text-sm text-gray-600 dark:text-gray-400 capitalize\">{trend.trend}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderGoals = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n          {t('goalProgress', 'Goal Progress Over Time')}\n        </h3>\n        \n        {/* Simple Progress Chart */}\n        <div className=\"space-y-6\">\n          {progressData.weeklyProgress.map((week, index) => (\n            <div key={index} className=\"space-y-3\">\n              <h4 className=\"font-medium text-gray-900 dark:text-white\">{week.week}</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">{t('motor', 'Motor')}</span>\n                    <span className=\"font-medium\">{week.motor}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div className=\"bg-blue-500 h-2 rounded-full transition-all duration-500\" style={{ width: `${week.motor}%` }} />\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">{t('communication', 'Communication')}</span>\n                    <span className=\"font-medium\">{week.communication}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div className=\"bg-green-500 h-2 rounded-full transition-all duration-500\" style={{ width: `${week.communication}%` }} />\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">{t('sensory', 'Sensory')}</span>\n                    <span className=\"font-medium\">{week.sensory}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div className=\"bg-purple-500 h-2 rounded-full transition-all duration-500\" style={{ width: `${week.sensory}%` }} />\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">{t('social', 'Social')}</span>\n                    <span className=\"font-medium\">{week.social}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div className=\"bg-yellow-500 h-2 rounded-full transition-all duration-500\" style={{ width: `${week.social}%` }} />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderMilestones = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n          {t('milestoneTimeline', 'Milestone Timeline')}\n        </h3>\n        \n        <div className=\"space-y-4\">\n          {progressData.milestones.map((milestone, index) => (\n            <div key={milestone.id} className=\"flex items-start space-x-4\">\n              <div className={`w-12 h-12 rounded-full flex items-center justify-center text-2xl ${\n                milestone.achieved ? 'bg-green-100 border-2 border-green-500' : 'bg-gray-100 border-2 border-gray-300'\n              }`}>\n                {milestone.achieved ? '✅' : milestone.icon}\n              </div>\n              <div className=\"flex-1\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className={`font-medium ${\n                    milestone.achieved ? 'text-green-700 dark:text-green-400' : 'text-gray-900 dark:text-white'\n                  }`}>\n                    {milestone.name}\n                  </h4>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {milestone.achieved ? milestone.date : `${t('target', 'Target')}: ${milestone.target}`}\n                  </span>\n                </div>\n                {milestone.achieved && (\n                  <div className=\"text-sm text-green-600 dark:text-green-400 mt-1\">\n                    {t('achieved', 'Achieved')} ✨\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderBehavior = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n          {t('behaviorAnalysis', 'Behavior Analysis')}\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          {/* Positive Behaviors */}\n          <div>\n            <h4 className=\"font-medium text-green-700 dark:text-green-400 mb-3 flex items-center\">\n              <span className=\"mr-2\">✅</span>\n              {t('positiveBehaviors', 'Positive Behaviors')}\n            </h4>\n            <div className=\"space-y-3\">\n              {['Cooperation', 'Communication', 'Focus'].map((behavior, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                  <span className=\"text-gray-900 dark:text-white\">{t(behavior.toLowerCase(), behavior)}</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div className=\"bg-green-500 h-2 rounded-full\" style={{ width: `${75 + index * 10}%` }} />\n                    </div>\n                    <span className=\"text-sm font-medium text-green-600\">{75 + index * 10}%</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Challenging Behaviors */}\n          <div>\n            <h4 className=\"font-medium text-orange-700 dark:text-orange-400 mb-3 flex items-center\">\n              <span className=\"mr-2\">⚠️</span>\n              {t('challengingBehaviors', 'Challenging Behaviors')}\n            </h4>\n            <div className=\"space-y-3\">\n              {['Meltdowns', 'Anxiety', 'Withdrawal'].map((behavior, index) => (\n                <div key={index} className=\"flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg\">\n                  <span className=\"text-gray-900 dark:text-white\">{t(behavior.toLowerCase(), behavior)}</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                      <div className=\"bg-orange-500 h-2 rounded-full\" style={{ width: `${30 - index * 5}%` }} />\n                    </div>\n                    <span className=\"text-sm font-medium text-orange-600\">{30 - index * 5}%</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderAchievements = () => (\n    <div className=\"space-y-6\">\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n          {t('achievementBadges', 'Achievement Badges')}\n        </h3>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {achievements.map((achievement) => (\n            <div\n              key={achievement.id}\n              className={`p-6 rounded-lg border-2 text-center transition-all ${\n                achievement.earned\n                  ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600'\n                  : 'bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 opacity-60'\n              }`}\n            >\n              <div className=\"text-4xl mb-3\">{achievement.icon}</div>\n              <h4 className={`font-medium mb-2 ${\n                achievement.earned ? 'text-yellow-800 dark:text-yellow-200' : 'text-gray-600 dark:text-gray-400'\n              }`}>\n                {achievement.title}\n              </h4>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{achievement.description}</p>\n              {achievement.earned && (\n                <div className=\"mt-3 text-xs font-medium text-yellow-600 dark:text-yellow-400\">\n                  {t('earned', 'Earned')} ✨\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeView) {\n      case 'overview': return renderOverview();\n      case 'goals': return renderGoals();\n      case 'milestones': return renderMilestones();\n      case 'behavior': return renderBehavior();\n      case 'achievements': return renderAchievements();\n      default: return renderOverview();\n    }\n  };\n\n  return (\n    <div className=\"bg-gray-50 dark:bg-gray-900 min-h-screen p-6\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white flex items-center\">\n            <span className=\"mr-3\">📊</span>\n            {t('progressVisualization', 'Progress Visualization')}\n          </h1>\n          <div className=\"flex items-center space-x-4\">\n            <select\n              value={timeRange}\n              onChange={(e) => setTimeRange(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n            >\n              <option value=\"week\">{t('thisWeek', 'This Week')}</option>\n              <option value=\"month\">{t('thisMonth', 'This Month')}</option>\n              <option value=\"quarter\">{t('thisQuarter', 'This Quarter')}</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"mb-8\">\n          <nav className=\"flex space-x-4 overflow-x-auto pb-2\">\n            {views.map((view) => (\n              <button\n                key={view.id}\n                onClick={() => setActiveView(view.id)}\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${\n                  activeView === view.id\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600'\n                }`}\n              >\n                <span>{view.icon}</span>\n                <span className=\"font-medium\">{view.label}</span>\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Content */}\n        {renderContent()}\n      </div>\n    </div>\n  );\n};\n\nexport default ProgressVisualization;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst EmergencyProtocols = ({ patientProfile, onEmergencyLog }) => {\n  const { t } = useLanguage();\n  const [activeProtocol, setActiveProtocol] = useState(null);\n  const [emergencyLog, setEmergencyLog] = useState({\n    type: '',\n    severity: 'low',\n    triggers: [],\n    interventions: [],\n    outcome: '',\n    duration: '',\n    notes: ''\n  });\n\n  const emergencyTypes = {\n    behavioral: {\n      id: 'behavioral',\n      name: t('behavioralEmergency', 'Behavioral Emergency'),\n      icon: '🚨',\n      color: 'bg-red-100 border-red-300',\n      protocols: [\n        {\n          id: 'meltdown',\n          name: t('meltdown', 'Meltdown'),\n          severity: 'high',\n          steps: [\n            t('meltdownStep1', 'Ensure safety of patient and others'),\n            t('meltdownStep2', 'Remove or reduce triggers'),\n            t('meltdownStep3', 'Provide calm, quiet space'),\n            t('meltdownStep4', 'Use calming techniques'),\n            t('meltdownStep5', 'Monitor and document')\n          ]\n        },\n        {\n          id: 'aggression',\n          name: t('aggression', 'Aggressive Behavior'),\n          severity: 'high',\n          steps: [\n            t('aggressionStep1', 'Prioritize safety'),\n            t('aggressionStep2', 'Maintain calm demeanor'),\n            t('aggressionStep3', 'Use de-escalation techniques'),\n            t('aggressionStep4', 'Implement behavior plan'),\n            t('aggressionStep5', 'Contact emergency contacts if needed')\n          ]\n        },\n        {\n          id: 'self_harm',\n          name: t('selfHarm', 'Self-Injurious Behavior'),\n          severity: 'critical',\n          steps: [\n            t('selfHarmStep1', 'Immediate intervention to prevent injury'),\n            t('selfHarmStep2', 'Redirect to safe alternatives'),\n            t('selfHarmStep3', 'Assess for injuries'),\n            t('selfHarmStep4', 'Implement safety protocols'),\n            t('selfHarmStep5', 'Contact medical personnel if needed')\n          ]\n        }\n      ]\n    },\n    medical: {\n      id: 'medical',\n      name: t('medicalEmergency', 'Medical Emergency'),\n      icon: '🏥',\n      color: 'bg-blue-100 border-blue-300',\n      protocols: [\n        {\n          id: 'seizure',\n          name: t('seizure', 'Seizure'),\n          severity: 'critical',\n          steps: [\n            t('seizureStep1', 'Keep patient safe, do not restrain'),\n            t('seizureStep2', 'Clear area of dangerous objects'),\n            t('seizureStep3', 'Time the seizure'),\n            t('seizureStep4', 'Place in recovery position after seizure'),\n            t('seizureStep5', 'Call emergency services if prolonged')\n          ]\n        },\n        {\n          id: 'breathing',\n          name: t('breathingDifficulty', 'Breathing Difficulty'),\n          severity: 'critical',\n          steps: [\n            t('breathingStep1', 'Assess airway and breathing'),\n            t('breathingStep2', 'Position for optimal breathing'),\n            t('breathingStep3', 'Administer rescue medication if prescribed'),\n            t('breathingStep4', 'Call emergency services'),\n            t('breathingStep5', 'Monitor vital signs')\n          ]\n        },\n        {\n          id: 'injury',\n          name: t('injury', 'Physical Injury'),\n          severity: 'medium',\n          steps: [\n            t('injuryStep1', 'Assess severity of injury'),\n            t('injuryStep2', 'Provide first aid'),\n            t('injuryStep3', 'Control bleeding if present'),\n            t('injuryStep4', 'Contact parents/guardians'),\n            t('injuryStep5', 'Seek medical attention if needed')\n          ]\n        }\n      ]\n    },\n    sensory: {\n      id: 'sensory',\n      name: t('sensoryOverload', 'Sensory Overload'),\n      icon: '👁️',\n      color: 'bg-purple-100 border-purple-300',\n      protocols: [\n        {\n          id: 'overload',\n          name: t('sensoryOverload', 'Sensory Overload'),\n          severity: 'medium',\n          steps: [\n            t('overloadStep1', 'Identify and remove sensory triggers'),\n            t('overloadStep2', 'Move to quiet, low-stimulation area'),\n            t('overloadStep3', 'Offer sensory tools (weighted blanket, fidgets)'),\n            t('overloadStep4', 'Use calming techniques'),\n            t('overloadStep5', 'Allow recovery time')\n          ]\n        },\n        {\n          id: 'shutdown',\n          name: t('sensoryShutdown', 'Sensory Shutdown'),\n          severity: 'medium',\n          steps: [\n            t('shutdownStep1', 'Recognize shutdown signs'),\n            t('shutdownStep2', 'Provide safe, quiet space'),\n            t('shutdownStep3', 'Minimize demands and expectations'),\n            t('shutdownStep4', 'Offer comfort items'),\n            t('shutdownStep5', 'Allow time for recovery')\n          ]\n        }\n      ]\n    }\n  };\n\n  const severityLevels = {\n    low: { color: 'bg-green-100 text-green-800', label: t('low', 'Low') },\n    medium: { color: 'bg-yellow-100 text-yellow-800', label: t('medium', 'Medium') },\n    high: { color: 'bg-orange-100 text-orange-800', label: t('high', 'High') },\n    critical: { color: 'bg-red-100 text-red-800', label: t('critical', 'Critical') }\n  };\n\n  const emergencyContacts = [\n    { id: 'parent1', name: t('primaryParent', 'Primary Parent'), phone: '+966 50 123 4567', role: t('mother', 'Mother') },\n    { id: 'parent2', name: t('secondaryParent', 'Secondary Parent'), phone: '+966 50 765 4321', role: t('father', 'Father') },\n    { id: 'emergency', name: t('emergencyServices', 'Emergency Services'), phone: '997', role: t('medical', 'Medical') },\n    { id: 'doctor', name: t('primaryDoctor', 'Primary Doctor'), phone: '+966 11 234 5678', role: t('physician', 'Physician') }\n  ];\n\n  const activateProtocol = (protocol) => {\n    setActiveProtocol(protocol);\n    setEmergencyLog(prev => ({\n      ...prev,\n      type: protocol.id,\n      severity: protocol.severity\n    }));\n  };\n\n  const deactivateProtocol = () => {\n    setActiveProtocol(null);\n    setEmergencyLog({\n      type: '',\n      severity: 'low',\n      triggers: [],\n      interventions: [],\n      outcome: '',\n      duration: '',\n      notes: ''\n    });\n  };\n\n  const logEmergency = () => {\n    const logEntry = {\n      ...emergencyLog,\n      timestamp: new Date(),\n      patientId: patientProfile?.id,\n      protocolUsed: activeProtocol?.id\n    };\n    onEmergencyLog?.(logEntry);\n    deactivateProtocol();\n  };\n\n  const callEmergencyContact = (contact) => {\n    // In a real app, this would integrate with phone system\n    alert(`${t('calling', 'Calling')} ${contact.name}: ${contact.phone}`);\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <span className=\"mr-3\">🚨</span>\n          {t('emergencyProtocols', 'Emergency Protocols')}\n        </h2>\n        {activeProtocol && (\n          <button\n            onClick={deactivateProtocol}\n            className=\"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n          >\n            {t('deactivate', 'Deactivate')}\n          </button>\n        )}\n      </div>\n\n      {!activeProtocol ? (\n        <div className=\"space-y-6\">\n          {/* Emergency Protocol Selection */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('selectEmergencyType', 'Select Emergency Type')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {Object.values(emergencyTypes).map((category) => (\n                <div key={category.id} className={`p-4 border-2 border-dashed rounded-lg ${category.color}`}>\n                  <h4 className=\"font-medium text-gray-900 mb-3 flex items-center\">\n                    <span className=\"mr-2 text-2xl\">{category.icon}</span>\n                    {category.name}\n                  </h4>\n                  <div className=\"space-y-2\">\n                    {category.protocols.map((protocol) => (\n                      <button\n                        key={protocol.id}\n                        onClick={() => activateProtocol(protocol)}\n                        className=\"w-full p-3 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\"\n                      >\n                        <div className=\"flex items-center justify-between\">\n                          <span className=\"font-medium text-gray-900\">{protocol.name}</span>\n                          <span className={`px-2 py-1 rounded text-xs font-medium ${severityLevels[protocol.severity].color}`}>\n                            {severityLevels[protocol.severity].label}\n                          </span>\n                        </div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Emergency Contacts */}\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('emergencyContacts', 'Emergency Contacts')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {emergencyContacts.map((contact) => (\n                <div key={contact.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">{contact.name}</h4>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">{contact.role}</p>\n                      <p className=\"text-sm font-mono text-gray-700 dark:text-gray-300\">{contact.phone}</p>\n                    </div>\n                    <button\n                      onClick={() => callEmergencyContact(contact)}\n                      className=\"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2\"\n                    >\n                      📞 {t('call', 'Call')}\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      ) : (\n        <div className=\"space-y-6\">\n          {/* Active Protocol */}\n          <div className=\"bg-red-50 dark:bg-red-900/20 border-2 border-red-300 dark:border-red-600 rounded-lg p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-xl font-bold text-red-800 dark:text-red-200 flex items-center\">\n                <span className=\"mr-2\">🚨</span>\n                {t('activeProtocol', 'ACTIVE PROTOCOL')}: {activeProtocol.name}\n              </h3>\n              <span className={`px-3 py-1 rounded-full text-sm font-medium ${severityLevels[activeProtocol.severity].color}`}>\n                {severityLevels[activeProtocol.severity].label} {t('severity', 'Severity')}\n              </span>\n            </div>\n\n            <div className=\"space-y-4\">\n              <h4 className=\"font-semibold text-red-700 dark:text-red-300\">\n                {t('followSteps', 'Follow these steps:')}\n              </h4>\n              <ol className=\"space-y-3\">\n                {activeProtocol.steps.map((step, index) => (\n                  <li key={index} className=\"flex items-start space-x-3\">\n                    <span className=\"flex-shrink-0 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold\">\n                      {index + 1}\n                    </span>\n                    <span className=\"text-red-800 dark:text-red-200\">{step}</span>\n                  </li>\n                ))}\n              </ol>\n            </div>\n          </div>\n\n          {/* Emergency Logging */}\n          <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n            <h4 className=\"font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('logEmergency', 'Log Emergency Details')}\n            </h4>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('duration', 'Duration')}\n                </label>\n                <select\n                  value={emergencyLog.duration}\n                  onChange={(e) => setEmergencyLog(prev => ({ ...prev, duration: e.target.value }))}\n                  className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"\">{t('selectDuration', 'Select Duration')}</option>\n                  <option value=\"under_5min\">{t('under5Min', 'Under 5 minutes')}</option>\n                  <option value=\"5_15min\">{t('5to15Min', '5-15 minutes')}</option>\n                  <option value=\"15_30min\">{t('15to30Min', '15-30 minutes')}</option>\n                  <option value=\"over_30min\">{t('over30Min', 'Over 30 minutes')}</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('outcome', 'Outcome')}\n                </label>\n                <select\n                  value={emergencyLog.outcome}\n                  onChange={(e) => setEmergencyLog(prev => ({ ...prev, outcome: e.target.value }))}\n                  className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"\">{t('selectOutcome', 'Select Outcome')}</option>\n                  <option value=\"resolved\">{t('resolved', 'Resolved')}</option>\n                  <option value=\"partially_resolved\">{t('partiallyResolved', 'Partially Resolved')}</option>\n                  <option value=\"ongoing\">{t('ongoing', 'Ongoing')}</option>\n                  <option value=\"escalated\">{t('escalated', 'Escalated to Medical')}</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('emergencyNotes', 'Emergency Notes')}\n              </label>\n              <textarea\n                value={emergencyLog.notes}\n                onChange={(e) => setEmergencyLog(prev => ({ ...prev, notes: e.target.value }))}\n                rows=\"4\"\n                className=\"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('emergencyNotesPlaceholder', 'Describe what happened, interventions used, and any other relevant details...')}\n              />\n            </div>\n\n            <div className=\"mt-6 flex justify-end space-x-4\">\n              <button\n                onClick={deactivateProtocol}\n                className=\"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                onClick={logEmergency}\n                className=\"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2\"\n              >\n                {t('logAndClose', 'Log & Close Emergency')}\n              </button>\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4\">\n            <h4 className=\"font-semibold text-yellow-800 dark:text-yellow-200 mb-3\">\n              {t('quickActions', 'Quick Actions')}\n            </h4>\n            <div className=\"flex flex-wrap gap-2\">\n              {emergencyContacts.slice(0, 2).map((contact) => (\n                <button\n                  key={contact.id}\n                  onClick={() => callEmergencyContact(contact)}\n                  className=\"px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 text-sm\"\n                >\n                  📞 {contact.name}\n                </button>\n              ))}\n              <button\n                onClick={() => callEmergencyContact(emergencyContacts[2])}\n                className=\"px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm\"\n              >\n                🚑 {t('callEmergency', 'Call 997')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default EmergencyProtocols;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport SpecialNeedsPatientProfile from '../../components/SpecialNeeds/PatientProfile';\nimport CommunicationBoard from '../../components/SpecialNeeds/CommunicationBoard';\nimport SensoryEnvironmentControls from '../../components/SpecialNeeds/SensoryControls';\nimport BehavioralTracking from '../../components/SpecialNeeds/BehavioralTracking';\nimport AdaptiveTreatmentPlan from '../../components/SpecialNeeds/AdaptiveTreatmentPlan';\nimport VisualSchedule from '../../components/SpecialNeeds/VisualSchedule';\nimport ProgressVisualization from '../../components/SpecialNeeds/ProgressVisualization';\nimport EmergencyProtocols from '../../components/SpecialNeeds/EmergencyProtocols';\n\nconst SpecialNeedsDashboard = () => {\n  const { t } = useLanguage();\n  const [activeSection, setActiveSection] = useState('overview');\n  const [currentPatient, setCurrentPatient] = useState({\n    id: 'patient_001',\n    name: '<PERSON>',\n    age: 8,\n    primaryDiagnosis: 'autism',\n    communicationLevel: 'non_verbal',\n    sensoryProfile: 'sensory_seeking'\n  });\n\n  const sections = [\n    {\n      id: 'overview',\n      title: t('overview', 'Overview'),\n      icon: '📋',\n      description: t('overviewDesc', 'Patient overview and quick stats')\n    },\n    {\n      id: 'profile',\n      title: t('patientProfile', 'Patient Profile'),\n      icon: '👤',\n      description: t('profileDesc', 'Detailed special needs profile')\n    },\n    {\n      id: 'communication',\n      title: t('communication', 'Communication'),\n      icon: '💬',\n      description: t('communicationDesc', 'Visual communication tools')\n    },\n    {\n      id: 'sensory',\n      title: t('sensoryEnvironment', 'Sensory Environment'),\n      icon: '🎛️',\n      description: t('sensoryDesc', 'Environmental controls and settings')\n    },\n    {\n      id: 'behavior',\n      title: t('behaviorTracking', 'Behavior Tracking'),\n      icon: '📊',\n      description: t('behaviorDesc', 'Track and analyze behaviors')\n    },\n    {\n      id: 'treatment',\n      title: t('treatmentPlan', 'Treatment Plan'),\n      icon: '🎯',\n      description: t('treatmentDesc', 'Adaptive treatment planning')\n    },\n    {\n      id: 'schedule',\n      title: t('visualSchedule', 'Visual Schedule'),\n      icon: '📅',\n      description: t('scheduleDesc', 'Visual session planning and tracking')\n    },\n    {\n      id: 'progress',\n      title: t('progressTracking', 'Progress Tracking'),\n      icon: '📊',\n      description: t('progressDesc', 'Visual progress and achievements')\n    },\n    {\n      id: 'emergency',\n      title: t('emergencyProtocols', 'Emergency Protocols'),\n      icon: '🚨',\n      description: t('emergencyDesc', 'Crisis management and safety protocols')\n    }\n  ];\n\n  const quickStats = [\n    {\n      title: t('totalSessions', 'Total Sessions'),\n      value: '24',\n      change: '+3',\n      changeType: 'positive',\n      icon: '📅'\n    },\n    {\n      title: t('behaviorGoals', 'Behavior Goals Met'),\n      value: '8/12',\n      change: '+2',\n      changeType: 'positive',\n      icon: '🎯'\n    },\n    {\n      title: t('communicationProgress', 'Communication Progress'),\n      value: '75%',\n      change: '+15%',\n      changeType: 'positive',\n      icon: '💬'\n    },\n    {\n      title: t('sensoryTolerance', 'Sensory Tolerance'),\n      value: 'Improving',\n      change: 'Stable',\n      changeType: 'neutral',\n      icon: '👁️'\n    }\n  ];\n\n  const recentActivities = [\n    {\n      id: 1,\n      type: 'behavior',\n      title: t('behaviorLogAdded', 'Behavior log added'),\n      description: t('cooperationBehavior', 'Cooperation behavior - High intensity'),\n      time: '10 minutes ago',\n      icon: '📊',\n      color: 'text-green-600'\n    },\n    {\n      id: 2,\n      type: 'communication',\n      title: t('communicationSuccess', 'Communication success'),\n      description: t('usedPictureCards', 'Successfully used picture cards to request break'),\n      time: '1 hour ago',\n      icon: '💬',\n      color: 'text-blue-600'\n    },\n    {\n      id: 3,\n      type: 'sensory',\n      title: t('sensoryAdjustment', 'Sensory adjustment'),\n      description: t('lightingReduced', 'Lighting reduced to calm setting'),\n      time: '2 hours ago',\n      icon: '🎛️',\n      color: 'text-purple-600'\n    }\n  ];\n\n  const upcomingGoals = [\n    {\n      id: 1,\n      title: t('increaseVerbalRequests', 'Increase verbal requests'),\n      progress: 60,\n      target: t('5VerbalRequestsPerSession', '5 verbal requests per session'),\n      dueDate: t('thisWeek', 'This week')\n    },\n    {\n      id: 2,\n      title: t('tolerateLoudSounds', 'Tolerate loud sounds'),\n      progress: 30,\n      target: t('2MinutesTolerance', '2 minutes tolerance'),\n      dueDate: t('nextWeek', 'Next week')\n    },\n    {\n      id: 3,\n      title: t('followVisualSchedule', 'Follow visual schedule'),\n      progress: 85,\n      target: t('independentTransitions', 'Independent transitions'),\n      dueDate: t('thisMonth', 'This month')\n    }\n  ];\n\n  const renderOverview = () => (\n    <div className=\"space-y-6\">\n      {/* Patient Info Card */}\n      <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-2xl font-bold mb-2\">{currentPatient.name}</h2>\n            <div className=\"flex items-center space-x-4 text-blue-100\">\n              <span>👶 {t('age', 'Age')}: {currentPatient.age}</span>\n              <span>🧩 {t('diagnosis', 'Diagnosis')}: {t(currentPatient.primaryDiagnosis, currentPatient.primaryDiagnosis)}</span>\n              <span>💬 {t('communication', 'Communication')}: {t(currentPatient.communicationLevel, currentPatient.communicationLevel)}</span>\n            </div>\n          </div>\n          <div className=\"text-6xl opacity-20\">\n            🧩\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {quickStats.map((stat, index) => (\n          <div key={index} className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{stat.title}</p>\n                <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{stat.value}</p>\n                <p className={`text-sm ${\n                  stat.changeType === 'positive' ? 'text-green-600' : \n                  stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'\n                }`}>\n                  {stat.change}\n                </p>\n              </div>\n              <div className=\"text-3xl\">{stat.icon}</div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Recent Activities and Goals */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Activities */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('recentActivities', 'Recent Activities')}\n          </h3>\n          <div className=\"space-y-4\">\n            {recentActivities.map((activity) => (\n              <div key={activity.id} className=\"flex items-start space-x-3\">\n                <div className={`text-xl ${activity.color}`}>{activity.icon}</div>\n                <div className=\"flex-1\">\n                  <p className=\"font-medium text-gray-900 dark:text-white\">{activity.title}</p>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">{activity.description}</p>\n                  <p className=\"text-xs text-gray-500 dark:text-gray-500\">{activity.time}</p>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Upcoming Goals */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('upcomingGoals', 'Upcoming Goals')}\n          </h3>\n          <div className=\"space-y-4\">\n            {upcomingGoals.map((goal) => (\n              <div key={goal.id} className=\"space-y-2\">\n                <div className=\"flex items-center justify-between\">\n                  <p className=\"font-medium text-gray-900 dark:text-white\">{goal.title}</p>\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">{goal.progress}%</span>\n                </div>\n                <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div \n                    className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                    style={{ width: `${goal.progress}%` }}\n                  />\n                </div>\n                <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400\">\n                  <span>{goal.target}</span>\n                  <span>{goal.dueDate}</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderContent = () => {\n    switch (activeSection) {\n      case 'overview':\n        return renderOverview();\n      case 'profile':\n        return <SpecialNeedsPatientProfile patient={currentPatient} onUpdate={setCurrentPatient} />;\n      case 'communication':\n        return <CommunicationBoard onMessageSelect={(message) => console.log('Message:', message)} />;\n      case 'sensory':\n        return <SensoryEnvironmentControls patientProfile={currentPatient} onEnvironmentChange={(env) => console.log('Environment:', env)} />;\n      case 'behavior':\n        return <BehavioralTracking patientId={currentPatient.id} onBehaviorLog={(log) => console.log('Behavior log:', log)} />;\n      case 'treatment':\n        return <AdaptiveTreatmentPlan patientProfile={currentPatient} onPlanUpdate={(plan) => console.log('Treatment plan:', plan)} />;\n      case 'schedule':\n        return <VisualSchedule patientProfile={currentPatient} sessionActivities={[]} onScheduleUpdate={(schedule) => console.log('Schedule:', schedule)} />;\n      case 'progress':\n        return <ProgressVisualization patientData={currentPatient} goals={[]} behaviorLogs={[]} />;\n      case 'emergency':\n        return <EmergencyProtocols patientProfile={currentPatient} onEmergencyLog={(log) => console.log('Emergency log:', log)} />;\n      default:\n        return renderOverview();\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n            {t('specialNeedsTherapy', 'Special Needs Physical Therapy')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('specialNeedsDesc', 'Comprehensive care for patients with special needs')}\n          </p>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"mb-8\">\n          <nav className=\"flex space-x-4 overflow-x-auto pb-2\">\n            {sections.map((section) => (\n              <button\n                key={section.id}\n                onClick={() => setActiveSection(section.id)}\n                className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${\n                  activeSection === section.id\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'\n                }`}\n              >\n                <span>{section.icon}</span>\n                <span className=\"font-medium\">{section.title}</span>\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Content */}\n        <div className=\"mb-8\">\n          {renderContent()}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SpecialNeedsDashboard;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst AdaptiveTreatmentPlan = ({ patientProfile, onPlanUpdate }) => {\n  const { t } = useLanguage();\n  const [activeTab, setActiveTab] = useState('goals');\n  const [treatmentPlan, setTreatmentPlan] = useState({\n    goals: [],\n    activities: [],\n    accommodations: [],\n    schedule: {},\n    progressMetrics: []\n  });\n\n  const goalCategories = {\n    motor: {\n      label: t('motorSkills', 'Motor Skills'),\n      icon: '🏃',\n      color: 'bg-blue-100 border-blue-300',\n      goals: [\n        { id: 'gross_motor', label: t('grossMotor', 'Gross Motor Development'), description: t('grossMotorDesc', 'Large muscle movements and coordination') },\n        { id: 'fine_motor', label: t('fineMotor', 'Fine Motor Development'), description: t('fineMotorDesc', 'Small muscle control and dexterity') },\n        { id: 'balance', label: t('balance', 'Balance and Stability'), description: t('balanceDesc', 'Postural control and equilibrium') },\n        { id: 'coordination', label: t('coordination', 'Coordination'), description: t('coordinationDesc', 'Movement planning and execution') }\n      ]\n    },\n    sensory: {\n      label: t('sensoryIntegration', 'Sensory Integration'),\n      icon: '👁️',\n      color: 'bg-purple-100 border-purple-300',\n      goals: [\n        { id: 'tactile_processing', label: t('tactileProcessing', 'Tactile Processing'), description: t('tactileDesc', 'Touch sensitivity and discrimination') },\n        { id: 'vestibular_processing', label: t('vestibularProcessing', 'Vestibular Processing'), description: t('vestibularDesc', 'Movement and balance processing') },\n        { id: 'proprioceptive', label: t('proprioceptive', 'Proprioceptive Awareness'), description: t('proprioceptiveDesc', 'Body position awareness') },\n        { id: 'sensory_modulation', label: t('sensoryModulation', 'Sensory Modulation'), description: t('sensoryModulationDesc', 'Regulating sensory input') }\n      ]\n    },\n    communication: {\n      label: t('communication', 'Communication'),\n      icon: '💬',\n      color: 'bg-green-100 border-green-300',\n      goals: [\n        { id: 'verbal_expression', label: t('verbalExpression', 'Verbal Expression'), description: t('verbalExpressionDesc', 'Speaking and articulation') },\n        { id: 'nonverbal_communication', label: t('nonverbalCommunication', 'Non-verbal Communication'), description: t('nonverbalDesc', 'Gestures and body language') },\n        { id: 'aac_usage', label: t('aacUsage', 'AAC Device Usage'), description: t('aacUsageDesc', 'Alternative communication methods') },\n        { id: 'social_communication', label: t('socialCommunication', 'Social Communication'), description: t('socialCommDesc', 'Interactive communication skills') }\n      ]\n    },\n    behavioral: {\n      label: t('behavioral', 'Behavioral'),\n      icon: '🎯',\n      color: 'bg-yellow-100 border-yellow-300',\n      goals: [\n        { id: 'self_regulation', label: t('selfRegulation', 'Self-Regulation'), description: t('selfRegulationDesc', 'Managing emotions and behaviors') },\n        { id: 'attention_focus', label: t('attentionFocus', 'Attention and Focus'), description: t('attentionDesc', 'Sustained attention skills') },\n        { id: 'social_skills', label: t('socialSkills', 'Social Skills'), description: t('socialSkillsDesc', 'Interaction with others') },\n        { id: 'independence', label: t('independence', 'Independence'), description: t('independenceDesc', 'Self-care and autonomy') }\n      ]\n    }\n  };\n\n  const adaptiveActivities = {\n    autism: [\n      { id: 'structured_play', name: t('structuredPlay', 'Structured Play'), duration: 15, sensoryLevel: 'low', description: t('structuredPlayDesc', 'Predictable play activities with clear rules') },\n      { id: 'sensory_breaks', name: t('sensoryBreaks', 'Sensory Breaks'), duration: 5, sensoryLevel: 'calming', description: t('sensoryBreaksDesc', 'Calming activities to regulate sensory input') },\n      { id: 'visual_schedules', name: t('visualSchedules', 'Visual Schedule Review'), duration: 10, sensoryLevel: 'low', description: t('visualSchedulesDesc', 'Review upcoming activities with pictures') },\n      { id: 'social_stories', name: t('socialStories', 'Social Stories'), duration: 10, sensoryLevel: 'low', description: t('socialStoriesDesc', 'Stories about social situations and expectations') }\n    ],\n    cerebral_palsy: [\n      { id: 'range_of_motion', name: t('rangeOfMotion', 'Range of Motion'), duration: 20, sensoryLevel: 'moderate', description: t('romDesc', 'Gentle stretching and joint mobility') },\n      { id: 'strength_training', name: t('strengthTraining', 'Strength Training'), duration: 15, sensoryLevel: 'moderate', description: t('strengthDesc', 'Muscle strengthening exercises') },\n      { id: 'gait_training', name: t('gaitTraining', 'Gait Training'), duration: 20, sensoryLevel: 'high', description: t('gaitDesc', 'Walking and mobility practice') },\n      { id: 'positioning', name: t('positioning', 'Positioning'), duration: 10, sensoryLevel: 'low', description: t('positioningDesc', 'Proper body positioning and support') }\n    ],\n    down_syndrome: [\n      { id: 'motor_planning', name: t('motorPlanning', 'Motor Planning'), duration: 15, sensoryLevel: 'moderate', description: t('motorPlanningDesc', 'Planning and executing movements') },\n      { id: 'balance_activities', name: t('balanceActivities', 'Balance Activities'), duration: 15, sensoryLevel: 'moderate', description: t('balanceActivitiesDesc', 'Static and dynamic balance training') },\n      { id: 'cognitive_motor', name: t('cognitiveMotor', 'Cognitive-Motor Tasks'), duration: 20, sensoryLevel: 'moderate', description: t('cognitiveMotorDesc', 'Thinking while moving activities') },\n      { id: 'functional_skills', name: t('functionalSkills', 'Functional Skills'), duration: 25, sensoryLevel: 'low', description: t('functionalSkillsDesc', 'Daily living movement skills') }\n    ]\n  };\n\n  const accommodationOptions = [\n    { id: 'extra_time', label: t('extraTime', 'Extra Time'), icon: '⏰', description: t('extraTimeDesc', 'Additional time for task completion') },\n    { id: 'visual_cues', label: t('visualCues', 'Visual Cues'), icon: '👁️', description: t('visualCuesDesc', 'Picture prompts and visual supports') },\n    { id: 'sensory_tools', label: t('sensoryTools', 'Sensory Tools'), icon: '🧸', description: t('sensoryToolsDesc', 'Fidgets, weighted items, sensory aids') },\n    { id: 'quiet_space', label: t('quietSpace', 'Quiet Space'), icon: '🤫', description: t('quietSpaceDesc', 'Low-stimulation environment option') },\n    { id: 'movement_breaks', label: t('movementBreaks', 'Movement Breaks'), icon: '🏃', description: t('movementBreaksDesc', 'Regular movement and stretch breaks') },\n    { id: 'simplified_instructions', label: t('simplifiedInstructions', 'Simplified Instructions'), icon: '📝', description: t('simplifiedDesc', 'Clear, step-by-step directions') },\n    { id: 'peer_support', label: t('peerSupport', 'Peer Support'), icon: '👥', description: t('peerSupportDesc', 'Buddy system or group activities') },\n    { id: 'assistive_technology', label: t('assistiveTechnology', 'Assistive Technology'), icon: '📱', description: t('assistiveTechDesc', 'Communication devices and mobility aids') }\n  ];\n\n  useEffect(() => {\n    if (patientProfile) {\n      generateAdaptivePlan(patientProfile);\n    }\n  }, [patientProfile]);\n\n  const generateAdaptivePlan = (profile) => {\n    const diagnosis = profile.primaryDiagnosis;\n    const suggestedActivities = adaptiveActivities[diagnosis] || [];\n    \n    // Generate goals based on diagnosis and profile\n    const suggestedGoals = generateGoalsForDiagnosis(diagnosis);\n    \n    // Generate accommodations based on sensory profile\n    const suggestedAccommodations = generateAccommodations(profile);\n    \n    setTreatmentPlan(prev => ({\n      ...prev,\n      activities: suggestedActivities,\n      goals: suggestedGoals,\n      accommodations: suggestedAccommodations\n    }));\n  };\n\n  const generateGoalsForDiagnosis = (diagnosis) => {\n    const goalMap = {\n      autism: ['sensory_modulation', 'social_communication', 'self_regulation', 'fine_motor'],\n      cerebral_palsy: ['gross_motor', 'balance', 'coordination', 'independence'],\n      down_syndrome: ['motor_planning', 'balance', 'cognitive_motor', 'social_skills'],\n      intellectual_disability: ['functional_skills', 'independence', 'social_skills', 'communication']\n    };\n    \n    return goalMap[diagnosis] || [];\n  };\n\n  const generateAccommodations = (profile) => {\n    const accommodations = [];\n    \n    if (profile.sensoryProfile === 'sensory_seeking') {\n      accommodations.push('sensory_tools', 'movement_breaks');\n    }\n    if (profile.sensoryProfile === 'sensory_avoiding') {\n      accommodations.push('quiet_space', 'visual_cues');\n    }\n    if (profile.communicationLevel === 'non_verbal') {\n      accommodations.push('visual_cues', 'assistive_technology');\n    }\n    \n    accommodations.push('extra_time', 'simplified_instructions');\n    \n    return accommodations;\n  };\n\n  const addGoal = (categoryId, goalId) => {\n    const category = goalCategories[categoryId];\n    const goal = category.goals.find(g => g.id === goalId);\n    \n    if (goal && !treatmentPlan.goals.find(g => g.id === goalId)) {\n      const newGoal = {\n        ...goal,\n        categoryId,\n        targetDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now\n        priority: 'medium',\n        progress: 0,\n        milestones: []\n      };\n      \n      setTreatmentPlan(prev => ({\n        ...prev,\n        goals: [...prev.goals, newGoal]\n      }));\n    }\n  };\n\n  const removeGoal = (goalId) => {\n    setTreatmentPlan(prev => ({\n      ...prev,\n      goals: prev.goals.filter(g => g.id !== goalId)\n    }));\n  };\n\n  const updateGoalProgress = (goalId, progress) => {\n    setTreatmentPlan(prev => ({\n      ...prev,\n      goals: prev.goals.map(g => \n        g.id === goalId ? { ...g, progress } : g\n      )\n    }));\n  };\n\n  const tabs = [\n    { id: 'goals', label: t('treatmentGoals', 'Treatment Goals'), icon: '🎯' },\n    { id: 'activities', label: t('activities', 'Activities'), icon: '🎮' },\n    { id: 'accommodations', label: t('accommodations', 'Accommodations'), icon: '🛠️' },\n    { id: 'schedule', label: t('schedule', 'Schedule'), icon: '📅' },\n    { id: 'progress', label: t('progress', 'Progress'), icon: '📊' }\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <span className=\"mr-3\">🎯</span>\n          {t('adaptiveTreatmentPlan', 'Adaptive Treatment Plan')}\n        </h2>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm font-medium\">\n            {patientProfile?.primaryDiagnosis ? t(patientProfile.primaryDiagnosis) : t('general', 'General')}\n          </span>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-8 overflow-x-auto\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <span>{tab.icon}</span>\n              <span>{tab.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"space-y-6\">\n        {activeTab === 'goals' && (\n          <div className=\"space-y-6\">\n            {/* Current Goals */}\n            {treatmentPlan.goals.length > 0 && (\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                  {t('currentGoals', 'Current Goals')}\n                </h3>\n                <div className=\"space-y-4\">\n                  {treatmentPlan.goals.map((goal) => (\n                    <div key={goal.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h4 className=\"font-medium text-gray-900 dark:text-white\">{goal.label}</h4>\n                        <button\n                          onClick={() => removeGoal(goal.id)}\n                          className=\"text-red-500 hover:text-red-700 text-sm\"\n                        >\n                          ✕\n                        </button>\n                      </div>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">{goal.description}</p>\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex-1 mr-4\">\n                          <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                            <span>{t('progress', 'Progress')}</span>\n                            <span>{goal.progress}%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                            <div \n                              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                              style={{ width: `${goal.progress}%` }}\n                            />\n                          </div>\n                        </div>\n                        <input\n                          type=\"range\"\n                          min=\"0\"\n                          max=\"100\"\n                          value={goal.progress}\n                          onChange={(e) => updateGoalProgress(goal.id, parseInt(e.target.value))}\n                          className=\"w-20\"\n                        />\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Add New Goals */}\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                {t('addNewGoals', 'Add New Goals')}\n              </h3>\n              <div className=\"space-y-6\">\n                {Object.entries(goalCategories).map(([categoryId, category]) => (\n                  <div key={categoryId} className={`p-4 border-2 border-dashed rounded-lg ${category.color}`}>\n                    <h4 className=\"font-medium text-gray-900 mb-3 flex items-center\">\n                      <span className=\"mr-2\">{category.icon}</span>\n                      {category.label}\n                    </h4>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                      {category.goals.map((goal) => (\n                        <button\n                          key={goal.id}\n                          onClick={() => addGoal(categoryId, goal.id)}\n                          disabled={treatmentPlan.goals.find(g => g.id === goal.id)}\n                          className={`p-3 text-left rounded-lg border transition-colors ${\n                            treatmentPlan.goals.find(g => g.id === goal.id)\n                              ? 'bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed'\n                              : 'bg-white border-gray-200 hover:border-gray-300 hover:bg-gray-50'\n                          }`}\n                        >\n                          <div className=\"font-medium text-sm text-gray-900\">{goal.label}</div>\n                          <div className=\"text-xs text-gray-600 mt-1\">{goal.description}</div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'activities' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('recommendedActivities', 'Recommended Activities')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {treatmentPlan.activities.map((activity) => (\n                <div key={activity.id} className=\"p-4 border border-gray-200 dark:border-gray-600 rounded-lg\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">{activity.name}</h4>\n                    <span className=\"text-sm text-gray-500\">{activity.duration} min</span>\n                  </div>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">{activity.description}</p>\n                  <div className=\"flex items-center justify-between\">\n                    <span className={`px-2 py-1 rounded text-xs font-medium ${\n                      activity.sensoryLevel === 'low' ? 'bg-green-100 text-green-800' :\n                      activity.sensoryLevel === 'moderate' ? 'bg-yellow-100 text-yellow-800' :\n                      activity.sensoryLevel === 'high' ? 'bg-red-100 text-red-800' :\n                      'bg-blue-100 text-blue-800'\n                    }`}>\n                      {t(activity.sensoryLevel, activity.sensoryLevel)} {t('sensory', 'sensory')}\n                    </span>\n                    <button className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\">\n                      {t('addToSession', 'Add to Session')}\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {activeTab === 'accommodations' && (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('recommendedAccommodations', 'Recommended Accommodations')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {accommodationOptions.map((accommodation) => (\n                <div\n                  key={accommodation.id}\n                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${\n                    treatmentPlan.accommodations.includes(accommodation.id)\n                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                  }`}\n                  onClick={() => {\n                    const isSelected = treatmentPlan.accommodations.includes(accommodation.id);\n                    setTreatmentPlan(prev => ({\n                      ...prev,\n                      accommodations: isSelected\n                        ? prev.accommodations.filter(a => a !== accommodation.id)\n                        : [...prev.accommodations, accommodation.id]\n                    }));\n                  }}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-2\">{accommodation.icon}</div>\n                    <div className=\"font-medium text-gray-900 dark:text-white mb-1\">{accommodation.label}</div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">{accommodation.description}</div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {(activeTab === 'schedule' || activeTab === 'progress') && (\n          <div className=\"text-center py-12\">\n            <div className=\"text-6xl mb-4\">\n              {activeTab === 'schedule' ? '📅' : '📊'}\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n              {activeTab === 'schedule' ? t('scheduleBuilder', 'Schedule Builder') : t('progressTracking', 'Progress Tracking')}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {activeTab === 'schedule' \n                ? t('scheduleComingSoon', 'Visual schedule builder coming soon...')\n                : t('progressComingSoon', 'Advanced progress tracking coming soon...')\n              }\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Save Button */}\n      <div className=\"mt-8 flex justify-end space-x-4\">\n        <button\n          onClick={() => generateAdaptivePlan(patientProfile)}\n          className=\"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n        >\n          {t('regeneratePlan', 'Regenerate Plan')}\n        </button>\n        <button\n          onClick={() => onPlanUpdate?.(treatmentPlan)}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n        >\n          {t('saveTreatmentPlan', 'Save Treatment Plan')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default AdaptiveTreatmentPlan;\n"], "names": ["_ref", "patient", "onUpdate", "t", "useLanguage", "activeTab", "setActiveTab", "useState", "diagnosisTypes", "id", "label", "icon", "communicationMethods", "sensoryPreferences", "options", "tabs", "_jsxs", "className", "children", "_jsx", "primaryDiagnosis", "map", "tab", "onClick", "concat", "diagnosis", "type", "name", "value", "method", "rows", "placeholder", "sensory", "option", "replace", "trigger", "strategy", "onMessageSelect", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "selectedMessages", "setSelectedMessages", "communicationCategories", "feelings", "color", "items", "text", "needs", "activities", "pain", "responses", "handleItemSelect", "item", "newMessage", "Date", "now", "category", "timestamp", "prev", "clearMessages", "length", "window", "utterance", "SpeechSynthesisUtterance", "lang", "speechSynthesis", "speak", "speakMessage", "m", "join", "message", "filter", "Object", "entries", "_ref2", "key", "_sensoryPresets$activ", "patientProfile", "onEnvironmentChange", "environment", "setEnvironment", "lighting", "brightness", "flashing", "sound", "volume", "enabled", "visual", "animations", "contrast", "colorScheme", "alerts", "audio", "vibration", "activePreset", "setActivePreset", "sensoryPresets", "autism_friendly", "description", "settings", "sensory_seeking", "calming", "focus", "soundOptions", "colorSchemes", "colors", "useEffect", "updateEnvironment", "_objectSpread", "preset", "preset<PERSON>ey", "applyPreset", "min", "max", "onChange", "e", "parseInt", "target", "checked", "_Fragment", "scheme", "index", "style", "backgroundColor", "patientId", "onBehaviorLog", "behaviorLog", "setBehaviorLog", "behavior", "intensity", "duration", "triggers", "interventions", "outcome", "notes", "behaviorTypes", "commonTriggers", "intensityLevels", "emoji", "toLocaleDateString", "handleBehaviorSelect", "behaviorType", "level", "handleTriggerToggle", "triggerId", "includes", "intervention", "handleInterventionToggle", "interventionId", "i", "saveBehaviorLog", "logEntry", "disabled", "sessionActivities", "onScheduleUpdate", "schedule", "setSchedule", "selectedActivity", "setSelectedActivity", "scheduleMode", "setScheduleMode", "activityLibrary", "arrival", "warmup", "exercise", "sensory_break", "play_therapy", "communication", "snack_time", "cleanup", "goodbye", "scheduleTemplates", "motor_focused", "communication_focused", "sensory_integration", "moveActivity", "itemId", "direction", "currentIndex", "findIndex", "newIndex", "newSchedule", "calculateStartTime", "lastActivity", "lastStartTime", "startTime", "hours", "minutes", "split", "Number", "lastEndTime", "setHours", "getHours", "toString", "padStart", "getMinutes", "getCompletionPercentage", "completed", "Math", "round", "reduce", "total", "activity", "mode", "template", "<PERSON><PERSON><PERSON>", "activityId", "applyTemplate", "values", "newScheduleItem", "addActivityToSchedule", "toggleActivityCompletion", "removeActivityFromSchedule", "updateActivityNotes", "width", "patientData", "goals", "behaviorLogs", "activeView", "setActiveView", "timeRange", "setTimeRange", "progressData", "progress", "milestones", "achieved", "date", "weeklyProgress", "week", "motor", "social", "behaviorTrends", "trend", "change", "achievements", "title", "earned", "views", "renderOverview", "goal", "a", "achievement", "view", "renderContent", "milestone", "toLowerCase", "onEmergencyLog", "activeProtocol", "setActiveProtocol", "emergencyLog", "setEmergencyLog", "severity", "emergencyTypes", "behavioral", "protocols", "steps", "medical", "severityLevels", "low", "medium", "high", "critical", "emergencyContacts", "phone", "role", "deactivateProtocol", "callEmergencyContact", "contact", "alert", "step", "logEmergency", "protocolUsed", "slice", "protocol", "activateProtocol", "SpecialNeedsDashboard", "activeSection", "setActiveSection", "currentPatient", "setCurrentPatient", "age", "communicationLevel", "sensoryProfile", "sections", "quickStats", "changeType", "recentActivities", "time", "upcomingGoals", "dueDate", "stat", "section", "SpecialNeedsPatientProfile", "CommunicationBoard", "console", "log", "SensoryEnvironmentControls", "env", "BehavioralTracking", "AdaptiveTreatmentPlan", "onPlanUpdate", "plan", "VisualSchedule", "ProgressVisualization", "EmergencyProtocols", "treatmentPlan", "setTreatmentPlan", "accommodations", "progressMetrics", "goalCategories", "adaptiveActivities", "autism", "sensoryLevel", "cerebral_palsy", "down_syndrome", "accommodationOptions", "generateAdaptivePlan", "profile", "suggestedActivities", "suggestedGoals", "generateGoalsForDiagnosis", "suggestedAccommodations", "generateAccommodations", "intellectual_disability", "push", "removeGoal", "goalId", "g", "updateGoalProgress", "categoryId", "addGoal", "find", "newGoal", "targetDate", "priority", "accommodation", "isSelected"], "sourceRoot": ""}