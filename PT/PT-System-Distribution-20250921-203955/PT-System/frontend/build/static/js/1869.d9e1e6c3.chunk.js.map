{"version": 3, "file": "static/js/1869.d9e1e6c3.chunk.js", "mappings": "wJAwZA,MAEA,EAFoB,IArZpB,MACEA,WAAAA,GACEC,KAAKC,QAAUC,+BACfF,KAAKG,cAAgB,cACrBH,KAAKI,mBACP,CAGAC,YAAAA,GACE,OAAOC,aAAaC,QAAQ,UAAY,YAC1C,CAGA,gBAAMC,CAAWC,GAAyB,IAAfC,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpC,MAAMG,EAAQd,KAAKK,eACbU,EAAG,GAAAC,OAAMhB,KAAKC,SAAOe,OAAGP,GAExBQ,EAAiB,CACrBC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADF,OAAYF,KAIzBK,GAAYC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbH,GACAP,GAAO,IACVQ,SAAOE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACFH,EAAeC,SACfR,EAAQQ,WAIf,IACE,MAAMG,QAAiBC,MAAMP,EAAKI,GAElC,IAAKE,EAASE,GACZ,MAAM,IAAIC,MAAM,uBAADR,OAAwBK,EAASI,SAIlD,aADmBJ,EAASK,MAE9B,CAAE,MAAOC,GAGP,OAFAC,QAAQC,KAAK,0BAADb,OAA2BP,EAAQ,kCAAkCkB,EAAMG,SAEhF9B,KAAK+B,uBAAuBtB,EAAUC,EAC/C,CACF,CAGAqB,sBAAAA,CAAuBtB,EAAUC,GAC/B,MAAMsB,EAAStB,EAAQsB,QAAU,MAC3BC,EAAOvB,EAAQwB,KAAOC,KAAKC,MAAM1B,EAAQwB,MAAQ,KAGvD,OAAIzB,EAAS4B,SAAS,YACbrC,KAAKsC,sBAAsB7B,EAAUuB,EAAQC,GAC3CxB,EAAS4B,SAAS,kBACpBrC,KAAKuC,4BAA4B9B,EAAUuB,EAAQC,GACjDxB,EAAS4B,SAAS,sBACpBrC,KAAKwC,uBAAuB/B,EAAUuB,EAAQC,GAC5CxB,EAAS4B,SAAS,oBACpBrC,KAAKyC,iBAAiBhC,EAAUuB,EAAQC,GAG1C,CAAES,SAAS,EAAOf,MAAO,0CAClC,CAGAvB,iBAAAA,GACOJ,KAAKO,QAAQ,iBAChBP,KAAK2C,QAAQ,eAAe,GAC5B3C,KAAK2C,QAAQ,WAAY3C,KAAK4C,sBAC9B5C,KAAK2C,QAAQ,cAAe,CAAC,GAC7B3C,KAAK2C,QAAQ,uBAAwB,IACrC3C,KAAK2C,QAAQ,mBAAoB,IACjC3C,KAAK2C,QAAQ,iBAAkB,IAEnC,CAGApC,OAAAA,CAAQsC,GACN,IACE,MAAMC,EAAOxC,aAAaC,QAAQP,KAAKG,cAAgB0C,GACvD,OAAOC,EAAOX,KAAKC,MAAMU,GAAQ,IACnC,CAAE,MAAOnB,GAEP,OADAC,QAAQD,MAAM,mCAAoCA,GAC3C,IACT,CACF,CAEAgB,OAAAA,CAAQE,EAAKE,GACX,IAEE,OADAzC,aAAaqC,QAAQ3C,KAAKG,cAAgB0C,EAAKV,KAAKa,UAAUD,KACvD,CACT,CAAE,MAAOpB,GAEP,OADAC,QAAQD,MAAM,iCAAkCA,IACzC,CACT,CACF,CAEAsB,UAAAA,CAAWJ,GACT,IAEE,OADAvC,aAAa2C,WAAWjD,KAAKG,cAAgB0C,IACtC,CACT,CAAE,MAAOlB,GAEP,OADAC,QAAQD,MAAM,oCAAqCA,IAC5C,CACT,CACF,CAGAiB,kBAAAA,GACE,MAAO,CACL,CACEM,GAAI,mBACJC,KAAM,iBACNC,OAAQ,oDACRC,IAAK,GACLC,OAAQ,OACRC,UAAW,iBACXC,YAAa,4EACbC,MAAO,gBACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,YAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAExB,CACEjB,GAAI,mBACJC,KAAM,gBACNC,OAAQ,gEACRC,IAAK,GACLC,OAAQ,SACRC,UAAW,qBACXC,YAAa,qGACbC,MAAO,cACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,OAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAG5B,CAGA,iBAAMC,GACJ,OAAO,IAAIC,QAASC,IAClBC,WAAW,KACTD,EAAQtE,KAAKO,QAAQ,aAAe,KACnC,MAEP,CAEA,gBAAMiE,CAAWC,GACf,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MACMG,GADW1E,KAAKO,QAAQ,aAAe,IACpBoE,KAAKC,GAAKA,EAAE1B,KAAOuB,GAC5CH,EAAQI,GAAW,OAClB,MAEP,CAGA,qBAAMG,CAAgBJ,EAAWK,GAC/B,IACE,MAAMzD,QAAiBrB,KAAKQ,WAAW,WAAY,CACjDwB,OAAQ,OACRE,KAAMC,KAAKa,WAAS5B,EAAAA,EAAAA,GAAC,CACnBqD,aACGK,MAGP,OAAOzD,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvDwE,EAAeN,IAAUrD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpB0D,GAAW,IACdE,WAAW,IAAId,MAAOC,gBAExBnE,KAAK2C,QAAQ,cAAeoC,GAC5BT,GAAQ,IACP,MAEP,CACF,CAEA,oBAAMW,CAAeR,GACnB,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvD+D,EAAQS,EAAeN,IAAc,OACpC,MAEP,CAGA,iBAAMS,CAAYC,GAChB,IACE,MAAM9D,QAAiBrB,KAAKQ,WAAW,iBAAkB,CACvDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUmC,KAEvB,OAAO9D,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMa,EAAUpF,KAAKO,QAAQ,yBAA2B,GAClDuB,GAAOV,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZH,GAAW,IACdH,WAAW,IAAId,MAAOC,cACtB1C,OAAQ,SAEV2D,EAAQG,KAAKzD,GACb9B,KAAK2C,QAAQ,uBAAwByC,GACrCd,EAAQxC,IACP,MAEP,CACF,CAEA,6BAAM0D,CAAwBf,GAC5B,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,kBAADQ,OAAmByD,IACzD,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACMkB,GADUzF,KAAKO,QAAQ,yBAA2B,IACzBmF,OAAOC,GAAOA,EAAIlB,YAAcA,GAC/DH,EAAQmB,IACP,MAEP,CACF,CAGA,yBAAMG,CAAoBC,GACxB,IACE,MAAMxE,QAAiBrB,KAAKQ,WAAW,qBAAsB,CAC3DwB,OAAQ,OACRE,KAAMC,KAAKa,UAAU6C,KAEvB,OAAOxE,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMuB,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CwF,GAAO3E,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZO,GAAW,IACd5B,WAAW,IAAIC,MAAOC,gBAExB2B,EAASP,KAAKQ,GACd/F,KAAK2C,QAAQ,mBAAoBmD,GACjCxB,EAAQyB,IACP,MAEP,CACF,CAEA,yBAAMC,CAAoBvB,GACxB,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,sBAADQ,OAAuByD,IAC7D,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACM0B,GADWjG,KAAKO,QAAQ,qBAAuB,IACpBmF,OAAOQ,GAAQA,EAAKzB,YAAcA,GACnEH,EAAQ2B,IACP,MAEP,CACF,CAGA,uBAAME,CAAkBC,GACtB,IACE,MAAM/E,QAAiBrB,KAAKQ,WAAW,mBAAoB,CACzDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUoD,KAEvB,OAAO/E,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjD+F,GAAWlF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACf8B,GAAIgB,KAAKmB,MAAMC,YACZc,GAAe,IAClBpB,WAAW,IAAId,MAAOC,gBAExBkC,EAAad,KAAKe,GAClBtG,KAAK2C,QAAQ,iBAAkB0D,GAC/B/B,EAAQgC,IACP,MAEP,CACF,CAEA,uBAAMC,CAAkB9B,GACtB,IACE,MAAMhE,EAAWgE,EAAS,oBAAAzD,OAAuByD,GAAc,mBACzDpD,QAAiBrB,KAAKQ,WAAWC,GACvC,OAAOY,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDiG,EAAsB/B,EACxB4B,EAAaX,OAAOe,GAAOA,EAAIhC,YAAcA,GAC7C4B,EACJ/B,EAAQkC,IACP,MAEP,CACF,CAGA,kBAAME,GACJ,OAAO,IAAIrC,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDoG,EAAiB3G,KAAKO,QAAQ,yBAA2B,GACzDuF,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CuE,EAAc9E,KAAKO,QAAQ,gBAAkB,CAAC,EAE9CqG,EAAY,CAChBC,eAAgBR,EAAazF,OAC7BkG,oBAAqBH,EAAe/F,OACpCmG,sBAAuBjB,EAASlF,OAChCoG,wBAAyBC,OAAOC,KAAKpC,GAAalE,OAClDuG,eAAgB,IACXd,EAAae,OAAO,GAAGC,IAAIC,IAAClG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,MAASD,OAClDX,EAAeS,OAAO,GAAGC,IAAIG,IAACpG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,iBAAoBC,OAC/D1B,EAASsB,OAAO,GAAGC,IAAIzC,IAACxD,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,YAAe3C,KACvD6C,KAAK,CAACC,EAAGC,IAAM,IAAIzD,KAAKyD,EAAE3C,WAAa,IAAId,KAAKwD,EAAE1C,YAAYoC,MAAM,EAAG,KAG3E9C,EAAQsC,IACP,MAEP,CAGAgB,YAAAA,GACe,CAAC,WAAY,cAAe,uBAAwB,mBAAoB,kBAChFC,QAAQhF,GAAO7C,KAAKiD,WAAWJ,IACpC7C,KAAKiD,WAAW,eAChBjD,KAAKI,mBACP,CAGA0H,UAAAA,GASE,MARa,CACXC,SAAU/H,KAAKO,QAAQ,YACvBuE,YAAa9E,KAAKO,QAAQ,eAC1ByH,qBAAsBhI,KAAKO,QAAQ,wBACnC0H,iBAAkBjI,KAAKO,QAAQ,oBAC/B2H,eAAgBlI,KAAKO,QAAQ,kBAC7B4H,YAAY,IAAIjE,MAAOC,cAG3B,CAGAiE,UAAAA,CAAWnG,GACT,IAME,OALIA,EAAK8F,UAAU/H,KAAK2C,QAAQ,WAAYV,EAAK8F,UAC7C9F,EAAK6C,aAAa9E,KAAK2C,QAAQ,cAAeV,EAAK6C,aACnD7C,EAAK+F,sBAAsBhI,KAAK2C,QAAQ,uBAAwBV,EAAK+F,sBACrE/F,EAAKgG,kBAAkBjI,KAAK2C,QAAQ,mBAAoBV,EAAKgG,kBAC7DhG,EAAKiG,gBAAgBlI,KAAK2C,QAAQ,iBAAkBV,EAAKiG,iBACtD,CACT,CAAE,MAAOvG,GAEP,OADAC,QAAQD,MAAM,wBAAyBA,IAChC,CACT,CACF,CAGA0G,KAAAA,GAAiB,IAAXC,EAAE3H,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,IACT,OAAO,IAAI0D,QAAQC,GAAWC,WAAWD,EAASgE,GACpD,E,uHC7YF,MA+UA,EA/UwBC,KACtB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,UAAEjE,IAAckE,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OACVnE,EAASoE,IAAcC,EAAAA,EAAAA,UAAS,OAChCb,EAAgBc,IAAqBD,EAAAA,EAAAA,UAAS,KAC9CE,EAASC,IAAcH,EAAAA,EAAAA,WAAS,IAChCI,EAAWC,IAAgBL,EAAAA,EAAAA,UAAS,cACpCM,EAAOC,IAAYP,EAAAA,EAAAA,UAAS,CACjCQ,aAAc,EACdC,yBAA0B,EAC1BC,sBAAuB,EACvBC,iBAAkB,KAGpBC,EAAAA,EAAAA,WAAU,KACRC,IACAC,KACC,CAACpF,IAEJ,MAAMmF,EAAkBE,UACtB,GAAIrF,EACF,IACE,MAAMsF,QAAoBC,EAAAA,EAAYxF,WAAWC,GACjDqE,EAAWiB,EACb,CAAE,MAAOpI,GACPC,QAAQD,MAAM,yBAA0BA,GACxCsI,EAAAA,GAAMtI,MAAM6G,EAAE,sBAAuB,8BACvC,CAEFU,GAAW,IAGPW,EAAqBC,UACzB,IACE,MAAMzD,QAAqB2D,EAAAA,EAAYzD,kBAAkB9B,GACzDuE,EAAkB3C,GAGlB,MAAMgD,EAAQ,CACZE,aAAclD,EAAazF,OAC3B4I,yBAA0BnD,EAAaX,OAAO4B,GAAgB,cAAXA,EAAEC,MAAsB3G,OAC3E6I,sBAAuBpD,EAAaX,OAAO4B,GAAgB,aAAXA,EAAEC,MAAqB3G,OACvE8I,iBAAkBrD,EAAaX,OAAO4B,GAAgB,aAAXA,EAAEC,MAAqB3G,QAEpE0I,EAASD,EACX,CAAE,MAAO1H,GACPC,QAAQD,MAAM,iCAAkCA,EAClD,GAGIuI,EAAsB3C,IAC1B,OAAQA,GACN,IAAK,YAAa,MAAO,qBACzB,IAAK,WAAY,MAAO,kBACxB,IAAK,WAAY,MAAO,oBACxB,IAAK,gBAAiB,MAAO,sBAC7B,QAAS,MAAO,iBAId4C,EAAuB5C,IAC3B,OAAQA,GACN,IAAK,YAAa,MAAO,gBACzB,IAAK,WAAY,MAAO,kBACxB,IAAK,WAAY,MAAO,iBACxB,IAAK,gBAAiB,MAAO,kBAC7B,QAAS,MAAO,kBAId6C,EAAmBC,IACvB,OAAQA,GACN,IAAK,SAAU,MAAO,gBACtB,IAAK,UAAW,MAAO,eACvB,IAAK,QAAS,MAAO,gBACrB,QAAS,MAAO,iBAIpB,OAAIpB,GAEAqB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6DAA4DC,SAAA,EAEzEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oFAAmFC,UAChGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAM9B,GAAU,GACzB2B,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,mDAAkDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACZ/B,EAAE,cAAe,+BAEnB9D,IACC+F,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wCAAuCC,SAAA,CACjDhC,EAAE,UAAW,WAAW,KAAGC,EAAQ/D,EAAQtB,OAASsB,EAAQvB,KAC5DuB,EAAQnB,YACP+G,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+DAA8DC,SAC3E/B,EAAQ/D,EAAQlB,YAAckB,EAAQnB,sBAOnD+G,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2EAA0EC,SAAA,EACxFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAAyB,gBAGxCE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,uEAAsEC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAwB,cAGvCE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,uEAAsEC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBAAyB,8BAWpDD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEC,UACtFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMtB,EAAa,aAC5BmB,UAAS,8DAAAvJ,OACO,cAAdmI,EACI,oCACA,8EACHqB,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BACZ/B,EAAE,cAAe,oBAEpBiC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMtB,EAAa,gBAC5BmB,UAAS,8DAAAvJ,OACO,iBAAdmI,EACI,oCACA,8EACHqB,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZ/B,EAAE,eAAgB,gBAAgB,KAAGN,EAAetH,OAAO,eAMpE0J,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,CAClD,cAAdrB,IACCsB,EAAAA,EAAAA,MAAAE,EAAAA,SAAA,CAAAH,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhC,EAAE,uBAAwB,6BAE7B8B,EAAAA,EAAAA,KAACM,EAAAA,EAAoB,OAIV,iBAAdzB,IACCsB,EAAAA,EAAAA,MAAAE,EAAAA,SAAA,CAAAH,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEhC,EAAE,uBAAwB,4BAG5BN,EAAetH,OAAS,GACvB0J,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBtC,EAAeb,IAAI,CAACf,EAAauE,KAChCJ,EAAAA,EAAAA,MAAA,OAEEF,UAAU,6DAA4DC,SAAA,EAEtEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAvJ,OAAKkJ,EAAmB5D,EAAYiB,MAAK,KAAAvG,OAAImJ,EAAoB7D,EAAYiB,MAAK,eAC9FkD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,4CAA2CC,SAAA,CACjC,cAArBlE,EAAYiB,MAAwBiB,EAAE,0BAA2B,4BAC5C,aAArBlC,EAAYiB,MAAuBiB,EAAE,uBAAwB,yBACxC,aAArBlC,EAAYiB,MAAuBiB,EAAE,mBAAoB,qBACpC,kBAArBlC,EAAYiB,MAA4BiB,EAAE,oBAAqB,uBAC9D,CAAC,YAAa,WAAY,WAAY,iBAAiBnG,SAASiE,EAAYiB,OAASiB,EAAE,UAAW,gBAEtGiC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAvJ,OAAKoJ,EAAgB9D,EAAY+D,UAAS,eACtDC,EAAAA,EAAAA,KAAA,QAAAE,SAAOlE,EAAY+D,UAAY,YAC/BC,EAAAA,EAAAA,KAAA,QAAAE,SAAM,YACNF,EAAAA,EAAAA,KAAA,QAAAE,SAAO,IAAItG,KAAKoC,EAAYtB,WAAW8F,+BAM/CL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4DAA2DC,SAAA,CACvEhC,EAAE,QAAS,SAAS,QAEvB8B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDlE,EAAYyE,QAAUzE,EAAY0E,OAASxC,EAAE,kBAAmB,2BAIrEiC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4DAA2DC,SAAA,CACvEhC,EAAE,aAAc,eAAe,QAElC8B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDlE,EAAYjF,UAAYmH,EAAE,qBAAsB,+BAtChDqC,OA6CXJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8CACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEhC,EAAE,iBAAkB,6BAEvB8B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDhC,EAAE,eAAgB,4DAErB8B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BACZ/B,EAAE,iBAAkB,kGAWrCiC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCACZ/B,EAAE,eAAgB,sBAGrBiC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDhC,EAAE,eAAgB,oBAErB8B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDC,SAClEnB,EAAME,mBAIXkB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDhC,EAAE,aAAc,oBAGrB8B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAChEnB,EAAMG,+BAIXiB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDhC,EAAE,YAAa,mBAGpB8B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAChEnB,EAAMI,4BAIXgB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sCACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDhC,EAAE,WAAY,kBAGnB8B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAChEnB,EAAMK,gCAQjBe,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yGAAwGC,SAAA,EACrHC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gEAA+DC,SAAA,EAC3EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZ/B,EAAE,oBAAqB,8BAE1B8B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CC,SACxDhC,EAAE,kBAAmB,+G", "sources": ["services/dataService.js", "pages/AI/AIAssistantPage.jsx"], "sourcesContent": ["// Data Service for PhysioFlow\n// Handles API calls with localStorage fallback for demonstration purposes\n\nclass DataService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n    this.storagePrefix = 'physioflow_';\n    this.initializeStorage();\n  }\n\n  // Helper method to get auth token\n  getAuthToken() {\n    return localStorage.getItem('token') || 'demo-token';\n  }\n\n  // Helper method to make API requests\n  async apiRequest(endpoint, options = {}) {\n    const token = this.getAuthToken();\n    const url = `${this.baseURL}${endpoint}`;\n\n    const defaultOptions = {\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      }\n    };\n\n    const finalOptions = {\n      ...defaultOptions,\n      ...options,\n      headers: {\n        ...defaultOptions.headers,\n        ...options.headers\n      }\n    };\n\n    try {\n      const response = await fetch(url, finalOptions);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.warn(`API request failed for ${endpoint}, using localStorage fallback:`, error.message);\n      // Fallback to localStorage for demo purposes\n      return this.fallbackToLocalStorage(endpoint, options);\n    }\n  }\n\n  // Fallback to localStorage when API is not available\n  fallbackToLocalStorage(endpoint, options) {\n    const method = options.method || 'GET';\n    const data = options.body ? JSON.parse(options.body) : null;\n\n    // Handle different endpoints with existing localStorage methods\n    if (endpoint.includes('/bodymap')) {\n      return this.handleBodyMapFallback(endpoint, method, data);\n    } else if (endpoint.includes('/communication')) {\n      return this.handleCommunicationFallback(endpoint, method, data);\n    } else if (endpoint.includes('/exercise-programs')) {\n      return this.handleExerciseFallback(endpoint, method, data);\n    } else if (endpoint.includes('/ai-interactions')) {\n      return this.handleAIFallback(endpoint, method, data);\n    }\n\n    return { success: false, error: 'Endpoint not supported in fallback mode' };\n  }\n\n  // Initialize storage with demo data\n  initializeStorage() {\n    if (!this.getItem('initialized')) {\n      this.setItem('initialized', true);\n      this.setItem('patients', this.getDefaultPatients());\n      this.setItem('bodyMapData', {});\n      this.setItem('communicationHistory', []);\n      this.setItem('exercisePrograms', []);\n      this.setItem('aiInteractions', []);\n    }\n  }\n\n  // Storage helpers\n  getItem(key) {\n    try {\n      const item = localStorage.getItem(this.storagePrefix + key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Error getting item from storage:', error);\n      return null;\n    }\n  }\n\n  setItem(key, value) {\n    try {\n      localStorage.setItem(this.storagePrefix + key, JSON.stringify(value));\n      return true;\n    } catch (error) {\n      console.error('Error setting item in storage:', error);\n      return false;\n    }\n  }\n\n  removeItem(key) {\n    try {\n      localStorage.removeItem(this.storagePrefix + key);\n      return true;\n    } catch (error) {\n      console.error('Error removing item from storage:', error);\n      return false;\n    }\n  }\n\n  // Default demo data\n  getDefaultPatients() {\n    return [\n      {\n        id: 'demo-patient-001',\n        name: 'Ahmed Mohammed',\n        nameAr: 'أحمد محمد',\n        age: 28,\n        gender: 'male',\n        condition: 'Cerebral Palsy',\n        conditionAr: 'الشلل الدماغي',\n        phone: '+966501234567',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'whatsapp'],\n          language: 'en',\n          quietHours: { start: '22:00', end: '08:00' }\n        },\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 'demo-patient-002',\n        name: 'Sarah Johnson',\n        nameAr: 'سارة جونسون',\n        age: 35,\n        gender: 'female',\n        condition: 'Spinal Cord Injury',\n        conditionAr: 'إصابة الحبل الشوكي',\n        phone: '+**********',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'sms'],\n          language: 'en',\n          quietHours: { start: '21:00', end: '07:00' }\n        },\n        createdAt: new Date().toISOString()\n      }\n    ];\n  }\n\n  // Patient data methods\n  async getPatients() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve(this.getItem('patients') || []);\n      }, 100);\n    });\n  }\n\n  async getPatient(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const patients = this.getItem('patients') || [];\n        const patient = patients.find(p => p.id === patientId);\n        resolve(patient || null);\n      }, 100);\n    });\n  }\n\n  // Body Map data methods\n  async saveBodyMapData(patientId, bodyMapData) {\n    try {\n      const response = await this.apiRequest('/bodymap', {\n        method: 'POST',\n        body: JSON.stringify({\n          patientId,\n          ...bodyMapData\n        })\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const allBodyMapData = this.getItem('bodyMapData') || {};\n          allBodyMapData[patientId] = {\n            ...bodyMapData,\n            timestamp: new Date().toISOString()\n          };\n          this.setItem('bodyMapData', allBodyMapData);\n          resolve(true);\n        }, 200);\n      });\n    }\n  }\n\n  async getBodyMapData(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const allBodyMapData = this.getItem('bodyMapData') || {};\n        resolve(allBodyMapData[patientId] || null);\n      }, 100);\n    });\n  }\n\n  // Communication methods\n  async sendMessage(messageData) {\n    try {\n      const response = await this.apiRequest('/communication', {\n        method: 'POST',\n        body: JSON.stringify(messageData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const message = {\n            id: Date.now().toString(),\n            ...messageData,\n            timestamp: new Date().toISOString(),\n            status: 'sent'\n          };\n          history.push(message);\n          this.setItem('communicationHistory', history);\n          resolve(message);\n        }, 500);\n      });\n    }\n  }\n\n  async getCommunicationHistory(patientId) {\n    try {\n      const response = await this.apiRequest(`/communication/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const patientHistory = history.filter(msg => msg.patientId === patientId);\n          resolve(patientHistory);\n        }, 100);\n      });\n    }\n  }\n\n  // Exercise Program methods\n  async saveExerciseProgram(programData) {\n    try {\n      const response = await this.apiRequest('/exercise-programs', {\n        method: 'POST',\n        body: JSON.stringify(programData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const program = {\n            id: Date.now().toString(),\n            ...programData,\n            createdAt: new Date().toISOString()\n          };\n          programs.push(program);\n          this.setItem('exercisePrograms', programs);\n          resolve(program);\n        }, 300);\n      });\n    }\n  }\n\n  async getExercisePrograms(patientId) {\n    try {\n      const response = await this.apiRequest(`/exercise-programs/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const patientPrograms = programs.filter(prog => prog.patientId === patientId);\n          resolve(patientPrograms);\n        }, 100);\n      });\n    }\n  }\n\n  // AI Interaction methods\n  async saveAIInteraction(interactionData) {\n    try {\n      const response = await this.apiRequest('/ai-interactions', {\n        method: 'POST',\n        body: JSON.stringify(interactionData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const interaction = {\n            id: Date.now().toString(),\n            ...interactionData,\n            timestamp: new Date().toISOString()\n          };\n          interactions.push(interaction);\n          this.setItem('aiInteractions', interactions);\n          resolve(interaction);\n        }, 100);\n      });\n    }\n  }\n\n  async getAIInteractions(patientId) {\n    try {\n      const endpoint = patientId ? `/ai-interactions/${patientId}` : '/ai-interactions';\n      const response = await this.apiRequest(endpoint);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const patientInteractions = patientId\n            ? interactions.filter(int => int.patientId === patientId)\n            : interactions;\n          resolve(patientInteractions);\n        }, 100);\n      });\n    }\n  }\n\n  // Analytics methods\n  async getAnalytics() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const interactions = this.getItem('aiInteractions') || [];\n        const communications = this.getItem('communicationHistory') || [];\n        const programs = this.getItem('exercisePrograms') || [];\n        const bodyMapData = this.getItem('bodyMapData') || {};\n\n        const analytics = {\n          totalAIQueries: interactions.length,\n          totalCommunications: communications.length,\n          totalExercisePrograms: programs.length,\n          totalBodyMapAssessments: Object.keys(bodyMapData).length,\n          recentActivity: [\n            ...interactions.slice(-5).map(i => ({ type: 'ai', ...i })),\n            ...communications.slice(-5).map(c => ({ type: 'communication', ...c })),\n            ...programs.slice(-5).map(p => ({ type: 'exercise', ...p }))\n          ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10)\n        };\n\n        resolve(analytics);\n      }, 200);\n    });\n  }\n\n  // Clear all demo data\n  clearAllData() {\n    const keys = ['patients', 'bodyMapData', 'communicationHistory', 'exercisePrograms', 'aiInteractions'];\n    keys.forEach(key => this.removeItem(key));\n    this.removeItem('initialized');\n    this.initializeStorage();\n  }\n\n  // Export data for backup\n  exportData() {\n    const data = {\n      patients: this.getItem('patients'),\n      bodyMapData: this.getItem('bodyMapData'),\n      communicationHistory: this.getItem('communicationHistory'),\n      exercisePrograms: this.getItem('exercisePrograms'),\n      aiInteractions: this.getItem('aiInteractions'),\n      exportedAt: new Date().toISOString()\n    };\n    return data;\n  }\n\n  // Import data from backup\n  importData(data) {\n    try {\n      if (data.patients) this.setItem('patients', data.patients);\n      if (data.bodyMapData) this.setItem('bodyMapData', data.bodyMapData);\n      if (data.communicationHistory) this.setItem('communicationHistory', data.communicationHistory);\n      if (data.exercisePrograms) this.setItem('exercisePrograms', data.exercisePrograms);\n      if (data.aiInteractions) this.setItem('aiInteractions', data.aiInteractions);\n      return true;\n    } catch (error) {\n      console.error('Error importing data:', error);\n      return false;\n    }\n  }\n\n  // Simulate API delay\n  delay(ms = 100) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n\n// Create singleton instance\nconst dataService = new DataService();\n\nexport default dataService;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { AIAnalyticsDashboard } from '../../components/AI/AIAssistant';\nimport dataService from '../../services/dataService';\nimport toast from 'react-hot-toast';\n\nconst AIAssistantPage = () => {\n  const { t, isRTL } = useLanguage();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n  const [patient, setPatient] = useState(null);\n  const [aiInteractions, setAiInteractions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('analytics');\n  const [stats, setStats] = useState({\n    totalQueries: 0,\n    treatmentRecommendations: 0,\n    exerciseModifications: 0,\n    progressAnalyses: 0\n  });\n\n  useEffect(() => {\n    loadPatientData();\n    loadAIInteractions();\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    if (patientId) {\n      try {\n        const patientData = await dataService.getPatient(patientId);\n        setPatient(patientData);\n      } catch (error) {\n        console.error('Error loading patient:', error);\n        toast.error(t('errorLoadingPatient', 'Error loading patient data'));\n      }\n    }\n    setLoading(false);\n  };\n\n  const loadAIInteractions = async () => {\n    try {\n      const interactions = await dataService.getAIInteractions(patientId);\n      setAiInteractions(interactions);\n      \n      // Calculate stats\n      const stats = {\n        totalQueries: interactions.length,\n        treatmentRecommendations: interactions.filter(i => i.type === 'treatment').length,\n        exerciseModifications: interactions.filter(i => i.type === 'exercise').length,\n        progressAnalyses: interactions.filter(i => i.type === 'progress').length\n      };\n      setStats(stats);\n    } catch (error) {\n      console.error('Error loading AI interactions:', error);\n    }\n  };\n\n  const getInteractionIcon = (type) => {\n    switch (type) {\n      case 'treatment': return 'fas fa-stethoscope';\n      case 'exercise': return 'fas fa-dumbbell';\n      case 'progress': return 'fas fa-chart-line';\n      case 'documentation': return 'fas fa-file-medical';\n      default: return 'fas fa-brain';\n    }\n  };\n\n  const getInteractionColor = (type) => {\n    switch (type) {\n      case 'treatment': return 'text-blue-600';\n      case 'exercise': return 'text-purple-600';\n      case 'progress': return 'text-green-600';\n      case 'documentation': return 'text-orange-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const getProviderIcon = (provider) => {\n    switch (provider) {\n      case 'gemini': return 'fab fa-google';\n      case 'chatgpt': return 'fas fa-robot';\n      case 'local': return 'fas fa-server';\n      default: return 'fas fa-brain';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"ai-assistant-page min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => navigate(-1)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-arrow-left text-xl\"></i>\n                </button>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-brain mr-3 text-orange-600\"></i>\n                    {t('aiAssistant', 'AI Assistant & Analytics')}\n                  </h1>\n                  {patient && (\n                    <p className=\"text-gray-600 dark:text-gray-300 mt-1\">\n                      {t('patient', 'Patient')}: {isRTL ? patient.nameAr : patient.name}\n                      {patient.condition && (\n                        <span className=\"ml-2 text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                          {isRTL ? patient.conditionAr : patient.condition}\n                        </span>\n                      )}\n                    </p>\n                  )}\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fab fa-google mr-1\"></i>\n                    Gemini AI\n                  </span>\n                  <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-robot mr-1\"></i>\n                    ChatGPT\n                  </span>\n                  <span className=\"bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-server mr-1\"></i>\n                    Local LLM\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <nav className=\"flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('analytics')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                activeTab === 'analytics'\n                  ? 'border-orange-500 text-orange-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-chart-line mr-2\"></i>\n              {t('aiAnalytics', 'AI Analytics')}\n            </button>\n            <button\n              onClick={() => setActiveTab('interactions')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                activeTab === 'interactions'\n                  ? 'border-orange-500 text-orange-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-comments mr-2\"></i>\n              {t('interactions', 'Interactions')} ({aiInteractions.length})\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-8\">\n          {/* Main Content */}\n          <div className=\"xl:col-span-3\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              {activeTab === 'analytics' && (\n                <>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                    {t('aiAnalyticsDashboard', 'AI Analytics Dashboard')}\n                  </h2>\n                  <AIAnalyticsDashboard />\n                </>\n              )}\n\n              {activeTab === 'interactions' && (\n                <>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                    {t('aiInteractionHistory', 'AI Interaction History')}\n                  </h2>\n                  \n                  {aiInteractions.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {aiInteractions.map((interaction, index) => (\n                        <div\n                          key={index}\n                          className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\"\n                        >\n                          <div className=\"flex items-start justify-between mb-3\">\n                            <div className=\"flex items-center space-x-3\">\n                              <i className={`${getInteractionIcon(interaction.type)} ${getInteractionColor(interaction.type)} text-lg`}></i>\n                              <div>\n                                <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                                  {interaction.type === 'treatment' && t('treatmentRecommendation', 'Treatment Recommendation')}\n                                  {interaction.type === 'exercise' && t('exerciseModification', 'Exercise Modification')}\n                                  {interaction.type === 'progress' && t('progressAnalysis', 'Progress Analysis')}\n                                  {interaction.type === 'documentation' && t('documentationHelp', 'Documentation Help')}\n                                  {!['treatment', 'exercise', 'progress', 'documentation'].includes(interaction.type) && t('aiQuery', 'AI Query')}\n                                </h3>\n                                <div className=\"flex items-center space-x-2 text-sm text-gray-500\">\n                                  <i className={`${getProviderIcon(interaction.provider)} text-xs`}></i>\n                                  <span>{interaction.provider || 'gemini'}</span>\n                                  <span>•</span>\n                                  <span>{new Date(interaction.timestamp).toLocaleString()}</span>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                          \n                          <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3\">\n                            <div className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                              {t('query', 'Query')}:\n                            </div>\n                            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                              {interaction.prompt || interaction.query || t('noQueryRecorded', 'No query recorded')}\n                            </div>\n                          </div>\n                          \n                          <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3\">\n                            <div className=\"text-sm font-medium text-blue-700 dark:text-blue-300 mb-1\">\n                              {t('aiResponse', 'AI Response')}:\n                            </div>\n                            <div className=\"text-sm text-blue-600 dark:text-blue-400\">\n                              {interaction.response || t('noResponseRecorded', 'No response recorded')}\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-12\">\n                      <i className=\"fas fa-brain text-6xl text-gray-300 mb-4\"></i>\n                      <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                        {t('noInteractions', 'No AI interactions yet')}\n                      </h3>\n                      <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n                        {t('startUsingAI', 'Start using the AI assistant to see interactions here')}\n                      </p>\n                      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-md mx-auto\">\n                        <p className=\"text-blue-800 dark:text-blue-200 text-sm\">\n                          <i className=\"fas fa-info-circle mr-2\"></i>\n                          {t('aiFloatingNote', 'Look for the brain icon in the bottom-right corner to access the AI Assistant')}\n                        </p>\n                      </div>\n                    </div>\n                  )}\n                </>\n              )}\n            </div>\n          </div>\n\n          {/* Stats Sidebar */}\n          <div className=\"xl:col-span-1\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                <i className=\"fas fa-chart-bar mr-2 text-gray-600\"></i>\n                {t('aiUsageStats', 'AI Usage Stats')}\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('totalQueries', 'Total Queries')}\n                  </span>\n                  <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {stats.totalQueries}\n                  </span>\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <i className=\"fas fa-stethoscope text-blue-600\"></i>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {t('treatments', 'Treatments')}\n                      </span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {stats.treatmentRecommendations}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <i className=\"fas fa-dumbbell text-purple-600\"></i>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {t('exercises', 'Exercises')}\n                      </span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {stats.exerciseModifications}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <i className=\"fas fa-chart-line text-green-600\"></i>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {t('progress', 'Progress')}\n                      </span>\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {stats.progressAnalyses}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* AI Assistant Note */}\n            <div className=\"bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mt-6\">\n              <h4 className=\"text-sm font-medium text-orange-900 dark:text-orange-200 mb-2\">\n                <i className=\"fas fa-brain mr-2\"></i>\n                {t('aiAssistantActive', 'AI Assistant is Active')}\n              </h4>\n              <p className=\"text-sm text-orange-800 dark:text-orange-300\">\n                {t('aiAssistantNote', 'The AI Assistant is available as a floating button in the bottom-right corner of the screen')}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AIAssistantPage;\n"], "names": ["constructor", "this", "baseURL", "process", "storagePrefix", "initializeStorage", "getAuthToken", "localStorage", "getItem", "apiRequest", "endpoint", "options", "arguments", "length", "undefined", "token", "url", "concat", "defaultOptions", "headers", "finalOptions", "_objectSpread", "response", "fetch", "ok", "Error", "status", "json", "error", "console", "warn", "message", "fallbackToLocalStorage", "method", "data", "body", "JSON", "parse", "includes", "handleBodyMapFallback", "handleCommunicationFallback", "handleExerciseFallback", "handleAIFallback", "success", "setItem", "getDefaultPatients", "key", "item", "value", "stringify", "removeItem", "id", "name", "nameAr", "age", "gender", "condition", "conditionAr", "phone", "email", "communicationPreferences", "preferredChannels", "language", "quietHours", "start", "end", "createdAt", "Date", "toISOString", "getPatients", "Promise", "resolve", "setTimeout", "getPatient", "patientId", "patient", "find", "p", "saveBodyMapData", "bodyMapData", "allBodyMapData", "timestamp", "getBodyMapData", "sendMessage", "messageData", "history", "now", "toString", "push", "getCommunicationHistory", "patientHistory", "filter", "msg", "saveExerciseProgram", "programData", "programs", "program", "getExercisePrograms", "patientPrograms", "prog", "saveAIInteraction", "interactionData", "interactions", "interaction", "getAIInteractions", "patientInteractions", "int", "getAnalytics", "communications", "analytics", "totalAIQueries", "totalCommunications", "totalExercisePrograms", "totalBodyMapAssessments", "Object", "keys", "recentActivity", "slice", "map", "i", "type", "c", "sort", "a", "b", "clearAllData", "for<PERSON>ach", "exportData", "patients", "communicationHistory", "exercisePrograms", "aiInteractions", "exportedAt", "importData", "delay", "ms", "AIAssistantPage", "t", "isRTL", "useLanguage", "useParams", "navigate", "useNavigate", "setPatient", "useState", "setAiInteractions", "loading", "setLoading", "activeTab", "setActiveTab", "stats", "setStats", "totalQueries", "treatmentRecommendations", "exerciseModifications", "progressAnalyses", "useEffect", "loadPatientData", "loadAIInteractions", "async", "patientData", "dataService", "toast", "getInteractionIcon", "getInteractionColor", "getProviderIcon", "provider", "_jsx", "className", "children", "_jsxs", "onClick", "_Fragment", "AIAnalyticsDashboard", "index", "toLocaleString", "prompt", "query"], "sourceRoot": ""}