{"version": 3, "file": "static/js/9252.9e45be23.chunk.js", "mappings": "wJAwZA,MAEA,EAFoB,IArZpB,MACEA,WAAAA,GACEC,KAAKC,QAAUC,+BACfF,KAAKG,cAAgB,cACrBH,KAAKI,mBACP,CAGAC,YAAAA,GACE,OAAOC,aAAaC,QAAQ,UAAY,YAC1C,CAGA,gBAAMC,CAAWC,GAAyB,IAAfC,EAAOC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACpC,MAAMG,EAAQd,KAAKK,eACbU,EAAG,GAAAC,OAAMhB,KAAKC,SAAOe,OAAGP,GAExBQ,EAAiB,CACrBC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADF,OAAYF,KAIzBK,GAAYC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbH,GACAP,GAAO,IACVQ,SAAOE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACFH,EAAeC,SACfR,EAAQQ,WAIf,IACE,MAAMG,QAAiBC,MAAMP,EAAKI,GAElC,IAAKE,EAASE,GACZ,MAAM,IAAIC,MAAM,uBAADR,OAAwBK,EAASI,SAIlD,aADmBJ,EAASK,MAE9B,CAAE,MAAOC,GAGP,OAFAC,QAAQC,KAAK,0BAADb,OAA2BP,EAAQ,kCAAkCkB,EAAMG,SAEhF9B,KAAK+B,uBAAuBtB,EAAUC,EAC/C,CACF,CAGAqB,sBAAAA,CAAuBtB,EAAUC,GAC/B,MAAMsB,EAAStB,EAAQsB,QAAU,MAC3BC,EAAOvB,EAAQwB,KAAOC,KAAKC,MAAM1B,EAAQwB,MAAQ,KAGvD,OAAIzB,EAAS4B,SAAS,YACbrC,KAAKsC,sBAAsB7B,EAAUuB,EAAQC,GAC3CxB,EAAS4B,SAAS,kBACpBrC,KAAKuC,4BAA4B9B,EAAUuB,EAAQC,GACjDxB,EAAS4B,SAAS,sBACpBrC,KAAKwC,uBAAuB/B,EAAUuB,EAAQC,GAC5CxB,EAAS4B,SAAS,oBACpBrC,KAAKyC,iBAAiBhC,EAAUuB,EAAQC,GAG1C,CAAES,SAAS,EAAOf,MAAO,0CAClC,CAGAvB,iBAAAA,GACOJ,KAAKO,QAAQ,iBAChBP,KAAK2C,QAAQ,eAAe,GAC5B3C,KAAK2C,QAAQ,WAAY3C,KAAK4C,sBAC9B5C,KAAK2C,QAAQ,cAAe,CAAC,GAC7B3C,KAAK2C,QAAQ,uBAAwB,IACrC3C,KAAK2C,QAAQ,mBAAoB,IACjC3C,KAAK2C,QAAQ,iBAAkB,IAEnC,CAGApC,OAAAA,CAAQsC,GACN,IACE,MAAMC,EAAOxC,aAAaC,QAAQP,KAAKG,cAAgB0C,GACvD,OAAOC,EAAOX,KAAKC,MAAMU,GAAQ,IACnC,CAAE,MAAOnB,GAEP,OADAC,QAAQD,MAAM,mCAAoCA,GAC3C,IACT,CACF,CAEAgB,OAAAA,CAAQE,EAAKE,GACX,IAEE,OADAzC,aAAaqC,QAAQ3C,KAAKG,cAAgB0C,EAAKV,KAAKa,UAAUD,KACvD,CACT,CAAE,MAAOpB,GAEP,OADAC,QAAQD,MAAM,iCAAkCA,IACzC,CACT,CACF,CAEAsB,UAAAA,CAAWJ,GACT,IAEE,OADAvC,aAAa2C,WAAWjD,KAAKG,cAAgB0C,IACtC,CACT,CAAE,MAAOlB,GAEP,OADAC,QAAQD,MAAM,oCAAqCA,IAC5C,CACT,CACF,CAGAiB,kBAAAA,GACE,MAAO,CACL,CACEM,GAAI,mBACJC,KAAM,iBACNC,OAAQ,oDACRC,IAAK,GACLC,OAAQ,OACRC,UAAW,iBACXC,YAAa,4EACbC,MAAO,gBACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,YAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAExB,CACEjB,GAAI,mBACJC,KAAM,gBACNC,OAAQ,gEACRC,IAAK,GACLC,OAAQ,SACRC,UAAW,qBACXC,YAAa,qGACbC,MAAO,cACPC,MAAO,oBACPC,yBAA0B,CACxBC,kBAAmB,CAAC,QAAS,OAC7BC,SAAU,KACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,UAErCC,WAAW,IAAIC,MAAOC,eAG5B,CAGA,iBAAMC,GACJ,OAAO,IAAIC,QAASC,IAClBC,WAAW,KACTD,EAAQtE,KAAKO,QAAQ,aAAe,KACnC,MAEP,CAEA,gBAAMiE,CAAWC,GACf,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MACMG,GADW1E,KAAKO,QAAQ,aAAe,IACpBoE,KAAKC,GAAKA,EAAE1B,KAAOuB,GAC5CH,EAAQI,GAAW,OAClB,MAEP,CAGA,qBAAMG,CAAgBJ,EAAWK,GAC/B,IACE,MAAMzD,QAAiBrB,KAAKQ,WAAW,WAAY,CACjDwB,OAAQ,OACRE,KAAMC,KAAKa,WAAS5B,EAAAA,EAAAA,GAAC,CACnBqD,aACGK,MAGP,OAAOzD,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvDwE,EAAeN,IAAUrD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACpB0D,GAAW,IACdE,WAAW,IAAId,MAAOC,gBAExBnE,KAAK2C,QAAQ,cAAeoC,GAC5BT,GAAQ,IACP,MAEP,CACF,CAEA,oBAAMW,CAAeR,GACnB,OAAO,IAAIJ,QAASC,IAClBC,WAAW,KACT,MAAMQ,EAAiB/E,KAAKO,QAAQ,gBAAkB,CAAC,EACvD+D,EAAQS,EAAeN,IAAc,OACpC,MAEP,CAGA,iBAAMS,CAAYC,GAChB,IACE,MAAM9D,QAAiBrB,KAAKQ,WAAW,iBAAkB,CACvDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUmC,KAEvB,OAAO9D,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMa,EAAUpF,KAAKO,QAAQ,yBAA2B,GAClDuB,GAAOV,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZH,GAAW,IACdH,WAAW,IAAId,MAAOC,cACtB1C,OAAQ,SAEV2D,EAAQG,KAAKzD,GACb9B,KAAK2C,QAAQ,uBAAwByC,GACrCd,EAAQxC,IACP,MAEP,CACF,CAEA,6BAAM0D,CAAwBf,GAC5B,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,kBAADQ,OAAmByD,IACzD,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACMkB,GADUzF,KAAKO,QAAQ,yBAA2B,IACzBmF,OAAOC,GAAOA,EAAIlB,YAAcA,GAC/DH,EAAQmB,IACP,MAEP,CACF,CAGA,yBAAMG,CAAoBC,GACxB,IACE,MAAMxE,QAAiBrB,KAAKQ,WAAW,qBAAsB,CAC3DwB,OAAQ,OACRE,KAAMC,KAAKa,UAAU6C,KAEvB,OAAOxE,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAMuB,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CwF,GAAO3E,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACX8B,GAAIgB,KAAKmB,MAAMC,YACZO,GAAW,IACd5B,WAAW,IAAIC,MAAOC,gBAExB2B,EAASP,KAAKQ,GACd/F,KAAK2C,QAAQ,mBAAoBmD,GACjCxB,EAAQyB,IACP,MAEP,CACF,CAEA,yBAAMC,CAAoBvB,GACxB,IACE,MAAMpD,QAAiBrB,KAAKQ,WAAW,sBAADQ,OAAuByD,IAC7D,OAAOpD,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MACM0B,GADWjG,KAAKO,QAAQ,qBAAuB,IACpBmF,OAAOQ,GAAQA,EAAKzB,YAAcA,GACnEH,EAAQ2B,IACP,MAEP,CACF,CAGA,uBAAME,CAAkBC,GACtB,IACE,MAAM/E,QAAiBrB,KAAKQ,WAAW,mBAAoB,CACzDwB,OAAQ,OACRE,KAAMC,KAAKa,UAAUoD,KAEvB,OAAO/E,EAASqB,QAAUrB,EAASY,KAAO,IAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjD+F,GAAWlF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACf8B,GAAIgB,KAAKmB,MAAMC,YACZc,GAAe,IAClBpB,WAAW,IAAId,MAAOC,gBAExBkC,EAAad,KAAKe,GAClBtG,KAAK2C,QAAQ,iBAAkB0D,GAC/B/B,EAAQgC,IACP,MAEP,CACF,CAEA,uBAAMC,CAAkB9B,GACtB,IACE,MAAMhE,EAAWgE,EAAS,oBAAAzD,OAAuByD,GAAc,mBACzDpD,QAAiBrB,KAAKQ,WAAWC,GACvC,OAAOY,EAASqB,QAAUrB,EAASY,KAAO,EAC5C,CAAE,MAAON,GAEP,OAAO,IAAI0C,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDiG,EAAsB/B,EACxB4B,EAAaX,OAAOe,GAAOA,EAAIhC,YAAcA,GAC7C4B,EACJ/B,EAAQkC,IACP,MAEP,CACF,CAGA,kBAAME,GACJ,OAAO,IAAIrC,QAASC,IAClBC,WAAW,KACT,MAAM8B,EAAerG,KAAKO,QAAQ,mBAAqB,GACjDoG,EAAiB3G,KAAKO,QAAQ,yBAA2B,GACzDuF,EAAW9F,KAAKO,QAAQ,qBAAuB,GAC/CuE,EAAc9E,KAAKO,QAAQ,gBAAkB,CAAC,EAE9CqG,EAAY,CAChBC,eAAgBR,EAAazF,OAC7BkG,oBAAqBH,EAAe/F,OACpCmG,sBAAuBjB,EAASlF,OAChCoG,wBAAyBC,OAAOC,KAAKpC,GAAalE,OAClDuG,eAAgB,IACXd,EAAae,OAAO,GAAGC,IAAIC,IAAClG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,MAASD,OAClDX,EAAeS,OAAO,GAAGC,IAAIG,IAACpG,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,iBAAoBC,OAC/D1B,EAASsB,OAAO,GAAGC,IAAIzC,IAACxD,EAAAA,EAAAA,GAAA,CAAOmG,KAAM,YAAe3C,KACvD6C,KAAK,CAACC,EAAGC,IAAM,IAAIzD,KAAKyD,EAAE3C,WAAa,IAAId,KAAKwD,EAAE1C,YAAYoC,MAAM,EAAG,KAG3E9C,EAAQsC,IACP,MAEP,CAGAgB,YAAAA,GACe,CAAC,WAAY,cAAe,uBAAwB,mBAAoB,kBAChFC,QAAQhF,GAAO7C,KAAKiD,WAAWJ,IACpC7C,KAAKiD,WAAW,eAChBjD,KAAKI,mBACP,CAGA0H,UAAAA,GASE,MARa,CACXC,SAAU/H,KAAKO,QAAQ,YACvBuE,YAAa9E,KAAKO,QAAQ,eAC1ByH,qBAAsBhI,KAAKO,QAAQ,wBACnC0H,iBAAkBjI,KAAKO,QAAQ,oBAC/B2H,eAAgBlI,KAAKO,QAAQ,kBAC7B4H,YAAY,IAAIjE,MAAOC,cAG3B,CAGAiE,UAAAA,CAAWnG,GACT,IAME,OALIA,EAAK8F,UAAU/H,KAAK2C,QAAQ,WAAYV,EAAK8F,UAC7C9F,EAAK6C,aAAa9E,KAAK2C,QAAQ,cAAeV,EAAK6C,aACnD7C,EAAK+F,sBAAsBhI,KAAK2C,QAAQ,uBAAwBV,EAAK+F,sBACrE/F,EAAKgG,kBAAkBjI,KAAK2C,QAAQ,mBAAoBV,EAAKgG,kBAC7DhG,EAAKiG,gBAAgBlI,KAAK2C,QAAQ,iBAAkBV,EAAKiG,iBACtD,CACT,CAAE,MAAOvG,GAEP,OADAC,QAAQD,MAAM,wBAAyBA,IAChC,CACT,CACF,CAGA0G,KAAAA,GAAiB,IAAXC,EAAE3H,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,IACT,OAAO,IAAI0D,QAAQC,GAAWC,WAAWD,EAASgE,GACpD,E,+FChZF,MAAMC,EAAkBC,IAA8D,IAA7D,iBAAEC,EAAgB,kBAAEC,EAAoB,GAAE,UAAEjE,GAAW+D,EAC9E,MAAM,EAAEG,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,KACpCC,EAAmBC,IAAwBF,EAAAA,EAAAA,UAAS,KACpDG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAYC,IAAiBN,EAAAA,EAAAA,UAAS,KACtCO,EAAkBC,IAAuBR,EAAAA,EAAAA,UAAS,QAClDS,EAAoBC,IAAyBV,EAAAA,EAAAA,UAAS,QACtDW,EAAkBC,IAAuBZ,EAAAA,EAAAA,UAAS,OAGnDa,EAAa,CACjB,CAAE3G,GAAI,MAAOC,KAAMwF,EAAE,gBAAiB,kBAAmBvF,OAAQ,iEACjE,CAAEF,GAAI,WAAYC,KAAMwF,EAAE,WAAY,qBAAsBvF,OAAQ,iEACpE,CAAEF,GAAI,cAAeC,KAAMwF,EAAE,cAAe,eAAgBvF,OAAQ,8CACpE,CAAEF,GAAI,UAAWC,KAAMwF,EAAE,UAAW,WAAYvF,OAAQ,8CACxD,CAAEF,GAAI,eAAgBC,KAAMwF,EAAE,eAAgB,gBAAiBvF,OAAQ,8CACvE,CAAEF,GAAI,SAAUC,KAAMwF,EAAE,SAAU,kBAAmBvF,OAAQ,mFAC7D,CAAEF,GAAI,WAAYC,KAAMwF,EAAE,WAAY,YAAavF,OAAQ,wCAC3D,CAAEF,GAAI,UAAWC,KAAMwF,EAAE,UAAW,WAAYvF,OAAQ,8CACxD,CAAEF,GAAI,cAAeC,KAAMwF,EAAE,aAAc,eAAgBvF,OAAQ,kEAI/D0G,EAAmB,CACvB,CAAE5G,GAAI,MAAOC,KAAMwF,EAAE,YAAa,cAAevF,OAAQ,mFACzD,CAAEF,GAAI,WAAYC,KAAMwF,EAAE,WAAY,YAAavF,OAAQ,kCAC3D,CAAEF,GAAI,eAAgBC,KAAMwF,EAAE,eAAgB,gBAAiBvF,OAAQ,kCACvE,CAAEF,GAAI,WAAYC,KAAMwF,EAAE,WAAY,YAAavF,OAAQ,mCAIvD2G,EAAY,CAChB,CAAE7G,GAAI,MAAOC,KAAMwF,EAAE,eAAgB,kBAAmBvF,OAAQ,0FAChE,CAAEF,GAAI,aAAcC,KAAMwF,EAAE,YAAa,cAAevF,OAAQ,uEAChE,CAAEF,GAAI,aAAcC,KAAMwF,EAAE,YAAa,cAAevF,OAAQ,uEAChE,CAAEF,GAAI,OAAQC,KAAMwF,EAAE,OAAQ,QAASvF,OAAQ,kCAC/C,CAAEF,GAAI,YAAaC,KAAMwF,EAAE,WAAY,aAAcvF,OAAQ,uEAC7D,CAAEF,GAAI,OAAQC,KAAMwF,EAAE,OAAQ,QAASvF,OAAQ,wCAC/C,CAAEF,GAAI,YAAaC,KAAMwF,EAAE,YAAa,aAAcvF,OAAQ,8CAC9D,CAAEF,GAAI,OAAQC,KAAMwF,EAAE,OAAQ,QAASvF,OAAQ,oDAC/C,CAAEF,GAAI,OAAQC,KAAMwF,EAAE,OAAQ,QAASvF,OAAQ,kCAC/C,CAAEF,GAAI,OAAQC,KAAMwF,EAAE,OAAQ,QAASvF,OAAQ,gDAGjD4G,EAAAA,EAAAA,WAAU,KACRC,KACC,KAEHD,EAAAA,EAAAA,WAAU,KACRE,KACC,CAACpB,EAAWO,EAAYE,EAAkBE,EAAoBE,IAEjE,MAAMM,EAAgBE,UACpBf,GAAW,GACX,IA2IEL,EAzIsB,CACpB,CACE7F,GAAI,EACJC,KAAM,yBACNC,OAAQ,uEACRgH,SAAU,WACVC,WAAY,WACZC,SAAU,YACVC,SAAU,aACVC,UAAW,OACXC,YAAa,kCACbC,MAAO,yFACPC,MAAO,oEACPC,aAAc,CACZC,GAAI,CACF,4CACA,wCACA,qBACA,6BAEFC,GAAI,CACF,+JACA,sGACA,+EACA,yEAGJC,SAAU,CACRF,GAAI,6EACJC,GAAI,sSAENE,YAAa,CACXH,GAAI,kEACJC,GAAI,+RAENG,cAAe,CACbJ,GAAI,mDACJC,GAAI,4PAENI,KAAM,CAAC,UAAW,aAAc,qBAChCC,UAAW,sBACXC,UAAU,EACVC,OAAQ,IACRC,WAAY,KAEd,CACEpI,GAAI,EACJC,KAAM,gBACNC,OAAQ,sEACRgH,SAAU,WACVC,WAAY,WACZC,SAAU,OACVC,SAAU,4BACVC,UAAW,OACXC,YAAa,kCACbC,MAAO,sFACPC,MAAO,oEACPC,aAAc,CACZC,GAAI,CACF,wCACA,yCACA,gDACA,0DACA,8BAEFC,GAAI,CACF,iLACA,qIACA,uLACA,0RACA,wGAGJC,SAAU,CACRF,GAAI,mEACJC,GAAI,6QAENE,YAAa,CACXH,GAAI,uDACJC,GAAI,8KAENG,cAAe,CACbJ,GAAI,2DACJC,GAAI,2RAENI,KAAM,CAAC,iBAAkB,cAAe,QACxCC,UAAW,sBACXC,UAAU,EACVC,OAAQ,IACRC,WAAY,KAEd,CACEpI,GAAI,EACJC,KAAM,gBACNC,OAAQ,wHACRgH,SAAU,WACVC,WAAY,WACZC,SAAU,aACVC,SAAU,YACVC,UAAW,OACXC,YAAa,2BACbC,MAAO,yFACPC,MAAO,oEACPC,aAAc,CACZC,GAAI,CACF,iCACA,uDACA,kDACA,mDAEFC,GAAI,CACF,4IACA,2PACA,kOACA,kLAGJC,SAAU,CACRF,GAAI,wEACJC,GAAI,iUAENE,YAAa,CACXH,GAAI,gDACJC,GAAI,yNAENG,cAAe,CACbJ,GAAI,qEACJC,GAAI,6QAENI,KAAM,CAAC,sBAAuB,aAAc,iBAC5CC,UAAW,sBACXC,UAAU,EACVC,OAAQ,IACRC,WAAY,MAKlB,CAAE,MAAO3J,GACPC,QAAQD,MAAM,2BAA4BA,GAC1C4J,EAAAA,GAAM5J,MAAMgH,EAAE,wBAAyB,2BACzC,CAAC,QACCS,GAAW,EACb,GAGIc,EAAkBA,KACtB,IAAIsB,EAAW1C,EAGXO,IACFmC,EAAWA,EAAS9F,OAAO+F,GACzBA,EAAStI,KAAKuI,cAAcrJ,SAASgH,EAAWqC,gBAChDD,EAASrI,OAAOf,SAASgH,IACzBoC,EAASP,KAAKS,KAAKC,GAAOA,EAAIF,cAAcrJ,SAASgH,EAAWqC,kBAK3C,QAArBnC,IACFiC,EAAWA,EAAS9F,OAAO+F,GAAYA,EAASrB,WAAab,IAIpC,QAAvBE,IACF+B,EAAWA,EAAS9F,OAAO+F,GAAYA,EAASpB,aAAeZ,IAIxC,QAArBE,IACF6B,EAAWA,EAAS9F,OAAO+F,GAAYA,EAASnB,WAAaX,IAG/DT,EAAqBsC,IAGjBK,EAAwBJ,IACxBhD,GACFA,EAAiBgD,GAEnBF,EAAAA,GAAM7I,QAAQiG,EAAE,gBAAiB,+BAG7BmD,EAAsBzB,IAC1B,OAAQA,GACN,IAAK,WAAY,MAAO,8BACxB,IAAK,eAAgB,MAAO,gCAC5B,IAAK,WAAY,MAAO,0BACxB,QAAS,MAAO,8BAQpB,OAAIlB,GAEA4C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uCAAsCC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kEACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDtD,EAAE,mBAAoB,8BAO7BuD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uCACZrD,EAAE,kBAAmB,wBAExBoD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDhD,EAAkBrI,OAAO,IAAE+H,EAAE,YAAa,oBAK/CoD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4DAA2DC,SAAA,EAExEC,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAAA,SACE3E,KAAK,OACL4E,YAAaxD,EAAE,kBAAmB,uBAClC5F,MAAOsG,EACP+C,SAAWC,GAAM/C,EAAc+C,EAAEC,OAAOvJ,OACxCiJ,UAAU,uKAKdE,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAAA,UACEnJ,MAAOwG,EACP6C,SAAWC,GAAM7C,EAAoB6C,EAAEC,OAAOvJ,OAC9CiJ,UAAU,kKAAiKC,SAE1KpC,EAAWxC,IAAI+C,IACd8B,EAAAA,EAAAA,KAAA,UAA0BnJ,MAAOqH,EAASlH,GAAG+I,SAC1CrD,EAAQwB,EAAShH,OAASgH,EAASjH,MADzBiH,EAASlH,UAQ5BgJ,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAAA,UACEnJ,MAAO0G,EACP2C,SAAWC,GAAM3C,EAAsB2C,EAAEC,OAAOvJ,OAChDiJ,UAAU,kKAAiKC,SAE1KnC,EAAiBzC,IAAIkF,IACpBL,EAAAA,EAAAA,KAAA,UAAuBnJ,MAAOwJ,EAAMrJ,GAAG+I,SACpCrD,EAAQ2D,EAAMnJ,OAASmJ,EAAMpJ,MADnBoJ,EAAMrJ,UAQzBgJ,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAAA,UACEnJ,MAAO4G,EACPyC,SAAWC,GAAMzC,EAAoByC,EAAEC,OAAOvJ,OAC9CiJ,UAAU,kKAAiKC,SAE1KlC,EAAU1C,IAAImF,IACbN,EAAAA,EAAAA,KAAA,UAAsBnJ,MAAOyJ,EAAKtJ,GAAG+I,SAClCrD,EAAQ4D,EAAKpJ,OAASoJ,EAAKrJ,MADjBqJ,EAAKtJ,YASI,IAA7B+F,EAAkBrI,QACjBmL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtD,EAAE,mBAAoB,mDAI3BuD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEhD,EAAkB5B,IAAIoE,IACrBS,SAAAA,EAAAA,KAACO,EAAY,CAEXhB,SAAUA,EACViB,SAAUb,EACVc,YAtGcC,EAsGiBnB,EAASvI,GArG7CwF,EAAkBiD,KAAKkB,GAAMA,EAAG3J,KAAO0J,IAsGlCd,mBAAoBA,GAJfL,EAASvI,IAnGA0J,gBAkHxBE,EAAoBrB,IAEP,CACf,yBAA0B,yFAC1B,gBAAiB,sFACjB,eAAgB,0FAeFA,EAASvI,KAXF,CACrB,SAAY,yFACZ,YAAe,sFACf,QAAW,yFACX,aAAgB,yFAChB,OAAU,yFACV,SAAY,sFACZ,QAAW,yFACX,YAAe,uFAG8BuI,EAASrB,WAAa,0FAGjE2C,EAAmBtB,IACP,CACd,SAAY,WACZ,YAAe,oBACf,QAAW,gBACX,aAAgB,OAChB,OAAU,YACV,SAAY,UACZ,QAAW,aACX,YAAe,wBAGFA,EAASrB,WAAa,YAIjCqC,EAAeO,IAA6D,IAA5D,SAAEvB,EAAQ,SAAEiB,EAAQ,WAAEC,EAAU,mBAAEb,GAAoBkB,EAC1E,MAAM,EAAErE,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdoE,EAAaC,IAAkBlE,EAAAA,EAAAA,WAAS,IACxCmE,EAAYC,IAAiBpE,EAAAA,EAAAA,WAAS,IACtCqE,EAAWC,IAAgBtE,EAAAA,EAAAA,WAAS,GAE3C,OACE+C,EAAAA,EAAAA,MAAA,OAAKC,UAAS,iFAAAhL,OACZ2L,EAAa,iDAAmD,wCAC/DV,SAAA,EAEDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kGAAiGC,SAAA,CAC5GkB,GAQApB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2FAA0FC,SAAA,EACvGC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,UAAAhL,OAAY+L,EAAgBtB,GAAS,qBACjDS,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uCAAsCC,SACnDrD,EAAQ6C,EAASrI,OAASqI,EAAStI,WAVxC+I,EAAAA,EAAAA,KAAA,OACEqB,IAAK9B,EAASf,OAASoC,EAAiBrB,GACxC+B,IAAK5E,EAAQ6C,EAASrI,OAASqI,EAAStI,KACxC6I,UAAU,6BACVyB,QAASA,IAAML,GAAc,MAWjClB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBC,UACrCC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAhL,OAAgD8K,EAAmBL,EAASpB,aAAc4B,SACtGtD,EAAE8C,EAASpB,WAAYoB,EAASpB,gBAIpCoB,EAASd,QACRoB,EAAAA,EAAAA,MAAA2B,EAAAA,SAAA,CAAAzB,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,UACpCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mEAAkEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrD,EAAE,QAAS,eAGhBuD,EAAAA,EAAAA,KAAA,UACEyB,QAASA,IAAML,GAAa,GAC5BtB,UAAU,uIAAsIC,UAEhJC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0CAAyCC,UACtDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAMpBW,IACCT,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2BAA0BC,UACvCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0EAMnBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DrD,EAAQ6C,EAASrI,OAASqI,EAAStI,QAGtC4I,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZP,EAASlB,aAEZwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZpD,EAAQ6C,EAAShB,YAAcgB,EAASjB,cAE3CuB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZP,EAASJ,OAAO,KAAGI,EAASH,WAAW,IAAE3C,EAAE,OAAQ,QAAQ,WAIhEoD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,UACEyB,QAASA,IAAMT,GAAgBD,GAC/BjB,UAAU,wDAAuDC,SAEhEgB,EAActE,EAAE,cAAe,gBAAkBA,EAAE,cAAe,mBAGrEuD,EAAAA,EAAAA,KAAA,UACEyB,QAASA,IAAMjB,EAASjB,GACxBmC,SAAUjB,EACVX,UAAS,kDAAAhL,OACP2L,EACI,+CACA,4CACHV,SAEFU,EAAahE,EAAE,QAAS,SAAWA,EAAE,cAAe,qBAKxDsE,IACCf,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,iDAAgDC,SAAA,CAC3DtD,EAAE,eAAgB,gBAAgB,QAErCuD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sEAAqEC,UAC/ErD,EAAQ6C,EAASb,aAAaE,GAAKW,EAASb,aAAaC,IAAIxD,IAAI,CAACwG,EAAMC,KACxE5B,EAAAA,EAAAA,KAAA,MAAAD,SAAiB4B,GAARC,UAKf/B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,iDAAgDC,SAAA,CAC3DtD,EAAE,WAAY,YAAY,QAE7BuD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CrD,EAAQ6C,EAASV,SAASD,GAAKW,EAASV,SAASF,SAItDkB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,iDAAgDC,SAAA,CAC3DtD,EAAE,cAAe,eAAe,QAEnCuD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CrD,EAAQ6C,EAAST,YAAYF,GAAKW,EAAST,YAAYH,gBASnEwC,GAAa5B,EAASd,QACrBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6EAA6E2B,QAASA,IAAML,GAAa,GAAOrB,UAC7HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iEAAiE2B,QAAUtB,GAAMA,EAAE0B,kBAAkB9B,SAAA,EAClHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChErD,EAAQ6C,EAASrI,OAASqI,EAAStI,QAEtC+I,EAAAA,EAAAA,KAAA,UACEyB,QAASA,IAAML,GAAa,GAC5BtB,UAAU,gFAA+EC,UAEzFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+BAGjBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,KAAA,SACEF,UAAU,2BACVgC,UAAQ,EACRC,UAAQ,EACRV,IAAK9B,EAASd,MAAMsB,SACrB,4DAWf,IAGaiC,EAAyBC,IAAmD,IAAlD,UAAE1J,EAAS,OAAE2J,EAAM,eAAEC,EAAiB,MAAMF,EACjF,MAAM,EAAExF,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdH,EAAmB4F,IAAwBtF,EAAAA,EAAAA,WAAuB,OAAdqF,QAAc,IAAdA,OAAc,EAAdA,EAAgBvF,YAAa,KACjFyF,EAAaC,IAAkBxF,EAAAA,EAAAA,WAAuB,OAAdqF,QAAc,IAAdA,OAAc,EAAdA,EAAgBlL,OAAQ,KAChEsL,EAAoBC,IAAyB1F,EAAAA,EAAAA,WAAuB,OAAdqF,QAAc,IAAdA,OAAc,EAAdA,EAAgBM,cAAe,KACrFC,EAAiBC,IAAsB7F,EAAAA,EAAAA,WAAuB,OAAdqF,QAAc,IAAdA,OAAc,EAAdA,EAAgB9D,WAAY,MAC5EuE,EAAWC,IAAgB/F,EAAAA,EAAAA,WAAuB,OAAdqF,QAAc,IAAdA,OAAc,EAAdA,EAAgBS,YAAa,UACjEE,EAAaC,IAAkBjG,EAAAA,EAAAA,WAAS,GAezCkG,EAAuBA,CAACpB,EAAOqB,EAAOpM,KAC1C,MAAMqM,EAAU,IAAI1G,GACpB0G,EAAQtB,IAAM1M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQgO,EAAQtB,IAAM,IAAE,CAACqB,GAAQpM,IAC/CuL,EAAqBc,IAqCvB,OACErD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BC,SAAA,EACvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8CACZrD,EAAE,yBAA0B,gCAE/BoD,EAAAA,EAAAA,MAAA,UACE4B,QAASA,IAAMsB,GAAe,GAC9BjD,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrD,EAAE,cAAe,uBAItBoD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0BAAyBC,SAAA,EACtCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,cAAe,mBAEpBuD,EAAAA,EAAAA,KAAA,SACE3E,KAAK,OACLxE,MAAOwL,EACPnC,SAAWC,GAAMmC,EAAenC,EAAEC,OAAOvJ,OACzCiJ,UAAU,kKACVG,YAAaxD,EAAE,mBAAoB,4BAIvCoD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,cAAe,kBAEpBuD,EAAAA,EAAAA,KAAA,YACEnJ,MAAO0L,EACPrC,SAAWC,GAAMqC,EAAsBrC,EAAEC,OAAOvJ,OAChDsM,KAAM,EACNrD,UAAU,8KACVG,YAAaxD,EAAE,mBAAoB,mCAIvCoD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,kBAAmB,+BAExBoD,EAAAA,EAAAA,MAAA,UACEhJ,MAAO6L,EACPxC,SAAWC,GAAMwC,EAAmBxC,EAAEC,OAAOvJ,OAC7CiJ,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,MAAA,UAAQhJ,MAAM,IAAGkJ,SAAA,CAAC,KAAGtD,EAAE,OAAQ,YAC/BoD,EAAAA,EAAAA,MAAA,UAAQhJ,MAAM,IAAGkJ,SAAA,CAAC,KAAGtD,EAAE,QAAS,aAChCoD,EAAAA,EAAAA,MAAA,UAAQhJ,MAAM,IAAGkJ,SAAA,CAAC,KAAGtD,EAAE,QAAS,aAChCoD,EAAAA,EAAAA,MAAA,UAAQhJ,MAAM,IAAGkJ,SAAA,CAAC,KAAGtD,EAAE,QAAS,aAChCoD,EAAAA,EAAAA,MAAA,UAAQhJ,MAAM,IAAGkJ,SAAA,CAAC,KAAGtD,EAAE,QAAS,aAChCoD,EAAAA,EAAAA,MAAA,UAAQhJ,MAAM,KAAIkJ,SAAA,CAAC,MAAItD,EAAE,QAAS,mBAItCoD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,YAAa,gBAElBoD,EAAAA,EAAAA,MAAA,UACEhJ,MAAO+L,EACP1C,SAAWC,GAAM0C,EAAa1C,EAAEC,OAAOvJ,OACvCiJ,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQnJ,MAAM,QAAOkJ,SAAEtD,EAAE,QAAS,YAClCuD,EAAAA,EAAAA,KAAA,UAAQnJ,MAAM,kBAAiBkJ,SAAEtD,EAAE,gBAAiB,sBACpDuD,EAAAA,EAAAA,KAAA,UAAQnJ,MAAM,eAAckJ,SAAEtD,EAAE,aAAc,uBAC9CuD,EAAAA,EAAAA,KAAA,UAAQnJ,MAAM,eAAckJ,SAAEtD,EAAE,WAAY,uBAC5CuD,EAAAA,EAAAA,KAAA,UAAQnJ,MAAM,SAAQkJ,SAAEtD,EAAE,SAAU,mBAIxCoD,EAAAA,EAAAA,MAAA,UACE4B,QAhHc2B,KACxB,IAAKf,EAAYgB,OAEf,YADAhE,EAAAA,GAAM5J,MAAMgH,EAAE,sBAAuB,6BAIvC,GAAiC,IAA7BD,EAAkB9H,OAEpB,YADA2K,EAAAA,GAAM5J,MAAMgH,EAAE,kBAAmB,wCAInC,MAAM5C,EAAU,CACd7C,IAAkB,OAAdmL,QAAc,IAAdA,OAAc,EAAdA,EAAgBnL,KAAMgB,KAAKmB,MAC/BlC,KAAMoL,EACNI,YAAaF,EACblE,SAAUqE,EACVE,YACAhG,UAAWJ,EACXjE,YACAR,WAAyB,OAAdoK,QAAc,IAAdA,OAAc,EAAdA,EAAgBpK,aAAa,IAAIC,MAAOC,cACnDqL,WAAW,IAAItL,MAAOC,eAGpBiK,GACFA,EAAOrI,GAGTwF,EAAAA,GAAM7I,QAAQiG,EAAE,eAAgB,yCAsFtBqD,UAAU,2FAA0FC,SAAA,EAEpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrD,EAAE,cAAe,uBAKtBoD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,CACnEtD,EAAE,oBAAqB,sBAAsB,KAAGD,EAAkB9H,OAAO,OAG9C,IAA7B8H,EAAkB9H,QACjBmL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtD,EAAE,sBAAuB,gCAE5BuD,EAAAA,EAAAA,KAAA,UACEyB,QAASA,IAAMsB,GAAe,GAC9BjD,UAAU,qDAAoDC,SAE7DtD,EAAE,mBAAoB,iCAI3BuD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBvD,EAAkBrB,IAAI,CAACoE,EAAUqC,KAChC/B,EAAAA,EAAAA,MAAA,OAAqCC,UAAU,6DAA4DC,SAAA,EACzGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0JAAyJC,SAAA,EACtKC,EAAAA,EAAAA,KAAA,OACEqB,IAAK9B,EAASf,OAASoC,EAAiBrB,GACxC+B,IAAK5E,EAAQ6C,EAASrI,OAASqI,EAAStI,KACxC6I,UAAU,6BACVyB,QAAUpB,IACRA,EAAEC,OAAOmD,MAAMC,QAAU,OACzBrD,EAAEC,OAAOqD,YAAYF,MAAMC,QAAU,WAGzCxD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4DAA2DC,UACxEC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,UAAAhL,OAAY+L,EAAgBtB,GAAS,qDAGrDM,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtDrD,EAAQ6C,EAASrI,OAASqI,EAAStI,QAEtC4I,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,CACpDtD,EAAE8C,EAASrB,SAAUqB,EAASrB,UAAU,WAAIzB,EAAE8C,EAASpB,WAAYoB,EAASpB,sBAInF6B,EAAAA,EAAAA,KAAA,UACEyB,QAASA,IA7KDG,KAC5BQ,EAAqB5F,EAAkBhD,OAAO,CAACkK,EAAGtI,IAAMA,IAAMwG,KA4K3B+B,CAAqB/B,GACpC9B,UAAU,kCAAiCC,UAE3CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAIjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,OAAQ,WAEbuD,EAAAA,EAAAA,KAAA,SACE3E,KAAK,SACLuI,IAAI,IACJ/M,MAAO0I,EAASsE,KAChB3D,SAAWC,GAAM6C,EAAqBpB,EAAO,OAAQkC,SAAS3D,EAAEC,OAAOvJ,QACvEiJ,UAAU,6KAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,OAAQ,WAEbuD,EAAAA,EAAAA,KAAA,SACE3E,KAAK,SACLuI,IAAI,IACJ/M,MAAO0I,EAASwE,KAChB7D,SAAWC,GAAM6C,EAAqBpB,EAAO,OAAQkC,SAAS3D,EAAEC,OAAOvJ,QACvEiJ,UAAU,6KAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,WAAY,eAEjBuD,EAAAA,EAAAA,KAAA,SACE3E,KAAK,OACLxE,MAAO0I,EAASlB,SAChB6B,SAAWC,GAAM6C,EAAqBpB,EAAO,WAAYzB,EAAEC,OAAOvJ,OAClEiJ,UAAU,6KAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,OAAQ,WAEbuD,EAAAA,EAAAA,KAAA,SACE3E,KAAK,OACLxE,MAAO0I,EAASyE,SAChB9D,SAAWC,GAAM6C,EAAqBpB,EAAO,WAAYzB,EAAEC,OAAOvJ,OAClEiJ,UAAU,gLAKhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtD,EAAE,QAAS,YAEduD,EAAAA,EAAAA,KAAA,SACE3E,KAAK,OACLxE,MAAO0I,EAAS0E,MAChB/D,SAAWC,GAAM6C,EAAqBpB,EAAO,QAASzB,EAAEC,OAAOvJ,OAC/DiJ,UAAU,uKACVG,YAAaxD,EAAE,WAAY,yCAEzB,GAAA3H,OA9FKyK,EAASvI,GAAE,KAAAlC,OAAI8M,gBAwGvCkB,IACC9C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iFAAgFC,UAC7FC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,UAC3GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtD,EAAE,iBAAkB,sBAEvBuD,EAAAA,EAAAA,KAAA,UACEyB,QAASA,IAAMsB,GAAe,GAC9BjD,UAAU,6DAA4DC,UAEtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAGjBE,EAAAA,EAAAA,KAAC3D,EAAe,CACdE,iBA7RgBgD,IAC5B,MAAM2E,GAAoBhP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACrBqK,GAAQ,IACXsE,KAAM,EACNE,KAAM,GACN1F,SAAU,aACV2F,SAAU,aACVC,MAAO,KAET7B,EAAqB,IAAI5F,EAAmB0H,IAC5CnB,GAAe,IAoRHvG,kBAAmBA,EACnBjE,UAAWA,c", "sources": ["services/dataService.js", "components/Exercise/ExerciseLibrary.jsx"], "sourcesContent": ["// Data Service for PhysioFlow\n// Handles API calls with localStorage fallback for demonstration purposes\n\nclass DataService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api/v1';\n    this.storagePrefix = 'physioflow_';\n    this.initializeStorage();\n  }\n\n  // Helper method to get auth token\n  getAuthToken() {\n    return localStorage.getItem('token') || 'demo-token';\n  }\n\n  // Helper method to make API requests\n  async apiRequest(endpoint, options = {}) {\n    const token = this.getAuthToken();\n    const url = `${this.baseURL}${endpoint}`;\n\n    const defaultOptions = {\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${token}`\n      }\n    };\n\n    const finalOptions = {\n      ...defaultOptions,\n      ...options,\n      headers: {\n        ...defaultOptions.headers,\n        ...options.headers\n      }\n    };\n\n    try {\n      const response = await fetch(url, finalOptions);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return data;\n    } catch (error) {\n      console.warn(`API request failed for ${endpoint}, using localStorage fallback:`, error.message);\n      // Fallback to localStorage for demo purposes\n      return this.fallbackToLocalStorage(endpoint, options);\n    }\n  }\n\n  // Fallback to localStorage when API is not available\n  fallbackToLocalStorage(endpoint, options) {\n    const method = options.method || 'GET';\n    const data = options.body ? JSON.parse(options.body) : null;\n\n    // Handle different endpoints with existing localStorage methods\n    if (endpoint.includes('/bodymap')) {\n      return this.handleBodyMapFallback(endpoint, method, data);\n    } else if (endpoint.includes('/communication')) {\n      return this.handleCommunicationFallback(endpoint, method, data);\n    } else if (endpoint.includes('/exercise-programs')) {\n      return this.handleExerciseFallback(endpoint, method, data);\n    } else if (endpoint.includes('/ai-interactions')) {\n      return this.handleAIFallback(endpoint, method, data);\n    }\n\n    return { success: false, error: 'Endpoint not supported in fallback mode' };\n  }\n\n  // Initialize storage with demo data\n  initializeStorage() {\n    if (!this.getItem('initialized')) {\n      this.setItem('initialized', true);\n      this.setItem('patients', this.getDefaultPatients());\n      this.setItem('bodyMapData', {});\n      this.setItem('communicationHistory', []);\n      this.setItem('exercisePrograms', []);\n      this.setItem('aiInteractions', []);\n    }\n  }\n\n  // Storage helpers\n  getItem(key) {\n    try {\n      const item = localStorage.getItem(this.storagePrefix + key);\n      return item ? JSON.parse(item) : null;\n    } catch (error) {\n      console.error('Error getting item from storage:', error);\n      return null;\n    }\n  }\n\n  setItem(key, value) {\n    try {\n      localStorage.setItem(this.storagePrefix + key, JSON.stringify(value));\n      return true;\n    } catch (error) {\n      console.error('Error setting item in storage:', error);\n      return false;\n    }\n  }\n\n  removeItem(key) {\n    try {\n      localStorage.removeItem(this.storagePrefix + key);\n      return true;\n    } catch (error) {\n      console.error('Error removing item from storage:', error);\n      return false;\n    }\n  }\n\n  // Default demo data\n  getDefaultPatients() {\n    return [\n      {\n        id: 'demo-patient-001',\n        name: 'Ahmed Mohammed',\n        nameAr: 'أحمد محمد',\n        age: 28,\n        gender: 'male',\n        condition: 'Cerebral Palsy',\n        conditionAr: 'الشلل الدماغي',\n        phone: '+966501234567',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'whatsapp'],\n          language: 'en',\n          quietHours: { start: '22:00', end: '08:00' }\n        },\n        createdAt: new Date().toISOString()\n      },\n      {\n        id: 'demo-patient-002',\n        name: 'Sarah Johnson',\n        nameAr: 'سارة جونسون',\n        age: 35,\n        gender: 'female',\n        condition: 'Spinal Cord Injury',\n        conditionAr: 'إصابة الحبل الشوكي',\n        phone: '+**********',\n        email: '<EMAIL>',\n        communicationPreferences: {\n          preferredChannels: ['email', 'sms'],\n          language: 'en',\n          quietHours: { start: '21:00', end: '07:00' }\n        },\n        createdAt: new Date().toISOString()\n      }\n    ];\n  }\n\n  // Patient data methods\n  async getPatients() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        resolve(this.getItem('patients') || []);\n      }, 100);\n    });\n  }\n\n  async getPatient(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const patients = this.getItem('patients') || [];\n        const patient = patients.find(p => p.id === patientId);\n        resolve(patient || null);\n      }, 100);\n    });\n  }\n\n  // Body Map data methods\n  async saveBodyMapData(patientId, bodyMapData) {\n    try {\n      const response = await this.apiRequest('/bodymap', {\n        method: 'POST',\n        body: JSON.stringify({\n          patientId,\n          ...bodyMapData\n        })\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const allBodyMapData = this.getItem('bodyMapData') || {};\n          allBodyMapData[patientId] = {\n            ...bodyMapData,\n            timestamp: new Date().toISOString()\n          };\n          this.setItem('bodyMapData', allBodyMapData);\n          resolve(true);\n        }, 200);\n      });\n    }\n  }\n\n  async getBodyMapData(patientId) {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const allBodyMapData = this.getItem('bodyMapData') || {};\n        resolve(allBodyMapData[patientId] || null);\n      }, 100);\n    });\n  }\n\n  // Communication methods\n  async sendMessage(messageData) {\n    try {\n      const response = await this.apiRequest('/communication', {\n        method: 'POST',\n        body: JSON.stringify(messageData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const message = {\n            id: Date.now().toString(),\n            ...messageData,\n            timestamp: new Date().toISOString(),\n            status: 'sent'\n          };\n          history.push(message);\n          this.setItem('communicationHistory', history);\n          resolve(message);\n        }, 500);\n      });\n    }\n  }\n\n  async getCommunicationHistory(patientId) {\n    try {\n      const response = await this.apiRequest(`/communication/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const history = this.getItem('communicationHistory') || [];\n          const patientHistory = history.filter(msg => msg.patientId === patientId);\n          resolve(patientHistory);\n        }, 100);\n      });\n    }\n  }\n\n  // Exercise Program methods\n  async saveExerciseProgram(programData) {\n    try {\n      const response = await this.apiRequest('/exercise-programs', {\n        method: 'POST',\n        body: JSON.stringify(programData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const program = {\n            id: Date.now().toString(),\n            ...programData,\n            createdAt: new Date().toISOString()\n          };\n          programs.push(program);\n          this.setItem('exercisePrograms', programs);\n          resolve(program);\n        }, 300);\n      });\n    }\n  }\n\n  async getExercisePrograms(patientId) {\n    try {\n      const response = await this.apiRequest(`/exercise-programs/${patientId}`);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const programs = this.getItem('exercisePrograms') || [];\n          const patientPrograms = programs.filter(prog => prog.patientId === patientId);\n          resolve(patientPrograms);\n        }, 100);\n      });\n    }\n  }\n\n  // AI Interaction methods\n  async saveAIInteraction(interactionData) {\n    try {\n      const response = await this.apiRequest('/ai-interactions', {\n        method: 'POST',\n        body: JSON.stringify(interactionData)\n      });\n      return response.success ? response.data : null;\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const interaction = {\n            id: Date.now().toString(),\n            ...interactionData,\n            timestamp: new Date().toISOString()\n          };\n          interactions.push(interaction);\n          this.setItem('aiInteractions', interactions);\n          resolve(interaction);\n        }, 100);\n      });\n    }\n  }\n\n  async getAIInteractions(patientId) {\n    try {\n      const endpoint = patientId ? `/ai-interactions/${patientId}` : '/ai-interactions';\n      const response = await this.apiRequest(endpoint);\n      return response.success ? response.data : [];\n    } catch (error) {\n      // Fallback to localStorage\n      return new Promise((resolve) => {\n        setTimeout(() => {\n          const interactions = this.getItem('aiInteractions') || [];\n          const patientInteractions = patientId\n            ? interactions.filter(int => int.patientId === patientId)\n            : interactions;\n          resolve(patientInteractions);\n        }, 100);\n      });\n    }\n  }\n\n  // Analytics methods\n  async getAnalytics() {\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const interactions = this.getItem('aiInteractions') || [];\n        const communications = this.getItem('communicationHistory') || [];\n        const programs = this.getItem('exercisePrograms') || [];\n        const bodyMapData = this.getItem('bodyMapData') || {};\n\n        const analytics = {\n          totalAIQueries: interactions.length,\n          totalCommunications: communications.length,\n          totalExercisePrograms: programs.length,\n          totalBodyMapAssessments: Object.keys(bodyMapData).length,\n          recentActivity: [\n            ...interactions.slice(-5).map(i => ({ type: 'ai', ...i })),\n            ...communications.slice(-5).map(c => ({ type: 'communication', ...c })),\n            ...programs.slice(-5).map(p => ({ type: 'exercise', ...p }))\n          ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10)\n        };\n\n        resolve(analytics);\n      }, 200);\n    });\n  }\n\n  // Clear all demo data\n  clearAllData() {\n    const keys = ['patients', 'bodyMapData', 'communicationHistory', 'exercisePrograms', 'aiInteractions'];\n    keys.forEach(key => this.removeItem(key));\n    this.removeItem('initialized');\n    this.initializeStorage();\n  }\n\n  // Export data for backup\n  exportData() {\n    const data = {\n      patients: this.getItem('patients'),\n      bodyMapData: this.getItem('bodyMapData'),\n      communicationHistory: this.getItem('communicationHistory'),\n      exercisePrograms: this.getItem('exercisePrograms'),\n      aiInteractions: this.getItem('aiInteractions'),\n      exportedAt: new Date().toISOString()\n    };\n    return data;\n  }\n\n  // Import data from backup\n  importData(data) {\n    try {\n      if (data.patients) this.setItem('patients', data.patients);\n      if (data.bodyMapData) this.setItem('bodyMapData', data.bodyMapData);\n      if (data.communicationHistory) this.setItem('communicationHistory', data.communicationHistory);\n      if (data.exercisePrograms) this.setItem('exercisePrograms', data.exercisePrograms);\n      if (data.aiInteractions) this.setItem('aiInteractions', data.aiInteractions);\n      return true;\n    } catch (error) {\n      console.error('Error importing data:', error);\n      return false;\n    }\n  }\n\n  // Simulate API delay\n  delay(ms = 100) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n}\n\n// Create singleton instance\nconst dataService = new DataService();\n\nexport default dataService;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst ExerciseLibrary = ({ onSelectExercise, selectedExercises = [], patientId }) => {\n  const { t, isRTL } = useLanguage();\n  const [exercises, setExercises] = useState([]);\n  const [filteredExercises, setFilteredExercises] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedDifficulty, setSelectedDifficulty] = useState('all');\n  const [selectedBodyPart, setSelectedBodyPart] = useState('all');\n\n  // Exercise categories\n  const categories = [\n    { id: 'all', name: t('allCategories', 'All Categories'), nameAr: 'جميع الفئات' },\n    { id: 'strength', name: t('strength', 'Strength Training'), nameAr: 'تدريب القوة' },\n    { id: 'flexibility', name: t('flexibility', 'Flexibility'), nameAr: 'المرونة' },\n    { id: 'balance', name: t('balance', 'Balance'), nameAr: 'التوازن' },\n    { id: 'coordination', name: t('coordination', 'Coordination'), nameAr: 'التنسيق' },\n    { id: 'cardio', name: t('cardio', 'Cardiovascular'), nameAr: 'القلب والأوعية' },\n    { id: 'mobility', name: t('mobility', 'Mobility'), nameAr: 'الحركة' },\n    { id: 'posture', name: t('posture', 'Posture'), nameAr: 'الوضعية' },\n    { id: 'pain_relief', name: t('painRelief', 'Pain Relief'), nameAr: 'تخفيف الألم' }\n  ];\n\n  // Difficulty levels\n  const difficultyLevels = [\n    { id: 'all', name: t('allLevels', 'All Levels'), nameAr: 'جميع المستويات' },\n    { id: 'beginner', name: t('beginner', 'Beginner'), nameAr: 'مبتدئ' },\n    { id: 'intermediate', name: t('intermediate', 'Intermediate'), nameAr: 'متوسط' },\n    { id: 'advanced', name: t('advanced', 'Advanced'), nameAr: 'متقدم' }\n  ];\n\n  // Body parts\n  const bodyParts = [\n    { id: 'all', name: t('allBodyParts', 'All Body Parts'), nameAr: 'جميع أجزاء الجسم' },\n    { id: 'upper_body', name: t('upperBody', 'Upper Body'), nameAr: 'الجزء العلوي' },\n    { id: 'lower_body', name: t('lowerBody', 'Lower Body'), nameAr: 'الجزء السفلي' },\n    { id: 'core', name: t('core', 'Core'), nameAr: 'الجذع' },\n    { id: 'full_body', name: t('fullBody', 'Full Body'), nameAr: 'الجسم كاملاً' },\n    { id: 'neck', name: t('neck', 'Neck'), nameAr: 'الرقبة' },\n    { id: 'shoulders', name: t('shoulders', 'Shoulders'), nameAr: 'الأكتاف' },\n    { id: 'arms', name: t('arms', 'Arms'), nameAr: 'الذراعين' },\n    { id: 'back', name: t('back', 'Back'), nameAr: 'الظهر' },\n    { id: 'legs', name: t('legs', 'Legs'), nameAr: 'الساقين' }\n  ];\n\n  useEffect(() => {\n    loadExercises();\n  }, []);\n\n  useEffect(() => {\n    filterExercises();\n  }, [exercises, searchTerm, selectedCategory, selectedDifficulty, selectedBodyPart]);\n\n  const loadExercises = async () => {\n    setLoading(true);\n    try {\n      // Mock exercise data - replace with actual API call\n      const mockExercises = [\n        {\n          id: 1,\n          name: 'Shoulder Blade Squeeze',\n          nameAr: 'ضغط لوح الكتف',\n          category: 'strength',\n          difficulty: 'beginner',\n          bodyPart: 'shoulders',\n          duration: '10-15 reps',\n          equipment: 'None',\n          equipmentAr: 'لا شيء',\n          image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',\n          video: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',\n          instructions: {\n            en: [\n              'Sit or stand with your arms at your sides',\n              'Squeeze your shoulder blades together',\n              'Hold for 5 seconds',\n              'Slowly release and repeat'\n            ],\n            ar: [\n              'اجلس أو قف مع ذراعيك على جانبيك',\n              'اضغط لوحي كتفك معاً',\n              'امسك لمدة 5 ثوان',\n              'حرر ببطء وكرر'\n            ]\n          },\n          benefits: {\n            en: 'Improves posture, strengthens upper back muscles, reduces shoulder tension',\n            ar: 'يحسن الوضعية، يقوي عضلات الظهر العلوية، يقلل توتر الكتف'\n          },\n          precautions: {\n            en: 'Avoid if you have acute shoulder injury. Stop if you feel pain.',\n            ar: 'تجنب إذا كان لديك إصابة حادة في الكتف. توقف إذا شعرت بألم.'\n          },\n          modifications: {\n            en: 'Can be done seated for those with balance issues',\n            ar: 'يمكن القيام به جالساً لمن لديهم مشاكل في التوازن'\n          },\n          tags: ['posture', 'upper_back', 'beginner_friendly'],\n          createdBy: 'Dr. Sarah Al-Rashid',\n          approved: true,\n          rating: 4.8,\n          usageCount: 156\n        },\n        {\n          id: 2,\n          name: 'Ankle Circles',\n          nameAr: 'دوائر الكاحل',\n          category: 'mobility',\n          difficulty: 'beginner',\n          bodyPart: 'legs',\n          duration: '10 circles each direction',\n          equipment: 'None',\n          equipmentAr: 'لا شيء',\n          image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop&q=80',\n          video: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',\n          instructions: {\n            en: [\n              'Sit comfortably with one leg extended',\n              'Lift your foot slightly off the ground',\n              'Slowly rotate your ankle in a circular motion',\n              'Complete 10 circles clockwise, then 10 counterclockwise',\n              'Repeat with the other foot'\n            ],\n            ar: [\n              'اجلس بشكل مريح مع ساق واحدة ممدودة',\n              'ارفع قدمك قليلاً عن الأرض',\n              'قم بتدوير كاحلك ببطء في حركة دائرية',\n              'أكمل 10 دوائر في اتجاه عقارب الساعة، ثم 10 عكس عقارب الساعة',\n              'كرر مع القدم الأخرى'\n            ]\n          },\n          benefits: {\n            en: 'Improves ankle mobility, reduces stiffness, enhances circulation',\n            ar: 'يحسن حركة الكاحل، يقلل التصلب، يعزز الدورة الدموية'\n          },\n          precautions: {\n            en: 'Move slowly and gently. Stop if you experience pain.',\n            ar: 'تحرك ببطء ولطف. توقف إذا شعرت بألم.'\n          },\n          modifications: {\n            en: 'Can be done while lying down if sitting is uncomfortable',\n            ar: 'يمكن القيام به أثناء الاستلقاء إذا كان الجلوس غير مريح'\n          },\n          tags: ['ankle_mobility', 'circulation', 'easy'],\n          createdBy: 'Dr. Ahmed Al-Rashid',\n          approved: true,\n          rating: 4.6,\n          usageCount: 203\n        },\n        {\n          id: 3,\n          name: 'Wall Push-ups',\n          nameAr: 'تمرين الضغط على الحائط',\n          category: 'strength',\n          difficulty: 'beginner',\n          bodyPart: 'upper_body',\n          duration: '8-12 reps',\n          equipment: 'Wall',\n          equipmentAr: 'حائط',\n          image: 'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400&h=300&fit=crop&q=80',\n          video: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',\n          instructions: {\n            en: [\n              'Stand arm\\'s length from a wall',\n              'Place palms flat against the wall at shoulder height',\n              'Lean forward and push back to starting position',\n              'Keep your body straight throughout the movement'\n            ],\n            ar: [\n              'قف على مسافة ذراع من الحائط',\n              'ضع راحتي يديك مسطحتين على الحائط على مستوى الكتف',\n              'انحن للأمام ثم ادفع للعودة إلى الوضع الأولي',\n              'حافظ على استقامة جسمك طوال الحركة'\n            ]\n          },\n          benefits: {\n            en: 'Strengthens chest, shoulders, and arms. Improves upper body strength.',\n            ar: 'يقوي الصدر والكتفين والذراعين. يحسن قوة الجزء العلوي من الجسم.'\n          },\n          precautions: {\n            en: 'Avoid if you have wrist or shoulder problems.',\n            ar: 'تجنب إذا كان لديك مشاكل في المعصم أو الكتف.'\n          },\n          modifications: {\n            en: 'Move closer to wall for easier version, further for harder version',\n            ar: 'اقترب من الحائط للنسخة الأسهل، ابتعد للنسخة الأصعب'\n          },\n          tags: ['upper_body_strength', 'functional', 'home_exercise'],\n          createdBy: 'Dr. Fatima Al-Zahra',\n          approved: true,\n          rating: 4.7,\n          usageCount: 189\n        }\n      ];\n      \n      setExercises(mockExercises);\n    } catch (error) {\n      console.error('Error loading exercises:', error);\n      toast.error(t('errorLoadingExercises', 'Error loading exercises'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const filterExercises = () => {\n    let filtered = exercises;\n\n    // Search filter\n    if (searchTerm) {\n      filtered = filtered.filter(exercise =>\n        exercise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        exercise.nameAr.includes(searchTerm) ||\n        exercise.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n    }\n\n    // Category filter\n    if (selectedCategory !== 'all') {\n      filtered = filtered.filter(exercise => exercise.category === selectedCategory);\n    }\n\n    // Difficulty filter\n    if (selectedDifficulty !== 'all') {\n      filtered = filtered.filter(exercise => exercise.difficulty === selectedDifficulty);\n    }\n\n    // Body part filter\n    if (selectedBodyPart !== 'all') {\n      filtered = filtered.filter(exercise => exercise.bodyPart === selectedBodyPart);\n    }\n\n    setFilteredExercises(filtered);\n  };\n\n  const handleExerciseSelect = (exercise) => {\n    if (onSelectExercise) {\n      onSelectExercise(exercise);\n    }\n    toast.success(t('exerciseAdded', 'Exercise added to program'));\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 'beginner': return 'bg-green-100 text-green-800';\n      case 'intermediate': return 'bg-yellow-100 text-yellow-800';\n      case 'advanced': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const isExerciseSelected = (exerciseId) => {\n    return selectedExercises.some(ex => ex.id === exerciseId);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n        <span className=\"ml-2 text-gray-600 dark:text-gray-400\">\n          {t('loadingExercises', 'Loading exercises...')}\n        </span>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"exercise-library\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            <i className=\"fas fa-dumbbell mr-2 text-blue-600\"></i>\n            {t('exerciseLibrary', 'Exercise Library')}\n          </h3>\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {filteredExercises.length} {t('exercises', 'exercises')}\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\">\n          {/* Search */}\n          <div>\n            <input\n              type=\"text\"\n              placeholder={t('searchExercises', 'Search exercises...')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n\n          {/* Category Filter */}\n          <div>\n            <select\n              value={selectedCategory}\n              onChange={(e) => setSelectedCategory(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {categories.map(category => (\n                <option key={category.id} value={category.id}>\n                  {isRTL ? category.nameAr : category.name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Difficulty Filter */}\n          <div>\n            <select\n              value={selectedDifficulty}\n              onChange={(e) => setSelectedDifficulty(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {difficultyLevels.map(level => (\n                <option key={level.id} value={level.id}>\n                  {isRTL ? level.nameAr : level.name}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* Body Part Filter */}\n          <div>\n            <select\n              value={selectedBodyPart}\n              onChange={(e) => setSelectedBodyPart(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {bodyParts.map(part => (\n                <option key={part.id} value={part.id}>\n                  {isRTL ? part.nameAr : part.name}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Exercise Grid */}\n        {filteredExercises.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <i className=\"fas fa-search text-4xl text-gray-400 mb-4\"></i>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('noExercisesFound', 'No exercises found matching your criteria')}\n            </p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredExercises.map(exercise => (\n              <ExerciseCard\n                key={exercise.id}\n                exercise={exercise}\n                onSelect={handleExerciseSelect}\n                isSelected={isExerciseSelected(exercise.id)}\n                getDifficultyColor={getDifficultyColor}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Helper functions for exercise display\nconst getExerciseImage = (exercise) => {\n  // Generate a placeholder image based on exercise type and category\n  const imageMap = {\n    'shoulder-blade-squeeze': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',\n    'ankle-circles': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop&q=80',\n    'wall-pushups': 'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400&h=300&fit=crop&q=80'\n  };\n\n  // Category-based fallback images\n  const categoryImages = {\n    'strength': 'https://images.unsplash.com/photo-1581009146145-b5ef050c2e1e?w=400&h=300&fit=crop&q=80',\n    'flexibility': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop&q=80',\n    'balance': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',\n    'coordination': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',\n    'cardio': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',\n    'mobility': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop&q=80',\n    'posture': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',\n    'pain_relief': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop&q=80'\n  };\n\n  return imageMap[exercise.id] || categoryImages[exercise.category] || 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80';\n};\n\nconst getExerciseIcon = (exercise) => {\n  const iconMap = {\n    'strength': 'dumbbell',\n    'flexibility': 'expand-arrows-alt',\n    'balance': 'balance-scale',\n    'coordination': 'sync',\n    'cardio': 'heartbeat',\n    'mobility': 'walking',\n    'posture': 'user-check',\n    'pain_relief': 'hand-holding-medical'\n  };\n\n  return iconMap[exercise.category] || 'dumbbell';\n};\n\n// Exercise Card Component\nconst ExerciseCard = ({ exercise, onSelect, isSelected, getDifficultyColor }) => {\n  const { t, isRTL } = useLanguage();\n  const [showDetails, setShowDetails] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [showVideo, setShowVideo] = useState(false);\n\n  return (\n    <div className={`border rounded-lg overflow-hidden transition-all duration-200 hover:shadow-lg ${\n      isSelected ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-600'\n    }`}>\n      {/* Exercise Image/Video */}\n      <div className=\"relative h-48 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-gray-700 dark:to-gray-600\">\n        {!imageError ? (\n          <img\n            src={exercise.image || getExerciseImage(exercise)}\n            alt={isRTL ? exercise.nameAr : exercise.name}\n            className=\"w-full h-full object-cover\"\n            onError={() => setImageError(true)}\n          />\n        ) : (\n          <div className=\"w-full h-full flex flex-col items-center justify-center text-gray-500 dark:text-gray-400\">\n            <i className={`fas fa-${getExerciseIcon(exercise)} text-4xl mb-2`}></i>\n            <span className=\"text-sm font-medium text-center px-2\">\n              {isRTL ? exercise.nameAr : exercise.name}\n            </span>\n          </div>\n        )}\n\n        <div className=\"absolute top-2 right-2\">\n          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getDifficultyColor(exercise.difficulty)}`}>\n            {t(exercise.difficulty, exercise.difficulty)}\n          </span>\n        </div>\n\n        {exercise.video && (\n          <>\n            <div className=\"absolute top-2 left-2\">\n              <span className=\"px-2 py-1 text-xs font-medium rounded-full bg-red-500 text-white\">\n                <i className=\"fas fa-play mr-1\"></i>\n                {t('video', 'Video')}\n              </span>\n            </div>\n            <button\n              onClick={() => setShowVideo(true)}\n              className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity duration-200\"\n            >\n              <div className=\"bg-white bg-opacity-90 rounded-full p-3\">\n                <i className=\"fas fa-play text-blue-600 text-xl\"></i>\n              </div>\n            </button>\n          </>\n        )}\n\n        {isSelected && (\n          <div className=\"absolute bottom-2 left-2\">\n            <i className=\"fas fa-check-circle text-blue-600 text-xl bg-white rounded-full\"></i>\n          </div>\n        )}\n      </div>\n\n      {/* Exercise Info */}\n      <div className=\"p-4\">\n        <h4 className=\"font-semibold text-gray-900 dark:text-white mb-2\">\n          {isRTL ? exercise.nameAr : exercise.name}\n        </h4>\n        \n        <div className=\"space-y-2 text-sm text-gray-600 dark:text-gray-400\">\n          <div className=\"flex items-center\">\n            <i className=\"fas fa-clock mr-2\"></i>\n            {exercise.duration}\n          </div>\n          <div className=\"flex items-center\">\n            <i className=\"fas fa-tools mr-2\"></i>\n            {isRTL ? exercise.equipmentAr : exercise.equipment}\n          </div>\n          <div className=\"flex items-center\">\n            <i className=\"fas fa-star mr-2 text-yellow-500\"></i>\n            {exercise.rating} ({exercise.usageCount} {t('uses', 'uses')})\n          </div>\n        </div>\n\n        <div className=\"mt-4 flex items-center justify-between\">\n          <button\n            onClick={() => setShowDetails(!showDetails)}\n            className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n          >\n            {showDetails ? t('hideDetails', 'Hide Details') : t('viewDetails', 'View Details')}\n          </button>\n          \n          <button\n            onClick={() => onSelect(exercise)}\n            disabled={isSelected}\n            className={`px-3 py-1 text-sm rounded-lg transition-colors ${\n              isSelected\n                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                : 'bg-blue-600 text-white hover:bg-blue-700'\n            }`}\n          >\n            {isSelected ? t('added', 'Added') : t('addExercise', 'Add Exercise')}\n          </button>\n        </div>\n\n        {/* Exercise Details */}\n        {showDetails && (\n          <div className=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600\">\n            <div className=\"space-y-3 text-sm\">\n              <div>\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-1\">\n                  {t('instructions', 'Instructions')}:\n                </h5>\n                <ol className=\"list-decimal list-inside space-y-1 text-gray-600 dark:text-gray-400\">\n                  {(isRTL ? exercise.instructions.ar : exercise.instructions.en).map((step, index) => (\n                    <li key={index}>{step}</li>\n                  ))}\n                </ol>\n              </div>\n              \n              <div>\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-1\">\n                  {t('benefits', 'Benefits')}:\n                </h5>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {isRTL ? exercise.benefits.ar : exercise.benefits.en}\n                </p>\n              </div>\n              \n              <div>\n                <h5 className=\"font-medium text-gray-900 dark:text-white mb-1\">\n                  {t('precautions', 'Precautions')}:\n                </h5>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {isRTL ? exercise.precautions.ar : exercise.precautions.en}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Video Modal */}\n      {showVideo && exercise.video && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50\" onClick={() => setShowVideo(false)}>\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 max-w-4xl w-full mx-4\" onClick={(e) => e.stopPropagation()}>\n            <div className=\"flex justify-between items-center mb-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {isRTL ? exercise.nameAr : exercise.name}\n              </h3>\n              <button\n                onClick={() => setShowVideo(false)}\n                className=\"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200\"\n              >\n                <i className=\"fas fa-times text-xl\"></i>\n              </button>\n            </div>\n            <div className=\"aspect-video\">\n              <video\n                className=\"w-full h-full rounded-lg\"\n                controls\n                autoPlay\n                src={exercise.video}\n              >\n                Your browser does not support the video tag.\n              </video>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ExerciseLibrary;\n\n// Exercise Program Builder Component\nexport const ExerciseProgramBuilder = ({ patientId, onSave, initialProgram = null }) => {\n  const { t, isRTL } = useLanguage();\n  const [selectedExercises, setSelectedExercises] = useState(initialProgram?.exercises || []);\n  const [programName, setProgramName] = useState(initialProgram?.name || '');\n  const [programDescription, setProgramDescription] = useState(initialProgram?.description || '');\n  const [programDuration, setProgramDuration] = useState(initialProgram?.duration || '4');\n  const [frequency, setFrequency] = useState(initialProgram?.frequency || 'daily');\n  const [showLibrary, setShowLibrary] = useState(false);\n\n  const handleExerciseSelect = (exercise) => {\n    const exerciseWithSettings = {\n      ...exercise,\n      sets: 1,\n      reps: 10,\n      duration: '30 seconds',\n      restTime: '30 seconds',\n      notes: ''\n    };\n    setSelectedExercises([...selectedExercises, exerciseWithSettings]);\n    setShowLibrary(false);\n  };\n\n  const handleExerciseUpdate = (index, field, value) => {\n    const updated = [...selectedExercises];\n    updated[index] = { ...updated[index], [field]: value };\n    setSelectedExercises(updated);\n  };\n\n  const handleExerciseRemove = (index) => {\n    setSelectedExercises(selectedExercises.filter((_, i) => i !== index));\n  };\n\n  const handleSaveProgram = () => {\n    if (!programName.trim()) {\n      toast.error(t('programNameRequired', 'Program name is required'));\n      return;\n    }\n\n    if (selectedExercises.length === 0) {\n      toast.error(t('selectExercises', 'Please select at least one exercise'));\n      return;\n    }\n\n    const program = {\n      id: initialProgram?.id || Date.now(),\n      name: programName,\n      description: programDescription,\n      duration: programDuration,\n      frequency,\n      exercises: selectedExercises,\n      patientId,\n      createdAt: initialProgram?.createdAt || new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    };\n\n    if (onSave) {\n      onSave(program);\n    }\n\n    toast.success(t('programSaved', 'Exercise program saved successfully'));\n  };\n\n  return (\n    <div className=\"exercise-program-builder\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            <i className=\"fas fa-clipboard-list mr-2 text-green-600\"></i>\n            {t('exerciseProgramBuilder', 'Exercise Program Builder')}\n          </h3>\n          <button\n            onClick={() => setShowLibrary(true)}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('addExercise', 'Add Exercise')}\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Program Settings */}\n          <div className=\"lg:col-span-1 space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('programName', 'Program Name')}\n              </label>\n              <input\n                type=\"text\"\n                value={programName}\n                onChange={(e) => setProgramName(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterProgramName', 'Enter program name')}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('description', 'Description')}\n              </label>\n              <textarea\n                value={programDescription}\n                onChange={(e) => setProgramDescription(e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none\"\n                placeholder={t('enterDescription', 'Enter program description')}\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('programDuration', 'Program Duration (weeks)')}\n              </label>\n              <select\n                value={programDuration}\n                onChange={(e) => setProgramDuration(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              >\n                <option value=\"1\">1 {t('week', 'week')}</option>\n                <option value=\"2\">2 {t('weeks', 'weeks')}</option>\n                <option value=\"4\">4 {t('weeks', 'weeks')}</option>\n                <option value=\"6\">6 {t('weeks', 'weeks')}</option>\n                <option value=\"8\">8 {t('weeks', 'weeks')}</option>\n                <option value=\"12\">12 {t('weeks', 'weeks')}</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('frequency', 'Frequency')}\n              </label>\n              <select\n                value={frequency}\n                onChange={(e) => setFrequency(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              >\n                <option value=\"daily\">{t('daily', 'Daily')}</option>\n                <option value=\"every_other_day\">{t('everyOtherDay', 'Every Other Day')}</option>\n                <option value=\"3_times_week\">{t('threeTimes', '3 Times per Week')}</option>\n                <option value=\"2_times_week\">{t('twoTimes', '2 Times per Week')}</option>\n                <option value=\"weekly\">{t('weekly', 'Weekly')}</option>\n              </select>\n            </div>\n\n            <button\n              onClick={handleSaveProgram}\n              className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n            >\n              <i className=\"fas fa-save mr-2\"></i>\n              {t('saveProgram', 'Save Program')}\n            </button>\n          </div>\n\n          {/* Selected Exercises */}\n          <div className=\"lg:col-span-2\">\n            <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-4\">\n              {t('selectedExercises', 'Selected Exercises')} ({selectedExercises.length})\n            </h4>\n\n            {selectedExercises.length === 0 ? (\n              <div className=\"text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg\">\n                <i className=\"fas fa-dumbbell text-4xl text-gray-400 mb-4\"></i>\n                <p className=\"text-gray-500 dark:text-gray-400\">\n                  {t('noExercisesSelected', 'No exercises selected yet')}\n                </p>\n                <button\n                  onClick={() => setShowLibrary(true)}\n                  className=\"mt-2 text-blue-600 hover:text-blue-800 font-medium\"\n                >\n                  {t('addFirstExercise', 'Add your first exercise')}\n                </button>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {selectedExercises.map((exercise, index) => (\n                  <div key={`${exercise.id}-${index}`} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className=\"w-16 h-16 rounded-lg overflow-hidden bg-gradient-to-br from-blue-100 to-purple-100 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center\">\n                          <img\n                            src={exercise.image || getExerciseImage(exercise)}\n                            alt={isRTL ? exercise.nameAr : exercise.name}\n                            className=\"w-full h-full object-cover\"\n                            onError={(e) => {\n                              e.target.style.display = 'none';\n                              e.target.nextSibling.style.display = 'flex';\n                            }}\n                          />\n                          <div className=\"hidden w-full h-full flex-col items-center justify-center\">\n                            <i className={`fas fa-${getExerciseIcon(exercise)} text-lg text-gray-500 dark:text-gray-400`}></i>\n                          </div>\n                        </div>\n                        <div>\n                          <h5 className=\"font-medium text-gray-900 dark:text-white\">\n                            {isRTL ? exercise.nameAr : exercise.name}\n                          </h5>\n                          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                            {t(exercise.category, exercise.category)} • {t(exercise.difficulty, exercise.difficulty)}\n                          </p>\n                        </div>\n                      </div>\n                      <button\n                        onClick={() => handleExerciseRemove(index)}\n                        className=\"text-red-600 hover:text-red-800\"\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n\n                    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('sets', 'Sets')}\n                        </label>\n                        <input\n                          type=\"number\"\n                          min=\"1\"\n                          value={exercise.sets}\n                          onChange={(e) => handleExerciseUpdate(index, 'sets', parseInt(e.target.value))}\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('reps', 'Reps')}\n                        </label>\n                        <input\n                          type=\"number\"\n                          min=\"1\"\n                          value={exercise.reps}\n                          onChange={(e) => handleExerciseUpdate(index, 'reps', parseInt(e.target.value))}\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('duration', 'Duration')}\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={exercise.duration}\n                          onChange={(e) => handleExerciseUpdate(index, 'duration', e.target.value)}\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('rest', 'Rest')}\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={exercise.restTime}\n                          onChange={(e) => handleExerciseUpdate(index, 'restTime', e.target.value)}\n                          className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"mt-3\">\n                      <label className=\"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        {t('notes', 'Notes')}\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={exercise.notes}\n                        onChange={(e) => handleExerciseUpdate(index, 'notes', e.target.value)}\n                        className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                        placeholder={t('addNotes', 'Add notes for this exercise...')}\n                      />\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Exercise Library Modal */}\n      {showLibrary && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                  {t('selectExercise', 'Select Exercise')}\n                </h3>\n                <button\n                  onClick={() => setShowLibrary(false)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-times\"></i>\n                </button>\n              </div>\n              <ExerciseLibrary\n                onSelectExercise={handleExerciseSelect}\n                selectedExercises={selectedExercises}\n                patientId={patientId}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n"], "names": ["constructor", "this", "baseURL", "process", "storagePrefix", "initializeStorage", "getAuthToken", "localStorage", "getItem", "apiRequest", "endpoint", "options", "arguments", "length", "undefined", "token", "url", "concat", "defaultOptions", "headers", "finalOptions", "_objectSpread", "response", "fetch", "ok", "Error", "status", "json", "error", "console", "warn", "message", "fallbackToLocalStorage", "method", "data", "body", "JSON", "parse", "includes", "handleBodyMapFallback", "handleCommunicationFallback", "handleExerciseFallback", "handleAIFallback", "success", "setItem", "getDefaultPatients", "key", "item", "value", "stringify", "removeItem", "id", "name", "nameAr", "age", "gender", "condition", "conditionAr", "phone", "email", "communicationPreferences", "preferredChannels", "language", "quietHours", "start", "end", "createdAt", "Date", "toISOString", "getPatients", "Promise", "resolve", "setTimeout", "getPatient", "patientId", "patient", "find", "p", "saveBodyMapData", "bodyMapData", "allBodyMapData", "timestamp", "getBodyMapData", "sendMessage", "messageData", "history", "now", "toString", "push", "getCommunicationHistory", "patientHistory", "filter", "msg", "saveExerciseProgram", "programData", "programs", "program", "getExercisePrograms", "patientPrograms", "prog", "saveAIInteraction", "interactionData", "interactions", "interaction", "getAIInteractions", "patientInteractions", "int", "getAnalytics", "communications", "analytics", "totalAIQueries", "totalCommunications", "totalExercisePrograms", "totalBodyMapAssessments", "Object", "keys", "recentActivity", "slice", "map", "i", "type", "c", "sort", "a", "b", "clearAllData", "for<PERSON>ach", "exportData", "patients", "communicationHistory", "exercisePrograms", "aiInteractions", "exportedAt", "importData", "delay", "ms", "ExerciseLibrary", "_ref", "onSelectExercise", "selectedExercises", "t", "isRTL", "useLanguage", "exercises", "setExercises", "useState", "filteredExercises", "setFilteredExercises", "loading", "setLoading", "searchTerm", "setSearchTerm", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedDifficulty", "selectedBodyPart", "setSelectedBodyPart", "categories", "difficultyLevels", "bodyParts", "useEffect", "loadExercises", "filterExercises", "async", "category", "difficulty", "bodyPart", "duration", "equipment", "equipmentAr", "image", "video", "instructions", "en", "ar", "benefits", "precautions", "modifications", "tags", "created<PERSON>y", "approved", "rating", "usageCount", "toast", "filtered", "exercise", "toLowerCase", "some", "tag", "handleExerciseSelect", "getDifficultyColor", "_jsxs", "className", "children", "_jsx", "placeholder", "onChange", "e", "target", "level", "part", "ExerciseCard", "onSelect", "isSelected", "exerciseId", "ex", "getExerciseImage", "getExerciseIcon", "_ref2", "showDetails", "setShowDetails", "imageError", "setImageError", "showVideo", "setShowVideo", "src", "alt", "onError", "_Fragment", "onClick", "disabled", "step", "index", "stopPropagation", "controls", "autoPlay", "ExerciseProgramBuilder", "_ref3", "onSave", "initialProgram", "setSelectedExercises", "programName", "setProgramName", "programDescription", "setProgramDescription", "description", "programDuration", "setProgramDuration", "frequency", "setFrequency", "showLibrary", "setShowLibrary", "handleExerciseUpdate", "field", "updated", "rows", "handleSaveProgram", "trim", "updatedAt", "style", "display", "nextS<PERSON>ling", "_", "handleExerciseRemove", "min", "sets", "parseInt", "reps", "restTime", "notes", "exerciseWithSettings"], "sourceRoot": ""}