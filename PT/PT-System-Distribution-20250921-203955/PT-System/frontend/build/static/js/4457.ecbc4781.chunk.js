"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[4457],{4457:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var s=a(5043),i=a(3216),r=a(5475),n=a(7921),o=a(4120),l=a(579);const d=()=>{var e;const{t:t,isRTL:a}=(0,n.o)(),d=(0,i.Zp)(),c=(0,i.zy)(),[m]=(0,r.ok)(),[p,x]=(0,s.useState)(null),[h,f]=(0,s.useState)(null);(0,s.useEffect)(()=>{var e,t;if(null!==(e=c.state)&&void 0!==e&&e.patient&&null!==(t=c.state)&&void 0!==t&&t.fromPatientProfile){var a,s,i;const e=c.state.patient;f(e),x({patientId:e._id||e.id,patientName:e.name||"".concat(e.firstName," ").concat(e.lastName),therapist:(null===(a=e.therapyInfo)||void 0===a?void 0:a.primaryTherapist)||(null===(s=e.primaryTherapist)||void 0===s?void 0:s.firstName)+" "+(null===(i=e.primaryTherapist)||void 0===i?void 0:i.lastName)||"",reassessmentDate:(new Date).toISOString().split("T")[0],assessor:"Current User",facility:"PT Clinic"})}else{const e=m.get("patientId"),t=m.get("patientName"),a=m.get("therapist");e&&t&&x({patientId:e,patientName:decodeURIComponent(t),therapist:a?decodeURIComponent(a):"",reassessmentDate:(new Date).toISOString().split("T")[0],assessor:"Current User",facility:"PT Clinic"})}},[m,c.state]);const g=()=>{d("/patients/".concat((null===p||void 0===p?void 0:p.patientId)||""))};return(0,l.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("button",{onClick:g,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,l.jsx)("i",{className:"fas fa-arrow-".concat(a?"right":"left"," text-gray-600 dark:text-gray-400")})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:t("reassessment","Reassessment")}),(null===p||void 0===p?void 0:p.patientName)&&(0,l.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:[t("patient","Patient"),": ",p.patientName]})]})]}),(0,l.jsx)("div",{className:"flex space-x-3",children:(0,l.jsxs)("button",{onClick:g,className:"px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-times mr-2"}),t("cancel","Cancel")]})})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,l.jsx)(o.A,{patientData:h,fromPatientProfile:null===(e=c.state)||void 0===e?void 0:e.fromPatientProfile,initialData:p,onSubmit:e=>{console.log("Reassessment submitted:",e),d("/patients/".concat((null===p||void 0===p?void 0:p.patientId)||""))},onCancel:g})})]})}}}]);
//# sourceMappingURL=4457.ecbc4781.chunk.js.map