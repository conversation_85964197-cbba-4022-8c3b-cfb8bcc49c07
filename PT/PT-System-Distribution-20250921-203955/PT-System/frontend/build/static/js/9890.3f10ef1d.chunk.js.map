{"version": 3, "file": "static/js/9890.3f10ef1d.chunk.js", "mappings": "yLAqBA,MAsXA,EAtX0BA,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAC9B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,iBAC9CC,EAAgBC,IAAqBF,EAAAA,EAAAA,UAAS,QAC9CG,EAAeC,IAAoBJ,EAAAA,EAAAA,UAAS,CAAC,IAC7CK,EAASC,IAAcN,EAAAA,EAAAA,WAAS,IAGvCO,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAW,CACfC,SAAU,CACRC,cAAe,KACfC,eAAgB,IAChBC,oBAAqB,IACrBC,kBAAmB,IACnBC,sBAAuB,KACvBC,yBAA0B,GAC1BC,cAAe,MACfC,kBAAmB,MACnBC,kBAAmB,KACnBC,eAAgB,MAElBC,gBAAiB,CACfC,UAAW,IACXC,WAAY,IACZC,OAAQ,GACRC,aAAc,GACdC,WAAY,KAEdC,oBAAqB,CACnBC,gBAAiB,CAAEN,UAAW,IAAKO,MAAO,IAAKC,KAAM,MACrDC,oBAAqB,CAAET,UAAW,IAAKO,MAAO,IAAKC,KAAM,MACzDE,cAAe,CAAEV,UAAW,GAAIO,MAAO,IAAKC,KAAM,MAClDG,aAAc,CAAEX,UAAW,GAAIO,MAAO,GAAIC,KAAM,MAChDI,aAAc,CAAEZ,UAAW,GAAIO,MAAO,GAAIC,KAAM,OAElDK,aAAc,CACZC,UAAW,CACT,OAAQ,IACR,QAAS,IACT,QAAS,IACT,QAAS,IACT,MAAO,KAETC,OAAQ,CACNC,KAAM,IACNC,OAAQ,KAEVC,WAAY,CACV,iBAAkB,IAClB,kBAAmB,IACnB,kBAAmB,IACnB,gBAAiB,GACjB,uBAAwB,IACxB,yBAA0B,IAC1B,wBAAyB,IACzB,MAAS,MAGbC,eAAgB,CACdC,sBAAuB,CACrBC,YAAa,GACbC,SAAU,IACVC,QAAS,GACTC,KAAM,IAERC,gBAAiB,CACfC,SAAU,GACVC,SAAU,IACVC,kBAAmB,IACnBC,YAAa,IAEfC,mBAAoB,CAClBC,UAAW,IACXC,KAAM,IACNC,KAAM,GACNC,KAAM,KAGVC,aAAc,CACZC,gBAAiB,IACjBC,uBAAwB,GACxBC,uBAAwB,GACxBC,uBAAwB,MAE1BC,iBAAkB,CAChBC,aAAc,QACdC,yBAA0B,KAC1BC,kBAAmB,KACnBC,oBAAqB,KACrBC,eAAgB,MAElBC,iBAAkB,CAChBC,WAAY,CACV,CAAEC,KAAM,kBAAmBC,SAAU,GAAIC,eAAgB,KAAMC,aAAc,KAC7E,CAAEH,KAAM,mBAAoBC,SAAU,GAAIC,eAAgB,KAAMC,aAAc,KAC9E,CAAEH,KAAM,oBAAqBC,SAAU,GAAIC,eAAgB,KAAMC,aAAc,KAC/E,CAAEH,KAAM,mBAAoBC,SAAU,GAAIC,eAAgB,KAAMC,aAAc,OAGlFC,OAAQ,CACNC,kBAAmB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAChEC,mBAAoB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACjEC,kBAAmB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,OAI/EC,WAAW,KACTzE,EAAiBI,GACjBF,GAAW,IACV,MACF,CAACR,IAEJ,MAAMgF,EAAWC,IAAA,IAAC,MAAEC,EAAK,MAAEC,EAAK,SAAEC,EAAQ,KAAEC,EAAI,MAAEC,EAAK,MAAEC,EAAK,WAAEC,GAAYP,EAAA,OAC1EQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAAET,KACrEU,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAAER,IAChEC,IACCQ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEP,QAG7DQ,EAAAA,EAAAA,KAAA,OAAKF,UAAS,UAAAG,OAAYP,EAAK,iBAAAO,OAAgBP,EAAK,sBAAqBK,UACvEC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAG,OAAKR,EAAI,UAAAQ,OAASP,EAAK,mBAAAO,OAAkBP,EAAK,uBAG7DC,IACCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,uBAAAG,OACH,OAAVN,EAAiB,iBAA6B,SAAVA,EAAmB,eAAiB,iBACvEI,SAAA,EACDC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,gBAAAG,OAA4B,OAAVN,EAAiB,KAAiB,SAAVA,EAAmB,OAAS,QAAO,WACxFC,EAAW,QAEdI,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gDAA+CC,SAC5D9F,EAAE,iBAAkB,6BAOzBiG,EAAkBA,KACtB,IAAKzF,EAAcuB,oBACjB,OAAOgE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SAAC,0BAG9E,MAAMI,EAAS,CAAC,cAAe,eAAgB,gBAAiB,aAAc,iBAE9E,OACEN,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE9F,EAAE,2BAA4B,wCAEhCmG,OAAOC,QAAQ5F,EAAcuB,qBAAqBsE,IAAI,CAAAC,EAAeC,KAAK,IAAlBC,EAAMC,GAAKH,EAAA,OAClEV,EAAAA,EAAAA,MAAA,OAAgBC,UAAU,YAAWC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAEU,KACxEZ,EAAAA,EAAAA,MAAA,QAAMC,UAAU,kDAAiDC,SAAA,CAAEW,EAAKvE,KAAK,WAE/E6D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,oBAAAG,OAAsBE,EAAOK,EAAQL,EAAOQ,QAAO,gCAC5DC,MAAO,CAAEC,MAAM,GAADZ,OAAKS,EAAKvE,KAAI,WAGhC0D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDW,EAAK/E,UAAU,OAAK+E,EAAKxE,MAAM,kBAZ1BuE,SAoBZK,EAAqBA,KACzB,IAAKrG,EAAciB,gBACjB,OAAOsE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SAAC,0BAG9E,MAAMgB,EAAS,CACb9G,EAAE,YAAa,aACfA,EAAE,aAAc,eAChBA,EAAE,SAAU,WACZA,EAAE,eAAgB,gBAClBA,EAAE,aAAc,gBAGZ+G,EAASZ,OAAOY,OAAOvG,EAAciB,iBACrCQ,EAAQ8E,EAAOC,OAAO,CAACC,EAAKC,IAAQD,EAAMC,EAAK,GAC/ChB,EAAS,CAAC,iBAAkB,gBAAiB,kBAAmB,eAAgB,iBAChFiB,EAAW,CAAC,eAAgB,cAAe,gBAAiB,aAAc,eAEhF,OACEvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE9F,EAAE,8BAA+B,oCAEpC+F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yBAAwBC,SACpCgB,EAAOT,IAAI,CAACe,EAAOb,KAClB,MAAMjB,EAAQyB,EAAOR,GACfZ,EAAa1D,EAAQ,EAAIoF,KAAKC,MAAOhC,EAAQrD,EAAS,KAAO,EACnE,OACE2D,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,+EAA8EC,SAAA,EACvGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,wBAAAG,OAA0BmB,EAASZ,OACjDR,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAEsB,QAE1ExB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,qBAAAG,OAAuBE,EAAOK,IAAST,SAAER,KACvDM,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CAAEH,EAAW,YAPhEyB,WAiBhBG,EAAcA,KAClB,IAAK/G,EAAcsE,OACjB,OAAOiB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SAAC,0BAG9E,MACM0B,EAAahH,EAAcsE,OAAOC,mBAAqB,GACvD0C,EAAcjH,EAAcsE,OAAOE,oBAAsB,GACzD0C,EAAWL,KAAKM,OAAOH,KAAeC,GAE5C,OACE7B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE9F,EAAE,gBAAiB,qBAEtB4F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sCAAqCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAE9F,EAAE,aAAc,oBAEtE4F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kCACfE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAE9F,EAAE,cAAe,wBAGzE+F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+BAA8BC,SArBpC,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,OAsB7EO,IAAI,CAACuB,EAAOrB,KAClB,MAAMsB,EAAkBH,EAAW,EAAKF,EAAWjB,GAASmB,EAAY,IAAM,EACxEI,EAAmBJ,EAAW,EAAKD,EAAYlB,GAASmB,EAAY,IAAM,EAChF,OACE9B,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,uCAAsCC,SAAA,EAC/DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,wBACVc,MAAO,CAAEoB,OAAO,GAAD/B,OAAK6B,EAAe,KAAKG,UAAW,OACnD3C,MAAK,GAAAW,OAAK4B,EAAK,MAAA5B,OAAKwB,EAAWjB,GAAM,kBAEvCR,EAAAA,EAAAA,KAAA,OACEF,UAAU,yBACVc,MAAO,CAAEoB,OAAO,GAAD/B,OAAK8B,EAAgB,KAAKE,UAAW,OACpD3C,MAAK,GAAAW,OAAK4B,EAAK,MAAA5B,OAAKyB,EAAYlB,GAAM,sBAG1CR,EAAAA,EAAAA,KAAA,QAAMF,UAAU,kFAAiFC,SAC9F8B,MAdKA,cAyBxB,OAAIlH,GAEAqF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAMnBD,EAAAA,EAAAA,MAAA,OAAKC,UAAS,aAAAG,OAAe/F,EAAQ,cAAgB,gBAAiB6F,SAAA,EAEpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D9F,EAAE,oBAAqB,qCAE1B+F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD9F,EAAE,iCAAkC,kEAGzC4F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEN,MAAOnF,EACP8H,SAAWC,GAAM9H,EAAkB8H,EAAEC,OAAO7C,OAC5CO,UAAU,2HAA0HC,SAAA,EAEpIC,EAAAA,EAAAA,KAAA,UAAQT,MAAM,cAAaQ,SAAE9F,EAAE,YAAa,kBAC5C+F,EAAAA,EAAAA,KAAA,UAAQT,MAAM,eAAcQ,SAAE9F,EAAE,aAAc,mBAC9C+F,EAAAA,EAAAA,KAAA,UAAQT,MAAM,eAAcQ,SAAE9F,EAAE,aAAc,mBAC9C+F,EAAAA,EAAAA,KAAA,UAAQT,MAAM,YAAWQ,SAAE9F,EAAE,WAAY,gBACzC+F,EAAAA,EAAAA,KAAA,UAAQT,MAAM,WAAUQ,SAAE9F,EAAE,UAAW,kBAEzC4F,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZ7F,EAAE,eAAgB,2BAMzB4F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACZ,EAAQ,CACPE,MAAOrF,EAAE,gBAAiB,kBAC1BsF,MAA6B,QAAxB/F,EAAEiB,EAAcM,gBAAQ,IAAAvB,GAAe,QAAfC,EAAtBD,EAAwBwB,qBAAa,IAAAvB,OAAf,EAAtBA,EAAuC4I,iBAC9C7C,SAAQ,GAAAS,OAA2B,QAA3BvG,EAAKe,EAAcM,gBAAQ,IAAArB,OAAA,EAAtBA,EAAwBuB,eAAc,KAAAgF,OAAIhG,EAAE,SAAU,WACnEwF,KAAK,eACLC,MAAM,OACNC,MAAM,KACNC,WAAW,UAEbI,EAAAA,EAAAA,KAACZ,EAAQ,CACPE,MAAOrF,EAAE,iBAAkB,mBAC3BsF,MAAK,GAAAU,OAA2B,QAA3BtG,EAAKc,EAAcM,gBAAQ,IAAApB,OAAA,EAAtBA,EAAwByB,sBAAqB,KACvDoE,SAAQ,GAAAS,OAA2B,QAA3BrG,EAAKa,EAAcM,gBAAQ,IAAAnB,OAAA,EAAtBA,EAAwBsB,oBAAmB,KAAA+E,OAAIhG,EAAE,YAAa,cAC3EwF,KAAK,sBACLC,MAAM,QACNC,MAAM,KACNC,WAAW,SAEbI,EAAAA,EAAAA,KAACZ,EAAQ,CACPE,MAAOrF,EAAE,kBAAmB,oBAC5BsF,MAAK,GAAAU,OAA2B,QAA3BpG,EAAKY,EAAcM,gBAAQ,IAAAlB,OAAA,EAAtBA,EAAwBwB,yBAAwB,KAAA4E,OAAIhG,EAAE,OAAQ,SACxEuF,SAAUvF,EAAE,kBAAmB,oBAC/BwF,KAAK,eACLC,MAAM,SACNC,MAAM,OACNC,WAAW,SAEbI,EAAAA,EAAAA,KAACZ,EAAQ,CACPE,MAAOrF,EAAE,gBAAiB,kBAC1BsF,MAA6B,QAAxBzF,EAAEW,EAAcM,gBAAQ,IAAAjB,GAAe,QAAfC,EAAtBD,EAAwBwB,qBAAa,IAAAvB,OAAf,EAAtBA,EAAuCsI,iBAC9C7C,SAAQ,GAAAS,OAA2B,QAA3BjG,EAAKS,EAAcM,gBAAQ,IAAAf,OAAA,EAAtBA,EAAwBuB,kBAAiB,KAAA0E,OAAIhG,EAAE,YAAa,cACzEwF,KAAK,wBACLC,MAAM,SACNC,MAAM,KACNC,WAAW,YAKfC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,KAACE,EAAe,OAElBF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,KAACc,EAAkB,UAKvBd,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,KAACwB,EAAW,S", "sources": ["pages/Analytics/AdvancedAnalytics.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\nimport { format, subDays, startOfMonth, endOfMonth } from 'date-fns';\n\nconst AdvancedAnalytics = () => {\n  const { t, isRTL } = useLanguage();\n  const [selectedPeriod, setSelectedPeriod] = useState('last-30-days');\n  const [selectedMetric, setSelectedMetric] = useState('all');\n  const [analyticsData, setAnalyticsData] = useState({});\n  const [loading, setLoading] = useState(true);\n\n  // Mock comprehensive analytics data\n  useEffect(() => {\n    const mockData = {\n      overview: {\n        totalPatients: 1247,\n        activePatients: 892,\n        completedTreatments: 456,\n        ongoingTreatments: 436,\n        averageCompletionRate: 78.5,\n        averageTreatmentDuration: 42, // days\n        totalSessions: 15678,\n        completedSessions: 12543,\n        cancelledSessions: 1234,\n        noShowSessions: 1901\n      },\n      patientProgress: {\n        completed: 456,\n        inProgress: 436,\n        onHold: 67,\n        discontinued: 89,\n        notStarted: 199\n      },\n      treatmentCompletion: {\n        physicalTherapy: { completed: 234, total: 298, rate: 78.5 },\n        occupationalTherapy: { completed: 156, total: 201, rate: 77.6 },\n        speechTherapy: { completed: 89, total: 123, rate: 72.4 },\n        specialNeeds: { completed: 67, total: 89, rate: 75.3 },\n        carfPrograms: { completed: 45, total: 56, rate: 80.4 }\n      },\n      demographics: {\n        ageGroups: {\n          '0-18': 234,\n          '19-35': 345,\n          '36-50': 298,\n          '51-65': 267,\n          '65+': 103\n        },\n        gender: {\n          male: 623,\n          female: 624\n        },\n        conditions: {\n          'Cerebral Palsy': 156,\n          'Autism Spectrum': 134,\n          'Stroke Recovery': 189,\n          'Spinal Injury': 98,\n          'Developmental Delays': 167,\n          'Neurological Disorders': 145,\n          'Orthopedic Conditions': 234,\n          'Other': 124\n        }\n      },\n      outcomeMetrics: {\n        functionalImprovement: {\n          significant: 67, // >75% improvement\n          moderate: 156,   // 50-75% improvement\n          minimal: 89,     // 25-50% improvement\n          none: 34         // <25% improvement\n        },\n        goalAchievement: {\n          exceeded: 89,\n          achieved: 234,\n          partiallyAchieved: 156,\n          notAchieved: 67\n        },\n        satisfactionScores: {\n          excellent: 456,\n          good: 234,\n          fair: 89,\n          poor: 23\n        }\n      },\n      timeAnalysis: {\n        averageWaitTime: 5.2, // days\n        averageSessionDuration: 45, // minutes\n        averageTreatmentLength: 42, // days\n        completionTimeVariance: 12.5 // days\n      },\n      financialMetrics: {\n        totalRevenue: 2456789,\n        averageRevenuePerPatient: 1970,\n        insuranceCoverage: 78.5,\n        outOfPocketPayments: 21.5,\n        collectionRate: 94.2\n      },\n      staffPerformance: {\n        therapists: [\n          { name: 'Dr. Sarah Ahmed', patients: 45, completionRate: 85.2, satisfaction: 4.8 },\n          { name: 'Dr. Mohammed Ali', patients: 38, completionRate: 82.1, satisfaction: 4.7 },\n          { name: 'Dr. Fatima Hassan', patients: 42, completionRate: 88.9, satisfaction: 4.9 },\n          { name: 'Dr. Ahmed Khalil', patients: 35, completionRate: 79.3, satisfaction: 4.6 }\n        ]\n      },\n      trends: {\n        monthlyAdmissions: [45, 52, 48, 61, 58, 67, 72, 69, 74, 78, 82, 85],\n        monthlyCompletions: [34, 38, 42, 45, 48, 52, 56, 59, 62, 65, 68, 71],\n        satisfactionTrend: [4.2, 4.3, 4.4, 4.5, 4.6, 4.7, 4.8, 4.7, 4.8, 4.9, 4.8, 4.9]\n      }\n    };\n\n    setTimeout(() => {\n      setAnalyticsData(mockData);\n      setLoading(false);\n    }, 1000);\n  }, [selectedPeriod]);\n\n  const StatCard = ({ title, value, subtitle, icon, color, trend, percentage }) => (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{title}</p>\n          <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{value}</p>\n          {subtitle && (\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">{subtitle}</p>\n          )}\n        </div>\n        <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/30 rounded-lg`}>\n          <i className={`${icon} text-${color}-600 dark:text-${color}-400 text-xl`}></i>\n        </div>\n      </div>\n      {trend && (\n        <div className=\"mt-4 flex items-center\">\n          <span className={`text-sm font-medium ${\n            trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'\n          }`}>\n            <i className={`fas fa-arrow-${trend === 'up' ? 'up' : trend === 'down' ? 'down' : 'right'} mr-1`}></i>\n            {percentage}%\n          </span>\n          <span className=\"text-sm text-gray-500 dark:text-gray-400 ml-2\">\n            {t('fromLastPeriod', 'from last period')}\n          </span>\n        </div>\n      )}\n    </div>\n  );\n\n  const CompletionChart = () => {\n    if (!analyticsData.treatmentCompletion) {\n      return <div className=\"flex items-center justify-center h-64 text-gray-500\">Loading chart data...</div>;\n    }\n\n    const colors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-purple-500'];\n\n    return (\n      <div className=\"space-y-4\">\n        <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {t('treatmentCompletionRates', 'Treatment Completion Rates by Type')}\n        </h4>\n        {Object.entries(analyticsData.treatmentCompletion).map(([type, data], index) => (\n          <div key={type} className=\"space-y-2\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{type}</span>\n              <span className=\"text-sm font-bold text-gray-900 dark:text-white\">{data.rate}%</span>\n            </div>\n            <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n              <div\n                className={`h-3 rounded-full ${colors[index % colors.length]} transition-all duration-500`}\n                style={{ width: `${data.rate}%` }}\n              ></div>\n            </div>\n            <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n              {data.completed} of {data.total} completed\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  const PatientProgressPie = () => {\n    if (!analyticsData.patientProgress) {\n      return <div className=\"flex items-center justify-center h-64 text-gray-500\">Loading chart data...</div>;\n    }\n\n    const labels = [\n      t('completed', 'Completed'),\n      t('inProgress', 'In Progress'),\n      t('onHold', 'On Hold'),\n      t('discontinued', 'Discontinued'),\n      t('notStarted', 'Not Started')\n    ];\n\n    const values = Object.values(analyticsData.patientProgress);\n    const total = values.reduce((sum, val) => sum + val, 0);\n    const colors = ['text-green-600', 'text-blue-600', 'text-yellow-600', 'text-red-600', 'text-gray-600'];\n    const bgColors = ['bg-green-500', 'bg-blue-500', 'bg-yellow-500', 'bg-red-500', 'bg-gray-500'];\n\n    return (\n      <div className=\"space-y-4\">\n        <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {t('patientProgressDistribution', 'Patient Progress Distribution')}\n        </h4>\n        <div className=\"grid grid-cols-1 gap-3\">\n          {labels.map((label, index) => {\n            const value = values[index];\n            const percentage = total > 0 ? Math.round((value / total) * 100) : 0;\n            return (\n              <div key={label} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className={`w-4 h-4 rounded-full ${bgColors[index]}`}></div>\n                  <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{label}</span>\n                </div>\n                <div className=\"text-right\">\n                  <div className={`text-lg font-bold ${colors[index]}`}>{value}</div>\n                  <div className=\"text-xs text-gray-500 dark:text-gray-400\">{percentage}%</div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    );\n  };\n\n  const TrendsChart = () => {\n    if (!analyticsData.trends) {\n      return <div className=\"flex items-center justify-center h-64 text-gray-500\">Loading chart data...</div>;\n    }\n\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const admissions = analyticsData.trends.monthlyAdmissions || [];\n    const completions = analyticsData.trends.monthlyCompletions || [];\n    const maxValue = Math.max(...admissions, ...completions);\n\n    return (\n      <div className=\"space-y-4\">\n        <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {t('monthlyTrends', 'Monthly Trends')}\n        </h4>\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center space-x-4 text-sm\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-blue-500 rounded\"></div>\n              <span className=\"text-gray-700 dark:text-gray-300\">{t('admissions', 'Admissions')}</span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-green-500 rounded\"></div>\n              <span className=\"text-gray-700 dark:text-gray-300\">{t('completions', 'Completions')}</span>\n            </div>\n          </div>\n          <div className=\"grid grid-cols-12 gap-2 h-48\">\n            {months.map((month, index) => {\n              const admissionHeight = maxValue > 0 ? (admissions[index] / maxValue) * 100 : 0;\n              const completionHeight = maxValue > 0 ? (completions[index] / maxValue) * 100 : 0;\n              return (\n                <div key={month} className=\"flex flex-col items-center space-y-1\">\n                  <div className=\"flex-1 flex flex-col justify-end space-y-1 w-full\">\n                    <div\n                      className=\"bg-blue-500 rounded-t\"\n                      style={{ height: `${admissionHeight}%`, minHeight: '2px' }}\n                      title={`${month}: ${admissions[index]} admissions`}\n                    ></div>\n                    <div\n                      className=\"bg-green-500 rounded-t\"\n                      style={{ height: `${completionHeight}%`, minHeight: '2px' }}\n                      title={`${month}: ${completions[index]} completions`}\n                    ></div>\n                  </div>\n                  <span className=\"text-xs text-gray-500 dark:text-gray-400 transform rotate-45 origin-bottom-left\">\n                    {month}\n                  </span>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('advancedAnalytics', 'Advanced Analytics & Reporting')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('comprehensivePatientStatistics', 'Comprehensive patient statistics and treatment analytics')}\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <select\n            value={selectedPeriod}\n            onChange={(e) => setSelectedPeriod(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          >\n            <option value=\"last-7-days\">{t('last7Days', 'Last 7 Days')}</option>\n            <option value=\"last-30-days\">{t('last30Days', 'Last 30 Days')}</option>\n            <option value=\"last-90-days\">{t('last90Days', 'Last 90 Days')}</option>\n            <option value=\"last-year\">{t('lastYear', 'Last Year')}</option>\n            <option value=\"all-time\">{t('allTime', 'All Time')}</option>\n          </select>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportReport', 'Export Report')}\n          </button>\n        </div>\n      </div>\n\n      {/* Overview Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <StatCard\n          title={t('totalPatients', 'Total Patients')}\n          value={analyticsData.overview?.totalPatients?.toLocaleString()}\n          subtitle={`${analyticsData.overview?.activePatients} ${t('active', 'active')}`}\n          icon=\"fas fa-users\"\n          color=\"blue\"\n          trend=\"up\"\n          percentage=\"12.5\"\n        />\n        <StatCard\n          title={t('completionRate', 'Completion Rate')}\n          value={`${analyticsData.overview?.averageCompletionRate}%`}\n          subtitle={`${analyticsData.overview?.completedTreatments} ${t('completed', 'completed')}`}\n          icon=\"fas fa-check-circle\"\n          color=\"green\"\n          trend=\"up\"\n          percentage=\"5.2\"\n        />\n        <StatCard\n          title={t('averageDuration', 'Average Duration')}\n          value={`${analyticsData.overview?.averageTreatmentDuration} ${t('days', 'days')}`}\n          subtitle={t('treatmentLength', 'treatment length')}\n          icon=\"fas fa-clock\"\n          color=\"yellow\"\n          trend=\"down\"\n          percentage=\"3.1\"\n        />\n        <StatCard\n          title={t('totalSessions', 'Total Sessions')}\n          value={analyticsData.overview?.totalSessions?.toLocaleString()}\n          subtitle={`${analyticsData.overview?.completedSessions} ${t('completed', 'completed')}`}\n          icon=\"fas fa-calendar-check\"\n          color=\"purple\"\n          trend=\"up\"\n          percentage=\"8.7\"\n        />\n      </div>\n\n      {/* Charts Row 1 */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <CompletionChart />\n        </div>\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <PatientProgressPie />\n        </div>\n      </div>\n\n      {/* Trends Chart */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <TrendsChart />\n      </div>\n    </div>\n  );\n};\n\nexport default AdvancedAnalytics;\n"], "names": ["AdvancedAnalytics", "_analyticsData$overvi", "_analyticsData$overvi2", "_analyticsData$overvi3", "_analyticsData$overvi4", "_analyticsData$overvi5", "_analyticsData$overvi6", "_analyticsData$overvi7", "_analyticsData$overvi8", "_analyticsData$overvi9", "t", "isRTL", "useLanguage", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "useState", "selectedMetric", "setSelectedMetric", "analyticsData", "setAnalyticsData", "loading", "setLoading", "useEffect", "mockData", "overview", "totalPatients", "activePatients", "completedTreatments", "ongoingTreatments", "averageCompletionRate", "averageTreatmentDuration", "totalSessions", "completedSessions", "cancelledSessions", "noShowSessions", "patientProgress", "completed", "inProgress", "onHold", "discontinued", "notStarted", "treatmentCompletion", "physicalTherapy", "total", "rate", "occupationalTherapy", "speechTherapy", "specialNeeds", "carfPrograms", "demographics", "ageGroups", "gender", "male", "female", "conditions", "outcomeMetrics", "functionalImprovement", "significant", "moderate", "minimal", "none", "goalAchievement", "exceeded", "achieved", "partiallyAchieved", "notAchieved", "satisfactionScores", "excellent", "good", "fair", "poor", "timeAnalysis", "averageWaitTime", "averageSessionDuration", "averageTreatmentLength", "completionTimeVariance", "financialMetrics", "totalRevenue", "averageRevenuePerPatient", "insuranceCoverage", "outOfPocketPayments", "collectionRate", "staffPerformance", "therapists", "name", "patients", "completionRate", "satisfaction", "trends", "monthlyAdmissions", "monthlyCompletions", "satisfactionTrend", "setTimeout", "StatCard", "_ref", "title", "value", "subtitle", "icon", "color", "trend", "percentage", "_jsxs", "className", "children", "_jsx", "concat", "CompletionChart", "colors", "Object", "entries", "map", "_ref2", "index", "type", "data", "length", "style", "width", "PatientProgress<PERSON>ie", "labels", "values", "reduce", "sum", "val", "bgColors", "label", "Math", "round", "TrendsChart", "admissions", "completions", "maxValue", "max", "month", "admissionHeight", "completionHeight", "height", "minHeight", "onChange", "e", "target", "toLocaleString"], "sourceRoot": ""}