{"version": 3, "file": "static/js/9444.dc6c180d.chunk.js", "mappings": "6MAIA,MA+lBA,EA/lBuBA,KACrB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAOC,IAAYC,EAAAA,EAAAA,UAAS,KAC5BC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,OAC1CG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,GAGjCK,EAAkB,CACtBC,MAAO,CACLC,KAAMZ,EAAE,QAAS,iEACjBa,OAAQ,gBACRC,YAAad,EAAE,YAAa,0NAC5Be,MAAO,OACPC,KAAM,eACNC,UAAW,EACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAI3BC,OAAQ,CACNvC,KAAMZ,EAAE,SAAU,4BAClBa,OAAQ,SACRC,YAAad,EAAE,aAAc,kRAC7Be,MAAO,OACPC,KAAM,iBACNC,UAAW,EACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAI3BE,UAAW,CACTxC,KAAMZ,EAAE,YAAa,wHACrBa,OAAQ,qBACRC,YAAad,EAAE,gBAAiB,qNAChCe,MAAO,QACPC,KAAM,uBACNC,UAAW,EACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAI3BG,MAAO,CACLzC,KAAMZ,EAAE,QAAS,2DACjBa,OAAQ,QACRC,YAAad,EAAE,YAAa,gQAC5Be,MAAO,OACPC,KAAM,oBACNC,UAAW,GACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAI3BI,aAAc,CACZ1C,KAAMZ,EAAE,cAAe,iEACvBa,OAAQ,sBACRC,YAAad,EAAE,kBAAmB,kMAClCe,MAAO,SACPC,KAAM,eACNC,UAAW,EACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAI3BK,mBAAoB,CAClB3C,KAAMZ,EAAE,oBAAqB,oFAC7Ba,OAAQ,qBACRC,YAAad,EAAE,wBAAyB,4LACxCe,MAAO,SACPC,KAAM,eACNC,UAAW,EACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAI3BM,aAAc,CACZ5C,KAAMZ,EAAE,eAAgB,uEACxBa,OAAQ,eACRC,YAAad,EAAE,mBAAoB,iJACnCe,MAAO,SACPC,KAAM,wBACNC,UAAW,EACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAI3BO,QAAS,CACP7C,KAAMZ,EAAE,UAAW,4BACnBa,OAAQ,UACRC,YAAad,EAAE,cAAe,sOAC9Be,MAAO,MACPC,KAAM,kBACNC,UAAW,EACXC,YAAa,CACXC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,MAOvBQ,EAAuB,CAC3BvC,MAAO,CACLP,KAAMZ,EAAE,mBAAoB,yFAC5Ba,OAAQ,oBACRK,YAAa,CACXE,iBAAkBpB,EAAE,mBAAoB,6EACxCqB,aAAcrB,EAAE,eAAgB,kHAChCsB,aAActB,EAAE,eAAgB,6EAChCuB,oBAAqBvB,EAAE,sBAAuB,uEAC9CwB,aAAcxB,EAAE,eAAgB,mFAChCyB,uBAAwBzB,EAAE,yBAA0B,iIACpD0B,cAAe1B,EAAE,gBAAiB,4GAClC2B,0BAA2B3B,EAAE,4BAA6B,6IAC1D4B,sBAAuB5B,EAAE,wBAAyB,uIAClD6B,YAAa7B,EAAE,cAAe,uJAC9B8B,mBAAoB9B,EAAE,qBAAsB,qGAC5C+B,WAAY/B,EAAE,aAAc,6EAC5BgC,iBAAkBhC,EAAE,mBAAoB,yEAG5CiC,OAAQ,CACNrB,KAAMZ,EAAE,oBAAqB,mFAC7Ba,OAAQ,qBACRK,YAAa,CACXgB,aAAclC,EAAE,eAAgB,2DAChCmC,eAAgBnC,EAAE,iBAAkB,2DACpCoC,aAAcpC,EAAE,eAAgB,uEAChCqC,eAAgBrC,EAAE,iBAAkB,2DACpCsC,iBAAkBtC,EAAE,mBAAoB,uEACxCuC,mBAAoBvC,EAAE,qBAAsB,uEAC5CwC,iBAAkBxC,EAAE,mBAAoB,mFACxCyC,mBAAoBzC,EAAE,qBAAsB,uEAC5C0C,cAAe1C,EAAE,gBAAiB,6EAClC2C,YAAa3C,EAAE,cAAe,+FAC9B4C,eAAgB5C,EAAE,iBAAkB,qFAGxC6C,UAAW,CACTjC,KAAMZ,EAAE,uBAAwB,qGAChCa,OAAQ,wBACRK,YAAa,CACX4B,iCAAkC9C,EAAE,mCAAoC,gJACxE+C,2BAA4B/C,EAAE,6BAA8B,wHAC5DgD,yBAA0BhD,EAAE,2BAA4B,4GACxDiD,eAAgBjD,EAAE,iBAAkB,mFACpCkD,oBAAqBlD,EAAE,sBAAuB,6FAKpD2D,EAAAA,EAAAA,WAAU,KACRvD,EAASwD,OAAOC,QAAQnD,GAAiBoD,IAAIC,IAAA,IAAEC,EAAKC,GAAKF,EAAA,OAAAG,EAAAA,EAAAA,GAAA,CAAQC,GAAIH,GAAQC,MAC7ExD,GAAW,IACV,IAgBH,OAAID,GAEA4D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDC,SAClEtE,EAAE,iBAAkB,gFAEvBoE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5CtE,EAAE,qBAAsB,4QAI7BuE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEtE,EAAE,cAAe,4EAGtBoE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBnE,EAAM2D,IAAIG,IACT,MAAMO,EA9CItD,KACxB,IAAIuD,EAAQ,EACRC,EAAU,EASd,OAPAd,OAAOe,OAAOzD,GAAa0D,QAAQC,IACjCjB,OAAOe,OAAOE,GAAUD,QAAQE,IAC9BL,IACIK,GAAYJ,QAIb,CAAED,QAAOC,YAmCsBK,CAAiBd,EAAK/C,aAC9C,OACEqD,EAAAA,EAAAA,MAAA,OAEES,QAASA,IAAMzE,EAAgB0D,GAC/BI,UAAS,0DAAAY,QACK,OAAZ3E,QAAY,IAAZA,OAAY,EAAZA,EAAc6D,MAAOF,EAAKE,GACtB,iDACA,yFACHG,SAAA,EAEHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAY,OAAKhB,EAAKjD,KAAI,UAAAiE,OAAShB,EAAKlD,MAAK,mBAAAkE,OAAkBhB,EAAKlD,MAAK,gBACzEwD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SACtDL,EAAKrD,QAER2D,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,CACpDL,EAAKhD,UAAU,IAAEjB,EAAE,QAAS,oDAKrCoE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDL,EAAKnD,eAERyD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4CAA2CC,SAAA,EACxDC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAC/CE,EAAgBE,QAAQ,IAAEF,EAAgBC,MAAM,IAAEzE,EAAE,cAAe,4CAEtEoE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,MAAAY,OAAQhB,EAAKlD,MAAK,yBAC3BmE,MAAO,CAAEC,MAAM,GAADF,OAAMT,EAAgBE,QAAUF,EAAgBC,MAAS,IAAG,eA/B3ER,EAAKE,gBA4CxBC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SAC3BhE,GACCiE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,UAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAY,OAAK3E,EAAaU,KAAI,UAAAiE,OAAS3E,EAAaS,MAAK,mBAAAkE,OAAkB3E,EAAaS,MAAK,yBACjGwD,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChEhE,EAAaM,QAEhBwD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDhE,EAAaQ,eAEhByD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2CAA0CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZ/D,EAAaW,UAAU,IAAEjB,EAAE,QAAS,4CAEvCoE,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8EAAAY,OAAgF3E,EAAaS,MAAK,cAAAkE,OAAa3E,EAAaS,MAAK,iBAAAkE,OAAgB3E,EAAaS,MAAK,sBAAAkE,OAAqB3E,EAAaS,MAAK,QAAOuD,SAC7NhE,EAAaO,uBAQ1B0D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEtE,EAAE,kBAAmB,+EAGvB4D,OAAOC,QAAQH,GAAsBI,IAAIsB,IAAA,IAAEC,EAAaR,GAASO,EAAA,OAChEb,EAAAA,EAAAA,MAAA,OAAuBF,UAAU,OAAMC,SAAA,EACrCC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2EAA0EC,SAAA,EACtFF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,OAAAY,OACM,UAAhBI,EAA0B,cACV,WAAhBA,EAA2B,SAAW,eAAc,yBAErDR,EAASjE,SAEZwD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,SACnDV,OAAOC,QAAQgB,EAAS3D,aAAa4C,IAAIwB,IAAsC,IAADC,EAAA,IAAnCC,EAAeC,GAAeH,EACxE,MAAMI,EAAqD,QAAxCH,EAAGjF,EAAaY,YAAYmE,UAAY,IAAAE,OAAA,EAArCA,EAAwCC,GAC9D,OACEjB,EAAAA,EAAAA,MAAA,OAAyBF,UAAS,oDAAAY,OAChCS,EACI,iFACA,2EACHpB,SAAA,EACDF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,WAAAY,OACbS,EACI,qCACA,oCACHpB,SACAmB,KAEHrB,EAAAA,EAAAA,KAAA,KAAGC,UAAS,OAAAY,OACVS,EAAgB,iCAAmC,qCAb7CF,SAZRH,YAoChBjB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+FAA8FC,UAC3GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEtE,EAAE,aAAc,kDAEnBoE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5CtE,EAAE,iBAAkB,iP", "sources": ["pages/Admin/RoleManagement.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst RoleManagement = () => {\n  const { t, isRTL } = useLanguage();\n  const [roles, setRoles] = useState([]);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Role definitions with detailed permissions\n  const roleDefinitions = {\n    admin: {\n      name: t('admin', 'مدير النظام'),\n      nameEn: 'Administrator',\n      description: t('adminDesc', 'صلاحيات كاملة لإدارة النظام والمستخدمين'),\n      color: 'gray',\n      icon: 'fas fa-crown',\n      userCount: 2,\n      permissions: {\n        forms: {\n          medicalDiagnosis: true,\n          ptEvaluation: true,\n          reassessment: true,\n          dischargeEvaluation: true,\n          followUpForm: true,\n          patientEducationDoctor: true,\n          progressNotes: true,\n          patientEducationTherapist: true,\n          patientEducationNurse: true,\n          nursingForm: true,\n          clinicalIndicators: true,\n          labResults: true,\n          radiologyResults: true\n        },\n        system: {\n          viewPatients: true,\n          createPatients: true,\n          editPatients: true,\n          deletePatients: true,\n          viewAppointments: true,\n          createAppointments: true,\n          editAppointments: true,\n          deleteAppointments: true,\n          viewAnalytics: true,\n          manageUsers: true,\n          systemSettings: true\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: true\n        }\n      }\n    },\n    doctor: {\n      name: t('doctor', 'طبيب'),\n      nameEn: 'Doctor',\n      description: t('doctorDesc', 'التشخيص الطبي وتقييم العلاج الطبيعي وإعادة التقييم'),\n      color: 'blue',\n      icon: 'fas fa-user-md',\n      userCount: 5,\n      permissions: {\n        forms: {\n          medicalDiagnosis: true,\n          ptEvaluation: true,\n          reassessment: true,\n          dischargeEvaluation: true,\n          followUpForm: true,\n          patientEducationDoctor: true,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: true,\n          labResults: true,\n          radiologyResults: true\n        },\n        system: {\n          viewPatients: true,\n          createPatients: true,\n          editPatients: true,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: true,\n          editAppointments: true,\n          deleteAppointments: false,\n          viewAnalytics: true,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: true\n        }\n      }\n    },\n    therapist: {\n      name: t('therapist', 'أخصائي العلاج الطبيعي'),\n      nameEn: 'Physical Therapist',\n      description: t('therapistDesc', 'ملاحظات تقدم الحالة ونموذج تثقيف المريض'),\n      color: 'green',\n      icon: 'fas fa-hands-helping',\n      userCount: 8,\n      permissions: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: true,\n          patientEducationTherapist: true,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: true,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: true,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: false\n        }\n      }\n    },\n    nurse: {\n      name: t('nurse', 'ممرض/ممرضة'),\n      nameEn: 'Nurse',\n      description: t('nurseDesc', 'نموذج تثقيف المريض ونموذج التمريض التابع للمركز'),\n      color: 'pink',\n      icon: 'fas fa-user-nurse',\n      userCount: 12,\n      permissions: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: true,\n          nursingForm: true,\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      }\n    },\n    external_lab: {\n      name: t('externalLab', 'مختبر خارجي'),\n      nameEn: 'External Laboratory',\n      description: t('externalLabDesc', 'إدخال نتائج المختبر للمرضى المحولين'),\n      color: 'purple',\n      icon: 'fas fa-flask',\n      userCount: 3,\n      permissions: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: false,\n          labResults: true,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: false,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      }\n    },\n    external_radiology: {\n      name: t('externalRadiology', 'مركز أشعة خارجي'),\n      nameEn: 'External Radiology',\n      description: t('externalRadiologyDesc', 'إدخال نتائج الأشعة للمرضى المحولين'),\n      color: 'indigo',\n      icon: 'fas fa-x-ray',\n      userCount: 2,\n      permissions: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: true\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: false,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      }\n    },\n    receptionist: {\n      name: t('receptionist', 'موظف استقبال'),\n      nameEn: 'Receptionist',\n      description: t('receptionistDesc', 'تسجيل المرضى وحجز المواعيد'),\n      color: 'yellow',\n      icon: 'fas fa-concierge-bell',\n      userCount: 4,\n      permissions: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: true,\n          editPatients: true,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: true,\n          editAppointments: true,\n          deleteAppointments: true,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      }\n    },\n    manager: {\n      name: t('manager', 'مدير'),\n      nameEn: 'Manager',\n      description: t('managerDesc', 'عرض التحليلات والتقارير وإدارة المستخدمين'),\n      color: 'red',\n      icon: 'fas fa-user-tie',\n      userCount: 1,\n      permissions: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: true,\n          manageUsers: true,\n          systemSettings: true\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: true\n        }\n      }\n    }\n  };\n\n  // Permission categories with Arabic translations\n  const permissionCategories = {\n    forms: {\n      name: t('formsPermissions', 'صلاحيات النماذج'),\n      nameEn: 'Forms Permissions',\n      permissions: {\n        medicalDiagnosis: t('medicalDiagnosis', 'التشخيص الطبي'),\n        ptEvaluation: t('ptEvaluation', 'تقييم العلاج الطبيعي'),\n        reassessment: t('reassessment', 'إعادة التقييم'),\n        dischargeEvaluation: t('dischargeEvaluation', 'تقييم الخروج'),\n        followUpForm: t('followUpForm', 'نموذج المتابعة'),\n        patientEducationDoctor: t('patientEducationDoctor', 'نموذج تثقيف المريض (طبيب)'),\n        progressNotes: t('progressNotes', 'ملاحظات تقدم الحالة'),\n        patientEducationTherapist: t('patientEducationTherapist', 'نموذج تثقيف المريض (أخصائي)'),\n        patientEducationNurse: t('patientEducationNurse', 'نموذج تثقيف المريض (تمريض)'),\n        nursingForm: t('nursingForm', 'نموذج التمريض التابع للمركز'),\n        clinicalIndicators: t('clinicalIndicators', 'المؤشرات السريرية'),\n        labResults: t('labResults', 'نتائج المختبر'),\n        radiologyResults: t('radiologyResults', 'نتائج الأشعة')\n      }\n    },\n    system: {\n      name: t('systemPermissions', 'صلاحيات النظام'),\n      nameEn: 'System Permissions',\n      permissions: {\n        viewPatients: t('viewPatients', 'عرض المرضى'),\n        createPatients: t('createPatients', 'إنشاء مرضى'),\n        editPatients: t('editPatients', 'تعديل المرضى'),\n        deletePatients: t('deletePatients', 'حذف المرضى'),\n        viewAppointments: t('viewAppointments', 'عرض المواعيد'),\n        createAppointments: t('createAppointments', 'إنشاء مواعيد'),\n        editAppointments: t('editAppointments', 'تعديل المواعيد'),\n        deleteAppointments: t('deleteAppointments', 'حذف المواعيد'),\n        viewAnalytics: t('viewAnalytics', 'عرض التحليلات'),\n        manageUsers: t('manageUsers', 'إدارة المستخدمين'),\n        systemSettings: t('systemSettings', 'إعدادات النظام')\n      }\n    },\n    analytics: {\n      name: t('analyticsPermissions', 'صلاحيات التحليلات'),\n      nameEn: 'Analytics Permissions',\n      permissions: {\n        functionalIndependenceComparison: t('functionalIndependenceComparison', 'درجة الاستقلالية الوظيفية'),\n        treatmentPlanEffectiveness: t('treatmentPlanEffectiveness', 'فعالية الخطة العلاجية'),\n        clinicalProgressTracking: t('clinicalProgressTracking', 'تتبع التقدم السريري'),\n        outcomeMetrics: t('outcomeMetrics', 'مقاييس النتائج'),\n        complianceReporting: t('complianceReporting', 'تقارير الامتثال')\n      }\n    }\n  };\n\n  useEffect(() => {\n    setRoles(Object.entries(roleDefinitions).map(([key, role]) => ({ id: key, ...role })));\n    setLoading(false);\n  }, []);\n\n  const countPermissions = (permissions) => {\n    let total = 0;\n    let granted = 0;\n    \n    Object.values(permissions).forEach(category => {\n      Object.values(category).forEach(permission => {\n        total++;\n        if (permission) granted++;\n      });\n    });\n    \n    return { total, granted };\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n          {t('roleManagement', 'إدارة الأدوار')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          {t('roleManagementDesc', 'عرض وإدارة أدوار المستخدمين وصلاحياتهم في النظام')}\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Roles List */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('systemRoles', 'أدوار النظام')}\n              </h3>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-3\">\n                {roles.map(role => {\n                  const permissionStats = countPermissions(role.permissions);\n                  return (\n                    <div\n                      key={role.id}\n                      onClick={() => setSelectedRole(role)}\n                      className={`p-4 rounded-lg border cursor-pointer transition-colors ${\n                        selectedRole?.id === role.id\n                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                          : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\n                      }`}\n                    >\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <div className=\"flex items-center\">\n                          <i className={`${role.icon} text-${role.color}-600 dark:text-${role.color}-400 mr-3`}></i>\n                          <div>\n                            <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                              {role.name}\n                            </h4>\n                            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n                              {role.userCount} {t('users', 'مستخدم')}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                        {role.description}\n                      </p>\n                      <div className=\"flex items-center justify-between text-xs\">\n                        <span className=\"text-gray-500 dark:text-gray-400\">\n                          {permissionStats.granted}/{permissionStats.total} {t('permissions', 'صلاحية')}\n                        </span>\n                        <div className=\"w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-1\">\n                          <div \n                            className={`bg-${role.color}-600 h-1 rounded-full`}\n                            style={{ width: `${(permissionStats.granted / permissionStats.total) * 100}%` }}\n                          ></div>\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Role Details */}\n        <div className=\"lg:col-span-2\">\n          {selectedRole ? (\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center\">\n                    <i className={`${selectedRole.icon} text-${selectedRole.color}-600 dark:text-${selectedRole.color}-400 text-2xl mr-4`}></i>\n                    <div>\n                      <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                        {selectedRole.name}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {selectedRole.description}\n                      </p>\n                      <div className=\"flex items-center mt-2 space-x-4\">\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          <i className=\"fas fa-users mr-1\"></i>\n                          {selectedRole.userCount} {t('users', 'مستخدم')}\n                        </span>\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${selectedRole.color}-100 text-${selectedRole.color}-800 dark:bg-${selectedRole.color}-900/30 dark:text-${selectedRole.color}-200`}>\n                          {selectedRole.nameEn}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"p-6\">\n                <h4 className=\"text-lg font-medium text-gray-900 dark:text-white mb-6\">\n                  {t('rolePermissions', 'صلاحيات الدور')}\n                </h4>\n                \n                {Object.entries(permissionCategories).map(([categoryKey, category]) => (\n                  <div key={categoryKey} className=\"mb-8\">\n                    <h5 className=\"text-md font-medium text-gray-900 dark:text-white mb-4 flex items-center\">\n                      <i className={`fas ${\n                        categoryKey === 'forms' ? 'fa-file-alt' :\n                        categoryKey === 'system' ? 'fa-cog' : 'fa-chart-bar'\n                      } mr-2 text-gray-500`}></i>\n                      {category.name}\n                    </h5>\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n                      {Object.entries(category.permissions).map(([permissionKey, permissionName]) => {\n                        const hasPermission = selectedRole.permissions[categoryKey]?.[permissionKey];\n                        return (\n                          <div key={permissionKey} className={`flex items-center justify-between p-3 rounded-lg ${\n                            hasPermission \n                              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' \n                              : 'bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600'\n                          }`}>\n                            <span className={`text-sm ${\n                              hasPermission \n                                ? 'text-green-900 dark:text-green-200' \n                                : 'text-gray-600 dark:text-gray-400'\n                            }`}>\n                              {permissionName}\n                            </span>\n                            <i className={`fas ${\n                              hasPermission ? 'fa-check-circle text-green-600' : 'fa-times-circle text-gray-400'\n                            }`}></i>\n                          </div>\n                        );\n                      })}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          ) : (\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-12\">\n              <div className=\"text-center\">\n                <i className=\"fas fa-user-tag text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('selectRole', 'اختر دور')}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {t('selectRoleDesc', 'اختر دوراً من القائمة لعرض تفاصيل صلاحياته')}\n                </p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RoleManagement;\n"], "names": ["RoleManagement", "t", "isRTL", "useLanguage", "roles", "setRoles", "useState", "selectedR<PERSON>", "setSelectedRole", "loading", "setLoading", "roleDefinitions", "admin", "name", "nameEn", "description", "color", "icon", "userCount", "permissions", "forms", "medicalDiagnosis", "ptEvaluation", "reassessment", "dischargeEvaluation", "followUpForm", "patientEducationDoctor", "progressNotes", "patientEducationTherapist", "patientEducationNurse", "nursingForm", "clinicalIndicators", "labResults", "radiologyResults", "system", "viewPatients", "createPatients", "editPatients", "deletePatients", "viewAppointments", "createAppointments", "editAppointments", "deleteAppointments", "viewAnalytics", "manageUsers", "systemSettings", "analytics", "functionalIndependenceComparison", "treatmentPlanEffectiveness", "clinicalProgressTracking", "outcomeMetrics", "complianceReporting", "doctor", "therapist", "nurse", "external_lab", "external_radiology", "receptionist", "manager", "permissionCategories", "useEffect", "Object", "entries", "map", "_ref", "key", "role", "_objectSpread", "id", "_jsx", "className", "children", "_jsxs", "permissionStats", "total", "granted", "values", "for<PERSON>ach", "category", "permission", "countPermissions", "onClick", "concat", "style", "width", "_ref2", "categoryKey", "_ref3", "_selectedRole$permiss", "<PERSON><PERSON><PERSON>", "permissionName", "hasPermission"], "sourceRoot": ""}