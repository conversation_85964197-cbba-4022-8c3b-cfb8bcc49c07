{"version": 3, "file": "static/js/7862.978ba1c5.chunk.js", "mappings": "yLAGA,MAsSA,EAtSyBA,IAA2B,IAA1B,UAAEC,EAAY,MAAMD,EAC5C,MAAM,EAAEE,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,QAC9CC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,QAG1CG,IAAoBH,EAAAA,EAAAA,UAAS,CAClC,CACEI,GAAI,EACJC,KAAM,aACNC,KAAM,QACNC,KAAM,mBACNC,UAAW,sBACXC,QAAS,yFACTC,UAAW,0BACXC,SAAU,GACVC,OAAQ,YACRC,MAAO,kFACPC,UAAW,CACT,gCACA,kCACA,wCAEFC,MAAO,CACL,mCACA,2BACA,0BAEFC,SAAU,GACVC,YAAa,cAEf,CACEb,GAAI,EACJC,KAAM,aACNC,KAAM,QACNC,KAAM,uBACNC,UAAW,wBACXC,QAAS,yFACTC,UAAW,sBACXC,SAAU,GACVC,OAAQ,YACRC,MAAO,oFACPC,UAAW,CACT,qCACA,mCACA,oCAEFC,MAAO,CACL,6BACA,2CACA,8BAEFC,SAAU,GACVC,YAAa,cAEf,CACEb,GAAI,EACJC,KAAM,aACNC,KAAM,QACNC,KAAM,iBACNC,UAAW,sBACXC,QAAS,2GACTC,UAAW,8BACXC,SAAU,GACVC,OAAQ,YACRC,MAAO,oFACPC,UAAW,CACT,oCACA,sCACA,kCAEFC,MAAO,CACL,+BACA,iCACA,uCAEFC,SAAU,GACVC,YAAa,cAEf,CACEb,GAAI,EACJC,KAAM,aACNC,KAAM,QACNC,KAAM,gBACNC,UAAW,yBACXC,QAAS,oBACTC,UAAW,oBACXC,SAAU,GACVC,OAAQ,YACRC,MAAO,gEACPC,UAAW,CACT,gCACA,sCACA,wCAEFC,MAAO,CACL,6BACA,0BACA,8BAEFC,SAAU,GACVC,YAAa,gBAIXC,EAAkBf,EAAiBgB,OAAOC,IAC9C,MAAMC,EAAiC,QAAnBvB,GACE,SAAnBA,GAA6B,IAAIwB,KAAKF,EAAQf,OAAS,IAAIiB,KAAKA,KAAKC,MAAQ,SAC1D,UAAnBzB,GAA8B,IAAIwB,KAAKF,EAAQf,OAAS,IAAIiB,KAAKA,KAAKC,MAAQ,QAE3EC,EAA6B,QAAjBvB,GAA0BmB,EAAQb,KAAKkB,cAAcC,SAASzB,EAAawB,eAE7F,OAAOJ,GAAeG,IAGlBG,EAAkBf,IACtB,OAAQA,GACN,IAAK,YACH,MAAO,uEACT,IAAK,cACH,MAAO,mEACT,IAAK,YACH,MAAO,+DACT,IAAK,YACH,MAAO,2EACT,QACE,MAAO,qEAWb,OACEgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EAEtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sFAAqFC,SAAA,EAClGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACZlC,EAAE,mBAAoB,yBAGzBiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,UACEI,MAAOlC,EACPmC,SAAWC,GAAMnC,EAAkBmC,EAAEC,OAAOH,OAC5CH,UAAU,2JAA0JC,SAAA,EAEpKC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,MAAKF,SAAEnC,EAAE,UAAW,eAClCoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,OAAMF,SAAEnC,EAAE,WAAY,gBACpCoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,QAAOF,SAAEnC,EAAE,YAAa,oBAGxCiC,EAAAA,EAAAA,MAAA,UACEI,MAAO/B,EACPgC,SAAWC,GAAMhC,EAAgBgC,EAAEC,OAAOH,OAC1CH,UAAU,2JAA0JC,SAAA,EAEpKC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,MAAKF,SAAEnC,EAAE,WAAY,gBACnCoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,WAAUF,SAAEnC,EAAE,kBAAmB,uBAC/CoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,eAAcF,SAAEnC,EAAE,sBAAuB,2BACvDoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,SAAQF,SAAEnC,EAAE,gBAAiB,qBAC3CoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,QAAOF,SAAEnC,EAAE,eAAgB,+BAOjDoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACjBZ,EAAgBkB,OAAS,GACxBL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBZ,EAAgBmB,IAAKjB,IACpBQ,SAAAA,EAAAA,MAAA,OAAsBC,UAAU,+FAA8FC,SAAA,EAE5HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oEAAmEC,SAAA,EAChFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wFAAuFC,UACpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDAGjBD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEV,EAAQb,QAEXqB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,CACpDV,EAAQf,KAAK,OAAKe,EAAQd,KAAK,WAAIc,EAAQT,SAAS,IAAEhB,EAAE,UAAW,qBAK1EiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAS,OAAgDX,EAAeP,EAAQR,SAAUkB,SAC7FnC,EAAEyB,EAAQR,OAAQQ,EAAQR,WAE7BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,CAC/DV,EAAQJ,SAAS,KAAGrB,EAAE,WAAY,gBAErCoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,oBAAAS,QA7EPtB,EA6E8CI,EAAQJ,SA5E1EA,GAAY,GAAW,eACvBA,GAAY,GAAW,cACvBA,GAAY,GAAW,gBACpB,eA0EeuB,MAAO,CAAEC,MAAM,GAADF,OAAKlB,EAAQJ,SAAQ,oBAQ7CY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEnC,EAAE,sBAAuB,0BAE5BiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAASnC,EAAE,UAAW,WAAW,OAAU,IAAEC,EAAQwB,EAAQX,QAAUW,EAAQV,cAClFkB,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAASnC,EAAE,YAAa,aAAa,OAAU,IAAEyB,EAAQZ,oBAIhEoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEnC,EAAE,YAAa,gBAElBoC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qDAAoDC,SAC/DV,EAAQN,UAAUuB,IAAI,CAACI,EAAUC,KAChCd,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,oBAAmBC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACZY,IAFMC,aAUjBd,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEnC,EAAE,iBAAkB,sBAEvBoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCV,EAAQL,MAAMsB,IAAI,CAACM,EAAMD,KACxBX,EAAAA,EAAAA,KAAA,QAAkBF,UAAU,iGAAgGC,SACzHa,GADQD,SAQhBtB,EAAQP,QACPe,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEnC,EAAE,eAAgB,oBAErBoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sFAAqFC,SAC/FV,EAAQP,WAMdO,EAAQH,cACPW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kEAAiEC,SAAA,EAC9EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8BACZlC,EAAE,cAAe,gBAAgB,KAAGyB,EAAQH,iBA9FzCG,EAAQhB,IAhDJY,WAqJlBY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEnC,EAAE,qBAAsB,2BAE3BoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CnC,EAAE,yBAA0B,oE,cC3R3C,MA2eA,EA3emBiD,KACjB,MAAM,EAAEjD,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdgD,EAAWC,IAAgB9C,EAAAA,EAAAA,UAAS,UACpC+C,EAAYC,IAAiBhD,EAAAA,EAAAA,UAAS,KACtCiD,EAAcC,IAAmBlD,EAAAA,EAAAA,UAAS,QAG1CmD,IAAkBnD,EAAAA,EAAAA,UAAS,CAChC,CACEI,GAAI,EACJgD,YAAa,yFACbC,cAAe,0BACfC,UAAW,SACXC,SAAU,gBACVC,UAAW,aACXC,QAAS,aACT7C,OAAQ,SACRI,SAAU,GACVR,UAAW,sBACXkD,kBAAmB,GACnBC,cAAe,GACf5C,MAAO,CAAC,6BAA8B,+BAAgC,iCAExE,CACEX,GAAI,EACJgD,YAAa,yFACbC,cAAe,sBACfC,UAAW,iBACXC,SAAU,eACVC,UAAW,aACXC,QAAS,aACT7C,OAAQ,SACRI,SAAU,GACVR,UAAW,wBACXkD,kBAAmB,GACnBC,cAAe,GACf5C,MAAO,CAAC,6BAA8B,kBAAmB,4BAE3D,CACEX,GAAI,EACJgD,YAAa,2GACbC,cAAe,8BACfC,UAAW,gBACXC,SAAU,gBACVC,UAAW,aACXC,QAAS,aACT7C,OAAQ,SACRI,SAAU,GACVR,UAAW,sBACXkD,kBAAmB,EACnBC,cAAe,GACf5C,MAAO,CAAC,wBAAyB,oBAAqB,0BAExD,CACEX,GAAI,EACJgD,YAAa,yFACbC,cAAe,sBACfC,UAAW,0BACXC,SAAU,aACVC,UAAW,aACXC,QAAS,aACT7C,OAAQ,YACRI,SAAU,IACVR,UAAW,oBACXkD,kBAAmB,GACnBC,cAAe,GACf5C,MAAO,CAAC,wBAAyB,gBAAiB,2BAK/C6C,IAAoB5D,EAAAA,EAAAA,UAAS,CAClC,CACEI,GAAI,EACJgD,YAAa,yFACbC,cAAe,0BACfQ,cAAe,8BACf5C,YAAa,mBACb6C,UAAW,eACXnD,SAAU,aACVH,UAAW,sBACXI,OAAQ,WAEV,CACER,GAAI,EACJgD,YAAa,yFACbC,cAAe,sBACfQ,cAAe,mBACf5C,YAAa,mBACb6C,UAAW,qBACXnD,SAAU,aACVH,UAAW,wBACXI,OAAQ,WAEV,CACER,GAAI,EACJgD,YAAa,2GACbC,cAAe,8BACfQ,cAAe,uBACf5C,YAAa,mBACb6C,UAAW,SACXnD,SAAU,aACVH,UAAW,sBACXI,OAAQ,aAINmD,EAAkB,CACtBC,OAAQrE,EAAE,SAAU,4BACpBsE,eAAgBtE,EAAE,gBAAiB,kBACnCuE,cAAevE,EAAE,eAAgB,iBACjCwE,wBAAyBxE,EAAE,yBAA0B,2BACrDyE,aAAczE,EAAE,cAAe,iBAG3B0E,EAAiB,CACrBC,cAAe,CAAEC,MAAO5E,EAAE,gBAAiB,iBAAkB6E,MAAO,6BACpEC,aAAc,CAAEF,MAAO5E,EAAE,cAAe,gBAAiB6E,MAAO,+BAChEE,cAAe,CAAEH,MAAO5E,EAAE,gBAAiB,iBAAkB6E,MAAO,iCACpEG,WAAY,CAAEJ,MAAO5E,EAAE,aAAc,cAAe6E,MAAO,iCAC3DI,QAAS,CAAEL,MAAO5E,EAAE,UAAW,WAAY6E,MAAO,8BAG9CK,EAAe,CACnBC,OAAQ,CAAEP,MAAO5E,EAAE,SAAU,UAAW6E,MAAO,+BAC/CO,UAAW,CAAER,MAAO5E,EAAE,YAAa,aAAc6E,MAAO,6BACxDQ,OAAQ,CAAET,MAAO5E,EAAE,SAAU,UAAW6E,MAAO,iCAC/CS,UAAW,CAAEV,MAAO5E,EAAE,YAAa,aAAc6E,MAAO,4BAGpDU,EAAgB/B,EAAehC,OAAOgE,IAC1C,MAAMC,EAAgBD,EAAK/B,YAAY3B,cAAcC,SAASqB,EAAWtB,gBACpD0D,EAAK9B,cAAc5B,cAAcC,SAASqB,EAAWtB,eACpE4D,EAAiC,QAAjBpC,GAA0BkC,EAAKvE,SAAWqC,EAChE,OAAOmC,GAAiBC,IAUpBC,EAAO,CACX,CAAElF,GAAI,QAASmE,MAAO5E,EAAE,iBAAkB,mBAAoB4F,KAAM,yBACpE,CAAEnF,GAAI,SAAUmE,MAAO5E,EAAE,mBAAoB,qBAAsB4F,KAAM,oBACzE,CAAEnF,GAAI,UAAWmE,MAAO5E,EAAE,mBAAoB,qBAAsB4F,KAAM,mBAG5E,OACE3D,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAS,OAAS1C,EAAQ,cAAgB,gBAAiBkC,SAAA,EAE9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DnC,EAAE,aAAc,iBAEnBoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CnC,EAAE,iBAAkB,qDAGzBoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oGAAmGC,SAAA,EACnHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,mBAAoB,+BAM7BiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAAEnC,EAAE,aAAc,kBACrFoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAAEqB,EAAef,kBAKtFL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAAEnC,EAAE,cAAe,mBACtFoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAC5DqB,EAAehC,OAAOqE,GAAkB,WAAbA,EAAE5E,QAAqBwB,kBAM3DL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0EAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAAEnC,EAAE,mBAAoB,yBAC3FoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAAC,gBAKtEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAAEnC,EAAE,kBAAmB,uBAC1FiC,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mDAAkDC,SAAA,CAC5D2D,KAAKC,MAAMvC,EAAewC,OAAO,CAACC,EAAKJ,IAAMI,EAAMJ,EAAExE,SAAU,GAAKmC,EAAef,QAAQ,mBAQtGL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,SACnCwD,EAAKjD,IAAKwD,IACTjE,EAAAA,EAAAA,MAAA,UAEEkE,QAASA,IAAMhD,EAAa+C,EAAIzF,IAChCyB,UAAS,0FAAAS,OACPO,IAAcgD,EAAIzF,GACd,mDACA,0HACH0B,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAWgE,EAAIN,QAClBxD,EAAAA,EAAAA,KAAA,QAAAD,SAAO+D,EAAItB,UATNsB,EAAIzF,SAgBF,UAAdyC,IACCd,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mGAAkGC,UAC/GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,SAAU,aAEfiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACExB,KAAK,OACLyB,MAAOe,EACPd,SAAWC,GAAMc,EAAcd,EAAEC,OAAOH,OACxC+D,YAAapG,EAAE,mBAAoB,wBACnCkC,UAAU,2IAEZE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DAIjBD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,SAAU,aAEfiC,EAAAA,EAAAA,MAAA,UACEI,MAAOiB,EACPhB,SAAWC,GAAMgB,EAAgBhB,EAAEC,OAAOH,OAC1CH,UAAU,kIAAiIC,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,MAAKF,SAAEnC,EAAE,cAAe,mBACtCoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,SAAQF,SAAEnC,EAAE,SAAU,aACpCoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,YAAWF,SAAEnC,EAAE,YAAa,gBAC1CoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,SAAQF,SAAEnC,EAAE,SAAU,aACpCoC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,YAAWF,SAAEnC,EAAE,YAAa,sBAI9CoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uJAAsJC,SAAA,EACtKC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBACZlC,EAAE,kBAAmB,8BAQjB,UAAdkD,IACCjB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAA2CC,SACvDoD,EAAc7C,IAAK8C,IAClBvD,SAAAA,EAAAA,MAAA,OAAmBC,UAAU,6DAA4DC,SAAA,EACvFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChElC,EAAQuF,EAAK/B,YAAc+B,EAAK9B,iBAEnCtB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAS,OAAgDuC,EAAaM,EAAKvE,QAAQ4D,OAAQ1C,SAC9F+C,EAAaM,EAAKvE,QAAQ2D,YAI/B3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CAAEnC,EAAE,YAAa,aAAa,QACxFoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,oDAAmDC,SAChEiC,EAAgBoB,EAAK7B,iBAI1B1B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CAAEnC,EAAE,WAAY,aAAa,QACvFoC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAS,OAAgD+B,EAAec,EAAK5B,UAAUiB,OAAQ1C,SAClGuC,EAAec,EAAK5B,UAAUgB,YAInC3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CAAEnC,EAAE,YAAa,aAAa,QACxFoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,oDAAmDC,SAAEqD,EAAK3E,gBAG5EoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CAAEnC,EAAE,WAAY,YAAY,QACtFiC,EAAAA,EAAAA,MAAA,QAAMC,UAAU,oDAAmDC,SAAA,CAChEqD,EAAKzB,kBAAkB,IAAEyB,EAAKxB,wBAMrC/B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAEnC,EAAE,WAAY,eACtFiC,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CAAEqD,EAAKnE,SAAS,WAE5Ee,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,gDAAAS,QA9MHtB,EA8MsEmE,EAAKnE,SA7M/FA,GAAY,GAAW,eACvBA,GAAY,GAAW,cACvBA,GAAY,GAAW,gBACpB,eA2MWuB,MAAO,CAAEC,MAAM,GAADF,OAAK6C,EAAKnE,SAAQ,cAMtCY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DC,SAAA,CAAEnC,EAAE,QAAS,SAAS,QAC/FiC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,YAAWC,SAAA,CACtBqD,EAAKpE,MAAMiF,MAAM,EAAG,GAAG3D,IAAI,CAACM,EAAMD,KACjCd,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,6DAA4DC,SAAA,EACpFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACZc,IAFMD,IAKVyC,EAAKpE,MAAMqB,OAAS,IACnBR,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2CAA0CC,SAAA,CAAC,IACrDqD,EAAKpE,MAAMqB,OAAS,EAAE,IAAEzC,EAAE,YAAa,wBAOjDiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAACkE,EAAAA,GAAI,CACHC,GAAE,eAAA5D,OAAiB6C,EAAK/E,IACxByB,UAAU,6GAA4GC,SAErHnC,EAAE,cAAe,mBAEpBoC,EAAAA,EAAAA,KAAA,UAAQF,UAAU,wJAAuJC,UACvKC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBA/ETsD,EAAK/E,IAhKDY,UAsPQ,IAAzBkE,EAAc9C,SACbR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0EACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEnC,EAAE,mBAAoB,+BAEzBoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CnC,EAAE,kBAAmB,2DAQjB,WAAdkD,IACCjB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GnC,EAAE,UAAW,cAEhBoC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GnC,EAAE,gBAAiB,qBAEtBoC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GnC,EAAE,cAAe,mBAEpBoC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GnC,EAAE,YAAa,gBAElBoC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GnC,EAAE,YAAa,gBAElBoC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GnC,EAAE,UAAW,mBAIpBoC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvF8B,EAAiBvB,IAAK8D,IACrBvE,EAAAA,EAAAA,MAAA,MAAuBC,UAAU,0CAAyCC,SAAA,EACxEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DlC,EAAQuG,EAAU/C,YAAc+C,EAAU9C,mBAG/CzB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SAAEqE,EAAUtC,iBAClE9B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAEqE,EAAUxF,eAEvEiB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnD,IAAIR,KAAK6E,EAAUlF,aAAamF,wBAEnCrE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtD,IAAIR,KAAK6E,EAAUlF,aAAaoF,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,kBAGvFxE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9EqE,EAAUrC,aAEb/B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9EqE,EAAU3F,aAEbuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,UAC7DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UAAQF,UAAU,gFAA+EC,UAC/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kBAEfE,EAAAA,EAAAA,KAAA,UAAQF,UAAU,oFAAmFC,UACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mBAEfE,EAAAA,EAAAA,KAAA,UAAQF,UAAU,wFAAuFC,UACvGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BAjCZsE,EAAU/F,YA2CE,IAA5BwD,EAAiBxB,SAChBR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qEACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEnC,EAAE,qBAAsB,2BAE3BoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CnC,EAAE,uBAAwB,gEAQtB,YAAdkD,IACCd,EAAAA,EAAAA,KAACyE,EAAgB,O", "sources": ["components/Treatments/TreatmentHistory.jsx", "pages/Treatments/Treatments.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst TreatmentHistory = ({ patientId = null }) => {\n  const { t, isRTL } = useLanguage();\n  const [selectedPeriod, setSelectedPeriod] = useState('all');\n  const [selectedType, setSelectedType] = useState('all');\n\n  // Mock treatment history data\n  const [treatmentHistory] = useState([\n    {\n      id: 1,\n      date: '2024-01-20',\n      time: '10:00',\n      type: 'Physical Therapy',\n      therapist: 'Dr. <PERSON>',\n      patient: 'أحمد محمد الأحمد',\n      patientEn: '<PERSON>',\n      duration: 60,\n      status: 'completed',\n      notes: 'Pat<PERSON> showed good progress in motor skills. Continued with balance exercises.',\n      exercises: [\n        'Balance training - 15 minutes',\n        'Strength exercises - 20 minutes',\n        'Coordination activities - 25 minutes'\n      ],\n      goals: [\n        'Improve balance and coordination',\n        'Increase muscle strength',\n        'Enhance motor planning'\n      ],\n      progress: 75,\n      nextSession: '2024-01-22'\n    },\n    {\n      id: 2,\n      date: '2024-01-18',\n      time: '14:30',\n      type: 'Occupational Therapy',\n      therapist: 'Dr. <PERSON>',\n      patient: 'فاطمة علي السالم',\n      patientEn: 'Fatima Ali Al-Salem',\n      duration: 45,\n      status: 'completed',\n      notes: 'Focused on fine motor skills and daily living activities. Patient responded well.',\n      exercises: [\n        'Fine motor activities - 20 minutes',\n        'Daily living skills - 15 minutes',\n        'Sensory integration - 10 minutes'\n      ],\n      goals: [\n        'Improve fine motor control',\n        'Develop independence in daily activities',\n        'Enhance sensory processing'\n      ],\n      progress: 60,\n      nextSession: '2024-01-21'\n    },\n    {\n      id: 3,\n      date: '2024-01-16',\n      time: '09:30',\n      type: 'Speech Therapy',\n      therapist: 'Dr. Fatima Al-Zahra',\n      patient: 'محمد عبدالله الخالد',\n      patientEn: 'Mohammed Abdullah Al-Khalid',\n      duration: 30,\n      status: 'completed',\n      notes: 'Communication skills session. Patient made good progress with verbal expressions.',\n      exercises: [\n        'Verbal communication - 15 minutes',\n        'Language comprehension - 10 minutes',\n        'Social interaction - 5 minutes'\n      ],\n      goals: [\n        'Improve verbal communication',\n        'Enhance language understanding',\n        'Develop social communication skills'\n      ],\n      progress: 45,\n      nextSession: '2024-01-19'\n    },\n    {\n      id: 4,\n      date: '2024-01-15',\n      time: '11:00',\n      type: 'Group Therapy',\n      therapist: 'Dr. Mohammed Al-Khalid',\n      patient: 'Multiple Patients',\n      patientEn: 'Multiple Patients',\n      duration: 90,\n      status: 'completed',\n      notes: 'Group session focusing on social skills and peer interaction.',\n      exercises: [\n        'Group activities - 30 minutes',\n        'Social skills practice - 30 minutes',\n        'Collaborative exercises - 30 minutes'\n      ],\n      goals: [\n        'Improve social interaction',\n        'Develop teamwork skills',\n        'Enhance peer communication'\n      ],\n      progress: 70,\n      nextSession: '2024-01-22'\n    }\n  ]);\n\n  const filteredHistory = treatmentHistory.filter(session => {\n    const periodMatch = selectedPeriod === 'all' || \n      (selectedPeriod === 'week' && new Date(session.date) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) ||\n      (selectedPeriod === 'month' && new Date(session.date) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000));\n    \n    const typeMatch = selectedType === 'all' || session.type.toLowerCase().includes(selectedType.toLowerCase());\n    \n    return periodMatch && typeMatch;\n  });\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'in_progress':\n        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400';\n      case 'cancelled':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      case 'scheduled':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const getProgressColor = (progress) => {\n    if (progress >= 80) return 'bg-green-500';\n    if (progress >= 60) return 'bg-blue-500';\n    if (progress >= 40) return 'bg-yellow-500';\n    return 'bg-red-500';\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            <i className=\"fas fa-history text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('treatmentHistory', 'Treatment History')}\n          </h2>\n          \n          <div className=\"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4\">\n            <select\n              value={selectedPeriod}\n              onChange={(e) => setSelectedPeriod(e.target.value)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"all\">{t('allTime', 'All Time')}</option>\n              <option value=\"week\">{t('lastWeek', 'Last Week')}</option>\n              <option value=\"month\">{t('lastMonth', 'Last Month')}</option>\n            </select>\n            \n            <select\n              value={selectedType}\n              onChange={(e) => setSelectedType(e.target.value)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"all\">{t('allTypes', 'All Types')}</option>\n              <option value=\"physical\">{t('physicalTherapy', 'Physical Therapy')}</option>\n              <option value=\"occupational\">{t('occupationalTherapy', 'Occupational Therapy')}</option>\n              <option value=\"speech\">{t('speechTherapy', 'Speech Therapy')}</option>\n              <option value=\"group\">{t('groupTherapy', 'Group Therapy')}</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Treatment History List */}\n      <div className=\"p-6\">\n        {filteredHistory.length > 0 ? (\n          <div className=\"space-y-6\">\n            {filteredHistory.map((session) => (\n              <div key={session.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                {/* Session Header */}\n                <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex-shrink-0\">\n                      <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\n                        <i className=\"fas fa-user-md text-blue-600 dark:text-blue-400\"></i>\n                      </div>\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                        {session.type}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {session.date} at {session.time} • {session.duration} {t('minutes', 'minutes')}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-3 mt-3 md:mt-0\">\n                    <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(session.status)}`}>\n                      {t(session.status, session.status)}\n                    </span>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {session.progress}% {t('progress', 'Progress')}\n                      </div>\n                      <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1\">\n                        <div \n                          className={`h-2 rounded-full ${getProgressColor(session.progress)}`}\n                          style={{ width: `${session.progress}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Session Details */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n                      {t('patientAndTherapist', 'Patient & Therapist')}\n                    </h4>\n                    <div className=\"space-y-1 text-sm text-gray-600 dark:text-gray-400\">\n                      <p><strong>{t('patient', 'Patient')}:</strong> {isRTL ? session.patient : session.patientEn}</p>\n                      <p><strong>{t('therapist', 'Therapist')}:</strong> {session.therapist}</p>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n                      {t('exercises', 'Exercises')}\n                    </h4>\n                    <ul className=\"space-y-1 text-sm text-gray-600 dark:text-gray-400\">\n                      {session.exercises.map((exercise, index) => (\n                        <li key={index} className=\"flex items-center\">\n                          <i className=\"fas fa-check-circle text-green-500 mr-2 text-xs\"></i>\n                          {exercise}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n\n                {/* Goals */}\n                <div className=\"mt-4\">\n                  <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n                    {t('treatmentGoals', 'Treatment Goals')}\n                  </h4>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {session.goals.map((goal, index) => (\n                      <span key={index} className=\"px-3 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-sm rounded-full\">\n                        {goal}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Notes */}\n                {session.notes && (\n                  <div className=\"mt-4\">\n                    <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n                      {t('sessionNotes', 'Session Notes')}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg\">\n                      {session.notes}\n                    </p>\n                  </div>\n                )}\n\n                {/* Next Session */}\n                {session.nextSession && (\n                  <div className=\"mt-4 flex items-center text-sm text-blue-600 dark:text-blue-400\">\n                    <i className=\"fas fa-calendar-plus mr-2\"></i>\n                    {t('nextSession', 'Next session')}: {session.nextSession}\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-history text-4xl text-gray-400 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('noTreatmentHistory', 'No Treatment History')}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('noTreatmentHistoryDesc', 'No treatment sessions found for the selected criteria')}\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default TreatmentHistory;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport TreatmentHistory from '../../components/Treatments/TreatmentHistory';\nimport { Link } from 'react-router-dom';\n\nconst Treatments = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('plans');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n\n  // Mock treatment plans data\n  const [treatmentPlans] = useState([\n    {\n      id: 1,\n      patientName: 'أحمد محمد الأحمد',\n      patientNameEn: '<PERSON>',\n      condition: 'autism',\n      planType: 'comprehensive',\n      startDate: '2024-01-01',\n      endDate: '2024-06-30',\n      status: 'active',\n      progress: 75,\n      therapist: 'Dr. <PERSON>',\n      sessionsCompleted: 15,\n      totalSessions: 20,\n      goals: ['Improve social interaction', 'Develop communication skills', 'Reduce sensory sensitivities']\n    },\n    {\n      id: 2,\n      patientName: 'فاطمة علي السالم',\n      patientNameEn: 'Fatima Ali Al-Salem',\n      condition: 'cerebral_palsy',\n      planType: 'motor_skills',\n      startDate: '2023-12-01',\n      endDate: '2024-05-31',\n      status: 'active',\n      progress: 60,\n      therapist: 'Dr. Ahmed Al-Mansouri',\n      sessionsCompleted: 12,\n      totalSessions: 18,\n      goals: ['Improve gross motor skills', 'Enhance balance', 'Strengthen core muscles']\n    },\n    {\n      id: 3,\n      patientName: 'محمد عبدالله الخالد',\n      patientNameEn: 'Mohammed Abdullah Al-Khalid',\n      condition: 'down_syndrome',\n      planType: 'developmental',\n      startDate: '2024-01-15',\n      endDate: '2024-07-15',\n      status: 'active',\n      progress: 45,\n      therapist: 'Dr. Maryam Al-Zahra',\n      sessionsCompleted: 8,\n      totalSessions: 16,\n      goals: ['Cognitive development', 'Fine motor skills', 'Independence training']\n    },\n    {\n      id: 4,\n      patientName: 'نورا سعد الغامدي',\n      patientNameEn: 'Nora Saad Al-Ghamdi',\n      condition: 'intellectual_disability',\n      planType: 'behavioral',\n      startDate: '2023-11-01',\n      endDate: '2024-04-30',\n      status: 'completed',\n      progress: 100,\n      therapist: 'Dr. Omar Al-Harbi',\n      sessionsCompleted: 20,\n      totalSessions: 20,\n      goals: ['Behavioral management', 'Social skills', 'Daily living skills']\n    }\n  ]);\n\n  // Mock active treatments data\n  const [activeTreatments] = useState([\n    {\n      id: 1,\n      patientName: 'أحمد محمد الأحمد',\n      patientNameEn: 'Ahmed Mohammed Al-Ahmed',\n      treatmentType: 'Sensory Integration Therapy',\n      nextSession: '2024-01-22 09:00',\n      frequency: 'Twice weekly',\n      duration: '60 minutes',\n      therapist: 'Dr. Sarah Al-Rashid',\n      status: 'ongoing'\n    },\n    {\n      id: 2,\n      patientName: 'فاطمة علي السالم',\n      patientNameEn: 'Fatima Ali Al-Salem',\n      treatmentType: 'Physical Therapy',\n      nextSession: '2024-01-23 10:30',\n      frequency: 'Three times weekly',\n      duration: '45 minutes',\n      therapist: 'Dr. Ahmed Al-Mansouri',\n      status: 'ongoing'\n    },\n    {\n      id: 3,\n      patientName: 'محمد عبدالله الخالد',\n      patientNameEn: 'Mohammed Abdullah Al-Khalid',\n      treatmentType: 'Occupational Therapy',\n      nextSession: '2024-01-24 14:00',\n      frequency: 'Weekly',\n      duration: '30 minutes',\n      therapist: 'Dr. Maryam Al-Zahra',\n      status: 'ongoing'\n    }\n  ]);\n\n  const conditionLabels = {\n    autism: t('autism', 'Autism Spectrum Disorder'),\n    cerebral_palsy: t('cerebralPalsy', 'Cerebral Palsy'),\n    down_syndrome: t('downSyndrome', 'Down Syndrome'),\n    intellectual_disability: t('intellectualDisability', 'Intellectual Disability'),\n    spina_bifida: t('spinaBifida', 'Spina Bifida')\n  };\n\n  const planTypeLabels = {\n    comprehensive: { label: t('comprehensive', 'Comprehensive'), color: 'bg-blue-100 text-blue-800' },\n    motor_skills: { label: t('motorSkills', 'Motor Skills'), color: 'bg-green-100 text-green-800' },\n    developmental: { label: t('developmental', 'Developmental'), color: 'bg-purple-100 text-purple-800' },\n    behavioral: { label: t('behavioral', 'Behavioral'), color: 'bg-yellow-100 text-yellow-800' },\n    sensory: { label: t('sensory', 'Sensory'), color: 'bg-pink-100 text-pink-800' }\n  };\n\n  const statusLabels = {\n    active: { label: t('active', 'Active'), color: 'bg-green-100 text-green-800' },\n    completed: { label: t('completed', 'Completed'), color: 'bg-gray-100 text-gray-800' },\n    paused: { label: t('paused', 'Paused'), color: 'bg-yellow-100 text-yellow-800' },\n    cancelled: { label: t('cancelled', 'Cancelled'), color: 'bg-red-100 text-red-800' }\n  };\n\n  const filteredPlans = treatmentPlans.filter(plan => {\n    const matchesSearch = plan.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         plan.patientNameEn.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || plan.status === filterStatus;\n    return matchesSearch && matchesStatus;\n  });\n\n  const getProgressColor = (progress) => {\n    if (progress >= 80) return 'bg-green-500';\n    if (progress >= 60) return 'bg-blue-500';\n    if (progress >= 40) return 'bg-yellow-500';\n    return 'bg-red-500';\n  };\n\n  const tabs = [\n    { id: 'plans', label: t('treatmentPlans', 'Treatment Plans'), icon: 'fas fa-clipboard-list' },\n    { id: 'active', label: t('activeTreatments', 'Active Treatments'), icon: 'fas fa-heartbeat' },\n    { id: 'history', label: t('treatmentHistory', 'Treatment History'), icon: 'fas fa-history' }\n  ];\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('treatments', 'Treatments')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('treatmentsDesc', 'Manage treatment plans and therapy sessions')}\n          </p>\n        </div>\n        <div className=\"flex space-x-4\">\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('newTreatmentPlan', 'New Treatment Plan')}\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n              <i className=\"fas fa-clipboard-list text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('totalPlans', 'Total Plans')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{treatmentPlans.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-full\">\n              <i className=\"fas fa-heartbeat text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('activePlans', 'Active Plans')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {treatmentPlans.filter(p => p.status === 'active').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full\">\n              <i className=\"fas fa-calendar-check text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('sessionsThisWeek', 'Sessions This Week')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">12</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n              <i className=\"fas fa-chart-line text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('averageProgress', 'Average Progress')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {Math.round(treatmentPlans.reduce((sum, p) => sum + p.progress, 0) / treatmentPlans.length)}%\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <i className={tab.icon}></i>\n              <span>{tab.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Filters */}\n      {activeTab === 'plans' && (\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('search', 'Search')}\n              </label>\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  placeholder={t('searchTreatments', 'Search treatments...')}\n                  className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n                <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('status', 'Status')}\n              </label>\n              <select\n                value={filterStatus}\n                onChange={(e) => setFilterStatus(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n                <option value=\"active\">{t('active', 'Active')}</option>\n                <option value=\"completed\">{t('completed', 'Completed')}</option>\n                <option value=\"paused\">{t('paused', 'Paused')}</option>\n                <option value=\"cancelled\">{t('cancelled', 'Cancelled')}</option>\n              </select>\n            </div>\n\n            <div className=\"flex items-end\">\n              <button className=\"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n                <i className=\"fas fa-filter mr-2\"></i>\n                {t('advancedFilters', 'Advanced Filters')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Treatment Plans Tab */}\n      {activeTab === 'plans' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 p-6\">\n            {filteredPlans.map((plan) => (\n              <div key={plan.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {isRTL ? plan.patientName : plan.patientNameEn}\n                  </h3>\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusLabels[plan.status].color}`}>\n                    {statusLabels[plan.status].label}\n                  </span>\n                </div>\n\n                <div className=\"space-y-3 mb-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('condition', 'Condition')}:</span>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {conditionLabels[plan.condition]}\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('planType', 'Plan Type')}:</span>\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${planTypeLabels[plan.planType].color}`}>\n                      {planTypeLabels[plan.planType].label}\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('therapist', 'Therapist')}:</span>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{plan.therapist}</span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('sessions', 'Sessions')}:</span>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {plan.sessionsCompleted}/{plan.totalSessions}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Progress Bar */}\n                <div className=\"mb-4\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">{t('progress', 'Progress')}</span>\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{plan.progress}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(plan.progress)}`}\n                      style={{ width: `${plan.progress}%` }}\n                    />\n                  </div>\n                </div>\n\n                {/* Goals */}\n                <div className=\"mb-4\">\n                  <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">{t('goals', 'Goals')}:</h4>\n                  <ul className=\"space-y-1\">\n                    {plan.goals.slice(0, 2).map((goal, index) => (\n                      <li key={index} className=\"text-sm text-gray-600 dark:text-gray-400 flex items-center\">\n                        <i className=\"fas fa-check-circle text-green-500 mr-2 text-xs\"></i>\n                        {goal}\n                      </li>\n                    ))}\n                    {plan.goals.length > 2 && (\n                      <li className=\"text-sm text-gray-500 dark:text-gray-500\">\n                        +{plan.goals.length - 2} {t('moreGoals', 'more goals')}\n                      </li>\n                    )}\n                  </ul>\n                </div>\n\n                {/* Actions */}\n                <div className=\"flex space-x-2\">\n                  <Link\n                    to={`/treatments/${plan.id}`}\n                    className=\"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors text-center\"\n                  >\n                    {t('viewDetails', 'View Details')}\n                  </Link>\n                  <button className=\"px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n                    <i className=\"fas fa-edit\"></i>\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {filteredPlans.length === 0 && (\n            <div className=\"text-center py-12\">\n              <i className=\"fas fa-clipboard-list text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {t('noTreatmentPlans', 'No treatment plans found')}\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                {t('createFirstPlan', 'Create your first treatment plan to get started')}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Active Treatments Tab */}\n      {activeTab === 'active' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('patient', 'Patient')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('treatmentType', 'Treatment Type')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('nextSession', 'Next Session')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('frequency', 'Frequency')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('therapist', 'Therapist')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('actions', 'Actions')}\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                {activeTreatments.map((treatment) => (\n                  <tr key={treatment.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {isRTL ? treatment.patientName : treatment.patientNameEn}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900 dark:text-white\">{treatment.treatmentType}</div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">{treatment.duration}</div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900 dark:text-white\">\n                        {new Date(treatment.nextSession).toLocaleDateString()}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {new Date(treatment.nextSession).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {treatment.frequency}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {treatment.therapist}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button className=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                          <i className=\"fas fa-eye\"></i>\n                        </button>\n                        <button className=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300\">\n                          <i className=\"fas fa-edit\"></i>\n                        </button>\n                        <button className=\"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300\">\n                          <i className=\"fas fa-pause\"></i>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {activeTreatments.length === 0 && (\n            <div className=\"text-center py-12\">\n              <i className=\"fas fa-heartbeat text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {t('noActiveTreatments', 'No active treatments')}\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                {t('startTreatmentPrompt', 'Start a treatment plan to see active treatments here')}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Treatment History Tab */}\n      {activeTab === 'history' && (\n        <TreatmentHistory />\n      )}\n    </div>\n  );\n};\n\nexport default Treatments;\n"], "names": ["_ref", "patientId", "t", "isRTL", "useLanguage", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedPeriod", "useState", "selectedType", "setSelectedType", "treatmentHistory", "id", "date", "time", "type", "therapist", "patient", "patientEn", "duration", "status", "notes", "exercises", "goals", "progress", "nextSession", "filteredHistory", "filter", "session", "periodMatch", "Date", "now", "typeMatch", "toLowerCase", "includes", "getStatusColor", "_jsxs", "className", "children", "_jsx", "value", "onChange", "e", "target", "length", "map", "concat", "style", "width", "exercise", "index", "goal", "Treatments", "activeTab", "setActiveTab", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "treatmentPlans", "patientName", "patientNameEn", "condition", "planType", "startDate", "endDate", "sessionsCompleted", "totalSessions", "activeTreatments", "treatmentType", "frequency", "<PERSON><PERSON><PERSON><PERSON>", "autism", "cerebral_palsy", "down_syndrome", "intellectual_disability", "spina_bifida", "planTypeLabels", "comprehensive", "label", "color", "motor_skills", "developmental", "behavioral", "sensory", "statusLabels", "active", "completed", "paused", "cancelled", "filteredPlans", "plan", "matchesSearch", "matchesStatus", "tabs", "icon", "p", "Math", "round", "reduce", "sum", "tab", "onClick", "placeholder", "slice", "Link", "to", "treatment", "toLocaleDateString", "toLocaleTimeString", "hour", "minute", "TreatmentHistory"], "sourceRoot": ""}