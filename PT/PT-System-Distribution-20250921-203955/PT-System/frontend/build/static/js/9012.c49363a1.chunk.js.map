{"version": 3, "file": "static/js/9012.c49363a1.chunk.js", "mappings": "yLAIA,MAoUA,EApUwBA,KACtB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACPC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,KACxCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,KACtCK,EAAcC,IAAmBN,EAAAA,EAAAA,UAAS,QAC1CO,EAAYC,IAAiBR,EAAAA,EAAAA,UAAS,QAE7CS,EAAAA,EAAAA,WAAU,KAER,MAAMC,EAAkB,CACtB,CACEC,GAAI,EACJC,SAAU,8BACVC,YAAa,WACbC,UAAW,QACXC,YAAa,oBACbC,cAAe,uBACfC,OAAQ,YACRC,SAAU,aACVC,SAAU,UAEZ,CACER,GAAI,EACJC,SAAU,yBACVC,YAAa,aACbC,UAAW,QACXC,YAAa,oBACbC,cAAe,uBACfC,OAAQ,iBACRC,SAAU,YACVC,SAAU,QAEZ,CACER,GAAI,EACJC,SAAU,sBACVC,YAAa,eACbC,UAAW,QACXC,YAAa,kBACbC,cAAe,uBACfC,OAAQ,YACRC,SAAU,WACVC,SAAU,UAEZ,CACER,GAAI,EACJC,SAAU,wBACVC,YAAa,eACbC,UAAW,QACXC,YAAa,mBACbC,cAAe,uBACfC,OAAQ,QACRC,SAAU,aACVC,SAAU,OAEZ,CACER,GAAI,EACJC,SAAU,uBACVC,YAAa,cACbC,UAAW,QACXC,YAAa,oBACbC,cAAe,uBACfC,OAAQ,YACRC,SAAU,YACVC,SAAU,SAIdC,WAAW,KACTrB,EAAeW,GACfR,GAAW,IACV,MACF,IAEH,MAAMmB,EAAkBJ,IACtB,OAAQA,GACN,IAAK,YACH,MAAO,uEACT,IAAK,iBACH,MAAO,2EACT,IAAK,QACH,MAAO,mEACT,QACE,MAAO,qEAIPK,EAAoBH,IACxB,OAAQA,GACN,IAAK,OACH,MAAO,iCACT,IAAK,SAIL,QACE,MAAO,mCAHT,IAAK,MACH,MAAO,qCAMPI,EAAsBzB,EAAY0B,OAAOC,IAC7C,MAAMC,EAAgBD,EAAWZ,YAAYc,cAAcC,SAASzB,EAAWwB,gBAC1DF,EAAWb,SAASe,cAAcC,SAASzB,EAAWwB,gBACtDF,EAAWV,YAAYY,cAAcC,SAASzB,EAAWwB,eACxEE,EAAiC,QAAjBxB,GAA0BoB,EAAWR,SAAWZ,EAChEyB,EAA6B,QAAfvB,GAAwBkB,EAAWP,WAAaX,EAEpE,OAAOmB,GAAiBG,GAAiBC,IAa3C,OAAI7B,GAEA8B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yDACfD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvB,IAAIE,MAAM,IAAIC,IAAI,CAACC,EAAGC,KACrBP,EAAAA,EAAAA,KAAA,OAAaC,UAAU,6CAAbM,YASpBJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qFAAoFC,UACjGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4GAA2GC,SACtHrC,EAAE,kBAAmB,uBAExBsC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,kEAAiEC,SAAA,EAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZpC,EAAE,yBAA0B,iEAGjCsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BACZT,EAAoBgB,OAAO,IAAE3C,EAAE,cAAe,4BAQzDmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sGAAqGC,UAClHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ErC,EAAE,SAAU,aAEfsC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SACES,KAAK,OACLC,MAAOtC,EACPuC,SAAWC,GAAMvC,EAAcuC,EAAEC,OAAOH,OACxCI,YAAajD,EAAE,oBAAqB,yBACpCoC,UAAU,2IAEZD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DAIjBE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ErC,EAAE,SAAU,aAEfsC,EAAAA,EAAAA,MAAA,UACEO,MAAOpC,EACPqC,SAAWC,GAAMrC,EAAgBqC,EAAEC,OAAOH,OAC1CT,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,KAAA,UAAQU,MAAM,MAAKR,SAAErC,EAAE,cAAe,mBACtCmC,EAAAA,EAAAA,KAAA,UAAQU,MAAM,YAAWR,SAAErC,EAAE,YAAa,gBAC1CmC,EAAAA,EAAAA,KAAA,UAAQU,MAAM,iBAAgBR,SAAErC,EAAE,gBAAiB,qBACnDmC,EAAAA,EAAAA,KAAA,UAAQU,MAAM,QAAOR,SAAErC,EAAE,QAAS,kBAItCsC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/ErC,EAAE,WAAY,gBAEjBsC,EAAAA,EAAAA,MAAA,UACEO,MAAOlC,EACPmC,SAAWC,GAAMnC,EAAcmC,EAAEC,OAAOH,OACxCT,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,KAAA,UAAQU,MAAM,MAAKR,SAAErC,EAAE,WAAY,gBACnCmC,EAAAA,EAAAA,KAAA,UAAQU,MAAM,aAAYR,SAAErC,EAAE,aAAc,iBAC5CmC,EAAAA,EAAAA,KAAA,UAAQU,MAAM,YAAWR,SAAErC,EAAE,YAAa,gBAC1CmC,EAAAA,EAAAA,KAAA,UAAQU,MAAM,WAAUR,SAAErC,EAAE,WAAY,eACxCmC,EAAAA,EAAAA,KAAA,UAAQU,MAAM,YAAWR,SAAErC,EAAE,YAAa,sBAI9CmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,MAAA,UACEY,QAASA,KACP1C,EAAc,IACdE,EAAgB,OAChBE,EAAc,QAEhBwB,UAAU,yFAAwFC,SAAA,EAElGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZpC,EAAE,eAAgB,4BAO3BmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,UAC9BC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,2DAA0DC,SAAA,EACzEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,8BAA6BC,UAC5CC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GrC,EAAE,OAAQ,WAEbmC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GrC,EAAE,UAAW,cAEhBmC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GrC,EAAE,cAAe,mBAEpBmC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GrC,EAAE,OAAQ,WAEbmC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GrC,EAAE,SAAU,aAEfmC,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oGAAmGC,SAC9GrC,EAAE,UAAW,mBAIpBmC,EAAAA,EAAAA,KAAA,SAAOC,UAAU,0EAAyEC,SACvFV,EAAoBa,IAAKX,IACxBS,SAAAA,EAAAA,MAAA,MAAwBF,UAAU,0CAAyCC,SAAA,EACzEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,gCAAAe,OAAkCzB,EAAiBG,EAAWN,cAC1Ee,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,SAC/DR,EAAWb,YAEdmB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,SACtDR,EAAWP,oBAKpBgB,EAAAA,EAAAA,MAAA,MAAIF,UAAU,8BAA6BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,SAC/DR,EAAWZ,eAEdqB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,CAAC,OACnDR,EAAWX,iBAGpBiB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oEAAmEC,SAC9ER,EAAWV,eAEdgB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,uEAAsEC,UA3KhFe,EA4KUvB,EAAWT,cA3KhC,IAAIiC,KAAKD,GAAYE,mBAAmB,QAAS,CACtDC,KAAM,UACNC,MAAO,QACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,gBAwKIxB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,KAAA,QAAMC,UAAS,2EAAAe,OAA6E1B,EAAeI,EAAWR,SAAUgB,SAC7HR,EAAWR,OAAOuC,QAAQ,IAAK,UAGpCzB,EAAAA,EAAAA,KAAA,MAAIC,UAAU,kDAAiDC,UAC7DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,gFAA+EC,UAC/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kBAEfD,EAAAA,EAAAA,KAAA,UAAQC,UAAU,oFAAmFC,UACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAEfD,EAAAA,EAAAA,KAAA,UAAQC,UAAU,4EAA2EC,UAC3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BA1CZP,EAAWd,IAlJdqC,iBAuMgB,IAA/BzB,EAAoBgB,SACnBL,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnErC,EAAE,gBAAiB,2BAEtBmC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5CrC,EAAE,oBAAqB,2D", "sources": ["pages/Forms/FormSubmissions.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { Link } from 'react-router-dom';\n\nconst FormSubmissions = () => {\n  const { t } = useLanguage();\n  const [submissions, setSubmissions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [formFilter, setFormFilter] = useState('all');\n\n  useEffect(() => {\n    // Simulate loading submissions data\n    const mockSubmissions = [\n      {\n        id: 1,\n        formName: 'PT Adult Initial Assessment',\n        patientName: '<PERSON>',\n        patientId: 'PT001',\n        submittedBy: 'Dr. <PERSON>',\n        submittedDate: '2024-02-20T10:30:00Z',\n        status: 'completed',\n        formType: 'assessment',\n        priority: 'normal'\n      },\n      {\n        id: 2,\n        formName: 'Treatment Plan & Goals',\n        patientName: '<PERSON>',\n        patientId: 'PT002',\n        submittedBy: 'Dr. <PERSON>',\n        submittedDate: '2024-02-20T09:15:00Z',\n        status: 'pending_review',\n        formType: 'treatment',\n        priority: 'high'\n      },\n      {\n        id: 3,\n        formName: 'Daily Progress Note',\n        patientName: 'Mike Johnson',\n        patientId: 'PT003',\n        submittedBy: 'Dr. Emily Davis',\n        submittedDate: '2024-02-20T08:45:00Z',\n        status: 'completed',\n        formType: 'progress',\n        priority: 'normal'\n      },\n      {\n        id: 4,\n        formName: 'Pain Assessment Scale',\n        patientName: 'Sarah Wilson',\n        patientId: 'PT004',\n        submittedBy: 'Dr. James Miller',\n        submittedDate: '2024-02-19T16:20:00Z',\n        status: 'draft',\n        formType: 'assessment',\n        priority: 'low'\n      },\n      {\n        id: 5,\n        formName: 'Discharge Assessment',\n        patientName: 'David Brown',\n        patientId: 'PT005',\n        submittedBy: 'Dr. Lisa Anderson',\n        submittedDate: '2024-02-19T14:10:00Z',\n        status: 'completed',\n        formType: 'discharge',\n        priority: 'high'\n      }\n    ];\n    \n    setTimeout(() => {\n      setSubmissions(mockSubmissions);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200';\n      case 'pending_review':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200';\n      case 'draft':\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-200';\n      default:\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high':\n        return 'text-red-600 dark:text-red-400';\n      case 'normal':\n        return 'text-blue-600 dark:text-blue-400';\n      case 'low':\n        return 'text-gray-600 dark:text-gray-400';\n      default:\n        return 'text-blue-600 dark:text-blue-400';\n    }\n  };\n\n  const filteredSubmissions = submissions.filter(submission => {\n    const matchesSearch = submission.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         submission.formName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         submission.submittedBy.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = statusFilter === 'all' || submission.status === statusFilter;\n    const matchesForm = formFilter === 'all' || submission.formType === formFilter;\n    \n    return matchesSearch && matchesStatus && matchesForm;\n  });\n\n  const formatDate = (dateString) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"space-y-4\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 dark:bg-gray-700 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-green-500 via-teal-500 to-blue-500 rounded-lg shadow-lg mb-8\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-green-600 via-teal-600 to-blue-600 bg-clip-text text-transparent\">\n                  {t('formSubmissions', 'Form Submissions')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-inbox text-green-500 mr-2\"></i>\n                  {t('submissionsDescription', 'Review and manage all submitted forms and patient data')}\n                </p>\n              </div>\n              <div className=\"bg-gradient-to-r from-green-400 to-teal-400 text-white px-4 py-2 rounded-full shadow-lg\">\n                <i className=\"fas fa-file-check mr-2\"></i>\n                {filteredSubmissions.length} {t('submissions', 'Submissions')}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('search', 'Search')}\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                placeholder={t('searchSubmissions', 'Search submissions...')}\n                className=\"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n              <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('status', 'Status')}\n            </label>\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n              <option value=\"completed\">{t('completed', 'Completed')}</option>\n              <option value=\"pending_review\">{t('pendingReview', 'Pending Review')}</option>\n              <option value=\"draft\">{t('draft', 'Draft')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('formType', 'Form Type')}\n            </label>\n            <select\n              value={formFilter}\n              onChange={(e) => setFormFilter(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">{t('allForms', 'All Forms')}</option>\n              <option value=\"assessment\">{t('assessment', 'Assessment')}</option>\n              <option value=\"treatment\">{t('treatment', 'Treatment')}</option>\n              <option value=\"progress\">{t('progress', 'Progress')}</option>\n              <option value=\"discharge\">{t('discharge', 'Discharge')}</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => {\n                setSearchTerm('');\n                setStatusFilter('all');\n                setFormFilter('all');\n              }}\n              className=\"w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors\"\n            >\n              <i className=\"fas fa-undo mr-2\"></i>\n              {t('clearFilters', 'Clear Filters')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Submissions List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead className=\"bg-gray-50 dark:bg-gray-900\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('form', 'Form')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('submittedBy', 'Submitted By')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('date', 'Date')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              {filteredSubmissions.map((submission) => (\n                <tr key={submission.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <i className={`fas fa-file-alt text-lg mr-3 ${getPriorityColor(submission.priority)}`}></i>\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {submission.formName}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {submission.formType}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {submission.patientName}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      ID: {submission.patientId}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {submission.submittedBy}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                    {formatDate(submission.submittedDate)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}>\n                      {submission.status.replace('_', ' ')}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200\">\n                        <i className=\"fas fa-eye\"></i>\n                      </button>\n                      <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200\">\n                        <i className=\"fas fa-edit\"></i>\n                      </button>\n                      <button className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200\">\n                        <i className=\"fas fa-download\"></i>\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {filteredSubmissions.length === 0 && (\n        <div className=\"text-center py-12\">\n          <i className=\"fas fa-inbox text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n            {t('noSubmissions', 'No submissions found')}\n          </h3>\n          <p className=\"text-gray-500 dark:text-gray-400\">\n            {t('noSubmissionsDesc', 'No form submissions match your current filters.')}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FormSubmissions;\n"], "names": ["FormSubmissions", "t", "useLanguage", "submissions", "setSubmissions", "useState", "loading", "setLoading", "searchTerm", "setSearchTerm", "statusFilter", "setStatus<PERSON>ilter", "formFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect", "mockSubmissions", "id", "formName", "patientName", "patientId", "submittedBy", "submittedDate", "status", "formType", "priority", "setTimeout", "getStatusColor", "getPriorityColor", "filteredSubmissions", "filter", "submission", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "matchesForm", "_jsx", "className", "children", "_jsxs", "Array", "map", "_", "i", "length", "type", "value", "onChange", "e", "target", "placeholder", "onClick", "concat", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "replace"], "sourceRoot": ""}