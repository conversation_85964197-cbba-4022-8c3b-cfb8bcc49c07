{"version": 3, "file": "static/js/3194.ef80f179.chunk.js", "mappings": "mMAGA,MAsbA,EAtb+BA,IAMxB,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAN0B,YAC9BC,EAAW,OACXC,EAAM,SACNC,EAAQ,UACRC,GAAY,EAAK,YACjBC,EAAc,CAAC,GAChB1B,EACC,MAAM,EAAE2B,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KAGfC,EAAkBR,GAAe,CAAC,GAEjCS,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,CAEnDC,gBAAgB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACpDC,WAAY,GACZC,iBAAkB,GAGlBC,iBAAkBV,EAAgBU,kBAAoB,GACtDC,mBAAoBX,EAAgBW,oBAAsB,GAC1DC,eAAgBZ,EAAgBY,gBAAkB,GAClDC,gBAAiBb,EAAgBa,iBAAmB,GAGpDC,uBAAwB,CACtBC,QAA8C,QAAtC5C,EAAA6B,EAAgBc,8BAAsB,IAAA3C,OAAA,EAAtCA,EAAwC4C,SAAU,OAC1DC,WAAiD,QAAtC5C,EAAA4B,EAAgBc,8BAAsB,IAAA1C,OAAA,EAAtCA,EAAwC4C,YAAa,GAChEC,eAAqD,QAAtC5C,EAAA2B,EAAgBc,8BAAsB,IAAAzC,OAAA,EAAtCA,EAAwC4C,gBAAiB,UACxEC,YAAkD,QAAtC5C,EAAA0B,EAAgBc,8BAAsB,IAAAxC,OAAA,EAAtCA,EAAwC4C,aAAc,WAIpEC,eAAgB,CACdC,QAAsC,QAA9B7C,EAAAyB,EAAgBmB,sBAAc,IAAA5C,OAAA,EAA9BA,EAAgC6C,SAAU,UAClDC,UAAwC,QAA9B7C,EAAAwB,EAAgBmB,sBAAc,IAAA3C,OAAA,EAA9BA,EAAgC6C,WAAY,UACtDC,SAAuC,QAA9B7C,EAAAuB,EAAgBmB,sBAAc,IAAA1C,OAAA,EAA9BA,EAAgC6C,UAAW,UACpDC,YAA0C,QAA9B7C,EAAAsB,EAAgBmB,sBAAc,IAAAzC,OAAA,EAA9BA,EAAgC6C,aAAc,UAC1DC,gBAA8C,QAA9B7C,EAAAqB,EAAgBmB,sBAAc,IAAAxC,OAAA,EAA9BA,EAAgC6C,iBAAkB,WAIpEC,gBAAiB,CACfC,mBAAkD,QAA/B9C,EAAAoB,EAAgByB,uBAAe,IAAA7C,OAAA,EAA/BA,EAAiC8C,oBAAqB,UACzEC,eAA8C,QAA/B9C,EAAAmB,EAAgByB,uBAAe,IAAA5C,OAAA,EAA/BA,EAAiC8C,gBAAiB,QACjEC,qBAAoD,QAA/B9C,EAAAkB,EAAgByB,uBAAe,IAAA3C,OAAA,EAA/BA,EAAiC8C,sBAAuB,gBAC7EC,cAA6C,QAA/B9C,EAAAiB,EAAgByB,uBAAe,IAAA1C,OAAA,EAA/BA,EAAiC8C,eAAgB,SAIjEC,YAAa,CACXC,YAAuC,QAA3B/C,EAAAgB,EAAgB8B,mBAAW,IAAA9C,OAAA,EAA3BA,EAA6B+C,aAAc,UACvDC,WAAsC,QAA3B/C,EAAAe,EAAgB8B,mBAAW,IAAA7C,OAAA,EAA3BA,EAA6B+C,YAAa,UACrDC,cAAyC,QAA3B/C,EAAAc,EAAgB8B,mBAAW,IAAA5C,OAAA,EAA3BA,EAA6B+C,eAAgB,WAC3DC,SAAoC,QAA3B/C,EAAAa,EAAgB8B,mBAAW,IAAA3C,OAAA,EAA3BA,EAA6B+C,UAAW,YAInDC,mBAAoB,CAClBC,4BAA8D,QAAlChD,EAAAY,EAAgBmC,0BAAkB,IAAA/C,OAAA,EAAlCA,EAAoCgD,6BAA8B,GAC9FC,uBAAyD,QAAlChD,EAAAW,EAAgBmC,0BAAkB,IAAA9C,OAAA,EAAlCA,EAAoCgD,wBAAyB,GACpFC,oBAAsD,QAAlChD,EAAAU,EAAgBmC,0BAAkB,IAAA7C,OAAA,EAAlCA,EAAoCgD,qBAAsB,GAC9EC,iBAAmD,QAAlChD,EAAAS,EAAgBmC,0BAAkB,IAAA5C,OAAA,EAAlCA,EAAoCgD,kBAAmB,IAI1EC,eAAgBxC,EAAgBwC,gBAAkB,GAClDC,gBAAiBzC,EAAgByC,iBAAmB,GACpDC,eAAgB1C,EAAgB0C,iBAAkB,EAClDC,mBAAoB3C,EAAgB2C,oBAAsB,GAG1DC,UAAW5C,EAAgB4C,WAAa,GACxCC,WAAY7C,EAAgB6C,YAAc,GAC1CC,eAAgB9C,EAAgB8C,gBAAkB,GAClDC,gBAAiB/C,EAAgB+C,iBAAmB,KAGhDC,EAAkB,CACtB,CAAEC,MAAO,UAAWC,MAAOrD,EAAE,UAAW,YACxC,CAAEoD,MAAO,aAAcC,MAAOrD,EAAE,YAAa,eAC7C,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,mBACrD,CAAEoD,MAAO,eAAgBC,MAAOrD,EAAE,cAAe,iBACjD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,oBAGjDsD,EAAmB,CACvB,CAAEF,MAAO,cAAeC,MAAOrD,EAAE,cAAe,gBAChD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,uBACrD,CAAEoD,MAAO,kBAAmBC,MAAOrD,EAAE,iBAAkB,wBACvD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,gBAAiB,uBACrD,CAAEoD,MAAO,eAAgBC,MAAOrD,EAAE,cAAe,sBAG7CuD,EAAsB,CAC1B,CAAEH,MAAO,OAAQC,MAAOrD,EAAE,OAAQ,SAClC,CAAEoD,MAAO,UAAWC,MAAOrD,EAAE,UAAW,YACxC,CAAEoD,MAAO,aAAcC,MAAOrD,EAAE,aAAc,eAC9C,CAAEoD,MAAO,OAAQC,MAAOrD,EAAE,OAAQ,SAClC,CAAEoD,MAAO,YAAaC,MAAOrD,EAAE,YAAa,eAGxCwD,EAAgB,CACpB,CAAEJ,MAAO,UAAWC,MAAOrD,EAAE,UAAW,YACxC,CAAEoD,MAAO,gBAAiBC,MAAOrD,EAAE,gBAAiB,qBACpD,CAAEoD,MAAO,iBAAkBC,MAAOrD,EAAE,iBAAkB,oBACtD,CAAEoD,MAAO,UAAWC,MAAOrD,EAAE,UAAW,oBACxC,CAAEoD,MAAO,WAAYC,MAAOrD,EAAE,WAAY,sBAGtCyD,EAAoBA,CAACC,EAAON,KAChC/C,EAAkBsD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBD,GAAI,IACP,CAACD,GAAQN,MAIPS,EAAqBA,CAACC,EAAUJ,EAAON,KAC3C/C,EAAkBsD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBD,GAAI,IACP,CAACG,IAAQF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACJD,EAAKG,IAAS,IACjB,CAACJ,GAAQN,QAoBf,OACEW,EAAAA,EAAAA,MAAA,OAAKC,UAAS,sDAAAC,OAAwDhE,EAAQ,cAAgB,gBAAiBiE,SAAA,EAC7GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kDAAiDE,SAC5DlE,EAAE,yBAA0B,8BAE9BD,EAAYqE,OACXL,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CE,SAAA,CACpDlE,EAAE,UAAW,WAAW,KAAGD,EAAYqE,YAI9CL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEE,QAASxE,EACTmE,UAAU,uFAAsFE,SAE/FlE,EAAE,SAAU,aAEfmE,EAAAA,EAAAA,KAAA,UACEE,QA3BSC,KACb1E,GACFA,EAAOQ,IA0BD4D,UAAU,kFAAiFE,SAE1FlE,EAAE,iBAAkB,4BAK3B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,sBAAuB,mCAE5B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAeG,eACtBiE,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DY,UAAU,wIAGdD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,aAAc,kBAEnBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAeO,WACtB6D,SAAWC,GAAMhB,EAAkB,aAAcgB,EAAEC,OAAOtB,OAC1DY,UAAU,kIACVW,YAAa3E,EAAE,gBAAiB,wBAGpC+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,mBAAoB,wBAEzBmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAeQ,iBACtB4D,SAAWC,GAAMhB,EAAkB,mBAAoBgB,EAAEC,OAAOtB,OAChEY,UAAU,kIACVW,YAAa3E,EAAE,gBAAiB,8BAOxC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,8BAA+B,qCAEpC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvB+D,EAAAA,EAAAA,MAAA,UACEX,MAAOhD,EAAeW,eACtByD,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,GAAEc,SAAElE,EAAE,cAAe,kBAClCmD,EAAgByB,IAAIC,IACnBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,eAIzBW,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,kBAAmB,uBAExB+D,EAAAA,EAAAA,MAAA,UACEX,MAAOhD,EAAeY,gBACtBwD,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOtB,OAC/DY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,GAAEc,SAAElE,EAAE,cAAe,kBAClCsD,EAAiBsB,IAAIC,IACpBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,qBAQ7BW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,0BAA2B,+BAEhC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,sBAAuB,2BAE5BmE,EAAAA,EAAAA,KAAA,UACEf,MAAOhD,EAAea,uBAAuBC,OAC7CsD,SAAWC,GAAMZ,EAAmB,yBAA0B,SAAUY,EAAEC,OAAOtB,OACjFY,UAAU,kIAAiIE,SAE1IX,EAAoBqB,IAAIC,IACvBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,cAIzBW,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,gBAAiB,oBAEtBmE,EAAAA,EAAAA,KAAA,UACEf,MAAOhD,EAAea,uBAAuBG,cAC7CoD,SAAWC,GAAMZ,EAAmB,yBAA0B,gBAAiBY,EAAEC,OAAOtB,OACxFY,UAAU,kIAAiIE,SAE1IX,EAAoBqB,IAAIC,IACvBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,oBAQ7BW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,oBAAqB,yBAE1BmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,SAClEY,OAAOC,QAAQ3E,EAAekB,gBAAgBsD,IAAII,IAAA,IAAEC,EAAO7B,GAAM4B,EAAA,OAChEjB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAEiF,EAAOA,EAAMC,OAAO,GAAGC,cAAgBF,EAAMG,MAAM,OAExDjB,EAAAA,EAAAA,KAAA,UACEf,MAAOA,EACPoB,SAAWC,GAAMZ,EAAmB,iBAAkBoB,EAAOR,EAAEC,OAAOtB,OACtEY,UAAU,kIAAiIE,SAE1IV,EAAcoB,IAAIC,IACjBV,EAAAA,EAAAA,KAAA,UAA0Bf,MAAOyB,EAAMzB,MAAMc,SAAEW,EAAMxB,OAAxCwB,EAAMzB,YAVf6B,WAmBhBlB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,cAAe,mBAEpBmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDY,OAAOC,QAAQ3E,EAAe6B,aAAa2C,IAAIS,IAAA,IAAEC,EAAOlC,GAAMiC,EAAA,OAC7DtB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAEsF,EAAOA,EAAMJ,OAAO,GAAGC,cAAgBG,EAAMF,MAAM,OAExDrB,EAAAA,EAAAA,MAAA,UACEX,MAAOA,EACPoB,SAAWC,GAAMZ,EAAmB,cAAeyB,EAAOb,EAAEC,OAAOtB,OACnEY,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,cACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAElE,EAAE,UAAW,cACtCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,WAAUc,SAAElE,EAAE,WAAY,eACxCmE,EAAAA,EAAAA,KAAA,UAAQf,MAAM,WAAUc,SAAElE,EAAE,WAAY,mBAZlCsF,WAoBhBvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,yBAA0B,+BAE/B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,YAAa,gBAElBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAe2C,UACtByB,SAAWC,GAAMhB,EAAkB,YAAagB,EAAEC,OAAOtB,OACzDmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,uBAAwB,iDAG3C+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,aAAc,iBAEnBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAe4C,WACtBwB,SAAWC,GAAMhB,EAAkB,aAAcgB,EAAEC,OAAOtB,OAC1DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,wBAAyB,0DAOhD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,0BAA2B,gCAEhC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,kBAAmB,sBAExBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAewC,gBACtB4B,SAAWC,GAAMhB,EAAkB,kBAAmBgB,EAAEC,OAAOtB,OAC/DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,6BAA8B,qDAGjD+D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,iBAAkB,sBAEvBmE,EAAAA,EAAAA,KAAA,YACEf,MAAOhD,EAAe6C,eACtBuB,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOtB,OAC9DmC,KAAK,IACLvB,UAAU,kIACVW,YAAa3E,EAAE,4BAA6B,2CAGhD+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLiB,GAAG,iBACHC,QAASrF,EAAeyC,eACxB2B,SAAWC,GAAMhB,EAAkB,iBAAkBgB,EAAEC,OAAOe,SAC9DzB,UAAU,2BAEZG,EAAAA,EAAAA,KAAA,SAAOuB,QAAQ,iBAAiB1B,UAAU,uDAAsDE,SAC7FlE,EAAE,2BAA4B,oCAGlCI,EAAeyC,iBACdkB,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ElE,EAAE,qBAAsB,2BAE3BmE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLnB,MAAOhD,EAAe0C,mBACtB0B,SAAWC,GAAMhB,EAAkB,qBAAsBgB,EAAEC,OAAOtB,OAClEY,UAAU,6J", "sources": ["components/SpecialNeeds/SpecialNeedsAssessment.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SpecialNeedsAssessment = ({\n  initialData,\n  onSave,\n  onCancel,\n  isEditing = false,\n  patientInfo = {}\n}) => {\n  const { t, isRTL } = useLanguage();\n\n  // Ensure initialData is always an object\n  const safeInitialData = initialData || {};\n\n  const [assessmentData, setAssessmentData] = useState({\n    // Basic Assessment Info\n    assessmentDate: new Date().toISOString().split('T')[0],\n    assessedBy: '',\n    caregiverPresent: '',\n\n    // Diagnosis and Conditions\n    primaryDiagnosis: safeInitialData.primaryDiagnosis || '',\n    secondaryDiagnoses: safeInitialData.secondaryDiagnoses || [],\n    cognitiveLevel: safeInitialData.cognitiveLevel || '',\n    functionalLevel: safeInitialData.functionalLevel || '',\n\n    // Communication Assessment\n    communicationAbilities: {\n      verbal: safeInitialData.communicationAbilities?.verbal || 'none',\n      nonVerbal: safeInitialData.communicationAbilities?.nonVerbal || [],\n      comprehension: safeInitialData.communicationAbilities?.comprehension || 'limited',\n      expression: safeInitialData.communicationAbilities?.expression || 'limited'\n    },\n\n    // Sensory Processing\n    sensoryProfile: {\n      visual: safeInitialData.sensoryProfile?.visual || 'typical',\n      auditory: safeInitialData.sensoryProfile?.auditory || 'typical',\n      tactile: safeInitialData.sensoryProfile?.tactile || 'typical',\n      vestibular: safeInitialData.sensoryProfile?.vestibular || 'typical',\n      proprioceptive: safeInitialData.sensoryProfile?.proprioceptive || 'typical'\n    },\n\n    // Behavioral Patterns\n    behaviorProfile: {\n      socialInteraction: safeInitialData.behaviorProfile?.socialInteraction || 'limited',\n      attentionSpan: safeInitialData.behaviorProfile?.attentionSpan || 'short',\n      emotionalRegulation: safeInitialData.behaviorProfile?.emotionalRegulation || 'needs-support',\n      adaptability: safeInitialData.behaviorProfile?.adaptability || 'rigid'\n    },\n    \n    // Motor Skills\n    motorSkills: {\n      grossMotor: safeInitialData.motorSkills?.grossMotor || 'delayed',\n      fineMotor: safeInitialData.motorSkills?.fineMotor || 'delayed',\n      coordination: safeInitialData.motorSkills?.coordination || 'impaired',\n      balance: safeInitialData.motorSkills?.balance || 'impaired'\n    },\n\n    // Adaptive Strategies\n    adaptiveStrategies: {\n      environmentalModifications: safeInitialData.adaptiveStrategies?.environmentalModifications || [],\n      communicationSupports: safeInitialData.adaptiveStrategies?.communicationSupports || [],\n      behavioralSupports: safeInitialData.adaptiveStrategies?.behavioralSupports || [],\n      sensorySupports: safeInitialData.adaptiveStrategies?.sensorySupports || []\n    },\n\n    // Goals and Recommendations\n    treatmentGoals: safeInitialData.treatmentGoals || [],\n    recommendations: safeInitialData.recommendations || '',\n    followUpNeeded: safeInitialData.followUpNeeded || false,\n    nextAssessmentDate: safeInitialData.nextAssessmentDate || '',\n\n    // Additional Notes\n    strengths: safeInitialData.strengths || '',\n    challenges: safeInitialData.challenges || '',\n    familyConcerns: safeInitialData.familyConcerns || '',\n    additionalNotes: safeInitialData.additionalNotes || ''\n  });\n\n  const cognitiveLevels = [\n    { value: 'typical', label: t('typical', 'Typical') },\n    { value: 'mild-delay', label: t('mildDelay', 'Mild Delay') },\n    { value: 'moderate-delay', label: t('moderateDelay', 'Moderate Delay') },\n    { value: 'severe-delay', label: t('severeDelay', 'Severe Delay') },\n    { value: 'profound-delay', label: t('profoundDelay', 'Profound Delay') }\n  ];\n\n  const functionalLevels = [\n    { value: 'independent', label: t('independent', 'Independent') },\n    { value: 'minimal-assist', label: t('minimalAssist', 'Minimal Assistance') },\n    { value: 'moderate-assist', label: t('moderateAssist', 'Moderate Assistance') },\n    { value: 'maximum-assist', label: t('maximumAssist', 'Maximum Assistance') },\n    { value: 'total-assist', label: t('totalAssist', 'Total Assistance') }\n  ];\n\n  const communicationLevels = [\n    { value: 'none', label: t('none', 'None') },\n    { value: 'limited', label: t('limited', 'Limited') },\n    { value: 'functional', label: t('functional', 'Functional') },\n    { value: 'good', label: t('good', 'Good') },\n    { value: 'excellent', label: t('excellent', 'Excellent') }\n  ];\n\n  const sensoryLevels = [\n    { value: 'typical', label: t('typical', 'Typical') },\n    { value: 'hyposensitive', label: t('hyposensitive', 'Under-responsive') },\n    { value: 'hypersensitive', label: t('hypersensitive', 'Over-responsive') },\n    { value: 'seeking', label: t('seeking', 'Sensory Seeking') },\n    { value: 'avoiding', label: t('avoiding', 'Sensory Avoiding') }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setAssessmentData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleNestedChange = (category, field, value) => {\n    setAssessmentData(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [field]: value\n      }\n    }));\n  };\n\n  const handleArrayChange = (field, value, isAdd = true) => {\n    setAssessmentData(prev => ({\n      ...prev,\n      [field]: isAdd \n        ? [...prev[field], value]\n        : prev[field].filter(item => item !== value)\n    }));\n  };\n\n  const handleSave = () => {\n    if (onSave) {\n      onSave(assessmentData);\n    }\n  };\n\n  return (\n    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h2 className=\"text-xl font-bold text-gray-900 dark:text-white\">\n            {t('specialNeedsAssessment', 'Special Needs Assessment')}\n          </h2>\n          {patientInfo.name && (\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('patient', 'Patient')}: {patientInfo.name}\n            </p>\n          )}\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={onCancel}\n            className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          <button\n            onClick={handleSave}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            {t('saveAssessment', 'Save Assessment')}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"space-y-8\">\n        {/* Basic Assessment Information */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('basicAssessmentInfo', 'Basic Assessment Information')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('assessmentDate', 'Assessment Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={assessmentData.assessmentDate}\n                onChange={(e) => handleInputChange('assessmentDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('assessedBy', 'Assessed By')}\n              </label>\n              <input\n                type=\"text\"\n                value={assessmentData.assessedBy}\n                onChange={(e) => handleInputChange('assessedBy', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('therapistName', 'Therapist Name')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('caregiverPresent', 'Caregiver Present')}\n              </label>\n              <input\n                type=\"text\"\n                value={assessmentData.caregiverPresent}\n                onChange={(e) => handleInputChange('caregiverPresent', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('caregiverName', 'Caregiver Name')}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Diagnosis and Functional Level */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('diagnosisAndFunctionalLevel', 'Diagnosis and Functional Level')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('cognitiveLevel', 'Cognitive Level')}\n              </label>\n              <select\n                value={assessmentData.cognitiveLevel}\n                onChange={(e) => handleInputChange('cognitiveLevel', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                <option value=\"\">{t('selectLevel', 'Select Level')}</option>\n                {cognitiveLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('functionalLevel', 'Functional Level')}\n              </label>\n              <select\n                value={assessmentData.functionalLevel}\n                onChange={(e) => handleInputChange('functionalLevel', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                <option value=\"\">{t('selectLevel', 'Select Level')}</option>\n                {functionalLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Communication Assessment */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('communicationAssessment', 'Communication Assessment')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('verbalCommunication', 'Verbal Communication')}\n              </label>\n              <select\n                value={assessmentData.communicationAbilities.verbal}\n                onChange={(e) => handleNestedChange('communicationAbilities', 'verbal', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {communicationLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('comprehension', 'Comprehension')}\n              </label>\n              <select\n                value={assessmentData.communicationAbilities.comprehension}\n                onChange={(e) => handleNestedChange('communicationAbilities', 'comprehension', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              >\n                {communicationLevels.map(level => (\n                  <option key={level.value} value={level.value}>{level.label}</option>\n                ))}\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Sensory Processing */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('sensoryProcessing', 'Sensory Processing')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {Object.entries(assessmentData.sensoryProfile).map(([sense, value]) => (\n              <div key={sense}>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t(sense, sense.charAt(0).toUpperCase() + sense.slice(1))}\n                </label>\n                <select\n                  value={value}\n                  onChange={(e) => handleNestedChange('sensoryProfile', sense, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  {sensoryLevels.map(level => (\n                    <option key={level.value} value={level.value}>{level.label}</option>\n                  ))}\n                </select>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Motor Skills */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('motorSkills', 'Motor Skills')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {Object.entries(assessmentData.motorSkills).map(([skill, value]) => (\n              <div key={skill}>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t(skill, skill.charAt(0).toUpperCase() + skill.slice(1))}\n                </label>\n                <select\n                  value={value}\n                  onChange={(e) => handleNestedChange('motorSkills', skill, e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"typical\">{t('typical', 'Typical')}</option>\n                  <option value=\"delayed\">{t('delayed', 'Delayed')}</option>\n                  <option value=\"impaired\">{t('impaired', 'Impaired')}</option>\n                  <option value=\"emerging\">{t('emerging', 'Emerging')}</option>\n                </select>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Strengths and Challenges */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('strengthsAndChallenges', 'Strengths and Challenges')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('strengths', 'Strengths')}\n              </label>\n              <textarea\n                value={assessmentData.strengths}\n                onChange={(e) => handleInputChange('strengths', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('strengthsPlaceholder', 'List patient strengths and abilities...')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('challenges', 'Challenges')}\n              </label>\n              <textarea\n                value={assessmentData.challenges}\n                onChange={(e) => handleInputChange('challenges', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('challengesPlaceholder', 'List areas of difficulty and challenges...')}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Recommendations */}\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('recommendationsAndGoals', 'Recommendations and Goals')}\n          </h3>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('recommendations', 'Recommendations')}\n              </label>\n              <textarea\n                value={assessmentData.recommendations}\n                onChange={(e) => handleInputChange('recommendations', e.target.value)}\n                rows=\"4\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('recommendationsPlaceholder', 'Treatment recommendations and strategies...')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('familyConcerns', 'Family Concerns')}\n              </label>\n              <textarea\n                value={assessmentData.familyConcerns}\n                onChange={(e) => handleInputChange('familyConcerns', e.target.value)}\n                rows=\"3\"\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                placeholder={t('familyConcernsPlaceholder', 'Family concerns and priorities...')}\n              />\n            </div>\n            <div className=\"flex items-center space-x-3\">\n              <input\n                type=\"checkbox\"\n                id=\"followUpNeeded\"\n                checked={assessmentData.followUpNeeded}\n                onChange={(e) => handleInputChange('followUpNeeded', e.target.checked)}\n                className=\"w-4 h-4 text-blue-600\"\n              />\n              <label htmlFor=\"followUpNeeded\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                {t('followUpAssessmentNeeded', 'Follow-up assessment needed')}\n              </label>\n            </div>\n            {assessmentData.followUpNeeded && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('nextAssessmentDate', 'Next Assessment Date')}\n                </label>\n                <input\n                  type=\"date\"\n                  value={assessmentData.nextAssessmentDate}\n                  onChange={(e) => handleInputChange('nextAssessmentDate', e.target.value)}\n                  className=\"w-full md:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                />\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SpecialNeedsAssessment;\n"], "names": ["_ref", "_safeInitialData$comm", "_safeInitialData$comm2", "_safeInitialData$comm3", "_safeInitialData$comm4", "_safeInitialData$sens", "_safeInitialData$sens2", "_safeInitialData$sens3", "_safeInitialData$sens4", "_safeInitialData$sens5", "_safeInitialData$beha", "_safeInitialData$beha2", "_safeInitialData$beha3", "_safeInitialData$beha4", "_safeInitialData$moto", "_safeInitialData$moto2", "_safeInitialData$moto3", "_safeInitialData$moto4", "_safeInitialData$adap", "_safeInitialData$adap2", "_safeInitialData$adap3", "_safeInitialData$adap4", "initialData", "onSave", "onCancel", "isEditing", "patientInfo", "t", "isRTL", "useLanguage", "safeInitialData", "assessmentData", "setAssessmentData", "useState", "assessmentDate", "Date", "toISOString", "split", "assessedBy", "caregiverPresent", "primaryDiagnosis", "secondaryDiagnoses", "cognitiveLevel", "functionalLevel", "communicationAbilities", "verbal", "nonVerbal", "comprehension", "expression", "sensoryProfile", "visual", "auditory", "tactile", "vestibular", "proprioceptive", "behaviorProfile", "socialInteraction", "attentionSpan", "emotionalRegulation", "adaptability", "motorSkills", "grossMotor", "fineMotor", "coordination", "balance", "adaptiveStrategies", "environmentalModifications", "communicationSupports", "behavioralSupports", "sensorySupports", "treatmentGoals", "recommendations", "followUpNeeded", "nextAssessmentDate", "strengths", "challenges", "familyConcerns", "additionalNotes", "cognitiveLevels", "value", "label", "functionalLevels", "communicationLevels", "sensoryLevels", "handleInputChange", "field", "prev", "_objectSpread", "handleNestedChange", "category", "_jsxs", "className", "concat", "children", "_jsx", "name", "onClick", "handleSave", "type", "onChange", "e", "target", "placeholder", "map", "level", "Object", "entries", "_ref2", "sense", "char<PERSON>t", "toUpperCase", "slice", "_ref3", "skill", "rows", "id", "checked", "htmlFor"], "sourceRoot": ""}