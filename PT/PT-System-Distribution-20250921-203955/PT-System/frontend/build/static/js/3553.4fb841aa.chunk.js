"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3194,3553],{3194:(e,a,r)=>{r.r(a),r.d(a,{default:()=>n});var t=r(2555),s=r(5043),l=r(7921),i=r(579);const n=e=>{var a,r,n,d,o,c,g,m,x,u,h,y,b,p,v,f,k,j,N,w,C;let{initialData:A,onSave:P,onCancel:S,isEditing:D=!1,patientInfo:I={}}=e;const{t:M,isRTL:R}=(0,l.o)(),E=A||{},[F,L]=(0,s.useState)({assessmentDate:(new Date).toISOString().split("T")[0],assessedBy:"",caregiverPresent:"",primaryDiagnosis:E.primaryDiagnosis||"",secondaryDiagnoses:E.secondaryDiagnoses||[],cognitiveLevel:E.cognitiveLevel||"",functionalLevel:E.functionalLevel||"",communicationAbilities:{verbal:(null===(a=E.communicationAbilities)||void 0===a?void 0:a.verbal)||"none",nonVerbal:(null===(r=E.communicationAbilities)||void 0===r?void 0:r.nonVerbal)||[],comprehension:(null===(n=E.communicationAbilities)||void 0===n?void 0:n.comprehension)||"limited",expression:(null===(d=E.communicationAbilities)||void 0===d?void 0:d.expression)||"limited"},sensoryProfile:{visual:(null===(o=E.sensoryProfile)||void 0===o?void 0:o.visual)||"typical",auditory:(null===(c=E.sensoryProfile)||void 0===c?void 0:c.auditory)||"typical",tactile:(null===(g=E.sensoryProfile)||void 0===g?void 0:g.tactile)||"typical",vestibular:(null===(m=E.sensoryProfile)||void 0===m?void 0:m.vestibular)||"typical",proprioceptive:(null===(x=E.sensoryProfile)||void 0===x?void 0:x.proprioceptive)||"typical"},behaviorProfile:{socialInteraction:(null===(u=E.behaviorProfile)||void 0===u?void 0:u.socialInteraction)||"limited",attentionSpan:(null===(h=E.behaviorProfile)||void 0===h?void 0:h.attentionSpan)||"short",emotionalRegulation:(null===(y=E.behaviorProfile)||void 0===y?void 0:y.emotionalRegulation)||"needs-support",adaptability:(null===(b=E.behaviorProfile)||void 0===b?void 0:b.adaptability)||"rigid"},motorSkills:{grossMotor:(null===(p=E.motorSkills)||void 0===p?void 0:p.grossMotor)||"delayed",fineMotor:(null===(v=E.motorSkills)||void 0===v?void 0:v.fineMotor)||"delayed",coordination:(null===(f=E.motorSkills)||void 0===f?void 0:f.coordination)||"impaired",balance:(null===(k=E.motorSkills)||void 0===k?void 0:k.balance)||"impaired"},adaptiveStrategies:{environmentalModifications:(null===(j=E.adaptiveStrategies)||void 0===j?void 0:j.environmentalModifications)||[],communicationSupports:(null===(N=E.adaptiveStrategies)||void 0===N?void 0:N.communicationSupports)||[],behavioralSupports:(null===(w=E.adaptiveStrategies)||void 0===w?void 0:w.behavioralSupports)||[],sensorySupports:(null===(C=E.adaptiveStrategies)||void 0===C?void 0:C.sensorySupports)||[]},treatmentGoals:E.treatmentGoals||[],recommendations:E.recommendations||"",followUpNeeded:E.followUpNeeded||!1,nextAssessmentDate:E.nextAssessmentDate||"",strengths:E.strengths||"",challenges:E.challenges||"",familyConcerns:E.familyConcerns||"",additionalNotes:E.additionalNotes||""}),T=[{value:"typical",label:M("typical","Typical")},{value:"mild-delay",label:M("mildDelay","Mild Delay")},{value:"moderate-delay",label:M("moderateDelay","Moderate Delay")},{value:"severe-delay",label:M("severeDelay","Severe Delay")},{value:"profound-delay",label:M("profoundDelay","Profound Delay")}],q=[{value:"independent",label:M("independent","Independent")},{value:"minimal-assist",label:M("minimalAssist","Minimal Assistance")},{value:"moderate-assist",label:M("moderateAssist","Moderate Assistance")},{value:"maximum-assist",label:M("maximumAssist","Maximum Assistance")},{value:"total-assist",label:M("totalAssist","Total Assistance")}],B=[{value:"none",label:M("none","None")},{value:"limited",label:M("limited","Limited")},{value:"functional",label:M("functional","Functional")},{value:"good",label:M("good","Good")},{value:"excellent",label:M("excellent","Excellent")}],O=[{value:"typical",label:M("typical","Typical")},{value:"hyposensitive",label:M("hyposensitive","Under-responsive")},{value:"hypersensitive",label:M("hypersensitive","Over-responsive")},{value:"seeking",label:M("seeking","Sensory Seeking")},{value:"avoiding",label:M("avoiding","Sensory Avoiding")}],U=(e,a)=>{L(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a}))},G=(e,a,r)=>{L(s=>(0,t.A)((0,t.A)({},s),{},{[e]:(0,t.A)((0,t.A)({},s[e]),{},{[a]:r})}))};return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ".concat(R?"font-arabic":"font-english"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:M("specialNeedsAssessment","Special Needs Assessment")}),I.name&&(0,i.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[M("patient","Patient"),": ",I.name]})]}),(0,i.jsxs)("div",{className:"flex space-x-3",children:[(0,i.jsx)("button",{onClick:S,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:M("cancel","Cancel")}),(0,i.jsx)("button",{onClick:()=>{P&&P(F)},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:M("saveAssessment","Save Assessment")})]})]}),(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("basicAssessmentInfo","Basic Assessment Information")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("assessmentDate","Assessment Date")}),(0,i.jsx)("input",{type:"date",value:F.assessmentDate,onChange:e=>U("assessmentDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("assessedBy","Assessed By")}),(0,i.jsx)("input",{type:"text",value:F.assessedBy,onChange:e=>U("assessedBy",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("therapistName","Therapist Name")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("caregiverPresent","Caregiver Present")}),(0,i.jsx)("input",{type:"text",value:F.caregiverPresent,onChange:e=>U("caregiverPresent",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("caregiverName","Caregiver Name")})]})]})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("diagnosisAndFunctionalLevel","Diagnosis and Functional Level")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("cognitiveLevel","Cognitive Level")}),(0,i.jsxs)("select",{value:F.cognitiveLevel,onChange:e=>U("cognitiveLevel",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:M("selectLevel","Select Level")}),T.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("functionalLevel","Functional Level")}),(0,i.jsxs)("select",{value:F.functionalLevel,onChange:e=>U("functionalLevel",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:M("selectLevel","Select Level")}),q.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))]})]})]})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("communicationAssessment","Communication Assessment")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("verbalCommunication","Verbal Communication")}),(0,i.jsx)("select",{value:F.communicationAbilities.verbal,onChange:e=>G("communicationAbilities","verbal",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:B.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("comprehension","Comprehension")}),(0,i.jsx)("select",{value:F.communicationAbilities.comprehension,onChange:e=>G("communicationAbilities","comprehension",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:B.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("sensoryProcessing","Sensory Processing")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Object.entries(F.sensoryProfile).map(e=>{let[a,r]=e;return(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M(a,a.charAt(0).toUpperCase()+a.slice(1))}),(0,i.jsx)("select",{value:r,onChange:e=>G("sensoryProfile",a,e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:O.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]},a)})})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("motorSkills","Motor Skills")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(F.motorSkills).map(e=>{let[a,r]=e;return(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M(a,a.charAt(0).toUpperCase()+a.slice(1))}),(0,i.jsxs)("select",{value:r,onChange:e=>G("motorSkills",a,e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"typical",children:M("typical","Typical")}),(0,i.jsx)("option",{value:"delayed",children:M("delayed","Delayed")}),(0,i.jsx)("option",{value:"impaired",children:M("impaired","Impaired")}),(0,i.jsx)("option",{value:"emerging",children:M("emerging","Emerging")})]})]},a)})})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("strengthsAndChallenges","Strengths and Challenges")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("strengths","Strengths")}),(0,i.jsx)("textarea",{value:F.strengths,onChange:e=>U("strengths",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("strengthsPlaceholder","List patient strengths and abilities...")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("challenges","Challenges")}),(0,i.jsx)("textarea",{value:F.challenges,onChange:e=>U("challenges",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("challengesPlaceholder","List areas of difficulty and challenges...")})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("recommendationsAndGoals","Recommendations and Goals")}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("recommendations","Recommendations")}),(0,i.jsx)("textarea",{value:F.recommendations,onChange:e=>U("recommendations",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("recommendationsPlaceholder","Treatment recommendations and strategies...")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("familyConcerns","Family Concerns")}),(0,i.jsx)("textarea",{value:F.familyConcerns,onChange:e=>U("familyConcerns",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("familyConcernsPlaceholder","Family concerns and priorities...")})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("input",{type:"checkbox",id:"followUpNeeded",checked:F.followUpNeeded,onChange:e=>U("followUpNeeded",e.target.checked),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("label",{htmlFor:"followUpNeeded",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:M("followUpAssessmentNeeded","Follow-up assessment needed")})]}),F.followUpNeeded&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("nextAssessmentDate","Next Assessment Date")}),(0,i.jsx)("input",{type:"date",value:F.nextAssessmentDate,onChange:e=>U("nextAssessmentDate",e.target.value),className:"w-full md:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]})]})]})}},3553:(e,a,r)=>{r.r(a),r.d(a,{default:()=>h});var t=r(2555),s=r(5043),l=r(3216),i=r(7921),n=r(4528),d=r(3768),o=r(3194),c=r(7016),g=(r(4117),r(579));class m extends s.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,a){console.error("ErrorBoundary caught an error:",e,a),this.setState({error:e,errorInfo:a})}render(){return this.state.hasError?(0,g.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,g.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6",children:[(0,g.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full mb-4",children:(0,g.jsx)("i",{className:"fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl"})}),(0,g.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white text-center mb-2",children:"Something went wrong"}),(0,g.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-center mb-6",children:"We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists."}),(0,g.jsxs)("div",{className:"flex space-x-3",children:[(0,g.jsxs)("button",{onClick:()=>window.location.reload(),className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,g.jsx)("i",{className:"fas fa-refresh mr-2"}),"Refresh Page"]}),(0,g.jsxs)("button",{onClick:()=>window.history.back(),className:"flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,g.jsx)("i",{className:"fas fa-arrow-left mr-2"}),"Go Back"]})]}),!1]})}):this.props.children}}const x=m;var u=r(6305);const h=()=>{var e,a;const{t:r,isRTL:m}=(0,i.o)(),{user:h,isAuthenticated:y,hasPermission:b}=(0,n.A)(),p=(0,l.Zp)(),{id:v}=(0,l.g)(),[f,k]=(0,s.useState)(1),[j,N]=(0,s.useState)(!1),[w,C]=(0,s.useState)(!1),[A,P]=(0,s.useState)(!1),[S,D]=(0,s.useState)(null),[I,M]=(0,s.useState)(!1),[R,E]=(0,s.useState)(null),[F,L]=(0,s.useState)({firstName:"",lastName:"",firstNameAr:"",lastNameAr:"",nationalId:"",dateOfBirth:"",gender:"",phone:"",email:"",address:"",city:"",region:"",postalCode:"",emergencyContactName:"",emergencyContactPhone:"",emergencyContactRelation:"",insuranceProvider:"",insuranceNumber:"",insuranceExpiry:"",medicalHistory:"",currentMedications:"",allergies:"",primaryDiagnosis:"",referringPhysician:"",hasSpecialNeeds:!1,specialNeedsType:[],communicationMethod:"",sensoryPreferences:{lighting:"normal",sound:"normal",texture:"normal"},behavioralNotes:"",caregiverNotes:""});(0,s.useEffect)(()=>{v&&(C(!0),T(v))},[v]);const T=async e=>{N(!0);try{const a={id:e,firstName:"Ahmed",lastName:"Mohammed Al-Ahmed",firstNameAr:"\u0623\u062d\u0645\u062f",lastNameAr:"\u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",nationalId:"**********",dateOfBirth:"1995-03-15",gender:"male",phone:"+966501234567",email:"<EMAIL>",address:"Riyadh",addressAr:"\u0627\u0644\u0631\u064a\u0627\u0636",city:"Riyadh",postalCode:"12345",country:"Saudi Arabia",emergencyContactName:"Fatima Al-Ahmed",emergencyContactNameAr:"\u0641\u0627\u0637\u0645\u0629 \u0627\u0644\u0623\u062d\u0645\u062f",emergencyContactPhone:"+966507654321",emergencyContactRelationship:"mother",insuranceProvider:"Bupa Arabia",insurancePolicyNumber:"BP123456789",primaryDiagnosis:"Cerebral Palsy",currentMedications:"Baclofen 10mg, Vitamin D",allergies:"Penicillin",referringPhysician:"Dr. Sarah Al-Rashid"};L(e=>(0,t.A)((0,t.A)({},e),a))}catch(a){d.Ay.error(r("errorLoadingPatient","Error loading patient data"))}finally{N(!1)}},q=[{id:1,title:r("basicInfo","Basic Information"),icon:"fas fa-user"},{id:2,title:r("contactInfo","Contact Information"),icon:"fas fa-phone"},{id:3,title:r("emergencyContact","Emergency Contact"),icon:"fas fa-exclamation-triangle"},{id:4,title:r("insurance","Insurance"),icon:"fas fa-shield-alt"},{id:5,title:r("medicalInfo","Medical Information"),icon:"fas fa-notes-medical"},{id:6,title:r("specialNeeds","Special Needs"),icon:"fas fa-puzzle-piece"}],B=[{id:"autism",label:r("autism","Autism Spectrum Disorder"),icon:"\ud83e\udde9"},{id:"cerebralPalsy",label:r("cerebralPalsy","Cerebral Palsy"),icon:"\ud83e\uddbd"},{id:"downSyndrome",label:r("downSyndrome","Down Syndrome"),icon:"\ud83d\udc99"},{id:"intellectualDisability",label:r("intellectualDisability","Intellectual Disability"),icon:"\ud83e\udde0"},{id:"adhd",label:r("adhd","ADHD"),icon:"\u26a1"},{id:"sensoryProcessing",label:r("sensoryProcessing","Sensory Processing Disorder"),icon:"\ud83d\udc41\ufe0f"}],O=(e,a)=>{L(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a}))},U=e=>{switch(e){case 1:return F.firstName&&F.lastName&&F.nationalId&&F.dateOfBirth&&F.gender;case 2:return F.phone&&F.address&&F.city;case 3:return F.emergencyContactName&&F.emergencyContactPhone;default:return!0}};return(0,g.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(m?"font-arabic":"font-english"),children:[(0,g.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 px-6 py-4",children:(0,g.jsx)("div",{className:"flex items-center justify-between",children:(0,g.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,g.jsx)("button",{onClick:()=>p("/patients"),className:"p-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,g.jsx)("i",{className:"fas fa-arrow-left"})}),(0,g.jsxs)("div",{children:[(0,g.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:w?r("editPatient","Edit Patient"):r("addNewPatient","Add New Patient")}),(0,g.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[r("step","Step")," ",f," ",r("of","of")," ",q.length,": ",null===(e=q[f-1])||void 0===e?void 0:e.title]})]})]})})}),(0,g.jsxs)("div",{className:"max-w-4xl mx-auto p-6",children:[(0,g.jsx)("div",{className:"mb-8",children:(0,g.jsx)("div",{className:"flex items-center justify-between",children:q.map((e,a)=>(0,g.jsxs)("div",{className:"flex items-center",children:[(0,g.jsx)("div",{className:"flex items-center justify-center w-10 h-10 rounded-full border-2 ".concat(f>=e.id?"border-blue-500 bg-blue-500 text-white":"border-gray-300 text-gray-400"),children:(0,g.jsx)("i",{className:e.icon})}),a<q.length-1&&(0,g.jsx)("div",{className:"w-full h-1 mx-4 ".concat(f>e.id?"bg-blue-500":"bg-gray-300")})]},e.id))})}),(0,g.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,g.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:null===(a=q[f-1])||void 0===a?void 0:a.title}),(()=>{switch(f){case 1:return(0,g.jsx)("div",{className:"space-y-6",children:(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("firstName","First Name")," *"]}),(0,g.jsx)("input",{type:"text",value:F.firstName,onChange:e=>O("firstName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("lastName","Last Name")," *"]}),(0,g.jsx)("input",{type:"text",value:F.lastName,onChange:e=>O("lastName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("firstNameAr","First Name (Arabic)")}),(0,g.jsx)("input",{type:"text",value:F.firstNameAr,onChange:e=>O("firstNameAr",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",dir:"rtl"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("lastNameAr","Last Name (Arabic)")}),(0,g.jsx)("input",{type:"text",value:F.lastNameAr,onChange:e=>O("lastNameAr",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",dir:"rtl"})]}),(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("nationalId","National ID")," *"]}),(0,g.jsx)("input",{type:"text",value:F.nationalId,onChange:e=>O("nationalId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("dateOfBirth","Date of Birth")," *"]}),(0,g.jsx)("input",{type:"date",value:F.dateOfBirth,onChange:e=>O("dateOfBirth",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{className:"md:col-span-2",children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("gender","Gender")," *"]}),(0,g.jsxs)("div",{className:"flex space-x-4",children:[(0,g.jsxs)("label",{className:"flex items-center",children:[(0,g.jsx)("input",{type:"radio",name:"gender",value:"male",checked:"male"===F.gender,onChange:e=>O("gender",e.target.value),className:"mr-2"}),r("male","Male")]}),(0,g.jsxs)("label",{className:"flex items-center",children:[(0,g.jsx)("input",{type:"radio",name:"gender",value:"female",checked:"female"===F.gender,onChange:e=>O("gender",e.target.value),className:"mr-2"}),r("female","Female")]})]})]})]})});case 2:return(0,g.jsx)("div",{className:"space-y-6",children:(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("phone","Phone Number")," *"]}),(0,g.jsx)("input",{type:"tel",value:F.phone,onChange:e=>O("phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("email","Email")}),(0,g.jsx)("input",{type:"email",value:F.email,onChange:e=>O("email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,g.jsxs)("div",{className:"md:col-span-2",children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("address","Address")," *"]}),(0,g.jsx)("textarea",{value:F.address,onChange:e=>O("address",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("city","City")," *"]}),(0,g.jsx)("input",{type:"text",value:F.city,onChange:e=>O("city",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("region","Region")}),(0,g.jsx)("input",{type:"text",value:F.region,onChange:e=>O("region",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})});case 3:return(0,g.jsx)("div",{className:"space-y-6",children:(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("emergencyContactName","Emergency Contact Name")," *"]}),(0,g.jsx)("input",{type:"text",value:F.emergencyContactName,onChange:e=>O("emergencyContactName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r("emergencyContactPhone","Emergency Contact Phone")," *"]}),(0,g.jsx)("input",{type:"tel",value:F.emergencyContactPhone,onChange:e=>O("emergencyContactPhone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("emergencyContactRelation","Relationship")}),(0,g.jsxs)("select",{value:F.emergencyContactRelation,onChange:e=>O("emergencyContactRelation",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,g.jsx)("option",{value:"",children:r("selectRelationship","Select Relationship")}),(0,g.jsx)("option",{value:"parent",children:r("parent","Parent")}),(0,g.jsx)("option",{value:"spouse",children:r("spouse","Spouse")}),(0,g.jsx)("option",{value:"sibling",children:r("sibling","Sibling")}),(0,g.jsx)("option",{value:"child",children:r("child","Child")}),(0,g.jsx)("option",{value:"friend",children:r("friend","Friend")}),(0,g.jsx)("option",{value:"caregiver",children:r("caregiver","Caregiver")}),(0,g.jsx)("option",{value:"other",children:r("other","Other")})]})]})]})});case 4:return(0,g.jsx)("div",{className:"space-y-6",children:(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("insuranceProvider","Insurance Provider")}),(0,g.jsxs)("select",{value:F.insuranceProvider,onChange:e=>O("insuranceProvider",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,g.jsx)("option",{value:"",children:r("selectInsuranceProvider","Select Insurance Provider")}),(0,g.jsx)("option",{value:"bupa",children:r("bupa","Bupa Arabia")}),(0,g.jsx)("option",{value:"tawuniya",children:r("tawuniya","Tawuniya")}),(0,g.jsx)("option",{value:"medgulf",children:r("medgulf","MedGulf")}),(0,g.jsx)("option",{value:"alrajhi",children:r("alrajhi","Al Rajhi Takaful")}),(0,g.jsx)("option",{value:"saico",children:r("saico","SAICO")}),(0,g.jsx)("option",{value:"ncci",children:r("ncci","NCCI")}),(0,g.jsx)("option",{value:"other",children:r("other","Other")})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("insuranceNumber","Insurance Number")}),(0,g.jsx)("input",{type:"text",value:F.insuranceNumber,onChange:e=>O("insuranceNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("insuranceExpiry","Insurance Expiry Date")}),(0,g.jsx)("input",{type:"date",value:F.insuranceExpiry,onChange:e=>O("insuranceExpiry",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})});case 5:return(0,g.jsx)("div",{className:"space-y-6",children:(0,g.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("primaryDiagnosis","Primary Diagnosis")}),(0,g.jsx)("input",{type:"text",value:F.primaryDiagnosis,onChange:e=>O("primaryDiagnosis",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r("primaryDiagnosisPlaceholder","Enter primary diagnosis or condition")})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("referringPhysician","Referring Physician")}),(0,g.jsx)("input",{type:"text",value:F.referringPhysician,onChange:e=>O("referringPhysician",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r("referringPhysicianPlaceholder","Enter referring physician name")})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("medicalHistory","Medical History")}),(0,g.jsx)("textarea",{value:F.medicalHistory,onChange:e=>O("medicalHistory",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r("medicalHistoryPlaceholder","Enter relevant medical history, previous surgeries, conditions...")})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("currentMedications","Current Medications")}),(0,g.jsx)("textarea",{value:F.currentMedications,onChange:e=>O("currentMedications",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r("currentMedicationsPlaceholder","List current medications, dosages, and frequency...")})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("allergies","Allergies")}),(0,g.jsx)("textarea",{value:F.allergies,onChange:e=>O("allergies",e.target.value),rows:"2",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r("allergiesPlaceholder","List any known allergies to medications, foods, or materials...")})]})]})});case 6:return(0,g.jsxs)("div",{className:"space-y-6",children:[(0,g.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,g.jsx)("input",{type:"checkbox",id:"hasSpecialNeeds",checked:F.hasSpecialNeeds,onChange:e=>O("hasSpecialNeeds",e.target.checked),className:"w-4 h-4 text-blue-600"}),(0,g.jsx)("label",{htmlFor:"hasSpecialNeeds",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:r("patientHasSpecialNeeds","Patient has special needs")})]}),F.hasSpecialNeeds&&(0,g.jsxs)("div",{className:"space-y-6",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:r("specialNeedsType","Type of Special Needs")}),(0,g.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:B.map(e=>(0,g.jsx)("div",{onClick:()=>(e=>{L(a=>(0,t.A)((0,t.A)({},a),{},{specialNeedsType:a.specialNeedsType.includes(e)?a.specialNeedsType.filter(a=>a!==e):[...a.specialNeedsType,e]}))})(e.id),className:"p-4 border rounded-lg cursor-pointer transition-colors ".concat(F.specialNeedsType.includes(e.id)?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600 hover:border-gray-400"),children:(0,g.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,g.jsx)("span",{className:"text-2xl",children:e.icon}),(0,g.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.label})]})},e.id))})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("communicationMethod","Preferred Communication Method")}),(0,g.jsxs)("select",{value:F.communicationMethod,onChange:e=>O("communicationMethod",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,g.jsx)("option",{value:"",children:r("selectCommunicationMethod","Select Communication Method")}),(0,g.jsx)("option",{value:"verbal",children:r("verbal","Verbal Communication")}),(0,g.jsx)("option",{value:"visual",children:r("visual","Visual Aids/Pictures")}),(0,g.jsx)("option",{value:"sign",children:r("signLanguage","Sign Language")}),(0,g.jsx)("option",{value:"written",children:r("written","Written Communication")}),(0,g.jsx)("option",{value:"assistive",children:r("assistiveDevice","Assistive Communication Device")}),(0,g.jsx)("option",{value:"caregiver",children:r("throughCaregiver","Through Caregiver")})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:r("sensoryPreferences","Sensory Preferences")}),(0,g.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1",children:r("lighting","Lighting")}),(0,g.jsxs)("select",{value:F.sensoryPreferences.lighting,onChange:e=>O("sensoryPreferences",(0,t.A)((0,t.A)({},F.sensoryPreferences),{},{lighting:e.target.value})),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,g.jsx)("option",{value:"normal",children:r("normal","Normal")}),(0,g.jsx)("option",{value:"dim",children:r("dim","Dim/Low")}),(0,g.jsx)("option",{value:"bright",children:r("bright","Bright")}),(0,g.jsx)("option",{value:"avoid",children:r("avoidFlashing","Avoid Flashing")})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1",children:r("sound","Sound")}),(0,g.jsxs)("select",{value:F.sensoryPreferences.sound,onChange:e=>O("sensoryPreferences",(0,t.A)((0,t.A)({},F.sensoryPreferences),{},{sound:e.target.value})),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,g.jsx)("option",{value:"normal",children:r("normal","Normal")}),(0,g.jsx)("option",{value:"quiet",children:r("quiet","Quiet/Low")}),(0,g.jsx)("option",{value:"noSudden",children:r("noSuddenSounds","No Sudden Sounds")}),(0,g.jsx)("option",{value:"musicTherapy",children:r("musicTherapy","Music Therapy")})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1",children:r("texture","Texture/Touch")}),(0,g.jsxs)("select",{value:F.sensoryPreferences.texture,onChange:e=>O("sensoryPreferences",(0,t.A)((0,t.A)({},F.sensoryPreferences),{},{texture:e.target.value})),className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,g.jsx)("option",{value:"normal",children:r("normal","Normal")}),(0,g.jsx)("option",{value:"gentle",children:r("gentle","Gentle Touch")}),(0,g.jsx)("option",{value:"firm",children:r("firm","Firm Pressure")}),(0,g.jsx)("option",{value:"avoid",children:r("avoidTextures","Avoid Certain Textures")})]})]})]})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("behavioralNotes","Behavioral Notes")}),(0,g.jsx)("textarea",{value:F.behavioralNotes,onChange:e=>O("behavioralNotes",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r("behavioralNotesPlaceholder","Describe any behavioral patterns, triggers, or special considerations...")})]}),(0,g.jsxs)("div",{children:[(0,g.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:r("caregiverNotes","Caregiver Notes")}),(0,g.jsx)("textarea",{value:F.caregiverNotes,onChange:e=>O("caregiverNotes",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:r("caregiverNotesPlaceholder","Additional notes from caregivers or family members...")})]}),(0,g.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:r("detailedAssessment","Detailed Special Needs Assessment")}),(0,g.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-300 mt-1",children:S?r("assessmentCompleted","Assessment completed and saved"):r("assessmentRecommended","Complete a detailed assessment for better treatment planning")})]}),(0,g.jsx)("button",{type:"button",onClick:()=>P(!0),className:"px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",children:S?r("editAssessment","Edit Assessment"):r("startAssessment","Start Assessment")})]})}),(0,g.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,g.jsxs)("div",{className:"flex items-center justify-between",children:[(0,g.jsxs)("div",{children:[(0,g.jsx)("h4",{className:"text-sm font-medium text-green-900 dark:text-green-100",children:r("carfAssessment","CARF-Compliant Assessment")}),(0,g.jsx)("p",{className:"text-xs text-green-700 dark:text-green-300 mt-1",children:R?r("carfAssessmentCompleted","CARF assessment completed and saved"):r("carfAssessmentRecommended","Complete CARF-compliant assessment for accreditation compliance")})]}),(0,g.jsx)("button",{type:"button",onClick:()=>M(!0),className:"px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors",children:R?r("editCARFAssessment","Edit CARF Assessment"):r("startCARFAssessment","Start CARF Assessment")})]})})]})]});default:return(0,g.jsxs)("div",{children:["Step content for step ",f]})}})(),(0,g.jsxs)("div",{className:"flex items-center justify-between mt-8 pt-6 border-t border-gray-200 dark:border-gray-600",children:[(0,g.jsxs)("button",{onClick:()=>{k(e=>Math.max(e-1,1))},disabled:1===f,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,g.jsx)("i",{className:"fas fa-arrow-left mr-2"}),r("previous","Previous")]}),(0,g.jsxs)("div",{className:"flex space-x-3",children:[(0,g.jsx)("button",{onClick:()=>p("/patients"),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:r("cancel","Cancel")}),f<q.length?(0,g.jsxs)("button",{onClick:()=>{U(f)?k(e=>Math.min(e+1,q.length)):d.Ay.error(r("pleaseCompleteRequiredFields","Please complete all required fields"))},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[r("next","Next"),(0,g.jsx)("i",{className:"fas fa-arrow-right ml-2"})]}):(0,g.jsx)("button",{onClick:async()=>{if(U(f)){if(!y||!h)return d.Ay.error(r("authenticationRequired","Please log in to create a patient")),void p("/login");if(b("create_patients")){console.log("User authenticated:",h),console.log("User permissions:",h.permissions),console.log("Token available:",!!localStorage.getItem("pt_auth_token")),N(!0);try{console.log("\ud83d\ude80 Starting patient creation..."),console.log("\ud83d\udccb Form data:",F),console.log("\ud83d\udd0d Assessment data:",S),console.log("\ud83d\udcca CARF assessment data:",R);const e={firstName:F.firstName,lastName:F.lastName,nationalId:F.nationalId,dateOfBirth:F.dateOfBirth,gender:F.gender,phone:F.phone,email:F.email,address:{street:F.address,city:F.city,state:F.region,zipCode:F.postalCode,country:F.country||"Saudi Arabia"},emergencyContact:{name:F.emergencyContactName,phone:F.emergencyContactPhone,relationship:F.emergencyContactRelation||F.emergencyContactRelationship},insurance:{provider:F.insuranceProvider,policyNumber:F.insuranceNumber||F.insurancePolicyNumber,groupNumber:F.insuranceGroupNumber},medicalHistory:F.medicalHistory,currentMedications:F.currentMedications,allergies:F.allergies,primaryDiagnosis:F.primaryDiagnosis,referringPhysician:F.referringPhysician,specialNeedsAssessment:S,carfAssessment:R};if(console.log("\ud83d\udce4 Sending patient data to API:",e),console.log("\ud83d\udd17 API endpoint: /patients"),console.log("\ud83d\udd11 Token in localStorage:",localStorage.getItem("pt_auth_token")?"Present":"Missing"),w){const a=await u.mo.put("/patients/".concat(v),e);if(!a.success)throw new Error(a.message||"Failed to update patient");d.Ay.success(r("patientUpdatedSuccessfully","Patient updated successfully")),p("/patients/".concat(v))}else{console.log("\ud83d\udd04 Making POST request to create patient...");const a=await u.mo.post("/patients",e);if(console.log("\ud83d\udce5 API response received:",a),!a.success)throw new Error(a.message||"Failed to create patient");d.Ay.success(r("patientCreatedSuccessfully","Patient created successfully")),p("/patients")}}catch(e){console.error("\u274c Error creating patient:",e),console.error("\u274c Error details:",{message:e.message,response:e.response,request:e.request,config:e.config});let a=r("errorCreatingPatient","Error creating patient");e.response?(console.error("Server Error Response:",e.response.data),console.error("Status Code:",e.response.status),401===e.response.status?a=r("authenticationRequired","Authentication required. Please log in again."):403===e.response.status?a=r("insufficientPermissions","You do not have permission to create patients."):409===e.response.status?a=r("patientAlreadyExists","A patient with this National ID already exists."):e.response.data&&e.response.data.message&&(a=e.response.data.message)):e.request?(console.error("Network Error:",e.request),a=r("networkError","Network error. Please check your connection and try again.")):(console.error("Error Message:",e.message),a=e.message||a),d.Ay.error(a)}finally{N(!1)}}else d.Ay.error(r("insufficientPermissions","You do not have permission to create patients"))}else d.Ay.error(r("pleaseCompleteRequiredFields","Please complete all required fields"))},disabled:j,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:j?(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),w?r("updating","Updating..."):r("creating","Creating...")]}):(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("i",{className:"fas fa-save mr-2"}),w?r("updatePatient","Update Patient"):r("createPatient","Create Patient")]})})]})]})]})]}),A&&(0,g.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,g.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",children:(0,g.jsx)(x,{children:(0,g.jsx)(o.default,{initialData:S||{},onSave:e=>{D(e),P(!1),d.Ay.success(r("assessmentSaved","Assessment saved successfully"))},onCancel:()=>P(!1),patientInfo:{name:"".concat(F.firstName," ").concat(F.lastName).trim()||r("newPatient","New Patient")}})})})}),I&&(0,g.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,g.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto",children:(0,g.jsx)(x,{children:(0,g.jsx)(c.default,{patientId:null,initialData:R||{},onSave:e=>{E(e),M(!1),d.Ay.success(r("carfAssessmentSaved","CARF Assessment saved successfully"))},onCancel:()=>M(!1)})})})})]})}}}]);
//# sourceMappingURL=3553.4fb841aa.chunk.js.map