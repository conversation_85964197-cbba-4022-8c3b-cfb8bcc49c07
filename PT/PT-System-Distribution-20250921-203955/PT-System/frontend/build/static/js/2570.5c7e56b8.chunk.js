"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2570],{2570:(e,a,r)=>{r.r(a),r.d(a,{default:()=>o});var t=r(2555),l=r(5043),s=r(7921),i=r(4528),d=r(3216),n=r(579);const o=()=>{const{t:e,isRTL:a}=(0,s.o)(),{user:r}=(0,i.A)(),{patientId:o}=(0,d.g)(),c=(0,d.Zp)(),[m,g]=(0,l.useState)({documentNumber:"QP-".concat(Date.now()),issueDate:(new Date).toISOString().split("T")[0],version:"01",reviewNumber:"01",patientName:"",mrNumber:"",diagnosis:"",onsetDate:"",physician:"",functionalProblems:[],functionalImpairments:[],functionalActivityRestrictions:[],specificDescriptions:["","","","","",""],shortTermGoals:{weeks:"",goals:[""]},longTermGoals:{weeks:"",goals:[""]},painControl:[],reduceSwelling:[],improveROM:[],improveFlexibility:[],muscleStrengthening:[],posturalCorrection:[],improveBalance:[],improveEndurance:[],gaitTraining:[],homeInstructions:!1,otherTreatments:"",planReviewedWithPatient:!1,therapistSignature:"",therapistBadge:"",therapistDate:(new Date).toISOString().split("T")[0],physicianSignature:"",physicianBadge:"",physicianDate:"",submittedBy:(null===r||void 0===r?void 0:r.id)||"",submittedAt:(new Date).toISOString()}),[b,x]=(0,l.useState)(!1),[u,h]=(0,l.useState)({}),[y,p]=(0,l.useState)(1),v=[{value:"pain",label:e("pain","Pain"),hasLaterality:!1},{value:"bed_mobility",label:e("bedMobility","\u2193 Bed/mat mobility status"),hasLaterality:!1},{value:"transfer_status",label:e("transferStatus","\u2193 Transfer status"),hasLaterality:!1},{value:"ue_rom",label:e("ueROM","Limited UE ROM"),hasLaterality:!0},{value:"le_rom",label:e("leROM","Limited LE ROM"),hasLaterality:!0},{value:"le_strength",label:e("leStrength","\u2193 LE strength"),hasLaterality:!0},{value:"ue_strength",label:e("ueStrength","\u2193 UE strength"),hasLaterality:!0},{value:"neck_trunk_strength",label:e("neckTrunkStrength","\u2193 Neck/trunk strength"),hasLaterality:!1},{value:"abnormal_tone",label:e("abnormalTone","Abnormal tone"),hasLaterality:!1},{value:"abnormal_movement",label:e("abnormalMovement","Abnormal movement"),hasLaterality:!1},{value:"skin_breakdown",label:e("skinBreakdown","Skin breakdown"),hasLaterality:!1},{value:"gait_asymmetry",label:e("gaitAsymmetry","Gait asymmetry altered Gait pattern"),hasLaterality:!1}],k=[{value:"atrophy",label:e("atrophy","Atrophy")},{value:"muscle_weakness",label:e("muscleWeakness","Muscle weakness")},{value:"imbalance",label:e("imbalance","Imbalance/poor balance")},{value:"lack_coordination",label:e("lackCoordination","Lack of coordination")},{value:"visual_perception",label:e("visualPerception","\u2193 Visual perception")},{value:"soft_tissue_dysfunction",label:e("softTissueDysfunction","Soft tissue dysfunction")},{value:"poor_posture",label:e("poorPosture","Poor posture/abnormal posture")},{value:"improper_body_mechanics",label:e("improperBodyMechanics","Improper body mechanics")},{value:"wc_mobility",label:e("wcMobility","\u2193 W/C mobility")},{value:"difficulty_ambulating",label:e("difficultyAmbulating","Difficulty ambulating")},{value:"abnormal_gait",label:e("abnormalGait","Abnormal gait")}],f=[{value:"endurance",label:e("endurance","\u2193 Endurance")},{value:"sensation",label:e("sensation","\u2193 Sensation/proprioception")},{value:"respiratory",label:e("respiratory","\u2193 Respiratory capacity")},{value:"fine_motor",label:e("fineMotor","\u2193 Fine motor/dexterity")},{value:"functional_activity",label:e("functionalActivity","Decreased functional activity: ADL/work skills")},{value:"joint_hypomobility",label:e("jointHypomobility","Joint hypomobility")},{value:"joint_hypermobility",label:e("jointHypermobility","Joint hypermobility")},{value:"contracture",label:e("contracture","Contracture")},{value:"other",label:e("other","Other")}],j={painControl:[{value:"us",label:e("ultrasound","US")},{value:"laser",label:e("laser","LASER")},{value:"tens",label:e("tens","TENS")},{value:"thermal",label:e("thermal","Thermal")}],reduceSwelling:[{value:"cryotherapy",label:e("cryotherapy","Cryotherapy")},{value:"hvc",label:e("hvc","HVC")},{value:"compression",label:e("compression","Compression")}],improveROM:[{value:"prom",label:e("prom","PROM")},{value:"mobilization",label:e("mobilization","Mobilization")},{value:"met",label:e("met","MET")}],improveFlexibility:[{value:"stretching",label:e("stretching","Stretching")},{value:"thermal_flex",label:e("thermal","Thermal")},{value:"myofascial",label:e("myofascial","Myofascial release")}],muscleStrengthening:[{value:"isometric",label:e("isometric","Isometric")},{value:"active_assisted",label:e("activeAssisted","Active Assisted")},{value:"active_resisted",label:e("activeResisted","Active Resisted")},{value:"core_strengthening",label:e("coreStrengthening","Core Strengthening")},{value:"plyometrics",label:e("plyometrics","Plyometrics")},{value:"fes",label:e("fes","FES")},{value:"pnf",label:e("pnf","PNF")}],posturalCorrection:[{value:"body_mechanics",label:e("bodyMechanics","Proper Body Mechanics")},{value:"ergonomics",label:e("ergonomics","Ergonomics")},{value:"tilt_table",label:e("tiltTable","Tilt table")}],improveBalance:[{value:"frenkels",label:e("frenkels","Frenkel's Ex")},{value:"balance_board",label:e("balanceBoard","Balance Board")},{value:"agility",label:e("agility","Agility Ex's")},{value:"proprioception",label:e("proprioception","Proprioception Training")},{value:"lumbopelvic",label:e("lumbopelvic","Lumbopelvic Rhythm")}],improveEndurance:[{value:"aerobic",label:e("aerobic","Aerobic Ex's")},{value:"bicycle",label:e("bicycle","Bicycle")},{value:"treadmill",label:e("treadmill","Treadmill")}],gaitTraining:[{value:"normal_gait",label:e("normalGait","Normal Gait Pattern")},{value:"fwb",label:e("fwb","Weight Bearing: FWB")},{value:"pwb",label:e("pwb","PWB")},{value:"wb",label:e("wb","WB")}]};(0,l.useEffect)(()=>{o&&N()},[o]);const N=async()=>{try{x(!0);const e=await fetch("/api/patients/".concat(o));if(e.ok){const a=await e.json();g(e=>(0,t.A)((0,t.A)({},e),{},{patientName:a.name||"",mrNumber:a.mrNumber||"",diagnosis:a.diagnosis||""}))}}catch(e){console.error("Error loading patient data:",e)}finally{x(!1)}},w=(e,a)=>{g(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a})),u[e]&&h(a=>(0,t.A)((0,t.A)({},a),{},{[e]:null}))},T=(e,a,r)=>{g(l=>(0,t.A)((0,t.A)({},l),{},{[e]:r?[...l[e],a]:l[e].filter(e=>e!==a)}))},A=(e,a,r)=>{g(l=>(0,t.A)((0,t.A)({},l),{},{[e]:(0,t.A)((0,t.A)({},l[e]),{},{goals:l[e].goals.map((e,t)=>t===a?r:e)})}))},P=e=>{g(a=>(0,t.A)((0,t.A)({},a),{},{[e]:(0,t.A)((0,t.A)({},a[e]),{},{goals:[...a[e].goals,""]})}))},S=(e,a)=>{g(r=>(0,t.A)((0,t.A)({},r),{},{[e]:(0,t.A)((0,t.A)({},r[e]),{},{goals:r[e].goals.filter((e,r)=>r!==a)})}))};return b?(0,n.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:(0,n.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("initialPlanOfCare","Initial Plan of Care Physical Therapy")}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e("planOfCareDescription","2-page comprehensive treatment planning document")})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsxs)("span",{className:"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm rounded-full",children:[(0,n.jsx)("i",{className:"fas fa-clipboard-list mr-1"}),e("planOfCare","Plan of Care")]}),(0,n.jsxs)("span",{className:"px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 text-sm rounded-full",children:[e("page","Page")," ",y,"/2"]})]})]})})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:(0,n.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,n.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:[{id:1,label:e("patientProblemsGoals","Patient Problems & Goals")},{id:2,label:e("treatmentPlanSignatures","Treatment Plan & Signatures")}].map(e=>(0,n.jsx)("button",{onClick:()=>p(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(y===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:e.label},e.id))})})}),(0,n.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),(()=>{const a={};return m.patientName.trim()||(a.patientName=e("patientNameRequired","Patient name is required")),m.mrNumber.trim()||(a.mrNumber=e("mrNumberRequired","MR number is required")),m.diagnosis.trim()||(a.diagnosis=e("diagnosisRequired","Diagnosis is required")),m.onsetDate||(a.onsetDate=e("onsetDateRequired","Onset date is required")),m.physician.trim()||(a.physician=e("physicianRequired","Physician is required")),m.shortTermGoals.weeks||(a.shortTermWeeks=e("shortTermWeeksRequired","Short term weeks required")),m.shortTermGoals.goals.every(e=>!e.trim())&&(a.shortTermGoals=e("shortTermGoalsRequired","At least one short term goal is required")),m.longTermGoals.weeks||(a.longTermWeeks=e("longTermWeeksRequired","Long term weeks required")),m.longTermGoals.goals.every(e=>!e.trim())&&(a.longTermGoals=e("longTermGoalsRequired","At least one long term goal is required")),m.therapistSignature.trim()||(a.therapistSignature=e("therapistSignatureRequired","Therapist signature is required")),m.therapistBadge.trim()||(a.therapistBadge=e("therapistBadgeRequired","Therapist badge number is required")),h(a),0===Object.keys(a).length})())try{x(!0);const a=(0,t.A)((0,t.A)({},m),{},{submittedBy:r.id,submittedAt:(new Date).toISOString()});if(!(await fetch("/api/v1/initial-plan-of-care/public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok)throw new Error("Failed to save plan of care");alert(e("planOfCareSaved","Initial Plan of Care saved successfully!")),c(o?"/patients/".concat(o):"/patients")}catch(l){console.error("Error saving plan of care:",l),alert(e("errorSaving","Error saving plan of care. Please try again."))}finally{x(!1)}},className:"space-y-6",children:[1===y&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("documentNumber","Document Number")}),(0,n.jsx)("input",{type:"text",value:m.documentNumber,onChange:e=>w("documentNumber",e.target.value),className:"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",readOnly:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("issueDate","Issue Date")}),(0,n.jsx)("input",{type:"date",value:m.issueDate,onChange:e=>w("issueDate",e.target.value),className:"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("version","Version")}),(0,n.jsx)("input",{type:"text",value:m.version,className:"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white",readOnly:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("reviewNumber","Review Number")}),(0,n.jsx)("input",{type:"text",value:m.reviewNumber,className:"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white",readOnly:!0})]})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("patientInformation","Patient Information")}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("patientName","Patient Name")," *"]}),(0,n.jsx)("input",{type:"text",value:m.patientName,onChange:e=>w("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.patientName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.patientName&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.patientName})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("mrNumber","MR #")," *"]}),(0,n.jsx)("input",{type:"text",value:m.mrNumber,onChange:e=>w("mrNumber",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.mrNumber?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.mrNumber&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.mrNumber})]}),(0,n.jsxs)("div",{className:"md:col-span-2",children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("diagnosis","Diagnosis")," *"]}),(0,n.jsx)("textarea",{value:m.diagnosis,onChange:e=>w("diagnosis",e.target.value),rows:"3",className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.diagnosis?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.diagnosis&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.diagnosis})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("onsetDate","Onset Date")," *"]}),(0,n.jsx)("input",{type:"date",value:m.onsetDate,onChange:e=>w("onsetDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.onsetDate?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.onsetDate&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.onsetDate})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("physician","Physician")," *"]}),(0,n.jsx)("input",{type:"text",value:m.physician,onChange:e=>w("physician",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.physician?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.physician&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.physician})]})]})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("patientProblems","Patient Problems (Reason for Referral)")}),(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[(0,n.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white",children:e("functionalProblems","Functional Problems")}),(0,n.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white",children:e("functionalImpairments","Functional Impairments")}),(0,n.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white",children:e("functionalActivityRestrictions","Functional Activity Restrictions")}),(0,n.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white",children:e("specificDescription","Specific Description / Objective Measure")})]})}),(0,n.jsx)("tbody",{children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top",children:(0,n.jsx)("div",{className:"space-y-2",children:v.map(e=>(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("input",{type:"checkbox",id:"fp-".concat(e.value),checked:m.functionalProblems.some(a=>a.value===e.value),onChange:a=>{a.target.checked?T("functionalProblems",{value:e.value,label:e.label,laterality:e.hasLaterality?"B":null},!0):g(a=>(0,t.A)((0,t.A)({},a),{},{functionalProblems:a.functionalProblems.filter(a=>a.value!==e.value)}))},className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,n.jsx)("label",{htmlFor:"fp-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label}),e.hasLaterality&&m.functionalProblems.some(a=>a.value===e.value)&&(0,n.jsx)("div",{className:"ml-4 flex space-x-2",children:["R","L","B"].map(a=>{var r;return(0,n.jsxs)("label",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"radio",name:"laterality-".concat(e.value),value:a,checked:(null===(r=m.functionalProblems.find(a=>a.value===e.value))||void 0===r?void 0:r.laterality)===a,onChange:a=>{g(r=>(0,t.A)((0,t.A)({},r),{},{functionalProblems:r.functionalProblems.map(r=>r.value===e.value?(0,t.A)((0,t.A)({},r),{},{laterality:a.target.value}):r)}))},className:"mr-1 h-3 w-3 text-blue-600"}),(0,n.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:a})]},a)})})]},e.value))})}),(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top",children:(0,n.jsx)("div",{className:"space-y-2",children:k.map(e=>(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("input",{type:"checkbox",id:"fi-".concat(e.value),checked:m.functionalImpairments.includes(e.value),onChange:a=>T("functionalImpairments",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,n.jsx)("label",{htmlFor:"fi-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value))})}),(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top",children:(0,n.jsxs)("div",{className:"space-y-2",children:[f.map(e=>(0,n.jsxs)("div",{className:"flex items-start",children:[(0,n.jsx)("input",{type:"checkbox",id:"far-".concat(e.value),checked:m.functionalActivityRestrictions.includes(e.value),onChange:a=>T("functionalActivityRestrictions",e.value,a.target.checked),className:"mt-1 mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,n.jsx)("label",{htmlFor:"far-".concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)),m.functionalActivityRestrictions.includes("other")&&(0,n.jsx)("textarea",{placeholder:e("specifyOther","Specify other..."),value:m.otherActivityRestriction||"",onChange:e=>w("otherActivityRestriction",e.target.value),className:"w-full mt-2 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})]})}),(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top",children:(0,n.jsx)("div",{className:"space-y-3",children:[1,2,3,4,5,6].map(a=>(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1",children:[a,"."]}),(0,n.jsx)("textarea",{value:m.specificDescriptions[a-1],onChange:e=>{const r=[...m.specificDescriptions];r[a-1]=e.target.value,w("specificDescriptions",r)},className:"w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2",placeholder:e("objectiveMeasure","Objective measure or description...")})]},a))})})]})})]})})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("goalsOfTreatment","Goals of Treatment")}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("shortTermGoals","Short Term Goals")}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("weeks","Weeks")," *"]}),(0,n.jsx)("input",{type:"number",value:m.shortTermGoals.weeks,onChange:e=>g(a=>(0,t.A)((0,t.A)({},a),{},{shortTermGoals:(0,t.A)((0,t.A)({},a.shortTermGoals),{},{weeks:e.target.value})})),min:"1",max:"52",className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.shortTermWeeks?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.shortTermWeeks&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.shortTermWeeks})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[m.shortTermGoals.goals.map((a,r)=>(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mt-2",children:[r+1,"."]}),(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)("textarea",{value:a,onChange:e=>A("shortTermGoals",r,e.target.value),placeholder:e("patientWill","Patient will..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})}),m.shortTermGoals.goals.length>1&&(0,n.jsx)("button",{type:"button",onClick:()=>S("shortTermGoals",r),className:"mt-2 text-red-600 hover:text-red-800 dark:text-red-400",children:(0,n.jsx)("i",{className:"fas fa-trash text-sm"})})]},r)),(0,n.jsxs)("button",{type:"button",onClick:()=>P("shortTermGoals"),className:"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm",children:[(0,n.jsx)("i",{className:"fas fa-plus mr-1"}),e("addGoal","Add Goal")]}),u.shortTermGoals&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:u.shortTermGoals})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("longTermGoals","Long Term Goals")}),(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("weeks","Weeks")," *"]}),(0,n.jsx)("input",{type:"number",value:m.longTermGoals.weeks,onChange:e=>g(a=>(0,t.A)((0,t.A)({},a),{},{longTermGoals:(0,t.A)((0,t.A)({},a.longTermGoals),{},{weeks:e.target.value})})),min:"1",max:"52",className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.longTermWeeks?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.longTermWeeks&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.longTermWeeks})]}),(0,n.jsxs)("div",{className:"space-y-3",children:[m.longTermGoals.goals.map((a,r)=>(0,n.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,n.jsxs)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mt-2",children:[r+1,"."]}),(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)("textarea",{value:a,onChange:e=>A("longTermGoals",r,e.target.value),placeholder:e("patientWill","Patient will..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})}),m.longTermGoals.goals.length>1&&(0,n.jsx)("button",{type:"button",onClick:()=>S("longTermGoals",r),className:"mt-2 text-red-600 hover:text-red-800 dark:text-red-400",children:(0,n.jsx)("i",{className:"fas fa-trash text-sm"})})]},r)),(0,n.jsxs)("button",{type:"button",onClick:()=>P("longTermGoals"),className:"flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 text-sm",children:[(0,n.jsx)("i",{className:"fas fa-plus mr-1"}),e("addGoal","Add Goal")]}),u.longTermGoals&&(0,n.jsx)("p",{className:"text-red-500 text-sm",children:u.longTermGoals})]})]})]})]})]}),2===y&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("treatmentPlan","Treatment Plan")}),(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full border-collapse border border-gray-300 dark:border-gray-600",children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{className:"bg-gray-50 dark:bg-gray-700",children:[(0,n.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white",children:e("treatmentCategory","Treatment Category")}),(0,n.jsx)("th",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-white",children:e("specificTreatments","Specific Treatments")})]})}),(0,n.jsxs)("tbody",{children:[Object.entries(j).map(a=>{let[r,t]=a;return(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top font-medium text-gray-900 dark:text-white",children:e(r,r.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()))}),(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3",children:(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:t.map(e=>{var a;return(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",id:"".concat(r,"-").concat(e.value),checked:(null===(a=m[r])||void 0===a?void 0:a.includes(e.value))||!1,onChange:a=>T(r,e.value,a.target.checked),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,n.jsx)("label",{htmlFor:"".concat(r,"-").concat(e.value),className:"text-sm text-gray-700 dark:text-gray-300",children:e.label})]},e.value)})})})]},r)}),(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top font-medium text-gray-900 dark:text-white",children:e("homeInstructions","Home Instructions")}),(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",id:"homeInstructions",checked:m.homeInstructions,onChange:e=>w("homeInstructions",e.target.checked),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,n.jsx)("label",{htmlFor:"homeInstructions",className:"text-sm text-gray-700 dark:text-gray-300",children:e("provideHomeInstructions","Provide home exercise instructions")})]})})]}),(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3 align-top font-medium text-gray-900 dark:text-white",children:e("others","Others")}),(0,n.jsx)("td",{className:"border border-gray-300 dark:border-gray-600 px-4 py-3",children:(0,n.jsx)("textarea",{value:m.otherTreatments,onChange:e=>w("otherTreatments",e.target.value),placeholder:e("specifyOtherTreatments","Specify other treatments..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"3"})})]})]})]})})]}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",id:"planReviewed",checked:m.planReviewedWithPatient,onChange:e=>w("planReviewedWithPatient",e.target.checked),className:"mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,n.jsx)("label",{htmlFor:"planReviewed",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e("planReviewedWithPatient","Plan of Care Reviewed with Patient")})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("signatures","Signatures")}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("therapistSignature","Therapist Signature")}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("signature","Signature")," *"]}),(0,n.jsx)("input",{type:"text",value:m.therapistSignature,onChange:e=>w("therapistSignature",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.therapistSignature?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.therapistSignature&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.therapistSignature})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("badgeNumber","Badge No.")," *"]}),(0,n.jsx)("input",{type:"text",value:m.therapistBadge,onChange:e=>w("therapistBadge",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.therapistBadge?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.therapistBadge&&(0,n.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.therapistBadge})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("date","Date")}),(0,n.jsx)("input",{type:"date",value:m.therapistDate,onChange:e=>w("therapistDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("physicianReview","Physician Review")}),(0,n.jsx)("div",{className:"mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:(0,n.jsx)("p",{className:"text-sm text-gray-700 dark:text-gray-300 italic",children:e("physicianReviewText","Have reviewed this plan of care and re-certify a continuing need for services.")})}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("physicianSignature","Physician Signature")}),(0,n.jsx)("input",{type:"text",value:m.physicianSignature,onChange:e=>w("physicianSignature",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("badgeNumber","Badge No.")}),(0,n.jsx)("input",{type:"text",value:m.physicianBadge,onChange:e=>w("physicianBadge",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("date","Date")}),(0,n.jsx)("input",{type:"date",value:m.physicianDate,onChange:e=>w("physicianDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("button",{type:"button",onClick:()=>c(-1),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700",children:e("cancel","Cancel")}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[y>1&&(0,n.jsx)("button",{type:"button",onClick:()=>p(y-1),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700",children:e("previous","Previous")}),y<2?(0,n.jsx)("button",{type:"button",onClick:()=>p(y+1),className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:e("next","Next")}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("button",{type:"button",onClick:async()=>{try{x(!0);const a=(0,t.A)((0,t.A)({},m),{},{generatedAt:(new Date).toISOString(),generatedBy:r.name||r.email,patientId:o}),l=await fetch("/api/v1/initial-plan-of-care/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(a)});if(!l.ok)throw new Error("HTTP error! status: ".concat(l.status));{const a=await l.blob(),r=window.URL.createObjectURL(a),t=document.createElement("a");t.href=r,t.download="initial-plan-of-care-".concat(m.patientName.replace(/\s+/g,"-"),"-").concat(m.issueDate,".pdf"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(r),document.body.removeChild(t),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(a){console.error("Error generating PDF:",a),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{x(!1)}},disabled:b,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-file-pdf mr-2"}),b?e("generating","Generating..."):e("generatePDF","Generate PDF")]}),(0,n.jsx)("button",{type:"submit",disabled:b,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?e("saving","Saving..."):e("savePlanOfCare","Save Plan of Care")})]})]})]})]})]})}}}]);
//# sourceMappingURL=2570.5c7e56b8.chunk.js.map