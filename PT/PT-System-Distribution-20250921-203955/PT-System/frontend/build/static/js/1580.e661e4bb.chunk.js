"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1580],{1580:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var s=a(2555),r=a(5043),i=a(7921),d=a(3768),l=a(579);const c=()=>{const{t:e,isRTL:t}=(0,i.o)(),[a,c]=(0,r.useState)("eligibility"),[n,x]=(0,r.useState)(!1),[o,m]=(0,r.useState)([{id:"ELG-2024-001",patientName:"Ahmed Al-Rashid",nationalId:"**********",insuranceProvider:"Bupa Arabia",policyNumber:"BUPA-12345",checkDate:"2024-02-15",status:"Active",coverageType:"Comprehensive",deductible:500,copayPercentage:20,maxCoverage:5e4,usedCoverage:12e3,remainingCoverage:38e3,validUntil:"2024-12-31"},{id:"ELG-2024-002",patientName:"Fatima Al-Zahra",nationalId:"**********",insuranceProvider:"Tawuniya",policyNumber:"TAW-67890",checkDate:"2024-02-14",status:"Active",coverageType:"Basic",deductible:300,copayPercentage:15,maxCoverage:3e4,usedCoverage:8500,remainingCoverage:21500,validUntil:"2024-11-30"}]),[g,p]=(0,r.useState)([{id:"CLM-NPHIES-001",patientName:"Ahmed Al-Rashid",nationalId:"**********",claimNumber:"NPHIES-2024-001",submissionDate:"2024-02-15",serviceDate:"2024-02-10",provider:"PT Clinic",serviceType:"Physical Therapy",amount:1500,status:"Approved",approvedAmount:1200,rejectionReason:null,processingTime:"2 days",nphiesReference:"NPH-REF-12345"},{id:"CLM-NPHIES-002",patientName:"Fatima Al-Zahra",nationalId:"**********",claimNumber:"NPHIES-2024-002",submissionDate:"2024-02-14",serviceDate:"2024-02-08",provider:"PT Clinic",serviceType:"Occupational Therapy",amount:2e3,status:"Pending",approvedAmount:0,rejectionReason:null,processingTime:"In Progress",nphiesReference:"NPH-REF-67890"},{id:"CLM-NPHIES-003",patientName:"Mohammed Al-Otaibi",nationalId:"**********",claimNumber:"NPHIES-2024-003",submissionDate:"2024-02-13",serviceDate:"2024-02-05",provider:"PT Clinic",serviceType:"Speech Therapy",amount:1200,status:"Rejected",approvedAmount:0,rejectionReason:"Service not covered under current policy",processingTime:"1 day",nphiesReference:"NPH-REF-11223"}]),[h,y]=(0,r.useState)([{id:"COMP-2024-001",reportType:"Monthly Claims Report",period:"February 2024",generatedDate:"2024-02-28",status:"Submitted",totalClaims:45,approvedClaims:38,rejectedClaims:4,pendingClaims:3,totalAmount:67500,approvedAmount:54e3,complianceScore:95},{id:"COMP-2024-002",reportType:"Provider Performance Report",period:"Q1 2024",generatedDate:"2024-03-31",status:"Draft",totalClaims:134,approvedClaims:118,rejectedClaims:12,pendingClaims:4,totalAmount:201e3,approvedAmount:175500,complianceScore:92}]),u=[{id:"eligibility",label:e("eligibilityVerification","Eligibility Verification"),icon:"fas fa-check-circle"},{id:"claims",label:e("claimsSubmission","Claims Submission"),icon:"fas fa-file-medical"},{id:"compliance",label:e("complianceReporting","Compliance Reporting"),icon:"fas fa-clipboard-check"},{id:"settings",label:e("nphiesSettings","NPHIES Settings"),icon:"fas fa-cog"}],b=e=>new Intl.NumberFormat("ar-SA",{style:"currency",currency:"SAR"}).format(e),N=e=>{switch(e.toLowerCase()){case"active":case"approved":return"text-green-600 bg-green-100 dark:bg-green-900/30";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30";case"rejected":return"text-red-600 bg-red-100 dark:bg-red-900/30";case"submitted":return"text-blue-600 bg-blue-100 dark:bg-blue-900/30";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30"}};return(0,l.jsxs)("div",{className:"space-y-6 ".concat(t?"font-arabic":"font-english"),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("nphiesIntegration","NPHIES Integration")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("nphiesDescription","Saudi National Platform for Health Information Exchange integration")})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)("div",{className:"flex items-center px-3 py-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),(0,l.jsx)("span",{className:"text-sm text-green-700 dark:text-green-300",children:e("connected","Connected")})]}),(0,l.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-sync mr-2"}),e("syncData","Sync Data")]})]})]}),(0,l.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,l.jsx)("nav",{className:"-mb-px flex space-x-8",children:u.map(e=>(0,l.jsxs)("button",{onClick:()=>c(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(a===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,l.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})}),"eligibility"===a&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalChecks","Total Checks")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:o.length})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-users text-blue-600 dark:text-blue-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("activePatients","Active Patients")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:o.filter(e=>"Active"===e.status).length})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-dollar-sign text-purple-600 dark:text-purple-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalCoverage","Total Coverage")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b(o.reduce((e,t)=>e+t.remainingCoverage,0))})]})]})})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("newEligibilityCheck","New Eligibility Check")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsx)("input",{type:"text",placeholder:e("nationalId","National ID"),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,l.jsx)("input",{type:"text",placeholder:e("policyNumber","Policy Number"),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,l.jsxs)("button",{onClick:()=>(async t=>{x(!0);try{await new Promise(e=>setTimeout(e,3e3));const a=(0,s.A)((0,s.A)({id:"ELG-2024-".concat(String(o.length+1).padStart(3,"0"))},t),{},{checkDate:(new Date).toISOString().split("T")[0],status:"Active",coverageType:"Comprehensive",deductible:500,copayPercentage:20,maxCoverage:5e4,usedCoverage:0,remainingCoverage:5e4,validUntil:"2024-12-31"});m(e=>[a,...e]),d.Ay.success(e("eligibilityCheckCompleted","Eligibility check completed successfully"))}catch(a){d.Ay.error(e("eligibilityCheckFailed","Eligibility check failed"))}finally{x(!1)}})({}),disabled:n,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[n?(0,l.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}):(0,l.jsx)("i",{className:"fas fa-search mr-2"}),e("checkEligibility","Check Eligibility")]})]})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("eligibilityResults","Eligibility Results")})}),(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("insurance","Insurance")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("coverage","Coverage")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("remaining","Remaining")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,l.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:o.map(t=>(0,l.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.patientName}),(0,l.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",t.nationalId]})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:t.insuranceProvider}),(0,l.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.policyNumber})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:b(t.maxCoverage)}),(0,l.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.coverageType})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b(t.remainingCoverage)}),(0,l.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[Math.round(t.remainingCoverage/t.maxCoverage*100),"% ",e("available","available")]})]}),(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,l.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(N(t.status)),children:t.status})}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,l.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3",children:e("viewDetails","View Details")}),(0,l.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",children:e("refresh","Refresh")})]})]},t.id))})]})})]})]}),"claims"===a&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-file-medical text-blue-600 dark:text-blue-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalClaims","Total Claims")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.length})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("approvedClaims","Approved")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.filter(e=>"Approved"===e.status).length})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("pendingClaims","Pending")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.filter(e=>"Pending"===e.status).length})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-times-circle text-red-600 dark:text-red-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("rejectedClaims","Rejected")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.filter(e=>"Rejected"===e.status).length})]})]})})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("submitNewClaim","Submit New Claim")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,l.jsx)("input",{type:"text",placeholder:e("patientName","Patient Name"),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,l.jsx)("input",{type:"text",placeholder:e("serviceType","Service Type"),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,l.jsx)("input",{type:"number",placeholder:e("amount","Amount"),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,l.jsxs)("button",{onClick:()=>(async t=>{x(!0);try{await new Promise(e=>setTimeout(e,2e3));const a=(0,s.A)((0,s.A)({id:"CLM-NPHIES-".concat(String(g.length+1).padStart(3,"0")),claimNumber:"NPHIES-2024-".concat(String(g.length+1).padStart(3,"0"))},t),{},{submissionDate:(new Date).toISOString().split("T")[0],status:"Pending",approvedAmount:0,rejectionReason:null,processingTime:"In Progress",nphiesReference:"NPH-REF-".concat(Math.random().toString(36).substr(2,9))});p(e=>[a,...e]),d.Ay.success(e("claimSubmittedSuccessfully","Claim submitted to NPHIES successfully"))}catch(a){d.Ay.error(e("claimSubmissionFailed","Claim submission failed"))}finally{x(!1)}})({}),disabled:n,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",children:[n?(0,l.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}):(0,l.jsx)("i",{className:"fas fa-paper-plane mr-2"}),e("submitClaim","Submit Claim")]})]})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("claimsHistory","Claims History")})}),(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("claimNumber","Claim Number")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("service","Service")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("amount","Amount")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,l.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:g.map(t=>(0,l.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t.claimNumber}),(0,l.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.nphiesReference})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:t.patientName}),(0,l.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",t.nationalId]})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:t.serviceType}),(0,l.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:t.serviceDate})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b(t.amount)}),t.approvedAmount>0&&(0,l.jsxs)("div",{className:"text-sm text-green-600 dark:text-green-400",children:[b(t.approvedAmount)," ",e("approved","approved")]})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(N(t.status)),children:t.status}),t.rejectionReason&&(0,l.jsx)("div",{className:"text-xs text-red-600 dark:text-red-400 mt-1",children:t.rejectionReason})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,l.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3",children:e("viewDetails","View Details")}),"Pending"===t.status&&(0,l.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",children:e("track","Track")})]})]},t.id))})]})})]})]}),"compliance"===a&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("complianceReporting","Compliance Reporting")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("complianceDescription","Automated compliance reporting and regulatory submissions to NPHIES")})]}),"settings"===a&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("nphiesSettings","NPHIES Settings")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("settingsDescription","Configure NPHIES connection, API credentials, and integration settings")})]})]})}}}]);
//# sourceMappingURL=1580.e661e4bb.chunk.js.map