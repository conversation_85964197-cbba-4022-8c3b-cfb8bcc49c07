{"version": 3, "file": "static/js/2866.5f65dba1.chunk.js", "mappings": "qVAsBA,MA8sBA,EA9sB2BA,KACzB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACPC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,cACpCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAaC,IAAkBJ,EAAAA,EAAAA,UAAS,KACxCK,EAAaC,IAAkBN,EAAAA,EAAAA,UAAS,KAE/CO,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACV,IAEJ,MAAMU,EAAqBC,UACzBP,GAAW,GACX,IAEE,MAAMQ,QAAwBC,MAAM,wBAAyB,CAC3DC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,UAChD,eAAgB,sBAKdC,QAAyBL,MAAM,yBAA0B,CAC7DC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIL,EAAgBO,KAAMD,EAAiBC,GA6BzC,MAAM,IAAIC,MAAM,kCA7B6B,CAC7C,MAAMC,QAAoBT,EAAgBU,OAKpCC,SAJqBL,EAAiBI,QAGrBD,EAAYG,KAAKC,gBAAkB,IACdC,IAAIC,IAAI,CAClDC,MAAOD,EAAKC,MACZC,QAASF,EAAKE,QACdC,KAAMH,EAAKG,KACXC,OAAoB,GAAZJ,EAAKG,KACbE,SAAuB,EAAbL,EAAKM,UAGjB3B,EAAeiB,GAaff,EAVwB,CACtB,CAAE0B,SAAU,WAAYC,OAAQ,KAAOC,WAAY,IACnD,CAAEF,SAAU,YAAaC,OAAQ,IAAMC,WAAY,IACnD,CAAEF,SAAU,OAAQC,OAAQ,IAAMC,WAAY,IAC9C,CAAEF,SAAU,YAAaC,OAAQ,IAAMC,WAAY,GACnD,CAAEF,SAAU,WAAYC,OAAQ,IAAMC,WAAY,GAClD,CAAEF,SAAU,YAAaC,OAAQ,IAAMC,WAAY,GACnD,CAAEF,SAAU,QAASC,OAAQ,IAAMC,WAAY,KAInD,CAGF,CAAE,MAAOC,GACPC,QAAQD,MAAM,iCAAkCA,EAClD,CAAC,QACCjC,GAAW,EACb,GAIImC,EACU,MADVA,EAEW,KAFXA,EAGO,IAHPA,EAIiB,KAJjBA,EAOW,EAPXA,EAQY,GARZA,EASY,EAoCZC,EAAe,CACnB,CACEC,MAAO3C,EAAE,iBAAkB,mBAC3B4C,KAAM,qBACNC,MAAO,OACPC,KAAM,gBACNC,YAAa/C,EAAE,oBAAqB,kCAEtC,CACE2C,MAAO3C,EAAE,cAAe,0BACxB4C,KAAM,sBACNC,MAAO,QACPC,KAAM,wBACNC,YAAa/C,EAAE,iBAAkB,iCAEnC,CACE2C,MAAO3C,EAAE,kBAAmB,oBAC5B4C,KAAM,iBACNC,MAAO,SACPC,KAAM,uBACNC,YAAa/C,EAAE,mBAAoB,iCAErC,CACE2C,MAAO3C,EAAE,cAAe,qBACxB4C,KAAM,oBACNC,MAAO,SACPC,KAAM,qBACNC,YAAa/C,EAAE,uBAAwB,qCAIrCgD,EAAkBC,IACtB,OAAQA,GACN,IAAK,YAAa,MAAO,8BACzB,IAAK,UAAW,MAAO,gCACvB,IAAK,SAAU,MAAO,0BACtB,QAAS,MAAO,8BAIdC,EAAkBb,GACf,IAAIc,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,QACTC,OAAOlB,GAGZ,OACEmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D1D,EAAE,qBAAsB,0BAE3B2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD1D,EAAE,oBAAqB,yDAI5B2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CF,EAAAA,EAAAA,MAAA,UACEI,MAAO1D,EACP2D,SAAWC,GAAM3D,EAAa2D,EAAEC,OAAOH,OACvCH,UAAU,2HAA0HC,SAAA,EAEpIC,EAAAA,EAAAA,KAAA,UAAQC,MAAM,WAAUF,SAAE1D,EAAE,WAAY,gBACxC2D,EAAAA,EAAAA,KAAA,UAAQC,MAAM,YAAWF,SAAE1D,EAAE,YAAa,iBAC1C2D,EAAAA,EAAAA,KAAA,UAAQC,MAAM,cAAaF,SAAE1D,EAAE,cAAe,mBAC9C2D,EAAAA,EAAAA,KAAA,UAAQC,MAAM,WAAUF,SAAE1D,EAAE,WAAY,wBAM9CwD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,eAAgB,oBAErBwD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,uBACHR,UAAU,gIAA+HC,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,UAClEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4DAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtD1D,EAAE,mBAAoB,wBAEzB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD1D,EAAE,kBAAmB,6BAK5BwD,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,6BACHR,UAAU,oIAAmIC,SAAA,EAE7IC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wDAAuDC,UACpEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtD1D,EAAE,gBAAiB,qBAEtB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD1D,EAAE,aAAc,wBAKvBwD,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,sBACHR,UAAU,wIAAuIC,SAAA,EAEjJC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtD1D,EAAE,eAAgB,oBAErB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD1D,EAAE,cAAe,yBAKxBwD,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,gBACHR,UAAU,wIAAuIC,SAAA,EAEjJC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtD1D,EAAE,eAAgB,yBAErB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD1D,EAAE,cAAe,8BAO1BwD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yGAAwGC,SAAA,EACrHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gEAA+DC,SAAC,kCAG9EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAACK,EAAAA,GAAI,CACHC,GAAG,4BACHR,UAAU,8EAA6EC,SACxF,wBAGDC,EAAAA,EAAAA,KAACK,EAAAA,GAAI,CACHC,GAAG,uBACHR,UAAU,wEAAuEC,SAClF,2BAQPF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAChE1D,EAAE,eAAgB,oBAErB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAC5DR,EAAeT,MAElBe,EAAAA,EAAAA,MAAA,KAAGC,UAAU,yBAAwBC,SAAA,CAAC,QAAM1D,EAAE,gBAAiB,+BAKrE2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+CAA8CC,UAC3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAChE1D,EAAE,gBAAiB,qBAEtB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAC5DR,EAAeT,MAElBe,EAAAA,EAAAA,MAAA,KAAGC,UAAU,uBAAsBC,SAAA,CAAC,OAAK1D,EAAE,gBAAiB,+BAKlE2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAChE1D,EAAE,YAAa,iBAElB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAC5DR,EAAeT,MAElBe,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wBAAuBC,SAAA,CAAC,QAAM1D,EAAE,gBAAiB,+BAKpE2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAChE1D,EAAE,sBAAuB,kBAE5B2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDAAkDC,SAC5DR,EAAeT,MAElBkB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BAAyBC,SAAE1D,EAAE,oBAAqB,oCAOvEwD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE1D,EAAE,eAAgB,sBAGvB2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,UAClBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEhB,EAAad,IAAI,CAACsC,EAAQC,KACzBR,EAAAA,EAAAA,KAACK,EAAAA,GAAI,CAEHC,GAAIC,EAAOpB,KACXW,UAAS,2EAAAxC,OAA6EiD,EAAOrB,MAAK,kBAAA5B,OAAiBiD,EAAOrB,MAAK,sBAAA5B,OAAqBiD,EAAOrB,MAAK,mCAAkCa,UAElMF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,UAAAxC,OAAYiD,EAAOrB,MAAK,iBAAA5B,OAAgBiD,EAAOrB,MAAK,sCAAA5B,OAAqCiD,EAAOrB,MAAK,6BAAA5B,OAA4BiD,EAAOrB,MAAK,6BAA4Ba,UACrLC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAxC,OAAKiD,EAAOtB,KAAI,UAAA3B,OAASiD,EAAOrB,MAAK,mBAAA5B,OAAkBiD,EAAOrB,MAAK,aAEjFW,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtDQ,EAAOvB,SAEVgB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDQ,EAAOnB,qBAbToB,YAwBfX,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE1D,EAAE,qBAAsB,0BAE3B2D,EAAAA,EAAAA,KAACK,EAAAA,GAAI,CACHC,GAAG,YACHR,UAAU,wDAAuDC,SAEhE1D,EAAE,UAAW,kBAGlB2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,UAClBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SArUT,CACzB,CACEU,GAAI,UACJC,KAAM,UACNC,QAAS,kBACTjC,OAAQ,IACRkC,OAAQ,OACRtB,OAAQ,YACRuB,KAAM,aACNzB,YAAa,4BAEf,CACEqB,GAAI,UACJC,KAAM,YACNC,QAAS,kBACTjC,OAAQ,IACRkC,OAAQ,cACRtB,OAAQ,UACRuB,KAAM,aACNzB,YAAa,sBAEf,CACEqB,GAAI,UACJC,KAAM,UACNC,QAAS,qBACTjC,OAAQ,IACRkC,OAAQ,OACRtB,OAAQ,YACRuB,KAAM,aACNzB,YAAa,sBAySenB,IAAK6C,IACvBjB,EAAAA,EAAAA,MAAA,OAA0BC,UAAU,+EAA8EC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,kBAAAxC,OACS,YAArBwD,EAAYJ,KACR,oCACA,mCACHX,UACDC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAxC,OACW,YAArBwD,EAAYJ,KACR,wDACA,2DAGRb,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SACrDe,EAAYH,WAEfX,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDe,EAAY1B,qBAInBS,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8CAA6CC,SACvDR,EAAeuB,EAAYpC,WAE9BsB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAxC,OAAgD+B,EAAeyB,EAAYxB,SAAUS,SACjG1D,EAAEyE,EAAYxB,OAAQwB,EAAYxB,eA3B/BwB,EAAYL,aAqC9BZ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAC1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE1D,EAAE,kBAAmB,uBAExB2D,EAAAA,EAAAA,KAACK,EAAAA,GAAI,CACHC,GAAG,aACHR,UAAU,wDAAuDC,SAEhE1D,EAAE,UAAW,kBAGlB2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,UAClBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oFAAmFC,SAAA,EAChGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SACrD1D,EAAE,iBAAkB,sBAEvB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD1D,EAAE,YAAa,uBAItB2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDAAuDC,SACjEjB,UAKPe,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sFAAqFC,SAAA,EAClGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SACrD1D,EAAE,gBAAiB,qBAEtB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD1D,EAAE,mBAAoB,8BAI7B2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAAyDC,SACnEjB,UAKPe,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gFAA+EC,SAAA,EAC5FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+CAA8CC,UAC3DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAEfD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SACrD1D,EAAE,iBAAkB,sBAEvB2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD1D,EAAE,iBAAkB,4BAI3B2D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDAAmDC,SAC7DjB,qBAUbpC,IACAmD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EAEzDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,eAAgB,oBAErB2D,EAAAA,EAAAA,KAACe,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIlB,UAC5CF,EAAAA,EAAAA,MAACqB,EAAAA,EAAS,CAACnD,KAAMnB,EAAYmD,SAAA,EAC3BC,EAAAA,EAAAA,KAACmB,EAAAA,EAAa,CAACC,gBAAgB,SAC/BpB,EAAAA,EAAAA,KAACqB,EAAAA,EAAK,CAACC,QAAQ,WACftB,EAAAA,EAAAA,KAACuB,EAAAA,EAAK,KACNvB,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,CAACC,UAAYxB,GAAU,CAAC,IAAD3C,OAAK2C,EAAMyB,kBAAoB,OAC9D1B,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,KACP3B,EAAAA,EAAAA,KAAC4B,EAAAA,EAAI,CAAClB,KAAK,WAAWY,QAAQ,UAAUO,QAAQ,IAAIC,OAAO,UAAUC,KAAK,UAAUC,KAAK,aACzFhC,EAAAA,EAAAA,KAAC4B,EAAAA,EAAI,CAAClB,KAAK,WAAWY,QAAQ,WAAWO,QAAQ,IAAIC,OAAO,UAAUC,KAAK,UAAUC,KAAK,cAC1FhC,EAAAA,EAAAA,KAAC4B,EAAAA,EAAI,CAAClB,KAAK,WAAWY,QAAQ,SAASO,QAAQ,IAAIC,OAAO,UAAUC,KAAK,UAAUC,KAAK,oBAM9FnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,mBAAoB,wBAEzB2D,EAAAA,EAAAA,KAACe,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIlB,UAC5CF,EAAAA,EAAAA,MAACoC,EAAAA,EAAQ,CAAAlC,SAAA,EACPC,EAAAA,EAAAA,KAACkC,EAAAA,EAAG,CACFnE,KAAMjB,EACNqF,GAAG,MACHC,GAAG,MACHC,WAAW,EACXC,MAAOC,IAAA,IAAC,KAAEP,EAAI,WAAErD,GAAY4D,EAAA,SAAAjF,OAAQ0E,EAAI,KAAA1E,OAAIqB,EAAU,MACtD6D,YAAa,GACbT,KAAK,UACLT,QAAQ,SAAQvB,SAEfjD,EAAYmB,IAAI,CAACwE,EAAOjC,KACvBR,EAAAA,EAAAA,KAAC0C,EAAAA,EAAI,CAAuBX,KAAM,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAAWvB,EAAQ,IAAG,QAAAlD,OAAxGkD,QAGvBR,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,CAACC,UAAYxB,GAAU,CAAC,IAAD3C,OAAK2C,EAAMyB,kBAAoB,eAMpE7B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,qBAAsB,0BAE3B2D,EAAAA,EAAAA,KAACe,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIlB,UAC5CF,EAAAA,EAAAA,MAAC8C,EAAAA,EAAQ,CAAC5E,KAAMnB,EAAYmD,SAAA,EAC1BC,EAAAA,EAAAA,KAACmB,EAAAA,EAAa,CAACC,gBAAgB,SAC/BpB,EAAAA,EAAAA,KAACqB,EAAAA,EAAK,CAACC,QAAQ,WACftB,EAAAA,EAAAA,KAACuB,EAAAA,EAAK,KACNvB,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,CAACC,UAAYxB,GAAU,CAAC,IAAD3C,OAAK2C,EAAMyB,kBAAoB,OAC9D1B,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,KACP3B,EAAAA,EAAAA,KAAC4C,EAAAA,EAAG,CAACtB,QAAQ,UAAUS,KAAK,UAAUC,KAAK,aAC3ChC,EAAAA,EAAAA,KAAC4C,EAAAA,EAAG,CAACtB,QAAQ,SAASS,KAAK,UAAUC,KAAK,oBAMhDnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,4BAA6B,yBAElC2D,EAAAA,EAAAA,KAACe,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIlB,UAC5CF,EAAAA,EAAAA,MAACgD,EAAAA,EAAS,CAAC9E,KAAMnB,EAAYmD,SAAA,EAC3BC,EAAAA,EAAAA,KAACmB,EAAAA,EAAa,CAACC,gBAAgB,SAC/BpB,EAAAA,EAAAA,KAACqB,EAAAA,EAAK,CAACC,QAAQ,WACftB,EAAAA,EAAAA,KAACuB,EAAAA,EAAK,CAACuB,QAAQ,UACf9C,EAAAA,EAAAA,KAACuB,EAAAA,EAAK,CAACuB,QAAQ,QAAQC,YAAY,WACnC/C,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,KACRxB,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,KACP3B,EAAAA,EAAAA,KAAC4C,EAAAA,EAAG,CAACE,QAAQ,OAAOxB,QAAQ,WAAWS,KAAK,UAAUC,KAAK,cAC3DhC,EAAAA,EAAAA,KAACgD,EAAAA,EAAI,CAACF,QAAQ,QAAQpC,KAAK,WAAWY,QAAQ,UAAUQ,OAAO,UAAUE,KAAK,4BAQxFnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,eAAgB,oBAErBwD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,qBACHR,UAAU,gIAA+HC,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAE1D,EAAE,gBAAiB,qBAC7E2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAE1D,EAAE,qBAAsB,iCAIrFwD,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,sBACHR,UAAU,oIAAmIC,SAAA,EAE7IC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAE1D,EAAE,gBAAiB,qBAC7E2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAE1D,EAAE,gBAAiB,4BAIhFwD,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,uBACHR,UAAU,wIAAuIC,SAAA,EAEjJC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAE1D,EAAE,cAAe,mBAC3E2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAE1D,EAAE,oBAAqB,gCAIpFwD,EAAAA,EAAAA,MAACQ,EAAAA,GAAI,CACHC,GAAG,qBACHR,UAAU,wIAAuIC,SAAA,EAEjJC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SAAE1D,EAAE,cAAe,mBAC3E2D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAE1D,EAAE,mBAAoB,oCAOrFK,IACAmD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EAEzDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,eAAgB,oBAErB2D,EAAAA,EAAAA,KAACe,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIlB,UAC5CF,EAAAA,EAAAA,MAACqB,EAAAA,EAAS,CAACnD,KAAMnB,EAAYmD,SAAA,EAC3BC,EAAAA,EAAAA,KAACmB,EAAAA,EAAa,CAACC,gBAAgB,SAC/BpB,EAAAA,EAAAA,KAACqB,EAAAA,EAAK,CAACC,QAAQ,WACftB,EAAAA,EAAAA,KAACuB,EAAAA,EAAK,KACNvB,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,CAACC,UAAYxB,GAAU,CAAC,IAAD3C,OAAK2C,EAAMyB,kBAAoB,OAC9D1B,EAAAA,EAAAA,KAAC2B,EAAAA,EAAM,KACP3B,EAAAA,EAAAA,KAAC4B,EAAAA,EAAI,CAAClB,KAAK,WAAWY,QAAQ,UAAUO,QAAQ,IAAIC,OAAO,UAAUC,KAAK,UAAUC,KAAK,aACzFhC,EAAAA,EAAAA,KAAC4B,EAAAA,EAAI,CAAClB,KAAK,WAAWY,QAAQ,WAAWO,QAAQ,IAAIC,OAAO,UAAUC,KAAK,UAAUC,KAAK,cAC1FhC,EAAAA,EAAAA,KAAC4B,EAAAA,EAAI,CAAClB,KAAK,WAAWY,QAAQ,SAASO,QAAQ,IAAIC,OAAO,UAAUC,KAAK,UAAUC,KAAK,oBAM9FnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE1D,EAAE,mBAAoB,wBAEzB2D,EAAAA,EAAAA,KAACe,EAAAA,EAAmB,CAACC,MAAM,OAAOC,OAAQ,IAAIlB,UAC5CF,EAAAA,EAAAA,MAACoC,EAAAA,EAAQ,CAAAlC,SAAA,EACPC,EAAAA,EAAAA,KAACkC,EAAAA,EAAG,CACFnE,KAAMjB,EACNqF,GAAG,MACHC,GAAG,MACHC,WAAW,EACXC,MAAOW,IAAA,IAAC,KAAEjB,EAAI,WAAErD,GAAYsE,EAAA,SAAA3F,OAAQ0E,EAAI,KAAA1E,OAAIqB,EAAU,MACtD6D,YAAa,GACbT,KAAK,UACLT,QAAQ,SAAQvB,SAEfjD,EAAYmB,IAAI,CAACwE,EAAOjC,KACvBR,EAAAA,EAAAA,KAAC0C,EAAAA,EAAI,CAAuBX,KAAM,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,WAAWvB,EAAQ,IAAG,QAAAlD,OAAxGkD,QAGvBR,EAAAA,EAAAA,KAACwB,EAAAA,EAAO,CAACC,UAAYxB,GAAU,CAAC,IAAD3C,OAAK2C,EAAMyB,kBAAoB,oB", "sources": ["pages/Financial/FinancialDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { Link } from 'react-router-dom';\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts';\nimport { format, subDays, startOfMonth, endOfMonth } from 'date-fns';\n\nconst FinancialDashboard = () => {\n  const { t } = useLanguage();\n  const [timeRange, setTimeRange] = useState('thisMonth');\n  const [loading, setLoading] = useState(true);\n  const [revenueData, setRevenueData] = useState([]);\n  const [expenseData, setExpenseData] = useState([]);\n\n  useEffect(() => {\n    fetchFinancialData();\n  }, [timeRange]);\n\n  const fetchFinancialData = async () => {\n    setLoading(true);\n    try {\n      // Fetch billing statistics\n      const billingResponse = await fetch('/api/v1/billing/stats', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      // Fetch payment statistics\n      const paymentsResponse = await fetch('/api/v1/payments/stats', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (billingResponse.ok && paymentsResponse.ok) {\n        const billingData = await billingResponse.json();\n        const paymentsData = await paymentsResponse.json();\n\n        // Process monthly revenue data\n        const monthlyRevenue = billingData.data.monthlyRevenue || [];\n        const processedRevenueData = monthlyRevenue.map(item => ({\n          month: item.month,\n          revenue: item.revenue,\n          paid: item.paid,\n          profit: item.paid * 0.7, // Assuming 30% expenses\n          patients: item.count * 3 // Estimate patients per invoice\n        }));\n\n        setRevenueData(processedRevenueData);\n\n        // Mock expense data (you can create an expenses API later)\n        const mockExpenseData = [\n          { category: 'Salaries', amount: 25000, percentage: 45 },\n          { category: 'Equipment', amount: 8000, percentage: 15 },\n          { category: 'Rent', amount: 6000, percentage: 11 },\n          { category: 'Utilities', amount: 3000, percentage: 5 },\n          { category: 'Supplies', amount: 4000, percentage: 7 },\n          { category: 'Insurance', amount: 2000, percentage: 4 },\n          { category: 'Other', amount: 7000, percentage: 13 }\n        ];\n\n        setExpenseData(mockExpenseData);\n      } else {\n        throw new Error('Failed to fetch financial data');\n      }\n    } catch (error) {\n      console.error('Error fetching financial data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Mock financial data\n  const financialMetrics = {\n    totalRevenue: 125000,\n    totalExpenses: 85000,\n    netProfit: 40000,\n    outstandingPayments: 15000,\n    insuranceClaims: 35000,\n    cashPayments: 90000,\n    pendingClaims: 8,\n    approvedClaims: 45,\n    rejectedClaims: 3\n  };\n\n  const recentTransactions = [\n    {\n      id: 'TXN-001',\n      type: 'payment',\n      patient: 'Ahmed Al-Rashid',\n      amount: 350,\n      method: 'Cash',\n      status: 'completed',\n      date: '2024-02-15',\n      description: 'Physical Therapy Session'\n    },\n    {\n      id: 'TXN-002',\n      type: 'insurance',\n      patient: 'Fatima Al-Zahra',\n      amount: 500,\n      method: 'Bupa Arabia',\n      status: 'pending',\n      date: '2024-02-14',\n      description: 'Initial Assessment'\n    },\n    {\n      id: 'TXN-003',\n      type: 'payment',\n      patient: 'Mohammed Al-Otaibi',\n      amount: 250,\n      method: 'Card',\n      status: 'completed',\n      date: '2024-02-13',\n      description: 'Follow-up Session'\n    }\n  ];\n\n  const quickActions = [\n    {\n      title: t('processPayment', 'Process Payment'),\n      icon: 'fas fa-credit-card',\n      color: 'blue',\n      link: '/payments/new',\n      description: t('processNewPayment', 'Process a new patient payment')\n    },\n    {\n      title: t('submitClaim', 'Submit Insurance Claim'),\n      icon: 'fas fa-file-invoice',\n      color: 'green',\n      link: '/insurance/claims/new',\n      description: t('submitNewClaim', 'Submit a new insurance claim')\n    },\n    {\n      title: t('generateInvoice', 'Generate Invoice'),\n      icon: 'fas fa-receipt',\n      color: 'purple',\n      link: '/billing/invoice/new',\n      description: t('createNewInvoice', 'Create a new patient invoice')\n    },\n    {\n      title: t('viewReports', 'Financial Reports'),\n      icon: 'fas fa-chart-line',\n      color: 'orange',\n      link: '/reports/financial',\n      description: t('viewFinancialReports', 'View detailed financial reports')\n    }\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed': return 'text-green-600 bg-green-100';\n      case 'pending': return 'text-yellow-600 bg-yellow-100';\n      case 'failed': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('financialDashboard', 'Financial Dashboard')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('financialOverview', 'Overview of your clinic\\'s financial performance')}\n          </p>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <select\n            value={timeRange}\n            onChange={(e) => setTimeRange(e.target.value)}\n            className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          >\n            <option value=\"thisWeek\">{t('thisWeek', 'This Week')}</option>\n            <option value=\"thisMonth\">{t('thisMonth', 'This Month')}</option>\n            <option value=\"thisQuarter\">{t('thisQuarter', 'This Quarter')}</option>\n            <option value=\"thisYear\">{t('thisYear', 'This Year')}</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('quickActions', 'Quick Actions')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <Link\n            to=\"/financial/templates\"\n            className=\"flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\"\n          >\n            <div className=\"p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg mr-3\">\n              <i className=\"fas fa-file-invoice text-blue-600 dark:text-blue-400\"></i>\n            </div>\n            <div>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                {t('invoiceTemplates', 'Invoice Templates')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('manageTemplates', 'Manage templates')}\n              </p>\n            </div>\n          </Link>\n\n          <Link\n            to=\"/financial/invoices/create\"\n            className=\"flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors\"\n          >\n            <div className=\"p-2 bg-green-100 dark:bg-green-900/50 rounded-lg mr-3\">\n              <i className=\"fas fa-plus text-green-600 dark:text-green-400\"></i>\n            </div>\n            <div>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                {t('createInvoice', 'Create Invoice')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('newInvoice', 'New invoice')}\n              </p>\n            </div>\n          </Link>\n\n          <Link\n            to=\"/financial/invoices\"\n            className=\"flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors\"\n          >\n            <div className=\"p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg mr-3\">\n              <i className=\"fas fa-list text-purple-600 dark:text-purple-400\"></i>\n            </div>\n            <div>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                {t('viewInvoices', 'View Invoices')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('allInvoices', 'All invoices')}\n              </p>\n            </div>\n          </Link>\n\n          <Link\n            to=\"/integrations\"\n            className=\"flex items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors\"\n          >\n            <div className=\"p-2 bg-orange-100 dark:bg-orange-900/50 rounded-lg mr-3\">\n              <i className=\"fas fa-link text-orange-600 dark:text-orange-400\"></i>\n            </div>\n            <div>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                {t('integrations', 'Saudi Integrations')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('nphiesZatca', 'NPHIES & ZATCA')}\n              </p>\n            </div>\n          </Link>\n        </div>\n\n        {/* Debug/Test Section */}\n        <div className=\"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800\">\n          <h3 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n            🧪 Testing & Debug\n          </h3>\n          <div className=\"flex space-x-2\">\n            <Link\n              to=\"/financial/test-templates\"\n              className=\"px-3 py-1 bg-yellow-200 text-yellow-800 rounded text-sm hover:bg-yellow-300\"\n            >\n              Test Templates API\n            </Link>\n            <Link\n              to=\"/financial/templates\"\n              className=\"px-3 py-1 bg-blue-200 text-blue-800 rounded text-sm hover:bg-blue-300\"\n            >\n              Templates UI\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      {/* Financial Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-dollar-sign text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalRevenue', 'Total Revenue')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(financialMetrics.totalRevenue)}\n              </p>\n              <p className=\"text-sm text-green-600\">+12% {t('fromLastMonth', 'from last month')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg\">\n              <i className=\"fas fa-minus-circle text-red-600 dark:text-red-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalExpenses', 'Total Expenses')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(financialMetrics.totalExpenses)}\n              </p>\n              <p className=\"text-sm text-red-600\">+5% {t('fromLastMonth', 'from last month')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-chart-line text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('netProfit', 'Net Profit')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(financialMetrics.netProfit)}\n              </p>\n              <p className=\"text-sm text-blue-600\">+18% {t('fromLastMonth', 'from last month')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('outstandingPayments', 'Outstanding')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(financialMetrics.outstandingPayments)}\n              </p>\n              <p className=\"text-sm text-yellow-600\">{t('pendingCollection', 'Pending collection')}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('quickActions', 'Quick Actions')}\n          </h3>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            {quickActions.map((action, index) => (\n              <Link\n                key={index}\n                to={action.link}\n                className={`p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-${action.color}-300 hover:bg-${action.color}-50 dark:hover:bg-${action.color}-900/20 transition-colors group`}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <div className={`p-2 bg-${action.color}-100 dark:bg-${action.color}-900/30 rounded-lg group-hover:bg-${action.color}-200 dark:group-hover:bg-${action.color}-900/50 transition-colors`}>\n                    <i className={`${action.icon} text-${action.color}-600 dark:text-${action.color}-400`}></i>\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                      {action.title}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {action.description}\n                    </p>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Recent Transactions and Insurance Claims */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Recent Transactions */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('recentTransactions', 'Recent Transactions')}\n            </h3>\n            <Link\n              to=\"/payments\"\n              className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n            >\n              {t('viewAll', 'View All')}\n            </Link>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-4\">\n              {recentTransactions.map((transaction) => (\n                <div key={transaction.id} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`p-2 rounded-lg ${\n                      transaction.type === 'payment' \n                        ? 'bg-green-100 dark:bg-green-900/30' \n                        : 'bg-blue-100 dark:bg-blue-900/30'\n                    }`}>\n                      <i className={`${\n                        transaction.type === 'payment' \n                          ? 'fas fa-credit-card text-green-600 dark:text-green-400' \n                          : 'fas fa-shield-alt text-blue-600 dark:text-blue-400'\n                      }`}></i>\n                    </div>\n                    <div>\n                      <p className=\"font-medium text-gray-900 dark:text-white\">\n                        {transaction.patient}\n                      </p>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {transaction.description}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"text-right\">\n                    <p className=\"font-semibold text-gray-900 dark:text-white\">\n                      {formatCurrency(transaction.amount)}\n                    </p>\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(transaction.status)}`}>\n                      {t(transaction.status, transaction.status)}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Insurance Claims Summary */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('insuranceClaims', 'Insurance Claims')}\n            </h3>\n            <Link\n              to=\"/insurance\"\n              className=\"text-blue-600 hover:text-blue-800 text-sm font-medium\"\n            >\n              {t('viewAll', 'View All')}\n            </Link>\n          </div>\n          <div className=\"p-6\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n                    <i className=\"fas fa-check-circle text-green-600 dark:text-green-400\"></i>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900 dark:text-white\">\n                      {t('approvedClaims', 'Approved Claims')}\n                    </p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('thisMonth', 'This month')}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-2xl font-bold text-green-600 dark:text-green-400\">\n                    {financialMetrics.approvedClaims}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n                    <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400\"></i>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900 dark:text-white\">\n                      {t('pendingClaims', 'Pending Claims')}\n                    </p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('awaitingApproval', 'Awaiting approval')}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-2xl font-bold text-yellow-600 dark:text-yellow-400\">\n                    {financialMetrics.pendingClaims}\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"p-2 bg-red-100 dark:bg-red-900/30 rounded-lg\">\n                    <i className=\"fas fa-times-circle text-red-600 dark:text-red-400\"></i>\n                  </div>\n                  <div>\n                    <p className=\"font-medium text-gray-900 dark:text-white\">\n                      {t('rejectedClaims', 'Rejected Claims')}\n                    </p>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {t('requiresReview', 'Requires review')}\n                    </p>\n                  </div>\n                </div>\n                <div className=\"text-right\">\n                  <p className=\"text-2xl font-bold text-red-600 dark:text-red-400\">\n                    {financialMetrics.rejectedClaims}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Financial Charts */}\n      {!loading && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\">\n          {/* Revenue Trend Chart */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('revenueTrend', 'Revenue Trend')}\n            </h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <AreaChart data={revenueData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />\n                <Legend />\n                <Area type=\"monotone\" dataKey=\"revenue\" stackId=\"1\" stroke=\"#8884d8\" fill=\"#8884d8\" name=\"Revenue\" />\n                <Area type=\"monotone\" dataKey=\"expenses\" stackId=\"1\" stroke=\"#82ca9d\" fill=\"#82ca9d\" name=\"Expenses\" />\n                <Area type=\"monotone\" dataKey=\"profit\" stackId=\"1\" stroke=\"#ffc658\" fill=\"#ffc658\" name=\"Profit\" />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Expense Breakdown Chart */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('expenseBreakdown', 'Expense Breakdown')}\n            </h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={expenseData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ name, percentage }) => `${name} ${percentage}%`}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"amount\"\n                >\n                  {expenseData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658'][index % 7]} />\n                  ))}\n                </Pie>\n                <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Monthly Performance Chart */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('monthlyPerformance', 'Monthly Performance')}\n            </h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={revenueData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />\n                <Legend />\n                <Bar dataKey=\"revenue\" fill=\"#8884d8\" name=\"Revenue\" />\n                <Bar dataKey=\"profit\" fill=\"#82ca9d\" name=\"Profit\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Patient Revenue Correlation */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('patientRevenueCorrelation', 'Patient vs Revenue')}\n            </h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={revenueData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis yAxisId=\"left\" />\n                <YAxis yAxisId=\"right\" orientation=\"right\" />\n                <Tooltip />\n                <Legend />\n                <Bar yAxisId=\"left\" dataKey=\"patients\" fill=\"#8884d8\" name=\"Patients\" />\n                <Line yAxisId=\"right\" type=\"monotone\" dataKey=\"revenue\" stroke=\"#ff7300\" name=\"Revenue ($)\" />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n\n      {/* Quick Actions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mt-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('quickActions', 'Quick Actions')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <Link\n            to=\"/financial/billing\"\n            className=\"flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\"\n          >\n            <i className=\"fas fa-file-invoice text-blue-600 text-xl mr-3\"></i>\n            <div>\n              <p className=\"font-medium text-gray-900 dark:text-white\">{t('createInvoice', 'Create Invoice')}</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('generateNewInvoice', 'Generate new invoice')}</p>\n            </div>\n          </Link>\n\n          <Link\n            to=\"/financial/payments\"\n            className=\"flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors\"\n          >\n            <i className=\"fas fa-credit-card text-green-600 text-xl mr-3\"></i>\n            <div>\n              <p className=\"font-medium text-gray-900 dark:text-white\">{t('recordPayment', 'Record Payment')}</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('addNewPayment', 'Add new payment')}</p>\n            </div>\n          </Link>\n\n          <Link\n            to=\"/financial/insurance\"\n            className=\"flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors\"\n          >\n            <i className=\"fas fa-shield-alt text-purple-600 text-xl mr-3\"></i>\n            <div>\n              <p className=\"font-medium text-gray-900 dark:text-white\">{t('submitClaim', 'Submit Claim')}</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('newInsuranceClaim', 'New insurance claim')}</p>\n            </div>\n          </Link>\n\n          <Link\n            to=\"/financial/reports\"\n            className=\"flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors\"\n          >\n            <i className=\"fas fa-chart-bar text-yellow-600 text-xl mr-3\"></i>\n            <div>\n              <p className=\"font-medium text-gray-900 dark:text-white\">{t('viewReports', 'View Reports')}</p>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('financialReports', 'Financial reports')}</p>\n            </div>\n          </Link>\n        </div>\n      </div>\n\n      {/* Financial Charts */}\n      {!loading && (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\">\n          {/* Revenue Trend Chart */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('revenueTrend', 'Revenue Trend')}\n            </h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <AreaChart data={revenueData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"month\" />\n                <YAxis />\n                <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />\n                <Legend />\n                <Area type=\"monotone\" dataKey=\"revenue\" stackId=\"1\" stroke=\"#8884d8\" fill=\"#8884d8\" name=\"Revenue\" />\n                <Area type=\"monotone\" dataKey=\"expenses\" stackId=\"1\" stroke=\"#82ca9d\" fill=\"#82ca9d\" name=\"Expenses\" />\n                <Area type=\"monotone\" dataKey=\"profit\" stackId=\"1\" stroke=\"#ffc658\" fill=\"#ffc658\" name=\"Profit\" />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Expense Breakdown Chart */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              {t('expenseBreakdown', 'Expense Breakdown')}\n            </h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <PieChart>\n                <Pie\n                  data={expenseData}\n                  cx=\"50%\"\n                  cy=\"50%\"\n                  labelLine={false}\n                  label={({ name, percentage }) => `${name} ${percentage}%`}\n                  outerRadius={80}\n                  fill=\"#8884d8\"\n                  dataKey=\"amount\"\n                >\n                  {expenseData.map((entry, index) => (\n                    <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658'][index % 7]} />\n                  ))}\n                </Pie>\n                <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, '']} />\n              </PieChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FinancialDashboard;\n"], "names": ["FinancialDashboard", "t", "useLanguage", "timeRange", "setTimeRange", "useState", "loading", "setLoading", "revenueData", "setRevenueData", "expenseData", "setExpenseData", "useEffect", "fetchFinancialData", "async", "billingResponse", "fetch", "headers", "concat", "localStorage", "getItem", "paymentsResponse", "ok", "Error", "billingData", "json", "processedRevenueData", "data", "monthlyRevenue", "map", "item", "month", "revenue", "paid", "profit", "patients", "count", "category", "amount", "percentage", "error", "console", "financialMetrics", "quickActions", "title", "icon", "color", "link", "description", "getStatusColor", "status", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "_jsxs", "className", "children", "_jsx", "value", "onChange", "e", "target", "Link", "to", "action", "index", "id", "type", "patient", "method", "date", "transaction", "ResponsiveContainer", "width", "height", "AreaChart", "Cartesian<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XAxis", "dataKey", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "formatter", "toLocaleString", "Legend", "Area", "stackId", "stroke", "fill", "name", "<PERSON><PERSON><PERSON>", "Pie", "cx", "cy", "labelLine", "label", "_ref", "outerRadius", "entry", "Cell", "<PERSON><PERSON><PERSON>", "Bar", "Line<PERSON>hart", "yAxisId", "orientation", "Line", "_ref2"], "sourceRoot": ""}