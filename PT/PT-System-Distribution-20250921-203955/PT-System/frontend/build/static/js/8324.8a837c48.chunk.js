"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8324],{8324:(e,a,r)=>{r.r(a),r.d(a,{default:()=>n});var t=r(2555),s=r(5043),i=r(7921),l=r(4528),d=r(2015),c=r(579);const n=()=>{const{t:e,isRTL:a,language:r,setLanguage:n}=(0,i.o)(),{user:o}=(0,l.A)(),{theme:g,setTheme:x}=(0,d.D)(),[m,h]=(0,s.useState)("profile"),[b,y]=(0,s.useState)(!1),[u,f]=(0,s.useState)({firstName:"Dr. Sarah",lastName:"Al-<PERSON>",email:"<EMAIL>",phone:"+966 50 123 4567",specialization:"Pediatric Physical Therapy",license:"PT-2024-001",department:"Special Needs Department",experience:"8 years",bio:"Specialized in pediatric physical therapy with focus on special needs children.",avatar:null}),[p,j]=(0,s.useState)({emailNotifications:!0,smsNotifications:!1,pushNotifications:!0,appointmentReminders:!0,reportNotifications:!1,marketingEmails:!1,autoSave:!0,sessionTimeout:30,defaultView:"dashboard",dateFormat:"DD/MM/YYYY",timeFormat:"24h",timezone:"Asia/Riyadh"}),[v,k]=(0,s.useState)({twoFactorEnabled:!1,loginAlerts:!0,sessionHistory:!0,dataExportRequests:[]}),N=[{id:"profile",label:e("profile","Profile"),icon:"fas fa-user"},{id:"preferences",label:e("preferences","Preferences"),icon:"fas fa-cog"},{id:"security",label:e("security","Security"),icon:"fas fa-shield-alt"},{id:"activity",label:e("activity","Activity"),icon:"fas fa-history"}],w=(e,a)=>{f(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a}))},Y=(e,a)=>{j(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a}))};return(0,c.jsxs)("div",{className:"p-6 ".concat(a?"font-arabic":"font-english"),children:[(0,c.jsx)("div",{className:"flex items-center justify-between mb-6",children:(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("userProfile","User Profile")}),(0,c.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("profileDesc","Manage your account settings and preferences")})]})}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,c.jsx)("div",{className:"lg:col-span-1",children:(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,c.jsx)("nav",{className:"p-4 space-y-2",children:N.map(e=>(0,c.jsxs)("button",{onClick:()=>h(e.id),className:"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ".concat(m===e.id?"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300":"text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"),children:[(0,c.jsx)("i",{className:e.icon}),(0,c.jsx)("span",{children:e.label})]},e.id))})})}),(0,c.jsx)("div",{className:"lg:col-span-3",children:(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:["profile"===m&&(0,c.jsxs)("div",{className:"p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("profileInformation","Profile Information")}),(0,c.jsx)("button",{onClick:()=>b?void y(!1):y(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:b?e("save","Save"):e("edit","Edit")})]}),(0,c.jsxs)("div",{className:"flex items-center mb-6",children:[(0,c.jsxs)("div",{className:"relative",children:[(0,c.jsx)("div",{className:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center overflow-hidden",children:u.avatar?(0,c.jsx)("img",{src:u.avatar,alt:"Avatar",className:"w-full h-full object-cover"}):(0,c.jsx)("i",{className:"fas fa-user text-3xl text-gray-500 dark:text-gray-400"})}),b&&(0,c.jsxs)("label",{className:"absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700",children:[(0,c.jsx)("i",{className:"fas fa-camera text-sm"}),(0,c.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{const a=e.target.files[0];if(a){const e=new FileReader;e.onload=e=>{f(a=>(0,t.A)((0,t.A)({},a),{},{avatar:e.target.result}))},e.readAsDataURL(a)}},className:"hidden"})]})]}),(0,c.jsxs)("div",{className:"ml-6",children:[(0,c.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[u.firstName," ",u.lastName]}),(0,c.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:u.specialization}),(0,c.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:u.department})]})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("firstName","First Name")}),(0,c.jsx)("input",{type:"text",value:u.firstName,onChange:e=>w("firstName",e.target.value),disabled:!b,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("lastName","Last Name")}),(0,c.jsx)("input",{type:"text",value:u.lastName,onChange:e=>w("lastName",e.target.value),disabled:!b,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("email","Email")}),(0,c.jsx)("input",{type:"email",value:u.email,onChange:e=>w("email",e.target.value),disabled:!b,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("phone","Phone")}),(0,c.jsx)("input",{type:"tel",value:u.phone,onChange:e=>w("phone",e.target.value),disabled:!b,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("specialization","Specialization")}),(0,c.jsx)("input",{type:"text",value:u.specialization,onChange:e=>w("specialization",e.target.value),disabled:!b,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800"})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("license","License Number")}),(0,c.jsx)("input",{type:"text",value:u.license,onChange:e=>w("license",e.target.value),disabled:!b,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800"})]})]}),(0,c.jsxs)("div",{className:"mt-6",children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("bio","Bio")}),(0,c.jsx)("textarea",{rows:"4",value:u.bio,onChange:e=>w("bio",e.target.value),disabled:!b,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-800"})]})]}),"preferences"===m&&(0,c.jsxs)("div",{className:"p-6",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("userPreferences","User Preferences")}),(0,c.jsxs)("div",{className:"space-y-8",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("notificationPreferences","Notification Preferences")}),(0,c.jsx)("div",{className:"space-y-4",children:Object.entries(p).slice(0,6).map(a=>{let[r,t]=a;return(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e(r,r.charAt(0).toUpperCase()+r.slice(1).replace(/([A-Z])/g," $1"))}),(0,c.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("".concat(r,"Desc"),"Enable ".concat(r.toLowerCase().replace(/([A-Z])/g," $1")," notifications"))})]}),(0,c.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,c.jsx)("input",{type:"checkbox",checked:t,onChange:e=>Y(r,e.target.checked),className:"sr-only peer"}),(0,c.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]},r)})})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("systemPreferences","System Preferences")}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("language","Language")}),(0,c.jsxs)("select",{value:r,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,c.jsx)("option",{value:"en",children:"English"}),(0,c.jsx)("option",{value:"ar",children:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("theme","Theme")}),(0,c.jsxs)("select",{value:g,onChange:e=>x(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,c.jsx)("option",{value:"light",children:e("light","Light")}),(0,c.jsx)("option",{value:"dark",children:e("dark","Dark")}),(0,c.jsx)("option",{value:"system",children:e("system","System")})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("dateFormat","Date Format")}),(0,c.jsxs)("select",{value:p.dateFormat,onChange:e=>Y("dateFormat",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,c.jsx)("option",{value:"DD/MM/YYYY",children:"DD/MM/YYYY"}),(0,c.jsx)("option",{value:"MM/DD/YYYY",children:"MM/DD/YYYY"}),(0,c.jsx)("option",{value:"YYYY-MM-DD",children:"YYYY-MM-DD"})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("timeFormat","Time Format")}),(0,c.jsxs)("select",{value:p.timeFormat,onChange:e=>Y("timeFormat",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,c.jsx)("option",{value:"12h",children:"12 Hour"}),(0,c.jsx)("option",{value:"24h",children:"24 Hour"})]})]})]})]})]})]}),"security"===m&&(0,c.jsxs)("div",{className:"p-6",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("securitySettings","Security Settings")}),(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("twoFactorAuth","Two-Factor Authentication")}),(0,c.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("twoFactorDesc","Add an extra layer of security to your account")})]}),(0,c.jsx)("button",{onClick:()=>{return e="twoFactorEnabled",a=!v.twoFactorEnabled,void k(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a}));var e,a},className:"px-4 py-2 rounded-lg transition-colors ".concat(v.twoFactorEnabled?"bg-red-600 text-white hover:bg-red-700":"bg-green-600 text-white hover:bg-green-700"),children:v.twoFactorEnabled?e("disable","Disable"):e("enable","Enable")})]}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("passwordSecurity","Password & Security")}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsx)("button",{className:"w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e("changePassword","Change Password")}),(0,c.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("lastChanged","Last changed 30 days ago")})]}),(0,c.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})}),(0,c.jsx)("button",{className:"w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e("loginHistory","Login History")}),(0,c.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("viewRecentLogins","View recent login activity")})]}),(0,c.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})}),(0,c.jsx)("button",{className:"w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e("activeSessions","Active Sessions")}),(0,c.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("manageActiveSessions","Manage your active sessions")})]}),(0,c.jsx)("i",{className:"fas fa-chevron-right text-gray-400"})]})})]})]})]})]}),"activity"===m&&(0,c.jsxs)("div",{className:"p-6",children:[(0,c.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("activityLog","Activity Log")}),(0,c.jsx)("div",{className:"space-y-4",children:[{action:"Login",time:"2 hours ago",details:"Logged in from Chrome on Windows",icon:"fas fa-sign-in-alt",color:"text-green-600"},{action:"Profile Updated",time:"1 day ago",details:"Updated contact information",icon:"fas fa-user-edit",color:"text-blue-600"},{action:"Password Changed",time:"3 days ago",details:"Password was successfully changed",icon:"fas fa-key",color:"text-yellow-600"},{action:"Data Export",time:"1 week ago",details:"Exported patient data report",icon:"fas fa-download",color:"text-purple-600"},{action:"Settings Modified",time:"2 weeks ago",details:"Updated notification preferences",icon:"fas fa-cog",color:"text-gray-600"}].map((e,a)=>(0,c.jsxs)("div",{className:"flex items-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,c.jsx)("div",{className:"p-3 rounded-full bg-gray-100 dark:bg-gray-700 mr-4",children:(0,c.jsx)("i",{className:"".concat(e.icon," ").concat(e.color)})}),(0,c.jsxs)("div",{className:"flex-1",children:[(0,c.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e.action}),(0,c.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.details}),(0,c.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-500 mt-1",children:e.time})]})]},a))})]})]})})]})]})}}}]);
//# sourceMappingURL=8324.8a837c48.chunk.js.map