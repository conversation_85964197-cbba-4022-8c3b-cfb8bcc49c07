{"version": 3, "file": "static/js/146.b7d966e3.chunk.js", "mappings": "qNAKA,MAs5BA,EAt5ByBA,KACvB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,IACxCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CAEvCG,eAAe,MAADC,OAAQC,KAAKC,OAC3BC,WAAW,IAAIF,MAAOG,cAAcC,MAAM,KAAK,GAC/CC,QAAS,KACTC,aAAc,KAGdC,YAAa,GAGbC,UAAW,CACT,CACEC,GAAI,EACJC,KAAM,GACNC,iBAAkB,GAClBC,qBAAyB,OAAJzB,QAAI,IAAJA,OAAI,EAAJA,EAAM0B,OAAQ,GACnCC,mBAAoB,GACpBC,iBAAkB,GAClBC,QAAS,KAKbC,aAAiB,OAAJ9B,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,KAAM,GACzBS,aAAa,IAAIlB,MAAOG,iBAGnBgB,EAASC,IAAczB,EAAAA,EAAAA,WAAS,IAChC0B,EAAQC,IAAa3B,EAAAA,EAAAA,UAAS,CAAC,IAEtC4B,EAAAA,EAAAA,WAAU,KACJlC,GACFmC,KAED,CAACnC,IAEJ,MAAMmC,EAAkBC,UACtB,IACEL,GAAW,GACX,MAAMM,QAAiBC,MAAM,iBAAD5B,OAAkBV,IAC9C,GAAIqC,EAASE,GAAI,CACf,MAAMC,QAAoBH,EAASI,OACnCjC,EAAYkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPxB,YAAasB,EAAYhB,MAAI,GAAAd,OAAO8B,EAAYI,UAAS,KAAAlC,OAAI8B,EAAYK,WAAc,KAE3F,CACF,CAAE,MAAOC,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACCf,GAAW,EACb,GAGIiB,EAAoBA,CAACC,EAAOC,KAChC1C,EAAYkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACO,GAAQC,KAGPlB,EAAOiB,IACThB,EAAUS,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACO,GAAQ,SAKTE,EAAuBA,CAACC,EAAOH,EAAOC,KAC1C1C,EAAYkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPvB,UAAWuB,EAAKvB,UAAUkC,IAAI,CAACC,EAAUC,IACvCA,IAAMH,GAAKT,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQW,GAAQ,IAAE,CAACL,GAAQC,IAAUI,MAIpD,MAAME,EAAQ,YAAA9C,OAAe0C,EAAK,KAAA1C,OAAIuC,GAClCjB,EAAOwB,IACTvB,EAAUS,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACc,GAAW,SAKZC,EAAcA,KACdlD,EAASY,UAAUuC,OAAS,GAC9BlD,EAAYkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPvB,UAAW,IACNuB,EAAKvB,UACR,CACEC,GAAIsB,EAAKvB,UAAUuC,OAAS,EAC5BrC,KAAM,GACNC,iBAAkB,GAClBC,qBAAyB,OAAJzB,QAAI,IAAJA,OAAI,EAAJA,EAAM0B,OAAQ,GACnCC,mBAAoB,GACpBC,iBAAkB,GAClBC,QAAS,SAObgC,EAAkBP,IAClB7C,EAASY,UAAUuC,OAAS,GAC9BlD,EAAYkC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPvB,UAAWuB,EAAKvB,UAAUyC,OAAO,CAACC,EAAGN,IAAMA,IAAMH,OAmJvD,OAAItB,GAEAgC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uFAAsFC,UACnGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,8GAA6GC,SACxHrE,EAAE,wBAAyB,gCAE9BsE,EAAAA,EAAAA,MAAA,KAAGF,UAAU,kEAAiEC,SAAA,EAC5EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCACZpE,EAAE,0BAA2B,iFAEhCsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oCACZpE,EAAE,gBAAiB,qBAEtBsE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCACZpE,EAAE,QAAS,eAEdsE,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6DAA4DC,SAAA,EAC1EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uCACZpE,EAAE,oBAAqB,gCAI9BsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uCAAsCC,SAAA,EACnDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BACZpE,EAAE,UAAW,eAEhBsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mGAAkGC,SAAA,EAC/GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BACZpE,EAAE,WAAY,6BAS3BmE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2KAA0KC,UACvLC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,SAC5B,CAAC,EAAG,GAAGX,IAAKa,IACXD,EAAAA,EAAAA,MAAA,UAEEE,QAASA,IAAM9D,EAAe6D,GAC9BH,UAAS,gEAAArD,OACPN,IAAgB8D,EACZ,sEACA,iKACHF,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,OAAArD,OAAkB,IAATwD,EAAa,UAAY,kBAAiB,WACrD,IAATA,EAAavE,EAAE,cAAe,6BAA+BA,EAAE,sBAAuB,2BATlFuE,OAaXD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2DAA0DC,SAAA,CACtErE,EAAE,OAAQ,QAAQ,IAAES,EAAY,IAAET,EAAE,KAAM,MAAM,cAMvDsE,EAAAA,EAAAA,MAAA,QAAMG,SAjIWhC,UAGnB,GAFAiC,EAAEC,iBA7FiBC,MACnB,MAAMC,EAAY,CAAC,EA6CnB,OA1CKjE,EAASW,YAAYuD,SACxBD,EAAUtD,YAAcvB,EAAE,sBAAuB,6BAInDY,EAASY,UAAUuD,QAAQ,CAACpB,EAAUF,KACpC,GAAKE,EAASjC,KAEP,CACL,MAAMsD,EAAe,IAAIhE,KAAK2C,EAASjC,MACjCuD,EAAQ,IAAIjE,KAClBiE,EAAMC,SAAS,EAAG,EAAG,EAAG,GACpBF,EAAeC,IACjBJ,EAAU,YAAD9D,OAAa0C,EAAK,UAAWzD,EAAE,0BAA2B,uCAEvE,MARE6E,EAAU,YAAD9D,OAAa0C,EAAK,UAAWzD,EAAE,eAAgB,oBAsB1D,GAZK2D,EAAShC,iBAAiBmD,SAC7BD,EAAU,YAAD9D,OAAa0C,EAAK,sBAAuBzD,EAAE,2BAA4B,kCAG7E2D,EAAS/B,oBAAoBkD,SAChCD,EAAU,YAAD9D,OAAa0C,EAAK,yBAA0BzD,EAAE,8BAA+B,qCAGnF2D,EAAS7B,mBAAmBgD,SAC/BD,EAAU,YAAD9D,OAAa0C,EAAK,wBAAyBzD,EAAE,6BAA8B,qCAGjF2D,EAAS5B,iBAEP,CACL,MAAMoD,EAAW,IAAInE,KAAK2C,EAAS5B,kBAC7BkD,EAAQ,IAAIjE,KAClBiE,EAAMC,SAAS,GAAI,GAAI,GAAI,KACvBC,GAAYF,IACdJ,EAAU,YAAD9D,OAAa0C,EAAK,sBAAuBzD,EAAE,uBAAwB,6CAEhF,MARE6E,EAAU,YAAD9D,OAAa0C,EAAK,sBAAuBzD,EAAE,2BAA4B,qCAWpFsC,EAAUuC,GAC+B,IAAlCO,OAAOC,KAAKR,GAAWd,QAiDzBa,GAIL,IACExC,GAAW,GAEX,MAAMkD,GAActC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfpC,GAAQ,IACXqB,YAAa9B,EAAKsB,GAClBS,aAAa,IAAIlB,MAAOG,gBAW1B,WARuBwB,MAAM,gCAAiC,CAC5D4C,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAUL,MAGV1C,GAIX,MAAM,IAAIgD,MAAM,iCAHhBC,MAAM7F,EAAE,oBAAqB,uCAC7BO,EAASF,EAAS,aAAAU,OAAgBV,GAAc,YAIpD,CAAE,MAAO8C,GACPC,QAAQD,MAAM,+BAAgCA,GAC9C0C,MAAM7F,EAAE,cAAe,kDACzB,CAAC,QACCoC,GAAW,EACb,GA8FgCgC,UAAU,YAAWC,SAAA,CAEhC,IAAhB5D,IACC6D,EAAAA,EAAAA,MAAAwB,EAAAA,SAAA,CAAAzB,SAAA,EAEEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0JAAyJC,SAAA,EACtKC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gFAA+EC,SAAA,EAC3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZpE,EAAE,sBAAuB,4BAG5BsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CC,SAAA,EAC5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,4EAA2EC,SAAA,EAC1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZpE,EAAE,iBAAkB,uBAEvBmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAO3C,EAASE,eAChBkF,SAAWtB,GAAMrB,EAAkB,iBAAkBqB,EAAEuB,OAAO1C,OAC9Da,UAAU,oIACV8B,UAAQ,QAGZ5B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,4EAA2EC,SAAA,EAC1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZpE,EAAE,YAAa,kBAElBmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAO3C,EAASM,UAChB8E,SAAWtB,GAAMrB,EAAkB,YAAaqB,EAAEuB,OAAO1C,OACzDa,UAAU,yKAGdE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,gFAA+EC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZpE,EAAE,UAAW,eAEhBmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAO3C,EAASS,QAChB+C,UAAU,gJACV8B,UAAQ,QAGZ5B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,gFAA+EC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZpE,EAAE,eAAgB,qBAErBmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAO3C,EAASU,aAChB8C,UAAU,gJACV8B,UAAQ,cAOhB5B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oKAAmKC,SAAA,EAChLC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,kFAAiFC,SAAA,EAC7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wDACZpE,EAAE,qBAAsB,2BAG3BmE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sGAAqGC,UAClHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,6CAA4CC,SAAA,EACvDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZpE,EAAE,yBAA0B,iFAIjCsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6FAA4FC,SAAA,EACzGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oEAAmEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACZpE,EAAE,cAAe,gBAAgB,SAEpCmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAO3C,EAASW,YAChByE,SAAWtB,GAAMrB,EAAkB,cAAeqB,EAAEuB,OAAO1C,OAC3Da,UAAS,sIAAArD,OACPsB,EAAOd,YAAc,iBAAmB,0CAE1C4E,YAAanG,EAAE,mBAAoB,yBACnCoG,UAAQ,IAET/D,EAAOd,cACN+C,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAOd,sBAOhB+C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oKAAmKC,SAAA,EAChLC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,+EAA8EC,SAAA,EAC1FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACZpE,EAAE,kBAAmB,wBAGvBY,EAASY,UAAUuC,OAAS,IAC3BO,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAASV,EACTM,UAAU,mLAAkLC,SAAA,EAE5LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZpE,EAAE,cAAe,wBAKxBmE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0GAAyGC,UACtHC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,+CAA8CC,SAAA,EACzDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZpE,EAAE,sBAAuB,kMAI9BmE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBzD,EAASY,UAAU6E,MAAM,EAAG,GAAG3C,IAAI,CAACC,EAAUF,KAC7Ca,EAAAA,EAAAA,MAAA,OAAuBF,UAAU,gGAA+FC,SAAA,EAC9HC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,6EAA4EC,SAAA,EACxFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oEACZpE,EAAE,WAAY,aAAa,IAAEyD,EAAQ,KAGvC7C,EAASY,UAAUuC,OAAS,IAC3BO,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAASA,IAAMR,EAAeP,GAC9BW,UAAU,iLAAgLC,SAAA,EAE1LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZpE,EAAE,SAAU,iBAKnBsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZpE,EAAE,OAAQ,QAAQ,SAErBmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAOI,EAASjC,KAChBsE,SAAWtB,GAAMlB,EAAqBC,EAAO,OAAQiB,EAAEuB,OAAO1C,OAC9Da,UAAS,kIAAArD,OACPsB,EAAO,YAADtB,OAAa0C,EAAK,UAAW,iBAAmB,wCAExD2C,UAAQ,IAET/D,EAAO,YAADtB,OAAa0C,EAAK,YACvBa,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAa0C,EAAK,iBAM/Ba,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZpE,EAAE,sBAAuB,wBAAwB,SAEpDmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAOI,EAAS/B,oBAChBoE,SAAWtB,GAAMlB,EAAqBC,EAAO,sBAAuBiB,EAAEuB,OAAO1C,OAC7Ea,UAAS,0IAAArD,OACPsB,EAAO,YAADtB,OAAa0C,EAAK,yBAA0B,iBAAmB,4CAEvE0C,YAAanG,EAAE,2BAA4B,iCAC3CoG,UAAQ,IAET/D,EAAO,YAADtB,OAAa0C,EAAK,2BACvBa,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAa0C,EAAK,mCAOjCa,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qGAAoGC,SAAA,EACjHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,sFAAqFC,SAAA,EACpGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8DACZpE,EAAE,4BAA6B,oCAAoC,SAEtEmE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAC3DrE,EAAE,uBAAwB,wHAE7BmE,EAAAA,EAAAA,KAAA,YACEZ,MAAOI,EAAShC,iBAChBqE,SAAWtB,GAAMlB,EAAqBC,EAAO,mBAAoBiB,EAAEuB,OAAO1C,OAC1Ea,UAAS,sIAAArD,OACPsB,EAAO,YAADtB,OAAa0C,EAAK,sBAAuB,iBAAmB,0CAEpE6C,KAAK,IACLH,YAAanG,EAAE,8BAA+B,uIAC9CoG,UAAQ,IAET/D,EAAO,YAADtB,OAAa0C,EAAK,wBACvBa,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAa0C,EAAK,6BAM/Ba,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yGAAwGC,SAAA,EACrHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEACZpE,EAAE,qBAAsB,uBAAuB,SAElDmE,EAAAA,EAAAA,KAAA,YACEZ,MAAOI,EAAS7B,mBAChBkE,SAAWtB,GAAMlB,EAAqBC,EAAO,qBAAsBiB,EAAEuB,OAAO1C,OAC5Ea,UAAS,0IAAArD,OACPsB,EAAO,YAADtB,OAAa0C,EAAK,wBAAyB,iBAAmB,4CAEtE6C,KAAK,IACLH,YAAanG,EAAE,gCAAiC,wHAChDoG,UAAQ,IAET/D,EAAO,YAADtB,OAAa0C,EAAK,0BACvBa,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAa0C,EAAK,+BAK/Ba,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EAEzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZpE,EAAE,yBAA0B,+BAA+B,SAE9DmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAOI,EAAS5B,iBAChBiE,SAAWtB,GAAMlB,EAAqBC,EAAO,mBAAoBiB,EAAEuB,OAAO1C,OAC1Ea,UAAS,kIAAArD,OACPsB,EAAO,YAADtB,OAAa0C,EAAK,sBAAuB,iBAAmB,wCAEpE2C,UAAQ,IAET/D,EAAO,YAADtB,OAAa0C,EAAK,wBACvBa,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAa0C,EAAK,6BAM/Ba,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZpE,EAAE,UAAW,eAEhBmE,EAAAA,EAAAA,KAAA,YACEZ,MAAOI,EAAS3B,QAChBgE,SAAWtB,GAAMlB,EAAqBC,EAAO,UAAWiB,EAAEuB,OAAO1C,OACjEa,UAAU,kLACVkC,KAAK,IACLH,YAAanG,EAAE,qBAAsB,0EA1JnC2D,EAASlC,aAsKZ,IAAhBhB,IACC0D,EAAAA,EAAAA,KAAA2B,EAAAA,SAAA,CAAAzB,UAEEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0JAAyJC,SAAA,EACtKC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2EAA0EC,SAAA,EACtFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZpE,EAAE,sBAAuB,mCAG3BY,EAASY,UAAUuC,OAAS,IAC3BO,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAASV,EACTM,UAAU,mLAAkLC,SAAA,EAE5LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZpE,EAAE,cAAe,wBAKxBmE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,2CAA0CC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZpE,EAAE,gCAAiC,uJAIxCsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,CACvBzD,EAASY,UAAU6E,MAAM,EAAG,GAAG3C,IAAI,CAACC,EAAUF,KAC7C,MAAM8C,EAAc9C,EAAQ,EAC5B,OACEa,EAAAA,EAAAA,MAAA,OAAuBF,UAAU,0FAAyFC,SAAA,EACxHC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,yEAAwEC,SAAA,EACpFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACZpE,EAAE,WAAY,aAAa,IAAEuG,EAAc,MAG9CjC,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAASA,IAAMR,EAAeuC,GAC9BnC,UAAU,iLAAgLC,SAAA,EAE1LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZpE,EAAE,SAAU,iBAKjBsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0DACZpE,EAAE,OAAQ,QAAQ,SAErBmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAOI,EAASjC,KAChBsE,SAAWtB,GAAMlB,EAAqB+C,EAAa,OAAQ7B,EAAEuB,OAAO1C,OACpEa,UAAS,kIAAArD,OACPsB,EAAO,YAADtB,OAAawF,EAAW,UAAW,iBAAmB,wCAE9DH,UAAQ,IAET/D,EAAO,YAADtB,OAAawF,EAAW,YAC7BjC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAawF,EAAW,iBAMrCjC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6DACZpE,EAAE,sBAAuB,wBAAwB,SAEpDmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAOI,EAAS/B,oBAChBoE,SAAWtB,GAAMlB,EAAqB+C,EAAa,sBAAuB7B,EAAEuB,OAAO1C,OACnFa,UAAS,0IAAArD,OACPsB,EAAO,YAADtB,OAAawF,EAAW,yBAA0B,iBAAmB,4CAE7EJ,YAAanG,EAAE,2BAA4B,iCAC3CoG,UAAQ,IAET/D,EAAO,YAADtB,OAAawF,EAAW,2BAC7BjC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAawF,EAAW,mCAOvCjC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qGAAoGC,SAAA,EACjHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,sFAAqFC,SAAA,EACpGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,8DACZpE,EAAE,4BAA6B,oCAAoC,SAEtEmE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAC3DrE,EAAE,uBAAwB,wHAE7BmE,EAAAA,EAAAA,KAAA,YACEZ,MAAOI,EAAShC,iBAChBqE,SAAWtB,GAAMlB,EAAqB+C,EAAa,mBAAoB7B,EAAEuB,OAAO1C,OAChFa,UAAS,sIAAArD,OACPsB,EAAO,YAADtB,OAAawF,EAAW,sBAAuB,iBAAmB,0CAE1ED,KAAK,IACLH,YAAanG,EAAE,8BAA+B,uIAC9CoG,UAAQ,IAET/D,EAAO,YAADtB,OAAawF,EAAW,wBAC7BjC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAawF,EAAW,6BAMrCjC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yGAAwGC,SAAA,EACrHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kEACZpE,EAAE,qBAAsB,uBAAuB,SAElDmE,EAAAA,EAAAA,KAAA,YACEZ,MAAOI,EAAS7B,mBAChBkE,SAAWtB,GAAMlB,EAAqB+C,EAAa,qBAAsB7B,EAAEuB,OAAO1C,OAClFa,UAAS,0IAAArD,OACPsB,EAAO,YAADtB,OAAawF,EAAW,wBAAyB,iBAAmB,4CAE5ED,KAAK,IACLH,YAAanG,EAAE,gCAAiC,wHAChDoG,UAAQ,IAET/D,EAAO,YAADtB,OAAawF,EAAW,0BAC7BjC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAawF,EAAW,+BAKrCjC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6CAA4CC,SAAA,EAEzDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oFAAmFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+DACZpE,EAAE,yBAA0B,+BAA+B,SAE9DmE,EAAAA,EAAAA,KAAA,SACE4B,KAAK,OACLxC,MAAOI,EAAS5B,iBAChBiE,SAAWtB,GAAMlB,EAAqB+C,EAAa,mBAAoB7B,EAAEuB,OAAO1C,OAChFa,UAAS,kIAAArD,OACPsB,EAAO,YAADtB,OAAawF,EAAW,sBAAuB,iBAAmB,wCAE1EH,UAAQ,IAET/D,EAAO,YAADtB,OAAawF,EAAW,wBAC7BjC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,8CAA6CC,SAAA,EACxDF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZ/B,EAAO,YAADtB,OAAawF,EAAW,6BAMrCjC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,wFAAuFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACZpE,EAAE,UAAW,eAEhBmE,EAAAA,EAAAA,KAAA,YACEZ,MAAOI,EAAS3B,QAChBgE,SAAWtB,GAAMlB,EAAqB+C,EAAa,UAAW7B,EAAEuB,OAAO1C,OACvEa,UAAU,kLACVkC,KAAK,IACLH,YAAanG,EAAE,qBAAsB,0EAzJnC2D,EAASlC,MAiKtBb,EAASY,UAAUuC,OAAS,IAC3BO,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wHAAuHC,SAAA,EACpIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEACbD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDrE,EAAE,wBAAyB,yCAE9BsE,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAASV,EACTM,UAAU,uLAAsLC,SAAA,EAEhMF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZpE,EAAE,6BAA8B,2CAU/CmE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sJAAqJC,UAClKC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAASA,IAAMjE,GAAU,GACzB6D,UAAU,6KAA4KC,SAAA,EAEtLF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BACZpE,EAAE,SAAU,aAGdS,EAAc,IACb6D,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QArmBCgC,KACX/F,EAAc,GAChBC,EAAeD,EAAc,IAomBjB2D,UAAU,mLAAkLC,SAAA,EAE5LF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BACZpE,EAAE,eAAgB,wBAKzBsE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAxsBM/B,UAClB,IACEL,GAAW,GAEX,MAAMqE,GAAOzD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRpC,GAAQ,IACX8F,aAAa,IAAI1F,MAAOG,cACxBwF,YAAaxG,EAAK0B,MAAQ1B,EAAKyG,MAC/BvG,UAAWA,IAGPqC,QAAiBC,MAAM,6BAA8B,CACzD4C,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADzE,OAAY8F,aAAaC,QAAQ,WAElDrB,KAAMC,KAAKC,UAAUc,KAGvB,IAAI/D,EAASE,GAaX,MAAM,IAAIgD,MAAM,uBAAD7E,OAAwB2B,EAASqE,SAbjC,CACf,MAAMC,QAAatE,EAASsE,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,kBAAA1G,OAAqBH,EAASW,YAAYmG,QAAQ,OAAQ,KAAI,KAAA3G,OAAIH,EAASM,UAAS,QAC9FoG,SAAS7B,KAAKkC,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAAS7B,KAAKqC,YAAYT,GAE1BxB,MAAM7F,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAOmD,GACPC,QAAQD,MAAM,wBAAyBA,GACvC0C,MAAM7F,EAAE,qBAAsB,2CAChC,CAAC,QACCoC,GAAW,EACb,GAiqBY2F,SAAU5F,EACViC,UAAU,yNAAwNC,SAAA,EAElOF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZjC,EAAUnC,EAAE,aAAc,iBAAmBA,EAAE,cAAe,mBAGhES,EAAc,GACb6D,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLvB,QAloBCwD,KACXvH,EAAc,GAChBC,EAAeD,EAAc,IAioBjB2D,UAAU,+KAA8KC,SAAA,CAEvLrE,EAAE,WAAY,cACfmE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kCAGfE,EAAAA,EAAAA,MAAA,UACEyB,KAAK,SACLgC,SAAU5F,EACViC,UAAU,iOAAgOC,SAAA,EAE1OF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZjC,EAAUnC,EAAE,SAAU,aAAeA,EAAE,mBAAoB,wC", "sources": ["pages/Forms/FollowUpPlanForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useParams, useNavigate } from 'react-router-dom';\n\nconst FollowUpPlanForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [currentPage, setCurrentPage] = useState(1);\n  const [formData, setFormData] = useState({\n    // Document Metadata\n    documentNumber: `QP-${Date.now()}`,\n    issueDate: new Date().toISOString().split('T')[0],\n    version: '01',\n    reviewNumber: '01',\n    \n    // Patient Information\n    patientName: '',\n    \n    // Follow-up Entries (up to 4)\n    followUps: [\n      {\n        id: 1,\n        date: '',\n        currentSituation: '',\n        physiotherapistName: user?.name || '',\n        treatmentProposals: '',\n        nextFollowUpDate: '',\n        remarks: ''\n      }\n    ],\n    \n    // Metadata\n    submittedBy: user?.id || '',\n    submittedAt: new Date().toISOString()\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        setFormData(prev => ({\n          ...prev,\n          patientName: patientData.name || `${patientData.firstName} ${patientData.lastName}` || ''\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleFollowUpChange = (index, field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      followUps: prev.followUps.map((followUp, i) => \n        i === index ? { ...followUp, [field]: value } : followUp\n      )\n    }));\n    \n    const errorKey = `followUp_${index}_${field}`;\n    if (errors[errorKey]) {\n      setErrors(prev => ({\n        ...prev,\n        [errorKey]: null\n      }));\n    }\n  };\n\n  const addFollowUp = () => {\n    if (formData.followUps.length < 4) {\n      setFormData(prev => ({\n        ...prev,\n        followUps: [\n          ...prev.followUps,\n          {\n            id: prev.followUps.length + 1,\n            date: '',\n            currentSituation: '',\n            physiotherapistName: user?.name || '',\n            treatmentProposals: '',\n            nextFollowUpDate: '',\n            remarks: ''\n          }\n        ]\n      }));\n    }\n  };\n\n  const removeFollowUp = (index) => {\n    if (formData.followUps.length > 1) {\n      setFormData(prev => ({\n        ...prev,\n        followUps: prev.followUps.filter((_, i) => i !== index)\n      }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n\n    // Validate each follow-up\n    formData.followUps.forEach((followUp, index) => {\n      if (!followUp.date) {\n        newErrors[`followUp_${index}_date`] = t('dateRequired', 'Date is required');\n      } else {\n        const followUpDate = new Date(followUp.date);\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        if (followUpDate > today) {\n          newErrors[`followUp_${index}_date`] = t('dateMustBePastOrPresent', 'Date must be in the past or present');\n        }\n      }\n\n      if (!followUp.currentSituation.trim()) {\n        newErrors[`followUp_${index}_currentSituation`] = t('currentSituationRequired', 'Current situation is required');\n      }\n\n      if (!followUp.physiotherapistName.trim()) {\n        newErrors[`followUp_${index}_physiotherapistName`] = t('physiotherapistNameRequired', 'Physiotherapist name is required');\n      }\n\n      if (!followUp.treatmentProposals.trim()) {\n        newErrors[`followUp_${index}_treatmentProposals`] = t('treatmentProposalsRequired', 'Treatment proposals are required');\n      }\n\n      if (!followUp.nextFollowUpDate) {\n        newErrors[`followUp_${index}_nextFollowUpDate`] = t('nextFollowUpDateRequired', 'Next follow-up date is required');\n      } else {\n        const nextDate = new Date(followUp.nextFollowUpDate);\n        const today = new Date();\n        today.setHours(23, 59, 59, 999);\n        if (nextDate <= today) {\n          newErrors[`followUp_${index}_nextFollowUpDate`] = t('nextDateMustBeFuture', 'Next follow-up date must be in the future');\n        }\n      }\n    });\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n      \n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/follow-up-plan/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `follow-up-plan-${formData.patientName.replace(/\\s+/g, '-')}-${formData.issueDate}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n        \n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      const submissionData = {\n        ...formData,\n        submittedBy: user.id,\n        submittedAt: new Date().toISOString()\n      };\n\n      const response = await fetch('/api/v1/follow-up-plan/public', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        alert(t('followUpPlanSaved', 'Follow-up plan saved successfully!'));\n        navigate(patientId ? `/patients/${patientId}` : '/patients');\n      } else {\n        throw new Error('Failed to save follow-up plan');\n      }\n    } catch (error) {\n      console.error('Error saving follow-up plan:', error);\n      alert(t('errorSaving', 'Error saving follow-up plan. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const nextPage = () => {\n    if (currentPage < 2) {\n      setCurrentPage(currentPage + 1);\n    }\n  };\n\n  const prevPage = () => {\n    if (currentPage > 1) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 rounded-lg shadow-lg mb-6\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 bg-clip-text text-transparent\">\n                  {t('followUpPlanForDoctor', 'Follow Up Plan for Doctor')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-user-md text-emerald-500 mr-2\"></i>\n                  {t('followUpPlanDescription', 'Comprehensive follow-up planning and progress tracking for medical review')}\n                </p>\n                <div className=\"flex items-center space-x-4 mt-3\">\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-clock text-blue-500 mr-1\"></i>\n                    {t('estimatedTime', '15-25 minutes')}\n                  </span>\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-file-alt text-green-500 mr-1\"></i>\n                    {t('pages', '2 pages')}\n                  </span>\n                  <span className=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                    <i className=\"fas fa-repeat text-purple-500 mr-1\"></i>\n                    {t('repeatableEntries', 'Up to 4 follow-ups')}\n                  </span>\n                </div>\n              </div>\n              <div className=\"flex flex-col items-center space-y-2\">\n                <div className=\"bg-gradient-to-r from-emerald-400 to-teal-400 text-white px-4 py-2 rounded-full shadow-lg\">\n                  <i className=\"fas fa-stethoscope mr-2\"></i>\n                  {t('medical', 'Medical')}\n                </div>\n                <div className=\"bg-gradient-to-r from-blue-400 to-indigo-400 text-white px-3 py-1 rounded-full text-sm shadow-md\">\n                  <i className=\"fas fa-calendar-check mr-1\"></i>\n                  {t('followUp', 'Follow-up')}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Page Navigation */}\n      <div className=\"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-indigo-200 dark:border-indigo-700 p-4 mb-6\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex space-x-2\">\n            {[1, 2].map((page) => (\n              <button\n                key={page}\n                onClick={() => setCurrentPage(page)}\n                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${\n                  currentPage === page\n                    ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg'\n                    : 'bg-white dark:bg-gray-700 text-indigo-600 dark:text-indigo-400 border border-indigo-200 dark:border-indigo-600 hover:bg-indigo-50 dark:hover:bg-indigo-900/30'\n                }`}\n              >\n                <i className={`fas ${page === 1 ? 'fa-user' : 'fa-calendar-alt'} mr-2`}></i>\n                {page === 1 ? t('patientInfo', 'Patient Info & Follow-ups') : t('additionalFollowUps', 'Additional Follow-ups')}\n              </button>\n            ))}\n          </div>\n          <div className=\"text-sm text-indigo-600 dark:text-indigo-400 font-medium\">\n            {t('page', 'Page')} {currentPage} {t('of', 'of')} 2\n          </div>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Page 1: Patient Information and First Follow-ups */}\n        {currentPage === 1 && (\n          <>\n            {/* Document Header */}\n            <div className=\"bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-lg shadow-lg border border-cyan-200 dark:border-cyan-700 p-6\">\n              <h2 className=\"text-lg font-semibold text-cyan-900 dark:text-cyan-100 mb-4 flex items-center\">\n                <i className=\"fas fa-file-alt text-cyan-600 dark:text-cyan-400 mr-2\"></i>\n                {t('documentInformation', 'Document Information')}\n              </h2>\n              \n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\">\n                <div className=\"bg-white dark:bg-cyan-800/20 border border-cyan-200 dark:border-cyan-600 rounded-lg p-3\">\n                  <label className=\"block font-medium text-cyan-800 dark:text-cyan-200 mb-2 flex items-center\">\n                    <i className=\"fas fa-hashtag text-cyan-600 dark:text-cyan-400 mr-1\"></i>\n                    {t('documentNumber', 'Document Number')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.documentNumber}\n                    onChange={(e) => handleInputChange('documentNumber', e.target.value)}\n                    className=\"w-full px-2 py-1 border border-cyan-300 dark:border-cyan-600 rounded bg-cyan-50 dark:bg-cyan-700 text-cyan-900 dark:text-cyan-100\"\n                    readOnly\n                  />\n                </div>\n                <div className=\"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-3\">\n                  <label className=\"block font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n                    <i className=\"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1\"></i>\n                    {t('issueDate', 'Issue Date')}\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.issueDate}\n                    onChange={(e) => handleInputChange('issueDate', e.target.value)}\n                    className=\"w-full px-2 py-1 border border-blue-300 dark:border-blue-600 rounded bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                <div className=\"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-3\">\n                  <label className=\"block font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center\">\n                    <i className=\"fas fa-code-branch text-indigo-600 dark:text-indigo-400 mr-1\"></i>\n                    {t('version', 'Version')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.version}\n                    className=\"w-full px-2 py-1 border border-indigo-300 dark:border-indigo-600 rounded bg-indigo-50 dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100\"\n                    readOnly\n                  />\n                </div>\n                <div className=\"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-3\">\n                  <label className=\"block font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center\">\n                    <i className=\"fas fa-eye text-purple-600 dark:text-purple-400 mr-1\"></i>\n                    {t('reviewNumber', 'Review Number')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={formData.reviewNumber}\n                    className=\"w-full px-2 py-1 border border-purple-300 dark:border-purple-600 rounded bg-purple-50 dark:bg-purple-700 text-purple-900 dark:text-purple-100\"\n                    readOnly\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Patient Information */}\n            <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6\">\n              <h2 className=\"text-lg font-semibold text-green-900 dark:text-green-100 mb-6 flex items-center\">\n                <i className=\"fas fa-user text-green-600 dark:text-green-400 mr-2\"></i>\n                {t('patientInformation', 'Patient Information')}\n              </h2>\n              \n              <div className=\"bg-green-100 dark:bg-green-800/30 border border-green-300 dark:border-green-600 rounded-lg p-4 mb-6\">\n                <p className=\"text-sm text-green-800 dark:text-green-200\">\n                  <i className=\"fas fa-info-circle text-green-600 dark:text-green-400 mr-2\"></i>\n                  {t('patientInfoInstruction', 'Enter the patient\\'s name for whom this follow-up plan is being created.')}\n                </p>\n              </div>\n\n              <div className=\"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4\">\n                <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2\">\n                  <i className=\"fas fa-user-tag text-green-600 dark:text-green-400 mr-1\"></i>\n                  {t('patientName', 'Patient Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.patientName}\n                  onChange={(e) => handleInputChange('patientName', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ${\n                    errors.patientName ? 'border-red-500' : 'border-green-300 dark:border-green-600'\n                  }`}\n                  placeholder={t('enterPatientName', 'Enter patient name...')}\n                  required\n                />\n                {errors.patientName && (\n                  <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                    <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                    {errors.patientName}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* Follow-up Entries */}\n            <div className=\"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-lg font-semibold text-orange-900 dark:text-orange-100 flex items-center\">\n                  <i className=\"fas fa-calendar-check text-orange-600 dark:text-orange-400 mr-2\"></i>\n                  {t('followUpEntries', 'Follow-up Entries')}\n                </h2>\n\n                {formData.followUps.length < 4 && (\n                  <button\n                    type=\"button\"\n                    onClick={addFollowUp}\n                    className=\"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg flex items-center\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('addFollowUp', 'Add Follow-up')}\n                  </button>\n                )}\n              </div>\n\n              <div className=\"bg-orange-100 dark:bg-orange-800/30 border border-orange-300 dark:border-orange-600 rounded-lg p-4 mb-6\">\n                <p className=\"text-sm text-orange-800 dark:text-orange-200\">\n                  <i className=\"fas fa-info-circle text-orange-600 dark:text-orange-400 mr-2\"></i>\n                  {t('followUpInstruction', 'Document up to 4 follow-up sessions with detailed progress notes and treatment plans. Each entry should include current situation, treatment proposals, and next appointment scheduling.')}\n                </p>\n              </div>\n\n              <div className=\"space-y-6\">\n                {formData.followUps.slice(0, 2).map((followUp, index) => (\n                  <div key={followUp.id} className=\"bg-white dark:bg-orange-800/20 border border-orange-200 dark:border-orange-600 rounded-lg p-6\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"text-lg font-medium text-orange-900 dark:text-orange-100 flex items-center\">\n                        <i className=\"fas fa-clipboard-list text-orange-600 dark:text-orange-400 mr-2\"></i>\n                        {t('followUp', 'Follow-up')} {index + 1}\n                      </h3>\n\n                      {formData.followUps.length > 1 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeFollowUp(index)}\n                          className=\"px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md flex items-center text-sm\"\n                        >\n                          <i className=\"fas fa-trash mr-1\"></i>\n                          {t('remove', 'Remove')}\n                        </button>\n                      )}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                      {/* Date */}\n                      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4\">\n                        <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n                          <i className=\"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1\"></i>\n                          {t('date', 'Date')} *\n                        </label>\n                        <input\n                          type=\"date\"\n                          value={followUp.date}\n                          onChange={(e) => handleFollowUpChange(index, 'date', e.target.value)}\n                          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ${\n                            errors[`followUp_${index}_date`] ? 'border-red-500' : 'border-blue-300 dark:border-blue-600'\n                          }`}\n                          required\n                        />\n                        {errors[`followUp_${index}_date`] && (\n                          <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                            <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                            {errors[`followUp_${index}_date`]}\n                          </p>\n                        )}\n                      </div>\n\n                      {/* Physiotherapist Name */}\n                      <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4\">\n                        <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center\">\n                          <i className=\"fas fa-user-md text-purple-600 dark:text-purple-400 mr-1\"></i>\n                          {t('physiotherapistName', 'Physiotherapist Name')} *\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={followUp.physiotherapistName}\n                          onChange={(e) => handleFollowUpChange(index, 'physiotherapistName', e.target.value)}\n                          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500 ${\n                            errors[`followUp_${index}_physiotherapistName`] ? 'border-red-500' : 'border-purple-300 dark:border-purple-600'\n                          }`}\n                          placeholder={t('enterPhysiotherapistName', 'Enter physiotherapist name...')}\n                          required\n                        />\n                        {errors[`followUp_${index}_physiotherapistName`] && (\n                          <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                            <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                            {errors[`followUp_${index}_physiotherapistName`]}\n                          </p>\n                        )}\n                      </div>\n                    </div>\n\n                    {/* Current Situation */}\n                    <div className=\"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4\">\n                      <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center\">\n                        <i className=\"fas fa-chart-line text-green-600 dark:text-green-400 mr-1\"></i>\n                        {t('currentSituationOfPatient', 'Current Situation of the Patient')} *\n                      </label>\n                      <p className=\"text-xs text-green-600 dark:text-green-400 mb-2\">\n                        {t('currentSituationHint', 'Include: Improvement-goals achieved, functional status, ROM-Muscle strength etc., compared to previous assessment')}\n                      </p>\n                      <textarea\n                        value={followUp.currentSituation}\n                        onChange={(e) => handleFollowUpChange(index, 'currentSituation', e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ${\n                          errors[`followUp_${index}_currentSituation`] ? 'border-red-500' : 'border-green-300 dark:border-green-600'\n                        }`}\n                        rows=\"4\"\n                        placeholder={t('currentSituationPlaceholder', 'Describe the patient\\'s current condition, progress made, functional improvements, ROM measurements, muscle strength changes, etc...')}\n                        required\n                      />\n                      {errors[`followUp_${index}_currentSituation`] && (\n                        <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                          <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                          {errors[`followUp_${index}_currentSituation`]}\n                        </p>\n                      )}\n                    </div>\n\n                    {/* Treatment Proposals */}\n                    <div className=\"mt-6 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4\">\n                      <label className=\"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center\">\n                        <i className=\"fas fa-prescription text-indigo-600 dark:text-indigo-400 mr-1\"></i>\n                        {t('treatmentProposals', 'Treatment Proposals')} *\n                      </label>\n                      <textarea\n                        value={followUp.treatmentProposals}\n                        onChange={(e) => handleFollowUpChange(index, 'treatmentProposals', e.target.value)}\n                        className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500 ${\n                          errors[`followUp_${index}_treatmentProposals`] ? 'border-red-500' : 'border-indigo-300 dark:border-indigo-600'\n                        }`}\n                        rows=\"4\"\n                        placeholder={t('treatmentProposalsPlaceholder', 'Outline recommended treatments, interventions, modifications to current plan, new exercises, equipment needs, etc...')}\n                        required\n                      />\n                      {errors[`followUp_${index}_treatmentProposals`] && (\n                        <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                          <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                          {errors[`followUp_${index}_treatmentProposals`]}\n                        </p>\n                      )}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\">\n                      {/* Next Follow-up Date */}\n                      <div className=\"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-600 rounded-lg p-4\">\n                        <label className=\"block text-sm font-medium text-teal-800 dark:text-teal-200 mb-2 flex items-center\">\n                          <i className=\"fas fa-calendar-plus text-teal-600 dark:text-teal-400 mr-1\"></i>\n                          {t('nextFollowUpOutpatient', 'Next Follow Up (Outpatient)')} *\n                        </label>\n                        <input\n                          type=\"date\"\n                          value={followUp.nextFollowUpDate}\n                          onChange={(e) => handleFollowUpChange(index, 'nextFollowUpDate', e.target.value)}\n                          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100 focus:ring-2 focus:ring-teal-500 ${\n                            errors[`followUp_${index}_nextFollowUpDate`] ? 'border-red-500' : 'border-teal-300 dark:border-teal-600'\n                          }`}\n                          required\n                        />\n                        {errors[`followUp_${index}_nextFollowUpDate`] && (\n                          <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                            <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                            {errors[`followUp_${index}_nextFollowUpDate`]}\n                          </p>\n                        )}\n                      </div>\n\n                      {/* Remarks */}\n                      <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4\">\n                        <label className=\"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center\">\n                          <i className=\"fas fa-sticky-note text-yellow-600 dark:text-yellow-400 mr-1\"></i>\n                          {t('remarks', 'Remarks')}\n                        </label>\n                        <textarea\n                          value={followUp.remarks}\n                          onChange={(e) => handleFollowUpChange(index, 'remarks', e.target.value)}\n                          className=\"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500\"\n                          rows=\"3\"\n                          placeholder={t('remarksPlaceholder', 'Additional notes, observations, or special considerations...')}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Page 2: Additional Follow-ups */}\n        {currentPage === 2 && (\n          <>\n            {/* Additional Follow-up Entries */}\n            <div className=\"bg-gradient-to-r from-rose-50 to-pink-50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-rose-200 dark:border-rose-700 p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <h2 className=\"text-lg font-semibold text-rose-900 dark:text-rose-100 flex items-center\">\n                  <i className=\"fas fa-calendar-week text-rose-600 dark:text-rose-400 mr-2\"></i>\n                  {t('additionalFollowUps', 'Additional Follow-up Entries')}\n                </h2>\n\n                {formData.followUps.length < 4 && (\n                  <button\n                    type=\"button\"\n                    onClick={addFollowUp}\n                    className=\"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg flex items-center\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('addFollowUp', 'Add Follow-up')}\n                  </button>\n                )}\n              </div>\n\n              <div className=\"bg-rose-100 dark:bg-rose-800/30 border border-rose-300 dark:border-rose-600 rounded-lg p-4 mb-6\">\n                <p className=\"text-sm text-rose-800 dark:text-rose-200\">\n                  <i className=\"fas fa-info-circle text-rose-600 dark:text-rose-400 mr-2\"></i>\n                  {t('additionalFollowUpInstruction', 'Continue documenting follow-up sessions. Each entry builds upon previous assessments to track long-term progress and treatment effectiveness.')}\n                </p>\n              </div>\n\n              <div className=\"space-y-6\">\n                {formData.followUps.slice(2, 4).map((followUp, index) => {\n                  const actualIndex = index + 2;\n                  return (\n                    <div key={followUp.id} className=\"bg-white dark:bg-rose-800/20 border border-rose-200 dark:border-rose-600 rounded-lg p-6\">\n                      <div className=\"flex items-center justify-between mb-4\">\n                        <h3 className=\"text-lg font-medium text-rose-900 dark:text-rose-100 flex items-center\">\n                          <i className=\"fas fa-clipboard-list text-rose-600 dark:text-rose-400 mr-2\"></i>\n                          {t('followUp', 'Follow-up')} {actualIndex + 1}\n                        </h3>\n\n                        <button\n                          type=\"button\"\n                          onClick={() => removeFollowUp(actualIndex)}\n                          className=\"px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md flex items-center text-sm\"\n                        >\n                          <i className=\"fas fa-trash mr-1\"></i>\n                          {t('remove', 'Remove')}\n                        </button>\n                      </div>\n\n                      {/* Same structure as page 1 follow-ups */}\n                      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                        {/* Date */}\n                        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4\">\n                          <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center\">\n                            <i className=\"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1\"></i>\n                            {t('date', 'Date')} *\n                          </label>\n                          <input\n                            type=\"date\"\n                            value={followUp.date}\n                            onChange={(e) => handleFollowUpChange(actualIndex, 'date', e.target.value)}\n                            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ${\n                              errors[`followUp_${actualIndex}_date`] ? 'border-red-500' : 'border-blue-300 dark:border-blue-600'\n                            }`}\n                            required\n                          />\n                          {errors[`followUp_${actualIndex}_date`] && (\n                            <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                              <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                              {errors[`followUp_${actualIndex}_date`]}\n                            </p>\n                          )}\n                        </div>\n\n                        {/* Physiotherapist Name */}\n                        <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4\">\n                          <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center\">\n                            <i className=\"fas fa-user-md text-purple-600 dark:text-purple-400 mr-1\"></i>\n                            {t('physiotherapistName', 'Physiotherapist Name')} *\n                          </label>\n                          <input\n                            type=\"text\"\n                            value={followUp.physiotherapistName}\n                            onChange={(e) => handleFollowUpChange(actualIndex, 'physiotherapistName', e.target.value)}\n                            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500 ${\n                              errors[`followUp_${actualIndex}_physiotherapistName`] ? 'border-red-500' : 'border-purple-300 dark:border-purple-600'\n                            }`}\n                            placeholder={t('enterPhysiotherapistName', 'Enter physiotherapist name...')}\n                            required\n                          />\n                          {errors[`followUp_${actualIndex}_physiotherapistName`] && (\n                            <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                              <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                              {errors[`followUp_${actualIndex}_physiotherapistName`]}\n                            </p>\n                          )}\n                        </div>\n                      </div>\n\n                      {/* Current Situation */}\n                      <div className=\"mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4\">\n                        <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center\">\n                          <i className=\"fas fa-chart-line text-green-600 dark:text-green-400 mr-1\"></i>\n                          {t('currentSituationOfPatient', 'Current Situation of the Patient')} *\n                        </label>\n                        <p className=\"text-xs text-green-600 dark:text-green-400 mb-2\">\n                          {t('currentSituationHint', 'Include: Improvement-goals achieved, functional status, ROM-Muscle strength etc., compared to previous assessment')}\n                        </p>\n                        <textarea\n                          value={followUp.currentSituation}\n                          onChange={(e) => handleFollowUpChange(actualIndex, 'currentSituation', e.target.value)}\n                          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ${\n                            errors[`followUp_${actualIndex}_currentSituation`] ? 'border-red-500' : 'border-green-300 dark:border-green-600'\n                          }`}\n                          rows=\"4\"\n                          placeholder={t('currentSituationPlaceholder', 'Describe the patient\\'s current condition, progress made, functional improvements, ROM measurements, muscle strength changes, etc...')}\n                          required\n                        />\n                        {errors[`followUp_${actualIndex}_currentSituation`] && (\n                          <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                            <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                            {errors[`followUp_${actualIndex}_currentSituation`]}\n                          </p>\n                        )}\n                      </div>\n\n                      {/* Treatment Proposals */}\n                      <div className=\"mt-6 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4\">\n                        <label className=\"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center\">\n                          <i className=\"fas fa-prescription text-indigo-600 dark:text-indigo-400 mr-1\"></i>\n                          {t('treatmentProposals', 'Treatment Proposals')} *\n                        </label>\n                        <textarea\n                          value={followUp.treatmentProposals}\n                          onChange={(e) => handleFollowUpChange(actualIndex, 'treatmentProposals', e.target.value)}\n                          className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500 ${\n                            errors[`followUp_${actualIndex}_treatmentProposals`] ? 'border-red-500' : 'border-indigo-300 dark:border-indigo-600'\n                          }`}\n                          rows=\"4\"\n                          placeholder={t('treatmentProposalsPlaceholder', 'Outline recommended treatments, interventions, modifications to current plan, new exercises, equipment needs, etc...')}\n                          required\n                        />\n                        {errors[`followUp_${actualIndex}_treatmentProposals`] && (\n                          <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                            <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                            {errors[`followUp_${actualIndex}_treatmentProposals`]}\n                          </p>\n                        )}\n                      </div>\n\n                      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6\">\n                        {/* Next Follow-up Date */}\n                        <div className=\"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-600 rounded-lg p-4\">\n                          <label className=\"block text-sm font-medium text-teal-800 dark:text-teal-200 mb-2 flex items-center\">\n                            <i className=\"fas fa-calendar-plus text-teal-600 dark:text-teal-400 mr-1\"></i>\n                            {t('nextFollowUpOutpatient', 'Next Follow Up (Outpatient)')} *\n                          </label>\n                          <input\n                            type=\"date\"\n                            value={followUp.nextFollowUpDate}\n                            onChange={(e) => handleFollowUpChange(actualIndex, 'nextFollowUpDate', e.target.value)}\n                            className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100 focus:ring-2 focus:ring-teal-500 ${\n                              errors[`followUp_${actualIndex}_nextFollowUpDate`] ? 'border-red-500' : 'border-teal-300 dark:border-teal-600'\n                            }`}\n                            required\n                          />\n                          {errors[`followUp_${actualIndex}_nextFollowUpDate`] && (\n                            <p className=\"text-red-500 text-sm mt-1 flex items-center\">\n                              <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                              {errors[`followUp_${actualIndex}_nextFollowUpDate`]}\n                            </p>\n                          )}\n                        </div>\n\n                        {/* Remarks */}\n                        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4\">\n                          <label className=\"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center\">\n                            <i className=\"fas fa-sticky-note text-yellow-600 dark:text-yellow-400 mr-1\"></i>\n                            {t('remarks', 'Remarks')}\n                          </label>\n                          <textarea\n                            value={followUp.remarks}\n                            onChange={(e) => handleFollowUpChange(actualIndex, 'remarks', e.target.value)}\n                            className=\"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500\"\n                            rows=\"3\"\n                            placeholder={t('remarksPlaceholder', 'Additional notes, observations, or special considerations...')}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  );\n                })}\n\n                {formData.followUps.length < 3 && (\n                  <div className=\"bg-gray-50 dark:bg-gray-800/50 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center\">\n                    <i className=\"fas fa-plus-circle text-4xl text-gray-400 dark:text-gray-500 mb-4\"></i>\n                    <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                      {t('noAdditionalFollowUps', 'No additional follow-ups added yet')}\n                    </p>\n                    <button\n                      type=\"button\"\n                      onClick={addFollowUp}\n                      className=\"px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg flex items-center mx-auto\"\n                    >\n                      <i className=\"fas fa-plus mr-2\"></i>\n                      {t('addFirstAdditionalFollowUp', 'Add Additional Follow-up')}\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </>\n        )}\n\n        {/* Navigation Buttons */}\n        <div className=\"bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex space-x-3\">\n              <button\n                type=\"button\"\n                onClick={() => navigate(-1)}\n                className=\"px-6 py-3 bg-gradient-to-r from-gray-500 to-slate-500 text-white rounded-lg hover:from-gray-600 hover:to-slate-600 transition-all duration-200 shadow-lg flex items-center\"\n              >\n                <i className=\"fas fa-arrow-left mr-2\"></i>\n                {t('cancel', 'Cancel')}\n              </button>\n              \n              {currentPage > 1 && (\n                <button\n                  type=\"button\"\n                  onClick={prevPage}\n                  className=\"px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 shadow-lg flex items-center\"\n                >\n                  <i className=\"fas fa-chevron-left mr-2\"></i>\n                  {t('previousPage', 'Previous Page')}\n                </button>\n              )}\n            </div>\n            \n            <div className=\"flex space-x-4\">\n              <button\n                type=\"button\"\n                onClick={generatePDF}\n                disabled={loading}\n                className=\"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {loading ? t('generating', 'Generating...') : t('generatePDF', 'Generate PDF')}\n              </button>\n              \n              {currentPage < 2 ? (\n                <button\n                  type=\"button\"\n                  onClick={nextPage}\n                  className=\"px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg flex items-center\"\n                >\n                  {t('nextPage', 'Next Page')}\n                  <i className=\"fas fa-chevron-right ml-2\"></i>\n                </button>\n              ) : (\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"px-8 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 text-white rounded-lg hover:from-emerald-600 hover:to-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg\"\n                >\n                  <i className=\"fas fa-save mr-2\"></i>\n                  {loading ? t('saving', 'Saving...') : t('saveFollowUpPlan', 'Save Follow-up Plan')}\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default FollowUpPlanForm;\n"], "names": ["FollowUpPlanForm", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "currentPage", "setCurrentPage", "useState", "formData", "setFormData", "documentNumber", "concat", "Date", "now", "issueDate", "toISOString", "split", "version", "reviewNumber", "patientName", "followUps", "id", "date", "currentSituation", "physiotherapist<PERSON>ame", "name", "treatmentProposals", "nextFollowUpDate", "remarks", "submittedBy", "submittedAt", "loading", "setLoading", "errors", "setErrors", "useEffect", "loadPatientData", "async", "response", "fetch", "ok", "patientData", "json", "prev", "_objectSpread", "firstName", "lastName", "error", "console", "handleInputChange", "field", "value", "handleFollowUpChange", "index", "map", "followUp", "i", "<PERSON><PERSON><PERSON>", "addFollowUp", "length", "removeFollowUp", "filter", "_", "_jsx", "className", "children", "_jsxs", "page", "onClick", "onSubmit", "e", "preventDefault", "validateForm", "newErrors", "trim", "for<PERSON>ach", "followUpDate", "today", "setHours", "nextDate", "Object", "keys", "submissionData", "method", "headers", "body", "JSON", "stringify", "Error", "alert", "_Fragment", "type", "onChange", "target", "readOnly", "placeholder", "required", "slice", "rows", "actualIndex", "prevPage", "pdfData", "generatedAt", "generatedBy", "email", "localStorage", "getItem", "status", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "nextPage"], "sourceRoot": ""}