{"version": 3, "file": "static/js/1549.cf8b44b0.chunk.js", "mappings": "iOAMA,MAmZA,EAnZ2BA,KACzB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MACX,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OAEVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CAEvCC,YAAa,GACbP,UAAWA,GAAa,GACxBQ,gBAAgB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACpDC,cAAkB,OAAJd,QAAI,IAAJA,OAAI,EAAJA,EAAMe,OAAQ,GAG5BC,iBAAkB,EAClBC,iBAAkB,EAClBC,eAAgB,EAChBC,cAAe,EACfC,cAAe,GAGfC,YAAa,GACbC,YAAa,GACbC,UAAW,GACXC,aAAc,GACdC,cAAe,GAGfC,mBAAoB,GACpBC,iBAAkB,GAGlBC,iBAAkB,GAClBC,eAAgB,GAGhBC,YAAa,EACbC,WAAY,EACZC,sBAAuB,EACvBC,WAAY,EACZC,aAAc,EAGdC,mBAAoB,GACpBC,mBAAoB,GACpBC,uBAAwB,GAGxBC,UAAW,GACXC,gBAAiB,GACjBC,sBAAuB,GAGvBC,YAAa,CAAC,EAGdC,gBAAiB,GACjBC,kBAAmB,GACnBC,oBAAoB,KAGfC,EAASC,IAActC,EAAAA,EAAAA,WAAS,IAChCuC,EAAQC,IAAaxC,EAAAA,EAAAA,UAAS,CAAC,IAiBtCyC,EAAAA,EAAAA,WAAU,KACJ/C,GACFgD,KAED,CAAChD,IAEJ,MAAMgD,EAAkBC,UACtB,IACEL,GAAW,GACX,MAAMM,QAAiBC,MAAM,iBAADC,OAAkBpD,IAC9C,GAAIkD,EAASG,GAAI,CACf,MAAMC,QAAoBJ,EAASK,OACnClD,EAAYmD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPjD,YAAa+C,EAAYzC,MAAQ,GACjCb,UAAWsD,EAAYI,KAAO1D,IAElC,CACF,CAAE,MAAO2D,GACPC,QAAQD,MAAM,8BAA+BA,EAC/C,CAAC,QACCf,GAAW,EACb,GAGIiB,EAAoBA,CAACC,EAAOC,KAChC1D,EAAYmD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACM,GAAQC,KAGPlB,EAAOiB,IACThB,EAAUU,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,GAAI,IACP,CAACM,GAAQ,SAmHf,OAAInB,GAEAqB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kGAAiGC,UAC9GF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7DvE,EAAE,sBAAuB,4BAE5BqE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDvE,EAAE,4BAA6B,oDAGpCqE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,8FAA6FC,SAAA,EAC3GF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qCACZtE,EAAE,iBAAkB,gCAQ/BwE,EAAAA,EAAAA,MAAA,QAAMC,SAvEWnB,UAGnB,GAFAoB,EAAEC,iBAzDiBC,MACnB,MAAMC,EAAY,CAAC,EASnB,OAPKpE,EAASG,YAAYkE,SAAQD,EAAUjE,YAAcZ,EAAE,sBAAuB,6BAC9ES,EAASQ,aAAa6D,SAAQD,EAAU5D,aAAejB,EAAE,uBAAwB,+BAClFS,EAASU,iBAAmB,GAAKV,EAASU,iBAAmB,MAC/D0D,EAAU1D,iBAAmBnB,EAAE,iBAAkB,oCAGnDmD,EAAU0B,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWI,QAiDzBL,GAIL,IACE3B,GAAW,GAEX,MAAMiC,GAAcpB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfrD,GAAQ,IACX0E,YAAahF,EAAKiF,GAClBC,aAAa,IAAIvE,MAAOC,gBAW1B,WARuByC,MAAM,kCAAmC,CAC9D8B,OAAQ,OACRC,QAAS,CACP,eAAgB,oBAElBC,KAAMC,KAAKC,UAAUR,MAGVxB,GAIX,MAAM,IAAIiC,MAAM,kCAHhBC,MAAM5F,EAAE,sBAAuB,wCAC/BO,EAASF,EAAS,aAAAoD,OAAgBpD,GAAc,YAIpD,CAAE,MAAO2D,GACPC,QAAQD,MAAM,gCAAiCA,GAC/C4B,MAAM5F,EAAE,cAAe,mDACzB,CAAC,QACCiD,GAAW,EACb,GAoCgCqB,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEvE,EAAE,qBAAsB,0BAG3BwE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EvE,EAAE,cAAe,gBAAgB,SAEpCqE,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACLzB,MAAO3D,EAASG,YAChBkF,SAAWpB,GAAMR,EAAkB,cAAeQ,EAAEqB,OAAO3B,OAC3DE,UAAS,8FAAAb,OACPP,EAAOtC,YAAc,iBAAmB,wCAE1CoF,UAAQ,IAET9C,EAAOtC,cACNyD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAErB,EAAOtC,kBAIrD4D,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EvE,EAAE,iBAAkB,mBAAmB,SAE1CqE,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACLzB,MAAO3D,EAASI,eAChBiF,SAAWpB,GAAMR,EAAkB,iBAAkBQ,EAAEqB,OAAO3B,OAC9DE,UAAU,kIACV0B,UAAQ,QAIZxB,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/EvE,EAAE,eAAgB,iBAAiB,SAEtCqE,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACLzB,MAAO3D,EAASQ,aAChB6E,SAAWpB,GAAMR,EAAkB,eAAgBQ,EAAEqB,OAAO3B,OAC5DE,UAAS,8FAAAb,OACPP,EAAOjC,aAAe,iBAAmB,wCAE3C+E,UAAQ,IAET9C,EAAOjC,eACNoD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4BAA2BC,SAAErB,EAAOjC,yBAOzDuD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEvE,EAAE,gBAAiB,kCAGtBqE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClE,CACC,CAAEJ,MAAO,mBAAoB8B,MAAOjG,EAAE,cAAe,iBACrD,CAAEmE,MAAO,mBAAoB8B,MAAOjG,EAAE,cAAe,yBACrD,CAAEmE,MAAO,iBAAkB8B,MAAOjG,EAAE,YAAa,uBACjD,CAAEmE,MAAO,gBAAiB8B,MAAOjG,EAAE,WAAY,uBAC/CkG,IAAKC,IACL3B,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E4B,EAAKF,SAER5B,EAAAA,EAAAA,KAAA,SACEwB,KAAK,QACLO,IAAI,IACJC,IAAI,KACJjC,MAAO3D,EAAS0F,EAAKhC,OACrB2B,SAAWpB,GAAMR,EAAkBiC,EAAKhC,MAAOmC,SAAS5B,EAAEqB,OAAO3B,QACjEE,UAAU,uFAEZE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,OACNF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iCAAgCC,SAAE9D,EAAS0F,EAAKhC,UAChEE,EAAAA,EAAAA,KAAA,QAAAE,SAAM,YAfA4B,EAAKhC,cAuBrBK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEvE,EAAE,eAAgB,+BAGrBqE,EAAAA,EAAAA,KAACkC,EAAAA,EAAe,CACdC,SAAU/F,EAASmC,YACnB6D,iBA5OmB7D,IAC3BlC,EAAYmD,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACPjB,YAAaA,MA0OP8D,UAAU,QAKdlC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,UACEwB,KAAK,SACLc,QAASA,IAAMpG,GAAU,GACzB+D,UAAU,4IAA2IC,SAEpJvE,EAAE,SAAU,aAGfwE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEqB,KAAK,SACLc,QA1OQrD,UAClB,IACEL,GAAW,GAEX,MAAM2D,GAAO9C,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRrD,GAAQ,IACXoG,aAAa,IAAI/F,MAAOC,cACxB+F,YAAa3G,EAAKe,MAAQf,EAAK4G,MAC/B1G,UAAWA,IAGPkD,QAAiBC,MAAM,+BAAgC,CAC3D8B,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAAD9B,OAAYuD,aAAaC,QAAQ,WAElDzB,KAAMC,KAAKC,UAAUkB,KAGvB,IAAIrD,EAASG,GAaX,MAAM,IAAIiC,MAAM,uBAADlC,OAAwBF,EAAS2D,SAbjC,CACf,MAAMC,QAAa5D,EAAS4D,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,mBAAAnE,OAAsBhD,EAASG,YAAYiH,QAAQ,OAAQ,KAAI,KAAApE,OAAIhD,EAASI,eAAc,QACpG4G,SAASjC,KAAKsC,YAAYN,GAC1BA,EAAEO,QACFV,OAAOC,IAAIU,gBAAgBZ,GAC3BK,SAASjC,KAAKyC,YAAYT,GAE1B5B,MAAM5F,EAAE,eAAgB,+BAC1B,CAGF,CAAE,MAAOgE,GACPC,QAAQD,MAAM,wBAAyBA,GACvC4B,MAAM5F,EAAE,qBAAsB,2CAChC,CAAC,QACCiD,GAAW,EACb,GAmMUiF,SAAUlF,EACVsB,UAAU,gIAA+HC,SAAA,EAEzIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZtB,EAAUhD,EAAE,aAAc,iBAAmBA,EAAE,cAAe,oBAGjEqE,EAAAA,EAAAA,KAAA,UACEwB,KAAK,SACLqC,SAAUlF,EACVsB,UAAU,gHAA+GC,SAExHvB,EAAUhD,EAAE,SAAU,aAAeA,EAAE,qBAAsB,sC", "sources": ["pages/Forms/PainAssessmentForm.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport ClinicalBodyMap from '../../components/BodyMap/ClinicalBodyMap';\n\nconst PainAssessmentForm = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n\n  const [formData, setFormData] = useState({\n    // Patient Information\n    patientName: '',\n    patientId: patientId || '',\n    assessmentDate: new Date().toISOString().split('T')[0],\n    assessorName: user?.name || '',\n    \n    // Pain Assessment\n    currentPainLevel: 0,\n    averagePainLevel: 0,\n    worstPainLevel: 0,\n    bestPainLevel: 0,\n    painLocations: [],\n    \n    // Pain Characteristics\n    painQuality: [],\n    painPattern: '',\n    painOnset: '',\n    painDuration: '',\n    painFrequency: '',\n    \n    // Aggravating Factors\n    aggravatingFactors: [],\n    aggravatingOther: '',\n    \n    // Relieving Factors\n    relievingFactors: [],\n    relievingOther: '',\n    \n    // Functional Impact\n    sleepImpact: 0,\n    workImpact: 0,\n    dailyActivitiesImpact: 0,\n    moodImpact: 0,\n    socialImpact: 0,\n    \n    // Pain Management\n    currentMedications: '',\n    previousTreatments: '',\n    treatmentEffectiveness: '',\n    \n    // Goals and Expectations\n    painGoals: '',\n    functionalGoals: '',\n    treatmentExpectations: '',\n    \n    // Body Map Data\n    bodyMapData: {},\n    \n    // Additional Notes\n    additionalNotes: '',\n    assessorSignature: '',\n    assessmentComplete: false\n  });\n\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n\n  const painQualityOptions = [\n    'Sharp', 'Dull', 'Aching', 'Burning', 'Stabbing', 'Throbbing',\n    'Cramping', 'Shooting', 'Tingling', 'Numbness', 'Stiffness'\n  ];\n\n  const aggravatingFactorOptions = [\n    'Movement', 'Standing', 'Sitting', 'Walking', 'Lifting', 'Bending',\n    'Coughing', 'Sneezing', 'Weather Changes', 'Stress', 'Activity', 'Rest'\n  ];\n\n  const relievingFactorOptions = [\n    'Rest', 'Movement', 'Heat', 'Cold', 'Medication', 'Massage',\n    'Stretching', 'Exercise', 'Position Change', 'Sleep'\n  ];\n\n  useEffect(() => {\n    if (patientId) {\n      loadPatientData();\n    }\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch(`/api/patients/${patientId}`);\n      if (response.ok) {\n        const patientData = await response.json();\n        setFormData(prev => ({\n          ...prev,\n          patientName: patientData.name || '',\n          patientId: patientData._id || patientId\n        }));\n      }\n    } catch (error) {\n      console.error('Error loading patient data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  const handleCheckboxChange = (field, option, checked) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: checked \n        ? [...prev[field], option]\n        : prev[field].filter(item => item !== option)\n    }));\n  };\n\n  const handleBodyMapChange = (bodyMapData) => {\n    setFormData(prev => ({\n      ...prev,\n      bodyMapData: bodyMapData\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.patientName.trim()) newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    if (!formData.assessorName.trim()) newErrors.assessorName = t('assessorNameRequired', 'Assessor name is required');\n    if (formData.currentPainLevel < 0 || formData.currentPainLevel > 10) {\n      newErrors.currentPainLevel = t('painLevelRange', 'Pain level must be between 0-10');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const generatePDF = async () => {\n    try {\n      setLoading(true);\n\n      const pdfData = {\n        ...formData,\n        generatedAt: new Date().toISOString(),\n        generatedBy: user.name || user.email,\n        patientId: patientId\n      };\n\n      const response = await fetch('/api/v1/pain-assessments/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(pdfData)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `pain-assessment-${formData.patientName.replace(/\\s+/g, '-')}-${formData.assessmentDate}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n\n        alert(t('pdfGenerated', 'PDF generated successfully!'));\n      } else {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert(t('errorGeneratingPDF', 'Error generating PDF. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      setLoading(true);\n\n      const submissionData = {\n        ...formData,\n        submittedBy: user.id,\n        submittedAt: new Date().toISOString()\n      };\n\n      const response = await fetch('/api/v1/pain-assessments/public', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(submissionData)\n      });\n\n      if (response.ok) {\n        alert(t('painAssessmentSaved', 'Pain assessment saved successfully!'));\n        navigate(patientId ? `/patients/${patientId}` : '/patients');\n      } else {\n        throw new Error('Failed to save pain assessment');\n      }\n    } catch (error) {\n      console.error('Error saving pain assessment:', error);\n      alert(t('errorSaving', 'Error saving pain assessment. Please try again.'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('painAssessmentScale', 'Pain Assessment Scale')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                {t('painAssessmentDescription', 'Comprehensive pain evaluation and tracking')}\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 text-sm rounded-full\">\n                <i className=\"fas fa-exclamation-triangle mr-1\"></i>\n                {t('painAssessment', 'Pain Assessment')}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Form Content */}\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Patient Information */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {t('patientInformation', 'Patient Information')}\n          </h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('patientName', 'Patient Name')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.patientName}\n                onChange={(e) => handleInputChange('patientName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.patientName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.patientName && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n              )}\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('assessmentDate', 'Assessment Date')} *\n              </label>\n              <input\n                type=\"date\"\n                value={formData.assessmentDate}\n                onChange={(e) => handleInputChange('assessmentDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                required\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('assessorName', 'Assessor Name')} *\n              </label>\n              <input\n                type=\"text\"\n                value={formData.assessorName}\n                onChange={(e) => handleInputChange('assessorName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${\n                  errors.assessorName ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'\n                }`}\n                required\n              />\n              {errors.assessorName && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.assessorName}</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Pain Intensity */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {t('painIntensity', 'Pain Intensity (0-10 Scale)')}\n          </h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {[\n              { field: 'currentPainLevel', label: t('currentPain', 'Current Pain') },\n              { field: 'averagePainLevel', label: t('averagePain', 'Average Pain (24hrs)') },\n              { field: 'worstPainLevel', label: t('worstPain', 'Worst Pain (24hrs)') },\n              { field: 'bestPainLevel', label: t('bestPain', 'Best Pain (24hrs)') }\n            ].map((item) => (\n              <div key={item.field}>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {item.label}\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"0\"\n                  max=\"10\"\n                  value={formData[item.field]}\n                  onChange={(e) => handleInputChange(item.field, parseInt(e.target.value))}\n                  className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\n                />\n                <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                  <span>0</span>\n                  <span className=\"font-bold text-lg text-red-600\">{formData[item.field]}</span>\n                  <span>10</span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Body Map */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n            {t('painLocation', 'Pain Location & Body Map')}\n          </h2>\n          \n          <ClinicalBodyMap\n            painData={formData.bodyMapData}\n            onPainDataChange={handleBodyMapChange}\n            readonly={false}\n          />\n        </div>\n\n        {/* Navigation Buttons */}\n        <div className=\"flex justify-between\">\n          <button\n            type=\"button\"\n            onClick={() => navigate(-1)}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n\n          <div className=\"flex space-x-3\">\n            <button\n              type=\"button\"\n              onClick={generatePDF}\n              disabled={loading}\n              className=\"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\"\n            >\n              <i className=\"fas fa-file-pdf mr-2\"></i>\n              {loading ? t('generating', 'Generating...') : t('generatePDF', 'Generate PDF')}\n            </button>\n\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {loading ? t('saving', 'Saving...') : t('savePainAssessment', 'Save Pain Assessment')}\n            </button>\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default PainAssessmentForm;\n"], "names": ["PainAssessmentForm", "t", "isRTL", "useLanguage", "user", "useAuth", "patientId", "useParams", "navigate", "useNavigate", "formData", "setFormData", "useState", "patientName", "assessmentDate", "Date", "toISOString", "split", "assessor<PERSON>ame", "name", "currentPainLevel", "averagePainLevel", "worstPainLevel", "bestPainLevel", "painLocations", "painQuality", "painPattern", "painOnset", "painDuration", "painFrequency", "aggravatingFactors", "aggravatingOther", "relievingFactors", "relieving<PERSON><PERSON>", "sleepImpact", "workImpact", "dailyActivitiesImpact", "moodImpact", "socialImpact", "currentMedications", "previousTreatments", "treatmentEffectiveness", "painGoals", "functionalGoals", "treatmentExpectations", "bodyMapData", "additionalNotes", "assessorSignature", "assessmentComplete", "loading", "setLoading", "errors", "setErrors", "useEffect", "loadPatientData", "async", "response", "fetch", "concat", "ok", "patientData", "json", "prev", "_objectSpread", "_id", "error", "console", "handleInputChange", "field", "value", "_jsx", "className", "children", "_jsxs", "onSubmit", "e", "preventDefault", "validateForm", "newErrors", "trim", "Object", "keys", "length", "submissionData", "submittedBy", "id", "submittedAt", "method", "headers", "body", "JSON", "stringify", "Error", "alert", "type", "onChange", "target", "required", "label", "map", "item", "min", "max", "parseInt", "ClinicalBodyMap", "painData", "onPainDataChange", "readonly", "onClick", "pdfData", "generatedAt", "generatedBy", "email", "localStorage", "getItem", "status", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "replace", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "disabled"], "sourceRoot": ""}