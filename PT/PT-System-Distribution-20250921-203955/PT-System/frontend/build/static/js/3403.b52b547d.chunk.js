"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3403],{2816:(e,t,a)=>{a.d(t,{rR:()=>i});const i={FULL_COMPLIANCE:{score:100,label:"Full Compliance",color:"green",description:"Meets all CARF standards"},SUBSTANTIAL_COMPLIANCE:{score:85,label:"Substantial Compliance",color:"yellow",description:"Minor areas for improvement"},PARTIAL_COMPLIANCE:{score:70,label:"Partial Compliance",color:"orange",description:"Significant improvements needed"},NON_COMPLIANCE:{score:0,label:"Non-Compliance",color:"red",description:"Major deficiencies identified"}}},3403:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var i=a(5043),s=a(7921),r=(a(2816),a(579));const n=()=>{var e,t,a,n,l,o,c,d,m,u,x,g,v,h,f,p,b,y,j,N,k,w,C,I,M,A,L;const{t:T,isRTL:S}=(0,s.o)(),[P,R]=(0,i.useState)("current-quarter"),[O,E]=(0,i.useState)({}),[F,Q]=(0,i.useState)(!0);(0,i.useEffect)(()=>{const e={satisfaction:{overallSatisfaction:88,serviceQuality:4.2,recommendationLikelihood:85,communicationEffectiveness:90,culturalCompetency:87,trend:"+3%",target:85,responses:156,responseRate:78},outcomes:{goalAchievement:82,functionalImprovement:75,dischargeToCommunity:88,readmissionRate:8,averageLengthOfStay:45,trend:"+5%",totalPatients:234},efficiency:{assessmentCompletion:96,treatmentPlanTimeliness:94,serviceInitiation:98,dischargePlanningTimeliness:92,documentationCompletion:97,trend:"+2%"},functionalOutcomes:{fim:{admissionAverage:78,dischargeAverage:105,improvement:27,efficiencyScore:.6},qualityOfLife:{physicalHealth:7.2,psychologicalWellbeing:6.8,socialRelationships:7,environmentalFactors:7.5,overallScore:7.1},communityParticipation:{employment:65,socialActivities:78,independentLiving:72,recreation:85,overallParticipation:75}},riskManagement:{fallIncidents:2,medicationErrors:1,equipmentFailures:0,behavioralIncidents:3,totalIncidents:6,incidentRate:2.6,trend:"-15%"}};setTimeout(()=>{E(e),Q(!1)},1e3)},[P]);const q=e=>{let{title:t,value:a,target:i,unit:s,trend:n,status:l,icon:o,color:c,description:d}=e;return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-3 bg-".concat(c,"-100 dark:bg-").concat(c,"-900/30 rounded-lg"),children:(0,r.jsx)("i",{className:"".concat(o," text-").concat(c,"-600 dark:text-").concat(c,"-400 text-xl")})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t}),(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:d})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("span",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[a,s]}),n&&(0,r.jsx)("span",{className:"text-sm font-medium ".concat(n.startsWith("+")?"text-green-600":"text-red-600"),children:n})]}),i&&(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[T("target","Target"),": ",i,s]})]})]}),i&&(0,r.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-".concat(c,"-600 h-2 rounded-full transition-all duration-300"),style:{width:"".concat(Math.min(a/i*100,100),"%")}})}),(0,r.jsx)("div",{className:"mt-2 text-xs font-medium ".concat("above-target"===l?"text-green-600":"at-target"===l?"text-blue-600":"below-target"===l?"text-yellow-600":"text-red-600"),children:"above-target"===l?T("aboveTarget","Above Target"):"at-target"===l?T("atTarget","At Target"):"below-target"===l?T("belowTarget","Below Target"):T("needsImprovement","Needs Improvement")})]})},D=e=>{let{title:t,data:a,type:i="bar"}=e;return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:t}),(0,r.jsx)("div",{className:"space-y-4",children:Object.entries(a).map(e=>{let[t,a]=e;return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400 capitalize",children:T(t,t.replace(/([A-Z])/g," $1").trim())}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(Math.min(a,100),"%")}})}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white w-12 text-right",children:"number"===typeof a?a.toFixed(1):a})]})]},t)})})]})};return F?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,r.jsxs)("div",{className:"space-y-6 ".concat(S?"font-arabic":"font-english"),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:T("carfQualityMeasures","CARF Quality Measures")}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:T("qualityMeasuresDescription","Monitor quality indicators and outcome measures")})]}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsxs)("select",{value:P,onChange:e=>R(e.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,r.jsx)("option",{value:"current-quarter",children:T("currentQuarter","Current Quarter")}),(0,r.jsx)("option",{value:"last-quarter",children:T("lastQuarter","Last Quarter")}),(0,r.jsx)("option",{value:"current-year",children:T("currentYear","Current Year")}),(0,r.jsx)("option",{value:"last-year",children:T("lastYear","Last Year")})]}),(0,r.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)("i",{className:"fas fa-download mr-2"}),T("exportReport","Export Report")]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:T("personServedSatisfaction","Person-Served Satisfaction")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(q,{title:T("overallSatisfaction","Overall Satisfaction"),value:null===(e=O.satisfaction)||void 0===e?void 0:e.overallSatisfaction,target:85,unit:"%",trend:null===(t=O.satisfaction)||void 0===t?void 0:t.trend,status:"above-target",icon:"fas fa-smile",color:"green",description:"".concat(null===(a=O.satisfaction)||void 0===a?void 0:a.responses," ").concat(T("responses","responses"))}),(0,r.jsx)(q,{title:T("serviceQuality","Service Quality"),value:null===(n=O.satisfaction)||void 0===n?void 0:n.serviceQuality,target:4,unit:"/5",trend:"+0.2",status:"above-target",icon:"fas fa-star",color:"blue",description:T("averageRating","Average Rating")}),(0,r.jsx)(q,{title:T("recommendationLikelihood","Recommendation Likelihood"),value:null===(l=O.satisfaction)||void 0===l?void 0:l.recommendationLikelihood,target:80,unit:"%",trend:"+2%",status:"above-target",icon:"fas fa-thumbs-up",color:"purple",description:T("wouldRecommend","Would Recommend")})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:T("treatmentOutcomes","Treatment Outcomes")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(q,{title:T("goalAchievement","Goal Achievement"),value:null===(o=O.outcomes)||void 0===o?void 0:o.goalAchievement,target:80,unit:"%",trend:null===(c=O.outcomes)||void 0===c?void 0:c.trend,status:"above-target",icon:"fas fa-bullseye",color:"green",description:T("goalsAchieved","Goals Achieved")}),(0,r.jsx)(q,{title:T("functionalImprovement","Functional Improvement"),value:null===(d=O.outcomes)||void 0===d?void 0:d.functionalImprovement,target:70,unit:"%",trend:"+3%",status:"above-target",icon:"fas fa-chart-line",color:"blue",description:T("patientsImproved","Patients Improved")}),(0,r.jsx)(q,{title:T("dischargeToCommunity","Discharge to Community"),value:null===(m=O.outcomes)||void 0===m?void 0:m.dischargeToCommunity,target:85,unit:"%",trend:"+1%",status:"above-target",icon:"fas fa-home",color:"purple",description:T("successfulDischarges","Successful Discharges")})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:T("serviceEfficiency","Service Efficiency")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(q,{title:T("assessmentCompletion","Assessment Completion"),value:null===(u=O.efficiency)||void 0===u?void 0:u.assessmentCompletion,target:95,unit:"%",trend:"+1%",status:"above-target",icon:"fas fa-clipboard-check",color:"green",description:T("within72Hours","Within 72 Hours")}),(0,r.jsx)(q,{title:T("treatmentPlanTimeliness","Treatment Plan Timeliness"),value:null===(x=O.efficiency)||void 0===x?void 0:x.treatmentPlanTimeliness,target:95,unit:"%",trend:"-1%",status:"below-target",icon:"fas fa-calendar-check",color:"yellow",description:T("within30Days","Within 30 Days")}),(0,r.jsx)(q,{title:T("documentationCompletion","Documentation Completion"),value:null===(g=O.efficiency)||void 0===g?void 0:g.documentationCompletion,target:95,unit:"%",trend:"+2%",status:"above-target",icon:"fas fa-file-alt",color:"blue",description:T("completeDocumentation","Complete Documentation")})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(D,{title:T("functionalIndependenceMeasure","Functional Independence Measure (FIM)"),data:{admission:null===(v=O.functionalOutcomes)||void 0===v||null===(h=v.fim)||void 0===h?void 0:h.admissionAverage,discharge:null===(f=O.functionalOutcomes)||void 0===f||null===(p=f.fim)||void 0===p?void 0:p.dischargeAverage,improvement:null===(b=O.functionalOutcomes)||void 0===b||null===(y=b.fim)||void 0===y?void 0:y.improvement}}),(0,r.jsx)(D,{title:T("qualityOfLife","Quality of Life Measures"),data:null===(j=O.functionalOutcomes)||void 0===j?void 0:j.qualityOfLife}),(0,r.jsx)(D,{title:T("communityParticipation","Community Participation"),data:null===(N=O.functionalOutcomes)||void 0===N?void 0:N.communityParticipation})]}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:T("riskManagement","Risk Management Indicators")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600 dark:text-red-400",children:null===(k=O.riskManagement)||void 0===k?void 0:k.fallIncidents}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:T("fallIncidents","Fall Incidents")})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:null===(w=O.riskManagement)||void 0===w?void 0:w.medicationErrors}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:T("medicationErrors","Medication Errors")})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:null===(C=O.riskManagement)||void 0===C?void 0:C.behavioralIncidents}),(0,r.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:T("behavioralIncidents","Behavioral Incidents")})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:null===(I=O.riskManagement)||void 0===I?void 0:I.incidentRate}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[T("incidentRate","Incident Rate")," (per 100 patients)"]})]})]}),(0,r.jsx)("div",{className:"mt-4 flex items-center justify-center",children:(0,r.jsxs)("span",{className:"text-sm font-medium ".concat(null!==(M=O.riskManagement)&&void 0!==M&&null!==(A=M.trend)&&void 0!==A&&A.startsWith("-")?"text-green-600":"text-red-600"),children:[null===(L=O.riskManagement)||void 0===L?void 0:L.trend," ",T("fromLastPeriod","from last period")]})})]})]})},l=()=>(0,r.jsx)(n,{})}}]);
//# sourceMappingURL=3403.b52b547d.chunk.js.map