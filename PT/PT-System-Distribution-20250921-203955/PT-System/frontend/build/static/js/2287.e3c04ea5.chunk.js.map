{"version": 3, "file": "static/js/2287.e3c04ea5.chunk.js", "mappings": "0MAGA,MA6yBA,EA7yB+BA,IAAsD,IAArD,SAAEC,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EAC7E,MAAM,EAAEK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KAEfC,EAAoBA,CAACC,EAAOC,KAChC,MAAMC,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAASY,aAG9B,GAAIJ,EAAMK,SAAS,KAAM,CACvB,MAAOC,EAAQC,GAASP,EAAMQ,MAAM,KACpCN,EAAQI,IAAOH,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQD,EAAQI,IAAO,IAAE,CAACC,GAAQN,GACnD,MACEC,EAAQF,GAASC,EAGnBR,EAAe,cAAeS,GAG1BR,EAAOM,IACTL,EAAUc,IAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUM,GAAI,IAAE,CAACT,GAAQ,SAIrCU,EAAoBA,CAACV,EAAOC,EAAOU,KACvC,MAAMC,EAAepB,EAASY,YAAYJ,IAAU,GACpD,IAAIa,EAGFA,EADEF,EACS,IAAIC,EAAcX,GAElBW,EAAaE,OAAOC,GAAQA,IAASd,GAGlDF,EAAkBC,EAAOa,IA8B3B,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,qBAAsB,0BAE3BuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,sBAAuB,gDAK9BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,cAAe,gBAAgB,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAYiB,KAC5BC,SAAWC,GAAMxB,EAAkB,OAAQwB,EAAEC,OAAOvB,OACpDgB,UAAS,mJAAAQ,OACP/B,EAAO2B,KAAO,iBAAmB,mBAEnCK,YAAa9B,EAAE,mBAAoB,wBAEpCF,EAAO2B,OAAQF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAExB,EAAO2B,WAGnEL,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,MAAO,OAAO,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEnDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,MACLpB,MAAM,IACNU,QAAsC,MAA7BnB,EAASY,YAAYuB,IAC9BL,SAAWC,GAAMxB,EAAkB,MAAOwB,EAAEC,OAAOvB,OACnDgB,UAAU,SAEXrB,EAAE,OAAQ,YAEboB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,MACLpB,MAAM,IACNU,QAAsC,MAA7BnB,EAASY,YAAYuB,IAC9BL,SAAWC,GAAMxB,EAAkB,MAAOwB,EAAEC,OAAOvB,OACnDgB,UAAU,SAEXrB,EAAE,SAAU,gBAGhBF,EAAOiC,MAAOR,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAExB,EAAOiC,UAGlEX,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,qBAEtBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,gBACLpB,MAAM,SACNU,QAAgD,WAAvCnB,EAASY,YAAYwB,cAC9BN,SAAWC,GAAMxB,EAAkB,gBAAiBwB,EAAEC,OAAOvB,OAC7DgB,UAAU,SAEXrB,EAAE,SAAU,cAEfoB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,gBACLpB,MAAM,UACNU,QAAgD,YAAvCnB,EAASY,YAAYwB,cAC9BN,SAAWC,GAAMxB,EAAkB,gBAAiBwB,EAAEC,OAAOvB,OAC7DgB,UAAU,SAEXrB,EAAE,UAAW,qBAKpBoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,YAAa,kBAAkB,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAYyB,UAC5BP,SAAWC,GAAMxB,EAAkB,YAAawB,EAAEC,OAAOvB,OACzDgB,UAAS,mJAAAQ,OACP/B,EAAOmC,UAAY,iBAAmB,mBAExCH,YAAa9B,EAAE,iBAAkB,sBAElCF,EAAOmC,YAAaV,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAExB,EAAOmC,gBAGxEb,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,cAAe,eAAe,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEnEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAY0B,YAC5BR,SAAWC,GAAMxB,EAAkB,cAAewB,EAAEC,OAAOvB,OAC3DgB,UAAS,mJAAAQ,OACP/B,EAAOoC,YAAc,iBAAmB,mBAE1CJ,YAAa9B,EAAE,mBAAoB,uBAEpCF,EAAOoC,cAAeX,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAExB,EAAOoC,kBAG1Ed,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,MAAO,OAAO,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEnDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLW,IAAI,KACJC,IAAI,MACJ/B,MAAOT,EAASY,YAAY6B,IAC5BX,SAAWC,GAAMxB,EAAkB,MAAOwB,EAAEC,OAAOvB,OACnDgB,UAAS,mJAAAQ,OACP/B,EAAOuC,IAAM,iBAAmB,mBAElCP,YAAa9B,EAAE,WAAY,eAE5BF,EAAOuC,MAAOd,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAExB,EAAOuC,UAGlEjB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,aAAc,kBAEnBuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAY8B,WAC5BZ,SAAWC,GAAMxB,EAAkB,aAAcwB,EAAEC,OAAOvB,OAC1DgB,UAAU,kKACVS,YAAa9B,EAAE,kBAAmB,2BAItCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,mBAAoB,sBAAsB,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/EC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAY+B,WAC5Bb,SAAWC,GAAMxB,EAAkB,aAAcwB,EAAEC,OAAOvB,OAC1DgB,UAAU,wKAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,OAAQ,WAEbuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAYgC,KAC5Bd,SAAWC,GAAMxB,EAAkB,OAAQwB,EAAEC,OAAOvB,OACpDgB,UAAU,wKAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,qBAAsB,2BAE3BuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAYiC,mBAC5Bf,SAAWC,GAAMxB,EAAkB,qBAAsBwB,EAAEC,OAAOvB,OAClEgB,UAAU,kKACVS,YAAa9B,EAAE,yBAA0B,qCAM/CoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,uBAEtBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,CA/N9C,CAC3B,WAAY,UAAW,YAAa,sBAAuB,aAC3D,oBAAqB,iBAAkB,UAAW,0BAA2B,WA8NjDoB,IAAKC,IACzBvB,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,QAASnB,EAASY,YAAYoC,cAAcnC,SAASkC,GACrDjB,SAAWC,GAAMb,EAAkB,gBAAiB6B,EAAQhB,EAAEC,OAAOb,SACrEM,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OAPjGA,KAUdpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,QAASnB,EAASY,YAAYoC,cAAcnC,SAAS,SACrDiB,SAAWC,GAAMb,EAAkB,gBAAiB,QAASa,EAAEC,OAAOb,SACtEM,UAAU,UAEZD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gDAA+CC,SAAA,CAAEtB,EAAE,QAAS,SAAS,QACrFuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLM,YAAa9B,EAAE,UAAW,WAC1BqB,UAAU,yGACV0B,UAAWnD,EAASY,YAAYoC,cAAcnC,SAAS,uBAQjEW,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,iBAAkB,sBAEvBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,CA/P9B,CAC5B,aAAc,WAAY,oCAC1B,qBAAsB,wBA8POoB,IAAKC,IAC1BvB,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,iBACLpB,MAAOsC,EACP5B,QAASnB,EAASY,YAAYwC,iBAAmBL,EACjDjB,SAAWC,GAAMxB,EAAkB,iBAAkBwB,EAAEC,OAAOvB,OAC9DgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OATjGA,KAYdpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,iBACLpB,MAAM,QACNU,QAAiD,UAAxCnB,EAASY,YAAYwC,eAC9BtB,SAAWC,GAAMxB,EAAkB,iBAAkBwB,EAAEC,OAAOvB,OAC9DgB,UAAU,UAEZD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gDAA+CC,SAAA,CAAEtB,EAAE,QAAS,SAAS,QACrFuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLM,YAAa9B,EAAE,UAAW,WAC1BqB,UAAU,yGACV0B,SAAkD,UAAxCnD,EAASY,YAAYwC,6BAQzC5B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,kBAAmB,wBAExBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SApSL,CAC3B,YAAa,iBAAkB,gBAoSDoB,IAAKC,IACzBvB,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,kBACLpB,MAAOsC,EACP5B,QAASnB,EAASY,YAAYyC,gBAAgBC,kBAAoBP,EAClEjB,SAAWC,GAAMxB,EAAkB,kCAAmCwB,EAAEC,OAAOvB,OAC/EgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OATjGA,UAelBvB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,qBAEtBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SArTL,CAC3B,oBAAqB,eAAgB,mBAqTPoB,IAAKC,IACzBvB,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,QAASnB,EAASY,YAAYyC,gBAAgBE,cAAc1C,SAASkC,GACrEjB,SAAWC,IACT,MAAMX,EAAepB,EAASY,YAAYyC,gBAAgBE,eAAiB,GAC3E,IAAIlC,EAEFA,EADEU,EAAEC,OAAOb,QACA,IAAIC,EAAc2B,GAElB3B,EAAaE,OAAOC,GAAQA,IAASwB,GAElDxC,EAAkB,gCAAiCc,IAErDI,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OAhBjGA,aAwBpBvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,YAAa,aAAa,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DC,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAY4C,UAC5B1B,SAAWC,GAAMxB,EAAkB,YAAawB,EAAEC,OAAOvB,OACzDgD,KAAM,EACNhC,UAAS,mJAAAQ,OACP/B,EAAOsD,UAAY,iBAAmB,mBAExCtB,YAAa9B,EAAE,iBAAkB,qBAElCF,EAAOsD,YAAa7B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAExB,EAAOsD,gBAGxEhC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,YAAa,kBAElBuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAY8C,UAC5B5B,SAAWC,GAAMxB,EAAkB,YAAawB,EAAEC,OAAOvB,OACzDgB,UAAU,kKACVS,YAAa9B,EAAE,iBAAkB,2BAIrCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EtB,EAAE,iBAAkB,mBAAmB,KAACuB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE1EC,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAY+C,eAC5B7B,SAAWC,GAAMxB,EAAkB,iBAAkBwB,EAAEC,OAAOvB,OAC9DgD,KAAM,EACNhC,UAAS,mJAAAQ,OACP/B,EAAOyD,eAAiB,iBAAmB,mBAE7CzB,YAAa9B,EAAE,sBAAuB,2BAEvCF,EAAOyD,iBAAkBhC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAExB,EAAOyD,qBAG7EnC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,0BAA2B,gCAEhCuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYgD,wBAC5B9B,SAAWC,GAAMxB,EAAkB,0BAA2BwB,EAAEC,OAAOvB,OACvEgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,+BAAgC,4CAMrDoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,yBAA0B,gCAE/BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,OAAQ,WAEbuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAYiD,gBAAgBC,KAC5ChC,SAAWC,GAAMxB,EAAkB,uBAAwBwB,EAAEC,OAAOvB,OACpEgB,UAAU,wKAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,wBAAyB,6BAE9BuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYiD,gBAAgBE,cAC5CjC,SAAWC,GAAMxB,EAAkB,gCAAiCwB,EAAEC,OAAOvB,OAC7EgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,qBAAsB,mCAO7CoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,kBAAmB,uBAExBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,kBACLpB,MAAM,MACNU,QAASnB,EAASY,YAAYoD,gBAAgBC,WAC9CnC,SAAWC,GAAMxB,EAAkB,6BAAiD,QAAnBwB,EAAEC,OAAOvB,OAC1EgB,UAAU,SAEXrB,EAAE,MAAO,WAEZoB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,kBACLpB,MAAM,KACNU,SAAUnB,EAASY,YAAYoD,gBAAgBC,WAC/CnC,SAAWC,GAAMxB,EAAkB,6BAAiD,QAAnBwB,EAAEC,OAAOvB,OAC1EgB,UAAU,SAEXrB,EAAE,KAAM,YAGZJ,EAASY,YAAYoD,gBAAgBC,aACpCzC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,gBAAiB,sBAEtBuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,MAAOT,EAASY,YAAYoD,gBAAgBpC,KAC5CE,SAAWC,GAAMxB,EAAkB,uBAAwBwB,EAAEC,OAAOvB,OACpEgB,UAAU,kKACVS,YAAa9B,EAAE,qBAAsB,qCAQ/CoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,oCAAqC,0CAE1CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,4BACLpB,MAAM,MACNU,QAASnB,EAASY,YAAYsD,0BAA0BC,iBACxDrC,SAAWC,GAAMxB,EAAkB,6CAAiE,QAAnBwB,EAAEC,OAAOvB,OAC1FgB,UAAU,SAEXrB,EAAE,MAAO,WAEZoB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,4BACLpB,MAAM,KACNU,SAAUnB,EAASY,YAAYsD,0BAA0BC,iBACzDrC,SAAWC,GAAMxB,EAAkB,6CAAiE,QAAnBwB,EAAEC,OAAOvB,OAC1FgB,UAAU,SAEXrB,EAAE,KAAM,YAGZJ,EAASY,YAAYsD,0BAA0BC,mBAC9C3C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYsD,0BAA0BE,SACtDtC,SAAWC,GAAMxB,EAAkB,qCAAsCwB,EAAEC,OAAOvB,OAClFgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,gBAAiB,8BAQ1CoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,4BAA6B,kCAElCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,4BACLpB,MAAM,MACNU,QAASnB,EAASY,YAAYyD,0BAA0BC,gBACxDxC,SAAWC,GAAMxB,EAAkB,4CAAgE,QAAnBwB,EAAEC,OAAOvB,OACzFgB,UAAU,SAEXrB,EAAE,MAAO,WAEZoB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,4BACLpB,MAAM,KACNU,SAAUnB,EAASY,YAAYyD,0BAA0BC,gBACzDxC,SAAWC,GAAMxB,EAAkB,4CAAgE,QAAnBwB,EAAEC,OAAOvB,OACzFgB,UAAU,SAEXrB,EAAE,KAAM,YAGZJ,EAASY,YAAYyD,0BAA0BC,kBAC9C9C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,UAAW,cAEhBuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYyD,0BAA0BE,QACtDzC,SAAWC,GAAMxB,EAAkB,oCAAqCwB,EAAEC,OAAOvB,OACjFgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,eAAgB,6BAQzCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,sCAAuC,6CAE5CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,mBACLpB,MAAM,MACNU,QAASnB,EAASY,YAAY4D,iBAAiBC,aAC/C3C,SAAWC,GAAMxB,EAAkB,gCAAoD,QAAnBwB,EAAEC,OAAOvB,OAC7EgB,UAAU,SAEXrB,EAAE,MAAO,WAEZoB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,mBACLpB,MAAM,KACNU,SAAUnB,EAASY,YAAY4D,iBAAiBC,aAChD3C,SAAWC,GAAMxB,EAAkB,gCAAoD,QAAnBwB,EAAEC,OAAOvB,OAC7EgB,UAAU,SAEXrB,EAAE,KAAM,YAGZJ,EAASY,YAAY4D,iBAAiBC,eACrCjD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,UAAW,cAEhBuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAY4D,iBAAiBD,QAC7CzC,SAAWC,GAAMxB,EAAkB,2BAA4BwB,EAAEC,OAAOvB,OACxEgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,eAAgB,6BAQzCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,cAAe,kBAEpBuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAY8D,YAC5B5C,SAAWC,GAAMxB,EAAkB,cAAewB,EAAEC,OAAOvB,OAC3DgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,mBAAoB,2BAKvCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,qBAAsB,2BAE3BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,CAtnBzC,CAChC,WAAY,eAAgB,mBAAoB,SAChD,kBAAmB,SAAU,gCAAiC,QAqnB7BoB,IAAKC,IAC9BvB,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,QAASnB,EAASY,YAAY+D,mBAAmB9D,SAASkC,GAC1DjB,SAAWC,GAAMb,EAAkB,qBAAsB6B,EAAQhB,EAAEC,OAAOb,SAC1EM,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OAPjGA,KAUdpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,QAASnB,EAASY,YAAY+D,mBAAmB9D,SAAS,SAC1DiB,SAAWC,GAAMb,EAAkB,qBAAsB,QAASa,EAAEC,OAAOb,SAC3EM,UAAU,UAEZD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gDAA+CC,SAAA,CAAEtB,EAAE,QAAS,SAAS,QACrFuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLM,YAAa9B,EAAE,UAAW,WAC1BqB,UAAU,yGACV0B,UAAWnD,EAASY,YAAY+D,mBAAmB9D,SAAS,uBAQtEW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,cAAe,kBAEpBuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYgE,YAC5B9C,SAAWC,GAAMxB,EAAkB,cAAewB,EAAEC,OAAOvB,OAC3DgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,mBAAoB,2BAIvCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,qBAAsB,2BAE3BuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYiE,mBAC5B/C,SAAWC,GAAMxB,EAAkB,qBAAsBwB,EAAEC,OAAOvB,OAClEgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,0BAA2B,uCAMhDoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,qBAEtBuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYkE,cAC5BhD,SAAWC,GAAMxB,EAAkB,gBAAiBwB,EAAEC,OAAOvB,OAC7DgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,qBAAsB,8BAIzCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,YAAa,gBAElBuB,EAAAA,EAAAA,KAAA,YACElB,MAAOT,EAASY,YAAYmE,UAC5BjD,SAAWC,GAAMxB,EAAkB,YAAawB,EAAEC,OAAOvB,OACzDgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,iBAAkB,4BAMvCoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,sBAAuB,4BAE5BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,CAhtBzB,CACjC,UAAW,YAgtBuBoB,IAAKC,IAC/BvB,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,QAASnB,EAASY,YAAYoE,oBAAoBnE,SAASkC,GAC3DjB,SAAWC,GAAMb,EAAkB,sBAAuB6B,EAAQhB,EAAEC,OAAOb,SAC3EM,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OAPjGA,KAUdpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,QAASnB,EAASY,YAAYoE,oBAAoBnE,SAAS,UAC3DiB,SAAWC,GAAMb,EAAkB,sBAAuB,SAAUa,EAAEC,OAAOb,SAC7EM,UAAU,UAEZD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gDAA+CC,SAAA,CAAEtB,EAAE,SAAU,UAAU,QACvFuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLM,YAAa9B,EAAE,UAAW,WAC1BqB,UAAU,yGACV0B,UAAWnD,EAASY,YAAYoE,oBAAoBnE,SAAS,2BCzR7E,EA1gB6Bd,IAAsD,IAADkF,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAApD,SAAExF,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EAC3E,MAAM,EAAEK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KAEfC,EAAoBA,CAACC,EAAOC,KAChC,MAAMC,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAASyF,kBAG9B,GAAIjF,EAAMK,SAAS,KAAM,CACvB,MAAM6E,EAAQlF,EAAMQ,MAAM,KAC1B,IAAI2E,EAAUjF,EACd,IAAK,IAAIkF,EAAI,EAAGA,EAAIF,EAAMG,OAAS,EAAGD,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMG,OAAS,IAAMpF,CACrC,MACEC,EAAQF,GAASC,EAGnBR,EAAe,mBAAoBS,GAG/BR,EAAOM,IACTL,EAAUc,IAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUM,GAAI,IAAE,CAACT,GAAQ,SAiBrCsF,EAA2BA,CAACC,EAAQvF,EAAOC,KAC/C,MAAMC,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAASyF,kBAK9B,GAJK/E,EAAQsF,gBAAgBD,KAC3BrF,EAAQsF,gBAAgBD,GAAU,CAAEE,WAAY,GAAIC,QAAS,KAGjD,eAAV1F,EAAwB,CAC1B,MAAM2F,EAAoBzF,EAAQsF,gBAAgBD,GAAQE,YAAc,GACpEE,EAAkBtF,SAASJ,GAC7BC,EAAQsF,gBAAgBD,GAAQE,WAAaE,EAAkB7E,OAAOC,GAAQA,IAASd,GAEvFC,EAAQsF,gBAAgBD,GAAQE,WAAa,IAAIE,EAAmB1F,EAExE,MACEC,EAAQsF,gBAAgBD,GAAQvF,GAASC,EAG3CR,EAAe,mBAAoBS,IAI/B0F,EAAgB,CACpB,CAAE3F,MAAO,SAAU4F,MAAOjG,EAAE,SAAU,WACtC,CAAEK,MAAO,WAAY4F,MAAOjG,EAAE,WAAY,aAC1C,CAAEK,MAAO,iBAAkB4F,MAAOjG,EAAE,gBAAiB,oBAIjDkG,EAAmB,CACvB,CAAE7F,MAAO,MAAO4F,MAAOjG,EAAE,MAAO,QAChC,CAAEK,MAAO,OAAQ4F,MAAOjG,EAAE,OAAQ,SAClC,CAAEK,MAAO,WAAY4F,MAAOjG,EAAE,WAAY,aAC1C,CAAEK,MAAO,OAAQ4F,MAAOjG,EAAE,OAAQ,UAI9BmG,EAAuB,CAC3B,CAAE9F,MAAO,uBAAwB4F,MAAOjG,EAAE,sBAAuB,yBACjE,CAAEK,MAAO,kBAAmB4F,MAAOjG,EAAE,iBAAkB,oBACvD,CAAEK,MAAO,sBAAuB4F,MAAOjG,EAAE,qBAAsB,wBAC/D,CAAEK,MAAO,oBAAqB4F,MAAOjG,EAAE,mBAAoB,uBAIvDoG,EAAwB,CAC5B,CAAE/F,MAAO,YAAa4F,MAAOjG,EAAE,YAAa,cAC5C,CAAEK,MAAO,OAAQ4F,MAAOjG,EAAE,OAAQ,SAClC,CAAEK,MAAO,OAAQ4F,MAAOjG,EAAE,OAAQ,SAClC,CAAEK,MAAO,OAAQ4F,MAAOjG,EAAE,OAAQ,UAa9BqG,EAA0B,CAC9B,CAAEhG,MAAO,mBAAoB4F,MAAOjG,EAAE,iBAAkB,qBACxD,CAAEK,MAAO,2BAA4B4F,MAAOjG,EAAE,yBAA0B,6BACxE,CAAEK,MAAO,gBAAiB4F,MAAOjG,EAAE,eAAgB,kBACnD,CAAEK,MAAO,yBAA0B4F,MAAOjG,EAAE,uBAAwB,4BAIhEsG,EAA2B,CAC/B,CAAEjG,MAAO,kBAAmB4F,MAAOjG,EAAE,iBAAkB,oBACvD,CAAEK,MAAO,aAAc4F,MAAOjG,EAAE,YAAa,eAC7C,CAAEK,MAAO,iBAAkB4F,MAAOjG,EAAE,gBAAiB,mBACrD,CAAEK,MAAO,eAAgB4F,MAAOjG,EAAE,cAAe,kBAI7CuG,EAAyB,CAC7BC,YAAa,CACX,gBAAiB,OAAQ,SAAU,UAAW,SAAU,WAAY,SAEtEC,UAAW,CACT,gBAAiB,mBAAoB,WAAY,UAAW,YAAa,WAAY,YAEvFC,iBAAkB,CAChB,gBAAiB,SAAU,WAAY,eAAgB,SAAU,eAEnEC,cAAe,CACb,gBAAiB,eAAgB,YAAa,YAAa,YAAa,YAE1EC,gBAAiB,CACf,gBAAiB,YAAa,SAAU,YAAa,aAAc,uBAErEC,WAAY,CACV,gBAAiB,SAAU,WAAY,cAAe,0BAExDC,eAAgB,CACd,gBAAiB,YAAa,kBAAmB,qBAAsB,eAAgB,eAEzFC,cAAe,CACb,gBAAiB,aAAc,aAAc,eAAgB,6BAA8B,iBAE7FC,aAAc,CACZ,gBAAiB,WAAY,YAAa,cAAe,qBAE3DC,UAAW,CACT,gBAAiB,cAAe,iBAAkB,QAAS,eAUzDC,EAA0B,SAACC,EAAO/G,GAAK,IAAAgH,EAAAC,EAAA,IAAEC,IAAYC,UAAA9B,OAAA,QAAA+B,IAAAD,UAAA,KAAAA,UAAA,GAAO,OAChEnG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,6DAA4DC,SAC1E6F,KAEH5F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClC0E,EAActD,IAAKC,IAAM,IAAA8E,EAAA,OACxBrG,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAMrB,EACNC,MAAOsC,EAAOtC,MACdU,SAAyC,QAAhC0G,EAAA7H,EAASyF,iBAAiBjF,UAAM,IAAAqH,OAAA,EAAhCA,EAAkCC,UAAW/E,EAAOtC,MAC7DqB,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAIzB,EAAK,WAAWuB,EAAEC,OAAOvB,OAC/DgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,WAatBiH,GAA6D,cAAb,QAAhCF,EAAAxH,EAASyF,iBAAiBjF,UAAM,IAAAgH,OAAA,EAAhCA,EAAkCM,UACjDtG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,YACElB,OAAuC,QAAhCgH,EAAAzH,EAASyF,iBAAiBjF,UAAM,IAAAiH,OAAA,EAAhCA,EAAkCM,WAAY,GACrDjG,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAIzB,EAAK,aAAauB,EAAEC,OAAOvB,OACjEgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,gBAAiB,yBAIlC,EAGF4H,EAA+BA,CAACC,EAAYC,KAAS,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACzD9G,EAAAA,EAAAA,MAAA,OAAqBC,UAAU,6DAA4DC,SAAA,EACzFC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE8H,EAAWD,MAEhBzG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SAClB,QADkByG,EACnDxB,EAAuBuB,UAAU,IAAAC,OAAA,EAAjCA,EAAmCrF,IAAKyF,IAAS,IAAAC,EAAAC,EAAA,OAChDjH,EAAAA,EAAAA,MAAA,SAAuBC,UAAU,oBAAmBC,SAAA,EAClDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAA6D,QAApDqH,EAAAxI,EAASyF,iBAAiBO,gBAAgBkC,UAAU,IAAAM,GAAY,QAAZC,EAApDD,EAAsDvC,kBAAU,IAAAwC,OAAZ,EAApDA,EAAkE5H,SAAS0H,MAAc,EAClGzG,SAAWC,GAAM+D,EAAyBoC,EAAW,aAAcK,GACnE9G,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAEmI,EAAUtF,cAAcC,QAAQ,UAAW,IAAKqF,OAR3CA,QAaqC,QAApDH,EAAApI,EAASyF,iBAAiBO,gBAAgBkC,UAAU,IAAAE,GAAY,QAAZC,EAApDD,EAAsDnC,kBAAU,IAAAoC,OAAZ,EAApDA,EAAkEK,KAAKC,GAAW,kBAANA,MAC3EnH,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,UAAW,cAEhBuB,EAAAA,EAAAA,KAAA,YACElB,OAA2D,QAApD6H,EAAAtI,EAASyF,iBAAiBO,gBAAgBkC,UAAU,IAAAI,OAAA,EAApDA,EAAsDpC,UAAW,GACxEpE,SAAWC,GAAM+D,EAAyBoC,EAAW,UAAWnG,EAAEC,OAAOvB,OACzEgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,kBAAmB,8BA9BlC8H,IAsCZ,OACE1G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,4BAA6B,qCAElCuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,iCAAkC,wDAKzCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,CACnD4F,EAAwBlH,EAAE,iBAAkB,mBAAoB,kBAChEkH,EAAwBlH,EAAE,2BAA4B,8BAA+B,mBACrFkH,EAAwBlH,EAAE,kBAAmB,oBAAqB,mBAClEkH,EAAwBlH,EAAE,2BAA4B,8BAA+B,uBAIxFoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,kBAAmB,uBAExBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SApK5C,CAC7B,gCACA,mBACA,cACA,6BACA,QAgK8BoB,IAAKC,IAAM,IAAA6F,EAAA,OACjCpH,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAkD,QAAzCyH,EAAA5I,EAASyF,iBAAiBoD,uBAAe,IAAAD,OAAA,EAAzCA,EAA2C/H,SAASkC,MAAW,EACxEjB,SAAWC,GA3OCb,EAACV,EAAOC,EAAOU,KACvC,MAAMC,EAAepB,EAASyF,iBAAiBjF,IAAU,GACzD,IAAIa,EAGFA,EADEF,EACS,IAAIC,EAAcX,GAElBW,EAAaE,OAAOC,GAAQA,IAASd,GAGlDF,EAAkBC,EAAOa,IAiOIH,CAAkB,kBAAmB6B,EAAQhB,EAAEC,OAAOb,SACvEM,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OARxCA,WAgBlBvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,WAAY,gBAEjBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB4E,EAAiBxD,IAAKC,IACrBvB,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,WACLpB,MAAOsC,EAAOtC,MACdU,QAASnB,EAASyF,iBAAiBqD,WAAa/F,EAAOtC,MACvDqB,SAAWC,GAAMxB,EAAkB,WAAYwB,EAAEC,OAAOvB,OACxDgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,cAezBe,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,2BAA4B,iCAEjCuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB+E,EAAwB3D,IAAKC,IAC5BvB,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,2BACLpB,MAAOsC,EAAOtC,MACdU,QAASnB,EAASyF,iBAAiBsD,2BAA6BhG,EAAOtC,MACvEqB,SAAWC,GAAMxB,EAAkB,2BAA4BwB,EAAEC,OAAOvB,OACxEgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,cAezBe,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,oBAAqB,yBAE1BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBgF,EAAyB5D,IAAKC,IAC7BvB,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,oBACLpB,MAAOsC,EAAOtC,MACdU,QAASnB,EAASyF,iBAAiBuD,oBAAsBjG,EAAOtC,MAChEqB,SAAWC,GAAMxB,EAAkB,oBAAqBwB,EAAEC,OAAOvB,OACjEgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,iBAiB3Be,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,oBAEtBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB6E,EAAqBzD,IAAKC,IACzBvB,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,gBACLpB,MAAOsC,EAAOtC,MACdU,QAASnB,EAASyF,iBAAiBwD,gBAAkBlG,EAAOtC,MAC5DqB,SAAWC,GAAMxB,EAAkB,gBAAiBwB,EAAEC,OAAOvB,OAC7DgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,cAezBe,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,iBAAkB,sBAEvBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB8E,EAAsB1D,IAAKC,IAC1BvB,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,iBACLpB,MAAOsC,EAAOtC,MACdU,QAASnB,EAASyF,iBAAiByD,iBAAmBnG,EAAOtC,MAC7DqB,SAAWC,GAAMxB,EAAkB,iBAAkBwB,EAAEC,OAAOvB,OAC9DgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,iBAiB3Be,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,aAAc,kBAEnBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,gBAAiB,0BAEtBuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,OAA2C,QAApCwE,EAAAjF,EAASyF,iBAAiB0D,kBAAU,IAAAlE,OAAA,EAApCA,EAAsCmE,KAAM,GACnDtH,SAAWC,GAAMxB,EAAkB,gBAAiBwB,EAAEC,OAAOvB,OAC7DgB,UAAU,kKACVS,YAAY,eAGhBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,YAAa,sBAElBuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLnB,OAA2C,QAApCyE,EAAAlF,EAASyF,iBAAiB0D,kBAAU,IAAAjE,OAAA,EAApCA,EAAsCmE,KAAM,GACnDvH,SAAWC,GAAMxB,EAAkB,gBAAiBwB,EAAEC,OAAOvB,OAC7DgB,UAAU,kKACVS,YAAY,WAGhBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,kBAAmB,4BAExBuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLnB,OAA2C,QAApC0E,EAAAnF,EAASyF,iBAAiB0D,kBAAU,IAAAhE,OAAA,EAApCA,EAAsCmE,KAAM,GACnDxH,SAAWC,GAAMxB,EAAkB,gBAAiBwB,EAAEC,OAAOvB,OAC7DgB,UAAU,kKACVS,YAAY,iBAOpBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,kBAAmB,wBAExBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,CACnDsG,EAA6B,cAAe,eAC5CA,EAA6B,YAAa,aAC1CA,EAA6B,mBAAoB,oBACjDA,EAA6B,gBAAiB,iBAC9CA,EAA6B,kBAAmB,mBAChDA,EAA6B,aAAc,cAC3CA,EAA6B,iBAAkB,kBAC/CA,EAA6B,uBAAwB,iBACrDA,EAA6B,eAAgB,gBAC7CA,EAA6B,YAAa,oBAK/CxG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,sBAAuB,4BAE5BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,CA/SnD,CACxB,mBAAoB,qBAAuB,iBAAkB,0BAC7D,sBAAuB,6BAA8B,uBAAwB,eAAgB,QA8SlEoB,IAAKC,IAAM,IAAAwG,EAAAC,EAAA,OAC5BhI,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAsD,QAA7CoI,EAAAvJ,EAASyF,iBAAiBgE,2BAAmB,IAAAF,GAAY,QAAZC,EAA7CD,EAA+CG,kBAAU,IAAAF,OAAZ,EAA7CA,EAA2D3I,SAASkC,MAAW,EACxFjB,SAAWC,IAAO,IAAD4H,EACf,MAAMC,GAAiE,QAA7CD,EAAA3J,EAASyF,iBAAiBgE,2BAAmB,IAAAE,OAAA,EAA7CA,EAA+CD,aAAc,GACvF,IAAIG,EAEFA,EADE9H,EAAEC,OAAOb,QACK,IAAIyI,EAAmB7G,GAEvB6G,EAAkBtI,OAAOC,GAAQA,IAASwB,GAE5DxC,EAAkB,iCAAkCsJ,IAEtDpI,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OAjBxCA,MAqBdpB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAsD,QAA7CiE,EAAApF,EAASyF,iBAAiBgE,2BAAmB,IAAArE,GAAY,QAAZC,EAA7CD,EAA+CsE,kBAAU,IAAArE,OAAZ,EAA7CA,EAA2DxE,SAAS,aAAa,EAC1FiB,SAAWC,IAAO,IAAD+H,EACf,MAAMF,GAAiE,QAA7CE,EAAA9J,EAASyF,iBAAiBgE,2BAAmB,IAAAK,OAAA,EAA7CA,EAA+CJ,aAAc,GACvF,IAAIG,EAEFA,EADE9H,EAAEC,OAAOb,QACK,IAAIyI,EAAmB,UAEvBA,EAAkBtI,OAAOC,GAAiB,WAATA,GAEnDhB,EAAkB,iCAAkCsJ,IAEtDpI,UAAU,UAEZD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,gDAA+CC,SAAA,CAAEtB,EAAE,SAAU,UAAU,QACvFuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLM,YAAa9B,EAAE,UAAW,WAC1BqB,UAAU,yGACV0B,WAAwD,QAA9CmC,EAACtF,EAASyF,iBAAiBgE,2BAAmB,IAAAnE,GAAY,QAAZC,EAA7CD,EAA+CoE,kBAAU,IAAAnE,GAAzDA,EAA2D1E,SAAS,sBAKvFW,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,YACElB,OAAoD,QAA7C+E,EAAAxF,EAASyF,iBAAiBgE,2BAAmB,IAAAjE,OAAA,EAA7CA,EAA+CuC,WAAY,GAClEjG,SAAWC,GAAMxB,EAAkB,+BAAgCwB,EAAEC,OAAOvB,OAC5EgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,gBAAiB,iCC1N9C,EAvSgBL,IAAqE,IAApE,cAAEgK,EAAgB,GAAE,aAAEC,EAAY,MAAEzC,EAAK,WAAE0C,GAAa,GAAMlK,EAC7E,MAAM,EAAEK,IAAME,EAAAA,EAAAA,KACR4J,GAASC,EAAAA,EAAAA,QAAO,OACfC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,MAGzCC,EAAY,CAChBC,KAAM,CACJnE,MAAOjG,EAAE,OAAQ,QACjBqK,MAAO,UACPC,KAAM,qGAERC,KAAM,CACJtE,MAAOjG,EAAE,OAAQ,QACjBqK,MAAO,UACPC,KAAM,uCAERE,aAAc,CACZvE,MAAOjG,EAAE,eAAgB,iBACzBqK,MAAO,UACPC,KAAM,uEAERG,cAAe,CACbxE,MAAOjG,EAAE,gBAAiB,kBAC1BqK,MAAO,UACPC,KAAM,wEAERI,QAAS,CACPzE,MAAOjG,EAAE,UAAW,YACpBqK,MAAO,UACPC,KAAM,sCAERK,SAAU,CACR1E,MAAOjG,EAAE,WAAY,aACrBqK,MAAO,UACPC,KAAM,yCAERM,YAAa,CACX3E,MAAOjG,EAAE,cAAe,gBACxBqK,MAAO,UACPC,KAAM,qCAERO,aAAc,CACZ5E,MAAOjG,EAAE,eAAgB,iBACzBqK,MAAO,UACPC,KAAM,yCAERQ,SAAU,CACR7E,MAAOjG,EAAE,WAAY,aACrBqK,MAAO,UACPC,KAAM,qCAERS,UAAW,CACT9E,MAAOjG,EAAE,YAAa,cACtBqK,MAAO,UACPC,KAAM,yCAERU,MAAO,CACL/E,MAAOjG,EAAE,QAAS,SAClBqK,MAAO,UACPC,KAAM,yCAERW,QAAS,CACPhF,MAAOjG,EAAE,UAAW,WACpBqK,MAAO,UACPC,KAAM,yCAERY,OAAQ,CACNjF,MAAOjG,EAAE,SAAU,UACnBqK,MAAO,UACPC,KAAM,yCAERa,UAAW,CACTlF,MAAOjG,EAAE,YAAa,cACtBqK,MAAO,UACPC,KAAM,yCAERc,WAAY,CACVnF,MAAOjG,EAAE,aAAc,eACvBqK,MAAO,UACPC,KAAM,yCAERe,SAAU,CACRpF,MAAOjG,EAAE,WAAY,aACrBqK,MAAO,UACPC,KAAM,yCAERgB,UAAW,CACTrF,MAAOjG,EAAE,YAAa,cACtBqK,MAAO,UACPC,KAAM,yCAERiB,QAAS,CACPtF,MAAOjG,EAAE,UAAW,YACpBqK,MAAO,UACPC,KAAM,yCAERkB,SAAU,CACRvF,MAAOjG,EAAE,WAAY,aACrBqK,MAAO,UACPC,KAAM,yCAERmB,SAAU,CACRxF,MAAOjG,EAAE,WAAY,aACrBqK,MAAO,UACPC,KAAM,yCAERoB,UAAW,CACTzF,MAAOjG,EAAE,YAAa,cACtBqK,MAAO,UACPC,KAAM,yCAERqB,UAAW,CACT1F,MAAOjG,EAAE,YAAa,cACtBqK,MAAO,UACPC,KAAM,yCAERsB,UAAW,CACT3F,MAAOjG,EAAE,YAAa,cACtBqK,MAAO,UACPC,KAAM,0CAIJuB,EAAmBC,IACvB,GAAIlC,EAAc,CAChB,MAAMmC,EAAmBpC,EAAclJ,SAASqL,GAC5CnC,EAAczI,OAAO8K,GAAQA,IAASF,GACtC,IAAInC,EAAemC,GACvBlC,EAAamC,EACf,GAGIE,EAAcH,GAAYnC,EAAclJ,SAASqL,GAEvD,OACE1K,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,CAClG6F,IACC5F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SAAE6F,KAG5E/F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAE9CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wEAAuEC,SAClFtB,EAAE,YAAa,iBAElBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBC,UAClCF,EAAAA,EAAAA,MAAA,OACE8K,IAAKpC,EACLqC,MAAM,MACNC,OAAO,MACPC,QAAQ,cACRhL,UAAU,qFAAoFC,SAAA,EAG9FC,EAAAA,EAAAA,KAAA,QACE+K,EAAE,0iBAEFC,KAAK,OACLC,OAAO,UACPC,YAAY,IACZpL,UAAU,yBAIXqL,OAAOC,QAAQxC,GACbjJ,OAAO0L,IAAA,IAAEC,GAAID,EAAA,OAAMC,EAAIpM,SAAS,UAChCiC,IAAIoK,IAAA,IAAEhB,EAASE,GAAKc,EAAA,OACrB1L,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QACE+K,EAAGN,EAAK1B,KACRiC,KAAMN,EAAWH,GAAWE,EAAK3B,MAAQ,cACzCmC,OAAQR,EAAK3B,MACboC,YAAY,IACZM,QAASd,EAAWH,GAAW,GAAO9B,IAAgB8B,EAAU,GAAM,GACtEzK,UAAU,iDACV2L,QAASA,IAAMnB,EAAgBC,GAC/BmB,aAAcA,IAAMhD,EAAe6B,GACnCoB,aAAcA,IAAMjD,EAAe,QAEpCD,IAAgB8B,IACfvK,EAAAA,EAAAA,KAAA,QACE4L,EAAE,MACFC,EAAE,KACFC,WAAW,SACXhM,UAAU,oDAAmDC,SAE5D0K,EAAK/F,UAnBJ6F,cA6BhB1K,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wEAAuEC,SAClFtB,EAAE,WAAY,gBAEjBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sBAAqBC,UAClCF,EAAAA,EAAAA,MAAA,OACE+K,MAAM,MACNC,OAAO,MACPC,QAAQ,cACRhL,UAAU,qFAAoFC,SAAA,EAG9FC,EAAAA,EAAAA,KAAA,QACE+K,EAAE,oLAEFC,KAAK,OACLC,OAAO,UACPC,YAAY,IACZpL,UAAU,yBAIXqL,OAAOC,QAAQxC,GACbjJ,OAAOoM,IAAA,IAAET,GAAIS,EAAA,OAAKT,EAAIpM,SAAS,UAC/BiC,IAAI6K,IAAA,IAAEzB,EAASE,GAAKuB,EAAA,OACrBnM,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QACE+K,EAAGN,EAAK1B,KACRiC,KAAMN,EAAWH,GAAWE,EAAK3B,MAAQ,cACzCmC,OAAQR,EAAK3B,MACboC,YAAY,IACZM,QAASd,EAAWH,GAAW,GAAO9B,IAAgB8B,EAAU,GAAM,GACtEzK,UAAU,iDACV2L,QAASA,IAAMnB,EAAgBC,GAC/BmB,aAAcA,IAAMhD,EAAe6B,GACnCoB,aAAcA,IAAMjD,EAAe,QAEpCD,IAAgB8B,IACfvK,EAAAA,EAAAA,KAAA,QACE4L,EAAE,MACFC,EAAE,KACFC,WAAW,SACXhM,UAAU,oDAAmDC,SAE5D0K,EAAK/F,UAnBJ6F,gBA8BjBnC,EAAclE,OAAS,IACtBrE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DC,SAAA,CACtEtB,EAAE,gBAAiB,kBAAkB,QAExCuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCqI,EAAcjH,IAAKoJ,IAAO,IAAA0B,EAAA,OACzBpM,EAAAA,EAAAA,MAAA,QAEEC,UAAU,oIAAmIC,SAAA,CAE1H,QAF0HkM,EAE5IrD,EAAU2B,UAAQ,IAAA0B,OAAA,EAAlBA,EAAoBvH,OACrB1E,EAAAA,EAAAA,KAAA,UACEyL,QAASA,IAAMnB,EAAgBC,GAC/BzK,UAAU,qFAAoFC,SAC/F,WAPIwK,UAiBdjC,IACCzI,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DC,SAAA,CACtEtB,EAAE,eAAgB,gBAAgB,QAErCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGtB,EAAE,oBAAqB,mDAC9BoB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGtB,EAAE,gBAAiB,4CAC1BoB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGtB,EAAE,oBAAqB,sCAC9BoB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGtB,EAAE,qBAAsB,gEC2S3C,EAzkBgCL,IAAsD,IAAD8N,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAApD,SAAEzO,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EAC9E,MAAM,EAAEK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KAEfC,EAAoBA,CAACC,EAAOC,KAChC,MAAMC,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAAS0O,qBAG9B,GAAIlO,EAAMK,SAAS,KAAM,CACvB,MAAM6E,EAAQlF,EAAMQ,MAAM,KAC1B,IAAI2E,EAAUjF,EACd,IAAK,IAAIkF,EAAI,EAAGA,EAAIF,EAAMG,OAAS,EAAGD,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMG,OAAS,IAAMpF,CACrC,MACEC,EAAQF,GAASC,EAGnBR,EAAe,sBAAuBS,GAGlCR,EAAOM,IACTL,EAAUc,IAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUM,GAAI,IAAE,CAACT,GAAQ,SAkBrC4F,EAAgB,CACpB,CAAE3F,MAAO,SAAU4F,MAAOjG,EAAE,SAAU,WACtC,CAAEK,MAAO,WAAY4F,MAAOjG,EAAE,WAAY,aAC1C,CAAEK,MAAO,iBAAkB4F,MAAOjG,EAAE,gBAAiB,oBAIjDuO,EAAqB,CACzB,CAAElO,MAAO,UAAW4F,MAAOjG,EAAE,UAAW,YACxC,CAAEK,MAAO,aAAc4F,MAAOjG,EAAE,aAAc,eAC9C,CAAEK,MAAO,eAAgB4F,MAAOjG,EAAE,cAAe,kBAI7CwO,EAAe,CACnB,CAAEnO,MAAO,SAAU4F,MAAOjG,EAAE,SAAU,WACtC,CAAEK,MAAO,UAAW4F,MAAOjG,EAAE,UAAW,YACxC,CAAEK,MAAO,iBAAkB4F,MAAOjG,EAAE,gBAAiB,oBAqBjDyO,EAAmB,CACvB,CAAEpO,MAAO,qBAAsB4F,MAAOjG,EAAE,mBAAoB,uBAC5D,CAAEK,MAAO,wBAAyB4F,MAAOjG,EAAE,sBAAuB,0BAClE,CAAEK,MAAO,aAAc4F,MAAOjG,EAAE,YAAa,eAC7C,CAAEK,MAAO,QAAS4F,MAAOjG,EAAE,QAAS,WAIhC0O,EAAkB,CACtB,CAAErO,MAAO,cAAe4F,MAAOjG,EAAE,cAAe,gBAChD,CAAEK,MAAO,cAAe4F,MAAOjG,EAAE,cAAe,gBAChD,CAAEK,MAAO,QAAS4F,MAAOjG,EAAE,QAAS,WAGhCkH,EAA0B,SAACC,EAAO/G,GAAK,IAAAuO,EAAAC,EAAAC,EAAA,IAAEC,EAAOvH,UAAA9B,OAAA,QAAA+B,IAAAD,UAAA,GAAAA,UAAA,GAAGvB,EAAesB,IAAYC,UAAA9B,OAAA,QAAA+B,IAAAD,UAAA,KAAAA,UAAA,GAAO,OACzFnG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,6DAA4DC,SAC1E6F,KAEH5F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCwN,EAAQpM,IAAKC,IAAM,IAAAoM,EAAA,OAClB3N,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAMrB,EACNC,MAAOsC,EAAOtC,MACdU,SAA4C,QAAnCgO,EAAAnP,EAAS0O,oBAAoBlO,UAAM,IAAA2O,OAAA,EAAnCA,EAAqCrH,UAAW/E,EAAOtC,MAChEqB,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAIzB,EAAK,WAAWuB,EAAEC,OAAOvB,OAC/DgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,WAatBiH,IAAiE,cAAb,QAAnCqH,EAAA/O,EAAS0O,oBAAoBlO,UAAM,IAAAuO,OAAA,EAAnCA,EAAqCjH,SAAyE,aAAb,QAAnCkH,EAAAhP,EAAS0O,oBAAoBlO,UAAM,IAAAwO,OAAA,EAAnCA,EAAqClH,WACnHtG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,YACElB,OAA0C,QAAnCwO,EAAAjP,EAAS0O,oBAAoBlO,UAAM,IAAAyO,OAAA,EAAnCA,EAAqClH,WAAY,GACxDjG,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAIzB,EAAK,aAAauB,EAAEC,OAAOvB,OACjEgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,gBAAiB,yBAIlC,EAGR,OACEoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,iCAAkC,yCAEvCuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,oCAAqC,kDAK5CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,CACnD4F,EAAwBlH,EAAE,mBAAoB,sBAAuB,oBACrEkH,EAAwBlH,EAAE,+BAAgC,oCAAqC,cAAeuO,OAIjHnN,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,QAAS,YAEdoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCkN,EAAa9L,IAAKC,IAAM,IAAAqM,EAAA,OACvB5N,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,QACLpB,MAAOsC,EAAOtC,MACdU,SAA2C,QAAlCiO,EAAApP,EAAS0O,oBAAoBW,aAAK,IAAAD,OAAA,EAAlCA,EAAoCtH,UAAW/E,EAAOtC,MAC/DqB,SAAWC,GAAMxB,EAAkB,eAAgBwB,EAAEC,OAAOvB,OAC5DgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,WAayB,aAAb,QAAlCoN,EAAA7N,EAAS0O,oBAAoBW,aAAK,IAAAxB,OAAA,EAAlCA,EAAoC/F,UACnCtG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,QAAS,YAEduB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SAhH1C,CACxB,WAAY,cAAe,UAAW,6BAgHLoB,IAAKwM,IAAK,IAAAC,EAAAC,EAAA,OAC3BhO,EAAAA,EAAAA,MAAA,SAAmBC,UAAU,oBAAmBC,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAA2C,QAAlCoO,EAAAvP,EAAS0O,oBAAoBW,aAAK,IAAAE,GAAO,QAAPC,EAAlCD,EAAoCD,aAAK,IAAAE,OAAP,EAAlCA,EAA2C3O,SAASyO,MAAU,EACvExN,SAAWC,IAAO,IAAD0N,EACf,MAAMC,GAAkD,QAAlCD,EAAAzP,EAAS0O,oBAAoBW,aAAK,IAAAI,OAAA,EAAlCA,EAAoCH,QAAS,GACnE,IAAIK,EAEFA,EADE5N,EAAEC,OAAOb,QACC,IAAIuO,EAAeJ,GAEnBI,EAAcpO,OAAOC,GAAQA,IAAS+N,GAEpD/O,EAAkB,cAAeoP,IAEnClO,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAE4N,MAhBlDA,WAqBlB9N,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,YACElB,OAAyC,QAAlCqN,EAAA9N,EAAS0O,oBAAoBW,aAAK,IAAAvB,OAAA,EAAlCA,EAAoC/F,WAAY,GACvDjG,SAAWC,GAAMxB,EAAkB,iBAAkBwB,EAAEC,OAAOvB,OAC9DgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,gBAAiB,iCAS5CoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,sBAAuB,4BAE5BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAxJxC,CACjC,aAAc,gBAAiB,WAAY,WAAY,iBAwJrBoB,IAAKC,IAAM,IAAA6M,EAAA,OACrCpO,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAyD,QAAhDyO,EAAA5P,EAAS0O,oBAAoBmB,2BAAmB,IAAAD,OAAA,EAAhDA,EAAkD/O,SAASkC,MAAW,EAC/EjB,SAAWC,GAtMCb,EAACV,EAAOC,EAAOU,KACvC,MAAMC,EAAepB,EAAS0O,oBAAoBlO,IAAU,GAC5D,IAAIa,EAGFA,EADEF,EACS,IAAIC,EAAcX,GAElBW,EAAaE,OAAOC,GAAQA,IAASd,GAGlDF,EAAkBC,EAAOa,IA4LIH,CAAkB,sBAAuB6B,EAAQhB,EAAEC,OAAOb,SAC3EM,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE2C,EAAOE,cAAcC,QAAQ,UAAW,IAAKH,OARxCA,WAgBlBvB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,iCAAkC,0CAEvCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,gBACLpB,MAAM,MACNU,QAAmD,QAA5C4M,EAAE/N,EAAS0O,oBAAoBoB,qBAAa,IAAA/B,OAAA,EAA1CA,EAA4CgC,SACrDjO,SAAWC,GAAMxB,EAAkB,yBAA6C,QAAnBwB,EAAEC,OAAOvB,OACtEgB,UAAU,SAEXrB,EAAE,MAAO,WAEZoB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,gBACLpB,MAAM,KACNU,UAAoD,QAA3C6M,EAAChO,EAAS0O,oBAAoBoB,qBAAa,IAAA9B,GAA1CA,EAA4C+B,UACtDjO,SAAWC,GAAMxB,EAAkB,yBAA6C,QAAnBwB,EAAEC,OAAOvB,OACtEgB,UAAU,SAEXrB,EAAE,KAAM,aAG8B,QAA1C6N,EAAAjO,EAAS0O,oBAAoBoB,qBAAa,IAAA7B,OAAA,EAA1CA,EAA4C8B,YAC3CvO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,aAAc,kBAEnBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SAxMjD,CACjB,mBAAoB,kBAAmB,cAAe,kBACtD,uBAAwB,qBAAsB,gBAAiB,oBAC/D,sBAsM0BoB,IAAKlB,IAAI,IAAAoO,EAAAC,EAAA,OACnBzO,EAAAA,EAAAA,MAAA,SAAkBC,UAAU,oBAAmBC,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAmD,QAA1C6O,EAAAhQ,EAAS0O,oBAAoBoB,qBAAa,IAAAE,GAAO,QAAPC,EAA1CD,EAA4CE,aAAK,IAAAD,OAAP,EAA1CA,EAAmDpP,SAASe,MAAS,EAC9EE,SAAWC,IAAO,IAADoO,EACf,MAAMC,GAAyD,QAA1CD,EAAAnQ,EAAS0O,oBAAoBoB,qBAAa,IAAAK,OAAA,EAA1CA,EAA4CD,QAAS,GAC1E,IAAIG,EAEFA,EADEtO,EAAEC,OAAOb,QACA,IAAIiP,EAAcxO,GAElBwO,EAAa9O,OAAOC,GAAQA,IAASK,GAElDrB,EAAkB,sBAAuB8P,IAE3C5O,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAEwB,EAAKqB,cAAcC,QAAQ,UAAW,IAAKtB,OAjBtCA,WAuBlBJ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,YACElB,OAAiD,QAA1CyN,EAAAlO,EAAS0O,oBAAoBoB,qBAAa,IAAA5B,OAAA,EAA1CA,EAA4CnG,WAAY,GAC/DjG,SAAWC,GAAMxB,EAAkB,yBAA0BwB,EAAEC,OAAOvB,OACtEgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,gBAAiB,iCAS5CoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,iBAAkB,sBAEvBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,iBACLpB,MAAM,MACNU,QAAoD,QAA7CgN,EAAEnO,EAAS0O,oBAAoB4B,sBAAc,IAAAnC,OAAA,EAA3CA,EAA6CoC,QACtDzO,SAAWC,GAAMxB,EAAkB,yBAA6C,QAAnBwB,EAAEC,OAAOvB,OACtEgB,UAAU,SAEXrB,EAAE,MAAO,WAEZoB,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,iBACLpB,MAAM,KACNU,UAAqD,QAA5CiN,EAACpO,EAAS0O,oBAAoB4B,sBAAc,IAAAlC,GAA3CA,EAA6CmC,SACvDzO,SAAWC,GAAMxB,EAAkB,yBAA6C,QAAnBwB,EAAEC,OAAOvB,OACtEgB,UAAU,SAEXrB,EAAE,KAAM,aAG+B,QAA3CiO,EAAArO,EAAS0O,oBAAoB4B,sBAAc,IAAAjC,OAAA,EAA3CA,EAA6CkC,WAC5C/O,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,YAAa,wBAElBuB,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLW,IAAI,IACJC,IAAI,KACJ/B,OAAkD,QAA3C6N,EAAAtO,EAAS0O,oBAAoB4B,sBAAc,IAAAhC,OAAA,EAA3CA,EAA6CkC,YAAa,GACjE1O,SAAWC,GAAMxB,EAAkB,2BAA4BwB,EAAEC,OAAOvB,OACxEgB,UAAU,kKACVS,YAAY,aAGhBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,QAAS,YAEdoB,EAAAA,EAAAA,MAAA,UACEf,OAAkD,QAA3C8N,EAAAvO,EAAS0O,oBAAoB4B,sBAAc,IAAA/B,OAAA,EAA3CA,EAA6CkC,QAAS,GAC7D3O,SAAWC,GAAMxB,EAAkB,uBAAwBwB,EAAEC,OAAOvB,OACpEgB,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,GAAEiB,SAAEtB,EAAE,cAAe,kBAClCyO,EAAiB/L,IAAKC,IACrBpB,EAAAA,EAAAA,KAAA,UAA2BlB,MAAOsC,EAAOtC,MAAMiB,SAC5CqB,EAAOsD,OADGtD,EAAOtC,kBAO5Be,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,WAAY,gBAEjBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCoN,EAAgBhM,IAAKC,IAAM,IAAA2N,EAAA,OAC1BlP,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,WACLpB,MAAOsC,EAAOtC,MACdU,SAAoD,QAA3CuP,EAAA1Q,EAAS0O,oBAAoB4B,sBAAc,IAAAI,OAAA,EAA3CA,EAA6C9O,QAASmB,EAAOtC,MACtEqB,SAAWC,GAAMxB,EAAkB,sBAAuBwB,EAAEC,OAAOvB,OACnEgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,eAgBzBkB,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAACgP,EAAO,CACNpJ,MAAOnH,EAAE,eAAgB,yCACzB2J,eAA0D,QAA3CyE,EAAAxO,EAAS0O,oBAAoB4B,sBAAc,IAAA9B,OAAA,EAA3CA,EAA6CoC,eAAgB,GAC5E5G,aAAe6G,GAAUtQ,EAAkB,8BAA+BsQ,GAC1E5G,YAAY,OAKhBzI,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,YAAa,iBAElBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CACC,CAAEjB,MAAO,kBAAmB4F,MAAOjG,EAAE,eAAgB,oBACrD,CAAEK,MAAO,oBAAqB4F,MAAOjG,EAAE,oBAAqB,yBAC5D,CAAEK,MAAO,sBAAuB4F,MAAOjG,EAAE,qBAAsB,2BAC/D,CAAEK,MAAO,UAAW4F,MAAOjG,EAAE,UAAW,aACxC0C,IAAKC,IAAM,IAAA+N,EAAA,OACXtP,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,YACLpB,MAAOsC,EAAOtC,MACdU,SAAoD,QAA3C2P,EAAA9Q,EAAS0O,oBAAoB4B,sBAAc,IAAAQ,OAAA,EAA3CA,EAA6CC,SAAUhO,EAAOtC,MACvEqB,SAAWC,GAAMxB,EAAkB,uBAAwBwB,EAAEC,OAAOvB,OACpEgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,eAezBe,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,gBAAiB,qBAEtBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CACC,CAAEjB,MAAO,WAAY4F,MAAOjG,EAAE,WAAY,aAC1C,CAAEK,MAAO,eAAgB4F,MAAOjG,EAAE,eAAgB,iBAClD,CAAEK,MAAO,aAAc4F,MAAOjG,EAAE,aAAc,eAC9C,CAAEK,MAAO,OAAQ4F,MAAOjG,EAAE,OAAQ,UAClC0C,IAAKC,IAAM,IAAAiO,EAAA,OACXxP,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,gBACLpB,MAAOsC,EAAOtC,MACdU,SAAoD,QAA3C6P,EAAAhR,EAAS0O,oBAAoB4B,sBAAc,IAAAU,OAAA,EAA3CA,EAA6CC,aAAclO,EAAOtC,MAC3EqB,SAAWC,GAAMxB,EAAkB,2BAA4BwB,EAAEC,OAAOvB,OACxEgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,kBAiB3Be,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,kBAAmB,uBAExBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE,CACC,aAAc,YAAa,UAAW,SAAU,QAAS,OACzD,WAAY,WAAY,WAAY,WAAY,WAAY,aAC5DoB,IAAKoO,IAAW,IAAAC,EAAAC,EAAA,OAChB5P,EAAAA,EAAAA,MAAA,SAAyBC,UAAU,oBAAmBC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAoD,QAA3CgQ,EAAAnR,EAAS0O,oBAAoB4B,sBAAc,IAAAa,GAAa,QAAbC,EAA3CD,EAA6CD,mBAAW,IAAAE,OAAb,EAA3CA,EAA0DvQ,SAASqQ,MAAgB,EAC5FpP,SAAWC,IAAO,IAADsP,EACf,MAAMC,GAAiE,QAA3CD,EAAArR,EAAS0O,oBAAoB4B,sBAAc,IAAAe,OAAA,EAA3CA,EAA6CH,cAAe,GACxF,IAAIK,EAEFA,EADExP,EAAEC,OAAOb,QACO,IAAImQ,EAAqBJ,GAEzBI,EAAoBhQ,OAAOC,GAAQA,IAAS2P,GAEhE3Q,EAAkB,6BAA8BgR,IAElD9P,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAE8Q,EAAYjO,cAAcC,QAAQ,UAAW,IAAKgO,OAjB7CA,WAyBlB1P,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,qBAAsB,0BAE3BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CACC,WAAY,UAAW,UAAW,SAAU,WAAY,OAAQ,oBAChEoB,IAAK0O,IAAM,IAAAC,EAAAC,EAAA,OACXlQ,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAoD,QAA3CsQ,EAAAzR,EAAS0O,oBAAoB4B,sBAAc,IAAAmB,GAAoB,QAApBC,EAA3CD,EAA6CE,0BAAkB,IAAAD,OAApB,EAA3CA,EAAiE7Q,SAAS2Q,MAAW,EAC9F1P,SAAWC,IAAO,IAAD6P,EACf,MAAMC,GAA4D,QAA3CD,EAAA5R,EAAS0O,oBAAoB4B,sBAAc,IAAAsB,OAAA,EAA3CA,EAA6CD,qBAAsB,GAC1F,IAAIG,EAEFA,EADE/P,EAAEC,OAAOb,QACE,IAAI0Q,EAAgBL,GAEpBK,EAAevQ,OAAOC,GAAQA,IAASiQ,GAEtDjR,EAAkB,oCAAqCuR,IAEzDrQ,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAEoR,EAAOvO,cAAcC,QAAQ,UAAW,IAAKsO,OAjBxCA,WAwBlBhQ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,mBAAoB,wBAEzBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CACC,OAAQ,OAAQ,OAAQ,aAAc,WAAY,UAAW,mBAC7DoB,IAAK0O,IAAM,IAAAO,EAAAC,EAAA,OACXxQ,EAAAA,EAAAA,MAAA,SAAoBC,UAAU,oBAAmBC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAoD,QAA3C4Q,EAAA/R,EAAS0O,oBAAoB4B,sBAAc,IAAAyB,GAAkB,QAAlBC,EAA3CD,EAA6CE,wBAAgB,IAAAD,OAAlB,EAA3CA,EAA+DnR,SAAS2Q,MAAW,EAC5F1P,SAAWC,IAAO,IAADmQ,EACf,MAAML,GAA4D,QAA3CK,EAAAlS,EAAS0O,oBAAoB4B,sBAAc,IAAA4B,OAAA,EAA3CA,EAA6CD,mBAAoB,GACxF,IAAIH,EAEFA,EADE/P,EAAEC,OAAOb,QACE,IAAI0Q,EAAgBL,GAEpBK,EAAevQ,OAAOC,GAAQA,IAASiQ,GAEtDjR,EAAkB,kCAAmCuR,IAEvDrQ,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDtB,EAAEoR,EAAOvO,cAAcC,QAAQ,UAAW,IAAKsO,OAjBxCA,QAsBhBhQ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sDAAqDC,SACnEtB,EAAE,wBAAyB,8BAE9BuB,EAAAA,EAAAA,KAAA,YACElB,OAAkD,QAA3CgO,EAAAzO,EAAS0O,oBAAoB4B,sBAAc,IAAA7B,OAAA,EAA3CA,EAA6C0D,uBAAwB,GAC5ErQ,SAAWC,GAAMxB,EAAkB,sCAAuCwB,EAAEC,OAAOvB,OACnFgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,oBAAqB,gECrV1D,EAxO6BL,IAAsD,IAADqS,EAAAC,EAAAC,EAAA,IAApD,SAAEtS,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EAC3E,MAAM,EAAEK,IAAME,EAAAA,EAAAA,KAERC,EAAoBA,CAACC,EAAOC,KAChC,MAAMC,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAASuS,kBAG9B,GAAI/R,EAAMK,SAAS,KAAM,CACvB,MAAM6E,EAAQlF,EAAMQ,MAAM,KAC1B,IAAI2E,EAAUjF,EACd,IAAK,IAAIkF,EAAI,EAAGA,EAAIF,EAAMG,OAAS,EAAGD,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMG,OAAS,IAAMpF,CACrC,MACEC,EAAQF,GAASC,EAGnBR,EAAe,mBAAoBS,GAG/BR,EAAOM,IACTL,EAAUc,IAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUM,GAAI,IAAE,CAACT,GAAQ,SAKrC4F,EAAgB,CACpB,CAAE3F,MAAO,SAAU4F,MAAOjG,EAAE,SAAU,WACtC,CAAEK,MAAO,WAAY4F,MAAOjG,EAAE,WAAY,aAC1C,CAAEK,MAAO,0BAA2B4F,MAAOjG,EAAE,wBAAyB,6BAIlEoS,EAAsB,CAC1B,CAAE/R,MAAO,SAAU4F,MAAOjG,EAAE,SAAU,WACtC,CAAEK,MAAO,WAAY4F,MAAOjG,EAAE,WAAY,aAC1C,CAAEK,MAAO,iBAAkB4F,MAAOjG,EAAE,gBAAiB,oBAIjDqS,EAAe,CACnB,CAAExF,IAAK,OAAQ5G,MAAOjG,EAAE,OAAQ,SAChC,CAAE6M,IAAK,aAAc5G,MAAOjG,EAAE,aAAc,gBAC5C,CAAE6M,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,eAC1C,CAAE6M,IAAK,cAAe5G,MAAOjG,EAAE,cAAe,gBAC9C,CAAE6M,IAAK,eAAgB5G,MAAOjG,EAAE,eAAgB,iBAChD,CAAE6M,IAAK,iBAAkB5G,MAAOjG,EAAE,iBAAkB,oBAIhDsS,EAAoB,CACxB,CAAEzF,IAAK,aAAc5G,MAAOjG,EAAE,aAAc,gBAC5C,CAAE6M,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,gBAC1C,CAAE6M,IAAK,eAAgB5G,MAAOjG,EAAE,eAAgB,mBAChD,CAAE6M,IAAK,aAAc5G,MAAOjG,EAAE,aAAc,iBAC5C,CAAE6M,IAAK,4BAA6B5G,MAAOjG,EAAE,4BAA6B,gCAC1E,CAAE6M,IAAK,qBAAsB5G,MAAOjG,EAAE,qBAAsB,wBAGxDuS,EAA+BA,CAACC,EAAUrL,KAAK,IAAAsL,EAAAC,EAAA,OACnDtR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SAAE6F,KAG1E/F,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,qBAEtBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClC0E,EAActD,IAAKC,IAAM,IAAAgQ,EAAA,OACxBvR,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAI,GAAAI,OAAK2Q,EAAQ,UACjBnS,MAAOsC,EAAOtC,MACdU,SAA4C,QAAnC4R,EAAA/S,EAASuS,iBAAiBK,UAAS,IAAAG,OAAA,EAAnCA,EAAqCjL,UAAW/E,EAAOtC,MAChEqB,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI2Q,EAAQ,WAAW7Q,EAAEC,OAAOvB,OAClEgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,eAgByB,cAAb,QAAnCoS,EAAA7S,EAASuS,iBAAiBK,UAAS,IAAAC,OAAA,EAAnCA,EAAqC/K,SACW,6BAAb,QAAnCgL,EAAA9S,EAASuS,iBAAiBK,UAAS,IAAAE,OAAA,EAAnCA,EAAqChL,WACrCnG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,qEAAoEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,SAAAD,UACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oHAAmHC,SAC9HtB,EAAE,aAAc,iBAEnBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,OAAQ,WAEbuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,QAAS,iBAIlBuB,EAAAA,EAAAA,KAAA,SAAAD,SACG+Q,EAAa3P,IAAKlB,IAAI,IAAAoR,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACrB7R,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8FAA6FC,SACxGE,EAAKyE,SAER1E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,OAA0C,QAAnCuS,EAAAhT,EAASuS,iBAAiBK,UAAS,IAAAI,GAAa,QAAbC,EAAnCD,EAAqCM,mBAAW,IAAAL,GAAY,QAAZC,EAAhDD,EAAmDrR,EAAKqL,YAAI,IAAAiG,OAAzB,EAAnCA,EAA8DK,OAAQ,GAC7EzR,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI2Q,EAAQ,iBAAA3Q,OAAgBL,EAAKqL,IAAG,SAASlL,EAAEC,OAAOvB,OACxFgB,UAAU,uKACVS,YAAa9B,EAAE,iBAAkB,wBAGrCuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,OAA0C,QAAnC0S,EAAAnT,EAASuS,iBAAiBK,UAAS,IAAAO,GAAa,QAAbC,EAAnCD,EAAqCG,mBAAW,IAAAF,GAAY,QAAZC,EAAhDD,EAAmDxR,EAAKqL,YAAI,IAAAoG,OAAzB,EAAnCA,EAA8DG,QAAS,GAC9E1R,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI2Q,EAAQ,iBAAA3Q,OAAgBL,EAAKqL,IAAG,UAAUlL,EAAEC,OAAOvB,OACzFgB,UAAU,uKACVS,YAAa9B,EAAE,iBAAkB,yBAnB9BwB,EAAKqL,kBA+B5B,OACEzL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,+BAAgC,uCAErCuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,qCAAsC,kDAK5CuS,EAA6B,mBAAoBvS,EAAE,4BAA6B,iCAGhFuS,EAA6B,mBAAoBvS,EAAE,4BAA6B,kCAGjFoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,gCAAiC,0CAItCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,qBAEtBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClC0E,EAActD,IAAKC,IAAM,IAAA0Q,EAAA,OACxBjS,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,qBACLpB,MAAOsC,EAAOtC,MACdU,SAAgE,QAAvDsS,EAAAzT,EAASuS,iBAAiBmB,qCAA6B,IAAAD,OAAA,EAAvDA,EAAyD3L,UAAW/E,EAAOtC,MACpFqB,SAAWC,GAAMxB,EAAkB,uCAAwCwB,EAAEC,OAAOvB,OACpFgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,eAgB6C,cAAb,QAAvD2R,EAAApS,EAASuS,iBAAiBmB,qCAA6B,IAAAtB,OAAA,EAAvDA,EAAyDtK,SACW,6BAAb,QAAvDuK,EAAArS,EAASuS,iBAAiBmB,qCAA6B,IAAArB,OAAA,EAAvDA,EAAyDvK,WACzDtG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDgR,EAAkB5P,IAAK6Q,IACtBnS,EAAAA,EAAAA,MAAA,OAAoBC,UAAU,6DAA4DC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EiS,EAAKtN,SAER1E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClC8Q,EAAoB1P,IAAKC,IAAM,IAAA6Q,EAAAC,EAAA,OAC9BrS,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAI,gBAAAI,OAAkB0R,EAAK1G,KAC3BxM,MAAOsC,EAAOtC,MACdU,SAAgE,QAAvDyS,EAAA5T,EAASuS,iBAAiBmB,qCAA6B,IAAAE,GAAa,QAAbC,EAAvDD,EAAyDN,mBAAW,IAAAO,OAAb,EAAvDA,EAAuEF,EAAK1G,QAASlK,EAAOtC,MACrGqB,SAAWC,GAAMxB,EAAkB,6CAAD0B,OAA8C0R,EAAK1G,KAAOlL,EAAEC,OAAOvB,OACrGgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,aANfkT,EAAK1G,SAwBnBzL,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,YACElB,OAA8D,QAAvD6R,EAAAtS,EAASuS,iBAAiBmB,qCAA6B,IAAApB,OAAA,EAAvDA,EAAyDvK,WAAY,GAC5EjG,SAAWC,GAAMxB,EAAkB,yCAA0CwB,EAAEC,OAAOvB,OACtFgD,KAAM,EACNhC,UAAU,kKACVS,YAAa9B,EAAE,gBAAiB,iCC8JhD,EA5XiCL,IAAsD,IAArD,SAAEC,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EAC/E,MAAM,EAAEK,IAAME,EAAAA,EAAAA,KAERC,EAAoBA,CAACC,EAAOC,KAChC,MAAMC,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAAS8T,sBAG9B,GAAItT,EAAMK,SAAS,KAAM,CACvB,MAAM6E,EAAQlF,EAAMQ,MAAM,KAC1B,IAAI2E,EAAUjF,EACd,IAAK,IAAIkF,EAAI,EAAGA,EAAIF,EAAMG,OAAS,EAAGD,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMG,OAAS,IAAMpF,CACrC,MACEC,EAAQF,GAASC,EAGnBR,EAAe,uBAAwBS,GAGnCR,EAAOM,IACTL,EAAUc,IAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUM,GAAI,IAAE,CAACT,GAAQ,SAKrCuT,EAAmB,CACvB,CAAEtT,MAAO,IAAK4F,MAAO,sBACrB,CAAE5F,MAAO,IAAK4F,MAAO,yBACrB,CAAE5F,MAAO,IAAK4F,MAAO,iCACrB,CAAE5F,MAAO,IAAK4F,MAAO,8BACrB,CAAE5F,MAAO,IAAK4F,MAAO,iCACrB,CAAE5F,MAAO,IAAK4F,MAAO,cACrB,CAAE5F,MAAO,MAAO4F,MAAO,yBAInB2N,EAAmB,CACvB,CAAEvT,MAAO,IAAK4F,MAAO,eACrB,CAAE5F,MAAO,IAAK4F,MAAO,iBACrB,CAAE5F,MAAO,IAAK4F,MAAO,cACrB,CAAE5F,MAAO,IAAK4F,MAAO,kBACrB,CAAE5F,MAAO,IAAK4F,MAAO,aACrB,CAAE5F,MAAO,MAAO4F,MAAO,yBAInB4N,EAAmB,CACvBtJ,KAAM,CACJpD,MAAOnH,EAAE,OAAQ,QACjB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,UAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,kBAAmB5G,MAAOjG,EAAE,qBAAsB,uBAAwB+T,YAAa,UAC9F,CAAElH,IAAK,kBAAmB5G,MAAOjG,EAAE,sBAAuB,uBAAwB+T,YAAa,UAC/F,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,eAAgB,gBAAiB+T,YAAa,UAC3E,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,gBAAiB,gBAAiB+T,YAAa,YAGhFC,SAAU,CACR7M,MAAOnH,EAAE,WAAY,YACrB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,WAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,WACrE,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,mBAAoB5G,MAAOjG,EAAE,mBAAoB,qBAAsB+T,YAAa,UAC3F,CAAElH,IAAK,mBAAoB5G,MAAOjG,EAAE,mBAAoB,qBAAsB+T,YAAa,YAG/FE,MAAO,CACL9M,MAAOnH,EAAE,QAAS,SAClB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,WAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,WAGzEG,QAAS,CACP/M,MAAOnH,EAAE,UAAW,WACpB8T,UAAW,CACT,CAAEjH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,aAAc5G,MAAOjG,EAAE,aAAc,cAAe+T,YAAa,YAG5EI,MAAO,CACLhN,MAAOnH,EAAE,QAAS,SAClB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,UAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,kBAAmB5G,MAAOjG,EAAE,kBAAmB,oBAAqB+T,YAAa,UACxF,CAAElH,IAAK,iBAAkB5G,MAAOjG,EAAE,iBAAkB,mBAAoB+T,YAAa,YAGzFK,QAAS,CACPjN,MAAOnH,EAAE,UAAW,WACpB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,UAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,SACrE,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,WAGzEM,MAAO,CACLlN,MAAOnH,EAAE,QAAS,SAClB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,UAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,kBAAmB5G,MAAOjG,EAAE,qBAAsB,uBAAwB+T,YAAa,UAC9F,CAAElH,IAAK,kBAAmB5G,MAAOjG,EAAE,sBAAuB,uBAAwB+T,YAAa,UAC/F,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,eAAgB,gBAAiB+T,YAAa,UAC3E,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,gBAAiB,gBAAiB+T,YAAa,YAGhFO,IAAK,CACHnN,MAAOnH,EAAE,MAAO,OAChB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,WAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,mBAAoB5G,MAAOjG,EAAE,mBAAoB,qBAAsB+T,YAAa,UAC3F,CAAElH,IAAK,mBAAoB5G,MAAOjG,EAAE,mBAAoB,qBAAsB+T,YAAa,YAG/FQ,KAAM,CACJpN,MAAOnH,EAAE,OAAQ,QACjB8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,WAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,WAGzES,MAAO,CACLrN,MAAOnH,EAAE,QAAS,SAClB8T,UAAW,CACT,CAAEjH,IAAK,eAAgB5G,MAAOjG,EAAE,eAAgB,gBAAiB+T,YAAa,UAC9E,CAAElH,IAAK,iBAAkB5G,MAAOjG,EAAE,iBAAkB,kBAAmB+T,YAAa,UACpF,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,WAAY5G,MAAOjG,EAAE,WAAY,YAAa+T,YAAa,YAGtEU,cAAe,CACbtN,MAAOnH,EAAE,gBAAiB,kBAC1B8T,UAAW,CACT,CAAEjH,IAAK,UAAW5G,MAAOjG,EAAE,UAAW,WAAY+T,YAAa,UAC/D,CAAElH,IAAK,YAAa5G,MAAOjG,EAAE,YAAa,aAAc+T,YAAa,UACrE,CAAElH,IAAK,iBAAkB5G,MAAOjG,EAAE,iBAAkB,mBAAoB+T,YAAa,UACrF,CAAElH,IAAK,WAAY5G,MAAOjG,EAAE,WAAY,YAAa+T,YAAa,aAgClEW,EAA6BA,CAACC,EAAUC,KAC5CxT,EAAAA,EAAAA,MAAA,OAAoBC,UAAU,uFAAsFC,SAAA,EAClHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAAEsT,EAAMzN,SAC3E5F,EAAAA,EAAAA,KAAA,UACEyL,QAASA,IAhCS2H,KACxB,MAAMC,EAAQf,EAAiBc,GACzBrU,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAAS8T,sBAEzBpT,EAAQqU,KAAWrU,EAAQqU,GAAY,CAAC,GAE7CC,EAAMd,UAAUe,QAAQC,IACjBxU,EAAQqU,GAAUG,EAASjI,OAC9BvM,EAAQqU,GAAUG,EAASjI,KAAO,CAAC,GAIrC,MAAMkI,EAAeD,EAASf,YAAYjR,QAAQ,OAAK,IAEvDxC,EAAQqU,GAAUG,EAASjI,KAAO,CAChCmI,KAAMD,EACNE,KAAMF,EACNG,YAAa,IACbC,YAAa,IACbC,YAAa,IACbC,YAAa,OAIjBxV,EAAe,uBAAwBS,IAQlBgV,CAAiBX,GAChCtT,UAAU,yFAAwFC,SAEjGtB,EAAE,aAAc,qBAIrBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,qEAAoEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,SAAAD,UACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oHAAmHC,SAC9HtB,EAAE,WAAY,eAEjBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,cAAe,mBAEpBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAAC,UAGpIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAAC,UAGpIC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,cAAe,sBAEpBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,cAAe,sBAEpBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,cAAe,sBAEpBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,cAAe,2BAIxBuB,EAAAA,EAAAA,KAAA,SAAAD,SACGsT,EAAMd,UAAUpR,IAAKoS,IAAQ,IAAAS,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAC5B9U,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8FAA6FC,SACxGwT,EAAS7O,SAEZ1E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6GAA4GC,SACvHwT,EAASf,eAEZxS,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLW,IAAI,IACJC,IAAI,MACJ/B,OAA8C,QAAvCkV,EAAA3V,EAAS8T,qBAAqBiB,UAAS,IAAAY,GAAgB,QAAhBC,EAAvCD,EAA0CT,EAASjI,YAAI,IAAA2I,OAAhB,EAAvCA,EAAyDR,OAAQ,GACxEtT,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI8S,EAAQ,KAAA9S,OAAIiT,EAASjI,IAAG,SAASlL,EAAEC,OAAOvB,OAChFgB,UAAU,uKACVS,YAAY,aAGhBP,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLW,IAAI,IACJC,IAAI,MACJ/B,OAA8C,QAAvCoV,EAAA7V,EAAS8T,qBAAqBiB,UAAS,IAAAc,GAAgB,QAAhBC,EAAvCD,EAA0CX,EAASjI,YAAI,IAAA6I,OAAhB,EAAvCA,EAAyDT,OAAQ,GACxEvT,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI8S,EAAQ,KAAA9S,OAAIiT,EAASjI,IAAG,SAASlL,EAAEC,OAAOvB,OAChFgB,UAAU,uKACVS,YAAY,aAGhBP,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,MAAA,UACEf,OAA8C,QAAvCsV,EAAA/V,EAAS8T,qBAAqBiB,UAAS,IAAAgB,GAAgB,QAAhBC,EAAvCD,EAA0Cb,EAASjI,YAAI,IAAA+I,OAAhB,EAAvCA,EAAyDV,cAAe,GAC/ExT,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI8S,EAAQ,KAAA9S,OAAIiT,EAASjI,IAAG,gBAAgBlL,EAAEC,OAAOvB,OACvFgB,UAAU,uKAAsKC,SAAA,EAEhLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,GAAEiB,SAAEtB,EAAE,SAAU,YAC7B2T,EAAiBjR,IAAKwM,IACrB3N,EAAAA,EAAAA,KAAA,UAA0BlB,MAAO6O,EAAM7O,MAAMiB,SAC1C4N,EAAMjJ,OADIiJ,EAAM7O,cAMzBkB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,MAAA,UACEf,OAA8C,QAAvCwV,EAAAjW,EAAS8T,qBAAqBiB,UAAS,IAAAkB,GAAgB,QAAhBC,EAAvCD,EAA0Cf,EAASjI,YAAI,IAAAiJ,OAAhB,EAAvCA,EAAyDX,cAAe,GAC/EzT,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI8S,EAAQ,KAAA9S,OAAIiT,EAASjI,IAAG,gBAAgBlL,EAAEC,OAAOvB,OACvFgB,UAAU,uKAAsKC,SAAA,EAEhLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,GAAEiB,SAAEtB,EAAE,SAAU,YAC7B2T,EAAiBjR,IAAKwM,IACrB3N,EAAAA,EAAAA,KAAA,UAA0BlB,MAAO6O,EAAM7O,MAAMiB,SAC1C4N,EAAMjJ,OADIiJ,EAAM7O,cAMzBkB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,MAAA,UACEf,OAA8C,QAAvC0V,EAAAnW,EAAS8T,qBAAqBiB,UAAS,IAAAoB,GAAgB,QAAhBC,EAAvCD,EAA0CjB,EAASjI,YAAI,IAAAmJ,OAAhB,EAAvCA,EAAyDZ,cAAe,GAC/E1T,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI8S,EAAQ,KAAA9S,OAAIiT,EAASjI,IAAG,gBAAgBlL,EAAEC,OAAOvB,OACvFgB,UAAU,uKAAsKC,SAAA,EAEhLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,GAAEiB,SAAEtB,EAAE,SAAU,YAC7B4T,EAAiBlR,IAAKwM,IACrB3N,EAAAA,EAAAA,KAAA,UAA0BlB,MAAO6O,EAAM7O,MAAMiB,SAC1C4N,EAAMjJ,OADIiJ,EAAM7O,cAMzBkB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,MAAA,UACEf,OAA8C,QAAvC4V,EAAArW,EAAS8T,qBAAqBiB,UAAS,IAAAsB,GAAgB,QAAhBC,EAAvCD,EAA0CnB,EAASjI,YAAI,IAAAqJ,OAAhB,EAAvCA,EAAyDb,cAAe,GAC/E3T,SAAWC,GAAMxB,EAAkB,GAAD0B,OAAI8S,EAAQ,KAAA9S,OAAIiT,EAASjI,IAAG,gBAAgBlL,EAAEC,OAAOvB,OACvFgB,UAAU,uKAAsKC,SAAA,EAEhLC,EAAAA,EAAAA,KAAA,UAAQlB,MAAM,GAAEiB,SAAEtB,EAAE,SAAU,YAC7B4T,EAAiBlR,IAAKwM,IACrB3N,EAAAA,EAAAA,KAAA,UAA0BlB,MAAO6O,EAAM7O,MAAMiB,SAC1C4N,EAAMjJ,OADIiJ,EAAM7O,eA/ElByU,EAASjI,gBA3ClB8H,GAwIZ,OACEvT,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,wCAAyC,gDAE9CuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,8BAA+B,mEAKtCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4DAA2DC,SACtEtB,EAAE,uBAAwB,4BAE7BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAC,UAC1DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEtB,EAAE,sBAAuB,gCAEpFoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAC,UAC1DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEtB,EAAE,uBAAwB,iCAErFoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEtB,EAAE,aAAc,kBAC3EuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEtB,EAAE,sBAAuB,2CAEpFoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SAAEtB,EAAE,aAAc,kBAC3EuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEtB,EAAE,kBAAmB,6CAMpFoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,iBAAkB,sBAEvBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBoL,OAAOC,QAAQkH,GACb3S,OAAO0L,IAAA,IAAEC,GAAID,EAAA,MAAK,CAAC,OAAQ,WAAY,QAAS,UAAW,QAAS,WAAWnM,SAASoM,KACxFnK,IAAIoK,IAAA,IAAE6H,EAAUC,GAAM9H,EAAA,OAAK4H,EAA2BC,EAAUC,WAKvExT,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,sBAAuB,8BAE5BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBoL,OAAOC,QAAQkH,GACb3S,OAAOoM,IAAA,IAAET,GAAIS,EAAA,MAAK,CAAC,QAAS,MAAO,OAAQ,QAAS,iBAAiB7M,SAASoM,KAC9EnK,IAAI6K,IAAA,IAAEoH,EAAUC,GAAMrH,EAAA,OAAKmH,EAA2BC,EAAUC,cCnD7E,EAlUkCjV,IAAsD,IAADwW,EAAA,IAApD,SAAEvW,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EAChF,MAAM,EAAEK,IAAME,EAAAA,EAAAA,KAERC,EAAoBA,CAACC,EAAOC,KAChC,MAAMC,GAAOC,EAAAA,EAAAA,GAAA,GAAQX,EAASwW,uBAG9B,GAAIhW,EAAMK,SAAS,KAAM,CACvB,MAAM6E,EAAQlF,EAAMQ,MAAM,KAC1B,IAAI2E,EAAUjF,EACd,IAAK,IAAIkF,EAAI,EAAGA,EAAIF,EAAMG,OAAS,EAAGD,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMG,OAAS,IAAMpF,CACrC,MACEC,EAAQF,GAASC,EAGnBR,EAAe,wBAAyBS,GAGpCR,EAAOM,IACTL,EAAUc,IAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUM,GAAI,IAAE,CAACT,GAAQ,SAkBrCiW,EAAqB,CACzB,CAAExJ,IAAK,MAAO5G,MAAO,4BACrB,CAAE4G,IAAK,QAAS5G,MAAO,cACvB,CAAE4G,IAAK,MAAO5G,MAAO,yBACrB,CAAE4G,IAAK,QAAS5G,MAAO,wBACvB,CAAE4G,IAAK,QAAS5G,MAAO,wBACvB,CAAE4G,IAAK,QAAS5G,MAAO,wBACvB,CAAE4G,IAAK,QAAS5G,MAAO,uBACvB,CAAE4G,IAAK,SAAU5G,MAAOjG,EAAE,SAAU,YAehCgG,EAAgB,CACpB,CAAE3F,MAAO,SAAU4F,MAAOjG,EAAE,SAAU,WACtC,CAAEK,MAAO,WAAY4F,MAAOjG,EAAE,WAAY,cAItCsW,EAAgB,CACpB,CAAEjW,MAAO,IAAK4F,MAAO,KACrB,CAAE5F,MAAO,IAAK4F,MAAO,KACrB,CAAE5F,MAAO,QAAS4F,MAAOjG,EAAE,QAAS,UACpC,CAAEK,MAAO,OAAQ4F,MAAOjG,EAAE,OAAQ,UAkBpC,OACEoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,4CAA6C,sDAElDuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,sCAAuC,uEAK9CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,qBAAsB,0BAE3BuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,qEAAoEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,SAAAD,UACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oHAAmHC,SAC9HtB,EAAE,OAAQ,WAEbuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,YAAa,cAElBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,cAAe,gBAEpBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,aAAc,cAEnBuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,eAAgB,qBAIzBuB,EAAAA,EAAAA,KAAA,SAAAD,SACG+U,EAAmB3T,IAAK6Q,IAAI,IAAAgD,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAC3B9V,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8FAA6FC,SACxGiS,EAAKtN,SAER1E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,OAAwD,QAAjDkW,EAAA3W,EAASwW,sBAAsBC,0BAAkB,IAAAE,GAAY,QAAZC,EAAjDD,EAAoDhD,EAAK1G,YAAI,IAAA2J,OAAZ,EAAjDA,EAA+DW,SAAU,GAChFzV,SAAWC,GAAMxB,EAAkB,sBAAD0B,OAAuB0R,EAAK1G,IAAG,WAAWlL,EAAEC,OAAOvB,OACrFgB,UAAU,uKACVS,YAAa9B,EAAE,QAAS,cAG5BuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAI,GAAAI,OAAK0R,EAAK1G,IAAG,aACjBxM,MAAM,IACNU,QAAqF,OAA3B,QAAjD0V,EAAA7W,EAASwW,sBAAsBC,0BAAkB,IAAAI,GAAY,QAAZC,EAAjDD,EAAoDlD,EAAK1G,YAAI,IAAA6J,OAAZ,EAAjDA,EAA+DU,UACxE1V,SAAWC,GAAMxB,EAAkB,sBAAD0B,OAAuB0R,EAAK1G,IAAG,aAAalL,EAAEC,OAAOvB,OACvFgB,UAAU,SACV,QAGJD,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAI,GAAAI,OAAK0R,EAAK1G,IAAG,aACjBxM,MAAM,IACNU,QAAqF,OAA3B,QAAjD4V,EAAA/W,EAASwW,sBAAsBC,0BAAkB,IAAAM,GAAY,QAAZC,EAAjDD,EAAoDpD,EAAK1G,YAAI,IAAA+J,OAAZ,EAAjDA,EAA+DQ,UACxE1V,SAAWC,GAAMxB,EAAkB,sBAAD0B,OAAuB0R,EAAK1G,IAAG,aAAalL,EAAEC,OAAOvB,OACvFgB,UAAU,SACV,aAKRE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLnB,OAAwD,QAAjDwW,EAAAjX,EAASwW,sBAAsBC,0BAAkB,IAAAQ,GAAY,QAAZC,EAAjDD,EAAoDtD,EAAK1G,YAAI,IAAAiK,OAAZ,EAAjDA,EAA+DO,SAAU,GAChF3V,SAAWC,GAAMxB,EAAkB,sBAAD0B,OAAuB0R,EAAK1G,IAAG,WAAWlL,EAAEC,OAAOvB,OACrFgB,UAAU,uKACVS,YAAa9B,EAAE,QAAS,cAG5BuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAI,GAAAI,OAAK0R,EAAK1G,IAAG,aACjBxM,MAAM,IACNU,QAAqF,OAA3B,QAAjDgW,EAAAnX,EAASwW,sBAAsBC,0BAAkB,IAAAU,GAAY,QAAZC,EAAjDD,EAAoDxD,EAAK1G,YAAI,IAAAmK,OAAZ,EAAjDA,EAA+DM,UACxE5V,SAAWC,GAAMxB,EAAkB,sBAAD0B,OAAuB0R,EAAK1G,IAAG,aAAalL,EAAEC,OAAOvB,OACvFgB,UAAU,SACV,QAGJD,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAI,GAAAI,OAAK0R,EAAK1G,IAAG,aACjBxM,MAAM,IACNU,QAAqF,OAA3B,QAAjDkW,EAAArX,EAASwW,sBAAsBC,0BAAkB,IAAAY,GAAY,QAAZC,EAAjDD,EAAoD1D,EAAK1G,YAAI,IAAAqK,OAAZ,EAAjDA,EAA+DI,UACxE5V,SAAWC,GAAMxB,EAAkB,sBAAD0B,OAAuB0R,EAAK1G,IAAG,aAAalL,EAAEC,OAAOvB,OACvFgB,UAAU,SACV,cArEDkS,EAAK1G,kBAkFxBzL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,WAAY,eAIjBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtB,EAAE,gBAAiB,qBAEtBuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClC0E,EAActD,IAAKC,IAAM,IAAA4U,EAAA,OACxBnW,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLC,KAAK,eACLpB,MAAOsC,EAAOtC,MACdU,SAAgD,QAAvCwW,EAAA3X,EAASwW,sBAAsBoB,gBAAQ,IAAAD,OAAA,EAAvCA,EAAyC7P,UAAW/E,EAAOtC,MACpEqB,SAAWC,GAAMxB,EAAkB,kBAAmBwB,EAAEC,OAAOvB,OAC/DgB,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEqB,EAAOsD,UATzDtD,EAAOtC,cAgB4B,cAAb,QAAvC8V,EAAAvW,EAASwW,sBAAsBoB,gBAAQ,IAAArB,OAAA,EAAvCA,EAAyCzO,UACxCnG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,qEAAoEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,SAAAD,UACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oHAAmHC,SAC9HtB,EAAE,SAAU,aAEfuB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,QAAS,YAEduB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sHAAqHC,SAChItB,EAAE,OAAQ,gBAIjBuB,EAAAA,EAAAA,KAAA,SAAAD,SA5MQ,CAClB,CAAEuL,IAAK,MAAO5G,MAAO,8BACrB,CAAE4G,IAAK,MAAO5G,MAAO,+BACrB,CAAE4G,IAAK,MAAO5G,MAAO,4BACrB,CAAE4G,IAAK,MAAO5G,MAAO,gCACrB,CAAE4G,IAAK,WAAY5G,MAAO,YAC1B,CAAE4G,IAAK,YAAa5G,MAAO,cAC3B,CAAE4G,IAAK,SAAU5G,MAAO,WAsMCvD,IAAK+U,IAChBrW,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8FAA6FC,SACxGmW,EAAOxR,SAEV1E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCgV,EAAc5T,IAAKC,IAAM,IAAA+U,EAAAC,EAAAC,EAAAC,EAAA,OACxBzW,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAgD,QAAvC2W,EAAA9X,EAASwW,sBAAsBoB,gBAAQ,IAAAE,GAAa,QAAbC,EAAvCD,EAAyCxE,mBAAW,IAAAyE,GAAc,QAAdC,EAApDD,EAAuDF,EAAO5K,YAAI,IAAA+K,GAAO,QAAPC,EAAlED,EAAoExE,aAAK,IAAAyE,OAAlC,EAAvCA,EAA2EpX,SAASkC,EAAOtC,UAAU,EAC9GqB,SAAWC,IAAO,IAADmW,EAAAC,EAAAC,EACf,MAAMC,GAAuD,QAAvCH,EAAAlY,EAASwW,sBAAsBoB,gBAAQ,IAAAM,GAAa,QAAbC,EAAvCD,EAAyC5E,mBAAW,IAAA6E,GAAc,QAAdC,EAApDD,EAAuDN,EAAO5K,YAAI,IAAAmL,OAA3B,EAAvCA,EAAoE5E,QAAS,GACnG,IAAI8E,EAEFA,EADEvW,EAAEC,OAAOb,QACC,IAAIkX,EAAetV,EAAOtC,OAE1B4X,EAAc/W,OAAOC,GAAQA,IAASwB,EAAOtC,OAE3DF,EAAkB,wBAAD0B,OAAyB4V,EAAO5K,IAAG,UAAUqL,IAEhE7W,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAEqB,EAAOsD,UAhBxBtD,EAAOtC,cAqBzBkB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,wDAAuDC,UACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCgV,EAAc5T,IAAKC,IAAM,IAAAwV,EAAAC,EAAAC,EAAAC,EAAA,OACxBlX,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLT,SAAgD,QAAvCoX,EAAAvY,EAASwW,sBAAsBoB,gBAAQ,IAAAW,GAAa,QAAbC,EAAvCD,EAAyCjF,mBAAW,IAAAkF,GAAc,QAAdC,EAApDD,EAAuDX,EAAO5K,YAAI,IAAAwL,GAAM,QAANC,EAAlED,EAAoElF,YAAI,IAAAmF,OAAjC,EAAvCA,EAA0E7X,SAASkC,EAAOtC,UAAU,EAC7GqB,SAAWC,IAAO,IAAD4W,EAAAC,EAAAC,EACf,MAAMR,GAAuD,QAAvCM,EAAA3Y,EAASwW,sBAAsBoB,gBAAQ,IAAAe,GAAa,QAAbC,EAAvCD,EAAyCrF,mBAAW,IAAAsF,GAAc,QAAdC,EAApDD,EAAuDf,EAAO5K,YAAI,IAAA4L,OAA3B,EAAvCA,EAAoEtF,OAAQ,GAClG,IAAI+E,EAEFA,EADEvW,EAAEC,OAAOb,QACC,IAAIkX,EAAetV,EAAOtC,OAE1B4X,EAAc/W,OAAOC,GAAQA,IAASwB,EAAOtC,OAE3DF,EAAkB,wBAAD0B,OAAyB4V,EAAO5K,IAAG,SAASqL,IAE/D7W,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,UAASC,SAAEqB,EAAOsD,UAhBxBtD,EAAOtC,eA/BlBoX,EAAO5K,oBChIlC,EAnIyBlN,IAAsD,IAArD,SAAEC,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EACvE,MAAM,EAAEK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KAErB,OACEkB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,0BAA2B,kCAEhCuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,kCAAmC,2EAI1CuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uDAAsDC,SACjEtB,EAAE,oBAAqB,0BAE1BuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,8BAA+B,2LAO1CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,0CAA2C,iDAEhDuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,iCAAkC,yCAEvCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAKtB,EAAE,cAAe,gBAAgB,iCACtCoB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAKtB,EAAE,cAAe,eAAe,oCACrCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,iBAAkB,sBACzBuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,sBAAuB,4BAC9BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,sBAAuB,4BAC9BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,iBAAkB,4BAI7BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,eAAgB,oBAErBuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,2BAA4B,mCAEjCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,kBAAmB,uBAC1BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,wBAAyB,2CAChCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,wBAAyB,8BAChCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,6BAA8B,mCACrCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,+BAAgC,6CAI3CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,0BAA2B,+BAEhCuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,iCAAkC,yCAEvCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAKtB,EAAE,cAAe,gBAAgB,uBACtCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,0BAA2B,+BAClCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,iBAAkB,sBACzBuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,wBAAyB,oCAIpCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,uBAAwB,4BAE7BuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,iCAAkC,yCAEvCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,sBAAuB,2BAC9BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,iBAAkB,sBACzBuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,iBAAkB,qBACzBuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,kBAAmB,uBAC1BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,gBAAiB,qBACxBuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,oBAAqB,gCAIhCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qGAAoGC,SAAA,EACjHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,qBAAsB,2BAE3BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iDAAgDC,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAEtB,EAAE,gBAAiB,qBACjFuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEtB,EAAE,yBAA0B,mDAGzFuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mDAAkDC,SAAA,EAC/DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAAEtB,EAAE,iBAAkB,sBACpFuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAA4CC,SAAEtB,EAAE,kBAAmB,wCAGpFuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAAEtB,EAAE,aAAc,iBAClFuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SAAEtB,EAAE,mBAAoB,yCAGvFuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAAEtB,EAAE,gBAAiB,oBACrFuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SAAEtB,EAAE,8BAA+B,sDC2B9G,EApJuBL,IAAsD,IAArD,SAAEC,EAAQ,eAAEC,EAAc,OAAEC,EAAM,UAAEC,GAAWJ,EACrE,MAAM,EAAEK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KAErB,OACEkB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEtB,EAAE,kBAAmB,yBAExBuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,0BAA2B,uEAIlCuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,uDAAsDC,SACjEtB,EAAE,oBAAqB,0BAE1BuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,4BAA6B,+IAOxCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,yBAA0B,8BAE/BuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,6BAA8B,yCAEnCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAKtB,EAAE,qBAAsB,uBAAuB,MAAIA,EAAE,OAAQ,YAClEoB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAKtB,EAAE,qBAAsB,uBAAuB,MAAIA,EAAE,OAAQ,YAClEuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,0BAA2B,gCAClCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,sBAAuB,iCAIlCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,eAAgB,oBAErBuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,2BAA4B,uCAEjCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,kBAAmB,uBAC1BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,uBAAwB,4BAC/BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,uBAAwB,gCAC/BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,uBAAwB,mCAInCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,8BAA+B,oCAEpCuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,0BAA2B,qCAEhCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,eAAgB,oBACvBuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,wBAAyB,6BAChCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,kBAAmB,uBAC1BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,qBAAsB,kCAIjCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,2BAA4B,mCAEjCuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CtB,EAAE,0BAA2B,qCAEhCoB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wEAAuEC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,oBAAqB,yBAC5BuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,wBAAyB,6BAChCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,2BAA4B,gCACnCuB,EAAAA,EAAAA,KAAA,MAAAD,SAAKtB,EAAE,yBAA0B,oCAIrCoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qGAAoGC,SAAA,EACjHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtB,EAAE,uBAAwB,4BAE7BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mDAAkDC,SAAA,EAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,iDAAgDC,SAAEtB,EAAE,aAAc,iBAChFuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAA4CC,SAAEtB,EAAE,uBAAwB,mDAGzFuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iDAAgDC,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iDACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+CAA8CC,SAAEtB,EAAE,gBAAiB,qBACjFuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAEtB,EAAE,8BAA+B,gDAG9FuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,cAAaC,UAC1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAAEtB,EAAE,cAAe,mBACnFuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SAAEtB,EAAE,0BAA2B,mDAQpGoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEtB,EAAE,oBAAqB,yBAE1BoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sDAAqDC,SAAC,OACrEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAEtB,EAAE,aAAc,qBAE7EoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wDAAuDC,SAAC,UACvEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAEtB,EAAE,aAAc,qBAE7EoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,SAAC,SACzEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAEtB,EAAE,qBAAsB,6BAErFoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,SAAC,UACzEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SAAEtB,EAAE,YAAa,4BCgoBtF,EAhwB8BL,IAAyF,IAAD+Y,EAAA,IAAvF,UAAEzW,EAAS,YAAE0W,EAAW,mBAAEC,EAAkB,YAAEC,EAAc,CAAC,EAAC,OAAEC,EAAM,SAAEC,GAAUpZ,EAC/G,MAAM,EAAEK,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACf8Y,GAAWC,EAAAA,EAAAA,OACThX,UAAWiX,EAAY,aAAEC,IAAiBC,EAAAA,EAAAA,MAC3CC,EAAaC,IAAkBpP,EAAAA,EAAAA,UAAS,IACxCqP,EAASC,IAActP,EAAAA,EAAAA,WAAS,IAChCpK,EAAQC,IAAamK,EAAAA,EAAAA,UAAS,CAAC,IAC/BuP,EAASC,IAAcxP,EAAAA,EAAAA,UAAS,MAGjCyP,EAAkB1X,GAAaiX,GAG9BtZ,EAAUga,IAAe1P,EAAAA,EAAAA,UAAS,CAEvC1J,YAAa,CACXiB,KAAM,GACNM,IAAK,GACLC,cAAe,GACfC,UAAW,GACXC,YAAa,GACbG,IAAK,GACLC,WAAY,GACZC,YAAY,IAAIsX,MAAOC,cAAclZ,MAAM,KAAK,GAChD4B,MAAM,IAAIqX,MAAOE,eAAeC,MAAM,EAAG,GACzCvX,mBAAoB,GACpBG,cAAe,GACfI,eAAgB,GAChBC,gBAAiB,CACfC,gBAAiB,GACjBC,cAAe,IAEjBC,UAAW,GACXE,UAAW,GACXC,eAAgB,GAChBC,wBAAyB,GACzBC,gBAAiB,CACfC,KAAM,GACNC,cAAe,IAEjBC,gBAAiB,CACfC,YAAY,EACZrC,KAAM,IAERsC,0BAA2B,CACzBC,kBAAkB,EAClBC,SAAU,IAEZC,0BAA2B,CACzBC,iBAAiB,EACjBC,QAAS,IAEXC,iBAAkB,CAChBC,cAAc,EACdF,QAAS,IAEXG,YAAa,GACbC,mBAAoB,GACpBC,YAAa,GACbC,mBAAoB,GACpBC,cAAe,GACfC,UAAW,GACXC,oBAAqB,IAIvBS,iBAAkB,CAChB4U,eAAgB,CACdvS,OAAQ,GACRC,SAAU,IAEZuS,gBAAiB,CACfxS,OAAQ,GACRC,SAAU,IAEZwS,gBAAiB,CACfzS,OAAQ,GACRC,SAAU,IAEZyS,gBAAiB,CACf1S,OAAQ,GACRC,SAAU,IAEZc,gBAAiB,GACjBC,SAAU,GACVC,yBAA0B,GAC1BC,kBAAmB,GACnBC,cAAe,GACfC,eAAgB,GAChBC,WAAY,CACVC,GAAI,GACJC,GAAI,GACJC,GAAI,IAENtD,gBAAiB,CACfY,YAAa,CACXX,WAAY,GACZC,QAAS,IAEXW,UAAW,CACTZ,WAAY,GACZC,QAAS,IAEXY,iBAAkB,CAChBb,WAAY,GACZC,QAAS,IAEXa,cAAe,CACbd,WAAY,GACZC,QAAS,IAEXc,gBAAiB,CACff,WAAY,GACZC,QAAS,IAEXe,WAAY,CACVhB,WAAY,GACZC,QAAS,IAEXgB,eAAgB,CACdjB,WAAY,GACZC,QAAS,IAEXiB,cAAe,CACblB,WAAY,GACZC,QAAS,IAEXkB,aAAc,CACZnB,WAAY,GACZC,QAAS,IAEXmB,UAAW,CACTpB,WAAY,GACZC,QAAS,KAGbuD,oBAAqB,CACnBC,WAAY,GACZ3B,SAAU,KAKd2G,oBAAqB,CACnB+L,iBAAkB,CAChB3S,OAAQ,GACRC,SAAU,IAEZ2S,YAAa,CACX5S,OAAQ,GACRC,SAAU,IAEZsH,MAAO,CACLvH,OAAQ,GACRwH,MAAO,GACPvH,SAAU,IAEZ8H,oBAAqB,GACrBC,cAAe,CACbC,UAAU,EACVG,MAAO,GACPnI,SAAU,IAEZ4S,gBAAiB,CACfC,iBAAiB,EACjBC,UAAW,GACXC,SAAU,GACVC,eAAgB,GAChBC,mBAAoB,GACpBC,WAAY,IAEdC,YAAa,CACXpT,OAAQ,GACRqT,QAAS,GACTC,oBAAqB,GACrBC,gBAAiB,GACjBC,SAAU,GACVC,WAAY,GACZC,aAAc,IAEhBC,mBAAoB,CAClBC,SAAU,GACVC,aAAc,GACdC,SAAU,GACVC,UAAU,EACVC,qBAAsB,IAExBC,eAAgB,CACdrS,WAAY,GACZsS,eAAgB,GAChBjU,SAAU,GACVkU,eAAgB,GAChBC,cAAe,IAEjB5L,eAAgB,CACdC,SAAS,EACTC,UAAW,GACXC,MAAO,GACP7O,KAAM,GACNkZ,SAAU,GACV/J,MAAO,GACPG,YAAa,GACbD,UAAW,GACXU,mBAAoB,GACpBM,iBAAkB,GAClBE,qBAAsB,GACtBgK,QAAS,GACTC,mBAAoB,KAKxB7J,iBAAkB,CAChB8J,iBAAkB,CAChBvU,OAAQ,GACRwL,YAAa,CACXgJ,KAAM,CAAE/I,KAAM,GAAIC,MAAO,IACzB+I,WAAY,CAAEhJ,KAAM,GAAIC,MAAO,IAC/BgJ,UAAW,CAAEjJ,KAAM,GAAIC,MAAO,IAC9BiJ,YAAa,CAAElJ,KAAM,GAAIC,MAAO,IAChCkJ,aAAc,CAAEnJ,KAAM,GAAIC,MAAO,IACjCmJ,eAAgB,CAAEpJ,KAAM,GAAIC,MAAO,MAGvCoJ,iBAAkB,CAChB9U,OAAQ,GACRwL,YAAa,CACXgJ,KAAM,CAAE/I,KAAM,GAAIC,MAAO,IACzB+I,WAAY,CAAEhJ,KAAM,GAAIC,MAAO,IAC/BgJ,UAAW,CAAEjJ,KAAM,GAAIC,MAAO,IAC9BiJ,YAAa,CAAElJ,KAAM,GAAIC,MAAO,IAChCkJ,aAAc,CAAEnJ,KAAM,GAAIC,MAAO,IACjCmJ,eAAgB,CAAEpJ,KAAM,GAAIC,MAAO,MAGvCE,8BAA+B,CAC7B5L,OAAQ,GACRwL,YAAa,CACXuJ,WAAY,GACZC,UAAW,GACXC,aAAc,GACdC,WAAY,GACZC,0BAA2B,GAC3BC,mBAAoB,IAEtBnV,SAAU,KAKd+L,qBAAsB,CACpBnJ,KAAM,CACJwS,QAAS,CAAE/H,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IAC/F2H,UAAW,CAAEhI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACjG4H,gBAAiB,CAAEjI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACvG6H,gBAAiB,CAAElI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACvG8H,UAAW,CAAEnI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACjG+H,UAAW,CAAEpI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,KAEnGrB,SAAU,CACR+I,QAAS,CAAE/H,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IAC/F2H,UAAW,CAAEhI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACjGgI,UAAW,CAAErI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACjGiI,UAAW,CAAEtI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACjGkI,iBAAkB,CAAEvI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,IACxGmI,iBAAkB,CAAExI,KAAM,GAAIC,KAAM,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,GAAIC,YAAa,MAM5Ge,sBAAuB,CACrBC,mBAAoB,CAClBoH,IAAK,CAAEtG,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,IACvDoG,MAAO,CAAEvG,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,IACzDqG,IAAK,CAAExG,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,IACvDsG,MAAO,CAAEzG,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,IACzDuG,MAAO,CAAE1G,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,IACzDwG,MAAO,CAAE3G,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,IACzDyG,MAAO,CAAE5G,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,IACzD0G,OAAQ,CAAE7G,OAAQ,GAAIC,SAAU,GAAIC,OAAQ,GAAIC,SAAU,KAE5DE,SAAU,CACR9P,OAAQ,GACRwL,YAAa,CACX+K,IAAK,CAAE7K,MAAO,GAAID,KAAM,IACxB+K,IAAK,CAAE9K,MAAO,GAAID,KAAM,IACxBgL,IAAK,CAAE/K,MAAO,GAAID,KAAM,IACxBiL,IAAK,CAAEhL,MAAO,GAAID,KAAM,IACxBkL,SAAU,CAAEjL,MAAO,GAAID,KAAM,IAC7BmL,UAAW,CAAElL,MAAO,GAAID,KAAM,IAC9BoL,OAAQ,CAAEnL,MAAO,GAAID,KAAM,MAG/BqL,mBAAoB,CAClB9W,OAAQ,GACR1D,SAAU,GACV2D,SAAU,IAEZ8W,mBAAoB,CAClB/W,OAAQ,GACRC,SAAU,IAEZ+W,kBAAmB,CACjBC,MAAO,GACPjX,OAAQ,GACRkX,MAAO,GACPC,cAAe,GACflX,SAAU,GACVe,SAAU,IAEZoW,aAAc,CACZC,WAAY,CACVrX,OAAQ,GACRsX,MAAO,GACPrX,SAAU,IAEZsX,WAAY,CACVvX,OAAQ,GACRsX,MAAO,GACPrX,SAAU,KAGduX,aAAc,CACZC,SAAU,GACVC,cAAe,GACfR,MAAO,GACPS,kBAAmB,CACjBC,OAAQ,GACRC,QAAS,GACTC,MAAO,GACPC,QAAS,KAGbC,gBAAiB,CACfC,IAAK,CAAEf,MAAO,IACdgB,SAAU,CAAEhB,MAAO,IACnBiB,KAAM,CAAEjB,MAAO,IACfkB,KAAM,CAAElB,MAAO,IACfmB,WAAY,CAAEnB,MAAO,IACrBZ,OAAQ,CAAEvc,KAAM,GAAImd,MAAO,MAK/BoB,aAAc,CACZC,qBAAsB,CACpBC,YAAa,GACbC,YAAa,GACbC,YAAa,GACbC,MAAO,GACPC,oBAAqB,GACrBC,oBAAqB,GACrBC,eAAgB,GAChBC,wBAAyB,GACzBC,eAAgB,GAChBC,uBAAuB,GAEzBC,IAAK,CACHC,gBAAiB,GACjBC,sBAAuB,GACvBC,sBAAuB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IAC5DC,2BAA4B,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,IACjEC,6BAA8B,CAC5BC,oBAAqB,CAAEC,QAAS,GAAIC,YAAa,IACjDC,eAAgB,CAAEF,QAAS,GAAIC,YAAa,IAC5CE,eAAgB,CAAEH,QAAS,GAAIC,YAAa,IAC5CG,gBAAiB,CAAEJ,QAAS,GAAIC,YAAa,IAC7Cje,cAAe,CAAEge,QAAS,GAAIC,YAAa,IAC3CI,kBAAmB,CAAEL,QAAS,GAAIC,YAAa,IAC/CK,gBAAiB,CAAEN,QAAS,GAAIC,YAAa,OAMnDM,WAAY,CACVC,mBAAoB,GACpBC,cAAe,GACfC,mBAAoB,GACpBC,cAAe,GACfC,aAAc,GACdC,gBAAiB,CAAC,GAAI,GAAI,IAC1BC,yBAA0B,OAK9BC,EAAAA,EAAAA,WAAU,KACR,GAAItJ,GAAsBD,EAAa,CAAC,IAADwJ,EAAAC,EAAAC,EAErC3I,EAAWf,GAGX,MAAM2J,GAAe/hB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChBX,GAAQ,IACXY,aAAWD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACNX,EAASY,aAAW,IACvBiB,KAAMkX,EAAYlX,MAAI,GAAAI,OAAO8W,EAAY4J,UAAS,KAAA1gB,OAAI8W,EAAY6J,UAClEzgB,IAA4B,SAAvB4W,EAAY8J,OAAoB,IAA6B,WAAvB9J,EAAY8J,OAAsB,IAAM,GACnFxgB,UAAW0W,EAAY+J,YAAc,GACrCxgB,YAAayW,EAAYzW,aAAe,GACxCG,KAAoB,QAAf8f,EAAAxJ,EAAYtW,WAAG,IAAA8f,OAAA,EAAfA,EAAiBQ,aAAc,GACpCrgB,WAAYqW,EAAYiK,KAAOjK,EAAYkK,IAAM,GACjDzf,WAAqC,QAA1Bgf,EAAAzJ,EAAYmK,sBAAc,IAAAV,OAAA,EAA1BA,EAA4BW,mBAAoB,GAC3Dxf,gBAA0C,QAA1B8e,EAAA1J,EAAYmK,sBAAc,IAAAT,OAAA,EAA1BA,EAA4BW,QAAS,OAIzDpJ,EAAY0I,GACZ9I,GAAW,EACb,MAAWG,IAETH,GAAW,GACXyJ,WAAW,KACT,MAAMC,EAAc,CAClBL,GAAIlJ,EACJlY,KAAM,uEACN0hB,OAAQ,qBACRC,YAAa,aACb/gB,IAAK,EACLogB,OAAQ,OACRC,WAAY,aACZW,MAAO,mBACPnhB,YAAa,QACbohB,QAAS,qLAGX5J,EAAWwJ,GAGXtJ,EAAY2J,IAAQhjB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfgjB,GAAQ,IACX/iB,aAAWD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACNgjB,EAAS/iB,aAAW,IACvBiB,KAAMyhB,EAAYC,OAClBlhB,UAAWihB,EAAYL,GACvBxgB,IAAK6gB,EAAY7gB,IAAIsgB,WACrB5gB,IAAKmhB,EAAYT,OACjBvgB,YAAaghB,EAAYhhB,iBAI7BsX,GAAW,IACV,OAEJ,CAACG,EAAiBhB,EAAaC,KAGlCsJ,EAAAA,EAAAA,WAAU,KACJrJ,GAAenM,OAAO8W,KAAK3K,GAAapT,OAAS,GACnDmU,EAAY2J,IAAQhjB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfgjB,GACA1K,KAGN,CAACA,IAGJ,MAAM4K,EAAQ,CACZ,CAAEZ,GAAI,EAAG1b,MAAOnH,EAAE,qBAAsB,uBAAwB0jB,UAAWC,GAC3E,CAAEd,GAAI,EAAG1b,MAAOnH,EAAE,mBAAoB,kCAAmC0jB,UAAWE,GACpF,CAAEf,GAAI,EAAG1b,MAAOnH,EAAE,sBAAuB,sCAAuC0jB,UAAWG,GAC3F,CAAEhB,GAAI,EAAG1b,MAAOnH,EAAE,mBAAoB,oCAAqC0jB,UAAWI,GACtF,CAAEjB,GAAI,EAAG1b,MAAOnH,EAAE,uBAAwB,6CAA8C0jB,UAAWK,GACnG,CAAElB,GAAI,EAAG1b,MAAOnH,EAAE,wBAAyB,mDAAoD0jB,UAAWM,GAC1G,CAAEnB,GAAI,EAAG1b,MAAOnH,EAAE,eAAgB,+BAAgC0jB,UAAWO,GAC7E,CAAEpB,GAAI,EAAG1b,MAAOnH,EAAE,aAAc,sBAAuB0jB,UAAWQ,IAI9DC,EAAsBA,KAC1B,MAAMC,EAAY,CAAC,EAEnB,GACO,IADC/K,EAECzZ,EAASY,YAAYiB,OAAM2iB,EAAU3iB,KAAOzB,EAAE,eAAgB,qBAC9DJ,EAASY,YAAYuB,MAAKqiB,EAAUriB,IAAM/B,EAAE,cAAe,oBAC3DJ,EAASY,YAAYyB,YAAWmiB,EAAUniB,UAAYjC,EAAE,oBAAqB,2BAC7EJ,EAASY,YAAY0B,cAAakiB,EAAUliB,YAAclC,EAAE,sBAAuB,8BACnFJ,EAASY,YAAY6B,KAAOzC,EAASY,YAAY6B,IAAM,MAAI+hB,EAAU/hB,IAAMrC,EAAE,cAAe,4BAC5FJ,EAASY,YAAY4C,YAAWghB,EAAUhhB,UAAYpD,EAAE,oBAAqB,0BAC7EJ,EAASY,YAAY+C,iBAAgB6gB,EAAU7gB,eAAiBvD,EAAE,yBAA0B,gCAWrG,OADAD,EAAUqkB,GAC+B,IAAlC1X,OAAO8W,KAAKY,GAAW3e,QAoC1B4e,EAAaC,UACjB,GAAKH,IAAL,CAKA3K,GAAW,GACX,IACE,MAAM+K,GAAchkB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfX,GAAQ,IACXqC,YACAuiB,WAAW,IAAI3K,MAAOC,cACtB2K,WAAW,IAAI5K,MAAOC,gBAGpBhB,SACIA,EAAOyL,GAGfG,EAAAA,GAAMC,QAAQ3kB,EAAE,kBAAmB,kCAG/B+Y,EACFA,IAEAC,EAAS,YAEb,CAAE,MAAO4L,GACPC,QAAQD,MAAM,2BAA4BA,GAC1CF,EAAAA,GAAME,MAAM5kB,EAAE,cAAe,2BAC/B,CAAC,QACCwZ,GAAW,EACb,CA5BA,MAFEkL,EAAAA,GAAME,MAAM5kB,EAAE,kBAAmB,yCAuC/B8kB,EAA6C,QAAzBpM,EAAG+K,EAAMpK,EAAc,UAAE,IAAAX,OAAA,EAAtBA,EAAwBgL,UAErD,OACEniB,EAAAA,EAAAA,KAAA,OAAKF,UAAS,4CAAAQ,OAA8C5B,EAAQ,cAAgB,gBAAiBqB,UACnGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7DtB,EAAE,2BAA4B,oCAC9B2Z,GAAmBF,IAClBrY,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4DAA2DC,SAAA,CAAC,KACvEmY,EAAQ0J,QAAU1J,EAAQhY,YAInCF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtB,EAAE,yCAA0C,gDAI/CoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qFAAoFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAC,uBAEzEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yDAAwDC,SAAC,wBAE3EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SAAC,qBAE7EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SAAC,yBAIjFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,CAC5BqY,IACCvY,EAAAA,EAAAA,MAAA,UACE4L,QAASA,IAAMgM,EAAS,aAADnX,OAAc8X,IACrCtY,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,cAAe,oBAGtBoB,EAAAA,EAAAA,MAAA,UACE4L,QAzDU+X,KAEtBL,EAAAA,GAAMM,KAAKhlB,EAAE,0BAA2B,kDAwD5BqB,UAAU,oFAAmFC,SAAA,EAE7FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZrB,EAAE,YAAa,kBAElBuB,EAAAA,EAAAA,KAAA,UACEyL,QAAS+L,GAAQ,KAAWC,EAASW,EAAe,aAAA9X,OAAgB8X,GAAoB,cACxFtY,UAAU,uFAAsFC,SAE/FtB,EAAE,SAAU,qBAOrBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uDAAsDC,SAAA,CACnEtB,EAAE,WAAY,YAAY,KAAGqZ,EAAY,MAAIoK,EAAMhe,WAEtDrE,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CC,SAAA,CACvD2jB,KAAKC,MAAO7L,EAAcoK,EAAMhe,OAAU,KAAK,KAAGzF,EAAE,WAAY,mBAGrEuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAU,2DACV8jB,MAAO,CAAEhZ,MAAM,GAADtK,OAAMwX,EAAcoK,EAAMhe,OAAU,IAAG,cAM3DlE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,SACzCmiB,EAAM/gB,IAAK0iB,IACV7jB,EAAAA,EAAAA,KAAA,UAEEyL,QAASA,KAAMqY,OAtJNC,EAsJsBF,EAAKvC,QArJlDvJ,EAAegM,GADQA,OAuJTjkB,UAAS,gFAAAQ,OACPwX,IAAgB+L,EAAKvC,GACjB,mDACA,0HACHvhB,UAEHF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,8BAA6BC,SAAA,EAC3CC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,iEAAAQ,OACbwX,IAAgB+L,EAAKvC,GACjB,yBACAxJ,EAAc+L,EAAKvC,GACnB,0BACA,6BACHvhB,SACA+X,EAAc+L,EAAKvC,GAAK,SAAMuC,EAAKvC,MAEtCthB,EAAAA,EAAAA,KAAA,QAAAD,SAAO8jB,EAAKje,YAlBTie,EAAKvC,YA2BpBthB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8CAA6CC,SACzDwjB,IACCvjB,EAAAA,EAAAA,KAACujB,EAAoB,CACnBllB,SAAUA,EACVC,eA/KWA,CAAC0lB,EAASC,KAC/B5L,EAAY2J,IAAQhjB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfgjB,GAAQ,IACX,CAACgC,IAAOhlB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHgjB,EAASgC,IACTC,OA2KG1lB,OAAQA,EACRC,UAAWA,OAMjBqB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,UACE4L,QApMiByY,KACrBpM,EAAc,GAChBC,EAAeD,EAAc,IAmMvBtW,SAA0B,IAAhBsW,EACVhY,UAAS,0CAAAQ,OACS,IAAhBwX,EACI,+CACA,4CACH/X,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZrB,EAAE,WAAY,gBAGjBoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEyL,QAASqX,EACTthB,SAAUwW,EACVlY,UAAU,wGAAuGC,SAEhHiY,GACCnY,EAAAA,EAAAA,MAAAskB,EAAAA,SAAA,CAAApkB,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZrB,EAAE,SAAU,iBAGfoB,EAAAA,EAAAA,MAAAskB,EAAAA,SAAA,CAAApkB,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrB,EAAE,OAAQ,aAKhBqZ,EAAcoK,EAAMhe,QACnBrE,EAAAA,EAAAA,MAAA,UACE4L,QA/OS2Y,KACjBxB,IACE9K,EAAcoK,EAAMhe,QACtB6T,EAAeD,EAAc,GAG/BqL,EAAAA,GAAME,MAAM5kB,EAAE,kBAAmB,6CA0OvBqB,UAAU,kFAAiFC,SAAA,CAE1FtB,EAAE,OAAQ,SACXuB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCAGfE,EAAAA,EAAAA,KAAA,UACEyL,QAASqX,EACTthB,SAAUwW,EACVlY,UAAU,sGAAqGC,SAE9GiY,GACCnY,EAAAA,EAAAA,MAAAskB,EAAAA,SAAA,CAAApkB,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZrB,EAAE,aAAc,qBAGnBoB,EAAAA,EAAAA,MAAAskB,EAAAA,SAAA,CAAApkB,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZrB,EAAE,WAAY,uC", "sources": ["components/Assessment/Pages/PatientInformationPage.jsx", "components/Assessment/Pages/AssessmentReviewPage.jsx", "components/Assessment/BodyMap.jsx", "components/Assessment/Pages/MusculoskeletalExamPage.jsx", "components/Assessment/Pages/SensoryFunctionsPage.jsx", "components/Assessment/Pages/NeuromusculoskeletalPage.jsx", "components/Assessment/Pages/NeurodynamicsReflexesPage.jsx", "components/Assessment/Pages/EquipmentICFPage.jsx", "components/Assessment/Pages/SignaturesPage.jsx", "components/Assessment/PTAdultAssessmentForm.jsx"], "sourcesContent": ["import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\n\nconst PatientInformationPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t, isRTL } = useLanguage();\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData.patientInfo };\n    \n    // Handle nested fields\n    if (field.includes('.')) {\n      const [parent, child] = field.split('.');\n      newData[parent] = { ...newData[parent], [child]: value };\n    } else {\n      newData[field] = value;\n    }\n\n    updateFormData('patientInfo', newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleArrayChange = (field, value, checked) => {\n    const currentArray = formData.patientInfo[field] || [];\n    let newArray;\n\n    if (checked) {\n      newArray = [...currentArray, value];\n    } else {\n      newArray = currentArray.filter(item => item !== value);\n    }\n\n    handleInputChange(field, newArray);\n  };\n\n  const jobOccupationOptions = [\n    'Military', 'Teacher', 'Homemaker', 'Engineer-Technician', 'Field work',\n    'Farmers/fisherman', 'Office workers', 'Retired', 'Unemployed & not active', 'Student'\n  ];\n\n  const educationLevelOptions = [\n    'Illiterate', 'Literate', 'Literate without formal education',\n    'Complete education', 'Incomplete education'\n  ];\n\n  const residenceTypeOptions = [\n    'Apartment', 'Detached House', 'Nursing Home'\n  ];\n\n  const socialSupportOptions = [\n    'Family assistance', 'Living alone', 'Limited Support'\n  ];\n\n  const pastMedicalHistoryOptions = [\n    'Diabetes', 'Hypertension', 'Heart Conditions', 'Asthma',\n    'Kidney Diseases', 'Cancer', 'No known past medical history', 'None'\n  ];\n\n  const sourceOfInformationOptions = [\n    'Patient', 'Relative'\n  ];\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('patientInformation', 'Patient Information')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('basicPatientDetails', 'Basic patient details and demographics')}\n        </p>\n      </div>\n\n      {/* Basic Information */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"text\"\n            value={formData.patientInfo.name}\n            onChange={(e) => handleInputChange('name', e.target.value)}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.name ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder={t('enterPatientName', 'Enter patient name')}\n          />\n          {errors.name && <p className=\"text-red-500 text-sm mt-1\">{errors.name}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('sex', 'Sex')} <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"sex\"\n                value=\"M\"\n                checked={formData.patientInfo.sex === 'M'}\n                onChange={(e) => handleInputChange('sex', e.target.value)}\n                className=\"mr-2\"\n              />\n              {t('male', 'Male')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"sex\"\n                value=\"F\"\n                checked={formData.patientInfo.sex === 'F'}\n                onChange={(e) => handleInputChange('sex', e.target.value)}\n                className=\"mr-2\"\n              />\n              {t('female', 'Female')}\n            </label>\n          </div>\n          {errors.sex && <p className=\"text-red-500 text-sm mt-1\">{errors.sex}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('maritalStatus', 'Marital Status')}\n          </label>\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"maritalStatus\"\n                value=\"Single\"\n                checked={formData.patientInfo.maritalStatus === 'Single'}\n                onChange={(e) => handleInputChange('maritalStatus', e.target.value)}\n                className=\"mr-2\"\n              />\n              {t('single', 'Single')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"maritalStatus\"\n                value=\"Married\"\n                checked={formData.patientInfo.maritalStatus === 'Married'}\n                onChange={(e) => handleInputChange('maritalStatus', e.target.value)}\n                className=\"mr-2\"\n              />\n              {t('married', 'Married')}\n            </label>\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('patientId', 'Patient I.D No')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"text\"\n            value={formData.patientInfo.patientId}\n            onChange={(e) => handleInputChange('patientId', e.target.value)}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.patientId ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder={t('enterPatientId', 'Enter patient ID')}\n          />\n          {errors.patientId && <p className=\"text-red-500 text-sm mt-1\">{errors.patientId}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('nationality', 'Nationality')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"text\"\n            value={formData.patientInfo.nationality}\n            onChange={(e) => handleInputChange('nationality', e.target.value)}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.nationality ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder={t('enterNationality', 'Enter nationality')}\n          />\n          {errors.nationality && <p className=\"text-red-500 text-sm mt-1\">{errors.nationality}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('age', 'Age')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"number\"\n            min=\"18\"\n            max=\"120\"\n            value={formData.patientInfo.age}\n            onChange={(e) => handleInputChange('age', e.target.value)}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.age ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder={t('enterAge', 'Enter age')}\n          />\n          {errors.age && <p className=\"text-red-500 text-sm mt-1\">{errors.age}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('fileNumber', 'File Number')}\n          </label>\n          <input\n            type=\"text\"\n            value={formData.patientInfo.fileNumber}\n            onChange={(e) => handleInputChange('fileNumber', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterFileNumber', 'Enter file number')}\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('dateOfEvaluation', 'Date of Evaluation')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"date\"\n            value={formData.patientInfo.dateOfEval}\n            onChange={(e) => handleInputChange('dateOfEval', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('time', 'Time')}\n          </label>\n          <input\n            type=\"time\"\n            value={formData.patientInfo.time}\n            onChange={(e) => handleInputChange('time', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('referredByHospital', 'Referred by Hospital')}\n          </label>\n          <input\n            type=\"text\"\n            value={formData.patientInfo.referredByHospital}\n            onChange={(e) => handleInputChange('referredByHospital', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterReferringHospital', 'Enter referring hospital')}\n          />\n        </div>\n      </div>\n\n      {/* Job & Occupation */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('jobOccupation', 'Job & Occupation')}\n        </label>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\n          {jobOccupationOptions.map((option) => (\n            <label key={option} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.patientInfo.jobOccupation.includes(option)}\n                onChange={(e) => handleArrayChange('jobOccupation', option, e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">{t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}</span>\n            </label>\n          ))}\n          <div className=\"col-span-full\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.patientInfo.jobOccupation.includes('Other')}\n                onChange={(e) => handleArrayChange('jobOccupation', 'Other', e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300 mr-2\">{t('other', 'Other')}:</span>\n              <input\n                type=\"text\"\n                placeholder={t('specify', 'Specify')}\n                className=\"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                disabled={!formData.patientInfo.jobOccupation.includes('Other')}\n              />\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* Education Level */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('educationLevel', 'Education Level')}\n        </label>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n          {educationLevelOptions.map((option) => (\n            <label key={option} className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"educationLevel\"\n                value={option}\n                checked={formData.patientInfo.educationLevel === option}\n                onChange={(e) => handleInputChange('educationLevel', e.target.value)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">{t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}</span>\n            </label>\n          ))}\n          <div className=\"col-span-full\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"educationLevel\"\n                value=\"Other\"\n                checked={formData.patientInfo.educationLevel === 'Other'}\n                onChange={(e) => handleInputChange('educationLevel', e.target.value)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300 mr-2\">{t('other', 'Other')}:</span>\n              <input\n                type=\"text\"\n                placeholder={t('specify', 'Specify')}\n                className=\"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                disabled={formData.patientInfo.educationLevel !== 'Other'}\n              />\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* Living Condition */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('typeOfResidence', 'Type of Residence')}\n          </label>\n          <div className=\"space-y-2\">\n            {residenceTypeOptions.map((option) => (\n              <label key={option} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"typeOfResidence\"\n                  value={option}\n                  checked={formData.patientInfo.livingCondition.typeOfResidence === option}\n                  onChange={(e) => handleInputChange('livingCondition.typeOfResidence', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('socialSupport', 'Social Support')}\n          </label>\n          <div className=\"space-y-2\">\n            {socialSupportOptions.map((option) => (\n              <label key={option} className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.patientInfo.livingCondition.socialSupport.includes(option)}\n                  onChange={(e) => {\n                    const currentArray = formData.patientInfo.livingCondition.socialSupport || [];\n                    let newArray;\n                    if (e.target.checked) {\n                      newArray = [...currentArray, option];\n                    } else {\n                      newArray = currentArray.filter(item => item !== option);\n                    }\n                    handleInputChange('livingCondition.socialSupport', newArray);\n                  }}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Medical Information */}\n      <div className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('diagnosis', 'Diagnosis')} <span className=\"text-red-500\">*</span>\n          </label>\n          <textarea\n            value={formData.patientInfo.diagnosis}\n            onChange={(e) => handleInputChange('diagnosis', e.target.value)}\n            rows={3}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.diagnosis ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder={t('enterDiagnosis', 'Enter diagnosis')}\n          />\n          {errors.diagnosis && <p className=\"text-red-500 text-sm mt-1\">{errors.diagnosis}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('icd10Code', 'ICD 10 CODE')}\n          </label>\n          <input\n            type=\"text\"\n            value={formData.patientInfo.icd10Code}\n            onChange={(e) => handleInputChange('icd10Code', e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterIcd10Code', 'Enter ICD 10 code')}\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('chiefComplaint', 'Chief Complaint')} <span className=\"text-red-500\">*</span>\n          </label>\n          <textarea\n            value={formData.patientInfo.chiefComplaint}\n            onChange={(e) => handleInputChange('chiefComplaint', e.target.value)}\n            rows={3}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n              errors.chiefComplaint ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder={t('enterChiefComplaint', 'Enter chief complaint')}\n          />\n          {errors.chiefComplaint && <p className=\"text-red-500 text-sm mt-1\">{errors.chiefComplaint}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('associatedMedicalIssues', 'Associated Medical Issues')}\n          </label>\n          <textarea\n            value={formData.patientInfo.associatedMedicalIssues}\n            onChange={(e) => handleInputChange('associatedMedicalIssues', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterAssociatedMedicalIssues', 'Enter associated medical issues')}\n          />\n        </div>\n      </div>\n\n      {/* History of Trauma/Illness */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('historyOfTraumaIllness', 'History of Trauma/Illness')}\n        </label>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('date', 'Date')}\n            </label>\n            <input\n              type=\"date\"\n              value={formData.patientInfo.historyOfTrauma.date}\n              onChange={(e) => handleInputChange('historyOfTrauma.date', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('circumstancesEtiology', 'Circumstances/Etiology')}\n            </label>\n            <textarea\n              value={formData.patientInfo.historyOfTrauma.circumstances}\n              onChange={(e) => handleInputChange('historyOfTrauma.circumstances', e.target.value)}\n              rows={2}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterCircumstances', 'Enter circumstances')}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Previous Surgery */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('previousSurgery', 'Previous Surgery')}\n        </label>\n        <div className=\"space-y-3\">\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"previousSurgery\"\n                value=\"Yes\"\n                checked={formData.patientInfo.previousSurgery.hasSurgery}\n                onChange={(e) => handleInputChange('previousSurgery.hasSurgery', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('yes', 'Yes')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"previousSurgery\"\n                value=\"No\"\n                checked={!formData.patientInfo.previousSurgery.hasSurgery}\n                onChange={(e) => handleInputChange('previousSurgery.hasSurgery', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('no', 'No')}\n            </label>\n          </div>\n          {formData.patientInfo.previousSurgery.hasSurgery && (\n            <div>\n              <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                {t('typeOfSurgery', 'Type of Surgery')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.patientInfo.previousSurgery.type}\n                onChange={(e) => handleInputChange('previousSurgery.type', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterTypeOfSurgery', 'Enter type of surgery')}\n              />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Radiological Investigation */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('radiologicalInvestigationFindings', 'Radiological Investigation/Findings')}\n        </label>\n        <div className=\"space-y-3\">\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"radiologicalInvestigation\"\n                value=\"Yes\"\n                checked={formData.patientInfo.radiologicalInvestigation.hasInvestigation}\n                onChange={(e) => handleInputChange('radiologicalInvestigation.hasInvestigation', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('yes', 'Yes')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"radiologicalInvestigation\"\n                value=\"No\"\n                checked={!formData.patientInfo.radiologicalInvestigation.hasInvestigation}\n                onChange={(e) => handleInputChange('radiologicalInvestigation.hasInvestigation', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('no', 'No')}\n            </label>\n          </div>\n          {formData.patientInfo.radiologicalInvestigation.hasInvestigation && (\n            <div>\n              <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                {t('findings', 'Findings')}\n              </label>\n              <textarea\n                value={formData.patientInfo.radiologicalInvestigation.findings}\n                onChange={(e) => handleInputChange('radiologicalInvestigation.findings', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterFindings', 'Enter findings')}\n              />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Previous Rehab Intervention */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('previousRehabIntervention', 'Previous Rehab Intervention')}\n        </label>\n        <div className=\"space-y-3\">\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"previousRehabIntervention\"\n                value=\"Yes\"\n                checked={formData.patientInfo.previousRehabIntervention.hasIntervention}\n                onChange={(e) => handleInputChange('previousRehabIntervention.hasIntervention', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('yes', 'Yes')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"previousRehabIntervention\"\n                value=\"No\"\n                checked={!formData.patientInfo.previousRehabIntervention.hasIntervention}\n                onChange={(e) => handleInputChange('previousRehabIntervention.hasIntervention', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('no', 'No')}\n            </label>\n          </div>\n          {formData.patientInfo.previousRehabIntervention.hasIntervention && (\n            <div>\n              <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                {t('details', 'Details')}\n              </label>\n              <textarea\n                value={formData.patientInfo.previousRehabIntervention.details}\n                onChange={(e) => handleInputChange('previousRehabIntervention.details', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterDetails', 'Enter details')}\n              />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Current Equipment/Assistive Technology */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('currentEquipmentAssistiveTechnology', 'Current Equipment/Assistive Technology')}\n        </label>\n        <div className=\"space-y-3\">\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"currentEquipment\"\n                value=\"Yes\"\n                checked={formData.patientInfo.currentEquipment.hasEquipment}\n                onChange={(e) => handleInputChange('currentEquipment.hasEquipment', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('yes', 'Yes')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"currentEquipment\"\n                value=\"No\"\n                checked={!formData.patientInfo.currentEquipment.hasEquipment}\n                onChange={(e) => handleInputChange('currentEquipment.hasEquipment', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('no', 'No')}\n            </label>\n          </div>\n          {formData.patientInfo.currentEquipment.hasEquipment && (\n            <div>\n              <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                {t('details', 'Details')}\n              </label>\n              <textarea\n                value={formData.patientInfo.currentEquipment.details}\n                onChange={(e) => handleInputChange('currentEquipment.details', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterDetails', 'Enter details')}\n              />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Precautions */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          {t('precautions', 'Precautions')}\n        </label>\n        <textarea\n          value={formData.patientInfo.precautions}\n          onChange={(e) => handleInputChange('precautions', e.target.value)}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n          placeholder={t('enterPrecautions', 'Enter precautions')}\n        />\n      </div>\n\n      {/* Past Medical History */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('pastMedicalHistory', 'Past Medical History')}\n        </label>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\n          {pastMedicalHistoryOptions.map((option) => (\n            <label key={option} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.patientInfo.pastMedicalHistory.includes(option)}\n                onChange={(e) => handleArrayChange('pastMedicalHistory', option, e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">{t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}</span>\n            </label>\n          ))}\n          <div className=\"col-span-full\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.patientInfo.pastMedicalHistory.includes('Other')}\n                onChange={(e) => handleArrayChange('pastMedicalHistory', 'Other', e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300 mr-2\">{t('other', 'Other')}:</span>\n              <input\n                type=\"text\"\n                placeholder={t('specify', 'Specify')}\n                className=\"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                disabled={!formData.patientInfo.pastMedicalHistory.includes('Other')}\n              />\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* Medications and Effects */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('medications', 'Medications')}\n          </label>\n          <textarea\n            value={formData.patientInfo.medications}\n            onChange={(e) => handleInputChange('medications', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterMedications', 'Enter medications')}\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('effectsSideEffects', 'Effects/Side Effects')}\n          </label>\n          <textarea\n            value={formData.patientInfo.effectsSideEffects}\n            onChange={(e) => handleInputChange('effectsSideEffects', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterEffectsSideEffects', 'Enter effects/side effects')}\n          />\n        </div>\n      </div>\n\n      {/* Family History and Allergies */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('familyHistory', 'Family History')}\n          </label>\n          <textarea\n            value={formData.patientInfo.familyHistory}\n            onChange={(e) => handleInputChange('familyHistory', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterFamilyHistory', 'Enter family history')}\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('allergies', 'Allergies')}\n          </label>\n          <textarea\n            value={formData.patientInfo.allergies}\n            onChange={(e) => handleInputChange('allergies', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterAllergies', 'Enter allergies')}\n          />\n        </div>\n      </div>\n\n      {/* Source of Information */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('sourceOfInformation', 'Source of Information')}\n        </label>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-3\">\n          {sourceOfInformationOptions.map((option) => (\n            <label key={option} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.patientInfo.sourceOfInformation.includes(option)}\n                onChange={(e) => handleArrayChange('sourceOfInformation', option, e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">{t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}</span>\n            </label>\n          ))}\n          <div className=\"col-span-full\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.patientInfo.sourceOfInformation.includes('Others')}\n                onChange={(e) => handleArrayChange('sourceOfInformation', 'Others', e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300 mr-2\">{t('others', 'Others')}:</span>\n              <input\n                type=\"text\"\n                placeholder={t('specify', 'Specify')}\n                className=\"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                disabled={!formData.patientInfo.sourceOfInformation.includes('Others')}\n              />\n            </label>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientInformationPage;\n", "import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\n\nconst AssessmentReviewPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t, isRTL } = useLanguage();\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData.assessmentReview };\n    \n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    updateFormData('assessmentReview', newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleArrayChange = (field, value, checked) => {\n    const currentArray = formData.assessmentReview[field] || [];\n    let newArray;\n\n    if (checked) {\n      newArray = [...currentArray, value];\n    } else {\n      newArray = currentArray.filter(item => item !== value);\n    }\n\n    handleInputChange(field, newArray);\n  };\n\n  const handleReviewSystemChange = (system, field, value) => {\n    const newData = { ...formData.assessmentReview };\n    if (!newData.reviewOfSystems[system]) {\n      newData.reviewOfSystems[system] = { complaints: [], explain: '' };\n    }\n    \n    if (field === 'complaints') {\n      const currentComplaints = newData.reviewOfSystems[system].complaints || [];\n      if (currentComplaints.includes(value)) {\n        newData.reviewOfSystems[system].complaints = currentComplaints.filter(item => item !== value);\n      } else {\n        newData.reviewOfSystems[system].complaints = [...currentComplaints, value];\n      }\n    } else {\n      newData.reviewOfSystems[system][field] = value;\n    }\n\n    updateFormData('assessmentReview', newData);\n  };\n\n  // Assessment status options\n  const statusOptions = [\n    { value: 'Normal', label: t('normal', 'Normal') },\n    { value: 'Impaired', label: t('impaired', 'Impaired') },\n    { value: 'Not Applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  // Risk level options\n  const riskLevelOptions = [\n    { value: 'Low', label: t('low', 'Low') },\n    { value: 'High', label: t('high', 'High') },\n    { value: 'Moderate', label: t('moderate', 'Moderate') },\n    { value: 'None', label: t('none', 'None') }\n  ];\n\n  // Communication options\n  const communicationOptions = [\n    { value: 'Functional/effective', label: t('functionalEffective', 'Functional/effective') },\n    { value: 'Mild impairment', label: t('mildImpairment', 'Mild impairment') },\n    { value: 'Moderate impairment', label: t('moderateImpairment', 'Moderate impairment') },\n    { value: 'Severe impairment', label: t('severeImpairment', 'Severe impairment') }\n  ];\n\n  // Rehab potential options\n  const rehabPotentialOptions = [\n    { value: 'Excellent', label: t('excellent', 'Excellent') },\n    { value: 'Good', label: t('good', 'Good') },\n    { value: 'Fair', label: t('fair', 'Fair') },\n    { value: 'Poor', label: t('poor', 'Poor') }\n  ];\n\n  // Nutritional risk options\n  const nutritionalRiskOptions = [\n    'Reduced food intake last week',\n    'BMI > 18.5 kg/m2',\n    'Weight Loss',\n    'Enteral/Parenteral Feeding',\n    'None'\n  ];\n\n  // Psychosocial behavior risk options\n  const psychosocialRiskOptions = [\n    { value: 'No apparent risk', label: t('noApparentRisk', 'No apparent risk') },\n    { value: 'Mild behavioral concerns', label: t('mildBehavioralConcerns', 'Mild behavioral concerns') },\n    { value: 'Moderate Risk', label: t('moderateRisk', 'Moderate Risk') },\n    { value: 'High psychosocial risk', label: t('highPsychosocialRisk', 'High psychosocial risk') }\n  ];\n\n  // Developmental risk options\n  const developmentalRiskOptions = [\n    { value: 'Age appropriate', label: t('ageAppropriate', 'Age appropriate') },\n    { value: 'Mild delay', label: t('mildDelay', 'Mild delay') },\n    { value: 'Moderate delay', label: t('moderateDelay', 'Moderate delay') },\n    { value: 'Severe delay', label: t('severeDelay', 'Severe delay') }\n  ];\n\n  // Review of systems options\n  const reviewOfSystemsOptions = {\n    respiratory: [\n      'No Complaints', 'COPD', 'Sputum', 'Dyspnea', 'Asthma', 'Cyanosis', 'Cough'\n    ],\n    neurology: [\n      'No Complaints', 'Headache/Vertigo', 'Seizures', 'Tremors', 'Paralysis', 'Tingling', 'Numbness'\n    ],\n    gastrointestinal: [\n      'No Complaints', 'Nausea', 'Vomiting', 'Constipation', 'Hernia', 'Hematemesis'\n    ],\n    genitourinary: [\n      'No Complaints', 'Incontinence', 'Frequency', 'Discharge', 'Hematuria', 'Catheter'\n    ],\n    musculoskeletal: [\n      'No Complaints', 'Fractures', 'Trauma', 'Stiffness', 'Amputation', 'Congenital problems'\n    ],\n    hematology: [\n      'No Complaints', 'Anemia', 'Bruising', 'Hematemesis', 'History of transfusion'\n    ],\n    cardiovascular: [\n      'No Complaints', 'Pacemaker', 'Stent placement', 'Exertional dyspnea', 'Palpitations', 'Tachycardia'\n    ],\n    integumentary: [\n      'No Complaints', 'Pliability', 'Skin Color', 'Poor healing', 'Presence of scar formation', 'Pigmentations'\n    ],\n    reproductive: [\n      'No Complaints', 'Bleeding', 'Impotence', 'Hot flushes', 'Obstetric history'\n    ],\n    endocrine: [\n      'No Complaints', 'Night Sweat', 'Weight Changes', 'Fever', 'Polyphagia'\n    ]\n  };\n\n  // Bone health screening options\n  const boneHealthOptions = [\n    'History of Falls', 'Crohn\\'s/Ulcerative', 'Celiac Disease', 'Glucocorticoids >1 year',\n    'Bed Bound >6 months', 'Heterotrophic Ossification', 'Aromatase Inhibitors', 'Chemotherapy', 'None'\n  ];\n\n  const renderAssessmentSection = (title, field, showComments = true) => (\n    <div className=\"space-y-3\">\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n        {title}\n      </label>\n      <div className=\"flex flex-wrap gap-4\">\n        {statusOptions.map((option) => (\n          <label key={option.value} className=\"flex items-center\">\n            <input\n              type=\"radio\"\n              name={field}\n              value={option.value}\n              checked={formData.assessmentReview[field]?.status === option.value}\n              onChange={(e) => handleInputChange(`${field}.status`, e.target.value)}\n              className=\"mr-2\"\n            />\n            <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n          </label>\n        ))}\n      </div>\n      {showComments && formData.assessmentReview[field]?.status === 'Impaired' && (\n        <div className=\"mt-3\">\n          <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n            {t('comments', 'Comments')}\n          </label>\n          <textarea\n            value={formData.assessmentReview[field]?.comments || ''}\n            onChange={(e) => handleInputChange(`${field}.comments`, e.target.value)}\n            rows={2}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterComments', 'Enter comments')}\n          />\n        </div>\n      )}\n    </div>\n  );\n\n  const renderReviewOfSystemsSection = (systemName, systemKey) => (\n    <div key={systemKey} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n      <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n        {t(systemKey, systemName)}\n      </h4>\n      <div className=\"space-y-3\">\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n          {reviewOfSystemsOptions[systemKey]?.map((complaint) => (\n            <label key={complaint} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.assessmentReview.reviewOfSystems[systemKey]?.complaints?.includes(complaint) || false}\n                onChange={(e) => handleReviewSystemChange(systemKey, 'complaints', complaint)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                {t(complaint.toLowerCase().replace(/[^a-z]/g, ''), complaint)}\n              </span>\n            </label>\n          ))}\n        </div>\n        {formData.assessmentReview.reviewOfSystems[systemKey]?.complaints?.some(c => c !== 'No Complaints') && (\n          <div>\n            <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('explain', 'Explain')}\n            </label>\n            <textarea\n              value={formData.assessmentReview.reviewOfSystems[systemKey]?.explain || ''}\n              onChange={(e) => handleReviewSystemChange(systemKey, 'explain', e.target.value)}\n              rows={2}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('explainSymptoms', 'Explain symptoms')}\n            />\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('assessmentReviewOfSystems', 'Assessment & Review of Systems')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('comprehensiveSystemsAssessment', 'Comprehensive systems assessment and screening')}\n        </p>\n      </div>\n\n      {/* Screening Assessments */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {renderAssessmentSection(t('skinAppearance', 'Skin Appearance'), 'skinAppearance')}\n        {renderAssessmentSection(t('mentalFunctionsScreening', 'Mental Functions Screening'), 'mentalFunctions')}\n        {renderAssessmentSection(t('visualScreening', 'Visual Screening'), 'visualScreening')}\n        {renderAssessmentSection(t('hearingFunctionScreening', 'Hearing Function Screening'), 'hearingFunction')}\n      </div>\n\n      {/* Nutritional Risk */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('nutritionalRisk', 'Nutritional Risk')}\n        </label>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n          {nutritionalRiskOptions.map((option) => (\n            <label key={option} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.assessmentReview.nutritionalRisk?.includes(option) || false}\n                onChange={(e) => handleArrayChange('nutritionalRisk', option, e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                {t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}\n              </span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* Risk Assessments */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('fallRisk', 'Fall Risk')}\n          </label>\n          <div className=\"space-y-2\">\n            {riskLevelOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"fallRisk\"\n                  value={option.value}\n                  checked={formData.assessmentReview.fallRisk === option.value}\n                  onChange={(e) => handleInputChange('fallRisk', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('psychosocialBehaviorRisk', 'Psychosocial/Behavior Risk')}\n          </label>\n          <div className=\"space-y-2\">\n            {psychosocialRiskOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"psychosocialBehaviorRisk\"\n                  value={option.value}\n                  checked={formData.assessmentReview.psychosocialBehaviorRisk === option.value}\n                  onChange={(e) => handleInputChange('psychosocialBehaviorRisk', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('developmentalRisk', 'Developmental Risk')}\n          </label>\n          <div className=\"space-y-2\">\n            {developmentalRiskOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"developmentalRisk\"\n                  value={option.value}\n                  checked={formData.assessmentReview.developmentalRisk === option.value}\n                  onChange={(e) => handleInputChange('developmentalRisk', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Communication and Rehab Potential */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('communication', 'Communication')}\n          </label>\n          <div className=\"space-y-2\">\n            {communicationOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"communication\"\n                  value={option.value}\n                  checked={formData.assessmentReview.communication === option.value}\n                  onChange={(e) => handleInputChange('communication', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('rehabPotential', 'Rehab Potential')}\n          </label>\n          <div className=\"space-y-2\">\n            {rehabPotentialOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"rehabPotential\"\n                  value={option.value}\n                  checked={formData.assessmentReview.rehabPotential === option.value}\n                  onChange={(e) => handleInputChange('rehabPotential', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Vital Signs */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('vitalSigns', 'Vital Signs')}\n        </label>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('bloodPressure', 'Blood Pressure (BP)')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.assessmentReview.vitalSigns?.bp || ''}\n              onChange={(e) => handleInputChange('vitalSigns.bp', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"120/80\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('pulseRate', 'Pulse Rate (PR)')}\n            </label>\n            <input\n              type=\"number\"\n              value={formData.assessmentReview.vitalSigns?.pr || ''}\n              onChange={(e) => handleInputChange('vitalSigns.pr', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"72\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('respiratoryRate', 'Respiratory Rate (RR)')}\n            </label>\n            <input\n              type=\"number\"\n              value={formData.assessmentReview.vitalSigns?.rr || ''}\n              onChange={(e) => handleInputChange('vitalSigns.rr', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"16\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Review of Systems */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('reviewOfSystems', 'Review of Systems')}\n        </h3>\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4\">\n          {renderReviewOfSystemsSection('Respiratory', 'respiratory')}\n          {renderReviewOfSystemsSection('Neurology', 'neurology')}\n          {renderReviewOfSystemsSection('Gastrointestinal', 'gastrointestinal')}\n          {renderReviewOfSystemsSection('Genitourinary', 'genitourinary')}\n          {renderReviewOfSystemsSection('Musculoskeletal', 'musculoskeletal')}\n          {renderReviewOfSystemsSection('Hematology', 'hematology')}\n          {renderReviewOfSystemsSection('Cardiovascular', 'cardiovascular')}\n          {renderReviewOfSystemsSection('Integumentary (Skin)', 'integumentary')}\n          {renderReviewOfSystemsSection('Reproductive', 'reproductive')}\n          {renderReviewOfSystemsSection('Endocrine', 'endocrine')}\n        </div>\n      </div>\n\n      {/* Bone Health Screening */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('boneHealthScreening', 'Bone Health Screening')}\n        </label>\n        <div className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n            {boneHealthOptions.map((option) => (\n              <label key={option} className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.assessmentReview.boneHealthScreening?.conditions?.includes(option) || false}\n                  onChange={(e) => {\n                    const currentConditions = formData.assessmentReview.boneHealthScreening?.conditions || [];\n                    let newConditions;\n                    if (e.target.checked) {\n                      newConditions = [...currentConditions, option];\n                    } else {\n                      newConditions = currentConditions.filter(item => item !== option);\n                    }\n                    handleInputChange('boneHealthScreening.conditions', newConditions);\n                  }}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                  {t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}\n                </span>\n              </label>\n            ))}\n            <div className=\"col-span-full\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.assessmentReview.boneHealthScreening?.conditions?.includes('Others') || false}\n                  onChange={(e) => {\n                    const currentConditions = formData.assessmentReview.boneHealthScreening?.conditions || [];\n                    let newConditions;\n                    if (e.target.checked) {\n                      newConditions = [...currentConditions, 'Others'];\n                    } else {\n                      newConditions = currentConditions.filter(item => item !== 'Others');\n                    }\n                    handleInputChange('boneHealthScreening.conditions', newConditions);\n                  }}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300 mr-2\">{t('others', 'Others')}:</span>\n                <input\n                  type=\"text\"\n                  placeholder={t('specify', 'Specify')}\n                  className=\"px-2 py-1 border border-gray-300 rounded text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  disabled={!formData.assessmentReview.boneHealthScreening?.conditions?.includes('Others')}\n                />\n              </label>\n            </div>\n          </div>\n          <div>\n            <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('comments', 'Comments')}\n            </label>\n            <textarea\n              value={formData.assessmentReview.boneHealthScreening?.comments || ''}\n              onChange={(e) => handleInputChange('boneHealthScreening.comments', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('enterComments', 'Enter comments')}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AssessmentReviewPage;\n", "import React, { useState, useRef } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst BodyMap = ({ selectedAreas = [], onAreaSelect, title, showLegend = true }) => {\n  const { t } = useLanguage();\n  const svgRef = useRef(null);\n  const [hoveredArea, setHoveredArea] = useState(null);\n\n  // Body areas with their SVG paths and labels\n  const bodyAreas = {\n    head: {\n      label: t('head', 'Head'),\n      color: '#FF6B6B',\n      path: 'M150,20 C170,20 180,30 180,50 C180,70 170,80 150,80 C130,80 120,70 120,50 C120,30 130,20 150,20 Z'\n    },\n    neck: {\n      label: t('neck', 'Neck'),\n      color: '#4ECDC4',\n      path: 'M140,80 L160,80 L160,100 L140,100 Z'\n    },\n    leftShoulder: {\n      label: t('leftShoulder', 'Left Shoulder'),\n      color: '#45B7D1',\n      path: 'M100,100 C110,95 120,100 130,110 L120,130 C110,125 100,120 90,115 Z'\n    },\n    rightShoulder: {\n      label: t('rightShoulder', 'Right Shoulder'),\n      color: '#45B7D1',\n      path: 'M200,100 C190,95 180,100 170,110 L180,130 C190,125 200,120 210,115 Z'\n    },\n    leftArm: {\n      label: t('leftArm', 'Left Arm'),\n      color: '#96CEB4',\n      path: 'M90,115 L80,180 L95,185 L105,120 Z'\n    },\n    rightArm: {\n      label: t('rightArm', 'Right Arm'),\n      color: '#96CEB4',\n      path: 'M210,115 L220,180 L205,185 L195,120 Z'\n    },\n    leftForearm: {\n      label: t('leftForearm', 'Left Forearm'),\n      color: '#FFEAA7',\n      path: 'M80,180 L70,240 L85,245 L95,185 Z'\n    },\n    rightForearm: {\n      label: t('rightForearm', 'Right Forearm'),\n      color: '#FFEAA7',\n      path: 'M220,180 L230,240 L215,245 L205,185 Z'\n    },\n    leftHand: {\n      label: t('leftHand', 'Left Hand'),\n      color: '#DDA0DD',\n      path: 'M70,240 L60,260 L75,265 L85,245 Z'\n    },\n    rightHand: {\n      label: t('rightHand', 'Right Hand'),\n      color: '#DDA0DD',\n      path: 'M230,240 L240,260 L225,265 L215,245 Z'\n    },\n    chest: {\n      label: t('chest', 'Chest'),\n      color: '#74B9FF',\n      path: 'M130,100 L170,100 L170,160 L130,160 Z'\n    },\n    abdomen: {\n      label: t('abdomen', 'Abdomen'),\n      color: '#A29BFE',\n      path: 'M130,160 L170,160 L170,220 L130,220 Z'\n    },\n    pelvis: {\n      label: t('pelvis', 'Pelvis'),\n      color: '#FD79A8',\n      path: 'M130,220 L170,220 L170,260 L130,260 Z'\n    },\n    leftThigh: {\n      label: t('leftThigh', 'Left Thigh'),\n      color: '#00B894',\n      path: 'M120,260 L140,260 L135,340 L115,340 Z'\n    },\n    rightThigh: {\n      label: t('rightThigh', 'Right Thigh'),\n      color: '#00B894',\n      path: 'M160,260 L180,260 L185,340 L165,340 Z'\n    },\n    leftKnee: {\n      label: t('leftKnee', 'Left Knee'),\n      color: '#E17055',\n      path: 'M115,340 L135,340 L135,360 L115,360 Z'\n    },\n    rightKnee: {\n      label: t('rightKnee', 'Right Knee'),\n      color: '#E17055',\n      path: 'M165,340 L185,340 L185,360 L165,360 Z'\n    },\n    leftLeg: {\n      label: t('leftLeg', 'Left Leg'),\n      color: '#FDCB6E',\n      path: 'M115,360 L135,360 L130,440 L110,440 Z'\n    },\n    rightLeg: {\n      label: t('rightLeg', 'Right Leg'),\n      color: '#FDCB6E',\n      path: 'M165,360 L185,360 L190,440 L170,440 Z'\n    },\n    leftFoot: {\n      label: t('leftFoot', 'Left Foot'),\n      color: '#6C5CE7',\n      path: 'M110,440 L130,440 L135,460 L105,460 Z'\n    },\n    rightFoot: {\n      label: t('rightFoot', 'Right Foot'),\n      color: '#6C5CE7',\n      path: 'M170,440 L190,440 L195,460 L165,460 Z'\n    },\n    upperBack: {\n      label: t('upperBack', 'Upper Back'),\n      color: '#00CEC9',\n      path: 'M130,100 L170,100 L170,180 L130,180 Z'\n    },\n    lowerBack: {\n      label: t('lowerBack', 'Lower Back'),\n      color: '#55A3FF',\n      path: 'M130,180 L170,180 L170,260 L130,260 Z'\n    }\n  };\n\n  const handleAreaClick = (areaKey) => {\n    if (onAreaSelect) {\n      const newSelectedAreas = selectedAreas.includes(areaKey)\n        ? selectedAreas.filter(area => area !== areaKey)\n        : [...selectedAreas, areaKey];\n      onAreaSelect(newSelectedAreas);\n    }\n  };\n\n  const isSelected = (areaKey) => selectedAreas.includes(areaKey);\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      {title && (\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">{title}</h3>\n      )}\n      \n      <div className=\"flex flex-col lg:flex-row gap-6\">\n        {/* Front View */}\n        <div className=\"flex-1\">\n          <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-3 text-center\">\n            {t('frontView', 'Front View')}\n          </h4>\n          <div className=\"flex justify-center\">\n            <svg\n              ref={svgRef}\n              width=\"300\"\n              height=\"480\"\n              viewBox=\"0 0 300 480\"\n              className=\"border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700\"\n            >\n              {/* Body outline */}\n              <path\n                d=\"M150,20 C170,20 180,30 180,50 C180,70 170,80 150,80 C130,80 120,70 120,50 C120,30 130,20 150,20 Z\n                   M140,80 L160,80 L170,100 L200,100 C210,95 220,100 230,115 L220,180 L230,240 L240,260 L225,265 L215,245 L205,185 L195,120 L180,130 L170,110 L170,260 L180,260 L185,340 L185,360 L190,440 L195,460 L165,460 L170,440 L165,360 L165,340 L160,260 L140,260 L135,340 L135,360 L130,440 L135,460 L105,460 L110,440 L115,360 L115,340 L120,260 L130,260 L130,110 L120,130 L105,120 L95,185 L85,245 L75,265 L60,260 L70,240 L80,180 L90,115 C100,100 110,95 130,100 L140,80 Z\"\n                fill=\"none\"\n                stroke=\"#374151\"\n                strokeWidth=\"2\"\n                className=\"dark:stroke-gray-400\"\n              />\n              \n              {/* Clickable body areas */}\n              {Object.entries(bodyAreas)\n                .filter(([key]) => !key.includes('Back'))\n                .map(([areaKey, area]) => (\n                <g key={areaKey}>\n                  <path\n                    d={area.path}\n                    fill={isSelected(areaKey) ? area.color : 'transparent'}\n                    stroke={area.color}\n                    strokeWidth=\"2\"\n                    opacity={isSelected(areaKey) ? 0.8 : (hoveredArea === areaKey ? 0.4 : 0.2)}\n                    className=\"cursor-pointer transition-opacity duration-200\"\n                    onClick={() => handleAreaClick(areaKey)}\n                    onMouseEnter={() => setHoveredArea(areaKey)}\n                    onMouseLeave={() => setHoveredArea(null)}\n                  />\n                  {hoveredArea === areaKey && (\n                    <text\n                      x=\"150\"\n                      y=\"10\"\n                      textAnchor=\"middle\"\n                      className=\"fill-gray-900 dark:fill-white text-sm font-medium\"\n                    >\n                      {area.label}\n                    </text>\n                  )}\n                </g>\n              ))}\n            </svg>\n          </div>\n        </div>\n\n        {/* Back View */}\n        <div className=\"flex-1\">\n          <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-3 text-center\">\n            {t('backView', 'Back View')}\n          </h4>\n          <div className=\"flex justify-center\">\n            <svg\n              width=\"300\"\n              height=\"480\"\n              viewBox=\"0 0 300 480\"\n              className=\"border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700\"\n            >\n              {/* Back body outline */}\n              <path\n                d=\"M150,20 C170,20 180,30 180,50 C180,70 170,80 150,80 C130,80 120,70 120,50 C120,30 130,20 150,20 Z\n                   M140,80 L160,80 L170,100 L170,260 L160,260 L140,260 L130,260 L130,100 L140,80 Z\"\n                fill=\"none\"\n                stroke=\"#374151\"\n                strokeWidth=\"2\"\n                className=\"dark:stroke-gray-400\"\n              />\n              \n              {/* Back areas */}\n              {Object.entries(bodyAreas)\n                .filter(([key]) => key.includes('Back'))\n                .map(([areaKey, area]) => (\n                <g key={areaKey}>\n                  <path\n                    d={area.path}\n                    fill={isSelected(areaKey) ? area.color : 'transparent'}\n                    stroke={area.color}\n                    strokeWidth=\"2\"\n                    opacity={isSelected(areaKey) ? 0.8 : (hoveredArea === areaKey ? 0.4 : 0.2)}\n                    className=\"cursor-pointer transition-opacity duration-200\"\n                    onClick={() => handleAreaClick(areaKey)}\n                    onMouseEnter={() => setHoveredArea(areaKey)}\n                    onMouseLeave={() => setHoveredArea(null)}\n                  />\n                  {hoveredArea === areaKey && (\n                    <text\n                      x=\"150\"\n                      y=\"10\"\n                      textAnchor=\"middle\"\n                      className=\"fill-gray-900 dark:fill-white text-sm font-medium\"\n                    >\n                      {area.label}\n                    </text>\n                  )}\n                </g>\n              ))}\n            </svg>\n          </div>\n        </div>\n      </div>\n\n      {/* Selected Areas Display */}\n      {selectedAreas.length > 0 && (\n        <div className=\"mt-6\">\n          <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('selectedAreas', 'Selected Areas')}:\n          </h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {selectedAreas.map((areaKey) => (\n              <span\n                key={areaKey}\n                className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\"\n              >\n                {bodyAreas[areaKey]?.label}\n                <button\n                  onClick={() => handleAreaClick(areaKey)}\n                  className=\"ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100\"\n                >\n                  ×\n                </button>\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Legend */}\n      {showLegend && (\n        <div className=\"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('instructions', 'Instructions')}:\n          </h4>\n          <ul className=\"text-sm text-gray-600 dark:text-gray-400 space-y-1\">\n            <li>• {t('clickToSelectArea', 'Click on body areas to select/deselect them')}</li>\n            <li>• {t('hoverForLabel', 'Hover over areas to see their labels')}</li>\n            <li>• {t('multipleSelection', 'Multiple areas can be selected')}</li>\n            <li>• {t('selectedAreasShown', 'Selected areas are highlighted and listed below')}</li>\n          </ul>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default BodyMap;\n", "import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\nimport BodyMap from '../BodyMap';\n\nconst MusculoskeletalExamPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t, isRTL } = useLanguage();\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData.musculoskeletalExam };\n    \n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    updateFormData('musculoskeletalExam', newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleArrayChange = (field, value, checked) => {\n    const currentArray = formData.musculoskeletalExam[field] || [];\n    let newArray;\n\n    if (checked) {\n      newArray = [...currentArray, value];\n    } else {\n      newArray = currentArray.filter(item => item !== value);\n    }\n\n    handleInputChange(field, newArray);\n  };\n\n  // Status options\n  const statusOptions = [\n    { value: 'Normal', label: t('normal', 'Normal') },\n    { value: 'Impaired', label: t('impaired', 'Impaired') },\n    { value: 'Not Applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  // Circulation options\n  const circulationOptions = [\n    { value: 'Present', label: t('present', 'Present') },\n    { value: 'Diminished', label: t('diminished', 'Diminished') },\n    { value: 'Not Palpable', label: t('notPalpable', 'Not Palpable') }\n  ];\n\n  // Edema options\n  const edemaOptions = [\n    { value: 'Absent', label: t('absent', 'Absent') },\n    { value: 'Present', label: t('present', 'Present') },\n    { value: 'Not Applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  // Edema grade options\n  const edemaGradeOptions = [\n    '+1 Trace', '+2 Moderate', '+3 Deep', '+4 Very deep, non-pitting'\n  ];\n\n  // Soft tissue palpation options\n  const softTissuePalpationOptions = [\n    'Tenderness', 'Trigger Point', 'Swelling', 'Warmness', 'Muscles Spasm'\n  ];\n\n  // Wound types\n  const woundTypes = [\n    'Traumatic Wounds', 'Surgical Wounds', 'Scar Tissue', 'Pressure Ulcers',\n    'Diabetic Foot Ulcers', 'Post-Surgical Scar', 'Venous Ulcers', 'Fungal Infections',\n    'Burn (1st/2nd/3rd)'\n  ];\n\n  // Pain scale options\n  const painScaleOptions = [\n    { value: 'Numeric Pain Scale', label: t('numericPainScale', 'Numeric Pain Scale') },\n    { value: 'Disability Pain Index', label: t('disabilityPainIndex', 'Disability Pain Index') },\n    { value: 'Wong Baker', label: t('wongBaker', 'Wong Baker') },\n    { value: 'Flacc', label: t('flacc', 'Flacc') }\n  ];\n\n  // Pain type options\n  const painTypeOptions = [\n    { value: 'Nociceptive', label: t('nociceptive', 'Nociceptive') },\n    { value: 'Neuropathic', label: t('neuropathic', 'Neuropathic') },\n    { value: 'Mixed', label: t('mixed', 'Mixed') }\n  ];\n\n  const renderAssessmentSection = (title, field, options = statusOptions, showComments = true) => (\n    <div className=\"space-y-3\">\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n        {title}\n      </label>\n      <div className=\"flex flex-wrap gap-4\">\n        {options.map((option) => (\n          <label key={option.value} className=\"flex items-center\">\n            <input\n              type=\"radio\"\n              name={field}\n              value={option.value}\n              checked={formData.musculoskeletalExam[field]?.status === option.value}\n              onChange={(e) => handleInputChange(`${field}.status`, e.target.value)}\n              className=\"mr-2\"\n            />\n            <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n          </label>\n        ))}\n      </div>\n      {showComments && (formData.musculoskeletalExam[field]?.status === 'Impaired' || formData.musculoskeletalExam[field]?.status === 'Present') && (\n        <div className=\"mt-3\">\n          <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n            {t('comments', 'Comments')}\n          </label>\n          <textarea\n            value={formData.musculoskeletalExam[field]?.comments || ''}\n            onChange={(e) => handleInputChange(`${field}.comments`, e.target.value)}\n            rows={2}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterComments', 'Enter comments')}\n          />\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('musculoskeletalExaminationPain', 'Musculoskeletal Examination & Pain')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('physicalExaminationPainAssessment', 'Physical examination and pain assessment')}\n        </p>\n      </div>\n\n      {/* Physical Examination */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {renderAssessmentSection(t('skinWarmthTurgor', 'Skin Warmth/Turgor'), 'skinWarmthTurgor')}\n        {renderAssessmentSection(t('circulationPulsesByPalpation', 'Circulation: Pulses by Palpation'), 'circulation', circulationOptions)}\n      </div>\n\n      {/* Edema Assessment */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('edema', 'Edema')}\n        </label>\n        <div className=\"space-y-4\">\n          <div className=\"flex flex-wrap gap-4\">\n            {edemaOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"edema\"\n                  value={option.value}\n                  checked={formData.musculoskeletalExam.edema?.status === option.value}\n                  onChange={(e) => handleInputChange('edema.status', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n          {formData.musculoskeletalExam.edema?.status === 'Present' && (\n            <div className=\"space-y-3\">\n              <div>\n                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  {t('grade', 'Grade')}\n                </label>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-2\">\n                  {edemaGradeOptions.map((grade) => (\n                    <label key={grade} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.musculoskeletalExam.edema?.grade?.includes(grade) || false}\n                        onChange={(e) => {\n                          const currentGrades = formData.musculoskeletalExam.edema?.grade || [];\n                          let newGrades;\n                          if (e.target.checked) {\n                            newGrades = [...currentGrades, grade];\n                          } else {\n                            newGrades = currentGrades.filter(item => item !== grade);\n                          }\n                          handleInputChange('edema.grade', newGrades);\n                        }}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">{grade}</span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n              <div>\n                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                  {t('comments', 'Comments')}\n                </label>\n                <textarea\n                  value={formData.musculoskeletalExam.edema?.comments || ''}\n                  onChange={(e) => handleInputChange('edema.comments', e.target.value)}\n                  rows={2}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterComments', 'Enter comments')}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Soft Tissue Palpation */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('softTissuePalpation', 'Soft Tissue Palpation')}\n        </label>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3\">\n          {softTissuePalpationOptions.map((option) => (\n            <label key={option} className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.musculoskeletalExam.softTissuePalpation?.includes(option) || false}\n                onChange={(e) => handleArrayChange('softTissuePalpation', option, e.target.checked)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                {t(option.toLowerCase().replace(/[^a-z]/g, ''), option)}\n              </span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* Wound Assessment */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('presenceOfWoundOrSkinBreakdown', 'Presence of Wound or Skin Breakdown')}\n        </label>\n        <div className=\"space-y-4\">\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"woundPresence\"\n                value=\"Yes\"\n                checked={formData.musculoskeletalExam.woundPresence?.hasWound}\n                onChange={(e) => handleInputChange('woundPresence.hasWound', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('yes', 'Yes')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"woundPresence\"\n                value=\"No\"\n                checked={!formData.musculoskeletalExam.woundPresence?.hasWound}\n                onChange={(e) => handleInputChange('woundPresence.hasWound', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('no', 'No')}\n            </label>\n          </div>\n          {formData.musculoskeletalExam.woundPresence?.hasWound && (\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  {t('woundTypes', 'Wound Types')}\n                </label>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n                  {woundTypes.map((type) => (\n                    <label key={type} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.musculoskeletalExam.woundPresence?.types?.includes(type) || false}\n                        onChange={(e) => {\n                          const currentTypes = formData.musculoskeletalExam.woundPresence?.types || [];\n                          let newTypes;\n                          if (e.target.checked) {\n                            newTypes = [...currentTypes, type];\n                          } else {\n                            newTypes = currentTypes.filter(item => item !== type);\n                          }\n                          handleInputChange('woundPresence.types', newTypes);\n                        }}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                        {t(type.toLowerCase().replace(/[^a-z]/g, ''), type)}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n              <div>\n                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                  {t('comments', 'Comments')}\n                </label>\n                <textarea\n                  value={formData.musculoskeletalExam.woundPresence?.comments || ''}\n                  onChange={(e) => handleInputChange('woundPresence.comments', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterComments', 'Enter comments')}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Pain Assessment */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('painAssessment', 'Pain Assessment')}\n        </label>\n        <div className=\"space-y-4\">\n          <div className=\"flex space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"painAssessment\"\n                value=\"Yes\"\n                checked={formData.musculoskeletalExam.painAssessment?.hasPain}\n                onChange={(e) => handleInputChange('painAssessment.hasPain', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('yes', 'Yes')}\n            </label>\n            <label className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name=\"painAssessment\"\n                value=\"No\"\n                checked={!formData.musculoskeletalExam.painAssessment?.hasPain}\n                onChange={(e) => handleInputChange('painAssessment.hasPain', e.target.value === 'Yes')}\n                className=\"mr-2\"\n              />\n              {t('no', 'No')}\n            </label>\n          </div>\n          {formData.musculoskeletalExam.painAssessment?.hasPain && (\n            <div className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                    {t('painScore', 'Pain Score (0-10)')}\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    max=\"10\"\n                    value={formData.musculoskeletalExam.painAssessment?.painScore || ''}\n                    onChange={(e) => handleInputChange('painAssessment.painScore', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder=\"0-10\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                    {t('scale', 'Scale')}\n                  </label>\n                  <select\n                    value={formData.musculoskeletalExam.painAssessment?.scale || ''}\n                    onChange={(e) => handleInputChange('painAssessment.scale', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"\">{t('selectScale', 'Select scale')}</option>\n                    {painScaleOptions.map((option) => (\n                      <option key={option.value} value={option.value}>\n                        {option.label}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n              <div>\n                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  {t('painType', 'Pain Type')}\n                </label>\n                <div className=\"flex flex-wrap gap-4\">\n                  {painTypeOptions.map((option) => (\n                    <label key={option.value} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"painType\"\n                        value={option.value}\n                        checked={formData.musculoskeletalExam.painAssessment?.type === option.value}\n                        onChange={(e) => handleInputChange('painAssessment.type', e.target.value)}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* Pain Location Body Map */}\n              <div>\n                <BodyMap\n                  title={t('painLocation', 'Pain Location - Select affected areas')}\n                  selectedAreas={formData.musculoskeletalExam.painAssessment?.bodyMapAreas || []}\n                  onAreaSelect={(areas) => handleInputChange('painAssessment.bodyMapAreas', areas)}\n                  showLegend={true}\n                />\n              </div>\n\n              {/* Additional Pain Details */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                    {t('painOnset', 'Pain Onset')}\n                  </label>\n                  <div className=\"space-y-2\">\n                    {[\n                      { value: 'New last 7 days', label: t('newLast7Days', 'New last 7 days') },\n                      { value: 'Recent last 3 Mos', label: t('recentLast3Months', 'Recent last 3 months') },\n                      { value: 'More Distant >3 Mos', label: t('moreDistant3Months', 'More distant >3 months') },\n                      { value: 'Unknown', label: t('unknown', 'Unknown') }\n                    ].map((option) => (\n                      <label key={option.value} className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name=\"painOnset\"\n                          value={option.value}\n                          checked={formData.musculoskeletalExam.painAssessment?.onset === option.value}\n                          onChange={(e) => handleInputChange('painAssessment.onset', e.target.value)}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                    {t('painFrequency', 'Pain Frequency')}\n                  </label>\n                  <div className=\"space-y-2\">\n                    {[\n                      { value: 'Constant', label: t('constant', 'Constant') },\n                      { value: 'Intermittent', label: t('intermittent', 'Intermittent') },\n                      { value: 'Occasional', label: t('occasional', 'Occasional') },\n                      { value: 'Rare', label: t('rare', 'Rare') }\n                    ].map((option) => (\n                      <label key={option.value} className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name=\"painFrequency\"\n                          value={option.value}\n                          checked={formData.musculoskeletalExam.painAssessment?.frequency === option.value}\n                          onChange={(e) => handleInputChange('painAssessment.frequency', e.target.value)}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              </div>\n\n              {/* Pain Description */}\n              <div>\n                <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                  {t('painDescription', 'Pain Description')}\n                </label>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\">\n                  {[\n                    'Knife-like', 'Throbbing', 'Burning', 'Aching', 'Sharp', 'Dull',\n                    'Cramping', 'Shooting', 'Stabbing', 'Tingling', 'Numbness', 'Stiffness'\n                  ].map((description) => (\n                    <label key={description} className=\"flex items-center\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.musculoskeletalExam.painAssessment?.description?.includes(description) || false}\n                        onChange={(e) => {\n                          const currentDescriptions = formData.musculoskeletalExam.painAssessment?.description || [];\n                          let newDescriptions;\n                          if (e.target.checked) {\n                            newDescriptions = [...currentDescriptions, description];\n                          } else {\n                            newDescriptions = currentDescriptions.filter(item => item !== description);\n                          }\n                          handleInputChange('painAssessment.description', newDescriptions);\n                        }}\n                        className=\"mr-2\"\n                      />\n                      <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                        {t(description.toLowerCase().replace(/[^a-z]/g, ''), description)}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* Aggravating and Relieving Factors */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                    {t('aggravatingFactors', 'Aggravating Factors')}\n                  </label>\n                  <div className=\"space-y-2\">\n                    {[\n                      'Movement', 'Fatigue', 'Weather', 'Stress', 'Activity', 'Rest', 'Position changes'\n                    ].map((factor) => (\n                      <label key={factor} className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.musculoskeletalExam.painAssessment?.aggravatingFactors?.includes(factor) || false}\n                          onChange={(e) => {\n                            const currentFactors = formData.musculoskeletalExam.painAssessment?.aggravatingFactors || [];\n                            let newFactors;\n                            if (e.target.checked) {\n                              newFactors = [...currentFactors, factor];\n                            } else {\n                              newFactors = currentFactors.filter(item => item !== factor);\n                            }\n                            handleInputChange('painAssessment.aggravatingFactors', newFactors);\n                          }}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                          {t(factor.toLowerCase().replace(/[^a-z]/g, ''), factor)}\n                        </span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                    {t('relievingFactors', 'Relieving Factors')}\n                  </label>\n                  <div className=\"space-y-2\">\n                    {[\n                      'Cold', 'Heat', 'Rest', 'Medication', 'Exercise', 'Massage', 'Position change'\n                    ].map((factor) => (\n                      <label key={factor} className=\"flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={formData.musculoskeletalExam.painAssessment?.relievingFactors?.includes(factor) || false}\n                          onChange={(e) => {\n                            const currentFactors = formData.musculoskeletalExam.painAssessment?.relievingFactors || [];\n                            let newFactors;\n                            if (e.target.checked) {\n                              newFactors = [...currentFactors, factor];\n                            } else {\n                              newFactors = currentFactors.filter(item => item !== factor);\n                            }\n                            handleInputChange('painAssessment.relievingFactors', newFactors);\n                          }}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                          {t(factor.toLowerCase().replace(/[^a-z]/g, ''), factor)}\n                        </span>\n                      </label>\n                    ))}\n                  </div>\n                  <div className=\"mt-3\">\n                    <label className=\"block text-sm text-gray-600 dark:text-gray-400 mb-1\">\n                      {t('describeReliefMethods', 'Describe relief methods')}\n                    </label>\n                    <textarea\n                      value={formData.musculoskeletalExam.painAssessment?.relievingDescription || ''}\n                      onChange={(e) => handleInputChange('painAssessment.relievingDescription', e.target.value)}\n                      rows={2}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                      placeholder={t('describeWhatHelps', 'Describe what helps relieve the pain')}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MusculoskeletalExamPage;\n", "import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\n\nconst SensoryFunctionsPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t } = useLanguage();\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData.sensoryFunctions };\n\n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    updateFormData('sensoryFunctions', newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  // Status options\n  const statusOptions = [\n    { value: 'Normal', label: t('normal', 'Normal') },\n    { value: 'Impaired', label: t('impaired', 'Impaired') },\n    { value: 'Need Further Assessment', label: t('needFurtherAssessment', 'Need Further Assessment') }\n  ];\n\n  // Coordination test options\n  const coordinationOptions = [\n    { value: 'Intact', label: t('intact', 'Intact') },\n    { value: 'Impaired', label: t('impaired', 'Impaired') },\n    { value: 'Not Applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  // Sensory assessment types\n  const sensoryTypes = [\n    { key: 'pain', label: t('pain', 'Pain') },\n    { key: 'lightTouch', label: t('lightTouch', 'Light Touch') },\n    { key: 'sharpDull', label: t('sharpDull', 'Sharp-Dull') },\n    { key: 'temperature', label: t('temperature', 'Temperature') },\n    { key: 'stereognosis', label: t('stereognosis', 'Stereognosis') },\n    { key: 'proprioception', label: t('proprioception', 'Proprioception') }\n  ];\n\n  // Coordination tests\n  const coordinationTests = [\n    { key: 'eyeContact', label: t('eyeContact', 'Eye Contact') },\n    { key: 'eyeToHand', label: t('eyeToHand', 'Eye to Hand') },\n    { key: 'fingerToNose', label: t('fingerToNose', 'Finger to Nose') },\n    { key: 'heelToShin', label: t('heelToShin', 'Heel to Shin') },\n    { key: 'rapidAlternatingMovements', label: t('rapidAlternatingMovements', 'Rapid Alternating Movements') },\n    { key: 'dysdiadochokinesia', label: t('dysdiadochokinesia', 'Dysdiadochokinesia') }\n  ];\n\n  const renderSensoryAssessmentTable = (bodyPart, title) => (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">{title}</h3>\n\n      {/* Status Selection */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n          {t('overallStatus', 'Overall Status')}\n        </label>\n        <div className=\"flex flex-wrap gap-4\">\n          {statusOptions.map((option) => (\n            <label key={option.value} className=\"flex items-center\">\n              <input\n                type=\"radio\"\n                name={`${bodyPart}Status`}\n                value={option.value}\n                checked={formData.sensoryFunctions[bodyPart]?.status === option.value}\n                onChange={(e) => handleInputChange(`${bodyPart}.status`, e.target.value)}\n                className=\"mr-2\"\n              />\n              <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n            </label>\n          ))}\n        </div>\n      </div>\n\n      {/* Detailed Assessments Table */}\n      {(formData.sensoryFunctions[bodyPart]?.status === 'Impaired' ||\n        formData.sensoryFunctions[bodyPart]?.status === 'Need Further Assessment') && (\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full border-collapse border border-gray-300 dark:border-gray-600\">\n            <thead>\n              <tr className=\"bg-gray-50 dark:bg-gray-700\">\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('assessment', 'Assessment')}\n                </th>\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('left', 'Left')}\n                </th>\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('right', 'Right')}\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {sensoryTypes.map((type) => (\n                <tr key={type.key}>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-900 dark:text-white\">\n                    {type.label}\n                  </td>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                    <input\n                      type=\"text\"\n                      value={formData.sensoryFunctions[bodyPart]?.assessments?.[type.key]?.left || ''}\n                      onChange={(e) => handleInputChange(`${bodyPart}.assessments.${type.key}.left`, e.target.value)}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                      placeholder={t('intactImpaired', 'Intact/Impaired')}\n                    />\n                  </td>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                    <input\n                      type=\"text\"\n                      value={formData.sensoryFunctions[bodyPart]?.assessments?.[type.key]?.right || ''}\n                      onChange={(e) => handleInputChange(`${bodyPart}.assessments.${type.key}.right`, e.target.value)}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                      placeholder={t('intactImpaired', 'Intact/Impaired')}\n                    />\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('sensoryFunctionsCoordination', 'Sensory Functions & Coordination')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('sensoryAssessmentCoordinationTests', 'Sensory assessment and coordination tests')}\n        </p>\n      </div>\n\n      {/* Upper Body Sensory Functions */}\n      {renderSensoryAssessmentTable('upperBodySensory', t('upperBodySensoryFunctions', 'Upper Body Sensory Functions'))}\n\n      {/* Lower Body Sensory Functions */}\n      {renderSensoryAssessmentTable('lowerBodySensory', t('lowerBodySensoryFunctions', 'Lower Body Sensory Functions'))}\n\n      {/* Coordination and Voluntary Movement */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('coordinationVoluntaryMovement', 'Coordination and Voluntary Movement')}\n        </h3>\n\n        {/* Status Selection */}\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('overallStatus', 'Overall Status')}\n          </label>\n          <div className=\"flex flex-wrap gap-4\">\n            {statusOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"coordinationStatus\"\n                  value={option.value}\n                  checked={formData.sensoryFunctions.coordinationVoluntaryMovement?.status === option.value}\n                  onChange={(e) => handleInputChange('coordinationVoluntaryMovement.status', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* Detailed Coordination Tests */}\n        {(formData.sensoryFunctions.coordinationVoluntaryMovement?.status === 'Impaired' ||\n          formData.sensoryFunctions.coordinationVoluntaryMovement?.status === 'Need Further Assessment') && (\n          <div className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              {coordinationTests.map((test) => (\n                <div key={test.key} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {test.label}\n                  </label>\n                  <div className=\"flex flex-wrap gap-3\">\n                    {coordinationOptions.map((option) => (\n                      <label key={option.value} className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name={`coordination_${test.key}`}\n                          value={option.value}\n                          checked={formData.sensoryFunctions.coordinationVoluntaryMovement?.assessments?.[test.key] === option.value}\n                          onChange={(e) => handleInputChange(`coordinationVoluntaryMovement.assessments.${test.key}`, e.target.value)}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Comments */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('comments', 'Comments')}\n              </label>\n              <textarea\n                value={formData.sensoryFunctions.coordinationVoluntaryMovement?.comments || ''}\n                onChange={(e) => handleInputChange('coordinationVoluntaryMovement.comments', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterComments', 'Enter comments')}\n              />\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default SensoryFunctionsPage;\n", "import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\n\nconst NeuromusculoskeletalPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t } = useLanguage();\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData.neuromusculoskeletal };\n\n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    updateFormData('neuromusculoskeletal', newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  // Muscle test grades (0-5 scale)\n  const muscleTestGrades = [\n    { value: '0', label: '0 - No contraction' },\n    { value: '1', label: '1 - Trace contraction' },\n    { value: '2', label: '2 - Poor (gravity eliminated)' },\n    { value: '3', label: '3 - Fair (against gravity)' },\n    { value: '4', label: '4 - Good (against resistance)' },\n    { value: '5', label: '5 - Normal' },\n    { value: 'N/A', label: 'N/A - Not Applicable' }\n  ];\n\n  // Muscle tone grades (0-4 scale)\n  const muscleToneGrades = [\n    { value: '0', label: '0 - Flaccid' },\n    { value: '1', label: '1 - Hypotonic' },\n    { value: '2', label: '2 - Normal' },\n    { value: '3', label: '3 - Hypertonic' },\n    { value: '4', label: '4 - Rigid' },\n    { value: 'N/A', label: 'N/A - Not Applicable' }\n  ];\n\n  // Joint assessments with normal ranges\n  const jointAssessments = {\n    neck: {\n      title: t('neck', 'Neck'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '60°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '75°' },\n        { key: 'lateralFlexionL', label: t('lateralFlexionLeft', 'Lateral Flexion (L)'), normalRange: '45°' },\n        { key: 'lateralFlexionR', label: t('lateralFlexionRight', 'Lateral Flexion (R)'), normalRange: '45°' },\n        { key: 'rotationL', label: t('rotationLeft', 'Rotation (L)'), normalRange: '80°' },\n        { key: 'rotationR', label: t('rotationRight', 'Rotation (R)'), normalRange: '80°' }\n      ]\n    },\n    shoulder: {\n      title: t('shoulder', 'Shoulder'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '180°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '60°' },\n        { key: 'abduction', label: t('abduction', 'Abduction'), normalRange: '180°' },\n        { key: 'adduction', label: t('adduction', 'Adduction'), normalRange: '50°' },\n        { key: 'internalRotation', label: t('internalRotation', 'Internal Rotation'), normalRange: '90°' },\n        { key: 'externalRotation', label: t('externalRotation', 'External Rotation'), normalRange: '90°' }\n      ]\n    },\n    elbow: {\n      title: t('elbow', 'Elbow'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '150°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '0°' }\n      ]\n    },\n    forearm: {\n      title: t('forearm', 'Forearm'),\n      movements: [\n        { key: 'pronation', label: t('pronation', 'Pronation'), normalRange: '90°' },\n        { key: 'supination', label: t('supination', 'Supination'), normalRange: '90°' }\n      ]\n    },\n    wrist: {\n      title: t('wrist', 'Wrist'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '90°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '70°' },\n        { key: 'radialDeviation', label: t('radialDeviation', 'Radial Deviation'), normalRange: '25°' },\n        { key: 'ulnarDeviation', label: t('ulnarDeviation', 'Ulnar Deviation'), normalRange: '35°' }\n      ]\n    },\n    fingers: {\n      title: t('fingers', 'Fingers'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '90°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '0°' },\n        { key: 'abduction', label: t('abduction', 'Abduction'), normalRange: '25°' },\n        { key: 'adduction', label: t('adduction', 'Adduction'), normalRange: '0°' }\n      ]\n    },\n    trunk: {\n      title: t('trunk', 'Trunk'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '80°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '30°' },\n        { key: 'lateralFlexionL', label: t('lateralFlexionLeft', 'Lateral Flexion (L)'), normalRange: '40°' },\n        { key: 'lateralFlexionR', label: t('lateralFlexionRight', 'Lateral Flexion (R)'), normalRange: '40°' },\n        { key: 'rotationL', label: t('rotationLeft', 'Rotation (L)'), normalRange: '45°' },\n        { key: 'rotationR', label: t('rotationRight', 'Rotation (R)'), normalRange: '45°' }\n      ]\n    },\n    hip: {\n      title: t('hip', 'Hip'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '120°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '30°' },\n        { key: 'abduction', label: t('abduction', 'Abduction'), normalRange: '45°' },\n        { key: 'adduction', label: t('adduction', 'Adduction'), normalRange: '30°' },\n        { key: 'internalRotation', label: t('internalRotation', 'Internal Rotation'), normalRange: '45°' },\n        { key: 'externalRotation', label: t('externalRotation', 'External Rotation'), normalRange: '45°' }\n      ]\n    },\n    knee: {\n      title: t('knee', 'Knee'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '135°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '0°' }\n      ]\n    },\n    ankle: {\n      title: t('ankle', 'Ankle'),\n      movements: [\n        { key: 'dorsiflexion', label: t('dorsiflexion', 'Dorsiflexion'), normalRange: '20°' },\n        { key: 'plantarflexion', label: t('plantarflexion', 'Plantarflexion'), normalRange: '50°' },\n        { key: 'inversion', label: t('inversion', 'Inversion'), normalRange: '35°' },\n        { key: 'eversion', label: t('eversion', 'Eversion'), normalRange: '15°' }\n      ]\n    },\n    thoracoLumbar: {\n      title: t('thoracoLumbar', 'Thoraco-Lumbar'),\n      movements: [\n        { key: 'flexion', label: t('flexion', 'Flexion'), normalRange: '80°' },\n        { key: 'extension', label: t('extension', 'Extension'), normalRange: '30°' },\n        { key: 'lateralFlexion', label: t('lateralFlexion', 'Lateral Flexion'), normalRange: '40°' },\n        { key: 'rotation', label: t('rotation', 'Rotation'), normalRange: '45°' }\n      ]\n    }\n  };\n\n  const fillNormalValues = (jointKey) => {\n    const joint = jointAssessments[jointKey];\n    const newData = { ...formData.neuromusculoskeletal };\n\n    if (!newData[jointKey]) newData[jointKey] = {};\n\n    joint.movements.forEach(movement => {\n      if (!newData[jointKey][movement.key]) {\n        newData[jointKey][movement.key] = {};\n      }\n\n      // Extract numeric value from normal range\n      const numericValue = movement.normalRange.replace('°', '');\n\n      newData[jointKey][movement.key] = {\n        arom: numericValue,\n        prom: numericValue,\n        muscleTestL: '5',\n        muscleTestR: '5',\n        muscleToneL: '2',\n        muscleToneR: '2'\n      };\n    });\n\n    updateFormData('neuromusculoskeletal', newData);\n  };\n\n  const renderJointAssessmentTable = (jointKey, joint) => (\n    <div key={jointKey} className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{joint.title}</h3>\n        <button\n          onClick={() => fillNormalValues(jointKey)}\n          className=\"px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors\"\n        >\n          {t('fillNormal', 'Fill Normal')}\n        </button>\n      </div>\n\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full border-collapse border border-gray-300 dark:border-gray-600\">\n          <thead>\n            <tr className=\"bg-gray-50 dark:bg-gray-700\">\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                {t('movement', 'Movement')}\n              </th>\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                {t('normalRange', 'Normal Range')}\n              </th>\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                AROM\n              </th>\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                PROM\n              </th>\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                {t('muscleTestL', 'Muscle Test (L)')}\n              </th>\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                {t('muscleTestR', 'Muscle Test (R)')}\n              </th>\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                {t('muscleToneL', 'Muscle Tone (L)')}\n              </th>\n              <th className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                {t('muscleToneR', 'Muscle Tone (R)')}\n              </th>\n            </tr>\n          </thead>\n          <tbody>\n            {joint.movements.map((movement) => (\n              <tr key={movement.key}>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm text-gray-900 dark:text-white\">\n                  {movement.label}\n                </td>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-3 py-2 text-center text-sm text-gray-600 dark:text-gray-400\">\n                  {movement.normalRange}\n                </td>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    max=\"180\"\n                    value={formData.neuromusculoskeletal[jointKey]?.[movement.key]?.arom || ''}\n                    onChange={(e) => handleInputChange(`${jointKey}.${movement.key}.arom`, e.target.value)}\n                    className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder=\"0-180\"\n                  />\n                </td>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    max=\"180\"\n                    value={formData.neuromusculoskeletal[jointKey]?.[movement.key]?.prom || ''}\n                    onChange={(e) => handleInputChange(`${jointKey}.${movement.key}.prom`, e.target.value)}\n                    className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder=\"0-180\"\n                  />\n                </td>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                  <select\n                    value={formData.neuromusculoskeletal[jointKey]?.[movement.key]?.muscleTestL || ''}\n                    onChange={(e) => handleInputChange(`${jointKey}.${movement.key}.muscleTestL`, e.target.value)}\n                    className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"\">{t('select', 'Select')}</option>\n                    {muscleTestGrades.map((grade) => (\n                      <option key={grade.value} value={grade.value}>\n                        {grade.label}\n                      </option>\n                    ))}\n                  </select>\n                </td>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                  <select\n                    value={formData.neuromusculoskeletal[jointKey]?.[movement.key]?.muscleTestR || ''}\n                    onChange={(e) => handleInputChange(`${jointKey}.${movement.key}.muscleTestR`, e.target.value)}\n                    className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"\">{t('select', 'Select')}</option>\n                    {muscleTestGrades.map((grade) => (\n                      <option key={grade.value} value={grade.value}>\n                        {grade.label}\n                      </option>\n                    ))}\n                  </select>\n                </td>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                  <select\n                    value={formData.neuromusculoskeletal[jointKey]?.[movement.key]?.muscleToneL || ''}\n                    onChange={(e) => handleInputChange(`${jointKey}.${movement.key}.muscleToneL`, e.target.value)}\n                    className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"\">{t('select', 'Select')}</option>\n                    {muscleToneGrades.map((grade) => (\n                      <option key={grade.value} value={grade.value}>\n                        {grade.label}\n                      </option>\n                    ))}\n                  </select>\n                </td>\n                <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                  <select\n                    value={formData.neuromusculoskeletal[jointKey]?.[movement.key]?.muscleToneR || ''}\n                    onChange={(e) => handleInputChange(`${jointKey}.${movement.key}.muscleToneR`, e.target.value)}\n                    className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"\">{t('select', 'Select')}</option>\n                    {muscleToneGrades.map((grade) => (\n                      <option key={grade.value} value={grade.value}>\n                        {grade.label}\n                      </option>\n                    ))}\n                  </select>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('neuromusculoskeletalMovementFunctions', 'Neuromusculoskeletal & Movement Functions')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('rangeOfMotionMuscleTestTone', 'Range of Motion, Muscle Test, and Muscle Tone assessments')}\n        </p>\n      </div>\n\n      {/* Assessment Parameters Legend */}\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n        <h3 className=\"text-lg font-medium text-blue-900 dark:text-blue-100 mb-4\">\n          {t('assessmentParameters', 'Assessment Parameters')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-3\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">AROM</h4>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('activeRangeOfMotion', 'Active Range of Motion')}</p>\n          </div>\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-3\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">PROM</h4>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('passiveRangeOfMotion', 'Passive Range of Motion')}</p>\n          </div>\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-3\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">{t('muscleTest', 'Muscle Test')}</h4>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('muscleStrengthScale', '0-5 Scale (Manual Muscle Testing)')}</p>\n          </div>\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-3\">\n            <h4 className=\"font-medium text-gray-900 dark:text-white\">{t('muscleTone', 'Muscle Tone')}</h4>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">{t('muscleToneScale', '0-4 Scale (Modified Ashworth)')}</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Upper Extremity Assessments */}\n      <div>\n        <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n          {t('upperExtremity', 'Upper Extremity')}\n        </h3>\n        <div className=\"space-y-6\">\n          {Object.entries(jointAssessments)\n            .filter(([key]) => ['neck', 'shoulder', 'elbow', 'forearm', 'wrist', 'fingers'].includes(key))\n            .map(([jointKey, joint]) => renderJointAssessmentTable(jointKey, joint))}\n        </div>\n      </div>\n\n      {/* Lower Extremity & Trunk Assessments */}\n      <div>\n        <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n          {t('lowerExtremityTrunk', 'Lower Extremity & Trunk')}\n        </h3>\n        <div className=\"space-y-6\">\n          {Object.entries(jointAssessments)\n            .filter(([key]) => ['trunk', 'hip', 'knee', 'ankle', 'thoracoLumbar'].includes(key))\n            .map(([jointKey, joint]) => renderJointAssessmentTable(jointKey, joint))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NeuromusculoskeletalPage;\n", "import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\n\nconst NeurodynamicsReflexesPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t } = useLanguage();\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData.neurodynamicsReflexes };\n\n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    updateFormData('neurodynamicsReflexes', newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleArrayChange = (field, value, checked) => {\n    const currentArray = formData.neurodynamicsReflexes[field] || [];\n    let newArray;\n\n    if (checked) {\n      newArray = [...currentArray, value];\n    } else {\n      newArray = currentArray.filter(item => item !== value);\n    }\n\n    handleInputChange(field, newArray);\n  };\n\n  // Neurodynamics tests\n  const neurodynamicsTests = [\n    { key: 'slr', label: 'SLR (Straight Leg Raise)' },\n    { key: 'slump', label: 'Slump Test' },\n    { key: 'pkb', label: 'PKB (Prone Knee Bend)' },\n    { key: 'ulnt1', label: 'ULNT1 (Median Nerve)' },\n    { key: 'ulnt2', label: 'ULNT2 (Median Nerve)' },\n    { key: 'ulnt3', label: 'ULNT3 (Radial Nerve)' },\n    { key: 'ulnt4', label: 'ULNT4 (Ulnar Nerve)' },\n    { key: 'others', label: t('others', 'Others') }\n  ];\n\n  // Reflex tests\n  const reflexTests = [\n    { key: 'btr', label: 'BTR (Biceps Tendon Reflex)' },\n    { key: 'ttr', label: 'TTR (Triceps Tendon Reflex)' },\n    { key: 'ktr', label: 'KTR (Knee Tendon Reflex)' },\n    { key: 'atr', label: 'ATR (Achilles Tendon Reflex)' },\n    { key: 'babinsky', label: 'Babinsky' },\n    { key: 'hoffmanns', label: \"Hoffmann's\" },\n    { key: 'clonus', label: 'Clonus' }\n  ];\n\n  // Status options\n  const statusOptions = [\n    { value: 'Normal', label: t('normal', 'Normal') },\n    { value: 'Impaired', label: t('impaired', 'Impaired') }\n  ];\n\n  // Reflex response options\n  const reflexOptions = [\n    { value: '+', label: '+' },\n    { value: '-', label: '-' },\n    { value: 'Hyper', label: t('hyper', 'Hyper') },\n    { value: 'Hypo', label: t('hypo', 'Hypo') }\n  ];\n\n  // Balance assessment options\n  const balanceTests = [\n    'FIST', 'Berg Balance Scale', 'TIMED UP & go'\n  ];\n\n  // Gait patterns\n  const gaitPatterns = [\n    'Antalgic Gait', 'Ataxic', 'Hemiplegic', 'Parkinsonian', 'Trendelenburg', 'Steppage', 'Waddling', 'None'\n  ];\n\n  // Mobility tests\n  const mobilityTests = [\n    '10 Meter Walk', 'Six-Minute Walk', 'FGA', 'Other Tools'\n  ];\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('neurodynamicsReflexesFunctionalEvaluation', 'Neurodynamics, Reflexes & Functional Evaluation')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('neurodynamicsReflexesGaitAssessment', 'Neurodynamics tests, reflexes, and functional gait assessment')}\n        </p>\n      </div>\n\n      {/* Neurodynamics Tests */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('neurodynamicsTests', 'Neurodynamics Tests')}\n        </h3>\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full border-collapse border border-gray-300 dark:border-gray-600\">\n            <thead>\n              <tr className=\"bg-gray-50 dark:bg-gray-700\">\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('test', 'Test')}\n                </th>\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('leftRange', 'L Range')}\n                </th>\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('leftSymptom', 'L Symptom')}\n                </th>\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('rightRange', 'R Range')}\n                </th>\n                <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('rightSymptom', 'R Symptom')}\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {neurodynamicsTests.map((test) => (\n                <tr key={test.key}>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-900 dark:text-white\">\n                    {test.label}\n                  </td>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                    <input\n                      type=\"text\"\n                      value={formData.neurodynamicsReflexes.neurodynamicsTests?.[test.key]?.lRange || ''}\n                      onChange={(e) => handleInputChange(`neurodynamicsTests.${test.key}.lRange`, e.target.value)}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                      placeholder={t('range', 'Range')}\n                    />\n                  </td>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                    <div className=\"flex space-x-2\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name={`${test.key}_lSymptom`}\n                          value=\"+\"\n                          checked={formData.neurodynamicsReflexes.neurodynamicsTests?.[test.key]?.lSymptom === '+'}\n                          onChange={(e) => handleInputChange(`neurodynamicsTests.${test.key}.lSymptom`, e.target.value)}\n                          className=\"mr-1\"\n                        />\n                        +\n                      </label>\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name={`${test.key}_lSymptom`}\n                          value=\"-\"\n                          checked={formData.neurodynamicsReflexes.neurodynamicsTests?.[test.key]?.lSymptom === '-'}\n                          onChange={(e) => handleInputChange(`neurodynamicsTests.${test.key}.lSymptom`, e.target.value)}\n                          className=\"mr-1\"\n                        />\n                        -\n                      </label>\n                    </div>\n                  </td>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                    <input\n                      type=\"text\"\n                      value={formData.neurodynamicsReflexes.neurodynamicsTests?.[test.key]?.rRange || ''}\n                      onChange={(e) => handleInputChange(`neurodynamicsTests.${test.key}.rRange`, e.target.value)}\n                      className=\"w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                      placeholder={t('range', 'Range')}\n                    />\n                  </td>\n                  <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                    <div className=\"flex space-x-2\">\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name={`${test.key}_rSymptom`}\n                          value=\"+\"\n                          checked={formData.neurodynamicsReflexes.neurodynamicsTests?.[test.key]?.rSymptom === '+'}\n                          onChange={(e) => handleInputChange(`neurodynamicsTests.${test.key}.rSymptom`, e.target.value)}\n                          className=\"mr-1\"\n                        />\n                        +\n                      </label>\n                      <label className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name={`${test.key}_rSymptom`}\n                          value=\"-\"\n                          checked={formData.neurodynamicsReflexes.neurodynamicsTests?.[test.key]?.rSymptom === '-'}\n                          onChange={(e) => handleInputChange(`neurodynamicsTests.${test.key}.rSymptom`, e.target.value)}\n                          className=\"mr-1\"\n                        />\n                        -\n                      </label>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Reflexes */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('reflexes', 'Reflexes')}\n        </h3>\n\n        {/* Overall Status */}\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('overallStatus', 'Overall Status')}\n          </label>\n          <div className=\"flex flex-wrap gap-4\">\n            {statusOptions.map((option) => (\n              <label key={option.value} className=\"flex items-center\">\n                <input\n                  type=\"radio\"\n                  name=\"reflexStatus\"\n                  value={option.value}\n                  checked={formData.neurodynamicsReflexes.reflexes?.status === option.value}\n                  onChange={(e) => handleInputChange('reflexes.status', e.target.value)}\n                  className=\"mr-2\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">{option.label}</span>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* Detailed Reflex Assessment */}\n        {formData.neurodynamicsReflexes.reflexes?.status === 'Impaired' && (\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full border-collapse border border-gray-300 dark:border-gray-600\">\n              <thead>\n                <tr className=\"bg-gray-50 dark:bg-gray-700\">\n                  <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-left text-sm font-medium text-gray-900 dark:text-white\">\n                    {t('reflex', 'Reflex')}\n                  </th>\n                  <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                    {t('right', 'Right')}\n                  </th>\n                  <th className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-center text-sm font-medium text-gray-900 dark:text-white\">\n                    {t('left', 'Left')}\n                  </th>\n                </tr>\n              </thead>\n              <tbody>\n                {reflexTests.map((reflex) => (\n                  <tr key={reflex.key}>\n                    <td className=\"border border-gray-300 dark:border-gray-600 px-4 py-2 text-sm text-gray-900 dark:text-white\">\n                      {reflex.label}\n                    </td>\n                    <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                      <div className=\"flex flex-wrap gap-2\">\n                        {reflexOptions.map((option) => (\n                          <label key={option.value} className=\"flex items-center\">\n                            <input\n                              type=\"checkbox\"\n                              checked={formData.neurodynamicsReflexes.reflexes?.assessments?.[reflex.key]?.right?.includes(option.value) || false}\n                              onChange={(e) => {\n                                const currentValues = formData.neurodynamicsReflexes.reflexes?.assessments?.[reflex.key]?.right || [];\n                                let newValues;\n                                if (e.target.checked) {\n                                  newValues = [...currentValues, option.value];\n                                } else {\n                                  newValues = currentValues.filter(item => item !== option.value);\n                                }\n                                handleInputChange(`reflexes.assessments.${reflex.key}.right`, newValues);\n                              }}\n                              className=\"mr-1\"\n                            />\n                            <span className=\"text-xs\">{option.label}</span>\n                          </label>\n                        ))}\n                      </div>\n                    </td>\n                    <td className=\"border border-gray-300 dark:border-gray-600 px-2 py-2\">\n                      <div className=\"flex flex-wrap gap-2\">\n                        {reflexOptions.map((option) => (\n                          <label key={option.value} className=\"flex items-center\">\n                            <input\n                              type=\"checkbox\"\n                              checked={formData.neurodynamicsReflexes.reflexes?.assessments?.[reflex.key]?.left?.includes(option.value) || false}\n                              onChange={(e) => {\n                                const currentValues = formData.neurodynamicsReflexes.reflexes?.assessments?.[reflex.key]?.left || [];\n                                let newValues;\n                                if (e.target.checked) {\n                                  newValues = [...currentValues, option.value];\n                                } else {\n                                  newValues = currentValues.filter(item => item !== option.value);\n                                }\n                                handleInputChange(`reflexes.assessments.${reflex.key}.left`, newValues);\n                              }}\n                              className=\"mr-1\"\n                            />\n                            <span className=\"text-xs\">{option.label}</span>\n                          </label>\n                        ))}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default NeurodynamicsReflexesPage;\n", "import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\n\nconst EquipmentICFPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('recommendedEquipmentICF', 'Recommended Equipment & ICF')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('assistiveTechnologyICFFramework', 'Assistive technology recommendations and ICF framework assessment')}\n        </p>\n      </div>\n\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n        <div className=\"flex items-center\">\n          <i className=\"fas fa-info-circle text-blue-500 mr-3\"></i>\n          <div>\n            <h3 className=\"text-lg font-medium text-blue-900 dark:text-blue-100\">\n              {t('pageInDevelopment', 'Page In Development')}\n            </h3>\n            <p className=\"text-blue-700 dark:text-blue-300 mt-1\">\n              {t('equipmentICFPageDescription', 'This page will include assistive technology recommendations and comprehensive ICF (International Classification of Functioning, Disability and Health) framework assessment.')}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Placeholder content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('recommendedEquipmentAssistiveTechnology', 'Recommended Equipment/Assistive Technology')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willIncludeEquipmentCategories', 'Will include equipment categories:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('walkingAids', 'Walking Aids')} (Axillary crutches, etc.)</li>\n            <li>{t('wheelchairs', 'Wheelchairs')} (Adult/Child, various types)</li>\n            <li>{t('standingFrames', 'Standing Frames')}</li>\n            <li>{t('lowerLimbProstheses', 'Lower Limb Prostheses')}</li>\n            <li>{t('upperLimbProstheses', 'Upper Limb Prostheses')}</li>\n            <li>{t('spinalOrthoses', 'Spinal Orthoses')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('icfFramework', 'ICF Framework')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willIncludeICFComponents', 'Will include ICF components:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('healthCondition', 'Health Condition')}</li>\n            <li>{t('bodyStructureFunction', 'Body Structure/Function (Impairment)')}</li>\n            <li>{t('limitationsOfActivity', 'Limitations of Activity')}</li>\n            <li>{t('restrictionOfParticipation', 'Restriction of Participation')}</li>\n            <li>{t('environmentalPersonalFactors', 'Environmental & Personal Factors')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('equipmentSpecifications', 'Equipment Specifications')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willIncludeSpecificationFields', 'Will include specification fields:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('usageStatus', 'Usage Status')} (Used/Not Used)</li>\n            <li>{t('technicalSpecifications', 'Technical Specifications')}</li>\n            <li>{t('recommendedUse', 'Recommended Use')}</li>\n            <li>{t('reviewedWithPhysician', 'Reviewed with Physician')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('environmentalFactors', 'Environmental Factors')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willAssessEnvironmentalFactors', 'Will assess environmental factors:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('physicalEnvironment', 'Physical Environment')}</li>\n            <li>{t('homeAssessment', 'Home Assessment')}</li>\n            <li>{t('transportation', 'Transportation')}</li>\n            <li>{t('workEnvironment', 'Work Environment')}</li>\n            <li>{t('socialSupport', 'Social Support')}</li>\n            <li>{t('attitudesOfOthers', 'Attitudes of Others')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 lg:col-span-2\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('icfAssessmentAreas', 'ICF Assessment Areas')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 dark:bg-blue-900/30 rounded-lg p-3\">\n                <h4 className=\"font-medium text-blue-900 dark:text-blue-100\">{t('bodyFunctions', 'Body Functions')}</h4>\n                <p className=\"text-sm text-blue-700 dark:text-blue-300\">{t('physiologicalFunctions', 'Physiological functions of body systems')}</p>\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"bg-green-100 dark:bg-green-900/30 rounded-lg p-3\">\n                <h4 className=\"font-medium text-green-900 dark:text-green-100\">{t('bodyStructures', 'Body Structures')}</h4>\n                <p className=\"text-sm text-green-700 dark:text-green-300\">{t('anatomicalParts', 'Anatomical parts of the body')}</p>\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"bg-yellow-100 dark:bg-yellow-900/30 rounded-lg p-3\">\n                <h4 className=\"font-medium text-yellow-900 dark:text-yellow-100\">{t('activities', 'Activities')}</h4>\n                <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">{t('executionOfTasks', 'Execution of tasks or actions')}</p>\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"bg-purple-100 dark:bg-purple-900/30 rounded-lg p-3\">\n                <h4 className=\"font-medium text-purple-900 dark:text-purple-100\">{t('participation', 'Participation')}</h4>\n                <p className=\"text-sm text-purple-700 dark:text-purple-300\">{t('involvementInLifeSituations', 'Involvement in life situations')}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EquipmentICFPage;\n", "import React from 'react';\nimport { useLanguage } from '../../../contexts/LanguageContext';\n\nconst SignaturesPage = ({ formData, updateFormData, errors, setErrors }) => {\n  const { t, isRTL } = useLanguage();\n\n  return (\n    <div className=\"p-6 space-y-8\">\n      <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {t('signaturesFinal', 'Signatures & Final')}\n        </h2>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n          {t('finalCommentsSignatures', 'Final comments, patient concerns, and professional signatures')}\n        </p>\n      </div>\n\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n        <div className=\"flex items-center\">\n          <i className=\"fas fa-info-circle text-blue-500 mr-3\"></i>\n          <div>\n            <h3 className=\"text-lg font-medium text-blue-900 dark:text-blue-100\">\n              {t('pageInDevelopment', 'Page In Development')}\n            </h3>\n            <p className=\"text-blue-700 dark:text-blue-300 mt-1\">\n              {t('signaturesPageDescription', 'This page will include therapist and physician signatures, area comments, patient concerns/expectations, and family preferences.')}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Placeholder content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('professionalSignatures', 'Professional Signatures')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willIncludeSignatureFields', 'Will include signature fields for:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('therapistSignature', 'Therapist Signature')} & {t('date', 'Date')}</li>\n            <li>{t('physicianSignature', 'Physician Signature')} & {t('date', 'Date')}</li>\n            <li>{t('digitalSignatureSupport', 'Digital signature support')}</li>\n            <li>{t('signatureValidation', 'Signature validation')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('areaComments', 'Area Comments')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willIncludeCommentFields', 'Will include comment fields for:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('generalComments', 'General comments')}</li>\n            <li>{t('clinicalObservations', 'Clinical observations')}</li>\n            <li>{t('recommendationsNotes', 'Recommendations and notes')}</li>\n            <li>{t('followUpInstructions', 'Follow-up instructions')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('patientConcernsExpectations', 'Patient Concerns/Expectations')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willCapturePatientInput', 'Will capture patient input on:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('mainConcerns', 'Main concerns')}</li>\n            <li>{t('treatmentExpectations', 'Treatment expectations')}</li>\n            <li>{t('functionalGoals', 'Functional goals')}</li>\n            <li>{t('qualityOfLifeGoals', 'Quality of life goals')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('patientFamilyPreferences', 'Patient & Family Preferences')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('willDocumentPreferences', 'Will document preferences for:')}\n          </p>\n          <ul className=\"list-disc list-inside text-gray-600 dark:text-gray-400 mt-2 space-y-1\">\n            <li>{t('treatmentApproach', 'Treatment approach')}</li>\n            <li>{t('schedulingPreferences', 'Scheduling preferences')}</li>\n            <li>{t('communicationPreferences', 'Communication preferences')}</li>\n            <li>{t('culturalConsiderations', 'Cultural considerations')}</li>\n          </ul>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 lg:col-span-2\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-3\">\n            {t('assessmentCompletion', 'Assessment Completion')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"text-center\">\n              <div className=\"bg-green-100 dark:bg-green-900/30 rounded-lg p-4\">\n                <i className=\"fas fa-check-circle text-green-500 text-2xl mb-2\"></i>\n                <h4 className=\"font-medium text-green-900 dark:text-green-100\">{t('validation', 'Validation')}</h4>\n                <p className=\"text-sm text-green-700 dark:text-green-300\">{t('formValidationChecks', 'Form validation and completeness checks')}</p>\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 dark:bg-blue-900/30 rounded-lg p-4\">\n                <i className=\"fas fa-file-pdf text-blue-500 text-2xl mb-2\"></i>\n                <h4 className=\"font-medium text-blue-900 dark:text-blue-100\">{t('pdfGeneration', 'PDF Generation')}</h4>\n                <p className=\"text-sm text-blue-700 dark:text-blue-300\">{t('generatePrintableAssessment', 'Generate printable assessment report')}</p>\n              </div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"bg-purple-100 dark:bg-purple-900/30 rounded-lg p-4\">\n                <i className=\"fas fa-save text-purple-500 text-2xl mb-2\"></i>\n                <h4 className=\"font-medium text-purple-900 dark:text-purple-100\">{t('dataStorage', 'Data Storage')}</h4>\n                <p className=\"text-sm text-purple-700 dark:text-purple-300\">{t('secureDataStorageBackup', 'Secure data storage and backup')}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Assessment Summary */}\n      <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('assessmentSummary', 'Assessment Summary')}\n        </h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n          <div>\n            <div className=\"text-2xl font-bold text-blue-600 dark:text-blue-400\">8</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('totalPages', 'Total Pages')}</div>\n          </div>\n          <div>\n            <div className=\"text-2xl font-bold text-green-600 dark:text-green-400\">200+</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('dataFields', 'Data Fields')}</div>\n          </div>\n          <div>\n            <div className=\"text-2xl font-bold text-yellow-600 dark:text-yellow-400\">15+</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('assessmentSections', 'Assessment Sections')}</div>\n          </div>\n          <div>\n            <div className=\"text-2xl font-bold text-purple-600 dark:text-purple-400\">CARF</div>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">{t('compliant', 'Compliant')}</div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SignaturesPage;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport toast from 'react-hot-toast';\n\n// Import individual page components\nimport PatientInformationPage from './Pages/PatientInformationPage';\nimport AssessmentReviewPage from './Pages/AssessmentReviewPage';\nimport MusculoskeletalExamPage from './Pages/MusculoskeletalExamPage';\nimport SensoryFunctionsPage from './Pages/SensoryFunctionsPage';\nimport NeuromusculoskeletalPage from './Pages/NeuromusculoskeletalPage';\nimport NeurodynamicsReflexesPage from './Pages/NeurodynamicsReflexesPage';\nimport EquipmentICFPage from './Pages/EquipmentICFPage';\nimport SignaturesPage from './Pages/SignaturesPage';\n\nconst PTAdultAssessmentForm = ({ patientId, patientData, fromPatientProfile, initialData = {}, onSave, onCancel }) => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const { patientId: urlPatientId, assessmentId } = useParams();\n  const [currentPage, setCurrentPage] = useState(1);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [patient, setPatient] = useState(null);\n\n  // Use patientId from props or URL params\n  const activePatientId = patientId || urlPatientId;\n\n  // Main form data state\n  const [formData, setFormData] = useState({\n    // Page 1: Patient Information\n    patientInfo: {\n      name: '',\n      sex: '',\n      maritalStatus: '',\n      patientId: '',\n      nationality: '',\n      age: '',\n      fileNumber: '',\n      dateOfEval: new Date().toISOString().split('T')[0],\n      time: new Date().toTimeString().slice(0, 5),\n      referredByHospital: '',\n      jobOccupation: [],\n      educationLevel: '',\n      livingCondition: {\n        typeOfResidence: '',\n        socialSupport: []\n      },\n      diagnosis: '',\n      icd10Code: '',\n      chiefComplaint: '',\n      associatedMedicalIssues: '',\n      historyOfTrauma: {\n        date: '',\n        circumstances: ''\n      },\n      previousSurgery: {\n        hasSurgery: false,\n        type: ''\n      },\n      radiologicalInvestigation: {\n        hasInvestigation: false,\n        findings: ''\n      },\n      previousRehabIntervention: {\n        hasIntervention: false,\n        details: ''\n      },\n      currentEquipment: {\n        hasEquipment: false,\n        details: ''\n      },\n      precautions: '',\n      pastMedicalHistory: [],\n      medications: '',\n      effectsSideEffects: '',\n      familyHistory: '',\n      allergies: '',\n      sourceOfInformation: []\n    },\n\n    // Page 2: Assessment and Review of Systems\n    assessmentReview: {\n      skinAppearance: {\n        status: '',\n        comments: ''\n      },\n      mentalFunctions: {\n        status: '',\n        comments: ''\n      },\n      visualScreening: {\n        status: '',\n        comments: ''\n      },\n      hearingFunction: {\n        status: '',\n        comments: ''\n      },\n      nutritionalRisk: [],\n      fallRisk: '',\n      psychosocialBehaviorRisk: '',\n      developmentalRisk: '',\n      communication: '',\n      rehabPotential: '',\n      vitalSigns: {\n        bp: '',\n        pr: '',\n        rr: ''\n      },\n      reviewOfSystems: {\n        respiratory: {\n          complaints: [],\n          explain: ''\n        },\n        neurology: {\n          complaints: [],\n          explain: ''\n        },\n        gastrointestinal: {\n          complaints: [],\n          explain: ''\n        },\n        genitourinary: {\n          complaints: [],\n          explain: ''\n        },\n        musculoskeletal: {\n          complaints: [],\n          explain: ''\n        },\n        hematology: {\n          complaints: [],\n          explain: ''\n        },\n        cardiovascular: {\n          complaints: [],\n          explain: ''\n        },\n        integumentary: {\n          complaints: [],\n          explain: ''\n        },\n        reproductive: {\n          complaints: [],\n          explain: ''\n        },\n        endocrine: {\n          complaints: [],\n          explain: ''\n        }\n      },\n      boneHealthScreening: {\n        conditions: [],\n        comments: ''\n      }\n    },\n\n    // Page 3: Musculoskeletal Examination and Pain\n    musculoskeletalExam: {\n      skinWarmthTurgor: {\n        status: '',\n        comments: ''\n      },\n      circulation: {\n        status: '',\n        comments: ''\n      },\n      edema: {\n        status: '',\n        grade: [],\n        comments: ''\n      },\n      softTissuePalpation: [],\n      woundPresence: {\n        hasWound: false,\n        types: [],\n        comments: ''\n      },\n      woundAssessment: {\n        needsAssessment: false,\n        onsetDate: '',\n        location: '',\n        classification: '',\n        clinicalImpression: '',\n        conclusion: ''\n      },\n      eligibility: {\n        status: '',\n        reasons: '',\n        alternativeServices: '',\n        recommendations: '',\n        referral: '',\n        precaution: '',\n        instructions: ''\n      },\n      treatmentFrequency: {\n        sessions: '',\n        timesPerWeek: '',\n        duration: '',\n        oneVisit: false,\n        anticipatedDischarge: ''\n      },\n      jointPathology: {\n        conditions: [],\n        jointIntegrity: [],\n        comments: '',\n        deformityTypes: '',\n        deformitySite: ''\n      },\n      painAssessment: {\n        hasPain: false,\n        painScore: '',\n        scale: '',\n        type: '',\n        location: [],\n        onset: '',\n        description: [],\n        frequency: '',\n        aggravatingFactors: [],\n        relievingFactors: [],\n        relievingDescription: '',\n        pattern: [],\n        associatedSymptoms: []\n      }\n    },\n\n    // Page 4: Sensory Functions and Coordination\n    sensoryFunctions: {\n      upperBodySensory: {\n        status: '',\n        assessments: {\n          pain: { left: '', right: '' },\n          lightTouch: { left: '', right: '' },\n          sharpDull: { left: '', right: '' },\n          temperature: { left: '', right: '' },\n          stereognosis: { left: '', right: '' },\n          proprioception: { left: '', right: '' }\n        }\n      },\n      lowerBodySensory: {\n        status: '',\n        assessments: {\n          pain: { left: '', right: '' },\n          lightTouch: { left: '', right: '' },\n          sharpDull: { left: '', right: '' },\n          temperature: { left: '', right: '' },\n          stereognosis: { left: '', right: '' },\n          proprioception: { left: '', right: '' }\n        }\n      },\n      coordinationVoluntaryMovement: {\n        status: '',\n        assessments: {\n          eyeContact: '',\n          eyeToHand: '',\n          fingerToNose: '',\n          heelToShin: '',\n          rapidAlternatingMovements: '',\n          dysdiadochokinesia: ''\n        },\n        comments: ''\n      }\n    },\n\n    // Page 5: Neuromusculoskeletal and Movement Functions\n    neuromusculoskeletal: {\n      neck: {\n        flexion: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        extension: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        lateralFlexionL: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        lateralFlexionR: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        rotationL: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        rotationR: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' }\n      },\n      shoulder: {\n        flexion: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        extension: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        abduction: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        adduction: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        internalRotation: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' },\n        externalRotation: { arom: '', prom: '', muscleTestL: '', muscleTestR: '', muscleToneL: '', muscleToneR: '' }\n      },\n      // Additional joints will be added in the component file\n    },\n\n    // Page 6: Neurodynamics, Reflexes, Functional Evaluation\n    neurodynamicsReflexes: {\n      neurodynamicsTests: {\n        slr: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' },\n        slump: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' },\n        pkb: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' },\n        ulnt1: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' },\n        ulnt2: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' },\n        ulnt3: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' },\n        ulnt4: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' },\n        others: { lRange: '', lSymptom: '', rRange: '', rSymptom: '' }\n      },\n      reflexes: {\n        status: '',\n        assessments: {\n          btr: { right: '', left: '' },\n          ttr: { right: '', left: '' },\n          ktr: { right: '', left: '' },\n          atr: { right: '', left: '' },\n          babinsky: { right: '', left: '' },\n          hoffmanns: { right: '', left: '' },\n          clonus: { right: '', left: '' }\n        }\n      },\n      posturalAssessment: {\n        status: '',\n        findings: [],\n        comments: ''\n      },\n      functionalMobility: {\n        status: '',\n        comments: ''\n      },\n      balanceAssessment: {\n        tests: [],\n        status: '',\n        score: '',\n        dynamicStatic: '',\n        comments: '',\n        fallRisk: ''\n      },\n      coordination: {\n        upperLimbs: {\n          status: '',\n          sides: [],\n          comments: ''\n        },\n        lowerLimbs: {\n          status: '',\n          sides: [],\n          comments: ''\n        }\n      },\n      gaitAnalysis: {\n        patterns: [],\n        mobilityTests: [],\n        score: '',\n        functionalQuality: {\n          safety: '',\n          cadence: '',\n          speed: '',\n          fatigue: ''\n        }\n      },\n      assessmentTools: {\n        fim: { score: '' },\n        oswestry: { score: '' },\n        lefs: { score: '' },\n        uefi: { score: '' },\n        kneeInjury: { score: '' },\n        others: { name: '', score: '' }\n      }\n    },\n\n    // Page 7: Recommended Equipment and ICF\n    equipmentICF: {\n      recommendedEquipment: {\n        usageStatus: '',\n        walkingAids: [],\n        wheelchairs: [],\n        other: [],\n        lowerLimbProstheses: [],\n        upperLimbProstheses: [],\n        spinalOrthoses: [],\n        technicalSpecifications: '',\n        recommendedUse: '',\n        reviewedWithPhysician: false\n      },\n      icf: {\n        healthCondition: '',\n        bodyStructureFunction: '',\n        limitationsOfActivity: ['', '', '', '', '', '', '', '', '', ''],\n        restrictionOfParticipation: ['', '', '', '', '', '', '', '', '', ''],\n        environmentalPersonalFactors: {\n          physicalEnvironment: { barrier: '', facilitator: '' },\n          homeAssessment: { barrier: '', facilitator: '' },\n          transportation: { barrier: '', facilitator: '' },\n          workEnvironment: { barrier: '', facilitator: '' },\n          socialSupport: { barrier: '', facilitator: '' },\n          attitudesOfOthers: { barrier: '', facilitator: '' },\n          personalFactors: { barrier: '', facilitator: '' }\n        }\n      }\n    },\n\n    // Page 8: Signatures and Final\n    signatures: {\n      therapistSignature: '',\n      therapistDate: '',\n      physicianSignature: '',\n      physicianDate: '',\n      areaComments: '',\n      patientConcerns: ['', '', ''],\n      patientFamilyPreferences: ''\n    }\n  });\n\n  // Load patient data - use provided patient data or fetch from API\n  useEffect(() => {\n    if (fromPatientProfile && patientData) {\n      // Use patient data passed from patient profile\n      setPatient(patientData);\n\n      // Pre-populate form with patient data\n      const updatedFormData = {\n        ...formData,\n        patientInfo: {\n          ...formData.patientInfo,\n          name: patientData.name || `${patientData.firstName} ${patientData.lastName}`,\n          sex: patientData.gender === 'male' ? 'M' : patientData.gender === 'female' ? 'F' : '',\n          patientId: patientData.nationalId || '',\n          nationality: patientData.nationality || '',\n          age: patientData.age?.toString() || '',\n          fileNumber: patientData._id || patientData.id || '',\n          diagnosis: patientData.medicalHistory?.primaryDiagnosis || '',\n          chiefComplaint: patientData.medicalHistory?.notes || ''\n        }\n      };\n\n      setFormData(updatedFormData);\n      setLoading(false);\n    } else if (activePatientId) {\n      // Fallback to mock data or API fetch if no patient data provided\n      setLoading(true);\n      setTimeout(() => {\n        const mockPatient = {\n          id: activePatientId,\n          name: 'أحمد محمد علي',\n          nameEn: 'Ahmed Mohammed Ali',\n          dateOfBirth: '2016-03-15',\n          age: 8,\n          gender: 'male',\n          nationalId: '**********',\n          phone: '+966 50 123 4567',\n          nationality: 'Saudi',\n          address: 'الرياض، المملكة العربية السعودية'\n        };\n\n        setPatient(mockPatient);\n\n        // Pre-populate form with patient data\n        setFormData(prevData => ({\n          ...prevData,\n          patientInfo: {\n            ...prevData.patientInfo,\n            name: mockPatient.nameEn,\n            patientId: mockPatient.id,\n            age: mockPatient.age.toString(),\n            sex: mockPatient.gender,\n            nationality: mockPatient.nationality\n          }\n        }));\n\n        setLoading(false);\n      }, 500);\n    }\n  }, [activePatientId, patientData, fromPatientProfile]);\n\n  // Initialize form data with any provided initial data\n  useEffect(() => {\n    if (initialData && Object.keys(initialData).length > 0) {\n      setFormData(prevData => ({\n        ...prevData,\n        ...initialData\n      }));\n    }\n  }, [initialData]);\n\n  // Page configuration\n  const pages = [\n    { id: 1, title: t('patientInformation', 'Patient Information'), component: PatientInformationPage },\n    { id: 2, title: t('assessmentReview', 'Assessment & Review of Systems'), component: AssessmentReviewPage },\n    { id: 3, title: t('musculoskeletalExam', 'Musculoskeletal Examination & Pain'), component: MusculoskeletalExamPage },\n    { id: 4, title: t('sensoryFunctions', 'Sensory Functions & Coordination'), component: SensoryFunctionsPage },\n    { id: 5, title: t('neuromusculoskeletal', 'Neuromusculoskeletal & Movement Functions'), component: NeuromusculoskeletalPage },\n    { id: 6, title: t('neurodynamicsReflexes', 'Neurodynamics, Reflexes & Functional Evaluation'), component: NeurodynamicsReflexesPage },\n    { id: 7, title: t('equipmentICF', 'Recommended Equipment & ICF'), component: EquipmentICFPage },\n    { id: 8, title: t('signatures', 'Signatures & Final'), component: SignaturesPage }\n  ];\n\n  // Validation functions\n  const validateCurrentPage = () => {\n    const newErrors = {};\n    \n    switch (currentPage) {\n      case 1:\n        if (!formData.patientInfo.name) newErrors.name = t('nameRequired', 'Name is required');\n        if (!formData.patientInfo.sex) newErrors.sex = t('sexRequired', 'Sex is required');\n        if (!formData.patientInfo.patientId) newErrors.patientId = t('patientIdRequired', 'Patient ID is required');\n        if (!formData.patientInfo.nationality) newErrors.nationality = t('nationalityRequired', 'Nationality is required');\n        if (!formData.patientInfo.age || formData.patientInfo.age < 18) newErrors.age = t('ageRequired', 'Age must be 18 or older');\n        if (!formData.patientInfo.diagnosis) newErrors.diagnosis = t('diagnosisRequired', 'Diagnosis is required');\n        if (!formData.patientInfo.chiefComplaint) newErrors.chiefComplaint = t('chiefComplaintRequired', 'Chief complaint is required');\n        break;\n      \n      case 2:\n        // Add validation for page 2 fields\n        break;\n      \n      // Add validation for other pages as needed\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  // Navigation functions\n  const handleNextPage = () => {\n    if (validateCurrentPage()) {\n      if (currentPage < pages.length) {\n        setCurrentPage(currentPage + 1);\n      }\n    } else {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before proceeding'));\n    }\n  };\n\n  const handlePreviousPage = () => {\n    if (currentPage > 1) {\n      setCurrentPage(currentPage - 1);\n    }\n  };\n\n  const handlePageClick = (pageNumber) => {\n    setCurrentPage(pageNumber);\n  };\n\n  // Form data update function\n  const updateFormData = (section, data) => {\n    setFormData(prevData => ({\n      ...prevData,\n      [section]: {\n        ...prevData[section],\n        ...data\n      }\n    }));\n  };\n\n  // Save form\n  const handleSave = async () => {\n    if (!validateCurrentPage()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before saving'));\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const assessmentData = {\n        ...formData,\n        patientId,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      if (onSave) {\n        await onSave(assessmentData);\n      }\n\n      toast.success(t('assessmentSaved', 'Assessment saved successfully'));\n      \n      // Navigate back or to patient details\n      if (onCancel) {\n        onCancel();\n      } else {\n        navigate('/patients');\n      }\n    } catch (error) {\n      console.error('Error saving assessment:', error);\n      toast.error(t('errorSaving', 'Error saving assessment'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Export to PDF\n  const handleExportPDF = () => {\n    // This will be implemented to generate a PDF version of the form\n    toast.info(t('pdfExportNotImplemented', 'PDF export functionality will be implemented'));\n  };\n\n  const CurrentPageComponent = pages[currentPage - 1]?.component;\n\n  return (\n    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('ptAdultInitialAssessment', 'PT Adult Initial Assessment Form')}\n                {activePatientId && patient && (\n                  <span className=\"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3\">\n                    - {patient.nameEn || patient.name}\n                  </span>\n                )}\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                {t('comprehensivePhysicalTherapyEvaluation', 'Comprehensive Physical Therapy Evaluation')}\n              </p>\n\n              {/* Compliance Badges */}\n              <div className=\"flex flex-wrap gap-2 mt-3\">\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n                  <i className=\"fas fa-certificate text-blue-600 dark:text-blue-400\"></i>\n                  <span className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">CARF Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full\">\n                  <i className=\"fas fa-shield-alt text-green-600 dark:text-green-400\"></i>\n                  <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">CBAHI Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n                  <i className=\"fas fa-lock text-purple-600 dark:text-purple-400\"></i>\n                  <span className=\"text-sm font-medium text-purple-800 dark:text-purple-200\">HIPAA Secure</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-orange-100 dark:bg-orange-900/30 rounded-full\">\n                  <i className=\"fas fa-universal-access text-orange-600 dark:text-orange-400\"></i>\n                  <span className=\"text-sm font-medium text-orange-800 dark:text-orange-200\">Accessible</span>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex space-x-3\">\n              {activePatientId && (\n                <button\n                  onClick={() => navigate(`/patients/${activePatientId}`)}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <i className=\"fas fa-user mr-2\"></i>\n                  {t('viewPatient', 'View Patient')}\n                </button>\n              )}\n              <button\n                onClick={handleExportPDF}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {t('exportPDF', 'Export PDF')}\n              </button>\n              <button\n                onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow mb-6 p-4\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {t('progress', 'Progress')}: {currentPage} / {pages.length}\n            </span>\n            <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {Math.round((currentPage / pages.length) * 100)}% {t('complete', 'Complete')}\n            </span>\n          </div>\n          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n            <div \n              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n              style={{ width: `${(currentPage / pages.length) * 100}%` }}\n            ></div>\n          </div>\n        </div>\n\n        {/* Page Navigation Tabs */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow mb-6\">\n          <div className=\"border-b border-gray-200 dark:border-gray-600\">\n            <nav className=\"-mb-px flex overflow-x-auto\">\n              {pages.map((page) => (\n                <button\n                  key={page.id}\n                  onClick={() => handlePageClick(page.id)}\n                  className={`py-4 px-6 text-sm font-medium whitespace-nowrap border-b-2 transition-colors ${\n                    currentPage === page.id\n                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                  }`}\n                >\n                  <span className=\"flex items-center space-x-2\">\n                    <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${\n                      currentPage === page.id\n                        ? 'bg-blue-500 text-white'\n                        : currentPage > page.id\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-300 text-gray-600'\n                    }`}>\n                      {currentPage > page.id ? '✓' : page.id}\n                    </span>\n                    <span>{page.title}</span>\n                  </span>\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Current Page Content */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n          {CurrentPageComponent && (\n            <CurrentPageComponent\n              formData={formData}\n              updateFormData={updateFormData}\n              errors={errors}\n              setErrors={setErrors}\n            />\n          )}\n        </div>\n\n        {/* Navigation Buttons */}\n        <div className=\"flex items-center justify-between mt-6\">\n          <button\n            onClick={handlePreviousPage}\n            disabled={currentPage === 1}\n            className={`px-6 py-2 rounded-lg transition-colors ${\n              currentPage === 1\n                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                : 'bg-gray-600 text-white hover:bg-gray-700'\n            }`}\n          >\n            <i className=\"fas fa-arrow-left mr-2\"></i>\n            {t('previous', 'Previous')}\n          </button>\n\n          <div className=\"flex space-x-3\">\n            <button\n              onClick={handleSave}\n              disabled={loading}\n              className=\"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50\"\n            >\n              {loading ? (\n                <>\n                  <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                  {t('saving', 'Saving...')}\n                </>\n              ) : (\n                <>\n                  <i className=\"fas fa-save mr-2\"></i>\n                  {t('save', 'Save')}\n                </>\n              )}\n            </button>\n\n            {currentPage < pages.length ? (\n              <button\n                onClick={handleNextPage}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                {t('next', 'Next')}\n                <i className=\"fas fa-arrow-right ml-2\"></i>\n              </button>\n            ) : (\n              <button\n                onClick={handleSave}\n                disabled={loading}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n              >\n                {loading ? (\n                  <>\n                    <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                    {t('completing', 'Completing...')}\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-check mr-2\"></i>\n                    {t('complete', 'Complete Assessment')}\n                  </>\n                )}\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PTAdultAssessmentForm;\n"], "names": ["_ref", "formData", "updateFormData", "errors", "setErrors", "t", "isRTL", "useLanguage", "handleInputChange", "field", "value", "newData", "_objectSpread", "patientInfo", "includes", "parent", "child", "split", "prev", "handleArrayChange", "checked", "currentArray", "newArray", "filter", "item", "_jsxs", "className", "children", "_jsx", "type", "name", "onChange", "e", "target", "concat", "placeholder", "sex", "maritalStatus", "patientId", "nationality", "min", "max", "age", "fileNumber", "dateOfEval", "time", "referredByHospital", "map", "option", "jobOccupation", "toLowerCase", "replace", "disabled", "educationLevel", "livingCondition", "typeOfResidence", "socialSupport", "diagnosis", "rows", "icd10Code", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "associatedMedicalIssues", "historyOfTrauma", "date", "circumstances", "previousSurgery", "hasSurgery", "radiologicalInvestigation", "hasInvestigation", "findings", "previousRehabIntervention", "hasIntervention", "details", "currentEquipment", "hasEquipment", "precautions", "pastMedicalHistory", "medications", "effectsSideEffects", "familyHistory", "allergies", "sourceOfInformation", "_formData$assessmentR0", "_formData$assessmentR1", "_formData$assessmentR10", "_formData$assessmentR14", "_formData$assessmentR15", "_formData$assessmentR17", "_formData$assessmentR18", "_formData$assessmentR19", "assessmentReview", "parts", "current", "i", "length", "handleReviewSystemChange", "system", "reviewOfSystems", "complaints", "explain", "currentComplaints", "statusOptions", "label", "riskLevelOptions", "communicationOptions", "rehabPotentialOptions", "psychosocialRiskOptions", "developmentalRiskOptions", "reviewOfSystemsOptions", "respiratory", "neurology", "gastrointestinal", "genitourinary", "musculoskeletal", "hematology", "cardiovascular", "integumentary", "reproductive", "endocrine", "renderAssessmentSection", "title", "_formData$assessmentR2", "_formData$assessmentR3", "showComments", "arguments", "undefined", "_formData$assessmentR", "status", "comments", "renderReviewOfSystemsSection", "systemName", "systemKey", "_reviewOfSystemsOptio", "_formData$assessmentR6", "_formData$assessmentR7", "_formData$assessmentR8", "complaint", "_formData$assessmentR4", "_formData$assessmentR5", "some", "c", "_formData$assessmentR9", "nutritionalRisk", "fallRisk", "psychosocialBehaviorRisk", "developmentalRisk", "communication", "rehabPotential", "vitalSigns", "bp", "pr", "rr", "_formData$assessmentR11", "_formData$assessmentR12", "boneHealthScreening", "conditions", "_formData$assessmentR13", "currentConditions", "newConditions", "_formData$assessmentR16", "<PERSON><PERSON><PERSON><PERSON>", "onAreaSelect", "showLegend", "svgRef", "useRef", "hoveredArea", "setHoveredArea", "useState", "bodyAreas", "head", "color", "path", "neck", "leftShoulder", "rightShoulder", "leftArm", "rightArm", "leftForearm", "rightForearm", "leftHand", "rightHand", "chest", "abdomen", "pelvis", "leftThigh", "rightThigh", "leftKnee", "<PERSON><PERSON><PERSON>", "leftLeg", "rightLeg", "leftFoot", "rightFoot", "upperBack", "lowerBack", "handleAreaClick", "areaKey", "newSelectedAreas", "area", "isSelected", "ref", "width", "height", "viewBox", "d", "fill", "stroke", "strokeWidth", "Object", "entries", "_ref2", "key", "_ref3", "opacity", "onClick", "onMouseEnter", "onMouseLeave", "x", "y", "textAnchor", "_ref4", "_ref5", "_bodyAreas$areaKey", "_formData$musculoskel6", "_formData$musculoskel0", "_formData$musculoskel10", "_formData$musculoskel11", "_formData$musculoskel12", "_formData$musculoskel16", "_formData$musculoskel17", "_formData$musculoskel18", "_formData$musculoskel19", "_formData$musculoskel20", "_formData$musculoskel21", "_formData$musculoskel23", "_formData$musculoskel35", "musculoskeletalExam", "circulationOptions", "edemaOptions", "painScaleOptions", "painTypeOptions", "_formData$musculoskel2", "_formData$musculoskel3", "_formData$musculoskel4", "options", "_formData$musculoskel", "_formData$musculoskel5", "edema", "grade", "_formData$musculoskel7", "_formData$musculoskel8", "_formData$musculoskel9", "currentGrades", "newGrades", "_formData$musculoskel1", "softTissuePalpation", "woundPresence", "hasWound", "_formData$musculoskel13", "_formData$musculoskel14", "types", "_formData$musculoskel15", "currentTypes", "newTypes", "painAssessment", "<PERSON><PERSON><PERSON>", "painScore", "scale", "_formData$musculoskel22", "BodyMap", "bodyMapAreas", "areas", "_formData$musculoskel24", "onset", "_formData$musculoskel25", "frequency", "description", "_formData$musculoskel26", "_formData$musculoskel27", "_formData$musculoskel28", "currentDescriptions", "newDescriptions", "factor", "_formData$musculoskel29", "_formData$musculoskel30", "aggravatingFactors", "_formData$musculoskel31", "currentFactors", "newFactors", "_formData$musculoskel32", "_formData$musculoskel33", "relievingFactors", "_formData$musculoskel34", "relievingDescription", "_formData$sensoryFunc1", "_formData$sensoryFunc10", "_formData$sensoryFunc13", "sensoryFunctions", "coordinationOptions", "sensoryTypes", "coordinationTests", "renderSensoryAssessmentTable", "bodyPart", "_formData$sensoryFunc2", "_formData$sensoryFunc3", "_formData$sensoryFunc", "_formData$sensoryFunc4", "_formData$sensoryFunc5", "_formData$sensoryFunc6", "_formData$sensoryFunc7", "_formData$sensoryFunc8", "_formData$sensoryFunc9", "assessments", "left", "right", "_formData$sensoryFunc0", "coordinationVoluntaryMovement", "test", "_formData$sensoryFunc11", "_formData$sensoryFunc12", "neuromusculoskeletal", "muscleTestGrades", "muscleToneGrades", "jointAssessments", "movements", "normalRange", "shoulder", "elbow", "forearm", "wrist", "fingers", "trunk", "hip", "knee", "ankle", "thoracoLumbar", "renderJointAssessmentTable", "jointKey", "joint", "for<PERSON>ach", "movement", "numericValue", "arom", "prom", "muscleTestL", "muscleTestR", "muscleToneL", "muscleToneR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_formData$neuromuscul", "_formData$neuromuscul2", "_formData$neuromuscul3", "_formData$neuromuscul4", "_formData$neuromuscul5", "_formData$neuromuscul6", "_formData$neuromuscul7", "_formData$neuromuscul8", "_formData$neuromuscul9", "_formData$neuromuscul0", "_formData$neuromuscul1", "_formData$neuromuscul10", "_formData$neurodynami12", "neurodynamicsReflexes", "neurodynamicsTests", "reflexOptions", "_formData$neurodynami", "_formData$neurodynami2", "_formData$neurodynami3", "_formData$neurodynami4", "_formData$neurodynami5", "_formData$neurodynami6", "_formData$neurodynami7", "_formData$neurodynami8", "_formData$neurodynami9", "_formData$neurodynami0", "_formData$neurodynami1", "_formData$neurodynami10", "lRange", "lSymptom", "rRange", "rSymptom", "_formData$neurodynami11", "reflexes", "reflex", "_formData$neurodynami13", "_formData$neurodynami14", "_formData$neurodynami15", "_formData$neurodynami16", "_formData$neurodynami17", "_formData$neurodynami18", "_formData$neurodynami19", "currentV<PERSON>ues", "newValues", "_formData$neurodynami20", "_formData$neurodynami21", "_formData$neurodynami22", "_formData$neurodynami23", "_formData$neurodynami24", "_formData$neurodynami25", "_formData$neurodynami26", "_pages", "patientData", "fromPatientProfile", "initialData", "onSave", "onCancel", "navigate", "useNavigate", "urlPatientId", "assessmentId", "useParams", "currentPage", "setCurrentPage", "loading", "setLoading", "patient", "setPatient", "activePatientId", "setFormData", "Date", "toISOString", "toTimeString", "slice", "skinAppearance", "mentalFunctions", "visualScreening", "hearingFunction", "skinWarmthTurgor", "circulation", "woundAssessment", "needsAssessment", "onsetDate", "location", "classification", "clinicalImpression", "conclusion", "eligibility", "reasons", "alternativeServices", "recommendations", "referral", "precaution", "instructions", "treatmentFrequency", "sessions", "timesPerWeek", "duration", "oneVisit", "anticipated<PERSON><PERSON><PERSON>ge", "jointPathology", "jointIntegrity", "deformityTypes", "deformitySite", "pattern", "associatedSymptoms", "upperBodySensory", "pain", "lightTouch", "sharpDull", "temperature", "stereognosis", "proprioception", "lowerBodySensory", "eyeContact", "eyeToHand", "fingerToNose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rapidAlternatingMovements", "dysdiadochokinesia", "flexion", "extension", "lateralFlexionL", "lateralFlexionR", "rotationL", "rotationR", "abduction", "adduction", "internalRotation", "externalRotation", "slr", "slump", "pkb", "ulnt1", "ulnt2", "ulnt3", "ulnt4", "others", "btr", "ttr", "ktr", "atr", "<PERSON><PERSON><PERSON>", "ho<PERSON><PERSON><PERSON>", "clonus", "posturalAssessment", "functionalMobility", "balanceAssessment", "tests", "score", "dynamicStatic", "coordination", "upperLimbs", "sides", "lowerLimbs", "gaitAnalysis", "patterns", "mobilityTests", "functionalQuality", "safety", "cadence", "speed", "fatigue", "assessmentTools", "fim", "oswestry", "lefs", "uefi", "kneeInjury", "equipmentICF", "recommendedEquipment", "usageStatus", "walkingAids", "wheelchairs", "other", "lowerLimbProstheses", "upperLimbProstheses", "spinalOrthoses", "technicalSpecifications", "recommendedUse", "reviewedWithPhysician", "icf", "healthCondition", "bodyStructureFunction", "limitationsOfActivity", "restrictionOfParticipation", "environmentalPersonalFactors", "physicalEnvironment", "barrier", "facilitator", "homeAssessment", "transportation", "workEnvironment", "attitudesOfOthers", "personalFactors", "signatures", "therapistSignature", "therapistDate", "physicianSignature", "physicianDate", "areaComments", "patientConcerns", "patientFamilyPreferences", "useEffect", "_patientData$age", "_patientData$medicalH", "_patientData$medicalH2", "updatedFormData", "firstName", "lastName", "gender", "nationalId", "toString", "_id", "id", "medicalHistory", "primaryDiagnosis", "notes", "setTimeout", "mockPatient", "nameEn", "dateOfBirth", "phone", "address", "prevData", "keys", "pages", "component", "PatientInformationPage", "AssessmentReviewPage", "MusculoskeletalExamPage", "SensoryFunctionsPage", "NeuromusculoskeletalPage", "NeurodynamicsReflexesPage", "EquipmentICFPage", "SignaturesPage", "validateCurrentPage", "newErrors", "handleSave", "async", "assessmentData", "createdAt", "updatedAt", "toast", "success", "error", "console", "CurrentPageComponent", "handleExportPDF", "info", "Math", "round", "style", "page", "handlePageClick", "pageNumber", "section", "data", "handlePreviousPage", "_Fragment", "handleNextPage"], "sourceRoot": ""}