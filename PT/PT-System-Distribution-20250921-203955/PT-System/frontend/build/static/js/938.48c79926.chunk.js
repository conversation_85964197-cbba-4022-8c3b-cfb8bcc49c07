/*! For license information please see 938.48c79926.chunk.js.LICENSE.txt */
(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[938],{14:t=>{t.exports=function(){return!1}},61:t=>{t.exports=function(t,e){return t<e}},108:(t,e,r)=>{"use strict";r.d(e,{u:()=>v});var n=r(8387),o=r(5043),i=r(9889),a=r.n(i),c=r(6307),u=r(155),l=r(240);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return y(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var v=(0,o.forwardRef)(function(t,e){var r=t.aspect,i=t.initialDimension,s=void 0===i?{width:-1,height:-1}:i,f=t.width,h=void 0===f?"100%":f,y=t.height,v=void 0===y?"100%":y,m=t.minWidth,b=void 0===m?0:m,g=t.minHeight,x=t.maxHeight,w=t.children,O=t.debounce,j=void 0===O?0:O,A=t.id,S=t.className,P=t.onResize,E=t.style,k=void 0===E?{}:E,M=(0,o.useRef)(null),T=(0,o.useRef)();T.current=P,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var _=d((0,o.useState)({containerWidth:s.width,containerHeight:s.height}),2),C=_[0],I=_[1],D=(0,o.useCallback)(function(t,e){I(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;D(n,o),null===(e=T.current)||void 0===e||e.call(T,n,o)};j>0&&(t=a()(t,j,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=M.current.getBoundingClientRect(),n=r.width,o=r.height;return D(n,o),e.observe(M.current),function(){e.disconnect()}},[D,j]);var N=(0,o.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,u.R)((0,c._3)(h)||(0,c._3)(v),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",h,v),(0,u.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,c._3)(h)?t:h,i=(0,c._3)(v)?e:v;r&&r>0&&(n?i=n/r:i&&(n=i*r),x&&i>x&&(i=x)),(0,u.R)(n>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,i,h,v,b,g,r);var a=!Array.isArray(w)&&(0,l.Mn)(w.type).endsWith("Chart");return o.Children.map(w,function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:n,height:i},a?{style:p({height:"100%",width:"100%",maxHeight:i,maxWidth:n},t.props.style)}:{})):t})},[r,w,v,x,g,b,C,h]);return o.createElement("div",{id:A?"".concat(A):void 0,className:(0,n.A)("recharts-responsive-container",S),style:p(p({},k),{},{width:h,height:v,minWidth:b,minHeight:g,maxHeight:x}),ref:M},N)})},143:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},149:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},155:(t,e,r)=>{"use strict";r.d(e,{R:()=>n});var n=function(t,e){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},163:(t,e,r)=>{"use strict";r.d(e,{r:()=>nt});var n=r(8420),o=r(5043),i=r(1629),a=r.n(i),c=r(8387),u=r(1639),l=r(8892),s=r(240),f=["points","className","baseLinePoints","connectNulls"];function p(){return p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},p.apply(this,arguments)}function h(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function d(t){return function(t){if(Array.isArray(t))return y(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return y(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var v=function(t){return t&&t.x===+t.x&&t.y===+t.y},m=function(t,e){var r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){v(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),v(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e}(t);e&&(r=[r.reduce(function(t,e){return[].concat(d(t),d(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},b=function(t){var e=t.points,r=t.className,n=t.baseLinePoints,i=t.connectNulls,a=h(t,f);if(!e||!e.length)return null;var u=(0,c.A)("recharts-polygon",r);if(n&&n.length){var l=a.stroke&&"none"!==a.stroke,d=function(t,e,r){var n=m(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(m(e.reverse(),r).slice(1))}(e,n,i);return o.createElement("g",{className:u},o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"Z"===d.slice(-1)?a.fill:"none",stroke:"none",d:d})),l?o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"none",d:m(e,i)})):null,l?o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"none",d:m(n,i)})):null)}var y=m(e,i);return o.createElement("path",p({},(0,s.J9)(a,!0),{fill:"Z"===y.slice(-1)?a.fill:"none",className:u,d:y}))},g=r(4140),x=r(7287),w=r(165);function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function E(t,e,r){return e=M(e),function(t,e){if(e&&("object"===O(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,k()?Reflect.construct(e,r||[],M(t).constructor):e.apply(t,r))}function k(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(k=function(){return!!t})()}function M(t){return M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},M(t)}function T(t,e){return T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},T(t,e)}function _(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}var I=Math.PI/180,D=1e-5,N=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),E(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}(e,t),r=e,i=[{key:"renderTickItem",value:function(t,e,r){return o.isValidElement(t)?o.cloneElement(t,e):a()(t)?t(e):o.createElement(g.E,j({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],(n=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize||8,c=(0,w.IZ)(r,n,o,t.coordinate),u=(0,w.IZ)(r,n,o+("inner"===i?-1:1)*a,t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*I);return r>D?"outer"===e?"start":"end":r<-D?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.radius,i=t.axisLine,a=t.axisLineType,c=S(S({},(0,s.J9)(this.props,!1)),{},{fill:"none"},(0,s.J9)(i,!1));if("circle"===a)return o.createElement(l.c,j({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:r,r:n}));var u=this.props.ticks.map(function(t){return(0,w.IZ)(e,r,n,t.coordinate)});return o.createElement(b,j({className:"recharts-polar-angle-axis-line"},c,{points:u}))}},{key:"renderTicks",value:function(){var t=this,r=this.props,n=r.ticks,i=r.tick,a=r.tickLine,l=r.tickFormatter,f=r.stroke,p=(0,s.J9)(this.props,!1),h=(0,s.J9)(i,!1),d=S(S({},p),{},{fill:"none"},(0,s.J9)(a,!1)),y=n.map(function(r,n){var s=t.getTickLineCoord(r),y=S(S(S({textAnchor:t.getTickTextAnchor(r)},p),{},{stroke:"none",fill:f},h),{},{index:n,payload:r,x:s.x2,y:s.y2});return o.createElement(u.W,j({className:(0,c.A)("recharts-polar-angle-axis-tick",(0,w.Zk)(i)),key:"tick-".concat(r.coordinate)},(0,x.XC)(t.props,r,n)),a&&o.createElement("line",j({className:"recharts-polar-angle-axis-tick-line"},d,s)),i&&e.renderTickItem(i,y,l?l(r.value,n):r.value))});return o.createElement(u.W,{className:"recharts-polar-angle-axis-ticks"},y)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,n=t.axisLine;return r<=0||!e||!e.length?null:o.createElement(u.W,{className:(0,c.A)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks())}}])&&P(r.prototype,n),i&&P(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(o.PureComponent);_(N,"displayName","PolarAngleAxis"),_(N,"axisType","angleAxis"),_(N,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var B=r(2794),R=r.n(B),L=r(9364),z=r.n(L),F=r(2647),U=["cx","cy","angle","ticks","axisLine"],W=["ticks","tick","angle","tickFormatter","stroke"];function q(t){return q="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},q(t)}function X(){return X=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},X.apply(this,arguments)}function H(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function V(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?H(Object(r),!0).forEach(function(e){Q(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function K(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function G(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tt(n.key),n)}}function J(t,e,r){return e=Z(e),function(t,e){if(e&&("object"===q(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Y()?Reflect.construct(e,r||[],Z(t).constructor):e.apply(t,r))}function Y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Y=function(){return!!t})()}function Z(t){return Z=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Z(t)}function $(t,e){return $=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},$(t,e)}function Q(t,e,r){return(e=tt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tt(t){var e=function(t,e){if("object"!=q(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=q(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==q(e)?e:e+""}var et=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),J(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$(t,e)}(e,t),r=e,i=[{key:"renderTickItem",value:function(t,e,r){return o.isValidElement(t)?o.cloneElement(t,e):a()(t)?t(e):o.createElement(g.E,X({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],(n=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,o=r.cx,i=r.cy;return(0,w.IZ)(o,i,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=R()(o,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:z()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,i=t.ticks,a=t.axisLine,c=K(t,U),u=i.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),l=(0,w.IZ)(e,r,u[0],n),f=(0,w.IZ)(e,r,u[1],n),p=V(V(V({},(0,s.J9)(c,!1)),{},{fill:"none"},(0,s.J9)(a,!1)),{},{x1:l.x,y1:l.y,x2:f.x,y2:f.y});return o.createElement("line",X({className:"recharts-polar-radius-axis-line"},p))}},{key:"renderTicks",value:function(){var t=this,r=this.props,n=r.ticks,i=r.tick,a=r.angle,l=r.tickFormatter,f=r.stroke,p=K(r,W),h=this.getTickTextAnchor(),d=(0,s.J9)(p,!1),y=(0,s.J9)(i,!1),v=n.map(function(r,n){var s=t.getTickValueCoord(r),p=V(V(V(V({textAnchor:h,transform:"rotate(".concat(90-a,", ").concat(s.x,", ").concat(s.y,")")},d),{},{stroke:"none",fill:f},y),{},{index:n},s),{},{payload:r});return o.createElement(u.W,X({className:(0,c.A)("recharts-polar-radius-axis-tick",(0,w.Zk)(i)),key:"tick-".concat(r.coordinate)},(0,x.XC)(t.props,r,n)),e.renderTickItem(i,p,l?l(r.value,n):r.value))});return o.createElement(u.W,{className:"recharts-polar-radius-axis-ticks"},v)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,n=t.tick;return e&&e.length?o.createElement(u.W,{className:(0,c.A)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),F.J.renderCallByParent(this.props,this.getViewBox())):null}}])&&G(r.prototype,n),i&&G(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(o.PureComponent);Q(et,"displayName","PolarRadiusAxis"),Q(et,"axisType","radiusAxis"),Q(et,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var rt=r(4240),nt=(0,n.gu)({chartName:"PieChart",GraphicalChild:rt.F,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:N},{axisType:"radiusAxis",AxisComp:et}],formatAxisMap:w.pr,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}})},165:(t,e,r)=>{"use strict";r.d(e,{IZ:()=>b,Kg:()=>v,Zk:()=>A,lY:()=>g,pr:()=>x,yy:()=>j});var n=r(9686),o=r.n(n),i=r(5043),a=r(1629),c=r.n(a),u=r(6307),l=r(202);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return y(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var v=Math.PI/180,m=function(t){return 180*t/Math.PI},b=function(t,e,r,n){return{x:t+Math.cos(-v*n)*r,y:e+Math.sin(-v*n)*r}},g=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},x=function(t,e,r,n,i){var a=t.width,c=t.height,s=t.startAngle,f=t.endAngle,y=(0,u.F4)(t.cx,a,a/2),v=(0,u.F4)(t.cy,c,c/2),m=g(a,c,r),b=(0,u.F4)(t.innerRadius,m,0),x=(0,u.F4)(t.outerRadius,m,.8*m);return Object.keys(e).reduce(function(t,r){var a,c=e[r],u=c.domain,m=c.reversed;if(o()(c.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[b,x]),m&&(a=[a[1],a[0]]);else{var g=d(a=c.range,2);s=g[0],f=g[1]}var w=(0,l.W7)(c,i),O=w.realScaleType,j=w.scale;j.domain(u).range(a),(0,l.YB)(j);var A=(0,l.w7)(j,p(p({},c),{},{realScaleType:O})),S=p(p(p({},c),A),{},{range:a,radius:x,realScaleType:O,scale:j,cx:y,cy:v,innerRadius:b,outerRadius:x,startAngle:s,endAngle:f});return p(p({},t),{},h({},r,S))},{})},w=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return Math.sqrt(Math.pow(r-o,2)+Math.pow(n-i,2))}({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=(r-o)/a,u=Math.acos(c);return n>i&&(u=2*Math.PI-u),{radius:a,angle:m(u),angleInRadian:u}},O=function(t,e){var r=e.startAngle,n=e.endAngle,o=Math.floor(r/360),i=Math.floor(n/360);return t+360*Math.min(o,i)},j=function(t,e){var r=t.x,n=t.y,o=w({x:r,y:n},e),i=o.radius,a=o.angle,c=e.innerRadius,u=e.outerRadius;if(i<c||i>u)return!1;if(0===i)return!0;var l,s=function(t){var e=t.startAngle,r=t.endAngle,n=Math.floor(e/360),o=Math.floor(r/360),i=Math.min(n,o);return{startAngle:e-360*i,endAngle:r-360*i}}(e),f=s.startAngle,h=s.endAngle,d=a;if(f<=h){for(;d>h;)d-=360;for(;d<f;)d+=360;l=d>=f&&d<=h}else{for(;d>f;)d-=360;for(;d<h;)d+=360;l=d>=h&&d<=f}return l?p(p({},e),{},{radius:i,angle:O(d,e)}):null},A=function(t){return(0,i.isValidElement)(t)||c()(t)||"boolean"===typeof t?"":t.className}},168:(t,e,r)=>{"use strict";r.d(e,{N:()=>R});var n=r(5043),o=r(1744),i=r(1629),a=r.n(i),c=r(9686),u=r.n(c),l=r(9853),s=r.n(l),f=r(8387),p=r(8471),h=r(8892),d=r(1639),y=r(1519),v=r(8813),m=r(6307),b=r(240),g=r(6015),x=r(202),w=["type","layout","connectNulls","ref"],O=["key"];function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function A(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function S(){return S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},S.apply(this,arguments)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function E(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){N(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function k(t){return function(t){if(Array.isArray(t))return M(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return M(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return M(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function T(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,B(n.key),n)}}function _(t,e,r){return e=I(e),function(t,e){if(e&&("object"===j(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,C()?Reflect.construct(e,r||[],I(t).constructor):e.apply(t,r))}function C(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(C=function(){return!!t})()}function I(t){return I=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},I(t)}function D(t,e){return D=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},D(t,e)}function N(t,e,r){return(e=B(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function B(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}var R=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return N(t=_(this,e,[].concat(n)),"state",{isAnimationFinished:!0,totalLength:0}),N(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),N(t,"getStrokeDasharray",function(r,n,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(n,r);for(var a=Math.floor(r/i),c=r%i,u=n-r,l=[],s=0,f=0;s<o.length;f+=o[s],++s)if(f+o[s]>c){l=[].concat(k(o.slice(0,s)),[c-f]);break}var p=l.length%2===0?[0,u]:[u];return[].concat(k(e.repeat(o,a)),k(l),p).map(function(t){return"".concat(t,"px")}).join(", ")}),N(t,"id",(0,m.NF)("recharts-line-")),N(t,"pathRef",function(e){t.mainCurve=e}),N(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),N(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&D(t,e)}(e,t),r=e,c=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!==0?[].concat(k(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(k(n),k(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(a()(t))r=t(e);else{var o=e.key,i=A(e,O),c=(0,f.A)("recharts-line-dot","boolean"!==typeof t?t.className:"");r=n.createElement(h.c,S({key:o},i,{className:c}))}return r}}],(i=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.points,i=r.xAxis,a=r.yAxis,c=r.layout,u=r.children,l=(0,b.aS)(u,v.u);if(!l)return null;var s=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:(0,x.kr)(t.payload,e)}},f={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(d.W,f,l.map(function(t){return n.cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,dataPointFormatter:s})}))}},{key:"renderDots",value:function(t,r,o){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,a=i.dot,c=i.points,u=i.dataKey,l=(0,b.J9)(this.props,!1),s=(0,b.J9)(a,!0),f=c.map(function(t,r){var n=E(E(E({key:"dot-".concat(r),r:3},l),s),{},{index:r,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return e.renderDotItem(a,n)}),p={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(o,")"):null};return n.createElement(d.W,S({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,o){var i=this.props,a=i.type,c=i.layout,u=i.connectNulls,l=(i.ref,A(i,w)),s=E(E(E({},(0,b.J9)(l,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},o),{},{type:a,layout:c,connectNulls:u});return n.createElement(p.I,S({},s,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,i=this.props,a=i.points,c=i.strokeDasharray,u=i.isAnimationActive,l=i.animationBegin,s=i.animationDuration,f=i.animationEasing,p=i.animationId,h=i.animateNewValues,d=i.width,y=i.height,v=this.state,b=v.prevPoints,g=v.totalLength;return n.createElement(o.Ay,{begin:l,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"line-".concat(p),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var o=n.t;if(b){var i=b.length/a.length,u=a.map(function(t,e){var r=Math.floor(e*i);if(b[r]){var n=b[r],a=(0,m.Dj)(n.x,t.x),c=(0,m.Dj)(n.y,t.y);return E(E({},t),{},{x:a(o),y:c(o)})}if(h){var u=(0,m.Dj)(2*d,t.x),l=(0,m.Dj)(y/2,t.y);return E(E({},t),{},{x:u(o),y:l(o)})}return E(E({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(u,t,e)}var l,s=(0,m.Dj)(0,g)(o);if(c){var f="".concat(c).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});l=r.getStrokeDasharray(s,g,f)}else l=r.generateSimpleStrokeDasharray(g,s);return r.renderCurveStatically(a,t,e,{strokeDasharray:l})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!s()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,o=e.dot,i=e.points,a=e.className,c=e.xAxis,l=e.yAxis,s=e.top,p=e.left,h=e.width,v=e.height,m=e.isAnimationActive,g=e.id;if(r||!i||!i.length)return null;var x=this.state.isAnimationFinished,w=1===i.length,O=(0,f.A)("recharts-line",a),j=c&&c.allowDataOverflow,A=l&&l.allowDataOverflow,S=j||A,P=u()(g)?this.id:g,E=null!==(t=(0,b.J9)(o,!1))&&void 0!==t?t:{r:3,strokeWidth:2},k=E.r,M=void 0===k?3:k,T=E.strokeWidth,_=void 0===T?2:T,C=((0,b.sT)(o)?o:{}).clipDot,I=void 0===C||C,D=2*M+_;return n.createElement(d.W,{className:O},j||A?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(P)},n.createElement("rect",{x:j?p:p-h/2,y:A?s:s-v/2,width:j?h:2*h,height:A?v:2*v})),!I&&n.createElement("clipPath",{id:"clipPath-dots-".concat(P)},n.createElement("rect",{x:p-D/2,y:s-D/2,width:h+D,height:v+D}))):null,!w&&this.renderCurve(S,P),this.renderErrorBar(S,P),(w||o)&&this.renderDots(S,I,P),(!m||x)&&y.Z.renderCallByParent(this.props,i))}}])&&T(r.prototype,i),c&&T(r,c),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,i,c}(n.PureComponent);N(R,"displayName","Line"),N(R,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!g.m.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),N(R,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,l=t.displayedData,s=t.offset,f=e.layout;return E({points:l.map(function(t,e){var l=(0,x.kr)(t,a);return"horizontal"===f?{x:(0,x.nb)({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:u()(l)?null:n.scale(l),value:l,payload:t}:{x:u()(l)?null:r.scale(l),y:(0,x.nb)({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:l,payload:t}}),layout:f},s)})},202:(t,e,r)=>{"use strict";r.d(e,{s0:()=>wi,gH:()=>mi,YB:()=>Ci,HQ:()=>Mi,xi:()=>Ii,Hj:()=>Ki,BX:()=>xi,tA:()=>gi,DW:()=>Ui,y2:()=>Fi,nb:()=>zi,PW:()=>Pi,Ay:()=>vi,vf:()=>Ai,Mk:()=>qi,Ps:()=>bi,Mn:()=>Ri,kA:()=>Wi,Rh:()=>Ei,w7:()=>Li,zb:()=>Ji,kr:()=>yi,_L:()=>Si,KC:()=>Gi,A1:()=>ji,W7:()=>Ti,AQ:()=>Vi,_f:()=>Di});var n={};r.r(n),r.d(n,{scaleBand:()=>o.A,scaleDiverging:()=>Qn,scaleDivergingLog:()=>to,scaleDivergingPow:()=>ro,scaleDivergingSqrt:()=>no,scaleDivergingSymlog:()=>eo,scaleIdentity:()=>Ht,scaleImplicit:()=>ie.h,scaleLinear:()=>Xt,scaleLog:()=>te,scaleOrdinal:()=>ie.A,scalePoint:()=>o.z,scalePow:()=>se,scaleQuantile:()=>Oe,scaleQuantize:()=>je,scaleRadial:()=>he,scaleSequential:()=>Vn,scaleSequentialLog:()=>Kn,scaleSequentialPow:()=>Jn,scaleSequentialQuantile:()=>Zn,scaleSequentialSqrt:()=>Yn,scaleSequentialSymlog:()=>Gn,scaleSqrt:()=>fe,scaleSymlog:()=>oe,scaleThreshold:()=>Ae,scaleTime:()=>Wn,scaleUtc:()=>qn,tickFormat:()=>Wt});var o=r(2099);const i=Math.sqrt(50),a=Math.sqrt(10),c=Math.sqrt(2);function u(t,e,r){const n=(e-t)/Math.max(0,r),o=Math.floor(Math.log10(n)),l=n/Math.pow(10,o),s=l>=i?10:l>=a?5:l>=c?2:1;let f,p,h;return o<0?(h=Math.pow(10,-o)/s,f=Math.round(t*h),p=Math.round(e*h),f/h<t&&++f,p/h>e&&--p,h=-h):(h=Math.pow(10,o)*s,f=Math.round(t/h),p=Math.round(e/h),f*h<t&&++f,p*h>e&&--p),p<f&&.5<=r&&r<2?u(t,e,2*r):[f,p,h]}function l(t,e,r){if(!((r=+r)>0))return[];if((t=+t)===(e=+e))return[t];const n=e<t,[o,i,a]=n?u(e,t,r):u(t,e,r);if(!(i>=o))return[];const c=i-o+1,l=new Array(c);if(n)if(a<0)for(let u=0;u<c;++u)l[u]=(i-u)/-a;else for(let u=0;u<c;++u)l[u]=(i-u)*a;else if(a<0)for(let u=0;u<c;++u)l[u]=(o+u)/-a;else for(let u=0;u<c;++u)l[u]=(o+u)*a;return l}function s(t,e,r){return u(t=+t,e=+e,r=+r)[2]}function f(t,e,r){r=+r;const n=(e=+e)<(t=+t),o=n?s(e,t,r):s(t,e,r);return(n?-1:1)*(o<0?1/-o:o)}function p(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function h(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function d(t){let e,r,n;function o(t,n){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<0?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=p,r=(e,r)=>p(t(e),r),n=(e,r)=>t(e)-r):(e=t===p||t===h?t:y,r=t,n=t),{left:o,center:function(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const i=o(t,e,r,(arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length)-1);return i>r&&n(t[i-1],e)>-n(t[i],e)?i-1:i},right:function(t,n){let o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(o<i){if(0!==e(n,n))return i;do{const e=o+i>>>1;r(t[e],n)<=0?o=e+1:i=e}while(o<i)}return o}}}function y(){return 0}function v(t){return null===t?NaN:+t}const m=d(p),b=m.right,g=(m.left,d(v).center,b);function x(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function w(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function O(){}var j=.7,A=1/j,S="\\s*([+-]?\\d+)\\s*",P="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",E="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",k=/^#([0-9a-f]{3,8})$/,M=new RegExp("^rgb\\(".concat(S,",").concat(S,",").concat(S,"\\)$")),T=new RegExp("^rgb\\(".concat(E,",").concat(E,",").concat(E,"\\)$")),_=new RegExp("^rgba\\(".concat(S,",").concat(S,",").concat(S,",").concat(P,"\\)$")),C=new RegExp("^rgba\\(".concat(E,",").concat(E,",").concat(E,",").concat(P,"\\)$")),I=new RegExp("^hsl\\(".concat(P,",").concat(E,",").concat(E,"\\)$")),D=new RegExp("^hsla\\(".concat(P,",").concat(E,",").concat(E,",").concat(P,"\\)$")),N={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function B(){return this.rgb().formatHex()}function R(){return this.rgb().formatRgb()}function L(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=k.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?z(e):3===r?new W(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?F(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?F(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=M.exec(t))?new W(e[1],e[2],e[3],1):(e=T.exec(t))?new W(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=_.exec(t))?F(e[1],e[2],e[3],e[4]):(e=C.exec(t))?F(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=I.exec(t))?G(e[1],e[2]/100,e[3]/100,1):(e=D.exec(t))?G(e[1],e[2]/100,e[3]/100,e[4]):N.hasOwnProperty(t)?z(N[t]):"transparent"===t?new W(NaN,NaN,NaN,0):null}function z(t){return new W(t>>16&255,t>>8&255,255&t,1)}function F(t,e,r,n){return n<=0&&(t=e=r=NaN),new W(t,e,r,n)}function U(t,e,r,n){return 1===arguments.length?((o=t)instanceof O||(o=L(o)),o?new W((o=o.rgb()).r,o.g,o.b,o.opacity):new W):new W(t,e,r,null==n?1:n);var o}function W(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function q(){return"#".concat(K(this.r)).concat(K(this.g)).concat(K(this.b))}function X(){const t=H(this.opacity);return"".concat(1===t?"rgb(":"rgba(").concat(V(this.r),", ").concat(V(this.g),", ").concat(V(this.b)).concat(1===t?")":", ".concat(t,")"))}function H(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function V(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function K(t){return((t=V(t))<16?"0":"")+t.toString(16)}function G(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new Y(t,e,r,n)}function J(t){if(t instanceof Y)return new Y(t.h,t.s,t.l,t.opacity);if(t instanceof O||(t=L(t)),!t)return new Y;if(t instanceof Y)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+6*(r<n):r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new Y(a,c,u,t.opacity)}function Y(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function Z(t){return(t=(t||0)%360)<0?t+360:t}function $(t){return Math.max(0,Math.min(1,t||0))}function Q(t,e,r){return 255*(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)}function tt(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}x(O,L,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:B,formatHex:B,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return J(this).formatHsl()},formatRgb:R,toString:R}),x(W,U,w(O,{brighter(t){return t=null==t?A:Math.pow(A,t),new W(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?j:Math.pow(j,t),new W(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new W(V(this.r),V(this.g),V(this.b),H(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:q,formatHex:q,formatHex8:function(){return"#".concat(K(this.r)).concat(K(this.g)).concat(K(this.b)).concat(K(255*(isNaN(this.opacity)?1:this.opacity)))},formatRgb:X,toString:X})),x(Y,function(t,e,r,n){return 1===arguments.length?J(t):new Y(t,e,r,null==n?1:n)},w(O,{brighter(t){return t=null==t?A:Math.pow(A,t),new Y(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?j:Math.pow(j,t),new Y(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new W(Q(t>=240?t-240:t+120,o,n),Q(t,o,n),Q(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new Y(Z(this.h),$(this.s),$(this.l),H(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=H(this.opacity);return"".concat(1===t?"hsl(":"hsla(").concat(Z(this.h),", ").concat(100*$(this.s),"%, ").concat(100*$(this.l),"%").concat(1===t?")":", ".concat(t,")"))}}));const et=t=>()=>t;function rt(t,e){return function(r){return t+r*e}}function nt(t){return 1===(t=+t)?ot:function(e,r){return r-e?function(t,e,r){return t=Math.pow(t,r),e=Math.pow(e,r)-t,r=1/r,function(n){return Math.pow(t+n*e,r)}}(e,r,t):et(isNaN(e)?r:e)}}function ot(t,e){var r=e-t;return r?rt(t,r):et(isNaN(t)?e:t)}const it=function t(e){var r=nt(e);function n(t,e){var n=r((t=U(t)).r,(e=U(e)).r),o=r(t.g,e.g),i=r(t.b,e.b),a=ot(t.opacity,e.opacity);return function(e){return t.r=n(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return n.gamma=t,n}(1);function at(t){return function(e){var r,n,o=e.length,i=new Array(o),a=new Array(o),c=new Array(o);for(r=0;r<o;++r)n=U(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}at(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return tt((r-n/e)*e,a,o,i,c)}}),at(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return tt((r-n/e)*e,o,i,a,c)}});function ct(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=new Array(o),a=new Array(n);for(r=0;r<o;++r)i[r]=yt(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}function ut(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}function lt(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}function st(t,e){var r,n={},o={};for(r in null!==t&&"object"===typeof t||(t={}),null!==e&&"object"===typeof e||(e={}),e)r in t?n[r]=yt(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}var ft=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,pt=new RegExp(ft.source,"g");function ht(t,e){var r,n,o,i=ft.lastIndex=pt.lastIndex=0,a=-1,c=[],u=[];for(t+="",e+="";(r=ft.exec(t))&&(n=pt.exec(e));)(o=n.index)>i&&(o=e.slice(i,o),c[a]?c[a]+=o:c[++a]=o),(r=r[0])===(n=n[0])?c[a]?c[a]+=n:c[++a]=n:(c[++a]=null,u.push({i:a,x:lt(r,n)})),i=pt.lastIndex;return i<e.length&&(o=e.slice(i),c[a]?c[a]+=o:c[++a]=o),c.length<2?u[0]?function(t){return function(e){return t(e)+""}}(u[0].x):function(t){return function(){return t}}(e):(e=u.length,function(t){for(var r,n=0;n<e;++n)c[(r=u[n]).i]=r.x(t);return c.join("")})}function dt(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}}function yt(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?et(e):("number"===o?lt:"string"===o?(r=L(e))?(e=r,it):ht:e instanceof L?it:e instanceof Date?ut:(n=e,!ArrayBuffer.isView(n)||n instanceof DataView?Array.isArray(e)?ct:"function"!==typeof e.valueOf&&"function"!==typeof e.toString||isNaN(e)?st:lt:dt))(t,e)}function vt(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function mt(t){return+t}var bt=[0,1];function gt(t){return t}function xt(t,e){return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r});var r}function wt(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=xt(o,n),i=r(a,i)):(n=xt(n,o),i=r(i,a)),function(t){return i(n(t))}}function Ot(t,e,r){var n=Math.min(t.length,e.length)-1,o=new Array(n),i=new Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=xt(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=g(t,e,1,n)-1;return i[r](o[r](e))}}function jt(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function At(){var t,e,r,n,o,i,a=bt,c=bt,u=yt,l=gt;function s(){var t=Math.min(a.length,c.length);return l!==gt&&(l=function(t,e){var r;return t>e&&(r=t,t=e,e=r),function(r){return Math.max(t,Math.min(e,r))}}(a[0],a[t-1])),n=t>2?Ot:wt,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),lt)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,mt),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=vt,s()},f.clamp=function(t){return arguments.length?(l=!!t||gt,s()):l!==gt},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function St(){return At()(gt,gt)}var Pt,Et=r(4402),kt=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Mt(t){if(!(e=kt.exec(t)))throw new Error("invalid format: "+t);var e;return new Tt({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function Tt(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function _t(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function Ct(t){return(t=_t(Math.abs(t)))?t[1]:NaN}function It(t,e){var r=_t(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+new Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+new Array(o-n.length+2).join("0")}Mt.prototype=Tt.prototype,Tt.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Dt={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>It(100*t,e),r:It,s:function(t,e){var r=_t(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(Pt=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+new Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+new Array(1-i).join("0")+_t(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Nt(t){return t}var Bt,Rt,Lt,zt=Array.prototype.map,Ft=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function Ut(t){var e,r,n=void 0===t.grouping||void 0===t.thousands?Nt:(e=zt.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",i=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?Nt:function(t){return function(e){return e.replace(/[0-9]/g,function(e){return t[+e]})}}(zt.call(t.numerals,String)),u=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"\u2212":t.minus+"",s=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=Mt(t)).fill,r=t.align,f=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):Dt[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?i:/[%p]/.test(b)?u:"",w=Dt[b],O=/[defgprs%]/.test(b);function j(t){var o,i,u,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var A=(t=+t)<0||1/t<0;if(t=isNaN(t)?s:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),A&&0===+t&&"+"!==f&&(A=!1),p=(A?"("===f?f:l:"-"===f||"("===f?"":f)+p,j=("s"===b?Ft[8+Pt/3]:"")+j+(A&&"("===f?")":""),O)for(o=-1,i=t.length;++o<i;)if(48>(u=t.charCodeAt(o))||u>57){j=(46===u?a+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}y&&!h&&(t=n(t,1/0));var S=p.length+t.length+j.length,P=S<d?new Array(d-S+1).join(e):"";switch(y&&h&&(t=n(P+t,P.length?d-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,S=P.length>>1)+p+t+j+P.slice(S);break;default:t=P+p+t+j}return c(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:f,formatPrefix:function(t,e){var r=f(((t=Mt(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(Ct(e)/3))),o=Math.pow(10,-n),i=Ft[8+n/3];return function(t){return r(o*t)+i}}}}function Wt(t,e,r,n){var o,i=f(t,e,r);switch((n=Mt(null==n?",f":n)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(o=function(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Ct(e)/3)))-Ct(Math.abs(t)))}(i,a))||(n.precision=o),Lt(n,a);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=function(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Ct(e)-Ct(t))+1}(i,Math.max(Math.abs(t),Math.abs(e))))||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=function(t){return Math.max(0,-Ct(Math.abs(t)))}(i))||(n.precision=o-2*("%"===n.type))}return Rt(n)}function qt(t){var e=t.domain;return t.ticks=function(t){var r=e();return l(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return Wt(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],f=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);f-- >0;){if((o=s(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else{if(!(o<0))break;u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o}n=o}return t},t}function Xt(){var t=St();return t.copy=function(){return jt(t,Xt())},Et.C.apply(t,arguments),qt(t)}function Ht(t){var e;function r(t){return null==t||isNaN(t=+t)?e:t}return r.invert=r,r.domain=r.range=function(e){return arguments.length?(t=Array.from(e,mt),r):t.slice()},r.unknown=function(t){return arguments.length?(e=t,r):e},r.copy=function(){return Ht(t).unknown(e)},t=arguments.length?Array.from(t,mt):[0,1],qt(r)}function Vt(t,e){var r,n=0,o=(t=t.slice()).length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function Kt(t){return Math.log(t)}function Gt(t){return Math.exp(t)}function Jt(t){return-Math.log(-t)}function Yt(t){return-Math.exp(-t)}function Zt(t){return isFinite(t)?+("1e"+t):t<0?0:t}function $t(t){return(e,r)=>-t(-e,r)}function Qt(t){const e=t(Kt,Gt),r=e.domain;let n,o,i=10;function a(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(i),o=function(t){return 10===t?Zt:t===Math.E?Math.exp:e=>Math.pow(t,e)}(i),r()[0]<0?(n=$t(n),o=$t(o),t(Jt,Yt)):t(Kt,Gt),e}return e.base=function(t){return arguments.length?(i=+t,a()):i},e.domain=function(t){return arguments.length?(r(t),a()):r()},e.ticks=t=>{const e=r();let a=e[0],c=e[e.length-1];const u=c<a;u&&([a,c]=[c,a]);let s,f,p=n(a),h=n(c);const d=null==t?10:+t;let y=[];if(!(i%1)&&h-p<d){if(p=Math.floor(p),h=Math.ceil(h),a>0){for(;p<=h;++p)for(s=1;s<i;++s)if(f=p<0?s/o(-p):s*o(p),!(f<a)){if(f>c)break;y.push(f)}}else for(;p<=h;++p)for(s=i-1;s>=1;--s)if(f=p>0?s/o(-p):s*o(p),!(f<a)){if(f>c)break;y.push(f)}2*y.length<d&&(y=l(a,c,d))}else y=l(p,h,Math.min(h-p,d)).map(o);return u?y.reverse():y},e.tickFormat=(t,r)=>{if(null==t&&(t=10),null==r&&(r=10===i?"s":","),"function"!==typeof r&&(i%1||null!=(r=Mt(r)).precision||(r.trim=!0),r=Rt(r)),t===1/0)return r;const a=Math.max(1,i*t/e.ticks().length);return t=>{let e=t/o(Math.round(n(t)));return e*i<i-.5&&(e*=i),e<=a?r(t):""}},e.nice=()=>r(Vt(r(),{floor:t=>o(Math.floor(n(t))),ceil:t=>o(Math.ceil(n(t)))})),e}function te(){const t=Qt(At()).domain([1,10]);return t.copy=()=>jt(t,te()).base(t.base()),Et.C.apply(t,arguments),t}function ee(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function re(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function ne(t){var e=1,r=t(ee(e),re(e));return r.constant=function(r){return arguments.length?t(ee(e=+r),re(e)):e},qt(r)}function oe(){var t=ne(At());return t.copy=function(){return jt(t,oe()).constant(t.constant())},Et.C.apply(t,arguments)}Bt=Ut({thousands:",",grouping:[3],currency:["$",""]}),Rt=Bt.format,Lt=Bt.formatPrefix;var ie=r(5186);function ae(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function ce(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function ue(t){return t<0?-t*t:t*t}function le(t){var e=t(gt,gt),r=1;return e.exponent=function(e){return arguments.length?1===(r=+e)?t(gt,gt):.5===r?t(ce,ue):t(ae(r),ae(1/r)):r},qt(e)}function se(){var t=le(At());return t.copy=function(){return jt(t,se()).exponent(t.exponent())},Et.C.apply(t,arguments),t}function fe(){return se.apply(null,arguments).exponent(.5)}function pe(t){return Math.sign(t)*t*t}function he(){var t,e=St(),r=[0,1],n=!1;function o(r){var o=function(t){return Math.sign(t)*Math.sqrt(Math.abs(t))}(e(r));return isNaN(o)?t:n?Math.round(o):o}return o.invert=function(t){return e.invert(pe(t))},o.domain=function(t){return arguments.length?(e.domain(t),o):e.domain()},o.range=function(t){return arguments.length?(e.range((r=Array.from(t,mt)).map(pe)),o):r.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(n=!!t,o):n},o.clamp=function(t){return arguments.length?(e.clamp(t),o):e.clamp()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return he(e.domain(),r).round(n).clamp(e.clamp()).unknown(t)},Et.C.apply(o,arguments),qt(o)}function de(t,e){let r;if(void 0===e)for(const n of t)null!=n&&(r<n||void 0===r&&n>=n)&&(r=n);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function ye(t,e){let r;if(void 0===e)for(const n of t)null!=n&&(r>n||void 0===r&&n>=n)&&(r=n);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function ve(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:p;if(t===p)return me;if("function"!==typeof t)throw new TypeError("compare is not a function");return(e,r)=>{const n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}function me(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function be(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1/0,o=arguments.length>4?arguments[4]:void 0;if(e=Math.floor(e),r=Math.floor(Math.max(0,r)),n=Math.floor(Math.min(t.length-1,n)),!(r<=e&&e<=n))return t;for(o=void 0===o?me:ve(o);n>r;){if(n-r>600){const i=n-r+1,a=e-r+1,c=Math.log(i),u=.5*Math.exp(2*c/3),l=.5*Math.sqrt(c*u*(i-u)/i)*(a-i/2<0?-1:1);be(t,e,Math.max(r,Math.floor(e-a*u/i+l)),Math.min(n,Math.floor(e+(i-a)*u/i+l)),o)}const i=t[e];let a=r,c=n;for(ge(t,r,e),o(t[n],i)>0&&ge(t,r,n);a<c;){for(ge(t,a,c),++a,--c;o(t[a],i)<0;)++a;for(;o(t[c],i)>0;)--c}0===o(t[r],i)?ge(t,r,c):(++c,ge(t,c,n)),c<=e&&(r=c+1),e<=c&&(n=c-1)}return t}function ge(t,e,r){const n=t[e];t[e]=t[r],t[r]=n}function xe(t,e,r){if(t=Float64Array.from(function*(t,e){if(void 0===e)for(let r of t)null!=r&&(r=+r)>=r&&(yield r);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,r)),(n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return ye(t);if(e>=1)return de(t);var n,o=(n-1)*e,i=Math.floor(o),a=de(be(t,i).subarray(0,i+1));return a+(ye(t.subarray(i+1))-a)*(o-i)}}function we(t,e){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:v;if((n=t.length)&&!isNaN(e=+e)){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}function Oe(){var t,e=[],r=[],n=[];function o(){var t=0,o=Math.max(1,r.length);for(n=new Array(o-1);++t<o;)n[t-1]=we(e,t/o);return i}function i(e){return null==e||isNaN(e=+e)?t:r[g(n,e)]}return i.invertExtent=function(t){var o=r.indexOf(t);return o<0?[NaN,NaN]:[o>0?n[o-1]:e[0],o<n.length?n[o]:e[e.length-1]]},i.domain=function(t){if(!arguments.length)return e.slice();e=[];for(let r of t)null==r||isNaN(r=+r)||e.push(r);return e.sort(p),o()},i.range=function(t){return arguments.length?(r=Array.from(t),o()):r.slice()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.quantiles=function(){return n.slice()},i.copy=function(){return Oe().domain(e).range(r).unknown(t)},Et.C.apply(i,arguments)}function je(){var t,e=0,r=1,n=1,o=[.5],i=[0,1];function a(e){return null!=e&&e<=e?i[g(o,e,0,n)]:t}function c(){var t=-1;for(o=new Array(n);++t<n;)o[t]=((t+1)*r-(t-n)*e)/(n+1);return a}return a.domain=function(t){return arguments.length?([e,r]=t,e=+e,r=+r,c()):[e,r]},a.range=function(t){return arguments.length?(n=(i=Array.from(t)).length-1,c()):i.slice()},a.invertExtent=function(t){var a=i.indexOf(t);return a<0?[NaN,NaN]:a<1?[e,o[0]]:a>=n?[o[n-1],r]:[o[a-1],o[a]]},a.unknown=function(e){return arguments.length?(t=e,a):a},a.thresholds=function(){return o.slice()},a.copy=function(){return je().domain([e,r]).range(i).unknown(t)},Et.C.apply(qt(a),arguments)}function Ae(){var t,e=[.5],r=[0,1],n=1;function o(o){return null!=o&&o<=o?r[g(e,o,0,n)]:t}return o.domain=function(t){return arguments.length?(e=Array.from(t),n=Math.min(e.length,r.length-1),o):e.slice()},o.range=function(t){return arguments.length?(r=Array.from(t),n=Math.min(e.length,r.length-1),o):r.slice()},o.invertExtent=function(t){var n=r.indexOf(t);return[e[n-1],e[n]]},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return Ae().domain(e).range(r).unknown(t)},Et.C.apply(o,arguments)}const Se=1e3,Pe=6e4,Ee=36e5,ke=864e5,Me=6048e5,Te=2592e6,_e=31536e6,Ce=new Date,Ie=new Date;function De(t,e,r,n){function o(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{const e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{const a=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return a;let c;do{a.push(c=new Date(+r)),e(r,i),t(r)}while(c<r&&r<n);return a},o.filter=r=>De(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(o.count=(e,n)=>(Ce.setTime(+e),Ie.setTime(+n),t(Ce),t(Ie),Math.floor(r(Ce,Ie))),o.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?o.filter(n?e=>n(e)%t===0:e=>o.count(0,e)%t===0):o:null)),o}const Ne=De(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);Ne.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?De(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):Ne:null);Ne.range;const Be=De(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+e*Se)},(t,e)=>(e-t)/Se,t=>t.getUTCSeconds()),Re=(Be.range,De(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Se)},(t,e)=>{t.setTime(+t+e*Pe)},(t,e)=>(e-t)/Pe,t=>t.getMinutes())),Le=(Re.range,De(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+e*Pe)},(t,e)=>(e-t)/Pe,t=>t.getUTCMinutes())),ze=(Le.range,De(t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*Se-t.getMinutes()*Pe)},(t,e)=>{t.setTime(+t+e*Ee)},(t,e)=>(e-t)/Ee,t=>t.getHours())),Fe=(ze.range,De(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+e*Ee)},(t,e)=>(e-t)/Ee,t=>t.getUTCHours())),Ue=(Fe.range,De(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Pe)/ke,t=>t.getDate()-1)),We=(Ue.range,De(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ke,t=>t.getUTCDate()-1)),qe=(We.range,De(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/ke,t=>Math.floor(t/ke)));qe.range;function Xe(t){return De(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*Pe)/Me)}const He=Xe(0),Ve=Xe(1),Ke=Xe(2),Ge=Xe(3),Je=Xe(4),Ye=Xe(5),Ze=Xe(6);He.range,Ve.range,Ke.range,Ge.range,Je.range,Ye.range,Ze.range;function $e(t){return De(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/Me)}const Qe=$e(0),tr=$e(1),er=$e(2),rr=$e(3),nr=$e(4),or=$e(5),ir=$e(6),ar=(Qe.range,tr.range,er.range,rr.range,nr.range,or.range,ir.range,De(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear()),t=>t.getMonth())),cr=(ar.range,De(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear()),t=>t.getUTCMonth())),ur=(cr.range,De(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear()));ur.every=t=>isFinite(t=Math.floor(t))&&t>0?De(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null;ur.range;const lr=De(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());lr.every=t=>isFinite(t=Math.floor(t))&&t>0?De(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null;lr.range;function sr(t,e,r,n,o,i){const a=[[Be,1,Se],[Be,5,5e3],[Be,15,15e3],[Be,30,3e4],[i,1,Pe],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,Ee],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,ke],[n,2,1728e5],[r,1,Me],[e,1,Te],[e,3,7776e6],[t,1,_e]];function c(e,r,n){const o=Math.abs(r-e)/n,i=d(t=>{let[,,e]=t;return e}).right(a,o);if(i===a.length)return t.every(f(e/_e,r/_e,n));if(0===i)return Ne.every(Math.max(f(e,r,n),1));const[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){const n=e<t;n&&([t,e]=[e,t]);const o=r&&"function"===typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}const[fr,pr]=sr(lr,cr,Qe,qe,Fe,Le),[hr,dr]=sr(ur,ar,He,Ue,ze,Re);function yr(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function vr(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function mr(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var br,gr,xr,wr={"-":"",_:" ",0:"0"},Or=/^\s*\d+/,jr=/^%/,Ar=/[\\^$*+?|[\]().{}]/g;function Sr(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?new Array(r-i+1).join(e)+o:o)}function Pr(t){return t.replace(Ar,"\\$&")}function Er(t){return new RegExp("^(?:"+t.map(Pr).join("|")+")","i")}function kr(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function Mr(t,e,r){var n=Or.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function Tr(t,e,r){var n=Or.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function _r(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function Cr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function Ir(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function Dr(t,e,r){var n=Or.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function Nr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function Br(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function Rr(t,e,r){var n=Or.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function Lr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function zr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function Fr(t,e,r){var n=Or.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function Ur(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function Wr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function qr(t,e,r){var n=Or.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function Xr(t,e,r){var n=Or.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function Hr(t,e,r){var n=Or.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function Vr(t,e,r){var n=jr.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function Kr(t,e,r){var n=Or.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function Gr(t,e,r){var n=Or.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function Jr(t,e){return Sr(t.getDate(),e,2)}function Yr(t,e){return Sr(t.getHours(),e,2)}function Zr(t,e){return Sr(t.getHours()%12||12,e,2)}function $r(t,e){return Sr(1+Ue.count(ur(t),t),e,3)}function Qr(t,e){return Sr(t.getMilliseconds(),e,3)}function tn(t,e){return Qr(t,e)+"000"}function en(t,e){return Sr(t.getMonth()+1,e,2)}function rn(t,e){return Sr(t.getMinutes(),e,2)}function nn(t,e){return Sr(t.getSeconds(),e,2)}function on(t){var e=t.getDay();return 0===e?7:e}function an(t,e){return Sr(He.count(ur(t)-1,t),e,2)}function cn(t){var e=t.getDay();return e>=4||0===e?Je(t):Je.ceil(t)}function un(t,e){return t=cn(t),Sr(Je.count(ur(t),t)+(4===ur(t).getDay()),e,2)}function ln(t){return t.getDay()}function sn(t,e){return Sr(Ve.count(ur(t)-1,t),e,2)}function fn(t,e){return Sr(t.getFullYear()%100,e,2)}function pn(t,e){return Sr((t=cn(t)).getFullYear()%100,e,2)}function hn(t,e){return Sr(t.getFullYear()%1e4,e,4)}function dn(t,e){var r=t.getDay();return Sr((t=r>=4||0===r?Je(t):Je.ceil(t)).getFullYear()%1e4,e,4)}function yn(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+Sr(e/60|0,"0",2)+Sr(e%60,"0",2)}function vn(t,e){return Sr(t.getUTCDate(),e,2)}function mn(t,e){return Sr(t.getUTCHours(),e,2)}function bn(t,e){return Sr(t.getUTCHours()%12||12,e,2)}function gn(t,e){return Sr(1+We.count(lr(t),t),e,3)}function xn(t,e){return Sr(t.getUTCMilliseconds(),e,3)}function wn(t,e){return xn(t,e)+"000"}function On(t,e){return Sr(t.getUTCMonth()+1,e,2)}function jn(t,e){return Sr(t.getUTCMinutes(),e,2)}function An(t,e){return Sr(t.getUTCSeconds(),e,2)}function Sn(t){var e=t.getUTCDay();return 0===e?7:e}function Pn(t,e){return Sr(Qe.count(lr(t)-1,t),e,2)}function En(t){var e=t.getUTCDay();return e>=4||0===e?nr(t):nr.ceil(t)}function kn(t,e){return t=En(t),Sr(nr.count(lr(t),t)+(4===lr(t).getUTCDay()),e,2)}function Mn(t){return t.getUTCDay()}function Tn(t,e){return Sr(tr.count(lr(t)-1,t),e,2)}function _n(t,e){return Sr(t.getUTCFullYear()%100,e,2)}function Cn(t,e){return Sr((t=En(t)).getUTCFullYear()%100,e,2)}function In(t,e){return Sr(t.getUTCFullYear()%1e4,e,4)}function Dn(t,e){var r=t.getUTCDay();return Sr((t=r>=4||0===r?nr(t):nr.ceil(t)).getUTCFullYear()%1e4,e,4)}function Nn(){return"+0000"}function Bn(){return"%"}function Rn(t){return+t}function Ln(t){return Math.floor(+t/1e3)}function zn(t){return new Date(t)}function Fn(t){return t instanceof Date?+t:+new Date(+t)}function Un(t,e,r,n,o,i,a,c,u,l){var s=St(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,Fn)):p().map(zn)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"===typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(Vt(r,t)):s},s.copy=function(){return jt(s,Un(t,e,r,n,o,i,a,c,u,l))},s}function Wn(){return Et.C.apply(Un(hr,dr,ur,ar,He,Ue,ze,Re,Be,gr).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function qn(){return Et.C.apply(Un(fr,pr,lr,cr,Qe,We,Fe,Le,Be,xr).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function Xn(){var t,e,r,n,o,i=0,a=1,c=gt,u=!1;function l(e){return null==e||isNaN(e=+e)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(yt),l.rangeRound=s(vt),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function Hn(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function Vn(){var t=qt(Xn()(gt));return t.copy=function(){return Hn(t,Vn())},Et.K.apply(t,arguments)}function Kn(){var t=Qt(Xn()).domain([1,10]);return t.copy=function(){return Hn(t,Kn()).base(t.base())},Et.K.apply(t,arguments)}function Gn(){var t=ne(Xn());return t.copy=function(){return Hn(t,Gn()).constant(t.constant())},Et.K.apply(t,arguments)}function Jn(){var t=le(Xn());return t.copy=function(){return Hn(t,Jn()).exponent(t.exponent())},Et.K.apply(t,arguments)}function Yn(){return Jn.apply(null,arguments).exponent(.5)}function Zn(){var t=[],e=gt;function r(r){if(null!=r&&!isNaN(r=+r))return e((g(t,r,1)-1)/(t.length-1))}return r.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let r of e)null==r||isNaN(r=+r)||t.push(r);return t.sort(p),r},r.interpolator=function(t){return arguments.length?(e=t,r):e},r.range=function(){return t.map((r,n)=>e(n/(t.length-1)))},r.quantiles=function(e){return Array.from({length:e+1},(r,n)=>xe(t,n/e))},r.copy=function(){return Zn(e).domain(t)},Et.K.apply(r,arguments)}function $n(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=gt,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=yt);for(var r=0,n=e.length-1,o=e[0],i=new Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c=+c),e=i(u=+u),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(yt),h.rangeRound=d(vt),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function Qn(){var t=qt($n()(gt));return t.copy=function(){return Hn(t,Qn())},Et.K.apply(t,arguments)}function to(){var t=Qt($n()).domain([.1,1,10]);return t.copy=function(){return Hn(t,to()).base(t.base())},Et.K.apply(t,arguments)}function eo(){var t=ne($n());return t.copy=function(){return Hn(t,eo()).constant(t.constant())},Et.K.apply(t,arguments)}function ro(){var t=le($n());return t.copy=function(){return Hn(t,ro()).exponent(t.exponent())},Et.K.apply(t,arguments)}function no(){return ro.apply(null,arguments).exponent(.5)}function oo(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}!function(t){br=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=Er(o),s=kr(o),f=Er(i),p=kr(i),h=Er(a),d=kr(a),y=Er(c),v=kr(c),m=Er(u),b=kr(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:Jr,e:Jr,f:tn,g:pn,G:dn,H:Yr,I:Zr,j:$r,L:Qr,m:en,M:rn,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:Rn,s:Ln,S:nn,u:on,U:an,V:un,w:ln,W:sn,x:null,X:null,y:fn,Y:hn,Z:yn,"%":Bn},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:vn,e:vn,f:wn,g:Cn,G:Dn,H:mn,I:bn,j:gn,L:xn,m:On,M:jn,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:Rn,s:Ln,S:An,u:Sn,U:Pn,V:kn,w:Mn,W:Tn,x:null,X:null,y:_n,Y:In,Z:Nn,"%":Bn},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return A(t,e,r,n)},d:zr,e:zr,f:Hr,g:Nr,G:Dr,H:Ur,I:Ur,j:Fr,L:Xr,m:Lr,M:Wr,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:Rr,Q:Kr,s:Gr,S:qr,u:Tr,U:_r,V:Cr,w:Mr,W:Ir,x:function(t,e,n){return A(t,r,e,n)},X:function(t,e,r){return A(t,n,e,r)},y:Nr,Y:Dr,Z:Br,"%":Vr};function O(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=wr[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=mr(1900,void 0,1);if(A(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(e&&!("Z"in i)&&(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(o=(n=vr(mr(i.y,0,1))).getUTCDay(),n=o>4||0===o?tr.ceil(n):tr(n),n=We.offset(n,7*(i.V-1)),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(o=(n=yr(mr(i.y,0,1))).getDay(),n=o>4||0===o?Ve.ceil(n):Ve(n),n=Ue.offset(n,7*(i.V-1)),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?vr(mr(i.y,0,1)).getUTCDay():yr(mr(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,vr(i)):yr(i)}}function A(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return-1;if(37===(o=e.charCodeAt(a++))){if(o=e.charAt(a++),!(i=w[o in wr?e.charAt(a++):o])||(n=i(t,r,n))<0)return-1}else if(o!=r.charCodeAt(n++))return-1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}(t),gr=br.format,br.parse,xr=br.utcFormat,br.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var io=r(9236),ao=r(3809);function co(t){for(var e=t.length,r=new Array(e);--e>=0;)r[e]=e;return r}function uo(t,e){return t[e]}function lo(t){const e=[];return e.key=t,e}var so=r(539),fo=r.n(so),po=r(6745),ho=r.n(po),yo=r(9686),vo=r.n(yo),mo=r(1629),bo=r.n(mo),go=r(620),xo=r.n(go),wo=r(3097),Oo=r.n(wo),jo=r(3538),Ao=r.n(jo),So=r(5268),Po=r.n(So),Eo=r(643),ko=r.n(Eo),Mo=r(9853),To=r.n(Mo),_o=r(7424),Co=r.n(_o),Io=r(8210),Do=r.n(Io);function No(t){return function(t){if(Array.isArray(t))return Bo(t)}(t)||function(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return Bo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bo(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Bo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Ro=function(t){return t},Lo={"@@functional/placeholder":!0},zo=function(t){return t===Lo},Fo=function(t){return function e(){return 0===arguments.length||1===arguments.length&&zo(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},Uo=function t(e,r){return 1===e?r:Fo(function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==Lo}).length;return a>=e?r.apply(void 0,o):t(e-a,Fo(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return zo(t)?e.shift():t});return r.apply(void 0,No(i).concat(e))}))})},Wo=function(t){return Uo(t.length,t)},qo=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},Xo=Wo(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),Ho=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return Ro;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},Vo=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},Ko=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};const Go={rangeStep:function(t,e,r){for(var n=new(Do())(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new(Do())(t).abs().log(10).toNumber())+1},interpolateNumber:Wo(function(t,e,r){var n=+t;return n+r*(+e-n)}),uninterpolateNumber:Wo(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uninterpolateTruncation:Wo(function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))})};function Jo(t){return function(t){if(Array.isArray(t))return $o(t)}(t)||function(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Zo(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yo(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"===typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(u){o=!0,i=u}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}(t,e)||Zo(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Zo(t,e){if(t){if("string"===typeof t)return $o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?$o(t,e):void 0}}function $o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Qo(t){var e=Yo(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function ti(t,e,r){if(t.lte(0))return new(Do())(0);var n=Go.getDigitCount(t.toNumber()),o=new(Do())(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new(Do())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new(Do())(Math.ceil(c))}function ei(t,e,r){var n=1,o=new(Do())(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new(Do())(10).pow(Go.getDigitCount(t)-1),o=new(Do())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(Do())(Math.floor(t)))}else 0===t?o=new(Do())(Math.floor((e-1)/2)):r||(o=new(Do())(Math.floor(t)));var a=Math.floor((e-1)/2);return Ho(Xo(function(t){return o.add(new(Do())(t-a).mul(n)).toNumber()}),qo)(0,e)}function ri(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new(Do())(0),tickMin:new(Do())(0),tickMax:new(Do())(0)};var i,a=ti(new(Do())(e).sub(t).div(r-1),n,o);i=t<=0&&e>=0?new(Do())(0):(i=new(Do())(t).add(e).div(2)).sub(new(Do())(i).mod(a));var c=Math.ceil(i.sub(t).div(a).toNumber()),u=Math.ceil(new(Do())(e).sub(i).div(a).toNumber()),l=c+u+1;return l>r?ri(t,e,r,n,o+1):(l<r&&(u=e>0?u+(r-l):u,c=e>0?c:c+(r-l)),{step:a,tickMin:i.sub(new(Do())(c).mul(a)),tickMax:i.add(new(Do())(u).mul(a))})}var ni=Ko(function(t){var e=Yo(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),c=Yo(Qo([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(Jo(qo(0,o-1).map(function(){return 1/0}))):[].concat(Jo(qo(0,o-1).map(function(){return-1/0})),[l]);return r>n?Vo(s):s}if(u===l)return ei(u,o,i);var f=ri(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=Go.rangeStep(h,d.add(new(Do())(.1).mul(p)),p);return r>n?Vo(y):y}),oi=(Ko(function(t){var e=Yo(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(o,2),c=Yo(Qo([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return ei(u,o,i);var s=ti(new(Do())(l).sub(u).div(a-1),i,0),f=Ho(Xo(function(t){return new(Do())(u).add(new(Do())(t).mul(s)).toNumber()}),qo)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?Vo(f):f}),Ko(function(t,e){var r=Yo(t,2),n=r[0],o=r[1],i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Yo(Qo([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=ti(new(Do())(u).sub(c).div(l-1),i,0),f=[].concat(Jo(Go.rangeStep(new(Do())(c),new(Do())(u).sub(new(Do())(.99).mul(s)),s)),[u]);return n>o?Vo(f):f})),ii=r(8813),ai=r(6307),ci=r(240),ui=r(7165);function li(t){return li="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},li(t)}function si(t){return function(t){if(Array.isArray(t))return fi(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return fi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fi(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fi(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pi(Object(r),!0).forEach(function(e){di(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pi(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function di(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=li(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=li(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==li(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yi(t,e,r){return vo()(t)||vo()(e)?r:(0,ai.vh)(e)?Oo()(t,e,r):bo()(e)?e(t):r}function vi(t,e,r,n){var o=Ao()(t,function(t){return yi(t,e)});if("number"===r){var i=o.filter(function(t){return(0,ai.Et)(t)||parseFloat(t)});return i.length?[ho()(i),fo()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!vo()(t)}):o).map(function(t){return(0,ai.vh)(t)||t instanceof Date?t:""})}var mi=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null===r||void 0===r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&Math.abs(Math.abs(o.range[1]-o.range[0])-360)<=1e-6)for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if((0,ai.sA)(s-l)!==(0,ai.sA)(f-s)){var h=[];if((0,ai.sA)(f-s)===(0,ai.sA)(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},bi=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?hi(hi({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},gi=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,ci.Mn)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?hi(hi({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=vo()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:vo()(w)?void 0:(0,ai.F4)(w,r,0)})}}return i},xi=function(t){var e=t.barGap,r=t.barCategoryGap,n=t.bandSize,o=t.sizeList,i=void 0===o?[]:o,a=t.maxBarSize,c=i.length;if(c<1)return null;var u,l=(0,ai.F4)(e,n,0,!0),s=[];if(i[0].barSize===+i[0].barSize){var f=!1,p=n/c,h=i.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*l)>=n&&(h-=(c-1)*l,l=0),h>=n&&p>0&&(f=!0,h=c*(p*=.9));var d={offset:((n-h)/2|0)-l,size:0};u=i.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(si(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,ai.F4)(r,n,0,!0);n-2*y-(c-1)*l<=0&&(l=0);var v=(n-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=a===+a?Math.min(v,a):v;u=i.reduce(function(t,e,r){var n=[].concat(si(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return u},wi=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=i-(a.left||0)-(a.right||0),u=(0,ui.g)({children:o,legendWidth:c});if(u){var l=n||{},s=l.width,f=l.height,p=u.align,h=u.verticalAlign,d=u.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,ai.Et)(t[p]))return hi(hi({},t),{},di({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,ai.Et)(t[h]))return hi(hi({},t),{},di({},h,t[h]+(f||0)))}return t},Oi=function(t,e,r,n,o){var i=e.props.children,a=(0,ci.aS)(i,ii.u).filter(function(t){return function(t,e,r){return!!vo()(e)||("horizontal"===t?"yAxis"===e:"vertical"===t||"x"===r?"xAxis"===e:"y"!==r||"yAxis"===e)}(n,o,t.props.direction)});if(a&&a.length){var c=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=yi(e,r);if(vo()(n))return t;var o=Array.isArray(n)?[ho()(n),fo()(n)]:[n,n],i=c.reduce(function(t,r){var n=yi(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},ji=function(t,e,r,n,o){var i=e.map(function(e){return Oi(t,e,r,o,n)}).filter(function(t){return!vo()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},Ai=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&Oi(t,e,i,n)||vi(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},Si=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},Pi=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},Ei=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return u="angleAxis"===t.axisType&&(null===a||void 0===a?void 0:a.length)>=2?2*(0,ai.sA)(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks)?(t.ticks||t.niceTicks).map(function(t){var e=o?o.indexOf(t):t;return{coordinate:n(e)+u,value:t,offset:u}}).filter(function(t){return!Po()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},ki=new WeakMap,Mi=function(t,e){if("function"!==typeof e)return t;ki.has(t)||ki.set(t,new WeakMap);var r=ki.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},Ti=function(t,e,r){var i=t.scale,a=t.type,c=t.layout,u=t.axisType;if("auto"===i)return"radial"===c&&"radiusAxis"===u?{scale:o.A(),realScaleType:"band"}:"radial"===c&&"angleAxis"===u?{scale:Xt(),realScaleType:"linear"}:"category"===a&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:o.z(),realScaleType:"point"}:"category"===a?{scale:o.A(),realScaleType:"band"}:{scale:Xt(),realScaleType:"linear"};if(xo()(i)){var l="scale".concat(ko()(i));return{scale:(n[l]||o.z)(),realScaleType:n[l]?l:"point"}}return bo()(i)?{scale:i}:{scale:o.z(),realScaleType:"point"}},_i=1e-4,Ci=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-_i,i=Math.max(n[0],n[1])+_i,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},Ii=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},Di=function(t,e){if(!e||2!==e.length||!(0,ai.Et)(e[0])||!(0,ai.Et)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,ai.Et)(t[0])||t[0]<r)&&(o[0]=r),(!(0,ai.Et)(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},Ni={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=Po()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}oo(t,e)}},none:oo,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}oo(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,oo(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=Po()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},Bi=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=Ni[r],i=function(){var t=(0,ao.A)([]),e=co,r=oo,n=uo;function o(o){var i,a,c=Array.from(t.apply(this,arguments),lo),u=c.length,l=-1;for(const t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=(0,io.A)(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"===typeof e?e:(0,ao.A)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"===typeof t?t:(0,ao.A)(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?co:"function"===typeof t?t:(0,ao.A)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?oo:t,o):r},o}().keys(n).value(function(t,e){return+yi(t,e,0)}).order(co).offset(o);return i(t)},Ri=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?hi(hi({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if((0,ai.vh)(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[(0,ai.NF)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return hi(hi({},t),{},di({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];if(c.hasStack){c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return hi(hi({},e),{},di({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:Bi(t,a.items,o)}))},{})}return hi(hi({},e),{},di({},i,c))},{})},Li=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=ni(u,o,a);return t.domain([ho()(l),fo()(l)]),{niceTicks:l}}if(o&&"number"===n){var s=t.domain();return{niceTicks:oi(s,o,a)}}return null};function zi(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!vo()(o[e.dataKey])){var c=(0,ai.eP)(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=yi(o,vo()(a)?e.dataKey:a);return vo()(u)?null:e.scale(u)}var Fi=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=yi(i,e.dataKey,e.domain[a]);return vo()(c)?null:e.scale(c)-o/2+n},Ui=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},Wi=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?hi(hi({},t.type.defaultProps),t.props):t.props).stackId;if((0,ai.vh)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},qi=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[ho()(e.concat([t[0]]).filter(ai.Et)),fo()(e.concat([t[1]]).filter(ai.Et))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},Xi=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Hi=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Vi=function(t,e,r){if(bo()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,ai.Et)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(Xi.test(t[0])){var o=+Xi.exec(t[0])[1];n[0]=e[0]-o}else bo()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,ai.Et)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(Hi.test(t[1])){var i=+Hi.exec(t[1])[1];n[1]=e[1]+i}else bo()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},Ki=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=Co()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},Gi=function(t,e,r){return t&&t.length?To()(t,Oo()(r,"type.defaultProps.domain"))?e:t:e},Ji=function(t,e){var r=t.type.defaultProps?hi(hi({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return hi(hi({},(0,ci.J9)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:bi(t),value:yi(e,n),type:c,payload:e,chartType:u,hide:l})}},240:(t,e,r)=>{"use strict";r.d(e,{AW:()=>L,BU:()=>k,J9:()=>I,Me:()=>M,Mn:()=>j,OV:()=>D,X_:()=>R,aS:()=>E,ee:()=>B,sT:()=>C});var n=r(3097),o=r.n(n),i=r(9686),a=r.n(i),c=r(620),u=r.n(c),l=r(1629),s=r.n(l),f=r(6686),p=r.n(f),h=r(5043),d=r(9062),y=r(6307),v=r(5248),m=r(7287),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function w(t){return w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},w(t)}var O={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},j=function(t){return"string"===typeof t?t:t?t.displayName||t.name||"Component":""},A=null,S=null,P=function t(e){if(e===A&&Array.isArray(S))return S;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),S=r,A=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return j(t)}):[j(e)],P(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function k(t,e){var r=E(t,e);return r&&r[0]}var M=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!(!(0,y.Et)(r)||r<=0||!(0,y.Et)(n)||n<=0)},T=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],_=function(t){return t&&t.type&&u()(t.type)&&T.indexOf(t.type)>=0},C=function(t){return t&&"object"===w(t)&&"clipDot"in t},I=function(t,e,r){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;(function(t,e,r,n){var o,i=null!==(o=null===m.VU||void 0===m.VU?void 0:m.VU[n])&&void 0!==o?o:[];return e.startsWith("data-")||!s()(t)&&(n&&i.includes(e)||m.QQ.includes(e))||r&&m.j2.includes(e)})(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},D=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return N(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!N(i,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=x(r,b),i=e.props||{},c=i.children,u=x(i,g);return n&&c?(0,v.b)(o,u)&&D(n,c):!n&&!c&&(0,v.b)(o,u)}return!1},B=function(t,e){var r=[],n={};return P(t).forEach(function(t,o){if(_(t))r.push(t);else if(t){var i=j(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var l=c(t,i,o);r.push(l),n[i]=!0}}}),r},R=function(t){var e=t&&t.type;return e&&O[e]?O[e]:null},L=function(t,e){return P(e).indexOf(t)}},320:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},396:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},438:(t,e,r)=>{var n=r(2622);t.exports=function(t){return n(this,t).get(t)}},485:(t,e,r)=>{var n=r(2969);t.exports=function(t){return function(e){return n(e,t)}}},539:(t,e,r)=>{var n=r(9742),o=r(7498),i=r(3279);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},620:(t,e,r)=>{var n=r(6913),o=r(4052),i=r(2761);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},643:(t,e,r)=>{var n=r(7676)("toUpperCase");t.exports=n},644:t=>{t.exports=function(t){return t!==t}},677:(t,e,r)=>{"use strict";r.d(e,{h:()=>v});var n=r(5043),o=r(8387),i=r(240),a=r(165),c=r(6307);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s(Object(r),!0).forEach(function(e){p(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function p(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var h=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,c=t.isExternal,u=t.cornerRadius,l=t.cornerIsExternal,s=u*(c?1:-1)+n,f=Math.asin(u/s)/a.Kg,p=l?o:o+i*f,h=l?o-i*f:o;return{center:(0,a.IZ)(e,r,s,p),circleTangency:(0,a.IZ)(e,r,n,p),lineTangency:(0,a.IZ)(e,r,s*Math.cos(f*a.Kg),h),theta:f}},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,u=function(t,e){return(0,c.sA)(e-t)*Math.min(Math.abs(e-t),359.999)}(i,t.endAngle),l=i+u,s=(0,a.IZ)(e,r,o,i),f=(0,a.IZ)(e,r,o,l),p="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(i>l),",\n    ").concat(f.x,",").concat(f.y,"\n  ");if(n>0){var h=(0,a.IZ)(e,r,n,i),d=(0,a.IZ)(e,r,n,l);p+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(i<=l),",\n            ").concat(h.x,",").concat(h.y," Z")}else p+="L ".concat(e,",").concat(r," Z");return p},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e=f(f({},y),t),r=e.cx,a=e.cy,u=e.innerRadius,s=e.outerRadius,p=e.cornerRadius,v=e.forceCornerRadius,m=e.cornerIsExternal,b=e.startAngle,g=e.endAngle,x=e.className;if(s<u||b===g)return null;var w,O=(0,o.A)("recharts-sector",x),j=s-u,A=(0,c.F4)(p,j,0,!0);return w=A>0&&Math.abs(b-g)<360?function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,c.sA)(s-l),p=h({cx:e,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:u}),y=p.circleTangency,v=p.lineTangency,m=p.theta,b=h({cx:e,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:u}),g=b.circleTangency,x=b.lineTangency,w=b.theta,O=u?Math.abs(l-s):Math.abs(l-s)-m-w;if(O<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*-i,",0\n      "):d({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(O>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var A=h({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),S=A.circleTangency,P=A.lineTangency,E=A.theta,k=h({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),M=k.circleTangency,T=k.lineTangency,_=k.theta,C=u?Math.abs(l-s):Math.abs(l-s)-E-_;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(T.x,",").concat(T.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(S.x,",").concat(S.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(P.x,",").concat(P.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j}({cx:r,cy:a,innerRadius:u,outerRadius:s,cornerRadius:Math.min(A,j/2),forceCornerRadius:v,cornerIsExternal:m,startAngle:b,endAngle:g}):d({cx:r,cy:a,innerRadius:u,outerRadius:s,startAngle:b,endAngle:g}),n.createElement("path",l({},(0,i.J9)(e,!0),{className:O,d:w,role:"img"}))}},705:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},715:t=>{var e="\\ud800-\\udfff",r="["+e+"]",n="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",o="\\ud83c[\\udffb-\\udfff]",i="[^"+e+"]",a="(?:\\ud83c[\\udde6-\\uddff]){2}",c="[\\ud800-\\udbff][\\udc00-\\udfff]",u="(?:"+n+"|"+o+")"+"?",l="[\\ufe0e\\ufe0f]?",s=l+u+("(?:\\u200d(?:"+[i,a,c].join("|")+")"+l+u+")*"),f="(?:"+[i+n+"?",n,a,c,r].join("|")+")",p=RegExp(o+"(?="+o+")|"+f+s,"g");t.exports=function(t){return t.match(p)||[]}},755:(t,e,r)=>{var n=r(8895),o=r(7116);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},793:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},801:(t,e,r)=>{var n=r(1141),o=r(6686),i=r(9841),a=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,u=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return NaN;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=c.test(t);return r||u.test(t)?l(t.slice(2),r?2:8):a.test(t)?NaN:+t}},877:(t,e,r)=>{"use strict";r.d(e,{Q:()=>u});var n=r(8420),o=r(3839),i=r(2185),a=r(6026),c=r(3831),u=(0,n.gu)({chartName:"AreaChart",GraphicalChild:o.G,axisComponents:[{axisType:"xAxis",AxisComp:i.W},{axisType:"yAxis",AxisComp:a.h}],formatAxisMap:c.pr})},879:(t,e,r)=>{"use strict";r.d(e,{yp:()=>R,GG:()=>X,NE:()=>L,nZ:()=>z,xQ:()=>F});var n=r(5043),o=r(1629),i=r.n(o),a=r(2322),c=r.n(a),u=r(6361),l=r.n(u),s=r(9853),f=r.n(s),p=r(4342),h=r(8387),d=r(1744),y=r(240);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}function m(){return m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},m.apply(this,arguments)}function b(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return g(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach(function(e){O(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function O(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var j=function(t,e,r,n,o){var i,a=r-n;return i="M ".concat(t,",").concat(e),i+="L ".concat(t+r,",").concat(e),i+="L ".concat(t+r-a/2,",").concat(e+o),i+="L ".concat(t+r-a/2-n,",").concat(e+o),i+="L ".concat(t,",").concat(e," Z")},A={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},S=function(t){var e=w(w({},A),t),r=(0,n.useRef)(),o=b((0,n.useState)(-1),2),i=o[0],a=o[1];(0,n.useEffect)(function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&a(t)}catch(e){}},[]);var c=e.x,u=e.y,l=e.upperWidth,s=e.lowerWidth,f=e.height,p=e.className,v=e.animationEasing,g=e.animationDuration,x=e.animationBegin,O=e.isUpdateAnimationActive;if(c!==+c||u!==+u||l!==+l||s!==+s||f!==+f||0===l&&0===s||0===f)return null;var S=(0,h.A)("recharts-trapezoid",p);return O?n.createElement(d.Ay,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:f,x:c,y:u},to:{upperWidth:l,lowerWidth:s,height:f,x:c,y:u},duration:g,animationEasing:v,isActive:O},function(t){var o=t.upperWidth,a=t.lowerWidth,c=t.height,u=t.x,l=t.y;return n.createElement(d.Ay,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:g,easing:v},n.createElement("path",m({},(0,y.J9)(e,!0),{className:S,d:j(u,l,o,a,c),ref:r})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.J9)(e,!0),{className:S,d:j(c,u,l,s,f)})))},P=r(677),E=r(1639),k=r(1985),M=["option","shapeType","propTransformer","activeClassName","isActive"];function T(t){return T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},T(t)}function _(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function C(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function I(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?C(Object(r),!0).forEach(function(e){D(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):C(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function D(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=T(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=T(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==T(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function N(t,e){return I(I({},e),t)}function B(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.M,r);case"trapezoid":return n.createElement(S,r);case"sector":return n.createElement(P.h,r);case"symbols":if(function(t){return"symbols"===t}(e))return n.createElement(k.i,r);break;default:return null}}function R(t){var e,r=t.option,o=t.shapeType,a=t.propTransformer,u=void 0===a?N:a,s=t.activeClassName,f=void 0===s?"recharts-active-shape":s,p=t.isActive,h=_(t,M);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,I(I({},h),function(t){return(0,n.isValidElement)(t)?t.props:t}(r)));else if(i()(r))e=r(h);else if(c()(r)&&!l()(r)){var d=u(r,h);e=n.createElement(B,{shapeType:o,elementProps:d})}else{var y=h;e=n.createElement(B,{shapeType:o,elementProps:y})}return p?n.createElement(E.W,{className:f},e):e}function L(t,e){return null!=e&&"trapezoids"in t.props}function z(t,e){return null!=e&&"sectors"in t.props}function F(t,e){return null!=e&&"points"in t.props}function U(t,e){var r,n,o=t.x===(null===e||void 0===e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null===e||void 0===e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function W(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function q(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function X(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,o=function(t,e){var r;return L(t,e)?r="trapezoids":z(t,e)?r="sectors":F(t,e)&&(r="points"),r}(r,e),i=function(t,e){var r,n;return L(t,e)?null===(r=e.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:z(t,e)?null===(n=e.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:F(t,e)?e.payload:{}}(r,e),a=n.filter(function(t,n){var a=f()(i,t),c=r.props[o].filter(function(t){var n=function(t,e){var r;return L(t,e)?r=U:z(t,e)?r=W:F(t,e)&&(r=q),r}(r,e);return n(t,e)}),u=r.props[o].indexOf(c[c.length-1]);return a&&n===u});return n.indexOf(a[a.length-1])}},914:(t,e,r)=>{var n=r(9841);t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}},929:(t,e,r)=>{var n=r(3211),o=r(6571),i=r(9194),a=r(6686);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return!!("number"==c?o(r)&&i(e,r.length):"string"==c&&e in r)&&n(r[e],t)}},977:(t,e,r)=>{var n=r(9096),o=r(4416);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},1069:(t,e,r)=>{var n=r(8541);t.exports=function(t){return null==t?"":n(t)}},1141:(t,e,r)=>{var n=r(143),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},1143:(t,e,r)=>{var n=r(3028)(Object.keys,Object);t.exports=n},1170:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},1268:(t,e,r)=>{var n=r(5428),o=r(7574),i=r(6832),a=i&&i.isTypedArray,c=a?o(a):n;t.exports=c},1310:t=>{t.exports=function(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in Object(r)))}}},1327:(t,e,r)=>{"use strict";r.d(e,{s:()=>L});var n=r(5043),o=r(1629),i=r.n(o),a=r(8387),c=r(155),u=r(4794),l=r(1985),s=r(7287);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(){return p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},p.apply(this,arguments)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,x(n.key),n)}}function y(t,e,r){return e=m(e),function(t,e){if(e&&("object"===f(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,v()?Reflect.construct(e,r||[],m(t).constructor):e.apply(t,r))}function v(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(v=function(){return!!t})()}function m(t){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},m(t)}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function g(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function x(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var w=32,O=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),y(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&b(t,e)}(e,t),r=e,o=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=16,o=w/6,i=w/3,a=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:a,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:r,x2:w,y2:r,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:a,d:"M0,".concat(r,"h").concat(i,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(2*i,",").concat(r,"\n            H").concat(w,"M").concat(2*i,",").concat(r,"\n            A").concat(o,",").concat(o,",0,1,1,").concat(i,",").concat(r),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:a,d:"M0,".concat(4,"h").concat(w,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var c=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){g(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete c.legendIcon,n.cloneElement(t.legendIcon,c)}return n.createElement(l.i,{fill:a,cx:r,cy:r,size:w,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,l=e.layout,f=e.formatter,h=e.inactiveColor,d={x:0,y:0,width:w,height:w},y={display:"horizontal"===l?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var l=e.formatter||f,m=(0,a.A)(g(g({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var b=i()(e.value)?null:e.value;(0,c.R)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?h:e.color;return n.createElement("li",p({className:m,style:y,key:"legend-item-".concat(r)},(0,s.XC)(t.props,e,r)),n.createElement(u.u,{width:o,height:o,viewBox:d,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},l?l(b,e,r):b))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;if(!e||!e.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===r?o:"left"};return n.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}],o&&d(r.prototype,o),f&&d(r,f),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,f}(n.PureComponent);g(O,"displayName","Legend"),g(O,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var j=r(6307),A=r(2598);function S(t){return S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},S(t)}var P=["ref"];function E(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function k(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?E(Object(r),!0).forEach(function(e){D(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function M(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,N(n.key),n)}}function T(t,e,r){return e=C(e),function(t,e){if(e&&("object"===S(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,_()?Reflect.construct(e,r||[],C(t).constructor):e.apply(t,r))}function _(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(_=function(){return!!t})()}function C(t){return C=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},C(t)}function I(t,e){return I=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},I(t,e)}function D(t,e,r){return(e=N(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function N(t){var e=function(t,e){if("object"!=S(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=S(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==S(e)?e:e+""}function B(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function R(t){return t.value}var L=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return D(t=T(this,e,[].concat(n)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&I(t,e)}(e,t),r=e,i=[{key:"getWithHeight",value:function(t,e){var r=k(k({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,j.Et)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],(o=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?k({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),k(k({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=k(k({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"===typeof t)return n.createElement(t,e);e.ref;var r=B(e,P);return n.createElement(O,r)}(r,k(k({},this.props),{},{payload:(0,A.s)(u,c,R)})))}}])&&M(r.prototype,o),i&&M(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent);D(L,"displayName","Legend"),D(L,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},1340:(t,e,r)=>{var n=r(3211);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1}},1497:(t,e,r)=>{"use strict";var n=r(3218);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},1519:(t,e,r)=>{"use strict";r.d(e,{Z:()=>E});var n=r(5043),o=r(9686),i=r.n(o),a=r(6686),c=r.n(a),u=r(1629),l=r.n(u),s=r(4065),f=r.n(s),p=r(2647),h=r(1639),d=r(240),y=r(202);function v(t){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=["valueAccessor"],b=["data","dataKey","clockWise","id","textBreakAll"];function g(t){return function(t){if(Array.isArray(t))return x(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return x(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return x(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function w(){return w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},w.apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){A(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var P=function(t){return Array.isArray(t.value)?f()(t.value):t.value};function E(t){var e=t.valueAccessor,r=void 0===e?P:e,o=S(t,m),a=o.data,c=o.dataKey,u=o.clockWise,l=o.id,s=o.textBreakAll,f=S(o,b);return a&&a.length?n.createElement(h.W,{className:"recharts-label-list"},a.map(function(t,e){var o=i()(c)?r(t,e):(0,y.kr)(t&&t.payload,c),a=i()(l)?{}:{id:"".concat(l,"-").concat(e)};return n.createElement(p.J,w({},(0,d.J9)(t,!0),f,a,{parentViewBox:t.parentViewBox,value:o,textBreakAll:s,viewBox:p.J.parseViewBox(i()(u)?t:j(j({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))})):null}E.displayName="LabelList",E.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var o=t.children,i=(0,d.aS)(o,E).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return r?[function(t,e){return t?!0===t?n.createElement(E,{key:"labelList-implicit",data:e}):n.isValidElement(t)||l()(t)?n.createElement(E,{key:"labelList-implicit",data:e,content:t}):c()(t)?n.createElement(E,w({data:e},t,{key:"labelList-implicit"})):null:null}(t.label,e)].concat(g(i)):i}},1558:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},1629:(t,e,r)=>{var n=r(6913),o=r(6686);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},1639:(t,e,r)=>{"use strict";r.d(e,{W:()=>l});var n=r(5043),o=r(8387),i=r(240),a=["children","className"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var l=n.forwardRef(function(t,e){var r=t.children,l=t.className,s=u(t,a),f=(0,o.A)("recharts-layer",l);return n.createElement("g",c({className:f},(0,i.J9)(s,!0),{ref:e}),r)})},1714:(t,e,r)=>{var n=r(1340);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},1733:(t,e,r)=>{var n=r(1775),o=r(4664),i=r(9096);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},1744:(t,e,r)=>{"use strict";r.d(e,{Ay:()=>Ct});var n=r(5043),o=r(5173),i=r.n(o),a=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty;function l(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function s(t){return function(e,r,n){if(!e||!r||"object"!==typeof e||"object"!==typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function f(t){return a(t).concat(c(t))}var p=Object.hasOwn||function(t,e){return u.call(t,e)};function h(t,e){return t===e||!t&&!e&&t!==t&&e!==e}var d=Object.getOwnPropertyDescriptor,y=Object.keys;function v(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function m(t,e){return h(t.getTime(),e.getTime())}function b(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function g(t,e){return t===e}function x(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),c=t.entries(),u=0;(o=c.next())&&!o.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;)if(a[f])f++;else{var p=o.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}var w=h;function O(t,e,r){var n=y(t),o=n.length;if(y(e).length!==o)return!1;for(;o-- >0;)if(!M(t,e,r,n[o]))return!1;return!0}function j(t,e,r){var n,o,i,a=f(t),c=a.length;if(f(e).length!==c)return!1;for(;c-- >0;){if(!M(t,e,r,n=a[c]))return!1;if(o=d(t,n),i=d(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable))return!1}return!0}function A(t,e){return h(t.valueOf(),e.valueOf())}function S(t,e){return t.source===e.source&&t.flags===e.flags}function P(t,e,r){var n=t.size;if(n!==e.size)return!1;if(!n)return!0;for(var o,i,a=new Array(n),c=t.values();(o=c.next())&&!o.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(o.value,i.value,o.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function E(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function k(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function M(t,e,r,n){return!("_owner"!==n&&"__o"!==n&&"__v"!==n||!t.$$typeof&&!e.$$typeof)||p(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var T=Array.isArray,_="function"===typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,C=Object.assign,I=Object.prototype.toString.call.bind(Object.prototype.toString);var D=N();N({strict:!0}),N({circular:!0}),N({circular:!0,strict:!0}),N({createInternalComparator:function(){return h}}),N({strict:!0,createInternalComparator:function(){return h}}),N({circular:!0,createInternalComparator:function(){return h}}),N({circular:!0,createInternalComparator:function(){return h},strict:!0});function N(t){void 0===t&&(t={});var e,r=t.circular,n=void 0!==r&&r,o=t.createInternalComparator,i=t.createState,a=t.strict,c=void 0!==a&&a,u=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?j:v,areDatesEqual:m,areErrorsEqual:b,areFunctionsEqual:g,areMapsEqual:n?l(x,j):x,areNumbersEqual:w,areObjectsEqual:n?j:O,arePrimitiveWrappersEqual:A,areRegExpsEqual:S,areSetsEqual:n?l(P,j):P,areTypedArraysEqual:n?j:E,areUrlsEqual:k};if(r&&(o=C({},o,r(o))),e){var i=s(o.areArraysEqual),a=s(o.areMapsEqual),c=s(o.areObjectsEqual),u=s(o.areSetsEqual);o=C({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t),f=function(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areErrorsEqual,o=t.areFunctionsEqual,i=t.areMapsEqual,a=t.areNumbersEqual,c=t.areObjectsEqual,u=t.arePrimitiveWrappersEqual,l=t.areRegExpsEqual,s=t.areSetsEqual,f=t.areTypedArraysEqual,p=t.areUrlsEqual;return function(t,h,d){if(t===h)return!0;if(null==t||null==h)return!1;var y=typeof t;if(y!==typeof h)return!1;if("object"!==y)return"number"===y?a(t,h,d):"function"===y&&o(t,h,d);var v=t.constructor;if(v!==h.constructor)return!1;if(v===Object)return c(t,h,d);if(T(t))return e(t,h,d);if(null!=_&&_(t))return f(t,h,d);if(v===Date)return r(t,h,d);if(v===RegExp)return l(t,h,d);if(v===Map)return i(t,h,d);if(v===Set)return s(t,h,d);var m=I(t);return"[object Date]"===m?r(t,h,d):"[object RegExp]"===m?l(t,h,d):"[object Map]"===m?i(t,h,d):"[object Set]"===m?s(t,h,d):"[object Object]"===m?"function"!==typeof t.then&&"function"!==typeof h.then&&c(t,h,d):"[object URL]"===m?p(t,h,d):"[object Error]"===m?n(t,h,d):"[object Arguments]"===m?c(t,h,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&u(t,h,d)}}(u);return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache,l=void 0===u?e?new WeakMap:void 0:u,s=c.meta;return r(t,a,{cache:l,equals:o,meta:s,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:n,comparator:f,createState:i,equals:o?o(f):(e=f,function(t,r,n,o,i,a,c){return e(t,r,c)}),strict:c})}function B(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){r<0&&(r=o),o-r>e?(t(o),r=-1):function(t){"undefined"!==typeof requestAnimationFrame&&requestAnimationFrame(t)}(n)})}function R(t){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},R(t)}function L(t){return function(t){if(Array.isArray(t))return t}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return z(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return z(t,e)}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function z(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function F(){var t=function(){return null},e=!1,r=function r(n){if(!e){if(Array.isArray(n)){if(!n.length)return;var o=L(n),i=o[0],a=o.slice(1);return"number"===typeof i?void B(r.bind(null,a),i):(r(i),void B(r.bind(null,a)))}"object"===R(n)&&t(n),"function"===typeof n&&n()}};return{stop:function(){e=!0},start:function(t){e=!1,r(t)},subscribe:function(e){return t=e,function(){t=function(){return null}}}}}function U(t){return U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},U(t)}function W(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function q(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?W(Object(r),!0).forEach(function(e){X(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):W(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function X(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==U(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==U(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===U(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var H=function(t){return t},V=function(t,e){return Object.keys(e).reduce(function(r,n){return q(q({},r),{},X({},n,t(n,e[n])))},{})},K=function(t,e,r){return t.map(function(t){return"".concat((n=t,n.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())}))," ").concat(e,"ms ").concat(r);var n}).join(",")};function G(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||Y(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function J(t){return function(t){if(Array.isArray(t))return Z(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Y(t,e){if(t){if("string"===typeof t)return Z(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Z(t,e):void 0}}function Z(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var $=1e-4,Q=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},tt=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},et=function(t,e){return function(r){var n=Q(t,e);return tt(n,r)}},rt=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0],o=e[1],i=e[2],a=e[3];if(1===e.length)switch(e[0]){case"linear":n=0,o=0,i=1,a=1;break;case"ease":n=.25,o=.1,i=.25,a=1;break;case"ease-in":n=.42,o=0,i=1,a=1;break;case"ease-out":n=.42,o=0,i=.58,a=1;break;case"ease-in-out":n=0,o=0,i=.58,a=1;break;default:var c=e[0].split("(");if("cubic-bezier"===c[0]&&4===c[1].split(")")[0].split(",").length){var u=G(c[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}),4);n=u[0],o=u[1],i=u[2],a=u[3]}}[n,i,o,a].every(function(t){return"number"===typeof t&&t>=0&&t<=1});var l,s,f=et(n,i),p=et(o,a),h=(l=n,s=i,function(t){var e=Q(l,s),r=[].concat(J(e.map(function(t,e){return t*e}).slice(1)),[0]);return tt(r,t)}),d=function(t){return t>1?1:t<0?0:t},y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o=f(r)-e,i=h(r);if(Math.abs(o-e)<$||i<$)return p(r);r=d(r-o/i)}return p(r)};return y.isStepper=!1,y},nt=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"===typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rt(n);case"spring":return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return Math.abs(c-e)<$&&Math.abs(i)<$?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c}();default:if("cubic-bezier"===n.split("(")[0])return rt(n)}return"function"===typeof n?n:null};function ot(t){return ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ot(t)}function it(t){return function(t){if(Array.isArray(t))return ft(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||st(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function at(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ct(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?at(Object(r),!0).forEach(function(e){ut(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):at(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ut(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==ot(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ot(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===ot(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||st(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function st(t,e){if(t){if("string"===typeof t)return ft(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ft(t,e):void 0}}function ft(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var pt=function(t,e,r){return t+(e-t)*r},ht=function(t){return t.from!==t.to},dt=function t(e,r,n){var o=V(function(t,r){if(ht(r)){var n=lt(e(r.from,r.to,r.velocity),2),o=n[0],i=n[1];return ct(ct({},r),{},{from:o,velocity:i})}return r},r);return n<1?V(function(t,e){return ht(e)?ct(ct({},e),{},{velocity:pt(e.velocity,o[t].velocity,n),from:pt(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};const yt=function(t,e,r,n,o){var i,a,c,u,l=(i=t,a=e,[Object.keys(i),Object.keys(a)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})})),s=l.reduce(function(r,n){return ct(ct({},r),{},ut({},n,[t[n],e[n]]))},{}),f=l.reduce(function(r,n){return ct(ct({},r),{},ut({},n,{from:t[n],velocity:0,to:e[n]}))},{}),p=-1,h=function(){return null};return h=r.isStepper?function(n){c||(c=n);var i=(n-c)/r.dt;f=dt(r,f,i),o(ct(ct(ct({},t),e),V(function(t,e){return e.from},f))),c=n,Object.values(f).filter(ht).length&&(p=requestAnimationFrame(h))}:function(i){u||(u=i);var a=(i-u)/n,c=V(function(t,e){return pt.apply(void 0,it(e).concat([r(a)]))},s);if(o(ct(ct(ct({},t),e),c)),a<1)p=requestAnimationFrame(h);else{var l=V(function(t,e){return pt.apply(void 0,it(e).concat([r(1)]))},s);o(ct(ct(ct({},t),e),l))}},function(){return requestAnimationFrame(h),function(){cancelAnimationFrame(p)}}};function vt(t){return vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vt(t)}var mt=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function bt(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function gt(t){return function(t){if(Array.isArray(t))return xt(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return xt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xt(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function wt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Ot(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?wt(Object(r),!0).forEach(function(e){jt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):wt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function jt(t,e,r){return(e=St(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function At(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,St(n.key),n)}}function St(t){var e=function(t,e){if("object"!==vt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==vt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===vt(e)?e:String(e)}function Pt(t,e){return Pt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Pt(t,e)}function Et(t){var e=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}();return function(){var r,n=Tt(t);if(e){var o=Tt(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return kt(this,r)}}function kt(t,e){if(e&&("object"===vt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Mt(t)}function Mt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Tt(t){return Tt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Tt(t)}var _t=function(t){!function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Pt(t,e)}(a,t);var e,r,o,i=Et(a);function a(t,e){var r;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a);var n=(r=i.call(this,t,e)).props,o=n.isActive,c=n.attributeName,u=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(Mt(r)),r.changeStyle=r.changeStyle.bind(Mt(r)),!o||p<=0)return r.state={style:{}},"function"===typeof f&&(r.state={style:l}),kt(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"===typeof f)return r.state={style:u},kt(r);r.state={style:c?jt({},c,u):u}}else r.state={style:{}};return r}return e=a,(r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n)if(r){if(!(D(t.to,a)&&t.canBegin&&t.isActive)){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=l||i?c:t.to;if(this.state&&u){var f={style:o?jt({},o,s):s};(o&&u[o]!==s||!o&&u!==s)&&this.setState(f)}this.runAnimation(Ot(Ot({},this.props),{},{from:s,begin:0}))}}else{var p={style:o?jt({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(p)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=yt(r,n,nt(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration,u=void 0===c?0:c;return this.manager.start([o].concat(gt(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"===typeof c||"spring"===c)return[].concat(gt(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=K(p,i,c),d=Ot(Ot(Ot({},f.style),u),{},{transition:h});return[].concat(gt(t),[d,i,s]).filter(H)},[a,Math.max(u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=F());var e=t.begin,r=t.duration,n=t.attributeName,o=t.to,i=t.easing,a=t.onAnimationStart,c=t.onAnimationEnd,u=t.steps,l=t.children,s=this.manager;if(this.unSubscribe=s.subscribe(this.handleStyleChange),"function"!==typeof i&&"function"!==typeof l&&"spring"!==i)if(u.length>1)this.runStepAnimation(t);else{var f=n?jt({},n,o):o,p=K(Object.keys(f),r,i);s.start([a,e,Ot(Ot({},f),{},{transition:p}),r,c])}else this.runJSAnimation(t)}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,bt(t,mt)),a=n.Children.count(e),c=this.state.style;if("function"===typeof e)return e(c);if(!o||0===a||r<=0)return e;var u=function(t){var e=t.props,r=e.style,o=void 0===r?{}:r,a=e.className;return(0,n.cloneElement)(t,Ot(Ot({},i),{},{style:Ot(Ot({},o),c),className:a}))};return 1===a?u(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return u(t)}))}}])&&At(e.prototype,r),o&&At(e,o),Object.defineProperty(e,"prototype",{writable:!1}),a}(n.PureComponent);_t.displayName="Animate",_t.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},_t.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};const Ct=_t},1775:(t,e,r)=>{var n=r(5654);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},1946:(t,e,r)=>{var n=r(1340);t.exports=function(t){return n(this.__data__,t)>-1}},1985:(t,e,r)=>{"use strict";r.d(e,{i:()=>U});var n=r(5043),o=r(643),i=r.n(o);Math.abs,Math.atan2;const a=Math.cos,c=(Math.max,Math.min,Math.sin),u=Math.sqrt,l=Math.PI,s=2*l;const f={draw(t,e){const r=u(e/l);t.moveTo(r,0),t.arc(0,0,r,0,s)}},p={draw(t,e){const r=u(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},h=u(1/3),d=2*h,y={draw(t,e){const r=u(e/d),n=r*h;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},v={draw(t,e){const r=u(e),n=-r/2;t.rect(n,n,r,r)}},m=c(l/10)/c(7*l/10),b=c(s/10)*m,g=-a(s/10)*m,x={draw(t,e){const r=u(.8908130915292852*e),n=b*r,o=g*r;t.moveTo(0,-r),t.lineTo(n,o);for(let i=1;i<5;++i){const e=s*i/5,u=a(e),l=c(e);t.lineTo(l*r,-u*r),t.lineTo(u*n-l*o,l*n+u*o)}t.closePath()}},w=u(3),O={draw(t,e){const r=-u(e/(3*w));t.moveTo(0,2*r),t.lineTo(-w*r,-r),t.lineTo(w*r,-r),t.closePath()}},j=-.5,A=u(3)/2,S=1/u(12),P=3*(S/2+1),E={draw(t,e){const r=u(e/P),n=r/2,o=r*S,i=n,a=r*S+r,c=-i,l=a;t.moveTo(n,o),t.lineTo(i,a),t.lineTo(c,l),t.lineTo(j*n-A*o,A*n+j*o),t.lineTo(j*i-A*a,A*i+j*a),t.lineTo(j*c-A*l,A*c+j*l),t.lineTo(j*n+A*o,j*o-A*n),t.lineTo(j*i+A*a,j*a-A*i),t.lineTo(j*c+A*l,j*l-A*c),t.closePath()}};var k=r(3809),M=r(7371);u(3),u(3);var T=r(8387),_=r(240);function C(t){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}var I=["type","size","sizeType"];function D(){return D=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},D.apply(this,arguments)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function R(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=C(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=C(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==C(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function L(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var z={symbolCircle:f,symbolCross:p,symbolDiamond:y,symbolSquare:v,symbolStar:x,symbolTriangle:O,symbolWye:E},F=Math.PI/180,U=function(t){var e=t.type,r=void 0===e?"circle":e,o=t.size,a=void 0===o?64:o,c=t.sizeType,u=void 0===c?"area":c,l=B(B({},L(t,I)),{},{type:r,size:a,sizeType:u}),s=l.className,p=l.cx,h=l.cy,d=(0,_.J9)(l,!0);return p===+p&&h===+h&&a===+a?n.createElement("path",D({},d,{className:(0,T.A)("recharts-symbols",s),transform:"translate(".concat(p,", ").concat(h,")"),d:function(){var t=function(t){var e="symbol".concat(i()(t));return z[e]||f}(r),e=function(t,e){let r=null,n=(0,M.i)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"===typeof t?t:(0,k.A)(t||f),e="function"===typeof e?e:(0,k.A)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"===typeof e?e:(0,k.A)(e),o):t},o.size=function(t){return arguments.length?(e="function"===typeof t?t:(0,k.A)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o}().type(t).size(function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return.5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*F;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}}(a,u,r));return e()}()})):null};U.registerSymbol=function(t,e){z["symbol".concat(i()(t))]=e}},2070:(t,e,r)=>{var n=r(7937)(r(6552),"Set");t.exports=n},2074:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2099:(t,e,r)=>{"use strict";r.d(e,{A:()=>i,z:()=>c});var n=r(4402),o=r(5186);function i(){var t,e,r=(0,o.A)().unknown(void 0),a=r.domain,c=r.range,u=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<u,o=n?l:u,i=n?u:l;t=(i-o)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(r-f))*h,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var d=function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=new Array(o);++n<o;)i[n]=t+n*r;return i}(r).map(function(e){return o+t*e});return c(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([u,l]=t,u=+u,l=+l,d()):[u,l]},r.rangeRound=function(t){return[u,l]=t,u=+u,l=+l,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return i(a(),[u,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.C.apply(d(),arguments)}function a(t){var e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return a(e())},t}function c(){return a(i.apply(null,arguments).paddingInner(1))}},2103:(t,e,r)=>{"use strict";r.d(e,{DR:()=>g,pj:()=>O,rY:()=>k,yi:()=>E,Yp:()=>x,hj:()=>P,sk:()=>S,AF:()=>w,Nk:()=>A,$G:()=>j});var n=r(5043),o=r(3404),i=r(8990),a=r.n(i),c=r(7002),u=r.n(c),l=r(5797),s=r.n(l)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),f=r(6307);var p=(0,n.createContext)(void 0),h=(0,n.createContext)(void 0),d=(0,n.createContext)(void 0),y=(0,n.createContext)({}),v=(0,n.createContext)(void 0),m=(0,n.createContext)(0),b=(0,n.createContext)(0),g=function(t){var e=t.state,r=e.xAxisMap,o=e.yAxisMap,i=e.offset,a=t.clipPathId,c=t.children,u=t.width,l=t.height,f=s(i);return n.createElement(p.Provider,{value:r},n.createElement(h.Provider,{value:o},n.createElement(y.Provider,{value:i},n.createElement(d.Provider,{value:f},n.createElement(v.Provider,{value:a},n.createElement(m.Provider,{value:l},n.createElement(b.Provider,{value:u},c)))))))},x=function(){return(0,n.useContext)(v)};var w=function(t){var e=(0,n.useContext)(p);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},O=function(){var t=(0,n.useContext)(p);return(0,f.lX)(t)},j=function(){var t=(0,n.useContext)(h);return a()(t,function(t){return u()(t.domain,Number.isFinite)})||(0,f.lX)(t)},A=function(t){var e=(0,n.useContext)(h);null==e&&(0,o.A)(!1);var r=e[t];return null==r&&(0,o.A)(!1),r},S=function(){return(0,n.useContext)(d)},P=function(){return(0,n.useContext)(y)},E=function(){return(0,n.useContext)(b)},k=function(){return(0,n.useContext)(m)}},2154:(t,e,r)=>{var n=r(5575),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},2165:(t,e,r)=>{var n=r(5652);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},2185:(t,e,r)=>{"use strict";r.d(e,{W:()=>b});var n=r(5043),o=r(8387),i=r(2103),a=r(7671),c=r(202);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function s(t,e,r){return e=p(e),function(t,e){if(e&&("object"===u(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,f()?Reflect.construct(e,r||[],p(t).constructor):e.apply(t,r))}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},p(t)}function h(t,e){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},h(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function v(){return v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},v.apply(this,arguments)}function m(t){var e=t.xAxisId,r=(0,i.yi)(),u=(0,i.rY)(),l=(0,i.AF)(e);return null==l?null:n.createElement(a.u,v({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))}var b=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),s(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(m,this.props)}}])&&l(r.prototype,o),i&&l(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);d(b,"displayName","XAxis"),d(b,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0})},2291:(t,e,r)=>{"use strict";r.d(e,{E:()=>u});var n=r(8420),o=r(8643),i=r(2185),a=r(6026),c=r(3831),u=(0,n.gu)({chartName:"BarChart",GraphicalChild:o.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:i.W},{axisType:"yAxis",AxisComp:a.h}],formatAxisMap:c.pr})},2322:(t,e,r)=>{var n=r(6913),o=r(5990),i=r(2761),a=Function.prototype,c=Object.prototype,u=a.toString,l=c.hasOwnProperty,s=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=l.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==s}},2520:(t,e,r)=>{var n=r(5816),o=r(9096),i=r(9140),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return-1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},2536:(t,e,r)=>{var n=r(149),o=r(2969),i=r(9096),a=r(8883),c=r(320),u=r(7574),l=r(5893),s=r(3279),f=r(4052);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;e=n(e,u(i));var h=a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}});return c(h,function(t,e){return l(t,e,r)})}},2541:t=>{t.exports=function(t){return function(){return t}}},2587:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},2597:(t,e,r)=>{var n=r(4052),o=r(9841),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!o(t))||(a.test(t)||!i.test(t)||null!=e&&t in Object(e))}},2598:(t,e,r)=>{"use strict";r.d(e,{s:()=>c});var n=r(977),o=r.n(n),i=r(1629),a=r.n(i);function c(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},2622:(t,e,r)=>{var n=r(705);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},2647:(t,e,r)=>{"use strict";r.d(e,{J:()=>M});var n=r(5043),o=r(9686),i=r.n(o),a=r(1629),c=r.n(a),u=r(6686),l=r.n(u),s=r(8387),f=r(4140),p=r(240),h=r(6307),d=r(165);function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}var v=["offset"];function m(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach(function(e){O(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function O(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}var A=function(t){var e=t.value,r=t.formatter,n=i()(t.children)?e:t.children;return c()(r)?r(n):n},S=function(t,e,r){var o,a,c=t.position,u=t.viewBox,l=t.offset,f=t.className,p=u,y=p.cx,v=p.cy,m=p.innerRadius,b=p.outerRadius,g=p.startAngle,x=p.endAngle,w=p.clockWise,O=(m+b)/2,A=function(t,e){return(0,h.sA)(e-t)*Math.min(Math.abs(e-t),360)}(g,x),S=A>=0?1:-1;"insideStart"===c?(o=g+S*l,a=w):"insideEnd"===c?(o=x-S*l,a=!w):"end"===c&&(o=x+S*l,a=w),a=A<=0?a:!a;var P=(0,d.IZ)(y,v,O,o),E=(0,d.IZ)(y,v,O,o+359*(a?1:-1)),k="M".concat(P.x,",").concat(P.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(a?0:1,",\n    ").concat(E.x,",").concat(E.y),M=i()(t.id)?(0,h.NF)("recharts-radial-line-"):t.id;return n.createElement("text",j({},r,{dominantBaseline:"central",className:(0,s.A)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:M,d:k})),n.createElement("textPath",{xlinkHref:"#".concat(M)},e))},P=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e,i=o.cx,a=o.cy,c=o.innerRadius,u=o.outerRadius,l=(o.startAngle+o.endAngle)/2;if("outside"===n){var s=(0,d.IZ)(i,a,u+r,l),f=s.x;return{x:f,y:s.y,textAnchor:f>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var p=(c+u)/2,h=(0,d.IZ)(i,a,p,l);return{x:h.x,y:h.y,textAnchor:"middle",verticalAnchor:"middle"}},E=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e,a=i.x,c=i.y,u=i.width,s=i.height,f=s>=0?1:-1,p=f*n,d=f>0?"end":"start",y=f>0?"start":"end",v=u>=0?1:-1,m=v*n,b=v>0?"end":"start",g=v>0?"start":"end";if("top"===o)return w(w({},{x:a+u/2,y:c-f*n,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(c-r.y,0),width:u}:{});if("bottom"===o)return w(w({},{x:a+u/2,y:c+s+p,textAnchor:"middle",verticalAnchor:y}),r?{height:Math.max(r.y+r.height-(c+s),0),width:u}:{});if("left"===o){var x={x:a-m,y:c+s/2,textAnchor:b,verticalAnchor:"middle"};return w(w({},x),r?{width:Math.max(x.x-r.x,0),height:s}:{})}if("right"===o){var O={x:a+u+m,y:c+s/2,textAnchor:g,verticalAnchor:"middle"};return w(w({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:s}:{})}var j=r?{width:u,height:s}:{};return"insideLeft"===o?w({x:a+m,y:c+s/2,textAnchor:g,verticalAnchor:"middle"},j):"insideRight"===o?w({x:a+u-m,y:c+s/2,textAnchor:b,verticalAnchor:"middle"},j):"insideTop"===o?w({x:a+u/2,y:c+p,textAnchor:"middle",verticalAnchor:y},j):"insideBottom"===o?w({x:a+u/2,y:c+s-p,textAnchor:"middle",verticalAnchor:d},j):"insideTopLeft"===o?w({x:a+m,y:c+p,textAnchor:g,verticalAnchor:y},j):"insideTopRight"===o?w({x:a+u-m,y:c+p,textAnchor:b,verticalAnchor:y},j):"insideBottomLeft"===o?w({x:a+m,y:c+s-p,textAnchor:g,verticalAnchor:d},j):"insideBottomRight"===o?w({x:a+u-m,y:c+s-p,textAnchor:b,verticalAnchor:d},j):l()(o)&&((0,h.Et)(o.x)||(0,h._3)(o.x))&&((0,h.Et)(o.y)||(0,h._3)(o.y))?w({x:a+(0,h.F4)(o.x,u),y:c+(0,h.F4)(o.y,s),textAnchor:"end",verticalAnchor:"end"},j):w({x:a+u/2,y:c+s/2,textAnchor:"middle",verticalAnchor:"middle"},j)},k=function(t){return"cx"in t&&(0,h.Et)(t.cx)};function M(t){var e,r=t.offset,o=w({offset:void 0===r?5:r},g(t,v)),a=o.viewBox,u=o.position,l=o.value,h=o.children,d=o.content,y=o.className,m=void 0===y?"":y,b=o.textBreakAll;if(!a||i()(l)&&i()(h)&&!(0,n.isValidElement)(d)&&!c()(d))return null;if((0,n.isValidElement)(d))return(0,n.cloneElement)(d,o);if(c()(d)){if(e=(0,n.createElement)(d,o),(0,n.isValidElement)(e))return e}else e=A(o);var x=k(a),O=(0,p.J9)(o,!0);if(x&&("insideStart"===u||"insideEnd"===u||"end"===u))return S(o,e,O);var M=x?P(o):E(o);return n.createElement(f.E,j({className:(0,s.A)("recharts-label",m)},O,M,{breakAll:b}),e)}M.displayName="Label";var T=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.Et)(y)&&(0,h.Et)(v)){if((0,h.Et)(s)&&(0,h.Et)(f))return{x:s,y:f,width:y,height:v};if((0,h.Et)(p)&&(0,h.Et)(d))return{x:p,y:d,width:y,height:v}}return(0,h.Et)(s)&&(0,h.Et)(f)?{x:s,y:f,width:0,height:0}:(0,h.Et)(e)&&(0,h.Et)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};M.parseViewBox=T,M.renderCallByParent=function(t,e){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var o=t.children,i=T(t),a=(0,p.aS)(o,M).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||i,key:"label-".concat(r)})});if(!r)return a;var u=function(t,e){return t?!0===t?n.createElement(M,{key:"label-implicit",viewBox:e}):(0,h.vh)(t)?n.createElement(M,{key:"label-implicit",viewBox:e,value:t}):(0,n.isValidElement)(t)?t.type===M?(0,n.cloneElement)(t,{key:"label-implicit",viewBox:e}):n.createElement(M,{key:"label-implicit",content:t,viewBox:e}):c()(t)?n.createElement(M,{key:"label-implicit",content:t,viewBox:e}):l()(t)?n.createElement(M,j({viewBox:e},t,{key:"label-implicit"})):null:null}(t.label,e||i);return[u].concat(m(a))}},2662:(t,e,r)=>{var n=r(5575);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},2761:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},2777:(t,e,r)=>{var n=r(5193),o=r(2761),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")};t.exports=u},2794:(t,e,r)=>{var n=r(9742),o=r(7498),i=r(9096);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},2929:(t,e,r)=>{var n=r(6552).Uint8Array;t.exports=n},2969:(t,e,r)=>{var n=r(5324),o=r(914);t.exports=function(t,e){for(var r=0,i=(e=n(e,t)).length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},3028:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},3097:(t,e,r)=>{var n=r(2969);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},3204:(t,e,r)=>{var n=r(3343),o=r(2777),i=r(4052),a=r(4543),c=r(9194),u=r(1268),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)!e&&!l.call(t,v)||h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y))||d.push(v);return d}},3211:t=>{t.exports=function(t,e){return t===e||t!==t&&e!==e}},3218:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3279:t=>{t.exports=function(t){return t}},3331:(t,e,r)=>{var n=r(9676),o=r(929),i=r(7303);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},3343:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},3366:(t,e,r)=>{var n=r(7894),o=r(9057);t.exports=function(t,e){return null!=t&&o(t,e,n)}},3404:(t,e,r)=>{"use strict";r.d(e,{A:()=>i});var n=!0,o="Invariant failed";function i(t,e){if(!t){if(n)throw new Error(o);var r="function"===typeof e?e():e,i=r?"".concat(o,": ").concat(r):o;throw new Error(i)}}},3411:(t,e,r)=>{var n=r(149),o=r(9096),i=r(8883),a=r(4052);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},3440:(t,e,r)=>{var n=r(6552)["__core-js_shared__"];t.exports=n},3538:(t,e,r)=>{var n=r(755),o=r(3411);t.exports=function(t,e){return n(o(t,e),1)}},3668:(t,e,r)=>{var n=r(8902),o=r(2587),i=r(8114);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(m!==b&&!c(m,b,r,a,u)){y=!1;break}}return u.delete(t),u.delete(e),y}},3713:(t,e,r)=>{var n=r(6140),o=r(1143),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},3781:(t,e,r)=>{var n=r(9417),o=r(8673);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},3809:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{A:()=>n})},3831:(t,e,r)=>{"use strict";r.d(e,{P2:()=>w,bx:()=>O,pr:()=>m,sl:()=>b,vh:()=>g});var n=r(1733),o=r.n(n),i=r(7002),a=r.n(i),c=r(202),u=r(240),l=r(6307),s=r(8643);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var m=function(t,e,r,n,o){var i=t.width,a=t.height,f=t.layout,p=t.children,h=Object.keys(e),v={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,u.BU)(p,s.y);return h.reduce(function(i,a){var u,s,p,h,b,g=e[a],x=g.orientation,w=g.domain,O=g.padding,j=void 0===O?{}:O,A=g.mirror,S=g.reversed,P="".concat(x).concat(A?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var E=w[1]-w[0],k=1/0,M=g.categoricalDomain.sort(l.ck);if(M.forEach(function(t,e){e>0&&(k=Math.min((t||0)-(M[e-1]||0),k))}),Number.isFinite(k)){var T=k/E,_="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(u=T*_/2),"no-gap"===g.padding){var C=(0,l.F4)(t.barCategoryGap,T*_),I=T*_/2;u=I-C-(I-C)/_*C}}}s="xAxis"===n?[r.left+(j.left||0)+(u||0),r.left+r.width-(j.right||0)-(u||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(u||0),r.top+r.height-(j.bottom||0)-(u||0)]:g.range,S&&(s=[s[1],s[0]]);var D=(0,c.W7)(g,o,m),N=D.scale,B=D.realScaleType;N.domain(w).range(s),(0,c.YB)(N);var R=(0,c.w7)(N,d(d({},g),{},{realScaleType:B}));"xAxis"===n?(b="top"===x&&!A||"bottom"===x&&A,p=r.left,h=v[P]-b*g.height):"yAxis"===n&&(b="left"===x&&!A||"right"===x&&A,p=v[P]-b*g.width,h=r.top);var L=d(d(d({},g),R),{},{realScaleType:B,x:p,y:h,scale:N,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return L.bandSize=(0,c.Hj)(L,R),g.hide||"xAxis"!==n?g.hide||(v[P]+=(b?-1:1)*L.width):v[P]+=(b?-1:1)*L.height,d(d({},i),{},y({},a,L))},{})},b=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},g=function(t){var e=t.x1,r=t.y1,n=t.x2,o=t.y2;return b({x:e,y:r},{x:n,y:o})},x=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scale=e}return e=t,r=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],n=[{key:"create",value:function(e){return new t(e)}}],r&&p(e.prototype,r),n&&p(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();y(x,"EPS",1e-4);var w=function(t){var e=Object.keys(t).reduce(function(e,r){return d(d({},e),{},y({},r,x.create(t[r])))},{});return d(d({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return o()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})};var O=function(t){var e=t.width,r=t.height,n=function(t){return(t%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),o=n*Math.PI/180,i=Math.atan(r/e),a=o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o);return Math.abs(a)}},3839:(t,e,r)=>{"use strict";r.d(e,{G:()=>z});var n,o=r(5043),i=r(8387),a=r(1744),c=r(1629),u=r.n(c),l=r(539),s=r.n(l),f=r(9686),p=r.n(f),h=r(5268),d=r.n(h),y=r(9853),v=r.n(y),m=r(8471),b=r(8892),g=r(1639),x=r(1519),w=r(6015),O=r(6307),j=r(202),A=r(240),S=["layout","type","stroke","connectNulls","isRange","ref"],P=["key"];function E(t){return E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},E(t)}function k(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function M(){return M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},M.apply(this,arguments)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,L(n.key),n)}}function I(t,e,r){return e=N(e),function(t,e){if(e&&("object"===E(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,D()?Reflect.construct(e,r||[],N(t).constructor):e.apply(t,r))}function D(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(D=function(){return!!t})()}function N(t){return N=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},N(t)}function B(t,e){return B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},B(t,e)}function R(t,e,r){return(e=L(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function L(t){var e=function(t,e){if("object"!=E(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=E(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==E(e)?e:e+""}var z=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return R(t=I(this,e,[].concat(n)),"state",{isAnimationFinished:!0}),R(t,"id",(0,O.NF)("recharts-area-")),R(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),u()(e)&&e()}),R(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),u()(e)&&e()}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&B(t,e)}(e,t),r=e,c=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],(n=[{key:"renderDots",value:function(t,r,n){var i=this.props.isAnimationActive,a=this.state.isAnimationFinished;if(i&&!a)return null;var c=this.props,u=c.dot,l=c.points,s=c.dataKey,f=(0,A.J9)(this.props,!1),p=(0,A.J9)(u,!0),h=l.map(function(t,r){var n=_(_(_({key:"dot-".concat(r),r:3},f),p),{},{index:r,cx:t.x,cy:t.y,dataKey:s,value:t.value,payload:t.payload,points:l});return e.renderDotItem(u,n)}),d={clipPath:t?"url(#clipPath-".concat(r?"":"dots-").concat(n,")"):null};return o.createElement(g.W,M({className:"recharts-area-dots"},d),h)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,a=n[0].x,c=n[n.length-1].x,u=t*Math.abs(a-c),l=s()(n.map(function(t){return t.y||0}));return(0,O.Et)(r)&&"number"===typeof r?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(s()(r.map(function(t){return t.y||0})),l)),(0,O.Et)(l)?o.createElement("rect",{x:a<c?a:a-u,y:0,width:u,height:Math.floor(l+(i?parseInt("".concat(i),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,i=e.strokeWidth,a=n[0].y,c=n[n.length-1].y,u=t*Math.abs(a-c),l=s()(n.map(function(t){return t.x||0}));return(0,O.Et)(r)&&"number"===typeof r?l=Math.max(r,l):r&&Array.isArray(r)&&r.length&&(l=Math.max(s()(r.map(function(t){return t.x||0})),l)),(0,O.Et)(l)?o.createElement("rect",{x:0,y:a<c?a:a-u,width:l+(i?parseInt("".concat(i),10):1),height:Math.floor(u)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,n){var i=this.props,a=i.layout,c=i.type,u=i.stroke,l=i.connectNulls,s=i.isRange,f=(i.ref,k(i,S));return o.createElement(g.W,{clipPath:r?"url(#clipPath-".concat(n,")"):null},o.createElement(m.I,M({},(0,A.J9)(f,!0),{points:t,connectNulls:l,type:c,baseLine:e,layout:a,stroke:"none",className:"recharts-area-area"})),"none"!==u&&o.createElement(m.I,M({},(0,A.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:t})),"none"!==u&&s&&o.createElement(m.I,M({},(0,A.J9)(this.props,!1),{className:"recharts-area-curve",layout:a,type:c,connectNulls:l,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,n=this.props,i=n.points,c=n.baseLine,u=n.isAnimationActive,l=n.animationBegin,s=n.animationDuration,f=n.animationEasing,h=n.animationId,y=this.state,v=y.prevPoints,m=y.prevBaseLine;return o.createElement(a.Ay,{begin:l,duration:s,isActive:u,easing:f,from:{t:0},to:{t:1},key:"area-".concat(h),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a=n.t;if(v){var u,l=v.length/i.length,s=i.map(function(t,e){var r=Math.floor(e*l);if(v[r]){var n=v[r],o=(0,O.Dj)(n.x,t.x),i=(0,O.Dj)(n.y,t.y);return _(_({},t),{},{x:o(a),y:i(a)})}return t});return u=(0,O.Et)(c)&&"number"===typeof c?(0,O.Dj)(m,c)(a):p()(c)||d()(c)?(0,O.Dj)(m,0)(a):c.map(function(t,e){var r=Math.floor(e*l);if(m[r]){var n=m[r],o=(0,O.Dj)(n.x,t.x),i=(0,O.Dj)(n.y,t.y);return _(_({},t),{},{x:o(a),y:i(a)})}return t}),r.renderAreaStatically(s,u,t,e)}return o.createElement(g.W,null,o.createElement("defs",null,o.createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(a))),o.createElement(g.W,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(i,c,t,e)))})}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,o=r.baseLine,i=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return i&&n&&n.length&&(!c&&l>0||!v()(c,n)||!v()(u,o))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,o,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,a=e.points,c=e.className,u=e.top,l=e.left,s=e.xAxis,f=e.yAxis,h=e.width,d=e.height,y=e.isAnimationActive,v=e.id;if(r||!a||!a.length)return null;var m=this.state.isAnimationFinished,b=1===a.length,w=(0,i.A)("recharts-area",c),O=s&&s.allowDataOverflow,j=f&&f.allowDataOverflow,S=O||j,P=p()(v)?this.id:v,E=null!==(t=(0,A.J9)(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},k=E.r,M=void 0===k?3:k,T=E.strokeWidth,_=void 0===T?2:T,C=((0,A.sT)(n)?n:{}).clipDot,I=void 0===C||C,D=2*M+_;return o.createElement(g.W,{className:w},O||j?o.createElement("defs",null,o.createElement("clipPath",{id:"clipPath-".concat(P)},o.createElement("rect",{x:O?l:l-h/2,y:j?u:u-d/2,width:O?h:2*h,height:j?d:2*d})),!I&&o.createElement("clipPath",{id:"clipPath-dots-".concat(P)},o.createElement("rect",{x:l-D/2,y:u-D/2,width:h+D,height:d+D}))):null,b?null:this.renderArea(S,P),(n||b)&&this.renderDots(S,I,P),(!y||m)&&x.Z.renderCallByParent(this.props,a))}}])&&C(r.prototype,n),c&&C(r,c),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,c}(o.PureComponent);n=z,R(z,"displayName","Area"),R(z,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!w.m.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),R(z,"getBaseValue",function(t,e,r,n){var o=t.layout,i=t.baseValue,a=e.props.baseValue,c=null!==a&&void 0!==a?a:i;if((0,O.Et)(c)&&"number"===typeof c)return c;var u="horizontal"===o?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c||s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]}),R(z,"getComposedData",function(t){var e,r=t.props,o=t.item,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.bandSize,s=t.dataKey,f=t.stackedData,p=t.dataStartIndex,h=t.displayedData,d=t.offset,y=r.layout,v=f&&f.length,m=n.getBaseValue(r,o,i,a),b="horizontal"===y,g=!1,x=h.map(function(t,e){var r;v?r=f[p+e]:(r=(0,j.kr)(t,s),Array.isArray(r)?g=!0:r=[m,r]);var n=null==r[1]||v&&null==(0,j.kr)(t,s);return b?{x:(0,j.nb)({axis:i,ticks:c,bandSize:l,entry:t,index:e}),y:n?null:a.scale(r[1]),value:r,payload:t}:{x:n?null:i.scale(r[1]),y:(0,j.nb)({axis:a,ticks:u,bandSize:l,entry:t,index:e}),value:r,payload:t}});return e=v||g?x.map(function(t){var e=Array.isArray(t.value)?t.value[0]:null;return b?{x:t.x,y:null!=e&&null!=t.y?a.scale(e):null}:{x:null!=e?i.scale(e):null,y:t.y}}):b?a.scale(m):i.scale(m),_({points:x,baseLine:e,layout:y,isRange:g},d)}),R(z,"renderDotItem",function(t,e){var r;if(o.isValidElement(t))r=o.cloneElement(t,e);else if(u()(t))r=t(e);else{var n=(0,i.A)("recharts-area-dot","boolean"!==typeof t?t.className:""),a=e.key,c=k(e,P);r=o.createElement(b.c,M({},c,{key:a,className:n}))}return r})},3871:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},3892:t=>{t.exports=function(t){return this.__data__.has(t)}},3932:(t,e,r)=>{var n=r(396),o=r(485),i=r(2597),a=r(914);t.exports=function(t){return i(t)?n(a(t)):o(t)}},3950:(t,e,r)=>{var n=r(6686),o=r(4757),i=r(801),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h;return void 0===h||r>=e||r<0||v&&t-d>=s}function x(){var t=o();if(g(t))return w(t);p=setTimeout(x,function(t){var r=e-(t-h);return v?c(r,s-(t-d)):r}(t))}function w(t){return p=void 0,m&&u?b(t):(u=l=void 0,f)}function O(){var t=o(),r=g(t);if(u=arguments,l=this,h=t,r){if(void 0===p)return function(t){return d=t,p=setTimeout(x,e),y?b(t):f}(h);if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},4020:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return-1}},4052:t=>{var e=Array.isArray;t.exports=e},4065:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},4079:(t,e,r)=>{var n=r(8259),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e});t.exports=a},4140:(t,e,r)=>{"use strict";r.d(e,{E:()=>z});var n=r(5043),o=r(9686),i=r.n(o),a=r(8387),c=r(6307),u=r(6015),l=r(240),s=r(7213);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function y(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,b=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,g=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,x={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},w=Object.keys(x),O="NaN";var j=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),""===r||b.test(r)||(this.num=NaN,this.unit=""),w.includes(r)&&(this.num=function(t,e){return t*x[e]}(e,r),this.unit="px")}return e=t,n=[{key:"parse",value:function(e){var r,n=p(null!==(r=g.exec(e))&&void 0!==r?r:[],3),o=n[1],i=n[2];return new t(parseFloat(o),null!==i&&void 0!==i?i:"")}}],(r=[{key:"add",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num+e.num,this.unit)}},{key:"subtract",value:function(e){return this.unit!==e.unit?new t(NaN,""):new t(this.num-e.num,this.unit)}},{key:"multiply",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num*e.num,this.unit||e.unit)}},{key:"divide",value:function(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new t(NaN,""):new t(this.num/e.num,this.unit||e.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&d(e.prototype,r),n&&d(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,r,n}();function A(t){if(t.includes(O))return O;for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=v.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=j.parse(null!==o&&void 0!==o?o:""),u=j.parse(null!==a&&void 0!==a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return O;e=e.replace(v,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=p(null!==(s=m.exec(e))&&void 0!==s?s:[],4),h=f[1],d=f[2],y=f[3],b=j.parse(null!==h&&void 0!==h?h:""),g=j.parse(null!==y&&void 0!==y?y:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return O;e=e.replace(m,x.toString())}return e}var S=/\(([^()]*)\)/;function P(t){var e=t.replace(/\s+/g,"");return e=function(t){for(var e=t;e.includes("(");){var r=p(S.exec(e),2)[1];e=e.replace(S,A(r))}return e}(e),e=A(e)}function E(t){var e=function(t){try{return P(t)}catch(e){return O}}(t.slice(5,-1));return e===O?"":e}var k=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],M=["dx","dy","angle","className","breakAll"];function T(){return T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},T.apply(this,arguments)}function _(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function C(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return I(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return I(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function I(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var D=/[ \f\n\r\t\v\u2028\u2029]+/,N=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];return i()(e)||(o=r?e.toString().split(""):e.toString().split(D)),{wordsWithComputedWidth:o.map(function(t){return{word:t,width:(0,s.Pu)(t,n).width}}),spaceWidth:r?0:(0,s.Pu)("\xa0",n).width}}catch(a){return null}},B=function(t){return[{words:i()(t)?[]:t.toString().split(D)}]},R=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!u.m.isSsr){var l=N({breakAll:i,children:n,style:o});return l?function(t,e,r,n,o){var i=t.maxLines,a=t.children,u=t.style,l=t.breakAll,s=(0,c.Et)(i),f=a,p=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];if(c&&(null==n||o||c.width+a+r<Number(n)))c.words.push(i),c.width+=a+r;else{var u={words:[i],width:a};t.push(u)}return t},[])},h=p(e);if(!s)return h;for(var d,y=function(t){var e=f.slice(0,t),r=N({breakAll:l,style:u,children:e+"\u2026"}).wordsWithComputedWidth,o=p(r),a=o.length>i||function(t){return t.reduce(function(t,e){return t.width>e.width?t:e})}(o).width>Number(n);return[a,o]},v=0,m=f.length-1,b=0;v<=m&&b<=f.length-1;){var g=Math.floor((v+m)/2),x=C(y(g-1),2),w=x[0],O=x[1],j=C(y(g),1)[0];if(w||j||(v=g+1),w&&j&&(m=g-1),!w&&j){d=O;break}b++}return d||h}({breakAll:i,children:n,maxLines:a,style:o},l.wordsWithComputedWidth,l.spaceWidth,e,r):B(n)}return B(n)},L="#808080",z=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,u=t.lineHeight,s=void 0===u?"1em":u,f=t.capHeight,p=void 0===f?"0.71em":f,h=t.scaleToFit,d=void 0!==h&&h,y=t.textAnchor,v=void 0===y?"start":y,m=t.verticalAnchor,b=void 0===m?"end":m,g=t.fill,x=void 0===g?L:g,w=_(t,k),O=(0,n.useMemo)(function(){return R({breakAll:w.breakAll,children:w.children,maxLines:w.maxLines,scaleToFit:d,style:w.style,width:w.width})},[w.breakAll,w.children,w.maxLines,d,w.style,w.width]),j=w.dx,A=w.dy,S=w.angle,P=w.className,C=w.breakAll,I=_(w,M);if(!(0,c.vh)(r)||!(0,c.vh)(i))return null;var D,N=r+((0,c.Et)(j)?j:0),B=i+((0,c.Et)(A)?A:0);switch(b){case"start":D=E("calc(".concat(p,")"));break;case"middle":D=E("calc(".concat((O.length-1)/2," * -").concat(s," + (").concat(p," / 2))"));break;default:D=E("calc(".concat(O.length-1," * -").concat(s,")"))}var z=[];if(d){var F=O[0].width,U=w.width;z.push("scale(".concat(((0,c.Et)(U)?U/F:1)/F,")"))}return S&&z.push("rotate(".concat(S,", ").concat(N,", ").concat(B,")")),z.length&&(I.transform=z.join(" ")),n.createElement("text",T({},(0,l.J9)(I,!0),{x:N,y:B,className:(0,a.A)("recharts-text",P),textAnchor:v,fill:x.includes("url")?L:x}),O.map(function(t,e){var r=t.words.join(C?"":" ");return n.createElement("tspan",{x:N,dy:0===e?D:s,key:"".concat(r,"-").concat(e)},r)}))}},4160:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},4190:(t,e,r)=>{var n=r(1340);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},4240:(t,e,r)=>{"use strict";r.d(e,{F:()=>F});var n,o=r(5043),i=r(1744),a=r(3097),c=r.n(a),u=r(9853),l=r.n(u),s=r(9686),f=r.n(s),p=r(1629),h=r.n(p),d=r(8387),y=r(1639),v=r(8471),m=r(4140),b=r(2647),g=r(1519),x=r(7869),w=r(240),O=r(6015),j=r(165),A=r(6307),S=r(202),P=r(155),E=r(7287),k=r(879);function M(t){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function T(){return T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},T.apply(this,arguments)}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function C(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach(function(e){L(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function I(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,z(n.key),n)}}function D(t,e,r){return e=B(e),function(t,e){if(e&&("object"===M(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,N()?Reflect.construct(e,r||[],B(t).constructor):e.apply(t,r))}function N(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(N=function(){return!!t})()}function B(t){return B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},B(t)}function R(t,e){return R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},R(t,e)}function L(t,e,r){return(e=z(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function z(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}var F=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),L(r=D(this,e,[t]),"pieRef",null),L(r,"sectorRefs",[]),L(r,"id",(0,A.NF)("recharts-pie-")),L(r,"handleAnimationEnd",function(){var t=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),h()(t)&&t()}),L(r,"handleAnimationStart",function(){var t=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),h()(t)&&t()}),r.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&R(t,e)}(e,t),r=e,a=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(o.isValidElement(t))return o.cloneElement(t,e);if(h()(t))return t(e);var n=(0,d.A)("recharts-pie-label-line","boolean"!==typeof t?t.className:"");return o.createElement(v.I,T({},e,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(t,e,r){if(o.isValidElement(t))return o.cloneElement(t,e);var n=r;if(h()(t)&&(n=t(e),o.isValidElement(n)))return n;var i=(0,d.A)("recharts-pie-label-text","boolean"===typeof t||h()(t)?"":t.className);return o.createElement(m.E,T({},e,{alignmentBaseline:"middle",className:i}),n)}}],(n=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.label,i=r.labelLine,a=r.dataKey,c=r.valueKey,u=(0,w.J9)(this.props,!1),l=(0,w.J9)(n,!1),s=(0,w.J9)(i,!1),p=n&&n.offsetRadius||20,h=t.map(function(t,r){var h=(t.startAngle+t.endAngle)/2,d=(0,j.IZ)(t.cx,t.cy,t.outerRadius+p,h),v=C(C(C(C({},u),t),{},{stroke:"none"},l),{},{index:r,textAnchor:e.getTextAnchor(d.x,t.cx)},d),m=C(C(C(C({},u),t),{},{fill:"none",stroke:t.fill},s),{},{index:r,points:[(0,j.IZ)(t.cx,t.cy,t.outerRadius,h),d]}),b=a;return f()(a)&&f()(c)?b="value":f()(a)&&(b=c),o.createElement(y.W,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(r)},i&&e.renderLabelLineItem(i,m,"line"),e.renderLabelItem(n,v,(0,S.kr)(t,b)))});return o.createElement(y.W,{className:"recharts-pie-labels"},h)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,i=r.blendStroke,a=r.inactiveShape;return t.map(function(r,c){if(0===(null===r||void 0===r?void 0:r.startAngle)&&0===(null===r||void 0===r?void 0:r.endAngle)&&1!==t.length)return null;var u=e.isActiveIndex(c),l=a&&e.hasActiveIndex()?a:null,s=u?n:l,f=C(C({},r),{},{stroke:i?r.fill:r.stroke,tabIndex:-1});return o.createElement(y.W,T({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},(0,E.XC)(e.props,r,c),{key:"sector-".concat(null===r||void 0===r?void 0:r.startAngle,"-").concat(null===r||void 0===r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),o.createElement(k.yp,T({option:s,isActive:u,shapeType:"sector"},f)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,a=e.animationBegin,u=e.animationDuration,l=e.animationEasing,s=e.animationId,f=this.state,p=f.prevSectors,h=f.prevIsAnimationActive;return o.createElement(i.Ay,{begin:a,duration:u,isActive:n,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(h),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var n=e.t,i=[],a=(r&&r[0]).startAngle;return r.forEach(function(t,e){var r=p&&p[e],o=e>0?c()(t,"paddingAngle",0):0;if(r){var u=(0,A.Dj)(r.endAngle-r.startAngle,t.endAngle-t.startAngle),l=C(C({},t),{},{startAngle:a+o,endAngle:a+u(n)+o});i.push(l),a=l.endAngle}else{var s=t.endAngle,f=t.startAngle,h=(0,A.Dj)(0,s-f)(n),d=C(C({},t),{},{startAngle:a+o,endAngle:a+h+o});i.push(d),a=d.endAngle}}),o.createElement(y.W,null,t.renderSectorsStatically(i))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return!(r&&e&&e.length)||n&&l()(n,e)?this.renderSectorsStatically(e):this.renderSectorsWithAnimation()}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,i=e.className,a=e.label,c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.isAnimationActive,p=this.state.isAnimationFinished;if(r||!n||!n.length||!(0,A.Et)(c)||!(0,A.Et)(u)||!(0,A.Et)(l)||!(0,A.Et)(s))return null;var h=(0,d.A)("recharts-pie",i);return o.createElement(y.W,{tabIndex:this.props.rootTabIndex,className:h,ref:function(e){t.pieRef=e}},this.renderSectors(),a&&this.renderLabels(n),b.J.renderCallByParent(this.props,null,!1),(!f||p)&&g.Z.renderCallByParent(this.props,n,!1))}}])&&I(r.prototype,n),a&&I(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,a}(o.PureComponent);n=F,L(F,"displayName","Pie"),L(F,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!O.m.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),L(F,"parseDeltaAngle",function(t,e){return(0,A.sA)(e-t)*Math.min(Math.abs(e-t),360)}),L(F,"getRealPieData",function(t){var e=t.data,r=t.children,n=(0,w.J9)(t,!1),o=(0,w.aS)(r,x.f);return e&&e.length?e.map(function(t,e){return C(C(C({payload:t},n),t),o&&o[e]&&o[e].props)}):o&&o.length?o.map(function(t){return C(C({},n),t.props)}):[]}),L(F,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=(0,j.lY)(o,i);return{cx:n+(0,A.F4)(t.cx,o,o/2),cy:r+(0,A.F4)(t.cy,i,i/2),innerRadius:(0,A.F4)(t.innerRadius,a,0),outerRadius:(0,A.F4)(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}}),L(F,"getComposedData",function(t){var e=t.item,r=t.offset,o=void 0!==e.type.defaultProps?C(C({},e.type.defaultProps),e.props):e.props,i=n.getRealPieData(o);if(!i||!i.length)return null;var a=o.cornerRadius,c=o.startAngle,u=o.endAngle,l=o.paddingAngle,s=o.dataKey,p=o.nameKey,h=o.valueKey,d=o.tooltipType,y=Math.abs(o.minAngle),v=n.parseCoordinateOfPie(o,r),m=n.parseDeltaAngle(c,u),b=Math.abs(m),g=s;f()(s)&&f()(h)?((0,P.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g="value"):f()(s)&&((0,P.R)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),g=h);var x,w,O=i.filter(function(t){return 0!==(0,S.kr)(t,g,0)}).length,E=b-O*y-(b>=360?O:O-1)*l,k=i.reduce(function(t,e){var r=(0,S.kr)(e,g,0);return t+((0,A.Et)(r)?r:0)},0);k>0&&(x=i.map(function(t,e){var r,n=(0,S.kr)(t,g,0),o=(0,S.kr)(t,p,e),i=((0,A.Et)(n)?n:0)/k,u=(r=e?w.endAngle+(0,A.sA)(m)*l*(0!==n?1:0):c)+(0,A.sA)(m)*((0!==n?y:0)+i*E),s=(r+u)/2,f=(v.innerRadius+v.outerRadius)/2,h=[{name:o,value:n,payload:t,dataKey:g,type:d}],b=(0,j.IZ)(v.cx,v.cy,f,s);return w=C(C(C({percent:i,cornerRadius:a,name:o,tooltipPayload:h,midAngle:s,middleRadius:f,tooltipPosition:b},t),v),{},{value:(0,S.kr)(t,g),startAngle:r,endAngle:u,payload:t,paddingAngle:(0,A.sA)(m)*l})}));return C(C({},v),{},{sectors:x,data:i})})},4258:(t,e,r)=>{var n=r(5906)();t.exports=n},4262:(t,e,r)=>{var n=r(8895),o=r(4052);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},4342:(t,e,r)=>{"use strict";r.d(e,{J:()=>y,M:()=>m});var n=r(5043),o=r(8387),i=r(1744),a=r(240);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function u(){return u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},u.apply(this,arguments)}function l(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return s(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var d=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},y=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},v={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},m=function(t){var e=p(p({},v),t),r=(0,n.useRef)(),c=l((0,n.useState)(-1),2),s=c[0],f=c[1];(0,n.useEffect)(function(){if(r.current&&r.current.getTotalLength)try{var t=r.current.getTotalLength();t&&f(t)}catch(e){}},[]);var h=e.x,y=e.y,m=e.width,b=e.height,g=e.radius,x=e.className,w=e.animationEasing,O=e.animationDuration,j=e.animationBegin,A=e.isAnimationActive,S=e.isUpdateAnimationActive;if(h!==+h||y!==+y||m!==+m||b!==+b||0===m||0===b)return null;var P=(0,o.A)("recharts-rectangle",x);return S?n.createElement(i.Ay,{canBegin:s>0,from:{width:m,height:b,x:h,y:y},to:{width:m,height:b,x:h,y:y},duration:O,animationEasing:w,isActive:S},function(t){var o=t.width,c=t.height,l=t.x,f=t.y;return n.createElement(i.Ay,{canBegin:s>0,from:"0px ".concat(-1===s?1:s,"px"),to:"".concat(s,"px 0px"),attributeName:"strokeDasharray",begin:j,duration:O,isActive:A,easing:w},n.createElement("path",u({},(0,a.J9)(e,!0),{className:P,d:d(l,f,o,c,g),ref:r})))}):n.createElement("path",u({},(0,a.J9)(e,!0),{className:P,d:d(h,y,m,b,g)}))}},4402:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"===typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"===typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{C:()=>n,K:()=>o})},4416:(t,e,r)=>{var n=r(8902),o=r(5866),i=r(1558),a=r(8114),c=r(8182),u=r(2074);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;t:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m===m){for(var b=d.length;b--;)if(d[b]===m)continue t;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},4543:(t,e,r)=>{t=r.nmd(t);var n=r(6552),o=r(14),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=(c?c.isBuffer:void 0)||o;t.exports=u},4545:(t,e,r)=>{var n=r(7160);t.exports=function(){this.__data__=new n,this.size=0}},4552:(t,e,r)=>{var n=r(9812),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(u){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},4597:(t,e,r)=>{var n=r(2587),o=r(9096),i=r(2165),a=r(4052),c=r(929);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},4657:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},4664:(t,e,r)=>{var n=r(4258),o=r(8673);t.exports=function(t,e){return t&&n(t,e,o)}},4746:(t,e,r)=>{var n=r(5652);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},4757:(t,e,r)=>{var n=r(6552);t.exports=function(){return n.Date.now()}},4794:(t,e,r)=>{"use strict";r.d(e,{u:()=>l});var n=r(5043),o=r(8387),i=r(240),a=["children","width","height","viewBox","className","style","title","desc"];function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}function u(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function l(t){var e=t.children,r=t.width,l=t.height,s=t.viewBox,f=t.className,p=t.style,h=t.title,d=t.desc,y=u(t,a),v=s||{width:r,height:l,x:0,y:0},m=(0,o.A)("recharts-surface",f);return n.createElement("svg",c({},(0,i.J9)(y,!0,"svg"),{className:m,width:r,height:l,style:p,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height)}),n.createElement("title",null,h),n.createElement("desc",null,d),e)}},4816:(t,e,r)=>{var n=r(7251),o=r(7159),i=r(438),a=r(9394),c=r(6874);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},5029:(t,e,r)=>{var n=r(6989),o=r(3097),i=r(3366),a=r(2597),c=r(9417),u=r(1310),l=r(914);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},5051:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},5173:(t,e,r)=>{t.exports=r(1497)()},5186:(t,e,r)=>{"use strict";r.d(e,{A:()=>s,h:()=>l});class n extends Map{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c;if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[r,n]of t)this.set(r,n)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(i(this,t),e)}delete(t){return super.delete(a(this,t))}}Set;function o(t,e){let{_intern:r,_key:n}=t;const o=n(e);return r.has(o)?r.get(o):e}function i(t,e){let{_intern:r,_key:n}=t;const o=n(e);return r.has(o)?r.get(o):(r.set(o,e),e)}function a(t,e){let{_intern:r,_key:n}=t;const o=n(e);return r.has(o)&&(e=r.get(o),r.delete(o)),e}function c(t){return null!==t&&"object"===typeof t?t.valueOf():t}var u=r(4402);const l=Symbol("implicit");function s(){var t=new n,e=[],r=[],o=l;function i(n){let i=t.get(n);if(void 0===i){if(o!==l)return o;t.set(n,i=e.push(n)-1)}return r[i%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();e=[],t=new n;for(const n of r)t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(o=t,i):o},i.copy=function(){return s(e,r).unknown(o)},u.C.apply(i,arguments),i}},5193:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},5204:(t,e,r)=>{var n=r(7937)(r(6552),"Map");t.exports=n},5248:(t,e,r)=>{"use strict";function n(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{b:()=>n})},5268:(t,e,r)=>{var n=r(9160);t.exports=function(t){return n(t)&&t!=+t}},5295:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},5324:(t,e,r)=>{var n=r(4052),o=r(2597),i=r(4079),a=r(1069);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},5387:(t,e,r)=>{var n=r(7937)(r(6552),"Promise");t.exports=n},5428:(t,e,r)=>{var n=r(6913),o=r(6173),i=r(2761),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},5538:(t,e,r)=>{var n=r(7160),o=r(4545),i=r(793),a=r(7760),c=r(3892),u=r(6788);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},5575:(t,e,r)=>{var n=r(7937)(Object,"create");t.exports=n},5636:(t,e,r)=>{var n=r(1170),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},5647:(t,e,r)=>{var n=r(3279),o=r(5636),i=r(6350);t.exports=function(t,e){return i(o(t,e,n),t+"")}},5652:(t,e,r)=>{var n=r(4664),o=r(6516)(n);t.exports=o},5654:(t,e,r)=>{var n=r(7937),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(e){}}();t.exports=o},5713:t=>{t.exports=function(){}},5748:(t,e,r)=>{"use strict";r.d(e,{b:()=>u});var n=r(8420),o=r(168),i=r(2185),a=r(6026),c=r(3831),u=(0,n.gu)({chartName:"LineChart",GraphicalChild:o.N,axisComponents:[{axisType:"xAxis",AxisComp:i.W},{axisType:"yAxis",AxisComp:a.h}],formatAxisMap:c.pr})},5752:(t,e,r)=>{var n=r(9395),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w||(y=!1)}return c.delete(t),c.delete(e),y}},5797:(t,e,r)=>{var n=r(4816);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},5816:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return-1}},5866:(t,e,r)=>{var n=r(8468);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},5893:(t,e,r)=>{var n=r(6599);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l)return o>=u?l:l*("desc"==r[o]?-1:1)}return t.index-e.index}},5906:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},5967:t=>{t.exports=function(t){return t.split("")}},5990:(t,e,r)=>{var n=r(3028)(Object.getPrototypeOf,Object);t.exports=n},6015:(t,e,r)=>{"use strict";r.d(e,{m:()=>n});var n={isSsr:!("undefined"!==typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"===typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},6026:(t,e,r)=>{"use strict";r.d(e,{h:()=>b});var n=r(5043),o=r(8387),i=r(2103),a=r(7671),c=r(202);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function s(t,e,r){return e=p(e),function(t,e){if(e&&("object"===u(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,f()?Reflect.construct(e,r||[],p(t).constructor):e.apply(t,r))}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},p(t)}function h(t,e){return h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},h(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}function v(){return v=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},v.apply(this,arguments)}var m=function(t){var e=t.yAxisId,r=(0,i.yi)(),u=(0,i.rY)(),l=(0,i.Nk)(e);return null==l?null:n.createElement(a.u,v({},l,{className:(0,o.A)("recharts-".concat(l.axisType," ").concat(l.axisType),l.className),viewBox:{x:0,y:0,width:r,height:u},ticksGenerator:function(t){return(0,c.Rh)(t,!0)}}))},b=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),s(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(m,this.props)}}])&&l(r.prototype,o),i&&l(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);d(b,"displayName","YAxis"),d(b,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1})},6095:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},6140:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},6150:(t,e,r)=>{"use strict";r.d(e,{m:()=>J});var n=r(5043),o=r(7424),i=r.n(o),a=r(9686),c=r.n(a),u=r(8387),l=r(6307);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(){return f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},f.apply(this,arguments)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){v(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function v(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t){return Array.isArray(t)&&(0,l.vh)(t[0])&&(0,l.vh)(t[1])?t.join(" ~ "):t}var b=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,a=void 0===o?{}:o,s=t.itemStyle,h=void 0===s?{}:s,d=t.labelStyle,v=void 0===d?{}:d,b=t.payload,g=t.formatter,x=t.itemSorter,w=t.wrapperClassName,O=t.labelClassName,j=t.label,A=t.labelFormatter,S=t.accessibilityLayer,P=void 0!==S&&S,E=y({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),k=y({margin:0},v),M=!c()(j),T=M?j:"",_=(0,u.A)("recharts-default-tooltip",w),C=(0,u.A)("recharts-tooltip-label",O);M&&A&&void 0!==b&&null!==b&&(T=A(j,b));var I=P?{role:"status","aria-live":"assertive"}:{};return n.createElement("div",f({className:_,style:E},I),n.createElement("p",{className:C,style:k},n.isValidElement(T)?T:"".concat(T)),function(){if(b&&b.length){var t=(x?i()(b,x):b).map(function(t,e){if("none"===t.type)return null;var o=y({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},h),i=t.formatter||g||m,a=t.value,c=t.name,u=a,s=c;if(i&&null!=u&&null!=s){var f=i(a,c,t,e,b);if(Array.isArray(f)){var d=p(f,2);u=d[0],s=d[1]}else u=f}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},(0,l.vh)(s)?n.createElement("span",{className:"recharts-tooltip-item-name"},s):null,(0,l.vh)(s)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function g(t){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function x(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=g(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==g(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var w="recharts-tooltip-wrapper",O={visibility:"hidden"};function j(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return(0,u.A)(w,x(x(x(x({},"".concat(w,"-right"),(0,l.Et)(r)&&e&&(0,l.Et)(e.x)&&r>=e.x),"".concat(w,"-left"),(0,l.Et)(r)&&e&&(0,l.Et)(e.x)&&r<e.x),"".concat(w,"-bottom"),(0,l.Et)(n)&&e&&(0,l.Et)(e.y)&&n>=e.y),"".concat(w,"-top"),(0,l.Et)(n)&&e&&(0,l.Et)(e.y)&&n<e.y))}function A(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,s=t.viewBoxDimension;if(i&&(0,l.Et)(i[n]))return i[n];var f=r[n]-c-o,p=r[n]+o;return e[n]?a[n]?f:p:a[n]?f<u[n]?Math.max(p,u[n]):Math.max(f,u[n]):p+c>u[n]+s?Math.max(f,u[n]):Math.max(p,u[n])}function S(t){return S="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},S(t)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function E(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){I(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function k(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,D(n.key),n)}}function M(t,e,r){return e=_(e),function(t,e){if(e&&("object"===S(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,T()?Reflect.construct(e,r||[],_(t).constructor):e.apply(t,r))}function T(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(T=function(){return!!t})()}function _(t){return _=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_(t)}function C(t,e){return C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},C(t,e)}function I(t,e,r){return(e=D(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function D(t){var e=function(t,e){if("object"!=S(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=S(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==S(e)?e:e+""}var N=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return I(t=M(this,e,[].concat(n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),I(t,"handleKeyDown",function(e){var r,n,o,i;"Escape"===e.key&&t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&C(t,e)}(e,t),r=e,(o=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)===this.state.dismissedAtCoordinate.x&&(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.children,u=e.coordinate,l=e.hasPayload,s=e.isAnimationActive,f=e.offset,p=e.position,h=e.reverseDirection,d=e.useTranslate3d,y=e.viewBox,v=e.wrapperStyle,m=function(t){var e,r,n=t.allowEscapeViewBox,o=t.coordinate,i=t.offsetTopLeft,a=t.position,c=t.reverseDirection,u=t.tooltipBox,l=t.useTranslate3d,s=t.viewBox;return{cssProperties:u.height>0&&u.width>0&&o?function(t){var e=t.translateX,r=t.translateY;return{transform:t.useTranslate3d?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}({translateX:e=A({allowEscapeViewBox:n,coordinate:o,key:"x",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.width,viewBox:s,viewBoxDimension:s.width}),translateY:r=A({allowEscapeViewBox:n,coordinate:o,key:"y",offsetTopLeft:i,position:a,reverseDirection:c,tooltipDimension:u.height,viewBox:s,viewBoxDimension:s.height}),useTranslate3d:l}):O,cssClasses:j({translateX:e,translateY:r,coordinate:o})}}({allowEscapeViewBox:o,coordinate:u,offsetTopLeft:f,position:p,reverseDirection:h,tooltipBox:this.state.lastBoundingBox,useTranslate3d:d,viewBox:y}),b=m.cssClasses,g=m.cssProperties,x=E(E({transition:s&&r?"transform ".concat(i,"ms ").concat(a):void 0},g),{},{pointerEvents:"none",visibility:!this.state.dismissed&&r&&l?"visible":"hidden",position:"absolute",top:0,left:0},v);return n.createElement("div",{tabIndex:-1,className:b,style:x,ref:function(e){t.wrapperNode=e}},c)}}])&&k(r.prototype,o),i&&k(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent),B=r(6015),R=r(2598);function L(t){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(t)}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function F(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach(function(e){V(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function U(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,K(n.key),n)}}function W(t,e,r){return e=X(e),function(t,e){if(e&&("object"===L(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,q()?Reflect.construct(e,r||[],X(t).constructor):e.apply(t,r))}function q(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(q=function(){return!!t})()}function X(t){return X=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},X(t)}function H(t,e){return H=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},H(t,e)}function V(t,e,r){return(e=K(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function K(t){var e=function(t,e){if("object"!=L(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=L(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==L(e)?e:e+""}function G(t){return t.dataKey}var J=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),W(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&H(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this,e=this.props,r=e.active,o=e.allowEscapeViewBox,i=e.animationDuration,a=e.animationEasing,c=e.content,u=e.coordinate,l=e.filterNull,s=e.isAnimationActive,f=e.offset,p=e.payload,h=e.payloadUniqBy,d=e.position,y=e.reverseDirection,v=e.useTranslate3d,m=e.viewBox,g=e.wrapperStyle,x=null!==p&&void 0!==p?p:[];l&&x.length&&(x=(0,R.s)(p.filter(function(e){return null!=e.value&&(!0!==e.hide||t.props.includeHidden)}),h,G));var w=x.length>0;return n.createElement(N,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:r,coordinate:u,hasPayload:w,offset:f,position:d,reverseDirection:y,useTranslate3d:v,viewBox:m,wrapperStyle:g},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):"function"===typeof t?n.createElement(t,e):n.createElement(b,e)}(c,F(F({},this.props),{},{payload:x})))}}])&&U(r.prototype,o),i&&U(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent);V(J,"displayName","Tooltip"),V(J,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!B.m.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},6173:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},6179:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},6307:(t,e,r)=>{"use strict";r.d(e,{CG:()=>O,Dj:()=>j,Et:()=>y,F4:()=>x,NF:()=>g,_3:()=>d,ck:()=>S,eP:()=>A,lX:()=>w,sA:()=>h,uy:()=>v,vh:()=>m});var n=r(620),o=r.n(n),i=r(5268),a=r.n(i),c=r(3097),u=r.n(c),l=r(9160),s=r.n(l),f=r(9686),p=r.n(f),h=function(t){return 0===t?0:t>0?1:-1},d=function(t){return o()(t)&&t.indexOf("%")===t.length-1},y=function(t){return s()(t)&&!a()(t)},v=function(t){return p()(t)},m=function(t){return y(t)||o()(t)},b=0,g=function(t){var e=++b;return"".concat(t||"").concat(e)},x=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!y(t)&&!o()(t))return n;if(d(t)){var c=t.indexOf("%");r=e*parseFloat(t.slice(0,c))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},w=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},O=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},j=function(t,e){return y(t)&&y(e)?function(r){return t+r*(e-t)}:function(){return e}};function A(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"===typeof e?e(t):u()(t,e))===r}):null}var S=function(t,e){return y(t)&&y(e)?t-e:o()(t)&&o()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))}},6311:t=>{var e=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},6350:(t,e,r)=>{var n=r(8325),o=r(6578)(n);t.exports=o},6361:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},6378:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy"),v=Symbol.for("react.offscreen");function m(t){if("object"===typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case o:return e}}}r=Symbol.for("react.module.reference"),e.isFragment=function(t){return m(t)===i}},6399:(t,e,r)=>{var n=r(5538),o=r(3668),i=r(9987),a=r(5752),c=r(6924),u=r(4052),l=r(4543),s=r(1268),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e),O=(x=x==f?h:x)==h,j=(w=w==f?h:w)==h,A=x==w;if(A&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(A&&!O)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var S=O&&d.call(t,"__wrapped__"),P=j&&d.call(e,"__wrapped__");if(S||P){var E=S?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!A&&(m||(m=new n),a(t,e,r,y,v,m))}},6516:(t,e,r)=>{var n=r(6571);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},6532:(t,e,r)=>{var n=r(5538),o=r(6989);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},6552:(t,e,r)=>{var n=r(7105),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},6571:(t,e,r)=>{var n=r(1629),o=r(6173);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},6578:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},6599:(t,e,r)=>{var n=r(9841);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t===t,a=n(t),c=void 0!==e,u=null===e,l=e===e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return-1}return 0}},6600:(t,e,r)=>{var n=r(7937)(r(6552),"WeakMap");t.exports=n},6604:(t,e,r)=>{var n=r(3331)();t.exports=n},6686:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},6704:t=>{t.exports=function(t){return this.__data__.has(t)}},6745:(t,e,r)=>{var n=r(9742),o=r(61),i=r(3279);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},6788:(t,e,r)=>{var n=r(7160),o=r(5204),i=r(4816);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},6832:(t,e,r)=>{t=r.nmd(t);var n=r(7105),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;return t||a&&a.binding&&a.binding("util")}catch(e){}}();t.exports=c},6874:(t,e,r)=>{var n=r(2622);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},6913:(t,e,r)=>{var n=r(9812),o=r(4552),i=r(6095),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},6924:(t,e,r)=>{var n=r(7685),o=r(5204),i=r(5387),a=r(2070),c=r(6600),u=r(6913),l=r(6996),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},6954:(t,e,r)=>{var n=r(1629),o=r(7857),i=r(6686),a=r(6996),c=/^\[object .+?Constructor\]$/,u=Function.prototype,l=Object.prototype,s=u.toString,f=l.hasOwnProperty,p=RegExp("^"+s.call(f).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?p:c).test(a(t))}},6989:(t,e,r)=>{var n=r(6399),o=r(2761);t.exports=function t(e,r,i,a,c){return e===r||(null==e||null==r||!o(e)&&!o(r)?e!==e&&r!==r:n(e,r,i,a,t,c))}},6996:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(r){}try{return t+""}catch(r){}}return""}},7002:(t,e,r)=>{var n=r(5295),o=r(4746),i=r(9096),a=r(4052),c=r(929);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},7105:(t,e,r)=>{var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},7116:(t,e,r)=>{var n=r(9812),o=r(2777),i=r(4052),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},7159:(t,e,r)=>{var n=r(2622);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},7160:(t,e,r)=>{var n=r(7563),o=r(9935),i=r(4190),a=r(1946),c=r(1714);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},7165:(t,e,r)=>{"use strict";r.d(e,{g:()=>s});var n=r(1327),o=r(202),i=r(240);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?c(Object(r),!0).forEach(function(e){l(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var s=function(t){var e=t.children,r=t.formattedGraphicalItems,a=t.legendWidth,c=t.legendContent,l=(0,i.BU)(e,n.s);if(!l)return null;var s,f=n.s.defaultProps,p=void 0!==f?u(u({},f),l.props):{};return s=l.props&&l.props.payload?l.props&&l.props.payload:"children"===c?(r||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:l.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(r||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u(u({},r),e.props):{},i=n.dataKey,a=n.name,c=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||c||"square",color:(0,o.Ps)(e),value:a||i,payload:n}}),u(u(u({},p),n.s.getWithHeight(l,a)),{},{payload:s,item:l})}},7213:(t,e,r)=>{"use strict";r.d(e,{A3:()=>p,Pu:()=>f});var n=r(6015);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){c(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function c(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var u={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span";var f=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0===t||null===t||n.m.isSsr)return{width:0,height:0};var r=function(t){var e=a({},t);return Object.keys(e).forEach(function(t){e[t]||delete e[t]}),e}(e),o=JSON.stringify({text:t,copyStyle:r});if(u.widthCache[o])return u.widthCache[o];try{var i=document.getElementById(s);i||((i=document.createElement("span")).setAttribute("id",s),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var c=a(a({},l),r);Object.assign(i.style,c),i.textContent="".concat(t);var f=i.getBoundingClientRect(),p={width:f.width,height:f.height};return u.widthCache[o]=p,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),p}catch(h){return{width:0,height:0}}},p=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},7251:(t,e,r)=>{var n=r(8724),o=r(7160),i=r(5204);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},7283:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!==typeof n)throw new TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0===--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=new Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=new Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=new Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},7287:(t,e,r)=>{"use strict";r.d(e,{QQ:()=>c,VU:()=>l,XC:()=>p,_U:()=>f,j2:()=>s});var n=r(5043),o=r(6686),i=r.n(o);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}var c=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],u=["points","pathLength"],l={svg:["viewBox","children"],polygon:u,polyline:u},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"===typeof t||"boolean"===typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})}),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"===typeof i&&(n||(n={}),n[o]=function(t,e,r){return function(n){return t(e,r,n),null}}(i,e,r))}),n}},7303:(t,e,r)=>{var n=r(801),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-1/0?17976931348623157e292*(t<0?-1:1):t===t?t:0:0===t?t:0}},7371:(t,e,r)=>{"use strict";r.d(e,{i:()=>A});var n,o,i,a,c,u,l,s,f,p,h,d,y,v,m=r(7528);const b=Math.PI,g=2*b,x=1e-6,w=g-x;function O(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class j{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?O:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error("invalid digits: ".concat(t));if(e>15)return O;const r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append(n||(n=(0,m.A)(["M",",",""])),this._x0=this._x1=+t,this._y0=this._y1=+e)}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append(o||(o=(0,m.A)(["Z"]))))}lineTo(t,e){this._append(i||(i=(0,m.A)(["L",",",""])),this._x1=+t,this._y1=+e)}quadraticCurveTo(t,e,r,n){this._append(a||(a=(0,m.A)(["Q",",",",",",",""])),+t,+e,this._x1=+r,this._y1=+n)}bezierCurveTo(t,e,r,n,o,i){this._append(c||(c=(0,m.A)(["C",",",",",",",",",",",""])),+t,+e,+r,+n,this._x1=+o,this._y1=+i)}arcTo(t,e,r,n,o){if(t=+t,e=+e,r=+r,n=+n,(o=+o)<0)throw new Error("negative radius: ".concat(o));let i=this._x1,a=this._y1,c=r-t,p=n-e,h=i-t,d=a-e,y=h*h+d*d;if(null===this._x1)this._append(u||(u=(0,m.A)(["M",",",""])),this._x1=t,this._y1=e);else if(y>x)if(Math.abs(d*c-p*h)>x&&o){let u=r-i,l=n-a,v=c*c+p*p,g=u*u+l*l,w=Math.sqrt(v),O=Math.sqrt(y),j=o*Math.tan((b-Math.acos((v+y-g)/(2*w*O)))/2),A=j/O,S=j/w;Math.abs(A-1)>x&&this._append(s||(s=(0,m.A)(["L",",",""])),t+A*h,e+A*d),this._append(f||(f=(0,m.A)(["A",",",",0,0,",",",",",""])),o,o,+(d*u>h*l),this._x1=t+S*c,this._y1=e+S*p)}else this._append(l||(l=(0,m.A)(["L",",",""])),this._x1=t,this._y1=e);else;}arc(t,e,r,n,o,i){if(t=+t,e=+e,i=!!i,(r=+r)<0)throw new Error("negative radius: ".concat(r));let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append(p||(p=(0,m.A)(["M",",",""])),u,l):(Math.abs(this._x1-u)>x||Math.abs(this._y1-l)>x)&&this._append(h||(h=(0,m.A)(["L",",",""])),u,l),r&&(f<0&&(f=f%g+g),f>w?this._append(d||(d=(0,m.A)(["A",",",",0,1,",",",",","A",",",",0,1,",",",",",""])),r,r,s,t-a,e-c,r,r,s,this._x1=u,this._y1=l):f>x&&this._append(y||(y=(0,m.A)(["A",",",",0,",",",",",",",""])),r,r,+(f>=b),s,this._x1=t+r*Math.cos(o),this._y1=e+r*Math.sin(o)))}rect(t,e,r,n){this._append(v||(v=(0,m.A)(["M",",","h","v","h","Z"])),this._x0=this._x1=+t,this._y0=this._y1=+e,r=+r,+n,-r)}toString(){return this._}}function A(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{const t=Math.floor(r);if(!(t>=0))throw new RangeError("invalid digits: ".concat(r));e=t}return t},()=>new j(e)}j.prototype},7424:(t,e,r)=>{var n=r(755),o=r(2536),i=r(5647),a=r(929),c=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=c},7498:t=>{t.exports=function(t,e){return t>e}},7529:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},7563:t=>{t.exports=function(){this.__data__=[],this.size=0}},7574:t=>{t.exports=function(t){return function(e){return t(e)}}},7615:(t,e,r)=>{var n=r(5575);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},7671:(t,e,r)=>{"use strict";r.d(e,{u:()=>C});var n=r(5043),o=r(1629),i=r.n(o),a=r(3097),c=r.n(a),u=r(8387),l=r(5248),s=r(1639),f=r(4140),p=r(2647),h=r(6307),d=r(7287),y=r(240),v=r(8854),m=["viewBox"],b=["viewBox"],g=["ticks"];function x(t){return x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},x(t)}function w(){return w=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},w.apply(this,arguments)}function O(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?O(Object(r),!0).forEach(function(e){T(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function S(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function P(t,e,r){return e=k(e),function(t,e){if(e&&("object"===x(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,E()?Reflect.construct(e,r||[],k(t).constructor):e.apply(t,r))}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function k(t){return k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},k(t)}function M(t,e){return M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},M(t,e)}function T(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=x(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=x(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==x(e)?e:e+""}var C=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(r=P(this,e,[t])).state={fontSize:"",letterSpacing:""},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&M(t,e)}(e,t),r=e,a=[{key:"renderTickItem",value:function(t,e,r){var o=(0,u.A)(e.className,"recharts-cartesian-axis-tick-value");return n.isValidElement(t)?n.cloneElement(t,j(j({},e),{},{className:o})):i()(t)?t(j(j({},e),{},{className:o})):n.createElement(f.E,w({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],(o=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=A(t,m),o=this.props,i=o.viewBox,a=A(o,b);return!(0,l.b)(r,i)||!(0,l.b)(n,a)||!(0,l.b)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,d=c.tickSize,y=c.mirror,v=c.tickMargin,m=y?-1:1,b=t.tickSize||d,g=(0,h.Et)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!y*f)-m*b)-m*v,i=g;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!y*s)-m*b)-m*v,a=g;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +y*s)+m*b)+m*v,a=g;break;default:e=r=t.coordinate,a=(n=(o=l+ +y*f)+m*b)+m*v,i=g}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.orientation,l=t.mirror,s=t.axisLine,f=j(j(j({},(0,y.J9)(this.props,!1)),(0,y.J9)(s,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var p=+("top"===a&&!l||"bottom"===a&&l);f=j(j({},f),{},{x1:e,y1:r+p*i,x2:e+o,y2:r+p*i})}else{var h=+("left"===a&&!l||"right"===a&&l);f=j(j({},f),{},{x1:e+h*o,y1:r,x2:e+h*o,y2:r+i})}return n.createElement("line",w({},f,{className:(0,u.A)("recharts-cartesian-axis-line",c()(s,"className"))}))}},{key:"renderTicks",value:function(t,r,o){var a=this,l=this.props,f=l.tickLine,p=l.stroke,h=l.tick,m=l.tickFormatter,b=l.unit,g=(0,v.f)(j(j({},this.props),{},{ticks:t}),r,o),x=this.getTickTextAnchor(),O=this.getTickVerticalAnchor(),A=(0,y.J9)(this.props,!1),S=(0,y.J9)(h,!1),P=j(j({},A),{},{fill:"none"},(0,y.J9)(f,!1)),E=g.map(function(t,r){var o=a.getTickLineCoord(t),l=o.line,y=o.tick,v=j(j(j(j({textAnchor:x,verticalAnchor:O},A),{},{stroke:"none",fill:p},S),y),{},{index:r,payload:t,visibleTicksCount:g.length,tickFormatter:m});return n.createElement(s.W,w({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,d.XC)(a.props,t,r)),f&&n.createElement("line",w({},P,l,{className:(0,u.A)("recharts-cartesian-axis-tick-line",c()(f,"className"))})),h&&e.renderTickItem(h,v,"".concat(i()(m)?m(t.value,r):t.value).concat(b||"")))});return n.createElement("g",{className:"recharts-cartesian-axis-ticks"},E)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,o=e.width,a=e.height,c=e.ticksGenerator,l=e.className;if(e.hide)return null;var f=this.props,h=f.ticks,d=A(f,g),y=h;return i()(c)&&(y=h&&h.length>0?c(this.props):c(d)),o<=0||a<=0||!y||!y.length?null:n.createElement(s.W,{className:(0,u.A)("recharts-cartesian-axis",l),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(y,this.state.fontSize,this.state.letterSpacing),p.J.renderCallByParent(this.props))}}])&&S(r.prototype,o),a&&S(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,a}(n.Component);T(C,"displayName","CartesianAxis"),T(C,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},7676:(t,e,r)=>{var n=r(8189),o=r(6311),i=r(9115),a=r(1069);t.exports=function(t){return function(e){e=a(e);var r=o(e)?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},7685:(t,e,r)=>{var n=r(7937)(r(6552),"DataView");t.exports=n},7734:(t,e,r)=>{"use strict";r.d(e,{d:()=>T});var n=r(5043),o=r(1629),i=r.n(o),a=r(155),c=r(6307),u=r(240),l=r(202),s=r(8854),f=r(7671),p=r(2103),h=["x1","y1","x2","y2","key"],d=["offset"];function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function b(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(){return g=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},g.apply(this,arguments)}function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var w=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.ry;return n.createElement("rect",{x:o,y:i,ry:u,width:a,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function O(t,e){var r;if(n.isValidElement(t))r=n.cloneElement(t,e);else if(i()(t))r=t(e);else{var o=e.x1,a=e.y1,c=e.x2,l=e.y2,s=e.key,f=x(e,h),p=(0,u.J9)(f,!1),y=(p.offset,x(p,d));r=n.createElement("line",g({},y,{x1:o,y1:a,x2:c,y2:l,fill:"none",key:s}))}return r}function j(t){var e=t.x,r=t.width,o=t.horizontal,i=void 0===o||o,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){var a=m(m({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o});return O(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function A(t){var e=t.y,r=t.height,o=t.vertical,i=void 0===o||o,a=t.verticalPoints;if(!i||!a||!a.length)return null;var c=a.map(function(n,o){var a=m(m({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o});return O(i,a)});return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function S(t){var e=t.horizontalFill,r=t.fillOpacity,o=t.x,i=t.y,a=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=!s[u+1]?i+c-t:s[u+1]-t;if(l<=0)return null;var f=u%e.length;return n.createElement("rect",{key:"react-".concat(u),y:t,x:o,height:l,width:a,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function P(t){var e=t.vertical,r=void 0===e||e,o=t.verticalFill,i=t.fillOpacity,a=t.x,c=t.y,u=t.width,l=t.height,s=t.verticalPoints;if(!r||!o||!o.length)return null;var f=s.map(function(t){return Math.round(t+a-a)}).sort(function(t,e){return t-e});a!==f[0]&&f.unshift(0);var p=f.map(function(t,e){var r=!f[e+1]?a+u-t:f[e+1]-t;if(r<=0)return null;var s=e%o.length;return n.createElement("rect",{key:"react-".concat(e),x:t,y:c,width:r,height:l,stroke:"none",fill:o[s],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},p)}var E=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},k=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return(0,l.PW)((0,s.f)(m(m(m({},f.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},M={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function T(t){var e,r,o,u,l,s,f=(0,p.yi)(),h=(0,p.rY)(),d=(0,p.hj)(),v=m(m({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:M.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:M.fill,horizontal:null!==(o=t.horizontal)&&void 0!==o?o:M.horizontal,horizontalFill:null!==(u=t.horizontalFill)&&void 0!==u?u:M.horizontalFill,vertical:null!==(l=t.vertical)&&void 0!==l?l:M.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:M.verticalFill,x:(0,c.Et)(t.x)?t.x:d.left,y:(0,c.Et)(t.y)?t.y:d.top,width:(0,c.Et)(t.width)?t.width:d.width,height:(0,c.Et)(t.height)?t.height:d.height}),b=v.x,x=v.y,O=v.width,T=v.height,_=v.syncWithTicks,C=v.horizontalValues,I=v.verticalValues,D=(0,p.pj)(),N=(0,p.$G)();if(!(0,c.Et)(O)||O<=0||!(0,c.Et)(T)||T<=0||!(0,c.Et)(b)||b!==+b||!(0,c.Et)(x)||x!==+x)return null;var B=v.verticalCoordinatesGenerator||E,R=v.horizontalCoordinatesGenerator||k,L=v.horizontalPoints,z=v.verticalPoints;if((!L||!L.length)&&i()(R)){var F=C&&C.length,U=R({yAxis:N?m(m({},N),{},{ticks:F?C:N.ticks}):void 0,width:f,height:h,offset:d},!!F||_);(0,a.R)(Array.isArray(U),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(y(U),"]")),Array.isArray(U)&&(L=U)}if((!z||!z.length)&&i()(B)){var W=I&&I.length,q=B({xAxis:D?m(m({},D),{},{ticks:W?I:D.ticks}):void 0,width:f,height:h,offset:d},!!W||_);(0,a.R)(Array.isArray(q),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(y(q),"]")),Array.isArray(q)&&(z=q)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(w,{fill:v.fill,fillOpacity:v.fillOpacity,x:v.x,y:v.y,width:v.width,height:v.height,ry:v.ry}),n.createElement(j,g({},v,{offset:d,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(A,g({},v,{offset:d,verticalPoints:z,xAxis:D,yAxis:N})),n.createElement(S,g({},v,{horizontalPoints:L})),n.createElement(P,g({},v,{verticalPoints:z})))}T.displayName="CartesianGrid"},7760:t=>{t.exports=function(t){return this.__data__.get(t)}},7828:t=>{t.exports=function(){return[]}},7857:(t,e,r)=>{var n=r(3440),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},7869:(t,e,r)=>{"use strict";r.d(e,{f:()=>n});var n=function(t){return null};n.displayName="Cell"},7894:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},7937:(t,e,r)=>{var n=r(6954),o=r(4657);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},8114:t=>{t.exports=function(t,e){return t.has(e)}},8182:(t,e,r)=>{var n=r(2070),o=r(5713),i=r(2074),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},8189:(t,e,r)=>{var n=r(3871);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},8210:function(t,e,r){var n;!function(){"use strict";var o,i=1e9,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},c=!0,u="[DecimalError] ",l=u+"Invalid argument: ",s=u+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=1e7,y=9007199254740991,v=f(1286742750677284.5),m={};function b(t,e){var r,n,o,i,a,u,l,s,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),c?k(e,p):e;if(l=t.d,s=e.d,a=t.e,o=e.e,l=l.slice(),i=a-o){for(i<0?(n=l,i=-i,u=s.length):(n=s,o=a,u=l.length),i>(u=(a=Math.ceil(p/7))>u?a+1:u+1)&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((u=l.length)-(i=s.length)<0&&(i=u,n=s,s=l,l=n),r=0;i;)r=(l[--i]=l[i]+s[i]+r)/d|0,l[i]%=d;for(r&&(l.unshift(r),++o),u=l.length;0==l[--u];)l.pop();return e.d=l,e.e=o,c?k(e,p):e}function g(t,e,r){if(t!==~~t||t<e||t>r)throw Error(l+t)}function x(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=S(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=S(r))}else if(0===a)return"0";for(;a%10===0;)a/=10;return i+a}m.absoluteValue=m.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},m.comparedTo=m.cmp=function(t){var e,r,n,o,i=this;if(t=new i.constructor(t),i.s!==t.s)return i.s||-t.s;if(i.e!==t.e)return i.e>t.e^i.s<0?1:-1;for(e=0,r=(n=i.d.length)<(o=t.d.length)?n:o;e<r;++e)if(i.d[e]!==t.d[e])return i.d[e]>t.d[e]^i.s<0?1:-1;return n===o?0:n>o^i.s<0?1:-1},m.decimalPlaces=m.dp=function(){var t=this,e=t.d.length-1,r=7*(e-t.e);if(e=t.d[e])for(;e%10==0;e/=10)r--;return r<0?0:r},m.dividedBy=m.div=function(t){return w(this,new this.constructor(t))},m.dividedToIntegerBy=m.idiv=function(t){var e=this.constructor;return k(w(this,new e(t),0,1),e.precision)},m.equals=m.eq=function(t){return!this.cmp(t)},m.exponent=function(){return j(this)},m.greaterThan=m.gt=function(t){return this.cmp(t)>0},m.greaterThanOrEqualTo=m.gte=function(t){return this.cmp(t)>=0},m.isInteger=m.isint=function(){return this.e>this.d.length-2},m.isNegative=m.isneg=function(){return this.s<0},m.isPositive=m.ispos=function(){return this.s>0},m.isZero=function(){return 0===this.s},m.lessThan=m.lt=function(t){return this.cmp(t)<0},m.lessThanOrEqualTo=m.lte=function(t){return this.cmp(t)<1},m.logarithm=m.log=function(t){var e,r=this,n=r.constructor,i=n.precision,a=i+5;if(void 0===t)t=new n(10);else if((t=new n(t)).s<1||t.eq(o))throw Error(u+"NaN");if(r.s<1)throw Error(u+(r.s?"NaN":"-Infinity"));return r.eq(o)?new n(0):(c=!1,e=w(P(r,a),P(t,a),a),c=!0,k(e,i))},m.minus=m.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?M(e,t):b(e,(t.s=-t.s,t))},m.modulo=m.mod=function(t){var e,r=this,n=r.constructor,o=n.precision;if(!(t=new n(t)).s)throw Error(u+"NaN");return r.s?(c=!1,e=w(r,t,0,1).times(t),c=!0,r.minus(e)):k(new n(r),o)},m.naturalExponential=m.exp=function(){return O(this)},m.naturalLogarithm=m.ln=function(){return P(this)},m.negated=m.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},m.plus=m.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?b(e,t):M(e,(t.s=-t.s,t))},m.precision=m.sd=function(t){var e,r,n,o=this;if(void 0!==t&&t!==!!t&&1!==t&&0!==t)throw Error(l+t);if(e=j(o)+1,r=7*(n=o.d.length-1)+1,n=o.d[n]){for(;n%10==0;n/=10)r--;for(n=o.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},m.squareRoot=m.sqrt=function(){var t,e,r,n,o,i,a,l=this,s=l.constructor;if(l.s<1){if(!l.s)return new s(0);throw Error(u+"NaN")}for(t=j(l),c=!1,0==(o=Math.sqrt(+l))||o==1/0?(((e=x(l.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new s(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new s(o.toString()),o=a=(r=s.precision)+3;;)if(n=(i=n).plus(w(l,i,a+2)).times(.5),x(i.d).slice(0,a)===(e=x(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(k(i,r+1,0),i.times(i).eq(l)){n=i;break}}else if("9999"!=e)break;a+=4}return c=!0,k(n,r)},m.times=m.mul=function(t){var e,r,n,o,i,a,u,l,s,f=this,p=f.constructor,h=f.d,y=(t=new p(t)).d;if(!f.s||!t.s)return new p(0);for(t.s*=f.s,r=f.e+t.e,(l=h.length)<(s=y.length)&&(i=h,h=y,y=i,a=l,l=s,s=a),i=[],n=a=l+s;n--;)i.push(0);for(n=s;--n>=0;){for(e=0,o=l+n;o>n;)u=i[o]+y[n]*h[o-n-1]+e,i[o--]=u%d|0,e=u/d|0;i[o]=(i[o]+e)%d|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,c?k(t,p.precision):t},m.toDecimalPlaces=m.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),void 0===t?r:(g(t,0,i),void 0===e?e=n.rounding:g(e,0,8),k(r,t+j(r)+1,e))},m.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=T(n,!0):(g(t,0,i),void 0===e?e=o.rounding:g(e,0,8),r=T(n=k(new o(n),t+1,e),!0,t+1)),r},m.toFixed=function(t,e){var r,n,o=this,a=o.constructor;return void 0===t?T(o):(g(t,0,i),void 0===e?e=a.rounding:g(e,0,8),r=T((n=k(new a(o),t+j(o)+1,e)).abs(),!1,t+j(n)+1),o.isneg()&&!o.isZero()?"-"+r:r)},m.toInteger=m.toint=function(){var t=this,e=t.constructor;return k(new e(t),j(t)+1,e.rounding)},m.toNumber=function(){return+this},m.toPower=m.pow=function(t){var e,r,n,i,a,l,s=this,p=s.constructor,h=+(t=new p(t));if(!t.s)return new p(o);if(!(s=new p(s)).s){if(t.s<1)throw Error(u+"Infinity");return s}if(s.eq(o))return s;if(n=p.precision,t.eq(o))return k(s,n);if(l=(e=t.e)>=(r=t.d.length-1),a=s.s,l){if((r=h<0?-h:h)<=y){for(i=new p(o),e=Math.ceil(n/7+4),c=!1;r%2&&_((i=i.times(s)).d,e),0!==(r=f(r/2));)_((s=s.times(s)).d,e);return c=!0,t.s<0?new p(o).div(i):k(i,n)}}else if(a<0)throw Error(u+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,s.s=1,c=!1,i=t.times(P(s,n+12)),c=!0,(i=O(i)).s=a,i},m.toPrecision=function(t,e){var r,n,o=this,a=o.constructor;return void 0===t?n=T(o,(r=j(o))<=a.toExpNeg||r>=a.toExpPos):(g(t,1,i),void 0===e?e=a.rounding:g(e,0,8),n=T(o=k(new a(o),t,e),t<=(r=j(o))||r<=a.toExpNeg,t)),n},m.toSignificantDigits=m.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(g(t,1,i),void 0===e?e=r.rounding:g(e,0,8)),k(new r(this),t,e)},m.toString=m.valueOf=m.val=m.toJSON=function(){var t=this,e=j(t),r=t.constructor;return T(t,e<=r.toExpNeg||e>=r.toExpPos)};var w=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%d|0,n=r/d|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=n*d+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,l,s,f,p,h,y,v,m,b,g,x,w,O,A,S,P,E,M=n.constructor,T=n.s==o.s?1:-1,_=n.d,C=o.d;if(!n.s)return new M(n);if(!o.s)throw Error(u+"Division by zero");for(l=n.e-o.e,P=C.length,A=_.length,v=(y=new M(T)).d=[],s=0;C[s]==(_[s]||0);)++s;if(C[s]>(_[s]||0)&&--l,(x=null==i?i=M.precision:a?i+(j(n)-j(o))+1:i)<0)return new M(0);if(x=x/7+2|0,s=0,1==P)for(f=0,C=C[0],x++;(s<A||f)&&x--;s++)w=f*d+(_[s]||0),v[s]=w/C|0,f=w%C|0;else{for((f=d/(C[0]+1)|0)>1&&(C=t(C,f),_=t(_,f),P=C.length,A=_.length),O=P,b=(m=_.slice(0,P)).length;b<P;)m[b++]=0;(E=C.slice()).unshift(0),S=C[0],C[1]>=d/2&&++S;do{f=0,(c=e(C,m,P,b))<0?(g=m[0],P!=b&&(g=g*d+(m[1]||0)),(f=g/S|0)>1?(f>=d&&(f=d-1),1==(c=e(p=t(C,f),m,h=p.length,b=m.length))&&(f--,r(p,P<h?E:C,h))):(0==f&&(c=f=1),p=C.slice()),(h=p.length)<b&&p.unshift(0),r(m,p,b),-1==c&&(c=e(C,m,P,b=m.length))<1&&(f++,r(m,P<b?E:C,b)),b=m.length):0===c&&(f++,m=[0]),v[s++]=f,c&&m[0]?m[b++]=_[O]||0:(m=[_[O]],b=1)}while((O++<A||void 0!==m[0])&&x--)}return v[0]||v.shift(),y.e=l,k(y,a?i+j(y)+1:i)}}();function O(t,e){var r,n,i,a,u,l=0,f=0,h=t.constructor,d=h.precision;if(j(t)>16)throw Error(s+j(t));if(!t.s)return new h(o);for(null==e?(c=!1,u=d):u=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(u+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=i=new h(o),h.precision=u;;){if(n=k(n.times(t),u),r=r.times(++l),x((a=i.plus(w(n,r,u))).d).slice(0,u)===x(i.d).slice(0,u)){for(;f--;)i=k(i.times(i),u);return h.precision=d,null==e?(c=!0,k(i,d)):i}i=a}}function j(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function A(t,e,r){if(e>t.LN10.sd())throw c=!0,r&&(t.precision=r),Error(u+"LN10 precision limit exceeded");return k(new t(t.LN10),e)}function S(t){for(var e="";t--;)e+="0";return e}function P(t,e){var r,n,i,a,l,s,f,p,h,d=1,y=t,v=y.d,m=y.constructor,b=m.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(o))return new m(0);if(null==e?(c=!1,p=b):p=e,y.eq(10))return null==e&&(c=!0),A(m,p);if(p+=10,m.precision=p,n=(r=x(v)).charAt(0),a=j(y),!(Math.abs(a)<15e14))return f=A(m,p+2,b).times(a+""),y=P(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=b,null==e?(c=!0,k(y,b)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=x((y=y.times(t)).d)).charAt(0),d++;for(a=j(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),s=l=y=w(y.minus(o),y.plus(o),p),h=k(y.times(y),p),i=3;;){if(l=k(l.times(h),p),x((f=s.plus(w(l,new m(i),p))).d).slice(0,p)===x(s.d).slice(0,p))return s=s.times(2),0!==a&&(s=s.plus(A(m,p+2,b).times(a+""))),s=w(s,new m(d),p),m.precision=b,null==e?(c=!0,k(s,b)):s;s=f,i+=2}}function E(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=f(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),c&&(t.e>v||t.e<-v))throw Error(s+r)}else t.s=0,t.e=0,t.d=[0];return t}function k(t,e,r){var n,o,i,a,u,l,h,y,m=t.d;for(a=1,i=m[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,h=m[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=m.length))return t;for(h=i=m[y],a=1;i>=10;i/=10)a++;o=(n%=7)-7+a}if(void 0!==r&&(u=h/(i=p(10,a-o-1))%10|0,l=e<0||void 0!==m[y+1]||h%i,l=r<4?(u||l)&&(0==r||r==(t.s<0?3:2)):u>5||5==u&&(4==r||l||6==r&&(n>0?o>0?h/p(10,a-o):0:m[y-1])%10&1||r==(t.s<0?8:7))),e<1||!m[0])return l?(i=j(t),m.length=1,e=e-i-1,m[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(m.length=1,m[0]=t.e=t.s=0),t;if(0==n?(m.length=y,i=1,y--):(m.length=y+1,i=p(10,7-n),m[y]=o>0?(h/p(10,a-o)%p(10,o)|0)*i:0),l)for(;;){if(0==y){(m[0]+=i)==d&&(m[0]=1,++t.e);break}if(m[y]+=i,m[y]!=d)break;m[y--]=0,i=1}for(n=m.length;0===m[--n];)m.pop();if(c&&(t.e>v||t.e<-v))throw Error(s+j(t));return t}function M(t,e){var r,n,o,i,a,u,l,s,f,p,h=t.constructor,y=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),c?k(e,y):e;if(l=t.d,p=e.d,n=e.e,s=t.e,l=l.slice(),a=s-n){for((f=a<0)?(r=l,a=-a,u=p.length):(r=p,n=s,u=l.length),a>(o=Math.max(Math.ceil(y/7),u)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=l.length)<(u=p.length))&&(u=o),o=0;o<u;o++)if(l[o]!=p[o]){f=l[o]<p[o];break}a=0}for(f&&(r=l,l=p,p=r,e.s=-e.s),u=l.length,o=p.length-u;o>0;--o)l[u++]=0;for(o=p.length;o>a;){if(l[--o]<p[o]){for(i=o;i&&0===l[--i];)l[i]=d-1;--l[i],l[o]+=d}l[o]-=p[o]}for(;0===l[--u];)l.pop();for(;0===l[0];l.shift())--n;return l[0]?(e.d=l,e.e=n,c?k(e,y):e):new h(0)}function T(t,e,r){var n,o=j(t),i=x(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+S(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+S(-o-1)+i,r&&(n=r-a)>0&&(i+=S(n))):o>=a?(i+=S(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+S(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=S(n))),t.s<0?"-"+i:i}function _(t,e){if(t.length>e)return t.length=e,!0}function C(t){if(!t||"object"!==typeof t)throw Error(u+"Object expected");var e,r,n,o=["precision",1,i,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(!(f(n)===n&&n>=o[e+1]&&n<=o[e+2]))throw Error(l+r+": "+n);this[r]=n}if(void 0!==(n=t[r="LN10"])){if(n!=Math.LN10)throw Error(l+r+": "+n);this[r]=new this(n)}return this}a=function t(e){var r,n,o;function i(t){var e=this;if(!(e instanceof i))return new i(t);if(e.constructor=i,t instanceof i)return e.s=t.s,e.e=t.e,void(e.d=(t=t.d)?t.slice():t);if("number"===typeof t){if(0*t!==0)throw Error(l+t);if(t>0)e.s=1;else{if(!(t<0))return e.s=0,e.e=0,void(e.d=[0]);t=-t,e.s=-1}return t===~~t&&t<1e7?(e.e=0,void(e.d=[t])):E(e,t.toString())}if("string"!==typeof t)throw Error(l+t);if(45===t.charCodeAt(0)?(t=t.slice(1),e.s=-1):e.s=1,!h.test(t))throw Error(l+t);E(e,t)}if(i.prototype=m,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=C,void 0===e&&(e={}),e)for(o=["precision","rounding","toExpNeg","toExpPos","LN10"],r=0;r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}(a),a.default=a.Decimal=a,o=new a(1),void 0===(n=function(){return a}.call(e,r,e,t))||(t.exports=n)}()},8259:(t,e,r)=>{var n=r(5797);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},8325:(t,e,r)=>{var n=r(2541),o=r(5654),i=r(3279),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},8387:(t,e,r)=>{"use strict";function n(t){var e,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(r=n(t[e]))&&(o&&(o+=" "),o+=r)}else for(r in t)t[r]&&(o&&(o+=" "),o+=r);return o}r.d(e,{A:()=>o});const o=function(){for(var t,e,r=0,o="",i=arguments.length;r<i;r++)(t=arguments[r])&&(e=n(t))&&(o&&(o+=" "),o+=e);return o}},8420:(t,e,r)=>{"use strict";r.d(e,{gu:()=>Ye});var n=r(5043),o=r(9686),i=r.n(o),a=r(1629),c=r.n(a),u=r(6604),l=r.n(u),s=r(3097),f=r.n(s),p=r(7424),h=r.n(p),d=r(9889),y=r.n(d),v=r(8387),m=r(3404),b=r(4794),g=r(1639),x=r(6150),w=r(1327),O=r(8892),j=r(4342),A=r(240),S=r(2099),P=r(4140),E=r(202),k=r(6307);function M(t){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},M(t)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){C(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=M(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==M(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var I=["Webkit","Moz","O","ms"];function D(t){return D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},D(t)}function N(){return N=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},N.apply(this,arguments)}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function R(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(Object(r),!0).forEach(function(e){q(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function L(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,X(n.key),n)}}function z(t,e,r){return e=U(e),function(t,e){if(e&&("object"===D(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,F()?Reflect.construct(e,r||[],U(t).constructor):e.apply(t,r))}function F(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(F=function(){return!!t})()}function U(t){return U=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},U(t)}function W(t,e){return W=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},W(t,e)}function q(t,e,r){return(e=X(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function X(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:e+""}var H=function(t){return t.changedTouches&&!!t.changedTouches.length},V=function(t){function e(t){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),q(r=z(this,e,[t]),"handleDrag",function(t){r.leaveTimer&&(clearTimeout(r.leaveTimer),r.leaveTimer=null),r.state.isTravellerMoving?r.handleTravellerMove(t):r.state.isSlideMoving&&r.handleSlideDrag(t)}),q(r,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&r.handleDrag(t.changedTouches[0])}),q(r,"handleDragEnd",function(){r.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=r.props,e=t.endIndex,n=t.onDragEnd,o=t.startIndex;null===n||void 0===n||n({endIndex:e,startIndex:o})}),r.detachDragEndListener()}),q(r,"handleLeaveWrapper",function(){(r.state.isTravellerMoving||r.state.isSlideMoving)&&(r.leaveTimer=window.setTimeout(r.handleDragEnd,r.props.leaveTimeOut))}),q(r,"handleEnterSlideOrTraveller",function(){r.setState({isTextActive:!0})}),q(r,"handleLeaveSlideOrTraveller",function(){r.setState({isTextActive:!1})}),q(r,"handleSlideDragStart",function(t){var e=H(t)?t.changedTouches[0]:t;r.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:e.pageX}),r.attachDragEndListener()}),r.travellerDragStartHandlers={startX:r.handleTravellerDragStart.bind(r,"startX"),endX:r.handleTravellerDragStart.bind(r,"endX")},r.state={},r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&W(t,e)}(e,t),r=e,i=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,o=t.width,i=t.height,a=t.stroke,c=Math.floor(r+i/2)-1;return n.createElement(n.Fragment,null,n.createElement("rect",{x:e,y:r,width:o,height:i,fill:a,stroke:"none"}),n.createElement("line",{x1:e+1,y1:c,x2:e+o-1,y2:c,fill:"none",stroke:"#fff"}),n.createElement("line",{x1:e+1,y1:c+2,x2:e+o-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,r){return n.isValidElement(t)?n.cloneElement(t,r):c()(t)?t(r):e.renderDefaultTraveller(r)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return R({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=(0,S.z)().domain(l()(0,c)).range([o,o+i-a]),s=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:s}}({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var s=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=0,n=t.length-1;n-r>1;){var o=Math.floor((r+n)/2);t[o]>e?n=o:r=o}return e>=t[n]?n:r}}],(o=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var r=t.startX,n=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(r,n),l=Math.max(r,n),s=e.getIndexInRange(o,u),f=e.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,E.kr)(r[t],o,t);return c()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});h.startIndex===l&&h.endIndex===s||!f||f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=H(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex;this.setState(q(q({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&function(){var t=h.length-1;return"startX"===n&&(o>i?m%p===0:b%p===0)||o<i&&b===t||"endX"===n&&(o>i?b%p===0:m%p===0)||o>i&&b===t}()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(!(-1===l||l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(q({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.fill,c=t.stroke;return n.createElement("rect",{stroke:c,fill:a,x:e,y:r,width:o,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,o=t.width,i=t.height,a=t.data,c=t.children,u=t.padding,l=n.Children.only(c);return l?n.cloneElement(l,{x:e,y:r,width:o,height:i,margin:u,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(t,r){var o,i,a=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=R(R({},(0,A.J9)(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(o=h[d])||void 0===o?void 0:o.name,", Max value: ").concat(null===(i=h[y])||void 0===i?void 0:i.name);return n.createElement(g.W,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[r],onTouchStart:this.travellerDragStartHandlers[r],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,r))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,o=r.y,i=r.height,a=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return n.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:u,y:o,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,o=t.y,i=t.height,a=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return n.createElement(g.W,{className:"recharts-brush-texts"},n.createElement(P.E,N({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:o+i/2},f),this.getTextOfTick(e)),n.createElement(P.E,N({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:o+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,o=t.children,i=t.x,a=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,m=s.isTravellerFocused;if(!e||!e.length||!(0,k.Et)(i)||!(0,k.Et)(a)||!(0,k.Et)(c)||!(0,k.Et)(u)||c<=0||u<=0)return null;var b=(0,v.A)("recharts-brush",r),x=1===n.Children.count(o),w=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=I.reduce(function(t,n){return _(_({},t),{},C({},n+r,e))},{});return n[t]=e,n}("userSelect","none");return n.createElement(g.W,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:w},this.renderBackground(),x&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||m||l)&&this.renderText())}}])&&L(r.prototype,o),i&&L(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.PureComponent);q(V,"displayName","Brush"),q(V,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var K=r(7213),G=r(7165),J=r(2647),Y=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},Z=r(3831),$=r(155);function Q(){return Q=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Q.apply(this,arguments)}function tt(t){return tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},tt(t)}function et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?et(Object(r),!0).forEach(function(e){ut(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lt(n.key),n)}}function ot(t,e,r){return e=at(e),function(t,e){if(e&&("object"===tt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,it()?Reflect.construct(e,r||[],at(t).constructor):e.apply(t,r))}function it(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(it=function(){return!!t})()}function at(t){return at=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},at(t)}function ct(t,e){return ct=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ct(t,e)}function ut(t,e,r){return(e=lt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lt(t){var e=function(t,e){if("object"!=tt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tt(e)?e:e+""}var st=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),ot(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ct(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this.props,r=t.x,o=t.y,i=t.r,a=t.alwaysShow,c=t.clipPathId,u=(0,k.vh)(r),l=(0,k.vh)(o);if((0,$.R)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=(0,Z.P2)({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return Y(t,"discard")&&!i.isInRange(a)?null:a}(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,m=rt(rt({clipPath:Y(this.props,"hidden")?"url(#".concat(c,")"):void 0},(0,A.J9)(this.props,!0)),{},{cx:f,cy:p});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-dot",y)},e.renderDot(d,m),J.J.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}])&&nt(r.prototype,o),i&&nt(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);ut(st,"displayName","ReferenceDot"),ut(st,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),ut(st,"renderDot",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement(O.c,Q({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var ft=r(4597),pt=r.n(ft),ht=r(2103);function dt(t){return dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dt(t)}function yt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,jt(n.key),n)}}function vt(t,e,r){return e=bt(e),function(t,e){if(e&&("object"===dt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,mt()?Reflect.construct(e,r||[],bt(t).constructor):e.apply(t,r))}function mt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(mt=function(){return!!t})()}function bt(t){return bt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},bt(t)}function gt(t,e){return gt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},gt(t,e)}function xt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function wt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xt(Object(r),!0).forEach(function(e){Ot(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Ot(t,e,r){return(e=jt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function jt(t){var e=function(t,e){if("object"!=dt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dt(e)?e:e+""}function At(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return St(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return St(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function St(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Pt(){return Pt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Pt.apply(this,arguments)}function Et(t){var e=t.x,r=t.y,o=t.segment,i=t.xAxisId,a=t.yAxisId,u=t.shape,l=t.className,s=t.alwaysShow,f=(0,ht.Yp)(),p=(0,ht.AF)(i),h=(0,ht.Nk)(a),d=(0,ht.sk)();if(!f||!d)return null;(0,$.R)(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var y=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(Y(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(Y(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return Y(u,"discard")&&pt()(g,function(e){return!t.isInRange(e)})?null:g}return null}((0,Z.P2)({x:p.scale,y:h.scale}),(0,k.vh)(e),(0,k.vh)(r),o&&2===o.length,d,t.position,p.orientation,h.orientation,t);if(!y)return null;var m=At(y,2),b=m[0],x=b.x,w=b.y,O=m[1],j=O.x,S=O.y,P=wt(wt({clipPath:Y(t,"hidden")?"url(#".concat(f,")"):void 0},(0,A.J9)(t,!0)),{},{x1:x,y1:w,x2:j,y2:S});return n.createElement(g.W,{className:(0,v.A)("recharts-reference-line",l)},function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement("line",Pt({},e,{className:"recharts-reference-line-line"}))}(u,P),J.J.renderCallByParent(t,(0,Z.vh)({x1:x,y1:w,x2:j,y2:S})))}var kt=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),vt(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&gt(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){return n.createElement(Et,this.props)}}])&&yt(r.prototype,o),i&&yt(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);function Mt(){return Mt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mt.apply(this,arguments)}function Tt(t){return Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Tt(t)}function _t(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function Ct(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_t(Object(r),!0).forEach(function(e){Lt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_t(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function It(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,zt(n.key),n)}}function Dt(t,e,r){return e=Bt(e),function(t,e){if(e&&("object"===Tt(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Nt()?Reflect.construct(e,r||[],Bt(t).constructor):e.apply(t,r))}function Nt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Nt=function(){return!!t})()}function Bt(t){return Bt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Bt(t)}function Rt(t,e){return Rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Rt(t,e)}function Lt(t,e,r){return(e=zt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function zt(t){var e=function(t,e){if("object"!=Tt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Tt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Tt(e)?e:e+""}Ot(kt,"displayName","ReferenceLine"),Ot(kt,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var Ft=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),Dt(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Rt(t,e)}(e,t),r=e,(o=[{key:"render",value:function(){var t=this.props,r=t.x1,o=t.x2,i=t.y1,a=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;(0,$.R)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,k.vh)(r),f=(0,k.vh)(o),p=(0,k.vh)(i),h=(0,k.vh)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=(0,Z.P2)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!Y(o,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,Z.sl)(p,h):null}(s,f,p,h,this.props);if(!y&&!d)return null;var m=Y(this.props,"hidden")?"url(#".concat(l,")"):void 0;return n.createElement(g.W,{className:(0,v.A)("recharts-reference-area",c)},e.renderRect(d,Ct(Ct({clipPath:m},(0,A.J9)(this.props,!0)),y)),J.J.renderCallByParent(this.props,y))}}])&&It(r.prototype,o),i&&It(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,o,i}(n.Component);function Ut(t){return function(t){if(Array.isArray(t))return Wt(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"===typeof t)return Wt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wt(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}Lt(Ft,"displayName","ReferenceArea"),Lt(Ft,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),Lt(Ft,"renderRect",function(t,e){return n.isValidElement(t)?n.cloneElement(t,e):c()(t)?t(e):n.createElement(j.M,Mt({},e,{className:"recharts-reference-area-rect"}))});var qt=function(t,e,r,n,o){var i=(0,A.aS)(t,kt),a=(0,A.aS)(t,st),c=[].concat(Ut(i),Ut(a)),u=(0,A.aS)(t,Ft),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&Y(e.props,"extendDomain")&&(0,k.Et)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&Y(e.props,"extendDomain")&&(0,k.Et)(e.props[p])&&(0,k.Et)(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,k.Et)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},Xt=r(165),Ht=r(5248),Vt=r(7283),Kt=new(r.n(Vt)()),Gt="recharts.syncMouseEvents",Jt=r(7287);function Yt(t){return Yt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Yt(t)}function Zt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Qt(n.key),n)}}function $t(t,e,r){return(e=Qt(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Qt(t){var e=function(t,e){if("object"!=Yt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Yt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Yt(e)?e:e+""}var te=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),$t(this,"activeIndex",0),$t(this,"coordinateList",[]),$t(this,"layout","horizontal")},(e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!==n&&void 0!==n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!==i&&void 0!==i?i:this.container,this.layout=null!==c&&void 0!==c?c:this.layout,this.offset=null!==l&&void 0!==l?l:this.offset,this.mouseHandlerCallback=null!==f&&void 0!==f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){var t,e;if("horizontal"===this.layout&&0!==this.coordinateList.length){var r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=n+a+c,s=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:l,pageY:s})}}}])&&Zt(t.prototype,e),r&&Zt(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,r}();var ee=r(879),re=r(8471);function ne(t){return ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ne(t)}var oe=["x","y","top","left","width","height","className"];function ie(){return ie=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ie.apply(this,arguments)}function ae(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ce(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=ne(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ne(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ne(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ue(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var le=function(t,e,r,n,o,i){return"M".concat(t,",").concat(o,"v").concat(n,"M").concat(i,",").concat(e,"h").concat(r)},se=function(t){var e=t.x,r=void 0===e?0:e,o=t.y,i=void 0===o?0:o,a=t.top,c=void 0===a?0:a,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ae(Object(r),!0).forEach(function(e){ce(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ae(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:l,width:f,height:h},ue(t,oe));return(0,k.Et)(r)&&(0,k.Et)(i)&&(0,k.Et)(f)&&(0,k.Et)(h)&&(0,k.Et)(c)&&(0,k.Et)(l)?n.createElement("path",ie({},(0,A.J9)(y,!0),{className:(0,v.A)("recharts-cross",d),d:le(r,i,f,h,c,l)})):null};function fe(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,Xt.IZ)(e,r,n,o),(0,Xt.IZ)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var pe=r(677);function he(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return fe(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,Xt.IZ)(c,u,l,f),h=(0,Xt.IZ)(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}function de(t){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},de(t)}function ye(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ve(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ye(Object(r),!0).forEach(function(e){me(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ye(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function me(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=de(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=de(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==de(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function be(t){var e,r,o,i=t.element,a=t.tooltipEventType,c=t.isActive,u=t.activeCoordinate,l=t.activePayload,s=t.offset,f=t.activeTooltipIndex,p=t.tooltipAxisBandSize,h=t.layout,d=t.chartName,y=null!==(e=i.props.cursor)&&void 0!==e?e:null===(r=i.type.defaultProps)||void 0===r?void 0:r.cursor;if(!i||!y||!c||!u||"ScatterChart"!==d&&"axis"!==a)return null;var m=re.I;if("ScatterChart"===d)o=u,m=se;else if("BarChart"===d)o=function(t,e,r,n){var o=n/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===t?e.x-o:r.left+.5,y:"horizontal"===t?r.top+.5:e.y-o,width:"horizontal"===t?n:r.width-1,height:"horizontal"===t?r.height-1:n}}(h,u,s,p),m=j.M;else if("radial"===h){var b=fe(u),g=b.cx,x=b.cy,w=b.radius;o={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=pe.h}else o={points:he(h,u,s)},m=re.I;var O=ve(ve(ve(ve({stroke:"#ccc",pointerEvents:"none"},s),o),(0,A.J9)(y,!1)),{},{payload:l,payloadIndex:f,className:(0,v.A)("recharts-tooltip-cursor",y.className)});return(0,n.isValidElement)(y)?(0,n.cloneElement)(y,O):(0,n.createElement)(m,O)}var ge=["item"],xe=["children","className","width","height","style","compact","title","desc"];function we(t){return we="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},we(t)}function Oe(){return Oe=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Oe.apply(this,arguments)}function je(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||_e(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ae(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Se(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Be(n.key),n)}}function Pe(t,e,r){return e=ke(e),function(t,e){if(e&&("object"===we(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,Ee()?Reflect.construct(e,r||[],ke(t).constructor):e.apply(t,r))}function Ee(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(Ee=function(){return!!t})()}function ke(t){return ke=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ke(t)}function Me(t,e){return Me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Me(t,e)}function Te(t){return function(t){if(Array.isArray(t))return Ce(t)}(t)||function(t){if("undefined"!==typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||_e(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _e(t,e){if(t){if("string"===typeof t)return Ce(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ce(t,e):void 0}}function Ce(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ie(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function De(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ie(Object(r),!0).forEach(function(e){Ne(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ie(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function Ne(t,e,r){return(e=Be(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Be(t){var e=function(t,e){if("object"!=we(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=we(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==we(e)?e:e+""}var Re={xAxis:["bottom","top"],yAxis:["left","right"]},Le={width:"100%",height:"100%"},ze={x:0,y:0};function Fe(t){return t}var Ue=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!==r&&void 0!==r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(Te(t),Te(r)):t},[]);return i.length>0?i:t&&t.length&&(0,k.Et)(n)&&(0,k.Et)(o)?t.slice(n,o+1):[]};function We(t){return"number"===t?[0,"auto"]:void 0}var qe=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=Ue(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,k.eP)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(Te(o),[(0,E.zb)(c,l)]):o},[])},Xe=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i=function(t,e){return"horizontal"===e?t.x:"vertical"===e?t.y:"centric"===e?t.angle:t.radius}(o,r),a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=(0,E.gH)(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=qe(t,e,l,s),p=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return De(De(De({},n),(0,Xt.IZ)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return De(De(De({},n),(0,Xt.IZ)(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return ze}(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},He=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,a=e.axisIdKey,c=e.stackGroups,u=e.dataStartIndex,s=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,E._L)(f,o);return r.reduce(function(e,r){var y,v=void 0!==r.type.defaultProps?De(De({},r.type.defaultProps),r.props):r.props,m=v.type,b=v.dataKey,g=v.allowDataOverflow,x=v.allowDuplicatedCategory,w=v.scale,O=v.ticks,j=v.includeHidden,A=v[a];if(e[A])return e;var S,P,M,T=Ue(t.data,{graphicalItems:n.filter(function(t){var e;return(a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a])===A}),dataStartIndex:u,dataEndIndex:s}),_=T.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null===t||void 0===t?void 0:t[0],o=null===t||void 0===t?void 0:t[1];if(n&&o&&(0,k.Et)(n)&&(0,k.Et)(o))return!0}return!1})(v.domain,g,m)&&(S=(0,E.AQ)(v.domain,null,g),!d||"number"!==m&&"auto"===w||(M=(0,E.Ay)(T,b,"category")));var C=We(m);if(!S||0===S.length){var I,D=null!==(I=v.domain)&&void 0!==I?I:C;if(b){if(S=(0,E.Ay)(T,b,m),"category"===m&&d){var N=(0,k.CG)(S);x&&N?(P=S,S=l()(0,_)):x||(S=(0,E.KC)(D,S,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(Te(t),[e])},[]))}else if("category"===m)S=x?S.filter(function(t){return""!==t&&!i()(t)}):(0,E.KC)(D,S,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||i()(e)?t:[].concat(Te(t),[e])},[]);else if("number"===m){var B=(0,E.A1)(T,n.filter(function(t){var e,r,n=a in t.props?t.props[a]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[a],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===A&&(j||!o)}),b,o,f);B&&(S=B)}!d||"number"!==m&&"auto"===w||(M=(0,E.Ay)(T,b,"category"))}else S=d?l()(0,_):c&&c[A]&&c[A].hasStack&&"number"===m?"expand"===h?[0,1]:(0,E.Mk)(c[A].stackGroups,u,s):(0,E.vf)(T,n.filter(function(t){var e=a in t.props?t.props[a]:t.type.defaultProps[a],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===A&&(j||!r)}),m,f,!0);if("number"===m)S=qt(p,S,A,o,O),D&&(S=(0,E.AQ)(D,S,g));else if("category"===m&&D){var R=D;S.every(function(t){return R.indexOf(t)>=0})&&(S=R)}}return De(De({},e),{},Ne({},A,De(De({},v),{},{axisType:o,domain:S,categoricalDomain:M,duplicateDomain:P,originalDomain:null!==(y=v.domain)&&void 0!==y?y:C,isCategorical:d,layout:f})))},{})},Ve=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.children,p="".concat(n,"Id"),h=(0,A.aS)(s,o),d={};return h&&h.length?d=He(t,{axes:h,graphicalItems:i,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(d=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,s=t.layout,p=t.children,h=Ue(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),d=h.length,y=(0,E._L)(s,o),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?De(De({},e.type.defaultProps),e.props):e.props)[i],g=We("number");return t[b]?t:(v++,y?m=l()(0,d):a&&a[b]&&a[b].hasStack?(m=(0,E.Mk)(a[b].stackGroups,c,u),m=qt(p,m,b,o)):(m=(0,E.AQ)(g,(0,E.vf)(h,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===b&&!o}),"number",s),n.defaultProps.allowDataOverflow),m=qt(p,m,b,o)),De(De({},t),{},Ne({},b,De(De({axisType:o},n.defaultProps),{},{hide:!0,orientation:f()(Re,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:s}))))},{})}(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:p,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),d},Ke=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,A.BU)(e,V),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:Boolean(r)}},Ge=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},Je=function(t,e){return"xAxis"===e?t[e].width:"yAxis"===e?t[e].height:void 0},Ye=function(t){var e=t.chartName,r=t.GraphicalChild,o=t.defaultTooltipEventType,a=void 0===o?"axis":o,u=t.validateTooltipEventTypes,l=void 0===u?["axis"]:u,s=t.axisComponents,p=t.legendContent,d=t.formatAxisMap,S=t.defaultProps,P=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,a=e.updateId,c=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,f=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=Ge(f),v=y.numericAxisName,b=y.cateAxisName,g=function(t){return!(!t||!t.length)&&t.some(function(t){var e=(0,A.Mn)(t&&t.type);return e&&e.indexOf("Bar")>=0})}(r),x=[];return r.forEach(function(r,y){var w=Ue(t.data,{graphicalItems:[r],dataStartIndex:c,dataEndIndex:u}),O=void 0!==r.type.defaultProps?De(De({},r.type.defaultProps),r.props):r.props,j=O.dataKey,S=O.maxBarSize,P=O["".concat(v,"Id")],k=O["".concat(b,"Id")],M=s.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=O["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,m.A)(!1);var i=n[o];return De(De({},t),{},Ne(Ne({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,E.Rh)(i)))},{}),T=M[b],_=M["".concat(b,"Ticks")],C=n&&n[P]&&n[P].hasStack&&(0,E.kA)(r,n[P].stackGroups),I=(0,A.Mn)(r.type).indexOf("Bar")>=0,D=(0,E.Hj)(T,_),N=[],B=g&&(0,E.tA)({barSize:l,stackGroups:n,totalSize:Je(M,b)});if(I){var R,L,z=i()(S)?d:S,F=null!==(R=null!==(L=(0,E.Hj)(T,_,!0))&&void 0!==L?L:z)&&void 0!==R?R:0;N=(0,E.BX)({barGap:p,barCategoryGap:h,bandSize:F!==D?F:D,sizeList:B[k],maxBarSize:z}),F!==D&&(N=N.map(function(t){return De(De({},t),{},{position:De(De({},t.position),{},{offset:t.position.offset-F/2})})}))}var U=r&&r.type&&r.type.getComposedData;U&&x.push({props:De(De({},U(De(De({},M),{},{displayedData:w,props:t,dataKey:j,item:r,bandSize:D,barPosition:N,offset:o,stackedData:C,layout:f,dataStartIndex:c,dataEndIndex:u}))),{},Ne(Ne(Ne({key:r.key||"item-".concat(y)},v,M[v]),b,M[b]),"animationId",a)),childIndex:(0,A.AW)(r,t.children),item:r})}),x},M=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!(0,A.Me)({props:o}))return null;var u=o.children,l=o.layout,p=o.stackOffset,y=o.data,v=o.reverseStackOrder,m=Ge(l),b=m.numericAxisName,g=m.cateAxisName,x=(0,A.aS)(u,r),O=(0,E.Mn)(y,x,"".concat(b,"Id"),"".concat(g,"Id"),p,v),j=s.reduce(function(t,e){var r="".concat(e.axisType,"Map");return De(De({},t),{},Ne({},r,Ve(o,De(De({},e),{},{graphicalItems:x,stackGroups:e.axisType===b&&O,dataStartIndex:i,dataEndIndex:a}))))},{}),S=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,p=r.margin||{},h=(0,A.BU)(s,V),d=(0,A.BU)(s,w.s),y=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:De(De({},t),{},Ne({},n,t[n]+r.width))},{left:p.left||0,right:p.right||0}),v=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:De(De({},t),{},Ne({},n,f()(t,"".concat(n))+r.height))},{top:p.top||0,bottom:p.bottom||0}),m=De(De({},v),y),b=m.bottom;h&&(m.bottom+=h.props.height||V.defaultProps.height),d&&e&&(m=(0,E.s0)(m,n,r,e));var g=u-m.left-m.right,x=l-m.top-m.bottom;return De(De({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})}(De(De({},j),{},{props:o,graphicalItems:x}),null===n||void 0===n?void 0:n.legendBBox);Object.keys(j).forEach(function(t){j[t]=d(o,j[t],S,t.replace("Map",""),e)});var M=function(t){var e=(0,k.lX)(t),r=(0,E.Rh)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:h()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,E.Hj)(e,r)}}(j["".concat(g,"Map")]),T=P(o,De(De({},j),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:x,stackGroups:O,offset:S}));return De(De({formattedGraphicalItems:T,graphicalItems:x,offset:S,stackGroups:O},M),j)},T=function(t){function r(t){var o,a,u;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r),Ne(u=Pe(this,r,[t]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),Ne(u,"accessibilityManager",new te),Ne(u,"handleLegendBBoxUpdate",function(t){if(t){var e=u.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;u.setState(De({legendBBox:t},M({props:u.props,dataStartIndex:r,dataEndIndex:n,updateId:o},De(De({},u.state),{},{legendBBox:t}))))}}),Ne(u,"handleReceiveSyncEvent",function(t,e,r){if(u.props.syncId===t){if(r===u.eventEmitterSymbol&&"function"!==typeof u.props.syncMethod)return;u.applySyncEvent(e)}}),Ne(u,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==u.state.dataStartIndex||r!==u.state.dataEndIndex){var n=u.state.updateId;u.setState(function(){return De({dataStartIndex:e,dataEndIndex:r},M({props:u.props,dataStartIndex:e,dataEndIndex:r,updateId:n},u.state))}),u.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),Ne(u,"handleMouseEnter",function(t){var e=u.getMouseInfo(t);if(e){var r=De(De({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseEnter;c()(n)&&n(r,t)}}),Ne(u,"triggeredAfterMouseMove",function(t){var e=u.getMouseInfo(t),r=e?De(De({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};u.setState(r),u.triggerSyncEvent(r);var n=u.props.onMouseMove;c()(n)&&n(r,t)}),Ne(u,"handleItemMouseEnter",function(t){u.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),Ne(u,"handleItemMouseLeave",function(){u.setState(function(){return{isTooltipActive:!1}})}),Ne(u,"handleMouseMove",function(t){t.persist(),u.throttleTriggeredAfterMouseMove(t)}),Ne(u,"handleMouseLeave",function(t){u.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};u.setState(e),u.triggerSyncEvent(e);var r=u.props.onMouseLeave;c()(r)&&r(e,t)}),Ne(u,"handleOuterEvent",function(t){var e,r=(0,A.X_)(t),n=f()(u.props,"".concat(r));r&&c()(n)&&n(null!==(e=/.*touch.*/i.test(r)?u.getMouseInfo(t.changedTouches[0]):u.getMouseInfo(t))&&void 0!==e?e:{},t)}),Ne(u,"handleClick",function(t){var e=u.getMouseInfo(t);if(e){var r=De(De({},e),{},{isTooltipActive:!0});u.setState(r),u.triggerSyncEvent(r);var n=u.props.onClick;c()(n)&&n(r,t)}}),Ne(u,"handleMouseDown",function(t){var e=u.props.onMouseDown;c()(e)&&e(u.getMouseInfo(t),t)}),Ne(u,"handleMouseUp",function(t){var e=u.props.onMouseUp;c()(e)&&e(u.getMouseInfo(t),t)}),Ne(u,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),Ne(u,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseDown(t.changedTouches[0])}),Ne(u,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&u.handleMouseUp(t.changedTouches[0])}),Ne(u,"handleDoubleClick",function(t){var e=u.props.onDoubleClick;c()(e)&&e(u.getMouseInfo(t),t)}),Ne(u,"handleContextMenu",function(t){var e=u.props.onContextMenu;c()(e)&&e(u.getMouseInfo(t),t)}),Ne(u,"triggerSyncEvent",function(t){void 0!==u.props.syncId&&Kt.emit(Gt,u.props.syncId,t,u.eventEmitterSymbol)}),Ne(u,"applySyncEvent",function(t){var e=u.props,r=e.layout,n=e.syncMethod,o=u.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)u.setState(De({dataStartIndex:i,dataEndIndex:a},M({props:u.props,dataStartIndex:i,dataEndIndex:a,updateId:o},u.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=u.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"===typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=De(De({},p),{},{x:p.left,y:p.top}),v=Math.min(c,y.x+y.width),m=Math.min(l,y.y+y.height),b=h[s]&&h[s].value,g=qe(u.state,u.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:ze;u.setState(De(De({},t),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else u.setState(t)}),Ne(u,"renderCursor",function(t){var r,o=u.state,i=o.isTooltipActive,a=o.activeCoordinate,c=o.activePayload,l=o.offset,s=o.activeTooltipIndex,f=o.tooltipAxisBandSize,p=u.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:i,d=u.props.layout,y=t.key||"_recharts-cursor";return n.createElement(be,{key:y,activeCoordinate:a,activePayload:c,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),Ne(u,"renderPolarAxis",function(t,e,r){var o=f()(t,"type.axisType"),i=f()(u.state,"".concat(o,"Map")),a=t.type.defaultProps,c=void 0!==a?De(De({},a),t.props):t.props,l=i&&i[c["".concat(o,"Id")]];return(0,n.cloneElement)(t,De(De({},l),{},{className:(0,v.A)(o,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,E.Rh)(l,!0)}))}),Ne(u,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,o=e.polarAngles,i=e.polarRadius,a=u.state,c=a.radiusAxisMap,l=a.angleAxisMap,s=(0,k.lX)(c),f=(0,k.lX)(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(o)?o:(0,E.Rh)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,E.Rh)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),Ne(u,"renderLegend",function(){var t=u.state.formattedGraphicalItems,e=u.props,r=e.children,o=e.width,i=e.height,a=u.props.margin||{},c=o-(a.left||0)-(a.right||0),l=(0,G.g)({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:p});if(!l)return null;var s=l.item,f=Ae(l,ge);return(0,n.cloneElement)(s,De(De({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:u.handleLegendBBoxUpdate}))}),Ne(u,"renderTooltip",function(){var t,e=u.props,r=e.children,o=e.accessibilityLayer,i=(0,A.BU)(r,x.m);if(!i)return null;var a=u.state,c=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=i.props.active)&&void 0!==t?t:c;return(0,n.cloneElement)(i,{viewBox:De(De({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:o})}),Ne(u,"renderBrush",function(t){var e=u.props,r=e.margin,o=e.data,i=u.state,a=i.offset,c=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,E.HQ)(u.handleBrushChange,t.props.onChange),data:o,x:(0,k.Et)(t.props.x)?t.props.x:a.left,y:(0,k.Et)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,k.Et)(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:l,updateId:"brush-".concat(s)})}),Ne(u,"renderReferenceElement",function(t,e,r){if(!t)return null;var o=u.clipPathId,i=u.state,a=i.xAxisMap,c=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:c[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:o})}),Ne(u,"renderActivePoints",function(t){var e=t.item,n=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?De(De({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=De(De({index:i,dataKey:l.dataKey,cx:n.x,cy:n.y,r:4,fill:(0,E.Ps)(e.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},(0,A.J9)(s,!1)),(0,Jt._U)(s));return c.push(r.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(r.renderActiveDot(s,De(De({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),Ne(u,"renderGraphicChild",function(t,e,r){var o=u.filterFormatItem(t,e,r);if(!o)return null;var a=u.getTooltipEventType(),c=u.state,l=c.isTooltipActive,s=c.tooltipAxis,f=c.activeTooltipIndex,p=c.activeLabel,h=u.props.children,d=(0,A.BU)(h,x.m),y=o.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==o.item.type.defaultProps?De(De({},o.item.type.defaultProps),o.item.props):o.item.props,w=g.activeDot,O=g.hide,j=g.activeBar,S=g.activeShape,P=Boolean(!O&&l&&d&&(w||j||S)),M={};"axis"!==a&&d&&"click"===d.props.trigger?M={onClick:(0,E.HQ)(u.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(M={onMouseLeave:(0,E.HQ)(u.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,E.HQ)(u.handleItemMouseEnter,t.props.onMouseEnter)});var T=(0,n.cloneElement)(t,De(De({},o.props),M));if(P){if(!(f>=0)){var _,C=(null!==(_=u.getItemByXY(u.state.activeCoordinate))&&void 0!==_?_:{graphicalItem:T}).graphicalItem,I=C.item,D=void 0===I?t:I,N=C.childIndex,B=De(De(De({},o.props),M),{},{activeIndex:N});return[(0,n.cloneElement)(D,B),null,null]}var R,L;if(s.dataKey&&!s.allowDuplicatedCategory){var z="function"===typeof s.dataKey?function(t){return"function"===typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());R=(0,k.eP)(v,z,p),L=m&&b&&(0,k.eP)(b,z,p)}else R=null===v||void 0===v?void 0:v[f],L=m&&b&&b[f];if(S||j){var F=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,De(De(De({},o.props),M),{},{activeIndex:F})),null,null]}if(!i()(R))return[T].concat(Te(u.renderActivePoints({item:o,activePoint:R,basePoint:L,childIndex:f,isRange:m})))}return m?[T,null,null]:[T,null]}),Ne(u,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,De(De({key:"recharts-customized-".concat(r)},u.props),u.state))}),Ne(u,"renderMap",{CartesianGrid:{handler:Fe,once:!0},ReferenceArea:{handler:u.renderReferenceElement},ReferenceLine:{handler:Fe},ReferenceDot:{handler:u.renderReferenceElement},XAxis:{handler:Fe},YAxis:{handler:Fe},Brush:{handler:u.renderBrush,once:!0},Bar:{handler:u.renderGraphicChild},Line:{handler:u.renderGraphicChild},Area:{handler:u.renderGraphicChild},Radar:{handler:u.renderGraphicChild},RadialBar:{handler:u.renderGraphicChild},Scatter:{handler:u.renderGraphicChild},Pie:{handler:u.renderGraphicChild},Funnel:{handler:u.renderGraphicChild},Tooltip:{handler:u.renderCursor,once:!0},PolarGrid:{handler:u.renderPolarGrid,once:!0},PolarAngleAxis:{handler:u.renderPolarAxis},PolarRadiusAxis:{handler:u.renderPolarAxis},Customized:{handler:u.renderCustomized}}),u.clipPathId="".concat(null!==(o=t.id)&&void 0!==o?o:(0,k.NF)("recharts"),"-clip"),u.throttleTriggeredAfterMouseMove=y()(u.triggeredAfterMouseMove,null!==(a=t.throttleDelay)&&void 0!==a?a:1e3/60),u.state={},u}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Me(t,e)}(r,t),o=r,u=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,A.BU)(e,x.m);if(i){var a=i.props.defaultIndex;if(!("number"!==typeof a||a<0||a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=qe(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=De(De({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){return this.props.accessibilityLayer?(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin&&this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}}),null):null;var r,n}},{key:"componentDidUpdate",value:function(t){(0,A.OV)([(0,A.BU)(t.children,x.m)],[(0,A.BU)(this.props.children,x.m)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,A.BU)(this.props.children,x.m);if(t&&"boolean"===typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:a}return a}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,K.A3)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=Xe(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=(0,k.lX)(u).scale,h=(0,k.lX)(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return De(De({},o),{},{xValue:d,yValue:y},f)}return f?De(De({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;if(u&&l){var s=(0,k.lX)(u);return(0,Xt.yy)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,A.BU)(t,x.m),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),De(De({},(0,Jt._U)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){Kt.on(Gt,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Kt.removeListener(Gt,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,A.Mn)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,o=e.top,i=e.height,a=e.width;return n.createElement("defs",null,n.createElement("clipPath",{id:t},n.createElement("rect",{x:r,y:o,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=je(e,2),n=r[0],o=r[1];return De(De({},t),{},Ne({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=je(e,2),n=r[0],o=r[1];return De(De({},t),{},Ne({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?De(De({},u.type.defaultProps),u.props):u.props,s=(0,A.Mn)(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return(0,j.J)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return(0,Xt.yy)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,ee.NE)(a,n)||(0,ee.nZ)(a,n)||(0,ee.xQ)(a,n)){var h=(0,ee.GG)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:De(De({},a),{},{childIndex:d}),payload:(0,ee.xQ)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t=this;if(!(0,A.Me)(this))return null;var e,r,o=this.props,i=o.children,a=o.className,c=o.width,u=o.height,l=o.style,s=o.compact,f=o.title,p=o.desc,h=Ae(o,xe),d=(0,A.J9)(h,!1);if(s)return n.createElement(ht.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement(b.u,Oe({},d,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),(0,A.ee)(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(e=this.props.tabIndex)&&void 0!==e?e:0,d.role=null!==(r=this.props.role)&&void 0!==r?r:"application",d.onKeyDown=function(e){t.accessibilityManager.keyboardEvent(e)},d.onFocus=function(){t.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return n.createElement(ht.DR,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},n.createElement("div",Oe({className:(0,v.A)("recharts-wrapper",a),style:De({position:"relative",cursor:"default",width:c,height:u},l)},y,{ref:function(e){t.container=e}}),n.createElement(b.u,Oe({},d,{width:c,height:u,title:f,desc:p,style:Le}),this.renderClipPath(),(0,A.ee)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],u&&Se(o.prototype,u),s&&Se(o,s),Object.defineProperty(o,"prototype",{writable:!1}),o;var o,u,s}(n.Component);Ne(T,"displayName",e),Ne(T,"defaultProps",De({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},S)),Ne(T,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,a=t.width,c=t.height,u=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=Ke(t);return De(De(De({},h),{},{updateId:0},M(De(De({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||a!==e.prevWidth||c!==e.prevHeight||u!==e.prevLayout||l!==e.prevStackOffset||!(0,Ht.b)(s,e.prevMargin)){var d=Ke(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=De(De({},Xe(e,n,u)),{},{updateId:e.updateId+1}),m=De(De(De({},d),y),v);return De(De(De({},m),M(De({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:a,prevHeight:c,prevLayout:u,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,A.OV)(o,e.prevChildren)){var b,g,x,w,O=(0,A.BU)(o,V),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:f,S=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:p,P=j!==f||S!==p,E=!i()(n)&&!P?e.updateId:e.updateId+1;return De(De({updateId:E},M(De(De({props:t},e),{},{updateId:E,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),Ne(T,"renderActiveDot",function(t,e,r){var o;return o=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):c()(t)?t(e):n.createElement(O.c,e),n.createElement(g.W,{className:"recharts-active-dot",key:r},o)});var _=(0,n.forwardRef)(function(t,e){return n.createElement(T,Oe({},t,{ref:e}))});return _.displayName=T.displayName,_}},8468:(t,e,r)=>{var n=r(5816),o=r(644),i=r(4020);t.exports=function(t,e,r){return e===e?i(t,e,r):n(t,o,r)}},8471:(t,e,r)=>{"use strict";r.d(e,{I:()=>G});var n=r(5043);function o(){}function i(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function c(t){this._context=t}function u(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}function h(t){return t<0?-1:1}function d(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0),c=(i*o+a*n)/(n+o);return(h(i)+h(a))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs(c))||0}function y(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function v(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function m(t){this._context=t}function b(t){this._context=new g(t)}function g(t){this._context=t}function x(t){this._context=t}function w(t){var e,r,n=t.length-1,o=new Array(n),i=new Array(n),a=new Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(i[n-1]=(t[n]+o[n-1])/2,e=0;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function O(t,e){this._context=t,this._t=e}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},m.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:v(this,this._t0,y(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,v(this,y(this,r=d(this,t,e)),r);break;default:v(this,this._t0,r=d(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(b.prototype=Object.create(m.prototype)).point=function(t,e){m.prototype.point.call(this,e,t)},g.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=w(t),o=w(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var j=r(9236),A=r(3809),S=r(7371);function P(t){return t[0]}function E(t){return t[1]}function k(t,e){var r=(0,A.A)(!0),n=null,o=p,i=null,a=(0,S.i)(c);function c(c){var u,l,s,f=(c=(0,j.A)(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"===typeof t?t:void 0===t?P:(0,A.A)(t),e="function"===typeof e?e:void 0===e?E:(0,A.A)(e),c.x=function(e){return arguments.length?(t="function"===typeof e?e:(0,A.A)(+e),c):t},c.y=function(t){return arguments.length?(e="function"===typeof t?t:(0,A.A)(+t),c):e},c.defined=function(t){return arguments.length?(r="function"===typeof t?t:(0,A.A)(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function M(t,e,r){var n=null,o=(0,A.A)(!0),i=null,a=p,c=null,u=(0,S.i)(l);function l(l){var s,f,p,h,d,y=(l=(0,j.A)(l)).length,v=!1,m=new Array(y),b=new Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return k().defined(o).curve(a).context(i)}return t="function"===typeof t?t:void 0===t?P:(0,A.A)(+t),e="function"===typeof e?e:void 0===e?(0,A.A)(0):(0,A.A)(+e),r="function"===typeof r?r:void 0===r?E:(0,A.A)(+r),l.x=function(e){return arguments.length?(t="function"===typeof e?e:(0,A.A)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"===typeof e?e:(0,A.A)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"===typeof t?t:(0,A.A)(+t),l):n},l.y=function(t){return arguments.length?(e="function"===typeof t?t:(0,A.A)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"===typeof t?t:(0,A.A)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"===typeof t?t:(0,A.A)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"===typeof t?t:(0,A.A)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}var T=r(643),_=r.n(T),C=r(1629),I=r.n(C),D=r(8387),N=r(7287),B=r(240),R=r(6307);function L(t){return L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},L(t)}function z(){return z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},z.apply(this,arguments)}function F(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?F(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function W(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=L(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=L(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==L(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var q={curveBasisClosed:function(t){return new c(t)},curveBasisOpen:function(t){return new u(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new m(t)},curveMonotoneY:function(t){return new b(t)},curveNatural:function(t){return new x(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},X=function(t){return t.x===+t.x&&t.y===+t.y},H=function(t){return t.x},V=function(t){return t.y},K=function(t){var e,r=t.type,n=void 0===r?"linear":r,o=t.points,i=void 0===o?[]:o,a=t.baseLine,c=t.layout,u=t.connectNulls,l=void 0!==u&&u,s=function(t,e){if(I()(t))return t;var r="curve".concat(_()(t));return"curveMonotone"!==r&&"curveBump"!==r||!e?q[r]||p:q["".concat(r).concat("vertical"===e?"Y":"X")]}(n,c),f=l?i.filter(function(t){return X(t)}):i;if(Array.isArray(a)){var h=l?a.filter(function(t){return X(t)}):a,d=f.map(function(t,e){return U(U({},t),{},{base:h[e]})});return(e="vertical"===c?M().y(V).x1(H).x0(function(t){return t.base.x}):M().x(H).y1(V).y0(function(t){return t.base.y})).defined(X).curve(s),e(d)}return(e="vertical"===c&&(0,R.Et)(a)?M().y(V).x1(H).x0(a):(0,R.Et)(a)?M().x(H).y1(V).y0(a):k().x(H).y(V)).defined(X).curve(s),e(f)},G=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?K(t):o;return n.createElement("path",z({},(0,B.J9)(t,!1),(0,N._U)(t),{className:(0,D.A)("recharts-curve",e),d:a,ref:i}))}},8541:(t,e,r)=>{var n=r(9812),o=r(149),i=r(4052),a=r(9841),c=n?n.prototype:void 0,u=c?c.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return u?u.call(e):"";var r=e+"";return"0"==r&&1/e==-1/0?"-0":r}},8643:(t,e,r)=>{"use strict";r.d(e,{y:()=>X});var n=r(5043),o=r(8387),i=r(1744),a=r(9853),c=r.n(a),u=r(9686),l=r.n(u),s=r(1639),f=r(8813),p=r(7869),h=r(1519),d=r(6307),y=r(240),v=r(6015),m=r(202),b=r(7287),g=r(3404),x=r(879),w=["x","y"];function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function j(){return j=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},j.apply(this,arguments)}function A(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?A(Object(r),!0).forEach(function(e){P(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=O(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==O(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function E(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function k(t,e){var r=t.x,n=t.y,o=E(t,w),i="".concat(r),a=parseInt(i,10),c="".concat(n),u=parseInt(c,10),l="".concat(e.height||o.height),s=parseInt(l,10),f="".concat(e.width||o.width),p=parseInt(f,10);return S(S(S(S(S({},e),o),a?{x:a}:{}),u?{y:u}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function M(t){return n.createElement(x.yp,j({shapeType:"rectangle",propTransformer:k,activeClassName:"recharts-active-bar"},t))}var T,_=["value","background"];function C(t){return C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},C(t)}function I(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function D(){return D=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},D.apply(this,arguments)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function B(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach(function(e){W(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function R(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,q(n.key),n)}}function L(t,e,r){return e=F(e),function(t,e){if(e&&("object"===C(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,z()?Reflect.construct(e,r||[],F(t).constructor):e.apply(t,r))}function z(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(z=function(){return!!t})()}function F(t){return F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},F(t)}function U(t,e){return U=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},U(t,e)}function W(t,e,r){return(e=q(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function q(t){var e=function(t,e){if("object"!=C(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=C(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==C(e)?e:e+""}var X=function(t){function e(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return W(t=L(this,e,[].concat(n)),"state",{isAnimationFinished:!1}),W(t,"id",(0,d.NF)("recharts-bar-")),W(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),W(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&U(t,e)}(e,t),r=e,u=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],(a=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,c=r.activeBar,u=(0,y.J9)(this.props,!1);return t&&t.map(function(t,r){var l=r===a,f=l?c:o,p=B(B(B({},u),t),{},{isActive:l,option:f,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(s.W,D({className:"recharts-bar-rectangle"},(0,b.XC)(e.props,t,r),{key:"rectangle-".concat(null===t||void 0===t?void 0:t.x,"-").concat(null===t||void 0===t?void 0:t.y,"-").concat(null===t||void 0===t?void 0:t.value,"-").concat(r)}),n.createElement(M,p))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,o=e.layout,a=e.isAnimationActive,c=e.animationBegin,u=e.animationDuration,l=e.animationEasing,f=e.animationId,p=this.state.prevData;return n.createElement(i.Ay,{begin:c,duration:u,isActive:a,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,a=r.map(function(t,e){var r=p&&p[e];if(r){var n=(0,d.Dj)(r.x,t.x),a=(0,d.Dj)(r.y,t.y),c=(0,d.Dj)(r.width,t.width),u=(0,d.Dj)(r.height,t.height);return B(B({},t),{},{x:n(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===o){var l=(0,d.Dj)(0,t.height)(i);return B(B({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,d.Dj)(0,t.width)(i);return B(B({},t),{},{width:s})});return n.createElement(s.W,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return!(r&&e&&e.length)||n&&c()(n,e)?this.renderRectanglesStatically(e):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,o=e.dataKey,i=e.activeIndex,a=(0,y.J9)(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=I(e,_);if(!c)return null;var l=B(B(B(B(B({},u),{},{fill:"#eee"},c),a),(0,b.XC)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(M,D({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,c=r.layout,u=r.children,l=(0,y.aS)(u,f.u);if(!l)return null;var p="vertical"===c?o[0].height/2:o[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,m.kr)(t,e)}},d={clipPath:t?"url(#clipPath-".concat(e,")"):null};return n.createElement(s.W,d,l.map(function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:c,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,i=t.className,a=t.xAxis,c=t.yAxis,u=t.left,f=t.top,p=t.width,d=t.height,y=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,o.A)("recharts-bar",i),x=a&&a.allowDataOverflow,w=c&&c.allowDataOverflow,O=x||w,j=l()(m)?this.id:m;return n.createElement(s.W,{className:g},x||w?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(j)},n.createElement("rect",{x:x?u:u-p/2,y:w?f:f-d/2,width:x?p:2*p,height:w?d:2*d}))):null,n.createElement(s.W,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,j),(!y||b)&&h.Z.renderCallByParent(this.props,r))}}])&&R(r.prototype,a),u&&R(r,u),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,a,u}(n.PureComponent);T=X,W(X,"displayName","Bar"),W(X,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!v.m.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),W(X,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,h=t.offset,v=(0,m.xi)(n,r);if(!v)return null;var b=e.layout,x=r.type.defaultProps,w=void 0!==x?B(B({},x),r.props):r.props,O=w.dataKey,j=w.children,A=w.minPointSize,S="horizontal"===b?a:i,P=l?S.scale.domain():null,E=(0,m.DW)({numericAxis:S}),k=(0,y.aS)(j,p.f),M=f.map(function(t,e){var n,f,p,h,y,x;l?n=(0,m._f)(l[s+e],P):(n=(0,m.kr)(t,O),Array.isArray(n)||(n=[E,n]));var w=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"===typeof t)return t;var o=(0,d.Et)(r)||(0,d.uy)(r);return o?t(r,n):(o||(0,g.A)(!1),e)}}(A,T.defaultProps.minPointSize)(n[1],e);if("horizontal"===b){var j,S=[a.scale(n[0]),a.scale(n[1])],M=S[0],_=S[1];f=(0,m.y2)({axis:i,ticks:c,bandSize:o,offset:v.offset,entry:t,index:e}),p=null!==(j=null!==_&&void 0!==_?_:M)&&void 0!==j?j:void 0,h=v.size;var C=M-_;if(y=Number.isNaN(C)?0:C,x={x:f,y:a.y,width:h,height:a.height},Math.abs(w)>0&&Math.abs(y)<Math.abs(w)){var I=(0,d.sA)(y||w)*(Math.abs(w)-Math.abs(y));p-=I,y+=I}}else{var D=[i.scale(n[0]),i.scale(n[1])],N=D[0],R=D[1];if(f=N,p=(0,m.y2)({axis:a,ticks:u,bandSize:o,offset:v.offset,entry:t,index:e}),h=R-N,y=v.size,x={x:i.x,y:p,width:i.width,height:y},Math.abs(w)>0&&Math.abs(h)<Math.abs(w))h+=(0,d.sA)(h||w)*(Math.abs(w)-Math.abs(h))}return B(B(B({},t),{},{x:f,y:p,width:h,height:y,value:l?n:n[1],payload:t,background:x},k&&k[e]&&k[e].props),{},{tooltipPayload:[(0,m.zb)(r,t)],tooltipPosition:{x:f+h/2,y:p+y/2}})});return B({data:M,layout:b},h)})},8673:(t,e,r)=>{var n=r(3204),o=r(3713),i=r(6571);t.exports=function(t){return i(t)?n(t):o(t)}},8724:(t,e,r)=>{var n=r(7615),o=r(5051),i=r(2154),a=r(8734),c=r(2662);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},8734:(t,e,r)=>{var n=r(5575),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},8813:(t,e,r)=>{"use strict";r.d(e,{u:()=>x});var n=r(5043),o=r(3404),i=r(1639),a=r(240),c=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(){return l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},l.apply(this,arguments)}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(!t)return;if("string"===typeof t)return f(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function h(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,g(n.key),n)}}function d(t,e,r){return e=v(e),function(t,e){if(e&&("object"===u(e)||"function"===typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,y()?Reflect.construct(e,r||[],v(t).constructor):e.apply(t,r))}function y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(y=function(){return!!t})()}function v(t){return v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},v(t)}function m(t,e){return m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},m(t,e)}function b(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}var x=function(t){function e(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),d(this,e,arguments)}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&m(t,e)}(e,t),r=e,(u=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,u=t.width,f=t.dataKey,h=t.data,d=t.dataPointFormatter,y=t.xAxis,v=t.yAxis,m=p(t,c),b=(0,a.J9)(m,!1);"x"===this.props.direction&&"number"!==y.type&&(0,o.A)(!1);var g=h.map(function(t){var o=d(t,f),a=o.x,c=o.y,p=o.value,h=o.errorVal;if(!h)return null;var m,g,x=[];if(Array.isArray(h)){var w=s(h,2);m=w[0],g=w[1]}else m=g=h;if("vertical"===r){var O=y.scale,j=c+e,A=j+u,S=j-u,P=O(p-m),E=O(p+g);x.push({x1:E,y1:A,x2:E,y2:S}),x.push({x1:P,y1:j,x2:E,y2:j}),x.push({x1:P,y1:A,x2:P,y2:S})}else if("horizontal"===r){var k=v.scale,M=a+e,T=M-u,_=M+u,C=k(p-m),I=k(p+g);x.push({x1:T,y1:I,x2:_,y2:I}),x.push({x1:M,y1:C,x2:M,y2:I}),x.push({x1:T,y1:C,x2:_,y2:C})}return n.createElement(i.W,l({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},b),x.map(function(t){return n.createElement("line",l({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(i.W,{className:"recharts-errorBars"},g)}}])&&h(r.prototype,u),f&&h(r,f),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,u,f}(n.Component);b(x,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),b(x,"displayName","ErrorBar")},8854:(t,e,r)=>{"use strict";r.d(e,{f:()=>y});var n=r(1629),o=r.n(n),i=r(6307),a=r(7213),c=r(6015),u=r(3831);function l(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function s(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){d(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e,r){return(e=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t,e,r){var n=t.tick,f=t.ticks,p=t.viewBox,d=t.minTickGap,y=t.orientation,v=t.interval,m=t.tickFormatter,b=t.unit,g=t.angle;if(!f||!f.length||!n)return[];if((0,i.Et)(v)||c.m.isSsr)return function(t,e){return l(t,e+1)}(f,"number"===typeof v&&(0,i.Et)(v)?v:0);var x=[],w="top"===y||"bottom"===y?"width":"height",O=b&&"width"===w?(0,a.Pu)(b,{fontSize:e,letterSpacing:r}):{width:0,height:0},j=function(t,n){var i=o()(m)?m(t.value,n):t.value;return"width"===w?function(t,e,r){var n={width:t.width+e.width,height:t.height+e.height};return(0,u.bx)(n,r)}((0,a.Pu)(i,{fontSize:e,letterSpacing:r}),O,g):(0,a.Pu)(i,{fontSize:e,letterSpacing:r})[w]},A=f.length>=2?(0,i.sA)(f[1].coordinate-f[0].coordinate):1,S=function(t,e,r){var n="width"===r,o=t.x,i=t.y,a=t.width,c=t.height;return 1===e?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i}}(p,A,w);return"equidistantPreserveStart"===v?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,f=0,p=1,h=c,d=function(){var e=null===n||void 0===n?void 0:n[f];if(void 0===e)return{v:l(n,p)};var i,a=f,d=function(){return void 0===i&&(i=r(e,a)),i},y=e.coordinate,v=0===f||s(t,y,d,h,u);v||(f=0,h=c,p+=1),v&&(h=y+t*(d()/2+o),f+=p)};p<=a.length;)if(i=d())return i.v;return[]}(A,S,j,f,d):(x="preserveStart"===v||"preserveStartEnd"===v?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var f=n[c-1],p=r(f,c-1),d=t*(f.coordinate+t*p/2-l);a[c-1]=f=h(h({},f),{},{tickCoord:d>0?f.coordinate-d*t:f.coordinate}),s(t,f.tickCoord,function(){return p},u,l)&&(l=f.tickCoord-t*(p/2+o),a[c-1]=h(h({},f),{},{isShow:!0}))}for(var y=i?c-1:c,v=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var f=t*(i.coordinate-t*c()/2-u);a[e]=i=h(h({},i),{},{tickCoord:f<0?i.coordinate-f*t:i.coordinate})}else a[e]=i=h(h({},i),{},{tickCoord:i.coordinate});s(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=h(h({},i),{},{isShow:!0}))},m=0;m<y;m++)v(m);return a}(A,S,j,f,d,"preserveStartEnd"===v):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],f=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var p=t*(l.coordinate+t*f()/2-u);i[e]=l=h(h({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate})}else i[e]=l=h(h({},l),{},{tickCoord:l.coordinate});s(t,l.tickCoord,f,c,u)&&(u=l.tickCoord-t*(f()/2+o),i[e]=h(h({},l),{},{isShow:!0}))},f=a-1;f>=0;f--)l(f);return i}(A,S,j,f,d),x.filter(function(t){return t.isShow}))}},8883:(t,e,r)=>{var n=r(5652),o=r(6571);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},8892:(t,e,r)=>{"use strict";r.d(e,{c:()=>u});var n=r(5043),o=r(8387),i=r(7287),a=r(240);function c(){return c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},c.apply(this,arguments)}var u=function(t){var e=t.cx,r=t.cy,u=t.r,l=t.className,s=(0,o.A)("recharts-dot",l);return e===+e&&r===+r&&u===+u?n.createElement("circle",c({},(0,a.J9)(t,!1),(0,i._U)(t),{className:s,cx:e,cy:r,r:u})):null}},8895:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},8902:(t,e,r)=>{var n=r(4816),o=r(6179),i=r(6704);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},8990:(t,e,r)=>{var n=r(9995)(r(2520));t.exports=n},9057:(t,e,r)=>{var n=r(5324),o=r(2777),i=r(4052),a=r(9194),c=r(6173),u=r(914);t.exports=function(t,e,r){for(var l=-1,s=(e=n(e,t)).length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},9062:(t,e,r)=>{"use strict";t.exports=r(6378)},9096:(t,e,r)=>{var n=r(9256),o=r(5029),i=r(3279),a=r(4052),c=r(3932);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},9115:(t,e,r)=>{var n=r(5967),o=r(6311),i=r(715);t.exports=function(t){return o(t)?i(t):n(t)}},9140:(t,e,r)=>{var n=r(7303);t.exports=function(t){var e=n(t),r=e%1;return e===e?r?e-r:e:0}},9160:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},9194:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},9236:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});Array.prototype.slice;function n(t){return"object"===typeof t&&"length"in t?t:Array.from(t)}},9256:(t,e,r)=>{var n=r(6532),o=r(3781),i=r(1310);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},9364:(t,e,r)=>{var n=r(9742),o=r(9096),i=r(61);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},9394:(t,e,r)=>{var n=r(2622);t.exports=function(t){return n(this,t).has(t)}},9395:(t,e,r)=>{var n=r(4262),o=r(9621),i=r(8673);t.exports=function(t){return n(t,i,o)}},9417:(t,e,r)=>{var n=r(6686);t.exports=function(t){return t===t&&!n(t)}},9621:(t,e,r)=>{var n=r(7529),o=r(7828),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(t){return null==t?[]:(t=Object(t),n(a(t),function(e){return i.call(t,e)}))}:o;t.exports=c},9676:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},9686:t=>{t.exports=function(t){return null==t}},9742:(t,e,r)=>{var n=r(9841);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c===c&&!n(c):r(c,u)))var u=c,l=a}return l}},9812:(t,e,r)=>{var n=r(6552).Symbol;t.exports=n},9841:(t,e,r)=>{var n=r(6913),o=r(2761);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},9853:(t,e,r)=>{var n=r(6989);t.exports=function(t,e){return n(t,e)}},9889:(t,e,r)=>{var n=r(3950),o=r(6686);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},9935:(t,e,r)=>{var n=r(1340),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},9987:(t,e,r)=>{var n=r(9812),o=r(2929),i=r(3211),a=r(3668),c=r(4160),u=r(2074),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!f(new o(t),new o(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)return!1;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},9995:(t,e,r)=>{var n=r(9096),o=r(6571),i=r(8673);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}}}]);
//# sourceMappingURL=938.48c79926.chunk.js.map