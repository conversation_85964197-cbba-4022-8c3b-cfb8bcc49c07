"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[5657],{5657:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var r=a(2555),s=a(5043),i=a(3216),d=a(7921),n=a(4528),l=a(3768),o=a(579);const c=()=>{const{t:e,isRTL:t}=(0,d.o)(),{user:a}=(0,n.A)(),c=(0,i.Zp)(),x=(0,i.zy)(),[m,u]=(0,s.useState)(!1),g=x.pathname.includes("/new"),[b,p]=(0,s.useState)(g?"create":"invoices"),[y,h]=(0,s.useState)([]),[v,f]=(0,s.useState)(""),[k,j]=(0,s.useState)("all"),[N,w]=(0,s.useState)([]),[A,D]=(0,s.useState)(!1),[S,C]=(0,s.useState)({patientId:"",patientName:"",serviceDate:(new Date).toISOString().split("T")[0],dueDate:"",services:[{description:"",quantity:1,unitPrice:"",total:0}],subtotal:0,taxRate:15,taxAmount:0,discountAmount:0,totalAmount:0,notes:"",paymentTerms:"30"}),[T,I]=(0,s.useState)({});(0,s.useEffect)(()=>{P()},[]),(0,s.useEffect)(()=>{F()},[S.services,S.discountAmount,S.taxRate]);const P=async()=>{u(!0);try{const e=new URLSearchParams((0,r.A)((0,r.A)({page:1,limit:50},v&&{search:v}),"all"!==k&&{status:k})),t=await fetch("/api/v1/billing?".concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!t.ok)throw new Error("Failed to load invoices");{const e=await t.json();h(e.data||[])}}catch(e){console.error("Error loading invoices:",e),l.Ay.error("Failed to load invoices");h([{id:"INV-2024-001",patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",patientNameEn:"Ahmed Mohammed Ali",issueDate:"2024-01-15",dueDate:"2024-02-14",amount:2500,status:"paid",services:["Physical Therapy Session","Assessment"],paymentDate:"2024-01-20"},{id:"INV-2024-002",patientName:"\u0641\u0627\u0637\u0645\u0629 \u0623\u062d\u0645\u062f",patientNameEn:"Fatima Ahmed",issueDate:"2024-01-14",dueDate:"2024-02-13",amount:1800,status:"pending",services:["Initial Assessment","Treatment Plan"]},{id:"INV-2024-003",patientName:"\u0645\u062d\u0645\u062f \u0633\u0627\u0644\u0645",patientNameEn:"Mohammed Salem",issueDate:"2024-01-13",dueDate:"2024-02-12",amount:3200,status:"overdue",services:["Treatment Package","Follow-up Sessions"]}])}finally{u(!1)}},F=()=>{const e=S.services.reduce((e,t)=>e+(t.quantity||0)*(t.unitPrice||0),0),t=S.discountAmount||0,a=e-t,s=a*(S.taxRate||0)/100,i=a+s;C(t=>(0,r.A)((0,r.A)({},t),{},{subtotal:e,taxAmount:s,totalAmount:i}))},E=(e,t)=>{C(a=>(0,r.A)((0,r.A)({},a),{},{[e]:t})),T[e]&&I(t=>(0,r.A)((0,r.A)({},t),{},{[e]:null}))},R=(e,t,a)=>{const s=[...S.services];if(s[e]=(0,r.A)((0,r.A)({},s[e]),{},{[t]:a}),"quantity"===t||"unitPrice"===t){const r="quantity"===t?a:s[e].quantity,i="unitPrice"===t?a:s[e].unitPrice;s[e].total=(r||0)*(i||0)}C(e=>(0,r.A)((0,r.A)({},e),{},{services:s}))},q=e=>{switch(e){case"paid":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"overdue":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";case"cancelled":return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400";default:return"text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400"}},_=y.filter(e=>{var t;const a=e.patientName.toLowerCase().includes(v.toLowerCase())||(null===(t=e.patientNameEn)||void 0===t?void 0:t.toLowerCase().includes(v.toLowerCase()))||e.id.toLowerCase().includes(v.toLowerCase()),r="all"===k||e.status===k;return a&&r});return(0,o.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("billingManagement","Billing Management")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("billingManagementDesc","Create invoices, manage billing, and track payments")})]}),(0,o.jsx)("div",{className:"mb-6",children:(0,o.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,o.jsxs)("button",{onClick:()=>p("invoices"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("invoices"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-file-invoice mr-2"}),e("invoices","Invoices")]}),(0,o.jsxs)("button",{onClick:()=>p("create"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("create"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),e("createInvoice","Create Invoice")]}),(0,o.jsxs)("button",{onClick:()=>p("templates"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("templates"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-file-alt mr-2"}),e("templates","Templates")]}),(0,o.jsxs)("button",{onClick:async()=>{try{const e={patient:"507f1f77bcf86cd799439011",services:[{name:"Test Physical Therapy Session",description:"Demo session for testing",quantity:1,unitPrice:500,date:(new Date).toISOString().split("T")[0]}],dueDate:new Date(Date.now()+2592e6).toISOString().split("T")[0],notes:"Test invoice created for demonstration"},t=await fetch("/api/v1/billing",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){const e=await t.json();throw new Error(e.message||"Failed to create test invoice")}await t.json();l.Ay.success("Test invoice created successfully"),P()}catch(e){console.error("Error creating test invoice:",e),l.Ay.error("Failed to create test invoice")}},className:"py-2 px-3 ml-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",title:"Create a test invoice for demo purposes",children:[(0,o.jsx)("i",{className:"fas fa-flask mr-2"}),"Test Invoice"]}),(0,o.jsxs)("button",{onClick:()=>p("settings"),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat("settings"===b?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-cog mr-2"}),e("settings","Settings")]})]})})}),"invoices"===b&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,o.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice text-blue-600 dark:text-blue-400 mr-2"}),e("invoiceList","Invoice List")]}),(0,o.jsxs)("div",{className:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:"text",value:v,onChange:e=>f(e.target.value),placeholder:e("searchInvoices","Search invoices..."),className:"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"}),(0,o.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]}),(0,o.jsxs)("select",{value:k,onChange:e=>j(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,o.jsx)("option",{value:"paid",children:e("paid","Paid")}),(0,o.jsx)("option",{value:"pending",children:e("pending","Pending")}),(0,o.jsx)("option",{value:"overdue",children:e("overdue","Overdue")}),(0,o.jsx)("option",{value:"cancelled",children:e("cancelled","Cancelled")})]})]})]})}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full",children:[(0,o.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("invoiceNumber","Invoice #")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("amount","Amount")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("issueDate","Issue Date")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("dueDate","Due Date")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,o.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,o.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:_.map(a=>{var r;return(0,o.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a.id})}),(0,o.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t?a.patientName:a.patientNameEn}),(0,o.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:null===(r=a.services)||void 0===r?void 0:r.join(", ")})]}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[a.amount.toLocaleString()," ",e("sar","SAR")]})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:a.issueDate}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:a.dueDate}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,o.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(q(a.status)),children:e(a.status,a.status)})}),(0,o.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsx)("button",{onClick:()=>{return e=a.id,void c("/financial/invoice/".concat(e));var e},className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200",title:e("viewInvoice","View Invoice"),children:(0,o.jsx)("i",{className:"fas fa-eye"})}),(0,o.jsx)("button",{onClick:()=>(async e=>{try{const t=await fetch("/api/v1/billing/".concat(e,"/send"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!t.ok){const e=await t.json();throw new Error(e.message||"Failed to send invoice")}{const e=await t.json();l.Ay.success(e.message||"Invoice sent successfully"),P()}}catch(t){console.error("Error sending invoice:",t),l.Ay.error(t.message)}})(a.id),className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200",title:e("sendInvoice","Send Invoice"),children:(0,o.jsx)("i",{className:"fas fa-paper-plane"})}),(0,o.jsx)("button",{onClick:()=>(async e=>{try{if(!(await fetch("/api/v1/billing/".concat(e,"/pdf"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).ok)throw new Error("Failed to generate PDF");l.Ay.success("PDF download will be available soon")}catch(t){console.error("Error downloading invoice:",t),l.Ay.error("Failed to download invoice")}})(a.id),className:"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-200",title:e("downloadPDF","Download PDF"),children:(0,o.jsx)("i",{className:"fas fa-download"})}),("pending"===a.status||"sent"===a.status)&&(0,o.jsx)("button",{onClick:()=>(async e=>{try{const t=await fetch("/api/v1/billing/".concat(e,"/mark-paid"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify({paymentMethod:"cash",notes:"Marked as paid from billing page"})});if(!t.ok){const e=await t.json();throw new Error(e.message||"Failed to mark as paid")}{const e=await t.json();l.Ay.success(e.message||"Invoice marked as paid"),P()}}catch(t){console.error("Error marking as paid:",t),l.Ay.error(t.message)}})(a.id),className:"text-emerald-600 hover:text-emerald-900 dark:text-emerald-400 dark:hover:text-emerald-200",title:e("markAsPaid","Mark as Paid"),children:(0,o.jsx)("i",{className:"fas fa-check-circle"})}),"pending"===a.status&&(0,o.jsx)("button",{onClick:()=>{return e=a.id,console.log("Edit invoice:",e),void(0,l.Ay)("Invoice editing coming soon");var e},className:"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-200",title:e("editInvoice","Edit Invoice"),children:(0,o.jsx)("i",{className:"fas fa-edit"})})]})})]},a.id)})})]})}),0===_.length&&(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice text-4xl text-gray-400 mb-4"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("noInvoicesFound","No invoices found")})]})]}),"create"===b&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-plus text-green-600 dark:text-green-400 mr-2"}),e("createNewInvoice","Create New Invoice")]}),(0,o.jsxs)("form",{onSubmit:async t=>{if(t.preventDefault(),(()=>{const t={};return S.patientName.trim()||(t.patientName=e("patientNameRequired","Patient name is required")),S.dueDate||(t.dueDate=e("dueDateRequired","Due date is required")),S.services.forEach((a,r)=>{a.description.trim()||(t["service_".concat(r,"_description")]=e("serviceDescriptionRequired","Service description is required")),(!a.unitPrice||a.unitPrice<=0)&&(t["service_".concat(r,"_unitPrice")]=e("validPriceRequired","Valid price is required"))}),I(t),0===Object.keys(t).length})()){u(!0);try{const t={patient:S.patientId,services:S.services.map(e=>({name:e.description,description:e.description,quantity:parseInt(e.quantity),unitPrice:parseFloat(e.unitPrice),date:S.serviceDate})),dueDate:S.dueDate,notes:S.notes,discount:{type:"fixed",value:parseFloat(S.discountAmount)||0,amount:parseFloat(S.discountAmount)||0},tax:{rate:parseFloat(S.taxRate)||15,amount:parseFloat(S.taxAmount)||0}},a=await fetch("/api/v1/billing",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify(t)});if(!a.ok){const e=await a.json();throw new Error(e.message||"Failed to create invoice")}{const t=await a.json();C({patientId:"",patientName:"",serviceDate:(new Date).toISOString().split("T")[0],dueDate:"",services:[{description:"",quantity:1,unitPrice:"",total:0}],subtotal:0,taxRate:15,taxAmount:0,discountAmount:0,totalAmount:0,notes:"",paymentTerms:"30"}),l.Ay.success(t.message||e("invoiceCreatedSuccessfully","Invoice created successfully")),p("invoices"),P()}}catch(a){l.Ay.error(e("errorCreatingInvoice","Error creating invoice"))}finally{u(!1)}}else l.Ay.error(e("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-6",children:[(0,o.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4",children:e("patientInformation","Patient Information")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("patientName","Patient Name")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("input",{type:"text",value:S.patientName,onChange:e=>{E("patientName",e.target.value),(async e=>{if(!e||e.length<2)w([]);else try{const t=await fetch("/api/v1/patients/search?q=".concat(encodeURIComponent(e)),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(t.ok){const e=await t.json();w(e.data||[])}}catch(t){console.error("Error searching patients:",t)}})(e.target.value),D(!0)},onFocus:()=>D(!0),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(T.patientName?"border-red-500":"border-gray-300"),placeholder:e("enterPatientName","Enter patient name")}),(0,o.jsx)("i",{className:"fas fa-search absolute right-3 top-3 text-gray-400"})]}),A&&N.length>0&&(0,o.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:N.map(e=>(0,o.jsxs)("div",{onClick:()=>(e=>{C(t=>(0,r.A)((0,r.A)({},t),{},{patientId:e._id,patientName:"".concat(e.firstName," ").concat(e.lastName)})),w([]),D(!1)})(e),className:"px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0",children:[(0,o.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:[e.firstName," ",e.lastName]}),(0,o.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e.nationalId," \u2022 ",e.phone]})]},e._id))}),T.patientName&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:T.patientName})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("serviceDate","Service Date")}),(0,o.jsx)("input",{type:"date",value:S.serviceDate,onChange:e=>E("serviceDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("dueDate","Due Date")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"date",value:S.dueDate,onChange:e=>E("dueDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(T.dueDate?"border-red-500":"border-gray-300")}),T.dueDate&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:T.dueDate})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("paymentTerms","Payment Terms (Days)")}),(0,o.jsxs)("select",{value:S.paymentTerms,onChange:e=>E("paymentTerms",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsxs)("option",{value:"15",children:["15 ",e("days","Days")]}),(0,o.jsxs)("option",{value:"30",children:["30 ",e("days","Days")]}),(0,o.jsxs)("option",{value:"45",children:["45 ",e("days","Days")]}),(0,o.jsxs)("option",{value:"60",children:["60 ",e("days","Days")]})]})]})]})]}),(0,o.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-green-900 dark:text-green-100",children:e("services","Services")}),(0,o.jsxs)("button",{type:"button",onClick:()=>{C(e=>(0,r.A)((0,r.A)({},e),{},{services:[...e.services,{description:"",quantity:1,unitPrice:"",total:0}]}))},className:"px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-1"}),e("addService","Add Service")]})]}),(0,o.jsx)("div",{className:"space-y-4",children:S.services.map((t,a)=>(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4 items-end",children:[(0,o.jsxs)("div",{className:"md:col-span-2",children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("serviceDescription","Service Description")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"text",value:t.description,onChange:e=>R(a,"description",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(T["service_".concat(a,"_description")]?"border-red-500":"border-gray-300"),placeholder:e("enterServiceDescription","Enter service description")}),T["service_".concat(a,"_description")]&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:T["service_".concat(a,"_description")]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("quantity","Quantity")}),(0,o.jsx)("input",{type:"number",min:"1",value:t.quantity,onChange:e=>R(a,"quantity",parseInt(e.target.value)||1),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("unitPrice","Unit Price")," ",(0,o.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,o.jsx)("input",{type:"number",step:"0.01",min:"0",value:t.unitPrice,onChange:e=>R(a,"unitPrice",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(T["service_".concat(a,"_unitPrice")]?"border-red-500":"border-gray-300"),placeholder:"0.00"}),T["service_".concat(a,"_unitPrice")]&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:T["service_".concat(a,"_unitPrice")]})]}),(0,o.jsxs)("div",{className:"flex items-end space-x-2",children:[(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("total","Total")}),(0,o.jsxs)("div",{className:"px-3 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg text-gray-900 dark:text-white",children:[t.total.toFixed(2)," ",e("sar","SAR")]})]}),S.services.length>1&&(0,o.jsx)("button",{type:"button",onClick:()=>(e=>{if(S.services.length>1){const t=S.services.filter((t,a)=>a!==e);C(e=>(0,r.A)((0,r.A)({},e),{},{services:t}))}})(a),className:"p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200",children:(0,o.jsx)("i",{className:"fas fa-trash"})})]})]})},a))})]}),(0,o.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-purple-900 dark:text-purple-100 mb-4",children:e("invoiceTotals","Invoice Totals")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("discountAmount","Discount Amount")}),(0,o.jsx)("input",{type:"number",step:"0.01",min:"0",value:S.discountAmount,onChange:e=>E("discountAmount",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0.00"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("taxRate","Tax Rate (%)")}),(0,o.jsx)("input",{type:"number",step:"0.01",min:"0",max:"100",value:S.taxRate,onChange:e=>E("taxRate",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("subtotal","Subtotal"),":"]}),(0,o.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:[S.subtotal.toFixed(2)," ",e("sar","SAR")]})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("discount","Discount"),":"]}),(0,o.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:["-",S.discountAmount.toFixed(2)," ",e("sar","SAR")]})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("tax","Tax")," (",S.taxRate,"%):"]}),(0,o.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:[S.taxAmount.toFixed(2)," ",e("sar","SAR")]})]}),(0,o.jsx)("div",{className:"border-t border-gray-200 dark:border-gray-600 pt-2",children:(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsxs)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[e("total","Total"),":"]}),(0,o.jsxs)("span",{className:"text-lg font-bold text-blue-600 dark:text-blue-400",children:[S.totalAmount.toFixed(2)," ",e("sar","SAR")]})]})})]})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("notes","Notes")}),(0,o.jsx)("textarea",{value:S.notes,onChange:e=>E("notes",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterAdditionalNotes","Enter additional notes or terms")})]}),(0,o.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,o.jsx)("button",{type:"button",onClick:()=>c("/financial/billing"),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:e("cancel","Cancel")}),(0,o.jsx)("button",{type:"submit",disabled:m,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:m?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),e("creating","Creating...")]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-file-invoice mr-2"}),e("createInvoice","Create Invoice")]})})]})]})]}),"templates"===b&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-file-alt text-purple-600 dark:text-purple-400 mr-2"}),e("invoiceTemplates","Invoice Templates")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,o.jsx)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice text-4xl text-blue-600 dark:text-blue-400 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:e("standardTemplate","Standard Template")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:e("standardTemplateDesc","Basic invoice template for general services")}),(0,o.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e("useTemplate","Use Template")})]})}),(0,o.jsx)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("i",{className:"fas fa-file-medical text-4xl text-green-600 dark:text-green-400 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:e("medicalTemplate","Medical Template")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:e("medicalTemplateDesc","Specialized template for medical services")}),(0,o.jsx)("button",{className:"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:e("useTemplate","Use Template")})]})}),(0,o.jsx)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("i",{className:"fas fa-plus text-4xl text-gray-400 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:e("createTemplate","Create Template")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:e("createTemplateDesc","Create a custom invoice template")}),(0,o.jsx)("button",{className:"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:e("createNew","Create New")})]})})]})]}),"settings"===b&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-cog text-gray-600 dark:text-gray-400 mr-2"}),e("billingSettings","Billing Settings")]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4",children:e("companyInformation","Company Information")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("companyName","Company Name")}),(0,o.jsx)("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterCompanyName","Enter company name")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("taxNumber","Tax Number")}),(0,o.jsx)("input",{type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterTaxNumber","Enter tax number")})]})]})]}),(0,o.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-md font-semibold text-green-900 dark:text-green-100 mb-4",children:e("defaultSettings","Default Settings")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("defaultTaxRate","Default Tax Rate (%)")}),(0,o.jsx)("input",{type:"number",step:"0.01",defaultValue:"15",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("defaultPaymentTerms","Default Payment Terms (Days)")}),(0,o.jsxs)("select",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,o.jsxs)("option",{value:"15",children:["15 ",e("days","Days")]}),(0,o.jsxs)("option",{value:"30",selected:!0,children:["30 ",e("days","Days")]}),(0,o.jsxs)("option",{value:"45",children:["45 ",e("days","Days")]}),(0,o.jsxs)("option",{value:"60",children:["60 ",e("days","Days")]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("invoicePrefix","Invoice Prefix")}),(0,o.jsx)("input",{type:"text",defaultValue:"INV-",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]})]}),(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsxs)("button",{className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-save mr-2"}),e("saveSettings","Save Settings")]})})]})]})]})}}}]);
//# sourceMappingURL=5657.47a93af9.chunk.js.map