"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8530],{8530:(e,a,r)=>{r.r(a),r.d(a,{default:()=>d});var t=r(2555),s=r(5043),l=r(7921),i=r(579);const d=()=>{const{t:e,isRTL:a}=(0,l.o)(),r=(0,s.useRef)(null),[d,o]=(0,s.useState)({title:"",description:"",category:"assessment",fields:[],settings:{allowSave:!0,requireSignature:!1,autoSave:!0,multiPage:!1,theme:"professional",colorScheme:"blue"}}),[n,c]=(0,s.useState)("design"),[x,p]=(0,s.useState)(null),[g,u]=(0,s.useState)(!1),[m,b]=(0,s.useState)(null),[f,h]=(0,s.useState)(null),y=[{type:"text",label:e("textInput","Text Input"),icon:"fas fa-font",category:"basic",color:"blue",defaultProps:{placeholder:"Enter text...",required:!1,maxLength:255,backgroundColor:"#f8fafc",borderColor:"#3b82f6",textColor:"#1e40af"}},{type:"textarea",label:e("textArea","Text Area"),icon:"fas fa-align-left",category:"basic",color:"green",defaultProps:{placeholder:"Enter detailed text...",required:!1,rows:4,backgroundColor:"#f0fdf4",borderColor:"#10b981",textColor:"#065f46"}},{type:"number",label:e("number","Number"),icon:"fas fa-hashtag",category:"basic",color:"purple",defaultProps:{placeholder:"0",required:!1,min:0,max:999999,backgroundColor:"#faf5ff",borderColor:"#8b5cf6",textColor:"#6b21a8"}},{type:"email",label:e("email","Email"),icon:"fas fa-envelope",category:"basic",color:"indigo",defaultProps:{placeholder:"<EMAIL>",required:!1,backgroundColor:"#f0f9ff",borderColor:"#6366f1",textColor:"#3730a3"}},{type:"phone",label:e("phone","Phone"),icon:"fas fa-phone",category:"basic",color:"teal",defaultProps:{placeholder:"+****************",required:!1,backgroundColor:"#f0fdfa",borderColor:"#14b8a6",textColor:"#0f766e"}},{type:"date",label:e("date","Date"),icon:"fas fa-calendar",category:"basic",color:"orange",defaultProps:{required:!1,backgroundColor:"#fff7ed",borderColor:"#f97316",textColor:"#c2410c"}},{type:"pain-scale",label:e("painScale","Pain Scale (0-10)"),icon:"fas fa-thermometer-half",category:"medical",defaultProps:{min:0,max:10,step:1,showLabels:!0}},{type:"body-map",label:e("bodyMap","Body Map"),icon:"fas fa-male",category:"medical",defaultProps:{allowMultiple:!0,painTypes:["sharp","dull","burning"]}},{type:"range-of-motion",label:e("rangeOfMotion","Range of Motion"),icon:"fas fa-expand-arrows-alt",category:"medical",defaultProps:{joint:"shoulder",movements:["flexion","extension"]}},{type:"muscle-strength",label:e("muscleStrength","Muscle Strength (0-5)"),icon:"fas fa-dumbbell",category:"medical",defaultProps:{scale:"oxford",showDescriptions:!0}},{type:"vital-signs",label:e("vitalSigns","Vital Signs"),icon:"fas fa-heartbeat",category:"medical",defaultProps:{fields:["bp","hr","temp","resp"]}},{type:"signature",label:e("electronicSignature","Electronic Signature"),icon:"fas fa-signature",category:"medical",defaultProps:{required:!0,signatureType:"therapist"}},{type:"consent-checkbox",label:e("consentCheckbox","Consent Checkbox"),icon:"fas fa-check-square",category:"medical",defaultProps:{consentText:"I consent to treatment",required:!0}},{type:"diagnosis-codes",label:e("diagnosisCodes","Diagnosis Codes (ICD-10)"),icon:"fas fa-code",category:"medical",defaultProps:{allowMultiple:!0,searchable:!0}},{type:"medication-list",label:e("medicationList","Medication List"),icon:"fas fa-pills",category:"medical",defaultProps:{allowAdd:!0,fields:["name","dosage","frequency"]}},{type:"email",label:e("email","Email"),icon:"fas fa-envelope",category:"basic",defaultProps:{placeholder:"<EMAIL>",required:!1}},{type:"phone",label:e("phone","Phone"),icon:"fas fa-phone",category:"basic",defaultProps:{placeholder:"+966 50 123 4567",required:!1}},{type:"date",label:e("date","Date"),icon:"fas fa-calendar",category:"basic",defaultProps:{required:!1}},{type:"select",label:e("dropdown","Dropdown"),icon:"fas fa-chevron-down",category:"choice",defaultProps:{options:["Option 1","Option 2","Option 3"],required:!1}},{type:"radio",label:e("radioButtons","Radio Buttons"),icon:"fas fa-dot-circle",category:"choice",defaultProps:{options:["Option 1","Option 2","Option 3"],required:!1}},{type:"checkbox",label:e("checkboxes","Checkboxes"),icon:"fas fa-check-square",category:"choice",defaultProps:{options:["Option 1","Option 2","Option 3"],required:!1}},{type:"rating",label:e("rating","Rating"),icon:"fas fa-star",category:"advanced",defaultProps:{max:5,required:!1}},{type:"pain-scale",label:e("painScale","Pain Scale"),icon:"fas fa-thermometer-half",category:"medical",defaultProps:{min:0,max:10,required:!1}},{type:"signature",label:e("signature","Signature"),icon:"fas fa-signature",category:"advanced",defaultProps:{required:!1}},{type:"section",label:e("section","Section Header"),icon:"fas fa-heading",category:"layout",defaultProps:{title:"Section Title",description:""}}],k=e=>{const a=(0,t.A)({id:"field_".concat(Date.now()),type:e.type,label:e.label},e.defaultProps);o(e=>(0,t.A)((0,t.A)({},e),{},{fields:[...e.fields,a]}))},j=(0,s.useCallback)((e,a)=>{o(r=>(0,t.A)((0,t.A)({},r),{},{fields:r.fields.map(r=>r.id===e?(0,t.A)((0,t.A)({},r),a):r)})),x&&x.id===e&&p(e=>(0,t.A)((0,t.A)({},e),a))},[x]),v=((0,s.useCallback)((e,a)=>{j(e,{label:a})},[j]),(0,s.useCallback)((e,a,r)=>{j(e,{[a]:r})},[j]),(0,s.useCallback)((e,a)=>{h(a),e.dataTransfer.effectAllowed="copy"},[]),(0,s.useCallback)(e=>{e.preventDefault(),f&&(k(f.type),h(null))},[f]),(0,s.useCallback)(e=>{e.preventDefault(),e.dataTransfer.dropEffect="copy"},[]),(0,s.useCallback)((e,a)=>{const r=[...d.fields],[s]=r.splice(e,1);r.splice(a,0,s),o(e=>(0,t.A)((0,t.A)({},e),{},{fields:r}))},[d.fields]),a=>{var r,s,l;const d={className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",disabled:!0};switch(a.type){case"text":case"email":case"phone":return(0,i.jsx)("input",(0,t.A)({type:a.type,placeholder:a.placeholder},d));case"textarea":return(0,i.jsx)("textarea",(0,t.A)({rows:a.rows,placeholder:a.placeholder},d));case"number":return(0,i.jsx)("input",(0,t.A)({type:"number",min:a.min,max:a.max,placeholder:a.placeholder},d));case"date":return(0,i.jsx)("input",(0,t.A)({type:"date"},d));case"select":return(0,i.jsxs)("select",(0,t.A)((0,t.A)({},d),{},{children:[(0,i.jsx)("option",{children:e("selectOption","Select an option...")}),null===(r=a.options)||void 0===r?void 0:r.map((e,a)=>(0,i.jsx)("option",{value:e,children:e},a))]}));case"radio":return(0,i.jsx)("div",{className:"space-y-2",children:null===(s=a.options)||void 0===s?void 0:s.map((e,r)=>(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"radio",name:a.id,disabled:!0,className:"text-blue-600"}),(0,i.jsx)("span",{className:"text-gray-900 dark:text-white",children:e})]},r))});case"checkbox":return(0,i.jsx)("div",{className:"space-y-2",children:null===(l=a.options)||void 0===l?void 0:l.map((e,a)=>(0,i.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",disabled:!0,className:"text-blue-600"}),(0,i.jsx)("span",{className:"text-gray-900 dark:text-white",children:e})]},a))});case"rating":return(0,i.jsx)("div",{className:"flex space-x-1",children:[...Array(a.max)].map((e,a)=>(0,i.jsx)("i",{className:"fas fa-star text-gray-300 text-xl"},a))});case"pain-scale":return(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("div",{className:"flex justify-between items-center",children:[...Array(11)].map((e,a)=>(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)("div",{className:"w-8 h-8 rounded-full border-2 border-gray-300 flex items-center justify-center text-sm ".concat(a<=3?"bg-green-100":a<=6?"bg-yellow-100":"bg-red-100"),children:a})},a))}),(0,i.jsxs)("div",{className:"flex justify-between text-xs text-gray-500",children:[(0,i.jsx)("span",{children:e("noPain","No Pain")}),(0,i.jsx)("span",{children:e("worstPain","Worst Pain")})]})]});case"signature":return(0,i.jsx)("div",{className:"border border-gray-300 dark:border-gray-600 rounded-lg p-4 h-32 bg-gray-50 dark:bg-gray-700 flex items-center justify-center",children:(0,i.jsx)("span",{className:"text-gray-500 dark:text-gray-400",children:e("signatureArea","Signature Area")})});case"section":return(0,i.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:a.title}),a.description&&(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:a.description})]});default:return(0,i.jsxs)("div",{className:"text-gray-500",children:["Unknown field type: ",a.type]})}});return(0,i.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 ".concat(a?"font-arabic":"font-english"),children:[(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 px-6 py-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("div",{className:"flex items-center space-x-4",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-xl font-bold text-gray-900 dark:text-white",children:e("formBuilder","Form Builder")}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("formBuilderDesc","Create custom forms with drag-and-drop interface")})]})}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("button",{onClick:()=>{var e;return null===(e=r.current)||void 0===e?void 0:e.click()},disabled:g,className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 flex items-center",children:[(0,i.jsx)("i",{className:"fas ".concat(g?"fa-spinner fa-spin":"fa-magic"," mr-2")}),g?e("processing","Processing..."):e("aiGenerate","AI Generate")]}),(0,i.jsx)("button",{className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:e("preview","Preview")}),(0,i.jsx)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:e("save","Save")})]})]}),(0,i.jsx)("input",{ref:r,type:"file",accept:".pdf,.doc,.docx,.txt",onChange:e=>{const a=e.target.files[0];a&&(async()=>{u(!0);try{await new Promise(e=>setTimeout(e,3e3));const e={detectedFields:[{type:"text",label:"Patient Name",required:!0},{type:"date",label:"Date of Birth",required:!0},{type:"email",label:"Email Address",required:!1},{type:"phone",label:"Phone Number",required:!0},{type:"textarea",label:"Medical History",required:!1},{type:"select",label:"Insurance Provider",options:["Bupa","Tawuniya","Malath","Other"],required:!0},{type:"checkbox",label:"Symptoms",options:["Pain","Stiffness","Weakness","Numbness"],required:!1},{type:"pain-scale",label:"Pain Level",required:!1},{type:"signature",label:"Patient Signature",required:!0}],confidence:.92,documentType:"Patient Intake Form",language:"English",pages:1};b(e),o(a=>(0,t.A)((0,t.A)({},a),{},{title:e.documentType,description:"Auto-generated from uploaded document with ".concat(Math.round(100*e.confidence),"% confidence"),fields:e.detectedFields.map((e,a)=>{var r;return(0,t.A)((0,t.A)({id:"field_".concat(a)},e),null===(r=y.find(a=>a.type===e.type))||void 0===r?void 0:r.defaultProps)})}))}catch(e){console.error("Document processing failed:",e)}finally{u(!1)}})()},className:"hidden"})]}),g&&(0,i.jsx)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-800 px-6 py-4",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("i",{className:"fas fa-magic text-purple-600 dark:text-purple-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-purple-900 dark:text-purple-100",children:e("aiProcessing","AI Processing Document...")}),(0,i.jsx)("p",{className:"text-xs text-purple-700 dark:text-purple-300",children:e("aiProcessingDesc","Analyzing document structure and extracting form fields")})]})]})}),m&&(0,i.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border-b border-green-200 dark:border-green-800 px-6 py-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-green-900 dark:text-green-100",children:e("aiAnalysisComplete","AI Analysis Complete")}),(0,i.jsx)("p",{className:"text-xs text-green-700 dark:text-green-300",children:e("detectedFields","Detected ".concat(m.detectedFields.length," fields with ").concat(Math.round(100*m.confidence),"% confidence"))})]})]}),(0,i.jsx)("button",{onClick:()=>b(null),className:"text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200",children:(0,i.jsx)("i",{className:"fas fa-times"})})]})}),(0,i.jsxs)("div",{className:"flex h-screen",children:[(0,i.jsx)("div",{className:"w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-600 overflow-y-auto",children:(0,i.jsxs)("div",{className:"p-4",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("fieldTypes","Field Types")}),["basic","choice","advanced","medical","layout"].map(a=>(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 uppercase tracking-wide",children:e(a,a)}),(0,i.jsx)("div",{className:"grid grid-cols-2 gap-2",children:y.filter(e=>e.category===a).map(e=>(0,i.jsx)("button",{onClick:()=>k(e),className:"p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left",children:(0,i.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,i.jsx)("i",{className:"".concat(e.icon," text-gray-600 dark:text-gray-400 mb-2")}),(0,i.jsx)("span",{className:"text-xs text-gray-900 dark:text-white",children:e.label})]})},e.type))})]},a))]})}),(0,i.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-600 p-4",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("formTitle","Form Title")}),(0,i.jsx)("input",{type:"text",value:d.title,onChange:e=>o(a=>(0,t.A)((0,t.A)({},a),{},{title:e.target.value})),placeholder:e("enterFormTitle","Enter form title..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("category","Category")}),(0,i.jsxs)("select",{value:d.category,onChange:e=>o(a=>(0,t.A)((0,t.A)({},a),{},{category:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"assessment",children:e("assessment","Assessment")}),(0,i.jsx)("option",{value:"intake",children:e("intake","Intake")}),(0,i.jsx)("option",{value:"progress",children:e("progress","Progress")}),(0,i.jsx)("option",{value:"discharge",children:e("discharge","Discharge")}),(0,i.jsx)("option",{value:"consent",children:e("consent","Consent")})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("description","Description")}),(0,i.jsx)("input",{type:"text",value:d.description,onChange:e=>o(a=>(0,t.A)((0,t.A)({},a),{},{description:e.target.value})),placeholder:e("enterDescription","Enter description..."),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})}),(0,i.jsx)("div",{className:"flex-1 p-6 overflow-y-auto",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:d.title||e("untitledForm","Untitled Form")}),d.description&&(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:d.description})]}),(0,i.jsx)("div",{className:"space-y-4",children:0===d.fields.length?(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-12 text-center",children:[(0,i.jsx)("i",{className:"fas fa-plus-circle text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noFieldsYet","No fields yet")}),(0,i.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:e("addFieldsDesc","Add fields from the sidebar or upload a document to get started")}),(0,i.jsxs)("button",{onClick:()=>{var e;return null===(e=r.current)||void 0===e?void 0:e.click()},className:"px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-magic mr-2"}),e("uploadDocument","Upload Document")]})]}):d.fields.map((e,a)=>{var r;return(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border-2 transition-colors p-6 ".concat((null===x||void 0===x?void 0:x.id)===e.id?"border-blue-500":"border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500"),onClick:()=>p(e),children:(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,i.jsx)("i",{className:(null===(r=y.find(a=>a.type===e.type))||void 0===r?void 0:r.icon)||"fas fa-question"}),(0,i.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e.label}),e.required&&(0,i.jsx)("span",{className:"text-red-500 text-sm",children:"*"})]}),(0,i.jsx)("div",{className:"mt-3",children:v(e)})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,i.jsx)("button",{onClick:a=>{a.stopPropagation(),p(e)},className:"p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded",children:(0,i.jsx)("i",{className:"fas fa-edit"})}),(0,i.jsx)("button",{onClick:a=>{var r;a.stopPropagation(),r=e.id,o(e=>(0,t.A)((0,t.A)({},e),{},{fields:e.fields.filter(e=>e.id!==r)})),p(null)},className:"p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded",children:(0,i.jsx)("i",{className:"fas fa-trash"})})]})]})},e.id)})})]})})]}),x&&(0,i.jsx)("div",{className:"w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-600 overflow-y-auto",children:(0,i.jsxs)("div",{className:"p-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("fieldProperties","Field Properties")}),(0,i.jsx)("button",{onClick:()=>p(null),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,i.jsx)("i",{className:"fas fa-times"})})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("fieldLabel","Field Label")}),(0,i.jsx)("input",{type:"text",value:x.label,onChange:e=>j(x.id,{label:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e("required","Required")}),(0,i.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,i.jsx)("input",{type:"checkbox",checked:x.required||!1,onChange:e=>j(x.id,{required:e.target.checked}),className:"sr-only peer"}),(0,i.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"})]})]}),("text"===x.type||"textarea"===x.type)&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("placeholder","Placeholder")}),(0,i.jsx)("input",{type:"text",value:x.placeholder||"",onChange:e=>j(x.id,{placeholder:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),("select"===x.type||"radio"===x.type||"checkbox"===x.type)&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("options","Options")}),(0,i.jsxs)("div",{className:"space-y-2",children:[(x.options||[]).map((e,a)=>(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"text",value:e,onChange:e=>{const r=[...x.options||[]];r[a]=e.target.value,j(x.id,{options:r})},className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,i.jsx)("button",{onClick:()=>{const e=[...x.options||[]];e.splice(a,1),j(x.id,{options:e})},className:"p-2 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200",children:(0,i.jsx)("i",{className:"fas fa-trash text-sm"})})]},a)),(0,i.jsxs)("button",{onClick:()=>{const e=[...x.options||[],"New Option"];j(x.id,{options:e})},className:"w-full px-3 py-2 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),e("addOption","Add Option")]})]})]})]})]})})]})]})}}}]);
//# sourceMappingURL=8530.4ec8e10c.chunk.js.map