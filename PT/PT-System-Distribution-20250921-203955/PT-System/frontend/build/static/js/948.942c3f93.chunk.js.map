{"version": 3, "file": "static/js/948.942c3f93.chunk.js", "mappings": "2MAKA,MAoNA,EApN2BA,KACzB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,OACPC,EAAgBC,IAAqBC,EAAAA,EAAAA,WAAS,IAC9CC,EAAgBC,IAAqBF,EAAAA,EAAAA,UAAS,MAa/CG,EAAa,CACjBC,iBAAkB,eAClBC,mBAAoB,CAAC,eAAgB,mBACrCC,eAAgB,0HAChBC,mBAAoB,iEACpBC,UAAW,oBACXC,YAAa,kCACbC,uBAAwB,CACtBC,UAAW,UACXC,iBAAkB,aAClBC,gBAAiB,OACjBC,aAAc,OACdC,cAAe,SACfC,kBAAmB,UACnBC,uBAAwB,uDACxBC,eAAgB,mCAChBC,MAAO,qEAETC,wBAAyB,CACvBC,kBAAmB,uBACnBC,WAAY,gBACZC,kBAAmB,2EACnBC,qBAAsB,UACtBC,mBAAoB,YACpBN,MAAO,uFAIX,OACEO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,wDAAuDC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8CACZ/B,EAAE,qBAAsB,4BAE3BiC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5ChC,EAAE,sBAAuB,4EAI9B8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEhC,EAAE,wBAAyB,8BAG9B8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,UACEC,QAASA,KACP5B,EAAkB,MAClBH,GAAkB,IAEpB4B,UAAU,2LAA0LC,UAEpMF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uEACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtDhC,EAAE,gBAAiB,qBAEtBiC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDhC,EAAE,uBAAwB,iCAKjCiC,EAAAA,EAAAA,KAAA,UACEC,QAASA,KACP5B,EAAkBC,GAClBJ,GAAkB,IAEpB4B,UAAU,iMAAgMC,UAE1MF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0EACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtDhC,EAAE,mBAAoB,wBAEzBiC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDhC,EAAE,sBAAuB,0CAOlC8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEhC,EAAE,qBAAsB,0BAE3BiC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE,CACC,CAAEG,GAAI,cAAeC,MAAO,0BAA2BC,KAAM,cAAeC,MAAO,QACnF,CAAEH,GAAI,UAAWC,MAAO,kBAAmBC,KAAM,uBAAwBC,MAAO,OAChF,CAAEH,GAAI,aAAcC,MAAO,8BAA+BC,KAAM,eAAgBC,MAAO,QACvF,CAAEH,GAAI,eAAgBC,MAAO,0BAA2BC,KAAM,eAAgBC,MAAO,UACrF,CAAEH,GAAI,gBAAiBC,MAAO,2BAA4BC,KAAM,cAAeC,MAAO,SACtF,CAAEH,GAAI,OAAQC,MAAO,kBAAmBC,KAAM,8BAA+BC,MAAO,UACpF,CAAEH,GAAI,QAASC,MAAO,wBAAyBC,KAAM,kBAAmBC,MAAO,WAC/EC,IAAKC,IACLP,EAAAA,EAAAA,KAAA,OAEEF,UAAS,UAAAU,OAAYD,EAAQF,MAAK,gBAAAG,OAAeD,EAAQF,MAAK,qCAAAG,OAAoCD,EAAQF,MAAK,qBAAAG,OAAoBD,EAAQF,MAAK,QAAON,UAEvJF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAU,OAAKD,EAAQH,KAAI,UAAAI,OAASD,EAAQF,MAAK,mBAAAG,OAAkBD,EAAQF,MAAK,gBAClFL,EAAAA,EAAAA,KAAA,QAAMF,UAAU,oDAAmDC,SAChEhC,EAAEwC,EAAQL,GAAIK,EAAQJ,aANtBI,EAAQL,WAerBL,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEhC,EAAE,WAAY,eAEjB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,wBAAyB,yCAE9B8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,aAAc,sCAEnB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,iBAAkB,sCAEvB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,yBAA0B,kCAGjC8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,0BAA2B,gCAEhC8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,cAAe,oCAEpB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,oBAAqB,0BAE1B8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6DAA4DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ/B,EAAE,iBAAkB,yCAO5BK,IACCyB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEhC,EAAE,qBAAsB,2BAE3B8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAAShC,EAAE,mBAAoB,qBAAqB,OAAU,IAAEK,EAAeG,qBAClFsB,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAAShC,EAAE,iBAAkB,mBAAmB,OAAU,IAAEK,EAAeqC,mBAC9EZ,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAAShC,EAAE,WAAY,YAAY,OAAU,IAAEK,EAAesC,gBAEnEV,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMU,QAAQC,IAAI,wBAAyBxC,GACpD0B,UAAU,0EAAyEC,SAElFhC,EAAE,cAAe,oCAOzBE,IACC+B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iFAAgFC,UAC7FC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,UAC3GC,EAAAA,EAAAA,KAACa,EAAAA,QAAkB,CACjBC,UAAU,mBACVC,YAAa3C,GAAkB,CAAC,EAChC4C,OArMQC,IAClBN,QAAQC,IAAI,wBAAyBK,GACrCC,EAAAA,GAAMC,QAAQ,uCACd9C,EAAkB4C,GAClB/C,GAAkB,IAkMRkD,SA/LSC,KACnBnD,GAAkB,a", "sources": ["pages/Test/TestCARFAssessment.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport CARFAssessmentForm from '../../components/CARF/CARFAssessmentForm';\nimport toast from 'react-hot-toast';\n\nconst TestCARFAssessment = () => {\n  const { t } = useTranslation();\n  const [showAssessment, setShowAssessment] = useState(false);\n  const [assessmentData, setAssessmentData] = useState(null);\n\n  const handleSave = (data) => {\n    console.log('CARF Assessment Data:', data);\n    toast.success('CARF Assessment saved successfully!');\n    setAssessmentData(data);\n    setShowAssessment(false);\n  };\n\n  const handleCancel = () => {\n    setShowAssessment(false);\n  };\n\n  const sampleData = {\n    primaryDiagnosis: 'Stroke (CVA)',\n    secondaryDiagnoses: ['Hypertension', 'Diabetes Type 2'],\n    medicalHistory: 'Patient suffered ischemic stroke 3 months ago affecting left hemisphere. Previous history of hypertension and diabetes.',\n    currentMedications: 'Lisinopril 10mg daily, Metformin 500mg BID, Aspirin 81mg daily',\n    allergies: 'Penicillin - rash',\n    precautions: 'Fall risk, Right-sided weakness',\n    psychosocialAssessment: {\n      moodState: 'anxious',\n      copingStrategies: 'developing',\n      motivationLevel: 'high',\n      insightLevel: 'good',\n      familySupport: 'strong',\n      socialConnections: 'limited',\n      culturalConsiderations: 'Arabic-speaking family, Islamic faith considerations',\n      spiritualNeeds: 'Prayer time accommodation needed',\n      notes: 'Patient shows good motivation but anxiety about recovery timeline'\n    },\n    environmentalAssessment: {\n      homeAccessibility: 'partially-accessible',\n      homeSafety: 'minor-hazards',\n      homeModifications: 'Ramp needed for front entrance, grab bars in bathroom, remove throw rugs',\n      transportationAccess: 'limited',\n      communityResources: 'available',\n      notes: 'Family willing to make home modifications, local mosque provides community support'\n    }\n  };\n\n  return (\n    <div className=\"p-6 max-w-4xl mx-auto\">\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n          <i className=\"fas fa-clipboard-check mr-3 text-blue-600\"></i>\n          {t('testCarfAssessment', 'Test CARF Assessment')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          {t('testCarfDescription', 'Test the CARF Assessment form with all sections and functionality.')}\n        </p>\n      </div>\n\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('carfAssessmentTesting', 'CARF Assessment Testing')}\n        </h2>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n          <button\n            onClick={() => {\n              setAssessmentData(null);\n              setShowAssessment(true);\n            }}\n            className=\"flex items-center justify-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors border border-blue-200 dark:border-blue-800\"\n          >\n            <div className=\"text-center\">\n              <i className=\"fas fa-plus-circle text-blue-600 dark:text-blue-400 text-2xl mb-2\"></i>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                {t('newAssessment', 'New Assessment')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('startBlankAssessment', 'Start with blank form')}\n              </p>\n            </div>\n          </button>\n\n          <button\n            onClick={() => {\n              setAssessmentData(sampleData);\n              setShowAssessment(true);\n            }}\n            className=\"flex items-center justify-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors border border-green-200 dark:border-green-800\"\n          >\n            <div className=\"text-center\">\n              <i className=\"fas fa-file-medical text-green-600 dark:text-green-400 text-2xl mb-2\"></i>\n              <h3 className=\"font-medium text-gray-900 dark:text-white\">\n                {t('sampleAssessment', 'Sample Assessment')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {t('prefilledSampleData', 'Pre-filled with sample data')}\n              </p>\n            </div>\n          </button>\n        </div>\n\n        {/* Assessment Sections Overview */}\n        <div className=\"mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('assessmentSections', 'Assessment Sections')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {[\n              { id: 'demographic', title: 'Demographic Information', icon: 'fas fa-user', color: 'blue' },\n              { id: 'medical', title: 'Medical History', icon: 'fas fa-notes-medical', color: 'red' },\n              { id: 'functional', title: 'Functional Assessment (FIM)', icon: 'fas fa-tasks', color: 'blue' },\n              { id: 'psychosocial', title: 'Psychosocial Assessment', icon: 'fas fa-brain', color: 'purple' },\n              { id: 'environmental', title: 'Environmental Assessment', icon: 'fas fa-home', color: 'green' },\n              { id: 'risk', title: 'Risk Assessment', icon: 'fas fa-exclamation-triangle', color: 'yellow' },\n              { id: 'goals', title: 'Goals and Preferences', icon: 'fas fa-bullseye', color: 'indigo' }\n            ].map((section) => (\n              <div\n                key={section.id}\n                className={`p-3 bg-${section.color}-50 dark:bg-${section.color}-900/20 rounded-lg border border-${section.color}-200 dark:border-${section.color}-800`}\n              >\n                <div className=\"flex items-center\">\n                  <i className={`${section.icon} text-${section.color}-600 dark:text-${section.color}-400 mr-2`}></i>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {t(section.id, section.title)}\n                  </span>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Features List */}\n        <div className=\"mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('features', 'Features')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('comprehensiveSections', 'Comprehensive assessment sections')}\n              </div>\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('fimScoring', 'FIM scoring system integration')}\n              </div>\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('riskAssessment', 'Risk assessment and mitigation')}\n              </div>\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('psychosocialEvaluation', 'Psychosocial evaluation')}\n              </div>\n            </div>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('environmentalAssessment', 'Environmental assessment')}\n              </div>\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('goalSetting', 'Goal setting and preferences')}\n              </div>\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('dischargePlanning', 'Discharge planning')}\n              </div>\n              <div className=\"flex items-center text-sm text-gray-700 dark:text-gray-300\">\n                <i className=\"fas fa-check text-green-600 mr-2\"></i>\n                {t('carfCompliance', 'CARF compliance standards')}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Last Assessment Data */}\n        {assessmentData && (\n          <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              {t('lastAssessmentData', 'Last Assessment Data')}\n            </h3>\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              <p><strong>{t('primaryDiagnosis', 'Primary Diagnosis')}:</strong> {assessmentData.primaryDiagnosis}</p>\n              <p><strong>{t('assessmentDate', 'Assessment Date')}:</strong> {assessmentData.assessmentDate}</p>\n              <p><strong>{t('assessor', 'Assessor')}:</strong> {assessmentData.assessor}</p>\n            </div>\n            <button\n              onClick={() => console.log('Full Assessment Data:', assessmentData)}\n              className=\"mt-2 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700\"\n            >\n              {t('logFullData', 'Log Full Data to Console')}\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* CARF Assessment Modal */}\n      {showAssessment && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto\">\n            <CARFAssessmentForm\n              patientId=\"test-patient-123\"\n              initialData={assessmentData || {}}\n              onSave={handleSave}\n              onCancel={handleCancel}\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TestCARFAssessment;\n"], "names": ["TestCARFAssessment", "t", "useTranslation", "showAssessment", "setShowAssessment", "useState", "assessmentData", "setAssessmentData", "sampleData", "primaryDiagnosis", "secondaryDiagnoses", "medicalHistory", "currentMedications", "allergies", "precautions", "psychosocialAssessment", "moodState", "copingStrategies", "motivationLevel", "insightLevel", "familySupport", "socialConnections", "culturalConsiderations", "spiritual<PERSON><PERSON>s", "notes", "environmentalAssessment", "homeAccessibility", "homeSafety", "homeModifications", "transportationAccess", "communityResources", "_jsxs", "className", "children", "_jsx", "onClick", "id", "title", "icon", "color", "map", "section", "concat", "assessmentDate", "assessor", "console", "log", "CARFAssessmentForm", "patientId", "initialData", "onSave", "data", "toast", "success", "onCancel", "handleCancel"], "sourceRoot": ""}