"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6873],{6873:(e,a,r)=>{r.r(a),r.d(a,{default:()=>b});var t=r(2555),s=r(5043),i=r(3216),l=r(7921),n=r(3768),d=r(579);const o=e=>{let{formData:a,handleInputChange:r,errors:s}=e;const{t:i}=(0,l.o)(),n=[{key:"pain",label:i("pain","Pain"),hasSide:!1},{key:"bedMatMobility",label:i("bedMatMobilityStatus","\u2193 Bed/mat mobility status"),hasSide:!1},{key:"transferStatus",label:i("transferStatus","\u2193 Transfer status"),hasSide:!1},{key:"limitedUEROM",label:i("limitedUEROM","Limited R/L/B UE ROM"),hasSide:!0},{key:"limitedLEROM",label:i("limitedLEROM","Limited R/L/B LE ROM"),hasSide:!0},{key:"decreasedLEStrength",label:i("decreasedLEStrength","\u2193 R/L/B LE strength"),hasSide:!0},{key:"decreasedUEStrength",label:i("decreasedUEStrength","\u2193 R/L/B UE strength"),hasSide:!0},{key:"neckTrunkStrength",label:i("neckTrunkStrength","\u2193 Neck/trunk strength"),hasSide:!1},{key:"abnormalTone",label:i("abnormalTone","Abnormal tone"),hasSide:!0},{key:"abnormalMovement",label:i("abnormalMovement","Abnormal movement"),hasSide:!0},{key:"skinBreakdown",label:i("skinBreakdown","Skin breakdown"),hasSide:!1},{key:"gaitAsymmetry",label:i("gaitAsymmetry","Gait asymmetry altered Gait pattern"),hasSide:!1},{key:"atrophy",label:i("atrophy","Atrophy"),hasSide:!0},{key:"muscleWeakness",label:i("muscleWeakness","Muscle weakness"),hasSide:!0},{key:"imbalance",label:i("imbalance","Imbalance/poor balance"),hasSide:!1},{key:"lackCoordination",label:i("lackCoordination","Lack of coordination"),hasSide:!0},{key:"visualPerception",label:i("visualPerception","\u2193 Visual perception"),hasSide:!1},{key:"softTissueDysfunction",label:i("softTissueDysfunction","Soft tissue dysfunction"),hasSide:!0},{key:"poorPosture",label:i("poorPosture","Poor posture/abnormal posture"),hasSide:!1},{key:"improperBodyMechanics",label:i("improperBodyMechanics","Improper body mechanics"),hasSide:!1},{key:"wheelchairMobility",label:i("wheelchairMobility","\u2193 W/C mobility"),hasSide:!1},{key:"difficultyAmbulating",label:i("difficultyAmbulating","Difficulty ambulating"),hasSide:!1},{key:"abnormalGait",label:i("abnormalGait","Abnormal gait"),hasSide:!1},{key:"decreasedEndurance",label:i("decreasedEndurance","\u2193 Endurance"),hasSide:!1},{key:"decreasedSensation",label:i("decreasedSensation","\u2193 Sensation/proprioception"),hasSide:!0},{key:"respiratoryCapacity",label:i("respiratoryCapacity","\u2193 Respiratory capacity"),hasSide:!1},{key:"fineMotorDexterity",label:i("fineMotorDexterity","\u2193 Fine motor/dexterity"),hasSide:!0},{key:"functionalActivity",label:i("functionalActivity","Decreased functional activity: ADL/work skills"),hasSide:!1},{key:"jointHypomobility",label:i("jointHypomobility","Joint hypomobility"),hasSide:!0},{key:"jointHypermobility",label:i("jointHypermobility","Joint hypermobility"),hasSide:!0},{key:"contracture",label:i("contracture","Contracture"),hasSide:!0},{key:"other",label:i("other","Other"),hasSide:!1}],o=[{value:"R",label:i("right","R")},{value:"L",label:i("left","L")},{value:"B",label:i("bilateral","B")}],c=(e,s,i)=>{const l=a.functionalProblems[e]||{},n=(0,t.A)((0,t.A)({},l),{},{[s]:i});r("functionalProblems.".concat(e),n)};return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:i("functionalProblems","Patient Problems (Reason for Referral)")}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:i("functionalProblemsImpairments","Functional Problems & Impairments")}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:n.map(e=>{const r=a.functionalProblems[e.key]||{};return(0,d.jsx)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("input",{type:"checkbox",id:e.key,checked:r.checked||!1,onChange:a=>c(e.key,"checked",a.target.checked),className:"mt-1"}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{htmlFor:e.key,className:"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer",children:e.label}),e.hasSide&&r.checked&&(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsxs)("label",{className:"block text-xs text-gray-600 dark:text-gray-400 mb-1",children:[i("side","Side"),":"]}),(0,d.jsx)("div",{className:"flex space-x-2",children:o.map(a=>(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",name:"".concat(e.key,"_side"),value:a.value,checked:r.side===a.value,onChange:a=>c(e.key,"side",a.target.value),className:"mr-1"}),(0,d.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:a.label})]},a.value))})]}),r.checked&&(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsxs)("label",{className:"block text-xs text-gray-600 dark:text-gray-400 mb-1",children:[i("specificDescription","Specific Description / Objective Measure"),":"]}),(0,d.jsx)("textarea",{value:r.description||"",onChange:a=>c(e.key,"description",a.target.value),rows:2,className:"w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterMeasurableDescription","Enter measurable description (e.g., Pain score 7/10)")})]})]})]})},e.key)})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:i("topSixProblems","Top 6 Problems Summary")}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:i("topProblemsDescription","Summarize the most important problems from the selections above")}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:a.topProblems.map((e,t)=>(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[t+1,". ",i("problem","Problem")," ",t+1]}),(0,d.jsx)("textarea",{value:e,onChange:e=>((e,t)=>{const s=[...a.topProblems];s[e]=t,r("topProblems",s)})(t,e.target.value),rows:2,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:i("enterProblemSummary","Enter problem summary")})]},t))})]})]})]})},c=e=>{let{formData:a,handleInputChange:r,handleArrayChange:t,addGoal:s,removeGoal:i,errors:n}=e;const{t:o}=(0,l.o)(),c=[o("goalExample1","Patient will demonstrate improved balance by standing on one foot for 30 seconds without support"),o("goalExample2","Patient will increase shoulder flexion ROM from 90\xb0 to 150\xb0 within treatment period"),o("goalExample3","Patient will walk 100 meters independently without assistive device"),o("goalExample4","Patient will demonstrate proper body mechanics during lifting activities"),o("goalExample5","Patient will achieve pain level of 3/10 or less during functional activities")],m=(e,l,m,g)=>{var b,u;const h=(null===(b=a[e])||void 0===b?void 0:b.goals)||[],x=(null===(u=a[e])||void 0===u?void 0:u.weeks)||"";return(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-md font-semibold text-gray-900 dark:text-white mb-4",children:l}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("weeks","Weeks")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"number",min:"1",max:"52",value:x,onChange:a=>r("".concat(e,".weeks"),a.target.value),className:"w-32 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white ".concat(n[m]?"border-red-500":"border-gray-300"),placeholder:"1-52"}),n[m]&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:n[m]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[o("goals","Goals")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("button",{type:"button",onClick:()=>s(e),className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-1"}),o("addGoal","Add Goal")]})]}),h.map((a,r)=>(0,d.jsx)("div",{className:"relative",children:(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsxs)("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mt-2 min-w-[20px]",children:[r+1,"."]}),(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsx)("textarea",{value:a,onChange:a=>t("".concat(e,".goals"),r,a.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white",placeholder:o("enterSMARTGoal","Enter SMART goal (Specific, Measurable, Achievable, Relevant, Time-bound)")})}),h.length>1&&(0,d.jsx)("button",{type:"button",onClick:()=>i(e,r),className:"mt-2 p-1 text-red-600 hover:text-red-800 transition-colors",title:o("removeGoal","Remove Goal"),children:(0,d.jsx)("i",{className:"fas fa-trash text-sm"})})]})},r)),n[g]&&(0,d.jsx)("p",{className:"text-red-500 text-sm",children:n[g]})]}),(0,d.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2",children:[(0,d.jsx)("i",{className:"fas fa-lightbulb mr-1"}),o("goalExamples","Goal Examples"),":"]}),(0,d.jsx)("ul",{className:"text-xs text-blue-800 dark:text-blue-200 space-y-1",children:c.slice(0,3).map((e,a)=>(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"mr-2",children:"\u2022"}),(0,d.jsx)("span",{children:e})]},a))})]}),(0,d.jsxs)("div",{className:"mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-green-900 dark:text-green-100 mb-2",children:[(0,d.jsx)("i",{className:"fas fa-check-circle mr-1"}),o("smartGoalsReminder","SMART Goals Checklist"),":"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-2 text-xs text-green-800 dark:text-green-200",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"S"}),"pecific: ",o("specific","Clear and well-defined")]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"M"}),"easurable: ",o("measurable","Quantifiable outcomes")]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"A"}),"chievable: ",o("achievable","Realistic and attainable")]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"R"}),"elevant: ",o("relevant","Related to patient needs")]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"T"}),"ime-bound: ",o("timeBound","Has a clear timeframe")]})]})]})]})};return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:o("goalsOfTreatment","Goals of Treatment")}),(0,d.jsxs)("div",{className:"space-y-6",children:[m("shortTermGoals",o("shortTermGoals","Short Term Goals"),"shortTermWeeks","shortTermGoals"),m("longTermGoals",o("longTermGoals","Long Term Goals"),"longTermWeeks","longTermGoals"),(0,d.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mt-1 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1",children:[o("goalValidationTips","Goal Validation Tips"),":"]}),(0,d.jsxs)("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",o("goalTip1","Long term goals should have more weeks than short term goals")]}),(0,d.jsxs)("li",{children:["\u2022 ",o("goalTip2","Include specific measurements (degrees, distances, pain scales)")]}),(0,d.jsxs)("li",{children:["\u2022 ",o("goalTip3","Focus on functional outcomes that matter to the patient")]}),(0,d.jsxs)("li",{children:["\u2022 ",o("goalTip4","Ensure goals are achievable within the specified timeframe")]})]})]})]})})]})]})},m=e=>{var a,r,s;let{formData:i,handleInputChange:n}=e;const{t:o}=(0,l.o)(),c=(e,a,r)=>{const s=i.treatmentPlan[e]||{},l=(0,t.A)((0,t.A)({},s),{},{[a]:r});n("treatmentPlan.".concat(e),l)},m=[{key:"painControl",title:o("painControl","Pain Control"),icon:"fas fa-hand-holding-medical",treatments:[{key:"us",label:"US (Ultrasound)"},{key:"laser",label:"LASER"},{key:"tens",label:"TENS"},{key:"thermal",label:o("thermal","Thermal")}]},{key:"reduceSwelling",title:o("reduceSwellingEdema","Reduce Swelling/Edema"),icon:"fas fa-snowflake",treatments:[{key:"cryotherapy",label:o("cryotherapy","Cryotherapy")},{key:"hvc",label:"HVC"},{key:"compression",label:o("compression","Compression")}]},{key:"improveROM",title:o("improveROM","Improve ROM"),icon:"fas fa-expand-arrows-alt",treatments:[{key:"prom",label:"PROM"},{key:"mobilization",label:o("mobilization","Mobilization")},{key:"met",label:"MET"}]},{key:"improveFlexibility",title:o("improveFlexibility","Improve Flexibility"),icon:"fas fa-running",treatments:[{key:"stretching",label:o("stretching","Stretching")},{key:"thermal",label:o("thermal","Thermal")},{key:"myofascialRelease",label:o("myofascialRelease","Myofascial release")}]},{key:"muscleStrengthening",title:o("muscleStrengthening","Muscle Strengthening"),icon:"fas fa-dumbbell",treatments:[{key:"isometric",label:o("isometric","Isometric")},{key:"activeAssisted",label:o("activeAssisted","Active Assisted")},{key:"activeResisted",label:o("activeResisted","Active Resisted")},{key:"coreStrengthening",label:o("coreStrengthening","Core Strengthening")},{key:"plyometrics",label:o("plyometrics","Plyometrics")},{key:"fes",label:"FES"},{key:"pnf",label:"PNF"}]},{key:"posturalCorrection",title:o("posturalCorrection","Postural Correction"),icon:"fas fa-user-check",treatments:[{key:"properBodyMechanics",label:o("properBodyMechanics","Proper Body Mechanics")},{key:"ergonomics",label:o("ergonomics","Ergonomics")},{key:"tiltTable",label:o("tiltTable","Tilt table")}]},{key:"improveBalance",title:o("improveBalanceCoordination","Improve Balance and Coordination"),icon:"fas fa-balance-scale",treatments:[{key:"frenkelsEx",label:o("frenkelsEx","Frenkel's Ex")},{key:"balanceBoard",label:o("balanceBoard","Balance Board")},{key:"agilityEx",label:o("agilityEx","Agility Ex's")},{key:"proprioceptionTraining",label:o("proprioceptionTraining","Proprioception Training")},{key:"lumbopelvicRhythm",label:o("lumbopelvicRhythm","Lumbopelvic Rhythm")}]},{key:"improveEndurance",title:o("improveEndurance","Improve Endurance"),icon:"fas fa-heartbeat",treatments:[{key:"aerobicEx",label:o("aerobicEx","Aerobic Ex's")},{key:"bicycle",label:o("bicycle","Bicycle")},{key:"treadmill",label:o("treadmill","Treadmill")}]},{key:"gaitTraining",title:o("gaitTraining","Gait Training"),icon:"fas fa-walking",treatments:[{key:"normalGaitPattern",label:o("normalGaitPattern","Normal Gait Pattern")}],hasWeightBearing:!0},{key:"homeInstructions",title:o("homeInstructions","Home Instructions"),icon:"fas fa-home",treatments:[{key:"others",label:o("others","Others")}],hasDescription:!0}],g=[{value:"FWB",label:"FWB (Full Weight Bearing)"},{value:"PWB",label:"PWB (Partial Weight Bearing)"},{value:"WB",label:"WB (Weight Bearing)"},{value:"NWB",label:"NWB (Non Weight Bearing)"}];return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:o("treatmentPlan","Treatment Plan")}),(0,d.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:m.map(e=>{var a,r,t;return(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("i",{className:"".concat(e.icon," text-blue-600 dark:text-blue-400 mr-3")}),(0,d.jsx)("h3",{className:"text-md font-semibold text-gray-900 dark:text-white",children:e.title})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[e.treatments.map(a=>{var r;return(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:(null===(r=i.treatmentPlan[e.key])||void 0===r?void 0:r[a.key])||!1,onChange:r=>c(e.key,a.key,r.target.checked),className:"mr-3"}),(0,d.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:a.label})]},a.key)}),e.hasWeightBearing&&(null===(a=i.treatmentPlan[e.key])||void 0===a?void 0:a.normalGaitPattern)&&(0,d.jsxs)("div",{className:"mt-4 p-3 bg-white dark:bg-gray-600 rounded border",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[o("weightBearing","Weight Bearing"),":"]}),(0,d.jsx)("div",{className:"space-y-2",children:g.map(a=>{var r;return(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",name:"weightBearingType",value:a.value,checked:(null===(r=i.treatmentPlan[e.key])||void 0===r?void 0:r.weightBearingType)===a.value,onChange:a=>c(e.key,"weightBearingType",a.target.value),className:"mr-2"}),(0,d.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:a.label})]},a.value)})})]}),e.hasDescription&&(null===(r=i.treatmentPlan[e.key])||void 0===r?void 0:r.others)&&(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[o("description","Description"),":"]}),(0,d.jsx)("textarea",{value:(null===(t=i.treatmentPlan[e.key])||void 0===t?void 0:t.description)||"",onChange:a=>c(e.key,"description",a.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white",placeholder:o("enterHomeInstructions","Enter home instructions and exercises")})]})]})]},e.key)})}),(0,d.jsxs)("div",{className:"mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-calendar-alt mr-2"}),o("treatmentSchedule","Treatment Schedule")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:o("sessionsPerWeek","Sessions per Week")}),(0,d.jsx)("input",{type:"number",min:"1",max:"7",value:(null===(a=i.treatmentSchedule)||void 0===a?void 0:a.sessionsPerWeek)||"",onChange:e=>n("treatmentSchedule.sessionsPerWeek",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:"1-7"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:o("sessionDuration","Session Duration (minutes)")}),(0,d.jsx)("input",{type:"number",min:"15",max:"120",value:(null===(r=i.treatmentSchedule)||void 0===r?void 0:r.sessionDuration)||"",onChange:e=>n("treatmentSchedule.sessionDuration",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:"30-60"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:o("totalWeeks","Total Treatment Weeks")}),(0,d.jsx)("input",{type:"number",min:"1",max:"52",value:(null===(s=i.treatmentSchedule)||void 0===s?void 0:s.totalWeeks)||"",onChange:e=>n("treatmentSchedule.totalWeeks",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white",placeholder:"4-12"})]})]})]})]})},g=e=>{var a,r,t,s,i,n,o;let{formData:c,handleInputChange:m,errors:g}=e;const{t:b}=(0,l.o)();return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:b("planReviewSignatures","Plan Review and Signatures")}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:c.planReviewedWithPatient||!1,onChange:e=>m("planReviewedWithPatient",e.target.checked),className:"mr-3"}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:b("planReviewedWithPatient","Plan of Care Reviewed with Patient")})]}),(0,d.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 mt-2 ml-6",children:b("planReviewDescription","Check this box to confirm that the treatment plan has been discussed and reviewed with the patient and/or their family.")})]}),(0,d.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4",children:[(0,d.jsx)("i",{className:"fas fa-user-md mr-2"}),b("therapistSignature","Therapist Signature")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:[b("therapistName","Therapist Name")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:(null===(a=c.therapistSignature)||void 0===a?void 0:a.name)||"",onChange:e=>m("therapistSignature.name",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ".concat(g.therapistName?"border-red-500":"border-blue-300"),placeholder:b("enterTherapistName","Enter therapist full name")}),g.therapistName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:g.therapistName})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:[b("badgeNumber","Badge No.")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:(null===(r=c.therapistSignature)||void 0===r?void 0:r.badgeNo)||"",onChange:e=>m("therapistSignature.badgeNo",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ".concat(g.therapistBadge?"border-red-500":"border-blue-300"),placeholder:b("enterBadgeNumber","Enter badge number")}),g.therapistBadge&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:g.therapistBadge})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1",children:b("date","Date")}),(0,d.jsx)("input",{type:"date",value:(null===(t=c.therapistSignature)||void 0===t?void 0:t.date)||"",onChange:e=>m("therapistSignature.date",e.target.value),className:"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white"})]})]}),(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2",children:b("digitalSignature","Digital Signature")}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center",children:[(0,d.jsx)("i",{className:"fas fa-signature text-3xl text-blue-400 mb-2"}),(0,d.jsx)("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:b("digitalSignaturePlaceholder","Digital signature will be captured here")}),(0,d.jsxs)("button",{type:"button",className:"mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-pen mr-2"}),b("addSignature","Add Signature")]})]})]})]}),(0,d.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-green-900 dark:text-green-100 mb-4",children:[(0,d.jsx)("i",{className:"fas fa-user-md mr-2"}),b("physicianReview","Physician Review")]}),(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2",children:b("physicianStatement","Physician Statement")}),(0,d.jsx)("textarea",{value:(null===(s=c.physicianReview)||void 0===s?void 0:s.statement)||"",onChange:e=>m("physicianReview.statement",e.target.value),rows:3,className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white",placeholder:b("physicianStatementPlaceholder","Have reviewed this plan of care and re-certify a continuing need for services.")})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:b("physicianName","Physician Name")}),(0,d.jsx)("input",{type:"text",value:(null===(i=c.physicianReview)||void 0===i?void 0:i.signature)||"",onChange:e=>m("physicianReview.signature",e.target.value),className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white",placeholder:b("enterPhysicianName","Enter physician name")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:b("badgeNumber","Badge No.")}),(0,d.jsx)("input",{type:"text",value:(null===(n=c.physicianReview)||void 0===n?void 0:n.badgeNo)||"",onChange:e=>m("physicianReview.badgeNo",e.target.value),className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white",placeholder:b("enterBadgeNumber","Enter badge number")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-1",children:b("reviewDate","Review Date")}),(0,d.jsx)("input",{type:"date",value:(null===(o=c.physicianReview)||void 0===o?void 0:o.date)||"",onChange:e=>m("physicianReview.date",e.target.value),className:"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white"})]})]}),(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2",children:b("physicianDigitalSignature","Physician Digital Signature")}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-8 text-center",children:[(0,d.jsx)("i",{className:"fas fa-signature text-3xl text-green-400 mb-2"}),(0,d.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:b("physicianSignaturePlaceholder","Physician digital signature will be captured here")}),(0,d.jsxs)("button",{type:"button",className:"mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-pen mr-2"}),b("addPhysicianSignature","Add Physician Signature")]})]})]})]}),(0,d.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mt-1 mr-3"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:b("complianceNotice","Compliance & Legal Notice")}),(0,d.jsxs)("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",b("hipaaCompliance","This form complies with HIPAA privacy and security requirements")]}),(0,d.jsxs)("li",{children:["\u2022 ",b("carfCompliance","Treatment plan meets CARF accreditation standards")]}),(0,d.jsxs)("li",{children:["\u2022 ",b("cbahiCompliance","Documentation follows CBAHI quality guidelines")]}),(0,d.jsxs)("li",{children:["\u2022 ",b("signatureRequirement","Digital signatures are legally binding and encrypted")]}),(0,d.jsxs)("li",{children:["\u2022 ",b("auditTrail","All changes are logged for audit trail purposes")]})]})]})]})})]})]})},b=e=>{let{patientId:a,patientData:r,fromPatientProfile:b,initialData:u={},onSave:h,onCancel:x}=e;const{t:p}=(0,l.o)(),y=(0,i.Zp)(),{patientId:k,planId:f}=(0,i.g)(),[v,N]=(0,s.useState)(!1),[j,w]=(0,s.useState)({}),[S,P]=(0,s.useState)(null),C=a||k,[T,R]=(0,s.useState)({documentNumber:"QP-",issueDate:(new Date).toISOString().split("T")[0],version:"01",reviewNumber:"01",patientName:"",mrNumber:"",diagnosis:"",onsetDate:"",physician:"",functionalProblems:{pain:{checked:!1,description:"",side:""},bedMatMobility:{checked:!1,description:"",side:""},transferStatus:{checked:!1,description:"",side:""},limitedUEROM:{checked:!1,description:"",side:""},limitedLEROM:{checked:!1,description:"",side:""},decreasedLEStrength:{checked:!1,description:"",side:""},decreasedUEStrength:{checked:!1,description:"",side:""},neckTrunkStrength:{checked:!1,description:"",side:""},abnormalTone:{checked:!1,description:"",side:""},abnormalMovement:{checked:!1,description:"",side:""},skinBreakdown:{checked:!1,description:"",side:""},gaitAsymmetry:{checked:!1,description:"",side:""},atrophy:{checked:!1,description:"",side:""},muscleWeakness:{checked:!1,description:"",side:""},imbalance:{checked:!1,description:"",side:""},lackCoordination:{checked:!1,description:"",side:""},visualPerception:{checked:!1,description:"",side:""},softTissueDysfunction:{checked:!1,description:"",side:""},poorPosture:{checked:!1,description:"",side:""},improperBodyMechanics:{checked:!1,description:"",side:""},wheelchairMobility:{checked:!1,description:"",side:""},difficultyAmbulating:{checked:!1,description:"",side:""},abnormalGait:{checked:!1,description:"",side:""},decreasedEndurance:{checked:!1,description:"",side:""},decreasedSensation:{checked:!1,description:"",side:""},respiratoryCapacity:{checked:!1,description:"",side:""},fineMotorDexterity:{checked:!1,description:"",side:""},functionalActivity:{checked:!1,description:"",side:""},jointHypomobility:{checked:!1,description:"",side:""},jointHypermobility:{checked:!1,description:"",side:""},contracture:{checked:!1,description:"",side:""},other:{checked:!1,description:"",side:""}},topProblems:["","","","","",""],shortTermGoals:{weeks:"",goals:["","","",""]},longTermGoals:{weeks:"",goals:["","","",""]},treatmentPlan:{painControl:{us:!1,laser:!1,tens:!1,thermal:!1},reduceSwelling:{cryotherapy:!1,hvc:!1,compression:!1},improveROM:{prom:!1,mobilization:!1,met:!1},improveFlexibility:{stretching:!1,thermal:!1,myofascialRelease:!1},muscleStrengthening:{isometric:!1,activeAssisted:!1,activeResisted:!1,coreStrengthening:!1,plyometrics:!1,fes:!1,pnf:!1},posturalCorrection:{properBodyMechanics:!1,ergonomics:!1,tiltTable:!1},improveBalance:{frenkelsEx:!1,balanceBoard:!1,agilityEx:!1,proprioceptionTraining:!1,lumbopelvicRhythm:!1},improveEndurance:{aerobicEx:!1,bicycle:!1,treadmill:!1},gaitTraining:{normalGaitPattern:!1,weightBearing:"",weightBearingType:""},homeInstructions:{others:!1,description:""}},planReviewedWithPatient:!1,therapistSignature:{name:"",badgeNo:"",date:(new Date).toISOString().split("T")[0]},physicianReview:{statement:"Have reviewed this plan of care and re-certify a continuing need for services.",signature:"",badgeNo:"",date:""}});(0,s.useEffect)(()=>{if(b&&r){const e={id:r._id||r.id,name:r.name||"".concat(r.firstName," ").concat(r.lastName),nameEn:r.nameEn||"".concat(r.firstName," ").concat(r.lastName),mrNumber:r.mrNumber||r._id,dateOfBirth:r.dateOfBirth,age:r.age,gender:r.gender,nationalId:r.nationalId,phone:r.phone,nationality:r.nationality,address:r.address};P(e),R(a=>{var s,i;return(0,t.A)((0,t.A)({},a),{},{patientName:e.nameEn,mrNumber:e.mrNumber,dateOfBirth:e.dateOfBirth,age:(null===(s=e.age)||void 0===s?void 0:s.toString())||"",gender:e.gender,diagnosis:(null===(i=r.medicalHistory)||void 0===i?void 0:i.primaryDiagnosis)||""},u)}),N(!1)}else C&&(N(!0),setTimeout(()=>{const e={id:C,name:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",nameEn:"Ahmed Mohammed Ali",mrNumber:"MR-2024-001",dateOfBirth:"2016-03-15",age:8,gender:"male",nationalId:"**********",phone:"+966 50 123 4567",nationality:"Saudi",address:"\u0627\u0644\u0631\u064a\u0627\u0636\u060c \u0627\u0644\u0645\u0645\u0644\u0643\u0629 \u0627\u0644\u0639\u0631\u0628\u064a\u0629 \u0627\u0644\u0633\u0639\u0648\u062f\u064a\u0629"};P(e),R(a=>(0,t.A)((0,t.A)({},a),{},{patientName:e.nameEn,mrNumber:e.mrNumber})),N(!1)},500))},[C,b,r,u]),(0,s.useEffect)(()=>{u&&Object.keys(u).length>0&&R(e=>(0,t.A)((0,t.A)({},e),u))},[u]);const E=(e,a)=>{const r=(0,t.A)({},T);if(e.includes(".")){const t=e.split(".");let s=r;for(let e=0;e<t.length-1;e++)s[t[e]]||(s[t[e]]={}),s=s[t[e]];s[t[t.length-1]]=a}else r[e]=a;R(r),j[e]&&w(a=>(0,t.A)((0,t.A)({},a),{},{[e]:null}))};return v&&!S?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,d.jsx)("div",{className:"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900",children:(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};T.patientName.trim()||(e.patientName=p("patientNameRequired","Patient name is required")),T.mrNumber.trim()||(e.mrNumber=p("mrNumberRequired","MR number is required")),T.diagnosis.trim()||(e.diagnosis=p("diagnosisRequired","Diagnosis is required")),T.onsetDate||(e.onsetDate=p("onsetDateRequired","Onset date is required")),T.physician.trim()||(e.physician=p("physicianRequired","Physician is required")),(!T.shortTermGoals.weeks||T.shortTermGoals.weeks<1)&&(e.shortTermWeeks=p("shortTermWeeksRequired","Short term weeks must be at least 1")),(!T.longTermGoals.weeks||T.longTermGoals.weeks<1)&&(e.longTermWeeks=p("longTermWeeksRequired","Long term weeks must be at least 1")),T.longTermGoals.weeks<=T.shortTermGoals.weeks&&(e.longTermWeeks=p("longTermWeeksMustBeGreater","Long term weeks must be greater than short term weeks"));const a=T.shortTermGoals.goals.some(e=>e.trim()),r=T.longTermGoals.goals.some(e=>e.trim());return a||(e.shortTermGoals=p("atLeastOneShortTermGoal","At least one short term goal is required")),r||(e.longTermGoals=p("atLeastOneLongTermGoal","At least one long term goal is required")),T.therapistSignature.name.trim()||(e.therapistName=p("therapistNameRequired","Therapist name is required")),T.therapistSignature.badgeNo.trim()||(e.therapistBadge=p("therapistBadgeRequired","Therapist badge number is required")),w(e),0===Object.keys(e).length})()){N(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),h)h(T);else{const e=JSON.parse(localStorage.getItem("planOfCareData")||"[]"),a=(0,t.A)((0,t.A)({},T),{},{id:Date.now(),patientId:C,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString()});e.push(a),localStorage.setItem("planOfCareData",JSON.stringify(e)),n.Ay.success(p("planOfCareSaved","Plan of Care saved successfully")),y(C?"/patients/".concat(C):"/patients")}}catch(a){console.error("Error saving plan of care:",a),n.Ay.error(p("errorSavingPlan","Error saving plan of care"))}finally{N(!1)}}else n.Ay.error(p("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-8",children:[(0,d.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[p("initialPlanOfCare","Initial Plan of Care for Physical Therapy"),C&&S&&(0,d.jsxs)("span",{className:"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3",children:["- ",S.nameEn||S.name]})]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:p("planOfCareDescription","Comprehensive 2-page plan of care for physical therapy treatment")}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mt-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:[(0,d.jsx)("i",{className:"fas fa-certificate text-blue-600 dark:text-blue-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"CARF Compliant"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full",children:[(0,d.jsx)("i",{className:"fas fa-shield-alt text-green-600 dark:text-green-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"CBAHI Compliant"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:[(0,d.jsx)("i",{className:"fas fa-lock text-purple-600 dark:text-purple-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-purple-800 dark:text-purple-200",children:"HIPAA Secure"})]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[C&&(0,d.jsxs)("button",{type:"button",onClick:()=>y("/patients/".concat(C)),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-user mr-2"}),p("viewPatient","View Patient")]}),(0,d.jsxs)("button",{type:"button",onClick:()=>{n.Ay.success(p("pdfExported","PDF exported successfully"))},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-file-pdf mr-2"}),p("exportPDF","Export PDF")]}),(0,d.jsx)("button",{type:"button",onClick:x||(()=>y(C?"/patients/".concat(C):"/patients")),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:p("cancel","Cancel")})]})]})}),(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:p("documentInformation","Document Information")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:p("documentNumber","Document Number")}),(0,d.jsx)("input",{type:"text",value:T.documentNumber,onChange:e=>E("documentNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"QP-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:p("issueDate","Issue Date")}),(0,d.jsx)("input",{type:"date",value:T.issueDate,onChange:e=>E("issueDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:p("version","Version")}),(0,d.jsx)("input",{type:"text",value:T.version,onChange:e=>E("version",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"01"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:p("reviewNumber","Review Number")}),(0,d.jsx)("input",{type:"text",value:T.reviewNumber,onChange:e=>E("reviewNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"01"})]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:p("patientInformation","Patient Information")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[p("patientName","Patient Name")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:T.patientName,onChange:e=>E("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(j.patientName?"border-red-500":"border-gray-300"),placeholder:p("enterPatientName","Enter patient name")}),j.patientName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:j.patientName})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[p("mrNumber","MR #")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:T.mrNumber,onChange:e=>E("mrNumber",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(j.mrNumber?"border-red-500":"border-gray-300"),placeholder:p("enterMRNumber","Enter MR number")}),j.mrNumber&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:j.mrNumber})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[p("diagnosis","Diagnosis")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("textarea",{value:T.diagnosis,onChange:e=>E("diagnosis",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(j.diagnosis?"border-red-500":"border-gray-300"),placeholder:p("enterDiagnosis","Enter diagnosis")}),j.diagnosis&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:j.diagnosis})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[p("onsetDate","Onset Date")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"date",value:T.onsetDate,onChange:e=>E("onsetDate",e.target.value),max:(new Date).toISOString().split("T")[0],className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(j.onsetDate?"border-red-500":"border-gray-300")}),j.onsetDate&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:j.onsetDate})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[p("physician","Physician")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:T.physician,onChange:e=>E("physician",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(j.physician?"border-red-500":"border-gray-300"),placeholder:p("enterPhysicianName","Enter physician name")}),j.physician&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:j.physician})]})]})]}),(0,d.jsx)(o,{formData:T,handleInputChange:E,errors:j}),(0,d.jsx)(c,{formData:T,handleInputChange:E,handleArrayChange:(e,a,r)=>{const s=(0,t.A)({},T),i=e.split(".");let l=s;for(let t=0;t<i.length-1;t++)l=l[i[t]];l[i[i.length-1]][a]=r,R(s)},addGoal:e=>{const a=(0,t.A)({},T);a[e].goals.push(""),R(a)},removeGoal:(e,a)=>{const r=(0,t.A)({},T);r[e].goals.splice(a,1),R(r)},errors:j}),(0,d.jsx)(m,{formData:T,handleInputChange:E}),(0,d.jsx)(g,{formData:T,handleInputChange:E,errors:j}),(0,d.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,d.jsx)("button",{type:"button",onClick:x||(()=>y(C?"/patients/".concat(C):"/patients")),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:p("cancel","Cancel")}),(0,d.jsx)("button",{type:"submit",disabled:v,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:v?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),p("saving","Saving...")]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),p("savePlanOfCare","Save Plan of Care")]})})]})]})})}}}]);
//# sourceMappingURL=6873.3aea0ef4.chunk.js.map