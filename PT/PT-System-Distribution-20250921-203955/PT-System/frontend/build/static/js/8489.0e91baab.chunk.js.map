{"version": 3, "file": "static/js/8489.0e91baab.chunk.js", "mappings": "4IAEO,MAsOMA,EAAyB,CACpCC,gBAAiB,CACfC,MAAO,IACPC,MAAO,kBACPC,MAAO,QACPC,YAAa,4BAEfC,uBAAwB,CACtBJ,MAAO,GACPC,MAAO,yBACPC,MAAO,SACPC,YAAa,+BAEfE,mBAAoB,CAClBL,MAAO,GACPC,MAAO,qBACPC,MAAO,SACPC,YAAa,mCAEfG,eAAgB,CACdN,MAAO,EACPC,MAAO,iBACPC,MAAO,MACPC,YAAa,iC,0FC3PjB,MAyRA,EAzRgCI,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACpC,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,CAAC,IAC/CC,EAAkBC,IAAuBF,EAAAA,EAAAA,UAAS,OAClDG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAGvCK,EAAAA,EAAAA,WAAU,KACR,MAAMC,EAAW,CACfC,QAAS,CACPxD,MAAO,GACPyD,MAAO,yBACPC,eAAgB,aAChBC,eAAgB,cAElBC,WAAY,CACVC,cAAe,CAAE7D,MAAO,GAAI8D,OAAQ,YAAaC,OAAQ,GACzDC,kBAAmB,CAAEhE,MAAO,GAAI8D,OAAQ,cAAeC,OAAQ,GAC/DE,gBAAiB,CAAEjE,MAAO,GAAI8D,OAAQ,YAAaC,OAAQ,GAC3DG,eAAgB,CAAElE,MAAO,GAAI8D,OAAQ,YAAaC,OAAQ,GAC1DI,uBAAwB,CAAEnE,MAAO,GAAI8D,OAAQ,cAAeC,OAAQ,IAEtEK,eAAgB,CACd,CACEC,GAAI,EACJC,SAAU,eACVC,QAAS,+DACTC,SAAU,QACVC,QAAS,aACTX,OAAQ,eAEV,CACEO,GAAI,EACJC,SAAU,SACVC,QAAS,uDACTC,SAAU,WACVC,QAAS,aACTX,OAAQ,WAEV,CACEO,GAAI,EACJC,SAAU,YACVC,QAAS,gDACTC,SAAU,QACVC,QAAS,aACTX,OAAQ,eAKdY,WAAW,KACT1B,EAAkBO,GAClBF,GAAW,IACV,MACF,IAEH,MAOMsB,EAAiBC,IAAA,IAAC,MAAEC,EAAK,MAAE7E,EAAK,OAAE8D,EAAM,OAAEC,EAAM,KAAEe,EAAI,MAAE5E,GAAO0E,EAAA,OACnEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,UAAAG,OAAYjF,EAAK,iBAAAiF,OAAgBjF,EAAK,sBAAqB+E,UACvEC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAG,OAAKL,EAAI,UAAAK,OAASjF,EAAK,mBAAAiF,OAAkBjF,EAAK,qBAE5D6E,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAAEJ,KACrEE,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,CACpDlB,EAAO,IAAEnB,EAAE,mBAAoB,+BAItCmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mDAAkDC,SAAA,CAAEjF,EAAM,QACzEkF,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uBAAAG,OACD,cAAXrB,EAAyB,iBACd,gBAAXA,EAA2B,kBAAoB,gBAC9CmB,SACW,cAAXnB,EAAyBlB,EAAE,YAAa,aAC7B,gBAAXkB,EAA2BlB,EAAE,cAAe,eAC5CA,EAAE,eAAgB,0BAIzBsC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,MAAAG,OAAQjF,EAAK,qDACtBkF,MAAO,CAAEC,MAAM,GAADF,OAAKnF,EAAK,cAM1BsF,EAAcC,IAAA,IAAC,QAAEhB,EAAO,cAAEiB,GAAeD,EAAA,OAC7CL,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,yCAAAG,OACQ,UAArBZ,EAAQC,SAAuB,2EACV,aAArBD,EAAQC,SAA0B,2EAClC,gEACCS,SACAV,EAAQC,YAEXU,EAAAA,EAAAA,KAAA,QAAMF,UAAS,yCAAAG,OACM,cAAnBZ,EAAQT,OAAyB,uEACd,gBAAnBS,EAAQT,OAA2B,mEACnC,oEACCmB,SACAV,EAAQT,aAGboB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iDAAgDC,SAAEV,EAAQA,WACvEQ,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,CACpDrC,EAAE,UAAW,YAAY,KAAG,IAAI6C,KAAKlB,EAAQE,SAASiB,4BAG3DR,EAAAA,EAAAA,KAAA,UACES,QAASA,IAAMH,EAAcjB,GAC7BS,UAAU,4FAA2FC,SAEpGrC,EAAE,cAAe,wBAM1B,GAAIQ,EACF,OACE8B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qEAKrB,MAAMY,GArFsB5F,GAqFkC,QAAtBQ,EAAAuC,EAAeS,eAAO,IAAAhD,OAAA,EAAtBA,EAAwBR,QAAS,IApF1D,GAAWF,EAAAA,GAAuBC,gBAC3CC,GAAS,GAAWF,EAAAA,GAAuBM,uBAC3CJ,GAAS,GAAWF,EAAAA,GAAuBO,mBACxCP,EAAAA,GAAuBQ,eAJJN,MAuF5B,OACE+E,EAAAA,EAAAA,MAAA,OAAKC,UAAS,aAAAG,OAAetC,EAAQ,cAAgB,gBAAiBoC,SAAA,EAEpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DrC,EAAE,iBAAkB,gCAEvBsC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDrC,EAAE,kBAAmB,8DAG1BmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oFAAmFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZpC,EAAE,eAAgB,qBAErBmC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZpC,EAAE,gBAAiB,4BAM1BmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChErC,EAAE,oBAAqB,8BAE1BmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mDAAkDC,SAAA,CACxC,QADwCxE,EAC9DsC,EAAeS,eAAO,IAAA/C,OAAA,EAAtBA,EAAwBT,MAAM,QAEjCkF,EAAAA,EAAAA,KAAA,OAAKF,UAAS,4BAAAG,OAA8BS,EAAa1F,MAAK,QAAO+E,SAClEW,EAAa3F,eAKpB8E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDrC,EAAE,iBAAkB,sBAEvBsC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SACrD,IAAIQ,KAA2B,QAAvB/E,EAACqC,EAAeS,eAAO,IAAA9C,OAAA,EAAtBA,EAAwBgD,gBAAgBgC,2BAGtDX,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDrC,EAAE,iBAAkB,sBAEvBsC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4CAA2CC,SACrD,IAAIQ,KAA2B,QAAvB9E,EAACoC,EAAeS,eAAO,IAAA7C,OAAA,EAAtBA,EAAwBgD,gBAAgB+B,8BAKxDR,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,UACnEC,EAAAA,EAAAA,KAAA,OACEF,UAAS,MAAAG,OAAQS,EAAa1F,MAAK,qDACnCkF,MAAO,CAAEC,MAAM,GAADF,OAA2B,QAA3BvE,EAAKmC,EAAeS,eAAO,IAAA5C,OAAA,EAAtBA,EAAwBZ,MAAK,WAGpDkF,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDW,EAAazF,kBAKlB4E,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrErC,EAAE,uBAAwB,6BAE7BmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEC,EAAAA,EAAAA,KAACP,EAAc,CACbE,MAAOjC,EAAE,gBAAiB,iBAC1B5C,MAAgC,QAA3Ba,EAAEkC,EAAea,kBAAU,IAAA/C,GAAe,QAAfC,EAAzBD,EAA2BgD,qBAAa,IAAA/C,OAAf,EAAzBA,EAA0Cd,MACjD8D,OAAiC,QAA3B/C,EAAEgC,EAAea,kBAAU,IAAA7C,GAAe,QAAfC,EAAzBD,EAA2B8C,qBAAa,IAAA7C,OAAf,EAAzBA,EAA0C8C,OAClDC,OAAiC,QAA3B9C,EAAE8B,EAAea,kBAAU,IAAA3C,GAAe,QAAfC,EAAzBD,EAA2B4C,qBAAa,IAAA3C,OAAf,EAAzBA,EAA0C6C,OAClDe,KAAK,kBACL5E,MAAM,UAERgF,EAAAA,EAAAA,KAACP,EAAc,CACbE,MAAOjC,EAAE,oBAAqB,sBAC9B5C,MAAgC,QAA3BmB,EAAE4B,EAAea,kBAAU,IAAAzC,GAAmB,QAAnBC,EAAzBD,EAA2B6C,yBAAiB,IAAA5C,OAAnB,EAAzBA,EAA8CpB,MACrD8D,OAAiC,QAA3BzC,EAAE0B,EAAea,kBAAU,IAAAvC,GAAmB,QAAnBC,EAAzBD,EAA2B2C,yBAAiB,IAAA1C,OAAnB,EAAzBA,EAA8CwC,OACtDC,OAAiC,QAA3BxC,EAAEwB,EAAea,kBAAU,IAAArC,GAAmB,QAAnBC,EAAzBD,EAA2ByC,yBAAiB,IAAAxC,OAAnB,EAAzBA,EAA8CuC,OACtDe,KAAK,oBACL5E,MAAM,WAERgF,EAAAA,EAAAA,KAACP,EAAc,CACbE,MAAOjC,EAAE,kBAAmB,oBAC5B5C,MAAgC,QAA3ByB,EAAEsB,EAAea,kBAAU,IAAAnC,GAAiB,QAAjBC,EAAzBD,EAA2BwC,uBAAe,IAAAvC,OAAjB,EAAzBA,EAA4C1B,MACnD8D,OAAiC,QAA3BnC,EAAEoB,EAAea,kBAAU,IAAAjC,GAAiB,QAAjBC,EAAzBD,EAA2BsC,uBAAe,IAAArC,OAAjB,EAAzBA,EAA4CkC,OACpDC,OAAiC,QAA3BlC,EAAEkB,EAAea,kBAAU,IAAA/B,GAAiB,QAAjBC,EAAzBD,EAA2BoC,uBAAe,IAAAnC,OAAjB,EAAzBA,EAA4CiC,OACpDe,KAAK,kBACL5E,MAAM,YAERgF,EAAAA,EAAAA,KAACP,EAAc,CACbE,MAAOjC,EAAE,iBAAkB,mBAC3B5C,MAAgC,QAA3B+B,EAAEgB,EAAea,kBAAU,IAAA7B,GAAgB,QAAhBC,EAAzBD,EAA2BmC,sBAAc,IAAAlC,OAAhB,EAAzBA,EAA2ChC,MAClD8D,OAAiC,QAA3B7B,EAAEc,EAAea,kBAAU,IAAA3B,GAAgB,QAAhBC,EAAzBD,EAA2BiC,sBAAc,IAAAhC,OAAhB,EAAzBA,EAA2C4B,OACnDC,OAAiC,QAA3B5B,EAAEY,EAAea,kBAAU,IAAAzB,GAAgB,QAAhBC,EAAzBD,EAA2B+B,sBAAc,IAAA9B,OAAhB,EAAzBA,EAA2C2B,OACnDe,KAAK,oBACL5E,MAAM,YAERgF,EAAAA,EAAAA,KAACP,EAAc,CACbE,MAAOjC,EAAE,yBAA0B,2BACnC5C,MAAgC,QAA3BqC,EAAEU,EAAea,kBAAU,IAAAvB,GAAwB,QAAxBC,EAAzBD,EAA2B8B,8BAAsB,IAAA7B,OAAxB,EAAzBA,EAAmDtC,MAC1D8D,OAAiC,QAA3BvB,EAAEQ,EAAea,kBAAU,IAAArB,GAAwB,QAAxBC,EAAzBD,EAA2B4B,8BAAsB,IAAA3B,OAAxB,EAAzBA,EAAmDsB,OAC3DC,OAAiC,QAA3BtB,EAAEM,EAAea,kBAAU,IAAAnB,GAAwB,QAAxBC,EAAzBD,EAA2B0B,8BAAsB,IAAAzB,OAAxB,EAAzBA,EAAmDqB,OAC3De,KAAK,qBACL5E,MAAM,kBAMZ6E,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrErC,EAAE,iBAAkB,iCAEvBsC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACM,QADNtC,EACvBI,EAAeqB,sBAAc,IAAAzB,OAAA,EAA7BA,EAA+BkD,IAAItB,IAClCW,EAAAA,EAAAA,KAACI,EAAW,CAEVf,QAASA,EACTiB,cAAgBjB,GAAYpB,EAAoBoB,IAF3CA,EAAQF,cC3Q3B,EAJsByB,KACbZ,EAAAA,EAAAA,KAAC3E,EAAuB,G", "sources": ["utils/carfStandards.js", "components/CARF/CARFComplianceDashboard.jsx", "pages/Compliance/CARFDashboard.jsx"], "sourcesContent": ["// CARF (Commission on Accreditation of Rehabilitation Facilities) Standards Configuration\n\nexport const CARF_STANDARDS = {\n  // CARF Documentation Requirements\n  DOCUMENTATION: {\n    ASSESSMENT: {\n      id: 'DOC_ASSESS',\n      title: 'Initial Assessment Requirements',\n      description: 'Comprehensive assessment within required timeframes',\n      requirements: [\n        'Initial assessment completed within 72 hours of admission',\n        'Comprehensive medical history documented',\n        'Functional assessment using standardized tools',\n        'Psychosocial assessment completed',\n        'Environmental assessment conducted',\n        'Risk assessment documented',\n        'Cultural and linguistic needs identified',\n        'Person-served preferences documented'\n      ],\n      timeframe: '72 hours',\n      responsible: 'Clinical Team',\n      category: 'Assessment'\n    },\n    TREATMENT_PLAN: {\n      id: 'DOC_PLAN',\n      title: 'Treatment Plan Documentation',\n      description: 'Individualized treatment plan requirements',\n      requirements: [\n        'Treatment plan developed within 30 days',\n        'Measurable goals and objectives defined',\n        'Person-served involvement in planning',\n        'Interdisciplinary team input documented',\n        'Frequency and duration of services specified',\n        'Discharge criteria established',\n        'Plan reviewed and updated regularly',\n        'Person-served signature obtained'\n      ],\n      timeframe: '30 days',\n      responsible: 'Treatment Team',\n      category: 'Planning'\n    },\n    PROGRESS_NOTES: {\n      id: 'DOC_PROGRESS',\n      title: 'Progress Documentation',\n      description: 'Regular progress monitoring and documentation',\n      requirements: [\n        'Progress notes completed after each session',\n        'Objective measurements recorded',\n        'Goal progress documented',\n        'Barriers to progress identified',\n        'Plan modifications noted',\n        'Person-served response documented',\n        'Safety incidents reported',\n        'Interdisciplinary communication noted'\n      ],\n      timeframe: 'Each session',\n      responsible: 'Service Provider',\n      category: 'Progress'\n    }\n  },\n\n  // CARF Quality Indicators\n  QUALITY_INDICATORS: {\n    SATISFACTION: {\n      id: 'QI_SAT',\n      title: 'Person-Served Satisfaction',\n      description: 'Satisfaction measurement and improvement',\n      metrics: [\n        'Overall satisfaction score ≥ 85%',\n        'Service quality rating ≥ 4.0/5.0',\n        'Recommendation likelihood ≥ 80%',\n        'Communication effectiveness ≥ 85%',\n        'Cultural competency rating ≥ 85%'\n      ],\n      frequency: 'Quarterly',\n      target: '≥ 85%',\n      category: 'Satisfaction'\n    },\n    OUTCOMES: {\n      id: 'QI_OUT',\n      title: 'Treatment Outcomes',\n      description: 'Functional improvement and goal achievement',\n      metrics: [\n        'Goal achievement rate ≥ 80%',\n        'Functional improvement ≥ 70%',\n        'Discharge to community ≥ 85%',\n        'Readmission rate ≤ 10%',\n        'Length of stay within targets'\n      ],\n      frequency: 'Monthly',\n      target: 'Varies by metric',\n      category: 'Outcomes'\n    },\n    EFFICIENCY: {\n      id: 'QI_EFF',\n      title: 'Service Efficiency',\n      description: 'Timely and efficient service delivery',\n      metrics: [\n        'Assessment completion within 72 hours',\n        'Treatment plan within 30 days',\n        'Service initiation within 14 days',\n        'Discharge planning within timeframes',\n        'Documentation completion rates ≥ 95%'\n      ],\n      frequency: 'Monthly',\n      target: '≥ 95%',\n      category: 'Efficiency'\n    }\n  },\n\n  // CARF Outcome Measures\n  OUTCOME_MEASURES: {\n    FUNCTIONAL: {\n      id: 'OM_FUNC',\n      title: 'Functional Independence Measure (FIM)',\n      description: 'Standardized functional assessment tool',\n      domains: [\n        'Self-Care',\n        'Sphincter Control',\n        'Transfers',\n        'Locomotion',\n        'Communication',\n        'Social Cognition'\n      ],\n      scale: '1-7 (1=Total Assist, 7=Complete Independence)',\n      frequency: 'Admission, Discharge, Follow-up',\n      category: 'Functional'\n    },\n    QUALITY_OF_LIFE: {\n      id: 'OM_QOL',\n      title: 'Quality of Life Measures',\n      description: 'Person-centered quality of life assessment',\n      domains: [\n        'Physical Health',\n        'Psychological Well-being',\n        'Social Relationships',\n        'Environmental Factors',\n        'Personal Satisfaction',\n        'Community Integration'\n      ],\n      scale: 'Standardized QOL instruments',\n      frequency: 'Quarterly',\n      category: 'Quality of Life'\n    },\n    PARTICIPATION: {\n      id: 'OM_PART',\n      title: 'Community Participation',\n      description: 'Community integration and participation measures',\n      domains: [\n        'Employment/Education',\n        'Social Activities',\n        'Community Mobility',\n        'Independent Living',\n        'Recreation/Leisure',\n        'Civic Participation'\n      ],\n      scale: 'Participation frequency and satisfaction',\n      frequency: 'Discharge, 3-month, 6-month follow-up',\n      category: 'Participation'\n    }\n  },\n\n  // CARF Risk Management\n  RISK_MANAGEMENT: {\n    SAFETY: {\n      id: 'RM_SAFETY',\n      title: 'Safety Risk Assessment',\n      description: 'Comprehensive safety risk evaluation',\n      areas: [\n        'Fall risk assessment',\n        'Medication safety',\n        'Equipment safety',\n        'Environmental hazards',\n        'Behavioral risks',\n        'Medical complications'\n      ],\n      frequency: 'Admission and ongoing',\n      category: 'Safety'\n    },\n    INCIDENTS: {\n      id: 'RM_INC',\n      title: 'Incident Reporting',\n      description: 'Systematic incident tracking and analysis',\n      types: [\n        'Falls',\n        'Medication errors',\n        'Equipment failures',\n        'Behavioral incidents',\n        'Medical emergencies',\n        'Rights violations'\n      ],\n      timeframe: 'Immediate reporting',\n      category: 'Incidents'\n    }\n  },\n\n  // CARF Performance Improvement\n  PERFORMANCE_IMPROVEMENT: {\n    DATA_COLLECTION: {\n      id: 'PI_DATA',\n      title: 'Data Collection and Analysis',\n      description: 'Systematic data collection for improvement',\n      requirements: [\n        'Regular data collection protocols',\n        'Statistical analysis methods',\n        'Trend identification',\n        'Benchmark comparisons',\n        'Root cause analysis',\n        'Action plan development'\n      ],\n      frequency: 'Ongoing',\n      category: 'Data'\n    },\n    CORRECTIVE_ACTION: {\n      id: 'PI_ACTION',\n      title: 'Corrective Action Plans',\n      description: 'Systematic approach to addressing deficiencies',\n      components: [\n        'Problem identification',\n        'Root cause analysis',\n        'Action plan development',\n        'Implementation timeline',\n        'Responsibility assignment',\n        'Monitoring and evaluation'\n      ],\n      timeframe: 'As needed',\n      category: 'Improvement'\n    }\n  }\n};\n\n// CARF Compliance Tracking\nexport const CARF_COMPLIANCE_LEVELS = {\n  FULL_COMPLIANCE: {\n    score: 100,\n    label: 'Full Compliance',\n    color: 'green',\n    description: 'Meets all CARF standards'\n  },\n  SUBSTANTIAL_COMPLIANCE: {\n    score: 85,\n    label: 'Substantial Compliance',\n    color: 'yellow',\n    description: 'Minor areas for improvement'\n  },\n  PARTIAL_COMPLIANCE: {\n    score: 70,\n    label: 'Partial Compliance',\n    color: 'orange',\n    description: 'Significant improvements needed'\n  },\n  NON_COMPLIANCE: {\n    score: 0,\n    label: 'Non-Compliance',\n    color: 'red',\n    description: 'Major deficiencies identified'\n  }\n};\n\n// CARF Assessment Tools\nexport const CARF_ASSESSMENT_TOOLS = {\n  FIM: {\n    name: 'Functional Independence Measure',\n    domains: ['Motor', 'Cognitive'],\n    items: 18,\n    scale: '1-7',\n    administration: 'Trained clinician'\n  },\n  CARE: {\n    name: 'Comprehensive Assessment of Rehabilitation Environments',\n    purpose: 'Environmental assessment',\n    domains: ['Physical', 'Social', 'Cultural'],\n    frequency: 'Annual'\n  },\n  SATISFACTION_SURVEY: {\n    name: 'Person-Served Satisfaction Survey',\n    purpose: 'Satisfaction measurement',\n    frequency: 'Quarterly',\n    method: 'Survey or interview'\n  }\n};\n\n// CARF Documentation Templates\nexport const CARF_TEMPLATES = {\n  ASSESSMENT: {\n    sections: [\n      'Demographic Information',\n      'Medical History',\n      'Functional Assessment',\n      'Psychosocial Assessment',\n      'Environmental Assessment',\n      'Risk Assessment',\n      'Goals and Preferences'\n    ]\n  },\n  TREATMENT_PLAN: {\n    sections: [\n      'Assessment Summary',\n      'Long-term Goals',\n      'Short-term Objectives',\n      'Service Plan',\n      'Discharge Criteria',\n      'Person-Served Input',\n      'Team Signatures'\n    ]\n  },\n  PROGRESS_NOTE: {\n    sections: [\n      'Session Information',\n      'Objective Measurements',\n      'Goal Progress',\n      'Interventions Provided',\n      'Person Response',\n      'Plan Modifications',\n      'Next Session Plan'\n    ]\n  }\n};\n\nexport default CARF_STANDARDS;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { CARF_STANDARDS, CARF_COMPLIANCE_LEVELS } from '../../utils/carfStandards';\n\nconst CARFComplianceDashboard = () => {\n  const { t, isRTL } = useLanguage();\n  const [complianceData, setComplianceData] = useState({});\n  const [selectedStandard, setSelectedStandard] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Mock compliance data\n  useEffect(() => {\n    const mockData = {\n      overall: {\n        score: 87,\n        level: 'SUBSTANTIAL_COMPLIANCE',\n        lastAssessment: '2024-01-15',\n        nextAssessment: '2024-07-15'\n      },\n      categories: {\n        documentation: { score: 92, status: 'compliant', issues: 2 },\n        qualityIndicators: { score: 85, status: 'substantial', issues: 3 },\n        outcomeMeasures: { score: 88, status: 'compliant', issues: 1 },\n        riskManagement: { score: 90, status: 'compliant', issues: 1 },\n        performanceImprovement: { score: 82, status: 'substantial', issues: 4 }\n      },\n      recentFindings: [\n        {\n          id: 1,\n          standard: 'DOC_PROGRESS',\n          finding: 'Progress notes missing objective measurements in 8% of cases',\n          severity: 'Minor',\n          dueDate: '2024-03-01',\n          status: 'In Progress'\n        },\n        {\n          id: 2,\n          standard: 'QI_SAT',\n          finding: 'Satisfaction survey response rate below target (78%)',\n          severity: 'Moderate',\n          dueDate: '2024-02-15',\n          status: 'Pending'\n        },\n        {\n          id: 3,\n          standard: 'RM_SAFETY',\n          finding: 'Fall risk assessment documentation incomplete',\n          severity: 'Minor',\n          dueDate: '2024-02-28',\n          status: 'Completed'\n        }\n      ]\n    };\n\n    setTimeout(() => {\n      setComplianceData(mockData);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const getComplianceLevel = (score) => {\n    if (score >= 95) return CARF_COMPLIANCE_LEVELS.FULL_COMPLIANCE;\n    if (score >= 85) return CARF_COMPLIANCE_LEVELS.SUBSTANTIAL_COMPLIANCE;\n    if (score >= 70) return CARF_COMPLIANCE_LEVELS.PARTIAL_COMPLIANCE;\n    return CARF_COMPLIANCE_LEVELS.NON_COMPLIANCE;\n  };\n\n  const ComplianceCard = ({ title, score, status, issues, icon, color }) => (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className=\"flex items-center space-x-3\">\n          <div className={`p-3 bg-${color}-100 dark:bg-${color}-900/30 rounded-lg`}>\n            <i className={`${icon} text-${color}-600 dark:text-${color}-400 text-xl`}></i>\n          </div>\n          <div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">{title}</h3>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {issues} {t('issuesIdentified', 'issues identified')}\n            </p>\n          </div>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">{score}%</div>\n          <div className={`text-sm font-medium ${\n            status === 'compliant' ? 'text-green-600' : \n            status === 'substantial' ? 'text-yellow-600' : 'text-red-600'\n          }`}>\n            {status === 'compliant' ? t('compliant', 'Compliant') :\n             status === 'substantial' ? t('substantial', 'Substantial') :\n             t('nonCompliant', 'Non-Compliant')}\n          </div>\n        </div>\n      </div>\n      <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n        <div \n          className={`bg-${color}-600 h-2 rounded-full transition-all duration-300`}\n          style={{ width: `${score}%` }}\n        ></div>\n      </div>\n    </div>\n  );\n\n  const FindingCard = ({ finding, onViewDetails }) => (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-4\">\n      <div className=\"flex items-start justify-between\">\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <span className={`px-2 py-1 rounded text-xs font-medium ${\n              finding.severity === 'Minor' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :\n              finding.severity === 'Moderate' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :\n              'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'\n            }`}>\n              {finding.severity}\n            </span>\n            <span className={`px-2 py-1 rounded text-xs font-medium ${\n              finding.status === 'Completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :\n              finding.status === 'In Progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :\n              'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'\n            }`}>\n              {finding.status}\n            </span>\n          </div>\n          <p className=\"text-gray-900 dark:text-white font-medium mb-1\">{finding.finding}</p>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {t('dueDate', 'Due Date')}: {new Date(finding.dueDate).toLocaleDateString()}\n          </p>\n        </div>\n        <button\n          onClick={() => onViewDetails(finding)}\n          className=\"ml-4 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n        >\n          {t('viewDetails', 'View Details')}\n        </button>\n      </div>\n    </div>\n  );\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  const overallLevel = getComplianceLevel(complianceData.overall?.score || 0);\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('carfCompliance', 'CARF Compliance Dashboard')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('carfDescription', 'Monitor compliance with CARF accreditation standards')}\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportReport', 'Export Report')}\n          </button>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-sync mr-2\"></i>\n            {t('runAssessment', 'Run Assessment')}\n          </button>\n        </div>\n      </div>\n\n      {/* Overall Compliance */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {t('overallCompliance', 'Overall CARF Compliance')}\n          </h2>\n          <div className=\"text-right\">\n            <div className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {complianceData.overall?.score}%\n            </div>\n            <div className={`text-sm font-medium text-${overallLevel.color}-600`}>\n              {overallLevel.label}\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n          <div>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('lastAssessment', 'Last Assessment')}\n            </p>\n            <p className=\"font-medium text-gray-900 dark:text-white\">\n              {new Date(complianceData.overall?.lastAssessment).toLocaleDateString()}\n            </p>\n          </div>\n          <div>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-1\">\n              {t('nextAssessment', 'Next Assessment')}\n            </p>\n            <p className=\"font-medium text-gray-900 dark:text-white\">\n              {new Date(complianceData.overall?.nextAssessment).toLocaleDateString()}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4\">\n          <div \n            className={`bg-${overallLevel.color}-600 h-4 rounded-full transition-all duration-500`}\n            style={{ width: `${complianceData.overall?.score}%` }}\n          ></div>\n        </div>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n          {overallLevel.description}\n        </p>\n      </div>\n\n      {/* Compliance Categories */}\n      <div>\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('complianceCategories', 'Compliance by Category')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <ComplianceCard\n            title={t('documentation', 'Documentation')}\n            score={complianceData.categories?.documentation?.score}\n            status={complianceData.categories?.documentation?.status}\n            issues={complianceData.categories?.documentation?.issues}\n            icon=\"fas fa-file-alt\"\n            color=\"blue\"\n          />\n          <ComplianceCard\n            title={t('qualityIndicators', 'Quality Indicators')}\n            score={complianceData.categories?.qualityIndicators?.score}\n            status={complianceData.categories?.qualityIndicators?.status}\n            issues={complianceData.categories?.qualityIndicators?.issues}\n            icon=\"fas fa-chart-line\"\n            color=\"green\"\n          />\n          <ComplianceCard\n            title={t('outcomeMeasures', 'Outcome Measures')}\n            score={complianceData.categories?.outcomeMeasures?.score}\n            status={complianceData.categories?.outcomeMeasures?.status}\n            issues={complianceData.categories?.outcomeMeasures?.issues}\n            icon=\"fas fa-bullseye\"\n            color=\"purple\"\n          />\n          <ComplianceCard\n            title={t('riskManagement', 'Risk Management')}\n            score={complianceData.categories?.riskManagement?.score}\n            status={complianceData.categories?.riskManagement?.status}\n            issues={complianceData.categories?.riskManagement?.issues}\n            icon=\"fas fa-shield-alt\"\n            color=\"orange\"\n          />\n          <ComplianceCard\n            title={t('performanceImprovement', 'Performance Improvement')}\n            score={complianceData.categories?.performanceImprovement?.score}\n            status={complianceData.categories?.performanceImprovement?.status}\n            issues={complianceData.categories?.performanceImprovement?.issues}\n            icon=\"fas fa-trending-up\"\n            color=\"indigo\"\n          />\n        </div>\n      </div>\n\n      {/* Recent Findings */}\n      <div>\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('recentFindings', 'Recent Compliance Findings')}\n        </h2>\n        <div className=\"space-y-4\">\n          {complianceData.recentFindings?.map(finding => (\n            <FindingCard\n              key={finding.id}\n              finding={finding}\n              onViewDetails={(finding) => setSelectedStandard(finding)}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CARFComplianceDashboard;\n", "import React from 'react';\nimport CARFComplianceDashboard from '../../components/CARF/CARFComplianceDashboard';\n\nconst CARFDashboard = () => {\n  return <CARFComplianceDashboard />;\n};\n\nexport default CARFDashboard;\n"], "names": ["CARF_COMPLIANCE_LEVELS", "FULL_COMPLIANCE", "score", "label", "color", "description", "SUBSTANTIAL_COMPLIANCE", "PARTIAL_COMPLIANCE", "NON_COMPLIANCE", "CARFComplianceDashboard", "_complianceData$overa", "_complianceData$overa2", "_complianceData$overa3", "_complianceData$overa4", "_complianceData$overa5", "_complianceData$categ", "_complianceData$categ2", "_complianceData$categ3", "_complianceData$categ4", "_complianceData$categ5", "_complianceData$categ6", "_complianceData$categ7", "_complianceData$categ8", "_complianceData$categ9", "_complianceData$categ0", "_complianceData$categ1", "_complianceData$categ10", "_complianceData$categ11", "_complianceData$categ12", "_complianceData$categ13", "_complianceData$categ14", "_complianceData$categ15", "_complianceData$categ16", "_complianceData$categ17", "_complianceData$categ18", "_complianceData$categ19", "_complianceData$categ20", "_complianceData$categ21", "_complianceData$categ22", "_complianceData$categ23", "_complianceData$categ24", "_complianceData$categ25", "_complianceData$categ26", "_complianceData$categ27", "_complianceData$categ28", "_complianceData$recen", "t", "isRTL", "useLanguage", "complianceData", "setComplianceData", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedStandard", "loading", "setLoading", "useEffect", "mockData", "overall", "level", "lastAssessment", "nextAssessment", "categories", "documentation", "status", "issues", "qualityIndicators", "outcomeMeasures", "riskManagement", "performanceImprovement", "recentFindings", "id", "standard", "finding", "severity", "dueDate", "setTimeout", "ComplianceCard", "_ref", "title", "icon", "_jsxs", "className", "children", "_jsx", "concat", "style", "width", "FindingCard", "_ref2", "onViewDetails", "Date", "toLocaleDateString", "onClick", "overallLevel", "map", "CARFDashboard"], "sourceRoot": ""}