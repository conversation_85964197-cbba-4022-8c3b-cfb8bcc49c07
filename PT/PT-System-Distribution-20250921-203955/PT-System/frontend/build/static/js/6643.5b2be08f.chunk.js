"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6643],{6643:(e,r,a)=>{a.d(r,{A:()=>s});var t=a(2555),i=a(5043),l=a(7921),n=a(579);const s=e=>{let{onPainPointSelect:r,selectedPainPoints:a=[],readonly:s=!1}=e;const{t:d,isRTL:x}=(0,l.o)(),[c,m]=(0,i.useState)(null),[o,h]=(0,i.useState)(a),y=[{level:1,color:"#FEF3C7",label:"Mild"},{level:2,color:"#FDE68A",label:"Mild-Moderate"},{level:3,color:"#FCD34D",label:"Moderate"},{level:4,color:"#FBBF24",label:"Moderate-Severe"},{level:5,color:"#F59E0B",label:"Severe"},{level:6,color:"#D97706",label:"Very Severe"},{level:7,color:"#B45309",label:"Extreme"},{level:8,color:"#92400E",label:"Unbearable"},{level:9,color:"#78350F",label:"Maximum"},{level:10,color:"#451A03",label:"Worst Possible"}];return(0,i.useEffect)(()=>{h(a)},[a]),(0,n.jsx)("div",{className:"w-full",children:(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-6",children:[(0,n.jsx)("div",{className:"flex justify-center mb-4",children:(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:d("clinicalBodyMap","Clinical Body Pain Assessment")})}),!s&&(0,n.jsx)("div",{className:"mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:(0,n.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:d("bodyMapInstructions","Click on numbered body regions to mark pain. Click repeatedly to increase pain intensity (1-10). Click at level 10 to remove.")})}),(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsxs)("svg",{width:"700",height:"600",viewBox:"0 0 700 600",className:"border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800",children:[(0,n.jsxs)("g",{id:"front-view",children:[(0,n.jsx)("text",{x:"175",y:"25",textAnchor:"middle",className:"fill-gray-700 dark:fill-gray-300 text-lg font-bold",children:d("frontView","ANTERIOR")}),(0,n.jsxs)("g",{fill:"#F7FAFC",stroke:"#2D3748",strokeWidth:"1.5",children:[(0,n.jsx)("path",{d:"M 175 35 C 185 35, 195 40, 200 50 C 205 60, 200 70, 195 75 C 185 80, 165 80, 155 75 C 150 70, 145 60, 150 50 C 155 40, 165 35, 175 35 Z"}),(0,n.jsx)("path",{d:"M 165 75 L 165 90 L 185 90 L 185 75"}),(0,n.jsx)("path",{d:"M 140 90 C 130 95, 125 100, 125 110 L 125 130 C 125 140, 130 150, 140 155 L 150 160 L 200 160 L 210 155 C 220 150, 225 140, 225 130 L 225 110 C 225 100, 220 95, 210 90 Z"}),(0,n.jsx)("path",{d:"M 150 160 L 145 180 L 140 220 L 138 260 L 140 300 L 145 320 L 155 330 L 195 330 L 205 320 L 210 300 L 212 260 L 210 220 L 205 180 L 200 160 Z"}),(0,n.jsxs)("g",{id:"right-arm",children:[(0,n.jsx)("path",{d:"M 125 110 C 115 115, 105 120, 100 130 L 95 150 L 90 170 C 88 180, 90 185, 95 188 L 105 190 C 110 188, 115 185, 118 180 L 125 160 L 130 140 C 132 130, 130 120, 125 110 Z"}),(0,n.jsx)("path",{d:"M 95 188 C 85 192, 75 200, 70 210 L 65 230 L 60 250 C 58 260, 60 265, 65 268 L 75 270 C 80 268, 85 265, 88 260 L 95 240 L 100 220 C 102 210, 100 200, 95 188 Z"}),(0,n.jsx)("ellipse",{cx:"62",cy:"275",rx:"12",ry:"8"})]}),(0,n.jsxs)("g",{id:"left-arm",children:[(0,n.jsx)("path",{d:"M 225 110 C 235 115, 245 120, 250 130 L 255 150 L 260 170 C 262 180, 260 185, 255 188 L 245 190 C 240 188, 235 185, 232 180 L 225 160 L 220 140 C 218 130, 220 120, 225 110 Z"}),(0,n.jsx)("path",{d:"M 255 188 C 265 192, 275 200, 280 210 L 285 230 L 290 250 C 292 260, 290 265, 285 268 L 275 270 C 270 268, 265 265, 262 260 L 255 240 L 250 220 C 248 210, 250 200, 255 188 Z"}),(0,n.jsx)("ellipse",{cx:"288",cy:"275",rx:"12",ry:"8"})]}),(0,n.jsxs)("g",{id:"right-leg",children:[(0,n.jsx)("path",{d:"M 155 330 L 150 350 L 145 390 L 140 430 C 138 440, 140 445, 145 448 L 155 450 C 160 448, 165 445, 168 440 L 170 400 L 175 360 L 180 330 Z"}),(0,n.jsx)("path",{d:"M 145 448 L 140 470 L 135 510 L 130 540 C 128 550, 130 555, 135 558 L 145 560 C 150 558, 155 555, 158 550 L 160 520 L 165 480 L 168 448 Z"}),(0,n.jsx)("ellipse",{cx:"142",cy:"570",rx:"15",ry:"8"})]}),(0,n.jsxs)("g",{id:"left-leg",children:[(0,n.jsx)("path",{d:"M 195 330 L 200 350 L 205 390 L 210 430 C 212 440, 210 445, 205 448 L 195 450 C 190 448, 185 445, 182 440 L 180 400 L 175 360 L 170 330 Z"}),(0,n.jsx)("path",{d:"M 205 448 L 210 470 L 215 510 L 220 540 C 222 550, 220 555, 215 558 L 205 560 C 200 558, 195 555, 192 550 L 190 520 L 185 480 L 182 448 Z"}),(0,n.jsx)("ellipse",{cx:"208",cy:"570",rx:"15",ry:"8"})]})]})]}),(0,n.jsxs)("g",{id:"back-view",children:[(0,n.jsx)("text",{x:"525",y:"25",textAnchor:"middle",className:"fill-gray-700 dark:fill-gray-300 text-lg font-bold",children:d("backView","POSTERIOR")}),(0,n.jsxs)("g",{fill:"#F7FAFC",stroke:"#2D3748",strokeWidth:"1.5",children:[(0,n.jsx)("path",{d:"M 525 35 C 535 35, 545 40, 550 50 C 555 60, 550 70, 545 75 C 535 80, 515 80, 505 75 C 500 70, 495 60, 500 50 C 505 40, 515 35, 525 35 Z"}),(0,n.jsx)("path",{d:"M 515 75 L 515 90 L 535 90 L 535 75"}),(0,n.jsx)("path",{d:"M 490 90 C 480 95, 475 100, 475 110 L 475 130 C 475 140, 480 150, 490 155 L 500 160 L 550 160 L 560 155 C 570 150, 575 140, 575 130 L 575 110 C 575 100, 570 95, 560 90 Z"}),(0,n.jsx)("path",{d:"M 500 160 L 495 180 L 490 220 L 488 260 L 490 300 L 495 320 L 505 330 L 545 330 L 555 320 L 560 300 L 562 260 L 560 220 L 555 180 L 550 160 Z"}),(0,n.jsx)("line",{x1:"525",y1:"90",x2:"525",y2:"330",stroke:"#4A5568",strokeWidth:"2"}),(0,n.jsx)("circle",{cx:"525",cy:"110",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"130",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"150",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"170",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"190",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"210",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"230",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"250",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"270",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"290",r:"2",fill:"#4A5568"}),(0,n.jsx)("circle",{cx:"525",cy:"310",r:"2",fill:"#4A5568"}),(0,n.jsxs)("g",{id:"back-right-arm",children:[(0,n.jsx)("path",{d:"M 475 110 C 465 115, 455 120, 450 130 L 445 150 L 440 170 C 438 180, 440 185, 445 188 L 455 190 C 460 188, 465 185, 468 180 L 475 160 L 480 140 C 482 130, 480 120, 475 110 Z"}),(0,n.jsx)("path",{d:"M 445 188 C 435 192, 425 200, 420 210 L 415 230 L 410 250 C 408 260, 410 265, 415 268 L 425 270 C 430 268, 435 265, 438 260 L 445 240 L 450 220 C 452 210, 450 200, 445 188 Z"})]}),(0,n.jsxs)("g",{id:"back-left-arm",children:[(0,n.jsx)("path",{d:"M 575 110 C 585 115, 595 120, 600 130 L 605 150 L 610 170 C 612 180, 610 185, 605 188 L 595 190 C 590 188, 585 185, 582 180 L 575 160 L 570 140 C 568 130, 570 120, 575 110 Z"}),(0,n.jsx)("path",{d:"M 605 188 C 615 192, 625 200, 630 210 L 635 230 L 640 250 C 642 260, 640 265, 635 268 L 625 270 C 620 268, 615 265, 612 260 L 605 240 L 600 220 C 598 210, 600 200, 605 188 Z"})]}),(0,n.jsxs)("g",{id:"back-right-leg",children:[(0,n.jsx)("path",{d:"M 505 330 L 500 350 L 495 390 L 490 430 C 488 440, 490 445, 495 448 L 505 450 C 510 448, 515 445, 518 440 L 520 400 L 525 360 L 530 330 Z"}),(0,n.jsx)("path",{d:"M 495 448 L 490 470 L 485 510 L 480 540 C 478 550, 480 555, 485 558 L 495 560 C 500 558, 505 555, 508 550 L 510 520 L 515 480 L 518 448 Z"}),(0,n.jsx)("ellipse",{cx:"492",cy:"570",rx:"15",ry:"8"})]}),(0,n.jsxs)("g",{id:"back-left-leg",children:[(0,n.jsx)("path",{d:"M 545 330 L 550 350 L 555 390 L 560 430 C 562 440, 560 445, 555 448 L 545 450 C 540 448, 535 445, 532 440 L 530 400 L 525 360 L 520 330 Z"}),(0,n.jsx)("path",{d:"M 555 448 L 560 470 L 565 510 L 570 540 C 572 550, 570 555, 565 558 L 555 560 C 550 558, 545 555, 542 550 L 540 520 L 535 480 L 532 448 Z"}),(0,n.jsx)("ellipse",{cx:"558",cy:"570",rx:"15",ry:"8"})]})]})]}),Object.entries({head:{id:1,name:"Head",nameAr:"\u0627\u0644\u0631\u0623\u0633",x:175,y:55,area:"head-neck"},neck:{id:2,name:"Neck",nameAr:"\u0627\u0644\u0631\u0642\u0628\u0629",x:175,y:82,area:"head-neck"},rightShoulder:{id:3,name:"Right Shoulder",nameAr:"\u0627\u0644\u0643\u062a\u0641 \u0627\u0644\u0623\u064a\u0645\u0646",x:140,y:105,area:"upper-extremity"},rightUpperArm:{id:4,name:"Right Upper Arm",nameAr:"\u0627\u0644\u0630\u0631\u0627\u0639 \u0627\u0644\u0623\u064a\u0645\u0646 \u0627\u0644\u0639\u0644\u0648\u064a",x:112,y:150,area:"upper-extremity"},rightElbow:{id:5,name:"Right Elbow",nameAr:"\u0627\u0644\u0643\u0648\u0639 \u0627\u0644\u0623\u064a\u0645\u0646",x:95,y:189,area:"upper-extremity"},rightForearm:{id:6,name:"Right Forearm",nameAr:"\u0627\u0644\u0633\u0627\u0639\u062f \u0627\u0644\u0623\u064a\u0645\u0646",x:78,y:230,area:"upper-extremity"},rightWrist:{id:7,name:"Right Wrist",nameAr:"\u0627\u0644\u0631\u0633\u063a \u0627\u0644\u0623\u064a\u0645\u0646",x:65,y:268,area:"upper-extremity"},rightHand:{id:8,name:"Right Hand",nameAr:"\u0627\u0644\u064a\u062f \u0627\u0644\u064a\u0645\u0646\u0649",x:62,y:275,area:"upper-extremity"},leftShoulder:{id:9,name:"Left Shoulder",nameAr:"\u0627\u0644\u0643\u062a\u0641 \u0627\u0644\u0623\u064a\u0633\u0631",x:210,y:105,area:"upper-extremity"},leftUpperArm:{id:10,name:"Left Upper Arm",nameAr:"\u0627\u0644\u0630\u0631\u0627\u0639 \u0627\u0644\u0623\u064a\u0633\u0631 \u0627\u0644\u0639\u0644\u0648\u064a",x:238,y:150,area:"upper-extremity"},leftElbow:{id:11,name:"Left Elbow",nameAr:"\u0627\u0644\u0643\u0648\u0639 \u0627\u0644\u0623\u064a\u0633\u0631",x:255,y:189,area:"upper-extremity"},leftForearm:{id:12,name:"Left Forearm",nameAr:"\u0627\u0644\u0633\u0627\u0639\u062f \u0627\u0644\u0623\u064a\u0633\u0631",x:272,y:230,area:"upper-extremity"},leftWrist:{id:13,name:"Left Wrist",nameAr:"\u0627\u0644\u0631\u0633\u063a \u0627\u0644\u0623\u064a\u0633\u0631",x:285,y:268,area:"upper-extremity"},leftHand:{id:14,name:"Left Hand",nameAr:"\u0627\u0644\u064a\u062f \u0627\u0644\u064a\u0633\u0631\u0649",x:288,y:275,area:"upper-extremity"},upperChest:{id:15,name:"Upper Chest",nameAr:"\u0623\u0639\u0644\u0649 \u0627\u0644\u0635\u062f\u0631",x:175,y:125,area:"trunk"},lowerChest:{id:16,name:"Lower Chest",nameAr:"\u0623\u0633\u0641\u0644 \u0627\u0644\u0635\u062f\u0631",x:175,y:160,area:"trunk"},upperAbdomen:{id:17,name:"Upper Abdomen",nameAr:"\u0623\u0639\u0644\u0649 \u0627\u0644\u0628\u0637\u0646",x:175,y:200,area:"trunk"},lowerAbdomen:{id:18,name:"Lower Abdomen",nameAr:"\u0623\u0633\u0641\u0644 \u0627\u0644\u0628\u0637\u0646",x:175,y:240,area:"trunk"},pelvis:{id:19,name:"Pelvis",nameAr:"\u0627\u0644\u062d\u0648\u0636",x:175,y:280,area:"trunk"},rightHip:{id:20,name:"Right Hip",nameAr:"\u0627\u0644\u0648\u0631\u0643 \u0627\u0644\u0623\u064a\u0645\u0646",x:155,y:315,area:"lower-extremity"},rightThigh:{id:21,name:"Right Thigh",nameAr:"\u0627\u0644\u0641\u062e\u0630 \u0627\u0644\u0623\u064a\u0645\u0646",x:155,y:370,area:"lower-extremity"},rightKnee:{id:22,name:"Right Knee",nameAr:"\u0627\u0644\u0631\u0643\u0628\u0629 \u0627\u0644\u064a\u0645\u0646\u0649",x:152,y:420,area:"lower-extremity"},rightCalf:{id:23,name:"Right Calf",nameAr:"\u0631\u0628\u0644\u0629 \u0627\u0644\u0633\u0627\u0642 \u0627\u0644\u064a\u0645\u0646\u0649",x:148,y:480,area:"lower-extremity"},rightAnkle:{id:24,name:"Right Ankle",nameAr:"\u0627\u0644\u0643\u0627\u062d\u0644 \u0627\u0644\u0623\u064a\u0645\u0646",x:145,y:530,area:"lower-extremity"},rightFoot:{id:25,name:"Right Foot",nameAr:"\u0627\u0644\u0642\u062f\u0645 \u0627\u0644\u064a\u0645\u0646\u0649",x:142,y:570,area:"lower-extremity"},leftHip:{id:26,name:"Left Hip",nameAr:"\u0627\u0644\u0648\u0631\u0643 \u0627\u0644\u0623\u064a\u0633\u0631",x:195,y:315,area:"lower-extremity"},leftThigh:{id:27,name:"Left Thigh",nameAr:"\u0627\u0644\u0641\u062e\u0630 \u0627\u0644\u0623\u064a\u0633\u0631",x:195,y:370,area:"lower-extremity"},leftKnee:{id:28,name:"Left Knee",nameAr:"\u0627\u0644\u0631\u0643\u0628\u0629 \u0627\u0644\u064a\u0633\u0631\u0649",x:198,y:420,area:"lower-extremity"},leftCalf:{id:29,name:"Left Calf",nameAr:"\u0631\u0628\u0644\u0629 \u0627\u0644\u0633\u0627\u0642 \u0627\u0644\u064a\u0633\u0631\u0649",x:202,y:480,area:"lower-extremity"},leftAnkle:{id:30,name:"Left Ankle",nameAr:"\u0627\u0644\u0643\u0627\u062d\u0644 \u0627\u0644\u0623\u064a\u0633\u0631",x:205,y:530,area:"lower-extremity"},leftFoot:{id:31,name:"Left Foot",nameAr:"\u0627\u0644\u0642\u062f\u0645 \u0627\u0644\u064a\u0633\u0631\u0649",x:208,y:570,area:"lower-extremity"},headBack:{id:32,name:"Head (Back)",nameAr:"\u0627\u0644\u0631\u0623\u0633 (\u062e\u0644\u0641)",x:525,y:55,area:"head-neck"},neckBack:{id:33,name:"Neck (Back)",nameAr:"\u0627\u0644\u0631\u0642\u0628\u0629 (\u062e\u0644\u0641)",x:525,y:82,area:"head-neck"},cervicalSpine:{id:34,name:"Cervical Spine",nameAr:"\u0627\u0644\u0641\u0642\u0631\u0627\u062a \u0627\u0644\u0639\u0646\u0642\u064a\u0629",x:525,y:110,area:"spine"},thoracicSpine:{id:35,name:"Thoracic Spine",nameAr:"\u0627\u0644\u0641\u0642\u0631\u0627\u062a \u0627\u0644\u0635\u062f\u0631\u064a\u0629",x:525,y:170,area:"spine"},lumbarSpine:{id:36,name:"Lumbar Spine",nameAr:"\u0627\u0644\u0641\u0642\u0631\u0627\u062a \u0627\u0644\u0642\u0637\u0646\u064a\u0629",x:525,y:230,area:"spine"},sacrum:{id:37,name:"Sacrum",nameAr:"\u0627\u0644\u0639\u062c\u0632",x:525,y:290,area:"spine"},rightShoulderBack:{id:38,name:"Right Shoulder (Back)",nameAr:"\u0627\u0644\u0643\u062a\u0641 \u0627\u0644\u0623\u064a\u0645\u0646 (\u062e\u0644\u0641)",x:490,y:105,area:"upper-extremity"},leftShoulderBack:{id:39,name:"Left Shoulder (Back)",nameAr:"\u0627\u0644\u0643\u062a\u0641 \u0627\u0644\u0623\u064a\u0633\u0631 (\u062e\u0644\u0641)",x:560,y:105,area:"upper-extremity"},rightUpperArmBack:{id:40,name:"Right Upper Arm (Back)",nameAr:"\u0627\u0644\u0630\u0631\u0627\u0639 \u0627\u0644\u0623\u064a\u0645\u0646 \u0627\u0644\u0639\u0644\u0648\u064a (\u062e\u0644\u0641)",x:462,y:150,area:"upper-extremity"},leftUpperArmBack:{id:41,name:"Left Upper Arm (Back)",nameAr:"\u0627\u0644\u0630\u0631\u0627\u0639 \u0627\u0644\u0623\u064a\u0633\u0631 \u0627\u0644\u0639\u0644\u0648\u064a (\u062e\u0644\u0641)",x:588,y:150,area:"upper-extremity"},rightElbowBack:{id:42,name:"Right Elbow (Back)",nameAr:"\u0627\u0644\u0643\u0648\u0639 \u0627\u0644\u0623\u064a\u0645\u0646 (\u062e\u0644\u0641)",x:445,y:189,area:"upper-extremity"},leftElbowBack:{id:43,name:"Left Elbow (Back)",nameAr:"\u0627\u0644\u0643\u0648\u0639 \u0627\u0644\u0623\u064a\u0633\u0631 (\u062e\u0644\u0641)",x:605,y:189,area:"upper-extremity"},rightGluteal:{id:44,name:"Right Gluteal",nameAr:"\u0627\u0644\u0623\u0631\u062f\u0627\u0641 \u0627\u0644\u064a\u0645\u0646\u0649",x:505,y:315,area:"gluteal"},leftGluteal:{id:45,name:"Left Gluteal",nameAr:"\u0627\u0644\u0623\u0631\u062f\u0627\u0641 \u0627\u0644\u064a\u0633\u0631\u0649",x:545,y:315,area:"gluteal"},rightThighBack:{id:46,name:"Right Thigh (Back)",nameAr:"\u0627\u0644\u0641\u062e\u0630 \u0627\u0644\u0623\u064a\u0645\u0646 (\u062e\u0644\u0641)",x:505,y:370,area:"lower-extremity"},leftThighBack:{id:47,name:"Left Thigh (Back)",nameAr:"\u0627\u0644\u0641\u062e\u0630 \u0627\u0644\u0623\u064a\u0633\u0631 (\u062e\u0644\u0641)",x:545,y:370,area:"lower-extremity"},rightCalfBack:{id:48,name:"Right Calf (Back)",nameAr:"\u0631\u0628\u0644\u0629 \u0627\u0644\u0633\u0627\u0642 \u0627\u0644\u064a\u0645\u0646\u0649 (\u062e\u0644\u0641)",x:498,y:480,area:"lower-extremity"},leftCalfBack:{id:49,name:"Left Calf (Back)",nameAr:"\u0631\u0628\u0644\u0629 \u0627\u0644\u0633\u0627\u0642 \u0627\u0644\u064a\u0633\u0631\u0649 (\u062e\u0644\u0641)",x:552,y:480,area:"lower-extremity"},rightFootBack:{id:50,name:"Right Foot (Back)",nameAr:"\u0627\u0644\u0642\u062f\u0645 \u0627\u0644\u064a\u0645\u0646\u0649 (\u062e\u0644\u0641)",x:492,y:570,area:"lower-extremity"}}).map(e=>{let[a,i]=e;const l=(e=>{const r=o.find(r=>r.regionId===e);return(null===r||void 0===r?void 0:r.painLevel)||0})(i.id),d=(e=>{var r;const a=o.find(r=>r.regionId===e);return a&&(null===(r=y.find(e=>e.level===a.painLevel))||void 0===r?void 0:r.color)||"#F3F4F6"})(i.id);return(0,n.jsxs)("g",{children:[(0,n.jsx)("circle",{cx:i.x,cy:i.y,r:"12",fill:d,fillOpacity:"0.9",stroke:l>0?"#DC2626":"#2D3748",strokeWidth:l>0?"3":"2",className:"cursor-pointer transition-all duration-200 ".concat(s?"":"hover:stroke-blue-500 hover:stroke-4 hover:r-14"),onClick:()=>((e,a)=>{if(s)return;const i=o.findIndex(e=>e.regionId===a.id);if(i>=0){const e=o[i],l=e.painLevel<10?e.painLevel+1:0;if(0===l){const e=o.filter(e=>e.regionId!==a.id);h(e),null===r||void 0===r||r(e)}else{const e=o.map(e=>e.regionId===a.id?(0,t.A)((0,t.A)({},e),{},{painLevel:l}):e);h(e),null===r||void 0===r||r(e)}}else{const e={regionId:a.id,regionName:a.name,regionNameAr:a.nameAr,painLevel:1,anatomicalArea:a.area,timestamp:(new Date).toISOString()},t=[...o,e];h(t),null===r||void 0===r||r(t)}})(0,i),onMouseEnter:()=>m(i),onMouseLeave:()=>m(null)}),(0,n.jsx)("text",{x:i.x,y:i.y+4,textAnchor:"middle",className:"fill-gray-800 dark:fill-gray-200 text-sm font-bold pointer-events-none",style:{fontSize:"11px"},children:i.id}),l>0&&(0,n.jsx)("text",{x:i.x,y:i.y-20,textAnchor:"middle",className:"fill-red-600 dark:fill-red-400 text-sm font-bold",style:{fontSize:"13px"},children:l})]},a)})]})}),c&&(0,n.jsx)("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center",children:(0,n.jsxs)("span",{className:"text-sm font-medium text-blue-900 dark:text-blue-100",children:[(0,n.jsxs)("strong",{children:["#",c.id]})," - ",x?c.nameAr:c.name,(0,n.jsxs)("span",{className:"text-blue-600 dark:text-blue-300 ml-2",children:["(",c.area,")"]})]})}),(0,n.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsx)("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-2",children:d("painIntensityScale","Pain Intensity Scale (0-10)")}),(0,n.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full border border-gray-400 bg-gray-200"}),(0,n.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"0 - No Pain"})]}),y.slice(0,5).map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full border border-gray-400",style:{backgroundColor:e.color}}),(0,n.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:e.level})]},e.level)),(0,n.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full border border-gray-400",style:{backgroundColor:"#451A03"}}),(0,n.jsx)("span",{className:"text-gray-700 dark:text-gray-300",children:"10 - Worst"})]})]})]}),(0,n.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsx)("h4",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-2",children:d("anatomicalRegions","Anatomical Regions (50 Standardized Areas)")}),(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600 dark:text-gray-400",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Head/Neck (1-4)"}),(0,n.jsx)("br",{}),"Head, Neck"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Upper Extremities (5-20)"}),(0,n.jsx)("br",{}),"Shoulders, Arms, Hands"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Trunk (21-28)"}),(0,n.jsx)("br",{}),"Chest, Abdomen, Pelvis"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Lower Extremities (29-44)"}),(0,n.jsx)("br",{}),"Hips, Legs, Feet"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Spine (45-48)"}),(0,n.jsx)("br",{}),"Cervical to Sacrum"]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Gluteal (49-50)"}),(0,n.jsx)("br",{}),"Buttocks Region"]})]})]})]})})}}}]);
//# sourceMappingURL=6643.5b2be08f.chunk.js.map