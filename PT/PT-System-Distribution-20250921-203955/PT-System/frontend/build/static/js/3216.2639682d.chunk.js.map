{"version": 3, "file": "static/js/3216.2639682d.chunk.js", "mappings": "4MAIA,MA0XA,EA1X6BA,KAC3B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CACvCC,MAAO,CACLC,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,QAAQ,EACRC,WAAW,GAEbC,IAAK,CACHL,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,QAAQ,EACRC,WAAW,GAEbE,KAAM,CACJN,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,QAAQ,EACRC,WAAW,GAEbG,MAAO,CACLP,cAAc,EACdC,YAAY,EACZC,SAAS,EACTC,QAAQ,EACRC,WAAW,MAIRI,EAAaC,IAAkBX,EAAAA,EAAAA,UAAS,CAC7CY,WAAY,CACVC,SAAS,EACTC,MAAO,QACPC,IAAK,SAEPC,UAAW,CACTC,OAAQ,QACRC,UAAW,aAEbC,SAAU,KACVC,SAAU,SAGLC,EAASC,IAActB,EAAAA,EAAAA,WAAS,IAEvCuB,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAA4BC,YAyC5BC,EAAyBA,CAACC,EAAUC,EAAKC,KAC7ClB,EAAemB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAI,IACP,CAACH,IAAQI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACJD,EAAKH,IAAS,IACjB,CAACC,GAAMC,QAKPG,EAAoB,CACxB,CACEJ,IAAK,eACLK,MAAOtC,EAAE,eAAgB,gBACzBuC,YAAavC,EAAE,+BAAgC,4CAC/CwC,KAAM,uBAER,CACEP,IAAK,aACLK,MAAOtC,EAAE,aAAc,cACvBuC,YAAavC,EAAE,6BAA8B,8CAC7CwC,KAAM,oBAER,CACEP,IAAK,UACLK,MAAOtC,EAAE,UAAW,WACpBuC,YAAavC,EAAE,2BAA4B,yCAC3CwC,KAAM,sBAER,CACEP,IAAK,SACLK,MAAOtC,EAAE,SAAU,UACnBuC,YAAavC,EAAE,0BAA2B,0CAC1CwC,KAAM,cAER,CACEP,IAAK,YACLK,MAAOtC,EAAE,YAAa,aACtBuC,YAAavC,EAAE,6BAA8B,uCAC7CwC,KAAM,oBAIJC,EAAW,CACf,CACER,IAAK,QACLK,MAAOtC,EAAE,QAAS,SAClBwC,KAAM,kBACND,YAAavC,EAAE,mBAAoB,oCAErC,CACEiC,IAAK,MACLK,MAAOtC,EAAE,MAAO,OAChBwC,KAAM,aACND,YAAavC,EAAE,iBAAkB,2CAEnC,CACEiC,IAAK,OACLK,MAAOtC,EAAE,OAAQ,QACjBwC,KAAM,cACND,YAAavC,EAAE,kBAAmB,8CAEpC,CACEiC,IAAK,QACLK,MAAOtC,EAAE,QAAS,UAClBwC,KAAM,iBACND,YAAavC,EAAE,mBAAoB,+CAIvC,OACE0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wEAAuEC,SAAA,EACpFF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D5C,EAAE,uBAAwB,4BAE7B6C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5C,EAAE,2BAA4B,0DAInC6C,EAAAA,EAAAA,KAAA,UACEC,QAjH2BhB,UACjCH,GAAW,GACX,IAQEoB,EAAAA,GAAMC,QAAQhD,EAAE,kBAAmB,8CACrC,CAAE,MAAOiD,GACPC,QAAQD,MAAM,wCAAyCA,GACvDF,EAAAA,GAAME,MAAMjD,EAAE,sBAAuB,0CACvC,CAAC,QACC2B,GAAW,EACb,GAkGMwB,SAAUzB,EACViB,UAAU,mHAAkHC,SAE3HlB,GACCgB,EAAAA,EAAAA,MAAAU,EAAAA,SAAA,CAAAR,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZ3C,EAAE,SAAU,iBAGf0C,EAAAA,EAAAA,MAAAU,EAAAA,SAAA,CAAAR,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ3C,EAAE,eAAgB,0BAO3B0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8DAA6DC,SAAA,EAC1EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0DAAyDC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE5C,EAAE,uBAAwB,4BAE7B6C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzD5C,EAAE,sBAAuB,yEAI9B6C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G5C,EAAE,mBAAoB,uBAExByC,EAASY,IAAKC,IACbT,EAAAA,EAAAA,KAAA,MAAsBF,UAAU,sGAAqGC,UACnIF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAY,OAAKD,EAAQd,KAAI,WAC5Bc,EAAQhB,UAHJgB,EAAQrB,YASvBY,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFP,EAAkBgB,IAAKG,IACtBd,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAY,OAAKC,EAAKhB,KAAI,uBAE5BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DY,EAAKlB,SAERO,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDY,EAAKjB,sBAKbE,EAASY,IAAKC,IACbT,EAAAA,EAAAA,KAAA,MAAsBF,UAAU,0CAAyCC,UACvEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,2BAA0BC,UACzCC,EAAAA,EAAAA,KAAA,SACEW,KAAK,WACLC,QAAStD,EAASmD,EAAQrB,KAAKuB,EAAKvB,KACpCyB,SAAUA,IApKNC,EAACL,EAASE,KACpCpD,EAAY+B,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACmB,IAAOlB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKmB,IAAQ,IAChB,CAACE,IAAQrB,EAAKmB,GAASE,SA+JWG,CAAoBL,EAAQrB,IAAKuB,EAAKvB,KACtDU,UAAU,kIANPW,EAAQrB,QAjBZuB,EAAKvB,iBAoCxBS,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE5C,EAAE,aAAc,kBAGnB0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEW,KAAK,WACLC,QAAS1C,EAAYE,WAAWC,QAChCwC,SAAWE,GAAM7B,EAAuB,aAAc,UAAW6B,EAAEC,OAAOJ,SAC1Ed,UAAU,gIAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,gDAA+CC,SAC5D5C,EAAE,mBAAoB,2BAI1Be,EAAYE,WAAWC,UACtBwB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5C,EAAE,YAAa,iBAElB6C,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOnB,EAAYE,WAAWE,MAC9BuC,SAAWE,GAAM7B,EAAuB,aAAc,QAAS6B,EAAEC,OAAO3B,OACxES,UAAU,kIAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5C,EAAE,UAAW,eAEhB6C,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLtB,MAAOnB,EAAYE,WAAWG,IAC9BsC,SAAWE,GAAM7B,EAAuB,aAAc,MAAO6B,EAAEC,OAAO3B,OACtES,UAAU,2IAStBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE5C,EAAE,oBAAqB,yBAG1B0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5C,EAAE,kBAAmB,uBAExB0C,EAAAA,EAAAA,MAAA,UACER,MAAOnB,EAAYM,UAAUC,OAC7BoC,SAAWE,GAAM7B,EAAuB,YAAa,SAAU6B,EAAEC,OAAO3B,OACxES,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,YAAWU,SAAE5C,EAAE,YAAa,gBAC1C6C,EAAAA,EAAAA,KAAA,UAAQX,MAAM,QAAOU,SAAE5C,EAAE,QAAS,YAClC6C,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQU,SAAE5C,EAAE,SAAU,aACpC6C,EAAAA,EAAAA,KAAA,UAAQX,MAAM,QAAOU,SAAE5C,EAAE,QAAS,kBAItC0C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5C,EAAE,oBAAqB,yBAE1B0C,EAAAA,EAAAA,MAAA,UACER,MAAOnB,EAAYM,UAAUE,UAC7BmC,SAAWE,GAAM7B,EAAuB,YAAa,YAAa6B,EAAEC,OAAO3B,OAC3ES,UAAU,4HAA2HC,SAAA,EAErIC,EAAAA,EAAAA,KAAA,UAAQX,MAAM,YAAWU,SAAE5C,EAAE,YAAa,gBAC1C6C,EAAAA,EAAAA,KAAA,UAAQX,MAAM,SAAQU,SAAE5C,EAAE,SAAU,aACpC6C,EAAAA,EAAAA,KAAA,UAAQX,MAAM,QAAOU,SAAE5C,EAAE,QAAS,2BAQ5C0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE5C,EAAE,oBAAqB,yBAE1B6C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5C,EAAE,wBAAyB,sDAG9B6C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCH,EAASY,IAAKC,IACbZ,EAAAA,EAAAA,MAAA,UAEEI,QAASA,IAAMC,EAAAA,GAAMC,QAAQ,QAADO,OAASD,EAAQhB,MAAK,wBAClDK,UAAU,qFAAoFC,SAAA,EAE9FC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAY,OAAKD,EAAQd,KAAI,WAC5BxC,EAAE,OAAQ,QAAQ,IAAEsD,EAAQhB,QALxBgB,EAAQrB,c", "sources": ["pages/Notifications/NotificationSettings.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst NotificationSettings = () => {\n  const { t, isRTL } = useLanguage();\n  const [settings, setSettings] = useState({\n    email: {\n      appointments: true,\n      treatments: true,\n      billing: true,\n      system: false,\n      marketing: false\n    },\n    sms: {\n      appointments: true,\n      treatments: false,\n      billing: true,\n      system: false,\n      marketing: false\n    },\n    push: {\n      appointments: true,\n      treatments: true,\n      billing: true,\n      system: true,\n      marketing: false\n    },\n    inApp: {\n      appointments: true,\n      treatments: true,\n      billing: true,\n      system: true,\n      marketing: true\n    }\n  });\n  \n  const [preferences, setPreferences] = useState({\n    quietHours: {\n      enabled: true,\n      start: '22:00',\n      end: '08:00'\n    },\n    frequency: {\n      digest: 'daily', // daily, weekly, never\n      reminders: 'immediate' // immediate, hourly, daily\n    },\n    language: 'en',\n    timezone: 'UTC'\n  });\n\n  const [loading, setLoading] = useState(false);\n\n  useEffect(() => {\n    fetchNotificationSettings();\n  }, []);\n\n  const fetchNotificationSettings = async () => {\n    try {\n      // Mock API call - replace with actual API\n      // const response = await fetch('/api/notifications/settings');\n      // const data = await response.json();\n      // setSettings(data.settings);\n      // setPreferences(data.preferences);\n    } catch (error) {\n      console.error('Error fetching notification settings:', error);\n    }\n  };\n\n  const updateNotificationSettings = async () => {\n    setLoading(true);\n    try {\n      // Mock API call - replace with actual API\n      // await fetch('/api/notifications/settings', {\n      //   method: 'PUT',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ settings, preferences })\n      // });\n      \n      toast.success(t('settingsUpdated', 'Notification settings updated successfully'));\n    } catch (error) {\n      console.error('Error updating notification settings:', error);\n      toast.error(t('settingsUpdateError', 'Failed to update notification settings'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChannelToggle = (channel, type) => {\n    setSettings(prev => ({\n      ...prev,\n      [channel]: {\n        ...prev[channel],\n        [type]: !prev[channel][type]\n      }\n    }));\n  };\n\n  const handlePreferenceChange = (category, key, value) => {\n    setPreferences(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [key]: value\n      }\n    }));\n  };\n\n  const notificationTypes = [\n    {\n      key: 'appointments',\n      label: t('appointments', 'Appointments'),\n      description: t('appointmentNotificationsDesc', 'Reminders and updates about appointments'),\n      icon: 'fas fa-calendar-alt'\n    },\n    {\n      key: 'treatments',\n      label: t('treatments', 'Treatments'),\n      description: t('treatmentNotificationsDesc', 'Updates about treatment plans and progress'),\n      icon: 'fas fa-heartbeat'\n    },\n    {\n      key: 'billing',\n      label: t('billing', 'Billing'),\n      description: t('billingNotificationsDesc', 'Payment reminders and billing updates'),\n      icon: 'fas fa-dollar-sign'\n    },\n    {\n      key: 'system',\n      label: t('system', 'System'),\n      description: t('systemNotificationsDesc', 'System maintenance and security alerts'),\n      icon: 'fas fa-cog'\n    },\n    {\n      key: 'marketing',\n      label: t('marketing', 'Marketing'),\n      description: t('marketingNotificationsDesc', 'Promotional content and newsletters'),\n      icon: 'fas fa-bullhorn'\n    }\n  ];\n\n  const channels = [\n    {\n      key: 'email',\n      label: t('email', 'Email'),\n      icon: 'fas fa-envelope',\n      description: t('emailChannelDesc', 'Receive notifications via email')\n    },\n    {\n      key: 'sms',\n      label: t('sms', 'SMS'),\n      icon: 'fas fa-sms',\n      description: t('smsChannelDesc', 'Receive notifications via text message')\n    },\n    {\n      key: 'push',\n      label: t('push', 'Push'),\n      icon: 'fas fa-bell',\n      description: t('pushChannelDesc', 'Receive push notifications on your device')\n    },\n    {\n      key: 'inApp',\n      label: t('inApp', 'In-App'),\n      icon: 'fas fa-desktop',\n      description: t('inAppChannelDesc', 'Show notifications within the application')\n    }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('notificationSettings', 'Notification Settings')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('notificationSettingsDesc', 'Customize how and when you receive notifications')}\n          </p>\n        </div>\n\n        <button\n          onClick={updateNotificationSettings}\n          disabled={loading}\n          className=\"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors mt-4 sm:mt-0\"\n        >\n          {loading ? (\n            <>\n              <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n              {t('saving', 'Saving...')}\n            </>\n          ) : (\n            <>\n              <i className=\"fas fa-save mr-2\"></i>\n              {t('saveSettings', 'Save Settings')}\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Notification Channels Matrix */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('notificationChannels', 'Notification Channels')}\n          </h2>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n            {t('channelsDescription', 'Choose how you want to receive different types of notifications')}\n          </p>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('notificationType', 'Notification Type')}\n                </th>\n                {channels.map((channel) => (\n                  <th key={channel.key} className=\"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    <div className=\"flex flex-col items-center\">\n                      <i className={`${channel.icon} mb-1`}></i>\n                      {channel.label}\n                    </div>\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              {notificationTypes.map((type) => (\n                <tr key={type.key}>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0\">\n                        <i className={`${type.icon} text-gray-400`}></i>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {type.label}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {type.description}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  {channels.map((channel) => (\n                    <td key={channel.key} className=\"px-6 py-4 whitespace-nowrap text-center\">\n                      <label className=\"inline-flex items-center\">\n                        <input\n                          type=\"checkbox\"\n                          checked={settings[channel.key][type.key]}\n                          onChange={() => handleChannelToggle(channel.key, type.key)}\n                          className=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                        />\n                      </label>\n                    </td>\n                  ))}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Preferences */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Quiet Hours */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('quietHours', 'Quiet Hours')}\n          </h3>\n          \n          <div className=\"space-y-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={preferences.quietHours.enabled}\n                onChange={(e) => handlePreferenceChange('quietHours', 'enabled', e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                {t('enableQuietHours', 'Enable quiet hours')}\n              </span>\n            </label>\n\n            {preferences.quietHours.enabled && (\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('startTime', 'Start Time')}\n                  </label>\n                  <input\n                    type=\"time\"\n                    value={preferences.quietHours.start}\n                    onChange={(e) => handlePreferenceChange('quietHours', 'start', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('endTime', 'End Time')}\n                  </label>\n                  <input\n                    type=\"time\"\n                    value={preferences.quietHours.end}\n                    onChange={(e) => handlePreferenceChange('quietHours', 'end', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Frequency Settings */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('frequencySettings', 'Frequency Settings')}\n          </h3>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('digestFrequency', 'Digest Frequency')}\n              </label>\n              <select\n                value={preferences.frequency.digest}\n                onChange={(e) => handlePreferenceChange('frequency', 'digest', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"immediate\">{t('immediate', 'Immediate')}</option>\n                <option value=\"daily\">{t('daily', 'Daily')}</option>\n                <option value=\"weekly\">{t('weekly', 'Weekly')}</option>\n                <option value=\"never\">{t('never', 'Never')}</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('reminderFrequency', 'Reminder Frequency')}\n              </label>\n              <select\n                value={preferences.frequency.reminders}\n                onChange={(e) => handlePreferenceChange('frequency', 'reminders', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"immediate\">{t('immediate', 'Immediate')}</option>\n                <option value=\"hourly\">{t('hourly', 'Hourly')}</option>\n                <option value=\"daily\">{t('daily', 'Daily')}</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Test Notifications */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('testNotifications', 'Test Notifications')}\n        </h3>\n        <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n          {t('testNotificationsDesc', 'Send test notifications to verify your settings')}\n        </p>\n        \n        <div className=\"flex flex-wrap gap-2\">\n          {channels.map((channel) => (\n            <button\n              key={channel.key}\n              onClick={() => toast.success(`Test ${channel.label} notification sent!`)}\n              className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors\"\n            >\n              <i className={`${channel.icon} mr-2`}></i>\n              {t('test', 'Test')} {channel.label}\n            </button>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NotificationSettings;\n"], "names": ["NotificationSettings", "t", "isRTL", "useLanguage", "settings", "setSettings", "useState", "email", "appointments", "treatments", "billing", "system", "marketing", "sms", "push", "inApp", "preferences", "setPreferences", "quietHours", "enabled", "start", "end", "frequency", "digest", "reminders", "language", "timezone", "loading", "setLoading", "useEffect", "fetchNotificationSettings", "async", "handlePreferenceChange", "category", "key", "value", "prev", "_objectSpread", "notificationTypes", "label", "description", "icon", "channels", "_jsxs", "className", "children", "_jsx", "onClick", "toast", "success", "error", "console", "disabled", "_Fragment", "map", "channel", "concat", "type", "checked", "onChange", "handleChannelToggle", "e", "target"], "sourceRoot": ""}