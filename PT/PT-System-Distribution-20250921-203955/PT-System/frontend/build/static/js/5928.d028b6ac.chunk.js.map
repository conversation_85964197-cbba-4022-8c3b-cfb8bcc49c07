{"version": 3, "file": "static/js/5928.d028b6ac.chunk.js", "mappings": "iMAIA,MA6CA,EA7CiBA,KACf,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KAElB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAS,6EAAAC,OAA+EJ,EAAQ,cAAgB,gBAAiBK,UACpIC,EAAAA,EAAAA,MAAA,OAAKH,UAAU,cAAaE,SAAA,EAC1BH,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qGAAoGE,UACjHH,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2EAGfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDE,SAAC,SAEtEH,EAAAA,EAAAA,KAAA,MAAIC,UAAU,+DAA8DE,SACzEL,EAAQ,+FAAsB,oBAGjCE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDAAwDE,SAClEL,EACG,wUACA,0EAINM,EAAAA,EAAAA,MAAA,OAAKH,UAAU,gEAA+DE,SAAA,EAC5EC,EAAAA,EAAAA,MAACC,EAAAA,GAAI,CACHC,GAAG,aACHL,UAAU,0NAAyNE,SAAA,EAEnOH,EAAAA,EAAAA,KAAA,KAAGC,UAAS,eAAAC,OAAiBJ,EAAQ,OAAS,UAC7CA,EAAQ,wFAAoB,sBAG/BM,EAAAA,EAAAA,MAAA,UACEG,QAASA,IAAMC,OAAOC,QAAQC,OAC9BT,UAAU,gSAA+RE,SAAA,EAEzSH,EAAAA,EAAAA,KAAA,KAAGC,UAAS,qBAAAC,OAAuBJ,EAAQ,OAAS,UACnDA,EAAQ,2BAAS,qB", "sources": ["pages/NotFound/NotFound.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst NotFound = () => {\n  const { isRTL } = useLanguage();\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      <div className=\"text-center\">\n        <div className=\"w-24 h-24 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-6\">\n          <i className=\"fas fa-exclamation-triangle text-4xl text-red-600 dark:text-red-400\"></i>\n        </div>\n        \n        <h1 className=\"text-6xl font-bold text-gray-900 dark:text-white mb-4\">404</h1>\n        \n        <h2 className=\"text-2xl font-semibold text-gray-700 dark:text-gray-300 mb-4\">\n          {isRTL ? 'الصفحة غير موجودة' : 'Page Not Found'}\n        </h2>\n        \n        <p className=\"text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto\">\n          {isRTL \n            ? 'عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى موقع آخر.'\n            : 'Sorry, the page you are looking for doesn\\'t exist or has been moved.'\n          }\n        </p>\n        \n        <div className=\"space-y-3 sm:space-y-0 sm:space-x-3 sm:flex sm:justify-center\">\n          <Link\n            to=\"/dashboard\"\n            className=\"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <i className={`fas fa-home ${isRTL ? 'ml-2' : 'mr-2'}`}></i>\n            {isRTL ? 'العودة للرئيسية' : 'Go to Dashboard'}\n          </Link>\n          \n          <button\n            onClick={() => window.history.back()}\n            className=\"inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <i className={`fas fa-arrow-left ${isRTL ? 'ml-2' : 'mr-2'}`}></i>\n            {isRTL ? 'رجوع' : 'Go Back'}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NotFound;\n"], "names": ["NotFound", "isRTL", "useLanguage", "_jsx", "className", "concat", "children", "_jsxs", "Link", "to", "onClick", "window", "history", "back"], "sourceRoot": ""}