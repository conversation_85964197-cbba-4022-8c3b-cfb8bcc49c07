{"version": 3, "file": "static/js/2684.5fa84280.chunk.js", "mappings": "oMAOO,MAAMA,EAAiBA,KAC5B,MAAM,KAAEC,IAASC,EAAAA,EAAAA,MACVC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,OACxCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAEvCG,EAAAA,EAAAA,WAAU,KACJP,EACFQ,KAEAL,EAAe,MACfG,GAAW,KAEZ,CAACN,IAEJ,MAAMQ,EAAsBC,UAC1B,IACEH,GAAW,GAGX,MAAMI,QAAiBC,MAAM,yBAA0B,CACrDC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9Bf,EAAec,EAAOE,KAAKjB,YAC7B,KAAO,CAEL,MAAMkB,EAAkBC,EAAyBrB,EAAKsB,MACtDnB,EAAeiB,EACjB,CACF,CAAE,MAAOG,GACPC,QAAQD,MAAM,6BAA8BA,GAE5C,MAAMH,EAAkBC,GAA6B,OAAJrB,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,OAAQ,SAC/DnB,EAAeiB,EACjB,CAAC,QACCd,GAAW,EACb,GAGIe,EAA4BC,IAChC,MAAMG,EAAkB,CACtBC,MAAO,CACLC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAKzBC,OAAQ,CACNhC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBK,oBAAoB,EACpBJ,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbE,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAKzBE,UAAW,CACTjC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BG,oBAAoB,EACpBF,uBAAuB,EACvBC,aAAa,EACbE,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAKzBG,MAAO,CACLlC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAKzBI,aAAc,CACZnC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAKzBK,mBAAoB,CAClBpC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAKzBM,aAAc,CACZrC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAK3B,OAAOjC,EAAgBH,IAASG,EAAgBoC,OAiClD,MAAO,CACL3D,cACAG,UACA4D,cAhCoBA,CAACC,EAAUC,KAAgB,IAADC,EAC9C,SAAKlE,IAAgBF,KAGH,UAAdA,EAAKsB,QAEmB,QAArB8C,EAAAlE,EAAYgE,UAAS,IAAAE,OAAA,EAArBA,EAAwBD,MAAe,KA2B9CE,iBAvBuBA,CAACH,EAAUI,OAC7BpE,IAAgBF,KAGH,UAAdA,EAAKsB,MAEFgD,EAAeC,KAAKJ,IAAU,IAAAK,EAAA,OAAyB,QAAzBA,EAAItE,EAAYgE,UAAS,IAAAM,OAAA,EAArBA,EAAwBL,MAkBjEM,QAdenD,IACJ,OAAJtB,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,QAASA,EActBoD,WAVkBC,GACXA,EAAMC,SAAa,OAAJ5E,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MAU5BuD,SAAc,OAAJ7E,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,KAChBwD,QAAwB,WAAX,OAAJ9E,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MACfyD,SAAyB,YAAX,OAAJ/E,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MAChB0D,YAA4B,eAAX,OAAJhF,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MACnB2D,QAAwB,WAAX,OAAJjF,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MACf4D,cAA8B,kBAAX,OAAJlF,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MACrB6D,oBAAoC,wBAAX,OAAJnF,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MAC3B8D,eAA+B,kBAAX,OAAJpF,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,MACtB+D,UAA0B,aAAX,OAAJrF,QAAI,IAAJA,OAAI,EAAJA,EAAMsB,Q,aC1WrB,MAAMgE,EAAkBC,IAQjB,IARkB,SACvBC,EAAQ,WACRrB,EAAU,SACVD,EAAW,QAAO,KAClB5C,EAAI,MACJqD,EAAK,SACLc,EAAW,KAAI,aACfC,GAAe,GAChBH,EACC,MAAM,cAAEtB,EAAa,QAAEQ,EAAO,WAAEC,EAAU,QAAErE,EAAO,SAAEwE,GAAa9E,KAC5D,EAAE4F,IAAMC,EAAAA,EAAAA,KAGd,OAAIvF,GAEAwF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAsCN,UACnDK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mEAMjBxE,IAASmD,EAAQnD,GACZoE,EAAgBD,IAAYI,EAAAA,EAAAA,KAACE,EAAY,CAACC,aAAc1E,IAAY,KAGzEqD,IAAUD,EAAWC,GAChBe,EAAgBD,IAAYI,EAAAA,EAAAA,KAACE,EAAY,CAACE,cAAetB,IAAa,KAI3ER,IAAeF,EAAcC,EAAUC,GAClCuB,EAAgBD,IAAYI,EAAAA,EAAAA,KAACE,EAAY,CAACG,mBAAoB/B,EAAYD,SAAUA,IAAgB,KAItGsB,GAIHO,EAAeI,IAAoE,IAAnE,aAAEH,EAAY,cAAEC,EAAa,mBAAEC,EAAkB,SAAEhC,GAAUiC,EACjF,MAAM,EAAER,IAAMC,EAAAA,EAAAA,MACR,SAAEf,GAAa9E,IAEfqG,EAAY,CAChB1E,MAAOiE,EAAE,QAAS,iEAClBhC,OAAQgC,EAAE,SAAU,4BACpB/B,UAAW+B,EAAE,YAAa,wHAC1B9B,MAAO8B,EAAE,QAAS,2DAClB7B,aAAc6B,EAAE,cAAe,iEAC/B5B,mBAAoB4B,EAAE,oBAAqB,oFAC3C3B,aAAc2B,EAAE,eAAgB,uEAChCU,QAASV,EAAE,UAAW,6BAGlBW,EAAkB,CAEtB1E,iBAAkB+D,EAAE,mBAAoB,6EACxC9D,aAAc8D,EAAE,eAAgB,kHAChC7D,aAAc6D,EAAE,eAAgB,6EAChC5D,oBAAqB4D,EAAE,sBAAuB,uEAC9C3D,aAAc2D,EAAE,eAAgB,mFAChC1D,uBAAwB0D,EAAE,yBAA0B,iIACpDzD,cAAeyD,EAAE,gBAAiB,4GAClCxD,0BAA2BwD,EAAE,4BAA6B,6IAC1DvD,sBAAuBuD,EAAE,wBAAyB,uIAClDtD,YAAasD,EAAE,cAAe,uJAC9BrD,mBAAoBqD,EAAE,qBAAsB,qGAC5CpD,WAAYoD,EAAE,aAAc,6EAC5BnD,iBAAkBmD,EAAE,mBAAoB,uEAGxCjD,aAAciD,EAAE,eAAgB,2DAChChD,eAAgBgD,EAAE,iBAAkB,2DACpC/C,aAAc+C,EAAE,eAAgB,uEAChC9C,eAAgB8C,EAAE,iBAAkB,2DACpC7C,iBAAkB6C,EAAE,mBAAoB,uEACxC5C,mBAAoB4C,EAAE,qBAAsB,uEAC5C3C,iBAAkB2C,EAAE,mBAAoB,mFACxC1C,mBAAoB0C,EAAE,qBAAsB,uEAC5CzC,cAAeyC,EAAE,gBAAiB,6EAClCxC,YAAawC,EAAE,cAAe,+FAC9BvC,eAAgBuC,EAAE,iBAAkB,mFAGpCrC,iCAAkCqC,EAAE,mCAAoC,gJACxEpC,2BAA4BoC,EAAE,6BAA8B,wHAC5DnC,yBAA0BmC,EAAE,2BAA4B,4GACxDlC,eAAgBkC,EAAE,iBAAkB,mFACpCjC,oBAAqBiC,EAAE,sBAAuB,0FAGhD,OACEE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uCAAsCN,UACnDe,EAAAA,EAAAA,MAAA,OAAKT,UAAU,uBAAsBN,SAAA,EACnCK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMN,UACnBK,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DN,SACrEG,EAAE,eAAgB,0EAErBY,EAAAA,EAAAA,MAAA,OAAKT,UAAU,6CAA4CN,SAAA,EACzDe,EAAAA,EAAAA,MAAA,KAAAf,SAAA,CACGG,EAAE,cAAe,iEAAe,MAAEE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaN,SAAEY,EAAUvB,IAAaA,OAG1FmB,IACCO,EAAAA,EAAAA,MAAA,KAAAf,SAAA,CACGG,EAAE,eAAgB,6EAAiB,MAAEE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2BAA0BN,SAAEY,EAAUJ,IAAiBA,OAIhHC,IACCM,EAAAA,EAAAA,MAAA,KAAAf,SAAA,CACGG,EAAE,gBAAiB,+FAAoB,KACxCE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2BAA0BN,SACvCS,EAAcO,IAAIlF,GAAQ8E,EAAU9E,IAASA,GAAMmF,KAAK,uBAK9DP,IACCK,EAAAA,EAAAA,MAAA,KAAAf,SAAA,CACGG,EAAE,qBAAsB,qGAAqB,KAC9CE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2BAA0BN,SACvCc,EAAgBJ,IAAuBA,WAKhDL,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yDAAwDN,UACrEK,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+CAA8CN,SACxDG,EAAE,wBAAyB,4TAgC3Be,EAAYC,IAAA,IAAC,SAAEnB,EAAQ,SAAEC,EAAQ,aAAEC,GAAe,GAAMiB,EAAA,OACnEd,EAAAA,EAAAA,KAACP,EAAe,CAAChE,KAAK,QAAQmE,SAAUA,EAAUC,aAAcA,EAAaF,SAC1EA,K,cC3KL,MA2hBA,EA3hB8BoB,KAAO,IAADC,EAAAC,EAClC,MAAM,EAAEnB,EAAC,MAAEoB,IAAUnB,EAAAA,EAAAA,MACdoB,EAAOC,IAAY7G,EAAAA,EAAAA,UAAS,KAC5B8G,EAAcC,IAAmB/G,EAAAA,EAAAA,UAAS,OAC1CF,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,OACxCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCgH,EAAQC,IAAajH,EAAAA,EAAAA,WAAS,IAC9BkH,EAAWC,IAAgBnH,EAAAA,EAAAA,UAAS,SAGrCoH,EAAkB,CACtB7D,OAAQ,CACN8D,KAAM9B,EAAE,SAAU,4BAClB+B,OAAQ,SACRC,YAAahC,EAAE,oBAAqB,2hBACpCiC,MAAO,QAEThE,UAAW,CACT6D,KAAM9B,EAAE,YAAa,wHACrB+B,OAAQ,qBACRC,YAAahC,EAAE,uBAAwB,qNACvCiC,MAAO,SAET/D,MAAO,CACL4D,KAAM9B,EAAE,QAAS,2DACjB+B,OAAQ,QACRC,YAAahC,EAAE,mBAAoB,gQACnCiC,MAAO,QAET9D,aAAc,CACZ2D,KAAM9B,EAAE,cAAe,iEACvB+B,OAAQ,sBACRC,YAAahC,EAAE,yBAA0B,4GACzCiC,MAAO,UAET7D,mBAAoB,CAClB0D,KAAM9B,EAAE,oBAAqB,oFAC7B+B,OAAQ,qBACRC,YAAahC,EAAE,+BAAgC,sGAC/CiC,MAAO,UAET5D,aAAc,CACZyD,KAAM9B,EAAE,eAAgB,uEACxB+B,OAAQ,eACRC,YAAahC,EAAE,0BAA2B,iJAC1CiC,MAAO,UAETvB,QAAS,CACPoB,KAAM9B,EAAE,UAAW,4BACnB+B,OAAQ,UACRC,YAAahC,EAAE,qBAAsB,sOACrCiC,MAAO,OAETlG,MAAO,CACL+F,KAAM9B,EAAE,QAAS,iEACjB+B,OAAQ,gBACRC,YAAahC,EAAE,mBAAoB,mFACnCiC,MAAO,SAKLC,EAAuB,CAC3BlG,MAAO,CACL8F,KAAM9B,EAAE,mBAAoB,yFAC5B+B,OAAQ,oBACRxH,YAAa,CACX0B,iBAAkB+D,EAAE,mBAAoB,6EACxC9D,aAAc8D,EAAE,eAAgB,kHAChC7D,aAAc6D,EAAE,eAAgB,6EAChC5D,oBAAqB4D,EAAE,sBAAuB,uEAC9C3D,aAAc2D,EAAE,eAAgB,mFAChC1D,uBAAwB0D,EAAE,yBAA0B,iIACpDzD,cAAeyD,EAAE,gBAAiB,4GAClCxD,0BAA2BwD,EAAE,4BAA6B,6IAC1DvD,sBAAuBuD,EAAE,wBAAyB,uIAClDtD,YAAasD,EAAE,cAAe,uJAC9BrD,mBAAoBqD,EAAE,qBAAsB,qGAC5CpD,WAAYoD,EAAE,aAAc,6EAC5BnD,iBAAkBmD,EAAE,mBAAoB,yEAG5ClD,OAAQ,CACNgF,KAAM9B,EAAE,oBAAqB,mFAC7B+B,OAAQ,qBACRxH,YAAa,CACXwC,aAAciD,EAAE,eAAgB,2DAChChD,eAAgBgD,EAAE,iBAAkB,2DACpC/C,aAAc+C,EAAE,eAAgB,uEAChC9C,eAAgB8C,EAAE,iBAAkB,2DACpC7C,iBAAkB6C,EAAE,mBAAoB,uEACxC5C,mBAAoB4C,EAAE,qBAAsB,uEAC5C3C,iBAAkB2C,EAAE,mBAAoB,mFACxC1C,mBAAoB0C,EAAE,qBAAsB,uEAC5CzC,cAAeyC,EAAE,gBAAiB,6EAClCxC,YAAawC,EAAE,cAAe,+FAC9BvC,eAAgBuC,EAAE,iBAAkB,qFAGxCtC,UAAW,CACToE,KAAM9B,EAAE,uBAAwB,qGAChC+B,OAAQ,wBACRxH,YAAa,CACXoD,iCAAkCqC,EAAE,mCAAoC,wRACxEpC,2BAA4BoC,EAAE,6BAA8B,yRAC5DnC,yBAA0BmC,EAAE,2BAA4B,4GACxDlC,eAAgBkC,EAAE,iBAAkB,mFACpCjC,oBAAqBiC,EAAE,sBAAuB,6FAKpDpF,EAAAA,EAAAA,WAAU,KACRuH,KACC,IAEH,MAAMA,EAAYrH,UAChB,IACEH,GAAW,GACX,MAAMI,QAAiBC,MAAM,gBAAiB,CAC5CC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9B+F,EAAShG,EAAOE,KAClB,KAAO,CAwCL8F,EAtCkB,CAChB,CACEc,IAAK,IACLC,UAAW,2BACXC,SAAU,2BACVC,MAAO,6BACP5G,KAAM,SACN6G,WAAY,kFACZC,UAAU,GAEZ,CACEL,IAAK,IACLC,UAAW,iCACXC,SAAU,qBACVC,MAAO,0BACP5G,KAAM,YACN6G,WAAY,kFACZC,UAAU,GAEZ,CACEL,IAAK,IACLC,UAAW,2BACXC,SAAU,2BACVC,MAAO,0BACP5G,KAAM,QACN6G,WAAY,6CACZC,UAAU,GAEZ,CACEL,IAAK,IACLC,UAAW,sEACXC,SAAU,GACVC,MAAO,kBACP5G,KAAM,eACN6G,WAAY,gEACZC,UAAU,IAIhB,CACF,CAAE,MAAO7G,GACPC,QAAQD,MAAM,uBAAwBA,GACtC8G,EAAAA,GAAM9G,MAAMoE,EAAE,oBAAqB,+HACrC,CAAC,QACCrF,GAAW,EACb,GA4BIe,EAA4BC,IAChC,MAAMG,EAAkB,CACtBkC,OAAQ,CACNhC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAGzBE,UAAW,CACTjC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,IAGzBG,MAAO,CACLlC,MAAO,CACLC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,wBAAwB,EACxBC,eAAe,EACfC,2BAA2B,EAC3BC,uBAAuB,EACvBC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,kBAAkB,GAEpBC,OAAQ,CACNC,cAAc,EACdC,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAkB,EAClBC,oBAAoB,EACpBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,GAElBC,UAAW,CACTC,kCAAkC,EAClCC,4BAA4B,EAC5BC,0BAA0B,EAC1BC,gBAAgB,EAChBC,qBAAqB,KAK3B,OAAOjC,EAAgBH,IAASG,EAAgBoC,OAG5CyE,EAAoBtI,IACxBmH,EAAgBnH,GAhJUS,WAC1B,IACE,MAAMC,QAAiBC,MAAM,4BAADE,OAA6B0H,GAAU,CACjE3H,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9Bf,EAAec,EAAOE,KAAKjB,YAC7B,KAAO,CAEL,MAAMF,EAAOgH,EAAMwB,KAAKC,GAAKA,EAAEV,MAAQQ,GACvC,GAAIvI,EAAM,CACR,MAAMoB,EAAkBC,EAAyBrB,EAAKsB,MACtDnB,EAAeiB,EACjB,CACF,CACF,CAAE,MAAOG,GACPC,QAAQD,MAAM,6BAA8BA,GAC5C8G,EAAAA,GAAM9G,MAAMoE,EAAE,0BAA2B,yHAC3C,GA2HAnF,CAAoBR,EAAK+H,MAqE3B,OAAI1H,GAEAwF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CN,UAC5DK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBD,EAAAA,EAAAA,KAACa,EAAS,CAAAlB,UACRe,EAAAA,EAAAA,MAAA,OAAKT,UAAU,wBAAuBN,SAAA,EAEtCe,EAAAA,EAAAA,MAAA,OAAKT,UAAU,OAAMN,SAAA,EACnBK,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wDAAuDN,SAClEG,EAAE,wBAAyB,4FAE9BE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCN,SAC5CG,EAAE,4BAA6B,mbAIpCY,EAAAA,EAAAA,MAAA,OAAKT,UAAU,wCAAuCN,SAAA,EAEpDK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeN,UAC5Be,EAAAA,EAAAA,MAAA,OAAKT,UAAU,0FAAyFN,SAAA,EACtGK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDN,UAChEK,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDN,SAChEG,EAAE,QAAS,qEAGhBE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKN,UAClBK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWN,SACvBwB,EAAMR,IAAIxG,IAAI,IAAA0I,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OACbjD,EAAAA,EAAAA,KAAA,OAEEkD,QAASA,IAAMT,EAAiBtI,GAChC8F,UAAS,0DAAAjF,QACK,OAAZqG,QAAY,IAAZA,OAAY,EAAZA,EAAca,OAAQ/H,EAAK+H,IACvB,iDACA,yFACHvC,UAEHe,EAAAA,EAAAA,MAAA,OAAKT,UAAU,oCAAmCN,SAAA,EAChDe,EAAAA,EAAAA,MAAA,OAAAf,SAAA,EACEe,EAAAA,EAAAA,MAAA,MAAIT,UAAU,4CAA2CN,SAAA,CACtDxF,EAAKgI,UAAU,IAAEhI,EAAKiI,aAEzBpC,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CN,SACpDxF,EAAKkI,YAGVrC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYN,UACzBK,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8EAAAjF,OAA0G,QAA1G6H,EAAgFlB,EAAgBxH,EAAKsB,aAAK,IAAAoH,OAAA,EAA1BA,EAA4Bd,MAAK,cAAA/G,OAAuC,QAAvC8H,EAAanB,EAAgBxH,EAAKsB,aAAK,IAAAqH,OAAA,EAA1BA,EAA4Bf,MAAK,iBAAA/G,OAA0C,QAA1C+H,EAAgBpB,EAAgBxH,EAAKsB,aAAK,IAAAsH,OAAA,EAA1BA,EAA4BhB,MAAK,sBAAA/G,OAA+C,QAA/CgI,EAAqBrB,EAAgBxH,EAAKsB,aAAK,IAAAuH,OAAA,EAA1BA,EAA4BjB,MAAK,QAAOpC,SAC/P,QAD+PsD,EACzRtB,EAAgBxH,EAAKsB,aAAK,IAAAwH,OAAA,EAA1BA,EAA4BrB,aAnB9BzH,EAAK+H,iBA+BtBlC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeN,SAC3B0B,GACCX,EAAAA,EAAAA,MAAA,OAAKT,UAAU,0FAAyFN,SAAA,EACtGK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDN,UAChEe,EAAAA,EAAAA,MAAA,OAAKT,UAAU,oCAAmCN,SAAA,EAChDe,EAAAA,EAAAA,MAAA,OAAAf,SAAA,EACEe,EAAAA,EAAAA,MAAA,MAAIT,UAAU,sDAAqDN,SAAA,CAChEG,EAAE,iBAAkB,8CAAW,IAAEuB,EAAac,UAAU,IAAEd,EAAae,aAE1E1B,EAAAA,EAAAA,MAAA,KAAGT,UAAU,2CAA0CN,SAAA,CAClB,QADkBqB,EACpDW,EAAgBN,EAAa5F,aAAK,IAAAuF,OAAA,EAAlCA,EAAoCY,KAAK,MAAsC,QAAnCX,EAACU,EAAgBN,EAAa5F,aAAK,IAAAwF,OAAA,EAAlCA,EAAoCa,mBAGtFpB,EAAAA,EAAAA,MAAA,OAAKT,UAAU,iBAAgBN,SAAA,EAC7BK,EAAAA,EAAAA,KAAA,UACEkD,QA7GItI,UACtB,GAAKyG,EAEL,IACEG,GAAU,GACV,MAAM3G,QAAiBC,MAAM,4BAADE,OAA6BqG,EAAaa,IAAG,UAAU,CACjFiB,OAAQ,OACRpI,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,aAIpD,GAAIL,EAASM,GAAI,CACf,MAAMC,QAAeP,EAASQ,OAC9Bf,EAAec,EAAOE,KAAKjB,aAC3BmI,EAAAA,GAAMY,QAAQtD,EAAE,mBAAoB,kMACtC,KAAO,CACL,MAAMvE,EAAkBC,EAAyB6F,EAAa5F,MAC9DnB,EAAeiB,GACfiH,EAAAA,GAAMY,QAAQtD,EAAE,mBAAoB,kQACtC,CACF,CAAE,MAAOpE,GACPC,QAAQD,MAAM,+BAAgCA,GAC9C8G,EAAAA,GAAM9G,MAAMoE,EAAE,4BAA6B,wJAC7C,CAAC,QACC0B,GAAU,EACZ,GAoFkB6B,SAAU9B,EACVtB,UAAU,sGAAqGN,SAE9GG,EAAE,kBAAmB,4FAExBE,EAAAA,EAAAA,KAAA,UACEkD,QA/IItI,UACtB,GAAKyG,GAAiBhH,EAEtB,IACEmH,GAAU,UACa1G,MAAM,4BAADE,OAA6BqG,EAAaa,KAAO,CAC3EiB,OAAQ,MACRpI,QAAS,CACP,eAAgB,mBAChB,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,WAElDoI,KAAMC,KAAKC,UAAU,CAAEnJ,mBAGZc,GACXqH,EAAAA,GAAMY,QAAQtD,EAAE,mBAAoB,0HAEpC0C,EAAAA,GAAMY,QAAQtD,EAAE,mBAAoB,yLAExC,CAAE,MAAOpE,GACPC,QAAQD,MAAM,4BAA6BA,GAC3C8G,EAAAA,GAAM9G,MAAMoE,EAAE,yBAA0B,6GAC1C,CAAC,QACC0B,GAAU,EACZ,GAwHkB6B,SAAU9B,EACVtB,UAAU,sGAAqGN,SAE9G4B,EAASzB,EAAE,SAAU,8DAAmBA,EAAE,kBAAmB,uFAMrEzF,IACC2F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKN,SACjB8D,OAAOC,QAAQ1B,GAAsBrB,IAAIjB,IAAA,IAAEiE,EAAatF,GAASqB,EAAA,OAChEgB,EAAAA,EAAAA,MAAA,OAAuBT,UAAU,OAAMN,SAAA,EACrCK,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDN,SACnEtB,EAASuD,QAEZ5B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCN,SACnD8D,OAAOC,QAAQrF,EAAShE,aAAasG,IAAIL,IAAA,IAAA/B,EAAA,IAAEqF,EAAeC,GAAevD,EAAA,OACxEI,EAAAA,EAAAA,MAAA,OAAyBT,UAAU,+EAA8EN,SAAA,EAC/GK,EAAAA,EAAAA,KAAA,QAAMC,UAAU,wCAAuCN,SACpDkE,KAEHnD,EAAAA,EAAAA,MAAA,SAAOT,UAAU,mDAAkDN,SAAA,EACjEK,EAAAA,EAAAA,KAAA,SACE8D,KAAK,WACLC,SAAiC,QAAxBxF,EAAAlE,EAAYsJ,UAAY,IAAApF,OAAA,EAAxBA,EAA2BqF,MAAkB,EACtDI,SAAWC,GApLVC,EAAC7F,EAAUC,EAAY6F,KACpD7J,EAAe8J,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdD,GAAI,IACP,CAAC/F,IAAQgG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACJD,EAAK/F,IAAS,IACjB,CAACC,GAAa6F,QA+K2BD,CAAuBP,EAAaC,EAAeK,EAAEK,OAAOP,SAC7E9D,UAAU,kBAEZD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qcAXT2D,SANND,WA4BlB3D,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+FAA8FN,UAC3Ge,EAAAA,EAAAA,MAAA,OAAKT,UAAU,cAAaN,SAAA,EAC1BK,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDN,SACnEG,EAAE,aAAc,oEAEnBE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCN,SAC5CG,EAAE,iBAAkB,qQ", "sources": ["hooks/usePermissions.js", "components/Auth/PermissionGuard.jsx", "pages/Admin/PermissionsManagement.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\n\n/**\n * Custom hook for managing user permissions\n * هوك مخصص لإدارة صلاحيات المستخدم\n */\nexport const usePermissions = () => {\n  const { user } = useAuth();\n  const [permissions, setPermissions] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (user) {\n      loadUserPermissions();\n    } else {\n      setPermissions(null);\n      setLoading(false);\n    }\n  }, [user]);\n\n  const loadUserPermissions = async () => {\n    try {\n      setLoading(true);\n      \n      // Try to get permissions from API\n      const response = await fetch('/api/v1/permissions/me', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setPermissions(result.data.permissions);\n      } else {\n        // Fallback to mock permissions based on user role\n        const mockPermissions = getMockPermissionsByRole(user.role);\n        setPermissions(mockPermissions);\n      }\n    } catch (error) {\n      console.error('Error loading permissions:', error);\n      // Fallback to mock permissions\n      const mockPermissions = getMockPermissionsByRole(user?.role || 'nurse');\n      setPermissions(mockPermissions);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getMockPermissionsByRole = (role) => {\n    const rolePermissions = {\n      admin: {\n        forms: {\n          medicalDiagnosis: true,\n          ptEvaluation: true,\n          reassessment: true,\n          dischargeEvaluation: true,\n          followUpForm: true,\n          patientEducationDoctor: true,\n          progressNotes: true,\n          patientEducationTherapist: true,\n          patientEducationNurse: true,\n          nursingForm: true,\n          clinicalIndicators: true,\n          labResults: true,\n          radiologyResults: true\n        },\n        system: {\n          viewPatients: true,\n          createPatients: true,\n          editPatients: true,\n          deletePatients: true,\n          viewAppointments: true,\n          createAppointments: true,\n          editAppointments: true,\n          deleteAppointments: true,\n          viewAnalytics: true,\n          manageUsers: true,\n          systemSettings: true\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: true\n        }\n      },\n      \n      // صلاحيات الطبيب - Doctor Permissions\n      doctor: {\n        forms: {\n          medicalDiagnosis: true,           // 1. التشخيص الطبي\n          ptEvaluation: true,               // 2. تقييم العلاج الطبيعي\n          reassessment: true,               // 3. إعادة التقييم\n          dischargeEvaluation: true,        // 4. تقييم الخروج\n          followUpForm: true,               // 5. نموذج المتابعة\n          patientEducationDoctor: true,     // 6. نموذج تثقيف المريض\n          clinicalIndicators: true,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          labResults: true,\n          radiologyResults: true\n        },\n        system: {\n          viewPatients: true,\n          createPatients: true,\n          editPatients: true,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: true,\n          editAppointments: true,\n          deleteAppointments: false,\n          viewAnalytics: true,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: true\n        }\n      },\n\n      // صلاحيات أخصائي العلاج الطبيعي - Therapist Permissions\n      therapist: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: true,              // 1. ملاحظات تقدم الحالة\n          patientEducationTherapist: true,  // 2. نموذج تثقيف المريض\n          clinicalIndicators: true,\n          patientEducationNurse: false,\n          nursingForm: false,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: true,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: false\n        }\n      },\n\n      // صلاحيات التمريض - Nurse Permissions\n      nurse: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: true,      // 1. نموذج تثقيف المريض\n          nursingForm: true,                // 2. نموذج التمريض التابع للمركز\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      },\n\n      // صلاحيات المختبرات الخارجية - External Lab Permissions\n      external_lab: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: false,\n          labResults: true,                 // إدخال نتائج المختبر\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,               // عرض المرضى المحولين فقط\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: false,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      },\n\n      // صلاحيات مراكز الأشعة الخارجية - External Radiology Permissions\n      external_radiology: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: true            // إدخال نتائج الأشعة\n        },\n        system: {\n          viewPatients: true,               // عرض المرضى المحولين فقط\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: false,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      },\n\n      // صلاحيات الاستقبال - Receptionist Permissions\n      receptionist: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: true,             // تسجيل المرضى الجدد\n          editPatients: true,               // تعديل بيانات المرضى\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: true,         // حجز المواعيد\n          editAppointments: true,           // تعديل المواعيد\n          deleteAppointments: true,         // إلغاء المواعيد\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      }\n    };\n\n    return rolePermissions[role] || rolePermissions.nurse;\n  };\n\n  // Check if user has specific permission\n  const hasPermission = (category, permission) => {\n    if (!permissions || !user) return false;\n    \n    // Admin has all permissions\n    if (user.role === 'admin') return true;\n    \n    return permissions[category]?.[permission] || false;\n  };\n\n  // Check if user has any permission in a category\n  const hasAnyPermission = (category, permissionList) => {\n    if (!permissions || !user) return false;\n    \n    // Admin has all permissions\n    if (user.role === 'admin') return true;\n    \n    return permissionList.some(permission => permissions[category]?.[permission]);\n  };\n\n  // Check if user has specific role\n  const hasRole = (role) => {\n    return user?.role === role;\n  };\n\n  // Check if user has any of the specified roles\n  const hasAnyRole = (roles) => {\n    return roles.includes(user?.role);\n  };\n\n  return {\n    permissions,\n    loading,\n    hasPermission,\n    hasAnyPermission,\n    hasRole,\n    hasAnyRole,\n    userRole: user?.role,\n    isAdmin: user?.role === 'admin',\n    isDoctor: user?.role === 'doctor',\n    isTherapist: user?.role === 'therapist',\n    isNurse: user?.role === 'nurse',\n    isExternalLab: user?.role === 'external_lab',\n    isExternalRadiology: user?.role === 'external_radiology',\n    isReceptionist: user?.role === 'receptionist',\n    isManager: user?.role === 'manager'\n  };\n};\n", "import React from 'react';\nimport { usePermissions } from '../../hooks/usePermissions';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\n/**\n * Permission Guard Component\n * مكون حماية الصلاحيات\n * \n * يتحكم في عرض المحتوى بناءً على صلاحيات المستخدم\n */\n\nconst PermissionGuard = ({ \n  children, \n  permission, \n  category = 'forms', \n  role, \n  roles, \n  fallback = null,\n  showFallback = true \n}) => {\n  const { hasPermission, hasRole, hasAnyRole, loading, userRole } = usePermissions();\n  const { t } = useLanguage();\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-4\">\n        <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  // Check role-based access\n  if (role && !hasRole(role)) {\n    return showFallback ? (fallback || <AccessDenied requiredRole={role} />) : null;\n  }\n\n  if (roles && !hasAnyRole(roles)) {\n    return showFallback ? (fallback || <AccessDenied requiredRoles={roles} />) : null;\n  }\n\n  // Check permission-based access\n  if (permission && !hasPermission(category, permission)) {\n    return showFallback ? (fallback || <AccessDenied requiredPermission={permission} category={category} />) : null;\n  }\n\n  // User has access, render children\n  return children;\n};\n\n// Access Denied Component\nconst AccessDenied = ({ requiredRole, requiredRoles, requiredPermission, category }) => {\n  const { t } = useLanguage();\n  const { userRole } = usePermissions();\n\n  const roleNames = {\n    admin: t('admin', 'مدير النظام'),\n    doctor: t('doctor', 'طبيب'),\n    therapist: t('therapist', 'أخصائي العلاج الطبيعي'),\n    nurse: t('nurse', 'ممرض/ممرضة'),\n    external_lab: t('externalLab', 'مختبر خارجي'),\n    external_radiology: t('externalRadiology', 'مركز أشعة خارجي'),\n    receptionist: t('receptionist', 'موظف استقبال'),\n    manager: t('manager', 'مدير')\n  };\n\n  const permissionNames = {\n    // Forms permissions\n    medicalDiagnosis: t('medicalDiagnosis', 'التشخيص الطبي'),\n    ptEvaluation: t('ptEvaluation', 'تقييم العلاج الطبيعي'),\n    reassessment: t('reassessment', 'إعادة التقييم'),\n    dischargeEvaluation: t('dischargeEvaluation', 'تقييم الخروج'),\n    followUpForm: t('followUpForm', 'نموذج المتابعة'),\n    patientEducationDoctor: t('patientEducationDoctor', 'نموذج تثقيف المريض (طبيب)'),\n    progressNotes: t('progressNotes', 'ملاحظات تقدم الحالة'),\n    patientEducationTherapist: t('patientEducationTherapist', 'نموذج تثقيف المريض (أخصائي)'),\n    patientEducationNurse: t('patientEducationNurse', 'نموذج تثقيف المريض (تمريض)'),\n    nursingForm: t('nursingForm', 'نموذج التمريض التابع للمركز'),\n    clinicalIndicators: t('clinicalIndicators', 'المؤشرات السريرية'),\n    labResults: t('labResults', 'نتائج المختبر'),\n    radiologyResults: t('radiologyResults', 'نتائج الأشعة'),\n    \n    // System permissions\n    viewPatients: t('viewPatients', 'عرض المرضى'),\n    createPatients: t('createPatients', 'إنشاء مرضى'),\n    editPatients: t('editPatients', 'تعديل المرضى'),\n    deletePatients: t('deletePatients', 'حذف المرضى'),\n    viewAppointments: t('viewAppointments', 'عرض المواعيد'),\n    createAppointments: t('createAppointments', 'إنشاء مواعيد'),\n    editAppointments: t('editAppointments', 'تعديل المواعيد'),\n    deleteAppointments: t('deleteAppointments', 'حذف المواعيد'),\n    viewAnalytics: t('viewAnalytics', 'عرض التحليلات'),\n    manageUsers: t('manageUsers', 'إدارة المستخدمين'),\n    systemSettings: t('systemSettings', 'إعدادات النظام'),\n    \n    // Analytics permissions\n    functionalIndependenceComparison: t('functionalIndependenceComparison', 'درجة الاستقلالية الوظيفية'),\n    treatmentPlanEffectiveness: t('treatmentPlanEffectiveness', 'فعالية الخطة العلاجية'),\n    clinicalProgressTracking: t('clinicalProgressTracking', 'تتبع التقدم السريري'),\n    outcomeMetrics: t('outcomeMetrics', 'مقاييس النتائج'),\n    complianceReporting: t('complianceReporting', 'تقارير الامتثال')\n  };\n\n  return (\n    <div className=\"flex items-center justify-center p-8\">\n      <div className=\"text-center max-w-md\">\n        <div className=\"mb-4\">\n          <i className=\"fas fa-shield-alt text-6xl text-red-400\"></i>\n        </div>\n        <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n          {t('accessDenied', 'الوصول مرفوض')}\n        </h3>\n        <div className=\"text-gray-600 dark:text-gray-400 space-y-2\">\n          <p>\n            {t('currentRole', 'دورك الحالي')}: <span className=\"font-medium\">{roleNames[userRole] || userRole}</span>\n          </p>\n          \n          {requiredRole && (\n            <p>\n              {t('requiredRole', 'الدور المطلوب')}: <span className=\"font-medium text-red-600\">{roleNames[requiredRole] || requiredRole}</span>\n            </p>\n          )}\n          \n          {requiredRoles && (\n            <p>\n              {t('requiredRoles', 'الأدوار المطلوبة')}: \n              <span className=\"font-medium text-red-600\">\n                {requiredRoles.map(role => roleNames[role] || role).join(' أو ')}\n              </span>\n            </p>\n          )}\n          \n          {requiredPermission && (\n            <p>\n              {t('requiredPermission', 'الصلاحية المطلوبة')}: \n              <span className=\"font-medium text-red-600\">\n                {permissionNames[requiredPermission] || requiredPermission}\n              </span>\n            </p>\n          )}\n        </div>\n        <div className=\"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg\">\n          <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n            {t('contactAdminForAccess', 'يرجى التواصل مع مدير النظام للحصول على الصلاحيات المطلوبة')}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Specific permission guards for common use cases\n\n// Doctor only access\nexport const DoctorOnly = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard role=\"doctor\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Therapist only access\nexport const TherapistOnly = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard role=\"therapist\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Nurse only access\nexport const NurseOnly = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard role=\"nurse\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Admin only access\nexport const AdminOnly = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard role=\"admin\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Medical staff access (Doctor or Therapist)\nexport const MedicalStaffOnly = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard roles={['doctor', 'therapist']} fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Clinical staff access (Doctor, Therapist, or Nurse)\nexport const ClinicalStaffOnly = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard roles={['doctor', 'therapist', 'nurse']} fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// External services access\nexport const ExternalServicesOnly = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard roles={['external_lab', 'external_radiology']} fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Form-specific permission guards\n\n// Medical diagnosis form access\nexport const MedicalDiagnosisAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"medicalDiagnosis\" category=\"forms\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Progress notes access\nexport const ProgressNotesAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"progressNotes\" category=\"forms\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Clinical indicators access\nexport const ClinicalIndicatorsAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"clinicalIndicators\" category=\"forms\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Lab results access\nexport const LabResultsAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"labResults\" category=\"forms\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Radiology results access\nexport const RadiologyResultsAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"radiologyResults\" category=\"forms\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// System permission guards\n\n// Patient management access\nexport const PatientManagementAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"editPatients\" category=\"system\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// Analytics access\nexport const AnalyticsAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"viewAnalytics\" category=\"system\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\n// User management access\nexport const UserManagementAccess = ({ children, fallback, showFallback = true }) => (\n  <PermissionGuard permission=\"manageUsers\" category=\"system\" fallback={fallback} showFallback={showFallback}>\n    {children}\n  </PermissionGuard>\n);\n\nexport default PermissionGuard;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { usePermissions } from '../../hooks/usePermissions';\nimport PermissionGuard, { AdminOnly } from '../../components/Auth/PermissionGuard';\nimport toast from 'react-hot-toast';\n\nconst PermissionsManagement = () => {\n  const { t, isRTL } = useLanguage();\n  const [users, setUsers] = useState([]);\n  const [selectedUser, setSelectedUser] = useState(null);\n  const [permissions, setPermissions] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [activeTab, setActiveTab] = useState('users');\n\n  // Role definitions with Arabic translations\n  const roleDefinitions = {\n    doctor: {\n      name: t('doctor', 'طبيب'),\n      nameEn: 'Doctor',\n      description: t('doctorPermissions', 'التشخيص الطبي، تقييم العلاج الطبيعي، إعادة التقييم، تقييم الخروج، نموذج المتابعة، نموذج تثقيف المريض'),\n      color: 'blue'\n    },\n    therapist: {\n      name: t('therapist', 'أخصائي العلاج الطبيعي'),\n      nameEn: 'Physical Therapist',\n      description: t('therapistPermissions', 'ملاحظات تقدم الحالة، نموذج تثقيف المريض'),\n      color: 'green'\n    },\n    nurse: {\n      name: t('nurse', 'ممرض/ممرضة'),\n      nameEn: 'Nurse',\n      description: t('nursePermissions', 'نموذج تثقيف المريض، نموذج التمريض التابع للمركز'),\n      color: 'pink'\n    },\n    external_lab: {\n      name: t('externalLab', 'مختبر خارجي'),\n      nameEn: 'External Laboratory',\n      description: t('externalLabPermissions', 'إدخال نتائج المختبر'),\n      color: 'purple'\n    },\n    external_radiology: {\n      name: t('externalRadiology', 'مركز أشعة خارجي'),\n      nameEn: 'External Radiology',\n      description: t('externalRadiologyPermissions', 'إدخال نتائج الأشعة'),\n      color: 'indigo'\n    },\n    receptionist: {\n      name: t('receptionist', 'موظف استقبال'),\n      nameEn: 'Receptionist',\n      description: t('receptionistPermissions', 'تسجيل المرضى، حجز المواعيد'),\n      color: 'yellow'\n    },\n    manager: {\n      name: t('manager', 'مدير'),\n      nameEn: 'Manager',\n      description: t('managerPermissions', 'عرض التحليلات والتقارير، إدارة المستخدمين'),\n      color: 'red'\n    },\n    admin: {\n      name: t('admin', 'مدير النظام'),\n      nameEn: 'Administrator',\n      description: t('adminPermissions', 'جميع الصلاحيات'),\n      color: 'gray'\n    }\n  };\n\n  // Permission categories with Arabic translations\n  const permissionCategories = {\n    forms: {\n      name: t('formsPermissions', 'صلاحيات النماذج'),\n      nameEn: 'Forms Permissions',\n      permissions: {\n        medicalDiagnosis: t('medicalDiagnosis', 'التشخيص الطبي'),\n        ptEvaluation: t('ptEvaluation', 'تقييم العلاج الطبيعي'),\n        reassessment: t('reassessment', 'إعادة التقييم'),\n        dischargeEvaluation: t('dischargeEvaluation', 'تقييم الخروج'),\n        followUpForm: t('followUpForm', 'نموذج المتابعة'),\n        patientEducationDoctor: t('patientEducationDoctor', 'نموذج تثقيف المريض (طبيب)'),\n        progressNotes: t('progressNotes', 'ملاحظات تقدم الحالة'),\n        patientEducationTherapist: t('patientEducationTherapist', 'نموذج تثقيف المريض (أخصائي)'),\n        patientEducationNurse: t('patientEducationNurse', 'نموذج تثقيف المريض (تمريض)'),\n        nursingForm: t('nursingForm', 'نموذج التمريض التابع للمركز'),\n        clinicalIndicators: t('clinicalIndicators', 'المؤشرات السريرية'),\n        labResults: t('labResults', 'نتائج المختبر'),\n        radiologyResults: t('radiologyResults', 'نتائج الأشعة')\n      }\n    },\n    system: {\n      name: t('systemPermissions', 'صلاحيات النظام'),\n      nameEn: 'System Permissions',\n      permissions: {\n        viewPatients: t('viewPatients', 'عرض المرضى'),\n        createPatients: t('createPatients', 'إنشاء مرضى'),\n        editPatients: t('editPatients', 'تعديل المرضى'),\n        deletePatients: t('deletePatients', 'حذف المرضى'),\n        viewAppointments: t('viewAppointments', 'عرض المواعيد'),\n        createAppointments: t('createAppointments', 'إنشاء مواعيد'),\n        editAppointments: t('editAppointments', 'تعديل المواعيد'),\n        deleteAppointments: t('deleteAppointments', 'حذف المواعيد'),\n        viewAnalytics: t('viewAnalytics', 'عرض التحليلات'),\n        manageUsers: t('manageUsers', 'إدارة المستخدمين'),\n        systemSettings: t('systemSettings', 'إعدادات النظام')\n      }\n    },\n    analytics: {\n      name: t('analyticsPermissions', 'صلاحيات التحليلات'),\n      nameEn: 'Analytics Permissions',\n      permissions: {\n        functionalIndependenceComparison: t('functionalIndependenceComparison', 'درجة الاستقلالية الوظيفية قبل وبعد البرنامج العلاجي'),\n        treatmentPlanEffectiveness: t('treatmentPlanEffectiveness', 'نسبة التحسن السريري حسب نوع الخطة العلاجية المستخدمة'),\n        clinicalProgressTracking: t('clinicalProgressTracking', 'تتبع التقدم السريري'),\n        outcomeMetrics: t('outcomeMetrics', 'مقاييس النتائج'),\n        complianceReporting: t('complianceReporting', 'تقارير الامتثال')\n      }\n    }\n  };\n\n  useEffect(() => {\n    loadUsers();\n  }, []);\n\n  const loadUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/v1/users', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setUsers(result.data);\n      } else {\n        // Mock data for demonstration\n        const mockUsers = [\n          {\n            _id: '1',\n            firstName: 'أحمد',\n            lastName: 'محمد',\n            email: '<EMAIL>',\n            role: 'doctor',\n            department: 'العلاج الطبيعي',\n            isActive: true\n          },\n          {\n            _id: '2',\n            firstName: 'فاطمة',\n            lastName: 'علي',\n            email: '<EMAIL>',\n            role: 'therapist',\n            department: 'العلاج الطبيعي',\n            isActive: true\n          },\n          {\n            _id: '3',\n            firstName: 'سارة',\n            lastName: 'أحمد',\n            email: '<EMAIL>',\n            role: 'nurse',\n            department: 'التمريض',\n            isActive: true\n          },\n          {\n            _id: '4',\n            firstName: 'مختبر الشفاء',\n            lastName: '',\n            email: '<EMAIL>',\n            role: 'external_lab',\n            department: 'مختبر خارجي',\n            isActive: true\n          }\n        ];\n        setUsers(mockUsers);\n      }\n    } catch (error) {\n      console.error('Error loading users:', error);\n      toast.error(t('errorLoadingUsers', 'خطأ في تحميل المستخدمين'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadUserPermissions = async (userId) => {\n    try {\n      const response = await fetch(`/api/v1/permissions/user/${userId}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setPermissions(result.data.permissions);\n      } else {\n        // Mock permissions based on role\n        const user = users.find(u => u._id === userId);\n        if (user) {\n          const mockPermissions = getMockPermissionsByRole(user.role);\n          setPermissions(mockPermissions);\n        }\n      }\n    } catch (error) {\n      console.error('Error loading permissions:', error);\n      toast.error(t('errorLoadingPermissions', 'خطأ في تحميل الصلاحيات'));\n    }\n  };\n\n  const getMockPermissionsByRole = (role) => {\n    const rolePermissions = {\n      doctor: {\n        forms: {\n          medicalDiagnosis: true,\n          ptEvaluation: true,\n          reassessment: true,\n          dischargeEvaluation: true,\n          followUpForm: true,\n          patientEducationDoctor: true,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: true,\n          labResults: true,\n          radiologyResults: true\n        },\n        system: {\n          viewPatients: true,\n          createPatients: true,\n          editPatients: true,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: true,\n          editAppointments: true,\n          deleteAppointments: false,\n          viewAnalytics: true,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: true\n        }\n      },\n      therapist: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: true,\n          patientEducationTherapist: true,\n          patientEducationNurse: false,\n          nursingForm: false,\n          clinicalIndicators: true,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: true,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: true,\n          treatmentPlanEffectiveness: true,\n          clinicalProgressTracking: true,\n          outcomeMetrics: true,\n          complianceReporting: false\n        }\n      },\n      nurse: {\n        forms: {\n          medicalDiagnosis: false,\n          ptEvaluation: false,\n          reassessment: false,\n          dischargeEvaluation: false,\n          followUpForm: false,\n          patientEducationDoctor: false,\n          progressNotes: false,\n          patientEducationTherapist: false,\n          patientEducationNurse: true,\n          nursingForm: true,\n          clinicalIndicators: false,\n          labResults: false,\n          radiologyResults: false\n        },\n        system: {\n          viewPatients: true,\n          createPatients: false,\n          editPatients: false,\n          deletePatients: false,\n          viewAppointments: true,\n          createAppointments: false,\n          editAppointments: false,\n          deleteAppointments: false,\n          viewAnalytics: false,\n          manageUsers: false,\n          systemSettings: false\n        },\n        analytics: {\n          functionalIndependenceComparison: false,\n          treatmentPlanEffectiveness: false,\n          clinicalProgressTracking: false,\n          outcomeMetrics: false,\n          complianceReporting: false\n        }\n      }\n    };\n\n    return rolePermissions[role] || rolePermissions.nurse;\n  };\n\n  const handleUserSelect = (user) => {\n    setSelectedUser(user);\n    loadUserPermissions(user._id);\n  };\n\n  const handlePermissionChange = (category, permission, value) => {\n    setPermissions(prev => ({\n      ...prev,\n      [category]: {\n        ...prev[category],\n        [permission]: value\n      }\n    }));\n  };\n\n  const savePermissions = async () => {\n    if (!selectedUser || !permissions) return;\n\n    try {\n      setSaving(true);\n      const response = await fetch(`/api/v1/permissions/user/${selectedUser._id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({ permissions })\n      });\n\n      if (response.ok) {\n        toast.success(t('permissionsSaved', 'تم حفظ الصلاحيات بنجاح'));\n      } else {\n        toast.success(t('permissionsSaved', 'تم حفظ الصلاحيات بنجاح (وضع التجريب)'));\n      }\n    } catch (error) {\n      console.error('Error saving permissions:', error);\n      toast.error(t('errorSavingPermissions', 'خطأ في حفظ الصلاحيات'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const resetToDefaults = async () => {\n    if (!selectedUser) return;\n\n    try {\n      setSaving(true);\n      const response = await fetch(`/api/v1/permissions/user/${selectedUser._id}/reset`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        setPermissions(result.data.permissions);\n        toast.success(t('permissionsReset', 'تم إعادة تعيين الصلاحيات للافتراضية'));\n      } else {\n        const mockPermissions = getMockPermissionsByRole(selectedUser.role);\n        setPermissions(mockPermissions);\n        toast.success(t('permissionsReset', 'تم إعادة تعيين الصلاحيات للافتراضية (وضع التجريب)'));\n      }\n    } catch (error) {\n      console.error('Error resetting permissions:', error);\n      toast.error(t('errorResettingPermissions', 'خطأ في إعادة تعيين الصلاحيات'));\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <AdminOnly>\n      <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n          {t('permissionsManagement', 'إدارة الصلاحيات')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400\">\n          {t('permissionsManagementDesc', 'تحديد صلاحيات إدخال البيانات في النظام الطبي الإلكتروني حسب أدوار الفريق العلاجي')}\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Users List */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('users', 'المستخدمون')}\n              </h3>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-3\">\n                {users.map(user => (\n                  <div\n                    key={user._id}\n                    onClick={() => handleUserSelect(user)}\n                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${\n                      selectedUser?._id === user._id\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'\n                    }`}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                          {user.firstName} {user.lastName}\n                        </h4>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {user.email}\n                        </p>\n                      </div>\n                      <div className=\"text-right\">\n                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-${roleDefinitions[user.role]?.color}-100 text-${roleDefinitions[user.role]?.color}-800 dark:bg-${roleDefinitions[user.role]?.color}-900/30 dark:text-${roleDefinitions[user.role]?.color}-200`}>\n                          {roleDefinitions[user.role]?.name}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Permissions Editor */}\n        <div className=\"lg:col-span-2\">\n          {selectedUser ? (\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n              <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      {t('permissionsFor', 'صلاحيات')} {selectedUser.firstName} {selectedUser.lastName}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {roleDefinitions[selectedUser.role]?.name} - {roleDefinitions[selectedUser.role]?.description}\n                    </p>\n                  </div>\n                  <div className=\"flex space-x-3\">\n                    <button\n                      onClick={resetToDefaults}\n                      disabled={saving}\n                      className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50\"\n                    >\n                      {t('resetToDefaults', 'إعادة للافتراضي')}\n                    </button>\n                    <button\n                      onClick={savePermissions}\n                      disabled={saving}\n                      className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n                    >\n                      {saving ? t('saving', 'جاري الحفظ...') : t('savePermissions', 'حفظ الصلاحيات')}\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {permissions && (\n                <div className=\"p-6\">\n                  {Object.entries(permissionCategories).map(([categoryKey, category]) => (\n                    <div key={categoryKey} className=\"mb-8\">\n                      <h4 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n                        {category.name}\n                      </h4>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        {Object.entries(category.permissions).map(([permissionKey, permissionName]) => (\n                          <div key={permissionKey} className=\"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                            <span className=\"text-sm text-gray-900 dark:text-white\">\n                              {permissionName}\n                            </span>\n                            <label className=\"relative inline-flex items-center cursor-pointer\">\n                              <input\n                                type=\"checkbox\"\n                                checked={permissions[categoryKey]?.[permissionKey] || false}\n                                onChange={(e) => handlePermissionChange(categoryKey, permissionKey, e.target.checked)}\n                                className=\"sr-only peer\"\n                              />\n                              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-600 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                            </label>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          ) : (\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-12\">\n              <div className=\"text-center\">\n                <i className=\"fas fa-user-shield text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('selectUser', 'اختر مستخدم')}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {t('selectUserDesc', 'اختر مستخدماً من القائمة لعرض وتعديل صلاحياته')}\n                </p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n      </div>\n    </AdminOnly>\n  );\n};\n\nexport default PermissionsManagement;\n"], "names": ["usePermissions", "user", "useAuth", "permissions", "setPermissions", "useState", "loading", "setLoading", "useEffect", "loadUserPermissions", "async", "response", "fetch", "headers", "concat", "localStorage", "getItem", "ok", "result", "json", "data", "mockPermissions", "getMockPermissionsByRole", "role", "error", "console", "rolePermissions", "admin", "forms", "medicalDiagnosis", "ptEvaluation", "reassessment", "dischargeEvaluation", "followUpForm", "patientEducationDoctor", "progressNotes", "patientEducationTherapist", "patientEducationNurse", "nursingForm", "clinicalIndicators", "labResults", "radiologyResults", "system", "viewPatients", "createPatients", "editPatients", "deletePatients", "viewAppointments", "createAppointments", "editAppointments", "deleteAppointments", "viewAnalytics", "manageUsers", "systemSettings", "analytics", "functionalIndependenceComparison", "treatmentPlanEffectiveness", "clinicalProgressTracking", "outcomeMetrics", "complianceReporting", "doctor", "therapist", "nurse", "external_lab", "external_radiology", "receptionist", "hasPermission", "category", "permission", "_permissions$category", "hasAnyPermission", "permissionList", "some", "_permissions$category2", "hasRole", "hasAnyRole", "roles", "includes", "userRole", "isAdmin", "isDoctor", "isTherapist", "isNurse", "isExternalLab", "isExternalRadiology", "isReceptionist", "is<PERSON>anager", "PermissionGuard", "_ref", "children", "fallback", "showFallback", "t", "useLanguage", "_jsx", "className", "AccessDenied", "requiredRole", "requiredRoles", "requiredPermission", "_ref2", "roleNames", "manager", "permissionNames", "_jsxs", "map", "join", "AdminOnly", "_ref6", "PermissionsManagement", "_roleDefinitions$sele", "_roleDefinitions$sele2", "isRTL", "users", "setUsers", "selected<PERSON>ser", "setSelectedUser", "saving", "setSaving", "activeTab", "setActiveTab", "roleDefinitions", "name", "nameEn", "description", "color", "permissionCategories", "loadUsers", "_id", "firstName", "lastName", "email", "department", "isActive", "toast", "handleUserSelect", "userId", "find", "u", "_roleDefinitions$user", "_roleDefinitions$user2", "_roleDefinitions$user3", "_roleDefinitions$user4", "_roleDefinitions$user5", "onClick", "method", "success", "disabled", "body", "JSON", "stringify", "Object", "entries", "categoryKey", "<PERSON><PERSON><PERSON>", "permissionName", "type", "checked", "onChange", "e", "handlePermissionChange", "value", "prev", "_objectSpread", "target"], "sourceRoot": ""}