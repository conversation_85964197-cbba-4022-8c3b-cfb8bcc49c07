"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3376],{3376:(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var s=a(2555),r=a(5043),i=a(3216),l=a(4117),n=a(7921),d=a(4528),c=a(3768),o=a(579);const x=e=>{let{patientId:t}=e;const[a,i]=(0,r.useState)(null),[n,d]=(0,r.useState)(!0),[x,m]=(0,r.useState)({visaNumber:"",visaType:"work",serviceIds:[]}),[g,h]=(0,r.useState)(!1),{t:y}=(0,l.Bd)();(0,r.useEffect)(()=>{t&&u()},[t]);const u=async()=>{d(!0);try{const e=await fetch("/api/v1/nphies/status/".concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){const t=await e.json();i(t.data)}else{if(404!==e.status)throw new Error("Failed to load NPHIES status");i(null)}}catch(e){console.error("Error loading NPHIES status:",e),c.Ay.error("Failed to load NPHIES integration status")}finally{d(!1)}},b=e=>{switch(e){case"verified":case"active":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"rejected":case"expired":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}};return n?(0,o.jsxs)("div",{className:"flex items-center justify-center p-6",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:y("loadingNphiesData","Loading NPHIES data...")})]}):(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-shield-alt text-blue-600 dark:text-blue-400 mr-2"}),y("nphiesIntegration","NPHIES Integration")]}),a&&(0,o.jsxs)("button",{onClick:async()=>{try{const e=await fetch("/api/v1/nphies/eligibility/".concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!e.ok){const t=await e.json();throw new Error(t.message||"Failed to verify eligibility")}{const t=await e.json();c.Ay.success("Eligibility verified successfully"),i(e=>(0,s.A)((0,s.A)({},e),{},{eligibilityStatus:t.data.eligibilityStatus,eligibilityResponse:t.data.eligibilityResponse}))}}catch(e){console.error("Error verifying eligibility:",e),c.Ay.error(e.message||"Failed to verify eligibility")}},className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:[(0,o.jsx)("i",{className:"fas fa-sync mr-1"}),y("verifyEligibility","Verify Eligibility")]})]}),a?(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:y("eligibilityStatus","Eligibility Status")}),(0,o.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat(b(a.eligibilityStatus)),children:y(a.eligibilityStatus,a.eligibilityStatus)}),a.lastUpdated&&(0,o.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-2",children:[y("lastUpdated","Last Updated"),": ",new Date(a.lastUpdated).toLocaleDateString()]})]}),(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:y("activeEligibility","Active Eligibility")}),(0,o.jsx)("div",{className:"flex items-center",children:a.hasActiveEligibility?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-check-circle text-green-600 mr-2"}),(0,o.jsx)("span",{className:"text-green-600 dark:text-green-400",children:y("active","Active")})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-times-circle text-red-600 mr-2"}),(0,o.jsx)("span",{className:"text-red-600 dark:text-red-400",children:y("inactive","Inactive")})]})})]})]}),a.insuranceInfo&&(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:y("insuranceInformation","Insurance Information")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("payerName","Payer Name"),":"]}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:a.insuranceInfo.payerName})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("membershipNumber","Membership Number"),":"]}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:a.insuranceInfo.membershipNumber})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("coverageType","Coverage Type"),":"]}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:a.insuranceInfo.coverageType})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("copayAmount","Copay Amount"),":"]}),(0,o.jsxs)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:[a.insuranceInfo.copayAmount," ",y("sar","SAR")]})]})]})]}),(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:y("visaInformation","Visa Information")}),!a.visaInfo&&(0,o.jsxs)("button",{onClick:()=>h(!0),className:"px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-1"}),y("linkVisa","Link Visa")]})]}),a.visaInfo?(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("visaNumber","Visa Number"),":"]}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:a.visaInfo.visaNumber})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("visaType","Visa Type"),":"]}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:y(a.visaInfo.visaType,a.visaInfo.visaType)})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("visaStatus","Status"),":"]}),(0,o.jsx)("span",{className:"ml-2 px-2 py-1 rounded text-xs font-medium ".concat(b(a.visaInfo.status)),children:y(a.visaInfo.status,a.visaInfo.status)})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("span",{className:"font-medium text-gray-700 dark:text-gray-300",children:[y("expiryDate","Expiry Date"),":"]}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:new Date(a.visaInfo.expiryDate).toLocaleDateString()})]})]}):(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:y("noVisaLinked","No visa information linked")})]}),(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:y("claimsSummary","Claims Summary")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:a.claimsCount||0}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:y("totalClaims","Total Claims")})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:a.hasActivePreAuth?"\u2713":"\u2717"}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:y("preAuthorization","Pre-Authorization")})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:a.hasActiveEligibility?"\u2713":"\u2717"}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:y("eligibilityActive","Eligibility Active")})]})]})]})]}):(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:y("noNphiesIntegration","No NPHIES integration found for this patient")}),(0,o.jsx)("button",{onClick:()=>(0,c.Ay)("NPHIES integration setup coming soon"),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:y("setupIntegration","Setup Integration")})]}),g&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:y("linkVisa","Link Visa")}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:y("visaNumber","Visa Number")}),(0,o.jsx)("input",{type:"text",value:x.visaNumber,onChange:e=>m(t=>(0,s.A)((0,s.A)({},t),{},{visaNumber:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",placeholder:y("enterVisaNumber","Enter visa number")})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:y("visaType","Visa Type")}),(0,o.jsxs)("select",{value:x.visaType,onChange:e=>m(t=>(0,s.A)((0,s.A)({},t),{},{visaType:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",children:[(0,o.jsx)("option",{value:"work",children:y("work","Work")}),(0,o.jsx)("option",{value:"visit",children:y("visit","Visit")}),(0,o.jsx)("option",{value:"residence",children:y("residence","Residence")}),(0,o.jsx)("option",{value:"hajj",children:y("hajj","Hajj")}),(0,o.jsx)("option",{value:"umrah",children:y("umrah","Umrah")}),(0,o.jsx)("option",{value:"transit",children:y("transit","Transit")})]})]})]}),(0,o.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,o.jsx)("button",{onClick:()=>h(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200",children:y("cancel","Cancel")}),(0,o.jsx)("button",{onClick:async()=>{if(x.visaNumber)try{const e=await fetch("/api/v1/nphies/visa/link",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify((0,s.A)({patientId:t},x))});if(!e.ok){const t=await e.json();throw new Error(t.message||"Failed to link visa")}{const t=await e.json();c.Ay.success("Visa linked successfully"),i(e=>(0,s.A)((0,s.A)({},e),{},{visaInfo:t.data.visaInfo})),h(!1),m({visaNumber:"",visaType:"work",serviceIds:[]})}}catch(e){console.error("Error linking visa:",e),c.Ay.error(e.message||"Failed to link visa")}else c.Ay.error("Please enter visa number")},className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:y("linkVisa","Link Visa")})]})]})})]})},m=e=>{let{invoiceId:t}=e;const[a,s]=(0,r.useState)(null),[i,n]=(0,r.useState)(!0),[d,x]=(0,r.useState)(!1),{t:m}=(0,l.Bd)();(0,r.useEffect)(()=>{g()},[]);const g=async()=>{n(!0);try{const e=await fetch("/api/v1/zatca/compliance",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!e.ok)throw new Error("Failed to load ZATCA status");{const t=await e.json();s(t.data)}}catch(e){console.error("Error loading ZATCA status:",e),c.Ay.error("Failed to load ZATCA integration status")}finally{n(!1)}},h=e=>{switch(e){case"registered":case"accepted":case"active":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"pending":case"submitted":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"rejected":case"suspended":case"cancelled":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}};if(i)return(0,o.jsxs)("div",{className:"flex items-center justify-center p-6",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}),(0,o.jsx)("span",{className:"ml-2 text-gray-600 dark:text-gray-400",children:m("loadingZatcaData","Loading ZATCA data...")})]});if(!a)return(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("i",{className:"fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:m("noZatcaIntegration","ZATCA integration not configured")}),(0,o.jsx)("button",{onClick:()=>(0,c.Ay)("ZATCA integration setup coming soon"),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:m("setupIntegration","Setup Integration")})]})});const y=(u=a.overallCompliance)>=90?{level:"excellent",color:"text-green-600",icon:"fas fa-check-circle"}:u>=70?{level:"good",color:"text-blue-600",icon:"fas fa-info-circle"}:u>=50?{level:"fair",color:"text-yellow-600",icon:"fas fa-exclamation-triangle"}:{level:"poor",color:"text-red-600",icon:"fas fa-times-circle"};var u;return(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-receipt text-green-600 dark:text-green-400 mr-2"}),m("zatcaIntegration","ZATCA Integration")]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[t&&(0,o.jsx)("button",{onClick:async()=>{if(t){x(!0);try{const e=await fetch("/api/v1/zatca/invoice/submit",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify({invoiceId:t})});if(!e.ok){const t=await e.json();throw new Error(t.message||"Failed to submit invoice to ZATCA")}{const t=await e.json();c.Ay.success("Invoice submitted to ZATCA successfully"),c.Ay.success("ZATCA Invoice Number: ".concat(t.data.zatcaInvoiceNumber)),g()}}catch(e){console.error("Error submitting to ZATCA:",e),c.Ay.error(e.message||"Failed to submit invoice to ZATCA")}finally{x(!1)}}else c.Ay.error("No invoice selected")},disabled:d,className:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50",children:d?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1 inline-block"}),m("submitting","Submitting...")]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-upload mr-1"}),m("submitToZatca","Submit to ZATCA")]})}),(0,o.jsxs)("button",{onClick:async()=>{try{const e=await fetch("/api/v1/zatca/report/generate",{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify({reportType:"monthly",period:{year:(new Date).getFullYear(),month:(new Date).getMonth()+1}})});if(!e.ok){const t=await e.json();throw new Error(t.message||"Failed to generate tax report")}await e.json();c.Ay.success("Tax report generated successfully"),g()}catch(e){console.error("Error generating tax report:",e),c.Ay.error(e.message||"Failed to generate tax report")}},className:"px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700",children:[(0,o.jsx)("i",{className:"fas fa-file-alt mr-1"}),m("generateReport","Generate Report")]})]})]}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:m("registrationStatus","Registration Status")}),(0,o.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat(h(a.registrationStatus)),children:m(a.registrationStatus,a.registrationStatus)})]}),(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:m("certificateStatus","Certificate Status")}),(0,o.jsx)("div",{className:"flex items-center",children:a.certificateValid?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-check-circle text-green-600 mr-2"}),(0,o.jsx)("span",{className:"text-green-600 dark:text-green-400",children:m("valid","Valid")})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-times-circle text-red-600 mr-2"}),(0,o.jsx)("span",{className:"text-red-600 dark:text-red-400",children:m("invalid","Invalid")})]})})]})]}),(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:m("complianceOverview","Compliance Overview")}),(0,o.jsxs)("div",{className:"flex items-center mb-3",children:[(0,o.jsx)("i",{className:"".concat(y.icon," ").concat(y.color," mr-2")}),(0,o.jsxs)("span",{className:"font-medium ".concat(y.color),children:[a.overallCompliance,"% ",m("compliant","Compliant")]}),(0,o.jsxs)("span",{className:"ml-2 text-sm text-gray-500 dark:text-gray-400",children:["(",m(y.level,y.level),")"]})]}),(0,o.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2",children:(0,o.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(a.overallCompliance>=90?"bg-green-600":a.overallCompliance>=70?"bg-blue-600":a.overallCompliance>=50?"bg-yellow-600":"bg-red-600"),style:{width:"".concat(a.overallCompliance,"%")}})})]}),(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:m("submissionStatistics","Submission Statistics")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:a.totalInvoicesSubmitted}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:m("totalSubmitted","Total Submitted")})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:a.successfulSubmissions}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:m("successful","Successful")})]}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"text-2xl font-bold text-red-600 dark:text-red-400",children:a.failedSubmissions}),(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:m("failed","Failed")})]})]})]}),a.complianceChecks&&a.complianceChecks.length>0&&(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:m("recentComplianceChecks","Recent Compliance Checks")}),(0,o.jsx)("div",{className:"space-y-2",children:a.complianceChecks.map((e,t)=>(0,o.jsxs)("div",{className:"flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded",children:[(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("i",{className:"fas ".concat("passed"===e.status?"fa-check-circle text-green-600":"warning"===e.status?"fa-exclamation-triangle text-yellow-600":"fa-times-circle text-red-600"," mr-2")}),(0,o.jsx)("span",{className:"text-sm text-gray-900 dark:text-white",children:m(e.checkType,e.checkType)})]}),(0,o.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(h(e.status)),children:m(e.status,e.status)})]},t))})]}),a.lastSyncDate&&(0,o.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 p-4 rounded-lg",children:[(0,o.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:m("lastSyncInformation","Last Sync Information")}),(0,o.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[m("lastSyncDate","Last Sync"),": ",new Date(a.lastSyncDate).toLocaleString()]})]}),(0,o.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800",children:(0,o.jsxs)("div",{className:"flex items-start",children:[(0,o.jsx)("i",{className:"fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2 mt-1"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h4",{className:"font-medium text-blue-900 dark:text-blue-100 mb-1",children:m("zatcaCompliance","ZATCA Compliance")}),(0,o.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:m("zatcaComplianceInfo","All invoices are automatically generated with ZATCA-compliant QR codes and digital signatures. Tax calculations follow Saudi VAT regulations.")})]})]})})]})]})},g=()=>{var e,t;const{id:a}=(0,i.g)(),g=(0,i.Zp)(),{t:h}=(0,l.Bd)(),{isRTL:y}=(0,n.o)(),{user:u}=(0,d.A)(),[b,p]=(0,r.useState)(null),[f,j]=(0,r.useState)([]),[v,N]=(0,r.useState)(!0),[k,w]=(0,r.useState)(null),[S,A]=(0,r.useState)(!1),[I,C]=(0,r.useState)(!1),[D,T]=(0,r.useState)("details");(0,r.useEffect)(()=>{P(),E()},[a]);const P=async()=>{N(!0);try{if(/^[0-9a-fA-F]{24}$/.test(a)){const e=await fetch("/api/v1/billing/".concat(a),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){const t=await e.json();return p(t.data),void j(t.data.payments||[])}throw new Error("Failed to load invoice details")}throw new Error("Using mock data for demo")}catch(e){console.error("Error loading invoice:",e),console.log("Using mock data for invoice:",a);p({_id:a,invoiceNumber:"INV-2024-001",patient:{firstName:"\u0623\u062d\u0645\u062f",lastName:"\u0645\u062d\u0645\u062f \u0639\u0644\u064a",nationalId:"**********",phone:"+966501234567",email:"<EMAIL>"},services:[{name:"Physical Therapy Session",description:"Initial assessment and treatment",quantity:2,unitPrice:500,total:1e3},{name:"Exercise Program",description:"Customized exercise plan",quantity:1,unitPrice:300,total:300}],subtotal:1300,tax:{rate:15,amount:195},discount:{type:"fixed",amount:50},total:1445,status:"sent",issueDate:"2024-01-15",dueDate:"2024-02-14",notes:"Initial treatment package for lower back pain",createdBy:{firstName:"Dr. Sarah",lastName:"Ahmed"}}),j([{_id:"1",amount:500,method:"cash",date:"2024-01-20",reference:"CASH001",receivedBy:{firstName:"Admin",lastName:"User"}}])}finally{N(!1)}},E=async()=>{try{if(/^[0-9a-fA-F]{24}$/.test(a)){const e=await fetch("/api/v1/analytics/invoice/".concat(a),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){const t=await e.json();return void w(t.data)}}w({viewCount:Math.floor(15*Math.random())+5,emailsSent:Math.floor(3*Math.random())+1,lastViewed:(new Date).toISOString(),paymentHistory:[{date:"2024-01-20",amount:500,method:"cash"}],relatedInvoices:Math.floor(5*Math.random())+2,patientTotalSpent:Math.floor(5e3*Math.random())+2e3})}catch(e){console.error("Error loading analytics:",e),w({viewCount:5,emailsSent:2,lastViewed:"2024-01-22T10:30:00Z",paymentHistory:[{date:"2024-01-20",amount:500,method:"cash"}],relatedInvoices:3,patientTotalSpent:2500})}};return v?(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):b?(0,o.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6 print:bg-white print:p-0",children:[(0,o.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6",children:(0,o.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4 mb-4 lg:mb-0",children:[(0,o.jsx)("button",{onClick:()=>g("/billing/invoice"),className:"text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white",children:(0,o.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h("invoiceDetails","Invoice Details")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:b.invoiceNumber})]})]}),(0,o.jsx)("div",{className:"flex items-center space-x-4",children:(0,o.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat((e=>{switch(e){case"paid":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"sent":return"text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400";case"pending":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";case"overdue":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}})(b.status)),children:h(b.status,b.status)})})]})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6",children:(0,o.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,o.jsxs)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:[(0,o.jsxs)("button",{onClick:()=>T("details"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("details"===D?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-file-invoice mr-2"}),h("invoiceDetails","Invoice Details")]}),(0,o.jsxs)("button",{onClick:()=>T("integrations"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("integrations"===D?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-link mr-2"}),h("integrations","Integrations")]}),(0,o.jsxs)("button",{onClick:()=>T("signatures"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("signatures"===D?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,o.jsx)("i",{className:"fas fa-signature mr-2"}),h("signatures","Signatures")]})]})})}),"details"===D&&(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 print:grid-cols-1",children:[(0,o.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice text-blue-600 dark:text-blue-400 mr-2"}),h("invoiceInformation","Invoice Information")]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:h("patientDetails","Patient Details")}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("name","Name"),":"]})," ",b.patient.firstName," ",b.patient.lastName]}),(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("nationalId","National ID"),":"]})," ",b.patient.nationalId]}),(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("phone","Phone"),":"]})," ",b.patient.phone]}),(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("email","Email"),":"]})," ",b.patient.email]})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:h("invoiceDetails","Invoice Details")}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("invoiceNumber","Invoice #"),":"]})," ",b.invoiceNumber]}),(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("issueDate","Issue Date"),":"]})," ",new Date(b.issueDate).toLocaleDateString()]}),(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("dueDate","Due Date"),":"]})," ",new Date(b.dueDate).toLocaleDateString()]}),(0,o.jsxs)("p",{className:"text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{className:"font-medium",children:[h("createdBy","Created By"),":"]})," ",null===(e=b.createdBy)||void 0===e?void 0:e.firstName," ",null===(t=b.createdBy)||void 0===t?void 0:t.lastName]})]})]})]}),b.notes&&(0,o.jsxs)("div",{className:"mt-6",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:h("notes","Notes")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg",children:b.notes})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-list text-blue-600 dark:text-blue-400 mr-2"}),h("services","Services")]}),(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full",children:[(0,o.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("service","Service")}),(0,o.jsx)("th",{className:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("quantity","Qty")}),(0,o.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("unitPrice","Unit Price")}),(0,o.jsx)("th",{className:"px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("total","Total")})]})}),(0,o.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:b.services.map((e,t)=>(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"px-4 py-4",children:(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.name}),(0,o.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description})]})}),(0,o.jsx)("td",{className:"px-4 py-4 text-center text-sm text-gray-900 dark:text-white",children:e.quantity}),(0,o.jsxs)("td",{className:"px-4 py-4 text-right text-sm text-gray-900 dark:text-white",children:[e.unitPrice.toLocaleString()," ",h("sar","SAR")]}),(0,o.jsxs)("td",{className:"px-4 py-4 text-right text-sm font-medium text-gray-900 dark:text-white",children:[e.total.toLocaleString()," ",h("sar","SAR")]})]},t))})]})}),(0,o.jsx)("div",{className:"mt-6 border-t border-gray-200 dark:border-gray-600 pt-6",children:(0,o.jsx)("div",{className:"flex justify-end",children:(0,o.jsxs)("div",{className:"w-64 space-y-2",children:[(0,o.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{children:[h("subtotal","Subtotal"),":"]}),(0,o.jsxs)("span",{children:[b.subtotal.toLocaleString()," ",h("sar","SAR")]})]}),b.discount&&b.discount.amount>0&&(0,o.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{children:[h("discount","Discount"),":"]}),(0,o.jsxs)("span",{children:["-",b.discount.amount.toLocaleString()," ",h("sar","SAR")]})]}),(0,o.jsxs)("div",{className:"flex justify-between text-sm text-gray-600 dark:text-gray-400",children:[(0,o.jsxs)("span",{children:[h("tax","Tax")," (",b.tax.rate,"%):"]}),(0,o.jsxs)("span",{children:[b.tax.amount.toLocaleString()," ",h("sar","SAR")]})]}),(0,o.jsxs)("div",{className:"flex justify-between text-lg font-bold text-gray-900 dark:text-white border-t border-gray-200 dark:border-gray-600 pt-2",children:[(0,o.jsxs)("span",{children:[h("total","Total"),":"]}),(0,o.jsxs)("span",{children:[b.total.toLocaleString()," ",h("sar","SAR")]})]})]})})})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-credit-card text-blue-600 dark:text-blue-400 mr-2"}),h("paymentHistory","Payment History")]}),f.length>0?(0,o.jsx)("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"w-full",children:[(0,o.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,o.jsxs)("tr",{children:[(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("date","Date")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("amount","Amount")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("method","Method")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("reference","Reference")}),(0,o.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:h("receivedBy","Received By")})]})}),(0,o.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:f.map(e=>{var t,a;return(0,o.jsxs)("tr",{children:[(0,o.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:new Date(e.date).toLocaleDateString()}),(0,o.jsxs)("td",{className:"px-4 py-4 text-sm font-medium text-gray-900 dark:text-white",children:[e.amount.toLocaleString()," ",h("sar","SAR")]}),(0,o.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:(0,o.jsx)("span",{className:"capitalize",children:e.method})}),(0,o.jsx)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:e.reference||"-"}),(0,o.jsxs)("td",{className:"px-4 py-4 text-sm text-gray-900 dark:text-white",children:[null===(t=e.receivedBy)||void 0===t?void 0:t.firstName," ",null===(a=e.receivedBy)||void 0===a?void 0:a.lastName]})]},e._id)})})]})}):(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("i",{className:"fas fa-credit-card text-4xl text-gray-400 mb-4"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:h("noPaymentsFound","No payments found for this invoice")})]}),(0,o.jsx)("div",{className:"mt-6 border-t border-gray-200 dark:border-gray-600 pt-6",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,o.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",children:[(0,o.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:h("totalPaid","Total Paid")}),(0,o.jsxs)("div",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:[f.reduce((e,t)=>e+t.amount,0).toLocaleString()," ",h("sar","SAR")]})]}),(0,o.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg",children:[(0,o.jsx)("div",{className:"text-sm text-yellow-600 dark:text-yellow-400 font-medium",children:h("remainingBalance","Remaining Balance")}),(0,o.jsxs)("div",{className:"text-2xl font-bold text-yellow-900 dark:text-yellow-100",children:[(b.total-f.reduce((e,t)=>e+t.amount,0)).toLocaleString()," ",h("sar","SAR")]})]}),(0,o.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg",children:[(0,o.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400 font-medium",children:h("paymentStatus","Payment Status")}),(0,o.jsx)("div",{className:"text-lg font-bold text-green-900 dark:text-green-100",children:f.reduce((e,t)=>e+t.amount,0)>=b.total?h("fullyPaid","Fully Paid"):h("partiallyPaid","Partially Paid")})]})]})})]})]}),(0,o.jsxs)("div",{className:"space-y-6 print:hidden",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-cogs text-blue-600 dark:text-blue-400 mr-2"}),h("actions","Actions")]}),(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsxs)("button",{onClick:async()=>{try{if(/^[0-9a-fA-F]{24}$/.test(a)){if(!(await fetch("/api/v1/billing/".concat(a,"/send"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}})).ok)throw new Error("Failed to send invoice");c.Ay.success("Invoice sent successfully"),P()}else c.Ay.success("Invoice sent successfully (Demo Mode)"),p(e=>(0,s.A)((0,s.A)({},e),{},{status:"sent"}))}catch(e){c.Ay.error("Failed to send invoice")}},className:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-paper-plane mr-2"}),h("sendInvoice","Send Invoice")]}),(0,o.jsxs)("button",{onClick:()=>{window.print()},className:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-print mr-2"}),h("printInvoice","Print Invoice")]}),(0,o.jsxs)("button",{onClick:async()=>{try{if(/^[0-9a-fA-F]{24}$/.test(a)){if(!(await fetch("/api/v1/billing/".concat(a,"/pdf"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}})).ok)throw new Error("Failed to generate PDF");c.Ay.success("PDF download will be available soon")}else c.Ay.success("PDF generated successfully (Demo Mode)"),console.log("Would generate PDF for invoice:",b.invoiceNumber)}catch(e){c.Ay.error("Failed to download PDF")}},className:"w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-download mr-2"}),h("downloadPDF","Download PDF")]}),("pending"===b.status||"sent"===b.status)&&(0,o.jsxs)("button",{onClick:async()=>{try{if(/^[0-9a-fA-F]{24}$/.test(a)){if(!(await fetch("/api/v1/billing/".concat(a,"/mark-paid"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"},body:JSON.stringify({paymentMethod:"cash",notes:"Marked as paid from invoice detail"})})).ok)throw new Error("Failed to mark as paid");c.Ay.success("Invoice marked as paid"),P()}else{c.Ay.success("Invoice marked as paid (Demo Mode)"),p(e=>(0,s.A)((0,s.A)({},e),{},{status:"paid"}));const e={_id:Date.now().toString(),amount:b.total-f.reduce((e,t)=>e+t.amount,0),method:"cash",date:(new Date).toISOString(),reference:"DEMO-"+Date.now(),receivedBy:{firstName:"Demo",lastName:"User"}};j(t=>[...t,e])}}catch(e){c.Ay.error("Failed to mark as paid")}},className:"w-full flex items-center justify-center px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-check-circle mr-2"}),h("markAsPaid","Mark as Paid")]}),(0,o.jsxs)("button",{onClick:()=>C(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),h("addPayment","Add Payment")]}),"pending"===b.status&&(0,o.jsxs)("button",{onClick:()=>A(!0),className:"w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,o.jsx)("i",{className:"fas fa-edit mr-2"}),h("editInvoice","Edit Invoice")]})]})]}),k&&(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-chart-line text-blue-600 dark:text-blue-400 mr-2"}),h("analytics","Analytics")]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:h("viewCount","View Count")}),(0,o.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:k.viewCount})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:h("emailsSent","Emails Sent")}),(0,o.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:k.emailsSent})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:h("lastViewed","Last Viewed")}),(0,o.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:new Date(k.lastViewed).toLocaleDateString()})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:h("relatedInvoices","Related Invoices")}),(0,o.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:k.relatedInvoices})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:h("patientTotalSpent","Patient Total Spent")}),(0,o.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:[k.patientTotalSpent.toLocaleString()," ",h("sar","SAR")]})]})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,o.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,o.jsx)("i",{className:"fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2"}),h("quickStats","Quick Stats")]}),(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg",children:[(0,o.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400 font-medium",children:h("invoiceAge","Invoice Age")}),(0,o.jsxs)("div",{className:"text-lg font-bold text-blue-900 dark:text-blue-100",children:[Math.floor((new Date-new Date(b.issueDate))/864e5)," ",h("days","days")]})]}),(0,o.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 p-3 rounded-lg",children:[(0,o.jsx)("div",{className:"text-sm text-green-600 dark:text-green-400 font-medium",children:h("paymentProgress","Payment Progress")}),(0,o.jsxs)("div",{className:"text-lg font-bold text-green-900 dark:text-green-100",children:[Math.round(f.reduce((e,t)=>e+t.amount,0)/b.total*100),"%"]}),(0,o.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2",children:(0,o.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(Math.round(f.reduce((e,t)=>e+t.amount,0)/b.total*100),"%")}})})]}),(0,o.jsxs)("div",{className:"p-3 rounded-lg ".concat(new Date(b.dueDate)<new Date?"bg-red-50 dark:bg-red-900/20":"bg-yellow-50 dark:bg-yellow-900/20"),children:[(0,o.jsx)("div",{className:"text-sm font-medium ".concat(new Date(b.dueDate)<new Date?"text-red-600 dark:text-red-400":"text-yellow-600 dark:text-yellow-400"),children:new Date(b.dueDate)<new Date?h("overdue","Overdue"):h("daysUntilDue","Days Until Due")}),(0,o.jsxs)("div",{className:"text-lg font-bold ".concat(new Date(b.dueDate)<new Date?"text-red-900 dark:text-red-100":"text-yellow-900 dark:text-yellow-100"),children:[Math.abs(Math.floor((new Date(b.dueDate)-new Date)/864e5))," ",h("days","days")]})]})]})]})]})]}),"integrations"===D&&(0,o.jsx)("div",{className:"space-y-6",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,o.jsx)(x,{patientId:b.patient._id}),(0,o.jsx)(m,{invoiceId:b._id})]})}),"signatures"===D&&(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,o.jsxs)("div",{className:"text-center py-8",children:[(0,o.jsx)("i",{className:"fas fa-signature text-4xl text-gray-400 mb-4"}),(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:h("electronicSignatures","Electronic Signatures")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:h("signatureFeatureInfo","Electronic signature functionality for invoice approval and patient consent")}),(0,o.jsxs)("button",{onClick:()=>(0,c.Ay)("Electronic signature feature coming soon"),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:[(0,o.jsx)("i",{className:"fas fa-plus mr-2"}),h("createSignatureDocument","Create Signature Document")]})]})})]}),I&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:h("addPayment","Add Payment")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:h("paymentModalPlaceholder","Payment form will be implemented here")}),(0,o.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,o.jsx)("button",{onClick:()=>C(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200",children:h("cancel","Cancel")}),(0,o.jsx)("button",{onClick:()=>{C(!1),c.Ay.success("Payment functionality coming soon")},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:h("addPayment","Add Payment")})]})]})}),S&&(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:h("editInvoice","Edit Invoice")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:h("editModalPlaceholder","Invoice editing form will be implemented here")}),(0,o.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,o.jsx)("button",{onClick:()=>A(!1),className:"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200",children:h("cancel","Cancel")}),(0,o.jsx)("button",{onClick:()=>{A(!1),c.Ay.success("Edit functionality coming soon")},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:h("saveChanges","Save Changes")})]})]})})]}):(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("i",{className:"fas fa-file-invoice text-4xl text-gray-400 mb-4"}),(0,o.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:h("invoiceNotFound","Invoice not found")}),(0,o.jsx)("button",{onClick:()=>g("/billing/invoice"),className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:h("backToInvoices","Back to Invoices")})]})})}}}]);
//# sourceMappingURL=3376.af5a878f.chunk.js.map