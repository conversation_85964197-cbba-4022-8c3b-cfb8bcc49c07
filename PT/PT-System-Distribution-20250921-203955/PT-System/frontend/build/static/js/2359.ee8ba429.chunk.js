"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2359],{2359:(e,t,s)=>{s.r(t),s.d(t,{default:()=>d});var r=s(5043),a=s(7921),l=s(579);const d=()=>{const{t:e}=(0,a.o)(),[t,s]=(0,r.useState)({totalForms:11,totalSubmissions:0,completionRate:0,averageTime:0,popularForms:[],recentActivity:[]});return(0,r.useEffect)(()=>{s({totalForms:11,totalSubmissions:156,completionRate:87.5,averageTime:18.5,popularForms:[{name:"PT Adult Initial Assessment",submissions:45,percentage:28.8},{name:"Daily Progress Note",submissions:38,percentage:24.4},{name:"Pain Assessment Scale",submissions:25,percentage:16},{name:"Treatment Plan & Goals",submissions:22,percentage:14.1},{name:"Home Exercise Program",submissions:18,percentage:11.5}],recentActivity:[{form:"PT Adult Initial Assessment",patient:"<PERSON>",time:"2 hours ago",status:"completed"},{form:"Daily Progress Note",patient:"<PERSON>",time:"4 hours ago",status:"completed"},{form:"Pain Assessment Scale",patient:"Mike <PERSON>",time:"6 hours ago",status:"completed"},{form:"Treatment Plan & Goals",patient:"Sarah Wilson",time:"8 hours ago",status:"completed"},{form:"Follow Up Plan",patient:"David Brown",time:"1 day ago",status:"completed"}]})},[]),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsx)("div",{className:"bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8",children:(0,l.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,l.jsx)("div",{className:"px-6 py-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:e("formAnalytics","Form Analytics")}),(0,l.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-chart-bar text-blue-500 mr-2"}),e("formAnalyticsDescription","Comprehensive insights into form usage and performance")]})]}),(0,l.jsxs)("div",{className:"bg-gradient-to-r from-blue-400 to-purple-400 text-white px-4 py-2 rounded-full shadow-lg",children:[(0,l.jsx)("i",{className:"fas fa-analytics mr-2"}),e("analytics","Analytics")]})]})})})}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,l.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-file-alt text-blue-600 dark:text-blue-400 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400",children:e("totalForms","Total Forms")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:t.totalForms})]})]})}),(0,l.jsx)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-paper-plane text-green-600 dark:text-green-400 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:e("totalSubmissions","Total Submissions")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:t.totalSubmissions})]})]})}),(0,l.jsx)("div",{className:"bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg shadow-lg border border-purple-200 dark:border-purple-700 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-percentage text-purple-600 dark:text-purple-400 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-purple-600 dark:text-purple-400",children:e("completionRate","Completion Rate")}),(0,l.jsxs)("p",{className:"text-2xl font-bold text-purple-900 dark:text-purple-100",children:[t.completionRate,"%"]})]})]})}),(0,l.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg",children:(0,l.jsx)("i",{className:"fas fa-clock text-orange-600 dark:text-orange-400 text-2xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-orange-600 dark:text-orange-400",children:e("averageTime","Avg. Time")}),(0,l.jsxs)("p",{className:"text-2xl font-bold text-orange-900 dark:text-orange-100",children:[t.averageTime,"m"]})]})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,l.jsxs)("div",{className:"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6",children:[(0,l.jsxs)("h2",{className:"text-lg font-semibold text-emerald-900 dark:text-emerald-100 mb-6 flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-star text-emerald-600 dark:text-emerald-400 mr-2"}),e("popularForms","Most Popular Forms")]}),(0,l.jsx)("div",{className:"space-y-4",children:t.popularForms.map((e,t)=>(0,l.jsxs)("div",{className:"bg-white dark:bg-emerald-800/20 border border-emerald-200 dark:border-emerald-600 rounded-lg p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)("h3",{className:"font-medium text-emerald-900 dark:text-emerald-100",children:e.name}),(0,l.jsxs)("span",{className:"text-sm text-emerald-600 dark:text-emerald-400",children:[e.submissions," submissions"]})]}),(0,l.jsx)("div",{className:"w-full bg-emerald-200 dark:bg-emerald-800 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-gradient-to-r from-emerald-500 to-teal-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.percentage,"%")}})}),(0,l.jsxs)("p",{className:"text-xs text-emerald-600 dark:text-emerald-400 mt-1",children:[e.percentage,"% of total submissions"]})]},t))})]}),(0,l.jsxs)("div",{className:"bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-violet-200 dark:border-violet-700 p-6",children:[(0,l.jsxs)("h2",{className:"text-lg font-semibold text-violet-900 dark:text-violet-100 mb-6 flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-history text-violet-600 dark:text-violet-400 mr-2"}),e("recentActivity","Recent Activity")]}),(0,l.jsx)("div",{className:"space-y-4",children:t.recentActivity.map((e,t)=>(0,l.jsx)("div",{className:"bg-white dark:bg-violet-800/20 border border-violet-200 dark:border-violet-600 rounded-lg p-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium text-violet-900 dark:text-violet-100",children:e.form}),(0,l.jsxs)("p",{className:"text-sm text-violet-600 dark:text-violet-400",children:["Patient: ",e.patient]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200",children:[(0,l.jsx)("i",{className:"fas fa-check-circle mr-1"}),e.status]}),(0,l.jsx)("p",{className:"text-xs text-violet-500 dark:text-violet-400 mt-1",children:e.time})]})]})},t))})]})]}),(0,l.jsxs)("div",{className:"mt-8 bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-chart-line text-gray-600 dark:text-gray-400 mr-2"}),e("additionalInsights","Additional Insights")]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"p-4 bg-blue-100 dark:bg-blue-900/40 rounded-lg mb-3",children:(0,l.jsx)("i",{className:"fas fa-users text-blue-600 dark:text-blue-400 text-3xl"})}),(0,l.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e("activeUsers","Active Users")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:"24"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("thisWeek","This week")})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"p-4 bg-green-100 dark:bg-green-900/40 rounded-lg mb-3",children:(0,l.jsx)("i",{className:"fas fa-download text-green-600 dark:text-green-400 text-3xl"})}),(0,l.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e("pdfDownloads","PDF Downloads")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:"89"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("thisMonth","This month")})]}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"p-4 bg-purple-100 dark:bg-purple-900/40 rounded-lg mb-3",children:(0,l.jsx)("i",{className:"fas fa-clock text-purple-600 dark:text-purple-400 text-3xl"})}),(0,l.jsx)("h3",{className:"font-medium text-gray-900 dark:text-gray-100",children:e("avgSessionTime","Avg. Session Time")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:"12.3m"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("perForm","Per form")})]})]})]})]})}}}]);
//# sourceMappingURL=2359.ee8ba429.chunk.js.map