"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[7223],{7223:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s=a(2555),r=a(5043),o=a(7921),i=a(4528),c=a(3768),n=a(579);const l=()=>{const{t:e,isRTL:t}=(0,o.o)(),{user:a}=(0,i.A)(),[l,d]=(0,r.useState)(!1),[m,x]=(0,r.useState)({carf:{},cbahi:{},hipaa:{},nphies:{}}),[u,g]=(0,r.useState)("carf"),[p,h]=(0,r.useState)("month");(0,r.useEffect)(()=>{b()},[u,p]);const b=async()=>{try{d(!0);const e=await fetch("/api/v1/analytics/compliance?type=".concat(u,"&period=").concat(p),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(e.ok){const t=await e.json();x(e=>(0,s.A)((0,s.A)({},e),{},{[u]:t.data||{}}))}else{const e={carf:{overallScore:94.5,lastAudit:"2024-01-15",nextAudit:"2024-07-15",standards:{Leadership:{score:96,status:"compliant",issues:0},"Human Resources":{score:92,status:"compliant",issues:1},"Business Practices":{score:98,status:"compliant",issues:0},"Quality Improvement":{score:89,status:"needs-attention",issues:2},"Health & Safety":{score:95,status:"compliant",issues:0},"Rights of Persons Served":{score:97,status:"compliant",issues:0}},recentActions:[{date:"2024-03-15",action:"Updated quality improvement procedures",status:"completed"},{date:"2024-03-10",action:"Staff training on new protocols",status:"completed"},{date:"2024-03-05",action:"Documentation review",status:"in-progress"}]},cbahi:{overallScore:91.2,lastAudit:"2024-02-01",nextAudit:"2024-08-01",standards:{"Patient Safety":{score:93,status:"compliant",issues:1},"Quality Management":{score:88,status:"needs-attention",issues:3},"Infection Control":{score:95,status:"compliant",issues:0},"Medication Management":{score:90,status:"compliant",issues:1},"Clinical Governance":{score:92,status:"compliant",issues:1},"Human Resources":{score:89,status:"needs-attention",issues:2}},recentActions:[{date:"2024-03-20",action:"Infection control audit",status:"completed"},{date:"2024-03-18",action:"Quality metrics review",status:"completed"},{date:"2024-03-12",action:"Staff competency assessment",status:"in-progress"}]},hipaa:{overallScore:97.8,lastAudit:"2024-01-30",nextAudit:"2024-07-30",standards:{"Administrative Safeguards":{score:98,status:"compliant",issues:0},"Physical Safeguards":{score:96,status:"compliant",issues:1},"Technical Safeguards":{score:99,status:"compliant",issues:0},"Privacy Rule":{score:97,status:"compliant",issues:0},"Security Rule":{score:98,status:"compliant",issues:0},"Breach Notification":{score:100,status:"compliant",issues:0}},recentActions:[{date:"2024-03-22",action:"Security risk assessment",status:"completed"},{date:"2024-03-15",action:"Privacy training update",status:"completed"},{date:"2024-03-08",action:"Access control review",status:"completed"}]},nphies:{overallScore:88.5,lastAudit:"2024-02-15",nextAudit:"2024-08-15",standards:{"Data Integration":{score:90,status:"compliant",issues:1},"Claims Processing":{score:85,status:"needs-attention",issues:3},"Prior Authorization":{score:92,status:"compliant",issues:1},"Eligibility Verification":{score:87,status:"needs-attention",issues:2},"Payment Processing":{score:89,status:"compliant",issues:1},"Reporting Standards":{score:88,status:"needs-attention",issues:2}},recentActions:[{date:"2024-03-25",action:"Claims processing optimization",status:"in-progress"},{date:"2024-03-20",action:"Integration testing",status:"completed"},{date:"2024-03-15",action:"Reporting format update",status:"completed"}]}};x(t=>(0,s.A)((0,s.A)({},t),{},{[u]:e[u]||{}}))}}catch(t){console.error("Error loading compliance data:",t),c.Ay.error(e("errorLoadingCompliance","Error loading compliance data"))}finally{d(!1)}},f=async t=>{try{d(!0);const a=await fetch("/api/v1/analytics/export?type=compliance&format=".concat(t,"&compliance=").concat(u),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token"))}});if(!a.ok)throw new Error("Export failed");{const s=await a.blob(),r=window.URL.createObjectURL(s),o=document.createElement("a");o.href=r,o.download="".concat(u,"-compliance-report.").concat(t),document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(r),document.body.removeChild(o),c.Ay.success(e("reportExported","Report exported successfully"))}}catch(a){console.error("Error exporting report:",a),c.Ay.error(e("errorExporting","Error exporting report"))}finally{d(!1)}},y=[{id:"carf",label:"CARF Compliance",icon:"fas fa-certificate",description:"Commission on Accreditation of Rehabilitation Facilities",color:"blue"},{id:"cbahi",label:"CBAHI Compliance",icon:"fas fa-shield-alt",description:"Central Board for Accreditation of Healthcare Institutions",color:"green"},{id:"hipaa",label:"HIPAA Compliance",icon:"fas fa-lock",description:"Health Insurance Portability and Accountability Act",color:"purple"},{id:"nphies",label:"NPHIES Compliance",icon:"fas fa-network-wired",description:"National Platform for Health Information Exchange Services",color:"orange"}],v=e=>{switch(e){case"compliant":return"text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30";case"needs-attention":return"text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30";case"non-compliant":return"text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30";default:return"text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-900/30"}},k=m[u]||{},j=y.find(e=>e.id===u);return(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,n.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8",children:(0,n.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,n.jsx)("div",{className:"px-6 py-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent",children:e("complianceReports","Compliance Reports")}),(0,n.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-shield-check text-indigo-500 mr-2"}),e("complianceReportsDesc","Regulatory compliance monitoring and reporting")]})]}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsxs)("button",{onClick:()=>f("pdf"),disabled:l,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-file-pdf mr-2"}),e("exportPDF","Export PDF")]}),(0,n.jsxs)("button",{onClick:()=>f("excel"),disabled:l,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-file-excel mr-2"}),e("exportExcel","Export Excel")]})]})]})})})}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:y.map(t=>(0,n.jsxs)("button",{onClick:()=>g(t.id),className:"p-6 rounded-lg border-2 transition-all duration-200 text-left ".concat(u===t.id?"border-".concat(t.color,"-500 bg-").concat(t.color,"-50 dark:bg-").concat(t.color,"-900/20"):"border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-gray-300 dark:hover:border-gray-600"),children:[(0,n.jsxs)("div",{className:"flex items-center mb-3",children:[(0,n.jsx)("div",{className:"p-3 bg-".concat(t.color,"-100 dark:bg-").concat(t.color,"-900/40 rounded-lg mr-3"),children:(0,n.jsx)("i",{className:"".concat(t.icon," text-").concat(t.color,"-600 dark:text-").concat(t.color,"-400 text-xl")})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white",children:t.label}),k.overallScore&&(0,n.jsxs)("p",{className:"text-sm font-medium text-".concat(t.color,"-600 dark:text-").concat(t.color,"-400"),children:[k.overallScore,"% ",e("compliant","Compliant")]})]})]}),(0,n.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:t.description})]},t.id))}),l?(0,n.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"})}):(0,n.jsxs)("div",{className:"space-y-8",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,n.jsx)("div",{className:"bg-gradient-to-r from-".concat(null===j||void 0===j?void 0:j.color,"-50 to-").concat(null===j||void 0===j?void 0:j.color,"-100 dark:from-").concat(null===j||void 0===j?void 0:j.color,"-900/20 dark:to-").concat(null===j||void 0===j?void 0:j.color,"-800/20 rounded-lg p-6 border border-").concat(null===j||void 0===j?void 0:j.color,"-200 dark:border-").concat(null===j||void 0===j?void 0:j.color,"-700"),children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-".concat(null===j||void 0===j?void 0:j.color,"-100 dark:bg-").concat(null===j||void 0===j?void 0:j.color,"-900/40 rounded-lg"),children:(0,n.jsx)("i",{className:"fas fa-chart-line text-".concat(null===j||void 0===j?void 0:j.color,"-600 dark:text-").concat(null===j||void 0===j?void 0:j.color,"-400 text-2xl")})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-".concat(null===j||void 0===j?void 0:j.color,"-600 dark:text-").concat(null===j||void 0===j?void 0:j.color,"-400"),children:e("overallScore","Overall Score")}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-".concat(null===j||void 0===j?void 0:j.color,"-900 dark:text-").concat(null===j||void 0===j?void 0:j.color,"-100"),children:[k.overallScore||0,"%"]})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg",children:(0,n.jsx)("i",{className:"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-2xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-blue-600 dark:text-blue-400",children:e("lastAudit","Last Audit")}),(0,n.jsx)("p",{className:"text-lg font-bold text-blue-900 dark:text-blue-100",children:k.lastAudit?new Date(k.lastAudit).toLocaleDateString():"N/A"})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg",children:(0,n.jsx)("i",{className:"fas fa-calendar-alt text-orange-600 dark:text-orange-400 text-2xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("h3",{className:"text-sm font-medium text-orange-600 dark:text-orange-400",children:e("nextAudit","Next Audit")}),(0,n.jsx)("p",{className:"text-lg font-bold text-orange-900 dark:text-orange-100",children:k.nextAudit?new Date(k.nextAudit).toLocaleDateString():"N/A"})]})]})})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("standardsCompliance","Standards Compliance")}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Object.entries(k.standards||{}).map(t=>{let[a,s]=t;return(0,n.jsxs)("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg p-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:a}),(0,n.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(v(s.status)),children:s.status})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[s.score,"%"]}),s.issues>0&&(0,n.jsxs)("span",{className:"text-sm text-red-600 dark:text-red-400",children:[s.issues," ",e("issues","issues")]})]})]},a)})})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("recentActions","Recent Actions")}),(0,n.jsx)("div",{className:"space-y-4",children:(k.recentActions||[]).map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e.action}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:new Date(e.date).toLocaleDateString()})]}),(0,n.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200"),children:e.status})]},t))})]})]})]})}}}]);
//# sourceMappingURL=7223.22546d9d.chunk.js.map