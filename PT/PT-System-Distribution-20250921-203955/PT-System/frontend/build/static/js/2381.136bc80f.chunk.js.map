{"version": 3, "file": "static/js/2381.136bc80f.chunk.js", "mappings": "iOAMA,MA+xBA,EA/xBqBA,KACnB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAIhCC,EAAWC,IADGN,EAASO,SAASC,SAAS,SACdJ,EAAAA,EAAAA,UAAwB,aACnDK,EAAUC,IAAeN,EAAAA,EAAAA,UAAS,KAClCO,EAAYC,IAAiBR,EAAAA,EAAAA,UAAS,KACtCS,EAAcC,IAAmBV,EAAAA,EAAAA,UAAS,QAC1CW,EAAsBC,IAA2BZ,EAAAA,EAAAA,UAAS,KAC1Da,EAAmBC,IAAwBd,EAAAA,EAAAA,WAAS,IAGpDe,EAAaC,IAAkBhB,EAAAA,EAAAA,UAAS,CAC7CiB,UAAW,GACXC,YAAa,GACbC,OAAQ,GACRC,cAAe,GACfC,YAAa,GACbC,YAAa,GACbC,cAAe,GACfC,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,YAAa,GACbC,gBAAiB,GACjBC,eAAgB,GAChBC,MAAO,MAGFC,EAAQC,IAAajC,EAAAA,EAAAA,UAAS,CAAC,IAEtCkC,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAiCMA,EAAeC,UACnBrC,GAAW,GACX,IACE,MAAMsC,EAAc,IAAIC,iBAAeC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CACtCC,KAAM,EACNC,MAAO,IACHlC,GAAc,CAAEmC,OAAQnC,IACP,QAAjBE,GAA0B,CAAEkC,OAAQlC,KAGpCmC,QAAiBC,MAAM,oBAADC,OAAqBT,GAAe,CAC9DU,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIL,EAASM,GAIX,MAAM,IAAIC,MAAM,2BAJD,CACf,MAAMC,QAAaR,EAASS,OAC5B/C,EAAY8C,EAAKA,MAAQ,GAC3B,CAGF,CAAE,MAAOE,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAMF,MAAM,2BAwCZhD,EAtCqB,CACnB,CACEmD,GAAI,SACJvC,YAAa,uEACbwC,cAAe,qBACfvC,OAAQ,KACRC,cAAe,OACfC,YAAa,eACbsB,OAAQ,YACRgB,KAAM,aACNpC,cAAe,eACfD,YAAa,4BAEf,CACEmC,GAAI,SACJvC,YAAa,0DACbwC,cAAe,eACfvC,OAAQ,IACRC,cAAe,OACfC,YAAa,QACbsB,OAAQ,UACRgB,KAAM,aACNpC,cAAe,eACfD,YAAa,sBAEf,CACEmC,GAAI,SACJvC,YAAa,oDACbwC,cAAe,iBACfvC,OAAQ,KACRC,cAAe,YACfC,YAAa,oBACbsB,OAAQ,YACRgB,KAAM,aACNpC,cAAe,eACfD,YAAa,sBAInB,CAAC,QACCvB,GAAW,EACb,GAGI6D,EAAoBA,CAACC,EAAOC,KAChC9C,EAAe+C,IAAIxB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUwB,GAAI,IAAE,CAACF,GAAQC,KACxC9B,EAAO6B,IACT5B,EAAU8B,IAAIxB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUwB,GAAI,IAAE,CAACF,GAAQ,SAiGrCG,EAAkBrB,IACtB,OAAQA,GACN,IAAK,YACH,MAAO,uEACT,IAAK,UACH,MAAO,2EACT,IAAK,SACH,MAAO,+DACT,IAAK,WACH,MAAO,2EACT,QACE,MAAO,qEAIPsB,EAAwBC,IAC5B,OAAQA,GACN,IAAK,OACH,MAAO,yBACT,IAAK,OACH,MAAO,qBACT,IAAK,YACH,MAAO,oBACT,IAAK,gBACH,MAAO,oBACT,QACE,MAAO,iBAIPC,EAAmB9D,EAAS+D,OAAOC,IAAY,IAADC,EAClD,MAAMC,EAAgBF,EAAQnD,YAAYsD,cAAcpE,SAASG,EAAWiE,iBAClC,QADgDF,EACrED,EAAQX,qBAAa,IAAAY,OAAA,EAArBA,EAAuBE,cAAcpE,SAASG,EAAWiE,iBACzDH,EAAQ9C,cAAciD,cAAcpE,SAASG,EAAWiE,eACvEC,EAAiC,QAAjBhE,GAA0B4D,EAAQ1B,SAAWlC,EACnE,OAAO8D,GAAiBE,IAGpBC,EAAiB,CACrB,CAAEZ,MAAO,OAAQa,MAAOtF,EAAE,OAAQ,QAASuF,KAAM,0BACjD,CAAEd,MAAO,OAAQa,MAAOtF,EAAE,aAAc,eAAgBuF,KAAM,sBAC9D,CAAEd,MAAO,gBAAiBa,MAAOtF,EAAE,eAAgB,iBAAkBuF,KAAM,qBAC3E,CAAEd,MAAO,YAAaa,MAAOtF,EAAE,YAAa,aAAcuF,KAAM,sBAG5DC,EAAe,CACnB,CAAEf,MAAO,eAAgBa,MAAOtF,EAAE,cAAe,iBACjD,CAAEyE,MAAO,QAASa,MAAOtF,EAAE,QAAS,eACpC,CAAEyE,MAAO,aAAca,MAAOtF,EAAE,aAAc,eAC9C,CAAEyE,MAAO,oBAAqBa,MAAOtF,EAAE,mBAAoB,sBAC3D,CAAEyE,MAAO,kBAAmBa,MAAOtF,EAAE,iBAAkB,qBAGzD,OACEyF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D3F,EAAE,qBAAsB,0BAE3B4F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD3F,EAAE,yBAA0B,4EAKjC4F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMhF,EAAa,WAC5B6E,UAAS,4CAAAjC,OACO,YAAd7C,EACI,mDACA,0HACH+E,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ1F,EAAE,iBAAkB,uBAEvByF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMhF,EAAa,WAC5B6E,UAAS,4CAAAjC,OACO,YAAd7C,EACI,mDACA,0HACH+E,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBACZ1F,EAAE,iBAAkB,uBAEvByF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMhF,EAAa,WAC5B6E,UAAS,4CAAAjC,OACO,YAAd7C,EACI,mDACA,0HACH+E,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZ1F,EAAE,iBAAkB,6BAOd,YAAdY,IACC6E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACZ1F,EAAE,oBAAqB,2BAG1ByF,EAAAA,EAAAA,MAAA,QAAMK,SA3Lc/C,UAG1B,GAFAgD,EAAEC,iBArBwBC,MAC1B,MAAMC,EAAY,CAAC,EAgBnB,OAdKxE,EAAYG,YAAYsE,SAC3BD,EAAUrE,YAAc7B,EAAE,sBAAuB,+BAE9C0B,EAAYI,QAAUJ,EAAYI,QAAU,KAC/CoE,EAAUpE,OAAS9B,EAAE,sBAAuB,6BAEzC0B,EAAYK,gBACfmE,EAAUnE,cAAgB/B,EAAE,wBAAyB,+BAElD0B,EAAYM,cACfkE,EAAUlE,YAAchC,EAAE,sBAAuB,6BAGnD4C,EAAUsD,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWI,QAMzBL,GAAL,CAKAvF,GAAW,GACX,IAEE,MAAM6F,EAAc,CAClBC,QAAS9E,EAAYE,UACrBE,OAAQ2E,WAAW/E,EAAYI,QAC/B+C,OAAQnD,EAAYK,cACpB2E,UAAWhF,EAAYQ,cACvBQ,MAAOhB,EAAYgB,OAAShB,EAAYO,aAIR,SAA9BP,EAAYK,eAA4BL,EAAYiF,cACtDJ,EAAYI,YAAcjF,EAAYiF,aAIN,kBAA9BjF,EAAYK,eAAqCL,EAAYkF,cAC/DL,EAAYK,YAAclF,EAAYkF,aAGxC,MAAMrD,QAAiBC,MAAM,mBAAoB,CAC/CqB,OAAQ,OACRnB,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElBiD,KAAMC,KAAKC,UAAUR,KAGvB,IAAIhD,EAASM,GAwBN,CACL,MAAMmD,QAAkBzD,EAASS,OACjC,MAAM,IAAIF,MAAMkD,EAAUC,SAAW,4BACvC,CA3BiB,CACf,MAAMC,QAAe3D,EAASS,OAG9BrC,EAAe,CACbC,UAAW,GACXC,YAAa,GACbC,OAAQ,GACRC,cAAe,GACfC,YAAa,GACbC,YAAa,GACbC,cAAe,GACfC,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,YAAa,GACbC,gBAAiB,GACjBC,eAAgB,GAChBC,MAAO,KAGTyB,EAAAA,GAAMgD,QAAQD,EAAOD,SAAWjH,EAAE,+BAAgC,mCAClEa,EAAa,WAGbiC,GACF,CAIF,CAAE,MAAOmB,GACPE,EAAAA,GAAMF,MAAMjE,EAAE,yBAA0B,4BAC1C,CAAC,QACCU,GAAW,EACb,CAhEA,MAFEyD,EAAAA,GAAMF,MAAMjE,EAAE,kBAAmB,6CAuLQ0F,UAAU,YAAWC,SAAA,EAExDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,cAAe,gBAAgB,KAAC4F,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACL3C,MAAO/C,EAAYG,YACnBwF,SAAWtB,IACTxB,EAAkB,cAAewB,EAAEuB,OAAO7C,OA1UvC1B,WACrB,IAAKwE,GAASA,EAAMjB,OAAS,EAC3B/E,EAAwB,SAI1B,IACE,MAAMgC,QAAiBC,MAAM,6BAADC,OAA8B+D,mBAAmBD,IAAU,CACrF7D,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,GAAIL,EAASM,GAAI,CACf,MAAME,QAAaR,EAASS,OAC5BzC,EAAwBwC,EAAKA,MAAQ,GACvC,CACF,CAAE,MAAOE,GACPC,QAAQD,MAAM,4BAA6BA,EAC7C,GAuTkBwD,CAAe1B,EAAEuB,OAAO7C,OACxBhD,GAAqB,IAEvBiG,QAASA,IAAMjG,GAAqB,GACpCiE,UAAS,mJAAAjC,OACPd,EAAOd,YAAc,iBAAmB,mBAE1C8F,YAAa3H,EAAE,mBAAoB,yBAErC4F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAIdlE,GAAqBF,EAAqBgF,OAAS,IAClDV,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gJAA+IC,SAC3JrE,EAAqBsG,IAAKpB,IACzBf,EAAAA,EAAAA,MAAA,OAEEI,QAASA,IAtURW,KACrB7E,EAAe+C,IAAIxB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACdwB,GAAI,IACP9C,UAAW4E,EAAQqB,IACnBhG,YAAY,GAAD4B,OAAK+C,EAAQsB,UAAS,KAAArE,OAAI+C,EAAQuB,aAE/CxG,EAAwB,IACxBE,GAAqB,IA+TcuG,CAAcxB,GAC7Bd,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4CAA2CC,SAAA,CACvDa,EAAQsB,UAAU,IAAEtB,EAAQuB,aAE/BtC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDa,EAAQyB,WAAW,WAAIzB,EAAQ0B,WAR7B1B,EAAQqB,QAepBlF,EAAOd,cACN+D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEhD,EAAOd,kBAIrD4D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E3F,EAAE,gBAAiB,qBAEtB4F,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACL3C,MAAO/C,EAAYQ,cACnBmF,SAAWtB,GAAMxB,EAAkB,gBAAiBwB,EAAEuB,OAAO7C,OAC7DiB,UAAU,kKACViC,YAAa3H,EAAE,qBAAsB,iCAM3CyF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,SAAU,UAAU,KAAC4F,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEzDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEwB,KAAK,SACLe,KAAK,OACLC,IAAI,IACJ3D,MAAO/C,EAAYI,OACnBuF,SAAWtB,GAAMxB,EAAkB,SAAUwB,EAAEuB,OAAO7C,OACtDiB,UAAS,mJAAAjC,OACPd,EAAOb,OAAS,iBAAmB,mBAErC6F,YAAY,UAEd/B,EAAAA,EAAAA,KAAA,QAAMF,UAAU,0DAAyDC,SACtE3F,EAAE,MAAO,YAGb2C,EAAOb,SACN8D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEhD,EAAOb,aAIrD2D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,gBAAiB,kBAAkB,KAAC4F,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAExEF,EAAAA,EAAAA,MAAA,UACEhB,MAAO/C,EAAYK,cACnBsF,SAAWtB,GAAMxB,EAAkB,gBAAiBwB,EAAEuB,OAAO7C,OAC7DiB,UAAS,mJAAAjC,OACPd,EAAOZ,cAAgB,iBAAmB,mBACzC4D,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQnB,MAAM,GAAEkB,SAAE3F,EAAE,sBAAuB,2BAC1CqF,EAAeuC,IAAI/C,IAClBe,EAAAA,EAAAA,KAAA,UAA2BnB,MAAOI,EAAOJ,MAAMkB,SAC5Cd,EAAOS,OADGT,EAAOJ,WAKvB9B,EAAOZ,gBACN6D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEhD,EAAOZ,oBAIrD0D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E3F,EAAE,cAAe,gBAAgB,KAAC4F,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEF,EAAAA,EAAAA,MAAA,UACEhB,MAAO/C,EAAYM,YACnBqF,SAAWtB,GAAMxB,EAAkB,cAAewB,EAAEuB,OAAO7C,OAC3DiB,UAAS,mJAAAjC,OACPd,EAAOX,YAAc,iBAAmB,mBACvC2D,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQnB,MAAM,GAAEkB,SAAE3F,EAAE,oBAAqB,yBACxCwF,EAAaoC,IAAIR,IAChBxB,EAAAA,EAAAA,KAAA,UAAyBnB,MAAO2C,EAAK3C,MAAMkB,SACxCyB,EAAK9B,OADK8B,EAAK3C,WAKrB9B,EAAOX,cACN4D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEhD,EAAOX,qBAMvDyD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E3F,EAAE,cAAe,mBAEpB4F,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACL3C,MAAO/C,EAAYS,YACnBkF,SAAWtB,GAAMxB,EAAkB,cAAewB,EAAEuB,OAAO7C,OAC3DiB,UAAU,wKAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E3F,EAAE,cAAe,kBAEpB4F,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACL3C,MAAO/C,EAAYO,YACnBoF,SAAWtB,GAAMxB,EAAkB,cAAewB,EAAEuB,OAAO7C,OAC3DiB,UAAU,kKACViC,YAAa3H,EAAE,mBAAoB,sCAMzCyF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E3F,EAAE,QAAS,YAEd4F,EAAAA,EAAAA,KAAA,YACEnB,MAAO/C,EAAYgB,MACnB2E,SAAWtB,GAAMxB,EAAkB,QAASwB,EAAEuB,OAAO7C,OACrD4D,KAAM,EACN3C,UAAU,kKACViC,YAAa3H,EAAE,aAAc,gCAKjCyF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEwB,KAAK,SACLvB,QAASA,IAAMxF,EAAS,cACxBqF,UAAU,+FAA8FC,SAEvG3F,EAAE,SAAU,aAEf4F,EAAAA,EAAAA,KAAA,UACEwB,KAAK,SACLkB,SAAU7H,EACViF,UAAU,kIAAiIC,SAE1IlF,GACCgF,EAAAA,EAAAA,MAAA8C,EAAAA,SAAA,CAAA5C,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZ1F,EAAE,aAAc,qBAGnByF,EAAAA,EAAAA,MAAA8C,EAAAA,SAAA,CAAA5C,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZ1F,EAAE,iBAAkB,iCAUpB,YAAdY,IACC6E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sFAAqFC,SAAA,EAClGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACZ1F,EAAE,iBAAkB,uBAGvByF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEwB,KAAK,OACL3C,MAAOvD,EACPmG,SAAWtB,GAAM5E,EAAc4E,EAAEuB,OAAO7C,OACxCkD,YAAa3H,EAAE,iBAAkB,sBACjC0F,UAAU,oKAEZE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAGfD,EAAAA,EAAAA,MAAA,UACEhB,MAAOrD,EACPiG,SAAWtB,GAAM1E,EAAgB0E,EAAEuB,OAAO7C,OAC1CiB,UAAU,2JAA0JC,SAAA,EAEpKC,EAAAA,EAAAA,KAAA,UAAQnB,MAAM,MAAKkB,SAAE3F,EAAE,cAAe,mBACtC4F,EAAAA,EAAAA,KAAA,UAAQnB,MAAM,YAAWkB,SAAE3F,EAAE,YAAa,gBAC1C4F,EAAAA,EAAAA,KAAA,UAAQnB,MAAM,UAASkB,SAAE3F,EAAE,UAAW,cACtC4F,EAAAA,EAAAA,KAAA,UAAQnB,MAAM,SAAQkB,SAAE3F,EAAE,SAAU,aACpC4F,EAAAA,EAAAA,KAAA,UAAQnB,MAAM,WAAUkB,SAAE3F,EAAE,WAAY,0BAMhD4F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G3F,EAAE,UAAW,cAEhB4F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G3F,EAAE,SAAU,aAEf4F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G3F,EAAE,SAAU,aAEf4F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G3F,EAAE,OAAQ,WAEb4F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G3F,EAAE,SAAU,aAEf4F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G3F,EAAE,OAAQ,WAEb4F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G3F,EAAE,UAAW,mBAIpB4F,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFb,EAAiB8C,IAAK5C,IACrBS,EAAAA,EAAAA,MAAA,MAAqBC,UAAU,0CAAyCC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/D1F,EAAQ+E,EAAQnD,YAAcmD,EAAQX,iBAEzCuB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDX,EAAQ9C,sBAIf0D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,CAC/DX,EAAQlD,OAAO0G,iBAAiB,IAAExI,EAAE,MAAO,aAGhD4F,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAjC,OAAKmB,EAAqBI,EAAQjD,eAAc,0BAC5D6D,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpD3F,EAAEgF,EAAQjD,cAAeiD,EAAQjD,uBAIxC6D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpD3F,EAAEgF,EAAQhD,YAAagD,EAAQhD,kBAGpC4D,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAjC,OAAgDkB,EAAeK,EAAQ1B,SAAUqC,SAC7F3F,EAAEgF,EAAQ1B,OAAQ0B,EAAQ1B,aAG/BsC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9EX,EAAQV,QAEXsB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,UAC7DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UAAQF,UAAU,gFAA+EC,UAC/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kBAEfE,EAAAA,EAAAA,KAAA,UAAQF,UAAU,oFAAmFC,UACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mBAEK,cAAnBV,EAAQ1B,SACPsC,EAAAA,EAAAA,KAAA,UAAQF,UAAU,4EAA2EC,UAC3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBA/CdV,EAAQZ,YA0DI,IAA5BU,EAAiBwB,SAChBb,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5C3F,EAAE,kBAAmB,6BAQjB,YAAdY,IACC6E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACZ1F,EAAE,iBAAkB,uBAGvByF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EAEnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDAAsDC,SAChE3F,EAAE,eAAgB,oBAErByF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,sDAAqDC,SAAA,CAAC,UACzD3F,EAAE,MAAO,cAGrB4F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yEAMnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gGAA+FC,UAC5GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDAAwDC,SAClE3F,EAAE,iBAAkB,sBAEvByF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wDAAuDC,SAAA,CAAC,WAC1D3F,EAAE,MAAO,cAGtB4F,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2EAMnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oGAAmGC,UAChHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DAA0DC,SACpE3F,EAAE,iBAAkB,sBAEvB4F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAAyDC,SAAC,cAIzEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8EAOrBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,UAAQF,UAAU,uHAAsHC,UACtIF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wEACbD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtD3F,EAAE,gBAAiB,sBAEtB4F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD3F,EAAE,sBAAuB,kDAMlC4F,EAAAA,EAAAA,KAAA,UAAQF,UAAU,uHAAsHC,UACtIF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEACbD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4CAA2CC,SACtD3F,EAAE,iBAAkB,sBAEvB4F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpD3F,EAAE,uBAAwB,qD", "sources": ["pages/Financial/PaymentsPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst PaymentsPage = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n\n  // Check if we're on the \"new\" route to auto-open the payment form\n  const isNewPayment = location.pathname.includes('/new');\n  const [activeTab, setActiveTab] = useState(isNewPayment ? 'process' : 'process');\n  const [payments, setPayments] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [patientSearchResults, setPatientSearchResults] = useState([]);\n  const [showPatientSearch, setShowPatientSearch] = useState(false);\n\n  // Payment form state\n  const [paymentForm, setPaymentForm] = useState({\n    patientId: '',\n    patientName: '',\n    amount: '',\n    paymentMethod: '',\n    paymentType: '',\n    description: '',\n    invoiceNumber: '',\n    sessionDate: new Date().toISOString().split('T')[0],\n    copayAmount: '',\n    insuranceAmount: '',\n    discountAmount: '',\n    notes: ''\n  });\n\n  const [errors, setErrors] = useState({});\n\n  useEffect(() => {\n    loadPayments();\n  }, []);\n\n  const searchPatients = async (query) => {\n    if (!query || query.length < 2) {\n      setPatientSearchResults([]);\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/v1/patients/search?q=${encodeURIComponent(query)}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setPatientSearchResults(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error searching patients:', error);\n    }\n  };\n\n  const selectPatient = (patient) => {\n    setPaymentForm(prev => ({\n      ...prev,\n      patientId: patient._id,\n      patientName: `${patient.firstName} ${patient.lastName}`\n    }));\n    setPatientSearchResults([]);\n    setShowPatientSearch(false);\n  };\n\n  const loadPayments = async () => {\n    setLoading(true);\n    try {\n      const queryParams = new URLSearchParams({\n        page: 1,\n        limit: 50,\n        ...(searchTerm && { search: searchTerm }),\n        ...(filterStatus !== 'all' && { status: filterStatus })\n      });\n\n      const response = await fetch(`/api/v1/payments?${queryParams}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setPayments(data.data || []);\n      } else {\n        throw new Error('Failed to load payments');\n      }\n    } catch (error) {\n      console.error('Error loading payments:', error);\n      toast.error('Failed to load payments');\n      // Fallback to mock data for development\n      const mockPayments = [\n        {\n          id: 'PAY001',\n          patientName: 'أحمد محمد علي',\n          patientNameEn: 'Ahmed Mohammed Ali',\n          amount: 1500,\n          paymentMethod: 'cash',\n          paymentType: 'full_payment',\n          status: 'completed',\n          date: '2024-01-15',\n          invoiceNumber: 'INV-2024-001',\n          description: 'Physical Therapy Session'\n        },\n        {\n          id: 'PAY002',\n          patientName: 'فاطمة أحمد',\n          patientNameEn: 'Fatima Ahmed',\n          amount: 800,\n          paymentMethod: 'card',\n          paymentType: 'copay',\n          status: 'pending',\n          date: '2024-01-14',\n          invoiceNumber: 'INV-2024-002',\n          description: 'Initial Assessment'\n        },\n        {\n          id: 'PAY003',\n          patientName: 'محمد سالم',\n          patientNameEn: 'Mohammed Salem',\n          amount: 2200,\n          paymentMethod: 'insurance',\n          paymentType: 'insurance_payment',\n          status: 'completed',\n          date: '2024-01-13',\n          invoiceNumber: 'INV-2024-003',\n          description: 'Treatment Package'\n        }\n      ];\n      setPayments(mockPayments);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setPaymentForm(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const validatePaymentForm = () => {\n    const newErrors = {};\n\n    if (!paymentForm.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n    if (!paymentForm.amount || paymentForm.amount <= 0) {\n      newErrors.amount = t('validAmountRequired', 'Valid amount is required');\n    }\n    if (!paymentForm.paymentMethod) {\n      newErrors.paymentMethod = t('paymentMethodRequired', 'Payment method is required');\n    }\n    if (!paymentForm.paymentType) {\n      newErrors.paymentType = t('paymentTypeRequired', 'Payment type is required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmitPayment = async (e) => {\n    e.preventDefault();\n    \n    if (!validatePaymentForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Prepare payment data\n      const paymentData = {\n        patient: paymentForm.patientId,\n        amount: parseFloat(paymentForm.amount),\n        method: paymentForm.paymentMethod,\n        reference: paymentForm.invoiceNumber,\n        notes: paymentForm.notes || paymentForm.description\n      };\n\n      // Add card details if payment method is card\n      if (paymentForm.paymentMethod === 'card' && paymentForm.cardDetails) {\n        paymentData.cardDetails = paymentForm.cardDetails;\n      }\n\n      // Add bank details if payment method is bank transfer\n      if (paymentForm.paymentMethod === 'bank_transfer' && paymentForm.bankDetails) {\n        paymentData.bankDetails = paymentForm.bankDetails;\n      }\n\n      const response = await fetch('/api/v1/payments', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(paymentData)\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n\n        // Reset form\n        setPaymentForm({\n          patientId: '',\n          patientName: '',\n          amount: '',\n          paymentMethod: '',\n          paymentType: '',\n          description: '',\n          invoiceNumber: '',\n          sessionDate: new Date().toISOString().split('T')[0],\n          copayAmount: '',\n          insuranceAmount: '',\n          discountAmount: '',\n          notes: ''\n        });\n\n        toast.success(result.message || t('paymentProcessedSuccessfully', 'Payment processed successfully'));\n        setActiveTab('history');\n\n        // Reload payments\n        loadPayments();\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to process payment');\n      }\n    } catch (error) {\n      toast.error(t('errorProcessingPayment', 'Error processing payment'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'failed':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      case 'refunded':\n        return 'text-purple-600 bg-purple-100 dark:bg-purple-900/30 dark:text-purple-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const getPaymentMethodIcon = (method) => {\n    switch (method) {\n      case 'cash':\n        return 'fas fa-money-bill-wave';\n      case 'card':\n        return 'fas fa-credit-card';\n      case 'insurance':\n        return 'fas fa-shield-alt';\n      case 'bank_transfer':\n        return 'fas fa-university';\n      default:\n        return 'fas fa-coins';\n    }\n  };\n\n  const filteredPayments = payments.filter(payment => {\n    const matchesSearch = payment.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         payment.patientNameEn?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         payment.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || payment.status === filterStatus;\n    return matchesSearch && matchesStatus;\n  });\n\n  const paymentMethods = [\n    { value: 'cash', label: t('cash', 'Cash'), icon: 'fas fa-money-bill-wave' },\n    { value: 'card', label: t('creditCard', 'Credit Card'), icon: 'fas fa-credit-card' },\n    { value: 'bank_transfer', label: t('bankTransfer', 'Bank Transfer'), icon: 'fas fa-university' },\n    { value: 'insurance', label: t('insurance', 'Insurance'), icon: 'fas fa-shield-alt' }\n  ];\n\n  const paymentTypes = [\n    { value: 'full_payment', label: t('fullPayment', 'Full Payment') },\n    { value: 'copay', label: t('copay', 'Co-payment') },\n    { value: 'deductible', label: t('deductible', 'Deductible') },\n    { value: 'insurance_payment', label: t('insurancePayment', 'Insurance Payment') },\n    { value: 'partial_payment', label: t('partialPayment', 'Partial Payment') }\n  ];\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          {t('paymentsManagement', 'Payments Management')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          {t('paymentsManagementDesc', 'Process payments, track transactions, and manage financial records')}\n        </p>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-600\">\n          <nav className=\"-mb-px flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('process')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'process'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('processPayment', 'Process Payment')}\n            </button>\n            <button\n              onClick={() => setActiveTab('history')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'history'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-history mr-2\"></i>\n              {t('paymentHistory', 'Payment History')}\n            </button>\n            <button\n              onClick={() => setActiveTab('reports')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'reports'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-chart-bar mr-2\"></i>\n              {t('paymentReports', 'Payment Reports')}\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'process' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-credit-card text-green-600 dark:text-green-400 mr-2\"></i>\n            {t('processNewPayment', 'Process New Payment')}\n          </h2>\n          \n          <form onSubmit={handleSubmitPayment} className=\"space-y-6\">\n            {/* Patient Information */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"relative\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    value={paymentForm.patientName}\n                    onChange={(e) => {\n                      handleInputChange('patientName', e.target.value);\n                      searchPatients(e.target.value);\n                      setShowPatientSearch(true);\n                    }}\n                    onFocus={() => setShowPatientSearch(true)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.patientName ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder={t('enterPatientName', 'Enter patient name')}\n                  />\n                  <i className=\"fas fa-search absolute right-3 top-3 text-gray-400\"></i>\n                </div>\n\n                {/* Patient Search Results */}\n                {showPatientSearch && patientSearchResults.length > 0 && (\n                  <div className=\"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                    {patientSearchResults.map((patient) => (\n                      <div\n                        key={patient._id}\n                        onClick={() => selectPatient(patient)}\n                        className=\"px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0\"\n                      >\n                        <div className=\"font-medium text-gray-900 dark:text-white\">\n                          {patient.firstName} {patient.lastName}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {patient.nationalId} • {patient.phone}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                )}\n\n                {errors.patientName && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n                )}\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('invoiceNumber', 'Invoice Number')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={paymentForm.invoiceNumber}\n                  onChange={(e) => handleInputChange('invoiceNumber', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterInvoiceNumber', 'Enter invoice number')}\n                />\n              </div>\n            </div>\n\n            {/* Payment Details */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('amount', 'Amount')} <span className=\"text-red-500\">*</span>\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    value={paymentForm.amount}\n                    onChange={(e) => handleInputChange('amount', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.amount ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder=\"0.00\"\n                  />\n                  <span className=\"absolute right-3 top-2 text-gray-500 dark:text-gray-400\">\n                    {t('sar', 'SAR')}\n                  </span>\n                </div>\n                {errors.amount && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.amount}</p>\n                )}\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('paymentMethod', 'Payment Method')} <span className=\"text-red-500\">*</span>\n                </label>\n                <select\n                  value={paymentForm.paymentMethod}\n                  onChange={(e) => handleInputChange('paymentMethod', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                    errors.paymentMethod ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">{t('selectPaymentMethod', 'Select payment method')}</option>\n                  {paymentMethods.map(method => (\n                    <option key={method.value} value={method.value}>\n                      {method.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.paymentMethod && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.paymentMethod}</p>\n                )}\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('paymentType', 'Payment Type')} <span className=\"text-red-500\">*</span>\n                </label>\n                <select\n                  value={paymentForm.paymentType}\n                  onChange={(e) => handleInputChange('paymentType', e.target.value)}\n                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                    errors.paymentType ? 'border-red-500' : 'border-gray-300'\n                  }`}\n                >\n                  <option value=\"\">{t('selectPaymentType', 'Select payment type')}</option>\n                  {paymentTypes.map(type => (\n                    <option key={type.value} value={type.value}>\n                      {type.label}\n                    </option>\n                  ))}\n                </select>\n                {errors.paymentType && (\n                  <p className=\"text-red-500 text-sm mt-1\">{errors.paymentType}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Additional Details */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('sessionDate', 'Session Date')}\n                </label>\n                <input\n                  type=\"date\"\n                  value={paymentForm.sessionDate}\n                  onChange={(e) => handleInputChange('sessionDate', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('description', 'Description')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={paymentForm.description}\n                  onChange={(e) => handleInputChange('description', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterDescription', 'Enter payment description')}\n                />\n              </div>\n            </div>\n\n            {/* Notes */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('notes', 'Notes')}\n              </label>\n              <textarea\n                value={paymentForm.notes}\n                onChange={(e) => handleInputChange('notes', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterNotes', 'Enter additional notes')}\n              />\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                type=\"button\"\n                onClick={() => navigate('/financial')}\n                className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {loading ? (\n                  <>\n                    <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                    {t('processing', 'Processing...')}\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-credit-card mr-2\"></i>\n                    {t('processPayment', 'Process Payment')}\n                  </>\n                )}\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Payment History Tab */}\n      {activeTab === 'history' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n            <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                <i className=\"fas fa-history text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('paymentHistory', 'Payment History')}\n              </h2>\n\n              <div className=\"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    placeholder={t('searchPayments', 'Search payments...')}\n                    className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  />\n                  <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n                </div>\n\n                <select\n                  value={filterStatus}\n                  onChange={(e) => setFilterStatus(e.target.value)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                >\n                  <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n                  <option value=\"completed\">{t('completed', 'Completed')}</option>\n                  <option value=\"pending\">{t('pending', 'Pending')}</option>\n                  <option value=\"failed\">{t('failed', 'Failed')}</option>\n                  <option value=\"refunded\">{t('refunded', 'Refunded')}</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('patient', 'Patient')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('amount', 'Amount')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('method', 'Method')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('type', 'Type')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('status', 'Status')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('date', 'Date')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('actions', 'Actions')}\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                {filteredPayments.map((payment) => (\n                  <tr key={payment.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {isRTL ? payment.patientName : payment.patientNameEn}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {payment.invoiceNumber}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {payment.amount.toLocaleString()} {t('sar', 'SAR')}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center\">\n                        <i className={`${getPaymentMethodIcon(payment.paymentMethod)} text-gray-400 mr-2`}></i>\n                        <span className=\"text-sm text-gray-900 dark:text-white\">\n                          {t(payment.paymentMethod, payment.paymentMethod)}\n                        </span>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm text-gray-900 dark:text-white\">\n                        {t(payment.paymentType, payment.paymentType)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(payment.status)}`}>\n                        {t(payment.status, payment.status)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {payment.date}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200\">\n                          <i className=\"fas fa-eye\"></i>\n                        </button>\n                        <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200\">\n                          <i className=\"fas fa-print\"></i>\n                        </button>\n                        {payment.status === 'completed' && (\n                          <button className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200\">\n                            <i className=\"fas fa-undo\"></i>\n                          </button>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {filteredPayments.length === 0 && (\n            <div className=\"text-center py-12\">\n              <i className=\"fas fa-receipt text-4xl text-gray-400 mb-4\"></i>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                {t('noPaymentsFound', 'No payments found')}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Payment Reports Tab */}\n      {activeTab === 'reports' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-chart-bar text-purple-600 dark:text-purple-400 mr-2\"></i>\n            {t('paymentReports', 'Payment Reports')}\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {/* Daily Revenue */}\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">\n                    {t('dailyRevenue', 'Daily Revenue')}\n                  </p>\n                  <p className=\"text-2xl font-bold text-blue-900 dark:text-blue-100\">\n                    12,500 {t('sar', 'SAR')}\n                  </p>\n                </div>\n                <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg\">\n                  <i className=\"fas fa-calendar-day text-blue-600 dark:text-blue-400 text-xl\"></i>\n                </div>\n              </div>\n            </div>\n\n            {/* Monthly Revenue */}\n            <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                    {t('monthlyRevenue', 'Monthly Revenue')}\n                  </p>\n                  <p className=\"text-2xl font-bold text-green-900 dark:text-green-100\">\n                    125,000 {t('sar', 'SAR')}\n                  </p>\n                </div>\n                <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg\">\n                  <i className=\"fas fa-calendar-alt text-green-600 dark:text-green-400 text-xl\"></i>\n                </div>\n              </div>\n            </div>\n\n            {/* Collection Rate */}\n            <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-purple-800 dark:text-purple-200\">\n                    {t('collectionRate', 'Collection Rate')}\n                  </p>\n                  <p className=\"text-2xl font-bold text-purple-900 dark:text-purple-100\">\n                    92.5%\n                  </p>\n                </div>\n                <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg\">\n                  <i className=\"fas fa-percentage text-purple-600 dark:text-purple-400 text-xl\"></i>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Report Actions */}\n          <div className=\"mt-8 grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <button className=\"p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n              <div className=\"flex items-center\">\n                <i className=\"fas fa-file-excel text-green-600 dark:text-green-400 text-2xl mr-4\"></i>\n                <div className=\"text-left\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                    {t('exportToExcel', 'Export to Excel')}\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('downloadPaymentData', 'Download payment data as Excel file')}\n                  </p>\n                </div>\n              </div>\n            </button>\n\n            <button className=\"p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n              <div className=\"flex items-center\">\n                <i className=\"fas fa-file-pdf text-red-600 dark:text-red-400 text-2xl mr-4\"></i>\n                <div className=\"text-left\">\n                  <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                    {t('generateReport', 'Generate Report')}\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('createDetailedReport', 'Create detailed payment report')}\n                  </p>\n                </div>\n              </div>\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PaymentsPage;\n"], "names": ["PaymentsPage", "t", "isRTL", "useLanguage", "user", "useAuth", "navigate", "useNavigate", "location", "useLocation", "loading", "setLoading", "useState", "activeTab", "setActiveTab", "pathname", "includes", "payments", "setPayments", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "patientSearchResults", "setPatientSearchResults", "showPatientSearch", "setShowPatientSearch", "paymentForm", "setPaymentForm", "patientId", "patientName", "amount", "paymentMethod", "paymentType", "description", "invoiceNumber", "sessionDate", "Date", "toISOString", "split", "copayAmount", "insuranceAmount", "discountAmount", "notes", "errors", "setErrors", "useEffect", "loadPayments", "async", "queryParams", "URLSearchParams", "_objectSpread", "page", "limit", "search", "status", "response", "fetch", "concat", "headers", "localStorage", "getItem", "ok", "Error", "data", "json", "error", "console", "toast", "id", "patientNameEn", "date", "handleInputChange", "field", "value", "prev", "getStatusColor", "getPaymentMethodIcon", "method", "filteredPayments", "filter", "payment", "_payment$patientNameE", "matchesSearch", "toLowerCase", "matchesStatus", "paymentMethods", "label", "icon", "paymentTypes", "_jsxs", "className", "children", "_jsx", "onClick", "onSubmit", "e", "preventDefault", "validatePaymentForm", "newErrors", "trim", "Object", "keys", "length", "paymentData", "patient", "parseFloat", "reference", "cardDetails", "bankDetails", "body", "JSON", "stringify", "errorData", "message", "result", "success", "type", "onChange", "target", "query", "encodeURIComponent", "searchPatients", "onFocus", "placeholder", "map", "_id", "firstName", "lastName", "selectPatient", "nationalId", "phone", "step", "min", "rows", "disabled", "_Fragment", "toLocaleString"], "sourceRoot": ""}