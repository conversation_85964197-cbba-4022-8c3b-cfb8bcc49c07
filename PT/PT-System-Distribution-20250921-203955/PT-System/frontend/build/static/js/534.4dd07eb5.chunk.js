"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[534],{534:(e,a,t)=>{t.r(a),t.d(a,{default:()=>d});var r=t(5043),n=t(7921),s=(t(3091),t(579));const i=()=>{const{t:e}=(0,n.o)(),[a,t]=(0,r.useState)("patient-care"),[i,d]=(0,r.useState)({}),[l,c]=(0,r.useState)(!0);(0,r.useEffect)(()=>{setTimeout(()=>{d({"patient-care":{measures:[{id:"pc-01",name:"Patient Assessment Completion Rate",value:98.5,target:95,trend:"up",description:"Percentage of patients with completed initial assessments within 24 hours"},{id:"pc-02",name:"Pain Assessment Documentation",value:96.2,target:90,trend:"up",description:"Percentage of patients with documented pain assessments"},{id:"pc-03",name:"Discharge Planning Completion",value:94.8,target:95,trend:"down",description:"Percentage of patients with completed discharge plans"}]},"patient-safety":{measures:[{id:"ps-01",name:"Fall Prevention Compliance",value:99.1,target:98,trend:"up",description:"Percentage of high-risk patients with fall prevention measures"},{id:"ps-02",name:"Medication Error Rate",value:.2,target:.5,trend:"down",description:"Medication errors per 1000 patient days"},{id:"ps-03",name:"Healthcare-Associated Infections",value:1.1,target:2,trend:"stable",description:"HAI rate per 1000 patient days"}]},"quality-management":{measures:[{id:"qm-01",name:"Patient Satisfaction Score",value:4.6,target:4,trend:"up",description:"Average patient satisfaction rating (1-5 scale)"},{id:"qm-02",name:"Clinical Outcome Achievement",value:87.3,target:85,trend:"up",description:"Percentage of patients achieving treatment goals"},{id:"qm-03",name:"Quality Improvement Projects",value:12,target:10,trend:"up",description:"Number of active quality improvement initiatives"}]}}),c(!1)},1e3)},[]);const o=[{id:"patient-care",label:e("patientCare","Patient Care"),icon:"fas fa-user-md"},{id:"patient-safety",label:e("patientSafety","Patient Safety"),icon:"fas fa-shield-alt"},{id:"quality-management",label:e("qualityManagement","Quality Management"),icon:"fas fa-chart-line"}],m=e=>{switch(e){case"up":return"fas fa-arrow-up text-green-500";case"down":return"fas fa-arrow-down text-red-500";case"stable":return"fas fa-minus text-yellow-500";default:return"fas fa-minus text-gray-500"}},g=function(e,a){return(arguments.length>2&&void 0!==arguments[2]&&arguments[2]?e<=a:e>=a)?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"};return l?(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("cbahiQualityMeasures","CBAHI Quality Measures")}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("cbahiQualityDescription","Key performance indicators and quality metrics for CBAHI compliance")})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full",children:[(0,s.jsx)("i",{className:"fas fa-chart-bar text-green-600 dark:text-green-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"Quality Metrics"})]})]})}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,s.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,s.jsx)("nav",{className:"flex space-x-8 px-6",children:o.map(e=>(0,s.jsxs)("button",{onClick:()=>t(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(a===e.id?"border-green-500 text-green-600 dark:text-green-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,s.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})}),(0,s.jsx)("div",{className:"p-6",children:i[a]&&(0,s.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:i[a].measures.map(e=>(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white text-sm",children:e.name}),(0,s.jsx)("i",{className:m(e.trend)})]}),(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"flex items-baseline space-x-2",children:[(0,s.jsxs)("span",{className:"text-3xl font-bold ".concat(g(e.value,e.target,e.id.includes("error")||e.id.includes("infection"))),children:[e.value,e.id.includes("satisfaction")||e.id.includes("error")||e.id.includes("infection")?"":"%"]}),(0,s.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["/ ",e.target,e.id.includes("satisfaction")||e.id.includes("error")||e.id.includes("infection")?"":"%"," target"]})]})}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:e.description}),(0,s.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full ".concat(e.id.includes("error")||e.id.includes("infection")?e.value<=e.target?"bg-green-500":"bg-red-500":e.value>=e.target?"bg-green-500":"bg-red-500"),style:{width:"".concat(Math.min(e.id.includes("error")||e.id.includes("infection")?Math.max(0,100-e.value/e.target*100):e.value/e.target*100,100),"%")}})})]},e.id))})})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("performanceSummary","Performance Summary")})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("i",{className:"fas fa-trophy text-green-600 dark:text-green-400 text-2xl"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("excellentPerformance","Excellent Performance")}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-2",children:e("excellentDescription","8 out of 9 quality measures exceed targets")})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("i",{className:"fas fa-chart-line text-blue-600 dark:text-blue-400 text-2xl"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("continuousImprovement","Continuous Improvement")}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-2",children:e("improvementDescription","Positive trends in 7 key performance areas")})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("i",{className:"fas fa-shield-check text-purple-600 dark:text-purple-400 text-2xl"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("cbahiCompliant","CBAHI Compliant")}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-2",children:e("complianceDescription","All measures meet or exceed CBAHI standards")})]})]})})]}),(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("improvementActionPlan","Improvement Action Plan")})}),(0,s.jsx)("div",{className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mt-1"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-yellow-800 dark:text-yellow-200",children:e("improveDischargePlanning","Improve Discharge Planning Completion")}),(0,s.jsx)("p",{className:"text-sm text-yellow-600 dark:text-yellow-400 mt-1",children:e("dischargeAction","Implement automated discharge planning reminders and staff training")}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsxs)("span",{className:"text-xs bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded",children:[e("priority","Priority"),": ",e("medium","Medium")]})})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:[(0,s.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 mt-1"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-green-800 dark:text-green-200",children:e("maintainExcellence","Maintain Excellence in Patient Safety")}),(0,s.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400 mt-1",children:e("safetyAction","Continue current safety protocols and regular staff training")}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsxs)("span",{className:"text-xs bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200 px-2 py-1 rounded",children:[e("status","Status"),": ",e("onTrack","On Track")]})})]})]})]})})]})]})},d=()=>(0,s.jsx)(i,{})},3091:(e,a,t)=>{t.d(a,{RS:()=>r});const r={PATIENT_CARE:{id:"PC",title:"Patient Care Standards",description:"Standards for comprehensive patient care and safety",requirements:["Patient assessment and care planning","Medication management and safety","Infection prevention and control","Patient rights and responsibilities","Pain assessment and management","Patient education and discharge planning"]},PATIENT_SAFETY:{id:"PS",title:"Patient Safety Standards",description:"Standards for ensuring patient safety throughout care",requirements:["Patient identification protocols","Fall prevention programs","Medication error prevention","Healthcare-associated infection prevention","Safe surgery protocols","Emergency response procedures"]},QUALITY_MANAGEMENT:{id:"QM",title:"Quality Management Standards",description:"Standards for quality improvement and management",requirements:["Quality improvement programs","Performance measurement and monitoring","Risk management systems","Patient satisfaction monitoring","Clinical outcome measurement","Continuous quality improvement"]},HUMAN_RESOURCES:{id:"HR",title:"Human Resources Standards",description:"Standards for healthcare workforce management",requirements:["Staff competency assessment","Continuing education programs","Professional development","Performance evaluation","Credentialing and privileging","Workplace safety"]},INFORMATION_MANAGEMENT:{id:"IM",title:"Information Management Standards",description:"Standards for healthcare information systems",requirements:["Medical record management","Data security and privacy","Information system reliability","Data backup and recovery","Electronic health records","Health information exchange"]},GOVERNANCE:{id:"GL",title:"Governance and Leadership Standards",description:"Standards for organizational governance",requirements:["Organizational structure","Leadership accountability","Strategic planning","Resource allocation","Policy development","Compliance monitoring"]},FACILITY_MANAGEMENT:{id:"FM",title:"Facility Management Standards",description:"Standards for healthcare facility operations",requirements:["Facility safety and security","Environmental controls","Equipment management","Emergency preparedness","Waste management","Utility systems"]}}}}]);
//# sourceMappingURL=534.4dd07eb5.chunk.js.map