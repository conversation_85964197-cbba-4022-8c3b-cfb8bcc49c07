{"version": 3, "file": "static/js/3850.d788bca7.chunk.js", "mappings": "iOAGA,MAwHA,EAxHkCA,IAA8C,IAA7C,SAAEC,EAAQ,kBAAEC,EAAiB,OAAEC,GAAQH,EACxE,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERC,EAAwB,CAC5B,CAAEC,MAAO,qBAAsBC,MAAOJ,EAAE,oBAAqB,uBAC7D,CAAEG,MAAO,gBAAiBC,MAAOJ,EAAE,eAAgB,kBACnD,CAAEG,MAAO,uBAAwBC,MAAOJ,EAAE,sBAAuB,0BAGnE,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DACZN,EAAE,sBAAuB,4BAE5BK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EP,EAAE,iBAAkB,sBAEvBQ,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAASa,eAChBC,SAAWC,GAAMd,EAAkB,iBAAkBc,EAAEC,OAAOV,OAC9DG,UAAU,kKACVQ,YAAY,YAGhBT,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EP,EAAE,YAAa,iBAElBQ,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAASkB,UAChBJ,SAAWC,GAAMd,EAAkB,YAAac,EAAEC,OAAOV,OACzDG,UAAU,wKAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EP,EAAE,UAAW,cAEhBQ,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAASmB,QAChBL,SAAWC,GAAMd,EAAkB,UAAWc,EAAEC,OAAOV,OACvDG,UAAU,kKACVQ,YAAY,WAGhBT,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EP,EAAE,eAAgB,oBAErBQ,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAASoB,aAChBN,SAAWC,GAAMd,EAAkB,eAAgBc,EAAEC,OAAOV,OAC5DG,UAAU,kKACVQ,YAAY,iBAOpBT,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mEACZN,EAAE,iBAAkB,uBAEvBK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,gDAA+CC,SAAA,CACzDP,EAAE,uBAAwB,wDAAwD,KAACQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAErHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDL,EAAsBgB,IAAKC,IAC1Bd,EAAAA,EAAAA,MAAA,SAEEC,UAAS,8EAAAc,OACPvB,EAASwB,iBAAmBF,EAAOhB,MAC/B,uFACA,2FACHI,SAAA,EAEHC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLa,KAAK,iBACLnB,MAAOgB,EAAOhB,MACdoB,QAAS1B,EAASwB,iBAAmBF,EAAOhB,MAC5CQ,SAAWC,GAAMd,EAAkB,iBAAkBc,EAAEC,OAAOV,OAC9DG,UAAU,aAEZD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uEAAAc,OACZvB,EAASwB,iBAAmBF,EAAOhB,MAC/B,gCACA,wCACHI,SACAV,EAASwB,iBAAmBF,EAAOhB,QAClCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAGnBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEY,EAAOf,aAzBnCe,EAAOhB,UA8BjBJ,EAAOsB,iBACNb,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAER,EAAOsB,2BCsE7D,EAtLgCzB,IAA2D,IAA1D,SAAEC,EAAQ,kBAAEC,EAAiB,oBAAE0B,GAAqB5B,EACnF,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERwB,EAA4B,CAChC,CAAEC,IAAK,QAAStB,MAAOJ,EAAE,QAAS,UAClC,CAAE0B,IAAK,8BAA+BtB,MAAOJ,EAAE,8BAA+B,mCAC9E,CAAE0B,IAAK,gBAAiBtB,MAAOJ,EAAE,gBAAiB,kBAClD,CAAE0B,IAAK,QAAStB,MAAOJ,EAAE,QAAS,WAG9B2B,EAAiB,CACrB,CAAED,IAAK,WAAYtB,MAAOJ,EAAE,WAAY,aACxC,CAAE0B,IAAK,YAAatB,MAAOJ,EAAE,YAAa,cAC1C,CAAE0B,IAAK,YAAatB,MAAOJ,EAAE,YAAa,cAC1C,CAAE0B,IAAK,kBAAmBtB,MAAOJ,EAAE,kBAAmB,qBACtD,CAAE0B,IAAK,iBAAkBtB,MAAOJ,EAAE,iBAAkB,oBACpD,CAAE0B,IAAK,wBAAyBtB,MAAOJ,EAAE,wBAAyB,2BAClE,CAAE0B,IAAK,iBAAkBtB,MAAOJ,EAAE,iBAAkB,oBACpD,CAAE0B,IAAK,gBAAiBtB,MAAOJ,EAAE,gBAAiB,kBAClD,CAAE0B,IAAK,WAAYtB,MAAOJ,EAAE,WAAY,aACxC,CAAE0B,IAAK,kBAAmBtB,MAAOJ,EAAE,kBAAmB,qBACtD,CAAE0B,IAAK,iBAAkBtB,MAAOJ,EAAE,iBAAkB,oBACpD,CAAE0B,IAAK,yBAA0BtB,MAAOJ,EAAE,yBAA0B,6BACpE,CAAE0B,IAAK,oBAAqBtB,MAAOJ,EAAE,oBAAqB,uBAC1D,CAAE0B,IAAK,iBAAkBtB,MAAOJ,EAAE,iBAAkB,oBACpD,CAAE0B,IAAK,MAAOtB,MAAOJ,EAAE,MAAO,QAC9B,CAAE0B,IAAK,SAAUtB,MAAOJ,EAAE,SAAU,WACpC,CAAE0B,IAAK,YAAatB,MAAOJ,EAAE,YAAa,gBAGtC4B,EAA4BA,CAACC,EAASC,EAAOC,EAAMC,KACvD3B,EAAAA,EAAAA,MAAA,OAAKC,UAAS,MAAAc,OAAQY,EAAK,gBAAAZ,OAAeY,EAAK,0BAAAZ,OAAyBY,EAAK,qBAAAZ,OAAoBY,EAAK,uBAAsBzB,SAAA,EAC1HF,EAAAA,EAAAA,MAAA,MAAIC,UAAS,8BAAAc,OAAgCY,EAAK,mBAAAZ,OAAkBY,EAAK,aAAYzB,SAAA,EACnFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAc,OAAKW,EAAI,UAAAX,OAASY,EAAK,mBAAAZ,OAAkBY,EAAK,eACzDF,MAEHtB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDkB,EAA0BP,IAAKC,IAC9Bd,EAAAA,EAAAA,MAAA,SAAwBC,UAAU,oBAAmBC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLc,QAAS1B,EAASgC,GAASV,EAAOO,KAClCf,SAAWC,GAAMd,EAAkB,GAADsB,OAAIS,EAAO,KAAAT,OAAID,EAAOO,KAAOd,EAAEC,OAAOU,SACxEjB,UAAS,aAAAc,OAAeY,EAAK,oBAAAZ,OAAmBY,EAAK,WAEvDxB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,gBAAAc,OAAkBY,EAAK,mBAAAZ,OAAkBY,EAAK,QAAOzB,SACjEY,EAAOf,UARAe,EAAOO,QAetB7B,EAASgC,GAASI,QACjB5B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,kCAAAc,OAAoCY,EAAK,mBAAAZ,OAAkBY,EAAK,aAAYzB,SAAA,CACzFP,EAAE,mBAAoB,qBAAqB,QAE9CQ,EAAAA,EAAAA,KAAA,YACEL,MAAON,EAASgC,GAASK,iBACzBvB,SAAWC,GAAMd,EAAkB,GAADsB,OAAIS,EAAO,qBAAqBjB,EAAEC,OAAOV,OAC3EgC,KAAM,EACN7B,UAAS,kCAAAc,OAAoCY,EAAK,4CAAAZ,OAA2CY,EAAK,sBAAAZ,OAAqBY,EAAK,iBAAAZ,OAAgBY,EAAK,qBAAAZ,OAAoBY,EAAK,wBAC1KlB,YAAad,EAAE,0BAA2B,8CAO9CoC,EAAiBA,CAACP,EAASC,EAAOC,EAAMC,KAC5C3B,EAAAA,EAAAA,MAAA,OAAKC,UAAS,MAAAc,OAAQY,EAAK,gBAAAZ,OAAeY,EAAK,0BAAAZ,OAAyBY,EAAK,qBAAAZ,OAAoBY,EAAK,uBAAsBzB,SAAA,EAC1HF,EAAAA,EAAAA,MAAA,MAAIC,UAAS,8BAAAc,OAAgCY,EAAK,mBAAAZ,OAAkBY,EAAK,aAAYzB,SAAA,EACnFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAc,OAAKW,EAAI,UAAAX,OAASY,EAAK,mBAAAZ,OAAkBY,EAAK,eACzDF,MAIHtB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sGAAqGC,UAClHF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLc,QAAS1B,EAASgC,GAASQ,UAC3B1B,SAAWC,GAAMY,EAAoBK,EAAS,YAAajB,EAAEC,OAAOU,SACpEjB,UAAU,8CAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iDAAgDC,SAC7DP,EAAE,YAAa,sBAMtBQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDoB,EAAeW,OAAOnB,GAAyB,cAAfA,EAAOO,KAAqBR,IAAKC,IAChEd,EAAAA,EAAAA,MAAA,SAEEC,UAAS,qBAAAc,OAAuBvB,EAASgC,GAASQ,UAAY,aAAe,IAAK9B,SAAA,EAElFC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLc,QAAS1B,EAASgC,GAASV,EAAOO,KAClCf,SAAWC,GAAMY,EAAoBK,EAASV,EAAOO,IAAKd,EAAEC,OAAOU,SACnEgB,SAAU1C,EAASgC,GAASQ,UAC5B/B,UAAS,aAAAc,OAAeY,EAAK,oBAAAZ,OAAmBY,EAAK,WAEvDxB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,gBAAAc,OAAkBY,EAAK,mBAAAZ,OAAkBY,EAAK,QAAOzB,SACjEY,EAAOf,UAXLe,EAAOO,QAkBjB7B,EAASgC,GAASW,SAAW3C,EAASgC,GAASQ,YAC9ChC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,kCAAAc,OAAoCY,EAAK,mBAAAZ,OAAkBY,EAAK,aAAYzB,SAAA,CACzFP,EAAE,oBAAqB,sBAAsB,QAEhDQ,EAAAA,EAAAA,KAAA,YACEL,MAAON,EAASgC,GAASY,kBACzB9B,SAAWC,GAAMd,EAAkB,GAADsB,OAAIS,EAAO,sBAAsBjB,EAAEC,OAAOV,OAC5EgC,KAAM,EACN7B,UAAS,kCAAAc,OAAoCY,EAAK,4CAAAZ,OAA2CY,EAAK,sBAAAZ,OAAqBY,EAAK,iBAAAZ,OAAgBY,EAAK,qBAAAZ,OAAoBY,EAAK,wBAC1KlB,YAAad,EAAE,wBAAyB,gCAK7CH,EAASgC,GAASQ,YACjBhC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZN,EAAE,oBAAqB,2DAMhC,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEACZN,EAAE,8BAA+B,uCAGpCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvBqB,EACC,4BACA5B,EAAE,4BAA6B,+BAC/B,uBACA,QAIDoC,EACC,kBACApC,EAAE,kBAAmB,oBACrB,oBACA,OAID4B,EACC,8BACA5B,EAAE,8BAA+B,kCACjC,eACA,SAIDoC,EACC,oBACApC,EAAE,oBAAqB,sBACvB,oBACA,iBCLV,EA1KwBJ,IAA8C,IAA7C,SAAEC,EAAQ,kBAAEC,EAAiB,OAAEC,GAAQH,EAC9D,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERyC,EAA4B,CAChC,CAAEvC,MAAO,YAAaC,MAAOJ,EAAE,YAAa,cAC5C,CAAEG,MAAO,cAAeC,MAAOJ,EAAE,cAAe,gBAChD,CAAEG,MAAO,iBAAkBC,MAAOJ,EAAE,gBAAiB,mBACrD,CAAEG,MAAO,iBAAkBC,MAAOJ,EAAE,gBAAiB,oBAGjD2C,EAA4B,CAChC,CAAExC,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,SAClC,CAAEG,MAAO,WAAYC,MAAOJ,EAAE,WAAY,aAC1C,CAAEG,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,SAClC,CAAEG,MAAO,iBAAkBC,MAAOJ,EAAE,gBAAiB,oBAqBjD4C,EAAmBA,CAACC,EAAOC,EAASC,EAAeC,EAAYlB,EAAOmB,KAC1E5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,CACrEuB,EAAM,KAACtB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEzCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SAAE0C,KAE9DzC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEuC,EAAQ5B,IAAKC,IACZ,MAAMa,EA3BU7B,KACtB,OAAQA,GACN,IAAK,cACL,IAAK,OACH,MAAO,QACT,IAAK,iBACL,IAAK,WACH,MAAO,SACT,IAAK,YACL,IAAK,OACH,MAAO,MACT,IAAK,iBACH,MAAO,OACT,QACE,MAAO,SAaS+C,CAAe/B,EAAOhB,OAC9BgD,EAAaJ,IAAkB5B,EAAOhB,MAE5C,OACEE,EAAAA,EAAAA,MAAA,SAEEC,UAAS,8EAAAc,OACP+B,EAAU,UAAA/B,OACIY,EAAK,YAAAZ,OAAWY,EAAK,gBAAAZ,OAAeY,EAAK,iBAAAZ,OAAgBY,EAAK,mBAAAZ,OAAkBY,EAAK,QAC/F,yFACHzB,SAAA,EAEHC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLa,KAAMuB,EACN1C,MAAOgB,EAAOhB,MACdoB,QAAS4B,EACTxC,SAAWC,GAAMd,EAAkB+C,EAAOjC,EAAEC,OAAOV,OACnDG,UAAU,aAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uEAAAc,OACZ+B,EAAU,UAAA/B,OACIY,EAAK,YAAAZ,OAAWY,EAAK,QAC/B,wCACHzB,SACA4C,IACC3C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAGnBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAEY,EAAOf,UAxBzCe,EAAOhB,WA+BD,mBAAlB4C,IACC1C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EP,EAAE,SAAU,UAAU,KAACQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEzDC,EAAAA,EAAAA,KAAA,YACEL,MAAON,EAAS,GAADuB,OAAIyB,EAAK,YAAa,GACrClC,SAAWC,GAAMd,EAAkB,GAADsB,OAAIyB,EAAK,UAAUjC,EAAEC,OAAOV,OAC9DgC,KAAM,EACN7B,UAAS,mJAAAc,OACPrB,EAAO,GAADqB,OAAIyB,EAAK,WAAY,iBAAmB,mBAEhD/B,YAAad,EAAE,0BAA2B,+CAE3CD,EAAO,GAADqB,OAAIyB,EAAK,aACdrC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAER,EAAO,GAADqB,OAAIyB,EAAK,gBAK9D9C,EAAOiD,KACNxC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAER,EAAOiD,QAKvD,OACE3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACZN,EAAE,4BAA6B,oCAGlCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvBqC,EACC,4BACAF,EACA7C,EAASuD,0BACT,4BACApD,EAAE,4BAA6B,gCAC/BA,EAAE,gCAAiC,+EAIpC4C,EACC,4BACAD,EACA9C,EAASwD,0BACT,4BACArD,EAAE,4BAA6B,gCAC/BA,EAAE,gCAAiC,kFAIrCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZN,EAAE,qBAAsB,2BAE3BQ,EAAAA,EAAAA,KAAA,YACEL,MAAON,EAASyD,SAChB3C,SAAWC,GAAMd,EAAkB,WAAYc,EAAEC,OAAOV,OACxDgC,KAAM,EACN7B,UAAU,kKACVQ,YAAad,EAAE,sBAAuB,mGAExCQ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDP,EAAE,eAAgB,wHAKvBK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACZN,EAAE,uBAAwB,yBAAyB,QAEtDK,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,WAAEF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAASP,EAAE,YAAa,aAAa,OAAU,IAAEA,EAAE,uBAAwB,yDACjFK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,WAAEF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAASP,EAAE,gBAAiB,kBAAkB,OAAU,IAAEA,EAAE,2BAA4B,kDAC9FK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,WAAEF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAASP,EAAE,cAAe,eAAe,OAAU,IAAEA,EAAE,yBAA0B,2DACvFK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,WAAEF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAASP,EAAE,gBAAiB,kBAAkB,OAAU,IAAEA,EAAE,2BAA4B,yEC8B1G,EAhMgCJ,IAAsC,IAArC,SAAEC,EAAQ,kBAAEC,GAAmBF,EAC9D,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERsD,EAA0B,CAC9B,CAAE7B,IAAK,iBAAkBtB,MAAOJ,EAAE,iBAAkB,oBACpD,CAAE0B,IAAK,6BAA8BtB,MAAOJ,EAAE,6BAA8B,uEAC5E,CAAE0B,IAAK,mCAAoCtB,MAAOJ,EAAE,mCAAoC,gDACxF,CAAE0B,IAAK,iBAAkBtB,MAAOJ,EAAE,iBAAkB,oBACpD,CAAE0B,IAAK,0BAA2BtB,MAAOJ,EAAE,0BAA2B,6BACtE,CAAE0B,IAAK,qBAAsBtB,MAAOJ,EAAE,qBAAsB,wBAC5D,CAAE0B,IAAK,uBAAwBtB,MAAOJ,EAAE,uBAAwB,6CAChE,CAAE0B,IAAK,iCAAkCtB,MAAOJ,EAAE,iCAAkC,0CACpF,CAAE0B,IAAK,gCAAiCtB,MAAOJ,EAAE,gCAAiC,6CAClF,CAAE0B,IAAK,sCAAuCtB,MAAOJ,EAAE,sCAAuC,gDAC9F,CAAE0B,IAAK,gCAAiCtB,MAAOJ,EAAE,gCAAiC,yCAClF,CAAE0B,IAAK,oBAAqBtB,MAAOJ,EAAE,oBAAqB,4BAC1D,CAAE0B,IAAK,2BAA4BtB,MAAOJ,EAAE,2BAA4B,+BACxE,CAAE0B,IAAK,kBAAmBtB,MAAOJ,EAAE,kBAAmB,qBACtD,CAAE0B,IAAK,0BAA2BtB,MAAOJ,EAAE,0BAA2B,8BACtE,CAAE0B,IAAK,iBAAkBtB,MAAOJ,EAAE,iBAAkB,oBACpD,CAAE0B,IAAK,oBAAqBtB,MAAOJ,EAAE,oBAAqB,uBAC1D,CAAE0B,IAAK,YAAatB,MAAOJ,EAAE,YAAa,cAC1C,CAAE0B,IAAK,OAAQtB,MAAOJ,EAAE,OAAQ,SAChC,CAAE0B,IAAK,SAAUtB,MAAOJ,EAAE,SAAU,WACpC,CAAE0B,IAAK,SAAUtB,MAAOJ,EAAE,SAAU,iCAGhCwD,EAAwB,CAC5B,CAAE9B,IAAK,WAAYtB,MAAOJ,EAAE,WAAY,eACxC,CAAE0B,IAAK,gBAAiBtB,MAAOJ,EAAE,gBAAiB,mBAClD,CAAE0B,IAAK,UAAWtB,MAAOJ,EAAE,UAAW,aAGlCyD,EAAsB,CAC1B,CAAE/B,IAAK,mBAAoBtB,MAAOJ,EAAE,mBAAoB,sBACxD,CAAE0B,IAAK,QAAStB,MAAOJ,EAAE,QAAS,UAClC,CAAE0B,IAAK,qBAAsBtB,MAAOJ,EAAE,qBAAsB,wBAC5D,CAAE0B,IAAK,qBAAsBtB,MAAOJ,EAAE,qBAAsB,wBAC5D,CAAE0B,IAAK,QAAStB,MAAOJ,EAAE,QAAS,UAClC,CAAE0B,IAAK,gBAAiBtB,MAAOJ,EAAE,gBAAiB,mBAG9C0D,EAAoB,CACxB,CAAEhC,IAAK,yBAA0BtB,MAAOJ,EAAE,yBAA0B,0CACpE,CAAE0B,IAAK,iCAAkCtB,MAAOJ,EAAE,iCAAkC,uCACpF,CAAE0B,IAAK,uCAAwCtB,MAAOJ,EAAE,uCAAwC,6CAChG,CAAE0B,IAAK,wBAAyBtB,MAAOJ,EAAE,wBAAyB,4BAClE,CAAE0B,IAAK,sBAAuBtB,MAAOJ,EAAE,sBAAuB,yBAC9D,CAAE0B,IAAK,yBAA0BtB,MAAOJ,EAAE,yBAA0B,6BACpE,CAAE0B,IAAK,gBAAiBtB,MAAOJ,EAAE,gBAAiB,mBAClD,CAAE0B,IAAK,eAAgBtB,MAAOJ,EAAE,eAAgB,oBAG5C2D,EAAsBA,CAAC7B,EAAOgB,EAASjB,EAASE,EAAMC,KAC1D3B,EAAAA,EAAAA,MAAA,OAAKC,UAAS,MAAAc,OAAQY,EAAK,gBAAAZ,OAAeY,EAAK,0BAAAZ,OAAyBY,EAAK,qBAAAZ,OAAoBY,EAAK,uBAAsBzB,SAAA,EAC1HF,EAAAA,EAAAA,MAAA,MAAIC,UAAS,8BAAAc,OAAgCY,EAAK,mBAAAZ,OAAkBY,EAAK,aAAYzB,SAAA,EACnFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAc,OAAKW,EAAI,UAAAX,OAASY,EAAK,mBAAAZ,OAAkBY,EAAK,eACzDF,MAEHtB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClEuC,EAAQ5B,IAAKC,IACZd,EAAAA,EAAAA,MAAA,SAAwBC,UAAU,mBAAkBC,SAAA,EAClDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLc,QAAS1B,EAASgC,GAASV,EAAOO,KAClCf,SAAWC,GAAMd,EAAkB,GAADsB,OAAIS,EAAO,KAAAT,OAAID,EAAOO,KAAOd,EAAEC,OAAOU,SACxEjB,UAAS,kBAAAc,OAAoBY,EAAK,oBAAAZ,OAAmBY,EAAK,WAE5DxB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,gBAAAc,OAAkBY,EAAK,mBAAAZ,OAAkBY,EAAK,sBAAqBzB,SAC/EY,EAAOf,UARAe,EAAOO,QAeV,qBAAZG,GAAkChC,EAASgC,GAASW,SACnDnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,kCAAAc,OAAoCY,EAAK,mBAAAZ,OAAkBY,EAAK,aAAYzB,SAAA,CACzFP,EAAE,oBAAqB,sBAAsB,QAEhDQ,EAAAA,EAAAA,KAAA,YACEL,MAAON,EAASgC,GAASY,kBACzB9B,SAAWC,GAAMd,EAAkB,GAADsB,OAAIS,EAAO,sBAAsBjB,EAAEC,OAAOV,OAC5EgC,KAAM,EACN7B,UAAS,kCAAAc,OAAoCY,EAAK,4CAAAZ,OAA2CY,EAAK,sBAAAZ,OAAqBY,EAAK,iBAAAZ,OAAgBY,EAAK,qBAAAZ,OAAoBY,EAAK,wBAC1KlB,YAAad,EAAE,+BAAgC,2CAOzD,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACZN,EAAE,iCAAkC,2CAGvCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvBoD,EACC3D,EAAE,mBAAoB,qBACtBuD,EACA,mBACA,oBACA,WAIFlD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEACZN,EAAE,iBAAkB,uBAGvBK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACxEP,EAAE,iBAAkB,sBAEvBQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBiD,EAAsBtC,IAAKC,IAC1Bd,EAAAA,EAAAA,MAAA,SAAwBC,UAAU,oBAAmBC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLc,QAAS1B,EAAS+D,eAAezC,EAAOO,KACxCf,SAAWC,GAAMd,EAAkB,kBAADsB,OAAmBD,EAAOO,KAAOd,EAAEC,OAAOU,SAC5EjB,UAAU,4CAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDY,EAAOf,UARAe,EAAOO,YAgBzBrB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACxEP,EAAE,eAAgB,oBAErBQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBkD,EAAoBvC,IAAKC,IACxBd,EAAAA,EAAAA,MAAA,SAAwBC,UAAU,oBAAmBC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLc,QAAS1B,EAASgE,aAAa1C,EAAOO,KACtCf,SAAWC,GAAMd,EAAkB,gBAADsB,OAAiBD,EAAOO,KAAOd,EAAEC,OAAOU,SAC1EjB,UAAU,4CAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDY,EAAOf,UARAe,EAAOO,iBAkB5BiC,EACC3D,EAAE,aAAc,cAChB0D,EACA,aACA,yBACA,UAIFrD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEACZN,EAAE,sBAAuB,wBAAwB,QAEpDK,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGP,EAAE,sBAAuB,uEAChCK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGP,EAAE,0BAA2B,yEACpCK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGP,EAAE,mBAAoB,4DAC7BK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGP,EAAE,wBAAyB,wDAClCK,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGP,EAAE,mBAAoB,0DC6FzC,EArRmCJ,IAA8C,IAA7C,SAAEC,EAAQ,kBAAEC,EAAiB,OAAEC,GAAQH,EACzE,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAER6D,EAAuB,CAC3B,CAAE3D,MAAO,SAAUC,MAAOJ,EAAE,SAAU,WACtC,CAAEG,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,SAClC,CAAEG,MAAO,iBAAkBC,MAAOJ,EAAE,gBAAiB,oBAGjD+D,EAA2B,CAC/B,CAAE5D,MAAO,MAAOC,MAAOJ,EAAE,MAAO,QAChC,CAAEG,MAAO,KAAMC,MAAOJ,EAAE,KAAM,OAC9B,CAAEG,MAAO,iBAAkBC,MAAOJ,EAAE,gBAAiB,oBAGjDgE,EAAwB,CAC5B,CAAE7D,MAAO,MAAOC,MAAOJ,EAAE,MAAO,QAChC,CAAEG,MAAO,KAAMC,MAAOJ,EAAE,KAAM,QAG1BiE,EAA2B,CAC/B,CAAEvC,IAAK,YAAatB,MAAOJ,EAAE,YAAa,cAC1C,CAAE0B,IAAK,SAAUtB,MAAOJ,EAAE,SAAU,WACpC,CAAE0B,IAAK,eAAgBtB,MAAOJ,EAAE,eAAgB,kBAChD,CAAE0B,IAAK,sBAAuBtB,MAAOJ,EAAE,sBAAuB,yBAC9D,CAAE0B,IAAK,SAAUtB,MAAOJ,EAAE,SAAU,YAGhC4C,EAAmBA,CAACC,EAAOC,EAASC,EAAeC,EAAYlB,EAAOmB,KAC1E5C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,CACrEuB,EAAM,KAACtB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,SAExC0C,IACCzC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SAAE0C,KAGhEzC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uBAAsBC,SAClCuC,EAAQ5B,IAAKC,IACZd,EAAAA,EAAAA,MAAA,SAEEC,UAAS,kFAAAc,OACP2B,IAAkB5B,EAAOhB,MACrB,kFACA,yFACHI,SAAA,EAEHC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLa,KAAMuB,EACN1C,MAAOgB,EAAOhB,MACdoB,QAASwB,IAAkB5B,EAAOhB,MAClCQ,SAAWC,GAAMd,EAAkB+C,EAAOjC,EAAEC,OAAOV,OACnDG,UAAU,aAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uEAAAc,OACZ2B,IAAkB5B,EAAOhB,MACrB,8BACA,wCACHI,SACAwC,IAAkB5B,EAAOhB,QACxBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAGnBE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAEY,EAAOf,UAxBzCe,EAAOhB,UA6BjBJ,EAAOiD,KACNxC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAER,EAAOiD,QAKvD,OACE3C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qEACZN,EAAE,sBAAuB,4BAG5BK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvBqC,EACC,gBACAkB,EACAjE,EAASqE,cACT,gBACAlE,EAAE,gBAAiB,kBACnBA,EAAE,2BAA4B,sDAI/B4C,EACC,oBACAmB,EACAlE,EAASsE,kBACT,oBACAnE,EAAE,8BAA+B,+DACjCA,EAAE,+BAAgC,0DAInC4C,EACC,iBACAoB,EACAnE,EAASuE,eACT,iBACApE,EAAE,iBAAkB,mBACpBA,EAAE,4BAA6B,mEAMrCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDACZN,EAAE,oBAAqB,2BAG1BK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gEAA+DC,SAC1EP,EAAE,mCAAoC,6CAEzCQ,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE0D,EAAyB/C,IAAKC,IAC7Bd,EAAAA,EAAAA,MAAA,SAAwBC,UAAU,oBAAmBC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLc,QAAS1B,EAASwE,0BAA0BlD,EAAOO,KACnDf,SAAWC,GAAMd,EAAkB,6BAADsB,OAA8BD,EAAOO,KAAOd,EAAEC,OAAOU,SACvFjB,UAAU,8CAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,6CAA4CC,SACzDY,EAAOf,UARAe,EAAOO,QAetB7B,EAASwE,0BAA0B7B,SAClCnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oEAAmEC,SAAA,CACjFP,EAAE,oBAAqB,sBAAsB,QAEhDQ,EAAAA,EAAAA,KAAA,YACEL,MAAON,EAASwE,0BAA0B5B,kBAC1C9B,SAAWC,GAAMd,EAAkB,8CAA+Cc,EAAEC,OAAOV,OAC3FgC,KAAM,EACN7B,UAAU,uKACVQ,YAAad,EAAE,2BAA4B,2CAQrDK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEACZN,EAAE,mBAAoB,0BAGzBK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,KAAA,YACEL,MAAON,EAASyE,iBAChB3D,SAAWC,GAAMd,EAAkB,mBAAoBc,EAAEC,OAAOV,OAChEgC,KAAM,EACN7B,UAAU,4KACVQ,YAAad,EAAE,2BAA4B,mJAE7CQ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDAAmDC,SAC7DP,EAAE,oBAAqB,0IAM9BK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DACZN,EAAE,qBAAsB,2BAG3BK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EP,EAAE,gBAAiB,kBAAkB,KAACQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAExEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAAS0E,cAChB5D,SAAWC,GAAMd,EAAkB,gBAAiBc,EAAEC,OAAOV,OAC7DG,UAAS,mJAAAc,OACPrB,EAAOwE,cAAgB,iBAAmB,mBAE5CzD,YAAad,EAAE,qBAAsB,+BAEtCD,EAAOwE,gBACN/D,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAER,EAAOwE,oBAIrDlE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EP,EAAE,YAAa,gBAElBQ,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAAS2E,mBAChB7D,SAAWC,GAAMd,EAAkB,qBAAsBc,EAAEC,OAAOV,OAClEG,UAAU,kKACVQ,YAAad,EAAE,mBAAoB,2BAIvCK,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EP,EAAE,cAAe,aAAa,KAACQ,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEjEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAAS4E,iBAChB9D,SAAWC,GAAMd,EAAkB,mBAAoBc,EAAEC,OAAOV,OAChEG,UAAS,mJAAAc,OACPrB,EAAO0E,iBAAmB,iBAAmB,mBAE/C3D,YAAad,EAAE,mBAAoB,wBAEpCD,EAAO0E,mBACNjE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAER,EAAO0E,uBAIrDpE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EP,EAAE,OAAQ,WAEbQ,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLN,MAAON,EAAS6E,cAChB/D,SAAWC,GAAMd,EAAkB,gBAAiBc,EAAEC,OAAOV,OAC7DG,UAAU,2KAMhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EP,EAAE,mBAAoB,wBAEzBK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDP,EAAE,8BAA+B,8CAEpCK,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLH,UAAU,uFAAsFC,SAAA,EAEhGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZN,EAAE,eAAgB,iCCmNjC,EAvdsBJ,IAAsC,IAArC,UAAE+E,EAAS,OAAEC,EAAM,SAAEC,GAAUjF,EACpD,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,MACR,KAAE6E,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,OACTN,UAAWO,EAAY,YAAEC,IAAgBC,EAAAA,EAAAA,MAC1CC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCxF,EAAQyF,IAAaD,EAAAA,EAAAA,UAAS,CAAC,IAC/BE,EAASC,IAAcH,EAAAA,EAAAA,UAAS,MAGjCI,EAAkBhB,GAAaO,GAG9BrF,EAAU+F,IAAeL,EAAAA,EAAAA,UAAS,CAEvC7E,eAAgB,MAChBK,WAAW,IAAI8E,MAAOC,cAAcC,MAAM,KAAK,GAC/C/E,QAAS,KACTC,aAAc,KAGdI,eAAgB,GAGhB2E,0BAA2B,CACzBC,OAAO,EACPC,6BAA6B,EAC7BC,eAAe,EACflE,OAAO,EACPC,iBAAkB,IAIpBkE,gBAAiB,CACfC,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,iBAAiB,EACjBC,gBAAgB,EAChBC,uBAAuB,EACvBC,gBAAgB,EAChBC,eAAe,EACfC,UAAU,EACVrE,QAAQ,EACRC,kBAAmB,GACnBJ,WAAW,EACXyE,iBAAiB,EACjBC,gBAAgB,EAChBC,wBAAwB,EACxBC,mBAAmB,EACnBC,gBAAgB,EAChBC,KAAK,GAIPC,4BAA6B,CAC3BnB,OAAO,EACPC,6BAA6B,EAC7BC,eAAe,EACflE,OAAO,EACPC,iBAAkB,IAIpBmF,kBAAmB,CACjBhB,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,iBAAiB,EACjBC,gBAAgB,EAChBC,uBAAuB,EACvBC,gBAAgB,EAChBC,eAAe,EACfC,UAAU,EACVrE,QAAQ,EACRC,kBAAmB,GACnBJ,WAAW,EACXyE,iBAAiB,EACjBC,gBAAgB,EAChBC,wBAAwB,EACxBC,mBAAmB,EACnBC,gBAAgB,EAChBC,KAAK,GAIP/D,0BAA2B,GAC3BkE,gCAAiC,GACjCjE,0BAA2B,GAC3BkE,gCAAiC,GAGjCjE,SAAU,GAGVkE,iBAAkB,CAChBC,gBAAgB,EAChBC,4BAA4B,EAC5BC,kCAAkC,EAClCC,gBAAgB,EAChBC,yBAAyB,EACzBC,oBAAoB,EACpBC,sBAAsB,EACtBC,gCAAgC,EAChCC,+BAA+B,EAC/BC,qCAAqC,EACrCC,+BAA+B,EAC/BC,mBAAmB,EACnBC,0BAA0B,EAC1BC,iBAAiB,EACjB9F,QAAQ,EACRC,kBAAmB,GACnB8F,yBAAyB,EACzBC,gBAAgB,EAChBC,mBAAmB,EACnBC,WAAW,EACXC,MAAM,EACNC,QAAQ,GAIVhF,eAAgB,CACdiF,UAAU,EACVC,eAAe,EACfC,SAAS,GAEXlF,aAAc,CACZmF,kBAAkB,EAClBC,OAAO,EACPC,oBAAoB,EACpBC,oBAAoB,EACpBlD,OAAO,EACPE,eAAe,GAIjBiD,WAAY,CACVC,wBAAwB,EACxBC,gCAAgC,EAChCC,sCAAsC,EACtCC,uBAAuB,EACvBC,qBAAqB,EACrBC,wBAAwB,EACxBC,eAAe,EACfC,cAAc,GAIhB1F,cAAe,GACfC,kBAAmB,GACnBC,eAAgB,GAGhBC,0BAA2B,CACzBwF,WAAW,EACXC,QAAQ,EACRC,cAAc,EACdC,qBAAqB,EACrBxH,QAAQ,EACRC,kBAAmB,IAIrB6B,iBAAkB,GAGlBC,eAAmB,OAAJO,QAAI,IAAJA,OAAI,EAAJA,EAAMxD,OAAQ,GAC7BkD,mBAAoB,GACpBC,kBAAsB,OAAJK,QAAI,IAAJA,OAAI,EAAJA,EAAMmF,UAAW,GACnCvF,eAAe,IAAImB,MAAOC,cAAcC,MAAM,KAAK,MAIrDmE,EAAAA,EAAAA,WAAU,KACJvE,IACFL,GAAW,GAEX6E,WAAW,KAYTzE,EAXoB,CAClB0E,GAAIzE,EACJrE,KAAM,uEACN+I,OAAQ,qBACRC,SAAU,cACVC,YAAa,aACbpD,IAAK,EACLqD,OAAQ,OACRC,UAAW,mBAIbnF,GAAW,IACV,OAEJ,CAACK,IAEJ,MAAM7F,EAAoBA,CAAC+C,EAAO1C,KAChC,MAAMuK,GAAOC,EAAAA,EAAAA,GAAA,GAAQ9K,GAGrB,GAAIgD,EAAM+H,SAAS,KAAM,CACvB,MAAMC,EAAQhI,EAAMkD,MAAM,KAC1B,IAAI+E,EAAUJ,EACd,IAAK,IAAIK,EAAI,EAAGA,EAAIF,EAAMG,OAAS,EAAGD,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMG,OAAS,IAAM7K,CACrC,MACEuK,EAAQ7H,GAAS1C,EAGnByF,EAAY8E,GAGR3K,EAAO8C,IACT2C,EAAUyF,IAAIN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUM,GAAI,IAAE,CAACpI,GAAQ,SAoH3C,OAAIwC,IAAYI,GAEZjF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAMnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,QAAM6K,SA5DWC,UAGnB,GAFAvK,EAAEwK,iBA1CiBC,MACnB,MAAMC,EAAY,CAAC,EAqCnB,OAlCKzL,EAASwB,iBACZiK,EAAUjK,eAAiBrB,EAAE,yBAA0B,gCAEpDH,EAASuD,4BACZkI,EAAUlI,0BAA4BpD,EAAE,6BAA8B,6CAEnEH,EAASwD,4BACZiI,EAAUjI,0BAA4BrD,EAAE,6BAA8B,6CAEnEH,EAASqE,gBACZoH,EAAUpH,cAAgBlE,EAAE,wBAAyB,qCAElDH,EAASsE,oBACZmH,EAAUnH,kBAAoBnE,EAAE,4BAA6B,yCAE1DH,EAASuE,iBACZkH,EAAUlH,eAAiBpE,EAAE,yBAA0B,sCAEpDH,EAAS0E,cAAcgH,SAC1BD,EAAU/G,cAAgBvE,EAAE,wBAAyB,+BAElDH,EAAS4E,iBAAiB8G,SAC7BD,EAAU7G,iBAAmBzE,EAAE,yBAA0B,uCAIhB,mBAAvCH,EAASuD,2BAAmDvD,EAASyH,gCAAgCiE,SACvGD,EAAUhE,gCAAkCtH,EAAE,iBAAkB,uDAEvB,mBAAvCH,EAASwD,2BAAmDxD,EAAS0H,gCAAgCgE,SACvGD,EAAU/D,gCAAkCvH,EAAE,iBAAkB,uDAGlEwF,EAAU8F,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWN,QAMzBK,GAAL,CAKA/F,GAAW,GAEX,IAIE,SAFM,IAAIoG,QAAQC,GAAWxB,WAAWwB,EAAS,MAE7C/G,EACFA,EAAO/E,OACF,CAEL,MAAM+L,EAAsBC,KAAKC,MAAMC,aAAaC,QAAQ,sBAAwB,MAC9EC,GAAOtB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACR9K,GAAQ,IACXuK,GAAIvE,KAAKqG,MACTvH,UAAWgB,EACXwG,WAAW,IAAItG,MAAOC,cACtBsG,WAAW,IAAIvG,MAAOC,gBAExB8F,EAAoBS,KAAKJ,GACzBF,aAAaO,QAAQ,oBAAqBT,KAAKU,UAAUX,IAEzDY,EAAAA,GAAMC,QAAQzM,EAAE,qBAAsB,yDAGpCgF,EADEW,EACO,aAADvE,OAAcuE,GAEb,YAEb,CACF,CAAE,MAAO+G,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CF,EAAAA,GAAME,MAAM1M,EAAE,kBAAmB,+BACnC,CAAC,QACCsF,GAAW,EACb,CApCA,MAFEkH,EAAAA,GAAME,MAAM1M,EAAE,kBAAmB,6CAwDHM,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7DP,EAAE,6BAA8B,4DAChC2F,GAAmBF,IAClBpF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4DAA2DC,SAAA,CAAC,KACvEkF,EAAQ4E,QAAU5E,EAAQnE,YAInCd,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDP,EAAE,2BAA4B,kEAIjCK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qFAAoFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAC,uBAEzEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yDAAwDC,SAAC,wBAE3EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SAAC,2BAKjFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,CAC5BoF,IACCtF,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLmM,QAASA,IAAM5H,EAAS,aAAD5D,OAAcuE,IACrCrF,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZN,EAAE,cAAe,oBAGtBK,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLmM,QA9DUC,KAEtBL,EAAAA,GAAMC,QAAQzM,EAAE,cAAe,+BA6DnBM,UAAU,oFAAmFC,SAAA,EAE7FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZN,EAAE,YAAa,kBAElBQ,EAAAA,EAAAA,KAAA,UACEC,KAAK,SACLmM,QAAS/H,GAAQ,KAAWG,EAASW,EAAe,aAAAvE,OAAgBuE,GAAoB,cACxFrF,UAAU,uFAAsFC,SAE/FP,EAAE,SAAU,qBAOrBQ,EAAAA,EAAAA,KAACsM,EAAyB,CACxBjN,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,KAIVS,EAAAA,EAAAA,KAACuM,EAAuB,CACtBlN,SAAUA,EACVC,kBAAmBA,EACnB0B,oBApMoBA,CAACK,EAASgB,EAAO1C,KAC3C,MAAMuK,GAAOC,EAAAA,EAAAA,GAAA,GAAQ9K,GAEP,cAAVgD,GAAyB1C,GAE3BqL,OAAOC,KAAKf,EAAQ7I,IAAUmL,QAAQtL,IACxB,cAARA,GAA+B,sBAARA,IACzBgJ,EAAQ7I,GAASH,IAAO,KAG5BgJ,EAAQ7I,GAASY,kBAAoB,IAClB,cAAVI,GAAyB1C,IAElCuK,EAAQ7I,GAASQ,WAAY,GAG/BqI,EAAQ7I,GAASgB,GAAS1C,EAC1ByF,EAAY8E,OAuLRlK,EAAAA,EAAAA,KAACyM,EAAe,CACdpN,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,KAIVS,EAAAA,EAAAA,KAAC0M,EAAuB,CACtBrN,SAAUA,EACVC,kBAAmBA,KAIrBU,EAAAA,EAAAA,KAAC2M,EAA0B,CACzBtN,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,KAIVM,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEC,KAAK,SACLmM,QAAS/H,GAAQ,KAAWG,EAASW,EAAe,aAAAvE,OAAgBuE,GAAoB,cACxFrF,UAAU,+FAA8FC,SAEvGP,EAAE,SAAU,aAEfQ,EAAAA,EAAAA,KAAA,UACEC,KAAK,SACL8B,SAAU8C,EACV/E,UAAU,kIAAiIC,SAE1I8E,GACChF,EAAAA,EAAAA,MAAA+M,EAAAA,SAAA,CAAA7M,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZN,EAAE,SAAU,iBAGfK,EAAAA,EAAAA,MAAA+M,EAAAA,SAAA,CAAA7M,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZN,EAAE,oBAAqB,oC", "sources": ["components/EducationForm/DocumentAssessmentSection.jsx", "components/EducationForm/LearningBarriersSection.jsx", "components/EducationForm/SelfCareSection.jsx", "components/EducationForm/EducationalNeedsSection.jsx", "components/EducationForm/AdditionalQuestionsSection.jsx", "components/EducationForm/EducationForm.jsx"], "sourcesContent": ["import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst DocumentAssessmentSection = ({ formData, handleInputChange, errors }) => {\n  const { t } = useLanguage();\n\n  const assessmentTypeOptions = [\n    { value: 'Initial Assessment', label: t('initialAssessment', 'Initial Assessment') },\n    { value: 'Re-Assessment', label: t('reAssessment', 'Re-Assessment') },\n    { value: 'Discharge Assessment', label: t('dischargeAssessment', 'Discharge Assessment') }\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Document Information */}\n      <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          <i className=\"fas fa-file-alt text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('documentInformation', 'Document Information')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('documentNumber', 'Document Number')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.documentNumber}\n              onChange={(e) => handleInputChange('documentNumber', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"QP-\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('issueDate', 'Issue Date')}\n            </label>\n            <input\n              type=\"date\"\n              value={formData.issueDate}\n              onChange={(e) => handleInputChange('issueDate', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('version', 'Version')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.version}\n              onChange={(e) => handleInputChange('version', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"01\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('reviewNumber', 'Review Number')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.reviewNumber}\n              onChange={(e) => handleInputChange('reviewNumber', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"01\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Assessment Type */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          <i className=\"fas fa-clipboard-check text-green-600 dark:text-green-400 mr-2\"></i>\n          {t('assessmentType', 'Assessment Type')}\n        </h2>\n        <div className=\"space-y-3\">\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n            {t('selectAssessmentType', 'Please select the type of assessment being conducted')} <span className=\"text-red-500\">*</span>\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {assessmentTypeOptions.map((option) => (\n              <label\n                key={option.value}\n                className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                  formData.assessmentType === option.value\n                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200'\n                    : 'border-gray-300 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-500'\n                }`}\n              >\n                <input\n                  type=\"radio\"\n                  name=\"assessmentType\"\n                  value={option.value}\n                  checked={formData.assessmentType === option.value}\n                  onChange={(e) => handleInputChange('assessmentType', e.target.value)}\n                  className=\"sr-only\"\n                />\n                <div className=\"flex items-center\">\n                  <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                    formData.assessmentType === option.value\n                      ? 'border-green-500 bg-green-500'\n                      : 'border-gray-300 dark:border-gray-600'\n                  }`}>\n                    {formData.assessmentType === option.value && (\n                      <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                    )}\n                  </div>\n                  <span className=\"font-medium\">{option.label}</span>\n                </div>\n              </label>\n            ))}\n          </div>\n          {errors.assessmentType && (\n            <p className=\"text-red-500 text-sm mt-2\">{errors.assessmentType}</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DocumentAssessmentSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst LearningBarriersSection = ({ formData, handleInputChange, handleBarrierChange }) => {\n  const { t } = useLanguage();\n\n  const learningPreferenceOptions = [\n    { key: 'video', label: t('video', 'Video') },\n    { key: 'writtenMaterialsWithPicture', label: t('writtenMaterialsWithPicture', 'Written Materials with Picture') },\n    { key: 'demonstration', label: t('demonstration', 'Demonstration') },\n    { key: 'other', label: t('other', 'Other') }\n  ];\n\n  const barrierOptions = [\n    { key: 'physical', label: t('physical', 'Physical') },\n    { key: 'cognitive', label: t('cognitive', 'Cognitive') },\n    { key: 'emotional', label: t('emotional', 'Emotional') },\n    { key: 'culturalBelieve', label: t('culturalBelieve', 'Cultural/Believe') },\n    { key: 'poorMotivation', label: t('poorMotivation', 'Poor Motivation') },\n    { key: 'financialDifficulties', label: t('financialDifficulties', 'Financial Difficulties') },\n    { key: 'readingAbility', label: t('readingAbility', 'Reading Ability') },\n    { key: 'psychological', label: t('psychological', 'Psychological') },\n    { key: 'language', label: t('language', 'Language') },\n    { key: 'impairedHearing', label: t('impairedHearing', 'Impaired Hearing') },\n    { key: 'speechBarriers', label: t('speechBarriers', 'Speech Barriers') },\n    { key: 'responsibilitiesAtHome', label: t('responsibilitiesAtHome', 'Responsibilities at Home') },\n    { key: 'religiousPractice', label: t('religiousPractice', 'Religious Practice') },\n    { key: 'impairedVision', label: t('impairedVision', 'Impaired Vision') },\n    { key: 'age', label: t('age', 'Age') },\n    { key: 'others', label: t('others', 'Others') },\n    { key: 'noBarrier', label: t('noBarrier', 'No Barrier') }\n  ];\n\n  const renderLearningPreferences = (section, title, icon, color) => (\n    <div className={`bg-${color}-50 dark:bg-${color}-900/20 border border-${color}-200 dark:border-${color}-800 rounded-lg p-6`}>\n      <h3 className={`text-md font-semibold text-${color}-900 dark:text-${color}-100 mb-4`}>\n        <i className={`${icon} text-${color}-600 dark:text-${color}-400 mr-2`}></i>\n        {title}\n      </h3>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n        {learningPreferenceOptions.map((option) => (\n          <label key={option.key} className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={formData[section][option.key]}\n              onChange={(e) => handleInputChange(`${section}.${option.key}`, e.target.checked)}\n              className={`mr-3 text-${color}-600 focus:ring-${color}-500`}\n            />\n            <span className={`text-sm text-${color}-800 dark:text-${color}-200`}>\n              {option.label}\n            </span>\n          </label>\n        ))}\n      </div>\n      \n      {/* Other description field */}\n      {formData[section].other && (\n        <div className=\"mt-4\">\n          <label className={`block text-sm font-medium text-${color}-800 dark:text-${color}-200 mb-1`}>\n            {t('otherDescription', 'Other Description')}:\n          </label>\n          <textarea\n            value={formData[section].otherDescription}\n            onChange={(e) => handleInputChange(`${section}.otherDescription`, e.target.value)}\n            rows={2}\n            className={`w-full px-3 py-2 border border-${color}-300 rounded-lg focus:ring-2 focus:ring-${color}-500 focus:border-${color}-500 dark:bg-${color}-800 dark:border-${color}-600 dark:text-white`}\n            placeholder={t('describeOtherPreference', 'Describe other learning preference')}\n          />\n        </div>\n      )}\n    </div>\n  );\n\n  const renderBarriers = (section, title, icon, color) => (\n    <div className={`bg-${color}-50 dark:bg-${color}-900/20 border border-${color}-200 dark:border-${color}-800 rounded-lg p-6`}>\n      <h3 className={`text-md font-semibold text-${color}-900 dark:text-${color}-100 mb-4`}>\n        <i className={`${icon} text-${color}-600 dark:text-${color}-400 mr-2`}></i>\n        {title}\n      </h3>\n      \n      {/* No Barrier option - prominently displayed */}\n      <div className=\"mb-4 p-3 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg\">\n        <label className=\"flex items-center\">\n          <input\n            type=\"checkbox\"\n            checked={formData[section].noBarrier}\n            onChange={(e) => handleBarrierChange(section, 'noBarrier', e.target.checked)}\n            className=\"mr-3 text-green-600 focus:ring-green-500\"\n          />\n          <span className=\"font-medium text-green-800 dark:text-green-200\">\n            {t('noBarrier', 'No Barrier')}\n          </span>\n        </label>\n      </div>\n      \n      {/* Other barriers */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n        {barrierOptions.filter(option => option.key !== 'noBarrier').map((option) => (\n          <label \n            key={option.key} \n            className={`flex items-center ${formData[section].noBarrier ? 'opacity-50' : ''}`}\n          >\n            <input\n              type=\"checkbox\"\n              checked={formData[section][option.key]}\n              onChange={(e) => handleBarrierChange(section, option.key, e.target.checked)}\n              disabled={formData[section].noBarrier}\n              className={`mr-3 text-${color}-600 focus:ring-${color}-500`}\n            />\n            <span className={`text-sm text-${color}-800 dark:text-${color}-200`}>\n              {option.label}\n            </span>\n          </label>\n        ))}\n      </div>\n      \n      {/* Others description field */}\n      {formData[section].others && !formData[section].noBarrier && (\n        <div className=\"mt-4\">\n          <label className={`block text-sm font-medium text-${color}-800 dark:text-${color}-200 mb-1`}>\n            {t('othersDescription', 'Others Description')}:\n          </label>\n          <textarea\n            value={formData[section].othersDescription}\n            onChange={(e) => handleInputChange(`${section}.othersDescription`, e.target.value)}\n            rows={2}\n            className={`w-full px-3 py-2 border border-${color}-300 rounded-lg focus:ring-2 focus:ring-${color}-500 focus:border-${color}-500 dark:bg-${color}-800 dark:border-${color}-600 dark:text-white`}\n            placeholder={t('describeOtherBarriers', 'Describe other barriers')}\n          />\n        </div>\n      )}\n      \n      {formData[section].noBarrier && (\n        <div className=\"mt-3 text-sm text-green-700 dark:text-green-300\">\n          <i className=\"fas fa-info-circle mr-1\"></i>\n          {t('noBarrierSelected', 'No barriers selected - other options are disabled')}\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-graduation-cap text-purple-600 dark:text-purple-400 mr-2\"></i>\n        {t('learningPreferencesBarriers', 'Learning Preferences & Barriers')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Patient Learning Preferences */}\n        {renderLearningPreferences(\n          'patientLearningPreference',\n          t('patientLearningPreference', 'Patient Learning Preference'),\n          'fas fa-user-graduate',\n          'blue'\n        )}\n\n        {/* Patient Barriers */}\n        {renderBarriers(\n          'patientBarriers',\n          t('patientBarriers', 'Patient Barriers'),\n          'fas fa-user-times',\n          'red'\n        )}\n\n        {/* Caregiver Learning Preferences */}\n        {renderLearningPreferences(\n          'caregiverLearningPreference',\n          t('caregiverLearningPreference', 'Caregiver Learning Preferences'),\n          'fas fa-users',\n          'green'\n        )}\n\n        {/* Caregiver Barriers */}\n        {renderBarriers(\n          'caregiverBarriers',\n          t('caregiverBarriers', 'Caregiver Barriers'),\n          'fas fa-user-slash',\n          'orange'\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default LearningBarriersSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SelfCareSection = ({ formData, handleInputChange, errors }) => {\n  const { t } = useLanguage();\n\n  const selfCareCapabilityOptions = [\n    { value: 'Dependent', label: t('dependent', 'Dependent') },\n    { value: 'Independent', label: t('independent', 'Independent') },\n    { value: 'Need Assistant', label: t('needAssistant', 'Need Assistant') },\n    { value: 'Not Applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  const selfCareMotivationOptions = [\n    { value: 'Poor', label: t('poor', 'Poor') },\n    { value: 'Moderate', label: t('moderate', 'Moderate') },\n    { value: 'High', label: t('high', 'High') },\n    { value: 'Not Applicable', label: t('notApplicable', 'Not Applicable') }\n  ];\n\n  const getOptionColor = (value) => {\n    switch (value) {\n      case 'Independent':\n      case 'High':\n        return 'green';\n      case 'Need Assistant':\n      case 'Moderate':\n        return 'yellow';\n      case 'Dependent':\n      case 'Poor':\n        return 'red';\n      case 'Not Applicable':\n        return 'gray';\n      default:\n        return 'blue';\n    }\n  };\n\n  const renderRadioGroup = (field, options, selectedValue, errorField, title, description) => (\n    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n      <h3 className=\"text-md font-semibold text-gray-900 dark:text-white mb-2\">\n        {title} <span className=\"text-red-500\">*</span>\n      </h3>\n      <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">{description}</p>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\">\n        {options.map((option) => {\n          const color = getOptionColor(option.value);\n          const isSelected = selectedValue === option.value;\n          \n          return (\n            <label\n              key={option.value}\n              className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${\n                isSelected\n                  ? `border-${color}-500 bg-${color}-50 dark:bg-${color}-900/20 text-${color}-800 dark:text-${color}-200`\n                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n              }`}\n            >\n              <input\n                type=\"radio\"\n                name={field}\n                value={option.value}\n                checked={isSelected}\n                onChange={(e) => handleInputChange(field, e.target.value)}\n                className=\"sr-only\"\n              />\n              <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                isSelected\n                  ? `border-${color}-500 bg-${color}-500`\n                  : 'border-gray-300 dark:border-gray-600'\n              }`}>\n                {isSelected && (\n                  <div className=\"w-2 h-2 rounded-full bg-white\"></div>\n                )}\n              </div>\n              <span className=\"font-medium text-sm\">{option.label}</span>\n            </label>\n          );\n        })}\n      </div>\n      \n      {/* Conditional reason field for \"Not Applicable\" */}\n      {selectedValue === 'Not Applicable' && (\n        <div className=\"mt-4\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            {t('reason', 'Reason')} <span className=\"text-red-500\">*</span>\n          </label>\n          <textarea\n            value={formData[`${field}Reason`] || ''}\n            onChange={(e) => handleInputChange(`${field}Reason`, e.target.value)}\n            rows={2}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white ${\n              errors[`${field}Reason`] ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder={t('explainWhyNotApplicable', 'Please explain why this is not applicable')}\n          />\n          {errors[`${field}Reason`] && (\n            <p className=\"text-red-500 text-sm mt-1\">{errors[`${field}Reason`]}</p>\n          )}\n        </div>\n      )}\n      \n      {errors[errorField] && (\n        <p className=\"text-red-500 text-sm mt-2\">{errors[errorField]}</p>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-hands-helping text-blue-600 dark:text-blue-400 mr-2\"></i>\n        {t('patientSelfCareAssessment', 'Patient Self-Care Assessment')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Patient Self-Care Capability */}\n        {renderRadioGroup(\n          'patientSelfCareCapability',\n          selfCareCapabilityOptions,\n          formData.patientSelfCareCapability,\n          'patientSelfCareCapability',\n          t('patientSelfCareCapability', 'Patient Self-Care Capability'),\n          t('selfCareCapabilityDescription', 'Assess the patient\\'s ability to perform self-care activities independently')\n        )}\n\n        {/* Patient Self-Care Motivation */}\n        {renderRadioGroup(\n          'patientSelfCareMotivation',\n          selfCareMotivationOptions,\n          formData.patientSelfCareMotivation,\n          'patientSelfCareMotivation',\n          t('patientSelfCareMotivation', 'Patient Self-Care Motivation'),\n          t('selfCareMotivationDescription', 'Evaluate the patient\\'s motivation level for engaging in self-care activities')\n        )}\n\n        {/* Comments Section */}\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n          <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n            <i className=\"fas fa-comment-alt text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('additionalComments', 'Additional Comments')}\n          </h3>\n          <textarea\n            value={formData.comments}\n            onChange={(e) => handleInputChange('comments', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n            placeholder={t('commentsPlaceholder', 'Add any additional observations, notes, or comments about the patient\\'s self-care assessment')}\n          />\n          <p className=\"text-xs text-blue-700 dark:text-blue-300 mt-2\">\n            {t('commentsHint', 'Include any relevant observations about the patient\\'s self-care abilities, barriers, or special considerations')}\n          </p>\n        </div>\n\n        {/* Assessment Summary */}\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <h4 className=\"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2\">\n            <i className=\"fas fa-lightbulb text-yellow-600 dark:text-yellow-400 mr-1\"></i>\n            {t('assessmentGuidelines', 'Assessment Guidelines')}:\n          </h4>\n          <ul className=\"text-xs text-yellow-700 dark:text-yellow-300 space-y-1\">\n            <li>• <strong>{t('dependent', 'Dependent')}:</strong> {t('dependentDescription', 'Requires full assistance for self-care activities')}</li>\n            <li>• <strong>{t('needAssistant', 'Need Assistant')}:</strong> {t('needAssistantDescription', 'Requires partial assistance or supervision')}</li>\n            <li>• <strong>{t('independent', 'Independent')}:</strong> {t('independentDescription', 'Can perform self-care activities without assistance')}</li>\n            <li>• <strong>{t('notApplicable', 'Not Applicable')}:</strong> {t('notApplicableDescription', 'Self-care assessment is not relevant (provide reason)')}</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SelfCareSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst EducationalNeedsSection = ({ formData, handleInputChange }) => {\n  const { t } = useLanguage();\n\n  const educationalNeedsOptions = [\n    { key: 'diseaseProcess', label: t('diseaseProcess', 'Disease Process') },\n    { key: 'safeEffectiveUseMedication', label: t('safeEffectiveUseMedication', 'Safe and Effective Use of Medication Side Effects and Interactions') },\n    { key: 'safeEffectiveUseMedicalEquipment', label: t('safeEffectiveUseMedicalEquipment', 'Safe and Effective Use of Medical Equipment') },\n    { key: 'painManagement', label: t('painManagement', 'Pain Management') },\n    { key: 'rehabilitationTechnique', label: t('rehabilitationTechnique', 'Rehabilitation Technique') },\n    { key: 'communityResources', label: t('communityResources', 'Community Resources') },\n    { key: 'dischargeInstruction', label: t('dischargeInstruction', 'Discharge Instruction of Continuing Care') },\n    { key: 'patientsRightsResponsibilities', label: t('patientsRightsResponsibilities', 'Patients\\' Rights and Responsibilities') },\n    { key: 'whenHowObtainFurtherTreatment', label: t('whenHowObtainFurtherTreatment', 'When and How to Obtain Further Treatment') },\n    { key: 'personalHygienePractitionerHandWash', label: t('personalHygienePractitionerHandWash', 'Personal Hygiene and Practitioner Hand Wash') },\n    { key: 'infectionControlRelatedIssues', label: t('infectionControlRelatedIssues', 'Infection Control and Related Issues') },\n    { key: 'carePlanTreatment', label: t('carePlanTreatment', 'Care Plan and Treatment') },\n    { key: 'informedConsentProcedure', label: t('informedConsentProcedure', 'Informed Consent Procedure') },\n    { key: 'homeInstruction', label: t('homeInstruction', 'Home Instruction') },\n    { key: 'diagnosticTestProcedure', label: t('diagnosticTestProcedure', 'Diagnostic Test/Procedure') },\n    { key: 'socialServices', label: t('socialServices', 'Social Services') },\n    { key: 'healthMaintenance', label: t('healthMaintenance', 'Health Maintenance') },\n    { key: 'nutrition', label: t('nutrition', 'Nutrition') },\n    { key: 'risk', label: t('risk', 'Risk') },\n    { key: 'safety', label: t('safety', 'Safety') },\n    { key: 'others', label: t('others', 'Others (Specify in comment)') }\n  ];\n\n  const teachingMethodOptions = [\n    { key: 'oneToOne', label: t('oneToOne', 'One to One') },\n    { key: 'groupTeaching', label: t('groupTeaching', 'Group Teaching') },\n    { key: 'lecture', label: t('lecture', 'Lecture') }\n  ];\n\n  const teachingToolOptions = [\n    { key: 'writtenMaterials', label: t('writtenMaterials', 'Written Materials') },\n    { key: 'audio', label: t('audio', 'Audio') },\n    { key: 'verbalInstructions', label: t('verbalInstructions', 'Verbal Instructions') },\n    { key: 'writtenInstruction', label: t('writtenInstruction', 'Written Instruction') },\n    { key: 'video', label: t('video', 'Video') },\n    { key: 'demonstration', label: t('demonstration', 'Demonstration') }\n  ];\n\n  const evaluationOptions = [\n    { key: 'notReceptiveToLearning', label: t('notReceptiveToLearning', 'Not Receptive to Learning/No Learning') },\n    { key: 'unableToVerbalizeBasicConcepts', label: t('unableToVerbalizeBasicConcepts', 'Unable to Verbalize Basic Concepts') },\n    { key: 'verbalizeBasicConceptsWithAssistance', label: t('verbalizeBasicConceptsWithAssistance', 'Verbalize Basic Concepts with Assistance') },\n    { key: 'verbalizeBasicConcept', label: t('verbalizeBasicConcept', 'Verbalize Basic Concept') },\n    { key: 'returnDemonstration', label: t('returnDemonstration', 'Return Demonstration') },\n    { key: 'appliesKnowledgeSkills', label: t('appliesKnowledgeSkills', 'Applies Knowledge/skills') },\n    { key: 'notApplicable', label: t('notApplicable', 'Not Applicable') },\n    { key: 'needFollowUp', label: t('needFollowUp', 'Need Follow-up') }\n  ];\n\n  const renderCheckboxGroup = (title, options, section, icon, color) => (\n    <div className={`bg-${color}-50 dark:bg-${color}-900/20 border border-${color}-200 dark:border-${color}-800 rounded-lg p-6`}>\n      <h3 className={`text-md font-semibold text-${color}-900 dark:text-${color}-100 mb-4`}>\n        <i className={`${icon} text-${color}-600 dark:text-${color}-400 mr-2`}></i>\n        {title}\n      </h3>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n        {options.map((option) => (\n          <label key={option.key} className=\"flex items-start\">\n            <input\n              type=\"checkbox\"\n              checked={formData[section][option.key]}\n              onChange={(e) => handleInputChange(`${section}.${option.key}`, e.target.checked)}\n              className={`mt-1 mr-3 text-${color}-600 focus:ring-${color}-500`}\n            />\n            <span className={`text-sm text-${color}-800 dark:text-${color}-200 leading-tight`}>\n              {option.label}\n            </span>\n          </label>\n        ))}\n      </div>\n      \n      {/* Others description field for educational needs */}\n      {section === 'educationalNeeds' && formData[section].others && (\n        <div className=\"mt-4\">\n          <label className={`block text-sm font-medium text-${color}-800 dark:text-${color}-200 mb-1`}>\n            {t('othersDescription', 'Others Description')}:\n          </label>\n          <textarea\n            value={formData[section].othersDescription}\n            onChange={(e) => handleInputChange(`${section}.othersDescription`, e.target.value)}\n            rows={2}\n            className={`w-full px-3 py-2 border border-${color}-300 rounded-lg focus:ring-2 focus:ring-${color}-500 focus:border-${color}-500 dark:bg-${color}-800 dark:border-${color}-600 dark:text-white`}\n            placeholder={t('specifyOtherEducationalNeeds', 'Specify other educational needs')}\n          />\n        </div>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-book-open text-indigo-600 dark:text-indigo-400 mr-2\"></i>\n        {t('educationalNeedsGivenEducation', 'Educational Needs & Given Education')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Educational Needs */}\n        {renderCheckboxGroup(\n          t('educationalNeeds', 'Educational Needs'),\n          educationalNeedsOptions,\n          'educationalNeeds',\n          'fas fa-list-check',\n          'purple'\n        )}\n\n        {/* Given Education */}\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n          <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-6\">\n            <i className=\"fas fa-chalkboard-teacher text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('givenEducation', 'Given Education')}\n          </h3>\n          \n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            {/* Teaching Method */}\n            <div>\n              <h4 className=\"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3\">\n                {t('teachingMethod', 'Teaching Method')}\n              </h4>\n              <div className=\"space-y-2\">\n                {teachingMethodOptions.map((option) => (\n                  <label key={option.key} className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.teachingMethod[option.key]}\n                      onChange={(e) => handleInputChange(`teachingMethod.${option.key}`, e.target.checked)}\n                      className=\"mr-3 text-blue-600 focus:ring-blue-500\"\n                    />\n                    <span className=\"text-sm text-blue-800 dark:text-blue-200\">\n                      {option.label}\n                    </span>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {/* Teaching Tool */}\n            <div>\n              <h4 className=\"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3\">\n                {t('teachingTool', 'Teaching Tool')}\n              </h4>\n              <div className=\"space-y-2\">\n                {teachingToolOptions.map((option) => (\n                  <label key={option.key} className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={formData.teachingTool[option.key]}\n                      onChange={(e) => handleInputChange(`teachingTool.${option.key}`, e.target.checked)}\n                      className=\"mr-3 text-blue-600 focus:ring-blue-500\"\n                    />\n                    <span className=\"text-sm text-blue-800 dark:text-blue-200\">\n                      {option.label}\n                    </span>\n                  </label>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Evaluation */}\n        {renderCheckboxGroup(\n          t('evaluation', 'Evaluation'),\n          evaluationOptions,\n          'evaluation',\n          'fas fa-clipboard-check',\n          'green'\n        )}\n\n        {/* Education Summary */}\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <h4 className=\"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2\">\n            <i className=\"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mr-1\"></i>\n            {t('educationGuidelines', 'Education Guidelines')}:\n          </h4>\n          <ul className=\"text-xs text-yellow-700 dark:text-yellow-300 space-y-1\">\n            <li>• {t('selectRelevantNeeds', 'Select all educational needs relevant to the patient and family')}</li>\n            <li>• {t('chooseAppropriateMethod', 'Choose appropriate teaching methods based on learning preferences')}</li>\n            <li>• {t('useMultipleTools', 'Use multiple teaching tools for better comprehension')}</li>\n            <li>• {t('evaluateEffectiveness', 'Evaluate the effectiveness of education provided')}</li>\n            <li>• {t('documentFollowUp', 'Document any follow-up education needs')}</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default EducationalNeedsSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst AdditionalQuestionsSection = ({ formData, handleInputChange, errors }) => {\n  const { t } = useLanguage();\n\n  const patientAttendOptions = [\n    { value: 'School', label: t('school', 'School') },\n    { value: 'Work', label: t('work', 'Work') },\n    { value: 'NOT APPLICABLE', label: t('notApplicable', 'NOT APPLICABLE') }\n  ];\n\n  const problemsAttendingOptions = [\n    { value: 'Yes', label: t('yes', 'Yes') },\n    { value: 'No', label: t('no', 'No') },\n    { value: 'NOT APPLICABLE', label: t('notApplicable', 'NOT APPLICABLE') }\n  ];\n\n  const literacySkillsOptions = [\n    { value: 'Yes', label: t('yes', 'Yes') },\n    { value: 'No', label: t('no', 'No') }\n  ];\n\n  const postDischargeCareOptions = [\n    { key: 'caregiver', label: t('caregiver', 'Caregiver') },\n    { key: 'family', label: t('family', 'Family') },\n    { key: 'reAssessment', label: t('reAssessment', 'Re-Assessment') },\n    { key: 'dischargeAssessment', label: t('dischargeAssessment', 'Discharge Assessment') },\n    { key: 'others', label: t('others', 'Others') }\n  ];\n\n  const renderRadioGroup = (field, options, selectedValue, errorField, title, description) => (\n    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n      <h4 className=\"text-sm font-semibold text-gray-900 dark:text-white mb-2\">\n        {title} <span className=\"text-red-500\">*</span>\n      </h4>\n      {description && (\n        <p className=\"text-xs text-gray-600 dark:text-gray-400 mb-3\">{description}</p>\n      )}\n      \n      <div className=\"flex flex-wrap gap-3\">\n        {options.map((option) => (\n          <label\n            key={option.value}\n            className={`flex items-center px-3 py-2 border rounded-lg cursor-pointer transition-colors ${\n              selectedValue === option.value\n                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200'\n                : 'border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500'\n            }`}\n          >\n            <input\n              type=\"radio\"\n              name={field}\n              value={option.value}\n              checked={selectedValue === option.value}\n              onChange={(e) => handleInputChange(field, e.target.value)}\n              className=\"sr-only\"\n            />\n            <div className={`w-3 h-3 rounded-full border-2 mr-2 flex items-center justify-center ${\n              selectedValue === option.value\n                ? 'border-blue-500 bg-blue-500'\n                : 'border-gray-300 dark:border-gray-600'\n            }`}>\n              {selectedValue === option.value && (\n                <div className=\"w-1.5 h-1.5 rounded-full bg-white\"></div>\n              )}\n            </div>\n            <span className=\"text-sm font-medium\">{option.label}</span>\n          </label>\n        ))}\n      </div>\n      \n      {errors[errorField] && (\n        <p className=\"text-red-500 text-sm mt-2\">{errors[errorField]}</p>\n      )}\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Additional Questions */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n          <i className=\"fas fa-question-circle text-orange-600 dark:text-orange-400 mr-2\"></i>\n          {t('additionalQuestions', 'Additional Questions')}\n        </h2>\n        \n        <div className=\"space-y-4\">\n          {/* Patient Attend */}\n          {renderRadioGroup(\n            'patientAttend',\n            patientAttendOptions,\n            formData.patientAttend,\n            'patientAttend',\n            t('patientAttend', 'Patient Attend'),\n            t('patientAttendDescription', 'Does the patient currently attend school or work?')\n          )}\n\n          {/* Problems Attending */}\n          {renderRadioGroup(\n            'problemsAttending',\n            problemsAttendingOptions,\n            formData.problemsAttending,\n            'problemsAttending',\n            t('problemsAttendingSchoolWork', 'Does the Patient Have Any Problems Attending School / Work?'),\n            t('problemsAttendingDescription', 'Are there any barriers preventing regular attendance?')\n          )}\n\n          {/* Literacy Skills */}\n          {renderRadioGroup(\n            'literacySkills',\n            literacySkillsOptions,\n            formData.literacySkills,\n            'literacySkills',\n            t('literacySkills', 'Literacy Skills'),\n            t('literacySkillsDescription', 'Can the patient read and write at an appropriate level?')\n          )}\n        </div>\n      </div>\n\n      {/* Post-Discharge Care */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n          <i className=\"fas fa-home text-green-600 dark:text-green-400 mr-2\"></i>\n          {t('postDischargeCare', 'Post-Discharge Care')}\n        </h2>\n        \n        <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n          <h3 className=\"text-md font-semibold text-green-900 dark:text-green-100 mb-4\">\n            {t('whoWillProvideCareAfterDischarge', 'Who Will Provide Care After Discharge?')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n            {postDischargeCareOptions.map((option) => (\n              <label key={option.key} className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.postDischargeCareProvider[option.key]}\n                  onChange={(e) => handleInputChange(`postDischargeCareProvider.${option.key}`, e.target.checked)}\n                  className=\"mr-3 text-green-600 focus:ring-green-500\"\n                />\n                <span className=\"text-sm text-green-800 dark:text-green-200\">\n                  {option.label}\n                </span>\n              </label>\n            ))}\n          </div>\n          \n          {/* Others description field */}\n          {formData.postDischargeCareProvider.others && (\n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-1\">\n                {t('othersDescription', 'Others Description')}:\n              </label>\n              <textarea\n                value={formData.postDischargeCareProvider.othersDescription}\n                onChange={(e) => handleInputChange('postDischargeCareProvider.othersDescription', e.target.value)}\n                rows={2}\n                className=\"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white\"\n                placeholder={t('specifyOtherCareProvider', 'Specify other care provider')}\n              />\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Plans and Comments */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n          <i className=\"fas fa-clipboard-list text-purple-600 dark:text-purple-400 mr-2\"></i>\n          {t('plansAndComments', 'Plans and Comments')}\n        </h2>\n        \n        <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4\">\n          <textarea\n            value={formData.plansAndComments}\n            onChange={(e) => handleInputChange('plansAndComments', e.target.value)}\n            rows={6}\n            className=\"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white\"\n            placeholder={t('plansCommentsPlaceholder', 'Document future education plans, follow-up requirements, additional comments, or special considerations for the patient and family education')}\n          />\n          <p className=\"text-xs text-purple-700 dark:text-purple-300 mt-2\">\n            {t('plansCommentsHint', 'Include any specific education plans, follow-up schedules, or special considerations for ongoing patient and family education')}\n          </p>\n        </div>\n      </div>\n\n      {/* Therapist Signature */}\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-6\">\n          <i className=\"fas fa-signature text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('therapistSignature', 'Therapist Signature')}\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              {t('therapistName', 'Therapist Name')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.therapistName}\n              onChange={(e) => handleInputChange('therapistName', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ${\n                errors.therapistName ? 'border-red-500' : 'border-blue-300'\n              }`}\n              placeholder={t('enterTherapistName', 'Enter therapist full name')}\n            />\n            {errors.therapistName && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.therapistName}</p>\n            )}\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              {t('signature', 'Signature')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.therapistSignature}\n              onChange={(e) => handleInputChange('therapistSignature', e.target.value)}\n              className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n              placeholder={t('digitalSignature', 'Digital signature')}\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              {t('badgeNumber', 'Badge No.')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.therapistBadgeNo}\n              onChange={(e) => handleInputChange('therapistBadgeNo', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ${\n                errors.therapistBadgeNo ? 'border-red-500' : 'border-blue-300'\n              }`}\n              placeholder={t('enterBadgeNumber', 'Enter badge number')}\n            />\n            {errors.therapistBadgeNo && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.therapistBadgeNo}</p>\n            )}\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              {t('date', 'Date')}\n            </label>\n            <input\n              type=\"date\"\n              value={formData.therapistDate}\n              onChange={(e) => handleInputChange('therapistDate', e.target.value)}\n              className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n            />\n          </div>\n        </div>\n        \n        {/* Digital Signature Placeholder */}\n        <div className=\"mt-6\">\n          <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2\">\n            {t('digitalSignature', 'Digital Signature')}\n          </label>\n          <div className=\"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center\">\n            <i className=\"fas fa-signature text-3xl text-blue-400 mb-2\"></i>\n            <p className=\"text-sm text-blue-600 dark:text-blue-400\">\n              {t('digitalSignaturePlaceholder', 'Digital signature will be captured here')}\n            </p>\n            <button\n              type=\"button\"\n              className=\"mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-pen mr-2\"></i>\n              {t('addSignature', 'Add Signature')}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdditionalQuestionsSection;\n", "import React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport DocumentAssessmentSection from './DocumentAssessmentSection';\nimport LearningBarriersSection from './LearningBarriersSection';\nimport SelfCareSection from './SelfCareSection';\nimport EducationalNeedsSection from './EducationalNeedsSection';\nimport AdditionalQuestionsSection from './AdditionalQuestionsSection';\n\nconst EducationForm = ({ patientId, onSave, onCancel }) => {\n  const { t } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const { patientId: urlPatientId, educationId } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [patient, setPatient] = useState(null);\n\n  // Use patientId from props or URL params\n  const activePatientId = patientId || urlPatientId;\n\n  // Form data structure\n  const [formData, setFormData] = useState({\n    // Document Information\n    documentNumber: 'QP-',\n    issueDate: new Date().toISOString().split('T')[0],\n    version: '01',\n    reviewNumber: '01',\n    \n    // Assessment Type\n    assessmentType: '', // Initial Assessment, Re-Assessment, Discharge Assessment\n    \n    // Patient Learning Preferences\n    patientLearningPreference: {\n      video: false,\n      writtenMaterialsWithPicture: false,\n      demonstration: false,\n      other: false,\n      otherDescription: ''\n    },\n    \n    // Patient Barriers\n    patientBarriers: {\n      physical: false,\n      cognitive: false,\n      emotional: false,\n      culturalBelieve: false,\n      poorMotivation: false,\n      financialDifficulties: false,\n      readingAbility: false,\n      psychological: false,\n      language: false,\n      others: false,\n      othersDescription: '',\n      noBarrier: false,\n      impairedHearing: false,\n      speechBarriers: false,\n      responsibilitiesAtHome: false,\n      religiousPractice: false,\n      impairedVision: false,\n      age: false\n    },\n    \n    // Caregiver Learning Preferences\n    caregiverLearningPreference: {\n      video: false,\n      writtenMaterialsWithPicture: false,\n      demonstration: false,\n      other: false,\n      otherDescription: ''\n    },\n    \n    // Caregiver Barriers\n    caregiverBarriers: {\n      physical: false,\n      cognitive: false,\n      emotional: false,\n      culturalBelieve: false,\n      poorMotivation: false,\n      financialDifficulties: false,\n      readingAbility: false,\n      psychological: false,\n      language: false,\n      others: false,\n      othersDescription: '',\n      noBarrier: false,\n      impairedHearing: false,\n      speechBarriers: false,\n      responsibilitiesAtHome: false,\n      religiousPractice: false,\n      impairedVision: false,\n      age: false\n    },\n    \n    // Patient Self-Care\n    patientSelfCareCapability: '', // Dependent, Independent, Need Assistant, Not Applicable\n    patientSelfCareCapabilityReason: '',\n    patientSelfCareMotivation: '', // Poor, Moderate, High, Not Applicable\n    patientSelfCareMotivationReason: '',\n    \n    // Comments\n    comments: '',\n    \n    // Educational Needs\n    educationalNeeds: {\n      diseaseProcess: false,\n      safeEffectiveUseMedication: false,\n      safeEffectiveUseMedicalEquipment: false,\n      painManagement: false,\n      rehabilitationTechnique: false,\n      communityResources: false,\n      dischargeInstruction: false,\n      patientsRightsResponsibilities: false,\n      whenHowObtainFurtherTreatment: false,\n      personalHygienePractitionerHandWash: false,\n      infectionControlRelatedIssues: false,\n      carePlanTreatment: false,\n      informedConsentProcedure: false,\n      homeInstruction: false,\n      others: false,\n      othersDescription: '',\n      diagnosticTestProcedure: false,\n      socialServices: false,\n      healthMaintenance: false,\n      nutrition: false,\n      risk: false,\n      safety: false\n    },\n    \n    // Given Education\n    teachingMethod: {\n      oneToOne: false,\n      groupTeaching: false,\n      lecture: false\n    },\n    teachingTool: {\n      writtenMaterials: false,\n      audio: false,\n      verbalInstructions: false,\n      writtenInstruction: false,\n      video: false,\n      demonstration: false\n    },\n    \n    // Evaluation\n    evaluation: {\n      notReceptiveToLearning: false,\n      unableToVerbalizeBasicConcepts: false,\n      verbalizeBasicConceptsWithAssistance: false,\n      verbalizeBasicConcept: false,\n      returnDemonstration: false,\n      appliesKnowledgeSkills: false,\n      notApplicable: false,\n      needFollowUp: false\n    },\n    \n    // Additional Questions\n    patientAttend: '', // School, Work, NOT APPLICABLE\n    problemsAttending: '', // Yes, No, NOT APPLICABLE\n    literacySkills: '', // Yes, No\n    \n    // Post-Discharge Care\n    postDischargeCareProvider: {\n      caregiver: false,\n      family: false,\n      reAssessment: false,\n      dischargeAssessment: false,\n      others: false,\n      othersDescription: ''\n    },\n    \n    // Plans and Comments\n    plansAndComments: '',\n    \n    // Therapist Signature\n    therapistName: user?.name || '',\n    therapistSignature: '',\n    therapistBadgeNo: user?.badgeNo || '',\n    therapistDate: new Date().toISOString().split('T')[0]\n  });\n\n  // Load patient data if patientId is provided\n  useEffect(() => {\n    if (activePatientId) {\n      setLoading(true);\n      // Mock patient data loading - in real implementation, fetch from API\n      setTimeout(() => {\n        const mockPatient = {\n          id: activePatientId,\n          name: 'أحمد محمد علي',\n          nameEn: 'Ahmed Mohammed Ali',\n          mrNumber: 'MR-2024-001',\n          dateOfBirth: '2016-03-15',\n          age: 8,\n          gender: 'male',\n          diagnosis: 'Cerebral Palsy'\n        };\n        \n        setPatient(mockPatient);\n        setLoading(false);\n      }, 500);\n    }\n  }, [activePatientId]);\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData };\n    \n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    setFormData(newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  // Handle special logic for \"No Barrier\" checkboxes\n  const handleBarrierChange = (section, field, value) => {\n    const newData = { ...formData };\n    \n    if (field === 'noBarrier' && value) {\n      // If \"No Barrier\" is checked, uncheck all other barriers\n      Object.keys(newData[section]).forEach(key => {\n        if (key !== 'noBarrier' && key !== 'othersDescription') {\n          newData[section][key] = false;\n        }\n      });\n      newData[section].othersDescription = '';\n    } else if (field !== 'noBarrier' && value) {\n      // If any other barrier is checked, uncheck \"No Barrier\"\n      newData[section].noBarrier = false;\n    }\n    \n    newData[section][field] = value;\n    setFormData(newData);\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.assessmentType) {\n      newErrors.assessmentType = t('assessmentTypeRequired', 'Assessment type is required');\n    }\n    if (!formData.patientSelfCareCapability) {\n      newErrors.patientSelfCareCapability = t('selfCareCapabilityRequired', 'Patient self-care capability is required');\n    }\n    if (!formData.patientSelfCareMotivation) {\n      newErrors.patientSelfCareMotivation = t('selfCareMotivationRequired', 'Patient self-care motivation is required');\n    }\n    if (!formData.patientAttend) {\n      newErrors.patientAttend = t('patientAttendRequired', 'Patient attend field is required');\n    }\n    if (!formData.problemsAttending) {\n      newErrors.problemsAttending = t('problemsAttendingRequired', 'Problems attending field is required');\n    }\n    if (!formData.literacySkills) {\n      newErrors.literacySkills = t('literacySkillsRequired', 'Literacy skills field is required');\n    }\n    if (!formData.therapistName.trim()) {\n      newErrors.therapistName = t('therapistNameRequired', 'Therapist name is required');\n    }\n    if (!formData.therapistBadgeNo.trim()) {\n      newErrors.therapistBadgeNo = t('therapistBadgeRequired', 'Therapist badge number is required');\n    }\n\n    // Conditional validation for \"Not Applicable\" reasons\n    if (formData.patientSelfCareCapability === 'Not Applicable' && !formData.patientSelfCareCapabilityReason.trim()) {\n      newErrors.patientSelfCareCapabilityReason = t('reasonRequired', 'Reason is required when selecting \"Not Applicable\"');\n    }\n    if (formData.patientSelfCareMotivation === 'Not Applicable' && !formData.patientSelfCareMotivationReason.trim()) {\n      newErrors.patientSelfCareMotivationReason = t('reasonRequired', 'Reason is required when selecting \"Not Applicable\"');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    \n    try {\n      // Mock API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      if (onSave) {\n        onSave(formData);\n      } else {\n        // Save to localStorage for demo\n        const savedEducationForms = JSON.parse(localStorage.getItem('educationFormData') || '[]');\n        const newForm = {\n          ...formData,\n          id: Date.now(),\n          patientId: activePatientId,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n        savedEducationForms.push(newForm);\n        localStorage.setItem('educationFormData', JSON.stringify(savedEducationForms));\n        \n        toast.success(t('educationFormSaved', 'Patient and Family Education Form saved successfully'));\n        \n        if (activePatientId) {\n          navigate(`/patients/${activePatientId}`);\n        } else {\n          navigate('/patients');\n        }\n      }\n    } catch (error) {\n      console.error('Error saving education form:', error);\n      toast.error(t('errorSavingForm', 'Error saving education form'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleExportPDF = () => {\n    // Mock PDF export - implement with jsPDF or similar\n    toast.success(t('pdfExported', 'PDF exported successfully'));\n  };\n\n  if (loading && !patient) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900\">\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Header */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n          <div className=\"flex items-start justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('patientFamilyEducationForm', 'Patient and Family Education Form for Physical Therapist')}\n                {activePatientId && patient && (\n                  <span className=\"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3\">\n                    - {patient.nameEn || patient.name}\n                  </span>\n                )}\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                {t('educationFormDescription', 'Comprehensive 1-page education assessment and planning form')}\n              </p>\n              \n              {/* Compliance Badges */}\n              <div className=\"flex flex-wrap gap-2 mt-3\">\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n                  <i className=\"fas fa-certificate text-blue-600 dark:text-blue-400\"></i>\n                  <span className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">CARF Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full\">\n                  <i className=\"fas fa-shield-alt text-green-600 dark:text-green-400\"></i>\n                  <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">CBAHI Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n                  <i className=\"fas fa-lock text-purple-600 dark:text-purple-400\"></i>\n                  <span className=\"text-sm font-medium text-purple-800 dark:text-purple-200\">HIPAA Secure</span>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"flex space-x-3\">\n              {activePatientId && (\n                <button\n                  type=\"button\"\n                  onClick={() => navigate(`/patients/${activePatientId}`)}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <i className=\"fas fa-user mr-2\"></i>\n                  {t('viewPatient', 'View Patient')}\n                </button>\n              )}\n              <button\n                type=\"button\"\n                onClick={handleExportPDF}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {t('exportPDF', 'Export PDF')}\n              </button>\n              <button\n                type=\"button\"\n                onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Document Information and Assessment Type */}\n        <DocumentAssessmentSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n        />\n\n        {/* Learning Preferences and Barriers */}\n        <LearningBarriersSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          handleBarrierChange={handleBarrierChange}\n        />\n\n        {/* Self-Care Assessment */}\n        <SelfCareSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n        />\n\n        {/* Educational Needs */}\n        <EducationalNeedsSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n        />\n\n        {/* Additional Questions and Signature */}\n        <AdditionalQuestionsSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n        />\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <button\n            type=\"button\"\n            onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                {t('saving', 'Saving...')}\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveEducationForm', 'Save Education Form')}\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default EducationForm;\n"], "names": ["_ref", "formData", "handleInputChange", "errors", "t", "useLanguage", "assessmentTypeOptions", "value", "label", "_jsxs", "className", "children", "_jsx", "type", "documentNumber", "onChange", "e", "target", "placeholder", "issueDate", "version", "reviewNumber", "map", "option", "concat", "assessmentType", "name", "checked", "handleBarrierChange", "learningPreferenceOptions", "key", "barrierOptions", "renderLearningPreferences", "section", "title", "icon", "color", "other", "otherDescription", "rows", "renderBarriers", "<PERSON><PERSON><PERSON><PERSON>", "filter", "disabled", "others", "othersDescription", "selfCareCapabilityOptions", "selfCareMotivationOptions", "renderRadioGroup", "field", "options", "selected<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ield", "description", "getOptionColor", "isSelected", "patientSelfCareCapability", "patientSelfCareMotivation", "comments", "educationalNeedsOptions", "teachingMethodOptions", "teachingToolOptions", "evaluationOptions", "renderCheckboxGroup", "teachingMethod", "teachingTool", "patientAttendOptions", "problemsAttendingOptions", "literacySkillsOptions", "postDischargeCareOptions", "patientAttend", "problemsAttending", "literacySkills", "postDischargeCareProvider", "plansAndComments", "<PERSON><PERSON><PERSON>", "therapistSignature", "therapistBadgeNo", "therapistDate", "patientId", "onSave", "onCancel", "user", "useAuth", "navigate", "useNavigate", "urlPatientId", "educationId", "useParams", "loading", "setLoading", "useState", "setErrors", "patient", "setPatient", "activePatientId", "setFormData", "Date", "toISOString", "split", "patientLearningPreference", "video", "writtenMaterialsWithPicture", "demonstration", "patientBarriers", "physical", "cognitive", "emotional", "cultural<PERSON><PERSON>eve", "poorMotivation", "financialDifficulties", "readingAbility", "psychological", "language", "impairedHearing", "speechBarriers", "responsibilitiesAtHome", "religiousPractice", "impairedVision", "age", "caregiverLearningPreference", "caregiver<PERSON><PERSON><PERSON>s", "patientSelfCareCapabilityReason", "patientSelfCareMotivationReason", "educationalNeeds", "diseaseProcess", "safeEffectiveUseMedication", "safeEffectiveUseMedicalEquipment", "painManagement", "rehabilitationTechnique", "communityResources", "dischargeInstruction", "patientsRightsResponsibilities", "whenHowObtainFurtherTreatment", "personalHygienePractitionerHandWash", "infectionControlRelatedIssues", "carePlanTreatment", "informedConsentProcedure", "homeInstruction", "diagnosticTestProcedure", "socialServices", "healthMaintenance", "nutrition", "risk", "safety", "oneToOne", "groupTeaching", "lecture", "writtenMaterials", "audio", "verbalInstructions", "writtenInstruction", "evaluation", "notReceptiveToLearning", "unableToVerbalizeBasicConcepts", "verbalizeBasicConceptsWithAssistance", "verbalizeBasicConcept", "returnDemonstration", "appliesKnowledgeSkills", "notApplicable", "needFollowUp", "caregiver", "family", "reAssessment", "dischargeAssessment", "badgeNo", "useEffect", "setTimeout", "id", "nameEn", "mr<PERSON><PERSON><PERSON>", "dateOfBirth", "gender", "diagnosis", "newData", "_objectSpread", "includes", "parts", "current", "i", "length", "prev", "onSubmit", "async", "preventDefault", "validateForm", "newErrors", "trim", "Object", "keys", "Promise", "resolve", "savedEducationForms", "JSON", "parse", "localStorage", "getItem", "newForm", "now", "createdAt", "updatedAt", "push", "setItem", "stringify", "toast", "success", "error", "console", "onClick", "handleExportPDF", "DocumentAssessmentSection", "LearningBarriersSection", "for<PERSON>ach", "SelfCareSection", "EducationalNeedsSection", "AdditionalQuestionsSection", "_Fragment"], "sourceRoot": ""}