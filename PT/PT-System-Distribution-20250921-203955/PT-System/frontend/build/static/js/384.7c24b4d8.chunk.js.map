{"version": 3, "file": "static/js/384.7c24b4d8.chunk.js", "mappings": "iMAIA,MAosBA,EApsBcA,KACZ,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,aACpCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAAgBC,IAAqBJ,EAAAA,EAAAA,UAAS,QAG9CK,IAAiBL,EAAAA,EAAAA,UAAS,CAC/B,CACEM,GAAI,EACJC,KAAM,mCACNC,OAAQ,kPACRC,SAAU,aACVC,YAAa,8FACbC,cAAe,qYACfC,OAAQ,IACRC,aAAc,aACdC,OAAQ,SACRC,WAAY,IACZC,WAAY,CAAC,OAAQ,QAAS,SAC9BC,SAAU,CAAC,WAAY,kBAAmB,cAAe,iBAAkB,iBAC3EC,MAAO,mBAET,CACEZ,GAAI,EACJC,KAAM,0BACNC,OAAQ,iHACRC,SAAU,aACVC,YAAa,oDACbC,cAAe,uJACfC,OAAQ,GACRC,aAAc,aACdC,OAAQ,SACRC,WAAY,IAEd,CACET,GAAI,EACJC,KAAM,2BACNC,OAAQ,qGACRC,SAAU,aACVC,YAAa,qDACbC,cAAe,uMACfC,OAAQ,EACRC,aAAc,aACdC,OAAQ,SACRC,WAAY,IAEd,CACET,GAAI,EACJC,KAAM,6BACNC,OAAQ,+FACRC,SAAU,gBACVC,YAAa,oEACbC,cAAe,iRACfC,OAAQ,GACRC,aAAc,aACdC,OAAQ,SACRC,WAAY,IAEd,CACET,GAAI,EACJC,KAAM,2BACNC,OAAQ,iHACRC,SAAU,eACVC,YAAa,gDACbC,cAAe,4JACfC,OAAQ,EACRC,aAAc,aACdC,OAAQ,SACRC,WAAY,IAEd,CACET,GAAI,EACJC,KAAM,yBACNC,OAAQ,+FACRC,SAAU,YACVC,YAAa,gDACbC,cAAe,gJACfC,OAAQ,GACRC,aAAc,aACdC,OAAQ,QACRC,WAAY,MAKTI,IAAkBnB,EAAAA,EAAAA,UAAS,CAChC,CACEM,GAAI,EACJc,SAAU,0BACVC,YAAa,yFACbC,cAAe,0BACfC,YAAa,sBACbC,cAAe,aACfV,OAAQ,aAEV,CACER,GAAI,EACJc,SAAU,2BACVC,YAAa,yFACbC,cAAe,sBACfC,YAAa,wBACbC,cAAe,aACfV,OAAQ,kBAEV,CACER,GAAI,EACJc,SAAU,6BACVC,YAAa,2GACbC,cAAe,8BACfC,YAAa,sBACbC,cAAe,aACfV,OAAQ,eAINW,EAAiB,CACrBC,WAAY,CAAEC,MAAOhC,EAAE,aAAc,cAAeiC,MAAO,6BAC3DC,WAAY,CAAEF,MAAOhC,EAAE,aAAc,cAAeiC,MAAO,+BAC3DE,cAAe,CAAEH,MAAOhC,EAAE,eAAgB,iBAAkBiC,MAAO,iCACnEG,aAAc,CAAEJ,MAAOhC,EAAE,eAAgB,gBAAiBiC,MAAO,iCACjEI,UAAW,CAAEL,MAAOhC,EAAE,YAAa,aAAciC,MAAO,8BAGpDK,EAAe,CACnBC,OAAQ,CAAEP,MAAOhC,EAAE,SAAU,UAAWiC,MAAO,+BAC/CO,MAAO,CAAER,MAAOhC,EAAE,QAAS,SAAUiC,MAAO,iCAC5CQ,SAAU,CAAET,MAAOhC,EAAE,WAAY,YAAaiC,MAAO,6BACrDS,UAAW,CAAEV,MAAOhC,EAAE,YAAa,aAAciC,MAAO,+BACxDU,eAAgB,CAAEX,MAAOhC,EAAE,gBAAiB,kBAAmBiC,MAAO,kCAGlEW,EAAoBlC,EAAcmC,OAAOC,IAC7C,MAAMC,EAAgBD,EAAKlC,KAAKoC,cAAcC,SAAS3C,EAAW0C,gBAC7CF,EAAKjC,OAAOoC,SAAS3C,GACpC4C,EAAqC,QAAnB1C,GAA4BsC,EAAKhC,WAAaN,EACtE,OAAOuC,GAAiBG,IAGpBC,EAAO,CACX,CAAExC,GAAI,WAAYqB,MAAOhC,EAAE,WAAY,YAAaoD,KAAM,yBAC1D,CAAEzC,GAAI,YAAaqB,MAAOhC,EAAE,gBAAiB,kBAAmBoD,KAAM,mBACtE,CAAEzC,GAAI,YAAaqB,MAAOhC,EAAE,iBAAkB,mBAAoBoD,KAAM,gBACxE,CAAEzC,GAAI,YAAaqB,MAAOhC,EAAE,YAAa,aAAcoD,KAAM,oBAC7D,CAAEzC,GAAI,UAAWqB,MAAOhC,EAAE,cAAe,gBAAiBoD,KAAM,gBAChE,CAAEzC,GAAI,WAAYqB,MAAOhC,EAAE,WAAY,YAAaoD,KAAM,eAG5D,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAC,OAAStD,EAAQ,cAAgB,gBAAiBuD,SAAA,EAE9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DxD,EAAE,QAAS,YAEdyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CxD,EAAE,YAAa,2DAGpByD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iBAAgBE,UAC7BH,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,iBACHL,UAAU,oGAAmGE,SAAA,EAE7GC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZtD,EAAE,gBAAiB,4BAM1BqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,EACzDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAExD,EAAE,iBAAkB,sBACzFyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAAE9C,EAAckD,kBAKrFH,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAExD,EAAE,kBAAmB,uBAC1FyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5D9C,EAAcmC,OAAOgB,GAAkB,WAAbA,EAAE1C,QAAqByC,kBAM1DH,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAExD,EAAE,iBAAkB,sBACzFyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAAEhC,EAAeoC,kBAKtFH,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAExD,EAAE,aAAc,kBACrFyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5D9C,EAAcoD,OAAO,CAACC,EAAKF,IAAME,EAAMF,EAAEzC,WAAY,iBAQhEqC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBE,SACnCL,EAAKa,IAAKC,IACTZ,EAAAA,EAAAA,MAAA,UAEEa,QAASA,IAAM9D,EAAa6D,EAAItD,IAChC2C,UAAS,0FAAAC,OACPpD,IAAc8D,EAAItD,GACd,mDACA,0HACH6C,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAWW,EAAIb,QAClBK,EAAAA,EAAAA,KAAA,QAAAD,SAAOS,EAAIjC,UATNiC,EAAItD,SAgBF,aAAdR,IACCsD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UACxBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8JAA6JE,SAAA,EAC1KH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,gFAA+EE,SAAA,EAC3FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gEACZtD,EAAE,gBAAiB,iCAGtBqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDE,SAAA,EAEnEH,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,mBACHL,UAAU,4IAA2IE,SAAA,EAErJH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2HAA0HE,UACvIC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SAAExD,EAAE,gBAAiB,qBAC3FqD,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CE,SAAA,CAAE9C,EAAckD,OAAO,iCAGlFH,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SACpDxD,EAAE,oBAAqB,yEAK5BqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,mBACHL,UAAU,+IAA8IE,SAAA,EAExJH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+HAA8HE,UAC3IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SAAExD,EAAE,gBAAiB,qBAC7FyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6CAA4CE,SAAC,iCAG9DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6CAA4CE,SACtDxD,EAAE,oBAAqB,qEAK5BqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,iBACHL,UAAU,kJAAiJE,SAAA,EAE3JH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mIAAkIE,UAC/IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,6DAA4DE,SAAExD,EAAE,cAAe,mBAC7FyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+CAA8CE,SAAC,+BAGhEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+CAA8CE,SACxDxD,EAAE,kBAAmB,+EAK1BqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,qBACHL,UAAU,kJAAiJE,SAAA,EAE3JH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mIAAkIE,UAC/IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,6DAA4DE,SAAExD,EAAE,kBAAmB,uBACjGqD,EAAAA,EAAAA,MAAA,KAAGC,UAAU,+CAA8CE,SAAA,CAAEhC,EAAeoC,OAAO,yBAGvFH,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+CAA8CE,SACxDxD,EAAE,sBAAuB,gEAK9BqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,kBACHL,UAAU,kJAAiJE,SAAA,EAE3JH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mIAAkIE,UAC/IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,6DAA4DE,SAAExD,EAAE,eAAgB,oBAC9FyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+CAA8CE,SAAC,4BAGhEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+CAA8CE,SACxDxD,EAAE,mBAAoB,mEAK3BqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFE,SAAA,EACtGH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBE,SAAA,EACrCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SAAExD,EAAE,eAAgB,oBAC1FyD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SAAC,wBAG5DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EACxBH,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,uBACHL,UAAU,gHAA+GE,SAAA,EAEzHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZtD,EAAE,kBAAmB,yBAExBqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,wBACHL,UAAU,gHAA+GE,SAAA,EAEzHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZtD,EAAE,mBAAoB,0BAEzBqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,wBACHL,UAAU,gHAA+GE,SAAA,EAEzHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZtD,EAAE,kBAAmB,sCAUrB,cAAdG,IACCkD,EAAAA,EAAAA,MAAAc,EAAAA,SAAA,CAAAX,SAAA,EAEEC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mGAAkGE,UAC/GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExD,EAAE,SAAU,aAEfqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEW,KAAK,OACLC,MAAO/D,EACPgE,SAAWC,GAAMhE,EAAcgE,EAAEC,OAAOH,OACxCI,YAAazE,EAAE,cAAe,mBAC9BsD,UAAU,2IAEZG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6DAIjBD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/ExD,EAAE,WAAY,eAEjBqD,EAAAA,EAAAA,MAAA,UACEgB,MAAO7D,EACP8D,SAAWC,GAAM9D,EAAkB8D,EAAEC,OAAOH,OAC5Cf,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQY,MAAM,MAAKb,SAAExD,EAAE,gBAAiB,qBACxCyD,EAAAA,EAAAA,KAAA,UAAQY,MAAM,aAAYb,SAAExD,EAAE,aAAc,iBAC5CyD,EAAAA,EAAAA,KAAA,UAAQY,MAAM,aAAYb,SAAExD,EAAE,aAAc,iBAC5CyD,EAAAA,EAAAA,KAAA,UAAQY,MAAM,gBAAeb,SAAExD,EAAE,eAAgB,oBACjDyD,EAAAA,EAAAA,KAAA,UAAQY,MAAM,eAAcb,SAAExD,EAAE,eAAgB,mBAChDyD,EAAAA,EAAAA,KAAA,UAAQY,MAAM,YAAWb,SAAExD,EAAE,YAAa,sBAI9CyD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iBAAgBE,UAC7BH,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uJAAsJE,SAAA,EACtKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBACZtD,EAAE,kBAAmB,+BAO9BqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFE,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2DAA0DE,SACtEZ,EAAkBoB,IAAKlB,IACtBO,EAAAA,EAAAA,MAAA,OAAmBC,UAAU,+FAA8FE,SAAA,EACzHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChEvD,EAAQ6C,EAAKjC,OAASiC,EAAKlC,QAE9B6C,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDjB,EAAaQ,EAAK3B,QAAQc,OAAQuB,SAC9FlB,EAAaQ,EAAK3B,QAAQa,YAI/ByB,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gDAA+CE,SACzDvD,EAAQ6C,EAAK9B,cAAgB8B,EAAK/B,cAIpC+B,EAAKzB,aACJoC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4BAA2BE,SACvCV,EAAKzB,WAAW2C,IAAKU,IACpBrB,EAAAA,EAAAA,MAAA,QAAqBC,UAAS,8CAAAC,OACf,SAAbmB,EAAsB,mEACT,UAAbA,EAAuB,uEACvB,4EACClB,SAAA,EACDC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,OAAAC,OACG,SAAbmB,EAAsB,iBACT,UAAbA,EAAuB,gBACvB,UAAS,WAEVA,IAVQA,MAiBhB5B,EAAKxB,WACJ+B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DE,SAAA,CAAExD,EAAE,WAAY,YAAY,QACrGyD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uBAAsBE,SAClCV,EAAKxB,SAAS0C,IAAKW,IAClBlB,EAAAA,EAAAA,KAAA,QAAoBH,UAAU,0FAAyFE,SACpHmB,GADQA,UAQnBtB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CE,SAAA,CAAExD,EAAE,WAAY,YAAY,QACtFyD,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDzB,EAAegB,EAAKhC,UAAUmB,OAAQuB,SAClG1B,EAAegB,EAAKhC,UAAUkB,YAInCqB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CE,SAAA,CAAExD,EAAE,SAAU,UAAU,QAClFyD,EAAAA,EAAAA,KAAA,QAAMH,UAAU,oDAAmDE,SAAEV,EAAK7B,aAG5EoC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CE,SAAA,CAAExD,EAAE,QAAS,SAAS,QAChFyD,EAAAA,EAAAA,KAAA,QAAMH,UAAU,oDAAmDE,SAAEV,EAAK1B,iBAG5EiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2CAA0CE,SAAA,CAAExD,EAAE,eAAgB,iBAAiB,QAC/FyD,EAAAA,EAAAA,KAAA,QAAMH,UAAU,2CAA0CE,SACvD,IAAIoB,KAAK9B,EAAK5B,cAAc2D,8BAKnCxB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,CAC5BV,EAAKvB,OACJkC,EAAAA,EAAAA,KAACC,EAAAA,GAAI,CACHC,GAAIb,EAAKvB,MACT+B,UAAU,6GAA4GE,SAErHxD,EAAE,UAAW,eAGhByD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,iGAAgGE,SAC/GxD,EAAE,UAAW,eAGlByD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wJAAuJE,UACvKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mBAEfG,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wJAAuJE,UACvKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBA3FTR,EAAKnC,OAkGW,IAA7BiC,EAAkBgB,SACjBP,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExD,EAAE,eAAgB,qBAErByD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CxD,EAAE,sBAAuB,mDASvB,cAAdG,IACCkD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFE,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GxD,EAAE,WAAY,gBAEjByD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GxD,EAAE,UAAW,cAEhByD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GxD,EAAE,cAAe,mBAEpByD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GxD,EAAE,gBAAiB,qBAEtByD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GxD,EAAE,SAAU,aAEfyD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GxD,EAAE,UAAW,mBAIpByD,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvFhC,EAAewC,IAAKlB,IACnBO,EAAAA,EAAAA,MAAA,MAAkBC,UAAU,0CAAyCE,SAAA,EACnEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAAEV,EAAKrB,cAE3EgC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DvD,EAAQ6C,EAAKpB,YAAcoB,EAAKnB,mBAGrC8B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EV,EAAKlB,eAER6B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9E,IAAIoB,KAAK9B,EAAKjB,eAAegD,wBAEhCpB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDjB,EAAaQ,EAAK3B,QAAQc,OAAQuB,SAC9FlB,EAAaQ,EAAK3B,QAAQa,WAG/ByB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kDAAiDE,UAC7DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,gFAA+EE,UAC/FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kBAEfG,EAAAA,EAAAA,KAAA,UAAQH,UAAU,oFAAmFE,UACnGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBAEfG,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wFAAuFE,UACvGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,yBA7BZR,EAAKnC,YAuCK,IAA1Ba,EAAeoC,SACdP,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExD,EAAE,mBAAoB,yBAEzByD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CxD,EAAE,uBAAwB,4CAQtB,YAAdG,IACCsD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0FAAyFE,UACtGH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExD,EAAE,cAAe,mBAEpByD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjDxD,EAAE,kBAAmB,uDAExBqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,iBACHL,UAAU,2GAA0GE,SAAA,EAEpHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZtD,EAAE,gBAAiB,0BAOb,cAAdG,IACCsD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0FAAyFE,UACtGH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qEACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExD,EAAE,gBAAiB,qBAEtByD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjDxD,EAAE,gBAAiB,kEAEtBqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,mBACHL,UAAU,6GAA4GE,SAAA,EAEtHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2BACZtD,EAAE,gBAAiB,0BAOb,aAAdG,IACCsD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0FAAyFE,UACtGH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+DACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnExD,EAAE,eAAgB,oBAErByD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjDxD,EAAE,eAAgB,gEAErBqD,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,kBACHL,UAAU,+GAA8GE,SAAA,EAExHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sBACZtD,EAAE,iBAAkB,8B", "sources": ["pages/Forms/Forms.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { Link } from 'react-router-dom';\n\nconst Forms = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('overview');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterCategory, setFilterCategory] = useState('all');\n\n  // Mock form templates data\n  const [formTemplates] = useState([\n    {\n      id: 1,\n      name: 'PT Adult Initial Assessment Form',\n      nameAr: 'نموذج التقييم الأولي للعلاج الطبيعي للبالغين',\n      category: 'assessment',\n      description: 'Comprehensive 8-page PT Adult Initial Assessment Form with body map, CARF & CBAHI compliant',\n      descriptionAr: 'نموذج تقييم أولي شامل للعلاج الطبيعي من 8 صفحات مع خريطة الجسم، متوافق مع CARF و CBAHI',\n      fields: 300,\n      lastModified: '2024-02-10',\n      status: 'active',\n      usageCount: 125,\n      compliance: ['CARF', 'CBAHI', 'HIPAA'],\n      features: ['Body Map', 'Pain Assessment', 'ROM Testing', 'Muscle Testing', 'Neurodynamics'],\n      route: '/assessment/new'\n    },\n    {\n      id: 2,\n      name: 'Initial Assessment Form',\n      nameAr: 'نموذج التقييم الأولي',\n      category: 'assessment',\n      description: 'Comprehensive initial assessment for new patients',\n      descriptionAr: 'تقييم أولي شامل للمرضى الجدد',\n      fields: 12,\n      lastModified: '2024-01-15',\n      status: 'active',\n      usageCount: 45\n    },\n    {\n      id: 2,\n      name: 'Progress Evaluation Form',\n      nameAr: 'نموذج تقييم التقدم',\n      category: 'evaluation',\n      description: 'Monthly progress evaluation for ongoing treatments',\n      descriptionAr: 'تقييم التقدم الشهري للعلاجات الجارية',\n      fields: 8,\n      lastModified: '2024-01-12',\n      status: 'active',\n      usageCount: 32\n    },\n    {\n      id: 3,\n      name: 'Sensory Profile Assessment',\n      nameAr: 'تقييم الملف الحسي',\n      category: 'special_needs',\n      description: 'Detailed sensory processing assessment for special needs patients',\n      descriptionAr: 'تقييم مفصل للمعالجة الحسية لمرضى الاحتياجات الخاصة',\n      fields: 15,\n      lastModified: '2024-01-10',\n      status: 'active',\n      usageCount: 28\n    },\n    {\n      id: 4,\n      name: 'Family Consultation Form',\n      nameAr: 'نموذج استشارة الأسرة',\n      category: 'consultation',\n      description: 'Family meeting and consultation documentation',\n      descriptionAr: 'توثيق اجتماع واستشارة الأسرة',\n      fields: 6,\n      lastModified: '2024-01-08',\n      status: 'active',\n      usageCount: 18\n    },\n    {\n      id: 5,\n      name: 'Discharge Summary Form',\n      nameAr: 'نموذج ملخص الخروج',\n      category: 'discharge',\n      description: 'Patient discharge summary and recommendations',\n      descriptionAr: 'ملخص خروج المريض والتوصيات',\n      fields: 10,\n      lastModified: '2024-01-05',\n      status: 'draft',\n      usageCount: 5\n    }\n  ]);\n\n  // Mock submitted forms data\n  const [submittedForms] = useState([\n    {\n      id: 1,\n      formName: 'Initial Assessment Form',\n      patientName: 'أحمد محمد الأحمد',\n      patientNameEn: 'Ahmed Mohammed Al-Ahmed',\n      submittedBy: 'Dr. Sarah Al-Rashid',\n      submittedDate: '2024-01-20',\n      status: 'completed'\n    },\n    {\n      id: 2,\n      formName: 'Progress Evaluation Form',\n      patientName: 'فاطمة علي السالم',\n      patientNameEn: 'Fatima Ali Al-Salem',\n      submittedBy: 'Dr. Ahmed Al-Mansouri',\n      submittedDate: '2024-01-19',\n      status: 'pending_review'\n    },\n    {\n      id: 3,\n      formName: 'Sensory Profile Assessment',\n      patientName: 'محمد عبدالله الخالد',\n      patientNameEn: 'Mohammed Abdullah Al-Khalid',\n      submittedBy: 'Dr. Maryam Al-Zahra',\n      submittedDate: '2024-01-18',\n      status: 'completed'\n    }\n  ]);\n\n  const categoryLabels = {\n    assessment: { label: t('assessment', 'Assessment'), color: 'bg-blue-100 text-blue-800' },\n    evaluation: { label: t('evaluation', 'Evaluation'), color: 'bg-green-100 text-green-800' },\n    special_needs: { label: t('specialNeeds', 'Special Needs'), color: 'bg-purple-100 text-purple-800' },\n    consultation: { label: t('consultation', 'Consultation'), color: 'bg-yellow-100 text-yellow-800' },\n    discharge: { label: t('discharge', 'Discharge'), color: 'bg-gray-100 text-gray-800' }\n  };\n\n  const statusLabels = {\n    active: { label: t('active', 'Active'), color: 'bg-green-100 text-green-800' },\n    draft: { label: t('draft', 'Draft'), color: 'bg-yellow-100 text-yellow-800' },\n    archived: { label: t('archived', 'Archived'), color: 'bg-gray-100 text-gray-800' },\n    completed: { label: t('completed', 'Completed'), color: 'bg-green-100 text-green-800' },\n    pending_review: { label: t('pendingReview', 'Pending Review'), color: 'bg-yellow-100 text-yellow-800' }\n  };\n\n  const filteredTemplates = formTemplates.filter(form => {\n    const matchesSearch = form.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         form.nameAr.includes(searchTerm);\n    const matchesCategory = filterCategory === 'all' || form.category === filterCategory;\n    return matchesSearch && matchesCategory;\n  });\n\n  const tabs = [\n    { id: 'overview', label: t('overview', 'Overview'), icon: 'fas fa-tachometer-alt' },\n    { id: 'templates', label: t('formTemplates', 'Form Templates'), icon: 'fas fa-file-alt' },\n    { id: 'submitted', label: t('submittedForms', 'Submitted Forms'), icon: 'fas fa-inbox' },\n    { id: 'analytics', label: t('analytics', 'Analytics'), icon: 'fas fa-chart-bar' },\n    { id: 'builder', label: t('formBuilder', 'Form Builder'), icon: 'fas fa-tools' },\n    { id: 'settings', label: t('settings', 'Settings'), icon: 'fas fa-cog' }\n  ];\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('forms', 'Forms')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('formsDesc', 'Manage assessment forms and patient documentation')}\n          </p>\n        </div>\n        <div className=\"flex space-x-4\">\n          <Link\n            to=\"/forms/builder\"\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center\"\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('createNewForm', 'Create New Form')}\n          </Link>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n              <i className=\"fas fa-file-alt text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('totalTemplates', 'Total Templates')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{formTemplates.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-full\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('activeTemplates', 'Active Templates')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formTemplates.filter(f => f.status === 'active').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full\">\n              <i className=\"fas fa-inbox text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('submittedForms', 'Submitted Forms')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{submittedForms.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n              <i className=\"fas fa-chart-bar text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('totalUsage', 'Total Usage')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formTemplates.reduce((sum, f) => sum + f.usageCount, 0)}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600 mb-6\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <i className={tab.icon}></i>\n              <span>{tab.label}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Overview Tab - Navigation Cards */}\n      {activeTab === 'overview' && (\n        <div className=\"space-y-8\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow-lg border border-blue-200 dark:border-blue-700 p-6\">\n            <h2 className=\"text-xl font-semibold text-blue-900 dark:text-blue-100 mb-6 flex items-center\">\n              <i className=\"fas fa-tachometer-alt text-blue-600 dark:text-blue-400 mr-2\"></i>\n              {t('formsOverview', 'Forms Management Overview')}\n            </h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {/* Form Templates */}\n              <Link\n                to=\"/forms/templates\"\n                className=\"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-900/60 transition-colors\">\n                    <i className=\"fas fa-th-large text-blue-600 dark:text-blue-400 text-2xl\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-semibold text-blue-900 dark:text-blue-100\">{t('formTemplates', 'Form Templates')}</h3>\n                    <p className=\"text-sm text-blue-600 dark:text-blue-400\">{formTemplates.length} templates available</p>\n                  </div>\n                </div>\n                <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                  {t('formTemplatesDesc', 'Browse and use pre-built form templates for patient assessments')}\n                </p>\n              </Link>\n\n              {/* Form Analytics */}\n              <Link\n                to=\"/forms/analytics\"\n                className=\"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-900/60 transition-colors\">\n                    <i className=\"fas fa-chart-bar text-green-600 dark:text-green-400 text-2xl\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-semibold text-green-900 dark:text-green-100\">{t('formAnalytics', 'Form Analytics')}</h3>\n                    <p className=\"text-sm text-green-600 dark:text-green-400\">87.5% completion rate</p>\n                  </div>\n                </div>\n                <p className=\"text-sm text-green-700 dark:text-green-300\">\n                  {t('formAnalyticsDesc', 'View comprehensive insights into form usage and performance')}\n                </p>\n              </Link>\n\n              {/* Form Builder */}\n              <Link\n                to=\"/forms/builder\"\n                className=\"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg group-hover:bg-purple-200 dark:group-hover:bg-purple-900/60 transition-colors\">\n                    <i className=\"fas fa-tools text-purple-600 dark:text-purple-400 text-2xl\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-semibold text-purple-900 dark:text-purple-100\">{t('formBuilder', 'Form Builder')}</h3>\n                    <p className=\"text-sm text-purple-600 dark:text-purple-400\">Create custom forms</p>\n                  </div>\n                </div>\n                <p className=\"text-sm text-purple-700 dark:text-purple-300\">\n                  {t('formBuilderDesc', 'Build custom forms with drag-and-drop interface and advanced features')}\n                </p>\n              </Link>\n\n              {/* Form Submissions */}\n              <Link\n                to=\"/forms/submissions\"\n                className=\"bg-white dark:bg-orange-800/20 border border-orange-200 dark:border-orange-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg group-hover:bg-orange-200 dark:group-hover:bg-orange-900/60 transition-colors\">\n                    <i className=\"fas fa-inbox text-orange-600 dark:text-orange-400 text-2xl\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-semibold text-orange-900 dark:text-orange-100\">{t('formSubmissions', 'Form Submissions')}</h3>\n                    <p className=\"text-sm text-orange-600 dark:text-orange-400\">{submittedForms.length} submissions</p>\n                  </div>\n                </div>\n                <p className=\"text-sm text-orange-700 dark:text-orange-300\">\n                  {t('formSubmissionsDesc', 'Review and manage all submitted forms and patient data')}\n                </p>\n              </Link>\n\n              {/* Form Settings */}\n              <Link\n                to=\"/forms/settings\"\n                className=\"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-6 hover:shadow-lg transition-all duration-200 group\"\n              >\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"p-3 bg-indigo-100 dark:bg-indigo-900/40 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-900/60 transition-colors\">\n                    <i className=\"fas fa-cog text-indigo-600 dark:text-indigo-400 text-2xl\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-semibold text-indigo-900 dark:text-indigo-100\">{t('formSettings', 'Form Settings')}</h3>\n                    <p className=\"text-sm text-indigo-600 dark:text-indigo-400\">Configure system</p>\n                  </div>\n                </div>\n                <p className=\"text-sm text-indigo-700 dark:text-indigo-300\">\n                  {t('formSettingsDesc', 'Configure form behavior, security, and system preferences')}\n                </p>\n              </Link>\n\n              {/* Quick Actions */}\n              <div className=\"bg-white dark:bg-teal-800/20 border border-teal-200 dark:border-teal-600 rounded-lg p-6\">\n                <div className=\"flex items-center mb-4\">\n                  <div className=\"p-3 bg-teal-100 dark:bg-teal-900/40 rounded-lg\">\n                    <i className=\"fas fa-bolt text-teal-600 dark:text-teal-400 text-2xl\"></i>\n                  </div>\n                  <div className=\"ml-4\">\n                    <h3 className=\"text-lg font-semibold text-teal-900 dark:text-teal-100\">{t('quickActions', 'Quick Actions')}</h3>\n                    <p className=\"text-sm text-teal-600 dark:text-teal-400\">Common tasks</p>\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <Link\n                    to=\"/forms/pt-assessment\"\n                    className=\"block text-sm text-teal-700 dark:text-teal-300 hover:text-teal-900 dark:hover:text-teal-100 transition-colors\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('newPTAssessment', 'New PT Assessment')}\n                  </Link>\n                  <Link\n                    to=\"/forms/treatment-plan\"\n                    className=\"block text-sm text-teal-700 dark:text-teal-300 hover:text-teal-900 dark:hover:text-teal-100 transition-colors\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('newTreatmentPlan', 'New Treatment Plan')}\n                  </Link>\n                  <Link\n                    to=\"/forms/daily-progress\"\n                    className=\"block text-sm text-teal-700 dark:text-teal-300 hover:text-teal-900 dark:hover:text-teal-100 transition-colors\"\n                  >\n                    <i className=\"fas fa-plus mr-2\"></i>\n                    {t('newProgressNote', 'New Progress Note')}\n                  </Link>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Form Templates Tab */}\n      {activeTab === 'templates' && (\n        <>\n          {/* Filters */}\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('search', 'Search')}\n                </label>\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    placeholder={t('searchForms', 'Search forms...')}\n                    className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                  />\n                  <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('category', 'Category')}\n                </label>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                >\n                  <option value=\"all\">{t('allCategories', 'All Categories')}</option>\n                  <option value=\"assessment\">{t('assessment', 'Assessment')}</option>\n                  <option value=\"evaluation\">{t('evaluation', 'Evaluation')}</option>\n                  <option value=\"special_needs\">{t('specialNeeds', 'Special Needs')}</option>\n                  <option value=\"consultation\">{t('consultation', 'Consultation')}</option>\n                  <option value=\"discharge\">{t('discharge', 'Discharge')}</option>\n                </select>\n              </div>\n\n              <div className=\"flex items-end\">\n                <button className=\"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n                  <i className=\"fas fa-filter mr-2\"></i>\n                  {t('advancedFilters', 'Advanced Filters')}\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Form Templates Grid */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 p-6\">\n              {filteredTemplates.map((form) => (\n                <div key={form.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-md transition-shadow\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      {isRTL ? form.nameAr : form.name}\n                    </h3>\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusLabels[form.status].color}`}>\n                      {statusLabels[form.status].label}\n                    </span>\n                  </div>\n\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                    {isRTL ? form.descriptionAr : form.description}\n                  </p>\n\n                  {/* Compliance Badges */}\n                  {form.compliance && (\n                    <div className=\"flex flex-wrap gap-2 mb-4\">\n                      {form.compliance.map((standard) => (\n                        <span key={standard} className={`px-2 py-1 text-xs font-medium rounded-full ${\n                          standard === 'CARF' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200' :\n                          standard === 'CBAHI' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200' :\n                          'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-200'\n                        }`}>\n                          <i className={`fas ${\n                            standard === 'CARF' ? 'fa-certificate' :\n                            standard === 'CBAHI' ? 'fa-shield-alt' :\n                            'fa-lock'\n                          } mr-1`}></i>\n                          {standard}\n                        </span>\n                      ))}\n                    </div>\n                  )}\n\n                  {/* Features */}\n                  {form.features && (\n                    <div className=\"mb-4\">\n                      <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">{t('features', 'Features')}:</h4>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {form.features.map((feature) => (\n                          <span key={feature} className=\"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded\">\n                            {feature}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  <div className=\"space-y-2 mb-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('category', 'Category')}:</span>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${categoryLabels[form.category].color}`}>\n                        {categoryLabels[form.category].label}\n                      </span>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('fields', 'Fields')}:</span>\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{form.fields}</span>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('usage', 'Usage')}:</span>\n                      <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{form.usageCount}</span>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('lastModified', 'Last Modified')}:</span>\n                      <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {new Date(form.lastModified).toLocaleDateString()}\n                      </span>\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-2\">\n                    {form.route ? (\n                      <Link\n                        to={form.route}\n                        className=\"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors text-center\"\n                      >\n                        {t('useForm', 'Use Form')}\n                      </Link>\n                    ) : (\n                      <button className=\"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors\">\n                        {t('useForm', 'Use Form')}\n                      </button>\n                    )}\n                    <button className=\"px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n                      <i className=\"fas fa-edit\"></i>\n                    </button>\n                    <button className=\"px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n                      <i className=\"fas fa-copy\"></i>\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {filteredTemplates.length === 0 && (\n              <div className=\"text-center py-12\">\n                <i className=\"fas fa-file-alt text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('noFormsFound', 'No forms found')}\n                </h3>\n                <p className=\"text-gray-500 dark:text-gray-400\">\n                  {t('tryAdjustingFilters', 'Try adjusting your search or filters')}\n                </p>\n              </div>\n            )}\n          </div>\n        </>\n      )}\n\n      {/* Submitted Forms Tab */}\n      {activeTab === 'submitted' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('formName', 'Form Name')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('patient', 'Patient')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('submittedBy', 'Submitted By')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('submittedDate', 'Submitted Date')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('status', 'Status')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('actions', 'Actions')}\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                {submittedForms.map((form) => (\n                  <tr key={form.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">{form.formName}</div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {isRTL ? form.patientName : form.patientNameEn}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {form.submittedBy}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {new Date(form.submittedDate).toLocaleDateString()}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusLabels[form.status].color}`}>\n                        {statusLabels[form.status].label}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button className=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                          <i className=\"fas fa-eye\"></i>\n                        </button>\n                        <button className=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300\">\n                          <i className=\"fas fa-download\"></i>\n                        </button>\n                        <button className=\"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300\">\n                          <i className=\"fas fa-edit\"></i>\n                        </button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {submittedForms.length === 0 && (\n            <div className=\"text-center py-12\">\n              <i className=\"fas fa-inbox text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {t('noSubmittedForms', 'No submitted forms')}\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                {t('submittedFormsPrompt', 'Submitted forms will appear here')}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Form Builder Tab */}\n      {activeTab === 'builder' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-tools text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('formBuilder', 'Form Builder')}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n              {t('formBuilderDesc', 'Create custom forms with drag-and-drop interface')}\n            </p>\n            <Link\n              to=\"/forms/builder\"\n              className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('startBuilding', 'Start Building')}\n            </Link>\n          </div>\n        </div>\n      )}\n\n      {/* Analytics Tab */}\n      {activeTab === 'analytics' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-chart-bar text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('formAnalytics', 'Form Analytics')}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n              {t('analyticsDesc', 'View comprehensive insights into form usage and performance')}\n            </p>\n            <Link\n              to=\"/forms/analytics\"\n              className=\"inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n            >\n              <i className=\"fas fa-chart-line mr-2\"></i>\n              {t('viewAnalytics', 'View Analytics')}\n            </Link>\n          </div>\n        </div>\n      )}\n\n      {/* Settings Tab */}\n      {activeTab === 'settings' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-cog text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('formSettings', 'Form Settings')}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-6\">\n              {t('settingsDesc', 'Configure form behavior, security, and system preferences')}\n            </p>\n            <Link\n              to=\"/forms/settings\"\n              className=\"inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors\"\n            >\n              <i className=\"fas fa-tools mr-2\"></i>\n              {t('manageSettings', 'Manage Settings')}\n            </Link>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Forms;\n"], "names": ["Forms", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "searchTerm", "setSearchTerm", "filterCategory", "setFilterCategory", "formTemplates", "id", "name", "nameAr", "category", "description", "descriptionAr", "fields", "lastModified", "status", "usageCount", "compliance", "features", "route", "submittedForms", "formName", "patientName", "patientNameEn", "submittedBy", "submittedDate", "categoryLabels", "assessment", "label", "color", "evaluation", "special_needs", "consultation", "discharge", "statusLabels", "active", "draft", "archived", "completed", "pending_review", "filteredTemplates", "filter", "form", "matchesSearch", "toLowerCase", "includes", "matchesCategory", "tabs", "icon", "_jsxs", "className", "concat", "children", "_jsx", "Link", "to", "length", "f", "reduce", "sum", "map", "tab", "onClick", "_Fragment", "type", "value", "onChange", "e", "target", "placeholder", "standard", "feature", "Date", "toLocaleDateString"], "sourceRoot": ""}