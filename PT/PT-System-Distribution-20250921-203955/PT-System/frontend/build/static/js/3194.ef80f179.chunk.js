"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3194],{3194:(e,a,l)=>{l.r(a),l.d(a,{default:()=>o});var r=l(2555),t=l(5043),s=l(7921),i=l(579);const o=e=>{var a,l,o,d,n,c,g,m,x,b,v,y,h,u,p,k,f,j,N,w,A;let{initialData:C,onSave:S,onCancel:D,isEditing:P=!1,patientInfo:L={}}=e;const{t:M,isRTL:U}=(0,s.o)(),F=C||{},[I,T]=(0,t.useState)({assessmentDate:(new Date).toISOString().split("T")[0],assessedBy:"",caregiverPresent:"",primaryDiagnosis:F.primaryDiagnosis||"",secondaryDiagnoses:F.secondaryDiagnoses||[],cognitiveLevel:F.cognitiveLevel||"",functionalLevel:F.functionalLevel||"",communicationAbilities:{verbal:(null===(a=F.communicationAbilities)||void 0===a?void 0:a.verbal)||"none",nonVerbal:(null===(l=F.communicationAbilities)||void 0===l?void 0:l.nonVerbal)||[],comprehension:(null===(o=F.communicationAbilities)||void 0===o?void 0:o.comprehension)||"limited",expression:(null===(d=F.communicationAbilities)||void 0===d?void 0:d.expression)||"limited"},sensoryProfile:{visual:(null===(n=F.sensoryProfile)||void 0===n?void 0:n.visual)||"typical",auditory:(null===(c=F.sensoryProfile)||void 0===c?void 0:c.auditory)||"typical",tactile:(null===(g=F.sensoryProfile)||void 0===g?void 0:g.tactile)||"typical",vestibular:(null===(m=F.sensoryProfile)||void 0===m?void 0:m.vestibular)||"typical",proprioceptive:(null===(x=F.sensoryProfile)||void 0===x?void 0:x.proprioceptive)||"typical"},behaviorProfile:{socialInteraction:(null===(b=F.behaviorProfile)||void 0===b?void 0:b.socialInteraction)||"limited",attentionSpan:(null===(v=F.behaviorProfile)||void 0===v?void 0:v.attentionSpan)||"short",emotionalRegulation:(null===(y=F.behaviorProfile)||void 0===y?void 0:y.emotionalRegulation)||"needs-support",adaptability:(null===(h=F.behaviorProfile)||void 0===h?void 0:h.adaptability)||"rigid"},motorSkills:{grossMotor:(null===(u=F.motorSkills)||void 0===u?void 0:u.grossMotor)||"delayed",fineMotor:(null===(p=F.motorSkills)||void 0===p?void 0:p.fineMotor)||"delayed",coordination:(null===(k=F.motorSkills)||void 0===k?void 0:k.coordination)||"impaired",balance:(null===(f=F.motorSkills)||void 0===f?void 0:f.balance)||"impaired"},adaptiveStrategies:{environmentalModifications:(null===(j=F.adaptiveStrategies)||void 0===j?void 0:j.environmentalModifications)||[],communicationSupports:(null===(N=F.adaptiveStrategies)||void 0===N?void 0:N.communicationSupports)||[],behavioralSupports:(null===(w=F.adaptiveStrategies)||void 0===w?void 0:w.behavioralSupports)||[],sensorySupports:(null===(A=F.adaptiveStrategies)||void 0===A?void 0:A.sensorySupports)||[]},treatmentGoals:F.treatmentGoals||[],recommendations:F.recommendations||"",followUpNeeded:F.followUpNeeded||!1,nextAssessmentDate:F.nextAssessmentDate||"",strengths:F.strengths||"",challenges:F.challenges||"",familyConcerns:F.familyConcerns||"",additionalNotes:F.additionalNotes||""}),B=[{value:"typical",label:M("typical","Typical")},{value:"mild-delay",label:M("mildDelay","Mild Delay")},{value:"moderate-delay",label:M("moderateDelay","Moderate Delay")},{value:"severe-delay",label:M("severeDelay","Severe Delay")},{value:"profound-delay",label:M("profoundDelay","Profound Delay")}],G=[{value:"independent",label:M("independent","Independent")},{value:"minimal-assist",label:M("minimalAssist","Minimal Assistance")},{value:"moderate-assist",label:M("moderateAssist","Moderate Assistance")},{value:"maximum-assist",label:M("maximumAssist","Maximum Assistance")},{value:"total-assist",label:M("totalAssist","Total Assistance")}],R=[{value:"none",label:M("none","None")},{value:"limited",label:M("limited","Limited")},{value:"functional",label:M("functional","Functional")},{value:"good",label:M("good","Good")},{value:"excellent",label:M("excellent","Excellent")}],O=[{value:"typical",label:M("typical","Typical")},{value:"hyposensitive",label:M("hyposensitive","Under-responsive")},{value:"hypersensitive",label:M("hypersensitive","Over-responsive")},{value:"seeking",label:M("seeking","Sensory Seeking")},{value:"avoiding",label:M("avoiding","Sensory Avoiding")}],E=(e,a)=>{T(l=>(0,r.A)((0,r.A)({},l),{},{[e]:a}))},V=(e,a,l)=>{T(t=>(0,r.A)((0,r.A)({},t),{},{[e]:(0,r.A)((0,r.A)({},t[e]),{},{[a]:l})}))};return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 ".concat(U?"font-arabic":"font-english"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:M("specialNeedsAssessment","Special Needs Assessment")}),L.name&&(0,i.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[M("patient","Patient"),": ",L.name]})]}),(0,i.jsxs)("div",{className:"flex space-x-3",children:[(0,i.jsx)("button",{onClick:D,className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:M("cancel","Cancel")}),(0,i.jsx)("button",{onClick:()=>{S&&S(I)},className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:M("saveAssessment","Save Assessment")})]})]}),(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("basicAssessmentInfo","Basic Assessment Information")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("assessmentDate","Assessment Date")}),(0,i.jsx)("input",{type:"date",value:I.assessmentDate,onChange:e=>E("assessmentDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("assessedBy","Assessed By")}),(0,i.jsx)("input",{type:"text",value:I.assessedBy,onChange:e=>E("assessedBy",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("therapistName","Therapist Name")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("caregiverPresent","Caregiver Present")}),(0,i.jsx)("input",{type:"text",value:I.caregiverPresent,onChange:e=>E("caregiverPresent",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("caregiverName","Caregiver Name")})]})]})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("diagnosisAndFunctionalLevel","Diagnosis and Functional Level")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("cognitiveLevel","Cognitive Level")}),(0,i.jsxs)("select",{value:I.cognitiveLevel,onChange:e=>E("cognitiveLevel",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:M("selectLevel","Select Level")}),B.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("functionalLevel","Functional Level")}),(0,i.jsxs)("select",{value:I.functionalLevel,onChange:e=>E("functionalLevel",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:M("selectLevel","Select Level")}),G.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))]})]})]})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("communicationAssessment","Communication Assessment")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("verbalCommunication","Verbal Communication")}),(0,i.jsx)("select",{value:I.communicationAbilities.verbal,onChange:e=>V("communicationAbilities","verbal",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:R.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("comprehension","Comprehension")}),(0,i.jsx)("select",{value:I.communicationAbilities.comprehension,onChange:e=>V("communicationAbilities","comprehension",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:R.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("sensoryProcessing","Sensory Processing")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Object.entries(I.sensoryProfile).map(e=>{let[a,l]=e;return(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M(a,a.charAt(0).toUpperCase()+a.slice(1))}),(0,i.jsx)("select",{value:l,onChange:e=>V("sensoryProfile",a,e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:O.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]},a)})})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("motorSkills","Motor Skills")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:Object.entries(I.motorSkills).map(e=>{let[a,l]=e;return(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M(a,a.charAt(0).toUpperCase()+a.slice(1))}),(0,i.jsxs)("select",{value:l,onChange:e=>V("motorSkills",a,e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"typical",children:M("typical","Typical")}),(0,i.jsx)("option",{value:"delayed",children:M("delayed","Delayed")}),(0,i.jsx)("option",{value:"impaired",children:M("impaired","Impaired")}),(0,i.jsx)("option",{value:"emerging",children:M("emerging","Emerging")})]})]},a)})})]}),(0,i.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("strengthsAndChallenges","Strengths and Challenges")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("strengths","Strengths")}),(0,i.jsx)("textarea",{value:I.strengths,onChange:e=>E("strengths",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("strengthsPlaceholder","List patient strengths and abilities...")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("challenges","Challenges")}),(0,i.jsx)("textarea",{value:I.challenges,onChange:e=>E("challenges",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("challengesPlaceholder","List areas of difficulty and challenges...")})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:M("recommendationsAndGoals","Recommendations and Goals")}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("recommendations","Recommendations")}),(0,i.jsx)("textarea",{value:I.recommendations,onChange:e=>E("recommendations",e.target.value),rows:"4",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("recommendationsPlaceholder","Treatment recommendations and strategies...")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("familyConcerns","Family Concerns")}),(0,i.jsx)("textarea",{value:I.familyConcerns,onChange:e=>E("familyConcerns",e.target.value),rows:"3",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:M("familyConcernsPlaceholder","Family concerns and priorities...")})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("input",{type:"checkbox",id:"followUpNeeded",checked:I.followUpNeeded,onChange:e=>E("followUpNeeded",e.target.checked),className:"w-4 h-4 text-blue-600"}),(0,i.jsx)("label",{htmlFor:"followUpNeeded",className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:M("followUpAssessmentNeeded","Follow-up assessment needed")})]}),I.followUpNeeded&&(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:M("nextAssessmentDate","Next Assessment Date")}),(0,i.jsx)("input",{type:"date",value:I.nextAssessmentDate,onChange:e=>E("nextAssessmentDate",e.target.value),className:"w-full md:w-auto px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]})]})]})}}}]);
//# sourceMappingURL=3194.ef80f179.chunk.js.map