"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1549],{1549:(e,a,t)=>{t.r(a),t.d(a,{default:()=>c});var s=t(2555),r=t(5043),n=t(7921),i=t(4528),d=t(3216),l=t(6643),o=t(579);const c=()=>{const{t:e,isRTL:a}=(0,n.o)(),{user:t}=(0,i.A)(),{patientId:c}=(0,d.g)(),m=(0,d.Zp)(),[g,p]=(0,r.useState)({patientName:"",patientId:c||"",assessmentDate:(new Date).toISOString().split("T")[0],assessorName:(null===t||void 0===t?void 0:t.name)||"",currentPainLevel:0,averagePainLevel:0,worstPainLevel:0,bestPainLevel:0,painLocations:[],painQuality:[],painPattern:"",painOnset:"",painDuration:"",painFrequency:"",aggravatingFactors:[],aggravatingOther:"",relievingFactors:[],relievingOther:"",sleepImpact:0,workImpact:0,dailyActivitiesImpact:0,moodImpact:0,socialImpact:0,currentMedications:"",previousTreatments:"",treatmentEffectiveness:"",painGoals:"",functionalGoals:"",treatmentExpectations:"",bodyMapData:{},additionalNotes:"",assessorSignature:"",assessmentComplete:!1}),[b,x]=(0,r.useState)(!1),[y,u]=(0,r.useState)({});(0,r.useEffect)(()=>{c&&h()},[c]);const h=async()=>{try{x(!0);const e=await fetch("/api/patients/".concat(c));if(e.ok){const a=await e.json();p(e=>(0,s.A)((0,s.A)({},e),{},{patientName:a.name||"",patientId:a._id||c}))}}catch(e){console.error("Error loading patient data:",e)}finally{x(!1)}},v=(e,a)=>{p(t=>(0,s.A)((0,s.A)({},t),{},{[e]:a})),y[e]&&u(a=>(0,s.A)((0,s.A)({},a),{},{[e]:null}))};return b?(0,o.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,o.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:(0,o.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("painAssessmentScale","Pain Assessment Scale")}),(0,o.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e("painAssessmentDescription","Comprehensive pain evaluation and tracking")})]}),(0,o.jsx)("div",{className:"flex items-center space-x-2",children:(0,o.jsxs)("span",{className:"px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 text-sm rounded-full",children:[(0,o.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),e("painAssessment","Pain Assessment")]})})]})})}),(0,o.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),(()=>{const a={};return g.patientName.trim()||(a.patientName=e("patientNameRequired","Patient name is required")),g.assessorName.trim()||(a.assessorName=e("assessorNameRequired","Assessor name is required")),(g.currentPainLevel<0||g.currentPainLevel>10)&&(a.currentPainLevel=e("painLevelRange","Pain level must be between 0-10")),u(a),0===Object.keys(a).length})())try{x(!0);const a=(0,s.A)((0,s.A)({},g),{},{submittedBy:t.id,submittedAt:(new Date).toISOString()});if(!(await fetch("/api/v1/pain-assessments/public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})).ok)throw new Error("Failed to save pain assessment");alert(e("painAssessmentSaved","Pain assessment saved successfully!")),m(c?"/patients/".concat(c):"/patients")}catch(r){console.error("Error saving pain assessment:",r),alert(e("errorSaving","Error saving pain assessment. Please try again."))}finally{x(!1)}},className:"space-y-6",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("patientInformation","Patient Information")}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("patientName","Patient Name")," *"]}),(0,o.jsx)("input",{type:"text",value:g.patientName,onChange:e=>v("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(y.patientName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),y.patientName&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:y.patientName})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("assessmentDate","Assessment Date")," *"]}),(0,o.jsx)("input",{type:"date",value:g.assessmentDate,onChange:e=>v("assessmentDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),(0,o.jsxs)("div",{children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("assessorName","Assessor Name")," *"]}),(0,o.jsx)("input",{type:"text",value:g.assessorName,onChange:e=>v("assessorName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(y.assessorName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),y.assessorName&&(0,o.jsx)("p",{className:"text-red-500 text-sm mt-1",children:y.assessorName})]})]})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("painIntensity","Pain Intensity (0-10 Scale)")}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{field:"currentPainLevel",label:e("currentPain","Current Pain")},{field:"averagePainLevel",label:e("averagePain","Average Pain (24hrs)")},{field:"worstPainLevel",label:e("worstPain","Worst Pain (24hrs)")},{field:"bestPainLevel",label:e("bestPain","Best Pain (24hrs)")}].map(e=>(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e.label}),(0,o.jsx)("input",{type:"range",min:"0",max:"10",value:g[e.field],onChange:a=>v(e.field,parseInt(a.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"}),(0,o.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,o.jsx)("span",{children:"0"}),(0,o.jsx)("span",{className:"font-bold text-lg text-red-600",children:g[e.field]}),(0,o.jsx)("span",{children:"10"})]})]},e.field))})]}),(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("painLocation","Pain Location & Body Map")}),(0,o.jsx)(l.A,{painData:g.bodyMapData,onPainDataChange:e=>{p(a=>(0,s.A)((0,s.A)({},a),{},{bodyMapData:e}))},readonly:!1})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("button",{type:"button",onClick:()=>m(-1),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700",children:e("cancel","Cancel")}),(0,o.jsxs)("div",{className:"flex space-x-3",children:[(0,o.jsxs)("button",{type:"button",onClick:async()=>{try{x(!0);const a=(0,s.A)((0,s.A)({},g),{},{generatedAt:(new Date).toISOString(),generatedBy:t.name||t.email,patientId:c}),r=await fetch("/api/v1/pain-assessments/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(a)});if(!r.ok)throw new Error("HTTP error! status: ".concat(r.status));{const a=await r.blob(),t=window.URL.createObjectURL(a),s=document.createElement("a");s.href=t,s.download="pain-assessment-".concat(g.patientName.replace(/\s+/g,"-"),"-").concat(g.assessmentDate,".pdf"),document.body.appendChild(s),s.click(),window.URL.revokeObjectURL(t),document.body.removeChild(s),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(a){console.error("Error generating PDF:",a),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{x(!1)}},disabled:b,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:[(0,o.jsx)("i",{className:"fas fa-file-pdf mr-2"}),b?e("generating","Generating..."):e("generatePDF","Generate PDF")]}),(0,o.jsx)("button",{type:"submit",disabled:b,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?e("saving","Saving..."):e("savePainAssessment","Save Pain Assessment")})]})]})]})]})}}}]);
//# sourceMappingURL=1549.cf8b44b0.chunk.js.map