{"version": 3, "file": "static/js/2432.8d9c6c13.chunk.js", "mappings": "2MAIA,MA+jBA,EA/jBgBA,KACd,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,aACpCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAqBC,IAA0BN,EAAAA,EAAAA,WAAS,IACxDO,EAAiBC,IAAsBR,EAAAA,EAAAA,UAAS,OAGhDS,EAAUC,IAAeV,EAAAA,EAAAA,UAAS,CACvC,CACEW,GAAI,eACJC,YAAa,kBACbC,UAAW,OACXC,UAAW,aACXC,QAAS,aACTC,OAAQ,KACRC,WAAY,KACZC,OAAQ,OACRC,cAAe,YACfC,kBAAmB,cACnBC,SAAU,CACR,CAAEC,KAAM,2BAA4BC,SAAU,EAAGC,UAAW,IAAKC,MAAO,MACxE,CAAEH,KAAM,qBAAsBC,SAAU,EAAGC,UAAW,IAAKC,MAAO,MAEpEC,MAAO,4BACPC,UAAW,mBAEb,CACEhB,GAAI,eACJC,YAAa,kBACbC,UAAW,OACXC,UAAW,aACXC,QAAS,aACTC,OAAQ,IACRC,WAAY,IACZC,OAAQ,iBACRC,cAAe,QACfC,kBAAmB,WACnBC,SAAU,CACR,CAAEC,KAAM,uBAAwBC,SAAU,EAAGC,UAAW,IAAKC,MAAO,MACpE,CAAEH,KAAM,2BAA4BC,SAAU,EAAGC,UAAW,IAAKC,MAAO,MAE1EC,MAAO,mCACPC,UAAW,mBAEb,CACEhB,GAAI,eACJC,YAAa,qBACbC,UAAW,OACXC,UAAW,aACXC,QAAS,aACTC,OAAQ,KACRC,WAAY,EACZC,OAAQ,UACRC,cAAe,OACfC,kBAAmB,KACnBC,SAAU,CACR,CAAEC,KAAM,iBAAkBC,SAAU,EAAGC,UAAW,IAAKC,MAAO,OAEhEC,MAAO,yBACPC,UAAW,wBAKRC,EAAUC,IAAe7B,EAAAA,EAAAA,UAAS,CACvC,CACEW,GAAI,eACJmB,UAAW,eACXlB,YAAa,kBACbI,OAAQ,KACRe,OAAQ,YACRC,SAAU,cACVC,KAAM,aACNf,OAAQ,YACRgB,UAAW,iBACXC,YAAa,UAEf,CACExB,GAAI,eACJmB,UAAW,eACXlB,YAAa,kBACbI,OAAQ,IACRe,OAAQ,OACRC,SAAU,KACVC,KAAM,aACNf,OAAQ,YACRgB,UAAW,WACXC,YAAa,gBAEf,CACExB,GAAI,eACJmB,UAAW,eACXlB,YAAa,kBACbI,OAAQ,KACRe,OAAQ,YACRC,SAAU,WACVC,KAAM,aACNf,OAAQ,UACRgB,UAAW,kBACXC,YAAa,YAIXC,EAAO,CACX,CAAEzB,GAAI,WAAY0B,MAAO1C,EAAE,WAAY,YAAa2C,KAAM,uBAC1D,CAAE3B,GAAI,WAAY0B,MAAO1C,EAAE,WAAY,YAAa2C,KAAM,sBAC1D,CAAE3B,GAAI,UAAW0B,MAAO1C,EAAE,UAAW,WAAY2C,KAAM,oBACvD,CAAE3B,GAAI,WAAY0B,MAAO1C,EAAE,WAAY,YAAa2C,KAAM,eAGtDC,EAAkBvB,GACf,IAAIwB,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,QACTC,OAAO5B,GAGN6B,EAAkB3B,IACtB,OAAQA,EAAO4B,eACb,IAAK,OAIL,IAAK,YAAa,MAAO,mDAHzB,IAAK,iBAAkB,MAAO,sDAC9B,IAAK,UAAW,MAAO,sDACvB,IAAK,UAEL,IAAK,SAAU,MAAO,6CACtB,QAAS,MAAO,kDA4XpB,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAS,aAAAC,OAAerD,EAAQ,cAAgB,gBAAiBsD,SAAA,EAEpEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DvD,EAAE,kBAAmB,yBAExBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjDvD,EAAE,qBAAsB,wDAI7BwD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8BAA6BE,UAC1CH,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oFAAmFE,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,yBACZrD,EAAE,aAAc,wBAMvBwD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gDAA+CE,UAC5DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBE,SACnCd,EAAKgB,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMvD,EAAasD,EAAI1C,IAChCqC,UAAS,4CAAAC,OACPnD,IAAcuD,EAAI1C,GACd,mDACA,0HACHuC,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKI,EAAIf,KAAI,WACxBe,EAAIhB,QATAgB,EAAI1C,SAgBF,aAAdb,IAxYHiD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,gBAAiB,qBAEtBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DzC,EAAS8C,kBAMlBJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,eAAgB,WAErBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DzC,EAAS+C,OAAOC,GAAsB,SAAfA,EAAIvC,QAAmBqC,kBAMvDJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,kBAAmB,cAExBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DzC,EAAS+C,OAAOC,GAAsB,YAAfA,EAAIvC,QAAuC,mBAAfuC,EAAIvC,QAA6BqC,kBAM7FJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,cAAe,mBAEpBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DX,EAAe9B,EAASiD,OAAO,CAACC,EAAKF,IAAQE,EAAMF,EAAIzC,OAAQ,kBAQ1E+B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FE,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChEvD,EAAE,WAAY,eAEjBoD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,SACES,KAAK,OACLC,YAAalE,EAAE,iBAAkB,sBACjCmE,MAAO7D,EACP8D,SAAWC,GAAM9D,EAAc8D,EAAEC,OAAOH,OACxCd,UAAU,8HAEZD,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAMhD,GAAuB,GACtC0C,UAAU,kFAAiFE,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZrD,EAAE,aAAc,yBAKvBwD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,YAAa,iBAElBwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,UAAW,cAEhBwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,SAAU,aAEfwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,OAAQ,WAEbwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,UAAW,eAEhBwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,SAAU,aAEfwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,UAAW,mBAIpBwD,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvFzC,EAAS2C,IAAKc,IACbnB,EAAAA,EAAAA,MAAA,MAAqBC,UAAU,0CAAyCE,SAAA,EACtEH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DgB,EAAQvD,MAEXwC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDgB,EAAQpD,gBAGbiC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DgB,EAAQtD,eAEXmC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CAAC,OACnDgB,EAAQrD,iBAGjBsC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,gFAA+EE,SAC1FX,EAAe2B,EAAQlD,WAE1B+B,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DX,EAAe2B,EAAQjD,cAEzBiD,EAAQlD,OAASkD,EAAQjD,aACxB8B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,CACpDX,EAAe2B,EAAQlD,OAASkD,EAAQjD,YAAY,IAAEtB,EAAE,YAAa,oBAI5EwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EgB,EAAQnD,WAEXoC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAeqB,EAAQhD,SAAUgC,SAC7FgB,EAAQhD,YAGb6B,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDE,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAM9C,EAAmB0D,GAClClB,UAAU,qFAAoFE,SAE7FvD,EAAE,OAAQ,WAEbwD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,yFAAwFE,SACvGvD,EAAE,QAAS,WAEM,SAAnBuE,EAAQhD,SACPiC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,wFAAuFE,SACtGvD,EAAE,gBAAiB,yBAlDnBuE,EAAQvD,kBAgRZ,aAAdb,IAhNHiD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,gBAAiB,qBAEtBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DtB,EAAS2B,kBAMlBJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,gBAAiB,qBAEtBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DX,EAAeX,EAAS4B,OAAOW,GAAkB,cAAbA,EAAEjD,QAAwBwC,OAAO,CAACC,EAAKQ,IAAMR,EAAMQ,EAAEnD,OAAQ,eAM1GmC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,kBAAmB,cAExBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DtB,EAAS4B,OAAOW,GAAkB,YAAbA,EAAEjD,QAAsBqC,kBAMtDJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChEvD,EAAE,cAAe,mBAEpBoD,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mDAAkDE,SAAA,CAC5DkB,KAAKC,MAAOzC,EAAS4B,OAAOW,GAAkB,cAAbA,EAAEjD,QAAwBqC,OAAS3B,EAAS2B,OAAU,KAAK,mBAQvGR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FE,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChEvD,EAAE,iBAAkB,sBAEvBoD,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFE,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZrD,EAAE,gBAAiB,yBAIxBwD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,YAAa,iBAElBwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,UAAW,cAEhBwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,UAAW,cAEhBwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,SAAU,aAEfwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,SAAU,aAEfwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,OAAQ,WAEbwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,SAAU,aAEfwD,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GvD,EAAE,UAAW,mBAIpBwD,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvFtB,EAASwB,IAAKkB,IACbvB,EAAAA,EAAAA,MAAA,MAAqBC,UAAU,0CAAyCE,SAAA,EACtEH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DoB,EAAQ3D,MAEXwC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDoB,EAAQpC,gBAGbiB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EoB,EAAQxC,aAEXqB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EoB,EAAQ1D,eAEXuC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,gFAA+EE,SAC1FX,EAAe+B,EAAQtD,WAE1B+B,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDoB,EAAQvC,SAEVuC,EAAQtC,WACPmB,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDoB,EAAQtC,eAIfmB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EoB,EAAQrC,QAEXkB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAeyB,EAAQpD,SAAUgC,SAC7FoB,EAAQpD,YAGb6B,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDE,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,qFAAoFE,SACnGvD,EAAE,OAAQ,WAEbwD,EAAAA,EAAAA,KAAA,UAAQH,UAAU,oFAAmFE,SAClGvD,EAAE,UAAW,kBAzCX2E,EAAQ3D,kBAiGZ,YAAdb,IACCiD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEvD,EAAE,iBAAkB,sBAEvBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CvD,EAAE,qBAAsB,8DAIhB,aAAdG,IACCiD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrEvD,EAAE,kBAAmB,uBAExBwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CvD,EAAE,sBAAuB,yE", "sources": ["pages/Billing/Billing.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst Billing = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('invoices');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [showNewInvoiceModal, setShowNewInvoiceModal] = useState(false);\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\n\n  // Mock data for invoices\n  const [invoices, setInvoices] = useState([\n    {\n      id: 'INV-2024-001',\n      patientName: '<PERSON>',\n      patientId: 'P001',\n      issueDate: '2024-02-15',\n      dueDate: '2024-03-15',\n      amount: 1500,\n      paidAmount: 1500,\n      status: 'Paid',\n      paymentMethod: 'Insurance',\n      insuranceProvider: 'Bupa Arabia',\n      services: [\n        { name: 'Physical Therapy Session', quantity: 5, unitPrice: 250, total: 1250 },\n        { name: 'Initial Assessment', quantity: 1, unitPrice: 250, total: 250 }\n      ],\n      notes: 'Lower back pain treatment',\n      therapist: 'Dr. Sarah Ahmed'\n    },\n    {\n      id: 'INV-2024-002',\n      patientName: 'Fatima Al-Zahra',\n      patientId: 'P002',\n      issueDate: '2024-02-14',\n      dueDate: '2024-03-14',\n      amount: 2000,\n      paidAmount: 400,\n      status: 'Partially Paid',\n      paymentMethod: 'Mixed',\n      insuranceProvider: 'Tawuniya',\n      services: [\n        { name: 'Occupational Therapy', quantity: 6, unitPrice: 300, total: 1800 },\n        { name: 'Special Needs Assessment', quantity: 1, unitPrice: 200, total: 200 }\n      ],\n      notes: 'Autism spectrum disorder support',\n      therapist: 'Dr. Mona Hassan'\n    },\n    {\n      id: 'INV-2024-003',\n      patientName: 'Mohammed Al-Otaibi',\n      patientId: 'P003',\n      issueDate: '2024-02-13',\n      dueDate: '2024-03-13',\n      amount: 1200,\n      paidAmount: 0,\n      status: 'Pending',\n      paymentMethod: 'Cash',\n      insuranceProvider: null,\n      services: [\n        { name: 'Speech Therapy', quantity: 4, unitPrice: 300, total: 1200 }\n      ],\n      notes: 'Speech delay treatment',\n      therapist: 'Dr. Layla Ibrahim'\n    }\n  ]);\n\n  // Mock data for payment history\n  const [payments, setPayments] = useState([\n    {\n      id: 'PAY-2024-001',\n      invoiceId: 'INV-2024-001',\n      patientName: 'Ahmed Al-Rashid',\n      amount: 1500,\n      method: 'Insurance',\n      provider: 'Bupa Arabia',\n      date: '2024-02-18',\n      status: 'Completed',\n      reference: 'BUPA-REF-12345',\n      processedBy: 'System'\n    },\n    {\n      id: 'PAY-2024-002',\n      invoiceId: 'INV-2024-002',\n      patientName: 'Fatima Al-Zahra',\n      amount: 400,\n      method: 'Cash',\n      provider: null,\n      date: '2024-02-16',\n      status: 'Completed',\n      reference: 'CASH-001',\n      processedBy: 'Receptionist'\n    },\n    {\n      id: 'PAY-2024-003',\n      invoiceId: 'INV-2024-002',\n      patientName: 'Fatima Al-Zahra',\n      amount: 1200,\n      method: 'Insurance',\n      provider: 'Tawuniya',\n      date: '2024-02-20',\n      status: 'Pending',\n      reference: 'TAW-PENDING-456',\n      processedBy: 'System'\n    }\n  ]);\n\n  const tabs = [\n    { id: 'invoices', label: t('invoices', 'Invoices'), icon: 'fas fa-file-invoice' },\n    { id: 'payments', label: t('payments', 'Payments'), icon: 'fas fa-credit-card' },\n    { id: 'reports', label: t('reports', 'Reports'), icon: 'fas fa-chart-bar' },\n    { id: 'settings', label: t('settings', 'Settings'), icon: 'fas fa-cog' }\n  ];\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'paid': return 'text-green-600 bg-green-100 dark:bg-green-900/30';\n      case 'partially paid': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';\n      case 'pending': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30';\n      case 'overdue': return 'text-red-600 bg-red-100 dark:bg-red-900/30';\n      case 'completed': return 'text-green-600 bg-green-100 dark:bg-green-900/30';\n      case 'failed': return 'text-red-600 bg-red-100 dark:bg-red-900/30';\n      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';\n    }\n  };\n\n  const handleGenerateInvoice = async (invoiceData) => {\n    setLoading(true);\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const newInvoice = {\n        id: `INV-2024-${String(invoices.length + 1).padStart(3, '0')}`,\n        ...invoiceData,\n        issueDate: new Date().toISOString().split('T')[0],\n        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],\n        paidAmount: 0,\n        status: 'Pending'\n      };\n      \n      setInvoices(prev => [newInvoice, ...prev]);\n      setShowNewInvoiceModal(false);\n      toast.success(t('invoiceGeneratedSuccessfully', 'Invoice generated successfully'));\n    } catch (error) {\n      toast.error(t('errorGeneratingInvoice', 'Error generating invoice'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderInvoicesTab = () => (\n    <div className=\"space-y-6\">\n      {/* Invoices Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-file-invoice text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalInvoices', 'Total Invoices')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {invoices.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('paidInvoices', 'Paid')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {invoices.filter(inv => inv.status === 'Paid').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('pendingInvoices', 'Pending')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {invoices.filter(inv => inv.status === 'Pending' || inv.status === 'Partially Paid').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-dollar-sign text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalAmount', 'Total Amount')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(invoices.reduce((sum, inv) => sum + inv.amount, 0))}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Invoices List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('invoices', 'Invoices')}\n          </h3>\n          <div className=\"flex items-center space-x-3\">\n            <input\n              type=\"text\"\n              placeholder={t('searchInvoices', 'Search invoices...')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            />\n            <button\n              onClick={() => setShowNewInvoiceModal(true)}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('newInvoice', 'New Invoice')}\n            </button>\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('invoiceId', 'Invoice ID')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('amount', 'Amount')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('paid', 'Paid')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('dueDate', 'Due Date')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {invoices.map((invoice) => (\n                <tr key={invoice.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {invoice.id}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {invoice.issueDate}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {invoice.patientName}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      ID: {invoice.patientId}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatCurrency(invoice.amount)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {formatCurrency(invoice.paidAmount)}\n                    </div>\n                    {invoice.amount > invoice.paidAmount && (\n                      <div className=\"text-sm text-red-600 dark:text-red-400\">\n                        {formatCurrency(invoice.amount - invoice.paidAmount)} {t('remaining', 'remaining')}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {invoice.dueDate}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(invoice.status)}`}>\n                      {invoice.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button\n                      onClick={() => setSelectedInvoice(invoice)}\n                      className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3\"\n                    >\n                      {t('view', 'View')}\n                    </button>\n                    <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3\">\n                      {t('print', 'Print')}\n                    </button>\n                    {invoice.status !== 'Paid' && (\n                      <button className=\"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300\">\n                        {t('recordPayment', 'Record Payment')}\n                      </button>\n                    )}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPaymentsTab = () => (\n    <div className=\"space-y-6\">\n      {/* Payments Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-credit-card text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalPayments', 'Total Payments')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {payments.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-dollar-sign text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalReceived', 'Total Received')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(payments.filter(p => p.status === 'Completed').reduce((sum, p) => sum + p.amount, 0))}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('pendingPayments', 'Pending')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {payments.filter(p => p.status === 'Pending').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-percentage text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('successRate', 'Success Rate')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {Math.round((payments.filter(p => p.status === 'Completed').length / payments.length) * 100)}%\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Payments List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('paymentHistory', 'Payment History')}\n          </h3>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('recordPayment', 'Record Payment')}\n          </button>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('paymentId', 'Payment ID')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('invoice', 'Invoice')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('amount', 'Amount')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('method', 'Method')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('date', 'Date')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {payments.map((payment) => (\n                <tr key={payment.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {payment.id}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {payment.reference}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {payment.invoiceId}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {payment.patientName}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                    {formatCurrency(payment.amount)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {payment.method}\n                    </div>\n                    {payment.provider && (\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {payment.provider}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {payment.date}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(payment.status)}`}>\n                      {payment.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3\">\n                      {t('view', 'View')}\n                    </button>\n                    <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\">\n                      {t('receipt', 'Receipt')}\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('billingPayments', 'Billing & Payments')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('billingDescription', 'Manage invoices, payments, and billing reports')}\n          </p>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportData', 'Export Data')}\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <i className={`${tab.icon} mr-2`}></i>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'invoices' && renderInvoicesTab()}\n      {activeTab === 'payments' && renderPaymentsTab()}\n      {activeTab === 'reports' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('billingReports', 'Billing Reports')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('reportsDescription', 'Comprehensive billing analytics and financial reports')}\n          </p>\n        </div>\n      )}\n      {activeTab === 'settings' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('billingSettings', 'Billing Settings')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('settingsDescription', 'Configure billing preferences, tax rates, and payment methods')}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Billing;\n"], "names": ["Billing", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "searchTerm", "setSearchTerm", "loading", "setLoading", "showNewInvoiceModal", "setShowNewInvoiceModal", "selectedInvoice", "setSelectedInvoice", "invoices", "setInvoices", "id", "patientName", "patientId", "issueDate", "dueDate", "amount", "paidAmount", "status", "paymentMethod", "insuranceProvider", "services", "name", "quantity", "unitPrice", "total", "notes", "therapist", "payments", "setPayments", "invoiceId", "method", "provider", "date", "reference", "processedBy", "tabs", "label", "icon", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "toLowerCase", "_jsxs", "className", "concat", "children", "_jsx", "map", "tab", "onClick", "length", "filter", "inv", "reduce", "sum", "type", "placeholder", "value", "onChange", "e", "target", "invoice", "p", "Math", "round", "payment"], "sourceRoot": ""}