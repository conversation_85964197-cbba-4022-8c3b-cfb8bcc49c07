{"version": 3, "file": "static/js/4444.e4541ef8.chunk.js", "mappings": "2MAIA,MAwjBA,EAxjBiBA,KACf,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,aACpCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAS,KACtCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAqBC,IAA0BN,EAAAA,EAAAA,WAAS,IACxDO,EAAiBC,IAAsBR,EAAAA,EAAAA,UAAS,OAGhDS,EAAmBC,IAAwBV,EAAAA,EAAAA,UAAS,CACzD,CACEW,GAAI,UACJC,KAAM,iCACNC,OAAQ,4JACRC,YAAa,qEACbC,cAAe,kMACfC,SAAU,mBACVC,SAAU,UACVC,cAAe,GACfC,gBAAiB,EACjBC,gBAAiB,GACjBC,MAAO,IACPC,gBAAiB,KACjBC,SAAU,GACVC,OAAQ,SACRC,SAAU,CACR,CAAEb,KAAM,qBAAsBc,SAAU,EAAGL,MAAO,KAClD,CAAET,KAAM,2BAA4Bc,SAAU,GAAIL,MAAO,KACzD,CAAET,KAAM,sBAAuBc,SAAU,EAAGL,MAAO,MAErDM,SAAU,CACR,mCACA,8BACA,oBACA,wBACA,2BAEFC,iBAAkB,CAAC,YAAa,kBAAmB,+BACnDC,iBAAkB,GAClBC,eAAgB,GAChBC,cAAe,KAEjB,CACEpB,GAAI,UACJC,KAAM,gCACNC,OAAQ,yKACRC,YAAa,iEACbC,cAAe,8RACfC,SAAU,gBACVC,SAAU,UACVC,cAAe,GACfC,gBAAiB,EACjBC,gBAAiB,GACjBC,MAAO,KACPC,gBAAiB,KACjBC,SAAU,GACVC,OAAQ,SACRC,SAAU,CACR,CAAEb,KAAM,2BAA4Bc,SAAU,EAAGL,MAAO,KACxD,CAAET,KAAM,uBAAwBc,SAAU,EAAGL,MAAO,KACpD,CAAET,KAAM,iBAAkBc,SAAU,EAAGL,MAAO,KAC9C,CAAET,KAAM,qBAAsBc,SAAU,EAAGL,MAAO,MAEpDM,SAAU,CACR,yCACA,8BACA,8BACA,qCACA,uCACA,+BAEFC,iBAAkB,CAAC,SAAU,iBAAkB,gBAAiB,QAChEC,iBAAkB,GAClBC,eAAgB,GAChBC,cAAe,KAEjB,CACEpB,GAAI,UACJC,KAAM,iCACNC,OAAQ,uHACRC,YAAa,4DACbC,cAAe,+KACfC,SAAU,iBACVC,SAAU,UACVC,cAAe,GACfC,gBAAiB,EACjBC,gBAAiB,GACjBC,MAAO,KACPC,gBAAiB,KACjBC,SAAU,GACVC,OAAQ,SACRC,SAAU,CACR,CAAEb,KAAM,sBAAuBc,SAAU,EAAGL,MAAO,KACnD,CAAET,KAAM,mBAAoBc,SAAU,GAAIL,MAAO,KACjD,CAAET,KAAM,eAAgBc,SAAU,EAAGL,MAAO,KAC5C,CAAET,KAAM,iBAAkBc,SAAU,EAAGL,MAAO,MAEhDM,SAAU,CACR,iCACA,wBACA,2BACA,4BACA,gCACA,wBAEFC,iBAAkB,CAAC,kBAAmB,eAAgB,uBACtDC,iBAAkB,GAClBC,eAAgB,GAChBC,cAAe,QAKZC,EAAoBC,IAAyBjC,EAAAA,EAAAA,UAAS,CAC3D,CACEW,GAAI,aACJuB,UAAW,UACXC,YAAa,iCACbC,YAAa,kBACbC,UAAW,OACXC,aAAc,aACdC,UAAW,aACXC,QAAS,aACThB,OAAQ,SACRiB,SAAU,GACVC,kBAAmB,EACnBxB,cAAe,GACfyB,kBAAmB,EACnBC,UAAW,kBACXC,YAAa,cAEf,CACElC,GAAI,aACJuB,UAAW,UACXC,YAAa,gCACbC,YAAa,kBACbC,UAAW,OACXC,aAAc,aACdC,UAAW,aACXC,QAAS,aACThB,OAAQ,SACRiB,SAAU,GACVC,kBAAmB,GACnBxB,cAAe,GACfyB,kBAAmB,GACnBC,UAAW,kBACXC,YAAa,cAEf,CACElC,GAAI,aACJuB,UAAW,UACXC,YAAa,iCACbC,YAAa,qBACbC,UAAW,OACXC,aAAc,aACdC,UAAW,aACXC,QAAS,aACThB,OAAQ,YACRiB,SAAU,IACVC,kBAAmB,GACnBxB,cAAe,GACfyB,kBAAmB,EACnBC,UAAW,kBACXC,YAAa,QAIXC,EAAO,CACX,CAAEnC,GAAI,WAAYoC,MAAOpD,EAAE,WAAY,YAAaqD,KAAM,cAC1D,CAAErC,GAAI,cAAeoC,MAAOpD,EAAE,cAAe,eAAgBqD,KAAM,qBACnE,CAAErC,GAAI,YAAaoC,MAAOpD,EAAE,YAAa,aAAcqD,KAAM,qBAC7D,CAAErC,GAAI,WAAYoC,MAAOpD,EAAE,WAAY,YAAaqD,KAAM,eAGtDC,EAAkBC,GACf,IAAIC,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,QACTC,OAAOL,GAGNM,EAAkBhC,IACtB,OAAQA,EAAOiC,eACb,IAAK,SAAU,MAAO,mDACtB,IAAK,YAAa,MAAO,gDACzB,IAAK,SAAU,MAAO,sDACtB,IAAK,YAAa,MAAO,6CAEzB,QAAS,MAAO,kDAwTpB,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAS,aAAAC,OAAehE,EAAQ,cAAgB,gBAAiBiE,SAAA,EAEpEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DlE,EAAE,oBAAqB,yBAE1BmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjDlE,EAAE,sBAAuB,qEAI9BmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8BAA6BE,UAC1CH,EAAAA,EAAAA,MAAA,UAAQC,UAAU,oFAAmFE,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,yBACZhE,EAAE,aAAc,wBAMvBmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gDAA+CE,UAC5DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBE,SACnCf,EAAKiB,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAMlE,EAAaiE,EAAIrD,IAChCgD,UAAS,4CAAAC,OACP9D,IAAckE,EAAIrD,GACd,mDACA,0HACHkD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKI,EAAIhB,KAAI,WACxBgB,EAAIjB,QATAiB,EAAIrD,SAgBF,aAAdb,IApUH4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChElE,EAAE,gBAAiB,qBAEtBmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DpD,EAAkByD,kBAM3BJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,+DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChElE,EAAE,oBAAqB,yBAE1BmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5D7B,EAAmBmC,OAAOC,GAAkB,WAAbA,EAAE5C,QAAqB0C,kBAM/DJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChElE,EAAE,oBAAqB,qBAE1B+D,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mDAAkDE,SAAA,CAC5DQ,KAAKC,MAAM7D,EAAkB8D,OAAO,CAACC,EAAKC,IAAQD,EAAMC,EAAI3C,eAAgB,GAAKrB,EAAkByD,QAAQ,gBAMpHJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChElE,EAAE,gBAAiB,iBAEtBmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,UAC3DpD,EAAkB8D,OAAO,CAACC,EAAKC,IAAQD,EAAMC,EAAI1C,cAAe,GAAKtB,EAAkByD,QAAQQ,QAAQ,iBAQnHhB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FE,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChElE,EAAE,oBAAqB,yBAE1B+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,SACEa,KAAK,OACLC,YAAajF,EAAE,iBAAkB,sBACjCkF,MAAO5E,EACP6E,SAAWC,GAAM7E,EAAc6E,EAAEC,OAAOH,OACxClB,UAAU,8HAEZD,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAAM3D,GAAuB,GACtCqD,UAAU,kFAAiFE,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZhE,EAAE,aAAc,yBAKvBmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,MAAKE,UAClBC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,SAClEpD,EAAkBsD,IAAKU,IACtBf,EAAAA,EAAAA,MAAA,OAAkBC,UAAU,+FAA8FE,SAAA,EACxHH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChEY,EAAI7D,QAEPkD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,2CAA0CE,SACpDY,EAAI5D,aAGTiD,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAeiB,EAAIjD,SAAUqC,SACzFY,EAAIjD,aAITsC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gDAA+CE,SACzDY,EAAI3D,eAGP4C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BE,SAAA,EAC3CH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCE,SAAA,CAAElE,EAAE,WAAY,YAAY,QAC9EmE,EAAAA,EAAAA,KAAA,QAAMH,UAAU,gCAA+BE,SAAEY,EAAIxD,eAEvDyC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BE,SAAA,EAC3CH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCE,SAAA,CAAElE,EAAE,WAAY,YAAY,QAC9EmE,EAAAA,EAAAA,KAAA,QAAMH,UAAU,gCAA+BE,SAAEY,EAAIvD,oBAEvDwC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+BAA8BE,SAAA,EAC3CH,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCE,SAAA,CAAElE,EAAE,WAAY,YAAY,QAC9EmE,EAAAA,EAAAA,KAAA,QAAMH,UAAU,gCAA+BE,SAAEY,EAAIzD,kBAIzD0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDE,SAAA,EACjEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,CACGY,EAAIlD,SAAW,IACduC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,wDAAuDE,SACpEZ,EAAewB,EAAIpD,UAGxByC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,uDAAsDE,SACnEZ,EAAewB,EAAInD,iBAAmBmD,EAAIpD,YAG9CoD,EAAIlD,SAAW,IACdmC,EAAAA,EAAAA,MAAA,QAAMC,UAAU,0GAAyGE,SAAA,CACtHY,EAAIlD,SAAS,KAAG5B,EAAE,MAAO,cAKhC+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kFAAiFE,SAAA,EAC9FH,EAAAA,EAAAA,MAAA,QAAAG,SAAA,CAAOY,EAAI5C,iBAAiB,IAAElC,EAAE,WAAY,gBAC5C+D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sCACbG,EAAAA,EAAAA,KAAA,QAAAD,SAAOY,EAAI1C,uBAIf2B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEG,QAASA,IAAMzD,EAAmBiE,GAClCd,UAAU,iGAAgGE,SAEzGlE,EAAE,cAAe,mBAEpBmE,EAAAA,EAAAA,KAAA,UAAQH,UAAU,sKAAqKE,SACpLlE,EAAE,SAAU,oBArEX8E,EAAI9D,eAsOP,gBAAdb,IApJHgE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,YAAWE,UAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FE,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChElE,EAAE,qBAAsB,0BAE3B+D,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFE,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZhE,EAAE,gBAAiB,yBAIxBmE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GlE,EAAE,UAAW,cAEhBmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GlE,EAAE,UAAW,cAEhBmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GlE,EAAE,WAAY,eAEjBmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GlE,EAAE,YAAa,gBAElBmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GlE,EAAE,cAAe,mBAEpBmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GlE,EAAE,SAAU,aAEfmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GlE,EAAE,UAAW,mBAIpBmE,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvF7B,EAAmB+B,IAAKkB,IACvBvB,EAAAA,EAAAA,MAAA,MAAwBC,UAAU,0CAAyCE,SAAA,EACzEH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DoB,EAAW7C,eAEdsB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CAAC,OACnDoB,EAAW5C,iBAGpBqB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDoB,EAAW9C,eAEduB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtDoB,EAAW1C,UAAU,MAAI0C,EAAWzC,eAGzCkB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0DAAyDE,UACtEC,EAAAA,EAAAA,KAAA,OACEH,UAAU,2DACVN,MAAO,CAAE6B,MAAM,GAADtB,OAAKqB,EAAWxC,SAAQ,WAG1CiB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wCAAuCE,SAAA,CACpDoB,EAAWxC,SAAS,WAGzBiB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CE,SAAA,CAC3DoB,EAAWvC,kBAAkB,IAAEuC,EAAW/D,cAAc,IAAEvB,EAAE,WAAY,mBAG7EmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EoB,EAAWrC,aAEdkB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EoB,EAAWpC,aAAelD,EAAE,YAAa,gBAE5CmE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAeyB,EAAWzD,SAAUqC,SAChGoB,EAAWzD,YAGhBkC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDE,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,qFAAoFE,SACnGlE,EAAE,OAAQ,UAEU,WAAtBsF,EAAWzD,SACVsC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,oFAAmFE,SAClGlE,EAAE,WAAY,mBAlDdsF,EAAWtE,iBA2Gf,cAAdb,IACC4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,mBAAoB,wBAEzBmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5ClE,EAAE,uBAAwB,iEAIlB,aAAdG,IACC4D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrElE,EAAE,kBAAmB,uBAExBmE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5ClE,EAAE,sBAAuB,6E", "sources": ["pages/Packages/Packages.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst Packages = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('packages');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [showNewPackageModal, setShowNewPackageModal] = useState(false);\n  const [selectedPackage, setSelectedPackage] = useState(null);\n\n  // Mock data for treatment packages\n  const [treatmentPackages, setTreatmentPackages] = useState([\n    {\n      id: 'PKG-001',\n      name: 'Basic Physical Therapy Package',\n      nameAr: 'باقة العلاج الطبيعي الأساسية',\n      description: 'Comprehensive physical therapy sessions for general rehabilitation',\n      descriptionAr: 'جلسات علاج طبيعي شاملة للتأهيل العام',\n      category: 'Physical Therapy',\n      duration: '4 weeks',\n      totalSessions: 12,\n      sessionsPerWeek: 3,\n      sessionDuration: 45,\n      price: 3000,\n      discountedPrice: 2700,\n      discount: 10,\n      status: 'Active',\n      services: [\n        { name: 'Initial Assessment', sessions: 1, price: 300 },\n        { name: 'Physical Therapy Session', sessions: 10, price: 250 },\n        { name: 'Progress Evaluation', sessions: 1, price: 200 }\n      ],\n      features: [\n        'Initial comprehensive assessment',\n        'Personalized treatment plan',\n        'Progress tracking',\n        'Home exercise program',\n        'Final evaluation report'\n      ],\n      targetConditions: ['Back pain', 'Joint stiffness', 'Post-surgery rehabilitation'],\n      assignedPatients: 45,\n      completionRate: 87,\n      averageRating: 4.6\n    },\n    {\n      id: 'PKG-002',\n      name: 'Special Needs Therapy Package',\n      nameAr: 'باقة علاج ذوي الاحتياجات الخاصة',\n      description: 'Specialized therapy for children and adults with special needs',\n      descriptionAr: 'علاج متخصص للأطفال والبالغين من ذوي الاحتياجات الخاصة',\n      category: 'Special Needs',\n      duration: '8 weeks',\n      totalSessions: 24,\n      sessionsPerWeek: 3,\n      sessionDuration: 60,\n      price: 7200,\n      discountedPrice: 6480,\n      discount: 10,\n      status: 'Active',\n      services: [\n        { name: 'Special Needs Assessment', sessions: 2, price: 400 },\n        { name: 'Occupational Therapy', sessions: 8, price: 350 },\n        { name: 'Speech Therapy', sessions: 8, price: 350 },\n        { name: 'Behavioral Support', sessions: 6, price: 300 }\n      ],\n      features: [\n        'Comprehensive special needs assessment',\n        'Multi-disciplinary approach',\n        'Family training and support',\n        'Adaptive equipment recommendations',\n        'Progress monitoring with visual aids',\n        'Sensory integration therapy'\n      ],\n      targetConditions: ['Autism', 'Cerebral Palsy', 'Down Syndrome', 'ADHD'],\n      assignedPatients: 23,\n      completionRate: 92,\n      averageRating: 4.8\n    },\n    {\n      id: 'PKG-003',\n      name: 'Premium Rehabilitation Package',\n      nameAr: 'باقة التأهيل المتميزة',\n      description: 'Intensive rehabilitation program with advanced techniques',\n      descriptionAr: 'برنامج تأهيل مكثف بتقنيات متقدمة',\n      category: 'Rehabilitation',\n      duration: '6 weeks',\n      totalSessions: 18,\n      sessionsPerWeek: 3,\n      sessionDuration: 60,\n      price: 5400,\n      discountedPrice: 4860,\n      discount: 10,\n      status: 'Active',\n      services: [\n        { name: 'Advanced Assessment', sessions: 1, price: 400 },\n        { name: 'Physical Therapy', sessions: 12, price: 300 },\n        { name: 'Hydrotherapy', sessions: 3, price: 400 },\n        { name: 'Electrotherapy', sessions: 2, price: 350 }\n      ],\n      features: [\n        'Advanced diagnostic assessment',\n        'Hydrotherapy sessions',\n        'Electrotherapy treatment',\n        'Manual therapy techniques',\n        'Personalized exercise program',\n        'Nutritional guidance'\n      ],\n      targetConditions: ['Sports injuries', 'Chronic pain', 'Post-operative care'],\n      assignedPatients: 31,\n      completionRate: 89,\n      averageRating: 4.7\n    }\n  ]);\n\n  // Mock data for package assignments\n  const [packageAssignments, setPackageAssignments] = useState([\n    {\n      id: 'ASSIGN-001',\n      packageId: 'PKG-001',\n      packageName: 'Basic Physical Therapy Package',\n      patientName: 'Ahmed Al-Rashid',\n      patientId: 'P001',\n      assignedDate: '2024-02-01',\n      startDate: '2024-02-05',\n      endDate: '2024-03-05',\n      status: 'Active',\n      progress: 75,\n      completedSessions: 9,\n      totalSessions: 12,\n      remainingSessions: 3,\n      therapist: 'Dr. Sarah Ahmed',\n      nextSession: '2024-02-20'\n    },\n    {\n      id: 'ASSIGN-002',\n      packageId: 'PKG-002',\n      packageName: 'Special Needs Therapy Package',\n      patientName: 'Fatima Al-Zahra',\n      patientId: 'P002',\n      assignedDate: '2024-01-15',\n      startDate: '2024-01-20',\n      endDate: '2024-03-20',\n      status: 'Active',\n      progress: 50,\n      completedSessions: 12,\n      totalSessions: 24,\n      remainingSessions: 12,\n      therapist: 'Dr. Mona Hassan',\n      nextSession: '2024-02-18'\n    },\n    {\n      id: 'ASSIGN-003',\n      packageId: 'PKG-001',\n      packageName: 'Basic Physical Therapy Package',\n      patientName: 'Mohammed Al-Otaibi',\n      patientId: 'P003',\n      assignedDate: '2024-01-10',\n      startDate: '2024-01-15',\n      endDate: '2024-02-15',\n      status: 'Completed',\n      progress: 100,\n      completedSessions: 12,\n      totalSessions: 12,\n      remainingSessions: 0,\n      therapist: 'Dr. Omar Khalil',\n      nextSession: null\n    }\n  ]);\n\n  const tabs = [\n    { id: 'packages', label: t('packages', 'Packages'), icon: 'fas fa-box' },\n    { id: 'assignments', label: t('assignments', 'Assignments'), icon: 'fas fa-user-check' },\n    { id: 'analytics', label: t('analytics', 'Analytics'), icon: 'fas fa-chart-line' },\n    { id: 'settings', label: t('settings', 'Settings'), icon: 'fas fa-cog' }\n  ];\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'active': return 'text-green-600 bg-green-100 dark:bg-green-900/30';\n      case 'completed': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';\n      case 'paused': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';\n      case 'cancelled': return 'text-red-600 bg-red-100 dark:bg-red-900/30';\n      case 'inactive': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';\n      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';\n    }\n  };\n\n  const handleCreatePackage = async (packageData) => {\n    setLoading(true);\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const newPackage = {\n        id: `PKG-${String(treatmentPackages.length + 1).padStart(3, '0')}`,\n        ...packageData,\n        assignedPatients: 0,\n        completionRate: 0,\n        averageRating: 0,\n        status: 'Active'\n      };\n      \n      setTreatmentPackages(prev => [newPackage, ...prev]);\n      setShowNewPackageModal(false);\n      toast.success(t('packageCreatedSuccessfully', 'Package created successfully'));\n    } catch (error) {\n      toast.error(t('errorCreatingPackage', 'Error creating package'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderPackagesTab = () => (\n    <div className=\"space-y-6\">\n      {/* Packages Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-box text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalPackages', 'Total Packages')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {treatmentPackages.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-users text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('activeAssignments', 'Active Assignments')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {packageAssignments.filter(a => a.status === 'Active').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-percentage text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('averageCompletion', 'Avg Completion')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {Math.round(treatmentPackages.reduce((sum, pkg) => sum + pkg.completionRate, 0) / treatmentPackages.length)}%\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-star text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('averageRating', 'Avg Rating')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {(treatmentPackages.reduce((sum, pkg) => sum + pkg.averageRating, 0) / treatmentPackages.length).toFixed(1)}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Packages Grid */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('treatmentPackages', 'Treatment Packages')}\n          </h3>\n          <div className=\"flex items-center space-x-3\">\n            <input\n              type=\"text\"\n              placeholder={t('searchPackages', 'Search packages...')}\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            />\n            <button\n              onClick={() => setShowNewPackageModal(true)}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('newPackage', 'New Package')}\n            </button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {treatmentPackages.map((pkg) => (\n              <div key={pkg.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div>\n                    <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      {pkg.name}\n                    </h4>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {pkg.nameAr}\n                    </p>\n                  </div>\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(pkg.status)}`}>\n                    {pkg.status}\n                  </span>\n                </div>\n\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  {pkg.description}\n                </p>\n\n                <div className=\"space-y-2 mb-4\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">{t('duration', 'Duration')}:</span>\n                    <span className=\"text-gray-900 dark:text-white\">{pkg.duration}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">{t('sessions', 'Sessions')}:</span>\n                    <span className=\"text-gray-900 dark:text-white\">{pkg.totalSessions}</span>\n                  </div>\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600 dark:text-gray-400\">{t('category', 'Category')}:</span>\n                    <span className=\"text-gray-900 dark:text-white\">{pkg.category}</span>\n                  </div>\n                </div>\n\n                <div className=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div>\n                      {pkg.discount > 0 && (\n                        <span className=\"text-sm text-gray-500 dark:text-gray-400 line-through\">\n                          {formatCurrency(pkg.price)}\n                        </span>\n                      )}\n                      <span className=\"text-lg font-bold text-gray-900 dark:text-white ml-2\">\n                        {formatCurrency(pkg.discountedPrice || pkg.price)}\n                      </span>\n                    </div>\n                    {pkg.discount > 0 && (\n                      <span className=\"px-2 py-1 text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full\">\n                        {pkg.discount}% {t('off', 'OFF')}\n                      </span>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                    <span>{pkg.assignedPatients} {t('patients', 'patients')}</span>\n                    <div className=\"flex items-center\">\n                      <i className=\"fas fa-star text-yellow-400 mr-1\"></i>\n                      <span>{pkg.averageRating}</span>\n                    </div>\n                  </div>\n\n                  <div className=\"flex space-x-2\">\n                    <button\n                      onClick={() => setSelectedPackage(pkg)}\n                      className=\"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors\"\n                    >\n                      {t('viewDetails', 'View Details')}\n                    </button>\n                    <button className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                      {t('assign', 'Assign')}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderAssignmentsTab = () => (\n    <div className=\"space-y-6\">\n      {/* Assignments List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('packageAssignments', 'Package Assignments')}\n          </h3>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('assignPackage', 'Assign Package')}\n          </button>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('package', 'Package')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('progress', 'Progress')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('therapist', 'Therapist')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('nextSession', 'Next Session')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {packageAssignments.map((assignment) => (\n                <tr key={assignment.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {assignment.patientName}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      ID: {assignment.patientId}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {assignment.packageName}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {assignment.startDate} - {assignment.endDate}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3\">\n                        <div\n                          className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                          style={{ width: `${assignment.progress}%` }}\n                        ></div>\n                      </div>\n                      <span className=\"text-sm text-gray-900 dark:text-white\">\n                        {assignment.progress}%\n                      </span>\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n                      {assignment.completedSessions}/{assignment.totalSessions} {t('sessions', 'sessions')}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {assignment.therapist}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {assignment.nextSession || t('completed', 'Completed')}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(assignment.status)}`}>\n                      {assignment.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3\">\n                      {t('view', 'View')}\n                    </button>\n                    {assignment.status === 'Active' && (\n                      <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\">\n                        {t('schedule', 'Schedule')}\n                      </button>\n                    )}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('treatmentPackages', 'Treatment Packages')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('packagesDescription', 'Manage treatment packages, pricing, and patient assignments')}\n          </p>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <button className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n            <i className=\"fas fa-download mr-2\"></i>\n            {t('exportData', 'Export Data')}\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <i className={`${tab.icon} mr-2`}></i>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'packages' && renderPackagesTab()}\n      {activeTab === 'assignments' && renderAssignmentsTab()}\n      {activeTab === 'analytics' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('packageAnalytics', 'Package Analytics')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('analyticsDescription', 'Comprehensive package performance analytics and insights')}\n          </p>\n        </div>\n      )}\n      {activeTab === 'settings' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('packageSettings', 'Package Settings')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('settingsDescription', 'Configure package templates, pricing rules, and discount policies')}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Packages;\n"], "names": ["Packages", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "searchTerm", "setSearchTerm", "loading", "setLoading", "showNewPackageModal", "setShowNewPackageModal", "selected<PERSON><PERSON><PERSON>", "setSelectedPackage", "treatmentPackages", "setTreatmentPackages", "id", "name", "nameAr", "description", "descriptionAr", "category", "duration", "totalSessions", "sessionsPerWeek", "sessionDuration", "price", "discountedPrice", "discount", "status", "services", "sessions", "features", "targetConditions", "assignedPatients", "completionRate", "averageRating", "packageAssignments", "setPackageAssignments", "packageId", "packageName", "patientName", "patientId", "assignedDate", "startDate", "endDate", "progress", "completedSessions", "remainingSessions", "therapist", "nextSession", "tabs", "label", "icon", "formatCurrency", "amount", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "toLowerCase", "_jsxs", "className", "concat", "children", "_jsx", "map", "tab", "onClick", "length", "filter", "a", "Math", "round", "reduce", "sum", "pkg", "toFixed", "type", "placeholder", "value", "onChange", "e", "target", "assignment", "width"], "sourceRoot": ""}