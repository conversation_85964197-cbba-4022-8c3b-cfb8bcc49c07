"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2441],{2441:(e,s,n)=>{n.r(s),n.d(s,{default:()=>d});n(5043);var a=n(3216),r=n(4120),t=n(579);const d=()=>{const{patientId:e,planOfCareId:s,reassessmentId:n}=(0,a.g)();return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,t.jsx)(r.A,{patientId:e,planOfCareId:s,reassessmentId:n})})}}}]);
//# sourceMappingURL=2441.52492c4c.chunk.js.map