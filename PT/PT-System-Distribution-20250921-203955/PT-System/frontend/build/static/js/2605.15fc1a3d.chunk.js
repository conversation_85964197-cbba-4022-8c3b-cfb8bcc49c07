"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2605],{2605:(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var o=s(5043),a=s(6305),r=s(579);const n=()=>{const[e,t]=(0,o.useState)(!1),[s,n]=(0,o.useState)(null);return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 p-6",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"\ud83e\uddea Patient Creation Test"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsx)("button",{onClick:()=>{localStorage.clear(),console.log("\ud83e\uddf9 localStorage cleared"),n({success:!0,message:"localStorage cleared! Please login again."})},className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"\ud83e\uddf9 Clear Storage"}),(0,r.jsx)("button",{onClick:async()=>{try{console.log("\ud83c\udf10 Testing network connectivity...");const e=await fetch("http://localhost:5001/api/v1/health");console.log("\ud83c\udfe5 Health check response:",e.status);const t=await fetch("http://localhost:5001/api/v1/auth/me",{method:"OPTIONS"});console.log("\u2708\ufe0f CORS preflight response:",t.status);const s=localStorage.getItem("pt_auth_token"),o=await fetch("http://localhost:5001/api/v1/auth/me",{headers:{Authorization:"Bearer ".concat(s),"Content-Type":"application/json"}});console.log("\ud83d\udd10 Authenticated request response:",o.status),console.log("\u2705 Network connectivity test completed!"),n({success:!0,data:{healthCheck:e.status,corsPreflght:t.status,authenticatedRequest:o.status},message:"Network connectivity test completed!"})}catch(e){console.error("\u274c Network test failed:",e),console.error("\u274c Network connectivity test failed!"),n({success:!1,error:e.message,details:{networkError:e.toString()}})}},className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:"\ud83c\udf10 Test Network"}),(0,r.jsx)("button",{onClick:async()=>{try{console.log("\ud83d\udd10 Testing authentication..."),console.log("\ud83d\udd17 API Base URL:","http://localhost:5001/api/v1");const e=localStorage.getItem("pt_auth_token");if(console.log("\ud83d\udd11 Token present:",!!e),console.log("\ud83d\udd11 Token length:",e?e.length:0),console.log("\ud83d\udd11 Token preview:",e?e.substring(0,50)+"...":"None"),e)try{const t=e.split(".");if(console.log("\ud83d\udd0d JWT parts count:",t.length),3===t.length){const e=JSON.parse(atob(t[1]));console.log("\ud83d\udd0d JWT payload:",e),console.log("\ud83d\udd0d JWT expires:",new Date(1e3*e.exp)),console.log("\ud83d\udd0d JWT expired:",Date.now()>1e3*e.exp)}}catch(s){console.error("\ud83d\udd0d JWT parsing error:",s)}const t=await a.mo.get("/auth/me");console.log("\ud83d\udc64 User info:",t),console.log("\u2705 Authentication test successful!"),n({success:!0,data:t,message:"Authentication successful!"})}catch(o){var e,t;console.error("\u274c Auth test failed:",o),console.error("\u274c Authentication test failed!"),n({success:!1,error:o.message,details:{response:null===(e=o.response)||void 0===e?void 0:e.data,status:null===(t=o.response)||void 0===t?void 0:t.status,request:o.request?"Request made but no response":"No request made"}})}},className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"\ud83d\udd10 Test Authentication"}),(0,r.jsx)("button",{onClick:async()=>{t(!0),n(null);try{console.log("\ud83e\uddea Testing Patient Creation...");const e=localStorage.getItem("pt_auth_token");console.log("\ud83d\udd11 Token available:",!!e),console.log("\ud83d\udd11 Token value:",e?e.substring(0,50)+"...":"None");const t={firstName:"Test",lastName:"Patient",nationalId:Math.random().toString().substring(2,12),dateOfBirth:"1990-01-01",gender:"male",phone:"+966501234567",email:"<EMAIL>",address:{street:"123 Test Street",city:"Riyadh",state:"Riyadh Province",zipCode:"12345",country:"Saudi Arabia"},emergencyContact:{name:"Emergency Contact",phone:"+966507654321",relationship:"friend"},insurance:{provider:"Test Insurance",policyNumber:"TEST123456",groupNumber:"GRP001"}};console.log("\ud83d\udce4 Sending test data:",t);const s=await a.mo.post("/patients",t);console.log("\ud83d\udce5 Response received:",s),n({success:!0,data:s,message:"Patient created successfully!"}),console.log("\u2705 Test patient created successfully!")}catch(s){console.error("\u274c Test failed:",s);let t="Unknown error",o={};var e;if(s.response)t=(null===(e=s.response.data)||void 0===e?void 0:e.message)||"HTTP ".concat(s.response.status),o={status:s.response.status,data:s.response.data,headers:s.response.headers};else s.request?(t="Network error - no response received",o={request:s.request,message:s.message}):(t=s.message,o={error:s.toString()});n({success:!1,error:t,details:o}),console.error("\u274c Test failed: ".concat(t))}finally{t(!1)}},disabled:e,className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50",children:e?"\u23f3 Testing...":"\ud83e\uddea Test Patient Creation"})]}),s&&(0,r.jsxs)("div",{className:"p-4 rounded-lg ".concat(s.success?"bg-green-100 dark:bg-green-900":"bg-red-100 dark:bg-red-900"),children:[(0,r.jsx)("h3",{className:"font-bold mb-2 ".concat(s.success?"text-green-800 dark:text-green-200":"text-red-800 dark:text-red-200"),children:s.success?"\u2705 Success":"\u274c Failed"}),(0,r.jsx)("p",{className:"mb-4 ".concat(s.success?"text-green-700 dark:text-green-300":"text-red-700 dark:text-red-300"),children:s.success?s.message:s.error}),(0,r.jsxs)("details",{className:"mt-4",children:[(0,r.jsx)("summary",{className:"cursor-pointer font-medium",children:"\ud83d\udccb Details"}),(0,r.jsx)("pre",{className:"mt-2 p-3 bg-gray-100 dark:bg-gray-700 rounded text-xs overflow-auto",children:JSON.stringify(s.success?s.data:s.details,null,2)})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-bold text-blue-800 dark:text-blue-200 mb-2",children:"\ud83d\udcdd Instructions"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside space-y-1 text-blue-700 dark:text-blue-300 text-sm",children:[(0,r.jsx)("li",{children:"First, test authentication to ensure you're logged in"}),(0,r.jsx)("li",{children:"Then test patient creation to see if the API call works"}),(0,r.jsx)("li",{children:"Check the browser console for detailed logs"}),(0,r.jsx)("li",{children:"If it fails, check the error details below"})]})]}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900 rounded-lg",children:[(0,r.jsx)("h3",{className:"font-bold text-yellow-800 dark:text-yellow-200 mb-2",children:"\ud83d\udd0d Debug Info"}),(0,r.jsxs)("div",{className:"text-yellow-700 dark:text-yellow-300 text-sm space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"API URL:"})," ","http://localhost:5001/api/v1"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Token Present:"})," ",localStorage.getItem("pt_auth_token")?"Yes":"No"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Environment:"})," ","production"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Mock API:"})," ","false"]})]})]})]})})})}}}]);
//# sourceMappingURL=2605.15fc1a3d.chunk.js.map