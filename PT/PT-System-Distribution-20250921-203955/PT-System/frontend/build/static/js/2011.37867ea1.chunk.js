"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2011],{2011:(e,a,r)=>{r.r(a),r.d(a,{default:()=>o});var t=r(2555),s=r(5043),i=r(7921),d=r(4528),n=r(3216),l=r(579);const o=()=>{const{t:e,isRTL:a}=(0,i.o)(),{user:r}=(0,d.A)(),{patientId:o}=(0,n.g)(),c=(0,n.Zp)(),[g,m]=(0,s.useState)({documentNumber:"QP-",issueDate:(new Date).toISOString().split("T")[0],version:"01",reviewNumber:"01",patientName:"",mrNumber:"",dischargeDate:"",diagnosis:"",admissionDate:"",physician:"",totalVisits:0,noShowCancellations:0,evaluationAddendum:[],evaluationOther:"",treatmentReceived:"",gait:"",balanceCoordination:"",pain:"",functionalAssessment:"",environmentalAssessment:"",activitiesLimitation:"",participateRestriction:"",familySupport:"",assistiveDevices:"",riskFactors:"",patientCaregiverTraining:"",dischargeReasons:[],dischargeOther:"",continueHEP:!1,continueHEPDetails:"",equipmentNeeds:"",followUpPhysician:!1,followUpPhysicianDetails:"",recommendationOther:"",followUpFrequency:"",followUpDate:"",followUpRiskFactors:"",planReviewedWithFamily:!1,therapistSignature:"",therapistBadgeNo:"",therapistDate:(new Date).toISOString().split("T")[0],comments:"",dischargePlanReviewed:!1,therapistSignature2:"",therapistBadgeNo2:"",therapistDate2:(new Date).toISOString().split("T")[0]}),[x,b]=(0,s.useState)(!1),[h,u]=(0,s.useState)({}),[y,p]=(0,s.useState)("patient-info");(0,s.useEffect)(()=>{o&&k()},[o]);const k=async()=>{try{b(!0);const e=await fetch("/api/patients/".concat(o));if(e.ok){const a=await e.json();m(e=>(0,t.A)((0,t.A)({},e),{},{patientName:a.name||"",mrNumber:a.mrNumber||"",physician:a.physician||"",diagnosis:a.diagnosis||""}))}}catch(e){console.error("Error loading patient data:",e)}finally{b(!1)}},v=(e,a)=>{m(r=>(0,t.A)((0,t.A)({},r),{},{[e]:a})),h[e]&&u(a=>(0,t.A)((0,t.A)({},a),{},{[e]:null}))},w=(e,a,r)=>{m(s=>(0,t.A)((0,t.A)({},s),{},{[e]:r?[...s[e],a]:s[e].filter(e=>e!==a)}))};return x?(0,l.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,l.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("outpatientDischargeAssessment","Outpatient Discharge Assessment")}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e("dischargeAssessmentDescription","Complete discharge evaluation and recommendations")})]}),(0,l.jsx)("div",{className:"flex space-x-3",children:(0,l.jsxs)("button",{onClick:async()=>{try{b(!0);const a=(0,t.A)((0,t.A)({},g),{},{generatedAt:(new Date).toISOString(),generatedBy:r.name||r.email,patientId:o}),s=await fetch("/api/v1/discharge-assessments/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(a)});if(!s.ok)throw new Error("HTTP error! status: ".concat(s.status));{const a=await s.blob(),r=window.URL.createObjectURL(a),t=document.createElement("a");t.href=r,t.download="discharge-assessment-".concat(g.patientName.replace(/\s+/g,"-"),"-").concat(g.dischargeDate,".pdf"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(r),document.body.removeChild(t),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(a){console.error("Error generating PDF:",a),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{b(!1)}},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-file-pdf mr-2"}),e("generatePDF","Generate PDF")]})})]})}),(0,l.jsx)("div",{className:"px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-gray-600 dark:text-gray-400 mb-1",children:e("documentNumber","Document Number")}),(0,l.jsx)("input",{type:"text",value:g.documentNumber,onChange:e=>v("documentNumber",e.target.value),className:"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-gray-600 dark:text-gray-400 mb-1",children:e("issueDate","Issue Date")}),(0,l.jsx)("input",{type:"date",value:g.issueDate,onChange:e=>v("issueDate",e.target.value),className:"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-gray-600 dark:text-gray-400 mb-1",children:e("version","Version")}),(0,l.jsx)("input",{type:"text",value:g.version,onChange:e=>v("version",e.target.value),className:"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-gray-600 dark:text-gray-400 mb-1",children:e("reviewNumber","Review Number")}),(0,l.jsx)("input",{type:"text",value:g.reviewNumber,onChange:e=>v("reviewNumber",e.target.value),className:"w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"})]})]})})]}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:(0,l.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,l.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"patient-info",label:e("patientInformation","Patient Information")},{id:"evaluation",label:e("evaluation","Evaluation")},{id:"progress",label:e("progress","Progress Summary")},{id:"discharge",label:e("discharge","Discharge & Recommendations")},{id:"signatures",label:e("signatures","Signatures")}].map(e=>(0,l.jsx)("button",{onClick:()=>p(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(y===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:e.label},e.id))})})}),(0,l.jsxs)("form",{onSubmit:async a=>{if(a.preventDefault(),(()=>{const a={};return g.patientName.trim()||(a.patientName=e("patientNameRequired","Patient name is required")),g.mrNumber.trim()||(a.mrNumber=e("mrNumberRequired","MR number is required")),g.dischargeDate||(a.dischargeDate=e("dischargeDateRequired","Discharge date is required")),g.diagnosis.trim()||(a.diagnosis=e("diagnosisRequired","Diagnosis is required")),g.admissionDate||(a.admissionDate=e("admissionDateRequired","Admission date is required")),g.physician.trim()||(a.physician=e("physicianRequired","Physician is required")),g.treatmentReceived.trim()||(a.treatmentReceived=e("treatmentReceivedRequired","Treatment received is required")),g.patientCaregiverTraining.trim()||(a.patientCaregiverTraining=e("trainingRequired","Patient/Caregiver training is required")),0===g.dischargeReasons.length&&(a.dischargeReasons=e("dischargeReasonRequired","At least one discharge reason is required")),g.therapistSignature.trim()||(a.therapistSignature=e("therapistSignatureRequired","Therapist signature is required")),g.therapistBadgeNo.trim()||(a.therapistBadgeNo=e("badgeNumberRequired","Badge number is required")),g.admissionDate&&g.dischargeDate&&new Date(g.admissionDate)>=new Date(g.dischargeDate)&&(a.dischargeDate=e("dischargeDateAfterAdmission","Discharge date must be after admission date")),g.followUpDate&&new Date(g.followUpDate)<=new Date&&(a.followUpDate=e("followUpDateFuture","Follow-up date must be in the future")),g.totalVisits<0&&(a.totalVisits=e("totalVisitsPositive","Total visits must be positive")),g.noShowCancellations<0&&(a.noShowCancellations=e("noShowPositive","No-show/cancellations must be positive")),u(a),0===Object.keys(a).length})())try{b(!0);const a=(0,t.A)((0,t.A)({},g),{},{submittedBy:r.id,submittedAt:(new Date).toISOString(),patientId:o}),s=await fetch("/api/v1/discharge-assessments/public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw new Error("Failed to save discharge assessment");await s.json();alert(e("dischargeAssessmentSaved","Discharge assessment saved successfully!")),c("/patients/".concat(o))}catch(s){console.error("Error saving discharge assessment:",s),alert(e("errorSaving","Error saving discharge assessment. Please try again."))}finally{b(!1)}},className:"space-y-6",children:["patient-info"===y&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("patientInformation","Patient Information")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("patientName","Patient Name")," *"]}),(0,l.jsx)("input",{type:"text",value:g.patientName,onChange:e=>v("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.patientName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.patientName&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.patientName})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("mrNumber","MR Number")," *"]}),(0,l.jsx)("input",{type:"text",value:g.mrNumber,onChange:e=>v("mrNumber",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.mrNumber?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.mrNumber&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.mrNumber})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("dischargeDate","Discharge Date")," *"]}),(0,l.jsx)("input",{type:"date",value:g.dischargeDate,onChange:e=>v("dischargeDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.dischargeDate?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.dischargeDate&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.dischargeDate})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("admissionDate","Admission Date")," *"]}),(0,l.jsx)("input",{type:"date",value:g.admissionDate,onChange:e=>v("admissionDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.admissionDate?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.admissionDate&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.admissionDate})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("physician","Physician")," *"]}),(0,l.jsx)("input",{type:"text",value:g.physician,onChange:e=>v("physician",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.physician?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.physician&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.physician})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("totalVisits","Number of Total Visits")," *"]}),(0,l.jsx)("input",{type:"number",min:"0",value:g.totalVisits,onChange:e=>v("totalVisits",parseInt(e.target.value)||0),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.totalVisits?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.totalVisits&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.totalVisits})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("noShowCancellations","Number of No-show/Cancellations")," *"]}),(0,l.jsx)("input",{type:"number",min:"0",value:g.noShowCancellations,onChange:e=>v("noShowCancellations",parseInt(e.target.value)||0),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.noShowCancellations?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.noShowCancellations&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.noShowCancellations})]})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("diagnosis","Diagnosis")," *"]}),(0,l.jsx)("textarea",{value:g.diagnosis,onChange:e=>v("diagnosis",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.diagnosis?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),h.diagnosis&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.diagnosis})]})]}),"evaluation"===y&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("evaluationAddendum","Evaluation Addendum")}),(0,l.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6",children:["Functional","LE","UE","Ankle/Foot","Cervical","Lumbar","Amputee","Hand","Voice","Cognitive","Visual/Perceptual","Wound","Lymphedema","Pulmonary","Communication","Dysphagia","Urinary","Incontinence","Other"].map(e=>(0,l.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,l.jsx)("input",{type:"checkbox",checked:g.evaluationAddendum.includes(e),onChange:a=>w("evaluationAddendum",e,a.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,l.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e})]},e))}),g.evaluationAddendum.includes("Other")&&(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("otherEvaluation","Other Evaluation Details")}),(0,l.jsx)("textarea",{value:g.evaluationOther,onChange:e=>v("evaluationOther",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterOtherDetails","Enter other evaluation details...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("treatmentReceived","Treatment Received")," *"]}),(0,l.jsx)("textarea",{value:g.treatmentReceived,onChange:e=>v("treatmentReceived",e.target.value),rows:4,className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.treatmentReceived?"border-red-500":"border-gray-300 dark:border-gray-600"),placeholder:e("describeTreatmentReceived","Describe the treatment received during the course of care..."),required:!0}),h.treatmentReceived&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.treatmentReceived})]})]}),"progress"===y&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("summaryOfProgress","Summary of Progress")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("gait","Gait")}),(0,l.jsx)("textarea",{value:g.gait,onChange:e=>v("gait",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeGaitProgress","Describe gait progress...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("balanceCoordination","Balance/Coordination")}),(0,l.jsx)("textarea",{value:g.balanceCoordination,onChange:e=>v("balanceCoordination",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeBalanceProgress","Describe balance/coordination progress...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("pain","PAIN")}),(0,l.jsx)("textarea",{value:g.pain,onChange:e=>v("pain",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describePainProgress","Describe pain management progress...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("functionalAssessment","Functional Assessment")}),(0,l.jsx)("textarea",{value:g.functionalAssessment,onChange:e=>v("functionalAssessment",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeFunctionalProgress","Describe functional assessment progress...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("environmentalAssessment","Environmental Assessment")}),(0,l.jsx)("textarea",{value:g.environmentalAssessment,onChange:e=>v("environmentalAssessment",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeEnvironmentalProgress","Describe environmental assessment...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("activitiesLimitation","Activities Limitation")}),(0,l.jsx)("textarea",{value:g.activitiesLimitation,onChange:e=>v("activitiesLimitation",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeActivitiesLimitation","Describe activities limitation...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("participateRestriction","Participate Restriction")}),(0,l.jsx)("textarea",{value:g.participateRestriction,onChange:e=>v("participateRestriction",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeParticipateRestriction","Describe participation restriction...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("familySupport","Family Support")}),(0,l.jsx)("textarea",{value:g.familySupport,onChange:e=>v("familySupport",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeFamilySupport","Describe family support...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("assistiveDevices","Assistive Devices")}),(0,l.jsx)("textarea",{value:g.assistiveDevices,onChange:e=>v("assistiveDevices",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeAssistiveDevices","Describe assistive devices used...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("riskFactors","Risk Factors")}),(0,l.jsx)("textarea",{value:g.riskFactors,onChange:e=>v("riskFactors",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeRiskFactors","Describe risk factors...")})]})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("patientCaregiverTraining","Patient/Caregiver Training")," *"]}),(0,l.jsx)("textarea",{value:g.patientCaregiverTraining,onChange:e=>v("patientCaregiverTraining",e.target.value),rows:4,className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.patientCaregiverTraining?"border-red-500":"border-gray-300 dark:border-gray-600"),placeholder:e("describeTraining","Describe patient and caregiver training provided..."),required:!0}),h.patientCaregiverTraining&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.patientCaregiverTraining})]})]}),"discharge"===y&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("dischargeAndRecommendations","Discharge & Recommendations")}),(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsxs)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:[e("reasonForDischarge","Reason for Discharge")," *"]}),(0,l.jsx)("div",{className:"space-y-3",children:["Goals Met","Medical Condition","Reached Maximal Potential","Non-Compliance","Objective findings inconsistent with patient's complaints and/or diagnosis","Other"].map(e=>(0,l.jsxs)("label",{className:"flex items-start space-x-3",children:[(0,l.jsx)("input",{type:"checkbox",checked:g.dischargeReasons.includes(e),onChange:a=>w("dischargeReasons",e,a.target.checked),className:"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,l.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e})]},e))}),g.dischargeReasons.includes("Other")&&(0,l.jsx)("div",{className:"mt-4",children:(0,l.jsx)("textarea",{value:g.dischargeOther,onChange:e=>v("dischargeOther",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterOtherDischargeReason","Enter other discharge reason...")})}),h.dischargeReasons&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-2",children:h.dischargeReasons})]}),(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("recommendations","Recommendations")}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"flex items-center space-x-3 mb-3",children:[(0,l.jsx)("input",{type:"checkbox",checked:g.continueHEP,onChange:e=>v("continueHEP",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e("continueWithHEP","Continue with HEP (Home Exercise Program) as issued")})]}),g.continueHEP&&(0,l.jsx)("textarea",{value:g.continueHEPDetails,onChange:e=>v("continueHEPDetails",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterHEPDetails","Enter HEP details and instructions...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("equipmentNeeds","Equipment Needs")}),(0,l.jsx)("textarea",{value:g.equipmentNeeds,onChange:e=>v("equipmentNeeds",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeEquipmentNeeds","Describe any equipment needs...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"flex items-center space-x-3 mb-3",children:[(0,l.jsx)("input",{type:"checkbox",checked:g.followUpPhysician,onChange:e=>v("followUpPhysician",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e("followUpWithPhysician","Follow up with physician")})]}),g.followUpPhysician&&(0,l.jsx)("textarea",{value:g.followUpPhysicianDetails,onChange:e=>v("followUpPhysicianDetails",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterFollowUpDetails","Enter follow-up details...")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("otherRecommendations","Other Recommendations")}),(0,l.jsx)("textarea",{value:g.recommendationOther,onChange:e=>v("recommendationOther",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterOtherRecommendations","Enter other recommendations...")})]})]})]}),(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("followUpInformation","Follow-up Information")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("followUpFrequency","How often patient needs follow-up?")}),(0,l.jsx)("input",{type:"text",value:g.followUpFrequency,onChange:e=>v("followUpFrequency",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterFrequency","e.g., Every 3 months")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("followUpDate","Date follow up appointment")}),(0,l.jsx)("input",{type:"date",value:g.followUpDate,onChange:e=>v("followUpDate",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.followUpDate?"border-red-500":"border-gray-300 dark:border-gray-600")}),h.followUpDate&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.followUpDate})]})]}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("followUpRiskFactors","Risk factors (affect the follow up)")}),(0,l.jsx)("textarea",{value:g.followUpRiskFactors,onChange:e=>v("followUpRiskFactors",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("describeFollowUpRiskFactors","Describe risk factors that may affect follow-up...")})]}),(0,l.jsx)("div",{className:"mt-4",children:(0,l.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,l.jsx)("input",{type:"checkbox",checked:g.planReviewedWithFamily,onChange:e=>v("planReviewedWithFamily",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,l.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e("planReviewedWithFamily","Plan of follow up reviewed with patient / family")})]})})]})]}),"signatures"===y&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("signaturesAndComments","Signatures and Comments")}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("therapistSignature","Therapist Signature")," *"]}),(0,l.jsx)("input",{type:"text",value:g.therapistSignature,onChange:e=>v("therapistSignature",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.therapistSignature?"border-red-500":"border-gray-300 dark:border-gray-600"),placeholder:e("enterTherapistName","Enter therapist name"),required:!0}),h.therapistSignature&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.therapistSignature})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("badgeNumber","Badge No.")," *"]}),(0,l.jsx)("input",{type:"text",value:g.therapistBadgeNo,onChange:e=>v("therapistBadgeNo",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(h.therapistBadgeNo?"border-red-500":"border-gray-300 dark:border-gray-600"),placeholder:e("enterBadgeNumber","Enter badge number"),required:!0}),h.therapistBadgeNo&&(0,l.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.therapistBadgeNo})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("date","Date")," *"]}),(0,l.jsx)("input",{type:"date",value:g.therapistDate,onChange:e=>v("therapistDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("comments","Comments")}),(0,l.jsx)("textarea",{value:g.comments,onChange:e=>v("comments",e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterComments","Enter any additional comments...")})]}),(0,l.jsx)("div",{children:(0,l.jsxs)("label",{className:"flex items-center space-x-3",children:[(0,l.jsx)("input",{type:"checkbox",checked:g.dischargePlanReviewed,onChange:e=>v("dischargePlanReviewed",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,l.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e("dischargePlanningReviewed","Discharge planning was reviewed with patient/family")})]})}),(0,l.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-600 pt-6",children:[(0,l.jsx)("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("confirmationSignature","Confirmation Signature (Page 2)")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("therapistSignatureTitle","Therapist Signature/Title")}),(0,l.jsx)("input",{type:"text",value:g.therapistSignature2,onChange:e=>v("therapistSignature2",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterTherapistNameTitle","Enter therapist name and title")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("badgeNumber","Badge No.")}),(0,l.jsx)("input",{type:"text",value:g.therapistBadgeNo2,onChange:e=>v("therapistBadgeNo2",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:e("enterBadgeNumber","Enter badge number")})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("date","Date")}),(0,l.jsx)("input",{type:"date",value:g.therapistDate2,onChange:e=>v("therapistDate2",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("button",{type:"button",onClick:()=>c(-1),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700",children:e("cancel","Cancel")}),(0,l.jsx)("button",{type:"submit",disabled:x,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:x?e("saving","Saving..."):e("saveAssessment","Save Assessment")})]})]})]})}}}]);
//# sourceMappingURL=2011.37867ea1.chunk.js.map