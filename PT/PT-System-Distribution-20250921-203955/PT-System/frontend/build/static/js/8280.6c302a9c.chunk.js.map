{"version": 3, "file": "static/js/8280.6c302a9c.chunk.js", "mappings": "0OAOA,MAwVA,EAxV4BA,KAC1B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,UAAEC,IAAcC,EAAAA,EAAAA,KAChBC,GAAWC,EAAAA,EAAAA,OACVC,EAASC,IAAcC,EAAAA,EAAAA,UAAS,OAChCC,EAAkBC,IAAuBF,EAAAA,EAAAA,UAAS,KAClDG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAChCK,EAAWC,IAAgBN,EAAAA,EAAAA,UAAS,YACpCO,EAAOC,IAAYR,EAAAA,EAAAA,UAAS,CACjCS,cAAe,EACfC,eAAgB,EAChBC,kBAAmB,EACnBC,cAAe,KAGjBC,EAAAA,EAAAA,WAAU,KACRC,IACAC,KACC,CAACrB,IAEJ,MAAMoB,EAAkBE,UACtB,GAAItB,EACF,IACE,MAAMuB,QAAoBC,EAAAA,EAAYC,WAAWzB,GACjDK,EAAWkB,EACb,CAAE,MAAOG,GACPC,QAAQD,MAAM,yBAA0BA,GACxCE,EAAAA,GAAMF,MAAM7B,EAAE,sBAAuB,8BACvC,CAEFa,GAAW,IAGPW,EAAuBC,UAC3B,GAAItB,EACF,IACE,MAAM6B,QAAiBL,EAAAA,EAAYM,oBAAoB9B,GACvDQ,EAAoBqB,GAGpB,MAAMb,EAAiBa,EAASE,OAAO,CAACC,EAAKC,KAAO,IAAAC,EAAA,OAAKF,IAAwB,QAAjBE,EAAAD,EAAQE,iBAAS,IAAAD,OAAA,EAAjBA,EAAmBE,SAAU,IAAI,GAC3FlB,EAAgBW,EAASO,OAAS,EACtCP,EAASE,OAAO,CAACC,EAAKC,IAAYD,GAAOC,EAAQI,YAAc,GAAI,GAAKR,EAASO,OAAS,EAE5FtB,EAAS,CACPC,cAAec,EAASO,OACxBpB,iBACAC,kBAAmBY,EAASS,OAAOC,GAAkB,cAAbA,EAAEC,QAAwBJ,OAClElB,cAAeA,EAAcuB,QAAQ,IAEzC,CAAE,MAAOf,GACPC,QAAQD,MAAM,mCAAoCA,EACpD,GAgBEgB,EAAsBL,IAC1B,OAAQA,GACN,KAAK,EAAG,MAAO,8BACf,KAAK,EAAG,MAAO,gCACf,KAAK,EAAG,MAAO,gCACf,KAAK,EAAG,MAAO,0BACf,KAAK,EAAG,MAAO,gCACf,QAAS,MAAO,8BAIdM,EAAsBN,IAC1B,OAAQA,GACN,KAAK,EAAG,OAAOxC,EAAE,WAAY,YAC7B,KAAK,EAAG,OAAOA,EAAE,OAAQ,QACzB,KAAK,EAAG,OAAOA,EAAE,eAAgB,gBACjC,KAAK,EAAG,OAAOA,EAAE,WAAY,YAC7B,KAAK,EAAG,OAAOA,EAAE,SAAU,UAC3B,QAAS,OAAOA,EAAE,UAAW,aAIjC,OAAIY,GAEAmC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iEAAgEC,SAAA,EAE7EF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oFAAmFC,UAChGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAM9C,GAAU,GACzB2C,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iCAEfE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,mDAAkDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yCACZhD,EAAE,kBAAmB,kCAEvBO,IACC2C,EAAAA,EAAAA,MAAA,KAAGF,UAAU,wCAAuCC,SAAA,CACjDjD,EAAE,UAAW,WAAW,KAAGC,EAAQM,EAAQ6C,OAAS7C,EAAQ8C,KAC5D9C,EAAQ+C,YACPP,EAAAA,EAAAA,KAAA,QAAMC,UAAU,+DAA8DC,SAC3EhD,EAAQM,EAAQgD,YAAchD,EAAQ+C,sBAOnDP,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8BAA6BC,UAC1CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2EAA0EC,SAAA,EACxFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBAAwB,QAC/BhD,EAAE,YAAa,iBAEvBkD,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2EAA0EC,SAAA,EACxFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZhD,EAAE,WAAY,8BAU7B+C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0EAAyEC,UACtFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpC,EAAa,WAC5BiC,UAAS,8DAAAQ,OACO,YAAd1C,EACI,oCACA,8EACHmC,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZhD,EAAE,kBAAmB,wBAExBkD,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpC,EAAa,WAC5BiC,UAAS,8DAAAQ,OACO,YAAd1C,EACI,oCACA,8EACHmC,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZhD,EAAE,gBAAiB,sBAEtBkD,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpC,EAAa,YAC5BiC,UAAS,8DAAAQ,OACO,aAAd1C,EACI,oCACA,8EACHmC,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZhD,EAAE,aAAc,eAAe,KAAGU,EAAiB6B,OAAO,eAMnEQ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8CAA6CC,UAC1DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,CAClD,YAAdnC,IACCoC,EAAAA,EAAAA,MAAAO,EAAAA,SAAA,CAAAR,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEjD,EAAE,kBAAmB,8BAExB+C,EAAAA,EAAAA,KAACW,EAAAA,EAAe,CACdvD,UAAWA,EACXwD,oBAAoB,OAKX,YAAd7C,IACCoC,EAAAA,EAAAA,MAAAO,EAAAA,SAAA,CAAAR,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEjD,EAAE,wBAAyB,8BAE9B+C,EAAAA,EAAAA,KAACa,EAAAA,EAAsB,CACrBzD,UAAWA,EACX0D,OA3JQpC,UACxB,UACQE,EAAAA,EAAYmC,qBAAmBC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CAAC,EAAIC,GAAW,IAAE7D,qBAClDqB,IACNO,EAAAA,GAAMkC,QAAQjE,EAAE,eAAgB,wCAChCe,EAAa,WACf,CAAE,MAAOc,GACPC,QAAQD,MAAM,wBAAyBA,GACvCE,EAAAA,GAAMF,MAAM7B,EAAE,cAAe,wBAC/B,QAuJyB,aAAdc,IACCoC,EAAAA,EAAAA,MAAAO,EAAAA,SAAA,CAAAR,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrEjD,EAAE,mBAAoB,uBAGxBU,EAAiB6B,OAAS,GACzBQ,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACvBvC,EAAiBwD,IAAI,CAAC9B,EAAS+B,KAAK,IAAAC,EAAA,OACnCrB,EAAAA,EAAAA,KAAA,OAEEC,UAAU,uHAAsHC,UAEhIC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oDAAmDC,SAC9Db,EAAQiB,QAEXN,EAAAA,EAAAA,KAAA,QAAMC,UAAS,kCAAAQ,OAAoCX,EAAmBT,EAAQI,aAAcS,SACzFH,EAAmBV,EAAQI,eAE9BU,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2DAA0DC,SAAA,EACtD,QAAjBmB,EAAAhC,EAAQE,iBAAS,IAAA8B,OAAA,EAAjBA,EAAmB7B,SAAU,EAAE,IAAEvC,EAAE,YAAa,oBAIrD+C,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDb,EAAQiC,eAGXnB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,MAAA,QAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yBACZhD,EAAE,WAAY,YAAY,KAAGoC,EAAQkC,SAAS,IAAEtE,EAAE,QAAS,aAE9DkD,EAAAA,EAAAA,MAAA,QAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,uBACZhD,EAAE,YAAa,aAAa,KAAGoC,EAAQmC,cAE1CrB,EAAAA,EAAAA,MAAA,QAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZhD,EAAE,UAAW,WAAW,KAAG,IAAIwE,KAAKpC,EAAQqC,WAAWC,+BAK9DxB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0FAAyFC,SAAA,EACzGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZhD,EAAE,QAAS,aAEdkD,EAAAA,EAAAA,MAAA,UAAQF,UAAU,0FAAyFC,SAAA,EACzGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZhD,EAAE,OAAQ,kBA5CZmE,QAoDXjB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iDACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnEjD,EAAE,aAAc,+BAEnB+C,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjDjD,EAAE,qBAAsB,wDAE3BkD,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMpC,EAAa,WAC5BiC,UAAU,sFAAqFC,SAAA,EAE/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZhD,EAAE,gBAAiB,iCAUlC+C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qDAAoDC,SAAA,EACjEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCACZhD,EAAE,gBAAiB,sBAGtBkD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDjD,EAAE,gBAAiB,qBAEtB+C,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDC,SAClEjC,EAAME,oBAIXgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDjD,EAAE,iBAAkB,sBAEvB+C,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sDAAqDC,SAClEjC,EAAMG,qBAIX+B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SACvDjD,EAAE,gBAAiB,qBAEtBkD,EAAAA,EAAAA,MAAA,QAAMF,UAAU,sDAAqDC,SAAA,CAClEjC,EAAMK,cAAc,2B", "sources": ["pages/Exercise/ExerciseLibraryPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport ExerciseLibrary, { ExerciseProgramBuilder } from '../../components/Exercise/ExerciseLibrary';\nimport dataService from '../../services/dataService';\nimport toast from 'react-hot-toast';\n\nconst ExerciseLibraryPage = () => {\n  const { t, isRTL } = useLanguage();\n  const { patientId } = useParams();\n  const navigate = useNavigate();\n  const [patient, setPatient] = useState(null);\n  const [exercisePrograms, setExercisePrograms] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [activeTab, setActiveTab] = useState('library');\n  const [stats, setStats] = useState({\n    totalPrograms: 0,\n    totalExercises: 0,\n    completedSessions: 0,\n    avgDifficulty: 0\n  });\n\n  useEffect(() => {\n    loadPatientData();\n    loadExercisePrograms();\n  }, [patientId]);\n\n  const loadPatientData = async () => {\n    if (patientId) {\n      try {\n        const patientData = await dataService.getPatient(patientId);\n        setPatient(patientData);\n      } catch (error) {\n        console.error('Error loading patient:', error);\n        toast.error(t('errorLoadingPatient', 'Error loading patient data'));\n      }\n    }\n    setLoading(false);\n  };\n\n  const loadExercisePrograms = async () => {\n    if (patientId) {\n      try {\n        const programs = await dataService.getExercisePrograms(patientId);\n        setExercisePrograms(programs);\n        \n        // Calculate stats\n        const totalExercises = programs.reduce((sum, program) => sum + (program.exercises?.length || 0), 0);\n        const avgDifficulty = programs.length > 0 ? \n          programs.reduce((sum, program) => sum + (program.difficulty || 1), 0) / programs.length : 0;\n        \n        setStats({\n          totalPrograms: programs.length,\n          totalExercises,\n          completedSessions: programs.filter(p => p.status === 'completed').length,\n          avgDifficulty: avgDifficulty.toFixed(1)\n        });\n      } catch (error) {\n        console.error('Error loading exercise programs:', error);\n      }\n    }\n  };\n\n  const handleProgramSave = async (programData) => {\n    try {\n      await dataService.saveExerciseProgram({ ...programData, patientId });\n      await loadExercisePrograms();\n      toast.success(t('programSaved', 'Exercise program saved successfully'));\n      setActiveTab('programs');\n    } catch (error) {\n      console.error('Error saving program:', error);\n      toast.error(t('errorSaving', 'Error saving program'));\n    }\n  };\n\n  const getDifficultyColor = (difficulty) => {\n    switch (difficulty) {\n      case 1: return 'bg-green-100 text-green-800';\n      case 2: return 'bg-yellow-100 text-yellow-800';\n      case 3: return 'bg-orange-100 text-orange-800';\n      case 4: return 'bg-red-100 text-red-800';\n      case 5: return 'bg-purple-100 text-purple-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getDifficultyLabel = (difficulty) => {\n    switch (difficulty) {\n      case 1: return t('beginner', 'Beginner');\n      case 2: return t('easy', 'Easy');\n      case 3: return t('intermediate', 'Intermediate');\n      case 4: return t('advanced', 'Advanced');\n      case 5: return t('expert', 'Expert');\n      default: return t('unknown', 'Unknown');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"exercise-library-page min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => navigate(-1)}\n                  className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  <i className=\"fas fa-arrow-left text-xl\"></i>\n                </button>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-dumbbell mr-3 text-purple-600\"></i>\n                    {t('exerciseLibrary', 'Exercise Library & Programs')}\n                  </h1>\n                  {patient && (\n                    <p className=\"text-gray-600 dark:text-gray-300 mt-1\">\n                      {t('patient', 'Patient')}: {isRTL ? patient.nameAr : patient.name}\n                      {patient.condition && (\n                        <span className=\"ml-2 text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded\">\n                          {isRTL ? patient.conditionAr : patient.condition}\n                        </span>\n                      )}\n                    </p>\n                  )}\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-video mr-1\"></i>\n                    500+ {t('exercises', 'Exercises')}\n                  </span>\n                  <span className=\"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-magic mr-1\"></i>\n                    {t('adaptive', 'Adaptive')}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <nav className=\"flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('library')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                activeTab === 'library'\n                  ? 'border-purple-500 text-purple-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-book mr-2\"></i>\n              {t('exerciseLibrary', 'Exercise Library')}\n            </button>\n            <button\n              onClick={() => setActiveTab('builder')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                activeTab === 'builder'\n                  ? 'border-purple-500 text-purple-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('createProgram', 'Create Program')}\n            </button>\n            <button\n              onClick={() => setActiveTab('programs')}\n              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                activeTab === 'programs'\n                  ? 'border-purple-500 text-purple-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-list mr-2\"></i>\n              {t('myPrograms', 'My Programs')} ({exercisePrograms.length})\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-8\">\n          {/* Main Content */}\n          <div className=\"xl:col-span-3\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              {activeTab === 'library' && (\n                <>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                    {t('browseExercises', 'Browse Exercise Library')}\n                  </h2>\n                  <ExerciseLibrary\n                    patientId={patientId}\n                    showProgramBuilder={false}\n                  />\n                </>\n              )}\n\n              {activeTab === 'builder' && (\n                <>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                    {t('createExerciseProgram', 'Create Exercise Program')}\n                  </h2>\n                  <ExerciseProgramBuilder\n                    patientId={patientId}\n                    onSave={handleProgramSave}\n                  />\n                </>\n              )}\n\n              {activeTab === 'programs' && (\n                <>\n                  <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n                    {t('exercisePrograms', 'Exercise Programs')}\n                  </h2>\n                  \n                  {exercisePrograms.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {exercisePrograms.map((program, index) => (\n                        <div\n                          key={index}\n                          className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n                        >\n                          <div className=\"flex items-start justify-between\">\n                            <div className=\"flex-1\">\n                              <div className=\"flex items-center space-x-3 mb-2\">\n                                <h3 className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                                  {program.name}\n                                </h3>\n                                <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(program.difficulty)}`}>\n                                  {getDifficultyLabel(program.difficulty)}\n                                </span>\n                                <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">\n                                  {program.exercises?.length || 0} {t('exercises', 'exercises')}\n                                </span>\n                              </div>\n                              \n                              <p className=\"text-gray-600 dark:text-gray-400 text-sm mb-3\">\n                                {program.description}\n                              </p>\n                              \n                              <div className=\"flex items-center space-x-4 text-sm text-gray-500\">\n                                <span>\n                                  <i className=\"fas fa-calendar mr-1\"></i>\n                                  {t('duration', 'Duration')}: {program.duration} {t('weeks', 'weeks')}\n                                </span>\n                                <span>\n                                  <i className=\"fas fa-repeat mr-1\"></i>\n                                  {t('frequency', 'Frequency')}: {program.frequency}\n                                </span>\n                                <span>\n                                  <i className=\"fas fa-clock mr-1\"></i>\n                                  {t('created', 'Created')}: {new Date(program.createdAt).toLocaleDateString()}\n                                </span>\n                              </div>\n                            </div>\n                            \n                            <div className=\"flex items-center space-x-2\">\n                              <button className=\"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\">\n                                <i className=\"fas fa-play mr-1\"></i>\n                                {t('start', 'Start')}\n                              </button>\n                              <button className=\"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors\">\n                                <i className=\"fas fa-edit mr-1\"></i>\n                                {t('edit', 'Edit')}\n                              </button>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-12\">\n                      <i className=\"fas fa-dumbbell text-6xl text-gray-300 mb-4\"></i>\n                      <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                        {t('noPrograms', 'No exercise programs yet')}\n                      </h3>\n                      <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n                        {t('createFirstProgram', 'Create your first exercise program to get started')}\n                      </p>\n                      <button\n                        onClick={() => setActiveTab('builder')}\n                        className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n                      >\n                        <i className=\"fas fa-plus mr-2\"></i>\n                        {t('createProgram', 'Create Program')}\n                      </button>\n                    </div>\n                  )}\n                </>\n              )}\n            </div>\n          </div>\n\n          {/* Stats Sidebar */}\n          <div className=\"xl:col-span-1\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                <i className=\"fas fa-chart-bar mr-2 text-gray-600\"></i>\n                {t('exerciseStats', 'Exercise Stats')}\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('totalPrograms', 'Total Programs')}\n                  </span>\n                  <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {stats.totalPrograms}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('totalExercises', 'Total Exercises')}\n                  </span>\n                  <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {stats.totalExercises}\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {t('avgDifficulty', 'Avg Difficulty')}\n                  </span>\n                  <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {stats.avgDifficulty}/5\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ExerciseLibraryPage;\n"], "names": ["ExerciseLibraryPage", "t", "isRTL", "useLanguage", "patientId", "useParams", "navigate", "useNavigate", "patient", "setPatient", "useState", "exercisePrograms", "setExercisePrograms", "loading", "setLoading", "activeTab", "setActiveTab", "stats", "setStats", "totalPrograms", "totalExercises", "completedSessions", "avg<PERSON><PERSON><PERSON><PERSON>y", "useEffect", "loadPatientData", "loadExercisePrograms", "async", "patientData", "dataService", "getPatient", "error", "console", "toast", "programs", "getExercisePrograms", "reduce", "sum", "program", "_program$exercises", "exercises", "length", "difficulty", "filter", "p", "status", "toFixed", "getDifficultyColor", "getDifficultyLabel", "_jsx", "className", "children", "_jsxs", "onClick", "nameAr", "name", "condition", "conditionAr", "concat", "_Fragment", "ExerciseLibrary", "showProgramBuilder", "ExerciseProgramBuilder", "onSave", "saveExerciseProgram", "_objectSpread", "programData", "success", "map", "index", "_program$exercises2", "description", "duration", "frequency", "Date", "createdAt", "toLocaleDateString"], "sourceRoot": ""}