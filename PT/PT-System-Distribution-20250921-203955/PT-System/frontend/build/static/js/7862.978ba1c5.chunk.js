"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[7862],{7862:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var s=a(5043),r=a(7921),l=a(579);const i=e=>{let{patientId:t=null}=e;const{t:a,isRTL:i}=(0,r.o)(),[n,d]=(0,s.useState)("all"),[c,o]=(0,s.useState)("all"),[x]=(0,s.useState)([{id:1,date:"2024-01-20",time:"10:00",type:"Physical Therapy",therapist:"Dr. <PERSON>",patient:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",patientEn:"<PERSON>",duration:60,status:"completed",notes:"Pat<PERSON> showed good progress in motor skills. Continued with balance exercises.",exercises:["Balance training - 15 minutes","Strength exercises - 20 minutes","Coordination activities - 25 minutes"],goals:["Improve balance and coordination","Increase muscle strength","Enhance motor planning"],progress:75,nextSession:"2024-01-22"},{id:2,date:"2024-01-18",time:"14:30",type:"Occupational Therapy",therapist:"Dr. Ahmed Al-Mansouri",patient:"\u0641\u0627\u0637\u0645\u0629 \u0639\u0644\u064a \u0627\u0644\u0633\u0627\u0644\u0645",patientEn:"Fatima Ali Al-Salem",duration:45,status:"completed",notes:"Focused on fine motor skills and daily living activities. Patient responded well.",exercises:["Fine motor activities - 20 minutes","Daily living skills - 15 minutes","Sensory integration - 10 minutes"],goals:["Improve fine motor control","Develop independence in daily activities","Enhance sensory processing"],progress:60,nextSession:"2024-01-21"},{id:3,date:"2024-01-16",time:"09:30",type:"Speech Therapy",therapist:"Dr. Fatima Al-Zahra",patient:"\u0645\u062d\u0645\u062f \u0639\u0628\u062f\u0627\u0644\u0644\u0647 \u0627\u0644\u062e\u0627\u0644\u062f",patientEn:"Mohammed Abdullah Al-Khalid",duration:30,status:"completed",notes:"Communication skills session. Patient made good progress with verbal expressions.",exercises:["Verbal communication - 15 minutes","Language comprehension - 10 minutes","Social interaction - 5 minutes"],goals:["Improve verbal communication","Enhance language understanding","Develop social communication skills"],progress:45,nextSession:"2024-01-19"},{id:4,date:"2024-01-15",time:"11:00",type:"Group Therapy",therapist:"Dr. Mohammed Al-Khalid",patient:"Multiple Patients",patientEn:"Multiple Patients",duration:90,status:"completed",notes:"Group session focusing on social skills and peer interaction.",exercises:["Group activities - 30 minutes","Social skills practice - 30 minutes","Collaborative exercises - 30 minutes"],goals:["Improve social interaction","Develop teamwork skills","Enhance peer communication"],progress:70,nextSession:"2024-01-22"}]),m=x.filter(e=>{const t="all"===n||"week"===n&&new Date(e.date)>=new Date(Date.now()-6048e5)||"month"===n&&new Date(e.date)>=new Date(Date.now()-2592e6),a="all"===c||e.type.toLowerCase().includes(c.toLowerCase());return t&&a}),g=e=>{switch(e){case"completed":return"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400";case"in_progress":return"text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400";case"cancelled":return"text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400";case"scheduled":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"}};return(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,l.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[(0,l.jsx)("i",{className:"fas fa-history text-blue-600 dark:text-blue-400 mr-2"}),a("treatmentHistory","Treatment History")]}),(0,l.jsxs)("div",{className:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4",children:[(0,l.jsxs)("select",{value:n,onChange:e=>d(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,l.jsx)("option",{value:"all",children:a("allTime","All Time")}),(0,l.jsx)("option",{value:"week",children:a("lastWeek","Last Week")}),(0,l.jsx)("option",{value:"month",children:a("lastMonth","Last Month")})]}),(0,l.jsxs)("select",{value:c,onChange:e=>o(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,l.jsx)("option",{value:"all",children:a("allTypes","All Types")}),(0,l.jsx)("option",{value:"physical",children:a("physicalTherapy","Physical Therapy")}),(0,l.jsx)("option",{value:"occupational",children:a("occupationalTherapy","Occupational Therapy")}),(0,l.jsx)("option",{value:"speech",children:a("speechTherapy","Speech Therapy")}),(0,l.jsx)("option",{value:"group",children:a("groupTherapy","Group Therapy")})]})]})]})}),(0,l.jsx)("div",{className:"p-6",children:m.length>0?(0,l.jsx)("div",{className:"space-y-6",children:m.map(e=>{return(0,l.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-md transition-shadow",children:[(0,l.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center",children:(0,l.jsx)("i",{className:"fas fa-user-md text-blue-600 dark:text-blue-400"})})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.type}),(0,l.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.date," at ",e.time," \u2022 ",e.duration," ",a("minutes","minutes")]})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3 mt-3 md:mt-0",children:[(0,l.jsx)("span",{className:"px-3 py-1 text-sm font-medium rounded-full ".concat(g(e.status)),children:a(e.status,e.status)}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[e.progress,"% ",a("progress","Progress")]}),(0,l.jsx)("div",{className:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1",children:(0,l.jsx)("div",{className:"h-2 rounded-full ".concat((t=e.progress,t>=80?"bg-green-500":t>=60?"bg-blue-500":t>=40?"bg-yellow-500":"bg-red-500")),style:{width:"".concat(e.progress,"%")}})})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:a("patientAndTherapist","Patient & Therapist")}),(0,l.jsxs)("div",{className:"space-y-1 text-sm text-gray-600 dark:text-gray-400",children:[(0,l.jsxs)("p",{children:[(0,l.jsxs)("strong",{children:[a("patient","Patient"),":"]})," ",i?e.patient:e.patientEn]}),(0,l.jsxs)("p",{children:[(0,l.jsxs)("strong",{children:[a("therapist","Therapist"),":"]})," ",e.therapist]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:a("exercises","Exercises")}),(0,l.jsx)("ul",{className:"space-y-1 text-sm text-gray-600 dark:text-gray-400",children:e.exercises.map((e,t)=>(0,l.jsxs)("li",{className:"flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-2 text-xs"}),e]},t))})]})]}),(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:a("treatmentGoals","Treatment Goals")}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:e.goals.map((e,t)=>(0,l.jsx)("span",{className:"px-3 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-sm rounded-full",children:e},t))})]}),e.notes&&(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:a("sessionNotes","Session Notes")}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg",children:e.notes})]}),e.nextSession&&(0,l.jsxs)("div",{className:"mt-4 flex items-center text-sm text-blue-600 dark:text-blue-400",children:[(0,l.jsx)("i",{className:"fas fa-calendar-plus mr-2"}),a("nextSession","Next session"),": ",e.nextSession]})]},e.id);var t})}):(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("i",{className:"fas fa-history text-4xl text-gray-400 mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:a("noTreatmentHistory","No Treatment History")}),(0,l.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:a("noTreatmentHistoryDesc","No treatment sessions found for the selected criteria")})]})})]})};var n=a(5475);const d=()=>{const{t:e,isRTL:t}=(0,r.o)(),[a,d]=(0,s.useState)("plans"),[c,o]=(0,s.useState)(""),[x,m]=(0,s.useState)("all"),[g]=(0,s.useState)([{id:1,patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",patientNameEn:"Ahmed Mohammed Al-Ahmed",condition:"autism",planType:"comprehensive",startDate:"2024-01-01",endDate:"2024-06-30",status:"active",progress:75,therapist:"Dr. Sarah Al-Rashid",sessionsCompleted:15,totalSessions:20,goals:["Improve social interaction","Develop communication skills","Reduce sensory sensitivities"]},{id:2,patientName:"\u0641\u0627\u0637\u0645\u0629 \u0639\u0644\u064a \u0627\u0644\u0633\u0627\u0644\u0645",patientNameEn:"Fatima Ali Al-Salem",condition:"cerebral_palsy",planType:"motor_skills",startDate:"2023-12-01",endDate:"2024-05-31",status:"active",progress:60,therapist:"Dr. Ahmed Al-Mansouri",sessionsCompleted:12,totalSessions:18,goals:["Improve gross motor skills","Enhance balance","Strengthen core muscles"]},{id:3,patientName:"\u0645\u062d\u0645\u062f \u0639\u0628\u062f\u0627\u0644\u0644\u0647 \u0627\u0644\u062e\u0627\u0644\u062f",patientNameEn:"Mohammed Abdullah Al-Khalid",condition:"down_syndrome",planType:"developmental",startDate:"2024-01-15",endDate:"2024-07-15",status:"active",progress:45,therapist:"Dr. Maryam Al-Zahra",sessionsCompleted:8,totalSessions:16,goals:["Cognitive development","Fine motor skills","Independence training"]},{id:4,patientName:"\u0646\u0648\u0631\u0627 \u0633\u0639\u062f \u0627\u0644\u063a\u0627\u0645\u062f\u064a",patientNameEn:"Nora Saad Al-Ghamdi",condition:"intellectual_disability",planType:"behavioral",startDate:"2023-11-01",endDate:"2024-04-30",status:"completed",progress:100,therapist:"Dr. Omar Al-Harbi",sessionsCompleted:20,totalSessions:20,goals:["Behavioral management","Social skills","Daily living skills"]}]),[h]=(0,s.useState)([{id:1,patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",patientNameEn:"Ahmed Mohammed Al-Ahmed",treatmentType:"Sensory Integration Therapy",nextSession:"2024-01-22 09:00",frequency:"Twice weekly",duration:"60 minutes",therapist:"Dr. Sarah Al-Rashid",status:"ongoing"},{id:2,patientName:"\u0641\u0627\u0637\u0645\u0629 \u0639\u0644\u064a \u0627\u0644\u0633\u0627\u0644\u0645",patientNameEn:"Fatima Ali Al-Salem",treatmentType:"Physical Therapy",nextSession:"2024-01-23 10:30",frequency:"Three times weekly",duration:"45 minutes",therapist:"Dr. Ahmed Al-Mansouri",status:"ongoing"},{id:3,patientName:"\u0645\u062d\u0645\u062f \u0639\u0628\u062f\u0627\u0644\u0644\u0647 \u0627\u0644\u062e\u0627\u0644\u062f",patientNameEn:"Mohammed Abdullah Al-Khalid",treatmentType:"Occupational Therapy",nextSession:"2024-01-24 14:00",frequency:"Weekly",duration:"30 minutes",therapist:"Dr. Maryam Al-Zahra",status:"ongoing"}]),p={autism:e("autism","Autism Spectrum Disorder"),cerebral_palsy:e("cerebralPalsy","Cerebral Palsy"),down_syndrome:e("downSyndrome","Down Syndrome"),intellectual_disability:e("intellectualDisability","Intellectual Disability"),spina_bifida:e("spinaBifida","Spina Bifida")},y={comprehensive:{label:e("comprehensive","Comprehensive"),color:"bg-blue-100 text-blue-800"},motor_skills:{label:e("motorSkills","Motor Skills"),color:"bg-green-100 text-green-800"},developmental:{label:e("developmental","Developmental"),color:"bg-purple-100 text-purple-800"},behavioral:{label:e("behavioral","Behavioral"),color:"bg-yellow-100 text-yellow-800"},sensory:{label:e("sensory","Sensory"),color:"bg-pink-100 text-pink-800"}},u={active:{label:e("active","Active"),color:"bg-green-100 text-green-800"},completed:{label:e("completed","Completed"),color:"bg-gray-100 text-gray-800"},paused:{label:e("paused","Paused"),color:"bg-yellow-100 text-yellow-800"},cancelled:{label:e("cancelled","Cancelled"),color:"bg-red-100 text-red-800"}},b=g.filter(e=>{const t=e.patientName.toLowerCase().includes(c.toLowerCase())||e.patientNameEn.toLowerCase().includes(c.toLowerCase()),a="all"===x||e.status===x;return t&&a}),j=[{id:"plans",label:e("treatmentPlans","Treatment Plans"),icon:"fas fa-clipboard-list"},{id:"active",label:e("activeTreatments","Active Treatments"),icon:"fas fa-heartbeat"},{id:"history",label:e("treatmentHistory","Treatment History"),icon:"fas fa-history"}];return(0,l.jsxs)("div",{className:"p-6 ".concat(t?"font-arabic":"font-english"),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("treatments","Treatments")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("treatmentsDesc","Manage treatment plans and therapy sessions")})]}),(0,l.jsx)("div",{className:"flex space-x-4",children:(0,l.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-plus mr-2"}),e("newTreatmentPlan","New Treatment Plan")]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:(0,l.jsx)("i",{className:"fas fa-clipboard-list text-blue-600 dark:text-blue-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalPlans","Total Plans")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.length})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-full",children:(0,l.jsx)("i",{className:"fas fa-heartbeat text-green-600 dark:text-green-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("activePlans","Active Plans")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.filter(e=>"active"===e.status).length})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full",children:(0,l.jsx)("i",{className:"fas fa-calendar-check text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("sessionsThisWeek","Sessions This Week")}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"12"})]})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:(0,l.jsx)("i",{className:"fas fa-chart-line text-purple-600 dark:text-purple-400 text-xl"})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("averageProgress","Average Progress")}),(0,l.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[Math.round(g.reduce((e,t)=>e+t.progress,0)/g.length),"%"]})]})]})})]}),(0,l.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,l.jsx)("nav",{className:"-mb-px flex space-x-8",children:j.map(e=>(0,l.jsxs)("button",{onClick:()=>d(e.id),className:"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(a===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,l.jsx)("i",{className:e.icon}),(0,l.jsx)("span",{children:e.label})]},e.id))})}),"plans"===a&&(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("search","Search")}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("input",{type:"text",value:c,onChange:e=>o(e.target.value),placeholder:e("searchTreatments","Search treatments..."),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,l.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("status","Status")}),(0,l.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,l.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,l.jsx)("option",{value:"active",children:e("active","Active")}),(0,l.jsx)("option",{value:"completed",children:e("completed","Completed")}),(0,l.jsx)("option",{value:"paused",children:e("paused","Paused")}),(0,l.jsx)("option",{value:"cancelled",children:e("cancelled","Cancelled")})]})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsxs)("button",{className:"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-filter mr-2"}),e("advancedFilters","Advanced Filters")]})})]})}),"plans"===a&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,l.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 p-6",children:b.map(a=>{return(0,l.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:t?a.patientName:a.patientNameEn}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(u[a.status].color),children:u[a.status].label})]}),(0,l.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("condition","Condition"),":"]}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:p[a.condition]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("planType","Plan Type"),":"]}),(0,l.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(y[a.planType].color),children:y[a.planType].label})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("therapist","Therapist"),":"]}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:a.therapist})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e("sessions","Sessions"),":"]}),(0,l.jsxs)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[a.sessionsCompleted,"/",a.totalSessions]})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:e("progress","Progress")}),(0,l.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[a.progress,"%"]})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,l.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat((s=a.progress,s>=80?"bg-green-500":s>=60?"bg-blue-500":s>=40?"bg-yellow-500":"bg-red-500")),style:{width:"".concat(a.progress,"%")}})})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("goals","Goals"),":"]}),(0,l.jsxs)("ul",{className:"space-y-1",children:[a.goals.slice(0,2).map((e,t)=>(0,l.jsxs)("li",{className:"text-sm text-gray-600 dark:text-gray-400 flex items-center",children:[(0,l.jsx)("i",{className:"fas fa-check-circle text-green-500 mr-2 text-xs"}),e]},t)),a.goals.length>2&&(0,l.jsxs)("li",{className:"text-sm text-gray-500 dark:text-gray-500",children:["+",a.goals.length-2," ",e("moreGoals","more goals")]})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(n.N_,{to:"/treatments/".concat(a.id),className:"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors text-center",children:e("viewDetails","View Details")}),(0,l.jsx)("button",{className:"px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:(0,l.jsx)("i",{className:"fas fa-edit"})})]})]},a.id);var s})}),0===b.length&&(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("i",{className:"fas fa-clipboard-list text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noTreatmentPlans","No treatment plans found")}),(0,l.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("createFirstPlan","Create your first treatment plan to get started")})]})]}),"active"===a&&(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("treatmentType","Treatment Type")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("nextSession","Next Session")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("frequency","Frequency")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("therapist","Therapist")}),(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,l.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:h.map(e=>(0,l.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t?e.patientName:e.patientNameEn})}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:e.treatmentType}),(0,l.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.duration})]}),(0,l.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,l.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:new Date(e.nextSession).toLocaleDateString()}),(0,l.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(e.nextSession).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:e.frequency}),(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:e.therapist}),(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("button",{className:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300",children:(0,l.jsx)("i",{className:"fas fa-eye"})}),(0,l.jsx)("button",{className:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300",children:(0,l.jsx)("i",{className:"fas fa-edit"})}),(0,l.jsx)("button",{className:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300",children:(0,l.jsx)("i",{className:"fas fa-pause"})})]})})]},e.id))})]})}),0===h.length&&(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("i",{className:"fas fa-heartbeat text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noActiveTreatments","No active treatments")}),(0,l.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("startTreatmentPrompt","Start a treatment plan to see active treatments here")})]})]}),"history"===a&&(0,l.jsx)(i,{})]})}}}]);
//# sourceMappingURL=7862.978ba1c5.chunk.js.map