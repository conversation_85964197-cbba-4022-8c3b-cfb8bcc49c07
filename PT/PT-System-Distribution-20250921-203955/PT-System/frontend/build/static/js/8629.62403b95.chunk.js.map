{"version": 3, "file": "static/js/8629.62403b95.chunk.js", "mappings": "mMAIA,MAiUA,EAjUwBA,KACtB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACfC,GAAWC,EAAAA,EAAAA,OACVC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,KACtCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,QAC1CG,EAAYC,IAAiBJ,EAAAA,EAAAA,UAAS,UAGtCK,IAAgBL,EAAAA,EAAAA,UAAS,CAC9B,CACEM,GAAI,EACJC,YAAa,yFACbC,cAAe,0BACfC,KAAM,aACNC,KAAM,QACNC,SAAU,GACVC,KAAM,mBACNC,UAAW,sBACXC,SAAU,SACVC,OAAQ,YACRC,MAAO,2BAET,CACEV,GAAI,EACJC,YAAa,yFACbC,cAAe,sBACfC,KAAM,aACNC,KAAM,QACNC,SAAU,GACVC,KAAM,uBACNC,UAAW,wBACXC,SAAU,SACVC,OAAQ,UACRC,MAAO,sBAET,CACEV,GAAI,EACJC,YAAa,2GACbC,cAAe,8BACfC,KAAM,aACNC,KAAM,QACNC,SAAU,GACVC,KAAM,iBACNC,UAAW,sBACXC,SAAU,SACVC,OAAQ,YACRC,MAAO,gCAET,CACEV,GAAI,EACJC,YAAa,+FACbC,cAAe,yBACfC,KAAM,aACNC,KAAM,QACNC,SAAU,GACVC,KAAM,gBACNC,UAAW,yBACXC,SAAU,YACVC,OAAQ,YACRC,MAAO,qCAKLC,EAAuBZ,EAAaa,OAAOC,IAAgB,IAADC,EAC9D,MAAMC,EAAgBF,EAAYZ,YAAYe,cAAcC,SAASzB,EAAWwB,iBAClC,QADgDF,EACzED,EAAYX,qBAAa,IAAAY,OAAA,EAAzBA,EAA2BE,cAAcC,SAASzB,EAAWwB,iBAC7DH,EAAYN,UAAUS,cAAcC,SAASzB,EAAWwB,gBACxDH,EAAYP,KAAKU,cAAcC,SAASzB,EAAWwB,eAElEE,EAAiC,QAAjBvB,GAA0BkB,EAAYJ,SAAWd,EAEjEwB,GAAQ,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC5CC,EAAkBV,EAAYV,KAMpC,OAAOY,GAAiBG,IALW,QAAfrB,GACe,UAAfA,GAA0B0B,IAAoBJ,GAC/B,aAAftB,GAA6B0B,GAAmBJ,GACjC,SAAftB,GAAyB0B,EAAkBJ,KAK3DK,EAAkBf,IACtB,OAAQA,GACN,IAAK,YACH,MAAO,uEACT,IAAK,UACH,MAAO,2EACT,IAAK,YACH,MAAO,mEACT,IAAK,YACH,MAAO,+DACT,QACE,MAAO,qEAIPgB,EAAenB,IACnB,OAAQA,EAAKU,eACX,IAAK,mBACH,MAAO,iBACT,IAAK,uBACH,MAAO,oBACT,IAAK,iBACH,MAAO,kBACT,IAAK,gBACH,MAAO,eACT,QACE,MAAO,uBAIb,OACEU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DzC,EAAE,kBAAmB,uBAExB0C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDzC,EAAE,sBAAuB,0CAI9BuC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMxC,EAAS,0BACxBqC,UAAU,sFAAqFC,SAAA,EAE/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZxC,EAAE,eAAgB,qBAErBuC,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMxC,EAAS,qBACxBqC,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZxC,EAAE,iBAAkB,+BAO7B0C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mGAAkGC,UAC/GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EzC,EAAE,SAAU,aAEfuC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEvB,KAAK,OACLyB,MAAOvC,EACPwC,SAAWC,GAAMxC,EAAcwC,EAAEC,OAAOH,OACxCI,YAAahD,EAAE,qBAAsB,0BACrCwC,UAAU,2KAEZE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DAIjBD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EzC,EAAE,SAAU,aAEfuC,EAAAA,EAAAA,MAAA,UACEK,MAAOpC,EACPqC,SAAWC,GAAMrC,EAAgBqC,EAAEC,OAAOH,OAC1CJ,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQE,MAAM,MAAKH,SAAEzC,EAAE,cAAe,mBACtC0C,EAAAA,EAAAA,KAAA,UAAQE,MAAM,YAAWH,SAAEzC,EAAE,YAAa,gBAC1C0C,EAAAA,EAAAA,KAAA,UAAQE,MAAM,UAASH,SAAEzC,EAAE,UAAW,cACtC0C,EAAAA,EAAAA,KAAA,UAAQE,MAAM,YAAWH,SAAEzC,EAAE,YAAa,gBAC1C0C,EAAAA,EAAAA,KAAA,UAAQE,MAAM,YAAWH,SAAEzC,EAAE,YAAa,sBAI9CuC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EzC,EAAE,OAAQ,WAEbuC,EAAAA,EAAAA,MAAA,UACEK,MAAOlC,EACPmC,SAAWC,GAAMnC,EAAcmC,EAAEC,OAAOH,OACxCJ,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQE,MAAM,MAAKH,SAAEzC,EAAE,WAAY,gBACnC0C,EAAAA,EAAAA,KAAA,UAAQE,MAAM,QAAOH,SAAEzC,EAAE,QAAS,YAClC0C,EAAAA,EAAAA,KAAA,UAAQE,MAAM,WAAUH,SAAEzC,EAAE,WAAY,eACxC0C,EAAAA,EAAAA,KAAA,UAAQE,MAAM,OAAMH,SAAEzC,EAAE,OAAQ,iBAIpC0C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASA,KACPrC,EAAc,IACdG,EAAgB,OAChBE,EAAc,UAEhB6B,UAAU,yFAAwFC,SAAA,EAElGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBACZxC,EAAE,QAAS,oBAOpBuC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,CAChEzC,EAAE,eAAgB,gBAAgB,KAAGwB,EAAqByB,OAAO,UAItEP,EAAAA,EAAAA,KAAA,OAAKF,UAAU,MAAKC,SACjBjB,EAAqByB,OAAS,GAC7BP,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBjB,EAAqB0B,IAAKxB,IACzBa,EAAAA,EAAAA,MAAA,OAEEC,UAAU,+FAA8FC,SAAA,EAExGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wFAAuFC,UACpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAW,OAAKb,EAAYZ,EAAYP,MAAK,0CAEhDoB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChExC,EAAQyB,EAAYZ,YAAcY,EAAYX,iBAEjDwB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,CACpDf,EAAYP,KAAK,WAAIO,EAAYN,oBAKxCmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAW,OAAgDd,EAAeX,EAAYJ,SAAUmB,SACjGzC,EAAE0B,EAAYJ,OAAQI,EAAYJ,WAErCiB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/Df,EAAYV,QAEfuB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDf,EAAYT,KAAK,KAAGS,EAAYR,SAAS,IAAElB,EAAE,MAAO,OAAO,iBAMpEuC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAAEzC,EAAE,WAAY,YAAY,QAC9E0C,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qCAAoCC,SAAEf,EAAYL,eAEpEkB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAAEzC,EAAE,WAAY,YAAY,QAC9EuC,EAAAA,EAAAA,MAAA,QAAMC,UAAU,qCAAoCC,SAAA,CAAEf,EAAYR,SAAS,IAAElB,EAAE,UAAW,kBAE5FuC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAAEzC,EAAE,QAAS,SAAS,QACxE0C,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qCAAoCC,SAAEf,EAAYH,eAItEgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kCAAiCC,SAAA,EAC9CF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uFAAsFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZxC,EAAE,OAAQ,YAEbuC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,yFAAwFC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZxC,EAAE,OAAQ,WAEW,YAAvB0B,EAAYJ,SACXiB,EAAAA,EAAAA,MAAA,UAAQC,UAAU,2FAA0FC,SAAA,EAC1GC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZxC,EAAE,UAAW,eAGlBuC,EAAAA,EAAAA,MAAA,UAAQC,UAAU,qFAAoFC,SAAA,EACpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZxC,EAAE,SAAU,kBAjEZ0B,EAAYb,QAwEvB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEzC,EAAE,sBAAuB,4BAE5B0C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDzC,EAAE,qBAAsB,iDAE3BuC,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMxC,EAAS,qBACxBqC,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZxC,EAAE,sBAAuB,sC", "sources": ["pages/Appointments/AppointmentList.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst AppointmentList = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [filterDate, setFilterDate] = useState('today');\n\n  // Mock appointment data\n  const [appointments] = useState([\n    {\n      id: 1,\n      patientName: 'أحمد محمد الأحمد',\n      patientNameEn: '<PERSON>',\n      date: '2024-01-25',\n      time: '10:00',\n      duration: 60,\n      type: 'Physical Therapy',\n      therapist: 'Dr. <PERSON>',\n      location: 'Room 1',\n      status: 'confirmed',\n      notes: 'Regular therapy session'\n    },\n    {\n      id: 2,\n      patientName: 'فاطمة علي السالم',\n      patientNameEn: '<PERSON>ima <PERSON>',\n      date: '2024-01-25',\n      time: '11:30',\n      duration: 45,\n      type: 'Occupational Therapy',\n      therapist: 'Dr. <PERSON>',\n      location: 'Room 2',\n      status: 'pending',\n      notes: 'Assessment session'\n    },\n    {\n      id: 3,\n      patientName: 'محمد عبدالله الخالد',\n      patientNameEn: 'Mohammed Abdullah Al-Khalid',\n      date: '2024-01-25',\n      time: '14:00',\n      duration: 30,\n      type: 'Speech Therapy',\n      therapist: 'Dr. Fatima Al-Zahra',\n      location: 'Room 3',\n      status: 'completed',\n      notes: 'Communication skills session'\n    },\n    {\n      id: 4,\n      patientName: 'سارة أحمد المطيري',\n      patientNameEn: 'Sarah Ahmed Al-Mutairi',\n      date: '2024-01-26',\n      time: '09:00',\n      duration: 90,\n      type: 'Group Therapy',\n      therapist: 'Dr. Mohammed Al-Khalid',\n      location: 'Gymnasium',\n      status: 'confirmed',\n      notes: 'Group session for social skills'\n    }\n  ]);\n\n  // Filter appointments based on search and filters\n  const filteredAppointments = appointments.filter(appointment => {\n    const matchesSearch = appointment.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         appointment.patientNameEn?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         appointment.therapist.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         appointment.type.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesStatus = filterStatus === 'all' || appointment.status === filterStatus;\n    \n    const today = new Date().toISOString().split('T')[0];\n    const appointmentDate = appointment.date;\n    const matchesDate = filterDate === 'all' || \n                       (filterDate === 'today' && appointmentDate === today) ||\n                       (filterDate === 'upcoming' && appointmentDate >= today) ||\n                       (filterDate === 'past' && appointmentDate < today);\n    \n    return matchesSearch && matchesStatus && matchesDate;\n  });\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'confirmed':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'completed':\n        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400';\n      case 'cancelled':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const getTypeIcon = (type) => {\n    switch (type.toLowerCase()) {\n      case 'physical therapy':\n        return 'fas fa-running';\n      case 'occupational therapy':\n        return 'fas fa-hand-paper';\n      case 'speech therapy':\n        return 'fas fa-comments';\n      case 'group therapy':\n        return 'fas fa-users';\n      default:\n        return 'fas fa-stethoscope';\n    }\n  };\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {t('appointmentList', 'Appointment List')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n              {t('appointmentListDesc', 'View and manage all appointments')}\n            </p>\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={() => navigate('/appointments/calendar')}\n              className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n            >\n              <i className=\"fas fa-calendar mr-2\"></i>\n              {t('calendarView', 'Calendar View')}\n            </button>\n            <button\n              onClick={() => navigate('/appointments/new')}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('newAppointment', 'New Appointment')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('search', 'Search')}\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                placeholder={t('searchAppointments', 'Search appointments...')}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              />\n              <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('status', 'Status')}\n            </label>\n            <select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n              <option value=\"confirmed\">{t('confirmed', 'Confirmed')}</option>\n              <option value=\"pending\">{t('pending', 'Pending')}</option>\n              <option value=\"completed\">{t('completed', 'Completed')}</option>\n              <option value=\"cancelled\">{t('cancelled', 'Cancelled')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('date', 'Date')}\n            </label>\n            <select\n              value={filterDate}\n              onChange={(e) => setFilterDate(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"all\">{t('allDates', 'All Dates')}</option>\n              <option value=\"today\">{t('today', 'Today')}</option>\n              <option value=\"upcoming\">{t('upcoming', 'Upcoming')}</option>\n              <option value=\"past\">{t('past', 'Past')}</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button\n              onClick={() => {\n                setSearchTerm('');\n                setFilterStatus('all');\n                setFilterDate('today');\n              }}\n              className=\"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n            >\n              <i className=\"fas fa-refresh mr-2\"></i>\n              {t('reset', 'Reset')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Appointments List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {t('appointments', 'Appointments')} ({filteredAppointments.length})\n          </h2>\n        </div>\n        \n        <div className=\"p-6\">\n          {filteredAppointments.length > 0 ? (\n            <div className=\"space-y-4\">\n              {filteredAppointments.map((appointment) => (\n                <div\n                  key={appointment.id}\n                  className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-md transition-shadow\"\n                >\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center\">\n                        <i className={`${getTypeIcon(appointment.type)} text-blue-600 dark:text-blue-400`}></i>\n                      </div>\n                      <div>\n                        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                          {isRTL ? appointment.patientName : appointment.patientNameEn}\n                        </h3>\n                        <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {appointment.type} • {appointment.therapist}\n                        </p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(appointment.status)}`}>\n                        {t(appointment.status, appointment.status)}\n                      </span>\n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {appointment.date}\n                        </div>\n                        <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                          {appointment.time} ({appointment.duration} {t('min', 'min')})\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">{t('location', 'Location')}:</span>\n                      <span className=\"ml-2 text-gray-900 dark:text-white\">{appointment.location}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">{t('duration', 'Duration')}:</span>\n                      <span className=\"ml-2 text-gray-900 dark:text-white\">{appointment.duration} {t('minutes', 'minutes')}</span>\n                    </div>\n                    <div>\n                      <span className=\"text-gray-600 dark:text-gray-400\">{t('notes', 'Notes')}:</span>\n                      <span className=\"ml-2 text-gray-900 dark:text-white\">{appointment.notes}</span>\n                    </div>\n                  </div>\n                  \n                  <div className=\"mt-4 flex justify-end space-x-2\">\n                    <button className=\"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors\">\n                      <i className=\"fas fa-eye mr-1\"></i>\n                      {t('view', 'View')}\n                    </button>\n                    <button className=\"px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition-colors\">\n                      <i className=\"fas fa-edit mr-1\"></i>\n                      {t('edit', 'Edit')}\n                    </button>\n                    {appointment.status === 'pending' && (\n                      <button className=\"px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition-colors\">\n                        <i className=\"fas fa-check mr-1\"></i>\n                        {t('confirm', 'Confirm')}\n                      </button>\n                    )}\n                    <button className=\"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors\">\n                      <i className=\"fas fa-times mr-1\"></i>\n                      {t('cancel', 'Cancel')}\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <i className=\"fas fa-calendar-times text-4xl text-gray-400 mb-4\"></i>\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                {t('noAppointmentsFound', 'No Appointments Found')}\n              </h3>\n              <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n                {t('noAppointmentsDesc', 'No appointments match your current filters')}\n              </p>\n              <button\n                onClick={() => navigate('/appointments/new')}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                <i className=\"fas fa-plus mr-2\"></i>\n                {t('scheduleAppointment', 'Schedule Appointment')}\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AppointmentList;\n"], "names": ["AppointmentList", "t", "isRTL", "useLanguage", "navigate", "useNavigate", "searchTerm", "setSearchTerm", "useState", "filterStatus", "setFilterStatus", "filterDate", "setFilterDate", "appointments", "id", "patientName", "patientNameEn", "date", "time", "duration", "type", "therapist", "location", "status", "notes", "filteredAppointments", "filter", "appointment", "_appointment$patientN", "matchesSearch", "toLowerCase", "includes", "matchesStatus", "today", "Date", "toISOString", "split", "appointmentDate", "getStatusColor", "getTypeIcon", "_jsxs", "className", "children", "_jsx", "onClick", "value", "onChange", "e", "target", "placeholder", "length", "map", "concat"], "sourceRoot": ""}