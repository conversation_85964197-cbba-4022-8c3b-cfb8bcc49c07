{"version": 3, "file": "static/js/3095.61e14377.chunk.js", "mappings": "iOAKA,MAkgBA,EAlgBgCA,KAC9B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,MAGVC,EAAaC,KAFHC,EAAAA,EAAAA,OAEqBC,EAAAA,EAAAA,UAAS,MACxCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,OAC5BK,EAASC,IAAcN,EAAAA,EAAAA,UAAS,CACrCO,OAAQ,GACRC,UAAW,GACXC,gBAAiB,GACjBC,UAAW,GACXC,QAAS,MAEJC,EAAYC,IAAiBb,EAAAA,EAAAA,UAAS,CAC3Cc,YAAa,EACbC,WAAY,EACZC,MAAO,EACPC,MAAO,MAGTC,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACd,EAASO,EAAWE,cAExB,MAAMK,EAAkBC,UACtB,IACElB,GAAW,GACXE,EAAS,MAETiB,QAAQC,IAAI,oCAGZ,MAAMC,EAAeC,OAAOC,YAC1BD,OAAOE,QAAQrB,GAASsB,OAAOC,IAAA,IAAEC,EAAGC,GAAMF,EAAA,MAAe,KAAVE,KAG3CC,EAAc,IAAIC,iBAAeC,EAAAA,EAAAA,GAAC,CACtCC,KAAMtB,EAAWE,YAAYqB,WAC7BlB,MAAOL,EAAWK,MAAMkB,YACrBZ,IAGLF,QAAQC,IAAI,WAAW,wCAADc,OAA0CL,IAEhE,MAAMM,QAAiBC,MAAM,wCAADF,OAAyCL,GAAe,CAClFQ,SAAON,EAAAA,EAAAA,GAAA,CACL,eAAgB,oBAEZO,aAAaC,QAAQ,UAAY,CACnC,cAAgB,UAADL,OAAYI,aAAaC,QAAQ,cAOtD,GAFApB,QAAQC,IAAI,mBAAoBe,EAASK,SAErCL,EAASM,GAcN,CACL,MAAMC,QAAkBP,EAASQ,OAAOC,MAAM,MAAS,IAEvD,MADAzB,QAAQC,IAAI,kBAAmBsB,GACzB,IAAIG,MAAMH,EAAUI,SAAO,QAAAZ,OAAYC,EAASK,OAAM,MAAAN,OAAKC,EAASY,YAC5E,CAlBiB,CACf,MAAMC,QAAab,EAASQ,OAG5B,GAFAxB,QAAQC,IAAI,iBAAkB4B,IAET,IAAjBA,EAAKC,QAQP,MAAM,IAAIJ,MAAMG,EAAKF,SAAW,wCAPhClD,EAAeoD,EAAKrD,aAAe,IACnCgB,EAAcuC,IAAInB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbmB,GAAI,IACPrC,WAAYmC,EAAKnC,YAAc,EAC/BC,MAAOkC,EAAKlC,OAAS,IAK3B,CAKF,CAAE,MAAOb,GACPkB,QAAQlB,MAAM,uCAAwCA,GACtDC,EAASD,EAAM6C,SACflD,EAAe,GACjB,CAAC,QACCI,GAAW,EACb,GAGImD,EAAqBA,CAACC,EAAOxB,KACjCxB,EAAW8C,IAAInB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACVmB,GAAI,IACP,CAACE,GAAQxB,KAEXjB,EAAcuC,IAAInB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUmB,GAAI,IAAEtC,YAAa,MAG3CyC,EAAoBrB,IACxBrB,EAAcuC,IAAInB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUmB,GAAI,IAAEtC,YAAaoB,MAkE3CsB,EAA2BC,IAC/B,IAAKA,GAA8B,IAAnBA,EAAQC,OAAc,OAAO,KAE7C,MAAMC,EAAgBF,EAAQ,GAS9B,OACEG,EAAAA,EAAAA,MAAA,QAAMC,UAAS,8CAAAzB,OATI,CACnB,YAAa,8BACb,oBAAqB,0BACrB,4BAA6B,4BAC7B,iBAAkB,gCAClB,MAAS,6BAImEuB,IAAkB,6BAA8BG,SAAA,CACzHH,EACAF,EAAQC,OAAS,GAAC,KAAAtB,OAASqB,EAAQC,OAAS,OAKnD,OAAIzD,GAEA8D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qEAKjB1D,GAEA4D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8CAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mCAAkCC,SAC7CtE,EAAE,0BAA2B,0CAEhCuE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4BAA2BC,UACxCC,EAAAA,EAAAA,KAAA,KAAAD,SAAI3D,OAEN4D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,UACEC,QAAS7C,EACT0C,UAAU,oFAAmFC,SAE5FtE,EAAE,WAAY,0BAU3BoE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kGAAiGC,SAAA,EAC9GC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DtE,EAAE,uBAAwB,4BAE7BuE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDtE,EAAE,6BAA8B,uDAGrCoE,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,kCACHL,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrE,EAAE,yBAA0B,qCAMnCuE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtE,EAAE,SAAU,aAEfuE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLrC,MAAOzB,EAAQE,OACf6D,SAAWC,GAAMhB,EAAmB,SAAUgB,EAAEC,OAAOxC,OACvDyC,YAAa/E,EAAE,iBAAkB,sBACjCqE,UAAU,gJAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtE,EAAE,YAAa,gBAElBuE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLrC,MAAOzB,EAAQG,UACf4D,SAAWC,GAAMhB,EAAmB,YAAagB,EAAEC,OAAOxC,OAC1DyC,YAAa/E,EAAE,oBAAqB,0BACpCqE,UAAU,gJAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtE,EAAE,kBAAmB,uBAExBoE,EAAAA,EAAAA,MAAA,UACE9B,MAAOzB,EAAQI,gBACf2D,SAAWC,GAAMhB,EAAmB,kBAAmBgB,EAAEC,OAAOxC,OAChE+B,UAAU,0IAAyIC,SAAA,EAEnJC,EAAAA,EAAAA,KAAA,UAAQjC,MAAM,GAAEgC,SAAEtE,EAAE,aAAc,kBAClCuE,EAAAA,EAAAA,KAAA,UAAQjC,MAAM,YAAWgC,SAAEtE,EAAE,WAAY,gBACzCuE,EAAAA,EAAAA,KAAA,UAAQjC,MAAM,oBAAmBgC,SAAEtE,EAAE,mBAAoB,wBACzDuE,EAAAA,EAAAA,KAAA,UAAQjC,MAAM,4BAA2BgC,SAAEtE,EAAE,0BAA2B,gCACxEuE,EAAAA,EAAAA,KAAA,UAAQjC,MAAM,iBAAgBgC,SAAEtE,EAAE,gBAAiB,qBACnDuE,EAAAA,EAAAA,KAAA,UAAQjC,MAAM,QAAOgC,SAAEtE,EAAE,QAAS,kBAItCoE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtE,EAAE,YAAa,iBAElBuE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLrC,MAAOzB,EAAQK,UACf0D,SAAWC,GAAMhB,EAAmB,YAAagB,EAAEC,OAAOxC,OAC1D+B,UAAU,gJAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EtE,EAAE,UAAW,eAEhBuE,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLrC,MAAOzB,EAAQM,QACfyD,SAAWC,GAAMhB,EAAmB,UAAWgB,EAAEC,OAAOxC,OACxD+B,UAAU,wJAQpBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mGAAkGC,UAC/GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDtE,EAAE,iBAAkB,oDAAqD,CACxEgF,OAAQ5D,EAAWE,YAAc,GAAKF,EAAWK,MAAQ,EACzDwD,IAAKC,KAAKC,IAAI/D,EAAWE,YAAcF,EAAWK,MAAOL,EAAWI,OACpEA,MAAOJ,EAAWI,WAGtB4C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,2CAA0CC,SACxDtE,EAAE,eAAgB,sBAErBoE,EAAAA,EAAAA,MAAA,UACE9B,MAAOlB,EAAWK,MAClBmD,SAAWC,GAAMxD,EAAcuC,IAAInB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUmB,GAAI,IAAEnC,MAAO2D,SAASP,EAAEC,OAAOxC,OAAQhB,YAAa,KACjG+C,UAAU,gIAA+HC,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,UAAQjC,MAAO,GAAGgC,SAAC,QACnBC,EAAAA,EAAAA,KAAA,UAAQjC,MAAO,GAAGgC,SAAC,QACnBC,EAAAA,EAAAA,KAAA,UAAQjC,MAAO,GAAGgC,SAAC,mBAO3BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6GAA4GC,SAAA,EACzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,2DAA0DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtE,EAAE,UAAW,cAEhBuE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtE,EAAE,gBAAiB,qBAEtBuE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtE,EAAE,YAAa,gBAElBuE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtE,EAAE,kBAAmB,uBAExBuE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtE,EAAE,SAAU,aAEfuE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GtE,EAAE,SAAU,aAEfuE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qGAAoGC,SAC/GtE,EAAE,UAAW,mBAIpBuE,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFjE,EAAYgF,IAAKC,IAChBlB,SAAAA,EAAAA,MAAA,MAAyBC,UAAU,0CAAyCC,SAAA,EAC1EC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DgB,EAAWC,eAEdnB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CAAC,OACnDgB,EAAWE,kBAItBjB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9E,IAAImB,KAAKH,EAAWI,eAAeC,wBAEtCpB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9EgB,EAAWM,sBAEdrB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,SACxCN,EAAwBsB,EAAWO,qBAEtCtB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,UAC/EF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,CAAMgB,EAAWQ,YAAY,aAC7B1B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,CACnCgB,EAAWS,oBAAoB,qBAItCxB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UA9PnCpB,EA+PUoC,EAAWpC,QAvPzCqB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAzB,OAPI,CACnBoD,MAAO,gCACPC,UAAW,8BACXC,SAAU,6BAIkEhD,IAAW,6BAA8BoB,SAClHtE,EAAEkD,EAAQA,SAwPDqB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6DAA4DC,UACxEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0CAAyCC,SAAA,EACtDC,EAAAA,EAAAA,KAACE,EAAAA,GAAI,CACHC,GAAE,+BAAA9B,OAAiC0C,EAAWa,KAC9C9B,UAAU,gFACV+B,MAAOpG,EAAE,OAAQ,QAAQsE,UAEzBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kBAEfE,EAAAA,EAAAA,KAACE,EAAAA,GAAI,CACHC,GAAE,+BAAA9B,OAAiC0C,EAAWa,IAAG,SACjD9B,UAAU,oFACV+B,MAAOpG,EAAE,OAAQ,QAAQsE,UAEzBC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mBAEfE,EAAAA,EAAAA,KAAA,UACEC,QAASA,IA7SX5C,WAClB,IACE,MAAMiB,QAAiBC,MAAM,oCAAqC,CAChEuD,OAAQ,OACRtD,QAAS,CACP,eAAgB,oBAElBuD,KAAMC,KAAKC,UAAUlB,KAGvB,GAAIzC,EAASM,GAAI,CACf,MAAMsD,QAAa5D,EAAS4D,OACtBC,EAAMC,OAAOC,IAAIC,gBAAgBJ,GACjCK,EAAIC,SAASC,cAAc,KACjCF,EAAEG,KAAOP,EACTI,EAAEI,SAAQ,wBAAAtE,OAA2B0C,EAAWC,YAAW,KAAA3C,OAAI0C,EAAWI,cAAa,QACvFqB,SAAST,KAAKa,YAAYL,GAC1BA,EAAEM,QACFT,OAAOC,IAAIS,gBAAgBX,GAC3BK,SAAST,KAAKgB,YAAYR,EAC5B,CACF,CAAE,MAAOnG,GACPkB,QAAQlB,MAAM,yBAA0BA,GACxC4G,MAAMvH,EAAE,sBAAuB,4CACjC,GAqRmCwH,CAAYlC,GAC3BjB,UAAU,wFACV+B,MAAOpG,EAAE,cAAe,gBAAgBsE,UAExCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAEfE,EAAAA,EAAAA,KAAA,UACEC,QAASA,IA1UV5C,WACnB,GAAK+E,OAAOc,QAAQzH,EAAE,0BAA2B,+DAIjD,IAKE,WAJuB8C,MAAM,iCAADF,OAAkC8E,GAAM,CAClErB,OAAQ,YAGGlD,GAIX,MAAM,IAAII,MAAM,+BAHhBjD,EAAesD,GAAQA,EAAKzB,OAAOmD,GAAcA,EAAWa,MAAQuB,IACpEH,MAAMvH,EAAE,oBAAqB,6CAIjC,CAAE,MAAOW,GACPkB,QAAQlB,MAAM,6BAA8BA,GAC5C4G,MAAMvH,EAAE,gBAAiB,gDAC3B,GAuTmC2H,CAAarC,EAAWa,KACvC9B,UAAU,4EACV+B,MAAOpG,EAAE,SAAU,UAAUsE,UAE7BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BA3DZiB,EAAWa,KAlOVjD,eAwSM,IAAvB7C,EAAY6D,SACXE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEtE,EAAE,qBAAsB,qCAE3BuE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDtE,EAAE,2BAA4B,+DAEjCoE,EAAAA,EAAAA,MAACK,EAAAA,GAAI,CACHC,GAAG,kCACHL,UAAU,2GAA0GC,SAAA,EAEpHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZrE,EAAE,wBAAyB,oCAOnCoB,EAAWG,WAAa,IACvBgD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yGAAwGC,UACrHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDtE,EAAE,OAAQ,QAAQ,IAAEoB,EAAWE,YAAY,IAAEtB,EAAE,KAAM,MAAM,IAAEoB,EAAWG,eAE3E6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMT,EAAiB3C,EAAWE,YAAc,GACzDsG,SAAqC,IAA3BxG,EAAWE,YACrB+C,UAAU,0OAAyOC,SAElPtE,EAAE,WAAY,cAIhB6H,MAAMC,KAAK,CAAE5D,OAAQgB,KAAKC,IAAI,EAAG/D,EAAWG,aAAe,CAACc,EAAG0F,KAC9D,MAAMrF,EAAOqF,EAAI7C,KAAK8C,IAAI,EAAG5G,EAAWE,YAAc,GACtD,OAAIoB,EAAOtB,EAAWG,WAAmB,MAGvCgD,EAAAA,EAAAA,KAAA,UAEEC,QAASA,IAAMT,EAAiBrB,GAChC2B,UAAS,mDAAAzB,OACPF,IAAStB,EAAWE,YAChB,yCACA,2IACHgD,SAEF5B,GARIA,MAaX6B,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAAMT,EAAiB3C,EAAWE,YAAc,GACzDsG,SAAUxG,EAAWE,cAAgBF,EAAWG,WAChD8C,UAAU,0OAAyOC,SAElPtE,EAAE,OAAQ,qB", "sources": ["pages/Forms/DischargeAssessmentList.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Link, useNavigate } from 'react-router-dom';\n\nconst DischargeAssessmentList = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n\n  const [assessments, setAssessments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [filters, setFilters] = useState({\n    search: '',\n    therapist: '',\n    dischargeReason: '',\n    startDate: '',\n    endDate: ''\n  });\n  const [pagination, setPagination] = useState({\n    currentPage: 1,\n    totalPages: 1,\n    total: 0,\n    limit: 10\n  });\n\n  useEffect(() => {\n    loadAssessments();\n  }, [filters, pagination.currentPage]);\n\n  const loadAssessments = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('Loading discharge assessments...');\n\n      // Filter out empty values\n      const cleanFilters = Object.fromEntries(\n        Object.entries(filters).filter(([_, value]) => value !== '')\n      );\n\n      const queryParams = new URLSearchParams({\n        page: pagination.currentPage.toString(),\n        limit: pagination.limit.toString(),\n        ...cleanFilters\n      });\n\n      console.log('API URL:', `/api/v1/discharge-assessments/public?${queryParams}`);\n\n      const response = await fetch(`/api/v1/discharge-assessments/public?${queryParams}`, {\n        headers: {\n          'Content-Type': 'application/json',\n          // Add authorization header if needed\n          ...(localStorage.getItem('token') && {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          })\n        }\n      });\n\n      console.log('Response status:', response.status);\n\n      if (response.ok) {\n        const data = await response.json();\n        console.log('Response data:', data);\n\n        if (data.success !== false) {\n          setAssessments(data.assessments || []);\n          setPagination(prev => ({\n            ...prev,\n            totalPages: data.totalPages || 1,\n            total: data.total || 0\n          }));\n        } else {\n          throw new Error(data.message || 'Failed to load discharge assessments');\n        }\n      } else {\n        const errorData = await response.json().catch(() => ({}));\n        console.log('Error response:', errorData);\n        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\n      }\n    } catch (error) {\n      console.error('Error loading discharge assessments:', error);\n      setError(error.message);\n      setAssessments([]); // Set empty array on error\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFilterChange = (field, value) => {\n    setFilters(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    setPagination(prev => ({ ...prev, currentPage: 1 }));\n  };\n\n  const handlePageChange = (page) => {\n    setPagination(prev => ({ ...prev, currentPage: page }));\n  };\n\n  const handleDelete = async (id) => {\n    if (!window.confirm(t('confirmDeleteAssessment', 'Are you sure you want to delete this discharge assessment?'))) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/v1/discharge-assessments/${id}`, {\n        method: 'DELETE'\n      });\n\n      if (response.ok) {\n        setAssessments(prev => prev.filter(assessment => assessment._id !== id));\n        alert(t('assessmentDeleted', 'Discharge assessment deleted successfully'));\n      } else {\n        throw new Error('Failed to delete assessment');\n      }\n    } catch (error) {\n      console.error('Error deleting assessment:', error);\n      alert(t('errorDeleting', 'Error deleting assessment. Please try again.'));\n    }\n  };\n\n  const downloadPDF = async (assessment) => {\n    try {\n      const response = await fetch('/api/v1/discharge-assessments/pdf', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(assessment)\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `discharge-assessment-${assessment.patientName}-${assessment.dischargeDate}.pdf`;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      }\n    } catch (error) {\n      console.error('Error downloading PDF:', error);\n      alert(t('errorDownloadingPDF', 'Error downloading PDF. Please try again.'));\n    }\n  };\n\n  const getStatusBadge = (status) => {\n    const statusColors = {\n      draft: 'bg-yellow-100 text-yellow-800',\n      completed: 'bg-green-100 text-green-800',\n      reviewed: 'bg-blue-100 text-blue-800'\n    };\n\n    return (\n      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColors[status] || 'bg-gray-100 text-gray-800'}`}>\n        {t(status, status)}\n      </span>\n    );\n  };\n\n  const getDischargeReasonBadge = (reasons) => {\n    if (!reasons || reasons.length === 0) return null;\n    \n    const primaryReason = reasons[0];\n    const reasonColors = {\n      'Goals Met': 'bg-green-100 text-green-800',\n      'Medical Condition': 'bg-red-100 text-red-800',\n      'Reached Maximal Potential': 'bg-blue-100 text-blue-800',\n      'Non-Compliance': 'bg-orange-100 text-orange-800',\n      'Other': 'bg-gray-100 text-gray-800'\n    };\n\n    return (\n      <span className={`px-2 py-1 text-xs font-medium rounded-full ${reasonColors[primaryReason] || 'bg-gray-100 text-gray-800'}`}>\n        {primaryReason}\n        {reasons.length > 1 && ` +${reasons.length - 1}`}\n      </span>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n        <div className=\"flex\">\n          <div className=\"flex-shrink-0\">\n            <i className=\"fas fa-exclamation-circle text-red-400\"></i>\n          </div>\n          <div className=\"ml-3\">\n            <h3 className=\"text-sm font-medium text-red-800\">\n              {t('errorLoadingAssessments', 'Error loading discharge assessments')}\n            </h3>\n            <div className=\"mt-2 text-sm text-red-700\">\n              <p>{error}</p>\n            </div>\n            <div className=\"mt-4\">\n              <button\n                onClick={loadAssessments}\n                className=\"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\"\n              >\n                {t('tryAgain', 'Try Again')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('dischargeAssessments', 'Discharge Assessments')}\n              </h1>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                {t('manageDischargeAssessments', 'Manage and view patient discharge assessments')}\n              </p>\n            </div>\n            <Link\n              to=\"/forms/discharge-assessment/new\"\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('newDischargeAssessment', 'New Discharge Assessment')}\n            </Link>\n          </div>\n        </div>\n\n        {/* Filters */}\n        <div className=\"px-6 py-4 bg-gray-50 dark:bg-gray-700\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('search', 'Search')}\n              </label>\n              <input\n                type=\"text\"\n                value={filters.search}\n                onChange={(e) => handleFilterChange('search', e.target.value)}\n                placeholder={t('searchPatients', 'Search patients...')}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('therapist', 'Therapist')}\n              </label>\n              <input\n                type=\"text\"\n                value={filters.therapist}\n                onChange={(e) => handleFilterChange('therapist', e.target.value)}\n                placeholder={t('filterByTherapist', 'Filter by therapist...')}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('dischargeReason', 'Discharge Reason')}\n              </label>\n              <select\n                value={filters.dischargeReason}\n                onChange={(e) => handleFilterChange('dischargeReason', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm\"\n              >\n                <option value=\"\">{t('allReasons', 'All Reasons')}</option>\n                <option value=\"Goals Met\">{t('goalsMet', 'Goals Met')}</option>\n                <option value=\"Medical Condition\">{t('medicalCondition', 'Medical Condition')}</option>\n                <option value=\"Reached Maximal Potential\">{t('reachedMaximalPotential', 'Reached Maximal Potential')}</option>\n                <option value=\"Non-Compliance\">{t('nonCompliance', 'Non-Compliance')}</option>\n                <option value=\"Other\">{t('other', 'Other')}</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('startDate', 'Start Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={filters.startDate}\n                onChange={(e) => handleFilterChange('startDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('endDate', 'End Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={filters.endDate}\n                onChange={(e) => handleFilterChange('endDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Results Summary */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mb-6 p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {t('showingResults', 'Showing {{start}} to {{end}} of {{total}} results', {\n              start: (pagination.currentPage - 1) * pagination.limit + 1,\n              end: Math.min(pagination.currentPage * pagination.limit, pagination.total),\n              total: pagination.total\n            })}\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <label className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('itemsPerPage', 'Items per page:')}\n            </label>\n            <select\n              value={pagination.limit}\n              onChange={(e) => setPagination(prev => ({ ...prev, limit: parseInt(e.target.value), currentPage: 1 }))}\n              className=\"px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm\"\n            >\n              <option value={10}>10</option>\n              <option value={25}>25</option>\n              <option value={50}>50</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Assessments Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('dischargeDate', 'Discharge Date')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('therapist', 'Therapist')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('dischargeReason', 'Discharge Reason')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('visits', 'Visits')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              {assessments.map((assessment) => (\n                <tr key={assessment._id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {assessment.patientName}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        MR: {assessment.mrNumber}\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {new Date(assessment.dischargeDate).toLocaleDateString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {assessment.therapistSignature}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {getDischargeReasonBadge(assessment.dischargeReasons)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    <div>\n                      <div>{assessment.totalVisits} total</div>\n                      <div className=\"text-xs text-gray-500\">\n                        {assessment.noShowCancellations} no-shows\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    {getStatusBadge(assessment.status)}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                    <div className=\"flex items-center justify-end space-x-2\">\n                      <Link\n                        to={`/forms/discharge-assessment/${assessment._id}`}\n                        className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300\"\n                        title={t('view', 'View')}\n                      >\n                        <i className=\"fas fa-eye\"></i>\n                      </Link>\n                      <Link\n                        to={`/forms/discharge-assessment/${assessment._id}/edit`}\n                        className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                        title={t('edit', 'Edit')}\n                      >\n                        <i className=\"fas fa-edit\"></i>\n                      </Link>\n                      <button\n                        onClick={() => downloadPDF(assessment)}\n                        className=\"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300\"\n                        title={t('downloadPDF', 'Download PDF')}\n                      >\n                        <i className=\"fas fa-file-pdf\"></i>\n                      </button>\n                      <button\n                        onClick={() => handleDelete(assessment._id)}\n                        className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                        title={t('delete', 'Delete')}\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {/* Empty State */}\n        {assessments.length === 0 && (\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-clipboard-list text-gray-400 text-4xl mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('noAssessmentsFound', 'No discharge assessments found')}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400 mb-4\">\n              {t('noAssessmentsDescription', 'Get started by creating your first discharge assessment.')}\n            </p>\n            <Link\n              to=\"/forms/discharge-assessment/new\"\n              className=\"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('createFirstAssessment', 'Create First Assessment')}\n            </Link>\n          </div>\n        )}\n      </div>\n\n      {/* Pagination */}\n      {pagination.totalPages > 1 && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-700 mt-6 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n              {t('page', 'Page')} {pagination.currentPage} {t('of', 'of')} {pagination.totalPages}\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={() => handlePageChange(pagination.currentPage - 1)}\n                disabled={pagination.currentPage === 1}\n                className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {t('previous', 'Previous')}\n              </button>\n              \n              {/* Page Numbers */}\n              {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {\n                const page = i + Math.max(1, pagination.currentPage - 2);\n                if (page > pagination.totalPages) return null;\n                \n                return (\n                  <button\n                    key={page}\n                    onClick={() => handlePageChange(page)}\n                    className={`px-3 py-2 border rounded-lg text-sm font-medium ${\n                      page === pagination.currentPage\n                        ? 'bg-blue-600 text-white border-blue-600'\n                        : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'\n                    }`}\n                  >\n                    {page}\n                  </button>\n                );\n              })}\n              \n              <button\n                onClick={() => handlePageChange(pagination.currentPage + 1)}\n                disabled={pagination.currentPage === pagination.totalPages}\n                className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {t('next', 'Next')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default DischargeAssessmentList;\n"], "names": ["DischargeAssessmentList", "t", "isRTL", "useLanguage", "user", "useAuth", "assessments", "setAssessments", "useNavigate", "useState", "loading", "setLoading", "error", "setError", "filters", "setFilters", "search", "therapist", "dischargeReason", "startDate", "endDate", "pagination", "setPagination", "currentPage", "totalPages", "total", "limit", "useEffect", "loadAssessments", "async", "console", "log", "cleanFilters", "Object", "fromEntries", "entries", "filter", "_ref", "_", "value", "queryParams", "URLSearchParams", "_objectSpread", "page", "toString", "concat", "response", "fetch", "headers", "localStorage", "getItem", "status", "ok", "errorData", "json", "catch", "Error", "message", "statusText", "data", "success", "prev", "handleFilterChange", "field", "handlePageChange", "getDischargeReasonBadge", "reasons", "length", "primaryReason", "_jsxs", "className", "children", "_jsx", "onClick", "Link", "to", "type", "onChange", "e", "target", "placeholder", "start", "end", "Math", "min", "parseInt", "map", "assessment", "patientName", "mr<PERSON><PERSON><PERSON>", "Date", "dischargeDate", "toLocaleDateString", "therapistSignature", "dischargeReasons", "totalVisits", "noShowCancellations", "draft", "completed", "reviewed", "_id", "title", "method", "body", "JSON", "stringify", "blob", "url", "window", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "alert", "downloadPDF", "confirm", "id", "handleDelete", "disabled", "Array", "from", "i", "max"], "sourceRoot": ""}