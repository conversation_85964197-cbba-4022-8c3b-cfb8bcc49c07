"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3070],{2945:(e,a,t)=>{t.d(a,{A:()=>n});var r=t(2555),s=t(5043),l=t(7921),d=t(3768),i=t(579);const n=e=>{let{onSubmit:a,onCancel:t,initialData:n=null}=e;const{t:o,isRTL:c}=(0,l.o)(),[m,g]=(0,s.useState)(!1),[x,u]=(0,s.useState)({patientId:(null===n||void 0===n?void 0:n.patientId)||"",patientName:(null===n||void 0===n?void 0:n.patientName)||"",date:(null===n||void 0===n?void 0:n.date)||(new Date).toISOString().split("T")[0],time:(null===n||void 0===n?void 0:n.time)||"",duration:(null===n||void 0===n?void 0:n.duration)||"60",type:(null===n||void 0===n?void 0:n.type)||"consultation",therapist:(null===n||void 0===n?void 0:n.therapist)||"",location:(null===n||void 0===n?void 0:n.location)||"room_1",notes:(null===n||void 0===n?void 0:n.notes)||"",priority:(null===n||void 0===n?void 0:n.priority)||"normal",reminderEnabled:(null===n||void 0===n?void 0:n.reminderEnabled)||!0,reminderTime:(null===n||void 0===n?void 0:n.reminderTime)||"24"}),[h,p]=(0,s.useState)({}),b=[{value:"consultation",label:o("consultation","Consultation")},{value:"assessment",label:o("assessment","Assessment")},{value:"therapy_session",label:o("therapySession","Therapy Session")},{value:"follow_up",label:o("followUp","Follow-up")},{value:"group_therapy",label:o("groupTherapy","Group Therapy")},{value:"evaluation",label:o("evaluation","Evaluation")}],y=[{value:"room_1",label:o("room1","Room 1 - Main Therapy")},{value:"room_2",label:o("room2","Room 2 - Assessment")},{value:"room_3",label:o("room3","Room 3 - Group Therapy")},{value:"gym",label:o("gym","Gymnasium")},{value:"pool",label:o("pool","Hydrotherapy Pool")}],v=(e,a)=>{u(t=>(0,r.A)((0,r.A)({},t),{},{[e]:a})),h[e]&&p(a=>(0,r.A)((0,r.A)({},a),{},{[e]:null}))};return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:n?o("editAppointment","Edit Appointment"):o("newAppointment","New Appointment")}),t&&(0,i.jsx)("button",{onClick:t,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,i.jsx)("i",{className:"fas fa-times text-xl"})})]}),(0,i.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};return x.patientName.trim()||(e.patientName=o("patientNameRequired","Patient name is required")),x.date||(e.date=o("dateRequired","Date is required")),x.time||(e.time=o("timeRequired","Time is required")),x.therapist||(e.therapist=o("therapistRequired","Therapist is required")),p(e),0===Object.keys(e).length})()){g(!0);try{await new Promise(e=>setTimeout(e,1e3)),a&&a(x),d.Ay.success(o("appointmentSaved","Appointment saved successfully"))}catch(t){d.Ay.error(o("errorSavingAppointment","Error saving appointment"))}finally{g(!1)}}else d.Ay.error(o("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("patientName","Patient Name")," ",(0,i.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,i.jsx)("input",{type:"text",value:x.patientName,onChange:e=>v("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(h.patientName?"border-red-500":"border-gray-300"),placeholder:o("enterPatientName","Enter patient name")}),h.patientName&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.patientName})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("appointmentType","Appointment Type")}),(0,i.jsx)("select",{value:x.type,onChange:e=>v("type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:b.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("date","Date")," ",(0,i.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,i.jsx)("input",{type:"date",value:x.date,onChange:e=>v("date",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(h.date?"border-red-500":"border-gray-300")}),h.date&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.date})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("time","Time")," ",(0,i.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,i.jsxs)("select",{value:x.time,onChange:e=>v("time",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(h.time?"border-red-500":"border-gray-300"),children:[(0,i.jsx)("option",{value:"",children:o("selectTime","Select time")}),["08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00"].map(e=>(0,i.jsx)("option",{value:e,children:e},e))]}),h.time&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.time})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("duration","Duration (minutes)")}),(0,i.jsxs)("select",{value:x.duration,onChange:e=>v("duration",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,i.jsxs)("option",{value:"30",children:["30 ",o("minutes","minutes")]}),(0,i.jsxs)("option",{value:"45",children:["45 ",o("minutes","minutes")]}),(0,i.jsxs)("option",{value:"60",children:["60 ",o("minutes","minutes")]}),(0,i.jsxs)("option",{value:"90",children:["90 ",o("minutes","minutes")]}),(0,i.jsxs)("option",{value:"120",children:["120 ",o("minutes","minutes")]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[o("therapist","Therapist")," ",(0,i.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,i.jsxs)("select",{value:x.therapist,onChange:e=>v("therapist",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(h.therapist?"border-red-500":"border-gray-300"),children:[(0,i.jsx)("option",{value:"",children:o("selectTherapist","Select therapist")}),[{value:"dr_sarah",label:"Dr. Sarah Al-Rashid"},{value:"dr_ahmed",label:"Dr. Ahmed Al-Mansouri"},{value:"dr_fatima",label:"Dr. Fatima Al-Zahra"},{value:"dr_mohammed",label:"Dr. Mohammed Al-Khalid"}].map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))]}),h.therapist&&(0,i.jsx)("p",{className:"text-red-500 text-sm mt-1",children:h.therapist})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("location","Location")}),(0,i.jsx)("select",{value:x.location,onChange:e=>v("location",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:y.map(e=>(0,i.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("priority","Priority")}),(0,i.jsxs)("select",{value:x.priority,onChange:e=>v("priority",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,i.jsx)("option",{value:"low",children:o("low","Low")}),(0,i.jsx)("option",{value:"normal",children:o("normal","Normal")}),(0,i.jsx)("option",{value:"high",children:o("high","High")}),(0,i.jsx)("option",{value:"urgent",children:o("urgent","Urgent")})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("reminder","Reminder")}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("input",{type:"checkbox",checked:x.reminderEnabled,onChange:e=>v("reminderEnabled",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,i.jsxs)("select",{value:x.reminderTime,onChange:e=>v("reminderTime",e.target.value),disabled:!x.reminderEnabled,className:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50",children:[(0,i.jsxs)("option",{value:"15",children:["15 ",o("minutesBefore","minutes before")]}),(0,i.jsxs)("option",{value:"30",children:["30 ",o("minutesBefore","minutes before")]}),(0,i.jsxs)("option",{value:"60",children:["1 ",o("hourBefore","hour before")]}),(0,i.jsxs)("option",{value:"1440",children:["1 ",o("dayBefore","day before")]})]})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:o("notes","Notes")}),(0,i.jsx)("textarea",{value:x.notes,onChange:e=>v("notes",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:o("enterNotes","Enter any additional notes")})]}),(0,i.jsxs)("div",{className:"flex justify-end space-x-4",children:[t&&(0,i.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:o("cancel","Cancel")}),(0,i.jsx)("button",{type:"submit",disabled:m,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:m?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),o("saving","Saving...")]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("i",{className:"fas fa-save mr-2"}),o("saveAppointment","Save Appointment")]})})]})]})]})}},3070:(e,a,t)=>{t.r(a),t.d(a,{default:()=>o});var r=t(5043),s=t(3216),l=t(7921),d=t(579);const i=e=>{let{appointments:a=[],onDateSelect:t,onAppointmentClick:s}=e;const{t:i,isRTL:n}=(0,l.o)(),[o,c]=(0,r.useState)(new Date),[m,g]=(0,r.useState)(null),x=o.getMonth(),u=o.getFullYear(),h=new Date(u,x,1),p=new Date(u,x+1,0),b=h.getDay(),y=p.getDate(),v=[];for(let r=0;r<b;r++)v.push(null);for(let r=1;r<=y;r++)v.push(r);const j=[i("january","January"),i("february","February"),i("march","March"),i("april","April"),i("may","May"),i("june","June"),i("july","July"),i("august","August"),i("september","September"),i("october","October"),i("november","November"),i("december","December")],f=[i("sunday","Sun"),i("monday","Mon"),i("tuesday","Tue"),i("wednesday","Wed"),i("thursday","Thu"),i("friday","Fri"),i("saturday","Sat")],N=e=>{const a=new Date(o);a.setMonth(x+e),c(a)},k=e=>{if(!e)return[];const t="".concat(u,"-").concat(String(x+1).padStart(2,"0"),"-").concat(String(e).padStart(2,"0"));return a.filter(e=>e.date===t)},w=e=>{const a=new Date;return e===a.getDate()&&x===a.getMonth()&&u===a.getFullYear()},A=e=>{if(!e||!m)return!1;return"".concat(u,"-").concat(String(x+1).padStart(2,"0"),"-").concat(String(e).padStart(2,"0"))===m};return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("button",{onClick:()=>N(-1),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,d.jsx)("i",{className:"fas fa-chevron-".concat(n?"right":"left"," text-gray-600 dark:text-gray-400")})}),(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[j[x]," ",u]}),(0,d.jsx)("button",{onClick:()=>N(1),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,d.jsx)("i",{className:"fas fa-chevron-".concat(n?"left":"right"," text-gray-600 dark:text-gray-400")})})]}),(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsx)("div",{className:"grid grid-cols-7 gap-1 mb-2",children:f.map((e,a)=>(0,d.jsx)("div",{className:"p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400",children:e},a))}),(0,d.jsx)("div",{className:"grid grid-cols-7 gap-1",children:v.map((e,a)=>{const r=k(e),l=r.length>0;return(0,d.jsx)("div",{onClick:()=>(e=>{if(!e)return;const a="".concat(u,"-").concat(String(x+1).padStart(2,"0"),"-").concat(String(e).padStart(2,"0"));g(a),t&&t(a)})(e),className:"\n                  relative p-2 h-20 border border-gray-100 dark:border-gray-700 rounded-lg cursor-pointer transition-all\n                  ".concat(e?"hover:bg-gray-50 dark:hover:bg-gray-700":"cursor-default","\n                  ").concat(w(e)?"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800":"","\n                  ").concat(A(e)?"bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-700":"","\n                  ").concat(e?"":"bg-gray-50 dark:bg-gray-900","\n                "),children:e&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"text-sm font-medium ".concat(w(e)?"text-blue-600 dark:text-blue-400":"text-gray-900 dark:text-white"),children:e}),l&&(0,d.jsxs)("div",{className:"mt-1 space-y-1",children:[r.slice(0,2).map((e,a)=>(0,d.jsx)("div",{onClick:a=>{a.stopPropagation(),s&&s(e)},className:"text-xs bg-blue-500 text-white px-1 py-0.5 rounded truncate hover:bg-blue-600 transition-colors",title:"".concat(e.time," - ").concat(e.patientName),children:e.time},a)),r.length>2&&(0,d.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",r.length-2," ",i("more","more")]})]})]})},a)})})]}),m&&(0,d.jsxs)("div",{className:"p-4 border-t border-gray-200 dark:border-gray-600",children:[(0,d.jsxs)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:[i("appointmentsFor","Appointments for")," ",m]}),k(parseInt(m.split("-")[2])).length>0?(0,d.jsx)("div",{className:"space-y-2",children:k(parseInt(m.split("-")[2])).map((e,a)=>(0,d.jsxs)("div",{onClick:()=>s&&s(e),className:"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.patientName}),(0,d.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:[e.time," - ",e.type]})]}),(0,d.jsx)("div",{className:"px-2 py-1 text-xs rounded-full ".concat("confirmed"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"pending"===e.status?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"),children:i(e.status,e.status)})]},a))}):(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:i("noAppointments","No appointments scheduled for this date")})]})]})};var n=t(2945);const o=()=>{const{t:e,isRTL:a}=(0,l.o)(),t=(0,s.Zp)(),[o,c]=(0,r.useState)(null),[m,g]=(0,r.useState)(!1),[x,u]=(0,r.useState)(null),[h]=(0,r.useState)([{id:1,patientName:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",patientNameEn:"Ahmed Mohammed Al-Ahmed",date:"2024-01-25",time:"10:00",type:"Physical Therapy",therapist:"Dr. Sarah Al-Rashid",status:"confirmed"},{id:2,patientName:"\u0641\u0627\u0637\u0645\u0629 \u0639\u0644\u064a \u0627\u0644\u0633\u0627\u0644\u0645",patientNameEn:"Fatima Ali Al-Salem",date:"2024-01-25",time:"11:30",type:"Occupational Therapy",therapist:"Dr. Ahmed Al-Mansouri",status:"pending"},{id:3,patientName:"\u0645\u062d\u0645\u062f \u0639\u0628\u062f\u0627\u0644\u0644\u0647 \u0627\u0644\u062e\u0627\u0644\u062f",patientNameEn:"Mohammed Abdullah Al-Khalid",date:"2024-01-25",time:"14:00",type:"Speech Therapy",therapist:"Dr. Fatima Al-Zahra",status:"completed"},{id:4,patientName:"\u0633\u0627\u0631\u0629 \u0623\u062d\u0645\u062f \u0627\u0644\u0645\u0637\u064a\u0631\u064a",patientNameEn:"Sarah Ahmed Al-Mutairi",date:"2024-01-26",time:"09:00",type:"Group Therapy",therapist:"Dr. Mohammed Al-Khalid",status:"confirmed"},{id:5,patientName:"\u0639\u0644\u064a \u0645\u062d\u0645\u062f \u0627\u0644\u0632\u0647\u0631\u0627\u0646\u064a",patientNameEn:"Ali Mohammed Al-Zahrani",date:"2024-01-26",time:"15:30",type:"Physical Therapy",therapist:"Dr. Sarah Al-Rashid",status:"confirmed"}]),p=e=>{u(e),console.log("Appointment clicked:",e)},b=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;u(null),e&&c(new Date(e)),g(!0)};return(0,d.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("appointmentCalendar","Appointment Calendar")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("appointmentCalendarDesc","View and manage appointments in calendar format")})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsxs)("button",{onClick:()=>t("/appointments"),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-list mr-2"}),e("listView","List View")]}),(0,d.jsxs)("button",{onClick:()=>b(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("newAppointment","New Appointment")]})]})]})}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.filter(e=>"confirmed"===e.status).length}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("confirmed","Confirmed")})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.filter(e=>"pending"===e.status).length}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("pending","Pending")})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.filter(e=>"completed"===e.status).length}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("completed","Completed")})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,d.jsx)("i",{className:"fas fa-calendar text-purple-600 dark:text-purple-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("h4",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.length}),(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("totalAppointments","Total")})]})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsx)("div",{className:"lg:col-span-2",children:(0,d.jsx)(i,{appointments:h,onDateSelect:e=>{c(new Date(e))},onAppointmentClick:p})}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("quickActions","Quick Actions")}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("button",{onClick:()=>b(),className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-left",children:[(0,d.jsx)("i",{className:"fas fa-plus mr-2"}),e("scheduleAppointment","Schedule Appointment")]}),(0,d.jsxs)("button",{onClick:()=>t("/appointments"),className:"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-left",children:[(0,d.jsx)("i",{className:"fas fa-list mr-2"}),e("viewAllAppointments","View All Appointments")]}),(0,d.jsxs)("button",{className:"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-left",children:[(0,d.jsx)("i",{className:"fas fa-download mr-2"}),e("exportCalendar","Export Calendar")]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("todaysAppointments","Today's Appointments")}),h.filter(e=>e.date===(new Date).toISOString().split("T")[0]).length>0?(0,d.jsx)("div",{className:"space-y-3",children:h.filter(e=>e.date===(new Date).toISOString().split("T")[0]).map(e=>(0,d.jsx)("div",{className:"p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:()=>p(e),children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.time}),(0,d.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:a?e.patientName:e.patientNameEn})]}),(0,d.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("confirmed"===e.status?"bg-green-500":"pending"===e.status?"bg-yellow-500":"completed"===e.status?"bg-blue-500":"bg-red-500")})]})},e.id))}):(0,d.jsx)("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:e("noAppointmentsToday","No appointments scheduled for today")})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("legend","Legend")}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("confirmed","Confirmed")})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-yellow-500 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("pending","Pending")})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("completed","Completed")})]}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full mr-3"}),(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("cancelled","Cancelled")})]})]})]})]})]}),m&&(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:(0,d.jsx)(n.A,{initialData:o?{date:o.toISOString().split("T")[0]}:null,onSubmit:e=>{console.log("New appointment:",e),g(!1)},onCancel:()=>g(!1)})})})]})}}}]);
//# sourceMappingURL=3070.1a733a74.chunk.js.map