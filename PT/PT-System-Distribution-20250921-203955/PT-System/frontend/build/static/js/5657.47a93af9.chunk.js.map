{"version": 3, "file": "static/js/5657.47a93af9.chunk.js", "mappings": "iOAMA,MAmpCA,EAnpCoBA,KAClB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,GAGjCC,EAAeL,EAASM,SAASC,SAAS,SACzCC,EAAWC,IAAgBL,EAAAA,EAAAA,UAASC,EAAe,SAAW,aAC9DK,EAAUC,IAAeP,EAAAA,EAAAA,UAAS,KAClCQ,EAAYC,IAAiBT,EAAAA,EAAAA,UAAS,KACtCU,EAAcC,IAAmBX,EAAAA,EAAAA,UAAS,QAC1CY,EAAsBC,IAA2Bb,EAAAA,EAAAA,UAAS,KAC1Dc,EAAmBC,IAAwBf,EAAAA,EAAAA,WAAS,IAGpDgB,EAAaC,IAAkBjB,EAAAA,EAAAA,UAAS,CAC7CkB,UAAW,GACXC,YAAa,GACbC,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,QAAS,GACTC,SAAU,CACR,CACEC,YAAa,GACbC,SAAU,EACVC,UAAW,GACXC,MAAO,IAGXC,SAAU,EACVC,QAAS,GACTC,UAAW,EACXC,eAAgB,EAChBC,YAAa,EACbC,MAAO,GACPC,aAAc,QAGTC,EAAQC,IAAatC,EAAAA,EAAAA,UAAS,CAAC,IAEtCuC,EAAAA,EAAAA,WAAU,KACRC,KACC,KAEHD,EAAAA,EAAAA,WAAU,KACRE,KACC,CAACzB,EAAYS,SAAUT,EAAYiB,eAAgBjB,EAAYe,UAElE,MA6JMS,EAAeE,UACnB3C,GAAW,GACX,IACE,MAAM4C,EAAc,IAAIC,iBAAeC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAC,CACtCC,KAAM,EACNC,MAAO,IACHvC,GAAc,CAAEwC,OAAQxC,IACP,QAAjBE,GAA0B,CAAEuC,OAAQvC,KAGpCwC,QAAiBC,MAAM,mBAADC,OAAoBT,GAAe,CAC7DU,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIL,EAASM,GAIX,MAAM,IAAIC,MAAM,2BAJD,CACf,MAAMC,QAAaR,EAASS,OAC5BpD,EAAYmD,EAAKA,MAAQ,GAC3B,CAGF,CAAE,MAAOE,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAMF,MAAM,2BAmCZrD,EAjCqB,CACnB,CACEwD,GAAI,eACJ5C,YAAa,uEACb6C,cAAe,qBACfC,UAAW,aACXzC,QAAS,aACT0C,OAAQ,KACRjB,OAAQ,OACRxB,SAAU,CAAC,2BAA4B,cACvC0C,YAAa,cAEf,CACEJ,GAAI,eACJ5C,YAAa,0DACb6C,cAAe,eACfC,UAAW,aACXzC,QAAS,aACT0C,OAAQ,KACRjB,OAAQ,UACRxB,SAAU,CAAC,qBAAsB,mBAEnC,CACEsC,GAAI,eACJ5C,YAAa,oDACb6C,cAAe,iBACfC,UAAW,aACXzC,QAAS,aACT0C,OAAQ,KACRjB,OAAQ,UACRxB,SAAU,CAAC,oBAAqB,wBAItC,CAAC,QACC1B,GAAW,EACb,GAGI0C,EAAyBA,KAC7B,MAAMX,EAAWd,EAAYS,SAAS2C,OAAO,CAACC,EAAKC,IAE1CD,GADeC,EAAQ3C,UAAY,IAAM2C,EAAQ1C,WAAa,GAEpE,GAEGK,EAAiBjB,EAAYiB,gBAAkB,EAC/CsC,EAAgBzC,EAAWG,EAC3BD,EAAauC,GAAiBvD,EAAYe,SAAW,GAAM,IAC3DG,EAAcqC,EAAgBvC,EAEpCf,EAAeuD,IAAI3B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACd2B,GAAI,IACP1C,WACAE,YACAE,kBAIEuC,EAAoBA,CAACC,EAAOC,KAChC1D,EAAeuD,IAAI3B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAU2B,GAAI,IAAE,CAACE,GAAQC,KACxCtC,EAAOqC,IACTpC,EAAUkC,IAAI3B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAU2B,GAAI,IAAE,CAACE,GAAQ,SAIrCE,EAAsBA,CAACC,EAAOH,EAAOC,KACzC,MAAMG,EAAkB,IAAI9D,EAAYS,UAOxC,GANAqD,EAAgBD,IAAMhC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACjBiC,EAAgBD,IAAM,IACzB,CAACH,GAAQC,IAIG,aAAVD,GAAkC,cAAVA,EAAuB,CACjD,MAAM/C,EAAqB,aAAV+C,EAAuBC,EAAQG,EAAgBD,GAAOlD,SACjEC,EAAsB,cAAV8C,EAAwBC,EAAQG,EAAgBD,GAAOjD,UACzEkD,EAAgBD,GAAOhD,OAASF,GAAY,IAAMC,GAAa,EACjE,CAEAX,EAAeuD,IAAI3B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAU2B,GAAI,IAAE/C,SAAUqD,MAqIzCC,EAAkB9B,IACtB,OAAQA,GACN,IAAK,OACH,MAAO,uEACT,IAAK,UACH,MAAO,2EACT,IAAK,UACH,MAAO,+DACT,IAAK,YACH,MAAO,mEACT,QACE,MAAO,qEAIP+B,EAAmB1E,EAAS2E,OAAOC,IAAY,IAADC,EAClD,MAAMC,EAAgBF,EAAQ/D,YAAYkE,cAAclF,SAASK,EAAW6E,iBAClC,QADgDF,EACrED,EAAQlB,qBAAa,IAAAmB,OAAA,EAArBA,EAAuBE,cAAclF,SAASK,EAAW6E,iBACzDH,EAAQnB,GAAGsB,cAAclF,SAASK,EAAW6E,eAC5DC,EAAiC,QAAjB5E,GAA0BwE,EAAQjC,SAAWvC,EACnE,OAAO0E,GAAiBE,IAG1B,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DpG,EAAE,oBAAqB,yBAE1BqG,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDpG,EAAE,wBAAyB,6DAKhCqG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMtF,EAAa,YAC5BmF,UAAS,4CAAApC,OACO,aAAdhD,EACI,mDACA,0HACHqF,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZnG,EAAE,WAAY,gBAEjBkG,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMtF,EAAa,UAC5BmF,UAAS,4CAAApC,OACO,WAAdhD,EACI,mDACA,0HACHqF,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZnG,EAAE,gBAAiB,sBAEtBkG,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMtF,EAAa,aAC5BmF,UAAS,4CAAApC,OACO,cAAdhD,EACI,mDACA,0HACHqF,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZnG,EAAE,YAAa,iBAElBkG,EAAAA,EAAAA,MAAA,UACEI,QAjWcjD,UACxB,IAEE,MAAMkD,EAAkB,CACtBC,QAAS,2BACTpE,SAAU,CACR,CACEqE,KAAM,gCACNpE,YAAa,2BACbC,SAAU,EACVC,UAAW,IACXmE,MAAM,IAAI1E,MAAOC,cAAcC,MAAM,KAAK,KAG9CC,QAAS,IAAIH,KAAKA,KAAK2E,MAAQ,QAA0B1E,cAAcC,MAAM,KAAK,GAClFY,MAAO,0CAGHe,QAAiBC,MAAM,kBAAmB,CAC9C8C,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElB2C,KAAMC,KAAKC,UAAUR,KAGvB,IAAI1C,EAASM,GAIN,CACL,MAAM6C,QAAkBnD,EAASS,OACjC,MAAM,IAAIF,MAAM4C,EAAUC,SAAW,gCACvC,OANuBpD,EAASS,OAC9BG,EAAAA,GAAMyC,QAAQ,qCACd/D,GAKJ,CAAE,MAAOoB,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CE,EAAAA,GAAMF,MAAM,gCACd,GA4TU4B,UAAU,iGACVgB,MAAM,0CAAyCf,SAAA,EAE/CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBAAwB,mBAGvCD,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMtF,EAAa,YAC5BmF,UAAS,4CAAApC,OACO,aAAdhD,EACI,mDACA,0HACHqF,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZnG,EAAE,WAAY,sBAOR,aAAde,IACCmF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sFAAqFC,SAAA,EAClGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DACZnG,EAAE,cAAe,oBAGpBkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACL9B,MAAOnE,EACPkG,SAAWC,GAAMlG,EAAckG,EAAEC,OAAOjC,OACxCkC,YAAaxH,EAAE,iBAAkB,sBACjCmG,UAAU,oKAEZE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAGfD,EAAAA,EAAAA,MAAA,UACEZ,MAAOjE,EACPgG,SAAWC,GAAMhG,EAAgBgG,EAAEC,OAAOjC,OAC1Ca,UAAU,2JAA0JC,SAAA,EAEpKC,EAAAA,EAAAA,KAAA,UAAQf,MAAM,MAAKc,SAAEpG,EAAE,cAAe,mBACtCqG,EAAAA,EAAAA,KAAA,UAAQf,MAAM,OAAMc,SAAEpG,EAAE,OAAQ,WAChCqG,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAEpG,EAAE,UAAW,cACtCqG,EAAAA,EAAAA,KAAA,UAAQf,MAAM,UAASc,SAAEpG,EAAE,UAAW,cACtCqG,EAAAA,EAAAA,KAAA,UAAQf,MAAM,YAAWc,SAAEpG,EAAE,YAAa,2BAMlDqG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GpG,EAAE,gBAAiB,gBAEtBqG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GpG,EAAE,UAAW,cAEhBqG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GpG,EAAE,SAAU,aAEfqG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GpG,EAAE,YAAa,iBAElBqG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GpG,EAAE,UAAW,eAEhBqG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GpG,EAAE,SAAU,aAEfqG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9GpG,EAAE,UAAW,mBAIpBqG,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFT,EAAiB8B,IAAK5B,IAAO,IAAA6B,EAAA,OAC5BxB,EAAAA,EAAAA,MAAA,MAAqBC,UAAU,0CAAyCC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DP,EAAQnB,QAGbwB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DnG,EAAQ4F,EAAQ/D,YAAc+D,EAAQlB,iBAEzC0B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtC,QADsCsB,EACtD7B,EAAQzD,gBAAQ,IAAAsF,OAAA,EAAhBA,EAAkBC,KAAK,YAG5BtB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,CAC/DP,EAAQhB,OAAO+C,iBAAiB,IAAE5H,EAAE,MAAO,aAGhDqG,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9EP,EAAQjB,aAEXyB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9EP,EAAQ1D,WAEXkE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAApC,OAAgD2B,EAAeG,EAAQjC,SAAUwC,SAC7FpG,EAAE6F,EAAQjC,OAAQiC,EAAQjC,aAG/ByC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,UAC7DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEC,QAASA,KAAMuB,OA7iBlBC,EA6iB8BjC,EAAQnB,QA3iBzDrE,EAAS,sBAAD0D,OAAuB+D,IAFZA,OA8iBG3B,UAAU,gFACVgB,MAAOnH,EAAE,cAAe,gBAAgBoG,UAExCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kBAEfE,EAAAA,EAAAA,KAAA,UACEC,QAASA,IA/iBbjD,WAClB,IACE,MAAMQ,QAAiBC,MAAM,mBAADC,OAAoB+D,EAAS,SAAS,CAChElB,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIL,EAASM,GAIN,CACL,MAAM6C,QAAkBnD,EAASS,OACjC,MAAM,IAAIF,MAAM4C,EAAUC,SAAW,yBACvC,CAPiB,CACf,MAAMc,QAAelE,EAASS,OAC9BG,EAAAA,GAAMyC,QAAQa,EAAOd,SAAW,6BAChC9D,GACF,CAIF,CAAE,MAAOoB,GACPC,QAAQD,MAAM,yBAA0BA,GACxCE,EAAAA,GAAMF,MAAMA,EAAM0C,QACpB,GA0hBqCe,CAAYnC,EAAQnB,IACnCyB,UAAU,oFACVgB,MAAOnH,EAAE,cAAe,gBAAgBoG,UAExCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BAEfE,EAAAA,EAAAA,KAAA,UACEC,QAASA,IA9hBTjD,WACtB,IAOE,WANuBS,MAAM,mBAADC,OAAoB+D,EAAS,QAAQ,CAC/D9D,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,cAIvCC,GAIX,MAAM,IAAIC,MAAM,0BAFhBK,EAAAA,GAAMyC,QAAQ,sCAIlB,CAAE,MAAO3C,GACPC,QAAQD,MAAM,6BAA8BA,GAC5CE,EAAAA,GAAMF,MAAM,6BACd,GA6gBqC0D,CAAgBpC,EAAQnB,IACvCyB,UAAU,wFACVgB,MAAOnH,EAAE,cAAe,gBAAgBoG,UAExCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAEM,YAAnBN,EAAQjC,QAA2C,SAAnBiC,EAAQjC,UACxCyC,EAAAA,EAAAA,KAAA,UACEC,QAASA,IAlhBhBjD,WACjB,IACE,MAAMQ,QAAiBC,MAAM,mBAADC,OAAoB+D,EAAS,cAAc,CACrElB,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElB2C,KAAMC,KAAKC,UAAU,CACnBmB,cAAe,OACfpF,MAAO,uCAIX,IAAIe,EAASM,GAIN,CACL,MAAM6C,QAAkBnD,EAASS,OACjC,MAAM,IAAIF,MAAM4C,EAAUC,SAAW,yBACvC,CAPiB,CACf,MAAMc,QAAelE,EAASS,OAC9BG,EAAAA,GAAMyC,QAAQa,EAAOd,SAAW,0BAChC9D,GACF,CAIF,CAAE,MAAOoB,GACPC,QAAQD,MAAM,yBAA0BA,GACxCE,EAAAA,GAAMF,MAAMA,EAAM0C,QACpB,GAyfuCkB,CAAWtC,EAAQnB,IAClCyB,UAAU,4FACVgB,MAAOnH,EAAE,aAAc,gBAAgBoG,UAEvCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BAGG,YAAnBN,EAAQjC,SACPyC,EAAAA,EAAAA,KAAA,UACEC,QAASA,KAAM8B,OA/fpBN,EA+fgCjC,EAAQnB,GA7f3DF,QAAQ6D,IAAI,gBAAiBP,QAC7BrD,EAAAA,EAAAA,IAAM,+BAHaqD,OAggBK3B,UAAU,wFACVgB,MAAOnH,EAAE,cAAe,gBAAgBoG,UAExCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBApEdN,EAAQnB,aA+EI,IAA5BiB,EAAiB2C,SAChBpC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5CpG,EAAE,kBAAmB,6BAQjB,WAAde,IACCmF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDACZnG,EAAE,mBAAoB,0BAGzBkG,EAAAA,EAAAA,MAAA,QAAMqC,SAvVclF,UAG1B,GAFAiE,EAAEkB,iBAzBwBC,MAC1B,MAAMC,EAAY,CAAC,EAoBnB,OAlBK/G,EAAYG,YAAY6G,SAC3BD,EAAU5G,YAAc9B,EAAE,sBAAuB,6BAE9C2B,EAAYQ,UACfuG,EAAUvG,QAAUnC,EAAE,kBAAmB,yBAI3C2B,EAAYS,SAASwG,QAAQ,CAAC3D,EAASO,KAChCP,EAAQ5C,YAAYsG,SACvBD,EAAU,WAAD3E,OAAYyB,EAAK,iBAAkBxF,EAAE,6BAA8B,sCAEzEiF,EAAQ1C,WAAa0C,EAAQ1C,WAAa,KAC7CmG,EAAU,WAAD3E,OAAYyB,EAAK,eAAgBxF,EAAE,qBAAsB,8BAItEiD,EAAUyF,GAC+B,IAAlCG,OAAOC,KAAKJ,GAAWJ,QAMzBG,GAAL,CAKA/H,GAAW,GACX,IAEE,MAAMqI,EAAc,CAClBvC,QAAS7E,EAAYE,UACrBO,SAAUT,EAAYS,SAASqF,IAAIxC,IAAO,CACxCwB,KAAMxB,EAAQ5C,YACdA,YAAa4C,EAAQ5C,YACrBC,SAAU0G,SAAS/D,EAAQ3C,UAC3BC,UAAW0G,WAAWhE,EAAQ1C,WAC9BmE,KAAM/E,EAAYI,eAEpBI,QAASR,EAAYQ,QACrBW,MAAOnB,EAAYmB,MACnBoG,SAAU,CACR9B,KAAM,QACN9B,MAAO2D,WAAWtH,EAAYiB,iBAAmB,EACjDiC,OAAQoE,WAAWtH,EAAYiB,iBAAmB,GAEpDuG,IAAK,CACHC,KAAMH,WAAWtH,EAAYe,UAAY,GACzCmC,OAAQoE,WAAWtH,EAAYgB,YAAc,IAI3CkB,QAAiBC,MAAM,kBAAmB,CAC9C8C,OAAQ,OACR5C,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,oBAElB2C,KAAMC,KAAKC,UAAUgC,KAGvB,IAAIlF,EAASM,GA+BN,CACL,MAAM6C,QAAkBnD,EAASS,OACjC,MAAM,IAAIF,MAAM4C,EAAUC,SAAW,2BACvC,CAlCiB,CACf,MAAMc,QAAelE,EAASS,OAG9B1C,EAAe,CACbC,UAAW,GACXC,YAAa,GACbC,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,QAAS,GACTC,SAAU,CACR,CACEC,YAAa,GACbC,SAAU,EACVC,UAAW,GACXC,MAAO,IAGXC,SAAU,EACVC,QAAS,GACTC,UAAW,EACXC,eAAgB,EAChBC,YAAa,EACbC,MAAO,GACPC,aAAc,OAGhB0B,EAAAA,GAAMyC,QAAQa,EAAOd,SAAWjH,EAAE,6BAA8B,iCAChEgB,EAAa,YAGbmC,GACF,CAIF,CAAE,MAAOoB,GACPE,EAAAA,GAAMF,MAAMvE,EAAE,uBAAwB,0BACxC,CAAC,QACCU,GAAW,EACb,CA3EA,MAFE+D,EAAAA,GAAMF,MAAMvE,EAAE,kBAAmB,6CAmVQmG,UAAU,YAAWC,SAAA,EAExDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACxEpG,EAAE,qBAAsB,0BAE3BkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EpG,EAAE,cAAe,gBAAgB,KAACqG,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACL9B,MAAO3D,EAAYG,YACnBuF,SAAWC,IACTlC,EAAkB,cAAekC,EAAEC,OAAOjC,OA/pBzCjC,WACrB,IAAKgG,GAASA,EAAMf,OAAS,EAC3B9G,EAAwB,SAI1B,IACE,MAAMqC,QAAiBC,MAAM,6BAADC,OAA8BuF,mBAAmBD,IAAU,CACrFrF,QAAS,CACP,cAAgB,UAADD,OAAYE,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,GAAIL,EAASM,GAAI,CACf,MAAME,QAAaR,EAASS,OAC5B9C,EAAwB6C,EAAKA,MAAQ,GACvC,CACF,CAAE,MAAOE,GACPC,QAAQD,MAAM,4BAA6BA,EAC7C,GA4oBoBgF,CAAejC,EAAEC,OAAOjC,OACxB5D,GAAqB,IAEvB8H,QAASA,IAAM9H,GAAqB,GACpCyE,UAAS,mJAAApC,OACPf,EAAOlB,YAAc,iBAAmB,mBAE1C0F,YAAaxH,EAAE,mBAAoB,yBAErCqG,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAId1E,GAAqBF,EAAqB+G,OAAS,IAClDjC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gJAA+IC,SAC3J7E,EAAqBkG,IAAKjB,IACzBN,EAAAA,EAAAA,MAAA,OAEEI,QAASA,IA3pBVE,KACrB5E,EAAeuD,IAAI3B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACd2B,GAAI,IACPtD,UAAW2E,EAAQiD,IACnB3H,YAAY,GAADiC,OAAKyC,EAAQkD,UAAS,KAAA3F,OAAIyC,EAAQmD,aAE/CnI,EAAwB,IACxBE,GAAqB,IAopBgBkI,CAAcpD,GAC7BL,UAAU,kIAAiIC,SAAA,EAE3IF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4CAA2CC,SAAA,CACvDI,EAAQkD,UAAU,IAAElD,EAAQmD,aAE/BzD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtDI,EAAQqD,WAAW,WAAIrD,EAAQsD,WAR7BtD,EAAQiD,QAepBzG,EAAOlB,cACNuE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEpD,EAAOlB,kBAIrDoE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,cAAe,mBAEpBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACL9B,MAAO3D,EAAYI,YACnBsF,SAAWC,GAAMlC,EAAkB,cAAekC,EAAEC,OAAOjC,OAC3Da,UAAU,2KAKhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EpG,EAAE,UAAW,YAAY,KAACqG,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE5DC,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACL9B,MAAO3D,EAAYQ,QACnBkF,SAAWC,GAAMlC,EAAkB,UAAWkC,EAAEC,OAAOjC,OACvDa,UAAS,mJAAApC,OACPf,EAAOb,QAAU,iBAAmB,qBAGvCa,EAAOb,UACNkE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEpD,EAAOb,cAIrD+D,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,eAAgB,2BAErBkG,EAAAA,EAAAA,MAAA,UACEZ,MAAO3D,EAAYoB,aACnBsE,SAAWC,GAAMlC,EAAkB,eAAgBkC,EAAEC,OAAOjC,OAC5Da,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAIc,SAAA,CAAC,MAAIpG,EAAE,OAAQ,YACjCkG,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAIc,SAAA,CAAC,MAAIpG,EAAE,OAAQ,YACjCkG,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAIc,SAAA,CAAC,MAAIpG,EAAE,OAAQ,YACjCkG,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAIc,SAAA,CAAC,MAAIpG,EAAE,OAAQ,wBAOzCkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gGAA+FC,SAAA,EAC5GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEpG,EAAE,WAAY,eAEjBkG,EAAAA,EAAAA,MAAA,UACEkB,KAAK,SACLd,QApfGyD,KACjBnI,EAAeuD,IAAI3B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACd2B,GAAI,IACP/C,SAAU,IACL+C,EAAK/C,SACR,CACEC,YAAa,GACbC,SAAU,EACVC,UAAW,GACXC,MAAO,QA4eC2D,UAAU,4FAA2FC,SAAA,EAErGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZnG,EAAE,aAAc,sBAIrBqG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBzE,EAAYS,SAASqF,IAAI,CAACxC,EAASO,KAClCa,EAAAA,EAAAA,KAAA,OAAiBF,UAAU,uFAAsFC,UAC/GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDC,SAAA,EAC9DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EpG,EAAE,qBAAsB,uBAAuB,KAACqG,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAElFC,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACL9B,MAAOL,EAAQ5C,YACfgF,SAAWC,GAAM/B,EAAoBC,EAAO,cAAe8B,EAAEC,OAAOjC,OACpEa,UAAS,mJAAApC,OACPf,EAAO,WAADe,OAAYyB,EAAK,iBAAkB,iBAAmB,mBAE9DgC,YAAaxH,EAAE,0BAA2B,+BAE3CgD,EAAO,WAADe,OAAYyB,EAAK,mBACtBa,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEpD,EAAO,WAADe,OAAYyB,EAAK,uBAIrEU,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,WAAY,eAEjBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,SACL4C,IAAI,IACJ1E,MAAOL,EAAQ3C,SACf+E,SAAWC,GAAM/B,EAAoBC,EAAO,WAAYwD,SAAS1B,EAAEC,OAAOjC,QAAU,GACpFa,UAAU,wKAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EpG,EAAE,YAAa,cAAc,KAACqG,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEhEC,EAAAA,EAAAA,KAAA,SACEe,KAAK,SACL6C,KAAK,OACLD,IAAI,IACJ1E,MAAOL,EAAQ1C,UACf8E,SAAWC,GAAM/B,EAAoBC,EAAO,YAAayD,WAAW3B,EAAEC,OAAOjC,QAAU,GACvFa,UAAS,mJAAApC,OACPf,EAAO,WAADe,OAAYyB,EAAK,eAAgB,iBAAmB,mBAE5DgC,YAAY,SAEbxE,EAAO,WAADe,OAAYyB,EAAK,iBACtBa,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEpD,EAAO,WAADe,OAAYyB,EAAK,qBAIrEU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2BAA0BC,SAAA,EACvCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,QAAS,YAEdkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8HAA6HC,SAAA,CACzInB,EAAQzC,MAAM0H,QAAQ,GAAG,IAAElK,EAAE,MAAO,aAGxC2B,EAAYS,SAASkG,OAAS,IAC7BjC,EAAAA,EAAAA,KAAA,UACEe,KAAK,SACLd,QAASA,IAhjBZd,KACrB,GAAI7D,EAAYS,SAASkG,OAAS,EAAG,CACnC,MAAM7C,EAAkB9D,EAAYS,SAASwD,OAAO,CAACuE,EAAGC,IAAMA,IAAM5E,GACpE5D,EAAeuD,IAAI3B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAU2B,GAAI,IAAE/C,SAAUqD,IAC/C,GA4iBuC4E,CAAc7E,GAC7BW,UAAU,gFAA+EC,UAEzFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BApEbX,UA+EhBU,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kEAAiEC,SAC5EpG,EAAE,gBAAiB,qBAGtBkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,iBAAkB,sBAEvBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,SACL6C,KAAK,OACLD,IAAI,IACJ1E,MAAO3D,EAAYiB,eACnByE,SAAWC,GAAMlC,EAAkB,iBAAkB6D,WAAW3B,EAAEC,OAAOjC,QAAU,GACnFa,UAAU,kKACVqB,YAAY,aAIhBtB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,UAAW,mBAEhBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,SACL6C,KAAK,OACLD,IAAI,IACJM,IAAI,MACJhF,MAAO3D,EAAYe,QACnB2E,SAAWC,GAAMlC,EAAkB,UAAW6D,WAAW3B,EAAEC,OAAOjC,QAAU,GAC5Ea,UAAU,2KAKhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uFAAsFC,UACnGF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAAEpG,EAAE,WAAY,YAAY,QAC9EkG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4CAA2CC,SAAA,CACxDzE,EAAYc,SAASyH,QAAQ,GAAG,IAAElK,EAAE,MAAO,cAGhDkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAAEpG,EAAE,WAAY,YAAY,QAC9EkG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4CAA2CC,SAAA,CAAC,IACxDzE,EAAYiB,eAAesH,QAAQ,GAAG,IAAElK,EAAE,MAAO,cAGvDkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAAEpG,EAAE,MAAO,OAAO,KAAG2B,EAAYe,QAAQ,UAC3FwD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4CAA2CC,SAAA,CACxDzE,EAAYgB,UAAUuH,QAAQ,GAAG,IAAElK,EAAE,MAAO,cAGjDqG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,sDAAqDC,SAAA,CAAEpG,EAAE,QAAS,SAAS,QAC3FkG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,qDAAoDC,SAAA,CACjEzE,EAAYkB,YAAYqH,QAAQ,GAAG,IAAElK,EAAE,MAAO,2BAU7DkG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,QAAS,YAEdqG,EAAAA,EAAAA,KAAA,YACEf,MAAO3D,EAAYmB,MACnBuE,SAAWC,GAAMlC,EAAkB,QAASkC,EAAEC,OAAOjC,OACrDiF,KAAM,EACNpE,UAAU,kKACVqB,YAAaxH,EAAE,uBAAwB,yCAK3CkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEe,KAAK,SACLd,QAASA,IAAMjG,EAAS,sBACxB8F,UAAU,+FAA8FC,SAEvGpG,EAAE,SAAU,aAEfqG,EAAAA,EAAAA,KAAA,UACEe,KAAK,SACLoD,SAAU/J,EACV0F,UAAU,kIAAiIC,SAE1I3F,GACCyF,EAAAA,EAAAA,MAAAuE,EAAAA,SAAA,CAAArE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZnG,EAAE,WAAY,mBAGjBkG,EAAAA,EAAAA,MAAAuE,EAAAA,SAAA,CAAArE,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZnG,EAAE,gBAAiB,gCAUnB,cAAde,IACCmF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DACZnG,EAAE,mBAAoB,yBAGzBkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EAEnEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,UAC3GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wEACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEpG,EAAE,mBAAoB,wBAEzBqG,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDpG,EAAE,uBAAwB,kDAE7BqG,EAAAA,EAAAA,KAAA,UAAQF,UAAU,yFAAwFC,SACvGpG,EAAE,cAAe,wBAKxBqG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,UAC3GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0EACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEpG,EAAE,kBAAmB,uBAExBqG,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDpG,EAAE,sBAAuB,gDAE5BqG,EAAAA,EAAAA,KAAA,UAAQF,UAAU,2FAA0FC,SACzGpG,EAAE,cAAe,wBAKxBqG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,+FAA8FC,UAC3GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEpG,EAAE,iBAAkB,sBAEvBqG,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDpG,EAAE,qBAAsB,uCAE3BqG,EAAAA,EAAAA,KAAA,UAAQF,UAAU,6FAA4FC,SAC3GpG,EAAE,YAAa,2BASb,aAAde,IACCmF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qDACZnG,EAAE,kBAAmB,wBAGxBkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACxEpG,EAAE,qBAAsB,0BAE3BkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,cAAe,mBAEpBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACLjB,UAAU,kKACVqB,YAAaxH,EAAE,mBAAoB,4BAGvCkG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,YAAa,iBAElBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACLjB,UAAU,kKACVqB,YAAaxH,EAAE,iBAAkB,gCAOzCkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gEAA+DC,SAC1EpG,EAAE,kBAAmB,uBAExBkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,iBAAkB,2BAEvBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,SACL6C,KAAK,OACLS,aAAa,KACbvE,UAAU,wKAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,sBAAuB,mCAE5BkG,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kKAAiKC,SAAA,EACjLF,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAIc,SAAA,CAAC,MAAIpG,EAAE,OAAQ,YACjCkG,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAKqF,UAAQ,EAAAvE,SAAA,CAAC,MAAIpG,EAAE,OAAQ,YAC1CkG,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAIc,SAAA,CAAC,MAAIpG,EAAE,OAAQ,YACjCkG,EAAAA,EAAAA,MAAA,UAAQZ,MAAM,KAAIc,SAAA,CAAC,MAAIpG,EAAE,OAAQ,kBAGrCkG,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EpG,EAAE,gBAAiB,qBAEtBqG,EAAAA,EAAAA,KAAA,SACEe,KAAK,OACLsD,aAAa,OACbvE,UAAU,8KAOlBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZnG,EAAE,eAAgB,+B", "sources": ["pages/Financial/BillingPage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst BillingPage = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n\n  // Check if we're on the \"new\" route to auto-open the invoice form\n  const isNewInvoice = location.pathname.includes('/new');\n  const [activeTab, setActiveTab] = useState(isNewInvoice ? 'create' : 'invoices');\n  const [invoices, setInvoices] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [patientSearchResults, setPatientSearchResults] = useState([]);\n  const [showPatientSearch, setShowPatientSearch] = useState(false);\n\n  // Invoice form state\n  const [invoiceForm, setInvoiceForm] = useState({\n    patientId: '',\n    patientName: '',\n    serviceDate: new Date().toISOString().split('T')[0],\n    dueDate: '',\n    services: [\n      {\n        description: '',\n        quantity: 1,\n        unitPrice: '',\n        total: 0\n      }\n    ],\n    subtotal: 0,\n    taxRate: 15, // VAT rate in Saudi Arabia\n    taxAmount: 0,\n    discountAmount: 0,\n    totalAmount: 0,\n    notes: '',\n    paymentTerms: '30'\n  });\n\n  const [errors, setErrors] = useState({});\n\n  useEffect(() => {\n    loadInvoices();\n  }, []);\n\n  useEffect(() => {\n    calculateInvoiceTotals();\n  }, [invoiceForm.services, invoiceForm.discountAmount, invoiceForm.taxRate]);\n\n  const searchPatients = async (query) => {\n    if (!query || query.length < 2) {\n      setPatientSearchResults([]);\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/v1/patients/search?q=${encodeURIComponent(query)}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setPatientSearchResults(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error searching patients:', error);\n    }\n  };\n\n  const selectPatient = (patient) => {\n    setInvoiceForm(prev => ({\n      ...prev,\n      patientId: patient._id,\n      patientName: `${patient.firstName} ${patient.lastName}`\n    }));\n    setPatientSearchResults([]);\n    setShowPatientSearch(false);\n  };\n\n  const viewInvoice = (invoiceId) => {\n    // Navigate to invoice detail view\n    navigate(`/financial/invoice/${invoiceId}`);\n  };\n\n  const sendInvoice = async (invoiceId) => {\n    try {\n      const response = await fetch(`/api/v1/billing/${invoiceId}/send`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        toast.success(result.message || 'Invoice sent successfully');\n        loadInvoices(); // Reload to update status\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to send invoice');\n      }\n    } catch (error) {\n      console.error('Error sending invoice:', error);\n      toast.error(error.message);\n    }\n  };\n\n  const downloadInvoice = async (invoiceId) => {\n    try {\n      const response = await fetch(`/api/v1/billing/${invoiceId}/pdf`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        // For now, just show a success message\n        toast.success('PDF download will be available soon');\n      } else {\n        throw new Error('Failed to generate PDF');\n      }\n    } catch (error) {\n      console.error('Error downloading invoice:', error);\n      toast.error('Failed to download invoice');\n    }\n  };\n\n  const markAsPaid = async (invoiceId) => {\n    try {\n      const response = await fetch(`/api/v1/billing/${invoiceId}/mark-paid`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          paymentMethod: 'cash',\n          notes: 'Marked as paid from billing page'\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        toast.success(result.message || 'Invoice marked as paid');\n        loadInvoices(); // Reload to update status\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to mark as paid');\n      }\n    } catch (error) {\n      console.error('Error marking as paid:', error);\n      toast.error(error.message);\n    }\n  };\n\n  const editInvoice = (invoiceId) => {\n    // Switch to edit mode\n    console.log('Edit invoice:', invoiceId);\n    toast('Invoice editing coming soon');\n  };\n\n  const createTestInvoice = async () => {\n    try {\n      // Create a test invoice for demo purposes\n      const testInvoiceData = {\n        patient: '507f1f77bcf86cd799439011', // Mock patient ID\n        services: [\n          {\n            name: 'Test Physical Therapy Session',\n            description: 'Demo session for testing',\n            quantity: 1,\n            unitPrice: 500,\n            date: new Date().toISOString().split('T')[0]\n          }\n        ],\n        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now\n        notes: 'Test invoice created for demonstration'\n      };\n\n      const response = await fetch('/api/v1/billing', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(testInvoiceData)\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        toast.success('Test invoice created successfully');\n        loadInvoices(); // Reload invoices\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create test invoice');\n      }\n    } catch (error) {\n      console.error('Error creating test invoice:', error);\n      toast.error('Failed to create test invoice');\n    }\n  };\n\n  const loadInvoices = async () => {\n    setLoading(true);\n    try {\n      const queryParams = new URLSearchParams({\n        page: 1,\n        limit: 50,\n        ...(searchTerm && { search: searchTerm }),\n        ...(filterStatus !== 'all' && { status: filterStatus })\n      });\n\n      const response = await fetch(`/api/v1/billing?${queryParams}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setInvoices(data.data || []);\n      } else {\n        throw new Error('Failed to load invoices');\n      }\n    } catch (error) {\n      console.error('Error loading invoices:', error);\n      toast.error('Failed to load invoices');\n      // Fallback to mock data for development\n      const mockInvoices = [\n        {\n          id: 'INV-2024-001',\n          patientName: 'أحمد محمد علي',\n          patientNameEn: 'Ahmed Mohammed Ali',\n          issueDate: '2024-01-15',\n          dueDate: '2024-02-14',\n          amount: 2500,\n          status: 'paid',\n          services: ['Physical Therapy Session', 'Assessment'],\n          paymentDate: '2024-01-20'\n        },\n        {\n          id: 'INV-2024-002',\n          patientName: 'فاطمة أحمد',\n          patientNameEn: 'Fatima Ahmed',\n          issueDate: '2024-01-14',\n          dueDate: '2024-02-13',\n          amount: 1800,\n          status: 'pending',\n          services: ['Initial Assessment', 'Treatment Plan']\n        },\n        {\n          id: 'INV-2024-003',\n          patientName: 'محمد سالم',\n          patientNameEn: 'Mohammed Salem',\n          issueDate: '2024-01-13',\n          dueDate: '2024-02-12',\n          amount: 3200,\n          status: 'overdue',\n          services: ['Treatment Package', 'Follow-up Sessions']\n        }\n      ];\n      setInvoices(mockInvoices);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateInvoiceTotals = () => {\n    const subtotal = invoiceForm.services.reduce((sum, service) => {\n      const serviceTotal = (service.quantity || 0) * (service.unitPrice || 0);\n      return sum + serviceTotal;\n    }, 0);\n\n    const discountAmount = invoiceForm.discountAmount || 0;\n    const taxableAmount = subtotal - discountAmount;\n    const taxAmount = (taxableAmount * (invoiceForm.taxRate || 0)) / 100;\n    const totalAmount = taxableAmount + taxAmount;\n\n    setInvoiceForm(prev => ({\n      ...prev,\n      subtotal,\n      taxAmount,\n      totalAmount\n    }));\n  };\n\n  const handleInputChange = (field, value) => {\n    setInvoiceForm(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleServiceChange = (index, field, value) => {\n    const updatedServices = [...invoiceForm.services];\n    updatedServices[index] = {\n      ...updatedServices[index],\n      [field]: value\n    };\n\n    // Calculate service total\n    if (field === 'quantity' || field === 'unitPrice') {\n      const quantity = field === 'quantity' ? value : updatedServices[index].quantity;\n      const unitPrice = field === 'unitPrice' ? value : updatedServices[index].unitPrice;\n      updatedServices[index].total = (quantity || 0) * (unitPrice || 0);\n    }\n\n    setInvoiceForm(prev => ({ ...prev, services: updatedServices }));\n  };\n\n  const addService = () => {\n    setInvoiceForm(prev => ({\n      ...prev,\n      services: [\n        ...prev.services,\n        {\n          description: '',\n          quantity: 1,\n          unitPrice: '',\n          total: 0\n        }\n      ]\n    }));\n  };\n\n  const removeService = (index) => {\n    if (invoiceForm.services.length > 1) {\n      const updatedServices = invoiceForm.services.filter((_, i) => i !== index);\n      setInvoiceForm(prev => ({ ...prev, services: updatedServices }));\n    }\n  };\n\n  const validateInvoiceForm = () => {\n    const newErrors = {};\n\n    if (!invoiceForm.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n    if (!invoiceForm.dueDate) {\n      newErrors.dueDate = t('dueDateRequired', 'Due date is required');\n    }\n    \n    // Validate services\n    invoiceForm.services.forEach((service, index) => {\n      if (!service.description.trim()) {\n        newErrors[`service_${index}_description`] = t('serviceDescriptionRequired', 'Service description is required');\n      }\n      if (!service.unitPrice || service.unitPrice <= 0) {\n        newErrors[`service_${index}_unitPrice`] = t('validPriceRequired', 'Valid price is required');\n      }\n    });\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmitInvoice = async (e) => {\n    e.preventDefault();\n    \n    if (!validateInvoiceForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Prepare billing data\n      const billingData = {\n        patient: invoiceForm.patientId,\n        services: invoiceForm.services.map(service => ({\n          name: service.description,\n          description: service.description,\n          quantity: parseInt(service.quantity),\n          unitPrice: parseFloat(service.unitPrice),\n          date: invoiceForm.serviceDate\n        })),\n        dueDate: invoiceForm.dueDate,\n        notes: invoiceForm.notes,\n        discount: {\n          type: 'fixed',\n          value: parseFloat(invoiceForm.discountAmount) || 0,\n          amount: parseFloat(invoiceForm.discountAmount) || 0\n        },\n        tax: {\n          rate: parseFloat(invoiceForm.taxRate) || 15,\n          amount: parseFloat(invoiceForm.taxAmount) || 0\n        }\n      };\n\n      const response = await fetch('/api/v1/billing', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(billingData)\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n\n        // Reset form\n        setInvoiceForm({\n          patientId: '',\n          patientName: '',\n          serviceDate: new Date().toISOString().split('T')[0],\n          dueDate: '',\n          services: [\n            {\n              description: '',\n              quantity: 1,\n              unitPrice: '',\n              total: 0\n            }\n          ],\n          subtotal: 0,\n          taxRate: 15,\n          taxAmount: 0,\n          discountAmount: 0,\n          totalAmount: 0,\n          notes: '',\n          paymentTerms: '30'\n        });\n\n        toast.success(result.message || t('invoiceCreatedSuccessfully', 'Invoice created successfully'));\n        setActiveTab('invoices');\n\n        // Reload invoices\n        loadInvoices();\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create invoice');\n      }\n    } catch (error) {\n      toast.error(t('errorCreatingInvoice', 'Error creating invoice'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'paid':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'overdue':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      case 'cancelled':\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n      default:\n        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400';\n    }\n  };\n\n  const filteredInvoices = invoices.filter(invoice => {\n    const matchesSearch = invoice.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         invoice.patientNameEn?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         invoice.id.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || invoice.status === filterStatus;\n    return matchesSearch && matchesStatus;\n  });\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          {t('billingManagement', 'Billing Management')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          {t('billingManagementDesc', 'Create invoices, manage billing, and track payments')}\n        </p>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-600\">\n          <nav className=\"-mb-px flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('invoices')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'invoices'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-file-invoice mr-2\"></i>\n              {t('invoices', 'Invoices')}\n            </button>\n            <button\n              onClick={() => setActiveTab('create')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'create'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('createInvoice', 'Create Invoice')}\n            </button>\n            <button\n              onClick={() => setActiveTab('templates')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'templates'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-file-alt mr-2\"></i>\n              {t('templates', 'Templates')}\n            </button>\n            <button\n              onClick={createTestInvoice}\n              className=\"py-2 px-3 ml-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\"\n              title=\"Create a test invoice for demo purposes\"\n            >\n              <i className=\"fas fa-flask mr-2\"></i>\n              Test Invoice\n            </button>\n            <button\n              onClick={() => setActiveTab('settings')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'settings'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-cog mr-2\"></i>\n              {t('settings', 'Settings')}\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'invoices' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n            <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                <i className=\"fas fa-file-invoice text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('invoiceList', 'Invoice List')}\n              </h2>\n              \n              <div className=\"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    placeholder={t('searchInvoices', 'Search invoices...')}\n                    className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  />\n                  <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n                </div>\n                \n                <select\n                  value={filterStatus}\n                  onChange={(e) => setFilterStatus(e.target.value)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                >\n                  <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n                  <option value=\"paid\">{t('paid', 'Paid')}</option>\n                  <option value=\"pending\">{t('pending', 'Pending')}</option>\n                  <option value=\"overdue\">{t('overdue', 'Overdue')}</option>\n                  <option value=\"cancelled\">{t('cancelled', 'Cancelled')}</option>\n                </select>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('invoiceNumber', 'Invoice #')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('patient', 'Patient')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('amount', 'Amount')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('issueDate', 'Issue Date')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('dueDate', 'Due Date')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('status', 'Status')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('actions', 'Actions')}\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                {filteredInvoices.map((invoice) => (\n                  <tr key={invoice.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {invoice.id}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {isRTL ? invoice.patientName : invoice.patientNameEn}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {invoice.services?.join(', ')}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {invoice.amount.toLocaleString()} {t('sar', 'SAR')}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {invoice.issueDate}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {invoice.dueDate}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(invoice.status)}`}>\n                        {t(invoice.status, invoice.status)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button\n                          onClick={() => viewInvoice(invoice.id)}\n                          className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200\"\n                          title={t('viewInvoice', 'View Invoice')}\n                        >\n                          <i className=\"fas fa-eye\"></i>\n                        </button>\n                        <button\n                          onClick={() => sendInvoice(invoice.id)}\n                          className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200\"\n                          title={t('sendInvoice', 'Send Invoice')}\n                        >\n                          <i className=\"fas fa-paper-plane\"></i>\n                        </button>\n                        <button\n                          onClick={() => downloadInvoice(invoice.id)}\n                          className=\"text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-200\"\n                          title={t('downloadPDF', 'Download PDF')}\n                        >\n                          <i className=\"fas fa-download\"></i>\n                        </button>\n                        {(invoice.status === 'pending' || invoice.status === 'sent') && (\n                          <button\n                            onClick={() => markAsPaid(invoice.id)}\n                            className=\"text-emerald-600 hover:text-emerald-900 dark:text-emerald-400 dark:hover:text-emerald-200\"\n                            title={t('markAsPaid', 'Mark as Paid')}\n                          >\n                            <i className=\"fas fa-check-circle\"></i>\n                          </button>\n                        )}\n                        {invoice.status === 'pending' && (\n                          <button\n                            onClick={() => editInvoice(invoice.id)}\n                            className=\"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-200\"\n                            title={t('editInvoice', 'Edit Invoice')}\n                          >\n                            <i className=\"fas fa-edit\"></i>\n                          </button>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n          \n          {filteredInvoices.length === 0 && (\n            <div className=\"text-center py-12\">\n              <i className=\"fas fa-file-invoice text-4xl text-gray-400 mb-4\"></i>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                {t('noInvoicesFound', 'No invoices found')}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Create Invoice Tab */}\n      {activeTab === 'create' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-plus text-green-600 dark:text-green-400 mr-2\"></i>\n            {t('createNewInvoice', 'Create New Invoice')}\n          </h2>\n\n          <form onSubmit={handleSubmitInvoice} className=\"space-y-6\">\n            {/* Patient Information */}\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n                {t('patientInformation', 'Patient Information')}\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"relative\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <div className=\"relative\">\n                    <input\n                      type=\"text\"\n                      value={invoiceForm.patientName}\n                      onChange={(e) => {\n                        handleInputChange('patientName', e.target.value);\n                        searchPatients(e.target.value);\n                        setShowPatientSearch(true);\n                      }}\n                      onFocus={() => setShowPatientSearch(true)}\n                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                        errors.patientName ? 'border-red-500' : 'border-gray-300'\n                      }`}\n                      placeholder={t('enterPatientName', 'Enter patient name')}\n                    />\n                    <i className=\"fas fa-search absolute right-3 top-3 text-gray-400\"></i>\n                  </div>\n\n                  {/* Patient Search Results */}\n                  {showPatientSearch && patientSearchResults.length > 0 && (\n                    <div className=\"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto\">\n                      {patientSearchResults.map((patient) => (\n                        <div\n                          key={patient._id}\n                          onClick={() => selectPatient(patient)}\n                          className=\"px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0\"\n                        >\n                          <div className=\"font-medium text-gray-900 dark:text-white\">\n                            {patient.firstName} {patient.lastName}\n                          </div>\n                          <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                            {patient.nationalId} • {patient.phone}\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n\n                  {errors.patientName && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('serviceDate', 'Service Date')}\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={invoiceForm.serviceDate}\n                    onChange={(e) => handleInputChange('serviceDate', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('dueDate', 'Due Date')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={invoiceForm.dueDate}\n                    onChange={(e) => handleInputChange('dueDate', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.dueDate ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                  />\n                  {errors.dueDate && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.dueDate}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('paymentTerms', 'Payment Terms (Days)')}\n                  </label>\n                  <select\n                    value={invoiceForm.paymentTerms}\n                    onChange={(e) => handleInputChange('paymentTerms', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"15\">15 {t('days', 'Days')}</option>\n                    <option value=\"30\">30 {t('days', 'Days')}</option>\n                    <option value=\"45\">45 {t('days', 'Days')}</option>\n                    <option value=\"60\">60 {t('days', 'Days')}</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* Services */}\n            <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-md font-semibold text-green-900 dark:text-green-100\">\n                  {t('services', 'Services')}\n                </h3>\n                <button\n                  type=\"button\"\n                  onClick={addService}\n                  className=\"px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\"\n                >\n                  <i className=\"fas fa-plus mr-1\"></i>\n                  {t('addService', 'Add Service')}\n                </button>\n              </div>\n\n              <div className=\"space-y-4\">\n                {invoiceForm.services.map((service, index) => (\n                  <div key={index} className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-5 gap-4 items-end\">\n                      <div className=\"md:col-span-2\">\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('serviceDescription', 'Service Description')} <span className=\"text-red-500\">*</span>\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={service.description}\n                          onChange={(e) => handleServiceChange(index, 'description', e.target.value)}\n                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                            errors[`service_${index}_description`] ? 'border-red-500' : 'border-gray-300'\n                          }`}\n                          placeholder={t('enterServiceDescription', 'Enter service description')}\n                        />\n                        {errors[`service_${index}_description`] && (\n                          <p className=\"text-red-500 text-sm mt-1\">{errors[`service_${index}_description`]}</p>\n                        )}\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('quantity', 'Quantity')}\n                        </label>\n                        <input\n                          type=\"number\"\n                          min=\"1\"\n                          value={service.quantity}\n                          onChange={(e) => handleServiceChange(index, 'quantity', parseInt(e.target.value) || 1)}\n                          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                        />\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                          {t('unitPrice', 'Unit Price')} <span className=\"text-red-500\">*</span>\n                        </label>\n                        <input\n                          type=\"number\"\n                          step=\"0.01\"\n                          min=\"0\"\n                          value={service.unitPrice}\n                          onChange={(e) => handleServiceChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}\n                          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                            errors[`service_${index}_unitPrice`] ? 'border-red-500' : 'border-gray-300'\n                          }`}\n                          placeholder=\"0.00\"\n                        />\n                        {errors[`service_${index}_unitPrice`] && (\n                          <p className=\"text-red-500 text-sm mt-1\">{errors[`service_${index}_unitPrice`]}</p>\n                        )}\n                      </div>\n\n                      <div className=\"flex items-end space-x-2\">\n                        <div className=\"flex-1\">\n                          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                            {t('total', 'Total')}\n                          </label>\n                          <div className=\"px-3 py-2 bg-gray-100 dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg text-gray-900 dark:text-white\">\n                            {service.total.toFixed(2)} {t('sar', 'SAR')}\n                          </div>\n                        </div>\n                        {invoiceForm.services.length > 1 && (\n                          <button\n                            type=\"button\"\n                            onClick={() => removeService(index)}\n                            className=\"p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200\"\n                          >\n                            <i className=\"fas fa-trash\"></i>\n                          </button>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Invoice Totals */}\n            <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-purple-900 dark:text-purple-100 mb-4\">\n                {t('invoiceTotals', 'Invoice Totals')}\n              </h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('discountAmount', 'Discount Amount')}\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      value={invoiceForm.discountAmount}\n                      onChange={(e) => handleInputChange('discountAmount', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                      placeholder=\"0.00\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('taxRate', 'Tax Rate (%)')}\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      max=\"100\"\n                      value={invoiceForm.taxRate}\n                      onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">{t('subtotal', 'Subtotal')}:</span>\n                      <span className=\"font-medium text-gray-900 dark:text-white\">\n                        {invoiceForm.subtotal.toFixed(2)} {t('sar', 'SAR')}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">{t('discount', 'Discount')}:</span>\n                      <span className=\"font-medium text-gray-900 dark:text-white\">\n                        -{invoiceForm.discountAmount.toFixed(2)} {t('sar', 'SAR')}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span className=\"text-gray-600 dark:text-gray-400\">{t('tax', 'Tax')} ({invoiceForm.taxRate}%):</span>\n                      <span className=\"font-medium text-gray-900 dark:text-white\">\n                        {invoiceForm.taxAmount.toFixed(2)} {t('sar', 'SAR')}\n                      </span>\n                    </div>\n                    <div className=\"border-t border-gray-200 dark:border-gray-600 pt-2\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">{t('total', 'Total')}:</span>\n                        <span className=\"text-lg font-bold text-blue-600 dark:text-blue-400\">\n                          {invoiceForm.totalAmount.toFixed(2)} {t('sar', 'SAR')}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Notes */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('notes', 'Notes')}\n              </label>\n              <textarea\n                value={invoiceForm.notes}\n                onChange={(e) => handleInputChange('notes', e.target.value)}\n                rows={3}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder={t('enterAdditionalNotes', 'Enter additional notes or terms')}\n              />\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                type=\"button\"\n                onClick={() => navigate('/financial/billing')}\n                className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {loading ? (\n                  <>\n                    <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                    {t('creating', 'Creating...')}\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-file-invoice mr-2\"></i>\n                    {t('createInvoice', 'Create Invoice')}\n                  </>\n                )}\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Templates Tab */}\n      {activeTab === 'templates' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-file-alt text-purple-600 dark:text-purple-400 mr-2\"></i>\n            {t('invoiceTemplates', 'Invoice Templates')}\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {/* Template placeholders */}\n            <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"text-center\">\n                <i className=\"fas fa-file-invoice text-4xl text-blue-600 dark:text-blue-400 mb-4\"></i>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                  {t('standardTemplate', 'Standard Template')}\n                </h3>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  {t('standardTemplateDesc', 'Basic invoice template for general services')}\n                </p>\n                <button className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                  {t('useTemplate', 'Use Template')}\n                </button>\n              </div>\n            </div>\n\n            <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"text-center\">\n                <i className=\"fas fa-file-medical text-4xl text-green-600 dark:text-green-400 mb-4\"></i>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                  {t('medicalTemplate', 'Medical Template')}\n                </h3>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  {t('medicalTemplateDesc', 'Specialized template for medical services')}\n                </p>\n                <button className=\"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\">\n                  {t('useTemplate', 'Use Template')}\n                </button>\n              </div>\n            </div>\n\n            <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow\">\n              <div className=\"text-center\">\n                <i className=\"fas fa-plus text-4xl text-gray-400 mb-4\"></i>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n                  {t('createTemplate', 'Create Template')}\n                </h3>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  {t('createTemplateDesc', 'Create a custom invoice template')}\n                </p>\n                <button className=\"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\">\n                  {t('createNew', 'Create New')}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Settings Tab */}\n      {activeTab === 'settings' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-cog text-gray-600 dark:text-gray-400 mr-2\"></i>\n            {t('billingSettings', 'Billing Settings')}\n          </h2>\n\n          <div className=\"space-y-6\">\n            {/* Company Information */}\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n                {t('companyInformation', 'Company Information')}\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('companyName', 'Company Name')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder={t('enterCompanyName', 'Enter company name')}\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('taxNumber', 'Tax Number')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder={t('enterTaxNumber', 'Enter tax number')}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Default Settings */}\n            <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-green-900 dark:text-green-100 mb-4\">\n                {t('defaultSettings', 'Default Settings')}\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('defaultTaxRate', 'Default Tax Rate (%)')}\n                  </label>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    defaultValue=\"15\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('defaultPaymentTerms', 'Default Payment Terms (Days)')}\n                  </label>\n                  <select className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\">\n                    <option value=\"15\">15 {t('days', 'Days')}</option>\n                    <option value=\"30\" selected>30 {t('days', 'Days')}</option>\n                    <option value=\"45\">45 {t('days', 'Days')}</option>\n                    <option value=\"60\">60 {t('days', 'Days')}</option>\n                  </select>\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('invoicePrefix', 'Invoice Prefix')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    defaultValue=\"INV-\"\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Save Settings */}\n            <div className=\"flex justify-end\">\n              <button className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveSettings', 'Save Settings')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default BillingPage;\n"], "names": ["BillingPage", "t", "isRTL", "useLanguage", "user", "useAuth", "navigate", "useNavigate", "location", "useLocation", "loading", "setLoading", "useState", "isNewInvoice", "pathname", "includes", "activeTab", "setActiveTab", "invoices", "setInvoices", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "patientSearchResults", "setPatientSearchResults", "showPatientSearch", "setShowPatientSearch", "invoiceForm", "setInvoiceForm", "patientId", "patientName", "serviceDate", "Date", "toISOString", "split", "dueDate", "services", "description", "quantity", "unitPrice", "total", "subtotal", "taxRate", "taxAmount", "discountAmount", "totalAmount", "notes", "paymentTerms", "errors", "setErrors", "useEffect", "loadInvoices", "calculateInvoiceTotals", "async", "queryParams", "URLSearchParams", "_objectSpread", "page", "limit", "search", "status", "response", "fetch", "concat", "headers", "localStorage", "getItem", "ok", "Error", "data", "json", "error", "console", "toast", "id", "patientNameEn", "issueDate", "amount", "paymentDate", "reduce", "sum", "service", "taxableAmount", "prev", "handleInputChange", "field", "value", "handleServiceChange", "index", "updatedServices", "getStatusColor", "filteredInvoices", "filter", "invoice", "_invoice$patientNameE", "matchesSearch", "toLowerCase", "matchesStatus", "_jsxs", "className", "children", "_jsx", "onClick", "testInvoiceData", "patient", "name", "date", "now", "method", "body", "JSON", "stringify", "errorData", "message", "success", "title", "type", "onChange", "e", "target", "placeholder", "map", "_invoice$services", "join", "toLocaleString", "viewInvoice", "invoiceId", "result", "sendInvoice", "downloadInvoice", "paymentMethod", "markAsPaid", "editInvoice", "log", "length", "onSubmit", "preventDefault", "validateInvoiceForm", "newErrors", "trim", "for<PERSON>ach", "Object", "keys", "billingData", "parseInt", "parseFloat", "discount", "tax", "rate", "query", "encodeURIComponent", "searchPatients", "onFocus", "_id", "firstName", "lastName", "selectPatient", "nationalId", "phone", "addService", "min", "step", "toFixed", "_", "i", "removeService", "max", "rows", "disabled", "_Fragment", "defaultValue", "selected"], "sourceRoot": ""}