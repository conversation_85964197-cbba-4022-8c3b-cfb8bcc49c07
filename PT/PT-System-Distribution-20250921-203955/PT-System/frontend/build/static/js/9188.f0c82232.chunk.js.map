{"version": 3, "file": "static/js/9188.f0c82232.chunk.js", "mappings": "2OAQA,MA2UA,EA3UuBA,KACrB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,YACpCC,EAAiBC,IAAsBF,EAAAA,EAAAA,UAAS,CACrDG,GAAI,mBACJC,KAAM,iBACNC,OAAQ,oDACRC,IAAK,GACLC,UAAW,iBACXC,YAAa,8EAGTC,EAAO,CACX,CACEN,GAAI,UACJO,MAAOf,EAAE,qBAAsB,wBAC/BgB,KAAM,iBACNC,MAAO,QAET,CACET,GAAI,gBACJO,MAAOf,EAAE,mBAAoB,qBAC7BgB,KAAM,kBACNC,MAAO,SAET,CACET,GAAI,YACJO,MAAOf,EAAE,kBAAmB,oBAC5BgB,KAAM,kBACNC,MAAO,UAET,CACET,GAAI,KACJO,MAAOf,EAAE,cAAe,gBACxBgB,KAAM,eACNC,MAAO,UAET,CACET,GAAI,YACJO,MAAOf,EAAE,cAAe,gBACxBgB,KAAM,oBACNC,MAAO,WAmBLC,EAAqBA,CAACD,EAAOE,KACjC,MAAMC,EAAS,CACbC,KAAMF,EAAW,yBAA2B,iCAC5CG,MAAOH,EAAW,0BAA4B,mCAC9CI,OAAQJ,EAAW,2BAA6B,qCAChDK,OAAQL,EAAW,2BAA6B,qCAChDM,OAAQN,EAAW,2BAA6B,sCAElD,OAAOC,EAAOH,IAAUG,EAAOC,MAGjC,OACEK,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2DAA0DC,SAAA,EAEvEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sCAAqCC,UAClDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCAAuC,sBAGtDE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzD5B,EAAE,eAAgB,8DAGvB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,+EAA8EC,SAAA,EAC3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ1B,EAAQK,EAAgBI,OAASJ,EAAgBG,SAEpDiB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6FAA4FC,SAAA,CACxG5B,EAAE,MAAO,OAAO,KAAGM,EAAgBK,QAEtCkB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6FAA4FC,SACxG3B,EAAQK,EAAgBO,YAAcP,EAAgBM,yBASnEiB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0EAAyEC,UACtFC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,yCAAwCC,UACrDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iCAAgCC,SAC5Cd,EAAKgB,IAAKC,IACTL,EAAAA,EAAAA,MAAA,UAEEM,QAASA,IAAM5B,EAAa2B,EAAIvB,IAChCmB,UAAS,sJAAAM,OAEL9B,IAAc4B,EAAIvB,GAAE,UAAAyB,OACRF,EAAId,MAAK,SAAAgB,OAAQf,EAAmBa,EAAId,OAAO,IAAK,sBAAAgB,OACxCf,EAAmBa,EAAId,OAAO,IAAQ,sBAEhEW,SAAA,EAEFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAM,OAAKF,EAAIf,KAAI,WACxBe,EAAIhB,QAXAgB,EAAIvB,YAmBnBkB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,CAE3C,YAAdzB,IACC0B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sCACZ3B,EAAE,qBAAsB,4BAE3B6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5B,EAAE,qBAAsB,yFAG7B0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uEAAsEC,SAAA,EACpFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iCACZ3B,EAAE,aAAc,kBAEnB0B,EAAAA,EAAAA,MAAA,QAAMC,UAAU,yEAAwEC,SAAA,EACtFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZ3B,EAAE,eAAgB,0BAIzB6B,EAAAA,EAAAA,KAACK,EAAAA,EAAO,CACNC,UAAW7B,EAAgBE,GAC3B4B,OAhHaC,IACzBC,QAAQC,IAAI,iBAAkBF,GAC9BG,EAAAA,GAAMC,QAAQzC,EAAE,sBAAuB,wCA+G3B0C,kBAAkB,SAOX,kBAAdvC,IACC0B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCACZ3B,EAAE,mBAAoB,uCAEzB6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5B,EAAE,2BAA4B,iGAGnC0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,yEAAwEC,SAAA,EACtFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ3B,EAAE,eAAgB,qBAErB0B,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2EAA0EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ3B,EAAE,YAAa,uBAItB6B,EAAAA,EAAAA,KAACc,EAAAA,EAAoB,CACnBR,UAAW7B,EAAgBE,GAC3BoC,OA7ImBC,IAC/BP,QAAQC,IAAI,sBAAuBM,GACnCL,EAAAA,GAAMC,QAAQzC,EAAE,cAAe,sCAkJZ,cAAdG,IACC0B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCACZ3B,EAAE,kBAAmB,0CAExB6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5B,EAAE,sBAAuB,sFAG9B0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2EAA0EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBAAwB,QAC/B3B,EAAE,YAAa,iBAEvB0B,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2EAA0EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ3B,EAAE,WAAY,sBAIrB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE5B,EAAE,kBAAmB,8BAExB6B,EAAAA,EAAAA,KAACiB,EAAAA,EAAe,CACdX,UAAW7B,EAAgBE,GAC3BuC,oBAAoB,QAGxBrB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE5B,EAAE,gBAAiB,8BAEtB6B,EAAAA,EAAAA,KAACmB,EAAAA,EAAsB,CACrBb,UAAW7B,EAAgBE,GAC3B4B,OAvLSa,IACzBX,QAAQC,IAAI,yBAA0BU,GACtCT,EAAAA,GAAMC,QAAQzC,EAAE,uBAAwB,sDA8LrB,OAAdG,IACC0B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sCACZ3B,EAAE,cAAe,qCAEpB6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5B,EAAE,gBAAiB,kGAGxB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2EAA0EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAAyB,gBAGxCD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uEAAsEC,SAAA,EACpFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBAAwB,cAGvCD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uEAAsEC,SAAA,EACpFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAAyB,sBAK5CD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDACbE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrE5B,EAAE,oBAAqB,6BAE1B6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5B,EAAE,kBAAmB,kGAExB6B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6GAA4GC,UACzHF,EAAAA,EAAAA,MAAA,KAAGC,UAAU,2CAA0CC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZ3B,EAAE,iBAAkB,8FASlB,cAAdG,IACC0B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CACZ3B,EAAE,cAAe,8BAEpB6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD5B,EAAE,uBAAwB,sEAG/B0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2EAA0EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZ3B,EAAE,WAAY,iBAEjB0B,EAAAA,EAAAA,MAAA,QAAMC,UAAU,yEAAwEC,SAAA,EACtFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZ3B,EAAE,WAAY,sBAIrB6B,EAAAA,EAAAA,KAACqB,EAAAA,EAAoB,aAO7BrB,EAAAA,EAAAA,KAACsB,EAAAA,EAAW,CAACC,YAAa9C,O,6EC1UhC,MA6YA,EA7Y6B+C,IAA4C,IAA3C,UAAElB,EAAS,OAAES,EAAM,UAAEU,EAAY,IAAID,EACjE,MAAM,EAAErD,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdqD,EAAkBC,IAAuBnD,EAAAA,EAAAA,UAAS,CAAC,WACnDoD,EAASC,IAAcrD,EAAAA,EAAAA,UAAS,KAChCsD,EAASC,IAAcvD,EAAAA,EAAAA,UAAS,KAChCwD,EAAkBC,IAAuBzD,EAAAA,EAAAA,UAAS,KAClD0D,EAASC,IAAc3D,EAAAA,EAAAA,UAAS,WAChC4D,EAAeC,IAAoB7D,EAAAA,EAAAA,UAAS,KAC5C8D,EAAoBC,IAAyB/D,EAAAA,EAAAA,UAAS,CAAC,GAGxDgE,EAAW,CACf,CACE7D,GAAI,QACJC,KAAMT,EAAE,QAAS,SACjBgB,KAAM,kBACNC,MAAO,OACPqD,WAAW,EACXC,YAAavE,EAAE,YAAa,mBAE9B,CACEQ,GAAI,MACJC,KAAMT,EAAE,MAAO,OACfgB,KAAM,aACNC,MAAO,QACPqD,WAAW,EACXC,YAAavE,EAAE,UAAW,iBAE5B,CACEQ,GAAI,WACJC,KAAMT,EAAE,WAAY,YACpBgB,KAAM,kBACNC,MAAO,QACPqD,WAAW,EACXC,YAAavE,EAAE,eAAgB,sBAEjC,CACEQ,GAAI,OACJC,KAAMT,EAAE,mBAAoB,qBAC5BgB,KAAM,cACNC,MAAO,SACPqD,WAAW,EACXC,YAAavE,EAAE,WAAY,yCAE7B,CACEQ,GAAI,WACJC,KAAMT,EAAE,WAAY,YACpBgB,KAAM,kBACNC,MAAO,OACPqD,WAAW,EACXC,YAAavE,EAAE,eAAgB,qCAK7BwE,EAAmB,CACvB,CACEhE,GAAI,uBACJC,KAAMT,EAAE,sBAAuB,wBAC/B2D,QAAS3D,EAAE,6BAA8B,wBACzCyE,QAASzE,EAAE,6BAA8B,oHACzCqE,SAAU,CAAC,QAAS,MAAO,WAAY,SAEzC,CACE7D,GAAI,wBACJC,KAAMT,EAAE,uBAAwB,yBAChC2D,QAAS3D,EAAE,8BAA+B,yBAC1CyE,QAASzE,EAAE,8BAA+B,mJAC1CqE,SAAU,CAAC,QAAS,aAEtB,CACE7D,GAAI,kBACJC,KAAMT,EAAE,iBAAkB,mBAC1B2D,QAAS3D,EAAE,wBAAyB,wBACpCyE,QAASzE,EAAE,wBAAyB,oFACpCqE,SAAU,CAAC,QAAS,WAAY,SAElC,CACE7D,GAAI,sBACJC,KAAMT,EAAE,qBAAsB,uBAC9B2D,QAAS3D,EAAE,4BAA6B,uBACxCyE,QAASzE,EAAE,4BAA6B,iFACxCqE,SAAU,CAAC,MAAO,SAEpB,CACE7D,GAAI,SACJC,KAAMT,EAAE,gBAAiB,kBACzB2D,QAAS,GACTc,QAAS,GACTJ,SAAU,CAAC,QAAS,MAAO,WAAY,WAK3CK,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACxC,IAEJ,MAAMwC,EAAyBC,UAC7B,IAEE,MAAMC,EAAc,CAClBC,kBAAmB,CAAC,QAAS,YAC7BC,SAAU,KACVC,SAAU,cACVC,WAAY,CAAEC,MAAO,QAASC,IAAK,SACnCC,YAAY,EACZC,eAAgB,MAChBC,aAAc,SACdC,kBAAmB,MACnBC,cAAe,OAEjBpB,EAAsBS,GACtBrB,EAAoBqB,EAAYC,kBAClC,CAAE,MAAOW,GACPnD,QAAQmD,MAAM,qCAAsCA,EACtD,GAkBIC,EAAuBC,IAC3BnC,EAAoBoC,GAClBA,EAAKC,SAASF,GACVC,EAAKE,OAAOtF,GAAMA,IAAOmF,GACzB,IAAIC,EAAMD,KA0ElB,OACE9D,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,UACpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qDAAoDC,SAAA,EACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uCACZ3B,EAAE,sBAAuB,4BAE5B0B,EAAAA,EAAAA,MAAA,UACEM,QArEe+D,KACvB,MAAMC,EAVU,WAAZjC,EACK,CAAC,MAAO,OAAQ,YACF,SAAZA,EACF,CAAC,QAAS,WAAY,QAEtBI,EAAmBW,mBAAqB,CAAC,SAMlDtB,EAAoBwC,GACpBxD,EAAAA,GAAMC,QAAQzC,EAAE,uBAAwB,qEAmEhC2B,UAAU,6FAA4FC,SAAA,EAEtGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ3B,EAAE,aAAc,sBAIrB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5B,EAAE,kBAAmB,uBAExB0B,EAAAA,EAAAA,MAAA,UACEuE,MAAOpC,EACPqC,SAAWC,GAxHKC,KAC5B,MAAMC,EAAW7B,EAAiB8B,KAAKtG,GAAKA,EAAEQ,KAAO4F,GACrD,GAAIC,EAAU,CACZvC,EAAoBsC,GACpBxC,EAAWyC,EAAS1C,SACpBD,EAAW2C,EAAS5B,SAGpB,MAAM8B,EAAoBF,EAAShC,SAASyB,OAAOU,GACjDnC,EAASiC,KAAKG,GAAKA,EAAEjG,KAAOgG,GAAWC,EAAEnC,YAE3Cd,EAAoB+C,EACtB,GA4G6BG,CAAqBP,EAAEQ,OAAOV,OAC/CtE,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQoE,MAAM,GAAErE,SAAE5B,EAAE,iBAAkB,uBACrCwE,EAAiB1C,IAAIuE,IACpBxE,EAAAA,EAAAA,KAAA,UAA0BoE,MAAOI,EAAS7F,GAAGoB,SAC1CyE,EAAS5F,MADC4F,EAAS7F,WAQ3B+C,EAAiBsC,SAAS,WACzBnE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5B,EAAE,UAAW,cAEhB6B,EAAAA,EAAAA,KAAA,SACE+E,KAAK,OACLX,MAAOtC,EACPuC,SAAWC,GAAMvC,EAAWuC,EAAEQ,OAAOV,OACrCtE,UAAU,kKACVkF,YAAa7G,EAAE,eAAgB,6BAMrC0B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5B,EAAE,UAAW,cAEhB6B,EAAAA,EAAAA,KAAA,YACEoE,MAAOxC,EACPyC,SAAWC,GAAMzC,EAAWyC,EAAEQ,OAAOV,OACrCa,KAAM,EACNnF,UAAU,8KACVkF,YAAa7G,EAAE,eAAgB,4BAEjC0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,CAC3D6B,EAAQsD,OAAO,SAAO/G,EAAE,aAAc,qBAK3C0B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5B,EAAE,UAAW,cAEhB0B,EAAAA,EAAAA,MAAA,UACEuE,MAAOlC,EACPmC,SAAWC,GAAMnC,EAAWmC,EAAEQ,OAAOV,OACrCtE,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQoE,MAAM,MAAKrE,SAAE5B,EAAE,aAAc,UACrC6B,EAAAA,EAAAA,KAAA,UAAQoE,MAAM,SAAQrE,SAAE5B,EAAE,gBAAiB,aAC3C6B,EAAAA,EAAAA,KAAA,UAAQoE,MAAM,OAAMrE,SAAE5B,EAAE,cAAe,WACvC6B,EAAAA,EAAAA,KAAA,UAAQoE,MAAM,SAAQrE,SAAE5B,EAAE,gBAAiB,mBAK/C0B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E5B,EAAE,eAAgB,iBAAiB,KAAGA,EAAE,WAAY,YAAY,QAEnE6B,EAAAA,EAAAA,KAAA,SACE+E,KAAK,iBACLX,MAAOhC,EACPiC,SAAWC,GAAMjC,EAAiBiC,EAAEQ,OAAOV,OAC3CtE,UAAU,2KAMhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4DAA2DC,SACtE5B,EAAE,wBAAyB,6BAE9B6B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvByC,EAASvC,IAAI0E,IACZ3E,EAAAA,EAAAA,KAAA,OAEEF,UAAS,uDAAAM,OACPsB,EAAiBsC,SAASW,EAAQhG,IAC9B,iDACA,6DAA4D,KAAAyB,OAC7DuE,EAAQlC,UAA8C,GAAlC,iCACzBtC,QAASA,IAAMwE,EAAQlC,WAAaoB,EAAoBc,EAAQhG,IAAIoB,UAEpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAM,OAAKuE,EAAQxF,KAAI,UAAAiB,OAASuE,EAAQvF,MAAK,mBACnDS,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4CAA2CC,SACvD4E,EAAQ/F,QAEXoB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtD4E,EAAQjC,qBAIf7C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EACxC4E,EAAQlC,YACRzC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wBAAuBC,SACpC5B,EAAE,aAAc,kBAGrB6B,EAAAA,EAAAA,KAAA,SACE+E,KAAK,WACLI,QAASzD,EAAiBsC,SAASW,EAAQhG,IAC3C0F,SAAUA,IAAMM,EAAQlC,WAAaoB,EAAoBc,EAAQhG,IACjEyG,UAAWT,EAAQlC,UACnB3C,UAAU,6DA/BX6E,EAAQhG,UAyCpB2D,EAAmBW,oBAClBpD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,4DAA2DC,SACtE5B,EAAE,qBAAsB,0BAE3B0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtD5B,EAAE,oBAAqB,aAAa,KAAGmE,EAAmBW,kBAAkBoC,KAAK,SAEnF/C,EAAmBc,aAClBvD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtD5B,EAAE,aAAc,eAAe,KAAGmE,EAAmBc,WAAWC,MAAM,MAAIf,EAAmBc,WAAWE,WAOjHzD,EAAAA,EAAAA,MAAA,UACEM,QA9MO4C,UACjB,KAnBKnB,EAAQ0D,OAKT5D,EAAiBsC,SAAS,WAAalC,EAAQwD,QACjD3E,EAAAA,GAAMiD,MAAMzF,EAAE,kBAAmB,kCAC1B,GAGuB,IAA5BuD,EAAiBwD,SACnBvE,EAAAA,GAAMiD,MAAMzF,EAAE,gBAAiB,qDACxB,IAXPwC,EAAAA,GAAMiD,MAAMzF,EAAE,kBAAmB,gCAC1B,IAiBe,OAExB,MAAMoH,EAAoB,CACxBjF,YACAkC,SAAUd,EACVE,UACAE,UACAI,UACAE,cAAeA,GAAiB,KAChCoC,SAAUxC,EACVwD,WAAW,IAAIC,MAAOC,eAGxB,IACM3E,SACIA,EAAOwE,GAIf9E,QAAQC,IAAI,yBAA0B6E,GAEtC5E,EAAAA,GAAMC,QAAQzC,EAAE,cAAe,oDAG/B0D,EAAW,IACXE,EAAW,IACXE,EAAoB,IACpBI,EAAiB,GACnB,CAAE,MAAOuB,GACPjD,EAAAA,GAAMiD,MAAMzF,EAAE,YAAa,0BAC3BsC,QAAQmD,MAAM,cAAeA,EAC/B,GA+KUwB,SAAsC,IAA5B1D,EAAiBwD,SAAiBtD,EAAQ0D,OACpDxF,UAAU,yIAAwIC,SAAA,EAElJC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZsC,EAAgBjE,EAAE,kBAAmB,oBAAsBA,EAAE,cAAe,8B", "sources": ["pages/Demo/PhysioFlowDemo.jsx", "components/Communication/CommunicationManager.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport BodyMap from '../../components/BodyMap/BodyMap';\nimport CommunicationManager from '../../components/Communication/CommunicationManager';\nimport ExerciseLibrary, { ExerciseProgramBuilder } from '../../components/Exercise/ExerciseLibrary';\nimport AIAssistant, { AIAnalyticsDashboard } from '../../components/AI/AIAssistant';\nimport toast from 'react-hot-toast';\n\nconst PhysioFlowDemo = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('bodymap');\n  const [selectedPatient, setSelectedPatient] = useState({\n    id: 'demo-patient-001',\n    name: '<PERSON>',\n    nameAr: 'أحمد محمد',\n    age: 28,\n    condition: 'Cerebral Palsy',\n    conditionAr: 'الشلل الدماغي'\n  });\n\n  const tabs = [\n    {\n      id: 'bodymap',\n      label: t('interactiveBodyMap', 'Interactive Body Map'),\n      icon: 'fas fa-user-md',\n      color: 'blue'\n    },\n    {\n      id: 'communication',\n      label: t('communicationHub', 'Communication Hub'),\n      icon: 'fas fa-comments',\n      color: 'green'\n    },\n    {\n      id: 'exercises',\n      label: t('exerciseLibrary', 'Exercise Library'),\n      icon: 'fas fa-dumbbell',\n      color: 'purple'\n    },\n    {\n      id: 'ai',\n      label: t('aiAssistant', 'AI Assistant'),\n      icon: 'fas fa-brain',\n      color: 'orange'\n    },\n    {\n      id: 'analytics',\n      label: t('aiAnalytics', 'AI Analytics'),\n      icon: 'fas fa-chart-line',\n      color: 'indigo'\n    }\n  ];\n\n  const handleBodyMapSave = (painData) => {\n    console.log('Body Map Data:', painData);\n    toast.success(t('painAssessmentSaved', 'Pain assessment saved successfully'));\n  };\n\n  const handleCommunicationSend = (messageData) => {\n    console.log('Communication Data:', messageData);\n    toast.success(t('messageSent', 'Message sent successfully'));\n  };\n\n  const handleProgramSave = (programData) => {\n    console.log('Exercise Program Data:', programData);\n    toast.success(t('exerciseProgramSaved', 'Exercise program saved successfully'));\n  };\n\n  const getTabColorClasses = (color, isActive) => {\n    const colors = {\n      blue: isActive ? 'bg-blue-600 text-white' : 'text-blue-600 hover:bg-blue-50',\n      green: isActive ? 'bg-green-600 text-white' : 'text-green-600 hover:bg-green-50',\n      purple: isActive ? 'bg-purple-600 text-white' : 'text-purple-600 hover:bg-purple-50',\n      orange: isActive ? 'bg-orange-600 text-white' : 'text-orange-600 hover:bg-orange-50',\n      indigo: isActive ? 'bg-indigo-600 text-white' : 'text-indigo-600 hover:bg-indigo-50'\n    };\n    return colors[color] || colors.blue;\n  };\n\n  return (\n    <div className=\"physioflow-demo min-h-screen bg-gray-50 dark:bg-gray-900\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 shadow-lg\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                  <i className=\"fas fa-rocket mr-3 text-blue-600\"></i>\n                  PhysioFlow Demo\n                </h1>\n                <p className=\"mt-2 text-lg text-gray-600 dark:text-gray-300\">\n                  {t('demoSubtitle', 'Experience the future of physical therapy management')}\n                </p>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg\">\n                  <i className=\"fas fa-user mr-2\"></i>\n                  {isRTL ? selectedPatient.nameAr : selectedPatient.name}\n                </div>\n                <div className=\"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg text-sm\">\n                  {t('age', 'Age')}: {selectedPatient.age}\n                </div>\n                <div className=\"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg text-sm\">\n                  {isRTL ? selectedPatient.conditionAr : selectedPatient.condition}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <nav className=\"flex space-x-8 overflow-x-auto\">\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`\n                  flex items-center px-4 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-colors duration-200\n                  ${activeTab === tab.id \n                    ? `border-${tab.color}-600 ${getTabColorClasses(tab.color, true)}` \n                    : `border-transparent ${getTabColorClasses(tab.color, false)}`\n                  }\n                `}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {tab.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Content Area */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Interactive Body Map */}\n        {activeTab === 'bodymap' && (\n          <div className=\"space-y-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-user-md mr-3 text-blue-600\"></i>\n                    {t('interactiveBodyMap', 'Interactive Body Map')}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n                    {t('bodyMapDescription', 'Click on body regions to assess pain levels and track patient symptoms visually')}\n                  </p>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-universal-access mr-1\"></i>\n                    {t('accessible', 'Accessible')}\n                  </span>\n                  <span className=\"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-language mr-1\"></i>\n                    {t('multilingual', 'Multilingual')}\n                  </span>\n                </div>\n              </div>\n              <BodyMap \n                patientId={selectedPatient.id}\n                onSave={handleBodyMapSave}\n                showInstructions={true}\n              />\n            </div>\n          </div>\n        )}\n\n        {/* Communication Hub */}\n        {activeTab === 'communication' && (\n          <div className=\"space-y-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-comments mr-3 text-green-600\"></i>\n                    {t('communicationHub', 'Multi-Channel Communication Hub')}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n                    {t('communicationDescription', 'Send messages via Email, SMS, WhatsApp, and Push notifications with intelligent routing')}\n                  </p>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-robot mr-1\"></i>\n                    {t('smartRouting', 'Smart Routing')}\n                  </span>\n                  <span className=\"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-clock mr-1\"></i>\n                    {t('scheduled', 'Scheduled')}\n                  </span>\n                </div>\n              </div>\n              <CommunicationManager \n                patientId={selectedPatient.id}\n                onSend={handleCommunicationSend}\n              />\n            </div>\n          </div>\n        )}\n\n        {/* Exercise Library */}\n        {activeTab === 'exercises' && (\n          <div className=\"space-y-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-dumbbell mr-3 text-purple-600\"></i>\n                    {t('exerciseLibrary', 'Exercise Library & Program Builder')}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n                    {t('exerciseDescription', 'Browse 500+ exercises with video demonstrations and create adaptive programs')}\n                  </p>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-video mr-1\"></i>\n                    500+ {t('exercises', 'Exercises')}\n                  </span>\n                  <span className=\"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-magic mr-1\"></i>\n                    {t('adaptive', 'Adaptive')}\n                  </span>\n                </div>\n              </div>\n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-8\">\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n                    {t('browseExercises', 'Browse Exercise Library')}\n                  </h3>\n                  <ExerciseLibrary \n                    patientId={selectedPatient.id}\n                    showProgramBuilder={false}\n                  />\n                </div>\n                <div>\n                  <h3 className=\"text-lg font-semibold mb-4 text-gray-900 dark:text-white\">\n                    {t('createProgram', 'Create Exercise Program')}\n                  </h3>\n                  <ExerciseProgramBuilder \n                    patientId={selectedPatient.id}\n                    onSave={handleProgramSave}\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* AI Assistant */}\n        {activeTab === 'ai' && (\n          <div className=\"space-y-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-brain mr-3 text-orange-600\"></i>\n                    {t('aiAssistant', 'AI-Powered Clinical Assistant')}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n                    {t('aiDescription', 'Get intelligent treatment recommendations, exercise modifications, and clinical insights')}\n                  </p>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fab fa-google mr-1\"></i>\n                    Gemini AI\n                  </span>\n                  <span className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-robot mr-1\"></i>\n                    ChatGPT\n                  </span>\n                  <span className=\"bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-server mr-1\"></i>\n                    Local LLM\n                  </span>\n                </div>\n              </div>\n              <div className=\"text-center py-12\">\n                <i className=\"fas fa-brain text-6xl text-orange-600 mb-4\"></i>\n                <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n                  {t('aiAssistantActive', 'AI Assistant is Active')}\n                </h3>\n                <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\n                  {t('aiAssistantNote', 'The AI Assistant is available as a floating button in the bottom-right corner of the screen')}\n                </p>\n                <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-md mx-auto\">\n                  <p className=\"text-blue-800 dark:text-blue-200 text-sm\">\n                    <i className=\"fas fa-info-circle mr-2\"></i>\n                    {t('aiFloatingNote', 'Look for the brain icon in the bottom-right corner to access the AI Assistant')}\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* AI Analytics */}\n        {activeTab === 'analytics' && (\n          <div className=\"space-y-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div>\n                  <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                    <i className=\"fas fa-chart-line mr-3 text-indigo-600\"></i>\n                    {t('aiAnalytics', 'AI Analytics Dashboard')}\n                  </h2>\n                  <p className=\"text-gray-600 dark:text-gray-300 mt-2\">\n                    {t('analyticsDescription', 'Monitor AI usage, performance metrics, and clinical insights')}\n                  </p>\n                </div>\n                <div className=\"flex space-x-2\">\n                  <span className=\"bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-chart-bar mr-1\"></i>\n                    {t('realTime', 'Real-time')}\n                  </span>\n                  <span className=\"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    <i className=\"fas fa-lightbulb mr-1\"></i>\n                    {t('insights', 'Insights')}\n                  </span>\n                </div>\n              </div>\n              <AIAnalyticsDashboard />\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* AI Assistant (Always Available) */}\n      <AIAssistant patientData={selectedPatient} />\n    </div>\n  );\n};\n\nexport default PhysioFlowDemo;\n", "import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst CommunicationManager = ({ patientId, onSend, templates = [] }) => {\n  const { t, isRTL } = useLanguage();\n  const [selectedChannels, setSelectedChannels] = useState(['email']);\n  const [message, setMessage] = useState('');\n  const [subject, setSubject] = useState('');\n  const [selectedTemplate, setSelectedTemplate] = useState('');\n  const [urgency, setUrgency] = useState('normal');\n  const [scheduledTime, setScheduledTime] = useState('');\n  const [patientPreferences, setPatientPreferences] = useState({});\n\n  // Communication channels\n  const channels = [\n    {\n      id: 'email',\n      name: t('email', 'Email'),\n      icon: 'fas fa-envelope',\n      color: 'blue',\n      available: true,\n      description: t('emailDesc', 'Send via email')\n    },\n    {\n      id: 'sms',\n      name: t('sms', 'SMS'),\n      icon: 'fas fa-sms',\n      color: 'green',\n      available: true,\n      description: t('smsDesc', 'Send via SMS')\n    },\n    {\n      id: 'whatsapp',\n      name: t('whatsapp', 'WhatsApp'),\n      icon: 'fab fa-whatsapp',\n      color: 'green',\n      available: true,\n      description: t('whatsappDesc', 'Send via WhatsApp')\n    },\n    {\n      id: 'push',\n      name: t('pushNotification', 'Push Notification'),\n      icon: 'fas fa-bell',\n      color: 'purple',\n      available: true,\n      description: t('pushDesc', 'Send push notification to mobile app')\n    },\n    {\n      id: 'telegram',\n      name: t('telegram', 'Telegram'),\n      icon: 'fab fa-telegram',\n      color: 'blue',\n      available: false,\n      description: t('telegramDesc', 'Send via Telegram (Coming Soon)')\n    }\n  ];\n\n  // Message templates\n  const messageTemplates = [\n    {\n      id: 'appointment_reminder',\n      name: t('appointmentReminder', 'Appointment Reminder'),\n      subject: t('appointmentReminderSubject', 'Appointment Reminder'),\n      content: t('appointmentReminderContent', 'Dear {patientName}, this is a reminder for your appointment on {date} at {time}. Please arrive 15 minutes early.'),\n      channels: ['email', 'sms', 'whatsapp', 'push']\n    },\n    {\n      id: 'exercise_instructions',\n      name: t('exerciseInstructions', 'Exercise Instructions'),\n      subject: t('exerciseInstructionsSubject', 'Your Exercise Program'),\n      content: t('exerciseInstructionsContent', 'Dear {patientName}, please find your personalized exercise program. Follow the instructions carefully and contact us if you have any questions.'),\n      channels: ['email', 'whatsapp']\n    },\n    {\n      id: 'progress_update',\n      name: t('progressUpdate', 'Progress Update'),\n      subject: t('progressUpdateSubject', 'Your Progress Update'),\n      content: t('progressUpdateContent', 'Dear {patientName}, here is your latest progress update. Keep up the great work!'),\n      channels: ['email', 'whatsapp', 'push']\n    },\n    {\n      id: 'medication_reminder',\n      name: t('medicationReminder', 'Medication Reminder'),\n      subject: t('medicationReminderSubject', 'Medication Reminder'),\n      content: t('medicationReminderContent', 'Time to take your medication: {medicationName}. Follow the prescribed dosage.'),\n      channels: ['sms', 'push']\n    },\n    {\n      id: 'custom',\n      name: t('customMessage', 'Custom Message'),\n      subject: '',\n      content: '',\n      channels: ['email', 'sms', 'whatsapp', 'push']\n    }\n  ];\n\n  // Load patient communication preferences\n  useEffect(() => {\n    loadPatientPreferences();\n  }, [patientId]);\n\n  const loadPatientPreferences = async () => {\n    try {\n      // Mock patient preferences - replace with actual API call\n      const preferences = {\n        preferredChannels: ['email', 'whatsapp'],\n        language: 'en',\n        timezone: 'Asia/Riyadh',\n        quietHours: { start: '22:00', end: '08:00' },\n        urgentOnly: false,\n        emailFrequency: 'all',\n        smsFrequency: 'urgent',\n        whatsappFrequency: 'all',\n        pushFrequency: 'all'\n      };\n      setPatientPreferences(preferences);\n      setSelectedChannels(preferences.preferredChannels);\n    } catch (error) {\n      console.error('Error loading patient preferences:', error);\n    }\n  };\n\n  const handleTemplateSelect = (templateId) => {\n    const template = messageTemplates.find(t => t.id === templateId);\n    if (template) {\n      setSelectedTemplate(templateId);\n      setSubject(template.subject);\n      setMessage(template.content);\n      \n      // Auto-select appropriate channels for this template\n      const availableChannels = template.channels.filter(channel => \n        channels.find(c => c.id === channel && c.available)\n      );\n      setSelectedChannels(availableChannels);\n    }\n  };\n\n  const handleChannelToggle = (channelId) => {\n    setSelectedChannels(prev => \n      prev.includes(channelId)\n        ? prev.filter(id => id !== channelId)\n        : [...prev, channelId]\n    );\n  };\n\n  const getRecommendedChannels = () => {\n    if (urgency === 'urgent') {\n      return ['sms', 'push', 'whatsapp'];\n    } else if (urgency === 'high') {\n      return ['email', 'whatsapp', 'push'];\n    } else {\n      return patientPreferences.preferredChannels || ['email'];\n    }\n  };\n\n  const handleAutoSelect = () => {\n    const recommended = getRecommendedChannels();\n    setSelectedChannels(recommended);\n    toast.success(t('channelsAutoSelected', 'Channels auto-selected based on urgency and patient preferences'));\n  };\n\n  const validateMessage = () => {\n    if (!message.trim()) {\n      toast.error(t('messageRequired', 'Message content is required'));\n      return false;\n    }\n    \n    if (selectedChannels.includes('email') && !subject.trim()) {\n      toast.error(t('subjectRequired', 'Subject is required for email'));\n      return false;\n    }\n    \n    if (selectedChannels.length === 0) {\n      toast.error(t('selectChannel', 'Please select at least one communication channel'));\n      return false;\n    }\n    \n    return true;\n  };\n\n  const handleSend = async () => {\n    if (!validateMessage()) return;\n\n    const communicationData = {\n      patientId,\n      channels: selectedChannels,\n      message,\n      subject,\n      urgency,\n      scheduledTime: scheduledTime || null,\n      template: selectedTemplate,\n      timestamp: new Date().toISOString()\n    };\n\n    try {\n      if (onSend) {\n        await onSend(communicationData);\n      }\n      \n      // Mock sending logic\n      console.log('Sending communication:', communicationData);\n      \n      toast.success(t('messageSent', 'Message sent successfully via selected channels'));\n      \n      // Reset form\n      setMessage('');\n      setSubject('');\n      setSelectedTemplate('');\n      setScheduledTime('');\n    } catch (error) {\n      toast.error(t('sendError', 'Error sending message'));\n      console.error('Send error:', error);\n    }\n  };\n\n  return (\n    <div className=\"communication-manager\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            <i className=\"fas fa-comments mr-2 text-blue-600\"></i>\n            {t('communicationCenter', 'Communication Center')}\n          </h3>\n          <button\n            onClick={handleAutoSelect}\n            className=\"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors\"\n          >\n            <i className=\"fas fa-magic mr-1\"></i>\n            {t('autoSelect', 'Auto Select')}\n          </button>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Message Composition */}\n          <div className=\"space-y-4\">\n            {/* Template Selection */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('messageTemplate', 'Message Template')}\n              </label>\n              <select\n                value={selectedTemplate}\n                onChange={(e) => handleTemplateSelect(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              >\n                <option value=\"\">{t('selectTemplate', 'Select a template')}</option>\n                {messageTemplates.map(template => (\n                  <option key={template.id} value={template.id}>\n                    {template.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            {/* Subject (for email) */}\n            {selectedChannels.includes('email') && (\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('subject', 'Subject')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={subject}\n                  onChange={(e) => setSubject(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterSubject', 'Enter email subject')}\n                />\n              </div>\n            )}\n\n            {/* Message Content */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('message', 'Message')}\n              </label>\n              <textarea\n                value={message}\n                onChange={(e) => setMessage(e.target.value)}\n                rows={6}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none\"\n                placeholder={t('enterMessage', 'Enter your message...')}\n              />\n              <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                {message.length}/1000 {t('characters', 'characters')}\n              </div>\n            </div>\n\n            {/* Urgency */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('urgency', 'Urgency')}\n              </label>\n              <select\n                value={urgency}\n                onChange={(e) => setUrgency(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              >\n                <option value=\"low\">{t('lowUrgency', 'Low')}</option>\n                <option value=\"normal\">{t('normalUrgency', 'Normal')}</option>\n                <option value=\"high\">{t('highUrgency', 'High')}</option>\n                <option value=\"urgent\">{t('urgentUrgency', 'Urgent')}</option>\n              </select>\n            </div>\n\n            {/* Scheduled Time */}\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                {t('scheduleTime', 'Schedule Time')} ({t('optional', 'Optional')})\n              </label>\n              <input\n                type=\"datetime-local\"\n                value={scheduledTime}\n                onChange={(e) => setScheduledTime(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              />\n            </div>\n          </div>\n\n          {/* Channel Selection */}\n          <div className=\"space-y-4\">\n            <div>\n              <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                {t('communicationChannels', 'Communication Channels')}\n              </h4>\n              <div className=\"space-y-3\">\n                {channels.map(channel => (\n                  <div\n                    key={channel.id}\n                    className={`p-3 border rounded-lg transition-all cursor-pointer ${\n                      selectedChannels.includes(channel.id)\n                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                        : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'\n                    } ${!channel.available ? 'opacity-50 cursor-not-allowed' : ''}`}\n                    onClick={() => channel.available && handleChannelToggle(channel.id)}\n                  >\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-3\">\n                        <i className={`${channel.icon} text-${channel.color}-600 text-lg`}></i>\n                        <div>\n                          <div className=\"font-medium text-gray-900 dark:text-white\">\n                            {channel.name}\n                          </div>\n                          <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                            {channel.description}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        {!channel.available && (\n                          <span className=\"text-xs text-gray-400\">\n                            {t('comingSoon', 'Coming Soon')}\n                          </span>\n                        )}\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedChannels.includes(channel.id)}\n                          onChange={() => channel.available && handleChannelToggle(channel.id)}\n                          disabled={!channel.available}\n                          className=\"w-4 h-4 text-blue-600 rounded focus:ring-blue-500\"\n                        />\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Patient Preferences */}\n            {patientPreferences.preferredChannels && (\n              <div className=\"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                <h5 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  {t('patientPreferences', 'Patient Preferences')}\n                </h5>\n                <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                  {t('preferredChannels', 'Preferred')}: {patientPreferences.preferredChannels.join(', ')}\n                </div>\n                {patientPreferences.quietHours && (\n                  <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {t('quietHours', 'Quiet Hours')}: {patientPreferences.quietHours.start} - {patientPreferences.quietHours.end}\n                  </div>\n                )}\n              </div>\n            )}\n\n            {/* Send Button */}\n            <button\n              onClick={handleSend}\n              disabled={selectedChannels.length === 0 || !message.trim()}\n              className=\"w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              <i className=\"fas fa-paper-plane mr-2\"></i>\n              {scheduledTime ? t('scheduleMessage', 'Schedule Message') : t('sendMessage', 'Send Message')}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CommunicationManager;\n\n// Communication History Component\nexport const CommunicationHistory = ({ patientId }) => {\n  const { t, isRTL } = useLanguage();\n  const [history, setHistory] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n\n  useEffect(() => {\n    loadCommunicationHistory();\n  }, [patientId, filter]);\n\n  const loadCommunicationHistory = async () => {\n    setLoading(true);\n    try {\n      // Mock communication history - replace with actual API call\n      const mockHistory = [\n        {\n          id: 1,\n          type: 'appointment_reminder',\n          channels: ['email', 'sms'],\n          subject: 'Appointment Reminder',\n          message: 'Your appointment is tomorrow at 2:00 PM',\n          status: 'delivered',\n          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n          deliveryStatus: {\n            email: 'delivered',\n            sms: 'delivered'\n          }\n        },\n        {\n          id: 2,\n          type: 'exercise_instructions',\n          channels: ['whatsapp'],\n          subject: 'Exercise Program',\n          message: 'Please find your updated exercise program',\n          status: 'read',\n          timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),\n          deliveryStatus: {\n            whatsapp: 'read'\n          }\n        }\n      ];\n\n      setHistory(mockHistory);\n    } catch (error) {\n      console.error('Error loading communication history:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'sent': return 'fas fa-paper-plane text-blue-500';\n      case 'delivered': return 'fas fa-check text-green-500';\n      case 'read': return 'fas fa-check-double text-green-600';\n      case 'failed': return 'fas fa-exclamation-triangle text-red-500';\n      default: return 'fas fa-clock text-gray-400';\n    }\n  };\n\n  const getChannelIcon = (channel) => {\n    switch (channel) {\n      case 'email': return 'fas fa-envelope';\n      case 'sms': return 'fas fa-sms';\n      case 'whatsapp': return 'fab fa-whatsapp';\n      case 'push': return 'fas fa-bell';\n      default: return 'fas fa-comment';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center p-8\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"communication-history\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            <i className=\"fas fa-history mr-2 text-gray-600\"></i>\n            {t('communicationHistory', 'Communication History')}\n          </h3>\n          <select\n            value={filter}\n            onChange={(e) => setFilter(e.target.value)}\n            className=\"px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n          >\n            <option value=\"all\">{t('allMessages', 'All Messages')}</option>\n            <option value=\"email\">{t('emailOnly', 'Email Only')}</option>\n            <option value=\"sms\">{t('smsOnly', 'SMS Only')}</option>\n            <option value=\"whatsapp\">{t('whatsappOnly', 'WhatsApp Only')}</option>\n          </select>\n        </div>\n\n        {history.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <i className=\"fas fa-inbox text-4xl text-gray-400 mb-4\"></i>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('noCommunicationHistory', 'No communication history found')}\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {history.map(item => (\n              <div key={item.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <div className=\"flex items-center space-x-3\">\n                    <i className={getStatusIcon(item.status)}></i>\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                        {item.subject}\n                      </h4>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                        {new Date(item.timestamp).toLocaleString()}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    {item.channels.map(channel => (\n                      <div key={channel} className=\"flex items-center space-x-1\">\n                        <i className={`${getChannelIcon(channel)} text-sm text-gray-500`}></i>\n                        <span className=\"text-xs text-gray-500\">\n                          {item.deliveryStatus[channel]}\n                        </span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n                <p className=\"text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-2 rounded\">\n                  {item.message}\n                </p>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n"], "names": ["PhysioFlowDemo", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "selectedPatient", "setSelectedPatient", "id", "name", "nameAr", "age", "condition", "conditionAr", "tabs", "label", "icon", "color", "getTabColorClasses", "isActive", "colors", "blue", "green", "purple", "orange", "indigo", "_jsxs", "className", "children", "_jsx", "map", "tab", "onClick", "concat", "BodyMap", "patientId", "onSave", "painData", "console", "log", "toast", "success", "showInstructions", "CommunicationManager", "onSend", "messageData", "ExerciseLibrary", "showProgramBuilder", "ExerciseProgramBuilder", "programData", "AIAnalyticsDashboard", "AIAssistant", "patientData", "_ref", "templates", "selectedChannels", "setSelectedChannels", "message", "setMessage", "subject", "setSubject", "selectedTemplate", "setSelectedTemplate", "urgency", "setUrgency", "scheduledTime", "setScheduledTime", "patientPreferences", "setPatientPreferences", "channels", "available", "description", "messageTemplates", "content", "useEffect", "loadPatientPreferences", "async", "preferences", "preferredChannels", "language", "timezone", "quietHours", "start", "end", "urgentOnly", "emailFrequency", "smsFrequency", "whatsappFrequency", "pushFrequency", "error", "handleChannelToggle", "channelId", "prev", "includes", "filter", "handleAutoSelect", "recommended", "value", "onChange", "e", "templateId", "template", "find", "availableChannels", "channel", "c", "handleTemplateSelect", "target", "type", "placeholder", "rows", "length", "checked", "disabled", "join", "trim", "communicationData", "timestamp", "Date", "toISOString"], "sourceRoot": ""}