{"version": 3, "file": "static/js/8602.c8c906ef.chunk.js", "mappings": "iMAIA,MAkBA,EAlB0BA,KAAO,IAADC,EAAAC,EAAAC,EAC9B,MAAM,UAAEC,EAAS,YAAEC,EAAW,WAAEC,IAAeC,EAAAA,EAAAA,KACzCC,GAAWC,EAAAA,EAAAA,MAEjB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,UACvDF,EAAAA,EAAAA,KAACG,EAAAA,EAAiB,CAChBT,UAAWA,EACXC,YAAaA,EACbC,WAAYA,EACZQ,YAA2B,QAAhBb,EAAEO,EAASO,aAAK,IAAAd,OAAA,EAAdA,EAAgBe,QAC7BC,mBAAkC,QAAhBf,EAAEM,EAASO,aAAK,IAAAb,OAAA,EAAdA,EAAgBe,mBACpCC,YAA2B,QAAhBf,EAAEK,EAASO,aAAK,IAAAZ,OAAA,EAAdA,EAAgBe,gB", "sources": ["pages/DailyProgressPage.jsx"], "sourcesContent": ["import React from 'react';\nimport { useParams, useLocation } from 'react-router-dom';\nimport DailyProgressForm from '../components/DailyProgress/DailyProgressForm';\n\nconst DailyProgressPage = () => {\n  const { patientId, treatmentId, progressId } = useParams();\n  const location = useLocation();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <DailyProgressForm\n        patientId={patientId}\n        treatmentId={treatmentId}\n        progressId={progressId}\n        patientData={location.state?.patient}\n        fromPatientProfile={location.state?.fromPatientProfile}\n        defaultDate={location.state?.defaultDate}\n      />\n    </div>\n  );\n};\n\nexport default DailyProgressPage;\n"], "names": ["DailyProgressPage", "_location$state", "_location$state2", "_location$state3", "patientId", "treatmentId", "progressId", "useParams", "location", "useLocation", "_jsx", "className", "children", "DailyProgressForm", "patientData", "state", "patient", "fromPatientProfile", "defaultDate"], "sourceRoot": ""}