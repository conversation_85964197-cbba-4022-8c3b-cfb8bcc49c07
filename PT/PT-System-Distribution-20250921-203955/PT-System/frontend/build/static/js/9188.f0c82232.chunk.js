"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9188],{9188:(e,s,a)=>{a.r(s),a.d(s,{default:()=>m});var t=a(5043),r=a(7921),i=a(2770),n=a(9737),l=a(6711),c=a(7378),d=a(3768),o=a(579);const m=()=>{const{t:e,isRTL:s}=(0,r.o)(),[a,m]=(0,t.useState)("bodymap"),[x,g]=(0,t.useState)({id:"demo-patient-001",name:"<PERSON>",nameAr:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f",age:28,condition:"Cerebral Palsy",conditionAr:"\u0627\u0644\u0634\u0644\u0644 \u0627\u0644\u062f\u0645\u0627\u063a\u064a"}),u=[{id:"bodymap",label:e("interactiveBodyMap","Interactive Body Map"),icon:"fas fa-user-md",color:"blue"},{id:"communication",label:e("communicationHub","Communication Hub"),icon:"fas fa-comments",color:"green"},{id:"exercises",label:e("exerciseLibrary","Exercise Library"),icon:"fas fa-dumbbell",color:"purple"},{id:"ai",label:e("aiAssistant","AI Assistant"),icon:"fas fa-brain",color:"orange"},{id:"analytics",label:e("aiAnalytics","AI Analytics"),icon:"fas fa-chart-line",color:"indigo"}],p=(e,s)=>{const a={blue:s?"bg-blue-600 text-white":"text-blue-600 hover:bg-blue-50",green:s?"bg-green-600 text-white":"text-green-600 hover:bg-green-50",purple:s?"bg-purple-600 text-white":"text-purple-600 hover:bg-purple-50",orange:s?"bg-orange-600 text-white":"text-orange-600 hover:bg-orange-50",indigo:s?"bg-indigo-600 text-white":"text-indigo-600 hover:bg-indigo-50"};return a[e]||a.blue};return(0,o.jsxs)("div",{className:"physioflow-demo min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-lg",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsx)("div",{className:"py-6",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-rocket mr-3 text-blue-600"}),"PhysioFlow Demo"]}),(0,o.jsx)("p",{className:"mt-2 text-lg text-gray-600 dark:text-gray-300",children:e("demoSubtitle","Experience the future of physical therapy management")})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg",children:[(0,o.jsx)("i",{className:"fas fa-user mr-2"}),s?x.nameAr:x.name]}),(0,o.jsxs)("div",{className:"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg text-sm",children:[e("age","Age"),": ",x.age]}),(0,o.jsx)("div",{className:"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg text-sm",children:s?x.conditionAr:x.condition})]})]})})})}),(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsx)("nav",{className:"flex space-x-8 overflow-x-auto",children:u.map(e=>(0,o.jsxs)("button",{onClick:()=>m(e.id),className:"\n                  flex items-center px-4 py-4 text-sm font-medium border-b-2 whitespace-nowrap transition-colors duration-200\n                  ".concat(a===e.id?"border-".concat(e.color,"-600 ").concat(p(e.color,!0)):"border-transparent ".concat(p(e.color,!1)),"\n                "),children:[(0,o.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})})}),(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["bodymap"===a&&(0,o.jsx)("div",{className:"space-y-6",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-user-md mr-3 text-blue-600"}),e("interactiveBodyMap","Interactive Body Map")]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:e("bodyMapDescription","Click on body regions to assess pain levels and track patient symptoms visually")})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-universal-access mr-1"}),e("accessible","Accessible")]}),(0,o.jsxs)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-language mr-1"}),e("multilingual","Multilingual")]})]})]}),(0,o.jsx)(i.A,{patientId:x.id,onSave:s=>{console.log("Body Map Data:",s),d.Ay.success(e("painAssessmentSaved","Pain assessment saved successfully"))},showInstructions:!0})]})}),"communication"===a&&(0,o.jsx)("div",{className:"space-y-6",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-comments mr-3 text-green-600"}),e("communicationHub","Multi-Channel Communication Hub")]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:e("communicationDescription","Send messages via Email, SMS, WhatsApp, and Push notifications with intelligent routing")})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-robot mr-1"}),e("smartRouting","Smart Routing")]}),(0,o.jsxs)("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-clock mr-1"}),e("scheduled","Scheduled")]})]})]}),(0,o.jsx)(n.A,{patientId:x.id,onSend:s=>{console.log("Communication Data:",s),d.Ay.success(e("messageSent","Message sent successfully"))}})]})}),"exercises"===a&&(0,o.jsx)("div",{className:"space-y-6",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-dumbbell mr-3 text-purple-600"}),e("exerciseLibrary","Exercise Library & Program Builder")]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:e("exerciseDescription","Browse 500+ exercises with video demonstrations and create adaptive programs")})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-video mr-1"}),"500+ ",e("exercises","Exercises")]}),(0,o.jsxs)("span",{className:"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-magic mr-1"}),e("adaptive","Adaptive")]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-2 gap-8",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-900 dark:text-white",children:e("browseExercises","Browse Exercise Library")}),(0,o.jsx)(l.h,{patientId:x.id,showProgramBuilder:!1})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-900 dark:text-white",children:e("createProgram","Create Exercise Program")}),(0,o.jsx)(l.A,{patientId:x.id,onSave:s=>{console.log("Exercise Program Data:",s),d.Ay.success(e("exerciseProgramSaved","Exercise program saved successfully"))}})]})]})]})}),"ai"===a&&(0,o.jsx)("div",{className:"space-y-6",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-brain mr-3 text-orange-600"}),e("aiAssistant","AI-Powered Clinical Assistant")]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:e("aiDescription","Get intelligent treatment recommendations, exercise modifications, and clinical insights")})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("span",{className:"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fab fa-google mr-1"}),"Gemini AI"]}),(0,o.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-robot mr-1"}),"ChatGPT"]}),(0,o.jsxs)("span",{className:"bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-server mr-1"}),"Local LLM"]})]})]}),(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("i",{className:"fas fa-brain text-6xl text-orange-600 mb-4"}),(0,o.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:e("aiAssistantActive","AI Assistant is Active")}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:e("aiAssistantNote","The AI Assistant is available as a floating button in the bottom-right corner of the screen")}),(0,o.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-md mx-auto",children:(0,o.jsxs)("p",{className:"text-blue-800 dark:text-blue-200 text-sm",children:[(0,o.jsx)("i",{className:"fas fa-info-circle mr-2"}),e("aiFloatingNote","Look for the brain icon in the bottom-right corner to access the AI Assistant")]})})]})]})}),"analytics"===a&&(0,o.jsx)("div",{className:"space-y-6",children:(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,o.jsxs)("div",{children:[(0,o.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,o.jsx)("i",{className:"fas fa-chart-line mr-3 text-indigo-600"}),e("aiAnalytics","AI Analytics Dashboard")]}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mt-2",children:e("analyticsDescription","Monitor AI usage, performance metrics, and clinical insights")})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[(0,o.jsxs)("span",{className:"bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-chart-bar mr-1"}),e("realTime","Real-time")]}),(0,o.jsxs)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,o.jsx)("i",{className:"fas fa-lightbulb mr-1"}),e("insights","Insights")]})]})]}),(0,o.jsx)(c.R,{})]})})]}),(0,o.jsx)(c.A,{patientData:x})]})}},9737:(e,s,a)=>{a.d(s,{A:()=>l});var t=a(5043),r=a(7921),i=a(3768),n=a(579);const l=e=>{let{patientId:s,onSend:a,templates:l=[]}=e;const{t:c,isRTL:d}=(0,r.o)(),[o,m]=(0,t.useState)(["email"]),[x,g]=(0,t.useState)(""),[u,p]=(0,t.useState)(""),[h,b]=(0,t.useState)(""),[y,f]=(0,t.useState)("normal"),[j,v]=(0,t.useState)(""),[N,w]=(0,t.useState)({}),k=[{id:"email",name:c("email","Email"),icon:"fas fa-envelope",color:"blue",available:!0,description:c("emailDesc","Send via email")},{id:"sms",name:c("sms","SMS"),icon:"fas fa-sms",color:"green",available:!0,description:c("smsDesc","Send via SMS")},{id:"whatsapp",name:c("whatsapp","WhatsApp"),icon:"fab fa-whatsapp",color:"green",available:!0,description:c("whatsappDesc","Send via WhatsApp")},{id:"push",name:c("pushNotification","Push Notification"),icon:"fas fa-bell",color:"purple",available:!0,description:c("pushDesc","Send push notification to mobile app")},{id:"telegram",name:c("telegram","Telegram"),icon:"fab fa-telegram",color:"blue",available:!1,description:c("telegramDesc","Send via Telegram (Coming Soon)")}],A=[{id:"appointment_reminder",name:c("appointmentReminder","Appointment Reminder"),subject:c("appointmentReminderSubject","Appointment Reminder"),content:c("appointmentReminderContent","Dear {patientName}, this is a reminder for your appointment on {date} at {time}. Please arrive 15 minutes early."),channels:["email","sms","whatsapp","push"]},{id:"exercise_instructions",name:c("exerciseInstructions","Exercise Instructions"),subject:c("exerciseInstructionsSubject","Your Exercise Program"),content:c("exerciseInstructionsContent","Dear {patientName}, please find your personalized exercise program. Follow the instructions carefully and contact us if you have any questions."),channels:["email","whatsapp"]},{id:"progress_update",name:c("progressUpdate","Progress Update"),subject:c("progressUpdateSubject","Your Progress Update"),content:c("progressUpdateContent","Dear {patientName}, here is your latest progress update. Keep up the great work!"),channels:["email","whatsapp","push"]},{id:"medication_reminder",name:c("medicationReminder","Medication Reminder"),subject:c("medicationReminderSubject","Medication Reminder"),content:c("medicationReminderContent","Time to take your medication: {medicationName}. Follow the prescribed dosage."),channels:["sms","push"]},{id:"custom",name:c("customMessage","Custom Message"),subject:"",content:"",channels:["email","sms","whatsapp","push"]}];(0,t.useEffect)(()=>{S()},[s]);const S=async()=>{try{const e={preferredChannels:["email","whatsapp"],language:"en",timezone:"Asia/Riyadh",quietHours:{start:"22:00",end:"08:00"},urgentOnly:!1,emailFrequency:"all",smsFrequency:"urgent",whatsappFrequency:"all",pushFrequency:"all"};w(e),m(e.preferredChannels)}catch(e){console.error("Error loading patient preferences:",e)}},C=e=>{m(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])};return(0,n.jsx)("div",{className:"communication-manager",children:(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[(0,n.jsx)("i",{className:"fas fa-comments mr-2 text-blue-600"}),c("communicationCenter","Communication Center")]}),(0,n.jsxs)("button",{onClick:()=>{const e="urgent"===y?["sms","push","whatsapp"]:"high"===y?["email","whatsapp","push"]:N.preferredChannels||["email"];m(e),i.Ay.success(c("channelsAutoSelected","Channels auto-selected based on urgency and patient preferences"))},className:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-magic mr-1"}),c("autoSelect","Auto Select")]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("messageTemplate","Message Template")}),(0,n.jsxs)("select",{value:h,onChange:e=>(e=>{const s=A.find(s=>s.id===e);if(s){b(e),p(s.subject),g(s.content);const a=s.channels.filter(e=>k.find(s=>s.id===e&&s.available));m(a)}})(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,n.jsx)("option",{value:"",children:c("selectTemplate","Select a template")}),A.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),o.includes("email")&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("subject","Subject")}),(0,n.jsx)("input",{type:"text",value:u,onChange:e=>p(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:c("enterSubject","Enter email subject")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("message","Message")}),(0,n.jsx)("textarea",{value:x,onChange:e=>g(e.target.value),rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none",placeholder:c("enterMessage","Enter your message...")}),(0,n.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[x.length,"/1000 ",c("characters","characters")]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("urgency","Urgency")}),(0,n.jsxs)("select",{value:y,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,n.jsx)("option",{value:"low",children:c("lowUrgency","Low")}),(0,n.jsx)("option",{value:"normal",children:c("normalUrgency","Normal")}),(0,n.jsx)("option",{value:"high",children:c("highUrgency","High")}),(0,n.jsx)("option",{value:"urgent",children:c("urgentUrgency","Urgent")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[c("scheduleTime","Schedule Time")," (",c("optional","Optional"),")"]}),(0,n.jsx)("input",{type:"datetime-local",value:j,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",children:c("communicationChannels","Communication Channels")}),(0,n.jsx)("div",{className:"space-y-3",children:k.map(e=>(0,n.jsx)("div",{className:"p-3 border rounded-lg transition-all cursor-pointer ".concat(o.includes(e.id)?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"," ").concat(e.available?"":"opacity-50 cursor-not-allowed"),onClick:()=>e.available&&C(e.id),children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("i",{className:"".concat(e.icon," text-").concat(e.color,"-600 text-lg")}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.description})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.available&&(0,n.jsx)("span",{className:"text-xs text-gray-400",children:c("comingSoon","Coming Soon")}),(0,n.jsx)("input",{type:"checkbox",checked:o.includes(e.id),onChange:()=>e.available&&C(e.id),disabled:!e.available,className:"w-4 h-4 text-blue-600 rounded focus:ring-blue-500"})]})]})},e.id))})]}),N.preferredChannels&&(0,n.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsx)("h5",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:c("patientPreferences","Patient Preferences")}),(0,n.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[c("preferredChannels","Preferred"),": ",N.preferredChannels.join(", ")]}),N.quietHours&&(0,n.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[c("quietHours","Quiet Hours"),": ",N.quietHours.start," - ",N.quietHours.end]})]}),(0,n.jsxs)("button",{onClick:async()=>{if(!(x.trim()?o.includes("email")&&!u.trim()?(i.Ay.error(c("subjectRequired","Subject is required for email")),0):0!==o.length||(i.Ay.error(c("selectChannel","Please select at least one communication channel")),0):(i.Ay.error(c("messageRequired","Message content is required")),0)))return;const e={patientId:s,channels:o,message:x,subject:u,urgency:y,scheduledTime:j||null,template:h,timestamp:(new Date).toISOString()};try{a&&await a(e),console.log("Sending communication:",e),i.Ay.success(c("messageSent","Message sent successfully via selected channels")),g(""),p(""),b(""),v("")}catch(t){i.Ay.error(c("sendError","Error sending message")),console.error("Send error:",t)}},disabled:0===o.length||!x.trim(),className:"w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-paper-plane mr-2"}),j?c("scheduleMessage","Schedule Message"):c("sendMessage","Send Message")]})]})]})]})})}}}]);
//# sourceMappingURL=9188.f0c82232.chunk.js.map