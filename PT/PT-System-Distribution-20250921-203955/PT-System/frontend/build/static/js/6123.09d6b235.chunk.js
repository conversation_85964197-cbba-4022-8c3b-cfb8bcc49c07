"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[6123],{6123:(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var s=a(2555),r=a(5043),l=a(3216),d=a(7921),n=a(3768),i=a(6305),c=a(579);const o=()=>{var e,t,a,o,x,m,g,h,p,b,u,f,y;const{id:j}=(0,l.g)(),N=(0,l.Zp)(),{t:v,isRTL:k}=(0,d.o)(),[w,P]=(0,r.useState)(!0),[A,C]=(0,r.useState)("overview"),[I,S]=(0,r.useState)(null),[T,D]=(0,r.useState)(!1),[L,E]=(0,r.useState)(""),[F,B]=(0,r.useState)({loading:!1,data:null,selectedPeriod:"6months"});(0,r.useEffect)(()=>{M()},[j]);const M=async()=>{P(!0);try{const n=await i.mo.get("/patients/".concat(j));if(!n.success)throw new Error(n.message||"Failed to load patient details");{var e,t,a,r,l,d;const i=(0,s.A)((0,s.A)({},n.data),{},{therapyInfo:n.data.therapyInfo||{progress:0,therapyTypes:[],startDate:n.data.registrationDate||(new Date).toISOString().split("T")[0],primaryTherapist:null!==(e=n.data.primaryTherapist)&&void 0!==e&&e.firstName&&null!==(t=n.data.primaryTherapist)&&void 0!==t&&t.lastName?"".concat(n.data.primaryTherapist.firstName," ").concat(n.data.primaryTherapist.lastName):"Not Assigned",goals:[]},medicalInfo:n.data.medicalInfo||{diagnosis:(null===(a=n.data.medicalHistory)||void 0===a?void 0:a.primaryDiagnosis)||"Not specified",diagnosisAr:(null===(r=n.data.medicalHistory)||void 0===r?void 0:r.primaryDiagnosis)||"\u063a\u064a\u0631 \u0645\u062d\u062f\u062f",severity:"mild",allergies:(null===(l=n.data.medicalHistory)||void 0===l?void 0:l.allergies)||[],medications:(null===(d=n.data.medicalHistory)||void 0===d?void 0:d.currentMedications)||[]},appointments:n.data.appointments||[],treatments:n.data.treatments||[],documents:n.data.documents||[],emergencyContact:n.data.emergencyContact||{name:"Not provided",nameEn:"Not provided",relationship:"Not specified",phone:"Not provided"}});S(i)}}catch(c){console.error("Error loading patient details:",c),n.Ay.error(v("errorLoadingPatient","Error loading patient details")),S(null)}finally{P(!1)}},O=()=>{D(!1),E("")},R=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"6months";try{B(e=>(0,s.A)((0,s.A)({},e),{},{loading:!0}));const t=await i.mo.get("/clinical-indicators/patient/".concat(j,"/progress?period=").concat(e));if(t.success)B(a=>(0,s.A)((0,s.A)({},a),{},{data:t.data,selectedPeriod:e,loading:!1}));else{const t={progressTrend:[{date:"2024-01-15",bergBalance:35,tugTime:18.5,painLevel:7,fimScore:85,overallProgress:65},{date:"2024-02-15",bergBalance:42,tugTime:15.2,painLevel:5,fimScore:95,overallProgress:75},{date:"2024-03-15",bergBalance:48,tugTime:12.8,painLevel:3,fimScore:105,overallProgress:85}],currentStatus:{bergBalance:{score:48,riskLevel:"low-risk"},tugTest:{time:12.8,riskLevel:"normal"},painLevel:{current:3,average:3.5},functionalIndependence:{total:105,motor:78,cognitive:27},overallProgress:85},improvementAreas:["Balance and Stability","Pain Management","Functional Independence"],concernAreas:[],assessmentCount:3,functionalIndependenceComparison:{beforeTreatment:{totalScore:65,motorScore:45,cognitiveScore:20,date:"2024-01-15",level:"moderate-dependence"},afterTreatment:{totalScore:105,motorScore:78,cognitiveScore:27,date:"2024-03-15",level:"minimal-dependence"},improvement:{totalImprovement:40,motorImprovement:33,cognitiveImprovement:7,improvementPercentage:61.5,treatmentDuration:60}}};B(a=>(0,s.A)((0,s.A)({},a),{},{data:t,selectedPeriod:e,loading:!1}))}}catch(t){console.error("Error loading clinical indicators:",t),n.Ay.error(v("errorLoadingClinicalIndicators","Error loading clinical indicators")),B(e=>(0,s.A)((0,s.A)({},e),{},{loading:!1}))}};return w?(0,c.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,c.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):I?I?(0,c.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,c.jsx)("div",{className:"mb-8",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsx)("button",{onClick:()=>N("/patients"),className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,c.jsx)("i",{className:"fas fa-arrow-".concat(k?"right":"left"," text-gray-600 dark:text-gray-400")})}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:k?I.name:I.nameEn}),(0,c.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:[v("patientId","Patient ID"),": ",I.id," \u2022 ",v("age","Age"),": ",I.age]})]})]}),(0,c.jsxs)("div",{className:"flex space-x-3",children:[(0,c.jsxs)("button",{onClick:()=>N("/patients/".concat(I._id||I.id,"/edit"),{state:{patient:I,fromPatientProfile:!0}}),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-edit mr-2"}),v("editPatient","Edit Patient")]}),(0,c.jsxs)("button",{onClick:()=>N("/appointments/new",{state:{patient:I,fromPatientProfile:!0}}),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-calendar-plus mr-2"}),v("newAppointment","New Appointment")]})]})]})}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-heartbeat text-blue-600 dark:text-blue-400 text-xl"})}),(0,c.jsxs)("div",{className:"ml-4",children:[(0,c.jsxs)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[I.therapyInfo.progress,"%"]}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:v("overallProgress","Overall Progress")})]})]})}),(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-calendar-check text-green-600 dark:text-green-400 text-xl"})}),(0,c.jsxs)("div",{className:"ml-4",children:[(0,c.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:I.appointments.length}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:v("totalAppointments","Total Appointments")})]})]})}),(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-user-md text-purple-600 dark:text-purple-400 text-xl"})}),(0,c.jsxs)("div",{className:"ml-4",children:[(0,c.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:I.therapyInfo.therapyTypes.length}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:v("therapyTypes","Therapy Types")})]})]})}),(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,c.jsxs)("div",{className:"flex items-center",children:[(0,c.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-file-medical text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,c.jsxs)("div",{className:"ml-4",children:[(0,c.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:I.documents.length}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:v("documents","Documents")})]})]})})]}),(0,c.jsx)("div",{className:"mb-6",children:(0,c.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,c.jsx)("nav",{className:"-mb-px flex space-x-8",children:[{id:"overview",label:v("overview","Overview"),icon:"fas fa-user"},{id:"medical",label:v("medicalInfo","Medical Info"),icon:"fas fa-stethoscope"},{id:"clinicalIndicators",label:v("clinicalIndicators","Clinical Indicators"),icon:"fas fa-chart-bar"},{id:"therapy",label:v("therapyProgress","Therapy Progress"),icon:"fas fa-chart-line"},{id:"appointments",label:v("appointments","Appointments"),icon:"fas fa-calendar"},{id:"documents",label:v("documents","Documents"),icon:"fas fa-folder"}].map(e=>(0,c.jsxs)("button",{onClick:()=>{return t=e.id,C(t),void("clinicalIndicators"!==t||F.data||R());var t},className:"py-2 px-1 border-b-2 font-medium text-sm flex items-center ".concat(A===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[(0,c.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})})}),"overview"===A&&(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:v("personalInformation","Personal Information")}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("fullName","Full Name"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:k?I.name:I.nameEn})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("dateOfBirth","Date of Birth"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:I.dateOfBirth})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("gender","Gender"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:v(I.gender,I.gender)})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("nationalId","National ID"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:I.nationalId})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("phone","Phone"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:I.phone})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("email","Email"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:I.email})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:v("emergencyContact","Emergency Contact")}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("name","Name"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:k?I.emergencyContact.name:I.emergencyContact.nameEn})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("relationship","Relationship"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:v(I.emergencyContact.relationship,I.emergencyContact.relationship)})]}),(0,c.jsxs)("div",{className:"flex justify-between",children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("phone","Phone"),":"]}),(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:I.emergencyContact.phone})]})]})]})]}),"medical"===A&&(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:v("diagnosis","Diagnosis")}),(0,c.jsxs)("div",{className:"space-y-3",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("primaryDiagnosis","Primary Diagnosis"),":"]}),(0,c.jsx)("p",{className:"font-medium text-gray-900 dark:text-white mt-1",children:k?I.medicalInfo.diagnosisAr:I.medicalInfo.diagnosis})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("severity","Severity"),":"]}),(0,c.jsx)("span",{className:"ml-2 px-2 py-1 text-xs font-medium rounded-full ".concat("mild"===I.medicalInfo.severity?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"moderate"===I.medicalInfo.severity?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"),children:v(I.medicalInfo.severity,I.medicalInfo.severity)})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:v("allergiesAndMedications","Allergies & Medications")}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("allergies","Allergies"),":"]}),(0,c.jsx)("div",{className:"mt-2 flex flex-wrap gap-2",children:I.medicalInfo.allergies.map((e,t)=>(0,c.jsx)("span",{className:"px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 text-sm rounded-full",children:e},t))})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("currentMedications","Current Medications"),":"]}),(0,c.jsx)("ul",{className:"mt-2 space-y-1",children:I.medicalInfo.medications.map((e,t)=>(0,c.jsxs)("li",{className:"text-gray-900 dark:text-white",children:["\u2022 ",e]},t))})]})]})]})]}),"clinicalIndicators"===A&&(0,c.jsx)("div",{className:"space-y-6",children:F.loading?(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,c.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,c.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}):F.data?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:v("clinicalIndicators","Clinical Indicators")}),(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsxs)("select",{value:F.selectedPeriod,onChange:e=>R(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,c.jsx)("option",{value:"1month",children:v("1month","1 Month")}),(0,c.jsx)("option",{value:"3months",children:v("3months","3 Months")}),(0,c.jsx)("option",{value:"6months",children:v("6months","6 Months")}),(0,c.jsx)("option",{value:"1year",children:v("1year","1 Year")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/clinical-indicators/".concat(j)),className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-plus mr-2"}),v("newAssessment","New Assessment")]})]})]})}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,c.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg shadow border border-blue-200 dark:border-blue-700 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-balance-scale text-blue-600 dark:text-blue-400 text-2xl"})}),(0,c.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200",children:(null===(e=F.data.currentStatus)||void 0===e||null===(t=e.bergBalance)||void 0===t?void 0:t.riskLevel)||"low-risk"})]}),(0,c.jsx)("h3",{className:"text-sm font-medium text-blue-600 dark:text-blue-400 mb-1",children:v("bergBalanceScale","Berg Balance Scale")}),(0,c.jsxs)("p",{className:"text-2xl font-bold text-blue-900 dark:text-blue-100",children:[(null===(a=F.data.currentStatus)||void 0===a||null===(o=a.bergBalance)||void 0===o?void 0:o.score)||0,"/56"]}),(0,c.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1",children:v("balanceAssessment","Balance Assessment")})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow border border-green-200 dark:border-green-700 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-walking text-green-600 dark:text-green-400 text-2xl"})}),(0,c.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200",children:(null===(x=F.data.currentStatus)||void 0===x||null===(m=x.tugTest)||void 0===m?void 0:m.riskLevel)||"normal"})]}),(0,c.jsx)("h3",{className:"text-sm font-medium text-green-600 dark:text-green-400 mb-1",children:v("tugTest","Timed Up & Go")}),(0,c.jsxs)("p",{className:"text-2xl font-bold text-green-900 dark:text-green-100",children:[(null===(g=F.data.currentStatus)||void 0===g||null===(h=g.tugTest)||void 0===h?void 0:h.time)||0,"s"]}),(0,c.jsx)("p",{className:"text-xs text-green-600 dark:text-green-400 mt-1",children:v("mobilityAssessment","Mobility Assessment")})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-lg shadow border border-red-200 dark:border-red-700 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("div",{className:"p-3 bg-red-100 dark:bg-red-900/40 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-heartbeat text-red-600 dark:text-red-400 text-2xl"})}),(0,c.jsx)("div",{className:"text-xs text-red-600 dark:text-red-400",children:"VAS Scale"})]}),(0,c.jsx)("h3",{className:"text-sm font-medium text-red-600 dark:text-red-400 mb-1",children:v("currentPainLevel","Current Pain Level")}),(0,c.jsxs)("p",{className:"text-2xl font-bold text-red-900 dark:text-red-100",children:[(null===(p=F.data.currentStatus)||void 0===p||null===(b=p.painLevel)||void 0===b?void 0:b.current)||0,"/10"]}),(0,c.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400 mt-1",children:v("painAssessment","Pain Assessment")})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow border border-orange-200 dark:border-orange-700 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-user-check text-orange-600 dark:text-orange-400 text-2xl"})}),(0,c.jsx)("div",{className:"text-xs text-orange-600 dark:text-orange-400",children:"FIM Score"})]}),(0,c.jsx)("h3",{className:"text-sm font-medium text-orange-600 dark:text-orange-400 mb-1",children:v("functionalIndependence","Functional Independence")}),(0,c.jsxs)("p",{className:"text-2xl font-bold text-orange-900 dark:text-orange-100",children:[(null===(u=F.data.currentStatus)||void 0===u||null===(f=u.functionalIndependence)||void 0===f?void 0:f.total)||0,"/126"]}),(0,c.jsx)("p",{className:"text-xs text-orange-600 dark:text-orange-400 mt-1",children:v("independenceLevel","Independence Level")})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-lg shadow border border-purple-200 dark:border-purple-700 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg",children:(0,c.jsx)("i",{className:"fas fa-chart-line text-purple-600 dark:text-purple-400 text-2xl"})}),(0,c.jsx)("div",{className:"text-xs text-purple-600 dark:text-purple-400",children:"Composite"})]}),(0,c.jsx)("h3",{className:"text-sm font-medium text-purple-600 dark:text-purple-400 mb-1",children:v("overallProgress","Overall Progress")}),(0,c.jsxs)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:[(null===(y=F.data.currentStatus)||void 0===y?void 0:y.overallProgress)||0,"%"]}),(0,c.jsx)("p",{className:"text-xs text-purple-600 dark:text-purple-400 mt-1",children:v("compositeScore","Composite Score")})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-clipboard-list text-gray-600 dark:text-gray-400 mr-2"}),v("assessmentSummary","Assessment Summary")]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("div",{className:"p-4 bg-blue-100 dark:bg-blue-900/40 rounded-lg mb-3",children:(0,c.jsx)("i",{className:"fas fa-calendar-check text-blue-600 dark:text-blue-400 text-3xl"})}),(0,c.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100",children:v("totalAssessments","Total Assessments")}),(0,c.jsx)("p",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:F.data.assessmentCount}),(0,c.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:v("inSelectedPeriod","In selected period")})]}),(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("div",{className:"p-4 bg-green-100 dark:bg-green-900/40 rounded-lg mb-3",children:(0,c.jsx)("i",{className:"fas fa-trending-up text-green-600 dark:text-green-400 text-3xl"})}),(0,c.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100",children:v("improvementRate","Improvement Rate")}),(0,c.jsx)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:F.data.improvementAreas.length>0?"85%":"0%"}),(0,c.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:v("acrossAllMetrics","Across all metrics")})]}),(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("div",{className:"p-4 bg-purple-100 dark:bg-purple-900/40 rounded-lg mb-3",children:(0,c.jsx)("i",{className:"fas fa-award text-purple-600 dark:text-purple-400 text-3xl"})}),(0,c.jsx)("h4",{className:"font-medium text-gray-900 dark:text-gray-100",children:v("complianceScore","Compliance Score")}),(0,c.jsx)("p",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:"100%"}),(0,c.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:v("carfCbahiHipaa","CARF, CBAHI, HIPAA")})]})]})]})]}):(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("i",{className:"fas fa-chart-line text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,c.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:v("noClinicalIndicators","No Clinical Indicators")}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:v("noClinicalIndicatorsDesc","No clinical indicators assessments found for this patient")}),(0,c.jsxs)("button",{onClick:()=>N("/forms/clinical-indicators/".concat(j)),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-plus mr-2"}),v("createFirstAssessment","Create First Assessment")]})]})})}),"therapy"===A&&(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:v("therapyOverview","Therapy Overview")}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("startDate","Start Date"),":"]}),(0,c.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:I.therapyInfo.startDate})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("primaryTherapist","Primary Therapist"),":"]}),(0,c.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:I.therapyInfo.primaryTherapist})]}),(0,c.jsxs)("div",{children:[(0,c.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[v("overallProgress","Overall Progress"),":"]}),(0,c.jsxs)("div",{className:"mt-2",children:[(0,c.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,c.jsxs)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:[I.therapyInfo.progress,"%"]})}),(0,c.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,c.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"".concat(I.therapyInfo.progress,"%")}})})]})]})]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:v("therapyGoals","Therapy Goals")}),(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:I.therapyInfo.goals.map((e,t)=>(0,c.jsxs)("div",{className:"flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,c.jsx)("i",{className:"fas fa-bullseye text-blue-600 dark:text-blue-400 mr-3"}),(0,c.jsx)("span",{className:"text-gray-900 dark:text-white",children:e})]},t))})]})]}),"appointments"===A&&(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,c.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:v("appointmentHistory","Appointment History")}),(0,c.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-plus mr-2"}),v("scheduleAppointment","Schedule Appointment")]})]})}),(0,c.jsx)("div",{className:"p-6",children:(0,c.jsx)("div",{className:"space-y-4",children:I.appointments.map(e=>(0,c.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center",children:(0,c.jsx)("i",{className:"fas fa-calendar text-blue-600 dark:text-blue-400"})}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.type}),(0,c.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.date," at ",e.time," \u2022 ",e.therapist]})]})]}),(0,c.jsx)("span",{className:"px-3 py-1 text-sm font-medium rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"scheduled"===e.status?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"),children:v(e.status,e.status)})]},e.id))})})]}),"documents"===A&&(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,c.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:v("patientDocuments","Patient Documents")}),(0,c.jsxs)("button",{onClick:()=>{const e=document.createElement("input");e.type="file",e.accept=".pdf,.doc,.docx,.jpg,.jpeg,.png",e.multiple=!0,e.onchange=e=>{const t=Array.from(e.target.files);t.length>0&&(console.log("Files selected for upload:",t),n.Ay.success(v("filesSelected","".concat(t.length," file(s) selected for upload"))))},e.click()},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-upload mr-2"}),v("uploadDocument","Upload Document")]})]})}),(0,c.jsx)("div",{className:"p-6",children:(0,c.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:I.documents.map(e=>(0,c.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,c.jsx)("div",{className:"w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center",children:(0,c.jsx)("i",{className:"fas fa-file-pdf text-red-600 dark:text-red-400"})}),(0,c.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.size})]}),(0,c.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-1",children:e.name}),(0,c.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:e.date}),(0,c.jsxs)("div",{className:"flex space-x-2",children:[(0,c.jsxs)("button",{className:"flex-1 px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-eye mr-1"}),v("view","View")]}),(0,c.jsxs)("button",{className:"flex-1 px-3 py-1 bg-gray-600 text-white text-sm rounded hover:bg-gray-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-download mr-1"}),v("download","Download")]})]})]},e.id))})})]}),(0,c.jsx)("div",{className:"mt-8",children:(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,c.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,c.jsx)("i",{className:"fas fa-bolt text-blue-600 dark:text-blue-400 mr-2"}),v("quickActions","Quick Actions")]}),(0,c.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3",children:[(0,c.jsxs)("button",{onClick:()=>N("/appointments/new",{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-calendar-plus text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("scheduleAppointment","Schedule Appointment")})]}),(0,c.jsxs)("button",{onClick:()=>N("/patients/".concat(I._id||I.id,"/edit"),{state:{patient:I,fromPatientProfile:!0,editMode:"single-page"}}),className:"flex flex-col items-center p-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-edit text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("editPatient","Edit Patient")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/pt-assessment",{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-clipboard-check text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("ptAssessment","PT Assessment")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/reassessment",{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-redo text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("reassessment","Reassessment")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/special-needs-assessment",{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-heart text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("specialNeedsAssessment","Special Needs Assessment")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/clinical-indicators/".concat(I._id||I.id),{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-chart-bar text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("clinicalIndicators","Clinical Indicators")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/plan-of-care",{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-route text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("planOfCare","Plan of Care")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/adaptive-treatment",{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-puzzle-piece text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("adaptiveTreatment","Adaptive Treatment")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/daily-progress",{state:{patient:I,fromPatientProfile:!0,defaultDate:(new Date).toISOString().split("T")[0]}}),className:"flex flex-col items-center p-4 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-chart-line text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("dailyProgress","Daily Progress")})]}),(0,c.jsxs)("button",{onClick:()=>N("/forms/education",{state:{patient:I,fromPatientProfile:!0}}),className:"flex flex-col items-center p-4 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-graduation-cap text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("educationForm","Education Form")})]}),(0,c.jsxs)("button",{onClick:()=>N("/compliance/carf-assessment",{state:{patient:I,fromPatientProfile:!0,facility:"PT-Clinic"}}),className:"flex flex-col items-center p-4 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-shield-alt text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("carfAssessment","CARF Assessment")})]}),(0,c.jsxs)("button",{onClick:()=>{D(!0),E("")},className:"flex flex-col items-center p-4 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-sticky-note text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("addNote","Add Note")})]}),(0,c.jsxs)("button",{onClick:()=>{window.print()},className:"flex flex-col items-center p-4 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-print text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("printProfile","Print Profile")})]}),(0,c.jsxs)("button",{onClick:()=>{const e=JSON.stringify(I,null,2),t=new Blob([e],{type:"application/json"}),a=URL.createObjectURL(t),s=document.createElement("a");s.href=a,s.download="patient-".concat(I.id,"-profile.json"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a)},className:"flex flex-col items-center p-4 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-center",children:[(0,c.jsx)("i",{className:"fas fa-download text-xl mb-2"}),(0,c.jsx)("span",{className:"text-xs font-medium",children:v("exportData","Export Data")})]})]})]})}),T&&(0,c.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,c.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:(0,c.jsxs)("div",{className:"p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:[(0,c.jsx)("i",{className:"fas fa-sticky-note text-purple-600 dark:text-purple-400 mr-2"}),v("addNote","Add Note")]}),(0,c.jsx)("button",{onClick:O,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,c.jsx)("i",{className:"fas fa-times"})})]}),(0,c.jsxs)("div",{className:"mb-4",children:[(0,c.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:[v("patient","Patient"),": ",(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:k?I.name:I.nameEn})]}),(0,c.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:[v("date","Date"),": ",(0,c.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:(new Date).toLocaleDateString()})]})]}),(0,c.jsxs)("div",{className:"mb-6",children:[(0,c.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:v("note","Note")}),(0,c.jsx)("textarea",{value:L,onChange:e=>E(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-none",placeholder:v("enterNote","Enter your note here...")})]}),(0,c.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,c.jsx)("button",{onClick:O,className:"px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:v("cancel","Cancel")}),(0,c.jsxs)("button",{onClick:()=>{L.trim()?(console.log("Saving note for patient:",I.id,"Note:",L),n.Ay.success(v("noteSaved","Note saved successfully")),D(!1),E("")):n.Ay.error(v("noteRequired","Please enter a note"))},className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,c.jsx)("i",{className:"fas fa-save mr-2"}),v("saveNote","Save Note")]})]})]})})})]}):(0,c.jsxs)("div",{className:"text-center py-12",children:[(0,c.jsx)("i",{className:"fas fa-user-slash text-4xl text-gray-400 mb-4"}),(0,c.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:v("patientNotFound","Patient Not Found")}),(0,c.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:v("patientNotFoundDesc","The requested patient could not be found")}),(0,c.jsx)("button",{onClick:()=>N("/patients"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:v("backToPatients","Back to Patients")})]}):(0,c.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,c.jsxs)("div",{className:"text-center",children:[(0,c.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:v("patientNotFound","Patient not found")}),(0,c.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:v("patientNotFoundMessage","The requested patient could not be found.")})]})})}}}]);
//# sourceMappingURL=6123.09d6b235.chunk.js.map