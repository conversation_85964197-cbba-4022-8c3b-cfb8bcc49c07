{"version": 3, "file": "static/js/4120.55da6506.chunk.js", "mappings": "oNAGA,MAwOA,EAxO+BA,IAAkE,IAADC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAAhE,SAAEC,EAAQ,kBAAEC,EAAiB,OAAEC,EAAM,mBAAEC,GAAoBV,EACzF,MAAM,EAAEW,IAAMC,EAAAA,EAAAA,KAEd,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DACZH,EAAE,sBAAuB,4BAE5BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EJ,EAAE,iBAAkB,sBAEvBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASY,eAChBC,SAAWC,GAAMb,EAAkB,iBAAkBa,EAAEC,OAAOJ,OAC9DJ,UAAU,kKACVS,YAAY,YAGhBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EJ,EAAE,YAAa,iBAElBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASiB,UAChBJ,SAAWC,GAAMb,EAAkB,YAAaa,EAAEC,OAAOJ,OACzDJ,UAAU,wKAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EJ,EAAE,UAAW,cAEhBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASkB,QAChBL,SAAWC,GAAMb,EAAkB,UAAWa,EAAEC,OAAOJ,OACvDJ,UAAU,kKACVS,YAAY,WAGhBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EJ,EAAE,eAAgB,oBAErBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASmB,aAChBN,SAAWC,GAAMb,EAAkB,eAAgBa,EAAEC,OAAOJ,OAC5DJ,UAAU,kKACVS,YAAY,iBAOpBV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDACZH,EAAE,qBAAsB,0BAI1BD,IACCG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACZH,EAAE,wBAAyB,uCAE9BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gDAA+CC,SAAA,EAC5DF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DJ,EAAE,mBAAoB,sBAAsB,QAE/CK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDL,EAAmBiB,kBAGxBd,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DJ,EAAE,iBAAkB,oBAAoB,QAE3CK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,UACnB,QAAjCd,EAAAS,EAAmBkB,sBAAc,IAAA3B,OAAA,EAAjCA,EAAmC4B,QAAS,YAGjDhB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,+CAA8CC,SAAA,CAC3DJ,EAAE,gBAAiB,mBAAmB,QAEzCK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,UACpB,QAAhCb,EAAAQ,EAAmBoB,qBAAa,IAAA5B,OAAA,EAAhCA,EAAkC2B,QAAS,eAMlDhB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,CACxEJ,EAAE,gBAAiB,kBAAkB,QAExCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uDAAsDC,SAAA,CACnEJ,EAAE,iBAAkB,oBAAoB,QAE3CK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0DAAyDC,UACnC,QAAjCZ,EAAAO,EAAmBkB,sBAAc,IAAAzB,GAAO,QAAPC,EAAjCD,EAAmC4B,aAAK,IAAA3B,OAAP,EAAjCA,EAA0C4B,IAAI,CAACC,EAAMC,KACpDrB,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,mBAAkBC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,YACvBC,EAAAA,EAAAA,KAAA,QAAAD,SAAOkB,MAFAC,OAILlB,EAAAA,EAAAA,KAAA,MAAAD,SAAKJ,EAAE,kBAAmB,6BAGpCE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uDAAsDC,SAAA,CACnEJ,EAAE,gBAAiB,mBAAmB,QAEzCK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0DAAyDC,UACpC,QAAhCV,EAAAK,EAAmBoB,qBAAa,IAAAzB,GAAO,QAAPC,EAAhCD,EAAkC0B,aAAK,IAAAzB,OAAP,EAAhCA,EAAyC0B,IAAI,CAACC,EAAMC,KACnDrB,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,mBAAkBC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,YACvBC,EAAAA,EAAAA,KAAA,QAAAD,SAAOkB,MAFAC,OAILlB,EAAAA,EAAAA,KAAA,MAAAD,SAAKJ,EAAE,kBAAmB,sCAQ5CE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EJ,EAAE,cAAe,gBAAgB,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAAS4B,YAChBf,SAAWC,GAAMb,EAAkB,cAAea,EAAEC,OAAOJ,OAC3DJ,UAAS,mJAAAsB,OACP3B,EAAO0B,YAAc,iBAAmB,mBAE1CZ,YAAaZ,EAAE,mBAAoB,wBAEpCF,EAAO0B,cACNnB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAO0B,kBAGrDtB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EJ,EAAE,WAAY,QAAQ,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEzDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAAS8B,SAChBjB,SAAWC,GAAMb,EAAkB,WAAYa,EAAEC,OAAOJ,OACxDJ,UAAS,mJAAAsB,OACP3B,EAAO4B,SAAW,iBAAmB,mBAEvCd,YAAaZ,EAAE,gBAAiB,qBAEjCF,EAAO4B,WACNrB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAO4B,eAGrDxB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EJ,EAAE,YAAa,aAAa,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DC,EAAAA,EAAAA,KAAA,YACEE,MAAOX,EAAS+B,UAChBlB,SAAWC,GAAMb,EAAkB,YAAaa,EAAEC,OAAOJ,OACzDqB,KAAM,EACNzB,UAAS,mJAAAsB,OACP3B,EAAO6B,UAAY,iBAAmB,mBAExCf,YAAaZ,EAAE,iBAAkB,qBAElCF,EAAO6B,YACNtB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAO6B,gBAGrDzB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EJ,EAAE,0BAA2B,8BAA8B,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE9FC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASiC,wBAChBpB,SAAWC,GAAMb,EAAkB,0BAA2Ba,EAAEC,OAAOJ,OACvEuB,KAAK,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACzC9B,UAAS,mJAAAsB,OACP3B,EAAO+B,wBAA0B,iBAAmB,qBAGvD/B,EAAO+B,0BACNxB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAO+B,8BAGrD3B,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EJ,EAAE,YAAa,aAAa,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASsC,UAChBzB,SAAWC,GAAMb,EAAkB,YAAaa,EAAEC,OAAOJ,OACzDJ,UAAS,mJAAAsB,OACP3B,EAAOoC,UAAY,iBAAmB,mBAExCtB,YAAaZ,EAAE,qBAAsB,0BAEtCF,EAAOoC,YACN7B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAOoC,yBCrC/D,EA1L+B7C,IAAsC,IAArC,SAAEO,EAAQ,kBAAEC,GAAmBR,EAC7D,MAAM,EAAEW,IAAMC,EAAAA,EAAAA,KAERkC,EAAgB,CACpB,CACEC,IAAK,iBACLC,MAAOrC,EAAE,iBAAkB,mBAC3BsC,KAAM,8BACNC,MAAO,MACP3B,YAAaZ,EAAE,4BAA6B,qFAE9C,CACEoC,IAAK,gBACLC,MAAOrC,EAAE,gBAAiB,mBAC1BsC,KAAM,oBACNC,MAAO,OACP3B,YAAaZ,EAAE,iBAAkB,gFAEnC,CACEoC,IAAK,kBACLC,MAAOrC,EAAE,kBAAmB,qBAC5BsC,KAAM,kBACNC,MAAO,QACP3B,YAAaZ,EAAE,wBAAyB,wFAE1C,CACEoC,IAAK,cACLC,MAAOrC,EAAE,cAAe,gBACxBsC,KAAM,iBACNC,MAAO,SACP3B,YAAaZ,EAAE,yBAA0B,sFAE3C,CACEoC,IAAK,OACLC,MAAOrC,EAAE,OAAQ,SACjBsC,KAAM,cACNC,MAAO,SACP3B,YAAaZ,EAAE,kBAAmB,yFAEpC,CACEoC,IAAK,sBACLC,MAAOrC,EAAE,sBAAuB,yBAChCsC,KAAM,cACNC,MAAO,OACP3B,YAAaZ,EAAE,0BAA2B,wFAE5C,CACEoC,IAAK,cACLC,MAAOrC,EAAE,cAAe,gBACxBsC,KAAM,oBACNC,MAAO,SACP3B,YAAaZ,EAAE,yBAA0B,gGAI7C,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gEACZH,EAAE,8BAA+B,4CAGpCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yGAAwGC,SAAA,EACrHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEACZH,EAAE,oCAAqC,sDAE1CK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SACxDJ,EAAE,8BAA+B,qLAItCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB+B,EAAcd,IAAKmB,IAClBtC,EAAAA,EAAAA,MAAA,OAAoBC,UAAS,MAAAsB,OAAQe,EAAKD,MAAK,gBAAAd,OAAee,EAAKD,MAAK,0BAAAd,OAAyBe,EAAKD,MAAK,qBAAAd,OAAoBe,EAAKD,MAAK,uBAAsBnC,SAAA,EAC7JF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,kCAAAsB,OAAoCe,EAAKD,MAAK,mBAAAd,OAAkBe,EAAKD,MAAK,aAAYnC,SAAA,EACpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAsB,OAAKe,EAAKF,KAAI,UAAAb,OAASe,EAAKD,MAAK,mBAAAd,OAAkBe,EAAKD,MAAK,eACxEC,EAAKH,UAERhC,EAAAA,EAAAA,KAAA,YACEE,MAAOX,EAAS6C,gBAAgBD,EAAKJ,KACrC3B,SAAWC,GAAMb,EAAkB,mBAAD4B,OAAoBe,EAAKJ,KAAO1B,EAAEC,OAAOJ,OAC3EqB,KAAM,EACNzB,UAAS,kCAAAsB,OAAoCe,EAAKD,MAAK,4CAAAd,OAA2Ce,EAAKD,MAAK,sBAAAd,OAAqBe,EAAKD,MAAK,iBAAAd,OAAgBe,EAAKD,MAAK,qBAAAd,OAAoBe,EAAKD,MAAK,wBACnM3B,YAAa4B,EAAK5B,eAIpBV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDF,EAAAA,EAAAA,MAAA,OAAKC,UAAS,oCAAAsB,OAAsCe,EAAKD,MAAK,mBAAAd,OAAkBe,EAAKD,MAAK,QAAOnC,SAAA,EAC/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACbE,EAAAA,EAAAA,KAAA,QAAAD,SAAOJ,EAAE,8BAA+B,uCAE1CE,EAAAA,EAAAA,MAAA,OAAKC,UAAS,oCAAAsB,OAAsCe,EAAKD,MAAK,mBAAAd,OAAkBe,EAAKD,MAAK,QAAOnC,SAAA,EAC/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACbE,EAAAA,EAAAA,KAAA,QAAAD,SAAOJ,EAAE,4BAA6B,qCAExCE,EAAAA,EAAAA,MAAA,OAAKC,UAAS,oCAAAsB,OAAsCe,EAAKD,MAAK,mBAAAd,OAAkBe,EAAKD,MAAK,QAAOnC,SAAA,EAC/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACbE,EAAAA,EAAAA,KAAA,QAAAD,SAAOJ,EAAE,oBAAqB,iCAzB1BwC,EAAKJ,SAiCnBlC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gEACZH,EAAE,kCAAmC,qCAAqC,QAE7EE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,CACxEJ,EAAE,gBAAiB,mBAAmB,QAEzCE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,wBAAyB,qDAClCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,yBAA0B,2CACnCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,0BAA2B,iCACpCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,0BAA2B,sCACpCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,oBAAqB,kCAGlCE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,CACxEJ,EAAE,oBAAqB,sBAAsB,QAEhDE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,qBAAsB,6CAC/BE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,6BAA8B,8CACvCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,uBAAwB,0CACjCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,4BAA6B,qDACtCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,gCAAiC,oDAOlDE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDACZH,EAAE,uBAAwB,0BAA0B,QAEvDE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yEAEfE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEJ,EAAE,YAAa,iBAElBK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDJ,EAAE,uBAAwB,mCAG/BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sEAEfE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEJ,EAAE,mBAAoB,yBAEzBK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDJ,EAAE,yBAA0B,oCAGjCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DAEfE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnEJ,EAAE,aAAc,iBAEnBK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDJ,EAAE,wBAAyB,8CCgG1C,EAjR+BX,IAA0G,IAAzG,SAAEO,EAAQ,kBAAEC,EAAiB,kBAAE6C,EAAiB,QAAEC,EAAO,WAAEC,EAAU,OAAE9C,EAAM,mBAAEC,GAAoBV,EACjI,MAAM,EAAEW,IAAMC,EAAAA,EAAAA,KAER4C,EAAoB7C,EAAE,wBAAyB,qNAE/C8C,EAAuBA,CAACC,EAAUC,EAAOV,EAAMC,KAAW,IAADU,EAC7D,MAAMC,EAAWtD,EAASmD,GACpBI,EAAmC,QAAtBD,EAASE,SACtBC,EAAsC,OAAtBH,EAASE,SACzBE,GAAkC,OAAlBvD,QAAkB,IAAlBA,GAA8B,QAAZkD,EAAlBlD,EAAqBgD,UAAS,IAAAE,OAAZ,EAAlBA,EAAgC7B,QAAS,GACzDmC,EAA2B,mBAAbR,EAAgC,YAAc,WAElE,OACE7C,EAAAA,EAAAA,MAAA,OAAKC,UAAS,MAAAsB,OAAQc,EAAK,gBAAAd,OAAec,EAAK,0BAAAd,OAAyBc,EAAK,qBAAAd,OAAoBc,EAAK,uBAAsBnC,SAAA,EAC1HF,EAAAA,EAAAA,MAAA,MAAIC,UAAS,8BAAAsB,OAAgCc,EAAK,mBAAAd,OAAkBc,EAAK,aAAYnC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAsB,OAAKa,EAAI,UAAAb,OAASc,EAAK,mBAAAd,OAAkBc,EAAK,eACzDS,KAIFM,EAAcE,OAAS,IACtBtD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACZH,EAAE,yBAA0B,4BAA4B,QAE3DK,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qDAAoDC,SAC/DkD,EAAcjC,IAAI,CAACC,EAAMC,KACxBrB,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,mBAAkBC,SAAA,EAC1CF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,qBAAoBC,SAAA,CAAEmB,EAAQ,EAAE,QAChDlB,EAAAA,EAAAA,KAAA,QAAAD,SAAOkB,MAFAC,UAUjBrB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,kCAAAsB,OAAoCc,EAAK,mBAAAd,OAAkBc,EAAK,aAAYnC,SAAA,CACzFJ,EAAE,gBAAgB,YAADyB,OAAcuB,IAAS,KAAC3C,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE3EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,8EAAAsB,OACd0B,EAAU,uFAEN,2FACH/C,SAAA,EACDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLmD,KAAI,GAAAhC,OAAKsB,EAAQ,aACjBxC,MAAM,MACNmD,QAASP,EACT1C,SAAWC,GAAMb,EAAkB,GAAD4B,OAAIsB,EAAQ,aAAarC,EAAEC,OAAOJ,OACpEJ,UAAU,aAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uEAAAsB,OACZ0B,EAAa,gCAAkC,wCAC9C/C,SACA+C,IAAc9C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAEhCE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEJ,EAAE,MAAO,aAG1CE,EAAAA,EAAAA,MAAA,SAAOC,UAAS,8EAAAsB,OACd4B,EAAa,6EAET,uFACHjD,SAAA,EACDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLmD,KAAI,GAAAhC,OAAKsB,EAAQ,aACjBxC,MAAM,KACNmD,QAASL,EACT5C,SAAWC,IACTb,EAAkB,GAAD4B,OAAIsB,EAAQ,aAAarC,EAAEC,OAAOJ,OAE5B,OAAnBG,EAAEC,OAAOJ,OAAmB2C,EAASS,mBACvC9D,EAAkB,GAAD4B,OAAIsB,EAAQ,sBAAsBF,IAGvD1C,UAAU,aAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAS,uEAAAsB,OACZ4B,EAAgB,4BAA8B,wCAC7CjD,SACAiD,IAAiBhD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qCAEnCE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEJ,EAAE,KAAM,cAG1CF,EAAO,GAAD2B,OAAI8B,EAAW,eACpBlD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAO,GAAD2B,OAAI8B,EAAW,mBAKnErD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAS,kCAAAsB,OAAoCc,EAAK,mBAAAd,OAAkBc,EAAK,aAAYnC,SACzFJ,EAAE,QAAS,YAEdK,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLsD,IAAI,IACJ9B,IAAI,KACJvB,MAAO2C,EAAShC,MAChBT,SAAWC,GAAMb,EAAkB,GAAD4B,OAAIsB,EAAQ,UAAUrC,EAAEC,OAAOJ,OACjEJ,UAAS,gCAAAsB,OAAkCc,EAAK,4CAAAd,OAA2Cc,EAAK,sBAAAd,OAAqBc,EAAK,iBAAAd,OAAgBc,EAAK,qBAAAd,OAAoBc,EAAK,wBACxK3B,YAAY,UAEdP,EAAAA,EAAAA,KAAA,KAAGF,UAAS,gBAAAsB,OAAkBc,EAAK,mBAAAd,OAAkBc,EAAK,aAAYnC,SACnEJ,EAAE,YAAa,6CAKnBqD,IACCnD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,kCAAAsB,OAAoCc,EAAK,mBAAAd,OAAkBc,EAAK,aAAYnC,SAAA,CACzFJ,EAAE,oBAAqB,uBAAuB,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEjFC,EAAAA,EAAAA,KAAA,YACEE,MAAO2C,EAASS,kBAChBlD,SAAWC,GAAMb,EAAkB,GAAD4B,OAAIsB,EAAQ,sBAAsBrC,EAAEC,OAAOJ,OAC7EqB,KAAM,EACNzB,UAAS,8DAAAsB,OAAgEc,EAAK,sBAAAd,OAAqBc,EAAK,iBAAAd,OAAgBc,EAAK,qBAAAd,OAAoBc,EAAK,yBAAAd,OACpJ3B,EAAO,GAAD2B,OAAI8B,EAAW,WAAY,iBAAgB,UAAA9B,OAAac,EAAK,SAErE3B,YAAaiC,IAEd/C,EAAO,GAAD2B,OAAI8B,EAAW,aACpBlD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAO,GAAD2B,OAAI8B,EAAW,gBAMpEF,IACCnD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,SAAOC,UAAS,kCAAAsB,OAAoCc,EAAK,mBAAAd,OAAkBc,EAAK,QAAOnC,SAAA,CACpFJ,EAAE,eAAgB,iBAAiB,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEtEF,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLuD,QAASA,IAAMlB,EAAQI,GACvB5C,UAAS,gBAAAsB,OAAkBc,EAAK,wCAAAd,OAAuCc,EAAK,kCAAiCnC,SAAA,EAE7GC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZH,EAAE,UAAW,mBAIlBK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB8C,EAASY,aAAazC,IAAI,CAACC,EAAMC,KAChCrB,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,6BAA4BC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,QAAMC,UAAS,iCAAAsB,OAAmCc,EAAK,mBAAAd,OAAkBc,EAAK,qBAAoBnC,SAAA,CAC/FmB,EAAQ,EAAE,QAEblB,EAAAA,EAAAA,KAAA,YACEE,MAAOe,EACPb,SAAWC,GAAMgC,EAAkB,GAADjB,OAAIsB,EAAQ,iBAAiBxB,EAAOb,EAAEC,OAAOJ,OAC/EqB,KAAM,EACNzB,UAAS,kCAAAsB,OAAoCc,EAAK,4CAAAd,OAA2Cc,EAAK,sBAAAd,OAAqBc,EAAK,iBAAAd,OAAgBc,EAAK,qBAAAd,OAAoBc,EAAK,wBAC1K3B,YAAaZ,EAAE,kBAAmB,qBAEnCkD,EAASY,aAAaN,OAAS,IAC9BnD,EAAAA,EAAAA,KAAA,UACEC,KAAK,SACLuD,QAASA,IAAMjB,EAAWG,EAAUxB,GACpCpB,UAAU,6DAA4DC,UAEtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BAjBToB,MAwBbzB,EAAO,GAAD2B,OAAI8B,EAAW,mBACpBlD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAO,GAAD2B,OAAI8B,EAAW,oBAGjErD,EAAAA,EAAAA,MAAA,OAAKC,UAAS,eAAAsB,OAAiBc,EAAK,iBAAAd,OAAgBc,EAAK,sBAAqBnC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,MAAIC,UAAS,8BAAAsB,OAAgCc,EAAK,mBAAAd,OAAkBc,EAAK,aAAYnC,SAAA,EACnFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZH,EAAE,qBAAsB,wBAAwB,QAEnDK,EAAAA,EAAAA,KAAA,KAAGF,UAAS,gBAAAsB,OAAkBc,EAAK,mBAAAd,OAAkBc,EAAK,QAAOnC,SAC9DJ,EAAE,wBAAyB,sFAOnCmD,IACCjD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yDAAwDC,SACrEJ,EAAE,uBAAwB,iEAG/BK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDAAiDC,SAC3DJ,EAAE,oBAAqB,mFAQpC,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DACZH,EAAE,mBAAoB,0BAGzBE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvB0C,EACC,iBACA9C,EAAE,iBAAkB,oBACpB,cACA,QAID8C,EACC,gBACA9C,EAAE,gBAAiB,mBACnB,kBACA,aAKJE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yGAAwGC,SAAA,EACrHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEACZH,EAAE,4BAA6B,+BAA+B,QAEjEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,CAC5EJ,EAAE,oBAAqB,yBAAyB,QAEnDE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,+BAAgC,sCACzCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,wBAAyB,4CAClCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,4BAA6B,mCACtCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,sBAAuB,4CAGpCE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,CAC5EJ,EAAE,uBAAwB,6BAA6B,QAE1DE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,2BAA4B,kCACrCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,0BAA2B,iCACpCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,yBAA0B,gCACnCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,6BAA8B,2DCuErD,EA/U6BX,IAAsC,IAArC,SAAEO,EAAQ,kBAAEC,GAAmBR,EAC3D,MAAM,EAAEW,IAAMC,EAAAA,EAAAA,KAER8D,EAAsB,CAC1B,CACE3B,IAAK,cACLY,MAAOhD,EAAE,cAAe,gBACxBsC,KAAM,8BACNC,MAAO,MACPyB,QAAS,CACP,CAAE5B,IAAK,KAAMC,MAAOrC,EAAE,KAAM,OAC5B,CAAEoC,IAAK,QAASC,MAAOrC,EAAE,QAAS,UAClC,CAAEoC,IAAK,OAAQC,MAAOrC,EAAE,OAAQ,SAChC,CAAEoC,IAAK,UAAWC,MAAOrC,EAAE,UAAW,cAG1C,CACEoC,IAAK,iBACLY,MAAOhD,EAAE,sBAAuB,yBAChCsC,KAAM,mBACNC,MAAO,OACPyB,QAAS,CACP,CAAE5B,IAAK,cAAeC,MAAOrC,EAAE,cAAe,gBAC9C,CAAEoC,IAAK,MAAOC,MAAOrC,EAAE,MAAO,QAC9B,CAAEoC,IAAK,cAAeC,MAAOrC,EAAE,cAAe,kBAGlD,CACEoC,IAAK,aACLY,MAAOhD,EAAE,aAAc,eACvBsC,KAAM,oBACNC,MAAO,QACPyB,QAAS,CACP,CAAE5B,IAAK,OAAQC,MAAOrC,EAAE,OAAQ,SAChC,CAAEoC,IAAK,eAAgBC,MAAOrC,EAAE,eAAgB,iBAChD,CAAEoC,IAAK,MAAOC,MAAOrC,EAAE,MAAO,UAGlC,CACEoC,IAAK,qBACLY,MAAOhD,EAAE,qBAAsB,uBAC/BsC,KAAM,2BACNC,MAAO,SACPyB,QAAS,CACP,CAAE5B,IAAK,aAAcC,MAAOrC,EAAE,aAAc,eAC5C,CAAEoC,IAAK,UAAWC,MAAOrC,EAAE,UAAW,YACtC,CAAEoC,IAAK,oBAAqBC,MAAOrC,EAAE,oBAAqB,yBAG9D,CACEoC,IAAK,sBACLY,MAAOhD,EAAE,sBAAuB,wBAChCsC,KAAM,kBACNC,MAAO,SACPyB,QAAS,CACP,CAAE5B,IAAK,YAAaC,MAAOrC,EAAE,YAAa,cAC1C,CAAEoC,IAAK,iBAAkBC,MAAOrC,EAAE,iBAAkB,oBACpD,CAAEoC,IAAK,iBAAkBC,MAAOrC,EAAE,iBAAkB,oBACpD,CAAEoC,IAAK,oBAAqBC,MAAOrC,EAAE,oBAAqB,uBAC1D,CAAEoC,IAAK,cAAeC,MAAOrC,EAAE,cAAe,gBAC9C,CAAEoC,IAAK,MAAOC,MAAOrC,EAAE,MAAO,QAC9B,CAAEoC,IAAK,MAAOC,MAAOrC,EAAE,MAAO,UAGlC,CACEoC,IAAK,qBACLY,MAAOhD,EAAE,qBAAsB,uBAC/BsC,KAAM,oBACNC,MAAO,SACPyB,QAAS,CACP,CAAE5B,IAAK,sBAAuBC,MAAOrC,EAAE,sBAAuB,0BAC9D,CAAEoC,IAAK,aAAcC,MAAOrC,EAAE,aAAc,eAC5C,CAAEoC,IAAK,YAAaC,MAAOrC,EAAE,YAAa,iBAG9C,CACEoC,IAAK,iBACLY,MAAOhD,EAAE,6BAA8B,oCACvCsC,KAAM,uBACNC,MAAO,OACPyB,QAAS,CACP,CAAE5B,IAAK,aAAcC,MAAOrC,EAAE,aAAc,iBAC5C,CAAEoC,IAAK,eAAgBC,MAAOrC,EAAE,eAAgB,kBAChD,CAAEoC,IAAK,YAAaC,MAAOrC,EAAE,YAAa,iBAC1C,CAAEoC,IAAK,yBAA0BC,MAAOrC,EAAE,yBAA0B,4BACpE,CAAEoC,IAAK,oBAAqBC,MAAOrC,EAAE,oBAAqB,yBAG9D,CACEoC,IAAK,mBACLY,MAAOhD,EAAE,mBAAoB,qBAC7BsC,KAAM,mBACNC,MAAO,OACPyB,QAAS,CACP,CAAE5B,IAAK,YAAaC,MAAOrC,EAAE,YAAa,iBAC1C,CAAEoC,IAAK,UAAWC,MAAOrC,EAAE,UAAW,YACtC,CAAEoC,IAAK,YAAaC,MAAOrC,EAAE,YAAa,iBAK1CiE,EAAuB,CAC3B,CAAE1D,MAAO,MAAO8B,MAAOrC,EAAE,MAAO,8BAChC,CAAEO,MAAO,MAAO8B,MAAOrC,EAAE,MAAO,iCAChC,CAAEO,MAAO,KAAM8B,MAAOrC,EAAE,KAAM,yBA2BhC,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gEACZH,EAAE,gBAAiB,sBAGtBE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZH,EAAE,4BAA6B,mCAElCK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDJ,EAAE,2BAA4B,2KAInCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvB2D,EAAoB1C,IA3CM6C,IAC/BhE,EAAAA,EAAAA,MAAA,OAAwBC,UAAS,MAAAsB,OAAQyC,EAAS3B,MAAK,gBAAAd,OAAeyC,EAAS3B,MAAK,0BAAAd,OAAyByC,EAAS3B,MAAK,qBAAAd,OAAoByC,EAAS3B,MAAK,uBAAsBnC,SAAA,EACjLF,EAAAA,EAAAA,MAAA,MAAIC,UAAS,8BAAAsB,OAAgCyC,EAAS3B,MAAK,mBAAAd,OAAkByC,EAAS3B,MAAK,aAAYnC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAsB,OAAKyC,EAAS5B,KAAI,UAAAb,OAASyC,EAAS3B,MAAK,mBAAAd,OAAkByC,EAAS3B,MAAK,eACpF2B,EAASlB,UAEZ3C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE8D,EAASF,QAAQ3C,IAAK8C,IACrBjE,EAAAA,EAAAA,MAAA,SAAwBC,UAAU,oBAAmBC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLoD,QAAS9D,EAASwE,cAAcF,EAAS9B,KAAK+B,EAAO/B,KACrD3B,SAAWC,GAAMb,EAAkB,iBAAD4B,OAAkByC,EAAS9B,IAAG,KAAAX,OAAI0C,EAAO/B,KAAO1B,EAAEC,OAAO+C,SAC3FvD,UAAS,aAAAsB,OAAeyC,EAAS3B,MAAK,oBAAAd,OAAmByC,EAAS3B,MAAK,WAEzElC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,gBAAAsB,OAAkByC,EAAS3B,MAAK,mBAAAd,OAAkByC,EAAS3B,MAAK,QAAOnC,SACnF+D,EAAO9B,UARA8B,EAAO/B,UAPf8B,EAAS9B,OA6CflC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZH,EAAE,eAAgB,qBAGrBE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLoD,QAAS9D,EAASwE,cAAcC,aAAaC,kBAC7C7D,SAAWC,GAAMb,EAAkB,+CAAgDa,EAAEC,OAAO+C,SAC5FvD,UAAU,gDAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+CAA8CC,SAC3DJ,EAAE,oBAAqB,6BAK5BE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,yBAAwBC,SAAA,EACvCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLoD,QAAS9D,EAASwE,cAAcC,aAAaE,cAC7C9D,SAAWC,GAAMb,EAAkB,2CAA4Ca,EAAEC,OAAO+C,SACxFvD,UAAU,gDAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SACvEJ,EAAE,gBAAiB,uBAKvBJ,EAASwE,cAAcC,aAAaE,gBACnClE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,SAC5B6D,EAAqB5C,IAAK8C,IACzBjE,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEC,KAAK,QACLmD,KAAK,oBACLlD,MAAO4D,EAAO5D,MACdmD,QAAS9D,EAASwE,cAAcC,aAAaG,oBAAsBL,EAAO5D,MAC1EE,SAAWC,GAAMb,EAAkB,+CAAgDa,EAAEC,OAAOJ,OAC5FJ,UAAU,gDAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,+CAA8CC,SAC3D+D,EAAO9B,UAVA8B,EAAO5D,oBAqB/BF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gGAA+FC,UAC5GF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLoD,QAAS9D,EAASwE,cAAcK,iBAChChE,SAAWC,GAAMb,EAAkB,iCAAkCa,EAAEC,OAAO+C,SAC9EvD,UAAU,8CAEZD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,2DAA0DC,SAAA,EACxEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDACZH,EAAE,mBAAoB,yBAEzBK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDAAiDC,SAC3DJ,EAAE,8BAA+B,0FAO1CE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,EAChFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZH,EAAE,SAAU,cAEfK,EAAAA,EAAAA,KAAA,YACEE,MAAOX,EAASwE,cAAcM,OAC9BjE,SAAWC,GAAMb,EAAkB,uBAAwBa,EAAEC,OAAOJ,OACpEqB,KAAM,EACNzB,UAAU,kKACVS,YAAaZ,EAAE,oBAAqB,yEAKxCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kEACZH,EAAE,oBAAqB,0BAE1BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,kBAAmB,wBAExBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLsD,IAAI,IACJ9B,IAAI,IACJvB,MAAOX,EAAS+E,kBAAkBC,gBAClCnE,SAAWC,GAAMb,EAAkB,oCAAqCa,EAAEC,OAAOJ,OACjFJ,UAAU,4KACVS,YAAY,YAGhBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,kBAAmB,iCAExBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLsD,IAAI,KACJ9B,IAAI,MACJvB,MAAOX,EAAS+E,kBAAkBE,gBAClCpE,SAAWC,GAAMb,EAAkB,oCAAqCa,EAAEC,OAAOJ,OACjFJ,UAAU,4KACVS,YAAY,eAGhBV,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,aAAc,kBAEnBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,SACLsD,IAAI,IACJ9B,IAAI,KACJvB,MAAOX,EAAS+E,kBAAkBG,WAClCrE,SAAWC,GAAMb,EAAkB,+BAAgCa,EAAEC,OAAOJ,OAC5EJ,UAAU,4KACVS,YAAY,sBAQtBV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yGAAwGC,SAAA,EACrHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+DACZH,EAAE,0BAA2B,6BAA6B,QAE7DE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,CAC5EJ,EAAE,yBAA0B,2BAA2B,QAE1DE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,8BAA+B,sCACxCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,wBAAyB,gCAClCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,6BAA8B,oCACvCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,6BAA8B,2CAG3CE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,CAC5EJ,EAAE,wBAAyB,2BAA2B,QAEzDE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,yBAA0B,iCACnCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,8BAA+B,qCACxCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,uBAAwB,8BACjCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,kBAAmB,iDCrE1C,EAjQ0BX,IAA8C,IAA7C,SAAEO,EAAQ,kBAAEC,EAAiB,OAAEC,GAAQT,EAChE,MAAM,EAAEW,IAAMC,EAAAA,EAAAA,KAEd,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DACZH,EAAE,aAAc,mBAGnBE,EAAAA,EAAAA,MAAA,SAAOC,UAAU,mBAAkBC,SAAA,EACjCC,EAAAA,EAAAA,KAAA,SACEC,KAAK,WACLoD,QAAS9D,EAASmF,wBAClBtE,SAAWC,GAAMb,EAAkB,0BAA2Ba,EAAEC,OAAO+C,SACvEvD,UAAU,iDAEZD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SACnEJ,EAAE,gCAAiC,yCAEtCK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDJ,EAAE,wBAAyB,wHAKjCJ,EAASmF,0BACR7E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sGAAqGC,SAAA,EAClHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yDAAwDC,SACrEJ,EAAE,sBAAuB,mDAG9BK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDAAiDC,SAC3DJ,EAAE,iCAAkC,sHAO7CE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DACZH,EAAE,qBAAsB,2BAG3BE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EJ,EAAE,gBAAiB,kBAAkB,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAExEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASoF,mBAAmBvB,KACnChD,SAAWC,GAAMb,EAAkB,0BAA2Ba,EAAEC,OAAOJ,OACvEJ,UAAS,qJAAAsB,OACP3B,EAAOmF,cAAgB,iBAAmB,mBAE5CrE,YAAaZ,EAAE,qBAAsB,+BAEtCF,EAAOmF,gBACN5E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAOmF,oBAIrD/E,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EJ,EAAE,cAAe,aAAa,KAACK,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEjEC,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASoF,mBAAmBE,QACnCzE,SAAWC,GAAMb,EAAkB,6BAA8Ba,EAAEC,OAAOJ,OAC1EJ,UAAS,qJAAAsB,OACP3B,EAAOqF,eAAiB,iBAAmB,mBAE7CvE,YAAaZ,EAAE,mBAAoB,wBAEpCF,EAAOqF,iBACN9E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEN,EAAOqF,qBAIrDjF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EJ,EAAE,OAAQ,WAEbK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASoF,mBAAmBI,KACnC3E,SAAWC,GAAMb,EAAkB,0BAA2Ba,EAAEC,OAAOJ,OACvEJ,UAAU,6KAMhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EJ,EAAE,mBAAoB,wBAEzBE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2FAA0FC,SAAA,EACvGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAA4CC,SACtDJ,EAAE,gCAAiC,wDAEtCE,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLH,UAAU,yFAAwFC,SAAA,EAElGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZH,EAAE,wBAAyB,wCAOpCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kEAAiEC,SAAA,EAC7EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZH,EAAE,kBAAmB,wBAIxBE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,qBAAsB,0BAE3BK,EAAAA,EAAAA,KAAA,YACEE,MAAOX,EAASyF,gBAAgBC,UAChC7E,SAAWC,GAAMb,EAAkB,4BAA6Ba,EAAEC,OAAOJ,OACzEqB,KAAM,EACNzB,UAAU,4KACVS,YAAaZ,EAAE,gCAAiC,qFAElDK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDAAmDC,SAC7DJ,EAAE,yBAA0B,6EAIjCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,qBAAsB,0BAE3BK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASyF,gBAAgBE,UAChC9E,SAAWC,GAAMb,EAAkB,4BAA6Ba,EAAEC,OAAOJ,OACzEJ,UAAU,4KACVS,YAAaZ,EAAE,gCAAiC,6BAIpDE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,uBAAwB,0BAE7BK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASyF,gBAAgBH,QAChCzE,SAAWC,GAAMb,EAAkB,0BAA2Ba,EAAEC,OAAOJ,OACvEJ,UAAU,4KACVS,YAAaZ,EAAE,uBAAwB,sCAI3CE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,gBAAiB,qBAEtBK,EAAAA,EAAAA,KAAA,SACEC,KAAK,OACLC,MAAOX,EAASyF,gBAAgBD,KAChC3E,SAAWC,GAAMb,EAAkB,uBAAwBa,EAAEC,OAAOJ,OACpEJ,UAAU,qLAMhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnFJ,EAAE,4BAA6B,kCAElCE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6FAA4FC,SAAA,EACzGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SACxDJ,EAAE,gCAAiC,wDAEtCE,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLH,UAAU,2FAA0FC,SAAA,EAEpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZH,EAAE,wBAAyB,qCAMlCK,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0GAAyGC,UACtHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kFACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,6DAA4DC,SACvEJ,EAAE,0BAA2B,gCAEhCK,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDAAmDC,SAC7DJ,EAAE,6BAA8B,mLAQ3CE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6DACZH,EAAE,sBAAuB,wBAAwB,QAEpDE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,CACxEJ,EAAE,4BAA6B,8BAA8B,QAEhEE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,sBAAuB,sCAChCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,oBAAqB,uCAC9BE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,0BAA2B,iCACpCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,2BAA4B,yCAGzCE,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,CACxEJ,EAAE,4BAA6B,8BAA8B,QAEhEE,EAAAA,EAAAA,MAAA,MAAIC,UAAU,qDAAoDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,6BAA8B,oCACvCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,wBAAyB,uCAClCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,0BAA2B,iCACpCE,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGJ,EAAE,yBAA0B,yDCkTjD,EAliByBX,IAQlB,IARmB,UACxBmG,EAAS,aACTC,EAAY,YACZC,EAAW,mBACXC,EAAkB,YAClBC,EAAW,OACXC,EAAM,SACNC,GACDzG,EACC,MAAM,EAAEW,IAAMC,EAAAA,EAAAA,MACR,KAAE8F,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,OACTV,UAAWW,EAAcV,aAAcW,EAAS,eAAEC,IAAmBC,EAAAA,EAAAA,MACtEC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChC3G,EAAQ4G,IAAaD,EAAAA,EAAAA,UAAS,CAAC,IAC/BE,EAASC,IAAcH,EAAAA,EAAAA,UAAS,OAChC1G,EAAoB8G,IAAyBJ,EAAAA,EAAAA,UAAS,MAGvDK,EAAkBtB,GAAaW,EAC/BY,EAAetB,GAAgBW,GAG9BxG,EAAUoH,IAAeP,EAAAA,EAAAA,UAAS,CAEvCjG,eAAgB,MAChBK,WAAW,IAAIkB,MAAOC,cAAcC,MAAM,KAAK,GAC/CnB,QAAS,KACTC,aAAc,KAGdS,YAAa,GACbE,SAAU,GACVC,UAAW,GACXE,wBAAyB,GACzBK,UAAW,GAGXO,gBAAiB,CACfwE,eAAgB,GAChBC,cAAe,GACfC,gBAAiB,GACjBC,YAAa,GACbC,KAAM,GACNC,oBAAqB,GACrBC,YAAa,IAIftG,eAAgB,CACdmC,SAAU,GACVlC,MAAO,GACPyC,kBAAmB,GACnBG,aAAc,CAAC,GAAI,GAAI,GAAI,KAI7B3C,cAAe,CACbiC,SAAU,GACVlC,MAAO,GACPyC,kBAAmB,GACnBG,aAAc,CAAC,GAAI,GAAI,GAAI,KAI7BM,cAAe,CACboD,YAAa,CACXC,IAAI,EACJC,OAAO,EACPC,MAAM,EACNC,SAAS,GAEXC,eAAgB,CACdC,aAAa,EACbC,KAAK,EACLC,aAAa,GAEfC,WAAY,CACVC,MAAM,EACNC,cAAc,EACdC,KAAK,GAEPC,mBAAoB,CAClBC,YAAY,EACZV,SAAS,EACTW,mBAAmB,GAErBC,oBAAqB,CACnBC,WAAW,EACXC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,EACnBC,aAAa,EACbC,KAAK,EACLC,KAAK,GAEPC,mBAAoB,CAClBC,qBAAqB,EACrBC,YAAY,EACZC,WAAW,GAEbC,eAAgB,CACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,wBAAwB,EACxBC,mBAAmB,GAErBC,iBAAkB,CAChBC,WAAW,EACXC,SAAS,EACTC,WAAW,GAEbxF,aAAc,CACZC,mBAAmB,EACnBC,cAAe,GACfC,kBAAmB,IAErBC,kBAAkB,EAClBC,OAAQ,IAIVC,kBAAmB,CACjBC,gBAAiB,GACjBC,gBAAiB,GACjBC,WAAY,IAIdC,yBAAyB,EACzBC,mBAAoB,CAClBvB,MAAU,OAAJsC,QAAI,IAAJA,OAAI,EAAJA,EAAMtC,OAAQ,GACpByB,SAAa,OAAJa,QAAI,IAAJA,OAAI,EAAJA,EAAMb,UAAW,GAC1BE,MAAM,IAAIrD,MAAOC,cAAcC,MAAM,KAAK,IAE5CoD,gBAAiB,CACfC,UAAW,iFACXC,UAAW,GACXL,QAAS,GACTE,KAAM,OAKV0E,EAAAA,EAAAA,WAAU,KACJhD,IACFN,GAAW,GAEXuD,WAAW,KACT,MAAMC,EAAc,CAClBC,GAAInD,EACJrD,KAAM,uEACNyG,OAAQ,qBACRxI,SAAU,cACVyI,YAAa,aACbC,IAAK,EACLC,OAAQ,OACR1I,UAAW,kBAIP2I,EAAmB,CACvBL,GAAIlD,EACJvB,UAAWsB,EACXnF,UAAW,uCACXO,UAAW,kBACXlB,YAAa,aACbC,eAAgB,CACdC,MAAO,EACPE,MAAO,CACL,sEACA,wDACA,wDAGJD,cAAe,CACbD,MAAO,GACPE,MAAO,CACL,qEACA,uDACA,uDAGJgD,cAAe,CACboD,YAAa,CAAEC,IAAI,EAAME,MAAM,GAC/Ba,oBAAqB,CAAEG,gBAAgB,EAAMC,mBAAmB,GAChEQ,eAAgB,CAAEE,cAAc,EAAME,wBAAwB,KAIlE5C,EAAWoD,GACXnD,EAAsByD,GAGtBtD,EAAYuD,IAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAQ,IACX/I,YAAawI,EAAYE,OACzBxI,SAAUsI,EAAYtI,SACtBC,UAAW2I,EAAiB3I,UAC5BO,UAAWoI,EAAiBpI,UAC5BL,wBAAyByI,EAAiBtJ,YAC1CC,gBAAcuJ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTD,EAAStJ,gBAAc,IAC1BC,MAAOoJ,EAAiBrJ,eAAeC,QAEzCC,eAAaqJ,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRD,EAASpJ,eAAa,IACzBD,MAAOoJ,EAAiBnJ,cAAcD,QAExCkD,eAAaoG,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRD,EAASnG,eACTkG,EAAiBlG,kBAIxBoC,GAAW,IACV,OAEJ,CAACM,EAAiBC,IAErB,MAAMlH,EAAoBA,CAAC4K,EAAOlK,KAChC,MAAMmK,GAAOF,EAAAA,EAAAA,GAAA,GAAQ5K,GAGrB,GAAI6K,EAAME,SAAS,KAAM,CACvB,MAAMC,EAAQH,EAAMxI,MAAM,KAC1B,IAAI4I,EAAUH,EACd,IAAK,IAAII,EAAI,EAAGA,EAAIF,EAAMpH,OAAS,EAAGsH,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMpH,OAAS,IAAMjD,CACrC,MACEmK,EAAQD,GAASlK,EAGnByG,EAAY0D,GAGR5K,EAAO2K,IACT/D,EAAUqE,IAAIP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUO,GAAI,IAAE,CAACN,GAAQ,SA+I3C,OAAIlE,IAAYI,GAEZtG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAMnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,QAAM8K,SA9DWC,UAGnB,GAFAvK,EAAEwK,iBA/DiBC,MACnB,MAAMC,EAAY,CAAC,EAGdxL,EAAS4B,YAAY6J,SACxBD,EAAU5J,YAAcxB,EAAE,sBAAuB,6BAE9CJ,EAAS8B,SAAS2J,SACrBD,EAAU1J,SAAW1B,EAAE,mBAAoB,0BAExCJ,EAAS+B,UAAU0J,SACtBD,EAAUzJ,UAAY3B,EAAE,oBAAqB,0BAE1CJ,EAASiC,0BACZuJ,EAAUvJ,wBAA0B7B,EAAE,yBAA0B,2CAE7DJ,EAASsC,UAAUmJ,SACtBD,EAAUlJ,UAAYlC,EAAE,oBAAqB,0BAI1CJ,EAASqB,eAAemC,WAC3BgI,EAAUE,kBAAoBtL,EAAE,4BAA6B,mDAE1DJ,EAASuB,cAAciC,WAC1BgI,EAAUG,iBAAmBvL,EAAE,2BAA4B,kDAIpB,OAArCJ,EAASqB,eAAemC,UAAsBxD,EAASqB,eAAe0C,kBAAkB0H,SAC1FD,EAAUI,gBAAkBxL,EAAE,iBAAkB,mDAEV,OAApCJ,EAASuB,cAAciC,UAAsBxD,EAASuB,cAAcwC,kBAAkB0H,SACxFD,EAAUK,eAAiBzL,EAAE,iBAAkB,mDAIR,OAArCJ,EAASqB,eAAemC,WACHxD,EAASqB,eAAe6C,aAAa4H,KAAKpK,GAAQA,EAAK+J,UAE5ED,EAAUO,sBAAwB3L,EAAE,uBAAwB,2CAGxB,OAApCJ,EAASuB,cAAciC,WACFxD,EAASuB,cAAc2C,aAAa4H,KAAKpK,GAAQA,EAAK+J,UAE3ED,EAAUQ,qBAAuB5L,EAAE,uBAAwB,2CAa/D,OARKJ,EAASoF,mBAAmBvB,KAAK4H,SACpCD,EAAUnG,cAAgBjF,EAAE,wBAAyB,+BAElDJ,EAASoF,mBAAmBE,QAAQmG,SACvCD,EAAUjG,eAAiBnF,EAAE,yBAA0B,uCAGzD0G,EAAU0E,GAC+B,IAAlCS,OAAOC,KAAKV,GAAW5H,QAMzB2H,GAAL,CAKA3E,GAAW,GAEX,IAIE,SAFM,IAAIuF,QAAQC,GAAWjC,WAAWiC,EAAS,MAE7CnG,EACFA,EAAOjG,OACF,CAEL,MAAMqM,EAAqBC,KAAKC,MAAMC,aAAaC,QAAQ,qBAAuB,MAC5EC,GAAe9B,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAChB5K,GAAQ,IACXqK,GAAIlI,KAAKwK,MACT/G,UAAWsB,EACX0F,qBAAsBzF,EACtB0F,WAAW,IAAI1K,MAAOC,cACtB0K,WAAW,IAAI3K,MAAOC,cACtB2K,WAAe,OAAJ5G,QAAI,IAAJA,OAAI,EAAJA,EAAMkE,KAAM,iBAEzBgC,EAAmBW,KAAKN,GACxBF,aAAaS,QAAQ,mBAAoBX,KAAKY,UAAUb,IAExDc,EAAAA,GAAMC,QAAQhN,EAAE,oBAAqB,6DAGnCiG,EADEa,EACO,aAADrF,OAAcqF,GAEb,YAEb,CACF,CAAE,MAAOmG,GACPC,QAAQD,MAAM,6BAA8BA,GAC5CF,EAAAA,GAAME,MAAMjN,EAAE,0BAA2B,6BAC3C,CAAC,QACCwG,GAAW,EACb,CAtCA,MAFEuG,EAAAA,GAAME,MAAMjN,EAAE,kBAAmB,6CA0DHG,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7DJ,EAAE,+BAAgC,mDAClC8G,GAAmBH,IAClBzG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4DAA2DC,SAAA,CAAC,KACvEuG,EAAQuD,QAAUvD,EAAQlD,YAInCpD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDJ,EAAE,0BAA2B,4DAI/BD,IACCG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,EACvDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDACbD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAC/CJ,EAAE,eAAgB,iBAAiB,KAAGD,EAAmBiB,mBAG9Dd,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDACbD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAC/CJ,EAAE,YAAa,aAAa,KAAGD,EAAmBmC,oBAO3DhC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qFAAoFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAC,uBAEzEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yDAAwDC,SAAC,wBAE3EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SAAC,2BAKjFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,CAC5B0G,IACC5G,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLuD,QAASA,IAAMoC,EAAS,aAADxE,OAAcqF,IACrC3G,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZH,EAAE,cAAe,oBAGtBE,EAAAA,EAAAA,MAAA,UACEI,KAAK,SACLuD,QAhFUsJ,KAEtBJ,EAAAA,GAAMC,QAAQhN,EAAE,cAAe,+BA+EnBG,UAAU,oFAAmFC,SAAA,EAE7FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZH,EAAE,YAAa,kBAElBK,EAAAA,EAAAA,KAAA,UACEC,KAAK,SACLuD,QAASiC,GAAQ,KAAWG,EAASa,EAAe,aAAArF,OAAgBqF,GAAoB,cACxF3G,UAAU,uFAAsFC,SAE/FJ,EAAE,SAAU,qBAOrBK,EAAAA,EAAAA,KAAC+M,EAAsB,CACrBxN,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,EACRC,mBAAoBA,KAItBM,EAAAA,EAAAA,KAACgN,EAAsB,CACrBzN,SAAUA,EACVC,kBAAmBA,KAIrBQ,EAAAA,EAAAA,KAACiN,EAAsB,CACrB1N,SAAUA,EACVC,kBAAmBA,EACnB6C,kBAzPkBA,CAAC+H,EAAOlJ,EAAOhB,KACvC,MAAMmK,GAAOF,EAAAA,EAAAA,GAAA,GAAQ5K,GACfgL,EAAQH,EAAMxI,MAAM,KAC1B,IAAI4I,EAAUH,EAEd,IAAK,IAAII,EAAI,EAAGA,EAAIF,EAAMpH,OAAS,EAAGsH,IACpCD,EAAUA,EAAQD,EAAME,IAG1BD,EAAQD,EAAMA,EAAMpH,OAAS,IAAIjC,GAAShB,EAC1CyG,EAAY0D,IAgPN/H,QA7OSI,IACf,MAAM2H,GAAOF,EAAAA,EAAAA,GAAA,GAAQ5K,GACrB8K,EAAQ3H,GAAUe,aAAa8I,KAAK,IACpC5F,EAAY0D,IA2ON9H,WAxOWA,CAACG,EAAUxB,KAC5B,MAAMmJ,GAAOF,EAAAA,EAAAA,GAAA,GAAQ5K,GACrB8K,EAAQ3H,GAAUe,aAAayJ,OAAOhM,EAAO,GAC7CyF,EAAY0D,IAsON5K,OAAQA,EACRC,mBAAoBA,KAItBM,EAAAA,EAAAA,KAACmN,EAAoB,CACnB5N,SAAUA,EACVC,kBAAmBA,KAIrBQ,EAAAA,EAAAA,KAACoN,EAAiB,CAChB7N,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,KAIVI,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEC,KAAK,SACLuD,QAASiC,GAAQ,KAAWG,EAASa,EAAe,aAAArF,OAAgBqF,GAAoB,cACxF3G,UAAU,+FAA8FC,SAEvGJ,EAAE,SAAU,aAEfK,EAAAA,EAAAA,KAAA,UACEC,KAAK,SACLoN,SAAUnH,EACVpG,UAAU,kIAAiIC,SAE1ImG,GACCrG,EAAAA,EAAAA,MAAAyN,EAAAA,SAAA,CAAAvN,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZH,EAAE,SAAU,iBAGfE,EAAAA,EAAAA,MAAAyN,EAAAA,SAAA,CAAAvN,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZH,EAAE,mBAAoB,kC", "sources": ["components/Reassessment/DocumentPatientSection.jsx", "components/Reassessment/ProgressSummarySection.jsx", "components/Reassessment/GoalsAssessmentSection.jsx", "components/Reassessment/TreatmentPlanSection.jsx", "components/Reassessment/SignaturesSection.jsx", "components/Reassessment/ReassessmentForm.jsx"], "sourcesContent": ["import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst DocumentPatientSection = ({ formData, handleInputChange, errors, originalPlanOfCare }) => {\n  const { t } = useLanguage();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Document Information */}\n      <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          <i className=\"fas fa-file-alt text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('documentInformation', 'Document Information')}\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('documentNumber', 'Document Number')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.documentNumber}\n              onChange={(e) => handleInputChange('documentNumber', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"QP-\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('issueDate', 'Issue Date')}\n            </label>\n            <input\n              type=\"date\"\n              value={formData.issueDate}\n              onChange={(e) => handleInputChange('issueDate', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('version', 'Version')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.version}\n              onChange={(e) => handleInputChange('version', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"01\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('reviewNumber', 'Review Number')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.reviewNumber}\n              onChange={(e) => handleInputChange('reviewNumber', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder=\"01\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Patient Information */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          <i className=\"fas fa-user text-green-600 dark:text-green-400 mr-2\"></i>\n          {t('patientInformation', 'Patient Information')}\n        </h2>\n        \n        {/* Original Plan Reference */}\n        {originalPlanOfCare && (\n          <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6\">\n            <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-3\">\n              <i className=\"fas fa-history text-blue-600 dark:text-blue-400 mr-2\"></i>\n              {t('originalPlanReference', 'Original Plan of Care Reference')}\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n              <div>\n                <span className=\"font-medium text-blue-800 dark:text-blue-200\">\n                  {t('originalPlanDate', 'Original Plan Date')}:\n                </span>\n                <span className=\"ml-2 text-blue-700 dark:text-blue-300\">\n                  {originalPlanOfCare.createdDate}\n                </span>\n              </div>\n              <div>\n                <span className=\"font-medium text-blue-800 dark:text-blue-200\">\n                  {t('shortTermWeeks', 'Short Term Weeks')}:\n                </span>\n                <span className=\"ml-2 text-blue-700 dark:text-blue-300\">\n                  {originalPlanOfCare.shortTermGoals?.weeks || 'N/A'}\n                </span>\n              </div>\n              <div>\n                <span className=\"font-medium text-blue-800 dark:text-blue-200\">\n                  {t('longTermWeeks', 'Long Term Weeks')}:\n                </span>\n                <span className=\"ml-2 text-blue-700 dark:text-blue-300\">\n                  {originalPlanOfCare.longTermGoals?.weeks || 'N/A'}\n                </span>\n              </div>\n            </div>\n            \n            {/* Original Goals Summary */}\n            <div className=\"mt-4\">\n              <h4 className=\"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2\">\n                {t('originalGoals', 'Original Goals')}:\n              </h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <span className=\"text-xs font-medium text-blue-700 dark:text-blue-300\">\n                    {t('shortTermGoals', 'Short Term Goals')}:\n                  </span>\n                  <ul className=\"text-xs text-blue-600 dark:text-blue-400 mt-1 space-y-1\">\n                    {originalPlanOfCare.shortTermGoals?.goals?.map((goal, index) => (\n                      <li key={index} className=\"flex items-start\">\n                        <span className=\"mr-1\">•</span>\n                        <span>{goal}</span>\n                      </li>\n                    )) || <li>{t('noGoalsRecorded', 'No goals recorded')}</li>}\n                  </ul>\n                </div>\n                <div>\n                  <span className=\"text-xs font-medium text-blue-700 dark:text-blue-300\">\n                    {t('longTermGoals', 'Long Term Goals')}:\n                  </span>\n                  <ul className=\"text-xs text-blue-600 dark:text-blue-400 mt-1 space-y-1\">\n                    {originalPlanOfCare.longTermGoals?.goals?.map((goal, index) => (\n                      <li key={index} className=\"flex items-start\">\n                        <span className=\"mr-1\">•</span>\n                        <span>{goal}</span>\n                      </li>\n                    )) || <li>{t('noGoalsRecorded', 'No goals recorded')}</li>}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.patientName}\n              onChange={(e) => handleInputChange('patientName', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.patientName ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterPatientName', 'Enter patient name')}\n            />\n            {errors.patientName && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n            )}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('mrNumber', 'MR #')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.mrNumber}\n              onChange={(e) => handleInputChange('mrNumber', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.mrNumber ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterMRNumber', 'Enter MR number')}\n            />\n            {errors.mrNumber && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.mrNumber}</p>\n            )}\n          </div>\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('diagnosis', 'Diagnosis')} <span className=\"text-red-500\">*</span>\n            </label>\n            <textarea\n              value={formData.diagnosis}\n              onChange={(e) => handleInputChange('diagnosis', e.target.value)}\n              rows={3}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.diagnosis ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterDiagnosis', 'Enter diagnosis')}\n            />\n            {errors.diagnosis && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.diagnosis}</p>\n            )}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('lastRecertificationDate', 'Last Re-certification Date')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"date\"\n              value={formData.lastRecertificationDate}\n              onChange={(e) => handleInputChange('lastRecertificationDate', e.target.value)}\n              max={new Date().toISOString().split('T')[0]}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.lastRecertificationDate ? 'border-red-500' : 'border-gray-300'\n              }`}\n            />\n            {errors.lastRecertificationDate && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.lastRecertificationDate}</p>\n            )}\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('physician', 'Physician')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.physician}\n              onChange={(e) => handleInputChange('physician', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.physician ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterPhysicianName', 'Enter physician name')}\n            />\n            {errors.physician && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.physician}</p>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DocumentPatientSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst ProgressSummarySection = ({ formData, handleInputChange }) => {\n  const { t } = useLanguage();\n\n  const progressAreas = [\n    {\n      key: 'painAssessment',\n      label: t('painAssessment', 'Pain Assessment'),\n      icon: 'fas fa-exclamation-triangle',\n      color: 'red',\n      placeholder: t('painAssessmentPlaceholder', 'Document pain levels, location, triggers, and improvements since last assessment')\n    },\n    {\n      key: 'rangeOfMotion',\n      label: t('rangeOfMotion', 'Range of Motion'),\n      icon: 'fas fa-arrows-alt',\n      color: 'blue',\n      placeholder: t('romPlaceholder', 'Document ROM measurements, improvements, limitations, and functional impact')\n    },\n    {\n      key: 'muscleTonePower',\n      label: t('muscleTonePower', 'Muscle Tone/Power'),\n      icon: 'fas fa-dumbbell',\n      color: 'green',\n      placeholder: t('muscleTonePlaceholder', 'Document muscle strength grades, tone changes, and functional strength improvements')\n    },\n    {\n      key: 'balanceGait',\n      label: t('balanceGait', 'Balance/Gait'),\n      icon: 'fas fa-walking',\n      color: 'purple',\n      placeholder: t('balanceGaitPlaceholder', 'Document balance assessments, gait patterns, stability, and mobility improvements')\n    },\n    {\n      key: 'adls',\n      label: t('adls', 'ADL\\'s'),\n      icon: 'fas fa-home',\n      color: 'orange',\n      placeholder: t('adlsPlaceholder', 'Document activities of daily living, independence level, and functional improvements')\n    },\n    {\n      key: 'homeExerciseProgram',\n      label: t('homeExerciseProgram', 'Home Exercise Program'),\n      icon: 'fas fa-home',\n      color: 'teal',\n      placeholder: t('homeExercisePlaceholder', 'Document compliance, effectiveness, modifications, and patient/family understanding')\n    },\n    {\n      key: 'riskFactors',\n      label: t('riskFactors', 'Risk Factors'),\n      icon: 'fas fa-shield-alt',\n      color: 'yellow',\n      placeholder: t('riskFactorsPlaceholder', 'Document safety concerns, fall risk, environmental hazards, and risk mitigation strategies')\n    }\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-chart-line text-indigo-600 dark:text-indigo-400 mr-2\"></i>\n        {t('reassessmentSummaryProgress', 'Reassessment and Summary of Progress')}\n      </h2>\n      \n      <div className=\"mb-4 p-4 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg\">\n        <h3 className=\"text-md font-semibold text-indigo-900 dark:text-indigo-100 mb-2\">\n          <i className=\"fas fa-info-circle text-indigo-600 dark:text-indigo-400 mr-2\"></i>\n          {t('progressAchievedFromPreviousGoals', 'Progress achieved from previously stated goals')}\n        </h3>\n        <p className=\"text-sm text-indigo-700 dark:text-indigo-300\">\n          {t('progressSummaryInstructions', 'Document the patient\\'s progress in each area since the last assessment. Include specific measurements, functional improvements, and any setbacks or challenges encountered.')}\n        </p>\n      </div>\n\n      <div className=\"space-y-6\">\n        {progressAreas.map((area) => (\n          <div key={area.key} className={`bg-${area.color}-50 dark:bg-${area.color}-900/20 border border-${area.color}-200 dark:border-${area.color}-800 rounded-lg p-4`}>\n            <label className={`block text-sm font-medium text-${area.color}-800 dark:text-${area.color}-200 mb-2`}>\n              <i className={`${area.icon} text-${area.color}-600 dark:text-${area.color}-400 mr-2`}></i>\n              {area.label}\n            </label>\n            <textarea\n              value={formData.progressSummary[area.key]}\n              onChange={(e) => handleInputChange(`progressSummary.${area.key}`, e.target.value)}\n              rows={4}\n              className={`w-full px-3 py-2 border border-${area.color}-300 rounded-lg focus:ring-2 focus:ring-${area.color}-500 focus:border-${area.color}-500 dark:bg-${area.color}-800 dark:border-${area.color}-600 dark:text-white`}\n              placeholder={area.placeholder}\n            />\n            \n            {/* Progress indicators */}\n            <div className=\"mt-2 flex items-center space-x-4 text-xs\">\n              <div className={`flex items-center space-x-1 text-${area.color}-600 dark:text-${area.color}-400`}>\n                <i className=\"fas fa-lightbulb\"></i>\n                <span>{t('includeSpecificMeasurements', 'Include specific measurements')}</span>\n              </div>\n              <div className={`flex items-center space-x-1 text-${area.color}-600 dark:text-${area.color}-400`}>\n                <i className=\"fas fa-chart-bar\"></i>\n                <span>{t('documentFunctionalChanges', 'Document functional changes')}</span>\n              </div>\n              <div className={`flex items-center space-x-1 text-${area.color}-600 dark:text-${area.color}-400`}>\n                <i className=\"fas fa-calendar-alt\"></i>\n                <span>{t('compareToBaseline', 'Compare to baseline')}</span>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Progress Summary Guidelines */}\n      <div className=\"mt-6 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n        <h4 className=\"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3\">\n          <i className=\"fas fa-clipboard-list text-gray-600 dark:text-gray-400 mr-2\"></i>\n          {t('progressDocumentationGuidelines', 'Progress Documentation Guidelines')}:\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <h5 className=\"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n              {t('whatToInclude', 'What to Include')}:\n            </h5>\n            <ul className=\"text-xs text-gray-600 dark:text-gray-400 space-y-1\">\n              <li>• {t('objectiveMeasurements', 'Objective measurements (ROM, strength grades)')}</li>\n              <li>• {t('functionalImprovements', 'Functional improvements or declines')}</li>\n              <li>• {t('patientReportedOutcomes', 'Patient-reported outcomes')}</li>\n              <li>• {t('complianceWithTreatment', 'Compliance with treatment plan')}</li>\n              <li>• {t('barriersToPogress', 'Barriers to progress')}</li>\n            </ul>\n          </div>\n          <div>\n            <h5 className=\"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n              {t('documentationTips', 'Documentation Tips')}:\n            </h5>\n            <ul className=\"text-xs text-gray-600 dark:text-gray-400 space-y-1\">\n              <li>• {t('useSpecificNumbers', 'Use specific numbers and measurements')}</li>\n              <li>• {t('compareToInitialAssessment', 'Compare to initial assessment findings')}</li>\n              <li>• {t('noteAnyComplications', 'Note any complications or setbacks')}</li>\n              <li>• {t('documentPatientMotivation', 'Document patient motivation and participation')}</li>\n              <li>• {t('includeCaregiversObservations', 'Include caregiver\\'s observations')}</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Assessment Tools */}\n      <div className=\"mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n        <h4 className=\"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3\">\n          <i className=\"fas fa-tools text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('quickAssessmentTools', 'Quick Assessment Tools')}:\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div className=\"text-center\">\n            <div className=\"bg-blue-100 dark:bg-blue-800 rounded-lg p-3 mb-2\">\n              <i className=\"fas fa-thermometer-half text-blue-600 dark:text-blue-400 text-2xl\"></i>\n            </div>\n            <h5 className=\"text-xs font-semibold text-blue-800 dark:text-blue-200\">\n              {t('painScale', 'Pain Scale')}\n            </h5>\n            <p className=\"text-xs text-blue-600 dark:text-blue-400\">\n              {t('painScaleDescription', '0-10 Numeric Rating Scale')}\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"bg-blue-100 dark:bg-blue-800 rounded-lg p-3 mb-2\">\n              <i className=\"fas fa-balance-scale text-blue-600 dark:text-blue-400 text-2xl\"></i>\n            </div>\n            <h5 className=\"text-xs font-semibold text-blue-800 dark:text-blue-200\">\n              {t('bergBalanceScale', 'Berg Balance Scale')}\n            </h5>\n            <p className=\"text-xs text-blue-600 dark:text-blue-400\">\n              {t('bergBalanceDescription', '14-item balance assessment')}\n            </p>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"bg-blue-100 dark:bg-blue-800 rounded-lg p-3 mb-2\">\n              <i className=\"fas fa-ruler text-blue-600 dark:text-blue-400 text-2xl\"></i>\n            </div>\n            <h5 className=\"text-xs font-semibold text-blue-800 dark:text-blue-200\">\n              {t('goniometry', 'Goniometry')}\n            </h5>\n            <p className=\"text-xs text-blue-600 dark:text-blue-400\">\n              {t('goniometryDescription', 'ROM measurements in degrees')}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProgressSummarySection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst GoalsAssessmentSection = ({ formData, handleInputChange, handleArrayChange, addGoal, removeGoal, errors, originalPlanOfCare }) => {\n  const { t } = useLanguage();\n\n  const defaultReasonText = t('goalNotAchievedReason', 'The Goal was not achieved due to [insert reason], and The treatment plan will be adjusted to address the identified barriers, and the goals will be updated accordingly to better support the patient\\'s Progress.');\n\n  const renderGoalAssessment = (goalType, title, icon, color) => {\n    const goalData = formData[goalType];\n    const isAchieved = goalData.achieved === 'Yes';\n    const isNotAchieved = goalData.achieved === 'No';\n    const originalGoals = originalPlanOfCare?.[goalType]?.goals || [];\n    const errorPrefix = goalType === 'shortTermGoals' ? 'shortTerm' : 'longTerm';\n\n    return (\n      <div className={`bg-${color}-50 dark:bg-${color}-900/20 border border-${color}-200 dark:border-${color}-800 rounded-lg p-6`}>\n        <h3 className={`text-lg font-semibold text-${color}-900 dark:text-${color}-100 mb-4`}>\n          <i className={`${icon} text-${color}-600 dark:text-${color}-400 mr-2`}></i>\n          {title}\n        </h3>\n\n        {/* Original Goals Reference */}\n        {originalGoals.length > 0 && (\n          <div className=\"mb-6 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg\">\n            <h4 className=\"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2\">\n              <i className=\"fas fa-history text-gray-600 dark:text-gray-400 mr-1\"></i>\n              {t('originalGoalsReference', 'Original Goals Reference')}:\n            </h4>\n            <ul className=\"text-sm text-gray-700 dark:text-gray-300 space-y-1\">\n              {originalGoals.map((goal, index) => (\n                <li key={index} className=\"flex items-start\">\n                  <span className=\"mr-2 text-gray-500\">{index + 1}.</span>\n                  <span>{goal}</span>\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        {/* Achievement Status */}\n        <div className=\"mb-6\">\n          <label className={`block text-sm font-medium text-${color}-800 dark:text-${color}-200 mb-3`}>\n            {t('achievedGoals', `Achieved ${title}`)} <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"flex space-x-6\">\n            <label className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${\n              isAchieved\n                ? `border-green-500 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200`\n                : 'border-gray-300 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-500'\n            }`}>\n              <input\n                type=\"radio\"\n                name={`${goalType}_achieved`}\n                value=\"Yes\"\n                checked={isAchieved}\n                onChange={(e) => handleInputChange(`${goalType}.achieved`, e.target.value)}\n                className=\"sr-only\"\n              />\n              <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                isAchieved ? 'border-green-500 bg-green-500' : 'border-gray-300 dark:border-gray-600'\n              }`}>\n                {isAchieved && <div className=\"w-2 h-2 rounded-full bg-white\"></div>}\n              </div>\n              <span className=\"font-medium\">{t('yes', 'Yes')}</span>\n            </label>\n\n            <label className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ${\n              isNotAchieved\n                ? `border-red-500 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200`\n                : 'border-gray-300 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-500'\n            }`}>\n              <input\n                type=\"radio\"\n                name={`${goalType}_achieved`}\n                value=\"No\"\n                checked={isNotAchieved}\n                onChange={(e) => {\n                  handleInputChange(`${goalType}.achieved`, e.target.value);\n                  // Pre-fill reason text when \"No\" is selected\n                  if (e.target.value === 'No' && !goalData.reasonNotAchieved) {\n                    handleInputChange(`${goalType}.reasonNotAchieved`, defaultReasonText);\n                  }\n                }}\n                className=\"sr-only\"\n              />\n              <div className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${\n                isNotAchieved ? 'border-red-500 bg-red-500' : 'border-gray-300 dark:border-gray-600'\n              }`}>\n                {isNotAchieved && <div className=\"w-2 h-2 rounded-full bg-white\"></div>}\n              </div>\n              <span className=\"font-medium\">{t('no', 'No')}</span>\n            </label>\n          </div>\n          {errors[`${errorPrefix}Achieved`] && (\n            <p className=\"text-red-500 text-sm mt-2\">{errors[`${errorPrefix}Achieved`]}</p>\n          )}\n        </div>\n\n        {/* Weeks */}\n        <div className=\"mb-6\">\n          <label className={`block text-sm font-medium text-${color}-800 dark:text-${color}-200 mb-1`}>\n            {t('weeks', 'Weeks')}\n          </label>\n          <input\n            type=\"number\"\n            min=\"1\"\n            max=\"52\"\n            value={goalData.weeks}\n            onChange={(e) => handleInputChange(`${goalType}.weeks`, e.target.value)}\n            className={`w-32 px-3 py-2 border border-${color}-300 rounded-lg focus:ring-2 focus:ring-${color}-500 focus:border-${color}-500 dark:bg-${color}-800 dark:border-${color}-600 dark:text-white`}\n            placeholder=\"1-52\"\n          />\n          <p className={`text-xs text-${color}-600 dark:text-${color}-400 mt-1`}>\n            {t('weeksHint', 'Number of weeks for goal achievement')}\n          </p>\n        </div>\n\n        {/* Reason for Not Achieved (conditional) */}\n        {isNotAchieved && (\n          <div className=\"mb-6\">\n            <label className={`block text-sm font-medium text-${color}-800 dark:text-${color}-200 mb-1`}>\n              {t('reasonNotAchieved', 'Reason Not Achieved')} <span className=\"text-red-500\">*</span>\n            </label>\n            <textarea\n              value={goalData.reasonNotAchieved}\n              onChange={(e) => handleInputChange(`${goalType}.reasonNotAchieved`, e.target.value)}\n              rows={4}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-${color}-500 focus:border-${color}-500 dark:bg-${color}-800 dark:border-${color}-600 dark:text-white ${\n                errors[`${errorPrefix}Reason`] ? 'border-red-500' : `border-${color}-300`\n              }`}\n              placeholder={defaultReasonText}\n            />\n            {errors[`${errorPrefix}Reason`] && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors[`${errorPrefix}Reason`]}</p>\n            )}\n          </div>\n        )}\n\n        {/* Updated Goals (conditional) */}\n        {isNotAchieved && (\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <label className={`block text-sm font-medium text-${color}-800 dark:text-${color}-200`}>\n                {t('updatedGoals', 'Updated Goals')} <span className=\"text-red-500\">*</span>\n              </label>\n              <button\n                type=\"button\"\n                onClick={() => addGoal(goalType)}\n                className={`px-3 py-1 bg-${color}-600 text-white rounded-lg hover:bg-${color}-700 transition-colors text-sm`}\n              >\n                <i className=\"fas fa-plus mr-1\"></i>\n                {t('addGoal', 'Add Goal')}\n              </button>\n            </div>\n            \n            <div className=\"space-y-3\">\n              {goalData.updatedGoals.map((goal, index) => (\n                <div key={index} className=\"flex items-start space-x-3\">\n                  <span className={`mt-2 text-sm font-medium text-${color}-700 dark:text-${color}-300 min-w-[20px]`}>\n                    {index + 1}.\n                  </span>\n                  <textarea\n                    value={goal}\n                    onChange={(e) => handleArrayChange(`${goalType}.updatedGoals`, index, e.target.value)}\n                    rows={2}\n                    className={`flex-1 px-3 py-2 border border-${color}-300 rounded-lg focus:ring-2 focus:ring-${color}-500 focus:border-${color}-500 dark:bg-${color}-800 dark:border-${color}-600 dark:text-white`}\n                    placeholder={t('goalPlaceholder', 'Patient will...')}\n                  />\n                  {goalData.updatedGoals.length > 1 && (\n                    <button\n                      type=\"button\"\n                      onClick={() => removeGoal(goalType, index)}\n                      className=\"mt-2 p-1 text-red-600 hover:text-red-800 transition-colors\"\n                    >\n                      <i className=\"fas fa-trash text-sm\"></i>\n                    </button>\n                  )}\n                </div>\n              ))}\n            </div>\n            \n            {errors[`${errorPrefix}UpdatedGoals`] && (\n              <p className=\"text-red-500 text-sm mt-2\">{errors[`${errorPrefix}UpdatedGoals`]}</p>\n            )}\n            \n            <div className={`mt-3 p-3 bg-${color}-100 dark:bg-${color}-900/30 rounded-lg`}>\n              <h5 className={`text-xs font-semibold text-${color}-800 dark:text-${color}-200 mb-1`}>\n                <i className=\"fas fa-lightbulb mr-1\"></i>\n                {t('smartGoalsReminder', 'SMART Goals Reminder')}:\n              </h5>\n              <p className={`text-xs text-${color}-700 dark:text-${color}-300`}>\n                {t('smartGoalsDescription', 'Goals should be Specific, Measurable, Achievable, Relevant, and Time-bound')}\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* Success Message for Achieved Goals */}\n        {isAchieved && (\n          <div className=\"p-4 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg\">\n            <div className=\"flex items-center\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 mr-2\"></i>\n              <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                {t('goalsAchievedSuccess', 'Congratulations! Goals have been successfully achieved.')}\n              </span>\n            </div>\n            <p className=\"text-xs text-green-700 dark:text-green-300 mt-1\">\n              {t('goalsAchievedNext', 'Consider setting new goals or progressing to more advanced objectives.')}\n            </p>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-bullseye text-purple-600 dark:text-purple-400 mr-2\"></i>\n        {t('goalsOfTreatment', 'Goals of Treatment')}\n      </h2>\n      \n      <div className=\"space-y-8\">\n        {/* Short Term Goals */}\n        {renderGoalAssessment(\n          'shortTermGoals',\n          t('shortTermGoals', 'Short Term Goals'),\n          'fas fa-flag',\n          'blue'\n        )}\n\n        {/* Long Term Goals */}\n        {renderGoalAssessment(\n          'longTermGoals',\n          t('longTermGoals', 'Long Term Goals'),\n          'fas fa-mountain',\n          'green'\n        )}\n      </div>\n\n      {/* Goals Assessment Guidelines */}\n      <div className=\"mt-8 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n        <h4 className=\"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-3\">\n          <i className=\"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mr-2\"></i>\n          {t('goalsAssessmentGuidelines', 'Goals Assessment Guidelines')}:\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <h5 className=\"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2\">\n              {t('achievedGoalsNext', 'If Goals Are Achieved')}:\n            </h5>\n            <ul className=\"text-xs text-yellow-600 dark:text-yellow-400 space-y-1\">\n              <li>• {t('documentSpecificAchievements', 'Document specific achievements')}</li>\n              <li>• {t('considerAdvancedGoals', 'Consider setting more advanced goals')}</li>\n              <li>• {t('planMaintenanceStrategies', 'Plan maintenance strategies')}</li>\n              <li>• {t('prepareForDischarge', 'Prepare for discharge planning')}</li>\n            </ul>\n          </div>\n          <div>\n            <h5 className=\"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2\">\n              {t('notAchievedGoalsNext', 'If Goals Are Not Achieved')}:\n            </h5>\n            <ul className=\"text-xs text-yellow-600 dark:text-yellow-400 space-y-1\">\n              <li>• {t('identifySpecificBarriers', 'Identify specific barriers')}</li>\n              <li>• {t('modifyTreatmentApproach', 'Modify treatment approach')}</li>\n              <li>• {t('adjustGoalExpectations', 'Adjust goal expectations')}</li>\n              <li>• {t('increaseFrequencyIntensity', 'Consider frequency/intensity changes')}</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GoalsAssessmentSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst TreatmentPlanSection = ({ formData, handleInputChange }) => {\n  const { t } = useLanguage();\n\n  const treatmentCategories = [\n    {\n      key: 'painControl',\n      title: t('painControl', 'Pain Control'),\n      icon: 'fas fa-hand-holding-medical',\n      color: 'red',\n      options: [\n        { key: 'us', label: t('us', 'US') },\n        { key: 'laser', label: t('laser', 'LASER') },\n        { key: 'tens', label: t('tens', 'TENS') },\n        { key: 'thermal', label: t('thermal', 'Thermal') }\n      ]\n    },\n    {\n      key: 'reduceSwelling',\n      title: t('reduceSwellingEdema', 'Reduce Swelling/Edema'),\n      icon: 'fas fa-snowflake',\n      color: 'blue',\n      options: [\n        { key: 'cryotherapy', label: t('cryotherapy', 'Cryotherapy') },\n        { key: 'hvc', label: t('hvc', 'HVC') },\n        { key: 'compression', label: t('compression', 'Compression') }\n      ]\n    },\n    {\n      key: 'improveROM',\n      title: t('improveROM', 'Improve ROM'),\n      icon: 'fas fa-arrows-alt',\n      color: 'green',\n      options: [\n        { key: 'prom', label: t('prom', 'PROM') },\n        { key: 'mobilization', label: t('mobilization', 'Mobilization') },\n        { key: 'met', label: t('met', 'MET') }\n      ]\n    },\n    {\n      key: 'improveFlexibility',\n      title: t('improveFlexibility', 'Improve Flexibility'),\n      icon: 'fas fa-expand-arrows-alt',\n      color: 'purple',\n      options: [\n        { key: 'stretching', label: t('stretching', 'Stretching') },\n        { key: 'thermal', label: t('thermal', 'Thermal') },\n        { key: 'myofascialRelease', label: t('myofascialRelease', 'Myofascial release') }\n      ]\n    },\n    {\n      key: 'muscleStrengthening',\n      title: t('muscleStrengthening', 'Muscle Strengthening'),\n      icon: 'fas fa-dumbbell',\n      color: 'orange',\n      options: [\n        { key: 'isometric', label: t('isometric', 'Isometric') },\n        { key: 'activeAssisted', label: t('activeAssisted', 'Active Assisted') },\n        { key: 'activeResisted', label: t('activeResisted', 'Active Resisted') },\n        { key: 'coreStrengthening', label: t('coreStrengthening', 'Core Strengthening') },\n        { key: 'plyometrics', label: t('plyometrics', 'Plyometrics') },\n        { key: 'fes', label: t('fes', 'FES') },\n        { key: 'pnf', label: t('pnf', 'PNF') }\n      ]\n    },\n    {\n      key: 'posturalCorrection',\n      title: t('posturalCorrection', 'Postural Correction'),\n      icon: 'fas fa-user-check',\n      color: 'indigo',\n      options: [\n        { key: 'properBodyMechanics', label: t('properBodyMechanics', 'Proper Body Mechanics') },\n        { key: 'ergonomics', label: t('ergonomics', 'Ergonomics') },\n        { key: 'tiltTable', label: t('tiltTable', 'Tilt table') }\n      ]\n    },\n    {\n      key: 'improveBalance',\n      title: t('improveBalanceCoordination', 'Improve Balance and Coordination'),\n      icon: 'fas fa-balance-scale',\n      color: 'teal',\n      options: [\n        { key: 'frenkelsEx', label: t('frenkelsEx', 'Frenkel\\'s Ex') },\n        { key: 'balanceBoard', label: t('balanceBoard', 'Balance Board') },\n        { key: 'agilityEx', label: t('agilityEx', 'Agility Ex\\'s') },\n        { key: 'proprioceptionTraining', label: t('proprioceptionTraining', 'Proprioception Training') },\n        { key: 'lumbopelvicRhythm', label: t('lumbopelvicRhythm', 'Lumbopelvic Rhythm') }\n      ]\n    },\n    {\n      key: 'improveEndurance',\n      title: t('improveEndurance', 'Improve Endurance'),\n      icon: 'fas fa-heartbeat',\n      color: 'pink',\n      options: [\n        { key: 'aerobicEx', label: t('aerobicEx', 'Aerobic Ex\\'s') },\n        { key: 'bicycle', label: t('bicycle', 'Bicycle') },\n        { key: 'treadmill', label: t('treadmill', 'Treadmill') }\n      ]\n    }\n  ];\n\n  const weightBearingOptions = [\n    { value: 'FWB', label: t('fwb', 'FWB (Full Weight Bearing)') },\n    { value: 'PWB', label: t('pwb', 'PWB (Partial Weight Bearing)') },\n    { value: 'WB', label: t('wb', 'WB (Weight Bearing)') }\n  ];\n\n  const renderTreatmentCategory = (category) => (\n    <div key={category.key} className={`bg-${category.color}-50 dark:bg-${category.color}-900/20 border border-${category.color}-200 dark:border-${category.color}-800 rounded-lg p-4`}>\n      <h3 className={`text-md font-semibold text-${category.color}-900 dark:text-${category.color}-100 mb-3`}>\n        <i className={`${category.icon} text-${category.color}-600 dark:text-${category.color}-400 mr-2`}></i>\n        {category.title}\n      </h3>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2\">\n        {category.options.map((option) => (\n          <label key={option.key} className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={formData.treatmentPlan[category.key][option.key]}\n              onChange={(e) => handleInputChange(`treatmentPlan.${category.key}.${option.key}`, e.target.checked)}\n              className={`mr-2 text-${category.color}-600 focus:ring-${category.color}-500`}\n            />\n            <span className={`text-sm text-${category.color}-800 dark:text-${category.color}-200`}>\n              {option.label}\n            </span>\n          </label>\n        ))}\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-clipboard-list text-blue-600 dark:text-blue-400 mr-2\"></i>\n        {t('treatmentPlan', 'Treatment Plan')}\n      </h2>\n      \n      <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n        <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-2\">\n          <i className=\"fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('treatmentPlanInstructions', 'Treatment Plan Instructions')}\n        </h3>\n        <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n          {t('treatmentPlanDescription', 'Select all applicable treatment interventions. This updated plan should reflect the current needs based on the reassessment findings and goal achievement status.')}\n        </p>\n      </div>\n\n      <div className=\"space-y-6\">\n        {/* Treatment Categories */}\n        {treatmentCategories.map(renderTreatmentCategory)}\n\n        {/* Gait Training - Special Section */}\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <h3 className=\"text-md font-semibold text-yellow-900 dark:text-yellow-100 mb-3\">\n            <i className=\"fas fa-walking text-yellow-600 dark:text-yellow-400 mr-2\"></i>\n            {t('gaitTraining', 'Gait Training')}\n          </h3>\n          \n          <div className=\"space-y-4\">\n            {/* Normal Gait Pattern */}\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={formData.treatmentPlan.gaitTraining.normalGaitPattern}\n                onChange={(e) => handleInputChange('treatmentPlan.gaitTraining.normalGaitPattern', e.target.checked)}\n                className=\"mr-2 text-yellow-600 focus:ring-yellow-500\"\n              />\n              <span className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n                {t('normalGaitPattern', 'Normal Gait Pattern')}\n              </span>\n            </label>\n\n            {/* Weight Bearing */}\n            <div>\n              <label className=\"flex items-center mb-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={formData.treatmentPlan.gaitTraining.weightBearing}\n                  onChange={(e) => handleInputChange('treatmentPlan.gaitTraining.weightBearing', e.target.checked)}\n                  className=\"mr-2 text-yellow-600 focus:ring-yellow-500\"\n                />\n                <span className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n                  {t('weightBearing', 'Weight Bearing')}\n                </span>\n              </label>\n              \n              {/* Weight Bearing Type (conditional) */}\n              {formData.treatmentPlan.gaitTraining.weightBearing && (\n                <div className=\"ml-6 space-y-2\">\n                  {weightBearingOptions.map((option) => (\n                    <label key={option.value} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"weightBearingType\"\n                        value={option.value}\n                        checked={formData.treatmentPlan.gaitTraining.weightBearingType === option.value}\n                        onChange={(e) => handleInputChange('treatmentPlan.gaitTraining.weightBearingType', e.target.value)}\n                        className=\"mr-2 text-yellow-600 focus:ring-yellow-500\"\n                      />\n                      <span className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                        {option.label}\n                      </span>\n                    </label>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Home Instructions */}\n        <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={formData.treatmentPlan.homeInstructions}\n              onChange={(e) => handleInputChange('treatmentPlan.homeInstructions', e.target.checked)}\n              className=\"mr-3 text-green-600 focus:ring-green-500\"\n            />\n            <div>\n              <span className=\"text-md font-semibold text-green-900 dark:text-green-100\">\n                <i className=\"fas fa-home text-green-600 dark:text-green-400 mr-2\"></i>\n                {t('homeInstructions', 'Home Instructions')}\n              </span>\n              <p className=\"text-sm text-green-700 dark:text-green-300 mt-1\">\n                {t('homeInstructionsDescription', 'Provide patient and family with home exercise program and care instructions')}\n              </p>\n            </div>\n          </label>\n        </div>\n\n        {/* Others */}\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            <i className=\"fas fa-plus-circle text-gray-600 dark:text-gray-400 mr-2\"></i>\n            {t('others', 'Others')}\n          </label>\n          <textarea\n            value={formData.treatmentPlan.others}\n            onChange={(e) => handleInputChange('treatmentPlan.others', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white\"\n            placeholder={t('othersPlaceholder', 'Specify any additional treatment interventions not listed above')}\n          />\n        </div>\n\n        {/* Treatment Schedule */}\n        <div className=\"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4\">\n          <h3 className=\"text-md font-semibold text-indigo-900 dark:text-indigo-100 mb-4\">\n            <i className=\"fas fa-calendar-alt text-indigo-600 dark:text-indigo-400 mr-2\"></i>\n            {t('treatmentSchedule', 'Treatment Schedule')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-1\">\n                {t('sessionsPerWeek', 'Sessions per Week')}\n              </label>\n              <input\n                type=\"number\"\n                min=\"1\"\n                max=\"7\"\n                value={formData.treatmentSchedule.sessionsPerWeek}\n                onChange={(e) => handleInputChange('treatmentSchedule.sessionsPerWeek', e.target.value)}\n                className=\"w-full px-3 py-2 border border-indigo-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-indigo-800 dark:border-indigo-600 dark:text-white\"\n                placeholder=\"1-7\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-1\">\n                {t('sessionDuration', 'Session Duration (minutes)')}\n              </label>\n              <input\n                type=\"number\"\n                min=\"15\"\n                max=\"180\"\n                value={formData.treatmentSchedule.sessionDuration}\n                onChange={(e) => handleInputChange('treatmentSchedule.sessionDuration', e.target.value)}\n                className=\"w-full px-3 py-2 border border-indigo-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-indigo-800 dark:border-indigo-600 dark:text-white\"\n                placeholder=\"15-180\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-1\">\n                {t('totalWeeks', 'Total Weeks')}\n              </label>\n              <input\n                type=\"number\"\n                min=\"1\"\n                max=\"52\"\n                value={formData.treatmentSchedule.totalWeeks}\n                onChange={(e) => handleInputChange('treatmentSchedule.totalWeeks', e.target.value)}\n                className=\"w-full px-3 py-2 border border-indigo-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-indigo-800 dark:border-indigo-600 dark:text-white\"\n                placeholder=\"1-52\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Treatment Plan Guidelines */}\n      <div className=\"mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n        <h4 className=\"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-3\">\n          <i className=\"fas fa-lightbulb text-yellow-600 dark:text-yellow-400 mr-2\"></i>\n          {t('treatmentPlanGuidelines', 'Treatment Plan Guidelines')}:\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <h5 className=\"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2\">\n              {t('planningConsiderations', 'Planning Considerations')}:\n            </h5>\n            <ul className=\"text-xs text-yellow-600 dark:text-yellow-400 space-y-1\">\n              <li>• {t('basedOnReassessmentFindings', 'Based on reassessment findings')}</li>\n              <li>• {t('alignWithUpdatedGoals', 'Align with updated goals')}</li>\n              <li>• {t('considerPatientPreferences', 'Consider patient preferences')}</li>\n              <li>• {t('accountForProgressBarriers', 'Account for progress barriers')}</li>\n            </ul>\n          </div>\n          <div>\n            <h5 className=\"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2\">\n              {t('evidenceBasedPractice', 'Evidence-Based Practice')}:\n            </h5>\n            <ul className=\"text-xs text-yellow-600 dark:text-yellow-400 space-y-1\">\n              <li>• {t('useCurrentBestEvidence', 'Use current best evidence')}</li>\n              <li>• {t('integrateClinicianExpertise', 'Integrate clinician expertise')}</li>\n              <li>• {t('respectPatientValues', 'Respect patient values')}</li>\n              <li>• {t('monitorOutcomes', 'Monitor outcomes regularly')}</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TreatmentPlanSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SignaturesSection = ({ formData, handleInputChange, errors }) => {\n  const { t } = useLanguage();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Plan Review with Patient */}\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n          <i className=\"fas fa-handshake text-blue-600 dark:text-blue-400 mr-2\"></i>\n          {t('planReview', 'Plan Review')}\n        </h2>\n        \n        <label className=\"flex items-start\">\n          <input\n            type=\"checkbox\"\n            checked={formData.planReviewedWithPatient}\n            onChange={(e) => handleInputChange('planReviewedWithPatient', e.target.checked)}\n            className=\"mt-1 mr-3 text-blue-600 focus:ring-blue-500\"\n          />\n          <div>\n            <span className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">\n              {t('planOfCareReviewedWithPatient', 'Plan of Care Reviewed with Patient')}\n            </span>\n            <p className=\"text-xs text-blue-600 dark:text-blue-400 mt-1\">\n              {t('planReviewDescription', 'Confirm that the updated plan of care has been discussed and reviewed with the patient and/or family members')}\n            </p>\n          </div>\n        </label>\n        \n        {formData.planReviewedWithPatient && (\n          <div className=\"mt-4 p-3 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg\">\n            <div className=\"flex items-center\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 mr-2\"></i>\n              <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                {t('planReviewConfirmed', 'Plan review confirmed with patient/family')}\n              </span>\n            </div>\n            <p className=\"text-xs text-green-700 dark:text-green-300 mt-1\">\n              {t('planReviewConfirmedDescription', 'The patient and/or family have been informed of the updated treatment plan, goals, and expected outcomes.')}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Therapist Signature */}\n      <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          <i className=\"fas fa-user-md text-green-600 dark:text-green-400 mr-2\"></i>\n          {t('therapistSignature', 'Therapist Signature')}\n        </h2>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('therapistName', 'Therapist Name')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.therapistSignature.name}\n              onChange={(e) => handleInputChange('therapistSignature.name', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.therapistName ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterTherapistName', 'Enter therapist full name')}\n            />\n            {errors.therapistName && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.therapistName}</p>\n            )}\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('badgeNumber', 'Badge No.')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.therapistSignature.badgeNo}\n              onChange={(e) => handleInputChange('therapistSignature.badgeNo', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.therapistBadge ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterBadgeNumber', 'Enter badge number')}\n            />\n            {errors.therapistBadge && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.therapistBadge}</p>\n            )}\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('date', 'Date')}\n            </label>\n            <input\n              type=\"date\"\n              value={formData.therapistSignature.date}\n              onChange={(e) => handleInputChange('therapistSignature.date', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            />\n          </div>\n        </div>\n        \n        {/* Digital Signature Placeholder */}\n        <div className=\"mt-6\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('digitalSignature', 'Digital Signature')}\n          </label>\n          <div className=\"border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-8 text-center\">\n            <i className=\"fas fa-signature text-3xl text-green-400 mb-2\"></i>\n            <p className=\"text-sm text-green-600 dark:text-green-400\">\n              {t('therapistSignaturePlaceholder', 'Therapist digital signature will be captured here')}\n            </p>\n            <button\n              type=\"button\"\n              className=\"mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n            >\n              <i className=\"fas fa-pen mr-2\"></i>\n              {t('addTherapistSignature', 'Add Therapist Signature')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Physician Review and Signature */}\n      <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6\">\n        <h2 className=\"text-lg font-semibold text-purple-900 dark:text-purple-100 mb-4\">\n          <i className=\"fas fa-user-md text-purple-600 dark:text-purple-400 mr-2\"></i>\n          {t('physicianReview', 'Physician Review')}\n        </h2>\n        \n        {/* Physician Statement */}\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2\">\n            {t('physicianStatement', 'Physician Statement')}\n          </label>\n          <textarea\n            value={formData.physicianReview.statement}\n            onChange={(e) => handleInputChange('physicianReview.statement', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white\"\n            placeholder={t('physicianStatementPlaceholder', 'Have reviewed this plan of care and re-certify a continuing need for services.')}\n          />\n          <p className=\"text-xs text-purple-600 dark:text-purple-400 mt-1\">\n            {t('physicianStatementHint', 'Standard statement confirming physician review and re-certification')}\n          </p>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-1\">\n              {t('physicianSignature', 'Physician Signature')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.physicianReview.signature}\n              onChange={(e) => handleInputChange('physicianReview.signature', e.target.value)}\n              className=\"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white\"\n              placeholder={t('physicianSignaturePlaceholder', 'Physician signature')}\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-1\">\n              {t('physicianBadgeNumber', 'Physician Badge No.')}\n            </label>\n            <input\n              type=\"text\"\n              value={formData.physicianReview.badgeNo}\n              onChange={(e) => handleInputChange('physicianReview.badgeNo', e.target.value)}\n              className=\"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white\"\n              placeholder={t('physicianBadgeNumber', 'Enter physician badge number')}\n            />\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-1\">\n              {t('physicianDate', 'Physician Date')}\n            </label>\n            <input\n              type=\"date\"\n              value={formData.physicianReview.date}\n              onChange={(e) => handleInputChange('physicianReview.date', e.target.value)}\n              className=\"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white\"\n            />\n          </div>\n        </div>\n        \n        {/* Physician Digital Signature Placeholder */}\n        <div className=\"mt-6\">\n          <label className=\"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2\">\n            {t('physicianDigitalSignature', 'Physician Digital Signature')}\n          </label>\n          <div className=\"border-2 border-dashed border-purple-300 dark:border-purple-600 rounded-lg p-8 text-center\">\n            <i className=\"fas fa-signature text-3xl text-purple-400 mb-2\"></i>\n            <p className=\"text-sm text-purple-600 dark:text-purple-400\">\n              {t('physicianSignaturePlaceholder', 'Physician digital signature will be captured here')}\n            </p>\n            <button\n              type=\"button\"\n              className=\"mt-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n            >\n              <i className=\"fas fa-pen mr-2\"></i>\n              {t('addPhysicianSignature', 'Add Physician Signature')}\n            </button>\n          </div>\n        </div>\n        \n        {/* Re-certification Notice */}\n        <div className=\"mt-6 p-4 bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 rounded-lg\">\n          <div className=\"flex items-start\">\n            <i className=\"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5\"></i>\n            <div>\n              <h4 className=\"text-sm font-semibold text-yellow-800 dark:text-yellow-200\">\n                {t('recertificationRequired', 'Re-certification Required')}\n              </h4>\n              <p className=\"text-xs text-yellow-700 dark:text-yellow-300 mt-1\">\n                {t('recertificationDescription', 'Physician signature is required to re-certify the continuing need for physical therapy services. This confirms medical necessity and supports insurance coverage.')}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Signature Guidelines */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n        <h4 className=\"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3\">\n          <i className=\"fas fa-info-circle text-gray-600 dark:text-gray-400 mr-2\"></i>\n          {t('signatureGuidelines', 'Signature Guidelines')}:\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <h5 className=\"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n              {t('therapistResponsibilities', 'Therapist Responsibilities')}:\n            </h5>\n            <ul className=\"text-xs text-gray-600 dark:text-gray-400 space-y-1\">\n              <li>• {t('completeAllSections', 'Complete all required sections')}</li>\n              <li>• {t('reviewWithPatient', 'Review plan with patient/family')}</li>\n              <li>• {t('provideDigitalSignature', 'Provide digital signature')}</li>\n              <li>• {t('submitForPhysicianReview', 'Submit for physician review')}</li>\n            </ul>\n          </div>\n          <div>\n            <h5 className=\"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2\">\n              {t('physicianResponsibilities', 'Physician Responsibilities')}:\n            </h5>\n            <ul className=\"text-xs text-gray-600 dark:text-gray-400 space-y-1\">\n              <li>• {t('reviewReassessmentFindings', 'Review reassessment findings')}</li>\n              <li>• {t('validateTreatmentPlan', 'Validate updated treatment plan')}</li>\n              <li>• {t('confirmMedicalNecessity', 'Confirm medical necessity')}</li>\n              <li>• {t('provideRecertification', 'Provide re-certification signature')}</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SignaturesSection;\n", "import React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport DocumentPatientSection from './DocumentPatientSection';\nimport ProgressSummarySection from './ProgressSummarySection';\nimport GoalsAssessmentSection from './GoalsAssessmentSection';\nimport TreatmentPlanSection from './TreatmentPlanSection';\nimport SignaturesSection from './SignaturesSection';\n\nconst ReassessmentForm = ({\n  patientId,\n  planOfCareId,\n  patientData,\n  fromPatientProfile,\n  initialData,\n  onSave,\n  onCancel\n}) => {\n  const { t } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const { patientId: urlPatientId, planOfCareId: urlPlanId, reassessmentId } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [patient, setPatient] = useState(null);\n  const [originalPlanOfCare, setOriginalPlanOfCare] = useState(null);\n\n  // Use patientId and planOfCareId from props or URL params\n  const activePatientId = patientId || urlPatientId;\n  const activePlanId = planOfCareId || urlPlanId;\n\n  // Form data structure\n  const [formData, setFormData] = useState({\n    // Document Information\n    documentNumber: 'QP-',\n    issueDate: new Date().toISOString().split('T')[0],\n    version: '01',\n    reviewNumber: '01',\n    \n    // Patient Information\n    patientName: '',\n    mrNumber: '',\n    diagnosis: '',\n    lastRecertificationDate: '',\n    physician: '',\n    \n    // Progress Summary\n    progressSummary: {\n      painAssessment: '',\n      rangeOfMotion: '',\n      muscleTonePower: '',\n      balanceGait: '',\n      adls: '',\n      homeExerciseProgram: '',\n      riskFactors: ''\n    },\n    \n    // Short Term Goals Assessment\n    shortTermGoals: {\n      achieved: '', // Yes/No\n      weeks: '',\n      reasonNotAchieved: '',\n      updatedGoals: ['', '', '', '']\n    },\n    \n    // Long Term Goals Assessment\n    longTermGoals: {\n      achieved: '', // Yes/No\n      weeks: '',\n      reasonNotAchieved: '',\n      updatedGoals: ['', '', '', '']\n    },\n    \n    // Treatment Plan (same structure as original plan of care)\n    treatmentPlan: {\n      painControl: {\n        us: false,\n        laser: false,\n        tens: false,\n        thermal: false\n      },\n      reduceSwelling: {\n        cryotherapy: false,\n        hvc: false,\n        compression: false\n      },\n      improveROM: {\n        prom: false,\n        mobilization: false,\n        met: false\n      },\n      improveFlexibility: {\n        stretching: false,\n        thermal: false,\n        myofascialRelease: false\n      },\n      muscleStrengthening: {\n        isometric: false,\n        activeAssisted: false,\n        activeResisted: false,\n        coreStrengthening: false,\n        plyometrics: false,\n        fes: false,\n        pnf: false\n      },\n      posturalCorrection: {\n        properBodyMechanics: false,\n        ergonomics: false,\n        tiltTable: false\n      },\n      improveBalance: {\n        frenkelsEx: false,\n        balanceBoard: false,\n        agilityEx: false,\n        proprioceptionTraining: false,\n        lumbopelvicRhythm: false\n      },\n      improveEndurance: {\n        aerobicEx: false,\n        bicycle: false,\n        treadmill: false\n      },\n      gaitTraining: {\n        normalGaitPattern: false,\n        weightBearing: '',\n        weightBearingType: '' // FWB, PWB, WB\n      },\n      homeInstructions: false,\n      others: ''\n    },\n    \n    // Treatment Schedule\n    treatmentSchedule: {\n      sessionsPerWeek: '',\n      sessionDuration: '',\n      totalWeeks: ''\n    },\n    \n    // Plan Review and Signatures\n    planReviewedWithPatient: false,\n    therapistSignature: {\n      name: user?.name || '',\n      badgeNo: user?.badgeNo || '',\n      date: new Date().toISOString().split('T')[0]\n    },\n    physicianReview: {\n      statement: 'Have reviewed this plan of care and re-certify a continuing need for services.',\n      signature: '',\n      badgeNo: '',\n      date: ''\n    }\n  });\n\n  // Load patient and original plan of care data\n  useEffect(() => {\n    if (activePatientId) {\n      setLoading(true);\n      // Mock data loading - in real implementation, fetch from API\n      setTimeout(() => {\n        const mockPatient = {\n          id: activePatientId,\n          name: 'أحمد محمد علي',\n          nameEn: 'Ahmed Mohammed Ali',\n          mrNumber: 'MR-2024-001',\n          dateOfBirth: '2016-03-15',\n          age: 8,\n          gender: 'male',\n          diagnosis: 'Cerebral Palsy'\n        };\n        \n        // Mock original plan of care data\n        const mockOriginalPlan = {\n          id: activePlanId,\n          patientId: activePatientId,\n          diagnosis: 'Cerebral Palsy with spastic diplegia',\n          physician: 'Dr. Sarah Ahmed',\n          createdDate: '2024-01-15',\n          shortTermGoals: {\n            weeks: 4,\n            goals: [\n              'Patient will improve balance by standing on one foot for 10 seconds',\n              'Patient will increase lower extremity strength by 25%',\n              'Patient will demonstrate proper transfer techniques'\n            ]\n          },\n          longTermGoals: {\n            weeks: 12,\n            goals: [\n              'Patient will walk 50 meters independently without assistive device',\n              'Patient will achieve functional independence in ADLs',\n              'Patient will demonstrate improved postural control'\n            ]\n          },\n          treatmentPlan: {\n            painControl: { us: true, tens: true },\n            muscleStrengthening: { activeResisted: true, coreStrengthening: true },\n            improveBalance: { balanceBoard: true, proprioceptionTraining: true }\n          }\n        };\n        \n        setPatient(mockPatient);\n        setOriginalPlanOfCare(mockOriginalPlan);\n        \n        // Pre-populate form with patient and plan data\n        setFormData(prevData => ({\n          ...prevData,\n          patientName: mockPatient.nameEn,\n          mrNumber: mockPatient.mrNumber,\n          diagnosis: mockOriginalPlan.diagnosis,\n          physician: mockOriginalPlan.physician,\n          lastRecertificationDate: mockOriginalPlan.createdDate,\n          shortTermGoals: {\n            ...prevData.shortTermGoals,\n            weeks: mockOriginalPlan.shortTermGoals.weeks\n          },\n          longTermGoals: {\n            ...prevData.longTermGoals,\n            weeks: mockOriginalPlan.longTermGoals.weeks\n          },\n          treatmentPlan: {\n            ...prevData.treatmentPlan,\n            ...mockOriginalPlan.treatmentPlan\n          }\n        }));\n        \n        setLoading(false);\n      }, 500);\n    }\n  }, [activePatientId, activePlanId]);\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData };\n    \n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    setFormData(newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleArrayChange = (field, index, value) => {\n    const newData = { ...formData };\n    const parts = field.split('.');\n    let current = newData;\n    \n    for (let i = 0; i < parts.length - 1; i++) {\n      current = current[parts[i]];\n    }\n    \n    current[parts[parts.length - 1]][index] = value;\n    setFormData(newData);\n  };\n\n  const addGoal = (goalType) => {\n    const newData = { ...formData };\n    newData[goalType].updatedGoals.push('');\n    setFormData(newData);\n  };\n\n  const removeGoal = (goalType, index) => {\n    const newData = { ...formData };\n    newData[goalType].updatedGoals.splice(index, 1);\n    setFormData(newData);\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n    if (!formData.mrNumber.trim()) {\n      newErrors.mrNumber = t('mrNumberRequired', 'MR number is required');\n    }\n    if (!formData.diagnosis.trim()) {\n      newErrors.diagnosis = t('diagnosisRequired', 'Diagnosis is required');\n    }\n    if (!formData.lastRecertificationDate) {\n      newErrors.lastRecertificationDate = t('lastRecertDateRequired', 'Last re-certification date is required');\n    }\n    if (!formData.physician.trim()) {\n      newErrors.physician = t('physicianRequired', 'Physician is required');\n    }\n\n    // Goals validation\n    if (!formData.shortTermGoals.achieved) {\n      newErrors.shortTermAchieved = t('shortTermAchievedRequired', 'Short term goal achievement status is required');\n    }\n    if (!formData.longTermGoals.achieved) {\n      newErrors.longTermAchieved = t('longTermAchievedRequired', 'Long term goal achievement status is required');\n    }\n\n    // Conditional validation for \"No\" responses\n    if (formData.shortTermGoals.achieved === 'No' && !formData.shortTermGoals.reasonNotAchieved.trim()) {\n      newErrors.shortTermReason = t('reasonRequired', 'Reason is required when goals are not achieved');\n    }\n    if (formData.longTermGoals.achieved === 'No' && !formData.longTermGoals.reasonNotAchieved.trim()) {\n      newErrors.longTermReason = t('reasonRequired', 'Reason is required when goals are not achieved');\n    }\n\n    // At least one updated goal required if not achieved\n    if (formData.shortTermGoals.achieved === 'No') {\n      const hasUpdatedGoal = formData.shortTermGoals.updatedGoals.some(goal => goal.trim());\n      if (!hasUpdatedGoal) {\n        newErrors.shortTermUpdatedGoals = t('updatedGoalsRequired', 'At least one updated goal is required');\n      }\n    }\n    if (formData.longTermGoals.achieved === 'No') {\n      const hasUpdatedGoal = formData.longTermGoals.updatedGoals.some(goal => goal.trim());\n      if (!hasUpdatedGoal) {\n        newErrors.longTermUpdatedGoals = t('updatedGoalsRequired', 'At least one updated goal is required');\n      }\n    }\n\n    // Therapist signature validation\n    if (!formData.therapistSignature.name.trim()) {\n      newErrors.therapistName = t('therapistNameRequired', 'Therapist name is required');\n    }\n    if (!formData.therapistSignature.badgeNo.trim()) {\n      newErrors.therapistBadge = t('therapistBadgeRequired', 'Therapist badge number is required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    \n    try {\n      // Mock API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      if (onSave) {\n        onSave(formData);\n      } else {\n        // Save to localStorage for demo\n        const savedReassessments = JSON.parse(localStorage.getItem('reassessmentData') || '[]');\n        const newReassessment = {\n          ...formData,\n          id: Date.now(),\n          patientId: activePatientId,\n          originalPlanOfCareId: activePlanId,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n          createdBy: user?.id || 'current-user'\n        };\n        savedReassessments.push(newReassessment);\n        localStorage.setItem('reassessmentData', JSON.stringify(savedReassessments));\n        \n        toast.success(t('reassessmentSaved', 'Reassessment and Updated Plan of Care saved successfully'));\n        \n        if (activePatientId) {\n          navigate(`/patients/${activePatientId}`);\n        } else {\n          navigate('/patients');\n        }\n      }\n    } catch (error) {\n      console.error('Error saving reassessment:', error);\n      toast.error(t('errorSavingReassessment', 'Error saving reassessment'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleExportPDF = () => {\n    // Mock PDF export - implement with jsPDF or similar\n    toast.success(t('pdfExported', 'PDF exported successfully'));\n  };\n\n  if (loading && !patient) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900\">\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Header */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n          <div className=\"flex items-start justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('reassessmentUpdatePlanOfCare', 'Reassessment and Update Plan of Care for Doctor')}\n                {activePatientId && patient && (\n                  <span className=\"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3\">\n                    - {patient.nameEn || patient.name}\n                  </span>\n                )}\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                {t('reassessmentDescription', 'Comprehensive 2-page reassessment and plan update form')}\n              </p>\n              \n              {/* Original Plan Reference */}\n              {originalPlanOfCare && (\n                <div className=\"flex items-center space-x-4 mt-3 text-sm\">\n                  <div className=\"flex items-center space-x-2\">\n                    <i className=\"fas fa-link text-blue-600 dark:text-blue-400\"></i>\n                    <span className=\"text-gray-700 dark:text-gray-300\">\n                      {t('originalPlan', 'Original Plan')}: {originalPlanOfCare.createdDate}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <i className=\"fas fa-user-md text-green-600 dark:text-green-400\"></i>\n                    <span className=\"text-gray-700 dark:text-gray-300\">\n                      {t('physician', 'Physician')}: {originalPlanOfCare.physician}\n                    </span>\n                  </div>\n                </div>\n              )}\n              \n              {/* Compliance Badges */}\n              <div className=\"flex flex-wrap gap-2 mt-3\">\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n                  <i className=\"fas fa-certificate text-blue-600 dark:text-blue-400\"></i>\n                  <span className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">CARF Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full\">\n                  <i className=\"fas fa-shield-alt text-green-600 dark:text-green-400\"></i>\n                  <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">CBAHI Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n                  <i className=\"fas fa-lock text-purple-600 dark:text-purple-400\"></i>\n                  <span className=\"text-sm font-medium text-purple-800 dark:text-purple-200\">HIPAA Secure</span>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"flex space-x-3\">\n              {activePatientId && (\n                <button\n                  type=\"button\"\n                  onClick={() => navigate(`/patients/${activePatientId}`)}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <i className=\"fas fa-user mr-2\"></i>\n                  {t('viewPatient', 'View Patient')}\n                </button>\n              )}\n              <button\n                type=\"button\"\n                onClick={handleExportPDF}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {t('exportPDF', 'Export PDF')}\n              </button>\n              <button\n                type=\"button\"\n                onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Document Information and Patient Information */}\n        <DocumentPatientSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n          originalPlanOfCare={originalPlanOfCare}\n        />\n\n        {/* Progress Summary */}\n        <ProgressSummarySection\n          formData={formData}\n          handleInputChange={handleInputChange}\n        />\n\n        {/* Goals Assessment */}\n        <GoalsAssessmentSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          handleArrayChange={handleArrayChange}\n          addGoal={addGoal}\n          removeGoal={removeGoal}\n          errors={errors}\n          originalPlanOfCare={originalPlanOfCare}\n        />\n\n        {/* Treatment Plan */}\n        <TreatmentPlanSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n        />\n\n        {/* Signatures */}\n        <SignaturesSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n        />\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <button\n            type=\"button\"\n            onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                {t('saving', 'Saving...')}\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveReassessment', 'Save Reassessment')}\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default ReassessmentForm;\n"], "names": ["_ref", "_originalPlanOfCare$s", "_originalPlanOfCare$l", "_originalPlanOfCare$s2", "_originalPlanOfCare$s3", "_originalPlanOfCare$l2", "_originalPlanOfCare$l3", "formData", "handleInputChange", "errors", "originalPlanOfCare", "t", "useLanguage", "_jsxs", "className", "children", "_jsx", "type", "value", "documentNumber", "onChange", "e", "target", "placeholder", "issueDate", "version", "reviewNumber", "createdDate", "shortTermGoals", "weeks", "longTermGoals", "goals", "map", "goal", "index", "patientName", "concat", "mr<PERSON><PERSON><PERSON>", "diagnosis", "rows", "lastRecertificationDate", "max", "Date", "toISOString", "split", "physician", "progressAreas", "key", "label", "icon", "color", "area", "progressSummary", "handleArrayChange", "addGoal", "removeGoal", "defaultReasonText", "renderGoalAssessment", "goalType", "title", "_originalPlanOfCare$g", "goalData", "isAchieved", "achieved", "isNotAchieved", "originalGoals", "errorPrefix", "length", "name", "checked", "reasonNotAchieved", "min", "onClick", "updatedGoals", "treatmentCategories", "options", "weightBearingOptions", "category", "option", "treatmentPlan", "gaitTraining", "normalGaitPattern", "weightBearing", "weightBearingType", "homeInstructions", "others", "treatmentSchedule", "sessionsPerWeek", "sessionDuration", "totalWeeks", "planReviewedWithPatient", "therapistSignature", "<PERSON><PERSON><PERSON>", "badgeNo", "therapistBadge", "date", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statement", "signature", "patientId", "planOfCareId", "patientData", "fromPatientProfile", "initialData", "onSave", "onCancel", "user", "useAuth", "navigate", "useNavigate", "urlPatientId", "urlPlanId", "reassessmentId", "useParams", "loading", "setLoading", "useState", "setErrors", "patient", "setPatient", "setOriginalPlanOfCare", "activePatientId", "activePlanId", "setFormData", "painAssessment", "rangeOfMotion", "muscleTonePower", "balanceGait", "adls", "homeExerciseProgram", "riskFactors", "painControl", "us", "laser", "tens", "thermal", "reduceSwelling", "cryotherapy", "hvc", "compression", "improveROM", "prom", "mobilization", "met", "improveFlexibility", "stretching", "myofascialRelease", "muscleStrengthening", "isometric", "activeAssisted", "activeResisted", "coreStrengthening", "plyometrics", "fes", "pnf", "posturalCorrection", "properBodyMechanics", "ergonomics", "tiltTable", "improveBalance", "frenkelsEx", "balanceBoard", "agilityEx", "proprioceptionTraining", "lumbopelvicRhythm", "improveEndurance", "aerobicEx", "bicycle", "treadmill", "useEffect", "setTimeout", "mockPatient", "id", "nameEn", "dateOfBirth", "age", "gender", "mockOriginalPlan", "prevData", "_objectSpread", "field", "newData", "includes", "parts", "current", "i", "prev", "onSubmit", "async", "preventDefault", "validateForm", "newErrors", "trim", "shortTermAchieved", "longTermAchieved", "shortTermReason", "longTermReason", "some", "shortTermUpdatedGoals", "longTermUpdatedGoals", "Object", "keys", "Promise", "resolve", "savedReassessments", "JSON", "parse", "localStorage", "getItem", "newReassessment", "now", "originalPlanOfCareId", "createdAt", "updatedAt", "created<PERSON>y", "push", "setItem", "stringify", "toast", "success", "error", "console", "handleExportPDF", "DocumentPatientSection", "ProgressSummarySection", "GoalsAssessmentSection", "splice", "TreatmentPlanSection", "SignaturesSection", "disabled", "_Fragment"], "sourceRoot": ""}