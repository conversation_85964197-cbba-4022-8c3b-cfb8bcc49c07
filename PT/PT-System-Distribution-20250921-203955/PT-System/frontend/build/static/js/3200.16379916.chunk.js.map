{"version": 3, "file": "static/js/3200.16379916.chunk.js", "mappings": "6MAIA,MA6WA,EA7W2BA,KACzB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAeC,IAAoBC,EAAAA,EAAAA,UAAS,KAC5CC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAChCG,EAAQC,IAAaJ,EAAAA,EAAAA,UAAS,QAC9BK,EAAuBC,IAA4BN,EAAAA,EAAAA,UAAS,KAEnEO,EAAAA,EAAAA,WAAU,KACRC,KACC,CAACL,IAEJ,MAAMK,EAAqBC,UACzBP,GAAW,GACX,IAEE,MAAMQ,EAAoB,CACxB,CACEC,GAAI,EACJC,KAAM,cACNC,MAAO,uBACPC,QAAS,4EACTC,QAAS,gEACTC,UAAW,4NACXC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,MACjCC,MAAM,EACNC,SAAU,SACVC,KAAM,sBACNC,MAAO,QAET,CACEZ,GAAI,EACJC,KAAM,YACNC,MAAO,yBACPC,QAAS,sGACTC,QAAS,sDACTC,UAAW,0KACXC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,OACjCC,MAAM,EACNC,SAAU,OACVC,KAAM,mBACNC,MAAO,SAET,CACEZ,GAAI,EACJC,KAAM,SACNC,MAAO,qBACPC,QAAS,sEACTC,QAAS,8DACTC,UAAW,qQACXC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,OACjCC,MAAM,EACNC,SAAU,MACVC,KAAM,aACNC,MAAO,UAET,CACEZ,GAAI,EACJC,KAAM,UACNC,MAAO,mBACPC,QAAS,yFACTC,QAAS,4CACTC,UAAW,wMACXC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,OACjCC,MAAM,EACNC,SAAU,SACVC,KAAM,qBACNC,MAAO,SAET,CACEZ,GAAI,EACJC,KAAM,QACNC,MAAO,sBACPC,QAAS,2GACTC,QAAS,4DACTC,UAAW,kOACXC,UAAW,IAAIC,KAAKA,KAAKC,MAAQ,OACjCC,MAAM,EACNC,SAAU,OACVC,KAAM,8BACNC,MAAO,QAKX,IAAIC,EAAwBd,EACb,QAAXP,IAEAqB,EADa,WAAXrB,EACsBO,EAAkBP,OAAOsB,IAAMA,EAAEL,MAEjCV,EAAkBP,OAAOsB,GAAKA,EAAEb,OAAST,IAIrEJ,EAAiByB,EACnB,CAAE,MAAOE,GACPC,QAAQD,MAAM,gCAAiCA,GAC/CE,EAAAA,GAAMF,MAAM,+BACd,CAAC,QACCxB,GAAW,EACb,GAoEI2B,EAAoBR,IACxB,OAAQA,GACN,IAAK,OAAQ,MAAO,0BACpB,IAAK,SAAU,MAAO,gCACtB,IAAK,MAAO,MAAO,8BACnB,QAAS,MAAO,8BAIdS,EAAgBlB,IACpB,OAAQA,GACN,IAAK,cAAe,MAAO,gBAC3B,IAAK,YAAa,MAAO,iBACzB,IAAK,UAAW,MAAO,kBACvB,IAAK,QAAS,MAAO,eAErB,QAAS,MAAO,kBAIdmB,EAAmBd,IACvB,MACMe,EADM,IAAId,KACGD,EACbgB,EAAQC,KAAKC,MAAMH,EAAI,MACvBI,EAAOF,KAAKC,MAAMF,EAAQ,IAEhC,OAAIA,EAAQ,EAAUtC,EAAE,UAAW,YAC/BsC,EAAQ,GAAWtC,EAAE,WAAW,GAAD0C,OAAKJ,EAAK,eACzCG,EAAO,EAAUzC,EAAE,UAAU,GAAD0C,OAAKD,EAAI,cAClCnB,EAAUqB,sBAGnB,OAAIrC,GAEAsC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wEAAuEC,SAAA,EACpFC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D9C,EAAE,qBAAsB,0BAE3B4C,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjD9C,EAAE,sBAAuB,8CAI9B+C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2CAA0CC,SAAA,EACvDC,EAAAA,EAAAA,MAAA,UACEC,QA1GYlC,UACpB,IACEV,EAAiB6C,GACfA,EAAKC,IAAIpB,IAACqB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUrB,GAAC,IAAEL,MAAM,MAE/BQ,EAAAA,GAAMmB,QAAQ,mCAChB,CAAE,MAAOrB,GACPC,QAAQD,MAAM,2CAA4CA,GAC1DE,EAAAA,GAAMF,MAAM,2CACd,GAkGQc,UAAU,wEAAuEC,SAAA,EAEjFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BACZ7C,EAAE,cAAe,oBAGnBU,EAAsB2C,OAAS,IAC9BN,EAAAA,EAAAA,MAAA,UACEC,QA7FWlC,UACrB,IACEV,EAAiB6C,GACfA,EAAKzC,OAAOsB,IAAMpB,EAAsB4C,SAASxB,EAAEd,MAErDL,EAAyB,IACzBsB,EAAAA,GAAMmB,QAAQ,iCAChB,CAAE,MAAOrB,GACPC,QAAQD,MAAM,yCAA0CA,GACxDE,EAAAA,GAAMF,MAAM,0CACd,GAoFUc,UAAU,sEAAqEC,SAAA,EAE/EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sBACZ7C,EAAE,iBAAkB,6BAO7B4C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uBAAsBC,SAClC,CAAC,MAAO,SAAU,cAAe,YAAa,UAAW,QAAS,UAAUI,IAAKK,IAChFX,EAAAA,EAAAA,KAAA,UAEEI,QAASA,IAAMvC,EAAU8C,GACzBV,UAAS,8DAAAH,OACPlC,IAAW+C,EACP,yBACA,0GACHT,SAEF9C,EAAEuD,EAAYA,EAAWC,OAAO,GAAGC,cAAgBF,EAAWG,MAAM,KARhEH,MAcVpD,EAAckD,OAAS,IACtBT,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+EAA8EC,UAC3FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACE3B,KAAK,WACL0C,QAASjD,EAAsB2C,SAAWlD,EAAckD,OACxDO,SAAWC,GAAMA,EAAEC,OAAOH,aA1GtChD,EAAyBR,EAAc+C,IAAIpB,GAAKA,EAAEd,UAIlDL,EAAyB,IAuGbkC,UAAU,+DAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D9C,EAAE,YAAa,mBAInBU,EAAsB2C,OAAS,IAC9BN,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2CAA0CC,SAAA,CACvDpC,EAAsB2C,OAAO,IAAErD,EAAE,WAAY,qBAQxD4C,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,SACE,IAAzB3C,EAAckD,QACbN,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mDACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE9C,EAAE,kBAAmB,uBAExB4C,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5C9C,EAAE,6BAA8B,oDAIrCG,EAAc+C,IAAKa,IACjBnB,EAAAA,EAAAA,KAAA,OAEEC,UAAS,8DAAAH,OACPqB,EAAatC,KAAO,kBAAoB,kBAAiB,KAAAiB,OACtDqB,EAAatC,KAA0C,GAAnC,kCAAwCqB,UAEjEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,SACE3B,KAAK,WACL0C,QAASjD,EAAsB4C,SAASS,EAAa/C,IACrD4C,SAAUA,KAAMI,OA7JAC,EA6JyBF,EAAa/C,QA5JtEL,EAAyBsC,GACvBA,EAAKK,SAASW,GACVhB,EAAKzC,OAAOQ,GAAMA,IAAOiD,GACzB,IAAIhB,EAAMgB,IAJgBA,OA8JhBpB,UAAU,oEAGZD,EAAAA,EAAAA,KAAA,OAAKC,UAAS,kBAAAH,OAAoBP,EAAa4B,EAAa9C,MAAK,kBAAiB6B,UAChFF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAH,OAAKqB,EAAapC,KAAI,KAAAe,OAAIP,EAAa4B,EAAa9C,YAGlE8B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,4CAA2CC,SACtD7C,EAAQ8D,EAAa5C,QAAU4C,EAAa7C,SAE/C0B,EAAAA,EAAAA,KAAA,QAAMC,UAAS,8CAAAH,OAAgDR,EAAiB6B,EAAarC,WAAYoB,SACtG9C,EAAE+D,EAAarC,SAAUqC,EAAarC,aAEvCqC,EAAatC,OACbmB,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yCAGpBD,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD7C,EAAQ8D,EAAa1C,UAAY0C,EAAa3C,WAEjDwB,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACpDV,EAAgB2B,EAAazC,oBAKpCyB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EACxCiB,EAAatC,OACbmB,EAAAA,EAAAA,KAAA,UACEI,QAASA,IA9OVlC,WACjB,IACEV,EAAiB6C,GACfA,EAAKC,IAAIpB,GACPA,EAAEd,KAAOiD,GAAcd,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQrB,GAAC,IAAEL,MAAM,IAASK,IAGrDG,EAAAA,GAAMmB,QAAQ,8BAChB,CAAE,MAAOrB,GACPC,QAAQD,MAAM,sCAAuCA,GACrDE,EAAAA,GAAMF,MAAM,sCACd,GAmOiCmC,CAAWH,EAAa/C,IACvC6B,UAAU,0DACV3B,MAAOlB,EAAE,aAAc,gBAAgB8C,UAEvCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBAIjBD,EAAAA,EAAAA,KAAA,UACEI,QAASA,IA7NAlC,WACzB,IACEV,EAAiB6C,GAAQA,EAAKzC,OAAOsB,GAAKA,EAAEd,KAAOiD,IACnDhC,EAAAA,GAAMmB,QAAQ,uBAChB,CAAE,MAAOrB,GACPC,QAAQD,MAAM,+BAAgCA,GAC9CE,EAAAA,GAAMF,MAAM,gCACd,GAsN+BoC,CAAmBJ,EAAa/C,IAC/C6B,UAAU,yDACV3B,MAAOlB,EAAE,SAAU,UAAU8C,UAE7BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0BAvDdkB,EAAa/C,U", "sources": ["pages/Notifications/NotificationCenter.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst NotificationCenter = () => {\n  const { t, isRTL } = useLanguage();\n  const [notifications, setNotifications] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [filter, setFilter] = useState('all');\n  const [selectedNotifications, setSelectedNotifications] = useState([]);\n\n  useEffect(() => {\n    fetchNotifications();\n  }, [filter]);\n\n  const fetchNotifications = async () => {\n    setLoading(true);\n    try {\n      // Mock notifications data - replace with actual API call\n      const mockNotifications = [\n        {\n          id: 1,\n          type: 'appointment',\n          title: 'Appointment Reminder',\n          titleAr: 'تذكير بالموعد',\n          message: 'You have an appointment with <PERSON> tomorrow at 2:00 PM',\n          messageAr: 'لديك موعد مع أحمد حسن غداً في الساعة 2:00 مساءً',\n          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n          read: false,\n          priority: 'medium',\n          icon: 'fas fa-calendar-alt',\n          color: 'blue'\n        },\n        {\n          id: 2,\n          type: 'treatment',\n          title: 'Treatment Plan Updated',\n          titleAr: 'تم تحديث خطة العلاج',\n          message: 'Treatment plan for Sarah Al-Rashid has been updated',\n          messageAr: 'تم تحديث خطة العلاج لسارة الرشيد',\n          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n          read: true,\n          priority: 'high',\n          icon: 'fas fa-heartbeat',\n          color: 'green'\n        },\n        {\n          id: 3,\n          type: 'system',\n          title: 'System Maintenance',\n          titleAr: 'صيانة النظام',\n          message: 'Scheduled maintenance will occur tonight from 11 PM to 1 AM',\n          messageAr: 'ستتم الصيانة المجدولة الليلة من 11 مساءً إلى 1 صباحاً',\n          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n          read: false,\n          priority: 'low',\n          icon: 'fas fa-cog',\n          color: 'yellow'\n        },\n        {\n          id: 4,\n          type: 'billing',\n          title: 'Payment Received',\n          titleAr: 'تم استلام الدفعة',\n          message: 'Payment of $250 received from Omar Khalil',\n          messageAr: 'تم استلام دفعة بقيمة 250 دولار من عمر خليل',\n          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),\n          read: true,\n          priority: 'medium',\n          icon: 'fas fa-dollar-sign',\n          color: 'green'\n        },\n        {\n          id: 5,\n          type: 'alert',\n          title: 'High Priority Alert',\n          titleAr: 'تنبيه عالي الأولوية',\n          message: 'Patient Fatima Al-Zahra missed 3 consecutive appointments',\n          messageAr: 'المريضة فاطمة الزهراء فاتت 3 مواعيد متتالية',\n          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),\n          read: false,\n          priority: 'high',\n          icon: 'fas fa-exclamation-triangle',\n          color: 'red'\n        }\n      ];\n\n      // Filter notifications based on selected filter\n      let filteredNotifications = mockNotifications;\n      if (filter !== 'all') {\n        if (filter === 'unread') {\n          filteredNotifications = mockNotifications.filter(n => !n.read);\n        } else {\n          filteredNotifications = mockNotifications.filter(n => n.type === filter);\n        }\n      }\n\n      setNotifications(filteredNotifications);\n    } catch (error) {\n      console.error('Error fetching notifications:', error);\n      toast.error('Failed to load notifications');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const markAsRead = async (notificationId) => {\n    try {\n      setNotifications(prev => \n        prev.map(n => \n          n.id === notificationId ? { ...n, read: true } : n\n        )\n      );\n      toast.success('Notification marked as read');\n    } catch (error) {\n      console.error('Error marking notification as read:', error);\n      toast.error('Failed to mark notification as read');\n    }\n  };\n\n  const markAllAsRead = async () => {\n    try {\n      setNotifications(prev => \n        prev.map(n => ({ ...n, read: true }))\n      );\n      toast.success('All notifications marked as read');\n    } catch (error) {\n      console.error('Error marking all notifications as read:', error);\n      toast.error('Failed to mark all notifications as read');\n    }\n  };\n\n  const deleteNotification = async (notificationId) => {\n    try {\n      setNotifications(prev => prev.filter(n => n.id !== notificationId));\n      toast.success('Notification deleted');\n    } catch (error) {\n      console.error('Error deleting notification:', error);\n      toast.error('Failed to delete notification');\n    }\n  };\n\n  const deleteSelected = async () => {\n    try {\n      setNotifications(prev => \n        prev.filter(n => !selectedNotifications.includes(n.id))\n      );\n      setSelectedNotifications([]);\n      toast.success('Selected notifications deleted');\n    } catch (error) {\n      console.error('Error deleting selected notifications:', error);\n      toast.error('Failed to delete selected notifications');\n    }\n  };\n\n  const toggleSelectNotification = (notificationId) => {\n    setSelectedNotifications(prev => \n      prev.includes(notificationId)\n        ? prev.filter(id => id !== notificationId)\n        : [...prev, notificationId]\n    );\n  };\n\n  const selectAllNotifications = () => {\n    setSelectedNotifications(notifications.map(n => n.id));\n  };\n\n  const clearSelection = () => {\n    setSelectedNotifications([]);\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high': return 'text-red-600 bg-red-100';\n      case 'medium': return 'text-yellow-600 bg-yellow-100';\n      case 'low': return 'text-green-600 bg-green-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getTypeColor = (type) => {\n    switch (type) {\n      case 'appointment': return 'text-blue-600';\n      case 'treatment': return 'text-green-600';\n      case 'billing': return 'text-purple-600';\n      case 'alert': return 'text-red-600';\n      case 'system': return 'text-gray-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const formatTimestamp = (timestamp) => {\n    const now = new Date();\n    const diff = now - timestamp;\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const days = Math.floor(hours / 24);\n\n    if (hours < 1) return t('justNow', 'Just now');\n    if (hours < 24) return t('hoursAgo', `${hours} hours ago`);\n    if (days < 7) return t('daysAgo', `${days} days ago`);\n    return timestamp.toLocaleDateString();\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('notificationCenter', 'Notification Center')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('manageNotifications', 'Manage your notifications and alerts')}\n          </p>\n        </div>\n\n        <div className=\"flex items-center space-x-4 mt-4 sm:mt-0\">\n          <button\n            onClick={markAllAsRead}\n            className=\"px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors\"\n          >\n            <i className=\"fas fa-check-double mr-2\"></i>\n            {t('markAllRead', 'Mark All Read')}\n          </button>\n          \n          {selectedNotifications.length > 0 && (\n            <button\n              onClick={deleteSelected}\n              className=\"px-4 py-2 text-red-600 hover:bg-red-50 rounded-md transition-colors\"\n            >\n              <i className=\"fas fa-trash mr-2\"></i>\n              {t('deleteSelected', 'Delete Selected')}\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-wrap gap-2\">\n        {['all', 'unread', 'appointment', 'treatment', 'billing', 'alert', 'system'].map((filterType) => (\n          <button\n            key={filterType}\n            onClick={() => setFilter(filterType)}\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${\n              filter === filterType\n                ? 'bg-blue-600 text-white'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'\n            }`}\n          >\n            {t(filterType, filterType.charAt(0).toUpperCase() + filterType.slice(1))}\n          </button>\n        ))}\n      </div>\n\n      {/* Bulk Actions */}\n      {notifications.length > 0 && (\n        <div className=\"flex items-center justify-between bg-gray-50 dark:bg-gray-800 p-4 rounded-lg\">\n          <div className=\"flex items-center space-x-4\">\n            <label className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                checked={selectedNotifications.length === notifications.length}\n                onChange={(e) => e.target.checked ? selectAllNotifications() : clearSelection()}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                {t('selectAll', 'Select All')}\n              </span>\n            </label>\n            \n            {selectedNotifications.length > 0 && (\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {selectedNotifications.length} {t('selected', 'selected')}\n              </span>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Notifications List */}\n      <div className=\"space-y-4\">\n        {notifications.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-bell-slash text-4xl text-gray-400 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('noNotifications', 'No notifications')}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {t('noNotificationsDescription', 'You\\'re all caught up! No new notifications.')}\n            </p>\n          </div>\n        ) : (\n          notifications.map((notification) => (\n            <div\n              key={notification.id}\n              className={`bg-white dark:bg-gray-800 rounded-lg shadow p-4 border-l-4 ${\n                notification.read ? 'border-gray-300' : 'border-blue-500'\n              } ${!notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-start space-x-4\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedNotifications.includes(notification.id)}\n                    onChange={() => toggleSelectNotification(notification.id)}\n                    className=\"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  \n                  <div className={`p-2 rounded-lg ${getTypeColor(notification.type)} bg-opacity-10`}>\n                    <i className={`${notification.icon} ${getTypeColor(notification.type)}`}></i>\n                  </div>\n                  \n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                        {isRTL ? notification.titleAr : notification.title}\n                      </h4>\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(notification.priority)}`}>\n                        {t(notification.priority, notification.priority)}\n                      </span>\n                      {!notification.read && (\n                        <span className=\"w-2 h-2 bg-blue-600 rounded-full\"></span>\n                      )}\n                    </div>\n                    <p className=\"text-gray-600 dark:text-gray-400 text-sm mb-2\">\n                      {isRTL ? notification.messageAr : notification.message}\n                    </p>\n                    <p className=\"text-xs text-gray-500 dark:text-gray-500\">\n                      {formatTimestamp(notification.timestamp)}\n                    </p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center space-x-2\">\n                  {!notification.read && (\n                    <button\n                      onClick={() => markAsRead(notification.id)}\n                      className=\"p-2 text-gray-400 hover:text-blue-600 transition-colors\"\n                      title={t('markAsRead', 'Mark as read')}\n                    >\n                      <i className=\"fas fa-check\"></i>\n                    </button>\n                  )}\n                  \n                  <button\n                    onClick={() => deleteNotification(notification.id)}\n                    className=\"p-2 text-gray-400 hover:text-red-600 transition-colors\"\n                    title={t('delete', 'Delete')}\n                  >\n                    <i className=\"fas fa-trash\"></i>\n                  </button>\n                </div>\n              </div>\n            </div>\n          ))\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default NotificationCenter;\n"], "names": ["NotificationCenter", "t", "isRTL", "useLanguage", "notifications", "setNotifications", "useState", "loading", "setLoading", "filter", "setFilter", "selectedNotifications", "setSelectedNotifications", "useEffect", "fetchNotifications", "async", "mockNotifications", "id", "type", "title", "titleAr", "message", "messageAr", "timestamp", "Date", "now", "read", "priority", "icon", "color", "filteredNotifications", "n", "error", "console", "toast", "getPriorityColor", "getTypeColor", "formatTimestamp", "diff", "hours", "Math", "floor", "days", "concat", "toLocaleDateString", "_jsx", "className", "children", "_jsxs", "onClick", "prev", "map", "_objectSpread", "success", "length", "includes", "filterType", "char<PERSON>t", "toUpperCase", "slice", "checked", "onChange", "e", "target", "notification", "toggleSelectNotification", "notificationId", "mark<PERSON><PERSON><PERSON>", "deleteNotification"], "sourceRoot": ""}