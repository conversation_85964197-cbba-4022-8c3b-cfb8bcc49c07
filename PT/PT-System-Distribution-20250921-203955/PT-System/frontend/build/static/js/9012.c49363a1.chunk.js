"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9012],{9012:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var s=a(5043),r=a(7921),i=a(579);const d=()=>{const{t:e}=(0,r.o)(),[t,a]=(0,s.useState)([]),[d,l]=(0,s.useState)(!0),[n,o]=(0,s.useState)(""),[x,m]=(0,s.useState)("all"),[c,g]=(0,s.useState)("all");(0,s.useEffect)(()=>{const e=[{id:1,formName:"PT Adult Initial Assessment",patientName:"<PERSON>",patientId:"PT001",submittedBy:"Dr. <PERSON>",submittedDate:"2024-02-20T10:30:00Z",status:"completed",formType:"assessment",priority:"normal"},{id:2,formName:"Treatment Plan & Goals",patientName:"<PERSON>",patientId:"PT002",submittedBy:"Dr. <PERSON>",submittedDate:"2024-02-20T09:15:00Z",status:"pending_review",formType:"treatment",priority:"high"},{id:3,formName:"Daily Progress Note",patientName:"Mike Johnson",patientId:"PT003",submittedBy:"Dr. Emily Davis",submittedDate:"2024-02-20T08:45:00Z",status:"completed",formType:"progress",priority:"normal"},{id:4,formName:"Pain Assessment Scale",patientName:"Sarah Wilson",patientId:"PT004",submittedBy:"Dr. James Miller",submittedDate:"2024-02-19T16:20:00Z",status:"draft",formType:"assessment",priority:"low"},{id:5,formName:"Discharge Assessment",patientName:"David Brown",patientId:"PT005",submittedBy:"Dr. Lisa Anderson",submittedDate:"2024-02-19T14:10:00Z",status:"completed",formType:"discharge",priority:"high"}];setTimeout(()=>{a(e),l(!1)},1e3)},[]);const h=e=>{switch(e){case"completed":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200";case"pending_review":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-200";case"draft":return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-200";default:return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200"}},u=e=>{switch(e){case"high":return"text-red-600 dark:text-red-400";case"normal":default:return"text-blue-600 dark:text-blue-400";case"low":return"text-gray-600 dark:text-gray-400"}},p=t.filter(e=>{const t=e.patientName.toLowerCase().includes(n.toLowerCase())||e.formName.toLowerCase().includes(n.toLowerCase())||e.submittedBy.toLowerCase().includes(n.toLowerCase()),a="all"===x||e.status===x,s="all"===c||e.formType===c;return t&&a&&s});return d?(0,i.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,i.jsxs)("div",{className:"animate-pulse",children:[(0,i.jsx)("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"}),(0,i.jsx)("div",{className:"space-y-4",children:[...Array(5)].map((e,t)=>(0,i.jsx)("div",{className:"h-16 bg-gray-200 dark:bg-gray-700 rounded"},t))})]})}):(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsx)("div",{className:"bg-gradient-to-r from-green-500 via-teal-500 to-blue-500 rounded-lg shadow-lg mb-8",children:(0,i.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,i.jsx)("div",{className:"px-6 py-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-green-600 via-teal-600 to-blue-600 bg-clip-text text-transparent",children:e("formSubmissions","Form Submissions")}),(0,i.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-inbox text-green-500 mr-2"}),e("submissionsDescription","Review and manage all submitted forms and patient data")]})]}),(0,i.jsxs)("div",{className:"bg-gradient-to-r from-green-400 to-teal-400 text-white px-4 py-2 rounded-full shadow-lg",children:[(0,i.jsx)("i",{className:"fas fa-file-check mr-2"}),p.length," ",e("submissions","Submissions")]})]})})})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 mb-8",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("search","Search")}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("input",{type:"text",value:n,onChange:e=>o(e.target.value),placeholder:e("searchSubmissions","Search submissions..."),className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,i.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("status","Status")}),(0,i.jsxs)("select",{value:x,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,i.jsx)("option",{value:"completed",children:e("completed","Completed")}),(0,i.jsx)("option",{value:"pending_review",children:e("pendingReview","Pending Review")}),(0,i.jsx)("option",{value:"draft",children:e("draft","Draft")})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("formType","Form Type")}),(0,i.jsxs)("select",{value:c,onChange:e=>g(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"all",children:e("allForms","All Forms")}),(0,i.jsx)("option",{value:"assessment",children:e("assessment","Assessment")}),(0,i.jsx)("option",{value:"treatment",children:e("treatment","Treatment")}),(0,i.jsx)("option",{value:"progress",children:e("progress","Progress")}),(0,i.jsx)("option",{value:"discharge",children:e("discharge","Discharge")})]})]}),(0,i.jsx)("div",{className:"flex items-end",children:(0,i.jsxs)("button",{onClick:()=>{o(""),m("all"),g("all")},className:"w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-undo mr-2"}),e("clearFilters","Clear Filters")]})})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden",children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-700",children:[(0,i.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-900",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("form","Form")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("submittedBy","Submitted By")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("date","Date")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,i.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:p.map(e=>{return(0,i.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-file-alt text-lg mr-3 ".concat(u(e.priority))}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.formName}),(0,i.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.formType})]})]})}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.patientName}),(0,i.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",e.patientId]})]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:e.submittedBy}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:(t=e.submittedDate,new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(h(e.status)),children:e.status.replace("_"," ")})}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200",children:(0,i.jsx)("i",{className:"fas fa-eye"})}),(0,i.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200",children:(0,i.jsx)("i",{className:"fas fa-edit"})}),(0,i.jsx)("button",{className:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200",children:(0,i.jsx)("i",{className:"fas fa-download"})})]})})]},e.id);var t})})]})})}),0===p.length&&(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("i",{className:"fas fa-inbox text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noSubmissions","No submissions found")}),(0,i.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("noSubmissionsDesc","No form submissions match your current filters.")})]})]})}}}]);
//# sourceMappingURL=9012.c49363a1.chunk.js.map