"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9920],{9920:(e,s,t)=>{t.r(s),t.d(s,{default:()=>r});var a=t(5043),l=t(4528),n=t(3768),o=t(579);const r=()=>{const[e,s]=(0,a.useState)([]),[t,r]=(0,a.useState)(!0),[c,i]=(0,a.useState)(null),{user:d}=(0,l.A)();(0,a.useEffect)(()=>{m()},[]);const m=async()=>{r(!0),i(null);try{console.log("Loading templates..."),console.log("User:",d),console.log("Token:",localStorage.getItem("token"));const t=await fetch("/api/v1/invoice-templates",{method:"GET",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(console.log("Response status:",t.status),console.log("Response headers:",t.headers),!t.ok){const e=await t.text();throw console.error("Error response:",e),new Error("HTTP ".concat(t.status,": ").concat(e))}{var e;const a=await t.json();console.log("Templates data:",a),s(a.data||[]),n.Ay.success("Loaded ".concat((null===(e=a.data)||void 0===e?void 0:e.length)||0," templates"))}}catch(c){console.error("Error loading templates:",c),i(c.message),n.Ay.error("Failed to load templates: ".concat(c.message))}finally{r(!1)}};return t?(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Test Invoice Templates"}),(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"}),(0,o.jsx)("span",{children:"Loading templates..."})]})]}):c?(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Test Invoice Templates"}),(0,o.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-red-800 font-medium",children:"Error Loading Templates"}),(0,o.jsx)("p",{className:"text-red-600 mt-1",children:c}),(0,o.jsx)("button",{onClick:m,className:"mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"Retry"})]})]}):(0,o.jsxs)("div",{className:"p-6 max-w-6xl mx-auto",children:[(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Test Invoice Templates"}),(0,o.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Testing the invoice templates functionality"}),(0,o.jsxs)("div",{className:"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,o.jsx)("h3",{className:"text-blue-800 font-medium",children:"Debug Info"}),(0,o.jsxs)("p",{className:"text-blue-600 text-sm mt-1",children:["User: ",null===d||void 0===d?void 0:d.firstName," ",null===d||void 0===d?void 0:d.lastName," (",null===d||void 0===d?void 0:d.email,")"]}),(0,o.jsxs)("p",{className:"text-blue-600 text-sm",children:["Token: ",localStorage.getItem("token")?"Present":"Missing"]}),(0,o.jsxs)("p",{className:"text-blue-600 text-sm",children:["Templates loaded: ",e.length]})]})]}),(0,o.jsxs)("div",{className:"mb-4 flex justify-between items-center",children:[(0,o.jsxs)("h2",{className:"text-lg font-semibold",children:["Available Templates (",e.length,")"]}),(0,o.jsx)("button",{onClick:m,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Reload Templates"})]}),0===e.length?(0,o.jsx)("div",{className:"text-center py-8 bg-gray-50 rounded-lg",children:(0,o.jsx)("p",{className:"text-gray-600",children:"No templates found"})}):(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>{var s,t,a,l,r,i;return(0,o.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 shadow hover:shadow-md transition-shadow",children:[(0,o.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-semibold text-gray-900",children:e.name}),(0,o.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]}),e.isDefault&&(0,o.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 text-xs rounded",children:"Default"})]}),(0,o.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600",children:"Type:"}),(0,o.jsx)("span",{className:"font-medium",children:e.type})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600",children:"Category:"}),(0,o.jsx)("span",{className:"font-medium",children:e.category})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600",children:"Usage:"}),(0,o.jsxs)("span",{className:"font-medium",children:[e.usageCount," times"]})]}),(0,o.jsxs)("div",{className:"flex justify-between",children:[(0,o.jsx)("span",{className:"text-gray-600",children:"Version:"}),(0,o.jsx)("span",{className:"font-medium",children:e.version})]})]}),(0,o.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,o.jsx)("button",{onClick:()=>(async e=>{try{console.log("Using template:",e);const s=await fetch("/api/v1/invoice-templates/".concat(e,"/use"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!s.ok){const e=await s.text();throw console.error("Use template error:",e),new Error("HTTP ".concat(s.status,": ").concat(e))}{const e=await s.json();console.log("Use template response:",e),n.Ay.success("Template used successfully!"),m()}}catch(c){console.error("Error using template:",c),n.Ay.error("Failed to use template: ".concat(c.message))}})(e._id),className:"w-full px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm",children:"Test Use Template"}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,o.jsx)("button",{onClick:()=>console.log("Template details:",e),className:"px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm",children:"Log Details"}),(0,o.jsx)("button",{onClick:()=>navigator.clipboard.writeText(e._id),className:"px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm",children:"Copy ID"})]})]}),(0,o.jsxs)("div",{className:"mt-3 flex flex-wrap gap-1",children:[(null===(s=e.config)||void 0===s||null===(t=s.compliance)||void 0===t?void 0:t.zatcaCompliant)&&(0,o.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-700 text-xs rounded",children:"ZATCA"}),(null===(a=e.config)||void 0===a||null===(l=a.compliance)||void 0===l?void 0:l.nphiesCompliant)&&(0,o.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded",children:"NPHIES"}),(null===(r=e.config)||void 0===r||null===(i=r.compliance)||void 0===i?void 0:i.requireElectronicSignature)&&(0,o.jsx)("span",{className:"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded",children:"E-Sign"})]})]},e._id)})})]})}}}]);
//# sourceMappingURL=9920.c58f1c32.chunk.js.map