"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2866],{2866:(e,t,a)=>{a.r(t),a.d(t,{default:()=>v});var s=a(5043),r=a(7921),l=a(5475),i=a(1327),n=a(6150),d=a(108),c=a(7869),o=a(4240),x=a(7734),m=a(168),g=a(3839),h=a(8643),p=a(2185),u=a(6026),j=a(5748),b=a(2291),y=a(163),f=a(877),N=a(579);const v=()=>{const{t:e}=(0,r.o)(),[t,a]=(0,s.useState)("thisMonth"),[v,k]=(0,s.useState)(!0),[w,I]=(0,s.useState)([]),[A,C]=(0,s.useState)([]);(0,s.useEffect)(()=>{R()},[t]);const R=async()=>{k(!0);try{const e=await fetch("/api/v1/billing/stats",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}}),t=await fetch("/api/v1/payments/stats",{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(!e.ok||!t.ok)throw new Error("Failed to fetch financial data");{const a=await e.json(),s=(await t.json(),(a.data.monthlyRevenue||[]).map(e=>({month:e.month,revenue:e.revenue,paid:e.paid,profit:.7*e.paid,patients:3*e.count})));I(s);C([{category:"Salaries",amount:25e3,percentage:45},{category:"Equipment",amount:8e3,percentage:15},{category:"Rent",amount:6e3,percentage:11},{category:"Utilities",amount:3e3,percentage:5},{category:"Supplies",amount:4e3,percentage:7},{category:"Insurance",amount:2e3,percentage:4},{category:"Other",amount:7e3,percentage:13}])}}catch(e){console.error("Error fetching financial data:",e)}finally{k(!1)}},T=125e3,F=85e3,P=4e4,S=15e3,K=8,_=45,E=3,B=[{title:e("processPayment","Process Payment"),icon:"fas fa-credit-card",color:"blue",link:"/payments/new",description:e("processNewPayment","Process a new patient payment")},{title:e("submitClaim","Submit Insurance Claim"),icon:"fas fa-file-invoice",color:"green",link:"/insurance/claims/new",description:e("submitNewClaim","Submit a new insurance claim")},{title:e("generateInvoice","Generate Invoice"),icon:"fas fa-receipt",color:"purple",link:"/billing/invoice/new",description:e("createNewInvoice","Create a new patient invoice")},{title:e("viewReports","Financial Reports"),icon:"fas fa-chart-line",color:"orange",link:"/reports/financial",description:e("viewFinancialReports","View detailed financial reports")}],D=e=>{switch(e){case"completed":return"text-green-600 bg-green-100";case"pending":return"text-yellow-600 bg-yellow-100";case"failed":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}},M=e=>new Intl.NumberFormat("ar-SA",{style:"currency",currency:"SAR"}).format(e);return(0,N.jsxs)("div",{className:"space-y-6",children:[(0,N.jsxs)("div",{className:"flex items-center justify-between",children:[(0,N.jsxs)("div",{children:[(0,N.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("financialDashboard","Financial Dashboard")}),(0,N.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("financialOverview","Overview of your clinic's financial performance")})]}),(0,N.jsx)("div",{className:"flex items-center space-x-4",children:(0,N.jsxs)("select",{value:t,onChange:e=>a(e.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,N.jsx)("option",{value:"thisWeek",children:e("thisWeek","This Week")}),(0,N.jsx)("option",{value:"thisMonth",children:e("thisMonth","This Month")}),(0,N.jsx)("option",{value:"thisQuarter",children:e("thisQuarter","This Quarter")}),(0,N.jsx)("option",{value:"thisYear",children:e("thisYear","This Year")})]})})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,N.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("quickActions","Quick Actions")}),(0,N.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,N.jsxs)(l.N_,{to:"/financial/templates",className:"flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors",children:[(0,N.jsx)("div",{className:"p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg mr-3",children:(0,N.jsx)("i",{className:"fas fa-file-invoice text-blue-600 dark:text-blue-400"})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("invoiceTemplates","Invoice Templates")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("manageTemplates","Manage templates")})]})]}),(0,N.jsxs)(l.N_,{to:"/financial/invoices/create",className:"flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors",children:[(0,N.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900/50 rounded-lg mr-3",children:(0,N.jsx)("i",{className:"fas fa-plus text-green-600 dark:text-green-400"})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("createInvoice","Create Invoice")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("newInvoice","New invoice")})]})]}),(0,N.jsxs)(l.N_,{to:"/financial/invoices",className:"flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors",children:[(0,N.jsx)("div",{className:"p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg mr-3",children:(0,N.jsx)("i",{className:"fas fa-list text-purple-600 dark:text-purple-400"})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("viewInvoices","View Invoices")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("allInvoices","All invoices")})]})]}),(0,N.jsxs)(l.N_,{to:"/integrations",className:"flex items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors",children:[(0,N.jsx)("div",{className:"p-2 bg-orange-100 dark:bg-orange-900/50 rounded-lg mr-3",children:(0,N.jsx)("i",{className:"fas fa-link text-orange-600 dark:text-orange-400"})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e("integrations","Saudi Integrations")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("nphiesZatca","NPHIES & ZATCA")})]})]})]}),(0,N.jsxs)("div",{className:"mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800",children:[(0,N.jsx)("h3",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:"\ud83e\uddea Testing & Debug"}),(0,N.jsxs)("div",{className:"flex space-x-2",children:[(0,N.jsx)(l.N_,{to:"/financial/test-templates",className:"px-3 py-1 bg-yellow-200 text-yellow-800 rounded text-sm hover:bg-yellow-300",children:"Test Templates API"}),(0,N.jsx)(l.N_,{to:"/financial/templates",className:"px-3 py-1 bg-blue-200 text-blue-800 rounded text-sm hover:bg-blue-300",children:"Templates UI"})]})]})]}),(0,N.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,N.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,N.jsxs)("div",{className:"flex items-center",children:[(0,N.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,N.jsx)("i",{className:"fas fa-dollar-sign text-green-600 dark:text-green-400 text-xl"})}),(0,N.jsxs)("div",{className:"ml-4",children:[(0,N.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalRevenue","Total Revenue")}),(0,N.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:M(T)}),(0,N.jsxs)("p",{className:"text-sm text-green-600",children:["+12% ",e("fromLastMonth","from last month")]})]})]})}),(0,N.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,N.jsxs)("div",{className:"flex items-center",children:[(0,N.jsx)("div",{className:"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg",children:(0,N.jsx)("i",{className:"fas fa-minus-circle text-red-600 dark:text-red-400 text-xl"})}),(0,N.jsxs)("div",{className:"ml-4",children:[(0,N.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalExpenses","Total Expenses")}),(0,N.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:M(F)}),(0,N.jsxs)("p",{className:"text-sm text-red-600",children:["+5% ",e("fromLastMonth","from last month")]})]})]})}),(0,N.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,N.jsxs)("div",{className:"flex items-center",children:[(0,N.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,N.jsx)("i",{className:"fas fa-chart-line text-blue-600 dark:text-blue-400 text-xl"})}),(0,N.jsxs)("div",{className:"ml-4",children:[(0,N.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("netProfit","Net Profit")}),(0,N.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:M(P)}),(0,N.jsxs)("p",{className:"text-sm text-blue-600",children:["+18% ",e("fromLastMonth","from last month")]})]})]})}),(0,N.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,N.jsxs)("div",{className:"flex items-center",children:[(0,N.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,N.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,N.jsxs)("div",{className:"ml-4",children:[(0,N.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("outstandingPayments","Outstanding")}),(0,N.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:M(S)}),(0,N.jsx)("p",{className:"text-sm text-yellow-600",children:e("pendingCollection","Pending collection")})]})]})})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,N.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600",children:(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("quickActions","Quick Actions")})}),(0,N.jsx)("div",{className:"p-6",children:(0,N.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:B.map((e,t)=>(0,N.jsx)(l.N_,{to:e.link,className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-".concat(e.color,"-300 hover:bg-").concat(e.color,"-50 dark:hover:bg-").concat(e.color,"-900/20 transition-colors group"),children:(0,N.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,N.jsx)("div",{className:"p-2 bg-".concat(e.color,"-100 dark:bg-").concat(e.color,"-900/30 rounded-lg group-hover:bg-").concat(e.color,"-200 dark:group-hover:bg-").concat(e.color,"-900/50 transition-colors"),children:(0,N.jsx)("i",{className:"".concat(e.icon," text-").concat(e.color,"-600 dark:text-").concat(e.color,"-400")})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.description})]})]})},t))})})]}),(0,N.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,N.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("recentTransactions","Recent Transactions")}),(0,N.jsx)(l.N_,{to:"/payments",className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:e("viewAll","View All")})]}),(0,N.jsx)("div",{className:"p-6",children:(0,N.jsx)("div",{className:"space-y-4",children:[{id:"TXN-001",type:"payment",patient:"Ahmed Al-Rashid",amount:350,method:"Cash",status:"completed",date:"2024-02-15",description:"Physical Therapy Session"},{id:"TXN-002",type:"insurance",patient:"Fatima Al-Zahra",amount:500,method:"Bupa Arabia",status:"pending",date:"2024-02-14",description:"Initial Assessment"},{id:"TXN-003",type:"payment",patient:"Mohammed Al-Otaibi",amount:250,method:"Card",status:"completed",date:"2024-02-13",description:"Follow-up Session"}].map(t=>(0,N.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,N.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,N.jsx)("div",{className:"p-2 rounded-lg ".concat("payment"===t.type?"bg-green-100 dark:bg-green-900/30":"bg-blue-100 dark:bg-blue-900/30"),children:(0,N.jsx)("i",{className:"".concat("payment"===t.type?"fas fa-credit-card text-green-600 dark:text-green-400":"fas fa-shield-alt text-blue-600 dark:text-blue-400")})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:t.patient}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.description})]})]}),(0,N.jsxs)("div",{className:"text-right",children:[(0,N.jsx)("p",{className:"font-semibold text-gray-900 dark:text-white",children:M(t.amount)}),(0,N.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(D(t.status)),children:e(t.status,t.status)})]})]},t.id))})})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,N.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("insuranceClaims","Insurance Claims")}),(0,N.jsx)(l.N_,{to:"/insurance",className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:e("viewAll","View All")})]}),(0,N.jsx)("div",{className:"p-6",children:(0,N.jsxs)("div",{className:"space-y-4",children:[(0,N.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,N.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,N.jsx)("div",{className:"p-2 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,N.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400"})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e("approvedClaims","Approved Claims")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("thisMonth","This month")})]})]}),(0,N.jsx)("div",{className:"text-right",children:(0,N.jsx)("p",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:_})})]}),(0,N.jsxs)("div",{className:"flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:[(0,N.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,N.jsx)("div",{className:"p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,N.jsx)("i",{className:"fas fa-clock text-yellow-600 dark:text-yellow-400"})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e("pendingClaims","Pending Claims")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("awaitingApproval","Awaiting approval")})]})]}),(0,N.jsx)("div",{className:"text-right",children:(0,N.jsx)("p",{className:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",children:K})})]}),(0,N.jsxs)("div",{className:"flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg",children:[(0,N.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,N.jsx)("div",{className:"p-2 bg-red-100 dark:bg-red-900/30 rounded-lg",children:(0,N.jsx)("i",{className:"fas fa-times-circle text-red-600 dark:text-red-400"})}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e("rejectedClaims","Rejected Claims")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("requiresReview","Requires review")})]})]}),(0,N.jsx)("div",{className:"text-right",children:(0,N.jsx)("p",{className:"text-2xl font-bold text-red-600 dark:text-red-400",children:E})})]})]})})]})]}),!v&&(0,N.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6",children:[(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("revenueTrend","Revenue Trend")}),(0,N.jsx)(d.u,{width:"100%",height:300,children:(0,N.jsxs)(f.Q,{data:w,children:[(0,N.jsx)(x.d,{strokeDasharray:"3 3"}),(0,N.jsx)(p.W,{dataKey:"month"}),(0,N.jsx)(u.h,{}),(0,N.jsx)(n.m,{formatter:e=>["$".concat(e.toLocaleString()),""]}),(0,N.jsx)(i.s,{}),(0,N.jsx)(g.G,{type:"monotone",dataKey:"revenue",stackId:"1",stroke:"#8884d8",fill:"#8884d8",name:"Revenue"}),(0,N.jsx)(g.G,{type:"monotone",dataKey:"expenses",stackId:"1",stroke:"#82ca9d",fill:"#82ca9d",name:"Expenses"}),(0,N.jsx)(g.G,{type:"monotone",dataKey:"profit",stackId:"1",stroke:"#ffc658",fill:"#ffc658",name:"Profit"})]})})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("expenseBreakdown","Expense Breakdown")}),(0,N.jsx)(d.u,{width:"100%",height:300,children:(0,N.jsxs)(y.r,{children:[(0,N.jsx)(o.F,{data:A,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percentage:a}=e;return"".concat(t," ").concat(a,"%")},outerRadius:80,fill:"#8884d8",dataKey:"amount",children:A.map((e,t)=>(0,N.jsx)(c.f,{fill:["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658"][t%7]},"cell-".concat(t)))}),(0,N.jsx)(n.m,{formatter:e=>["$".concat(e.toLocaleString()),""]})]})})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("monthlyPerformance","Monthly Performance")}),(0,N.jsx)(d.u,{width:"100%",height:300,children:(0,N.jsxs)(b.E,{data:w,children:[(0,N.jsx)(x.d,{strokeDasharray:"3 3"}),(0,N.jsx)(p.W,{dataKey:"month"}),(0,N.jsx)(u.h,{}),(0,N.jsx)(n.m,{formatter:e=>["$".concat(e.toLocaleString()),""]}),(0,N.jsx)(i.s,{}),(0,N.jsx)(h.y,{dataKey:"revenue",fill:"#8884d8",name:"Revenue"}),(0,N.jsx)(h.y,{dataKey:"profit",fill:"#82ca9d",name:"Profit"})]})})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("patientRevenueCorrelation","Patient vs Revenue")}),(0,N.jsx)(d.u,{width:"100%",height:300,children:(0,N.jsxs)(j.b,{data:w,children:[(0,N.jsx)(x.d,{strokeDasharray:"3 3"}),(0,N.jsx)(p.W,{dataKey:"month"}),(0,N.jsx)(u.h,{yAxisId:"left"}),(0,N.jsx)(u.h,{yAxisId:"right",orientation:"right"}),(0,N.jsx)(n.m,{}),(0,N.jsx)(i.s,{}),(0,N.jsx)(h.y,{yAxisId:"left",dataKey:"patients",fill:"#8884d8",name:"Patients"}),(0,N.jsx)(m.N,{yAxisId:"right",type:"monotone",dataKey:"revenue",stroke:"#ff7300",name:"Revenue ($)"})]})})]})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6 mt-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("quickActions","Quick Actions")}),(0,N.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,N.jsxs)(l.N_,{to:"/financial/billing",className:"flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors",children:[(0,N.jsx)("i",{className:"fas fa-file-invoice text-blue-600 text-xl mr-3"}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e("createInvoice","Create Invoice")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("generateNewInvoice","Generate new invoice")})]})]}),(0,N.jsxs)(l.N_,{to:"/financial/payments",className:"flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors",children:[(0,N.jsx)("i",{className:"fas fa-credit-card text-green-600 text-xl mr-3"}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e("recordPayment","Record Payment")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("addNewPayment","Add new payment")})]})]}),(0,N.jsxs)(l.N_,{to:"/financial/insurance",className:"flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors",children:[(0,N.jsx)("i",{className:"fas fa-shield-alt text-purple-600 text-xl mr-3"}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e("submitClaim","Submit Claim")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("newInsuranceClaim","New insurance claim")})]})]}),(0,N.jsxs)(l.N_,{to:"/financial/reports",className:"flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors",children:[(0,N.jsx)("i",{className:"fas fa-chart-bar text-yellow-600 text-xl mr-3"}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e("viewReports","View Reports")}),(0,N.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("financialReports","Financial reports")})]})]})]})]}),!v&&(0,N.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6",children:[(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("revenueTrend","Revenue Trend")}),(0,N.jsx)(d.u,{width:"100%",height:300,children:(0,N.jsxs)(f.Q,{data:w,children:[(0,N.jsx)(x.d,{strokeDasharray:"3 3"}),(0,N.jsx)(p.W,{dataKey:"month"}),(0,N.jsx)(u.h,{}),(0,N.jsx)(n.m,{formatter:e=>["$".concat(e.toLocaleString()),""]}),(0,N.jsx)(i.s,{}),(0,N.jsx)(g.G,{type:"monotone",dataKey:"revenue",stackId:"1",stroke:"#8884d8",fill:"#8884d8",name:"Revenue"}),(0,N.jsx)(g.G,{type:"monotone",dataKey:"expenses",stackId:"1",stroke:"#82ca9d",fill:"#82ca9d",name:"Expenses"}),(0,N.jsx)(g.G,{type:"monotone",dataKey:"profit",stackId:"1",stroke:"#ffc658",fill:"#ffc658",name:"Profit"})]})})]}),(0,N.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("expenseBreakdown","Expense Breakdown")}),(0,N.jsx)(d.u,{width:"100%",height:300,children:(0,N.jsxs)(y.r,{children:[(0,N.jsx)(o.F,{data:A,cx:"50%",cy:"50%",labelLine:!1,label:e=>{let{name:t,percentage:a}=e;return"".concat(t," ").concat(a,"%")},outerRadius:80,fill:"#8884d8",dataKey:"amount",children:A.map((e,t)=>(0,N.jsx)(c.f,{fill:["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658"][t%7]},"cell-".concat(t)))}),(0,N.jsx)(n.m,{formatter:e=>["$".concat(e.toLocaleString()),""]})]})})]})]})]})}}}]);
//# sourceMappingURL=2866.5f65dba1.chunk.js.map