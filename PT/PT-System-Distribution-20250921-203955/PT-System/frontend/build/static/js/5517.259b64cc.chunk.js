"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[5517],{2593:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(2555),s=n(3986),l=(n(5043),n(579));const i=["children","variant","size","disabled","loading","className","type","onClick"],o=e=>{let{children:t,variant:n="primary",size:o="md",disabled:r=!1,loading:c=!1,className:d="",type:u="button",onClick:g}=e,b=(0,s.A)(e,i);const m={primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 focus:ring-blue-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500",warning:"bg-yellow-600 hover:bg-yellow-700 text-white focus:ring-yellow-500",ghost:"hover:bg-gray-100 text-gray-700 focus:ring-gray-500"},h={xs:"px-2.5 py-1.5 text-xs",sm:"px-3 py-2 text-sm",md:"px-4 py-2 text-sm",lg:"px-4 py-2 text-base",xl:"px-6 py-3 text-base"},y=["inline-flex items-center justify-center font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200",m[n]||m.primary,h[o]||h.md,r||c?"opacity-50 cursor-not-allowed":"",d].filter(Boolean).join(" ");return(0,l.jsxs)("button",(0,a.A)((0,a.A)({type:u,className:y,onClick:e=>{r||c?e.preventDefault():g&&g(e)},disabled:r||c},b),{},{children:[c&&(0,l.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,l.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,l.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),t]}))}},2903:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(3986),s=n(5043);const l=["title","titleId"];function i(e,t){let{title:n,titleId:i}=e,o=(0,a.A)(e,l);return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?s.createElement("title",{id:i},n):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const o=s.forwardRef(i)},3099:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(2555),s=n(3986),l=(n(5043),n(579));const i=["children","className","padding","shadow","border","rounded","background","hover"],o=e=>{let{children:t,className:n="",padding:o="p-6",shadow:r="shadow-sm",border:c="border border-gray-200",rounded:d="rounded-lg",background:u="bg-white",hover:g=""}=e,b=(0,s.A)(e,i);const m=[u,c,d,r,o,g,n].filter(Boolean).join(" ");return(0,l.jsx)("div",(0,a.A)((0,a.A)({className:m},b),{},{children:t}))}},3986:(e,t,n)=>{function a(e,t){if(null==e)return{};var n,a,s=function(e,t){if(null==e)return{};var n={};for(var a in e)if({}.hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;n[a]=e[a]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(a=0;a<l.length;a++)n=l[a],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}n.d(t,{A:()=>a})},4053:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(3986),s=n(5043);const l=["title","titleId"];function i(e,t){let{title:n,titleId:i}=e,o=(0,a.A)(e,l);return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?s.createElement("title",{id:i},n):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}const o=s.forwardRef(i)},5517:(e,t,n)=>{n.r(t),n.d(t,{default:()=>x});var a=n(2555),s=n(5043),l=n(4117),i=n(3768),o=n(4053),r=n(8153),c=n(2903),d=n(3986);const u=["title","titleId"];function g(e,t){let{title:n,titleId:a}=e,l=(0,d.A)(e,u);return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),n?s.createElement("title",{id:a},n):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const b=s.forwardRef(g);var m=n(3099),h=n(2593),y=n(6761),p=n(579);const x=()=>{const{t:e}=(0,l.Bd)(),[t,n]=(0,s.useState)(!1),[d,u]=(0,s.useState)({systemName:"PhysioFlow",systemDescription:"Advanced Physical Therapy Management System",defaultLanguage:"en",timezone:"UTC",dateFormat:"DD/MM/YYYY",timeFormat:"24h",sessionTimeout:30,passwordMinLength:8,requirePasswordComplexity:!0,enableTwoFactor:!1,maxLoginAttempts:5,enableEmailNotifications:!0,enableSMSNotifications:!1,enablePushNotifications:!0,notificationRetentionDays:30,maxFileUploadSize:10,maxUsersPerOrganization:100,dataRetentionMonths:24,backupFrequency:"daily",enableAIAnalytics:!0,enableAdvancedReporting:!0,enableMobileApp:!0,enableAPIAccess:!0});(0,s.useEffect)(()=>{g()},[]);const g=async()=>{try{n(!0)}catch(t){console.error("Failed to load settings:",t),i.oR.error(e("failedToLoadSettings","Failed to load system settings"))}finally{n(!1)}},x=(e,t,n)=>{u(e=>(0,a.A)((0,a.A)({},e),{},{[t]:n}))},f=e=>{let{title:t,icon:n,children:a}=e;return(0,p.jsxs)(m.A,{className:"p-6",children:[(0,p.jsxs)("div",{className:"flex items-center mb-4",children:[(0,p.jsx)(n,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,p.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t})]}),(0,p.jsx)("div",{className:"space-y-4",children:a})]})},v=e=>{let{label:t,type:n="text",value:a,onChange:s,options:l=null}=e;return(0,p.jsxs)("div",{children:[(0,p.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:t}),"select"===n?(0,p.jsx)("select",{value:a,onChange:e=>s(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:null===l||void 0===l?void 0:l.map(e=>(0,p.jsx)("option",{value:e.value,children:e.label},e.value))}):"checkbox"===n?(0,p.jsxs)("div",{className:"flex items-center",children:[(0,p.jsx)("input",{type:"checkbox",checked:a,onChange:e=>s(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,p.jsx)("span",{className:"ml-2 text-sm text-gray-600",children:"Enable this feature"})]}):(0,p.jsx)("input",{type:n,value:a,onChange:e=>s(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})};return t?(0,p.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,p.jsx)(y.Ay,{size:"lg"})}):(0,p.jsxs)("div",{className:"space-y-6",children:[(0,p.jsxs)("div",{className:"flex items-center justify-between",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("systemSettings","System Settings")}),(0,p.jsx)("p",{className:"text-gray-600 mt-1",children:e("systemSettingsDescription","Configure global system settings and preferences")})]}),(0,p.jsx)(h.A,{onClick:async()=>{try{n(!0),i.oR.success(e("settingsSaved","System settings saved successfully"))}catch(t){console.error("Failed to save settings:",t),i.oR.error(e("failedToSaveSettings","Failed to save system settings"))}finally{n(!1)}},disabled:t,children:e("saveSettings","Save Settings")})]}),(0,p.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,p.jsxs)(f,{title:e("generalSettings","General Settings"),icon:o.A,children:[(0,p.jsx)(v,{label:e("systemName","System Name"),value:d.systemName,onChange:e=>x(0,"systemName",e)}),(0,p.jsx)(v,{label:e("systemDescription","System Description"),value:d.systemDescription,onChange:e=>x(0,"systemDescription",e)}),(0,p.jsx)(v,{label:e("defaultLanguage","Default Language"),type:"select",value:d.defaultLanguage,onChange:e=>x(0,"defaultLanguage",e),options:[{value:"en",label:"English"},{value:"ar",label:"\u0627\u0644\u0639\u0631\u0628\u064a\u0629"},{value:"es",label:"Espa\xf1ol"},{value:"fr",label:"Fran\xe7ais"}]}),(0,p.jsx)(v,{label:e("timezone","Timezone"),type:"select",value:d.timezone,onChange:e=>x(0,"timezone",e),options:[{value:"UTC",label:"UTC"},{value:"America/New_York",label:"Eastern Time"},{value:"Europe/London",label:"London"},{value:"Asia/Riyadh",label:"Riyadh"}]})]}),(0,p.jsxs)(f,{title:e("securitySettings","Security Settings"),icon:r.A,children:[(0,p.jsx)(v,{label:e("sessionTimeout","Session Timeout (minutes)"),type:"number",value:d.sessionTimeout,onChange:e=>x(0,"sessionTimeout",parseInt(e))}),(0,p.jsx)(v,{label:e("passwordMinLength","Minimum Password Length"),type:"number",value:d.passwordMinLength,onChange:e=>x(0,"passwordMinLength",parseInt(e))}),(0,p.jsx)(v,{label:e("requirePasswordComplexity","Require Password Complexity"),type:"checkbox",value:d.requirePasswordComplexity,onChange:e=>x(0,"requirePasswordComplexity",e)}),(0,p.jsx)(v,{label:e("enableTwoFactor","Enable Two-Factor Authentication"),type:"checkbox",value:d.enableTwoFactor,onChange:e=>x(0,"enableTwoFactor",e)})]}),(0,p.jsxs)(f,{title:e("notificationSettings","Notification Settings"),icon:c.A,children:[(0,p.jsx)(v,{label:e("enableEmailNotifications","Enable Email Notifications"),type:"checkbox",value:d.enableEmailNotifications,onChange:e=>x(0,"enableEmailNotifications",e)}),(0,p.jsx)(v,{label:e("enableSMSNotifications","Enable SMS Notifications"),type:"checkbox",value:d.enableSMSNotifications,onChange:e=>x(0,"enableSMSNotifications",e)}),(0,p.jsx)(v,{label:e("enablePushNotifications","Enable Push Notifications"),type:"checkbox",value:d.enablePushNotifications,onChange:e=>x(0,"enablePushNotifications",e)}),(0,p.jsx)(v,{label:e("notificationRetentionDays","Notification Retention (days)"),type:"number",value:d.notificationRetentionDays,onChange:e=>x(0,"notificationRetentionDays",parseInt(e))})]}),(0,p.jsxs)(f,{title:e("featureSettings","Feature Settings"),icon:b,children:[(0,p.jsx)(v,{label:e("enableAIAnalytics","Enable AI Analytics"),type:"checkbox",value:d.enableAIAnalytics,onChange:e=>x(0,"enableAIAnalytics",e)}),(0,p.jsx)(v,{label:e("enableAdvancedReporting","Enable Advanced Reporting"),type:"checkbox",value:d.enableAdvancedReporting,onChange:e=>x(0,"enableAdvancedReporting",e)}),(0,p.jsx)(v,{label:e("enableMobileApp","Enable Mobile App Access"),type:"checkbox",value:d.enableMobileApp,onChange:e=>x(0,"enableMobileApp",e)}),(0,p.jsx)(v,{label:e("enableAPIAccess","Enable API Access"),type:"checkbox",value:d.enableAPIAccess,onChange:e=>x(0,"enableAPIAccess",e)})]})]})]})}},8153:(e,t,n)=>{n.d(t,{A:()=>o});var a=n(3986),s=n(5043);const l=["title","titleId"];function i(e,t){let{title:n,titleId:i}=e,o=(0,a.A)(e,l);return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":i},o),n?s.createElement("title",{id:i},n):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const o=s.forwardRef(i)}}]);
//# sourceMappingURL=5517.259b64cc.chunk.js.map