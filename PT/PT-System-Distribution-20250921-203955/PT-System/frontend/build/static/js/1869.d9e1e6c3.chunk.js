"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1869],{930:(e,t,s)=>{s.d(t,{A:()=>r});var a=s(2555);const r=new class{constructor(){this.baseURL="http://localhost:5001/api/v1",this.storagePrefix="physioflow_",this.initializeStorage()}getAuthToken(){return localStorage.getItem("token")||"demo-token"}async apiRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=this.getAuthToken(),r="".concat(this.baseURL).concat(e),i={headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}},n=(0,a.A)((0,a.A)((0,a.A)({},i),t),{},{headers:(0,a.A)((0,a.A)({},i.headers),t.headers)});try{const e=await fetch(r,n);if(!e.ok)throw new Error("HTTP error! status: ".concat(e.status));return await e.json()}catch(o){return console.warn("API request failed for ".concat(e,", using localStorage fallback:"),o.message),this.fallbackToLocalStorage(e,t)}}fallbackToLocalStorage(e,t){const s=t.method||"GET",a=t.body?JSON.parse(t.body):null;return e.includes("/bodymap")?this.handleBodyMapFallback(e,s,a):e.includes("/communication")?this.handleCommunicationFallback(e,s,a):e.includes("/exercise-programs")?this.handleExerciseFallback(e,s,a):e.includes("/ai-interactions")?this.handleAIFallback(e,s,a):{success:!1,error:"Endpoint not supported in fallback mode"}}initializeStorage(){this.getItem("initialized")||(this.setItem("initialized",!0),this.setItem("patients",this.getDefaultPatients()),this.setItem("bodyMapData",{}),this.setItem("communicationHistory",[]),this.setItem("exercisePrograms",[]),this.setItem("aiInteractions",[]))}getItem(e){try{const t=localStorage.getItem(this.storagePrefix+e);return t?JSON.parse(t):null}catch(t){return console.error("Error getting item from storage:",t),null}}setItem(e,t){try{return localStorage.setItem(this.storagePrefix+e,JSON.stringify(t)),!0}catch(s){return console.error("Error setting item in storage:",s),!1}}removeItem(e){try{return localStorage.removeItem(this.storagePrefix+e),!0}catch(t){return console.error("Error removing item from storage:",t),!1}}getDefaultPatients(){return[{id:"demo-patient-001",name:"Ahmed Mohammed",nameAr:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f",age:28,gender:"male",condition:"Cerebral Palsy",conditionAr:"\u0627\u0644\u0634\u0644\u0644 \u0627\u0644\u062f\u0645\u0627\u063a\u064a",phone:"+966501234567",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","whatsapp"],language:"en",quietHours:{start:"22:00",end:"08:00"}},createdAt:(new Date).toISOString()},{id:"demo-patient-002",name:"Sarah Johnson",nameAr:"\u0633\u0627\u0631\u0629 \u062c\u0648\u0646\u0633\u0648\u0646",age:35,gender:"female",condition:"Spinal Cord Injury",conditionAr:"\u0625\u0635\u0627\u0628\u0629 \u0627\u0644\u062d\u0628\u0644 \u0627\u0644\u0634\u0648\u0643\u064a",phone:"+**********",email:"<EMAIL>",communicationPreferences:{preferredChannels:["email","sms"],language:"en",quietHours:{start:"21:00",end:"07:00"}},createdAt:(new Date).toISOString()}]}async getPatients(){return new Promise(e=>{setTimeout(()=>{e(this.getItem("patients")||[])},100)})}async getPatient(e){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("patients")||[]).find(t=>t.id===e);t(s||null)},100)})}async saveBodyMapData(e,t){try{const s=await this.apiRequest("/bodymap",{method:"POST",body:JSON.stringify((0,a.A)({patientId:e},t))});return s.success?s.data:null}catch(s){return new Promise(s=>{setTimeout(()=>{const r=this.getItem("bodyMapData")||{};r[e]=(0,a.A)((0,a.A)({},t),{},{timestamp:(new Date).toISOString()}),this.setItem("bodyMapData",r),s(!0)},200)})}}async getBodyMapData(e){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("bodyMapData")||{};t(s[e]||null)},100)})}async sendMessage(e){try{const t=await this.apiRequest("/communication",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("communicationHistory")||[],r=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString(),status:"sent"});s.push(r),this.setItem("communicationHistory",s),t(r)},500)})}}async getCommunicationHistory(e){try{const t=await this.apiRequest("/communication/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("communicationHistory")||[]).filter(t=>t.patientId===e);t(s)},100)})}}async saveExerciseProgram(e){try{const t=await this.apiRequest("/exercise-programs",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("exercisePrograms")||[],r=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{createdAt:(new Date).toISOString()});s.push(r),this.setItem("exercisePrograms",s),t(r)},300)})}}async getExercisePrograms(e){try{const t=await this.apiRequest("/exercise-programs/".concat(e));return t.success?t.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=(this.getItem("exercisePrograms")||[]).filter(t=>t.patientId===e);t(s)},100)})}}async saveAIInteraction(e){try{const t=await this.apiRequest("/ai-interactions",{method:"POST",body:JSON.stringify(e)});return t.success?t.data:null}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("aiInteractions")||[],r=(0,a.A)((0,a.A)({id:Date.now().toString()},e),{},{timestamp:(new Date).toISOString()});s.push(r),this.setItem("aiInteractions",s),t(r)},100)})}}async getAIInteractions(e){try{const t=e?"/ai-interactions/".concat(e):"/ai-interactions",s=await this.apiRequest(t);return s.success?s.data:[]}catch(t){return new Promise(t=>{setTimeout(()=>{const s=this.getItem("aiInteractions")||[],a=e?s.filter(t=>t.patientId===e):s;t(a)},100)})}}async getAnalytics(){return new Promise(e=>{setTimeout(()=>{const t=this.getItem("aiInteractions")||[],s=this.getItem("communicationHistory")||[],r=this.getItem("exercisePrograms")||[],i=this.getItem("bodyMapData")||{},n={totalAIQueries:t.length,totalCommunications:s.length,totalExercisePrograms:r.length,totalBodyMapAssessments:Object.keys(i).length,recentActivity:[...t.slice(-5).map(e=>(0,a.A)({type:"ai"},e)),...s.slice(-5).map(e=>(0,a.A)({type:"communication"},e)),...r.slice(-5).map(e=>(0,a.A)({type:"exercise"},e))].sort((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)).slice(0,10)};e(n)},200)})}clearAllData(){["patients","bodyMapData","communicationHistory","exercisePrograms","aiInteractions"].forEach(e=>this.removeItem(e)),this.removeItem("initialized"),this.initializeStorage()}exportData(){return{patients:this.getItem("patients"),bodyMapData:this.getItem("bodyMapData"),communicationHistory:this.getItem("communicationHistory"),exercisePrograms:this.getItem("exercisePrograms"),aiInteractions:this.getItem("aiInteractions"),exportedAt:(new Date).toISOString()}}importData(e){try{return e.patients&&this.setItem("patients",e.patients),e.bodyMapData&&this.setItem("bodyMapData",e.bodyMapData),e.communicationHistory&&this.setItem("communicationHistory",e.communicationHistory),e.exercisePrograms&&this.setItem("exercisePrograms",e.exercisePrograms),e.aiInteractions&&this.setItem("aiInteractions",e.aiInteractions),!0}catch(t){return console.error("Error importing data:",t),!1}}delay(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;return new Promise(t=>setTimeout(t,e))}}},1869:(e,t,s)=>{s.r(t),s.d(t,{default:()=>m});var a=s(5043),r=s(7921),i=s(3216),n=s(7378),o=s(930),c=s(3768),l=s(579);const m=()=>{const{t:e,isRTL:t}=(0,r.o)(),{patientId:s}=(0,i.g)(),m=(0,i.Zp)(),[d,x]=(0,a.useState)(null),[g,h]=(0,a.useState)([]),[u,p]=(0,a.useState)(!0),[y,f]=(0,a.useState)("analytics"),[b,j]=(0,a.useState)({totalQueries:0,treatmentRecommendations:0,exerciseModifications:0,progressAnalyses:0});(0,a.useEffect)(()=>{I(),N()},[s]);const I=async()=>{if(s)try{const e=await o.A.getPatient(s);x(e)}catch(t){console.error("Error loading patient:",t),c.Ay.error(e("errorLoadingPatient","Error loading patient data"))}p(!1)},N=async()=>{try{const e=await o.A.getAIInteractions(s);h(e);const t={totalQueries:e.length,treatmentRecommendations:e.filter(e=>"treatment"===e.type).length,exerciseModifications:e.filter(e=>"exercise"===e.type).length,progressAnalyses:e.filter(e=>"progress"===e.type).length};j(t)}catch(e){console.error("Error loading AI interactions:",e)}},A=e=>{switch(e){case"treatment":return"fas fa-stethoscope";case"exercise":return"fas fa-dumbbell";case"progress":return"fas fa-chart-line";case"documentation":return"fas fa-file-medical";default:return"fas fa-brain"}},w=e=>{switch(e){case"treatment":return"text-blue-600";case"exercise":return"text-purple-600";case"progress":return"text-green-600";case"documentation":return"text-orange-600";default:return"text-gray-600"}},v=e=>{switch(e){case"gemini":return"fab fa-google";case"chatgpt":return"fas fa-robot";case"local":return"fas fa-server";default:return"fas fa-brain"}};return u?(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,l.jsxs)("div",{className:"ai-assistant-page min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsx)("div",{className:"py-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("button",{onClick:()=>m(-1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,l.jsx)("i",{className:"fas fa-arrow-left text-xl"})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[(0,l.jsx)("i",{className:"fas fa-brain mr-3 text-orange-600"}),e("aiAssistant","AI Assistant & Analytics")]}),d&&(0,l.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mt-1",children:[e("patient","Patient"),": ",t?d.nameAr:d.name,d.condition&&(0,l.jsx)("span",{className:"ml-2 text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded",children:t?d.conditionAr:d.condition})]})]})]}),(0,l.jsx)("div",{className:"flex items-center space-x-3",children:(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsxs)("span",{className:"bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,l.jsx)("i",{className:"fab fa-google mr-1"}),"Gemini AI"]}),(0,l.jsxs)("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,l.jsx)("i",{className:"fas fa-robot mr-1"}),"ChatGPT"]}),(0,l.jsxs)("span",{className:"bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium",children:[(0,l.jsx)("i",{className:"fas fa-server mr-1"}),"Local LLM"]})]})})]})})})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("nav",{className:"flex space-x-8",children:[(0,l.jsxs)("button",{onClick:()=>f("analytics"),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat("analytics"===y?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,l.jsx)("i",{className:"fas fa-chart-line mr-2"}),e("aiAnalytics","AI Analytics")]}),(0,l.jsxs)("button",{onClick:()=>f("interactions"),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat("interactions"===y?"border-orange-500 text-orange-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,l.jsx)("i",{className:"fas fa-comments mr-2"}),e("interactions","Interactions")," (",g.length,")"]})]})})}),(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-4 gap-8",children:[(0,l.jsx)("div",{className:"xl:col-span-3",children:(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:["analytics"===y&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:e("aiAnalyticsDashboard","AI Analytics Dashboard")}),(0,l.jsx)(n.R,{})]}),"interactions"===y&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:e("aiInteractionHistory","AI Interaction History")}),g.length>0?(0,l.jsx)("div",{className:"space-y-4",children:g.map((t,s)=>(0,l.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,l.jsx)("div",{className:"flex items-start justify-between mb-3",children:(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("i",{className:"".concat(A(t.type)," ").concat(w(t.type)," text-lg")}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("h3",{className:"font-medium text-gray-900 dark:text-white",children:["treatment"===t.type&&e("treatmentRecommendation","Treatment Recommendation"),"exercise"===t.type&&e("exerciseModification","Exercise Modification"),"progress"===t.type&&e("progressAnalysis","Progress Analysis"),"documentation"===t.type&&e("documentationHelp","Documentation Help"),!["treatment","exercise","progress","documentation"].includes(t.type)&&e("aiQuery","AI Query")]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,l.jsx)("i",{className:"".concat(v(t.provider)," text-xs")}),(0,l.jsx)("span",{children:t.provider||"gemini"}),(0,l.jsx)("span",{children:"\u2022"}),(0,l.jsx)("span",{children:new Date(t.timestamp).toLocaleString()})]})]})]})}),(0,l.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3",children:[(0,l.jsxs)("div",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[e("query","Query"),":"]}),(0,l.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.prompt||t.query||e("noQueryRecorded","No query recorded")})]}),(0,l.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3",children:[(0,l.jsxs)("div",{className:"text-sm font-medium text-blue-700 dark:text-blue-300 mb-1",children:[e("aiResponse","AI Response"),":"]}),(0,l.jsx)("div",{className:"text-sm text-blue-600 dark:text-blue-400",children:t.response||e("noResponseRecorded","No response recorded")})]})]},s))}):(0,l.jsxs)("div",{className:"text-center py-12",children:[(0,l.jsx)("i",{className:"fas fa-brain text-6xl text-gray-300 mb-4"}),(0,l.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noInteractions","No AI interactions yet")}),(0,l.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-4",children:e("startUsingAI","Start using the AI assistant to see interactions here")}),(0,l.jsx)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-md mx-auto",children:(0,l.jsxs)("p",{className:"text-blue-800 dark:text-blue-200 text-sm",children:[(0,l.jsx)("i",{className:"fas fa-info-circle mr-2"}),e("aiFloatingNote","Look for the brain icon in the bottom-right corner to access the AI Assistant")]})})]})]})]})}),(0,l.jsxs)("div",{className:"xl:col-span-1",children:[(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,l.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,l.jsx)("i",{className:"fas fa-chart-bar mr-2 text-gray-600"}),e("aiUsageStats","AI Usage Stats")]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("totalQueries","Total Queries")}),(0,l.jsx)("span",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:b.totalQueries})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("i",{className:"fas fa-stethoscope text-blue-600"}),(0,l.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("treatments","Treatments")})]}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.treatmentRecommendations})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("i",{className:"fas fa-dumbbell text-purple-600"}),(0,l.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("exercises","Exercises")})]}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.exerciseModifications})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("i",{className:"fas fa-chart-line text-green-600"}),(0,l.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("progress","Progress")})]}),(0,l.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:b.progressAnalyses})]})]})]})]}),(0,l.jsxs)("div",{className:"bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4 mt-6",children:[(0,l.jsxs)("h4",{className:"text-sm font-medium text-orange-900 dark:text-orange-200 mb-2",children:[(0,l.jsx)("i",{className:"fas fa-brain mr-2"}),e("aiAssistantActive","AI Assistant is Active")]}),(0,l.jsx)("p",{className:"text-sm text-orange-800 dark:text-orange-300",children:e("aiAssistantNote","The AI Assistant is available as a floating button in the bottom-right corner of the screen")})]})]})]})})]})}}}]);
//# sourceMappingURL=1869.d9e1e6c3.chunk.js.map