"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[7688],{7688:(e,s,a)=>{a.r(s),a.d(s,{default:()=>l});var r=a(5043),t=a(7921),d=a(579);const l=()=>{const{t:e,isRTL:s}=(0,t.o)(),[a,l]=(0,r.useState)("overview"),[i,n]=(0,r.useState)("month"),[c,o]=(0,r.useState)(null),[x,m]=(0,r.useState)({}),[g,h]=(0,r.useState)(!1),b={overview:{totalPatients:45,activePatients:38,completedSessions:156,averageProgress:72,monthlyGrowth:12,satisfactionRate:94},patientStats:[{condition:"Autism",count:18,percentage:40,color:"bg-blue-500"},{condition:"Cerebral Palsy",count:12,percentage:27,color:"bg-green-500"},{condition:"Down Syndrome",count:8,percentage:18,color:"bg-purple-500"},{condition:"Intellectual Disability",count:5,percentage:11,color:"bg-yellow-500"},{condition:"Other",count:2,percentage:4,color:"bg-gray-500"}],therapistPerformance:[{name:"Dr. Sarah Al-Rashid",patients:12,sessions:48,satisfaction:96,progress:78},{name:"Dr. Ahmed Al-Mansouri",patients:10,sessions:42,satisfaction:94,progress:75},{name:"Dr. Maryam Al-Zahra",patients:8,sessions:35,satisfaction:92,progress:70},{name:"Dr. Omar Al-Harbi",patients:6,sessions:28,satisfaction:90,progress:68},{name:"Dr. Layla Al-Dosari",patients:9,sessions:38,satisfaction:95,progress:73}],monthlyTrends:[{month:"Jan",sessions:120,patients:35,progress:68},{month:"Feb",sessions:135,patients:38,progress:70},{month:"Mar",sessions:142,patients:40,progress:72},{month:"Apr",sessions:156,patients:42,progress:74},{month:"May",sessions:148,patients:45,progress:72},{month:"Jun",sessions:162,patients:45,progress:75}]},p=[{id:1,name:"Patient Progress Report",nameAr:"\u062a\u0642\u0631\u064a\u0631 \u062a\u0642\u062f\u0645 \u0627\u0644\u0645\u0631\u0636\u0649",description:"Comprehensive progress analysis for all patients",descriptionAr:"\u062a\u062d\u0644\u064a\u0644 \u0634\u0627\u0645\u0644 \u0644\u0644\u062a\u0642\u062f\u0645 \u0644\u062c\u0645\u064a\u0639 \u0627\u0644\u0645\u0631\u0636\u0649",icon:"fas fa-chart-line",category:"progress",frequency:"Monthly"},{id:2,name:"Therapist Performance Report",nameAr:"\u062a\u0642\u0631\u064a\u0631 \u0623\u062f\u0627\u0621 \u0627\u0644\u0645\u0639\u0627\u0644\u062c\u064a\u0646",description:"Performance metrics and statistics for therapists",descriptionAr:"\u0645\u0642\u0627\u064a\u064a\u0633 \u0627\u0644\u0623\u062f\u0627\u0621 \u0648\u0627\u0644\u0625\u062d\u0635\u0627\u0626\u064a\u0627\u062a \u0644\u0644\u0645\u0639\u0627\u0644\u062c\u064a\u0646",icon:"fas fa-user-md",category:"performance",frequency:"Quarterly"},{id:3,name:"Special Needs Analysis",nameAr:"\u062a\u062d\u0644\u064a\u0644 \u0627\u0644\u0627\u062d\u062a\u064a\u0627\u062c\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629",description:"Detailed analysis of special needs patient outcomes",descriptionAr:"\u062a\u062d\u0644\u064a\u0644 \u0645\u0641\u0635\u0644 \u0644\u0646\u062a\u0627\u0626\u062c \u0645\u0631\u0636\u0649 \u0627\u0644\u0627\u062d\u062a\u064a\u0627\u062c\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629",icon:"fas fa-puzzle-piece",category:"special_needs",frequency:"Monthly"},{id:4,name:"Financial Summary",nameAr:"\u0627\u0644\u0645\u0644\u062e\u0635 \u0627\u0644\u0645\u0627\u0644\u064a",description:"Revenue and billing analysis",descriptionAr:"\u062a\u062d\u0644\u064a\u0644 \u0627\u0644\u0625\u064a\u0631\u0627\u062f\u0627\u062a \u0648\u0627\u0644\u0641\u0648\u0627\u062a\u064a\u0631",icon:"fas fa-dollar-sign",category:"financial",frequency:"Monthly"},{id:5,name:"Appointment Analytics",nameAr:"\u062a\u062d\u0644\u064a\u0644\u0627\u062a \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f",description:"Appointment scheduling and attendance patterns",descriptionAr:"\u0623\u0646\u0645\u0627\u0637 \u062c\u062f\u0648\u0644\u0629 \u0627\u0644\u0645\u0648\u0627\u0639\u064a\u062f \u0648\u0627\u0644\u062d\u0636\u0648\u0631",icon:"fas fa-calendar-alt",category:"appointments",frequency:"Weekly"},{id:6,name:"Treatment Effectiveness",nameAr:"\u0641\u0639\u0627\u0644\u064a\u0629 \u0627\u0644\u0639\u0644\u0627\u062c",description:"Analysis of treatment plan effectiveness",descriptionAr:"\u062a\u062d\u0644\u064a\u0644 \u0641\u0639\u0627\u0644\u064a\u0629 \u062e\u0637\u0637 \u0627\u0644\u0639\u0644\u0627\u062c",icon:"fas fa-heartbeat",category:"treatment",frequency:"Quarterly"}],y=[{id:"overview",label:e("overview","Overview"),icon:"fas fa-chart-pie"},{id:"templates",label:e("reportTemplates","Report Templates"),icon:"fas fa-file-alt"},{id:"analytics",label:e("analytics","Analytics"),icon:"fas fa-chart-bar"},{id:"custom",label:e("customReports","Custom Reports"),icon:"fas fa-tools"}];return(0,d.jsxs)("div",{className:"p-6 ".concat(s?"font-arabic":"font-english"),children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("reports","Reports")}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("reportsDesc","Analytics and reporting dashboard")})]}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("select",{value:i,onChange:e=>n(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"week",children:e("thisWeek","This Week")}),(0,d.jsx)("option",{value:"month",children:e("thisMonth","This Month")}),(0,d.jsx)("option",{value:"quarter",children:e("thisQuarter","This Quarter")}),(0,d.jsx)("option",{value:"year",children:e("thisYear","This Year")})]}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-download mr-2"}),e("exportData","Export Data")]})]})]}),(0,d.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,d.jsx)("nav",{className:"-mb-px flex space-x-8",children:y.map(e=>(0,d.jsxs)("button",{onClick:()=>l(e.id),className:"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(a===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,d.jsx)("i",{className:e.icon}),(0,d.jsx)("span",{children:e.label})]},e.id))})}),"overview"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-users text-blue-600 dark:text-blue-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalPatients","Total Patients")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.overview.totalPatients})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-user-check text-green-600 dark:text-green-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("activePatients","Active Patients")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.overview.activePatients})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-calendar-check text-purple-600 dark:text-purple-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("completedSessions","Completed Sessions")}),(0,d.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.overview.completedSessions})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-chart-line text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("averageProgress","Average Progress")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[b.overview.averageProgress,"%"]})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-indigo-100 dark:bg-indigo-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-arrow-up text-indigo-600 dark:text-indigo-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("monthlyGrowth","Monthly Growth")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["+",b.overview.monthlyGrowth,"%"]})]})]})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"p-3 bg-pink-100 dark:bg-pink-900/30 rounded-full",children:(0,d.jsx)("i",{className:"fas fa-heart text-pink-600 dark:text-pink-400 text-xl"})}),(0,d.jsxs)("div",{className:"ml-4",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("satisfactionRate","Satisfaction Rate")}),(0,d.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[b.overview.satisfactionRate,"%"]})]})]})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("patientDistribution","Patient Distribution by Condition")}),(0,d.jsx)("div",{className:"space-y-4",children:b.patientStats.map((e,s)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:"w-4 h-4 rounded-full ".concat(e.color," mr-3")}),(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.condition})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.count}),(0,d.jsx)("div",{className:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(e.percentage,"%")}})}),(0,d.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400 w-8",children:[e.percentage,"%"]})]})]},s))})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("therapistPerformance","Therapist Performance")}),(0,d.jsx)("div",{className:"space-y-4",children:b.therapistPerformance.slice(0,3).map((s,a)=>(0,d.jsxs)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-3 last:border-b-0",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:s.name}),(0,d.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[s.patients," patients"]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-2 text-xs",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("sessions","Sessions"),": "]}),(0,d.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:s.sessions})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("satisfaction","Satisfaction"),": "]}),(0,d.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:[s.satisfaction,"%"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("progress","Progress"),": "]}),(0,d.jsxs)("span",{className:"font-medium text-gray-900 dark:text-white",children:[s.progress,"%"]})]})]})]},a))})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("monthlyTrends","Monthly Trends")}),(0,d.jsx)("div",{className:"grid grid-cols-6 gap-4",children:b.monthlyTrends.map((s,a)=>(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:s.month}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e("sessions","Sessions"),": ",s.sessions]}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e("patients","Patients"),": ",s.patients]}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e("progress","Progress"),": ",s.progress,"%"]})]})]},a))})]})]}),"templates"===a&&(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(a=>(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-4",children:(0,d.jsx)("i",{className:"".concat(a.icon," text-blue-600 dark:text-blue-400 text-xl")})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s?a.nameAr:a.name}),(0,d.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:a.frequency})]})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:s?a.descriptionAr:a.description}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("button",{onClick:()=>(s=>{var a;o(s),alert("".concat(e("generating","Generating")," ").concat(null===(a=p.find(e=>e.id===s))||void 0===a?void 0:a.name,"..."))})(a.id),className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:e("generate","Generate")}),(0,d.jsx)("button",{className:"px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm",children:(0,d.jsx)("i",{className:"fas fa-cog"})})]})]},a.id))}),"analytics"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("patientDemographics","Patient Demographics")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("conditionDistribution","Condition Distribution")}),(0,d.jsx)("div",{className:"space-y-3",children:b.patientStats.map((e,s)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-4 h-4 rounded ".concat(e.color)}),(0,d.jsx)("span",{className:"text-gray-900 dark:text-white",children:e.condition})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:e.count}),(0,d.jsx)("div",{className:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"h-2 rounded-full ".concat(e.color),style:{width:"".concat(e.percentage,"%")}})}),(0,d.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400 w-8",children:[e.percentage,"%"]})]})]},s))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-4",children:e("ageDistribution","Age Distribution")}),(0,d.jsx)("div",{className:"space-y-3",children:[{range:"0-5 years",count:12,percentage:27},{range:"6-10 years",count:18,percentage:40},{range:"11-15 years",count:10,percentage:22},{range:"16+ years",count:5,percentage:11}].map((e,s)=>(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"text-gray-900 dark:text-white",children:e.range}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:e.count}),(0,d.jsx)("div",{className:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"h-2 rounded-full bg-blue-500",style:{width:"".concat(e.percentage,"%")}})}),(0,d.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400 w-8",children:[e.percentage,"%"]})]})]},s))})]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("treatmentOutcomes","Treatment Outcomes")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"relative w-32 h-32 mx-auto mb-4",children:[(0,d.jsxs)("svg",{className:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,d.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-gray-200 dark:text-gray-700"}),(0,d.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeDasharray:"85, 100",className:"text-green-500"})]}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"85%"})})]}),(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e("successRate","Success Rate")}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("goalAchievement","Goal Achievement")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"relative w-32 h-32 mx-auto mb-4",children:[(0,d.jsxs)("svg",{className:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,d.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-gray-200 dark:text-gray-700"}),(0,d.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeDasharray:"72, 100",className:"text-blue-500"})]}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"72%"})})]}),(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e("avgProgress","Avg Progress")}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("overallImprovement","Overall Improvement")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"relative w-32 h-32 mx-auto mb-4",children:[(0,d.jsxs)("svg",{className:"w-32 h-32 transform -rotate-90",viewBox:"0 0 36 36",children:[(0,d.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"currentColor",strokeWidth:"2",className:"text-gray-200 dark:text-gray-700"}),(0,d.jsx)("path",{d:"M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeDasharray:"94, 100",className:"text-purple-500"})]}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"94%"})})]}),(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e("satisfaction","Satisfaction")}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e("familySatisfaction","Family Satisfaction")})]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("therapistPerformance","Therapist Performance")}),(0,d.jsx)("div",{className:"overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{children:(0,d.jsxs)("tr",{className:"border-b border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("th",{className:"text-left py-3 px-4 font-medium text-gray-900 dark:text-white",children:e("therapist","Therapist")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 font-medium text-gray-900 dark:text-white",children:e("patients","Patients")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 font-medium text-gray-900 dark:text-white",children:e("sessions","Sessions")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 font-medium text-gray-900 dark:text-white",children:e("satisfaction","Satisfaction")}),(0,d.jsx)("th",{className:"text-center py-3 px-4 font-medium text-gray-900 dark:text-white",children:e("avgProgress","Avg Progress")})]})}),(0,d.jsx)("tbody",{children:b.therapistPerformance.map((e,s)=>(0,d.jsxs)("tr",{className:"border-b border-gray-100 dark:border-gray-700",children:[(0,d.jsx)("td",{className:"py-3 px-4",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center",children:(0,d.jsx)("i",{className:"fas fa-user-md text-blue-600 dark:text-blue-400 text-sm"})}),(0,d.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:e.name})]})}),(0,d.jsx)("td",{className:"text-center py-3 px-4 text-gray-600 dark:text-gray-400",children:e.patients}),(0,d.jsx)("td",{className:"text-center py-3 px-4 text-gray-600 dark:text-gray-400",children:e.sessions}),(0,d.jsx)("td",{className:"text-center py-3 px-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,d.jsxs)("span",{className:"text-gray-900 dark:text-white",children:[e.satisfaction,"%"]}),(0,d.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"h-2 rounded-full bg-green-500",style:{width:"".concat(e.satisfaction,"%")}})})]})}),(0,d.jsx)("td",{className:"text-center py-3 px-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,d.jsxs)("span",{className:"text-gray-900 dark:text-white",children:[e.progress,"%"]}),(0,d.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,d.jsx)("div",{className:"h-2 rounded-full bg-blue-500",style:{width:"".concat(e.progress,"%")}})})]})})]},s))})]})})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("monthlyTrends","Monthly Trends")}),(0,d.jsx)("div",{className:"grid grid-cols-6 gap-4",children:b.monthlyTrends.map((s,a)=>(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white mb-4",children:s.month}),(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:e("sessions","Sessions")}),(0,d.jsxs)("div",{className:"relative h-20 bg-gray-200 dark:bg-gray-700 rounded",children:[(0,d.jsx)("div",{className:"absolute bottom-0 w-full bg-blue-500 rounded",style:{height:"".concat(s.sessions/200*100,"%")}}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-end justify-center pb-1",children:(0,d.jsx)("span",{className:"text-xs text-white font-medium",children:s.sessions})})]})]}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:[e("progress","Progress"),": ",s.progress,"%"]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1 mt-1",children:(0,d.jsx)("div",{className:"h-1 rounded-full bg-green-500",style:{width:"".concat(s.progress,"%")}})})]},a))})]})]}),"custom"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("customReportBuilder","Custom Report Builder")}),(0,d.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),e("saveReport","Save Report")]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-1",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-4",children:e("dataSources","Data Sources")}),(0,d.jsx)("div",{className:"space-y-2",children:[{id:"patients",label:"Patients",icon:"fas fa-users",fields:["name","age","condition","status"]},{id:"treatments",label:"Treatments",icon:"fas fa-heartbeat",fields:["type","duration","progress","therapist"]},{id:"sessions",label:"Sessions",icon:"fas fa-calendar",fields:["date","duration","activities","notes"]},{id:"assessments",label:"Assessments",icon:"fas fa-clipboard-check",fields:["type","score","date","notes"]},{id:"goals",label:"Goals",icon:"fas fa-bullseye",fields:["title","progress","target_date","status"]}].map(e=>(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,d.jsx)("i",{className:"".concat(e.icon," text-blue-600 dark:text-blue-400")}),(0,d.jsx)("span",{className:"font-medium text-gray-900 dark:text-white",children:e.label})]}),(0,d.jsx)("div",{className:"space-y-1",children:e.fields.map(e=>(0,d.jsx)("div",{draggable:!0,className:"text-xs text-gray-600 dark:text-gray-400 p-1 bg-gray-50 dark:bg-gray-700 rounded cursor-move hover:bg-gray-100 dark:hover:bg-gray-600",children:e.replace("_"," ")},e))})]},e.id))})]}),(0,d.jsxs)("div",{className:"lg:col-span-2",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-4",children:e("reportCanvas","Report Canvas")}),(0,d.jsx)("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 min-h-96",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-2",children:e("reportHeader","Report Header")}),(0,d.jsx)("input",{type:"text",placeholder:e("reportTitle","Report Title"),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white mb-2"}),(0,d.jsx)("textarea",{placeholder:e("reportDescription","Report Description"),rows:"2",className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"})]}),(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white",children:e("patientSummary","Patient Summary")}),(0,d.jsx)("button",{className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200",children:(0,d.jsx)("i",{className:"fas fa-trash text-sm"})})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,d.jsx)("div",{className:"p-2 bg-blue-50 dark:bg-blue-900/20 rounded",children:(0,d.jsx)("span",{className:"text-blue-800 dark:text-blue-300",children:"Patient Name"})}),(0,d.jsx)("div",{className:"p-2 bg-blue-50 dark:bg-blue-900/20 rounded",children:(0,d.jsx)("span",{className:"text-blue-800 dark:text-blue-300",children:"Condition"})}),(0,d.jsx)("div",{className:"p-2 bg-blue-50 dark:bg-blue-900/20 rounded",children:(0,d.jsx)("span",{className:"text-blue-800 dark:text-blue-300",children:"Age"})}),(0,d.jsx)("div",{className:"p-2 bg-blue-50 dark:bg-blue-900/20 rounded",children:(0,d.jsx)("span",{className:"text-blue-800 dark:text-blue-300",children:"Status"})})]})]}),(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white",children:e("treatmentProgress","Treatment Progress")}),(0,d.jsx)("button",{className:"text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200",children:(0,d.jsx)("i",{className:"fas fa-trash text-sm"})})]}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsx)("div",{className:"p-2 bg-green-50 dark:bg-green-900/20 rounded",children:(0,d.jsx)("span",{className:"text-green-800 dark:text-green-300",children:"Progress Percentage"})}),(0,d.jsx)("div",{className:"p-2 bg-green-50 dark:bg-green-900/20 rounded",children:(0,d.jsx)("span",{className:"text-green-800 dark:text-green-300",children:"Sessions Completed"})}),(0,d.jsx)("div",{className:"p-2 bg-green-50 dark:bg-green-900/20 rounded",children:(0,d.jsx)("span",{className:"text-green-800 dark:text-green-300",children:"Goals Achieved"})})]})]}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center",children:[(0,d.jsx)("i",{className:"fas fa-plus text-blue-400 text-2xl mb-2"}),(0,d.jsx)("p",{className:"text-blue-600 dark:text-blue-400",children:e("dropFieldsHere","Drop fields here to add to report")})]})]})})]}),(0,d.jsxs)("div",{className:"lg:col-span-1",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-4",children:e("reportSettings","Report Settings")}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-3",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-3",children:e("filters","Filters")}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm text-gray-700 dark:text-gray-300 mb-1",children:e("dateRange","Date Range")}),(0,d.jsxs)("select",{className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"week",children:e("lastWeek","Last Week")}),(0,d.jsx)("option",{value:"month",children:e("lastMonth","Last Month")}),(0,d.jsx)("option",{value:"quarter",children:e("lastQuarter","Last Quarter")}),(0,d.jsx)("option",{value:"year",children:e("lastYear","Last Year")})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm text-gray-700 dark:text-gray-300 mb-1",children:e("condition","Condition")}),(0,d.jsxs)("select",{className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"all",children:e("allConditions","All Conditions")}),(0,d.jsx)("option",{value:"autism",children:"Autism"}),(0,d.jsx)("option",{value:"cerebral_palsy",children:"Cerebral Palsy"}),(0,d.jsx)("option",{value:"down_syndrome",children:"Down Syndrome"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm text-gray-700 dark:text-gray-300 mb-1",children:e("therapist","Therapist")}),(0,d.jsxs)("select",{className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,d.jsx)("option",{value:"all",children:e("allTherapists","All Therapists")}),(0,d.jsx)("option",{value:"sarah",children:"Dr. Sarah Ahmed"}),(0,d.jsx)("option",{value:"ahmed",children:"Dr. Ahmed Al-Mansouri"}),(0,d.jsx)("option",{value:"maryam",children:"Dr. Maryam Al-Zahra"})]})]})]})]}),(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-3",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-3",children:e("chartTypes","Chart Types")}),(0,d.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{type:"bar",icon:"fas fa-chart-bar",label:"Bar Chart"},{type:"line",icon:"fas fa-chart-line",label:"Line Chart"},{type:"pie",icon:"fas fa-chart-pie",label:"Pie Chart"},{type:"table",icon:"fas fa-table",label:"Table"}].map(e=>(0,d.jsxs)("button",{className:"p-2 border border-gray-200 dark:border-gray-600 rounded text-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,d.jsx)("i",{className:"".concat(e.icon," text-gray-600 dark:text-gray-400 mb-1")}),(0,d.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.label})]},e.type))})]}),(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-3",children:[(0,d.jsx)("h5",{className:"font-medium text-gray-900 dark:text-white mb-3",children:e("exportOptions","Export Options")}),(0,d.jsx)("div",{className:"space-y-2",children:["PDF","Excel","CSV","Word"].map(e=>(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",className:"rounded",defaultChecked:"PDF"===e}),(0,d.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:e})]},e))})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm",children:[(0,d.jsx)("i",{className:"fas fa-eye mr-2"}),e("previewReport","Preview Report")]}),(0,d.jsxs)("button",{className:"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm",children:[(0,d.jsx)("i",{className:"fas fa-download mr-2"}),e("generateReport","Generate Report")]})]})]})]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("savedCustomReports","Saved Custom Reports")}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[{name:"Monthly Progress Summary",nameAr:"\u0645\u0644\u062e\u0635 \u0627\u0644\u062a\u0642\u062f\u0645 \u0627\u0644\u0634\u0647\u0631\u064a",created:"2024-02-01",lastRun:"2024-02-10",frequency:"Monthly"},{name:"Autism Treatment Analysis",nameAr:"\u062a\u062d\u0644\u064a\u0644 \u0639\u0644\u0627\u062c \u0627\u0644\u062a\u0648\u062d\u062f",created:"2024-01-15",lastRun:"2024-02-05",frequency:"Weekly"},{name:"Therapist Performance Review",nameAr:"\u0645\u0631\u0627\u062c\u0639\u0629 \u0623\u062f\u0627\u0621 \u0627\u0644\u0645\u0639\u0627\u0644\u062c\u064a\u0646",created:"2024-01-20",lastRun:"2024-02-08",frequency:"Quarterly"}].map((a,r)=>(0,d.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:s?a.nameAr:a.name}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400 space-y-1 mb-3",children:[(0,d.jsxs)("div",{children:[e("created","Created"),": ",new Date(a.created).toLocaleDateString()]}),(0,d.jsxs)("div",{children:[e("lastRun","Last Run"),": ",new Date(a.lastRun).toLocaleDateString()]}),(0,d.jsxs)("div",{children:[e("frequency","Frequency"),": ",a.frequency]})]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("button",{className:"flex-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 transition-colors",children:e("run","Run")}),(0,d.jsx)("button",{className:"px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-edit"})}),(0,d.jsx)("button",{className:"px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded text-sm hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-trash"})})]})]},r))})]})]})]})}}}]);
//# sourceMappingURL=7688.36b69eb0.chunk.js.map