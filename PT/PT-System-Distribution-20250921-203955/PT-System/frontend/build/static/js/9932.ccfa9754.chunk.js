"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[9932],{9932:(e,r,a)=>{a.r(r),a.d(r,{default:()=>l});var t=a(2555),i=a(5043),n=a(7921),s=a(4528),o=a(3216),d=a(579);const l=()=>{const{t:e,isRTL:r}=(0,n.o)(),{user:a}=(0,s.A)(),{patientId:l}=(0,o.g)(),c=(0,o.Zp)(),[m,p]=(0,i.useState)({firstName:"",lastName:"",dateOfBirth:"",gender:"",maritalStatus:"",occupation:"",phoneNumber:"",email:"",address:"",city:"",zipCode:"",emergencyContactName:"",emergencyContactRelation:"",emergencyContactPhone:"",insuranceProvider:"",policyNumber:"",groupNumber:"",subscriberName:"",primaryPhysician:"",referringPhysician:"",chiefComplaint:"",currentMedications:"",allergies:"",previousSurgeries:"",medicalConditions:[],painLevel:0,painLocation:"",symptomDuration:"",symptomDescription:"",functionalLimitations:"",treatmentConsent:!1,privacyConsent:!1,financialConsent:!1,communicationConsent:!1,specialNeeds:"",accommodations:"",preferredLanguage:"English",interpreterNeeded:!1}),[b,g]=(0,i.useState)(!1),[u,h]=(0,i.useState)({}),[y,x]=(0,i.useState)("personal");e("male","Male"),e("female","Female"),e("other","Other"),e("single","Single"),e("married","Married"),e("divorced","Divorced"),e("widowed","Widowed");(0,i.useEffect)(()=>{l&&f()},[l]);const f=async()=>{try{g(!0);const e=await fetch("/api/patients/".concat(l));if(e.ok){const r=await e.json();p(e=>(0,t.A)((0,t.A)({},e),{},{firstName:r.firstName||"",lastName:r.lastName||"",dateOfBirth:r.dateOfBirth||"",phoneNumber:r.phoneNumber||"",email:r.email||""}))}}catch(e){console.error("Error loading patient data:",e)}finally{g(!1)}},N=(e,r)=>{p(a=>(0,t.A)((0,t.A)({},a),{},{[e]:r})),u[e]&&h(r=>(0,t.A)((0,t.A)({},r),{},{[e]:null}))};return b?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,d.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:(0,d.jsx)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("patientIntakeForm","Patient Intake Form")}),(0,d.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e("intakeFormDescription","Complete patient information and medical history")})]}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsxs)("span",{className:"px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 text-sm rounded-full",children:[(0,d.jsx)("i",{className:"fas fa-puzzle-piece mr-1"}),e("specialNeedsSupported","Special Needs Supported")]})})]})})}),(0,d.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 mb-6",children:(0,d.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,d.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:[{id:"personal",label:e("personalInformation","Personal Information")},{id:"medical",label:e("medicalHistory","Medical History")},{id:"symptoms",label:e("currentSymptoms","Current Symptoms")},{id:"consent",label:e("consentForms","Consent Forms")}].map(e=>(0,d.jsx)("button",{onClick:()=>x(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(y===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:e.label},e.id))})})}),(0,d.jsxs)("form",{onSubmit:async r=>{if(r.preventDefault(),(()=>{const r={};return m.firstName.trim()||(r.firstName=e("firstNameRequired","First name is required")),m.lastName.trim()||(r.lastName=e("lastNameRequired","Last name is required")),m.dateOfBirth||(r.dateOfBirth=e("dateOfBirthRequired","Date of birth is required")),m.gender||(r.gender=e("genderRequired","Gender is required")),m.phoneNumber.trim()||(r.phoneNumber=e("phoneRequired","Phone number is required")),m.chiefComplaint.trim()||(r.chiefComplaint=e("chiefComplaintRequired","Chief complaint is required")),m.treatmentConsent||(r.treatmentConsent=e("treatmentConsentRequired","Treatment consent is required")),m.privacyConsent||(r.privacyConsent=e("privacyConsentRequired","Privacy consent is required")),m.email&&!/\S+@\S+\.\S+/.test(m.email)&&(r.email=e("invalidEmail","Invalid email format")),m.phoneNumber&&!/^\+?[\d\s\-\(\)]+$/.test(m.phoneNumber)&&(r.phoneNumber=e("invalidPhone","Invalid phone number format")),h(r),0===Object.keys(r).length})())try{g(!0);const r=(0,t.A)((0,t.A)({},m),{},{submittedBy:a.id,submittedAt:(new Date).toISOString(),patientId:l});if(!(await fetch("/api/v1/patient-intake/public",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok)throw new Error("Failed to save intake form");alert(e("intakeFormSaved","Patient intake form saved successfully!")),c(l?"/patients/".concat(l):"/patients")}catch(i){console.error("Error saving intake form:",i),alert(e("errorSaving","Error saving intake form. Please try again."))}finally{g(!1)}},className:"space-y-6",children:["personal"===y&&(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:[(0,d.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:e("personalInformation","Personal Information")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("firstName","First Name")," *"]}),(0,d.jsx)("input",{type:"text",value:m.firstName,onChange:e=>N("firstName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.firstName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.firstName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.firstName})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[e("lastName","Last Name")," *"]}),(0,d.jsx)("input",{type:"text",value:m.lastName,onChange:e=>N("lastName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white ".concat(u.lastName?"border-red-500":"border-gray-300 dark:border-gray-600"),required:!0}),u.lastName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.lastName})]})]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("button",{type:"button",onClick:()=>c(-1),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700",children:e("cancel","Cancel")}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsxs)("button",{type:"button",onClick:async()=>{try{g(!0);const r=(0,t.A)((0,t.A)({},m),{},{generatedAt:(new Date).toISOString(),generatedBy:a.name||a.email,patientId:l}),i=await fetch("/api/v1/patient-intake/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(r)});if(!i.ok)throw new Error("HTTP error! status: ".concat(i.status));{const r=await i.blob(),a=window.URL.createObjectURL(r),t=document.createElement("a");t.href=a,t.download="patient-intake-".concat(m.firstName,"-").concat(m.lastName,"-").concat((new Date).toISOString().split("T")[0],".pdf"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(a),document.body.removeChild(t),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(r){console.error("Error generating PDF:",r),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{g(!1)}},disabled:b,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-file-pdf mr-2"}),b?e("generating","Generating..."):e("generatePDF","Generate PDF")]}),(0,d.jsx)("button",{type:"submit",disabled:b,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?e("saving","Saving..."):e("saveIntakeForm","Save Intake Form")})]})]})]})]})}}}]);
//# sourceMappingURL=9932.ccfa9754.chunk.js.map