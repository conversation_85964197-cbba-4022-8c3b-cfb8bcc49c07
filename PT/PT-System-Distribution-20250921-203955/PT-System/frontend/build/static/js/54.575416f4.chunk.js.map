{"version": 3, "file": "static/js/54.575416f4.chunk.js", "mappings": "mNAKA,MAwGA,EAxGyBA,KAAO,IAADC,EAC7B,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACfC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACVC,IAAgBC,EAAAA,EAAAA,OAChBC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,OACxCC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,OAE/CG,EAAAA,EAAAA,WAAU,KAAO,IAADC,EAAAC,EAEd,GAAkB,QAAdD,EAAAV,EAASY,aAAK,IAAAF,GAAdA,EAAgBG,SAAyB,QAAlBF,EAAIX,EAASY,aAAK,IAAAD,GAAdA,EAAgBG,mBAAoB,CACjE,MAAMD,EAAUb,EAASY,MAAMC,QAC/BL,EAAeK,GAEfR,EAAe,CACbU,UAAWF,EAAQG,KAAOH,EAAQI,GAClCC,YAAaL,EAAQM,MAAI,GAAAC,OAAOP,EAAQQ,UAAS,KAAAD,OAAIP,EAAQS,UAC7DC,IAAKV,EAAQU,KAAO,GACpBC,OAAQX,EAAQW,QAAU,GAC1BC,gBAAgB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACpDC,SAAU,eACVC,SAAU,aAEd,KAAO,CAEL,MAAMf,EAAYb,EAAa6B,IAAI,aAC7Bb,EAAchB,EAAa6B,IAAI,eAC/BR,EAAMrB,EAAa6B,IAAI,OACvBP,EAAStB,EAAa6B,IAAI,UAE5BhB,GAAaG,GACfb,EAAe,CACbU,UAAWA,EACXG,YAAac,mBAAmBd,GAChCK,IAAKA,EAAMU,SAASV,GAAO,GAC3BC,OAAQA,GAAU,GAClBC,gBAAgB,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACpDC,SAAU,eACVC,SAAU,aAGhB,GACC,CAAC5B,EAAcF,EAASY,QAE3B,MAMMsB,EAAeA,KACnBpC,EAAS,aAADsB,QAAyB,OAAXhB,QAAW,IAAXA,OAAW,EAAXA,EAAaW,YAAa,MAGlD,OACEoB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,UACEC,QAASL,EACTE,UAAU,4EAA2EC,UAErFC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,gBAAAhB,OAAkBxB,EAAQ,QAAU,OAAM,0CAExDuC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D1C,EAAE,eAAgB,oBAET,OAAXS,QAAW,IAAXA,OAAW,EAAXA,EAAac,eACZiB,EAAAA,EAAAA,MAAA,KAAGC,UAAU,wCAAuCC,SAAA,CACjD1C,EAAE,UAAW,WAAW,KAAGS,EAAYc,sBAMhDoB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASL,EACTE,UAAU,yIAAwIC,SAAA,EAElJC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZzC,EAAE,SAAU,qBAOrB2C,EAAAA,EAAAA,KAAA,OAAKF,UAAU,0FAAyFC,UACtGC,EAAAA,EAAAA,KAACE,EAAAA,EAAqB,CACpBjC,YAAaA,EACbO,mBAAkC,QAAhBpB,EAAEM,EAASY,aAAK,IAAAlB,OAAA,EAAdA,EAAgBoB,mBACpCV,YAAaA,EACbqC,SApDkBC,IACxBC,QAAQC,IAAI,2BAA4BF,GAExC5C,EAAS,aAADsB,QAAyB,OAAXhB,QAAW,IAAXA,OAAW,EAAXA,EAAaW,YAAa,MAkD1C8B,SAAUX,S", "sources": ["pages/Forms/PTAssessmentPage.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useNavigate, useSearchParams, useLocation } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport PTAdultAssessmentForm from '../../components/Assessment/PTAdultAssessmentForm';\n\nconst PTAssessmentPage = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const [initialData, setInitialData] = useState(null);\n  const [patientData, setPatientData] = useState(null);\n\n  useEffect(() => {\n    // Check if patient data was passed via React Router state (from patient profile)\n    if (location.state?.patient && location.state?.fromPatientProfile) {\n      const patient = location.state.patient;\n      setPatientData(patient);\n\n      setInitialData({\n        patientId: patient._id || patient.id,\n        patientName: patient.name || `${patient.firstName} ${patient.lastName}`,\n        age: patient.age || '',\n        gender: patient.gender || '',\n        assessmentDate: new Date().toISOString().split('T')[0],\n        assessor: 'Current User', // This should come from auth context\n        facility: 'PT Clinic'\n      });\n    } else {\n      // Fallback to URL parameters for backward compatibility\n      const patientId = searchParams.get('patientId');\n      const patientName = searchParams.get('patientName');\n      const age = searchParams.get('age');\n      const gender = searchParams.get('gender');\n\n      if (patientId && patientName) {\n        setInitialData({\n          patientId: patientId,\n          patientName: decodeURIComponent(patientName),\n          age: age ? parseInt(age) : '',\n          gender: gender || '',\n          assessmentDate: new Date().toISOString().split('T')[0],\n          assessor: 'Current User', // This should come from auth context\n          facility: 'PT Clinic'\n        });\n      }\n    }\n  }, [searchParams, location.state]);\n\n  const handleFormSubmit = (formData) => {\n    console.log('PT Assessment submitted:', formData);\n    // Here you would typically save to backend\n    navigate(`/patients/${initialData?.patientId || ''}`);\n  };\n\n  const handleCancel = () => {\n    navigate(`/patients/${initialData?.patientId || ''}`);\n  };\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={handleCancel}\n              className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\n            >\n              <i className={`fas fa-arrow-${isRTL ? 'right' : 'left'} text-gray-600 dark:text-gray-400`}></i>\n            </button>\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n                {t('ptAssessment', 'PT Assessment')}\n              </h1>\n              {initialData?.patientName && (\n                <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                  {t('patient', 'Patient')}: {initialData.patientName}\n                </p>\n              )}\n            </div>\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={handleCancel}\n              className=\"px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              <i className=\"fas fa-times mr-2\"></i>\n              {t('cancel', 'Cancel')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <PTAdultAssessmentForm\n          patientData={patientData}\n          fromPatientProfile={location.state?.fromPatientProfile}\n          initialData={initialData}\n          onSubmit={handleFormSubmit}\n          onCancel={handleCancel}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default PTAssessmentPage;\n"], "names": ["PTAssessmentPage", "_location$state3", "t", "isRTL", "useLanguage", "navigate", "useNavigate", "location", "useLocation", "searchParams", "useSearchParams", "initialData", "setInitialData", "useState", "patientData", "setPatientData", "useEffect", "_location$state", "_location$state2", "state", "patient", "fromPatientProfile", "patientId", "_id", "id", "patientName", "name", "concat", "firstName", "lastName", "age", "gender", "assessmentDate", "Date", "toISOString", "split", "assessor", "facility", "get", "decodeURIComponent", "parseInt", "handleCancel", "_jsxs", "className", "children", "_jsx", "onClick", "PTAdultAssessmentForm", "onSubmit", "formData", "console", "log", "onCancel"], "sourceRoot": ""}