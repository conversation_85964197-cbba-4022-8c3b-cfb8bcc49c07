"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8086],{8086:(e,r,a)=>{a.r(r),a.d(r,{default:()=>m});var t=a(2555),s=a(5043),l=a(7921),d=a(4528),i=a(918),n=a(3216),o=a(3768),c=a(579);const m=()=>{const{t:e,isRTL:r}=(0,l.o)(),{user:a}=(0,d.A)(),{createSubmission:m,updateSubmission:g,generatePDF:x,loading:b}=(0,i.h7)(),{patientId:u}=(0,n.g)(),p=(0,n.Zp)(),[f,h]=(0,s.useState)({patientName:"",patientId:u||"",planDate:(new Date).toISOString().split("T")[0],therapistName:(null===a||void 0===a?void 0:a.name)||"",primaryDiagnosis:"",secondaryDiagnosis:"",icdCodes:"",functionalLimitations:"",precautions:"",contraindications:"",goals:[{id:1,type:"short-term",description:"",specific:"",measurable:"",achievable:"",relevant:"",timebound:"",targetDate:"",status:"active"}],interventions:[],frequency:"",duration:"",totalSessions:"",outcomeTools:[],baselineMeasures:"",targetOutcomes:"",treatmentApproach:"",progressionPlan:"",homeExerciseProgram:!1,patientEducation:"",reviewFrequency:"weekly",progressIndicators:"",modificationCriteria:"",dischargeGoals:"",therapistSignature:"",patientConsent:!1,planApproved:!1}),[k,v]=(0,s.useState)(!1),[N,y]=(0,s.useState)({}),j=[{value:"short-term",label:e("shortTerm","Short-term (1-4 weeks)")},{value:"long-term",label:e("longTerm","Long-term (1-3 months)")},{value:"functional",label:e("functional","Functional Goal")},{value:"impairment",label:e("impairment","Impairment-based Goal")}];(0,s.useEffect)(()=>{u&&w()},[u]);const w=async()=>{try{v(!0);const e=await fetch("/api/patients/".concat(u));if(e.ok){const r=await e.json();h(e=>(0,t.A)((0,t.A)({},e),{},{patientName:r.name||"",patientId:r._id||u}))}}catch(e){console.error("Error loading patient data:",e)}finally{v(!1)}},D=(e,r)=>{h(a=>(0,t.A)((0,t.A)({},a),{},{[e]:r})),N[e]&&y(r=>(0,t.A)((0,t.A)({},r),{},{[e]:null}))},C=(e,r,a)=>{h(s=>(0,t.A)((0,t.A)({},s),{},{goals:s.goals.map((s,l)=>l===e?(0,t.A)((0,t.A)({},s),{},{[r]:a}):s)}));const s="goal_".concat(e,"_").concat(r);N[s]&&y(e=>(0,t.A)((0,t.A)({},e),{},{[s]:null}))};return k?(0,c.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,c.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,c.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,c.jsx)("div",{className:"bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 rounded-lg shadow-lg mb-6",children:(0,c.jsx)("div",{className:"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50",children:(0,c.jsx)("div",{className:"px-6 py-6",children:(0,c.jsxs)("div",{className:"flex items-center justify-between",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent",children:e("treatmentPlanGoals","Treatment Plan & Goals")}),(0,c.jsxs)("p",{className:"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-clipboard-list text-blue-500 mr-2"}),e("treatmentPlanDescription","Comprehensive treatment planning with SMART goals")]}),(0,c.jsxs)("div",{className:"flex items-center space-x-4 mt-3",children:[(0,c.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,c.jsx)("i",{className:"fas fa-clock text-blue-500 mr-1"}),e("estimatedTime","30-45 minutes")]}),(0,c.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,c.jsx)("i",{className:"fas fa-target text-green-500 mr-1"}),e("smartGoals","SMART Goals")]}),(0,c.jsxs)("span",{className:"flex items-center text-sm text-gray-600 dark:text-gray-400",children:[(0,c.jsx)("i",{className:"fas fa-chart-line text-purple-500 mr-1"}),e("comprehensive","Comprehensive")]})]})]}),(0,c.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,c.jsxs)("div",{className:"bg-gradient-to-r from-blue-400 to-indigo-400 text-white px-4 py-2 rounded-full shadow-lg",children:[(0,c.jsx)("i",{className:"fas fa-clipboard-list mr-2"}),e("treatmentPlan","Treatment Plan")]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-green-400 to-emerald-400 text-white px-3 py-1 rounded-full text-sm shadow-md",children:[(0,c.jsx)("i",{className:"fas fa-bullseye mr-1"}),e("goals","Goals")]})]})]})})})}),(0,c.jsxs)("form",{onSubmit:async r=>{if(r.preventDefault(),(()=>{const r={};return f.patientName.trim()||(r.patientName=e("patientNameRequired","Patient name is required")),f.primaryDiagnosis.trim()||(r.primaryDiagnosis=e("primaryDiagnosisRequired","Primary diagnosis is required")),f.therapistName.trim()||(r.therapistName=e("therapistNameRequired","Therapist name is required")),0===f.goals.length&&(r.goals=e("goalsRequired","At least one goal is required")),y(r),0===Object.keys(r).length})())try{v(!0);const r={formType:"treatment-plan",formName:"Treatment Plan & Goals",patientId:u,patientName:f.patientName,data:f,submittedBy:(null===a||void 0===a?void 0:a.id)||"anonymous",submittedByName:(null===a||void 0===a?void 0:a.name)||"Anonymous User",submittedAt:(new Date).toISOString(),status:"completed",metadata:{version:"1.0",formVersion:"v2024.1",compliance:["HIPAA","CARF","CBAHI"]}};await m(r);o.Ay.success(e("treatmentPlanSaved","Treatment plan saved successfully!")),p(u?"/patients/".concat(u):"/forms/submissions")}catch(t){console.error("Error saving treatment plan:",t),o.Ay.error(e("errorSaving","Error saving treatment plan. Please try again."))}finally{v(!1)}},className:"space-y-6",children:[(0,c.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg shadow-lg border border-green-200 dark:border-green-700 p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-green-900 dark:text-green-100 mb-6 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-user text-green-600 dark:text-green-400 mr-2"}),e("patientInformation","Patient Information")]}),(0,c.jsx)("div",{className:"bg-green-100 dark:bg-green-800/30 border border-green-300 dark:border-green-600 rounded-lg p-4 mb-6",children:(0,c.jsxs)("p",{className:"text-sm text-green-800 dark:text-green-200",children:[(0,c.jsx)("i",{className:"fas fa-info-circle text-green-600 dark:text-green-400 mr-2"}),e("patientInfoInstruction","Enter basic patient information and plan details for this treatment plan.")]})}),(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-user-tag text-green-600 dark:text-green-400 mr-1"}),e("patientName","Patient Name")," *"]}),(0,c.jsx)("input",{type:"text",value:f.patientName,onChange:e=>D("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ".concat(N.patientName?"border-red-500":"border-green-300 dark:border-green-600"),placeholder:e("enterPatientName","Enter patient name..."),required:!0}),N.patientName&&(0,c.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),N.patientName]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-calendar text-blue-600 dark:text-blue-400 mr-1"}),e("planDate","Plan Date")," *"]}),(0,c.jsx)("input",{type:"date",value:f.planDate,onChange:e=>D("planDate",e.target.value),className:"w-full px-3 py-2 border border-blue-300 dark:border-blue-600 rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500",required:!0})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-purple-800/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-user-md text-purple-600 dark:text-purple-400 mr-1"}),e("therapistName","Therapist Name")]}),(0,c.jsx)("input",{type:"text",value:f.therapistName,onChange:e=>D("therapistName",e.target.value),className:"w-full px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500",placeholder:e("enterTherapistName","Enter therapist name...")})]})]})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg shadow-lg border border-orange-200 dark:border-orange-700 p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-orange-900 dark:text-orange-100 mb-6 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-stethoscope text-orange-600 dark:text-orange-400 mr-2"}),e("diagnosisAssessment","Diagnosis & Assessment")]}),(0,c.jsx)("div",{className:"bg-orange-100 dark:bg-orange-800/30 border border-orange-300 dark:border-orange-600 rounded-lg p-4 mb-6",children:(0,c.jsxs)("p",{className:"text-sm text-orange-800 dark:text-orange-200",children:[(0,c.jsx)("i",{className:"fas fa-clipboard-check text-orange-600 dark:text-orange-400 mr-2"}),e("diagnosisInstruction","Document the patient's diagnosis, functional limitations, and any precautions or contraindications.")]})}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-diagnoses text-red-600 dark:text-red-400 mr-1"}),e("primaryDiagnosis","Primary Diagnosis")," *"]}),(0,c.jsx)("input",{type:"text",value:f.primaryDiagnosis,onChange:e=>D("primaryDiagnosis",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500 ".concat(N.primaryDiagnosis?"border-red-500":"border-red-300 dark:border-red-600"),placeholder:e("enterPrimaryDiagnosis","Enter primary diagnosis..."),required:!0}),N.primaryDiagnosis&&(0,c.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),N.primaryDiagnosis]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-pink-800/20 border border-pink-200 dark:border-pink-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-pink-800 dark:text-pink-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-plus-circle text-pink-600 dark:text-pink-400 mr-1"}),e("secondaryDiagnosis","Secondary Diagnosis")]}),(0,c.jsx)("input",{type:"text",value:f.secondaryDiagnosis,onChange:e=>D("secondaryDiagnosis",e.target.value),className:"w-full px-3 py-2 border border-pink-300 dark:border-pink-600 rounded-lg bg-white dark:bg-pink-700 text-pink-900 dark:text-pink-100 focus:ring-2 focus:ring-pink-500",placeholder:e("enterSecondaryDiagnosis","Enter secondary diagnosis...")})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-indigo-800/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-code text-indigo-600 dark:text-indigo-400 mr-1"}),e("icdCodes","ICD Codes")]}),(0,c.jsx)("input",{type:"text",value:f.icdCodes,onChange:e=>D("icdCodes",e.target.value),className:"w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500",placeholder:e("enterIcdCodes","Enter ICD codes...")})]})]}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-yellow-800/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-1"}),e("functionalLimitations","Functional Limitations")]}),(0,c.jsx)("textarea",{value:f.functionalLimitations,onChange:e=>D("functionalLimitations",e.target.value),className:"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500",rows:"3",placeholder:e("describeFunctionalLimitations","Describe functional limitations...")})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-red-800/20 border border-red-200 dark:border-red-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-shield-alt text-red-600 dark:text-red-400 mr-1"}),e("precautions","Precautions")]}),(0,c.jsx)("textarea",{value:f.precautions,onChange:e=>D("precautions",e.target.value),className:"w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500",rows:"2",placeholder:e("listPrecautions","List precautions...")})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-gray-800/20 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-gray-800 dark:text-gray-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-ban text-gray-600 dark:text-gray-400 mr-1"}),e("contraindications","Contraindications")]}),(0,c.jsx)("textarea",{value:f.contraindications,onChange:e=>D("contraindications",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-gray-500",rows:"2",placeholder:e("listContraindications","List contraindications...")})]})]})]})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-lg shadow-lg border border-emerald-200 dark:border-emerald-700 p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-emerald-900 dark:text-emerald-100 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-bullseye text-emerald-600 dark:text-emerald-400 mr-2"}),e("smartGoals","SMART Goals")]}),(0,c.jsxs)("button",{type:"button",onClick:()=>{const e={id:Date.now(),type:"short-term",description:"",specific:"",measurable:"",achievable:"",relevant:"",timebound:"",targetDate:"",status:"active"};h(r=>(0,t.A)((0,t.A)({},r),{},{goals:[...r.goals,e]}))},className:"px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-plus mr-2"}),e("addGoal","Add Goal")]})]}),(0,c.jsx)("div",{className:"bg-emerald-100 dark:bg-emerald-800/30 border border-emerald-300 dark:border-emerald-600 rounded-lg p-4 mb-6",children:(0,c.jsxs)("p",{className:"text-sm text-emerald-800 dark:text-emerald-200",children:[(0,c.jsx)("i",{className:"fas fa-target text-emerald-600 dark:text-emerald-400 mr-2"}),e("smartGoalsInstruction","Create SMART goals (Specific, Measurable, Achievable, Relevant, Time-bound) for the patient's treatment plan.")]})}),(0,c.jsx)("div",{className:"space-y-6",children:f.goals.map((r,a)=>(0,c.jsxs)("div",{className:"bg-white dark:bg-emerald-800/20 border border-emerald-200 dark:border-emerald-600 rounded-lg p-6",children:[(0,c.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,c.jsxs)("h3",{className:"text-lg font-medium text-emerald-900 dark:text-emerald-100 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-flag text-emerald-600 dark:text-emerald-400 mr-2"}),e("goal","Goal")," ",a+1]}),(0,c.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,c.jsx)("select",{value:r.type,onChange:e=>C(a,"type",e.target.value),className:"px-3 py-1 border border-emerald-300 dark:border-emerald-600 rounded-lg bg-white dark:bg-emerald-700 text-emerald-900 dark:text-emerald-100 text-sm",children:j.map(e=>(0,c.jsx)("option",{value:e.value,children:e.label},e.value))}),f.goals.length>1&&(0,c.jsxs)("button",{type:"button",onClick:()=>(e=>{f.goals.length>1&&h(r=>(0,t.A)((0,t.A)({},r),{},{goals:r.goals.filter((r,a)=>a!==e)}))})(a),className:"px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 transition-all duration-200 shadow-md flex items-center text-sm",children:[(0,c.jsx)("i",{className:"fas fa-trash mr-1"}),e("remove","Remove")]})]})]}),(0,c.jsxs)("div",{className:"mb-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-align-left text-blue-600 dark:text-blue-400 mr-1"}),e("goalDescription","Goal Description")," *"]}),(0,c.jsx)("textarea",{value:r.description,onChange:e=>C(a,"description",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ".concat(N["goal_".concat(a,"_description")]?"border-red-500":"border-blue-300 dark:border-blue-600"),rows:"2",placeholder:e("goalDescriptionPlaceholder","Patient will..."),required:!0}),N["goal_".concat(a,"_description")]&&(0,c.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),N["goal_".concat(a,"_description")]]})]}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-crosshairs text-purple-600 dark:text-purple-400 mr-1"}),e("specific","Specific")]}),(0,c.jsx)("input",{type:"text",value:r.specific,onChange:e=>C(a,"specific",e.target.value),className:"w-full px-3 py-2 border border-purple-300 dark:border-purple-600 rounded-lg bg-white dark:bg-purple-700 text-purple-900 dark:text-purple-100 focus:ring-2 focus:ring-purple-500",placeholder:e("specificPlaceholder","What exactly will be achieved?")})]}),(0,c.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-ruler text-green-600 dark:text-green-400 mr-1"}),e("measurable","Measurable")]}),(0,c.jsx)("input",{type:"text",value:r.measurable,onChange:e=>C(a,"measurable",e.target.value),className:"w-full px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500",placeholder:e("measurablePlaceholder","How will progress be measured?")})]}),(0,c.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-check-circle text-yellow-600 dark:text-yellow-400 mr-1"}),e("achievable","Achievable")]}),(0,c.jsx)("input",{type:"text",value:r.achievable,onChange:e=>C(a,"achievable",e.target.value),className:"w-full px-3 py-2 border border-yellow-300 dark:border-yellow-600 rounded-lg bg-white dark:bg-yellow-700 text-yellow-900 dark:text-yellow-100 focus:ring-2 focus:ring-yellow-500",placeholder:e("achievablePlaceholder","Is this goal realistic?")})]}),(0,c.jsxs)("div",{className:"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-link text-indigo-600 dark:text-indigo-400 mr-1"}),e("relevant","Relevant")]}),(0,c.jsx)("input",{type:"text",value:r.relevant,onChange:e=>C(a,"relevant",e.target.value),className:"w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100 focus:ring-2 focus:ring-indigo-500",placeholder:e("relevantPlaceholder","Why is this goal important?")})]})]}),(0,c.jsxs)("div",{className:"mt-4 grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,c.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-red-800 dark:text-red-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-clock text-red-600 dark:text-red-400 mr-1"}),e("timebound","Time-bound")]}),(0,c.jsx)("input",{type:"text",value:r.timebound,onChange:e=>C(a,"timebound",e.target.value),className:"w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg bg-white dark:bg-red-700 text-red-900 dark:text-red-100 focus:ring-2 focus:ring-red-500",placeholder:e("timeboundPlaceholder","When will this be achieved?")})]}),(0,c.jsxs)("div",{className:"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-teal-800 dark:text-teal-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-calendar-alt text-teal-600 dark:text-teal-400 mr-1"}),e("targetDate","Target Date")]}),(0,c.jsx)("input",{type:"date",value:r.targetDate,onChange:e=>C(a,"targetDate",e.target.value),className:"w-full px-3 py-2 border border-teal-300 dark:border-teal-600 rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100 focus:ring-2 focus:ring-teal-500"})]})]})]},r.id))})]}),(0,c.jsxs)("div",{className:"bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-lg shadow-lg border border-violet-200 dark:border-violet-700 p-6",children:[(0,c.jsxs)("h2",{className:"text-lg font-semibold text-violet-900 dark:text-violet-100 mb-6 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-tools text-violet-600 dark:text-violet-400 mr-2"}),e("treatmentInterventions","Treatment Interventions")]}),(0,c.jsx)("div",{className:"bg-violet-100 dark:bg-violet-800/30 border border-violet-300 dark:border-violet-600 rounded-lg p-4 mb-6",children:(0,c.jsxs)("p",{className:"text-sm text-violet-800 dark:text-violet-200",children:[(0,c.jsx)("i",{className:"fas fa-hand-holding-medical text-violet-600 dark:text-violet-400 mr-2"}),e("interventionsInstruction","Select the treatment interventions and specify frequency, duration, and total sessions.")]})}),(0,c.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-violet-800/20 border border-violet-200 dark:border-violet-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-violet-800 dark:text-violet-200 mb-4 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-list-check text-violet-600 dark:text-violet-400 mr-1"}),e("selectInterventions","Select Interventions")]}),(0,c.jsx)("div",{className:"grid grid-cols-1 gap-2 max-h-64 overflow-y-auto",children:["Manual Therapy","Therapeutic Exercise","Neuromuscular Re-education","Gait Training","Balance Training","Strength Training","Range of Motion","Pain Management","Patient Education","Functional Training","Modalities","Aquatic Therapy"].map(e=>(0,c.jsxs)("div",{className:"flex items-center bg-gray-50 dark:bg-violet-900/20 border border-violet-200 dark:border-violet-600 rounded-lg px-3 py-2",children:[(0,c.jsx)("input",{type:"checkbox",id:"intervention-".concat(e),checked:f.interventions.includes(e),onChange:r=>{return a="interventions",s=e,l=r.target.checked,void h(e=>{const r=e[a]||[];return l?(0,t.A)((0,t.A)({},e),{},{[a]:[...r,s]}):(0,t.A)((0,t.A)({},e),{},{[a]:r.filter(e=>e!==s)})});var a,s,l},className:"mr-2 h-4 w-4 text-violet-600 focus:ring-violet-500 border-violet-300 rounded"}),(0,c.jsx)("label",{htmlFor:"intervention-".concat(e),className:"text-sm text-violet-800 dark:text-violet-200 font-medium",children:e})]},e))})]}),(0,c.jsxs)("div",{className:"space-y-4",children:[(0,c.jsxs)("div",{className:"bg-white dark:bg-blue-800/20 border border-blue-200 dark:border-blue-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-calendar-week text-blue-600 dark:text-blue-400 mr-1"}),e("frequency","Frequency")," *"]}),(0,c.jsx)("input",{type:"text",value:f.frequency,onChange:e=>D("frequency",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-blue-700 text-blue-900 dark:text-blue-100 focus:ring-2 focus:ring-blue-500 ".concat(N.frequency?"border-red-500":"border-blue-300 dark:border-blue-600"),placeholder:e("frequencyPlaceholder","e.g., 3x per week"),required:!0}),N.frequency&&(0,c.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),N.frequency]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-green-800/20 border border-green-200 dark:border-green-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-green-800 dark:text-green-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-hourglass-half text-green-600 dark:text-green-400 mr-1"}),e("duration","Duration")," *"]}),(0,c.jsx)("input",{type:"text",value:f.duration,onChange:e=>D("duration",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100 focus:ring-2 focus:ring-green-500 ".concat(N.duration?"border-red-500":"border-green-300 dark:border-green-600"),placeholder:e("durationPlaceholder","e.g., 45 minutes"),required:!0}),N.duration&&(0,c.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),N.duration]})]}),(0,c.jsxs)("div",{className:"bg-white dark:bg-orange-800/20 border border-orange-200 dark:border-orange-600 rounded-lg p-4",children:[(0,c.jsxs)("label",{className:"block text-sm font-medium text-orange-800 dark:text-orange-200 mb-2 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-hashtag text-orange-600 dark:text-orange-400 mr-1"}),e("totalSessions","Total Sessions")," *"]}),(0,c.jsx)("input",{type:"number",value:f.totalSessions,onChange:e=>D("totalSessions",e.target.value),className:"w-full px-3 py-2 border rounded-lg bg-white dark:bg-orange-700 text-orange-900 dark:text-orange-100 focus:ring-2 focus:ring-orange-500 ".concat(N.totalSessions?"border-red-500":"border-orange-300 dark:border-orange-600"),placeholder:e("totalSessionsPlaceholder","e.g., 12"),min:"1",required:!0}),N.totalSessions&&(0,c.jsxs)("p",{className:"text-red-500 text-sm mt-1 flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-exclamation-triangle mr-1"}),N.totalSessions]})]})]})]})]}),(0,c.jsx)("div",{className:"bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-800 dark:to-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6",children:(0,c.jsxs)("div",{className:"flex justify-between items-center",children:[(0,c.jsxs)("button",{type:"button",onClick:()=>p(-1),className:"px-6 py-3 bg-gradient-to-r from-gray-500 to-slate-500 text-white rounded-lg hover:from-gray-600 hover:to-slate-600 transition-all duration-200 shadow-lg flex items-center",children:[(0,c.jsx)("i",{className:"fas fa-arrow-left mr-2"}),e("cancel","Cancel")]}),(0,c.jsxs)("div",{className:"flex space-x-4",children:[(0,c.jsxs)("button",{type:"button",onClick:async()=>{try{v(!0);const r=(0,t.A)((0,t.A)({},f),{},{generatedAt:(new Date).toISOString(),generatedBy:a.name||a.email,patientId:u}),s=await fetch("/api/v1/treatment-plans/pdf",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("token"))},body:JSON.stringify(r)});if(!s.ok)throw new Error("HTTP error! status: ".concat(s.status));{const r=await s.blob(),a=window.URL.createObjectURL(r),t=document.createElement("a");t.href=a,t.download="treatment-plan-".concat(f.patientName.replace(/\s+/g,"-"),"-").concat(f.planDate,".pdf"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(a),document.body.removeChild(t),alert(e("pdfGenerated","PDF generated successfully!"))}}catch(r){console.error("Error generating PDF:",r),alert(e("errorGeneratingPDF","Error generating PDF. Please try again."))}finally{v(!1)}},disabled:k,className:"px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-lg hover:from-red-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg",children:[(0,c.jsx)("i",{className:"fas fa-file-pdf mr-2"}),k?e("generating","Generating..."):e("generatePDF","Generate PDF")]}),(0,c.jsxs)("button",{type:"submit",disabled:k,className:"px-8 py-3 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg hover:from-blue-600 hover:to-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center transition-all duration-200 shadow-lg",children:[(0,c.jsx)("i",{className:"fas fa-save mr-2"}),k?e("saving","Saving..."):e("saveTreatmentPlan","Save Treatment Plan")]})]})]})})]})]})}}}]);
//# sourceMappingURL=8086.8269b6d5.chunk.js.map