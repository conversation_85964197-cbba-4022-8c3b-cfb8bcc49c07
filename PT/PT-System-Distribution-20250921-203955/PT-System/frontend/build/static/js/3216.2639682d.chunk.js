"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3216],{835:(e,s,t)=>{t.r(s),t.d(s,{default:()=>c});var a=t(2555),i=t(5043),n=t(7921),r=t(3768),l=t(579);const c=()=>{const{t:e,isRTL:s}=(0,n.o)(),[t,c]=(0,i.useState)({email:{appointments:!0,treatments:!0,billing:!0,system:!1,marketing:!1},sms:{appointments:!0,treatments:!1,billing:!0,system:!1,marketing:!1},push:{appointments:!0,treatments:!0,billing:!0,system:!0,marketing:!1},inApp:{appointments:!0,treatments:!0,billing:!0,system:!0,marketing:!0}}),[o,d]=(0,i.useState)({quietHours:{enabled:!0,start:"22:00",end:"08:00"},frequency:{digest:"daily",reminders:"immediate"},language:"en",timezone:"UTC"}),[m,u]=(0,i.useState)(!1);(0,i.useEffect)(()=>{x()},[]);const x=async()=>{},g=(e,s,t)=>{d(i=>(0,a.A)((0,a.A)({},i),{},{[e]:(0,a.A)((0,a.A)({},i[e]),{},{[s]:t})}))},y=[{key:"appointments",label:e("appointments","Appointments"),description:e("appointmentNotificationsDesc","Reminders and updates about appointments"),icon:"fas fa-calendar-alt"},{key:"treatments",label:e("treatments","Treatments"),description:e("treatmentNotificationsDesc","Updates about treatment plans and progress"),icon:"fas fa-heartbeat"},{key:"billing",label:e("billing","Billing"),description:e("billingNotificationsDesc","Payment reminders and billing updates"),icon:"fas fa-dollar-sign"},{key:"system",label:e("system","System"),description:e("systemNotificationsDesc","System maintenance and security alerts"),icon:"fas fa-cog"},{key:"marketing",label:e("marketing","Marketing"),description:e("marketingNotificationsDesc","Promotional content and newsletters"),icon:"fas fa-bullhorn"}],h=[{key:"email",label:e("email","Email"),icon:"fas fa-envelope",description:e("emailChannelDesc","Receive notifications via email")},{key:"sms",label:e("sms","SMS"),icon:"fas fa-sms",description:e("smsChannelDesc","Receive notifications via text message")},{key:"push",label:e("push","Push"),icon:"fas fa-bell",description:e("pushChannelDesc","Receive push notifications on your device")},{key:"inApp",label:e("inApp","In-App"),icon:"fas fa-desktop",description:e("inAppChannelDesc","Show notifications within the application")}];return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("notificationSettings","Notification Settings")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("notificationSettingsDesc","Customize how and when you receive notifications")})]}),(0,l.jsx)("button",{onClick:async()=>{u(!0);try{r.Ay.success(e("settingsUpdated","Notification settings updated successfully"))}catch(s){console.error("Error updating notification settings:",s),r.Ay.error(e("settingsUpdateError","Failed to update notification settings"))}finally{u(!1)}},disabled:m,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors mt-4 sm:mt-0",children:m?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),e("saving","Saving...")]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("i",{className:"fas fa-save mr-2"}),e("saveSettings","Save Settings")]})})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:[(0,l.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-700",children:[(0,l.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("notificationChannels","Notification Channels")}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e("channelsDescription","Choose how you want to receive different types of notifications")})]}),(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("notificationType","Notification Type")}),h.map(e=>(0,l.jsx)("th",{className:"px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:(0,l.jsxs)("div",{className:"flex flex-col items-center",children:[(0,l.jsx)("i",{className:"".concat(e.icon," mb-1")}),e.label]})},e.key))]})}),(0,l.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700",children:y.map(e=>(0,l.jsxs)("tr",{children:[(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("i",{className:"".concat(e.icon," text-gray-400")})}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.label}),(0,l.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description})]})]})}),h.map(s=>(0,l.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-center",children:(0,l.jsx)("label",{className:"inline-flex items-center",children:(0,l.jsx)("input",{type:"checkbox",checked:t[s.key][e.key],onChange:()=>((e,s)=>{c(t=>(0,a.A)((0,a.A)({},t),{},{[e]:(0,a.A)((0,a.A)({},t[e]),{},{[s]:!t[e][s]})}))})(s.key,e.key),className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"})})},s.key))]},e.key))})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("quietHours","Quiet Hours")}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:o.quietHours.enabled,onChange:e=>g("quietHours","enabled",e.target.checked),className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"}),(0,l.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:e("enableQuietHours","Enable quiet hours")})]}),o.quietHours.enabled&&(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("startTime","Start Time")}),(0,l.jsx)("input",{type:"time",value:o.quietHours.start,onChange:e=>g("quietHours","start",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("endTime","End Time")}),(0,l.jsx)("input",{type:"time",value:o.quietHours.end,onChange:e=>g("quietHours","end",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]})]})]})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("frequencySettings","Frequency Settings")}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("digestFrequency","Digest Frequency")}),(0,l.jsxs)("select",{value:o.frequency.digest,onChange:e=>g("frequency","digest",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,l.jsx)("option",{value:"immediate",children:e("immediate","Immediate")}),(0,l.jsx)("option",{value:"daily",children:e("daily","Daily")}),(0,l.jsx)("option",{value:"weekly",children:e("weekly","Weekly")}),(0,l.jsx)("option",{value:"never",children:e("never","Never")})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("reminderFrequency","Reminder Frequency")}),(0,l.jsxs)("select",{value:o.frequency.reminders,onChange:e=>g("frequency","reminders",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,l.jsx)("option",{value:"immediate",children:e("immediate","Immediate")}),(0,l.jsx)("option",{value:"hourly",children:e("hourly","Hourly")}),(0,l.jsx)("option",{value:"daily",children:e("daily","Daily")})]})]})]})]})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("testNotifications","Test Notifications")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:e("testNotificationsDesc","Send test notifications to verify your settings")}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:h.map(s=>(0,l.jsxs)("button",{onClick:()=>r.Ay.success("Test ".concat(s.label," notification sent!")),className:"px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors",children:[(0,l.jsx)("i",{className:"".concat(s.icon," mr-2")}),e("test","Test")," ",s.label]},s.key))})]})]})}}}]);
//# sourceMappingURL=3216.2639682d.chunk.js.map