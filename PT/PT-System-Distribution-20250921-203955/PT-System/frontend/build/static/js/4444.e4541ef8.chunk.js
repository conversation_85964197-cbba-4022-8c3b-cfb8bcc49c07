"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[4444],{4444:(e,s,a)=>{a.r(s),a.d(s,{default:()=>n});a(2555);var t=a(5043),r=a(7921),i=(a(3768),a(579));const n=()=>{const{t:e,isRTL:s}=(0,r.o)(),[a,n]=(0,t.useState)("packages"),[d,l]=(0,t.useState)(""),[c,x]=(0,t.useState)(!1),[o,g]=(0,t.useState)(!1),[m,p]=(0,t.useState)(null),[h,y]=(0,t.useState)([{id:"PKG-001",name:"Basic Physical Therapy Package",nameAr:"\u0628\u0627\u0642\u0629 \u0627\u0644\u0639\u0644\u0627\u062c \u0627\u0644\u0637\u0628\u064a\u0639\u064a \u0627\u0644\u0623\u0633\u0627\u0633\u064a\u0629",description:"Comprehensive physical therapy sessions for general rehabilitation",descriptionAr:"\u062c\u0644\u0633\u0627\u062a \u0639\u0644\u0627\u062c \u0637\u0628\u064a\u0639\u064a \u0634\u0627\u0645\u0644\u0629 \u0644\u0644\u062a\u0623\u0647\u064a\u0644 \u0627\u0644\u0639\u0627\u0645",category:"Physical Therapy",duration:"4 weeks",totalSessions:12,sessionsPerWeek:3,sessionDuration:45,price:3e3,discountedPrice:2700,discount:10,status:"Active",services:[{name:"Initial Assessment",sessions:1,price:300},{name:"Physical Therapy Session",sessions:10,price:250},{name:"Progress Evaluation",sessions:1,price:200}],features:["Initial comprehensive assessment","Personalized treatment plan","Progress tracking","Home exercise program","Final evaluation report"],targetConditions:["Back pain","Joint stiffness","Post-surgery rehabilitation"],assignedPatients:45,completionRate:87,averageRating:4.6},{id:"PKG-002",name:"Special Needs Therapy Package",nameAr:"\u0628\u0627\u0642\u0629 \u0639\u0644\u0627\u062c \u0630\u0648\u064a \u0627\u0644\u0627\u062d\u062a\u064a\u0627\u062c\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629",description:"Specialized therapy for children and adults with special needs",descriptionAr:"\u0639\u0644\u0627\u062c \u0645\u062a\u062e\u0635\u0635 \u0644\u0644\u0623\u0637\u0641\u0627\u0644 \u0648\u0627\u0644\u0628\u0627\u0644\u063a\u064a\u0646 \u0645\u0646 \u0630\u0648\u064a \u0627\u0644\u0627\u062d\u062a\u064a\u0627\u062c\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629",category:"Special Needs",duration:"8 weeks",totalSessions:24,sessionsPerWeek:3,sessionDuration:60,price:7200,discountedPrice:6480,discount:10,status:"Active",services:[{name:"Special Needs Assessment",sessions:2,price:400},{name:"Occupational Therapy",sessions:8,price:350},{name:"Speech Therapy",sessions:8,price:350},{name:"Behavioral Support",sessions:6,price:300}],features:["Comprehensive special needs assessment","Multi-disciplinary approach","Family training and support","Adaptive equipment recommendations","Progress monitoring with visual aids","Sensory integration therapy"],targetConditions:["Autism","Cerebral Palsy","Down Syndrome","ADHD"],assignedPatients:23,completionRate:92,averageRating:4.8},{id:"PKG-003",name:"Premium Rehabilitation Package",nameAr:"\u0628\u0627\u0642\u0629 \u0627\u0644\u062a\u0623\u0647\u064a\u0644 \u0627\u0644\u0645\u062a\u0645\u064a\u0632\u0629",description:"Intensive rehabilitation program with advanced techniques",descriptionAr:"\u0628\u0631\u0646\u0627\u0645\u062c \u062a\u0623\u0647\u064a\u0644 \u0645\u0643\u062b\u0641 \u0628\u062a\u0642\u0646\u064a\u0627\u062a \u0645\u062a\u0642\u062f\u0645\u0629",category:"Rehabilitation",duration:"6 weeks",totalSessions:18,sessionsPerWeek:3,sessionDuration:60,price:5400,discountedPrice:4860,discount:10,status:"Active",services:[{name:"Advanced Assessment",sessions:1,price:400},{name:"Physical Therapy",sessions:12,price:300},{name:"Hydrotherapy",sessions:3,price:400},{name:"Electrotherapy",sessions:2,price:350}],features:["Advanced diagnostic assessment","Hydrotherapy sessions","Electrotherapy treatment","Manual therapy techniques","Personalized exercise program","Nutritional guidance"],targetConditions:["Sports injuries","Chronic pain","Post-operative care"],assignedPatients:31,completionRate:89,averageRating:4.7}]),[u,b]=(0,t.useState)([{id:"ASSIGN-001",packageId:"PKG-001",packageName:"Basic Physical Therapy Package",patientName:"Ahmed Al-Rashid",patientId:"P001",assignedDate:"2024-02-01",startDate:"2024-02-05",endDate:"2024-03-05",status:"Active",progress:75,completedSessions:9,totalSessions:12,remainingSessions:3,therapist:"Dr. Sarah Ahmed",nextSession:"2024-02-20"},{id:"ASSIGN-002",packageId:"PKG-002",packageName:"Special Needs Therapy Package",patientName:"Fatima Al-Zahra",patientId:"P002",assignedDate:"2024-01-15",startDate:"2024-01-20",endDate:"2024-03-20",status:"Active",progress:50,completedSessions:12,totalSessions:24,remainingSessions:12,therapist:"Dr. Mona Hassan",nextSession:"2024-02-18"},{id:"ASSIGN-003",packageId:"PKG-001",packageName:"Basic Physical Therapy Package",patientName:"Mohammed Al-Otaibi",patientId:"P003",assignedDate:"2024-01-10",startDate:"2024-01-15",endDate:"2024-02-15",status:"Completed",progress:100,completedSessions:12,totalSessions:12,remainingSessions:0,therapist:"Dr. Omar Khalil",nextSession:null}]),k=[{id:"packages",label:e("packages","Packages"),icon:"fas fa-box"},{id:"assignments",label:e("assignments","Assignments"),icon:"fas fa-user-check"},{id:"analytics",label:e("analytics","Analytics"),icon:"fas fa-chart-line"},{id:"settings",label:e("settings","Settings"),icon:"fas fa-cog"}],N=e=>new Intl.NumberFormat("ar-SA",{style:"currency",currency:"SAR"}).format(e),j=e=>{switch(e.toLowerCase()){case"active":return"text-green-600 bg-green-100 dark:bg-green-900/30";case"completed":return"text-blue-600 bg-blue-100 dark:bg-blue-900/30";case"paused":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30";case"cancelled":return"text-red-600 bg-red-100 dark:bg-red-900/30";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/30"}};return(0,i.jsxs)("div",{className:"space-y-6 ".concat(s?"font-arabic":"font-english"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("treatmentPackages","Treatment Packages")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("packagesDescription","Manage treatment packages, pricing, and patient assignments")})]}),(0,i.jsx)("div",{className:"flex items-center space-x-4",children:(0,i.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-download mr-2"}),e("exportData","Export Data")]})})]}),(0,i.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600",children:(0,i.jsx)("nav",{className:"-mb-px flex space-x-8",children:k.map(e=>(0,i.jsxs)("button",{onClick:()=>n(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(a===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,i.jsx)("i",{className:"".concat(e.icon," mr-2")}),e.label]},e.id))})}),"packages"===a&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-box text-blue-600 dark:text-blue-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalPackages","Total Packages")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:h.length})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-users text-green-600 dark:text-green-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("activeAssignments","Active Assignments")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:u.filter(e=>"Active"===e.status).length})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-percentage text-purple-600 dark:text-purple-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("averageCompletion","Avg Completion")}),(0,i.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[Math.round(h.reduce((e,s)=>e+s.completionRate,0)/h.length),"%"]})]})]})}),(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg",children:(0,i.jsx)("i",{className:"fas fa-star text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,i.jsxs)("div",{className:"ml-4",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("averageRating","Avg Rating")}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:(h.reduce((e,s)=>e+s.averageRating,0)/h.length).toFixed(1)})]})]})})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("treatmentPackages","Treatment Packages")}),(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("input",{type:"text",placeholder:e("searchPackages","Search packages..."),value:d,onChange:e=>l(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,i.jsxs)("button",{onClick:()=>g(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),e("newPackage","New Package")]})]})]}),(0,i.jsx)("div",{className:"p-6",children:(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:h.map(s=>(0,i.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:s.nameAr})]}),(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(j(s.status)),children:s.status})]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:s.description}),(0,i.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("duration","Duration"),":"]}),(0,i.jsx)("span",{className:"text-gray-900 dark:text-white",children:s.duration})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("sessions","Sessions"),":"]}),(0,i.jsx)("span",{className:"text-gray-900 dark:text-white",children:s.totalSessions})]}),(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsxs)("span",{className:"text-gray-600 dark:text-gray-400",children:[e("category","Category"),":"]}),(0,i.jsx)("span",{className:"text-gray-900 dark:text-white",children:s.category})]})]}),(0,i.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-600 pt-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsxs)("div",{children:[s.discount>0&&(0,i.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400 line-through",children:N(s.price)}),(0,i.jsx)("span",{className:"text-lg font-bold text-gray-900 dark:text-white ml-2",children:N(s.discountedPrice||s.price)})]}),s.discount>0&&(0,i.jsxs)("span",{className:"px-2 py-1 text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full",children:[s.discount,"% ",e("off","OFF")]})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-4",children:[(0,i.jsxs)("span",{children:[s.assignedPatients," ",e("patients","patients")]}),(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("i",{className:"fas fa-star text-yellow-400 mr-1"}),(0,i.jsx)("span",{children:s.averageRating})]})]}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)("button",{onClick:()=>p(s),className:"flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors",children:e("viewDetails","View Details")}),(0,i.jsx)("button",{className:"px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:e("assign","Assign")})]})]})]},s.id))})})]})]}),"assignments"===a&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,i.jsxs)("div",{className:"px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e("packageAssignments","Package Assignments")}),(0,i.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,i.jsx)("i",{className:"fas fa-plus mr-2"}),e("assignPackage","Assign Package")]})]}),(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full",children:[(0,i.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,i.jsxs)("tr",{children:[(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("package","Package")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("progress","Progress")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("therapist","Therapist")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("nextSession","Next Session")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,i.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,i.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:u.map(s=>(0,i.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:s.patientName}),(0,i.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["ID: ",s.patientId]})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsx)("div",{className:"text-sm text-gray-900 dark:text-white",children:s.packageName}),(0,i.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[s.startDate," - ",s.endDate]})]}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(s.progress,"%")}})}),(0,i.jsxs)("span",{className:"text-sm text-gray-900 dark:text-white",children:[s.progress,"%"]})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:[s.completedSessions,"/",s.totalSessions," ",e("sessions","sessions")]})]}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:s.therapist}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:s.nextSession||e("completed","Completed")}),(0,i.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,i.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(j(s.status)),children:s.status})}),(0,i.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,i.jsx)("button",{className:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3",children:e("view","View")}),"Active"===s.status&&(0,i.jsx)("button",{className:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300",children:e("schedule","Schedule")})]})]},s.id))})]})})]})}),"analytics"===a&&(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("packageAnalytics","Package Analytics")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("analyticsDescription","Comprehensive package performance analytics and insights")})]}),"settings"===a&&(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("packageSettings","Package Settings")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("settingsDescription","Configure package templates, pricing rules, and discount policies")})]})]})}}}]);
//# sourceMappingURL=4444.e4541ef8.chunk.js.map