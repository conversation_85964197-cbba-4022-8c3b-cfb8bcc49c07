{"version": 3, "file": "static/js/6873.3aea0ef4.chunk.js", "mappings": "uNAGA,MAoKA,EApKkCA,IAA8C,IAA7C,SAAEC,EAAQ,kBAAEC,EAAiB,OAAEC,GAAQH,EACxE,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERC,EAA4B,CAChC,CAAEC,IAAK,OAAQC,MAAOJ,EAAE,OAAQ,QAASK,SAAS,GAClD,CAAEF,IAAK,iBAAkBC,MAAOJ,EAAE,uBAAwB,kCAA8BK,SAAS,GACjG,CAAEF,IAAK,iBAAkBC,MAAOJ,EAAE,iBAAkB,0BAAsBK,SAAS,GACnF,CAAEF,IAAK,eAAgBC,MAAOJ,EAAE,eAAgB,wBAAyBK,SAAS,GAClF,CAAEF,IAAK,eAAgBC,MAAOJ,EAAE,eAAgB,wBAAyBK,SAAS,GAClF,CAAEF,IAAK,sBAAuBC,MAAOJ,EAAE,sBAAuB,4BAAwBK,SAAS,GAC/F,CAAEF,IAAK,sBAAuBC,MAAOJ,EAAE,sBAAuB,4BAAwBK,SAAS,GAC/F,CAAEF,IAAK,oBAAqBC,MAAOJ,EAAE,oBAAqB,8BAA0BK,SAAS,GAC7F,CAAEF,IAAK,eAAgBC,MAAOJ,EAAE,eAAgB,iBAAkBK,SAAS,GAC3E,CAAEF,IAAK,mBAAoBC,MAAOJ,EAAE,mBAAoB,qBAAsBK,SAAS,GACvF,CAAEF,IAAK,gBAAiBC,MAAOJ,EAAE,gBAAiB,kBAAmBK,SAAS,GAC9E,CAAEF,IAAK,gBAAiBC,MAAOJ,EAAE,gBAAiB,uCAAwCK,SAAS,GACnG,CAAEF,IAAK,UAAWC,MAAOJ,EAAE,UAAW,WAAYK,SAAS,GAC3D,CAAEF,IAAK,iBAAkBC,MAAOJ,EAAE,iBAAkB,mBAAoBK,SAAS,GACjF,CAAEF,IAAK,YAAaC,MAAOJ,EAAE,YAAa,0BAA2BK,SAAS,GAC9E,CAAEF,IAAK,mBAAoBC,MAAOJ,EAAE,mBAAoB,wBAAyBK,SAAS,GAC1F,CAAEF,IAAK,mBAAoBC,MAAOJ,EAAE,mBAAoB,4BAAwBK,SAAS,GACzF,CAAEF,IAAK,wBAAyBC,MAAOJ,EAAE,wBAAyB,2BAA4BK,SAAS,GACvG,CAAEF,IAAK,cAAeC,MAAOJ,EAAE,cAAe,iCAAkCK,SAAS,GACzF,CAAEF,IAAK,wBAAyBC,MAAOJ,EAAE,wBAAyB,2BAA4BK,SAAS,GACvG,CAAEF,IAAK,qBAAsBC,MAAOJ,EAAE,qBAAsB,uBAAmBK,SAAS,GACxF,CAAEF,IAAK,uBAAwBC,MAAOJ,EAAE,uBAAwB,yBAA0BK,SAAS,GACnG,CAAEF,IAAK,eAAgBC,MAAOJ,EAAE,eAAgB,iBAAkBK,SAAS,GAC3E,CAAEF,IAAK,qBAAsBC,MAAOJ,EAAE,qBAAsB,oBAAgBK,SAAS,GACrF,CAAEF,IAAK,qBAAsBC,MAAOJ,EAAE,qBAAsB,mCAA+BK,SAAS,GACpG,CAAEF,IAAK,sBAAuBC,MAAOJ,EAAE,sBAAuB,+BAA2BK,SAAS,GAClG,CAAEF,IAAK,qBAAsBC,MAAOJ,EAAE,qBAAsB,+BAA2BK,SAAS,GAChG,CAAEF,IAAK,qBAAsBC,MAAOJ,EAAE,qBAAsB,kDAAmDK,SAAS,GACxH,CAAEF,IAAK,oBAAqBC,MAAOJ,EAAE,oBAAqB,sBAAuBK,SAAS,GAC1F,CAAEF,IAAK,qBAAsBC,MAAOJ,EAAE,qBAAsB,uBAAwBK,SAAS,GAC7F,CAAEF,IAAK,cAAeC,MAAOJ,EAAE,cAAe,eAAgBK,SAAS,GACvE,CAAEF,IAAK,QAASC,MAAOJ,EAAE,QAAS,SAAUK,SAAS,IAGjDC,EAAc,CAClB,CAAEC,MAAO,IAAKH,MAAOJ,EAAE,QAAS,MAChC,CAAEO,MAAO,IAAKH,MAAOJ,EAAE,OAAQ,MAC/B,CAAEO,MAAO,IAAKH,MAAOJ,EAAE,YAAa,OAGhCQ,EAAsBA,CAACC,EAAYC,EAAOH,KAC9C,MAAMI,EAAiBd,EAASe,mBAAmBH,IAAe,CAAC,EAC7DI,GAAcC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQH,GAAc,IAAE,CAACD,GAAQH,IACrDT,EAAkB,sBAADiB,OAAuBN,GAAcI,IASxD,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrElB,EAAE,qBAAsB,6CAG3BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnElB,EAAE,gCAAiC,wCAEtCmB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDhB,EAA0BkB,IAAKC,IAC9B,MAAMC,EAAUzB,EAASe,mBAAmBS,EAAOlB,MAAQ,CAAC,EAE5D,OACEgB,EAAAA,EAAAA,KAAA,OAAsBF,UAAU,6DAA4DC,UAC1FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLC,GAAIH,EAAOlB,IACXsB,QAASH,EAAQG,UAAW,EAC5BC,SAAWC,GAAMnB,EAAoBa,EAAOlB,IAAK,UAAWwB,EAAEC,OAAOH,SACrER,UAAU,UAEZD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,SAAQC,SAAA,EACrBC,EAAAA,EAAAA,KAAA,SAAOU,QAASR,EAAOlB,IAAKc,UAAU,sEAAqEC,SACxGG,EAAOjB,QAITiB,EAAOhB,SAAWiB,EAAQG,UACzBT,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,sDAAqDC,SAAA,CACnElB,EAAE,OAAQ,QAAQ,QAErBmB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,SAC5BZ,EAAYc,IAAKU,IAChBd,EAAAA,EAAAA,MAAA,SAAwBC,UAAU,oBAAmBC,SAAA,EACnDC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLQ,KAAI,GAAAhB,OAAKM,EAAOlB,IAAG,SACnBI,MAAOuB,EAAKvB,MACZkB,QAASH,EAAQQ,OAASA,EAAKvB,MAC/BmB,SAAWC,GAAMnB,EAAoBa,EAAOlB,IAAK,OAAQwB,EAAEC,OAAOrB,OAClEU,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SAAEY,EAAK1B,UATvD0B,EAAKvB,aAiBxBe,EAAQG,UACPT,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,sDAAqDC,SAAA,CACnElB,EAAE,sBAAuB,4CAA4C,QAExEmB,EAAAA,EAAAA,KAAA,YACEZ,MAAOe,EAAQU,aAAe,GAC9BN,SAAWC,GAAMnB,EAAoBa,EAAOlB,IAAK,cAAewB,EAAEC,OAAOrB,OACzE0B,KAAM,EACNhB,UAAU,uKACViB,YAAalC,EAAE,6BAA8B,oEAjD/CqB,EAAOlB,aA8DzBa,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,yDAAwDC,SACnElB,EAAE,iBAAkB,6BAEvBmB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDlB,EAAE,yBAA0B,sEAE/BmB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDrB,EAASsC,YAAYf,IAAI,CAACE,EAASc,KAClCpB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EkB,EAAQ,EAAE,KAAGpC,EAAE,UAAW,WAAW,IAAEoC,EAAQ,MAElDjB,EAAAA,EAAAA,KAAA,YACEZ,MAAOe,EACPI,SAAWC,GApGIU,EAACD,EAAO7B,KACrC,MAAM+B,EAAiB,IAAIzC,EAASsC,aACpCG,EAAeF,GAAS7B,EACxBT,EAAkB,cAAewC,IAiGFD,CAAuBD,EAAOT,EAAEC,OAAOrB,OACxD0B,KAAM,EACNhB,UAAU,kKACViB,YAAalC,EAAE,sBAAuB,6BAThCoC,gBCmCxB,EAnLqBxC,IAAsF,IAArF,SAAEC,EAAQ,kBAAEC,EAAiB,kBAAEyC,EAAiB,QAAEC,EAAO,WAAEC,EAAU,OAAE1C,GAAQH,EACnG,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERyC,EAAe,CACnB1C,EAAE,eAAgB,oGAClBA,EAAE,eAAgB,6FAClBA,EAAE,eAAgB,uEAClBA,EAAE,eAAgB,4EAClBA,EAAE,eAAgB,iFAGd2C,EAAoBA,CAACC,EAAUC,EAAOC,EAAWC,KAAgB,IAADC,EAAAC,EACpE,MAAMC,GAA0B,QAAlBF,EAAAnD,EAAS+C,UAAS,IAAAI,OAAA,EAAlBA,EAAoBE,QAAS,GACrCC,GAA0B,QAAlBF,EAAApD,EAAS+C,UAAS,IAAAK,OAAA,EAAlBA,EAAoBE,QAAS,GAE3C,OACEnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SAAE2B,KAG1E7B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,QAAS,SAAS,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEvDC,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACL6B,IAAI,IACJC,IAAI,KACJ9C,MAAO4C,EACPzB,SAAWC,GAAM7B,EAAkB,GAADiB,OAAI6B,EAAQ,UAAUjB,EAAEC,OAAOrB,OACjEU,UAAS,iJAAAF,OACPhB,EAAO+C,GAAa,iBAAmB,mBAEzCZ,YAAY,SAEbnC,EAAO+C,KACN3B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAO+C,SAKrD9B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,6DAA4DC,SAAA,CAC1ElB,EAAE,QAAS,SAAS,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEvDF,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACL+B,QAASA,IAAMd,EAAQI,GACvB3B,UAAU,uFAAsFC,SAAA,EAEhGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZjB,EAAE,UAAW,kBAIjBkD,EAAM9B,IAAI,CAACmC,EAAMnB,KAChBjB,EAAAA,EAAAA,KAAA,OAAiBF,UAAU,WAAUC,UACnCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,yEAAwEC,SAAA,CACrFkB,EAAQ,EAAE,QAEbjB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,SAAQC,UACrBC,EAAAA,EAAAA,KAAA,YACEZ,MAAOgD,EACP7B,SAAWC,GAAMY,EAAkB,GAADxB,OAAI6B,EAAQ,UAAUR,EAAOT,EAAEC,OAAOrB,OACxE0B,KAAM,EACNhB,UAAU,kKACViB,YAAalC,EAAE,iBAAkB,iFAGpCkD,EAAMM,OAAS,IACdrC,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACL+B,QAASA,IAAMb,EAAWG,EAAUR,GACpCnB,UAAU,6DACV4B,MAAO7C,EAAE,aAAc,eAAekB,UAEtCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+BArBXmB,IA4BXrC,EAAOgD,KACN5B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBAAsBC,SAAEnB,EAAOgD,SAKhD/B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DC,SAAA,EACvEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0BACZjB,EAAE,eAAgB,iBAAiB,QAEtCmB,EAAAA,EAAAA,KAAA,MAAIF,UAAU,qDAAoDC,SAC/DwB,EAAae,MAAM,EAAG,GAAGrC,IAAI,CAACsC,EAAStB,KACtCpB,EAAAA,EAAAA,MAAA,MAAgBC,UAAU,mBAAkBC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,OAAMC,SAAC,YACvBC,EAAAA,EAAAA,KAAA,QAAAD,SAAOwC,MAFAtB,UASfpB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qGAAoGC,SAAA,EACjHF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZjB,EAAE,qBAAsB,yBAAyB,QAEpDgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mFAAkFC,SAAA,EAC/FF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,MAAU,YAAUlB,EAAE,WAAY,8BAE5CgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,MAAU,cAAYlB,EAAE,aAAc,6BAEhDgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,MAAU,cAAYlB,EAAE,aAAc,gCAEhDgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,MAAU,YAAUlB,EAAE,WAAY,gCAE5CgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,MAAU,cAAYlB,EAAE,YAAa,sCAQzD,OACEgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrElB,EAAE,mBAAoB,yBAGzBgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CAEvByB,EACC,iBACA3C,EAAE,iBAAkB,oBACpB,iBACA,kBAID2C,EACC,gBACA3C,EAAE,gBAAiB,mBACnB,gBACA,kBAIFmB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oGAAmGC,UAChHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uEACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,gEAA+DC,SAAA,CAC1ElB,EAAE,qBAAsB,wBAAwB,QAEnDgB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,WAAY,oEACrBgB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,WAAY,uEACrBgB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,WAAY,+DACrBgB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,WAAY,mFCuFrC,EAhQ6BJ,IAAsC,IAAD+D,EAAAC,EAAAC,EAAA,IAApC,SAAEhE,EAAQ,kBAAEC,GAAmBF,EAC3D,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAER6D,EAAwBA,CAACC,EAAUC,EAAWzD,KAClD,MAAM0D,EAAkBpE,EAASqE,cAAcH,IAAa,CAAC,EACvDI,GAAerD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAQmD,GAAe,IAAE,CAACD,GAAYzD,IAC3DT,EAAkB,iBAADiB,OAAkBgD,GAAYI,IAG3CC,EAAsB,CAC1B,CACEjE,IAAK,cACL0C,MAAO7C,EAAE,cAAe,gBACxBqE,KAAM,8BACNC,WAAY,CACV,CAAEnE,IAAK,KAAMC,MAAO,mBACpB,CAAED,IAAK,QAASC,MAAO,SACvB,CAAED,IAAK,OAAQC,MAAO,QACtB,CAAED,IAAK,UAAWC,MAAOJ,EAAE,UAAW,cAG1C,CACEG,IAAK,iBACL0C,MAAO7C,EAAE,sBAAuB,yBAChCqE,KAAM,mBACNC,WAAY,CACV,CAAEnE,IAAK,cAAeC,MAAOJ,EAAE,cAAe,gBAC9C,CAAEG,IAAK,MAAOC,MAAO,OACrB,CAAED,IAAK,cAAeC,MAAOJ,EAAE,cAAe,kBAGlD,CACEG,IAAK,aACL0C,MAAO7C,EAAE,aAAc,eACvBqE,KAAM,2BACNC,WAAY,CACV,CAAEnE,IAAK,OAAQC,MAAO,QACtB,CAAED,IAAK,eAAgBC,MAAOJ,EAAE,eAAgB,iBAChD,CAAEG,IAAK,MAAOC,MAAO,SAGzB,CACED,IAAK,qBACL0C,MAAO7C,EAAE,qBAAsB,uBAC/BqE,KAAM,iBACNC,WAAY,CACV,CAAEnE,IAAK,aAAcC,MAAOJ,EAAE,aAAc,eAC5C,CAAEG,IAAK,UAAWC,MAAOJ,EAAE,UAAW,YACtC,CAAEG,IAAK,oBAAqBC,MAAOJ,EAAE,oBAAqB,yBAG9D,CACEG,IAAK,sBACL0C,MAAO7C,EAAE,sBAAuB,wBAChCqE,KAAM,kBACNC,WAAY,CACV,CAAEnE,IAAK,YAAaC,MAAOJ,EAAE,YAAa,cAC1C,CAAEG,IAAK,iBAAkBC,MAAOJ,EAAE,iBAAkB,oBACpD,CAAEG,IAAK,iBAAkBC,MAAOJ,EAAE,iBAAkB,oBACpD,CAAEG,IAAK,oBAAqBC,MAAOJ,EAAE,oBAAqB,uBAC1D,CAAEG,IAAK,cAAeC,MAAOJ,EAAE,cAAe,gBAC9C,CAAEG,IAAK,MAAOC,MAAO,OACrB,CAAED,IAAK,MAAOC,MAAO,SAGzB,CACED,IAAK,qBACL0C,MAAO7C,EAAE,qBAAsB,uBAC/BqE,KAAM,oBACNC,WAAY,CACV,CAAEnE,IAAK,sBAAuBC,MAAOJ,EAAE,sBAAuB,0BAC9D,CAAEG,IAAK,aAAcC,MAAOJ,EAAE,aAAc,eAC5C,CAAEG,IAAK,YAAaC,MAAOJ,EAAE,YAAa,iBAG9C,CACEG,IAAK,iBACL0C,MAAO7C,EAAE,6BAA8B,oCACvCqE,KAAM,uBACNC,WAAY,CACV,CAAEnE,IAAK,aAAcC,MAAOJ,EAAE,aAAc,iBAC5C,CAAEG,IAAK,eAAgBC,MAAOJ,EAAE,eAAgB,kBAChD,CAAEG,IAAK,YAAaC,MAAOJ,EAAE,YAAa,iBAC1C,CAAEG,IAAK,yBAA0BC,MAAOJ,EAAE,yBAA0B,4BACpE,CAAEG,IAAK,oBAAqBC,MAAOJ,EAAE,oBAAqB,yBAG9D,CACEG,IAAK,mBACL0C,MAAO7C,EAAE,mBAAoB,qBAC7BqE,KAAM,mBACNC,WAAY,CACV,CAAEnE,IAAK,YAAaC,MAAOJ,EAAE,YAAa,iBAC1C,CAAEG,IAAK,UAAWC,MAAOJ,EAAE,UAAW,YACtC,CAAEG,IAAK,YAAaC,MAAOJ,EAAE,YAAa,gBAG9C,CACEG,IAAK,eACL0C,MAAO7C,EAAE,eAAgB,iBACzBqE,KAAM,iBACNC,WAAY,CACV,CAAEnE,IAAK,oBAAqBC,MAAOJ,EAAE,oBAAqB,yBAE5DuE,kBAAkB,GAEpB,CACEpE,IAAK,mBACL0C,MAAO7C,EAAE,mBAAoB,qBAC7BqE,KAAM,cACNC,WAAY,CACV,CAAEnE,IAAK,SAAUC,MAAOJ,EAAE,SAAU,YAEtCwE,gBAAgB,IAIdC,EAAuB,CAC3B,CAAElE,MAAO,MAAOH,MAAO,6BACvB,CAAEG,MAAO,MAAOH,MAAO,gCACvB,CAAEG,MAAO,KAAMH,MAAO,uBACtB,CAAEG,MAAO,MAAOH,MAAO,6BAGzB,OACEY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrElB,EAAE,gBAAiB,qBAGtBmB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDkD,EAAoBhD,IAAK2C,IAAQ,IAAAW,EAAAC,EAAAC,EAAA,OAChC5D,EAAAA,EAAAA,MAAA,OAAwBC,UAAU,6CAA4CC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yBAAwBC,SAAA,EACrCC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAF,OAAKgD,EAASM,KAAI,6CAC9BlD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChE6C,EAASlB,YAId7B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,CACvB6C,EAASO,WAAWlD,IAAK4C,IAAS,IAAAa,EAAA,OACjC7D,EAAAA,EAAAA,MAAA,SAA2BC,UAAU,oBAAmBC,SAAA,EACtDC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLE,SAA6C,QAApCoD,EAAAhF,EAASqE,cAAcH,EAAS5D,YAAI,IAAA0E,OAAA,EAApCA,EAAuCb,EAAU7D,QAAQ,EAClEuB,SAAWC,GAAMmC,EAAsBC,EAAS5D,IAAK6D,EAAU7D,IAAKwB,EAAEC,OAAOH,SAC7ER,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvD8C,EAAU5D,UARH4D,EAAU7D,OAcvB4D,EAASQ,mBAAwD,QAAxCG,EAAI7E,EAASqE,cAAcH,EAAS5D,YAAI,IAAAuE,OAAA,EAApCA,EAAsCI,qBAClE9D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,gBAAiB,kBAAkB,QAExCmB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBuD,EAAqBrD,IAAKC,IAAM,IAAA0D,EAAA,OAC/B/D,EAAAA,EAAAA,MAAA,SAA0BC,UAAU,oBAAmBC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLQ,KAAK,oBACLxB,MAAOc,EAAOd,MACdkB,SAA6C,QAApCsD,EAAAlF,EAASqE,cAAcH,EAAS5D,YAAI,IAAA4E,OAAA,EAApCA,EAAsCC,qBAAsB3D,EAAOd,MAC5EmB,SAAWC,GAAMmC,EAAsBC,EAAS5D,IAAK,oBAAqBwB,EAAEC,OAAOrB,OACnFU,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDG,EAAOjB,UAVAiB,EAAOd,cAmB1BwD,EAASS,iBAAsD,QAAxCG,EAAI9E,EAASqE,cAAcH,EAAS5D,YAAI,IAAAwE,OAAA,EAApCA,EAAsCM,UAChEjE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,cAAe,eAAe,QAEnCmB,EAAAA,EAAAA,KAAA,YACEZ,OAA2C,QAApCqE,EAAA/E,EAASqE,cAAcH,EAAS5D,YAAI,IAAAyE,OAAA,EAApCA,EAAsC5C,cAAe,GAC5DN,SAAWC,GAAMmC,EAAsBC,EAAS5D,IAAK,cAAewB,EAAEC,OAAOrB,OAC7E0B,KAAM,EACNhB,UAAU,kKACViB,YAAalC,EAAE,wBAAyB,mDA5DxC+D,EAAS5D,UAsEvBa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,EAC7GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZjB,EAAE,oBAAqB,0BAE1BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,kBAAmB,wBAExBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACL6B,IAAI,IACJC,IAAI,IACJ9C,OAAiC,QAA1BoD,EAAA9D,EAASqF,yBAAiB,IAAAvB,OAAA,EAA1BA,EAA4BwB,kBAAmB,GACtDzD,SAAWC,GAAM7B,EAAkB,oCAAqC6B,EAAEC,OAAOrB,OACjFU,UAAU,kKACViB,YAAY,YAGhBlB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,kBAAmB,iCAExBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACL6B,IAAI,KACJC,IAAI,MACJ9C,OAAiC,QAA1BqD,EAAA/D,EAASqF,yBAAiB,IAAAtB,OAAA,EAA1BA,EAA4BwB,kBAAmB,GACtD1D,SAAWC,GAAM7B,EAAkB,oCAAqC6B,EAAEC,OAAOrB,OACjFU,UAAU,kKACViB,YAAY,cAGhBlB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,aAAc,4BAEnBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACL6B,IAAI,IACJC,IAAI,KACJ9C,OAAiC,QAA1BsD,EAAAhE,EAASqF,yBAAiB,IAAArB,OAAA,EAA1BA,EAA4BwB,aAAc,GACjD3D,SAAWC,GAAM7B,EAAkB,+BAAgC6B,EAAEC,OAAOrB,OAC5EU,UAAU,kKACViB,YAAY,sBCpC1B,EAnN0BtC,IAA8C,IAAD0F,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAA5C,SAAE/F,EAAQ,kBAAEC,EAAiB,OAAEC,GAAQH,EAChE,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAEd,OACEe,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrElB,EAAE,uBAAwB,iCAG7BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLE,QAAS5B,EAASgG,0BAA2B,EAC7CnE,SAAWC,GAAM7B,EAAkB,0BAA2B6B,EAAEC,OAAOH,SACvER,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SACnElB,EAAE,0BAA2B,4CAGlCmB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qDAAoDC,SAC9DlB,EAAE,wBAAyB,iIAKhCgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBACZjB,EAAE,qBAAsB,2BAG3BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,gBAAiB,kBAAkB,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAExEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,OAAkC,QAA3B+E,EAAAzF,EAASiG,0BAAkB,IAAAR,OAAA,EAA3BA,EAA6BvD,OAAQ,GAC5CL,SAAWC,GAAM7B,EAAkB,0BAA2B6B,EAAEC,OAAOrB,OACvEU,UAAS,mJAAAF,OACPhB,EAAOgG,cAAgB,iBAAmB,mBAE5C7D,YAAalC,EAAE,qBAAsB,+BAEtCD,EAAOgG,gBACN5E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAOgG,oBAIrD/E,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,cAAe,aAAa,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEjEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,OAAkC,QAA3BgF,EAAA1F,EAASiG,0BAAkB,IAAAP,OAAA,EAA3BA,EAA6BS,UAAW,GAC/CtE,SAAWC,GAAM7B,EAAkB,6BAA8B6B,EAAEC,OAAOrB,OAC1EU,UAAS,mJAAAF,OACPhB,EAAOkG,eAAiB,iBAAmB,mBAE7C/D,YAAalC,EAAE,mBAAoB,wBAEpCD,EAAOkG,iBACN9E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAOkG,qBAIrDjF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,OAAQ,WAEbmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,OAAkC,QAA3BiF,EAAA3F,EAASiG,0BAAkB,IAAAN,OAAA,EAA3BA,EAA6BU,OAAQ,GAC5CxE,SAAWC,GAAM7B,EAAkB,0BAA2B6B,EAAEC,OAAOrB,OACvEU,UAAU,2KAMhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,mBAAoB,wBAEzBgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SACpDlB,EAAE,8BAA+B,8CAEpCgB,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACLN,UAAU,uFAAsFC,SAAA,EAEhGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZjB,EAAE,eAAgB,8BAO3BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gGAA+FC,SAAA,EAC5GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,gEAA+DC,SAAA,EAC3EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wBACZjB,EAAE,kBAAmB,wBAIxBgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oEAAmEC,SACjFlB,EAAE,qBAAsB,0BAE3BmB,EAAAA,EAAAA,KAAA,YACEZ,OAA+B,QAAxBkF,EAAA5F,EAASsG,uBAAe,IAAAV,OAAA,EAAxBA,EAA0BW,YAAa,GAC9C1E,SAAWC,GAAM7B,EAAkB,4BAA6B6B,EAAEC,OAAOrB,OACzE0B,KAAM,EACNhB,UAAU,uKACViB,YAAalC,EAAE,gCAAiC,wFAIpDgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oEAAmEC,SACjFlB,EAAE,gBAAiB,qBAEtBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,OAA+B,QAAxBmF,EAAA7F,EAASsG,uBAAe,IAAAT,OAAA,EAAxBA,EAA0BW,YAAa,GAC9C3E,SAAWC,GAAM7B,EAAkB,4BAA6B6B,EAAEC,OAAOrB,OACzEU,UAAU,uKACViB,YAAalC,EAAE,qBAAsB,8BAIzCgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oEAAmEC,SACjFlB,EAAE,cAAe,gBAEpBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,OAA+B,QAAxBoF,EAAA9F,EAASsG,uBAAe,IAAAR,OAAA,EAAxBA,EAA0BK,UAAW,GAC5CtE,SAAWC,GAAM7B,EAAkB,0BAA2B6B,EAAEC,OAAOrB,OACvEU,UAAU,uKACViB,YAAalC,EAAE,mBAAoB,4BAIvCgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oEAAmEC,SACjFlB,EAAE,aAAc,kBAEnBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,OAA+B,QAAxBqF,EAAA/F,EAASsG,uBAAe,IAAAP,OAAA,EAAxBA,EAA0BM,OAAQ,GACzCxE,SAAWC,GAAM7B,EAAkB,uBAAwB6B,EAAEC,OAAOrB,OACpEU,UAAU,gLAMhBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oEAAmEC,SACjFlB,EAAE,4BAA6B,kCAElCgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2FAA0FC,SAAA,EACvGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAA4CC,SACtDlB,EAAE,gCAAiC,wDAEtCgB,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACLN,UAAU,yFAAwFC,SAAA,EAElGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oBACZjB,EAAE,wBAAyB,wCAOpCmB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oGAAmGC,UAChHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uEACbD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gEAA+DC,SAC1ElB,EAAE,mBAAoB,gCAEzBgB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,yDAAwDC,SAAA,EACpEF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,kBAAmB,uEAC5BgB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,iBAAkB,yDAC3BgB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,kBAAmB,sDAC5BgB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,uBAAwB,4DACjCgB,EAAAA,EAAAA,MAAA,MAAAE,SAAA,CAAI,UAAGlB,EAAE,aAAc,wEC4dvC,EA/pBuBJ,IAOhB,IAPiB,UACtB0G,EAAS,YACTC,EAAW,mBACXC,EAAkB,YAClBC,EAAc,CAAC,EAAC,OAChBC,EAAM,SACNC,GACD/G,EACC,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KACR2G,GAAWC,EAAAA,EAAAA,OACTP,UAAWQ,EAAY,OAAEC,IAAWC,EAAAA,EAAAA,MACrCC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAChCpH,EAAQqH,IAAaD,EAAAA,EAAAA,UAAS,CAAC,IAC/BE,EAASC,IAAcH,EAAAA,EAAAA,UAAS,MAGjCI,EAAkBjB,GAAaQ,GAG9BjH,EAAU2H,IAAeL,EAAAA,EAAAA,UAAS,CAEvCM,eAAgB,MAChBC,WAAW,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC/CC,QAAS,KACTC,aAAc,KAGdC,YAAa,GACbC,SAAU,GACVC,UAAW,GACXC,UAAW,GACXC,UAAW,GAGXxH,mBAAoB,CAClByH,KAAM,CAAE5G,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC/CwG,eAAgB,CAAE7G,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACzDyG,eAAgB,CAAE9G,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACzD0G,aAAc,CAAE/G,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACvD2G,aAAc,CAAEhH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACvD4G,oBAAqB,CAAEjH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC9D6G,oBAAqB,CAAElH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC9D8G,kBAAmB,CAAEnH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC5D+G,aAAc,CAAEpH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACvDgH,iBAAkB,CAAErH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC3DiH,cAAe,CAAEtH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACxDkH,cAAe,CAAEvH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACxDmH,QAAS,CAAExH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAClDoH,eAAgB,CAAEzH,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACzDqH,UAAW,CAAE1H,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACpDsH,iBAAkB,CAAE3H,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC3DuH,iBAAkB,CAAE5H,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC3DwH,sBAAuB,CAAE7H,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAChEyH,YAAa,CAAE9H,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACtD0H,sBAAuB,CAAE/H,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAChE2H,mBAAoB,CAAEhI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC7D4H,qBAAsB,CAAEjI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC/D6H,aAAc,CAAElI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACvD8H,mBAAoB,CAAEnI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC7D+H,mBAAoB,CAAEpI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC7DgI,oBAAqB,CAAErI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC9DiI,mBAAoB,CAAEtI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC7DkI,mBAAoB,CAAEvI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC7DmI,kBAAmB,CAAExI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC5DoI,mBAAoB,CAAEzI,SAAS,EAAOO,YAAa,GAAIF,KAAM,IAC7DqI,YAAa,CAAE1I,SAAS,EAAOO,YAAa,GAAIF,KAAM,IACtDsI,MAAO,CAAE3I,SAAS,EAAOO,YAAa,GAAIF,KAAM,KAIlDK,YAAa,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAGlCkI,eAAgB,CACdlH,MAAO,GACPD,MAAO,CAAC,GAAI,GAAI,GAAI,KAEtBoH,cAAe,CACbnH,MAAO,GACPD,MAAO,CAAC,GAAI,GAAI,GAAI,KAItBgB,cAAe,CACbqG,YAAa,CACXC,IAAI,EACJC,OAAO,EACPC,MAAM,EACNC,SAAS,GAEXC,eAAgB,CACdC,aAAa,EACbC,KAAK,EACLC,aAAa,GAEfC,WAAY,CACVC,MAAM,EACNC,cAAc,EACdC,KAAK,GAEPC,mBAAoB,CAClBC,YAAY,EACZV,SAAS,EACTW,mBAAmB,GAErBC,oBAAqB,CACnBC,WAAW,EACXC,gBAAgB,EAChBC,gBAAgB,EAChBC,mBAAmB,EACnBC,aAAa,EACbC,KAAK,EACLC,KAAK,GAEPC,mBAAoB,CAClBC,qBAAqB,EACrBC,YAAY,EACZC,WAAW,GAEbC,eAAgB,CACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,wBAAwB,EACxBC,mBAAmB,GAErBC,iBAAkB,CAChBC,WAAW,EACXC,SAAS,EACTC,WAAW,GAEbC,aAAc,CACZ/H,mBAAmB,EACnBgI,cAAe,GACf9H,kBAAmB,IAErB+H,iBAAkB,CAChB9H,QAAQ,EACRjD,YAAa,KAKjB6D,yBAAyB,EACzBC,mBAAoB,CAClB/D,KAAM,GACNiE,QAAS,GACTE,MAAM,IAAIyB,MAAOC,cAAcC,MAAM,KAAK,IAE5C1B,gBAAiB,CACfC,UAAW,iFACXC,UAAW,GACXL,QAAS,GACTE,KAAM,OAKV8G,EAAAA,EAAAA,WAAU,KACR,GAAIxG,GAAsBD,EAAa,CAErC,MAAMc,EAAU,CACd7F,GAAI+E,EAAY0G,KAAO1G,EAAY/E,GACnCO,KAAMwE,EAAYxE,MAAI,GAAAhB,OAAOwF,EAAY2G,UAAS,KAAAnM,OAAIwF,EAAY4G,UAClEC,OAAQ7G,EAAY6G,QAAM,GAAArM,OAAOwF,EAAY2G,UAAS,KAAAnM,OAAIwF,EAAY4G,UACtElF,SAAU1B,EAAY0B,UAAY1B,EAAY0G,IAC9CI,YAAa9G,EAAY8G,YACzBC,IAAK/G,EAAY+G,IACjBC,OAAQhH,EAAYgH,OACpBC,WAAYjH,EAAYiH,WACxBC,MAAOlH,EAAYkH,MACnBC,YAAanH,EAAYmH,YACzBC,QAASpH,EAAYoH,SAGvBrG,EAAWD,GAGXG,EAAYoG,IAAQ,IAAAC,EAAAC,EAAA,OAAAhN,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACf8M,GAAQ,IACX5F,YAAaX,EAAQ+F,OACrBnF,SAAUZ,EAAQY,SAClBoF,YAAahG,EAAQgG,YACrBC,KAAgB,QAAXO,EAAAxG,EAAQiG,WAAG,IAAAO,OAAA,EAAXA,EAAaE,aAAc,GAChCR,OAAQlG,EAAQkG,OAChBrF,WAAqC,QAA1B4F,EAAAvH,EAAYyH,sBAAc,IAAAF,OAAA,EAA1BA,EAA4BG,mBAAoB,IACxDxH,KAGLS,GAAW,EACb,MAAWK,IACTL,GAAW,GAEXgH,WAAW,KACT,MAAMC,EAAc,CAClB3M,GAAI+F,EACJxF,KAAM,uEACNqL,OAAQ,qBACRnF,SAAU,cACVoF,YAAa,aACbC,IAAK,EACLC,OAAQ,OACRC,WAAY,aACZC,MAAO,mBACPC,YAAa,QACbC,QAAS,qLAGXrG,EAAW6G,GAGX3G,EAAYoG,IAAQ9M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACf8M,GAAQ,IACX5F,YAAamG,EAAYf,OACzBnF,SAAUkG,EAAYlG,YAGxBf,GAAW,IACV,OAEJ,CAACK,EAAiBf,EAAoBD,EAAaE,KAGtDuG,EAAAA,EAAAA,WAAU,KACJvG,GAAe2H,OAAOC,KAAK5H,GAAajD,OAAS,GACnDgE,EAAYoG,IAAQ9M,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACf8M,GACAnH,KAGN,CAACA,IAEJ,MAAM3G,EAAoBA,CAACY,EAAOH,KAChC,MAAM+N,GAAOxN,EAAAA,EAAAA,GAAA,GAAQjB,GAGrB,GAAIa,EAAM6N,SAAS,KAAM,CACvB,MAAMC,EAAQ9N,EAAMmH,MAAM,KAC1B,IAAI4G,EAAUH,EACd,IAAK,IAAII,EAAI,EAAGA,EAAIF,EAAMhL,OAAS,EAAGkL,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMhL,OAAS,IAAMjD,CACrC,MACE+N,EAAQ5N,GAASH,EAGnBiH,EAAY8G,GAGRvO,EAAOW,IACT0G,EAAUuH,IAAI7N,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAU6N,GAAI,IAAE,CAACjO,GAAQ,SAqI3C,OAAIuG,IAAYI,GAEZlG,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAMnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,QAAM4N,SA5DWC,UAGnB,GAFAlN,EAAEmN,iBAvDiBC,MACnB,MAAMC,EAAY,CAAC,EAGdnP,EAASmI,YAAYiH,SACxBD,EAAUhH,YAAchI,EAAE,sBAAuB,6BAE9CH,EAASoI,SAASgH,SACrBD,EAAU/G,SAAWjI,EAAE,mBAAoB,0BAExCH,EAASqI,UAAU+G,SACtBD,EAAU9G,UAAYlI,EAAE,oBAAqB,0BAE1CH,EAASsI,YACZ6G,EAAU7G,UAAYnI,EAAE,oBAAqB,2BAE1CH,EAASuI,UAAU6G,SACtBD,EAAU5G,UAAYpI,EAAE,oBAAqB,4BAI1CH,EAASwK,eAAelH,OAAStD,EAASwK,eAAelH,MAAQ,KACpE6L,EAAUE,eAAiBlP,EAAE,yBAA0B,0CAEpDH,EAASyK,cAAcnH,OAAStD,EAASyK,cAAcnH,MAAQ,KAClE6L,EAAUG,cAAgBnP,EAAE,wBAAyB,uCAEnDH,EAASyK,cAAcnH,OAAStD,EAASwK,eAAelH,QAC1D6L,EAAUG,cAAgBnP,EAAE,6BAA8B,0DAI5D,MAAMoP,EAAmBvP,EAASwK,eAAenH,MAAMmM,KAAK9L,GAAQA,EAAK0L,QACnEK,EAAkBzP,EAASyK,cAAcpH,MAAMmM,KAAK9L,GAAQA,EAAK0L,QAkBvE,OAhBKG,IACHJ,EAAU3E,eAAiBrK,EAAE,0BAA2B,6CAErDsP,IACHN,EAAU1E,cAAgBtK,EAAE,yBAA0B,4CAInDH,EAASiG,mBAAmB/D,KAAKkN,SACpCD,EAAUjJ,cAAgB/F,EAAE,wBAAyB,+BAElDH,EAASiG,mBAAmBE,QAAQiJ,SACvCD,EAAU/I,eAAiBjG,EAAE,yBAA0B,uCAGzDoH,EAAU4H,GAC+B,IAAlCZ,OAAOC,KAAKW,GAAWxL,QAMzBuL,GAAL,CAKA7H,GAAW,GAEX,IAIE,SAFM,IAAIqI,QAAQC,GAAWtB,WAAWsB,EAAS,MAE7C9I,EACFA,EAAO7G,OACF,CAEL,MAAM4P,EAAaC,KAAKC,MAAMC,aAAaC,QAAQ,mBAAqB,MAClEC,GAAOhP,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACRjB,GAAQ,IACX2B,GAAImG,KAAKoI,MACTzJ,UAAWiB,EACXyI,WAAW,IAAIrI,MAAOC,cACtBqI,WAAW,IAAItI,MAAOC,gBAExB6H,EAAWS,KAAKJ,GAChBF,aAAaO,QAAQ,iBAAkBT,KAAKU,UAAUX,IAEtDY,EAAAA,GAAMC,QAAQtQ,EAAE,kBAAmB,oCAGjC4G,EADEW,EACO,aAADxG,OAAcwG,GAEb,YAEb,CACF,CAAE,MAAOgJ,GACPC,QAAQD,MAAM,6BAA8BA,GAC5CF,EAAAA,GAAME,MAAMvQ,EAAE,kBAAmB,6BACnC,CAAC,QACCkH,GAAW,EACb,CApCA,MAFEmJ,EAAAA,GAAME,MAAMvQ,EAAE,kBAAmB,6CAwDHiB,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7DlB,EAAE,oBAAqB,6CACvBuH,GAAmBF,IAClBrG,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4DAA2DC,SAAA,CAAC,KACvEmG,EAAQ+F,QAAU/F,EAAQtF,YAInCZ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDlB,EAAE,wBAAyB,uEAI9BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qFAAoFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAC,uBAEzEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yDAAwDC,SAAC,wBAE3EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yFAAwFC,SAAA,EACrGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SAAC,2BAKjFF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,CAC5BqG,IACCvG,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACL+B,QAASA,IAAMsD,EAAS,aAAD7F,OAAcwG,IACrCtG,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZjB,EAAE,cAAe,oBAGtBgB,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACL+B,QA9DUmN,KAEtBJ,EAAAA,GAAMC,QAAQtQ,EAAE,cAAe,+BA6DnBiB,UAAU,oFAAmFC,SAAA,EAE7FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZjB,EAAE,YAAa,kBAElBmB,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACL+B,QAASqD,GAAQ,KAAWC,EAASW,EAAe,aAAAxG,OAAgBwG,GAAoB,cACxFtG,UAAU,uFAAsFC,SAE/FlB,EAAE,SAAU,qBAOrBgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrElB,EAAE,sBAAuB,2BAE5BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,iBAAkB,sBAEvBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAAS4H,eAChB/F,SAAWC,GAAM7B,EAAkB,iBAAkB6B,EAAEC,OAAOrB,OAC9DU,UAAU,kKACViB,YAAY,YAGhBlB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,YAAa,iBAElBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAAS6H,UAChBhG,SAAWC,GAAM7B,EAAkB,YAAa6B,EAAEC,OAAOrB,OACzDU,UAAU,wKAGdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,UAAW,cAEhBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAASiI,QAChBpG,SAAWC,GAAM7B,EAAkB,UAAW6B,EAAEC,OAAOrB,OACvDU,UAAU,kKACViB,YAAY,WAGhBlB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/ElB,EAAE,eAAgB,oBAErBmB,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAASkI,aAChBrG,SAAWC,GAAM7B,EAAkB,eAAgB6B,EAAEC,OAAOrB,OAC5DU,UAAU,kKACViB,YAAY,iBAOpBlB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrElB,EAAE,qBAAsB,0BAE3BgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,cAAe,gBAAgB,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAASmI,YAChBtG,SAAWC,GAAM7B,EAAkB,cAAe6B,EAAEC,OAAOrB,OAC3DU,UAAS,mJAAAF,OACPhB,EAAOiI,YAAc,iBAAmB,mBAE1C9F,YAAalC,EAAE,mBAAoB,wBAEpCD,EAAOiI,cACN7G,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAOiI,kBAGrDhH,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,WAAY,QAAQ,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEzDC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAASoI,SAChBvG,SAAWC,GAAM7B,EAAkB,WAAY6B,EAAEC,OAAOrB,OACxDU,UAAS,mJAAAF,OACPhB,EAAOkI,SAAW,iBAAmB,mBAEvC/F,YAAalC,EAAE,gBAAiB,qBAEjCD,EAAOkI,WACN9G,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAOkI,eAGrDjH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,YAAa,aAAa,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DC,EAAAA,EAAAA,KAAA,YACEZ,MAAOV,EAASqI,UAChBxG,SAAWC,GAAM7B,EAAkB,YAAa6B,EAAEC,OAAOrB,OACzD0B,KAAM,EACNhB,UAAS,mJAAAF,OACPhB,EAAOmI,UAAY,iBAAmB,mBAExChG,YAAalC,EAAE,iBAAkB,qBAElCD,EAAOmI,YACN/G,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAOmI,gBAGrDlH,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,YAAa,cAAc,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEhEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAASsI,UAChBzG,SAAWC,GAAM7B,EAAkB,YAAa6B,EAAEC,OAAOrB,OACzD8C,KAAK,IAAIsE,MAAOC,cAAcC,MAAM,KAAK,GACzC5G,UAAS,mJAAAF,OACPhB,EAAOoI,UAAY,iBAAmB,qBAGzCpI,EAAOoI,YACNhH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAOoI,gBAGrDnH,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/ElB,EAAE,YAAa,aAAa,KAACmB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOV,EAASuI,UAChB1G,SAAWC,GAAM7B,EAAkB,YAAa6B,EAAEC,OAAOrB,OACzDU,UAAS,mJAAAF,OACPhB,EAAOqI,UAAY,iBAAmB,mBAExClG,YAAalC,EAAE,qBAAsB,0BAEtCD,EAAOqI,YACNjH,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEnB,EAAOqI,sBAOzDjH,EAAAA,EAAAA,KAACuP,EAAyB,CACxB7Q,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,KAIVoB,EAAAA,EAAAA,KAACwP,EAAY,CACX9Q,SAAUA,EACVC,kBAAmBA,EACnByC,kBA5WkBA,CAAC7B,EAAO0B,EAAO7B,KACvC,MAAM+N,GAAOxN,EAAAA,EAAAA,GAAA,GAAQjB,GACf2O,EAAQ9N,EAAMmH,MAAM,KAC1B,IAAI4G,EAAUH,EAEd,IAAK,IAAII,EAAI,EAAGA,EAAIF,EAAMhL,OAAS,EAAGkL,IACpCD,EAAUA,EAAQD,EAAME,IAG1BD,EAAQD,EAAMA,EAAMhL,OAAS,IAAIpB,GAAS7B,EAC1CiH,EAAY8G,IAmWN9L,QAhWSI,IACf,MAAM0L,GAAOxN,EAAAA,EAAAA,GAAA,GAAQjB,GACrByO,EAAQ1L,GAAUM,MAAMgN,KAAK,IAC7B1I,EAAY8G,IA8VN7L,WA3VWA,CAACG,EAAUR,KAC5B,MAAMkM,GAAOxN,EAAAA,EAAAA,GAAA,GAAQjB,GACrByO,EAAQ1L,GAAUM,MAAM0N,OAAOxO,EAAO,GACtCoF,EAAY8G,IAyVNvO,OAAQA,KAIVoB,EAAAA,EAAAA,KAAC0P,EAAoB,CACnBhR,SAAUA,EACVC,kBAAmBA,KAIrBqB,EAAAA,EAAAA,KAAC2P,EAAiB,CAChBjR,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,KAIViB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACL+B,QAASqD,GAAQ,KAAWC,EAASW,EAAe,aAAAxG,OAAgBwG,GAAoB,cACxFtG,UAAU,+FAA8FC,SAEvGlB,EAAE,SAAU,aAEfmB,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACLwP,SAAU9J,EACVhG,UAAU,kIAAiIC,SAE1I+F,GACCjG,EAAAA,EAAAA,MAAAgQ,EAAAA,SAAA,CAAA9P,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZjB,EAAE,SAAU,iBAGfgB,EAAAA,EAAAA,MAAAgQ,EAAAA,SAAA,CAAA9P,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZjB,EAAE,iBAAkB,kC", "sources": ["components/PlanOfCare/FunctionalProblemsSection.jsx", "components/PlanOfCare/GoalsSection.jsx", "components/PlanOfCare/TreatmentPlanSection.jsx", "components/PlanOfCare/SignaturesSection.jsx", "components/PlanOfCare/PlanOfCareForm.jsx"], "sourcesContent": ["import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst FunctionalProblemsSection = ({ formData, handleInputChange, errors }) => {\n  const { t } = useLanguage();\n\n  const functionalProblemsOptions = [\n    { key: 'pain', label: t('pain', 'Pain'), hasSide: false },\n    { key: 'bedMatMobility', label: t('bedMatMobilityStatus', '↓ Bed/mat mobility status'), hasSide: false },\n    { key: 'transferStatus', label: t('transferStatus', '↓ Transfer status'), hasSide: false },\n    { key: 'limitedUEROM', label: t('limitedUEROM', 'Limited R/L/B UE ROM'), hasSide: true },\n    { key: 'limitedLEROM', label: t('limitedLEROM', 'Limited R/L/B LE ROM'), hasSide: true },\n    { key: 'decreasedLEStrength', label: t('decreasedLEStrength', '↓ R/L/B LE strength'), hasSide: true },\n    { key: 'decreasedUEStrength', label: t('decreasedUEStrength', '↓ R/L/B UE strength'), hasSide: true },\n    { key: 'neckTrunkStrength', label: t('neckTrunkStrength', '↓ Neck/trunk strength'), hasSide: false },\n    { key: 'abnormalTone', label: t('abnormalTone', 'Abnormal tone'), hasSide: true },\n    { key: 'abnormalMovement', label: t('abnormalMovement', 'Abnormal movement'), hasSide: true },\n    { key: 'skinBreakdown', label: t('skinBreakdown', 'Skin breakdown'), hasSide: false },\n    { key: 'gaitAsymmetry', label: t('gaitAsymmetry', 'Gait asymmetry altered Gait pattern'), hasSide: false },\n    { key: 'atrophy', label: t('atrophy', 'Atrophy'), hasSide: true },\n    { key: 'muscleWeakness', label: t('muscleWeakness', 'Muscle weakness'), hasSide: true },\n    { key: 'imbalance', label: t('imbalance', 'Imbalance/poor balance'), hasSide: false },\n    { key: 'lackCoordination', label: t('lackCoordination', 'Lack of coordination'), hasSide: true },\n    { key: 'visualPerception', label: t('visualPerception', '↓ Visual perception'), hasSide: false },\n    { key: 'softTissueDysfunction', label: t('softTissueDysfunction', 'Soft tissue dysfunction'), hasSide: true },\n    { key: 'poorPosture', label: t('poorPosture', 'Poor posture/abnormal posture'), hasSide: false },\n    { key: 'improperBodyMechanics', label: t('improperBodyMechanics', 'Improper body mechanics'), hasSide: false },\n    { key: 'wheelchairMobility', label: t('wheelchairMobility', '↓ W/C mobility'), hasSide: false },\n    { key: 'difficultyAmbulating', label: t('difficultyAmbulating', 'Difficulty ambulating'), hasSide: false },\n    { key: 'abnormalGait', label: t('abnormalGait', 'Abnormal gait'), hasSide: false },\n    { key: 'decreasedEndurance', label: t('decreasedEndurance', '↓ Endurance'), hasSide: false },\n    { key: 'decreasedSensation', label: t('decreasedSensation', '↓ Sensation/proprioception'), hasSide: true },\n    { key: 'respiratoryCapacity', label: t('respiratoryCapacity', '↓ Respiratory capacity'), hasSide: false },\n    { key: 'fineMotorDexterity', label: t('fineMotorDexterity', '↓ Fine motor/dexterity'), hasSide: true },\n    { key: 'functionalActivity', label: t('functionalActivity', 'Decreased functional activity: ADL/work skills'), hasSide: false },\n    { key: 'jointHypomobility', label: t('jointHypomobility', 'Joint hypomobility'), hasSide: true },\n    { key: 'jointHypermobility', label: t('jointHypermobility', 'Joint hypermobility'), hasSide: true },\n    { key: 'contracture', label: t('contracture', 'Contracture'), hasSide: true },\n    { key: 'other', label: t('other', 'Other'), hasSide: false }\n  ];\n\n  const sideOptions = [\n    { value: 'R', label: t('right', 'R') },\n    { value: 'L', label: t('left', 'L') },\n    { value: 'B', label: t('bilateral', 'B') }\n  ];\n\n  const handleProblemChange = (problemKey, field, value) => {\n    const currentProblem = formData.functionalProblems[problemKey] || {};\n    const updatedProblem = { ...currentProblem, [field]: value };\n    handleInputChange(`functionalProblems.${problemKey}`, updatedProblem);\n  };\n\n  const handleTopProblemChange = (index, value) => {\n    const newTopProblems = [...formData.topProblems];\n    newTopProblems[index] = value;\n    handleInputChange('topProblems', newTopProblems);\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n        {t('functionalProblems', 'Patient Problems (Reason for Referral)')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Functional Problems Checkboxes */}\n        <div>\n          <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n            {t('functionalProblemsImpairments', 'Functional Problems & Impairments')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {functionalProblemsOptions.map((option) => {\n              const problem = formData.functionalProblems[option.key] || {};\n              \n              return (\n                <div key={option.key} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-start space-x-3\">\n                    <input\n                      type=\"checkbox\"\n                      id={option.key}\n                      checked={problem.checked || false}\n                      onChange={(e) => handleProblemChange(option.key, 'checked', e.target.checked)}\n                      className=\"mt-1\"\n                    />\n                    <div className=\"flex-1\">\n                      <label htmlFor={option.key} className=\"text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer\">\n                        {option.label}\n                      </label>\n                      \n                      {/* Side selection for applicable problems */}\n                      {option.hasSide && problem.checked && (\n                        <div className=\"mt-2\">\n                          <label className=\"block text-xs text-gray-600 dark:text-gray-400 mb-1\">\n                            {t('side', 'Side')}:\n                          </label>\n                          <div className=\"flex space-x-2\">\n                            {sideOptions.map((side) => (\n                              <label key={side.value} className=\"flex items-center\">\n                                <input\n                                  type=\"radio\"\n                                  name={`${option.key}_side`}\n                                  value={side.value}\n                                  checked={problem.side === side.value}\n                                  onChange={(e) => handleProblemChange(option.key, 'side', e.target.value)}\n                                  className=\"mr-1\"\n                                />\n                                <span className=\"text-xs text-gray-600 dark:text-gray-400\">{side.label}</span>\n                              </label>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n                      \n                      {/* Description field when checked */}\n                      {problem.checked && (\n                        <div className=\"mt-2\">\n                          <label className=\"block text-xs text-gray-600 dark:text-gray-400 mb-1\">\n                            {t('specificDescription', 'Specific Description / Objective Measure')}:\n                          </label>\n                          <textarea\n                            value={problem.description || ''}\n                            onChange={(e) => handleProblemChange(option.key, 'description', e.target.value)}\n                            rows={2}\n                            className=\"w-full px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                            placeholder={t('enterMeasurableDescription', 'Enter measurable description (e.g., Pain score 7/10)')}\n                          />\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Top 6 Problems Summary */}\n        <div>\n          <h3 className=\"text-md font-medium text-gray-900 dark:text-white mb-3\">\n            {t('topSixProblems', 'Top 6 Problems Summary')}\n          </h3>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-3\">\n            {t('topProblemsDescription', 'Summarize the most important problems from the selections above')}\n          </p>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {formData.topProblems.map((problem, index) => (\n              <div key={index}>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {index + 1}. {t('problem', 'Problem')} {index + 1}\n                </label>\n                <textarea\n                  value={problem}\n                  onChange={(e) => handleTopProblemChange(index, e.target.value)}\n                  rows={2}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterProblemSummary', 'Enter problem summary')}\n                />\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FunctionalProblemsSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst GoalsSection = ({ formData, handleInputChange, handleArrayChange, addGoal, removeGoal, errors }) => {\n  const { t } = useLanguage();\n\n  const goalExamples = [\n    t('goalExample1', 'Patient will demonstrate improved balance by standing on one foot for 30 seconds without support'),\n    t('goalExample2', 'Patient will increase shoulder flexion ROM from 90° to 150° within treatment period'),\n    t('goalExample3', 'Patient will walk 100 meters independently without assistive device'),\n    t('goalExample4', 'Patient will demonstrate proper body mechanics during lifting activities'),\n    t('goalExample5', 'Patient will achieve pain level of 3/10 or less during functional activities')\n  ];\n\n  const renderGoalSection = (goalType, title, weekField, goalsField) => {\n    const goals = formData[goalType]?.goals || [];\n    const weeks = formData[goalType]?.weeks || '';\n    \n    return (\n      <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6\">\n        <h3 className=\"text-md font-semibold text-gray-900 dark:text-white mb-4\">{title}</h3>\n        \n        {/* Weeks Input */}\n        <div className=\"mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            {t('weeks', 'Weeks')} <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"number\"\n            min=\"1\"\n            max=\"52\"\n            value={weeks}\n            onChange={(e) => handleInputChange(`${goalType}.weeks`, e.target.value)}\n            className={`w-32 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white ${\n              errors[weekField] ? 'border-red-500' : 'border-gray-300'\n            }`}\n            placeholder=\"1-52\"\n          />\n          {errors[weekField] && (\n            <p className=\"text-red-500 text-sm mt-1\">{errors[weekField]}</p>\n          )}\n        </div>\n\n        {/* Goals List */}\n        <div className=\"space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {t('goals', 'Goals')} <span className=\"text-red-500\">*</span>\n            </label>\n            <button\n              type=\"button\"\n              onClick={() => addGoal(goalType)}\n              className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-1\"></i>\n              {t('addGoal', 'Add Goal')}\n            </button>\n          </div>\n          \n          {goals.map((goal, index) => (\n            <div key={index} className=\"relative\">\n              <div className=\"flex items-start space-x-2\">\n                <span className=\"text-sm font-medium text-gray-600 dark:text-gray-400 mt-2 min-w-[20px]\">\n                  {index + 1}.\n                </span>\n                <div className=\"flex-1\">\n                  <textarea\n                    value={goal}\n                    onChange={(e) => handleArrayChange(`${goalType}.goals`, index, e.target.value)}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white\"\n                    placeholder={t('enterSMARTGoal', 'Enter SMART goal (Specific, Measurable, Achievable, Relevant, Time-bound)')}\n                  />\n                </div>\n                {goals.length > 1 && (\n                  <button\n                    type=\"button\"\n                    onClick={() => removeGoal(goalType, index)}\n                    className=\"mt-2 p-1 text-red-600 hover:text-red-800 transition-colors\"\n                    title={t('removeGoal', 'Remove Goal')}\n                  >\n                    <i className=\"fas fa-trash text-sm\"></i>\n                  </button>\n                )}\n              </div>\n            </div>\n          ))}\n          \n          {errors[goalsField] && (\n            <p className=\"text-red-500 text-sm\">{errors[goalsField]}</p>\n          )}\n        </div>\n\n        {/* Goal Examples */}\n        <div className=\"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2\">\n            <i className=\"fas fa-lightbulb mr-1\"></i>\n            {t('goalExamples', 'Goal Examples')}:\n          </h4>\n          <ul className=\"text-xs text-blue-800 dark:text-blue-200 space-y-1\">\n            {goalExamples.slice(0, 3).map((example, index) => (\n              <li key={index} className=\"flex items-start\">\n                <span className=\"mr-2\">•</span>\n                <span>{example}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        {/* SMART Goals Reminder */}\n        <div className=\"mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\n          <h4 className=\"text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\n            <i className=\"fas fa-check-circle mr-1\"></i>\n            {t('smartGoalsReminder', 'SMART Goals Checklist')}:\n          </h4>\n          <div className=\"grid grid-cols-1 md:grid-cols-5 gap-2 text-xs text-green-800 dark:text-green-200\">\n            <div>\n              <strong>S</strong>pecific: {t('specific', 'Clear and well-defined')}\n            </div>\n            <div>\n              <strong>M</strong>easurable: {t('measurable', 'Quantifiable outcomes')}\n            </div>\n            <div>\n              <strong>A</strong>chievable: {t('achievable', 'Realistic and attainable')}\n            </div>\n            <div>\n              <strong>R</strong>elevant: {t('relevant', 'Related to patient needs')}\n            </div>\n            <div>\n              <strong>T</strong>ime-bound: {t('timeBound', 'Has a clear timeframe')}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        {t('goalsOfTreatment', 'Goals of Treatment')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Short Term Goals */}\n        {renderGoalSection(\n          'shortTermGoals',\n          t('shortTermGoals', 'Short Term Goals'),\n          'shortTermWeeks',\n          'shortTermGoals'\n        )}\n\n        {/* Long Term Goals */}\n        {renderGoalSection(\n          'longTermGoals',\n          t('longTermGoals', 'Long Term Goals'),\n          'longTermWeeks',\n          'longTermGoals'\n        )}\n\n        {/* Goals Validation Info */}\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-start\">\n            <i className=\"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mt-1 mr-3\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1\">\n                {t('goalValidationTips', 'Goal Validation Tips')}:\n              </h4>\n              <ul className=\"text-sm text-yellow-700 dark:text-yellow-300 space-y-1\">\n                <li>• {t('goalTip1', 'Long term goals should have more weeks than short term goals')}</li>\n                <li>• {t('goalTip2', 'Include specific measurements (degrees, distances, pain scales)')}</li>\n                <li>• {t('goalTip3', 'Focus on functional outcomes that matter to the patient')}</li>\n                <li>• {t('goalTip4', 'Ensure goals are achievable within the specified timeframe')}</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GoalsSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst TreatmentPlanSection = ({ formData, handleInputChange }) => {\n  const { t } = useLanguage();\n\n  const handleTreatmentChange = (category, treatment, value) => {\n    const currentCategory = formData.treatmentPlan[category] || {};\n    const updatedCategory = { ...currentCategory, [treatment]: value };\n    handleInputChange(`treatmentPlan.${category}`, updatedCategory);\n  };\n\n  const treatmentCategories = [\n    {\n      key: 'painControl',\n      title: t('painControl', 'Pain Control'),\n      icon: 'fas fa-hand-holding-medical',\n      treatments: [\n        { key: 'us', label: 'US (Ultrasound)' },\n        { key: 'laser', label: 'LASER' },\n        { key: 'tens', label: 'TENS' },\n        { key: 'thermal', label: t('thermal', 'Thermal') }\n      ]\n    },\n    {\n      key: 'reduceSwelling',\n      title: t('reduceSwellingEdema', 'Reduce Swelling/Edema'),\n      icon: 'fas fa-snowflake',\n      treatments: [\n        { key: 'cryotherapy', label: t('cryotherapy', 'Cryotherapy') },\n        { key: 'hvc', label: 'HVC' },\n        { key: 'compression', label: t('compression', 'Compression') }\n      ]\n    },\n    {\n      key: 'improveROM',\n      title: t('improveROM', 'Improve ROM'),\n      icon: 'fas fa-expand-arrows-alt',\n      treatments: [\n        { key: 'prom', label: 'PROM' },\n        { key: 'mobilization', label: t('mobilization', 'Mobilization') },\n        { key: 'met', label: 'MET' }\n      ]\n    },\n    {\n      key: 'improveFlexibility',\n      title: t('improveFlexibility', 'Improve Flexibility'),\n      icon: 'fas fa-running',\n      treatments: [\n        { key: 'stretching', label: t('stretching', 'Stretching') },\n        { key: 'thermal', label: t('thermal', 'Thermal') },\n        { key: 'myofascialRelease', label: t('myofascialRelease', 'Myofascial release') }\n      ]\n    },\n    {\n      key: 'muscleStrengthening',\n      title: t('muscleStrengthening', 'Muscle Strengthening'),\n      icon: 'fas fa-dumbbell',\n      treatments: [\n        { key: 'isometric', label: t('isometric', 'Isometric') },\n        { key: 'activeAssisted', label: t('activeAssisted', 'Active Assisted') },\n        { key: 'activeResisted', label: t('activeResisted', 'Active Resisted') },\n        { key: 'coreStrengthening', label: t('coreStrengthening', 'Core Strengthening') },\n        { key: 'plyometrics', label: t('plyometrics', 'Plyometrics') },\n        { key: 'fes', label: 'FES' },\n        { key: 'pnf', label: 'PNF' }\n      ]\n    },\n    {\n      key: 'posturalCorrection',\n      title: t('posturalCorrection', 'Postural Correction'),\n      icon: 'fas fa-user-check',\n      treatments: [\n        { key: 'properBodyMechanics', label: t('properBodyMechanics', 'Proper Body Mechanics') },\n        { key: 'ergonomics', label: t('ergonomics', 'Ergonomics') },\n        { key: 'tiltTable', label: t('tiltTable', 'Tilt table') }\n      ]\n    },\n    {\n      key: 'improveBalance',\n      title: t('improveBalanceCoordination', 'Improve Balance and Coordination'),\n      icon: 'fas fa-balance-scale',\n      treatments: [\n        { key: 'frenkelsEx', label: t('frenkelsEx', \"Frenkel's Ex\") },\n        { key: 'balanceBoard', label: t('balanceBoard', 'Balance Board') },\n        { key: 'agilityEx', label: t('agilityEx', \"Agility Ex's\") },\n        { key: 'proprioceptionTraining', label: t('proprioceptionTraining', 'Proprioception Training') },\n        { key: 'lumbopelvicRhythm', label: t('lumbopelvicRhythm', 'Lumbopelvic Rhythm') }\n      ]\n    },\n    {\n      key: 'improveEndurance',\n      title: t('improveEndurance', 'Improve Endurance'),\n      icon: 'fas fa-heartbeat',\n      treatments: [\n        { key: 'aerobicEx', label: t('aerobicEx', \"Aerobic Ex's\") },\n        { key: 'bicycle', label: t('bicycle', 'Bicycle') },\n        { key: 'treadmill', label: t('treadmill', 'Treadmill') }\n      ]\n    },\n    {\n      key: 'gaitTraining',\n      title: t('gaitTraining', 'Gait Training'),\n      icon: 'fas fa-walking',\n      treatments: [\n        { key: 'normalGaitPattern', label: t('normalGaitPattern', 'Normal Gait Pattern') }\n      ],\n      hasWeightBearing: true\n    },\n    {\n      key: 'homeInstructions',\n      title: t('homeInstructions', 'Home Instructions'),\n      icon: 'fas fa-home',\n      treatments: [\n        { key: 'others', label: t('others', 'Others') }\n      ],\n      hasDescription: true\n    }\n  ];\n\n  const weightBearingOptions = [\n    { value: 'FWB', label: 'FWB (Full Weight Bearing)' },\n    { value: 'PWB', label: 'PWB (Partial Weight Bearing)' },\n    { value: 'WB', label: 'WB (Weight Bearing)' },\n    { value: 'NWB', label: 'NWB (Non Weight Bearing)' }\n  ];\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        {t('treatmentPlan', 'Treatment Plan')}\n      </h2>\n      \n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {treatmentCategories.map((category) => (\n          <div key={category.key} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n            <div className=\"flex items-center mb-4\">\n              <i className={`${category.icon} text-blue-600 dark:text-blue-400 mr-3`}></i>\n              <h3 className=\"text-md font-semibold text-gray-900 dark:text-white\">\n                {category.title}\n              </h3>\n            </div>\n            \n            <div className=\"space-y-3\">\n              {category.treatments.map((treatment) => (\n                <label key={treatment.key} className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={formData.treatmentPlan[category.key]?.[treatment.key] || false}\n                    onChange={(e) => handleTreatmentChange(category.key, treatment.key, e.target.checked)}\n                    className=\"mr-3\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    {treatment.label}\n                  </span>\n                </label>\n              ))}\n              \n              {/* Weight Bearing Options for Gait Training */}\n              {category.hasWeightBearing && formData.treatmentPlan[category.key]?.normalGaitPattern && (\n                <div className=\"mt-4 p-3 bg-white dark:bg-gray-600 rounded border\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('weightBearing', 'Weight Bearing')}:\n                  </label>\n                  <div className=\"space-y-2\">\n                    {weightBearingOptions.map((option) => (\n                      <label key={option.value} className=\"flex items-center\">\n                        <input\n                          type=\"radio\"\n                          name=\"weightBearingType\"\n                          value={option.value}\n                          checked={formData.treatmentPlan[category.key]?.weightBearingType === option.value}\n                          onChange={(e) => handleTreatmentChange(category.key, 'weightBearingType', e.target.value)}\n                          className=\"mr-2\"\n                        />\n                        <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                          {option.label}\n                        </span>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              )}\n              \n              {/* Description field for Home Instructions */}\n              {category.hasDescription && formData.treatmentPlan[category.key]?.others && (\n                <div className=\"mt-4\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    {t('description', 'Description')}:\n                  </label>\n                  <textarea\n                    value={formData.treatmentPlan[category.key]?.description || ''}\n                    onChange={(e) => handleTreatmentChange(category.key, 'description', e.target.value)}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white\"\n                    placeholder={t('enterHomeInstructions', 'Enter home instructions and exercises')}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Treatment Frequency and Duration */}\n      <div className=\"mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n        <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-3\">\n          <i className=\"fas fa-calendar-alt mr-2\"></i>\n          {t('treatmentSchedule', 'Treatment Schedule')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              {t('sessionsPerWeek', 'Sessions per Week')}\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              max=\"7\"\n              value={formData.treatmentSchedule?.sessionsPerWeek || ''}\n              onChange={(e) => handleInputChange('treatmentSchedule.sessionsPerWeek', e.target.value)}\n              className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n              placeholder=\"1-7\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              {t('sessionDuration', 'Session Duration (minutes)')}\n            </label>\n            <input\n              type=\"number\"\n              min=\"15\"\n              max=\"120\"\n              value={formData.treatmentSchedule?.sessionDuration || ''}\n              onChange={(e) => handleInputChange('treatmentSchedule.sessionDuration', e.target.value)}\n              className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n              placeholder=\"30-60\"\n            />\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n              {t('totalWeeks', 'Total Treatment Weeks')}\n            </label>\n            <input\n              type=\"number\"\n              min=\"1\"\n              max=\"52\"\n              value={formData.treatmentSchedule?.totalWeeks || ''}\n              onChange={(e) => handleInputChange('treatmentSchedule.totalWeeks', e.target.value)}\n              className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n              placeholder=\"4-12\"\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TreatmentPlanSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst SignaturesSection = ({ formData, handleInputChange, errors }) => {\n  const { t } = useLanguage();\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        {t('planReviewSignatures', 'Plan Review and Signatures')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Plan Reviewed with Patient */}\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={formData.planReviewedWithPatient || false}\n              onChange={(e) => handleInputChange('planReviewedWithPatient', e.target.checked)}\n              className=\"mr-3\"\n            />\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              {t('planReviewedWithPatient', 'Plan of Care Reviewed with Patient')}\n            </span>\n          </label>\n          <p className=\"text-xs text-gray-600 dark:text-gray-400 mt-2 ml-6\">\n            {t('planReviewDescription', 'Check this box to confirm that the treatment plan has been discussed and reviewed with the patient and/or their family.')}\n          </p>\n        </div>\n\n        {/* Therapist Signature */}\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n          <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n            <i className=\"fas fa-user-md mr-2\"></i>\n            {t('therapistSignature', 'Therapist Signature')}\n          </h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                {t('therapistName', 'Therapist Name')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.therapistSignature?.name || ''}\n                onChange={(e) => handleInputChange('therapistSignature.name', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ${\n                  errors.therapistName ? 'border-red-500' : 'border-blue-300'\n                }`}\n                placeholder={t('enterTherapistName', 'Enter therapist full name')}\n              />\n              {errors.therapistName && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.therapistName}</p>\n              )}\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                {t('badgeNumber', 'Badge No.')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.therapistSignature?.badgeNo || ''}\n                onChange={(e) => handleInputChange('therapistSignature.badgeNo', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ${\n                  errors.therapistBadge ? 'border-red-500' : 'border-blue-300'\n                }`}\n                placeholder={t('enterBadgeNumber', 'Enter badge number')}\n              />\n              {errors.therapistBadge && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.therapistBadge}</p>\n              )}\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                {t('date', 'Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={formData.therapistSignature?.date || ''}\n                onChange={(e) => handleInputChange('therapistSignature.date', e.target.value)}\n                className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n              />\n            </div>\n          </div>\n          \n          {/* Digital Signature Placeholder */}\n          <div className=\"mt-4\">\n            <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-2\">\n              {t('digitalSignature', 'Digital Signature')}\n            </label>\n            <div className=\"border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg p-8 text-center\">\n              <i className=\"fas fa-signature text-3xl text-blue-400 mb-2\"></i>\n              <p className=\"text-sm text-blue-600 dark:text-blue-400\">\n                {t('digitalSignaturePlaceholder', 'Digital signature will be captured here')}\n              </p>\n              <button\n                type=\"button\"\n                className=\"mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n              >\n                <i className=\"fas fa-pen mr-2\"></i>\n                {t('addSignature', 'Add Signature')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Physician Review */}\n        <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6\">\n          <h3 className=\"text-md font-semibold text-green-900 dark:text-green-100 mb-4\">\n            <i className=\"fas fa-user-md mr-2\"></i>\n            {t('physicianReview', 'Physician Review')}\n          </h3>\n          \n          {/* Physician Statement */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2\">\n              {t('physicianStatement', 'Physician Statement')}\n            </label>\n            <textarea\n              value={formData.physicianReview?.statement || ''}\n              onChange={(e) => handleInputChange('physicianReview.statement', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white\"\n              placeholder={t('physicianStatementPlaceholder', 'Have reviewed this plan of care and re-certify a continuing need for services.')}\n            />\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-1\">\n                {t('physicianName', 'Physician Name')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.physicianReview?.signature || ''}\n                onChange={(e) => handleInputChange('physicianReview.signature', e.target.value)}\n                className=\"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white\"\n                placeholder={t('enterPhysicianName', 'Enter physician name')}\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-1\">\n                {t('badgeNumber', 'Badge No.')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.physicianReview?.badgeNo || ''}\n                onChange={(e) => handleInputChange('physicianReview.badgeNo', e.target.value)}\n                className=\"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white\"\n                placeholder={t('enterBadgeNumber', 'Enter badge number')}\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-1\">\n                {t('reviewDate', 'Review Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={formData.physicianReview?.date || ''}\n                onChange={(e) => handleInputChange('physicianReview.date', e.target.value)}\n                className=\"w-full px-3 py-2 border border-green-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-green-800 dark:border-green-600 dark:text-white\"\n              />\n            </div>\n          </div>\n          \n          {/* Physician Digital Signature Placeholder */}\n          <div className=\"mt-4\">\n            <label className=\"block text-sm font-medium text-green-800 dark:text-green-200 mb-2\">\n              {t('physicianDigitalSignature', 'Physician Digital Signature')}\n            </label>\n            <div className=\"border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-8 text-center\">\n              <i className=\"fas fa-signature text-3xl text-green-400 mb-2\"></i>\n              <p className=\"text-sm text-green-600 dark:text-green-400\">\n                {t('physicianSignaturePlaceholder', 'Physician digital signature will be captured here')}\n              </p>\n              <button\n                type=\"button\"\n                className=\"mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <i className=\"fas fa-pen mr-2\"></i>\n                {t('addPhysicianSignature', 'Add Physician Signature')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Compliance and Legal Notice */}\n        <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n          <div className=\"flex items-start\">\n            <i className=\"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mt-1 mr-3\"></i>\n            <div>\n              <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-2\">\n                {t('complianceNotice', 'Compliance & Legal Notice')}\n              </h4>\n              <ul className=\"text-sm text-yellow-700 dark:text-yellow-300 space-y-1\">\n                <li>• {t('hipaaCompliance', 'This form complies with HIPAA privacy and security requirements')}</li>\n                <li>• {t('carfCompliance', 'Treatment plan meets CARF accreditation standards')}</li>\n                <li>• {t('cbahiCompliance', 'Documentation follows CBAHI quality guidelines')}</li>\n                <li>• {t('signatureRequirement', 'Digital signatures are legally binding and encrypted')}</li>\n                <li>• {t('auditTrail', 'All changes are logged for audit trail purposes')}</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SignaturesSection;\n", "import React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\nimport FunctionalProblemsSection from './FunctionalProblemsSection';\nimport GoalsSection from './GoalsSection';\nimport TreatmentPlanSection from './TreatmentPlanSection';\nimport SignaturesSection from './SignaturesSection';\n\nconst PlanOfCareForm = ({\n  patientId,\n  patientData,\n  fromPatientProfile,\n  initialData = {},\n  onSave,\n  onCancel\n}) => {\n  const { t } = useLanguage();\n  const navigate = useNavigate();\n  const { patientId: urlPatientId, planId } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [patient, setPatient] = useState(null);\n  \n  // Use patientId from props or URL params\n  const activePatientId = patientId || urlPatientId;\n\n  // Form data structure\n  const [formData, setFormData] = useState({\n    // Header Information\n    documentNumber: 'QP-',\n    issueDate: new Date().toISOString().split('T')[0],\n    version: '01',\n    reviewNumber: '01',\n    \n    // Patient Information\n    patientName: '',\n    mrNumber: '',\n    diagnosis: '',\n    onsetDate: '',\n    physician: '',\n    \n    // Functional Problems\n    functionalProblems: {\n      pain: { checked: false, description: '', side: '' },\n      bedMatMobility: { checked: false, description: '', side: '' },\n      transferStatus: { checked: false, description: '', side: '' },\n      limitedUEROM: { checked: false, description: '', side: '' },\n      limitedLEROM: { checked: false, description: '', side: '' },\n      decreasedLEStrength: { checked: false, description: '', side: '' },\n      decreasedUEStrength: { checked: false, description: '', side: '' },\n      neckTrunkStrength: { checked: false, description: '', side: '' },\n      abnormalTone: { checked: false, description: '', side: '' },\n      abnormalMovement: { checked: false, description: '', side: '' },\n      skinBreakdown: { checked: false, description: '', side: '' },\n      gaitAsymmetry: { checked: false, description: '', side: '' },\n      atrophy: { checked: false, description: '', side: '' },\n      muscleWeakness: { checked: false, description: '', side: '' },\n      imbalance: { checked: false, description: '', side: '' },\n      lackCoordination: { checked: false, description: '', side: '' },\n      visualPerception: { checked: false, description: '', side: '' },\n      softTissueDysfunction: { checked: false, description: '', side: '' },\n      poorPosture: { checked: false, description: '', side: '' },\n      improperBodyMechanics: { checked: false, description: '', side: '' },\n      wheelchairMobility: { checked: false, description: '', side: '' },\n      difficultyAmbulating: { checked: false, description: '', side: '' },\n      abnormalGait: { checked: false, description: '', side: '' },\n      decreasedEndurance: { checked: false, description: '', side: '' },\n      decreasedSensation: { checked: false, description: '', side: '' },\n      respiratoryCapacity: { checked: false, description: '', side: '' },\n      fineMotorDexterity: { checked: false, description: '', side: '' },\n      functionalActivity: { checked: false, description: '', side: '' },\n      jointHypomobility: { checked: false, description: '', side: '' },\n      jointHypermobility: { checked: false, description: '', side: '' },\n      contracture: { checked: false, description: '', side: '' },\n      other: { checked: false, description: '', side: '' }\n    },\n    \n    // Top 6 Problems Summary\n    topProblems: ['', '', '', '', '', ''],\n    \n    // Goals\n    shortTermGoals: {\n      weeks: '',\n      goals: ['', '', '', '']\n    },\n    longTermGoals: {\n      weeks: '',\n      goals: ['', '', '', '']\n    },\n    \n    // Treatment Plan\n    treatmentPlan: {\n      painControl: {\n        us: false,\n        laser: false,\n        tens: false,\n        thermal: false\n      },\n      reduceSwelling: {\n        cryotherapy: false,\n        hvc: false,\n        compression: false\n      },\n      improveROM: {\n        prom: false,\n        mobilization: false,\n        met: false\n      },\n      improveFlexibility: {\n        stretching: false,\n        thermal: false,\n        myofascialRelease: false\n      },\n      muscleStrengthening: {\n        isometric: false,\n        activeAssisted: false,\n        activeResisted: false,\n        coreStrengthening: false,\n        plyometrics: false,\n        fes: false,\n        pnf: false\n      },\n      posturalCorrection: {\n        properBodyMechanics: false,\n        ergonomics: false,\n        tiltTable: false\n      },\n      improveBalance: {\n        frenkelsEx: false,\n        balanceBoard: false,\n        agilityEx: false,\n        proprioceptionTraining: false,\n        lumbopelvicRhythm: false\n      },\n      improveEndurance: {\n        aerobicEx: false,\n        bicycle: false,\n        treadmill: false\n      },\n      gaitTraining: {\n        normalGaitPattern: false,\n        weightBearing: '',\n        weightBearingType: '' // FWB, PWB, WB\n      },\n      homeInstructions: {\n        others: false,\n        description: ''\n      }\n    },\n    \n    // Plan Review and Signatures\n    planReviewedWithPatient: false,\n    therapistSignature: {\n      name: '',\n      badgeNo: '',\n      date: new Date().toISOString().split('T')[0]\n    },\n    physicianReview: {\n      statement: 'Have reviewed this plan of care and re-certify a continuing need for services.',\n      signature: '',\n      badgeNo: '',\n      date: ''\n    }\n  });\n\n  // Load patient data if patientId is provided\n  useEffect(() => {\n    if (fromPatientProfile && patientData) {\n      // Use patient data passed from patient profile\n      const patient = {\n        id: patientData._id || patientData.id,\n        name: patientData.name || `${patientData.firstName} ${patientData.lastName}`,\n        nameEn: patientData.nameEn || `${patientData.firstName} ${patientData.lastName}`,\n        mrNumber: patientData.mrNumber || patientData._id,\n        dateOfBirth: patientData.dateOfBirth,\n        age: patientData.age,\n        gender: patientData.gender,\n        nationalId: patientData.nationalId,\n        phone: patientData.phone,\n        nationality: patientData.nationality,\n        address: patientData.address\n      };\n\n      setPatient(patient);\n\n      // Pre-populate form with patient data\n      setFormData(prevData => ({\n        ...prevData,\n        patientName: patient.nameEn,\n        mrNumber: patient.mrNumber,\n        dateOfBirth: patient.dateOfBirth,\n        age: patient.age?.toString() || '',\n        gender: patient.gender,\n        diagnosis: patientData.medicalHistory?.primaryDiagnosis || '',\n        ...initialData\n      }));\n\n      setLoading(false);\n    } else if (activePatientId) {\n      setLoading(true);\n      // Mock patient data loading - in real implementation, fetch from API\n      setTimeout(() => {\n        const mockPatient = {\n          id: activePatientId,\n          name: 'أحمد محمد علي',\n          nameEn: 'Ahmed Mohammed Ali',\n          mrNumber: 'MR-2024-001',\n          dateOfBirth: '2016-03-15',\n          age: 8,\n          gender: 'male',\n          nationalId: '**********',\n          phone: '+966 50 123 4567',\n          nationality: 'Saudi',\n          address: 'الرياض، المملكة العربية السعودية'\n        };\n\n        setPatient(mockPatient);\n\n        // Pre-populate form with patient data\n        setFormData(prevData => ({\n          ...prevData,\n          patientName: mockPatient.nameEn,\n          mrNumber: mockPatient.mrNumber\n        }));\n\n        setLoading(false);\n      }, 500);\n    }\n  }, [activePatientId, fromPatientProfile, patientData, initialData]);\n\n  // Initialize form data with any provided initial data\n  useEffect(() => {\n    if (initialData && Object.keys(initialData).length > 0) {\n      setFormData(prevData => ({\n        ...prevData,\n        ...initialData\n      }));\n    }\n  }, [initialData]);\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData };\n    \n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    setFormData(newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleArrayChange = (field, index, value) => {\n    const newData = { ...formData };\n    const parts = field.split('.');\n    let current = newData;\n    \n    for (let i = 0; i < parts.length - 1; i++) {\n      current = current[parts[i]];\n    }\n    \n    current[parts[parts.length - 1]][index] = value;\n    setFormData(newData);\n  };\n\n  const addGoal = (goalType) => {\n    const newData = { ...formData };\n    newData[goalType].goals.push('');\n    setFormData(newData);\n  };\n\n  const removeGoal = (goalType, index) => {\n    const newData = { ...formData };\n    newData[goalType].goals.splice(index, 1);\n    setFormData(newData);\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n    if (!formData.mrNumber.trim()) {\n      newErrors.mrNumber = t('mrNumberRequired', 'MR number is required');\n    }\n    if (!formData.diagnosis.trim()) {\n      newErrors.diagnosis = t('diagnosisRequired', 'Diagnosis is required');\n    }\n    if (!formData.onsetDate) {\n      newErrors.onsetDate = t('onsetDateRequired', 'Onset date is required');\n    }\n    if (!formData.physician.trim()) {\n      newErrors.physician = t('physicianRequired', 'Physician is required');\n    }\n\n    // Goals validation\n    if (!formData.shortTermGoals.weeks || formData.shortTermGoals.weeks < 1) {\n      newErrors.shortTermWeeks = t('shortTermWeeksRequired', 'Short term weeks must be at least 1');\n    }\n    if (!formData.longTermGoals.weeks || formData.longTermGoals.weeks < 1) {\n      newErrors.longTermWeeks = t('longTermWeeksRequired', 'Long term weeks must be at least 1');\n    }\n    if (formData.longTermGoals.weeks <= formData.shortTermGoals.weeks) {\n      newErrors.longTermWeeks = t('longTermWeeksMustBeGreater', 'Long term weeks must be greater than short term weeks');\n    }\n\n    // At least one goal required\n    const hasShortTermGoal = formData.shortTermGoals.goals.some(goal => goal.trim());\n    const hasLongTermGoal = formData.longTermGoals.goals.some(goal => goal.trim());\n    \n    if (!hasShortTermGoal) {\n      newErrors.shortTermGoals = t('atLeastOneShortTermGoal', 'At least one short term goal is required');\n    }\n    if (!hasLongTermGoal) {\n      newErrors.longTermGoals = t('atLeastOneLongTermGoal', 'At least one long term goal is required');\n    }\n\n    // Therapist signature validation\n    if (!formData.therapistSignature.name.trim()) {\n      newErrors.therapistName = t('therapistNameRequired', 'Therapist name is required');\n    }\n    if (!formData.therapistSignature.badgeNo.trim()) {\n      newErrors.therapistBadge = t('therapistBadgeRequired', 'Therapist badge number is required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    \n    try {\n      // Mock API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      if (onSave) {\n        onSave(formData);\n      } else {\n        // Save to localStorage for demo\n        const savedPlans = JSON.parse(localStorage.getItem('planOfCareData') || '[]');\n        const newPlan = {\n          ...formData,\n          id: Date.now(),\n          patientId: activePatientId,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        };\n        savedPlans.push(newPlan);\n        localStorage.setItem('planOfCareData', JSON.stringify(savedPlans));\n        \n        toast.success(t('planOfCareSaved', 'Plan of Care saved successfully'));\n        \n        if (activePatientId) {\n          navigate(`/patients/${activePatientId}`);\n        } else {\n          navigate('/patients');\n        }\n      }\n    } catch (error) {\n      console.error('Error saving plan of care:', error);\n      toast.error(t('errorSavingPlan', 'Error saving plan of care'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleExportPDF = () => {\n    // Mock PDF export - implement with jsPDF or similar\n    toast.success(t('pdfExported', 'PDF exported successfully'));\n  };\n\n  if (loading && !patient) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900\">\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Header */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n          <div className=\"flex items-start justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('initialPlanOfCare', 'Initial Plan of Care for Physical Therapy')}\n                {activePatientId && patient && (\n                  <span className=\"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3\">\n                    - {patient.nameEn || patient.name}\n                  </span>\n                )}\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                {t('planOfCareDescription', 'Comprehensive 2-page plan of care for physical therapy treatment')}\n              </p>\n              \n              {/* Compliance Badges */}\n              <div className=\"flex flex-wrap gap-2 mt-3\">\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n                  <i className=\"fas fa-certificate text-blue-600 dark:text-blue-400\"></i>\n                  <span className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">CARF Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full\">\n                  <i className=\"fas fa-shield-alt text-green-600 dark:text-green-400\"></i>\n                  <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">CBAHI Compliant</span>\n                </div>\n                <div className=\"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n                  <i className=\"fas fa-lock text-purple-600 dark:text-purple-400\"></i>\n                  <span className=\"text-sm font-medium text-purple-800 dark:text-purple-200\">HIPAA Secure</span>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"flex space-x-3\">\n              {activePatientId && (\n                <button\n                  type=\"button\"\n                  onClick={() => navigate(`/patients/${activePatientId}`)}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                >\n                  <i className=\"fas fa-user mr-2\"></i>\n                  {t('viewPatient', 'View Patient')}\n                </button>\n              )}\n              <button\n                type=\"button\"\n                onClick={handleExportPDF}\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                <i className=\"fas fa-file-pdf mr-2\"></i>\n                {t('exportPDF', 'Export PDF')}\n              </button>\n              <button\n                type=\"button\"\n                onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Document Metadata */}\n        <div className=\"bg-gray-50 dark:bg-gray-800 rounded-lg p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('documentInformation', 'Document Information')}\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('documentNumber', 'Document Number')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.documentNumber}\n                onChange={(e) => handleInputChange('documentNumber', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder=\"QP-\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('issueDate', 'Issue Date')}\n              </label>\n              <input\n                type=\"date\"\n                value={formData.issueDate}\n                onChange={(e) => handleInputChange('issueDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('version', 'Version')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.version}\n                onChange={(e) => handleInputChange('version', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder=\"01\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('reviewNumber', 'Review Number')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.reviewNumber}\n                onChange={(e) => handleInputChange('reviewNumber', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                placeholder=\"01\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Patient Information */}\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('patientInformation', 'Patient Information')}\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.patientName}\n                onChange={(e) => handleInputChange('patientName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.patientName ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder={t('enterPatientName', 'Enter patient name')}\n              />\n              {errors.patientName && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('mrNumber', 'MR #')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.mrNumber}\n                onChange={(e) => handleInputChange('mrNumber', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.mrNumber ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder={t('enterMRNumber', 'Enter MR number')}\n              />\n              {errors.mrNumber && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.mrNumber}</p>\n              )}\n            </div>\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('diagnosis', 'Diagnosis')} <span className=\"text-red-500\">*</span>\n              </label>\n              <textarea\n                value={formData.diagnosis}\n                onChange={(e) => handleInputChange('diagnosis', e.target.value)}\n                rows={3}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.diagnosis ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder={t('enterDiagnosis', 'Enter diagnosis')}\n              />\n              {errors.diagnosis && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.diagnosis}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('onsetDate', 'Onset Date')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"date\"\n                value={formData.onsetDate}\n                onChange={(e) => handleInputChange('onsetDate', e.target.value)}\n                max={new Date().toISOString().split('T')[0]}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.onsetDate ? 'border-red-500' : 'border-gray-300'\n                }`}\n              />\n              {errors.onsetDate && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.onsetDate}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('physician', 'Physician')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.physician}\n                onChange={(e) => handleInputChange('physician', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.physician ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder={t('enterPhysicianName', 'Enter physician name')}\n              />\n              {errors.physician && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.physician}</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Functional Problems Section */}\n        <FunctionalProblemsSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n        />\n\n        {/* Goals Section */}\n        <GoalsSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          handleArrayChange={handleArrayChange}\n          addGoal={addGoal}\n          removeGoal={removeGoal}\n          errors={errors}\n        />\n\n        {/* Treatment Plan Section */}\n        <TreatmentPlanSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n        />\n\n        {/* Signatures Section */}\n        <SignaturesSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n        />\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <button\n            type=\"button\"\n            onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                {t('saving', 'Saving...')}\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('savePlanOfCare', 'Save Plan of Care')}\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default PlanOfCareForm;\n"], "names": ["_ref", "formData", "handleInputChange", "errors", "t", "useLanguage", "functionalProblemsOptions", "key", "label", "hasSide", "sideOptions", "value", "handleProblemChange", "<PERSON><PERSON><PERSON>", "field", "currentProblem", "functionalProblems", "updatedProblem", "_objectSpread", "concat", "_jsxs", "className", "children", "_jsx", "map", "option", "problem", "type", "id", "checked", "onChange", "e", "target", "htmlFor", "side", "name", "description", "rows", "placeholder", "topProblems", "index", "handleTopProblemChange", "newTopProblems", "handleArrayChange", "addGoal", "removeGoal", "goal<PERSON><PERSON><PERSON><PERSON>", "renderGoalSection", "goalType", "title", "weekField", "goalsField", "_formData$goalType", "_formData$goalType2", "goals", "weeks", "min", "max", "onClick", "goal", "length", "slice", "example", "_formData$treatmentSc", "_formData$treatmentSc2", "_formData$treatmentSc3", "handleTreatmentChange", "category", "treatment", "currentCategory", "treatmentPlan", "updatedCategory", "treatmentCategories", "icon", "treatments", "hasWeightBearing", "hasDescription", "weightBearingOptions", "_formData$treatmentPl2", "_formData$treatmentPl4", "_formData$treatmentPl5", "_formData$treatmentPl", "normalGaitPattern", "_formData$treatmentPl3", "weightBearingType", "others", "treatmentSchedule", "sessionsPerWeek", "sessionDuration", "totalWeeks", "_formData$therapistSi", "_formData$therapistSi2", "_formData$therapistSi3", "_formData$physicianRe", "_formData$physicianRe2", "_formData$physicianRe3", "_formData$physicianRe4", "planReviewedWithPatient", "therapistSignature", "<PERSON><PERSON><PERSON>", "badgeNo", "therapistBadge", "date", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statement", "signature", "patientId", "patientData", "fromPatientProfile", "initialData", "onSave", "onCancel", "navigate", "useNavigate", "urlPatientId", "planId", "useParams", "loading", "setLoading", "useState", "setErrors", "patient", "setPatient", "activePatientId", "setFormData", "documentNumber", "issueDate", "Date", "toISOString", "split", "version", "reviewNumber", "patientName", "mr<PERSON><PERSON><PERSON>", "diagnosis", "onsetDate", "physician", "pain", "bedMatMobility", "transferStatus", "limitedUEROM", "limitedLEROM", "decreasedLEStrength", "decreasedUEStrength", "neckTrunkStrength", "abnormalTone", "abnormalMovement", "skinBreakdown", "gaitAsymmetry", "atrophy", "muscleWeakness", "imbalance", "lackCoordination", "visualPerception", "softTissueDysfunction", "poorPosture", "improperBodyMechanics", "wheelchairMobility", "difficultyAmbulating", "abnormalGait", "decreasedEndurance", "decreasedSensation", "respiratoryCapacity", "fineMotorDexterity", "functionalActivity", "jointHypomobility", "jointHypermobility", "contracture", "other", "shortTermGoals", "longTermGoals", "painControl", "us", "laser", "tens", "thermal", "reduceSwelling", "cryotherapy", "hvc", "compression", "improveROM", "prom", "mobilization", "met", "improveFlexibility", "stretching", "myofascialRelease", "muscleStrengthening", "isometric", "activeAssisted", "activeResisted", "coreStrengthening", "plyometrics", "fes", "pnf", "posturalCorrection", "properBodyMechanics", "ergonomics", "tiltTable", "improveBalance", "frenkelsEx", "balanceBoard", "agilityEx", "proprioceptionTraining", "lumbopelvicRhythm", "improveEndurance", "aerobicEx", "bicycle", "treadmill", "gaitTraining", "weightBearing", "homeInstructions", "useEffect", "_id", "firstName", "lastName", "nameEn", "dateOfBirth", "age", "gender", "nationalId", "phone", "nationality", "address", "prevData", "_patient$age", "_patientData$medicalH", "toString", "medicalHistory", "primaryDiagnosis", "setTimeout", "mockPatient", "Object", "keys", "newData", "includes", "parts", "current", "i", "prev", "onSubmit", "async", "preventDefault", "validateForm", "newErrors", "trim", "shortTermWeeks", "longTermWeeks", "hasShortTermGoal", "some", "hasLongTermGoal", "Promise", "resolve", "savedPlans", "JSON", "parse", "localStorage", "getItem", "newPlan", "now", "createdAt", "updatedAt", "push", "setItem", "stringify", "toast", "success", "error", "console", "handleExportPDF", "FunctionalProblemsSection", "GoalsSection", "splice", "TreatmentPlanSection", "SignaturesSection", "disabled", "_Fragment"], "sourceRoot": ""}