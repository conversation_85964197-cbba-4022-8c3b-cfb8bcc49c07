{"version": 3, "file": "static/js/1188.91dba9ce.chunk.js", "mappings": "mMAGA,MAuTA,EAvTqBA,KACnB,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACPC,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CACvCC,UAAU,EACVC,eAAe,EACfC,oBAAoB,EACpBC,cAAe,SACfC,gBAAiB,KACjBC,kBAAkB,EAClBC,aAAa,EACbC,YAAY,EACZC,gBAAiB,QACjBC,gBAAiB,UAGZC,EAAWC,IAAgBZ,EAAAA,EAAAA,UAAS,WAErCa,EAAsBA,CAACC,EAASC,KACpChB,EAAYiB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXD,GAAI,IACP,CAACF,GAAUC,MASTG,EAAO,CACX,CAAEC,GAAI,UAAWC,MAAOxB,EAAE,UAAW,WAAYyB,KAAM,cACvD,CAAEF,GAAI,WAAYC,MAAOxB,EAAE,WAAY,YAAayB,KAAM,qBAC1D,CAAEF,GAAI,gBAAiBC,MAAOxB,EAAE,gBAAiB,iBAAkByB,KAAM,eACzE,CAAEF,GAAI,SAAUC,MAAOxB,EAAE,SAAU,qBAAsByB,KAAM,oBAGjE,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CC,SAAA,EAE1DC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wFAAuFC,UACpGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,6GAA4GC,UACzHC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,+GAA8GC,SACzH5B,EAAE,eAAgB,oBAErB0B,EAAAA,EAAAA,MAAA,KAAGC,UAAU,kEAAiEC,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCACZ3B,EAAE,0BAA2B,oEAGlC0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6FAA4FC,SAAA,EACzGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ3B,EAAE,WAAY,yBAOzB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iGAAgGC,UAC7GC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvBN,EAAKQ,IAAKC,IACTL,EAAAA,EAAAA,MAAA,UAEEM,QAASA,IAAMhB,EAAae,EAAIR,IAChCI,UAAS,uFAAAM,OACPlB,IAAcgB,EAAIR,GACd,sEACA,6EACHK,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAS,GAAAM,OAAKF,EAAIN,KAAI,WACxBM,EAAIP,QATAO,EAAIR,YAiBnBM,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iGAAgGC,SAAA,CAG9F,YAAdb,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6EAA4EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oCACZ3B,EAAE,kBAAmB,wBAGxB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oCAAmCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAE5B,EAAE,WAAY,gBACtF6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAE5B,EAAE,eAAgB,0CAE7E6B,EAAAA,EAAAA,KAAA,SACEK,KAAK,WACLC,QAASjC,EAASG,SAClB+B,SAAWC,GAAMpB,EAAoB,WAAYoB,EAAEC,OAAOH,SAC1DR,UAAU,4EAKhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gGAA+FC,UAC5GF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oCAAmCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,yDAAwDC,SAAE5B,EAAE,gBAAiB,qBAC7F6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CAA4CC,SAAE5B,EAAE,oBAAqB,yCAEpF6B,EAAAA,EAAAA,KAAA,SACEK,KAAK,WACLC,QAASjC,EAASI,cAClB8B,SAAWC,GAAMpB,EAAoB,gBAAiBoB,EAAEC,OAAOH,SAC/DR,UAAU,+EAKhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oGAAmGC,UAChHF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oCAAmCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SAAE5B,EAAE,cAAe,mBAC7F6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SAAE5B,EAAE,kBAAmB,yCAEpF6B,EAAAA,EAAAA,KAAA,SACEK,KAAK,WACLC,QAASjC,EAASS,YAClByB,SAAWC,GAAMpB,EAAoB,cAAeoB,EAAEC,OAAOH,SAC7DR,UAAU,kFAKhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oGAAmGC,UAChHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnF5B,EAAE,kBAAmB,uBAExB0B,EAAAA,EAAAA,MAAA,UACEP,MAAOjB,EAASO,gBAChB2B,SAAWC,GAAMpB,EAAoB,kBAAmBoB,EAAEC,OAAOnB,OACjEQ,UAAU,+IAA8IC,SAAA,EAExJC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,KAAIS,SAAC,aACnBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,KAAIS,SAAC,6DAShB,aAAdb,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6EAA4EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCACZ3B,EAAE,mBAAoB,yBAGzB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wFAAuFC,UACpGF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oCAAmCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,qDAAoDC,SAAE5B,EAAE,mBAAoB,gCAC5F6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yCAAwCC,SAAE5B,EAAE,uBAAwB,8CAEnF6B,EAAAA,EAAAA,KAAA,SACEK,KAAK,WACLC,QAASjC,EAASQ,iBAClB0B,SAAWC,GAAMpB,EAAoB,mBAAoBoB,EAAEC,OAAOH,SAClER,UAAU,yEAKhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oGAAmGC,UAChHF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oCAAmCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2DAA0DC,SAAE5B,EAAE,aAAc,kBAC5F6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+CAA8CC,SAAE5B,EAAE,iBAAkB,sCAEnF6B,EAAAA,EAAAA,KAAA,SACEK,KAAK,WACLC,QAASjC,EAASU,WAClBwB,SAAWC,GAAMpB,EAAoB,aAAcoB,EAAEC,OAAOH,SAC5DR,UAAU,kFAKhBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oGAAmGC,UAChHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,sEAAqEC,SACnF5B,EAAE,kBAAmB,uBAExB0B,EAAAA,EAAAA,MAAA,UACEP,MAAOjB,EAASY,gBAChBsB,SAAWC,GAAMpB,EAAoB,kBAAmBoB,EAAEC,OAAOnB,OACjEQ,UAAU,+IAA8IC,SAAA,EAExJC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,WAAUS,SAAC,wBACzBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,OAAMS,SAAC,oBACrBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,UAASS,SAAC,qCAK9BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E5B,EAAE,gBAAiB,4BAEtB0B,EAAAA,EAAAA,MAAA,UACEP,MAAOjB,EAASM,cAChB4B,SAAWC,GAAMpB,EAAoB,gBAAiBoB,EAAEC,OAAOnB,OAC/DQ,UAAU,qIAAoIC,SAAA,EAE9IC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,QAAOS,SAAC,YACtBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAC,aACvBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAC,aACvBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAC,aACvBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,aAAYS,SAAC,6BASxB,kBAAdb,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6EAA4EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACZ3B,EAAE,uBAAwB,6BAG7B6B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,4FAA2FC,UACxGF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oCAAmCC,SAAA,EAClDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,uDAAsDC,SAAE5B,EAAE,qBAAsB,0BAChG6B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2CAA0CC,SAAE5B,EAAE,yBAA0B,gDAEvF6B,EAAAA,EAAAA,KAAA,SACEK,KAAK,WACLC,QAASjC,EAASK,mBAClB6B,SAAWC,GAAMpB,EAAoB,qBAAsBoB,EAAEC,OAAOH,SACpER,UAAU,gFASP,WAAdZ,IACCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,6EAA4EC,SAAA,EACxFC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCACZ3B,EAAE,iBAAkB,kCAGvB6B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gGAA+FC,UAC5GF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,oEAAmEC,SACjF5B,EAAE,kBAAmB,uBAExB0B,EAAAA,EAAAA,MAAA,UACEP,MAAOjB,EAASW,gBAChBuB,SAAWC,GAAMpB,EAAoB,kBAAmBoB,EAAEC,OAAOnB,OACjEQ,UAAU,0IAAyIC,SAAA,EAEnJC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,QAAOS,SAAC,WACtBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,UAASS,SAAC,wBAQlCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wBAAuBC,UACpCF,EAAAA,EAAAA,MAAA,UACEM,QAjRKO,KAEjBC,MAAMxC,EAAE,gBAAiB,kCAgRb2B,UAAU,mLAAkLC,SAAA,EAE5LC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ3B,EAAE,eAAgB,iC", "sources": ["pages/Forms/FormSettings.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst FormSettings = () => {\n  const { t } = useLanguage();\n  const [settings, setSettings] = useState({\n    autoSave: true,\n    pdfGeneration: true,\n    emailNotifications: true,\n    dataRetention: '7years',\n    defaultLanguage: 'en',\n    requireSignature: true,\n    allowDrafts: true,\n    auditTrail: true,\n    backupFrequency: 'daily',\n    encryptionLevel: 'high'\n  });\n\n  const [activeTab, setActiveTab] = useState('general');\n\n  const handleSettingChange = (setting, value) => {\n    setSettings(prev => ({\n      ...prev,\n      [setting]: value\n    }));\n  };\n\n  const handleSave = () => {\n    // Save settings logic here\n    alert(t('settingsSaved', 'Settings saved successfully!'));\n  };\n\n  const tabs = [\n    { id: 'general', label: t('general', 'General'), icon: 'fas fa-cog' },\n    { id: 'security', label: t('security', 'Security'), icon: 'fas fa-shield-alt' },\n    { id: 'notifications', label: t('notifications', 'Notifications'), icon: 'fas fa-bell' },\n    { id: 'backup', label: t('backup', 'Backup & Recovery'), icon: 'fas fa-database' }\n  ];\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n      {/* Header */}\n      <div className=\"bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-lg shadow-lg mb-8\">\n        <div className=\"bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-white/20 dark:border-gray-700/50\">\n          <div className=\"px-6 py-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\">\n                  {t('formSettings', 'Form Settings')}\n                </h1>\n                <p className=\"text-lg text-gray-700 dark:text-gray-300 mt-2 flex items-center\">\n                  <i className=\"fas fa-cog text-indigo-500 mr-2\"></i>\n                  {t('formSettingsDescription', 'Configure form behavior, security, and system preferences')}\n                </p>\n              </div>\n              <div className=\"bg-gradient-to-r from-indigo-400 to-purple-400 text-white px-4 py-2 rounded-full shadow-lg\">\n                <i className=\"fas fa-tools mr-2\"></i>\n                {t('settings', 'Settings')}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n        {/* Sidebar Tabs */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-4\">\n            <nav className=\"space-y-2\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-all duration-200 ${\n                    activeTab === tab.id\n                      ? 'bg-gradient-to-r from-indigo-500 to-purple-500 text-white shadow-lg'\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                  }`}\n                >\n                  <i className={`${tab.icon} mr-3`}></i>\n                  {tab.label}\n                </button>\n              ))}\n            </nav>\n          </div>\n        </div>\n\n        {/* Settings Content */}\n        <div className=\"lg:col-span-3\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6\">\n            \n            {/* General Settings */}\n            {activeTab === 'general' && (\n              <div className=\"space-y-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center\">\n                  <i className=\"fas fa-cog text-indigo-500 mr-2\"></i>\n                  {t('generalSettings', 'General Settings')}\n                </h2>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4\">\n                    <label className=\"flex items-center justify-between\">\n                      <div>\n                        <span className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">{t('autoSave', 'Auto Save')}</span>\n                        <p className=\"text-xs text-blue-600 dark:text-blue-400\">{t('autoSaveDesc', 'Automatically save form progress')}</p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        checked={settings.autoSave}\n                        onChange={(e) => handleSettingChange('autoSave', e.target.checked)}\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded\"\n                      />\n                    </label>\n                  </div>\n\n                  <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4\">\n                    <label className=\"flex items-center justify-between\">\n                      <div>\n                        <span className=\"text-sm font-medium text-green-900 dark:text-green-100\">{t('pdfGeneration', 'PDF Generation')}</span>\n                        <p className=\"text-xs text-green-600 dark:text-green-400\">{t('pdfGenerationDesc', 'Enable PDF export functionality')}</p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        checked={settings.pdfGeneration}\n                        onChange={(e) => handleSettingChange('pdfGeneration', e.target.checked)}\n                        className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-green-300 rounded\"\n                      />\n                    </label>\n                  </div>\n\n                  <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-4\">\n                    <label className=\"flex items-center justify-between\">\n                      <div>\n                        <span className=\"text-sm font-medium text-purple-900 dark:text-purple-100\">{t('allowDrafts', 'Allow Drafts')}</span>\n                        <p className=\"text-xs text-purple-600 dark:text-purple-400\">{t('allowDraftsDesc', 'Save incomplete forms as drafts')}</p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        checked={settings.allowDrafts}\n                        onChange={(e) => handleSettingChange('allowDrafts', e.target.checked)}\n                        className=\"h-4 w-4 text-purple-600 focus:ring-purple-500 border-purple-300 rounded\"\n                      />\n                    </label>\n                  </div>\n\n                  <div className=\"bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg p-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-orange-900 dark:text-orange-100 mb-2\">\n                        {t('defaultLanguage', 'Default Language')}\n                      </label>\n                      <select\n                        value={settings.defaultLanguage}\n                        onChange={(e) => handleSettingChange('defaultLanguage', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-orange-300 dark:border-orange-600 rounded-lg bg-white dark:bg-orange-700 text-orange-900 dark:text-orange-100\"\n                      >\n                        <option value=\"en\">English</option>\n                        <option value=\"ar\">العربية</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Security Settings */}\n            {activeTab === 'security' && (\n              <div className=\"space-y-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center\">\n                  <i className=\"fas fa-shield-alt text-red-500 mr-2\"></i>\n                  {t('securitySettings', 'Security Settings')}\n                </h2>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4\">\n                    <label className=\"flex items-center justify-between\">\n                      <div>\n                        <span className=\"text-sm font-medium text-red-900 dark:text-red-100\">{t('requireSignature', 'Require Digital Signature')}</span>\n                        <p className=\"text-xs text-red-600 dark:text-red-400\">{t('requireSignatureDesc', 'Mandate digital signatures for forms')}</p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        checked={settings.requireSignature}\n                        onChange={(e) => handleSettingChange('requireSignature', e.target.checked)}\n                        className=\"h-4 w-4 text-red-600 focus:ring-red-500 border-red-300 rounded\"\n                      />\n                    </label>\n                  </div>\n\n                  <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4\">\n                    <label className=\"flex items-center justify-between\">\n                      <div>\n                        <span className=\"text-sm font-medium text-yellow-900 dark:text-yellow-100\">{t('auditTrail', 'Audit Trail')}</span>\n                        <p className=\"text-xs text-yellow-600 dark:text-yellow-400\">{t('auditTrailDesc', 'Track all form modifications')}</p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        checked={settings.auditTrail}\n                        onChange={(e) => handleSettingChange('auditTrail', e.target.checked)}\n                        className=\"h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-yellow-300 rounded\"\n                      />\n                    </label>\n                  </div>\n\n                  <div className=\"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-indigo-900 dark:text-indigo-100 mb-2\">\n                        {t('encryptionLevel', 'Encryption Level')}\n                      </label>\n                      <select\n                        value={settings.encryptionLevel}\n                        onChange={(e) => handleSettingChange('encryptionLevel', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-indigo-300 dark:border-indigo-600 rounded-lg bg-white dark:bg-indigo-700 text-indigo-900 dark:text-indigo-100\"\n                      >\n                        <option value=\"standard\">Standard (AES-128)</option>\n                        <option value=\"high\">High (AES-256)</option>\n                        <option value=\"maximum\">Maximum (AES-256 + RSA)</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-teal-50 dark:bg-teal-900/20 border border-teal-200 dark:border-teal-700 rounded-lg p-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-teal-900 dark:text-teal-100 mb-2\">\n                        {t('dataRetention', 'Data Retention Period')}\n                      </label>\n                      <select\n                        value={settings.dataRetention}\n                        onChange={(e) => handleSettingChange('dataRetention', e.target.value)}\n                        className=\"w-full px-3 py-2 border border-teal-300 dark:border-teal-600 rounded-lg bg-white dark:bg-teal-700 text-teal-900 dark:text-teal-100\"\n                      >\n                        <option value=\"1year\">1 Year</option>\n                        <option value=\"3years\">3 Years</option>\n                        <option value=\"5years\">5 Years</option>\n                        <option value=\"7years\">7 Years</option>\n                        <option value=\"indefinite\">Indefinite</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Notifications Settings */}\n            {activeTab === 'notifications' && (\n              <div className=\"space-y-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center\">\n                  <i className=\"fas fa-bell text-yellow-500 mr-2\"></i>\n                  {t('notificationSettings', 'Notification Settings')}\n                </h2>\n\n                <div className=\"space-y-4\">\n                  <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4\">\n                    <label className=\"flex items-center justify-between\">\n                      <div>\n                        <span className=\"text-sm font-medium text-blue-900 dark:text-blue-100\">{t('emailNotifications', 'Email Notifications')}</span>\n                        <p className=\"text-xs text-blue-600 dark:text-blue-400\">{t('emailNotificationsDesc', 'Send email alerts for form submissions')}</p>\n                      </div>\n                      <input\n                        type=\"checkbox\"\n                        checked={settings.emailNotifications}\n                        onChange={(e) => handleSettingChange('emailNotifications', e.target.checked)}\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-blue-300 rounded\"\n                      />\n                    </label>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Backup Settings */}\n            {activeTab === 'backup' && (\n              <div className=\"space-y-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center\">\n                  <i className=\"fas fa-database text-green-500 mr-2\"></i>\n                  {t('backupSettings', 'Backup & Recovery Settings')}\n                </h2>\n\n                <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg p-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-green-900 dark:text-green-100 mb-2\">\n                      {t('backupFrequency', 'Backup Frequency')}\n                    </label>\n                    <select\n                      value={settings.backupFrequency}\n                      onChange={(e) => handleSettingChange('backupFrequency', e.target.value)}\n                      className=\"w-full px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg bg-white dark:bg-green-700 text-green-900 dark:text-green-100\"\n                    >\n                      <option value=\"hourly\">Hourly</option>\n                      <option value=\"daily\">Daily</option>\n                      <option value=\"weekly\">Weekly</option>\n                      <option value=\"monthly\">Monthly</option>\n                    </select>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Save Button */}\n            <div className=\"mt-8 flex justify-end\">\n              <button\n                onClick={handleSave}\n                className=\"px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-lg hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 shadow-lg flex items-center\"\n              >\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveSettings', 'Save Settings')}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FormSettings;\n"], "names": ["FormSettings", "t", "useLanguage", "settings", "setSettings", "useState", "autoSave", "pdfGeneration", "emailNotifications", "dataRetention", "defaultLanguage", "requireSignature", "allowDrafts", "auditTrail", "backupFrequency", "encryptionLevel", "activeTab", "setActiveTab", "handleSettingChange", "setting", "value", "prev", "_objectSpread", "tabs", "id", "label", "icon", "_jsxs", "className", "children", "_jsx", "map", "tab", "onClick", "concat", "type", "checked", "onChange", "e", "target", "handleSave", "alert"], "sourceRoot": ""}