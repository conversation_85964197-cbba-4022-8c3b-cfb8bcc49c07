{"version": 3, "file": "static/js/2960.9cda3f16.chunk.js", "mappings": "iOAMA,MAyyBA,EAzyBsBA,KACpB,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACf,KAAEC,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACVC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,GAGjCC,EAAaL,EAASM,SAASC,SAAS,SACvCC,EAAWC,IAAgBL,EAAAA,EAAAA,UAASC,EAAa,SAAW,WAC5DK,EAAQC,IAAaP,EAAAA,EAAAA,UAAS,KAC9BQ,EAAoBC,IAAyBT,EAAAA,EAAAA,UAAS,KACtDU,EAAYC,IAAiBX,EAAAA,EAAAA,UAAS,KACtCY,EAAcC,IAAmBb,EAAAA,EAAAA,UAAS,QAG1Cc,EAAWC,IAAgBf,EAAAA,EAAAA,UAAS,CACzCgB,UAAW,GACXC,YAAa,GACbC,kBAAmB,GACnBC,aAAc,GACdC,YAAa,GACbC,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,YAAa,GACbC,UAAW,GACXC,cAAe,GACfC,MAAO,GACPC,YAAa,MAGRC,EAAQC,IAAa/B,EAAAA,EAAAA,UAAS,CAAC,IAEtCgC,EAAAA,EAAAA,WAAU,KACRC,IACAC,KACC,IAEH,MAAMD,EAAaE,UACjBpC,GAAW,GACX,IA6CEQ,EA3CmB,CACjB,CACE6B,GAAI,SACJnB,YAAa,uEACboB,cAAe,qBACfnB,kBAAmB,cACnBC,aAAc,cACdC,YAAa,KACbC,YAAa,aACbI,YAAa,mBACba,OAAQ,WACRC,eAAgB,aAChBC,aAAc,aACdd,UAAW,kBAEb,CACEU,GAAI,SACJnB,YAAa,0DACboB,cAAe,eACfnB,kBAAmB,WACnBC,aAAc,cACdC,YAAa,KACbC,YAAa,aACbI,YAAa,qBACba,OAAQ,UACRC,eAAgB,aAChBb,UAAW,sBAEb,CACEU,GAAI,SACJnB,YAAa,oDACboB,cAAe,iBACfnB,kBAAmB,UACnBC,aAAc,cACdC,YAAa,KACbC,YAAa,aACbI,YAAa,oBACba,OAAQ,SACRC,eAAgB,aAChBE,aAAc,6BACdf,UAAW,uBAIjB,CAAE,MAAOgB,GACPC,EAAAA,GAAMD,MAAMrD,EAAE,qBAAsB,kCACtC,CAAC,QACCU,GAAW,EACb,GAGImC,EAAyBC,UAC7B,IAkCE1B,EAhCsB,CACpB,CACE2B,GAAI,OACJQ,KAAM,cACNC,OAAQ,sEACRC,cAAe,mBACfC,MAAO,qBACPC,QAAS,kBACTV,OAAQ,SACRW,eAAgB,iBAElB,CACEb,GAAI,WACJQ,KAAM,WACNC,OAAQ,yDACRC,cAAe,mBACfC,MAAO,yBACPC,QAAS,sBACTV,OAAQ,SACRW,eAAgB,gBAElB,CACEb,GAAI,UACJQ,KAAM,UACNC,OAAQ,iCACRC,cAAe,mBACfC,MAAO,wBACPC,QAAS,qBACTV,OAAQ,SACRW,eAAgB,iBAItB,CAAE,MAAOP,GACPC,EAAAA,GAAMD,MAAMrD,EAAE,wBAAyB,qCACzC,GAGI6D,EAAoBA,CAACC,EAAOC,KAChCrC,EAAasC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACF,GAAQC,KACtCtB,EAAOqB,IACTpB,EAAUsB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACF,GAAQ,SA0ErCI,EAAkBjB,IACtB,OAAQA,GACN,IAAK,WACH,MAAO,uEACT,IAAK,UACH,MAAO,2EACT,IAAK,SACH,MAAO,+DACT,IAAK,aACH,MAAO,mEACT,QACE,MAAO,qEAIPkB,EAAiBlD,EAAOmD,OAAOC,IAAU,IAADC,EAC5C,MAAMC,EAAgBF,EAAMzC,YAAY4C,cAAc1D,SAASO,EAAWmD,iBAClC,QADgDF,EACnED,EAAMrB,qBAAa,IAAAsB,OAAA,EAAnBA,EAAqBE,cAAc1D,SAASO,EAAWmD,iBACvDH,EAAMvC,aAAa0C,cAAc1D,SAASO,EAAWmD,gBACrDH,EAAMxC,kBAAkB2C,cAAc1D,SAASO,EAAWmD,eACzEC,EAAiC,QAAjBlD,GAA0B8C,EAAMpB,SAAW1B,EACjE,OAAOgD,GAAiBE,IAGpBC,EAAe,CACnB,CAAEX,MAAO,qBAAsBY,MAAO3E,EAAE,oBAAqB,uBAC7D,CAAE+D,MAAO,mBAAoBY,MAAO3E,EAAE,kBAAmB,qBACzD,CAAE+D,MAAO,uBAAwBY,MAAO3E,EAAE,sBAAuB,yBACjE,CAAE+D,MAAO,iBAAkBY,MAAO3E,EAAE,gBAAiB,mBACrD,CAAE+D,MAAO,oBAAqBY,MAAO3E,EAAE,mBAAoB,sBAC3D,CAAE+D,MAAO,eAAgBY,MAAO3E,EAAE,eAAgB,iBAClD,CAAE+D,MAAO,YAAaY,MAAO3E,EAAE,WAAY,eAG7C,OACE4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7D9E,EAAE,sBAAuB,2BAE5B+E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjD9E,EAAE,0BAA2B,wEAKlC+E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EACpCF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMhE,EAAa,UAC5B6D,UAAS,4CAAAI,OACO,WAAdlE,EACI,mDACA,0HACH+D,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZ7E,EAAE,kBAAmB,wBAExB4E,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMhE,EAAa,UAC5B6D,UAAS,4CAAAI,OACO,WAAdlE,EACI,mDACA,0HACH+D,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ7E,EAAE,cAAe,oBAEpB4E,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMhE,EAAa,aAC5B6D,UAAS,4CAAAI,OACO,cAAdlE,EACI,mDACA,0HACH+D,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2BACZ7E,EAAE,qBAAsB,2BAE3B4E,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMhE,EAAa,gBAC5B6D,UAAS,4CAAAI,OACO,iBAAdlE,EACI,mDACA,0HACH+D,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6BACZ7E,EAAE,eAAgB,0BAOZ,WAAde,IACC6D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,UAChEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sFAAqFC,SAAA,EAClGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,sDAAqDC,SAAA,EACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DACZ7E,EAAE,kBAAmB,wBAGxB4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEG,KAAK,OACLnB,MAAO1C,EACP8D,SAAWC,GAAM9D,EAAc8D,EAAEC,OAAOtB,OACxCuB,YAAatF,EAAE,eAAgB,oBAC/B6E,UAAU,oKAEZE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0DAGfD,EAAAA,EAAAA,MAAA,UACEb,MAAOxC,EACP4D,SAAWC,GAAM5D,EAAgB4D,EAAEC,OAAOtB,OAC1Cc,UAAU,2JAA0JC,SAAA,EAEpKC,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,MAAKe,SAAE9E,EAAE,cAAe,mBACtC+E,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,WAAUe,SAAE9E,EAAE,WAAY,eACxC+E,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,UAASe,SAAE9E,EAAE,UAAW,cACtC+E,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,SAAQe,SAAE9E,EAAE,SAAU,aACpC+E,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,aAAYe,SAAE9E,EAAE,aAAc,4BAMpD+E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9E,EAAE,UAAW,eAEhB+E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9E,EAAE,UAAW,cAEhB+E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9E,EAAE,WAAY,eAEjB+E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9E,EAAE,SAAU,aAEf+E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9E,EAAE,SAAU,aAEf+E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9E,EAAE,iBAAkB,sBAEvB+E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9G9E,EAAE,UAAW,mBAIpB+E,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFX,EAAeoB,IAAKlB,IACnBO,EAAAA,EAAAA,MAAA,MAAmBC,UAAU,0CAAyCC,SAAA,EACpEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DT,EAAMtB,QAGXgC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/D7E,EAAQoE,EAAMzC,YAAcyC,EAAMrB,iBAErC+B,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDT,EAAMvC,qBAIbiD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDT,EAAMxC,uBAGXkD,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,CAC/DT,EAAMtC,YAAYyD,iBAAiB,IAAExF,EAAE,MAAO,aAGnD+E,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAI,OAAgDf,EAAeG,EAAMpB,SAAU6B,SAC3F9E,EAAEqE,EAAMpB,OAAQoB,EAAMpB,aAG3B8B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oEAAmEC,SAC9ET,EAAMnB,kBAET6B,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kDAAiDC,UAC7DF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UAAQF,UAAU,gFAA+EC,UAC/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kBAEfE,EAAAA,EAAAA,KAAA,UAAQF,UAAU,oFAAmFC,UACnGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBAEG,YAAjBR,EAAMpB,SACL8B,EAAAA,EAAAA,KAAA,UAAQF,UAAU,wFAAuFC,UACvGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBA5CdR,EAAMtB,YAuDI,IAA1BoB,EAAesB,SACdb,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5C9E,EAAE,gBAAiB,qCAQf,WAAde,IACC6D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wDACZ7E,EAAE,iBAAkB,kCAGvB4E,EAAAA,EAAAA,MAAA,QAAMc,SA1RY5C,UAGxB,GAFAsC,EAAEO,iBA3BsBC,MACxB,MAAMC,EAAY,CAAC,EAsBnB,OApBKpE,EAAUG,YAAYkE,SACzBD,EAAUjE,YAAc5B,EAAE,sBAAuB,6BAE9CyB,EAAUI,oBACbgE,EAAUhE,kBAAoB7B,EAAE,4BAA6B,mCAE1DyB,EAAUK,aAAagE,SAC1BD,EAAU/D,aAAe9B,EAAE,uBAAwB,gCAEhDyB,EAAUM,aAAeN,EAAUM,aAAe,KACrD8D,EAAU9D,YAAc/B,EAAE,sBAAuB,mCAE9CyB,EAAUW,cACbyD,EAAUzD,YAAcpC,EAAE,sBAAuB,6BAE9CyB,EAAUY,UAAUyD,SACvBD,EAAUxD,UAAYrC,EAAE,oBAAqB,0BAG/C0C,EAAUmD,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWJ,QAMzBG,GAAL,CAKAlF,GAAW,GACX,UAEQ,IAAIuF,QAAQC,GAAWC,WAAWD,EAAS,OAEjD,MAAME,GAAQnC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACTxC,GAAS,IACZsB,GAAG,MAADkC,OAAQhD,KAAKoE,OACfpD,OAAQ,UACRC,gBAAgB,IAAIjB,MAAOC,cAAcC,MAAM,KAAK,KAGtDjB,EAAU8C,GAAQ,CAACoC,KAAapC,IAChCtC,EAAa,CACXC,UAAW,GACXC,YAAa,GACbC,kBAAmB,GACnBC,aAAc,GACdC,YAAa,GACbC,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,YAAa,GACbC,UAAW,GACXC,cAAe,GACfC,MAAO,GACPC,YAAa,KAGfc,EAAAA,GAAMgD,QAAQtG,EAAE,6BAA8B,2CAC9CgB,EAAa,SACf,CAAE,MAAOqC,GACPC,EAAAA,GAAMD,MAAMrD,EAAE,uBAAwB,oCACxC,CAAC,QACCU,GAAW,EACb,CAnCA,MAFE4C,EAAAA,GAAMD,MAAMrD,EAAE,kBAAmB,6CAsRM6E,UAAU,YAAWC,SAAA,EAEtDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACxE9E,EAAE,qBAAsB,0BAE3B4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,cAAe,gBAAgB,KAAC+E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEG,KAAK,OACLnB,MAAOtC,EAAUG,YACjBuD,SAAWC,GAAMvB,EAAkB,cAAeuB,EAAEC,OAAOtB,OAC3Dc,UAAS,mJAAAI,OACPxC,EAAOb,YAAc,iBAAmB,mBAE1C0D,YAAatF,EAAE,mBAAoB,wBAEpCyC,EAAOb,cACNmD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAErC,EAAOb,kBAIrDgD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,eAAgB,iBAAiB,KAAC+E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEtEC,EAAAA,EAAAA,KAAA,SACEG,KAAK,OACLnB,MAAOtC,EAAUK,aACjBqD,SAAWC,GAAMvB,EAAkB,eAAgBuB,EAAEC,OAAOtB,OAC5Dc,UAAS,mJAAAI,OACPxC,EAAOX,aAAe,iBAAmB,mBAE3CwD,YAAatF,EAAE,oBAAqB,yBAErCyC,EAAOX,eACNiD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAErC,EAAOX,yBAOzD8C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gGAA+FC,SAAA,EAC5GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,gEAA+DC,SAC1E9E,EAAE,uBAAwB,4BAE7B4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,oBAAqB,sBAAsB,KAAC+E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEhFF,EAAAA,EAAAA,MAAA,UACEb,MAAOtC,EAAUI,kBACjBsD,SAAWC,GAAMvB,EAAkB,oBAAqBuB,EAAEC,OAAOtB,OACjEc,UAAS,mJAAAI,OACPxC,EAAOZ,kBAAoB,iBAAmB,mBAC7CiD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,GAAEe,SAAE9E,EAAE,0BAA2B,+BAC9CmB,EAAmBoE,IAAIgB,IACtBxB,EAAAA,EAAAA,KAAA,UAA0BhB,MAAOwC,EAAShD,KAAKuB,SAC5CyB,EAAShD,MADCgD,EAASxD,QAKzBN,EAAOZ,oBACNkD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAErC,EAAOZ,wBAIrD+C,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,cAAe,gBAAgB,KAAC+E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEG,KAAK,SACLsB,KAAK,OACLC,IAAI,IACJ1C,MAAOtC,EAAUM,YACjBoD,SAAWC,GAAMvB,EAAkB,cAAeuB,EAAEC,OAAOtB,OAC3Dc,UAAS,mJAAAI,OACPxC,EAAOV,YAAc,iBAAmB,mBAE1CuD,YAAY,UAEdP,EAAAA,EAAAA,KAAA,QAAMF,UAAU,0DAAyDC,SACtE9E,EAAE,MAAO,YAGbyC,EAAOV,cACNgD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAErC,EAAOV,wBAOzD6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kEAAiEC,SAC5E9E,EAAE,qBAAsB,0BAE3B4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9E,EAAE,cAAe,mBAEpB+E,EAAAA,EAAAA,KAAA,SACEG,KAAK,OACLnB,MAAOtC,EAAUO,YACjBmD,SAAWC,GAAMvB,EAAkB,cAAeuB,EAAEC,OAAOtB,OAC3Dc,UAAU,wKAIdD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,cAAe,gBAAgB,KAAC+E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEF,EAAAA,EAAAA,MAAA,UACEb,MAAOtC,EAAUW,YACjB+C,SAAWC,GAAMvB,EAAkB,cAAeuB,EAAEC,OAAOtB,OAC3Dc,UAAS,mJAAAI,OACPxC,EAAOL,YAAc,iBAAmB,mBACvC0C,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,GAAEe,SAAE9E,EAAE,oBAAqB,yBACxC0E,EAAaa,IAAIL,IAChBH,EAAAA,EAAAA,KAAA,UAAyBhB,MAAOmB,EAAKnB,MAAMe,SACxCI,EAAKP,OADKO,EAAKnB,WAKrBtB,EAAOL,cACN2C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAErC,EAAOL,qBAKvDwC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/E9E,EAAE,YAAa,aAAa,KAAC+E,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DC,EAAAA,EAAAA,KAAA,YACEhB,MAAOtC,EAAUY,UACjB8C,SAAWC,GAAMvB,EAAkB,YAAauB,EAAEC,OAAOtB,OACzD2C,KAAM,EACN7B,UAAS,mJAAAI,OACPxC,EAAOJ,UAAY,iBAAmB,mBAExCiD,YAAatF,EAAE,iBAAkB,qBAElCyC,EAAOJ,YACN0C,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAErC,EAAOJ,gBAIrDuC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9E,EAAE,gBAAiB,qBAEtB+E,EAAAA,EAAAA,KAAA,SACEG,KAAK,OACLnB,MAAOtC,EAAUa,cACjB6C,SAAWC,GAAMvB,EAAkB,gBAAiBuB,EAAEC,OAAOtB,OAC7Dc,UAAU,kKACVS,YAAatF,EAAE,qBAAsB,2BAGvC4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9E,EAAE,QAAS,uBAEd+E,EAAAA,EAAAA,KAAA,YACEhB,MAAOtC,EAAUc,MACjB4C,SAAWC,GAAMvB,EAAkB,QAASuB,EAAEC,OAAOtB,OACrD2C,KAAM,EACN7B,UAAU,kKACVS,YAAatF,EAAE,uBAAwB,yCAQjD4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oGAAmGC,SAAA,EAChHC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,kEAAiEC,SAC5E9E,EAAE,cAAe,kBAEpB4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6FAA4FC,SAAA,EACzGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,2DACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDAAmDC,SAC7D9E,EAAE,gBAAiB,mDAEtB4E,EAAAA,EAAAA,MAAA,UACEM,KAAK,SACLL,UAAU,sFAAqFC,SAAA,EAE/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ7E,EAAE,cAAe,oBAEpB+E,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDAAmDC,SAC7D9E,EAAE,mBAAoB,6DAM7B4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEG,KAAK,SACLF,QAASA,IAAM3E,EAAS,wBACxBwE,UAAU,+FAA8FC,SAEvG9E,EAAE,SAAU,aAEf+E,EAAAA,EAAAA,KAAA,UACEG,KAAK,SACLyB,SAAUlG,EACVoE,UAAU,kIAAiIC,SAE1IrE,GACCmE,EAAAA,EAAAA,MAAAgC,EAAAA,SAAA,CAAA9B,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZ7E,EAAE,aAAc,qBAGnB4E,EAAAA,EAAAA,MAAAgC,EAAAA,SAAA,CAAA9B,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BACZ7E,EAAE,cAAe,8BAUjB,cAAde,IACC6D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gEACZ7E,EAAE,qBAAsB,2BAG3B+E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,uDAAsDC,SAClE3D,EAAmBoE,IAAKgB,IACvB3B,EAAAA,EAAAA,MAAA,OAAuBC,UAAU,+FAA8FC,SAAA,EAC7HF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEyB,EAAShD,QAEZwB,EAAAA,EAAAA,KAAA,QAAMF,UAAS,8CAAAI,OACO,WAApBsB,EAAStD,OACL,uEACA,gEACH6B,SACA9E,EAAEuG,EAAStD,OAAQsD,EAAStD,cAIjC2B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEyB,EAAS9C,oBAE/DmB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEyB,EAAS7C,YAE/DkB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qCACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEyB,EAAS5C,cAE/DiB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,6CACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAAEyB,EAAS3C,wBAIjEgB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAClCF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,iGAAgGC,SAAA,EAChHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZ7E,EAAE,OAAQ,YAEb4E,EAAAA,EAAAA,MAAA,UAAQC,UAAU,mGAAkGC,SAAA,EAClHC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sBACZ7E,EAAE,SAAU,kBAxCTuG,EAASxD,UAkDZ,iBAAdhC,IACC6D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gEACZ7E,EAAE,wBAAyB,8BAG9B4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8DAA6DC,SACxE9E,EAAE,yBAA0B,+BAG/B4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9E,EAAE,YAAa,iBAElB+E,EAAAA,EAAAA,KAAA,SACEG,KAAK,OACLL,UAAU,kKACVS,YAAatF,EAAE,iBAAkB,0BAIrC4E,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/E9E,EAAE,eAAgB,oBAErB+E,EAAAA,EAAAA,KAAA,SACEG,KAAK,OACLL,UAAU,kKACVS,YAAatF,EAAE,oBAAqB,gCAK1C+E,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFC,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uBACZ7E,EAAE,kBAAmB,6BAM5B4E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8DAA6DC,SAAA,EAC1EC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5C9E,EAAE,oCAAqC,oD", "sources": ["pages/Financial/InsurancePage.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst InsurancePage = () => {\n  const { t, isRTL } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [loading, setLoading] = useState(false);\n\n  // Check if we're on the \"new\" route to auto-open the claims form\n  const isNewClaim = location.pathname.includes('/new');\n  const [activeTab, setActiveTab] = useState(isNewClaim ? 'submit' : 'claims');\n  const [claims, setClaims] = useState([]);\n  const [insuranceProviders, setInsuranceProviders] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n\n  // Claim form state\n  const [claimForm, setClaimForm] = useState({\n    patientId: '',\n    patientName: '',\n    insuranceProvider: '',\n    policyNumber: '',\n    claimAmount: '',\n    serviceDate: new Date().toISOString().split('T')[0],\n    serviceType: '',\n    diagnosis: '',\n    treatmentCode: '',\n    notes: '',\n    attachments: []\n  });\n\n  const [errors, setErrors] = useState({});\n\n  useEffect(() => {\n    loadClaims();\n    loadInsuranceProviders();\n  }, []);\n\n  const loadClaims = async () => {\n    setLoading(true);\n    try {\n      // Mock data - replace with actual API call\n      const mockClaims = [\n        {\n          id: 'CLM001',\n          patientName: 'أحمد محمد علي',\n          patientNameEn: 'Ahmed Mohammed Ali',\n          insuranceProvider: 'Bupa Arabia',\n          policyNumber: 'BP123456789',\n          claimAmount: 2500,\n          serviceDate: '2024-01-15',\n          serviceType: 'Physical Therapy',\n          status: 'approved',\n          submissionDate: '2024-01-16',\n          approvalDate: '2024-01-20',\n          diagnosis: 'Cerebral Palsy'\n        },\n        {\n          id: 'CLM002',\n          patientName: 'فاطمة أحمد',\n          patientNameEn: 'Fatima Ahmed',\n          insuranceProvider: 'Tawuniya',\n          policyNumber: 'TW987654321',\n          claimAmount: 1800,\n          serviceDate: '2024-01-14',\n          serviceType: 'Initial Assessment',\n          status: 'pending',\n          submissionDate: '2024-01-15',\n          diagnosis: 'Spinal Cord Injury'\n        },\n        {\n          id: 'CLM003',\n          patientName: 'محمد سالم',\n          patientNameEn: 'Mohammed Salem',\n          insuranceProvider: 'Medgulf',\n          policyNumber: 'MG456789123',\n          claimAmount: 3200,\n          serviceDate: '2024-01-13',\n          serviceType: 'Treatment Package',\n          status: 'denied',\n          submissionDate: '2024-01-14',\n          denialReason: 'Pre-authorization required',\n          diagnosis: 'Muscular Dystrophy'\n        }\n      ];\n      setClaims(mockClaims);\n    } catch (error) {\n      toast.error(t('errorLoadingClaims', 'Error loading insurance claims'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const loadInsuranceProviders = async () => {\n    try {\n      // Mock data - replace with actual API call\n      const mockProviders = [\n        {\n          id: 'bupa',\n          name: 'Bupa Arabia',\n          nameAr: 'بوبا العربية',\n          contactNumber: '+966-11-123-4567',\n          email: '<EMAIL>',\n          website: 'www.bupa.com.sa',\n          status: 'active',\n          contractNumber: 'BUPA-2024-001'\n        },\n        {\n          id: 'tawuniya',\n          name: 'Tawuniya',\n          nameAr: 'التعاونية',\n          contactNumber: '+966-11-234-5678',\n          email: '<EMAIL>',\n          website: 'www.tawuniya.com.sa',\n          status: 'active',\n          contractNumber: 'TAW-2024-002'\n        },\n        {\n          id: 'medgulf',\n          name: 'Medgulf',\n          nameAr: 'مدجلف',\n          contactNumber: '+966-11-345-6789',\n          email: '<EMAIL>',\n          website: 'www.medgulf.com.sa',\n          status: 'active',\n          contractNumber: 'MED-2024-003'\n        }\n      ];\n      setInsuranceProviders(mockProviders);\n    } catch (error) {\n      toast.error(t('errorLoadingProviders', 'Error loading insurance providers'));\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setClaimForm(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const validateClaimForm = () => {\n    const newErrors = {};\n\n    if (!claimForm.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n    if (!claimForm.insuranceProvider) {\n      newErrors.insuranceProvider = t('insuranceProviderRequired', 'Insurance provider is required');\n    }\n    if (!claimForm.policyNumber.trim()) {\n      newErrors.policyNumber = t('policyNumberRequired', 'Policy number is required');\n    }\n    if (!claimForm.claimAmount || claimForm.claimAmount <= 0) {\n      newErrors.claimAmount = t('validAmountRequired', 'Valid claim amount is required');\n    }\n    if (!claimForm.serviceType) {\n      newErrors.serviceType = t('serviceTypeRequired', 'Service type is required');\n    }\n    if (!claimForm.diagnosis.trim()) {\n      newErrors.diagnosis = t('diagnosisRequired', 'Diagnosis is required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmitClaim = async (e) => {\n    e.preventDefault();\n    \n    if (!validateClaimForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Mock API call - replace with actual claim submission\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      const newClaim = {\n        ...claimForm,\n        id: `CLM${Date.now()}`,\n        status: 'pending',\n        submissionDate: new Date().toISOString().split('T')[0]\n      };\n      \n      setClaims(prev => [newClaim, ...prev]);\n      setClaimForm({\n        patientId: '',\n        patientName: '',\n        insuranceProvider: '',\n        policyNumber: '',\n        claimAmount: '',\n        serviceDate: new Date().toISOString().split('T')[0],\n        serviceType: '',\n        diagnosis: '',\n        treatmentCode: '',\n        notes: '',\n        attachments: []\n      });\n      \n      toast.success(t('claimSubmittedSuccessfully', 'Insurance claim submitted successfully'));\n      setActiveTab('claims');\n    } catch (error) {\n      toast.error(t('errorSubmittingClaim', 'Error submitting insurance claim'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'approved':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'pending':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'denied':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      case 'processing':\n        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const filteredClaims = claims.filter(claim => {\n    const matchesSearch = claim.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         claim.patientNameEn?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         claim.policyNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         claim.insuranceProvider.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'all' || claim.status === filterStatus;\n    return matchesSearch && matchesStatus;\n  });\n\n  const serviceTypes = [\n    { value: 'initial_assessment', label: t('initialAssessment', 'Initial Assessment') },\n    { value: 'physical_therapy', label: t('physicalTherapy', 'Physical Therapy') },\n    { value: 'occupational_therapy', label: t('occupationalTherapy', 'Occupational Therapy') },\n    { value: 'speech_therapy', label: t('speechTherapy', 'Speech Therapy') },\n    { value: 'treatment_package', label: t('treatmentPackage', 'Treatment Package') },\n    { value: 'consultation', label: t('consultation', 'Consultation') },\n    { value: 'follow_up', label: t('followUp', 'Follow-up') }\n  ];\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          {t('insuranceManagement', 'Insurance Management')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          {t('insuranceManagementDesc', 'Manage insurance claims, providers, and verification processes')}\n        </p>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"mb-6\">\n        <div className=\"border-b border-gray-200 dark:border-gray-600\">\n          <nav className=\"-mb-px flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('claims')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'claims'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-file-medical mr-2\"></i>\n              {t('insuranceClaims', 'Insurance Claims')}\n            </button>\n            <button\n              onClick={() => setActiveTab('submit')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'submit'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-plus mr-2\"></i>\n              {t('submitClaim', 'Submit Claim')}\n            </button>\n            <button\n              onClick={() => setActiveTab('providers')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'providers'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-shield-alt mr-2\"></i>\n              {t('insuranceProviders', 'Insurance Providers')}\n            </button>\n            <button\n              onClick={() => setActiveTab('verification')}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'verification'\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n              }`}\n            >\n              <i className=\"fas fa-check-circle mr-2\"></i>\n              {t('verification', 'Verification')}\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'claims' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n            <div className=\"flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                <i className=\"fas fa-file-medical text-blue-600 dark:text-blue-400 mr-2\"></i>\n                {t('insuranceClaims', 'Insurance Claims')}\n              </h2>\n              \n              <div className=\"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    placeholder={t('searchClaims', 'Search claims...')}\n                    className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  />\n                  <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n                </div>\n                \n                <select\n                  value={filterStatus}\n                  onChange={(e) => setFilterStatus(e.target.value)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                >\n                  <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n                  <option value=\"approved\">{t('approved', 'Approved')}</option>\n                  <option value=\"pending\">{t('pending', 'Pending')}</option>\n                  <option value=\"denied\">{t('denied', 'Denied')}</option>\n                  <option value=\"processing\">{t('processing', 'Processing')}</option>\n                </select>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50 dark:bg-gray-700\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('claimId', 'Claim ID')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('patient', 'Patient')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('provider', 'Provider')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('amount', 'Amount')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('status', 'Status')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('submissionDate', 'Submission Date')}\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                    {t('actions', 'Actions')}\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n                {filteredClaims.map((claim) => (\n                  <tr key={claim.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {claim.id}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {isRTL ? claim.patientName : claim.patientNameEn}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {claim.policyNumber}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm text-gray-900 dark:text-white\">\n                        {claim.insuranceProvider}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {claim.claimAmount.toLocaleString()} {t('sar', 'SAR')}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(claim.status)}`}>\n                        {t(claim.status, claim.status)}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                      {claim.submissionDate}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <div className=\"flex space-x-2\">\n                        <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-200\">\n                          <i className=\"fas fa-eye\"></i>\n                        </button>\n                        <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-200\">\n                          <i className=\"fas fa-download\"></i>\n                        </button>\n                        {claim.status === 'pending' && (\n                          <button className=\"text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-200\">\n                            <i className=\"fas fa-edit\"></i>\n                          </button>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n          \n          {filteredClaims.length === 0 && (\n            <div className=\"text-center py-12\">\n              <i className=\"fas fa-file-medical text-4xl text-gray-400 mb-4\"></i>\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                {t('noClaimsFound', 'No insurance claims found')}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Submit Claim Tab */}\n      {activeTab === 'submit' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-plus text-green-600 dark:text-green-400 mr-2\"></i>\n            {t('submitNewClaim', 'Submit New Insurance Claim')}\n          </h2>\n\n          <form onSubmit={handleSubmitClaim} className=\"space-y-6\">\n            {/* Patient Information */}\n            <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n                {t('patientInformation', 'Patient Information')}\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={claimForm.patientName}\n                    onChange={(e) => handleInputChange('patientName', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.patientName ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder={t('enterPatientName', 'Enter patient name')}\n                  />\n                  {errors.patientName && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('policyNumber', 'Policy Number')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={claimForm.policyNumber}\n                    onChange={(e) => handleInputChange('policyNumber', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.policyNumber ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder={t('enterPolicyNumber', 'Enter policy number')}\n                  />\n                  {errors.policyNumber && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.policyNumber}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Insurance Information */}\n            <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-green-900 dark:text-green-100 mb-4\">\n                {t('insuranceInformation', 'Insurance Information')}\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('insuranceProvider', 'Insurance Provider')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <select\n                    value={claimForm.insuranceProvider}\n                    onChange={(e) => handleInputChange('insuranceProvider', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.insuranceProvider ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                  >\n                    <option value=\"\">{t('selectInsuranceProvider', 'Select insurance provider')}</option>\n                    {insuranceProviders.map(provider => (\n                      <option key={provider.id} value={provider.name}>\n                        {provider.name}\n                      </option>\n                    ))}\n                  </select>\n                  {errors.insuranceProvider && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.insuranceProvider}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('claimAmount', 'Claim Amount')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <div className=\"relative\">\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      value={claimForm.claimAmount}\n                      onChange={(e) => handleInputChange('claimAmount', e.target.value)}\n                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                        errors.claimAmount ? 'border-red-500' : 'border-gray-300'\n                      }`}\n                      placeholder=\"0.00\"\n                    />\n                    <span className=\"absolute right-3 top-2 text-gray-500 dark:text-gray-400\">\n                      {t('sar', 'SAR')}\n                    </span>\n                  </div>\n                  {errors.claimAmount && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.claimAmount}</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Service Information */}\n            <div className=\"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-purple-900 dark:text-purple-100 mb-4\">\n                {t('serviceInformation', 'Service Information')}\n              </h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('serviceDate', 'Service Date')}\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={claimForm.serviceDate}\n                    onChange={(e) => handleInputChange('serviceDate', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('serviceType', 'Service Type')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <select\n                    value={claimForm.serviceType}\n                    onChange={(e) => handleInputChange('serviceType', e.target.value)}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.serviceType ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                  >\n                    <option value=\"\">{t('selectServiceType', 'Select service type')}</option>\n                    {serviceTypes.map(type => (\n                      <option key={type.value} value={type.value}>\n                        {type.label}\n                      </option>\n                    ))}\n                  </select>\n                  {errors.serviceType && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.serviceType}</p>\n                  )}\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('diagnosis', 'Diagnosis')} <span className=\"text-red-500\">*</span>\n                  </label>\n                  <textarea\n                    value={claimForm.diagnosis}\n                    onChange={(e) => handleInputChange('diagnosis', e.target.value)}\n                    rows={3}\n                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                      errors.diagnosis ? 'border-red-500' : 'border-gray-300'\n                    }`}\n                    placeholder={t('enterDiagnosis', 'Enter diagnosis')}\n                  />\n                  {errors.diagnosis && (\n                    <p className=\"text-red-500 text-sm mt-1\">{errors.diagnosis}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('treatmentCode', 'Treatment Code')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={claimForm.treatmentCode}\n                    onChange={(e) => handleInputChange('treatmentCode', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder={t('enterTreatmentCode', 'Enter treatment code')}\n                  />\n\n                  <div className=\"mt-3\">\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      {t('notes', 'Additional Notes')}\n                    </label>\n                    <textarea\n                      value={claimForm.notes}\n                      onChange={(e) => handleInputChange('notes', e.target.value)}\n                      rows={2}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                      placeholder={t('enterAdditionalNotes', 'Enter additional notes')}\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Attachments */}\n            <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n              <h3 className=\"text-md font-semibold text-yellow-900 dark:text-yellow-100 mb-4\">\n                {t('attachments', 'Attachments')}\n              </h3>\n              <div className=\"border-2 border-dashed border-yellow-300 dark:border-yellow-600 rounded-lg p-6 text-center\">\n                <i className=\"fas fa-cloud-upload-alt text-3xl text-yellow-400 mb-2\"></i>\n                <p className=\"text-sm text-yellow-700 dark:text-yellow-300 mb-2\">\n                  {t('dragDropFiles', 'Drag and drop files here, or click to select')}\n                </p>\n                <button\n                  type=\"button\"\n                  className=\"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors\"\n                >\n                  <i className=\"fas fa-plus mr-2\"></i>\n                  {t('selectFiles', 'Select Files')}\n                </button>\n                <p className=\"text-xs text-yellow-600 dark:text-yellow-400 mt-2\">\n                  {t('supportedFormats', 'Supported formats: PDF, JPG, PNG, DOC (Max 10MB)')}\n                </p>\n              </div>\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"flex justify-end space-x-4\">\n              <button\n                type=\"button\"\n                onClick={() => navigate('/financial/insurance')}\n                className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {loading ? (\n                  <>\n                    <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                    {t('submitting', 'Submitting...')}\n                  </>\n                ) : (\n                  <>\n                    <i className=\"fas fa-paper-plane mr-2\"></i>\n                    {t('submitClaim', 'Submit Claim')}\n                  </>\n                )}\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      {/* Insurance Providers Tab */}\n      {activeTab === 'providers' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-shield-alt text-purple-600 dark:text-purple-400 mr-2\"></i>\n            {t('insuranceProviders', 'Insurance Providers')}\n          </h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {insuranceProviders.map((provider) => (\n              <div key={provider.id} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-shadow\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                    {provider.name}\n                  </h3>\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n                    provider.status === 'active'\n                      ? 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400'\n                      : 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400'\n                  }`}>\n                    {t(provider.status, provider.status)}\n                  </span>\n                </div>\n\n                <div className=\"space-y-2 text-sm\">\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-phone text-gray-400 mr-2\"></i>\n                    <span className=\"text-gray-600 dark:text-gray-400\">{provider.contactNumber}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-envelope text-gray-400 mr-2\"></i>\n                    <span className=\"text-gray-600 dark:text-gray-400\">{provider.email}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-globe text-gray-400 mr-2\"></i>\n                    <span className=\"text-gray-600 dark:text-gray-400\">{provider.website}</span>\n                  </div>\n                  <div className=\"flex items-center\">\n                    <i className=\"fas fa-file-contract text-gray-400 mr-2\"></i>\n                    <span className=\"text-gray-600 dark:text-gray-400\">{provider.contractNumber}</span>\n                  </div>\n                </div>\n\n                <div className=\"mt-4 flex space-x-2\">\n                  <button className=\"flex-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm\">\n                    <i className=\"fas fa-edit mr-1\"></i>\n                    {t('edit', 'Edit')}\n                  </button>\n                  <button className=\"flex-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm\">\n                    <i className=\"fas fa-check mr-1\"></i>\n                    {t('verify', 'Verify')}\n                  </button>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Verification Tab */}\n      {activeTab === 'verification' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-6\">\n            <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 mr-2\"></i>\n            {t('insuranceVerification', 'Insurance Verification')}\n          </h2>\n\n          <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n            <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n              {t('verifyPatientInsurance', 'Verify Patient Insurance')}\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('patientId', 'Patient ID')}\n                </label>\n                <input\n                  type=\"text\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterPatientId', 'Enter patient ID')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('policyNumber', 'Policy Number')}\n                </label>\n                <input\n                  type=\"text\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('enterPolicyNumber', 'Enter policy number')}\n                />\n              </div>\n            </div>\n\n            <div className=\"mt-4\">\n              <button className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n                <i className=\"fas fa-search mr-2\"></i>\n                {t('verifyInsurance', 'Verify Insurance')}\n              </button>\n            </div>\n          </div>\n\n          {/* Verification Results Placeholder */}\n          <div className=\"mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-6 text-center\">\n            <i className=\"fas fa-shield-alt text-4xl text-gray-400 mb-4\"></i>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('verificationResultsWillAppearHere', 'Verification results will appear here')}\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default InsurancePage;\n"], "names": ["InsurancePage", "t", "isRTL", "useLanguage", "user", "useAuth", "navigate", "useNavigate", "location", "useLocation", "loading", "setLoading", "useState", "isNew<PERSON>laim", "pathname", "includes", "activeTab", "setActiveTab", "claims", "setClaims", "insuranceProviders", "setInsuranceProviders", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "claimForm", "setClaimForm", "patientId", "patientName", "insuranceProvider", "policyNumber", "claimAmount", "serviceDate", "Date", "toISOString", "split", "serviceType", "diagnosis", "treatmentCode", "notes", "attachments", "errors", "setErrors", "useEffect", "loadClaims", "loadInsuranceProviders", "async", "id", "patientNameEn", "status", "submissionDate", "approvalDate", "denialReason", "error", "toast", "name", "nameAr", "contactNumber", "email", "website", "contractNumber", "handleInputChange", "field", "value", "prev", "_objectSpread", "getStatusColor", "filteredClaims", "filter", "claim", "_claim$patientNameEn", "matchesSearch", "toLowerCase", "matchesStatus", "serviceTypes", "label", "_jsxs", "className", "children", "_jsx", "onClick", "concat", "type", "onChange", "e", "target", "placeholder", "map", "toLocaleString", "length", "onSubmit", "preventDefault", "validateClaimForm", "newErrors", "trim", "Object", "keys", "Promise", "resolve", "setTimeout", "newClaim", "now", "success", "provider", "step", "min", "rows", "disabled", "_Fragment"], "sourceRoot": ""}