{"version": 3, "file": "static/js/332.13e1358d.chunk.js", "mappings": "+LAIA,MAaA,EAb0BA,KACxB,MAAM,UAAEC,EAAS,YAAEC,IAAgBC,EAAAA,EAAAA,KAEnC,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2CAA0CC,UACvDF,EAAAA,EAAAA,KAACG,EAAAA,QAAa,CACZN,UAAWA,EACXC,YAAaA,M", "sources": ["pages/EducationFormPage.jsx"], "sourcesContent": ["import React from 'react';\nimport { useParams } from 'react-router-dom';\nimport EducationForm from '../components/EducationForm/EducationForm';\n\nconst EducationFormPage = () => {\n  const { patientId, educationId } = useParams();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <EducationForm \n        patientId={patientId} \n        educationId={educationId} \n      />\n    </div>\n  );\n};\n\nexport default EducationFormPage;\n"], "names": ["EducationFormPage", "patientId", "educationId", "useParams", "_jsx", "className", "children", "EducationForm"], "sourceRoot": ""}