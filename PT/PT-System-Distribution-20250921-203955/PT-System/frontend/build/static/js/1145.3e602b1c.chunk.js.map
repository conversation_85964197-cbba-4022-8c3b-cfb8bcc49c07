{"version": 3, "file": "static/js/1145.3e602b1c.chunk.js", "mappings": "oNAGA,MAkMA,EAlM6BA,IAA8C,IAA7C,SAAEC,EAAQ,kBAAEC,EAAiB,OAAEC,GAAQH,EACnE,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERC,EAAoB,CACxB,CAAEC,MAAO,UAAWC,MAAOJ,EAAE,UAAW,WAAYK,MAAO,SAC3D,CAAEF,MAAO,SAAUC,MAAOJ,EAAE,SAAU,UAAWK,MAAO,OACxD,CAAEF,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,QAASK,MAAO,UAClD,CAAEF,MAAO,kBAAmBC,MAAOJ,EAAE,iBAAkB,mBAAoBK,MAAO,WAG9EC,EAAc,CAClB,CAAEH,MAAO,cAAeC,MAAOJ,EAAE,cAAe,eAAgBK,MAAO,SACvE,CAAEF,MAAO,UAAWC,MAAOJ,EAAE,UAAW,WAAYK,MAAO,UAC3D,CAAEF,MAAO,WAAYC,MAAOJ,EAAE,WAAY,YAAaK,MAAO,OAC9D,CAAEF,MAAO,YAAaC,MAAOJ,EAAE,YAAa,aAAcK,MAAO,QACjE,CAAEF,MAAO,WAAYC,MAAOJ,EAAE,WAAY,YAAaK,MAAO,SAG1DE,EAAqB,CACzB,CAAEJ,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,QAASK,MAAO,SAClD,CAAEF,MAAO,SAAUC,MAAOJ,EAAE,SAAU,UAAWK,MAAO,QACxD,CAAEF,MAAO,MAAOC,MAAOJ,EAAE,MAAO,OAAQK,MAAO,UAC/C,CAAEF,MAAO,WAAYC,MAAOJ,EAAE,WAAY,YAAaK,MAAO,QAG1DG,EAAkBA,CAACH,EAAOI,KAC9B,MAAMC,EAAc,+DAEpB,IAAID,EAkBF,MAAM,GAANE,OAAUD,EAAW,4IAjBrB,OAAQL,GACN,IAAK,QACH,MAAM,GAANM,OAAUD,EAAW,gHACvB,IAAK,MACH,MAAM,GAANC,OAAUD,EAAW,oGACvB,IAAK,SACH,MAAM,GAANC,OAAUD,EAAW,sHACvB,IAAK,SACH,MAAM,GAANC,OAAUD,EAAW,sHACvB,IAAK,OACH,MAAM,GAANC,OAAUD,EAAW,0GAGvB,QACE,MAAM,GAANC,OAAUD,EAAW,4GAO7B,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DACZb,EAAE,gBAAiB,qCAGtBY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,oBAAqB,yBAE1Be,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDZ,EAAkBc,IAAKC,IACtBL,EAAAA,EAAAA,MAAA,SAEEC,UAAWL,EAAgBS,EAAOZ,MAAOR,EAASqB,oBAAsBD,EAAOd,OAAOW,SAAA,EAEtFC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLC,KAAK,oBACLjB,MAAOc,EAAOd,MACdkB,QAASxB,EAASqB,oBAAsBD,EAAOd,MAC/CmB,SAAWC,GAAMzB,EAAkB,oBAAqByB,EAAEC,OAAOrB,OACjEU,UAAU,aAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCAAkCC,UAC/CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAEG,EAAOb,YAZ3Ca,EAAOd,cAoBpBS,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,cAAe,8BAEpBe,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDR,EAAYU,IAAKC,IAChBL,EAAAA,EAAAA,MAAA,SAEEC,UAAWL,EAAgBS,EAAOZ,MAAOR,EAAS4B,cAAgBR,EAAOd,OAAOW,SAAA,EAEhFC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLC,KAAK,cACLjB,MAAOc,EAAOd,MACdkB,QAASxB,EAAS4B,cAAgBR,EAAOd,MACzCmB,SAAWC,GAAMzB,EAAkB,cAAeyB,EAAEC,OAAOrB,OAC3DU,UAAU,aAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCAAkCC,UAC/CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAEG,EAAOb,YAZ3Ca,EAAOd,cAoBpBS,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,cAAe,mBAEpBe,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDP,EAAmBS,IAAKC,IACvBL,EAAAA,EAAAA,MAAA,SAEEC,UAAWL,EAAgBS,EAAOZ,MAAOR,EAAS6B,qBAAuBT,EAAOd,OAAOW,SAAA,EAEvFC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLC,KAAK,qBACLjB,MAAOc,EAAOd,MACdkB,QAASxB,EAAS6B,qBAAuBT,EAAOd,MAChDmB,SAAWC,GAAMzB,EAAkB,qBAAsByB,EAAEC,OAAOrB,OAClEU,UAAU,aAEZE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mCAAkCC,UAC/CC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAEG,EAAOb,YAZ3Ca,EAAOd,cAoBpBS,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wFAAuFC,SAAA,EACpGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DC,SAAA,EACvEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEACZb,EAAE,iBAAkB,oCAEvBY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,gEAA+DC,SAC7Ed,EAAE,aAAc,kCAEnBe,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACLQ,IAAI,IACJC,IAAI,KACJzB,MAAON,EAASgC,UAAUC,OAC1BR,SAAWC,GAAMzB,EAAkB,mBAAoByB,EAAEC,OAAOrB,OAChEU,UAAS,+IAAAF,OACPZ,EAAOgC,WAAa,iBAAmB,kBAEzCC,YAAY,SAEbjC,EAAOgC,aACNhB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEf,EAAOgC,iBAGrDnB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,gEAA+DC,SAC7Ed,EAAE,YAAa,iCAElBe,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACLQ,IAAI,IACJC,IAAI,KACJzB,MAAON,EAASgC,UAAUI,MAC1BX,SAAWC,GAAMzB,EAAkB,kBAAmByB,EAAEC,OAAOrB,OAC/DU,UAAS,+IAAAF,OACPZ,EAAOmC,UAAY,iBAAmB,kBAExCF,YAAY,SAEbjC,EAAOmC,YACNnB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEf,EAAOmC,mBAIvDnB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8CAA6CC,UAC1DF,EAAAA,EAAAA,MAAA,KAAAE,SAAA,EAAGC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,OAAW,IAAEd,EAAE,SAAU,WAAW,OAAGe,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,SAAa,IAAEd,EAAE,WAAY,aAAa,OAAGe,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,SAAa,IAAEd,EAAE,eAAgB,iBAAiB,OAAGe,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,UAAc,IAAEd,EAAE,aAAc,8BC0GxN,EApSmCJ,IAAwF,IAADuC,EAAAC,EAAA,IAAtF,SAAEvC,EAAQ,kBAAEC,EAAiB,aAAEuC,EAAY,gBAAEC,EAAe,kBAAEC,GAAmB3C,EACnH,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAERuC,EAAmB,CACvB,4BACA,0BACA,mBACA,gBACA,oBACA,wBACA,kBACA,oBACA,yBACA,sBAGIC,EAAkB,CACtB,gBACA,eACA,eACA,mBACA,UACA,gBACA,SACA,aACA,YACA,sBAGIC,EAAcA,KAClBL,EAAa,sBAAuB,CAClCM,SAAU,GACVC,SAAU,GACVC,UAAW,WACXC,gBAAiB,GACjBC,WAAW,KAuBf,OACEnC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,8DACZb,EAAE,sBAAuB,4CAG5BY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEd,EAAE,sBAAuB,2BAE5BY,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACL6B,QAASN,EACT7B,UAAU,2FAA0FC,SAAA,EAEpGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZb,EAAE,cAAe,sBAIO,QAFvBmC,EAELtC,EAASoD,2BAAmB,IAAAd,OAAA,EAA5BA,EAA8BnB,IAAI,CAAC2B,EAAUO,KAC5CtC,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,kEAAiEC,SAAA,EAC1FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4CAA2CC,SAAA,CACtDd,EAAE,WAAY,YAAY,IAAEkD,EAAQ,MAEvCnC,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACL6B,QAASA,IAAMV,EAAgB,sBAAuBY,GACtDrC,UAAU,oDAAmDC,UAE7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+BAIjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDC,SAAA,EACnEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,eAAgB,oBAErBY,EAAAA,EAAAA,MAAA,UACET,MAAOwC,EAASA,SAChBrB,SAAWC,GAAMgB,EAAkB,sBAAuBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOR,GAAQ,IAAEA,SAAUpB,EAAEC,OAAOrB,SACnGU,UAAU,sKAAqKC,SAAA,EAE/KC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,GAAEW,SAAEd,EAAE,iBAAkB,qBACrCwC,EAAiBxB,IAAKoC,IACrBrC,EAAAA,EAAAA,KAAA,UAAkBZ,MAAOiD,EAAItC,SAAEsC,GAAlBA,KAEfrC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,QAAOW,SAAEd,EAAE,QAAS,kBAItCY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,WAAY,qBAEjBe,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACLQ,IAAI,IACJxB,MAAOwC,EAASC,SAChBtB,SAAWC,GAAMgB,EAAkB,sBAAuBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOR,GAAQ,IAAEC,SAAUrB,EAAEC,OAAOrB,SACnGU,UAAU,sKACVmB,YAAY,WAIhBpB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,YAAa,gBAElBY,EAAAA,EAAAA,MAAA,UACET,MAAOwC,EAASE,UAChBvB,SAAWC,GAAMgB,EAAkB,sBAAuBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOR,GAAQ,IAAEE,UAAWtB,EAAEC,OAAOrB,SACpGU,UAAU,sKAAqKC,SAAA,EAE/KC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,MAAKW,SAAEd,EAAE,MAAO,UAC9Be,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,WAAUW,SAAEd,EAAE,WAAY,eACxCe,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,OAAMW,SAAEd,EAAE,OAAQ,oBAKtCY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,kBAAmB,uBAExBe,EAAAA,EAAAA,KAAA,YACEZ,MAAOwC,EAASG,gBAChBxB,SAAWC,GAAMgB,EAAkB,sBAAuBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOR,GAAQ,IAAEG,gBAAiBvB,EAAEC,OAAOrB,SAC1GkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,0BAA2B,gDAvEtCkD,MA6ETrD,EAASoD,qBAA+D,IAAxCpD,EAASoD,oBAAoBK,UAC9D1C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFC,SAAA,EACtGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iDACbE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDd,EAAE,oBAAqB,8BAE1BY,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACL6B,QAASN,EACT7B,UAAU,sFAAqFC,SAAA,EAE/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZb,EAAE,mBAAoB,gCAO/BY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEd,EAAE,gBAAiB,qBAEtBY,EAAAA,EAAAA,MAAA,UACEO,KAAK,SACL6B,QAzISO,KACnBlB,EAAa,gBAAiB,CAC5BmB,UAAW,GACXZ,SAAU,GACVa,SAAU,GACVC,cAAe,eAqIP7C,UAAU,uFAAsFC,SAAA,EAEhGC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZb,EAAE,eAAgB,uBAIA,QAFjBoC,EAELvC,EAAS8D,qBAAa,IAAAvB,OAAA,EAAtBA,EAAwBpB,IAAI,CAACwC,EAAWN,KACvCtC,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,kEAAiEC,SAAA,EAC1FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4CAA2CC,SAAA,CACtDd,EAAE,YAAa,aAAa,IAAEkD,EAAQ,MAEzCnC,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACL6B,QAASA,IAAMV,EAAgB,gBAAiBY,GAChDrC,UAAU,oDAAmDC,UAE7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+BAIjBD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,gBAAiB,qBAEtBY,EAAAA,EAAAA,MAAA,UACET,MAAOqD,EAAUA,UACjBlC,SAAWC,GAAMgB,EAAkB,gBAAiBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOK,GAAS,IAAEA,UAAWjC,EAAEC,OAAOrB,SAC/FU,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,GAAEW,SAAEd,EAAE,kBAAmB,sBACtCyC,EAAgBzB,IAAK4C,IACpB7C,EAAAA,EAAAA,KAAA,UAAiBZ,MAAOyD,EAAG9C,SAAE8C,GAAhBA,KAEf7C,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,QAAOW,SAAEd,EAAE,QAAS,kBAItCY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,WAAY,qBAEjBe,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACLQ,IAAI,IACJxB,MAAOqD,EAAUZ,SACjBtB,SAAWC,GAAMgB,EAAkB,gBAAiBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOK,GAAS,IAAEZ,SAAUrB,EAAEC,OAAOrB,SAC9FU,UAAU,kKACVmB,YAAY,WAIhBpB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,gBAAiB,oBAEtBY,EAAAA,EAAAA,MAAA,UACET,MAAOqD,EAAUE,cACjBpC,SAAWC,GAAMgB,EAAkB,gBAAiBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOK,GAAS,IAAEE,cAAenC,EAAEC,OAAOrB,SACnGU,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,iBAAgBW,SAAEd,EAAE,gBAAiB,qBACnDe,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,YAAWW,SAAEd,EAAE,YAAa,gBAC1Ce,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,qBAAoBW,SAAEd,EAAE,oBAAqB,yBAC3De,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,gBAAeW,SAAEd,EAAE,eAAgB,6BAKvDY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,WAAY,qBAEjBe,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAOqD,EAAUC,SACjBnC,SAAWC,GAAMgB,EAAkB,gBAAiBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOK,GAAS,IAAEC,SAAUlC,EAAEC,OAAOrB,SAC9FU,UAAU,kKACVmB,YAAahC,EAAE,oBAAqB,0DAxEhCkD,QAgFdtC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,sBAAuB,2BAE5Be,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAASgE,oBAChBvC,SAAWC,GAAMzB,EAAkB,sBAAuByB,EAAEC,OAAOrB,OACnEkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,wBAAyB,0ECPpD,EArR6BJ,IAAiE,IAADkE,EAAA,IAA/D,SAAEjE,EAAQ,kBAAEC,EAAiB,kBAAEyC,EAAiB,OAAExC,GAAQH,EACtF,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,KAER8D,EAAwB,CAC5B,CAAE5D,MAAO,YAAaC,MAAOJ,EAAE,YAAa,aAAcK,MAAO,SACjE,CAAEF,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,QAASK,MAAO,QAClD,CAAEF,MAAO,eAAgBC,MAAOJ,EAAE,eAAgB,gBAAiBK,MAAO,UAC1E,CAAEF,MAAO,oBAAqBC,MAAOJ,EAAE,mBAAoB,qBAAsBK,MAAO,QAGpF2D,EAAoB,CACxB,CAAE7D,MAAO,YAAaC,MAAOJ,EAAE,YAAa,uBAAwBK,MAAO,SAC3E,CAAEF,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,iBAAkBK,MAAO,QAC3D,CAAEF,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,iBAAkBK,MAAO,UAC3D,CAAEF,MAAO,OAAQC,MAAOJ,EAAE,OAAQ,eAAgBK,MAAO,OACzD,CAAEF,MAAO,iBAAkBC,MAAOJ,EAAE,gBAAiB,kBAAmBK,MAAO,SAG3EG,EAAkBA,CAACH,EAAOI,KAC9B,MAAMC,EAAc,2EAEpB,IAAID,EAgBF,MAAM,GAANE,OAAUD,EAAW,4IAfrB,OAAQL,GACN,IAAK,QACH,MAAM,GAANM,OAAUD,EAAW,gHACvB,IAAK,OACH,MAAM,GAANC,OAAUD,EAAW,0GACvB,IAAK,SACH,MAAM,GAANC,OAAUD,EAAW,sHACvB,IAAK,MACH,MAAM,GAANC,OAAUD,EAAW,oGAGvB,QACE,MAAM,GAANC,OAAUD,EAAW,4GAO7B,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oEACZb,EAAE,0BAA2B,mCAGhCY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEd,EAAE,0BAA2B,+BAEP,QADpB8D,EACJjE,EAASoE,uBAAe,IAAAH,OAAA,EAAxBA,EAA0B9C,IAAI,CAACkD,EAAMhB,KACpCtC,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,kEAAiEC,SAAA,EAC1FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,iDAAgDC,SAAA,CAC3Dd,EAAE,OAAQ,QAAQ,IAAEkD,EAAQ,EAAE,KAAGgB,EAAKA,SAEzCnD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8BAA6BC,UAC1CF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,oBAAmBC,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACEI,KAAK,WACLE,QAAS6C,EAAKC,SACd7C,SAAWC,GAAMgB,EAAkB,kBAAmBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOe,GAAI,IAAEC,SAAU5C,EAAEC,OAAOH,WAC3FR,UAAU,UAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDd,EAAE,eAAgB,4BAK3BY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,sBAAuB,2BAE5Be,EAAAA,EAAAA,KAAA,YACEZ,MAAO+D,EAAKE,SACZ9C,SAAWC,GAAMgB,EAAkB,kBAAmBW,GAAKC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAOe,GAAI,IAAEE,SAAU7C,EAAEC,OAAOrB,SAC3FkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,mBAAoB,8CA5B/BkD,QAoCdtC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/Ed,EAAE,oBAAqB,sBAAsB,KAACe,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEhFC,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAASwE,kBAChB/C,SAAWC,GAAMzB,EAAkB,oBAAqByB,EAAEC,OAAOrB,OACjEkD,KAAM,EACNxC,UAAS,uJAAAF,OACPZ,EAAOsE,kBAAoB,iBAAmB,mBAEhDrC,YAAahC,EAAE,+BAAgC,2DAEhDD,EAAOsE,oBACNtD,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEf,EAAOsE,wBAIrDzD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,uBAAwB,4BAE7Be,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAASyE,qBAChBhD,SAAWC,GAAMzB,EAAkB,uBAAwByB,EAAEC,OAAOrB,OACpEkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,kCAAmC,0DAKxDY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,oBAAqB,yBAE1Be,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAAS0E,kBAChBjD,SAAWC,GAAMzB,EAAkB,oBAAqByB,EAAEC,OAAOrB,OACjEkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,+BAAgC,0DAInDY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,mBAAoB,wBAEzBe,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAAS2E,iBAChBlD,SAAWC,GAAMzB,EAAkB,mBAAoByB,EAAEC,OAAOrB,OAChEkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,8BAA+B,oDAMpDY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sDACZb,EAAE,oBAAqB,0BAG1BY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,iBAAkB,sBAEvBe,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDiD,EAAsB/C,IAAKC,IAC1BL,EAAAA,EAAAA,MAAA,SAEEC,UAAWL,EAAgBS,EAAOZ,MAAOR,EAAS4E,iBAAmBxD,EAAOd,OAAOW,SAAA,EAEnFC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLC,KAAK,iBACLjB,MAAOc,EAAOd,MACdkB,QAASxB,EAAS4E,iBAAmBxD,EAAOd,MAC5CmB,SAAWC,GAAMzB,EAAkB,iBAAkByB,EAAEC,OAAOrB,OAC9DU,UAAU,aAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAEG,EAAOb,UAXzCa,EAAOd,cAkBpBS,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,yBAA0B,+BAE/Be,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,SACnDkD,EAAkBhD,IAAKC,IACtBL,EAAAA,EAAAA,MAAA,SAEEC,UAAWL,EAAgBS,EAAOZ,MAAOR,EAAS6E,yBAA2BzD,EAAOd,OAAOW,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,SACEI,KAAK,QACLC,KAAK,yBACLjB,MAAOc,EAAOd,MACdkB,QAASxB,EAAS6E,yBAA2BzD,EAAOd,MACpDmB,SAAWC,GAAMzB,EAAkB,yBAA0ByB,EAAEC,OAAOrB,OACtEU,UAAU,aAEZE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,sBAAqBC,SAAEG,EAAOb,UAXzCa,EAAOd,cAkBpBS,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,sBAAuB,iCAE5Be,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACLQ,IAAI,IACJC,IAAI,IACJzB,MAAON,EAAS8E,oBAChBrD,SAAWC,GAAMzB,EAAkB,sBAAuByB,EAAEC,OAAOrB,OACnEU,UAAU,kKACVmB,YAAY,YAIhBpB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,4BAA6B,2BAElCe,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACLQ,IAAI,IACJC,IAAI,MACJzB,MAAON,EAAS+E,gBAChBtD,SAAWC,GAAMzB,EAAkB,kBAAmByB,EAAEC,OAAOrB,OAC/DU,UAAU,kKACVmB,YAAY,uBAQtBpB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,kBAAmB,wBAExBe,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAASgF,gBAChBvD,SAAWC,GAAMzB,EAAkB,kBAAmByB,EAAEC,OAAOrB,OAC/DkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,6BAA8B,+DAKjDY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,2BAA4B,4CAEjCe,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAASiF,yBAChBxD,SAAWC,GAAMzB,EAAkB,2BAA4ByB,EAAEC,OAAOrB,OACxEkD,KAAM,EACNxC,UAAU,sKACVmB,YAAahC,EAAE,mCAAoC,oE,cC7Q/D,MAoTA,EApT4BJ,IAKrB,IALsB,WAC3BmF,EAAa,GAAE,kBACfC,EAAiB,kBACjBC,EAAiB,SACjBC,GAAW,GACZtF,EACC,MAAM,EAAEI,EAAC,MAAEmF,IAAUlF,EAAAA,EAAAA,MACdmF,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,MAGrCC,EAAY,CAChB,CAAEpF,MAAO,QAASC,MAAO,QAASoF,QAAS,sBAC3C,CAAErF,MAAO,OAAQC,MAAO,OAAQoF,QAAS,4BACzC,CAAErF,MAAO,UAAWC,MAAO,UAAWoF,QAAS,4BAC/C,CAAErF,MAAO,YAAaC,MAAO,YAAaoF,QAAS,4BACnD,CAAErF,MAAO,WAAYC,MAAO,WAAYoF,QAAS,4BACjD,CAAErF,MAAO,WAAYC,MAAO,WAAYoF,QAAS,kCACjD,CAAErF,MAAO,SAAUC,MAAO,SAAUoF,QAAS,4BAC7C,CAAErF,MAAO,WAAYC,MAAO,WAAYoF,QAAS,uBAG7CC,EAAgB,CACpB,CAAEtF,MAAO,WAAYC,MAAO,WAAYoF,QAAS,kCACjD,CAAErF,MAAO,eAAgBC,MAAO,eAAgBoF,QAAS,kCACzD,CAAErF,MAAO,aAAcC,MAAO,aAAcoF,QAAS,8CACrD,CAAErF,MAAO,OAAQC,MAAO,OAAQoF,QAAS,6BAGrCE,EAAe,CACnB,CAAEvF,MAAO,WAAYC,MAAO,WAAYoF,QAAS,wCACjD,CAAErF,MAAO,OAAQC,MAAO,OAAQoF,QAAS,wCACzC,CAAErF,MAAO,WAAYC,MAAO,WAAYoF,QAAS,kCACjD,CAAErF,MAAO,UAAWC,MAAO,UAAWoF,QAAS,kCAC/C,CAAErF,MAAO,SAAUC,MAAO,SAAUoF,QAAS,wCAC7C,CAAErF,MAAO,WAAYC,MAAO,oBAAqBoF,QAAS,8EAGtDG,EAAwBA,CAACC,EAAaC,EAAO1F,KACjD,GAAI+E,EAAU,OAEd,MAAMY,EAAoBf,EAAW/D,IAAI+E,GACvCA,EAAMC,WAAaJ,GAAWzC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACrB4C,GAAK,IAAE,CAACF,GAAQ1F,IACrB4F,GAEW,OAAjBf,QAAiB,IAAjBA,GAAAA,EAAoBc,IAgBhBG,EAAoBC,IAAW,IAADC,EAAAC,EAClC,MAAMC,EAAS,CACb,EAAG,CAAEC,GAAI,OAAQC,GAAI,4BACrB,EAAG,CAAED,GAAI,gBAAiBC,GAAI,8EAC9B,EAAG,CAAED,GAAI,WAAYC,GAAI,kCACzB,EAAG,CAAED,GAAI,kBAAmBC,GAAI,8EAChC,EAAG,CAAED,GAAI,SAAUC,GAAI,4BACvB,EAAG,CAAED,GAAI,cAAeC,GAAI,qDAC5B,EAAG,CAAED,GAAI,UAAWC,GAAI,4BACxB,EAAG,CAAED,GAAI,aAAcC,GAAI,qDAC3B,EAAG,CAAED,GAAI,UAAWC,GAAI,4BACxB,GAAI,CAAED,GAAI,iBAAkBC,GAAI,uGAElC,OAAOpB,EAAqB,QAAhBgB,EAAGE,EAAOH,UAAM,IAAAC,OAAA,EAAbA,EAAeI,GAAkB,QAAhBH,EAAGC,EAAOH,UAAM,IAAAE,OAAA,EAAbA,EAAeE,IAGpD,OAA0B,IAAtBvB,EAAWzB,QAEX1C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,2DAA0DC,SACrEd,EAAE,sBAAuB,8BAE5BY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oBAAoB2F,KAAK,OAAOC,QAAQ,YAAYC,OAAO,eAAc5F,UACtFC,EAAAA,EAAAA,KAAA,QAAM4F,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,8HAGzE/F,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAC5Cd,EAAE,uBAAwB,kFAQnCY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mFAAkFC,SAAA,EAC/FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oDAAmDC,SAAA,EAChEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEd,EAAE,sBAAuB,8BAE5Be,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gDAA+CC,SACzDd,EAAE,uBAAwB,mDAI/Be,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kBAAiBC,UAC9BF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQC,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,8BAA6BC,UAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9Gd,EAAE,SAAU,aAEfe,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9Gd,EAAE,YAAa,iBAElBe,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9Gd,EAAE,WAAY,gBAEjBe,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9Gd,EAAE,YAAa,gBAElBe,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9Gd,EAAE,WAAY,eAEjBe,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9Gd,EAAE,QAAS,YAEZkF,IACAnE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,oGAAmGC,SAC9Gd,EAAE,UAAW,mBAKtBe,EAAAA,EAAAA,KAAA,SAAOF,UAAU,0EAAyEC,SACvFiE,EAAW/D,IAAI,CAAC+F,EAAW7D,KAAK,IAAA8D,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EArFhBnB,EAqFgB,OAC/BtF,EAAAA,EAAAA,MAAA,MAA6BC,UAAU,0CAAyCC,SAAA,EAE9EC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OACEF,UAAU,mDACVyG,MAAO,CAAEC,iBA5FFrB,EA4FqCa,EAAUlF,UA3FzD,CACb,EAAG,UAAW,EAAG,UAAW,EAAG,UAAW,EAAG,UAAW,EAAG,UAC3D,EAAG,UAAW,EAAG,UAAW,EAAG,UAAW,EAAG,UAAW,GAAI,WAEhDqE,IAAU,eAyFRtF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DqE,EAAQ4B,EAAUS,aAAeT,EAAUU,cAE9C7G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CAAC,IACtDiG,EAAUf,qBAOpBjF,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,uDAAsDC,SAAA,CACnEiG,EAAUlF,UAAU,UAEvBd,EAAAA,EAAAA,KAAA,QAAMF,UAAU,2CAA0CC,SACvDmF,EAAiBc,EAAUlF,mBAMlCd,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,SACxCoE,GACCnE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDiG,EAAUW,SACRvC,EACoD,QAD/C6B,EACJzB,EAAUoC,KAAK3H,GAAKA,EAAEG,QAAU4G,EAAUW,iBAAS,IAAAV,OAAA,EAAnDA,EAAqDxB,QACF,QADSyB,EAC5D1B,EAAUoC,KAAK3H,GAAKA,EAAEG,QAAU4G,EAAUW,iBAAS,IAAAT,OAAA,EAAnDA,EAAqD7G,MACnD,OAIRQ,EAAAA,EAAAA,MAAA,UACET,MAAO4G,EAAUW,UAAY,GAC7BpG,SAAWC,GAAMoE,EAAsBoB,EAAUf,SAAU,WAAYzE,EAAEC,OAAOrB,OAChFU,UAAU,gIAA+HC,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,GAAEW,SAAEd,EAAE,SAAU,eAC7BuF,EAAUvE,IAAIG,IACbJ,EAAAA,EAAAA,KAAA,UAAyBZ,MAAOgB,EAAKhB,MAAMW,SACxCqE,EAAQhE,EAAKqE,QAAUrE,EAAKf,OADlBe,EAAKhB,cAS1BY,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,SACxCoE,GACCnE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDiG,EAAUa,UACRzC,EACyD,QADpD+B,EACJzB,EAAckC,KAAKE,GAAKA,EAAE1H,QAAU4G,EAAUa,kBAAU,IAAAV,OAAA,EAAxDA,EAA0D1B,QACF,QADS2B,EACjE1B,EAAckC,KAAKE,GAAKA,EAAE1H,QAAU4G,EAAUa,kBAAU,IAAAT,OAAA,EAAxDA,EAA0D/G,MACxD,OAIRQ,EAAAA,EAAAA,MAAA,UACET,MAAO4G,EAAUa,WAAa,GAC9BtG,SAAWC,GAAMoE,EAAsBoB,EAAUf,SAAU,YAAazE,EAAEC,OAAOrB,OACjFU,UAAU,gIAA+HC,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,GAAEW,SAAEd,EAAE,SAAU,eAC7ByF,EAAczE,IAAI8G,IACjB/G,EAAAA,EAAAA,KAAA,UAAyBZ,MAAO2H,EAAK3H,MAAMW,SACxCqE,EAAQ2C,EAAKtC,QAAUsC,EAAK1H,OADlB0H,EAAK3H,cAS1BY,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,SACxCoE,GACCnE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDiG,EAAUgB,SACR5C,EACuD,QADlDiC,EACJ1B,EAAaiC,KAAK3H,GAAKA,EAAEG,QAAU4G,EAAUgB,iBAAS,IAAAX,OAAA,EAAtDA,EAAwD5B,QACF,QADS6B,EAC/D3B,EAAaiC,KAAK3H,GAAKA,EAAEG,QAAU4G,EAAUgB,iBAAS,IAAAV,OAAA,EAAtDA,EAAwDjH,MACtD,OAIRQ,EAAAA,EAAAA,MAAA,UACET,MAAO4G,EAAUgB,UAAY,GAC7BzG,SAAWC,GAAMoE,EAAsBoB,EAAUf,SAAU,WAAYzE,EAAEC,OAAOrB,OAChFU,UAAU,gIAA+HC,SAAA,EAEzIC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,GAAEW,SAAEd,EAAE,SAAU,eAC7B0F,EAAa1E,IAAIgH,IAChBjH,EAAAA,EAAAA,KAAA,UAA4BZ,MAAO6H,EAAQ7H,MAAMW,SAC9CqE,EAAQ6C,EAAQxC,QAAUwC,EAAQ5H,OADxB4H,EAAQ7H,cAS7BY,EAAAA,EAAAA,KAAA,MAAIF,UAAU,YAAWC,SACtBoE,GACCnE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,wCAAuCC,SACpDiG,EAAUkB,OAAS,OAGtBlH,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAO4G,EAAUkB,OAAS,GAC1B3G,SAAWC,GAAMoE,EAAsBoB,EAAUf,SAAU,QAASzE,EAAEC,OAAOrB,OAC7E6B,YAAahC,EAAE,WAAY,gBAC3Ba,UAAU,4IAMdqE,IACAnE,EAAAA,EAAAA,KAAA,MAAIF,UAAU,8BAA6BC,UACzCC,EAAAA,EAAAA,KAAA,UACEiC,QAASA,KAAMkF,OAhOJtC,EAgO0BmB,EAAUf,cA/N7Dd,GACa,OAAjBD,QAAiB,IAAjBA,GAAAA,EAAoBW,IAFSA,OAiOX/E,UAAU,4EACVsH,MAAOnI,EAAE,kBAAmB,qBAAqBc,UAEjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,UAAU2F,KAAK,OAAOE,OAAO,eAAeD,QAAQ,YAAW3F,UAC5EC,EAAAA,EAAAA,KAAA,QAAM4F,cAAc,QAAQC,eAAe,QAAQC,YAAa,EAAGC,EAAE,yIA1ItEC,EAAUf,oBAsJ3BjF,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gFAA+EC,UAC5FF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4CAA2CC,SAAA,EACxDF,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAC/Cd,EAAE,kBAAmB,qBAAqB,MAAEe,EAAAA,EAAAA,KAAA,UAAAD,SAASiE,EAAWzB,aAEnE1C,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAC/Cd,EAAE,mBAAoB,sBAAsB,MAAEY,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAC5CiE,EAAWzB,OAAS,GAClByB,EAAWqD,OAAO,CAACC,EAAKC,IAAMD,EAAMC,EAAEzG,UAAW,GAAKkD,EAAWzB,QAAQiF,QAAQ,GAClF,IACD,qBC+Xf,EAlqB0B3I,IAQnB,IARoB,UACzB4I,EAAS,YACTC,EAAW,YACXC,EAAW,mBACXC,EAAkB,YAClBC,EAAW,OACXC,EAAM,SACNC,GACDlJ,EACC,MAAM,EAAEI,IAAMC,EAAAA,EAAAA,MACR,KAAE8I,IAASC,EAAAA,EAAAA,KACXC,GAAWC,EAAAA,EAAAA,OACTV,UAAWW,EAAcV,YAAaW,EAAc,WAAEC,IAAeC,EAAAA,EAAAA,MACtEC,EAASC,IAAclE,EAAAA,EAAAA,WAAS,IAChCvF,EAAQ0J,IAAanE,EAAAA,EAAAA,UAAS,CAAC,IAC/BoE,EAASC,IAAcrE,EAAAA,EAAAA,UAAS,OAChCsE,EAAWC,IAAgBvE,EAAAA,EAAAA,UAAS,MAGrCwE,EAAkBtB,GAAaW,EAC/BY,EAAoBtB,GAAeW,GAGlCvJ,EAAUmK,IAAe1E,EAAAA,EAAAA,UAAS,CAEvC2E,aAAa,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GACjDC,aAAa,IAAIH,MAAOI,eAAeC,MAAM,EAAG,GAChDC,gBAAiB,GACjBC,YAAa,aAGbC,eAAmB,OAAJ3B,QAAI,IAAJA,OAAI,EAAJA,EAAM3H,OAAQ,GAC7BuJ,gBAAoB,OAAJ5B,QAAI,IAAJA,OAAI,EAAJA,EAAMZ,QAAS,GAC/ByC,kBAAsB,OAAJ7B,QAAI,IAAJA,OAAI,EAAJA,EAAM8B,UAAW,GACnCC,qBAAyB,OAAJ/B,QAAI,IAAJA,OAAI,EAAJA,EAAMgC,aAAc,GAGzC7J,kBAAmB,UACnBO,YAAa,cACbC,mBAAoB,SAGpBuB,oBAAqB,GACrB+H,mBAAoB,GACpBrH,cAAe,GACfE,oBAAqB,GAGrBhC,UAAW,CACTC,OAAQ,GACRG,MAAO,GACPgJ,MAAO,QAETlG,WAAY,GACZmG,oBAAqB,GACrBjH,gBAAiB,GACjBkH,qBAAsB,GAGtBC,WAAY,CACVC,cAAe,GACfC,UAAW,GACXC,gBAAiB,GACjBC,YAAa,GACbC,iBAAkB,IAIpBpH,kBAAmB,GACnBC,qBAAsB,GACtBC,kBAAmB,GACnBC,iBAAkB,GAGlBK,gBAAiB,GACjBH,uBAAwB,GACxBI,yBAA0B,GAG1BL,eAAgB,YAChBE,oBAAqB,GACrBC,gBAAiB,GAGjB8G,kBAAkB,EAClBC,aAAc,GACdC,eAAgB,GAGhBC,iBAAiB,EACjBC,oBAAqB,GACrBC,4BAA4B,EAC5BC,sBAAsB,KAIxBC,EAAAA,EAAAA,WAAU,KACR,GAAItD,GAAsBD,EAAa,CAAC,IAADwD,EAAAC,EAErCxC,EAAW,CACTyC,GAAI1D,EAAY2D,KAAO3D,EAAY0D,GACnChL,KAAMsH,EAAYtH,MAAI,GAAAT,OAAO+H,EAAY4D,UAAS,KAAA3L,OAAI+H,EAAY6D,UAClEC,OAAQ9D,EAAY8D,QAAM,GAAA7L,OAAO+H,EAAY4D,UAAS,KAAA3L,OAAI+H,EAAY6D,UACtEE,SAAU/D,EAAY+D,UAAY/D,EAAY2D,IAC9CK,IAAKhE,EAAYgE,IACjBC,WAAqC,QAA1BT,EAAAxD,EAAYkE,sBAAc,IAAAV,OAAA,EAA1BA,EAA4BW,mBAAoBnE,EAAYiE,WAAa,kBAItF,MAAMG,EAAgB,CACpBV,GAAIrC,EACJ5I,KAAM,mBACN4L,UAAW,aACXC,WAAkC,QAAvBb,EAAAzD,EAAYuE,mBAAW,IAAAd,OAAA,EAAvBA,EAAyBe,mBAAoB,oBACxDC,aAAc,CACZ,mCACA,oCACA,gCAIJtD,EAAaiD,GAGTlE,GACFoB,EAAYoD,IAAQjK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfiK,GACAxE,GAAW,IACd3E,gBAAiB6I,EAAcK,aAAanM,IAAIkD,IAAI,CAClDA,KAAMA,EACNE,SAAU,GACVD,UAAU,QAKhBqF,GAAW,EACb,MAAWM,IACTN,GAAW,GAEX6D,WAAW,KACT,MASMP,EAAgB,CACpBV,GAAIrC,EACJ5I,KAAM,mBACN4L,UAAW,aACXC,UAAW,kBACXG,aAAc,CACZ,mCACA,oCACA,gCAIJxD,EArBoB,CAClByC,GAAItC,EACJ1I,KAAM,uEACNoL,OAAQ,qBACRC,SAAU,cACVC,IAAK,EACLC,UAAW,mBAgBb9C,EAAaiD,GAGb9C,EAAYoD,IAAQjK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfiK,GAAQ,IACXnJ,gBAAiB6I,EAAcK,aAAanM,IAAIkD,IAAI,CAClDA,KAAMA,EACNE,SAAU,GACVD,UAAU,QAIdqF,GAAW,IACV,OAEJ,CAACM,EAAiBC,EAAmBpB,EAAoBD,EAAaE,IAEzE,MAAM9I,EAAoBA,CAAC+F,EAAO1F,KAChC,MAAMmN,GAAOnK,EAAAA,EAAAA,GAAA,GAAQtD,GAGrB,GAAIgG,EAAM0H,SAAS,KAAM,CACvB,MAAMC,EAAQ3H,EAAMuE,MAAM,KAC1B,IAAIqD,EAAUH,EACd,IAAK,IAAII,EAAI,EAAGA,EAAIF,EAAMlK,OAAS,EAAGoK,IAC/BD,EAAQD,EAAME,MAAKD,EAAQD,EAAME,IAAM,CAAC,GAC7CD,EAAUA,EAAQD,EAAME,IAE1BD,EAAQD,EAAMA,EAAMlK,OAAS,IAAMnD,CACrC,MACEmN,EAAQzH,GAAS1F,EAGnB6J,EAAYsD,GAGRvN,EAAO8F,IACT4D,EAAUkE,IAAIxK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUwK,GAAI,IAAE,CAAC9H,GAAQ,SAIrCtD,EAAoBA,CAACsD,EAAO3C,EAAO/C,KACvC,MAAMmN,GAAOnK,EAAAA,EAAAA,GAAA,GAAQtD,GACf2N,EAAQ3H,EAAMuE,MAAM,KAC1B,IAAIqD,EAAUH,EAEd,IAAK,IAAII,EAAI,EAAGA,EAAIF,EAAMlK,OAAS,EAAGoK,IACpCD,EAAUA,EAAQD,EAAME,IAG1BD,EAAQD,EAAMA,EAAMlK,OAAS,IAAIJ,GAAS/C,EAC1C6J,EAAYsD,IAiBRM,EAA0B7I,IAC9BiF,EAAY2D,IAAIxK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXwK,GAAI,IACP5I,WAAYA,MA4FhB,OAAIwE,IAAYG,GAEZ3I,EAAAA,EAAAA,KAAA,OAAKF,UAAU,wCAAuCC,UACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,sEAMnBE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,kDAAiDC,UAC9DF,EAAAA,EAAAA,MAAA,QAAMiN,SA1DWC,UAGnB,GAFAvM,EAAEwM,iBAjCiBC,MACnB,MAAMC,EAAY,CAAC,EA4BnB,OAzBKpO,EAASoK,cACZgE,EAAUhE,YAAcjK,EAAE,sBAAuB,6BAE9CH,EAASwK,cACZ4D,EAAU5D,YAAcrK,EAAE,sBAAuB,6BAE9CH,EAAS2K,kBACZyD,EAAUzD,gBAAkBxK,EAAE,0BAA2B,iCAEtDH,EAAS6K,cAAcwD,SAC1BD,EAAUvD,cAAgB1K,EAAE,wBAAyB,+BAElDH,EAASwE,kBAAkB6J,SAC9BD,EAAU5J,kBAAoBrE,EAAE,4BAA6B,oCAI3DH,EAASgC,UAAUC,SAAWjC,EAASgC,UAAUC,OAAS,GAAKjC,EAASgC,UAAUC,OAAS,MAC7FmM,EAAUlM,WAAa/B,EAAE,mBAAoB,oCAE3CH,EAASgC,UAAUI,QAAUpC,EAASgC,UAAUI,MAAQ,GAAKpC,EAASgC,UAAUI,MAAQ,MAC1FgM,EAAU/L,UAAYlC,EAAE,mBAAoB,oCAG9CyJ,EAAUwE,GAC+B,IAAlCE,OAAOC,KAAKH,GAAW3K,QAMzB0K,GAAL,CAKAxE,GAAW,GAEX,UAEQ,IAAI6E,QAAQC,GAAWjB,WAAWiB,EAAS,MAEjD,MAAMC,GAAYpL,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACbtD,GAAQ,IACXuM,GAAIlC,KAAKsE,MACThG,UAAWsB,EACXrB,YAAasB,EACb0E,WAAW,IAAIvE,MAAOC,cACtBuE,WAAW,IAAIxE,MAAOC,cACtBwE,WAAe,OAAJ5F,QAAI,IAAJA,OAAI,EAAJA,EAAMqD,KAAM,iBAGzB,GAAIvD,EACFA,EAAO0F,OACF,CAEL,MAAMK,EAAgBC,KAAKC,MAAMC,aAAaC,QAAQ,sBAAwB,MAC9EJ,EAAcK,KAAKV,GACnBQ,aAAaG,QAAQ,oBAAqBL,KAAKM,UAAUP,IAEzDQ,EAAAA,GAAMC,QAAQrP,EAAE,oBAAqB,2CAGnCiJ,EADEa,EACO,aAADnJ,OAAcmJ,GAEb,YAEb,CACF,CAAE,MAAOwF,GACPC,QAAQD,MAAM,8BAA+BA,GAC7CF,EAAAA,GAAME,MAAMtP,EAAE,sBAAuB,8BACvC,CAAC,QACCwJ,GAAW,EACb,CAvCA,MAFE4F,EAAAA,GAAME,MAAMtP,EAAE,kBAAmB,6CAsDHa,UAAU,YAAWC,SAAA,EAEjDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCC,SAAA,EAC/CF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,mDAAkDC,SAAA,CAC7Dd,EAAE,oBAAqB,uBACvB8J,GAAmBJ,IAClB9I,EAAAA,EAAAA,MAAA,QAAMC,UAAU,4DAA2DC,SAAA,CAAC,KACvE4I,EAAQ8C,QAAU9C,EAAQtI,YAInCL,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDd,EAAE,0BAA2B,0DAI/B0J,GAAWE,IACVhJ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,kDACbD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAC/Cd,EAAE,UAAW,WAAW,KAAG0J,EAAQ8C,OAAO,KAAG9C,EAAQ+C,SAAS,WAGnE7L,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACbD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAC/Cd,EAAE,YAAa,aAAa,KAAG4J,EAAUzI,YAG9CP,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACbD,EAAAA,EAAAA,MAAA,QAAMC,UAAU,mCAAkCC,SAAA,CAC/Cd,EAAE,YAAa,aAAa,KAAGH,EAAS6K,2BAOnD3J,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACL6B,QAAS8F,GAAQ,KAAWG,EAASa,EAAe,aAAAnJ,OAAgBmJ,GAAoB,cACxFjJ,UAAU,uFAAsFC,SAE/Fd,EAAE,SAAU,oBAOrBY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,uDACZb,EAAE,qBAAsB,2BAE3BY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/Ed,EAAE,cAAe,gBAAgB,KAACe,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAON,EAASoK,YAChB3I,SAAWC,GAAMzB,EAAkB,cAAeyB,EAAEC,OAAOrB,OAC3DU,UAAS,mJAAAF,OACPZ,EAAOkK,YAAc,iBAAmB,qBAG3ClK,EAAOkK,cACNlJ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEf,EAAOkK,kBAGrDrJ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/Ed,EAAE,cAAe,gBAAgB,KAACe,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAON,EAASwK,YAChB/I,SAAWC,GAAMzB,EAAkB,cAAeyB,EAAEC,OAAOrB,OAC3DU,UAAS,mJAAAF,OACPZ,EAAOsK,YAAc,iBAAmB,qBAG3CtK,EAAOsK,cACNtJ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEf,EAAOsK,kBAGrDzJ,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/Ed,EAAE,WAAY,sBAAsB,KAACe,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEvEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,SACLQ,IAAI,KACJC,IAAI,MACJzB,MAAON,EAAS2K,gBAChBlJ,SAAWC,GAAMzB,EAAkB,kBAAmByB,EAAEC,OAAOrB,OAC/DU,UAAS,mJAAAF,OACPZ,EAAOyK,gBAAkB,iBAAmB,mBAE9CxI,YAAY,OAEbjC,EAAOyK,kBACNzJ,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEf,EAAOyK,sBAGrD5J,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,cAAe,mBAEpBY,EAAAA,EAAAA,MAAA,UACET,MAAON,EAAS4K,YAChBnJ,SAAWC,GAAMzB,EAAkB,cAAeyB,EAAEC,OAAOrB,OAC3DU,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,aAAYW,SAAEd,EAAE,aAAc,iBAC5Ce,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,QAAOW,SAAEd,EAAE,QAAS,YAClCe,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,aAAYW,SAAEd,EAAE,YAAa,6BAOnDY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,4FAA2FC,SAAA,EACxGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8DAA6DC,SAAA,EACzEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACZb,EAAE,uBAAwB,6BAE7BY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/Ed,EAAE,gBAAiB,kBAAkB,KAACe,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAExEC,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAON,EAAS6K,cAChBpJ,SAAWC,GAAMzB,EAAkB,gBAAiByB,EAAEC,OAAOrB,OAC7DU,UAAS,mJAAAF,OACPZ,EAAO2K,cAAgB,iBAAmB,mBAE5C1I,YAAahC,EAAE,qBAAsB,0BAEtCD,EAAO2K,gBACN3J,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEf,EAAO2K,oBAGrD9J,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,QAAS,qBAEde,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAON,EAAS8K,eAChBrJ,SAAWC,GAAMzB,EAAkB,iBAAkByB,EAAEC,OAAOrB,OAC9DU,UAAU,kKACVmB,YAAahC,EAAE,aAAc,kCAGjCY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,cAAe,mBAEpBe,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAON,EAAS+K,iBAChBtJ,SAAWC,GAAMzB,EAAkB,mBAAoByB,EAAEC,OAAOrB,OAChEU,UAAU,kKACVmB,YAAahC,EAAE,mBAAoB,4BAGvCY,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/Ed,EAAE,aAAc,iBAEnBe,EAAAA,EAAAA,KAAA,SACEI,KAAK,OACLhB,MAAON,EAASiL,oBAChBxJ,SAAWC,GAAMzB,EAAkB,sBAAuByB,EAAEC,OAAOrB,OACnEU,UAAU,kKACVmB,YAAahC,EAAE,kBAAmB,sCAO1Ce,EAAAA,EAAAA,KAACyO,EAAoB,CACnB3P,SAAUA,EACVC,kBAAmBA,EACnBC,OAAQA,KAIVgB,EAAAA,EAAAA,KAAC0O,EAA0B,CACzB5P,SAAUA,EACVC,kBAAmBA,EACnBuC,aAlUa,SAACwD,GAA8B,IAAvB6J,EAAYC,UAAArM,OAAA,QAAAsM,IAAAD,UAAA,GAAAA,UAAA,GAAG,GAC1C,MAAMrC,GAAOnK,EAAAA,EAAAA,GAAA,GAAQtD,GAChByN,EAAQzH,KAAQyH,EAAQzH,GAAS,IACtCyH,EAAQzH,GAAOoJ,KAAKS,GACpB1F,EAAYsD,EACd,EA8TQhL,gBA5TgBA,CAACuD,EAAO3C,KAC9B,MAAMoK,GAAOnK,EAAAA,EAAAA,GAAA,GAAQtD,GACrByN,EAAQzH,GAAOgK,OAAO3M,EAAO,GAC7B8G,EAAYsD,IA0TN/K,kBAAmBA,KAIrBxB,EAAAA,EAAAA,KAAC+O,EAAoB,CACnBjQ,SAAUA,EACVC,kBAAmBA,EACnByC,kBAAmBA,EACnBxC,OAAQA,KAIVa,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wFAAuFC,SAAA,EACpGF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,4DAA2DC,SAAA,EACvEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yDACZb,EAAE,iBAAkB,uBAIvBY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,gEAA+DC,SAC7Ed,EAAE,kBAAmB,kCAExBY,EAAAA,EAAAA,MAAA,UACET,MAAON,EAASgC,UAAUC,OAC1BR,SAAWC,GAAMzB,EAAkB,mBAAoByB,EAAEC,OAAOrB,OAChEU,UAAU,6JAA4JC,SAAA,EAEtKC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,GAAEW,SAAEd,EAAE,SAAU,eAC7B,IAAI+P,MAAM,KAAK/O,IAAI,CAACgP,EAAGtC,KACtB9M,EAAAA,EAAAA,MAAA,UAAgBT,MAAOuN,EAAE5M,SAAA,CACtB4M,EAAE,SAAa,IAANA,EAAU1N,EAAE,SAAU,WACzB0N,GAAK,EAAI1N,EAAE,WAAY,QACvB0N,GAAK,EAAI1N,EAAE,eAAgB,YAC3BA,EAAE,aAAc,YAJZ0N,WASnB9M,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,gEAA+DC,SAC7Ed,EAAE,iBAAkB,iCAEvBY,EAAAA,EAAAA,MAAA,UACET,MAAON,EAASgC,UAAUI,MAC1BX,SAAWC,GAAMzB,EAAkB,kBAAmByB,EAAEC,OAAOrB,OAC/DU,UAAU,6JAA4JC,SAAA,EAEtKC,EAAAA,EAAAA,KAAA,UAAQZ,MAAM,GAAEW,SAAEd,EAAE,SAAU,eAC7B,IAAI+P,MAAM,KAAK/O,IAAI,CAACgP,EAAGtC,KACtB9M,EAAAA,EAAAA,MAAA,UAAgBT,MAAOuN,EAAE5M,SAAA,CACtB4M,EAAE,SAAa,IAANA,EAAU1N,EAAE,SAAU,WACzB0N,GAAK,EAAI1N,EAAE,WAAY,QACvB0N,GAAK,EAAI1N,EAAE,eAAgB,YAC3BA,EAAE,aAAc,YAJZ0N,WASnB9M,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,gEAA+DC,SAC7Ed,EAAE,sBAAuB,4BAE5Be,EAAAA,EAAAA,KAAA,YACEZ,MAAON,EAASqL,oBAChB5J,SAAWC,GAAMzB,EAAkB,sBAAuByB,EAAEC,OAAOrB,OACnEkD,KAAK,IACLxC,UAAU,6JACVmB,YAAahC,EAAE,uBAAwB,uEAM7CY,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,0DAAyDC,SACpEd,EAAE,sBAAuB,4BAE5Be,EAAAA,EAAAA,KAACkP,EAAAA,EAAe,CACdC,mBAAoBrQ,EAASkF,WAC7BoL,kBAAmBvC,EACnB1I,UAAU,OAKbrF,EAASkF,WAAWzB,OAAS,IAC5BvC,EAAAA,EAAAA,KAAA,OAAAD,UACEC,EAAAA,EAAAA,KAACqP,EAAmB,CAClBrL,WAAYlF,EAASkF,WACrBC,kBAAmB4I,EACnB3I,kBA5Yee,IAC7B,MAAMF,EAAoBjG,EAASkF,WAAWsL,OAAOtK,GAASA,EAAMC,WAAaA,GACjFgE,EAAY2D,IAAIxK,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACXwK,GAAI,IACP5I,WAAYe,MAyYAZ,UAAU,aAQpBtE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACL6B,QAAS8F,GAAQ,KAAWG,EAASa,EAAe,aAAAnJ,OAAgBmJ,GAAoB,cACxFjJ,UAAU,+FAA8FC,SAEvGd,EAAE,SAAU,aAEfe,EAAAA,EAAAA,KAAA,UACEI,KAAK,SACLmP,SAAU/G,EACV1I,UAAU,kIAAiIC,SAE1IyI,GACC3I,EAAAA,EAAAA,MAAA2P,EAAAA,SAAA,CAAAzP,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZb,EAAE,SAAU,iBAGfY,EAAAA,EAAAA,MAAA2P,EAAAA,SAAA,CAAAzP,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZb,EAAE,mBAAoB,mC", "sources": ["components/DailyProgress/PatientStatusSection.jsx", "components/DailyProgress/TreatmentActivitiesSection.jsx", "components/DailyProgress/ProgressNotesSection.jsx", "components/BodyMap/PainAssessmentTable.jsx", "components/DailyProgress/DailyProgressForm.jsx"], "sourcesContent": ["import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst PatientStatusSection = ({ formData, handleInputChange, errors }) => {\n  const { t } = useLanguage();\n\n  const attendanceOptions = [\n    { value: 'present', label: t('present', 'Present'), color: 'green' },\n    { value: 'absent', label: t('absent', 'Absent'), color: 'red' },\n    { value: 'late', label: t('late', 'Late'), color: 'yellow' },\n    { value: 'early_departure', label: t('earlyDeparture', 'Early Departure'), color: 'orange' }\n  ];\n\n  const moodOptions = [\n    { value: 'cooperative', label: t('cooperative', 'Cooperative'), color: 'green' },\n    { value: 'anxious', label: t('anxious', 'Anxious'), color: 'yellow' },\n    { value: 'agitated', label: t('agitated', 'Agitated'), color: 'red' },\n    { value: 'withdrawn', label: t('withdrawn', 'Withdrawn'), color: 'gray' },\n    { value: 'cheerful', label: t('cheerful', 'Cheerful'), color: 'blue' }\n  ];\n\n  const energyLevelOptions = [\n    { value: 'high', label: t('high', 'High'), color: 'green' },\n    { value: 'normal', label: t('normal', 'Normal'), color: 'blue' },\n    { value: 'low', label: t('low', 'Low'), color: 'yellow' },\n    { value: 'fatigued', label: t('fatigued', 'Fatigued'), color: 'red' }\n  ];\n\n  const getColorClasses = (color, isSelected) => {\n    const baseClasses = 'px-3 py-2 rounded-lg border transition-colors cursor-pointer';\n    \n    if (isSelected) {\n      switch (color) {\n        case 'green':\n          return `${baseClasses} bg-green-100 border-green-500 text-green-800 dark:bg-green-900/30 dark:border-green-400 dark:text-green-200`;\n        case 'red':\n          return `${baseClasses} bg-red-100 border-red-500 text-red-800 dark:bg-red-900/30 dark:border-red-400 dark:text-red-200`;\n        case 'yellow':\n          return `${baseClasses} bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/30 dark:border-yellow-400 dark:text-yellow-200`;\n        case 'orange':\n          return `${baseClasses} bg-orange-100 border-orange-500 text-orange-800 dark:bg-orange-900/30 dark:border-orange-400 dark:text-orange-200`;\n        case 'blue':\n          return `${baseClasses} bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/30 dark:border-blue-400 dark:text-blue-200`;\n        case 'gray':\n          return `${baseClasses} bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/30 dark:border-gray-400 dark:text-gray-200`;\n        default:\n          return `${baseClasses} bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/30 dark:border-gray-400 dark:text-gray-200`;\n      }\n    } else {\n      return `${baseClasses} bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600`;\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-user-check text-green-600 dark:text-green-400 mr-2\"></i>\n        {t('patientStatus', 'Patient Status & Presentation')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Patient Attendance */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('patientAttendance', 'Patient Attendance')}\n          </label>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n            {attendanceOptions.map((option) => (\n              <label\n                key={option.value}\n                className={getColorClasses(option.color, formData.patientAttendance === option.value)}\n              >\n                <input\n                  type=\"radio\"\n                  name=\"patientAttendance\"\n                  value={option.value}\n                  checked={formData.patientAttendance === option.value}\n                  onChange={(e) => handleInputChange('patientAttendance', e.target.value)}\n                  className=\"sr-only\"\n                />\n                <div className=\"flex items-center justify-center\">\n                  <span className=\"text-sm font-medium\">{option.label}</span>\n                </div>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* Patient Mood */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('patientMood', 'Patient Mood & Behavior')}\n          </label>\n          <div className=\"grid grid-cols-2 md:grid-cols-5 gap-3\">\n            {moodOptions.map((option) => (\n              <label\n                key={option.value}\n                className={getColorClasses(option.color, formData.patientMood === option.value)}\n              >\n                <input\n                  type=\"radio\"\n                  name=\"patientMood\"\n                  value={option.value}\n                  checked={formData.patientMood === option.value}\n                  onChange={(e) => handleInputChange('patientMood', e.target.value)}\n                  className=\"sr-only\"\n                />\n                <div className=\"flex items-center justify-center\">\n                  <span className=\"text-sm font-medium\">{option.label}</span>\n                </div>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* Energy Level */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">\n            {t('energyLevel', 'Energy Level')}\n          </label>\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n            {energyLevelOptions.map((option) => (\n              <label\n                key={option.value}\n                className={getColorClasses(option.color, formData.patientEnergyLevel === option.value)}\n              >\n                <input\n                  type=\"radio\"\n                  name=\"patientEnergyLevel\"\n                  value={option.value}\n                  checked={formData.patientEnergyLevel === option.value}\n                  onChange={(e) => handleInputChange('patientEnergyLevel', e.target.value)}\n                  className=\"sr-only\"\n                />\n                <div className=\"flex items-center justify-center\">\n                  <span className=\"text-sm font-medium\">{option.label}</span>\n                </div>\n              </label>\n            ))}\n          </div>\n        </div>\n\n        {/* Pain Assessment */}\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n          <h3 className=\"text-md font-semibold text-red-900 dark:text-red-100 mb-4\">\n            <i className=\"fas fa-exclamation-triangle text-red-600 dark:text-red-400 mr-2\"></i>\n            {t('painAssessment', 'Pain Assessment (0-10 Scale)')}\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-1\">\n                {t('painBefore', 'Pain Level Before Treatment')}\n              </label>\n              <input\n                type=\"number\"\n                min=\"0\"\n                max=\"10\"\n                value={formData.painLevel.before}\n                onChange={(e) => handleInputChange('painLevel.before', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white ${\n                  errors.painBefore ? 'border-red-500' : 'border-red-300'\n                }`}\n                placeholder=\"0-10\"\n              />\n              {errors.painBefore && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.painBefore}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-1\">\n                {t('painAfter', 'Pain Level After Treatment')}\n              </label>\n              <input\n                type=\"number\"\n                min=\"0\"\n                max=\"10\"\n                value={formData.painLevel.after}\n                onChange={(e) => handleInputChange('painLevel.after', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white ${\n                  errors.painAfter ? 'border-red-500' : 'border-red-300'\n                }`}\n                placeholder=\"0-10\"\n              />\n              {errors.painAfter && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.painAfter}</p>\n              )}\n            </div>\n          </div>\n          <div className=\"mt-3 text-xs text-red-700 dark:text-red-300\">\n            <p><strong>0:</strong> {t('noPain', 'No pain')} | <strong>1-3:</strong> {t('mildPain', 'Mild pain')} | <strong>4-6:</strong> {t('moderatePain', 'Moderate pain')} | <strong>7-10:</strong> {t('severePain', 'Severe pain')}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PatientStatusSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst TreatmentActivitiesSection = ({ formData, handleInputChange, addArrayItem, removeArrayItem, handleArrayChange }) => {\n  const { t } = useLanguage();\n\n  const commonActivities = [\n    'Range of Motion Exercises',\n    'Strengthening Exercises',\n    'Balance Training',\n    'Gait Training',\n    'Transfer Training',\n    'Functional Activities',\n    'Pain Management',\n    'Postural Training',\n    'Coordination Exercises',\n    'Endurance Training'\n  ];\n\n  const commonEquipment = [\n    'Parallel Bars',\n    'Exercise Mat',\n    'Therapy Ball',\n    'Resistance Bands',\n    'Weights',\n    'Balance Board',\n    'Walker',\n    'Wheelchair',\n    'TENS Unit',\n    'Ultrasound Machine'\n  ];\n\n  const addActivity = () => {\n    addArrayItem('activitiesPerformed', {\n      activity: '',\n      duration: '',\n      intensity: 'moderate',\n      patientResponse: '',\n      completed: true\n    });\n  };\n\n  const addExercise = () => {\n    addArrayItem('exercisesCompleted', {\n      exercise: '',\n      sets: '',\n      reps: '',\n      weight: '',\n      notes: ''\n    });\n  };\n\n  const addEquipment = () => {\n    addArrayItem('equipmentUsed', {\n      equipment: '',\n      duration: '',\n      settings: '',\n      effectiveness: 'effective'\n    });\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-dumbbell text-purple-600 dark:text-purple-400 mr-2\"></i>\n        {t('treatmentActivities', 'Treatment Activities & Interventions')}\n      </h2>\n      \n      <div className=\"space-y-8\">\n        {/* Activities Performed */}\n        <div>\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-md font-semibold text-gray-900 dark:text-white\">\n              {t('activitiesPerformed', 'Activities Performed')}\n            </h3>\n            <button\n              type=\"button\"\n              onClick={addActivity}\n              className=\"px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-1\"></i>\n              {t('addActivity', 'Add Activity')}\n            </button>\n          </div>\n          \n          {formData.activitiesPerformed?.map((activity, index) => (\n            <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                  {t('activity', 'Activity')} {index + 1}\n                </h4>\n                <button\n                  type=\"button\"\n                  onClick={() => removeArrayItem('activitiesPerformed', index)}\n                  className=\"text-red-600 hover:text-red-800 transition-colors\"\n                >\n                  <i className=\"fas fa-trash text-sm\"></i>\n                </button>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('activityName', 'Activity Name')}\n                  </label>\n                  <select\n                    value={activity.activity}\n                    onChange={(e) => handleArrayChange('activitiesPerformed', index, { ...activity, activity: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"\">{t('selectActivity', 'Select Activity')}</option>\n                    {commonActivities.map((act) => (\n                      <option key={act} value={act}>{act}</option>\n                    ))}\n                    <option value=\"other\">{t('other', 'Other')}</option>\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('duration', 'Duration (min)')}\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"1\"\n                    value={activity.duration}\n                    onChange={(e) => handleArrayChange('activitiesPerformed', index, { ...activity, duration: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder=\"15\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('intensity', 'Intensity')}\n                  </label>\n                  <select\n                    value={activity.intensity}\n                    onChange={(e) => handleArrayChange('activitiesPerformed', index, { ...activity, intensity: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"low\">{t('low', 'Low')}</option>\n                    <option value=\"moderate\">{t('moderate', 'Moderate')}</option>\n                    <option value=\"high\">{t('high', 'High')}</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('patientResponse', 'Patient Response')}\n                </label>\n                <textarea\n                  value={activity.patientResponse}\n                  onChange={(e) => handleArrayChange('activitiesPerformed', index, { ...activity, patientResponse: e.target.value })}\n                  rows={2}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('describePatientResponse', 'Describe patient response to activity')}\n                />\n              </div>\n            </div>\n          ))}\n          \n          {(!formData.activitiesPerformed || formData.activitiesPerformed.length === 0) && (\n            <div className=\"text-center py-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg\">\n              <i className=\"fas fa-dumbbell text-4xl text-gray-400 mb-2\"></i>\n              <p className=\"text-gray-500 dark:text-gray-400 mb-2\">\n                {t('noActivitiesAdded', 'No activities added yet')}\n              </p>\n              <button\n                type=\"button\"\n                onClick={addActivity}\n                className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n              >\n                <i className=\"fas fa-plus mr-2\"></i>\n                {t('addFirstActivity', 'Add First Activity')}\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Equipment Used */}\n        <div>\n          <div className=\"flex items-center justify-between mb-4\">\n            <h3 className=\"text-md font-semibold text-gray-900 dark:text-white\">\n              {t('equipmentUsed', 'Equipment Used')}\n            </h3>\n            <button\n              type=\"button\"\n              onClick={addEquipment}\n              className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n            >\n              <i className=\"fas fa-plus mr-1\"></i>\n              {t('addEquipment', 'Add Equipment')}\n            </button>\n          </div>\n          \n          {formData.equipmentUsed?.map((equipment, index) => (\n            <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4\">\n              <div className=\"flex items-center justify-between mb-3\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white\">\n                  {t('equipment', 'Equipment')} {index + 1}\n                </h4>\n                <button\n                  type=\"button\"\n                  onClick={() => removeArrayItem('equipmentUsed', index)}\n                  className=\"text-red-600 hover:text-red-800 transition-colors\"\n                >\n                  <i className=\"fas fa-trash text-sm\"></i>\n                </button>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('equipmentName', 'Equipment Name')}\n                  </label>\n                  <select\n                    value={equipment.equipment}\n                    onChange={(e) => handleArrayChange('equipmentUsed', index, { ...equipment, equipment: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"\">{t('selectEquipment', 'Select Equipment')}</option>\n                    {commonEquipment.map((eq) => (\n                      <option key={eq} value={eq}>{eq}</option>\n                    ))}\n                    <option value=\"other\">{t('other', 'Other')}</option>\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('duration', 'Duration (min)')}\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"1\"\n                    value={equipment.duration}\n                    onChange={(e) => handleArrayChange('equipmentUsed', index, { ...equipment, duration: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                    placeholder=\"10\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('effectiveness', 'Effectiveness')}\n                  </label>\n                  <select\n                    value={equipment.effectiveness}\n                    onChange={(e) => handleArrayChange('equipmentUsed', index, { ...equipment, effectiveness: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  >\n                    <option value=\"very_effective\">{t('veryEffective', 'Very Effective')}</option>\n                    <option value=\"effective\">{t('effective', 'Effective')}</option>\n                    <option value=\"somewhat_effective\">{t('somewhatEffective', 'Somewhat Effective')}</option>\n                    <option value=\"not_effective\">{t('notEffective', 'Not Effective')}</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div className=\"mt-4\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('settings', 'Settings/Notes')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={equipment.settings}\n                  onChange={(e) => handleArrayChange('equipmentUsed', index, { ...equipment, settings: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('equipmentSettings', 'Equipment settings, intensity, or special notes')}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Modifications Needed */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('modificationsNeeded', 'Modifications Needed')}\n          </label>\n          <textarea\n            value={formData.modificationsNeeded}\n            onChange={(e) => handleInputChange('modificationsNeeded', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('describeModifications', 'Describe any modifications made to activities or equipment')}\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TreatmentActivitiesSection;\n", "import React from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst ProgressNotesSection = ({ formData, handleInputChange, handleArrayChange, errors }) => {\n  const { t } = useLanguage();\n\n  const sessionQualityOptions = [\n    { value: 'excellent', label: t('excellent', 'Excellent'), color: 'green' },\n    { value: 'good', label: t('good', 'Good'), color: 'blue' },\n    { value: 'satisfactory', label: t('satisfactory', 'Satisfactory'), color: 'yellow' },\n    { value: 'needs_improvement', label: t('needsImprovement', 'Needs Improvement'), color: 'red' }\n  ];\n\n  const complianceOptions = [\n    { value: 'excellent', label: t('excellent', 'Excellent (90-100%)'), color: 'green' },\n    { value: 'good', label: t('good', 'Good (70-89%)'), color: 'blue' },\n    { value: 'fair', label: t('fair', 'Fair (50-69%)'), color: 'yellow' },\n    { value: 'poor', label: t('poor', 'Poor (<50%)'), color: 'red' },\n    { value: 'not_applicable', label: t('notApplicable', 'Not Applicable'), color: 'gray' }\n  ];\n\n  const getColorClasses = (color, isSelected) => {\n    const baseClasses = 'px-3 py-2 rounded-lg border transition-colors cursor-pointer text-center';\n    \n    if (isSelected) {\n      switch (color) {\n        case 'green':\n          return `${baseClasses} bg-green-100 border-green-500 text-green-800 dark:bg-green-900/30 dark:border-green-400 dark:text-green-200`;\n        case 'blue':\n          return `${baseClasses} bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/30 dark:border-blue-400 dark:text-blue-200`;\n        case 'yellow':\n          return `${baseClasses} bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/30 dark:border-yellow-400 dark:text-yellow-200`;\n        case 'red':\n          return `${baseClasses} bg-red-100 border-red-500 text-red-800 dark:bg-red-900/30 dark:border-red-400 dark:text-red-200`;\n        case 'gray':\n          return `${baseClasses} bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/30 dark:border-gray-400 dark:text-gray-200`;\n        default:\n          return `${baseClasses} bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/30 dark:border-gray-400 dark:text-gray-200`;\n      }\n    } else {\n      return `${baseClasses} bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600`;\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n      <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-6\">\n        <i className=\"fas fa-clipboard-list text-orange-600 dark:text-orange-400 mr-2\"></i>\n        {t('progressNotesAssessment', 'Progress Notes & Assessment')}\n      </h2>\n      \n      <div className=\"space-y-6\">\n        {/* Functional Goals Progress */}\n        <div>\n          <h3 className=\"text-md font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('functionalGoalsProgress', 'Functional Goals Progress')}\n          </h3>\n          {formData.functionalGoals?.map((goal, index) => (\n            <div key={index} className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4\">\n              <div className=\"mb-3\">\n                <h4 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('goal', 'Goal')} {index + 1}: {goal.goal}\n                </h4>\n                <div className=\"flex items-center space-x-4\">\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={goal.achieved}\n                      onChange={(e) => handleArrayChange('functionalGoals', index, { ...goal, achieved: e.target.checked })}\n                      className=\"mr-2\"\n                    />\n                    <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                      {t('goalAchieved', 'Goal Achieved')}\n                    </span>\n                  </label>\n                </div>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('progressDescription', 'Progress Description')}\n                </label>\n                <textarea\n                  value={goal.progress}\n                  onChange={(e) => handleArrayChange('functionalGoals', index, { ...goal, progress: e.target.value })}\n                  rows={2}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n                  placeholder={t('describeProgress', 'Describe progress towards this goal')}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Clinical Notes */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('objectiveFindings', 'Objective Findings')} <span className=\"text-red-500\">*</span>\n            </label>\n            <textarea\n              value={formData.objectiveFindings}\n              onChange={(e) => handleInputChange('objectiveFindings', e.target.value)}\n              rows={4}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.objectiveFindings ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('objectiveFindingsPlaceholder', 'Measurable observations, ROM, strength, balance, etc.')}\n            />\n            {errors.objectiveFindings && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.objectiveFindings}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('subjectiveComplaints', 'Subjective Complaints')}\n            </label>\n            <textarea\n              value={formData.subjectiveComplaints}\n              onChange={(e) => handleInputChange('subjectiveComplaints', e.target.value)}\n              rows={4}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('subjectiveComplaintsPlaceholder', 'Patient reported symptoms, concerns, feedback')}\n            />\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('treatmentResponse', 'Treatment Response')}\n            </label>\n            <textarea\n              value={formData.treatmentResponse}\n              onChange={(e) => handleInputChange('treatmentResponse', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('treatmentResponsePlaceholder', 'How patient responded to treatment interventions')}\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('adverseReactions', 'Adverse Reactions')}\n            </label>\n            <textarea\n              value={formData.adverseReactions}\n              onChange={(e) => handleInputChange('adverseReactions', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              placeholder={t('adverseReactionsPlaceholder', 'Any negative reactions or complications')}\n            />\n          </div>\n        </div>\n\n        {/* Quality Indicators */}\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n          <h3 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n            <i className=\"fas fa-star text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('qualityIndicators', 'Quality Indicators')}\n          </h3>\n          \n          <div className=\"space-y-4\">\n            {/* Session Quality */}\n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-3\">\n                {t('sessionQuality', 'Session Quality')}\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n                {sessionQualityOptions.map((option) => (\n                  <label\n                    key={option.value}\n                    className={getColorClasses(option.color, formData.sessionQuality === option.value)}\n                  >\n                    <input\n                      type=\"radio\"\n                      name=\"sessionQuality\"\n                      value={option.value}\n                      checked={formData.sessionQuality === option.value}\n                      onChange={(e) => handleInputChange('sessionQuality', e.target.value)}\n                      className=\"sr-only\"\n                    />\n                    <span className=\"text-sm font-medium\">{option.label}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {/* Home Exercise Compliance */}\n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-3\">\n                {t('homeExerciseCompliance', 'Home Exercise Compliance')}\n              </label>\n              <div className=\"grid grid-cols-2 md:grid-cols-5 gap-3\">\n                {complianceOptions.map((option) => (\n                  <label\n                    key={option.value}\n                    className={getColorClasses(option.color, formData.homeExerciseCompliance === option.value)}\n                  >\n                    <input\n                      type=\"radio\"\n                      name=\"homeExerciseCompliance\"\n                      value={option.value}\n                      checked={formData.homeExerciseCompliance === option.value}\n                      onChange={(e) => handleInputChange('homeExerciseCompliance', e.target.value)}\n                      className=\"sr-only\"\n                    />\n                    <span className=\"text-xs font-medium\">{option.label}</span>\n                  </label>\n                ))}\n              </div>\n            </div>\n\n            {/* Patient Satisfaction */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                  {t('patientSatisfaction', 'Patient Satisfaction (1-5)')}\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"1\"\n                  max=\"5\"\n                  value={formData.patientSatisfaction}\n                  onChange={(e) => handleInputChange('patientSatisfaction', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n                  placeholder=\"1-5\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                  {t('goalAchievementPercentage', 'Goal Achievement (%)')}\n                </label>\n                <input\n                  type=\"number\"\n                  min=\"0\"\n                  max=\"100\"\n                  value={formData.goalAchievement}\n                  onChange={(e) => handleInputChange('goalAchievement', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n                  placeholder=\"0-100\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Next Session Plan */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('nextSessionPlan', 'Next Session Plan')}\n          </label>\n          <textarea\n            value={formData.nextSessionPlan}\n            onChange={(e) => handleInputChange('nextSessionPlan', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('nextSessionPlanPlaceholder', 'Plan for next treatment session, modifications, goals')}\n          />\n        </div>\n\n        {/* Recommendations for Family */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            {t('recommendationsForFamily', 'Recommendations for Family/Caregivers')}\n          </label>\n          <textarea\n            value={formData.recommendationsForFamily}\n            onChange={(e) => handleInputChange('recommendationsForFamily', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('familyRecommendationsPlaceholder', 'Home exercises, precautions, activities to encourage')}\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProgressNotesSection;\n", "import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\n\nconst PainAssessmentTable = ({ \n  painPoints = [], \n  onPainPointUpdate, \n  onPainPointDelete,\n  readonly = false \n}) => {\n  const { t, isRTL } = useLanguage();\n  const [editingId, setEditingId] = useState(null);\n\n  // Pain characteristics options\n  const painTypes = [\n    { value: 'sharp', label: 'Sharp', labelAr: 'حاد' },\n    { value: 'dull', label: 'Dull', labelAr: 'كليل' },\n    { value: 'burning', label: 'Burning', labelAr: 'حارق' },\n    { value: 'throbbing', label: 'Throbbing', labelAr: 'نابض' },\n    { value: 'stabbing', label: 'Stabbing', labelAr: 'طاعن' },\n    { value: 'cramping', label: 'Cramping', labelAr: 'تشنجي' },\n    { value: 'aching', label: 'Aching', labelAr: 'مؤلم' },\n    { value: 'tingling', label: 'Tingling', labelAr: 'وخز' }\n  ];\n\n  const painFrequency = [\n    { value: 'constant', label: 'Constant', labelAr: 'مستمر' },\n    { value: 'intermittent', label: 'Intermittent', labelAr: 'متقطع' },\n    { value: 'occasional', label: 'Occasional', labelAr: 'أحياناً' },\n    { value: 'rare', label: 'Rare', labelAr: 'نادر' }\n  ];\n\n  const painTriggers = [\n    { value: 'movement', label: 'Movement', labelAr: 'الحركة' },\n    { value: 'rest', label: 'Rest', labelAr: 'الراحة' },\n    { value: 'pressure', label: 'Pressure', labelAr: 'الضغط' },\n    { value: 'weather', label: 'Weather', labelAr: 'الطقس' },\n    { value: 'stress', label: 'Stress', labelAr: 'التوتر' },\n    { value: 'activity', label: 'Physical Activity', labelAr: 'النشاط البدني' }\n  ];\n\n  const handlePainPointUpdate = (painPointId, field, value) => {\n    if (readonly) return;\n    \n    const updatedPainPoints = painPoints.map(point => \n      point.regionId === painPointId \n        ? { ...point, [field]: value }\n        : point\n    );\n    onPainPointUpdate?.(updatedPainPoints);\n  };\n\n  const handleDeletePainPoint = (painPointId) => {\n    if (readonly) return;\n    onPainPointDelete?.(painPointId);\n  };\n\n  const getPainLevelColor = (level) => {\n    const colors = {\n      1: '#FEF3C7', 2: '#FDE68A', 3: '#FCD34D', 4: '#FBBF24', 5: '#F59E0B',\n      6: '#D97706', 7: '#B45309', 8: '#92400E', 9: '#78350F', 10: '#451A03'\n    };\n    return colors[level] || '#E5E7EB';\n  };\n\n  const getPainLevelText = (level) => {\n    const levels = {\n      1: { en: 'Mild', ar: 'خفيف' },\n      2: { en: 'Mild-Moderate', ar: 'خفيف إلى متوسط' },\n      3: { en: 'Moderate', ar: 'متوسط' },\n      4: { en: 'Moderate-Severe', ar: 'متوسط إلى شديد' },\n      5: { en: 'Severe', ar: 'شديد' },\n      6: { en: 'Very Severe', ar: 'شديد جداً' },\n      7: { en: 'Extreme', ar: 'مفرط' },\n      8: { en: 'Unbearable', ar: 'لا يُحتمل' },\n      9: { en: 'Maximum', ar: 'أقصى' },\n      10: { en: 'Worst Possible', ar: 'الأسوأ على الإطلاق' }\n    };\n    return isRTL ? levels[level]?.ar : levels[level]?.en;\n  };\n\n  if (painPoints.length === 0) {\n    return (\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('painAssessmentTable', 'Pain Assessment Details')}\n        </h3>\n        <div className=\"text-center py-8\">\n          <div className=\"text-gray-400 dark:text-gray-500 mb-2\">\n            <svg className=\"mx-auto h-12 w-12\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n          </div>\n          <p className=\"text-gray-500 dark:text-gray-400\">\n            {t('noPainPointsSelected', 'No pain points selected. Click on the body map to add pain locations.')}\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600\">\n      <div className=\"p-4 border-b border-gray-200 dark:border-gray-600\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n          {t('painAssessmentTable', 'Pain Assessment Details')}\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          {t('painTableDescription', 'Detailed assessment of each pain location')}\n        </p>\n      </div>\n\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full\">\n          <thead className=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                {t('region', 'Region')}\n              </th>\n              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                {t('painLevel', 'Pain Level')}\n              </th>\n              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                {t('painType', 'Pain Type')}\n              </th>\n              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                {t('frequency', 'Frequency')}\n              </th>\n              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                {t('triggers', 'Triggers')}\n              </th>\n              <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                {t('notes', 'Notes')}\n              </th>\n              {!readonly && (\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              )}\n            </tr>\n          </thead>\n          <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n            {painPoints.map((painPoint, index) => (\n              <tr key={painPoint.regionId} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                {/* Region */}\n                <td className=\"px-4 py-4 whitespace-nowrap\">\n                  <div className=\"flex items-center\">\n                    <div \n                      className=\"w-4 h-4 rounded-full mr-3 border border-gray-300\"\n                      style={{ backgroundColor: getPainLevelColor(painPoint.painLevel) }}\n                    ></div>\n                    <div>\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {isRTL ? painPoint.regionNameAr : painPoint.regionName}\n                      </div>\n                      <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        #{painPoint.regionId}\n                      </div>\n                    </div>\n                  </div>\n                </td>\n\n                {/* Pain Level */}\n                <td className=\"px-4 py-4 whitespace-nowrap\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-lg font-bold text-gray-900 dark:text-white mr-2\">\n                      {painPoint.painLevel}/10\n                    </span>\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {getPainLevelText(painPoint.painLevel)}\n                    </span>\n                  </div>\n                </td>\n\n                {/* Pain Type */}\n                <td className=\"px-4 py-4 whitespace-nowrap\">\n                  {readonly ? (\n                    <span className=\"text-sm text-gray-900 dark:text-white\">\n                      {painPoint.painType ? \n                        (isRTL ? \n                          painTypes.find(t => t.value === painPoint.painType)?.labelAr : \n                          painTypes.find(t => t.value === painPoint.painType)?.label\n                        ) : '-'\n                      }\n                    </span>\n                  ) : (\n                    <select\n                      value={painPoint.painType || ''}\n                      onChange={(e) => handlePainPointUpdate(painPoint.regionId, 'painType', e.target.value)}\n                      className=\"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"\">{t('select', 'Select...')}</option>\n                      {painTypes.map(type => (\n                        <option key={type.value} value={type.value}>\n                          {isRTL ? type.labelAr : type.label}\n                        </option>\n                      ))}\n                    </select>\n                  )}\n                </td>\n\n                {/* Frequency */}\n                <td className=\"px-4 py-4 whitespace-nowrap\">\n                  {readonly ? (\n                    <span className=\"text-sm text-gray-900 dark:text-white\">\n                      {painPoint.frequency ? \n                        (isRTL ? \n                          painFrequency.find(f => f.value === painPoint.frequency)?.labelAr : \n                          painFrequency.find(f => f.value === painPoint.frequency)?.label\n                        ) : '-'\n                      }\n                    </span>\n                  ) : (\n                    <select\n                      value={painPoint.frequency || ''}\n                      onChange={(e) => handlePainPointUpdate(painPoint.regionId, 'frequency', e.target.value)}\n                      className=\"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"\">{t('select', 'Select...')}</option>\n                      {painFrequency.map(freq => (\n                        <option key={freq.value} value={freq.value}>\n                          {isRTL ? freq.labelAr : freq.label}\n                        </option>\n                      ))}\n                    </select>\n                  )}\n                </td>\n\n                {/* Triggers */}\n                <td className=\"px-4 py-4 whitespace-nowrap\">\n                  {readonly ? (\n                    <span className=\"text-sm text-gray-900 dark:text-white\">\n                      {painPoint.triggers ? \n                        (isRTL ? \n                          painTriggers.find(t => t.value === painPoint.triggers)?.labelAr : \n                          painTriggers.find(t => t.value === painPoint.triggers)?.label\n                        ) : '-'\n                      }\n                    </span>\n                  ) : (\n                    <select\n                      value={painPoint.triggers || ''}\n                      onChange={(e) => handlePainPointUpdate(painPoint.regionId, 'triggers', e.target.value)}\n                      className=\"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    >\n                      <option value=\"\">{t('select', 'Select...')}</option>\n                      {painTriggers.map(trigger => (\n                        <option key={trigger.value} value={trigger.value}>\n                          {isRTL ? trigger.labelAr : trigger.label}\n                        </option>\n                      ))}\n                    </select>\n                  )}\n                </td>\n\n                {/* Notes */}\n                <td className=\"px-4 py-4\">\n                  {readonly ? (\n                    <span className=\"text-sm text-gray-900 dark:text-white\">\n                      {painPoint.notes || '-'}\n                    </span>\n                  ) : (\n                    <input\n                      type=\"text\"\n                      value={painPoint.notes || ''}\n                      onChange={(e) => handlePainPointUpdate(painPoint.regionId, 'notes', e.target.value)}\n                      placeholder={t('addNotes', 'Add notes...')}\n                      className=\"text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white w-full\"\n                    />\n                  )}\n                </td>\n\n                {/* Actions */}\n                {!readonly && (\n                  <td className=\"px-4 py-4 whitespace-nowrap\">\n                    <button\n                      onClick={() => handleDeletePainPoint(painPoint.regionId)}\n                      className=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                      title={t('deletePainPoint', 'Delete pain point')}\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                      </svg>\n                    </button>\n                  </td>\n                )}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {/* Summary */}\n      <div className=\"p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600\">\n        <div className=\"flex justify-between items-center text-sm\">\n          <span className=\"text-gray-600 dark:text-gray-400\">\n            {t('totalPainPoints', 'Total Pain Points')}: <strong>{painPoints.length}</strong>\n          </span>\n          <span className=\"text-gray-600 dark:text-gray-400\">\n            {t('averagePainLevel', 'Average Pain Level')}: <strong>\n              {painPoints.length > 0 ? \n                (painPoints.reduce((sum, p) => sum + p.painLevel, 0) / painPoints.length).toFixed(1) : \n                '0'\n              }/10\n            </strong>\n          </span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PainAssessmentTable;\n", "import React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\nimport PatientStatusSection from './PatientStatusSection';\nimport TreatmentActivitiesSection from './TreatmentActivitiesSection';\nimport ProgressNotesSection from './ProgressNotesSection';\nimport ClinicalBodyMap from '../BodyMap/ClinicalBodyMap';\nimport PainAssessmentTable from '../BodyMap/PainAssessmentTable';\n\nconst DailyProgressForm = ({\n  patientId,\n  treatmentId,\n  patientData,\n  fromPatientProfile,\n  initialData,\n  onSave,\n  onCancel\n}) => {\n  const { t } = useLanguage();\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const { patientId: urlPatientId, treatmentId: urlTreatmentId, progressId } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [patient, setPatient] = useState(null);\n  const [treatment, setTreatment] = useState(null);\n\n  // Use patientId and treatmentId from props or URL params\n  const activePatientId = patientId || urlPatientId;\n  const activeTreatmentId = treatmentId || urlTreatmentId;\n\n  // Form data structure\n  const [formData, setFormData] = useState({\n    // Session Information\n    sessionDate: new Date().toISOString().split('T')[0],\n    sessionTime: new Date().toTimeString().slice(0, 5),\n    sessionDuration: '',\n    sessionType: 'individual', // individual, group, home_visit\n    \n    // Caregiver Information (auto-populated from auth)\n    caregiverName: user?.name || '',\n    caregiverTitle: user?.title || '',\n    caregiverBadgeNo: user?.badgeNo || '',\n    caregiverDepartment: user?.department || '',\n    \n    // Patient Status\n    patientAttendance: 'present', // present, absent, late, early_departure\n    patientMood: 'cooperative', // cooperative, anxious, agitated, withdrawn, cheerful\n    patientEnergyLevel: 'normal', // high, normal, low, fatigued\n    \n    // Treatment Activities\n    activitiesPerformed: [],\n    exercisesCompleted: [],\n    equipmentUsed: [],\n    modificationsNeeded: '',\n    \n    // Progress Assessment\n    painLevel: {\n      before: '',\n      after: '',\n      scale: '0-10'\n    },\n    painPoints: [], // Array of pain points from body map\n    painAssessmentNotes: '',\n    functionalGoals: [],\n    progressTowardsGoals: '',\n    \n    // Vital Signs (if applicable)\n    vitalSigns: {\n      bloodPressure: '',\n      heartRate: '',\n      respiratoryRate: '',\n      temperature: '',\n      oxygenSaturation: ''\n    },\n    \n    // Detailed Notes\n    objectiveFindings: '',\n    subjectiveComplaints: '',\n    treatmentResponse: '',\n    adverseReactions: '',\n    \n    // Plan and Recommendations\n    nextSessionPlan: '',\n    homeExerciseCompliance: '', // excellent, good, fair, poor, not_applicable\n    recommendationsForFamily: '',\n    \n    // Quality Indicators\n    sessionQuality: 'excellent', // excellent, good, satisfactory, needs_improvement\n    patientSatisfaction: '', // 1-5 scale\n    goalAchievement: '', // percentage or description\n    \n    // Follow-up\n    followUpRequired: false,\n    followUpDate: '',\n    followUpReason: '',\n    \n    // Compliance and Safety\n    safetyIncidents: false,\n    incidentDescription: '',\n    infectionControlCompliance: true,\n    equipmentSafetyCheck: true\n  });\n\n  // Load patient and treatment data\n  useEffect(() => {\n    if (fromPatientProfile && patientData) {\n      // Use patient data passed from patient profile\n      setPatient({\n        id: patientData._id || patientData.id,\n        name: patientData.name || `${patientData.firstName} ${patientData.lastName}`,\n        nameEn: patientData.nameEn || `${patientData.firstName} ${patientData.lastName}`,\n        mrNumber: patientData.mrNumber || patientData._id,\n        age: patientData.age,\n        diagnosis: patientData.medicalHistory?.primaryDiagnosis || patientData.diagnosis || 'Not specified'\n      });\n\n      // Mock treatment data - in real implementation, fetch from API\n      const mockTreatment = {\n        id: activeTreatmentId,\n        type: 'Physical Therapy',\n        startDate: '2024-01-15',\n        therapist: patientData.therapyInfo?.primaryTherapist || 'Current Therapist',\n        currentGoals: [\n          'Improve balance and coordination',\n          'Increase lower extremity strength',\n          'Enhance functional mobility'\n        ]\n      };\n\n      setTreatment(mockTreatment);\n\n      // Pre-populate form data with patient information\n      if (initialData) {\n        setFormData(prevData => ({\n          ...prevData,\n          ...initialData,\n          functionalGoals: mockTreatment.currentGoals.map(goal => ({\n            goal: goal,\n            progress: '',\n            achieved: false\n          }))\n        }));\n      }\n\n      setLoading(false);\n    } else if (activePatientId) {\n      setLoading(true);\n      // Mock data loading - replace with actual API calls\n      setTimeout(() => {\n        const mockPatient = {\n          id: activePatientId,\n          name: 'أحمد محمد علي',\n          nameEn: 'Ahmed Mohammed Ali',\n          mrNumber: 'MR-2024-001',\n          age: 8,\n          diagnosis: 'Cerebral Palsy'\n        };\n\n        const mockTreatment = {\n          id: activeTreatmentId,\n          type: 'Physical Therapy',\n          startDate: '2024-01-15',\n          therapist: 'Dr. Sarah Ahmed',\n          currentGoals: [\n            'Improve balance and coordination',\n            'Increase lower extremity strength',\n            'Enhance functional mobility'\n          ]\n        };\n\n        setPatient(mockPatient);\n        setTreatment(mockTreatment);\n\n        // Pre-populate functional goals from treatment\n        setFormData(prevData => ({\n          ...prevData,\n          functionalGoals: mockTreatment.currentGoals.map(goal => ({\n            goal: goal,\n            progress: '',\n            achieved: false\n          }))\n        }));\n\n        setLoading(false);\n      }, 500);\n    }\n  }, [activePatientId, activeTreatmentId, fromPatientProfile, patientData, initialData]);\n\n  const handleInputChange = (field, value) => {\n    const newData = { ...formData };\n    \n    // Handle nested fields\n    if (field.includes('.')) {\n      const parts = field.split('.');\n      let current = newData;\n      for (let i = 0; i < parts.length - 1; i++) {\n        if (!current[parts[i]]) current[parts[i]] = {};\n        current = current[parts[i]];\n      }\n      current[parts[parts.length - 1]] = value;\n    } else {\n      newData[field] = value;\n    }\n\n    setFormData(newData);\n\n    // Clear error when user starts typing\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const handleArrayChange = (field, index, value) => {\n    const newData = { ...formData };\n    const parts = field.split('.');\n    let current = newData;\n    \n    for (let i = 0; i < parts.length - 1; i++) {\n      current = current[parts[i]];\n    }\n    \n    current[parts[parts.length - 1]][index] = value;\n    setFormData(newData);\n  };\n\n  const addArrayItem = (field, defaultValue = '') => {\n    const newData = { ...formData };\n    if (!newData[field]) newData[field] = [];\n    newData[field].push(defaultValue);\n    setFormData(newData);\n  };\n\n  const removeArrayItem = (field, index) => {\n    const newData = { ...formData };\n    newData[field].splice(index, 1);\n    setFormData(newData);\n  };\n\n  // Pain assessment handlers\n  const handlePainPointsUpdate = (painPoints) => {\n    setFormData(prev => ({\n      ...prev,\n      painPoints: painPoints\n    }));\n  };\n\n  const handlePainPointDelete = (regionId) => {\n    const updatedPainPoints = formData.painPoints.filter(point => point.regionId !== regionId);\n    setFormData(prev => ({\n      ...prev,\n      painPoints: updatedPainPoints\n    }));\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Required fields validation\n    if (!formData.sessionDate) {\n      newErrors.sessionDate = t('sessionDateRequired', 'Session date is required');\n    }\n    if (!formData.sessionTime) {\n      newErrors.sessionTime = t('sessionTimeRequired', 'Session time is required');\n    }\n    if (!formData.sessionDuration) {\n      newErrors.sessionDuration = t('sessionDurationRequired', 'Session duration is required');\n    }\n    if (!formData.caregiverName.trim()) {\n      newErrors.caregiverName = t('caregiverNameRequired', 'Caregiver name is required');\n    }\n    if (!formData.objectiveFindings.trim()) {\n      newErrors.objectiveFindings = t('objectiveFindingsRequired', 'Objective findings are required');\n    }\n\n    // Pain level validation\n    if (formData.painLevel.before && (formData.painLevel.before < 0 || formData.painLevel.before > 10)) {\n      newErrors.painBefore = t('painLevelInvalid', 'Pain level must be between 0-10');\n    }\n    if (formData.painLevel.after && (formData.painLevel.after < 0 || formData.painLevel.after > 10)) {\n      newErrors.painAfter = t('painLevelInvalid', 'Pain level must be between 0-10');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    \n    try {\n      // Mock API call - replace with actual API endpoint\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const progressNote = {\n        ...formData,\n        id: Date.now(),\n        patientId: activePatientId,\n        treatmentId: activeTreatmentId,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        createdBy: user?.id || 'current-user'\n      };\n      \n      if (onSave) {\n        onSave(progressNote);\n      } else {\n        // Save to localStorage for demo\n        const savedProgress = JSON.parse(localStorage.getItem('dailyProgressData') || '[]');\n        savedProgress.push(progressNote);\n        localStorage.setItem('dailyProgressData', JSON.stringify(savedProgress));\n        \n        toast.success(t('progressNoteSaved', 'Daily progress note saved successfully'));\n        \n        if (activePatientId) {\n          navigate(`/patients/${activePatientId}`);\n        } else {\n          navigate('/patients');\n        }\n      }\n    } catch (error) {\n      console.error('Error saving progress note:', error);\n      toast.error(t('errorSavingProgress', 'Error saving progress note'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading && !patient) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900\">\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Header */}\n        <div className=\"border-b border-gray-200 dark:border-gray-600 pb-4\">\n          <div className=\"flex items-start justify-between\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {t('dailyProgressNote', 'Daily Progress Note')}\n                {activePatientId && patient && (\n                  <span className=\"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3\">\n                    - {patient.nameEn || patient.name}\n                  </span>\n                )}\n              </h1>\n              <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n                {t('progressNoteDescription', 'Comprehensive daily treatment progress documentation')}\n              </p>\n              \n              {/* Session Info */}\n              {patient && treatment && (\n                <div className=\"flex flex-wrap gap-4 mt-3 text-sm\">\n                  <div className=\"flex items-center space-x-2\">\n                    <i className=\"fas fa-user text-blue-600 dark:text-blue-400\"></i>\n                    <span className=\"text-gray-700 dark:text-gray-300\">\n                      {t('patient', 'Patient')}: {patient.nameEn} ({patient.mrNumber})\n                    </span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <i className=\"fas fa-heartbeat text-green-600 dark:text-green-400\"></i>\n                    <span className=\"text-gray-700 dark:text-gray-300\">\n                      {t('treatment', 'Treatment')}: {treatment.type}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <i className=\"fas fa-user-md text-purple-600 dark:text-purple-400\"></i>\n                    <span className=\"text-gray-700 dark:text-gray-300\">\n                      {t('caregiver', 'Caregiver')}: {formData.caregiverName}\n                    </span>\n                  </div>\n                </div>\n              )}\n            </div>\n            \n            <div className=\"flex space-x-3\">\n              <button\n                type=\"button\"\n                onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n                className=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\"\n              >\n                {t('cancel', 'Cancel')}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Session Information */}\n        <div className=\"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            <i className=\"fas fa-clock text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('sessionInformation', 'Session Information')}\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('sessionDate', 'Session Date')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"date\"\n                value={formData.sessionDate}\n                onChange={(e) => handleInputChange('sessionDate', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.sessionDate ? 'border-red-500' : 'border-gray-300'\n                }`}\n              />\n              {errors.sessionDate && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.sessionDate}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('sessionTime', 'Session Time')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"time\"\n                value={formData.sessionTime}\n                onChange={(e) => handleInputChange('sessionTime', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.sessionTime ? 'border-red-500' : 'border-gray-300'\n                }`}\n              />\n              {errors.sessionTime && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.sessionTime}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('duration', 'Duration (minutes)')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"number\"\n                min=\"15\"\n                max=\"180\"\n                value={formData.sessionDuration}\n                onChange={(e) => handleInputChange('sessionDuration', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                  errors.sessionDuration ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"30\"\n              />\n              {errors.sessionDuration && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.sessionDuration}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                {t('sessionType', 'Session Type')}\n              </label>\n              <select\n                value={formData.sessionType}\n                onChange={(e) => handleInputChange('sessionType', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n              >\n                <option value=\"individual\">{t('individual', 'Individual')}</option>\n                <option value=\"group\">{t('group', 'Group')}</option>\n                <option value=\"home_visit\">{t('homeVisit', 'Home Visit')}</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Caregiver Information */}\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6\">\n          <h2 className=\"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4\">\n            <i className=\"fas fa-user-md text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('caregiverInformation', 'Caregiver Information')}\n          </h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                {t('caregiverName', 'Caregiver Name')} <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                type=\"text\"\n                value={formData.caregiverName}\n                onChange={(e) => handleInputChange('caregiverName', e.target.value)}\n                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white ${\n                  errors.caregiverName ? 'border-red-500' : 'border-blue-300'\n                }`}\n                placeholder={t('enterCaregiverName', 'Enter caregiver name')}\n              />\n              {errors.caregiverName && (\n                <p className=\"text-red-500 text-sm mt-1\">{errors.caregiverName}</p>\n              )}\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                {t('title', 'Title/Position')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.caregiverTitle}\n                onChange={(e) => handleInputChange('caregiverTitle', e.target.value)}\n                className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n                placeholder={t('enterTitle', 'e.g., Physical Therapist')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                {t('badgeNumber', 'Badge Number')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.caregiverBadgeNo}\n                onChange={(e) => handleInputChange('caregiverBadgeNo', e.target.value)}\n                className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n                placeholder={t('enterBadgeNumber', 'Enter badge number')}\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1\">\n                {t('department', 'Department')}\n              </label>\n              <input\n                type=\"text\"\n                value={formData.caregiverDepartment}\n                onChange={(e) => handleInputChange('caregiverDepartment', e.target.value)}\n                className=\"w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-blue-800 dark:border-blue-600 dark:text-white\"\n                placeholder={t('enterDepartment', 'e.g., Physical Therapy')}\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Patient Status Section */}\n        <PatientStatusSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          errors={errors}\n        />\n\n        {/* Treatment Activities Section */}\n        <TreatmentActivitiesSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          addArrayItem={addArrayItem}\n          removeArrayItem={removeArrayItem}\n          handleArrayChange={handleArrayChange}\n        />\n\n        {/* Progress Notes Section */}\n        <ProgressNotesSection\n          formData={formData}\n          handleInputChange={handleInputChange}\n          handleArrayChange={handleArrayChange}\n          errors={errors}\n        />\n\n        {/* Pain Assessment Section */}\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6\">\n          <h2 className=\"text-lg font-semibold text-red-900 dark:text-red-100 mb-4\">\n            <i className=\"fas fa-heartbeat text-red-600 dark:text-red-400 mr-2\"></i>\n            {t('painAssessment', 'Pain Assessment')}\n          </h2>\n\n          {/* Traditional Pain Scale */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-1\">\n                {t('painLevelBefore', 'Pain Level Before Treatment')}\n              </label>\n              <select\n                value={formData.painLevel.before}\n                onChange={(e) => handleInputChange('painLevel.before', e.target.value)}\n                className=\"w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white\"\n              >\n                <option value=\"\">{t('select', 'Select...')}</option>\n                {[...Array(11)].map((_, i) => (\n                  <option key={i} value={i}>\n                    {i}/10 - {i === 0 ? t('noPain', 'No Pain') :\n                           i <= 3 ? t('mildPain', 'Mild') :\n                           i <= 6 ? t('moderatePain', 'Moderate') :\n                           t('severePain', 'Severe')}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-1\">\n                {t('painLevelAfter', 'Pain Level After Treatment')}\n              </label>\n              <select\n                value={formData.painLevel.after}\n                onChange={(e) => handleInputChange('painLevel.after', e.target.value)}\n                className=\"w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white\"\n              >\n                <option value=\"\">{t('select', 'Select...')}</option>\n                {[...Array(11)].map((_, i) => (\n                  <option key={i} value={i}>\n                    {i}/10 - {i === 0 ? t('noPain', 'No Pain') :\n                           i <= 3 ? t('mildPain', 'Mild') :\n                           i <= 6 ? t('moderatePain', 'Moderate') :\n                           t('severePain', 'Severe')}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-red-800 dark:text-red-200 mb-1\">\n                {t('painAssessmentNotes', 'Pain Assessment Notes')}\n              </label>\n              <textarea\n                value={formData.painAssessmentNotes}\n                onChange={(e) => handleInputChange('painAssessmentNotes', e.target.value)}\n                rows=\"3\"\n                className=\"w-full px-3 py-2 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-red-800 dark:border-red-600 dark:text-white\"\n                placeholder={t('painNotesPlaceholder', 'Describe pain characteristics, triggers, relief factors...')}\n              />\n            </div>\n          </div>\n\n          {/* Interactive Body Map */}\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"text-md font-medium text-red-800 dark:text-red-200 mb-3\">\n                {t('detailedPainMapping', 'Detailed Pain Mapping')}\n              </h3>\n              <ClinicalBodyMap\n                selectedPainPoints={formData.painPoints}\n                onPainPointSelect={handlePainPointsUpdate}\n                readonly={false}\n              />\n            </div>\n\n            {/* Pain Assessment Table */}\n            {formData.painPoints.length > 0 && (\n              <div>\n                <PainAssessmentTable\n                  painPoints={formData.painPoints}\n                  onPainPointUpdate={handlePainPointsUpdate}\n                  onPainPointDelete={handlePainPointDelete}\n                  readonly={false}\n                />\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Submit Button */}\n        <div className=\"flex justify-end space-x-4\">\n          <button\n            type=\"button\"\n            onClick={onCancel || (() => navigate(activePatientId ? `/patients/${activePatientId}` : '/patients'))}\n            className=\"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            {t('cancel', 'Cancel')}\n          </button>\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                {t('saving', 'Saving...')}\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveProgressNote', 'Save Progress Note')}\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default DailyProgressForm;\n"], "names": ["_ref", "formData", "handleInputChange", "errors", "t", "useLanguage", "attendanceOptions", "value", "label", "color", "moodOptions", "energyLevelOptions", "getColorClasses", "isSelected", "baseClasses", "concat", "_jsxs", "className", "children", "_jsx", "map", "option", "patientAttendance", "type", "name", "checked", "onChange", "e", "target", "patientMood", "patientEnergyLevel", "min", "max", "painLevel", "before", "painBefore", "placeholder", "after", "painAfter", "_formData$activitiesP", "_formData$equipmentUs", "addArrayItem", "removeArrayItem", "handleArrayChange", "commonActivities", "commonEquipment", "addActivity", "activity", "duration", "intensity", "patientResponse", "completed", "onClick", "activitiesPerformed", "index", "_objectSpread", "act", "rows", "length", "addEquipment", "equipment", "settings", "effectiveness", "equipmentUsed", "eq", "modificationsNeeded", "_formData$functionalG", "sessionQualityOptions", "complianceOptions", "functionalGoals", "goal", "achieved", "progress", "objectiveFindings", "subjectiveComplaints", "treatmentResponse", "adverseReactions", "sessionQuality", "homeExerciseCompliance", "patientSatisfaction", "goalAchievement", "nextSessionPlan", "recommendationsForFamily", "painPoints", "onPainPointUpdate", "onPainPointDelete", "readonly", "isRTL", "editingId", "setEditingId", "useState", "painTypes", "labelAr", "painFrequency", "painTriggers", "handlePainPointUpdate", "painPointId", "field", "updatedPainPoints", "point", "regionId", "getPainLevelText", "level", "_levels$level", "_levels$level2", "levels", "en", "ar", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "painPoint", "_painTypes$find", "_painTypes$find2", "_painFrequency$find", "_painFrequency$find2", "_painTriggers$find", "_painTriggers$find2", "style", "backgroundColor", "regionNameAr", "regionName", "painType", "find", "frequency", "f", "freq", "triggers", "trigger", "notes", "handleDeletePainPoint", "title", "reduce", "sum", "p", "toFixed", "patientId", "treatmentId", "patientData", "fromPatientProfile", "initialData", "onSave", "onCancel", "user", "useAuth", "navigate", "useNavigate", "urlPatientId", "urlTreatmentId", "progressId", "useParams", "loading", "setLoading", "setErrors", "patient", "setPatient", "treatment", "setTreatment", "activePatientId", "activeTreatmentId", "setFormData", "sessionDate", "Date", "toISOString", "split", "sessionTime", "toTimeString", "slice", "sessionDuration", "sessionType", "caregiverName", "caregiverTitle", "caregiverBadgeNo", "badgeNo", "caregiverDepartment", "department", "exercisesCompleted", "scale", "painAssessmentNotes", "progressTowardsGoals", "vitalSigns", "bloodPressure", "heartRate", "respiratoryRate", "temperature", "oxygenSaturation", "followUpRequired", "followUpDate", "followUpReason", "safetyIncidents", "incidentDescription", "infectionControlCompliance", "equipmentSafetyCheck", "useEffect", "_patientData$medicalH", "_patientData$therapyI", "id", "_id", "firstName", "lastName", "nameEn", "mr<PERSON><PERSON><PERSON>", "age", "diagnosis", "medicalHistory", "primaryDiagnosis", "mockTreatment", "startDate", "therapist", "therapyInfo", "primaryTherapist", "currentGoals", "prevData", "setTimeout", "newData", "includes", "parts", "current", "i", "prev", "handlePainPointsUpdate", "onSubmit", "async", "preventDefault", "validateForm", "newErrors", "trim", "Object", "keys", "Promise", "resolve", "progressNote", "now", "createdAt", "updatedAt", "created<PERSON>y", "savedProgress", "JSON", "parse", "localStorage", "getItem", "push", "setItem", "stringify", "toast", "success", "error", "console", "PatientStatusSection", "TreatmentActivitiesSection", "defaultValue", "arguments", "undefined", "splice", "ProgressNotesSection", "Array", "_", "ClinicalBodyMap", "selectedPainPoints", "onPainPointSelect", "PainAssessmentTable", "filter", "disabled", "_Fragment"], "sourceRoot": ""}