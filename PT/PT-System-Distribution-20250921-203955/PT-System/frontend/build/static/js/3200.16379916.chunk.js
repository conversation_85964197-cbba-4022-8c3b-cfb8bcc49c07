"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[3200],{3200:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var r=a(2555),s=a(5043),i=a(7921),l=a(3768),n=a(579);const c=()=>{const{t:e,isRTL:t}=(0,i.o)(),[a,c]=(0,s.useState)([]),[o,d]=(0,s.useState)(!0),[m,x]=(0,s.useState)("all"),[g,h]=(0,s.useState)([]);(0,s.useEffect)(()=>{u()},[m]);const u=async()=>{d(!0);try{const e=[{id:1,type:"appointment",title:"Appointment Reminder",titleAr:"\u062a\u0630\u0643\u064a\u0631 \u0628\u0627\u0644\u0645\u0648\u0639\u062f",message:"You have an appointment with <PERSON> tomorrow at 2:00 PM",messageAr:"\u0644\u062f\u064a\u0643 \u0645\u0648\u0639\u062f \u0645\u0639 \u0623\u062d\u0645\u062f \u062d\u0633\u0646 \u063a\u062f\u0627\u064b \u0641\u064a \u0627\u0644\u0633\u0627\u0639\u0629 2:00 \u0645\u0633\u0627\u0621\u064b",timestamp:new Date(Date.now()-72e5),read:!1,priority:"medium",icon:"fas fa-calendar-alt",color:"blue"},{id:2,type:"treatment",title:"Treatment Plan Updated",titleAr:"\u062a\u0645 \u062a\u062d\u062f\u064a\u062b \u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c",message:"Treatment plan for Sarah Al-Rashid has been updated",messageAr:"\u062a\u0645 \u062a\u062d\u062f\u064a\u062b \u062e\u0637\u0629 \u0627\u0644\u0639\u0644\u0627\u062c \u0644\u0633\u0627\u0631\u0629 \u0627\u0644\u0631\u0634\u064a\u062f",timestamp:new Date(Date.now()-144e5),read:!0,priority:"high",icon:"fas fa-heartbeat",color:"green"},{id:3,type:"system",title:"System Maintenance",titleAr:"\u0635\u064a\u0627\u0646\u0629 \u0627\u0644\u0646\u0638\u0627\u0645",message:"Scheduled maintenance will occur tonight from 11 PM to 1 AM",messageAr:"\u0633\u062a\u062a\u0645 \u0627\u0644\u0635\u064a\u0627\u0646\u0629 \u0627\u0644\u0645\u062c\u062f\u0648\u0644\u0629 \u0627\u0644\u0644\u064a\u0644\u0629 \u0645\u0646 11 \u0645\u0633\u0627\u0621\u064b \u0625\u0644\u0649 1 \u0635\u0628\u0627\u062d\u0627\u064b",timestamp:new Date(Date.now()-216e5),read:!1,priority:"low",icon:"fas fa-cog",color:"yellow"},{id:4,type:"billing",title:"Payment Received",titleAr:"\u062a\u0645 \u0627\u0633\u062a\u0644\u0627\u0645 \u0627\u0644\u062f\u0641\u0639\u0629",message:"Payment of $250 received from Omar Khalil",messageAr:"\u062a\u0645 \u0627\u0633\u062a\u0644\u0627\u0645 \u062f\u0641\u0639\u0629 \u0628\u0642\u064a\u0645\u0629 250 \u062f\u0648\u0644\u0627\u0631 \u0645\u0646 \u0639\u0645\u0631 \u062e\u0644\u064a\u0644",timestamp:new Date(Date.now()-288e5),read:!0,priority:"medium",icon:"fas fa-dollar-sign",color:"green"},{id:5,type:"alert",title:"High Priority Alert",titleAr:"\u062a\u0646\u0628\u064a\u0647 \u0639\u0627\u0644\u064a \u0627\u0644\u0623\u0648\u0644\u0648\u064a\u0629",message:"Patient Fatima Al-Zahra missed 3 consecutive appointments",messageAr:"\u0627\u0644\u0645\u0631\u064a\u0636\u0629 \u0641\u0627\u0637\u0645\u0629 \u0627\u0644\u0632\u0647\u0631\u0627\u0621 \u0641\u0627\u062a\u062a 3 \u0645\u0648\u0627\u0639\u064a\u062f \u0645\u062a\u062a\u0627\u0644\u064a\u0629",timestamp:new Date(Date.now()-432e5),read:!1,priority:"high",icon:"fas fa-exclamation-triangle",color:"red"}];let t=e;"all"!==m&&(t="unread"===m?e.filter(e=>!e.read):e.filter(e=>e.type===m)),c(t)}catch(e){console.error("Error fetching notifications:",e),l.Ay.error("Failed to load notifications")}finally{d(!1)}},y=e=>{switch(e){case"high":return"text-red-600 bg-red-100";case"medium":return"text-yellow-600 bg-yellow-100";case"low":return"text-green-600 bg-green-100";default:return"text-gray-600 bg-gray-100"}},f=e=>{switch(e){case"appointment":return"text-blue-600";case"treatment":return"text-green-600";case"billing":return"text-purple-600";case"alert":return"text-red-600";default:return"text-gray-600"}},p=t=>{const a=new Date-t,r=Math.floor(a/36e5),s=Math.floor(r/24);return r<1?e("justNow","Just now"):r<24?e("hoursAgo","".concat(r," hours ago")):s<7?e("daysAgo","".concat(s," days ago")):t.toLocaleDateString()};return o?(0,n.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("notificationCenter","Notification Center")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:e("manageNotifications","Manage your notifications and alerts")})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-4 mt-4 sm:mt-0",children:[(0,n.jsxs)("button",{onClick:async()=>{try{c(e=>e.map(e=>(0,r.A)((0,r.A)({},e),{},{read:!0}))),l.Ay.success("All notifications marked as read")}catch(e){console.error("Error marking all notifications as read:",e),l.Ay.error("Failed to mark all notifications as read")}},className:"px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-check-double mr-2"}),e("markAllRead","Mark All Read")]}),g.length>0&&(0,n.jsxs)("button",{onClick:async()=>{try{c(e=>e.filter(e=>!g.includes(e.id))),h([]),l.Ay.success("Selected notifications deleted")}catch(e){console.error("Error deleting selected notifications:",e),l.Ay.error("Failed to delete selected notifications")}},className:"px-4 py-2 text-red-600 hover:bg-red-50 rounded-md transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-trash mr-2"}),e("deleteSelected","Delete Selected")]})]})]}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:["all","unread","appointment","treatment","billing","alert","system"].map(t=>(0,n.jsx)("button",{onClick:()=>x(t),className:"px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(m===t?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"),children:e(t,t.charAt(0).toUpperCase()+t.slice(1))},t))}),a.length>0&&(0,n.jsx)("div",{className:"flex items-center justify-between bg-gray-50 dark:bg-gray-800 p-4 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("label",{className:"flex items-center",children:[(0,n.jsx)("input",{type:"checkbox",checked:g.length===a.length,onChange:e=>e.target.checked?void h(a.map(e=>e.id)):void h([]),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:e("selectAll","Select All")})]}),g.length>0&&(0,n.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[g.length," ",e("selected","selected")]})]})}),(0,n.jsx)("div",{className:"space-y-4",children:0===a.length?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("i",{className:"fas fa-bell-slash text-4xl text-gray-400 mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noNotifications","No notifications")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("noNotificationsDescription","You're all caught up! No new notifications.")})]}):a.map(a=>(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-4 border-l-4 ".concat(a.read?"border-gray-300":"border-blue-500"," ").concat(a.read?"":"bg-blue-50 dark:bg-blue-900/20"),children:(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,n.jsx)("input",{type:"checkbox",checked:g.includes(a.id),onChange:()=>{return e=a.id,void h(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e]);var e},className:"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("div",{className:"p-2 rounded-lg ".concat(f(a.type)," bg-opacity-10"),children:(0,n.jsx)("i",{className:"".concat(a.icon," ").concat(f(a.type))})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:t?a.titleAr:a.title}),(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(y(a.priority)),children:e(a.priority,a.priority)}),!a.read&&(0,n.jsx)("span",{className:"w-2 h-2 bg-blue-600 rounded-full"})]}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-2",children:t?a.messageAr:a.message}),(0,n.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:p(a.timestamp)})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[!a.read&&(0,n.jsx)("button",{onClick:()=>(async e=>{try{c(t=>t.map(t=>t.id===e?(0,r.A)((0,r.A)({},t),{},{read:!0}):t)),l.Ay.success("Notification marked as read")}catch(t){console.error("Error marking notification as read:",t),l.Ay.error("Failed to mark notification as read")}})(a.id),className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:e("markAsRead","Mark as read"),children:(0,n.jsx)("i",{className:"fas fa-check"})}),(0,n.jsx)("button",{onClick:()=>(async e=>{try{c(t=>t.filter(t=>t.id!==e)),l.Ay.success("Notification deleted")}catch(t){console.error("Error deleting notification:",t),l.Ay.error("Failed to delete notification")}})(a.id),className:"p-2 text-gray-400 hover:text-red-600 transition-colors",title:e("delete","Delete"),children:(0,n.jsx)("i",{className:"fas fa-trash"})})]})]})},a.id))})]})}}}]);
//# sourceMappingURL=3200.16379916.chunk.js.map