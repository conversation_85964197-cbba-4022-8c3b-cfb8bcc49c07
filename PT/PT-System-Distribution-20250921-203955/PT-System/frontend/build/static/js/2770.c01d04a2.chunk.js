"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2770],{2770:(e,t,s)=>{s.d(t,{A:()=>d});var r=s(2555),a=s(5043),i=s(7921),l=s(930),n=s(3768),o=s(579);const c=e=>{let{regionId:t,regionName:s,currentPainLevel:r,currentDetails:l,onSave:n,onClose:c}=e;const{t:d}=(0,i.o)(),[h,m]=(0,a.useState)(r),[u,x]=(0,a.useState)(l.type||""),[g,f]=(0,a.useState)(l.duration||""),[p,y]=(0,a.useState)(l.frequency||""),[k,b]=(0,a.useState)(l.notes||"");return(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,o.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:d("painAssessment","Pain Assessment")}),(0,o.jsx)("button",{onClick:c,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,o.jsx)("i",{className:"fas fa-times"})})]}),(0,o.jsx)("div",{className:"mb-4",children:(0,o.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white mb-2",children:[d("region","Region"),": ",s]})}),(0,o.jsxs)("div",{className:"mb-6",children:[(0,o.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[d("painLevel","Pain Level"),": ",h,"/10"]}),(0,o.jsx)("input",{type:"range",min:"0",max:"10",value:h,onChange:e=>m(parseInt(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,o.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,o.jsx)("span",{children:d("noPain","No Pain")}),(0,o.jsx)("span",{children:d("extremePain","Extreme Pain")})]})]}),(0,o.jsxs)("div",{className:"flex space-x-3",children:[(0,o.jsx)("button",{onClick:c,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors",children:d("cancel","Cancel")}),(0,o.jsx)("button",{onClick:()=>{n(t,h,{type:u,duration:g,frequency:p,notes:k})},className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:d("save","Save")})]})]})})})},d=e=>{var t,s,d;let{patientId:h,onRegionSelect:m,selectedRegions:u=[],painLevels:x={},readOnly:g=!1,onSave:f,showInstructions:p=!1}=e;const{t:y,isRTL:k}=(0,i.o)(),[b,v]=(0,a.useState)(null),[j,N]=(0,a.useState)(u),[A,w]=(0,a.useState)(x),[C,M]=(0,a.useState)({}),[S,L]=(0,a.useState)(!1),[E,W]=(0,a.useState)(null),[F,R]=(0,a.useState)(!1);(0,a.useEffect)(()=>{h&&T()},[h]);const T=async()=>{try{const e=await l.A.getBodyMapData(h);e&&(N(e.selectedRegions||[]),w(e.painLevels||{}),M(e.painDetails||{}))}catch(e){console.error("Error loading body map data:",e)}},H={head:{id:"head",name:y("head","Head"),nameAr:"\u0627\u0644\u0631\u0623\u0633"},neck:{id:"neck",name:y("neck","Neck"),nameAr:"\u0627\u0644\u0631\u0642\u0628\u0629"},leftShoulder:{id:"leftShoulder",name:y("leftShoulder","Left Shoulder"),nameAr:"\u0627\u0644\u0643\u062a\u0641 \u0627\u0644\u0623\u064a\u0633\u0631"},rightShoulder:{id:"rightShoulder",name:y("rightShoulder","Right Shoulder"),nameAr:"\u0627\u0644\u0643\u062a\u0641 \u0627\u0644\u0623\u064a\u0645\u0646"},leftArm:{id:"leftArm",name:y("leftArm","Left Arm"),nameAr:"\u0627\u0644\u0630\u0631\u0627\u0639 \u0627\u0644\u0623\u064a\u0633\u0631"},rightArm:{id:"rightArm",name:y("rightArm","Right Arm"),nameAr:"\u0627\u0644\u0630\u0631\u0627\u0639 \u0627\u0644\u0623\u064a\u0645\u0646"},leftElbow:{id:"leftElbow",name:y("leftElbow","Left Elbow"),nameAr:"\u0627\u0644\u0643\u0648\u0639 \u0627\u0644\u0623\u064a\u0633\u0631"},rightElbow:{id:"rightElbow",name:y("rightElbow","Right Elbow"),nameAr:"\u0627\u0644\u0643\u0648\u0639 \u0627\u0644\u0623\u064a\u0645\u0646"},leftWrist:{id:"leftWrist",name:y("leftWrist","Left Wrist"),nameAr:"\u0627\u0644\u0645\u0639\u0635\u0645 \u0627\u0644\u0623\u064a\u0633\u0631"},rightWrist:{id:"rightWrist",name:y("rightWrist","Right Wrist"),nameAr:"\u0627\u0644\u0645\u0639\u0635\u0645 \u0627\u0644\u0623\u064a\u0645\u0646"},chest:{id:"chest",name:y("chest","Chest"),nameAr:"\u0627\u0644\u0635\u062f\u0631"},upperBack:{id:"upperBack",name:y("upperBack","Upper Back"),nameAr:"\u0623\u0639\u0644\u0649 \u0627\u0644\u0638\u0647\u0631"},lowerBack:{id:"lowerBack",name:y("lowerBack","Lower Back"),nameAr:"\u0623\u0633\u0641\u0644 \u0627\u0644\u0638\u0647\u0631"},abdomen:{id:"abdomen",name:y("abdomen","Abdomen"),nameAr:"\u0627\u0644\u0628\u0637\u0646"},leftHip:{id:"leftHip",name:y("leftHip","Left Hip"),nameAr:"\u0627\u0644\u0648\u0631\u0643 \u0627\u0644\u0623\u064a\u0633\u0631"},rightHip:{id:"rightHip",name:y("rightHip","Right Hip"),nameAr:"\u0627\u0644\u0648\u0631\u0643 \u0627\u0644\u0623\u064a\u0645\u0646"},leftThigh:{id:"leftThigh",name:y("leftThigh","Left Thigh"),nameAr:"\u0627\u0644\u0641\u062e\u0630 \u0627\u0644\u0623\u064a\u0633\u0631"},rightThigh:{id:"rightThigh",name:y("rightThigh","Right Thigh"),nameAr:"\u0627\u0644\u0641\u062e\u0630 \u0627\u0644\u0623\u064a\u0645\u0646"},leftKnee:{id:"leftKnee",name:y("leftKnee","Left Knee"),nameAr:"\u0627\u0644\u0631\u0643\u0628\u0629 \u0627\u0644\u064a\u0633\u0631\u0649"},rightKnee:{id:"rightKnee",name:y("rightKnee","Right Knee"),nameAr:"\u0627\u0644\u0631\u0643\u0628\u0629 \u0627\u0644\u064a\u0645\u0646\u0649"},leftCalf:{id:"leftCalf",name:y("leftCalf","Left Calf"),nameAr:"\u0631\u0628\u0644\u0629 \u0627\u0644\u0633\u0627\u0642 \u0627\u0644\u064a\u0633\u0631\u0649"},rightCalf:{id:"rightCalf",name:y("rightCalf","Right Calf"),nameAr:"\u0631\u0628\u0644\u0629 \u0627\u0644\u0633\u0627\u0642 \u0627\u0644\u064a\u0645\u0646\u0649"},leftAnkle:{id:"leftAnkle",name:y("leftAnkle","Left Ankle"),nameAr:"\u0627\u0644\u0643\u0627\u062d\u0644 \u0627\u0644\u0623\u064a\u0633\u0631"},rightAnkle:{id:"rightAnkle",name:y("rightAnkle","Right Ankle"),nameAr:"\u0627\u0644\u0643\u0627\u062d\u0644 \u0627\u0644\u0623\u064a\u0645\u0646"},leftFoot:{id:"leftFoot",name:y("leftFoot","Left Foot"),nameAr:"\u0627\u0644\u0642\u062f\u0645 \u0627\u0644\u064a\u0633\u0631\u0649"},rightFoot:{id:"rightFoot",name:y("rightFoot","Right Foot"),nameAr:"\u0627\u0644\u0642\u062f\u0645 \u0627\u0644\u064a\u0645\u0646\u0649"}},K=e=>{g||(W(e),L(!0),m&&m(e))},B=e=>{const t=A[e]||0,s=j.includes(e);return 0!==t||s?t<=2?"#fef3c7":t<=4?"#fed7aa":t<=6?"#fca5a5":t<=8?"#f87171":"#dc2626":"#e5e7eb"},P=e=>{const t=j.includes(e);return b===e?.8:t?.7:.5};return(0,o.jsxs)("div",{className:"body-map-container",children:[(0,o.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:y("bodyMap","Body Map")}),!g&&(0,o.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:y("clickToSelectRegion","Click on body regions to select")})]}),(0,o.jsx)("div",{className:"flex justify-center",children:(0,o.jsxs)("svg",{width:"300",height:"600",viewBox:"0 0 300 600",className:"border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700",children:[(0,o.jsx)("ellipse",{cx:"150",cy:"50",rx:"25",ry:"30",fill:B("head"),opacity:P("head"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("head"),onMouseEnter:()=>v("head"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"140",y:"75",width:"20",height:"25",fill:B("neck"),opacity:P("neck"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("neck"),onMouseEnter:()=>v("neck"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"110",cy:"120",rx:"20",ry:"15",fill:B("leftShoulder"),opacity:P("leftShoulder"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("leftShoulder"),onMouseEnter:()=>v("leftShoulder"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"190",cy:"120",rx:"20",ry:"15",fill:B("rightShoulder"),opacity:P("rightShoulder"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("rightShoulder"),onMouseEnter:()=>v("rightShoulder"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"85",y:"135",width:"15",height:"60",fill:B("leftArm"),opacity:P("leftArm"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("leftArm"),onMouseEnter:()=>v("leftArm"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"200",y:"135",width:"15",height:"60",fill:B("rightArm"),opacity:P("rightArm"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("rightArm"),onMouseEnter:()=>v("rightArm"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"125",y:"100",width:"50",height:"80",fill:B("chest"),opacity:P("chest"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("chest"),onMouseEnter:()=>v("chest"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"125",y:"180",width:"50",height:"60",fill:B("abdomen"),opacity:P("abdomen"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("abdomen"),onMouseEnter:()=>v("abdomen"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"135",cy:"260",rx:"15",ry:"20",fill:B("leftHip"),opacity:P("leftHip"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("leftHip"),onMouseEnter:()=>v("leftHip"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"165",cy:"260",rx:"15",ry:"20",fill:B("rightHip"),opacity:P("rightHip"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("rightHip"),onMouseEnter:()=>v("rightHip"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"125",y:"280",width:"20",height:"80",fill:B("leftThigh"),opacity:P("leftThigh"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("leftThigh"),onMouseEnter:()=>v("leftThigh"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"155",y:"280",width:"20",height:"80",fill:B("rightThigh"),opacity:P("rightThigh"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("rightThigh"),onMouseEnter:()=>v("rightThigh"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"135",cy:"370",rx:"12",ry:"15",fill:B("leftKnee"),opacity:P("leftKnee"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("leftKnee"),onMouseEnter:()=>v("leftKnee"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"165",cy:"370",rx:"12",ry:"15",fill:B("rightKnee"),opacity:P("rightKnee"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("rightKnee"),onMouseEnter:()=>v("rightKnee"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"125",y:"385",width:"20",height:"70",fill:B("leftCalf"),opacity:P("leftCalf"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("leftCalf"),onMouseEnter:()=>v("leftCalf"),onMouseLeave:()=>v(null)}),(0,o.jsx)("rect",{x:"155",y:"385",width:"20",height:"70",fill:B("rightCalf"),opacity:P("rightCalf"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("rightCalf"),onMouseEnter:()=>v("rightCalf"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"135",cy:"480",rx:"15",ry:"25",fill:B("leftFoot"),opacity:P("leftFoot"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("leftFoot"),onMouseEnter:()=>v("leftFoot"),onMouseLeave:()=>v(null)}),(0,o.jsx)("ellipse",{cx:"165",cy:"480",rx:"15",ry:"25",fill:B("rightFoot"),opacity:P("rightFoot"),stroke:"#374151",strokeWidth:"2",className:"cursor-pointer transition-all duration-200",onClick:()=>K("rightFoot"),onMouseEnter:()=>v("rightFoot"),onMouseLeave:()=>v(null)})]})}),b&&(0,o.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,o.jsx)("div",{className:"text-sm font-medium text-blue-900 dark:text-blue-200",children:k?null===(t=H[b])||void 0===t?void 0:t.nameAr:null===(s=H[b])||void 0===s?void 0:s.name}),A[b]&&(0,o.jsxs)("div",{className:"text-xs text-blue-700 dark:text-blue-300 mt-1",children:[y("painLevel","Pain Level"),": ",A[b],"/10"]})]}),p&&(0,o.jsxs)("div",{className:"mt-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,o.jsxs)("h4",{className:"text-sm font-medium text-blue-900 dark:text-blue-200 mb-2",children:[(0,o.jsx)("i",{className:"fas fa-info-circle mr-2"}),y("instructions","Instructions")]}),(0,o.jsxs)("ul",{className:"text-sm text-blue-800 dark:text-blue-300 space-y-1",children:[(0,o.jsxs)("li",{children:["\u2022 ",y("clickBodyRegion","Click on any body region to assess pain level")]}),(0,o.jsxs)("li",{children:["\u2022 ",y("selectPainLevel","Select pain level from 0 (no pain) to 10 (extreme pain)")]}),(0,o.jsxs)("li",{children:["\u2022 ",y("addPainDetails","Add details about pain type, duration, and frequency")]}),(0,o.jsxs)("li",{children:["\u2022 ",y("saveAssessment","Click Save Assessment to store your data")]})]})]}),h&&j.length>0&&(0,o.jsx)("div",{className:"mt-6 flex justify-center",children:(0,o.jsx)("button",{onClick:async()=>{if(h){R(!0);try{const e={selectedRegions:j,painLevels:A,painDetails:C};await l.A.saveBodyMapData(h,e),f&&f(e),n.Ay.success(y("bodyMapSaved","Body map assessment saved successfully"))}catch(e){console.error("Error saving body map data:",e),n.Ay.error(y("saveError","Error saving data"))}finally{R(!1)}}},disabled:F,className:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:F?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,o.jsx)("span",{children:y("saving","Saving...")})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("i",{className:"fas fa-save"}),(0,o.jsx)("span",{children:y("saveAssessment","Save Assessment")})]})})}),(0,o.jsxs)("div",{className:"mt-6",children:[(0,o.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white mb-3",children:y("painScale","Pain Scale")}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-gray-300 rounded"}),(0,o.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"0"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-yellow-200 rounded"}),(0,o.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"1-2"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-orange-200 rounded"}),(0,o.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"3-4"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-red-200 rounded"}),(0,o.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"5-6"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-red-400 rounded"}),(0,o.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"7-8"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,o.jsx)("div",{className:"w-4 h-4 bg-red-600 rounded"}),(0,o.jsx)("span",{className:"text-xs text-gray-600 dark:text-gray-400",children:"9-10"})]})]})]})]}),S&&E&&(0,o.jsx)(c,{regionId:E,regionName:(null===(d=H[E])||void 0===d?void 0:d.name)||E,currentPainLevel:A[E]||0,currentDetails:C[E]||{},onSave:(e,t,s)=>{const a=[...j],i=(0,r.A)({},A),l=(0,r.A)({},C);if(t>0)a.includes(e)||a.push(e),i[e]=t,l[e]=s;else{const t=a.indexOf(e);t>-1&&a.splice(t,1),delete i[e],delete l[e]}N(a),w(i),M(l),L(!1)},onClose:()=>L(!1)})]})}}}]);
//# sourceMappingURL=2770.c01d04a2.chunk.js.map