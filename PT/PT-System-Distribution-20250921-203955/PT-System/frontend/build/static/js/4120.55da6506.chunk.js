"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[4120],{4120:(e,a,t)=>{t.d(a,{A:()=>h});var r=t(2555),s=t(5043),l=t(3216),i=t(7921),n=t(4528),o=t(3768),d=t(579);const c=e=>{var a,t,r,s,l,n;let{formData:o,handleInputChange:c,errors:m,originalPlanOfCare:x}=e;const{t:g}=(0,i.o)();return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,d.jsx)("i",{className:"fas fa-file-alt text-blue-600 dark:text-blue-400 mr-2"}),g("documentInformation","Document Information")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("documentNumber","Document Number")}),(0,d.jsx)("input",{type:"text",value:o.documentNumber,onChange:e=>c("documentNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"QP-"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("issueDate","Issue Date")}),(0,d.jsx)("input",{type:"date",value:o.issueDate,onChange:e=>c("issueDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("version","Version")}),(0,d.jsx)("input",{type:"text",value:o.version,onChange:e=>c("version",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"01"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:g("reviewNumber","Review Number")}),(0,d.jsx)("input",{type:"text",value:o.reviewNumber,onChange:e=>c("reviewNumber",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"01"})]})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,d.jsx)("i",{className:"fas fa-user text-green-600 dark:text-green-400 mr-2"}),g("patientInformation","Patient Information")]}),x&&(0,d.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-history text-blue-600 dark:text-blue-400 mr-2"}),g("originalPlanReference","Original Plan of Care Reference")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"font-medium text-blue-800 dark:text-blue-200",children:[g("originalPlanDate","Original Plan Date"),":"]}),(0,d.jsx)("span",{className:"ml-2 text-blue-700 dark:text-blue-300",children:x.createdDate})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"font-medium text-blue-800 dark:text-blue-200",children:[g("shortTermWeeks","Short Term Weeks"),":"]}),(0,d.jsx)("span",{className:"ml-2 text-blue-700 dark:text-blue-300",children:(null===(a=x.shortTermGoals)||void 0===a?void 0:a.weeks)||"N/A"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"font-medium text-blue-800 dark:text-blue-200",children:[g("longTermWeeks","Long Term Weeks"),":"]}),(0,d.jsx)("span",{className:"ml-2 text-blue-700 dark:text-blue-300",children:(null===(t=x.longTermGoals)||void 0===t?void 0:t.weeks)||"N/A"})]})]}),(0,d.jsxs)("div",{className:"mt-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2",children:[g("originalGoals","Original Goals"),":"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:[g("shortTermGoals","Short Term Goals"),":"]}),(0,d.jsx)("ul",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1 space-y-1",children:(null===(r=x.shortTermGoals)||void 0===r||null===(s=r.goals)||void 0===s?void 0:s.map((e,a)=>(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"mr-1",children:"\u2022"}),(0,d.jsx)("span",{children:e})]},a)))||(0,d.jsx)("li",{children:g("noGoalsRecorded","No goals recorded")})})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:[g("longTermGoals","Long Term Goals"),":"]}),(0,d.jsx)("ul",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1 space-y-1",children:(null===(l=x.longTermGoals)||void 0===l||null===(n=l.goals)||void 0===n?void 0:n.map((e,a)=>(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsx)("span",{className:"mr-1",children:"\u2022"}),(0,d.jsx)("span",{children:e})]},a)))||(0,d.jsx)("li",{children:g("noGoalsRecorded","No goals recorded")})})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[g("patientName","Patient Name")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:o.patientName,onChange:e=>c("patientName",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(m.patientName?"border-red-500":"border-gray-300"),placeholder:g("enterPatientName","Enter patient name")}),m.patientName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:m.patientName})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[g("mrNumber","MR #")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:o.mrNumber,onChange:e=>c("mrNumber",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(m.mrNumber?"border-red-500":"border-gray-300"),placeholder:g("enterMRNumber","Enter MR number")}),m.mrNumber&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:m.mrNumber})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[g("diagnosis","Diagnosis")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("textarea",{value:o.diagnosis,onChange:e=>c("diagnosis",e.target.value),rows:3,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(m.diagnosis?"border-red-500":"border-gray-300"),placeholder:g("enterDiagnosis","Enter diagnosis")}),m.diagnosis&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:m.diagnosis})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[g("lastRecertificationDate","Last Re-certification Date")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"date",value:o.lastRecertificationDate,onChange:e=>c("lastRecertificationDate",e.target.value),max:(new Date).toISOString().split("T")[0],className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(m.lastRecertificationDate?"border-red-500":"border-gray-300")}),m.lastRecertificationDate&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:m.lastRecertificationDate})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[g("physician","Physician")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:o.physician,onChange:e=>c("physician",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(m.physician?"border-red-500":"border-gray-300"),placeholder:g("enterPhysicianName","Enter physician name")}),m.physician&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:m.physician})]})]})]})]})},m=e=>{let{formData:a,handleInputChange:t}=e;const{t:r}=(0,i.o)(),s=[{key:"painAssessment",label:r("painAssessment","Pain Assessment"),icon:"fas fa-exclamation-triangle",color:"red",placeholder:r("painAssessmentPlaceholder","Document pain levels, location, triggers, and improvements since last assessment")},{key:"rangeOfMotion",label:r("rangeOfMotion","Range of Motion"),icon:"fas fa-arrows-alt",color:"blue",placeholder:r("romPlaceholder","Document ROM measurements, improvements, limitations, and functional impact")},{key:"muscleTonePower",label:r("muscleTonePower","Muscle Tone/Power"),icon:"fas fa-dumbbell",color:"green",placeholder:r("muscleTonePlaceholder","Document muscle strength grades, tone changes, and functional strength improvements")},{key:"balanceGait",label:r("balanceGait","Balance/Gait"),icon:"fas fa-walking",color:"purple",placeholder:r("balanceGaitPlaceholder","Document balance assessments, gait patterns, stability, and mobility improvements")},{key:"adls",label:r("adls","ADL's"),icon:"fas fa-home",color:"orange",placeholder:r("adlsPlaceholder","Document activities of daily living, independence level, and functional improvements")},{key:"homeExerciseProgram",label:r("homeExerciseProgram","Home Exercise Program"),icon:"fas fa-home",color:"teal",placeholder:r("homeExercisePlaceholder","Document compliance, effectiveness, modifications, and patient/family understanding")},{key:"riskFactors",label:r("riskFactors","Risk Factors"),icon:"fas fa-shield-alt",color:"yellow",placeholder:r("riskFactorsPlaceholder","Document safety concerns, fall risk, environmental hazards, and risk mitigation strategies")}];return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,d.jsx)("i",{className:"fas fa-chart-line text-indigo-600 dark:text-indigo-400 mr-2"}),r("reassessmentSummaryProgress","Reassessment and Summary of Progress")]}),(0,d.jsxs)("div",{className:"mb-4 p-4 bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-indigo-900 dark:text-indigo-100 mb-2",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-indigo-600 dark:text-indigo-400 mr-2"}),r("progressAchievedFromPreviousGoals","Progress achieved from previously stated goals")]}),(0,d.jsx)("p",{className:"text-sm text-indigo-700 dark:text-indigo-300",children:r("progressSummaryInstructions","Document the patient's progress in each area since the last assessment. Include specific measurements, functional improvements, and any setbacks or challenges encountered.")})]}),(0,d.jsx)("div",{className:"space-y-6",children:s.map(e=>(0,d.jsxs)("div",{className:"bg-".concat(e.color,"-50 dark:bg-").concat(e.color,"-900/20 border border-").concat(e.color,"-200 dark:border-").concat(e.color,"-800 rounded-lg p-4"),children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-".concat(e.color,"-800 dark:text-").concat(e.color,"-200 mb-2"),children:[(0,d.jsx)("i",{className:"".concat(e.icon," text-").concat(e.color,"-600 dark:text-").concat(e.color,"-400 mr-2")}),e.label]}),(0,d.jsx)("textarea",{value:a.progressSummary[e.key],onChange:a=>t("progressSummary.".concat(e.key),a.target.value),rows:4,className:"w-full px-3 py-2 border border-".concat(e.color,"-300 rounded-lg focus:ring-2 focus:ring-").concat(e.color,"-500 focus:border-").concat(e.color,"-500 dark:bg-").concat(e.color,"-800 dark:border-").concat(e.color,"-600 dark:text-white"),placeholder:e.placeholder}),(0,d.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-xs",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1 text-".concat(e.color,"-600 dark:text-").concat(e.color,"-400"),children:[(0,d.jsx)("i",{className:"fas fa-lightbulb"}),(0,d.jsx)("span",{children:r("includeSpecificMeasurements","Include specific measurements")})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1 text-".concat(e.color,"-600 dark:text-").concat(e.color,"-400"),children:[(0,d.jsx)("i",{className:"fas fa-chart-bar"}),(0,d.jsx)("span",{children:r("documentFunctionalChanges","Document functional changes")})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1 text-".concat(e.color,"-600 dark:text-").concat(e.color,"-400"),children:[(0,d.jsx)("i",{className:"fas fa-calendar-alt"}),(0,d.jsx)("span",{children:r("compareToBaseline","Compare to baseline")})]})]})]},e.key))}),(0,d.jsxs)("div",{className:"mt-6 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-clipboard-list text-gray-600 dark:text-gray-400 mr-2"}),r("progressDocumentationGuidelines","Progress Documentation Guidelines"),":"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2",children:[r("whatToInclude","What to Include"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",r("objectiveMeasurements","Objective measurements (ROM, strength grades)")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("functionalImprovements","Functional improvements or declines")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("patientReportedOutcomes","Patient-reported outcomes")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("complianceWithTreatment","Compliance with treatment plan")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("barriersToPogress","Barriers to progress")]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2",children:[r("documentationTips","Documentation Tips"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",r("useSpecificNumbers","Use specific numbers and measurements")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("compareToInitialAssessment","Compare to initial assessment findings")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("noteAnyComplications","Note any complications or setbacks")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("documentPatientMotivation","Document patient motivation and participation")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("includeCaregiversObservations","Include caregiver's observations")]})]})]})]})]}),(0,d.jsxs)("div",{className:"mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-tools text-blue-600 dark:text-blue-400 mr-2"}),r("quickAssessmentTools","Quick Assessment Tools"),":"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"bg-blue-100 dark:bg-blue-800 rounded-lg p-3 mb-2",children:(0,d.jsx)("i",{className:"fas fa-thermometer-half text-blue-600 dark:text-blue-400 text-2xl"})}),(0,d.jsx)("h5",{className:"text-xs font-semibold text-blue-800 dark:text-blue-200",children:r("painScale","Pain Scale")}),(0,d.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:r("painScaleDescription","0-10 Numeric Rating Scale")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"bg-blue-100 dark:bg-blue-800 rounded-lg p-3 mb-2",children:(0,d.jsx)("i",{className:"fas fa-balance-scale text-blue-600 dark:text-blue-400 text-2xl"})}),(0,d.jsx)("h5",{className:"text-xs font-semibold text-blue-800 dark:text-blue-200",children:r("bergBalanceScale","Berg Balance Scale")}),(0,d.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:r("bergBalanceDescription","14-item balance assessment")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"bg-blue-100 dark:bg-blue-800 rounded-lg p-3 mb-2",children:(0,d.jsx)("i",{className:"fas fa-ruler text-blue-600 dark:text-blue-400 text-2xl"})}),(0,d.jsx)("h5",{className:"text-xs font-semibold text-blue-800 dark:text-blue-200",children:r("goniometry","Goniometry")}),(0,d.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400",children:r("goniometryDescription","ROM measurements in degrees")})]})]})]})]})},x=e=>{let{formData:a,handleInputChange:t,handleArrayChange:r,addGoal:s,removeGoal:l,errors:n,originalPlanOfCare:o}=e;const{t:c}=(0,i.o)(),m=c("goalNotAchievedReason","The Goal was not achieved due to [insert reason], and The treatment plan will be adjusted to address the identified barriers, and the goals will be updated accordingly to better support the patient's Progress."),x=(e,i,x,g)=>{var u;const h=a[e],b="Yes"===h.achieved,p="No"===h.achieved,y=(null===o||void 0===o||null===(u=o[e])||void 0===u?void 0:u.goals)||[],f="shortTermGoals"===e?"shortTerm":"longTerm";return(0,d.jsxs)("div",{className:"bg-".concat(g,"-50 dark:bg-").concat(g,"-900/20 border border-").concat(g,"-200 dark:border-").concat(g,"-800 rounded-lg p-6"),children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-".concat(g,"-900 dark:text-").concat(g,"-100 mb-4"),children:[(0,d.jsx)("i",{className:"".concat(x," text-").concat(g,"-600 dark:text-").concat(g,"-400 mr-2")}),i]}),y.length>0&&(0,d.jsxs)("div",{className:"mb-6 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,d.jsxs)("h4",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2",children:[(0,d.jsx)("i",{className:"fas fa-history text-gray-600 dark:text-gray-400 mr-1"}),c("originalGoalsReference","Original Goals Reference"),":"]}),(0,d.jsx)("ul",{className:"text-sm text-gray-700 dark:text-gray-300 space-y-1",children:y.map((e,a)=>(0,d.jsxs)("li",{className:"flex items-start",children:[(0,d.jsxs)("span",{className:"mr-2 text-gray-500",children:[a+1,"."]}),(0,d.jsx)("span",{children:e})]},a))})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-".concat(g,"-800 dark:text-").concat(g,"-200 mb-3"),children:[c("achievedGoals","Achieved ".concat(i))," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("div",{className:"flex space-x-6",children:[(0,d.jsxs)("label",{className:"flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ".concat(b?"border-green-500 bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200":"border-gray-300 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-500"),children:[(0,d.jsx)("input",{type:"radio",name:"".concat(e,"_achieved"),value:"Yes",checked:b,onChange:a=>t("".concat(e,".achieved"),a.target.value),className:"sr-only"}),(0,d.jsx)("div",{className:"w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ".concat(b?"border-green-500 bg-green-500":"border-gray-300 dark:border-gray-600"),children:b&&(0,d.jsx)("div",{className:"w-2 h-2 rounded-full bg-white"})}),(0,d.jsx)("span",{className:"font-medium",children:c("yes","Yes")})]}),(0,d.jsxs)("label",{className:"flex items-center p-3 border-2 rounded-lg cursor-pointer transition-colors ".concat(p?"border-red-500 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200":"border-gray-300 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-500"),children:[(0,d.jsx)("input",{type:"radio",name:"".concat(e,"_achieved"),value:"No",checked:p,onChange:a=>{t("".concat(e,".achieved"),a.target.value),"No"!==a.target.value||h.reasonNotAchieved||t("".concat(e,".reasonNotAchieved"),m)},className:"sr-only"}),(0,d.jsx)("div",{className:"w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ".concat(p?"border-red-500 bg-red-500":"border-gray-300 dark:border-gray-600"),children:p&&(0,d.jsx)("div",{className:"w-2 h-2 rounded-full bg-white"})}),(0,d.jsx)("span",{className:"font-medium",children:c("no","No")})]})]}),n["".concat(f,"Achieved")]&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-2",children:n["".concat(f,"Achieved")]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-".concat(g,"-800 dark:text-").concat(g,"-200 mb-1"),children:c("weeks","Weeks")}),(0,d.jsx)("input",{type:"number",min:"1",max:"52",value:h.weeks,onChange:a=>t("".concat(e,".weeks"),a.target.value),className:"w-32 px-3 py-2 border border-".concat(g,"-300 rounded-lg focus:ring-2 focus:ring-").concat(g,"-500 focus:border-").concat(g,"-500 dark:bg-").concat(g,"-800 dark:border-").concat(g,"-600 dark:text-white"),placeholder:"1-52"}),(0,d.jsx)("p",{className:"text-xs text-".concat(g,"-600 dark:text-").concat(g,"-400 mt-1"),children:c("weeksHint","Number of weeks for goal achievement")})]}),p&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-".concat(g,"-800 dark:text-").concat(g,"-200 mb-1"),children:[c("reasonNotAchieved","Reason Not Achieved")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("textarea",{value:h.reasonNotAchieved,onChange:a=>t("".concat(e,".reasonNotAchieved"),a.target.value),rows:4,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-".concat(g,"-500 focus:border-").concat(g,"-500 dark:bg-").concat(g,"-800 dark:border-").concat(g,"-600 dark:text-white ").concat(n["".concat(f,"Reason")]?"border-red-500":"border-".concat(g,"-300")),placeholder:m}),n["".concat(f,"Reason")]&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:n["".concat(f,"Reason")]})]}),p&&(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-".concat(g,"-800 dark:text-").concat(g,"-200"),children:[c("updatedGoals","Updated Goals")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsxs)("button",{type:"button",onClick:()=>s(e),className:"px-3 py-1 bg-".concat(g,"-600 text-white rounded-lg hover:bg-").concat(g,"-700 transition-colors text-sm"),children:[(0,d.jsx)("i",{className:"fas fa-plus mr-1"}),c("addGoal","Add Goal")]})]}),(0,d.jsx)("div",{className:"space-y-3",children:h.updatedGoals.map((a,t)=>(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsxs)("span",{className:"mt-2 text-sm font-medium text-".concat(g,"-700 dark:text-").concat(g,"-300 min-w-[20px]"),children:[t+1,"."]}),(0,d.jsx)("textarea",{value:a,onChange:a=>r("".concat(e,".updatedGoals"),t,a.target.value),rows:2,className:"flex-1 px-3 py-2 border border-".concat(g,"-300 rounded-lg focus:ring-2 focus:ring-").concat(g,"-500 focus:border-").concat(g,"-500 dark:bg-").concat(g,"-800 dark:border-").concat(g,"-600 dark:text-white"),placeholder:c("goalPlaceholder","Patient will...")}),h.updatedGoals.length>1&&(0,d.jsx)("button",{type:"button",onClick:()=>l(e,t),className:"mt-2 p-1 text-red-600 hover:text-red-800 transition-colors",children:(0,d.jsx)("i",{className:"fas fa-trash text-sm"})})]},t))}),n["".concat(f,"UpdatedGoals")]&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-2",children:n["".concat(f,"UpdatedGoals")]}),(0,d.jsxs)("div",{className:"mt-3 p-3 bg-".concat(g,"-100 dark:bg-").concat(g,"-900/30 rounded-lg"),children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-".concat(g,"-800 dark:text-").concat(g,"-200 mb-1"),children:[(0,d.jsx)("i",{className:"fas fa-lightbulb mr-1"}),c("smartGoalsReminder","SMART Goals Reminder"),":"]}),(0,d.jsx)("p",{className:"text-xs text-".concat(g,"-700 dark:text-").concat(g,"-300"),children:c("smartGoalsDescription","Goals should be Specific, Measurable, Achievable, Relevant, and Time-bound")})]})]}),b&&(0,d.jsxs)("div",{className:"p-4 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:c("goalsAchievedSuccess","Congratulations! Goals have been successfully achieved.")})]}),(0,d.jsx)("p",{className:"text-xs text-green-700 dark:text-green-300 mt-1",children:c("goalsAchievedNext","Consider setting new goals or progressing to more advanced objectives.")})]})]})};return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,d.jsx)("i",{className:"fas fa-bullseye text-purple-600 dark:text-purple-400 mr-2"}),c("goalsOfTreatment","Goals of Treatment")]}),(0,d.jsxs)("div",{className:"space-y-8",children:[x("shortTermGoals",c("shortTermGoals","Short Term Goals"),"fas fa-flag","blue"),x("longTermGoals",c("longTermGoals","Long Term Goals"),"fas fa-mountain","green")]}),(0,d.jsxs)("div",{className:"mt-8 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-yellow-600 dark:text-yellow-400 mr-2"}),c("goalsAssessmentGuidelines","Goals Assessment Guidelines"),":"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2",children:[c("achievedGoalsNext","If Goals Are Achieved"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-yellow-600 dark:text-yellow-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",c("documentSpecificAchievements","Document specific achievements")]}),(0,d.jsxs)("li",{children:["\u2022 ",c("considerAdvancedGoals","Consider setting more advanced goals")]}),(0,d.jsxs)("li",{children:["\u2022 ",c("planMaintenanceStrategies","Plan maintenance strategies")]}),(0,d.jsxs)("li",{children:["\u2022 ",c("prepareForDischarge","Prepare for discharge planning")]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2",children:[c("notAchievedGoalsNext","If Goals Are Not Achieved"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-yellow-600 dark:text-yellow-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",c("identifySpecificBarriers","Identify specific barriers")]}),(0,d.jsxs)("li",{children:["\u2022 ",c("modifyTreatmentApproach","Modify treatment approach")]}),(0,d.jsxs)("li",{children:["\u2022 ",c("adjustGoalExpectations","Adjust goal expectations")]}),(0,d.jsxs)("li",{children:["\u2022 ",c("increaseFrequencyIntensity","Consider frequency/intensity changes")]})]})]})]})]})]})},g=e=>{let{formData:a,handleInputChange:t}=e;const{t:r}=(0,i.o)(),s=[{key:"painControl",title:r("painControl","Pain Control"),icon:"fas fa-hand-holding-medical",color:"red",options:[{key:"us",label:r("us","US")},{key:"laser",label:r("laser","LASER")},{key:"tens",label:r("tens","TENS")},{key:"thermal",label:r("thermal","Thermal")}]},{key:"reduceSwelling",title:r("reduceSwellingEdema","Reduce Swelling/Edema"),icon:"fas fa-snowflake",color:"blue",options:[{key:"cryotherapy",label:r("cryotherapy","Cryotherapy")},{key:"hvc",label:r("hvc","HVC")},{key:"compression",label:r("compression","Compression")}]},{key:"improveROM",title:r("improveROM","Improve ROM"),icon:"fas fa-arrows-alt",color:"green",options:[{key:"prom",label:r("prom","PROM")},{key:"mobilization",label:r("mobilization","Mobilization")},{key:"met",label:r("met","MET")}]},{key:"improveFlexibility",title:r("improveFlexibility","Improve Flexibility"),icon:"fas fa-expand-arrows-alt",color:"purple",options:[{key:"stretching",label:r("stretching","Stretching")},{key:"thermal",label:r("thermal","Thermal")},{key:"myofascialRelease",label:r("myofascialRelease","Myofascial release")}]},{key:"muscleStrengthening",title:r("muscleStrengthening","Muscle Strengthening"),icon:"fas fa-dumbbell",color:"orange",options:[{key:"isometric",label:r("isometric","Isometric")},{key:"activeAssisted",label:r("activeAssisted","Active Assisted")},{key:"activeResisted",label:r("activeResisted","Active Resisted")},{key:"coreStrengthening",label:r("coreStrengthening","Core Strengthening")},{key:"plyometrics",label:r("plyometrics","Plyometrics")},{key:"fes",label:r("fes","FES")},{key:"pnf",label:r("pnf","PNF")}]},{key:"posturalCorrection",title:r("posturalCorrection","Postural Correction"),icon:"fas fa-user-check",color:"indigo",options:[{key:"properBodyMechanics",label:r("properBodyMechanics","Proper Body Mechanics")},{key:"ergonomics",label:r("ergonomics","Ergonomics")},{key:"tiltTable",label:r("tiltTable","Tilt table")}]},{key:"improveBalance",title:r("improveBalanceCoordination","Improve Balance and Coordination"),icon:"fas fa-balance-scale",color:"teal",options:[{key:"frenkelsEx",label:r("frenkelsEx","Frenkel's Ex")},{key:"balanceBoard",label:r("balanceBoard","Balance Board")},{key:"agilityEx",label:r("agilityEx","Agility Ex's")},{key:"proprioceptionTraining",label:r("proprioceptionTraining","Proprioception Training")},{key:"lumbopelvicRhythm",label:r("lumbopelvicRhythm","Lumbopelvic Rhythm")}]},{key:"improveEndurance",title:r("improveEndurance","Improve Endurance"),icon:"fas fa-heartbeat",color:"pink",options:[{key:"aerobicEx",label:r("aerobicEx","Aerobic Ex's")},{key:"bicycle",label:r("bicycle","Bicycle")},{key:"treadmill",label:r("treadmill","Treadmill")}]}],l=[{value:"FWB",label:r("fwb","FWB (Full Weight Bearing)")},{value:"PWB",label:r("pwb","PWB (Partial Weight Bearing)")},{value:"WB",label:r("wb","WB (Weight Bearing)")}];return(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:[(0,d.jsx)("i",{className:"fas fa-clipboard-list text-blue-600 dark:text-blue-400 mr-2"}),r("treatmentPlan","Treatment Plan")]}),(0,d.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-blue-900 dark:text-blue-100 mb-2",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2"}),r("treatmentPlanInstructions","Treatment Plan Instructions")]}),(0,d.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:r("treatmentPlanDescription","Select all applicable treatment interventions. This updated plan should reflect the current needs based on the reassessment findings and goal achievement status.")})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[s.map(e=>(0,d.jsxs)("div",{className:"bg-".concat(e.color,"-50 dark:bg-").concat(e.color,"-900/20 border border-").concat(e.color,"-200 dark:border-").concat(e.color,"-800 rounded-lg p-4"),children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-".concat(e.color,"-900 dark:text-").concat(e.color,"-100 mb-3"),children:[(0,d.jsx)("i",{className:"".concat(e.icon," text-").concat(e.color,"-600 dark:text-").concat(e.color,"-400 mr-2")}),e.title]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2",children:e.options.map(r=>(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:a.treatmentPlan[e.key][r.key],onChange:a=>t("treatmentPlan.".concat(e.key,".").concat(r.key),a.target.checked),className:"mr-2 text-".concat(e.color,"-600 focus:ring-").concat(e.color,"-500")}),(0,d.jsx)("span",{className:"text-sm text-".concat(e.color,"-800 dark:text-").concat(e.color,"-200"),children:r.label})]},r.key))})]},e.key)),(0,d.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-yellow-900 dark:text-yellow-100 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-walking text-yellow-600 dark:text-yellow-400 mr-2"}),r("gaitTraining","Gait Training")]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:a.treatmentPlan.gaitTraining.normalGaitPattern,onChange:e=>t("treatmentPlan.gaitTraining.normalGaitPattern",e.target.checked),className:"mr-2 text-yellow-600 focus:ring-yellow-500"}),(0,d.jsx)("span",{className:"text-sm text-yellow-800 dark:text-yellow-200",children:r("normalGaitPattern","Normal Gait Pattern")})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"flex items-center mb-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:a.treatmentPlan.gaitTraining.weightBearing,onChange:e=>t("treatmentPlan.gaitTraining.weightBearing",e.target.checked),className:"mr-2 text-yellow-600 focus:ring-yellow-500"}),(0,d.jsx)("span",{className:"text-sm font-medium text-yellow-800 dark:text-yellow-200",children:r("weightBearing","Weight Bearing")})]}),a.treatmentPlan.gaitTraining.weightBearing&&(0,d.jsx)("div",{className:"ml-6 space-y-2",children:l.map(e=>(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"radio",name:"weightBearingType",value:e.value,checked:a.treatmentPlan.gaitTraining.weightBearingType===e.value,onChange:e=>t("treatmentPlan.gaitTraining.weightBearingType",e.target.value),className:"mr-2 text-yellow-600 focus:ring-yellow-500"}),(0,d.jsx)("span",{className:"text-sm text-yellow-700 dark:text-yellow-300",children:e.label})]},e.value))})]})]})]}),(0,d.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",children:(0,d.jsxs)("label",{className:"flex items-center",children:[(0,d.jsx)("input",{type:"checkbox",checked:a.treatmentPlan.homeInstructions,onChange:e=>t("treatmentPlan.homeInstructions",e.target.checked),className:"mr-3 text-green-600 focus:ring-green-500"}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("span",{className:"text-md font-semibold text-green-900 dark:text-green-100",children:[(0,d.jsx)("i",{className:"fas fa-home text-green-600 dark:text-green-400 mr-2"}),r("homeInstructions","Home Instructions")]}),(0,d.jsx)("p",{className:"text-sm text-green-700 dark:text-green-300 mt-1",children:r("homeInstructionsDescription","Provide patient and family with home exercise program and care instructions")})]})]})}),(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[(0,d.jsx)("i",{className:"fas fa-plus-circle text-gray-600 dark:text-gray-400 mr-2"}),r("others","Others")]}),(0,d.jsx)("textarea",{value:a.treatmentPlan.others,onChange:e=>t("treatmentPlan.others",e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white",placeholder:r("othersPlaceholder","Specify any additional treatment interventions not listed above")})]}),(0,d.jsxs)("div",{className:"bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4",children:[(0,d.jsxs)("h3",{className:"text-md font-semibold text-indigo-900 dark:text-indigo-100 mb-4",children:[(0,d.jsx)("i",{className:"fas fa-calendar-alt text-indigo-600 dark:text-indigo-400 mr-2"}),r("treatmentSchedule","Treatment Schedule")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-1",children:r("sessionsPerWeek","Sessions per Week")}),(0,d.jsx)("input",{type:"number",min:"1",max:"7",value:a.treatmentSchedule.sessionsPerWeek,onChange:e=>t("treatmentSchedule.sessionsPerWeek",e.target.value),className:"w-full px-3 py-2 border border-indigo-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-indigo-800 dark:border-indigo-600 dark:text-white",placeholder:"1-7"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-1",children:r("sessionDuration","Session Duration (minutes)")}),(0,d.jsx)("input",{type:"number",min:"15",max:"180",value:a.treatmentSchedule.sessionDuration,onChange:e=>t("treatmentSchedule.sessionDuration",e.target.value),className:"w-full px-3 py-2 border border-indigo-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-indigo-800 dark:border-indigo-600 dark:text-white",placeholder:"15-180"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-1",children:r("totalWeeks","Total Weeks")}),(0,d.jsx)("input",{type:"number",min:"1",max:"52",value:a.treatmentSchedule.totalWeeks,onChange:e=>t("treatmentSchedule.totalWeeks",e.target.value),className:"w-full px-3 py-2 border border-indigo-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-indigo-800 dark:border-indigo-600 dark:text-white",placeholder:"1-52"})]})]})]})]}),(0,d.jsxs)("div",{className:"mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-lightbulb text-yellow-600 dark:text-yellow-400 mr-2"}),r("treatmentPlanGuidelines","Treatment Plan Guidelines"),":"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2",children:[r("planningConsiderations","Planning Considerations"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-yellow-600 dark:text-yellow-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",r("basedOnReassessmentFindings","Based on reassessment findings")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("alignWithUpdatedGoals","Align with updated goals")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("considerPatientPreferences","Consider patient preferences")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("accountForProgressBarriers","Account for progress barriers")]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-yellow-700 dark:text-yellow-300 mb-2",children:[r("evidenceBasedPractice","Evidence-Based Practice"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-yellow-600 dark:text-yellow-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",r("useCurrentBestEvidence","Use current best evidence")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("integrateClinicianExpertise","Integrate clinician expertise")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("respectPatientValues","Respect patient values")]}),(0,d.jsxs)("li",{children:["\u2022 ",r("monitorOutcomes","Monitor outcomes regularly")]})]})]})]})]})]})},u=e=>{let{formData:a,handleInputChange:t,errors:r}=e;const{t:s}=(0,i.o)();return(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4",children:[(0,d.jsx)("i",{className:"fas fa-handshake text-blue-600 dark:text-blue-400 mr-2"}),s("planReview","Plan Review")]}),(0,d.jsxs)("label",{className:"flex items-start",children:[(0,d.jsx)("input",{type:"checkbox",checked:a.planReviewedWithPatient,onChange:e=>t("planReviewedWithPatient",e.target.checked),className:"mt-1 mr-3 text-blue-600 focus:ring-blue-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:s("planOfCareReviewedWithPatient","Plan of Care Reviewed with Patient")}),(0,d.jsx)("p",{className:"text-xs text-blue-600 dark:text-blue-400 mt-1",children:s("planReviewDescription","Confirm that the updated plan of care has been discussed and reviewed with the patient and/or family members")})]})]}),a.planReviewedWithPatient&&(0,d.jsxs)("div",{className:"mt-4 p-3 bg-green-100 dark:bg-green-900/30 border border-green-300 dark:border-green-700 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("i",{className:"fas fa-check-circle text-green-600 dark:text-green-400 mr-2"}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:s("planReviewConfirmed","Plan review confirmed with patient/family")})]}),(0,d.jsx)("p",{className:"text-xs text-green-700 dark:text-green-300 mt-1",children:s("planReviewConfirmedDescription","The patient and/or family have been informed of the updated treatment plan, goals, and expected outcomes.")})]})]}),(0,d.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-green-600 dark:text-green-400 mr-2"}),s("therapistSignature","Therapist Signature")]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[s("therapistName","Therapist Name")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:a.therapistSignature.name,onChange:e=>t("therapistSignature.name",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.therapistName?"border-red-500":"border-gray-300"),placeholder:s("enterTherapistName","Enter therapist full name")}),r.therapistName&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.therapistName})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:[s("badgeNumber","Badge No.")," ",(0,d.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,d.jsx)("input",{type:"text",value:a.therapistSignature.badgeNo,onChange:e=>t("therapistSignature.badgeNo",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ".concat(r.therapistBadge?"border-red-500":"border-gray-300"),placeholder:s("enterBadgeNumber","Enter badge number")}),r.therapistBadge&&(0,d.jsx)("p",{className:"text-red-500 text-sm mt-1",children:r.therapistBadge})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:s("date","Date")}),(0,d.jsx)("input",{type:"date",value:a.therapistSignature.date,onChange:e=>t("therapistSignature.date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]})]}),(0,d.jsxs)("div",{className:"mt-6",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:s("digitalSignature","Digital Signature")}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-green-300 dark:border-green-600 rounded-lg p-8 text-center",children:[(0,d.jsx)("i",{className:"fas fa-signature text-3xl text-green-400 mb-2"}),(0,d.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:s("therapistSignaturePlaceholder","Therapist digital signature will be captured here")}),(0,d.jsxs)("button",{type:"button",className:"mt-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-pen mr-2"}),s("addTherapistSignature","Add Therapist Signature")]})]})]})]}),(0,d.jsxs)("div",{className:"bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold text-purple-900 dark:text-purple-100 mb-4",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-purple-600 dark:text-purple-400 mr-2"}),s("physicianReview","Physician Review")]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2",children:s("physicianStatement","Physician Statement")}),(0,d.jsx)("textarea",{value:a.physicianReview.statement,onChange:e=>t("physicianReview.statement",e.target.value),rows:3,className:"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white",placeholder:s("physicianStatementPlaceholder","Have reviewed this plan of care and re-certify a continuing need for services.")}),(0,d.jsx)("p",{className:"text-xs text-purple-600 dark:text-purple-400 mt-1",children:s("physicianStatementHint","Standard statement confirming physician review and re-certification")})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-1",children:s("physicianSignature","Physician Signature")}),(0,d.jsx)("input",{type:"text",value:a.physicianReview.signature,onChange:e=>t("physicianReview.signature",e.target.value),className:"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white",placeholder:s("physicianSignaturePlaceholder","Physician signature")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-1",children:s("physicianBadgeNumber","Physician Badge No.")}),(0,d.jsx)("input",{type:"text",value:a.physicianReview.badgeNo,onChange:e=>t("physicianReview.badgeNo",e.target.value),className:"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white",placeholder:s("physicianBadgeNumber","Enter physician badge number")})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-1",children:s("physicianDate","Physician Date")}),(0,d.jsx)("input",{type:"date",value:a.physicianReview.date,onChange:e=>t("physicianReview.date",e.target.value),className:"w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-purple-800 dark:border-purple-600 dark:text-white"})]})]}),(0,d.jsxs)("div",{className:"mt-6",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-purple-800 dark:text-purple-200 mb-2",children:s("physicianDigitalSignature","Physician Digital Signature")}),(0,d.jsxs)("div",{className:"border-2 border-dashed border-purple-300 dark:border-purple-600 rounded-lg p-8 text-center",children:[(0,d.jsx)("i",{className:"fas fa-signature text-3xl text-purple-400 mb-2"}),(0,d.jsx)("p",{className:"text-sm text-purple-600 dark:text-purple-400",children:s("physicianSignaturePlaceholder","Physician digital signature will be captured here")}),(0,d.jsxs)("button",{type:"button",className:"mt-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-pen mr-2"}),s("addPhysicianSignature","Add Physician Signature")]})]})]}),(0,d.jsx)("div",{className:"mt-6 p-4 bg-yellow-100 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 rounded-lg",children:(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"text-sm font-semibold text-yellow-800 dark:text-yellow-200",children:s("recertificationRequired","Re-certification Required")}),(0,d.jsx)("p",{className:"text-xs text-yellow-700 dark:text-yellow-300 mt-1",children:s("recertificationDescription","Physician signature is required to re-certify the continuing need for physical therapy services. This confirms medical necessity and supports insurance coverage.")})]})]})})]}),(0,d.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:[(0,d.jsxs)("h4",{className:"text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3",children:[(0,d.jsx)("i",{className:"fas fa-info-circle text-gray-600 dark:text-gray-400 mr-2"}),s("signatureGuidelines","Signature Guidelines"),":"]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2",children:[s("therapistResponsibilities","Therapist Responsibilities"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",s("completeAllSections","Complete all required sections")]}),(0,d.jsxs)("li",{children:["\u2022 ",s("reviewWithPatient","Review plan with patient/family")]}),(0,d.jsxs)("li",{children:["\u2022 ",s("provideDigitalSignature","Provide digital signature")]}),(0,d.jsxs)("li",{children:["\u2022 ",s("submitForPhysicianReview","Submit for physician review")]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h5",{className:"text-xs font-semibold text-gray-700 dark:text-gray-300 mb-2",children:[s("physicianResponsibilities","Physician Responsibilities"),":"]}),(0,d.jsxs)("ul",{className:"text-xs text-gray-600 dark:text-gray-400 space-y-1",children:[(0,d.jsxs)("li",{children:["\u2022 ",s("reviewReassessmentFindings","Review reassessment findings")]}),(0,d.jsxs)("li",{children:["\u2022 ",s("validateTreatmentPlan","Validate updated treatment plan")]}),(0,d.jsxs)("li",{children:["\u2022 ",s("confirmMedicalNecessity","Confirm medical necessity")]}),(0,d.jsxs)("li",{children:["\u2022 ",s("provideRecertification","Provide re-certification signature")]})]})]})]})]})]})},h=e=>{let{patientId:a,planOfCareId:t,patientData:h,fromPatientProfile:b,initialData:p,onSave:y,onCancel:f}=e;const{t:k}=(0,i.o)(),{user:j}=(0,n.A)(),N=(0,l.Zp)(),{patientId:v,planOfCareId:w,reassessmentId:P}=(0,l.g)(),[T,R]=(0,s.useState)(!1),[S,C]=(0,s.useState)({}),[G,A]=(0,s.useState)(null),[D,B]=(0,s.useState)(null),I=a||v,E=t||w,[O,M]=(0,s.useState)({documentNumber:"QP-",issueDate:(new Date).toISOString().split("T")[0],version:"01",reviewNumber:"01",patientName:"",mrNumber:"",diagnosis:"",lastRecertificationDate:"",physician:"",progressSummary:{painAssessment:"",rangeOfMotion:"",muscleTonePower:"",balanceGait:"",adls:"",homeExerciseProgram:"",riskFactors:""},shortTermGoals:{achieved:"",weeks:"",reasonNotAchieved:"",updatedGoals:["","","",""]},longTermGoals:{achieved:"",weeks:"",reasonNotAchieved:"",updatedGoals:["","","",""]},treatmentPlan:{painControl:{us:!1,laser:!1,tens:!1,thermal:!1},reduceSwelling:{cryotherapy:!1,hvc:!1,compression:!1},improveROM:{prom:!1,mobilization:!1,met:!1},improveFlexibility:{stretching:!1,thermal:!1,myofascialRelease:!1},muscleStrengthening:{isometric:!1,activeAssisted:!1,activeResisted:!1,coreStrengthening:!1,plyometrics:!1,fes:!1,pnf:!1},posturalCorrection:{properBodyMechanics:!1,ergonomics:!1,tiltTable:!1},improveBalance:{frenkelsEx:!1,balanceBoard:!1,agilityEx:!1,proprioceptionTraining:!1,lumbopelvicRhythm:!1},improveEndurance:{aerobicEx:!1,bicycle:!1,treadmill:!1},gaitTraining:{normalGaitPattern:!1,weightBearing:"",weightBearingType:""},homeInstructions:!1,others:""},treatmentSchedule:{sessionsPerWeek:"",sessionDuration:"",totalWeeks:""},planReviewedWithPatient:!1,therapistSignature:{name:(null===j||void 0===j?void 0:j.name)||"",badgeNo:(null===j||void 0===j?void 0:j.badgeNo)||"",date:(new Date).toISOString().split("T")[0]},physicianReview:{statement:"Have reviewed this plan of care and re-certify a continuing need for services.",signature:"",badgeNo:"",date:""}});(0,s.useEffect)(()=>{I&&(R(!0),setTimeout(()=>{const e={id:I,name:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0639\u0644\u064a",nameEn:"Ahmed Mohammed Ali",mrNumber:"MR-2024-001",dateOfBirth:"2016-03-15",age:8,gender:"male",diagnosis:"Cerebral Palsy"},a={id:E,patientId:I,diagnosis:"Cerebral Palsy with spastic diplegia",physician:"Dr. Sarah Ahmed",createdDate:"2024-01-15",shortTermGoals:{weeks:4,goals:["Patient will improve balance by standing on one foot for 10 seconds","Patient will increase lower extremity strength by 25%","Patient will demonstrate proper transfer techniques"]},longTermGoals:{weeks:12,goals:["Patient will walk 50 meters independently without assistive device","Patient will achieve functional independence in ADLs","Patient will demonstrate improved postural control"]},treatmentPlan:{painControl:{us:!0,tens:!0},muscleStrengthening:{activeResisted:!0,coreStrengthening:!0},improveBalance:{balanceBoard:!0,proprioceptionTraining:!0}}};A(e),B(a),M(t=>(0,r.A)((0,r.A)({},t),{},{patientName:e.nameEn,mrNumber:e.mrNumber,diagnosis:a.diagnosis,physician:a.physician,lastRecertificationDate:a.createdDate,shortTermGoals:(0,r.A)((0,r.A)({},t.shortTermGoals),{},{weeks:a.shortTermGoals.weeks}),longTermGoals:(0,r.A)((0,r.A)({},t.longTermGoals),{},{weeks:a.longTermGoals.weeks}),treatmentPlan:(0,r.A)((0,r.A)({},t.treatmentPlan),a.treatmentPlan)})),R(!1)},500))},[I,E]);const q=(e,a)=>{const t=(0,r.A)({},O);if(e.includes(".")){const r=e.split(".");let s=t;for(let e=0;e<r.length-1;e++)s[r[e]]||(s[r[e]]={}),s=s[r[e]];s[r[r.length-1]]=a}else t[e]=a;M(t),S[e]&&C(a=>(0,r.A)((0,r.A)({},a),{},{[e]:null}))};return T&&!G?(0,d.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,d.jsx)("div",{className:"max-w-6xl mx-auto p-6 bg-white dark:bg-gray-900",children:(0,d.jsxs)("form",{onSubmit:async e=>{if(e.preventDefault(),(()=>{const e={};O.patientName.trim()||(e.patientName=k("patientNameRequired","Patient name is required")),O.mrNumber.trim()||(e.mrNumber=k("mrNumberRequired","MR number is required")),O.diagnosis.trim()||(e.diagnosis=k("diagnosisRequired","Diagnosis is required")),O.lastRecertificationDate||(e.lastRecertificationDate=k("lastRecertDateRequired","Last re-certification date is required")),O.physician.trim()||(e.physician=k("physicianRequired","Physician is required")),O.shortTermGoals.achieved||(e.shortTermAchieved=k("shortTermAchievedRequired","Short term goal achievement status is required")),O.longTermGoals.achieved||(e.longTermAchieved=k("longTermAchievedRequired","Long term goal achievement status is required")),"No"!==O.shortTermGoals.achieved||O.shortTermGoals.reasonNotAchieved.trim()||(e.shortTermReason=k("reasonRequired","Reason is required when goals are not achieved")),"No"!==O.longTermGoals.achieved||O.longTermGoals.reasonNotAchieved.trim()||(e.longTermReason=k("reasonRequired","Reason is required when goals are not achieved")),"No"===O.shortTermGoals.achieved&&(O.shortTermGoals.updatedGoals.some(e=>e.trim())||(e.shortTermUpdatedGoals=k("updatedGoalsRequired","At least one updated goal is required")));"No"===O.longTermGoals.achieved&&(O.longTermGoals.updatedGoals.some(e=>e.trim())||(e.longTermUpdatedGoals=k("updatedGoalsRequired","At least one updated goal is required")));return O.therapistSignature.name.trim()||(e.therapistName=k("therapistNameRequired","Therapist name is required")),O.therapistSignature.badgeNo.trim()||(e.therapistBadge=k("therapistBadgeRequired","Therapist badge number is required")),C(e),0===Object.keys(e).length})()){R(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),y)y(O);else{const e=JSON.parse(localStorage.getItem("reassessmentData")||"[]"),a=(0,r.A)((0,r.A)({},O),{},{id:Date.now(),patientId:I,originalPlanOfCareId:E,createdAt:(new Date).toISOString(),updatedAt:(new Date).toISOString(),createdBy:(null===j||void 0===j?void 0:j.id)||"current-user"});e.push(a),localStorage.setItem("reassessmentData",JSON.stringify(e)),o.Ay.success(k("reassessmentSaved","Reassessment and Updated Plan of Care saved successfully")),N(I?"/patients/".concat(I):"/patients")}}catch(a){console.error("Error saving reassessment:",a),o.Ay.error(k("errorSavingReassessment","Error saving reassessment"))}finally{R(!1)}}else o.Ay.error(k("pleaseFixErrors","Please fix the errors before submitting"))},className:"space-y-8",children:[(0,d.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 pb-4",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[k("reassessmentUpdatePlanOfCare","Reassessment and Update Plan of Care for Doctor"),I&&G&&(0,d.jsxs)("span",{className:"text-lg font-normal text-gray-600 dark:text-gray-400 ml-3",children:["- ",G.nameEn||G.name]})]}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:k("reassessmentDescription","Comprehensive 2-page reassessment and plan update form")}),D&&(0,d.jsxs)("div",{className:"flex items-center space-x-4 mt-3 text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("i",{className:"fas fa-link text-blue-600 dark:text-blue-400"}),(0,d.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:[k("originalPlan","Original Plan"),": ",D.createdDate]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("i",{className:"fas fa-user-md text-green-600 dark:text-green-400"}),(0,d.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:[k("physician","Physician"),": ",D.physician]})]})]}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-2 mt-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:[(0,d.jsx)("i",{className:"fas fa-certificate text-blue-600 dark:text-blue-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-blue-800 dark:text-blue-200",children:"CARF Compliant"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full",children:[(0,d.jsx)("i",{className:"fas fa-shield-alt text-green-600 dark:text-green-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800 dark:text-green-200",children:"CBAHI Compliant"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:[(0,d.jsx)("i",{className:"fas fa-lock text-purple-600 dark:text-purple-400"}),(0,d.jsx)("span",{className:"text-sm font-medium text-purple-800 dark:text-purple-200",children:"HIPAA Secure"})]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[I&&(0,d.jsxs)("button",{type:"button",onClick:()=>N("/patients/".concat(I)),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-user mr-2"}),k("viewPatient","View Patient")]}),(0,d.jsxs)("button",{type:"button",onClick:()=>{o.Ay.success(k("pdfExported","PDF exported successfully"))},className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,d.jsx)("i",{className:"fas fa-file-pdf mr-2"}),k("exportPDF","Export PDF")]}),(0,d.jsx)("button",{type:"button",onClick:f||(()=>N(I?"/patients/".concat(I):"/patients")),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white",children:k("cancel","Cancel")})]})]})}),(0,d.jsx)(c,{formData:O,handleInputChange:q,errors:S,originalPlanOfCare:D}),(0,d.jsx)(m,{formData:O,handleInputChange:q}),(0,d.jsx)(x,{formData:O,handleInputChange:q,handleArrayChange:(e,a,t)=>{const s=(0,r.A)({},O),l=e.split(".");let i=s;for(let r=0;r<l.length-1;r++)i=i[l[r]];i[l[l.length-1]][a]=t,M(s)},addGoal:e=>{const a=(0,r.A)({},O);a[e].updatedGoals.push(""),M(a)},removeGoal:(e,a)=>{const t=(0,r.A)({},O);t[e].updatedGoals.splice(a,1),M(t)},errors:S,originalPlanOfCare:D}),(0,d.jsx)(g,{formData:O,handleInputChange:q}),(0,d.jsx)(u,{formData:O,handleInputChange:q,errors:S}),(0,d.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,d.jsx)("button",{type:"button",onClick:f||(()=>N(I?"/patients/".concat(I):"/patients")),className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors",children:k("cancel","Cancel")}),(0,d.jsx)("button",{type:"submit",disabled:T,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:T?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),k("saving","Saving...")]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("i",{className:"fas fa-save mr-2"}),k("saveReassessment","Save Reassessment")]})})]})]})})}}}]);
//# sourceMappingURL=4120.55da6506.chunk.js.map