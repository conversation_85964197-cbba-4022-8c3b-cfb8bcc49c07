{"version": 3, "file": "static/js/534.4dd07eb5.chunk.js", "mappings": "iMAIA,MA6UA,EA7U6BA,KAC3B,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACPC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,iBAClDC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,CAAC,IACzCG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,IAEvCK,EAAAA,EAAAA,WAAU,KAERC,WAAW,KAuFTJ,EAtFwB,CACtB,eAAgB,CACdK,SAAU,CACR,CACEC,GAAI,QACJC,KAAM,qCACNC,MAAO,KACPC,OAAQ,GACRC,MAAO,KACPC,YAAa,6EAEf,CACEL,GAAI,QACJC,KAAM,gCACNC,MAAO,KACPC,OAAQ,GACRC,MAAO,KACPC,YAAa,2DAEf,CACEL,GAAI,QACJC,KAAM,gCACNC,MAAO,KACPC,OAAQ,GACRC,MAAO,OACPC,YAAa,2DAInB,iBAAkB,CAChBN,SAAU,CACR,CACEC,GAAI,QACJC,KAAM,6BACNC,MAAO,KACPC,OAAQ,GACRC,MAAO,KACPC,YAAa,kEAEf,CACEL,GAAI,QACJC,KAAM,wBACNC,MAAO,GACPC,OAAQ,GACRC,MAAO,OACPC,YAAa,2CAEf,CACEL,GAAI,QACJC,KAAM,mCACNC,MAAO,IACPC,OAAQ,EACRC,MAAO,SACPC,YAAa,oCAInB,qBAAsB,CACpBN,SAAU,CACR,CACEC,GAAI,QACJC,KAAM,6BACNC,MAAO,IACPC,OAAQ,EACRC,MAAO,KACPC,YAAa,mDAEf,CACEL,GAAI,QACJC,KAAM,+BACNC,MAAO,KACPC,OAAQ,GACRC,MAAO,KACPC,YAAa,oDAEf,CACEL,GAAI,QACJC,KAAM,+BACNC,MAAO,GACPC,OAAQ,GACRC,MAAO,KACPC,YAAa,wDAMrBT,GAAW,IACV,MACF,IAEH,MAAMU,EAAa,CACjB,CAAEN,GAAI,eAAgBO,MAAOnB,EAAE,cAAe,gBAAiBoB,KAAM,kBACrE,CAAER,GAAI,iBAAkBO,MAAOnB,EAAE,gBAAiB,kBAAmBoB,KAAM,qBAC3E,CAAER,GAAI,qBAAsBO,MAAOnB,EAAE,oBAAqB,sBAAuBoB,KAAM,sBAGnFC,EAAgBL,IACpB,OAAQA,GACN,IAAK,KACH,MAAO,iCACT,IAAK,OACH,MAAO,iCACT,IAAK,SACH,MAAO,+BACT,QACE,MAAO,+BAIPM,EAAgB,SAACR,EAAOC,GAE5B,OAFiDQ,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAClBT,GAASC,EAASD,GAASC,GAC1C,qCAAuC,gCACzD,EAEA,OAAIR,GAEAmB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAuCC,UACpDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,8FAA6FC,UAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D5B,EAAE,uBAAwB,6BAE7B0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjD5B,EAAE,0BAA2B,6EAGlC6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uFAAsFC,SAAA,EACnGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACbD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,yDAAwDC,SAAC,6BAM/EC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAAqBC,SACjCV,EAAWY,IAAKC,IACfF,EAAAA,EAAAA,MAAA,UAEEG,QAASA,IAAM7B,EAAoB4B,EAASnB,IAC5Ce,UAAS,8DAAAM,OACP/B,IAAqB6B,EAASnB,GAC1B,sDACA,0HACHgB,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAM,OAAKF,EAASX,KAAI,WAC7BW,EAASZ,QATLY,EAASnB,UAgBtBc,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,SACjBvB,EAAYH,KACXwB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,SAClEvB,EAAYH,GAAkBS,SAASmB,IAAKI,IAC3CL,EAAAA,EAAAA,MAAA,OAEEF,UAAU,yFAAwFC,SAAA,EAElGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oDAAmDC,SAC9DM,EAAQrB,QAEXa,EAAAA,EAAAA,KAAA,KAAGC,UAAWN,EAAaa,EAAQlB,aAGrCU,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gCAA+BC,SAAA,EAC5CC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,sBAAAM,OAAwBX,EACrCY,EAAQpB,MACRoB,EAAQnB,OACRmB,EAAQtB,GAAGuB,SAAS,UAAYD,EAAQtB,GAAGuB,SAAS,eAClDP,SAAA,CACDM,EAAQpB,MACRoB,EAAQtB,GAAGuB,SAAS,iBACpBD,EAAQtB,GAAGuB,SAAS,UAAYD,EAAQtB,GAAGuB,SAAS,aADd,GACkC,QAE3EN,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2CAA0CC,SAAA,CAAC,KACtDM,EAAQnB,OAAQmB,EAAQtB,GAAGuB,SAAS,iBACpCD,EAAQtB,GAAGuB,SAAS,UAAYD,EAAQtB,GAAGuB,SAAS,aADE,GACkB,IAAI,mBAKrFT,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzDM,EAAQjB,eAIXS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAS,oBAAAM,OACNC,EAAQtB,GAAGuB,SAAS,UAAYD,EAAQtB,GAAGuB,SAAS,aAChDD,EAAQpB,OAASoB,EAAQnB,OAAS,eAAiB,aACnDmB,EAAQpB,OAASoB,EAAQnB,OAAS,eAAiB,cAE1DqB,MAAO,CACLC,MAAM,GAADJ,OAAKK,KAAKC,IACZL,EAAQtB,GAAGuB,SAAS,UAAYD,EAAQtB,GAAGuB,SAAS,aACjDG,KAAKE,IAAI,EAAG,IAAON,EAAQpB,MAAQoB,EAAQnB,OAAU,KACpDmB,EAAQpB,MAAQoB,EAAQnB,OAAU,IACvC,KACD,YA9CFmB,EAAQtB,aA0DzBiB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE5B,EAAE,qBAAsB,4BAG7B0B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,yGAAwGC,UACrHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE5B,EAAE,uBAAwB,4BAE7B0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD5B,EAAE,uBAAwB,oDAI/B6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uGAAsGC,UACnHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE5B,EAAE,wBAAyB,6BAE9B0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD5B,EAAE,yBAA0B,oDAIjC6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2GAA0GC,UACvHF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yEAEfD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE5B,EAAE,iBAAkB,sBAEvB0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gDAA+CC,SACzD5B,EAAE,wBAAyB,6DAQtC6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,0FAAyFC,SAAA,EACtGF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE5B,EAAE,wBAAyB,gCAGhC0B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,MAAKC,UAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+HAA8HC,SAAA,EAC3IF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2EACbE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D5B,EAAE,2BAA4B,4CAEjC0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D5B,EAAE,kBAAmB,0EAExB0B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,kGAAiGC,SAAA,CAC9G5B,EAAE,WAAY,YAAY,KAAGA,EAAE,SAAU,sBAMlD6B,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2HAA0HC,SAAA,EACvIF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,iEACbE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,iDAAgDC,SAC3D5B,EAAE,qBAAsB,4CAE3B0B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,kDAAiDC,SAC3D5B,EAAE,eAAgB,mEAErB0B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,OAAMC,UACnBC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,8FAA6FC,SAAA,CAC1G5B,EAAE,SAAU,UAAU,KAAGA,EAAE,UAAW,mCC9T3D,EAJ6BD,KACpB2B,EAAAA,EAAAA,KAACe,EAA6B,G,mCCDhC,MAAMC,EAAkB,CAE7BC,aAAc,CACZ/B,GAAI,KACJgC,MAAO,yBACP3B,YAAa,sDACb4B,aAAc,CACZ,uCACA,mCACA,mCACA,sCACA,iCACA,6CAKJC,eAAgB,CACdlC,GAAI,KACJgC,MAAO,2BACP3B,YAAa,wDACb4B,aAAc,CACZ,mCACA,2BACA,8BACA,6CACA,yBACA,kCAKJE,mBAAoB,CAClBnC,GAAI,KACJgC,MAAO,+BACP3B,YAAa,mDACb4B,aAAc,CACZ,+BACA,yCACA,0BACA,kCACA,+BACA,mCAKJG,gBAAiB,CACfpC,GAAI,KACJgC,MAAO,4BACP3B,YAAa,gDACb4B,aAAc,CACZ,8BACA,gCACA,2BACA,yBACA,gCACA,qBAKJI,uBAAwB,CACtBrC,GAAI,KACJgC,MAAO,mCACP3B,YAAa,+CACb4B,aAAc,CACZ,4BACA,4BACA,iCACA,2BACA,4BACA,gCAKJK,WAAY,CACVtC,GAAI,KACJgC,MAAO,sCACP3B,YAAa,0CACb4B,aAAc,CACZ,2BACA,4BACA,qBACA,sBACA,qBACA,0BAKJM,oBAAqB,CACnBvC,GAAI,KACJgC,MAAO,gCACP3B,YAAa,+CACb4B,aAAc,CACZ,+BACA,yBACA,uBACA,yBACA,mBACA,oB", "sources": ["components/CBAHI/CBAHIQualityMeasures.jsx", "pages/Compliance/CBAHIQualityMeasures.jsx", "utils/cbahiStandards.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { CBAHI_STANDARDS } from '../../utils/cbahiStandards';\n\nconst CBAHIQualityMeasures = () => {\n  const { t } = useLanguage();\n  const [selectedCategory, setSelectedCategory] = useState('patient-care');\n  const [qualityData, setQualityData] = useState({});\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Mock quality measures data loading\n    setTimeout(() => {\n      const mockQualityData = {\n        'patient-care': {\n          measures: [\n            {\n              id: 'pc-01',\n              name: 'Patient Assessment Completion Rate',\n              value: 98.5,\n              target: 95,\n              trend: 'up',\n              description: 'Percentage of patients with completed initial assessments within 24 hours'\n            },\n            {\n              id: 'pc-02',\n              name: 'Pain Assessment Documentation',\n              value: 96.2,\n              target: 90,\n              trend: 'up',\n              description: 'Percentage of patients with documented pain assessments'\n            },\n            {\n              id: 'pc-03',\n              name: 'Discharge Planning Completion',\n              value: 94.8,\n              target: 95,\n              trend: 'down',\n              description: 'Percentage of patients with completed discharge plans'\n            }\n          ]\n        },\n        'patient-safety': {\n          measures: [\n            {\n              id: 'ps-01',\n              name: 'Fall Prevention Compliance',\n              value: 99.1,\n              target: 98,\n              trend: 'up',\n              description: 'Percentage of high-risk patients with fall prevention measures'\n            },\n            {\n              id: 'ps-02',\n              name: 'Medication Error Rate',\n              value: 0.2,\n              target: 0.5,\n              trend: 'down',\n              description: 'Medication errors per 1000 patient days'\n            },\n            {\n              id: 'ps-03',\n              name: 'Healthcare-Associated Infections',\n              value: 1.1,\n              target: 2.0,\n              trend: 'stable',\n              description: 'HAI rate per 1000 patient days'\n            }\n          ]\n        },\n        'quality-management': {\n          measures: [\n            {\n              id: 'qm-01',\n              name: 'Patient Satisfaction Score',\n              value: 4.6,\n              target: 4.0,\n              trend: 'up',\n              description: 'Average patient satisfaction rating (1-5 scale)'\n            },\n            {\n              id: 'qm-02',\n              name: 'Clinical Outcome Achievement',\n              value: 87.3,\n              target: 85,\n              trend: 'up',\n              description: 'Percentage of patients achieving treatment goals'\n            },\n            {\n              id: 'qm-03',\n              name: 'Quality Improvement Projects',\n              value: 12,\n              target: 10,\n              trend: 'up',\n              description: 'Number of active quality improvement initiatives'\n            }\n          ]\n        }\n      };\n      setQualityData(mockQualityData);\n      setLoading(false);\n    }, 1000);\n  }, []);\n\n  const categories = [\n    { id: 'patient-care', label: t('patientCare', 'Patient Care'), icon: 'fas fa-user-md' },\n    { id: 'patient-safety', label: t('patientSafety', 'Patient Safety'), icon: 'fas fa-shield-alt' },\n    { id: 'quality-management', label: t('qualityManagement', 'Quality Management'), icon: 'fas fa-chart-line' }\n  ];\n\n  const getTrendIcon = (trend) => {\n    switch (trend) {\n      case 'up':\n        return 'fas fa-arrow-up text-green-500';\n      case 'down':\n        return 'fas fa-arrow-down text-red-500';\n      case 'stable':\n        return 'fas fa-minus text-yellow-500';\n      default:\n        return 'fas fa-minus text-gray-500';\n    }\n  };\n\n  const getValueColor = (value, target, isLowerBetter = false) => {\n    const isGood = isLowerBetter ? value <= target : value >= target;\n    return isGood ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400';\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {t('cbahiQualityMeasures', 'CBAHI Quality Measures')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n              {t('cbahiQualityDescription', 'Key performance indicators and quality metrics for CBAHI compliance')}\n            </p>\n          </div>\n          <div className=\"flex items-center space-x-2 px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full\">\n            <i className=\"fas fa-chart-bar text-green-600 dark:text-green-400\"></i>\n            <span className=\"text-sm font-medium text-green-800 dark:text-green-200\">Quality Metrics</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Category Tabs */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <div className=\"border-b border-gray-200 dark:border-gray-600\">\n          <nav className=\"flex space-x-8 px-6\">\n            {categories.map((category) => (\n              <button\n                key={category.id}\n                onClick={() => setSelectedCategory(category.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${\n                  selectedCategory === category.id\n                    ? 'border-green-500 text-green-600 dark:text-green-400'\n                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n                }`}\n              >\n                <i className={`${category.icon} mr-2`}></i>\n                {category.label}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        {/* Quality Measures Content */}\n        <div className=\"p-6\">\n          {qualityData[selectedCategory] && (\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6\">\n              {qualityData[selectedCategory].measures.map((measure) => (\n                <div\n                  key={measure.id}\n                  className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-200 dark:border-gray-600\"\n                >\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"font-medium text-gray-900 dark:text-white text-sm\">\n                      {measure.name}\n                    </h3>\n                    <i className={getTrendIcon(measure.trend)}></i>\n                  </div>\n\n                  <div className=\"mb-4\">\n                    <div className=\"flex items-baseline space-x-2\">\n                      <span className={`text-3xl font-bold ${getValueColor(\n                        measure.value, \n                        measure.target, \n                        measure.id.includes('error') || measure.id.includes('infection')\n                      )}`}>\n                        {measure.value}\n                        {measure.id.includes('satisfaction') ? '' : \n                         measure.id.includes('error') || measure.id.includes('infection') ? '' : '%'}\n                      </span>\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n                        / {measure.target}{measure.id.includes('satisfaction') ? '' : \n                           measure.id.includes('error') || measure.id.includes('infection') ? '' : '%'} target\n                      </span>\n                    </div>\n                  </div>\n\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                    {measure.description}\n                  </p>\n\n                  {/* Progress Bar */}\n                  <div className=\"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2\">\n                    <div\n                      className={`h-2 rounded-full ${\n                        (measure.id.includes('error') || measure.id.includes('infection'))\n                          ? (measure.value <= measure.target ? 'bg-green-500' : 'bg-red-500')\n                          : (measure.value >= measure.target ? 'bg-green-500' : 'bg-red-500')\n                      }`}\n                      style={{\n                        width: `${Math.min(\n                          (measure.id.includes('error') || measure.id.includes('infection'))\n                            ? Math.max(0, 100 - (measure.value / measure.target) * 100)\n                            : (measure.value / measure.target) * 100,\n                          100\n                        )}%`\n                      }}\n                    ></div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Performance Summary */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('performanceSummary', 'Performance Summary')}\n          </h2>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <i className=\"fas fa-trophy text-green-600 dark:text-green-400 text-2xl\"></i>\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('excellentPerformance', 'Excellent Performance')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n                {t('excellentDescription', '8 out of 9 quality measures exceed targets')}\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <i className=\"fas fa-chart-line text-blue-600 dark:text-blue-400 text-2xl\"></i>\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('continuousImprovement', 'Continuous Improvement')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n                {t('improvementDescription', 'Positive trends in 7 key performance areas')}\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <i className=\"fas fa-shield-check text-purple-600 dark:text-purple-400 text-2xl\"></i>\n              </div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('cbahiCompliant', 'CBAHI Compliant')}\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n                {t('complianceDescription', 'All measures meet or exceed CBAHI standards')}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Action Plan */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('improvementActionPlan', 'Improvement Action Plan')}\n          </h2>\n        </div>\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            <div className=\"flex items-start space-x-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n              <i className=\"fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 mt-1\"></i>\n              <div className=\"flex-1\">\n                <h4 className=\"font-medium text-yellow-800 dark:text-yellow-200\">\n                  {t('improveDischargePlanning', 'Improve Discharge Planning Completion')}\n                </h4>\n                <p className=\"text-sm text-yellow-600 dark:text-yellow-400 mt-1\">\n                  {t('dischargeAction', 'Implement automated discharge planning reminders and staff training')}\n                </p>\n                <div className=\"mt-2\">\n                  <span className=\"text-xs bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded\">\n                    {t('priority', 'Priority')}: {t('medium', 'Medium')}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 mt-1\"></i>\n              <div className=\"flex-1\">\n                <h4 className=\"font-medium text-green-800 dark:text-green-200\">\n                  {t('maintainExcellence', 'Maintain Excellence in Patient Safety')}\n                </h4>\n                <p className=\"text-sm text-green-600 dark:text-green-400 mt-1\">\n                  {t('safetyAction', 'Continue current safety protocols and regular staff training')}\n                </p>\n                <div className=\"mt-2\">\n                  <span className=\"text-xs bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200 px-2 py-1 rounded\">\n                    {t('status', 'Status')}: {t('onTrack', 'On Track')}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CBAHIQualityMeasures;\n", "import React from 'react';\nimport CBAHIQualityMeasuresComponent from '../../components/CBAHI/CBAHIQualityMeasures';\n\nconst CBAHIQualityMeasures = () => {\n  return <CBAHIQualityMeasuresComponent />;\n};\n\nexport default CBAHIQualityMeasures;\n", "// CBAHI (Central Board for Accreditation of Healthcare Institutions) Standards\n// These standards are specific to healthcare institutions in Saudi Arabia\n\nexport const CBAHI_STANDARDS = {\n  // Patient Care Standards\n  PATIENT_CARE: {\n    id: 'PC',\n    title: 'Patient Care Standards',\n    description: 'Standards for comprehensive patient care and safety',\n    requirements: [\n      'Patient assessment and care planning',\n      'Medication management and safety',\n      'Infection prevention and control',\n      'Patient rights and responsibilities',\n      'Pain assessment and management',\n      'Patient education and discharge planning'\n    ]\n  },\n\n  // Patient Safety\n  PATIENT_SAFETY: {\n    id: 'PS',\n    title: 'Patient Safety Standards',\n    description: 'Standards for ensuring patient safety throughout care',\n    requirements: [\n      'Patient identification protocols',\n      'Fall prevention programs',\n      'Medication error prevention',\n      'Healthcare-associated infection prevention',\n      'Safe surgery protocols',\n      'Emergency response procedures'\n    ]\n  },\n\n  // Quality Management\n  QUALITY_MANAGEMENT: {\n    id: 'QM',\n    title: 'Quality Management Standards',\n    description: 'Standards for quality improvement and management',\n    requirements: [\n      'Quality improvement programs',\n      'Performance measurement and monitoring',\n      'Risk management systems',\n      'Patient satisfaction monitoring',\n      'Clinical outcome measurement',\n      'Continuous quality improvement'\n    ]\n  },\n\n  // Human Resources\n  HUMAN_RESOURCES: {\n    id: 'HR',\n    title: 'Human Resources Standards',\n    description: 'Standards for healthcare workforce management',\n    requirements: [\n      'Staff competency assessment',\n      'Continuing education programs',\n      'Professional development',\n      'Performance evaluation',\n      'Credentialing and privileging',\n      'Workplace safety'\n    ]\n  },\n\n  // Information Management\n  INFORMATION_MANAGEMENT: {\n    id: 'IM',\n    title: 'Information Management Standards',\n    description: 'Standards for healthcare information systems',\n    requirements: [\n      'Medical record management',\n      'Data security and privacy',\n      'Information system reliability',\n      'Data backup and recovery',\n      'Electronic health records',\n      'Health information exchange'\n    ]\n  },\n\n  // Governance and Leadership\n  GOVERNANCE: {\n    id: 'GL',\n    title: 'Governance and Leadership Standards',\n    description: 'Standards for organizational governance',\n    requirements: [\n      'Organizational structure',\n      'Leadership accountability',\n      'Strategic planning',\n      'Resource allocation',\n      'Policy development',\n      'Compliance monitoring'\n    ]\n  },\n\n  // Facility Management\n  FACILITY_MANAGEMENT: {\n    id: 'FM',\n    title: 'Facility Management Standards',\n    description: 'Standards for healthcare facility operations',\n    requirements: [\n      'Facility safety and security',\n      'Environmental controls',\n      'Equipment management',\n      'Emergency preparedness',\n      'Waste management',\n      'Utility systems'\n    ]\n  }\n};\n\n// CBAHI Compliance Requirements for PT Assessment\nexport const CBAHI_PT_ASSESSMENT_REQUIREMENTS = {\n  PATIENT_IDENTIFICATION: {\n    standard: 'PS.1',\n    requirement: 'Patient identification must include at least two identifiers',\n    implementation: 'Patient ID and name verification in assessment form'\n  },\n\n  COMPREHENSIVE_ASSESSMENT: {\n    standard: 'PC.1',\n    requirement: 'Comprehensive patient assessment within specified timeframe',\n    implementation: '8-page comprehensive PT assessment covering all required domains'\n  },\n\n  PAIN_ASSESSMENT: {\n    standard: 'PC.3',\n    requirement: 'Standardized pain assessment and documentation',\n    implementation: 'Multiple pain scales and body map for pain location'\n  },\n\n  FUNCTIONAL_ASSESSMENT: {\n    standard: 'PC.2',\n    requirement: 'Functional status assessment and documentation',\n    implementation: 'ROM, muscle testing, coordination, and functional mobility assessment'\n  },\n\n  DOCUMENTATION_STANDARDS: {\n    standard: 'IM.1',\n    requirement: 'Complete, accurate, and timely documentation',\n    implementation: 'Structured digital form with validation and audit trail'\n  },\n\n  PATIENT_SAFETY: {\n    standard: 'PS.2',\n    requirement: 'Fall risk and safety assessment',\n    implementation: 'Fall risk assessment and safety screening included'\n  },\n\n  QUALITY_MEASURES: {\n    standard: 'QM.1',\n    requirement: 'Quality indicators and outcome measurement',\n    implementation: 'Standardized assessment tools and outcome measures'\n  },\n\n  PROFESSIONAL_STANDARDS: {\n    standard: 'HR.1',\n    requirement: 'Licensed healthcare professional assessment',\n    implementation: 'Professional signature and credential verification'\n  }\n};\n\n// CBAHI Assessment Checklist\nexport const CBAHI_ASSESSMENT_CHECKLIST = [\n  {\n    category: 'Patient Identification',\n    items: [\n      'Patient name verification',\n      'Unique patient identifier',\n      'Date of birth confirmation',\n      'Contact information validation'\n    ]\n  },\n  {\n    category: 'Assessment Completeness',\n    items: [\n      'Medical history documentation',\n      'Current medications review',\n      'Allergy documentation',\n      'Social history assessment',\n      'Functional status evaluation'\n    ]\n  },\n  {\n    category: 'Clinical Assessment',\n    items: [\n      'Physical examination findings',\n      'Neurological assessment',\n      'Musculoskeletal evaluation',\n      'Pain assessment with scales',\n      'Functional mobility testing'\n    ]\n  },\n  {\n    category: 'Safety Assessment',\n    items: [\n      'Fall risk evaluation',\n      'Cognitive assessment',\n      'Environmental safety review',\n      'Equipment safety check',\n      'Emergency contact information'\n    ]\n  },\n  {\n    category: 'Documentation Quality',\n    items: [\n      'Complete form submission',\n      'Professional signature',\n      'Date and time stamps',\n      'Legible documentation',\n      'Error correction procedures'\n    ]\n  },\n  {\n    category: 'Patient Rights',\n    items: [\n      'Informed consent process',\n      'Privacy protection',\n      'Cultural considerations',\n      'Language preferences',\n      'Patient preferences documentation'\n    ]\n  }\n];\n\n// CBAHI Compliance Validation\nexport const validateCBAHICompliance = (assessmentData) => {\n  const compliance = {\n    score: 0,\n    maxScore: 0,\n    issues: [],\n    recommendations: []\n  };\n\n  // Check patient identification\n  if (assessmentData.patientInfo?.name && assessmentData.patientInfo?.patientId) {\n    compliance.score += 10;\n  } else {\n    compliance.issues.push('Missing patient identification information');\n  }\n  compliance.maxScore += 10;\n\n  // Check assessment completeness\n  if (assessmentData.patientInfo?.diagnosis && assessmentData.patientInfo?.chiefComplaint) {\n    compliance.score += 15;\n  } else {\n    compliance.issues.push('Incomplete medical history documentation');\n  }\n  compliance.maxScore += 15;\n\n  // Check pain assessment\n  if (assessmentData.musculoskeletalExam?.painAssessment?.present !== undefined) {\n    compliance.score += 10;\n  } else {\n    compliance.issues.push('Pain assessment not completed');\n  }\n  compliance.maxScore += 10;\n\n  // Check safety assessment\n  if (assessmentData.assessmentReview?.fallRisk) {\n    compliance.score += 10;\n  } else {\n    compliance.issues.push('Fall risk assessment not completed');\n  }\n  compliance.maxScore += 10;\n\n  // Check professional documentation\n  if (assessmentData.signatures?.therapistSignature) {\n    compliance.score += 5;\n  } else {\n    compliance.issues.push('Professional signature required');\n  }\n  compliance.maxScore += 5;\n\n  // Calculate compliance percentage\n  compliance.percentage = Math.round((compliance.score / compliance.maxScore) * 100);\n\n  // Add recommendations based on score\n  if (compliance.percentage < 80) {\n    compliance.recommendations.push('Complete all required assessment sections');\n    compliance.recommendations.push('Ensure proper documentation of patient safety measures');\n  }\n\n  if (compliance.percentage < 90) {\n    compliance.recommendations.push('Review CBAHI standards for comprehensive assessment');\n  }\n\n  return compliance;\n};\n\nexport default CBAHI_STANDARDS;\n"], "names": ["CBAHIQualityMeasures", "t", "useLanguage", "selectedCate<PERSON><PERSON>", "setSelectedCategory", "useState", "qualityData", "setQualityData", "loading", "setLoading", "useEffect", "setTimeout", "measures", "id", "name", "value", "target", "trend", "description", "categories", "label", "icon", "getTrendIcon", "getValueColor", "arguments", "length", "undefined", "_jsx", "className", "children", "_jsxs", "map", "category", "onClick", "concat", "measure", "includes", "style", "width", "Math", "min", "max", "CBAHIQualityMeasuresComponent", "CBAHI_STANDARDS", "PATIENT_CARE", "title", "requirements", "PATIENT_SAFETY", "QUALITY_MANAGEMENT", "HUMAN_RESOURCES", "INFORMATION_MANAGEMENT", "GOVERNANCE", "FACILITY_MANAGEMENT"], "sourceRoot": ""}