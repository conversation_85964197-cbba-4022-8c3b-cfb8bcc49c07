{"version": 3, "file": "static/js/1534.29570f94.chunk.js", "mappings": "uNAMA,MAwkBA,EAxkBiBA,KACf,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACfC,GAAWC,EAAAA,EAAAA,MACXC,GAAWC,EAAAA,EAAAA,OACVC,EAAYC,IAAiBC,EAAAA,EAAAA,UAAS,KACtCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,QAC1CG,EAAQC,IAAaJ,EAAAA,EAAAA,UAAS,SAC9BK,EAAUC,IAAeN,EAAAA,EAAAA,UAAS,KAClCO,EAASC,IAAcR,EAAAA,EAAAA,WAAS,IAkFvCS,EAAAA,EAAAA,WAAU,KACRC,KACC,KAGHD,EAAAA,EAAAA,WAAU,KACkB,cAAtBb,EAASe,UACXD,KAED,CAACd,EAASe,WAEb,MAAMD,EAAeE,UACnBJ,GAAW,GACX,IACE,MAAMK,QAAiBC,EAAAA,GAAIC,IAAI,aAC/B,IAAIF,EAASG,QAGX,MAAM,IAAIC,MAAMJ,EAASK,SAAW,2BAFpCZ,EAAYO,EAASM,KAIzB,CAAE,MAAOC,GACPC,QAAQD,MAAM,0BAA2BA,GAOvCE,EAAAA,GAAMF,MAAM7B,EAAE,uBAAwB,2BACtCe,EAAY,GAEhB,CAAC,QACCE,GAAW,EACb,GAKIe,EAAkB,CACtBC,OAAQjC,EAAE,SAAU,4BACpBkC,eAAgBlC,EAAE,gBAAiB,kBACnCmC,cAAenC,EAAE,eAAgB,iBACjCoC,wBAAyBpC,EAAE,yBAA0B,2BACrDqC,aAAcrC,EAAE,cAAe,iBAG3BsC,EAAe,CACnBC,OAAQ,CAAEC,MAAOxC,EAAE,SAAU,UAAWyC,MAAO,+BAC/CC,SAAU,CAAEF,MAAOxC,EAAE,WAAY,YAAayC,MAAO,6BACrDE,WAAY,CAAEH,MAAOxC,EAAE,aAAc,cAAeyC,MAAO,8BAGvDG,EAAmB9B,EACtB+B,OAAOC,IACN,MAAMC,EAAgBD,EAAQE,KAAKC,cAAcC,SAAS3C,EAAW0C,gBAChDH,EAAQK,OAAOF,cAAcC,SAAS3C,EAAW0C,eAChEG,EAAiC,QAAjB1C,GAA0BoC,EAAQO,SAAW3C,EACnE,OAAOqC,GAAiBK,IAEzBE,KAAK,CAACC,EAAGC,KACR,OAAQ5C,GACN,IAAK,OACH,OAAO2C,EAAEP,KAAKS,cAAcD,EAAER,MAChC,IAAK,MACH,OAAOO,EAAEG,IAAMF,EAAEE,IACnB,IAAK,YACH,OAAO,IAAIC,KAAKH,EAAEI,WAAa,IAAID,KAAKJ,EAAEK,WAC5C,IAAK,WACH,OAAOJ,EAAEK,SAAWN,EAAEM,SACxB,QACE,OAAO,KAwCf,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAS,OAAAC,OAAS/D,EAAQ,cAAgB,gBAAiBgE,SAAA,EAE9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCE,SAAA,EACrDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7DjE,EAAE,WAAY,eAEjBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CjE,EAAE,eAAgB,oDAGvB8D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BH,EAAAA,EAAAA,MAAA,UACEK,QAAShD,EACTiD,SAAUpD,EACV+C,UAAU,wHACVM,MAAOrE,EAAE,qBAAsB,wBAAwBiE,SAAA,EAEvDC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,wBAAAC,OAA0BhD,EAAU,eAAiB,MAChEhB,EAAE,UAAW,eAEhB8D,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMhE,EAAS,oBACxB4D,UAAU,wGAAuGE,SAAA,EAEjHC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBACZ/D,EAAE,iBAAkB,uBAEvB8D,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMhE,EAAS,iBACxB4D,UAAU,oGAAmGE,SAAA,EAE7GC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZ/D,EAAE,gBAAiB,6BAM1B8D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,EACzDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEjE,EAAE,gBAAiB,qBACxFkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAAEnD,EAASwD,kBAKhFJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEjE,EAAE,iBAAkB,sBACzFkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DnD,EAAS+B,OAAO0B,GAAkB,WAAbA,EAAElB,QAAqBiB,kBAMrDJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,0EAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEjE,EAAE,uBAAwB,4BAC/FkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DnD,EAAS+B,OAAO0B,GAAKA,EAAEC,iBAAiBF,kBAMjDJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8FAA6FE,UAC1GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uDAAsDE,UACnEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAAEjE,EAAE,kBAAmB,uBAC1F8D,EAAAA,EAAAA,MAAA,KAAGC,UAAU,mDAAkDE,SAAA,CAC5DQ,KAAKC,MAAM5D,EAAS6D,OAAO,CAACC,EAAKL,IAAMK,EAAML,EAAEV,SAAU,GAAK/C,EAASwD,QAAQ,mBAQ1FR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mGAAkGE,SAAA,EAC/GH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DE,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qCACZ/D,EAAE,eAAgB,qBAErB8D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDE,SAAA,EACnEH,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMhE,EAAS,iBACxB4D,UAAU,+IAA8IE,SAAA,EAExJC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2HAA0HE,UACvIC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEAEfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qEAAoEE,SACjFjE,EAAE,aAAc,qBAIrB8D,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMhE,EAAS,oBACxB4D,UAAU,uJAAsJE,SAAA,EAEhKC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mIAAkIE,UAC/IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kEAEfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qEAAoEE,SACjFjE,EAAE,iBAAkB,yBAIzB8D,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMhE,EAAS,iBACxB4D,UAAU,mJAAkJE,SAAA,EAE5JC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+HAA8HE,UAC3IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uEAEfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qEAAoEE,SACjFjE,EAAE,sBAAuB,8BAI9B8D,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMhE,EAAS,UACxB4D,UAAU,uJAAsJE,SAAA,EAEhKC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mIAAkIE,UAC/IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wEAEfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qEAAoEE,SACjFjE,EAAE,kBAAmB,0BAI1B8D,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMhE,EAAS,YACxB4D,UAAU,uJAAsJE,SAAA,EAEhKC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mIAAkIE,UAC/IC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qEAEfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qEAAoEE,SACjFjE,EAAE,UAAW,iBAIlB8D,EAAAA,EAAAA,MAAA,UACEK,QAASA,KACPpC,EAAAA,GAAMN,QAAQzB,EAAE,oBAAqB,8BACrC8B,QAAQ+C,IAAI,uBAEdd,UAAU,+IAA8IE,SAAA,EAExJC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2HAA0HE,UACvIC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,gEAEfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,qEAAoEE,SACjFjE,EAAE,aAAc,2BAOzBkE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mGAAkGE,UAC/GH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/EjE,EAAE,SAAU,aAEf8D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SACEY,KAAK,OACLC,MAAOxE,EACPyE,SAAWC,GAAMzE,EAAcyE,EAAEC,OAAOH,OACxCI,YAAanF,EAAE,iBAAkB,sBACjC+D,UAAU,2IAEZG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6DAIjBD,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/EjE,EAAE,SAAU,aAEf8D,EAAAA,EAAAA,MAAA,UACEiB,MAAOrE,EACPsE,SAAWC,GAAMtE,EAAgBsE,EAAEC,OAAOH,OAC1ChB,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQa,MAAM,MAAKd,SAAEjE,EAAE,cAAe,mBACtCkE,EAAAA,EAAAA,KAAA,UAAQa,MAAM,SAAQd,SAAEjE,EAAE,SAAU,aACpCkE,EAAAA,EAAAA,KAAA,UAAQa,MAAM,WAAUd,SAAEjE,EAAE,WAAY,eACxCkE,EAAAA,EAAAA,KAAA,UAAQa,MAAM,aAAYd,SAAEjE,EAAE,aAAc,uBAIhD8D,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,kEAAiEE,SAC/EjE,EAAE,SAAU,cAEf8D,EAAAA,EAAAA,MAAA,UACEiB,MAAOnE,EACPoE,SAAWC,GAAMpE,EAAUoE,EAAEC,OAAOH,OACpChB,UAAU,kIAAiIE,SAAA,EAE3IC,EAAAA,EAAAA,KAAA,UAAQa,MAAM,OAAMd,SAAEjE,EAAE,OAAQ,WAChCkE,EAAAA,EAAAA,KAAA,UAAQa,MAAM,MAAKd,SAAEjE,EAAE,MAAO,UAC9BkE,EAAAA,EAAAA,KAAA,UAAQa,MAAM,YAAWd,SAAEjE,EAAE,YAAa,iBAC1CkE,EAAAA,EAAAA,KAAA,UAAQa,MAAM,WAAUd,SAAEjE,EAAE,WAAY,qBAI5CkE,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iBAAgBE,UAC7BH,EAAAA,EAAAA,MAAA,UAAQC,UAAU,uJAAsJE,SAAA,EACtKC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBACZ/D,EAAE,kBAAmB,+BAO9B8D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,0FAAyFE,SAAA,EACtGC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,UAAW,cAEhBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,YAAa,gBAElBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,YAAa,gBAElBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,WAAY,eAEjBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,YAAa,iBAElBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,kBAAmB,uBAExBkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,SAAU,aAEfkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9GjE,EAAE,UAAW,mBAIpBkE,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvFjD,GACCkD,EAAAA,EAAAA,KAAA,MAAAD,UACEC,EAAAA,EAAAA,KAAA,MAAIkB,QAAQ,IAAIrB,UAAU,yBAAwBE,UAChDH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mCAAkCE,SAAA,EAC/CC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kEACfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,wCAAuCE,SACpDjE,EAAE,kBAAmB,gCAK5B4C,EAAiByC,IAAKvC,IACxBgB,SAAAA,EAAAA,MAAA,MAEEC,UAAU,yDACVI,QAASA,IAAMhE,EAAS,aAAD6D,OAAclB,EAAQwC,KAAMrB,SAAA,EAEnDC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0BAAyBE,UACtCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,uFAAsFE,UACnGC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,UAAAC,OAA+B,SAAnBlB,EAAQyC,OAAoB,OAAS,QAAO,4CAGxEzB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DhE,EAAQ6C,EAAQE,KAAOF,EAAQK,UAElCW,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtDjE,EAAE,MAAO,OAAO,KAAG8C,EAAQY,IAAI,IAAE1D,EAAE,QAAS,qBAKrDkE,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAU,8GAA6GE,SAC1HjC,EAAgBc,EAAQ0C,gBAG7BtB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EnB,EAAQ2C,aAEXvB,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0DAAyDE,UACtEC,EAAAA,EAAAA,KAAA,OACEH,UAAS,oBAAAC,QAnWPH,EAmW8Cf,EAAQe,SAlW1EA,GAAY,GAAW,eACvBA,GAAY,GAAW,cACvBA,GAAY,GAAW,gBACpB,eAgWe6B,MAAO,CAAEC,MAAM,GAAD3B,OAAKlB,EAAQe,SAAQ,WAGvCC,EAAAA,EAAAA,MAAA,QAAMC,UAAU,wCAAuCE,SAAA,CAAEnB,EAAQe,SAAS,aAG9EK,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9E,IAAIN,KAAKb,EAAQc,WAAWgC,wBAE/B1B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oEAAmEE,SAC9EnB,EAAQ0B,gBAAkB,IAAIb,KAAKb,EAAQ0B,iBAAiBoB,qBAAuB,OAEtF1B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgD1B,EAAaQ,EAAQO,QAAQZ,OAAQwB,SACjG3B,EAAaQ,EAAQO,QAAQb,WAGlC0B,EAAAA,EAAAA,KAAA,MAAIH,UAAU,kDAAiDE,UAC7DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBE,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,UACEC,QAAUc,IAAMY,OAzVXC,EAyV6BhD,EAAQwC,GAAIL,EAxV5Dc,uBACN5F,EAAS,aAAD6D,OAAc8B,IAFED,IAACC,GA0VL/B,UAAU,gFACVM,MAAOrE,EAAE,cAAe,gBAAgBiE,UAExCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kBAEfG,EAAAA,EAAAA,KAAA,UACEC,QAAUc,IAAMe,OAxXXF,EAwX6BhD,EAAQwC,GAAIL,EAvX5Dc,uBACN5F,EAAS,aAAD6D,OAAc8B,EAAS,UAFPE,IAACF,GAyXL/B,UAAU,oFACVM,MAAOrE,EAAE,cAAe,gBAAgBiE,UAExCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mBAEfG,EAAAA,EAAAA,KAAA,UACEC,QAAUc,GA1XJ5D,OAAOyE,EAAWG,KAG5C,GAFAA,EAAMF,kBAEFG,OAAOC,QAAQnG,EAAE,uBAAwB,kDAC3C,IACE,MAAMsB,QAAiBC,EAAAA,GAAI6E,OAAO,aAADpC,OAAc8B,IAC/C,IAAIxE,EAASG,QAIX,MAAM,IAAIC,MAAMJ,EAASK,SAAW,4BAHpCI,EAAAA,GAAMN,QAAQzB,EAAE,iBAAkB,iCAClCmB,GAIJ,CAAE,MAAOU,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAMF,MAAM7B,EAAE,uBAAwB,0BACxC,GA2WkCqG,CAAoBvD,EAAQwC,GAAIL,GAChDlB,UAAU,4EACVM,MAAOrE,EAAE,gBAAiB,kBAAkBiE,UAE5CC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,0BAxEdjB,EAAQwC,IAlUHzB,gBAoZlB7C,GAAuC,IAA5B4B,EAAiB0B,SAC5BR,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kEACbG,EAAAA,EAAAA,KAAA,MAAIH,UAAU,yDAAwDE,SACnEjE,EAAE,kBAAmB,wBAExBkE,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5CjE,EAAE,sBAAuB,mD", "sources": ["pages/Patients/Patients.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { apiHelpers as api } from '../../services/api';\nimport toast from 'react-hot-toast';\n\nconst Patients = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('all');\n  const [sortBy, setSortBy] = useState('name');\n  const [patients, setPatients] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Mock patient data for fallback\n  const mockPatients = [\n    {\n      id: 1,\n      name: 'أحمد محمد الأحمد',\n      nameEn: '<PERSON>',\n      age: 8,\n      gender: 'male',\n      condition: 'autism',\n      status: 'active',\n      lastVisit: '2024-01-15',\n      nextAppointment: '2024-01-22',\n      phone: '+966 50 123 4567',\n      emergencyContact: '+966 50 765 4321',\n      therapist: 'Dr. Sarah Al-Rashid',\n      progress: 75\n    },\n    {\n      id: 2,\n      name: 'فاطمة علي السالم',\n      nameEn: 'Fatima Ali Al-Salem',\n      age: 12,\n      gender: 'female',\n      condition: 'cerebral_palsy',\n      status: 'active',\n      lastVisit: '2024-01-14',\n      nextAppointment: '2024-01-21',\n      phone: '+966 50 234 5678',\n      emergencyContact: '+966 50 876 5432',\n      therapist: 'Dr. Ahmed Al-Mansouri',\n      progress: 60\n    },\n    {\n      id: 3,\n      name: 'محمد عبدالله الخالد',\n      nameEn: 'Mohammed Abdullah Al-Khalid',\n      age: 6,\n      gender: 'male',\n      condition: 'down_syndrome',\n      status: 'active',\n      lastVisit: '2024-01-13',\n      nextAppointment: '2024-01-20',\n      phone: '+966 50 345 6789',\n      emergencyContact: '+966 50 987 6543',\n      therapist: 'Dr. Maryam Al-Zahra',\n      progress: 45\n    },\n    {\n      id: 4,\n      name: 'نورا سعد الغامدي',\n      nameEn: 'Nora Saad Al-Ghamdi',\n      age: 10,\n      gender: 'female',\n      condition: 'intellectual_disability',\n      status: 'inactive',\n      lastVisit: '2023-12-20',\n      nextAppointment: null,\n      phone: '+966 50 456 7890',\n      emergencyContact: '+966 50 098 7654',\n      therapist: 'Dr. Omar Al-Harbi',\n      progress: 30\n    },\n    {\n      id: 5,\n      name: 'عبدالرحمن يوسف النعيمي',\n      nameEn: 'Abdulrahman Youssef Al-Naimi',\n      age: 14,\n      gender: 'male',\n      condition: 'spina_bifida',\n      status: 'active',\n      lastVisit: '2024-01-16',\n      nextAppointment: '2024-01-23',\n      phone: '+966 50 567 8901',\n      emergencyContact: '+966 50 109 8765',\n      therapist: 'Dr. Layla Al-Dosari',\n      progress: 85\n    }\n  ];\n\n  // Load patients from API\n  useEffect(() => {\n    loadPatients();\n  }, []);\n\n  // Refresh patients when navigating to this page\n  useEffect(() => {\n    if (location.pathname === '/patients') {\n      loadPatients();\n    }\n  }, [location.pathname]);\n\n  const loadPatients = async () => {\n    setLoading(true);\n    try {\n      const response = await api.get('/patients');\n      if (response.success) {\n        setPatients(response.data);\n      } else {\n        throw new Error(response.message || 'Failed to load patients');\n      }\n    } catch (error) {\n      console.error('Error loading patients:', error);\n\n      // In development mode, use mock data instead of showing error\n      if (process.env.NODE_ENV === 'development') {\n        console.log('Using mock patient data in development mode');\n        setPatients(mockPatients);\n      } else {\n        toast.error(t('errorLoadingPatients', 'Error loading patients'));\n        setPatients([]);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n  const conditionLabels = {\n    autism: t('autism', 'Autism Spectrum Disorder'),\n    cerebral_palsy: t('cerebralPalsy', 'Cerebral Palsy'),\n    down_syndrome: t('downSyndrome', 'Down Syndrome'),\n    intellectual_disability: t('intellectualDisability', 'Intellectual Disability'),\n    spina_bifida: t('spinaBifida', 'Spina Bifida')\n  };\n\n  const statusLabels = {\n    active: { label: t('active', 'Active'), color: 'bg-green-100 text-green-800' },\n    inactive: { label: t('inactive', 'Inactive'), color: 'bg-gray-100 text-gray-800' },\n    discharged: { label: t('discharged', 'Discharged'), color: 'bg-blue-100 text-blue-800' }\n  };\n\n  const filteredPatients = patients\n    .filter(patient => {\n      const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                           patient.nameEn.toLowerCase().includes(searchTerm.toLowerCase());\n      const matchesStatus = filterStatus === 'all' || patient.status === filterStatus;\n      return matchesSearch && matchesStatus;\n    })\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'age':\n          return a.age - b.age;\n        case 'lastVisit':\n          return new Date(b.lastVisit) - new Date(a.lastVisit);\n        case 'progress':\n          return b.progress - a.progress;\n        default:\n          return 0;\n      }\n    });\n\n  const getProgressColor = (progress) => {\n    if (progress >= 80) return 'bg-green-500';\n    if (progress >= 60) return 'bg-blue-500';\n    if (progress >= 40) return 'bg-yellow-500';\n    return 'bg-red-500';\n  };\n\n  const handleEditPatient = (patientId, event) => {\n    event.stopPropagation(); // Prevent row click\n    navigate(`/patients/${patientId}/edit`);\n  };\n\n  const handleDeletePatient = async (patientId, event) => {\n    event.stopPropagation(); // Prevent row click\n\n    if (window.confirm(t('confirmDeletePatient', 'Are you sure you want to delete this patient?'))) {\n      try {\n        const response = await api.delete(`/patients/${patientId}`);\n        if (response.success) {\n          toast.success(t('patientDeleted', 'Patient deleted successfully'));\n          loadPatients(); // Reload the list\n        } else {\n          throw new Error(response.message || 'Failed to delete patient');\n        }\n      } catch (error) {\n        console.error('Error deleting patient:', error);\n        toast.error(t('errorDeletingPatient', 'Error deleting patient'));\n      }\n    }\n  };\n\n  const handleViewPatient = (patientId, event) => {\n    event.stopPropagation(); // Prevent row click\n    navigate(`/patients/${patientId}`);\n  };\n\n  return (\n    <div className={`p-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n            {t('patients', 'Patients')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('patientsDesc', 'Manage patient records and treatment plans')}\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button\n            onClick={loadPatients}\n            disabled={loading}\n            className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center disabled:opacity-50\"\n            title={t('refreshPatientList', 'Refresh Patient List')}\n          >\n            <i className={`fas fa-sync-alt mr-2 ${loading ? 'animate-spin' : ''}`}></i>\n            {t('refresh', 'Refresh')}\n          </button>\n          <button\n            onClick={() => navigate('/patients/search')}\n            className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center\"\n          >\n            <i className=\"fas fa-search mr-2\"></i>\n            {t('advancedSearch', 'Advanced Search')}\n          </button>\n          <button\n            onClick={() => navigate('/patients/new')}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center\"\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('addNewPatient', 'Add New Patient')}\n          </button>\n        </div>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full\">\n              <i className=\"fas fa-users text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('totalPatients', 'Total Patients')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">{patients.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-full\">\n              <i className=\"fas fa-user-check text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('activePatients', 'Active Patients')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {patients.filter(p => p.status === 'active').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full\">\n              <i className=\"fas fa-calendar-check text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('upcomingAppointments', 'Upcoming Appointments')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {patients.filter(p => p.nextAppointment).length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full\">\n              <i className=\"fas fa-chart-line text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">{t('averageProgress', 'Average Progress')}</p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {Math.round(patients.reduce((sum, p) => sum + p.progress, 0) / patients.length)}%\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          <i className=\"fas fa-bolt text-yellow-500 mr-2\"></i>\n          {t('quickActions', 'Quick Actions')}\n        </h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n          <button\n            onClick={() => navigate('/patients/new')}\n            className=\"flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group\"\n          >\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-900/60 transition-colors\">\n              <i className=\"fas fa-user-plus text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <span className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center\">\n              {t('addPatient', 'Add Patient')}\n            </span>\n          </button>\n\n          <button\n            onClick={() => navigate('/patients/search')}\n            className=\"flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors group\"\n          >\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg group-hover:bg-purple-200 dark:group-hover:bg-purple-900/60 transition-colors\">\n              <i className=\"fas fa-search text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <span className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center\">\n              {t('advancedSearch', 'Advanced Search')}\n            </span>\n          </button>\n\n          <button\n            onClick={() => navigate('/appointments')}\n            className=\"flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors group\"\n          >\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-900/60 transition-colors\">\n              <i className=\"fas fa-calendar-plus text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <span className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center\">\n              {t('scheduleAppointment', 'Schedule Appointment')}\n            </span>\n          </button>\n\n          <button\n            onClick={() => navigate('/forms')}\n            className=\"flex flex-col items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors group\"\n          >\n            <div className=\"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg group-hover:bg-orange-200 dark:group-hover:bg-orange-900/60 transition-colors\">\n              <i className=\"fas fa-file-medical text-orange-600 dark:text-orange-400 text-xl\"></i>\n            </div>\n            <span className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center\">\n              {t('assessmentForms', 'Assessment Forms')}\n            </span>\n          </button>\n\n          <button\n            onClick={() => navigate('/reports')}\n            className=\"flex flex-col items-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-900/30 transition-colors group\"\n          >\n            <div className=\"p-3 bg-indigo-100 dark:bg-indigo-900/40 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-900/60 transition-colors\">\n              <i className=\"fas fa-chart-bar text-indigo-600 dark:text-indigo-400 text-xl\"></i>\n            </div>\n            <span className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center\">\n              {t('reports', 'Reports')}\n            </span>\n          </button>\n\n          <button\n            onClick={() => {\n              toast.success(t('exportingPatients', 'Exporting patient list...'));\n              console.log('Exporting patients');\n            }}\n            className=\"flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-900/20 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-900/30 transition-colors group\"\n          >\n            <div className=\"p-3 bg-gray-100 dark:bg-gray-900/40 rounded-lg group-hover:bg-gray-200 dark:group-hover:bg-gray-900/60 transition-colors\">\n              <i className=\"fas fa-download text-gray-600 dark:text-gray-400 text-xl\"></i>\n            </div>\n            <span className=\"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center\">\n              {t('exportList', 'Export List')}\n            </span>\n          </button>\n        </div>\n      </div>\n\n      {/* Filters and Search */}\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('search', 'Search')}\n            </label>\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                placeholder={t('searchPatients', 'Search patients...')}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n              />\n              <i className=\"fas fa-search absolute left-3 top-3 text-gray-400\"></i>\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('status', 'Status')}\n            </label>\n            <select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"all\">{t('allStatuses', 'All Statuses')}</option>\n              <option value=\"active\">{t('active', 'Active')}</option>\n              <option value=\"inactive\">{t('inactive', 'Inactive')}</option>\n              <option value=\"discharged\">{t('discharged', 'Discharged')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              {t('sortBy', 'Sort By')}\n            </label>\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"name\">{t('name', 'Name')}</option>\n              <option value=\"age\">{t('age', 'Age')}</option>\n              <option value=\"lastVisit\">{t('lastVisit', 'Last Visit')}</option>\n              <option value=\"progress\">{t('progress', 'Progress')}</option>\n            </select>\n          </div>\n\n          <div className=\"flex items-end\">\n            <button className=\"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\">\n              <i className=\"fas fa-filter mr-2\"></i>\n              {t('advancedFilters', 'Advanced Filters')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Patients Table */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('condition', 'Condition')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('therapist', 'Therapist')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('progress', 'Progress')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('lastVisit', 'Last Visit')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('nextAppointment', 'Next Appointment')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {loading ? (\n                <tr>\n                  <td colSpan=\"6\" className=\"px-6 py-12 text-center\">\n                    <div className=\"flex justify-center items-center\">\n                      <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n                      <span className=\"ml-3 text-gray-600 dark:text-gray-400\">\n                        {t('loadingPatients', 'Loading patients...')}\n                      </span>\n                    </div>\n                  </td>\n                </tr>\n              ) : filteredPatients.map((patient) => (\n                <tr\n                  key={patient.id}\n                  className=\"hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer\"\n                  onClick={() => navigate(`/patients/${patient.id}`)}\n                >\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"flex-shrink-0 h-10 w-10\">\n                        <div className=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                          <i className={`fas fa-${patient.gender === 'male' ? 'mars' : 'venus'} text-gray-600 dark:text-gray-400`}></i>\n                        </div>\n                      </div>\n                      <div className=\"ml-4\">\n                        <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                          {isRTL ? patient.name : patient.nameEn}\n                        </div>\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {t('age', 'Age')}: {patient.age} {t('years', 'years')}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className=\"px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full\">\n                      {conditionLabels[patient.condition]}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {patient.therapist}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center\">\n                      <div className=\"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2\">\n                        <div\n                          className={`h-2 rounded-full ${getProgressColor(patient.progress)}`}\n                          style={{ width: `${patient.progress}%` }}\n                        />\n                      </div>\n                      <span className=\"text-sm text-gray-900 dark:text-white\">{patient.progress}%</span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {new Date(patient.lastVisit).toLocaleDateString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                    {patient.nextAppointment ? new Date(patient.nextAppointment).toLocaleDateString() : '-'}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusLabels[patient.status].color}`}>\n                      {statusLabels[patient.status].label}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={(e) => handleViewPatient(patient.id, e)}\n                        className=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\"\n                        title={t('viewPatient', 'View Patient')}\n                      >\n                        <i className=\"fas fa-eye\"></i>\n                      </button>\n                      <button\n                        onClick={(e) => handleEditPatient(patient.id, e)}\n                        className=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300\"\n                        title={t('editPatient', 'Edit Patient')}\n                      >\n                        <i className=\"fas fa-edit\"></i>\n                      </button>\n                      <button\n                        onClick={(e) => handleDeletePatient(patient.id, e)}\n                        className=\"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300\"\n                        title={t('deletePatient', 'Delete Patient')}\n                      >\n                        <i className=\"fas fa-trash\"></i>\n                      </button>\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n\n        {!loading && filteredPatients.length === 0 && (\n          <div className=\"text-center py-12\">\n            <i className=\"fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4\"></i>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              {t('noPatientsFound', 'No patients found')}\n            </h3>\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              {t('tryAdjustingFilters', 'Try adjusting your search or filters')}\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Patients;\n"], "names": ["Patients", "t", "isRTL", "useLanguage", "navigate", "useNavigate", "location", "useLocation", "searchTerm", "setSearchTerm", "useState", "filterStatus", "setFilterStatus", "sortBy", "setSortBy", "patients", "setPatients", "loading", "setLoading", "useEffect", "loadPatients", "pathname", "async", "response", "api", "get", "success", "Error", "message", "data", "error", "console", "toast", "<PERSON><PERSON><PERSON><PERSON>", "autism", "cerebral_palsy", "down_syndrome", "intellectual_disability", "spina_bifida", "statusLabels", "active", "label", "color", "inactive", "discharged", "filteredPatients", "filter", "patient", "matchesSearch", "name", "toLowerCase", "includes", "nameEn", "matchesStatus", "status", "sort", "a", "b", "localeCompare", "age", "Date", "lastVisit", "progress", "_jsxs", "className", "concat", "children", "_jsx", "onClick", "disabled", "title", "length", "p", "nextAppointment", "Math", "round", "reduce", "sum", "log", "type", "value", "onChange", "e", "target", "placeholder", "colSpan", "map", "id", "gender", "condition", "therapist", "style", "width", "toLocaleDateString", "handleViewPatient", "patientId", "stopPropagation", "handleEditPatient", "event", "window", "confirm", "delete", "handleDeletePatient"], "sourceRoot": ""}