"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[1534],{1534:(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var r=a(5043),s=a(7921),l=a(3216),i=a(6305),d=a(3768),n=a(579);const c=()=>{const{t:e,isRTL:t}=(0,s.o)(),a=(0,l.Zp)(),c=(0,l.zy)(),[o,x]=(0,r.useState)(""),[g,m]=(0,r.useState)("all"),[p,h]=(0,r.useState)("name"),[u,b]=(0,r.useState)([]),[y,f]=(0,r.useState)(!0);(0,r.useEffect)(()=>{j()},[]),(0,r.useEffect)(()=>{"/patients"===c.pathname&&j()},[c.pathname]);const j=async()=>{f(!0);try{const e=await i.mo.get("/patients");if(!e.success)throw new Error(e.message||"Failed to load patients");b(e.data)}catch(t){console.error("Error loading patients:",t),d.Ay.error(e("errorLoadingPatients","Error loading patients")),b([])}finally{f(!1)}},k={autism:e("autism","Autism Spectrum Disorder"),cerebral_palsy:e("cerebralPalsy","Cerebral Palsy"),down_syndrome:e("downSyndrome","Down Syndrome"),intellectual_disability:e("intellectualDisability","Intellectual Disability"),spina_bifida:e("spinaBifida","Spina Bifida")},v={active:{label:e("active","Active"),color:"bg-green-100 text-green-800"},inactive:{label:e("inactive","Inactive"),color:"bg-gray-100 text-gray-800"},discharged:{label:e("discharged","Discharged"),color:"bg-blue-100 text-blue-800"}},N=u.filter(e=>{const t=e.name.toLowerCase().includes(o.toLowerCase())||e.nameEn.toLowerCase().includes(o.toLowerCase()),a="all"===g||e.status===g;return t&&a}).sort((e,t)=>{switch(p){case"name":return e.name.localeCompare(t.name);case"age":return e.age-t.age;case"lastVisit":return new Date(t.lastVisit)-new Date(e.lastVisit);case"progress":return t.progress-e.progress;default:return 0}});return(0,n.jsxs)("div",{className:"p-6 ".concat(t?"font-arabic":"font-english"),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e("patients","Patients")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("patientsDesc","Manage patient records and treatment plans")})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsxs)("button",{onClick:j,disabled:y,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center disabled:opacity-50",title:e("refreshPatientList","Refresh Patient List"),children:[(0,n.jsx)("i",{className:"fas fa-sync-alt mr-2 ".concat(y?"animate-spin":"")}),e("refresh","Refresh")]}),(0,n.jsxs)("button",{onClick:()=>a("/patients/search"),className:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-search mr-2"}),e("advancedSearch","Advanced Search")]}),(0,n.jsxs)("button",{onClick:()=>a("/patients/new"),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-plus mr-2"}),e("addNewPatient","Add New Patient")]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6",children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full",children:(0,n.jsx)("i",{className:"fas fa-users text-blue-600 dark:text-blue-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("totalPatients","Total Patients")}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:u.length})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/30 rounded-full",children:(0,n.jsx)("i",{className:"fas fa-user-check text-green-600 dark:text-green-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("activePatients","Active Patients")}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:u.filter(e=>"active"===e.status).length})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full",children:(0,n.jsx)("i",{className:"fas fa-calendar-check text-yellow-600 dark:text-yellow-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("upcomingAppointments","Upcoming Appointments")}),(0,n.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:u.filter(e=>e.nextAppointment).length})]})]})}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full",children:(0,n.jsx)("i",{className:"fas fa-chart-line text-purple-600 dark:text-purple-400 text-xl"})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e("averageProgress","Average Progress")}),(0,n.jsxs)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[Math.round(u.reduce((e,t)=>e+t.progress,0)/u.length),"%"]})]})]})})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-6",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:[(0,n.jsx)("i",{className:"fas fa-bolt text-yellow-500 mr-2"}),e("quickActions","Quick Actions")]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4",children:[(0,n.jsxs)("button",{onClick:()=>a("/patients/new"),className:"flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors group",children:[(0,n.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-900/60 transition-colors",children:(0,n.jsx)("i",{className:"fas fa-user-plus text-blue-600 dark:text-blue-400 text-xl"})}),(0,n.jsx)("span",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center",children:e("addPatient","Add Patient")})]}),(0,n.jsxs)("button",{onClick:()=>a("/patients/search"),className:"flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors group",children:[(0,n.jsx)("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/40 rounded-lg group-hover:bg-purple-200 dark:group-hover:bg-purple-900/60 transition-colors",children:(0,n.jsx)("i",{className:"fas fa-search text-purple-600 dark:text-purple-400 text-xl"})}),(0,n.jsx)("span",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center",children:e("advancedSearch","Advanced Search")})]}),(0,n.jsxs)("button",{onClick:()=>a("/appointments"),className:"flex flex-col items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors group",children:[(0,n.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/40 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-900/60 transition-colors",children:(0,n.jsx)("i",{className:"fas fa-calendar-plus text-green-600 dark:text-green-400 text-xl"})}),(0,n.jsx)("span",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center",children:e("scheduleAppointment","Schedule Appointment")})]}),(0,n.jsxs)("button",{onClick:()=>a("/forms"),className:"flex flex-col items-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg hover:bg-orange-100 dark:hover:bg-orange-900/30 transition-colors group",children:[(0,n.jsx)("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/40 rounded-lg group-hover:bg-orange-200 dark:group-hover:bg-orange-900/60 transition-colors",children:(0,n.jsx)("i",{className:"fas fa-file-medical text-orange-600 dark:text-orange-400 text-xl"})}),(0,n.jsx)("span",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center",children:e("assessmentForms","Assessment Forms")})]}),(0,n.jsxs)("button",{onClick:()=>a("/reports"),className:"flex flex-col items-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg hover:bg-indigo-100 dark:hover:bg-indigo-900/30 transition-colors group",children:[(0,n.jsx)("div",{className:"p-3 bg-indigo-100 dark:bg-indigo-900/40 rounded-lg group-hover:bg-indigo-200 dark:group-hover:bg-indigo-900/60 transition-colors",children:(0,n.jsx)("i",{className:"fas fa-chart-bar text-indigo-600 dark:text-indigo-400 text-xl"})}),(0,n.jsx)("span",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center",children:e("reports","Reports")})]}),(0,n.jsxs)("button",{onClick:()=>{d.Ay.success(e("exportingPatients","Exporting patient list...")),console.log("Exporting patients")},className:"flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-900/20 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-900/30 transition-colors group",children:[(0,n.jsx)("div",{className:"p-3 bg-gray-100 dark:bg-gray-900/40 rounded-lg group-hover:bg-gray-200 dark:group-hover:bg-gray-900/60 transition-colors",children:(0,n.jsx)("i",{className:"fas fa-download text-gray-600 dark:text-gray-400 text-xl"})}),(0,n.jsx)("span",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white text-center",children:e("exportList","Export List")})]})]})]}),(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600 mb-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("search","Search")}),(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)("input",{type:"text",value:o,onChange:e=>x(e.target.value),placeholder:e("searchPatients","Search patients..."),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"}),(0,n.jsx)("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("status","Status")}),(0,n.jsxs)("select",{value:g,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"all",children:e("allStatuses","All Statuses")}),(0,n.jsx)("option",{value:"active",children:e("active","Active")}),(0,n.jsx)("option",{value:"inactive",children:e("inactive","Inactive")}),(0,n.jsx)("option",{value:"discharged",children:e("discharged","Discharged")})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:e("sortBy","Sort By")}),(0,n.jsxs)("select",{value:p,onChange:e=>h(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,n.jsx)("option",{value:"name",children:e("name","Name")}),(0,n.jsx)("option",{value:"age",children:e("age","Age")}),(0,n.jsx)("option",{value:"lastVisit",children:e("lastVisit","Last Visit")}),(0,n.jsx)("option",{value:"progress",children:e("progress","Progress")})]})]}),(0,n.jsx)("div",{className:"flex items-end",children:(0,n.jsxs)("button",{className:"w-full px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-filter mr-2"}),e("advancedFilters","Advanced Filters")]})})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,n.jsx)("div",{className:"overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full",children:[(0,n.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("patient","Patient")}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("condition","Condition")}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("therapist","Therapist")}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("progress","Progress")}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("lastVisit","Last Visit")}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("nextAppointment","Next Appointment")}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("status","Status")}),(0,n.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e("actions","Actions")})]})}),(0,n.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:y?(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:"6",className:"px-6 py-12 text-center",children:(0,n.jsxs)("div",{className:"flex justify-center items-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,n.jsx)("span",{className:"ml-3 text-gray-600 dark:text-gray-400",children:e("loadingPatients","Loading patients...")})]})})}):N.map(r=>{return(0,n.jsxs)("tr",{className:"hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:()=>a("/patients/".concat(r.id)),children:[(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex-shrink-0 h-10 w-10",children:(0,n.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center",children:(0,n.jsx)("i",{className:"fas fa-".concat("male"===r.gender?"mars":"venus"," text-gray-600 dark:text-gray-400")})})}),(0,n.jsxs)("div",{className:"ml-4",children:[(0,n.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:t?r.name:r.nameEn}),(0,n.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:[e("age","Age"),": ",r.age," ",e("years","years")]})]})]})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full",children:k[r.condition]})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:r.therapist}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2",children:(0,n.jsx)("div",{className:"h-2 rounded-full ".concat((s=r.progress,s>=80?"bg-green-500":s>=60?"bg-blue-500":s>=40?"bg-yellow-500":"bg-red-500")),style:{width:"".concat(r.progress,"%")}})}),(0,n.jsxs)("span",{className:"text-sm text-gray-900 dark:text-white",children:[r.progress,"%"]})]})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:new Date(r.lastVisit).toLocaleDateString()}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white",children:r.nextAppointment?new Date(r.nextAppointment).toLocaleDateString():"-"}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(v[r.status].color),children:v[r.status].label})}),(0,n.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("button",{onClick:e=>{return t=r.id,e.stopPropagation(),void a("/patients/".concat(t));var t},className:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300",title:e("viewPatient","View Patient"),children:(0,n.jsx)("i",{className:"fas fa-eye"})}),(0,n.jsx)("button",{onClick:e=>{return t=r.id,e.stopPropagation(),void a("/patients/".concat(t,"/edit"));var t},className:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300",title:e("editPatient","Edit Patient"),children:(0,n.jsx)("i",{className:"fas fa-edit"})}),(0,n.jsx)("button",{onClick:t=>(async(t,a)=>{if(a.stopPropagation(),window.confirm(e("confirmDeletePatient","Are you sure you want to delete this patient?")))try{const a=await i.mo.delete("/patients/".concat(t));if(!a.success)throw new Error(a.message||"Failed to delete patient");d.Ay.success(e("patientDeleted","Patient deleted successfully")),j()}catch(r){console.error("Error deleting patient:",r),d.Ay.error(e("errorDeletingPatient","Error deleting patient"))}})(r.id,t),className:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300",title:e("deletePatient","Delete Patient"),children:(0,n.jsx)("i",{className:"fas fa-trash"})})]})})]},r.id);var s})})]})}),!y&&0===N.length&&(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("i",{className:"fas fa-search text-4xl text-gray-300 dark:text-gray-600 mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noPatientsFound","No patients found")}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("tryAdjustingFilters","Try adjusting your search or filters")})]})]})]})}}}]);
//# sourceMappingURL=1534.29570f94.chunk.js.map