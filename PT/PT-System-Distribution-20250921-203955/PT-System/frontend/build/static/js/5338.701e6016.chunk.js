"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[5338],{5338:(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var i=a(5043),s=a(3216),n=a(5475),r=a(7921),o=a(1145),l=a(579);const d=()=>{var e;const{t:t,isRTL:a}=(0,r.o)(),d=(0,s.Zp)(),c=(0,s.zy)(),[m]=(0,n.ok)(),[p,g]=(0,i.useState)(null),[h,u]=(0,i.useState)(null);(0,i.useEffect)(()=>{var e,t;if(null!==(e=c.state)&&void 0!==e&&e.patient&&null!==(t=c.state)&&void 0!==t&&t.fromPatientProfile){var a,i,s;const e=c.state.patient;u(e),g({patientId:e._id||e.id,patientName:e.name||"".concat(e.firstName," ").concat(e.lastName),therapist:(null===(a=e.therapyInfo)||void 0===a?void 0:a.primaryTherapist)||(null===(i=e.primaryTherapist)||void 0===i?void 0:i.firstName)+" "+(null===(s=e.primaryTherapist)||void 0===s?void 0:s.lastName)||"",sessionDate:c.state.defaultDate||(new Date).toISOString().split("T")[0],sessionTime:(new Date).toLocaleTimeString("en-US",{hour12:!1,hour:"2-digit",minute:"2-digit"}),facility:"PT Clinic"})}else{const e=m.get("patientId"),t=m.get("patientName"),a=m.get("therapist"),i=m.get("date");e&&t&&g({patientId:e,patientName:decodeURIComponent(t),therapist:a?decodeURIComponent(a):"",sessionDate:i||(new Date).toISOString().split("T")[0],sessionTime:(new Date).toLocaleTimeString("en-US",{hour12:!1,hour:"2-digit",minute:"2-digit"}),facility:"PT Clinic"})}},[m,c.state]);const x=()=>{d("/patients/".concat((null===p||void 0===p?void 0:p.patientId)||""))};return(0,l.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("button",{onClick:x,className:"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors",children:(0,l.jsx)("i",{className:"fas fa-arrow-".concat(a?"right":"left"," text-gray-600 dark:text-gray-400")})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:t("dailyProgress","Daily Progress")}),(null===p||void 0===p?void 0:p.patientName)&&(0,l.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:[t("patient","Patient"),": ",p.patientName," \u2022 ",t("date","Date"),": ",p.sessionDate]})]})]}),(0,l.jsx)("div",{className:"flex space-x-3",children:(0,l.jsxs)("button",{onClick:x,className:"px-4 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-times mr-2"}),t("cancel","Cancel")]})})]})}),(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:(0,l.jsx)(o.A,{patientData:h,fromPatientProfile:null===(e=c.state)||void 0===e?void 0:e.fromPatientProfile,initialData:p,onSubmit:e=>{console.log("Daily Progress submitted:",e),d("/patients/".concat((null===p||void 0===p?void 0:p.patientId)||""))},onCancel:x})})]})}}}]);
//# sourceMappingURL=5338.701e6016.chunk.js.map