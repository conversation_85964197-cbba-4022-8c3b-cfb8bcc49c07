{"version": 3, "file": "static/js/3191.dd85bbdc.chunk.js", "mappings": "iOAKA,MAoiCA,EApiCwBA,IAA0B,IAAzB,QAAEC,EAAO,OAAEC,GAAQF,EAC1C,MAAM,EAAEG,IAAMC,EAAAA,EAAAA,OACR,SAAEC,IAAaC,EAAAA,EAAAA,MACdC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAS,IACxCC,EAAcC,IAAmBF,EAAAA,EAAAA,UAAS,CAC/CG,KAAM,GACNC,WAAY,GACZC,YAAa,GACbC,kBAAmB,GACnBC,KAAM,WACNC,SAAU,UACVC,OAAQ,CACNC,OAAQ,CACNC,UAAU,EACVC,iBAAiB,EACjBC,kBAAkB,EAClBC,YAAa,GACbC,kBAAmB,IAErBC,YAAa,CACXC,oBAAoB,EACpBC,gBAAgB,EAChBC,mBAAmB,EACnBC,cAAc,EACdC,aAAc,IAEhBC,SAAU,CACRC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,SAAS,EACTC,qBAAqB,EACrBC,mBAAoB,IAEtBC,QAAS,CACPC,SAAU,MACVC,cAAc,EACdL,SAAS,EACTM,QAAS,GACTP,cAAc,EACdQ,WAAW,EACXC,eAAgB,SAElBC,OAAQ,CACNC,kBAAkB,EAClBC,iBAAiB,EACjBC,WAAW,EACXC,eAAe,EACfC,WAAY,GACZC,iBAAkB,IAEpBC,WAAY,CACVC,gBAAgB,EAChBC,iBAAiB,EACjBC,YAAY,EACZC,4BAA4B,IAGhCC,QAAS,CACPC,YAAa,CACXC,QAAS,UACTC,UAAW,UACXC,OAAQ,WAEVC,MAAO,CACLH,QAAS,QACTI,OAAQ,oBAEVC,OAAQ,CACNC,YAAa,WACbC,SAAU,KACVC,QAAS,CAAEC,IAAK,GAAIC,OAAQ,GAAIC,KAAM,GAAIC,MAAO,QAKjDC,EAAQ,CACZ,CAAEC,GAAI,EAAGC,MAAO,aAAcC,KAAM,sBACpC,CAAEF,GAAI,EAAGC,MAAO,gBAAiBC,KAAM,kBACvC,CAAEF,GAAI,EAAGC,MAAO,eAAgBC,KAAM,eACtC,CAAEF,GAAI,EAAGC,MAAO,WAAYC,KAAM,eAClC,CAAEF,GAAI,EAAGC,MAAO,UAAWC,KAAM,sBACjC,CAAEF,GAAI,EAAGC,MAAO,sBAAuBC,KAAM,uBAC7C,CAAEF,GAAI,EAAGC,MAAO,UAAWC,KAAM,mBAG7BC,EAAoBA,CAACC,EAASC,EAAOC,KAEvCrE,EADEmE,EACcG,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP,CAACH,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAKH,IAAQ,IAChB,CAACC,GAAQC,MAIGC,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP,CAACF,GAAQC,MAKTG,EAAqBA,CAACL,EAASM,EAAYL,EAAOC,KACtDrE,EAAgBsE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP/D,QAAMgE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDD,EAAK/D,QAAM,IACd,CAAC4D,IAAOI,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAK/D,OAAO4D,IAAQ,IACvB,CAACM,GAA0BL,GAAQK,GAAUF,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACxCD,EAAK/D,OAAO4D,GAASM,IAAW,IACnC,CAACL,GAAQC,IACPA,UAMNK,EAAiBA,KACrB,MAAMC,EAAW,CACfC,MAAO,GACPC,YAAa,GACbC,UAAW,OACXC,UAAU,GAGZ/E,EAAgBsE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP/D,QAAMgE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDD,EAAK/D,QAAM,IACdO,aAAWyD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACND,EAAK/D,OAAOO,aAAW,IAC1BK,aAAc,IAAImD,EAAK/D,OAAOO,YAAYK,aAAcwD,WAmB1DK,EAAuBA,KAC3B,MAAMC,EAAa,CACjBC,KAAM,GACNjF,KAAM,GACNC,WAAY,GACZC,YAAa,GACbC,kBAAmB,GACnB+E,aAAc,EACdC,SAAS,GAGXpF,EAAgBsE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP/D,QAAMgE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDD,EAAK/D,QAAM,IACda,UAAQmD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAK/D,OAAOa,UAAQ,IACvBQ,mBAAoB,IAAI0C,EAAK/D,OAAOa,SAASQ,mBAAoBqD,WA2xBzE,OACEI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iFAAgFC,UAC7FC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qFAAoFC,SAAA,EAEjGC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sFAAqFC,SAAA,EAClGF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,oBAAqB,0BAE1B6F,EAAAA,EAAAA,KAAA,UACEI,QAASnG,EACTgG,UAAU,6DAA4DC,UAEtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,+BAKjBD,EAAAA,EAAAA,KAAA,OAAKC,UAAU,0DAAyDC,UACtEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oCAAmCC,SAC/CzB,EAAM4B,IAAI,CAACC,EAAMC,KAChBJ,EAAAA,EAAAA,MAAA,OAAmBF,UAAU,oBAAmBC,SAAA,EAC9CF,EAAAA,EAAAA,KAAA,OACEC,UAAS,6EAAAO,OACPjG,GAAe+F,EAAK5B,GAChB,yBACA,iEACHwB,UAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAWK,EAAK1B,UAErBoB,EAAAA,EAAAA,KAAA,QAAMC,UAAS,gBAAAO,OACbjG,GAAe+F,EAAK5B,GAChB,mCACA,oCACHwB,SACAI,EAAK3B,QAEP4B,EAAQ9B,EAAMgC,OAAS,IACtBT,EAAAA,EAAAA,KAAA,OAAKC,UAAS,kBAAAO,OACZjG,EAAc+F,EAAK5B,GACf,cACA,oCArBA4B,EAAK5B,UA8BrBsB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,+BAA8BC,SAryBzBQ,MACxB,OAAQnG,GACN,KAAK,EACH,OACE4F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,mBAAoB,wBAGzBgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E/F,EAAE,eAAgB,iBAAiB,SAEtC6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,OACLgE,MAAOtE,EAAaE,KACpB+F,SAAWC,GAAM/B,EAAkB,KAAM,OAAQ+B,EAAEC,OAAO7B,OAC1DiB,UAAU,kKACVa,YAAY,4BAIhBX,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,qBAAsB,6BAE3B6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,OACLgE,MAAOtE,EAAaG,WACpB8F,SAAWC,GAAM/B,EAAkB,KAAM,aAAc+B,EAAEC,OAAO7B,OAChEiB,UAAU,kKACVa,YAAY,mFACZC,IAAI,eAKVZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E/F,EAAE,cAAe,eAAe,SAEnC6F,EAAAA,EAAAA,KAAA,YACEhB,MAAOtE,EAAaI,YACpB6F,SAAWC,GAAM/B,EAAkB,KAAM,cAAe+B,EAAEC,OAAO7B,OACjEgC,KAAM,EACNf,UAAU,kKACVa,YAAY,mCAIhBX,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,oBAAqB,2BAE1B6F,EAAAA,EAAAA,KAAA,YACEhB,MAAOtE,EAAaK,kBACpB4F,SAAWC,GAAM/B,EAAkB,KAAM,oBAAqB+B,EAAEC,OAAO7B,OACvEgC,KAAM,EACNf,UAAU,kKACVa,YAAY,mFACZC,IAAI,eAKVZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,eAAgB,oBAErBgG,EAAAA,EAAAA,MAAA,UACEnB,MAAOtE,EAAaM,KACpB2F,SAAWC,GAAM/B,EAAkB,KAAM,OAAQ+B,EAAEC,OAAO7B,OAC1DiB,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,WAAUkB,SAAE/F,EAAE,WAAY,eACxC6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,UAASkB,SAAE/F,EAAE,UAAW,cACtC6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,UAASkB,SAAE/F,EAAE,UAAW,cACtC6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,YAAWkB,SAAE/F,EAAE,YAAa,gBAC1C6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,aAAYkB,SAAE/F,EAAE,aAAc,iBAC5C6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,SAAQkB,SAAE/F,EAAE,SAAU,mBAIxCgG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,WAAY,eAEjBgG,EAAAA,EAAAA,MAAA,UACEnB,MAAOtE,EAAaO,SACpB0F,SAAWC,GAAM/B,EAAkB,KAAM,WAAY+B,EAAEC,OAAO7B,OAC9DiB,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,UAASkB,SAAE/F,EAAE,UAAW,cACtC6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,mBAAkBkB,SAAE/F,EAAE,kBAAmB,uBACvD6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,uBAAsBkB,SAAE/F,EAAE,sBAAuB,2BAC/D6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,iBAAgBkB,SAAE/F,EAAE,gBAAiB,qBACnD6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,uBAAsBkB,SAAE/F,EAAE,sBAAuB,2BAC/D6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,mBAAkBkB,SAAE/F,EAAE,kBAAmB,kCAOnE,KAAK,EACH,OACEgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,sBAAuB,2BAG5BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOC,OAAOC,SACpCuF,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,WAAYyB,EAAEC,OAAOI,SACzEhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,WAAY,mBAInBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOC,OAAOE,gBACpCsF,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,kBAAmByB,EAAEC,OAAOI,SAChFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,kBAAmB,kCAI1BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOC,OAAOG,iBACpCqF,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,mBAAoByB,EAAEC,OAAOI,SACjFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,mBAAoB,+BAK7BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,cAAe,mBAEpB6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,OACLgE,MAAOtE,EAAaQ,OAAOC,OAAOI,YAClCoF,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,cAAeyB,EAAEC,OAAO7B,OAC5EiB,UAAU,kKACVa,YAAY,gBAIhBX,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,oBAAqB,4BAE1B6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,OACLgE,MAAOtE,EAAaQ,OAAOC,OAAOK,kBAClCmF,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,oBAAqByB,EAAEC,OAAO7B,OAClFiB,UAAU,kKACVa,YAAY,uCACZC,IAAI,oBAQlB,KAAK,EACH,OACEZ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,qBAAsB,0BAG3BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOO,YAAYC,mBACzCiF,SAAWC,GAAMzB,EAAmB,cAAe,KAAM,qBAAsByB,EAAEC,OAAOI,SACxFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,qBAAsB,8BAI7BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOO,YAAYE,eACzCgF,SAAWC,GAAMzB,EAAmB,cAAe,KAAM,iBAAkByB,EAAEC,OAAOI,SACpFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,iBAAkB,0BAIzBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOO,YAAYG,kBACzC+E,SAAWC,GAAMzB,EAAmB,cAAe,KAAM,oBAAqByB,EAAEC,OAAOI,SACvFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,oBAAqB,oCAI5BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOO,YAAYI,aACzC8E,SAAWC,GAAMzB,EAAmB,cAAe,KAAM,eAAgByB,EAAEC,OAAOI,SAClFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,eAAgB,kCAKzBgG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,eAAgB,oBAErB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qCAAoCC,SAChDxF,EAAaQ,OAAOO,YAAYK,aAAauE,IAAI,CAACtB,EAAOwB,KACxDJ,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,sEAAqEC,SAAA,EAC9FF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,OACLgE,MAAOD,EAAMQ,MACboB,SAAWC,IACT,MAAMM,EAAY,IAAIxG,EAAaQ,OAAOO,YAAYK,cACtDoF,EAAUX,GAAOhB,MAAQqB,EAAEC,OAAO7B,MAClCG,EAAmB,cAAe,KAAM,eAAgB+B,IAE1DJ,YAAY,cACZb,UAAU,mHAEZD,EAAAA,EAAAA,KAAA,UACEI,QAASA,IA5UJG,KACzB5F,EAAgBsE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP/D,QAAMgE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDD,EAAK/D,QAAM,IACdO,aAAWyD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACND,EAAK/D,OAAOO,aAAW,IAC1BK,aAAcmD,EAAK/D,OAAOO,YAAYK,aAAaqF,OAAO,CAACC,EAAGC,IAAMA,IAAMd,WAqU7Ce,CAAkBf,GACjCN,UAAU,kCAAiCC,UAE3CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BAhBPM,OAqBdJ,EAAAA,EAAAA,MAAA,UACEC,QAASf,EACTY,UAAU,0EAAyEC,SAAA,EAEnFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ9F,EAAE,WAAY,yBAO3B,KAAK,EACH,OACEgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,wBAAyB,6BAG9BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOa,SAASC,gBACtC2E,SAAWC,GAAMzB,EAAmB,WAAY,KAAM,kBAAmByB,EAAEC,OAAOI,SAClFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,kBAAmB,2BAI1BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOa,SAASE,gBACtC0E,SAAWC,GAAMzB,EAAmB,WAAY,KAAM,kBAAmByB,EAAEC,OAAOI,SAClFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,kBAAmB,0BAI1BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOa,SAASG,aACtCyE,SAAWC,GAAMzB,EAAmB,WAAY,KAAM,eAAgByB,EAAEC,OAAOI,SAC/EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,eAAgB,uBAIvBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOa,SAASI,cACtCwE,SAAWC,GAAMzB,EAAmB,WAAY,KAAM,gBAAiByB,EAAEC,OAAOI,SAChFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,gBAAiB,yBAIxBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOa,SAASK,aACtCuE,SAAWC,GAAMzB,EAAmB,WAAY,KAAM,eAAgByB,EAAEC,OAAOI,SAC/EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,eAAgB,uBAIvBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOa,SAASO,oBACtCqE,SAAWC,GAAMzB,EAAmB,WAAY,KAAM,sBAAuByB,EAAEC,OAAOI,SACtFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,sBAAuB,kCAKhCgG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,yDAAwDC,SACnE/F,EAAE,qBAAsB,0BAE3B6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qCAAoCC,SAChDxF,EAAaQ,OAAOa,SAASQ,mBAAmB8D,IAAI,CAACkB,EAAShB,KAC7DJ,EAAAA,EAAAA,MAAA,OAAiBF,UAAU,0CAAyCC,SAAA,EAClEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BC,SAAA,EAC1CF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,OACLgE,MAAOuC,EAAQ1B,KACfc,SAAWC,IACT,MAAMY,EAAc,IAAI9G,EAAaQ,OAAOa,SAASQ,oBACrDiF,EAAYjB,GAAOV,KAAOe,EAAEC,OAAO7B,MACnCG,EAAmB,WAAY,KAAM,qBAAsBqC,IAE7DV,YAAY,eACZb,UAAU,4GAEZD,EAAAA,EAAAA,KAAA,SACEhF,KAAK,OACLgE,MAAOuC,EAAQ3G,KACf+F,SAAWC,IACT,MAAMY,EAAc,IAAI9G,EAAaQ,OAAOa,SAASQ,oBACrDiF,EAAYjB,GAAO3F,KAAOgG,EAAEC,OAAO7B,MACnCG,EAAmB,WAAY,KAAM,qBAAsBqC,IAE7DV,YAAY,eACZb,UAAU,+GAGdE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,SACLgE,MAAOuC,EAAQzB,aACfa,SAAWC,IACT,MAAMY,EAAc,IAAI9G,EAAaQ,OAAOa,SAASQ,oBACrDiF,EAAYjB,GAAOT,aAAe2B,WAAWb,EAAEC,OAAO7B,QAAU,EAChEG,EAAmB,WAAY,KAAM,qBAAsBqC,IAE7DV,YAAY,QACZb,UAAU,iHAEZD,EAAAA,EAAAA,KAAA,UACEI,QAASA,IA1bAG,KAC/B5F,EAAgBsE,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACfD,GAAI,IACP/D,QAAMgE,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACDD,EAAK/D,QAAM,IACda,UAAQmD,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GACHD,EAAK/D,OAAOa,UAAQ,IACvBQ,mBAAoB0C,EAAK/D,OAAOa,SAASQ,mBAAmB4E,OAAO,CAACC,EAAGC,IAAMA,IAAMd,WAmbpDmB,CAAwBnB,GACvCN,UAAU,kCAAiCC,UAE3CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gCAzCTM,OA+CdJ,EAAAA,EAAAA,MAAA,UACEC,QAAST,EACTM,UAAU,0EAAyEC,SAAA,EAEnFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ9F,EAAE,aAAc,2BAO7B,KAAK,EACH,OACEgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,uBAAwB,4BAG7BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,WAAY,eAEjBgG,EAAAA,EAAAA,MAAA,UACEnB,MAAOtE,EAAaQ,OAAOsB,QAAQC,SACnCkE,SAAWC,GAAMzB,EAAmB,UAAW,KAAM,WAAYyB,EAAEC,OAAO7B,OAC1EiB,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,MAAKkB,SAAC,uBACpBF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,MAAKkB,SAAC,qBACpBF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,MAAKkB,SAAC,sBAIxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,kEAAiEC,SAAA,CAC/E/F,EAAE,UAAW,YAAY,WAE5B6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,SACLgE,MAAOtE,EAAaQ,OAAOsB,QAAQG,QACnCgE,SAAWC,GAAMzB,EAAmB,UAAW,KAAM,UAAWsC,WAAWb,EAAEC,OAAO7B,QAAU,GAC9FiB,UAAU,kKACV0B,IAAI,IACJC,IAAI,MACJtB,KAAK,YAITH,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,iBAAkB,sBAEvBgG,EAAAA,EAAAA,MAAA,UACEnB,MAAOtE,EAAaQ,OAAOsB,QAAQK,eACnC8D,SAAWC,GAAMzB,EAAmB,UAAW,KAAM,iBAAkByB,EAAEC,OAAO7B,OAChFiB,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,QAAOkB,SAAE/F,EAAE,QAAS,YAClC6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,QAAOkB,SAAE/F,EAAE,QAAS,YAClC6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,OAAMkB,SAAE/F,EAAE,OAAQ,uBAKtCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOsB,QAAQE,aACrCiE,SAAWC,GAAMzB,EAAmB,UAAW,KAAM,eAAgByB,EAAEC,OAAOI,SAC9EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,eAAgB,uBAIvBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOsB,QAAQH,QACrCsE,SAAWC,GAAMzB,EAAmB,UAAW,KAAM,UAAWyB,EAAEC,OAAOI,SACzEhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,UAAW,kBAIlBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOsB,QAAQJ,aACrCuE,SAAWC,GAAMzB,EAAmB,UAAW,KAAM,eAAgByB,EAAEC,OAAOI,SAC9EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,eAAgB,uBAIvBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOsB,QAAQI,UACrC+D,SAAWC,GAAMzB,EAAmB,UAAW,KAAM,YAAayB,EAAEC,OAAOI,SAC3EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,YAAa,4BAQ9B,KAAK,EACH,OACEgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,sBAAuB,0BAG5BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oDAAmDC,SAC9D/F,EAAE,gBAAiB,qBAGtBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAO4B,OAAOC,iBACpC4D,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,mBAAoByB,EAAEC,OAAOI,SACjFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,mBAAoB,4BAI3BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAO4B,OAAOE,gBACpC2D,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,kBAAmByB,EAAEC,OAAOI,SAChFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,kBAAmB,2BAI1BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAO4B,OAAOG,UACpC0D,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,YAAayB,EAAEC,OAAOI,SAC1EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,YAAa,oBAIpBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAO4B,OAAOI,cACpCyD,SAAWC,GAAMzB,EAAmB,SAAU,KAAM,gBAAiByB,EAAEC,OAAOI,SAC9EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,gBAAiB,2BAK1BgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oDAAmDC,SAC9D/F,EAAE,oBAAqB,yBAG1BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOmC,WAAWC,eACxCqD,SAAWC,GAAMzB,EAAmB,aAAc,KAAM,iBAAkByB,EAAEC,OAAOI,SACnFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,iBAAkB,yBAIzBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOmC,WAAWE,gBACxCoD,SAAWC,GAAMzB,EAAmB,aAAc,KAAM,kBAAmByB,EAAEC,OAAOI,SACpFhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,kBAAmB,0BAI1BgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOmC,WAAWG,WACxCmD,SAAWC,GAAMzB,EAAmB,aAAc,KAAM,aAAcyB,EAAEC,OAAOI,SAC/EhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,aAAc,sBAIrBgG,EAAAA,EAAAA,MAAA,SAAOF,UAAU,oBAAmBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACEhF,KAAK,WACLiG,QAASvG,EAAaQ,OAAOmC,WAAWI,2BACxCkD,SAAWC,GAAMzB,EAAmB,aAAc,KAAM,6BAA8ByB,EAAEC,OAAOI,SAC/FhB,UAAU,uEAEZD,EAAAA,EAAAA,KAAA,QAAMC,UAAU,gDAA+CC,SAC5D/F,EAAE,6BAA8B,8CAQ/C,KAAK,EACH,OACEgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/F,EAAE,UAAW,cAGhBgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oDAAmDC,SAC9D/F,EAAE,cAAe,mBAGpBgG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,eAAgB,oBAErB6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,QACLgE,MAAOtE,EAAagD,QAAQC,YAAYC,QACxC+C,SAAWC,GAAMzB,EAAmB,UAAW,cAAe,UAAWyB,EAAEC,OAAO7B,OAClFiB,UAAU,wEAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,iBAAkB,sBAEvB6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,QACLgE,MAAOtE,EAAagD,QAAQC,YAAYE,UACxC8C,SAAWC,GAAMzB,EAAmB,UAAW,cAAe,YAAayB,EAAEC,OAAO7B,OACpFiB,UAAU,wEAIdE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,cAAe,mBAEpB6F,EAAAA,EAAAA,KAAA,SACEhF,KAAK,QACLgE,MAAOtE,EAAagD,QAAQC,YAAYG,OACxC6C,SAAWC,GAAMzB,EAAmB,UAAW,cAAe,SAAUyB,EAAEC,OAAO7B,OACjFiB,UAAU,2EAKhBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,oDAAmDC,SAC9D/F,EAAE,SAAU,aAGfgG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,cAAe,kBAEpBgG,EAAAA,EAAAA,MAAA,UACEnB,MAAOtE,EAAagD,QAAQO,OAAOC,YACnCyC,SAAWC,GAAMzB,EAAmB,UAAW,SAAU,cAAeyB,EAAEC,OAAO7B,OACjFiB,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,WAAUkB,SAAE/F,EAAE,WAAY,eACxC6F,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,YAAWkB,SAAE/F,EAAE,YAAa,sBAI9CgG,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,kEAAiEC,SAC/E/F,EAAE,WAAY,gBAEjBgG,EAAAA,EAAAA,MAAA,UACEnB,MAAOtE,EAAagD,QAAQO,OAAOE,SACnCwC,SAAWC,GAAMzB,EAAmB,UAAW,SAAU,WAAYyB,EAAEC,OAAO7B,OAC9EiB,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,KAAIkB,SAAC,QACnBF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,SAAQkB,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQhB,MAAM,QAAOkB,SAAC,yBAQpC,QACE,OACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BC,EAAAA,EAAAA,MAAA,KAAGF,UAAU,mCAAkCC,SAAA,CAAC,QACxC3F,EAAY,kDA0DrBmG,MAIHP,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sFAAqFC,SAAA,EAClGC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAM5F,EAAeqH,KAAKD,IAAI,EAAGrH,EAAc,IACxDuH,SAA0B,IAAhBvH,EACV0F,UAAU,0IAAyIC,SAAA,EAEnJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2BACZ9F,EAAE,WAAY,gBAGjBgG,EAAAA,EAAAA,MAAA,QAAMF,UAAU,2CAA0CC,SAAA,CACvD3F,EAAY,OAAKkE,EAAMgC,WAG1BT,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,SAC5B3F,EAAckE,EAAMgC,QACnBN,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAM5F,EAAeqH,KAAKF,IAAIlD,EAAMgC,OAAQlG,EAAc,IACnE0F,UAAU,6DAA4DC,SAAA,CAErE/F,EAAE,OAAQ,SACX6F,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gCAGfE,EAAAA,EAAAA,MAAA,UACEC,QAx1BK2B,UACjB,IACE,IAAKrH,EAAaE,KAAKoH,OAErB,YADAC,EAAAA,GAAMC,MAAM,6BAId,IAAKxH,EAAaI,YAAYkH,OAE5B,YADAC,EAAAA,GAAMC,MAAM,0CAIRhI,EAAOQ,GACbuH,EAAAA,GAAME,QAAQ,kCACdlI,GACF,CAAE,MAAOiI,GACPE,QAAQF,MAAM,yBAA0BA,GACxCD,EAAAA,GAAMC,MAAM,0BACd,GAu0BYjC,UAAU,+DAA8DC,SAAA,EAExEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ9F,EAAE,iBAAkB,iCCzoBrC,EA9YyBkI,KACvB,MAAOC,EAAWC,IAAgB9H,EAAAA,EAAAA,UAAS,KACpC+H,EAASC,IAAchI,EAAAA,EAAAA,WAAS,IAChCiI,EAAkBC,IAAuBlI,EAAAA,EAAAA,UAAS,OAClDmI,EAAiBC,IAAsBpI,EAAAA,EAAAA,WAAS,IAChD0G,EAAQ2B,IAAarI,EAAAA,EAAAA,UAAS,QAC/B,EAAEN,IAAMC,EAAAA,EAAAA,OACR,SAAEC,IAAaC,EAAAA,EAAAA,MACf,KAAEyI,IAASC,EAAAA,EAAAA,MAEjBC,EAAAA,EAAAA,WAAU,KACRC,KACC,CAAC/B,IAEJ,MAAM+B,EAAgBnB,UACpBU,GAAW,GACX,IACE,MAAMU,EAAc,IAAIC,gBACT,QAAXjC,GACFgC,EAAYE,OAAO,OAAQlC,GAG7B,MAAMmC,QAAiBC,MAAM,6BAAD/C,OAA8B2C,GAAe,CACvEK,QAAS,CACP,cAAgB,UAADhD,OAAYiD,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIJ,EAASK,GAIX,MAAM,IAAIC,MAAM,4BAJD,CACf,MAAMC,QAAaP,EAASQ,OAC5BvB,EAAasB,EAAKA,KACpB,CAGF,CAAE,MAAO3B,GACPE,QAAQF,MAAM,2BAA4BA,GAC1CD,EAAAA,GAAMC,MAAM,mCACd,CAAC,QACCO,GAAW,EACb,GA6FIsB,EAAmB/I,IACvB,OAAQA,GACN,IAAK,WACH,MAAO,sBACT,IAAK,UACH,MAAO,iBACT,IAAK,UACH,MAAO,kBACT,IAAK,YACH,MAAO,oBACT,IAAK,aACH,MAAO,kBACT,QACE,MAAO,gBAIPgJ,EAAgBhJ,IACpB,OAAQA,GACN,IAAK,WACH,MAAO,mEACT,IAAK,UACH,MAAO,uEACT,IAAK,UACH,MAAO,2EACT,IAAK,YACH,MAAO,+DACT,IAAK,aACH,MAAO,2EACT,QACE,MAAO,qEAIb,OAAIwH,GAEAxC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oCAAmCC,SAAA,EAChDC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,mDAAkDC,SAAA,EAC9DF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CACZ9F,EAAE,mBAAoB,yBAEzB6F,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjD/F,EAAE,uBAAwB,6EAI/BgG,EAAAA,EAAAA,MAAA,UACEC,QAASA,IAAMyC,GAAmB,GAClC5C,UAAU,kFAAiFC,SAAA,EAE3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ9F,EAAE,iBAAkB,0BAKzB6F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,qDAAoDC,UACjEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAiB,aAAW,OAAMC,SAC9C,CACC,CAAE+D,IAAK,MAAO1E,MAAO,gBAAiBX,KAAM,mBAC5C,CAAEqF,IAAK,WAAY1E,MAAO,WAAYX,KAAM,uBAC5C,CAAEqF,IAAK,UAAW1E,MAAO,UAAWX,KAAM,kBAC1C,CAAEqF,IAAK,UAAW1E,MAAO,UAAWX,KAAM,mBAC1C,CAAEqF,IAAK,YAAa1E,MAAO,YAAaX,KAAM,sBAC9CyB,IAAK6D,IACL/D,EAAAA,EAAAA,MAAA,UAEEC,QAASA,IAAM0C,EAAUoB,EAAID,KAC7BhE,UAAS,8DAAAO,OACPW,IAAW+C,EAAID,IACX,mDACA,0HACH/D,SAAA,EAEHF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAO,OAAK0D,EAAItF,KAAI,WACxBzE,EAAE+J,EAAI3E,MAAM4E,cAAcC,QAAQ,IAAK,IAAKF,EAAI3E,SAT5C2E,EAAID,cAiBnB9D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uDAAsDC,SAAA,CAClEoC,EAAUjC,IAAKgE,IACdlE,EAAAA,EAAAA,MAAA,OAEEF,UAAU,4HAA2HC,SAAA,EAGrIF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,oDAAmDC,UAChEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,mCAAkCC,SAAA,EAC/CC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,OAAKC,UAAS,kBAAAO,OAAoBwD,EAAaK,EAASrJ,MAAK,SAAQkF,UACnEF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAAO,OAAKuD,EAAgBM,EAASrJ,MAAK,iBAEjDmF,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SACnD,OAAb7F,GAAqBgK,EAASxJ,WAAawJ,EAASxJ,WAAawJ,EAASzJ,QAE7EoF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SACvC,OAAb7F,GAAqBgK,EAAStJ,kBAAoBsJ,EAAStJ,kBAAoBsJ,EAASvJ,oBAK9FuJ,EAASC,YACRtE,EAAAA,EAAAA,KAAA,QAAMC,UAAU,6GAA4GC,SACzH/F,EAAE,UAAW,mBAOtBgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKC,SAAA,EAClBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAE/F,EAAE,OAAQ,QAAQ,QACtE6F,EAAAA,EAAAA,KAAA,QAAMC,UAAS,yCAAAO,OAA2CwD,EAAaK,EAASrJ,OAAQkF,SACrF/F,EAAEkK,EAASrJ,KAAMqJ,EAASrJ,YAI/BmF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAE/F,EAAE,WAAY,YAAY,QAC9E6F,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACxD/F,EAAEkK,EAASpJ,SAAUoJ,EAASpJ,gBAInCkF,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAE/F,EAAE,aAAc,QAAQ,QAC5EgG,EAAAA,EAAAA,MAAA,QAAMF,UAAU,4CAA2CC,SAAA,CACxDmE,EAASE,WAAW,IAAEpK,EAAE,QAAS,gBAItCgG,EAAAA,EAAAA,MAAA,OAAKF,UAAU,+BAA8BC,SAAA,EAC3CC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,mCAAkCC,SAAA,CAAE/F,EAAE,UAAW,WAAW,QAC5E6F,EAAAA,EAAAA,KAAA,QAAMC,UAAU,4CAA2CC,SACxDmE,EAASG,iBAMhBrE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,CACvCmE,EAASnJ,OAAOmC,WAAWC,iBAC1B0C,EAAAA,EAAAA,KAAA,QAAMC,UAAU,iGAAgGC,SAAC,UAIlHmE,EAASnJ,OAAOmC,WAAWE,kBAC1ByC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,6FAA4FC,SAAC,WAI9GmE,EAASnJ,OAAOmC,WAAWI,6BAC1BuC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qGAAoGC,SAAC,kBAQ3HF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,YAAWC,UACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,MAAA,UACEC,QAASA,IA/QC2B,WACxB,UAEQwB,MAAM,6BAAD/C,OAA8BiE,EAAU,QAAQ,CACzDC,OAAQ,OACRlB,QAAS,CACP,cAAgB,UAADhD,OAAYiD,aAAaC,QAAQ,UAChD,eAAgB,sBAKpBiB,OAAOC,SAASC,KAAI,uCAAArE,OAA0CiE,EAChE,CAAE,MAAOvC,GACPE,QAAQF,MAAM,wBAAyBA,GACvCD,EAAAA,GAAMC,MAAM,yBACd,GA+P6B4C,CAAkBT,EAASU,KAC1C9E,UAAU,wFAAuFC,SAAA,EAEjGF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ9F,EAAE,cAAe,oBAGpB6F,EAAAA,EAAAA,KAAA,UACEI,QAASA,IApQO2B,WAC9B,IACE,MAAMuB,QAAiBC,MAAM,6BAAD/C,OAA8BiE,EAAU,cAAc,CAChFC,OAAQ,OACRlB,QAAS,CACP,cAAgB,UAADhD,OAAYiD,aAAaC,QAAQ,UAChD,eAAgB,sBAIpB,IAAIJ,EAASK,GAKX,MAAM,IAAIC,MAAM,sCAJGN,EAASQ,OAC5B7B,EAAAA,GAAME,QAAQ,oCACde,GAIJ,CAAE,MAAOhB,GACPE,QAAQF,MAAM,8BAA+BA,GAC7CD,EAAAA,GAAMC,MAAM,+BACd,GAgP6B8C,CAAwBX,EAASU,KAChD9E,UAAU,2HACVtB,MAAOxE,EAAE,YAAa,aAAa+F,UAEnCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAGboE,EAASC,YACTtE,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAtPA2B,WACzB,IASE,WARuBwB,MAAM,6BAAD/C,OAA8BiE,EAAU,gBAAgB,CAClFC,OAAQ,MACRlB,QAAS,CACP,cAAgB,UAADhD,OAAYiD,aAAaC,QAAQ,UAChD,eAAgB,uBAIPC,GAIX,MAAM,IAAIC,MAAM,4BAHhB3B,EAAAA,GAAME,QAAQ,2BACde,GAIJ,CAAE,MAAOhB,GACPE,QAAQF,MAAM,yBAA0BA,GACxCD,EAAAA,GAAMC,MAAM,oCACd,GAmO+B+C,CAAmBZ,EAASU,KAC3C9E,UAAU,iIACVtB,MAAOxE,EAAE,eAAgB,kBAAkB+F,UAE3CF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mBAIjBD,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAMuC,EAAoB0B,GACnCpE,UAAU,uIACVtB,MAAOxE,EAAE,UAAW,WAAW+F,UAE/BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBAlHdoE,EAASU,OA0HlB/E,EAAAA,EAAAA,KAAA,OACEI,QAASA,IAAMyC,GAAmB,GAClC5C,UAAU,2OAA0OC,UAEpPC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,cAAaC,SAAA,EAC1BF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,gEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE/F,EAAE,oBAAqB,0BAE1B6F,EAAAA,EAAAA,KAAA,KAAGC,UAAU,mCAAkCC,SAC5C/F,EAAE,uBAAwB,8CAOb,IAArBmI,EAAU7B,SACTN,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qDACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,2DAA0DC,SACrE/F,EAAE,mBAAoB,yBAEzB6F,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjD/F,EAAE,kBAAmB,qDAExB6F,EAAAA,EAAAA,KAAA,UACEI,QAASA,IAAM0C,EAAU,OACzB7C,UAAU,6DAA4DC,SAErE/F,EAAE,mBAAoB,2BAM5ByI,IACC5C,EAAAA,EAAAA,KAACkF,EAAe,CACdjL,QAASA,IAAM4I,GAAmB,GAClC3I,OA5RqB6H,UAC3B,IACE,MAAMuB,QAAiBC,MAAM,4BAA6B,CACxDmB,OAAQ,OACRlB,QAAS,CACP,cAAgB,UAADhD,OAAYiD,aAAaC,QAAQ,UAChD,eAAgB,oBAElByB,KAAMC,KAAKC,UAAU3K,KAGvB,GAAI4I,EAASK,GAAI,CACf,MAAME,QAAaP,EAASQ,OAG5B,OAFA7B,EAAAA,GAAME,QAAQ,iCACde,IACOW,EAAKA,IACd,CAAO,CACL,MAAMyB,QAAkBhC,EAASQ,OACjC,MAAM,IAAIF,MAAM0B,EAAUC,SAAW,4BACvC,CACF,CAAE,MAAOrD,GAEP,MADAE,QAAQF,MAAM,2BAA4BA,GACpCA,CACR,Q", "sources": ["components/Templates/TemplateBuilder.jsx", "pages/Financial/InvoiceTemplates.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst TemplateBuilder = ({ onClose, onSave }) => {\n  const { t } = useTranslation();\n  const { language } = useLanguage();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [templateData, setTemplateData] = useState({\n    name: '',\n    nameArabic: '',\n    description: '',\n    descriptionArabic: '',\n    type: 'standard',\n    category: 'general',\n    config: {\n      header: {\n        showLogo: true,\n        showCompanyInfo: true,\n        showInvoiceTitle: true,\n        customTitle: '',\n        customTitleArabic: ''\n      },\n      patientInfo: {\n        showPatientDetails: true,\n        showNationalId: true,\n        showInsuranceInfo: false,\n        showVisaInfo: false,\n        customFields: []\n      },\n      services: {\n        showServiceCode: true,\n        showDescription: true,\n        showQuantity: true,\n        showUnitPrice: true,\n        showDiscount: false,\n        showTax: true,\n        allowCustomServices: true,\n        predefinedServices: []\n      },\n      pricing: {\n        currency: 'SAR',\n        showSubtotal: true,\n        showTax: true,\n        taxRate: 15,\n        showDiscount: false,\n        showTotal: true,\n        roundingMethod: 'round'\n      },\n      footer: {\n        showPaymentTerms: true,\n        showBankDetails: false,\n        showNotes: true,\n        showSignature: false,\n        customText: '',\n        customTextArabic: ''\n      },\n      compliance: {\n        zatcaCompliant: true,\n        nphiesCompliant: false,\n        showQRCode: true,\n        requireElectronicSignature: false\n      }\n    },\n    styling: {\n      colorScheme: {\n        primary: '#2563eb',\n        secondary: '#64748b',\n        accent: '#10b981'\n      },\n      fonts: {\n        primary: 'Inter',\n        arabic: 'Noto Sans Arabic'\n      },\n      layout: {\n        orientation: 'portrait',\n        pageSize: 'A4',\n        margins: { top: 20, bottom: 20, left: 20, right: 20 }\n      }\n    }\n  });\n\n  const steps = [\n    { id: 1, title: 'Basic Info', icon: 'fas fa-info-circle' },\n    { id: 2, title: 'Header Config', icon: 'fas fa-heading' },\n    { id: 3, title: 'Patient Info', icon: 'fas fa-user' },\n    { id: 4, title: 'Services', icon: 'fas fa-list' },\n    { id: 5, title: 'Pricing', icon: 'fas fa-dollar-sign' },\n    { id: 6, title: 'Footer & Compliance', icon: 'fas fa-check-circle' },\n    { id: 7, title: 'Styling', icon: 'fas fa-palette' }\n  ];\n\n  const handleInputChange = (section, field, value) => {\n    if (section) {\n      setTemplateData(prev => ({\n        ...prev,\n        [section]: {\n          ...prev[section],\n          [field]: value\n        }\n      }));\n    } else {\n      setTemplateData(prev => ({\n        ...prev,\n        [field]: value\n      }));\n    }\n  };\n\n  const handleConfigChange = (section, subsection, field, value) => {\n    setTemplateData(prev => ({\n      ...prev,\n      config: {\n        ...prev.config,\n        [section]: {\n          ...prev.config[section],\n          [subsection ? subsection : field]: subsection ? {\n            ...prev.config[section][subsection],\n            [field]: value\n          } : value\n        }\n      }\n    }));\n  };\n\n  const addCustomField = () => {\n    const newField = {\n      label: '',\n      labelArabic: '',\n      fieldType: 'text',\n      required: false\n    };\n    \n    setTemplateData(prev => ({\n      ...prev,\n      config: {\n        ...prev.config,\n        patientInfo: {\n          ...prev.config.patientInfo,\n          customFields: [...prev.config.patientInfo.customFields, newField]\n        }\n      }\n    }));\n  };\n\n  const removeCustomField = (index) => {\n    setTemplateData(prev => ({\n      ...prev,\n      config: {\n        ...prev.config,\n        patientInfo: {\n          ...prev.config.patientInfo,\n          customFields: prev.config.patientInfo.customFields.filter((_, i) => i !== index)\n        }\n      }\n    }));\n  };\n\n  const addPredefinedService = () => {\n    const newService = {\n      code: '',\n      name: '',\n      nameArabic: '',\n      description: '',\n      descriptionArabic: '',\n      defaultPrice: 0,\n      taxable: true\n    };\n    \n    setTemplateData(prev => ({\n      ...prev,\n      config: {\n        ...prev.config,\n        services: {\n          ...prev.config.services,\n          predefinedServices: [...prev.config.services.predefinedServices, newService]\n        }\n      }\n    }));\n  };\n\n  const removePredefinedService = (index) => {\n    setTemplateData(prev => ({\n      ...prev,\n      config: {\n        ...prev.config,\n        services: {\n          ...prev.config.services,\n          predefinedServices: prev.config.services.predefinedServices.filter((_, i) => i !== index)\n        }\n      }\n    }));\n  };\n\n  const handleSave = async () => {\n    try {\n      if (!templateData.name.trim()) {\n        toast.error('Template name is required');\n        return;\n      }\n\n      if (!templateData.description.trim()) {\n        toast.error('Template description is required');\n        return;\n      }\n\n      await onSave(templateData);\n      toast.success('Template created successfully!');\n      onClose();\n    } catch (error) {\n      console.error('Error saving template:', error);\n      toast.error('Failed to save template');\n    }\n  };\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('basicInformation', 'Basic Information')}\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('templateName', 'Template Name')} *\n                </label>\n                <input\n                  type=\"text\"\n                  value={templateData.name}\n                  onChange={(e) => handleInputChange(null, 'name', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Enter template name\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('templateNameArabic', 'Template Name (Arabic)')}\n                </label>\n                <input\n                  type=\"text\"\n                  value={templateData.nameArabic}\n                  onChange={(e) => handleInputChange(null, 'nameArabic', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"أدخل اسم القالب\"\n                  dir=\"rtl\"\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('description', 'Description')} *\n                </label>\n                <textarea\n                  value={templateData.description}\n                  onChange={(e) => handleInputChange(null, 'description', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"Enter template description\"\n                />\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('descriptionArabic', 'Description (Arabic)')}\n                </label>\n                <textarea\n                  value={templateData.descriptionArabic}\n                  onChange={(e) => handleInputChange(null, 'descriptionArabic', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  placeholder=\"أدخل وصف القالب\"\n                  dir=\"rtl\"\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('templateType', 'Template Type')}\n                </label>\n                <select\n                  value={templateData.type}\n                  onChange={(e) => handleInputChange(null, 'type', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"standard\">{t('standard', 'Standard')}</option>\n                  <option value=\"medical\">{t('medical', 'Medical')}</option>\n                  <option value=\"therapy\">{t('therapy', 'Therapy')}</option>\n                  <option value=\"insurance\">{t('insurance', 'Insurance')}</option>\n                  <option value=\"government\">{t('government', 'Government')}</option>\n                  <option value=\"custom\">{t('custom', 'Custom')}</option>\n                </select>\n              </div>\n              \n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {t('category', 'Category')}\n                </label>\n                <select\n                  value={templateData.category}\n                  onChange={(e) => handleInputChange(null, 'category', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                >\n                  <option value=\"general\">{t('general', 'General')}</option>\n                  <option value=\"physical_therapy\">{t('physicalTherapy', 'Physical Therapy')}</option>\n                  <option value=\"occupational_therapy\">{t('occupationalTherapy', 'Occupational Therapy')}</option>\n                  <option value=\"speech_therapy\">{t('speechTherapy', 'Speech Therapy')}</option>\n                  <option value=\"medical_consultation\">{t('medicalConsultation', 'Medical Consultation')}</option>\n                  <option value=\"equipment_rental\">{t('equipmentRental', 'Equipment Rental')}</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 2:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('headerConfiguration', 'Header Configuration')}\n            </h3>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.header.showLogo}\n                    onChange={(e) => handleConfigChange('header', null, 'showLogo', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showLogo', 'Show Logo')}\n                  </span>\n                </label>\n                \n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.header.showCompanyInfo}\n                    onChange={(e) => handleConfigChange('header', null, 'showCompanyInfo', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showCompanyInfo', 'Show Company Information')}\n                  </span>\n                </label>\n                \n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.header.showInvoiceTitle}\n                    onChange={(e) => handleConfigChange('header', null, 'showInvoiceTitle', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showInvoiceTitle', 'Show Invoice Title')}\n                  </span>\n                </label>\n              </div>\n              \n              <div className=\"space-y-3\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('customTitle', 'Custom Title')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={templateData.config.header.customTitle}\n                    onChange={(e) => handleConfigChange('header', null, 'customTitle', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                    placeholder=\"Invoice\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('customTitleArabic', 'Custom Title (Arabic)')}\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={templateData.config.header.customTitleArabic}\n                    onChange={(e) => handleConfigChange('header', null, 'customTitleArabic', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                    placeholder=\"فاتورة\"\n                    dir=\"rtl\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 3:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('patientInformation', 'Patient Information')}\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.patientInfo.showPatientDetails}\n                    onChange={(e) => handleConfigChange('patientInfo', null, 'showPatientDetails', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showPatientDetails', 'Show Patient Details')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.patientInfo.showNationalId}\n                    onChange={(e) => handleConfigChange('patientInfo', null, 'showNationalId', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showNationalId', 'Show National ID')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.patientInfo.showInsuranceInfo}\n                    onChange={(e) => handleConfigChange('patientInfo', null, 'showInsuranceInfo', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showInsuranceInfo', 'Show Insurance Information')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.patientInfo.showVisaInfo}\n                    onChange={(e) => handleConfigChange('patientInfo', null, 'showVisaInfo', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showVisaInfo', 'Show Visa Information')}\n                  </span>\n                </label>\n              </div>\n\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('customFields', 'Custom Fields')}\n                </h4>\n                <div className=\"space-y-2 max-h-32 overflow-y-auto\">\n                  {templateData.config.patientInfo.customFields.map((field, index) => (\n                    <div key={index} className=\"flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-700 rounded\">\n                      <input\n                        type=\"text\"\n                        value={field.label}\n                        onChange={(e) => {\n                          const newFields = [...templateData.config.patientInfo.customFields];\n                          newFields[index].label = e.target.value;\n                          handleConfigChange('patientInfo', null, 'customFields', newFields);\n                        }}\n                        placeholder=\"Field label\"\n                        className=\"flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white\"\n                      />\n                      <button\n                        onClick={() => removeCustomField(index)}\n                        className=\"text-red-600 hover:text-red-800\"\n                      >\n                        <i className=\"fas fa-trash text-sm\"></i>\n                      </button>\n                    </div>\n                  ))}\n                </div>\n                <button\n                  onClick={addCustomField}\n                  className=\"mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\n                >\n                  <i className=\"fas fa-plus mr-1\"></i>\n                  {t('addField', 'Add Field')}\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 4:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('servicesConfiguration', 'Services Configuration')}\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.services.showServiceCode}\n                    onChange={(e) => handleConfigChange('services', null, 'showServiceCode', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showServiceCode', 'Show Service Code')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.services.showDescription}\n                    onChange={(e) => handleConfigChange('services', null, 'showDescription', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showDescription', 'Show Description')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.services.showQuantity}\n                    onChange={(e) => handleConfigChange('services', null, 'showQuantity', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showQuantity', 'Show Quantity')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.services.showUnitPrice}\n                    onChange={(e) => handleConfigChange('services', null, 'showUnitPrice', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showUnitPrice', 'Show Unit Price')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.services.showDiscount}\n                    onChange={(e) => handleConfigChange('services', null, 'showDiscount', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showDiscount', 'Show Discount')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.services.allowCustomServices}\n                    onChange={(e) => handleConfigChange('services', null, 'allowCustomServices', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('allowCustomServices', 'Allow Custom Services')}\n                  </span>\n                </label>\n              </div>\n\n              <div>\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white mb-2\">\n                  {t('predefinedServices', 'Predefined Services')}\n                </h4>\n                <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                  {templateData.config.services.predefinedServices.map((service, index) => (\n                    <div key={index} className=\"p-2 bg-gray-50 dark:bg-gray-700 rounded\">\n                      <div className=\"grid grid-cols-2 gap-2 mb-2\">\n                        <input\n                          type=\"text\"\n                          value={service.code}\n                          onChange={(e) => {\n                            const newServices = [...templateData.config.services.predefinedServices];\n                            newServices[index].code = e.target.value;\n                            handleConfigChange('services', null, 'predefinedServices', newServices);\n                          }}\n                          placeholder=\"Service code\"\n                          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white\"\n                        />\n                        <input\n                          type=\"text\"\n                          value={service.name}\n                          onChange={(e) => {\n                            const newServices = [...templateData.config.services.predefinedServices];\n                            newServices[index].name = e.target.value;\n                            handleConfigChange('services', null, 'predefinedServices', newServices);\n                          }}\n                          placeholder=\"Service name\"\n                          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white\"\n                        />\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <input\n                          type=\"number\"\n                          value={service.defaultPrice}\n                          onChange={(e) => {\n                            const newServices = [...templateData.config.services.predefinedServices];\n                            newServices[index].defaultPrice = parseFloat(e.target.value) || 0;\n                            handleConfigChange('services', null, 'predefinedServices', newServices);\n                          }}\n                          placeholder=\"Price\"\n                          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded dark:bg-gray-800 dark:text-white w-20\"\n                        />\n                        <button\n                          onClick={() => removePredefinedService(index)}\n                          className=\"text-red-600 hover:text-red-800\"\n                        >\n                          <i className=\"fas fa-trash text-sm\"></i>\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n                <button\n                  onClick={addPredefinedService}\n                  className=\"mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700\"\n                >\n                  <i className=\"fas fa-plus mr-1\"></i>\n                  {t('addService', 'Add Service')}\n                </button>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 5:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('pricingConfiguration', 'Pricing Configuration')}\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('currency', 'Currency')}\n                  </label>\n                  <select\n                    value={templateData.config.pricing.currency}\n                    onChange={(e) => handleConfigChange('pricing', null, 'currency', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  >\n                    <option value=\"SAR\">SAR - Saudi Riyal</option>\n                    <option value=\"USD\">USD - US Dollar</option>\n                    <option value=\"EUR\">EUR - Euro</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('taxRate', 'Tax Rate')} (%)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={templateData.config.pricing.taxRate}\n                    onChange={(e) => handleConfigChange('pricing', null, 'taxRate', parseFloat(e.target.value) || 0)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                    min=\"0\"\n                    max=\"100\"\n                    step=\"0.1\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('roundingMethod', 'Rounding Method')}\n                  </label>\n                  <select\n                    value={templateData.config.pricing.roundingMethod}\n                    onChange={(e) => handleConfigChange('pricing', null, 'roundingMethod', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  >\n                    <option value=\"round\">{t('round', 'Round')}</option>\n                    <option value=\"floor\">{t('floor', 'Floor')}</option>\n                    <option value=\"ceil\">{t('ceil', 'Ceiling')}</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.pricing.showSubtotal}\n                    onChange={(e) => handleConfigChange('pricing', null, 'showSubtotal', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showSubtotal', 'Show Subtotal')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.pricing.showTax}\n                    onChange={(e) => handleConfigChange('pricing', null, 'showTax', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showTax', 'Show Tax')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.pricing.showDiscount}\n                    onChange={(e) => handleConfigChange('pricing', null, 'showDiscount', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showDiscount', 'Show Discount')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.pricing.showTotal}\n                    onChange={(e) => handleConfigChange('pricing', null, 'showTotal', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showTotal', 'Show Total')}\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 6:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('footerAndCompliance', 'Footer & Compliance')}\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white\">\n                  {t('footerOptions', 'Footer Options')}\n                </h4>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.footer.showPaymentTerms}\n                    onChange={(e) => handleConfigChange('footer', null, 'showPaymentTerms', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showPaymentTerms', 'Show Payment Terms')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.footer.showBankDetails}\n                    onChange={(e) => handleConfigChange('footer', null, 'showBankDetails', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showBankDetails', 'Show Bank Details')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.footer.showNotes}\n                    onChange={(e) => handleConfigChange('footer', null, 'showNotes', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showNotes', 'Show Notes')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.footer.showSignature}\n                    onChange={(e) => handleConfigChange('footer', null, 'showSignature', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showSignature', 'Show Signature')}\n                  </span>\n                </label>\n              </div>\n\n              <div className=\"space-y-3\">\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white\">\n                  {t('complianceOptions', 'Compliance Options')}\n                </h4>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.compliance.zatcaCompliant}\n                    onChange={(e) => handleConfigChange('compliance', null, 'zatcaCompliant', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('zatcaCompliant', 'ZATCA Compliant')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.compliance.nphiesCompliant}\n                    onChange={(e) => handleConfigChange('compliance', null, 'nphiesCompliant', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('nphiesCompliant', 'NPHIES Compliant')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.compliance.showQRCode}\n                    onChange={(e) => handleConfigChange('compliance', null, 'showQRCode', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('showQRCode', 'Show QR Code')}\n                  </span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={templateData.config.compliance.requireElectronicSignature}\n                    onChange={(e) => handleConfigChange('compliance', null, 'requireElectronicSignature', e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                    {t('requireElectronicSignature', 'Require Electronic Signature')}\n                  </span>\n                </label>\n              </div>\n            </div>\n          </div>\n        );\n\n      case 7:\n        return (\n          <div className=\"space-y-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n              {t('styling', 'Styling')}\n            </h3>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div className=\"space-y-3\">\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white\">\n                  {t('colorScheme', 'Color Scheme')}\n                </h4>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('primaryColor', 'Primary Color')}\n                  </label>\n                  <input\n                    type=\"color\"\n                    value={templateData.styling.colorScheme.primary}\n                    onChange={(e) => handleConfigChange('styling', 'colorScheme', 'primary', e.target.value)}\n                    className=\"w-full h-10 border border-gray-300 dark:border-gray-600 rounded\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('secondaryColor', 'Secondary Color')}\n                  </label>\n                  <input\n                    type=\"color\"\n                    value={templateData.styling.colorScheme.secondary}\n                    onChange={(e) => handleConfigChange('styling', 'colorScheme', 'secondary', e.target.value)}\n                    className=\"w-full h-10 border border-gray-300 dark:border-gray-600 rounded\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('accentColor', 'Accent Color')}\n                  </label>\n                  <input\n                    type=\"color\"\n                    value={templateData.styling.colorScheme.accent}\n                    onChange={(e) => handleConfigChange('styling', 'colorScheme', 'accent', e.target.value)}\n                    className=\"w-full h-10 border border-gray-300 dark:border-gray-600 rounded\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-3\">\n                <h4 className=\"text-md font-medium text-gray-900 dark:text-white\">\n                  {t('layout', 'Layout')}\n                </h4>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('orientation', 'Orientation')}\n                  </label>\n                  <select\n                    value={templateData.styling.layout.orientation}\n                    onChange={(e) => handleConfigChange('styling', 'layout', 'orientation', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  >\n                    <option value=\"portrait\">{t('portrait', 'Portrait')}</option>\n                    <option value=\"landscape\">{t('landscape', 'Landscape')}</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    {t('pageSize', 'Page Size')}\n                  </label>\n                  <select\n                    value={templateData.styling.layout.pageSize}\n                    onChange={(e) => handleConfigChange('styling', 'layout', 'pageSize', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                  >\n                    <option value=\"A4\">A4</option>\n                    <option value=\"Letter\">Letter</option>\n                    <option value=\"Legal\">Legal</option>\n                  </select>\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n\n      default:\n        return (\n          <div className=\"text-center py-8\">\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              Step {currentStep} configuration will be implemented here.\n            </p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600\">\n          <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n            {t('createNewTemplate', 'Create New Template')}\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <i className=\"fas fa-times text-xl\"></i>\n          </button>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <div className=\"flex items-center justify-between\">\n            {steps.map((step, index) => (\n              <div key={step.id} className=\"flex items-center\">\n                <div\n                  className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${\n                    currentStep >= step.id\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-200 text-gray-600 dark:bg-gray-600 dark:text-gray-400'\n                  }`}\n                >\n                  <i className={step.icon}></i>\n                </div>\n                <span className={`ml-2 text-sm ${\n                  currentStep >= step.id\n                    ? 'text-blue-600 dark:text-blue-400'\n                    : 'text-gray-600 dark:text-gray-400'\n                }`}>\n                  {step.title}\n                </span>\n                {index < steps.length - 1 && (\n                  <div className={`w-8 h-0.5 mx-4 ${\n                    currentStep > step.id\n                      ? 'bg-blue-600'\n                      : 'bg-gray-200 dark:bg-gray-600'\n                  }`}></div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-96\">\n          {renderStepContent()}\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-600\">\n          <button\n            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}\n            disabled={currentStep === 1}\n            className=\"px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            <i className=\"fas fa-arrow-left mr-2\"></i>\n            {t('previous', 'Previous')}\n          </button>\n\n          <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n            {currentStep} of {steps.length}\n          </span>\n\n          <div className=\"flex space-x-3\">\n            {currentStep < steps.length ? (\n              <button\n                onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n              >\n                {t('next', 'Next')}\n                <i className=\"fas fa-arrow-right ml-2\"></i>\n              </button>\n            ) : (\n              <button\n                onClick={handleSave}\n                className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700\"\n              >\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('createTemplate', 'Create Template')}\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default TemplateBuilder;\n", "import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { Link } from 'react-router-dom';\nimport toast from 'react-hot-toast';\nimport TemplateBuilder from '../../components/Templates/TemplateBuilder';\n\nconst InvoiceTemplates = () => {\n  const [templates, setTemplates] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedTemplate, setSelectedTemplate] = useState(null);\n  const [showCreateModal, setShowCreateModal] = useState(false);\n  const [filter, setFilter] = useState('all');\n  const { t } = useTranslation();\n  const { language } = useLanguage();\n  const { user } = useAuth();\n\n  useEffect(() => {\n    loadTemplates();\n  }, [filter]);\n\n  const loadTemplates = async () => {\n    setLoading(true);\n    try {\n      const queryParams = new URLSearchParams();\n      if (filter !== 'all') {\n        queryParams.append('type', filter);\n      }\n\n      const response = await fetch(`/api/v1/invoice-templates?${queryParams}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setTemplates(data.data);\n      } else {\n        throw new Error('Failed to load templates');\n      }\n    } catch (error) {\n      console.error('Error loading templates:', error);\n      toast.error('Failed to load invoice templates');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUseTemplate = async (templateId) => {\n    try {\n      // Increment usage count\n      await fetch(`/api/v1/invoice-templates/${templateId}/use`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      // Navigate to create invoice with template\n      window.location.href = `/financial/invoices/create?template=${templateId}`;\n    } catch (error) {\n      console.error('Error using template:', error);\n      toast.error('Failed to use template');\n    }\n  };\n\n  const handleDuplicateTemplate = async (templateId) => {\n    try {\n      const response = await fetch(`/api/v1/invoice-templates/${templateId}/duplicate`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        toast.success('Template duplicated successfully');\n        loadTemplates();\n      } else {\n        throw new Error('Failed to duplicate template');\n      }\n    } catch (error) {\n      console.error('Error duplicating template:', error);\n      toast.error('Failed to duplicate template');\n    }\n  };\n\n  const handleSetAsDefault = async (templateId) => {\n    try {\n      const response = await fetch(`/api/v1/invoice-templates/${templateId}/set-default`, {\n        method: 'PUT',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (response.ok) {\n        toast.success('Template set as default');\n        loadTemplates();\n      } else {\n        throw new Error('Failed to set as default');\n      }\n    } catch (error) {\n      console.error('Error setting default:', error);\n      toast.error('Failed to set template as default');\n    }\n  };\n\n  const handleCreateTemplate = async (templateData) => {\n    try {\n      const response = await fetch('/api/v1/invoice-templates', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(templateData)\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        toast.success('Template created successfully');\n        loadTemplates();\n        return data.data;\n      } else {\n        const errorData = await response.json();\n        throw new Error(errorData.message || 'Failed to create template');\n      }\n    } catch (error) {\n      console.error('Error creating template:', error);\n      throw error;\n    }\n  };\n\n  const getTemplateIcon = (type) => {\n    switch (type) {\n      case 'standard':\n        return 'fas fa-file-invoice';\n      case 'medical':\n        return 'fas fa-user-md';\n      case 'therapy':\n        return 'fas fa-dumbbell';\n      case 'insurance':\n        return 'fas fa-shield-alt';\n      case 'government':\n        return 'fas fa-landmark';\n      default:\n        return 'fas fa-file';\n    }\n  };\n\n  const getTypeColor = (type) => {\n    switch (type) {\n      case 'standard':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400';\n      case 'medical':\n        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';\n      case 'therapy':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400';\n      case 'insurance':\n        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';\n      case 'government':\n        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              <i className=\"fas fa-file-invoice mr-3 text-blue-600\"></i>\n              {t('invoiceTemplates', 'Invoice Templates')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n              {t('invoiceTemplatesDesc', 'Create and manage invoice templates for different types of services')}\n            </p>\n          </div>\n          \n          <button\n            onClick={() => setShowCreateModal(true)}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center\"\n          >\n            <i className=\"fas fa-plus mr-2\"></i>\n            {t('createTemplate', 'Create Template')}\n          </button>\n        </div>\n\n        {/* Filter Tabs */}\n        <div className=\"mt-6 border-b border-gray-200 dark:border-gray-600\">\n          <nav className=\"flex space-x-8\" aria-label=\"Tabs\">\n            {[\n              { key: 'all', label: 'All Templates', icon: 'fas fa-th-large' },\n              { key: 'standard', label: 'Standard', icon: 'fas fa-file-invoice' },\n              { key: 'medical', label: 'Medical', icon: 'fas fa-user-md' },\n              { key: 'therapy', label: 'Therapy', icon: 'fas fa-dumbbell' },\n              { key: 'insurance', label: 'Insurance', icon: 'fas fa-shield-alt' }\n            ].map((tab) => (\n              <button\n                key={tab.key}\n                onClick={() => setFilter(tab.key)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${\n                  filter === tab.key\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <i className={`${tab.icon} mr-2`}></i>\n                {t(tab.label.toLowerCase().replace(' ', ''), tab.label)}\n              </button>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Templates Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {templates.map((template) => (\n          <div\n            key={template._id}\n            className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 hover:shadow-lg transition-shadow\"\n          >\n            {/* Template Header */}\n            <div className=\"p-6 border-b border-gray-200 dark:border-gray-600\">\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-center\">\n                  <div className={`p-3 rounded-lg ${getTypeColor(template.type)} mr-4`}>\n                    <i className={`${getTemplateIcon(template.type)} text-xl`}></i>\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      {language === 'ar' && template.nameArabic ? template.nameArabic : template.name}\n                    </h3>\n                    <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {language === 'ar' && template.descriptionArabic ? template.descriptionArabic : template.description}\n                    </p>\n                  </div>\n                </div>\n                \n                {template.isDefault && (\n                  <span className=\"px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 text-xs font-medium rounded\">\n                    {t('default', 'Default')}\n                  </span>\n                )}\n              </div>\n            </div>\n\n            {/* Template Info */}\n            <div className=\"p-6\">\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">{t('type', 'Type')}:</span>\n                  <span className={`px-2 py-1 rounded text-xs font-medium ${getTypeColor(template.type)}`}>\n                    {t(template.type, template.type)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">{t('category', 'Category')}:</span>\n                  <span className=\"text-gray-900 dark:text-white font-medium\">\n                    {t(template.category, template.category)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">{t('usageCount', 'Used')}:</span>\n                  <span className=\"text-gray-900 dark:text-white font-medium\">\n                    {template.usageCount} {t('times', 'times')}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-600 dark:text-gray-400\">{t('version', 'Version')}:</span>\n                  <span className=\"text-gray-900 dark:text-white font-medium\">\n                    {template.version}\n                  </span>\n                </div>\n              </div>\n\n              {/* Compliance Badges */}\n              <div className=\"mt-4 flex flex-wrap gap-2\">\n                {template.config.compliance.zatcaCompliant && (\n                  <span className=\"px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 text-xs rounded\">\n                    ZATCA\n                  </span>\n                )}\n                {template.config.compliance.nphiesCompliant && (\n                  <span className=\"px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 text-xs rounded\">\n                    NPHIES\n                  </span>\n                )}\n                {template.config.compliance.requireElectronicSignature && (\n                  <span className=\"px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 text-xs rounded\">\n                    E-Sign\n                  </span>\n                )}\n              </div>\n            </div>\n\n            {/* Template Actions */}\n            <div className=\"px-6 pb-6\">\n              <div className=\"flex space-x-2\">\n                <button\n                  onClick={() => handleUseTemplate(template._id)}\n                  className=\"flex-1 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm font-medium\"\n                >\n                  <i className=\"fas fa-play mr-1\"></i>\n                  {t('useTemplate', 'Use Template')}\n                </button>\n                \n                <button\n                  onClick={() => handleDuplicateTemplate(template._id)}\n                  className=\"px-3 py-2 bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500\"\n                  title={t('duplicate', 'Duplicate')}\n                >\n                  <i className=\"fas fa-copy\"></i>\n                </button>\n                \n                {!template.isDefault && (\n                  <button\n                    onClick={() => handleSetAsDefault(template._id)}\n                    className=\"px-3 py-2 bg-green-200 text-green-700 dark:bg-green-600 dark:text-green-300 rounded hover:bg-green-300 dark:hover:bg-green-500\"\n                    title={t('setAsDefault', 'Set as Default')}\n                  >\n                    <i className=\"fas fa-star\"></i>\n                  </button>\n                )}\n                \n                <button\n                  onClick={() => setSelectedTemplate(template)}\n                  className=\"px-3 py-2 bg-purple-200 text-purple-700 dark:bg-purple-600 dark:text-purple-300 rounded hover:bg-purple-300 dark:hover:bg-purple-500\"\n                  title={t('preview', 'Preview')}\n                >\n                  <i className=\"fas fa-eye\"></i>\n                </button>\n              </div>\n            </div>\n          </div>\n        ))}\n\n        {/* Create New Template Card */}\n        <div\n          onClick={() => setShowCreateModal(true)}\n          className=\"bg-white dark:bg-gray-800 rounded-lg shadow border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-colors flex items-center justify-center min-h-[400px]\"\n        >\n          <div className=\"text-center\">\n            <i className=\"fas fa-plus text-4xl text-gray-400 dark:text-gray-500 mb-4\"></i>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              {t('createNewTemplate', 'Create New Template')}\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {t('createCustomTemplate', 'Create a custom invoice template')}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Empty State */}\n      {templates.length === 0 && (\n        <div className=\"text-center py-12\">\n          <i className=\"fas fa-file-invoice text-6xl text-gray-400 mb-4\"></i>\n          <h3 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-2\">\n            {t('noTemplatesFound', 'No Templates Found')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n            {t('noTemplatesDesc', 'No invoice templates match your current filter')}\n          </p>\n          <button\n            onClick={() => setFilter('all')}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\"\n          >\n            {t('showAllTemplates', 'Show All Templates')}\n          </button>\n        </div>\n      )}\n\n      {/* Create Template Modal */}\n      {showCreateModal && (\n        <TemplateBuilder\n          onClose={() => setShowCreateModal(false)}\n          onSave={handleCreateTemplate}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default InvoiceTemplates;\n"], "names": ["_ref", "onClose", "onSave", "t", "useTranslation", "language", "useLanguage", "currentStep", "setCurrentStep", "useState", "templateData", "setTemplateData", "name", "nameArabic", "description", "descriptionArabic", "type", "category", "config", "header", "showLogo", "showCompanyInfo", "showInvoiceTitle", "customTitle", "customTitleArabic", "patientInfo", "showPatientDetails", "showNationalId", "showInsuranceInfo", "showVisaInfo", "customFields", "services", "showServiceCode", "showDescription", "showQuantity", "showUnitPrice", "showDiscount", "showTax", "allowCustomServices", "predefinedServices", "pricing", "currency", "showSubtotal", "taxRate", "showTotal", "roundingMethod", "footer", "showPaymentTerms", "showBankDetails", "showNotes", "showSignature", "customText", "customTextArabic", "compliance", "zatcaCompliant", "nphiesCompliant", "showQRCode", "requireElectronicSignature", "styling", "colorScheme", "primary", "secondary", "accent", "fonts", "arabic", "layout", "orientation", "pageSize", "margins", "top", "bottom", "left", "right", "steps", "id", "title", "icon", "handleInputChange", "section", "field", "value", "prev", "_objectSpread", "handleConfigChange", "subsection", "addCustomField", "newField", "label", "labelArabic", "fieldType", "required", "addPredefinedService", "newService", "code", "defaultPrice", "taxable", "_jsx", "className", "children", "_jsxs", "onClick", "map", "step", "index", "concat", "length", "renderStepContent", "onChange", "e", "target", "placeholder", "dir", "rows", "checked", "new<PERSON>ields", "filter", "_", "i", "removeCustomField", "service", "newServices", "parseFloat", "removePredefinedService", "min", "max", "Math", "disabled", "async", "trim", "toast", "error", "success", "console", "InvoiceTemplates", "templates", "setTemplates", "loading", "setLoading", "selectedTemplate", "setSelectedTemplate", "showCreateModal", "setShowCreateModal", "setFilter", "user", "useAuth", "useEffect", "loadTemplates", "queryParams", "URLSearchParams", "append", "response", "fetch", "headers", "localStorage", "getItem", "ok", "Error", "data", "json", "getTemplateIcon", "getTypeColor", "key", "tab", "toLowerCase", "replace", "template", "isDefault", "usageCount", "version", "templateId", "method", "window", "location", "href", "handleUseTemplate", "_id", "handleDuplicateTemplate", "handleSetAsDefault", "TemplateBuilder", "body", "JSON", "stringify", "errorData", "message"], "sourceRoot": ""}