{"version": 3, "file": "static/js/8317.5f56a66a.chunk.js", "mappings": "gMAIA,MAmWA,EAnWwBA,IAAiD,IAAhD,SAAEC,EAAQ,SAAEC,EAAQ,YAAEC,EAAc,MAAMH,EACjE,MAAM,EAAEI,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAASC,IAAcC,EAAAA,EAAAA,WAAS,IAEhCC,EAAUC,IAAeF,EAAAA,EAAAA,UAAS,CACvCG,WAAsB,OAAXT,QAAW,IAAXA,OAAW,EAAXA,EAAaS,YAAa,GACrCC,aAAwB,OAAXV,QAAW,IAAXA,OAAW,EAAXA,EAAaU,cAAe,GACzCC,MAAiB,OAAXX,QAAW,IAAXA,OAAW,EAAXA,EAAaW,QAAQ,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC/DC,MAAiB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAae,OAAQ,GAC3BC,UAAqB,OAAXhB,QAAW,IAAXA,OAAW,EAAXA,EAAagB,WAAY,KACnCC,MAAiB,OAAXjB,QAAW,IAAXA,OAAW,EAAXA,EAAaiB,OAAQ,eAC3BC,WAAsB,OAAXlB,QAAW,IAAXA,OAAW,EAAXA,EAAakB,YAAa,GACrCC,UAAqB,OAAXnB,QAAW,IAAXA,OAAW,EAAXA,EAAamB,WAAY,SACnCC,OAAkB,OAAXpB,QAAW,IAAXA,OAAW,EAAXA,EAAaoB,QAAS,GAC7BC,UAAqB,OAAXrB,QAAW,IAAXA,OAAW,EAAXA,EAAaqB,WAAY,SACnCC,iBAA4B,OAAXtB,QAAW,IAAXA,OAAW,EAAXA,EAAasB,mBAAmB,EACjDC,cAAyB,OAAXvB,QAAW,IAAXA,OAAW,EAAXA,EAAauB,eAAgB,QAGtCC,EAAQC,IAAanB,EAAAA,EAAAA,UAAS,CAAC,GAGhCoB,EAAmB,CACvB,CAAEC,MAAO,eAAgBC,MAAO3B,EAAE,eAAgB,iBAClD,CAAE0B,MAAO,aAAcC,MAAO3B,EAAE,aAAc,eAC9C,CAAE0B,MAAO,kBAAmBC,MAAO3B,EAAE,iBAAkB,oBACvD,CAAE0B,MAAO,YAAaC,MAAO3B,EAAE,WAAY,cAC3C,CAAE0B,MAAO,gBAAiBC,MAAO3B,EAAE,eAAgB,kBACnD,CAAE0B,MAAO,aAAcC,MAAO3B,EAAE,aAAc,gBAU1C4B,EAAY,CAChB,CAAEF,MAAO,SAAUC,MAAO3B,EAAE,QAAS,0BACrC,CAAE0B,MAAO,SAAUC,MAAO3B,EAAE,QAAS,wBACrC,CAAE0B,MAAO,SAAUC,MAAO3B,EAAE,QAAS,2BACrC,CAAE0B,MAAO,MAAOC,MAAO3B,EAAE,MAAO,cAChC,CAAE0B,MAAO,OAAQC,MAAO3B,EAAE,OAAQ,uBAU9B6B,EAAoBA,CAACC,EAAOJ,KAChCnB,EAAYwB,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACD,GAAQJ,KACrCH,EAAOO,IACTN,EAAUO,IAAIC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,GAAUD,GAAI,IAAE,CAACD,GAAQ,SAiD3C,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEpC,EAAcC,EAAE,kBAAmB,oBAAsBA,EAAE,iBAAkB,qBAE/EF,IACCsC,EAAAA,EAAAA,KAAA,UACEC,QAASvC,EACToC,UAAU,6DAA4DC,UAEtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,+BAKnBD,EAAAA,EAAAA,MAAA,QAAMpC,SAzCWyC,UAGnB,GAFAC,EAAEC,iBArBiBC,MACnB,MAAMC,EAAY,CAAC,EAgBnB,OAdKpC,EAASG,YAAYkC,SACxBD,EAAUjC,YAAcT,EAAE,sBAAuB,6BAE9CM,EAASI,OACZgC,EAAUhC,KAAOV,EAAE,eAAgB,qBAEhCM,EAASQ,OACZ4B,EAAU5B,KAAOd,EAAE,eAAgB,qBAEhCM,EAASW,YACZyB,EAAUzB,UAAYjB,EAAE,oBAAqB,0BAG/CwB,EAAUkB,GAC+B,IAAlCE,OAAOC,KAAKH,GAAWI,QAMzBL,GAAL,CAKArC,GAAW,GACX,UAEQ,IAAI2C,QAAQC,GAAWC,WAAWD,EAAS,MAE7CnD,GACFA,EAASS,GAGX4C,EAAAA,GAAMC,QAAQnD,EAAE,mBAAoB,kCACtC,CAAE,MAAOoD,GACPF,EAAAA,GAAME,MAAMpD,EAAE,yBAA0B,4BAC1C,CAAC,QACCI,GAAW,EACb,CAhBA,MAFE8C,EAAAA,GAAME,MAAMpD,EAAE,kBAAmB,6CAqCHkC,UAAU,YAAWC,SAAA,EAEjDF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,cAAe,gBAAgB,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAEpEC,EAAAA,EAAAA,KAAA,SACEpB,KAAK,OACLU,MAAOpB,EAASG,YAChB4C,SAAWd,GAAMV,EAAkB,cAAeU,EAAEe,OAAO5B,OAC3DQ,UAAS,mJAAAqB,OACPhC,EAAOd,YAAc,iBAAmB,mBAE1C+C,YAAaxD,EAAE,mBAAoB,wBAEpCuB,EAAOd,cACN2B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAOd,kBAIrDwB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,kBAAmB,uBAExBoC,EAAAA,EAAAA,KAAA,UACEV,MAAOpB,EAASU,KAChBqC,SAAWd,GAAMV,EAAkB,OAAQU,EAAEe,OAAO5B,OACpDQ,UAAU,kKAAiKC,SAE1KV,EAAiBgC,IAAIzC,IACpBoB,EAAAA,EAAAA,KAAA,UAAyBV,MAAOV,EAAKU,MAAMS,SACxCnB,EAAKW,OADKX,EAAKU,iBAS1BO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,OAAQ,QAAQ,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAErDC,EAAAA,EAAAA,KAAA,SACEpB,KAAK,OACLU,MAAOpB,EAASI,KAChB2C,SAAWd,GAAMV,EAAkB,OAAQU,EAAEe,OAAO5B,OACpDQ,UAAS,mJAAAqB,OACPhC,EAAOb,KAAO,iBAAmB,qBAGpCa,EAAOb,OACN0B,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAOb,WAIrDuB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,OAAQ,QAAQ,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAErDF,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASQ,KAChBuC,SAAWd,GAAMV,EAAkB,OAAQU,EAAEe,OAAO5B,OACpDQ,UAAS,mJAAAqB,OACPhC,EAAOT,KAAO,iBAAmB,mBAChCqB,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,GAAES,SAAEnC,EAAE,aAAc,iBAhJ5B,CAChB,QAAS,QAAS,QAAS,QAAS,QAAS,QAC7C,QAAS,QAAS,QAAS,QAAS,QAAS,QAC7C,QAAS,QAAS,QAAS,QAAS,QAAS,QAC7C,QAAS,QAAS,SA6IGyD,IAAI3C,IACbsB,EAAAA,EAAAA,KAAA,UAAmBV,MAAOZ,EAAKqB,SAC5BrB,GADUA,OAKhBS,EAAOT,OACNsB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAOT,WAIrDmB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,yBAEjBiC,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASS,SAChBsC,SAAWd,GAAMV,EAAkB,WAAYU,EAAEe,OAAO5B,OACxDQ,UAAU,kKAAiKC,SAAA,EAE3KF,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,UAAW,eACpCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,MAAKS,SAAA,CAAC,OAAKnC,EAAE,UAAW,wBAM5CiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEF,EAAAA,EAAAA,MAAA,SAAOC,UAAU,kEAAiEC,SAAA,CAC/EnC,EAAE,YAAa,aAAa,KAACoC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,eAAcC,SAAC,UAE/DF,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASW,UAChBoC,SAAWd,GAAMV,EAAkB,YAAaU,EAAEe,OAAO5B,OACzDQ,UAAS,mJAAAqB,OACPhC,EAAON,UAAY,iBAAmB,mBACrCkB,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,GAAES,SAAEnC,EAAE,kBAAmB,sBA1MhC,CACjB,CAAE0B,MAAO,WAAYC,MAAO,uBAC5B,CAAED,MAAO,WAAYC,MAAO,yBAC5B,CAAED,MAAO,YAAaC,MAAO,uBAC7B,CAAED,MAAO,cAAeC,MAAO,2BAuMT8B,IAAIxC,IACdmB,EAAAA,EAAAA,KAAA,UAA8BV,MAAOT,EAAUS,MAAMS,SAClDlB,EAAUU,OADAV,EAAUS,WAK1BH,EAAON,YACNmB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,4BAA2BC,SAAEZ,EAAON,gBAIrDgB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,eAEjBoC,EAAAA,EAAAA,KAAA,UACEV,MAAOpB,EAASY,SAChBmC,SAAWd,GAAMV,EAAkB,WAAYU,EAAEe,OAAO5B,OACxDQ,UAAU,kKAAiKC,SAE1KP,EAAU6B,IAAIvC,IACbkB,EAAAA,EAAAA,KAAA,UAA6BV,MAAOR,EAASQ,MAAMS,SAChDjB,EAASS,OADCT,EAASQ,iBAS9BO,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,eAEjBiC,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASc,SAChBiC,SAAWd,GAAMV,EAAkB,WAAYU,EAAEe,OAAO5B,OACxDQ,UAAU,kKAAiKC,SAAA,EAE3KC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,MAAKS,SAAEnC,EAAE,MAAO,UAC9BoC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAEnC,EAAE,SAAU,aACpCoC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,OAAMS,SAAEnC,EAAE,OAAQ,WAChCoC,EAAAA,EAAAA,KAAA,UAAQV,MAAM,SAAQS,SAAEnC,EAAE,SAAU,mBAIxCiC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,WAAY,eAEjBiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BC,SAAA,EAC1CC,EAAAA,EAAAA,KAAA,SACEpB,KAAK,WACL0C,QAASpD,EAASe,gBAClBgC,SAAWd,GAAMV,EAAkB,kBAAmBU,EAAEe,OAAOI,SAC/DxB,UAAU,+DAEZD,EAAAA,EAAAA,MAAA,UACEP,MAAOpB,EAASgB,aAChB+B,SAAWd,GAAMV,EAAkB,eAAgBU,EAAEe,OAAO5B,OAC5DiC,UAAWrD,EAASe,gBACpBa,UAAU,sLAAqLC,SAAA,EAE/LF,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,gBAAiB,sBAC1CiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,MAAInC,EAAE,gBAAiB,sBAC1CiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,KAAIS,SAAA,CAAC,KAAGnC,EAAE,aAAc,mBACtCiC,EAAAA,EAAAA,MAAA,UAAQP,MAAM,OAAMS,SAAA,CAAC,KAAGnC,EAAE,YAAa,8BAO/CiC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,SAAOF,UAAU,kEAAiEC,SAC/EnC,EAAE,QAAS,YAEdoC,EAAAA,EAAAA,KAAA,YACEV,MAAOpB,EAASa,MAChBkC,SAAWd,GAAMV,EAAkB,QAASU,EAAEe,OAAO5B,OACrDkC,KAAM,EACN1B,UAAU,kKACVsB,YAAaxD,EAAE,aAAc,oCAKjCiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6BAA4BC,SAAA,CACxCrC,IACCsC,EAAAA,EAAAA,KAAA,UACEpB,KAAK,SACLqB,QAASvC,EACToC,UAAU,+FAA8FC,SAEvGnC,EAAE,SAAU,aAGjBoC,EAAAA,EAAAA,KAAA,UACEpB,KAAK,SACL2C,SAAUxD,EACV+B,UAAU,kIAAiIC,SAE1IhC,GACC8B,EAAAA,EAAAA,MAAA4B,EAAAA,SAAA,CAAA1B,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZlC,EAAE,SAAU,iBAGfiC,EAAAA,EAAAA,MAAA4B,EAAAA,SAAA,CAAA1B,SAAA,EACEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,kBAAmB,kC,8GCxVtC,MAoOA,EApOuB8D,KACrB,MAAM,EAAE9D,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,KACf6D,GAAWC,EAAAA,EAAAA,OACVC,IAAgBC,EAAAA,EAAAA,OAChBnE,EAAaoE,IAAkB9D,EAAAA,EAAAA,UAAS,OAE/C+D,EAAAA,EAAAA,WAAU,KAER,MAAM5D,EAAYyD,EAAaI,IAAI,aAC7B5D,EAAcwD,EAAaI,IAAI,eAEjC7D,GAAaC,GACf0D,EAAe,CACb3D,UAAWA,EACXC,YAAa6D,mBAAmB7D,GAChCC,MAAM,IAAIC,MAAOC,cAAcC,MAAM,KAAK,GAC1CC,KAAM,QACNC,SAAU,GACVK,SAAU,YAGb,CAAC6C,IAYJ,OACEhC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,OAAMC,UACnBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCC,SAAA,EAChDF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,mDAAkDC,SAC7DnC,EAAE,yBAA0B,+BAE/BoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCAAuCC,SACjDnC,EAAE,6BAA8B,gDAIrCiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BF,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAM0B,EAAS,iBACxB7B,UAAU,kFAAiFC,SAAA,EAE3FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBACZlC,EAAE,mBAAoB,yBAEzBiC,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAM0B,EAAS,0BACxB7B,UAAU,sFAAqFC,SAAA,EAE/FC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,yBACZlC,EAAE,eAAgB,6BAO3BiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CC,SAAA,EACzDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iDAAgDC,UAC7DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAChEnC,EAAE,iBAAkB,sBAEvBoC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,kBAAmB,iCAK5EoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,mDAAkDC,UAC/DC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAAC,OAGpEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,sBAAuB,mCAKhFoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAAC,OAGpEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,iBAAkB,8BAK3EoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,8FAA6FC,UAC1GF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,qDAAoDC,UACjEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAMC,SAAA,EACnBC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,sDAAqDC,SAAC,QAGpEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,mCAAkCC,SAAEnC,EAAE,iBAAkB,sCAO7EiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCC,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,KAACmC,EAAAA,EAAe,CACdxE,YAAaA,EACbF,SA3GuBS,IAC/BkE,QAAQC,IAAI,2BAA4BnE,GAExCyD,EAAS,kBAyGDjE,SAtGW4E,KACnBX,EAAS,uBA0GL9B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EAExBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,0CACZlC,EAAE,YAAa,kBAElBiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAC/CnC,EAAE,OAAQ,yEAGfiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAC/CnC,EAAE,OAAQ,6DAGfiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAC/CnC,EAAE,OAAQ,kEAGfiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,oDACbE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,mCAAkCC,SAC/CnC,EAAE,OAAQ,iEAOnBiC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,sCACZlC,EAAE,sBAAuB,4BAE5BoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CACC,CAAEwC,KAAM,sBAAuBC,UAAW,mBAAoBC,WAAW,GACzE,CAAEF,KAAM,wBAAyBC,UAAW,uBAAwBC,WAAW,GAC/E,CAAEF,KAAM,sBAAuBC,UAAW,iBAAkBC,WAAW,GACvE,CAAEF,KAAM,yBAA0BC,UAAW,gBAAiBC,WAAW,IACzEpB,IAAI,CAACxC,EAAW6D,KAChB7C,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,4FAA2FC,SAAA,EACpHF,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/DlB,EAAU0D,QAEbvC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,2CAA0CC,SACtDlB,EAAU2D,gBAGfxC,EAAAA,EAAAA,KAAA,OAAKF,UAAS,wBAAAqB,OAA0BtC,EAAU4D,UAAY,eAAiB,kBATvEC,UAgBhB7C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8FAA6FC,SAAA,EAC1GF,EAAAA,EAAAA,MAAA,MAAIC,UAAU,2DAA0DC,SAAA,EACtEC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,wCACZlC,EAAE,qBAAsB,2BAE3BoC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,YAAWC,SACvB,CACC,CAAE4C,QAAS,iBAAkBjE,KAAM,QAASE,KAAM,oBAClD,CAAE+D,QAAS,kBAAmBjE,KAAM,QAASE,KAAM,wBACnD,CAAE+D,QAAS,qBAAsBjE,KAAM,QAASE,KAAM,mBACtDyC,IAAI,CAACuB,EAAaF,KAClB7C,EAAAA,EAAAA,MAAA,OAAiBC,UAAU,0DAAyDC,SAAA,EAClFC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,oDAAmDC,SAC/D6C,EAAYD,WAEf9C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CC,SAAA,CACtD6C,EAAYlE,KAAK,MAAIkE,EAAYhE,UAL5B8D,kB", "sources": ["components/Appointments/AppointmentForm.jsx", "pages/Appointments/NewAppointment.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst AppointmentForm = ({ onSubmit, onCancel, initialData = null }) => {\n  const { t, isRTL } = useLanguage();\n  const [loading, setLoading] = useState(false);\n  \n  const [formData, setFormData] = useState({\n    patientId: initialData?.patientId || '',\n    patientName: initialData?.patientName || '',\n    date: initialData?.date || new Date().toISOString().split('T')[0],\n    time: initialData?.time || '',\n    duration: initialData?.duration || '60',\n    type: initialData?.type || 'consultation',\n    therapist: initialData?.therapist || '',\n    location: initialData?.location || 'room_1',\n    notes: initialData?.notes || '',\n    priority: initialData?.priority || 'normal',\n    reminderEnabled: initialData?.reminderEnabled || true,\n    reminderTime: initialData?.reminderTime || '24'\n  });\n\n  const [errors, setErrors] = useState({});\n\n  // Mock data for dropdowns\n  const appointmentTypes = [\n    { value: 'consultation', label: t('consultation', 'Consultation') },\n    { value: 'assessment', label: t('assessment', 'Assessment') },\n    { value: 'therapy_session', label: t('therapySession', 'Therapy Session') },\n    { value: 'follow_up', label: t('followUp', 'Follow-up') },\n    { value: 'group_therapy', label: t('groupTherapy', 'Group Therapy') },\n    { value: 'evaluation', label: t('evaluation', 'Evaluation') }\n  ];\n\n  const therapists = [\n    { value: 'dr_sarah', label: 'Dr. Sarah Al-Rashid' },\n    { value: 'dr_ahmed', label: 'Dr. Ahmed Al-Mansouri' },\n    { value: 'dr_fatima', label: 'Dr. Fatima Al-Zahra' },\n    { value: 'dr_mohammed', label: 'Dr. Mohammed Al-Khalid' }\n  ];\n\n  const locations = [\n    { value: 'room_1', label: t('room1', 'Room 1 - Main Therapy') },\n    { value: 'room_2', label: t('room2', 'Room 2 - Assessment') },\n    { value: 'room_3', label: t('room3', 'Room 3 - Group Therapy') },\n    { value: 'gym', label: t('gym', 'Gymnasium') },\n    { value: 'pool', label: t('pool', 'Hydrotherapy Pool') }\n  ];\n\n  const timeSlots = [\n    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30',\n    '11:00', '11:30', '12:00', '12:30', '13:00', '13:30',\n    '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',\n    '17:00', '17:30', '18:00'\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: null }));\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    if (!formData.patientName.trim()) {\n      newErrors.patientName = t('patientNameRequired', 'Patient name is required');\n    }\n    if (!formData.date) {\n      newErrors.date = t('dateRequired', 'Date is required');\n    }\n    if (!formData.time) {\n      newErrors.time = t('timeRequired', 'Time is required');\n    }\n    if (!formData.therapist) {\n      newErrors.therapist = t('therapistRequired', 'Therapist is required');\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      toast.error(t('pleaseFixErrors', 'Please fix the errors before submitting'));\n      return;\n    }\n\n    setLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      if (onSubmit) {\n        onSubmit(formData);\n      }\n      \n      toast.success(t('appointmentSaved', 'Appointment saved successfully'));\n    } catch (error) {\n      toast.error(t('errorSavingAppointment', 'Error saving appointment'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n          {initialData ? t('editAppointment', 'Edit Appointment') : t('newAppointment', 'New Appointment')}\n        </h2>\n        {onCancel && (\n          <button\n            onClick={onCancel}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <i className=\"fas fa-times text-xl\"></i>\n          </button>\n        )}\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Patient Information */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('patientName', 'Patient Name')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"text\"\n              value={formData.patientName}\n              onChange={(e) => handleInputChange('patientName', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.patientName ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder={t('enterPatientName', 'Enter patient name')}\n            />\n            {errors.patientName && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.patientName}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('appointmentType', 'Appointment Type')}\n            </label>\n            <select\n              value={formData.type}\n              onChange={(e) => handleInputChange('type', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {appointmentTypes.map(type => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Date and Time */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('date', 'Date')} <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              type=\"date\"\n              value={formData.date}\n              onChange={(e) => handleInputChange('date', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.date ? 'border-red-500' : 'border-gray-300'\n              }`}\n            />\n            {errors.date && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.date}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('time', 'Time')} <span className=\"text-red-500\">*</span>\n            </label>\n            <select\n              value={formData.time}\n              onChange={(e) => handleInputChange('time', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.time ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">{t('selectTime', 'Select time')}</option>\n              {timeSlots.map(time => (\n                <option key={time} value={time}>\n                  {time}\n                </option>\n              ))}\n            </select>\n            {errors.time && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.time}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('duration', 'Duration (minutes)')}\n            </label>\n            <select\n              value={formData.duration}\n              onChange={(e) => handleInputChange('duration', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"30\">30 {t('minutes', 'minutes')}</option>\n              <option value=\"45\">45 {t('minutes', 'minutes')}</option>\n              <option value=\"60\">60 {t('minutes', 'minutes')}</option>\n              <option value=\"90\">90 {t('minutes', 'minutes')}</option>\n              <option value=\"120\">120 {t('minutes', 'minutes')}</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Therapist and Location */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('therapist', 'Therapist')} <span className=\"text-red-500\">*</span>\n            </label>\n            <select\n              value={formData.therapist}\n              onChange={(e) => handleInputChange('therapist', e.target.value)}\n              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${\n                errors.therapist ? 'border-red-500' : 'border-gray-300'\n              }`}\n            >\n              <option value=\"\">{t('selectTherapist', 'Select therapist')}</option>\n              {therapists.map(therapist => (\n                <option key={therapist.value} value={therapist.value}>\n                  {therapist.label}\n                </option>\n              ))}\n            </select>\n            {errors.therapist && (\n              <p className=\"text-red-500 text-sm mt-1\">{errors.therapist}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('location', 'Location')}\n            </label>\n            <select\n              value={formData.location}\n              onChange={(e) => handleInputChange('location', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              {locations.map(location => (\n                <option key={location.value} value={location.value}>\n                  {location.label}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Priority and Reminder */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('priority', 'Priority')}\n            </label>\n            <select\n              value={formData.priority}\n              onChange={(e) => handleInputChange('priority', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            >\n              <option value=\"low\">{t('low', 'Low')}</option>\n              <option value=\"normal\">{t('normal', 'Normal')}</option>\n              <option value=\"high\">{t('high', 'High')}</option>\n              <option value=\"urgent\">{t('urgent', 'Urgent')}</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              {t('reminder', 'Reminder')}\n            </label>\n            <div className=\"flex items-center space-x-2\">\n              <input\n                type=\"checkbox\"\n                checked={formData.reminderEnabled}\n                onChange={(e) => handleInputChange('reminderEnabled', e.target.checked)}\n                className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n              />\n              <select\n                value={formData.reminderTime}\n                onChange={(e) => handleInputChange('reminderTime', e.target.value)}\n                disabled={!formData.reminderEnabled}\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white disabled:opacity-50\"\n              >\n                <option value=\"15\">15 {t('minutesBefore', 'minutes before')}</option>\n                <option value=\"30\">30 {t('minutesBefore', 'minutes before')}</option>\n                <option value=\"60\">1 {t('hourBefore', 'hour before')}</option>\n                <option value=\"1440\">1 {t('dayBefore', 'day before')}</option>\n              </select>\n            </div>\n          </div>\n        </div>\n\n        {/* Notes */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            {t('notes', 'Notes')}\n          </label>\n          <textarea\n            value={formData.notes}\n            onChange={(e) => handleInputChange('notes', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white\"\n            placeholder={t('enterNotes', 'Enter any additional notes')}\n          />\n        </div>\n\n        {/* Form Actions */}\n        <div className=\"flex justify-end space-x-4\">\n          {onCancel && (\n            <button\n              type=\"button\"\n              onClick={onCancel}\n              className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              {t('cancel', 'Cancel')}\n            </button>\n          )}\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            {loading ? (\n              <>\n                <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n                {t('saving', 'Saving...')}\n              </>\n            ) : (\n              <>\n                <i className=\"fas fa-save mr-2\"></i>\n                {t('saveAppointment', 'Save Appointment')}\n              </>\n            )}\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n};\n\nexport default AppointmentForm;\n", "import React, { useEffect, useState } from 'react';\nimport { useNavigate, useSearchParams } from 'react-router-dom';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport AppointmentForm from '../../components/Appointments/AppointmentForm';\n\nconst NewAppointment = () => {\n  const { t, isRTL } = useLanguage();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const [initialData, setInitialData] = useState(null);\n\n  useEffect(() => {\n    // Get patient information from URL parameters\n    const patientId = searchParams.get('patientId');\n    const patientName = searchParams.get('patientName');\n\n    if (patientId && patientName) {\n      setInitialData({\n        patientId: patientId,\n        patientName: decodeURIComponent(patientName),\n        date: new Date().toISOString().split('T')[0], // Today's date\n        time: '09:00', // Default time\n        duration: 60, // Default duration\n        priority: 'normal'\n      });\n    }\n  }, [searchParams]);\n\n  const handleAppointmentSubmit = (formData) => {\n    console.log('New appointment created:', formData);\n    // Here you would typically save the appointment to your backend\n    navigate('/appointments');\n  };\n\n  const handleCancel = () => {\n    navigate('/appointments');\n  };\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              {t('scheduleNewAppointment', 'Schedule New Appointment')}\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n              {t('scheduleNewAppointmentDesc', 'Create a new appointment for a patient')}\n            </p>\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <button\n              onClick={() => navigate('/appointments')}\n              className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n            >\n              <i className=\"fas fa-list mr-2\"></i>\n              {t('viewAppointments', 'View Appointments')}\n            </button>\n            <button\n              onClick={() => navigate('/appointments/calendar')}\n              className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors\"\n            >\n              <i className=\"fas fa-calendar mr-2\"></i>\n              {t('calendarView', 'Calendar View')}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-calendar-plus text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('newAppointment', 'New Appointment')}\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('schedulePatient', 'Schedule a patient')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-user-md text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                4\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('availableTherapists', 'Available Therapists')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-door-open text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                5\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('availableRooms', 'Available Rooms')}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <h4 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                12\n              </h4>\n              <p className=\"text-gray-500 dark:text-gray-400\">{t('availableSlots', 'Available Time Slots')}</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Appointment Form */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2\">\n          <AppointmentForm\n            initialData={initialData}\n            onSubmit={handleAppointmentSubmit}\n            onCancel={handleCancel}\n          />\n        </div>\n        \n        {/* Sidebar with helpful information */}\n        <div className=\"space-y-6\">\n          {/* Quick Tips */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-lightbulb text-yellow-500 mr-2\"></i>\n              {t('quickTips', 'Quick Tips')}\n            </h3>\n            <div className=\"space-y-3 text-sm\">\n              <div className=\"flex items-start\">\n                <i className=\"fas fa-check-circle text-green-500 mr-2 mt-0.5\"></i>\n                <span className=\"text-gray-600 dark:text-gray-400\">\n                  {t('tip1', 'Select the appropriate therapy type for the patient\\'s condition')}\n                </span>\n              </div>\n              <div className=\"flex items-start\">\n                <i className=\"fas fa-check-circle text-green-500 mr-2 mt-0.5\"></i>\n                <span className=\"text-gray-600 dark:text-gray-400\">\n                  {t('tip2', 'Consider the patient\\'s availability and preferences')}\n                </span>\n              </div>\n              <div className=\"flex items-start\">\n                <i className=\"fas fa-check-circle text-green-500 mr-2 mt-0.5\"></i>\n                <span className=\"text-gray-600 dark:text-gray-400\">\n                  {t('tip3', 'Set reminders to ensure patients don\\'t miss appointments')}\n                </span>\n              </div>\n              <div className=\"flex items-start\">\n                <i className=\"fas fa-check-circle text-green-500 mr-2 mt-0.5\"></i>\n                <span className=\"text-gray-600 dark:text-gray-400\">\n                  {t('tip4', 'Add detailed notes for better session preparation')}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Available Therapists */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-user-md text-blue-500 mr-2\"></i>\n              {t('availableTherapists', 'Available Therapists')}\n            </h3>\n            <div className=\"space-y-3\">\n              {[\n                { name: 'Dr. Sarah Al-Rashid', specialty: 'Physical Therapy', available: true },\n                { name: 'Dr. Ahmed Al-Mansouri', specialty: 'Occupational Therapy', available: true },\n                { name: 'Dr. Fatima Al-Zahra', specialty: 'Speech Therapy', available: true },\n                { name: 'Dr. Mohammed Al-Khalid', specialty: 'Group Therapy', available: false }\n              ].map((therapist, index) => (\n                <div key={index} className=\"flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded\">\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {therapist.name}\n                    </div>\n                    <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                      {therapist.specialty}\n                    </div>\n                  </div>\n                  <div className={`w-3 h-3 rounded-full ${therapist.available ? 'bg-green-500' : 'bg-red-500'}`}></div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Recent Appointments */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n              <i className=\"fas fa-history text-purple-500 mr-2\"></i>\n              {t('recentAppointments', 'Recent Appointments')}\n            </h3>\n            <div className=\"space-y-3\">\n              {[\n                { patient: 'Ahmed Al-Ahmed', time: '10:00', type: 'Physical Therapy' },\n                { patient: 'Fatima Al-Salem', time: '11:30', type: 'Occupational Therapy' },\n                { patient: 'Mohammed Al-Khalid', time: '14:00', type: 'Speech Therapy' }\n              ].map((appointment, index) => (\n                <div key={index} className=\"p-2 border border-gray-200 dark:border-gray-600 rounded\">\n                  <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {appointment.patient}\n                  </div>\n                  <div className=\"text-xs text-gray-600 dark:text-gray-400\">\n                    {appointment.time} - {appointment.type}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NewAppointment;\n"], "names": ["_ref", "onSubmit", "onCancel", "initialData", "t", "isRTL", "useLanguage", "loading", "setLoading", "useState", "formData", "setFormData", "patientId", "patientName", "date", "Date", "toISOString", "split", "time", "duration", "type", "therapist", "location", "notes", "priority", "reminderEnabled", "reminderTime", "errors", "setErrors", "appointmentTypes", "value", "label", "locations", "handleInputChange", "field", "prev", "_objectSpread", "_jsxs", "className", "children", "_jsx", "onClick", "async", "e", "preventDefault", "validateForm", "newErrors", "trim", "Object", "keys", "length", "Promise", "resolve", "setTimeout", "toast", "success", "error", "onChange", "target", "concat", "placeholder", "map", "checked", "disabled", "rows", "_Fragment", "NewAppointment", "navigate", "useNavigate", "searchParams", "useSearchParams", "setInitialData", "useEffect", "get", "decodeURIComponent", "AppointmentForm", "console", "log", "handleCancel", "name", "specialty", "available", "index", "patient", "appointment"], "sourceRoot": ""}