"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[2844,6813],{2844:(e,s,r)=>{r.r(s),r.d(s,{default:()=>u});var a=r(5043),t=r(7921),i=r(579);const n=e=>{let{patient:s,onUpdate:r}=e;const{t:n}=(0,t.o)(),[o,l]=(0,a.useState)("diagnosis"),d=[{id:"autism",label:n("autism","Autism Spectrum Disorder"),icon:"\ud83e\udde9"},{id:"cerebral_palsy",label:n("cerebralPalsy","Cerebral Palsy"),icon:"\ud83e\uddbd"},{id:"down_syndrome",label:n("downSyndrome","Down Syndrome"),icon:"\ud83d\udc99"},{id:"intellectual_disability",label:n("intellectualDisability","Intellectual Disability"),icon:"\ud83e\udde0"},{id:"spina_bifida",label:n("spinaBifida","Spina Bifida"),icon:"\ud83e\uddb4"},{id:"muscular_dystrophy",label:n("muscularDystrophy","Muscular Dystrophy"),icon:"\ud83d\udcaa"},{id:"adhd",label:n("adhd","ADHD"),icon:"\u26a1"},{id:"sensory_processing",label:n("sensoryProcessing","Sensory Processing Disorder"),icon:"\ud83d\udc41\ufe0f"}],c=[{id:"verbal",label:n("verbal","Verbal Communication"),icon:"\ud83d\udde3\ufe0f"},{id:"sign_language",label:n("signLanguage","Sign Language"),icon:"\ud83d\udc4b"},{id:"picture_cards",label:n("pictureCards","Picture Cards/PECS"),icon:"\ud83d\uddbc\ufe0f"},{id:"aac_device",label:n("aacDevice","AAC Device"),icon:"\ud83d\udcf1"},{id:"gestures",label:n("gestures","Gestures/Body Language"),icon:"\ud83d\udc50"},{id:"written",label:n("written","Written Communication"),icon:"\u270d\ufe0f"}],m=[{id:"visual",label:n("visualStimuli","Visual Stimuli"),options:["low","moderate","high","avoid"]},{id:"auditory",label:n("auditoryStimuli","Auditory Stimuli"),options:["quiet","soft","moderate","avoid_loud"]},{id:"tactile",label:n("tactileInput","Tactile Input"),options:["light_touch","firm_pressure","avoid_touch","seeks_input"]},{id:"vestibular",label:n("vestibularInput","Vestibular Input"),options:["slow_movement","fast_movement","avoid_movement","seeks_movement"]},{id:"proprioceptive",label:n("proprioceptiveInput","Proprioceptive Input"),options:["light","moderate","heavy","seeks_input"]}],g=[{id:"diagnosis",label:n("diagnosis","Diagnosis & Conditions"),icon:"\ud83c\udfe5"},{id:"communication",label:n("communication","Communication"),icon:"\ud83d\udcac"},{id:"sensory",label:n("sensoryProfile","Sensory Profile"),icon:"\ud83d\udc41\ufe0f"},{id:"behavioral",label:n("behavioral","Behavioral Patterns"),icon:"\ud83d\udcca"},{id:"medical",label:n("medicalInfo","Medical Information"),icon:"\u2695\ufe0f"},{id:"family",label:n("familyInfo","Family & Caregivers"),icon:"\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d\udc66"}];return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:n("specialNeedsProfile","Special Needs Patient Profile")}),(0,i.jsx)("div",{className:"flex items-center space-x-2",children:(0,i.jsx)("span",{className:"px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium",children:(null===s||void 0===s?void 0:s.primaryDiagnosis)||n("unspecified","Unspecified")})})]}),(0,i.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,i.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:g.map(e=>(0,i.jsxs)("button",{onClick:()=>l(e.id),className:"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(o===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,i.jsx)("span",{children:e.icon}),(0,i.jsx)("span",{children:e.label})]},e.id))})}),(0,i.jsxs)("div",{className:"space-y-6",children:["diagnosis"===o&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("primarySecondaryDiagnoses","Primary & Secondary Diagnoses")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:d.map(e=>(0,i.jsx)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("span",{className:"text-2xl",children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.label}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"radio",name:"primaryDiagnosis",value:e.id,className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("primary","Primary")})]}),(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",name:"secondaryDiagnoses",value:e.id,className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("secondary","Secondary")})]})]})]})]})},e.id))})]}),"communication"===o&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("communicationMethods","Communication Methods & Preferences")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:c.map(e=>(0,i.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,i.jsx)("span",{className:"text-xl",children:e.icon}),(0,i.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.label})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("canUse","Can Use")})]}),(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("preferred","Preferred Method")})]}),(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:n("needsSupport","Needs Support")})]})]})]},e.id))}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:n("communicationNotes","Communication Notes & Strategies")}),(0,i.jsx)("textarea",{className:"w-full p-3 border border-yellow-200 dark:border-yellow-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",rows:"4",placeholder:n("communicationNotesPlaceholder","Enter specific communication strategies, triggers to avoid, successful approaches, etc.")})]})]}),"sensory"===o&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("sensoryProcessingProfile","Sensory Processing Profile")}),(0,i.jsx)("div",{className:"space-y-6",children:m.map(e=>(0,i.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:e.label}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:e.options.map(s=>(0,i.jsxs)("label",{className:"flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",children:[(0,i.jsx)("input",{type:"radio",name:"sensory_".concat(e.id),value:s,className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:n(s.replace("_",""),s.replace("_"," "))})]},s))})]},e.id))}),(0,i.jsxs)("div",{className:"mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-medium text-green-800 dark:text-green-200 mb-2",children:n("sensoryStrategies","Sensory Strategies & Accommodations")}),(0,i.jsx)("textarea",{className:"w-full p-3 border border-green-200 dark:border-green-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",rows:"4",placeholder:n("sensoryStrategiesPlaceholder","Enter sensory breaks needed, calming strategies, environmental modifications, etc.")})]})]}),"behavioral"===o&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("behavioralPatterns","Behavioral Patterns & Management")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\u26a0\ufe0f"}),n("behavioralTriggers","Behavioral Triggers")]}),(0,i.jsx)("div",{className:"space-y-2",children:["loud_noises","bright_lights","unexpected_changes","crowded_spaces","physical_contact","waiting"].map(e=>(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:n(e,e.replace("_"," "))})]},e))})]}),(0,i.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\u2705"}),n("calmingStrategies","Calming Strategies")]}),(0,i.jsx)("div",{className:"space-y-2",children:["deep_breathing","fidget_toys","quiet_space","music","movement_breaks","visual_schedules"].map(e=>(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm text-gray-700 dark:text-gray-300",children:n(e,e.replace("_"," "))})]},e))})]})]})]})]}),(0,i.jsx)("div",{className:"mt-8 flex justify-end",children:(0,i.jsx)("button",{onClick:()=>null===r||void 0===r?void 0:r(s),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:n("saveProfile","Save Profile")})})]})},o=e=>{let{onMessageSelect:s}=e;const{t:r}=(0,t.o)(),[n,o]=(0,a.useState)("feelings"),[l,d]=(0,a.useState)([]),c={feelings:{label:r("feelings","Feelings"),icon:"\ud83d\ude0a",color:"bg-yellow-100 border-yellow-300 text-yellow-800",items:[{id:"happy",text:r("happy","Happy"),icon:"\ud83d\ude0a",color:"bg-yellow-200"},{id:"sad",text:r("sad","Sad"),icon:"\ud83d\ude22",color:"bg-blue-200"},{id:"angry",text:r("angry","Angry"),icon:"\ud83d\ude20",color:"bg-red-200"},{id:"scared",text:r("scared","Scared"),icon:"\ud83d\ude28",color:"bg-purple-200"},{id:"tired",text:r("tired","Tired"),icon:"\ud83d\ude34",color:"bg-gray-200"},{id:"excited",text:r("excited","Excited"),icon:"\ud83e\udd29",color:"bg-orange-200"},{id:"confused",text:r("confused","Confused"),icon:"\ud83d\ude15",color:"bg-indigo-200"},{id:"calm",text:r("calm","Calm"),icon:"\ud83d\ude0c",color:"bg-green-200"}]},needs:{label:r("needs","Needs"),icon:"\ud83d\ude4b",color:"bg-blue-100 border-blue-300 text-blue-800",items:[{id:"water",text:r("water","Water"),icon:"\ud83d\udca7",color:"bg-blue-200"},{id:"food",text:r("food","Food"),icon:"\ud83c\udf4e",color:"bg-red-200"},{id:"bathroom",text:r("bathroom","Bathroom"),icon:"\ud83d\udebb",color:"bg-gray-200"},{id:"break",text:r("break","Break"),icon:"\u23f8\ufe0f",color:"bg-yellow-200"},{id:"help",text:r("help","Help"),icon:"\ud83c\udd98",color:"bg-red-200"},{id:"quiet",text:r("quiet","Quiet"),icon:"\ud83e\udd2b",color:"bg-purple-200"},{id:"move",text:r("move","Move"),icon:"\ud83c\udfc3",color:"bg-green-200"},{id:"stop",text:r("stop","Stop"),icon:"\u270b",color:"bg-red-200"}]},activities:{label:r("activities","Activities"),icon:"\ud83c\udfaf",color:"bg-green-100 border-green-300 text-green-800",items:[{id:"exercise",text:r("exercise","Exercise"),icon:"\ud83c\udfcb\ufe0f",color:"bg-green-200"},{id:"walk",text:r("walk","Walk"),icon:"\ud83d\udeb6",color:"bg-blue-200"},{id:"stretch",text:r("stretch","Stretch"),icon:"\ud83e\udd38",color:"bg-purple-200"},{id:"balance",text:r("balance","Balance"),icon:"\u2696\ufe0f",color:"bg-yellow-200"},{id:"play",text:r("play","Play"),icon:"\ud83c\udfae",color:"bg-pink-200"},{id:"music",text:r("music","Music"),icon:"\ud83c\udfb5",color:"bg-indigo-200"},{id:"draw",text:r("draw","Draw"),icon:"\ud83c\udfa8",color:"bg-orange-200"},{id:"read",text:r("read","Read"),icon:"\ud83d\udcda",color:"bg-brown-200"}]},pain:{label:r("pain","Pain/Discomfort"),icon:"\ud83e\udd15",color:"bg-red-100 border-red-300 text-red-800",items:[{id:"head_pain",text:r("headPain","Head Hurts"),icon:"\ud83e\udd15",color:"bg-red-200"},{id:"back_pain",text:r("backPain","Back Hurts"),icon:"\ud83e\udef8",color:"bg-red-200"},{id:"leg_pain",text:r("legPain","Leg Hurts"),icon:"\ud83e\uddb5",color:"bg-red-200"},{id:"arm_pain",text:r("armPain","Arm Hurts"),icon:"\ud83d\udcaa",color:"bg-red-200"},{id:"stomach_pain",text:r("stomachPain","Stomach Hurts"),icon:"\ud83e\udd30",color:"bg-red-200"},{id:"no_pain",text:r("noPain","No Pain"),icon:"\u2705",color:"bg-green-200"},{id:"little_pain",text:r("littlePain","Little Pain"),icon:"\ud83d\ude10",color:"bg-yellow-200"},{id:"big_pain",text:r("bigPain","Big Pain"),icon:"\ud83d\ude23",color:"bg-red-300"}]},responses:{label:r("responses","Yes/No/More"),icon:"\u2705",color:"bg-purple-100 border-purple-300 text-purple-800",items:[{id:"yes",text:r("yes","Yes"),icon:"\u2705",color:"bg-green-200"},{id:"no",text:r("no","No"),icon:"\u274c",color:"bg-red-200"},{id:"maybe",text:r("maybe","Maybe"),icon:"\ud83e\udd14",color:"bg-yellow-200"},{id:"more",text:r("more","More"),icon:"\u2795",color:"bg-blue-200"},{id:"less",text:r("less","Less"),icon:"\u2796",color:"bg-orange-200"},{id:"finished",text:r("finished","Finished"),icon:"\u2714\ufe0f",color:"bg-green-200"},{id:"again",text:r("again","Again"),icon:"\ud83d\udd04",color:"bg-purple-200"},{id:"different",text:r("different","Different"),icon:"\ud83d\udd00",color:"bg-indigo-200"}]}},m=e=>{const r={id:Date.now(),text:e.text,icon:e.icon,category:n,timestamp:new Date};d(e=>[...e,r]),null===s||void 0===s||s(r)};return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,i.jsx)("span",{className:"mr-3",children:"\ud83d\udcac"}),r("communicationBoard","Communication Board")]}),(0,i.jsx)("button",{onClick:()=>{d([])},className:"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:r("clear","Clear")})]}),l.length>0&&(0,i.jsxs)("div",{className:"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,i.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:r("selectedMessages","Selected Messages")}),(0,i.jsxs)("button",{onClick:()=>(e=>{if("speechSynthesis"in window){const s=new SpeechSynthesisUtterance(e);s.lang="en-US",speechSynthesis.speak(s)}})(l.map(e=>e.text).join(", ")),className:"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600",children:["\ud83d\udd0a ",r("speak","Speak")]})]}),(0,i.jsx)("div",{className:"flex flex-wrap gap-2",children:l.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2 bg-white dark:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-500",children:[(0,i.jsx)("span",{className:"text-lg",children:e.icon}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.text}),(0,i.jsx)("button",{onClick:()=>d(s=>s.filter(s=>s.id!==e.id)),className:"text-red-500 hover:text-red-700 text-xs",children:"\u2715"})]},e.id))})]}),(0,i.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,i.jsx)("nav",{className:"-mb-px flex space-x-4 overflow-x-auto",children:Object.entries(c).map(e=>{let[s,r]=e;return(0,i.jsxs)("button",{onClick:()=>o(s),className:"whitespace-nowrap py-3 px-4 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ".concat(n===s?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,i.jsx)("span",{className:"text-lg",children:r.icon}),(0,i.jsx)("span",{children:r.label})]},s)})})}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4",children:c[n].items.map(e=>(0,i.jsx)("button",{onClick:()=>m(e),className:"p-6 rounded-lg border-2 border-dashed transition-all duration-200 hover:scale-105 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ".concat(e.color," hover:border-solid"),children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-4xl mb-2",children:e.icon}),(0,i.jsx)("div",{className:"text-sm font-medium text-gray-800 dark:text-gray-900",children:e.text})]})},e.id))}),(0,i.jsxs)("div",{className:"mt-6 flex justify-center space-x-4",children:[(0,i.jsxs)("button",{onClick:()=>m({text:r("iAmReady","I am ready"),icon:"\ud83d\udc4d",color:"bg-green-200"}),className:"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center space-x-2",children:[(0,i.jsx)("span",{children:"\ud83d\udc4d"}),(0,i.jsx)("span",{children:r("iAmReady","I am ready")})]}),(0,i.jsxs)("button",{onClick:()=>m({text:r("iNeedHelp","I need help"),icon:"\ud83c\udd98",color:"bg-red-200"}),className:"px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center space-x-2",children:[(0,i.jsx)("span",{children:"\ud83c\udd98"}),(0,i.jsx)("span",{children:r("iNeedHelp","I need help")})]}),(0,i.jsxs)("button",{onClick:()=>m({text:r("allDone","All done"),icon:"\u2705",color:"bg-blue-200"}),className:"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center space-x-2",children:[(0,i.jsx)("span",{children:"\u2705"}),(0,i.jsx)("span",{children:r("allDone","All done")})]})]})]})};var l=r(2555);const d=e=>{var s;let{patientProfile:r,onEnvironmentChange:n}=e;const{t:o}=(0,t.o)(),[d,c]=(0,a.useState)({lighting:{brightness:50,color:"warm",flashing:!1},sound:{volume:30,type:"nature",enabled:!1},visual:{animations:!1,contrast:"normal",colorScheme:"default"},alerts:{visual:!0,audio:!1,vibration:!1}}),[m,g]=(0,a.useState)("custom"),x={autism_friendly:{name:o("autismFriendly","Autism Friendly"),icon:"\ud83e\udde9",description:o("autismFriendlyDesc","Low stimulation environment"),settings:{lighting:{brightness:30,color:"cool",flashing:!1},sound:{volume:10,type:"silence",enabled:!1},visual:{animations:!1,contrast:"high",colorScheme:"calm"},alerts:{visual:!0,audio:!1,vibration:!1}}},sensory_seeking:{name:o("sensorySeeking","Sensory Seeking"),icon:"\ud83c\udf08",description:o("sensorySeekingDesc","Enhanced sensory input"),settings:{lighting:{brightness:70,color:"dynamic",flashing:!1},sound:{volume:50,type:"upbeat",enabled:!0},visual:{animations:!0,contrast:"normal",colorScheme:"vibrant"},alerts:{visual:!0,audio:!0,vibration:!0}}},calming:{name:o("calming","Calming"),icon:"\ud83d\udd6f\ufe0f",description:o("calmingDesc","Soothing environment for relaxation"),settings:{lighting:{brightness:20,color:"warm",flashing:!1},sound:{volume:25,type:"nature",enabled:!0},visual:{animations:!1,contrast:"low",colorScheme:"soft"},alerts:{visual:!0,audio:!1,vibration:!1}}},focus:{name:o("focus","Focus"),icon:"\ud83c\udfaf",description:o("focusDesc","Minimal distractions for concentration"),settings:{lighting:{brightness:60,color:"neutral",flashing:!1},sound:{volume:0,type:"silence",enabled:!1},visual:{animations:!1,contrast:"high",colorScheme:"minimal"},alerts:{visual:!0,audio:!1,vibration:!1}}}},h=[{id:"silence",name:o("silence","Silence"),icon:"\ud83d\udd07"},{id:"nature",name:o("natureSounds","Nature Sounds"),icon:"\ud83c\udf3f"},{id:"white_noise",name:o("whiteNoise","White Noise"),icon:"\ud83d\udcfb"},{id:"classical",name:o("classical","Classical Music"),icon:"\ud83c\udfbc"},{id:"upbeat",name:o("upbeat","Upbeat Music"),icon:"\ud83c\udfb5"}],u=[{id:"default",name:o("default","Default"),colors:["#3B82F6","#10B981","#F59E0B"]},{id:"calm",name:o("calm","Calm"),colors:["#6B7280","#9CA3AF","#D1D5DB"]},{id:"vibrant",name:o("vibrant","Vibrant"),colors:["#EF4444","#F97316","#EAB308"]},{id:"soft",name:o("soft","Soft"),colors:["#F3E8FF","#FEF3C7","#ECFDF5"]},{id:"minimal",name:o("minimal","Minimal"),colors:["#000000","#FFFFFF","#6B7280"]}];(0,a.useEffect)(()=>{null===n||void 0===n||n(d)},[d,n]);const b=(e,s,r)=>{c(a=>(0,l.A)((0,l.A)({},a),{},{[e]:(0,l.A)((0,l.A)({},a[e]),{},{[s]:r})})),g("custom")};return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,i.jsx)("span",{className:"mr-3",children:"\ud83c\udf9b\ufe0f"}),o("sensoryEnvironmentControls","Sensory Environment Controls")]}),(0,i.jsx)("div",{className:"flex items-center space-x-2",children:(0,i.jsx)("span",{className:"px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm font-medium",children:(null===(s=x[m])||void 0===s?void 0:s.name)||o("custom","Custom")})})]}),(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("quickPresets","Quick Presets")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Object.entries(x).map(e=>{let[s,r]=e;return(0,i.jsx)("button",{onClick:()=>(e=>{const s=x[e];s&&(c(s.settings),g(e))})(s),className:"p-4 rounded-lg border-2 transition-all duration-200 hover:scale-105 ".concat(m===s?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl mb-2",children:r.icon}),(0,i.jsx)("div",{className:"font-medium text-gray-900 dark:text-white mb-1",children:r.name}),(0,i.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:r.description})]})},s)})})]}),(0,i.jsxs)("div",{className:"space-y-8",children:[(0,i.jsxs)("div",{className:"p-6 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,i.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\ud83d\udca1"}),o("lightingControls","Lighting Controls")]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("brightness","Brightness")}),(0,i.jsx)("input",{type:"range",min:"0",max:"100",value:d.lighting.brightness,onChange:e=>b("lighting","brightness",parseInt(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,i.jsxs)("div",{className:"text-center text-sm text-gray-600 dark:text-gray-400 mt-1",children:[d.lighting.brightness,"%"]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("colorTemperature","Color Temperature")}),(0,i.jsxs)("select",{value:d.lighting.color,onChange:e=>b("lighting","color",e.target.value),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"warm",children:o("warm","Warm")}),(0,i.jsx)("option",{value:"neutral",children:o("neutral","Neutral")}),(0,i.jsx)("option",{value:"cool",children:o("cool","Cool")}),(0,i.jsx)("option",{value:"dynamic",children:o("dynamic","Dynamic")})]})]}),(0,i.jsx)("div",{children:(0,i.jsxs)("label",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"checkbox",checked:d.lighting.flashing,onChange:e=>b("lighting","flashing",e.target.checked),className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:o("allowFlashing","Allow Flashing")})]})})]})]}),(0,i.jsxs)("div",{className:"p-6 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,i.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\ud83d\udd0a"}),o("soundControls","Sound Controls")]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"flex items-center mb-4",children:[(0,i.jsx)("input",{type:"checkbox",checked:d.sound.enabled,onChange:e=>b("sound","enabled",e.target.checked),className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:o("enableSound","Enable Sound")})]}),d.sound.enabled&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("volume","Volume")}),(0,i.jsx)("input",{type:"range",min:"0",max:"100",value:d.sound.volume,onChange:e=>b("sound","volume",parseInt(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,i.jsxs)("div",{className:"text-center text-sm text-gray-600 dark:text-gray-400 mt-1",children:[d.sound.volume,"%"]})]})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("soundType","Sound Type")}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:h.map(e=>(0,i.jsxs)("button",{onClick:()=>b("sound","type",e.id),className:"p-3 rounded-lg border text-center transition-colors ".concat(d.sound.type===e.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:[(0,i.jsx)("div",{className:"text-lg mb-1",children:e.icon}),(0,i.jsx)("div",{className:"text-xs font-medium",children:e.name})]},e.id))})]})]})]}),(0,i.jsxs)("div",{className:"p-6 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,i.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\ud83d\udc41\ufe0f"}),o("visualControls","Visual Controls")]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"flex items-center mb-4",children:[(0,i.jsx)("input",{type:"checkbox",checked:d.visual.animations,onChange:e=>b("visual","animations",e.target.checked),className:"mr-2"}),(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:o("enableAnimations","Enable Animations")})]}),(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("contrast","Contrast")}),(0,i.jsxs)("select",{value:d.visual.contrast,onChange:e=>b("visual","contrast",e.target.value),className:"w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"low",children:o("low","Low")}),(0,i.jsx)("option",{value:"normal",children:o("normal","Normal")}),(0,i.jsx)("option",{value:"high",children:o("high","High")})]})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:o("colorScheme","Color Scheme")}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-2",children:u.map(e=>(0,i.jsxs)("button",{onClick:()=>b("visual","colorScheme",e.id),className:"p-3 rounded-lg border text-center transition-colors ".concat(d.visual.colorScheme===e.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:[(0,i.jsx)("div",{className:"flex justify-center space-x-1 mb-2",children:e.colors.map((e,s)=>(0,i.jsx)("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:e}},s))}),(0,i.jsx)("div",{className:"text-xs font-medium text-gray-900 dark:text-white",children:e.name})]},e.id))})]})]})]})]}),(0,i.jsxs)("div",{className:"mt-8 flex justify-end space-x-4",children:[(0,i.jsx)("button",{onClick:()=>c({lighting:{brightness:50,color:"warm",flashing:!1},sound:{volume:30,type:"nature",enabled:!1},visual:{animations:!1,contrast:"normal",colorScheme:"default"},alerts:{visual:!0,audio:!1,vibration:!1}}),className:"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:o("reset","Reset")}),(0,i.jsx)("button",{onClick:()=>null===n||void 0===n?void 0:n(d),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:o("applySettings","Apply Settings")})]})]})},c=e=>{let{patientId:s,onBehaviorLog:r}=e;const{t:n}=(0,t.o)(),[o,d]=(0,a.useState)("log"),[c,m]=(0,a.useState)({behavior:"",intensity:3,duration:"",triggers:[],interventions:[],outcome:"",notes:""}),g=[{id:"self_stimming",label:n("selfStimming","Self-Stimming"),icon:"\ud83d\udd04",color:"bg-blue-100"},{id:"aggression",label:n("aggression","Aggression"),icon:"\ud83d\ude20",color:"bg-red-100"},{id:"withdrawal",label:n("withdrawal","Withdrawal"),icon:"\ud83d\ude14",color:"bg-gray-100"},{id:"anxiety",label:n("anxiety","Anxiety"),icon:"\ud83d\ude30",color:"bg-yellow-100"},{id:"meltdown",label:n("meltdown","Meltdown"),icon:"\ud83d\ude2d",color:"bg-orange-100"},{id:"cooperation",label:n("cooperation","Cooperation"),icon:"\ud83e\udd1d",color:"bg-green-100"},{id:"communication",label:n("communication","Communication"),icon:"\ud83d\udcac",color:"bg-purple-100"},{id:"focus",label:n("focus","Focus/Attention"),icon:"\ud83c\udfaf",color:"bg-indigo-100"}],x=[{id:"loud_noise",label:n("loudNoise","Loud Noise"),icon:"\ud83d\udd0a"},{id:"bright_lights",label:n("brightLights","Bright Lights"),icon:"\ud83d\udca1"},{id:"crowded_space",label:n("crowdedSpace","Crowded Space"),icon:"\ud83d\udc65"},{id:"unexpected_change",label:n("unexpectedChange","Unexpected Change"),icon:"\ud83d\udd04"},{id:"physical_contact",label:n("physicalContact","Physical Contact"),icon:"\ud83e\udd1a"},{id:"waiting",label:n("waiting","Waiting"),icon:"\u23f0"},{id:"new_person",label:n("newPerson","New Person"),icon:"\ud83d\udc64"},{id:"hunger_thirst",label:n("hungerThirst","Hunger/Thirst"),icon:"\ud83c\udf4e"}],h=[{id:"deep_breathing",label:n("deepBreathing","Deep Breathing"),icon:"\ud83e\udec1"},{id:"sensory_break",label:n("sensoryBreak","Sensory Break"),icon:"\u23f8\ufe0f"},{id:"visual_schedule",label:n("visualSchedule","Visual Schedule"),icon:"\ud83d\udcc5"},{id:"fidget_toy",label:n("fidgetToy","Fidget Toy"),icon:"\ud83e\uddf8"},{id:"quiet_space",label:n("quietSpace","Quiet Space"),icon:"\ud83e\udd2b"},{id:"preferred_activity",label:n("preferredActivity","Preferred Activity"),icon:"\ud83c\udfae"},{id:"social_story",label:n("socialStory","Social Story"),icon:"\ud83d\udcd6"},{id:"movement_break",label:n("movementBreak","Movement Break"),icon:"\ud83c\udfc3"}],u=[{value:1,label:n("veryLow","Very Low"),color:"bg-green-200",emoji:"\ud83d\ude0c"},{value:2,label:n("low","Low"),color:"bg-green-300",emoji:"\ud83d\ude42"},{value:3,label:n("moderate","Moderate"),color:"bg-yellow-300",emoji:"\ud83d\ude10"},{value:4,label:n("high","High"),color:"bg-orange-300",emoji:"\ud83d\ude1f"},{value:5,label:n("veryHigh","Very High"),color:"bg-red-300",emoji:"\ud83d\ude30"}],b=[{id:"log",label:n("logBehavior","Log Behavior"),icon:"\ud83d\udcdd"},{id:"patterns",label:n("patterns","Patterns"),icon:"\ud83d\udcca"},{id:"strategies",label:n("strategies","Strategies"),icon:"\ud83d\udca1"}];return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,i.jsx)("span",{className:"mr-3",children:"\ud83d\udcca"}),n("behavioralTracking","Behavioral Tracking")]}),(0,i.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:(new Date).toLocaleDateString()})]}),(0,i.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,i.jsx)("nav",{className:"-mb-px flex space-x-8",children:b.map(e=>(0,i.jsxs)("button",{onClick:()=>d(e.id),className:"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(o===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,i.jsx)("span",{children:e.icon}),(0,i.jsx)("span",{children:e.label})]},e.id))})}),"log"===o&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("selectBehaviorType","Select Behavior Type")}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:g.map(e=>(0,i.jsx)("button",{onClick:()=>{return s=e,void m(e=>(0,l.A)((0,l.A)({},e),{},{behavior:s.id}));var s},className:"p-4 rounded-lg border-2 transition-all ".concat(c.behavior===e.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"," ").concat(e.color),children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-2xl mb-2",children:e.icon}),(0,i.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.label})]})},e.id))})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("intensity","Intensity Level")}),(0,i.jsx)("div",{className:"space-y-2",children:u.map(e=>(0,i.jsxs)("button",{onClick:()=>m(s=>(0,l.A)((0,l.A)({},s),{},{intensity:e.value})),className:"w-full p-3 rounded-lg border-2 flex items-center space-x-3 transition-all ".concat(c.intensity===e.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"," ").concat(e.color),children:[(0,i.jsx)("span",{className:"text-xl",children:e.emoji}),(0,i.jsx)("span",{className:"font-medium text-gray-900",children:e.label})]},e.value))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("duration","Duration")}),(0,i.jsxs)("select",{value:c.duration,onChange:e=>m(s=>(0,l.A)((0,l.A)({},s),{},{duration:e.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:n("selectDuration","Select Duration")}),(0,i.jsx)("option",{value:"under_1min",children:n("under1Min","Under 1 minute")}),(0,i.jsx)("option",{value:"1_5min",children:n("1to5Min","1-5 minutes")}),(0,i.jsx)("option",{value:"5_15min",children:n("5to15Min","5-15 minutes")}),(0,i.jsx)("option",{value:"15_30min",children:n("15to30Min","15-30 minutes")}),(0,i.jsx)("option",{value:"over_30min",children:n("over30Min","Over 30 minutes")})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("identifiedTriggers","Identified Triggers")}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:x.map(e=>(0,i.jsx)("button",{onClick:()=>{return s=e.id,void m(e=>(0,l.A)((0,l.A)({},e),{},{triggers:e.triggers.includes(s)?e.triggers.filter(e=>e!==s):[...e.triggers,s]}));var s},className:"p-3 rounded-lg border-2 transition-all ".concat(c.triggers.includes(e.id)?"border-red-500 bg-red-50 dark:bg-red-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-lg mb-1",children:e.icon}),(0,i.jsx)("div",{className:"text-xs font-medium text-gray-900 dark:text-white",children:e.label})]})},e.id))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("interventionsUsed","Interventions Used")}),(0,i.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:h.map(e=>(0,i.jsx)("button",{onClick:()=>{return s=e.id,void m(e=>(0,l.A)((0,l.A)({},e),{},{interventions:e.interventions.includes(s)?e.interventions.filter(e=>e!==s):[...e.interventions,s]}));var s},className:"p-3 rounded-lg border-2 transition-all ".concat(c.interventions.includes(e.id)?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-lg mb-1",children:e.icon}),(0,i.jsx)("div",{className:"text-xs font-medium text-gray-900 dark:text-white",children:e.label})]})},e.id))})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("outcome","Outcome")}),(0,i.jsxs)("select",{value:c.outcome,onChange:e=>m(s=>(0,l.A)((0,l.A)({},s),{},{outcome:e.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:n("selectOutcome","Select Outcome")}),(0,i.jsx)("option",{value:"resolved_quickly",children:n("resolvedQuickly","Resolved Quickly")}),(0,i.jsx)("option",{value:"resolved_with_support",children:n("resolvedWithSupport","Resolved with Support")}),(0,i.jsx)("option",{value:"partially_resolved",children:n("partiallyResolved","Partially Resolved")}),(0,i.jsx)("option",{value:"escalated",children:n("escalated","Escalated")}),(0,i.jsx)("option",{value:"ongoing",children:n("ongoing","Ongoing")})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("additionalNotes","Additional Notes")}),(0,i.jsx)("textarea",{value:c.notes,onChange:e=>m(s=>(0,l.A)((0,l.A)({},s),{},{notes:e.target.value})),rows:"4",className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",placeholder:n("notesPlaceholder","Enter any additional observations, context, or details...")})]})]}),(0,i.jsx)("div",{className:"flex justify-end",children:(0,i.jsx)("button",{onClick:()=>{const e=(0,l.A)((0,l.A)({},c),{},{timestamp:new Date,patientId:s});null===r||void 0===r||r(e),m({behavior:"",intensity:3,duration:"",triggers:[],interventions:[],outcome:"",notes:""})},disabled:!c.behavior,className:"px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:n("saveBehaviorLog","Save Behavior Log")})})]}),"patterns"===o&&(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("div",{className:"text-6xl mb-4",children:"\ud83d\udcca"}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:n("behaviorPatterns","Behavior Patterns")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("patternsComingSoon","Pattern analysis and charts coming soon...")})]}),"strategies"===o&&(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)("div",{className:"text-6xl mb-4",children:"\ud83d\udca1"}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:n("behaviorStrategies","Behavior Strategies")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:n("strategiesComingSoon","Personalized behavior strategies coming soon...")})]})]})};var m=r(6813);const g=e=>{let{patientProfile:s,sessionActivities:r,onScheduleUpdate:n}=e;const{t:o}=(0,t.o)(),[d,c]=(0,a.useState)([]),[m,g]=(0,a.useState)(null),[x,h]=(0,a.useState)("session"),u={arrival:{id:"arrival",name:o("arrival","Arrival"),icon:"\ud83d\udeaa",color:"bg-blue-100",duration:5,description:o("arrivalDesc","Welcome and check-in")},warmup:{id:"warmup",name:o("warmup","Warm-up"),icon:"\ud83e\udd38",color:"bg-green-100",duration:10,description:o("warmupDesc","Gentle movement preparation")},exercise:{id:"exercise",name:o("exercise","Exercise"),icon:"\ud83c\udfcb\ufe0f",color:"bg-red-100",duration:20,description:o("exerciseDesc","Main therapy activities")},sensory_break:{id:"sensory_break",name:o("sensoryBreak","Sensory Break"),icon:"\ud83e\uddd8",color:"bg-purple-100",duration:5,description:o("sensoryBreakDesc","Calming sensory activity")},play_therapy:{id:"play_therapy",name:o("playTherapy","Play Therapy"),icon:"\ud83c\udfae",color:"bg-yellow-100",duration:15,description:o("playTherapyDesc","Therapeutic play activities")},communication:{id:"communication",name:o("communication","Communication"),icon:"\ud83d\udcac",color:"bg-pink-100",duration:10,description:o("communicationDesc","Communication practice")},snack_time:{id:"snack_time",name:o("snackTime","Snack Time"),icon:"\ud83c\udf4e",color:"bg-orange-100",duration:10,description:o("snackTimeDesc","Nutrition and social time")},cleanup:{id:"cleanup",name:o("cleanup","Clean-up"),icon:"\ud83e\uddf9",color:"bg-gray-100",duration:5,description:o("cleanupDesc","Organizing and tidying")},goodbye:{id:"goodbye",name:o("goodbye","Goodbye"),icon:"\ud83d\udc4b",color:"bg-indigo-100",duration:5,description:o("goodbyeDesc","Session wrap-up and farewell")}},b={autism_friendly:{name:o("autismFriendly","Autism Friendly"),description:o("autismScheduleDesc","Structured schedule with sensory breaks"),activities:["arrival","sensory_break","warmup","exercise","sensory_break","communication","cleanup","goodbye"]},motor_focused:{name:o("motorFocused","Motor Skills Focused"),description:o("motorScheduleDesc","Emphasis on physical development"),activities:["arrival","warmup","exercise","play_therapy","exercise","cleanup","goodbye"]},communication_focused:{name:o("communicationFocused","Communication Focused"),description:o("commScheduleDesc","Enhanced communication opportunities"),activities:["arrival","communication","warmup","play_therapy","communication","snack_time","goodbye"]},sensory_integration:{name:o("sensoryIntegration","Sensory Integration"),description:o("sensoryScheduleDesc","Balanced sensory experiences"),activities:["arrival","sensory_break","exercise","sensory_break","play_therapy","sensory_break","goodbye"]}},p=(e,s)=>{const r=d.findIndex(s=>s.id===e);if(-1===r)return;const a="up"===s?r-1:r+1;if(a<0||a>=d.length)return;const t=[...d];[t[r],t[a]]=[t[a],t[r]],c(t)},y=()=>{if(0===d.length)return"09:00";const e=d[d.length-1],s=e.startTime,[r,a]=s.split(":").map(Number),t=new Date;return t.setHours(r,a+e.duration),"".concat(t.getHours().toString().padStart(2,"0"),":").concat(t.getMinutes().toString().padStart(2,"0"))},v=()=>{if(0===d.length)return 0;const e=d.filter(e=>e.completed).length;return Math.round(e/d.length*100)};return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,i.jsx)("span",{className:"mr-3",children:"\ud83d\udcc5"}),o("visualSchedule","Visual Schedule")]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[o("totalTime","Total Time"),": ",d.reduce((e,s)=>e+s.duration,0)," ",o("minutes","minutes")]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[o("completed","Completed"),": ",v(),"%"]})]})]}),(0,i.jsx)("div",{className:"mb-6",children:(0,i.jsx)("div",{className:"flex space-x-4",children:["session","daily","weekly"].map(e=>(0,i.jsx)("button",{onClick:()=>h(e),className:"px-4 py-2 rounded-lg font-medium text-sm transition-colors ".concat(x===e?"bg-blue-600 text-white":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"),children:o(e,e)},e))})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)("div",{className:"lg:col-span-1",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("activityLibrary","Activity Library")}),(0,i.jsxs)("div",{className:"mb-6",children:[(0,i.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:o("templates","Templates")}),(0,i.jsx)("div",{className:"space-y-2",children:Object.entries(b).map(e=>{let[s,r]=e;return(0,i.jsxs)("button",{onClick:()=>(e=>{const s=b[e];if(s){const e=s.activities.map((e,s)=>{const r=u[e],a=new Date;return a.setHours(9,15*s),(0,l.A)((0,l.A)({},r),{},{id:"".concat(e,"_").concat(Date.now(),"_").concat(s),startTime:"".concat(a.getHours().toString().padStart(2,"0"),":").concat(a.getMinutes().toString().padStart(2,"0")),completed:!1,notes:""})});c(e)}})(s),className:"w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,i.jsx)("div",{className:"font-medium text-sm text-gray-900 dark:text-white",children:r.name}),(0,i.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1",children:r.description})]},s)})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:o("activities","Activities")}),(0,i.jsx)("div",{className:"space-y-2",children:Object.values(u).map(e=>(0,i.jsx)("button",{onClick:()=>(e=>{const s=u[e];if(s){const r=(0,l.A)((0,l.A)({},s),{},{id:"".concat(e,"_").concat(Date.now()),startTime:y(),completed:!1,notes:""});c(e=>[...e,r])}})(e.id),className:"w-full p-3 text-left border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ".concat(e.color),children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("span",{className:"text-2xl",children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.name}),(0,i.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:[e.duration," min"]})]})]})},e.id))})]})]}),(0,i.jsxs)("div",{className:"lg:col-span-2",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("currentSchedule","Current Schedule")}),0===d.length?(0,i.jsxs)("div",{className:"text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg",children:[(0,i.jsx)("div",{className:"text-6xl mb-4",children:"\ud83d\udcc5"}),(0,i.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:o("emptySchedule","No activities scheduled")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:o("addActivitiesPrompt","Add activities from the library or use a template to get started")})]}):(0,i.jsx)("div",{className:"space-y-3",children:d.map((e,s)=>(0,i.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg transition-all ".concat(e.completed?"bg-green-50 dark:bg-green-900/20 border-green-300":"bg-white dark:bg-gray-800"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,i.jsx)("button",{onClick:()=>p(e.id,"up"),disabled:0===s,className:"text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed",children:"\u25b2"}),(0,i.jsx)("button",{onClick:()=>p(e.id,"down"),disabled:s===d.length-1,className:"text-gray-400 hover:text-gray-600 disabled:opacity-30 disabled:cursor-not-allowed",children:"\u25bc"})]}),(0,i.jsx)("button",{onClick:()=>{return s=e.id,void c(e=>e.map(e=>e.id===s?(0,l.A)((0,l.A)({},e),{},{completed:!e.completed}):e));var s},className:"w-6 h-6 rounded-full border-2 flex items-center justify-center transition-colors ".concat(e.completed?"bg-green-500 border-green-500 text-white":"border-gray-300 hover:border-gray-400"),children:e.completed&&"\u2713"}),(0,i.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl ".concat(e.color),children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("h4",{className:"font-medium ".concat(e.completed?"line-through text-gray-500":"text-gray-900 dark:text-white"),children:e.name}),(0,i.jsxs)("span",{className:"text-sm text-gray-500",children:["(",e.duration," min)"]})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.startTime," - ",e.description]})]})]}),(0,i.jsx)("button",{onClick:()=>{return s=e.id,void c(e=>e.filter(e=>e.id!==s));var s},className:"text-red-500 hover:text-red-700 text-sm",children:"\u2715"})]}),(0,i.jsx)("div",{className:"mt-3",children:(0,i.jsx)("textarea",{value:e.notes,onChange:s=>{return r=e.id,a=s.target.value,void c(e=>e.map(e=>e.id===r?(0,l.A)((0,l.A)({},e),{},{notes:a}):e));var r,a},placeholder:o("addNotes","Add notes about this activity..."),className:"w-full p-2 text-sm border border-gray-200 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",rows:"2"})})]},e.id))}),d.length>0&&(0,i.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:o("sessionProgress","Session Progress")}),(0,i.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[d.filter(e=>e.completed).length," / ",d.length," ",o("completed","completed")]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3",children:(0,i.jsx)("div",{className:"bg-green-600 h-3 rounded-full transition-all duration-300",style:{width:"".concat(v(),"%")}})})]})]})]}),(0,i.jsxs)("div",{className:"mt-8 flex justify-end space-x-4",children:[(0,i.jsx)("button",{onClick:()=>c([]),className:"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:o("clearSchedule","Clear Schedule")}),(0,i.jsx)("button",{onClick:()=>null===n||void 0===n?void 0:n(d),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:o("saveSchedule","Save Schedule")})]})]})},x=e=>{let{patientData:s,goals:r,behaviorLogs:n}=e;const{t:o}=(0,t.o)(),[l,d]=(0,a.useState)("overview"),[c,m]=(0,a.useState)("week"),g={goals:[{id:"motor_skills",name:o("motorSkills","Motor Skills"),progress:75,target:100,color:"bg-blue-500"},{id:"communication",name:o("communication","Communication"),progress:60,target:100,color:"bg-green-500"},{id:"sensory_integration",name:o("sensoryIntegration","Sensory Integration"),progress:45,target:100,color:"bg-purple-500"},{id:"social_skills",name:o("socialSkills","Social Skills"),progress:30,target:100,color:"bg-yellow-500"}],milestones:[{id:1,name:o("firstSteps","First Independent Steps"),achieved:!0,date:"2024-01-15",icon:"\ud83d\udc76"},{id:2,name:o("verbalRequest","First Verbal Request"),achieved:!0,date:"2024-01-20",icon:"\ud83d\udcac"},{id:3,name:o("socialInteraction","Initiated Social Interaction"),achieved:!1,target:"2024-02-15",icon:"\ud83d\udc4b"},{id:4,name:o("independentTask","Completed Task Independently"),achieved:!1,target:"2024-02-28",icon:"\u2705"}],weeklyProgress:[{week:"Week 1",motor:20,communication:15,sensory:10,social:5},{week:"Week 2",motor:35,communication:25,sensory:20,social:10},{week:"Week 3",motor:50,communication:40,sensory:30,social:15},{week:"Week 4",motor:75,communication:60,sensory:45,social:30}],behaviorTrends:[{behavior:"Cooperation",trend:"improving",change:"+25%",color:"text-green-600"},{behavior:"Focus",trend:"stable",change:"0%",color:"text-blue-600"},{behavior:"Anxiety",trend:"decreasing",change:"-15%",color:"text-green-600"},{behavior:"Meltdowns",trend:"decreasing",change:"-40%",color:"text-green-600"}]},x=[{id:1,title:o("weekStreak","7-Day Streak"),description:o("weekStreakDesc","Attended all sessions this week"),icon:"\ud83d\udd25",earned:!0},{id:2,title:o("communicator","Great Communicator"),description:o("communicatorDesc","Used communication board 10 times"),icon:"\ud83d\udcac",earned:!0},{id:3,title:o("brave","Brave Explorer"),description:o("braveDesc","Tried 3 new activities"),icon:"\ud83c\udf1f",earned:!1},{id:4,title:o("helper","Super Helper"),description:o("helperDesc","Helped clean up 5 times"),icon:"\ud83e\uddb8",earned:!1}],h=[{id:"overview",label:o("overview","Overview"),icon:"\ud83d\udcca"},{id:"goals",label:o("goals","Goals"),icon:"\ud83c\udfaf"},{id:"milestones",label:o("milestones","Milestones"),icon:"\ud83c\udfc6"},{id:"behavior",label:o("behavior","Behavior"),icon:"\ud83d\udcc8"},{id:"achievements",label:o("achievements","Achievements"),icon:"\ud83c\udf1f"}],u=()=>(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:g.goals.map(e=>(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)("h3",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,i.jsxs)("span",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[e.progress,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-2",children:(0,i.jsx)("div",{className:"h-3 rounded-full transition-all duration-500 ".concat(e.color),style:{width:"".concat(e.progress,"%")}})}),(0,i.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[o("target","Target"),": ",e.target,"%"]})]},e.id))}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("recentAchievements","Recent Achievements")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:x.filter(e=>e.earned).map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700",children:[(0,i.jsx)("span",{className:"text-2xl",children:e.icon}),(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,i.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.description})]})]},e.id))})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("behaviorTrends","Behavior Trends")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:g.behaviorTrends.map((e,s)=>(0,i.jsxs)("div",{className:"text-center p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,i.jsx)("div",{className:"font-medium text-gray-900 dark:text-white mb-2",children:e.behavior}),(0,i.jsx)("div",{className:"text-2xl font-bold ".concat(e.color," mb-1"),children:e.change}),(0,i.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 capitalize",children:e.trend})]},s))})]})]});return(0,i.jsx)("div",{className:"bg-gray-50 dark:bg-gray-900 min-h-screen p-6",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,i.jsx)("span",{className:"mr-3",children:"\ud83d\udcca"}),o("progressVisualization","Progress Visualization")]}),(0,i.jsx)("div",{className:"flex items-center space-x-4",children:(0,i.jsxs)("select",{value:c,onChange:e=>m(e.target.value),className:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"week",children:o("thisWeek","This Week")}),(0,i.jsx)("option",{value:"month",children:o("thisMonth","This Month")}),(0,i.jsx)("option",{value:"quarter",children:o("thisQuarter","This Quarter")})]})})]}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)("nav",{className:"flex space-x-4 overflow-x-auto pb-2",children:h.map(e=>(0,i.jsxs)("button",{onClick:()=>d(e.id),className:"flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ".concat(l===e.id?"bg-blue-600 text-white":"bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-600"),children:[(0,i.jsx)("span",{children:e.icon}),(0,i.jsx)("span",{className:"font-medium",children:e.label})]},e.id))})}),(()=>{switch(l){case"overview":default:return u();case"goals":return(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:o("goalProgress","Goal Progress Over Time")}),(0,i.jsx)("div",{className:"space-y-6",children:g.weeklyProgress.map((e,s)=>(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.week}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:o("motor","Motor")}),(0,i.jsxs)("span",{className:"font-medium",children:[e.motor,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(e.motor,"%")}})})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:o("communication","Communication")}),(0,i.jsxs)("span",{className:"font-medium",children:[e.communication,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(e.communication,"%")}})})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:o("sensory","Sensory")}),(0,i.jsxs)("span",{className:"font-medium",children:[e.sensory,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-purple-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(e.sensory,"%")}})})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,i.jsx)("span",{className:"text-gray-600 dark:text-gray-400",children:o("social","Social")}),(0,i.jsxs)("span",{className:"font-medium",children:[e.social,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full transition-all duration-500",style:{width:"".concat(e.social,"%")}})})]})]})]},s))})]})});case"milestones":return(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:o("milestoneTimeline","Milestone Timeline")}),(0,i.jsx)("div",{className:"space-y-4",children:g.milestones.map((e,s)=>(0,i.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,i.jsx)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center text-2xl ".concat(e.achieved?"bg-green-100 border-2 border-green-500":"bg-gray-100 border-2 border-gray-300"),children:e.achieved?"\u2705":e.icon}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h4",{className:"font-medium ".concat(e.achieved?"text-green-700 dark:text-green-400":"text-gray-900 dark:text-white"),children:e.name}),(0,i.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.achieved?e.date:"".concat(o("target","Target"),": ").concat(e.target)})]}),e.achieved&&(0,i.jsxs)("div",{className:"text-sm text-green-600 dark:text-green-400 mt-1",children:[o("achieved","Achieved")," \u2728"]})]})]},e.id))})]})});case"behavior":return(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:o("behaviorAnalysis","Behavior Analysis")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-green-700 dark:text-green-400 mb-3 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\u2705"}),o("positiveBehaviors","Positive Behaviors")]}),(0,i.jsx)("div",{className:"space-y-3",children:["Cooperation","Communication","Focus"].map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",children:[(0,i.jsx)("span",{className:"text-gray-900 dark:text-white",children:o(e.toLowerCase(),e)}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"".concat(75+10*s,"%")}})}),(0,i.jsxs)("span",{className:"text-sm font-medium text-green-600",children:[75+10*s,"%"]})]})]},s))})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("h4",{className:"font-medium text-orange-700 dark:text-orange-400 mb-3 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\u26a0\ufe0f"}),o("challengingBehaviors","Challenging Behaviors")]}),(0,i.jsx)("div",{className:"space-y-3",children:["Meltdowns","Anxiety","Withdrawal"].map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg",children:[(0,i.jsx)("span",{className:"text-gray-900 dark:text-white",children:o(e.toLowerCase(),e)}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-orange-500 h-2 rounded-full",style:{width:"".concat(30-5*s,"%")}})}),(0,i.jsxs)("span",{className:"text-sm font-medium text-orange-600",children:[30-5*s,"%"]})]})]},s))})]})]})]})});case"achievements":return(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 p-6 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-6",children:o("achievementBadges","Achievement Badges")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:x.map(e=>(0,i.jsxs)("div",{className:"p-6 rounded-lg border-2 text-center transition-all ".concat(e.earned?"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600":"bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 opacity-60"),children:[(0,i.jsx)("div",{className:"text-4xl mb-3",children:e.icon}),(0,i.jsx)("h4",{className:"font-medium mb-2 ".concat(e.earned?"text-yellow-800 dark:text-yellow-200":"text-gray-600 dark:text-gray-400"),children:e.title}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.description}),e.earned&&(0,i.jsxs)("div",{className:"mt-3 text-xs font-medium text-yellow-600 dark:text-yellow-400",children:[o("earned","Earned")," \u2728"]})]},e.id))})]})})}})()]})})},h=e=>{let{patientProfile:s,onEmergencyLog:r}=e;const{t:n}=(0,t.o)(),[o,d]=(0,a.useState)(null),[c,m]=(0,a.useState)({type:"",severity:"low",triggers:[],interventions:[],outcome:"",duration:"",notes:""}),g={behavioral:{id:"behavioral",name:n("behavioralEmergency","Behavioral Emergency"),icon:"\ud83d\udea8",color:"bg-red-100 border-red-300",protocols:[{id:"meltdown",name:n("meltdown","Meltdown"),severity:"high",steps:[n("meltdownStep1","Ensure safety of patient and others"),n("meltdownStep2","Remove or reduce triggers"),n("meltdownStep3","Provide calm, quiet space"),n("meltdownStep4","Use calming techniques"),n("meltdownStep5","Monitor and document")]},{id:"aggression",name:n("aggression","Aggressive Behavior"),severity:"high",steps:[n("aggressionStep1","Prioritize safety"),n("aggressionStep2","Maintain calm demeanor"),n("aggressionStep3","Use de-escalation techniques"),n("aggressionStep4","Implement behavior plan"),n("aggressionStep5","Contact emergency contacts if needed")]},{id:"self_harm",name:n("selfHarm","Self-Injurious Behavior"),severity:"critical",steps:[n("selfHarmStep1","Immediate intervention to prevent injury"),n("selfHarmStep2","Redirect to safe alternatives"),n("selfHarmStep3","Assess for injuries"),n("selfHarmStep4","Implement safety protocols"),n("selfHarmStep5","Contact medical personnel if needed")]}]},medical:{id:"medical",name:n("medicalEmergency","Medical Emergency"),icon:"\ud83c\udfe5",color:"bg-blue-100 border-blue-300",protocols:[{id:"seizure",name:n("seizure","Seizure"),severity:"critical",steps:[n("seizureStep1","Keep patient safe, do not restrain"),n("seizureStep2","Clear area of dangerous objects"),n("seizureStep3","Time the seizure"),n("seizureStep4","Place in recovery position after seizure"),n("seizureStep5","Call emergency services if prolonged")]},{id:"breathing",name:n("breathingDifficulty","Breathing Difficulty"),severity:"critical",steps:[n("breathingStep1","Assess airway and breathing"),n("breathingStep2","Position for optimal breathing"),n("breathingStep3","Administer rescue medication if prescribed"),n("breathingStep4","Call emergency services"),n("breathingStep5","Monitor vital signs")]},{id:"injury",name:n("injury","Physical Injury"),severity:"medium",steps:[n("injuryStep1","Assess severity of injury"),n("injuryStep2","Provide first aid"),n("injuryStep3","Control bleeding if present"),n("injuryStep4","Contact parents/guardians"),n("injuryStep5","Seek medical attention if needed")]}]},sensory:{id:"sensory",name:n("sensoryOverload","Sensory Overload"),icon:"\ud83d\udc41\ufe0f",color:"bg-purple-100 border-purple-300",protocols:[{id:"overload",name:n("sensoryOverload","Sensory Overload"),severity:"medium",steps:[n("overloadStep1","Identify and remove sensory triggers"),n("overloadStep2","Move to quiet, low-stimulation area"),n("overloadStep3","Offer sensory tools (weighted blanket, fidgets)"),n("overloadStep4","Use calming techniques"),n("overloadStep5","Allow recovery time")]},{id:"shutdown",name:n("sensoryShutdown","Sensory Shutdown"),severity:"medium",steps:[n("shutdownStep1","Recognize shutdown signs"),n("shutdownStep2","Provide safe, quiet space"),n("shutdownStep3","Minimize demands and expectations"),n("shutdownStep4","Offer comfort items"),n("shutdownStep5","Allow time for recovery")]}]}},x={low:{color:"bg-green-100 text-green-800",label:n("low","Low")},medium:{color:"bg-yellow-100 text-yellow-800",label:n("medium","Medium")},high:{color:"bg-orange-100 text-orange-800",label:n("high","High")},critical:{color:"bg-red-100 text-red-800",label:n("critical","Critical")}},h=[{id:"parent1",name:n("primaryParent","Primary Parent"),phone:"+966 50 123 4567",role:n("mother","Mother")},{id:"parent2",name:n("secondaryParent","Secondary Parent"),phone:"+966 50 765 4321",role:n("father","Father")},{id:"emergency",name:n("emergencyServices","Emergency Services"),phone:"997",role:n("medical","Medical")},{id:"doctor",name:n("primaryDoctor","Primary Doctor"),phone:"+966 11 234 5678",role:n("physician","Physician")}],u=()=>{d(null),m({type:"",severity:"low",triggers:[],interventions:[],outcome:"",duration:"",notes:""})},b=e=>{alert("".concat(n("calling","Calling")," ").concat(e.name,": ").concat(e.phone))};return(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,i.jsx)("span",{className:"mr-3",children:"\ud83d\udea8"}),n("emergencyProtocols","Emergency Protocols")]}),o&&(0,i.jsx)("button",{onClick:u,className:"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:n("deactivate","Deactivate")})]}),o?(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border-2 border-red-300 dark:border-red-600 rounded-lg p-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsxs)("h3",{className:"text-xl font-bold text-red-800 dark:text-red-200 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2",children:"\ud83d\udea8"}),n("activeProtocol","ACTIVE PROTOCOL"),": ",o.name]}),(0,i.jsxs)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat(x[o.severity].color),children:[x[o.severity].label," ",n("severity","Severity")]})]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-red-700 dark:text-red-300",children:n("followSteps","Follow these steps:")}),(0,i.jsx)("ol",{className:"space-y-3",children:o.steps.map((e,s)=>(0,i.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,i.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold",children:s+1}),(0,i.jsx)("span",{className:"text-red-800 dark:text-red-200",children:e})]},s))})]})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6",children:[(0,i.jsx)("h4",{className:"font-semibold text-gray-900 dark:text-white mb-4",children:n("logEmergency","Log Emergency Details")}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("duration","Duration")}),(0,i.jsxs)("select",{value:c.duration,onChange:e=>m(s=>(0,l.A)((0,l.A)({},s),{},{duration:e.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:n("selectDuration","Select Duration")}),(0,i.jsx)("option",{value:"under_5min",children:n("under5Min","Under 5 minutes")}),(0,i.jsx)("option",{value:"5_15min",children:n("5to15Min","5-15 minutes")}),(0,i.jsx)("option",{value:"15_30min",children:n("15to30Min","15-30 minutes")}),(0,i.jsx)("option",{value:"over_30min",children:n("over30Min","Over 30 minutes")})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("outcome","Outcome")}),(0,i.jsxs)("select",{value:c.outcome,onChange:e=>m(s=>(0,l.A)((0,l.A)({},s),{},{outcome:e.target.value})),className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",children:[(0,i.jsx)("option",{value:"",children:n("selectOutcome","Select Outcome")}),(0,i.jsx)("option",{value:"resolved",children:n("resolved","Resolved")}),(0,i.jsx)("option",{value:"partially_resolved",children:n("partiallyResolved","Partially Resolved")}),(0,i.jsx)("option",{value:"ongoing",children:n("ongoing","Ongoing")}),(0,i.jsx)("option",{value:"escalated",children:n("escalated","Escalated to Medical")})]})]})]}),(0,i.jsxs)("div",{className:"mt-4",children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:n("emergencyNotes","Emergency Notes")}),(0,i.jsx)("textarea",{value:c.notes,onChange:e=>m(s=>(0,l.A)((0,l.A)({},s),{},{notes:e.target.value})),rows:"4",className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white",placeholder:n("emergencyNotesPlaceholder","Describe what happened, interventions used, and any other relevant details...")})]}),(0,i.jsxs)("div",{className:"mt-6 flex justify-end space-x-4",children:[(0,i.jsx)("button",{onClick:u,className:"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:n("cancel","Cancel")}),(0,i.jsx)("button",{onClick:()=>{const e=(0,l.A)((0,l.A)({},c),{},{timestamp:new Date,patientId:null===s||void 0===s?void 0:s.id,protocolUsed:null===o||void 0===o?void 0:o.id});null===r||void 0===r||r(e),u()},className:"px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",children:n("logAndClose","Log & Close Emergency")})]})]}),(0,i.jsxs)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-600 rounded-lg p-4",children:[(0,i.jsx)("h4",{className:"font-semibold text-yellow-800 dark:text-yellow-200 mb-3",children:n("quickActions","Quick Actions")}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2",children:[h.slice(0,2).map(e=>(0,i.jsxs)("button",{onClick:()=>b(e),className:"px-3 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 text-sm",children:["\ud83d\udcde ",e.name]},e.id)),(0,i.jsxs)("button",{onClick:()=>b(h[2]),className:"px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 text-sm",children:["\ud83d\ude91 ",n("callEmergency","Call 997")]})]})]})]}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("selectEmergencyType","Select Emergency Type")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:Object.values(g).map(e=>(0,i.jsxs)("div",{className:"p-4 border-2 border-dashed rounded-lg ".concat(e.color),children:[(0,i.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,i.jsx)("span",{className:"mr-2 text-2xl",children:e.icon}),e.name]}),(0,i.jsx)("div",{className:"space-y-2",children:e.protocols.map(e=>(0,i.jsx)("button",{onClick:()=>(e=>{d(e),m(s=>(0,l.A)((0,l.A)({},s),{},{type:e.id,severity:e.severity}))})(e),className:"w-full p-3 text-left bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{className:"font-medium text-gray-900",children:e.name}),(0,i.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(x[e.severity].color),children:x[e.severity].label})]})},e.id))})]},e.id))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:n("emergencyContacts","Emergency Contacts")}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:h.map(e=>(0,i.jsx)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.role}),(0,i.jsx)("p",{className:"text-sm font-mono text-gray-700 dark:text-gray-300",children:e.phone})]}),(0,i.jsxs)("button",{onClick:()=>b(e),className:"px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",children:["\ud83d\udcde ",n("call","Call")]})]})},e.id))})]})]})]})},u=()=>{const{t:e}=(0,t.o)(),[s,r]=(0,a.useState)("overview"),[l,u]=(0,a.useState)({id:"patient_001",name:"Ahmed Al-Rashid",age:8,primaryDiagnosis:"autism",communicationLevel:"non_verbal",sensoryProfile:"sensory_seeking"}),b=[{id:"overview",title:e("overview","Overview"),icon:"\ud83d\udccb",description:e("overviewDesc","Patient overview and quick stats")},{id:"profile",title:e("patientProfile","Patient Profile"),icon:"\ud83d\udc64",description:e("profileDesc","Detailed special needs profile")},{id:"communication",title:e("communication","Communication"),icon:"\ud83d\udcac",description:e("communicationDesc","Visual communication tools")},{id:"sensory",title:e("sensoryEnvironment","Sensory Environment"),icon:"\ud83c\udf9b\ufe0f",description:e("sensoryDesc","Environmental controls and settings")},{id:"behavior",title:e("behaviorTracking","Behavior Tracking"),icon:"\ud83d\udcca",description:e("behaviorDesc","Track and analyze behaviors")},{id:"treatment",title:e("treatmentPlan","Treatment Plan"),icon:"\ud83c\udfaf",description:e("treatmentDesc","Adaptive treatment planning")},{id:"schedule",title:e("visualSchedule","Visual Schedule"),icon:"\ud83d\udcc5",description:e("scheduleDesc","Visual session planning and tracking")},{id:"progress",title:e("progressTracking","Progress Tracking"),icon:"\ud83d\udcca",description:e("progressDesc","Visual progress and achievements")},{id:"emergency",title:e("emergencyProtocols","Emergency Protocols"),icon:"\ud83d\udea8",description:e("emergencyDesc","Crisis management and safety protocols")}],p=[{title:e("totalSessions","Total Sessions"),value:"24",change:"+3",changeType:"positive",icon:"\ud83d\udcc5"},{title:e("behaviorGoals","Behavior Goals Met"),value:"8/12",change:"+2",changeType:"positive",icon:"\ud83c\udfaf"},{title:e("communicationProgress","Communication Progress"),value:"75%",change:"+15%",changeType:"positive",icon:"\ud83d\udcac"},{title:e("sensoryTolerance","Sensory Tolerance"),value:"Improving",change:"Stable",changeType:"neutral",icon:"\ud83d\udc41\ufe0f"}],y=[{id:1,type:"behavior",title:e("behaviorLogAdded","Behavior log added"),description:e("cooperationBehavior","Cooperation behavior - High intensity"),time:"10 minutes ago",icon:"\ud83d\udcca",color:"text-green-600"},{id:2,type:"communication",title:e("communicationSuccess","Communication success"),description:e("usedPictureCards","Successfully used picture cards to request break"),time:"1 hour ago",icon:"\ud83d\udcac",color:"text-blue-600"},{id:3,type:"sensory",title:e("sensoryAdjustment","Sensory adjustment"),description:e("lightingReduced","Lighting reduced to calm setting"),time:"2 hours ago",icon:"\ud83c\udf9b\ufe0f",color:"text-purple-600"}],v=[{id:1,title:e("increaseVerbalRequests","Increase verbal requests"),progress:60,target:e("5VerbalRequestsPerSession","5 verbal requests per session"),dueDate:e("thisWeek","This week")},{id:2,title:e("tolerateLoudSounds","Tolerate loud sounds"),progress:30,target:e("2MinutesTolerance","2 minutes tolerance"),dueDate:e("nextWeek","Next week")},{id:3,title:e("followVisualSchedule","Follow visual schedule"),progress:85,target:e("independentTransitions","Independent transitions"),dueDate:e("thisMonth","This month")}],j=()=>(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold mb-2",children:l.name}),(0,i.jsxs)("div",{className:"flex items-center space-x-4 text-blue-100",children:[(0,i.jsxs)("span",{children:["\ud83d\udc76 ",e("age","Age"),": ",l.age]}),(0,i.jsxs)("span",{children:["\ud83e\udde9 ",e("diagnosis","Diagnosis"),": ",e(l.primaryDiagnosis,l.primaryDiagnosis)]}),(0,i.jsxs)("span",{children:["\ud83d\udcac ",e("communication","Communication"),": ",e(l.communicationLevel,l.communicationLevel)]})]})]}),(0,i.jsx)("div",{className:"text-6xl opacity-20",children:"\ud83e\udde9"})]})}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:p.map((e,s)=>(0,i.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e.title}),(0,i.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.value}),(0,i.jsx)("p",{className:"text-sm ".concat("positive"===e.changeType?"text-green-600":"negative"===e.changeType?"text-red-600":"text-gray-600"),children:e.change})]}),(0,i.jsx)("div",{className:"text-3xl",children:e.icon})]})},s))}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("recentActivities","Recent Activities")}),(0,i.jsx)("div",{className:"space-y-4",children:y.map(e=>(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"text-xl ".concat(e.color),children:e.icon}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,i.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.description}),(0,i.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-500",children:e.time})]})]},e.id))})]}),(0,i.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:e("upcomingGoals","Upcoming Goals")}),(0,i.jsx)("div",{className:"space-y-4",children:v.map(e=>(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,i.jsxs)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:[e.progress,"%"]})]}),(0,i.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,i.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})}),(0,i.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400",children:[(0,i.jsx)("span",{children:e.target}),(0,i.jsx)("span",{children:e.dueDate})]})]},e.id))})]})]})]});return(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:e("specialNeedsTherapy","Special Needs Physical Therapy")}),(0,i.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:e("specialNeedsDesc","Comprehensive care for patients with special needs")})]}),(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsx)("nav",{className:"flex space-x-4 overflow-x-auto pb-2",children:b.map(e=>(0,i.jsxs)("button",{onClick:()=>r(e.id),className:"flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-colors ".concat(s===e.id?"bg-blue-600 text-white":"bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,i.jsx)("span",{children:e.icon}),(0,i.jsx)("span",{className:"font-medium",children:e.title})]},e.id))})}),(0,i.jsx)("div",{className:"mb-8",children:(()=>{switch(s){case"overview":default:return j();case"profile":return(0,i.jsx)(n,{patient:l,onUpdate:u});case"communication":return(0,i.jsx)(o,{onMessageSelect:e=>console.log("Message:",e)});case"sensory":return(0,i.jsx)(d,{patientProfile:l,onEnvironmentChange:e=>console.log("Environment:",e)});case"behavior":return(0,i.jsx)(c,{patientId:l.id,onBehaviorLog:e=>console.log("Behavior log:",e)});case"treatment":return(0,i.jsx)(m.default,{patientProfile:l,onPlanUpdate:e=>console.log("Treatment plan:",e)});case"schedule":return(0,i.jsx)(g,{patientProfile:l,sessionActivities:[],onScheduleUpdate:e=>console.log("Schedule:",e)});case"progress":return(0,i.jsx)(x,{patientData:l,goals:[],behaviorLogs:[]});case"emergency":return(0,i.jsx)(h,{patientProfile:l,onEmergencyLog:e=>console.log("Emergency log:",e)})}})()})]})})}},6813:(e,s,r)=>{r.r(s),r.d(s,{default:()=>o});var a=r(2555),t=r(5043),i=r(7921),n=r(579);const o=e=>{let{patientProfile:s,onPlanUpdate:r}=e;const{t:o}=(0,i.o)(),[l,d]=(0,t.useState)("goals"),[c,m]=(0,t.useState)({goals:[],activities:[],accommodations:[],schedule:{},progressMetrics:[]}),g={motor:{label:o("motorSkills","Motor Skills"),icon:"\ud83c\udfc3",color:"bg-blue-100 border-blue-300",goals:[{id:"gross_motor",label:o("grossMotor","Gross Motor Development"),description:o("grossMotorDesc","Large muscle movements and coordination")},{id:"fine_motor",label:o("fineMotor","Fine Motor Development"),description:o("fineMotorDesc","Small muscle control and dexterity")},{id:"balance",label:o("balance","Balance and Stability"),description:o("balanceDesc","Postural control and equilibrium")},{id:"coordination",label:o("coordination","Coordination"),description:o("coordinationDesc","Movement planning and execution")}]},sensory:{label:o("sensoryIntegration","Sensory Integration"),icon:"\ud83d\udc41\ufe0f",color:"bg-purple-100 border-purple-300",goals:[{id:"tactile_processing",label:o("tactileProcessing","Tactile Processing"),description:o("tactileDesc","Touch sensitivity and discrimination")},{id:"vestibular_processing",label:o("vestibularProcessing","Vestibular Processing"),description:o("vestibularDesc","Movement and balance processing")},{id:"proprioceptive",label:o("proprioceptive","Proprioceptive Awareness"),description:o("proprioceptiveDesc","Body position awareness")},{id:"sensory_modulation",label:o("sensoryModulation","Sensory Modulation"),description:o("sensoryModulationDesc","Regulating sensory input")}]},communication:{label:o("communication","Communication"),icon:"\ud83d\udcac",color:"bg-green-100 border-green-300",goals:[{id:"verbal_expression",label:o("verbalExpression","Verbal Expression"),description:o("verbalExpressionDesc","Speaking and articulation")},{id:"nonverbal_communication",label:o("nonverbalCommunication","Non-verbal Communication"),description:o("nonverbalDesc","Gestures and body language")},{id:"aac_usage",label:o("aacUsage","AAC Device Usage"),description:o("aacUsageDesc","Alternative communication methods")},{id:"social_communication",label:o("socialCommunication","Social Communication"),description:o("socialCommDesc","Interactive communication skills")}]},behavioral:{label:o("behavioral","Behavioral"),icon:"\ud83c\udfaf",color:"bg-yellow-100 border-yellow-300",goals:[{id:"self_regulation",label:o("selfRegulation","Self-Regulation"),description:o("selfRegulationDesc","Managing emotions and behaviors")},{id:"attention_focus",label:o("attentionFocus","Attention and Focus"),description:o("attentionDesc","Sustained attention skills")},{id:"social_skills",label:o("socialSkills","Social Skills"),description:o("socialSkillsDesc","Interaction with others")},{id:"independence",label:o("independence","Independence"),description:o("independenceDesc","Self-care and autonomy")}]}},x={autism:[{id:"structured_play",name:o("structuredPlay","Structured Play"),duration:15,sensoryLevel:"low",description:o("structuredPlayDesc","Predictable play activities with clear rules")},{id:"sensory_breaks",name:o("sensoryBreaks","Sensory Breaks"),duration:5,sensoryLevel:"calming",description:o("sensoryBreaksDesc","Calming activities to regulate sensory input")},{id:"visual_schedules",name:o("visualSchedules","Visual Schedule Review"),duration:10,sensoryLevel:"low",description:o("visualSchedulesDesc","Review upcoming activities with pictures")},{id:"social_stories",name:o("socialStories","Social Stories"),duration:10,sensoryLevel:"low",description:o("socialStoriesDesc","Stories about social situations and expectations")}],cerebral_palsy:[{id:"range_of_motion",name:o("rangeOfMotion","Range of Motion"),duration:20,sensoryLevel:"moderate",description:o("romDesc","Gentle stretching and joint mobility")},{id:"strength_training",name:o("strengthTraining","Strength Training"),duration:15,sensoryLevel:"moderate",description:o("strengthDesc","Muscle strengthening exercises")},{id:"gait_training",name:o("gaitTraining","Gait Training"),duration:20,sensoryLevel:"high",description:o("gaitDesc","Walking and mobility practice")},{id:"positioning",name:o("positioning","Positioning"),duration:10,sensoryLevel:"low",description:o("positioningDesc","Proper body positioning and support")}],down_syndrome:[{id:"motor_planning",name:o("motorPlanning","Motor Planning"),duration:15,sensoryLevel:"moderate",description:o("motorPlanningDesc","Planning and executing movements")},{id:"balance_activities",name:o("balanceActivities","Balance Activities"),duration:15,sensoryLevel:"moderate",description:o("balanceActivitiesDesc","Static and dynamic balance training")},{id:"cognitive_motor",name:o("cognitiveMotor","Cognitive-Motor Tasks"),duration:20,sensoryLevel:"moderate",description:o("cognitiveMotorDesc","Thinking while moving activities")},{id:"functional_skills",name:o("functionalSkills","Functional Skills"),duration:25,sensoryLevel:"low",description:o("functionalSkillsDesc","Daily living movement skills")}]},h=[{id:"extra_time",label:o("extraTime","Extra Time"),icon:"\u23f0",description:o("extraTimeDesc","Additional time for task completion")},{id:"visual_cues",label:o("visualCues","Visual Cues"),icon:"\ud83d\udc41\ufe0f",description:o("visualCuesDesc","Picture prompts and visual supports")},{id:"sensory_tools",label:o("sensoryTools","Sensory Tools"),icon:"\ud83e\uddf8",description:o("sensoryToolsDesc","Fidgets, weighted items, sensory aids")},{id:"quiet_space",label:o("quietSpace","Quiet Space"),icon:"\ud83e\udd2b",description:o("quietSpaceDesc","Low-stimulation environment option")},{id:"movement_breaks",label:o("movementBreaks","Movement Breaks"),icon:"\ud83c\udfc3",description:o("movementBreaksDesc","Regular movement and stretch breaks")},{id:"simplified_instructions",label:o("simplifiedInstructions","Simplified Instructions"),icon:"\ud83d\udcdd",description:o("simplifiedDesc","Clear, step-by-step directions")},{id:"peer_support",label:o("peerSupport","Peer Support"),icon:"\ud83d\udc65",description:o("peerSupportDesc","Buddy system or group activities")},{id:"assistive_technology",label:o("assistiveTechnology","Assistive Technology"),icon:"\ud83d\udcf1",description:o("assistiveTechDesc","Communication devices and mobility aids")}];(0,t.useEffect)(()=>{s&&u(s)},[s]);const u=e=>{const s=e.primaryDiagnosis,r=x[s]||[],t=b(s),i=p(e);m(e=>(0,a.A)((0,a.A)({},e),{},{activities:r,goals:t,accommodations:i}))},b=e=>({autism:["sensory_modulation","social_communication","self_regulation","fine_motor"],cerebral_palsy:["gross_motor","balance","coordination","independence"],down_syndrome:["motor_planning","balance","cognitive_motor","social_skills"],intellectual_disability:["functional_skills","independence","social_skills","communication"]}[e]||[]),p=e=>{const s=[];return"sensory_seeking"===e.sensoryProfile&&s.push("sensory_tools","movement_breaks"),"sensory_avoiding"===e.sensoryProfile&&s.push("quiet_space","visual_cues"),"non_verbal"===e.communicationLevel&&s.push("visual_cues","assistive_technology"),s.push("extra_time","simplified_instructions"),s},y=[{id:"goals",label:o("treatmentGoals","Treatment Goals"),icon:"\ud83c\udfaf"},{id:"activities",label:o("activities","Activities"),icon:"\ud83c\udfae"},{id:"accommodations",label:o("accommodations","Accommodations"),icon:"\ud83d\udee0\ufe0f"},{id:"schedule",label:o("schedule","Schedule"),icon:"\ud83d\udcc5"},{id:"progress",label:o("progress","Progress"),icon:"\ud83d\udcca"}];return(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsxs)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white flex items-center",children:[(0,n.jsx)("span",{className:"mr-3",children:"\ud83c\udfaf"}),o("adaptiveTreatmentPlan","Adaptive Treatment Plan")]}),(0,n.jsx)("div",{className:"flex items-center space-x-2",children:(0,n.jsx)("span",{className:"px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full text-sm font-medium",children:null!==s&&void 0!==s&&s.primaryDiagnosis?o(s.primaryDiagnosis):o("general","General")})})]}),(0,n.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-600 mb-6",children:(0,n.jsx)("nav",{className:"-mb-px flex space-x-8 overflow-x-auto",children:y.map(e=>(0,n.jsxs)("button",{onClick:()=>d(e.id),className:"whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ".concat(l===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300"),children:[(0,n.jsx)("span",{children:e.icon}),(0,n.jsx)("span",{children:e.label})]},e.id))})}),(0,n.jsxs)("div",{className:"space-y-6",children:["goals"===l&&(0,n.jsxs)("div",{className:"space-y-6",children:[c.goals.length>0&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("currentGoals","Current Goals")}),(0,n.jsx)("div",{className:"space-y-4",children:c.goals.map(e=>(0,n.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.label}),(0,n.jsx)("button",{onClick:()=>{return s=e.id,void m(e=>(0,a.A)((0,a.A)({},e),{},{goals:e.goals.filter(e=>e.id!==s)}));var s},className:"text-red-500 hover:text-red-700 text-sm",children:"\u2715"})]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:e.description}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex-1 mr-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-1",children:[(0,n.jsx)("span",{children:o("progress","Progress")}),(0,n.jsxs)("span",{children:[e.progress,"%"]})]}),(0,n.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,n.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.progress,"%")}})})]}),(0,n.jsx)("input",{type:"range",min:"0",max:"100",value:e.progress,onChange:s=>{return r=e.id,t=parseInt(s.target.value),void m(e=>(0,a.A)((0,a.A)({},e),{},{goals:e.goals.map(e=>e.id===r?(0,a.A)((0,a.A)({},e),{},{progress:t}):e)}));var r,t},className:"w-20"})]})]},e.id))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("addNewGoals","Add New Goals")}),(0,n.jsx)("div",{className:"space-y-6",children:Object.entries(g).map(e=>{let[s,r]=e;return(0,n.jsxs)("div",{className:"p-4 border-2 border-dashed rounded-lg ".concat(r.color),children:[(0,n.jsxs)("h4",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,n.jsx)("span",{className:"mr-2",children:r.icon}),r.label]}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:r.goals.map(e=>(0,n.jsxs)("button",{onClick:()=>((e,s)=>{const r=g[e].goals.find(e=>e.id===s);if(r&&!c.goals.find(e=>e.id===s)){const s=(0,a.A)((0,a.A)({},r),{},{categoryId:e,targetDate:new Date(Date.now()+2592e6),priority:"medium",progress:0,milestones:[]});m(e=>(0,a.A)((0,a.A)({},e),{},{goals:[...e.goals,s]}))}})(s,e.id),disabled:c.goals.find(s=>s.id===e.id),className:"p-3 text-left rounded-lg border transition-colors ".concat(c.goals.find(s=>s.id===e.id)?"bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed":"bg-white border-gray-200 hover:border-gray-300 hover:bg-gray-50"),children:[(0,n.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.label}),(0,n.jsx)("div",{className:"text-xs text-gray-600 mt-1",children:e.description})]},e.id))})]},s)})})]})]}),"activities"===l&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("recommendedActivities","Recommended Activities")}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:c.activities.map(e=>(0,n.jsxs)("div",{className:"p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,n.jsxs)("span",{className:"text-sm text-gray-500",children:[e.duration," min"]})]}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3",children:e.description}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat("low"===e.sensoryLevel?"bg-green-100 text-green-800":"moderate"===e.sensoryLevel?"bg-yellow-100 text-yellow-800":"high"===e.sensoryLevel?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"),children:[o(e.sensoryLevel,e.sensoryLevel)," ",o("sensory","sensory")]}),(0,n.jsx)("button",{className:"text-blue-600 hover:text-blue-800 text-sm font-medium",children:o("addToSession","Add to Session")})]})]},e.id))})]}),"accommodations"===l&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:o("recommendedAccommodations","Recommended Accommodations")}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:h.map(e=>(0,n.jsx)("div",{className:"p-4 border-2 rounded-lg cursor-pointer transition-all ".concat(c.accommodations.includes(e.id)?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-600 hover:border-gray-300"),onClick:()=>{const s=c.accommodations.includes(e.id);m(r=>(0,a.A)((0,a.A)({},r),{},{accommodations:s?r.accommodations.filter(s=>s!==e.id):[...r.accommodations,e.id]}))},children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"text-2xl mb-2",children:e.icon}),(0,n.jsx)("div",{className:"font-medium text-gray-900 dark:text-white mb-1",children:e.label}),(0,n.jsx)("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:e.description})]})},e.id))})]}),("schedule"===l||"progress"===l)&&(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("div",{className:"text-6xl mb-4",children:"schedule"===l?"\ud83d\udcc5":"\ud83d\udcca"}),(0,n.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"schedule"===l?o("scheduleBuilder","Schedule Builder"):o("progressTracking","Progress Tracking")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"schedule"===l?o("scheduleComingSoon","Visual schedule builder coming soon..."):o("progressComingSoon","Advanced progress tracking coming soon...")})]})]}),(0,n.jsxs)("div",{className:"mt-8 flex justify-end space-x-4",children:[(0,n.jsx)("button",{onClick:()=>u(s),className:"px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:o("regeneratePlan","Regenerate Plan")}),(0,n.jsx)("button",{onClick:()=>null===r||void 0===r?void 0:r(c),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:o("saveTreatmentPlan","Save Treatment Plan")})]})]})}}}]);
//# sourceMappingURL=2844.e3673cc9.chunk.js.map