"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8489],{2816:(e,s,t)=>{t.d(s,{rR:()=>a});const a={FULL_COMPLIANCE:{score:100,label:"Full Compliance",color:"green",description:"Meets all CARF standards"},SUBSTANTIAL_COMPLIANCE:{score:85,label:"Substantial Compliance",color:"yellow",description:"Minor areas for improvement"},PARTIAL_COMPLIANCE:{score:70,label:"Partial Compliance",color:"orange",description:"Significant improvements needed"},NON_COMPLIANCE:{score:0,label:"Non-Compliance",color:"red",description:"Major deficiencies identified"}}},8489:(e,s,t)=>{t.r(s),t.d(s,{default:()=>o});var a=t(5043),i=t(7921),r=t(2816),l=t(579);const n=()=>{var e,s,t,n,o,d,c,u,m,x,g,v,b,h,p,f,y,j,N,k,w,C,A,M,I,L,D,R,S,P,O,_,E,F,T,q;const{t:U,isRTL:B}=(0,i.o)(),[V,Q]=(0,a.useState)({}),[G,Y]=(0,a.useState)(null),[z,H]=(0,a.useState)(!0);(0,a.useEffect)(()=>{const e={overall:{score:87,level:"SUBSTANTIAL_COMPLIANCE",lastAssessment:"2024-01-15",nextAssessment:"2024-07-15"},categories:{documentation:{score:92,status:"compliant",issues:2},qualityIndicators:{score:85,status:"substantial",issues:3},outcomeMeasures:{score:88,status:"compliant",issues:1},riskManagement:{score:90,status:"compliant",issues:1},performanceImprovement:{score:82,status:"substantial",issues:4}},recentFindings:[{id:1,standard:"DOC_PROGRESS",finding:"Progress notes missing objective measurements in 8% of cases",severity:"Minor",dueDate:"2024-03-01",status:"In Progress"},{id:2,standard:"QI_SAT",finding:"Satisfaction survey response rate below target (78%)",severity:"Moderate",dueDate:"2024-02-15",status:"Pending"},{id:3,standard:"RM_SAFETY",finding:"Fall risk assessment documentation incomplete",severity:"Minor",dueDate:"2024-02-28",status:"Completed"}]};setTimeout(()=>{Q(e),H(!1)},1e3)},[]);const J=e=>{let{title:s,score:t,status:a,issues:i,icon:r,color:n}=e;return(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"p-3 bg-".concat(n,"-100 dark:bg-").concat(n,"-900/30 rounded-lg"),children:(0,l.jsx)("i",{className:"".concat(r," text-").concat(n,"-600 dark:text-").concat(n,"-400 text-xl")})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:s}),(0,l.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[i," ",U("issuesIdentified","issues identified")]})]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("div",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:[t,"%"]}),(0,l.jsx)("div",{className:"text-sm font-medium ".concat("compliant"===a?"text-green-600":"substantial"===a?"text-yellow-600":"text-red-600"),children:"compliant"===a?U("compliant","Compliant"):"substantial"===a?U("substantial","Substantial"):U("nonCompliant","Non-Compliant")})]})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-".concat(n,"-600 h-2 rounded-full transition-all duration-300"),style:{width:"".concat(t,"%")}})})]})},K=e=>{let{finding:s,onViewDetails:t}=e;return(0,l.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-4",children:(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,l.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat("Minor"===s.severity?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300":"Moderate"===s.severity?"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300":"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"),children:s.severity}),(0,l.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat("Completed"===s.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300":"In Progress"===s.status?"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300":"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"),children:s.status})]}),(0,l.jsx)("p",{className:"text-gray-900 dark:text-white font-medium mb-1",children:s.finding}),(0,l.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[U("dueDate","Due Date"),": ",new Date(s.dueDate).toLocaleDateString()]})]}),(0,l.jsx)("button",{onClick:()=>t(s),className:"ml-4 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:U("viewDetails","View Details")})]})})};if(z)return(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})});const W=(X=(null===(e=V.overall)||void 0===e?void 0:e.score)||0)>=95?r.rR.FULL_COMPLIANCE:X>=85?r.rR.SUBSTANTIAL_COMPLIANCE:X>=70?r.rR.PARTIAL_COMPLIANCE:r.rR.NON_COMPLIANCE;var X;return(0,l.jsxs)("div",{className:"space-y-6 ".concat(B?"font-arabic":"font-english"),children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:U("carfCompliance","CARF Compliance Dashboard")}),(0,l.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:U("carfDescription","Monitor compliance with CARF accreditation standards")})]}),(0,l.jsxs)("div",{className:"flex space-x-3",children:[(0,l.jsxs)("button",{className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-download mr-2"}),U("exportReport","Export Report")]}),(0,l.jsxs)("button",{className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,l.jsx)("i",{className:"fas fa-sync mr-2"}),U("runAssessment","Run Assessment")]})]})]}),(0,l.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:U("overallCompliance","Overall CARF Compliance")}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("div",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:[null===(s=V.overall)||void 0===s?void 0:s.score,"%"]}),(0,l.jsx)("div",{className:"text-sm font-medium text-".concat(W.color,"-600"),children:W.label})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:U("lastAssessment","Last Assessment")}),(0,l.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:new Date(null===(t=V.overall)||void 0===t?void 0:t.lastAssessment).toLocaleDateString()})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-1",children:U("nextAssessment","Next Assessment")}),(0,l.jsx)("p",{className:"font-medium text-gray-900 dark:text-white",children:new Date(null===(n=V.overall)||void 0===n?void 0:n.nextAssessment).toLocaleDateString()})]})]}),(0,l.jsx)("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4",children:(0,l.jsx)("div",{className:"bg-".concat(W.color,"-600 h-4 rounded-full transition-all duration-500"),style:{width:"".concat(null===(o=V.overall)||void 0===o?void 0:o.score,"%")}})}),(0,l.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-2",children:W.description})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:U("complianceCategories","Compliance by Category")}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,l.jsx)(J,{title:U("documentation","Documentation"),score:null===(d=V.categories)||void 0===d||null===(c=d.documentation)||void 0===c?void 0:c.score,status:null===(u=V.categories)||void 0===u||null===(m=u.documentation)||void 0===m?void 0:m.status,issues:null===(x=V.categories)||void 0===x||null===(g=x.documentation)||void 0===g?void 0:g.issues,icon:"fas fa-file-alt",color:"blue"}),(0,l.jsx)(J,{title:U("qualityIndicators","Quality Indicators"),score:null===(v=V.categories)||void 0===v||null===(b=v.qualityIndicators)||void 0===b?void 0:b.score,status:null===(h=V.categories)||void 0===h||null===(p=h.qualityIndicators)||void 0===p?void 0:p.status,issues:null===(f=V.categories)||void 0===f||null===(y=f.qualityIndicators)||void 0===y?void 0:y.issues,icon:"fas fa-chart-line",color:"green"}),(0,l.jsx)(J,{title:U("outcomeMeasures","Outcome Measures"),score:null===(j=V.categories)||void 0===j||null===(N=j.outcomeMeasures)||void 0===N?void 0:N.score,status:null===(k=V.categories)||void 0===k||null===(w=k.outcomeMeasures)||void 0===w?void 0:w.status,issues:null===(C=V.categories)||void 0===C||null===(A=C.outcomeMeasures)||void 0===A?void 0:A.issues,icon:"fas fa-bullseye",color:"purple"}),(0,l.jsx)(J,{title:U("riskManagement","Risk Management"),score:null===(M=V.categories)||void 0===M||null===(I=M.riskManagement)||void 0===I?void 0:I.score,status:null===(L=V.categories)||void 0===L||null===(D=L.riskManagement)||void 0===D?void 0:D.status,issues:null===(R=V.categories)||void 0===R||null===(S=R.riskManagement)||void 0===S?void 0:S.issues,icon:"fas fa-shield-alt",color:"orange"}),(0,l.jsx)(J,{title:U("performanceImprovement","Performance Improvement"),score:null===(P=V.categories)||void 0===P||null===(O=P.performanceImprovement)||void 0===O?void 0:O.score,status:null===(_=V.categories)||void 0===_||null===(E=_.performanceImprovement)||void 0===E?void 0:E.status,issues:null===(F=V.categories)||void 0===F||null===(T=F.performanceImprovement)||void 0===T?void 0:T.issues,icon:"fas fa-trending-up",color:"indigo"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:U("recentFindings","Recent Compliance Findings")}),(0,l.jsx)("div",{className:"space-y-4",children:null===(q=V.recentFindings)||void 0===q?void 0:q.map(e=>(0,l.jsx)(K,{finding:e,onViewDetails:e=>Y(e)},e.id))})]})]})},o=()=>(0,l.jsx)(n,{})}}]);
//# sourceMappingURL=8489.0e91baab.chunk.js.map