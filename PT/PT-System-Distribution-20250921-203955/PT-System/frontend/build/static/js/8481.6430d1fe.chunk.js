"use strict";(self.webpackChunkphysioflow_frontend=self.webpackChunkphysioflow_frontend||[]).push([[8481],{8481:(e,a,r)=>{r.r(a),r.d(a,{default:()=>o});var s=r(2555),t=r(5043),i=r(3216),d=r(7921),l=r(3768),n=r(579);const o=()=>{const{t:e,isRTL:a}=(0,d.o)(),r=(0,i.Zp)(),[o,c]=(0,t.useState)({name:"",nationalId:"",phone:"",email:"",dateOfBirth:"",diagnosis:"",therapist:"",insuranceProvider:""}),[m,u]=(0,t.useState)([]),[g,h]=(0,t.useState)(!1),[x,b]=(0,t.useState)(!1),p=[{id:"P001",name:"\u0623\u062d\u0645\u062f \u0645\u062d\u0645\u062f \u0627\u0644\u0623\u062d\u0645\u062f",nameEn:"Ahmed Mohammed Al-Ahmed",nationalId:"**********",phone:"+966501234567",email:"<EMAIL>",dateOfBirth:"1995-03-15",age:29,diagnosis:"Cerebral Palsy",diagnosisAr:"\u0627\u0644\u0634\u0644\u0644 \u0627\u0644\u062f\u0645\u0627\u063a\u064a",therapist:"Dr. Sarah Al-Rashid",insuranceProvider:"Bupa Arabia",lastVisit:"2024-01-22",status:"active"},{id:"P002",name:"\u0641\u0627\u0637\u0645\u0629 \u0639\u0644\u064a \u0627\u0644\u0633\u0627\u0644\u0645",nameEn:"Fatima Ali Al-Salem",nationalId:"**********",phone:"+966507654321",email:"<EMAIL>",dateOfBirth:"1988-07-20",age:36,diagnosis:"Spinal Cord Injury",diagnosisAr:"\u0625\u0635\u0627\u0628\u0629 \u0627\u0644\u062d\u0628\u0644 \u0627\u0644\u0634\u0648\u0643\u064a",therapist:"Dr. Ahmed Al-Mansouri",insuranceProvider:"Tawuniya",lastVisit:"2024-01-20",status:"active"},{id:"P003",name:"\u0645\u062d\u0645\u062f \u0639\u0628\u062f\u0627\u0644\u0644\u0647 \u0627\u0644\u062e\u0627\u0644\u062f",nameEn:"Mohammed Abdullah Al-Khalid",nationalId:"**********",phone:"+966512345678",email:"<EMAIL>",dateOfBirth:"2010-12-05",age:14,diagnosis:"Autism Spectrum Disorder",diagnosisAr:"\u0627\u0636\u0637\u0631\u0627\u0628 \u0637\u064a\u0641 \u0627\u0644\u062a\u0648\u062d\u062f",therapist:"Dr. Fatima Al-Zahra",insuranceProvider:"Medgulf",lastVisit:"2024-01-18",status:"active"}],f=(e,a)=>{c(r=>(0,s.A)((0,s.A)({},r),{},{[e]:a}))};return(0,n.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:e("patientSearch","Patient Search")}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:e("patientSearchDesc","Search for patients using various criteria")})]}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6 mb-8",children:[(0,n.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-6",children:[(0,n.jsx)("i",{className:"fas fa-search text-blue-600 dark:text-blue-400 mr-2"}),e("searchCriteria","Search Criteria")]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("patientName","Patient Name")}),(0,n.jsx)("input",{type:"text",value:o.name,onChange:e=>f("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterPatientName","Enter patient name")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("nationalId","National ID")}),(0,n.jsx)("input",{type:"text",value:o.nationalId,onChange:e=>f("nationalId",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterNationalId","Enter national ID")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("phoneNumber","Phone Number")}),(0,n.jsx)("input",{type:"text",value:o.phone,onChange:e=>f("phone",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterPhoneNumber","Enter phone number")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("email","Email")}),(0,n.jsx)("input",{type:"email",value:o.email,onChange:e=>f("email",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterEmail","Enter email address")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("dateOfBirth","Date of Birth")}),(0,n.jsx)("input",{type:"date",value:o.dateOfBirth,onChange:e=>f("dateOfBirth",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("diagnosis","Diagnosis")}),(0,n.jsx)("input",{type:"text",value:o.diagnosis,onChange:e=>f("diagnosis",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterDiagnosis","Enter diagnosis")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("therapist","Therapist")}),(0,n.jsx)("input",{type:"text",value:o.therapist,onChange:e=>f("therapist",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:e("enterTherapistName","Enter therapist name")})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:e("insuranceProvider","Insurance Provider")}),(0,n.jsxs)("select",{value:o.insuranceProvider,onChange:e=>f("insuranceProvider",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white",children:[(0,n.jsx)("option",{value:"",children:e("selectInsuranceProvider","Select insurance provider")}),(0,n.jsx)("option",{value:"Bupa Arabia",children:"Bupa Arabia"}),(0,n.jsx)("option",{value:"Tawuniya",children:"Tawuniya"}),(0,n.jsx)("option",{value:"Medgulf",children:"Medgulf"}),(0,n.jsx)("option",{value:"SAICO",children:"SAICO"})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,n.jsxs)("button",{onClick:()=>{c({name:"",nationalId:"",phone:"",email:"",dateOfBirth:"",diagnosis:"",therapist:"",insuranceProvider:""}),u([]),b(!1)},className:"px-6 py-2 border border-gray-300 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-times mr-2"}),e("clear","Clear")]}),(0,n.jsx)("button",{onClick:async()=>{h(!0),b(!0);try{await new Promise(e=>setTimeout(e,1e3));const a=p.filter(e=>(!o.name||e.name.toLowerCase().includes(o.name.toLowerCase())||e.nameEn.toLowerCase().includes(o.name.toLowerCase()))&&(!o.nationalId||e.nationalId.includes(o.nationalId))&&(!o.phone||e.phone.includes(o.phone))&&(!o.email||e.email.toLowerCase().includes(o.email.toLowerCase()))&&(!o.dateOfBirth||e.dateOfBirth===o.dateOfBirth)&&(!o.diagnosis||e.diagnosis.toLowerCase().includes(o.diagnosis.toLowerCase())||e.diagnosisAr.includes(o.diagnosis))&&(!o.therapist||e.therapist.toLowerCase().includes(o.therapist.toLowerCase()))&&(!o.insuranceProvider||e.insuranceProvider.toLowerCase().includes(o.insuranceProvider.toLowerCase())));u(a),0===a.length?l.Ay.info(e("noResultsFound","No patients found matching your search criteria")):l.Ay.success(e("searchCompleted","Found ".concat(a.length," patient(s)")))}catch(a){l.Ay.error(e("searchError","Error performing search"))}finally{h(!1)}},disabled:g,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),e("searching","Searching...")]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("i",{className:"fas fa-search mr-2"}),e("search","Search")]})})]})]}),x&&(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600",children:[(0,n.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[(0,n.jsx)("i",{className:"fas fa-list text-green-600 dark:text-green-400 mr-2"}),e("searchResults","Search Results")," (",m.length,")"]})}),m.length>0?(0,n.jsx)("div",{className:"p-6",children:(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map(s=>(0,n.jsxs)("div",{onClick:()=>{return e=s.id,void r("/patients/".concat(e));var e},className:"border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-lg transition-all cursor-pointer hover:border-blue-300 dark:hover:border-blue-600",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center",children:(0,n.jsx)("i",{className:"fas fa-user text-blue-600 dark:text-blue-400 text-xl"})}),(0,n.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat("active"===s.status?"text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400":"text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400"),children:e(s.status,s.status)})]}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:a?s.name:s.nameEn}),(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-id-card mr-2 w-4"}),(0,n.jsx)("span",{children:s.nationalId})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-phone mr-2 w-4"}),(0,n.jsx)("span",{children:s.phone})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-stethoscope mr-2 w-4"}),(0,n.jsx)("span",{children:a?s.diagnosisAr:s.diagnosis})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-user-md mr-2 w-4"}),(0,n.jsx)("span",{children:s.therapist})]}),(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("i",{className:"fas fa-calendar mr-2 w-4"}),(0,n.jsxs)("span",{children:[e("lastVisit","Last Visit"),": ",s.lastVisit]})]})]}),(0,n.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)("i",{className:"fas fa-eye mr-2"}),e("viewDetails","View Details")]})})]},s.id))})}):(0,n.jsxs)("div",{className:"p-12 text-center",children:[(0,n.jsx)("i",{className:"fas fa-search text-4xl text-gray-400 mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:e("noResultsFound","No Results Found")}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:e("noResultsDesc","No patients found matching your search criteria. Try adjusting your search terms.")})]})]})]})}}}]);
//# sourceMappingURL=8481.6430d1fe.chunk.js.map