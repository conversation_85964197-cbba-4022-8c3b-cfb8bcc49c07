{"version": 3, "file": "static/js/1580.e661e4bb.chunk.js", "mappings": "6MAIA,MA+oBA,EA/oBeA,KACb,MAAM,EAAEC,EAAC,MAAEC,IAAUC,EAAAA,EAAAA,MACdC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAS,gBACpCC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,IAGhCG,EAAmBC,IAAwBJ,EAAAA,EAAAA,UAAS,CACzD,CACEK,GAAI,eACJC,YAAa,kBACbC,WAAY,aACZC,kBAAmB,cACnBC,aAAc,aACdC,UAAW,aACXC,OAAQ,SACRC,aAAc,gBACdC,WAAY,IACZC,gBAAiB,GACjBC,YAAa,IACbC,aAAc,KACdC,kBAAmB,KACnBC,WAAY,cAEd,CACEb,GAAI,eACJC,YAAa,kBACbC,WAAY,aACZC,kBAAmB,WACnBC,aAAc,YACdC,UAAW,aACXC,OAAQ,SACRC,aAAc,QACdC,WAAY,IACZC,gBAAiB,GACjBC,YAAa,IACbC,aAAc,KACdC,kBAAmB,MACnBC,WAAY,iBAITC,EAAmBC,IAAwBpB,EAAAA,EAAAA,UAAS,CACzD,CACEK,GAAI,iBACJC,YAAa,kBACbC,WAAY,aACZc,YAAa,kBACbC,eAAgB,aAChBC,YAAa,aACbC,SAAU,YACVC,YAAa,mBACbC,OAAQ,KACRf,OAAQ,WACRgB,eAAgB,KAChBC,gBAAiB,KACjBC,eAAgB,SAChBC,gBAAiB,iBAEnB,CACEzB,GAAI,iBACJC,YAAa,kBACbC,WAAY,aACZc,YAAa,kBACbC,eAAgB,aAChBC,YAAa,aACbC,SAAU,YACVC,YAAa,uBACbC,OAAQ,IACRf,OAAQ,UACRgB,eAAgB,EAChBC,gBAAiB,KACjBC,eAAgB,cAChBC,gBAAiB,iBAEnB,CACEzB,GAAI,iBACJC,YAAa,qBACbC,WAAY,aACZc,YAAa,kBACbC,eAAgB,aAChBC,YAAa,aACbC,SAAU,YACVC,YAAa,iBACbC,OAAQ,KACRf,OAAQ,WACRgB,eAAgB,EAChBC,gBAAiB,2CACjBC,eAAgB,QAChBC,gBAAiB,oBAIdC,EAAmBC,IAAwBhC,EAAAA,EAAAA,UAAS,CACzD,CACEK,GAAI,gBACJ4B,WAAY,wBACZC,OAAQ,gBACRC,cAAe,aACfxB,OAAQ,YACRyB,YAAa,GACbC,eAAgB,GAChBC,eAAgB,EAChBC,cAAe,EACfC,YAAa,MACbb,eAAgB,KAChBc,gBAAiB,IAEnB,CACEpC,GAAI,gBACJ4B,WAAY,8BACZC,OAAQ,UACRC,cAAe,aACfxB,OAAQ,QACRyB,YAAa,IACbC,eAAgB,IAChBC,eAAgB,GAChBC,cAAe,EACfC,YAAa,MACbb,eAAgB,OAChBc,gBAAiB,MAIfC,EAAO,CACX,CAAErC,GAAI,cAAesC,MAAOhD,EAAE,0BAA2B,4BAA6BiD,KAAM,uBAC5F,CAAEvC,GAAI,SAAUsC,MAAOhD,EAAE,mBAAoB,qBAAsBiD,KAAM,uBACzE,CAAEvC,GAAI,aAAcsC,MAAOhD,EAAE,sBAAuB,wBAAyBiD,KAAM,0BACnF,CAAEvC,GAAI,WAAYsC,MAAOhD,EAAE,iBAAkB,mBAAoBiD,KAAM,eAGnEC,EAAkBnB,GACf,IAAIoB,KAAKC,aAAa,QAAS,CACpCC,MAAO,WACPC,SAAU,QACTC,OAAOxB,GAGNyB,EAAkBxC,IACtB,OAAQA,EAAOyC,eACb,IAAK,SACL,IAAK,WAAY,MAAO,mDACxB,IAAK,UAAW,MAAO,sDACvB,IAAK,WAAY,MAAO,6CACxB,IAAK,YAAa,MAAO,gDAEzB,QAAS,MAAO,kDAobpB,OACEC,EAAAA,EAAAA,MAAA,OAAKC,UAAS,aAAAC,OAAe3D,EAAQ,cAAgB,gBAAiB4D,SAAA,EAEpEH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oCAAmCE,SAAA,EAChDH,EAAAA,EAAAA,MAAA,OAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,mDAAkDE,SAC7D7D,EAAE,oBAAqB,yBAE1B8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,wCAAuCE,SACjD7D,EAAE,oBAAqB,6EAI5B0D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8BAA6BE,SAAA,EAC1CH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2EAA0EE,SAAA,EACvFC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,4CACfG,EAAAA,EAAAA,KAAA,QAAMH,UAAU,6CAA4CE,SACzD7D,EAAE,YAAa,mBAGpB0D,EAAAA,EAAAA,MAAA,UAAQC,UAAU,kFAAiFE,SAAA,EACjGC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,qBACZ3D,EAAE,WAAY,uBAMrB8D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,gDAA+CE,UAC5DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wBAAuBE,SACnCd,EAAKgB,IAAKC,IACTN,EAAAA,EAAAA,MAAA,UAEEO,QAASA,IAAM7D,EAAa4D,EAAItD,IAChCiD,UAAS,4CAAAC,OACPzD,IAAc6D,EAAItD,GACd,mDACA,0HACHmD,SAAA,EAEHC,EAAAA,EAAAA,KAAA,KAAGH,UAAS,GAAAC,OAAKI,EAAIf,KAAI,WACxBe,EAAIhB,QATAgB,EAAItD,SAgBF,gBAAdP,IAvaHuD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChE7D,EAAE,cAAe,mBAEpB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DrD,EAAkB0D,kBAM3BJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,6DAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChE7D,EAAE,iBAAkB,sBAEvB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DrD,EAAkB2D,OAAOC,GAA0B,WAAjBA,EAAMpD,QAAqBkD,kBAMtEJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChE7D,EAAE,gBAAiB,qBAEtB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DX,EAAe1C,EAAkB6D,OAAO,CAACC,EAAKF,IAAUE,EAAMF,EAAM9C,kBAAmB,kBAQlGoC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrE7D,EAAE,sBAAuB,4BAE5B0D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,SACES,KAAK,OACLC,YAAaxE,EAAE,aAAc,eAC7B2D,UAAU,8HAEZG,EAAAA,EAAAA,KAAA,SACES,KAAK,OACLC,YAAaxE,EAAE,eAAgB,iBAC/B2D,UAAU,8HAEZD,EAAAA,EAAAA,MAAA,UACEO,QAASA,IA9HYQ,WAC7BlE,GAAW,GACX,UAEQ,IAAImE,QAAQC,GAAWC,WAAWD,EAAS,MAEjD,MAAME,GAAQC,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACZpE,GAAG,YAADkD,OAAcmB,OAAOvE,EAAkB0D,OAAS,GAAGc,SAAS,EAAG,OAC9DC,GAAW,IACdlE,WAAW,IAAImE,MAAOC,cAAcC,MAAM,KAAK,GAC/CpE,OAAQ,SACRC,aAAc,gBACdC,WAAY,IACZC,gBAAiB,GACjBC,YAAa,IACbC,aAAc,EACdC,kBAAmB,IACnBC,WAAY,eAGdd,EAAqB4E,GAAQ,CAACR,KAAaQ,IAC3CC,EAAAA,GAAMC,QAAQvF,EAAE,4BAA6B,4CAC/C,CAAE,MAAOwF,GACPF,EAAAA,GAAME,MAAMxF,EAAE,yBAA0B,4BAC1C,CAAC,QACCO,GAAW,EACb,GAoGuBkF,CAAuB,CAAC,GACvCC,SAAUpF,EACVqD,UAAU,sGAAqGE,SAAA,CAE9GvD,GACCwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iCAEbG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uBAEd3D,EAAE,mBAAoB,+BAM7B0D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0DAAyDE,UACtEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChE7D,EAAE,qBAAsB,4BAI7B8D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,UAAW,cAEhB8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,YAAa,gBAElB8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,WAAY,eAEjB8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,YAAa,gBAElB8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,SAAU,aAEf8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,UAAW,mBAIpB8D,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvFrD,EAAkBuD,IAAKK,IACtBV,EAAAA,EAAAA,MAAA,MAAmBC,UAAU,0CAAyCE,SAAA,EACpEH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DO,EAAMzD,eAET+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CAAC,OACnDO,EAAMxD,kBAGf8C,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDO,EAAMvD,qBAETiD,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDO,EAAMtD,mBAGX4C,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDX,EAAekB,EAAMhD,gBAExB0C,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDO,EAAMnD,mBAGXyC,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DX,EAAekB,EAAM9C,sBAExBoC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CACtD8B,KAAKC,MAAOxB,EAAM9C,kBAAoB8C,EAAMhD,YAAe,KAAK,KAAGpB,EAAE,YAAa,oBAGvF8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,8BAA6BE,UACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAeY,EAAMpD,SAAU6C,SAC3FO,EAAMpD,YAGX0C,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDE,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,qFAAoFE,SACnG7D,EAAE,cAAe,mBAEpB8D,EAAAA,EAAAA,KAAA,UAAQH,UAAU,oFAAmFE,SAClG7D,EAAE,UAAW,kBA3CXoE,EAAM1D,kBAmTV,WAAdP,IA3PHuD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWE,SAAA,EAExBH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,wCAAuCE,SAAA,EACpDC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,iDAAgDE,UAC7DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,oEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChE7D,EAAE,cAAe,mBAEpB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DrC,EAAkB0C,kBAM3BJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,mDAAkDE,UAC/DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,sEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChE7D,EAAE,iBAAkB,eAEvB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DrC,EAAkB2C,OAAO0B,GAA0B,aAAjBA,EAAM7E,QAAuBkD,kBAMxEJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,qDAAoDE,UACjEC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChE7D,EAAE,gBAAiB,cAEtB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DrC,EAAkB2C,OAAO0B,GAA0B,YAAjBA,EAAM7E,QAAsBkD,kBAMvEJ,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kDAAiDE,UAC9DH,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBE,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,+CAA8CE,UAC3DC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,kEAEfD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,OAAME,SAAA,EACnBC,EAAAA,EAAAA,KAAA,KAAGH,UAAU,uDAAsDE,SAChE7D,EAAE,iBAAkB,eAEvB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mDAAkDE,SAC5DrC,EAAkB2C,OAAO0B,GAA0B,aAAjBA,EAAM7E,QAAuBkD,qBAQ1ER,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrE7D,EAAE,iBAAkB,uBAEvB0D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,uDAAsDE,SAAA,EACnEC,EAAAA,EAAAA,KAAA,SACES,KAAK,OACLC,YAAaxE,EAAE,cAAe,gBAC9B2D,UAAU,8HAEZG,EAAAA,EAAAA,KAAA,SACES,KAAK,OACLC,YAAaxE,EAAE,cAAe,gBAC9B2D,UAAU,8HAEZG,EAAAA,EAAAA,KAAA,SACES,KAAK,SACLC,YAAaxE,EAAE,SAAU,UACzB2D,UAAU,8HAEZD,EAAAA,EAAAA,MAAA,UACEO,QAASA,IAnSWQ,WAC5BlE,GAAW,GACX,UAEQ,IAAImE,QAAQC,GAAWC,WAAWD,EAAS,MAEjD,MAAMmB,GAAQhB,EAAAA,EAAAA,IAAAA,EAAAA,EAAAA,GAAA,CACZpE,GAAG,cAADkD,OAAgBmB,OAAOvD,EAAkB0C,OAAS,GAAGc,SAAS,EAAG,MACnEtD,YAAY,eAADkC,OAAiBmB,OAAOvD,EAAkB0C,OAAS,GAAGc,SAAS,EAAG,OAC1Ee,GAAS,IACZpE,gBAAgB,IAAIuD,MAAOC,cAAcC,MAAM,KAAK,GACpDpE,OAAQ,UACRgB,eAAgB,EAChBC,gBAAiB,KACjBC,eAAgB,cAChBC,gBAAgB,WAADyB,OAAa+B,KAAKK,SAASC,SAAS,IAAIC,OAAO,EAAG,MAGnEzE,EAAqB4D,GAAQ,CAACS,KAAaT,IAC3CC,EAAAA,GAAMC,QAAQvF,EAAE,6BAA8B,0CAChD,CAAE,MAAOwF,GACPF,EAAAA,GAAME,MAAMxF,EAAE,wBAAyB,2BACzC,CAAC,QACCO,GAAW,EACb,GA2QuB4F,CAAsB,CAAC,GACtCT,SAAUpF,EACVqD,UAAU,sGAAqGE,SAAA,CAE9GvD,GACCwD,EAAAA,EAAAA,KAAA,KAAGH,UAAU,iCAEbG,EAAAA,EAAAA,KAAA,KAAGH,UAAU,4BAEd3D,EAAE,cAAe,0BAMxB0D,EAAAA,EAAAA,MAAA,OAAKC,UAAU,8CAA6CE,SAAA,EAC1DC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,0DAAyDE,UACtEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,sDAAqDE,SAChE7D,EAAE,gBAAiB,uBAIxB8D,EAAAA,EAAAA,KAAA,OAAKH,UAAU,kBAAiBE,UAC9BH,EAAAA,EAAAA,MAAA,SAAOC,UAAU,SAAQE,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOH,UAAU,8BAA6BE,UAC5CH,EAAAA,EAAAA,MAAA,MAAAG,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,cAAe,mBAEpB8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,UAAW,cAEhB8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,UAAW,cAEhB8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,SAAU,aAEf8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,SAAU,aAEf8D,EAAAA,EAAAA,KAAA,MAAIH,UAAU,oGAAmGE,SAC9G7D,EAAE,UAAW,mBAIpB8D,EAAAA,EAAAA,KAAA,SAAOH,UAAU,0EAAyEE,SACvFrC,EAAkBuC,IAAK8B,IACtBnC,EAAAA,EAAAA,MAAA,MAAmBC,UAAU,0CAAyCE,SAAA,EACpEH,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DgC,EAAMnE,eAEToC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDgC,EAAM1D,sBAGXuB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDgC,EAAMlF,eAET+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,2CAA0CE,SAAA,CAAC,OACnDgC,EAAMjF,kBAGf8C,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,wCAAuCE,SACnDgC,EAAM/D,eAETgC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,2CAA0CE,SACtDgC,EAAMjE,kBAGX8B,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,OAAKH,UAAU,oDAAmDE,SAC/DX,EAAe2C,EAAM9D,UAEvB8D,EAAM7D,eAAiB,IACtB0B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,6CAA4CE,SAAA,CACxDX,EAAe2C,EAAM7D,gBAAgB,IAAEhC,EAAE,WAAY,mBAI5D0D,EAAAA,EAAAA,MAAA,MAAIC,UAAU,8BAA6BE,SAAA,EACzCC,EAAAA,EAAAA,KAAA,QAAMH,UAAS,8CAAAC,OAAgDJ,EAAeqC,EAAM7E,SAAU6C,SAC3FgC,EAAM7E,SAER6E,EAAM5D,kBACL6B,EAAAA,EAAAA,KAAA,OAAKH,UAAU,8CAA6CE,SACzDgC,EAAM5D,sBAIbyB,EAAAA,EAAAA,MAAA,MAAIC,UAAU,kDAAiDE,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UAAQH,UAAU,qFAAoFE,SACnG7D,EAAE,cAAe,kBAEF,YAAjB6F,EAAM7E,SACL8C,EAAAA,EAAAA,KAAA,UAAQH,UAAU,oFAAmFE,SAClG7D,EAAE,QAAS,gBAnDX6F,EAAMnF,kBAkHV,eAAdP,IACCuD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrE7D,EAAE,sBAAuB,2BAE5B8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5C7D,EAAE,wBAAyB,4EAInB,aAAdG,IACCuD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kDAAiDE,SAAA,EAC9DC,EAAAA,EAAAA,KAAA,MAAIH,UAAU,2DAA0DE,SACrE7D,EAAE,iBAAkB,sBAEvB8D,EAAAA,EAAAA,KAAA,KAAGH,UAAU,mCAAkCE,SAC5C7D,EAAE,sBAAuB,kF", "sources": ["pages/NPHIES/NPHIES.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport toast from 'react-hot-toast';\n\nconst NPHIES = () => {\n  const { t, isRTL } = useLanguage();\n  const [activeTab, setActiveTab] = useState('eligibility');\n  const [loading, setLoading] = useState(false);\n\n  // Mock NPHIES data\n  const [eligibilityChecks, setEligibilityChecks] = useState([\n    {\n      id: 'ELG-2024-001',\n      patientName: '<PERSON>',\n      nationalId: '**********',\n      insuranceProvider: 'Bupa Arabia',\n      policyNumber: 'BUPA-12345',\n      checkDate: '2024-02-15',\n      status: 'Active',\n      coverageType: 'Comprehensive',\n      deductible: 500,\n      copayPercentage: 20,\n      maxCoverage: 50000,\n      usedCoverage: 12000,\n      remainingCoverage: 38000,\n      validUntil: '2024-12-31'\n    },\n    {\n      id: 'ELG-2024-002',\n      patientName: '<PERSON>ima Al-Zahra',\n      nationalId: '**********',\n      insuranceProvider: 'Tawuniya',\n      policyNumber: 'TAW-67890',\n      checkDate: '2024-02-14',\n      status: 'Active',\n      coverageType: 'Basic',\n      deductible: 300,\n      copayPercentage: 15,\n      maxCoverage: 30000,\n      usedCoverage: 8500,\n      remainingCoverage: 21500,\n      validUntil: '2024-11-30'\n    }\n  ]);\n\n  const [claimsSubmissions, setClaimsSubmissions] = useState([\n    {\n      id: 'CLM-NPHIES-001',\n      patientName: 'Ahmed Al-Rashid',\n      nationalId: '**********',\n      claimNumber: 'NPHIES-2024-001',\n      submissionDate: '2024-02-15',\n      serviceDate: '2024-02-10',\n      provider: 'PT Clinic',\n      serviceType: 'Physical Therapy',\n      amount: 1500,\n      status: 'Approved',\n      approvedAmount: 1200,\n      rejectionReason: null,\n      processingTime: '2 days',\n      nphiesReference: 'NPH-REF-12345'\n    },\n    {\n      id: 'CLM-NPHIES-002',\n      patientName: 'Fatima Al-Zahra',\n      nationalId: '**********',\n      claimNumber: 'NPHIES-2024-002',\n      submissionDate: '2024-02-14',\n      serviceDate: '2024-02-08',\n      provider: 'PT Clinic',\n      serviceType: 'Occupational Therapy',\n      amount: 2000,\n      status: 'Pending',\n      approvedAmount: 0,\n      rejectionReason: null,\n      processingTime: 'In Progress',\n      nphiesReference: 'NPH-REF-67890'\n    },\n    {\n      id: 'CLM-NPHIES-003',\n      patientName: 'Mohammed Al-Otaibi',\n      nationalId: '**********',\n      claimNumber: 'NPHIES-2024-003',\n      submissionDate: '2024-02-13',\n      serviceDate: '2024-02-05',\n      provider: 'PT Clinic',\n      serviceType: 'Speech Therapy',\n      amount: 1200,\n      status: 'Rejected',\n      approvedAmount: 0,\n      rejectionReason: 'Service not covered under current policy',\n      processingTime: '1 day',\n      nphiesReference: 'NPH-REF-11223'\n    }\n  ]);\n\n  const [complianceReports, setComplianceReports] = useState([\n    {\n      id: 'COMP-2024-001',\n      reportType: 'Monthly Claims Report',\n      period: 'February 2024',\n      generatedDate: '2024-02-28',\n      status: 'Submitted',\n      totalClaims: 45,\n      approvedClaims: 38,\n      rejectedClaims: 4,\n      pendingClaims: 3,\n      totalAmount: 67500,\n      approvedAmount: 54000,\n      complianceScore: 95\n    },\n    {\n      id: 'COMP-2024-002',\n      reportType: 'Provider Performance Report',\n      period: 'Q1 2024',\n      generatedDate: '2024-03-31',\n      status: 'Draft',\n      totalClaims: 134,\n      approvedClaims: 118,\n      rejectedClaims: 12,\n      pendingClaims: 4,\n      totalAmount: 201000,\n      approvedAmount: 175500,\n      complianceScore: 92\n    }\n  ]);\n\n  const tabs = [\n    { id: 'eligibility', label: t('eligibilityVerification', 'Eligibility Verification'), icon: 'fas fa-check-circle' },\n    { id: 'claims', label: t('claimsSubmission', 'Claims Submission'), icon: 'fas fa-file-medical' },\n    { id: 'compliance', label: t('complianceReporting', 'Compliance Reporting'), icon: 'fas fa-clipboard-check' },\n    { id: 'settings', label: t('nphiesSettings', 'NPHIES Settings'), icon: 'fas fa-cog' }\n  ];\n\n  const formatCurrency = (amount) => {\n    return new Intl.NumberFormat('ar-SA', {\n      style: 'currency',\n      currency: 'SAR'\n    }).format(amount);\n  };\n\n  const getStatusColor = (status) => {\n    switch (status.toLowerCase()) {\n      case 'active': return 'text-green-600 bg-green-100 dark:bg-green-900/30';\n      case 'approved': return 'text-green-600 bg-green-100 dark:bg-green-900/30';\n      case 'pending': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';\n      case 'rejected': return 'text-red-600 bg-red-100 dark:bg-red-900/30';\n      case 'submitted': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';\n      case 'draft': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';\n      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';\n    }\n  };\n\n  const handleEligibilityCheck = async (patientData) => {\n    setLoading(true);\n    try {\n      // Mock API call to NPHIES\n      await new Promise(resolve => setTimeout(resolve, 3000));\n      \n      const newCheck = {\n        id: `ELG-2024-${String(eligibilityChecks.length + 1).padStart(3, '0')}`,\n        ...patientData,\n        checkDate: new Date().toISOString().split('T')[0],\n        status: 'Active', // Mock response\n        coverageType: 'Comprehensive',\n        deductible: 500,\n        copayPercentage: 20,\n        maxCoverage: 50000,\n        usedCoverage: 0,\n        remainingCoverage: 50000,\n        validUntil: '2024-12-31'\n      };\n      \n      setEligibilityChecks(prev => [newCheck, ...prev]);\n      toast.success(t('eligibilityCheckCompleted', 'Eligibility check completed successfully'));\n    } catch (error) {\n      toast.error(t('eligibilityCheckFailed', 'Eligibility check failed'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleClaimSubmission = async (claimData) => {\n    setLoading(true);\n    try {\n      // Mock API call to NPHIES\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const newClaim = {\n        id: `CLM-NPHIES-${String(claimsSubmissions.length + 1).padStart(3, '0')}`,\n        claimNumber: `NPHIES-2024-${String(claimsSubmissions.length + 1).padStart(3, '0')}`,\n        ...claimData,\n        submissionDate: new Date().toISOString().split('T')[0],\n        status: 'Pending',\n        approvedAmount: 0,\n        rejectionReason: null,\n        processingTime: 'In Progress',\n        nphiesReference: `NPH-REF-${Math.random().toString(36).substr(2, 9)}`\n      };\n      \n      setClaimsSubmissions(prev => [newClaim, ...prev]);\n      toast.success(t('claimSubmittedSuccessfully', 'Claim submitted to NPHIES successfully'));\n    } catch (error) {\n      toast.error(t('claimSubmissionFailed', 'Claim submission failed'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const renderEligibilityTab = () => (\n    <div className=\"space-y-6\">\n      {/* Eligibility Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalChecks', 'Total Checks')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {eligibilityChecks.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-users text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('activePatients', 'Active Patients')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {eligibilityChecks.filter(check => check.status === 'Active').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg\">\n              <i className=\"fas fa-dollar-sign text-purple-600 dark:text-purple-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalCoverage', 'Total Coverage')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {formatCurrency(eligibilityChecks.reduce((sum, check) => sum + check.remainingCoverage, 0))}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Eligibility Verification Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('newEligibilityCheck', 'New Eligibility Check')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <input\n            type=\"text\"\n            placeholder={t('nationalId', 'National ID')}\n            className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          />\n          <input\n            type=\"text\"\n            placeholder={t('policyNumber', 'Policy Number')}\n            className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          />\n          <button\n            onClick={() => handleEligibilityCheck({})}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          >\n            {loading ? (\n              <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n            ) : (\n              <i className=\"fas fa-search mr-2\"></i>\n            )}\n            {t('checkEligibility', 'Check Eligibility')}\n          </button>\n        </div>\n      </div>\n\n      {/* Eligibility Results */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('eligibilityResults', 'Eligibility Results')}\n          </h3>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('insurance', 'Insurance')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('coverage', 'Coverage')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('remaining', 'Remaining')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {eligibilityChecks.map((check) => (\n                <tr key={check.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {check.patientName}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      ID: {check.nationalId}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {check.insuranceProvider}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {check.policyNumber}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {formatCurrency(check.maxCoverage)}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {check.coverageType}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {formatCurrency(check.remainingCoverage)}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {Math.round((check.remainingCoverage / check.maxCoverage) * 100)}% {t('available', 'available')}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(check.status)}`}>\n                      {check.status}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3\">\n                      {t('viewDetails', 'View Details')}\n                    </button>\n                    <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\">\n                      {t('refresh', 'Refresh')}\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderClaimsTab = () => (\n    <div className=\"space-y-6\">\n      {/* Claims Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg\">\n              <i className=\"fas fa-file-medical text-blue-600 dark:text-blue-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('totalClaims', 'Total Claims')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {claimsSubmissions.length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n              <i className=\"fas fa-check-circle text-green-600 dark:text-green-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('approvedClaims', 'Approved')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {claimsSubmissions.filter(claim => claim.status === 'Approved').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg\">\n              <i className=\"fas fa-clock text-yellow-600 dark:text-yellow-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('pendingClaims', 'Pending')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {claimsSubmissions.filter(claim => claim.status === 'Pending').length}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-3 bg-red-100 dark:bg-red-900/30 rounded-lg\">\n              <i className=\"fas fa-times-circle text-red-600 dark:text-red-400 text-xl\"></i>\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                {t('rejectedClaims', 'Rejected')}\n              </p>\n              <p className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                {claimsSubmissions.filter(claim => claim.status === 'Rejected').length}\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Claims Submission Form */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n          {t('submitNewClaim', 'Submit New Claim')}\n        </h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <input\n            type=\"text\"\n            placeholder={t('patientName', 'Patient Name')}\n            className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          />\n          <input\n            type=\"text\"\n            placeholder={t('serviceType', 'Service Type')}\n            className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          />\n          <input\n            type=\"number\"\n            placeholder={t('amount', 'Amount')}\n            className=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n          />\n          <button\n            onClick={() => handleClaimSubmission({})}\n            disabled={loading}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          >\n            {loading ? (\n              <i className=\"fas fa-spinner fa-spin mr-2\"></i>\n            ) : (\n              <i className=\"fas fa-paper-plane mr-2\"></i>\n            )}\n            {t('submitClaim', 'Submit Claim')}\n          </button>\n        </div>\n      </div>\n\n      {/* Claims List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-600\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {t('claimsHistory', 'Claims History')}\n          </h3>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('claimNumber', 'Claim Number')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('patient', 'Patient')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('service', 'Service')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('amount', 'Amount')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('status', 'Status')}\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\n                  {t('actions', 'Actions')}\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600\">\n              {claimsSubmissions.map((claim) => (\n                <tr key={claim.id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {claim.claimNumber}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {claim.nphiesReference}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {claim.patientName}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      ID: {claim.nationalId}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm text-gray-900 dark:text-white\">\n                      {claim.serviceType}\n                    </div>\n                    <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {claim.serviceDate}\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {formatCurrency(claim.amount)}\n                    </div>\n                    {claim.approvedAmount > 0 && (\n                      <div className=\"text-sm text-green-600 dark:text-green-400\">\n                        {formatCurrency(claim.approvedAmount)} {t('approved', 'approved')}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(claim.status)}`}>\n                      {claim.status}\n                    </span>\n                    {claim.rejectionReason && (\n                      <div className=\"text-xs text-red-600 dark:text-red-400 mt-1\">\n                        {claim.rejectionReason}\n                      </div>\n                    )}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                    <button className=\"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3\">\n                      {t('viewDetails', 'View Details')}\n                    </button>\n                    {claim.status === 'Pending' && (\n                      <button className=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\">\n                        {t('track', 'Track')}\n                      </button>\n                    )}\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className={`space-y-6 ${isRTL ? 'font-arabic' : 'font-english'}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            {t('nphiesIntegration', 'NPHIES Integration')}\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n            {t('nphiesDescription', 'Saudi National Platform for Health Information Exchange integration')}\n          </p>\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex items-center px-3 py-2 bg-green-100 dark:bg-green-900/30 rounded-lg\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></div>\n            <span className=\"text-sm text-green-700 dark:text-green-300\">\n              {t('connected', 'Connected')}\n            </span>\n          </div>\n          <button className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\">\n            <i className=\"fas fa-sync mr-2\"></i>\n            {t('syncData', 'Sync Data')}\n          </button>\n        </div>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"border-b border-gray-200 dark:border-gray-600\">\n        <nav className=\"-mb-px flex space-x-8\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`py-2 px-1 border-b-2 font-medium text-sm ${\n                activeTab === tab.id\n                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <i className={`${tab.icon} mr-2`}></i>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      {/* Tab Content */}\n      {activeTab === 'eligibility' && renderEligibilityTab()}\n      {activeTab === 'claims' && renderClaimsTab()}\n      {activeTab === 'compliance' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('complianceReporting', 'Compliance Reporting')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('complianceDescription', 'Automated compliance reporting and regulatory submissions to NPHIES')}\n          </p>\n        </div>\n      )}\n      {activeTab === 'settings' && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            {t('nphiesSettings', 'NPHIES Settings')}\n          </h3>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            {t('settingsDescription', 'Configure NPHIES connection, API credentials, and integration settings')}\n          </p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NPHIES;\n"], "names": ["NPHIES", "t", "isRTL", "useLanguage", "activeTab", "setActiveTab", "useState", "loading", "setLoading", "eligibility<PERSON><PERSON><PERSON>", "setEligibilityChecks", "id", "patientName", "nationalId", "insuranceProvider", "policyNumber", "checkDate", "status", "coverageType", "deductible", "copayPercentage", "maxCoverage", "usedCoverage", "remainingCoverage", "validUntil", "claimsSubmissions", "setClaimsSubmissions", "claimNumber", "submissionDate", "serviceDate", "provider", "serviceType", "amount", "approvedAmount", "rejectionReason", "processingTime", "nphiesReference", "complianceReports", "setComplianceReports", "reportType", "period", "generatedDate", "totalClaims", "approvedClaims", "rejectedClaims", "pendingClaims", "totalAmount", "complianceScore", "tabs", "label", "icon", "formatCurrency", "Intl", "NumberFormat", "style", "currency", "format", "getStatusColor", "toLowerCase", "_jsxs", "className", "concat", "children", "_jsx", "map", "tab", "onClick", "length", "filter", "check", "reduce", "sum", "type", "placeholder", "async", "Promise", "resolve", "setTimeout", "newCheck", "_objectSpread", "String", "padStart", "patientData", "Date", "toISOString", "split", "prev", "toast", "success", "error", "handleEligibilityCheck", "disabled", "Math", "round", "claim", "newClaim", "claimData", "random", "toString", "substr", "handleClaimSubmission"], "sourceRoot": ""}