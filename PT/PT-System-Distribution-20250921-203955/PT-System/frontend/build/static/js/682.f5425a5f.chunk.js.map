{"version": 3, "file": "static/js/682.f5425a5f.chunk.js", "mappings": "qNAKA,MA6bA,EA7b6BA,KAC3B,MAAOC,EAAkBC,IAAuBC,EAAAA,EAAAA,UAAS,CACvDC,OAAQ,CACNC,OAAQ,eACRC,YAAa,EACbC,iBAAkB,EAClBC,kBAAmB,EACnBC,SAAU,MAEZC,MAAO,CACLL,OAAQ,eACRM,cAAe,EACfC,sBAAuB,EACvBC,gBAAiB,EACjBJ,SAAU,MAEZK,qBAAsB,CACpBC,eAAgB,EAChBC,kBAAmB,EACnBC,oBAAqB,EACrBC,iBAAkB,GAEpBC,gBAAiB,CACfC,YAAa,EACbC,cAAe,EACfC,aAAc,MAGXC,EAASC,IAAcrB,EAAAA,EAAAA,WAAS,IACjC,EAAEsB,IAAMC,EAAAA,EAAAA,OACR,KAAEC,IAASC,EAAAA,EAAAA,MAEjBC,EAAAA,EAAAA,WAAU,KACRC,KACC,IAEH,MAAMA,EAAuBC,UAC3BP,GAAW,GACX,IAEE,MAAMQ,QAAuBC,MAAM,uBAAwB,CACzDC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,UAChD,eAAgB,sBAKdC,QAAsBL,MAAM,2BAA4B,CAC5DC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,UAChD,eAAgB,sBAYdE,SAP0BN,MAAM,0BAA2B,CAC/DC,QAAS,CACP,cAAgB,UAADC,OAAYC,aAAaC,QAAQ,UAChD,eAAgB,uBAITG,EAAAA,EAAAA,GAAA,GAAQvC,IAEnB,GAAI+B,EAAeS,GAAI,CACrB,MAAMC,QAAmBV,EAAeW,OACxCJ,EAAMnC,QAAMoC,EAAAA,EAAAA,GAAA,CACVnC,OAAQ,aACLqC,EAAWE,KAElB,CAEA,GAAIN,EAAcG,GAAI,CACpB,MAAMI,QAAkBP,EAAcK,OACtCJ,EAAM7B,MAAQ,CACZL,OAAQ,YACRM,cAAekC,EAAUD,KAAKE,uBAC9BlC,sBAAuBiC,EAAUD,KAAKhC,sBACtCC,gBAAiBgC,EAAUD,KAAKG,kBAChCtC,SAAUoC,EAAUD,KAAKI,aAE7B,CAGAT,EAAMzB,qBAAuB,CAC3BC,eAAgB,GAChBC,kBAAmB,EACnBC,oBAAqB,GACrBC,iBAAkB,GAIpBqB,EAAMpB,gBAAkB,CACtBC,YAAa,GACbC,cAAe,GACfC,aAAc,GAGhBpB,EAAoBqC,EACtB,CAAE,MAAOU,GACPC,QAAQD,MAAM,mCAAoCA,GAkChDE,EAAAA,GAAMF,MAAM,wCAEhB,CAAC,QACCzB,GAAW,EACb,GAGI4B,EAAkB/C,IACtB,OAAQA,GACN,IAAK,YACH,MAAO,uEACT,IAAK,UACH,MAAO,2EACT,IAAK,eACH,MAAO,+DACT,QACE,MAAO,qEAIPgD,EAAiBhD,IACrB,OAAQA,GACN,IAAK,YACH,MAAO,sBACT,IAAK,UACH,MAAO,8BACT,IAAK,eACH,MAAO,sBACT,QACE,MAAO,2BAIb,OAAIkB,GAEA+B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAA+CC,UAC5DF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sEAMnBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBC,SAAA,EAEpCC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,OAAMC,SAAA,EACnBF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,mDAAkDC,SAC7D/B,EAAE,uBAAwB,4BAE7B6B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wCAAuCC,SACjD/B,EAAE,2BAA4B,6GAKnCgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4DAA2DC,SAAA,EAExEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,sEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAAC,eAItEC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,yCAAApB,OAA2CiB,EAAenD,EAAiBG,OAAOC,SAAUmD,SAAA,EACzGF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAApB,OAAKkB,EAAcpD,EAAiBG,OAAOC,QAAO,WAC7DoB,EAAExB,EAAiBG,OAAOC,OAAQJ,EAAiBG,OAAOC,eAG/DoD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,cAAe,mBAC7E6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAAEvD,EAAiBG,OAAOE,kBAE/FmD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,aAAc,iBAC5E6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qCAAoCC,SAAEvD,EAAiBG,OAAOG,uBAEhFkD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,oBAAqB,yBACnF6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCC,SAAEvD,EAAiBG,OAAOI,8BAMnFiD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAAC,cAItEC,EAAAA,EAAAA,MAAA,QAAMF,UAAS,yCAAApB,OAA2CiB,EAAenD,EAAiBS,MAAML,SAAUmD,SAAA,EACxGF,EAAAA,EAAAA,KAAA,KAAGC,UAAS,GAAApB,OAAKkB,EAAcpD,EAAiBS,MAAML,QAAO,WAC5DoB,EAAExB,EAAiBS,MAAML,OAAQJ,EAAiBS,MAAML,eAG7DoD,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,gBAAiB,qBAC/E6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAAEvD,EAAiBS,MAAMC,oBAE9F8C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,aAAc,iBAC5E6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qCAAoCC,SAAEvD,EAAiBS,MAAME,4BAE/E6C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,aAAc,iBAC5EgC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,oCAAmCC,SAAA,CAAEvD,EAAiBS,MAAMG,gBAAgB,iBAMlG4C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/B,EAAE,cAAe,sBAGtBgC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6GAA4GC,SAAA,EAC1HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BACZ9B,EAAE,SAAU,iBAGjBgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,iBAAkB,sBAChF6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAAEvD,EAAiBa,qBAAqBC,qBAE7G0C,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,UAAW,cACzE6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,sCAAqCC,SAAEvD,EAAiBa,qBAAqBE,wBAE/FyC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,YAAa,gBAC3E6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qCAAoCC,SAAEvD,EAAiBa,qBAAqBG,gCAMlGwC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wEACbD,EAAAA,EAAAA,KAAA,MAAIC,UAAU,sDAAqDC,SAChE/B,EAAE,kBAAmB,0BAG1BgC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,6GAA4GC,SAAA,EAC1HF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6BACZ9B,EAAE,SAAU,iBAGjBgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,cAAe,mBAC7E6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oDAAmDC,SAAEvD,EAAiBkB,gBAAgBC,kBAExGqC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,WAAY,eAC1E6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,qCAAoCC,SAAEvD,EAAiBkB,gBAAgBE,oBAEzFoC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,uBAAsBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,2CAA0CC,SAAE/B,EAAE,UAAW,cACzE6B,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAEvD,EAAiBkB,gBAAgBG,4BAO7FmC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wCAAuCC,SAAA,EAEpDC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4DACZ9B,EAAE,qBAAsB,2BAG3BgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAC,sBACvEC,EAAAA,EAAAA,MAAA,QAAMF,UAAU,kCAAiCC,SAAA,CAAEvD,EAAiBS,MAAMG,gBAAgB,WAE5FyC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OACEC,UAAU,2DACVG,MAAO,CAAEC,MAAM,GAADxB,OAAKlC,EAAiBS,MAAMG,gBAAe,cAK/D4C,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAC,wBACvEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,mCAAkCC,SAAC,YAErDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gDAInBE,EAAAA,EAAAA,MAAA,OAAAD,SAAA,EACEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,yCAAwCC,SAAA,EACrDF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,uDAAsDC,SAAC,2BACvEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,oCAAmCC,SAAC,YAEtDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAAsDC,UACnEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,uDAOvBE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8FAA6FC,SAAA,EAC1GC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,2DAA0DC,SAAA,EACtEF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,yDACZ9B,EAAE,mBAAoB,yBAGzBgC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kEAAiEC,SAAA,EAC9EF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,4CACbE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D/B,EAAE,wBAAyB,iCAE9B6B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAC,yBAI5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,oEAAmEC,SAAA,EAChFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,6CACbE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D/B,EAAE,sBAAuB,mCAE5B6B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAC,0BAI5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CACbE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D/B,EAAE,iBAAkB,2CAEvB6B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAC,sBAI5DC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sEAAqEC,SAAA,EAClFF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0CACbE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,SAAQC,SAAA,EACrBF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oDAAmDC,SAC7D/B,EAAE,aAAc,sCAEnB6B,EAAAA,EAAAA,KAAA,KAAGC,UAAU,2CAA0CC,SAAC,gCAQlEC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BC,SAAA,EACxCC,EAAAA,EAAAA,MAAA,UACEG,QAASA,KAAMT,EAAAA,EAAAA,IAAM,oCACrBI,UAAU,kFAAiFC,SAAA,EAE3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,oBACZ9B,EAAE,kBAAmB,wBAGxBgC,EAAAA,EAAAA,MAAA,UACEG,QAASA,KAAMT,EAAAA,EAAAA,IAAM,2BACrBI,UAAU,oFAAmFC,SAAA,EAE7FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,wBACZ9B,EAAE,aAAc,mBAGnBgC,EAAAA,EAAAA,MAAA,UACEG,QAASA,KAAMT,EAAAA,EAAAA,IAAM,mCACrBI,UAAU,sFAAqFC,SAAA,EAE/FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,0BACZ9B,EAAE,mBAAoB,yBAGzBgC,EAAAA,EAAAA,MAAA,UACEG,QAAS9B,EACTyB,UAAU,kFAAiFC,SAAA,EAE3FF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,qBACZ9B,EAAE,cAAe,yB", "sources": ["pages/Integration/IntegrationDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useAuth } from '../../contexts/AuthContext';\nimport toast from 'react-hot-toast';\n\nconst IntegrationDashboard = () => {\n  const [integrationStats, setIntegrationStats] = useState({\n    nphies: {\n      status: 'disconnected',\n      totalClaims: 0,\n      successfulClaims: 0,\n      eligibilityChecks: 0,\n      lastSync: null\n    },\n    zatca: {\n      status: 'disconnected',\n      totalInvoices: 0,\n      successfulSubmissions: 0,\n      complianceScore: 0,\n      lastSync: null\n    },\n    electronicSignatures: {\n      totalDocuments: 0,\n      pendingSignatures: 0,\n      completedSignatures: 0,\n      expiredDocuments: 0\n    },\n    visaIntegration: {\n      linkedVisas: 0,\n      verifiedVisas: 0,\n      expiredVisas: 0\n    }\n  });\n  const [loading, setLoading] = useState(true);\n  const { t } = useTranslation();\n  const { user } = useAuth();\n\n  useEffect(() => {\n    loadIntegrationStats();\n  }, []);\n\n  const loadIntegrationStats = async () => {\n    setLoading(true);\n    try {\n      // Load NPHIES stats\n      const nphiesResponse = await fetch('/api/v1/nphies/stats', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      // Load ZATCA stats\n      const zatcaResponse = await fetch('/api/v1/zatca/compliance', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      // Load Electronic Signature stats\n      const signatureResponse = await fetch('/api/v1/signature/stats', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      const stats = { ...integrationStats };\n\n      if (nphiesResponse.ok) {\n        const nphiesData = await nphiesResponse.json();\n        stats.nphies = {\n          status: 'connected',\n          ...nphiesData.data\n        };\n      }\n\n      if (zatcaResponse.ok) {\n        const zatcaData = await zatcaResponse.json();\n        stats.zatca = {\n          status: 'connected',\n          totalInvoices: zatcaData.data.totalInvoicesSubmitted,\n          successfulSubmissions: zatcaData.data.successfulSubmissions,\n          complianceScore: zatcaData.data.overallCompliance,\n          lastSync: zatcaData.data.lastSyncDate\n        };\n      }\n\n      // Mock electronic signature stats (replace with actual API call)\n      stats.electronicSignatures = {\n        totalDocuments: 45,\n        pendingSignatures: 8,\n        completedSignatures: 37,\n        expiredDocuments: 2\n      };\n\n      // Mock visa integration stats (replace with actual API call)\n      stats.visaIntegration = {\n        linkedVisas: 23,\n        verifiedVisas: 21,\n        expiredVisas: 2\n      };\n\n      setIntegrationStats(stats);\n    } catch (error) {\n      console.error('Error loading integration stats:', error);\n      // In development mode, use mock data without showing error\n      if (process.env.NODE_ENV === 'development') {\n        console.log('Using mock integration data for development');\n        const mockStats = {\n          nphies: {\n            status: 'disconnected',\n            lastSync: null,\n            totalClaims: 0,\n            pendingClaims: 0,\n            approvedClaims: 0,\n            rejectedClaims: 0\n          },\n          zatca: {\n            status: 'disconnected',\n            lastSync: null,\n            totalInvoices: 0,\n            pendingInvoices: 0,\n            approvedInvoices: 0,\n            rejectedInvoices: 0\n          },\n          signatures: {\n            totalTemplates: 0,\n            activeTemplates: 0\n          },\n          electronicSignatures: {\n            totalDocuments: 0,\n            signedDocuments: 0,\n            pendingSignatures: 0,\n            rejectedSignatures: 0\n          }\n        };\n        setIntegrationStats(mockStats);\n      } else {\n        toast.error('Failed to load integration statistics');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'connected':\n        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';\n      case 'warning':\n        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';\n      case 'disconnected':\n        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'connected':\n        return 'fas fa-check-circle';\n      case 'warning':\n        return 'fas fa-exclamation-triangle';\n      case 'disconnected':\n        return 'fas fa-times-circle';\n      default:\n        return 'fas fa-question-circle';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 max-w-7xl mx-auto\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n          {t('integrationDashboard', 'Integration Dashboard')}\n        </h1>\n        <p className=\"text-gray-600 dark:text-gray-400 mt-2\">\n          {t('integrationDashboardDesc', 'Monitor and manage Saudi regulatory integrations including NPHIES, ZATCA, and electronic signatures')}\n        </p>\n      </div>\n\n      {/* Overview Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n        {/* NPHIES Card */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <i className=\"fas fa-shield-alt text-2xl text-blue-600 dark:text-blue-400 mr-3\"></i>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                NPHIES\n              </h3>\n            </div>\n            <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(integrationStats.nphies.status)}`}>\n              <i className={`${getStatusIcon(integrationStats.nphies.status)} mr-1`}></i>\n              {t(integrationStats.nphies.status, integrationStats.nphies.status)}\n            </span>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('totalClaims', 'Total Claims')}</span>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{integrationStats.nphies.totalClaims}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('successful', 'Successful')}</span>\n              <span className=\"text-sm font-medium text-green-600\">{integrationStats.nphies.successfulClaims}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('eligibilityChecks', 'Eligibility Checks')}</span>\n              <span className=\"text-sm font-medium text-blue-600\">{integrationStats.nphies.eligibilityChecks}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* ZATCA Card */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <i className=\"fas fa-receipt text-2xl text-green-600 dark:text-green-400 mr-3\"></i>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                ZATCA\n              </h3>\n            </div>\n            <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(integrationStats.zatca.status)}`}>\n              <i className={`${getStatusIcon(integrationStats.zatca.status)} mr-1`}></i>\n              {t(integrationStats.zatca.status, integrationStats.zatca.status)}\n            </span>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('totalInvoices', 'Total Invoices')}</span>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{integrationStats.zatca.totalInvoices}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('successful', 'Successful')}</span>\n              <span className=\"text-sm font-medium text-green-600\">{integrationStats.zatca.successfulSubmissions}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('compliance', 'Compliance')}</span>\n              <span className=\"text-sm font-medium text-blue-600\">{integrationStats.zatca.complianceScore}%</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Electronic Signatures Card */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <i className=\"fas fa-signature text-2xl text-purple-600 dark:text-purple-400 mr-3\"></i>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('eSignatures', 'E-Signatures')}\n              </h3>\n            </div>\n            <span className=\"px-2 py-1 rounded text-xs font-medium text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400\">\n              <i className=\"fas fa-check-circle mr-1\"></i>\n              {t('active', 'Active')}\n            </span>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('totalDocuments', 'Total Documents')}</span>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{integrationStats.electronicSignatures.totalDocuments}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('pending', 'Pending')}</span>\n              <span className=\"text-sm font-medium text-yellow-600\">{integrationStats.electronicSignatures.pendingSignatures}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('completed', 'Completed')}</span>\n              <span className=\"text-sm font-medium text-green-600\">{integrationStats.electronicSignatures.completedSignatures}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Visa Integration Card */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center\">\n              <i className=\"fas fa-passport text-2xl text-orange-600 dark:text-orange-400 mr-3\"></i>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {t('visaIntegration', 'Visa Integration')}\n              </h3>\n            </div>\n            <span className=\"px-2 py-1 rounded text-xs font-medium text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400\">\n              <i className=\"fas fa-check-circle mr-1\"></i>\n              {t('active', 'Active')}\n            </span>\n          </div>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('linkedVisas', 'Linked Visas')}</span>\n              <span className=\"text-sm font-medium text-gray-900 dark:text-white\">{integrationStats.visaIntegration.linkedVisas}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('verified', 'Verified')}</span>\n              <span className=\"text-sm font-medium text-green-600\">{integrationStats.visaIntegration.verifiedVisas}</span>\n            </div>\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">{t('expired', 'Expired')}</span>\n              <span className=\"text-sm font-medium text-red-600\">{integrationStats.visaIntegration.expiredVisas}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Detailed Sections */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Compliance Overview */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            <i className=\"fas fa-chart-line text-blue-600 dark:text-blue-400 mr-2\"></i>\n            {t('complianceOverview', 'Compliance Overview')}\n          </h3>\n          \n          <div className=\"space-y-4\">\n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">ZATCA Compliance</span>\n                <span className=\"text-sm font-bold text-blue-600\">{integrationStats.zatca.complianceScore}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div \n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${integrationStats.zatca.complianceScore}%` }}\n                ></div>\n              </div>\n            </div>\n            \n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">NPHIES Integration</span>\n                <span className=\"text-sm font-bold text-green-600\">95%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div className=\"bg-green-600 h-2 rounded-full w-[95%]\"></div>\n              </div>\n            </div>\n            \n            <div>\n              <div className=\"flex justify-between items-center mb-2\">\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Electronic Signatures</span>\n                <span className=\"text-sm font-bold text-purple-600\">88%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div className=\"bg-purple-600 h-2 rounded-full w-[88%]\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Activities */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            <i className=\"fas fa-clock text-green-600 dark:text-green-400 mr-2\"></i>\n            {t('recentActivities', 'Recent Activities')}\n          </h3>\n          \n          <div className=\"space-y-3\">\n            <div className=\"flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n              <i className=\"fas fa-file-invoice text-blue-600 mr-3\"></i>\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('invoiceSubmittedZatca', 'Invoice submitted to ZATCA')}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">2 minutes ago</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n              <i className=\"fas fa-check-circle text-green-600 mr-3\"></i>\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('eligibilityVerified', 'Patient eligibility verified')}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">15 minutes ago</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg\">\n              <i className=\"fas fa-signature text-purple-600 mr-3\"></i>\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('documentSigned', 'Treatment plan signed electronically')}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">1 hour ago</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg\">\n              <i className=\"fas fa-passport text-orange-600 mr-3\"></i>\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {t('visaLinked', 'Visa linked to patient services')}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400\">3 hours ago</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"mt-8 flex flex-wrap gap-4\">\n        <button\n          onClick={() => toast('NPHIES configuration coming soon')}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center\"\n        >\n          <i className=\"fas fa-cog mr-2\"></i>\n          {t('configureNphies', 'Configure NPHIES')}\n        </button>\n        \n        <button\n          onClick={() => toast('ZATCA setup coming soon')}\n          className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center\"\n        >\n          <i className=\"fas fa-receipt mr-2\"></i>\n          {t('setupZatca', 'Setup ZATCA')}\n        </button>\n        \n        <button\n          onClick={() => toast('Signature templates coming soon')}\n          className=\"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center\"\n        >\n          <i className=\"fas fa-signature mr-2\"></i>\n          {t('manageSignatures', 'Manage Signatures')}\n        </button>\n        \n        <button\n          onClick={loadIntegrationStats}\n          className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center\"\n        >\n          <i className=\"fas fa-sync mr-2\"></i>\n          {t('refreshData', 'Refresh Data')}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default IntegrationDashboard;\n"], "names": ["IntegrationDashboard", "integrationStats", "setIntegrationStats", "useState", "nphies", "status", "totalClaims", "successfulClaims", "eligibility<PERSON><PERSON><PERSON>", "lastSync", "zatca", "totalInvoices", "successfulSubmissions", "complianceScore", "electronicSignatures", "totalDocuments", "pendingSignatures", "completedSignatures", "expiredDocuments", "visaIntegration", "linkedVisas", "verifiedVisas", "expiredVisas", "loading", "setLoading", "t", "useTranslation", "user", "useAuth", "useEffect", "loadIntegrationStats", "async", "nphiesResponse", "fetch", "headers", "concat", "localStorage", "getItem", "zatcaResponse", "stats", "_objectSpread", "ok", "nphiesData", "json", "data", "zatcaData", "totalInvoicesSubmitted", "overallCompliance", "lastSyncDate", "error", "console", "toast", "getStatusColor", "getStatusIcon", "_jsx", "className", "children", "_jsxs", "style", "width", "onClick"], "sourceRoot": ""}