{"short_name": "PhysioFlow", "name": "PhysioFlow - Physical Therapy Management System", "description": "PhysioFlow: Empowering Recovery, Enhancing Lives. Comprehensive PT management with AI integration and special needs focus.", "icons": [{"src": "favicon.ico", "sizes": "64x64 32x32 24x24 16x16", "type": "image/x-icon"}, {"src": "logo192.png", "type": "image/png", "sizes": "192x192"}, {"src": "logo512.png", "type": "image/png", "sizes": "512x512"}], "start_url": ".", "display": "standalone", "theme_color": "#0ea5e9", "background_color": "#ffffff", "orientation": "portrait-primary", "scope": "/", "lang": "en", "dir": "ltr", "categories": ["medical", "healthcare", "productivity"], "screenshots": [{"src": "screenshot-wide.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide"}, {"src": "screenshot-narrow.png", "sizes": "750x1334", "type": "image/png", "form_factor": "narrow"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "View main dashboard", "url": "/dashboard", "icons": [{"src": "icon-dashboard.png", "sizes": "96x96"}]}, {"name": "Patients", "short_name": "Patients", "description": "Manage patients", "url": "/patients", "icons": [{"src": "icon-patients.png", "sizes": "96x96"}]}, {"name": "Appointments", "short_name": "Appointments", "description": "Schedule appointments", "url": "/appointments", "icons": [{"src": "icon-appointments.png", "sizes": "96x96"}]}, {"name": "New Patient", "short_name": "New Patient", "description": "Add new patient", "url": "/patients/new", "icons": [{"src": "icon-new-patient.png", "sizes": "96x96"}]}]}