{"name": "physioflow-frontend", "version": "1.0.0", "description": "PhysioFlow - Empowering Recovery, Enhancing Lives. A comprehensive physical therapy management system with AI integration and special needs focus.", "author": "PhysioFlow Technologies", "license": "MIT", "homepage": "https://physioflow.com", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "date-fns": "^2.30.0", "i18next": "^23.2.3", "postcss-flexbugs-fixes": "^4.0.0", "postcss-normalize": "^8.0.1", "postcss-preset-env": "^6.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.1", "react-hot-toast": "^2.4.1", "react-i18next": "^13.0.1", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "recharts": "^2.15.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,json,css,md}", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js", "serve": "serve -s build -l 3000"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@types/react-modal": "^3.16.0", "@types/react-table": "^7.7.14", "eslint": "^8.44.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "postcss": "^8.5.6", "prettier": "^3.0.0", "serve": "^14.2.0", "typescript": "^4.9.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["healthcare", "physical-therapy", "patient-management", "saudi-arabia", "medical-software", "react", "typescript"], "repository": {"type": "git", "url": "https://github.com/physioflow/physioflow.git"}, "bugs": {"url": "https://github.com/pt-system/frontend/issues"}}