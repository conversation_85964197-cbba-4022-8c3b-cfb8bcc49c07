# Physical Therapist System - Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the Physical Therapist System in Saudi Arabia, ensuring compliance with local healthcare regulations.

## Prerequisites

### System Requirements
- Docker 20.10+ and Docker Compose 2.0+
- Node.js 18+ (for development)
- PostgreSQL 15+ (if not using Docker)
- Redis 7+ (if not using Docker)
- Minimum 4GB RAM, 20GB storage
- SSL certificate for production deployment

### Saudi Arabia Compliance Requirements
- PDPL (Personal Data Protection Law) compliance
- NPHIES integration credentials
- SeHE (Saudi Health Information Exchange) API access
- Saudi Health Council registration

## Environment Configuration

### 1. Create Environment File
Create a `.env` file in the project root:

```bash
# Database Configuration
DB_PASSWORD=your_secure_database_password
DB_NAME=pt_system
DB_USER=postgres
DB_HOST=postgres
DB_PORT=5432

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# AI Service Configuration
OPENAI_API_KEY=your_openai_api_key

# NPHIES Integration (Saudi Arabia)
NPHIES_BASE_URL=https://api.nphies.sa
NPHIES_CLIENT_ID=your_nphies_client_id
NPHIES_CLIENT_SECRET=your_nphies_client_secret

# SeHE Integration (Saudi Health Information Exchange)
SEHE_BASE_URL=https://api.sehe.sa
SEHE_API_KEY=your_sehe_api_key

# Application Configuration
NODE_ENV=production
ALLOWED_ORIGINS=https://yourdomain.com
```

### 2. SSL Configuration
For production deployment, ensure you have:
- Valid SSL certificate
- Domain name configured
- Firewall rules allowing HTTPS traffic

## Deployment Options

### Option 1: Docker Compose (Recommended)

1. **Clone and Navigate to Project**
```bash
git clone <repository-url>
cd PT
```

2. **Configure Environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Build and Start Services**
```bash
cd backend/docker
docker-compose up -d
```

4. **Verify Deployment**
```bash
# Check all services are running
docker-compose ps

# Check logs
docker-compose logs -f

# Test health endpoints
curl http://localhost:3001/health  # Patient Service
curl http://localhost:3002/health  # Form Service
curl http://localhost:3004/health  # AI Service
curl http://localhost:3006/health  # Auth Service
```

### Option 2: Kubernetes Deployment

1. **Create Kubernetes Manifests**
```bash
# Apply database and Redis
kubectl apply -f k8s/postgres.yaml
kubectl apply -f k8s/redis.yaml

# Apply microservices
kubectl apply -f k8s/patient-service.yaml
kubectl apply -f k8s/form-service.yaml
kubectl apply -f k8s/ai-service.yaml
kubectl apply -f k8s/auth-service.yaml

# Apply ingress
kubectl apply -f k8s/ingress.yaml
```

2. **Configure Secrets**
```bash
kubectl create secret generic pt-secrets \
  --from-literal=db-password=your_password \
  --from-literal=jwt-secret=your_jwt_secret \
  --from-literal=openai-api-key=your_openai_key
```

### Option 3: Cloud Deployment (AWS/Azure/GCP)

#### AWS Deployment
1. **Use AWS ECS or EKS**
2. **Configure RDS for PostgreSQL**
3. **Use ElastiCache for Redis**
4. **Set up Application Load Balancer**
5. **Configure Route 53 for DNS**

#### Azure Deployment
1. **Use Azure Container Instances or AKS**
2. **Configure Azure Database for PostgreSQL**
3. **Use Azure Cache for Redis**
4. **Set up Application Gateway**

## Database Setup

### 1. Initialize Database
```bash
# Connect to PostgreSQL
psql -h localhost -U postgres -d pt_system

# Run schema creation
\i backend/shared/database/schema.sql

# Verify tables created
\dt
```

### 2. Create Initial Admin User
```sql
INSERT INTO users (
    id, username, email, password_hash, first_name, last_name,
    role, is_active
) VALUES (
    uuid_generate_v4(),
    'admin',
    '<EMAIL>',
    '$2b$12$encrypted_password_hash',
    'System',
    'Administrator',
    'admin',
    true
);
```

### 3. Load Form Templates
```bash
# Run the form template seeder
node backend/scripts/seedFormTemplates.js
```

## Security Configuration

### 1. Firewall Rules
```bash
# Allow only necessary ports
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 5432   # PostgreSQL (internal only)
ufw deny 6379   # Redis (internal only)
```

### 2. SSL/TLS Configuration
```nginx
# Nginx SSL configuration
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

### 3. Database Security
```sql
-- Create application-specific database user
CREATE USER pt_app WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE pt_system TO pt_app;
GRANT USAGE ON SCHEMA public TO pt_app;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO pt_app;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO pt_app;
```

## Monitoring and Logging

### 1. Health Checks
Set up monitoring for all service health endpoints:
- Patient Service: `http://localhost:3001/health`
- Form Service: `http://localhost:3002/health`
- AI Service: `http://localhost:3004/health`
- Auth Service: `http://localhost:3006/health`

### 2. Log Aggregation
Configure centralized logging:
```yaml
# docker-compose.override.yml
version: '3.8'
services:
  patient-service:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 3. Performance Monitoring
- Monitor database performance
- Track API response times
- Monitor memory and CPU usage
- Set up alerts for critical issues

## Backup and Recovery

### 1. Database Backup
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U postgres pt_system > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### 2. Application Data Backup
```bash
# Backup uploaded files and configurations
tar -czf app_backup_$DATE.tar.gz /app/uploads /app/config
```

## Compliance and Auditing

### 1. PDPL Compliance
- Ensure data encryption at rest and in transit
- Implement data retention policies
- Configure audit logging for all data access
- Set up data anonymization for analytics

### 2. Audit Trail Configuration
```sql
-- Enable audit logging
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (
        user_id, action, table_name, record_id,
        old_values, new_values, ip_address, created_at
    ) VALUES (
        current_setting('app.current_user_id', true)::uuid,
        TG_OP,
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
        current_setting('app.client_ip', true),
        CURRENT_TIMESTAMP
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
```bash
# Check PostgreSQL status
docker-compose logs postgres

# Test connection
psql -h localhost -U postgres -d pt_system -c "SELECT 1;"
```

2. **Service Communication Issues**
```bash
# Check network connectivity
docker network ls
docker network inspect pt_network
```

3. **Performance Issues**
```bash
# Monitor resource usage
docker stats
htop
```

### Support Contacts
- Technical Support: <EMAIL>
- Compliance Questions: <EMAIL>
- Emergency Contact: +966-XXX-XXXX

## Maintenance

### Regular Tasks
- Weekly database maintenance and optimization
- Monthly security updates
- Quarterly compliance audits
- Annual penetration testing

### Update Procedure
1. Backup current system
2. Test updates in staging environment
3. Schedule maintenance window
4. Deploy updates with rollback plan
5. Verify all services are operational
6. Update documentation
