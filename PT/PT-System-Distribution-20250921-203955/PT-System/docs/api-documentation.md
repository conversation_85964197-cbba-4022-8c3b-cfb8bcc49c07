# Physical Therapist System - API Documentation

## Overview
This document provides comprehensive API documentation for the Physical Therapist System microservices, designed for healthcare providers in Saudi Arabia.

## Base URLs
- **Patient Management Service**: `http://localhost:3001/api`
- **Form Management Service**: `http://localhost:3002/api`
- **AI Service**: `http://localhost:3004/api`
- **Authentication Service**: `http://localhost:3006/api`

## Authentication
All API endpoints (except login/register) require JWT authentication.

### Headers
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept-Language: en|ar
```

## Authentication Service (`/api/auth`)

### POST /auth/login
Authenticate user and receive JWT token.

**Request Body:**
```json
{
  "username": "string",
  "password": "string",
  "remember_me": false
}
```

**Response:**
```json
{
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "first_name": "string",
    "last_name": "string",
    "role": "admin|doctor|therapist|nurse|receptionist"
  },
  "expires_in": "24h"
}
```

### POST /auth/register
Register new user (admin only).

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "first_name": "string",
  "last_name": "string",
  "first_name_ar": "string",
  "last_name_ar": "string",
  "role": "admin|doctor|therapist|nurse|receptionist",
  "license_number": "string",
  "specialization": "string",
  "phone": "string"
}
```

### POST /auth/logout
Logout user and invalidate session.

### POST /auth/verify
Verify JWT token validity.

### POST /auth/change-password
Change user password.

**Request Body:**
```json
{
  "current_password": "string",
  "new_password": "string"
}
```

## Patient Management Service (`/api/patients`)

### GET /patients
Get paginated list of patients with search functionality.

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `search`: Search term for name, ID, or MRN
- `language`: Response language (en|ar)

**Response:**
```json
{
  "patients": [
    {
      "id": "uuid",
      "national_id": "string",
      "medical_record_number": "string",
      "full_name": "string",
      "first_name": "string",
      "last_name": "string",
      "date_of_birth": "date",
      "gender": "male|female",
      "phone": "string",
      "email": "string",
      "insurance_provider": "string",
      "preferred_language": "ar|en",
      "is_active": true,
      "created_at": "timestamp"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  }
}
```

### GET /patients/:id
Get specific patient details.

**Response:**
```json
{
  "id": "uuid",
  "national_id": "string",
  "medical_record_number": "string",
  "first_name": "string",
  "last_name": "string",
  "first_name_ar": "string",
  "last_name_ar": "string",
  "date_of_birth": "date",
  "gender": "male|female",
  "phone": "string",
  "email": "string",
  "emergency_contact_name": "string",
  "emergency_contact_phone": "string",
  "address": "string",
  "city": "string",
  "region": "string",
  "postal_code": "string",
  "insurance_provider": "string",
  "insurance_number": "string",
  "insurance_expiry": "date",
  "preferred_language": "ar|en",
  "is_active": true,
  "created_at": "timestamp",
  "updated_at": "timestamp"
}
```

### POST /patients
Create new patient.

**Request Body:**
```json
{
  "national_id": "string",
  "first_name": "string",
  "last_name": "string",
  "first_name_ar": "string",
  "last_name_ar": "string",
  "date_of_birth": "date",
  "gender": "male|female",
  "phone": "string",
  "email": "string",
  "emergency_contact_name": "string",
  "emergency_contact_phone": "string",
  "address": "string",
  "city": "string",
  "region": "string",
  "postal_code": "string",
  "insurance_provider": "string",
  "insurance_number": "string",
  "insurance_expiry": "date",
  "preferred_language": "ar|en"
}
```

### PUT /patients/:id
Update patient information.

### DELETE /patients/:id
Soft delete patient (sets is_active to false).

## Medical History Endpoints

### GET /medical-history/:patient_id
Get patient's medical history.

### POST /medical-history
Add medical history entry.

**Request Body:**
```json
{
  "patient_id": "uuid",
  "condition_name": "string",
  "condition_name_ar": "string",
  "icd_10_code": "string",
  "diagnosis_date": "date",
  "status": "active|resolved|chronic",
  "notes": "string"
}
```

## Allergies Endpoints

### GET /allergies/:patient_id
Get patient's allergies.

### POST /allergies
Add allergy entry.

**Request Body:**
```json
{
  "patient_id": "uuid",
  "allergen": "string",
  "allergen_ar": "string",
  "reaction": "string",
  "severity": "mild|moderate|severe"
}
```

## Form Management Service (`/api/form-templates`, `/api/form-submissions`)

### GET /form-templates
Get available form templates.

**Response:**
```json
{
  "templates": [
    {
      "id": "uuid",
      "name": "string",
      "name_ar": "string",
      "description": "string",
      "form_type": "string",
      "version": "string",
      "schema": {
        "sections": [
          {
            "id": "string",
            "title": "string",
            "title_ar": "string",
            "fields": [
              {
                "id": "string",
                "type": "text|textarea|select|date|number|checkbox",
                "label": "string",
                "label_ar": "string",
                "required": true,
                "options": ["array of options for select fields"]
              }
            ]
          }
        ]
      },
      "is_active": true,
      "created_at": "timestamp"
    }
  ]
}
```

### GET /form-templates/:id
Get specific form template.

### POST /form-templates
Create new form template (admin only).

### GET /form-submissions
Get form submissions with filtering.

**Query Parameters:**
- `patient_id`: Filter by patient
- `template_id`: Filter by form template
- `status`: Filter by status (draft|submitted|approved|rejected)
- `page`: Page number
- `limit`: Items per page

### POST /form-submissions
Submit new form.

**Request Body:**
```json
{
  "template_id": "uuid",
  "patient_id": "uuid",
  "form_data": {
    "field_id": "value",
    "another_field": "another_value"
  },
  "status": "draft|submitted"
}
```

### PUT /form-submissions/:id
Update form submission.

### PUT /form-submissions/:id/approve
Approve form submission (doctor/admin only).

### PUT /form-submissions/:id/reject
Reject form submission with reason.

## AI Service (`/api/ai`)

### POST /ai/auto-fill/demographics
Auto-fill patient demographics based on National ID.

**Request Body:**
```json
{
  "national_id": "string",
  "language": "en|ar"
}
```

### POST /ai/auto-fill/medical-history
Get AI suggestions for medical history based on diagnosis.

**Request Body:**
```json
{
  "diagnosis": "string",
  "patient_id": "uuid",
  "language": "en|ar"
}
```

**Response:**
```json
{
  "similar_cases": [
    {
      "condition_name": "string",
      "icd_10_code": "string",
      "frequency": 5
    }
  ],
  "ai_suggestions": {
    "related_conditions": ["array of conditions"],
    "treatment_approaches": ["array of treatments"],
    "expected_duration": "string",
    "complications": ["array of potential complications"]
  }
}
```

### POST /ai/auto-fill/treatment-goals
Generate treatment goals based on patient profile.

**Request Body:**
```json
{
  "diagnosis": "string",
  "patient_age": 35,
  "patient_gender": "male|female",
  "functional_level": "low|moderate|high",
  "language": "en|ar"
}
```

### POST /ai/auto-fill/form-fields
Auto-fill form fields based on previous submissions.

**Request Body:**
```json
{
  "form_type": "string",
  "patient_id": "uuid",
  "partial_data": {},
  "language": "en|ar"
}
```

### POST /ai/nlp/summarize
Summarize clinical notes using NLP.

**Request Body:**
```json
{
  "text": "string",
  "language": "en|ar",
  "max_length": 200
}
```

### POST /ai/nlp/translate
Translate text between Arabic and English.

**Request Body:**
```json
{
  "text": "string",
  "source_language": "en|ar",
  "target_language": "en|ar"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional details if available"
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request (validation error)
- `401`: Unauthorized (invalid/missing token)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found
- `409`: Conflict (duplicate resource)
- `429`: Too Many Requests (rate limited)
- `500`: Internal Server Error

## Rate Limiting
- Authentication endpoints: 5 requests per 15 minutes per IP
- General API endpoints: 100 requests per minute per user
- AI endpoints: 20 requests per minute per user

## Pagination
All list endpoints support pagination:
- Default page size: 20 items
- Maximum page size: 100 items
- Response includes pagination metadata

## Internationalization
- All text fields support both English and Arabic
- Use `Accept-Language` header to specify preferred language
- Arabic text fields use `_ar` suffix (e.g., `name_ar`)
- RTL support for Arabic content

## Compliance Notes
- All API calls are logged for audit purposes
- Patient data access requires appropriate permissions
- PDPL compliance enforced through data encryption and access controls
- Integration with NPHIES and SeHE follows Saudi healthcare standards
