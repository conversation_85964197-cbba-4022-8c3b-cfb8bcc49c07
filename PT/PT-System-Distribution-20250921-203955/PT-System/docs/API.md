# PhysioFlow API Documentation

## Overview

The PhysioFlow API provides comprehensive endpoints for managing physical therapy operations, patient data, AI integration, and communication systems. Built with RESTful principles and designed for healthcare compliance.

## Base URL

```
Production: https://api.physioflow.com/v1
Development: http://localhost:5000/api/v1
```

## Authentication

All API requests require authentication using JWT tokens.

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_id",
      "name": "Dr. <PERSON>",
      "email": "<EMAIL>",
      "role": "therapist"
    }
  }
}
```

### Headers
```http
Authorization: Bearer <token>
Content-Type: application/json
Accept-Language: en|ar
```

## Patients API

### Get All Patients
```http
GET /patients?page=1&limit=20&search=ahmed&status=active
```

### Get Patient by ID
```http
GET /patients/{id}
```

### Create Patient
```http
POST /patients
Content-Type: application/json

{
  "firstName": "Ahmed",
  "lastName": "Mohammed",
  "firstNameAr": "أحمد",
  "lastNameAr": "محمد",
  "dateOfBirth": "1990-01-15",
  "gender": "male",
  "phone": "+966501234567",
  "email": "<EMAIL>",
  "nationalId": "**********",
  "address": "Riyadh, Saudi Arabia",
  "emergencyContact": {
    "name": "Fatima Mohammed",
    "phone": "+966507654321",
    "relationship": "mother"
  },
  "medicalHistory": {
    "conditions": ["cerebral_palsy"],
    "medications": ["baclofen"],
    "allergies": ["penicillin"]
  }
}
```

### Update Patient
```http
PUT /patients/{id}
```

### Delete Patient
```http
DELETE /patients/{id}
```

## Assessments API

### PT Adult Initial Assessment
```http
POST /assessments/pt-initial
Content-Type: application/json

{
  "patientId": "patient_id",
  "assessmentData": {
    "chiefComplaint": "Lower back pain",
    "historyOfPresentIllness": "...",
    "painAssessment": {
      "regions": ["lowerBack"],
      "painLevels": {"lowerBack": 7},
      "painType": "sharp"
    },
    "functionalAssessment": {
      "mobility": "limited",
      "transfers": "moderate_assistance",
      "balance": "impaired"
    }
  }
}
```

### Body Map Assessment
```http
POST /assessments/body-map
Content-Type: application/json

{
  "patientId": "patient_id",
  "selectedRegions": ["lowerBack", "leftKnee"],
  "painLevels": {
    "lowerBack": 7,
    "leftKnee": 4
  },
  "painDescriptions": {
    "lowerBack": {
      "type": "sharp",
      "duration": "chronic",
      "frequency": "constant"
    }
  }
}
```

## Exercise Library API

### Get Exercises
```http
GET /exercises?category=strength&difficulty=beginner&bodyPart=upper_body&language=en
```

### Get Exercise by ID
```http
GET /exercises/{id}?language=en
```

### Create Exercise Program
```http
POST /exercise-programs
Content-Type: application/json

{
  "patientId": "patient_id",
  "name": "Upper Body Strengthening",
  "description": "Program for shoulder rehabilitation",
  "duration": "4",
  "frequency": "daily",
  "exercises": [
    {
      "exerciseId": "exercise_id",
      "sets": 3,
      "reps": 10,
      "duration": "30 seconds",
      "restTime": "30 seconds",
      "notes": "Focus on proper form"
    }
  ]
}
```

## Communication API

### Send Message
```http
POST /communication/send
Content-Type: application/json

{
  "patientId": "patient_id",
  "channels": ["email", "sms", "whatsapp"],
  "message": "Your appointment is tomorrow at 2:00 PM",
  "subject": "Appointment Reminder",
  "urgency": "normal",
  "scheduledTime": "2024-01-15T10:00:00Z"
}
```

### Get Communication History
```http
GET /communication/history/{patientId}?page=1&limit=20
```

### Update Communication Preferences
```http
PUT /patients/{id}/communication-preferences
Content-Type: application/json

{
  "preferredChannels": ["email", "whatsapp"],
  "language": "en",
  "quietHours": {
    "start": "22:00",
    "end": "08:00"
  },
  "emailFrequency": "all",
  "smsFrequency": "urgent"
}
```

## AI Integration API

### AI Assistant Query
```http
POST /ai/query
Content-Type: application/json

{
  "prompt": "Provide treatment recommendations for a patient with cerebral palsy",
  "context": {
    "patientId": "patient_id",
    "language": "en"
  },
  "provider": "gemini"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "response": "Based on the patient's condition...",
    "provider": "gemini",
    "usage": {
      "promptTokens": 150,
      "completionTokens": 300,
      "totalTokens": 450
    }
  }
}
```

### Treatment Recommendations
```http
POST /ai/treatment-recommendations
Content-Type: application/json

{
  "patientId": "patient_id",
  "symptoms": "Lower back pain, limited mobility",
  "assessmentData": {...}
}
```

### Exercise Modifications
```http
POST /ai/exercise-modifications
Content-Type: application/json

{
  "exerciseId": "exercise_id",
  "patientLimitations": "Limited shoulder range of motion",
  "patientId": "patient_id"
}
```

### Progress Analysis
```http
POST /ai/progress-analysis
Content-Type: application/json

{
  "patientId": "patient_id",
  "progressData": {
    "sessions": [...],
    "assessments": [...],
    "exercises": [...]
  }
}
```

## Analytics API

### Patient Analytics
```http
GET /analytics/patients/{id}?startDate=2024-01-01&endDate=2024-01-31
```

### Clinic Performance
```http
GET /analytics/clinic?period=monthly&year=2024
```

### AI Usage Analytics
```http
GET /analytics/ai?startDate=2024-01-01&endDate=2024-01-31
```

## Compliance API

### CARF Compliance Report
```http
GET /compliance/carf/report?startDate=2024-01-01&endDate=2024-01-31
```

### CBAHI Quality Measures
```http
GET /compliance/cbahi/quality-measures?period=quarterly&year=2024
```

### Audit Trail
```http
GET /compliance/audit-trail?userId=user_id&action=patient_access&startDate=2024-01-01
```

## Error Handling

All API endpoints return standardized error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "message": "Invalid email format"
    }
  }
}
```

### Error Codes

- `VALIDATION_ERROR`: Input validation failed
- `AUTHENTICATION_ERROR`: Invalid or missing authentication
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_ERROR`: Server error

## Rate Limiting

- **Standard endpoints**: 100 requests per minute
- **AI endpoints**: 20 requests per minute
- **File uploads**: 10 requests per minute

## Webhooks

### Patient Events
```http
POST /webhooks/patients
Content-Type: application/json

{
  "event": "patient.created",
  "data": {
    "patientId": "patient_id",
    "timestamp": "2024-01-15T10:00:00Z"
  }
}
```

### Assessment Events
```http
POST /webhooks/assessments
Content-Type: application/json

{
  "event": "assessment.completed",
  "data": {
    "assessmentId": "assessment_id",
    "patientId": "patient_id",
    "type": "pt_initial",
    "timestamp": "2024-01-15T10:00:00Z"
  }
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const PhysioFlowAPI = require('@physioflow/api-client');

const client = new PhysioFlowAPI({
  apiKey: 'your-api-key',
  baseURL: 'https://api.physioflow.com/v1'
});

// Get patient
const patient = await client.patients.get('patient_id');

// Create assessment
const assessment = await client.assessments.create({
  type: 'pt_initial',
  patientId: 'patient_id',
  data: {...}
});

// AI query
const aiResponse = await client.ai.query({
  prompt: 'Treatment recommendations',
  context: { patientId: 'patient_id' }
});
```

### Python
```python
from physioflow import PhysioFlowClient

client = PhysioFlowClient(
    api_key='your-api-key',
    base_url='https://api.physioflow.com/v1'
)

# Get patient
patient = client.patients.get('patient_id')

# Create assessment
assessment = client.assessments.create(
    type='pt_initial',
    patient_id='patient_id',
    data={...}
)

# AI query
ai_response = client.ai.query(
    prompt='Treatment recommendations',
    context={'patient_id': 'patient_id'}
)
```

## Integration Examples

### Healthcare System Integration
```javascript
// Example: Integrating with existing EMR system
const emrIntegration = {
  async syncPatient(emrPatientId) {
    const emrPatient = await emr.getPatient(emrPatientId);
    const physioflowPatient = await physioflow.patients.create({
      externalId: emrPatientId,
      firstName: emrPatient.firstName,
      lastName: emrPatient.lastName,
      // ... other fields
    });
    return physioflowPatient;
  }
};
```

### Billing System Integration
```javascript
// Example: Sync billing data
const billingIntegration = {
  async createInvoice(sessionId) {
    const session = await physioflow.sessions.get(sessionId);
    const invoice = await billing.createInvoice({
      patientId: session.patientId,
      services: session.services,
      amount: session.totalCost
    });
    return invoice;
  }
};
```

## Support

- **API Documentation**: [docs.physioflow.com/api](https://docs.physioflow.com/api)
- **Support Email**: <EMAIL>
- **Status Page**: [status.physioflow.com](https://status.physioflow.com)
