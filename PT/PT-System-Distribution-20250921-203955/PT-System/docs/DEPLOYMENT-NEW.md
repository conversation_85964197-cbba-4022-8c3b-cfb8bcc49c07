# PhysioFlow Deployment Guide

## Overview

This guide covers deployment strategies for PhysioFlow across different environments and cloud providers, ensuring HIPAA compliance and optimal performance.

## Prerequisites

- **Docker** 20.10+
- **Docker Compose** 2.0+
- **Node.js** 16.0+ (for local development)
- **SSL Certificate** (for production)
- **Domain Name** (for production)

## Environment Configuration

### Development Environment

```bash
# Clone repository
git clone https://github.com/physioflow/physioflow.git
cd physioflow

# Frontend setup
cd frontend
npm install
npm start

# Backend setup (when available)
cd ../backend
npm install
npm run dev
```

### Production Environment

```bash
# Build for production
npm run build:production

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

## Docker Configuration

### Frontend Dockerfile
```dockerfile
# Multi-stage build for production
FROM node:16-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose - Production
```yaml
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=${MONGODB_URI}
      - JWT_SECRET=${JWT_SECRET}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    restart: unless-stopped
    depends_on:
      - mongodb
      - redis

  mongodb:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  mongodb_data:
  redis_data:
```

## Environment Variables

### Frontend Environment Variables
```env
# Production
REACT_APP_API_URL=https://api.physioflow.com/v1
REACT_APP_ENVIRONMENT=production
REACT_APP_GEMINI_API_KEY=your_gemini_api_key

# Development
REACT_APP_API_URL=http://localhost:5000/api/v1
REACT_APP_ENVIRONMENT=development
```

### Backend Environment Variables
```env
# Database
MONGODB_URI=******************************************************
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRES_IN=7d

# AI Services
GEMINI_API_KEY=your_gemini_api_key
OPENAI_API_KEY=your_openai_api_key

# Email Service
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## Cloud Deployment

### AWS Deployment

#### Using AWS ECS
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com

docker build -t physioflow-frontend ./frontend
docker tag physioflow-frontend:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/physioflow-frontend:latest
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/physioflow-frontend:latest

# Deploy using ECS CLI
ecs-cli compose --file docker-compose.aws.yml service up
```

### Azure Deployment

#### Using Azure Container Instances
```bash
# Create resource group
az group create --name physioflow-rg --location eastus

# Create container registry
az acr create --resource-group physioflow-rg --name physioflowregistry --sku Basic

# Build and push image
az acr build --registry physioflowregistry --image physioflow-frontend:latest ./frontend

# Deploy container
az container create \
  --resource-group physioflow-rg \
  --name physioflow-frontend \
  --image physioflowregistry.azurecr.io/physioflow-frontend:latest \
  --dns-name-label physioflow \
  --ports 80 443
```

### Google Cloud Platform

#### Using Cloud Run
```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT_ID/physioflow-frontend ./frontend

# Deploy to Cloud Run
gcloud run deploy physioflow-frontend \
  --image gcr.io/PROJECT_ID/physioflow-frontend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## SSL/TLS Configuration

### Nginx SSL Configuration
```nginx
server {
    listen 80;
    server_name physioflow.com www.physioflow.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name physioflow.com www.physioflow.com;

    ssl_certificate /etc/nginx/ssl/physioflow.com.crt;
    ssl_certificate_key /etc/nginx/ssl/physioflow.com.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;

    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/ {
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Monitoring and Logging

### Health Check Endpoints
```javascript
// Backend health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: process.env.npm_package_version
  });
});
```

### Logging Configuration
```javascript
// Winston logger configuration
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'physioflow-api' },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

## Security Checklist

- [ ] SSL/TLS certificates configured
- [ ] Environment variables secured
- [ ] Database authentication enabled
- [ ] API rate limiting implemented
- [ ] CORS properly configured
- [ ] Security headers added
- [ ] Input validation implemented
- [ ] HIPAA compliance verified

## Troubleshooting

### Common Issues

1. **Container won't start**
   ```bash
   docker logs container_name
   docker exec -it container_name /bin/sh
   ```

2. **Database connection issues**
   ```bash
   # Check MongoDB status
   docker exec -it mongodb_container mongo --eval "db.adminCommand('ismaster')"
   ```

3. **SSL certificate issues**
   ```bash
   # Test SSL certificate
   openssl s_client -connect physioflow.com:443 -servername physioflow.com
   ```

## Support

- **Deployment Support**: <EMAIL>
- **Infrastructure Issues**: <EMAIL>
- **Security Concerns**: <EMAIL>
