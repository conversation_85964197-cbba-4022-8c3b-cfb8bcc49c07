const express = require('express');
const multer = require('multer');
const path = require('path');
const {
  uploadFile,
  uploadMultipleFiles,
  deleteFile,
  getFile,
  getFilesByType,
  uploadPatientDocument,
  uploadProfilePicture,
  uploadTreatmentImage,
  uploadFormAttachment,
  compressImage,
  generateThumbnail
} = require('../controllers/upload');

const { protect, authorize, checkPermission } = require('../middleware/auth');
const { AppError } = require('../middleware/errorHandler');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '../public/uploads');
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Define allowed file types
  const allowedTypes = {
    'image/jpeg': true,
    'image/jpg': true,
    'image/png': true,
    'image/gif': true,
    'image/webp': true,
    'application/pdf': true,
    'application/msword': true,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': true,
    'application/vnd.ms-excel': true,
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': true,
    'text/plain': true,
    'text/csv': true
  };

  if (allowedTypes[file.mimetype]) {
    cb(null, true);
  } else {
    cb(new AppError('Invalid file type. Only images, PDFs, and documents are allowed.', 400), false);
  }
};

// Configure multer with options
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_UPLOAD) || 5 * 1024 * 1024, // 5MB default
    files: 10 // Maximum 10 files at once
  }
});

// Configure multer for memory storage (for image processing)
const memoryUpload = multer({
  storage: multer.memoryStorage(),
  fileFilter: (req, file, cb) => {
    // Only allow images for memory upload
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new AppError('Only image files are allowed for this endpoint.', 400), false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB for images
  }
});

/**
 * @swagger
 * components:
 *   schemas:
 *     FileUpload:
 *       type: object
 *       properties:
 *         filename:
 *           type: string
 *           description: Generated filename
 *         originalName:
 *           type: string
 *           description: Original filename
 *         mimetype:
 *           type: string
 *           description: File MIME type
 *         size:
 *           type: integer
 *           description: File size in bytes
 *         url:
 *           type: string
 *           description: File access URL
 *         uploadDate:
 *           type: string
 *           format: date-time
 *           description: Upload timestamp
 */

// Protect all routes
router.use(protect);

/**
 * @swagger
 * /upload/single:
 *   post:
 *     summary: Upload a single file
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               type:
 *                 type: string
 *                 enum: [document, image, medical_record, profile_picture]
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/FileUpload'
 *       400:
 *         description: Invalid file or upload error
 */
router.post('/single', upload.single('file'), uploadFile);

/**
 * @swagger
 * /upload/multiple:
 *   post:
 *     summary: Upload multiple files
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *               type:
 *                 type: string
 *                 enum: [document, image, medical_record]
 *     responses:
 *       201:
 *         description: Files uploaded successfully
 */
router.post('/multiple', upload.array('files', 10), uploadMultipleFiles);

/**
 * @swagger
 * /upload/patient-document:
 *   post:
 *     summary: Upload patient document
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               patientId:
 *                 type: string
 *               documentType:
 *                 type: string
 *                 enum: [medical_record, insurance_card, id_copy, referral, assessment, other]
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Patient document uploaded successfully
 */
router.post('/patient-document', 
  checkPermission('edit_patients'),
  upload.single('file'), 
  uploadPatientDocument
);

/**
 * @swagger
 * /upload/profile-picture:
 *   post:
 *     summary: Upload profile picture
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               userId:
 *                 type: string
 *     responses:
 *       201:
 *         description: Profile picture uploaded successfully
 */
router.post('/profile-picture', 
  memoryUpload.single('file'), 
  uploadProfilePicture
);

/**
 * @swagger
 * /upload/treatment-image:
 *   post:
 *     summary: Upload treatment-related image
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               treatmentPlanId:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Treatment image uploaded successfully
 */
router.post('/treatment-image', 
  checkPermission('edit_treatments'),
  memoryUpload.single('file'), 
  uploadTreatmentImage
);

/**
 * @swagger
 * /upload/form-attachment:
 *   post:
 *     summary: Upload form attachment
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               formId:
 *                 type: string
 *               fieldId:
 *                 type: string
 *     responses:
 *       201:
 *         description: Form attachment uploaded successfully
 */
router.post('/form-attachment', 
  checkPermission('create_forms'),
  upload.single('file'), 
  uploadFormAttachment
);

/**
 * @swagger
 * /upload/compress-image:
 *   post:
 *     summary: Upload and compress image
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               quality:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 100
 *                 default: 80
 *               maxWidth:
 *                 type: integer
 *                 default: 1920
 *               maxHeight:
 *                 type: integer
 *                 default: 1080
 *     responses:
 *       201:
 *         description: Image compressed and uploaded successfully
 */
router.post('/compress-image', 
  memoryUpload.single('file'), 
  compressImage
);

/**
 * @swagger
 * /upload/thumbnail:
 *   post:
 *     summary: Generate thumbnail from image
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               width:
 *                 type: integer
 *                 default: 150
 *               height:
 *                 type: integer
 *                 default: 150
 *     responses:
 *       201:
 *         description: Thumbnail generated successfully
 */
router.post('/thumbnail', 
  memoryUpload.single('file'), 
  generateThumbnail
);

/**
 * @swagger
 * /upload/files/{type}:
 *   get:
 *     summary: Get files by type
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [document, image, medical_record, profile_picture]
 *     responses:
 *       200:
 *         description: List of files by type
 */
router.get('/files/:type', checkPermission('view_patients'), getFilesByType);

/**
 * @swagger
 * /upload/file/{filename}:
 *   get:
 *     summary: Get file by filename
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: File content
 *   delete:
 *     summary: Delete file
 *     tags: [File Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: filename
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: File deleted successfully
 */
router.route('/file/:filename')
  .get(getFile)
  .delete(checkPermission('delete_patients'), deleteFile);

module.exports = router;
