const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const PDFDocument = require('pdfkit');
const { protect } = require('../middleware/auth');

// Home Exercise Program Schema
const homeExerciseProgramSchema = new mongoose.Schema({
  patientName: { type: String, required: true },
  patientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Patient' },
  programDate: { type: Date, required: true },
  therapistName: { type: String, required: true },
  
  // Program Details
  programTitle: { type: String, required: true },
  programDuration: { type: String, default: '4' }, // weeks
  frequency: { type: String, default: 'daily' },
  totalSessions: String,
  
  // Exercises
  exercises: [{
    id: Number,
    name: String,
    category: String,
    description: String,
    instructions: String,
    repetitions: String,
    sets: String,
    frequency: String,
    modifiedRepetitions: String,
    modifiedSets: String,
    modifiedFrequency: String,
    customInstructions: String,
    notes: String,
    image: String
  }],
  
  // Safety Instructions
  safetyPrecautions: String,
  contraindications: String,
  warningSignsToStop: String,
  
  // Progress Tracking
  progressMeasures: [String],
  reviewDate: Date,
  modificationCriteria: String,
  
  // Patient Education
  patientInstructions: String,
  equipmentNeeded: String,
  environmentSetup: String,
  
  // Follow-up
  followUpSchedule: String,
  contactInformation: String,
  emergencyContact: String,
  
  // Signatures
  therapistSignature: String,
  patientAcknowledgment: { type: Boolean, default: false },
  caregiverAcknowledgment: { type: Boolean, default: false },
  
  // Metadata
  submittedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  submittedAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const HomeExerciseProgram = mongoose.model('HomeExerciseProgram', homeExerciseProgramSchema);

// Middleware to bypass auth in development
const optionalAuth = (req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    req.user = req.user || {
      id: '507f1f77bcf86cd799439011',
      role: 'therapist',
      name: 'Development User'
    };
  }
  next();
};

// Test route
router.get('/test', (req, res) => {
  res.json({
    message: 'Home Exercise Program API is working',
    timestamp: new Date().toISOString()
  });
});

// Public route to create programs (no auth for development)
router.post('/public', async (req, res) => {
  try {
    const programData = {
      ...req.body,
      submittedBy: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
      submittedAt: new Date()
    };

    // Basic validation
    if (!programData.patientName || !programData.therapistName || !programData.programTitle) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: patientName, therapistName, programTitle'
      });
    }

    const program = new HomeExerciseProgram(programData);
    await program.save();

    res.status(201).json({
      success: true,
      message: 'Home exercise program created successfully',
      program
    });
  } catch (error) {
    console.error('Error creating home exercise program:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating home exercise program',
      error: error.message
    });
  }
});

// Get all programs
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, patientId, search } = req.query;

    const query = {};
    if (patientId) query.patientId = patientId;
    if (search) {
      query.$or = [
        { patientName: new RegExp(search, 'i') },
        { programTitle: new RegExp(search, 'i') },
        { therapistName: new RegExp(search, 'i') }
      ];
    }

    const programs = await HomeExerciseProgram.find(query)
      .populate('patientId', 'name mrNumber')
      .populate('submittedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean();

    const total = await HomeExerciseProgram.countDocuments(query);

    res.json({
      success: true,
      programs: programs || [],
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total: total || 0
    });
  } catch (error) {
    console.error('Error fetching home exercise programs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Generate PDF
router.post('/pdf', optionalAuth, async (req, res) => {
  try {
    const programData = req.body;
    
    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="home-exercise-program-${programData.patientName}-${programData.programDate}.pdf"`);
    
    // Pipe PDF to response
    doc.pipe(res);

    // Add header
    doc.fontSize(16).font('Helvetica-Bold');
    doc.text('HOME EXERCISE PROGRAM (HEP)', { align: 'center' });
    doc.moveDown();

    // Program Information
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PROGRAM INFORMATION', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Patient Name: ${programData.patientName}`);
    doc.text(`Program Title: ${programData.programTitle}`);
    doc.text(`Program Date: ${programData.programDate}`);
    doc.text(`Therapist: ${programData.therapistName}`);
    doc.text(`Duration: ${programData.programDuration} weeks`);
    doc.text(`Frequency: ${programData.frequency}`);
    doc.moveDown();

    // Exercises
    if (programData.exercises && programData.exercises.length > 0) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('EXERCISE PROGRAM', { underline: true });
      doc.moveDown(0.5);

      programData.exercises.forEach((exercise, index) => {
        doc.fontSize(11).font('Helvetica-Bold');
        doc.text(`${index + 1}. ${exercise.name}`, { continued: false });
        
        doc.fontSize(9).font('Helvetica');
        if (exercise.description) {
          doc.text(`Description: ${exercise.description}`, { width: 500 });
        }
        
        if (exercise.instructions) {
          doc.text(`Instructions: ${exercise.instructions}`, { width: 500 });
        }
        
        // Exercise parameters
        const reps = exercise.modifiedRepetitions || exercise.repetitions;
        const sets = exercise.modifiedSets || exercise.sets;
        const freq = exercise.modifiedFrequency || exercise.frequency;
        
        doc.text(`Repetitions: ${reps} | Sets: ${sets} | Frequency: ${freq}`);
        
        if (exercise.customInstructions) {
          doc.text(`Special Instructions: ${exercise.customInstructions}`, { width: 500 });
        }
        
        if (exercise.notes) {
          doc.text(`Notes: ${exercise.notes}`, { width: 500 });
        }
        
        doc.moveDown(0.5);
      });
      doc.moveDown();
    }

    // Safety Instructions
    if (programData.safetyPrecautions || programData.contraindications || programData.warningSignsToStop) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('SAFETY INSTRUCTIONS', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      if (programData.safetyPrecautions) {
        doc.text(`Safety Precautions: ${programData.safetyPrecautions}`, { width: 500 });
      }
      if (programData.contraindications) {
        doc.text(`Contraindications: ${programData.contraindications}`, { width: 500 });
      }
      if (programData.warningSignsToStop) {
        doc.text(`Warning Signs to Stop: ${programData.warningSignsToStop}`, { width: 500 });
      }
      doc.moveDown();
    }

    // Patient Instructions
    if (programData.patientInstructions) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('PATIENT INSTRUCTIONS', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      doc.text(programData.patientInstructions, { width: 500 });
      doc.moveDown();
    }

    // Equipment Needed
    if (programData.equipmentNeeded) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('EQUIPMENT NEEDED', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      doc.text(programData.equipmentNeeded, { width: 500 });
      doc.moveDown();
    }

    // Follow-up Information
    if (programData.followUpSchedule || programData.contactInformation) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('FOLLOW-UP INFORMATION', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      if (programData.followUpSchedule) {
        doc.text(`Follow-up Schedule: ${programData.followUpSchedule}`);
      }
      if (programData.contactInformation) {
        doc.text(`Contact Information: ${programData.contactInformation}`);
      }
      if (programData.emergencyContact) {
        doc.text(`Emergency Contact: ${programData.emergencyContact}`);
      }
      doc.moveDown();
    }

    // Acknowledgments
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('ACKNOWLEDGMENTS', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Patient Acknowledgment: ${programData.patientAcknowledgment ? 'Yes' : 'No'}`);
    doc.text(`Caregiver Acknowledgment: ${programData.caregiverAcknowledgment ? 'Yes' : 'No'}`);
    doc.moveDown();

    // Signature
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PROGRAM CREATED BY', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Therapist: ${programData.therapistName}`);
    doc.text(`Date: ${programData.programDate}`);
    if (programData.therapistSignature) {
      doc.text(`Signature: ${programData.therapistSignature}`);
    }

    // Footer
    doc.fontSize(8).font('Helvetica');
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 50, doc.page.height - 50);
    doc.text('PhysioFlow - Home Exercise Program', { align: 'center' });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ message: 'Error generating PDF' });
  }
});

module.exports = router;
