const express = require('express');
const {
  getInvoiceTemplates,
  getInvoiceTemplate,
  createInvoiceTemplate,
  updateInvoiceTemplate,
  deleteInvoiceTemplate,
  duplicateInvoiceTemplate,
  setAsDefault,
  getTemplatePreview,
  getTemplateStats,
  useTemplate
} = require('../controllers/invoiceTemplate');
const { protect, checkPermission } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     InvoiceTemplate:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *         name:
 *           type: string
 *         nameArabic:
 *           type: string
 *         description:
 *           type: string
 *         type:
 *           type: string
 *           enum: [standard, medical, therapy, insurance, government, custom]
 *         category:
 *           type: string
 *           enum: [general, physical_therapy, occupational_therapy, speech_therapy, medical_consultation, equipment_rental]
 *         config:
 *           type: object
 *         styling:
 *           type: object
 *         isActive:
 *           type: boolean
 *         isDefault:
 *           type: boolean
 *         usageCount:
 *           type: number
 *         version:
 *           type: string
 */

/**
 * @swagger
 * /invoice-templates:
 *   get:
 *     summary: Get all invoice templates
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [standard, medical, therapy, insurance, government, custom]
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *       - in: query
 *         name: active
 *         schema:
 *           type: string
 *           enum: [true, false, all]
 *           default: true
 *     responses:
 *       200:
 *         description: List of invoice templates
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: number
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/InvoiceTemplate'
 */
router.get('/', protect, checkPermission('view_billing'), getInvoiceTemplates);

/**
 * @swagger
 * /invoice-templates/stats:
 *   get:
 *     summary: Get invoice template statistics
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Template statistics
 */
router.get('/stats', protect, checkPermission('view_reports'), getTemplateStats);

/**
 * @swagger
 * /invoice-templates:
 *   post:
 *     summary: Create new invoice template
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - type
 *             properties:
 *               name:
 *                 type: string
 *               nameArabic:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [standard, medical, therapy, insurance, government, custom]
 *               category:
 *                 type: string
 *               config:
 *                 type: object
 *               styling:
 *                 type: object
 *               isDefault:
 *                 type: boolean
 *     responses:
 *       201:
 *         description: Template created successfully
 *       400:
 *         description: Invalid input data
 */
router.post('/', protect, checkPermission('create_billing'), createInvoiceTemplate);

/**
 * @swagger
 * /invoice-templates/{id}:
 *   get:
 *     summary: Get single invoice template
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Invoice template details
 *       404:
 *         description: Template not found
 */
router.get('/:id', protect, checkPermission('view_billing'), getInvoiceTemplate);

/**
 * @swagger
 * /invoice-templates/{id}:
 *   put:
 *     summary: Update invoice template
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/InvoiceTemplate'
 *     responses:
 *       200:
 *         description: Template updated successfully
 *       404:
 *         description: Template not found
 *       403:
 *         description: Cannot modify system templates
 */
router.put('/:id', protect, checkPermission('edit_billing'), updateInvoiceTemplate);

/**
 * @swagger
 * /invoice-templates/{id}:
 *   delete:
 *     summary: Delete invoice template
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Template deleted successfully
 *       400:
 *         description: Template is in use
 *       404:
 *         description: Template not found
 */
router.delete('/:id', protect, checkPermission('delete_billing'), deleteInvoiceTemplate);

/**
 * @swagger
 * /invoice-templates/{id}/duplicate:
 *   post:
 *     summary: Duplicate invoice template
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       201:
 *         description: Template duplicated successfully
 *       404:
 *         description: Template not found
 */
router.post('/:id/duplicate', protect, checkPermission('create_billing'), duplicateInvoiceTemplate);

/**
 * @swagger
 * /invoice-templates/{id}/use:
 *   post:
 *     summary: Use template (increment usage count)
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Template usage recorded
 *       404:
 *         description: Template not found
 */
router.post('/:id/use', protect, checkPermission('view_billing'), useTemplate);

/**
 * @swagger
 * /invoice-templates/{id}/set-default:
 *   put:
 *     summary: Set template as default
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Template set as default
 *       404:
 *         description: Template not found
 */
router.put('/:id/set-default', protect, checkPermission('edit_billing'), setAsDefault);

/**
 * @swagger
 * /invoice-templates/{id}/preview:
 *   get:
 *     summary: Get template preview
 *     tags: [Invoice Templates]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Template preview with sample data
 *       404:
 *         description: Template not found
 */
router.get('/:id/preview', protect, checkPermission('view_billing'), getTemplatePreview);

module.exports = router;
