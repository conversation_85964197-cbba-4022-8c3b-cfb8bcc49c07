const express = require('express');
const {
  performAnalysis,
  getPatientAnalytics,
  getLatestAnalysis,
  getHighRiskPatients,
  getPendingAlerts,
  dismissAlert,
  addFeedback,
  getDashboardData,
  getTreatmentRecommendations,
  getOutcomePredictions,
  batchAnalyze,
  getStatistics
} = require('../controllers/aiAnalytics');

const router = express.Router();

// Middleware
const { protect, authorize } = require('../middleware/auth');
const {
  checkAnalyticsPermission,
  viewPatientsAccess,
  viewAnalyticsAccess,
  functionalIndependenceAccess,
  treatmentEffectivenessAccess,
  clinicalProgressAccess
} = require('../middleware/permissions');

// Apply authentication to all routes
router.use(protect);

/**
 * AI Analytics Routes
 * All routes require authentication
 */

// @route   POST /api/v1/ai-analytics/analyze/:patientId
// @desc    Perform AI analysis for a patient
// @access  Private (Doctor, Therapist, Admin)
router.post('/analyze/:patientId',
  functionalIndependenceAccess,
  performAnalysis
);

// @route   GET /api/v1/ai-analytics/patient/:patientId
// @desc    Get AI analytics for a patient
// @access  Private
router.get('/patient/:patientId',
  viewPatientsAccess,
  getPatientAnalytics
);

// @route   GET /api/v1/ai-analytics/patient/:patientId/latest
// @desc    Get latest AI analysis for a patient
// @access  Private
router.get('/patient/:patientId/latest',
  viewPatientsAccess,
  getLatestAnalysis
);

// @route   GET /api/v1/ai-analytics/patient/:patientId/recommendations
// @desc    Get treatment recommendations for a patient
// @access  Private
router.get('/patient/:patientId/recommendations',
  treatmentEffectivenessAccess,
  getTreatmentRecommendations
);

// @route   GET /api/v1/ai-analytics/patient/:patientId/predictions
// @desc    Get outcome predictions for a patient
// @access  Private
router.get('/patient/:patientId/predictions',
  viewAnalyticsAccess,
  getOutcomePredictions
);

// @route   GET /api/v1/ai-analytics/high-risk
// @desc    Get high-risk patients
// @access  Private (Admin, Manager, Doctor)
router.get('/high-risk',
  authorize('admin', 'manager', 'doctor'),
  clinicalProgressAccess,
  getHighRiskPatients
);

// @route   GET /api/v1/ai-analytics/alerts
// @desc    Get pending clinical alerts
// @access  Private
router.get('/alerts',
  viewAnalyticsAccess,
  getPendingAlerts
);

// @route   PUT /api/v1/ai-analytics/:id/alerts/:alertIndex/dismiss
// @desc    Dismiss a clinical alert
// @access  Private
router.put('/:id/alerts/:alertIndex/dismiss',
  viewAnalyticsAccess,
  dismissAlert
);

// @route   POST /api/v1/ai-analytics/:id/feedback
// @desc    Add feedback to AI recommendation
// @access  Private
router.post('/:id/feedback',
  treatmentEffectivenessAccess,
  addFeedback
);

// @route   GET /api/v1/ai-analytics/dashboard
// @desc    Get AI analytics dashboard data
// @access  Private (Admin, Manager, Doctor)
router.get('/dashboard',
  authorize('admin', 'manager', 'doctor'),
  functionalIndependenceAccess,
  getDashboardData
);

// @route   POST /api/v1/ai-analytics/batch-analyze
// @desc    Trigger batch analysis for multiple patients
// @access  Private (Admin only)
router.post('/batch-analyze',
  authorize('admin'),
  viewAnalyticsAccess,
  batchAnalyze
);

// @route   GET /api/v1/ai-analytics/statistics
// @desc    Get AI analytics statistics
// @access  Private (Admin, Manager)
router.get('/statistics',
  authorize('admin', 'manager'),
  viewAnalyticsAccess,
  getStatistics
);

module.exports = router;
