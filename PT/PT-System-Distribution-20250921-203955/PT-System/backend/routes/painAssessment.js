const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const PDFDocument = require('pdfkit');
const { protect } = require('../middleware/auth');

// Pain Assessment Schema
const painAssessmentSchema = new mongoose.Schema({
  patientName: { type: String, required: true },
  patientId: { type: mongoose.Schema.Types.ObjectId, ref: 'Patient' },
  assessmentDate: { type: Date, required: true },
  assessorName: { type: String, required: true },
  
  // Pain Intensity
  currentPainLevel: { type: Number, min: 0, max: 10, required: true },
  averagePainLevel: { type: Number, min: 0, max: 10 },
  worstPainLevel: { type: Number, min: 0, max: 10 },
  bestPainLevel: { type: Number, min: 0, max: 10 },
  
  // Pain Characteristics
  painQuality: [String],
  painPattern: String,
  painOnset: String,
  painDuration: String,
  painFrequency: String,
  
  // Factors
  aggravatingFactors: [String],
  aggravatingOther: String,
  relievingFactors: [String],
  relievingOther: String,
  
  // Functional Impact
  sleepImpact: { type: Number, min: 0, max: 10 },
  workImpact: { type: Number, min: 0, max: 10 },
  dailyActivitiesImpact: { type: Number, min: 0, max: 10 },
  moodImpact: { type: Number, min: 0, max: 10 },
  socialImpact: { type: Number, min: 0, max: 10 },
  
  // Pain Management
  currentMedications: String,
  previousTreatments: String,
  treatmentEffectiveness: String,
  
  // Goals
  painGoals: String,
  functionalGoals: String,
  treatmentExpectations: String,
  
  // Body Map Data
  bodyMapData: mongoose.Schema.Types.Mixed,
  
  // Additional
  additionalNotes: String,
  assessorSignature: String,
  assessmentComplete: { type: Boolean, default: false },
  
  // Metadata
  submittedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  submittedAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const PainAssessment = mongoose.model('PainAssessment', painAssessmentSchema);

// Middleware to bypass auth in development
const optionalAuth = (req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    req.user = req.user || {
      id: '507f1f77bcf86cd799439011',
      role: 'therapist',
      name: 'Development User'
    };
  }
  next();
};

// Test route
router.get('/test', (req, res) => {
  res.json({
    message: 'Pain Assessment API is working',
    timestamp: new Date().toISOString()
  });
});

// Public route to create assessments (no auth for development)
router.post('/public', async (req, res) => {
  try {
    const assessmentData = {
      ...req.body,
      submittedBy: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
      submittedAt: new Date()
    };

    // Basic validation
    if (!assessmentData.patientName || !assessmentData.assessorName) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: patientName, assessorName'
      });
    }

    const assessment = new PainAssessment(assessmentData);
    await assessment.save();

    res.status(201).json({
      success: true,
      message: 'Pain assessment created successfully',
      assessment
    });
  } catch (error) {
    console.error('Error creating pain assessment:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating pain assessment',
      error: error.message
    });
  }
});

// Get all pain assessments
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, patientId, search } = req.query;

    const query = {};
    if (patientId) query.patientId = patientId;
    if (search) {
      query.$or = [
        { patientName: new RegExp(search, 'i') },
        { assessorName: new RegExp(search, 'i') }
      ];
    }

    const assessments = await PainAssessment.find(query)
      .populate('patientId', 'name mrNumber')
      .populate('submittedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean();

    const total = await PainAssessment.countDocuments(query);

    res.json({
      success: true,
      assessments: assessments || [],
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total: total || 0
    });
  } catch (error) {
    console.error('Error fetching pain assessments:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Generate PDF
router.post('/pdf', optionalAuth, async (req, res) => {
  try {
    const assessmentData = req.body;
    
    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="pain-assessment-${assessmentData.patientName}-${assessmentData.assessmentDate}.pdf"`);
    
    // Pipe PDF to response
    doc.pipe(res);

    // Add header
    doc.fontSize(16).font('Helvetica-Bold');
    doc.text('PAIN ASSESSMENT SCALE', { align: 'center' });
    doc.moveDown();

    // Patient Information
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT INFORMATION', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Patient Name: ${assessmentData.patientName}`);
    doc.text(`Assessment Date: ${assessmentData.assessmentDate}`);
    doc.text(`Assessor: ${assessmentData.assessorName}`);
    doc.moveDown();

    // Pain Intensity
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PAIN INTENSITY (0-10 SCALE)', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Current Pain Level: ${assessmentData.currentPainLevel}/10`);
    doc.text(`Average Pain (24hrs): ${assessmentData.averagePainLevel}/10`);
    doc.text(`Worst Pain (24hrs): ${assessmentData.worstPainLevel}/10`);
    doc.text(`Best Pain (24hrs): ${assessmentData.bestPainLevel}/10`);
    doc.moveDown();

    // Pain Characteristics
    if (assessmentData.painQuality && assessmentData.painQuality.length > 0) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('PAIN CHARACTERISTICS', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      doc.text(`Pain Quality: ${assessmentData.painQuality.join(', ')}`);
      if (assessmentData.painPattern) doc.text(`Pain Pattern: ${assessmentData.painPattern}`);
      if (assessmentData.painOnset) doc.text(`Pain Onset: ${assessmentData.painOnset}`);
      if (assessmentData.painDuration) doc.text(`Duration: ${assessmentData.painDuration}`);
      if (assessmentData.painFrequency) doc.text(`Frequency: ${assessmentData.painFrequency}`);
      doc.moveDown();
    }

    // Aggravating Factors
    if (assessmentData.aggravatingFactors && assessmentData.aggravatingFactors.length > 0) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('AGGRAVATING FACTORS', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      doc.text(`Factors: ${assessmentData.aggravatingFactors.join(', ')}`);
      if (assessmentData.aggravatingOther) {
        doc.text(`Other: ${assessmentData.aggravatingOther}`);
      }
      doc.moveDown();
    }

    // Relieving Factors
    if (assessmentData.relievingFactors && assessmentData.relievingFactors.length > 0) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('RELIEVING FACTORS', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      doc.text(`Factors: ${assessmentData.relievingFactors.join(', ')}`);
      if (assessmentData.relievingOther) {
        doc.text(`Other: ${assessmentData.relievingOther}`);
      }
      doc.moveDown();
    }

    // Functional Impact
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('FUNCTIONAL IMPACT (0-10 SCALE)', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    if (assessmentData.sleepImpact !== undefined) doc.text(`Sleep Impact: ${assessmentData.sleepImpact}/10`);
    if (assessmentData.workImpact !== undefined) doc.text(`Work Impact: ${assessmentData.workImpact}/10`);
    if (assessmentData.dailyActivitiesImpact !== undefined) doc.text(`Daily Activities Impact: ${assessmentData.dailyActivitiesImpact}/10`);
    if (assessmentData.moodImpact !== undefined) doc.text(`Mood Impact: ${assessmentData.moodImpact}/10`);
    if (assessmentData.socialImpact !== undefined) doc.text(`Social Impact: ${assessmentData.socialImpact}/10`);
    doc.moveDown();

    // Goals and Treatment
    if (assessmentData.painGoals || assessmentData.functionalGoals || assessmentData.treatmentExpectations) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('GOALS AND TREATMENT EXPECTATIONS', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      if (assessmentData.painGoals) doc.text(`Pain Goals: ${assessmentData.painGoals}`, { width: 500 });
      if (assessmentData.functionalGoals) doc.text(`Functional Goals: ${assessmentData.functionalGoals}`, { width: 500 });
      if (assessmentData.treatmentExpectations) doc.text(`Treatment Expectations: ${assessmentData.treatmentExpectations}`, { width: 500 });
      doc.moveDown();
    }

    // Additional Notes
    if (assessmentData.additionalNotes) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('ADDITIONAL NOTES', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      doc.text(assessmentData.additionalNotes, { width: 500 });
      doc.moveDown();
    }

    // Signature
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('ASSESSMENT COMPLETED BY', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Assessor: ${assessmentData.assessorName}`);
    doc.text(`Date: ${assessmentData.assessmentDate}`);
    if (assessmentData.assessorSignature) {
      doc.text(`Signature: ${assessmentData.assessorSignature}`);
    }

    // Footer
    doc.fontSize(8).font('Helvetica');
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 50, doc.page.height - 50);
    doc.text('PhysioFlow - Pain Assessment Scale', { align: 'center' });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ message: 'Error generating PDF' });
  }
});

module.exports = router;
