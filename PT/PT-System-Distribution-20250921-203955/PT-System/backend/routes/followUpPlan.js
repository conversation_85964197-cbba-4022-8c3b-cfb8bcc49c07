const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const PDFDocument = require('pdfkit');
const { protect } = require('../middleware/auth');

// Follow Up Plan Schema
const followUpPlanSchema = new mongoose.Schema({
  // Document Metadata
  documentNumber: { type: String, required: true },
  issueDate: { type: Date, required: true },
  version: { type: String, default: '01' },
  reviewNumber: { type: String, default: '01' },
  
  // Patient Information
  patientName: { type: String, required: true },
  
  // Follow-up Entries (up to 4)
  followUps: [{
    id: Number,
    date: { type: Date, required: true },
    currentSituation: { type: String, required: true },
    physiotherapistName: { type: String, required: true },
    treatmentProposals: { type: String, required: true },
    nextFollowUpDate: { type: Date, required: true },
    remarks: String
  }],
  
  // Metadata
  submittedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  submittedAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const FollowUpPlan = mongoose.model('FollowUpPlan', followUpPlanSchema);

// Middleware to bypass auth in development
const optionalAuth = (req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    req.user = req.user || {
      id: '507f1f77bcf86cd799439011',
      role: 'therapist',
      name: 'Development User'
    };
  }
  next();
};

// Test route
router.get('/test', (req, res) => {
  res.json({
    message: 'Follow Up Plan API is working',
    timestamp: new Date().toISOString()
  });
});

// Public route to create follow-up plans (no auth for development)
router.post('/public', async (req, res) => {
  try {
    const planData = {
      ...req.body,
      submittedBy: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
      submittedAt: new Date()
    };

    // Basic validation
    if (!planData.patientName || !planData.followUps || planData.followUps.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: patientName, followUps'
      });
    }

    // Validate each follow-up entry
    for (let i = 0; i < planData.followUps.length; i++) {
      const followUp = planData.followUps[i];
      if (!followUp.date || !followUp.currentSituation || !followUp.physiotherapistName || 
          !followUp.treatmentProposals || !followUp.nextFollowUpDate) {
        return res.status(400).json({
          success: false,
          message: `Follow-up ${i + 1} is missing required fields`
        });
      }

      // Validate dates
      const followUpDate = new Date(followUp.date);
      const nextDate = new Date(followUp.nextFollowUpDate);
      const today = new Date();
      
      if (followUpDate > today) {
        return res.status(400).json({
          success: false,
          message: `Follow-up ${i + 1} date cannot be in the future`
        });
      }
      
      if (nextDate <= today) {
        return res.status(400).json({
          success: false,
          message: `Follow-up ${i + 1} next follow-up date must be in the future`
        });
      }
    }

    const plan = new FollowUpPlan(planData);
    await plan.save();

    res.status(201).json({
      success: true,
      message: 'Follow-up plan created successfully',
      plan
    });
  } catch (error) {
    console.error('Error creating follow-up plan:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating follow-up plan',
      error: error.message
    });
  }
});

// Get all follow-up plans
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, patientId, search } = req.query;

    const query = {};
    if (patientId) query.patientId = patientId;
    if (search) {
      query.$or = [
        { patientName: new RegExp(search, 'i') },
        { 'followUps.physiotherapistName': new RegExp(search, 'i') }
      ];
    }

    const plans = await FollowUpPlan.find(query)
      .populate('submittedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean();

    const total = await FollowUpPlan.countDocuments(query);

    res.json({
      success: true,
      plans: plans || [],
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total: total || 0
    });
  } catch (error) {
    console.error('Error fetching follow-up plans:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Generate PDF
router.post('/pdf', optionalAuth, async (req, res) => {
  try {
    const planData = req.body;
    
    // Create PDF document
    const doc = new PDFDocument({ margin: 50, size: 'A4' });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="follow-up-plan-${planData.patientName}-${planData.issueDate}.pdf"`);
    
    // Pipe PDF to response
    doc.pipe(res);

    // Helper function to add page header
    const addPageHeader = (pageNum) => {
      doc.fontSize(16).font('Helvetica-Bold');
      doc.text('FOLLOW UP PLAN FOR DOCTOR', { align: 'center' });
      doc.moveDown();

      // Document metadata
      doc.fontSize(10).font('Helvetica');
      doc.text(`Document Number: ${planData.documentNumber}`, 50, 100);
      doc.text(`Issue Date: ${planData.issueDate}`, 200, 100);
      doc.text(`Version: ${planData.version}`, 350, 100);
      doc.text(`Review Number: ${planData.reviewNumber}`, 450, 100);
      
      doc.text(`Page ${pageNum} of 2`, 500, 120);
      doc.moveDown();

      return 140;
    };

    // Page 1
    let currentY = addPageHeader(1);

    // Patient Information
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT INFORMATION', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Patient Name: ${planData.patientName}`, 50, currentY);
    currentY += 30;

    // Follow-up Entries (First 2 on page 1)
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('FOLLOW-UP ENTRIES', 50, currentY, { underline: true });
    currentY += 20;

    const followUpsPage1 = planData.followUps.slice(0, 2);
    
    followUpsPage1.forEach((followUp, index) => {
      // Check if we need a new page
      if (currentY > 650) {
        doc.addPage();
        currentY = addPageHeader(1);
      }

      doc.fontSize(11).font('Helvetica-Bold');
      doc.text(`Follow-up ${index + 1}`, 50, currentY, { underline: true });
      currentY += 20;

      doc.fontSize(10).font('Helvetica');
      
      // Date and Physiotherapist
      doc.text(`Date: ${followUp.date}`, 50, currentY);
      doc.text(`Physiotherapist: ${followUp.physiotherapistName}`, 300, currentY);
      currentY += 20;

      // Current Situation
      doc.font('Helvetica-Bold');
      doc.text('Current Situation of the Patient:', 50, currentY);
      currentY += 15;
      doc.font('Helvetica');
      doc.text(followUp.currentSituation, 50, currentY, { width: 500 });
      currentY += Math.max(40, Math.ceil(followUp.currentSituation.length / 80) * 12);

      // Treatment Proposals
      doc.font('Helvetica-Bold');
      doc.text('Treatment Proposals:', 50, currentY);
      currentY += 15;
      doc.font('Helvetica');
      doc.text(followUp.treatmentProposals, 50, currentY, { width: 500 });
      currentY += Math.max(40, Math.ceil(followUp.treatmentProposals.length / 80) * 12);

      // Next Follow-up and Remarks
      doc.text(`Next Follow Up (Outpatient): ${followUp.nextFollowUpDate}`, 50, currentY);
      currentY += 15;
      
      if (followUp.remarks) {
        doc.font('Helvetica-Bold');
        doc.text('Remarks:', 50, currentY);
        currentY += 15;
        doc.font('Helvetica');
        doc.text(followUp.remarks, 50, currentY, { width: 500 });
        currentY += Math.max(20, Math.ceil(followUp.remarks.length / 80) * 12);
      }

      currentY += 20; // Space between follow-ups
    });

    // Page 2 (if there are more follow-ups)
    const followUpsPage2 = planData.followUps.slice(2, 4);
    
    if (followUpsPage2.length > 0) {
      doc.addPage();
      currentY = addPageHeader(2);

      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('ADDITIONAL FOLLOW-UP ENTRIES', 50, currentY, { underline: true });
      currentY += 20;

      followUpsPage2.forEach((followUp, index) => {
        // Check if we need space
        if (currentY > 650) {
          doc.addPage();
          currentY = addPageHeader(2);
        }

        doc.fontSize(11).font('Helvetica-Bold');
        doc.text(`Follow-up ${index + 3}`, 50, currentY, { underline: true });
        currentY += 20;

        doc.fontSize(10).font('Helvetica');
        
        // Date and Physiotherapist
        doc.text(`Date: ${followUp.date}`, 50, currentY);
        doc.text(`Physiotherapist: ${followUp.physiotherapistName}`, 300, currentY);
        currentY += 20;

        // Current Situation
        doc.font('Helvetica-Bold');
        doc.text('Current Situation of the Patient:', 50, currentY);
        currentY += 15;
        doc.font('Helvetica');
        doc.text(followUp.currentSituation, 50, currentY, { width: 500 });
        currentY += Math.max(40, Math.ceil(followUp.currentSituation.length / 80) * 12);

        // Treatment Proposals
        doc.font('Helvetica-Bold');
        doc.text('Treatment Proposals:', 50, currentY);
        currentY += 15;
        doc.font('Helvetica');
        doc.text(followUp.treatmentProposals, 50, currentY, { width: 500 });
        currentY += Math.max(40, Math.ceil(followUp.treatmentProposals.length / 80) * 12);

        // Next Follow-up and Remarks
        doc.text(`Next Follow Up (Outpatient): ${followUp.nextFollowUpDate}`, 50, currentY);
        currentY += 15;
        
        if (followUp.remarks) {
          doc.font('Helvetica-Bold');
          doc.text('Remarks:', 50, currentY);
          currentY += 15;
          doc.font('Helvetica');
          doc.text(followUp.remarks, 50, currentY, { width: 500 });
          currentY += Math.max(20, Math.ceil(followUp.remarks.length / 80) * 12);
        }

        currentY += 20; // Space between follow-ups
      });
    }

    // Footer
    doc.fontSize(8).font('Helvetica');
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 50, doc.page.height - 50);
    doc.text('PhysioFlow - Follow Up Plan for Doctor', { align: 'center' });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ message: 'Error generating PDF' });
  }
});

module.exports = router;
