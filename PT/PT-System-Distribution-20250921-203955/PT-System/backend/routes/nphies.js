const express = require('express');
const {
  verifyEligibility,
  submitClaim,
  getClaimStatus,
  submitPreAuthorization,
  getPreAuthStatus,
  validate<PERSON>rovider,
  submitEncounter,
  getEncounterStatus,
  generateComplianceReport,
  syncPatientData,
  validateCoverage,
  submitBenefitInquiry,
  getSystemStatus,
  testConnection,
  getNphiesStats,
  linkVisaToServices,
  createElectronicSignature
} = require('../controllers/nphies');

const { protect, authorize, checkPermission } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /nphies/eligibility:
 *   post:
 *     summary: Verify patient eligibility
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - insuranceId
 *               - serviceType
 *             properties:
 *               patientId:
 *                 type: string
 *                 description: Patient identifier
 *               insuranceId:
 *                 type: string
 *                 description: Insurance policy number
 *               serviceType:
 *                 type: string
 *                 enum: [physical_therapy, occupational_therapy, speech_therapy, consultation]
 *               serviceDate:
 *                 type: string
 *                 format: date
 *                 description: Planned service date
 *     responses:
 *       200:
 *         description: Eligibility verification result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     eligible:
 *                       type: boolean
 *                     coverage:
 *                       type: object
 *                     benefits:
 *                       type: array
 *                     limitations:
 *                       type: array
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: NPHIES authentication failed
 */

// Protect all routes
router.use(protect);

/**
 * @swagger
 * /nphies/eligibility:
 *   post:
 *     summary: Verify patient eligibility with NPHIES
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 */
router.post('/eligibility', checkPermission('view_insurance'), verifyEligibility);

/**
 * @swagger
 * /nphies/claims:
 *   post:
 *     summary: Submit claim to NPHIES
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - providerId
 *               - services
 *               - totalAmount
 *             properties:
 *               patientId:
 *                 type: string
 *               providerId:
 *                 type: string
 *               services:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                     description:
 *                       type: string
 *                     quantity:
 *                       type: integer
 *                     unitPrice:
 *                       type: number
 *                     totalPrice:
 *                       type: number
 *                     serviceDate:
 *                       type: string
 *                       format: date
 *               totalAmount:
 *                 type: number
 *               diagnosis:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: Claim submitted successfully
 */
router.post('/claims', checkPermission('create_insurance'), submitClaim);

/**
 * @swagger
 * /nphies/claims/{claimId}/status:
 *   get:
 *     summary: Get claim status from NPHIES
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: claimId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Claim status information
 */
router.get('/claims/:claimId/status', checkPermission('view_insurance'), getClaimStatus);

/**
 * @swagger
 * /nphies/preauth:
 *   post:
 *     summary: Submit pre-authorization request
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - providerId
 *               - services
 *               - estimatedAmount
 *             properties:
 *               patientId:
 *                 type: string
 *               providerId:
 *                 type: string
 *               services:
 *                 type: array
 *                 items:
 *                   type: object
 *               estimatedAmount:
 *                 type: number
 *               treatmentPlan:
 *                 type: string
 *               urgency:
 *                 type: string
 *                 enum: [routine, urgent, emergency]
 *     responses:
 *       201:
 *         description: Pre-authorization submitted successfully
 */
router.post('/preauth', checkPermission('create_insurance'), submitPreAuthorization);

/**
 * @swagger
 * /nphies/preauth/{preAuthId}/status:
 *   get:
 *     summary: Get pre-authorization status
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: preAuthId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Pre-authorization status
 */
router.get('/preauth/:preAuthId/status', checkPermission('view_insurance'), getPreAuthStatus);

/**
 * @swagger
 * /nphies/provider/validate:
 *   post:
 *     summary: Validate provider credentials
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - providerId
 *               - licenseNumber
 *             properties:
 *               providerId:
 *                 type: string
 *               licenseNumber:
 *                 type: string
 *               specialization:
 *                 type: string
 *     responses:
 *       200:
 *         description: Provider validation result
 */
router.post('/provider/validate', checkPermission('edit_users'), validateProvider);

/**
 * @swagger
 * /nphies/encounters:
 *   post:
 *     summary: Submit encounter data
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - providerId
 *               - encounterType
 *               - encounterDate
 *             properties:
 *               patientId:
 *                 type: string
 *               providerId:
 *                 type: string
 *               encounterType:
 *                 type: string
 *                 enum: [outpatient, inpatient, emergency, home_visit]
 *               encounterDate:
 *                 type: string
 *                 format: date-time
 *               diagnosis:
 *                 type: array
 *                 items:
 *                   type: string
 *               procedures:
 *                 type: array
 *                 items:
 *                   type: object
 *     responses:
 *       201:
 *         description: Encounter submitted successfully
 */
router.post('/encounters', checkPermission('create_appointments'), submitEncounter);

/**
 * @swagger
 * /nphies/encounters/{encounterId}/status:
 *   get:
 *     summary: Get encounter status
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: encounterId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Encounter status
 */
router.get('/encounters/:encounterId/status', checkPermission('view_appointments'), getEncounterStatus);

/**
 * @swagger
 * /nphies/coverage/validate:
 *   post:
 *     summary: Validate insurance coverage
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - insuranceId
 *             properties:
 *               patientId:
 *                 type: string
 *               insuranceId:
 *                 type: string
 *               effectiveDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Coverage validation result
 */
router.post('/coverage/validate', checkPermission('view_insurance'), validateCoverage);

/**
 * @swagger
 * /nphies/benefits:
 *   post:
 *     summary: Submit benefit inquiry
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - serviceCategory
 *             properties:
 *               patientId:
 *                 type: string
 *               serviceCategory:
 *                 type: string
 *                 enum: [rehabilitation, therapy, consultation, diagnostic]
 *               specificService:
 *                 type: string
 *     responses:
 *       200:
 *         description: Benefit inquiry result
 */
router.post('/benefits', checkPermission('view_insurance'), submitBenefitInquiry);

/**
 * @swagger
 * /nphies/sync/patient:
 *   post:
 *     summary: Sync patient data with NPHIES
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *             properties:
 *               patientId:
 *                 type: string
 *               syncType:
 *                 type: string
 *                 enum: [full, demographics, insurance, medical_history]
 *                 default: full
 *     responses:
 *       200:
 *         description: Patient data sync result
 */
router.post('/sync/patient', checkPermission('edit_patients'), syncPatientData);

/**
 * @swagger
 * /nphies/compliance/report:
 *   get:
 *     summary: Generate compliance report
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [month, quarter, year]
 *         description: Reporting period
 *       - in: query
 *         name: reportType
 *         schema:
 *           type: string
 *           enum: [claims, encounters, eligibility, preauth]
 *         description: Type of compliance report
 *     responses:
 *       200:
 *         description: Compliance report data
 */
router.get('/compliance/report', checkPermission('view_reports'), generateComplianceReport);

/**
 * @swagger
 * /nphies/system/status:
 *   get:
 *     summary: Get NPHIES system status
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System status information
 */
router.get('/system/status', checkPermission('view_reports'), getSystemStatus);

/**
 * @swagger
 * /nphies/test/connection:
 *   get:
 *     summary: Test NPHIES connection
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Connection test result
 */
router.get('/test/connection', checkPermission('view_reports'), testConnection);

/**
 * @swagger
 * /nphies/stats:
 *   get:
 *     summary: Get NPHIES integration statistics
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *         description: Statistics period
 *     responses:
 *       200:
 *         description: NPHIES integration statistics
 */
router.get('/stats', checkPermission('view_reports'), getNphiesStats);

/**
 * @swagger
 * /nphies/visa/link:
 *   post:
 *     summary: Link visa to patient services
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - visaNumber
 *               - visaType
 *               - serviceIds
 *             properties:
 *               patientId:
 *                 type: string
 *               visaNumber:
 *                 type: string
 *               visaType:
 *                 type: string
 *                 enum: [work, visit, residence, hajj, umrah, transit]
 *               serviceIds:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Visa linked successfully
 *       400:
 *         description: Invalid visa or service data
 */
router.post('/visa/link', checkPermission('edit_patients'), linkVisaToServices);

/**
 * @swagger
 * /nphies/signature/create:
 *   post:
 *     summary: Create electronic signature document for NPHIES
 *     tags: [NPHIES]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - documentType
 *               - documentTitle
 *               - documentContent
 *               - patientId
 *               - signers
 *             properties:
 *               documentType:
 *                 type: string
 *                 enum: [consent_form, treatment_plan, insurance_claim]
 *               documentTitle:
 *                 type: string
 *               documentContent:
 *                 type: string
 *               patientId:
 *                 type: string
 *               signers:
 *                 type: array
 *                 items:
 *                   type: object
 *               relatedRecords:
 *                 type: object
 *     responses:
 *       201:
 *         description: Electronic signature document created
 *       400:
 *         description: Invalid document data
 */
router.post('/signature/create', checkPermission('create_signature'), createElectronicSignature);

module.exports = router;
