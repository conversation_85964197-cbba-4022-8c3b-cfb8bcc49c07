const express = require('express');
const router = express.Router();
const ExerciseProgram = require('../models/ExerciseProgram');
const { protect: auth } = require('../middleware/auth');

// @route   GET /api/exercise-programs/:patientId
// @desc    Get exercise programs for a patient
// @access  Private
router.get('/:patientId', auth, async (req, res) => {
  try {
    const { patientId } = req.params;
    const { status, limit = 10, page = 1 } = req.query;

    const programs = await ExerciseProgram.getPatientPrograms(patientId, status);

    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedPrograms = programs.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedPrograms,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: programs.length,
        pages: Math.ceil(programs.length / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching exercise programs:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch exercise programs'
    });
  }
});

// @route   GET /api/exercise-programs/program/:id
// @desc    Get a specific exercise program
// @access  Private
router.get('/program/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const program = await ExerciseProgram.findById(id)
      .populate('patientId', 'name nameAr age gender condition')
      .populate('therapistId', 'name email')
      .lean();

    if (!program) {
      return res.status(404).json({
        success: false,
        error: 'Exercise program not found'
      });
    }

    res.json({
      success: true,
      data: program
    });
  } catch (error) {
    console.error('Error fetching exercise program:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch exercise program'
    });
  }
});

// @route   POST /api/exercise-programs
// @desc    Create a new exercise program
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const {
      patientId,
      name,
      nameAr,
      description,
      descriptionAr,
      goals,
      goalsAr,
      duration,
      frequency,
      difficulty,
      exercises,
      startDate,
      reminders
    } = req.body;

    // Validate required fields
    if (!patientId || !name || !duration || !frequency || !difficulty || !exercises) {
      return res.status(400).json({
        success: false,
        error: 'Patient ID, name, duration, frequency, difficulty, and exercises are required'
      });
    }

    // Validate exercises array
    if (!Array.isArray(exercises) || exercises.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'At least one exercise is required'
      });
    }

    // Create new exercise program
    const program = new ExerciseProgram({
      patientId,
      therapistId: req.user.id,
      name,
      nameAr,
      description,
      descriptionAr,
      goals: goals || [],
      goalsAr: goalsAr || [],
      duration,
      frequency,
      difficulty,
      exercises,
      startDate: startDate ? new Date(startDate) : new Date(),
      reminders: reminders || {
        enabled: true,
        time: '09:00',
        days: ['monday', 'wednesday', 'friday'],
        channels: ['push']
      },
      status: 'active'
    });

    await program.save();

    // Populate therapist info
    await program.populate('therapistId', 'name email');

    res.status(201).json({
      success: true,
      data: program,
      message: 'Exercise program created successfully'
    });
  } catch (error) {
    console.error('Error creating exercise program:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create exercise program'
    });
  }
});

// @route   PUT /api/exercise-programs/:id
// @desc    Update an exercise program
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const program = await ExerciseProgram.findById(id);

    if (!program) {
      return res.status(404).json({
        success: false,
        error: 'Exercise program not found'
      });
    }

    // Update fields
    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        program[key] = updateData[key];
      }
    });

    await program.save();
    await program.populate('therapistId', 'name email');

    res.json({
      success: true,
      data: program,
      message: 'Exercise program updated successfully'
    });
  } catch (error) {
    console.error('Error updating exercise program:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update exercise program'
    });
  }
});

// @route   POST /api/exercise-programs/:id/complete-session
// @desc    Mark a session as completed
// @access  Private
router.post('/:id/complete-session', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { feedback } = req.body;

    const program = await ExerciseProgram.findById(id);

    if (!program) {
      return res.status(404).json({
        success: false,
        error: 'Exercise program not found'
      });
    }

    await program.completeSession(feedback);

    res.json({
      success: true,
      data: program,
      message: 'Session completed successfully'
    });
  } catch (error) {
    console.error('Error completing session:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to complete session'
    });
  }
});

// @route   POST /api/exercise-programs/:id/adaptation
// @desc    Add adaptation to a program
// @access  Private
router.post('/:id/adaptation', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { reason, changes } = req.body;

    if (!reason || !changes) {
      return res.status(400).json({
        success: false,
        error: 'Reason and changes are required'
      });
    }

    const program = await ExerciseProgram.findById(id);

    if (!program) {
      return res.status(404).json({
        success: false,
        error: 'Exercise program not found'
      });
    }

    await program.addAdaptation(reason, changes, req.user.id);

    res.json({
      success: true,
      data: program,
      message: 'Adaptation added successfully'
    });
  } catch (error) {
    console.error('Error adding adaptation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add adaptation'
    });
  }
});

// @route   GET /api/exercise-programs/analytics
// @desc    Get exercise program analytics
// @access  Private
router.get('/analytics', auth, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const analytics = await ExerciseProgram.getAnalytics(startDate, endDate);

    res.json({
      success: true,
      data: analytics[0] || {
        totalPrograms: 0,
        activePrograms: 0,
        completedPrograms: 0,
        avgDifficulty: 0,
        avgCompletionRate: 0,
        avgAdherenceRate: 0,
        totalExercises: 0,
        uniquePatients: 0
      }
    });
  } catch (error) {
    console.error('Error fetching exercise program analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch exercise program analytics'
    });
  }
});

// @route   GET /api/exercise-programs/attention
// @desc    Get programs needing attention
// @access  Private
router.get('/attention', auth, async (req, res) => {
  try {
    const programs = await ExerciseProgram.getProgramsNeedingAttention();

    res.json({
      success: true,
      data: programs
    });
  } catch (error) {
    console.error('Error fetching programs needing attention:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch programs needing attention'
    });
  }
});

// @route   GET /api/exercise-programs/library
// @desc    Get exercise library
// @access  Private
router.get('/library', auth, async (req, res) => {
  try {
    const { category, bodyPart, difficulty, search } = req.query;

    // Mock exercise library - in real app, this would come from a database
    let exercises = [
      {
        exerciseId: 'ex001',
        name: 'Shoulder Flexion',
        nameAr: 'ثني الكتف',
        category: 'flexibility',
        bodyPart: ['shoulders'],
        difficulty: 2,
        instructions: 'Slowly raise your arm forward and up as high as comfortable',
        instructionsAr: 'ارفع ذراعك ببطء للأمام وللأعلى بقدر ما هو مريح',
        videoUrl: '/videos/shoulder-flexion.mp4',
        imageUrl: '/images/shoulder-flexion.jpg',
        equipment: [],
        contraindications: ['acute shoulder injury']
      },
      {
        exerciseId: 'ex002',
        name: 'Knee Extension',
        nameAr: 'تمديد الركبة',
        category: 'strength',
        bodyPart: ['legs'],
        difficulty: 3,
        instructions: 'Sit in chair, slowly straighten your knee, hold for 5 seconds',
        instructionsAr: 'اجلس على كرسي، افرد ركبتك ببطء، احتفظ بالوضعية لمدة 5 ثوان',
        videoUrl: '/videos/knee-extension.mp4',
        imageUrl: '/images/knee-extension.jpg',
        equipment: ['chair'],
        contraindications: ['acute knee pain']
      },
      {
        exerciseId: 'ex003',
        name: 'Deep Breathing',
        nameAr: 'التنفس العميق',
        category: 'cardio',
        bodyPart: ['chest'],
        difficulty: 1,
        instructions: 'Breathe in slowly through nose, hold, then exhale through mouth',
        instructionsAr: 'تنفس ببطء من الأنف، احبس النفس، ثم ازفر من الفم',
        videoUrl: '/videos/deep-breathing.mp4',
        imageUrl: '/images/deep-breathing.jpg',
        equipment: [],
        contraindications: []
      }
      // Add more exercises as needed
    ];

    // Apply filters
    if (category) {
      exercises = exercises.filter(ex => ex.category === category);
    }
    if (bodyPart) {
      exercises = exercises.filter(ex => ex.bodyPart.includes(bodyPart));
    }
    if (difficulty) {
      exercises = exercises.filter(ex => ex.difficulty === parseInt(difficulty));
    }
    if (search) {
      const searchLower = search.toLowerCase();
      exercises = exercises.filter(ex => 
        ex.name.toLowerCase().includes(searchLower) ||
        ex.nameAr.includes(search) ||
        ex.instructions.toLowerCase().includes(searchLower)
      );
    }

    res.json({
      success: true,
      data: exercises,
      total: exercises.length
    });
  } catch (error) {
    console.error('Error fetching exercise library:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch exercise library'
    });
  }
});

// @route   DELETE /api/exercise-programs/:id
// @desc    Delete an exercise program
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const program = await ExerciseProgram.findById(id);

    if (!program) {
      return res.status(404).json({
        success: false,
        error: 'Exercise program not found'
      });
    }

    await program.deleteOne();

    res.json({
      success: true,
      message: 'Exercise program deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting exercise program:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete exercise program'
    });
  }
});

module.exports = router;
