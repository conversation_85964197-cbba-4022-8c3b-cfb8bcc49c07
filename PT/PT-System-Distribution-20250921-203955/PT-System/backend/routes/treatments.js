const express = require('express');
const {
  getTreatmentPlans,
  getTreatmentPlan,
  createTreatmentPlan,
  updateTreatmentPlan,
  deleteTreatmentPlan,
  addProgressNote,
  updateGoalStatus,
  getTreatmentPlansByPatient,
  getTreatmentPlansByTherapist,
  getDueForReview,
  getTreatmentStats,
  exportTreatmentPlan
} = require('../controllers/treatments');

const { protect, authorize, checkPermission, checkPatientAccess } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /treatments:
 *   get:
 *     summary: Get all treatment plans
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of treatment plans per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, completed, discontinued, on_hold, transferred]
 *         description: Filter by treatment plan status
 *       - in: query
 *         name: therapist
 *         schema:
 *           type: string
 *         description: Filter by therapist ID
 *       - in: query
 *         name: treatmentType
 *         schema:
 *           type: string
 *           enum: [physical_therapy, occupational_therapy, speech_therapy, combined, special_needs]
 *         description: Filter by treatment type
 *     responses:
 *       200:
 *         description: List of treatment plans
 *   post:
 *     summary: Create a new treatment plan
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TreatmentPlan'
 *     responses:
 *       201:
 *         description: Treatment plan created successfully
 *       400:
 *         description: Validation error
 */

// Protect all routes
router.use(protect);

// Get all treatment plans and create new treatment plan
router
  .route('/')
  .get(checkPermission('view_treatments'), getTreatmentPlans)
  .post(checkPermission('create_treatments'), createTreatmentPlan);

/**
 * @swagger
 * /treatments/stats:
 *   get:
 *     summary: Get treatment statistics
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Treatment statistics
 */
router.get('/stats', checkPermission('view_treatments'), getTreatmentStats);

/**
 * @swagger
 * /treatments/due-for-review:
 *   get:
 *     summary: Get treatment plans due for review
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Treatment plans due for review
 */
router.get('/due-for-review', checkPermission('view_treatments'), getDueForReview);

/**
 * @swagger
 * /treatments/patient/{patientId}:
 *   get:
 *     summary: Get treatment plans by patient
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient's treatment plans
 */
router.get('/patient/:patientId', checkPermission('view_treatments'), getTreatmentPlansByPatient);

/**
 * @swagger
 * /treatments/therapist/{therapistId}:
 *   get:
 *     summary: Get treatment plans by therapist
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: therapistId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Therapist's treatment plans
 */
router.get('/therapist/:therapistId', checkPermission('view_treatments'), getTreatmentPlansByTherapist);

/**
 * @swagger
 * /treatments/{id}:
 *   get:
 *     summary: Get treatment plan by ID
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Treatment plan details
 *       404:
 *         description: Treatment plan not found
 *   put:
 *     summary: Update treatment plan
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TreatmentPlan'
 *     responses:
 *       200:
 *         description: Treatment plan updated successfully
 *       404:
 *         description: Treatment plan not found
 *   delete:
 *     summary: Delete treatment plan
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Treatment plan deleted successfully
 *       404:
 *         description: Treatment plan not found
 */
router
  .route('/:id')
  .get(checkPermission('view_treatments'), getTreatmentPlan)
  .put(checkPermission('edit_treatments'), updateTreatmentPlan)
  .delete(checkPermission('delete_treatments'), authorize('admin'), deleteTreatmentPlan);

/**
 * @swagger
 * /treatments/{id}/progress:
 *   post:
 *     summary: Add progress note to treatment plan
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               goalsAddressed:
 *                 type: array
 *                 items:
 *                   type: string
 *               interventionsUsed:
 *                 type: array
 *                 items:
 *                   type: string
 *               patientResponse:
 *                 type: string
 *               progressNotes:
 *                 type: string
 *               functionalImprovements:
 *                 type: array
 *                 items:
 *                   type: string
 *               challenges:
 *                 type: array
 *                 items:
 *                   type: string
 *               modifications:
 *                 type: string
 *               homeExerciseCompliance:
 *                 type: string
 *                 enum: [excellent, good, fair, poor, not_applicable]
 *               painLevel:
 *                 type: object
 *                 properties:
 *                   before:
 *                     type: integer
 *                     minimum: 0
 *                     maximum: 10
 *                   after:
 *                     type: integer
 *                     minimum: 0
 *                     maximum: 10
 *     responses:
 *       201:
 *         description: Progress note added successfully
 */
router.post('/:id/progress', checkPermission('edit_treatments'), addProgressNote);

/**
 * @swagger
 * /treatments/{id}/goals/{goalId}:
 *   put:
 *     summary: Update goal status
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: goalId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [not_started, in_progress, achieved, modified, discontinued]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Goal status updated successfully
 */
router.put('/:id/goals/:goalId', checkPermission('edit_treatments'), updateGoalStatus);

/**
 * @swagger
 * /treatments/{id}/export:
 *   get:
 *     summary: Export treatment plan
 *     tags: [Treatment Plans]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [pdf, docx]
 *         description: Export format
 *     responses:
 *       200:
 *         description: Exported treatment plan
 */
router.get('/:id/export', checkPermission('view_treatments'), exportTreatmentPlan);

module.exports = router;
