const express = require('express');
const router = express.Router();
const BodyMapAssessment = require('../models/BodyMapAssessment');
const { protect: auth } = require('../middleware/auth');

// @route   GET /api/bodymap/:patientId
// @desc    Get body map assessments for a patient
// @access  Private
router.get('/:patientId', auth, async (req, res) => {
  try {
    const { patientId } = req.params;
    const { limit = 10, page = 1 } = req.query;

    const assessments = await BodyMapAssessment.find({ patientId })
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .populate('therapistId', 'name email')
      .lean();

    const total = await BodyMapAssessment.countDocuments({ patientId });

    res.json({
      success: true,
      data: assessments,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching body map assessments:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch body map assessments'
    });
  }
});

// @route   GET /api/bodymap/:patientId/latest
// @desc    Get latest body map assessment for a patient
// @access  Private
router.get('/:patientId/latest', auth, async (req, res) => {
  try {
    const { patientId } = req.params;

    const assessment = await BodyMapAssessment.findOne({ patientId })
      .sort({ createdAt: -1 })
      .populate('therapistId', 'name email')
      .lean();

    if (!assessment) {
      return res.status(404).json({
        success: false,
        error: 'No body map assessment found for this patient'
      });
    }

    res.json({
      success: true,
      data: assessment
    });
  } catch (error) {
    console.error('Error fetching latest body map assessment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch latest body map assessment'
    });
  }
});

// @route   POST /api/bodymap
// @desc    Create a new body map assessment
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const {
      patientId,
      selectedRegions,
      painLevels,
      painDetails,
      notes
    } = req.body;

    // Validate required fields
    if (!patientId || !selectedRegions || !Array.isArray(selectedRegions)) {
      return res.status(400).json({
        success: false,
        error: 'Patient ID and selected regions are required'
      });
    }

    // Create new assessment
    const assessment = new BodyMapAssessment({
      patientId,
      selectedRegions,
      painLevels: new Map(Object.entries(painLevels || {})),
      painDetails: new Map(Object.entries(painDetails || {})),
      therapistId: req.user.id,
      notes,
      assessmentDate: new Date()
    });

    await assessment.save();

    // Populate therapist info
    await assessment.populate('therapistId', 'name email');

    res.status(201).json({
      success: true,
      data: assessment,
      message: 'Body map assessment created successfully'
    });
  } catch (error) {
    console.error('Error creating body map assessment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create body map assessment'
    });
  }
});

// @route   PUT /api/bodymap/:id
// @desc    Update a body map assessment
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      selectedRegions,
      painLevels,
      painDetails,
      notes,
      status
    } = req.body;

    const assessment = await BodyMapAssessment.findById(id);

    if (!assessment) {
      return res.status(404).json({
        success: false,
        error: 'Body map assessment not found'
      });
    }

    // Update fields
    if (selectedRegions) assessment.selectedRegions = selectedRegions;
    if (painLevels) assessment.painLevels = new Map(Object.entries(painLevels));
    if (painDetails) assessment.painDetails = new Map(Object.entries(painDetails));
    if (notes !== undefined) assessment.notes = notes;
    if (status) assessment.status = status;

    await assessment.save();
    await assessment.populate('therapistId', 'name email');

    res.json({
      success: true,
      data: assessment,
      message: 'Body map assessment updated successfully'
    });
  } catch (error) {
    console.error('Error updating body map assessment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update body map assessment'
    });
  }
});

// @route   DELETE /api/bodymap/:id
// @desc    Delete a body map assessment
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const assessment = await BodyMapAssessment.findById(id);

    if (!assessment) {
      return res.status(404).json({
        success: false,
        error: 'Body map assessment not found'
      });
    }

    await assessment.deleteOne();

    res.json({
      success: true,
      message: 'Body map assessment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting body map assessment:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete body map assessment'
    });
  }
});

// @route   GET /api/bodymap/:patientId/summary
// @desc    Get pain summary for a patient
// @access  Private
router.get('/:patientId/summary', auth, async (req, res) => {
  try {
    const { patientId } = req.params;

    const assessment = await BodyMapAssessment.findOne({ patientId })
      .sort({ createdAt: -1 });

    if (!assessment) {
      return res.json({
        success: true,
        data: {
          totalRegions: 0,
          averagePain: 0,
          maxPain: 0,
          regions: []
        }
      });
    }

    const summary = assessment.getPainSummary();

    res.json({
      success: true,
      data: summary
    });
  } catch (error) {
    console.error('Error fetching pain summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch pain summary'
    });
  }
});

// @route   GET /api/bodymap/analytics
// @desc    Get body map analytics
// @access  Private
router.get('/analytics', auth, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const analytics = await BodyMapAssessment.getAnalytics(startDate, endDate);

    res.json({
      success: true,
      data: analytics[0] || {
        totalAssessments: 0,
        avgPainLevel: 0,
        maxPainLevel: 0,
        totalPatients: 0
      }
    });
  } catch (error) {
    console.error('Error fetching body map analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch body map analytics'
    });
  }
});

// @route   GET /api/bodymap/:patientId/history
// @desc    Get patient assessment history with trends
// @access  Private
router.get('/:patientId/history', auth, async (req, res) => {
  try {
    const { patientId } = req.params;
    const { limit = 10 } = req.query;

    const history = await BodyMapAssessment.getPatientHistory(patientId, parseInt(limit));

    // Calculate trends
    const trends = {
      painTrend: 'stable',
      regionTrend: 'stable',
      improvementPercentage: 0
    };

    if (history.length >= 2) {
      const latest = history[0];
      const previous = history[1];

      const latestAvg = latest.averagePainLevel || 0;
      const previousAvg = previous.averagePainLevel || 0;

      if (latestAvg < previousAvg) {
        trends.painTrend = 'improving';
        trends.improvementPercentage = Math.round(((previousAvg - latestAvg) / previousAvg) * 100);
      } else if (latestAvg > previousAvg) {
        trends.painTrend = 'worsening';
      }

      const latestRegions = latest.totalPainRegions || 0;
      const previousRegions = previous.totalPainRegions || 0;

      if (latestRegions < previousRegions) {
        trends.regionTrend = 'improving';
      } else if (latestRegions > previousRegions) {
        trends.regionTrend = 'worsening';
      }
    }

    res.json({
      success: true,
      data: {
        history,
        trends
      }
    });
  } catch (error) {
    console.error('Error fetching assessment history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch assessment history'
    });
  }
});

module.exports = router;
