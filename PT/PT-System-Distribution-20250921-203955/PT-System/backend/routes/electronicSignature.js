const express = require('express');
const {
  createSignatureDocument,
  signDocument,
  getSignatureDocument,
  getPatientSignatureDocuments,
  validateSignatureDocument
} = require('../controllers/electronicSignature');
const { protect, checkPermission } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     ElectronicSignature:
 *       type: object
 *       properties:
 *         documentId:
 *           type: string
 *         documentType:
 *           type: string
 *           enum: [consent_form, treatment_plan, invoice, discharge_summary, prescription]
 *         documentTitle:
 *           type: string
 *         documentContent:
 *           type: string
 *         patient:
 *           type: string
 *         workflow:
 *           type: object
 *           properties:
 *             status:
 *               type: string
 *               enum: [draft, pending_signatures, partially_signed, fully_signed, rejected, expired]
 *             requiredSignatures:
 *               type: number
 *             completedSignatures:
 *               type: number
 *             expiryDate:
 *               type: string
 *               format: date-time
 *         signers:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               signerType:
 *                 type: string
 *                 enum: [patient, guardian, therapist, doctor, witness]
 *               signerInfo:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   nationalId:
 *                     type: string
 *                   email:
 *                     type: string
 *               status:
 *                 type: string
 *                 enum: [pending, signed, rejected, expired]
 */

/**
 * @swagger
 * /signature/create:
 *   post:
 *     summary: Create electronic signature document
 *     tags: [Electronic Signature]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - documentType
 *               - documentTitle
 *               - documentContent
 *               - patientId
 *               - signers
 *             properties:
 *               documentType:
 *                 type: string
 *                 enum: [consent_form, treatment_plan, invoice, discharge_summary, prescription]
 *               documentTitle:
 *                 type: string
 *               documentContent:
 *                 type: string
 *               patientId:
 *                 type: string
 *               signers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     signerType:
 *                       type: string
 *                     signerInfo:
 *                       type: object
 *               relatedRecords:
 *                 type: object
 *               expiryDays:
 *                 type: number
 *                 default: 7
 *     responses:
 *       201:
 *         description: Electronic signature document created successfully
 *       400:
 *         description: Invalid input data
 *       404:
 *         description: Patient not found
 */
router.post('/create', protect, checkPermission('create_signature'), createSignatureDocument);

/**
 * @swagger
 * /signature/sign/{documentId}:
 *   post:
 *     summary: Sign document electronically
 *     tags: [Electronic Signature]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - signerId
 *               - authenticationMethod
 *             properties:
 *               signerId:
 *                 type: string
 *               signatureImage:
 *                 type: string
 *                 description: Base64 encoded signature image
 *               authenticationMethod:
 *                 type: string
 *                 enum: [password, otp, biometric, smart_card, digital_certificate]
 *               deviceInfo:
 *                 type: object
 *               geolocation:
 *                 type: object
 *               consentGiven:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Document signed successfully
 *       400:
 *         description: Document expired or already signed
 *       404:
 *         description: Document or signer not found
 */
router.post('/sign/:documentId', protect, signDocument);

/**
 * @swagger
 * /signature/{documentId}:
 *   get:
 *     summary: Get signature document details
 *     tags: [Electronic Signature]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Signature document details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ElectronicSignature'
 *       404:
 *         description: Document not found
 */
router.get('/:documentId', protect, checkPermission('view_signature'), getSignatureDocument);

/**
 * @swagger
 * /signature/patient/{patientId}:
 *   get:
 *     summary: Get patient signature documents
 *     tags: [Electronic Signature]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, pending_signatures, partially_signed, fully_signed, rejected, expired]
 *       - in: query
 *         name: documentType
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *     responses:
 *       200:
 *         description: Patient signature documents
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     documents:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ElectronicSignature'
 *                     pagination:
 *                       type: object
 */
router.get('/patient/:patientId', protect, checkPermission('view_signature'), getPatientSignatureDocuments);

/**
 * @swagger
 * /signature/validate/{documentId}:
 *   post:
 *     summary: Validate signature document
 *     tags: [Electronic Signature]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Signature validation completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     documentId:
 *                       type: string
 *                     isValid:
 *                       type: boolean
 *                     validationChecks:
 *                       type: array
 *                     lastValidated:
 *                       type: string
 *                       format: date-time
 *       404:
 *         description: Document not found
 */
router.post('/validate/:documentId', protect, checkPermission('validate_signature'), validateSignatureDocument);

module.exports = router;
