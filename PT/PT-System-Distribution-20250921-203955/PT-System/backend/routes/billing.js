const express = require('express');
const {
  getBillings,
  getBilling,
  createBilling,
  updateBilling,
  deleteBilling,
  addPayment,
  sendInvoice,
  sendReminder,
  markAsPaid,
  getBillingsByPatient,
  getOverdueBillings,
  getBillingStats,
  exportBillings,
  generateInvoicePDF
} = require('../controllers/billing');

const { protect, authorize, checkPermission } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /billing:
 *   get:
 *     summary: Get all billing records
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, sent, viewed, paid, partially_paid, overdue, cancelled]
 *         description: Filter by billing status
 *       - in: query
 *         name: patient
 *         schema:
 *           type: string
 *         description: Filter by patient ID
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter from date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter to date
 *     responses:
 *       200:
 *         description: List of billing records
 *   post:
 *     summary: Create a new billing record
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Billing'
 *     responses:
 *       201:
 *         description: Billing record created successfully
 *       400:
 *         description: Validation error
 */

// Protect all routes
router.use(protect);

// Get all billing records and create new billing record
router
  .route('/')
  .get(checkPermission('view_billing'), getBillings)
  .post(checkPermission('create_billing'), createBilling);

/**
 * @swagger
 * /billing/stats:
 *   get:
 *     summary: Get billing statistics
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, quarter, year]
 *         description: Time period for statistics
 *     responses:
 *       200:
 *         description: Billing statistics
 */
router.get('/stats', checkPermission('view_billing'), getBillingStats);

/**
 * @swagger
 * /billing/overdue:
 *   get:
 *     summary: Get overdue billing records
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Overdue billing records
 */
router.get('/overdue', checkPermission('view_billing'), getOverdueBillings);

/**
 * @swagger
 * /billing/export:
 *   get:
 *     summary: Export billing data
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, excel, pdf]
 *         description: Export format
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Export from date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Export to date
 *     responses:
 *       200:
 *         description: Exported billing data
 */
router.get('/export', checkPermission('view_billing'), exportBillings);

/**
 * @swagger
 * /billing/patient/{patientId}:
 *   get:
 *     summary: Get billing records by patient
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient's billing records
 */
router.get('/patient/:patientId', checkPermission('view_billing'), getBillingsByPatient);

/**
 * @swagger
 * /billing/{id}:
 *   get:
 *     summary: Get billing record by ID
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Billing record details
 *       404:
 *         description: Billing record not found
 *   put:
 *     summary: Update billing record
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Billing'
 *     responses:
 *       200:
 *         description: Billing record updated successfully
 *       404:
 *         description: Billing record not found
 *   delete:
 *     summary: Delete billing record
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Billing record deleted successfully
 *       404:
 *         description: Billing record not found
 */
router
  .route('/:id')
  .get(checkPermission('view_billing'), getBilling)
  .put(checkPermission('edit_billing'), updateBilling)
  .delete(checkPermission('delete_billing'), authorize('admin'), deleteBilling);

/**
 * @swagger
 * /billing/{id}/payments:
 *   post:
 *     summary: Add payment to billing record
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - amount
 *               - method
 *             properties:
 *               amount:
 *                 type: number
 *                 minimum: 0
 *               method:
 *                 type: string
 *                 enum: [cash, card, bank_transfer, insurance, check]
 *               reference:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Payment added successfully
 */
router.post('/:id/payments', checkPermission('edit_billing'), addPayment);

/**
 * @swagger
 * /billing/{id}/send:
 *   post:
 *     summary: Send invoice to patient
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               method:
 *                 type: string
 *                 enum: [email, sms, both]
 *                 default: email
 *               message:
 *                 type: string
 *     responses:
 *       200:
 *         description: Invoice sent successfully
 */
router.post('/:id/send', checkPermission('edit_billing'), sendInvoice);

/**
 * @swagger
 * /billing/{id}/reminder:
 *   post:
 *     summary: Send payment reminder
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               method:
 *                 type: string
 *                 enum: [email, sms, both]
 *                 default: email
 *               message:
 *                 type: string
 *     responses:
 *       200:
 *         description: Reminder sent successfully
 */
router.post('/:id/reminder', checkPermission('edit_billing'), sendReminder);

/**
 * @swagger
 * /billing/{id}/mark-paid:
 *   post:
 *     summary: Mark invoice as paid
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               paymentMethod:
 *                 type: string
 *                 enum: [cash, card, bank_transfer, insurance, check]
 *               reference:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Invoice marked as paid successfully
 */
router.post('/:id/mark-paid', checkPermission('edit_billing'), markAsPaid);

/**
 * @swagger
 * /billing/{id}/pdf:
 *   get:
 *     summary: Generate invoice PDF
 *     tags: [Billing]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Invoice PDF generated
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/:id/pdf', checkPermission('view_billing'), generateInvoicePDF);

module.exports = router;
