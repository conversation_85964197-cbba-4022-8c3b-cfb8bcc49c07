const express = require('express');
const {
  getDashboardStats,
  getPatientAnalytics,
  getTreatmentAnalytics,
  getFinancialAnalytics,
  getAppointmentAnalytics,
  getSpecialNeedsAnalytics,
  getPerformanceMetrics,
  getCustomReport,
  exportAnalytics,
  getRevenueReport,
  getPatientDemographics,
  getTreatmentOutcomes,
  getProviderPerformance,
  getInsuranceAnalytics,
  getComplianceReport,
  getInvoiceAnalytics
} = require('../controllers/analytics');

const AnalyticsService = require('../services/analyticsService');

const { protect, authorize, checkPermission } = require('../middleware/auth');
const { checkRole } = require('../middleware/rolePermissions');

const router = express.Router();

/**
 * @swagger
 * /analytics/dashboard:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, quarter, year]
 *         description: Time period for statistics
 *       - in: query
 *         name: dateFrom
 *         schema:
 *           type: string
 *           format: date
 *         description: Custom start date
 *       - in: query
 *         name: dateTo
 *         schema:
 *           type: string
 *           format: date
 *         description: Custom end date
 *     responses:
 *       200:
 *         description: Dashboard statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     overview:
 *                       type: object
 *                     trends:
 *                       type: array
 *                     charts:
 *                       type: object
 */

// Protect all routes
router.use(protect);

/**
 * @swagger
 * /analytics/dashboard:
 *   get:
 *     summary: Get comprehensive dashboard statistics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 */
router.get('/dashboard', checkPermission('view_reports'), getDashboardStats);

/**
 * @swagger
 * /analytics/patients:
 *   get:
 *     summary: Get patient analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *         description: Analysis period
 *       - in: query
 *         name: groupBy
 *         schema:
 *           type: string
 *           enum: [age, gender, condition, therapist]
 *         description: Group results by field
 *     responses:
 *       200:
 *         description: Patient analytics data
 */
router.get('/patients', checkPermission('view_reports'), getPatientAnalytics);

/**
 * @swagger
 * /analytics/treatments:
 *   get:
 *     summary: Get treatment analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: treatmentType
 *         schema:
 *           type: string
 *           enum: [physical_therapy, occupational_therapy, speech_therapy, special_needs]
 *         description: Filter by treatment type
 *       - in: query
 *         name: therapist
 *         schema:
 *           type: string
 *         description: Filter by therapist ID
 *     responses:
 *       200:
 *         description: Treatment analytics data
 */
router.get('/treatments', checkPermission('view_reports'), getTreatmentAnalytics);

/**
 * @swagger
 * /analytics/financial:
 *   get:
 *     summary: Get financial analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [month, quarter, year]
 *         description: Financial period
 *       - in: query
 *         name: breakdown
 *         schema:
 *           type: string
 *           enum: [service, therapist, insurance, payment_method]
 *         description: Revenue breakdown type
 *     responses:
 *       200:
 *         description: Financial analytics data
 */
router.get('/financial', checkPermission('view_reports'), getFinancialAnalytics);

/**
 * @swagger
 * /analytics/appointments:
 *   get:
 *     summary: Get appointment analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter]
 *         description: Analysis period
 *       - in: query
 *         name: therapist
 *         schema:
 *           type: string
 *         description: Filter by therapist ID
 *     responses:
 *       200:
 *         description: Appointment analytics data
 */
router.get('/appointments', checkPermission('view_reports'), getAppointmentAnalytics);

/**
 * @swagger
 * /analytics/special-needs:
 *   get:
 *     summary: Get special needs analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: condition
 *         schema:
 *           type: string
 *           enum: [autism, cerebral_palsy, down_syndrome, adhd, intellectual_disability]
 *         description: Filter by condition type
 *     responses:
 *       200:
 *         description: Special needs analytics data
 */
router.get('/special-needs', checkPermission('view_reports'), getSpecialNeedsAnalytics);

/**
 * @swagger
 * /analytics/performance:
 *   get:
 *     summary: Get performance metrics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: metric
 *         schema:
 *           type: string
 *           enum: [utilization, outcomes, satisfaction, efficiency]
 *         description: Performance metric type
 *       - in: query
 *         name: provider
 *         schema:
 *           type: string
 *         description: Filter by provider ID
 *     responses:
 *       200:
 *         description: Performance metrics data
 */
router.get('/performance', checkPermission('view_reports'), getPerformanceMetrics);

/**
 * @swagger
 * /analytics/demographics:
 *   get:
 *     summary: Get patient demographics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Patient demographics data
 */
router.get('/demographics', checkPermission('view_reports'), getPatientDemographics);

/**
 * @swagger
 * /analytics/outcomes:
 *   get:
 *     summary: Get treatment outcomes
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: treatmentType
 *         schema:
 *           type: string
 *         description: Filter by treatment type
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [month, quarter, year]
 *         description: Analysis period
 *     responses:
 *       200:
 *         description: Treatment outcomes data
 */
router.get('/outcomes', checkPermission('view_reports'), getTreatmentOutcomes);

/**
 * @swagger
 * /analytics/providers:
 *   get:
 *     summary: Get provider performance analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [month, quarter, year]
 *         description: Analysis period
 *     responses:
 *       200:
 *         description: Provider performance data
 */
router.get('/providers', checkPermission('view_reports'), getProviderPerformance);

/**
 * @swagger
 * /analytics/insurance:
 *   get:
 *     summary: Get insurance analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: provider
 *         schema:
 *           type: string
 *         description: Filter by insurance provider
 *     responses:
 *       200:
 *         description: Insurance analytics data
 */
router.get('/insurance', checkPermission('view_reports'), getInsuranceAnalytics);

/**
 * @swagger
 * /analytics/compliance:
 *   get:
 *     summary: Get compliance report
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [nphies, pdpl, quality, safety]
 *         description: Compliance report type
 *     responses:
 *       200:
 *         description: Compliance report data
 */
router.get('/compliance', checkPermission('view_reports'), getComplianceReport);

/**
 * @swagger
 * /analytics/revenue:
 *   get:
 *     summary: Get revenue report
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [month, quarter, year]
 *         description: Revenue period
 *       - in: query
 *         name: breakdown
 *         schema:
 *           type: string
 *           enum: [daily, weekly, monthly]
 *         description: Revenue breakdown
 *     responses:
 *       200:
 *         description: Revenue report data
 */
router.get('/revenue', checkPermission('view_reports'), getRevenueReport);

/**
 * @swagger
 * /analytics/custom:
 *   post:
 *     summary: Generate custom report
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reportType
 *               - dateRange
 *             properties:
 *               reportType:
 *                 type: string
 *                 enum: [patient, treatment, financial, appointment]
 *               dateRange:
 *                 type: object
 *                 properties:
 *                   start:
 *                     type: string
 *                     format: date
 *                   end:
 *                     type: string
 *                     format: date
 *               filters:
 *                 type: object
 *               groupBy:
 *                 type: array
 *                 items:
 *                   type: string
 *               metrics:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Custom report data
 */
router.post('/custom', checkPermission('create_reports'), getCustomReport);

/**
 * @swagger
 * /analytics/export:
 *   get:
 *     summary: Export analytics data
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         required: true
 *         schema:
 *           type: string
 *           enum: [dashboard, patients, treatments, financial, appointments]
 *         description: Type of analytics to export
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, excel, pdf]
 *           default: csv
 *         description: Export format
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *         description: Data period
 *     responses:
 *       200:
 *         description: Exported analytics data
 */
router.get('/export', checkPermission('view_reports'), exportAnalytics);

// Enhanced Analytics Endpoints
/**
 * @swagger
 * /analytics/advanced/dashboard:
 *   get:
 *     summary: Get comprehensive dashboard analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dateRange
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Comprehensive analytics data
 */
router.get('/advanced/dashboard', protect, async (req, res) => {
  try {
    const { dateRange = 30 } = req.query;
    const analytics = await AnalyticsService.getDashboardAnalytics(parseInt(dateRange));

    res.json({
      success: true,
      data: analytics
    });
  } catch (error) {
    console.error('Error fetching advanced dashboard analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analytics data'
    });
  }
});

/**
 * @swagger
 * /analytics/patient-outcomes:
 *   get:
 *     summary: Get patient outcome metrics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dateRange
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Patient outcome metrics
 */
router.get('/patient-outcomes', protect, async (req, res) => {
  try {
    const { dateRange = 30 } = req.query;
    const outcomes = await AnalyticsService.getPatientOutcomes(parseInt(dateRange));

    res.json({
      success: true,
      data: outcomes
    });
  } catch (error) {
    console.error('Error fetching patient outcomes:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch patient outcomes'
    });
  }
});

/**
 * @swagger
 * /analytics/treatment-effectiveness:
 *   get:
 *     summary: Get treatment effectiveness analysis
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Treatment effectiveness data
 */
router.get('/treatment-effectiveness', protect, async (req, res) => {
  try {
    const effectiveness = await AnalyticsService.getTreatmentEffectiveness();

    res.json({
      success: true,
      data: effectiveness
    });
  } catch (error) {
    console.error('Error fetching treatment effectiveness:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch treatment effectiveness'
    });
  }
});

/**
 * @swagger
 * /analytics/therapist-performance:
 *   get:
 *     summary: Get therapist performance metrics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Therapist performance data
 */
router.get('/therapist-performance', protect, async (req, res) => {
  try {
    const performance = await AnalyticsService.getTherapistPerformance();

    res.json({
      success: true,
      data: performance
    });
  } catch (error) {
    console.error('Error fetching therapist performance:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch therapist performance'
    });
  }
});

/**
 * @swagger
 * /analytics/revenue-metrics:
 *   get:
 *     summary: Get revenue analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dateRange
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Revenue metrics data
 */
router.get('/revenue-metrics', protect, async (req, res) => {
  try {
    const { dateRange = 30 } = req.query;
    const revenue = await AnalyticsService.getRevenueMetrics(parseInt(dateRange));

    res.json({
      success: true,
      data: revenue
    });
  } catch (error) {
    console.error('Error fetching revenue metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch revenue metrics'
    });
  }
});

/**
 * @swagger
 * /analytics/operational-metrics:
 *   get:
 *     summary: Get operational metrics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dateRange
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to analyze
 *     responses:
 *       200:
 *         description: Operational metrics data
 */
router.get('/operational-metrics', protect, async (req, res) => {
  try {
    const { dateRange = 30 } = req.query;
    const metrics = await AnalyticsService.getOperationalMetrics(parseInt(dateRange));

    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    console.error('Error fetching operational metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch operational metrics'
    });
  }
});

/**
 * @swagger
 * /analytics/invoice/{id}:
 *   get:
 *     summary: Get invoice analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Invoice ID
 *     responses:
 *       200:
 *         description: Invoice analytics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     viewCount:
 *                       type: number
 *                     emailsSent:
 *                       type: number
 *                     lastViewed:
 *                       type: string
 *                       format: date-time
 *                     paymentHistory:
 *                       type: array
 *                     relatedInvoices:
 *                       type: number
 *                     patientTotalSpent:
 *                       type: number
 *       404:
 *         description: Invoice not found
 *       500:
 *         description: Server error
 */
router.get('/invoice/:id', protect, checkPermission('view_billing'), getInvoiceAnalytics);

module.exports = router;
