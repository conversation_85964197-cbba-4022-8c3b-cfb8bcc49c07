const express = require('express');
const {
  getForms,
  getForm,
  createForm,
  updateForm,
  deleteForm,
  duplicateForm,
  publishForm,
  archiveForm,
  getFormsByCategory,
  getFormTemplates,
  validateForm,
  getFormSubmissions,
  createFormSubmission,
  getFormSubmission,
  updateFormSubmission,
  deleteFormSubmission,
  approveFormSubmission,
  rejectFormSubmission,
  getFormStats,
  exportFormSubmissions
} = require('../controllers/forms');

const { protect, authorize, checkPermission } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /forms:
 *   get:
 *     summary: Get all forms
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of forms per page
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [intake, assessment, progress, discharge, consent, insurance, special_needs, custom]
 *         description: Filter by form category
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, active, inactive, archived]
 *         description: Filter by form status
 *       - in: query
 *         name: isTemplate
 *         schema:
 *           type: boolean
 *         description: Filter templates only
 *     responses:
 *       200:
 *         description: List of forms
 *   post:
 *     summary: Create a new form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Form'
 *     responses:
 *       201:
 *         description: Form created successfully
 *       400:
 *         description: Validation error
 */

// Protect all routes
router.use(protect);

// Get all forms and create new form
router
  .route('/')
  .get(checkPermission('view_forms'), getForms)
  .post(checkPermission('create_forms'), createForm);

/**
 * @swagger
 * /forms/templates:
 *   get:
 *     summary: Get form templates
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of form templates
 */
router.get('/templates', checkPermission('view_forms'), getFormTemplates);

/**
 * @swagger
 * /forms/category/{category}:
 *   get:
 *     summary: Get forms by category
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: [intake, assessment, progress, discharge, consent, insurance, special_needs, custom]
 *     responses:
 *       200:
 *         description: Forms in the specified category
 */
router.get('/category/:category', checkPermission('view_forms'), getFormsByCategory);

/**
 * @swagger
 * /forms/{id}:
 *   get:
 *     summary: Get form by ID
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form details
 *       404:
 *         description: Form not found
 *   put:
 *     summary: Update form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Form'
 *     responses:
 *       200:
 *         description: Form updated successfully
 *       404:
 *         description: Form not found
 *   delete:
 *     summary: Delete form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form deleted successfully
 *       404:
 *         description: Form not found
 */
router
  .route('/:id')
  .get(checkPermission('view_forms'), getForm)
  .put(checkPermission('edit_forms'), updateForm)
  .delete(checkPermission('delete_forms'), authorize('admin'), deleteForm);

/**
 * @swagger
 * /forms/{id}/duplicate:
 *   post:
 *     summary: Duplicate form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: Title for the duplicated form
 *     responses:
 *       201:
 *         description: Form duplicated successfully
 */
router.post('/:id/duplicate', checkPermission('create_forms'), duplicateForm);

/**
 * @swagger
 * /forms/{id}/publish:
 *   put:
 *     summary: Publish form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form published successfully
 */
router.put('/:id/publish', checkPermission('edit_forms'), publishForm);

/**
 * @swagger
 * /forms/{id}/archive:
 *   put:
 *     summary: Archive form
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form archived successfully
 */
router.put('/:id/archive', checkPermission('edit_forms'), archiveForm);

/**
 * @swagger
 * /forms/{id}/validate:
 *   post:
 *     summary: Validate form configuration
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form validation results
 */
router.post('/:id/validate', checkPermission('view_forms'), validateForm);

/**
 * @swagger
 * /forms/{id}/stats:
 *   get:
 *     summary: Get form statistics
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form statistics
 */
router.get('/:id/stats', checkPermission('view_forms'), getFormStats);

/**
 * @swagger
 * /forms/{id}/export:
 *   get:
 *     summary: Export form data
 *     tags: [Forms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, excel, json]
 *         description: Export format
 *     responses:
 *       200:
 *         description: Exported form data
 */
router.get('/:id/export', checkPermission('view_forms'), exportFormSubmissions);

// Form Submissions Routes

/**
 * @swagger
 * /forms/{id}/submissions:
 *   get:
 *     summary: Get form submissions
 *     tags: [Form Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, submitted, reviewed, approved, rejected, archived]
 *         description: Filter by submission status
 *       - in: query
 *         name: patient
 *         schema:
 *           type: string
 *         description: Filter by patient ID
 *     responses:
 *       200:
 *         description: List of form submissions
 *   post:
 *     summary: Create form submission
 *     tags: [Form Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/FormSubmission'
 *     responses:
 *       201:
 *         description: Form submission created successfully
 */
router
  .route('/:id/submissions')
  .get(checkPermission('view_forms'), getFormSubmissions)
  .post(checkPermission('create_forms'), createFormSubmission);

/**
 * @swagger
 * /forms/{id}/submissions/{submissionId}:
 *   get:
 *     summary: Get form submission by ID
 *     tags: [Form Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: submissionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form submission details
 *   put:
 *     summary: Update form submission
 *     tags: [Form Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: submissionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/FormSubmission'
 *     responses:
 *       200:
 *         description: Form submission updated successfully
 *   delete:
 *     summary: Delete form submission
 *     tags: [Form Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: submissionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Form submission deleted successfully
 */
router
  .route('/:id/submissions/:submissionId')
  .get(checkPermission('view_forms'), getFormSubmission)
  .put(checkPermission('edit_forms'), updateFormSubmission)
  .delete(checkPermission('delete_forms'), deleteFormSubmission);

/**
 * @swagger
 * /forms/{id}/submissions/{submissionId}/approve:
 *   put:
 *     summary: Approve form submission
 *     tags: [Form Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: submissionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Form submission approved successfully
 */
router.put('/:id/submissions/:submissionId/approve', checkPermission('edit_forms'), approveFormSubmission);

/**
 * @swagger
 * /forms/{id}/submissions/{submissionId}/reject:
 *   put:
 *     summary: Reject form submission
 *     tags: [Form Submissions]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: submissionId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Form submission rejected successfully
 */
router.put('/:id/submissions/:submissionId/reject', checkPermission('edit_forms'), rejectFormSubmission);

module.exports = router;
