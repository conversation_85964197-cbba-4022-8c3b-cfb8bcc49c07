const express = require('express');
const {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  uploadPatientDocument,
  getPatientDocuments,
  deletePatientDocument,
  getPatientMedicalHistory,
  updatePatientMedicalHistory,
  getPatientSpecialNeeds,
  updatePatientSpecialNeeds,
  getPatientsByTherapist,
  getSpecialNeedsPatients,
  searchPatients,
  getPatientStats,
  exportPatients
} = require('../controllers/patients');

const { protect, authorize, checkPermission, checkPatientAccess } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /patients:
 *   get:
 *     summary: Get all patients
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of patients per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, discharged, transferred]
 *         description: Patient status filter
 *       - in: query
 *         name: therapist
 *         schema:
 *           type: string
 *         description: Filter by therapist ID
 *       - in: query
 *         name: specialNeeds
 *         schema:
 *           type: boolean
 *         description: Filter special needs patients
 *     responses:
 *       200:
 *         description: List of patients
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 count:
 *                   type: integer
 *                 pagination:
 *                   type: object
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Patient'
 *   post:
 *     summary: Create a new patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Patient'
 *     responses:
 *       201:
 *         description: Patient created successfully
 *       400:
 *         description: Validation error
 *       409:
 *         description: Patient with this national ID already exists
 */

// Protect all routes
router.use(protect);

// Get all patients and create new patient
router
  .route('/')
  .get(checkPermission('view_patients'), getPatients)
  .post(checkPermission('create_patients'), createPatient);

/**
 * @swagger
 * /patients/search:
 *   get:
 *     summary: Search patients
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query
 *       - in: query
 *         name: fields
 *         schema:
 *           type: string
 *         description: Comma-separated fields to search in
 *     responses:
 *       200:
 *         description: Search results
 */
router.get('/search', checkPermission('view_patients'), searchPatients);

/**
 * @swagger
 * /patients/stats:
 *   get:
 *     summary: Get patient statistics
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Patient statistics
 */
router.get('/stats', checkPermission('view_patients'), getPatientStats);

/**
 * @swagger
 * /patients/export:
 *   get:
 *     summary: Export patients data
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [csv, excel, pdf]
 *         description: Export format
 *     responses:
 *       200:
 *         description: Exported file
 */
router.get('/export', checkPermission('view_patients'), exportPatients);

/**
 * @swagger
 * /patients/therapist/{therapistId}:
 *   get:
 *     summary: Get patients by therapist
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: therapistId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Therapist's patients
 */
router.get('/therapist/:therapistId', checkPermission('view_patients'), getPatientsByTherapist);

/**
 * @swagger
 * /patients/special-needs:
 *   get:
 *     summary: Get special needs patients
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Special needs patients
 */
router.get('/special-needs', checkPermission('view_patients'), getSpecialNeedsPatients);

/**
 * @swagger
 * /patients/{id}:
 *   get:
 *     summary: Get patient by ID
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient details
 *       404:
 *         description: Patient not found
 *   put:
 *     summary: Update patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Patient'
 *     responses:
 *       200:
 *         description: Patient updated successfully
 *       404:
 *         description: Patient not found
 *   delete:
 *     summary: Delete patient
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient deleted successfully
 *       404:
 *         description: Patient not found
 */
router
  .route('/:id')
  .get(checkPermission('view_patients'), checkPatientAccess, getPatient)
  .put(checkPermission('edit_patients'), checkPatientAccess, updatePatient)
  .delete(checkPermission('delete_patients'), authorize('admin'), deletePatient);

/**
 * @swagger
 * /patients/{id}/documents:
 *   get:
 *     summary: Get patient documents
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient documents
 *   post:
 *     summary: Upload patient document
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               type:
 *                 type: string
 *                 enum: [medical_record, insurance_card, id_copy, referral, assessment, other]
 *               name:
 *                 type: string
 *     responses:
 *       201:
 *         description: Document uploaded successfully
 */
router
  .route('/:id/documents')
  .get(checkPermission('view_patients'), checkPatientAccess, getPatientDocuments)
  .post(checkPermission('edit_patients'), checkPatientAccess, uploadPatientDocument);

/**
 * @swagger
 * /patients/{id}/documents/{documentId}:
 *   delete:
 *     summary: Delete patient document
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: documentId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Document deleted successfully
 */
router.delete('/:id/documents/:documentId', 
  checkPermission('edit_patients'), 
  checkPatientAccess, 
  deletePatientDocument
);

/**
 * @swagger
 * /patients/{id}/medical-history:
 *   get:
 *     summary: Get patient medical history
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient medical history
 *   put:
 *     summary: Update patient medical history
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               allergies:
 *                 type: array
 *                 items:
 *                   type: string
 *               currentMedications:
 *                 type: array
 *                 items:
 *                   type: string
 *               pastSurgeries:
 *                 type: array
 *                 items:
 *                   type: string
 *               chronicConditions:
 *                 type: array
 *                 items:
 *                   type: string
 *               familyHistory:
 *                 type: array
 *                 items:
 *                   type: string
 *               primaryDiagnosis:
 *                 type: string
 *               secondaryDiagnoses:
 *                 type: array
 *                 items:
 *                   type: string
 *               referringPhysician:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Medical history updated successfully
 */
router
  .route('/:id/medical-history')
  .get(checkPermission('view_patients'), checkPatientAccess, getPatientMedicalHistory)
  .put(checkPermission('edit_patients'), checkPatientAccess, updatePatientMedicalHistory);

/**
 * @swagger
 * /patients/{id}/special-needs:
 *   get:
 *     summary: Get patient special needs assessment
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient special needs assessment
 *   put:
 *     summary: Update patient special needs assessment
 *     tags: [Patients]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               hasSpecialNeeds:
 *                 type: boolean
 *               types:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [autism, cerebral_palsy, down_syndrome, adhd, intellectual_disability, sensory_impairment, other]
 *               severity:
 *                 type: string
 *                 enum: [mild, moderate, severe]
 *               communicationMethod:
 *                 type: string
 *                 enum: [verbal, non_verbal, sign_language, picture_cards, assistive_device]
 *               behavioralNotes:
 *                 type: string
 *               sensoryPreferences:
 *                 type: object
 *               adaptiveEquipment:
 *                 type: array
 *                 items:
 *                   type: string
 *               caregiverInstructions:
 *                 type: string
 *     responses:
 *       200:
 *         description: Special needs assessment updated successfully
 */
router
  .route('/:id/special-needs')
  .get(checkPermission('view_patients'), checkPatientAccess, getPatientSpecialNeeds)
  .put(checkPermission('edit_patients'), checkPatientAccess, updatePatientSpecialNeeds);

module.exports = router;
