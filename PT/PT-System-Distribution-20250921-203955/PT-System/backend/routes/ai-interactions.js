const express = require('express');
const router = express.Router();
const AIInteraction = require('../models/AIInteraction');
const { protect: auth } = require('../middleware/auth');

// @route   GET /api/ai-interactions/:patientId?
// @desc    Get AI interactions for a patient or user
// @access  Private
router.get('/:patientId?', auth, async (req, res) => {
  try {
    const { patientId } = req.params;
    const { limit = 50, page = 1, type, provider, status } = req.query;

    let interactions;
    if (patientId) {
      interactions = await AIInteraction.getPatientHistory(patientId, parseInt(limit));
    } else {
      interactions = await AIInteraction.getUserHistory(req.user.id, parseInt(limit));
    }

    // Apply additional filters
    if (type) {
      interactions = interactions.filter(interaction => interaction.type === type);
    }
    if (provider) {
      interactions = interactions.filter(interaction => interaction.provider === provider);
    }
    if (status) {
      interactions = interactions.filter(interaction => interaction.status === status);
    }

    // Apply pagination
    const startIndex = (parseInt(page) - 1) * parseInt(limit);
    const endIndex = startIndex + parseInt(limit);
    const paginatedInteractions = interactions.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: paginatedInteractions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: interactions.length,
        pages: Math.ceil(interactions.length / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching AI interactions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch AI interactions'
    });
  }
});

// @route   GET /api/ai-interactions/session/:sessionId
// @desc    Get AI interactions for a specific session
// @access  Private
router.get('/session/:sessionId', auth, async (req, res) => {
  try {
    const { sessionId } = req.params;

    const interactions = await AIInteraction.getSessionInteractions(sessionId);

    res.json({
      success: true,
      data: interactions
    });
  } catch (error) {
    console.error('Error fetching session interactions:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch session interactions'
    });
  }
});

// @route   POST /api/ai-interactions
// @desc    Create a new AI interaction
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const {
      patientId,
      sessionId,
      type,
      provider,
      model,
      prompt,
      context,
      metadata
    } = req.body;

    // Validate required fields
    if (!sessionId || !prompt) {
      return res.status(400).json({
        success: false,
        error: 'Session ID and prompt are required'
      });
    }

    // Create new AI interaction
    const interaction = new AIInteraction({
      patientId: patientId || null,
      userId: req.user.id,
      sessionId,
      type: type || 'general',
      provider: provider || 'gemini',
      model: model || 'gemini-pro',
      prompt,
      context: context || {},
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        source: 'web',
        language: metadata?.language || 'en',
        timezone: metadata?.timezone,
        ...metadata
      },
      status: 'pending'
    });

    await interaction.save();

    // Here you would typically call the AI service
    // For demo purposes, we'll simulate a response
    setTimeout(async () => {
      try {
        const mockResponse = generateMockAIResponse(prompt, type);
        const mockUsage = {
          promptTokens: Math.floor(prompt.length / 4),
          completionTokens: Math.floor(mockResponse.length / 4),
          cost: Math.floor(Math.random() * 100) // cents
        };
        const responseTime = Math.floor(Math.random() * 3000) + 500; // 500-3500ms

        await interaction.markAsCompleted(mockResponse, mockUsage, responseTime);
      } catch (error) {
        await interaction.markAsFailed(error);
      }
    }, 1000);

    res.status(201).json({
      success: true,
      data: interaction,
      message: 'AI interaction created successfully'
    });
  } catch (error) {
    console.error('Error creating AI interaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create AI interaction'
    });
  }
});

// @route   PUT /api/ai-interactions/:id/feedback
// @desc    Add feedback to an AI interaction
// @access  Private
router.put('/:id/feedback', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { helpful, rating, comments } = req.body;

    const interaction = await AIInteraction.findById(id);

    if (!interaction) {
      return res.status(404).json({
        success: false,
        error: 'AI interaction not found'
      });
    }

    await interaction.addFeedback({
      helpful,
      rating,
      comments
    });

    res.json({
      success: true,
      data: interaction,
      message: 'Feedback added successfully'
    });
  } catch (error) {
    console.error('Error adding feedback:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add feedback'
    });
  }
});

// @route   PUT /api/ai-interactions/:id/performance
// @desc    Add performance metrics to an AI interaction
// @access  Private
router.put('/:id/performance', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { quality, relevance, accuracy } = req.body;

    const interaction = await AIInteraction.findById(id);

    if (!interaction) {
      return res.status(404).json({
        success: false,
        error: 'AI interaction not found'
      });
    }

    await interaction.addPerformanceMetrics({
      quality,
      relevance,
      accuracy
    });

    res.json({
      success: true,
      data: interaction,
      message: 'Performance metrics added successfully'
    });
  } catch (error) {
    console.error('Error adding performance metrics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add performance metrics'
    });
  }
});

// @route   GET /api/ai-interactions/analytics
// @desc    Get AI interaction analytics
// @access  Private
router.get('/analytics', auth, async (req, res) => {
  try {
    const { startDate, endDate, provider, type, status } = req.query;

    const filters = {};
    if (provider) filters.provider = provider;
    if (type) filters.type = type;
    if (status) filters.status = status;

    const analytics = await AIInteraction.getAnalytics(startDate, endDate, filters);

    res.json({
      success: true,
      data: analytics[0] || {
        totalInteractions: 0,
        completedInteractions: 0,
        failedInteractions: 0,
        successRate: 0,
        totalTokens: 0,
        totalCost: 0,
        avgResponseTime: 0,
        avgQuality: 0,
        avgRelevance: 0,
        avgAccuracy: 0,
        helpfulnessRate: 0,
        uniqueUsers: 0,
        uniquePatients: 0,
        providerBreakdown: [],
        typeBreakdown: []
      }
    });
  } catch (error) {
    console.error('Error fetching AI analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch AI analytics'
    });
  }
});

// @route   GET /api/ai-interactions/top-queries
// @desc    Get top AI queries
// @access  Private
router.get('/top-queries', auth, async (req, res) => {
  try {
    const { limit = 10, startDate, endDate } = req.query;

    const topQueries = await AIInteraction.getTopQueries(parseInt(limit), startDate, endDate);

    res.json({
      success: true,
      data: topQueries
    });
  } catch (error) {
    console.error('Error fetching top queries:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch top queries'
    });
  }
});

// @route   DELETE /api/ai-interactions/:id
// @desc    Archive an AI interaction
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const interaction = await AIInteraction.findById(id);

    if (!interaction) {
      return res.status(404).json({
        success: false,
        error: 'AI interaction not found'
      });
    }

    interaction.isArchived = true;
    await interaction.save();

    res.json({
      success: true,
      message: 'AI interaction archived successfully'
    });
  } catch (error) {
    console.error('Error archiving AI interaction:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to archive AI interaction'
    });
  }
});

// Helper function to generate mock AI responses
function generateMockAIResponse(prompt, type) {
  const responses = {
    treatment: [
      "Based on the patient's condition, I recommend a combination of manual therapy and therapeutic exercises. Focus on improving range of motion and strengthening the affected area.",
      "Consider implementing a progressive loading program with emphasis on functional movements. Monitor pain levels and adjust intensity accordingly.",
      "The treatment plan should include pain management strategies, mobility exercises, and gradual return to functional activities."
    ],
    exercise: [
      "For this exercise, consider modifying the range of motion based on the patient's current limitations. Start with 2 sets of 10 repetitions.",
      "This exercise can be progressed by adding resistance or increasing the hold time. Ensure proper form is maintained throughout.",
      "Consider alternative exercises if the patient experiences pain. Focus on pain-free range of motion initially."
    ],
    progress: [
      "The patient shows good improvement in range of motion and strength. Continue with the current program and consider progression.",
      "Progress is within expected parameters. The adherence rate is excellent, which contributes to positive outcomes.",
      "Consider reassessing goals and adjusting the treatment plan based on current progress and patient feedback."
    ],
    documentation: [
      "For documentation, include objective measurements, patient-reported outcomes, and functional improvements observed.",
      "Ensure all treatment sessions are properly documented with clear goals, interventions used, and patient response.",
      "Include any modifications made to the treatment plan and the rationale behind these changes."
    ],
    general: [
      "I'm here to help with any physical therapy related questions. Please provide more specific details about what you'd like to know.",
      "Based on current evidence-based practice, I can provide recommendations for treatment approaches and exercise modifications.",
      "Feel free to ask about specific conditions, treatment techniques, or patient management strategies."
    ]
  };

  const typeResponses = responses[type] || responses.general;
  return typeResponses[Math.floor(Math.random() * typeResponses.length)];
}

module.exports = router;
