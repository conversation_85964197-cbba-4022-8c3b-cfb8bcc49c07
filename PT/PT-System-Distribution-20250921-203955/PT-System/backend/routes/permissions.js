const express = require('express');
const {
  getUserPermissions,
  updateUserPermissions,
  getRolePermissions,
  applyDefaultPermissions,
  getPermissionCategories
} = require('../controllers/permissions');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protect all routes
router.use(protect);

// Get permission categories with descriptions (English/Arabic)
router.get('/categories', getPermissionCategories);

// Get default permissions for a specific role
router.get('/role/:role', getRolePermissions);

// Get user permissions
router.get('/user/:userId', getUserPermissions);

// Update user permissions (Admin only)
router.put('/user/:userId', authorize('admin'), updateUserPermissions);

// Apply default permissions to user (Admin only)
router.post('/user/:userId/apply-defaults', authorize('admin'), applyDefaultPermissions);

module.exports = router;
