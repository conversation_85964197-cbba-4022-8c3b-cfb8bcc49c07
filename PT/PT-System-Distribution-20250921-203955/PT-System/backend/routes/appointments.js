const express = require('express');
const {
  getAppointments,
  getAppointment,
  createAppointment,
  updateAppointment,
  deleteAppointment,
  cancelAppointment,
  rescheduleAppointment,
  completeAppointment,
  getAppointmentsByDate,
  getAppointmentsByPatient,
  getAppointmentsByTherapist,
  checkAvailability,
  getUpcomingAppointments,
  sendReminder,
  getAppointmentStats
} = require('../controllers/appointments');

const { protect, authorize, checkPermission, checkPatientAccess } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /appointments:
 *   get:
 *     summary: Get all appointments
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of appointments per page
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by specific date
 *       - in: query
 *         name: therapist
 *         schema:
 *           type: string
 *         description: Filter by therapist ID
 *       - in: query
 *         name: patient
 *         schema:
 *           type: string
 *         description: Filter by patient ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [scheduled, confirmed, in_progress, completed, cancelled, no_show, rescheduled]
 *         description: Filter by appointment status
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [consultation, treatment, follow_up, assessment, group_session]
 *         description: Filter by appointment type
 *     responses:
 *       200:
 *         description: List of appointments
 *   post:
 *     summary: Create a new appointment
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Appointment'
 *     responses:
 *       201:
 *         description: Appointment created successfully
 *       400:
 *         description: Validation error or scheduling conflict
 */

// Protect all routes
router.use(protect);

// Get all appointments and create new appointment
router
  .route('/')
  .get(checkPermission('view_appointments'), getAppointments)
  .post(checkPermission('create_appointments'), createAppointment);

/**
 * @swagger
 * /appointments/stats:
 *   get:
 *     summary: Get appointment statistics
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Appointment statistics
 */
router.get('/stats', checkPermission('view_appointments'), getAppointmentStats);

/**
 * @swagger
 * /appointments/upcoming:
 *   get:
 *     summary: Get upcoming appointments
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 7
 *         description: Number of days to look ahead
 *     responses:
 *       200:
 *         description: Upcoming appointments
 */
router.get('/upcoming', checkPermission('view_appointments'), getUpcomingAppointments);

/**
 * @swagger
 * /appointments/availability:
 *   get:
 *     summary: Check therapist availability
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: therapist
 *         required: true
 *         schema:
 *           type: string
 *         description: Therapist ID
 *       - in: query
 *         name: date
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Date to check availability
 *     responses:
 *       200:
 *         description: Availability information
 */
router.get('/availability', checkPermission('view_appointments'), checkAvailability);

/**
 * @swagger
 * /appointments/date/{date}:
 *   get:
 *     summary: Get appointments by date
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: date
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *     responses:
 *       200:
 *         description: Appointments for the specified date
 */
router.get('/date/:date', checkPermission('view_appointments'), getAppointmentsByDate);

/**
 * @swagger
 * /appointments/patient/{patientId}:
 *   get:
 *     summary: Get appointments by patient
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient's appointments
 */
router.get('/patient/:patientId', checkPermission('view_appointments'), getAppointmentsByPatient);

/**
 * @swagger
 * /appointments/therapist/{therapistId}:
 *   get:
 *     summary: Get appointments by therapist
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: therapistId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Therapist's appointments
 */
router.get('/therapist/:therapistId', checkPermission('view_appointments'), getAppointmentsByTherapist);

/**
 * @swagger
 * /appointments/{id}:
 *   get:
 *     summary: Get appointment by ID
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Appointment details
 *       404:
 *         description: Appointment not found
 *   put:
 *     summary: Update appointment
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Appointment'
 *     responses:
 *       200:
 *         description: Appointment updated successfully
 *       404:
 *         description: Appointment not found
 *   delete:
 *     summary: Delete appointment
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Appointment deleted successfully
 *       404:
 *         description: Appointment not found
 */
router
  .route('/:id')
  .get(checkPermission('view_appointments'), getAppointment)
  .put(checkPermission('edit_appointments'), updateAppointment)
  .delete(checkPermission('delete_appointments'), deleteAppointment);

/**
 * @swagger
 * /appointments/{id}/cancel:
 *   put:
 *     summary: Cancel appointment
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reason:
 *                 type: string
 *                 description: Cancellation reason
 *     responses:
 *       200:
 *         description: Appointment cancelled successfully
 */
router.put('/:id/cancel', checkPermission('edit_appointments'), cancelAppointment);

/**
 * @swagger
 * /appointments/{id}/reschedule:
 *   put:
 *     summary: Reschedule appointment
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - date
 *               - startTime
 *               - endTime
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *               startTime:
 *                 type: string
 *               endTime:
 *                 type: string
 *               reason:
 *                 type: string
 *     responses:
 *       200:
 *         description: Appointment rescheduled successfully
 */
router.put('/:id/reschedule', checkPermission('edit_appointments'), rescheduleAppointment);

/**
 * @swagger
 * /appointments/{id}/complete:
 *   put:
 *     summary: Complete appointment
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sessionNotes:
 *                 type: string
 *               progressNotes:
 *                 type: string
 *               nextSteps:
 *                 type: string
 *               homeExercises:
 *                 type: array
 *                 items:
 *                   type: string
 *               followUpRequired:
 *                 type: boolean
 *               followUpDate:
 *                 type: string
 *                 format: date
 *               patientSatisfaction:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *     responses:
 *       200:
 *         description: Appointment completed successfully
 */
router.put('/:id/complete', checkPermission('edit_appointments'), completeAppointment);

/**
 * @swagger
 * /appointments/{id}/reminder:
 *   post:
 *     summary: Send appointment reminder
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [email, sms, push]
 *                 default: email
 *     responses:
 *       200:
 *         description: Reminder sent successfully
 */
router.post('/:id/reminder', checkPermission('edit_appointments'), sendReminder);

module.exports = router;
