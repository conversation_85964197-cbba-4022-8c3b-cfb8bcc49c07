const express = require('express');
const router = express.Router();
const Communication = require('../models/Communication');
const { protect: auth } = require('../middleware/auth');

// @route   GET /api/communication/:patientId
// @desc    Get communication history for a patient
// @access  Private
router.get('/:patientId', auth, async (req, res) => {
  try {
    const { patientId } = req.params;
    const { limit = 20, page = 1, status, urgency } = req.query;

    const query = { patientId };
    if (status) query.status = status;
    if (urgency) query.urgency = urgency;

    const communications = await Communication.find(query)
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .populate('senderId', 'name email')
      .populate('patientId', 'name nameAr phone email')
      .lean();

    const total = await Communication.countDocuments(query);

    res.json({
      success: true,
      data: communications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching communication history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch communication history'
    });
  }
});

// @route   POST /api/communication
// @desc    Send a new message
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    const {
      patientId,
      channels,
      subject,
      message,
      messageAr,
      urgency,
      scheduledTime,
      template,
      templateData
    } = req.body;

    // Validate required fields
    if (!patientId || !channels || !Array.isArray(channels) || channels.length === 0 || !message) {
      return res.status(400).json({
        success: false,
        error: 'Patient ID, channels, and message are required'
      });
    }

    // Validate channels
    const validChannels = ['email', 'sms', 'whatsapp', 'push'];
    const invalidChannels = channels.filter(channel => !validChannels.includes(channel));
    if (invalidChannels.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid channels: ${invalidChannels.join(', ')}`
      });
    }

    // Create new communication
    const communication = new Communication({
      patientId,
      senderId: req.user.id,
      channels,
      subject,
      message,
      messageAr,
      urgency: urgency || 'medium',
      scheduledTime: scheduledTime ? new Date(scheduledTime) : null,
      template: template || 'custom',
      templateData: templateData ? new Map(Object.entries(templateData)) : new Map(),
      metadata: {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
        source: 'web'
      }
    });

    // If not scheduled, mark as sent immediately (in real app, this would trigger actual sending)
    if (!scheduledTime) {
      communication.status = 'sent';
      communication.sentAt = new Date();
      
      // Simulate delivery results for demo
      communication.deliveryResults = channels.map(channel => ({
        channel,
        status: 'sent',
        timestamp: new Date(),
        externalId: `demo_${Date.now()}_${channel}`
      }));
    }

    await communication.save();

    // Populate sender and patient info
    await communication.populate('senderId', 'name email');
    await communication.populate('patientId', 'name nameAr phone email');

    res.status(201).json({
      success: true,
      data: communication,
      message: scheduledTime ? 'Message scheduled successfully' : 'Message sent successfully'
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message'
    });
  }
});

// @route   PUT /api/communication/:id/status
// @desc    Update message status
// @access  Private
router.put('/:id/status', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, channel, externalId, errorMessage } = req.body;

    const communication = await Communication.findById(id);

    if (!communication) {
      return res.status(404).json({
        success: false,
        error: 'Communication not found'
      });
    }

    if (status === 'sent') {
      await communication.markAsSent(channel, externalId);
    } else if (status === 'delivered') {
      await communication.markAsDelivered(channel, externalId);
    } else if (status === 'failed') {
      await communication.markAsFailed(channel, errorMessage, externalId);
    } else {
      communication.status = status;
      await communication.save();
    }

    res.json({
      success: true,
      data: communication,
      message: 'Status updated successfully'
    });
  } catch (error) {
    console.error('Error updating communication status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update status'
    });
  }
});

// @route   POST /api/communication/:id/response
// @desc    Add response to a message
// @access  Private
router.post('/:id/response', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const { message, channel } = req.body;

    if (!message || !channel) {
      return res.status(400).json({
        success: false,
        error: 'Message and channel are required'
      });
    }

    const communication = await Communication.findById(id);

    if (!communication) {
      return res.status(404).json({
        success: false,
        error: 'Communication not found'
      });
    }

    await communication.addResponse(message, channel);

    res.json({
      success: true,
      data: communication,
      message: 'Response added successfully'
    });
  } catch (error) {
    console.error('Error adding response:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to add response'
    });
  }
});

// @route   GET /api/communication/scheduled
// @desc    Get pending scheduled messages
// @access  Private
router.get('/scheduled', auth, async (req, res) => {
  try {
    const scheduledMessages = await Communication.getPendingScheduled();

    res.json({
      success: true,
      data: scheduledMessages
    });
  } catch (error) {
    console.error('Error fetching scheduled messages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch scheduled messages'
    });
  }
});

// @route   GET /api/communication/analytics
// @desc    Get communication analytics
// @access  Private
router.get('/analytics', auth, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    const analytics = await Communication.getAnalytics(startDate, endDate);

    res.json({
      success: true,
      data: analytics[0] || {
        totalMessages: 0,
        sentMessages: 0,
        deliveredMessages: 0,
        failedMessages: 0,
        emailCount: 0,
        smsCount: 0,
        whatsappCount: 0,
        pushCount: 0,
        uniquePatients: 0,
        deliveryRate: 0
      }
    });
  } catch (error) {
    console.error('Error fetching communication analytics:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch communication analytics'
    });
  }
});

// @route   GET /api/communication/templates
// @desc    Get message templates
// @access  Private
router.get('/templates', auth, async (req, res) => {
  try {
    const templates = [
      {
        id: 'appointment_reminder',
        name: 'Appointment Reminder',
        nameAr: 'تذكير بالموعد',
        subject: 'Appointment Reminder - {{appointmentDate}}',
        subjectAr: 'تذكير بالموعد - {{appointmentDate}}',
        message: 'Dear {{patientName}}, this is a reminder for your appointment on {{appointmentDate}} at {{appointmentTime}}. Please arrive 15 minutes early.',
        messageAr: 'عزيزي {{patientName}}، هذا تذكير بموعدك في {{appointmentDate}} في {{appointmentTime}}. يرجى الحضور قبل 15 دقيقة.',
        variables: ['patientName', 'appointmentDate', 'appointmentTime'],
        channels: ['email', 'sms', 'whatsapp']
      },
      {
        id: 'exercise_reminder',
        name: 'Exercise Reminder',
        nameAr: 'تذكير بالتمارين',
        subject: 'Time for Your Exercises!',
        subjectAr: 'حان وقت التمارين!',
        message: 'Hi {{patientName}}, don\'t forget to do your prescribed exercises today. Your progress depends on consistency!',
        messageAr: 'مرحبا {{patientName}}، لا تنس أداء التمارين المقررة اليوم. تقدمك يعتمد على الانتظام!',
        variables: ['patientName'],
        channels: ['sms', 'push', 'whatsapp']
      },
      {
        id: 'progress_update',
        name: 'Progress Update',
        nameAr: 'تحديث التقدم',
        subject: 'Your Progress Update',
        subjectAr: 'تحديث تقدمك',
        message: 'Dear {{patientName}}, we\'re pleased to share your latest progress report. You\'ve completed {{completedSessions}} sessions with {{adherenceRate}}% adherence.',
        messageAr: 'عزيزي {{patientName}}، يسعدنا مشاركة تقرير تقدمك الأخير. لقد أكملت {{completedSessions}} جلسة بمعدل التزام {{adherenceRate}}%.',
        variables: ['patientName', 'completedSessions', 'adherenceRate'],
        channels: ['email', 'whatsapp']
      },
      {
        id: 'follow_up',
        name: 'Follow-up',
        nameAr: 'متابعة',
        subject: 'Follow-up on Your Treatment',
        subjectAr: 'متابعة علاجك',
        message: 'Hello {{patientName}}, how are you feeling after your last session? Please let us know if you have any concerns.',
        messageAr: 'مرحبا {{patientName}}، كيف تشعر بعد جلستك الأخيرة؟ يرجى إعلامنا إذا كان لديك أي مخاوف.',
        variables: ['patientName'],
        channels: ['email', 'sms', 'whatsapp']
      }
    ];

    res.json({
      success: true,
      data: templates
    });
  } catch (error) {
    console.error('Error fetching templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch templates'
    });
  }
});

// @route   DELETE /api/communication/:id
// @desc    Delete a communication record
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const communication = await Communication.findById(id);

    if (!communication) {
      return res.status(404).json({
        success: false,
        error: 'Communication not found'
      });
    }

    await communication.deleteOne();

    res.json({
      success: true,
      message: 'Communication deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting communication:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete communication'
    });
  }
});

module.exports = router;
