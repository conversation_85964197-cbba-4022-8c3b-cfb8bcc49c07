const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const PDFDocument = require('pdfkit');
const { protect } = require('../middleware/auth');

// Patient and Family Education Schema
const patientFamilyEducationSchema = new mongoose.Schema({
  // Document Metadata
  documentNumber: { type: String, required: true },
  issueDate: { type: Date, required: true },
  version: { type: String, default: '01' },
  reviewNumber: { type: String, default: '01' },
  
  // Assessment Type
  assessmentType: { 
    type: String, 
    enum: ['initial', 'reassessment', 'discharge'], 
    required: true 
  },
  
  // Patient Learning Preferences and Barriers
  patientLearningPreference: [String],
  patientLearningOther: String,
  patientBarriers: [String],
  patientBarriersOther: String,
  
  // Caregiver Learning Preferences and Barriers
  caregiverLearningPreference: [String],
  caregiverLearningOther: String,
  caregiverBarriers: [String],
  caregiverBarriersOther: String,
  
  // Patient Self-Care
  selfCareCapability: { 
    type: String, 
    enum: ['dependent', 'independent', 'need_assistant', 'not_applicable'],
    required: true 
  },
  selfCareCapabilityReason: String,
  selfCareMotivation: { 
    type: String, 
    enum: ['poor', 'moderate', 'high', 'not_applicable'],
    required: true 
  },
  selfCareMotivationReason: String,
  comments: String,
  
  // Educational Needs
  educationalNeeds: [String],
  educationalNeedsOther: String,
  
  // Given Education
  teachingMethod: [String],
  teachingTool: [String],
  
  // Evaluation
  evaluation: [String],
  
  // Additional Questions
  patientAttend: { 
    type: String, 
    enum: ['school', 'work', 'not_applicable'],
    required: true 
  },
  problemsAttending: { 
    type: String, 
    enum: ['yes', 'no', 'not_applicable'],
    required: true 
  },
  literacySkills: { 
    type: String, 
    enum: ['yes', 'no'],
    required: true 
  },
  
  // Post-Discharge Care
  careProviderAfterDischarge: [String],
  careProviderOther: String,
  
  // Plans and Comments
  plansComments: String,
  
  // Therapist Signature
  therapistName: { type: String, required: true },
  signature: { type: String, required: true },
  badgeNo: { type: String, required: true },
  date: { type: Date, required: true },
  
  // Metadata
  submittedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  submittedAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const PatientFamilyEducation = mongoose.model('PatientFamilyEducation', patientFamilyEducationSchema);

// Middleware to bypass auth in development
const optionalAuth = (req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    req.user = req.user || {
      id: '507f1f77bcf86cd799439011',
      role: 'therapist',
      name: 'Development User'
    };
  }
  next();
};

// Test route
router.get('/test', (req, res) => {
  res.json({
    message: 'Patient and Family Education API is working',
    timestamp: new Date().toISOString()
  });
});

// Public route to create education forms (no auth for development)
router.post('/public', async (req, res) => {
  try {
    const educationData = {
      ...req.body,
      submittedBy: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
      submittedAt: new Date()
    };

    // Basic validation
    if (!educationData.assessmentType || !educationData.therapistName || !educationData.signature) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: assessmentType, therapistName, signature'
      });
    }

    const education = new PatientFamilyEducation(educationData);
    await education.save();

    res.status(201).json({
      success: true,
      message: 'Patient and Family Education form created successfully',
      education
    });
  } catch (error) {
    console.error('Error creating patient and family education form:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating patient and family education form',
      error: error.message
    });
  }
});

// Get all education forms
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, patientId, search } = req.query;

    const query = {};
    if (patientId) query.patientId = patientId;
    if (search) {
      query.$or = [
        { therapistName: new RegExp(search, 'i') },
        { assessmentType: new RegExp(search, 'i') }
      ];
    }

    const educationForms = await PatientFamilyEducation.find(query)
      .populate('submittedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean();

    const total = await PatientFamilyEducation.countDocuments(query);

    res.json({
      success: true,
      educationForms: educationForms || [],
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total: total || 0
    });
  } catch (error) {
    console.error('Error fetching patient and family education forms:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Generate PDF
router.post('/pdf', optionalAuth, async (req, res) => {
  try {
    const educationData = req.body;
    
    // Create PDF document
    const doc = new PDFDocument({ margin: 50, size: 'A4' });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="patient-family-education-${educationData.therapistName}-${educationData.date}.pdf"`);
    
    // Pipe PDF to response
    doc.pipe(res);

    // Helper function to format arrays
    const formatArray = (arr, otherField = '') => {
      if (!arr || arr.length === 0) return 'None selected';
      let result = arr.filter(item => item !== 'others').join(', ');
      if (arr.includes('others') && otherField) {
        result += result ? `, ${otherField}` : otherField;
      }
      return result || 'None selected';
    };

    // Header
    doc.fontSize(16).font('Helvetica-Bold');
    doc.text('PATIENT AND FAMILY EDUCATION FORM', { align: 'center' });
    doc.text('FOR PHYSICAL THERAPIST', { align: 'center' });
    doc.moveDown();

    // Document metadata
    doc.fontSize(10).font('Helvetica');
    doc.text(`Document Number: ${educationData.documentNumber}`, 50, 120);
    doc.text(`Issue Date: ${educationData.issueDate}`, 200, 120);
    doc.text(`Version: ${educationData.version}`, 350, 120);
    doc.text(`Review Number: ${educationData.reviewNumber}`, 450, 120);
    doc.moveDown();

    let currentY = 150;

    // Assessment Type
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('ASSESSMENT TYPE', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    const assessmentTypeMap = {
      'initial': 'Initial Assessment',
      'reassessment': 'Re-Assessment',
      'discharge': 'Discharge Assessment'
    };
    doc.text(`Assessment Type: ${assessmentTypeMap[educationData.assessmentType] || educationData.assessmentType}`, 50, currentY);
    currentY += 30;

    // Patient Learning Preferences and Barriers
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT LEARNING PREFERENCES AND BARRIERS', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Learning Preferences: ${formatArray(educationData.patientLearningPreference, educationData.patientLearningOther)}`, 50, currentY, { width: 500 });
    currentY += 20;
    doc.text(`Barriers: ${formatArray(educationData.patientBarriers, educationData.patientBarriersOther)}`, 50, currentY, { width: 500 });
    currentY += 30;

    // Caregiver Learning Preferences and Barriers
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('CAREGIVER LEARNING PREFERENCES AND BARRIERS', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Learning Preferences: ${formatArray(educationData.caregiverLearningPreference, educationData.caregiverLearningOther)}`, 50, currentY, { width: 500 });
    currentY += 20;
    doc.text(`Barriers: ${formatArray(educationData.caregiverBarriers, educationData.caregiverBarriersOther)}`, 50, currentY, { width: 500 });
    currentY += 30;

    // Patient Self-Care
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT SELF-CARE ASSESSMENT', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    const capabilityMap = {
      'dependent': 'Dependent',
      'independent': 'Independent',
      'need_assistant': 'Need Assistant',
      'not_applicable': 'Not Applicable'
    };
    const motivationMap = {
      'poor': 'Poor',
      'moderate': 'Moderate',
      'high': 'High',
      'not_applicable': 'Not Applicable'
    };
    
    doc.text(`Self-Care Capability: ${capabilityMap[educationData.selfCareCapability] || educationData.selfCareCapability}`, 50, currentY);
    currentY += 15;
    if (educationData.selfCareCapabilityReason) {
      doc.text(`Reason: ${educationData.selfCareCapabilityReason}`, 70, currentY, { width: 450 });
      currentY += 15;
    }
    
    doc.text(`Self-Care Motivation: ${motivationMap[educationData.selfCareMotivation] || educationData.selfCareMotivation}`, 50, currentY);
    currentY += 15;
    if (educationData.selfCareMotivationReason) {
      doc.text(`Reason: ${educationData.selfCareMotivationReason}`, 70, currentY, { width: 450 });
      currentY += 15;
    }
    
    if (educationData.comments) {
      doc.text(`Comments: ${educationData.comments}`, 50, currentY, { width: 500 });
      currentY += 20;
    }
    currentY += 10;

    // Educational Needs
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('EDUCATIONAL NEEDS', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Educational Needs: ${formatArray(educationData.educationalNeeds, educationData.educationalNeedsOther)}`, 50, currentY, { width: 500 });
    currentY += 30;

    // Given Education
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('GIVEN EDUCATION', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Teaching Method: ${formatArray(educationData.teachingMethod)}`, 50, currentY, { width: 500 });
    currentY += 15;
    doc.text(`Teaching Tool: ${formatArray(educationData.teachingTool)}`, 50, currentY, { width: 500 });
    currentY += 30;

    // Evaluation
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('EVALUATION', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Evaluation: ${formatArray(educationData.evaluation)}`, 50, currentY, { width: 500 });
    currentY += 30;

    // Additional Questions
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('ADDITIONAL QUESTIONS', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    const attendMap = {
      'school': 'School',
      'work': 'Work',
      'not_applicable': 'NOT APPLICABLE'
    };
    const yesNoMap = {
      'yes': 'Yes',
      'no': 'No',
      'not_applicable': 'NOT APPLICABLE'
    };
    
    doc.text(`Patient Attend: ${attendMap[educationData.patientAttend] || educationData.patientAttend}`, 50, currentY);
    currentY += 15;
    doc.text(`Problems Attending School/Work: ${yesNoMap[educationData.problemsAttending] || educationData.problemsAttending}`, 50, currentY);
    currentY += 15;
    doc.text(`Literacy Skills: ${yesNoMap[educationData.literacySkills] || educationData.literacySkills}`, 50, currentY);
    currentY += 30;

    // Post-Discharge Care
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('WHO WILL PROVIDE CARE AFTER DISCHARGE?', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Care Provider: ${formatArray(educationData.careProviderAfterDischarge, educationData.careProviderOther)}`, 50, currentY, { width: 500 });
    currentY += 30;

    // Plans and Comments
    if (educationData.plansComments) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('PLANS AND COMMENTS', 50, currentY, { underline: true });
      currentY += 20;
      
      doc.fontSize(10).font('Helvetica');
      doc.text(educationData.plansComments, 50, currentY, { width: 500 });
      currentY += 30;
    }

    // Therapist Signature
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('THERAPIST SIGNATURE', 50, currentY, { underline: true });
    currentY += 20;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Therapist Name: ${educationData.therapistName}`, 50, currentY);
    currentY += 15;
    doc.text(`Signature: ${educationData.signature}`, 50, currentY);
    currentY += 15;
    doc.text(`Badge No.: ${educationData.badgeNo}`, 50, currentY);
    currentY += 15;
    doc.text(`Date: ${educationData.date}`, 50, currentY);

    // Footer
    doc.fontSize(8).font('Helvetica');
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 50, doc.page.height - 50);
    doc.text('PhysioFlow - Patient and Family Education Form', { align: 'center' });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ message: 'Error generating PDF' });
  }
});

module.exports = router;
