const express = require('express');
const {
  createClinicalIndicator,
  getPatientClinicalIndicators,
  getClinicalIndicator,
  updateClinicalIndicator,
  deleteClinicalIndicator,
  getPatientProgressAnalytics,
  getClinicalIndicatorsSummary
} = require('../controllers/clinicalIndicators');

const { protect, authorize } = require('../middleware/auth');
const { validateClinicalIndicator } = require('../middleware/validation');
const {
  requireTherapistPermissions,
  requireAnalyticsPermissions,
  requireClinicalAssessmentAccess
} = require('../middleware/checkPermissions');

const router = express.Router();

// Protect all routes
router.use(protect);

// Clinical Indicators CRUD
router
  .route('/')
  .post(
    requireTherapistPermissions.clinicalIndicators,
    validateClinicalIndicator,
    createClinicalIndicator
  );

router
  .route('/:id')
  .get(requireClinicalAssessmentAccess, getClinicalIndicator)
  .put(
    requireTherapistPermissions.clinicalIndicators,
    updateClinicalIndicator
  )
  .delete(
    requireTherapistPermissions.clinicalIndicators,
    deleteClinicalIndicator
  );

// Patient-specific routes
router
  .route('/patient/:patientId')
  .get(requireClinicalAssessmentAccess, getPatientClinicalIndicators);

router
  .route('/patient/:patientId/progress')
  .get(requireAnalyticsPermissions.clinicalProgressTracking, getPatientProgressAnalytics);

// Analytics routes
router
  .route('/analytics/summary')
  .get(requireAnalyticsPermissions.outcomeMetrics, getClinicalIndicatorsSummary);

module.exports = router;
