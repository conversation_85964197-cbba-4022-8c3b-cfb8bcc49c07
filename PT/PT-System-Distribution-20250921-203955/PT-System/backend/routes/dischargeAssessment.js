const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const DischargeAssessment = require('../models/DischargeAssessment');
const Patient = require('../models/Patient');
const { protect } = require('../middleware/auth');
const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

// Test route to check if the API is working (no auth required)
router.get('/test', (req, res) => {
  res.json({
    message: 'Discharge Assessment API is working',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Public route to get assessments (no auth for development)
router.get('/public', async (req, res) => {
  try {
    const assessments = await DischargeAssessment.find({})
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    res.json({
      success: true,
      message: 'Public discharge assessments retrieved',
      assessments: assessments || [],
      total: assessments.length
    });
  } catch (error) {
    console.error('Error fetching public discharge assessments:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Public route to create assessments (no auth for development)
router.post('/public', async (req, res) => {
  try {
    const assessmentData = {
      ...req.body,
      submittedBy: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'), // Valid ObjectId
      submittedAt: new Date()
    };

    // Basic validation
    if (!assessmentData.patientName || !assessmentData.mrNumber || !assessmentData.dischargeDate) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: patientName, mrNumber, dischargeDate'
      });
    }

    const assessment = new DischargeAssessment(assessmentData);
    await assessment.save();

    res.status(201).json({
      success: true,
      message: 'Discharge assessment created successfully',
      assessment
    });
  } catch (error) {
    console.error('Error creating discharge assessment:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating discharge assessment',
      error: error.message
    });
  }
});

// Create sample data for testing (no auth required)
router.get('/create-sample', async (req, res) => {
  try {
    const sampleAssessment = new DischargeAssessment({
      patientName: 'John Doe',
      mrNumber: 'MR001',
      dischargeDate: new Date(),
      diagnosis: 'Lower back pain with muscle spasms',
      admissionDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      physician: 'Dr. Smith',
      totalVisits: 12,
      noShowCancellations: 2,
      evaluationAddendum: ['Functional', 'Lumbar', 'Pain'],
      treatmentReceived: 'Manual therapy, therapeutic exercises, pain management education',
      gait: 'Improved from antalgic to normal gait pattern',
      balanceCoordination: 'Balance improved, no longer requires assistive device',
      pain: 'Pain reduced from 8/10 to 2/10 on VAS scale',
      functionalAssessment: 'Patient able to return to work with modifications',
      patientCaregiverTraining: 'Home exercise program provided and demonstrated',
      dischargeReasons: ['Goals Met'],
      continueHEP: true,
      continueHEPDetails: 'Continue with strengthening exercises 3x/week',
      therapistSignature: 'Jane Smith, PT',
      therapistBadgeNo: 'PT001',
      submittedBy: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011') // Valid ObjectId
    });

    await sampleAssessment.save();
    res.json({
      message: 'Sample discharge assessment created successfully',
      assessment: sampleAssessment
    });
  } catch (error) {
    console.error('Error creating sample data:', error);
    res.status(500).json({
      message: 'Error creating sample data',
      error: error.message
    });
  }
});

// Middleware to bypass auth in development
const optionalAuth = (req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    // In development, create a mock user if no auth
    req.user = req.user || {
      id: '507f1f77bcf86cd799439011',
      role: 'therapist',
      name: 'Development User'
    };
  }
  next();
};

// Get all discharge assessments
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, patientId, therapist, search } = req.query;

    const query = {};
    if (patientId) query.patientId = patientId;
    if (therapist) query.therapistSignature = new RegExp(therapist, 'i');
    if (search) {
      query.$or = [
        { patientName: new RegExp(search, 'i') },
        { mrNumber: new RegExp(search, 'i') },
        { therapistSignature: new RegExp(search, 'i') }
      ];
    }

    const assessments = await DischargeAssessment.find(query)
      .populate('patientId', 'name mrNumber')
      .populate('submittedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean(); // Use lean() for better performance

    const total = await DischargeAssessment.countDocuments(query);

    res.json({
      success: true,
      assessments: assessments || [],
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total: total || 0
    });
  } catch (error) {
    console.error('Error fetching discharge assessments:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Get discharge assessment by ID
router.get('/:id', protect, async (req, res) => {
  try {
    const assessment = await DischargeAssessment.findById(req.params.id)
      .populate('patientId', 'name mrNumber')
      .populate('submittedBy', 'name');

    if (!assessment) {
      return res.status(404).json({ message: 'Discharge assessment not found' });
    }

    res.json(assessment);
  } catch (error) {
    console.error('Error fetching discharge assessment:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Create new discharge assessment
router.post('/', optionalAuth, async (req, res) => {
  try {
    const assessmentData = {
      ...req.body,
      submittedBy: req.user.id,
      submittedAt: new Date()
    };

    // Validate required fields
    const requiredFields = [
      'patientName', 'mrNumber', 'dischargeDate', 'diagnosis', 
      'admissionDate', 'physician', 'treatmentReceived', 
      'patientCaregiverTraining', 'dischargeReasons', 
      'therapistSignature', 'therapistBadgeNo'
    ];

    for (const field of requiredFields) {
      if (!assessmentData[field] || 
          (Array.isArray(assessmentData[field]) && assessmentData[field].length === 0)) {
        return res.status(400).json({ 
          message: `${field} is required`,
          field 
        });
      }
    }

    // Validate dates
    if (new Date(assessmentData.admissionDate) >= new Date(assessmentData.dischargeDate)) {
      return res.status(400).json({ 
        message: 'Discharge date must be after admission date',
        field: 'dischargeDate'
      });
    }

    // Validate follow-up date if provided
    if (assessmentData.followUpDate && new Date(assessmentData.followUpDate) <= new Date()) {
      return res.status(400).json({ 
        message: 'Follow-up date must be in the future',
        field: 'followUpDate'
      });
    }

    // Validate numbers
    if (assessmentData.totalVisits < 0 || assessmentData.noShowCancellations < 0) {
      return res.status(400).json({ 
        message: 'Visit counts must be positive numbers'
      });
    }

    const assessment = new DischargeAssessment(assessmentData);
    await assessment.save();

    // Populate the response
    await assessment.populate('patientId', 'name mrNumber');
    await assessment.populate('submittedBy', 'name');

    res.status(201).json(assessment);
  } catch (error) {
    console.error('Error creating discharge assessment:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors 
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// Update discharge assessment
router.put('/:id', protect, async (req, res) => {
  try {
    const assessment = await DischargeAssessment.findById(req.params.id);

    if (!assessment) {
      return res.status(404).json({ message: 'Discharge assessment not found' });
    }

    // Check if user has permission to update
    if (assessment.submittedBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to update this assessment' });
    }

    const updatedAssessment = await DischargeAssessment.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: new Date() },
      { new: true, runValidators: true }
    ).populate('patientId', 'name mrNumber')
     .populate('submittedBy', 'name');

    res.json(updatedAssessment);
  } catch (error) {
    console.error('Error updating discharge assessment:', error);
    if (error.name === 'ValidationError') {
      return res.status(400).json({ 
        message: 'Validation error',
        errors: error.errors 
      });
    }
    res.status(500).json({ message: 'Server error' });
  }
});

// Delete discharge assessment
router.delete('/:id', protect, async (req, res) => {
  try {
    const assessment = await DischargeAssessment.findById(req.params.id);

    if (!assessment) {
      return res.status(404).json({ message: 'Discharge assessment not found' });
    }

    // Check if user has permission to delete
    if (assessment.submittedBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to delete this assessment' });
    }

    await DischargeAssessment.findByIdAndDelete(req.params.id);
    res.json({ message: 'Discharge assessment deleted successfully' });
  } catch (error) {
    console.error('Error deleting discharge assessment:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Generate PDF
router.post('/pdf', optionalAuth, async (req, res) => {
  try {
    const assessmentData = req.body;
    
    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="discharge-assessment-${assessmentData.patientName}-${assessmentData.dischargeDate}.pdf"`);
    
    // Pipe PDF to response
    doc.pipe(res);

    // Add header
    doc.fontSize(16).font('Helvetica-Bold');
    doc.text('OUTPATIENT DISCHARGE ASSESSMENT', { align: 'center' });
    doc.moveDown();

    // Document metadata
    doc.fontSize(10).font('Helvetica');
    doc.text(`Document Number: ${assessmentData.documentNumber}`, 50, doc.y);
    doc.text(`Issue Date: ${assessmentData.issueDate}`, 200, doc.y - 12);
    doc.text(`Version: ${assessmentData.version}`, 350, doc.y - 12);
    doc.text(`Review Number: ${assessmentData.reviewNumber}`, 450, doc.y - 12);
    doc.moveDown(2);

    // Patient Information
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT INFORMATION', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Patient Name: ${assessmentData.patientName}`);
    doc.text(`MR #: ${assessmentData.mrNumber}`);
    doc.text(`Discharge Date: ${assessmentData.dischargeDate}`);
    doc.text(`Admission Date: ${assessmentData.admissionDate}`);
    doc.text(`Physician: ${assessmentData.physician}`);
    doc.text(`Total Visits: ${assessmentData.totalVisits}`);
    doc.text(`No-show/Cancellations: ${assessmentData.noShowCancellations}`);
    doc.text(`Diagnosis: ${assessmentData.diagnosis}`);
    doc.moveDown();

    // Evaluation Addendum
    if (assessmentData.evaluationAddendum && assessmentData.evaluationAddendum.length > 0) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('EVALUATION ADDENDUM', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      doc.text(`Selected: ${assessmentData.evaluationAddendum.join(', ')}`);
      if (assessmentData.evaluationOther) {
        doc.text(`Other: ${assessmentData.evaluationOther}`);
      }
      doc.moveDown();
    }

    // Treatment Received
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('TREATMENT RECEIVED', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(assessmentData.treatmentReceived || 'Not specified', { width: 500 });
    doc.moveDown();

    // Summary of Progress
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('SUMMARY OF PROGRESS', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    const progressFields = [
      { label: 'Gait', value: assessmentData.gait },
      { label: 'Balance/Coordination', value: assessmentData.balanceCoordination },
      { label: 'Pain', value: assessmentData.pain },
      { label: 'Functional Assessment', value: assessmentData.functionalAssessment },
      { label: 'Environmental Assessment', value: assessmentData.environmentalAssessment },
      { label: 'Activities Limitation', value: assessmentData.activitiesLimitation },
      { label: 'Participate Restriction', value: assessmentData.participateRestriction },
      { label: 'Family Support', value: assessmentData.familySupport },
      { label: 'Assistive Devices', value: assessmentData.assistiveDevices },
      { label: 'Risk Factors', value: assessmentData.riskFactors }
    ];

    progressFields.forEach(field => {
      if (field.value) {
        doc.text(`${field.label}: ${field.value}`, { width: 500 });
        doc.moveDown(0.3);
      }
    });
    doc.moveDown();

    // Patient/Caregiver Training
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT/CAREGIVER TRAINING', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(assessmentData.patientCaregiverTraining || 'Not specified', { width: 500 });
    doc.moveDown();

    // Check if we need a new page
    if (doc.y > 650) {
      doc.addPage();
    }

    // Reason for Discharge
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('REASON FOR DISCHARGE', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Selected: ${assessmentData.dischargeReasons.join(', ')}`);
    if (assessmentData.dischargeOther) {
      doc.text(`Other: ${assessmentData.dischargeOther}`);
    }
    doc.moveDown();

    // Recommendations
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('RECOMMENDATIONS', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    if (assessmentData.continueHEP) {
      doc.text('✓ Continue with HEP as issued');
      if (assessmentData.continueHEPDetails) {
        doc.text(`   Details: ${assessmentData.continueHEPDetails}`);
      }
    }
    if (assessmentData.equipmentNeeds) {
      doc.text(`Equipment needs: ${assessmentData.equipmentNeeds}`);
    }
    if (assessmentData.followUpPhysician) {
      doc.text('✓ Follow up with physician');
      if (assessmentData.followUpPhysicianDetails) {
        doc.text(`   Details: ${assessmentData.followUpPhysicianDetails}`);
      }
    }
    if (assessmentData.recommendationOther) {
      doc.text(`Other: ${assessmentData.recommendationOther}`);
    }
    doc.moveDown();

    // Follow-up Information
    if (assessmentData.followUpFrequency || assessmentData.followUpDate) {
      doc.fontSize(12).font('Helvetica-Bold');
      doc.text('FOLLOW-UP INFORMATION', { underline: true });
      doc.moveDown(0.5);
      
      doc.fontSize(10).font('Helvetica');
      if (assessmentData.followUpFrequency) {
        doc.text(`Frequency: ${assessmentData.followUpFrequency}`);
      }
      if (assessmentData.followUpDate) {
        doc.text(`Date: ${assessmentData.followUpDate}`);
      }
      if (assessmentData.followUpRiskFactors) {
        doc.text(`Risk factors: ${assessmentData.followUpRiskFactors}`);
      }
      if (assessmentData.planReviewedWithFamily) {
        doc.text('✓ Plan reviewed with patient/family');
      }
      doc.moveDown();
    }

    // Signatures
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('SIGNATURES', { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Therapist: ${assessmentData.therapistSignature}`);
    doc.text(`Badge No.: ${assessmentData.therapistBadgeNo}`);
    doc.text(`Date: ${assessmentData.therapistDate}`);
    
    if (assessmentData.comments) {
      doc.moveDown(0.5);
      doc.text(`Comments: ${assessmentData.comments}`, { width: 500 });
    }

    if (assessmentData.dischargePlanReviewed) {
      doc.moveDown(0.5);
      doc.text('✓ Discharge planning was reviewed with patient/family');
    }

    // Footer
    doc.fontSize(8).font('Helvetica');
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 50, doc.page.height - 50);
    doc.text('PhysioFlow - Outpatient Discharge Assessment', { align: 'center' });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ message: 'Error generating PDF' });
  }
});

// Get discharge statistics
router.get('/stats/overview', protect, async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    const dateFilter = {};
    if (startDate && endDate) {
      dateFilter.dischargeDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }

    // Total discharges
    const totalDischarges = await DischargeAssessment.countDocuments(dateFilter);

    // Discharge reasons breakdown
    const reasonsBreakdown = await DischargeAssessment.aggregate([
      { $match: dateFilter },
      { $unwind: '$dischargeReasons' },
      { $group: { _id: '$dischargeReasons', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);

    // Average visits per discharge
    const avgVisits = await DischargeAssessment.aggregate([
      { $match: dateFilter },
      { $group: { _id: null, avgVisits: { $avg: '$totalVisits' } } }
    ]);

    // Monthly discharge trends
    const monthlyTrends = await DischargeAssessment.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: {
            year: { $year: '$dischargeDate' },
            month: { $month: '$dischargeDate' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ]);

    res.json({
      totalDischarges,
      reasonsBreakdown,
      averageVisits: avgVisits[0]?.avgVisits || 0,
      monthlyTrends
    });
  } catch (error) {
    console.error('Error fetching discharge statistics:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
