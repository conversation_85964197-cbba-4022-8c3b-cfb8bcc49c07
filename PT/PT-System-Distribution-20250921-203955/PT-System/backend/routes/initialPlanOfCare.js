const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();
const PDFDocument = require('pdfkit');
const { protect } = require('../middleware/auth');

// Initial Plan of Care Schema
const initialPlanOfCareSchema = new mongoose.Schema({
  // Document Metadata
  documentNumber: { type: String, required: true },
  issueDate: { type: Date, required: true },
  version: { type: String, default: '01' },
  reviewNumber: { type: String, default: '01' },
  
  // Patient Information
  patientName: { type: String, required: true },
  mrNumber: { type: String, required: true },
  diagnosis: { type: String, required: true },
  onsetDate: { type: Date, required: true },
  physician: { type: String, required: true },
  
  // Patient Problems
  functionalProblems: [{
    value: String,
    label: String,
    laterality: String // R, L, B
  }],
  functionalImpairments: [String],
  functionalActivityRestrictions: [String],
  otherActivityRestriction: String,
  specificDescriptions: [String],
  
  // Goals
  shortTermGoals: {
    weeks: { type: Number, required: true },
    goals: [String]
  },
  longTermGoals: {
    weeks: { type: Number, required: true },
    goals: [String]
  },
  
  // Treatment Plan
  painControl: [String],
  reduceSwelling: [String],
  improveROM: [String],
  improveFlexibility: [String],
  muscleStrengthening: [String],
  posturalCorrection: [String],
  improveBalance: [String],
  improveEndurance: [String],
  gaitTraining: [String],
  homeInstructions: { type: Boolean, default: false },
  otherTreatments: String,
  
  // Review and Signatures
  planReviewedWithPatient: { type: Boolean, default: false },
  therapistSignature: { type: String, required: true },
  therapistBadge: { type: String, required: true },
  therapistDate: { type: Date, required: true },
  physicianSignature: String,
  physicianBadge: String,
  physicianDate: Date,
  
  // Metadata
  submittedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  submittedAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const InitialPlanOfCare = mongoose.model('InitialPlanOfCare', initialPlanOfCareSchema);

// Middleware to bypass auth in development
const optionalAuth = (req, res, next) => {
  if (process.env.NODE_ENV === 'development') {
    req.user = req.user || {
      id: '507f1f77bcf86cd799439011',
      role: 'therapist',
      name: 'Development User'
    };
  }
  next();
};

// Test route
router.get('/test', (req, res) => {
  res.json({
    message: 'Initial Plan of Care API is working',
    timestamp: new Date().toISOString()
  });
});

// Public route to create plans (no auth for development)
router.post('/public', async (req, res) => {
  try {
    const planData = {
      ...req.body,
      submittedBy: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
      submittedAt: new Date()
    };

    // Basic validation
    if (!planData.patientName || !planData.mrNumber || !planData.diagnosis) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: patientName, mrNumber, diagnosis'
      });
    }

    const plan = new InitialPlanOfCare(planData);
    await plan.save();

    res.status(201).json({
      success: true,
      message: 'Initial Plan of Care created successfully',
      plan
    });
  } catch (error) {
    console.error('Error creating initial plan of care:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating initial plan of care',
      error: error.message
    });
  }
});

// Get all plans
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { page = 1, limit = 10, patientId, search } = req.query;

    const query = {};
    if (patientId) query.patientId = patientId;
    if (search) {
      query.$or = [
        { patientName: new RegExp(search, 'i') },
        { mrNumber: new RegExp(search, 'i') },
        { diagnosis: new RegExp(search, 'i') }
      ];
    }

    const plans = await InitialPlanOfCare.find(query)
      .populate('submittedBy', 'name email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .lean();

    const total = await InitialPlanOfCare.countDocuments(query);

    res.json({
      success: true,
      plans: plans || [],
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      total: total || 0
    });
  } catch (error) {
    console.error('Error fetching initial plans of care:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
});

// Generate PDF
router.post('/pdf', optionalAuth, async (req, res) => {
  try {
    const planData = req.body;
    
    // Create PDF document
    const doc = new PDFDocument({ margin: 50, size: 'A4' });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="initial-plan-of-care-${planData.patientName}-${planData.issueDate}.pdf"`);
    
    // Pipe PDF to response
    doc.pipe(res);

    // Helper function to add table
    const addTable = (headers, rows, startY) => {
      const tableTop = startY;
      const itemHeight = 20;
      const colWidth = (doc.page.width - 100) / headers.length;
      
      // Headers
      headers.forEach((header, i) => {
        doc.rect(50 + i * colWidth, tableTop, colWidth, itemHeight)
           .fillAndStroke('#f0f0f0', '#000000')
           .fillColor('#000000')
           .fontSize(10)
           .text(header, 50 + i * colWidth + 5, tableTop + 5, {
             width: colWidth - 10,
             height: itemHeight - 10,
             align: 'left'
           });
      });
      
      // Rows
      rows.forEach((row, rowIndex) => {
        const y = tableTop + (rowIndex + 1) * itemHeight;
        row.forEach((cell, colIndex) => {
          doc.rect(50 + colIndex * colWidth, y, colWidth, itemHeight)
             .stroke('#000000')
             .fillColor('#000000')
             .fontSize(9)
             .text(cell || '', 50 + colIndex * colWidth + 5, y + 5, {
               width: colWidth - 10,
               height: itemHeight - 10,
               align: 'left'
             });
        });
      });
      
      return tableTop + (rows.length + 1) * itemHeight + 10;
    };

    // Page 1 Header
    doc.fontSize(16).font('Helvetica-Bold');
    doc.text('INITIAL PLAN OF CARE PHYSICAL THERAPY', { align: 'center' });
    doc.moveDown();

    // Document metadata
    doc.fontSize(10).font('Helvetica');
    doc.text(`Document Number: ${planData.documentNumber}`, 50, 100);
    doc.text(`Issue Date: ${planData.issueDate}`, 200, 100);
    doc.text(`Version: ${planData.version}`, 350, 100);
    doc.text(`Review Number: ${planData.reviewNumber}`, 450, 100);
    doc.moveDown();

    // Patient Information
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT INFORMATION', 50, 130, { underline: true });
    doc.moveDown(0.5);
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Patient Name: ${planData.patientName}`, 50, 150);
    doc.text(`MR #: ${planData.mrNumber}`, 300, 150);
    doc.text(`Diagnosis: ${planData.diagnosis}`, 50, 170, { width: 500 });
    doc.text(`Onset Date: ${planData.onsetDate}`, 50, 200);
    doc.text(`Physician: ${planData.physician}`, 300, 200);

    // Patient Problems Table
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('PATIENT PROBLEMS (REASON FOR REFERRAL)', 50, 240, { underline: true });
    
    const problemHeaders = ['Functional Problems', 'Functional Impairments', 'Activity Restrictions', 'Descriptions'];
    const problemRows = [];
    
    // Format problems data
    const functionalProblems = planData.functionalProblems?.map(p => 
      p.laterality ? `${p.label} (${p.laterality})` : p.label
    ).join(', ') || '';
    
    const functionalImpairments = planData.functionalImpairments?.join(', ') || '';
    const activityRestrictions = planData.functionalActivityRestrictions?.join(', ') || '';
    const descriptions = planData.specificDescriptions?.filter(d => d.trim()).map((d, i) => `${i+1}. ${d}`).join('\n') || '';
    
    problemRows.push([
      functionalProblems,
      functionalImpairments, 
      activityRestrictions,
      descriptions
    ]);

    let currentY = addTable(problemHeaders, problemRows, 260);

    // Goals Section
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('GOALS OF TREATMENT', 50, currentY + 20, { underline: true });
    currentY += 50;
    
    doc.fontSize(10).font('Helvetica');
    doc.text(`Short Term Goals (${planData.shortTermGoals?.weeks} weeks):`, 50, currentY);
    currentY += 20;
    
    planData.shortTermGoals?.goals?.forEach((goal, index) => {
      if (goal.trim()) {
        doc.text(`${index + 1}. Patient will: ${goal}`, 70, currentY, { width: 450 });
        currentY += 20;
      }
    });
    
    currentY += 10;
    doc.text(`Long Term Goals (${planData.longTermGoals?.weeks} weeks):`, 50, currentY);
    currentY += 20;
    
    planData.longTermGoals?.goals?.forEach((goal, index) => {
      if (goal.trim()) {
        doc.text(`${index + 1}. Patient will: ${goal}`, 70, currentY, { width: 450 });
        currentY += 20;
      }
    });

    // Add new page for treatment plan
    doc.addPage();
    
    // Page 2 Header
    doc.fontSize(16).font('Helvetica-Bold');
    doc.text('TREATMENT PLAN', { align: 'center' });
    doc.moveDown();

    // Treatment Plan Table
    const treatmentCategories = [
      { key: 'painControl', label: 'Pain Control' },
      { key: 'reduceSwelling', label: 'Reduce Swelling/Edema' },
      { key: 'improveROM', label: 'Improve ROM' },
      { key: 'improveFlexibility', label: 'Improve Flexibility' },
      { key: 'muscleStrengthening', label: 'Muscle Strengthening' },
      { key: 'posturalCorrection', label: 'Postural Correction' },
      { key: 'improveBalance', label: 'Improve Balance and Coordination' },
      { key: 'improveEndurance', label: 'Improve Endurance' },
      { key: 'gaitTraining', label: 'Gait Training' }
    ];

    const treatmentHeaders = ['Treatment Category', 'Selected Treatments'];
    const treatmentRows = [];
    
    treatmentCategories.forEach(category => {
      const treatments = planData[category.key]?.join(', ') || '';
      if (treatments) {
        treatmentRows.push([category.label, treatments]);
      }
    });
    
    if (planData.homeInstructions) {
      treatmentRows.push(['Home Instructions', 'Provide home exercise instructions']);
    }
    
    if (planData.otherTreatments) {
      treatmentRows.push(['Others', planData.otherTreatments]);
    }

    currentY = addTable(treatmentHeaders, treatmentRows, 100);

    // Plan Review
    if (planData.planReviewedWithPatient) {
      doc.fontSize(10).font('Helvetica');
      doc.text('☑ Plan of Care Reviewed with Patient', 50, currentY + 20);
    }

    // Signatures
    doc.fontSize(12).font('Helvetica-Bold');
    doc.text('SIGNATURES', 50, currentY + 60, { underline: true });
    
    doc.fontSize(10).font('Helvetica');
    doc.text('Therapist:', 50, currentY + 90);
    doc.text(`Signature: ${planData.therapistSignature}`, 50, currentY + 110);
    doc.text(`Badge No.: ${planData.therapistBadge}`, 50, currentY + 130);
    doc.text(`Date: ${planData.therapistDate}`, 50, currentY + 150);

    doc.text('Physician Review:', 300, currentY + 90);
    doc.text('Have reviewed this plan of care and re-certify', 300, currentY + 110);
    doc.text('a continuing need for services.', 300, currentY + 125);
    if (planData.physicianSignature) {
      doc.text(`Signature: ${planData.physicianSignature}`, 300, currentY + 145);
      doc.text(`Badge No.: ${planData.physicianBadge}`, 300, currentY + 165);
      doc.text(`Date: ${planData.physicianDate}`, 300, currentY + 185);
    }

    // Footer
    doc.fontSize(8).font('Helvetica');
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 50, doc.page.height - 50);
    doc.text('PhysioFlow - Initial Plan of Care Physical Therapy', { align: 'center' });

    // Finalize PDF
    doc.end();

  } catch (error) {
    console.error('Error generating PDF:', error);
    res.status(500).json({ message: 'Error generating PDF' });
  }
});

module.exports = router;
