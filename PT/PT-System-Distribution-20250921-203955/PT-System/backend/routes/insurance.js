const express = require('express');
const {
  getInsurances,
  getInsurance,
  createInsurance,
  updateInsurance,
  deleteInsurance,
  verifyInsurance,
  checkEligibility,
  getInsurancesByPatient,
  getExpiringInsurances,
  submitClaim,
  getClaimStatus,
  getInsuranceStats
} = require('../controllers/insurance');

const { protect, authorize, checkPermission, checkPatientAccess } = require('../middleware/auth');

const router = express.Router();

/**
 * @swagger
 * /insurance:
 *   get:
 *     summary: Get all insurance records
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page
 *       - in: query
 *         name: provider
 *         schema:
 *           type: string
 *         description: Filter by insurance provider
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, suspended, expired]
 *         description: Filter by insurance status
 *       - in: query
 *         name: verificationStatus
 *         schema:
 *           type: string
 *           enum: [verified, pending, failed, expired]
 *         description: Filter by verification status
 *     responses:
 *       200:
 *         description: List of insurance records
 *   post:
 *     summary: Create a new insurance record
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Insurance'
 *     responses:
 *       201:
 *         description: Insurance record created successfully
 *       400:
 *         description: Validation error
 */

// Protect all routes
router.use(protect);

// Get all insurance records and create new insurance record
router
  .route('/')
  .get(checkPermission('view_insurance'), getInsurances)
  .post(checkPermission('create_insurance'), createInsurance);

/**
 * @swagger
 * /insurance/stats:
 *   get:
 *     summary: Get insurance statistics
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Insurance statistics
 */
router.get('/stats', checkPermission('view_insurance'), getInsuranceStats);

/**
 * @swagger
 * /insurance/expiring:
 *   get:
 *     summary: Get expiring insurance records
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *         description: Number of days to look ahead
 *     responses:
 *       200:
 *         description: Expiring insurance records
 */
router.get('/expiring', checkPermission('view_insurance'), getExpiringInsurances);

/**
 * @swagger
 * /insurance/patient/{patientId}:
 *   get:
 *     summary: Get insurance records by patient
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patientId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient's insurance records
 */
router.get('/patient/:patientId', checkPermission('view_insurance'), getInsurancesByPatient);

/**
 * @swagger
 * /insurance/{id}:
 *   get:
 *     summary: Get insurance record by ID
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Insurance record details
 *       404:
 *         description: Insurance record not found
 *   put:
 *     summary: Update insurance record
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Insurance'
 *     responses:
 *       200:
 *         description: Insurance record updated successfully
 *       404:
 *         description: Insurance record not found
 *   delete:
 *     summary: Delete insurance record
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Insurance record deleted successfully
 *       404:
 *         description: Insurance record not found
 */
router
  .route('/:id')
  .get(checkPermission('view_insurance'), getInsurance)
  .put(checkPermission('edit_insurance'), updateInsurance)
  .delete(checkPermission('delete_insurance'), authorize('admin'), deleteInsurance);

/**
 * @swagger
 * /insurance/{id}/verify:
 *   post:
 *     summary: Verify insurance coverage
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Insurance verification completed
 */
router.post('/:id/verify', checkPermission('edit_insurance'), verifyInsurance);

/**
 * @swagger
 * /insurance/{id}/eligibility:
 *   post:
 *     summary: Check eligibility for specific service
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - serviceType
 *             properties:
 *               serviceType:
 *                 type: string
 *                 enum: [physicalTherapy, occupationalTherapy, speechTherapy, specialNeeds]
 *               serviceDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Eligibility check results
 */
router.post('/:id/eligibility', checkPermission('view_insurance'), checkEligibility);

/**
 * @swagger
 * /insurance/{id}/claims:
 *   post:
 *     summary: Submit insurance claim
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - services
 *               - amount
 *               - serviceDate
 *             properties:
 *               services:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                     description:
 *                       type: string
 *                     amount:
 *                       type: number
 *               amount:
 *                 type: number
 *               serviceDate:
 *                 type: string
 *                 format: date
 *               provider:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Claim submitted successfully
 */
router.post('/:id/claims', checkPermission('create_insurance'), submitClaim);

/**
 * @swagger
 * /insurance/{id}/claims/{claimId}/status:
 *   get:
 *     summary: Get claim status
 *     tags: [Insurance]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: claimId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Claim status information
 */
router.get('/:id/claims/:claimId/status', checkPermission('view_insurance'), getClaimStatus);

module.exports = router;
