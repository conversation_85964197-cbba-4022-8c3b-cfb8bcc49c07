const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const hpp = require('hpp');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

// Load environment variables
require('dotenv').config();

// Import utilities
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');
const connectDB = require('./config/database');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const patientRoutes = require('./routes/patients');
const appointmentRoutes = require('./routes/appointments');
const treatmentRoutes = require('./routes/treatments');
const formRoutes = require('./routes/forms');
const insuranceRoutes = require('./routes/insurance');
const billingRoutes = require('./routes/billing');
const paymentsRoutes = require('./routes/payments');
const analyticsRoutes = require('./routes/analytics');
const uploadRoutes = require('./routes/upload');
const nphiesRoutes = require('./routes/nphies');
const notificationRoutes = require('./routes/notifications');
const electronicSignatureRoutes = require('./routes/electronicSignature');
const invoiceTemplateRoutes = require('./routes/invoiceTemplate');
const aiAnalyticsRoutes = require('./routes/aiAnalytics');

// PhysioFlow feature routes
const bodyMapRoutes = require('./routes/bodymap');
const communicationRoutes = require('./routes/communication');
const exerciseProgramRoutes = require('./routes/exercise-programs');
const aiInteractionRoutes = require('./routes/ai-interactions');
const dischargeAssessmentRoutes = require('./routes/dischargeAssessment');
const painAssessmentRoutes = require('./routes/painAssessment');
const homeExerciseProgramRoutes = require('./routes/homeExerciseProgram');
const initialPlanOfCareRoutes = require('./routes/initialPlanOfCare');
const patientFamilyEducationRoutes = require('./routes/patientFamilyEducation');
const followUpPlanRoutes = require('./routes/followUpPlan');
const clinicalIndicatorsRoutes = require('./routes/clinicalIndicators');
const permissionsRoutes = require('./routes/permissions');

// Create Express app
const app = express();

// Connect to database
connectDB();

// Trust proxy (for rate limiting behind reverse proxy)
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Rate limiting - Increased for development
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 10000, // Increased to 10000 for development
  message: {
    error: 'Too many requests from this IP, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Only apply rate limiting in production
if (process.env.NODE_ENV === 'production') {
  app.use('/api/', limiter);
  console.log('🔒 Rate limiting enabled for production');
} else {
  console.log('🔓 Rate limiting disabled for development');
}

// CORS configuration
const corsOrigin = process.env.CORS_ORIGIN || 'http://localhost:3001';
console.log('🌐 CORS Origin configured:', corsOrigin);

const corsOptions = {
  origin: corsOrigin.split(',').map(origin => origin.trim()),
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'X-Timezone',
    'Accept',
    'Origin'
  ],
};
console.log('🌐 CORS Options:', corsOptions);
app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());

// Data sanitization against NoSQL query injection
app.use(mongoSanitize());

// Data sanitization against XSS
app.use(xss());

// Prevent parameter pollution
app.use(hpp());

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Swagger configuration
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'PT System API',
      version: '1.0.0',
      description: 'Physical Therapy Management System API Documentation',
      contact: {
        name: 'PT System Team',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 5000}/api/v1`,
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./routes/*.js', './models/*.js'], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'PT System API is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'PT System API is running',
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || 'v1'
  });
});

app.get('/api/v1/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'PT System API is running',
    timestamp: new Date().toISOString(),
    version: process.env.API_VERSION || 'v1'
  });
});

// API routes
const apiVersion = process.env.API_VERSION || 'v1';
app.use(`/api/${apiVersion}/auth`, authRoutes);
app.use(`/api/${apiVersion}/users`, userRoutes);
app.use(`/api/${apiVersion}/patients`, patientRoutes);
app.use(`/api/${apiVersion}/appointments`, appointmentRoutes);
app.use(`/api/${apiVersion}/treatments`, treatmentRoutes);
app.use(`/api/${apiVersion}/forms`, formRoutes);
app.use(`/api/${apiVersion}/insurance`, insuranceRoutes);
app.use(`/api/${apiVersion}/billing`, billingRoutes);
app.use(`/api/${apiVersion}/payments`, paymentsRoutes);
app.use(`/api/${apiVersion}/analytics`, analyticsRoutes);
app.use(`/api/${apiVersion}/upload`, uploadRoutes);
app.use(`/api/${apiVersion}/nphies`, nphiesRoutes);
app.use(`/api/${apiVersion}/notifications`, notificationRoutes);
app.use(`/api/${apiVersion}/signature`, electronicSignatureRoutes);
app.use(`/api/${apiVersion}/invoice-templates`, invoiceTemplateRoutes);

// PhysioFlow feature routes
app.use(`/api/${apiVersion}/bodymap`, bodyMapRoutes);
app.use(`/api/${apiVersion}/communication`, communicationRoutes);
app.use(`/api/${apiVersion}/exercise-programs`, exerciseProgramRoutes);
app.use(`/api/${apiVersion}/ai-interactions`, aiInteractionRoutes);
app.use(`/api/${apiVersion}/discharge-assessments`, dischargeAssessmentRoutes);
app.use(`/api/${apiVersion}/pain-assessments`, painAssessmentRoutes);
app.use(`/api/${apiVersion}/home-exercise-programs`, homeExerciseProgramRoutes);
app.use(`/api/${apiVersion}/initial-plan-of-care`, initialPlanOfCareRoutes);
app.use(`/api/${apiVersion}/patient-family-education`, patientFamilyEducationRoutes);
app.use(`/api/${apiVersion}/follow-up-plan`, followUpPlanRoutes);
app.use(`/api/${apiVersion}/clinical-indicators`, clinicalIndicatorsRoutes);
app.use(`/api/${apiVersion}/permissions`, permissionsRoutes);
app.use(`/api/${apiVersion}/ai-analytics`, aiAnalyticsRoutes);

// Serve static files
app.use('/uploads', express.static('public/uploads'));

// 404 handler
app.all('*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: `Route ${req.originalUrl} not found`,
  });
});

// Global error handler
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received. Shutting down gracefully...');
  mongoose.connection.close(() => {
    logger.info('MongoDB connection closed.');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received. Shutting down gracefully...');
  mongoose.connection.close(() => {
    logger.info('MongoDB connection closed.');
    process.exit(0);
  });
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  logger.error('Unhandled Promise Rejection:', err);
  mongoose.connection.close(() => {
    process.exit(1);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  process.exit(1);
});

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  logger.info(`PT System API server running on port ${PORT} in ${process.env.NODE_ENV} mode`);
  logger.info(`API Documentation available at http://localhost:${PORT}/api-docs`);
});

module.exports = app;
