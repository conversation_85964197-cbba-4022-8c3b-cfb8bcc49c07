const mongoose = require('mongoose');

const dischargeAssessmentSchema = new mongoose.Schema({
  // Header Information
  documentNumber: {
    type: String,
    default: 'QP-'
  },
  issueDate: {
    type: Date,
    default: Date.now
  },
  version: {
    type: String,
    default: '01'
  },
  reviewNumber: {
    type: String,
    default: '01'
  },

  // Patient Information
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient'
  },
  patientName: {
    type: String,
    required: true,
    trim: true
  },
  mrNumber: {
    type: String,
    required: true,
    trim: true
  },
  dischargeDate: {
    type: Date,
    required: true
  },
  diagnosis: {
    type: String,
    required: true,
    trim: true
  },
  admissionDate: {
    type: Date,
    required: true
  },
  physician: {
    type: String,
    required: true,
    trim: true
  },
  totalVisits: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  noShowCancellations: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },

  // Evaluation Addendum
  evaluationAddendum: [{
    type: String,
    enum: [
      'Functional', 'LE', 'UE', 'Ankle/Foot', 'Cervical', 'Lumbar',
      'Amputee', 'Hand', 'Voice', 'Cognitive', 'Visual/Perceptual',
      'Wound', 'Lymphedema', 'Pulmonary', 'Communication', 'Dysphagia',
      'Urinary', 'Incontinence', 'Other'
    ]
  }],
  evaluationOther: {
    type: String,
    trim: true
  },

  // Treatment Received
  treatmentReceived: {
    type: String,
    required: true,
    trim: true
  },

  // Summary of Progress
  gait: {
    type: String,
    trim: true
  },
  balanceCoordination: {
    type: String,
    trim: true
  },
  pain: {
    type: String,
    trim: true
  },
  functionalAssessment: {
    type: String,
    trim: true
  },
  environmentalAssessment: {
    type: String,
    trim: true
  },
  activitiesLimitation: {
    type: String,
    trim: true
  },
  participateRestriction: {
    type: String,
    trim: true
  },
  familySupport: {
    type: String,
    trim: true
  },
  assistiveDevices: {
    type: String,
    trim: true
  },
  riskFactors: {
    type: String,
    trim: true
  },

  // Patient/Caregiver Training
  patientCaregiverTraining: {
    type: String,
    required: true,
    trim: true
  },

  // Reason for Discharge
  dischargeReasons: [{
    type: String,
    required: true,
    enum: [
      'Goals Met',
      'Medical Condition',
      'Reached Maximal Potential',
      'Non-Compliance',
      'Objective findings inconsistent with patient\'s complaints and/or diagnosis',
      'Other'
    ]
  }],
  dischargeOther: {
    type: String,
    trim: true
  },

  // Recommendations
  continueHEP: {
    type: Boolean,
    default: false
  },
  continueHEPDetails: {
    type: String,
    trim: true
  },
  equipmentNeeds: {
    type: String,
    trim: true
  },
  followUpPhysician: {
    type: Boolean,
    default: false
  },
  followUpPhysicianDetails: {
    type: String,
    trim: true
  },
  recommendationOther: {
    type: String,
    trim: true
  },

  // Follow-up Information
  followUpFrequency: {
    type: String,
    trim: true
  },
  followUpDate: {
    type: Date
  },
  followUpRiskFactors: {
    type: String,
    trim: true
  },
  planReviewedWithFamily: {
    type: Boolean,
    default: false
  },

  // Signatures and Comments
  therapistSignature: {
    type: String,
    required: true,
    trim: true
  },
  therapistBadgeNo: {
    type: String,
    required: true,
    trim: true
  },
  therapistDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  comments: {
    type: String,
    trim: true
  },
  dischargePlanReviewed: {
    type: Boolean,
    default: false
  },

  // Secondary Signature (Page 2)
  therapistSignature2: {
    type: String,
    trim: true
  },
  therapistBadgeNo2: {
    type: String,
    trim: true
  },
  therapistDate2: {
    type: Date
  },

  // System fields
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // Made optional for development
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['draft', 'completed', 'reviewed'],
    default: 'completed'
  }
}, {
  timestamps: true
});

// Indexes for better query performance
dischargeAssessmentSchema.index({ patientId: 1 });
dischargeAssessmentSchema.index({ mrNumber: 1 });
dischargeAssessmentSchema.index({ dischargeDate: -1 });
dischargeAssessmentSchema.index({ submittedBy: 1 });
dischargeAssessmentSchema.index({ therapistSignature: 1 });
dischargeAssessmentSchema.index({ dischargeReasons: 1 });

// Virtual for calculating length of stay
dischargeAssessmentSchema.virtual('lengthOfStay').get(function() {
  if (this.admissionDate && this.dischargeDate) {
    const diffTime = Math.abs(this.dischargeDate - this.admissionDate);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }
  return 0;
});

// Virtual for attendance rate
dischargeAssessmentSchema.virtual('attendanceRate').get(function() {
  if (this.totalVisits > 0) {
    const attendedVisits = this.totalVisits - this.noShowCancellations;
    return Math.round((attendedVisits / this.totalVisits) * 100);
  }
  return 0;
});

// Pre-save middleware to validate dates
dischargeAssessmentSchema.pre('save', function(next) {
  // Ensure discharge date is after admission date
  if (this.admissionDate && this.dischargeDate) {
    if (this.admissionDate >= this.dischargeDate) {
      const error = new Error('Discharge date must be after admission date');
      error.name = 'ValidationError';
      return next(error);
    }
  }

  // Ensure follow-up date is in the future if provided
  if (this.followUpDate && this.followUpDate <= new Date()) {
    const error = new Error('Follow-up date must be in the future');
    error.name = 'ValidationError';
    return next(error);
  }

  // Update the updatedAt field
  this.updatedAt = new Date();
  
  next();
});

// Static method to get discharge statistics
dischargeAssessmentSchema.statics.getStatistics = async function(startDate, endDate) {
  const matchStage = {};
  if (startDate && endDate) {
    matchStage.dischargeDate = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  const stats = await this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalDischarges: { $sum: 1 },
        avgTotalVisits: { $avg: '$totalVisits' },
        avgNoShows: { $avg: '$noShowCancellations' },
        avgLengthOfStay: {
          $avg: {
            $divide: [
              { $subtract: ['$dischargeDate', '$admissionDate'] },
              1000 * 60 * 60 * 24
            ]
          }
        }
      }
    }
  ]);

  return stats[0] || {
    totalDischarges: 0,
    avgTotalVisits: 0,
    avgNoShows: 0,
    avgLengthOfStay: 0
  };
};

// Static method to get discharge reasons breakdown
dischargeAssessmentSchema.statics.getDischargeReasonsBreakdown = async function(startDate, endDate) {
  const matchStage = {};
  if (startDate && endDate) {
    matchStage.dischargeDate = {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }

  return await this.aggregate([
    { $match: matchStage },
    { $unwind: '$dischargeReasons' },
    {
      $group: {
        _id: '$dischargeReasons',
        count: { $sum: 1 }
      }
    },
    { $sort: { count: -1 } }
  ]);
};

// Instance method to generate summary
dischargeAssessmentSchema.methods.generateSummary = function() {
  return {
    patient: this.patientName,
    mrNumber: this.mrNumber,
    dischargeDate: this.dischargeDate,
    lengthOfStay: this.lengthOfStay,
    totalVisits: this.totalVisits,
    attendanceRate: this.attendanceRate,
    primaryDischargeReason: this.dischargeReasons[0],
    therapist: this.therapistSignature
  };
};

module.exports = mongoose.model('DischargeAssessment', dischargeAssessmentSchema);
