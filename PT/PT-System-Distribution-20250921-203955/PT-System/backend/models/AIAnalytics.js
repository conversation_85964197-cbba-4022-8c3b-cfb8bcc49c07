const mongoose = require('mongoose');

/**
 * AI Analytics Model
 * Stores AI predictions, recommendations, and analytics data
 */

// Prediction Schema for various AI predictions
const predictionSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: [
      'outcome_prediction',
      'recovery_timeline',
      'risk_assessment',
      'treatment_recommendation',
      'functional_improvement'
    ]
  },
  confidence: {
    type: Number,
    required: true,
    min: 0,
    max: 1
  },
  value: mongoose.Schema.Types.Mixed, // Flexible field for different prediction types
  metadata: {
    model_version: String,
    features_used: [String],
    training_date: Date,
    accuracy_score: Number
  }
}, { _id: false });

// Risk Factor Schema
const riskFactorSchema = new mongoose.Schema({
  factor: {
    type: String,
    required: true
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    required: true
  },
  impact_score: {
    type: Number,
    min: 0,
    max: 10
  },
  description: String,
  recommendations: [String]
}, { _id: false });

// Treatment Recommendation Schema
const treatmentRecommendationSchema = new mongoose.Schema({
  treatment_type: {
    type: String,
    required: true
  },
  priority: {
    type: Number,
    min: 1,
    max: 10
  },
  confidence: {
    type: Number,
    min: 0,
    max: 1
  },
  expected_outcome: {
    improvement_percentage: Number,
    timeline_weeks: Number,
    success_probability: Number
  },
  contraindications: [String],
  prerequisites: [String],
  evidence_level: {
    type: String,
    enum: ['A', 'B', 'C', 'D'],
    default: 'C'
  }
}, { _id: false });

// Main AI Analytics Schema
const aiAnalyticsSchema = new mongoose.Schema({
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true,
    index: true
  },
  
  // Analysis metadata
  analysis_date: {
    type: Date,
    default: Date.now,
    index: true
  },
  
  analysis_type: {
    type: String,
    enum: [
      'initial_assessment',
      'progress_evaluation',
      'discharge_planning',
      'risk_screening',
      'treatment_optimization'
    ],
    required: true
  },
  
  // AI Predictions
  predictions: [predictionSchema],
  
  // Risk Assessment
  risk_assessment: {
    overall_risk_score: {
      type: Number,
      min: 0,
      max: 100
    },
    risk_category: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    risk_factors: [riskFactorSchema]
  },
  
  // Treatment Recommendations
  treatment_recommendations: [treatmentRecommendationSchema],
  
  // Outcome Predictions
  outcome_predictions: {
    functional_independence_score: {
      current: Number,
      predicted_3_months: Number,
      predicted_6_months: Number,
      predicted_12_months: Number
    },
    recovery_timeline: {
      estimated_weeks: Number,
      milestones: [{
        week: Number,
        expected_improvement: String,
        confidence: Number
      }]
    },
    success_probability: {
      complete_recovery: Number,
      significant_improvement: Number,
      minimal_improvement: Number,
      no_improvement: Number
    }
  },
  
  // Clinical Decision Support
  clinical_alerts: [{
    type: {
      type: String,
      enum: ['warning', 'caution', 'info', 'critical']
    },
    message: String,
    priority: {
      type: Number,
      min: 1,
      max: 5
    },
    action_required: Boolean,
    dismissed: {
      type: Boolean,
      default: false
    },
    dismissed_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    dismissed_at: Date,
    dismissed_reason: String
  }],
  
  // Data sources used for analysis
  data_sources: {
    assessments: [mongoose.Schema.Types.ObjectId],
    forms: [mongoose.Schema.Types.ObjectId],
    vital_signs: [mongoose.Schema.Types.ObjectId],
    lab_results: [mongoose.Schema.Types.ObjectId],
    imaging: [mongoose.Schema.Types.ObjectId]
  },
  
  // Model performance tracking
  model_performance: {
    accuracy_metrics: mongoose.Schema.Types.Mixed,
    validation_score: Number,
    last_updated: Date
  },
  
  // User feedback on AI recommendations
  feedback: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    recommendation_id: String,
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comments: String,
    implemented: Boolean,
    outcome: String,
    created_at: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Analysis status
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'archived'],
    default: 'pending'
  },
  
  // Processing metadata
  processing_time_ms: Number,
  error_message: String,
  
  // Audit trail
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  updated_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
aiAnalyticsSchema.index({ patient: 1, analysis_date: -1 });
aiAnalyticsSchema.index({ analysis_type: 1, status: 1 });
aiAnalyticsSchema.index({ 'risk_assessment.risk_category': 1 });
aiAnalyticsSchema.index({ 'clinical_alerts.type': 1, 'clinical_alerts.dismissed': 1 });

// Virtual for latest analysis
aiAnalyticsSchema.virtual('is_latest').get(function() {
  return this.analysis_date >= new Date(Date.now() - 24 * 60 * 60 * 1000); // Within last 24 hours
});

// Static methods
aiAnalyticsSchema.statics.getLatestAnalysis = function(patientId) {
  return this.findOne({ 
    patient: patientId, 
    status: 'completed' 
  }).sort({ analysis_date: -1 });
};

aiAnalyticsSchema.statics.getHighRiskPatients = function() {
  return this.find({
    'risk_assessment.risk_category': { $in: ['high', 'critical'] },
    status: 'completed'
  }).populate('patient', 'firstName lastName dateOfBirth');
};

aiAnalyticsSchema.statics.getPendingAlerts = function() {
  return this.find({
    'clinical_alerts.dismissed': false,
    status: 'completed'
  }).populate('patient', 'firstName lastName');
};

// Instance methods
aiAnalyticsSchema.methods.dismissAlert = function(alertIndex, userId, reason) {
  if (this.clinical_alerts[alertIndex]) {
    this.clinical_alerts[alertIndex].dismissed = true;
    this.clinical_alerts[alertIndex].dismissed_by = userId;
    this.clinical_alerts[alertIndex].dismissed_at = new Date();
    this.clinical_alerts[alertIndex].dismissed_reason = reason;
    return this.save();
  }
  throw new Error('Alert not found');
};

aiAnalyticsSchema.methods.addFeedback = function(userId, recommendationId, rating, comments, implemented, outcome) {
  this.feedback.push({
    user: userId,
    recommendation_id: recommendationId,
    rating,
    comments,
    implemented,
    outcome
  });
  return this.save();
};

// Pre-save middleware
aiAnalyticsSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updated_by = this.created_by; // Will be overridden by the calling code
  }
  next();
});

module.exports = mongoose.model('AIAnalytics', aiAnalyticsSchema);
