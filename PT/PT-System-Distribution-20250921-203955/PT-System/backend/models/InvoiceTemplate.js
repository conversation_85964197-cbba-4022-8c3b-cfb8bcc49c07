const mongoose = require('mongoose');

const invoiceTemplateSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  nameArabic: {
    type: String,
    trim: true
  },
  
  description: {
    type: String,
    required: true
  },
  
  descriptionArabic: {
    type: String
  },
  
  type: {
    type: String,
    enum: ['standard', 'medical', 'therapy', 'insurance', 'government', 'custom'],
    default: 'standard'
  },
  
  category: {
    type: String,
    enum: ['general', 'physical_therapy', 'occupational_therapy', 'speech_therapy', 'medical_consultation', 'equipment_rental'],
    default: 'general'
  },
  
  // Template Configuration
  config: {
    // Header Configuration
    header: {
      showLogo: {
        type: Boolean,
        default: true
      },
      showCompanyInfo: {
        type: Boolean,
        default: true
      },
      showInvoiceTitle: {
        type: Boolean,
        default: true
      },
      customTitle: String,
      customTitleArabic: String
    },
    
    // Patient/Client Information
    patientInfo: {
      showPatientDetails: {
        type: Boolean,
        default: true
      },
      showNationalId: {
        type: Boolean,
        default: true
      },
      showInsuranceInfo: {
        type: Boolean,
        default: false
      },
      showVisaInfo: {
        type: Boolean,
        default: false
      },
      customFields: [{
        label: String,
        labelArabic: String,
        fieldType: {
          type: String,
          enum: ['text', 'number', 'date', 'boolean'],
          default: 'text'
        },
        required: {
          type: Boolean,
          default: false
        }
      }]
    },
    
    // Services Configuration
    services: {
      showServiceCode: {
        type: Boolean,
        default: true
      },
      showDescription: {
        type: Boolean,
        default: true
      },
      showQuantity: {
        type: Boolean,
        default: true
      },
      showUnitPrice: {
        type: Boolean,
        default: true
      },
      showDiscount: {
        type: Boolean,
        default: false
      },
      showTax: {
        type: Boolean,
        default: true
      },
      allowCustomServices: {
        type: Boolean,
        default: true
      },
      predefinedServices: [{
        code: String,
        name: String,
        nameArabic: String,
        description: String,
        descriptionArabic: String,
        defaultPrice: Number,
        taxable: {
          type: Boolean,
          default: true
        }
      }]
    },
    
    // Pricing Configuration
    pricing: {
      currency: {
        type: String,
        default: 'SAR'
      },
      showSubtotal: {
        type: Boolean,
        default: true
      },
      showTax: {
        type: Boolean,
        default: true
      },
      taxRate: {
        type: Number,
        default: 15
      },
      showDiscount: {
        type: Boolean,
        default: false
      },
      showTotal: {
        type: Boolean,
        default: true
      },
      roundingMethod: {
        type: String,
        enum: ['round', 'floor', 'ceil'],
        default: 'round'
      }
    },
    
    // Footer Configuration
    footer: {
      showPaymentTerms: {
        type: Boolean,
        default: true
      },
      showBankDetails: {
        type: Boolean,
        default: false
      },
      showNotes: {
        type: Boolean,
        default: true
      },
      showSignature: {
        type: Boolean,
        default: false
      },
      customText: String,
      customTextArabic: String
    },
    
    // Compliance Configuration
    compliance: {
      zatcaCompliant: {
        type: Boolean,
        default: true
      },
      nphiesCompliant: {
        type: Boolean,
        default: false
      },
      showQRCode: {
        type: Boolean,
        default: true
      },
      requireElectronicSignature: {
        type: Boolean,
        default: false
      }
    }
  },
  
  // Template Styling
  styling: {
    colorScheme: {
      primary: {
        type: String,
        default: '#2563eb'
      },
      secondary: {
        type: String,
        default: '#64748b'
      },
      accent: {
        type: String,
        default: '#10b981'
      }
    },
    
    fonts: {
      primary: {
        type: String,
        default: 'Inter'
      },
      arabic: {
        type: String,
        default: 'Noto Sans Arabic'
      }
    },
    
    layout: {
      orientation: {
        type: String,
        enum: ['portrait', 'landscape'],
        default: 'portrait'
      },
      pageSize: {
        type: String,
        enum: ['A4', 'Letter', 'Legal'],
        default: 'A4'
      },
      margins: {
        top: {
          type: Number,
          default: 20
        },
        bottom: {
          type: Number,
          default: 20
        },
        left: {
          type: Number,
          default: 20
        },
        right: {
          type: Number,
          default: 20
        }
      }
    }
  },
  
  // Template Status
  isActive: {
    type: Boolean,
    default: true
  },
  
  isDefault: {
    type: Boolean,
    default: false
  },
  
  isSystemTemplate: {
    type: Boolean,
    default: false
  },
  
  // Usage Statistics
  usageCount: {
    type: Number,
    default: 0
  },
  
  lastUsed: Date,
  
  // Template Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Version Control
  version: {
    type: String,
    default: '1.0.0'
  },
  
  changelog: [{
    version: String,
    changes: String,
    changedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    changedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes
invoiceTemplateSchema.index({ type: 1, category: 1 });
invoiceTemplateSchema.index({ isActive: 1, isDefault: 1 });
invoiceTemplateSchema.index({ createdBy: 1 });
invoiceTemplateSchema.index({ usageCount: -1 });

// Virtual for template preview URL
invoiceTemplateSchema.virtual('previewUrl').get(function() {
  return `/api/v1/invoice-templates/${this._id}/preview`;
});

// Method to increment usage count
invoiceTemplateSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.lastUsed = new Date();
  return this.save();
};

// Method to create new version
invoiceTemplateSchema.methods.createVersion = function(changes, userId) {
  const versionParts = this.version.split('.').map(Number);
  versionParts[2] += 1; // Increment patch version
  
  this.version = versionParts.join('.');
  this.changelog.push({
    version: this.version,
    changes,
    changedBy: userId
  });
  
  this.updatedBy = userId;
  return this.save();
};

// Static method to get default template
invoiceTemplateSchema.statics.getDefaultTemplate = function(type = 'standard') {
  return this.findOne({ 
    type, 
    isDefault: true, 
    isActive: true 
  });
};

// Static method to get popular templates
invoiceTemplateSchema.statics.getPopularTemplates = function(limit = 5) {
  return this.find({ 
    isActive: true 
  })
  .sort({ usageCount: -1 })
  .limit(limit);
};

module.exports = mongoose.model('InvoiceTemplate', invoiceTemplateSchema);
