const mongoose = require('mongoose');

const aiInteractionSchema = new mongoose.Schema({
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient'
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sessionId: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['treatment', 'exercise', 'progress', 'documentation', 'assessment', 'general'],
    default: 'general'
  },
  provider: {
    type: String,
    enum: ['gemini', 'chatgpt', 'local', 'claude'],
    default: 'gemini'
  },
  model: {
    type: String,
    default: 'gemini-pro'
  },
  prompt: {
    type: String,
    required: true,
    maxlength: 4000
  },
  response: {
    type: String,
    maxlength: 8000
  },
  context: {
    patientData: mongoose.Schema.Types.Mixed,
    assessmentData: mongoose.Schema.Types.Mixed,
    exerciseData: mongoose.Schema.Types.Mixed,
    previousInteractions: [String]
  },
  usage: {
    promptTokens: {
      type: Number,
      default: 0
    },
    completionTokens: {
      type: Number,
      default: 0
    },
    totalTokens: {
      type: Number,
      default: 0
    },
    cost: {
      type: Number,
      default: 0
    }
  },
  performance: {
    responseTime: {
      type: Number, // milliseconds
      default: 0
    },
    quality: {
      type: Number,
      min: 1,
      max: 5
    },
    relevance: {
      type: Number,
      min: 1,
      max: 5
    },
    accuracy: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  feedback: {
    helpful: {
      type: Boolean
    },
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comments: String,
    submittedAt: Date
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'timeout'],
    default: 'pending'
  },
  error: {
    message: String,
    code: String,
    details: mongoose.Schema.Types.Mixed
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    source: {
      type: String,
      enum: ['web', 'mobile', 'api'],
      default: 'web'
    },
    language: {
      type: String,
      default: 'en'
    },
    timezone: String
  },
  tags: [String],
  isArchived: {
    type: Boolean,
    default: false
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
aiInteractionSchema.index({ userId: 1, createdAt: -1 });
aiInteractionSchema.index({ patientId: 1, createdAt: -1 });
aiInteractionSchema.index({ sessionId: 1 });
aiInteractionSchema.index({ type: 1, provider: 1 });
aiInteractionSchema.index({ status: 1, createdAt: -1 });
aiInteractionSchema.index({ 'feedback.helpful': 1 });
aiInteractionSchema.index({ isArchived: 1, createdAt: -1 });

// Virtual for cost in dollars
aiInteractionSchema.virtual('costInDollars').get(function() {
  return this.usage.cost ? (this.usage.cost / 100).toFixed(4) : '0.0000';
});

// Virtual for average performance score
aiInteractionSchema.virtual('averagePerformanceScore').get(function() {
  if (!this.performance) return null;
  
  const scores = [];
  if (this.performance.quality) scores.push(this.performance.quality);
  if (this.performance.relevance) scores.push(this.performance.relevance);
  if (this.performance.accuracy) scores.push(this.performance.accuracy);
  
  if (scores.length === 0) return null;
  
  const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
  return Math.round(average * 10) / 10;
});

// Virtual for response time in seconds
aiInteractionSchema.virtual('responseTimeInSeconds').get(function() {
  return this.performance.responseTime ? (this.performance.responseTime / 1000).toFixed(2) : '0.00';
});

// Method to mark as completed
aiInteractionSchema.methods.markAsCompleted = function(response, usage, responseTime) {
  this.status = 'completed';
  this.response = response;
  
  if (usage) {
    this.usage = { ...this.usage, ...usage };
  }
  
  if (responseTime) {
    this.performance.responseTime = responseTime;
  }
  
  return this.save();
};

// Method to mark as failed
aiInteractionSchema.methods.markAsFailed = function(error) {
  this.status = 'failed';
  this.error = {
    message: error.message || 'Unknown error',
    code: error.code || 'UNKNOWN_ERROR',
    details: error.details || {}
  };
  
  return this.save();
};

// Method to add feedback
aiInteractionSchema.methods.addFeedback = function(feedback) {
  this.feedback = {
    ...feedback,
    submittedAt: new Date()
  };
  
  return this.save();
};

// Method to add performance metrics
aiInteractionSchema.methods.addPerformanceMetrics = function(metrics) {
  this.performance = { ...this.performance, ...metrics };
  return this.save();
};

// Static method to get user interaction history
aiInteractionSchema.statics.getUserHistory = function(userId, limit = 50) {
  return this.find({ userId, isArchived: false })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('patientId', 'name nameAr')
    .lean();
};

// Static method to get patient interaction history
aiInteractionSchema.statics.getPatientHistory = function(patientId, limit = 20) {
  return this.find({ patientId, isArchived: false })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('userId', 'name email')
    .lean();
};

// Static method to get session interactions
aiInteractionSchema.statics.getSessionInteractions = function(sessionId) {
  return this.find({ sessionId })
    .sort({ createdAt: 1 })
    .lean();
};

// Static method to get analytics
aiInteractionSchema.statics.getAnalytics = function(startDate, endDate, filters = {}) {
  const matchStage = { isArchived: false };
  
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }
  
  if (filters.provider) matchStage.provider = filters.provider;
  if (filters.type) matchStage.type = filters.type;
  if (filters.status) matchStage.status = filters.status;

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalInteractions: { $sum: 1 },
        completedInteractions: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        failedInteractions: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        totalTokens: { $sum: '$usage.totalTokens' },
        totalCost: { $sum: '$usage.cost' },
        avgResponseTime: { $avg: '$performance.responseTime' },
        avgQuality: { $avg: '$performance.quality' },
        avgRelevance: { $avg: '$performance.relevance' },
        avgAccuracy: { $avg: '$performance.accuracy' },
        helpfulCount: {
          $sum: { $cond: [{ $eq: ['$feedback.helpful', true] }, 1, 0] }
        },
        feedbackCount: {
          $sum: { $cond: [{ $ne: ['$feedback.helpful', null] }, 1, 0] }
        },
        uniqueUsers: { $addToSet: '$userId' },
        uniquePatients: { $addToSet: '$patientId' },
        providerBreakdown: {
          $push: '$provider'
        },
        typeBreakdown: {
          $push: '$type'
        }
      }
    },
    {
      $project: {
        _id: 0,
        totalInteractions: 1,
        completedInteractions: 1,
        failedInteractions: 1,
        successRate: {
          $round: [
            { $multiply: [{ $divide: ['$completedInteractions', '$totalInteractions'] }, 100] },
            1
          ]
        },
        totalTokens: 1,
        totalCost: 1,
        avgResponseTime: { $round: ['$avgResponseTime', 0] },
        avgQuality: { $round: ['$avgQuality', 1] },
        avgRelevance: { $round: ['$avgRelevance', 1] },
        avgAccuracy: { $round: ['$avgAccuracy', 1] },
        helpfulnessRate: {
          $round: [
            { $multiply: [{ $divide: ['$helpfulCount', '$feedbackCount'] }, 100] },
            1
          ]
        },
        uniqueUsers: { $size: '$uniqueUsers' },
        uniquePatients: { $size: '$uniquePatients' },
        providerBreakdown: 1,
        typeBreakdown: 1
      }
    }
  ]);
};

// Static method to get top queries
aiInteractionSchema.statics.getTopQueries = function(limit = 10, startDate, endDate) {
  const matchStage = { 
    status: 'completed',
    isArchived: false 
  };
  
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: { $substr: ['$prompt', 0, 100] }, // First 100 chars
        count: { $sum: 1 },
        avgRating: { $avg: '$feedback.rating' },
        lastUsed: { $max: '$createdAt' }
      }
    },
    { $sort: { count: -1 } },
    { $limit: limit },
    {
      $project: {
        _id: 0,
        query: '$_id',
        count: 1,
        avgRating: { $round: ['$avgRating', 1] },
        lastUsed: 1
      }
    }
  ]);
};

// Pre-save middleware
aiInteractionSchema.pre('save', function(next) {
  // Calculate total tokens if not set
  if (this.usage.promptTokens && this.usage.completionTokens && !this.usage.totalTokens) {
    this.usage.totalTokens = this.usage.promptTokens + this.usage.completionTokens;
  }
  
  // Set default metadata
  if (!this.metadata.language) {
    this.metadata.language = 'en';
  }
  
  next();
});

// Post-save middleware for logging
aiInteractionSchema.post('save', function(doc) {
  if (doc.status === 'completed') {
    console.log(`AI interaction completed: ${doc.type} query for ${doc.patientId ? 'patient' : 'general'}`);
  }
});

module.exports = mongoose.model('AIInteraction', aiInteractionSchema);
