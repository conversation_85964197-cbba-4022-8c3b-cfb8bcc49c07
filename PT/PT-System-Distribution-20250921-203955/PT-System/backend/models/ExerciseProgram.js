const mongoose = require('mongoose');

const exerciseSchema = new mongoose.Schema({
  exerciseId: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  nameAr: String,
  category: {
    type: String,
    enum: ['strength', 'flexibility', 'cardio', 'balance', 'coordination', 'functional'],
    required: true
  },
  bodyPart: [{
    type: String,
    enum: ['head', 'neck', 'shoulders', 'arms', 'chest', 'back', 'core', 'hips', 'legs', 'feet']
  }],
  difficulty: {
    type: Number,
    min: 1,
    max: 5,
    required: true
  },
  sets: {
    type: Number,
    min: 1,
    default: 1
  },
  reps: {
    type: Number,
    min: 1
  },
  duration: {
    value: Number,
    unit: {
      type: String,
      enum: ['seconds', 'minutes'],
      default: 'seconds'
    }
  },
  restTime: {
    value: Number,
    unit: {
      type: String,
      enum: ['seconds', 'minutes'],
      default: 'seconds'
    }
  },
  instructions: {
    type: String,
    required: true
  },
  instructionsAr: String,
  modifications: [{
    condition: String,
    instruction: String,
    instructionAr: String
  }],
  contraindications: [String],
  equipment: [String],
  videoUrl: String,
  imageUrl: String,
  notes: String
}, { _id: false });

const exerciseProgramSchema = new mongoose.Schema({
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  therapistId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  name: {
    type: String,
    required: true,
    maxlength: 100
  },
  nameAr: String,
  description: {
    type: String,
    maxlength: 500
  },
  descriptionAr: String,
  goals: [{
    type: String,
    maxlength: 200
  }],
  goalsAr: [String],
  duration: {
    value: {
      type: Number,
      required: true,
      min: 1
    },
    unit: {
      type: String,
      enum: ['days', 'weeks', 'months'],
      default: 'weeks'
    }
  },
  frequency: {
    timesPerWeek: {
      type: Number,
      min: 1,
      max: 7,
      required: true
    },
    sessionsPerDay: {
      type: Number,
      min: 1,
      default: 1
    }
  },
  difficulty: {
    type: Number,
    min: 1,
    max: 5,
    required: true
  },
  exercises: [exerciseSchema],
  status: {
    type: String,
    enum: ['draft', 'active', 'completed', 'paused', 'cancelled'],
    default: 'draft'
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: Date,
  completedSessions: {
    type: Number,
    default: 0
  },
  totalSessions: {
    type: Number,
    required: true
  },
  progress: {
    percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    lastSessionDate: Date,
    nextSessionDate: Date,
    adherenceRate: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    }
  },
  adaptations: [{
    date: {
      type: Date,
      default: Date.now
    },
    reason: String,
    changes: String,
    therapistId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  feedback: [{
    sessionDate: Date,
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    difficulty: {
      type: Number,
      min: 1,
      max: 5
    },
    pain: {
      type: Number,
      min: 0,
      max: 10
    },
    comments: String,
    completedExercises: [String] // Array of exercise IDs
  }],
  reminders: {
    enabled: {
      type: Boolean,
      default: true
    },
    time: String, // HH:MM format
    days: [{
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    }],
    channels: [{
      type: String,
      enum: ['email', 'sms', 'push']
    }]
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
exerciseProgramSchema.index({ patientId: 1, status: 1, createdAt: -1 });
exerciseProgramSchema.index({ therapistId: 1, createdAt: -1 });
exerciseProgramSchema.index({ status: 1, startDate: 1 });
exerciseProgramSchema.index({ 'progress.nextSessionDate': 1 });

// Virtual for total exercises
exerciseProgramSchema.virtual('totalExercises').get(function() {
  return this.exercises ? this.exercises.length : 0;
});

// Virtual for estimated session duration
exerciseProgramSchema.virtual('estimatedSessionDuration').get(function() {
  if (!this.exercises || this.exercises.length === 0) return 0;
  
  let totalMinutes = 0;
  this.exercises.forEach(exercise => {
    // Calculate exercise time
    let exerciseTime = 0;
    if (exercise.duration && exercise.duration.value) {
      exerciseTime = exercise.duration.unit === 'minutes' ? 
        exercise.duration.value : exercise.duration.value / 60;
    } else if (exercise.reps) {
      // Estimate 2 seconds per rep
      exerciseTime = (exercise.reps * 2) / 60;
    }
    
    // Multiply by sets
    exerciseTime *= exercise.sets || 1;
    
    // Add rest time
    if (exercise.restTime && exercise.restTime.value) {
      const restTime = exercise.restTime.unit === 'minutes' ? 
        exercise.restTime.value : exercise.restTime.value / 60;
      exerciseTime += restTime * (exercise.sets - 1);
    }
    
    totalMinutes += exerciseTime;
  });
  
  return Math.round(totalMinutes);
});

// Virtual for completion percentage
exerciseProgramSchema.virtual('completionPercentage').get(function() {
  if (!this.totalSessions || this.totalSessions === 0) return 0;
  return Math.round((this.completedSessions / this.totalSessions) * 100);
});

// Virtual for days remaining
exerciseProgramSchema.virtual('daysRemaining').get(function() {
  if (!this.endDate) return null;
  const today = new Date();
  const diffTime = this.endDate - today;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
});

// Method to mark session as completed
exerciseProgramSchema.methods.completeSession = function(feedback) {
  this.completedSessions += 1;
  this.progress.lastSessionDate = new Date();
  this.progress.percentage = this.completionPercentage;
  
  // Calculate next session date
  const daysUntilNext = Math.ceil(7 / this.frequency.timesPerWeek);
  this.progress.nextSessionDate = new Date(Date.now() + daysUntilNext * 24 * 60 * 60 * 1000);
  
  // Add feedback if provided
  if (feedback) {
    this.feedback.push({
      sessionDate: new Date(),
      ...feedback
    });
  }
  
  // Update adherence rate
  this.updateAdherenceRate();
  
  // Check if program is completed
  if (this.completedSessions >= this.totalSessions) {
    this.status = 'completed';
    this.endDate = new Date();
  }
  
  return this.save();
};

// Method to update adherence rate
exerciseProgramSchema.methods.updateAdherenceRate = function() {
  const daysSinceStart = Math.ceil((new Date() - this.startDate) / (1000 * 60 * 60 * 24));
  const expectedSessions = Math.floor((daysSinceStart / 7) * this.frequency.timesPerWeek);
  
  if (expectedSessions > 0) {
    this.progress.adherenceRate = Math.min(100, Math.round((this.completedSessions / expectedSessions) * 100));
  }
};

// Method to add adaptation
exerciseProgramSchema.methods.addAdaptation = function(reason, changes, therapistId) {
  this.adaptations.push({
    reason,
    changes,
    therapistId
  });
  
  return this.save();
};

// Static method to get patient programs
exerciseProgramSchema.statics.getPatientPrograms = function(patientId, status) {
  const query = { patientId };
  if (status) query.status = status;
  
  return this.find(query)
    .sort({ createdAt: -1 })
    .populate('therapistId', 'name email')
    .lean();
};

// Static method to get programs needing attention
exerciseProgramSchema.statics.getProgramsNeedingAttention = function() {
  const today = new Date();
  const threeDaysAgo = new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000);
  
  return this.find({
    status: 'active',
    $or: [
      { 'progress.lastSessionDate': { $lt: threeDaysAgo } },
      { 'progress.adherenceRate': { $lt: 70 } }
    ]
  }).populate('patientId therapistId');
};

// Static method to get analytics
exerciseProgramSchema.statics.getAnalytics = function(startDate, endDate) {
  const matchStage = {};
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalPrograms: { $sum: 1 },
        activePrograms: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        completedPrograms: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        avgDifficulty: { $avg: '$difficulty' },
        avgCompletionRate: { $avg: '$progress.percentage' },
        avgAdherenceRate: { $avg: '$progress.adherenceRate' },
        totalExercises: { $sum: { $size: '$exercises' } },
        uniquePatients: { $addToSet: '$patientId' }
      }
    },
    {
      $project: {
        _id: 0,
        totalPrograms: 1,
        activePrograms: 1,
        completedPrograms: 1,
        avgDifficulty: { $round: ['$avgDifficulty', 1] },
        avgCompletionRate: { $round: ['$avgCompletionRate', 1] },
        avgAdherenceRate: { $round: ['$avgAdherenceRate', 1] },
        totalExercises: 1,
        uniquePatients: { $size: '$uniquePatients' }
      }
    }
  ]);
};

// Pre-save middleware
exerciseProgramSchema.pre('save', function(next) {
  // Calculate total sessions if not set
  if (!this.totalSessions) {
    const totalWeeks = this.duration.unit === 'weeks' ? this.duration.value :
                      this.duration.unit === 'days' ? Math.ceil(this.duration.value / 7) :
                      this.duration.value * 4; // months to weeks
    
    this.totalSessions = totalWeeks * this.frequency.timesPerWeek * this.frequency.sessionsPerDay;
  }
  
  // Set end date if not set
  if (!this.endDate && this.startDate) {
    const durationInDays = this.duration.unit === 'days' ? this.duration.value :
                          this.duration.unit === 'weeks' ? this.duration.value * 7 :
                          this.duration.value * 30; // months to days
    
    this.endDate = new Date(this.startDate.getTime() + durationInDays * 24 * 60 * 60 * 1000);
  }
  
  // Update adherence rate
  if (this.isModified('completedSessions') || this.isModified('startDate')) {
    this.updateAdherenceRate();
  }
  
  next();
});

// Post-save middleware for logging
exerciseProgramSchema.post('save', function(doc) {
  console.log(`Exercise program ${doc.status} for patient: ${doc.patientId}`);
});

module.exports = mongoose.model('ExerciseProgram', exerciseProgramSchema);
