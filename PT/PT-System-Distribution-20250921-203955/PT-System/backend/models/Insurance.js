const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Insurance:
 *       type: object
 *       required:
 *         - provider
 *         - policyNumber
 *         - patient
 *       properties:
 *         provider:
 *           type: string
 *           description: Insurance provider name
 *         policyNumber:
 *           type: string
 *           description: Policy number
 *         patient:
 *           type: string
 *           description: Patient ID
 */

const insuranceSchema = new mongoose.Schema({
  // Core information
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Patient is required']
  },
  
  // Insurance provider details
  provider: {
    name: {
      type: String,
      required: [true, 'Insurance provider name is required'],
      trim: true
    },
    code: {
      type: String,
      trim: true
    },
    contactInfo: {
      phone: String,
      email: String,
      website: String,
      address: {
        street: String,
        city: String,
        region: String,
        postalCode: String
      }
    }
  },
  
  // Policy details
  policyNumber: {
    type: String,
    required: [true, 'Policy number is required'],
    trim: true
  },
  groupNumber: {
    type: String,
    trim: true
  },
  memberNumber: {
    type: String,
    trim: true
  },
  
  // Coverage details
  coverage: {
    type: {
      type: String,
      enum: ['individual', 'family', 'group'],
      default: 'individual'
    },
    effectiveDate: {
      type: Date,
      required: [true, 'Effective date is required']
    },
    expiryDate: {
      type: Date,
      required: [true, 'Expiry date is required']
    },
    deductible: {
      type: Number,
      min: 0,
      default: 0
    },
    copayAmount: {
      type: Number,
      min: 0,
      default: 0
    },
    copayPercentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    maxCoverage: {
      type: Number,
      min: 0
    },
    usedCoverage: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  
  // Benefits
  benefits: {
    physicalTherapy: {
      covered: {
        type: Boolean,
        default: true
      },
      sessionsPerYear: Number,
      copayAmount: Number,
      requiresPreAuth: {
        type: Boolean,
        default: false
      }
    },
    occupationalTherapy: {
      covered: {
        type: Boolean,
        default: true
      },
      sessionsPerYear: Number,
      copayAmount: Number,
      requiresPreAuth: {
        type: Boolean,
        default: false
      }
    },
    speechTherapy: {
      covered: {
        type: Boolean,
        default: true
      },
      sessionsPerYear: Number,
      copayAmount: Number,
      requiresPreAuth: {
        type: Boolean,
        default: false
      }
    },
    specialNeeds: {
      covered: {
        type: Boolean,
        default: true
      },
      additionalBenefits: [String],
      requiresPreAuth: {
        type: Boolean,
        default: true
      }
    }
  },
  
  // Pre-authorization
  preAuthorization: {
    required: {
      type: Boolean,
      default: false
    },
    number: String,
    approvedSessions: Number,
    approvedAmount: Number,
    validFrom: Date,
    validTo: Date,
    status: {
      type: String,
      enum: ['pending', 'approved', 'denied', 'expired'],
      default: 'pending'
    },
    notes: String
  },
  
  // Status and verification
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'expired'],
    default: 'active'
  },
  verificationStatus: {
    type: String,
    enum: ['verified', 'pending', 'failed', 'expired'],
    default: 'pending'
  },
  lastVerified: Date,
  verificationNotes: String,
  
  // Claims tracking
  claimsHistory: [{
    claimNumber: String,
    submissionDate: Date,
    amount: Number,
    status: {
      type: String,
      enum: ['submitted', 'processing', 'approved', 'denied', 'paid']
    },
    paidAmount: Number,
    denialReason: String,
    processedDate: Date
  }],
  
  // System fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for remaining coverage
insuranceSchema.virtual('remainingCoverage').get(function() {
  if (this.coverage.maxCoverage) {
    return Math.max(0, this.coverage.maxCoverage - this.coverage.usedCoverage);
  }
  return null;
});

// Virtual for coverage percentage used
insuranceSchema.virtual('coverageUsedPercentage').get(function() {
  if (this.coverage.maxCoverage && this.coverage.maxCoverage > 0) {
    return Math.round((this.coverage.usedCoverage / this.coverage.maxCoverage) * 100);
  }
  return 0;
});

// Virtual for insurance status
insuranceSchema.virtual('insuranceStatus').get(function() {
  const now = new Date();
  
  if (this.coverage.expiryDate < now) {
    return 'expired';
  }
  
  if (this.status === 'active' && this.verificationStatus === 'verified') {
    return 'active';
  }
  
  return this.status;
});

// Virtual for days until expiry
insuranceSchema.virtual('daysUntilExpiry').get(function() {
  const now = new Date();
  const expiry = new Date(this.coverage.expiryDate);
  const diffTime = expiry - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Indexes for better performance
insuranceSchema.index({ patient: 1 });
insuranceSchema.index({ policyNumber: 1 });
insuranceSchema.index({ 'provider.name': 1 });
insuranceSchema.index({ status: 1 });
insuranceSchema.index({ 'coverage.expiryDate': 1 });
insuranceSchema.index({ verificationStatus: 1 });

// Pre-save middleware
insuranceSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.constructor.currentUser;
  }
  
  // Update status based on expiry date
  if (this.coverage.expiryDate < new Date()) {
    this.status = 'expired';
  }
  
  next();
});

// Static method to get active insurances
insuranceSchema.statics.getActiveInsurances = function() {
  return this.find({
    status: 'active',
    'coverage.expiryDate': { $gte: new Date() }
  }).populate('patient', 'firstName lastName nationalId');
};

// Static method to get expiring insurances
insuranceSchema.statics.getExpiringInsurances = function(days = 30) {
  const futureDate = new Date();
  futureDate.setDate(futureDate.getDate() + days);
  
  return this.find({
    status: 'active',
    'coverage.expiryDate': {
      $gte: new Date(),
      $lte: futureDate
    }
  }).populate('patient', 'firstName lastName nationalId phone email');
};

// Instance method to verify insurance
insuranceSchema.methods.verify = async function() {
  // Mock verification process
  this.verificationStatus = 'verified';
  this.lastVerified = new Date();
  this.verificationNotes = 'Verified successfully';
  
  return this.save();
};

// Instance method to check eligibility
insuranceSchema.methods.checkEligibility = function(serviceType) {
  const benefit = this.benefits[serviceType];
  
  if (!benefit || !benefit.covered) {
    return {
      eligible: false,
      reason: 'Service not covered'
    };
  }
  
  if (this.insuranceStatus !== 'active') {
    return {
      eligible: false,
      reason: 'Insurance not active'
    };
  }
  
  return {
    eligible: true,
    copayAmount: benefit.copayAmount || this.coverage.copayAmount,
    requiresPreAuth: benefit.requiresPreAuth,
    sessionsRemaining: benefit.sessionsPerYear // This would need more complex calculation
  };
};

// Instance method to add claim
insuranceSchema.methods.addClaim = function(claimData) {
  this.claimsHistory.push({
    ...claimData,
    submissionDate: new Date(),
    status: 'submitted'
  });
  
  return this.save();
};

module.exports = mongoose.model('Insurance', insuranceSchema);
