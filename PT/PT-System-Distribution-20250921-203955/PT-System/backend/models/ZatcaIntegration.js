const mongoose = require('mongoose');

const zatcaIntegrationSchema = new mongoose.Schema({
  // Organization Information
  organization: {
    taxNumber: {
      type: String,
      required: true,
      unique: true
    },
    commercialRegistration: String,
    organizationName: {
      type: String,
      required: true
    },
    organizationNameArabic: String,
    address: {
      street: String,
      city: String,
      postalCode: String,
      country: {
        type: String,
        default: 'SA'
      }
    },
    contactInfo: {
      phone: String,
      email: String
    }
  },
  
  // ZATCA Registration Status
  registrationStatus: {
    type: String,
    enum: ['pending', 'registered', 'suspended', 'cancelled'],
    default: 'pending'
  },
  
  registrationDate: Date,
  
  // Certificate Information
  certificate: {
    serialNumber: String,
    issuer: String,
    subject: String,
    validFrom: Date,
    validTo: Date,
    publicKey: String,
    privateKey: String, // Encrypted
    status: {
      type: String,
      enum: ['active', 'expired', 'revoked'],
      default: 'active'
    }
  },
  
  // Invoice Integration
  invoiceSettings: {
    invoicePrefix: {
      type: String,
      default: 'INV'
    },
    currentSequence: {
      type: Number,
      default: 1
    },
    qrCodeEnabled: {
      type: Boolean,
      default: true
    },
    digitalSignatureEnabled: {
      type: Boolean,
      default: true
    },
    xmlFormat: {
      type: String,
      enum: ['UBL2.1', 'UBL2.0'],
      default: 'UBL2.1'
    }
  },
  
  // Submitted Invoices
  submittedInvoices: [{
    invoiceId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Billing'
    },
    zatcaInvoiceNumber: String,
    uuid: String,
    hash: String,
    qrCode: String,
    xmlData: String,
    submissionDate: Date,
    status: {
      type: String,
      enum: ['submitted', 'accepted', 'rejected', 'warning'],
      default: 'submitted'
    },
    responseCode: String,
    responseMessage: String,
    validationErrors: [{
      code: String,
      message: String,
      severity: String
    }],
    clearanceStatus: {
      type: String,
      enum: ['cleared', 'not_cleared', 'pending'],
      default: 'pending'
    }
  }],
  
  // Tax Configuration
  taxConfiguration: {
    vatRate: {
      type: Number,
      default: 15
    },
    exemptionCategories: [{
      code: String,
      description: String,
      rate: Number
    }],
    taxableServices: [{
      serviceCode: String,
      serviceName: String,
      taxRate: Number,
      exemptionCode: String
    }]
  },
  
  // Reporting
  reports: [{
    reportType: {
      type: String,
      enum: ['monthly', 'quarterly', 'annual'],
      required: true
    },
    period: {
      year: Number,
      month: Number,
      quarter: Number
    },
    totalSales: Number,
    totalTax: Number,
    totalExempt: Number,
    submissionDate: Date,
    status: {
      type: String,
      enum: ['draft', 'submitted', 'accepted', 'rejected'],
      default: 'draft'
    },
    reportData: mongoose.Schema.Types.Mixed
  }],
  
  // Compliance Checks
  complianceChecks: [{
    checkType: {
      type: String,
      enum: ['invoice_format', 'tax_calculation', 'signature_validation', 'qr_code'],
      required: true
    },
    status: {
      type: String,
      enum: ['passed', 'failed', 'warning'],
      required: true
    },
    details: String,
    checkedAt: {
      type: Date,
      default: Date.now
    },
    checkedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // API Configuration
  apiConfiguration: {
    environment: {
      type: String,
      enum: ['sandbox', 'production'],
      default: 'sandbox'
    },
    baseUrl: String,
    clientId: String,
    clientSecret: String, // Encrypted
    accessToken: String, // Encrypted
    refreshToken: String, // Encrypted
    tokenExpiryDate: Date,
    lastSyncDate: Date
  },
  
  // Error Logs
  errorLogs: [{
    operation: String,
    errorCode: String,
    errorMessage: String,
    stackTrace: String,
    timestamp: {
      type: Date,
      default: Date.now
    },
    resolved: {
      type: Boolean,
      default: false
    }
  }],
  
  // Audit Trail
  auditLog: [{
    action: String,
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    details: mongoose.Schema.Types.Mixed,
    ipAddress: String
  }],
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
zatcaIntegrationSchema.index({ 'organization.taxNumber': 1 });
zatcaIntegrationSchema.index({ registrationStatus: 1 });
zatcaIntegrationSchema.index({ 'submittedInvoices.status': 1 });
zatcaIntegrationSchema.index({ 'submittedInvoices.submissionDate': -1 });

// Virtual for certificate validity
zatcaIntegrationSchema.virtual('isCertificateValid').get(function() {
  return this.certificate.status === 'active' && 
         this.certificate.validTo > new Date();
});

// Method to generate invoice hash
zatcaIntegrationSchema.methods.generateInvoiceHash = function(invoiceData) {
  // Implementation for ZATCA-compliant hash generation
  const crypto = require('crypto');
  const dataString = JSON.stringify(invoiceData);
  return crypto.createHash('sha256').update(dataString).digest('hex');
};

// Method to generate QR code
zatcaIntegrationSchema.methods.generateQRCode = function(invoiceData) {
  // Implementation for ZATCA-compliant QR code generation
  const qrData = {
    seller: this.organization.organizationName,
    taxNumber: this.organization.taxNumber,
    timestamp: new Date().toISOString(),
    total: invoiceData.totalAmount,
    tax: invoiceData.taxAmount
  };
  
  return Buffer.from(JSON.stringify(qrData)).toString('base64');
};

// Method to submit invoice to ZATCA
zatcaIntegrationSchema.methods.submitInvoice = async function(invoiceData) {
  // This will be implemented with actual ZATCA API calls
  const hash = this.generateInvoiceHash(invoiceData);
  const qrCode = this.generateQRCode(invoiceData);
  
  const submission = {
    invoiceId: invoiceData._id,
    zatcaInvoiceNumber: `${this.invoiceSettings.invoicePrefix}-${this.invoiceSettings.currentSequence}`,
    hash,
    qrCode,
    submissionDate: new Date(),
    status: 'submitted'
  };
  
  this.submittedInvoices.push(submission);
  this.invoiceSettings.currentSequence += 1;
  
  return this.save();
};

// Method to validate compliance
zatcaIntegrationSchema.methods.validateCompliance = function() {
  const checks = [];
  
  // Check certificate validity
  if (!this.isCertificateValid) {
    checks.push({
      checkType: 'signature_validation',
      status: 'failed',
      details: 'Certificate is expired or invalid'
    });
  }
  
  // Check tax configuration
  if (!this.taxConfiguration.vatRate) {
    checks.push({
      checkType: 'tax_calculation',
      status: 'failed',
      details: 'VAT rate not configured'
    });
  }
  
  this.complianceChecks.push(...checks);
  return this.save();
};

module.exports = mongoose.model('ZatcaIntegration', zatcaIntegrationSchema);
