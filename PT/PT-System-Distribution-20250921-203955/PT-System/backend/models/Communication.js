const mongoose = require('mongoose');

const communicationSchema = new mongoose.Schema({
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  senderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  channels: [{
    type: String,
    enum: ['email', 'sms', 'whatsapp', 'push'],
    required: true
  }],
  subject: {
    type: String,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    maxlength: 2000
  },
  messageAr: {
    type: String,
    maxlength: 2000
  },
  urgency: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  status: {
    type: String,
    enum: ['pending', 'sent', 'delivered', 'failed', 'cancelled'],
    default: 'pending'
  },
  scheduledTime: {
    type: Date
  },
  sentAt: {
    type: Date
  },
  deliveredAt: {
    type: Date
  },
  template: {
    type: String,
    enum: [
      'appointment_reminder',
      'appointment_confirmation',
      'exercise_reminder',
      'progress_update',
      'medication_reminder',
      'follow_up',
      'emergency_alert',
      'custom'
    ],
    default: 'custom'
  },
  templateData: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  },
  deliveryResults: [{
    channel: {
      type: String,
      enum: ['email', 'sms', 'whatsapp', 'push']
    },
    status: {
      type: String,
      enum: ['sent', 'delivered', 'failed', 'bounced']
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    errorMessage: String,
    externalId: String // ID from external service (Twilio, SendGrid, etc.)
  }],
  readAt: {
    type: Date
  },
  responseReceived: {
    type: Boolean,
    default: false
  },
  response: {
    message: String,
    receivedAt: Date,
    channel: String
  },
  metadata: {
    ipAddress: String,
    userAgent: String,
    source: {
      type: String,
      enum: ['web', 'mobile', 'api', 'system'],
      default: 'web'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
communicationSchema.index({ patientId: 1, createdAt: -1 });
communicationSchema.index({ senderId: 1, createdAt: -1 });
communicationSchema.index({ status: 1, scheduledTime: 1 });
communicationSchema.index({ urgency: 1, createdAt: -1 });
communicationSchema.index({ template: 1 });

// Virtual for delivery success rate
communicationSchema.virtual('deliverySuccessRate').get(function() {
  if (!this.deliveryResults || this.deliveryResults.length === 0) return 0;
  
  const successful = this.deliveryResults.filter(result => 
    result.status === 'sent' || result.status === 'delivered'
  ).length;
  
  return Math.round((successful / this.deliveryResults.length) * 100);
});

// Virtual for total delivery attempts
communicationSchema.virtual('totalDeliveryAttempts').get(function() {
  return this.deliveryResults ? this.deliveryResults.length : 0;
});

// Virtual for is overdue (for scheduled messages)
communicationSchema.virtual('isOverdue').get(function() {
  if (!this.scheduledTime || this.status !== 'pending') return false;
  return new Date() > this.scheduledTime;
});

// Method to mark as sent
communicationSchema.methods.markAsSent = function(channel, externalId) {
  this.status = 'sent';
  this.sentAt = new Date();
  
  if (!this.deliveryResults) this.deliveryResults = [];
  this.deliveryResults.push({
    channel,
    status: 'sent',
    timestamp: new Date(),
    externalId
  });
  
  return this.save();
};

// Method to mark as delivered
communicationSchema.methods.markAsDelivered = function(channel, externalId) {
  this.status = 'delivered';
  this.deliveredAt = new Date();
  
  // Update delivery result if exists
  const existingResult = this.deliveryResults.find(
    result => result.channel === channel && result.externalId === externalId
  );
  
  if (existingResult) {
    existingResult.status = 'delivered';
    existingResult.timestamp = new Date();
  } else {
    this.deliveryResults.push({
      channel,
      status: 'delivered',
      timestamp: new Date(),
      externalId
    });
  }
  
  return this.save();
};

// Method to mark as failed
communicationSchema.methods.markAsFailed = function(channel, errorMessage, externalId) {
  this.status = 'failed';
  
  if (!this.deliveryResults) this.deliveryResults = [];
  this.deliveryResults.push({
    channel,
    status: 'failed',
    timestamp: new Date(),
    errorMessage,
    externalId
  });
  
  return this.save();
};

// Method to add response
communicationSchema.methods.addResponse = function(message, channel) {
  this.responseReceived = true;
  this.response = {
    message,
    receivedAt: new Date(),
    channel
  };
  
  return this.save();
};

// Static method to get patient communication history
communicationSchema.statics.getPatientHistory = function(patientId, limit = 20) {
  return this.find({ patientId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('senderId', 'name email')
    .populate('patientId', 'name nameAr phone email')
    .lean();
};

// Static method to get pending scheduled messages
communicationSchema.statics.getPendingScheduled = function() {
  return this.find({
    status: 'pending',
    scheduledTime: { $lte: new Date() }
  }).populate('patientId senderId');
};

// Static method to get communication analytics
communicationSchema.statics.getAnalytics = function(startDate, endDate) {
  const matchStage = {};
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalMessages: { $sum: 1 },
        sentMessages: {
          $sum: { $cond: [{ $eq: ['$status', 'sent'] }, 1, 0] }
        },
        deliveredMessages: {
          $sum: { $cond: [{ $eq: ['$status', 'delivered'] }, 1, 0] }
        },
        failedMessages: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
        },
        emailCount: {
          $sum: { $cond: [{ $in: ['email', '$channels'] }, 1, 0] }
        },
        smsCount: {
          $sum: { $cond: [{ $in: ['sms', '$channels'] }, 1, 0] }
        },
        whatsappCount: {
          $sum: { $cond: [{ $in: ['whatsapp', '$channels'] }, 1, 0] }
        },
        pushCount: {
          $sum: { $cond: [{ $in: ['push', '$channels'] }, 1, 0] }
        },
        uniquePatients: { $addToSet: '$patientId' }
      }
    },
    {
      $project: {
        _id: 0,
        totalMessages: 1,
        sentMessages: 1,
        deliveredMessages: 1,
        failedMessages: 1,
        emailCount: 1,
        smsCount: 1,
        whatsappCount: 1,
        pushCount: 1,
        uniquePatients: { $size: '$uniquePatients' },
        deliveryRate: {
          $round: [
            { $multiply: [{ $divide: ['$deliveredMessages', '$totalMessages'] }, 100] },
            1
          ]
        }
      }
    }
  ]);
};

// Pre-save middleware
communicationSchema.pre('save', function(next) {
  // Set sent time if status is being changed to sent
  if (this.isModified('status') && this.status === 'sent' && !this.sentAt) {
    this.sentAt = new Date();
  }
  
  // Set delivered time if status is being changed to delivered
  if (this.isModified('status') && this.status === 'delivered' && !this.deliveredAt) {
    this.deliveredAt = new Date();
  }
  
  next();
});

// Post-save middleware for logging
communicationSchema.post('save', function(doc) {
  console.log(`Communication ${doc.status} for patient: ${doc.patientId}`);
});

module.exports = mongoose.model('Communication', communicationSchema);
