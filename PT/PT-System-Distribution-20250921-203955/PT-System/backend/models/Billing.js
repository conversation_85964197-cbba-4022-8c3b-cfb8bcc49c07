const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Billing:
 *       type: object
 *       required:
 *         - patient
 *         - amount
 *         - services
 *       properties:
 *         patient:
 *           type: string
 *           description: Patient ID
 *         amount:
 *           type: number
 *           description: Total billing amount
 *         services:
 *           type: array
 *           items:
 *             type: object
 *           description: List of services provided
 */

const billingSchema = new mongoose.Schema({
  // Core information
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Patient is required']
  },
  invoiceNumber: {
    type: String,
    unique: true,
    default: function() {
      return `INV-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
    }
  },
  
  // Services and charges
  services: [{
    name: {
      type: String,
      required: true
    },
    code: String, // CPT code or internal service code
    description: String,
    quantity: {
      type: Number,
      required: true,
      min: 1,
      default: 1
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0
    },
    totalPrice: {
      type: Number,
      required: true,
      min: 0
    },
    date: {
      type: Date,
      required: true
    },
    provider: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    appointment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Appointment'
    },
    treatmentPlan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'TreatmentPlan'
    }
  }],
  
  // Financial details
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  tax: {
    rate: {
      type: Number,
      min: 0,
      max: 100,
      default: 15 // Saudi VAT rate
    },
    amount: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  discount: {
    type: {
      type: String,
      enum: ['percentage', 'fixed'],
      default: 'percentage'
    },
    value: {
      type: Number,
      min: 0,
      default: 0
    },
    amount: {
      type: Number,
      min: 0,
      default: 0
    },
    reason: String
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  
  // Insurance information
  insurance: {
    provider: String,
    policyNumber: String,
    claimNumber: String,
    preAuthNumber: String,
    coveredAmount: {
      type: Number,
      min: 0,
      default: 0
    },
    copayAmount: {
      type: Number,
      min: 0,
      default: 0
    },
    deductibleAmount: {
      type: Number,
      min: 0,
      default: 0
    },
    status: {
      type: String,
      enum: ['pending', 'submitted', 'approved', 'denied', 'paid'],
      default: 'pending'
    },
    submissionDate: Date,
    responseDate: Date,
    denialReason: String
  },
  
  // Payment tracking
  payments: [{
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    method: {
      type: String,
      enum: ['cash', 'card', 'bank_transfer', 'insurance', 'check'],
      required: true
    },
    reference: String, // Transaction ID, check number, etc.
    date: {
      type: Date,
      default: Date.now
    },
    receivedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  }],
  
  // Status and dates
  status: {
    type: String,
    enum: ['draft', 'sent', 'viewed', 'paid', 'partially_paid', 'overdue', 'cancelled'],
    default: 'draft'
  },
  issueDate: {
    type: Date,
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: true
  },
  sentDate: Date,
  paidDate: Date,
  
  // Communication
  emailSent: {
    type: Boolean,
    default: false
  },
  emailSentDate: Date,
  remindersSent: {
    type: Number,
    default: 0
  },
  lastReminderDate: Date,
  
  // Notes and attachments
  notes: String,
  internalNotes: String,
  attachments: [{
    name: String,
    url: String,
    type: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  
  // System fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },

  // ZATCA Integration
  zatcaSubmission: {
    submitted: {
      type: Boolean,
      default: false
    },
    submissionDate: Date,
    zatcaInvoiceNumber: String,
    hash: String,
    qrCode: String,
    status: {
      type: String,
      enum: ['pending', 'accepted', 'rejected'],
      default: 'pending'
    },
    responseCode: String,
    responseMessage: String
  },

  // Electronic Signature Status
  signatureStatus: {
    type: String,
    enum: ['not_required', 'pending', 'signed', 'rejected'],
    default: 'not_required'
  },

  signedDate: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for total paid amount
billingSchema.virtual('totalPaid').get(function() {
  return this.payments.reduce((sum, payment) => sum + payment.amount, 0);
});

// Virtual for remaining balance
billingSchema.virtual('remainingBalance').get(function() {
  return Math.max(0, this.totalAmount - this.totalPaid);
});

// Virtual for payment status
billingSchema.virtual('paymentStatus').get(function() {
  const totalPaid = this.totalPaid;
  const totalAmount = this.totalAmount;
  
  if (totalPaid === 0) {
    return 'unpaid';
  } else if (totalPaid >= totalAmount) {
    return 'paid';
  } else {
    return 'partially_paid';
  }
});

// Virtual for days overdue
billingSchema.virtual('daysOverdue').get(function() {
  if (this.status === 'paid' || !this.dueDate) {
    return 0;
  }
  
  const now = new Date();
  const dueDate = new Date(this.dueDate);
  
  if (now > dueDate) {
    const diffTime = now - dueDate;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  
  return 0;
});

// Virtual for insurance portion
billingSchema.virtual('insurancePortion').get(function() {
  return this.insurance.coveredAmount || 0;
});

// Virtual for patient portion
billingSchema.virtual('patientPortion').get(function() {
  return this.totalAmount - this.insurancePortion;
});

// Indexes for better performance
billingSchema.index({ patient: 1, issueDate: -1 });
// invoiceNumber already has unique index
billingSchema.index({ status: 1 });
billingSchema.index({ dueDate: 1 });
billingSchema.index({ 'insurance.status': 1 });
billingSchema.index({ createdAt: -1 });

// Pre-save middleware
billingSchema.pre('save', function(next) {
  // Calculate subtotal
  this.subtotal = this.services.reduce((sum, service) => sum + service.totalPrice, 0);
  
  // Calculate tax amount
  this.tax.amount = (this.subtotal * this.tax.rate) / 100;
  
  // Calculate discount amount
  if (this.discount.type === 'percentage') {
    this.discount.amount = (this.subtotal * this.discount.value) / 100;
  } else {
    this.discount.amount = this.discount.value;
  }
  
  // Calculate total amount
  this.totalAmount = this.subtotal + this.tax.amount - this.discount.amount;
  
  // Update status based on payments
  const totalPaid = this.totalPaid;
  if (totalPaid >= this.totalAmount && this.status !== 'paid') {
    this.status = 'paid';
    this.paidDate = new Date();
  } else if (totalPaid > 0 && totalPaid < this.totalAmount) {
    this.status = 'partially_paid';
  }
  
  // Check if overdue
  if (this.dueDate < new Date() && this.status !== 'paid' && this.status !== 'cancelled') {
    this.status = 'overdue';
  }
  
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.constructor.currentUser;
  }
  
  next();
});

// Static method to get overdue invoices
billingSchema.statics.getOverdueInvoices = function() {
  return this.find({
    dueDate: { $lt: new Date() },
    status: { $nin: ['paid', 'cancelled'] }
  }).populate('patient', 'firstName lastName nationalId phone email');
};

// Static method to get revenue by period
billingSchema.statics.getRevenueByPeriod = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        issueDate: {
          $gte: startDate,
          $lte: endDate
        },
        status: { $ne: 'cancelled' }
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$totalAmount' },
        totalPaid: { $sum: { $sum: '$payments.amount' } },
        invoiceCount: { $sum: 1 }
      }
    }
  ]);
};

// Instance method to add payment
billingSchema.methods.addPayment = function(paymentData) {
  this.payments.push({
    ...paymentData,
    date: new Date()
  });
  
  return this.save();
};

// Instance method to send reminder
billingSchema.methods.sendReminder = async function() {
  this.remindersSent += 1;
  this.lastReminderDate = new Date();
  
  // Here you would implement actual email/SMS sending logic
  
  return this.save();
};

// Instance method to mark as sent
billingSchema.methods.markAsSent = function() {
  this.status = 'sent';
  this.sentDate = new Date();
  this.emailSent = true;
  this.emailSentDate = new Date();
  
  return this.save();
};

module.exports = mongoose.model('Billing', billingSchema);
