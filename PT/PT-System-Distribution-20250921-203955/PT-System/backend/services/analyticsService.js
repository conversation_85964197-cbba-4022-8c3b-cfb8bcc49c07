const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');
const TreatmentPlan = require('../models/TreatmentPlan');
const FormSubmission = require('../models/FormSubmission');
const Billing = require('../models/Billing');
const User = require('../models/User');

class AnalyticsService {
  // Patient Outcome Metrics
  static async getPatientOutcomes(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Recovery rate calculation
      const completedTreatments = await TreatmentPlan.countDocuments({
        status: 'completed',
        updatedAt: { $gte: startDate }
      });

      const totalTreatments = await TreatmentPlan.countDocuments({
        createdAt: { $gte: startDate }
      });

      const recoveryRate = totalTreatments > 0 ? (completedTreatments / totalTreatments) * 100 : 0;

      // Patient satisfaction from form submissions
      const satisfactionScores = await FormSubmission.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            'formData.satisfaction': { $exists: true }
          }
        },
        {
          $group: {
            _id: null,
            averageSatisfaction: { $avg: '$formData.satisfaction' },
            totalResponses: { $sum: 1 }
          }
        }
      ]);

      const satisfaction = satisfactionScores.length > 0 ? satisfactionScores[0].averageSatisfaction : 0;

      // Total patients in period
      const totalPatients = await Patient.countDocuments({
        createdAt: { $gte: startDate }
      });

      return {
        recoveryRate: Math.round(recoveryRate),
        satisfaction: Math.round(satisfaction * 10) / 10,
        totalPatients,
        completedTreatments,
        totalTreatments
      };
    } catch (error) {
      console.error('Error calculating patient outcomes:', error);
      throw error;
    }
  }

  // Treatment Effectiveness Analysis
  static async getTreatmentEffectiveness() {
    try {
      const treatmentStats = await TreatmentPlan.aggregate([
        {
          $group: {
            _id: '$treatmentType',
            totalCases: { $sum: 1 },
            completedCases: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            averageDuration: { $avg: '$duration' }
          }
        },
        {
          $project: {
            treatment: '$_id',
            totalCases: 1,
            completedCases: 1,
            successRate: {
              $multiply: [
                { $divide: ['$completedCases', '$totalCases'] },
                100
              ]
            },
            averageDuration: { $round: ['$averageDuration', 0] }
          }
        },
        { $sort: { successRate: -1 } }
      ]);

      return treatmentStats;
    } catch (error) {
      console.error('Error calculating treatment effectiveness:', error);
      throw error;
    }
  }

  // Therapist Performance Metrics
  static async getTherapistPerformance() {
    try {
      const therapistStats = await User.aggregate([
        {
          $match: { role: 'therapist' }
        },
        {
          $lookup: {
            from: 'treatmentplans',
            localField: '_id',
            foreignField: 'therapistId',
            as: 'treatments'
          }
        },
        {
          $lookup: {
            from: 'appointments',
            localField: '_id',
            foreignField: 'therapistId',
            as: 'appointments'
          }
        },
        {
          $project: {
            name: 1,
            email: 1,
            totalPatients: { $size: '$treatments' },
            completedTreatments: {
              $size: {
                $filter: {
                  input: '$treatments',
                  cond: { $eq: ['$$this.status', 'completed'] }
                }
              }
            },
            totalAppointments: { $size: '$appointments' },
            completedAppointments: {
              $size: {
                $filter: {
                  input: '$appointments',
                  cond: { $eq: ['$$this.status', 'completed'] }
                }
              }
            }
          }
        },
        {
          $project: {
            name: 1,
            email: 1,
            totalPatients: 1,
            successRate: {
              $cond: [
                { $gt: ['$totalPatients', 0] },
                {
                  $multiply: [
                    { $divide: ['$completedTreatments', '$totalPatients'] },
                    100
                  ]
                },
                0
              ]
            },
            appointmentCompletionRate: {
              $cond: [
                { $gt: ['$totalAppointments', 0] },
                {
                  $multiply: [
                    { $divide: ['$completedAppointments', '$totalAppointments'] },
                    100
                  ]
                },
                0
              ]
            }
          }
        },
        { $sort: { successRate: -1 } }
      ]);

      return therapistStats;
    } catch (error) {
      console.error('Error calculating therapist performance:', error);
      throw error;
    }
  }

  // Revenue Analytics
  static async getRevenueMetrics(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Monthly revenue breakdown
      const revenueData = await Billing.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['paid', 'completed'] }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            totalRevenue: { $sum: '$amount' },
            totalTransactions: { $sum: 1 },
            averageTransaction: { $avg: '$amount' }
          }
        },
        {
          $project: {
            month: {
              $dateToString: {
                format: '%Y-%m',
                date: {
                  $dateFromParts: {
                    year: '$_id.year',
                    month: '$_id.month'
                  }
                }
              }
            },
            totalRevenue: { $round: ['$totalRevenue', 2] },
            totalTransactions: 1,
            averageTransaction: { $round: ['$averageTransaction', 2] }
          }
        },
        { $sort: { month: 1 } }
      ]);

      // Payment method breakdown
      const paymentMethods = await Billing.aggregate([
        {
          $match: {
            createdAt: { $gte: startDate },
            status: { $in: ['paid', 'completed'] }
          }
        },
        {
          $group: {
            _id: '$paymentMethod',
            totalAmount: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        },
        {
          $project: {
            method: '$_id',
            totalAmount: { $round: ['$totalAmount', 2] },
            count: 1,
            averageAmount: { $round: [{ $divide: ['$totalAmount', '$count'] }, 2] }
          }
        }
      ]);

      // Outstanding payments
      const outstandingPayments = await Billing.aggregate([
        {
          $match: {
            status: { $in: ['pending', 'overdue'] }
          }
        },
        {
          $group: {
            _id: null,
            totalOutstanding: { $sum: '$amount' },
            count: { $sum: 1 }
          }
        }
      ]);

      return {
        revenueData,
        paymentMethods,
        outstandingPayments: outstandingPayments.length > 0 ? outstandingPayments[0] : { totalOutstanding: 0, count: 0 }
      };
    } catch (error) {
      console.error('Error calculating revenue metrics:', error);
      throw error;
    }
  }

  // Operational Metrics
  static async getOperationalMetrics(dateRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - dateRange);

      // Appointment utilization
      const appointmentStats = await Appointment.aggregate([
        {
          $match: {
            date: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: null,
            totalAppointments: { $sum: 1 },
            completedAppointments: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            cancelledAppointments: {
              $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
            },
            noShowAppointments: {
              $sum: { $cond: [{ $eq: ['$status', 'no-show'] }, 1, 0] }
            }
          }
        },
        {
          $project: {
            totalAppointments: 1,
            completedAppointments: 1,
            utilizationRate: {
              $multiply: [
                { $divide: ['$completedAppointments', '$totalAppointments'] },
                100
              ]
            },
            noShowRate: {
              $multiply: [
                { $divide: ['$noShowAppointments', '$totalAppointments'] },
                100
              ]
            },
            cancellationRate: {
              $multiply: [
                { $divide: ['$cancelledAppointments', '$totalAppointments'] },
                100
              ]
            }
          }
        }
      ]);

      // Patient retention rate
      const retentionStats = await Patient.aggregate([
        {
          $lookup: {
            from: 'appointments',
            localField: '_id',
            foreignField: 'patientId',
            as: 'appointments'
          }
        },
        {
          $project: {
            totalAppointments: { $size: '$appointments' },
            hasMultipleAppointments: { $gt: [{ $size: '$appointments' }, 1] }
          }
        },
        {
          $group: {
            _id: null,
            totalPatients: { $sum: 1 },
            retainedPatients: {
              $sum: { $cond: ['$hasMultipleAppointments', 1, 0] }
            }
          }
        },
        {
          $project: {
            retentionRate: {
              $multiply: [
                { $divide: ['$retainedPatients', '$totalPatients'] },
                100
              ]
            }
          }
        }
      ]);

      const appointmentMetrics = appointmentStats.length > 0 ? appointmentStats[0] : {
        utilizationRate: 0,
        noShowRate: 0,
        cancellationRate: 0
      };

      const retentionRate = retentionStats.length > 0 ? retentionStats[0].retentionRate : 0;

      return {
        appointmentUtilization: Math.round(appointmentMetrics.utilizationRate),
        noShowRate: Math.round(appointmentMetrics.noShowRate),
        cancellationRate: Math.round(appointmentMetrics.cancellationRate),
        patientRetentionRate: Math.round(retentionRate)
      };
    } catch (error) {
      console.error('Error calculating operational metrics:', error);
      throw error;
    }
  }

  // Comprehensive Dashboard Data
  static async getDashboardAnalytics(dateRange = 30) {
    try {
      const [
        patientOutcomes,
        treatmentEffectiveness,
        therapistPerformance,
        revenueMetrics,
        operationalMetrics
      ] = await Promise.all([
        this.getPatientOutcomes(dateRange),
        this.getTreatmentEffectiveness(),
        this.getTherapistPerformance(),
        this.getRevenueMetrics(dateRange),
        this.getOperationalMetrics(dateRange)
      ]);

      return {
        patientOutcomes,
        treatmentEffectiveness,
        therapistPerformance,
        revenueMetrics,
        operationalMetrics,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error generating dashboard analytics:', error);
      throw error;
    }
  }
}

module.exports = AnalyticsService;
