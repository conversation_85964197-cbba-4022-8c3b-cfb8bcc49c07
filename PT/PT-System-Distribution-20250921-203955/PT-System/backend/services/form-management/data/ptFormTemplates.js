// PT Form Templates based on the provided documents
// These templates define the structure for all 8 required PT forms

const ptFormTemplates = {
    // 1. PT Adult Initial Assessment Form
    ptAdultInitialAssessment: {
        name: "PT Adult Initial Assessment Form",
        name_ar: "نموذج التقييم الأولي للعلاج الطبيعي للبالغين",
        form_type: "initial_assessment",
        version: "1.0",
        schema: {
            sections: [
                {
                    id: "patient_demographics",
                    title: "Patient Demographics",
                    title_ar: "البيانات الديموغرافية للمريض",
                    fields: [
                        { id: "patient_name", type: "text", label: "Patient Name", label_ar: "اسم المريض", required: true },
                        { id: "medical_record_number", type: "text", label: "Medical Record Number", label_ar: "رقم السجل الطبي", required: true },
                        { id: "date_of_birth", type: "date", label: "Date of Birth", label_ar: "تاريخ الميلاد", required: true },
                        { id: "gender", type: "select", label: "Gender", label_ar: "الجنس", options: ["Male", "Female"], required: true },
                        { id: "admission_date", type: "date", label: "Admission Date", label_ar: "تاريخ الدخول" },
                        { id: "assessment_date", type: "date", label: "Assessment Date", label_ar: "تاريخ التقييم", required: true }
                    ]
                },
                {
                    id: "medical_history",
                    title: "Medical History",
                    title_ar: "التاريخ الطبي",
                    fields: [
                        { id: "chief_complaint", type: "textarea", label: "Chief Complaint", label_ar: "الشكوى الرئيسية", required: true },
                        { id: "history_present_illness", type: "textarea", label: "History of Present Illness", label_ar: "تاريخ المرض الحالي" },
                        { id: "past_medical_history", type: "textarea", label: "Past Medical History", label_ar: "التاريخ الطبي السابق" },
                        { id: "medications", type: "textarea", label: "Current Medications", label_ar: "الأدوية الحالية" },
                        { id: "allergies", type: "textarea", label: "Allergies", label_ar: "الحساسية" },
                        { id: "social_history", type: "textarea", label: "Social History", label_ar: "التاريخ الاجتماعي" }
                    ]
                },
                {
                    id: "vital_signs",
                    title: "Vital Signs",
                    title_ar: "العلامات الحيوية",
                    fields: [
                        { id: "blood_pressure", type: "text", label: "Blood Pressure (mmHg)", label_ar: "ضغط الدم" },
                        { id: "heart_rate", type: "number", label: "Heart Rate (bpm)", label_ar: "معدل ضربات القلب" },
                        { id: "respiratory_rate", type: "number", label: "Respiratory Rate", label_ar: "معدل التنفس" },
                        { id: "temperature", type: "number", label: "Temperature (°C)", label_ar: "درجة الحرارة" },
                        { id: "oxygen_saturation", type: "number", label: "Oxygen Saturation (%)", label_ar: "تشبع الأكسجين" }
                    ]
                },
                {
                    id: "physical_examination",
                    title: "Physical Examination",
                    title_ar: "الفحص الجسدي",
                    fields: [
                        { id: "general_appearance", type: "textarea", label: "General Appearance", label_ar: "المظهر العام" },
                        { id: "posture", type: "textarea", label: "Posture", label_ar: "الوضعية" },
                        { id: "gait", type: "textarea", label: "Gait", label_ar: "المشية" },
                        { id: "range_of_motion", type: "textarea", label: "Range of Motion", label_ar: "نطاق الحركة" },
                        { id: "muscle_strength", type: "textarea", label: "Muscle Strength", label_ar: "قوة العضلات" },
                        { id: "balance", type: "textarea", label: "Balance", label_ar: "التوازن" },
                        { id: "coordination", type: "textarea", label: "Coordination", label_ar: "التنسيق" }
                    ]
                },
                {
                    id: "functional_assessment",
                    title: "Functional Assessment",
                    title_ar: "التقييم الوظيفي",
                    fields: [
                        { id: "activities_daily_living", type: "textarea", label: "Activities of Daily Living", label_ar: "أنشطة الحياة اليومية" },
                        { id: "mobility", type: "textarea", label: "Mobility", label_ar: "الحركة" },
                        { id: "transfers", type: "textarea", label: "Transfers", label_ar: "النقل" },
                        { id: "assistive_devices", type: "textarea", label: "Assistive Devices", label_ar: "الأجهزة المساعدة" }
                    ]
                },
                {
                    id: "assessment_plan",
                    title: "Assessment and Plan",
                    title_ar: "التقييم والخطة",
                    fields: [
                        { id: "clinical_impression", type: "textarea", label: "Clinical Impression", label_ar: "الانطباع السريري", required: true },
                        { id: "problems_list", type: "textarea", label: "Problems List", label_ar: "قائمة المشاكل" },
                        { id: "goals", type: "textarea", label: "Goals", label_ar: "الأهداف" },
                        { id: "treatment_plan", type: "textarea", label: "Treatment Plan", label_ar: "خطة العلاج" },
                        { id: "frequency", type: "text", label: "Frequency", label_ar: "التكرار" },
                        { id: "duration", type: "text", label: "Duration", label_ar: "المدة" }
                    ]
                }
            ]
        }
    },

    // 2. Initial Plan of Care
    initialPlanOfCare: {
        name: "Initial Plan of Care for Physical Therapist",
        name_ar: "الخطة الأولية للرعاية للعلاج الطبيعي",
        form_type: "initial_plan_care",
        version: "1.0",
        schema: {
            sections: [
                {
                    id: "patient_info",
                    title: "Patient Information",
                    title_ar: "معلومات المريض",
                    fields: [
                        { id: "patient_name", type: "text", label: "Patient Name", label_ar: "اسم المريض", required: true },
                        { id: "medical_record_number", type: "text", label: "MRN", label_ar: "رقم السجل الطبي", required: true },
                        { id: "diagnosis", type: "textarea", label: "Primary Diagnosis", label_ar: "التشخيص الأساسي", required: true },
                        { id: "secondary_diagnosis", type: "textarea", label: "Secondary Diagnosis", label_ar: "التشخيص الثانوي" },
                        { id: "onset_date", type: "date", label: "Onset Date", label_ar: "تاريخ البداية" }
                    ]
                },
                {
                    id: "short_term_goals",
                    title: "Short Term Goals (2-4 weeks)",
                    title_ar: "الأهداف قصيرة المدى (2-4 أسابيع)",
                    fields: [
                        { id: "goal_1", type: "textarea", label: "Goal 1", label_ar: "الهدف 1" },
                        { id: "goal_2", type: "textarea", label: "Goal 2", label_ar: "الهدف 2" },
                        { id: "goal_3", type: "textarea", label: "Goal 3", label_ar: "الهدف 3" },
                        { id: "goal_4", type: "textarea", label: "Goal 4", label_ar: "الهدف 4" }
                    ]
                },
                {
                    id: "long_term_goals",
                    title: "Long Term Goals (6-8 weeks)",
                    title_ar: "الأهداف طويلة المدى (6-8 أسابيع)",
                    fields: [
                        { id: "goal_1", type: "textarea", label: "Goal 1", label_ar: "الهدف 1" },
                        { id: "goal_2", type: "textarea", label: "Goal 2", label_ar: "الهدف 2" },
                        { id: "goal_3", type: "textarea", label: "Goal 3", label_ar: "الهدف 3" },
                        { id: "goal_4", type: "textarea", label: "Goal 4", label_ar: "الهدف 4" }
                    ]
                },
                {
                    id: "treatment_plan",
                    title: "Treatment Plan",
                    title_ar: "خطة العلاج",
                    fields: [
                        { id: "treatment_frequency", type: "text", label: "Frequency", label_ar: "التكرار", required: true },
                        { id: "treatment_duration", type: "text", label: "Duration", label_ar: "المدة", required: true },
                        { id: "treatment_interventions", type: "textarea", label: "Interventions", label_ar: "التدخلات", required: true },
                        { id: "precautions", type: "textarea", label: "Precautions", label_ar: "الاحتياطات" },
                        { id: "contraindications", type: "textarea", label: "Contraindications", label_ar: "موانع الاستعمال" }
                    ]
                },
                {
                    id: "signatures",
                    title: "Signatures",
                    title_ar: "التوقيعات",
                    fields: [
                        { id: "therapist_name", type: "text", label: "Physical Therapist Name", label_ar: "اسم أخصائي العلاج الطبيعي", required: true },
                        { id: "therapist_signature", type: "signature", label: "Therapist Signature", label_ar: "توقيع المعالج", required: true },
                        { id: "therapist_date", type: "date", label: "Date", label_ar: "التاريخ", required: true },
                        { id: "physician_name", type: "text", label: "Physician Name", label_ar: "اسم الطبيب" },
                        { id: "physician_signature", type: "signature", label: "Physician Signature", label_ar: "توقيع الطبيب" },
                        { id: "physician_date", type: "date", label: "Date", label_ar: "التاريخ" }
                    ]
                }
            ]
        }
    },

    // 3. PT Daily Note
    ptDailyNote: {
        name: "PT Daily Note for Physical Therapist",
        name_ar: "الملاحظة اليومية للعلاج الطبيعي",
        form_type: "daily_note",
        version: "1.0",
        schema: {
            sections: [
                {
                    id: "session_info",
                    title: "Session Information",
                    title_ar: "معلومات الجلسة",
                    fields: [
                        { id: "patient_name", type: "text", label: "Patient Name", label_ar: "اسم المريض", required: true },
                        { id: "medical_record_number", type: "text", label: "MRN", label_ar: "رقم السجل الطبي", required: true },
                        { id: "session_date", type: "date", label: "Session Date", label_ar: "تاريخ الجلسة", required: true },
                        { id: "session_time", type: "time", label: "Session Time", label_ar: "وقت الجلسة" },
                        { id: "session_duration", type: "number", label: "Duration (minutes)", label_ar: "المدة (دقائق)" }
                    ]
                },
                {
                    id: "treatment_provided",
                    title: "Treatment Provided",
                    title_ar: "العلاج المقدم",
                    fields: [
                        { id: "interventions", type: "textarea", label: "Interventions", label_ar: "التدخلات", required: true },
                        { id: "exercises", type: "textarea", label: "Exercises", label_ar: "التمارين" },
                        { id: "modalities", type: "textarea", label: "Modalities", label_ar: "الطرق العلاجية" },
                        { id: "education", type: "textarea", label: "Patient Education", label_ar: "تثقيف المريض" }
                    ]
                },
                {
                    id: "patient_response",
                    title: "Patient Response",
                    title_ar: "استجابة المريض",
                    fields: [
                        { id: "tolerance", type: "select", label: "Tolerance", label_ar: "التحمل", options: ["Good", "Fair", "Poor"], required: true },
                        { id: "pain_level", type: "number", label: "Pain Level (0-10)", label_ar: "مستوى الألم (0-10)", min: 0, max: 10 },
                        { id: "functional_improvement", type: "textarea", label: "Functional Improvement", label_ar: "التحسن الوظيفي" },
                        { id: "patient_feedback", type: "textarea", label: "Patient Feedback", label_ar: "ملاحظات المريض" }
                    ]
                },
                {
                    id: "progress_notes",
                    title: "Progress Notes",
                    title_ar: "ملاحظات التقدم",
                    fields: [
                        { id: "objective_findings", type: "textarea", label: "Objective Findings", label_ar: "النتائج الموضوعية" },
                        { id: "progress_toward_goals", type: "textarea", label: "Progress Toward Goals", label_ar: "التقدم نحو الأهداف" },
                        { id: "plan_next_session", type: "textarea", label: "Plan for Next Session", label_ar: "خطة الجلسة القادمة" }
                    ]
                },
                {
                    id: "therapist_info",
                    title: "Therapist Information",
                    title_ar: "معلومات المعالج",
                    fields: [
                        { id: "therapist_name", type: "text", label: "Therapist Name", label_ar: "اسم المعالج", required: true },
                        { id: "therapist_signature", type: "signature", label: "Signature", label_ar: "التوقيع", required: true },
                        { id: "date_signed", type: "date", label: "Date", label_ar: "التاريخ", required: true }
                    ]
                }
            ]
        }
    }
};

    // 4. Patient and Family Education Form
    patientFamilyEducation: {
        name: "Patient and Family Education Form for Physical Therapist",
        name_ar: "نموذج تثقيف المريض والأسرة للعلاج الطبيعي",
        form_type: "patient_education",
        version: "1.0",
        schema: {
            sections: [
                {
                    id: "patient_info",
                    title: "Patient Information",
                    title_ar: "معلومات المريض",
                    fields: [
                        { id: "patient_name", type: "text", label: "Patient Name", label_ar: "اسم المريض", required: true },
                        { id: "medical_record_number", type: "text", label: "MRN", label_ar: "رقم السجل الطبي", required: true },
                        { id: "education_date", type: "date", label: "Education Date", label_ar: "تاريخ التثقيف", required: true }
                    ]
                },
                {
                    id: "learning_assessment",
                    title: "Learning Assessment",
                    title_ar: "تقييم التعلم",
                    fields: [
                        { id: "learning_style", type: "select", label: "Preferred Learning Style", label_ar: "أسلوب التعلم المفضل", options: ["Visual", "Auditory", "Kinesthetic", "Reading/Writing"], required: true },
                        { id: "language_preference", type: "select", label: "Language Preference", label_ar: "تفضيل اللغة", options: ["Arabic", "English", "Both"], required: true },
                        { id: "education_level", type: "select", label: "Education Level", label_ar: "المستوى التعليمي", options: ["Elementary", "High School", "University", "Graduate"] },
                        { id: "learning_barriers", type: "checkbox", label: "Learning Barriers", label_ar: "عوائق التعلم", options: ["Vision impairment", "Hearing impairment", "Cognitive impairment", "Language barrier", "Pain/discomfort", "Fatigue", "None"] }
                    ]
                },
                {
                    id: "educational_needs",
                    title: "Educational Needs",
                    title_ar: "الاحتياجات التعليمية",
                    fields: [
                        { id: "diagnosis_understanding", type: "select", label: "Understanding of Diagnosis", label_ar: "فهم التشخيص", options: ["Excellent", "Good", "Fair", "Poor"], required: true },
                        { id: "treatment_understanding", type: "select", label: "Understanding of Treatment", label_ar: "فهم العلاج", options: ["Excellent", "Good", "Fair", "Poor"], required: true },
                        { id: "topics_to_cover", type: "checkbox", label: "Topics to Cover", label_ar: "المواضيع المطلوب تغطيتها", options: ["Condition/Diagnosis", "Treatment plan", "Exercise program", "Pain management", "Activity modifications", "Precautions", "Home care", "Follow-up care"], required: true }
                    ]
                },
                {
                    id: "education_provided",
                    title: "Education Provided",
                    title_ar: "التثقيف المقدم",
                    fields: [
                        { id: "teaching_methods", type: "checkbox", label: "Teaching Methods Used", label_ar: "طرق التدريس المستخدمة", options: ["Verbal instruction", "Written materials", "Demonstration", "Return demonstration", "Video/multimedia", "Models/diagrams"] },
                        { id: "materials_given", type: "checkbox", label: "Materials Given", label_ar: "المواد المقدمة", options: ["Written instructions", "Exercise handouts", "Educational brochures", "Video resources", "Contact information"] },
                        { id: "content_covered", type: "textarea", label: "Content Covered", label_ar: "المحتوى المغطى", required: true }
                    ]
                },
                {
                    id: "evaluation",
                    title: "Evaluation of Learning",
                    title_ar: "تقييم التعلم",
                    fields: [
                        { id: "patient_verbalization", type: "textarea", label: "Patient Verbalization of Understanding", label_ar: "تعبير المريض عن الفهم" },
                        { id: "demonstration_ability", type: "select", label: "Ability to Demonstrate", label_ar: "القدرة على التطبيق", options: ["Independent", "With minimal assistance", "With moderate assistance", "Unable to demonstrate"] },
                        { id: "questions_concerns", type: "textarea", label: "Questions/Concerns Raised", label_ar: "الأسئلة/المخاوف المثارة" },
                        { id: "follow_up_needed", type: "select", label: "Follow-up Education Needed", label_ar: "الحاجة لتثقيف متابعة", options: ["Yes", "No"], required: true },
                        { id: "follow_up_plan", type: "textarea", label: "Follow-up Plan", label_ar: "خطة المتابعة" }
                    ]
                }
            ]
        }
    },

    // 5. Reassessment and Update Plan of Care
    reassessmentUpdatePlan: {
        name: "Reassessment and Update Plan of Care for Doctor",
        name_ar: "إعادة التقييم وتحديث خطة الرعاية للطبيب",
        form_type: "reassessment_update",
        version: "1.0",
        schema: {
            sections: [
                {
                    id: "patient_info",
                    title: "Patient Information",
                    title_ar: "معلومات المريض",
                    fields: [
                        { id: "patient_name", type: "text", label: "Patient Name", label_ar: "اسم المريض", required: true },
                        { id: "medical_record_number", type: "text", label: "MRN", label_ar: "رقم السجل الطبي", required: true },
                        { id: "reassessment_date", type: "date", label: "Reassessment Date", label_ar: "تاريخ إعادة التقييم", required: true },
                        { id: "original_start_date", type: "date", label: "Original Start Date", label_ar: "تاريخ البداية الأصلي" }
                    ]
                },
                {
                    id: "progress_review",
                    title: "Progress Review",
                    title_ar: "مراجعة التقدم",
                    fields: [
                        { id: "sessions_completed", type: "number", label: "Sessions Completed", label_ar: "الجلسات المكتملة", required: true },
                        { id: "attendance_rate", type: "number", label: "Attendance Rate (%)", label_ar: "معدل الحضور (%)", min: 0, max: 100 },
                        { id: "current_functional_level", type: "textarea", label: "Current Functional Level", label_ar: "المستوى الوظيفي الحالي", required: true },
                        { id: "goals_achieved", type: "textarea", label: "Goals Achieved", label_ar: "الأهداف المحققة" },
                        { id: "goals_not_achieved", type: "textarea", label: "Goals Not Achieved", label_ar: "الأهداف غير المحققة" }
                    ]
                },
                {
                    id: "current_status",
                    title: "Current Status",
                    title_ar: "الحالة الحالية",
                    fields: [
                        { id: "pain_level", type: "number", label: "Current Pain Level (0-10)", label_ar: "مستوى الألم الحالي (0-10)", min: 0, max: 10 },
                        { id: "range_of_motion", type: "textarea", label: "Range of Motion", label_ar: "نطاق الحركة" },
                        { id: "strength", type: "textarea", label: "Strength", label_ar: "القوة" },
                        { id: "functional_mobility", type: "textarea", label: "Functional Mobility", label_ar: "الحركة الوظيفية" },
                        { id: "balance_coordination", type: "textarea", label: "Balance and Coordination", label_ar: "التوازن والتنسيق" }
                    ]
                },
                {
                    id: "updated_plan",
                    title: "Updated Treatment Plan",
                    title_ar: "خطة العلاج المحدثة",
                    fields: [
                        { id: "continue_current_plan", type: "select", label: "Continue Current Plan", label_ar: "متابعة الخطة الحالية", options: ["Yes", "No"], required: true },
                        { id: "plan_modifications", type: "textarea", label: "Plan Modifications", label_ar: "تعديلات الخطة" },
                        { id: "new_goals", type: "textarea", label: "New Goals", label_ar: "الأهداف الجديدة" },
                        { id: "frequency_change", type: "text", label: "Frequency Change", label_ar: "تغيير التكرار" },
                        { id: "duration_change", type: "text", label: "Duration Change", label_ar: "تغيير المدة" },
                        { id: "discharge_planning", type: "select", label: "Discharge Planning", label_ar: "تخطيط الخروج", options: ["Continue therapy", "Discharge next visit", "Discharge in 2-3 visits", "Discharge in 1 week", "Discharge in 2 weeks"] }
                    ]
                }
            ]
        }
    },

    // 6. Outpatient Discharge Assessment
    outpatientDischarge: {
        name: "Outpatient Discharge Assessment for Doctor",
        name_ar: "تقييم خروج المرضى الخارجيين للطبيب",
        form_type: "outpatient_discharge",
        version: "1.0",
        schema: {
            sections: [
                {
                    id: "patient_info",
                    title: "Patient Information",
                    title_ar: "معلومات المريض",
                    fields: [
                        { id: "patient_name", type: "text", label: "Patient Name", label_ar: "اسم المريض", required: true },
                        { id: "medical_record_number", type: "text", label: "MRN", label_ar: "رقم السجل الطبي", required: true },
                        { id: "discharge_date", type: "date", label: "Discharge Date", label_ar: "تاريخ الخروج", required: true },
                        { id: "total_sessions", type: "number", label: "Total Sessions", label_ar: "إجمالي الجلسات", required: true }
                    ]
                },
                {
                    id: "treatment_summary",
                    title: "Treatment Summary",
                    title_ar: "ملخص العلاج",
                    fields: [
                        { id: "initial_diagnosis", type: "textarea", label: "Initial Diagnosis", label_ar: "التشخيص الأولي", required: true },
                        { id: "treatment_provided", type: "textarea", label: "Treatment Provided", label_ar: "العلاج المقدم", required: true },
                        { id: "initial_functional_level", type: "textarea", label: "Initial Functional Level", label_ar: "المستوى الوظيفي الأولي" },
                        { id: "discharge_functional_level", type: "textarea", label: "Discharge Functional Level", label_ar: "المستوى الوظيفي عند الخروج", required: true }
                    ]
                },
                {
                    id: "outcomes",
                    title: "Treatment Outcomes",
                    title_ar: "نتائج العلاج",
                    fields: [
                        { id: "goals_met", type: "select", label: "Goals Met", label_ar: "الأهداف المحققة", options: ["All goals met", "Most goals met", "Some goals met", "Few goals met", "No goals met"], required: true },
                        { id: "pain_improvement", type: "select", label: "Pain Improvement", label_ar: "تحسن الألم", options: ["Significant improvement", "Moderate improvement", "Minimal improvement", "No improvement", "Worsened"] },
                        { id: "functional_improvement", type: "select", label: "Functional Improvement", label_ar: "التحسن الوظيفي", options: ["Significant improvement", "Moderate improvement", "Minimal improvement", "No improvement", "Declined"] },
                        { id: "patient_satisfaction", type: "select", label: "Patient Satisfaction", label_ar: "رضا المريض", options: ["Very satisfied", "Satisfied", "Neutral", "Dissatisfied", "Very dissatisfied"] }
                    ]
                },
                {
                    id: "discharge_status",
                    title: "Discharge Status",
                    title_ar: "حالة الخروج",
                    fields: [
                        { id: "discharge_reason", type: "select", label: "Reason for Discharge", label_ar: "سبب الخروج", options: ["Goals achieved", "Maximum benefit reached", "Patient request", "Non-compliance", "Medical complications", "Insurance limitations"], required: true },
                        { id: "home_exercise_program", type: "select", label: "Home Exercise Program Given", label_ar: "برنامج التمارين المنزلية", options: ["Yes", "No"], required: true },
                        { id: "equipment_needs", type: "textarea", label: "Equipment Needs", label_ar: "احتياجات المعدات" },
                        { id: "activity_restrictions", type: "textarea", label: "Activity Restrictions", label_ar: "قيود النشاط" }
                    ]
                },
                {
                    id: "recommendations",
                    title: "Recommendations",
                    title_ar: "التوصيات",
                    fields: [
                        { id: "follow_up_needed", type: "select", label: "Follow-up Needed", label_ar: "الحاجة للمتابعة", options: ["Yes", "No"], required: true },
                        { id: "follow_up_timeframe", type: "text", label: "Follow-up Timeframe", label_ar: "الإطار الزمني للمتابعة" },
                        { id: "additional_services", type: "checkbox", label: "Additional Services Recommended", label_ar: "الخدمات الإضافية الموصى بها", options: ["Occupational therapy", "Speech therapy", "Psychology", "Social work", "Nutrition", "Pain management", "Orthopedic follow-up"] },
                        { id: "precautions", type: "textarea", label: "Precautions", label_ar: "الاحتياطات" }
                    ]
                }
            ]
        }
    }
};

module.exports = ptFormTemplates;
