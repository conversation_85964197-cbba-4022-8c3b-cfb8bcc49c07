{"name": "form-management-service", "version": "1.0.0", "description": "Form Management Microservice for PT System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.0", "joi": "^17.9.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.1.4", "uuid": "^9.0.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3"}, "keywords": ["healthcare", "form-management", "microservice"], "author": "PT System Team", "license": "MIT"}