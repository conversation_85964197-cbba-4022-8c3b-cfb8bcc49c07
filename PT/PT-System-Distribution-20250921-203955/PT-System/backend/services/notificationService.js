const nodemailer = require('nodemailer');
const twilio = require('twilio');
const webpush = require('web-push');
const Notification = require('../models/Notification');
const User = require('../models/User');
const Patient = require('../models/Patient');

class NotificationService {
  constructor() {
    // Email transporter setup
    this.emailTransporter = nodemailer.createTransport({
      service: process.env.EMAIL_SERVICE || 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    // SMS client setup (only if valid credentials are provided)
    if (process.env.TWILIO_ACCOUNT_SID &&
        process.env.TWILIO_AUTH_TOKEN &&
        process.env.TWILIO_ACCOUNT_SID.startsWith('AC') &&
        !process.env.TWILIO_ACCOUNT_SID.includes('your-twilio')) {
      this.smsClient = twilio(
        process.env.TWILIO_ACCOUNT_SID,
        process.env.TWILIO_AUTH_TOKEN
      );
    } else {
      this.smsClient = null;
      console.log('Twilio credentials not configured - SMS functionality disabled');
    }

    // Push notification setup (only if credentials are provided)
    if (process.env.VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY) {
      webpush.setVapidDetails(
        'mailto:' + (process.env.EMAIL_USER || '<EMAIL>'),
        process.env.VAPID_PUBLIC_KEY,
        process.env.VAPID_PRIVATE_KEY
      );
    } else {
      console.log('VAPID keys not provided - Push notifications disabled');
    }
  }

  // Create notification record
  async createNotification(data) {
    try {
      const notification = new Notification({
        userId: data.userId,
        type: data.type,
        title: data.title,
        titleAr: data.titleAr,
        message: data.message,
        messageAr: data.messageAr,
        priority: data.priority || 'medium',
        channels: data.channels || ['inApp'],
        metadata: data.metadata || {},
        scheduledFor: data.scheduledFor || new Date()
      });

      await notification.save();
      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Send email notification
  async sendEmail(to, subject, html, attachments = []) {
    try {
      const mailOptions = {
        from: process.env.EMAIL_FROM || process.env.EMAIL_USER,
        to,
        subject,
        html,
        attachments
      };

      const result = await this.emailTransporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return result;
    } catch (error) {
      console.error('Error sending email:', error);
      throw error;
    }
  }

  // Send SMS notification
  async sendSMS(to, message) {
    if (!this.smsClient) {
      console.log('SMS service not configured - skipping SMS notification');
      return { status: 'skipped', reason: 'SMS service not configured' };
    }

    try {
      const result = await this.smsClient.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to
      });

      console.log('SMS sent successfully:', result.sid);
      return result;
    } catch (error) {
      console.error('Error sending SMS:', error);
      throw error;
    }
  }

  // Send push notification
  async sendPushNotification(subscription, payload) {
    try {
      const result = await webpush.sendNotification(subscription, JSON.stringify(payload));
      console.log('Push notification sent successfully');
      return result;
    } catch (error) {
      console.error('Error sending push notification:', error);
      throw error;
    }
  }

  // Send appointment reminder
  async sendAppointmentReminder(appointmentId, reminderType = '24h') {
    try {
      // Fetch appointment details with patient and therapist info
      const appointment = await Appointment.findById(appointmentId)
        .populate('patientId', 'name nameAr email phone language preferences')
        .populate('therapistId', 'name email');

      if (!appointment) {
        throw new Error('Appointment not found');
      }

      const patient = appointment.patientId;
      const therapist = appointment.therapistId;
      const isArabic = patient.language === 'ar';

      // Prepare notification content
      const title = isArabic ? 'تذكير بالموعد' : 'Appointment Reminder';
      const message = isArabic 
        ? `لديك موعد مع ${therapist.name} في ${appointment.date.toLocaleDateString('ar')} في ${appointment.time}`
        : `You have an appointment with ${therapist.name} on ${appointment.date.toLocaleDateString()} at ${appointment.time}`;

      // Create notification record
      const notification = await this.createNotification({
        userId: patient._id,
        type: 'appointment',
        title,
        titleAr: 'تذكير بالموعد',
        message,
        messageAr: `لديك موعد مع ${therapist.name} في ${appointment.date.toLocaleDateString('ar')} في ${appointment.time}`,
        priority: 'high',
        channels: patient.preferences?.notificationChannels || ['email', 'sms'],
        metadata: {
          appointmentId: appointment._id,
          reminderType
        }
      });

      // Send via enabled channels
      const promises = [];

      if (patient.preferences?.notifications?.email && patient.email) {
        const emailHtml = this.generateAppointmentEmailTemplate(appointment, patient, therapist, isArabic);
        promises.push(this.sendEmail(patient.email, title, emailHtml));
      }

      if (patient.preferences?.notifications?.sms && patient.phone) {
        promises.push(this.sendSMS(patient.phone, message));
      }

      if (patient.preferences?.notifications?.push && patient.pushSubscription) {
        promises.push(this.sendPushNotification(patient.pushSubscription, {
          title,
          body: message,
          icon: '/icons/appointment.png',
          badge: '/icons/badge.png',
          data: { appointmentId: appointment._id }
        }));
      }

      await Promise.allSettled(promises);
      
      // Update notification status
      notification.status = 'sent';
      notification.sentAt = new Date();
      await notification.save();

      return notification;
    } catch (error) {
      console.error('Error sending appointment reminder:', error);
      throw error;
    }
  }

  // Send treatment update notification
  async sendTreatmentUpdate(treatmentPlanId, updateType, updateData) {
    try {
      const treatmentPlan = await TreatmentPlan.findById(treatmentPlanId)
        .populate('patientId', 'name nameAr email phone language preferences')
        .populate('therapistId', 'name email');

      if (!treatmentPlan) {
        throw new Error('Treatment plan not found');
      }

      const patient = treatmentPlan.patientId;
      const therapist = treatmentPlan.therapistId;
      const isArabic = patient.language === 'ar';

      let title, message;
      
      switch (updateType) {
        case 'plan_updated':
          title = isArabic ? 'تم تحديث خطة العلاج' : 'Treatment Plan Updated';
          message = isArabic 
            ? `تم تحديث خطة العلاج الخاصة بك من قبل ${therapist.name}`
            : `Your treatment plan has been updated by ${therapist.name}`;
          break;
        case 'progress_note':
          title = isArabic ? 'ملاحظة تقدم جديدة' : 'New Progress Note';
          message = isArabic 
            ? `تم إضافة ملاحظة تقدم جديدة لخطة العلاج الخاصة بك`
            : `A new progress note has been added to your treatment plan`;
          break;
        case 'goal_achieved':
          title = isArabic ? 'تم تحقيق هدف العلاج' : 'Treatment Goal Achieved';
          message = isArabic 
            ? `تهانينا! تم تحقيق أحد أهداف العلاج`
            : `Congratulations! You have achieved a treatment goal`;
          break;
        default:
          title = isArabic ? 'تحديث خطة العلاج' : 'Treatment Update';
          message = isArabic 
            ? `هناك تحديث جديد في خطة العلاج الخاصة بك`
            : `There is a new update to your treatment plan`;
      }

      const notification = await this.createNotification({
        userId: patient._id,
        type: 'treatment',
        title,
        titleAr: title,
        message,
        messageAr: message,
        priority: 'medium',
        channels: patient.preferences?.notificationChannels || ['email', 'inApp'],
        metadata: {
          treatmentPlanId: treatmentPlan._id,
          updateType,
          updateData
        }
      });

      // Send notifications
      const promises = [];

      if (patient.preferences?.notifications?.email && patient.email) {
        const emailHtml = this.generateTreatmentUpdateEmailTemplate(treatmentPlan, patient, therapist, updateType, updateData, isArabic);
        promises.push(this.sendEmail(patient.email, title, emailHtml));
      }

      if (patient.preferences?.notifications?.push && patient.pushSubscription) {
        promises.push(this.sendPushNotification(patient.pushSubscription, {
          title,
          body: message,
          icon: '/icons/treatment.png',
          data: { treatmentPlanId: treatmentPlan._id, updateType }
        }));
      }

      await Promise.allSettled(promises);

      notification.status = 'sent';
      notification.sentAt = new Date();
      await notification.save();

      return notification;
    } catch (error) {
      console.error('Error sending treatment update:', error);
      throw error;
    }
  }

  // Send billing notification
  async sendBillingNotification(billingId, notificationType) {
    try {
      const billing = await Billing.findById(billingId)
        .populate('patientId', 'name nameAr email phone language preferences');

      if (!billing) {
        throw new Error('Billing record not found');
      }

      const patient = billing.patientId;
      const isArabic = patient.language === 'ar';

      let title, message;
      
      switch (notificationType) {
        case 'invoice_generated':
          title = isArabic ? 'فاتورة جديدة' : 'New Invoice';
          message = isArabic 
            ? `تم إنشاء فاتورة جديدة بقيمة ${billing.amount} دولار`
            : `A new invoice for $${billing.amount} has been generated`;
          break;
        case 'payment_received':
          title = isArabic ? 'تم استلام الدفعة' : 'Payment Received';
          message = isArabic 
            ? `تم استلام دفعة بقيمة ${billing.amount} دولار`
            : `Payment of $${billing.amount} has been received`;
          break;
        case 'payment_overdue':
          title = isArabic ? 'دفعة متأخرة' : 'Payment Overdue';
          message = isArabic 
            ? `الدفعة بقيمة ${billing.amount} دولار متأخرة`
            : `Payment of $${billing.amount} is overdue`;
          break;
        default:
          title = isArabic ? 'تحديث الفواتير' : 'Billing Update';
          message = isArabic 
            ? `هناك تحديث في فاتورتك`
            : `There is an update to your billing`;
      }

      const notification = await this.createNotification({
        userId: patient._id,
        type: 'billing',
        title,
        titleAr: title,
        message,
        messageAr: message,
        priority: notificationType === 'payment_overdue' ? 'high' : 'medium',
        channels: patient.preferences?.notificationChannels || ['email'],
        metadata: {
          billingId: billing._id,
          notificationType,
          amount: billing.amount
        }
      });

      // Send notifications
      const promises = [];

      if (patient.preferences?.notifications?.email && patient.email) {
        const emailHtml = this.generateBillingEmailTemplate(billing, patient, notificationType, isArabic);
        promises.push(this.sendEmail(patient.email, title, emailHtml));
      }

      if (notificationType === 'payment_overdue' && patient.preferences?.notifications?.sms && patient.phone) {
        promises.push(this.sendSMS(patient.phone, message));
      }

      await Promise.allSettled(promises);

      notification.status = 'sent';
      notification.sentAt = new Date();
      await notification.save();

      return notification;
    } catch (error) {
      console.error('Error sending billing notification:', error);
      throw error;
    }
  }

  // Generate email templates
  generateAppointmentEmailTemplate(appointment, patient, therapist, isArabic) {
    const title = isArabic ? 'تذكير بالموعد' : 'Appointment Reminder';
    const greeting = isArabic ? `عزيزي/عزيزتي ${patient.nameAr || patient.name}` : `Dear ${patient.name}`;
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">${title}</h2>
        <p>${greeting},</p>
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>${isArabic ? 'تفاصيل الموعد' : 'Appointment Details'}</h3>
          <p><strong>${isArabic ? 'المعالج' : 'Therapist'}:</strong> ${therapist.name}</p>
          <p><strong>${isArabic ? 'التاريخ' : 'Date'}:</strong> ${appointment.date.toLocaleDateString(isArabic ? 'ar' : 'en')}</p>
          <p><strong>${isArabic ? 'الوقت' : 'Time'}:</strong> ${appointment.time}</p>
          <p><strong>${isArabic ? 'النوع' : 'Type'}:</strong> ${appointment.type}</p>
        </div>
        <p>${isArabic ? 'نتطلع لرؤيتك قريباً!' : 'We look forward to seeing you!'}</p>
        <p style="color: #6b7280; font-size: 12px;">
          ${isArabic ? 'هذه رسالة تلقائية، يرجى عدم الرد عليها.' : 'This is an automated message, please do not reply.'}
        </p>
      </div>
    `;
  }

  generateTreatmentUpdateEmailTemplate(treatmentPlan, patient, therapist, updateType, updateData, isArabic) {
    const title = isArabic ? 'تحديث خطة العلاج' : 'Treatment Plan Update';
    const greeting = isArabic ? `عزيزي/عزيزتي ${patient.nameAr || patient.name}` : `Dear ${patient.name}`;
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #059669;">${title}</h2>
        <p>${greeting},</p>
        <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>${isArabic ? 'تفاصيل التحديث' : 'Update Details'}</h3>
          <p><strong>${isArabic ? 'المعالج' : 'Therapist'}:</strong> ${therapist.name}</p>
          <p><strong>${isArabic ? 'نوع التحديث' : 'Update Type'}:</strong> ${updateType}</p>
          ${updateData ? `<p><strong>${isArabic ? 'التفاصيل' : 'Details'}:</strong> ${updateData}</p>` : ''}
        </div>
        <p>${isArabic ? 'يمكنك مراجعة التحديثات الكاملة في حسابك.' : 'You can view the complete updates in your account.'}</p>
        <p style="color: #6b7280; font-size: 12px;">
          ${isArabic ? 'هذه رسالة تلقائية، يرجى عدم الرد عليها.' : 'This is an automated message, please do not reply.'}
        </p>
      </div>
    `;
  }

  generateBillingEmailTemplate(billing, patient, notificationType, isArabic) {
    const title = isArabic ? 'تحديث الفواتير' : 'Billing Update';
    const greeting = isArabic ? `عزيزي/عزيزتي ${patient.nameAr || patient.name}` : `Dear ${patient.name}`;
    
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #7c3aed;">${title}</h2>
        <p>${greeting},</p>
        <div style="background-color: #faf5ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3>${isArabic ? 'تفاصيل الفاتورة' : 'Billing Details'}</h3>
          <p><strong>${isArabic ? 'رقم الفاتورة' : 'Invoice Number'}:</strong> ${billing.invoiceNumber}</p>
          <p><strong>${isArabic ? 'المبلغ' : 'Amount'}:</strong> $${billing.amount}</p>
          <p><strong>${isArabic ? 'الحالة' : 'Status'}:</strong> ${billing.status}</p>
          <p><strong>${isArabic ? 'تاريخ الاستحقاق' : 'Due Date'}:</strong> ${billing.dueDate?.toLocaleDateString(isArabic ? 'ar' : 'en')}</p>
        </div>
        <p>${isArabic ? 'يمكنك مراجعة تفاصيل الفاتورة الكاملة في حسابك.' : 'You can view the complete invoice details in your account.'}</p>
        <p style="color: #6b7280; font-size: 12px;">
          ${isArabic ? 'هذه رسالة تلقائية، يرجى عدم الرد عليها.' : 'This is an automated message, please do not reply.'}
        </p>
      </div>
    `;
  }

  // Get user notifications
  async getUserNotifications(userId, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        type = null,
        read = null,
        priority = null
      } = options;

      const query = { userId };
      
      if (type) query.type = type;
      if (read !== null) query.read = read;
      if (priority) query.priority = priority;

      const notifications = await Notification.find(query)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .exec();

      const total = await Notification.countDocuments(query);

      return {
        notifications,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        total
      };
    } catch (error) {
      console.error('Error fetching user notifications:', error);
      throw error;
    }
  }

  // Mark notification as read
  async markAsRead(notificationId, userId) {
    try {
      const notification = await Notification.findOneAndUpdate(
        { _id: notificationId, userId },
        { read: true, readAt: new Date() },
        { new: true }
      );

      return notification;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  // Delete notification
  async deleteNotification(notificationId, userId) {
    try {
      const result = await Notification.deleteOne({ _id: notificationId, userId });
      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }
}

module.exports = new NotificationService();
