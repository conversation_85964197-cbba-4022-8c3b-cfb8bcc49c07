const express = require('express');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const { pool } = require('../server');

const router = express.Router();

// Validation schemas
const patientSchema = Joi.object({
    national_id: Joi.string().pattern(/^\d{10}$/).required(),
    first_name: Joi.string().min(2).max(100).required(),
    last_name: Joi.string().min(2).max(100).required(),
    first_name_ar: Joi.string().max(100).optional(),
    last_name_ar: Joi.string().max(100).optional(),
    date_of_birth: Joi.date().required(),
    gender: Joi.string().valid('male', 'female').required(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    email: Joi.string().email().optional(),
    emergency_contact_name: Joi.string().max(200).optional(),
    emergency_contact_phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional(),
    address: Joi.string().optional(),
    city: Joi.string().max(100).optional(),
    region: Joi.string().max(100).optional(),
    postal_code: Joi.string().max(10).optional(),
    insurance_provider: Joi.string().max(100).optional(),
    insurance_number: Joi.string().max(100).optional(),
    insurance_expiry: Joi.date().optional(),
    preferred_language: Joi.string().valid('ar', 'en').default('ar')
});

// Generate medical record number
const generateMRN = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `MRN${timestamp}${random}`;
};

// GET /api/patients - Get all patients with pagination and search
router.get('/', async (req, res) => {
    try {
        const { page = 1, limit = 20, search = '', language = 'en' } = req.query;
        const offset = (page - 1) * limit;
        
        let query = `
            SELECT 
                id, national_id, medical_record_number,
                CASE WHEN $4 = 'ar' THEN 
                    COALESCE(first_name_ar, first_name) || ' ' || COALESCE(last_name_ar, last_name)
                ELSE 
                    first_name || ' ' || last_name 
                END as full_name,
                first_name, last_name, first_name_ar, last_name_ar,
                date_of_birth, gender, phone, email,
                insurance_provider, preferred_language,
                is_active, created_at
            FROM patients 
            WHERE is_active = true
        `;
        
        const params = [limit, offset, `%${search}%`, language];
        
        if (search) {
            query += ` AND (
                first_name ILIKE $3 OR last_name ILIKE $3 OR
                first_name_ar ILIKE $3 OR last_name_ar ILIKE $3 OR
                national_id ILIKE $3 OR medical_record_number ILIKE $3
            )`;
        }
        
        query += ` ORDER BY created_at DESC LIMIT $1 OFFSET $2`;
        
        const result = await pool.query(query, params);
        
        // Get total count
        let countQuery = 'SELECT COUNT(*) FROM patients WHERE is_active = true';
        const countParams = [];
        
        if (search) {
            countQuery += ` AND (
                first_name ILIKE $1 OR last_name ILIKE $1 OR
                first_name_ar ILIKE $1 OR last_name_ar ILIKE $1 OR
                national_id ILIKE $1 OR medical_record_number ILIKE $1
            )`;
            countParams.push(`%${search}%`);
        }
        
        const countResult = await pool.query(countQuery, countParams);
        const totalCount = parseInt(countResult.rows[0].count);
        
        res.json({
            patients: result.rows,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: totalCount,
                pages: Math.ceil(totalCount / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching patients:', error);
        res.status(500).json({ error: 'Failed to fetch patients' });
    }
});

// GET /api/patients/:id - Get patient by ID
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { language = 'en' } = req.query;
        
        const query = `
            SELECT 
                p.*,
                CASE WHEN $2 = 'ar' THEN 
                    COALESCE(p.first_name_ar, p.first_name) || ' ' || COALESCE(p.last_name_ar, p.last_name)
                ELSE 
                    p.first_name || ' ' || p.last_name 
                END as full_name
            FROM patients p 
            WHERE p.id = $1 AND p.is_active = true
        `;
        
        const result = await pool.query(query, [id, language]);
        
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Patient not found' });
        }
        
        res.json(result.rows[0]);
    } catch (error) {
        console.error('Error fetching patient:', error);
        res.status(500).json({ error: 'Failed to fetch patient' });
    }
});

// POST /api/patients - Create new patient
router.post('/', async (req, res) => {
    try {
        const { error, value } = patientSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        
        // Check if national ID already exists
        const existingPatient = await pool.query(
            'SELECT id FROM patients WHERE national_id = $1 AND is_active = true',
            [value.national_id]
        );
        
        if (existingPatient.rows.length > 0) {
            return res.status(409).json({ error: 'Patient with this National ID already exists' });
        }
        
        const patientId = uuidv4();
        const medicalRecordNumber = generateMRN();
        
        const query = `
            INSERT INTO patients (
                id, national_id, medical_record_number, first_name, last_name,
                first_name_ar, last_name_ar, date_of_birth, gender, phone, email,
                emergency_contact_name, emergency_contact_phone, address, city,
                region, postal_code, insurance_provider, insurance_number,
                insurance_expiry, preferred_language
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
                $16, $17, $18, $19, $20, $21
            ) RETURNING *
        `;
        
        const values = [
            patientId, value.national_id, medicalRecordNumber, value.first_name,
            value.last_name, value.first_name_ar, value.last_name_ar,
            value.date_of_birth, value.gender, value.phone, value.email,
            value.emergency_contact_name, value.emergency_contact_phone,
            value.address, value.city, value.region, value.postal_code,
            value.insurance_provider, value.insurance_number,
            value.insurance_expiry, value.preferred_language
        ];
        
        const result = await pool.query(query, values);
        
        res.status(201).json({
            message: 'Patient created successfully',
            patient: result.rows[0]
        });
    } catch (error) {
        console.error('Error creating patient:', error);
        res.status(500).json({ error: 'Failed to create patient' });
    }
});

// PUT /api/patients/:id - Update patient
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { error, value } = patientSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        
        // Check if patient exists
        const existingPatient = await pool.query(
            'SELECT id FROM patients WHERE id = $1 AND is_active = true',
            [id]
        );
        
        if (existingPatient.rows.length === 0) {
            return res.status(404).json({ error: 'Patient not found' });
        }
        
        const query = `
            UPDATE patients SET
                first_name = $2, last_name = $3, first_name_ar = $4, last_name_ar = $5,
                date_of_birth = $6, gender = $7, phone = $8, email = $9,
                emergency_contact_name = $10, emergency_contact_phone = $11,
                address = $12, city = $13, region = $14, postal_code = $15,
                insurance_provider = $16, insurance_number = $17,
                insurance_expiry = $18, preferred_language = $19,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            RETURNING *
        `;
        
        const values = [
            id, value.first_name, value.last_name, value.first_name_ar,
            value.last_name_ar, value.date_of_birth, value.gender, value.phone,
            value.email, value.emergency_contact_name, value.emergency_contact_phone,
            value.address, value.city, value.region, value.postal_code,
            value.insurance_provider, value.insurance_number,
            value.insurance_expiry, value.preferred_language
        ];
        
        const result = await pool.query(query, values);
        
        res.json({
            message: 'Patient updated successfully',
            patient: result.rows[0]
        });
    } catch (error) {
        console.error('Error updating patient:', error);
        res.status(500).json({ error: 'Failed to update patient' });
    }
});

// DELETE /api/patients/:id - Soft delete patient
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const result = await pool.query(
            'UPDATE patients SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1 AND is_active = true RETURNING id',
            [id]
        );
        
        if (result.rows.length === 0) {
            return res.status(404).json({ error: 'Patient not found' });
        }
        
        res.json({ message: 'Patient deleted successfully' });
    } catch (error) {
        console.error('Error deleting patient:', error);
        res.status(500).json({ error: 'Failed to delete patient' });
    }
});

module.exports = router;
