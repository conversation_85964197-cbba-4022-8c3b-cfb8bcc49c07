const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const { pool, redis } = require('../server');

const router = express.Router();

// Validation schemas
const loginSchema = Joi.object({
    username: Joi.string().min(3).max(50).required(),
    password: Joi.string().min(6).required(),
    remember_me: Joi.boolean().default(false)
});

const registerSchema = Joi.object({
    username: Joi.string().min(3).max(50).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])')).required(),
    first_name: Joi.string().min(2).max(100).required(),
    last_name: Joi.string().min(2).max(100).required(),
    first_name_ar: Joi.string().max(100).optional(),
    last_name_ar: Joi.string().max(100).optional(),
    role: Joi.string().valid('admin', 'doctor', 'therapist', 'nurse', 'receptionist').required(),
    license_number: Joi.string().max(100).optional(),
    specialization: Joi.string().max(100).optional(),
    phone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).optional()
});

// Generate JWT token
const generateToken = (user, expiresIn = '24h') => {
    return jwt.sign(
        {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            first_name: user.first_name,
            last_name: user.last_name
        },
        process.env.JWT_SECRET,
        { expiresIn }
    );
};

// Login endpoint
router.post('/login', async (req, res) => {
    try {
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        
        const { username, password, remember_me } = value;
        
        // Check if user exists and is active
        const userQuery = `
            SELECT id, username, email, password_hash, first_name, last_name,
                   first_name_ar, last_name_ar, role, license_number, 
                   specialization, phone, is_active
            FROM users 
            WHERE (username = $1 OR email = $1) AND is_active = true
        `;
        
        const userResult = await pool.query(userQuery, [username]);
        
        if (userResult.rows.length === 0) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        const user = userResult.rows[0];
        
        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        
        // Update last login
        await pool.query(
            'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = $1',
            [user.id]
        );
        
        // Generate token
        const expiresIn = remember_me ? '30d' : '24h';
        const token = generateToken(user, expiresIn);
        
        // Store session in Redis
        const sessionKey = `session:${user.id}:${Date.now()}`;
        await redis.setEx(sessionKey, remember_me ? 2592000 : 86400, JSON.stringify({
            user_id: user.id,
            username: user.username,
            role: user.role,
            login_time: new Date().toISOString(),
            ip_address: req.ip,
            user_agent: req.get('User-Agent')
        }));
        
        // Log successful login
        await pool.query(
            `INSERT INTO audit_logs (user_id, action, table_name, new_values, ip_address, user_agent)
             VALUES ($1, $2, $3, $4, $5, $6)`,
            [
                user.id,
                'LOGIN',
                'users',
                JSON.stringify({ login_time: new Date().toISOString() }),
                req.ip,
                req.get('User-Agent')
            ]
        );
        
        // Remove password hash from response
        delete user.password_hash;
        
        res.json({
            message: 'Login successful',
            token,
            user,
            session_key: sessionKey,
            expires_in: expiresIn
        });
        
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// Register endpoint
router.post('/register', async (req, res) => {
    try {
        const { error, value } = registerSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }
        
        // Check if username or email already exists
        const existingUser = await pool.query(
            'SELECT id FROM users WHERE username = $1 OR email = $2',
            [value.username, value.email]
        );
        
        if (existingUser.rows.length > 0) {
            return res.status(409).json({ error: 'Username or email already exists' });
        }
        
        // Hash password
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(value.password, saltRounds);
        
        // Create user
        const userId = uuidv4();
        const insertQuery = `
            INSERT INTO users (
                id, username, email, password_hash, first_name, last_name,
                first_name_ar, last_name_ar, role, license_number,
                specialization, phone
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING id, username, email, first_name, last_name, 
                     first_name_ar, last_name_ar, role, license_number,
                     specialization, phone, is_active, created_at
        `;
        
        const values = [
            userId, value.username, value.email, passwordHash,
            value.first_name, value.last_name, value.first_name_ar,
            value.last_name_ar, value.role, value.license_number,
            value.specialization, value.phone
        ];
        
        const result = await pool.query(insertQuery, values);
        const newUser = result.rows[0];
        
        // Log user creation
        await pool.query(
            `INSERT INTO audit_logs (user_id, action, table_name, new_values, ip_address, user_agent)
             VALUES ($1, $2, $3, $4, $5, $6)`,
            [
                userId,
                'CREATE_USER',
                'users',
                JSON.stringify(newUser),
                req.ip,
                req.get('User-Agent')
            ]
        );
        
        res.status(201).json({
            message: 'User registered successfully',
            user: newUser
        });
        
    } catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Registration failed' });
    }
});

// Logout endpoint
router.post('/logout', async (req, res) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        
        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Remove session from Redis
        const sessionKey = req.body.session_key;
        if (sessionKey) {
            await redis.del(sessionKey);
        }
        
        // Log logout
        await pool.query(
            `INSERT INTO audit_logs (user_id, action, table_name, new_values, ip_address, user_agent)
             VALUES ($1, $2, $3, $4, $5, $6)`,
            [
                decoded.id,
                'LOGOUT',
                'users',
                JSON.stringify({ logout_time: new Date().toISOString() }),
                req.ip,
                req.get('User-Agent')
            ]
        );
        
        res.json({ message: 'Logout successful' });
        
    } catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({ error: 'Logout failed' });
    }
});

// Token verification endpoint
router.post('/verify', async (req, res) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        
        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Check if user still exists and is active
        const userResult = await pool.query(
            'SELECT id, username, email, first_name, last_name, role, is_active FROM users WHERE id = $1 AND is_active = true',
            [decoded.id]
        );
        
        if (userResult.rows.length === 0) {
            return res.status(401).json({ error: 'User not found or inactive' });
        }
        
        res.json({
            valid: true,
            user: userResult.rows[0]
        });
        
    } catch (error) {
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            return res.status(401).json({ error: 'Invalid or expired token' });
        }
        console.error('Token verification error:', error);
        res.status(500).json({ error: 'Token verification failed' });
    }
});

// Change password endpoint
router.post('/change-password', async (req, res) => {
    try {
        const token = req.headers.authorization?.replace('Bearer ', '');
        if (!token) {
            return res.status(401).json({ error: 'No token provided' });
        }
        
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const { current_password, new_password } = req.body;
        
        if (!current_password || !new_password) {
            return res.status(400).json({ error: 'Current password and new password are required' });
        }
        
        // Validate new password strength
        const passwordSchema = Joi.string().min(8).pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#\$%\^&\*])'));
        const { error } = passwordSchema.validate(new_password);
        if (error) {
            return res.status(400).json({ error: 'New password must be at least 8 characters with uppercase, lowercase, number, and special character' });
        }
        
        // Get current password hash
        const userResult = await pool.query(
            'SELECT password_hash FROM users WHERE id = $1 AND is_active = true',
            [decoded.id]
        );
        
        if (userResult.rows.length === 0) {
            return res.status(404).json({ error: 'User not found' });
        }
        
        // Verify current password
        const isValidPassword = await bcrypt.compare(current_password, userResult.rows[0].password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Current password is incorrect' });
        }
        
        // Hash new password
        const saltRounds = 12;
        const newPasswordHash = await bcrypt.hash(new_password, saltRounds);
        
        // Update password
        await pool.query(
            'UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2',
            [newPasswordHash, decoded.id]
        );
        
        // Log password change
        await pool.query(
            `INSERT INTO audit_logs (user_id, action, table_name, new_values, ip_address, user_agent)
             VALUES ($1, $2, $3, $4, $5, $6)`,
            [
                decoded.id,
                'CHANGE_PASSWORD',
                'users',
                JSON.stringify({ password_changed: new Date().toISOString() }),
                req.ip,
                req.get('User-Agent')
            ]
        );
        
        res.json({ message: 'Password changed successfully' });
        
    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ error: 'Password change failed' });
    }
});

module.exports = router;
