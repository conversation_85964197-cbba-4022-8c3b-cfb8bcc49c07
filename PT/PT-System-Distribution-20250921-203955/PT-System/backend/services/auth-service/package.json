{"name": "auth-service", "version": "1.0.0", "description": "Authentication and Authorization Service for PT System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "joi": "^17.9.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.1.4", "uuid": "^9.0.0", "redis": "^4.6.7", "express-rate-limit": "^6.7.0", "speakeasy": "^2.0.0", "qrcode": "^1.5.3"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3"}, "keywords": ["healthcare", "authentication", "authorization", "security"], "author": "PT System Team", "license": "MIT"}