{"name": "ai-service", "version": "1.0.0", "description": "AI Service for PT System - Auto-fill, Clinical Decision Support, NLP", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.1.4", "uuid": "^9.0.0", "axios": "^1.4.0", "openai": "^4.0.0", "natural": "^6.5.0", "compromise": "^14.10.0", "redis": "^4.6.7"}, "devDependencies": {"nodemon": "^2.0.22", "jest": "^29.5.0", "supertest": "^6.3.3"}, "keywords": ["healthcare", "ai", "nlp", "clinical-decision-support"], "author": "PT System Team", "license": "MIT"}