const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { Pool } = require('pg');
const Redis = require('redis');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3004;

// Database connection
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'pt_system',
    password: process.env.DB_PASSWORD || 'password',
    port: process.env.DB_PORT || 5432,
});

// Redis connection
const redis = Redis.createClient({
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
});

redis.on('error', (err) => console.log('Redis Client Error', err));
redis.connect();

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
const autoFillRoutes = require('./routes/autoFill');
const clinicalDecisionRoutes = require('./routes/clinicalDecision');
const nlpRoutes = require('./routes/nlp');
const predictiveRoutes = require('./routes/predictive');

app.use('/api/ai/auto-fill', autoFillRoutes);
app.use('/api/ai/clinical-decision', clinicalDecisionRoutes);
app.use('/api/ai/nlp', nlpRoutes);
app.use('/api/ai/predictive', predictiveRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        service: 'ai-service',
        timestamp: new Date().toISOString()
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        error: 'Internal Server Error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({ error: 'Route not found' });
});

// Start server
app.listen(PORT, () => {
    console.log(`AI Service running on port ${PORT}`);
});

module.exports = { app, pool, redis };
