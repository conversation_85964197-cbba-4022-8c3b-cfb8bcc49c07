const express = require('express');
const { pool, redis } = require('../server');
const OpenAI = require('openai');

const router = express.Router();

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

// Auto-fill patient demographics based on National ID
router.post('/demographics', async (req, res) => {
    try {
        const { national_id, language = 'en' } = req.body;
        
        if (!national_id) {
            return res.status(400).json({ error: 'National ID is required' });
        }
        
        // Check cache first
        const cacheKey = `demographics:${national_id}`;
        const cached = await redis.get(cacheKey);
        
        if (cached) {
            return res.json(JSON.parse(cached));
        }
        
        // Query existing patient data
        const query = `
            SELECT 
                first_name, last_name, first_name_ar, last_name_ar,
                date_of_birth, gender, phone, email, address, city, region,
                insurance_provider, insurance_number, preferred_language
            FROM patients 
            WHERE national_id = $1 AND is_active = true
        `;
        
        const result = await pool.query(query, [national_id]);
        
        if (result.rows.length > 0) {
            const patientData = result.rows[0];
            
            // Cache the result for 1 hour
            await redis.setEx(cacheKey, 3600, JSON.stringify(patientData));
            
            res.json(patientData);
        } else {
            // If no existing patient, return empty structure
            res.json({
                message: 'No existing patient found',
                suggestions: {
                    preferred_language: 'ar', // Default for Saudi Arabia
                    region: 'Riyadh' // Default region
                }
            });
        }
    } catch (error) {
        console.error('Error in auto-fill demographics:', error);
        res.status(500).json({ error: 'Failed to auto-fill demographics' });
    }
});

// Auto-fill medical history based on diagnosis
router.post('/medical-history', async (req, res) => {
    try {
        const { diagnosis, patient_id, language = 'en' } = req.body;
        
        if (!diagnosis) {
            return res.status(400).json({ error: 'Diagnosis is required' });
        }
        
        // Check cache for similar diagnoses
        const cacheKey = `medical_history:${diagnosis.toLowerCase()}:${language}`;
        const cached = await redis.get(cacheKey);
        
        if (cached) {
            return res.json(JSON.parse(cached));
        }
        
        // Query similar cases from database
        const query = `
            SELECT 
                mh.condition_name, mh.condition_name_ar, mh.icd_10_code,
                COUNT(*) as frequency
            FROM medical_history mh
            WHERE LOWER(mh.condition_name) LIKE LOWER($1) 
               OR LOWER(mh.condition_name_ar) LIKE LOWER($1)
            GROUP BY mh.condition_name, mh.condition_name_ar, mh.icd_10_code
            ORDER BY frequency DESC
            LIMIT 5
        `;
        
        const result = await pool.query(query, [`%${diagnosis}%`]);
        
        // Use AI to suggest related conditions and treatments
        const aiPrompt = `
        Based on the diagnosis "${diagnosis}", suggest:
        1. Common related medical conditions
        2. Typical treatment approaches for physical therapy
        3. Expected duration of treatment
        4. Common complications to watch for
        
        Respond in JSON format with keys: related_conditions, treatment_approaches, expected_duration, complications.
        Keep responses concise and clinically relevant for Saudi Arabia healthcare context.
        `;
        
        let aiSuggestions = {};
        try {
            const completion = await openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [{ role: "user", content: aiPrompt }],
                max_tokens: 500,
                temperature: 0.3
            });
            
            aiSuggestions = JSON.parse(completion.choices[0].message.content);
        } catch (aiError) {
            console.error('AI suggestion error:', aiError);
            // Fallback to basic suggestions
            aiSuggestions = {
                related_conditions: [],
                treatment_approaches: ["Physical therapy", "Exercise therapy"],
                expected_duration: "4-8 weeks",
                complications: []
            };
        }
        
        const response = {
            similar_cases: result.rows,
            ai_suggestions: aiSuggestions,
            timestamp: new Date().toISOString()
        };
        
        // Cache for 24 hours
        await redis.setEx(cacheKey, 86400, JSON.stringify(response));
        
        res.json(response);
    } catch (error) {
        console.error('Error in auto-fill medical history:', error);
        res.status(500).json({ error: 'Failed to auto-fill medical history' });
    }
});

// Auto-fill treatment goals based on diagnosis and patient profile
router.post('/treatment-goals', async (req, res) => {
    try {
        const { diagnosis, patient_age, patient_gender, functional_level, language = 'en' } = req.body;
        
        if (!diagnosis) {
            return res.status(400).json({ error: 'Diagnosis is required' });
        }
        
        const cacheKey = `treatment_goals:${diagnosis}:${patient_age}:${patient_gender}:${functional_level}:${language}`;
        const cached = await redis.get(cacheKey);
        
        if (cached) {
            return res.json(JSON.parse(cached));
        }
        
        // Query similar treatment plans
        const query = `
            SELECT 
                tp.short_term_goals, tp.long_term_goals,
                tp.treatment_frequency, tp.estimated_duration
            FROM treatment_plans tp
            JOIN patients p ON tp.patient_id = p.id
            WHERE LOWER(tp.diagnosis) LIKE LOWER($1)
              AND EXTRACT(YEAR FROM AGE(p.date_of_birth)) BETWEEN $2 - 10 AND $2 + 10
              AND p.gender = $3
            ORDER BY tp.created_at DESC
            LIMIT 3
        `;
        
        const result = await pool.query(query, [`%${diagnosis}%`, patient_age || 30, patient_gender || 'male']);
        
        // AI-generated goals based on evidence-based practice
        const aiPrompt = `
        Generate SMART treatment goals for a ${patient_age || 'adult'} year old ${patient_gender || 'patient'} with ${diagnosis}.
        Functional level: ${functional_level || 'moderate'}
        
        Provide:
        1. 3-4 short-term goals (2-4 weeks)
        2. 3-4 long-term goals (6-8 weeks)
        3. Recommended frequency and duration
        
        Goals should be Specific, Measurable, Achievable, Relevant, and Time-bound.
        Consider Saudi Arabia healthcare context and cultural factors.
        
        Respond in JSON format with keys: short_term_goals, long_term_goals, frequency, duration.
        Each goal should be a string in both English and Arabic if possible.
        `;
        
        let aiGoals = {};
        try {
            const completion = await openai.chat.completions.create({
                model: "gpt-3.5-turbo",
                messages: [{ role: "user", content: aiPrompt }],
                max_tokens: 800,
                temperature: 0.4
            });
            
            aiGoals = JSON.parse(completion.choices[0].message.content);
        } catch (aiError) {
            console.error('AI goals generation error:', aiError);
            // Fallback goals
            aiGoals = {
                short_term_goals: [
                    "Reduce pain level from current to 3/10 within 2 weeks",
                    "Improve range of motion by 20% within 3 weeks",
                    "Increase functional mobility for daily activities"
                ],
                long_term_goals: [
                    "Return to previous functional level within 6-8 weeks",
                    "Achieve pain-free movement in affected area",
                    "Demonstrate proper body mechanics and exercise techniques"
                ],
                frequency: "3 times per week",
                duration: "6-8 weeks"
            };
        }
        
        const response = {
            similar_plans: result.rows,
            ai_generated_goals: aiGoals,
            timestamp: new Date().toISOString()
        };
        
        // Cache for 12 hours
        await redis.setEx(cacheKey, 43200, JSON.stringify(response));
        
        res.json(response);
    } catch (error) {
        console.error('Error in auto-fill treatment goals:', error);
        res.status(500).json({ error: 'Failed to auto-fill treatment goals' });
    }
});

// Auto-fill form fields based on previous submissions
router.post('/form-fields', async (req, res) => {
    try {
        const { form_type, patient_id, partial_data = {}, language = 'en' } = req.body;
        
        if (!form_type) {
            return res.status(400).json({ error: 'Form type is required' });
        }
        
        // Get patient's previous form submissions
        let query = `
            SELECT form_data, submission_date
            FROM form_submissions fs
            JOIN form_templates ft ON fs.template_id = ft.id
            WHERE ft.form_type = $1
        `;
        
        const params = [form_type];
        
        if (patient_id) {
            query += ` AND fs.patient_id = $2`;
            params.push(patient_id);
        }
        
        query += ` ORDER BY fs.submission_date DESC LIMIT 5`;
        
        const result = await pool.query(query, params);
        
        // Analyze patterns in previous submissions
        const suggestions = {};
        const fieldFrequency = {};
        
        result.rows.forEach(row => {
            const formData = row.form_data;
            Object.keys(formData).forEach(field => {
                if (formData[field] && formData[field] !== '') {
                    if (!fieldFrequency[field]) {
                        fieldFrequency[field] = {};
                    }
                    const value = formData[field];
                    fieldFrequency[field][value] = (fieldFrequency[field][value] || 0) + 1;
                }
            });
        });
        
        // Generate suggestions based on frequency
        Object.keys(fieldFrequency).forEach(field => {
            const values = fieldFrequency[field];
            const mostCommon = Object.keys(values).reduce((a, b) => 
                values[a] > values[b] ? a : b
            );
            suggestions[field] = mostCommon;
        });
        
        res.json({
            suggestions,
            previous_submissions_count: result.rows.length,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Error in auto-fill form fields:', error);
        res.status(500).json({ error: 'Failed to auto-fill form fields' });
    }
});

module.exports = router;
