const AIAnalytics = require('../models/AIAnalytics');
const aiAnalyticsService = require('../services/aiAnalyticsService');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

/**
 * AI Analytics Controller
 * Handles AI-powered analytics, predictions, and recommendations
 */

// @desc    Perform AI analysis for a patient
// @route   POST /api/v1/ai-analytics/analyze/:patientId
// @access  Private (Doctor, Therapist, Admin)
exports.performAnalysis = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;
  const { analysis_type = 'progress_evaluation' } = req.body;
  
  logger.info(`AI analysis requested for patient ${patientId} by user ${req.user.id}`);
  
  try {
    const analysis = await aiAnalyticsService.performAnalysis(
      patientId, 
      analysis_type, 
      req.user.id
    );
    
    res.status(201).json({
      success: true,
      data: analysis,
      message: 'AI analysis completed successfully'
    });
  } catch (error) {
    logger.error('AI analysis failed:', error);
    return next(new AppError('AI analysis failed', 500));
  }
});

// @desc    Get AI analytics for a patient
// @route   GET /api/v1/ai-analytics/patient/:patientId
// @access  Private
exports.getPatientAnalytics = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;
  const { limit = 10, page = 1 } = req.query;
  
  const analytics = await AIAnalytics.find({ patient: patientId })
    .sort({ analysis_date: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .populate('created_by', 'firstName lastName role')
    .populate('patient', 'firstName lastName dateOfBirth');
  
  const total = await AIAnalytics.countDocuments({ patient: patientId });
  
  res.status(200).json({
    success: true,
    count: analytics.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    },
    data: analytics
  });
});

// @desc    Get latest AI analysis for a patient
// @route   GET /api/v1/ai-analytics/patient/:patientId/latest
// @access  Private
exports.getLatestAnalysis = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;
  
  const analysis = await AIAnalytics.getLatestAnalysis(patientId);
  
  if (!analysis) {
    return next(new AppError('No AI analysis found for this patient', 404));
  }
  
  res.status(200).json({
    success: true,
    data: analysis
  });
});

// @desc    Get high-risk patients
// @route   GET /api/v1/ai-analytics/high-risk
// @access  Private (Admin, Manager, Doctor)
exports.getHighRiskPatients = asyncHandler(async (req, res, next) => {
  const highRiskPatients = await AIAnalytics.getHighRiskPatients();
  
  res.status(200).json({
    success: true,
    count: highRiskPatients.length,
    data: highRiskPatients
  });
});

// @desc    Get pending clinical alerts
// @route   GET /api/v1/ai-analytics/alerts
// @access  Private
exports.getPendingAlerts = asyncHandler(async (req, res, next) => {
  const alerts = await AIAnalytics.getPendingAlerts();
  
  res.status(200).json({
    success: true,
    count: alerts.length,
    data: alerts
  });
});

// @desc    Dismiss a clinical alert
// @route   PUT /api/v1/ai-analytics/:id/alerts/:alertIndex/dismiss
// @access  Private
exports.dismissAlert = asyncHandler(async (req, res, next) => {
  const { id, alertIndex } = req.params;
  const { reason } = req.body;
  
  const analysis = await AIAnalytics.findById(id);
  
  if (!analysis) {
    return next(new AppError('AI analysis not found', 404));
  }
  
  try {
    await analysis.dismissAlert(parseInt(alertIndex), req.user.id, reason);
    
    res.status(200).json({
      success: true,
      message: 'Alert dismissed successfully'
    });
  } catch (error) {
    return next(new AppError(error.message, 400));
  }
});

// @desc    Add feedback to AI recommendation
// @route   POST /api/v1/ai-analytics/:id/feedback
// @access  Private
exports.addFeedback = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const { recommendation_id, rating, comments, implemented, outcome } = req.body;
  
  const analysis = await AIAnalytics.findById(id);
  
  if (!analysis) {
    return next(new AppError('AI analysis not found', 404));
  }
  
  await analysis.addFeedback(
    req.user.id,
    recommendation_id,
    rating,
    comments,
    implemented,
    outcome
  );
  
  res.status(201).json({
    success: true,
    message: 'Feedback added successfully'
  });
});

// @desc    Get AI analytics dashboard data
// @route   GET /api/v1/ai-analytics/dashboard
// @access  Private (Admin, Manager, Doctor)
exports.getDashboardData = asyncHandler(async (req, res, next) => {
  const { timeframe = '30' } = req.query; // days
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(timeframe));
  
  // Get analytics summary
  const totalAnalyses = await AIAnalytics.countDocuments({
    analysis_date: { $gte: startDate },
    status: 'completed'
  });
  
  const highRiskCount = await AIAnalytics.countDocuments({
    'risk_assessment.risk_category': { $in: ['high', 'critical'] },
    analysis_date: { $gte: startDate },
    status: 'completed'
  });
  
  const pendingAlertsCount = await AIAnalytics.countDocuments({
    'clinical_alerts.dismissed': false,
    analysis_date: { $gte: startDate },
    status: 'completed'
  });
  
  // Get risk distribution
  const riskDistribution = await AIAnalytics.aggregate([
    {
      $match: {
        analysis_date: { $gte: startDate },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$risk_assessment.risk_category',
        count: { $sum: 1 }
      }
    }
  ]);
  
  // Get analysis trends
  const analysisTrends = await AIAnalytics.aggregate([
    {
      $match: {
        analysis_date: { $gte: startDate },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$analysis_date'
          }
        },
        count: { $sum: 1 },
        avgProcessingTime: { $avg: '$processing_time_ms' }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);
  
  // Get model performance metrics
  const modelPerformance = await AIAnalytics.aggregate([
    {
      $match: {
        analysis_date: { $gte: startDate },
        status: 'completed'
      }
    },
    {
      $unwind: '$predictions'
    },
    {
      $group: {
        _id: '$predictions.type',
        avgConfidence: { $avg: '$predictions.confidence' },
        count: { $sum: 1 }
      }
    }
  ]);
  
  res.status(200).json({
    success: true,
    data: {
      summary: {
        total_analyses: totalAnalyses,
        high_risk_patients: highRiskCount,
        pending_alerts: pendingAlertsCount,
        timeframe: `${timeframe} days`
      },
      risk_distribution: riskDistribution,
      analysis_trends: analysisTrends,
      model_performance: modelPerformance
    }
  });
});

// @desc    Get treatment recommendations for a patient
// @route   GET /api/v1/ai-analytics/patient/:patientId/recommendations
// @access  Private
exports.getTreatmentRecommendations = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;
  
  const analysis = await AIAnalytics.getLatestAnalysis(patientId);
  
  if (!analysis) {
    return next(new AppError('No AI analysis found for this patient', 404));
  }
  
  res.status(200).json({
    success: true,
    data: {
      recommendations: analysis.treatment_recommendations,
      confidence_scores: analysis.predictions,
      last_updated: analysis.analysis_date
    }
  });
});

// @desc    Get outcome predictions for a patient
// @route   GET /api/v1/ai-analytics/patient/:patientId/predictions
// @access  Private
exports.getOutcomePredictions = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;
  
  const analysis = await AIAnalytics.getLatestAnalysis(patientId);
  
  if (!analysis) {
    return next(new AppError('No AI analysis found for this patient', 404));
  }
  
  res.status(200).json({
    success: true,
    data: {
      outcome_predictions: analysis.outcome_predictions,
      risk_assessment: analysis.risk_assessment,
      last_updated: analysis.analysis_date
    }
  });
});

// @desc    Trigger batch analysis for all patients
// @route   POST /api/v1/ai-analytics/batch-analyze
// @access  Private (Admin only)
exports.batchAnalyze = asyncHandler(async (req, res, next) => {
  const { patient_ids, analysis_type = 'progress_evaluation' } = req.body;
  
  if (!patient_ids || !Array.isArray(patient_ids)) {
    return next(new AppError('Patient IDs array is required', 400));
  }
  
  const results = [];
  const errors = [];
  
  for (const patientId of patient_ids) {
    try {
      const analysis = await aiAnalyticsService.performAnalysis(
        patientId,
        analysis_type,
        req.user.id
      );
      results.push({ patientId, status: 'success', analysisId: analysis._id });
    } catch (error) {
      errors.push({ patientId, status: 'failed', error: error.message });
    }
  }
  
  res.status(200).json({
    success: true,
    data: {
      successful: results.length,
      failed: errors.length,
      results,
      errors
    }
  });
});

// @desc    Get AI analytics statistics
// @route   GET /api/v1/ai-analytics/statistics
// @access  Private (Admin, Manager)
exports.getStatistics = asyncHandler(async (req, res, next) => {
  const stats = await AIAnalytics.aggregate([
    {
      $facet: {
        totalAnalyses: [{ $count: 'count' }],
        statusDistribution: [
          { $group: { _id: '$status', count: { $sum: 1 } } }
        ],
        analysisTypeDistribution: [
          { $group: { _id: '$analysis_type', count: { $sum: 1 } } }
        ],
        avgProcessingTime: [
          { $group: { _id: null, avgTime: { $avg: '$processing_time_ms' } } }
        ],
        recentAnalyses: [
          { $match: { status: 'completed' } },
          { $sort: { analysis_date: -1 } },
          { $limit: 10 },
          { $lookup: { from: 'patients', localField: 'patient', foreignField: '_id', as: 'patient' } },
          { $unwind: '$patient' },
          { $project: { 
            analysis_date: 1, 
            analysis_type: 1, 
            'patient.firstName': 1, 
            'patient.lastName': 1,
            'risk_assessment.risk_category': 1
          }}
        ]
      }
    }
  ]);
  
  res.status(200).json({
    success: true,
    data: stats[0]
  });
});

module.exports = exports;
