const { asyncHand<PERSON>, AppError } = require('../middleware/errorHandler');
const Form = require('../models/Form');
const FormSubmission = require('../models/FormSubmission');
const logger = require('../utils/logger');

// @desc    Get all forms
// @route   GET /api/v1/forms
// @access  Private
const getForms = asyncHandler(async (req, res, next) => {
  // Build query
  let query = {};
  
  // Copy req.query
  const reqQuery = { ...req.query };
  
  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit'];
  
  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);
  
  // Create query string
  let queryStr = JSON.stringify(reqQuery);
  
  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);
  
  // Finding resource
  query = Form.find(JSON.parse(queryStr));
  
  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }
  
  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }
  
  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 25;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Form.countDocuments(JSON.parse(queryStr));
  
  query = query.skip(startIndex).limit(limit);
  
  // Populate references
  query = query.populate('createdBy', 'firstName lastName role')
               .populate('updatedBy', 'firstName lastName role');
  
  // Filter by user permissions
  if (req.user.role !== 'admin') {
    query = query.find({
      $or: [
        { 'permissions.canView': req.user.role },
        { createdBy: req.user._id }
      ]
    });
  }
  
  // Execute query
  const forms = await query;
  
  // Pagination result
  const pagination = {};
  
  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }
  
  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }
  
  res.status(200).json({
    success: true,
    count: forms.length,
    pagination,
    data: forms
  });
});

// @desc    Get single form
// @route   GET /api/v1/forms/:id
// @access  Private
const getForm = asyncHandler(async (req, res, next) => {
  const form = await Form.findById(req.params.id)
    .populate('createdBy', 'firstName lastName role')
    .populate('updatedBy', 'firstName lastName role');

  if (!form) {
    return next(new AppError(`Form not found with id of ${req.params.id}`, 404));
  }

  // Check if user has permission to view this form
  if (req.user.role !== 'admin') {
    const hasAccess = 
      form.permissions.canView.includes(req.user.role) ||
      form.createdBy._id.toString() === req.user._id.toString();

    if (!hasAccess) {
      return next(new AppError('Not authorized to access this form', 403));
    }
  }

  res.status(200).json({
    success: true,
    data: form
  });
});

// @desc    Create new form
// @route   POST /api/v1/forms
// @access  Private
const createForm = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.createdBy = req.user.id;

  // Validate form fields
  if (req.body.fields && req.body.fields.length > 0) {
    const validationErrors = validateFormFields(req.body.fields);
    if (validationErrors.length > 0) {
      return next(new AppError(`Form validation failed: ${validationErrors.join(', ')}`, 400));
    }
  }

  const form = await Form.create(req.body);

  // Populate references for response
  await form.populate('createdBy', 'firstName lastName role');

  logger.info(`New form created: ${form.title} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: form
  });
});

// @desc    Update form
// @route   PUT /api/v1/forms/:id
// @access  Private
const updateForm = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.updatedBy = req.user.id;

  let form = await Form.findById(req.params.id);

  if (!form) {
    return next(new AppError(`Form not found with id of ${req.params.id}`, 404));
  }

  // Check if user has permission to edit this form
  if (req.user.role !== 'admin') {
    const hasAccess = 
      form.permissions.canEdit.includes(req.user.role) ||
      form.createdBy.toString() === req.user._id.toString();

    if (!hasAccess) {
      return next(new AppError('Not authorized to edit this form', 403));
    }
  }

  // Validate form fields if being updated
  if (req.body.fields && req.body.fields.length > 0) {
    const validationErrors = validateFormFields(req.body.fields);
    if (validationErrors.length > 0) {
      return next(new AppError(`Form validation failed: ${validationErrors.join(', ')}`, 400));
    }
  }

  form = await Form.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  }).populate('createdBy', 'firstName lastName role')
   .populate('updatedBy', 'firstName lastName role');

  logger.info(`Form updated: ${form.title} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: form
  });
});

// @desc    Delete form
// @route   DELETE /api/v1/forms/:id
// @access  Private (Admin only)
const deleteForm = asyncHandler(async (req, res, next) => {
  const form = await Form.findById(req.params.id);

  if (!form) {
    return next(new AppError(`Form not found with id of ${req.params.id}`, 404));
  }

  // Check if form has submissions
  const submissionCount = await FormSubmission.countDocuments({ form: req.params.id });
  if (submissionCount > 0) {
    return next(new AppError('Cannot delete form with existing submissions. Archive it instead.', 400));
  }

  await form.deleteOne();

  logger.info(`Form deleted: ${form.title} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Form deleted successfully'
  });
});

// @desc    Duplicate form
// @route   POST /api/v1/forms/:id/duplicate
// @access  Private
const duplicateForm = asyncHandler(async (req, res, next) => {
  const form = await Form.findById(req.params.id);

  if (!form) {
    return next(new AppError(`Form not found with id of ${req.params.id}`, 404));
  }

  const duplicatedForm = await form.duplicate(req.body.title);

  logger.info(`Form duplicated: ${form.title} -> ${duplicatedForm.title} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: duplicatedForm
  });
});

// @desc    Publish form
// @route   PUT /api/v1/forms/:id/publish
// @access  Private
const publishForm = asyncHandler(async (req, res, next) => {
  const form = await Form.findById(req.params.id);

  if (!form) {
    return next(new AppError(`Form not found with id of ${req.params.id}`, 404));
  }

  // Validate form before publishing
  const validationErrors = form.validateFields();
  if (validationErrors.length > 0) {
    return next(new AppError(`Cannot publish form with validation errors: ${validationErrors.join(', ')}`, 400));
  }

  form.status = 'active';
  form.publishedAt = new Date();
  form.updatedBy = req.user.id;
  await form.save();

  logger.info(`Form published: ${form.title} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: form
  });
});

// @desc    Archive form
// @route   PUT /api/v1/forms/:id/archive
// @access  Private
const archiveForm = asyncHandler(async (req, res, next) => {
  const form = await Form.findById(req.params.id);

  if (!form) {
    return next(new AppError(`Form not found with id of ${req.params.id}`, 404));
  }

  form.status = 'archived';
  form.archivedAt = new Date();
  form.updatedBy = req.user.id;
  await form.save();

  logger.info(`Form archived: ${form.title} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: form
  });
});

// @desc    Get forms by category
// @route   GET /api/v1/forms/category/:category
// @access  Private
const getFormsByCategory = asyncHandler(async (req, res, next) => {
  const { category } = req.params;
  
  const forms = await Form.getByCategory(category, req.user.role);

  res.status(200).json({
    success: true,
    count: forms.length,
    data: forms
  });
});

// @desc    Get form templates
// @route   GET /api/v1/forms/templates
// @access  Private
const getFormTemplates = asyncHandler(async (req, res, next) => {
  const templates = await Form.getTemplates();

  res.status(200).json({
    success: true,
    count: templates.length,
    data: templates
  });
});

// Helper function to validate form fields
const validateFormFields = (fields) => {
  const errors = [];
  const fieldIds = new Set();

  fields.forEach((field, index) => {
    // Check required properties
    if (!field.id) {
      errors.push(`Field ${index + 1}: ID is required`);
    } else if (fieldIds.has(field.id)) {
      errors.push(`Field ${index + 1}: Duplicate ID "${field.id}"`);
    } else {
      fieldIds.add(field.id);
    }

    if (!field.label) {
      errors.push(`Field ${index + 1}: Label is required`);
    }

    if (!field.type) {
      errors.push(`Field ${index + 1}: Type is required`);
    }

    // Validate field-specific requirements
    if (['select', 'radio', 'checkbox'].includes(field.type) && (!field.options || field.options.length === 0)) {
      errors.push(`Field ${index + 1}: Options are required for ${field.type} fields`);
    }
  });

  return errors;
};

// Placeholder functions for missing exports
const validateForm = asyncHandler(async (req, res) => {
  res.json({ success: true, message: 'Form validation placeholder' });
});

const getFormSubmissions = asyncHandler(async (req, res) => {
  res.json({ success: true, data: [], message: 'Form submissions placeholder' });
});

const createFormSubmission = asyncHandler(async (req, res) => {
  res.json({ success: true, message: 'Form submission created placeholder' });
});

const getFormSubmission = asyncHandler(async (req, res) => {
  res.json({ success: true, data: {}, message: 'Form submission placeholder' });
});

const updateFormSubmission = asyncHandler(async (req, res) => {
  res.json({ success: true, message: 'Form submission updated placeholder' });
});

const deleteFormSubmission = asyncHandler(async (req, res) => {
  res.json({ success: true, message: 'Form submission deleted placeholder' });
});

const approveFormSubmission = asyncHandler(async (req, res) => {
  res.json({ success: true, message: 'Form submission approved placeholder' });
});

const rejectFormSubmission = asyncHandler(async (req, res) => {
  res.json({ success: true, message: 'Form submission rejected placeholder' });
});

const exportFormSubmissions = asyncHandler(async (req, res) => {
  res.json({ success: true, message: 'Form submissions exported placeholder' });
});

const getFormAnalytics = asyncHandler(async (req, res) => {
  res.json({ success: true, data: {}, message: 'Form analytics placeholder' });
});

// @desc    Get form statistics
// @route   GET /api/forms/:id/stats
// @access  Private
const getFormStats = asyncHandler(async (req, res) => {
  const form = await Form.findById(req.params.id);

  if (!form) {
    return res.status(404).json({
      success: false,
      message: 'Form not found'
    });
  }

  // Get submission statistics
  const totalSubmissions = await FormSubmission.countDocuments({ formId: req.params.id });
  const approvedSubmissions = await FormSubmission.countDocuments({
    formId: req.params.id,
    status: 'approved'
  });
  const pendingSubmissions = await FormSubmission.countDocuments({
    formId: req.params.id,
    status: 'pending'
  });
  const rejectedSubmissions = await FormSubmission.countDocuments({
    formId: req.params.id,
    status: 'rejected'
  });

  // Calculate completion rate
  const completionRate = totalSubmissions > 0 ? (approvedSubmissions / totalSubmissions) * 100 : 0;

  // Get average completion time (mock data for now)
  const avgCompletionTime = Math.floor(Math.random() * 30) + 5; // 5-35 minutes

  res.status(200).json({
    success: true,
    data: {
      totalSubmissions,
      approvedSubmissions,
      pendingSubmissions,
      rejectedSubmissions,
      completionRate: Math.round(completionRate * 100) / 100,
      avgCompletionTime
    }
  });
});

module.exports = {
  getForms,
  getForm,
  createForm,
  updateForm,
  deleteForm,
  duplicateForm,
  publishForm,
  archiveForm,
  getFormsByCategory,
  getFormTemplates,
  validateForm,
  getFormSubmissions,
  createFormSubmission,
  getFormSubmission,
  updateFormSubmission,
  deleteFormSubmission,
  approveFormSubmission,
  rejectFormSubmission,
  getFormStats,
  exportFormSubmissions,
  getFormAnalytics
};
