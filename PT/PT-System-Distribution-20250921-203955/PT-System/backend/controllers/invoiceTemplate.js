const asyncHandler = require('express-async-handler');
const InvoiceTemplate = require('../models/InvoiceTemplate');
const Billing = require('../models/Billing');

// @desc    Get all invoice templates
// @route   GET /api/v1/invoice-templates
// @access  Private
const getInvoiceTemplates = asyncHandler(async (req, res) => {
  const { type, category, active = 'true' } = req.query;
  
  const query = {};
  
  if (type) query.type = type;
  if (category) query.category = category;
  if (active !== 'all') query.isActive = active === 'true';
  
  const templates = await InvoiceTemplate.find(query)
    .populate('createdBy', 'firstName lastName')
    .populate('updatedBy', 'firstName lastName')
    .sort({ isDefault: -1, usageCount: -1, createdAt: -1 });
  
  res.status(200).json({
    success: true,
    count: templates.length,
    data: templates
  });
});

// @desc    Get single invoice template
// @route   GET /api/v1/invoice-templates/:id
// @access  Private
const getInvoiceTemplate = asyncHandler(async (req, res) => {
  const template = await InvoiceTemplate.findById(req.params.id)
    .populate('createdBy', 'firstName lastName')
    .populate('updatedBy', 'firstName lastName');
  
  if (!template) {
    return res.status(404).json({
      success: false,
      message: 'Invoice template not found'
    });
  }
  
  res.status(200).json({
    success: true,
    data: template
  });
});

// @desc    Create invoice template
// @route   POST /api/v1/invoice-templates
// @access  Private
const createInvoiceTemplate = asyncHandler(async (req, res) => {
  const templateData = {
    ...req.body,
    createdBy: req.user.id
  };
  
  // If this is set as default, unset other defaults of the same type
  if (templateData.isDefault) {
    await InvoiceTemplate.updateMany(
      { type: templateData.type, isDefault: true },
      { isDefault: false }
    );
  }
  
  const template = await InvoiceTemplate.create(templateData);
  
  res.status(201).json({
    success: true,
    message: 'Invoice template created successfully',
    data: template
  });
});

// @desc    Update invoice template
// @route   PUT /api/v1/invoice-templates/:id
// @access  Private
const updateInvoiceTemplate = asyncHandler(async (req, res) => {
  let template = await InvoiceTemplate.findById(req.params.id);
  
  if (!template) {
    return res.status(404).json({
      success: false,
      message: 'Invoice template not found'
    });
  }
  
  // Check if user can update this template
  if (template.isSystemTemplate && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Cannot modify system templates'
    });
  }
  
  const updateData = {
    ...req.body,
    updatedBy: req.user.id
  };
  
  // If this is set as default, unset other defaults of the same type
  if (updateData.isDefault) {
    await InvoiceTemplate.updateMany(
      { type: updateData.type || template.type, isDefault: true, _id: { $ne: template._id } },
      { isDefault: false }
    );
  }
  
  template = await InvoiceTemplate.findByIdAndUpdate(
    req.params.id,
    updateData,
    { new: true, runValidators: true }
  );
  
  res.status(200).json({
    success: true,
    message: 'Invoice template updated successfully',
    data: template
  });
});

// @desc    Delete invoice template
// @route   DELETE /api/v1/invoice-templates/:id
// @access  Private
const deleteInvoiceTemplate = asyncHandler(async (req, res) => {
  const template = await InvoiceTemplate.findById(req.params.id);
  
  if (!template) {
    return res.status(404).json({
      success: false,
      message: 'Invoice template not found'
    });
  }
  
  // Check if template is in use
  const invoicesUsingTemplate = await Billing.countDocuments({ 
    templateId: template._id 
  });
  
  if (invoicesUsingTemplate > 0) {
    return res.status(400).json({
      success: false,
      message: `Cannot delete template. It is being used by ${invoicesUsingTemplate} invoice(s)`
    });
  }
  
  // Check if it's a system template
  if (template.isSystemTemplate && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Cannot delete system templates'
    });
  }
  
  await template.deleteOne();
  
  res.status(200).json({
    success: true,
    message: 'Invoice template deleted successfully'
  });
});

// @desc    Duplicate invoice template
// @route   POST /api/v1/invoice-templates/:id/duplicate
// @access  Private
const duplicateInvoiceTemplate = asyncHandler(async (req, res) => {
  const originalTemplate = await InvoiceTemplate.findById(req.params.id);
  
  if (!originalTemplate) {
    return res.status(404).json({
      success: false,
      message: 'Invoice template not found'
    });
  }
  
  const duplicateData = originalTemplate.toObject();
  delete duplicateData._id;
  delete duplicateData.createdAt;
  delete duplicateData.updatedAt;
  delete duplicateData.__v;
  
  duplicateData.name = `${duplicateData.name} (Copy)`;
  duplicateData.isDefault = false;
  duplicateData.isSystemTemplate = false;
  duplicateData.usageCount = 0;
  duplicateData.lastUsed = undefined;
  duplicateData.createdBy = req.user.id;
  duplicateData.version = '1.0.0';
  duplicateData.changelog = [];
  
  const duplicatedTemplate = await InvoiceTemplate.create(duplicateData);
  
  res.status(201).json({
    success: true,
    message: 'Invoice template duplicated successfully',
    data: duplicatedTemplate
  });
});

// @desc    Set template as default
// @route   PUT /api/v1/invoice-templates/:id/set-default
// @access  Private
const setAsDefault = asyncHandler(async (req, res) => {
  const template = await InvoiceTemplate.findById(req.params.id);
  
  if (!template) {
    return res.status(404).json({
      success: false,
      message: 'Invoice template not found'
    });
  }
  
  // Unset other defaults of the same type
  await InvoiceTemplate.updateMany(
    { type: template.type, isDefault: true },
    { isDefault: false }
  );
  
  // Set this template as default
  template.isDefault = true;
  template.updatedBy = req.user.id;
  await template.save();
  
  res.status(200).json({
    success: true,
    message: 'Template set as default successfully',
    data: template
  });
});

// @desc    Get template preview
// @route   GET /api/v1/invoice-templates/:id/preview
// @access  Private
const getTemplatePreview = asyncHandler(async (req, res) => {
  const template = await InvoiceTemplate.findById(req.params.id);
  
  if (!template) {
    return res.status(404).json({
      success: false,
      message: 'Invoice template not found'
    });
  }
  
  // Generate sample invoice data for preview
  const sampleData = {
    invoiceNumber: 'INV-2024-001',
    issueDate: new Date(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    patient: {
      firstName: 'Ahmed',
      lastName: 'Al-Rashid',
      nationalId: '**********',
      phone: '+966501234567',
      email: '<EMAIL>',
      address: {
        street: 'King Fahd Road',
        city: 'Riyadh',
        postalCode: '12345'
      }
    },
    services: [
      {
        name: 'Physical Therapy Session',
        code: 'PT001',
        description: 'Individual physical therapy session',
        quantity: 3,
        unitPrice: 200,
        totalPrice: 600
      },
      {
        name: 'Exercise Equipment Rental',
        code: 'EQ001',
        description: 'Weekly equipment rental',
        quantity: 1,
        unitPrice: 150,
        totalPrice: 150
      }
    ],
    subtotal: 750,
    tax: {
      rate: 15,
      amount: 112.5
    },
    totalAmount: 862.5,
    notes: 'Thank you for choosing our services.'
  };
  
  res.status(200).json({
    success: true,
    data: {
      template,
      sampleData,
      previewHtml: generatePreviewHtml(template, sampleData)
    }
  });
});

// @desc    Use template (increment usage count)
// @route   POST /api/v1/invoice-templates/:id/use
// @access  Private
const useTemplate = asyncHandler(async (req, res) => {
  const template = await InvoiceTemplate.findById(req.params.id);

  if (!template) {
    return res.status(404).json({
      success: false,
      message: 'Invoice template not found'
    });
  }

  // Increment usage count
  await template.incrementUsage();

  res.status(200).json({
    success: true,
    message: 'Template usage recorded',
    data: {
      templateId: template._id,
      usageCount: template.usageCount,
      lastUsed: template.lastUsed
    }
  });
});

// @desc    Get template statistics
// @route   GET /api/v1/invoice-templates/stats
// @access  Private
const getTemplateStats = asyncHandler(async (req, res) => {
  const stats = await InvoiceTemplate.aggregate([
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        totalUsage: { $sum: '$usageCount' },
        avgUsage: { $avg: '$usageCount' }
      }
    }
  ]);
  
  const totalTemplates = await InvoiceTemplate.countDocuments();
  const activeTemplates = await InvoiceTemplate.countDocuments({ isActive: true });
  const systemTemplates = await InvoiceTemplate.countDocuments({ isSystemTemplate: true });
  
  res.status(200).json({
    success: true,
    data: {
      totalTemplates,
      activeTemplates,
      systemTemplates,
      typeStats: stats
    }
  });
});

// Helper function to generate preview HTML
const generatePreviewHtml = (template, sampleData) => {
  // This is a simplified preview generator
  // In a real implementation, you would use a proper template engine
  return `
    <div style="font-family: ${template.styling.fonts.primary}; color: ${template.styling.colorScheme.primary};">
      <h1>${template.config.header.customTitle || 'Invoice'}</h1>
      <p>Invoice Number: ${sampleData.invoiceNumber}</p>
      <p>Date: ${sampleData.issueDate.toLocaleDateString()}</p>
      <h2>Patient Information</h2>
      <p>${sampleData.patient.firstName} ${sampleData.patient.lastName}</p>
      <p>ID: ${sampleData.patient.nationalId}</p>
      <h2>Services</h2>
      ${sampleData.services.map(service => `
        <div>
          <p>${service.name} - ${service.quantity} x ${service.unitPrice} SAR = ${service.totalPrice} SAR</p>
        </div>
      `).join('')}
      <h3>Total: ${sampleData.totalAmount} SAR</h3>
    </div>
  `;
};

module.exports = {
  getInvoiceTemplates,
  getInvoiceTemplate,
  createInvoiceTemplate,
  updateInvoiceTemplate,
  deleteInvoiceTemplate,
  duplicateInvoiceTemplate,
  setAsDefault,
  getTemplatePreview,
  getTemplateStats,
  useTemplate
};
