const { asyncHandler, AppError } = require('../middleware/errorHandler');
const Appointment = require('../models/Appointment');
const Patient = require('../models/Patient');
const User = require('../models/User');
const logger = require('../utils/logger');

// @desc    Get all appointments
// @route   GET /api/v1/appointments
// @access  Private
const getAppointments = asyncHandler(async (req, res, next) => {
  // Build query
  let query = {};
  
  // Copy req.query
  const reqQuery = { ...req.query };
  
  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit'];
  
  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);
  
  // Create query string
  let queryStr = JSON.stringify(reqQuery);
  
  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);
  
  // Finding resource
  query = Appointment.find(JSON.parse(queryStr));
  
  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }
  
  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('date startTime');
  }
  
  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 25;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Appointment.countDocuments(JSON.parse(queryStr));
  
  query = query.skip(startIndex).limit(limit);
  
  // Populate references
  query = query.populate('patient', 'firstName lastName nationalId phone')
               .populate('therapist', 'firstName lastName specialization')
               .populate('treatmentPlan', 'diagnosis goals')
               .populate('createdBy', 'firstName lastName');
  
  // Filter by user role and permissions
  if (req.user.role !== 'admin') {
    // Non-admin users can only see appointments they're involved in
    query = query.find({
      $or: [
        { therapist: req.user._id },
        { createdBy: req.user._id },
        { 'patient.primaryTherapist': req.user._id },
        { 'patient.assignedDoctor': req.user._id }
      ]
    });
  }
  
  // Execute query
  const appointments = await query;
  
  // Pagination result
  const pagination = {};
  
  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }
  
  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }
  
  res.status(200).json({
    success: true,
    count: appointments.length,
    pagination,
    data: appointments
  });
});

// @desc    Get single appointment
// @route   GET /api/v1/appointments/:id
// @access  Private
const getAppointment = asyncHandler(async (req, res, next) => {
  const appointment = await Appointment.findById(req.params.id)
    .populate('patient', 'firstName lastName nationalId phone email specialNeeds')
    .populate('therapist', 'firstName lastName specialization phone email')
    .populate('treatmentPlan', 'diagnosis goals interventions')
    .populate('createdBy', 'firstName lastName role')
    .populate('updatedBy', 'firstName lastName role');

  if (!appointment) {
    return next(new AppError(`Appointment not found with id of ${req.params.id}`, 404));
  }

  // Check if user has access to this appointment
  if (req.user.role !== 'admin') {
    const hasAccess = 
      appointment.therapist._id.toString() === req.user._id.toString() ||
      appointment.createdBy._id.toString() === req.user._id.toString();

    if (!hasAccess) {
      return next(new AppError('Not authorized to access this appointment', 403));
    }
  }

  res.status(200).json({
    success: true,
    data: appointment
  });
});

// @desc    Create new appointment
// @route   POST /api/v1/appointments
// @access  Private
const createAppointment = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.createdBy = req.user.id;

  // Validate patient exists
  const patient = await Patient.findById(req.body.patient);
  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  // Validate therapist exists
  const therapist = await User.findById(req.body.therapist);
  if (!therapist || !['doctor', 'therapist'].includes(therapist.role)) {
    return next(new AppError('Invalid therapist', 400));
  }

  // Check for scheduling conflicts
  const conflictingAppointment = await Appointment.findOne({
    therapist: req.body.therapist,
    date: req.body.date,
    status: { $in: ['scheduled', 'confirmed', 'in_progress'] },
    $or: [
      {
        startTime: { $lt: req.body.endTime },
        endTime: { $gt: req.body.startTime }
      }
    ]
  });

  if (conflictingAppointment) {
    return next(new AppError('Therapist has a conflicting appointment at this time', 409));
  }

  const appointment = await Appointment.create(req.body);

  // Populate references for response
  await appointment.populate('patient', 'firstName lastName nationalId phone');
  await appointment.populate('therapist', 'firstName lastName specialization');
  await appointment.populate('createdBy', 'firstName lastName role');

  logger.info(`New appointment created: ${appointment._id} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: appointment
  });
});

// @desc    Update appointment
// @route   PUT /api/v1/appointments/:id
// @access  Private
const updateAppointment = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.updatedBy = req.user.id;

  let appointment = await Appointment.findById(req.params.id);

  if (!appointment) {
    return next(new AppError(`Appointment not found with id of ${req.params.id}`, 404));
  }

  // Check if user has permission to update this appointment
  if (req.user.role !== 'admin') {
    const hasAccess = 
      appointment.therapist.toString() === req.user._id.toString() ||
      appointment.createdBy.toString() === req.user._id.toString();

    if (!hasAccess) {
      return next(new AppError('Not authorized to update this appointment', 403));
    }
  }

  // If updating time/date, check for conflicts
  if (req.body.date || req.body.startTime || req.body.endTime || req.body.therapist) {
    const conflictingAppointment = await Appointment.findOne({
      _id: { $ne: appointment._id },
      therapist: req.body.therapist || appointment.therapist,
      date: req.body.date || appointment.date,
      status: { $in: ['scheduled', 'confirmed', 'in_progress'] },
      $or: [
        {
          startTime: { $lt: req.body.endTime || appointment.endTime },
          endTime: { $gt: req.body.startTime || appointment.startTime }
        }
      ]
    });

    if (conflictingAppointment) {
      return next(new AppError('Therapist has a conflicting appointment at this time', 409));
    }
  }

  appointment = await Appointment.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  }).populate('patient', 'firstName lastName nationalId phone')
   .populate('therapist', 'firstName lastName specialization')
   .populate('updatedBy', 'firstName lastName role');

  logger.info(`Appointment updated: ${appointment._id} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: appointment
  });
});

// @desc    Delete appointment
// @route   DELETE /api/v1/appointments/:id
// @access  Private
const deleteAppointment = asyncHandler(async (req, res, next) => {
  const appointment = await Appointment.findById(req.params.id);

  if (!appointment) {
    return next(new AppError(`Appointment not found with id of ${req.params.id}`, 404));
  }

  // Check if user has permission to delete this appointment
  if (req.user.role !== 'admin') {
    const hasAccess = 
      appointment.therapist.toString() === req.user._id.toString() ||
      appointment.createdBy.toString() === req.user._id.toString();

    if (!hasAccess) {
      return next(new AppError('Not authorized to delete this appointment', 403));
    }
  }

  // Check if appointment can be deleted (not completed)
  if (appointment.status === 'completed') {
    return next(new AppError('Cannot delete completed appointments', 400));
  }

  await appointment.deleteOne();

  logger.info(`Appointment deleted: ${req.params.id} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Appointment deleted successfully'
  });
});

// @desc    Cancel appointment
// @route   PUT /api/v1/appointments/:id/cancel
// @access  Private
const cancelAppointment = asyncHandler(async (req, res, next) => {
  const appointment = await Appointment.findById(req.params.id);

  if (!appointment) {
    return next(new AppError(`Appointment not found with id of ${req.params.id}`, 404));
  }

  // Check if appointment can be cancelled
  if (!appointment.canBeCancelled()) {
    return next(new AppError('Appointment cannot be cancelled (less than 24 hours notice or already completed)', 400));
  }

  appointment.status = 'cancelled';
  appointment.cancellationReason = req.body.reason;
  appointment.cancelledBy = req.user.id;
  appointment.cancelledAt = new Date();
  appointment.updatedBy = req.user.id;

  await appointment.save();

  logger.info(`Appointment cancelled: ${appointment._id} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: appointment
  });
});

// @desc    Reschedule appointment
// @route   PUT /api/v1/appointments/:id/reschedule
// @access  Private
const rescheduleAppointment = asyncHandler(async (req, res, next) => {
  const { date, startTime, endTime, reason } = req.body;
  
  const appointment = await Appointment.findById(req.params.id);

  if (!appointment) {
    return next(new AppError(`Appointment not found with id of ${req.params.id}`, 404));
  }

  // Check for conflicts at new time
  const conflictingAppointment = await Appointment.findOne({
    _id: { $ne: appointment._id },
    therapist: appointment.therapist,
    date: date,
    status: { $in: ['scheduled', 'confirmed', 'in_progress'] },
    $or: [
      {
        startTime: { $lt: endTime },
        endTime: { $gt: startTime }
      }
    ]
  });

  if (conflictingAppointment) {
    return next(new AppError('Therapist has a conflicting appointment at the new time', 409));
  }

  // Store original appointment details
  appointment.rescheduledFrom = {
    date: appointment.date,
    startTime: appointment.startTime,
    endTime: appointment.endTime
  };

  // Update appointment with new details
  appointment.date = date;
  appointment.startTime = startTime;
  appointment.endTime = endTime;
  appointment.status = 'rescheduled';
  appointment.rescheduledBy = req.user.id;
  appointment.rescheduledAt = new Date();
  appointment.updatedBy = req.user.id;

  if (reason) {
    appointment.notes = appointment.notes ? 
      `${appointment.notes}\n\nRescheduled: ${reason}` : 
      `Rescheduled: ${reason}`;
  }

  await appointment.save();

  logger.info(`Appointment rescheduled: ${appointment._id} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: appointment
  });
});

// @desc    Complete appointment
// @route   PUT /api/v1/appointments/:id/complete
// @access  Private
const completeAppointment = asyncHandler(async (req, res, next) => {
  const appointment = await Appointment.findById(req.params.id);

  if (!appointment) {
    return next(new AppError(`Appointment not found with id of ${req.params.id}`, 404));
  }

  // Only therapist can complete their appointments
  if (appointment.therapist.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new AppError('Only the assigned therapist can complete this appointment', 403));
  }

  // Update appointment outcome
  appointment.status = 'completed';
  appointment.outcome = {
    attended: true,
    completedOn: new Date(),
    sessionNotes: req.body.sessionNotes,
    progressNotes: req.body.progressNotes,
    nextSteps: req.body.nextSteps,
    homeExercises: req.body.homeExercises || [],
    followUpRequired: req.body.followUpRequired || false,
    followUpDate: req.body.followUpDate,
    patientSatisfaction: req.body.patientSatisfaction
  };
  appointment.updatedBy = req.user.id;

  await appointment.save();

  logger.info(`Appointment completed: ${appointment._id} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: appointment
  });
});

// @desc    Get appointments by date
// @route   GET /api/v1/appointments/date/:date
// @access  Private
const getAppointmentsByDate = asyncHandler(async (req, res, next) => {
  const { date } = req.params;

  const appointments = await Appointment.find({
    date: new Date(date),
    status: { $in: ['scheduled', 'confirmed', 'in_progress'] }
  })
  .populate('patient', 'firstName lastName nationalId')
  .populate('therapist', 'firstName lastName specialization')
  .sort('startTime');

  res.status(200).json({
    success: true,
    count: appointments.length,
    data: appointments
  });
});

// @desc    Get appointments by patient
// @route   GET /api/v1/appointments/patient/:patientId
// @access  Private
const getAppointmentsByPatient = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;

  const appointments = await Appointment.find({ patient: patientId })
    .populate('therapist', 'firstName lastName specialization')
    .populate('treatmentPlan', 'diagnosis')
    .sort('-date -startTime');

  res.status(200).json({
    success: true,
    count: appointments.length,
    data: appointments
  });
});

// @desc    Get appointments by therapist
// @route   GET /api/v1/appointments/therapist/:therapistId
// @access  Private
const getAppointmentsByTherapist = asyncHandler(async (req, res, next) => {
  const { therapistId } = req.params;

  // Check if user can access this therapist's appointments
  if (req.user.role !== 'admin' && req.user._id.toString() !== therapistId) {
    return next(new AppError('Not authorized to access this therapist\'s appointments', 403));
  }

  const appointments = await Appointment.find({ therapist: therapistId })
    .populate('patient', 'firstName lastName nationalId')
    .populate('treatmentPlan', 'diagnosis')
    .sort('date startTime');

  res.status(200).json({
    success: true,
    count: appointments.length,
    data: appointments
  });
});

// @desc    Check therapist availability
// @route   GET /api/v1/appointments/availability
// @access  Private
const checkAvailability = asyncHandler(async (req, res, next) => {
  const { therapist, date } = req.query;

  if (!therapist || !date) {
    return next(new AppError('Therapist ID and date are required', 400));
  }

  const availability = await Appointment.getTherapistAvailability(therapist, new Date(date));

  // Generate available time slots (assuming 9 AM to 5 PM, 1-hour slots)
  const workingHours = {
    start: '09:00',
    end: '17:00',
    slotDuration: 60 // minutes
  };

  const availableSlots = [];
  const bookedSlots = availability.map(apt => ({
    start: apt.startTime,
    end: apt.endTime
  }));

  // Generate time slots and check availability
  for (let hour = 9; hour < 17; hour++) {
    const startTime = `${hour.toString().padStart(2, '0')}:00`;
    const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;

    const isBooked = bookedSlots.some(slot =>
      (startTime >= slot.start && startTime < slot.end) ||
      (endTime > slot.start && endTime <= slot.end) ||
      (startTime <= slot.start && endTime >= slot.end)
    );

    if (!isBooked) {
      availableSlots.push({
        startTime,
        endTime,
        available: true
      });
    }
  }

  res.status(200).json({
    success: true,
    data: {
      date,
      therapist,
      availableSlots,
      bookedSlots
    }
  });
});

// @desc    Get upcoming appointments
// @route   GET /api/v1/appointments/upcoming
// @access  Private
const getUpcomingAppointments = asyncHandler(async (req, res, next) => {
  const days = parseInt(req.query.days) || 7;
  const startDate = new Date();
  const endDate = new Date();
  endDate.setDate(startDate.getDate() + days);

  let query = {
    date: {
      $gte: startDate,
      $lte: endDate
    },
    status: { $in: ['scheduled', 'confirmed'] }
  };

  // Filter by user role
  if (req.user.role !== 'admin') {
    query.therapist = req.user._id;
  }

  const appointments = await Appointment.find(query)
    .populate('patient', 'firstName lastName nationalId phone')
    .populate('therapist', 'firstName lastName')
    .sort('date startTime')
    .limit(50);

  res.status(200).json({
    success: true,
    count: appointments.length,
    data: appointments
  });
});

// @desc    Send appointment reminder
// @route   POST /api/v1/appointments/:id/reminder
// @access  Private
const sendReminder = asyncHandler(async (req, res, next) => {
  const { type = 'email' } = req.body;

  const appointment = await Appointment.findById(req.params.id)
    .populate('patient', 'firstName lastName email phone')
    .populate('therapist', 'firstName lastName');

  if (!appointment) {
    return next(new AppError(`Appointment not found with id of ${req.params.id}`, 404));
  }

  // Send reminder (mock implementation)
  await appointment.sendReminder(type);

  logger.info(`Reminder sent for appointment: ${appointment._id} via ${type}`);

  res.status(200).json({
    success: true,
    message: `${type} reminder sent successfully`
  });
});

// @desc    Get appointment statistics
// @route   GET /api/v1/appointments/stats
// @access  Private
const getAppointmentStats = asyncHandler(async (req, res, next) => {
  const stats = await Appointment.aggregate([
    {
      $group: {
        _id: null,
        totalAppointments: { $sum: 1 },
        scheduledAppointments: {
          $sum: { $cond: [{ $eq: ['$status', 'scheduled'] }, 1, 0] }
        },
        completedAppointments: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        cancelledAppointments: {
          $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
        },
        noShowAppointments: {
          $sum: { $cond: [{ $eq: ['$status', 'no_show'] }, 1, 0] }
        }
      }
    }
  ]);

  // Appointment types distribution
  const typeStats = await Appointment.aggregate([
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 }
      }
    }
  ]);

  // Daily appointment counts for the last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const dailyStats = await Appointment.aggregate([
    {
      $match: {
        date: { $gte: thirtyDaysAgo }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$date' }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      overview: stats[0] || {
        totalAppointments: 0,
        scheduledAppointments: 0,
        completedAppointments: 0,
        cancelledAppointments: 0,
        noShowAppointments: 0
      },
      typeDistribution: typeStats,
      dailyTrends: dailyStats
    }
  });
});

module.exports = {
  getAppointments,
  getAppointment,
  createAppointment,
  updateAppointment,
  deleteAppointment,
  cancelAppointment,
  rescheduleAppointment,
  completeAppointment,
  getAppointmentsByDate,
  getAppointmentsByPatient,
  getAppointmentsByTherapist,
  checkAvailability,
  getUpcomingAppointments,
  sendReminder,
  getAppointmentStats
};
