const crypto = require('crypto');
const { async<PERSON><PERSON><PERSON>, AppError } = require('../middleware/errorHandler');
const User = require('../models/User');
const logger = require('../utils/logger');
const sendEmail = require('../utils/sendEmail');

// @desc    Register user
// @route   POST /api/v1/auth/register
// @access  Public
const register = asyncHandler(async (req, res, next) => {
  const { firstName, lastName, email, password, role, phone, specialization, licenseNumber } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return next(new AppError('User with this email already exists', 409));
  }

  // Get default permissions for role
  const permissions = User.getDefaultPermissions(role);

  // Create user
  const user = await User.create({
    firstName,
    lastName,
    email,
    password,
    role,
    phone,
    specialization,
    licenseNumber,
    permissions,
    emailVerificationToken: crypto.randomBytes(20).toString('hex')
  });

  // Send verification email
  try {
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/v1/auth/verify-email/${user.emailVerificationToken}`;
    
    await sendEmail({
      email: user.email,
      subject: 'PT System - Email Verification',
      message: `Please verify your email by clicking: ${verificationUrl}`
    });
  } catch (error) {
    logger.error('Error sending verification email:', error);
    // Don't fail registration if email fails
  }

  logger.info(`New user registered: ${user.email} (${user.role})`);

  sendTokenResponse(user, 201, res);
});

// @desc    Login user
// @route   POST /api/v1/auth/login
// @access  Public
const login = asyncHandler(async (req, res, next) => {
  const { email, password } = req.body;

  // Validate email & password
  if (!email || !password) {
    return next(new AppError('Please provide an email and password', 400));
  }

  // Check for user
  const user = await User.findOne({ email }).select('+password');

  if (!user) {
    return next(new AppError('Invalid credentials', 401));
  }

  // Check if password matches
  const isMatch = await user.correctPassword(password, user.password);

  if (!isMatch) {
    return next(new AppError('Invalid credentials', 401));
  }

  // Check if user is active
  if (!user.isActive) {
    return next(new AppError('Account is deactivated. Please contact administrator.', 401));
  }

  // Update last login
  try {
    user.lastLogin = new Date();

    // Ensure user has proper permission structure
    if (!user.permissions || Array.isArray(user.permissions)) {
      user.setDefaultPermissions();
    }

    await user.save({ validateBeforeSave: false });
  } catch (error) {
    // If save fails due to permission structure, try to fix it
    if (error.message.includes('Cannot create field')) {
      logger.warn(`Permission structure issue for user ${user.email}, attempting to fix...`);

      // Update user directly in database with proper structure
      await User.updateOne(
        { _id: user._id },
        {
          $set: {
            lastLogin: new Date(),
            permissions: user.permissions || {}
          }
        }
      );
    } else {
      throw error;
    }
  }

  logger.info(`User logged in: ${user.email} (${user.role})`);

  sendTokenResponse(user, 200, res);
});

// @desc    Log user out / clear cookie
// @route   POST /api/v1/auth/logout
// @access  Public
const logout = asyncHandler(async (req, res, next) => {
  res.cookie('token', 'none', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true,
  });

  res.status(200).json({
    success: true,
    message: 'Logged out successfully'
  });
});

// @desc    Get current logged in user
// @route   GET /api/v1/auth/me
// @access  Private
const getMe = asyncHandler(async (req, res, next) => {
  // User is already available in req.user from protect middleware
  const user = await User.findById(req.user.id);

  res.status(200).json({
    success: true,
    user
  });
});

// @desc    Update user details
// @route   PUT /api/v1/auth/updatedetails
// @access  Private
const updateDetails = asyncHandler(async (req, res, next) => {
  const fieldsToUpdate = {
    firstName: req.body.firstName,
    lastName: req.body.lastName,
    email: req.body.email,
    phone: req.body.phone
  };

  // Remove undefined fields
  Object.keys(fieldsToUpdate).forEach(key => 
    fieldsToUpdate[key] === undefined && delete fieldsToUpdate[key]
  );

  const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
    new: true,
    runValidators: true
  });

  logger.info(`User updated details: ${user.email}`);

  res.status(200).json({
    success: true,
    user
  });
});

// @desc    Update password
// @route   PUT /api/v1/auth/updatepassword
// @access  Private
const updatePassword = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.user.id).select('+password');

  // Check current password
  if (!(await user.correctPassword(req.body.currentPassword, user.password))) {
    return next(new AppError('Password is incorrect', 400));
  }

  user.password = req.body.newPassword;
  await user.save();

  logger.info(`User changed password: ${user.email}`);

  sendTokenResponse(user, 200, res);
});

// @desc    Forgot password
// @route   POST /api/v1/auth/forgotpassword
// @access  Public
const forgotPassword = asyncHandler(async (req, res, next) => {
  const user = await User.findOne({ email: req.body.email });

  if (!user) {
    return next(new AppError('There is no user with that email', 404));
  }

  // Get reset token
  const resetToken = user.getResetPasswordToken();

  await user.save({ validateBeforeSave: false });

  // Create reset url
  const resetUrl = `${req.protocol}://${req.get('host')}/api/v1/auth/resetpassword/${resetToken}`;

  const message = `You are receiving this email because you (or someone else) has requested the reset of a password. Please make a PUT request to: \n\n ${resetUrl}`;

  try {
    await sendEmail({
      email: user.email,
      subject: 'PT System - Password Reset',
      message
    });

    res.status(200).json({ 
      success: true, 
      message: 'Email sent' 
    });
  } catch (err) {
    logger.error('Error sending password reset email:', err);
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;

    await user.save({ validateBeforeSave: false });

    return next(new AppError('Email could not be sent', 500));
  }
});

// @desc    Reset password
// @route   PUT /api/v1/auth/resetpassword/:resettoken
// @access  Public
const resetPassword = asyncHandler(async (req, res, next) => {
  // Get hashed token
  const resetPasswordToken = crypto
    .createHash('sha256')
    .update(req.params.resettoken)
    .digest('hex');

  const user = await User.findOne({
    passwordResetToken: resetPasswordToken,
    passwordResetExpires: { $gt: Date.now() }
  });

  if (!user) {
    return next(new AppError('Invalid token', 400));
  }

  // Set new password
  user.password = req.body.password;
  user.passwordResetToken = undefined;
  user.passwordResetExpires = undefined;
  await user.save();

  logger.info(`User reset password: ${user.email}`);

  sendTokenResponse(user, 200, res);
});

// @desc    Refresh JWT token
// @route   POST /api/v1/auth/refresh
// @access  Private
const refreshToken = asyncHandler(async (req, res, next) => {
  const user = req.user;

  logger.info(`Token refreshed for user: ${user.email}`);

  sendTokenResponse(user, 200, res);
});

// @desc    Verify email
// @route   GET /api/v1/auth/verify-email/:token
// @access  Public
const verifyEmail = asyncHandler(async (req, res, next) => {
  const user = await User.findOne({
    emailVerificationToken: req.params.token
  });

  if (!user) {
    return next(new AppError('Invalid verification token', 400));
  }

  user.emailVerified = true;
  user.emailVerificationToken = undefined;
  await user.save({ validateBeforeSave: false });

  logger.info(`Email verified for user: ${user.email}`);

  res.status(200).json({
    success: true,
    message: 'Email verified successfully'
  });
});

// @desc    Resend email verification
// @route   POST /api/v1/auth/resend-verification
// @access  Private
const resendVerification = asyncHandler(async (req, res, next) => {
  const user = req.user;

  if (user.emailVerified) {
    return next(new AppError('Email is already verified', 400));
  }

  // Generate new verification token
  user.emailVerificationToken = crypto.randomBytes(20).toString('hex');
  await user.save({ validateBeforeSave: false });

  const verificationUrl = `${req.protocol}://${req.get('host')}/api/v1/auth/verify-email/${user.emailVerificationToken}`;

  try {
    await sendEmail({
      email: user.email,
      subject: 'PT System - Email Verification',
      message: `Please verify your email by clicking: ${verificationUrl}`
    });

    res.status(200).json({
      success: true,
      message: 'Verification email sent'
    });
  } catch (error) {
    logger.error('Error sending verification email:', error);
    return next(new AppError('Email could not be sent', 500));
  }
});

// Get token from model, create cookie and send response
const sendTokenResponse = (user, statusCode, res) => {
  // Create token
  const token = user.getSignedJwtToken();

  const options = {
    expires: new Date(
      Date.now() + process.env.JWT_COOKIE_EXPIRE * 24 * 60 * 60 * 1000
    ),
    httpOnly: true
  };

  if (process.env.NODE_ENV === 'production') {
    options.secure = true;
  }

  res
    .status(statusCode)
    .cookie('token', token, options)
    .json({
      success: true,
      token,
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        permissions: user.permissions,
        isActive: user.isActive,
        emailVerified: user.emailVerified,
        preferences: user.preferences
      }
    });
};

module.exports = {
  register,
  login,
  logout,
  getMe,
  updateDetails,
  updatePassword,
  forgotPassword,
  resetPassword,
  refreshToken,
  verifyEmail,
  resendVerification
};
