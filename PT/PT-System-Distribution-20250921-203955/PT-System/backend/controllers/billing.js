const asyncHandler = require('express-async-handler');
const Billing = require('../models/Billing');
const Payment = require('../models/Payment');
const Patient = require('../models/Patient');
const { AppError } = require('../middleware/errorHandler');

// @desc    Get all billing records
// @route   GET /api/v1/billing
// @access  Private
const getBillings = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  // Build filter object
  const filter = {};

  if (req.query.status) {
    filter.status = req.query.status;
  }

  if (req.query.patient) {
    filter.patient = req.query.patient;
  }

  if (req.query.startDate && req.query.endDate) {
    filter.issueDate = {
      $gte: new Date(req.query.startDate),
      $lte: new Date(req.query.endDate)
    };
  }

  // Search functionality
  if (req.query.search) {
    filter.$or = [
      { invoiceNumber: { $regex: req.query.search, $options: 'i' } },
      { 'services.name': { $regex: req.query.search, $options: 'i' } }
    ];
  }

  const billings = await Billing.find(filter)
    .populate('patient', 'firstName lastName nationalId phone email')
    .populate('createdBy', 'firstName lastName')
    .populate('services.provider', 'firstName lastName')
    .sort({ issueDate: -1 })
    .skip(skip)
    .limit(limit);

  const total = await Billing.countDocuments(filter);

  res.status(200).json({
    success: true,
    data: billings,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalRecords: total,
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }
  });
});

// @desc    Get single billing record
// @route   GET /api/v1/billing/:id
// @access  Private
const getBilling = asyncHandler(async (req, res) => {
  const billing = await Billing.findById(req.params.id)
    .populate('patient', 'firstName lastName nationalId phone email address')
    .populate('createdBy', 'firstName lastName')
    .populate('updatedBy', 'firstName lastName')
    .populate('services.provider', 'firstName lastName')
    .populate('payments.receivedBy', 'firstName lastName');

  if (!billing) {
    return res.status(404).json({
      success: false,
      message: 'Billing record not found'
    });
  }

  res.status(200).json({
    success: true,
    data: billing
  });
});

// @desc    Create billing record
// @route   POST /api/v1/billing
// @access  Private
const createBilling = asyncHandler(async (req, res) => {
  const {
    patient,
    services,
    dueDate,
    notes,
    discount,
    tax
  } = req.body;

  // Validate required fields
  if (!patient || !services || !Array.isArray(services) || services.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'Patient and services are required'
    });
  }

  // Validate patient exists
  const patientExists = await Patient.findById(patient);
  if (!patientExists) {
    return res.status(404).json({
      success: false,
      message: 'Patient not found'
    });
  }

  // Calculate service totals
  const processedServices = services.map(service => ({
    ...service,
    totalPrice: service.quantity * service.unitPrice
  }));

  const billingData = {
    patient,
    services: processedServices,
    dueDate: dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    notes,
    discount: discount || { type: 'percentage', value: 0, amount: 0 },
    tax: tax || { rate: 15, amount: 0 },
    createdBy: req.user.id
  };

  const billing = await Billing.create(billingData);

  // Populate the created billing record
  const populatedBilling = await Billing.findById(billing._id)
    .populate('patient', 'firstName lastName nationalId phone email')
    .populate('createdBy', 'firstName lastName');

  res.status(201).json({
    success: true,
    data: populatedBilling,
    message: 'Invoice created successfully'
  });
});

// @desc    Update billing record
// @route   PUT /api/v1/billing/:id
// @access  Private
const updateBilling = asyncHandler(async (req, res) => {
  const billing = await Billing.findById(req.params.id);

  if (!billing) {
    return res.status(404).json({
      success: false,
      message: 'Billing record not found'
    });
  }

  // Don't allow updates to paid invoices
  if (billing.status === 'paid') {
    return res.status(400).json({
      success: false,
      message: 'Cannot update paid invoices'
    });
  }

  // Update fields
  const allowedUpdates = ['services', 'dueDate', 'notes', 'discount', 'tax', 'status'];
  const updates = {};

  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  // If services are updated, recalculate totals
  if (updates.services) {
    updates.services = updates.services.map(service => ({
      ...service,
      totalPrice: service.quantity * service.unitPrice
    }));
  }

  updates.updatedBy = req.user.id;

  const updatedBilling = await Billing.findByIdAndUpdate(
    req.params.id,
    updates,
    { new: true, runValidators: true }
  ).populate('patient', 'firstName lastName nationalId phone email')
   .populate('updatedBy', 'firstName lastName');

  res.status(200).json({
    success: true,
    data: updatedBilling,
    message: 'Billing record updated successfully'
  });
});

// @desc    Delete billing record
// @route   DELETE /api/billing/:id
// @access  Private
const deleteBilling = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Get payments
// @route   GET /api/billing/payments
// @access  Private
const getPayments = asyncHandler(async (req, res) => {
  // Mock data
  const payments = [
    {
      id: '1',
      invoiceId: '1',
      amount: 250.00,
      method: 'credit_card',
      status: 'completed',
      date: new Date('2024-01-20')
    }
  ];

  res.status(200).json({
    success: true,
    data: payments
  });
});

// @desc    Create payment
// @route   POST /api/billing/payments
// @access  Private
const createPayment = asyncHandler(async (req, res) => {
  // Mock implementation
  const payment = {
    id: Date.now().toString(),
    ...req.body,
    status: 'completed',
    date: new Date()
  };

  res.status(201).json({
    success: true,
    data: payment
  });
});

// @desc    Get billing statistics
// @route   GET /api/v1/billing/stats
// @access  Private
const getBillingStats = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;

  // Default to current month if no dates provided
  const start = startDate ? new Date(startDate) : new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  const end = endDate ? new Date(endDate) : new Date();

  // Get total statistics
  const totalStats = await Billing.aggregate([
    {
      $match: {
        issueDate: { $gte: start, $lte: end },
        status: { $ne: 'cancelled' }
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$totalAmount' },
        totalPaid: { $sum: { $sum: '$payments.amount' } },
        totalInvoices: { $sum: 1 },
        avgInvoiceAmount: { $avg: '$totalAmount' }
      }
    }
  ]);

  // Get status breakdown
  const statusStats = await Billing.aggregate([
    {
      $match: {
        issueDate: { $gte: start, $lte: end }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        amount: { $sum: '$totalAmount' }
      }
    }
  ]);

  // Get monthly revenue
  const monthlyRevenue = await Billing.aggregate([
    {
      $match: {
        issueDate: { $gte: start, $lte: end },
        status: { $ne: 'cancelled' }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$issueDate' },
          month: { $month: '$issueDate' }
        },
        revenue: { $sum: '$totalAmount' },
        paid: { $sum: { $sum: '$payments.amount' } },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1 }
    }
  ]);

  // Get overdue invoices
  const overdueCount = await Billing.countDocuments({
    dueDate: { $lt: new Date() },
    status: { $nin: ['paid', 'cancelled'] }
  });

  const stats = {
    totalRevenue: totalStats[0]?.totalRevenue || 0,
    totalPaid: totalStats[0]?.totalPaid || 0,
    pendingAmount: (totalStats[0]?.totalRevenue || 0) - (totalStats[0]?.totalPaid || 0),
    totalInvoices: totalStats[0]?.totalInvoices || 0,
    avgInvoiceAmount: totalStats[0]?.avgInvoiceAmount || 0,
    overdueInvoices: overdueCount,
    statusBreakdown: statusStats,
    monthlyRevenue: monthlyRevenue.map(item => ({
      month: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`,
      revenue: item.revenue,
      paid: item.paid,
      count: item.count
    }))
  };

  res.status(200).json({
    success: true,
    data: stats
  });
});

// @desc    Generate invoice PDF
// @route   GET /api/billing/invoices/:id/pdf
// @access  Private
const generateInvoicePDF = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    message: 'PDF generated successfully',
    downloadUrl: `/downloads/invoice-${req.params.id}.pdf`
  });
});

// @desc    Send invoice email
// @route   POST /api/billing/invoices/:id/send
// @access  Private
const sendInvoiceEmail = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    message: 'Invoice sent successfully'
  });
});

// @desc    Add payment to billing
// @route   POST /api/v1/billing/:id/payment
// @access  Private
const addPayment = asyncHandler(async (req, res) => {
  const { amount, method, reference, notes } = req.body;

  const billing = await Billing.findById(req.params.id);

  if (!billing) {
    return res.status(404).json({
      success: false,
      message: 'Billing record not found'
    });
  }

  if (amount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Payment amount must be greater than 0'
    });
  }

  if (amount > billing.remainingBalance) {
    return res.status(400).json({
      success: false,
      message: 'Payment amount cannot exceed remaining balance'
    });
  }

  // Add payment to billing record
  const paymentData = {
    amount,
    method,
    reference,
    receivedBy: req.user.id,
    notes
  };

  await billing.addPayment(paymentData);

  // Create separate payment record
  const payment = await Payment.create({
    patient: billing.patient,
    billing: billing._id,
    amount,
    method,
    reference,
    notes,
    status: 'completed',
    receivedBy: req.user.id,
    createdBy: req.user.id
  });

  // Populate and return updated billing
  const updatedBilling = await Billing.findById(billing._id)
    .populate('patient', 'firstName lastName nationalId phone email')
    .populate('payments.receivedBy', 'firstName lastName');

  res.status(201).json({
    success: true,
    data: {
      billing: updatedBilling,
      payment: payment
    },
    message: 'Payment added successfully'
  });
});

// @desc    Send invoice
// @route   POST /api/v1/billing/:id/send
// @access  Private
const sendInvoice = asyncHandler(async (req, res) => {
  const billing = await Billing.findById(req.params.id)
    .populate('patient', 'firstName lastName email phone');

  if (!billing) {
    return res.status(404).json({
      success: false,
      message: 'Billing record not found'
    });
  }

  // Mark as sent
  await billing.markAsSent();

  // Here you would implement actual email sending logic
  // For now, we'll just mark it as sent

  res.status(200).json({
    success: true,
    message: 'Invoice sent successfully',
    data: billing
  });
});

// @desc    Send reminder
// @route   POST /api/v1/billing/:id/reminder
// @access  Private
const sendReminder = asyncHandler(async (req, res) => {
  const billing = await Billing.findById(req.params.id)
    .populate('patient', 'firstName lastName email phone');

  if (!billing) {
    return res.status(404).json({
      success: false,
      message: 'Billing record not found'
    });
  }

  // Send reminder
  await billing.sendReminder();

  res.status(200).json({
    success: true,
    message: 'Reminder sent successfully',
    data: billing
  });
});

// @desc    Mark invoice as paid
// @route   POST /api/v1/billing/:id/mark-paid
// @access  Private
const markAsPaid = asyncHandler(async (req, res) => {
  const { paymentMethod = 'cash', reference, notes } = req.body;

  const billing = await Billing.findById(req.params.id);

  if (!billing) {
    return res.status(404).json({
      success: false,
      message: 'Billing record not found'
    });
  }

  if (billing.status === 'paid') {
    return res.status(400).json({
      success: false,
      message: 'Invoice is already paid'
    });
  }

  // Add full payment
  const remainingAmount = billing.remainingBalance;
  await billing.addPayment({
    amount: remainingAmount,
    method: paymentMethod,
    reference,
    receivedBy: req.user.id,
    notes
  });

  // Create payment record
  await Payment.create({
    patient: billing.patient,
    billing: billing._id,
    amount: remainingAmount,
    method: paymentMethod,
    reference,
    notes,
    status: 'completed',
    receivedBy: req.user.id,
    createdBy: req.user.id
  });

  const updatedBilling = await Billing.findById(billing._id)
    .populate('patient', 'firstName lastName nationalId phone email');

  res.status(200).json({
    success: true,
    message: 'Invoice marked as paid successfully',
    data: updatedBilling
  });
});

// @desc    Get billings by patient
// @route   GET /api/billing/patient/:patientId
// @access  Private
const getBillingsByPatient = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: []
  });
});

// @desc    Get overdue billings
// @route   GET /api/billing/overdue
// @access  Private
const getOverdueBillings = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: []
  });
});

// @desc    Export billings
// @route   GET /api/billing/export
// @access  Private
const exportBillings = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    message: 'Export generated successfully',
    downloadUrl: '/downloads/billings-export.csv'
  });
});

module.exports = {
  getBillings,
  getBilling,
  createBilling,
  updateBilling,
  deleteBilling,
  getPayments,
  createPayment,
  getBillingStats,
  generateInvoicePDF,
  sendInvoiceEmail: sendInvoice,
  addPayment,
  sendInvoice,
  sendReminder,
  markAsPaid,
  getBillingsByPatient,
  getOverdueBillings,
  exportBillings
};
