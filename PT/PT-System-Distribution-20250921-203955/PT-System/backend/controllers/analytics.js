const { asyncHandler, AppError } = require('../middleware/errorHandler');
const Patient = require('../models/Patient');
const Appointment = require('../models/Appointment');
const TreatmentPlan = require('../models/TreatmentPlan');
const Billing = require('../models/Billing');
const Insurance = require('../models/Insurance');
const User = require('../models/User');
const logger = require('../utils/logger');
const Payment = require('../models/Payment');

// @desc    Get dashboard statistics
// @route   GET /api/v1/analytics/dashboard
// @access  Private
const getDashboardStats = asyncHandler(async (req, res, next) => {
  const { period = 'month', dateFrom, dateTo } = req.query;
  
  // Calculate date range
  let startDate, endDate;
  if (dateFrom && dateTo) {
    startDate = new Date(dateFrom);
    endDate = new Date(dateTo);
  } else {
    endDate = new Date();
    startDate = new Date();
    
    switch (period) {
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case 'quarter':
        startDate.setMonth(endDate.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
    }
  }

  // Get overview statistics
  const [
    totalPatients,
    activePatients,
    specialNeedsPatients,
    totalAppointments,
    completedAppointments,
    totalRevenue,
    pendingPayments
  ] = await Promise.all([
    Patient.countDocuments(),
    Patient.countDocuments({ status: 'active' }),
    Patient.countDocuments({ 'specialNeeds.hasSpecialNeeds': true }),
    Appointment.countDocuments({
      date: { $gte: startDate, $lte: endDate }
    }),
    Appointment.countDocuments({
      date: { $gte: startDate, $lte: endDate },
      status: 'completed'
    }),
    Billing.aggregate([
      {
        $match: {
          issueDate: { $gte: startDate, $lte: endDate },
          status: { $ne: 'cancelled' }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$totalAmount' }
        }
      }
    ]),
    Billing.aggregate([
      {
        $match: {
          status: { $in: ['sent', 'overdue'] }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$remainingBalance' }
        }
      }
    ])
  ]);

  // Get daily trends for the period
  const dailyTrends = await Appointment.aggregate([
    {
      $match: {
        date: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$date' }
        },
        appointments: { $sum: 1 },
        completed: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);

  // Get patient demographics
  const demographics = await Patient.aggregate([
    {
      $group: {
        _id: '$gender',
        count: { $sum: 1 }
      }
    }
  ]);

  // Get treatment type distribution
  const treatmentTypes = await TreatmentPlan.aggregate([
    {
      $group: {
        _id: '$treatmentType',
        count: { $sum: 1 }
      }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      overview: {
        totalPatients,
        activePatients,
        specialNeedsPatients,
        totalAppointments,
        completedAppointments,
        completionRate: totalAppointments > 0 ? Math.round((completedAppointments / totalAppointments) * 100) : 0,
        totalRevenue: totalRevenue[0]?.total || 0,
        pendingPayments: pendingPayments[0]?.total || 0
      },
      trends: {
        daily: dailyTrends,
        period: period
      },
      charts: {
        demographics,
        treatmentTypes
      }
    }
  });
});

// @desc    Get patient analytics
// @route   GET /api/v1/analytics/patients
// @access  Private
const getPatientAnalytics = asyncHandler(async (req, res, next) => {
  const { period = 'month', groupBy = 'age' } = req.query;
  
  let groupStage = {};
  
  switch (groupBy) {
    case 'age':
      groupStage = {
        $group: {
          _id: {
            $switch: {
              branches: [
                { case: { $lt: ['$age', 18] }, then: 'Child (0-17)' },
                { case: { $lt: ['$age', 65] }, then: 'Adult (18-64)' },
                { case: { $gte: ['$age', 65] }, then: 'Senior (65+)' }
              ],
              default: 'Unknown'
            }
          },
          count: { $sum: 1 }
        }
      };
      break;
    case 'gender':
      groupStage = {
        $group: {
          _id: '$gender',
          count: { $sum: 1 }
        }
      };
      break;
    case 'condition':
      groupStage = {
        $group: {
          _id: '$medicalHistory.primaryDiagnosis',
          count: { $sum: 1 }
        }
      };
      break;
    case 'therapist':
      groupStage = {
        $group: {
          _id: '$primaryTherapist',
          count: { $sum: 1 }
        }
      };
      break;
  }

  const analytics = await Patient.aggregate([
    groupStage,
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'therapist'
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  // Get registration trends
  const registrationTrends = await Patient.aggregate([
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m', date: '$registrationDate' }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      distribution: analytics,
      trends: registrationTrends,
      groupBy: groupBy
    }
  });
});

// @desc    Get treatment analytics
// @route   GET /api/v1/analytics/treatments
// @access  Private
const getTreatmentAnalytics = asyncHandler(async (req, res, next) => {
  const { treatmentType, therapist } = req.query;
  
  let matchStage = { status: 'active' };
  if (treatmentType) matchStage.treatmentType = treatmentType;
  if (therapist) matchStage.therapist = therapist;

  // Get treatment plan statistics
  const treatmentStats = await TreatmentPlan.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$treatmentType',
        totalPlans: { $sum: 1 },
        completedPlans: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        averageGoals: { $avg: { $size: '$goals' } },
        averageDuration: { $avg: '$actualDuration' }
      }
    }
  ]);

  // Get goal achievement rates
  const goalStats = await TreatmentPlan.aggregate([
    { $match: matchStage },
    { $unwind: '$goals' },
    {
      $group: {
        _id: '$goals.status',
        count: { $sum: 1 }
      }
    }
  ]);

  // Get progress trends
  const progressTrends = await TreatmentPlan.aggregate([
    { $match: matchStage },
    { $unwind: '$progress' },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m', date: '$progress.date' }
        },
        sessions: { $sum: 1 },
        averagePainBefore: { $avg: '$progress.painLevel.before' },
        averagePainAfter: { $avg: '$progress.painLevel.after' }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      overview: treatmentStats,
      goalAchievement: goalStats,
      progressTrends: progressTrends
    }
  });
});

// @desc    Get financial analytics
// @route   GET /api/v1/analytics/financial
// @access  Private
const getFinancialAnalytics = asyncHandler(async (req, res, next) => {
  const { period = 'month', breakdown = 'service' } = req.query;
  
  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case 'month':
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case 'quarter':
      startDate.setMonth(endDate.getMonth() - 3);
      break;
    case 'year':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
  }

  // Get revenue statistics
  const revenueStats = await Billing.aggregate([
    {
      $match: {
        issueDate: { $gte: startDate, $lte: endDate },
        status: { $ne: 'cancelled' }
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$totalAmount' },
        totalPaid: { $sum: { $sum: '$payments.amount' } },
        totalOutstanding: { $sum: '$remainingBalance' },
        invoiceCount: { $sum: 1 }
      }
    }
  ]);

  // Get revenue breakdown
  let breakdownPipeline = [];
  switch (breakdown) {
    case 'service':
      breakdownPipeline = [
        { $unwind: '$services' },
        {
          $group: {
            _id: '$services.name',
            revenue: { $sum: '$services.totalPrice' },
            count: { $sum: 1 }
          }
        }
      ];
      break;
    case 'therapist':
      breakdownPipeline = [
        { $unwind: '$services' },
        {
          $group: {
            _id: '$services.provider',
            revenue: { $sum: '$services.totalPrice' },
            count: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'users',
            localField: '_id',
            foreignField: '_id',
            as: 'provider'
          }
        }
      ];
      break;
    case 'insurance':
      breakdownPipeline = [
        {
          $group: {
            _id: '$insurance.provider',
            revenue: { $sum: '$insurance.coveredAmount' },
            count: { $sum: 1 }
          }
        }
      ];
      break;
    case 'payment_method':
      breakdownPipeline = [
        { $unwind: '$payments' },
        {
          $group: {
            _id: '$payments.method',
            amount: { $sum: '$payments.amount' },
            count: { $sum: 1 }
          }
        }
      ];
      break;
  }

  const revenueBreakdown = await Billing.aggregate([
    {
      $match: {
        issueDate: { $gte: startDate, $lte: endDate },
        status: { $ne: 'cancelled' }
      }
    },
    ...breakdownPipeline,
    {
      $sort: { revenue: -1 }
    }
  ]);

  // Get monthly revenue trends
  const monthlyTrends = await Billing.aggregate([
    {
      $match: {
        issueDate: { $gte: startDate, $lte: endDate },
        status: { $ne: 'cancelled' }
      }
    },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m', date: '$issueDate' }
        },
        revenue: { $sum: '$totalAmount' },
        paid: { $sum: { $sum: '$payments.amount' } },
        invoices: { $sum: 1 }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      overview: revenueStats[0] || {
        totalRevenue: 0,
        totalPaid: 0,
        totalOutstanding: 0,
        invoiceCount: 0
      },
      breakdown: revenueBreakdown,
      trends: monthlyTrends,
      period: period,
      breakdownType: breakdown
    }
  });
});

// @desc    Get appointment analytics
// @route   GET /api/v1/analytics/appointments
// @access  Private
const getAppointmentAnalytics = asyncHandler(async (req, res, next) => {
  const { period = 'month', therapist } = req.query;
  
  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case 'week':
      startDate.setDate(endDate.getDate() - 7);
      break;
    case 'month':
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case 'quarter':
      startDate.setMonth(endDate.getMonth() - 3);
      break;
  }

  let matchStage = {
    date: { $gte: startDate, $lte: endDate }
  };
  
  if (therapist) {
    matchStage.therapist = therapist;
  }

  // Get appointment statistics
  const appointmentStats = await Appointment.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);

  // Get utilization by therapist
  const utilizationStats = await Appointment.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: '$therapist',
        totalAppointments: { $sum: 1 },
        completedAppointments: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        cancelledAppointments: {
          $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
        },
        noShowAppointments: {
          $sum: { $cond: [{ $eq: ['$status', 'no_show'] }, 1, 0] }
        }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'therapist'
      }
    },
    {
      $addFields: {
        utilizationRate: {
          $multiply: [
            { $divide: ['$completedAppointments', '$totalAppointments'] },
            100
          ]
        }
      }
    }
  ]);

  // Get daily appointment trends
  const dailyTrends = await Appointment.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: {
          $dateToString: { format: '%Y-%m-%d', date: '$date' }
        },
        scheduled: { $sum: 1 },
        completed: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        cancelled: {
          $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
        }
      }
    },
    {
      $sort: { '_id': 1 }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      overview: appointmentStats,
      utilization: utilizationStats,
      trends: dailyTrends,
      period: period
    }
  });
});

// @desc    Get special needs analytics
// @route   GET /api/analytics/special-needs
// @access  Private
const getSpecialNeedsAnalytics = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      totalSpecialNeedsPatients: 25,
      adaptiveTreatments: 18,
      successRate: 85.5
    }
  });
});

// @desc    Get performance metrics
// @route   GET /api/analytics/performance
// @access  Private
const getPerformanceMetrics = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      efficiency: 92.3,
      patientSatisfaction: 4.7,
      treatmentSuccess: 88.2
    }
  });
});

// @desc    Get custom report
// @route   POST /api/analytics/custom
// @access  Private
const getCustomReport = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: { message: 'Custom report generated' }
  });
});

// @desc    Export analytics
// @route   GET /api/analytics/export
// @access  Private
const exportAnalytics = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: { downloadUrl: '/downloads/analytics-export.csv' }
  });
});

// @desc    Get revenue report
// @route   GET /api/analytics/revenue
// @access  Private
const getRevenueReport = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: { totalRevenue: 125000, monthlyGrowth: 12.5 }
  });
});

// @desc    Get patient demographics
// @route   GET /api/analytics/demographics
// @access  Private
const getPatientDemographics = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      ageGroups: [
        { range: '18-30', count: 45 },
        { range: '31-50', count: 78 },
        { range: '51-70', count: 92 }
      ]
    }
  });
});

// @desc    Get treatment outcomes
// @route   GET /api/analytics/outcomes
// @access  Private
const getTreatmentOutcomes = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: { successRate: 87.5, averageSessionCount: 12 }
  });
});

// @desc    Get provider performance
// @route   GET /api/analytics/providers
// @access  Private
const getProviderPerformance = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: [
      { name: 'Dr. Smith', efficiency: 95.2, satisfaction: 4.8 },
      { name: 'Dr. Johnson', efficiency: 88.7, satisfaction: 4.6 }
    ]
  });
});

// @desc    Get insurance analytics
// @route   GET /api/analytics/insurance
// @access  Private
const getInsuranceAnalytics = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: { claimApprovalRate: 92.3, averageReimbursement: 185.50 }
  });
});

// @desc    Get compliance report
// @route   GET /api/analytics/compliance
// @access  Private
const getComplianceReport = asyncHandler(async (req, res) => {
  res.status(200).json({
    success: true,
    data: { carfCompliance: 98.5, cbahiCompliance: 96.8 }
  });
});

// @desc    Get invoice analytics
// @route   GET /api/v1/analytics/invoice/:id
// @access  Private
const getInvoiceAnalytics = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    // Get the invoice
    const invoice = await Billing.findById(id).populate('patient', 'firstName lastName');

    if (!invoice) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    // Get payment history for this invoice
    const payments = await Payment.find({ billing: id })
      .populate('receivedBy', 'firstName lastName')
      .sort({ createdAt: -1 });

    // Get related invoices for the same patient
    const relatedInvoices = await Billing.find({
      patient: invoice.patient._id,
      _id: { $ne: id }
    }).countDocuments();

    // Calculate patient total spent
    const patientBillings = await Billing.find({ patient: invoice.patient._id });
    const patientTotalSpent = patientBillings.reduce((sum, bill) => sum + bill.total, 0);

    // Mock analytics data (in real implementation, you'd track these)
    const analytics = {
      viewCount: Math.floor(Math.random() * 20) + 1,
      emailsSent: Math.floor(Math.random() * 5),
      lastViewed: new Date(),
      paymentHistory: payments.map(payment => ({
        date: payment.createdAt,
        amount: payment.amount,
        method: payment.method
      })),
      relatedInvoices,
      patientTotalSpent,
      invoiceAge: Math.floor((new Date() - new Date(invoice.createdAt)) / (1000 * 60 * 60 * 24)),
      paymentProgress: payments.reduce((sum, payment) => sum + payment.amount, 0) / invoice.total * 100,
      daysUntilDue: Math.floor((new Date(invoice.dueDate) - new Date()) / (1000 * 60 * 60 * 24)),
      isOverdue: new Date(invoice.dueDate) < new Date()
    };

    res.status(200).json({
      success: true,
      data: analytics
    });

  } catch (error) {
    logger.error('Error getting invoice analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Error retrieving invoice analytics'
    });
  }
});

module.exports = {
  getDashboardStats,
  getPatientAnalytics,
  getTreatmentAnalytics,
  getFinancialAnalytics,
  getAppointmentAnalytics,
  getSpecialNeedsAnalytics,
  getPerformanceMetrics,
  getCustomReport,
  exportAnalytics,
  getRevenueReport,
  getPatientDemographics,
  getTreatmentOutcomes,
  getProviderPerformance,
  getInsuranceAnalytics,
  getComplianceReport,
  getInvoiceAnalytics
};
