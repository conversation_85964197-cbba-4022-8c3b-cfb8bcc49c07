const { async<PERSON>and<PERSON>, AppError } = require('../middleware/errorHandler');
const Patient = require('../models/Patient');
const logger = require('../utils/logger');

// @desc    Get all patients
// @route   GET /api/v1/patients
// @access  Private
const getPatients = asyncHandler(async (req, res, next) => {
  // Build query
  let query = {};
  
  // Copy req.query
  const reqQuery = { ...req.query };
  
  // Fields to exclude
  const removeFields = ['select', 'sort', 'page', 'limit', '_t'];
  
  // Loop over removeFields and delete them from reqQuery
  removeFields.forEach(param => delete reqQuery[param]);
  
  // Create query string
  let queryStr = JSON.stringify(reqQuery);
  
  // Create operators ($gt, $gte, etc)
  queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);
  
  // Finding resource
  query = Patient.find(JSON.parse(queryStr));
  
  // Select Fields
  if (req.query.select) {
    const fields = req.query.select.split(',').join(' ');
    query = query.select(fields);
  }
  
  // Sort
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }
  
  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 25;
  const startIndex = (page - 1) * limit;
  const endIndex = page * limit;
  const total = await Patient.countDocuments(JSON.parse(queryStr));
  
  query = query.skip(startIndex).limit(limit);
  
  // Populate references
  query = query.populate('primaryTherapist', 'firstName lastName specialization')
                 .populate('assignedDoctor', 'firstName lastName specialization')
                 .populate('createdBy', 'firstName lastName');
  
  // Filter by user role and permissions
  if (req.user.role !== 'admin') {
    // Non-admin users can only see patients they're assigned to or created
    query = query.find({
      $or: [
        { primaryTherapist: req.user._id },
        { assignedDoctor: req.user._id },
        { createdBy: req.user._id }
      ]
    });
  }
  
  // Execute query
  const patients = await query;

  // Pagination result
  const pagination = {};
  
  if (endIndex < total) {
    pagination.next = {
      page: page + 1,
      limit
    };
  }
  
  if (startIndex > 0) {
    pagination.prev = {
      page: page - 1,
      limit
    };
  }
  
  res.status(200).json({
    success: true,
    count: patients.length,
    pagination,
    data: patients
  });
});

// @desc    Get single patient
// @route   GET /api/v1/patients/:id
// @access  Private
const getPatient = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id)
    .populate('primaryTherapist', 'firstName lastName specialization phone email')
    .populate('assignedDoctor', 'firstName lastName specialization phone email')
    .populate('createdBy', 'firstName lastName role')
    .populate('updatedBy', 'firstName lastName role');

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: patient
  });
});

// @desc    Create new patient
// @route   POST /api/v1/patients
// @access  Private
const createPatient = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.createdBy = req.user.id;

  // Check if patient with this national ID already exists
  const existingPatient = await Patient.findOne({ nationalId: req.body.nationalId });
  if (existingPatient) {
    return next(new AppError('Patient with this National ID already exists', 409));
  }

  const patient = await Patient.create(req.body);

  // Populate references for response
  await patient.populate('primaryTherapist', 'firstName lastName specialization');
  await patient.populate('assignedDoctor', 'firstName lastName specialization');
  await patient.populate('createdBy', 'firstName lastName role');

  logger.info(`New patient created: ${patient.nationalId} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: patient
  });
});

// @desc    Update patient
// @route   PUT /api/v1/patients/:id
// @access  Private
const updatePatient = asyncHandler(async (req, res, next) => {
  // Add user to req.body
  req.body.updatedBy = req.user.id;

  let patient = await Patient.findById(req.params.id);

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  // Check if national ID is being changed and if it conflicts
  if (req.body.nationalId && req.body.nationalId !== patient.nationalId) {
    const existingPatient = await Patient.findOne({ nationalId: req.body.nationalId });
    if (existingPatient) {
      return next(new AppError('Patient with this National ID already exists', 409));
    }
  }

  patient = await Patient.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  }).populate('primaryTherapist', 'firstName lastName specialization')
   .populate('assignedDoctor', 'firstName lastName specialization')
   .populate('updatedBy', 'firstName lastName role');

  logger.info(`Patient updated: ${patient.nationalId} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: patient
  });
});

// @desc    Delete patient
// @route   DELETE /api/v1/patients/:id
// @access  Private (Admin only)
const deletePatient = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id);

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  // Soft delete - change status to inactive instead of removing
  patient.status = 'inactive';
  patient.updatedBy = req.user.id;
  await patient.save();

  logger.info(`Patient soft deleted: ${patient.nationalId} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Patient deactivated successfully'
  });
});

// @desc    Search patients
// @route   GET /api/v1/patients/search
// @access  Private
const searchPatients = asyncHandler(async (req, res, next) => {
  const { q, fields } = req.query;

  if (!q) {
    return next(new AppError('Search query is required', 400));
  }

  // Default search fields
  const searchFields = fields ? fields.split(',') : ['firstName', 'lastName', 'nationalId', 'phone', 'email'];
  
  // Build search query
  const searchQuery = {
    $or: searchFields.map(field => ({
      [field]: { $regex: q, $options: 'i' }
    }))
  };

  // Filter by user permissions
  if (req.user.role !== 'admin') {
    searchQuery.$and = [{
      $or: [
        { primaryTherapist: req.user._id },
        { assignedDoctor: req.user._id },
        { createdBy: req.user._id }
      ]
    }];
  }

  const patients = await Patient.find(searchQuery)
    .populate('primaryTherapist', 'firstName lastName specialization')
    .populate('assignedDoctor', 'firstName lastName specialization')
    .limit(50)
    .sort('firstName lastName');

  res.status(200).json({
    success: true,
    count: patients.length,
    data: patients
  });
});

// @desc    Get patients by therapist
// @route   GET /api/v1/patients/therapist/:therapistId
// @access  Private
const getPatientsByTherapist = asyncHandler(async (req, res, next) => {
  const { therapistId } = req.params;

  // Check if user can access this therapist's patients
  if (req.user.role !== 'admin' && req.user._id.toString() !== therapistId) {
    return next(new AppError('Not authorized to access this therapist\'s patients', 403));
  }

  const patients = await Patient.getByTherapist(therapistId);

  res.status(200).json({
    success: true,
    count: patients.length,
    data: patients
  });
});

// @desc    Get special needs patients
// @route   GET /api/v1/patients/special-needs
// @access  Private
const getSpecialNeedsPatients = asyncHandler(async (req, res, next) => {
  let query = Patient.getSpecialNeedsPatients();

  // Filter by user permissions
  if (req.user.role !== 'admin') {
    query = query.find({
      $or: [
        { primaryTherapist: req.user._id },
        { assignedDoctor: req.user._id },
        { createdBy: req.user._id }
      ]
    });
  }

  const patients = await query.populate('primaryTherapist', 'firstName lastName specialization');

  res.status(200).json({
    success: true,
    count: patients.length,
    data: patients
  });
});

// @desc    Get patient statistics
// @route   GET /api/v1/patients/stats
// @access  Private
const getPatientStats = asyncHandler(async (req, res, next) => {
  const stats = await Patient.aggregate([
    {
      $group: {
        _id: null,
        totalPatients: { $sum: 1 },
        activePatients: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        specialNeedsPatients: {
          $sum: { $cond: ['$specialNeeds.hasSpecialNeeds', 1, 0] }
        },
        averageAge: { $avg: '$age' }
      }
    },
    {
      $project: {
        _id: 0,
        totalPatients: 1,
        activePatients: 1,
        specialNeedsPatients: 1,
        averageAge: { $round: ['$averageAge', 1] }
      }
    }
  ]);

  // Gender distribution
  const genderStats = await Patient.aggregate([
    {
      $group: {
        _id: '$gender',
        count: { $sum: 1 }
      }
    }
  ]);

  // Age groups
  const ageGroups = await Patient.aggregate([
    {
      $addFields: {
        ageGroup: {
          $switch: {
            branches: [
              { case: { $lt: ['$age', 18] }, then: 'Child (0-17)' },
              { case: { $lt: ['$age', 65] }, then: 'Adult (18-64)' },
              { case: { $gte: ['$age', 65] }, then: 'Senior (65+)' }
            ],
            default: 'Unknown'
          }
        }
      }
    },
    {
      $group: {
        _id: '$ageGroup',
        count: { $sum: 1 }
      }
    }
  ]);

  res.status(200).json({
    success: true,
    data: {
      overview: stats[0] || {
        totalPatients: 0,
        activePatients: 0,
        specialNeedsPatients: 0,
        averageAge: 0
      },
      genderDistribution: genderStats,
      ageGroups: ageGroups
    }
  });
});

// @desc    Upload patient document
// @route   POST /api/v1/patients/:id/documents
// @access  Private
const uploadPatientDocument = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id);

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  // File upload logic would go here
  // For now, we'll simulate document upload
  const document = {
    name: req.body.name || 'Document',
    type: req.body.type || 'other',
    url: '/uploads/documents/sample.pdf', // This would be the actual uploaded file URL
    uploadDate: new Date(),
    uploadedBy: req.user.id
  };

  patient.documents.push(document);
  await patient.save();

  logger.info(`Document uploaded for patient: ${patient.nationalId} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: document
  });
});

// @desc    Get patient documents
// @route   GET /api/v1/patients/:id/documents
// @access  Private
const getPatientDocuments = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id)
    .populate('documents.uploadedBy', 'firstName lastName role');

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    count: patient.documents.length,
    data: patient.documents
  });
});

// @desc    Delete patient document
// @route   DELETE /api/v1/patients/:id/documents/:documentId
// @access  Private
const deletePatientDocument = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id);

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  const document = patient.documents.id(req.params.documentId);
  if (!document) {
    return next(new AppError('Document not found', 404));
  }

  document.remove();
  await patient.save();

  logger.info(`Document deleted for patient: ${patient.nationalId} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    message: 'Document deleted successfully'
  });
});

// @desc    Get patient medical history
// @route   GET /api/v1/patients/:id/medical-history
// @access  Private
const getPatientMedicalHistory = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id).select('medicalHistory');

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: patient.medicalHistory
  });
});

// @desc    Update patient medical history
// @route   PUT /api/v1/patients/:id/medical-history
// @access  Private
const updatePatientMedicalHistory = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id);

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  patient.medicalHistory = { ...patient.medicalHistory, ...req.body };
  patient.updatedBy = req.user.id;
  await patient.save();

  logger.info(`Medical history updated for patient: ${patient.nationalId} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: patient.medicalHistory
  });
});

// @desc    Get patient special needs assessment
// @route   GET /api/v1/patients/:id/special-needs
// @access  Private
const getPatientSpecialNeeds = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id).select('specialNeeds');

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  res.status(200).json({
    success: true,
    data: patient.specialNeeds
  });
});

// @desc    Update patient special needs assessment
// @route   PUT /api/v1/patients/:id/special-needs
// @access  Private
const updatePatientSpecialNeeds = asyncHandler(async (req, res, next) => {
  const patient = await Patient.findById(req.params.id);

  if (!patient) {
    return next(new AppError(`Patient not found with id of ${req.params.id}`, 404));
  }

  patient.specialNeeds = { ...patient.specialNeeds, ...req.body };
  patient.updatedBy = req.user.id;
  await patient.save();

  logger.info(`Special needs assessment updated for patient: ${patient.nationalId} by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: patient.specialNeeds
  });
});

// @desc    Export patients data
// @route   GET /api/v1/patients/export
// @access  Private
const exportPatients = asyncHandler(async (req, res, next) => {
  const { format = 'csv' } = req.query;

  // Build query based on user permissions
  let query = {};
  if (req.user.role !== 'admin') {
    query = {
      $or: [
        { primaryTherapist: req.user._id },
        { assignedDoctor: req.user._id },
        { createdBy: req.user._id }
      ]
    };
  }

  const patients = await Patient.find(query)
    .populate('primaryTherapist', 'firstName lastName')
    .populate('assignedDoctor', 'firstName lastName')
    .select('-documents -medicalHistory.notes -specialNeeds.behavioralNotes');

  // For now, return JSON data
  // In a real implementation, you would generate CSV, Excel, or PDF files
  res.status(200).json({
    success: true,
    format: format,
    count: patients.length,
    data: patients,
    exportedAt: new Date().toISOString(),
    exportedBy: req.user.id
  });
});

module.exports = {
  getPatients,
  getPatient,
  createPatient,
  updatePatient,
  deletePatient,
  uploadPatientDocument,
  getPatientDocuments,
  deletePatientDocument,
  getPatientMedicalHistory,
  updatePatientMedicalHistory,
  getPatientSpecialNeeds,
  updatePatientSpecialNeeds,
  getPatientsByTherapist,
  getSpecialNeedsPatients,
  searchPatients,
  getPatientStats,
  exportPatients
};
