const asyncHandler = require('express-async-handler');
const Payment = require('../models/Payment');
const Billing = require('../models/Billing');
const Patient = require('../models/Patient');
const { AppError } = require('../middleware/errorHandler');

// @desc    Get all payments
// @route   GET /api/v1/payments
// @access  Private
const getPayments = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Build filter object
  const filter = {};
  
  if (req.query.status) {
    filter.status = req.query.status;
  }
  
  if (req.query.method) {
    filter.method = req.query.method;
  }
  
  if (req.query.patient) {
    filter.patient = req.query.patient;
  }
  
  if (req.query.startDate && req.query.endDate) {
    filter.paymentDate = {
      $gte: new Date(req.query.startDate),
      $lte: new Date(req.query.endDate)
    };
  }
  
  // Search functionality
  if (req.query.search) {
    filter.$or = [
      { transactionId: { $regex: req.query.search, $options: 'i' } },
      { reference: { $regex: req.query.search, $options: 'i' } }
    ];
  }
  
  const payments = await Payment.find(filter)
    .populate('patient', 'firstName lastName nationalId phone email')
    .populate('billing', 'invoiceNumber totalAmount')
    .populate('receivedBy', 'firstName lastName')
    .populate('processedBy', 'firstName lastName')
    .sort({ paymentDate: -1 })
    .skip(skip)
    .limit(limit);
  
  const total = await Payment.countDocuments(filter);
  
  res.status(200).json({
    success: true,
    data: payments,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalRecords: total,
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }
  });
});

// @desc    Get single payment
// @route   GET /api/v1/payments/:id
// @access  Private
const getPayment = asyncHandler(async (req, res) => {
  const payment = await Payment.findById(req.params.id)
    .populate('patient', 'firstName lastName nationalId phone email address')
    .populate('billing', 'invoiceNumber totalAmount services')
    .populate('receivedBy', 'firstName lastName')
    .populate('processedBy', 'firstName lastName')
    .populate('createdBy', 'firstName lastName');
  
  if (!payment) {
    return res.status(404).json({
      success: false,
      message: 'Payment not found'
    });
  }

  res.status(200).json({
    success: true,
    data: payment
  });
});

// @desc    Create payment
// @route   POST /api/v1/payments
// @access  Private
const createPayment = asyncHandler(async (req, res) => {
  const {
    patient,
    billing,
    amount,
    method,
    reference,
    cardDetails,
    bankDetails,
    insuranceDetails,
    notes
  } = req.body;

  // Validate required fields
  if (!patient || !amount || !method) {
    return res.status(400).json({
      success: false,
      message: 'Patient, amount, and payment method are required'
    });
  }

  // Validate patient exists
  const patientExists = await Patient.findById(patient);
  if (!patientExists) {
    return res.status(404).json({
      success: false,
      message: 'Patient not found'
    });
  }

  // If billing is provided, validate it exists and check remaining balance
  let billingRecord = null;
  if (billing) {
    billingRecord = await Billing.findById(billing);
    if (!billingRecord) {
      return res.status(404).json({
        success: false,
        message: 'Billing record not found'
      });
    }

    if (amount > billingRecord.remainingBalance) {
      return res.status(400).json({
        success: false,
        message: 'Payment amount cannot exceed remaining balance'
      });
    }
  }

  const paymentData = {
    patient,
    billing,
    amount,
    method,
    reference,
    cardDetails,
    bankDetails,
    insuranceDetails,
    notes,
    receivedBy: req.user.id,
    createdBy: req.user.id,
    status: 'completed' // Auto-complete for now
  };

  const payment = await Payment.create(paymentData);

  // If this payment is for a specific billing, add it to the billing record
  if (billingRecord) {
    await billingRecord.addPayment({
      amount,
      method,
      reference,
      receivedBy: req.user.id,
      notes
    });
  }
  
  // Populate the created payment record
  const populatedPayment = await Payment.findById(payment._id)
    .populate('patient', 'firstName lastName nationalId phone email')
    .populate('billing', 'invoiceNumber totalAmount')
    .populate('receivedBy', 'firstName lastName');

  res.status(201).json({
    success: true,
    data: populatedPayment,
    message: 'Payment recorded successfully'
  });
});

// @desc    Update payment
// @route   PUT /api/v1/payments/:id
// @access  Private
const updatePayment = asyncHandler(async (req, res) => {
  const payment = await Payment.findById(req.params.id);
  
  if (!payment) {
    return res.status(404).json({
      success: false,
      message: 'Payment not found'
    });
  }

  // Don't allow updates to completed payments (except status changes)
  if (payment.status === 'completed' && req.body.status !== 'refunded') {
    return res.status(400).json({
      success: false,
      message: 'Cannot update completed payments'
    });
  }

  // Update allowed fields
  const allowedUpdates = ['status', 'notes', 'reference'];
  const updates = {};
  
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      updates[field] = req.body[field];
    }
  });

  updates.updatedBy = req.user.id;

  const updatedPayment = await Payment.findByIdAndUpdate(
    req.params.id,
    updates,
    { new: true, runValidators: true }
  ).populate('patient', 'firstName lastName nationalId phone email')
   .populate('billing', 'invoiceNumber totalAmount')
   .populate('updatedBy', 'firstName lastName');

  res.status(200).json({
    success: true,
    data: updatedPayment,
    message: 'Payment updated successfully'
  });
});

// @desc    Process refund
// @route   POST /api/v1/payments/:id/refund
// @access  Private
const processRefund = asyncHandler(async (req, res) => {
  const { amount, reason } = req.body;
  
  const payment = await Payment.findById(req.params.id);
  
  if (!payment) {
    return res.status(404).json({
      success: false,
      message: 'Payment not found'
    });
  }

  if (payment.status !== 'completed') {
    return res.status(400).json({
      success: false,
      message: 'Can only refund completed payments'
    });
  }

  if (!amount || amount <= 0) {
    return res.status(400).json({
      success: false,
      message: 'Refund amount must be greater than 0'
    });
  }

  if (amount > payment.amount) {
    return res.status(400).json({
      success: false,
      message: 'Refund amount cannot exceed payment amount'
    });
  }

  await payment.processRefund(amount, reason, req.user.id);

  const updatedPayment = await Payment.findById(payment._id)
    .populate('patient', 'firstName lastName nationalId phone email')
    .populate('billing', 'invoiceNumber totalAmount')
    .populate('refund.processedBy', 'firstName lastName');

  res.status(200).json({
    success: true,
    data: updatedPayment,
    message: 'Refund processed successfully'
  });
});

// @desc    Get payment statistics
// @route   GET /api/v1/payments/stats
// @access  Private
const getPaymentStats = asyncHandler(async (req, res) => {
  const { startDate, endDate } = req.query;
  
  // Default to current month if no dates provided
  const start = startDate ? new Date(startDate) : new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  const end = endDate ? new Date(endDate) : new Date();

  // Get payment method breakdown
  const methodStats = await Payment.getPaymentsByPeriod(start, end);

  // Get daily revenue
  const dailyRevenue = await Payment.getRevenueByPeriod(start, end);

  // Get total statistics
  const totalStats = await Payment.aggregate([
    {
      $match: {
        paymentDate: { $gte: start, $lte: end },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        totalAmount: { $sum: '$amount' },
        totalRefunded: { $sum: '$refund.amount' },
        totalPayments: { $sum: 1 },
        avgPaymentAmount: { $avg: '$amount' }
      }
    }
  ]);

  const stats = {
    totalAmount: totalStats[0]?.totalAmount || 0,
    totalRefunded: totalStats[0]?.totalRefunded || 0,
    netAmount: (totalStats[0]?.totalAmount || 0) - (totalStats[0]?.totalRefunded || 0),
    totalPayments: totalStats[0]?.totalPayments || 0,
    avgPaymentAmount: totalStats[0]?.avgPaymentAmount || 0,
    methodBreakdown: methodStats,
    dailyRevenue: dailyRevenue
  };

  res.status(200).json({
    success: true,
    data: stats
  });
});

module.exports = {
  getPayments,
  getPayment,
  createPayment,
  updatePayment,
  processRefund,
  getPaymentStats
};
