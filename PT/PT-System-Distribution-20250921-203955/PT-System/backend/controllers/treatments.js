const Treatment = require('../models/TreatmentPlan');
const Patient = require('../models/Patient');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// @desc    Create new treatment plan
// @route   POST /api/treatments
// @access  Private (Therapist+)
const createTreatmentPlan = asyncHandler(async (req, res) => {
  const {
    patientId,
    diagnosis,
    goals,
    interventions,
    frequency,
    duration,
    notes,
    priority
  } = req.body;

  // Verify patient exists
  const patient = await Patient.findById(patientId);
  if (!patient) {
    throw new AppError('Patient not found', 404);
  }

  const treatment = await Treatment.create({
    patientId,
    therapistId: req.user.id,
    diagnosis,
    goals,
    interventions,
    frequency,
    duration,
    notes,
    priority,
    status: 'active'
  });

  await treatment.populate('patientId', 'name nameAr age gender');
  await treatment.populate('therapistId', 'name email');

  logger.info(`Treatment plan created for patient ${patientId} by therapist ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: treatment
  });
});

// @desc    Get all treatment plans
// @route   GET /api/treatments
// @access  Private
const getTreatmentPlans = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    status,
    priority,
    therapistId,
    patientId,
    search
  } = req.query;

  // Build query
  const query = {};
  
  if (status) query.status = status;
  if (priority) query.priority = priority;
  if (therapistId) query.therapistId = therapistId;
  if (patientId) query.patientId = patientId;
  
  if (search) {
    query.$or = [
      { diagnosis: { $regex: search, $options: 'i' } },
      { notes: { $regex: search, $options: 'i' } }
    ];
  }

  // Role-based filtering
  if (req.user.role === 'therapist') {
    query.therapistId = req.user.id;
  }

  const treatments = await Treatment.find(query)
    .populate('patientId', 'name nameAr age gender phone')
    .populate('therapistId', 'name email')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit))
    .skip((parseInt(page) - 1) * parseInt(limit));

  const total = await Treatment.countDocuments(query);

  res.json({
    success: true,
    data: treatments,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / parseInt(limit))
    }
  });
});

// @desc    Get single treatment plan
// @route   GET /api/treatments/:id
// @access  Private
const getTreatmentPlan = asyncHandler(async (req, res) => {
  const treatment = await Treatment.findById(req.params.id)
    .populate('patientId', 'name nameAr age gender phone email')
    .populate('therapistId', 'name email')
    .populate('sessions.therapistId', 'name');

  if (!treatment) {
    throw new AppError('Treatment plan not found', 404);
  }

  // Check access permissions
  if (req.user.role === 'therapist' && treatment.therapistId._id.toString() !== req.user.id) {
    throw new AppError('Not authorized to access this treatment plan', 403);
  }

  res.json({
    success: true,
    data: treatment
  });
});

// @desc    Update treatment plan
// @route   PUT /api/treatments/:id
// @access  Private (Therapist+)
const updateTreatmentPlan = asyncHandler(async (req, res) => {
  let treatment = await Treatment.findById(req.params.id);

  if (!treatment) {
    throw new AppError('Treatment plan not found', 404);
  }

  // Check ownership for therapists
  if (req.user.role === 'therapist' && treatment.therapistId.toString() !== req.user.id) {
    throw new AppError('Not authorized to update this treatment plan', 403);
  }

  treatment = await Treatment.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  ).populate('patientId', 'name nameAr age gender')
   .populate('therapistId', 'name email');

  logger.info(`Treatment plan ${req.params.id} updated by ${req.user.id}`);

  res.json({
    success: true,
    data: treatment
  });
});

// @desc    Delete treatment plan
// @route   DELETE /api/treatments/:id
// @access  Private (Admin/Therapist owner)
const deleteTreatmentPlan = asyncHandler(async (req, res) => {
  const treatment = await Treatment.findById(req.params.id);

  if (!treatment) {
    throw new AppError('Treatment plan not found', 404);
  }

  // Check ownership for therapists
  if (req.user.role === 'therapist' && treatment.therapistId.toString() !== req.user.id) {
    throw new AppError('Not authorized to delete this treatment plan', 403);
  }

  await treatment.deleteOne();

  logger.info(`Treatment plan ${req.params.id} deleted by ${req.user.id}`);

  res.json({
    success: true,
    message: 'Treatment plan deleted successfully'
  });
});

// @desc    Get treatment plans by patient
// @route   GET /api/treatments/patient/:patientId
// @access  Private
const getTreatmentPlansByPatient = asyncHandler(async (req, res) => {
  const { patientId } = req.params;
  const { status = 'active' } = req.query;

  const query = { patientId };
  if (status !== 'all') {
    query.status = status;
  }

  const treatments = await Treatment.find(query)
    .populate('therapistId', 'name email')
    .sort({ createdAt: -1 });

  res.json({
    success: true,
    data: treatments
  });
});

// @desc    Get treatment plans by therapist
// @route   GET /api/treatments/therapist/:therapistId
// @access  Private
const getTreatmentPlansByTherapist = asyncHandler(async (req, res) => {
  const { therapistId } = req.params;
  const { status = 'active' } = req.query;

  // Check if user can access this therapist's data
  if (req.user.role === 'therapist' && req.user.id !== therapistId) {
    throw new AppError('Not authorized to access this data', 403);
  }

  const query = { therapistId };
  if (status !== 'all') {
    query.status = status;
  }

  const treatments = await Treatment.find(query)
    .populate('patientId', 'name nameAr age gender')
    .sort({ createdAt: -1 });

  res.json({
    success: true,
    data: treatments
  });
});

// @desc    Get treatments due for review
// @route   GET /api/treatments/due-for-review
// @access  Private
const getDueForReview = asyncHandler(async (req, res) => {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const query = {
    status: 'active',
    $or: [
      { lastReviewDate: { $lt: thirtyDaysAgo } },
      { lastReviewDate: { $exists: false } }
    ]
  };

  // Role-based filtering
  if (req.user.role === 'therapist') {
    query.therapistId = req.user.id;
  }

  const treatments = await Treatment.find(query)
    .populate('patientId', 'name nameAr age gender')
    .populate('therapistId', 'name email')
    .sort({ lastReviewDate: 1 });

  res.json({
    success: true,
    data: treatments
  });
});

// @desc    Get treatment statistics
// @route   GET /api/treatments/stats
// @access  Private
const getTreatmentStats = asyncHandler(async (req, res) => {
  const { startDate, endDate, therapistId } = req.query;

  const matchStage = {};
  
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  if (therapistId) {
    matchStage.therapistId = therapistId;
  } else if (req.user.role === 'therapist') {
    matchStage.therapistId = req.user.id;
  }

  const stats = await Treatment.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalTreatments: { $sum: 1 },
        activeTreatments: {
          $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
        },
        completedTreatments: {
          $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
        },
        avgSessionsPerTreatment: { $avg: { $size: '$sessions' } },
        uniquePatients: { $addToSet: '$patientId' }
      }
    },
    {
      $project: {
        _id: 0,
        totalTreatments: 1,
        activeTreatments: 1,
        completedTreatments: 1,
        avgSessionsPerTreatment: { $round: ['$avgSessionsPerTreatment', 1] },
        uniquePatients: { $size: '$uniquePatients' }
      }
    }
  ]);

  res.json({
    success: true,
    data: stats[0] || {
      totalTreatments: 0,
      activeTreatments: 0,
      completedTreatments: 0,
      avgSessionsPerTreatment: 0,
      uniquePatients: 0
    }
  });
});

// @desc    Export treatment plan
// @route   GET /api/treatments/:id/export
// @access  Private
const exportTreatmentPlan = asyncHandler(async (req, res) => {
  const treatment = await Treatment.findById(req.params.id)
    .populate('patientId', 'name nameAr age gender phone email')
    .populate('therapistId', 'name email')
    .populate('sessions.therapistId', 'name');

  if (!treatment) {
    throw new AppError('Treatment plan not found', 404);
  }

  // Check access permissions
  if (req.user.role === 'therapist' && treatment.therapistId._id.toString() !== req.user.id) {
    throw new AppError('Not authorized to export this treatment plan', 403);
  }

  // For now, return JSON data - in production, this would generate PDF/Excel
  res.json({
    success: true,
    data: treatment,
    exportedAt: new Date().toISOString()
  });
});

// @desc    Add progress note
// @route   POST /api/treatments/:id/progress
// @access  Private
const addProgressNote = asyncHandler(async (req, res) => {
  const { note, painLevel, functionalLevel } = req.body;

  const treatment = await Treatment.findById(req.params.id);

  if (!treatment) {
    throw new AppError('Treatment plan not found', 404);
  }

  // Check access permissions
  if (req.user.role === 'therapist' && treatment.therapistId.toString() !== req.user.id) {
    throw new AppError('Not authorized to add progress note', 403);
  }

  const progressNote = {
    note,
    painLevel,
    functionalLevel,
    therapistId: req.user.id,
    date: new Date()
  };

  treatment.progressNotes = treatment.progressNotes || [];
  treatment.progressNotes.push(progressNote);

  await treatment.save();

  res.status(201).json({
    success: true,
    data: treatment,
    message: 'Progress note added successfully'
  });
});

// @desc    Update goal status
// @route   PUT /api/treatments/:id/goals/:goalId
// @access  Private
const updateGoalStatus = asyncHandler(async (req, res) => {
  const { status, notes } = req.body;

  const treatment = await Treatment.findById(req.params.id);

  if (!treatment) {
    throw new AppError('Treatment plan not found', 404);
  }

  // Check access permissions
  if (req.user.role === 'therapist' && treatment.therapistId.toString() !== req.user.id) {
    throw new AppError('Not authorized to update goal status', 403);
  }

  const goal = treatment.goals.id(req.params.goalId);
  if (!goal) {
    throw new AppError('Goal not found', 404);
  }

  goal.status = status;
  if (notes) goal.notes = notes;
  goal.lastUpdated = new Date();

  await treatment.save();

  res.json({
    success: true,
    data: treatment,
    message: 'Goal status updated successfully'
  });
});

module.exports = {
  createTreatmentPlan,
  getTreatmentPlans,
  getTreatmentPlan,
  updateTreatmentPlan,
  deleteTreatmentPlan,
  getTreatmentPlansByPatient,
  getTreatmentPlansByTherapist,
  getDueForReview,
  getTreatmentStats,
  exportTreatmentPlan,
  addProgressNote,
  updateGoalStatus
};
