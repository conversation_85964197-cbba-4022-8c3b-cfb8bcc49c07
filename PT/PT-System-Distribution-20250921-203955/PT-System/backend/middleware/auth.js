const jwt = require('jsonwebtoken');
const { async<PERSON>and<PERSON>, AppError } = require('./errorHandler');
const User = require('../models/User');
const logger = require('../utils/logger');

// Protect routes - verify JWT token
const protect = asyncHandler(async (req, res, next) => {
  let token;

  // Check for token in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }
  // Check for token in cookies
  else if (req.cookies.token) {
    token = req.cookies.token;
  }

  // Make sure token exists
  if (!token) {
    return next(new AppError('Not authorized to access this route', 401));
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Get user from token
    const user = await User.findById(decoded.id).select('+password');

    if (!user) {
      return next(new AppError('No user found with this token', 401));
    }

    // Check if user is active
    if (!user.isActive) {
      return next(new AppError('User account is deactivated', 401));
    }

    // Check if user changed password after the token was issued
    if (user.changedPasswordAfter(decoded.iat)) {
      return next(new AppError('User recently changed password! Please log in again.', 401));
    }

    // Grant access to protected route
    req.user = user;
    
    // Set current user for models
    User.currentUser = user._id;
    
    next();
  } catch (error) {
    logger.error('Token verification failed:', error);
    return next(new AppError('Not authorized to access this route', 401));
  }
});

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Not authorized to access this route', 401));
    }

    if (!roles.includes(req.user.role)) {
      return next(
        new AppError(`User role ${req.user.role} is not authorized to access this route`, 403)
      );
    }

    next();
  };
};

// Check specific permissions
const checkPermission = (permission) => {
  return (req, res, next) => {
    if (!req.user) {
      return next(new AppError('Not authorized to access this route', 401));
    }

    // Admin has all permissions
    if (req.user.role === 'admin') {
      return next();
    }

    // Check if user has the specific permission
    if (!req.user.permissions || !req.user.permissions.includes(permission)) {
      return next(
        new AppError(`You don't have permission to ${permission}`, 403)
      );
    }

    next();
  };
};

// Check if user can access patient data
const checkPatientAccess = asyncHandler(async (req, res, next) => {
  const patientId = req.params.patientId || req.params.id;
  
  if (!patientId) {
    return next(new AppError('Patient ID is required', 400));
  }

  // Admin can access all patients
  if (req.user.role === 'admin') {
    return next();
  }

  // Check if user is assigned to this patient
  const Patient = require('../models/Patient');
  const patient = await Patient.findById(patientId);

  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  // Check if user is the primary therapist or assigned doctor
  const hasAccess = 
    patient.primaryTherapist?.toString() === req.user._id.toString() ||
    patient.assignedDoctor?.toString() === req.user._id.toString() ||
    patient.createdBy?.toString() === req.user._id.toString();

  if (!hasAccess) {
    return next(new AppError('You are not authorized to access this patient\'s data', 403));
  }

  req.patient = patient;
  next();
});

// Rate limiting for authentication endpoints
const authRateLimit = require('express-rate-limit')({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts, please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for successful requests
    return req.user !== undefined;
  }
});

// Optional authentication - doesn't fail if no token
const optionalAuth = asyncHandler(async (req, res, next) => {
  let token;

  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies.token) {
    token = req.cookies.token;
  }

  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id);
      
      if (user && user.isActive && !user.changedPasswordAfter(decoded.iat)) {
        req.user = user;
        User.currentUser = user._id;
      }
    } catch (error) {
      // Silently fail for optional auth
      logger.debug('Optional auth failed:', error.message);
    }
  }

  next();
});

// Middleware to log user actions
const logUserAction = (action) => {
  return (req, res, next) => {
    if (req.user) {
      logger.info(`User ${req.user._id} (${req.user.role}) performed action: ${action}`, {
        userId: req.user._id,
        userRole: req.user.role,
        action: action,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });
    }
    next();
  };
};

// Middleware to check if user owns the resource
const checkOwnership = (Model, ownerField = 'createdBy') => {
  return asyncHandler(async (req, res, next) => {
    const resourceId = req.params.id;
    
    if (!resourceId) {
      return next(new AppError('Resource ID is required', 400));
    }

    // Admin can access all resources
    if (req.user.role === 'admin') {
      return next();
    }

    const resource = await Model.findById(resourceId);
    
    if (!resource) {
      return next(new AppError('Resource not found', 404));
    }

    // Check ownership
    if (resource[ownerField]?.toString() !== req.user._id.toString()) {
      return next(new AppError('You are not authorized to access this resource', 403));
    }

    req.resource = resource;
    next();
  });
};

// Middleware to validate API key for external integrations
const validateApiKey = asyncHandler(async (req, res, next) => {
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    return next(new AppError('API key is required', 401));
  }

  // In a real application, you would validate against a database
  // For now, we'll use environment variable
  if (apiKey !== process.env.API_KEY) {
    return next(new AppError('Invalid API key', 401));
  }

  next();
});

// Middleware to check session validity
const checkSession = asyncHandler(async (req, res, next) => {
  if (req.session && req.session.userId) {
    const user = await User.findById(req.session.userId);
    
    if (user && user.isActive) {
      req.user = user;
      User.currentUser = user._id;
    } else {
      req.session.destroy();
    }
  }
  
  next();
});

module.exports = {
  protect,
  authorize,
  checkPermission,
  checkPatientAccess,
  authRateLimit,
  optionalAuth,
  logUserAction,
  checkOwnership,
  validateApiKey,
  checkSession
};
