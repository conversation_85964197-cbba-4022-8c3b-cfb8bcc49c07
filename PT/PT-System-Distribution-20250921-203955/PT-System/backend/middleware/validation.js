const Joi = require('joi');

// Clinical Indicators validation
const validateClinicalIndicator = (req, res, next) => {
  const schema = Joi.object({
    patientId: Joi.string().required().messages({
      'string.empty': 'Patient ID is required',
      'any.required': 'Patient ID is required'
    }),
    assessmentId: Joi.string().required().messages({
      'string.empty': 'Assessment ID is required',
      'any.required': 'Assessment ID is required'
    }),
    assessmentType: Joi.string().valid('initial', 'progress', 'discharge', 'follow-up').required(),
    
    // Berg Balance Scale
    bergBalanceScale: Joi.object({
      totalScore: Joi.number().min(0).max(56).allow(null),
      riskLevel: Joi.string().valid('high-risk', 'moderate-risk', 'low-risk').allow(null),
      individualScores: Joi.object({
        sittingToStanding: Joi.number().min(0).max(4).allow(null),
        standingUnsupported: Joi.number().min(0).max(4).allow(null),
        sittingUnsupported: Joi.number().min(0).max(4).allow(null),
        standingToSitting: Joi.number().min(0).max(4).allow(null),
        transfers: Joi.number().min(0).max(4).allow(null),
        standingEyesClosed: Joi.number().min(0).max(4).allow(null),
        standingFeetTogether: Joi.number().min(0).max(4).allow(null),
        reachingForward: Joi.number().min(0).max(4).allow(null),
        pickingUpObject: Joi.number().min(0).max(4).allow(null),
        lookingOverShoulders: Joi.number().min(0).max(4).allow(null),
        turning360Degrees: Joi.number().min(0).max(4).allow(null),
        placingFeetOnStool: Joi.number().min(0).max(4).allow(null),
        standingOneFoot: Joi.number().min(0).max(4).allow(null),
        standingTandem: Joi.number().min(0).max(4).allow(null)
      }).allow(null),
      notes: Joi.string().allow('')
    }).allow(null),
    
    // Timed Up and Go
    timedUpAndGo: Joi.object({
      timeInSeconds: Joi.number().min(0).allow(null),
      riskLevel: Joi.string().valid('normal', 'mild-impairment', 'moderate-impairment', 'severe-impairment').allow(null),
      assistiveDevice: Joi.string().valid('none', 'cane', 'walker', 'wheelchair', 'other').default('none'),
      notes: Joi.string().allow('')
    }).allow(null),
    
    // Manual Muscle Testing
    manualMuscleTesting: Joi.object({
      upperExtremity: Joi.object({
        rightShoulder: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null),
          abduction: Joi.number().min(0).max(5).allow(null),
          adduction: Joi.number().min(0).max(5).allow(null)
        }).allow(null),
        leftShoulder: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null),
          abduction: Joi.number().min(0).max(5).allow(null),
          adduction: Joi.number().min(0).max(5).allow(null)
        }).allow(null),
        rightElbow: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null)
        }).allow(null),
        leftElbow: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null)
        }).allow(null)
      }).allow(null),
      lowerExtremity: Joi.object({
        rightHip: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null),
          abduction: Joi.number().min(0).max(5).allow(null),
          adduction: Joi.number().min(0).max(5).allow(null)
        }).allow(null),
        leftHip: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null),
          abduction: Joi.number().min(0).max(5).allow(null),
          adduction: Joi.number().min(0).max(5).allow(null)
        }).allow(null),
        rightKnee: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null)
        }).allow(null),
        leftKnee: Joi.object({
          flexion: Joi.number().min(0).max(5).allow(null),
          extension: Joi.number().min(0).max(5).allow(null)
        }).allow(null)
      }).allow(null),
      trunk: Joi.object({
        flexion: Joi.number().min(0).max(5).allow(null),
        extension: Joi.number().min(0).max(5).allow(null),
        lateralFlexionRight: Joi.number().min(0).max(5).allow(null),
        lateralFlexionLeft: Joi.number().min(0).max(5).allow(null)
      }).allow(null),
      overallStrengthIndex: Joi.number().min(0).max(5).allow(null),
      notes: Joi.string().allow('')
    }).allow(null),
    
    // Pain Scale
    painScale: Joi.object({
      currentPain: Joi.number().min(0).max(10).allow(null),
      averagePain24h: Joi.number().min(0).max(10).allow(null),
      worstPain24h: Joi.number().min(0).max(10).allow(null),
      painAtRest: Joi.number().min(0).max(10).allow(null),
      painWithActivity: Joi.number().min(0).max(10).allow(null),
      painLocation: Joi.array().items(Joi.string()).allow(null),
      painQuality: Joi.array().items(Joi.string().valid('sharp', 'dull', 'burning', 'aching', 'stabbing', 'throbbing', 'cramping', 'shooting')).allow(null),
      painPattern: Joi.string().valid('constant', 'intermittent', 'variable', 'progressive', 'improving').allow(null),
      notes: Joi.string().allow('')
    }).allow(null),
    
    // Functional Independence Measure
    functionalIndependenceMeasure: Joi.object({
      selfCare: Joi.object({
        eating: Joi.number().min(1).max(7).allow(null),
        grooming: Joi.number().min(1).max(7).allow(null),
        bathing: Joi.number().min(1).max(7).allow(null),
        dressingUpper: Joi.number().min(1).max(7).allow(null),
        dressingLower: Joi.number().min(1).max(7).allow(null),
        toileting: Joi.number().min(1).max(7).allow(null)
      }).allow(null),
      sphincterControl: Joi.object({
        bladder: Joi.number().min(1).max(7).allow(null),
        bowel: Joi.number().min(1).max(7).allow(null)
      }).allow(null),
      transfers: Joi.object({
        bedChairWheelchair: Joi.number().min(1).max(7).allow(null),
        toilet: Joi.number().min(1).max(7).allow(null),
        tubShower: Joi.number().min(1).max(7).allow(null)
      }).allow(null),
      locomotion: Joi.object({
        walkWheelchair: Joi.number().min(1).max(7).allow(null),
        stairs: Joi.number().min(1).max(7).allow(null)
      }).allow(null),
      communication: Joi.object({
        comprehension: Joi.number().min(1).max(7).allow(null),
        expression: Joi.number().min(1).max(7).allow(null)
      }).allow(null),
      socialCognition: Joi.object({
        socialInteraction: Joi.number().min(1).max(7).allow(null),
        problemSolving: Joi.number().min(1).max(7).allow(null),
        memory: Joi.number().min(1).max(7).allow(null)
      }).allow(null),
      totalScore: Joi.number().min(18).max(126).allow(null),
      motorScore: Joi.number().min(13).max(91).allow(null),
      cognitiveScore: Joi.number().min(5).max(35).allow(null),
      notes: Joi.string().allow('')
    }).allow(null),
    
    // Range of Motion
    rangeOfMotion: Joi.object({
      cervicalSpine: Joi.object({
        flexion: Joi.number().allow(null),
        extension: Joi.number().allow(null),
        lateralFlexionRight: Joi.number().allow(null),
        lateralFlexionLeft: Joi.number().allow(null),
        rotationRight: Joi.number().allow(null),
        rotationLeft: Joi.number().allow(null)
      }).allow(null),
      lumbarSpine: Joi.object({
        flexion: Joi.number().allow(null),
        extension: Joi.number().allow(null),
        lateralFlexionRight: Joi.number().allow(null),
        lateralFlexionLeft: Joi.number().allow(null)
      }).allow(null),
      notes: Joi.string().allow('')
    }).allow(null),
    
    clinicalNotes: Joi.string().allow(''),
    recommendations: Joi.string().allow(''),
    nextAssessmentDate: Joi.date().allow(null)
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    });
  }

  next();
};

// Patient validation
const validatePatient = (req, res, next) => {
  const schema = Joi.object({
    firstName: Joi.string().required(),
    lastName: Joi.string().required(),
    dateOfBirth: Joi.date().required(),
    gender: Joi.string().valid('male', 'female', 'other').required(),
    phone: Joi.string().required(),
    email: Joi.string().email().allow(''),
    address: Joi.object({
      street: Joi.string().required(),
      city: Joi.string().required(),
      state: Joi.string().required(),
      zipCode: Joi.string().required(),
      country: Joi.string().default('Saudi Arabia')
    }).required(),
    emergencyContact: Joi.object({
      name: Joi.string().required(),
      relationship: Joi.string().required(),
      phone: Joi.string().required()
    }).required(),
    medicalHistory: Joi.array().items(Joi.string()),
    allergies: Joi.array().items(Joi.string()),
    medications: Joi.array().items(Joi.string()),
    specialNeeds: Joi.object({
      hasSpecialNeeds: Joi.boolean().default(false),
      needsDescription: Joi.string().allow(''),
      accommodations: Joi.array().items(Joi.string()),
      assistiveDevices: Joi.array().items(Joi.string())
    })
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }))
    });
  }

  next();
};

module.exports = {
  validateClinicalIndicator,
  validatePatient
};
