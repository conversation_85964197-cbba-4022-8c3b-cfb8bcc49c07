const rolePermissions = {
  admin: [
    'view_all_patients',
    'create_patient',
    'edit_patient',
    'delete_patient',
    'view_appointments',
    'create_appointment',
    'edit_appointment',
    'delete_appointment',
    'view_assessments',
    'create_assessment',
    'edit_assessment',
    'delete_assessment',
    'view_treatments',
    'create_treatment',
    'edit_treatment',
    'delete_treatment',
    'view_forms',
    'create_form',
    'edit_form',
    'delete_form',
    'view_reports',
    'create_report',
    'view_analytics',
    'manage_users',
    'view_settings',
    'edit_settings',
    'view_billing',
    'create_billing',
    'edit_billing',
    'view_insurance',
    'create_insurance',
    'edit_insurance',
    'view_discharge_assessments',
    'create_discharge_assessment',
    'edit_discharge_assessment',
    'delete_discharge_assessment'
  ],
  therapist: [
    'view_assigned_patients',
    'create_patient',
    'edit_patient',
    'view_appointments',
    'create_appointment',
    'edit_appointment',
    'view_assessments',
    'create_assessment',
    'edit_assessment',
    'view_treatments',
    'create_treatment',
    'edit_treatment',
    'view_forms',
    'create_form',
    'edit_form',
    'view_reports',
    'view_analytics',
    'view_discharge_assessments',
    'create_discharge_assessment',
    'edit_discharge_assessment'
  ],
  nurse: [
    'view_assigned_patients',
    'view_appointments',
    'create_appointment',
    'edit_appointment',
    'view_assessments',
    'create_assessment',
    'view_treatments',
    'view_forms',
    'create_form',
    'view_reports',
    'view_discharge_assessments',
    'create_discharge_assessment'
  ],
  receptionist: [
    'view_patients',
    'create_patient',
    'edit_patient',
    'view_appointments',
    'create_appointment',
    'edit_appointment',
    'view_billing',
    'create_billing',
    'edit_billing',
    'view_insurance',
    'create_insurance',
    'edit_insurance'
  ],
  doctor: [
    'view_all_patients',
    'create_patient',
    'edit_patient',
    'view_appointments',
    'create_appointment',
    'edit_appointment',
    'view_assessments',
    'create_assessment',
    'edit_assessment',
    'view_treatments',
    'create_treatment',
    'edit_treatment',
    'view_forms',
    'create_form',
    'edit_form',
    'view_reports',
    'view_analytics',
    'view_discharge_assessments',
    'create_discharge_assessment',
    'edit_discharge_assessment'
  ],
  user: [
    'view_own_profile',
    'edit_own_profile',
    'view_appointments',
    'view_forms'
  ]
};

// Middleware to check if user has required permission
const checkPermission = (permission) => {
  return (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Admin has all permissions
      if (user.role === 'admin') {
        return next();
      }

      // Check if user's role has the required permission
      const userPermissions = rolePermissions[user.role] || [];
      
      if (userPermissions.includes(permission)) {
        return next();
      }

      // Check if user has custom permissions
      if (user.permissions && user.permissions.includes(permission)) {
        return next();
      }

      return res.status(403).json({ 
        message: 'Insufficient permissions',
        required: permission,
        userRole: user.role
      });
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  };
};

// Middleware to check multiple permissions (user needs at least one)
const checkAnyPermission = (permissions) => {
  return (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Admin has all permissions
      if (user.role === 'admin') {
        return next();
      }

      const userPermissions = rolePermissions[user.role] || [];
      
      // Check if user has any of the required permissions
      const hasPermission = permissions.some(permission => 
        userPermissions.includes(permission) || 
        (user.permissions && user.permissions.includes(permission))
      );

      if (hasPermission) {
        return next();
      }

      return res.status(403).json({ 
        message: 'Insufficient permissions',
        required: permissions,
        userRole: user.role
      });
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  };
};

// Middleware to check if user has role
const checkRole = (roles) => {
  return (req, res, next) => {
    try {
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const allowedRoles = Array.isArray(roles) ? roles : [roles];
      
      if (allowedRoles.includes(user.role)) {
        return next();
      }

      return res.status(403).json({ 
        message: 'Insufficient role permissions',
        required: allowedRoles,
        userRole: user.role
      });
    } catch (error) {
      console.error('Role check error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  };
};

// Function to get user permissions
const getUserPermissions = (userRole, customPermissions = []) => {
  const rolePerms = rolePermissions[userRole] || [];
  return [...new Set([...rolePerms, ...customPermissions])];
};

// Function to check if user has specific permission
const hasPermission = (user, permission) => {
  if (!user) return false;
  
  if (user.role === 'admin') return true;
  
  const userPermissions = rolePermissions[user.role] || [];
  return userPermissions.includes(permission) || 
         (user.permissions && user.permissions.includes(permission));
};

module.exports = {
  rolePermissions,
  checkPermission,
  checkAnyPermission,
  checkRole,
  getUserPermissions,
  hasPermission
};
