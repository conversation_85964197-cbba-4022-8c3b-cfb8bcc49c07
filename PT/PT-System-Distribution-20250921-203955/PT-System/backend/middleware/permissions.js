const { AppError } = require('./errorHandler');

/**
 * Middleware to check user permissions for specific actions
 * التحقق من صلاحيات المستخدم للإجراءات المحددة
 */

// Check form access permissions - التحقق من صلاحيات الوصول للنماذج
const checkFormPermission = (formType) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return next(new AppError('User not authenticated', 401));
    }

    // Admin has access to everything
    if (user.role === 'admin') {
      return next();
    }

    // Check if user has permission for this form
    const hasPermission = user.permissions?.forms?.[formType];
    
    if (!hasPermission) {
      return next(new AppError(`Access denied. You don't have permission to access ${formType} form`, 403));
    }

    next();
  };
};

// Check system access permissions - التحقق من صلاحيات الوصول للنظام
const checkSystemPermission = (action) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return next(new AppError('User not authenticated', 401));
    }

    // Admin has access to everything
    if (user.role === 'admin') {
      return next();
    }

    // Check if user has permission for this system action
    const hasPermission = user.permissions?.system?.[action];
    
    if (!hasPermission) {
      return next(new AppError(`Access denied. You don't have permission to ${action}`, 403));
    }

    next();
  };
};

// Check analytics access permissions - التحقق من صلاحيات الوصول للتحليلات
const checkAnalyticsPermission = (analyticsType) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return next(new AppError('User not authenticated', 401));
    }

    // Admin has access to everything
    if (user.role === 'admin') {
      return next();
    }

    // Check if user has permission for this analytics type
    const hasPermission = user.permissions?.analytics?.[analyticsType];
    
    if (!hasPermission) {
      return next(new AppError(`Access denied. You don't have permission to view ${analyticsType}`, 403));
    }

    next();
  };
};

// Role-based access control - التحكم في الوصول حسب الدور
const requireRole = (...roles) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return next(new AppError('User not authenticated', 401));
    }

    if (!roles.includes(user.role)) {
      return next(new AppError(`Access denied. Required role: ${roles.join(' or ')}`, 403));
    }

    next();
  };
};

// Check if user can access patient data - التحقق من إمكانية الوصول لبيانات المريض
const checkPatientAccess = (req, res, next) => {
  const user = req.user;
  
  if (!user) {
    return next(new AppError('User not authenticated', 401));
  }

  // Admin and doctors have full access
  if (['admin', 'doctor'].includes(user.role)) {
    return next();
  }

  // Therapists can access patients assigned to them
  if (user.role === 'therapist') {
    // TODO: Add logic to check if patient is assigned to this therapist
    return next();
  }

  // Nurses can access patients in their department
  if (user.role === 'nurse') {
    // TODO: Add logic to check if patient is in nurse's department
    return next();
  }

  // External services can only access patients referred to them
  if (['external_lab', 'external_radiology'].includes(user.role)) {
    // TODO: Add logic to check if patient is referred to this external service
    return next();
  }

  // Receptionists can access all patients for administrative purposes
  if (user.role === 'receptionist') {
    return next();
  }

  return next(new AppError('Access denied. You cannot access this patient data', 403));
};

// Specific permission checks for medical forms - فحص صلاحيات محددة للنماذج الطبية

// صلاحيات الطبيب - Doctor specific permissions
const doctorOnly = checkFormPermission('medicalDiagnosis');
const ptEvaluationAccess = checkFormPermission('ptEvaluation');
const reassessmentAccess = checkFormPermission('reassessment');
const dischargeEvaluationAccess = checkFormPermission('dischargeEvaluation');
const followUpFormAccess = checkFormPermission('followUpForm');

// صلاحيات أخصائي العلاج الطبيعي - Therapist specific permissions
const progressNotesAccess = checkFormPermission('progressNotes');
const therapistEducationAccess = checkFormPermission('patientEducationTherapist');

// صلاحيات التمريض - Nursing specific permissions
const nursingFormAccess = checkFormPermission('nursingForm');
const nurseEducationAccess = checkFormPermission('patientEducationNurse');

// صلاحيات الخدمات الخارجية - External services permissions
const labResultsAccess = checkFormPermission('labResults');
const radiologyResultsAccess = checkFormPermission('radiologyResults');

// Clinical indicators access - صلاحيات المؤشرات السريرية
const clinicalIndicatorsAccess = checkFormPermission('clinicalIndicators');

// System permissions - صلاحيات النظام
const viewPatientsAccess = checkSystemPermission('viewPatients');
const createPatientsAccess = checkSystemPermission('createPatients');
const editPatientsAccess = checkSystemPermission('editPatients');
const deletePatientsAccess = checkSystemPermission('deletePatients');
const manageUsersAccess = checkSystemPermission('manageUsers');
const systemSettingsAccess = checkSystemPermission('systemSettings');
const viewAnalyticsAccess = checkSystemPermission('viewAnalytics');

// Analytics permissions - صلاحيات التحليلات
const functionalIndependenceAccess = checkAnalyticsPermission('functionalIndependenceComparison');
const treatmentEffectivenessAccess = checkAnalyticsPermission('treatmentPlanEffectiveness');
const clinicalProgressAccess = checkAnalyticsPermission('clinicalProgressTracking');

module.exports = {
  checkFormPermission,
  checkSystemPermission,
  checkAnalyticsPermission,
  requireRole,
  checkPatientAccess,
  
  // Doctor permissions
  doctorOnly,
  ptEvaluationAccess,
  reassessmentAccess,
  dischargeEvaluationAccess,
  followUpFormAccess,
  
  // Therapist permissions
  progressNotesAccess,
  therapistEducationAccess,
  
  // Nursing permissions
  nursingFormAccess,
  nurseEducationAccess,
  
  // External services permissions
  labResultsAccess,
  radiologyResultsAccess,
  
  // Clinical indicators
  clinicalIndicatorsAccess,
  
  // System permissions
  viewPatientsAccess,
  createPatientsAccess,
  editPatientsAccess,
  deletePatientsAccess,
  manageUsersAccess,
  systemSettingsAccess,
  viewAnalyticsAccess,
  
  // Analytics permissions
  functionalIndependenceAccess,
  treatmentEffectivenessAccess,
  clinicalProgressAccess
};
