const { AppError } = require('./errorHandler');
const { defaultPermissions } = require('../controllers/permissions');

// Middleware to check specific permissions
// وسطية للتحقق من الصلاحيات المحددة
const checkPermission = (category, permission) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return next(new AppError('Authentication required', 401));
    }

    // Admin has all permissions
    if (user.role === 'admin') {
      return next();
    }

    // Get user permissions or default permissions for their role
    const userPermissions = user.permissions || defaultPermissions[user.role] || {};
    
    // Check if user has the specific permission
    const hasPermission = userPermissions[category] && userPermissions[category][permission];
    
    if (!hasPermission) {
      return next(new AppError(`Access denied. Required permission: ${category}.${permission}`, 403));
    }

    next();
  };
};

// Check multiple permissions (user needs ALL of them)
const checkPermissions = (permissionChecks) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return next(new AppError('Authentication required', 401));
    }

    // Admin has all permissions
    if (user.role === 'admin') {
      return next();
    }

    // Get user permissions or default permissions for their role
    const userPermissions = user.permissions || defaultPermissions[user.role] || {};
    
    // Check all required permissions
    for (const { category, permission } of permissionChecks) {
      const hasPermission = userPermissions[category] && userPermissions[category][permission];
      
      if (!hasPermission) {
        return next(new AppError(`Access denied. Required permission: ${category}.${permission}`, 403));
      }
    }

    next();
  };
};

// Check if user has ANY of the specified permissions
const checkAnyPermission = (permissionChecks) => {
  return (req, res, next) => {
    const user = req.user;
    
    if (!user) {
      return next(new AppError('Authentication required', 401));
    }

    // Admin has all permissions
    if (user.role === 'admin') {
      return next();
    }

    // Get user permissions or default permissions for their role
    const userPermissions = user.permissions || defaultPermissions[user.role] || {};
    
    // Check if user has any of the required permissions
    const hasAnyPermission = permissionChecks.some(({ category, permission }) => {
      return userPermissions[category] && userPermissions[category][permission];
    });
    
    if (!hasAnyPermission) {
      const requiredPerms = permissionChecks.map(p => `${p.category}.${p.permission}`).join(' OR ');
      return next(new AppError(`Access denied. Required permissions: ${requiredPerms}`, 403));
    }

    next();
  };
};

// Specific permission checkers for common use cases
// فحص الصلاحيات المحددة للحالات الشائعة

// Doctor permissions - صلاحيات الطبيب
const requireDoctorPermissions = {
  medicalDiagnosis: checkPermission('forms', 'medicalDiagnosis'),
  ptEvaluation: checkPermission('forms', 'ptEvaluation'),
  reassessment: checkPermission('forms', 'reassessment'),
  dischargeEvaluation: checkPermission('forms', 'dischargeEvaluation'),
  followUpForm: checkPermission('forms', 'followUpForm'),
  patientEducationDoctor: checkPermission('forms', 'patientEducationDoctor')
};

// Physical Therapist permissions - صلاحيات أخصائي العلاج الطبيعي
const requireTherapistPermissions = {
  progressNotes: checkPermission('forms', 'progressNotes'),
  patientEducationTherapist: checkPermission('forms', 'patientEducationTherapist'),
  clinicalIndicators: checkPermission('forms', 'clinicalIndicators'),
  bergBalanceScale: checkPermission('forms', 'bergBalanceScale'),
  tugTest: checkPermission('forms', 'tugTest'),
  manualMuscleTesting: checkPermission('forms', 'manualMuscleTesting'),
  painScaleVAS: checkPermission('forms', 'painScaleVAS'),
  functionalIndependence: checkPermission('forms', 'functionalIndependence')
};

// Nursing permissions - صلاحيات التمريض
const requireNursingPermissions = {
  patientEducationNurse: checkPermission('forms', 'patientEducationNurse'),
  nursingForm: checkPermission('forms', 'nursingForm')
};

// External service permissions - صلاحيات الخدمات الخارجية
const requireExternalPermissions = {
  labResults: checkPermission('forms', 'labResults'),
  radiologyResults: checkPermission('forms', 'radiologyResults')
};

// Clinical analytics permissions - صلاحيات التحليلات السريرية
const requireAnalyticsPermissions = {
  functionalIndependenceComparison: checkPermission('analytics', 'functionalIndependenceComparison'),
  treatmentPlanEffectiveness: checkPermission('analytics', 'treatmentPlanEffectiveness'),
  clinicalProgressTracking: checkPermission('analytics', 'clinicalProgressTracking'),
  outcomeMetrics: checkPermission('analytics', 'outcomeMetrics'),
  qualityIndicators: checkPermission('analytics', 'qualityIndicators')
};

// System permissions - صلاحيات النظام
const requireSystemPermissions = {
  viewPatients: checkPermission('system', 'viewPatients'),
  createPatients: checkPermission('system', 'createPatients'),
  editPatients: checkPermission('system', 'editPatients'),
  deletePatients: checkPermission('system', 'deletePatients'),
  viewAppointments: checkPermission('system', 'viewAppointments'),
  createAppointments: checkPermission('system', 'createAppointments'),
  editAppointments: checkPermission('system', 'editAppointments'),
  deleteAppointments: checkPermission('system', 'deleteAppointments'),
  viewBilling: checkPermission('system', 'viewBilling'),
  createBilling: checkPermission('system', 'createBilling'),
  editBilling: checkPermission('system', 'editBilling'),
  viewReports: checkPermission('system', 'viewReports'),
  viewAnalytics: checkPermission('system', 'viewAnalytics'),
  manageUsers: checkPermission('system', 'manageUsers'),
  systemSettings: checkPermission('system', 'systemSettings')
};

// Combined permission checkers for complex scenarios
const requirePatientEducationAccess = checkAnyPermission([
  { category: 'forms', permission: 'patientEducationDoctor' },
  { category: 'forms', permission: 'patientEducationTherapist' },
  { category: 'forms', permission: 'patientEducationNurse' }
]);

const requireClinicalAssessmentAccess = checkAnyPermission([
  { category: 'forms', permission: 'ptEvaluation' },
  { category: 'forms', permission: 'reassessment' },
  { category: 'forms', permission: 'dischargeEvaluation' },
  { category: 'forms', permission: 'clinicalIndicators' }
]);

const requireExternalServiceAccess = checkAnyPermission([
  { category: 'forms', permission: 'labResults' },
  { category: 'forms', permission: 'radiologyResults' }
]);

// Helper function to get user's effective permissions
const getUserEffectivePermissions = (user) => {
  if (user.role === 'admin') {
    // Admin gets all permissions
    const allPermissions = {};
    Object.keys(defaultPermissions.doctor || {}).forEach(category => {
      allPermissions[category] = {};
      Object.keys(defaultPermissions.doctor[category] || {}).forEach(permission => {
        allPermissions[category][permission] = true;
      });
    });
    return allPermissions;
  }
  
  return user.permissions || defaultPermissions[user.role] || {};
};

module.exports = {
  checkPermission,
  checkPermissions,
  checkAnyPermission,
  requireDoctorPermissions,
  requireTherapistPermissions,
  requireNursingPermissions,
  requireExternalPermissions,
  requireAnalyticsPermissions,
  requireSystemPermissions,
  requirePatientEducationAccess,
  requireClinicalAssessmentAccess,
  requireExternalServiceAccess,
  getUserEffectivePermissions
};
