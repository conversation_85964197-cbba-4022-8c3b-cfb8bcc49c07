# PT System - Production Deployment

## Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- MongoDB (local or remote)

### Installation & Startup

#### Option 1: Quick Start (Recommended)
1. Run the deployment script:
   ```
   .\deploy-windows.bat
   ```

#### Option 2: PowerShell Script
1. Run the PowerShell deployment script:
   ```
   .\deploy-windows.ps1
   ```

#### Option 3: Manual Start
1. Install backend dependencies:
   ```
   cd backend
   npm install --production
   ```

2. Start the application:
   ```
   .\start.bat
   ```

### Access the Application
- Application URL: http://localhost:3016
- API Documentation: http://localhost:3016/api-docs

### Demo Credentials
- Administrator: <EMAIL> / password123
- Therapist: <EMAIL> / password123

### Windows Service Installation
To install as a Windows Service:
```
.\install-service.ps1
```

### Configuration
Edit the `.env` file to configure database connection and other settings.

### Support
For technical support, please contact the PT System team.
