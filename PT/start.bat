@echo off
REM PT System - Quick Start Script for Windows
REM Simple script to start the PT System on port 3016

echo.
echo ========================================
echo    PT System - Quick Start
echo ========================================
echo.

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "backend\server.js" (
    echo [ERROR] Please run this script from the PT System root directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Set environment variables
set NODE_ENV=production
set PORT=3016

echo [INFO] Starting PT System on port 3016...
echo [INFO] Application will be available at: http://localhost:3016
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Start the backend server
cd backend
node server.js

pause
