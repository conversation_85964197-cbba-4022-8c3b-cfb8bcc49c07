@echo off
REM PT System - Distribution Package Creator (Batch Version)
REM This script creates a deployment-ready package for Windows Server

echo.
echo ========================================
echo    PT System - Distribution Creator
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] PowerShell is not available
    echo Please use PowerShell to run create-distribution.ps1
    pause
    exit /b 1
)

echo [INFO] Running PowerShell distribution script...
echo.

REM Run the PowerShell script
powershell -ExecutionPolicy Bypass -File "create-distribution.ps1"

echo.
echo [INFO] Distribution creation completed
pause
