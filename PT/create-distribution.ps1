# PT System - Distribution Package Creator
# This script creates a deployment-ready package for Windows Server

param(
    [string]$OutputPath = ".\PT-System-Distribution.zip",
    [switch]$IncludeSource,
    [switch]$Help
)

# Colors for output
$Red = "`e[91m"
$Green = "`e[92m"
$Yellow = "`e[93m"
$Blue = "`e[94m"
$NC = "`e[0m"

function Write-Info {
    param([string]$Message)
    Write-Host "${Blue}[INFO]${NC} $Message"
}

function Write-Success {
    param([string]$Message)
    Write-Host "${Green}[SUCCESS]${NC} $Message"
}

function Write-Warning {
    param([string]$Message)
    Write-Host "${Yellow}[WARNING]${NC} $Message"
}

function Write-Error {
    param([string]$Message)
    Write-Host "${Red}[ERROR]${NC} $Message"
}

function Show-Help {
    Write-Host ""
    Write-Host "PT System - Distribution Package Creator"
    Write-Host "========================================"
    Write-Host ""
    Write-Host "Usage: .\create-distribution.ps1 [Options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -OutputPath    Output file path (default: .\PT-System-Distribution.zip)"
    Write-Host "  -IncludeSource Include source files (for development deployment)"
    Write-Host "  -Help          Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\create-distribution.ps1"
    Write-Host "  .\create-distribution.ps1 -OutputPath 'C:\Deploy\PT-System.zip'"
    Write-Host "  .\create-distribution.ps1 -IncludeSource"
    Write-Host ""
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check if frontend build exists
    if (!(Test-Path "frontend\build")) {
        Write-Error "Frontend build not found. Please run 'npm run build' in the frontend directory first."
        exit 1
    }
    
    # Check if backend files exist
    if (!(Test-Path "backend\server.js")) {
        Write-Error "Backend server.js not found. Please ensure you're in the correct directory."
        exit 1
    }
    
    Write-Success "Prerequisites check passed"
}

function Get-DirectorySize {
    param([string]$Path)
    if (Test-Path $Path) {
        $size = (Get-ChildItem -Path $Path -Recurse -File | Measure-Object -Property Length -Sum).Sum
        return [math]::Round($size / 1MB, 2)
    }
    return 0
}

function Create-TempDirectory {
    $tempDir = Join-Path $env:TEMP "PT-System-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    return $tempDir
}

function Copy-ProductionFiles {
    param([string]$TempDir)
    
    Write-Info "Copying production files..."
    
    # Create distribution structure
    $distDir = Join-Path $tempDir "PT-System"
    New-Item -ItemType Directory -Path $distDir -Force | Out-Null
    
    # Copy essential files
    $essentialFiles = @(
        "deploy-windows.bat",
        "deploy-windows.ps1",
        "install-service.ps1",
        "start.bat",
        ".env.production",
        "README.md"
    )
    
    foreach ($file in $essentialFiles) {
        if (Test-Path $file) {
            Copy-Item $file $distDir -Force
            Write-Success "Copied: $file"
        } else {
            Write-Warning "File not found: $file"
        }
    }
    
    # Copy .env.production as .env
    if (Test-Path ".env.production") {
        Copy-Item ".env.production" (Join-Path $distDir ".env") -Force
        Write-Success "Copied .env.production as .env"
    }
    
    # Copy backend (production files only)
    Write-Info "Copying backend files..."
    $backendDist = Join-Path $distDir "backend"
    New-Item -ItemType Directory -Path $backendDist -Force | Out-Null
    
    # Backend essential files
    $backendFiles = @(
        "server.js",
        "package.json",
        "package.production.json"
    )
    
    foreach ($file in $backendFiles) {
        $sourcePath = Join-Path "backend" $file
        if (Test-Path $sourcePath) {
            Copy-Item $sourcePath $backendDist -Force
        }
    }
    
    # Copy backend directories
    $backendDirs = @(
        "config",
        "controllers",
        "middleware",
        "models",
        "routes",
        "services",
        "shared",
        "utils"
    )
    
    foreach ($dir in $backendDirs) {
        $sourcePath = Join-Path "backend" $dir
        if (Test-Path $sourcePath) {
            Copy-Item $sourcePath $backendDist -Recurse -Force
            Write-Success "Copied backend directory: $dir"
        }
    }
    
    # Copy frontend build
    Write-Info "Copying frontend build..."
    $frontendDist = Join-Path $distDir "frontend"
    New-Item -ItemType Directory -Path $frontendDist -Force | Out-Null
    
    if (Test-Path "frontend\build") {
        Copy-Item "frontend\build" $frontendDist -Recurse -Force
        Write-Success "Copied frontend build"
    }
    
    # Copy frontend package.json for serving
    if (Test-Path "frontend\package.json") {
        Copy-Item "frontend\package.json" $frontendDist -Force
    }
    
    # Copy essential directories
    $essentialDirs = @(
        "config",
        "docs"
    )
    
    foreach ($dir in $essentialDirs) {
        if (Test-Path $dir) {
            Copy-Item $dir $distDir -Recurse -Force
            Write-Success "Copied directory: $dir"
        }
    }
    
    # Create empty directories for runtime
    $runtimeDirs = @(
        "logs",
        "uploads",
        "backups"
    )
    
    foreach ($dir in $runtimeDirs) {
        $dirPath = Join-Path $distDir $dir
        New-Item -ItemType Directory -Path $dirPath -Force | Out-Null
        # Create .gitkeep file
        New-Item -ItemType File -Path (Join-Path $dirPath ".gitkeep") -Force | Out-Null
    }
    
    Write-Success "Runtime directories created"
    
    return $distDir
}

function Copy-SourceFiles {
    param([string]$DistDir)
    
    if ($IncludeSource) {
        Write-Info "Including source files for development deployment..."
        
        # Copy frontend source
        if (Test-Path "frontend\src") {
            $frontendSrc = Join-Path $DistDir "frontend\src"
            Copy-Item "frontend\src" $frontendSrc -Recurse -Force
            Write-Success "Copied frontend source files"
        }
        
        if (Test-Path "frontend\public") {
            $frontendPublic = Join-Path $DistDir "frontend\public"
            Copy-Item "frontend\public" $frontendPublic -Recurse -Force
            Write-Success "Copied frontend public files"
        }
        
        # Copy additional frontend files
        $frontendFiles = @(
            "tailwind.config.js",
            "postcss.config.js"
        )
        
        foreach ($file in $frontendFiles) {
            $sourcePath = Join-Path "frontend" $file
            if (Test-Path $sourcePath) {
                Copy-Item $sourcePath (Join-Path $DistDir "frontend") -Force
            }
        }
    }
}

function Create-InstallationGuide {
    param([string]$DistDir)
    
    Write-Info "Creating installation guide..."
    
    $installGuide = @"
# PT System - Windows Server Installation Guide

## System Requirements
- Windows Server 2016 or later
- Node.js 18.0.0 or higher
- MongoDB 5.0 or higher (local or remote)
- 4GB RAM minimum, 8GB recommended
- 10GB free disk space

## Quick Installation

### Step 1: Extract Files
Extract the PT-System distribution package to your desired location (e.g., C:\PT-System)

### Step 2: Install Node.js
1. Download Node.js from https://nodejs.org/
2. Install Node.js with default settings
3. Verify installation: Open Command Prompt and run `node --version`

### Step 3: Install MongoDB (if not already installed)
1. Download MongoDB Community Server from https://www.mongodb.com/
2. Install with default settings
3. Start MongoDB service

### Step 4: Deploy Application
1. Open Command Prompt as Administrator
2. Navigate to the PT-System directory
3. Run the deployment script:
   ```
   deploy-windows.bat
   ```

### Step 5: Access Application
- Open web browser and go to: http://localhost:3016
- Login with demo credentials:
  - Administrator: <EMAIL> / password123
  - Therapist: <EMAIL> / password123

## Alternative Installation Methods

### PowerShell Deployment
```powershell
.\deploy-windows.ps1
```

### Windows Service Installation
```powershell
.\install-service.ps1
```

### Manual Installation
1. Install backend dependencies:
   ```
   cd backend
   npm install --production
   ```

2. Start application:
   ```
   .\start.bat
   ```

## Configuration

### Environment Configuration
Edit the `.env` file to configure:
- Database connection
- Security settings
- Feature flags
- Integration settings

### Important Security Notes
1. Change default JWT_SECRET in .env file
2. Change default ENCRYPTION_KEY in .env file
3. Change default SESSION_SECRET in .env file
4. Update admin password after first login

## Troubleshooting

### Common Issues
1. **Port 3016 already in use**: Change PORT in .env file
2. **MongoDB connection failed**: Check MongoDB service is running
3. **Permission denied**: Run Command Prompt as Administrator

### Log Files
- Application logs: `logs/application.log`
- Error logs: `logs/pt-system-error.log`

### Support
For technical support, please contact the PT System team.

## Production Checklist
- [ ] Node.js installed
- [ ] MongoDB installed and running
- [ ] Application deployed successfully
- [ ] Environment variables configured
- [ ] Security settings updated
- [ ] Admin password changed
- [ ] Backup strategy implemented
- [ ] Firewall configured (if needed)
- [ ] SSL certificate installed (for production)

## Performance Optimization
- Use PM2 for process management (included in service installation)
- Configure MongoDB for production
- Set up regular backups
- Monitor system resources
- Configure log rotation

---
PT System v1.0.0 - Physical Therapy Management System
"@
    
    $installGuide | Out-File -FilePath (Join-Path $DistDir "INSTALLATION-GUIDE.md") -Encoding UTF8
    Write-Success "Created installation guide"
}

function Create-Archive {
    param([string]$DistDir, [string]$OutputPath)
    
    Write-Info "Creating distribution archive..."
    
    try {
        # Remove existing archive if it exists
        if (Test-Path $OutputPath) {
            Remove-Item $OutputPath -Force
        }
        
        # Create ZIP archive
        Compress-Archive -Path "$DistDir\*" -DestinationPath $OutputPath -CompressionLevel Optimal
        
        $archiveSize = [math]::Round((Get-Item $OutputPath).Length / 1MB, 2)
        Write-Success "Distribution package created: $OutputPath ($archiveSize MB)"
        
        return $true
    }
    catch {
        Write-Error "Failed to create archive: $($_.Exception.Message)"
        return $false
    }
}

function Show-Summary {
    param([string]$OutputPath, [string]$DistDir)
    
    Write-Host ""
    Write-Host "========================================"
    Write-Host "   Distribution Package Summary"
    Write-Host "========================================"
    Write-Host ""
    
    if (Test-Path $OutputPath) {
        $archiveSize = [math]::Round((Get-Item $OutputPath).Length / 1MB, 2)
        Write-Success "Package created successfully!"
        Write-Info "Output file: $OutputPath"
        Write-Info "Package size: $archiveSize MB"
    }
    
    $distSize = Get-DirectorySize $DistDir
    Write-Info "Uncompressed size: $distSize MB"
    
    Write-Host ""
    Write-Info "Package contents:"
    Write-Info "- Backend application (Node.js)"
    Write-Info "- Frontend build (React)"
    Write-Info "- Deployment scripts (Windows)"
    Write-Info "- Configuration files"
    Write-Info "- Installation guide"
    
    if ($IncludeSource) {
        Write-Info "- Source files (development)"
    }
    
    Write-Host ""
    Write-Info "Next steps:"
    Write-Info "1. Transfer $OutputPath to your Windows Server"
    Write-Info "2. Extract the archive"
    Write-Info "3. Follow the INSTALLATION-GUIDE.md"
    Write-Info "4. Run deploy-windows.bat"
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Host ""
Write-Host "========================================"
Write-Host "   PT System - Distribution Creator"
Write-Host "========================================"
Write-Host ""

Test-Prerequisites

$tempDir = Create-TempDirectory
Write-Info "Using temporary directory: $tempDir"

try {
    $distDir = Copy-ProductionFiles $tempDir
    Copy-SourceFiles $distDir
    Create-InstallationGuide $distDir
    
    $success = Create-Archive $distDir $OutputPath
    
    if ($success) {
        Show-Summary $OutputPath $distDir
    }
}
finally {
    # Cleanup temporary directory
    if (Test-Path $tempDir) {
        Remove-Item $tempDir -Recurse -Force
        Write-Info "Cleaned up temporary files"
    }
}
