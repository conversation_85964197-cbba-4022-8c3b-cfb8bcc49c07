# PT System - Windows Service Installation Script
# This script installs PT System as a Windows Service using PM2

param(
    [string]$Action = "install",
    [switch]$Help
)

# Colors for output
$Red = "`e[91m"
$Green = "`e[92m"
$Yellow = "`e[93m"
$Blue = "`e[94m"
$NC = "`e[0m"

function Write-Info {
    param([string]$Message)
    Write-Host "${Blue}[INFO]${NC} $Message"
}

function Write-Success {
    param([string]$Message)
    Write-Host "${Green}[SUCCESS]${NC} $Message"
}

function Write-Warning {
    param([string]$Message)
    Write-Host "${Yellow}[WARNING]${NC} $Message"
}

function Write-Error {
    param([string]$Message)
    Write-Host "${Red}[ERROR]${NC} $Message"
}

function Show-Help {
    Write-Host ""
    Write-Host "PT System - Windows Service Installation"
    Write-Host "========================================"
    Write-Host ""
    Write-Host "Usage: .\install-service.ps1 [Action]"
    Write-Host ""
    Write-Host "Actions:"
    Write-Host "  install   - Install PT System as Windows Service (default)"
    Write-Host "  uninstall - Remove PT System Windows Service"
    Write-Host "  start     - Start PT System service"
    Write-Host "  stop      - Stop PT System service"
    Write-Host "  restart   - Restart PT System service"
    Write-Host "  status    - Check service status"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\install-service.ps1"
    Write-Host "  .\install-service.ps1 start"
    Write-Host "  .\install-service.ps1 uninstall"
    Write-Host ""
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Install-PM2 {
    Write-Info "Checking PM2 installation..."
    
    try {
        $pm2Version = npm list -g pm2 --depth=0 2>$null
        if ($pm2Version -match "pm2@") {
            Write-Success "PM2 is already installed"
            return
        }
    }
    catch {
        # PM2 not found, continue with installation
    }
    
    Write-Info "Installing PM2 globally..."
    try {
        npm install -g pm2
        npm install -g pm2-windows-service
        Write-Success "PM2 installed successfully"
    }
    catch {
        Write-Error "Failed to install PM2"
        exit 1
    }
}

function Install-Service {
    if (!(Test-Administrator)) {
        Write-Error "This script must be run as Administrator to install Windows Service"
        Write-Info "Please run PowerShell as Administrator and try again"
        exit 1
    }
    
    Write-Info "Installing PT System as Windows Service..."
    
    # Install PM2
    Install-PM2
    
    # Create PM2 ecosystem file
    $ecosystemConfig = @"
module.exports = {
  apps: [{
    name: 'pt-system',
    script: 'server.js',
    cwd: '$PWD\backend',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3016
    },
    error_file: '$PWD\logs\pt-system-error.log',
    out_file: '$PWD\logs\pt-system-out.log',
    log_file: '$PWD\logs\pt-system-combined.log',
    time: true
  }]
};
"@
    
    $ecosystemConfig | Out-File -FilePath "ecosystem.config.js" -Encoding UTF8
    Write-Success "Created PM2 ecosystem configuration"
    
    # Start application with PM2
    try {
        pm2 start ecosystem.config.js
        pm2 save
        Write-Success "PT System started with PM2"
    }
    catch {
        Write-Error "Failed to start PT System with PM2"
        exit 1
    }
    
    # Install PM2 as Windows Service
    try {
        pm2-service-install -n "PT-System"
        Write-Success "PT System installed as Windows Service"
        
        # Start the service
        Start-Service -Name "PT-System"
        Write-Success "PT System service started"
        
        Write-Host ""
        Write-Success "Installation completed successfully!"
        Write-Info "Service Name: PT-System"
        Write-Info "Application URL: http://localhost:3016"
        Write-Info "Service will start automatically on system boot"
        Write-Host ""
    }
    catch {
        Write-Error "Failed to install Windows Service"
        Write-Warning "You can still use PM2 directly: pm2 start ecosystem.config.js"
    }
}

function Uninstall-Service {
    if (!(Test-Administrator)) {
        Write-Error "This script must be run as Administrator to uninstall Windows Service"
        exit 1
    }
    
    Write-Info "Uninstalling PT System Windows Service..."
    
    try {
        # Stop and remove PM2 processes
        pm2 stop all
        pm2 delete all
        pm2 kill
        
        # Uninstall Windows Service
        pm2-service-uninstall
        
        Write-Success "PT System service uninstalled successfully"
    }
    catch {
        Write-Warning "Some components may not have been removed completely"
        Write-Info "You may need to manually remove the Windows Service"
    }
}

function Start-PTService {
    Write-Info "Starting PT System service..."
    
    try {
        Start-Service -Name "PT-System"
        Write-Success "PT System service started"
    }
    catch {
        Write-Warning "Windows Service not found, trying PM2..."
        try {
            pm2 start ecosystem.config.js
            Write-Success "PT System started with PM2"
        }
        catch {
            Write-Error "Failed to start PT System"
        }
    }
}

function Stop-PTService {
    Write-Info "Stopping PT System service..."
    
    try {
        Stop-Service -Name "PT-System"
        Write-Success "PT System service stopped"
    }
    catch {
        Write-Warning "Windows Service not found, trying PM2..."
        try {
            pm2 stop all
            Write-Success "PT System stopped with PM2"
        }
        catch {
            Write-Error "Failed to stop PT System"
        }
    }
}

function Restart-PTService {
    Write-Info "Restarting PT System service..."
    Stop-PTService
    Start-Sleep -Seconds 3
    Start-PTService
}

function Get-ServiceStatus {
    Write-Info "Checking PT System service status..."
    
    # Check Windows Service
    try {
        $service = Get-Service -Name "PT-System" -ErrorAction Stop
        Write-Info "Windows Service Status: $($service.Status)"
    }
    catch {
        Write-Warning "Windows Service not found"
    }
    
    # Check PM2 processes
    try {
        $pm2List = pm2 jlist | ConvertFrom-Json
        if ($pm2List) {
            foreach ($app in $pm2List) {
                if ($app.name -eq "pt-system") {
                    Write-Info "PM2 Process: $($app.name) - Status: $($app.pm2_env.status)"
                    Write-Info "CPU: $($app.monit.cpu)% - Memory: $([math]::Round($app.monit.memory/1MB, 2)) MB"
                }
            }
        } else {
            Write-Warning "No PM2 processes found"
        }
    }
    catch {
        Write-Warning "PM2 not available or no processes running"
    }
    
    # Check if application is responding
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3016/health" -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Success "PT System is running and healthy"
        }
    }
    catch {
        Write-Warning "PT System is not responding on port 3016"
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

switch ($Action.ToLower()) {
    "install" { Install-Service }
    "uninstall" { Uninstall-Service }
    "start" { Start-PTService }
    "stop" { Stop-PTService }
    "restart" { Restart-PTService }
    "status" { Get-ServiceStatus }
    default {
        Write-Error "Unknown action: $Action"
        Show-Help
        exit 1
    }
}
