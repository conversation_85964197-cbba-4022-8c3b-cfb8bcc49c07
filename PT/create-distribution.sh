#!/bin/bash

# PT System - Distribution Package Creator for macOS/Linux
# This script creates a deployment-ready package for Windows Server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo ""
echo "========================================"
echo "   PT System - Distribution Creator"
echo "========================================"
echo ""

# Check if frontend build exists
if [ ! -d "frontend/build" ]; then
    print_error "Frontend build not found. Please run 'npm run build' in the frontend directory first."
    exit 1
fi

# Create temporary directory
TEMP_DIR="./PT-System-Distribution-$(date +%Y%m%d-%H%M%S)"
DIST_DIR="$TEMP_DIR/PT-System"

print_info "Creating distribution directory: $DIST_DIR"
mkdir -p "$DIST_DIR"

# Copy essential files
print_info "Copying essential files..."
cp deploy-windows.bat "$DIST_DIR/" 2>/dev/null || print_warning "deploy-windows.bat not found"
cp deploy-windows.ps1 "$DIST_DIR/" 2>/dev/null || print_warning "deploy-windows.ps1 not found"
cp install-service.ps1 "$DIST_DIR/" 2>/dev/null || print_warning "install-service.ps1 not found"
cp start.bat "$DIST_DIR/" 2>/dev/null || print_warning "start.bat not found"
cp .env.production "$DIST_DIR/.env" 2>/dev/null || print_warning ".env.production not found"

# Copy backend files
print_info "Copying backend files..."
mkdir -p "$DIST_DIR/backend"

# Copy backend essential files
cp backend/server.js "$DIST_DIR/backend/" 2>/dev/null || print_error "backend/server.js not found"
cp backend/package.json "$DIST_DIR/backend/" 2>/dev/null || print_error "backend/package.json not found"
cp backend/package.production.json "$DIST_DIR/backend/" 2>/dev/null || print_warning "backend/package.production.json not found"

# Copy backend directories
for dir in config controllers middleware models routes services shared utils; do
    if [ -d "backend/$dir" ]; then
        cp -r "backend/$dir" "$DIST_DIR/backend/"
        print_success "Copied backend directory: $dir"
    else
        print_warning "Backend directory not found: $dir"
    fi
done

# Copy frontend build
print_info "Copying frontend build..."
mkdir -p "$DIST_DIR/frontend"
if [ -d "frontend/build" ]; then
    cp -r frontend/build "$DIST_DIR/frontend/"
    print_success "Copied frontend build"
else
    print_error "Frontend build directory not found"
    exit 1
fi

# Copy frontend package.json for serving
cp frontend/package.json "$DIST_DIR/frontend/" 2>/dev/null || print_warning "frontend/package.json not found"

# Copy essential directories
for dir in config docs; do
    if [ -d "$dir" ]; then
        cp -r "$dir" "$DIST_DIR/"
        print_success "Copied directory: $dir"
    else
        print_warning "Directory not found: $dir"
    fi
done

# Create runtime directories
print_info "Creating runtime directories..."
for dir in logs uploads backups; do
    mkdir -p "$DIST_DIR/$dir"
    touch "$DIST_DIR/$dir/.gitkeep"
done
print_success "Runtime directories created"

# Create installation guide
print_info "Creating installation guide..."
cat > "$DIST_DIR/INSTALLATION-GUIDE.md" << 'EOF'
# PT System - Windows Server Installation Guide

## System Requirements
- Windows Server 2016 or later
- Node.js 18.0.0 or higher
- MongoDB 5.0 or higher (local or remote)
- 4GB RAM minimum, 8GB recommended
- 10GB free disk space

## Quick Installation

### Step 1: Extract Files
Extract the PT-System distribution package to your desired location (e.g., C:\PT-System)

### Step 2: Install Node.js
1. Download Node.js from https://nodejs.org/
2. Install Node.js with default settings
3. Verify installation: Open Command Prompt and run `node --version`

### Step 3: Install MongoDB (if not already installed)
1. Download MongoDB Community Server from https://www.mongodb.com/
2. Install with default settings
3. Start MongoDB service

### Step 4: Deploy Application
1. Open Command Prompt as Administrator
2. Navigate to the PT-System directory
3. Run the deployment script:
   ```
   deploy-windows.bat
   ```

### Step 5: Access Application
- Open web browser and go to: http://localhost:3016
- Login with demo credentials:
  - Administrator: <EMAIL> / password123
  - Therapist: <EMAIL> / password123

## Alternative Installation Methods

### PowerShell Deployment
```powershell
.\deploy-windows.ps1
```

### Windows Service Installation
```powershell
.\install-service.ps1
```

### Manual Installation
1. Install backend dependencies:
   ```
   cd backend
   npm install --production
   ```

2. Start application:
   ```
   .\start.bat
   ```

## Configuration

### Environment Configuration
Edit the `.env` file to configure:
- Database connection
- Security settings
- Feature flags
- Integration settings

### Important Security Notes
1. Change default JWT_SECRET in .env file
2. Change default ENCRYPTION_KEY in .env file
3. Change default SESSION_SECRET in .env file
4. Update admin password after first login

## Troubleshooting

### Common Issues
1. **Port 3016 already in use**: Change PORT in .env file
2. **MongoDB connection failed**: Check MongoDB service is running
3. **Permission denied**: Run Command Prompt as Administrator

### Log Files
- Application logs: `logs/application.log`
- Error logs: `logs/pt-system-error.log`

### Support
For technical support, please contact the PT System team.

---
PT System v1.0.0 - Physical Therapy Management System
EOF

print_success "Created installation guide"

# Create production README
print_info "Creating production README..."
cat > "$DIST_DIR/README.md" << 'EOF'
# PT System - Production Deployment

## Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- MongoDB (local or remote)

### Installation & Startup

#### Option 1: Quick Start (Recommended)
1. Run the deployment script:
   ```
   .\deploy-windows.bat
   ```

#### Option 2: PowerShell Script
1. Run the PowerShell deployment script:
   ```
   .\deploy-windows.ps1
   ```

#### Option 3: Manual Start
1. Install backend dependencies:
   ```
   cd backend
   npm install --production
   ```

2. Start the application:
   ```
   .\start.bat
   ```

### Access the Application
- Application URL: http://localhost:3016
- API Documentation: http://localhost:3016/api-docs

### Demo Credentials
- Administrator: <EMAIL> / password123
- Therapist: <EMAIL> / password123

### Windows Service Installation
To install as a Windows Service:
```
.\install-service.ps1
```

### Configuration
Edit the `.env` file to configure database connection and other settings.

### Support
For technical support, please contact the PT System team.
EOF

print_success "Created production README"

# Create ZIP archive
print_info "Creating distribution archive..."
ARCHIVE_NAME="PT-System-Distribution-$(date +%Y%m%d-%H%M%S).zip"

if command -v zip >/dev/null 2>&1; then
    cd "$TEMP_DIR"
    zip -r "../$ARCHIVE_NAME" PT-System/
    cd ..
    
    ARCHIVE_SIZE=$(du -h "$ARCHIVE_NAME" | cut -f1)
    print_success "Distribution package created: $ARCHIVE_NAME ($ARCHIVE_SIZE)"
else
    print_warning "zip command not found. Archive not created."
    print_info "Distribution files are available in: $DIST_DIR"
fi

# Show summary
echo ""
echo "========================================"
echo "   Distribution Package Summary"
echo "========================================"
echo ""

if [ -f "$ARCHIVE_NAME" ]; then
    print_success "Package created successfully!"
    print_info "Output file: $ARCHIVE_NAME"
fi

DIST_SIZE=$(du -sh "$DIST_DIR" | cut -f1)
print_info "Distribution size: $DIST_SIZE"

echo ""
print_info "Package contents:"
print_info "- Backend application (Node.js)"
print_info "- Frontend build (React)"
print_info "- Deployment scripts (Windows)"
print_info "- Configuration files"
print_info "- Installation guide"

echo ""
print_info "Next steps:"
if [ -f "$ARCHIVE_NAME" ]; then
    print_info "1. Transfer $ARCHIVE_NAME to your Windows Server"
else
    print_info "1. Create archive from $DIST_DIR and transfer to Windows Server"
fi
print_info "2. Extract the archive"
print_info "3. Follow the INSTALLATION-GUIDE.md"
print_info "4. Run deploy-windows.bat"
echo ""

print_success "Distribution creation completed!"
