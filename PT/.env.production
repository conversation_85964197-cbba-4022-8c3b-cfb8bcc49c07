# PT System - Production Environment Configuration
# Copy this file to .env and update with your actual values

# ==============================================
# APPLICATION CONFIGURATION
# ==============================================
NODE_ENV=production
PORT=3016
ALLOWED_ORIGINS=http://localhost:3016,http://127.0.0.1:3016

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
# MongoDB Configuration (Default local setup)
MONGODB_URI=mongodb://localhost:27017/pt_system

# Alternative PostgreSQL Configuration (if needed)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=pt_system
DB_USER=postgres
DB_PASSWORD=your_secure_database_password

# ==============================================
# REDIS CONFIGURATION (Optional)
# ==============================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# ==============================================
# SECURITY CONFIGURATION
# ==============================================
# JWT Configuration
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters_change_this
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Encryption key for sensitive data
ENCRYPTION_KEY=your_32_character_encryption_key_change_this

# Session configuration
SESSION_SECRET=your_session_secret_key_change_this
SESSION_TIMEOUT=3600

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==============================================
# FILE STORAGE CONFIGURATION
# ==============================================
# Local storage path
UPLOAD_PATH=./uploads

# ==============================================
# LOGGING CONFIGURATION
# ==============================================
LOG_LEVEL=info
LOG_FILE_PATH=./logs/application.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# ==============================================
# MONITORING AND HEALTH CHECKS
# ==============================================
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true

# ==============================================
# BACKUP CONFIGURATION
# ==============================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ==============================================
# COMPLIANCE AND AUDIT
# ==============================================
# Data retention
DATA_RETENTION_DAYS=2555
AUDIT_LOG_RETENTION_DAYS=3650
ANONYMIZATION_ENABLED=true

# Audit trail configuration
AUDIT_ENABLED=true
AUDIT_SENSITIVE_FIELDS=password,ssn,medical_record

# ==============================================
# FEATURE FLAGS
# ==============================================
FEATURE_AI_ENABLED=true
FEATURE_VOICE_INPUT=true
FEATURE_PREDICTIVE_ANALYTICS=true
FEATURE_MULTILINGUAL=true

# ==============================================
# LOCALIZATION
# ==============================================
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
TIMEZONE=Asia/Riyadh
DATE_FORMAT=DD/MM/YYYY
TIME_FORMAT=HH:mm

# ==============================================
# PERFORMANCE CONFIGURATION
# ==============================================
# Database connection pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# Cache configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=100mb

# ==============================================
# OPTIONAL INTEGRATIONS
# ==============================================
# Email Configuration (Optional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_email_password

# AI Service Configuration (Optional)
# OPENAI_API_KEY=sk-your_openai_api_key_here

# Saudi Arabia Integration (Optional)
# NPHIES_BASE_URL=https://api.nphies.sa
# NPHIES_CLIENT_ID=your_nphies_client_id
# NPHIES_CLIENT_SECRET=your_nphies_client_secret

# SMS Configuration (Optional)
# SMS_PROVIDER=stc
# SMS_API_KEY=your_sms_api_key
# SMS_SENDER_ID=PT_SYSTEM
