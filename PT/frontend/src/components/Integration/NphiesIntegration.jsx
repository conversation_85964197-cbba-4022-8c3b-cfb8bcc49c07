import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import toast from 'react-hot-toast';

const NphiesIntegration = ({ patientId }) => {
  const [nphiesData, setNphiesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [visaForm, setVisaForm] = useState({
    visaNumber: '',
    visaType: 'work',
    serviceIds: []
  });
  const [showVisaForm, setShowVisaForm] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    if (patientId) {
      loadNphiesStatus();
    }
  }, [patientId]);

  const loadNphiesStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/nphies/status/${patientId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setNphiesData(data.data);
      } else if (response.status === 404) {
        // No NPHIES integration found
        setNphiesData(null);
      } else {
        throw new Error('Failed to load NPHIES status');
      }
    } catch (error) {
      console.error('Error loading NPHIES status:', error);
      toast.error('Failed to load NPHIES integration status');
    } finally {
      setLoading(false);
    }
  };

  const verifyEligibility = async () => {
    try {
      const response = await fetch(`/api/v1/nphies/eligibility/${patientId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Eligibility verified successfully');
        setNphiesData(prev => ({
          ...prev,
          eligibilityStatus: data.data.eligibilityStatus,
          eligibilityResponse: data.data.eligibilityResponse
        }));
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to verify eligibility');
      }
    } catch (error) {
      console.error('Error verifying eligibility:', error);
      toast.error(error.message || 'Failed to verify eligibility');
    }
  };

  const linkVisa = async () => {
    if (!visaForm.visaNumber) {
      toast.error('Please enter visa number');
      return;
    }

    try {
      const response = await fetch('/api/v1/nphies/visa/link', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          patientId,
          ...visaForm
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Visa linked successfully');
        setNphiesData(prev => ({
          ...prev,
          visaInfo: data.data.visaInfo
        }));
        setShowVisaForm(false);
        setVisaForm({ visaNumber: '', visaType: 'work', serviceIds: [] });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to link visa');
      }
    } catch (error) {
      console.error('Error linking visa:', error);
      toast.error(error.message || 'Failed to link visa');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'verified':
      case 'active':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'rejected':
      case 'expired':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-6">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">
          {t('loadingNphiesData', 'Loading NPHIES data...')}
        </span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          <i className="fas fa-shield-alt text-blue-600 dark:text-blue-400 mr-2"></i>
          {t('nphiesIntegration', 'NPHIES Integration')}
        </h3>
        
        {nphiesData && (
          <button
            onClick={verifyEligibility}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            <i className="fas fa-sync mr-1"></i>
            {t('verifyEligibility', 'Verify Eligibility')}
          </button>
        )}
      </div>

      {!nphiesData ? (
        <div className="text-center py-8">
          <i className="fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"></i>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('noNphiesIntegration', 'No NPHIES integration found for this patient')}
          </p>
          <button
            onClick={() => toast('NPHIES integration setup coming soon')}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            {t('setupIntegration', 'Setup Integration')}
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Eligibility Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                {t('eligibilityStatus', 'Eligibility Status')}
              </h4>
              <span className={`px-2 py-1 rounded text-sm font-medium ${getStatusColor(nphiesData.eligibilityStatus)}`}>
                {t(nphiesData.eligibilityStatus, nphiesData.eligibilityStatus)}
              </span>
              {nphiesData.lastUpdated && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  {t('lastUpdated', 'Last Updated')}: {new Date(nphiesData.lastUpdated).toLocaleDateString()}
                </p>
              )}
            </div>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                {t('activeEligibility', 'Active Eligibility')}
              </h4>
              <div className="flex items-center">
                {nphiesData.hasActiveEligibility ? (
                  <>
                    <i className="fas fa-check-circle text-green-600 mr-2"></i>
                    <span className="text-green-600 dark:text-green-400">
                      {t('active', 'Active')}
                    </span>
                  </>
                ) : (
                  <>
                    <i className="fas fa-times-circle text-red-600 mr-2"></i>
                    <span className="text-red-600 dark:text-red-400">
                      {t('inactive', 'Inactive')}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Insurance Information */}
          {nphiesData.insuranceInfo && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                {t('insuranceInformation', 'Insurance Information')}
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('payerName', 'Payer Name')}:
                  </span>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {nphiesData.insuranceInfo.payerName}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('membershipNumber', 'Membership Number')}:
                  </span>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {nphiesData.insuranceInfo.membershipNumber}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('coverageType', 'Coverage Type')}:
                  </span>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {nphiesData.insuranceInfo.coverageType}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('copayAmount', 'Copay Amount')}:
                  </span>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {nphiesData.insuranceInfo.copayAmount} {t('sar', 'SAR')}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Visa Information */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900 dark:text-white">
                {t('visaInformation', 'Visa Information')}
              </h4>
              {!nphiesData.visaInfo && (
                <button
                  onClick={() => setShowVisaForm(true)}
                  className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                >
                  <i className="fas fa-plus mr-1"></i>
                  {t('linkVisa', 'Link Visa')}
                </button>
              )}
            </div>

            {nphiesData.visaInfo ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('visaNumber', 'Visa Number')}:
                  </span>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {nphiesData.visaInfo.visaNumber}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('visaType', 'Visa Type')}:
                  </span>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {t(nphiesData.visaInfo.visaType, nphiesData.visaInfo.visaType)}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('visaStatus', 'Status')}:
                  </span>
                  <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${getStatusColor(nphiesData.visaInfo.status)}`}>
                    {t(nphiesData.visaInfo.status, nphiesData.visaInfo.status)}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t('expiryDate', 'Expiry Date')}:
                  </span>
                  <span className="ml-2 text-gray-600 dark:text-gray-400">
                    {new Date(nphiesData.visaInfo.expiryDate).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                {t('noVisaLinked', 'No visa information linked')}
              </p>
            )}
          </div>

          {/* Claims Summary */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-3">
              {t('claimsSummary', 'Claims Summary')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {nphiesData.claimsCount || 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {t('totalClaims', 'Total Claims')}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {nphiesData.hasActivePreAuth ? '✓' : '✗'}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {t('preAuthorization', 'Pre-Authorization')}
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {nphiesData.hasActiveEligibility ? '✓' : '✗'}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {t('eligibilityActive', 'Eligibility Active')}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Visa Form Modal */}
      {showVisaForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {t('linkVisa', 'Link Visa')}
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('visaNumber', 'Visa Number')}
                </label>
                <input
                  type="text"
                  value={visaForm.visaNumber}
                  onChange={(e) => setVisaForm(prev => ({ ...prev, visaNumber: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder={t('enterVisaNumber', 'Enter visa number')}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('visaType', 'Visa Type')}
                </label>
                <select
                  value={visaForm.visaType}
                  onChange={(e) => setVisaForm(prev => ({ ...prev, visaType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="work">{t('work', 'Work')}</option>
                  <option value="visit">{t('visit', 'Visit')}</option>
                  <option value="residence">{t('residence', 'Residence')}</option>
                  <option value="hajj">{t('hajj', 'Hajj')}</option>
                  <option value="umrah">{t('umrah', 'Umrah')}</option>
                  <option value="transit">{t('transit', 'Transit')}</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowVisaForm(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
              >
                {t('cancel', 'Cancel')}
              </button>
              <button
                onClick={linkVisa}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                {t('linkVisa', 'Link Visa')}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default NphiesIntegration;
