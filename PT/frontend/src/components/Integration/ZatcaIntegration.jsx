import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import toast from 'react-hot-toast';

const ZatcaIntegration = ({ invoiceId }) => {
  const [zatcaStatus, setZatcaStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    loadZatcaStatus();
  }, []);

  const loadZatcaStatus = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/zatca/compliance', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setZatcaStatus(data.data);
      } else {
        throw new Error('Failed to load ZATCA status');
      }
    } catch (error) {
      console.error('Error loading ZATCA status:', error);
      toast.error('Failed to load ZATCA integration status');
    } finally {
      setLoading(false);
    }
  };

  const submitInvoiceToZatca = async () => {
    if (!invoiceId) {
      toast.error('No invoice selected');
      return;
    }

    setSubmitting(true);
    try {
      const response = await fetch('/api/v1/zatca/invoice/submit', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ invoiceId })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Invoice submitted to ZATCA successfully');
        
        // Show submission details
        toast.success(`ZATCA Invoice Number: ${data.data.zatcaInvoiceNumber}`);
        
        // Reload status
        loadZatcaStatus();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit invoice to ZATCA');
      }
    } catch (error) {
      console.error('Error submitting to ZATCA:', error);
      toast.error(error.message || 'Failed to submit invoice to ZATCA');
    } finally {
      setSubmitting(false);
    }
  };

  const generateTaxReport = async () => {
    try {
      const response = await fetch('/api/v1/zatca/report/generate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          reportType: 'monthly',
          period: {
            year: new Date().getFullYear(),
            month: new Date().getMonth() + 1
          }
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Tax report generated successfully');
        loadZatcaStatus();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to generate tax report');
      }
    } catch (error) {
      console.error('Error generating tax report:', error);
      toast.error(error.message || 'Failed to generate tax report');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'registered':
      case 'accepted':
      case 'active':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';
      case 'pending':
      case 'submitted':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'rejected':
      case 'suspended':
      case 'cancelled':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getComplianceLevel = (percentage) => {
    if (percentage >= 90) return { level: 'excellent', color: 'text-green-600', icon: 'fas fa-check-circle' };
    if (percentage >= 70) return { level: 'good', color: 'text-blue-600', icon: 'fas fa-info-circle' };
    if (percentage >= 50) return { level: 'fair', color: 'text-yellow-600', icon: 'fas fa-exclamation-triangle' };
    return { level: 'poor', color: 'text-red-600', icon: 'fas fa-times-circle' };
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-6">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">
          {t('loadingZatcaData', 'Loading ZATCA data...')}
        </span>
      </div>
    );
  }

  if (!zatcaStatus) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6">
        <div className="text-center py-8">
          <i className="fas fa-exclamation-triangle text-4xl text-yellow-500 mb-4"></i>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('noZatcaIntegration', 'ZATCA integration not configured')}
          </p>
          <button
            onClick={() => toast('ZATCA integration setup coming soon')}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            {t('setupIntegration', 'Setup Integration')}
          </button>
        </div>
      </div>
    );
  }

  const compliance = getComplianceLevel(zatcaStatus.overallCompliance);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          <i className="fas fa-receipt text-green-600 dark:text-green-400 mr-2"></i>
          {t('zatcaIntegration', 'ZATCA Integration')}
        </h3>
        
        <div className="flex space-x-2">
          {invoiceId && (
            <button
              onClick={submitInvoiceToZatca}
              disabled={submitting}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {submitting ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1 inline-block"></div>
                  {t('submitting', 'Submitting...')}
                </>
              ) : (
                <>
                  <i className="fas fa-upload mr-1"></i>
                  {t('submitToZatca', 'Submit to ZATCA')}
                </>
              )}
            </button>
          )}
          
          <button
            onClick={generateTaxReport}
            className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
          >
            <i className="fas fa-file-alt mr-1"></i>
            {t('generateReport', 'Generate Report')}
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Registration Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              {t('registrationStatus', 'Registration Status')}
            </h4>
            <span className={`px-2 py-1 rounded text-sm font-medium ${getStatusColor(zatcaStatus.registrationStatus)}`}>
              {t(zatcaStatus.registrationStatus, zatcaStatus.registrationStatus)}
            </span>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              {t('certificateStatus', 'Certificate Status')}
            </h4>
            <div className="flex items-center">
              {zatcaStatus.certificateValid ? (
                <>
                  <i className="fas fa-check-circle text-green-600 mr-2"></i>
                  <span className="text-green-600 dark:text-green-400">
                    {t('valid', 'Valid')}
                  </span>
                </>
              ) : (
                <>
                  <i className="fas fa-times-circle text-red-600 mr-2"></i>
                  <span className="text-red-600 dark:text-red-400">
                    {t('invalid', 'Invalid')}
                  </span>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Compliance Overview */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3">
            {t('complianceOverview', 'Compliance Overview')}
          </h4>
          
          <div className="flex items-center mb-3">
            <i className={`${compliance.icon} ${compliance.color} mr-2`}></i>
            <span className={`font-medium ${compliance.color}`}>
              {zatcaStatus.overallCompliance}% {t('compliant', 'Compliant')}
            </span>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              ({t(compliance.level, compliance.level)})
            </span>
          </div>
          
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                zatcaStatus.overallCompliance >= 90 ? 'bg-green-600' :
                zatcaStatus.overallCompliance >= 70 ? 'bg-blue-600' :
                zatcaStatus.overallCompliance >= 50 ? 'bg-yellow-600' : 'bg-red-600'
              }`}
              style={{ width: `${zatcaStatus.overallCompliance}%` }}
            ></div>
          </div>
        </div>

        {/* Submission Statistics */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h4 className="font-medium text-gray-900 dark:text-white mb-3">
            {t('submissionStatistics', 'Submission Statistics')}
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {zatcaStatus.totalInvoicesSubmitted}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {t('totalSubmitted', 'Total Submitted')}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {zatcaStatus.successfulSubmissions}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {t('successful', 'Successful')}
              </div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {zatcaStatus.failedSubmissions}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {t('failed', 'Failed')}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Compliance Checks */}
        {zatcaStatus.complianceChecks && zatcaStatus.complianceChecks.length > 0 && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-3">
              {t('recentComplianceChecks', 'Recent Compliance Checks')}
            </h4>
            
            <div className="space-y-2">
              {zatcaStatus.complianceChecks.map((check, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded">
                  <div className="flex items-center">
                    <i className={`fas ${
                      check.status === 'passed' ? 'fa-check-circle text-green-600' :
                      check.status === 'warning' ? 'fa-exclamation-triangle text-yellow-600' :
                      'fa-times-circle text-red-600'
                    } mr-2`}></i>
                    <span className="text-sm text-gray-900 dark:text-white">
                      {t(check.checkType, check.checkType)}
                    </span>
                  </div>
                  
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(check.status)}`}>
                    {t(check.status, check.status)}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Last Sync Information */}
        {zatcaStatus.lastSyncDate && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">
              {t('lastSyncInformation', 'Last Sync Information')}
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t('lastSyncDate', 'Last Sync')}: {new Date(zatcaStatus.lastSyncDate).toLocaleString()}
            </p>
          </div>
        )}

        {/* QR Code Information */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-start">
            <i className="fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2 mt-1"></i>
            <div>
              <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                {t('zatcaCompliance', 'ZATCA Compliance')}
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {t('zatcaComplianceInfo', 'All invoices are automatically generated with ZATCA-compliant QR codes and digital signatures. Tax calculations follow Saudi VAT regulations.')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZatcaIntegration;
