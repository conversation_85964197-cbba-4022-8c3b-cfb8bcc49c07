import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import SignatureCanvas from './SignatureCanvas';
import toast from 'react-hot-toast';

const SignatureDocument = ({ documentId, onSignatureComplete }) => {
  const [document, setDocument] = useState(null);
  const [loading, setLoading] = useState(true);
  const [signing, setSigning] = useState(false);
  const [selectedSigner, setSelectedSigner] = useState(null);
  const [signatureData, setSignatureData] = useState(null);
  const [authMethod, setAuthMethod] = useState('password');
  const [deviceInfo, setDeviceInfo] = useState({});
  const { t } = useTranslation();
  const { user } = useAuth();

  useEffect(() => {
    loadDocument();
    collectDeviceInfo();
  }, [documentId]);

  const loadDocument = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/signature/${documentId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDocument(data.data);
        
        // Find current user's signer record
        const userSigner = data.data.signers.find(signer => 
          signer.signerInfo.userId === user.id || 
          signer.signerInfo.email === user.email
        );
        
        if (userSigner && userSigner.status === 'pending') {
          setSelectedSigner(userSigner);
        }
      } else {
        throw new Error('Failed to load document');
      }
    } catch (error) {
      console.error('Error loading document:', error);
      toast.error('Failed to load signature document');
    } finally {
      setLoading(false);
    }
  };

  const collectDeviceInfo = () => {
    const info = {
      platform: navigator.platform,
      browser: navigator.userAgent,
      language: navigator.language,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    };
    setDeviceInfo(info);
  };

  const handleSignDocument = async () => {
    if (!selectedSigner || !signatureData) {
      toast.error('Please provide your signature');
      return;
    }

    setSigning(true);
    try {
      // Get geolocation if available
      let geolocation = null;
      if (navigator.geolocation) {
        try {
          const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              timeout: 5000,
              enableHighAccuracy: false
            });
          });
          
          geolocation = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy
          };
        } catch (error) {
          console.log('Geolocation not available:', error);
        }
      }

      const signaturePayload = {
        signerId: selectedSigner._id,
        signatureImage: signatureData,
        authenticationMethod: authMethod,
        deviceInfo,
        geolocation,
        consentGiven: true
      };

      const response = await fetch(`/api/v1/signature/sign/${documentId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(signaturePayload)
      });

      if (response.ok) {
        const result = await response.json();
        toast.success('Document signed successfully');
        
        // Reload document to show updated status
        await loadDocument();
        
        // Notify parent component
        if (onSignatureComplete) {
          onSignatureComplete(result.data);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to sign document');
      }
    } catch (error) {
      console.error('Error signing document:', error);
      toast.error(error.message || 'Failed to sign document');
    } finally {
      setSigning(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'fully_signed':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';
      case 'partially_signed':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400';
      case 'pending_signatures':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400';
      case 'expired':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getSignerStatusIcon = (status) => {
    switch (status) {
      case 'signed':
        return <i className="fas fa-check-circle text-green-600"></i>;
      case 'pending':
        return <i className="fas fa-clock text-yellow-600"></i>;
      case 'rejected':
        return <i className="fas fa-times-circle text-red-600"></i>;
      case 'expired':
        return <i className="fas fa-exclamation-triangle text-red-600"></i>;
      default:
        return <i className="fas fa-question-circle text-gray-600"></i>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600 dark:text-gray-400">
          {t('loadingDocument', 'Loading document...')}
        </span>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="text-center p-8">
        <i className="fas fa-file-signature text-4xl text-gray-400 mb-4"></i>
        <p className="text-gray-500 dark:text-gray-400">
          {t('documentNotFound', 'Document not found')}
        </p>
      </div>
    );
  }

  const canSign = selectedSigner && selectedSigner.status === 'pending' && !document.isExpired;

  return (
    <div className="max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-200 dark:border-gray-600">
      {/* Document Header */}
      <div className="border-b border-gray-200 dark:border-gray-600 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {document.documentTitle}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('documentType', 'Document Type')}: {t(document.documentType, document.documentType)}
            </p>
          </div>
          
          <div className="text-right">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(document.workflow.status)}`}>
              {t(document.workflow.status, document.workflow.status)}
            </span>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {document.completionPercentage}% {t('complete', 'Complete')}
            </p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${document.completionPercentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Document Content */}
      <div className="p-6">
        <div className="prose dark:prose-invert max-w-none">
          <div 
            className="whitespace-pre-wrap text-gray-900 dark:text-white"
            dangerouslySetInnerHTML={{ __html: document.documentContent }}
          />
        </div>
      </div>

      {/* Signers Section */}
      <div className="border-t border-gray-200 dark:border-gray-600 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('signers', 'Signers')} ({document.workflow.completedSignatures}/{document.workflow.requiredSignatures})
        </h3>
        
        <div className="space-y-3">
          {document.signers.map((signer) => (
            <div key={signer._id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                {getSignerStatusIcon(signer.status)}
                <div className="ml-3">
                  <p className="font-medium text-gray-900 dark:text-white">
                    {signer.signerInfo.name}
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {t(signer.signerType, signer.signerType)}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(signer.status)}`}>
                  {t(signer.status, signer.status)}
                </span>
                {signer.signedAt && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {new Date(signer.signedAt).toLocaleString()}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Signature Section */}
      {canSign && (
        <div className="border-t border-gray-200 dark:border-gray-600 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            {t('yourSignature', 'Your Signature')}
          </h3>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <SignatureCanvas
                onSignatureChange={setSignatureData}
                disabled={signing}
                width={400}
                height={200}
              />
            </div>
            
            <div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {t('authenticationMethod', 'Authentication Method')}
                </label>
                <select
                  value={authMethod}
                  onChange={(e) => setAuthMethod(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="password">{t('password', 'Password')}</option>
                  <option value="otp">{t('otp', 'OTP')}</option>
                  <option value="biometric">{t('biometric', 'Biometric')}</option>
                </select>
              </div>
              
              <div className="mb-6">
                <div className="flex items-start">
                  <input
                    type="checkbox"
                    id="consent"
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    defaultChecked
                  />
                  <label htmlFor="consent" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    {t('signatureConsent', 'I consent to electronically sign this document and understand that this signature has the same legal effect as a handwritten signature.')}
                  </label>
                </div>
              </div>
              
              <button
                onClick={handleSignDocument}
                disabled={!signatureData || signing}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {signing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('signing', 'Signing...')}
                  </>
                ) : (
                  <>
                    <i className="fas fa-signature mr-2"></i>
                    {t('signDocument', 'Sign Document')}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Document Info */}
      <div className="border-t border-gray-200 dark:border-gray-600 p-6 bg-gray-50 dark:bg-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">
              {t('documentId', 'Document ID')}:
            </span>
            <span className="ml-2 text-gray-600 dark:text-gray-400">
              {document.documentId}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">
              {t('createdAt', 'Created')}:
            </span>
            <span className="ml-2 text-gray-600 dark:text-gray-400">
              {new Date(document.createdAt).toLocaleDateString()}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">
              {t('expiresAt', 'Expires')}:
            </span>
            <span className={`ml-2 ${document.isExpired ? 'text-red-600' : 'text-gray-600 dark:text-gray-400'}`}>
              {new Date(document.workflow.expiryDate).toLocaleDateString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignatureDocument;
