# PT System - Windows Server PowerShell Deployment Script
# This script deploys and starts the PT System on Windows Server

param(
    [string]$Action = "deploy",
    [switch]$Help
)

# Colors for output
$Red = "`e[91m"
$Green = "`e[92m"
$Yellow = "`e[93m"
$Blue = "`e[94m"
$NC = "`e[0m"

function Write-Info {
    param([string]$Message)
    Write-Host "${Blue}[INFO]${NC} $Message"
}

function Write-Success {
    param([string]$Message)
    Write-Host "${Green}[SUCCESS]${NC} $Message"
}

function Write-Warning {
    param([string]$Message)
    Write-Host "${Yellow}[WARNING]${NC} $Message"
}

function Write-Error {
    param([string]$Message)
    Write-Host "${Red}[ERROR]${NC} $Message"
}

function Show-Help {
    Write-Host ""
    Write-Host "PT System - Windows Deployment Script"
    Write-Host "======================================"
    Write-Host ""
    Write-Host "Usage: .\deploy-windows.ps1 [Action]"
    Write-Host ""
    Write-Host "Actions:"
    Write-Host "  deploy    - Full deployment (default)"
    Write-Host "  start     - Start the application"
    Write-Host "  stop      - Stop the application"
    Write-Host "  restart   - Restart the application"
    Write-Host "  status    - Check application status"
    Write-Host "  install   - Install dependencies only"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\deploy-windows.ps1"
    Write-Host "  .\deploy-windows.ps1 start"
    Write-Host "  .\deploy-windows.ps1 -Help"
    Write-Host ""
}

function Test-Prerequisites {
    Write-Info "Checking prerequisites..."
    
    # Check Node.js
    try {
        $nodeVersion = node --version
        Write-Success "Node.js version $nodeVersion detected"
    }
    catch {
        Write-Error "Node.js is not installed or not in PATH"
        Write-Error "Please install Node.js 18.0.0 or higher from https://nodejs.org/"
        exit 1
    }
    
    # Check npm
    try {
        $npmVersion = npm --version
        Write-Success "npm version $npmVersion detected"
    }
    catch {
        Write-Error "npm is not installed or not in PATH"
        exit 1
    }
    
    Write-Success "Prerequisites check completed"
}

function Install-Dependencies {
    Write-Info "Installing backend dependencies..."
    
    Set-Location backend
    
    if (Test-Path "package.production.json") {
        Copy-Item "package.production.json" "package.json" -Force
        Write-Info "Using production package.json"
    }
    
    try {
        npm install --production
        Write-Success "Backend dependencies installed"
    }
    catch {
        Write-Error "Failed to install backend dependencies"
        exit 1
    }
    
    Set-Location ..
}

function Initialize-Directories {
    Write-Info "Creating required directories..."
    
    $directories = @("logs", "uploads", "backups")
    foreach ($dir in $directories) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir | Out-Null
            Write-Success "Created directory: $dir"
        }
    }
}

function Setup-Environment {
    Write-Info "Setting up environment configuration..."
    
    if (!(Test-Path ".env")) {
        if (Test-Path "config\environment.example") {
            Copy-Item "config\environment.example" ".env"
            Write-Success "Environment file created from template"
            Write-Warning "Please review and update .env file with your configuration"
        } else {
            Write-Warning "No environment template found"
        }
    } else {
        Write-Success "Environment file already exists"
    }
}

function Start-Application {
    Write-Info "Starting PT System..."
    Write-Host ""
    Write-Info "Application will be available at: http://localhost:3016"
    Write-Info "API Documentation: http://localhost:3016/api-docs"
    Write-Host ""
    Write-Warning "Demo Credentials:"
    Write-Warning "Administrator: <EMAIL> / password123"
    Write-Warning "Therapist: <EMAIL> / password123"
    Write-Host ""
    Write-Info "Press Ctrl+C to stop the server"
    Write-Host ""
    
    Set-Location backend
    node server.js
}

function Stop-Application {
    Write-Info "Stopping PT System..."
    
    $processes = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.Path -like "*pt*" }
    
    if ($processes) {
        foreach ($process in $processes) {
            Stop-Process -Id $process.Id -Force
            Write-Success "Stopped process: $($process.Id)"
        }
    } else {
        Write-Warning "No PT System processes found running"
    }
}

function Get-ApplicationStatus {
    Write-Info "Checking PT System status..."
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3016/health" -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Success "PT System is running and healthy"
        }
    }
    catch {
        Write-Warning "PT System is not responding or not running"
    }
    
    $processes = Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.Path -like "*pt*" }
    if ($processes) {
        Write-Info "Found $($processes.Count) PT System process(es) running"
        foreach ($process in $processes) {
            Write-Info "Process ID: $($process.Id), Memory: $([math]::Round($process.WorkingSet64/1MB, 2)) MB"
        }
    } else {
        Write-Warning "No PT System processes found"
    }
}

function Deploy-Application {
    Write-Host ""
    Write-Host "========================================"
    Write-Host "   PT System - Windows Deployment"
    Write-Host "========================================"
    Write-Host ""
    
    Test-Prerequisites
    Install-Dependencies
    Initialize-Directories
    Setup-Environment
    
    Write-Host ""
    Write-Success "Deployment completed successfully!"
    Write-Host ""
    Write-Info "To start the application, run:"
    Write-Info ".\deploy-windows.ps1 start"
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

switch ($Action.ToLower()) {
    "deploy" { Deploy-Application }
    "start" { Start-Application }
    "stop" { Stop-Application }
    "restart" { 
        Stop-Application
        Start-Sleep -Seconds 3
        Start-Application
    }
    "status" { Get-ApplicationStatus }
    "install" { 
        Test-Prerequisites
        Install-Dependencies
    }
    default {
        Write-Error "Unknown action: $Action"
        Show-Help
        exit 1
    }
}
