# PT System - Cleanup Script for Deployment
# This script removes development files and reduces package size

param(
    [switch]$Help,
    [switch]$DryRun
)

# Colors for output
$Red = "`e[91m"
$Green = "`e[92m"
$Yellow = "`e[93m"
$Blue = "`e[94m"
$NC = "`e[0m"

function Write-Info {
    param([string]$Message)
    Write-Host "${Blue}[INFO]${NC} $Message"
}

function Write-Success {
    param([string]$Message)
    Write-Host "${Green}[SUCCESS]${NC} $Message"
}

function Write-Warning {
    param([string]$Message)
    Write-Host "${Yellow}[WARNING]${NC} $Message"
}

function Write-Error {
    param([string]$Message)
    Write-Host "${Red}[ERROR]${NC} $Message"
}

function Show-Help {
    Write-Host ""
    Write-Host "PT System - Cleanup for Deployment"
    Write-Host "=================================="
    Write-Host ""
    Write-Host "Usage: .\cleanup-for-deployment.ps1 [Options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -DryRun   Show what would be deleted without actually deleting"
    Write-Host "  -Help     Show this help message"
    Write-Host ""
    Write-Host "This script removes:"
    Write-Host "  - Development dependencies"
    Write-Host "  - Source maps and development files"
    Write-Host "  - Test files and documentation"
    Write-Host "  - Git history and development tools"
    Write-Host "  - Temporary and cache files"
    Write-Host ""
}

function Get-DirectorySize {
    param([string]$Path)
    if (Test-Path $Path) {
        $size = (Get-ChildItem -Path $Path -Recurse -File | Measure-Object -Property Length -Sum).Sum
        return [math]::Round($size / 1MB, 2)
    }
    return 0
}

function Remove-ItemSafely {
    param(
        [string]$Path,
        [string]$Description
    )
    
    if (Test-Path $Path) {
        $size = Get-DirectorySize $Path
        if ($DryRun) {
            Write-Warning "[DRY RUN] Would remove: $Description ($Path) - $size MB"
        } else {
            try {
                Remove-Item -Path $Path -Recurse -Force
                Write-Success "Removed: $Description - $size MB"
            }
            catch {
                Write-Error "Failed to remove: $Description - $($_.Exception.Message)"
            }
        }
    } else {
        Write-Info "Not found: $Description ($Path)"
    }
}

function Cleanup-Frontend {
    Write-Info "Cleaning up frontend..."
    
    Set-Location frontend
    
    # Remove development dependencies from node_modules
    Remove-ItemSafely "node_modules" "Frontend node_modules"
    
    # Remove source files (keep only build)
    if (Test-Path "build") {
        Remove-ItemSafely "src" "Frontend source files"
        Remove-ItemSafely "public" "Frontend public files (keeping build)"
    } else {
        Write-Warning "Build directory not found. Run 'npm run build' first!"
    }
    
    # Remove development files
    Remove-ItemSafely "package-lock.json" "Frontend package-lock.json"
    Remove-ItemSafely ".eslintrc.js" "ESLint configuration"
    Remove-ItemSafely ".eslintrc.json" "ESLint configuration"
    Remove-ItemSafely "postcss.config.js" "PostCSS configuration"
    Remove-ItemSafely "tailwind.config.js" "Tailwind configuration"
    
    Set-Location ..
}

function Cleanup-Backend {
    Write-Info "Cleaning up backend..."
    
    Set-Location backend
    
    # Remove development dependencies
    Remove-ItemSafely "node_modules" "Backend node_modules"
    
    # Remove test files
    Remove-ItemSafely "tests" "Test files"
    Remove-ItemSafely "__tests__" "Test files"
    Remove-ItemSafely "*.test.js" "Test files"
    Remove-ItemSafely "*.spec.js" "Test files"
    
    # Remove development files
    Remove-ItemSafely "package-lock.json" "Backend package-lock.json"
    Remove-ItemSafely ".eslintrc.js" "ESLint configuration"
    Remove-ItemSafely ".eslintrc.json" "ESLint configuration"
    Remove-ItemSafely "jest.config.js" "Jest configuration"
    Remove-ItemSafely "nodemon.json" "Nodemon configuration"
    
    # Use production package.json if available
    if (Test-Path "package.production.json") {
        if ($DryRun) {
            Write-Warning "[DRY RUN] Would replace package.json with production version"
        } else {
            Copy-Item "package.production.json" "package.json" -Force
            Write-Success "Replaced package.json with production version"
        }
    }
    
    Set-Location ..
}

function Cleanup-Root {
    Write-Info "Cleaning up root directory..."
    
    # Remove Git history
    Remove-ItemSafely ".git" "Git history"
    Remove-ItemSafely ".gitignore" "Git ignore file"
    Remove-ItemSafely ".github" "GitHub workflows"
    
    # Remove development documentation
    Remove-ItemSafely "README-NEW.md" "Development README"
    Remove-ItemSafely "DEPLOYMENT-GUIDE.md" "Deployment guide"
    Remove-ItemSafely "FEATURES-COMPLETED.md" "Features documentation"
    Remove-ItemSafely "PROJECT-SUMMARY.md" "Project summary"
    Remove-ItemSafely "IMPLEMENTATION-CHECKLIST.md" "Implementation checklist"
    Remove-ItemSafely "ADMIN_PANEL_RESTORATION.md" "Admin panel docs"
    Remove-ItemSafely "ADVANCED_REPORTING_GUIDE.md" "Reporting guide"
    Remove-ItemSafely "CARF_IMPLEMENTATION_GUIDE.md" "CARF guide"
    Remove-ItemSafely "CUSTOMER-INSTALLATION-GUIDE.md" "Installation guide"
    Remove-ItemSafely "EXECUTIVE-SUMMARY.md" "Executive summary"
    Remove-ItemSafely "FINAL-PROJECT-SUMMARY.md" "Final summary"
    Remove-ItemSafely "FINAL_IMPLEMENTATION_SUMMARY.md" "Implementation summary"
    Remove-ItemSafely "PROJECT_STATUS.md" "Project status"
    Remove-ItemSafely "PT_ASSESSMENT_SYSTEM_GUIDE.md" "Assessment guide"
    Remove-ItemSafely "QUICK-DISTRIBUTION-GUIDE.md" "Distribution guide"
    Remove-ItemSafely "REBRANDING.md" "Rebranding docs"
    Remove-ItemSafely "REMAINING-FEATURES.md" "Remaining features"
    Remove-ItemSafely "WHITEPAPER.md" "Whitepaper"
    Remove-ItemSafely "APPLICATION-DISTRIBUTION-GUIDE.md" "Distribution guide"
    
    # Remove Docker development files
    Remove-ItemSafely "docker-compose.saas.yml" "SaaS Docker compose"
    Remove-ItemSafely "docker-compose.onprem.yml" "On-premise Docker compose"
    Remove-ItemSafely "deployment-config.md" "Deployment config"
    
    # Remove development scripts
    Remove-ItemSafely "scripts" "Development scripts"
    
    # Remove docs (keep essential ones)
    if (Test-Path "docs") {
        Set-Location docs
        Remove-ItemSafely "DEPLOYMENT-NEW.md" "New deployment docs"
        Remove-ItemSafely "deployment.md" "Deployment docs"
        Set-Location ..
    }
    
    # Remove temporary files
    Remove-ItemSafely "*.log" "Log files"
    Remove-ItemSafely "*.tmp" "Temporary files"
    Remove-ItemSafely "node_modules" "Root node_modules"
    Remove-ItemSafely "package-lock.json" "Root package-lock.json"
}

function Create-ProductionReadme {
    Write-Info "Creating production README..."
    
    $productionReadme = @"
# PT System - Production Deployment

## Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- MongoDB (local or remote)

### Installation & Startup

#### Option 1: Quick Start (Recommended)
1. Run the deployment script:
   ```
   .\deploy-windows.bat
   ```

#### Option 2: PowerShell Script
1. Run the PowerShell deployment script:
   ```
   .\deploy-windows.ps1
   ```

#### Option 3: Manual Start
1. Install backend dependencies:
   ```
   cd backend
   npm install --production
   ```

2. Start the application:
   ```
   .\start.bat
   ```

### Access the Application
- Application URL: http://localhost:3016
- API Documentation: http://localhost:3016/api-docs

### Demo Credentials
- Administrator: <EMAIL> / password123
- Therapist: <EMAIL> / password123

### Windows Service Installation
To install as a Windows Service:
```
.\install-service.ps1
```

### Configuration
Edit the `.env` file to configure database connection and other settings.

### Support
For technical support, please contact the PT System team.
"@
    
    if ($DryRun) {
        Write-Warning "[DRY RUN] Would create production README.md"
    } else {
        $productionReadme | Out-File -FilePath "README.md" -Encoding UTF8
        Write-Success "Created production README.md"
    }
}

function Show-Summary {
    Write-Host ""
    Write-Host "========================================"
    Write-Host "   Cleanup Summary"
    Write-Host "========================================"
    Write-Host ""
    
    $totalSize = 0
    if (Test-Path "frontend") { $totalSize += Get-DirectorySize "frontend" }
    if (Test-Path "backend") { $totalSize += Get-DirectorySize "backend" }
    if (Test-Path "docs") { $totalSize += Get-DirectorySize "docs" }
    if (Test-Path "config") { $totalSize += Get-DirectorySize "config" }
    
    Write-Info "Current package size: $totalSize MB"
    
    if ($DryRun) {
        Write-Warning "This was a DRY RUN - no files were actually deleted"
        Write-Info "Run without -DryRun to perform actual cleanup"
    } else {
        Write-Success "Cleanup completed successfully!"
        Write-Info "Package is now ready for deployment"
    }
    
    Write-Host ""
    Write-Info "Next steps:"
    Write-Info "1. Test the application: .\start.bat"
    Write-Info "2. Create deployment package: Compress the entire folder"
    Write-Info "3. Transfer to Windows server"
    Write-Info "4. Run: .\deploy-windows.bat"
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Host ""
Write-Host "========================================"
Write-Host "   PT System - Cleanup for Deployment"
Write-Host "========================================"
Write-Host ""

if ($DryRun) {
    Write-Warning "DRY RUN MODE - No files will be deleted"
    Write-Host ""
}

Cleanup-Frontend
Cleanup-Backend
Cleanup-Root
Create-ProductionReadme
Show-Summary
