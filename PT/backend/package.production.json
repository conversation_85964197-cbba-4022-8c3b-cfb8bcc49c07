{"name": "pt-backend", "version": "1.0.0", "description": "Physical Therapy Management System Backend - Production", "main": "server.js", "scripts": {"start": "node server.js", "install-prod": "npm install --production"}, "keywords": ["healthcare", "physical-therapy", "patient-management", "special-needs", "saudi-arabia"], "author": "PT System Team", "license": "MIT", "dependencies": {"archiver": "^6.0.1", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "compression": "^1.7.4", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-parser": "^3.0.0", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pdf-lib": "^1.17.1", "pdfkit": "^0.17.1", "qrcode": "^1.5.4", "sharp": "^0.32.6", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "twilio": "^4.19.0", "uuid": "^9.0.1", "validator": "^13.11.0", "web-push": "^3.6.7", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xml2js": "^0.6.2", "xss-clean": "^0.1.4"}, "engines": {"node": ">=18.0.0"}}