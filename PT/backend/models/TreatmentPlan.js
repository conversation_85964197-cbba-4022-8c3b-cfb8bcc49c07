const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     TreatmentPlan:
 *       type: object
 *       required:
 *         - patient
 *         - therapist
 *         - diagnosis
 *         - goals
 *         - startDate
 *       properties:
 *         patient:
 *           type: string
 *           description: Patient ID
 *         therapist:
 *           type: string
 *           description: Primary therapist ID
 *         diagnosis:
 *           type: string
 *           description: Primary diagnosis
 *         goals:
 *           type: array
 *           items:
 *             type: object
 *           description: Treatment goals
 */

const treatmentPlanSchema = new mongoose.Schema({
  // Core information
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Patient is required']
  },
  therapist: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Primary therapist is required']
  },
  
  // Medical information
  diagnosis: {
    primary: {
      type: String,
      required: [true, 'Primary diagnosis is required']
    },
    secondary: [String],
    icdCodes: [String]
  },
  
  // Treatment goals
  goals: [{
    description: {
      type: String,
      required: true
    },
    category: {
      type: String,
      enum: ['mobility', 'strength', 'balance', 'coordination', 'pain_management', 'functional', 'cognitive', 'communication', 'social'],
      required: true
    },
    targetDate: Date,
    priority: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    status: {
      type: String,
      enum: ['not_started', 'in_progress', 'achieved', 'modified', 'discontinued'],
      default: 'not_started'
    },
    progressNotes: [String],
    measurableOutcome: String,
    achievedDate: Date
  }],
  
  // Treatment details
  treatmentType: {
    type: String,
    enum: ['physical_therapy', 'occupational_therapy', 'speech_therapy', 'combined', 'special_needs'],
    required: [true, 'Treatment type is required']
  },
  frequency: {
    sessionsPerWeek: {
      type: Number,
      required: true,
      min: 1,
      max: 7
    },
    sessionDuration: {
      type: Number, // in minutes
      required: true,
      min: 15,
      max: 180
    },
    totalSessions: {
      type: Number,
      min: 1
    }
  },
  
  // Timeline
  startDate: {
    type: Date,
    required: [true, 'Start date is required']
  },
  endDate: Date,
  estimatedDuration: {
    type: Number, // in weeks
    min: 1
  },
  
  // Treatment interventions
  interventions: [{
    name: {
      type: String,
      required: true
    },
    description: String,
    frequency: String,
    duration: String, // e.g., "10 minutes", "throughout session"
    equipment: [String],
    modifications: String,
    progressCriteria: String
  }],
  
  // Exercise program
  exercises: [{
    name: {
      type: String,
      required: true
    },
    description: String,
    sets: Number,
    repetitions: Number,
    duration: String,
    intensity: String,
    frequency: String,
    equipment: [String],
    instructions: String,
    precautions: String,
    progressions: [String],
    isHomeExercise: {
      type: Boolean,
      default: false
    }
  }],
  
  // Special needs accommodations
  specialNeedsAccommodations: {
    communicationStrategies: [String],
    behavioralSupport: [String],
    sensoryAccommodations: {
      lighting: String,
      sound: String,
      tactile: String,
      visual: String
    },
    assistiveDevices: [String],
    environmentalModifications: [String],
    familyInvolvement: String
  },
  
  // Progress tracking
  progress: [{
    date: {
      type: Date,
      default: Date.now
    },
    sessionNumber: Number,
    goalsAddressed: [String],
    interventionsUsed: [String],
    patientResponse: String,
    progressNotes: String,
    functionalImprovements: [String],
    challenges: [String],
    modifications: String,
    homeExerciseCompliance: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'poor', 'not_applicable']
    },
    painLevel: {
      before: {
        type: Number,
        min: 0,
        max: 10
      },
      after: {
        type: Number,
        min: 0,
        max: 10
      }
    },
    functionalScores: [{
      assessment: String,
      score: Number,
      maxScore: Number,
      date: Date
    }],
    recordedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Status and outcomes
  status: {
    type: String,
    enum: ['active', 'completed', 'discontinued', 'on_hold', 'transferred'],
    default: 'active'
  },
  completionReason: String,
  outcomes: {
    goalsAchieved: Number,
    totalGoals: Number,
    functionalImprovement: {
      type: String,
      enum: ['significant', 'moderate', 'minimal', 'none', 'declined']
    },
    patientSatisfaction: {
      type: Number,
      min: 1,
      max: 5
    },
    recommendedFollowUp: String,
    dischargeNotes: String
  },
  
  // Billing and insurance
  billing: {
    totalCost: Number,
    insuranceCoverage: {
      provider: String,
      authorizationNumber: String,
      approvedSessions: Number,
      copayAmount: Number,
      deductible: Number
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'approved', 'partially_paid', 'paid', 'denied'],
      default: 'pending'
    }
  },
  
  // Team members
  team: [{
    member: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    role: {
      type: String,
      enum: ['primary_therapist', 'secondary_therapist', 'supervising_physician', 'consultant', 'assistant']
    },
    responsibilities: [String]
  }],
  
  // System fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  lastReviewDate: Date,
  nextReviewDate: Date
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for completion percentage
treatmentPlanSchema.virtual('completionPercentage').get(function() {
  if (!this.goals || this.goals.length === 0) return 0;
  
  const achievedGoals = this.goals.filter(goal => goal.status === 'achieved').length;
  return Math.round((achievedGoals / this.goals.length) * 100);
});

// Virtual for sessions completed
treatmentPlanSchema.virtual('sessionsCompleted').get(function() {
  return this.progress ? this.progress.length : 0;
});

// Virtual for remaining sessions
treatmentPlanSchema.virtual('sessionsRemaining').get(function() {
  if (!this.frequency.totalSessions) return null;
  return Math.max(0, this.frequency.totalSessions - this.sessionsCompleted);
});

// Virtual for treatment duration
treatmentPlanSchema.virtual('actualDuration').get(function() {
  if (!this.startDate) return null;
  
  const endDate = this.endDate || new Date();
  const diffTime = Math.abs(endDate - this.startDate);
  const diffWeeks = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 7));
  
  return diffWeeks;
});

// Indexes for better performance
treatmentPlanSchema.index({ patient: 1 });
treatmentPlanSchema.index({ therapist: 1 });
treatmentPlanSchema.index({ status: 1 });
treatmentPlanSchema.index({ startDate: 1 });
treatmentPlanSchema.index({ treatmentType: 1 });
treatmentPlanSchema.index({ createdAt: -1 });

// Pre-save middleware
treatmentPlanSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.constructor.currentUser;
  }
  
  // Set next review date if not set
  if (!this.nextReviewDate && this.startDate) {
    this.nextReviewDate = new Date(this.startDate.getTime() + (14 * 24 * 60 * 60 * 1000)); // 2 weeks
  }
  
  next();
});

// Static method to get active plans by therapist
treatmentPlanSchema.statics.getActiveByTherapist = function(therapistId) {
  return this.find({ 
    therapist: therapistId, 
    status: 'active' 
  }).populate('patient', 'firstName lastName nationalId');
};

// Static method to get plans due for review
treatmentPlanSchema.statics.getDueForReview = function() {
  return this.find({
    status: 'active',
    nextReviewDate: { $lte: new Date() }
  }).populate('patient therapist');
};

// Instance method to add progress note
treatmentPlanSchema.methods.addProgressNote = function(progressData) {
  this.progress.push({
    ...progressData,
    date: new Date(),
    sessionNumber: this.progress.length + 1
  });
  
  this.lastReviewDate = new Date();
  return this.save();
};

// Instance method to update goal status
treatmentPlanSchema.methods.updateGoalStatus = function(goalId, status, notes) {
  const goal = this.goals.id(goalId);
  if (goal) {
    goal.status = status;
    if (notes) {
      goal.progressNotes.push(notes);
    }
    if (status === 'achieved') {
      goal.achievedDate = new Date();
    }
  }
  return this.save();
};

// Instance method to check if plan needs review
treatmentPlanSchema.methods.needsReview = function() {
  return this.nextReviewDate && this.nextReviewDate <= new Date();
};

module.exports = mongoose.model('TreatmentPlan', treatmentPlanSchema);
