const mongoose = require('mongoose');

const bodyMapAssessmentSchema = new mongoose.Schema({
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  selectedRegions: [{
    type: String,
    required: true
  }],
  painLevels: {
    type: Map,
    of: Number,
    default: new Map()
  },
  painDetails: {
    type: Map,
    of: {
      type: {
        type: String,
        enum: ['sharp', 'dull', 'burning', 'throbbing', 'cramping', 'shooting']
      },
      duration: {
        type: String,
        enum: ['acute', 'chronic', 'intermittent']
      },
      frequency: {
        type: String,
        enum: ['constant', 'frequent', 'occasional', 'rare']
      },
      notes: String
    },
    default: new Map()
  },
  assessmentDate: {
    type: Date,
    default: Date.now
  },
  therapistId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: String,
  status: {
    type: String,
    enum: ['draft', 'completed', 'reviewed'],
    default: 'completed'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
bodyMapAssessmentSchema.index({ patientId: 1, createdAt: -1 });
bodyMapAssessmentSchema.index({ assessmentDate: -1 });
bodyMapAssessmentSchema.index({ therapistId: 1 });

// Virtual for total pain regions
bodyMapAssessmentSchema.virtual('totalPainRegions').get(function() {
  return this.selectedRegions ? this.selectedRegions.length : 0;
});

// Virtual for average pain level
bodyMapAssessmentSchema.virtual('averagePainLevel').get(function() {
  if (!this.painLevels || this.painLevels.size === 0) return 0;
  
  const levels = Array.from(this.painLevels.values());
  const sum = levels.reduce((acc, level) => acc + level, 0);
  return Math.round((sum / levels.length) * 10) / 10;
});

// Virtual for maximum pain level
bodyMapAssessmentSchema.virtual('maxPainLevel').get(function() {
  if (!this.painLevels || this.painLevels.size === 0) return 0;
  return Math.max(...Array.from(this.painLevels.values()));
});

// Method to get pain summary
bodyMapAssessmentSchema.methods.getPainSummary = function() {
  const summary = {
    totalRegions: this.totalPainRegions,
    averagePain: this.averagePainLevel,
    maxPain: this.maxPainLevel,
    regions: []
  };

  if (this.selectedRegions && this.painLevels) {
    this.selectedRegions.forEach(region => {
      const painLevel = this.painLevels.get(region) || 0;
      const details = this.painDetails.get(region) || {};
      
      summary.regions.push({
        region,
        painLevel,
        details
      });
    });
  }

  return summary;
};

// Static method to get patient assessment history
bodyMapAssessmentSchema.statics.getPatientHistory = function(patientId, limit = 10) {
  return this.find({ patientId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('therapistId', 'name email')
    .lean();
};

// Static method to get assessment analytics
bodyMapAssessmentSchema.statics.getAnalytics = function(startDate, endDate) {
  const matchStage = {};
  if (startDate || endDate) {
    matchStage.createdAt = {};
    if (startDate) matchStage.createdAt.$gte = new Date(startDate);
    if (endDate) matchStage.createdAt.$lte = new Date(endDate);
  }

  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalAssessments: { $sum: 1 },
        avgPainLevel: { $avg: '$averagePainLevel' },
        maxPainLevel: { $max: '$maxPainLevel' },
        totalPatients: { $addToSet: '$patientId' }
      }
    },
    {
      $project: {
        _id: 0,
        totalAssessments: 1,
        avgPainLevel: { $round: ['$avgPainLevel', 1] },
        maxPainLevel: 1,
        totalPatients: { $size: '$totalPatients' }
      }
    }
  ]);
};

// Pre-save middleware
bodyMapAssessmentSchema.pre('save', function(next) {
  // Ensure assessment date is set
  if (!this.assessmentDate) {
    this.assessmentDate = new Date();
  }
  
  // Validate pain levels are within range
  if (this.painLevels) {
    for (const [region, level] of this.painLevels) {
      if (level < 0 || level > 10) {
        return next(new Error(`Pain level for ${region} must be between 0 and 10`));
      }
    }
  }
  
  next();
});

// Post-save middleware for logging
bodyMapAssessmentSchema.post('save', function(doc) {
  console.log(`Body map assessment saved for patient: ${doc.patientId}`);
});

module.exports = mongoose.model('BodyMapAssessment', bodyMapAssessmentSchema);
