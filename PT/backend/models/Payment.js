const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Payment:
 *       type: object
 *       required:
 *         - patient
 *         - amount
 *         - method
 *       properties:
 *         patient:
 *           type: string
 *           description: Patient ID
 *         amount:
 *           type: number
 *           description: Payment amount
 *         method:
 *           type: string
 *           description: Payment method
 */

const paymentSchema = new mongoose.Schema({
  // Core information
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Patient is required']
  },
  billing: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Billing'
  },
  
  // Payment details
  amount: {
    type: Number,
    required: [true, 'Payment amount is required'],
    min: [0, 'Payment amount must be positive']
  },
  method: {
    type: String,
    enum: ['cash', 'card', 'bank_transfer', 'insurance', 'check', 'online'],
    required: [true, 'Payment method is required']
  },
  
  // Transaction details
  transactionId: {
    type: String,
    unique: true,
    default: function() {
      return `PAY-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
    }
  },
  reference: String, // Check number, bank reference, etc.
  
  // Card payment details (if applicable)
  cardDetails: {
    last4Digits: String,
    cardType: {
      type: String,
      enum: ['visa', 'mastercard', 'amex', 'mada']
    },
    authCode: String
  },
  
  // Bank transfer details (if applicable)
  bankDetails: {
    bankName: String,
    accountNumber: String,
    transferReference: String
  },
  
  // Insurance payment details (if applicable)
  insuranceDetails: {
    provider: String,
    claimNumber: String,
    approvalNumber: String,
    eobNumber: String // Explanation of Benefits number
  },
  
  // Status and dates
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded', 'cancelled'],
    default: 'pending'
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  processedDate: Date,
  
  // Refund information
  refund: {
    amount: {
      type: Number,
      min: 0,
      default: 0
    },
    reason: String,
    date: Date,
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // System fields
  receivedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Notes and attachments
  notes: String,
  internalNotes: String,
  attachments: [{
    name: String,
    url: String,
    type: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Audit trail
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for refunded amount
paymentSchema.virtual('refundedAmount').get(function() {
  return this.refund.amount || 0;
});

// Virtual for net amount (amount - refunded)
paymentSchema.virtual('netAmount').get(function() {
  return this.amount - this.refundedAmount;
});

// Virtual for payment type display
paymentSchema.virtual('methodDisplay').get(function() {
  const methodMap = {
    'cash': 'Cash',
    'card': 'Credit/Debit Card',
    'bank_transfer': 'Bank Transfer',
    'insurance': 'Insurance',
    'check': 'Check',
    'online': 'Online Payment'
  };
  return methodMap[this.method] || this.method;
});

// Indexes for better performance
paymentSchema.index({ patient: 1, paymentDate: -1 });
paymentSchema.index({ billing: 1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ paymentDate: -1 });
paymentSchema.index({ method: 1 });
paymentSchema.index({ transactionId: 1 });

// Pre-save middleware
paymentSchema.pre('save', function(next) {
  if (this.status === 'completed' && !this.processedDate) {
    this.processedDate = new Date();
  }
  
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.constructor.currentUser;
  }
  
  next();
});

// Static method to get payments by period
paymentSchema.statics.getPaymentsByPeriod = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        paymentDate: {
          $gte: startDate,
          $lte: endDate
        },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: '$method',
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    }
  ]);
};

// Static method to get revenue by period
paymentSchema.statics.getRevenueByPeriod = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        paymentDate: {
          $gte: startDate,
          $lte: endDate
        },
        status: 'completed'
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$paymentDate' },
          month: { $month: '$paymentDate' },
          day: { $dayOfMonth: '$paymentDate' }
        },
        totalAmount: { $sum: '$amount' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);
};

// Instance method to process refund
paymentSchema.methods.processRefund = function(refundAmount, reason, processedBy) {
  if (refundAmount > this.amount) {
    throw new Error('Refund amount cannot exceed payment amount');
  }
  
  this.refund = {
    amount: refundAmount,
    reason: reason,
    date: new Date(),
    processedBy: processedBy
  };
  
  if (refundAmount === this.amount) {
    this.status = 'refunded';
  }
  
  return this.save();
};

// Instance method to mark as completed
paymentSchema.methods.markAsCompleted = function(processedBy) {
  this.status = 'completed';
  this.processedDate = new Date();
  this.processedBy = processedBy;
  
  return this.save();
};

module.exports = mongoose.model('Payment', paymentSchema);
