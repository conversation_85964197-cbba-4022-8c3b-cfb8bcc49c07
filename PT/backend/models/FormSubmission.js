const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     FormSubmission:
 *       type: object
 *       required:
 *         - form
 *         - submittedBy
 *         - data
 *       properties:
 *         form:
 *           type: string
 *           description: Form ID
 *         submittedBy:
 *           type: string
 *           description: User who submitted the form
 *         data:
 *           type: object
 *           description: Form submission data
 */

const formSubmissionSchema = new mongoose.Schema({
  // Core information
  form: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Form',
    required: [true, 'Form is required']
  },
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient'
  },
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Submitter is required']
  },
  
  // Submission data
  data: {
    type: mongoose.Schema.Types.Mixed,
    required: [true, 'Form data is required']
  },
  
  // Metadata
  submissionId: {
    type: String,
    unique: true,
    default: function() {
      return `SUB-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
  },
  version: {
    type: String,
    default: '1.0'
  },
  
  // Status and workflow
  status: {
    type: String,
    enum: ['draft', 'submitted', 'reviewed', 'approved', 'rejected', 'archived'],
    default: 'submitted'
  },
  isComplete: {
    type: Boolean,
    default: true
  },
  completionPercentage: {
    type: Number,
    min: 0,
    max: 100,
    default: 100
  },
  
  // Timing information
  startedAt: {
    type: Date,
    default: Date.now
  },
  submittedAt: {
    type: Date,
    default: Date.now
  },
  completionTime: {
    type: Number, // in seconds
    default: 0
  },
  
  // Review and approval
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewedAt: Date,
  reviewNotes: String,
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  
  // File attachments
  attachments: [{
    fieldId: String,
    fileName: String,
    originalName: String,
    mimeType: String,
    size: Number,
    url: String,
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Digital signatures
  signatures: [{
    fieldId: String,
    signatureData: String, // Base64 encoded signature
    signerName: String,
    signerRole: String,
    signedAt: {
      type: Date,
      default: Date.now
    },
    ipAddress: String,
    userAgent: String
  }],
  
  // Validation and errors
  validationErrors: [{
    fieldId: String,
    message: String,
    code: String
  }],
  
  // Audit trail
  auditTrail: [{
    action: {
      type: String,
      enum: ['created', 'updated', 'submitted', 'reviewed', 'approved', 'rejected', 'archived']
    },
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    performedAt: {
      type: Date,
      default: Date.now
    },
    details: String,
    ipAddress: String,
    userAgent: String
  }],
  
  // Integration data
  integrations: {
    nphies: {
      submitted: {
        type: Boolean,
        default: false
      },
      submissionId: String,
      submittedAt: Date,
      status: String,
      response: mongoose.Schema.Types.Mixed
    },
    ehr: {
      synced: {
        type: Boolean,
        default: false
      },
      recordId: String,
      syncedAt: Date,
      lastSyncAttempt: Date,
      syncErrors: [String]
    }
  },
  
  // Notifications
  notifications: [{
    type: {
      type: String,
      enum: ['email', 'sms', 'push']
    },
    recipient: String,
    sentAt: Date,
    status: {
      type: String,
      enum: ['pending', 'sent', 'failed'],
      default: 'pending'
    },
    template: String,
    content: String
  }],
  
  // Privacy and consent
  consent: {
    dataProcessing: {
      type: Boolean,
      default: false
    },
    dataSharing: {
      type: Boolean,
      default: false
    },
    consentGivenAt: Date,
    consentGivenBy: String,
    ipAddress: String
  },
  
  // System fields
  source: {
    type: String,
    enum: ['web', 'mobile', 'tablet', 'kiosk', 'api'],
    default: 'web'
  },
  deviceInfo: {
    userAgent: String,
    ipAddress: String,
    platform: String,
    browser: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for submission age
formSubmissionSchema.virtual('submissionAge').get(function() {
  if (this.submittedAt) {
    const now = new Date();
    const diffTime = Math.abs(now - this.submittedAt);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }
  return 0;
});

// Virtual for status display
formSubmissionSchema.virtual('statusDisplay').get(function() {
  const statusMap = {
    draft: 'Draft',
    submitted: 'Submitted',
    reviewed: 'Under Review',
    approved: 'Approved',
    rejected: 'Rejected',
    archived: 'Archived'
  };
  return statusMap[this.status] || this.status;
});

// Indexes for better performance
formSubmissionSchema.index({ form: 1, submittedAt: -1 });
formSubmissionSchema.index({ patient: 1, submittedAt: -1 });
formSubmissionSchema.index({ submittedBy: 1 });
formSubmissionSchema.index({ status: 1 });
// submissionId already has unique index
formSubmissionSchema.index({ createdAt: -1 });

// Pre-save middleware
formSubmissionSchema.pre('save', function(next) {
  // Calculate completion time if not set
  if (this.isNew && this.startedAt && this.submittedAt) {
    this.completionTime = Math.round((this.submittedAt - this.startedAt) / 1000);
  }
  
  // Add audit trail entry for status changes
  if (this.isModified('status') && !this.isNew) {
    this.auditTrail.push({
      action: this.status,
      performedBy: this.constructor.currentUser,
      details: `Status changed to ${this.status}`
    });
  }
  
  next();
});

// Static method to get submissions by form
formSubmissionSchema.statics.getByForm = function(formId, filters = {}) {
  const query = { form: formId, ...filters };
  return this.find(query)
    .populate('submittedBy', 'firstName lastName role')
    .populate('patient', 'firstName lastName nationalId')
    .sort({ submittedAt: -1 });
};

// Static method to get submissions by patient
formSubmissionSchema.statics.getByPatient = function(patientId) {
  return this.find({ patient: patientId })
    .populate('form', 'title category')
    .populate('submittedBy', 'firstName lastName role')
    .sort({ submittedAt: -1 });
};

// Static method to get pending reviews
formSubmissionSchema.statics.getPendingReviews = function() {
  return this.find({ status: 'submitted' })
    .populate('form', 'title category')
    .populate('patient', 'firstName lastName nationalId')
    .populate('submittedBy', 'firstName lastName role')
    .sort({ submittedAt: 1 });
};

// Instance method to validate submission data
formSubmissionSchema.methods.validateData = async function() {
  const Form = mongoose.model('Form');
  const form = await Form.findById(this.form);
  
  if (!form) {
    throw new Error('Form not found');
  }
  
  const errors = [];
  
  form.fields.forEach(field => {
    const value = this.data[field.id];
    
    // Check required fields
    if (field.required && (value === undefined || value === null || value === '')) {
      errors.push({
        fieldId: field.id,
        message: `${field.label} is required`,
        code: 'REQUIRED'
      });
    }
    
    // Validate field types and constraints
    if (value !== undefined && value !== null && value !== '') {
      // Add specific validation logic based on field type
      if (field.type === 'email' && !/^\S+@\S+\.\S+$/.test(value)) {
        errors.push({
          fieldId: field.id,
          message: 'Invalid email format',
          code: 'INVALID_EMAIL'
        });
      }
      
      if (field.type === 'number' && isNaN(value)) {
        errors.push({
          fieldId: field.id,
          message: 'Must be a valid number',
          code: 'INVALID_NUMBER'
        });
      }
      
      // Add more validation rules as needed
    }
  });
  
  this.validationErrors = errors;
  return errors.length === 0;
};

// Instance method to add audit trail entry
formSubmissionSchema.methods.addAuditEntry = function(action, details, userId) {
  this.auditTrail.push({
    action: action,
    performedBy: userId || this.constructor.currentUser,
    details: details
  });
  
  return this.save();
};

// Instance method to approve submission
formSubmissionSchema.methods.approve = function(userId, notes) {
  this.status = 'approved';
  this.approvedBy = userId;
  this.approvedAt = new Date();
  if (notes) {
    this.reviewNotes = notes;
  }
  
  return this.addAuditEntry('approved', notes);
};

// Instance method to reject submission
formSubmissionSchema.methods.reject = function(userId, reason) {
  this.status = 'rejected';
  this.reviewedBy = userId;
  this.reviewedAt = new Date();
  this.reviewNotes = reason;
  
  return this.addAuditEntry('rejected', reason);
};

module.exports = mongoose.model('FormSubmission', formSubmissionSchema);
