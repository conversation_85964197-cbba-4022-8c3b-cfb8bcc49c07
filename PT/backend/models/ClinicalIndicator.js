const mongoose = require('mongoose');

const clinicalIndicatorSchema = new mongoose.Schema({
  // Patient Information
  patientId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  assessmentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Assessment',
    required: true
  },
  assessmentDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  assessmentType: {
    type: String,
    enum: ['initial', 'progress', 'discharge', 'follow-up'],
    required: true
  },

  // Berg Balance Scale (0-56 points)
  bergBalanceScale: {
    totalScore: {
      type: Number,
      min: 0,
      max: 56,
      default: null
    },
    riskLevel: {
      type: String,
      enum: ['high-risk', 'moderate-risk', 'low-risk'],
      default: null
    },
    individualScores: {
      sittingToStanding: { type: Number, min: 0, max: 4, default: null },
      standingUnsupported: { type: Number, min: 0, max: 4, default: null },
      sittingUnsupported: { type: Number, min: 0, max: 4, default: null },
      standingToSitting: { type: Number, min: 0, max: 4, default: null },
      transfers: { type: Number, min: 0, max: 4, default: null },
      standingEyesClosed: { type: Number, min: 0, max: 4, default: null },
      standingFeetTogether: { type: Number, min: 0, max: 4, default: null },
      reachingForward: { type: Number, min: 0, max: 4, default: null },
      pickingUpObject: { type: Number, min: 0, max: 4, default: null },
      lookingOverShoulders: { type: Number, min: 0, max: 4, default: null },
      turning360Degrees: { type: Number, min: 0, max: 4, default: null },
      placingFeetOnStool: { type: Number, min: 0, max: 4, default: null },
      standingOneFoot: { type: Number, min: 0, max: 4, default: null },
      standingTandem: { type: Number, min: 0, max: 4, default: null }
    },
    notes: String
  },

  // Timed Up and Go (TUG) Test
  timedUpAndGo: {
    timeInSeconds: {
      type: Number,
      min: 0,
      default: null
    },
    riskLevel: {
      type: String,
      enum: ['normal', 'mild-impairment', 'moderate-impairment', 'severe-impairment'],
      default: null
    },
    assistiveDevice: {
      type: String,
      enum: ['none', 'cane', 'walker', 'wheelchair', 'other'],
      default: 'none'
    },
    notes: String
  },

  // Manual Muscle Testing (MMT) - 0-5 scale
  manualMuscleTesting: {
    upperExtremity: {
      rightShoulder: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null },
        abduction: { type: Number, min: 0, max: 5, default: null },
        adduction: { type: Number, min: 0, max: 5, default: null }
      },
      leftShoulder: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null },
        abduction: { type: Number, min: 0, max: 5, default: null },
        adduction: { type: Number, min: 0, max: 5, default: null }
      },
      rightElbow: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null }
      },
      leftElbow: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null }
      },
      rightWrist: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null }
      },
      leftWrist: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null }
      }
    },
    lowerExtremity: {
      rightHip: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null },
        abduction: { type: Number, min: 0, max: 5, default: null },
        adduction: { type: Number, min: 0, max: 5, default: null }
      },
      leftHip: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null },
        abduction: { type: Number, min: 0, max: 5, default: null },
        adduction: { type: Number, min: 0, max: 5, default: null }
      },
      rightKnee: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null }
      },
      leftKnee: {
        flexion: { type: Number, min: 0, max: 5, default: null },
        extension: { type: Number, min: 0, max: 5, default: null }
      },
      rightAnkle: {
        dorsiflexion: { type: Number, min: 0, max: 5, default: null },
        plantarflexion: { type: Number, min: 0, max: 5, default: null }
      },
      leftAnkle: {
        dorsiflexion: { type: Number, min: 0, max: 5, default: null },
        plantarflexion: { type: Number, min: 0, max: 5, default: null }
      }
    },
    trunk: {
      flexion: { type: Number, min: 0, max: 5, default: null },
      extension: { type: Number, min: 0, max: 5, default: null },
      lateralFlexionRight: { type: Number, min: 0, max: 5, default: null },
      lateralFlexionLeft: { type: Number, min: 0, max: 5, default: null }
    },
    overallStrengthIndex: {
      type: Number,
      min: 0,
      max: 5,
      default: null
    },
    notes: String
  },

  // Pain Scale - Visual Analog Scale (VAS) 0-10
  painScale: {
    currentPain: {
      type: Number,
      min: 0,
      max: 10,
      default: null
    },
    averagePain24h: {
      type: Number,
      min: 0,
      max: 10,
      default: null
    },
    worstPain24h: {
      type: Number,
      min: 0,
      max: 10,
      default: null
    },
    painAtRest: {
      type: Number,
      min: 0,
      max: 10,
      default: null
    },
    painWithActivity: {
      type: Number,
      min: 0,
      max: 10,
      default: null
    },
    painLocation: [String],
    painQuality: {
      type: [String],
      enum: ['sharp', 'dull', 'burning', 'aching', 'stabbing', 'throbbing', 'cramping', 'shooting']
    },
    painPattern: {
      type: String,
      enum: ['constant', 'intermittent', 'variable', 'progressive', 'improving']
    },
    notes: String
  },

  // Functional Independence Measure (FIM) - 18 items, 1-7 scale each
  functionalIndependenceMeasure: {
    selfCare: {
      eating: { type: Number, min: 1, max: 7, default: null },
      grooming: { type: Number, min: 1, max: 7, default: null },
      bathing: { type: Number, min: 1, max: 7, default: null },
      dressingUpper: { type: Number, min: 1, max: 7, default: null },
      dressingLower: { type: Number, min: 1, max: 7, default: null },
      toileting: { type: Number, min: 1, max: 7, default: null }
    },
    sphincterControl: {
      bladder: { type: Number, min: 1, max: 7, default: null },
      bowel: { type: Number, min: 1, max: 7, default: null }
    },
    transfers: {
      bedChairWheelchair: { type: Number, min: 1, max: 7, default: null },
      toilet: { type: Number, min: 1, max: 7, default: null },
      tubShower: { type: Number, min: 1, max: 7, default: null }
    },
    locomotion: {
      walkWheelchair: { type: Number, min: 1, max: 7, default: null },
      stairs: { type: Number, min: 1, max: 7, default: null }
    },
    communication: {
      comprehension: { type: Number, min: 1, max: 7, default: null },
      expression: { type: Number, min: 1, max: 7, default: null }
    },
    socialCognition: {
      socialInteraction: { type: Number, min: 1, max: 7, default: null },
      problemSolving: { type: Number, min: 1, max: 7, default: null },
      memory: { type: Number, min: 1, max: 7, default: null }
    },
    totalScore: {
      type: Number,
      min: 18,
      max: 126,
      default: null
    },
    motorScore: {
      type: Number,
      min: 13,
      max: 91,
      default: null
    },
    cognitiveScore: {
      type: Number,
      min: 5,
      max: 35,
      default: null
    },
    notes: String
  },

  // Additional Clinical Indicators
  rangeOfMotion: {
    cervicalSpine: {
      flexion: { type: Number, default: null },
      extension: { type: Number, default: null },
      lateralFlexionRight: { type: Number, default: null },
      lateralFlexionLeft: { type: Number, default: null },
      rotationRight: { type: Number, default: null },
      rotationLeft: { type: Number, default: null }
    },
    lumbarSpine: {
      flexion: { type: Number, default: null },
      extension: { type: Number, default: null },
      lateralFlexionRight: { type: Number, default: null },
      lateralFlexionLeft: { type: Number, default: null }
    },
    notes: String
  },

  // Assessment Metadata
  assessedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  status: {
    type: String,
    enum: ['draft', 'completed', 'reviewed', 'approved'],
    default: 'draft'
  },
  clinicalNotes: String,
  recommendations: String,
  nextAssessmentDate: Date,

  // Compliance and Quality
  complianceFlags: {
    carf: { type: Boolean, default: true },
    cbahi: { type: Boolean, default: true },
    hipaa: { type: Boolean, default: true }
  },
  qualityIndicators: {
    dataCompleteness: { type: Number, min: 0, max: 100, default: 0 },
    assessmentReliability: { type: String, enum: ['high', 'medium', 'low'], default: 'medium' }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
clinicalIndicatorSchema.index({ patientId: 1, assessmentDate: -1 });
clinicalIndicatorSchema.index({ assessmentType: 1, assessmentDate: -1 });
clinicalIndicatorSchema.index({ assessedBy: 1, assessmentDate: -1 });

// Virtual for progress calculation
clinicalIndicatorSchema.virtual('progressIndicators').get(function() {
  return {
    bergBalanceImprovement: this.bergBalanceScale?.totalScore || 0,
    tugImprovement: this.timedUpAndGo?.timeInSeconds || 0,
    painReduction: this.painScale?.currentPain || 0,
    functionalImprovement: this.functionalIndependenceMeasure?.totalScore || 0,
    strengthImprovement: this.manualMuscleTesting?.overallStrengthIndex || 0
  };
});

// Method to calculate overall progress score
clinicalIndicatorSchema.methods.calculateProgressScore = function() {
  let totalScore = 0;
  let assessedItems = 0;

  // Berg Balance Scale (weight: 25%)
  if (this.bergBalanceScale?.totalScore !== null) {
    totalScore += (this.bergBalanceScale.totalScore / 56) * 25;
    assessedItems++;
  }

  // TUG Test (weight: 20%) - lower time is better
  if (this.timedUpAndGo?.timeInSeconds !== null) {
    const tugScore = Math.max(0, (30 - this.timedUpAndGo.timeInSeconds) / 30);
    totalScore += tugScore * 20;
    assessedItems++;
  }

  // Pain Scale (weight: 20%) - lower pain is better
  if (this.painScale?.currentPain !== null) {
    totalScore += ((10 - this.painScale.currentPain) / 10) * 20;
    assessedItems++;
  }

  // FIM Score (weight: 25%)
  if (this.functionalIndependenceMeasure?.totalScore !== null) {
    totalScore += (this.functionalIndependenceMeasure.totalScore / 126) * 25;
    assessedItems++;
  }

  // MMT Score (weight: 10%)
  if (this.manualMuscleTesting?.overallStrengthIndex !== null) {
    totalScore += (this.manualMuscleTesting.overallStrengthIndex / 5) * 10;
    assessedItems++;
  }

  return assessedItems > 0 ? Math.round(totalScore) : 0;
};

module.exports = mongoose.model('ClinicalIndicator', clinicalIndicatorSchema);
