const mongoose = require('mongoose');

const electronicSignatureSchema = new mongoose.Schema({
  // Document Information
  documentId: {
    type: String,
    required: true
  },
  
  documentType: {
    type: String,
    enum: [
      'consent_form',
      'treatment_plan',
      'invoice',
      'discharge_summary',
      'prescription',
      'assessment_form',
      'progress_note',
      'insurance_claim',
      'referral_letter',
      'medical_report'
    ],
    required: true
  },
  
  documentTitle: {
    type: String,
    required: true
  },
  
  documentContent: {
    type: String,
    required: true
  },
  
  documentHash: {
    type: String,
    required: true
  },
  
  // Patient Information
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  
  // Related Records
  relatedRecords: {
    billing: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Billing'
    },
    appointment: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Appointment'
    },
    treatmentPlan: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'TreatmentPlan'
    }
  },
  
  // Signers
  signers: [{
    signerType: {
      type: String,
      enum: ['patient', 'guardian', 'therapist', 'doctor', 'witness'],
      required: true
    },
    
    signerInfo: {
      userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      name: {
        type: String,
        required: true
      },
      nationalId: String,
      email: String,
      phone: String,
      relationship: String // For guardians
    },
    
    signatureData: {
      digitalSignature: String,
      signatureImage: String, // Base64 encoded signature image
      timestamp: {
        type: Date,
        required: true
      },
      ipAddress: String,
      userAgent: String,
      deviceInfo: {
        platform: String,
        browser: String,
        version: String,
        isMobile: Boolean
      },
      geolocation: {
        latitude: Number,
        longitude: Number,
        accuracy: Number
      }
    },
    
    // Biometric Data (for advanced signatures)
    biometricData: {
      fingerprint: String,
      voicePrint: String,
      faceRecognition: String,
      retinaScan: String
    },
    
    // Authentication Method
    authenticationMethod: {
      type: String,
      enum: ['password', 'otp', 'biometric', 'smart_card', 'digital_certificate'],
      required: true
    },
    
    authenticationDetails: {
      otpSent: Boolean,
      otpVerified: Boolean,
      certificateSerial: String,
      smartCardId: String
    },
    
    // Signature Status
    status: {
      type: String,
      enum: ['pending', 'signed', 'rejected', 'expired'],
      default: 'pending'
    },
    
    signedAt: Date,
    rejectedAt: Date,
    rejectionReason: String,
    
    // Legal Compliance
    consentGiven: {
      type: Boolean,
      default: false
    },
    
    consentTimestamp: Date,
    
    witnessInfo: {
      name: String,
      nationalId: String,
      signature: String,
      timestamp: Date
    }
  }],
  
  // Signature Workflow
  workflow: {
    status: {
      type: String,
      enum: ['draft', 'pending_signatures', 'partially_signed', 'fully_signed', 'rejected', 'expired'],
      default: 'draft'
    },
    
    requiredSignatures: Number,
    completedSignatures: Number,
    
    expiryDate: Date,
    
    remindersSent: [{
      sentTo: String,
      sentAt: Date,
      method: String // email, sms, push
    }],
    
    escalations: [{
      escalatedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      escalatedAt: Date,
      reason: String
    }]
  },
  
  // Digital Certificate Information
  certificate: {
    issuer: String,
    serialNumber: String,
    subject: String,
    validFrom: Date,
    validTo: Date,
    algorithm: String,
    keySize: Number,
    fingerprint: String
  },
  
  // Verification and Validation
  verification: {
    isValid: {
      type: Boolean,
      default: true
    },
    
    validationChecks: [{
      checkType: {
        type: String,
        enum: ['signature_integrity', 'certificate_validity', 'timestamp_verification', 'document_integrity']
      },
      status: {
        type: String,
        enum: ['passed', 'failed', 'warning']
      },
      details: String,
      checkedAt: Date
    }],
    
    lastValidated: Date,
    
    validationErrors: [{
      code: String,
      message: String,
      severity: String,
      timestamp: Date
    }]
  },
  
  // Legal and Compliance
  legalCompliance: {
    regulatoryFramework: {
      type: String,
      enum: ['saudi_esign_law', 'gdpr', 'hipaa', 'local_regulations'],
      default: 'saudi_esign_law'
    },
    
    evidencePackage: {
      documentHash: String,
      signatureHashes: [String],
      timestampToken: String,
      auditTrail: String,
      certificateChain: [String]
    },
    
    legalValidityConfirmed: {
      type: Boolean,
      default: false
    },
    
    legalValidityDate: Date,
    
    retentionPeriod: {
      type: Number,
      default: 2555 // 7 years in days
    },
    
    destructionDate: Date
  },
  
  // Integration with External Services
  externalIntegrations: {
    nphies: {
      submitted: Boolean,
      submissionId: String,
      status: String
    },
    
    zatca: {
      submitted: Boolean,
      submissionId: String,
      status: String
    },
    
    thirdPartyServices: [{
      serviceName: String,
      serviceId: String,
      status: String,
      submittedAt: Date
    }]
  },
  
  // Audit Trail
  auditLog: [{
    action: String,
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    details: mongoose.Schema.Types.Mixed,
    ipAddress: String,
    userAgent: String
  }],
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Indexes
electronicSignatureSchema.index({ patient: 1 });
electronicSignatureSchema.index({ documentType: 1 });
electronicSignatureSchema.index({ 'workflow.status': 1 });
electronicSignatureSchema.index({ 'signers.status': 1 });
electronicSignatureSchema.index({ createdAt: -1 });
electronicSignatureSchema.index({ 'workflow.expiryDate': 1 });

// Virtual for completion percentage
electronicSignatureSchema.virtual('completionPercentage').get(function() {
  if (this.workflow.requiredSignatures === 0) return 0;
  return (this.workflow.completedSignatures / this.workflow.requiredSignatures) * 100;
});

// Virtual for is expired
electronicSignatureSchema.virtual('isExpired').get(function() {
  return this.workflow.expiryDate && this.workflow.expiryDate < new Date();
});

// Method to add signer
electronicSignatureSchema.methods.addSigner = function(signerData) {
  this.signers.push({
    ...signerData,
    status: 'pending'
  });
  this.workflow.requiredSignatures = this.signers.length;
  return this.save();
};

// Method to sign document
electronicSignatureSchema.methods.signDocument = function(signerId, signatureData) {
  const signer = this.signers.id(signerId);
  if (!signer) {
    throw new Error('Signer not found');
  }
  
  signer.signatureData = signatureData;
  signer.status = 'signed';
  signer.signedAt = new Date();
  
  this.workflow.completedSignatures = this.signers.filter(s => s.status === 'signed').length;
  
  if (this.workflow.completedSignatures >= this.workflow.requiredSignatures) {
    this.workflow.status = 'fully_signed';
  } else {
    this.workflow.status = 'partially_signed';
  }
  
  return this.save();
};

// Method to validate signature
electronicSignatureSchema.methods.validateSignature = function() {
  // Implementation for signature validation
  const checks = [];
  
  // Check document integrity
  const currentHash = require('crypto')
    .createHash('sha256')
    .update(this.documentContent)
    .digest('hex');
    
  if (currentHash !== this.documentHash) {
    checks.push({
      checkType: 'document_integrity',
      status: 'failed',
      details: 'Document has been modified after signing'
    });
  }
  
  this.verification.validationChecks.push(...checks);
  this.verification.lastValidated = new Date();
  
  return this.save();
};

// Pre-save middleware
electronicSignatureSchema.pre('save', function(next) {
  // Generate document hash if not present
  if (!this.documentHash && this.documentContent) {
    this.documentHash = require('crypto')
      .createHash('sha256')
      .update(this.documentContent)
      .digest('hex');
  }
  
  // Set destruction date based on retention period
  if (!this.legalCompliance.destructionDate && this.legalCompliance.retentionPeriod) {
    const destructionDate = new Date();
    destructionDate.setDate(destructionDate.getDate() + this.legalCompliance.retentionPeriod);
    this.legalCompliance.destructionDate = destructionDate;
  }
  
  next();
});

module.exports = mongoose.model('ElectronicSignature', electronicSignatureSchema);
