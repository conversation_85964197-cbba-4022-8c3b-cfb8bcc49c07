const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  type: {
    type: String,
    enum: ['appointment', 'treatment', 'billing', 'system', 'alert', 'marketing'],
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  titleAr: {
    type: String,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000
  },
  messageAr: {
    type: String,
    maxlength: 1000
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
    index: true
  },
  channels: [{
    type: String,
    enum: ['email', 'sms', 'push', 'inApp']
  }],
  status: {
    type: String,
    enum: ['pending', 'sent', 'delivered', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  read: {
    type: Boolean,
    default: false,
    index: true
  },
  readAt: {
    type: Date
  },
  scheduledFor: {
    type: Date,
    default: Date.now,
    index: true
  },
  sentAt: {
    type: Date
  },
  deliveredAt: {
    type: Date
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  // Email specific fields
  emailData: {
    to: String,
    subject: String,
    messageId: String,
    deliveryStatus: String
  },
  // SMS specific fields
  smsData: {
    to: String,
    messageId: String,
    deliveryStatus: String
  },
  // Push notification specific fields
  pushData: {
    subscription: mongoose.Schema.Types.Mixed,
    payload: mongoose.Schema.Types.Mixed,
    deliveryStatus: String
  },
  // Error tracking
  deliveryErrors: [{
    channel: String,
    error: String,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  // Retry tracking
  retryCount: {
    type: Number,
    default: 0
  },
  maxRetries: {
    type: Number,
    default: 3
  },
  nextRetryAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
notificationSchema.index({ userId: 1, createdAt: -1 });
notificationSchema.index({ userId: 1, type: 1, read: 1 });
notificationSchema.index({ status: 1, scheduledFor: 1 });
notificationSchema.index({ userId: 1, priority: 1, read: 1 });

// Virtual for time since creation
notificationSchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diff = now - this.createdAt;
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(hours / 24);
  
  if (hours < 1) return 'Just now';
  if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (days < 7) return `${days} day${days > 1 ? 's' : ''} ago`;
  return this.createdAt.toLocaleDateString();
});

// Virtual for delivery status summary
notificationSchema.virtual('deliveryStatus').get(function() {
  if (this.status === 'delivered') return 'delivered';
  if (this.status === 'sent') return 'sent';
  if (this.status === 'failed') return 'failed';
  if (this.status === 'pending' && this.scheduledFor > new Date()) return 'scheduled';
  return 'pending';
});

// Static method to get notification statistics
notificationSchema.statics.getStatistics = async function(userId, dateRange = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - dateRange);
  
  const stats = await this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 },
        unread: { $sum: { $cond: [{ $eq: ['$read', false] }, 1, 0] } },
        byType: {
          $push: {
            type: '$type',
            priority: '$priority',
            read: '$read'
          }
        },
        byPriority: {
          $push: {
            priority: '$priority',
            read: '$read'
          }
        }
      }
    }
  ]);
  
  if (stats.length === 0) {
    return {
      total: 0,
      unread: 0,
      byType: {},
      byPriority: {}
    };
  }
  
  const result = stats[0];
  
  // Process by type
  const typeStats = {};
  result.byType.forEach(item => {
    if (!typeStats[item.type]) {
      typeStats[item.type] = { total: 0, unread: 0 };
    }
    typeStats[item.type].total++;
    if (!item.read) typeStats[item.type].unread++;
  });
  
  // Process by priority
  const priorityStats = {};
  result.byPriority.forEach(item => {
    if (!priorityStats[item.priority]) {
      priorityStats[item.priority] = { total: 0, unread: 0 };
    }
    priorityStats[item.priority].total++;
    if (!item.read) priorityStats[item.priority].unread++;
  });
  
  return {
    total: result.total,
    unread: result.unread,
    byType: typeStats,
    byPriority: priorityStats
  };
};

// Static method to mark all as read
notificationSchema.statics.markAllAsRead = async function(userId) {
  const result = await this.updateMany(
    { userId, read: false },
    { read: true, readAt: new Date() }
  );
  
  return result.modifiedCount;
};

// Static method to clean old notifications
notificationSchema.statics.cleanOldNotifications = async function(daysToKeep = 90) {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
  
  const result = await this.deleteMany({
    createdAt: { $lt: cutoffDate },
    read: true
  });
  
  return result.deletedCount;
};

// Instance method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.read = true;
  this.readAt = new Date();
  return this.save();
};

// Instance method to add error
notificationSchema.methods.addError = function(channel, error) {
  this.errors.push({
    channel,
    error: error.message || error,
    timestamp: new Date()
  });
  
  this.retryCount++;
  
  if (this.retryCount < this.maxRetries) {
    // Exponential backoff: 5 minutes, 15 minutes, 45 minutes
    const delayMinutes = 5 * Math.pow(3, this.retryCount - 1);
    this.nextRetryAt = new Date(Date.now() + delayMinutes * 60 * 1000);
    this.status = 'pending';
  } else {
    this.status = 'failed';
  }
  
  return this.save();
};

// Instance method to mark as sent
notificationSchema.methods.markAsSent = function(channel, deliveryData = {}) {
  this.status = 'sent';
  this.sentAt = new Date();
  
  // Store delivery data based on channel
  switch (channel) {
    case 'email':
      this.emailData = { ...this.emailData, ...deliveryData };
      break;
    case 'sms':
      this.smsData = { ...this.smsData, ...deliveryData };
      break;
    case 'push':
      this.pushData = { ...this.pushData, ...deliveryData };
      break;
  }
  
  return this.save();
};

// Pre-save middleware to validate channels
notificationSchema.pre('save', function(next) {
  // Ensure at least one channel is specified
  if (!this.channels || this.channels.length === 0) {
    this.channels = ['inApp'];
  }
  
  // Set default scheduled time if not provided
  if (!this.scheduledFor) {
    this.scheduledFor = new Date();
  }
  
  next();
});

// Post-save middleware for real-time updates
notificationSchema.post('save', function(doc) {
  // Emit real-time notification event
  if (global.io) {
    global.io.to(`user_${doc.userId}`).emit('notification', {
      type: 'new_notification',
      notification: doc
    });
  }
});

const Notification = mongoose.model('Notification', notificationSchema);

module.exports = Notification;
