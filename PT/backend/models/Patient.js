const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Patient:
 *       type: object
 *       required:
 *         - firstName
 *         - lastName
 *         - nationalId
 *         - dateOfBirth
 *         - gender
 *       properties:
 *         firstName:
 *           type: string
 *           description: <PERSON><PERSON>'s first name
 *         lastName:
 *           type: string
 *           description: <PERSON><PERSON>'s last name
 *         nationalId:
 *           type: string
 *           description: Patient's national ID number
 *         dateOfBirth:
 *           type: string
 *           format: date
 *           description: Pat<PERSON>'s date of birth
 *         gender:
 *           type: string
 *           enum: [male, female]
 *           description: Pat<PERSON>'s gender
 */

const patientSchema = new mongoose.Schema({
  // Basic Information
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot be more than 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot be more than 50 characters']
  },
  firstNameAr: {
    type: String,
    trim: true,
    maxlength: [50, 'Arabic first name cannot be more than 50 characters']
  },
  lastNameAr: {
    type: String,
    trim: true,
    maxlength: [50, 'Arabic last name cannot be more than 50 characters']
  },
  nationalId: {
    type: String,
    required: [true, 'National ID is required'],
    unique: true,
    trim: true,
    match: [/^\d{10}$/, 'National ID must be 10 digits']
  },
  dateOfBirth: {
    type: Date,
    required: [true, 'Date of birth is required'],
    validate: {
      validator: function(value) {
        return value < new Date();
      },
      message: 'Date of birth must be in the past'
    }
  },
  gender: {
    type: String,
    required: [true, 'Gender is required'],
    enum: {
      values: ['male', 'female'],
      message: 'Gender must be either male or female'
    }
  },
  
  // Contact Information
  phone: {
    type: String,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please provide a valid phone number']
  },
  email: {
    type: String,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email'
    ]
  },
  address: {
    street: String,
    city: String,
    region: String,
    postalCode: String,
    country: {
      type: String,
      default: 'Saudi Arabia'
    }
  },
  
  // Emergency Contact
  emergencyContact: {
    name: {
      type: String,
      required: [true, 'Emergency contact name is required']
    },
    relationship: {
      type: String,
      required: [true, 'Emergency contact relationship is required']
    },
    phone: {
      type: String,
      required: [true, 'Emergency contact phone is required'],
      match: [/^\+?[1-9]\d{1,14}$/, 'Please provide a valid phone number']
    },
    email: String
  },
  
  // Insurance Information
  insurance: {
    provider: String,
    policyNumber: String,
    groupNumber: String,
    expiryDate: Date,
    copayAmount: Number,
    deductible: Number,
    coverageLimit: Number,
    isActive: {
      type: Boolean,
      default: true
    }
  },
  
  // Medical Information
  medicalHistory: {
    allergies: [String],
    currentMedications: [String],
    pastSurgeries: [String],
    chronicConditions: [String],
    familyHistory: [String],
    primaryDiagnosis: String,
    secondaryDiagnoses: [String],
    referringPhysician: String,
    notes: String
  },
  
  // Special Needs Assessment
  specialNeeds: {
    hasSpecialNeeds: {
      type: Boolean,
      default: false
    },
    types: [{
      type: String,
      enum: ['autism', 'cerebral_palsy', 'down_syndrome', 'adhd', 'intellectual_disability', 'sensory_impairment', 'other']
    }],
    severity: {
      type: String,
      enum: ['mild', 'moderate', 'severe']
    },
    communicationMethod: {
      type: String,
      enum: ['verbal', 'non_verbal', 'sign_language', 'picture_cards', 'assistive_device']
    },
    behavioralNotes: String,
    sensoryPreferences: {
      lighting: {
        type: String,
        enum: ['bright', 'dim', 'natural', 'no_preference']
      },
      sound: {
        type: String,
        enum: ['quiet', 'moderate', 'background_music', 'no_preference']
      },
      temperature: {
        type: String,
        enum: ['cool', 'warm', 'room_temperature', 'no_preference']
      }
    },
    adaptiveEquipment: [String],
    caregiverInstructions: String
  },
  
  // Status and Tracking
  status: {
    type: String,
    enum: ['active', 'inactive', 'discharged', 'transferred'],
    default: 'active'
  },
  registrationDate: {
    type: Date,
    default: Date.now
  },
  lastVisit: Date,
  nextAppointment: Date,
  
  // Assigned Staff
  primaryTherapist: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  assignedDoctor: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  // Documents and Files
  documents: [{
    name: String,
    type: {
      type: String,
      enum: ['medical_record', 'insurance_card', 'id_copy', 'referral', 'assessment', 'other']
    },
    url: String,
    uploadDate: {
      type: Date,
      default: Date.now
    },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Privacy and Consent
  consent: {
    treatmentConsent: {
      type: Boolean,
      default: false
    },
    dataProcessingConsent: {
      type: Boolean,
      default: false
    },
    marketingConsent: {
      type: Boolean,
      default: false
    },
    consentDate: Date,
    consentGivenBy: String // Name of person who gave consent (if not the patient)
  },
  
  // System Fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full name
patientSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for full Arabic name
patientSchema.virtual('fullNameAr').get(function() {
  if (this.firstNameAr && this.lastNameAr) {
    return `${this.firstNameAr} ${this.lastNameAr}`;
  }
  return null;
});

// Virtual for age
patientSchema.virtual('age').get(function() {
  if (this.dateOfBirth) {
    const today = new Date();
    const birthDate = new Date(this.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }
  return null;
});

// Virtual for insurance status
patientSchema.virtual('insuranceStatus').get(function() {
  if (!this.insurance || !this.insurance.provider) {
    return 'uninsured';
  }
  
  if (this.insurance.expiryDate && this.insurance.expiryDate < new Date()) {
    return 'expired';
  }
  
  return this.insurance.isActive ? 'active' : 'inactive';
});

// Indexes for better performance (nationalId already has unique index)
patientSchema.index({ firstName: 1, lastName: 1 });
patientSchema.index({ status: 1 });
patientSchema.index({ primaryTherapist: 1 });
patientSchema.index({ registrationDate: -1 });
patientSchema.index({ 'specialNeeds.hasSpecialNeeds': 1 });

// Pre-save middleware
patientSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.constructor.currentUser;
  }
  next();
});

// Static method to get patients by therapist
patientSchema.statics.getByTherapist = function(therapistId) {
  return this.find({ primaryTherapist: therapistId, status: 'active' });
};

// Static method to get special needs patients
patientSchema.statics.getSpecialNeedsPatients = function() {
  return this.find({ 'specialNeeds.hasSpecialNeeds': true, status: 'active' });
};

// Instance method to check if patient is minor
patientSchema.methods.isMinor = function() {
  return this.age < 18;
};

// Instance method to get next appointment
patientSchema.methods.getNextAppointment = async function() {
  const Appointment = mongoose.model('Appointment');
  return await Appointment.findOne({
    patient: this._id,
    date: { $gte: new Date() },
    status: { $in: ['scheduled', 'confirmed'] }
  }).sort({ date: 1 });
};

// Virtual fields for frontend compatibility
patientSchema.virtual('name').get(function() {
  if (this.firstNameAr && this.lastNameAr) {
    return `${this.firstNameAr} ${this.lastNameAr}`;
  }
  return `${this.firstName} ${this.lastName}`;
});

patientSchema.virtual('nameEn').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

patientSchema.virtual('nameAr').get(function() {
  if (this.firstNameAr && this.lastNameAr) {
    return `${this.firstNameAr} ${this.lastNameAr}`;
  }
  return '';
});

// Ensure virtual fields are serialized
patientSchema.set('toJSON', { virtuals: true });
patientSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Patient', patientSchema);
