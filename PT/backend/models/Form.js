const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Form:
 *       type: object
 *       required:
 *         - title
 *         - fields
 *         - category
 *       properties:
 *         title:
 *           type: string
 *           description: Form title
 *         fields:
 *           type: array
 *           items:
 *             type: object
 *           description: Form fields configuration
 *         category:
 *           type: string
 *           description: Form category
 */

const formSchema = new mongoose.Schema({
  // Basic information
  title: {
    type: String,
    required: [true, 'Form title is required'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  titleAr: {
    type: String,
    trim: true,
    maxlength: [200, 'Arabic title cannot be more than 200 characters']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  descriptionAr: {
    type: String,
    maxlength: [1000, 'Arabic description cannot be more than 1000 characters']
  },
  
  // Form configuration
  category: {
    type: String,
    required: [true, 'Form category is required'],
    enum: ['intake', 'assessment', 'progress', 'discharge', 'consent', 'insurance', 'special_needs', 'custom']
  },
  version: {
    type: String,
    default: '1.0'
  },
  isTemplate: {
    type: Boolean,
    default: false
  },
  
  // Form fields
  fields: [{
    id: {
      type: String,
      required: true
    },
    type: {
      type: String,
      required: true,
      enum: ['text', 'textarea', 'number', 'email', 'phone', 'date', 'time', 'datetime', 
             'select', 'radio', 'checkbox', 'file', 'signature', 'rating', 'pain_scale', 'range']
    },
    label: {
      type: String,
      required: true
    },
    labelAr: String,
    placeholder: String,
    placeholderAr: String,
    helpText: String,
    helpTextAr: String,
    required: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      required: true
    },
    
    // Field-specific configurations
    options: [{
      value: String,
      label: String,
      labelAr: String
    }],
    validation: {
      min: Number,
      max: Number,
      minLength: Number,
      maxLength: Number,
      pattern: String,
      customValidation: String
    },
    conditional: {
      dependsOn: String, // field ID
      condition: String, // equals, not_equals, contains, etc.
      value: mongoose.Schema.Types.Mixed
    },
    
    // Layout and styling
    width: {
      type: String,
      enum: ['full', 'half', 'third', 'quarter'],
      default: 'full'
    },
    section: String,
    isHidden: {
      type: Boolean,
      default: false
    }
  }],
  
  // Form sections
  sections: [{
    id: String,
    title: String,
    titleAr: String,
    description: String,
    descriptionAr: String,
    order: Number,
    collapsible: {
      type: Boolean,
      default: false
    },
    collapsed: {
      type: Boolean,
      default: false
    }
  }],
  
  // Form settings
  settings: {
    allowMultipleSubmissions: {
      type: Boolean,
      default: false
    },
    requireAuthentication: {
      type: Boolean,
      default: true
    },
    saveProgress: {
      type: Boolean,
      default: true
    },
    showProgressBar: {
      type: Boolean,
      default: true
    },
    submitButtonText: {
      type: String,
      default: 'Submit'
    },
    submitButtonTextAr: String,
    successMessage: String,
    successMessageAr: String,
    redirectUrl: String,
    emailNotifications: {
      enabled: {
        type: Boolean,
        default: false
      },
      recipients: [String],
      subject: String,
      template: String
    }
  },
  
  // Access control
  permissions: {
    canView: [{
      type: String,
      enum: ['admin', 'doctor', 'therapist', 'nurse', 'receptionist', 'patient']
    }],
    canEdit: [{
      type: String,
      enum: ['admin', 'doctor', 'therapist', 'nurse', 'receptionist']
    }],
    canSubmit: [{
      type: String,
      enum: ['admin', 'doctor', 'therapist', 'nurse', 'receptionist', 'patient']
    }]
  },
  
  // Status and lifecycle
  status: {
    type: String,
    enum: ['draft', 'active', 'inactive', 'archived'],
    default: 'draft'
  },
  publishedAt: Date,
  archivedAt: Date,
  
  // Usage statistics
  stats: {
    totalSubmissions: {
      type: Number,
      default: 0
    },
    completedSubmissions: {
      type: Number,
      default: 0
    },
    averageCompletionTime: Number, // in seconds
    lastSubmissionDate: Date
  },
  
  // System fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for completion rate
formSchema.virtual('completionRate').get(function() {
  if (this.stats.totalSubmissions === 0) return 0;
  return Math.round((this.stats.completedSubmissions / this.stats.totalSubmissions) * 100);
});

// Virtual for field count
formSchema.virtual('fieldCount').get(function() {
  return this.fields ? this.fields.length : 0;
});

// Indexes for better performance
formSchema.index({ category: 1 });
formSchema.index({ status: 1 });
formSchema.index({ isTemplate: 1 });
formSchema.index({ createdAt: -1 });
formSchema.index({ 'permissions.canView': 1 });

// Pre-save middleware
formSchema.pre('save', function(next) {
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.constructor.currentUser;
  }
  
  // Set published date when status changes to active
  if (this.isModified('status') && this.status === 'active' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  
  // Set archived date when status changes to archived
  if (this.isModified('status') && this.status === 'archived' && !this.archivedAt) {
    this.archivedAt = new Date();
  }
  
  next();
});

// Static method to get forms by category
formSchema.statics.getByCategory = function(category, userRole) {
  const query = {
    category: category,
    status: 'active'
  };
  
  if (userRole) {
    query['permissions.canView'] = userRole;
  }
  
  return this.find(query).sort({ createdAt: -1 });
};

// Static method to get templates
formSchema.statics.getTemplates = function() {
  return this.find({
    isTemplate: true,
    status: { $in: ['active', 'draft'] }
  }).sort({ category: 1, title: 1 });
};

// Instance method to duplicate form
formSchema.methods.duplicate = function(newTitle) {
  const duplicatedForm = new this.constructor({
    title: newTitle || `${this.title} (Copy)`,
    titleAr: this.titleAr ? `${this.titleAr} (نسخة)` : undefined,
    description: this.description,
    descriptionAr: this.descriptionAr,
    category: this.category,
    fields: this.fields.map(field => ({
      ...field.toObject(),
      _id: undefined
    })),
    sections: this.sections.map(section => ({
      ...section.toObject(),
      _id: undefined
    })),
    settings: this.settings,
    permissions: this.permissions,
    status: 'draft',
    isTemplate: false,
    createdBy: this.constructor.currentUser
  });
  
  return duplicatedForm.save();
};

// Instance method to validate field configuration
formSchema.methods.validateFields = function() {
  const errors = [];
  
  this.fields.forEach((field, index) => {
    // Check required properties
    if (!field.id) {
      errors.push(`Field ${index + 1}: ID is required`);
    }
    
    if (!field.label) {
      errors.push(`Field ${index + 1}: Label is required`);
    }
    
    // Check for duplicate IDs
    const duplicateId = this.fields.find((f, i) => i !== index && f.id === field.id);
    if (duplicateId) {
      errors.push(`Field ${index + 1}: Duplicate ID "${field.id}"`);
    }
    
    // Validate field-specific requirements
    if (['select', 'radio', 'checkbox'].includes(field.type) && (!field.options || field.options.length === 0)) {
      errors.push(`Field ${index + 1}: Options are required for ${field.type} fields`);
    }
  });
  
  return errors;
};

module.exports = mongoose.model('Form', formSchema);
