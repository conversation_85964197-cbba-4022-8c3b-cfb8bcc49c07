const mongoose = require('mongoose');

/**
 * @swagger
 * components:
 *   schemas:
 *     Appointment:
 *       type: object
 *       required:
 *         - patient
 *         - therapist
 *         - date
 *         - startTime
 *         - endTime
 *         - type
 *       properties:
 *         patient:
 *           type: string
 *           description: Patient ID
 *         therapist:
 *           type: string
 *           description: Therapist ID
 *         date:
 *           type: string
 *           format: date
 *           description: Appointment date
 *         startTime:
 *           type: string
 *           description: Appointment start time
 *         endTime:
 *           type: string
 *           description: Appointment end time
 *         type:
 *           type: string
 *           enum: [consultation, treatment, follow_up, assessment]
 *           description: Type of appointment
 */

const appointmentSchema = new mongoose.Schema({
  // Core appointment information
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: [true, 'Patient is required']
  },
  therapist: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Therapist is required']
  },
  
  // Date and time
  date: {
    type: Date,
    required: [true, 'Appointment date is required'],
    validate: {
      validator: function(value) {
        return value >= new Date().setHours(0, 0, 0, 0);
      },
      message: 'Appointment date cannot be in the past'
    }
  },
  startTime: {
    type: String,
    required: [true, 'Start time is required'],
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Start time must be in HH:MM format']
  },
  endTime: {
    type: String,
    required: [true, 'End time is required'],
    match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'End time must be in HH:MM format']
  },
  duration: {
    type: Number, // in minutes
    default: function() {
      if (this.startTime && this.endTime) {
        const start = this.startTime.split(':');
        const end = this.endTime.split(':');
        const startMinutes = parseInt(start[0]) * 60 + parseInt(start[1]);
        const endMinutes = parseInt(end[0]) * 60 + parseInt(end[1]);
        return endMinutes - startMinutes;
      }
      return 60; // default 1 hour
    }
  },
  
  // Appointment details
  type: {
    type: String,
    required: [true, 'Appointment type is required'],
    enum: {
      values: ['consultation', 'treatment', 'follow_up', 'assessment', 'group_session'],
      message: 'Type must be one of: consultation, treatment, follow_up, assessment, group_session'
    }
  },
  status: {
    type: String,
    enum: {
      values: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show', 'rescheduled'],
      message: 'Status must be one of: scheduled, confirmed, in_progress, completed, cancelled, no_show, rescheduled'
    },
    default: 'scheduled'
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Treatment information
  treatmentPlan: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TreatmentPlan'
  },
  sessionNumber: {
    type: Number,
    min: 1
  },
  totalSessions: Number,
  
  // Location and room
  room: {
    type: String,
    trim: true
  },
  location: {
    type: String,
    default: 'Main Clinic'
  },
  
  // Notes and instructions
  notes: {
    type: String,
    maxlength: [1000, 'Notes cannot be more than 1000 characters']
  },
  specialInstructions: {
    type: String,
    maxlength: [500, 'Special instructions cannot be more than 500 characters']
  },
  
  // Cancellation/Rescheduling
  cancellationReason: String,
  cancelledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  cancelledAt: Date,
  rescheduledFrom: {
    date: Date,
    startTime: String,
    endTime: String
  },
  rescheduledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  rescheduledAt: Date,
  
  // Reminders and notifications
  reminders: [{
    type: {
      type: String,
      enum: ['email', 'sms', 'push'],
      required: true
    },
    sentAt: Date,
    status: {
      type: String,
      enum: ['pending', 'sent', 'failed'],
      default: 'pending'
    }
  }],
  
  // Billing information
  billing: {
    amount: {
      type: Number,
      min: 0
    },
    insuranceCovered: {
      type: Boolean,
      default: false
    },
    copayAmount: {
      type: Number,
      min: 0
    },
    paymentStatus: {
      type: String,
      enum: ['pending', 'paid', 'partially_paid', 'insurance_pending'],
      default: 'pending'
    }
  },
  
  // Session outcome (filled after completion)
  outcome: {
    attended: {
      type: Boolean,
      default: null
    },
    completedOn: Date,
    sessionNotes: String,
    progressNotes: String,
    nextSteps: String,
    homeExercises: [String],
    followUpRequired: {
      type: Boolean,
      default: false
    },
    followUpDate: Date,
    patientSatisfaction: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  
  // Special needs accommodations
  accommodations: {
    sensoryNeeds: {
      lighting: String,
      sound: String,
      temperature: String
    },
    communicationAids: [String],
    assistiveDevices: [String],
    behavioralSupport: String
  },
  
  // System fields
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for appointment datetime
appointmentSchema.virtual('datetime').get(function() {
  if (this.date && this.startTime) {
    const [hours, minutes] = this.startTime.split(':');
    const datetime = new Date(this.date);
    datetime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    return datetime;
  }
  return null;
});

// Virtual for end datetime
appointmentSchema.virtual('endDatetime').get(function() {
  if (this.date && this.endTime) {
    const [hours, minutes] = this.endTime.split(':');
    const datetime = new Date(this.date);
    datetime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    return datetime;
  }
  return null;
});

// Virtual for status display
appointmentSchema.virtual('statusDisplay').get(function() {
  const statusMap = {
    scheduled: 'Scheduled',
    confirmed: 'Confirmed',
    in_progress: 'In Progress',
    completed: 'Completed',
    cancelled: 'Cancelled',
    no_show: 'No Show',
    rescheduled: 'Rescheduled'
  };
  return statusMap[this.status] || this.status;
});

// Indexes for better performance
appointmentSchema.index({ patient: 1, date: 1 });
appointmentSchema.index({ therapist: 1, date: 1 });
appointmentSchema.index({ date: 1, startTime: 1 });
appointmentSchema.index({ status: 1 });
appointmentSchema.index({ type: 1 });
appointmentSchema.index({ createdAt: -1 });

// Compound index for conflict checking
appointmentSchema.index({ 
  therapist: 1, 
  date: 1, 
  startTime: 1, 
  endTime: 1,
  status: 1 
});

// Pre-save middleware to calculate duration
appointmentSchema.pre('save', function(next) {
  if (this.isModified('startTime') || this.isModified('endTime')) {
    if (this.startTime && this.endTime) {
      const start = this.startTime.split(':');
      const end = this.endTime.split(':');
      const startMinutes = parseInt(start[0]) * 60 + parseInt(start[1]);
      const endMinutes = parseInt(end[0]) * 60 + parseInt(end[1]);
      this.duration = endMinutes - startMinutes;
    }
  }
  
  if (this.isModified() && !this.isNew) {
    this.updatedBy = this.constructor.currentUser;
  }
  
  next();
});

// Pre-save validation for time conflicts
appointmentSchema.pre('save', async function(next) {
  if (this.isNew || this.isModified('date') || this.isModified('startTime') || this.isModified('endTime') || this.isModified('therapist')) {
    // Check for therapist conflicts
    const conflictingAppointment = await this.constructor.findOne({
      _id: { $ne: this._id },
      therapist: this.therapist,
      date: this.date,
      status: { $in: ['scheduled', 'confirmed', 'in_progress'] },
      $or: [
        {
          startTime: { $lt: this.endTime },
          endTime: { $gt: this.startTime }
        }
      ]
    });
    
    if (conflictingAppointment) {
      const error = new Error('Therapist has a conflicting appointment at this time');
      error.name = 'ValidationError';
      return next(error);
    }
  }
  
  next();
});

// Static method to get appointments by date range
appointmentSchema.statics.getByDateRange = function(startDate, endDate, filters = {}) {
  const query = {
    date: {
      $gte: startDate,
      $lte: endDate
    },
    ...filters
  };
  
  return this.find(query)
    .populate('patient', 'firstName lastName nationalId')
    .populate('therapist', 'firstName lastName specialization')
    .sort({ date: 1, startTime: 1 });
};

// Static method to get therapist availability
appointmentSchema.statics.getTherapistAvailability = async function(therapistId, date) {
  const appointments = await this.find({
    therapist: therapistId,
    date: date,
    status: { $in: ['scheduled', 'confirmed', 'in_progress'] }
  }).select('startTime endTime').sort({ startTime: 1 });
  
  return appointments;
};

// Instance method to check if appointment can be cancelled
appointmentSchema.methods.canBeCancelled = function() {
  const now = new Date();
  const appointmentTime = this.datetime;
  const hoursDifference = (appointmentTime - now) / (1000 * 60 * 60);
  
  return hoursDifference >= 24 && ['scheduled', 'confirmed'].includes(this.status);
};

// Instance method to send reminder
appointmentSchema.methods.sendReminder = async function(type = 'email') {
  // Implementation would depend on notification service
  this.reminders.push({
    type: type,
    sentAt: new Date(),
    status: 'sent'
  });
  
  await this.save();
};

module.exports = mongoose.model('Appointment', appointmentSchema);
