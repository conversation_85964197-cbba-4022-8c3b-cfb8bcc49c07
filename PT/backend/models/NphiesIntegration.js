const mongoose = require('mongoose');

const nphiesIntegrationSchema = new mongoose.Schema({
  // Patient Information
  patient: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Patient',
    required: true
  },
  
  // NPHIES Patient ID
  nphiesPatientId: {
    type: String,
    required: true,
    unique: true
  },
  
  // Insurance Information
  insuranceInfo: {
    payerId: {
      type: String,
      required: true
    },
    payerName: {
      type: String,
      required: true
    },
    membershipNumber: {
      type: String,
      required: true
    },
    policyNumber: String,
    coverageType: {
      type: String,
      enum: ['basic', 'comprehensive', 'premium'],
      default: 'basic'
    },
    validFrom: {
      type: Date,
      required: true
    },
    validTo: {
      type: Date,
      required: true
    },
    copayAmount: {
      type: Number,
      default: 0
    },
    deductibleAmount: {
      type: Number,
      default: 0
    }
  },
  
  // Eligibility Verification
  eligibilityStatus: {
    type: String,
    enum: ['verified', 'pending', 'rejected', 'expired'],
    default: 'pending'
  },
  
  eligibilityResponse: {
    requestId: String,
    responseId: String,
    status: String,
    coverageDetails: [{
      serviceType: String,
      covered: Boolean,
      copayAmount: Number,
      deductibleAmount: Number,
      maxBenefit: Number,
      usedBenefit: Number,
      remainingBenefit: Number
    }],
    lastVerified: Date,
    expiryDate: Date
  },
  
  // Pre-authorization
  preAuthorizations: [{
    requestId: String,
    authorizationNumber: String,
    serviceType: String,
    requestedAmount: Number,
    approvedAmount: Number,
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'expired'],
      default: 'pending'
    },
    validFrom: Date,
    validTo: Date,
    notes: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Claims
  claims: [{
    claimId: String,
    nphiesClaimId: String,
    billing: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Billing'
    },
    claimType: {
      type: String,
      enum: ['institutional', 'professional', 'pharmacy'],
      default: 'professional'
    },
    totalAmount: Number,
    approvedAmount: Number,
    paidAmount: Number,
    status: {
      type: String,
      enum: ['submitted', 'under_review', 'approved', 'rejected', 'paid'],
      default: 'submitted'
    },
    submissionDate: Date,
    responseDate: Date,
    paymentDate: Date,
    rejectionReason: String,
    documents: [{
      type: String,
      url: String,
      uploadDate: Date
    }]
  }],
  
  // Visa Integration
  visaInfo: {
    visaNumber: String,
    visaType: {
      type: String,
      enum: ['work', 'visit', 'residence', 'hajj', 'umrah', 'transit']
    },
    issueDate: Date,
    expiryDate: Date,
    sponsorInfo: {
      name: String,
      id: String,
      type: String
    },
    status: {
      type: String,
      enum: ['valid', 'expired', 'cancelled'],
      default: 'valid'
    },
    linkedServices: [{
      service: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Billing'
      },
      linkedDate: Date,
      verificationStatus: {
        type: String,
        enum: ['verified', 'pending', 'failed'],
        default: 'pending'
      }
    }]
  },
  
  // Electronic Signatures
  electronicSignatures: [{
    documentType: {
      type: String,
      enum: ['consent', 'treatment_plan', 'invoice', 'discharge', 'prescription'],
      required: true
    },
    documentId: String,
    signatureData: {
      digitalSignature: String,
      timestamp: Date,
      ipAddress: String,
      deviceInfo: String,
      biometricData: String // For advanced biometric signatures
    },
    signerInfo: {
      name: String,
      nationalId: String,
      role: {
        type: String,
        enum: ['patient', 'guardian', 'therapist', 'doctor'],
        required: true
      }
    },
    verificationStatus: {
      type: String,
      enum: ['valid', 'invalid', 'expired'],
      default: 'valid'
    },
    certificateInfo: {
      issuer: String,
      serialNumber: String,
      validFrom: Date,
      validTo: Date
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // ZATCA Integration
  zatcaInfo: {
    taxNumber: String,
    registrationStatus: {
      type: String,
      enum: ['registered', 'pending', 'rejected'],
      default: 'pending'
    },
    lastSyncDate: Date,
    invoiceHashes: [{
      invoiceId: String,
      hash: String,
      qrCode: String,
      submissionDate: Date,
      status: String
    }]
  },
  
  // Audit Trail
  auditLog: [{
    action: String,
    performedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    timestamp: {
      type: Date,
      default: Date.now
    },
    details: mongoose.Schema.Types.Mixed,
    ipAddress: String
  }],
  
  // Status and Metadata
  isActive: {
    type: Boolean,
    default: true
  },
  
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for better performance
nphiesIntegrationSchema.index({ patient: 1 });
nphiesIntegrationSchema.index({ nphiesPatientId: 1 });
nphiesIntegrationSchema.index({ 'insuranceInfo.membershipNumber': 1 });
nphiesIntegrationSchema.index({ 'visaInfo.visaNumber': 1 });
nphiesIntegrationSchema.index({ eligibilityStatus: 1 });
nphiesIntegrationSchema.index({ 'claims.status': 1 });

// Virtual for active eligibility
nphiesIntegrationSchema.virtual('hasActiveEligibility').get(function() {
  return this.eligibilityStatus === 'verified' && 
         this.eligibilityResponse.expiryDate > new Date();
});

// Virtual for active pre-authorization
nphiesIntegrationSchema.virtual('hasActivePreAuth').get(function() {
  return this.preAuthorizations.some(auth => 
    auth.status === 'approved' && auth.validTo > new Date()
  );
});

// Method to verify eligibility
nphiesIntegrationSchema.methods.verifyEligibility = async function() {
  // This will be implemented with actual NPHIES API calls
  this.eligibilityResponse.lastVerified = new Date();
  return this.save();
};

// Method to submit claim
nphiesIntegrationSchema.methods.submitClaim = async function(claimData) {
  // This will be implemented with actual NPHIES API calls
  this.claims.push({
    ...claimData,
    submissionDate: new Date(),
    status: 'submitted'
  });
  return this.save();
};

// Method to add electronic signature
nphiesIntegrationSchema.methods.addElectronicSignature = function(signatureData) {
  this.electronicSignatures.push({
    ...signatureData,
    createdAt: new Date()
  });
  return this.save();
};

// Pre-save middleware
nphiesIntegrationSchema.pre('save', function(next) {
  this.lastUpdated = new Date();
  next();
});

module.exports = mongoose.model('NphiesIntegration', nphiesIntegrationSchema);
