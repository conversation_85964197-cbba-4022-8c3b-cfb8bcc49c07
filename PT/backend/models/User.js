const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - firstName
 *         - lastName
 *         - email
 *         - password
 *         - role
 *       properties:
 *         firstName:
 *           type: string
 *           description: User's first name
 *         lastName:
 *           type: string
 *           description: User's last name
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         phone:
 *           type: string
 *           description: User's phone number
 *         role:
 *           type: string
 *           enum: [admin, doctor, therapist, nurse, receptionist]
 *           description: User's role in the system
 *         specialization:
 *           type: string
 *           description: Medical specialization (for doctors/therapists)
 *         licenseNumber:
 *           type: string
 *           description: Professional license number
 *         isActive:
 *           type: boolean
 *           description: Whether the user account is active
 *         avatar:
 *           type: string
 *           description: URL to user's profile picture
 */

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: [true, 'First name is required'],
    trim: true,
    maxlength: [50, 'First name cannot be more than 50 characters']
  },
  lastName: {
    type: String,
    required: [true, 'Last name is required'],
    trim: true,
    maxlength: [50, 'Last name cannot be more than 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email'
    ]
  },
  phone: {
    type: String,
    match: [/^\+?[1-9]\d{1,14}$/, 'Please provide a valid phone number']
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 8 characters'],
    select: false // Don't include password in queries by default
  },
  role: {
    type: String,
    enum: {
      values: ['admin', 'doctor', 'therapist', 'nurse', 'receptionist', 'manager', 'lab_technician', 'radiology_technician', 'external_lab', 'external_radiology'],
      message: 'Role must be one of: admin, doctor, therapist, nurse, receptionist, manager, lab_technician, radiology_technician, external_lab, external_radiology'
    },
    required: [true, 'User role is required']
  },
  specialization: {
    type: String,
    trim: true,
    maxlength: [100, 'Specialization cannot be more than 100 characters']
  },
  licenseNumber: {
    type: String,
    trim: true,
    maxlength: [50, 'License number cannot be more than 50 characters']
  },
  department: {
    type: String,
    trim: true,
    maxlength: [100, 'Department cannot be more than 100 characters']
  },

  // Detailed permissions for medical system access
  // تحديد صلاحيات إدخال البيانات في النظام الطبي الإلكتروني
  permissions: {
    // Form Access Permissions - صلاحيات الوصول للنماذج
    forms: {
      // Doctor Permissions - صلاحيات الطبيب
      medicalDiagnosis: { type: Boolean, default: false }, // التشخيص الطبي
      ptEvaluation: { type: Boolean, default: false }, // تقييم العلاج الطبيعي
      reassessment: { type: Boolean, default: false }, // إعادة التقييم
      dischargeEvaluation: { type: Boolean, default: false }, // تقييم الخروج
      followUpForm: { type: Boolean, default: false }, // نموذج المتابعة
      patientEducationDoctor: { type: Boolean, default: false }, // نموذج تثقيف المريض (طبيب)

      // Physical Therapist Permissions - صلاحيات أخصائي العلاج الطبيعي
      progressNotes: { type: Boolean, default: false }, // ملاحظات تقدم الحالة
      patientEducationTherapist: { type: Boolean, default: false }, // نموذج تثقيف المريض (أخصائي)

      // Nursing Permissions - صلاحيات التمريض
      patientEducationNurse: { type: Boolean, default: false }, // نموذج تثقيف المريض (تمريض)
      nursingForm: { type: Boolean, default: false }, // نموذج التمريض التابع للمركز

      // Clinical Indicators Access - صلاحيات المؤشرات السريرية
      clinicalIndicators: { type: Boolean, default: false }, // المؤشرات السريرية
      bergBalanceScale: { type: Boolean, default: false }, // مقياس بيرغ للتوازن
      tugTest: { type: Boolean, default: false }, // اختبار الوقوف والمشي
      manualMuscleTesting: { type: Boolean, default: false }, // اختبار العضلات اليدوي
      painScaleVAS: { type: Boolean, default: false }, // مقياس الألم
      functionalIndependence: { type: Boolean, default: false }, // مقياس الاستقلالية الوظيفية

      // External Services Forms - نماذج الخدمات الخارجية
      labResults: { type: Boolean, default: false }, // نتائج المختبر
      radiologyResults: { type: Boolean, default: false } // نتائج الأشعة
    },

    // System Access Permissions - صلاحيات الوصول للنظام
    system: {
      viewPatients: { type: Boolean, default: false }, // عرض المرضى
      createPatients: { type: Boolean, default: false }, // إنشاء مرضى
      editPatients: { type: Boolean, default: false }, // تعديل المرضى
      deletePatients: { type: Boolean, default: false }, // حذف المرضى
      viewAppointments: { type: Boolean, default: false }, // عرض المواعيد
      createAppointments: { type: Boolean, default: false }, // إنشاء مواعيد
      editAppointments: { type: Boolean, default: false }, // تعديل المواعيد
      deleteAppointments: { type: Boolean, default: false }, // حذف المواعيد
      viewBilling: { type: Boolean, default: false }, // عرض الفواتير
      createBilling: { type: Boolean, default: false }, // إنشاء فواتير
      editBilling: { type: Boolean, default: false }, // تعديل الفواتير
      viewReports: { type: Boolean, default: false }, // عرض التقارير
      viewAnalytics: { type: Boolean, default: false }, // عرض التحليلات
      manageUsers: { type: Boolean, default: false }, // إدارة المستخدمين
      systemSettings: { type: Boolean, default: false } // إعدادات النظام
    },

    // Clinical Analytics Permissions - صلاحيات التحليلات السريرية
    analytics: {
      functionalIndependenceComparison: { type: Boolean, default: false }, // درجة الاستقلالية الوظيفية قبل وبعد البرنامج العلاجي
      treatmentPlanEffectiveness: { type: Boolean, default: false }, // نسبة التحسن السريري حسب نوع الخطة العلاجية المستخدمة
      clinicalProgressTracking: { type: Boolean, default: false }, // تتبع التقدم السريري
      outcomeMetrics: { type: Boolean, default: false }, // مقاييس النتائج
      qualityIndicators: { type: Boolean, default: false } // مؤشرات الجودة
    }
  },

  // External Service Provider Information - معلومات مقدمي الخدمات الخارجية
  externalServiceInfo: {
    contractNumber: { type: String, default: null }, // رقم العقد
    contractStartDate: { type: Date, default: null }, // تاريخ بداية العقد
    contractEndDate: { type: Date, default: null }, // تاريخ انتهاء العقد
    serviceType: {
      type: String,
      enum: ['laboratory', 'radiology', 'pharmacy', 'other'],
      default: null
    }, // نوع الخدمة
    contactPerson: { type: String, default: null }, // الشخص المسؤول
    emergencyContact: { type: String, default: null }, // جهة الاتصال الطارئة
    serviceAreas: [{ type: String }], // مناطق الخدمة
    qualityCertifications: [{ type: String }] // شهادات الجودة
  },
  isActive: {
    type: Boolean,
    default: true
  },
  avatar: {
    type: String,
    default: null
  },
  lastLogin: {
    type: Date,
    default: null
  },
  passwordChangedAt: {
    type: Date,
    default: Date.now
  },
  passwordResetToken: String,
  passwordResetExpires: Date,
  emailVerificationToken: String,
  emailVerified: {
    type: Boolean,
    default: false
  },
  twoFactorEnabled: {
    type: Boolean,
    default: false
  },
  preferences: {
    language: {
      type: String,
      enum: ['en', 'ar'],
      default: 'en'
    },
    theme: {
      type: String,
      enum: ['light', 'dark'],
      default: 'light'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      },
      push: {
        type: Boolean,
        default: true
      }
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full name
userSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Index for better performance (email already has unique index)
userSchema.index({ role: 1 });
userSchema.index({ isActive: 1 });

// Pre-save middleware to hash password
userSchema.pre('save', async function(next) {
  // Only run this function if password was actually modified
  if (!this.isModified('password')) return next();

  // Hash the password with cost of 12
  this.password = await bcrypt.hash(this.password, 12);

  // Update passwordChangedAt
  if (!this.isNew) {
    this.passwordChangedAt = Date.now() - 1000;
  }

  next();
});

// Instance method to check password
userSchema.methods.correctPassword = async function(candidatePassword, userPassword) {
  return await bcrypt.compare(candidatePassword, userPassword);
};

// Instance method to check if password changed after JWT was issued
userSchema.methods.changedPasswordAfter = function(JWTTimestamp) {
  if (this.passwordChangedAt) {
    const changedTimestamp = parseInt(
      this.passwordChangedAt.getTime() / 1000,
      10
    );
    return JWTTimestamp < changedTimestamp;
  }
  return false;
};

// Instance method to generate JWT token
userSchema.methods.getSignedJwtToken = function() {
  return jwt.sign(
    { 
      id: this._id,
      role: this.role,
      permissions: this.permissions
    },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRE }
  );
};

// Instance method to generate password reset token
userSchema.methods.getResetPasswordToken = function() {
  // Generate token
  const resetToken = require('crypto').randomBytes(20).toString('hex');

  // Hash token and set to resetPasswordToken field
  this.passwordResetToken = require('crypto')
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  // Set expire
  this.passwordResetExpires = Date.now() + 10 * 60 * 1000; // 10 minutes

  return resetToken;
};

// Method to set default permissions based on role
// تحديد الصلاحيات الافتراضية حسب الدور الوظيفي
userSchema.methods.setDefaultPermissions = function() {
  const rolePermissions = {
    admin: {
      forms: {
        medicalDiagnosis: true,
        ptEvaluation: true,
        reassessment: true,
        dischargeEvaluation: true,
        followUpForm: true,
        patientEducationDoctor: true,
        progressNotes: true,
        patientEducationTherapist: true,
        patientEducationNurse: true,
        nursingForm: true,
        clinicalIndicators: true,
        specialNeedsAssessment: true,
        planOfCare: true,
        dailyProgress: true,
        labResults: true,
        radiologyResults: true
      },
      system: {
        viewPatients: true,
        createPatients: true,
        editPatients: true,
        deletePatients: true,
        viewAppointments: true,
        createAppointments: true,
        editAppointments: true,
        deleteAppointments: true,
        viewAnalytics: true,
        manageUsers: true,
        systemSettings: true
      },
      analytics: {
        functionalIndependenceComparison: true,
        treatmentPlanEffectiveness: true,
        clinicalProgressTracking: true,
        outcomeMetrics: true,
        complianceReporting: true
      }
    },

    // صلاحيات الطبيب - Doctor Permissions
    doctor: {
      forms: {
        medicalDiagnosis: true,           // 1. التشخيص الطبي
        ptEvaluation: true,               // 2. تقييم العلاج الطبيعي
        reassessment: true,               // 3. إعادة التقييم
        dischargeEvaluation: true,        // 4. تقييم الخروج
        followUpForm: true,               // 5. نموذج المتابعة
        patientEducationDoctor: true,     // 6. نموذج تثقيف المريض
        clinicalIndicators: true,         // المؤشرات السريرية (للمراجعة)
        specialNeedsAssessment: true,     // تقييم الاحتياجات الخاصة
        planOfCare: true,                 // خطة الرعاية
        progressNotes: false,             // ملاحظات التقدم (أخصائي فقط)
        patientEducationTherapist: false,
        patientEducationNurse: false,
        nursingForm: false,
        dailyProgress: false,
        labResults: true,                 // عرض نتائج المختبر
        radiologyResults: true            // عرض نتائج الأشعة
      },
      system: {
        viewPatients: true,
        createPatients: true,
        editPatients: true,
        deletePatients: false,
        viewAppointments: true,
        createAppointments: true,
        editAppointments: true,
        deleteAppointments: false,
        viewAnalytics: true,
        manageUsers: false,
        systemSettings: false
      },
      analytics: {
        functionalIndependenceComparison: true,
        treatmentPlanEffectiveness: true,
        clinicalProgressTracking: true,
        outcomeMetrics: true,
        complianceReporting: true
      }
    },

    // صلاحيات أخصائي العلاج الطبيعي - Physical Therapist Permissions
    therapist: {
      forms: {
        medicalDiagnosis: false,          // التشخيص الطبي (طبيب فقط)
        ptEvaluation: false,              // تقييم العلاج الطبيعي (طبيب فقط)
        reassessment: false,              // إعادة التقييم (طبيب فقط)
        dischargeEvaluation: false,       // تقييم الخروج (طبيب فقط)
        followUpForm: false,              // نموذج المتابعة (طبيب فقط)
        patientEducationDoctor: false,
        progressNotes: true,              // 1. ملاحظات تقدم الحالة (Progress Notes)
        patientEducationTherapist: true,  // 2. نموذج تثقيف المريض
        clinicalIndicators: true,         // المؤشرات السريرية
        specialNeedsAssessment: true,     // تقييم الاحتياجات الخاصة
        planOfCare: false,                // خطة الرعاية (طبيب فقط)
        dailyProgress: true,              // التقدم اليومي
        patientEducationNurse: false,
        nursingForm: false,
        labResults: false,                // عرض فقط
        radiologyResults: false           // عرض فقط
      },
      system: {
        viewPatients: true,
        createPatients: false,
        editPatients: false,
        deletePatients: false,
        viewAppointments: true,
        createAppointments: false,
        editAppointments: false,
        deleteAppointments: false,
        viewAnalytics: true,
        manageUsers: false,
        systemSettings: false
      },
      analytics: {
        functionalIndependenceComparison: true,
        treatmentPlanEffectiveness: true,
        clinicalProgressTracking: true,
        outcomeMetrics: true,
        complianceReporting: false
      }
    },

    // صلاحيات التمريض - Nursing Permissions
    nurse: {
      forms: {
        medicalDiagnosis: false,          // التشخيص الطبي (طبيب فقط)
        ptEvaluation: false,              // تقييم العلاج الطبيعي (طبيب فقط)
        reassessment: false,              // إعادة التقييم (طبيب فقط)
        dischargeEvaluation: false,       // تقييم الخروج (طبيب فقط)
        followUpForm: false,              // نموذج المتابعة (طبيب فقط)
        patientEducationDoctor: false,
        progressNotes: false,             // ملاحظات التقدم (أخصائي فقط)
        patientEducationTherapist: false,
        patientEducationNurse: true,      // 1. نموذج تثقيف المريض
        nursingForm: true,                // 2. نموذج التمريض التابع للمركز
        clinicalIndicators: false,        // المؤشرات السريرية (عرض فقط)
        specialNeedsAssessment: false,
        planOfCare: false,
        dailyProgress: false,
        labResults: false,
        radiologyResults: false
      },
      system: {
        viewPatients: true,
        createPatients: false,
        editPatients: false,
        deletePatients: false,
        viewAppointments: true,
        createAppointments: false,
        editAppointments: false,
        deleteAppointments: false,
        viewAnalytics: false,
        manageUsers: false,
        systemSettings: false
      },
      analytics: {
        functionalIndependenceComparison: false,
        treatmentPlanEffectiveness: false,
        clinicalProgressTracking: false,
        outcomeMetrics: false,
        complianceReporting: false
      }
    },

    // صلاحيات المختبرات الخارجية - External Laboratory Permissions
    external_lab: {
      forms: {
        medicalDiagnosis: false,
        ptEvaluation: false,
        reassessment: false,
        dischargeEvaluation: false,
        followUpForm: false,
        patientEducationDoctor: false,
        progressNotes: false,
        patientEducationTherapist: false,
        patientEducationNurse: false,
        nursingForm: false,
        clinicalIndicators: false,
        specialNeedsAssessment: false,
        planOfCare: false,
        dailyProgress: false,
        labResults: true,                 // إدخال نتائج المختبر
        radiologyResults: false
      },
      system: {
        viewPatients: true,               // عرض المرضى المحولين فقط
        createPatients: false,
        editPatients: false,
        deletePatients: false,
        viewAppointments: false,
        createAppointments: false,
        editAppointments: false,
        deleteAppointments: false,
        viewAnalytics: false,
        manageUsers: false,
        systemSettings: false
      },
      analytics: {
        functionalIndependenceComparison: false,
        treatmentPlanEffectiveness: false,
        clinicalProgressTracking: false,
        outcomeMetrics: false,
        complianceReporting: false
      }
    },

    // صلاحيات مراكز الأشعة الخارجية - External Radiology Permissions
    external_radiology: {
      forms: {
        medicalDiagnosis: false,
        ptEvaluation: false,
        reassessment: false,
        dischargeEvaluation: false,
        followUpForm: false,
        patientEducationDoctor: false,
        progressNotes: false,
        patientEducationTherapist: false,
        patientEducationNurse: false,
        nursingForm: false,
        clinicalIndicators: false,
        specialNeedsAssessment: false,
        planOfCare: false,
        dailyProgress: false,
        labResults: false,
        radiologyResults: true            // إدخال نتائج الأشعة
      },
      system: {
        viewPatients: true,               // عرض المرضى المحولين فقط
        createPatients: false,
        editPatients: false,
        deletePatients: false,
        viewAppointments: false,
        createAppointments: false,
        editAppointments: false,
        deleteAppointments: false,
        viewAnalytics: false,
        manageUsers: false,
        systemSettings: false
      },
      analytics: {
        functionalIndependenceComparison: false,
        treatmentPlanEffectiveness: false,
        clinicalProgressTracking: false,
        outcomeMetrics: false,
        complianceReporting: false
      }
    },

    // صلاحيات الاستقبال - Receptionist Permissions
    receptionist: {
      forms: {
        medicalDiagnosis: false,
        ptEvaluation: false,
        reassessment: false,
        dischargeEvaluation: false,
        followUpForm: false,
        patientEducationDoctor: false,
        progressNotes: false,
        patientEducationTherapist: false,
        patientEducationNurse: false,
        nursingForm: false,
        clinicalIndicators: false,
        specialNeedsAssessment: false,
        planOfCare: false,
        dailyProgress: false,
        labResults: false,
        radiologyResults: false
      },
      system: {
        viewPatients: true,
        createPatients: true,             // تسجيل المرضى الجدد
        editPatients: true,               // تعديل بيانات المرضى
        deletePatients: false,
        viewAppointments: true,
        createAppointments: true,         // حجز المواعيد
        editAppointments: true,           // تعديل المواعيد
        deleteAppointments: true,         // إلغاء المواعيد
        viewAnalytics: false,
        manageUsers: false,
        systemSettings: false
      },
      analytics: {
        functionalIndependenceComparison: false,
        treatmentPlanEffectiveness: false,
        clinicalProgressTracking: false,
        outcomeMetrics: false,
        complianceReporting: false
      }
    },

    // صلاحيات المدير - Manager Permissions
    manager: {
      forms: {
        medicalDiagnosis: false,
        ptEvaluation: false,
        reassessment: false,
        dischargeEvaluation: false,
        followUpForm: false,
        patientEducationDoctor: false,
        progressNotes: false,
        patientEducationTherapist: false,
        patientEducationNurse: false,
        nursingForm: false,
        clinicalIndicators: false,
        specialNeedsAssessment: false,
        planOfCare: false,
        dailyProgress: false,
        labResults: false,
        radiologyResults: false
      },
      system: {
        viewPatients: true,
        createPatients: false,
        editPatients: false,
        deletePatients: false,
        viewAppointments: true,
        createAppointments: false,
        editAppointments: false,
        deleteAppointments: false,
        viewAnalytics: true,              // عرض التحليلات والتقارير
        manageUsers: true,                // إدارة المستخدمين
        systemSettings: true              // إعدادات النظام
      },
      analytics: {
        functionalIndependenceComparison: true,
        treatmentPlanEffectiveness: true,
        clinicalProgressTracking: true,
        outcomeMetrics: true,
        complianceReporting: true
      }
    }
  };

  // Set permissions based on role
  if (rolePermissions[this.role]) {
    this.permissions = rolePermissions[this.role];
  }

  return this;
};

module.exports = mongoose.model('User', userSchema);
