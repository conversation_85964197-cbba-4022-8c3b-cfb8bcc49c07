{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/parameterObject.json#", "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "allOf": [{"$ref": "dataTypeBase.json#"}, {"required": ["paramType", "name"], "properties": {"paramType": {"enum": ["path", "query", "body", "header", "form"]}, "name": {"type": "string"}, "description": {"type": "string"}, "required": {"type": "boolean"}, "allowMultiple": {"type": "boolean"}}}, {"description": "type File requires special paramType and consumes", "oneOf": [{"properties": {"type": {"not": {"enum": ["File"]}}}}, {"properties": {"type": {"enum": ["File"]}, "paramType": {"enum": ["form"]}, "consumes": {"enum": ["multipart/form-data"]}}}]}]}