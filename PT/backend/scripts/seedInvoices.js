const mongoose = require('mongoose');
const Billing = require('../models/Billing');
const Patient = require('../models/Patient');
const User = require('../models/User');
const Payment = require('../models/Payment');

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/pt_system', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const seedInvoices = async () => {
  try {
    console.log('Starting invoice seeding...');

    // Create a test user first if none exists
    let user = await User.findOne();
    if (!user) {
      user = await User.create({
        firstName: 'Dr. Sarah',
        lastName: 'Ahmed',
        email: '<EMAIL>',
        password: 'password123',
        role: 'therapist',
        isActive: true
      });
      console.log('Created test user:', user._id);
    }

    // First, let's create a test patient if none exists
    let patient = await Patient.findOne();
    if (!patient) {
      patient = await Patient.create({
        firstName: 'أحمد',
        lastName: 'محمد علي',
        nationalId: '**********',
        phone: '+966501234567',
        email: '<EMAIL>',
        dateOfBirth: new Date('1985-05-15'),
        gender: 'male',
        address: {
          street: 'شارع الملك فهد',
          city: 'الرياض',
          state: 'الرياض',
          zipCode: '12345',
          country: 'Saudi Arabia'
        },
        emergencyContact: {
          name: 'فاطمة علي',
          phone: '+966502345678',
          relationship: 'زوجة'
        },
        createdBy: user._id
      });
      console.log('Created test patient:', patient._id);
    }

    // Create test invoices
    const invoices = [
      {
        patient: patient._id,
        invoiceNumber: 'INV-2024-001',
        services: [
          {
            name: 'Physical Therapy Session',
            description: 'Initial assessment and treatment',
            quantity: 2,
            unitPrice: 500,
            totalPrice: 1000,
            date: new Date('2024-01-15')
          },
          {
            name: 'Exercise Program',
            description: 'Customized exercise plan',
            quantity: 1,
            unitPrice: 300,
            totalPrice: 300,
            date: new Date('2024-01-15')
          }
        ],
        subtotal: 1300,
        tax: { rate: 15, amount: 195 },
        discount: { type: 'fixed', amount: 50 },
        totalAmount: 1445,
        status: 'sent',
        issueDate: new Date('2024-01-15'),
        dueDate: new Date('2024-02-14'),
        notes: 'Initial treatment package for lower back pain',
        createdBy: user._id
      },
      {
        patient: patient._id,
        invoiceNumber: 'INV-2024-002',
        services: [
          {
            name: 'Follow-up Session',
            description: 'Progress evaluation and treatment',
            quantity: 1,
            unitPrice: 400,
            totalPrice: 400,
            date: new Date('2024-01-20')
          }
        ],
        subtotal: 400,
        tax: { rate: 15, amount: 60 },
        discount: { type: 'fixed', amount: 0 },
        totalAmount: 460,
        status: 'draft',
        issueDate: new Date('2024-01-20'),
        dueDate: new Date('2024-02-19'),
        notes: 'Follow-up treatment session',
        createdBy: user._id
      },
      {
        patient: patient._id,
        invoiceNumber: 'INV-2024-003',
        services: [
          {
            name: 'Treatment Package',
            description: 'Complete rehabilitation package',
            quantity: 1,
            unitPrice: 2000,
            totalPrice: 2000,
            date: new Date('2024-01-10')
          }
        ],
        subtotal: 2000,
        tax: { rate: 15, amount: 300 },
        discount: { type: 'percentage', value: 10, amount: 200 },
        totalAmount: 2100,
        status: 'paid',
        issueDate: new Date('2024-01-10'),
        dueDate: new Date('2024-02-09'),
        notes: 'Comprehensive treatment package',
        createdBy: user._id
      }
    ];

    // Clear existing invoices
    await Billing.deleteMany({});
    console.log('Cleared existing invoices');

    // Create new invoices
    const createdInvoices = await Billing.create(invoices);
    console.log(`Created ${createdInvoices.length} test invoices`);

    // Create some payments for the paid invoice
    const paidInvoice = createdInvoices.find(inv => inv.status === 'paid');
    if (paidInvoice) {
      await Payment.create({
        patient: patient._id,
        billing: paidInvoice._id,
        amount: 1000,
        method: 'cash',
        reference: 'CASH001',
        notes: 'Partial payment',
        status: 'completed',
        receivedBy: user._id,
        createdBy: user._id
      });

      await Payment.create({
        patient: patient._id,
        billing: paidInvoice._id,
        amount: 1100,
        method: 'card',
        reference: 'CARD002',
        notes: 'Final payment',
        status: 'completed',
        receivedBy: user._id,
        createdBy: user._id
      });

      console.log('Created test payments');
    }

    // Create a partial payment for the sent invoice
    const sentInvoice = createdInvoices.find(inv => inv.status === 'sent');
    if (sentInvoice) {
      await Payment.create({
        patient: patient._id,
        billing: sentInvoice._id,
        amount: 500,
        method: 'cash',
        reference: 'CASH003',
        notes: 'Partial payment',
        status: 'completed',
        receivedBy: user._id,
        createdBy: user._id
      });

      console.log('Created partial payment for sent invoice');
    }

    console.log('Invoice seeding completed successfully!');
    console.log('Test invoice IDs:');
    createdInvoices.forEach(invoice => {
      console.log(`- ${invoice.invoiceNumber}: ${invoice._id}`);
    });

  } catch (error) {
    console.error('Error seeding invoices:', error);
  } finally {
    mongoose.connection.close();
  }
};

// Run the seeding
seedInvoices();
