const asyncHandler = require('express-async-handler');
const ElectronicSignature = require('../models/ElectronicSignature');
const Patient = require('../models/Patient');
const Billing = require('../models/Billing');
const crypto = require('crypto');

// @desc    Create electronic signature document
// @route   POST /api/v1/signature/create
// @access  Private
const createSignatureDocument = asyncHandler(async (req, res) => {
  const { 
    documentType, 
    documentTitle, 
    documentContent, 
    patientId, 
    signers,
    relatedRecords,
    expiryDays = 7
  } = req.body;

  try {
    const patient = await Patient.findById(patientId);
    if (!patient) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found'
      });
    }

    // Generate document hash
    const documentHash = crypto
      .createHash('sha256')
      .update(documentContent)
      .digest('hex');

    // Create electronic signature document
    const electronicSignature = new ElectronicSignature({
      documentId: `DOC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      documentType,
      documentTitle,
      documentContent,
      documentHash,
      patient: patientId,
      relatedRecords,
      signers: signers.map(signer => ({
        ...signer,
        status: 'pending'
      })),
      workflow: {
        status: 'pending_signatures',
        requiredSignatures: signers.length,
        completedSignatures: 0,
        expiryDate: new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000)
      },
      createdBy: req.user.id
    });

    await electronicSignature.save();

    // Add audit log entry
    electronicSignature.auditLog.push({
      action: 'document_created',
      performedBy: req.user.id,
      details: {
        documentType,
        signersCount: signers.length
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    await electronicSignature.save();

    res.status(201).json({
      success: true,
      message: 'Electronic signature document created successfully',
      data: {
        documentId: electronicSignature.documentId,
        status: electronicSignature.workflow.status,
        signers: electronicSignature.signers.map(signer => ({
          _id: signer._id,
          signerType: signer.signerType,
          name: signer.signerInfo.name,
          status: signer.status
        })),
        expiryDate: electronicSignature.workflow.expiryDate,
        completionPercentage: electronicSignature.completionPercentage
      }
    });

  } catch (error) {
    console.error('Electronic signature creation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create electronic signature document',
      error: error.message
    });
  }
});

// @desc    Sign document electronically
// @route   POST /api/v1/signature/sign/:documentId
// @access  Private
const signDocument = asyncHandler(async (req, res) => {
  const { documentId } = req.params;
  const { 
    signerId, 
    signatureImage, 
    authenticationMethod, 
    deviceInfo, 
    geolocation,
    consentGiven = true
  } = req.body;

  try {
    const electronicSignature = await ElectronicSignature.findOne({ 
      documentId 
    }).populate('patient', 'firstName lastName nationalId');

    if (!electronicSignature) {
      return res.status(404).json({
        success: false,
        message: 'Electronic signature document not found'
      });
    }

    if (electronicSignature.isExpired) {
      return res.status(400).json({
        success: false,
        message: 'Document has expired and cannot be signed'
      });
    }

    // Find the signer
    const signer = electronicSignature.signers.id(signerId);
    if (!signer) {
      return res.status(404).json({
        success: false,
        message: 'Signer not found in this document'
      });
    }

    if (signer.status === 'signed') {
      return res.status(400).json({
        success: false,
        message: 'Document already signed by this signer'
      });
    }

    // Generate digital signature
    const digitalSignature = crypto
      .createHash('sha256')
      .update(`${documentId}_${signerId}_${Date.now()}`)
      .digest('hex');

    // Create signature data
    const signatureData = {
      digitalSignature,
      signatureImage,
      timestamp: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      deviceInfo,
      geolocation
    };

    // Update signer
    signer.signatureData = signatureData;
    signer.status = 'signed';
    signer.signedAt = new Date();
    signer.authenticationMethod = authenticationMethod;
    signer.consentGiven = consentGiven;
    signer.consentTimestamp = new Date();

    // Update workflow
    electronicSignature.workflow.completedSignatures = 
      electronicSignature.signers.filter(s => s.status === 'signed').length;

    if (electronicSignature.workflow.completedSignatures >= 
        electronicSignature.workflow.requiredSignatures) {
      electronicSignature.workflow.status = 'fully_signed';
    } else {
      electronicSignature.workflow.status = 'partially_signed';
    }

    // Add audit log entry
    electronicSignature.auditLog.push({
      action: 'document_signed',
      performedBy: req.user.id,
      details: {
        signerId,
        signerName: signer.signerInfo.name,
        authenticationMethod,
        ipAddress: req.ip
      },
      ipAddress: req.ip,
      userAgent: req.get('User-Agent')
    });

    await electronicSignature.save();

    // If fully signed, trigger any related processes
    if (electronicSignature.workflow.status === 'fully_signed') {
      await handleFullySignedDocument(electronicSignature);
    }

    res.status(200).json({
      success: true,
      message: 'Document signed successfully',
      data: {
        documentId: electronicSignature.documentId,
        status: electronicSignature.workflow.status,
        completionPercentage: electronicSignature.completionPercentage,
        signedAt: signer.signedAt,
        isFullySigned: electronicSignature.workflow.status === 'fully_signed'
      }
    });

  } catch (error) {
    console.error('Electronic signature error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to sign document',
      error: error.message
    });
  }
});

// @desc    Get signature document details
// @route   GET /api/v1/signature/:documentId
// @access  Private
const getSignatureDocument = asyncHandler(async (req, res) => {
  const { documentId } = req.params;

  try {
    const electronicSignature = await ElectronicSignature.findOne({ 
      documentId 
    })
    .populate('patient', 'firstName lastName nationalId phone email')
    .populate('createdBy', 'firstName lastName');

    if (!electronicSignature) {
      return res.status(404).json({
        success: false,
        message: 'Electronic signature document not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        documentId: electronicSignature.documentId,
        documentType: electronicSignature.documentType,
        documentTitle: electronicSignature.documentTitle,
        documentContent: electronicSignature.documentContent,
        patient: electronicSignature.patient,
        workflow: electronicSignature.workflow,
        signers: electronicSignature.signers.map(signer => ({
          _id: signer._id,
          signerType: signer.signerType,
          signerInfo: signer.signerInfo,
          status: signer.status,
          signedAt: signer.signedAt,
          rejectedAt: signer.rejectedAt,
          rejectionReason: signer.rejectionReason
        })),
        verification: electronicSignature.verification,
        completionPercentage: electronicSignature.completionPercentage,
        isExpired: electronicSignature.isExpired,
        createdBy: electronicSignature.createdBy,
        createdAt: electronicSignature.createdAt
      }
    });

  } catch (error) {
    console.error('Get signature document error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get signature document',
      error: error.message
    });
  }
});

// @desc    Get patient signature documents
// @route   GET /api/v1/signature/patient/:patientId
// @access  Private
const getPatientSignatureDocuments = asyncHandler(async (req, res) => {
  const { patientId } = req.params;
  const { status, documentType, page = 1, limit = 10 } = req.query;

  try {
    const query = { patient: patientId };
    
    if (status) {
      query['workflow.status'] = status;
    }
    
    if (documentType) {
      query.documentType = documentType;
    }

    const skip = (page - 1) * limit;

    const documents = await ElectronicSignature.find(query)
      .populate('createdBy', 'firstName lastName')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await ElectronicSignature.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        documents: documents.map(doc => ({
          documentId: doc.documentId,
          documentType: doc.documentType,
          documentTitle: doc.documentTitle,
          workflow: doc.workflow,
          completionPercentage: doc.completionPercentage,
          isExpired: doc.isExpired,
          createdBy: doc.createdBy,
          createdAt: doc.createdAt
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalDocuments: total,
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get patient signature documents error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get patient signature documents',
      error: error.message
    });
  }
});

// @desc    Validate signature document
// @route   POST /api/v1/signature/validate/:documentId
// @access  Private
const validateSignatureDocument = asyncHandler(async (req, res) => {
  const { documentId } = req.params;

  try {
    const electronicSignature = await ElectronicSignature.findOne({ 
      documentId 
    });

    if (!electronicSignature) {
      return res.status(404).json({
        success: false,
        message: 'Electronic signature document not found'
      });
    }

    // Perform validation
    await electronicSignature.validateSignature();

    res.status(200).json({
      success: true,
      message: 'Signature validation completed',
      data: {
        documentId: electronicSignature.documentId,
        isValid: electronicSignature.verification.isValid,
        validationChecks: electronicSignature.verification.validationChecks,
        lastValidated: electronicSignature.verification.lastValidated
      }
    });

  } catch (error) {
    console.error('Signature validation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to validate signature document',
      error: error.message
    });
  }
});

// Helper function to handle fully signed documents
const handleFullySignedDocument = async (electronicSignature) => {
  try {
    // Update related records if applicable
    if (electronicSignature.relatedRecords.billing) {
      const billing = await Billing.findById(electronicSignature.relatedRecords.billing);
      if (billing) {
        billing.signatureStatus = 'signed';
        billing.signedDate = new Date();
        await billing.save();
      }
    }

    // Submit to external systems if required
    if (electronicSignature.documentType === 'consent_form') {
      // Submit to NPHIES if required
      electronicSignature.externalIntegrations.nphies = {
        submitted: true,
        submissionId: `NPHIES_${Date.now()}`,
        status: 'submitted'
      };
    }

    await electronicSignature.save();

  } catch (error) {
    console.error('Error handling fully signed document:', error);
  }
};

module.exports = {
  createSignatureDocument,
  signDocument,
  getSignatureDocument,
  getPatientSignatureDocuments,
  validateSignatureDocument
};
