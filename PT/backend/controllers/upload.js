const fs = require('fs').promises;
const path = require('path');
const sharp = require('sharp');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const Patient = require('../models/Patient');
const User = require('../models/User');
const TreatmentPlan = require('../models/TreatmentPlan');
const logger = require('../utils/logger');

// @desc    Upload single file
// @route   POST /api/v1/upload/single
// @access  Private
const uploadFile = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('Please upload a file', 400));
  }

  const { type = 'document', description } = req.body;

  const fileData = {
    filename: req.file.filename,
    originalName: req.file.originalname,
    mimetype: req.file.mimetype,
    size: req.file.size,
    url: `/uploads/${req.file.filename}`,
    uploadDate: new Date(),
    uploadedBy: req.user.id,
    type: type,
    description: description
  };

  logger.info(`File uploaded: ${req.file.originalname} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: fileData
  });
});

// @desc    Upload multiple files
// @route   POST /api/v1/upload/multiple
// @access  Private
const uploadMultipleFiles = asyncHandler(async (req, res, next) => {
  if (!req.files || req.files.length === 0) {
    return next(new AppError('Please upload at least one file', 400));
  }

  const { type = 'document' } = req.body;

  const filesData = req.files.map(file => ({
    filename: file.filename,
    originalName: file.originalname,
    mimetype: file.mimetype,
    size: file.size,
    url: `/uploads/${file.filename}`,
    uploadDate: new Date(),
    uploadedBy: req.user.id,
    type: type
  }));

  logger.info(`${req.files.length} files uploaded by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    count: filesData.length,
    data: filesData
  });
});

// @desc    Upload patient document
// @route   POST /api/v1/upload/patient-document
// @access  Private
const uploadPatientDocument = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('Please upload a file', 400));
  }

  const { patientId, documentType = 'other', description } = req.body;

  if (!patientId) {
    return next(new AppError('Patient ID is required', 400));
  }

  // Find patient
  const patient = await Patient.findById(patientId);
  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  const documentData = {
    name: description || req.file.originalname,
    type: documentType,
    url: `/uploads/${req.file.filename}`,
    uploadDate: new Date(),
    uploadedBy: req.user.id,
    filename: req.file.filename,
    originalName: req.file.originalname,
    mimetype: req.file.mimetype,
    size: req.file.size
  };

  // Add document to patient
  patient.documents.push(documentData);
  await patient.save();

  logger.info(`Patient document uploaded: ${req.file.originalname} for patient ${patientId} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: documentData
  });
});

// @desc    Upload profile picture
// @route   POST /api/v1/upload/profile-picture
// @access  Private
const uploadProfilePicture = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('Please upload an image file', 400));
  }

  const { userId } = req.body;
  const targetUserId = userId || req.user.id;

  // Check if user can upload for this user ID
  if (targetUserId !== req.user.id && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to upload profile picture for this user', 403));
  }

  // Find user
  const user = await User.findById(targetUserId);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  try {
    // Process image with Sharp
    const filename = `profile-${targetUserId}-${Date.now()}.jpg`;
    const filepath = path.join(__dirname, '../public/uploads', filename);

    await sharp(req.file.buffer)
      .resize(300, 300, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 90 })
      .toFile(filepath);

    // Delete old profile picture if exists
    if (user.avatar && user.avatar !== '/uploads/default-avatar.jpg') {
      const oldFilePath = path.join(__dirname, '../public', user.avatar);
      try {
        await fs.unlink(oldFilePath);
      } catch (error) {
        // Ignore error if file doesn't exist
        logger.warn(`Could not delete old profile picture: ${oldFilePath}`);
      }
    }

    // Update user avatar
    user.avatar = `/uploads/${filename}`;
    await user.save();

    logger.info(`Profile picture updated for user ${targetUserId} by user ${req.user.id}`);

    res.status(201).json({
      success: true,
      data: {
        filename: filename,
        url: `/uploads/${filename}`,
        size: req.file.size,
        uploadDate: new Date()
      }
    });

  } catch (error) {
    logger.error('Error processing profile picture:', error);
    return next(new AppError('Error processing image', 500));
  }
});

// @desc    Upload treatment image
// @route   POST /api/v1/upload/treatment-image
// @access  Private
const uploadTreatmentImage = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('Please upload an image file', 400));
  }

  const { treatmentPlanId, description } = req.body;

  if (!treatmentPlanId) {
    return next(new AppError('Treatment plan ID is required', 400));
  }

  // Find treatment plan
  const treatmentPlan = await TreatmentPlan.findById(treatmentPlanId);
  if (!treatmentPlan) {
    return next(new AppError('Treatment plan not found', 404));
  }

  try {
    // Process image with Sharp
    const filename = `treatment-${treatmentPlanId}-${Date.now()}.jpg`;
    const filepath = path.join(__dirname, '../public/uploads', filename);

    await sharp(req.file.buffer)
      .resize(1200, 800, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality: 85 })
      .toFile(filepath);

    const imageData = {
      filename: filename,
      originalName: req.file.originalname,
      url: `/uploads/${filename}`,
      description: description,
      uploadDate: new Date(),
      uploadedBy: req.user.id,
      size: req.file.size
    };

    logger.info(`Treatment image uploaded for plan ${treatmentPlanId} by user ${req.user.id}`);

    res.status(201).json({
      success: true,
      data: imageData
    });

  } catch (error) {
    logger.error('Error processing treatment image:', error);
    return next(new AppError('Error processing image', 500));
  }
});

// @desc    Upload form attachment
// @route   POST /api/v1/upload/form-attachment
// @access  Private
const uploadFormAttachment = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('Please upload a file', 400));
  }

  const { formId, fieldId } = req.body;

  if (!formId || !fieldId) {
    return next(new AppError('Form ID and Field ID are required', 400));
  }

  const attachmentData = {
    formId: formId,
    fieldId: fieldId,
    filename: req.file.filename,
    originalName: req.file.originalname,
    mimetype: req.file.mimetype,
    size: req.file.size,
    url: `/uploads/${req.file.filename}`,
    uploadDate: new Date(),
    uploadedBy: req.user.id
  };

  logger.info(`Form attachment uploaded for form ${formId}, field ${fieldId} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: attachmentData
  });
});

// @desc    Compress and upload image
// @route   POST /api/v1/upload/compress-image
// @access  Private
const compressImage = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('Please upload an image file', 400));
  }

  const { 
    quality = 80, 
    maxWidth = 1920, 
    maxHeight = 1080 
  } = req.body;

  try {
    const filename = `compressed-${Date.now()}.jpg`;
    const filepath = path.join(__dirname, '../public/uploads', filename);

    const processedImage = await sharp(req.file.buffer)
      .resize(parseInt(maxWidth), parseInt(maxHeight), {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality: parseInt(quality) })
      .toFile(filepath);

    const imageData = {
      filename: filename,
      originalName: req.file.originalname,
      url: `/uploads/${filename}`,
      originalSize: req.file.size,
      compressedSize: processedImage.size,
      compressionRatio: Math.round((1 - processedImage.size / req.file.size) * 100),
      uploadDate: new Date(),
      uploadedBy: req.user.id
    };

    logger.info(`Image compressed: ${req.file.originalname} (${req.file.size} -> ${processedImage.size} bytes) by user ${req.user.id}`);

    res.status(201).json({
      success: true,
      data: imageData
    });

  } catch (error) {
    logger.error('Error compressing image:', error);
    return next(new AppError('Error processing image', 500));
  }
});

// @desc    Generate thumbnail
// @route   POST /api/v1/upload/thumbnail
// @access  Private
const generateThumbnail = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return next(new AppError('Please upload an image file', 400));
  }

  const { width = 150, height = 150 } = req.body;

  try {
    const filename = `thumb-${Date.now()}.jpg`;
    const filepath = path.join(__dirname, '../public/uploads', filename);

    const thumbnail = await sharp(req.file.buffer)
      .resize(parseInt(width), parseInt(height), {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 90 })
      .toFile(filepath);

    const thumbnailData = {
      filename: filename,
      originalName: req.file.originalname,
      url: `/uploads/${filename}`,
      width: parseInt(width),
      height: parseInt(height),
      size: thumbnail.size,
      uploadDate: new Date(),
      uploadedBy: req.user.id
    };

    logger.info(`Thumbnail generated: ${req.file.originalname} (${width}x${height}) by user ${req.user.id}`);

    res.status(201).json({
      success: true,
      data: thumbnailData
    });

  } catch (error) {
    logger.error('Error generating thumbnail:', error);
    return next(new AppError('Error processing image', 500));
  }
});

// @desc    Get file
// @route   GET /api/v1/upload/file/:filename
// @access  Private
const getFile = asyncHandler(async (req, res, next) => {
  const { filename } = req.params;
  const filepath = path.join(__dirname, '../public/uploads', filename);

  try {
    await fs.access(filepath);
    res.sendFile(filepath);
  } catch (error) {
    return next(new AppError('File not found', 404));
  }
});

// @desc    Delete file
// @route   DELETE /api/v1/upload/file/:filename
// @access  Private
const deleteFile = asyncHandler(async (req, res, next) => {
  const { filename } = req.params;
  const filepath = path.join(__dirname, '../public/uploads', filename);

  try {
    await fs.unlink(filepath);
    
    logger.info(`File deleted: ${filename} by user ${req.user.id}`);

    res.status(200).json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    if (error.code === 'ENOENT') {
      return next(new AppError('File not found', 404));
    }
    logger.error('Error deleting file:', error);
    return next(new AppError('Error deleting file', 500));
  }
});

// @desc    Get files by type
// @route   GET /api/v1/upload/files/:type
// @access  Private
const getFilesByType = asyncHandler(async (req, res, next) => {
  const { type } = req.params;
  const uploadsDir = path.join(__dirname, '../public/uploads');

  try {
    const files = await fs.readdir(uploadsDir);
    
    // Filter files by type (this is a simple implementation)
    // In a real application, you'd store file metadata in a database
    const filteredFiles = files.filter(file => {
      if (type === 'image') {
        return /\.(jpg|jpeg|png|gif|webp)$/i.test(file);
      } else if (type === 'document') {
        return /\.(pdf|doc|docx|txt)$/i.test(file);
      } else if (type === 'profile_picture') {
        return file.startsWith('profile-');
      }
      return true;
    });

    const fileList = await Promise.all(
      filteredFiles.map(async (file) => {
        const filepath = path.join(uploadsDir, file);
        const stats = await fs.stat(filepath);
        
        return {
          filename: file,
          url: `/uploads/${file}`,
          size: stats.size,
          uploadDate: stats.birthtime,
          type: type
        };
      })
    );

    res.status(200).json({
      success: true,
      count: fileList.length,
      data: fileList
    });

  } catch (error) {
    logger.error('Error reading uploads directory:', error);
    return next(new AppError('Error retrieving files', 500));
  }
});

module.exports = {
  uploadFile,
  uploadMultipleFiles,
  uploadPatientDocument,
  uploadProfilePicture,
  uploadTreatmentImage,
  uploadFormAttachment,
  compressImage,
  generateThumbnail,
  getFile,
  deleteFile,
  getFilesByType
};
