const { asyncHandler, AppError } = require('../middleware/errorHandler');
const ClinicalIndicator = require('../models/ClinicalIndicator');
const Patient = require('../models/Patient');
const logger = require('../utils/logger');

// @desc    Create new clinical indicator assessment
// @route   POST /api/v1/clinical-indicators
// @access  Private
const createClinicalIndicator = asyncHandler(async (req, res, next) => {
  const {
    patientId,
    assessmentId,
    assessmentType,
    bergBalanceScale,
    timedUpAndGo,
    manualMuscleTesting,
    painScale,
    functionalIndependenceMeasure,
    rangeOfMotion,
    clinicalNotes,
    recommendations
  } = req.body;

  // Verify patient exists
  const patient = await Patient.findById(patientId);
  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  const clinicalIndicator = await ClinicalIndicator.create({
    patientId,
    assessmentId,
    assessmentType,
    bergBalanceScale,
    timedUpAndGo,
    manualMuscleTesting,
    painScale,
    functionalIndependenceMeasure,
    rangeOfMotion,
    clinicalNotes,
    recommendations,
    assessedBy: req.user.id,
    status: 'completed'
  });

  // Calculate progress score
  const progressScore = clinicalIndicator.calculateProgressScore();

  logger.info(`Clinical indicator created for patient ${patientId} by user ${req.user.id}`);

  res.status(201).json({
    success: true,
    data: {
      ...clinicalIndicator.toObject(),
      progressScore
    }
  });
});

// @desc    Get clinical indicators for a patient
// @route   GET /api/v1/clinical-indicators/patient/:patientId
// @access  Private
const getPatientClinicalIndicators = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;
  const { 
    assessmentType, 
    dateFrom, 
    dateTo, 
    limit = 10, 
    page = 1,
    sortBy = 'assessmentDate',
    sortOrder = 'desc'
  } = req.query;

  // Build query
  const query = { patientId };
  
  if (assessmentType) {
    query.assessmentType = assessmentType;
  }
  
  if (dateFrom || dateTo) {
    query.assessmentDate = {};
    if (dateFrom) query.assessmentDate.$gte = new Date(dateFrom);
    if (dateTo) query.assessmentDate.$lte = new Date(dateTo);
  }

  // Execute query with pagination
  const skip = (page - 1) * limit;
  const sortOptions = {};
  sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

  const [indicators, total] = await Promise.all([
    ClinicalIndicator.find(query)
      .populate('assessedBy', 'name email')
      .populate('reviewedBy', 'name email')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit)),
    ClinicalIndicator.countDocuments(query)
  ]);

  // Calculate progress scores for each indicator
  const indicatorsWithProgress = indicators.map(indicator => ({
    ...indicator.toObject(),
    progressScore: indicator.calculateProgressScore()
  }));

  res.status(200).json({
    success: true,
    count: indicators.length,
    total,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    },
    data: indicatorsWithProgress
  });
});

// @desc    Get clinical indicator by ID
// @route   GET /api/v1/clinical-indicators/:id
// @access  Private
const getClinicalIndicator = asyncHandler(async (req, res, next) => {
  const indicator = await ClinicalIndicator.findById(req.params.id)
    .populate('patientId', 'firstName lastName dateOfBirth medicalRecordNumber')
    .populate('assessedBy', 'name email')
    .populate('reviewedBy', 'name email');

  if (!indicator) {
    return next(new AppError('Clinical indicator not found', 404));
  }

  const progressScore = indicator.calculateProgressScore();

  res.status(200).json({
    success: true,
    data: {
      ...indicator.toObject(),
      progressScore
    }
  });
});

// @desc    Update clinical indicator
// @route   PUT /api/v1/clinical-indicators/:id
// @access  Private
const updateClinicalIndicator = asyncHandler(async (req, res, next) => {
  let indicator = await ClinicalIndicator.findById(req.params.id);

  if (!indicator) {
    return next(new AppError('Clinical indicator not found', 404));
  }

  // Check if user can update this indicator
  if (indicator.assessedBy.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to update this clinical indicator', 403));
  }

  indicator = await ClinicalIndicator.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  ).populate('assessedBy', 'name email')
   .populate('reviewedBy', 'name email');

  const progressScore = indicator.calculateProgressScore();

  logger.info(`Clinical indicator ${req.params.id} updated by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: {
      ...indicator.toObject(),
      progressScore
    }
  });
});

// @desc    Delete clinical indicator
// @route   DELETE /api/v1/clinical-indicators/:id
// @access  Private
const deleteClinicalIndicator = asyncHandler(async (req, res, next) => {
  const indicator = await ClinicalIndicator.findById(req.params.id);

  if (!indicator) {
    return next(new AppError('Clinical indicator not found', 404));
  }

  // Check if user can delete this indicator
  if (indicator.assessedBy.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(new AppError('Not authorized to delete this clinical indicator', 403));
  }

  await indicator.deleteOne();

  logger.info(`Clinical indicator ${req.params.id} deleted by user ${req.user.id}`);

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Get clinical progress analytics for a patient
// @route   GET /api/v1/clinical-indicators/patient/:patientId/progress
// @access  Private
const getPatientProgressAnalytics = asyncHandler(async (req, res, next) => {
  const { patientId } = req.params;
  const { period = '6months' } = req.query;

  // Verify patient exists
  const patient = await Patient.findById(patientId);
  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  // Calculate date range
  const endDate = new Date();
  const startDate = new Date();

  switch (period) {
    case '1month':
      startDate.setMonth(endDate.getMonth() - 1);
      break;
    case '3months':
      startDate.setMonth(endDate.getMonth() - 3);
      break;
    case '6months':
      startDate.setMonth(endDate.getMonth() - 6);
      break;
    case '1year':
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
  }

  const indicators = await ClinicalIndicator.find({
    patientId,
    assessmentDate: { $gte: startDate, $lte: endDate }
  }).sort({ assessmentDate: 1 })
    .populate('assessedBy', 'name email')
    .populate('reviewedBy', 'name email');

  if (indicators.length === 0) {
    return res.status(200).json({
      success: true,
      data: {
        patientInfo: {
          id: patient._id,
          name: `${patient.firstName} ${patient.lastName}`,
          medicalRecordNumber: patient.medicalRecordNumber,
          dateOfBirth: patient.dateOfBirth,
          primaryCondition: patient.primaryCondition || 'Not specified'
        },
        progressTrend: [],
        currentStatus: null,
        improvementAreas: [],
        concernAreas: [],
        assessmentCount: 0,
        functionalIndependenceComparison: null,
        treatmentPlanEffectiveness: []
      }
    });
  }

  // Calculate progress trends
  const progressTrend = indicators.map(indicator => ({
    date: indicator.assessmentDate,
    bergBalance: indicator.bergBalanceScale?.totalScore || null,
    tugTime: indicator.timedUpAndGo?.timeInSeconds || null,
    painLevel: indicator.painScale?.currentPain || null,
    fimScore: indicator.functionalIndependenceMeasure?.totalScore || null,
    overallProgress: indicator.calculateProgressScore()
  }));

  // Get latest assessment
  const latestIndicator = indicators[indicators.length - 1];
  const currentStatus = {
    bergBalance: {
      score: latestIndicator.bergBalanceScale?.totalScore || null,
      riskLevel: latestIndicator.bergBalanceScale?.riskLevel || null
    },
    tugTest: {
      time: latestIndicator.timedUpAndGo?.timeInSeconds || null,
      riskLevel: latestIndicator.timedUpAndGo?.riskLevel || null
    },
    painLevel: {
      current: latestIndicator.painScale?.currentPain || null,
      average: latestIndicator.painScale?.averagePain24h || null
    },
    functionalIndependence: {
      total: latestIndicator.functionalIndependenceMeasure?.totalScore || null,
      motor: latestIndicator.functionalIndependenceMeasure?.motorScore || null,
      cognitive: latestIndicator.functionalIndependenceMeasure?.cognitiveScore || null
    },
    overallProgress: latestIndicator.calculateProgressScore()
  };

  // Identify improvement and concern areas
  const improvementAreas = [];
  const concernAreas = [];

  if (indicators.length >= 2) {
    const firstIndicator = indicators[0];
    const lastIndicator = indicators[indicators.length - 1];

    // Berg Balance Scale
    if (firstIndicator.bergBalanceScale?.totalScore && lastIndicator.bergBalanceScale?.totalScore) {
      const improvement = lastIndicator.bergBalanceScale.totalScore - firstIndicator.bergBalanceScale.totalScore;
      if (improvement > 5) {
        improvementAreas.push('Balance and Stability');
      } else if (improvement < -3) {
        concernAreas.push('Balance and Stability');
      }
    }

    // TUG Test (lower is better)
    if (firstIndicator.timedUpAndGo?.timeInSeconds && lastIndicator.timedUpAndGo?.timeInSeconds) {
      const improvement = firstIndicator.timedUpAndGo.timeInSeconds - lastIndicator.timedUpAndGo.timeInSeconds;
      if (improvement > 2) {
        improvementAreas.push('Mobility and Gait');
      } else if (improvement < -2) {
        concernAreas.push('Mobility and Gait');
      }
    }

    // Pain Scale (lower is better)
    if (firstIndicator.painScale?.currentPain && lastIndicator.painScale?.currentPain) {
      const improvement = firstIndicator.painScale.currentPain - lastIndicator.painScale.currentPain;
      if (improvement > 2) {
        improvementAreas.push('Pain Management');
      } else if (improvement < -2) {
        concernAreas.push('Pain Management');
      }
    }

    // FIM Score
    if (firstIndicator.functionalIndependenceMeasure?.totalScore && lastIndicator.functionalIndependenceMeasure?.totalScore) {
      const improvement = lastIndicator.functionalIndependenceMeasure.totalScore - firstIndicator.functionalIndependenceMeasure.totalScore;
      if (improvement > 10) {
        improvementAreas.push('Functional Independence');
      } else if (improvement < -5) {
        concernAreas.push('Functional Independence');
      }
    }
  }

  // Calculate functional independence comparison (before/after treatment)
  const functionalIndependenceComparison = indicators.length >= 2 ? {
    beforeTreatment: {
      totalScore: indicators[0].functionalIndependenceMeasure?.totalScore || 0,
      motorScore: indicators[0].functionalIndependenceMeasure?.motorScore || 0,
      cognitiveScore: indicators[0].functionalIndependenceMeasure?.cognitiveScore || 0,
      date: indicators[0].assessmentDate,
      level: getFIMLevel(indicators[0].functionalIndependenceMeasure?.totalScore || 0)
    },
    afterTreatment: {
      totalScore: latestIndicator.functionalIndependenceMeasure?.totalScore || 0,
      motorScore: latestIndicator.functionalIndependenceMeasure?.motorScore || 0,
      cognitiveScore: latestIndicator.functionalIndependenceMeasure?.cognitiveScore || 0,
      date: latestIndicator.assessmentDate,
      level: getFIMLevel(latestIndicator.functionalIndependenceMeasure?.totalScore || 0)
    },
    improvement: {
      totalImprovement: (latestIndicator.functionalIndependenceMeasure?.totalScore || 0) - (indicators[0].functionalIndependenceMeasure?.totalScore || 0),
      motorImprovement: (latestIndicator.functionalIndependenceMeasure?.motorScore || 0) - (indicators[0].functionalIndependenceMeasure?.motorScore || 0),
      cognitiveImprovement: (latestIndicator.functionalIndependenceMeasure?.cognitiveScore || 0) - (indicators[0].functionalIndependenceMeasure?.cognitiveScore || 0),
      improvementPercentage: indicators[0].functionalIndependenceMeasure?.totalScore ?
        (((latestIndicator.functionalIndependenceMeasure?.totalScore || 0) - (indicators[0].functionalIndependenceMeasure?.totalScore || 0)) / (indicators[0].functionalIndependenceMeasure?.totalScore || 1)) * 100 : 0,
      treatmentDuration: Math.ceil((latestIndicator.assessmentDate - indicators[0].assessmentDate) / (1000 * 60 * 60 * 24))
    }
  } : null;

  // Get patient's treatment plan effectiveness
  const treatmentPlanEffectiveness = patient.treatmentPlan ? [{
    planType: patient.treatmentPlan,
    patientSpecific: true,
    averageImprovement: latestIndicator.calculateProgressScore(),
    bergBalanceImprovement: indicators.length >= 2 && indicators[0].bergBalanceScale?.totalScore && latestIndicator.bergBalanceScale?.totalScore ?
      ((latestIndicator.bergBalanceScale.totalScore - indicators[0].bergBalanceScale.totalScore) / indicators[0].bergBalanceScale.totalScore) * 100 : 0,
    tugImprovement: indicators.length >= 2 && indicators[0].timedUpAndGo?.timeInSeconds && latestIndicator.timedUpAndGo?.timeInSeconds ?
      ((indicators[0].timedUpAndGo.timeInSeconds - latestIndicator.timedUpAndGo.timeInSeconds) / indicators[0].timedUpAndGo.timeInSeconds) * 100 : 0,
    painReduction: indicators.length >= 2 && indicators[0].painScale?.currentPain && latestIndicator.painScale?.currentPain ?
      ((indicators[0].painScale.currentPain - latestIndicator.painScale.currentPain) / indicators[0].painScale.currentPain) * 100 : 0,
    fimImprovement: functionalIndependenceComparison ? functionalIndependenceComparison.improvement.improvementPercentage : 0,
    successRate: latestIndicator.calculateProgressScore() > 70 ? 100 : 0
  }] : [];

  res.status(200).json({
    success: true,
    data: {
      patientInfo: {
        id: patient._id,
        name: `${patient.firstName} ${patient.lastName}`,
        medicalRecordNumber: patient.medicalRecordNumber,
        dateOfBirth: patient.dateOfBirth,
        primaryCondition: patient.primaryCondition || 'Not specified',
        treatmentPlan: patient.treatmentPlan || 'Not specified',
        assignedTherapist: patient.assignedTherapist || 'Not assigned'
      },
      progressTrend,
      currentStatus,
      improvementAreas,
      concernAreas,
      assessmentCount: indicators.length,
      period,
      functionalIndependenceComparison,
      treatmentPlanEffectiveness
    }
  });
});

// Helper function to determine FIM level
const getFIMLevel = (score) => {
  if (score >= 108) return 'complete-independence';
  if (score >= 90) return 'modified-independence';
  if (score >= 72) return 'minimal-assistance';
  if (score >= 54) return 'moderate-assistance';
  if (score >= 36) return 'maximal-assistance';
  return 'total-assistance';
};

// @desc    Get clinical indicators summary statistics
// @route   GET /api/v1/clinical-indicators/analytics/summary
// @access  Private
const getClinicalIndicatorsSummary = asyncHandler(async (req, res, next) => {
  const { dateFrom, dateTo, assessmentType } = req.query;

  // Build match query
  const matchQuery = {};
  
  if (dateFrom || dateTo) {
    matchQuery.assessmentDate = {};
    if (dateFrom) matchQuery.assessmentDate.$gte = new Date(dateFrom);
    if (dateTo) matchQuery.assessmentDate.$lte = new Date(dateTo);
  }
  
  if (assessmentType) {
    matchQuery.assessmentType = assessmentType;
  }

  const summary = await ClinicalIndicator.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalAssessments: { $sum: 1 },
        avgBergBalance: { $avg: '$bergBalanceScale.totalScore' },
        avgTugTime: { $avg: '$timedUpAndGo.timeInSeconds' },
        avgPainLevel: { $avg: '$painScale.currentPain' },
        avgFimScore: { $avg: '$functionalIndependenceMeasure.totalScore' },
        highRiskBalance: {
          $sum: {
            $cond: [
              { $eq: ['$bergBalanceScale.riskLevel', 'high-risk'] },
              1,
              0
            ]
          }
        },
        severeImpairmentMobility: {
          $sum: {
            $cond: [
              { $eq: ['$timedUpAndGo.riskLevel', 'severe-impairment'] },
              1,
              0
            ]
          }
        },
        highPainLevels: {
          $sum: {
            $cond: [
              { $gte: ['$painScale.currentPain', 7] },
              1,
              0
            ]
          }
        }
      }
    }
  ]);

  const result = summary[0] || {
    totalAssessments: 0,
    avgBergBalance: 0,
    avgTugTime: 0,
    avgPainLevel: 0,
    avgFimScore: 0,
    highRiskBalance: 0,
    severeImpairmentMobility: 0,
    highPainLevels: 0
  };

  res.status(200).json({
    success: true,
    data: result
  });
});

module.exports = {
  createClinicalIndicator,
  getPatientClinicalIndicators,
  getClinicalIndicator,
  updateClinicalIndicator,
  deleteClinicalIndicator,
  getPatientProgressAnalytics,
  getClinicalIndicatorsSummary
};
