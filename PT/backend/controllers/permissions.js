const { asyncHandler, AppError } = require('../middleware/errorHandler');
const User = require('../models/User');
const logger = require('../utils/logger');

// Updated permission sets based on medical system requirements
// مجموعات الصلاحيات المحدثة حسب متطلبات النظام الطبي
const defaultPermissions = {
  // صلاحيات الطبيب - Doctor Permissions
  doctor: {
    forms: {
      medicalDiagnosis: true,           // 1. التشخيص الطبي
      ptEvaluation: true,               // 2. تقييم العلاج الطبيعي
      reassessment: true,               // 3. إعادة التقييم
      dischargeEvaluation: true,        // 4. تقييم الخروج
      followUpForm: true,               // 5. نموذج المتابعة
      patientEducationDoctor: true,     // 6. نموذج تثقيف المريض
      clinicalIndicators: true,         // المؤشرات السريرية (للمراجعة)
      specialNeedsAssessment: true,     // تقييم الاحتياجات الخاصة
      planOfCare: true,                 // خطة الرعاية
      progressNotes: false,             // ملاحظات التقدم (أخصائي فقط)
      patientEducationTherapist: false,
      patientEducationNurse: false,
      nursingForm: false,
      dailyProgress: false,
      labResults: true,                 // عرض نتائج المختبر
      radiologyResults: true            // عرض نتائج الأشعة
    },
    system: {
      viewPatients: true,
      createPatients: true,
      editPatients: true,
      deletePatients: false,
      viewAppointments: true,
      createAppointments: true,
      editAppointments: true,
      deleteAppointments: false,
      viewAnalytics: true,
      manageUsers: false,
      systemSettings: false
    },
    analytics: {
      functionalIndependenceComparison: true,
      treatmentPlanEffectiveness: true,
      clinicalProgressTracking: true,
      outcomeMetrics: true,
      qualityIndicators: true
    }
  },

  // صلاحيات أخصائي العلاج الطبيعي - Physical Therapist Permissions
  therapist: {
    forms: {
      medicalDiagnosis: false,          // التشخيص الطبي (طبيب فقط)
      ptEvaluation: false,              // تقييم العلاج الطبيعي (طبيب فقط)
      reassessment: false,              // إعادة التقييم (طبيب فقط)
      dischargeEvaluation: false,       // تقييم الخروج (طبيب فقط)
      followUpForm: false,              // نموذج المتابعة (طبيب فقط)
      patientEducationDoctor: false,
      progressNotes: true,              // 1. ملاحظات تقدم الحالة (Progress Notes)
      patientEducationTherapist: true,  // 2. نموذج تثقيف المريض
      clinicalIndicators: true,         // المؤشرات السريرية
      specialNeedsAssessment: true,     // تقييم الاحتياجات الخاصة
      planOfCare: false,                // خطة الرعاية (طبيب فقط)
      dailyProgress: true,              // التقدم اليومي
      patientEducationNurse: false,
      nursingForm: false,
      labResults: false,                // عرض فقط
      radiologyResults: false           // عرض فقط
    },
    system: {
      viewPatients: true,
      createPatients: false,
      editPatients: false,
      deletePatients: false,
      viewAppointments: true,
      createAppointments: false,
      editAppointments: false,
      deleteAppointments: false,
      viewAnalytics: true,
      manageUsers: false,
      systemSettings: false
    },
    analytics: {
      functionalIndependenceComparison: true,
      treatmentPlanEffectiveness: true,
      clinicalProgressTracking: true,
      outcomeMetrics: true,
      complianceReporting: false
    }
  },

  // صلاحيات التمريض - Nursing Permissions
  nurse: {
    forms: {
      medicalDiagnosis: false,          // التشخيص الطبي (طبيب فقط)
      ptEvaluation: false,              // تقييم العلاج الطبيعي (طبيب فقط)
      reassessment: false,              // إعادة التقييم (طبيب فقط)
      dischargeEvaluation: false,       // تقييم الخروج (طبيب فقط)
      followUpForm: false,              // نموذج المتابعة (طبيب فقط)
      patientEducationDoctor: false,
      progressNotes: false,             // ملاحظات التقدم (أخصائي فقط)
      patientEducationTherapist: false,
      patientEducationNurse: true,      // 1. نموذج تثقيف المريض
      nursingForm: true,                // 2. نموذج التمريض التابع للمركز
      clinicalIndicators: false,        // المؤشرات السريرية (عرض فقط)
      specialNeedsAssessment: false,
      planOfCare: false,
      dailyProgress: false,
      labResults: false,
      radiologyResults: false
    },
    system: {
      viewPatients: true,
      createPatients: false,
      editPatients: false,
      deletePatients: false,
      viewAppointments: true,
      createAppointments: false,
      editAppointments: false,
      deleteAppointments: false,
      viewAnalytics: false,
      manageUsers: false,
      systemSettings: false
    },
    analytics: {
      functionalIndependenceComparison: false,
      treatmentPlanEffectiveness: false,
      clinicalProgressTracking: false,
      outcomeMetrics: false,
      complianceReporting: false
    }
  },

  // External Lab Permissions - صلاحيات المختبرات الخارجية
  external_lab: {
    forms: {
      medicalDiagnosis: false,
      ptEvaluation: false,
      reassessment: false,
      dischargeEvaluation: false,
      followUpForm: false,
      patientEducationDoctor: false,
      progressNotes: false,
      patientEducationTherapist: false,
      patientEducationNurse: false,
      nursingForm: false,
      clinicalIndicators: false,
      bergBalanceScale: false,
      tugTest: false,
      manualMuscleTesting: false,
      painScaleVAS: false,
      functionalIndependence: false,
      labResults: true, // نتائج المختبر
      radiologyResults: false
    },
    system: {
      viewPatients: true,
      createPatients: false,
      editPatients: false,
      deletePatients: false,
      viewAppointments: false,
      createAppointments: false,
      editAppointments: false,
      deleteAppointments: false,
      viewBilling: false,
      createBilling: false,
      editBilling: false,
      viewReports: false,
      viewAnalytics: false,
      manageUsers: false,
      systemSettings: false
    },
    analytics: {
      functionalIndependenceComparison: false,
      treatmentPlanEffectiveness: false,
      clinicalProgressTracking: false,
      outcomeMetrics: false,
      qualityIndicators: false
    }
  },

  // External Radiology Permissions - صلاحيات مراكز الأشعة الخارجية
  external_radiology: {
    forms: {
      medicalDiagnosis: false,
      ptEvaluation: false,
      reassessment: false,
      dischargeEvaluation: false,
      followUpForm: false,
      patientEducationDoctor: false,
      progressNotes: false,
      patientEducationTherapist: false,
      patientEducationNurse: false,
      nursingForm: false,
      clinicalIndicators: false,
      bergBalanceScale: false,
      tugTest: false,
      manualMuscleTesting: false,
      painScaleVAS: false,
      functionalIndependence: false,
      labResults: false,
      radiologyResults: true // نتائج الأشعة
    },
    system: {
      viewPatients: true,
      createPatients: false,
      editPatients: false,
      deletePatients: false,
      viewAppointments: false,
      createAppointments: false,
      editAppointments: false,
      deleteAppointments: false,
      viewBilling: false,
      createBilling: false,
      editBilling: false,
      viewReports: false,
      viewAnalytics: false,
      manageUsers: false,
      systemSettings: false
    },
    analytics: {
      functionalIndependenceComparison: false,
      treatmentPlanEffectiveness: false,
      clinicalProgressTracking: false,
      outcomeMetrics: false,
      qualityIndicators: false
    }
  },

  // Admin has all permissions
  admin: {
    forms: {
      medicalDiagnosis: true,
      ptEvaluation: true,
      reassessment: true,
      dischargeEvaluation: true,
      followUpForm: true,
      patientEducationDoctor: true,
      progressNotes: true,
      patientEducationTherapist: true,
      patientEducationNurse: true,
      nursingForm: true,
      clinicalIndicators: true,
      bergBalanceScale: true,
      tugTest: true,
      manualMuscleTesting: true,
      painScaleVAS: true,
      functionalIndependence: true,
      labResults: true,
      radiologyResults: true
    },
    system: {
      viewPatients: true,
      createPatients: true,
      editPatients: true,
      deletePatients: true,
      viewAppointments: true,
      createAppointments: true,
      editAppointments: true,
      deleteAppointments: true,
      viewBilling: true,
      createBilling: true,
      editBilling: true,
      viewReports: true,
      viewAnalytics: true,
      manageUsers: true,
      systemSettings: true
    },
    analytics: {
      functionalIndependenceComparison: true,
      treatmentPlanEffectiveness: true,
      clinicalProgressTracking: true,
      outcomeMetrics: true,
      qualityIndicators: true
    }
  }
};

// @desc    Get user permissions
// @route   GET /api/v1/permissions/user/:userId
// @access  Private
const getUserPermissions = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.params.userId);
  
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.status(200).json({
    success: true,
    data: {
      userId: user._id,
      role: user.role,
      permissions: user.permissions || defaultPermissions[user.role] || {}
    }
  });
});

// @desc    Update user permissions
// @route   PUT /api/v1/permissions/user/:userId
// @access  Private (Admin only)
const updateUserPermissions = asyncHandler(async (req, res, next) => {
  const { permissions } = req.body;
  
  const user = await User.findByIdAndUpdate(
    req.params.userId,
    { permissions },
    { new: true, runValidators: true }
  );

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  logger.info(`User permissions updated for ${user.email} by ${req.user.email}`);

  res.status(200).json({
    success: true,
    data: {
      userId: user._id,
      role: user.role,
      permissions: user.permissions
    }
  });
});

// @desc    Get default permissions for a role
// @route   GET /api/v1/permissions/role/:role
// @access  Private
const getRolePermissions = asyncHandler(async (req, res, next) => {
  const { role } = req.params;
  
  if (!defaultPermissions[role]) {
    return next(new AppError('Invalid role', 400));
  }

  res.status(200).json({
    success: true,
    data: {
      role,
      permissions: defaultPermissions[role]
    }
  });
});

// @desc    Apply default permissions to user
// @route   POST /api/v1/permissions/user/:userId/apply-defaults
// @access  Private (Admin only)
const applyDefaultPermissions = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.params.userId);
  
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  const rolePermissions = defaultPermissions[user.role];
  
  if (!rolePermissions) {
    return next(new AppError('No default permissions found for this role', 400));
  }

  user.permissions = rolePermissions;
  await user.save();

  logger.info(`Default permissions applied to ${user.email} for role ${user.role}`);

  res.status(200).json({
    success: true,
    data: {
      userId: user._id,
      role: user.role,
      permissions: user.permissions
    }
  });
});

// @desc    Get all permission categories with descriptions
// @route   GET /api/v1/permissions/categories
// @access  Private
const getPermissionCategories = asyncHandler(async (req, res, next) => {
  const categories = {
    forms: {
      nameEn: 'Form Access Permissions',
      nameAr: 'صلاحيات الوصول للنماذج',
      permissions: {
        medicalDiagnosis: { nameEn: 'Medical Diagnosis', nameAr: 'التشخيص الطبي' },
        ptEvaluation: { nameEn: 'PT Evaluation', nameAr: 'تقييم العلاج الطبيعي' },
        reassessment: { nameEn: 'Reassessment', nameAr: 'إعادة التقييم' },
        dischargeEvaluation: { nameEn: 'Discharge Evaluation', nameAr: 'تقييم الخروج' },
        followUpForm: { nameEn: 'Follow-up Form', nameAr: 'نموذج المتابعة' },
        patientEducationDoctor: { nameEn: 'Patient Education (Doctor)', nameAr: 'نموذج تثقيف المريض (طبيب)' },
        progressNotes: { nameEn: 'Progress Notes', nameAr: 'ملاحظات تقدم الحالة' },
        patientEducationTherapist: { nameEn: 'Patient Education (Therapist)', nameAr: 'نموذج تثقيف المريض (أخصائي)' },
        patientEducationNurse: { nameEn: 'Patient Education (Nurse)', nameAr: 'نموذج تثقيف المريض (تمريض)' },
        nursingForm: { nameEn: 'Nursing Form', nameAr: 'نموذج التمريض التابع للمركز' },
        clinicalIndicators: { nameEn: 'Clinical Indicators', nameAr: 'المؤشرات السريرية' },
        bergBalanceScale: { nameEn: 'Berg Balance Scale', nameAr: 'مقياس بيرغ للتوازن' },
        tugTest: { nameEn: 'Timed Up and Go Test', nameAr: 'اختبار الوقوف والمشي' },
        manualMuscleTesting: { nameEn: 'Manual Muscle Testing', nameAr: 'اختبار العضلات اليدوي' },
        painScaleVAS: { nameEn: 'Pain Scale (VAS)', nameAr: 'مقياس الألم' },
        functionalIndependence: { nameEn: 'Functional Independence Measure', nameAr: 'مقياس الاستقلالية الوظيفية' },
        labResults: { nameEn: 'Laboratory Results', nameAr: 'نتائج المختبر' },
        radiologyResults: { nameEn: 'Radiology Results', nameAr: 'نتائج الأشعة' }
      }
    },
    system: {
      nameEn: 'System Access Permissions',
      nameAr: 'صلاحيات الوصول للنظام',
      permissions: {
        viewPatients: { nameEn: 'View Patients', nameAr: 'عرض المرضى' },
        createPatients: { nameEn: 'Create Patients', nameAr: 'إنشاء مرضى' },
        editPatients: { nameEn: 'Edit Patients', nameAr: 'تعديل المرضى' },
        deletePatients: { nameEn: 'Delete Patients', nameAr: 'حذف المرضى' },
        viewAppointments: { nameEn: 'View Appointments', nameAr: 'عرض المواعيد' },
        createAppointments: { nameEn: 'Create Appointments', nameAr: 'إنشاء مواعيد' },
        editAppointments: { nameEn: 'Edit Appointments', nameAr: 'تعديل المواعيد' },
        deleteAppointments: { nameEn: 'Delete Appointments', nameAr: 'حذف المواعيد' },
        viewBilling: { nameEn: 'View Billing', nameAr: 'عرض الفواتير' },
        createBilling: { nameEn: 'Create Billing', nameAr: 'إنشاء فواتير' },
        editBilling: { nameEn: 'Edit Billing', nameAr: 'تعديل الفواتير' },
        viewReports: { nameEn: 'View Reports', nameAr: 'عرض التقارير' },
        viewAnalytics: { nameEn: 'View Analytics', nameAr: 'عرض التحليلات' },
        manageUsers: { nameEn: 'Manage Users', nameAr: 'إدارة المستخدمين' },
        systemSettings: { nameEn: 'System Settings', nameAr: 'إعدادات النظام' }
      }
    },
    analytics: {
      nameEn: 'Clinical Analytics Permissions',
      nameAr: 'صلاحيات التحليلات السريرية',
      permissions: {
        functionalIndependenceComparison: { nameEn: 'Functional Independence Comparison', nameAr: 'درجة الاستقلالية الوظيفية قبل وبعد البرنامج العلاجي' },
        treatmentPlanEffectiveness: { nameEn: 'Treatment Plan Effectiveness', nameAr: 'نسبة التحسن السريري حسب نوع الخطة العلاجية المستخدمة' },
        clinicalProgressTracking: { nameEn: 'Clinical Progress Tracking', nameAr: 'تتبع التقدم السريري' },
        outcomeMetrics: { nameEn: 'Outcome Metrics', nameAr: 'مقاييس النتائج' },
        qualityIndicators: { nameEn: 'Quality Indicators', nameAr: 'مؤشرات الجودة' }
      }
    }
  };

  res.status(200).json({
    success: true,
    data: categories
  });
});

module.exports = {
  getUserPermissions,
  updateUserPermissions,
  getRolePermissions,
  applyDefaultPermissions,
  getPermissionCategories,
  defaultPermissions
};
