const asyncHandler = require('express-async-handler');
const ZatcaIntegration = require('../models/ZatcaIntegration');
const Billing = require('../models/Billing');
const crypto = require('crypto');
const QRCode = require('qrcode');
const xml2js = require('xml2js');

// @desc    Initialize ZATCA integration
// @route   POST /api/v1/zatca/initialize
// @access  Private
const initializeZatcaIntegration = asyncHandler(async (req, res) => {
  const {
    taxNumber,
    commercialRegistration,
    organizationName,
    organizationNameArabic,
    address,
    contactInfo
  } = req.body;

  try {
    // Check if integration already exists
    let zatcaIntegration = await ZatcaIntegration.findOne({ 
      'organization.taxNumber': taxNumber 
    });

    if (zatcaIntegration) {
      return res.status(400).json({
        success: false,
        message: 'ZATCA integration already exists for this tax number'
      });
    }

    // Create new ZATCA integration
    zatcaIntegration = new ZatcaIntegration({
      organization: {
        taxNumber,
        commercialRegistration,
        organizationName,
        organizationNameArabic,
        address,
        contactInfo
      },
      registrationStatus: 'pending',
      apiConfiguration: {
        environment: process.env.ZATCA_ENVIRONMENT || 'sandbox',
        baseUrl: process.env.ZATCA_BASE_URL || 'https://sandbox.zatca.gov.sa/api/v1',
        clientId: process.env.ZATCA_CLIENT_ID,
        clientSecret: process.env.ZATCA_CLIENT_SECRET
      },
      createdBy: req.user.id
    });

    await zatcaIntegration.save();

    res.status(201).json({
      success: true,
      message: 'ZATCA integration initialized successfully',
      data: {
        taxNumber: zatcaIntegration.organization.taxNumber,
        registrationStatus: zatcaIntegration.registrationStatus,
        organizationName: zatcaIntegration.organization.organizationName
      }
    });

  } catch (error) {
    console.error('ZATCA initialization error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initialize ZATCA integration',
      error: error.message
    });
  }
});

// @desc    Submit invoice to ZATCA
// @route   POST /api/v1/zatca/invoice/submit
// @access  Private
const submitInvoiceToZatca = asyncHandler(async (req, res) => {
  const { invoiceId } = req.body;

  try {
    const billing = await Billing.findById(invoiceId).populate('patient');
    if (!billing) {
      return res.status(404).json({
        success: false,
        message: 'Invoice not found'
      });
    }

    const zatcaIntegration = await ZatcaIntegration.findOne({
      registrationStatus: 'registered'
    });

    if (!zatcaIntegration) {
      return res.status(400).json({
        success: false,
        message: 'ZATCA integration not found or not registered'
      });
    }

    // Generate invoice hash
    const invoiceHash = generateInvoiceHash(billing);
    
    // Generate QR code
    const qrCodeData = await generateZatcaQRCode(billing, zatcaIntegration);
    
    // Generate XML data (UBL format)
    const xmlData = generateUBLXML(billing, zatcaIntegration);

    // Submit to ZATCA (mock implementation)
    const submissionResult = await submitToZatcaAPI({
      invoice: billing,
      hash: invoiceHash,
      qrCode: qrCodeData,
      xmlData: xmlData,
      zatcaIntegration: zatcaIntegration
    });

    // Update integration record
    const submission = {
      invoiceId: billing._id,
      zatcaInvoiceNumber: `${zatcaIntegration.invoiceSettings.invoicePrefix}-${zatcaIntegration.invoiceSettings.currentSequence}`,
      uuid: submissionResult.uuid,
      hash: invoiceHash,
      qrCode: qrCodeData,
      xmlData: xmlData,
      submissionDate: new Date(),
      status: submissionResult.status,
      responseCode: submissionResult.responseCode,
      responseMessage: submissionResult.responseMessage
    };

    zatcaIntegration.submittedInvoices.push(submission);
    zatcaIntegration.invoiceSettings.currentSequence += 1;
    await zatcaIntegration.save();

    // Update billing record
    billing.zatcaSubmission = {
      submitted: true,
      submissionDate: new Date(),
      zatcaInvoiceNumber: submission.zatcaInvoiceNumber,
      hash: invoiceHash,
      qrCode: qrCodeData,
      status: submissionResult.status
    };
    await billing.save();

    res.status(200).json({
      success: true,
      message: 'Invoice submitted to ZATCA successfully',
      data: {
        zatcaInvoiceNumber: submission.zatcaInvoiceNumber,
        hash: invoiceHash,
        qrCode: qrCodeData,
        status: submissionResult.status,
        submissionDate: submission.submissionDate
      }
    });

  } catch (error) {
    console.error('ZATCA submission error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit invoice to ZATCA',
      error: error.message
    });
  }
});

// @desc    Get ZATCA compliance status
// @route   GET /api/v1/zatca/compliance
// @access  Private
const getComplianceStatus = asyncHandler(async (req, res) => {
  try {
    const zatcaIntegration = await ZatcaIntegration.findOne({
      isActive: true
    });

    if (!zatcaIntegration) {
      return res.status(404).json({
        success: false,
        message: 'ZATCA integration not found'
      });
    }

    // Run compliance checks
    await zatcaIntegration.validateCompliance();

    const complianceStatus = {
      registrationStatus: zatcaIntegration.registrationStatus,
      certificateValid: zatcaIntegration.isCertificateValid,
      totalInvoicesSubmitted: zatcaIntegration.submittedInvoices.length,
      successfulSubmissions: zatcaIntegration.submittedInvoices.filter(inv => inv.status === 'accepted').length,
      failedSubmissions: zatcaIntegration.submittedInvoices.filter(inv => inv.status === 'rejected').length,
      lastSyncDate: zatcaIntegration.apiConfiguration.lastSyncDate,
      complianceChecks: zatcaIntegration.complianceChecks.slice(-5), // Last 5 checks
      overallCompliance: calculateOverallCompliance(zatcaIntegration)
    };

    res.status(200).json({
      success: true,
      data: complianceStatus
    });

  } catch (error) {
    console.error('ZATCA compliance check error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get compliance status',
      error: error.message
    });
  }
});

// @desc    Generate tax report
// @route   POST /api/v1/zatca/report/generate
// @access  Private
const generateTaxReport = asyncHandler(async (req, res) => {
  const { reportType, period } = req.body;

  try {
    const zatcaIntegration = await ZatcaIntegration.findOne({
      isActive: true
    });

    if (!zatcaIntegration) {
      return res.status(404).json({
        success: false,
        message: 'ZATCA integration not found'
      });
    }

    // Calculate report data based on period
    const reportData = await calculateTaxReportData(reportType, period);

    // Create report record
    const report = {
      reportType,
      period,
      totalSales: reportData.totalSales,
      totalTax: reportData.totalTax,
      totalExempt: reportData.totalExempt,
      submissionDate: new Date(),
      status: 'draft',
      reportData: reportData
    };

    zatcaIntegration.reports.push(report);
    await zatcaIntegration.save();

    res.status(201).json({
      success: true,
      message: 'Tax report generated successfully',
      data: report
    });

  } catch (error) {
    console.error('Tax report generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate tax report',
      error: error.message
    });
  }
});

// Helper function to generate invoice hash
const generateInvoiceHash = (billing) => {
  const hashData = {
    invoiceNumber: billing.invoiceNumber,
    issueDate: billing.issueDate,
    totalAmount: billing.totalAmount,
    taxAmount: billing.tax.amount,
    patientId: billing.patient._id
  };
  
  return crypto
    .createHash('sha256')
    .update(JSON.stringify(hashData))
    .digest('hex');
};

// Helper function to generate ZATCA QR code
const generateZatcaQRCode = async (billing, zatcaIntegration) => {
  const qrData = {
    seller: zatcaIntegration.organization.organizationName,
    taxNumber: zatcaIntegration.organization.taxNumber,
    timestamp: billing.issueDate,
    total: billing.totalAmount,
    tax: billing.tax.amount
  };
  
  const qrString = Buffer.from(JSON.stringify(qrData)).toString('base64');
  return await QRCode.toDataURL(qrString);
};

// Helper function to generate UBL XML
const generateUBLXML = (billing, zatcaIntegration) => {
  // Simplified UBL XML generation
  const xmlData = {
    Invoice: {
      ID: billing.invoiceNumber,
      IssueDate: billing.issueDate.toISOString().split('T')[0],
      InvoiceTypeCode: '388',
      DocumentCurrencyCode: 'SAR',
      AccountingSupplierParty: {
        Party: {
          PartyName: {
            Name: zatcaIntegration.organization.organizationName
          },
          PartyTaxScheme: {
            CompanyID: zatcaIntegration.organization.taxNumber
          }
        }
      },
      LegalMonetaryTotal: {
        TaxExclusiveAmount: billing.subtotal,
        TaxInclusiveAmount: billing.totalAmount,
        PayableAmount: billing.totalAmount
      }
    }
  };
  
  const builder = new xml2js.Builder();
  return builder.buildObject(xmlData);
};

// Helper function to submit to ZATCA API (mock)
const submitToZatcaAPI = async (data) => {
  // Mock implementation - replace with actual ZATCA API calls
  return {
    uuid: `UUID_${Date.now()}`,
    status: 'accepted',
    responseCode: '200',
    responseMessage: 'Invoice accepted successfully'
  };
};

// Helper function to calculate overall compliance
const calculateOverallCompliance = (zatcaIntegration) => {
  const checks = zatcaIntegration.complianceChecks;
  if (checks.length === 0) return 0;
  
  const passedChecks = checks.filter(check => check.status === 'passed').length;
  return Math.round((passedChecks / checks.length) * 100);
};

// Helper function to calculate tax report data
const calculateTaxReportData = async (reportType, period) => {
  // Mock implementation - replace with actual calculation
  return {
    totalSales: 100000,
    totalTax: 15000,
    totalExempt: 5000,
    breakdown: {
      standardRate: { sales: 95000, tax: 14250 },
      exemptSales: { sales: 5000, tax: 0 }
    }
  };
};

module.exports = {
  initializeZatcaIntegration,
  submitInvoiceToZatca,
  getComplianceStatus,
  generateTaxReport
};
