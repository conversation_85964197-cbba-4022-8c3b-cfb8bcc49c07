const asyncHandler = require('express-async-handler');
const Insurance = require('../models/Insurance');
const Patient = require('../models/Patient');

// @desc    Get all insurance records
// @route   GET /api/insurance
// @access  Private
const getInsurances = asyncHandler(async (req, res) => {
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const startIndex = (page - 1) * limit;

  const total = await Insurance.countDocuments();
  const providers = await Insurance.find()
    .skip(startIndex)
    .limit(limit)
    .sort({ name: 1 });

  res.status(200).json({
    success: true,
    count: providers.length,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    },
    data: providers
  });
});

// @desc    Get single insurance record
// @route   GET /api/insurance/:id
// @access  Private
const getInsurance = asyncHandler(async (req, res) => {
  const provider = await Insurance.findById(req.params.id);

  if (!provider) {
    return res.status(404).json({
      success: false,
      message: 'Insurance provider not found'
    });
  }

  res.status(200).json({
    success: true,
    data: provider
  });
});

// @desc    Create insurance record
// @route   POST /api/insurance
// @access  Private
const createInsurance = asyncHandler(async (req, res) => {
  const provider = await Insurance.create(req.body);

  res.status(201).json({
    success: true,
    data: provider
  });
});

// @desc    Update insurance record
// @route   PUT /api/insurance/:id
// @access  Private
const updateInsurance = asyncHandler(async (req, res) => {
  const provider = await Insurance.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  if (!provider) {
    return res.status(404).json({
      success: false,
      message: 'Insurance provider not found'
    });
  }

  res.status(200).json({
    success: true,
    data: provider
  });
});

// @desc    Delete insurance record
// @route   DELETE /api/insurance/:id
// @access  Private
const deleteInsurance = asyncHandler(async (req, res) => {
  const provider = await Insurance.findById(req.params.id);

  if (!provider) {
    return res.status(404).json({
      success: false,
      message: 'Insurance provider not found'
    });
  }

  await provider.deleteOne();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Get insurance claims
// @route   GET /api/insurance/claims
// @access  Private
const getInsuranceClaims = asyncHandler(async (req, res) => {
  // Mock data for now
  const claims = [
    {
      id: '1',
      patientId: 'patient1',
      patientName: 'John Doe',
      provider: 'Blue Cross',
      claimNumber: 'BC-2024-001',
      amount: 250.00,
      status: 'pending',
      submittedDate: new Date('2024-01-15'),
      services: ['Physical Therapy Session']
    },
    {
      id: '2',
      patientId: 'patient2',
      patientName: 'Jane Smith',
      provider: 'Aetna',
      claimNumber: 'AET-2024-002',
      amount: 180.00,
      status: 'approved',
      submittedDate: new Date('2024-01-10'),
      services: ['Initial Assessment']
    }
  ];

  res.status(200).json({
    success: true,
    data: claims
  });
});

// @desc    Create insurance claim
// @route   POST /api/insurance/claims
// @access  Private
const createInsuranceClaim = asyncHandler(async (req, res) => {
  // Mock implementation
  const claim = {
    id: Date.now().toString(),
    ...req.body,
    status: 'pending',
    submittedDate: new Date()
  };

  res.status(201).json({
    success: true,
    data: claim
  });
});

// @desc    Update insurance claim
// @route   PUT /api/insurance/claims/:id
// @access  Private
const updateInsuranceClaim = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: { id: req.params.id, ...req.body }
  });
});

// @desc    Get insurance verification
// @route   GET /api/insurance/verify/:patientId
// @access  Private
const verifyInsurance = asyncHandler(async (req, res) => {
  const patient = await Patient.findById(req.params.patientId);

  if (!patient) {
    return res.status(404).json({
      success: false,
      message: 'Patient not found'
    });
  }

  // Mock verification data
  const verification = {
    patientId: req.params.patientId,
    verified: true,
    provider: patient.insurance?.provider || 'Unknown',
    policyNumber: patient.insurance?.policyNumber || 'N/A',
    groupNumber: patient.insurance?.groupNumber || 'N/A',
    effectiveDate: patient.insurance?.effectiveDate || new Date(),
    expirationDate: patient.insurance?.expirationDate || new Date(),
    copay: patient.insurance?.copay || 0,
    deductible: patient.insurance?.deductible || 0,
    coinsurance: patient.insurance?.coinsurance || 0,
    benefits: {
      physicalTherapy: {
        covered: true,
        sessionsRemaining: 20,
        maxSessions: 30
      }
    }
  };

  res.status(200).json({
    success: true,
    data: verification
  });
});

// @desc    Check insurance eligibility
// @route   GET /api/insurance/eligibility/:patientId
// @access  Private
const checkEligibility = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: {
      eligible: true,
      benefits: {
        physicalTherapy: {
          covered: true,
          sessionsRemaining: 20
        }
      }
    }
  });
});

// @desc    Get insurances by patient
// @route   GET /api/insurance/patient/:patientId
// @access  Private
const getInsurancesByPatient = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: []
  });
});

// @desc    Get expiring insurances
// @route   GET /api/insurance/expiring
// @access  Private
const getExpiringInsurances = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: []
  });
});

// @desc    Submit insurance claim
// @route   POST /api/insurance/claims
// @access  Private
const submitClaim = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(201).json({
    success: true,
    data: { id: Date.now().toString(), status: 'submitted' }
  });
});

// @desc    Get claim status
// @route   GET /api/insurance/claims/:claimId/status
// @access  Private
const getClaimStatus = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: { status: 'pending', submittedDate: new Date() }
  });
});

// @desc    Get insurance statistics
// @route   GET /api/insurance/stats
// @access  Private
const getInsuranceStats = asyncHandler(async (req, res) => {
  // Mock implementation
  res.status(200).json({
    success: true,
    data: {
      totalProviders: 15,
      activeClaims: 45,
      pendingClaims: 12,
      approvedClaims: 33
    }
  });
});

module.exports = {
  getInsurances,
  getInsurance,
  createInsurance,
  updateInsurance,
  deleteInsurance,
  getInsuranceClaims,
  createInsuranceClaim,
  updateInsuranceClaim,
  verifyInsurance,
  checkEligibility,
  getInsurancesByPatient,
  getExpiringInsurances,
  submitClaim,
  getClaimStatus,
  getInsuranceStats
};
