const { asyncHandler, AppError } = require('../middleware/errorHandler');
const Patient = require('../models/Patient');
const Insurance = require('../models/Insurance');
const Billing = require('../models/Billing');
const User = require('../models/User');
const NphiesIntegration = require('../models/NphiesIntegration');
const ElectronicSignature = require('../models/ElectronicSignature');
const logger = require('../utils/logger');
const crypto = require('crypto');

// Mock NPHIES API configuration
const NPHIES_CONFIG = {
  baseUrl: process.env.NPHIES_BASE_URL || 'https://api.nphies.sa',
  apiKey: process.env.NPHIES_API_KEY || 'mock-api-key',
  providerId: process.env.NPHIES_PROVIDER_ID || 'PT-CLINIC-001',
  timeout: 30000
};

// @desc    Verify patient eligibility
// @route   POST /api/v1/nphies/eligibility
// @access  Private
const verifyEligibility = asyncHandler(async (req, res, next) => {
  const { patientId, insuranceId, serviceType, serviceDate } = req.body;

  // Find patient and insurance
  const patient = await Patient.findById(patientId);
  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  const insurance = await Insurance.findOne({ 
    patient: patientId, 
    policyNumber: insuranceId 
  });
  
  if (!insurance) {
    return next(new AppError('Insurance policy not found', 404));
  }

  // Mock NPHIES eligibility verification
  const eligibilityRequest = {
    patientId: patient.nationalId,
    insuranceId: insurance.policyNumber,
    serviceType: serviceType,
    serviceDate: serviceDate || new Date().toISOString().split('T')[0],
    providerId: NPHIES_CONFIG.providerId
  };

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Mock response based on insurance status
  const mockResponse = {
    eligible: insurance.status === 'active' && insurance.verificationStatus === 'verified',
    coverage: {
      deductible: insurance.coverage.deductible,
      copayAmount: insurance.coverage.copayAmount,
      copayPercentage: insurance.coverage.copayPercentage,
      maxCoverage: insurance.coverage.maxCoverage,
      remainingCoverage: insurance.remainingCoverage
    },
    benefits: {
      physicalTherapy: insurance.benefits.physicalTherapy,
      occupationalTherapy: insurance.benefits.occupationalTherapy,
      speechTherapy: insurance.benefits.speechTherapy,
      specialNeeds: insurance.benefits.specialNeeds
    },
    limitations: [],
    preAuthRequired: insurance.benefits[serviceType]?.requiresPreAuth || false,
    responseId: `ELIG-${Date.now()}`,
    timestamp: new Date().toISOString()
  };

  // Add limitations if coverage is low
  if (insurance.coverageUsedPercentage > 80) {
    mockResponse.limitations.push('Coverage limit approaching');
  }

  if (insurance.daysUntilExpiry < 30) {
    mockResponse.limitations.push('Insurance expires soon');
  }

  // Log the eligibility check
  logger.info(`NPHIES eligibility check: Patient ${patientId}, Service ${serviceType}, Eligible: ${mockResponse.eligible}`);

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Submit claim to NPHIES
// @route   POST /api/v1/nphies/claims
// @access  Private
const submitClaim = asyncHandler(async (req, res, next) => {
  const { patientId, providerId, services, totalAmount, diagnosis } = req.body;

  // Find patient
  const patient = await Patient.findById(patientId);
  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  // Find provider
  const provider = await User.findById(providerId);
  if (!provider) {
    return next(new AppError('Provider not found', 404));
  }

  // Mock claim submission
  const claimData = {
    claimId: `CLM-${Date.now()}`,
    patientId: patient.nationalId,
    providerId: provider.licenseNumber || NPHIES_CONFIG.providerId,
    services: services.map(service => ({
      ...service,
      code: service.code || 'PT-001', // Default PT service code
      nphiesCode: mapServiceToNphiesCode(service.code)
    })),
    totalAmount: totalAmount,
    diagnosis: diagnosis || [],
    submissionDate: new Date().toISOString(),
    status: 'submitted'
  };

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 2000));

  // Mock response
  const mockResponse = {
    claimId: claimData.claimId,
    status: 'submitted',
    submissionDate: claimData.submissionDate,
    estimatedProcessingTime: '3-5 business days',
    trackingNumber: `TRK-${Date.now()}`,
    acknowledgment: {
      received: true,
      validationPassed: true,
      errors: [],
      warnings: []
    }
  };

  // Add to insurance claims history if insurance exists
  const insurance = await Insurance.findOne({ patient: patientId });
  if (insurance) {
    insurance.claimsHistory.push({
      claimNumber: claimData.claimId,
      submissionDate: new Date(),
      amount: totalAmount,
      status: 'submitted'
    });
    await insurance.save();
  }

  logger.info(`NPHIES claim submitted: ${claimData.claimId} for patient ${patientId}, amount ${totalAmount}`);

  res.status(201).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Get claim status
// @route   GET /api/v1/nphies/claims/:claimId/status
// @access  Private
const getClaimStatus = asyncHandler(async (req, res, next) => {
  const { claimId } = req.params;

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Mock claim status based on claim age
  const claimAge = Date.now() - parseInt(claimId.split('-')[1]);
  const daysSinceSubmission = Math.floor(claimAge / (1000 * 60 * 60 * 24));

  let status = 'submitted';
  let processedAmount = 0;
  let denialReason = null;

  if (daysSinceSubmission >= 5) {
    // Randomly approve or deny claims after 5 days
    const approved = Math.random() > 0.2; // 80% approval rate
    status = approved ? 'approved' : 'denied';
    
    if (approved) {
      processedAmount = Math.random() * 1000 + 500; // Random amount between 500-1500
    } else {
      denialReason = 'Insufficient documentation provided';
    }
  } else if (daysSinceSubmission >= 2) {
    status = 'processing';
  }

  const mockResponse = {
    claimId: claimId,
    status: status,
    submissionDate: new Date(parseInt(claimId.split('-')[1])).toISOString(),
    lastUpdated: new Date().toISOString(),
    processedAmount: processedAmount,
    denialReason: denialReason,
    paymentDate: status === 'approved' ? new Date().toISOString() : null,
    processingNotes: status === 'processing' ? 'Claim under review by medical team' : null
  };

  logger.info(`NPHIES claim status checked: ${claimId}, Status: ${status}`);

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Submit pre-authorization
// @route   POST /api/v1/nphies/preauth
// @access  Private
const submitPreAuthorization = asyncHandler(async (req, res, next) => {
  const { patientId, providerId, services, estimatedAmount, treatmentPlan, urgency = 'routine' } = req.body;

  // Find patient
  const patient = await Patient.findById(patientId);
  if (!patient) {
    return next(new AppError('Patient not found', 404));
  }

  // Mock pre-authorization submission
  const preAuthData = {
    preAuthId: `PA-${Date.now()}`,
    patientId: patient.nationalId,
    providerId: providerId,
    services: services,
    estimatedAmount: estimatedAmount,
    treatmentPlan: treatmentPlan,
    urgency: urgency,
    submissionDate: new Date().toISOString(),
    status: 'submitted'
  };

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1500));

  // Mock response
  const mockResponse = {
    preAuthId: preAuthData.preAuthId,
    status: 'submitted',
    submissionDate: preAuthData.submissionDate,
    estimatedDecisionTime: urgency === 'emergency' ? '24 hours' : '5-7 business days',
    trackingNumber: `PATRK-${Date.now()}`,
    acknowledgment: {
      received: true,
      validationPassed: true,
      errors: [],
      warnings: urgency === 'routine' ? ['Standard processing time applies'] : []
    }
  };

  logger.info(`NPHIES pre-authorization submitted: ${preAuthData.preAuthId} for patient ${patientId}`);

  res.status(201).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Get pre-authorization status
// @route   GET /api/v1/nphies/preauth/:preAuthId/status
// @access  Private
const getPreAuthStatus = asyncHandler(async (req, res, next) => {
  const { preAuthId } = req.params;

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Mock pre-auth status
  const preAuthAge = Date.now() - parseInt(preAuthId.split('-')[1]);
  const daysSinceSubmission = Math.floor(preAuthAge / (1000 * 60 * 60 * 24));

  let status = 'submitted';
  let approvedAmount = 0;
  let approvedSessions = 0;
  let denialReason = null;

  if (daysSinceSubmission >= 3) {
    const approved = Math.random() > 0.15; // 85% approval rate for pre-auth
    status = approved ? 'approved' : 'denied';
    
    if (approved) {
      approvedAmount = Math.random() * 2000 + 1000; // Random amount between 1000-3000
      approvedSessions = Math.floor(Math.random() * 20) + 10; // 10-30 sessions
    } else {
      denialReason = 'Treatment not medically necessary based on submitted documentation';
    }
  } else if (daysSinceSubmission >= 1) {
    status = 'under_review';
  }

  const mockResponse = {
    preAuthId: preAuthId,
    status: status,
    submissionDate: new Date(parseInt(preAuthId.split('-')[1])).toISOString(),
    lastUpdated: new Date().toISOString(),
    approvedAmount: approvedAmount,
    approvedSessions: approvedSessions,
    validFrom: status === 'approved' ? new Date().toISOString() : null,
    validTo: status === 'approved' ? new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString() : null, // 90 days
    denialReason: denialReason,
    conditions: status === 'approved' ? ['Progress reports required every 30 days'] : []
  };

  logger.info(`NPHIES pre-auth status checked: ${preAuthId}, Status: ${status}`);

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Validate provider
// @route   POST /api/v1/nphies/provider/validate
// @access  Private
const validateProvider = asyncHandler(async (req, res, next) => {
  const { providerId, licenseNumber, specialization } = req.body;

  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 800));

  // Mock provider validation
  const mockResponse = {
    providerId: providerId,
    licenseNumber: licenseNumber,
    valid: true,
    status: 'active',
    specializations: [specialization || 'Physical Therapy'],
    accreditations: ['Saudi Physical Therapy Association', 'Ministry of Health'],
    validFrom: '2020-01-01',
    validTo: '2025-12-31',
    lastVerified: new Date().toISOString()
  };

  logger.info(`NPHIES provider validated: ${providerId}, License: ${licenseNumber}`);

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Test NPHIES connection
// @route   GET /api/v1/nphies/test/connection
// @access  Private
const testConnection = asyncHandler(async (req, res, next) => {
  // Simulate connection test
  await new Promise(resolve => setTimeout(resolve, 1000));

  const mockResponse = {
    connected: true,
    responseTime: Math.floor(Math.random() * 500) + 200, // 200-700ms
    serverStatus: 'operational',
    apiVersion: '2.1.0',
    lastSuccessfulConnection: new Date().toISOString(),
    endpoints: {
      eligibility: 'available',
      claims: 'available',
      preauth: 'available',
      encounters: 'available'
    }
  };

  logger.info('NPHIES connection test completed successfully');

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// Helper function to map service codes to NPHIES codes
const mapServiceToNphiesCode = (serviceCode) => {
  const codeMapping = {
    'PT-001': '97110', // Therapeutic exercise
    'PT-002': '97112', // Neuromuscular reeducation
    'PT-003': '97116', // Gait training
    'PT-004': '97140', // Manual therapy
    'OT-001': '97530', // Therapeutic activities
    'ST-001': '92507', // Speech therapy
    'EVAL-001': '97161', // PT evaluation
    'EVAL-002': '97165', // OT evaluation
  };
  
  return codeMapping[serviceCode] || serviceCode;
};

// @desc    Submit encounter
// @route   POST /api/v1/nphies/encounters
// @access  Private
const submitEncounter = asyncHandler(async (req, res, next) => {
  const { patientId, appointmentId, services, diagnosis } = req.body;

  // Mock encounter submission
  const mockResponse = {
    success: true,
    encounterId: `ENC-${Date.now()}`,
    status: 'submitted',
    submissionDate: new Date().toISOString(),
    message: 'Encounter submitted successfully'
  };

  res.status(201).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Get encounter status
// @route   GET /api/v1/nphies/encounters/:encounterId/status
// @access  Private
const getEncounterStatus = asyncHandler(async (req, res, next) => {
  const { encounterId } = req.params;

  const mockResponse = {
    encounterId,
    status: 'approved',
    statusDate: new Date().toISOString(),
    message: 'Encounter approved'
  };

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Generate compliance report
// @route   GET /api/v1/nphies/compliance/report
// @access  Private
const generateComplianceReport = asyncHandler(async (req, res, next) => {
  const mockReport = {
    reportId: `RPT-${Date.now()}`,
    generatedDate: new Date().toISOString(),
    complianceScore: 95,
    summary: 'System is compliant with NPHIES standards'
  };

  res.status(200).json({
    success: true,
    data: mockReport
  });
});

// @desc    Sync patient data
// @route   POST /api/v1/nphies/sync/patients
// @access  Private
const syncPatientData = asyncHandler(async (req, res, next) => {
  const mockResponse = {
    syncId: `SYNC-${Date.now()}`,
    status: 'completed',
    patientsProcessed: 150,
    syncDate: new Date().toISOString()
  };

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Validate coverage
// @route   POST /api/v1/nphies/coverage/validate
// @access  Private
const validateCoverage = asyncHandler(async (req, res, next) => {
  const mockResponse = {
    valid: true,
    coverageDetails: {
      policyNumber: req.body.policyNumber,
      status: 'active',
      validUntil: '2024-12-31'
    }
  };

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Submit benefit inquiry
// @route   POST /api/v1/nphies/benefits/inquiry
// @access  Private
const submitBenefitInquiry = asyncHandler(async (req, res, next) => {
  const mockResponse = {
    inquiryId: `INQ-${Date.now()}`,
    status: 'processed',
    benefits: {
      deductible: 500,
      copay: 50,
      coveragePercentage: 80
    }
  };

  res.status(200).json({
    success: true,
    data: mockResponse
  });
});

// @desc    Get system status
// @route   GET /api/v1/nphies/system/status
// @access  Private
const getSystemStatus = asyncHandler(async (req, res, next) => {
  const mockStatus = {
    status: 'operational',
    lastCheck: new Date().toISOString(),
    services: {
      eligibility: 'up',
      claims: 'up',
      preauth: 'up'
    }
  };

  res.status(200).json({
    success: true,
    data: mockStatus
  });
});

// @desc    Get NPHIES statistics
// @route   GET /api/v1/nphies/stats
// @access  Private
const getNphiesStats = asyncHandler(async (req, res, next) => {
  const mockStats = {
    totalClaims: 1250,
    approvedClaims: 1100,
    pendingClaims: 100,
    rejectedClaims: 50,
    totalAmount: 125000,
    approvedAmount: 110000
  };

  res.status(200).json({
    success: true,
    data: mockStats
  });
});

// @desc    Link visa to patient services
// @route   POST /api/v1/nphies/visa/link
// @access  Private
const linkVisaToServices = asyncHandler(async (req, res, next) => {
  const { patientId, visaNumber, visaType, serviceIds } = req.body;

  let nphiesIntegration = await NphiesIntegration.findOne({ patient: patientId });

  if (!nphiesIntegration) {
    // Create new integration record if not exists
    nphiesIntegration = new NphiesIntegration({
      patient: patientId,
      nphiesPatientId: `NPHIES_${patientId}`,
      createdBy: req.user.id
    });
  }

  try {
    // Verify visa with government systems (mock implementation)
    const visaVerification = {
      issueDate: new Date('2024-01-01'),
      expiryDate: new Date('2024-12-31'),
      sponsorInfo: {
        name: 'Healthcare Sponsor',
        id: 'SPONSOR123',
        type: 'organization'
      },
      status: 'valid'
    };

    // Update visa information
    nphiesIntegration.visaInfo = {
      visaNumber,
      visaType,
      issueDate: visaVerification.issueDate,
      expiryDate: visaVerification.expiryDate,
      sponsorInfo: visaVerification.sponsorInfo,
      status: visaVerification.status,
      linkedServices: serviceIds.map(serviceId => ({
        service: serviceId,
        linkedDate: new Date(),
        verificationStatus: 'verified'
      }))
    };

    await nphiesIntegration.save();

    res.status(200).json({
      success: true,
      message: 'Visa linked to services successfully',
      data: {
        visaInfo: nphiesIntegration.visaInfo,
        verificationStatus: visaVerification.status
      }
    });

  } catch (error) {
    logger.error('Visa linking error:', error);
    return next(new AppError('Failed to link visa to services', 500));
  }
});

// @desc    Create electronic signature for document
// @route   POST /api/v1/nphies/signature/create
// @access  Private
const createElectronicSignature = asyncHandler(async (req, res, next) => {
  const {
    documentType,
    documentTitle,
    documentContent,
    patientId,
    signers,
    relatedRecords
  } = req.body;

  try {
    // Generate document hash
    const documentHash = crypto
      .createHash('sha256')
      .update(documentContent)
      .digest('hex');

    // Create electronic signature document
    const electronicSignature = new ElectronicSignature({
      documentId: `DOC_${Date.now()}`,
      documentType,
      documentTitle,
      documentContent,
      documentHash,
      patient: patientId,
      relatedRecords,
      signers: signers.map(signer => ({
        ...signer,
        status: 'pending'
      })),
      workflow: {
        status: 'pending_signatures',
        requiredSignatures: signers.length,
        completedSignatures: 0,
        expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      },
      createdBy: req.user.id
    });

    await electronicSignature.save();

    res.status(201).json({
      success: true,
      message: 'Electronic signature document created successfully',
      data: {
        documentId: electronicSignature.documentId,
        status: electronicSignature.workflow.status,
        signers: electronicSignature.signers,
        expiryDate: electronicSignature.workflow.expiryDate
      }
    });

  } catch (error) {
    logger.error('Electronic signature creation error:', error);
    return next(new AppError('Failed to create electronic signature document', 500));
  }
});

module.exports = {
  verifyEligibility,
  submitClaim,
  getClaimStatus,
  submitPreAuthorization,
  getPreAuthStatus,
  validateProvider,
  submitEncounter,
  getEncounterStatus,
  generateComplianceReport,
  syncPatientData,
  validateCoverage,
  submitBenefitInquiry,
  getSystemStatus,
  testConnection,
  getNphiesStats,
  linkVisaToServices,
  createElectronicSignature
};
