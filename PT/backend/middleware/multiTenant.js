const mongoose = require('mongoose');
const logger = require('../utils/logger');

/**
 * Multi-tenant middleware for handling different deployment models
 */

// Tenant context storage
const tenantContext = new Map();

/**
 * Initialize tenant configuration based on deployment model
 */
const initializeTenantConfig = () => {
  const deploymentModel = process.env.DEPLOYMENT_MODEL || 'onpremise';
  const tenantMode = process.env.TENANT_MODE || 'single';
  
  return {
    deploymentModel,
    tenantMode,
    tenantIsolation: process.env.TENANT_ISOLATION || 'database',
    subdomainEnabled: process.env.TENANT_SUBDOMAIN === 'true',
    defaultTenant: process.env.DEFAULT_TENANT || 'default'
  };
};

/**
 * Extract tenant information from request
 */
const extractTenantInfo = (req) => {
  const config = initializeTenantConfig();
  
  // For single-tenant (on-premise), always use default tenant
  if (config.tenantMode === 'single') {
    return {
      tenantId: config.defaultTenant,
      tenantName: 'Default Organization',
      deploymentModel: config.deploymentModel
    };
  }
  
  // For multi-tenant (SaaS), extract from subdomain or header
  let tenantId = null;
  
  if (config.subdomainEnabled) {
    // Extract from subdomain: clinic1.ptsystem.com
    const host = req.get('host') || '';
    const subdomain = host.split('.')[0];
    if (subdomain && subdomain !== 'www' && subdomain !== 'api') {
      tenantId = subdomain;
    }
  }
  
  // Fallback to header or query parameter
  if (!tenantId) {
    tenantId = req.headers['x-tenant-id'] || 
              req.query.tenant || 
              req.body.tenantId ||
              config.defaultTenant;
  }
  
  return {
    tenantId: tenantId.toLowerCase(),
    tenantName: tenantId,
    deploymentModel: config.deploymentModel
  };
};

/**
 * Get database connection for tenant
 */
const getTenantDatabase = async (tenantInfo) => {
  const config = initializeTenantConfig();
  
  // For on-premise, use single database
  if (config.deploymentModel === 'onpremise') {
    return mongoose.connection;
  }
  
  // For SaaS, use tenant-specific database
  const tenantDbName = `pt_${tenantInfo.tenantId}`;
  
  // Check if connection already exists
  if (tenantContext.has(tenantInfo.tenantId)) {
    return tenantContext.get(tenantInfo.tenantId);
  }
  
  try {
    // Create new connection for tenant
    const tenantConnection = mongoose.createConnection(
      process.env.MONGODB_URI.replace(/\/[^\/]*$/, `/${tenantDbName}`),
      {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 5
      }
    );
    
    // Store connection
    tenantContext.set(tenantInfo.tenantId, tenantConnection);
    
    logger.info(`Created database connection for tenant: ${tenantInfo.tenantId}`);
    return tenantConnection;
    
  } catch (error) {
    logger.error(`Failed to create tenant database connection: ${error.message}`);
    throw error;
  }
};

/**
 * Middleware to set tenant context
 */
const setTenantContext = async (req, res, next) => {
  try {
    // Extract tenant information
    const tenantInfo = extractTenantInfo(req);
    
    // Get tenant database connection
    const tenantDb = await getTenantDatabase(tenantInfo);
    
    // Set tenant context in request
    req.tenant = {
      ...tenantInfo,
      db: tenantDb,
      config: initializeTenantConfig()
    };
    
    // Add tenant info to response headers (for debugging)
    if (process.env.NODE_ENV === 'development') {
      res.set('X-Tenant-ID', tenantInfo.tenantId);
      res.set('X-Deployment-Model', tenantInfo.deploymentModel);
    }
    
    next();
  } catch (error) {
    logger.error(`Tenant context error: ${error.message}`);
    res.status(500).json({
      success: false,
      error: 'Tenant configuration error'
    });
  }
};

/**
 * Get tenant-specific model
 */
const getTenantModel = (req, modelName, schema) => {
  const tenantDb = req.tenant.db;
  
  // For on-premise, use default models
  if (req.tenant.deploymentModel === 'onpremise') {
    return mongoose.model(modelName);
  }
  
  // For SaaS, use tenant-specific models
  const tenantModelName = `${req.tenant.tenantId}_${modelName}`;
  
  try {
    return tenantDb.model(tenantModelName);
  } catch (error) {
    // Model doesn't exist, create it
    return tenantDb.model(tenantModelName, schema);
  }
};

/**
 * Validate tenant access
 */
const validateTenantAccess = (req, res, next) => {
  const { tenantId, deploymentModel } = req.tenant;
  
  // For on-premise, always allow access
  if (deploymentModel === 'onpremise') {
    return next();
  }
  
  // For SaaS, validate tenant subscription and status
  // This would typically check against a tenant registry
  const isValidTenant = validateTenantSubscription(tenantId);
  
  if (!isValidTenant) {
    return res.status(403).json({
      success: false,
      error: 'Invalid tenant or subscription expired'
    });
  }
  
  next();
};

/**
 * Mock tenant subscription validation
 * In production, this would check against a subscription service
 */
const validateTenantSubscription = (tenantId) => {
  // Mock validation - in production, check against subscription database
  const validTenants = ['demo', 'clinic1', 'hospital1', 'default'];
  return validTenants.includes(tenantId);
};

/**
 * Cleanup tenant connections
 */
const cleanupTenantConnections = async () => {
  for (const [tenantId, connection] of tenantContext.entries()) {
    try {
      await connection.close();
      logger.info(`Closed connection for tenant: ${tenantId}`);
    } catch (error) {
      logger.error(`Error closing connection for tenant ${tenantId}: ${error.message}`);
    }
  }
  tenantContext.clear();
};

// Graceful shutdown
process.on('SIGINT', cleanupTenantConnections);
process.on('SIGTERM', cleanupTenantConnections);

module.exports = {
  setTenantContext,
  getTenantModel,
  validateTenantAccess,
  extractTenantInfo,
  getTenantDatabase,
  initializeTenantConfig,
  cleanupTenantConnections
};
