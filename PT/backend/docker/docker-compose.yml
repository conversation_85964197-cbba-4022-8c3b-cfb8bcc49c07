version: '3.8'

services:
  # Database
  postgres:
    image: postgres:15-alpine
    container_name: pt_postgres
    environment:
      POSTGRES_DB: pt_system
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD:-password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../shared/database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    networks:
      - pt_network
    restart: unless-stopped

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: pt_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pt_network
    restart: unless-stopped

  # Patient Management Service
  patient-service:
    build:
      context: ../services/patient-management
      dockerfile: Dockerfile
    container_name: pt_patient_service
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=pt_system
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
    networks:
      - pt_network
    restart: unless-stopped

  # Form Management Service
  form-service:
    build:
      context: ../services/form-management
      dockerfile: Dockerfile
    container_name: pt_form_service
    environment:
      - NODE_ENV=production
      - PORT=3002
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=pt_system
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    ports:
      - "3002:3002"
    depends_on:
      - postgres
      - redis
    networks:
      - pt_network
    restart: unless-stopped

  # AI Service
  ai-service:
    build:
      context: ../services/ai-service
      dockerfile: Dockerfile
    container_name: pt_ai_service
    environment:
      - NODE_ENV=production
      - PORT=3004
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=pt_system
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    ports:
      - "3004:3004"
    depends_on:
      - postgres
      - redis
    networks:
      - pt_network
    restart: unless-stopped

  # Authentication Service
  auth-service:
    build:
      context: ../services/auth-service
      dockerfile: Dockerfile
    container_name: pt_auth_service
    environment:
      - NODE_ENV=production
      - PORT=3006
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=pt_system
      - DB_USER=postgres
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key}
      - JWT_EXPIRES_IN=24h
    ports:
      - "3006:3006"
    depends_on:
      - postgres
      - redis
    networks:
      - pt_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  pt_network:
    driver: bridge
